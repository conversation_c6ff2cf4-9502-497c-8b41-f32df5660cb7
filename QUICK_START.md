# Paper2Code 流式API 快速开始指南

## 🚀 快速启动

### 启动流式API服务

**手动启动:**
```bash
pip install -r requirements_api.txt
python api_server.py
```

服务启动后，访问 `http://localhost:8000` 查看API信息。

### 2. 使用Web界面（推荐）

打开 `web_interface.html` 文件，获得实时处理体验：

1. **检查服务状态** - 确认API服务正常运行
2. **刷新文件列表** - 查看可用的PDF和JSON文件
3. **复制示例文件** - 将examples目录的文件复制到data目录
4. **流式处理论文** - 选择文件并实时查看处理进度

### 3. 使用命令行测试

运行测试脚本：
```bash
python test_api.py
```

### 4. 使用curl命令

```bash
# 检查服务状态
curl http://localhost:8000/health

# 获取文件列表
curl http://localhost:8000/list_papers

# 复制示例文件
curl -X POST "http://localhost:8000/copy_example_to_data?filename=demo/2310.05470v2.pdf"

# 流式处理论文
curl -X POST "http://localhost:8000/process_paper_stream" \
     -H "Content-Type: application/json" \
     -d '{
         "filename": "2310.05470v2.pdf",
         "paper_name": "autoj",
         "gpt_version": "openai/o3-mini"
     }'
```

## 📁 文件准备

### 方法1: 使用现有示例文件

项目已包含示例文件，可直接使用：
- `examples/demo/2310.05470v2.pdf` 和对应的JSON文件
- `examples/Transformer/Transformer.pdf` 和对应的JSON文件

通过API复制到data目录即可使用。

### 方法2: 准备自己的PDF文件（推荐）

现在API支持自动PDF转JSON！

1. **准备s2orc-doc2json工具:**
```bash
git clone https://github.com/allenai/s2orc-doc2json.git
cd ./s2orc-doc2json/grobid-0.7.3
./gradlew run
```

2. **将PDF文件放入data目录**
3. **直接使用API处理** - 会自动转换PDF为JSON！

### 方法3: 手动转换（可选）

如果你想手动转换：

```bash
# 转换PDF为JSON
python ./s2orc-doc2json/doc2json/grobid2json/process_pdf.py \
    -i data/your_paper.pdf \
    -t ./s2orc-doc2json/temp_dir/ \
    -o data/
```

## 🔧 配置说明

### API参数

- **filename**: PDF文件名（必需）
- **paper_name**: 论文名称（可选，默认使用文件名）
- **gpt_version**: GPT版本（可选，默认"openai/o3-mini"）

### 支持的GPT版本

- `openai/o3-mini` (默认，推荐)
- `openai/gpt-4`
- `openai/gpt-3.5-turbo`

### 环境变量

确保设置了OpenAI API密钥：
```bash
export OPENAI_API_KEY="your-api-key-here"
```

## 📊 处理流程

API执行以下步骤：

1. **PDF转JSON** - 如果JSON文件不存在，自动从PDF转换
2. **预处理** - 清理JSON数据
3. **规划** - 分析论文并生成实现计划
4. **配置提取** - 提取配置信息
5. **分析** - 深入分析论文内容
6. **编码** - 生成最终代码

整个过程可能需要几分钟到几十分钟，取决于论文复杂度。

## 📂 输出结果

处理完成后，结果保存在：

- `outputs/{paper_name}/` - 中间处理文件和分析结果
- `outputs/{paper_name}_repo/` - 最终生成的代码仓库

## ❗ 常见问题

### Q: API服务无法启动
A: 检查端口8000是否被占用，确保安装了所需依赖

### Q: 文件不存在错误
A: 确保PDF和对应的JSON文件都在data目录下

### Q: 处理时间过长
A: 大型论文处理需要较长时间，请耐心等待。可以通过日志查看进度

### Q: OpenAI API错误
A: 检查API密钥是否正确设置，网络连接是否正常

### Q: JSON文件格式错误
A: 确保使用s2orc-doc2json正确转换PDF文件

## 🛠️ 开发和调试

### 查看详细日志

API服务会输出详细的处理日志，包括每个步骤的执行情况。

### 修改配置

编辑 `api_server.py` 文件可以：
- 修改默认参数
- 调整超时时间
- 添加新的接口功能

### 测试新功能

使用 `test_api.py` 脚本测试API功能：
```bash
python test_api.py
```

## 📞 获取帮助

如果遇到问题：

1. 查看API服务的控制台输出
2. 检查 `outputs/` 目录下的日志文件
3. 使用Web界面的操作日志功能
4. 运行测试脚本诊断问题

## 🎯 最佳实践

1. **首次使用建议使用示例文件**进行测试
2. **处理大型论文前确保有足够时间**
3. **定期备份重要的输出结果**
4. **使用Web界面可以更直观地监控处理进度**
5. **遇到错误时查看详细的错误信息**
