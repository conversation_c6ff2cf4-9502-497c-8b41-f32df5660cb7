{"paper_id": "SparseTSF", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-24T23:34:47.116511Z"}, "title": "SparseTSF: Modeling Long-term Time Series Forecasting with 1k Parameters", "authors": [{"first": "Shen<PERSON>heng", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Technology", "location": {"postCode": "510006", "settlement": "Guangzhou", "country": "South China, China"}}, "email": ""}, {"first": "Weiwei", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "Peng Cheng Laboratory", "institution": "", "location": {"postCode": "518066", "settlement": "Shenzhen", "country": "China"}}, "email": "<<EMAIL>>."}, {"first": "Wentai", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Technology", "location": {"postCode": "510006", "settlement": "Guangzhou", "country": "South China, China"}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Technology", "location": {"postCode": "510006", "settlement": "Guangzhou", "country": "South China, China"}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Technology", "location": {"postCode": "510006", "settlement": "Guangzhou", "country": "South China, China"}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "This paper introduces SparseTSF, a novel, extremely lightweight model for Long-term Time Series Forecasting (LTSF), designed to address the challenges of modeling complex temporal dependencies over extended horizons with minimal computational resources. At the heart of SparseTSF lies the Cross-Period Sparse Forecasting technique, which simplifies the forecasting task by decoupling the periodicity and trend in time series data. This technique involves downsampling the original sequences to focus on crossperiod trend prediction, effectively extracting periodic features while minimizing the model's complexity and parameter count. Based on this technique, the SparseTSF model uses fewer than 1k parameters to achieve competitive or superior performance compared to state-of-the-art models. Furthermore, SparseTSF showcases remarkable generalization capabilities, making it wellsuited for scenarios with limited computational resources, small samples, or low-quality data.", "pdf_parse": {"paper_id": "SparseTSF", "_pdf_hash": "", "abstract": [{"text": "This paper introduces SparseTSF, a novel, extremely lightweight model for Long-term Time Series Forecasting (LTSF), designed to address the challenges of modeling complex temporal dependencies over extended horizons with minimal computational resources. At the heart of SparseTSF lies the Cross-Period Sparse Forecasting technique, which simplifies the forecasting task by decoupling the periodicity and trend in time series data. This technique involves downsampling the original sequences to focus on crossperiod trend prediction, effectively extracting periodic features while minimizing the model's complexity and parameter count. Based on this technique, the SparseTSF model uses fewer than 1k parameters to achieve competitive or superior performance compared to state-of-the-art models. Furthermore, SparseTSF showcases remarkable generalization capabilities, making it wellsuited for scenarios with limited computational resources, small samples, or low-quality data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Time series forecasting holds significant value in domains such as traffic flow, product sales, and energy consumption, as accurate predictions enable decision-makers to plan proactively. Achieving precise forecasts typically relies on powerful yet complex deep learning models, such as RNNs (<PERSON> et al., 2023) , TCNs (<PERSON> et al., 2018; <PERSON><PERSON> et al., 2019) , and Transformers (<PERSON> et al., 2022) . In recent years, there has been a growing interest in Longterm Time Series Forecasting (LTSF), which demands mod-els to provide an extended predictive view for advanced planning (<PERSON> et al., 2021) .", "cite_spans": [{"start": 292, "end": 312, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF34"}, {"start": 320, "end": 338, "text": "(<PERSON> et al., 2018;", "ref_id": "BIBREF0"}, {"start": 339, "end": 363, "text": "<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF8"}, {"start": 383, "end": 401, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF27"}, {"start": 582, "end": 601, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Although a longer predictive horizon offers convenience, it also introduces greater uncertainty (<PERSON> et al., 2023b) . This demands models capable of extracting more extensive temporal dependencies from longer historical windows. Consequently, modeling becomes more complex to capture these long-term temporal dependencies. For instance, Transformer-based models often have millions or tens of millions of parameters, limiting their practical usability, especially in scenarios with restricted computational resources (<PERSON><PERSON> et al., 2024) .", "cite_spans": [{"start": 96, "end": 115, "text": "(<PERSON> et al., 2023b)", "ref_id": null}, {"start": 517, "end": 536, "text": "(<PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In fact, the basis for accurate long-term time series forecasting lies in the inherent periodicity and trend of the data. For example, long-term forecasts of household electricity consumption are feasible due to the clear daily and weekly patterns in such data. Particularly for daily patterns, if we resample the electricity consumption at a certain time of the day into a daily sequence, each subsequence exhibits similar or consistent trends. In this case, the original sequence's periodicity and trend are decomposed and transformed. That is, periodic patterns are transformed into inter-subsequence dynamics, while trend patterns are reinterpreted as intrasubsequence characteristics. This decomposition offers a novel perspective for designing lightweight LTSF models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In this paper, we pioneer the exploration of how to utilize this inherent periodicity and decomposition in data to construct specialized lightweight time series forecasting models. Specifically, we introduce SparseTSF, an extremely lightweight LTSF model. Technically, we propose the Cross-Period Sparse Forecasting technique (hereinafter referred to as Sparse technique). It first downsamples the original sequences with constant periodicity into subsequences, then performs predictions on each downsampled subsequence, simplifying the original time series forecasting task into a cross-period trend prediction task. This approach yields two benefits: (i) effective decoupling of data periodicity and trend, enabling the model to stably identify and extract periodic features while focusing on predicting trend changes, and (ii) extreme compression of the model's parameter size, significantly reducing the demand for computational resources. As shown in Figure 1 , SparseTSF achieves near state-of-the-art prediction performance with less than 1k trainable parameters, which makes it 1∼4 orders of magnitude smaller than its counterparts.", "cite_spans": [], "ref_spans": [{"start": 963, "end": 964, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "1 0 2 1 0 3 1 0 4 1 0 5 1 0 6 1 0 7 1 0 8 0 . 1 5 0 . 2 0 0 . 2 5 0 . 3 0 0 . 3 5 0 . 4 0 I n f o r m e r ( 2 0 2 1 ) A u t o f o r m e r ( 2 0 2 1 ) F E D f o r m e r ( 2 0 2 2 ) F i L M ( 2 0 2 2 ) P a t c h T S T ( 2 0 2 3 ) D L i n e a r ( 2 0 2 3 ) F I T S ( 2 0 2 4 ) S p a r s e T S F ( O u r s ) M e a n S q u a r e d E r r o r ( M S E ) P a r a m e t e r s In summary, our contributions in this paper are as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We propose a novel Cross-Period Sparse Forecasting technique, which downsamples the original sequences to focus on cross-period trend prediction, effectively extracting periodic features while minimizing the model's complexity and parameter count.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• Based on the Sparse technique, we present the SparseTSF model, which requires fewer than 1k parameters, significantly reducing the computational resource demand of forecasting models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• The proposed SparseTSF model not only attains competitive or surpasses state-of-the-art predictive accuracy with a remarkably minimal parameter scale but also demonstrates robust generalization capabilities.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "The LTSF tasks, which aim at predicting over an extended horizon, are inherently more challenging. Initially, the Transformer architecture (<PERSON><PERSON><PERSON><PERSON> et al., 2017) , known for its robust long-term dependency modeling capabilities, gained widespread attention in the LTSF domain. Models such as Informer (<PERSON> et al., 2021) , Autoformer (<PERSON> et al., 2021) , and FEDformer (<PERSON> et al., 2022b) have modified the native structure of Transformer to suit time series forecasting tasks. More recent advancements, like PatchTST (<PERSON><PERSON> et al., 2023) and PETformer (<PERSON> et al., 2023a) , demonstrate that the original Transformer architecture can achieve impressive results with an appropriate patch strategy, a technique that is prevalently employed in the realm of computer vision (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2022) . Besides Transformer architectures, Convolutional Neural Networks (CNNs) and Multilayer Perceptrons (MLPs) are also mainstream approaches, including SCINet (<PERSON> et al., 2022a) , TimesNet (Wu et al., 2023) , MICN (<PERSON> et al., 2022) , TiDE (<PERSON> et al., 2023) , and HD-Mixer (<PERSON> et al., 2024a) . Recent studies have shown that transferring pretrained Large Language Models (LLMs) to the time series domain can also yield commendable results (<PERSON> et al., 2024; <PERSON> et al., 2023; Xue & Salim, 2023) . Moreover, recent works have revealed that RNN and GNN networks can also perform well in LTSF tasks, as exemplified by SegRNN (Lin et al., 2023b) and CrossGNN (Huang et al., 2024b) .", "cite_spans": [{"start": 139, "end": 161, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF25"}, {"start": 301, "end": 320, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF35"}, {"start": 334, "end": 351, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF28"}, {"start": 368, "end": 388, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 518, "end": 536, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 551, "end": 570, "text": "(<PERSON> et al., 2023a)", "ref_id": null}, {"start": 768, "end": 794, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF6"}, {"start": 795, "end": 811, "text": "He et al., 2022)", "ref_id": "BIBREF10"}, {"start": 969, "end": 988, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 1000, "end": 1017, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF29"}, {"start": 1025, "end": 1044, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF26"}, {"start": 1052, "end": 1070, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF4"}, {"start": 1086, "end": 1107, "text": "(<PERSON> et al., 2024a)", "ref_id": null}, {"start": 1255, "end": 1275, "text": "(<PERSON> et al., 2024;", "ref_id": "BIBREF2"}, {"start": 1276, "end": 1293, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF14"}, {"start": 1294, "end": 1312, "text": "Xue & Salim, 2023)", "ref_id": "BIBREF31"}, {"start": 1440, "end": 1459, "text": "(<PERSON> et al., 2023b)", "ref_id": null}, {"start": 1473, "end": 1494, "text": "(<PERSON> et al., 2024b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Work Development of Long-term Time Series Forecasting", "sec_num": "2."}, {"text": "Progress in Lightweight Forecasting Models Since DLinear (<PERSON><PERSON> et al., 2023) demonstrated that simple models could already extract strong temporal periodic dependencies, numerous studies have been pushing LTSF models towards lightweight designs, including LightTS (<PERSON> et al., 2022) , TiDE (<PERSON> et al., 2023) , TSMixer (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , and HDformer (<PERSON><PERSON> et al., 2024) . Recently, FITS emerged as a milestone in the lightweight LTSF process, being the first to reduce the LTSF model scale to the 10k parameter level while maintaining excellent predictive performance (<PERSON> et al., 2024) . FITS achieved this by transforming time-domain forecasting tasks into frequency-domain ones and using low-pass filters to reduce the required number of parameters. In this paper, our proposed SparseTSF model takes lightweight model design to the extreme. Utilizing the Cross-Period Sparse Forecasting technique, it's the first to reduce model parameters to below 1k.", "cite_spans": [{"start": 57, "end": 76, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}, {"start": 264, "end": 284, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF33"}, {"start": 292, "end": 310, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF4"}, {"start": 321, "end": 345, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF7"}, {"start": 361, "end": 380, "text": "(<PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF5"}, {"start": 579, "end": 596, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF30"}], "ref_spans": [], "eq_spans": [], "section": "Related Work Development of Long-term Time Series Forecasting", "sec_num": "2."}, {"text": "Long-term Time Series Forecasting The task of LTSF involves predicting future values over an extended horizon using previously observed multivariate time series (MTS) data. It is formalized as xt+1:t+H = f (x t-L+1:t ), where x t-L+1:t ∈ R L×C and xt+1:t+H ∈ R H×C . In this formulation, L represents the length of the historical observation window, C is the number of distinct features or channels, and H is the length of the forecast horizon. The main goal of LTSF is to extend the forecast horizon H as it provides richer and more advanced guidance in practical applications. However, an extended forecast horizon H also increases the complexity of the model, leading to a significant increase in parameters in mainstream models. To address this challenge, our research focuses on developing models that are not only extremely lightweight but also robust and effective.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3.1."}, {"text": "Channel Independent Strategy Recent advancements in the field of LTSF have seen a shift towards a Channel Independent (CI) approach, especially when dealing with multivariate time series data (<PERSON> et al., 2024) . This strategy simplifies the forecasting process by focusing on individual univariate time series within the dataset. Instead of the traditional approach, which utilizes the entire multivariate historical data to predict future outcomes, the CI method finds a shared function f :", "cite_spans": [{"start": 192, "end": 210, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3.1."}, {"text": "x (i) t-L+1:t ∈ R L → x(i) t+1:t+H ∈ R H", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3.1."}, {"text": "for each univariate series. This approach provides a more targeted and simplified prediction model for each channel, reducing the complexity of accounting for inter-channel relationships.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3.1."}, {"text": "As a result, the main goal of mainstream state-of-the-art models in recent years has shifted towards effectively predict by modeling long-term dependencies, including periodicity and trends, in univariate sequences. For instance, models like DLinear achieve this by extracting dominant periodicity from univariate sequences using a single linear layer (<PERSON><PERSON> et al., 2023) . More advanced models, such as PatchTST (<PERSON><PERSON> et al., 2023) and TiDE (<PERSON> et al., 2023) , employ more complex structures on single channels to extract temporal dependencies, aiming for superior predictive performance. In this paper, we adopt this CI strategy as well and focus on how to create an even more lightweight yet effective approach for capturing long-term dependencies in singlechannel time series.", "cite_spans": [{"start": 352, "end": 371, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}, {"start": 413, "end": 431, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 441, "end": 459, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3.1."}, {"text": "Given that the data to be forecasted often exhibits constant, periodicity a priori (e.g., electricity consumption and traffic flow typically have fixed daily cycles), we propose the Cross-Period Sparse Forecasting technique to enhance the extraction of long-term sequential dependencies while reducing the model's parameter scale. Utilizing a single linear layer to model the LTSF task within this framework leads to our SparseTSF model, as illustrated in Figure 2 .", "cite_spans": [], "ref_spans": [{"start": 463, "end": 464, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "Cross-Period Sparse Forecasting Assuming that the time series x (i) t-L+1:t has a known periodicity w, the first step is to downsample the original series into w subsequences of length n = L w . A model with shared parameters is then applied to these subsequences for prediction. After prediction, the w subsequences, each of length m = H w , are upsampled back to a complete forecast sequence of length H.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "Intuitively, this forecasting process appears as a sliding forecast with a sparse interval of w, performed by a fully connected layer with parameter sharing within a constant period w. This can be viewed as a model performing sparse sliding prediction across periods.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "Technically, the downsampling process is equivalent to reshaping x (i) t-L+1:t into a n × w matrix, which is then transposed to a w × n matrix. The sparse sliding prediction is equivalent to applying a linear layer of size n×m on the last dimension of the matrix, resulting in a w × m matrix. The upsampling step is equivalent to transposing the w × m matrix and reshaping it back into a complete forecast sequence of length H.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "However, this approach currently still faces two issues: (i) loss of information, as only one data point per period is utilized for prediction, while the rest are ignored; and (ii) amplification of the impact of outliers, as the presence of extreme values in the downsampled subsequences can directly affect the prediction.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "To address these issues, we additionally perform a sliding aggregation on the original sequence before executing sparse prediction, as depicted in Figure 2 . Each aggregated data point incorporates information from other points within its surrounding period, addressing issue (i). Moreover, as the aggregated value is essentially a weighted average of surrounding points, it mitigates the impact of outliers, thus resolving issue (ii). Technically, this sliding aggregation can be implemented using a 1D convolution with zero-padding and a kernel size of 2 × w 2 + 1. The process can be formulated as follows:", "cite_spans": [], "ref_spans": [{"start": 154, "end": 155, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x (i) t-L+1:t = x (i) t-L+1:t + Conv1D(x (i) t-L+1:t )", "eq_num": "(1)"}], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "Instance Normalization Time series data often exhibit distributional shifts between training and testing datasets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "Recent studies have shown that employing simple sample normalization strategies between the input and output of models can help mitigate this issue (<PERSON> et al., 2021; <PERSON> et al., 2023) . In our work, we also utilize a straightforward normalization strategy. Specifically, we subtract the mean of the sequence from itself before it enters the model and add it back after the model's output. This process is formulated as follows:", "cite_spans": [{"start": 148, "end": 166, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF15"}, {"start": 167, "end": 185, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x (i) t-L+1:t = x (i) t-L+1:t -E t (x (i) t-L+1:t ),", "eq_num": "(2)"}], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "x(i)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "t+1:t+H = x(i) t+1:t+H + E t (x (i) t-L+1:t ).", "eq_num": "(3)"}], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "Loss Function In alignment with current mainstream practices in the field, we adopt the classic Mean Squared Error (MSE) as the loss function for SparseTSF. This function measures the discrepancy between the predicted values x(i) t+1:t+H and the actual ground truth y ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SparseTSF", "sec_num": "3.2."}, {"text": "In this section, we provide a theoretical analysis of the SparseTSF model, focusing on its parameter efficiency and the effectiveness of the Sparse technique. The relevant theoretical proofs are provided in Appendix B.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "3.3."}, {"text": "Theorem 3.1. Given a historical look-back window length L, a forecast horizon H, and a constant periodicity w, the total number of parameters required for the SparseTSF model is", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PARAMETER EFFICIENCY OF SPARSETSF", "sec_num": "3.3.1."}, {"text": "L w × H w + 2 × w 2 + 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PARAMETER EFFICIENCY OF SPARSETSF", "sec_num": "3.3.1."}, {"text": "In LTSF tasks, the look-back window length L and forecast horizon H are usually quite large, for instance, up to 720, while the intrinsic periodicity w of the data is also typically large, such as 24. In this scenario, L w × H w + 2 × w 2 + 1 ≪ L × H. This means that the parameter scale of the SparseTSF model is much lighter than even the simplest single-layer linear model. This demonstrates the lightweight architecture of the SparseTSF model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PARAMETER EFFICIENCY OF SPARSETSF", "sec_num": "3.3.1."}, {"text": "The time series targeted for long-term forecasting often exhibits constant periodicity. Here, we first define the representation of such a sequence X. Definition 3.2. Consider a univariate time series X with a known period w, which can be decomposed into a periodic component P (t) and a trend component T (t), such that X(t) = P (t) + T (t). Here, P (t) represents the periodic part and satisfies the condition:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "P (t) = P (t + w).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "(5) Furthermore, we can derive the form of the modeling task after downsampling.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "In the context of a truncated subsequence x t-L+1:t of X(t) and its corresponding future sequence x t+1:t+H to be forecasted, the conventional approach involves using x t-L+1:t directly to predict x t+1:t+H , essentially estimating the function:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x t+1:t+H = f (x t-L+1:t )", "eq_num": "(6)"}], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "However, with the application of the Sparse technique, this forecasting task transforms into predicting downsampled subsequences, as per Lemma 3.3. Lemma 3.3. The SparseTSF model reformulates the forecasting task into predicting downsampled subsequences, namely:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x ′ t+1:t+m = f (x ′ t-n+1:t )", "eq_num": "(7)"}], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "Combining Definition 3.2 and Lemma 3.3, we can further deduce Theorem 3.4. Theorem 3.4. Given a time series dataset that satisfies Definition 3.2, the SparseTSF model's formulation becomes:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "p ′ t+1:t+m + t ′ t+1:t+m = f (p ′ t-n+1:t + t ′ t-n+1:t ) (8)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "where, for any i ∈ [t -n + 1 : t + m] and j ∈ [t -n + 1 : t + m], satisfying:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "p ′ i = p ′ j (9)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "Theorem 3.4 implies that the task of the SparseTSF model effectively transforms into predicting future trend components (i.e., t ′ ), using the constant periodic components (i.e., p ′ ) as a reference. This process effectively separates the periodic components, which are no longer explicitly modeled, allowing the model to focus more on the trend variations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "Intuitively, We can further validate this finding from the perspective of autocorrelation, a powerful tool for identifying patterns such as seasonality or periodicity in time series data. Definition 3.5 (AutoCorrelation Function (ACF) (<PERSON>, 2007) ). Given a time series {X t }, where t represents discrete time points, the ACF at lag k is defined as:", "cite_spans": [{"start": 235, "end": 249, "text": "(<PERSON><PERSON>, 2007)", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "ACF(k) = N -k t=1 (X t -µ)(X t+k -µ) N t=1 (X t -µ) 2 (10)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "where N is the total number of observations in the time series, X t is the value of the series at time t, X t+k is the value of the series at time t + k, and µ is the mean of the series {X t }. The lag time k in the ACF reveals the periodic patterns in the series, that is, when k equals the periodic length of the series, the ACF value typically shows a significant peak. As shown in Figure 3 , the original sequence exhibits clear periodicity, while the downsampled subsequences retain only trend characteristics. This demonstrates that, through its downsampling strategy, the SparseTSF model can efficiently separate and extract accurate periodic features from time series data. This not only reduces the complexity of the model but also enables it to focus on predicting trend variations, thereby exhibiting impressive performance in LTSF tasks.", "cite_spans": [], "ref_spans": [{"start": 392, "end": 393, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "In summary, the SparseTSF model's design, characterized by its parameter efficiency and focus on decoupling periodic features, makes it well-suited for LTSF tasks, especially in scenarios where the data exhibits clear periodic patterns.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EFFECTIVENESS OF SPARSETSF", "sec_num": "3.3.2."}, {"text": "In this section, we present the experimental results of SparseTSF on mainstream LTSF benchmarks. Additionally, we discuss the efficiency advantages brought by the lightweight architecture of SparseTSF. Furthermore, we conduct ablation studies and analysis to further reveal the effectiveness of the Sparse technique.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4."}, {"text": "Datasets We conducted experiments on four mainstream LTSF datasets that exhibit daily periodicity. These datasets include ETTh1&ETTh2 1 , Electricity 2 , and Traffic 3 . The 1 https://github.com/zhouhaoyi/ETDataset 2 https://archive.ics.uci.edu/ml/datasets 3 https://pems.dot.ca.gov/ details of these datasets are presented in Table 1 . Baselines We compared our approach with state-of-theart and representative methods in the field. These include Informer (<PERSON> et al., 2021) , Autoformer (<PERSON> et al., 2021) , Pyraformer (<PERSON> et al., 2022b) , FEDformer (<PERSON> et al., 2022b) , Film (<PERSON> et al., 2022a) , TimesNet (<PERSON> et al., 2023) , and PatchTST (<PERSON><PERSON> et al., 2023) . Additionally, we specifically compared SparseTSF with lightweight models, namely DLinear (<PERSON><PERSON> et al., 2023) and FITS (<PERSON> et al., 2024) . Following FITS, SparseTSF defaults to a look-back length of 720.", "cite_spans": [{"start": 457, "end": 476, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF35"}, {"start": 490, "end": 507, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF28"}, {"start": 521, "end": 540, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 553, "end": 573, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 581, "end": 601, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 613, "end": 630, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF29"}, {"start": 646, "end": 664, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 756, "end": 775, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}, {"start": 785, "end": 802, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF30"}], "ref_spans": [{"start": 333, "end": 334, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Experimental Setup", "sec_num": "4.1."}, {"text": "Environment All experiments in this study were implemented using PyTorch (<PERSON><PERSON><PERSON> et al., 2019) and conducted on a single NVIDIA RTX 4090 GPU with 24GB of memory. More experimental details are provided in Appendix A.2.", "cite_spans": [{"start": 73, "end": 94, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "4.1."}, {"text": "Table 2 presents a performance comparison between SparseTSF and other baseline models 4 . It is observable that SparseTSF ranks within the top two in all scenarios, achieving or closely approaching state-of-the-art levels with a significantly smaller parameter scale. This emphatically demonstrates the superiority of the Sparse technique proposed in this paper. Specifically, the Sparse technique is capable of more effectively extracting the periodicity and trends from data, thereby enabling exceptional predictive performance in long horizon scenarios. Additionally, the standard deviation of SparseTSF's results is notably small. In most cases, the standard deviation across 5 runs is within 0.001, which strongly indicates the robustness of the SparseTSF model.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Main Results", "sec_num": "4.2."}, {"text": "Beyond its powerful predictive performance, another significant benefit of the SparseTSF model is its extreme lightweight nature. Previously, Figure 1 visualized the parameter-performance comparison of SparseTSF with other mainstream models. Here, we further present a comprehensive comparison between SparseTSF and these base- ", "cite_spans": [], "ref_spans": [{"start": 149, "end": 150, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Efficiency Advantages of SparseTSF", "sec_num": "4.3."}, {"text": "Beyond its ultra-lightweight characteristics, the Sparse technique also possesses a robust capability to extract periodic features, which we will delve further into in this section.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation Studies and Analysis", "sec_num": "4.4."}, {"text": "The Sparse technique, combined with a simple single-layer linear model, forms the core of our proposed model, SparseTSF. Additionally, the Sparse technique can be integrated with other foundational models, including the Transformer (Vaswani ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Effectiveness of the Sparse Technique", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "weight ′ = SparseTSF(     1 0 . . . 0 0 1 . . . 0 . . . . . . . . . 0 0 0 0 1     ) ⊤ . (", "eq_num": "11"}], "section": "Effectiveness of the Sparse Technique", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Effectiveness of the Sparse Technique", "sec_num": null}, {"text": "From the visualization in Figure 4 , two observations can be made: (i) The Linear model can learn evenly spaced weight distribution stripes (i.e., periodic features) from the data, indicating that single linear layer can already extract the primary periodic characteristics from a univariate series with the CI strategy. These findings are consistent with previous research conclusions (<PERSON><PERSON> et al., 2023) . (ii) Compared to the Linear model, SparseTSF learns more distinct evenly spaced weight distribution stripes, indicating that SparseTSF has a stronger capability in extracting periodic features. This phenomenon aligns with the conclusions of Section 3.3.", "cite_spans": [{"start": 386, "end": 405, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}], "ref_spans": [{"start": 33, "end": 34, "text": "4", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Effectiveness of the Sparse Technique", "sec_num": null}, {"text": "Therefore, the Sparse technique can enhance the model's performance in LTSF tasks by strengthening its ability to extract periodic features from data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Effectiveness of the Sparse Technique", "sec_num": null}, {"text": "Impact of the Hyperparameter w The Sparse technique relies on the manual setting of the hyperparameter w, which represents the a priori main period. Here, we delve into the influence of different values of w on the forecast outcomes.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Effectiveness of the Sparse Technique", "sec_num": null}, {"text": "As indicated in the results from Table 6 , SparseTSF exhibits optimal performance when w = 24, aligning with the intrinsic main period of the data. Conversely, when w diverges from 24, a slight decline in performance is observed. This suggests that the hyperparameter w should ideally be set consistent with the data's a priori main period. In practical scenarios, datasets requiring long-term forecasting often exhibit inherent periodicity, such as daily or weekly cycles, common in domains like electricity, transportation, energy, and consumer goods consumption. Therefore, empirically identifying the predominant period and setting the appropriate w for such data is both feasible and straightforward. However, for data lacking clear periodicity and patterns, such as financial data, current LTSF models may not be effective (<PERSON><PERSON> et al., 2023) . Thus, the SparseTSF model may not be the preferred choice for these types of data. Nonetheless, we will further discuss the existing limitations and potential improvements of the SparseTSF model in the Section 5.1.", "cite_spans": [{"start": 829, "end": 848, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}], "ref_spans": [{"start": 39, "end": 40, "text": "6", "ref_id": "TABREF5"}], "eq_spans": [], "section": "Effectiveness of the Sparse Technique", "sec_num": null}, {"text": "The Sparse technique enhances the model's ability to extract periodic features from data. Therefore, the generalization capability of a trained SparseTSF model on different datasets with the same principal periodicity is promising. To investigate this, we further studied the cross-domain generalization performance of the SparseTSF model (i.e., training on a dataset from one domain and testing on a dataset from another). Specifically, we examined the performance from ETTh2 to ETTh1, which are datasets of the same type but collected from different machines, each with 7 variables. Additionally, we explored the performance from Electricity to ETTh1, where these datasets originate from different domains and have a differing number of variables (i.e., Electricity has 321 variables). On datasets with different numbers of variables, models trained with traditional non-CI strategies (like Informer) cannot transfer, whereas those trained with CI strategies (like PatchTST) can, due to the decoupling of CI strategies from channel relationships. These datasets all have a daily periodicity, i.e., a prior predominant period of w = 24. Therefore, the SparseTSF model exhibits outstanding generalization capabilities. This characteristic is highly beneficial for the application of the SparseTSF model in scenarios involving small samples and low-quality data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Generalization Ability of the SparseTSF Model", "sec_num": null}, {"text": "The SparseTSF model proposed in this paper excels in handling data with a stable main period, demonstrating enhanced feature extraction capabilities and an extremely lightweight architecture. However, there are two scenarios where SparseTSF may not be as effective:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations and Future Work", "sec_num": "5.1."}, {"text": "1. Ultra-Long Periods: In cases involving ultra-long periods (for example, periods exceeding 100), the Sparse technique results in overly sparse parameter connections. Consequently, SparseTSF does not perform optimally in such scenarios.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations and Future Work", "sec_num": "5.1."}, {"text": "2. Multiple Periods: SparseTSF may struggle with data that intertwines multiple periods, as the Sparse technique can only downsample and decompose one main period.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations and Future Work", "sec_num": "5.1."}, {"text": "We have further investigated the performance of SparseTSF in these scenarios in Appendix C and concluded that: (1) in ultra-long period scenarios, a denser connected model would be a better choice; (2) SparseTSF can still perform excellently in some multi-period scenarios (such as daily periods superimposed with weekly periods).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations and Future Work", "sec_num": "5.1."}, {"text": "Finally, one of our key future research directions is to further address the these potential limitations by designing additional modules to enhance SparseTSF's ability, thus achieving a balance between performance and parameter size.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations and Future Work", "sec_num": "5.1."}, {"text": "The Sparse technique proposed in this paper involves downsampling/upsampling to achieve periodicity/trend decoupling. It may share a similar idea with existing methods, as downsampling/upsampling and periodic/trend decomposition techniques are prevalent in related literature nowadays. Specifically, we provide a detailed analysis of the differences with respect to N-HiTS (<PERSON><PERSON><PERSON> et al., 2023) and OneShotSTL (<PERSON> et al., 2023) as follows, and present the comparison results in Appendix D.4.", "cite_spans": [{"start": 373, "end": 394, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF1"}, {"start": 410, "end": 427, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Differences Compared to Existing Methods", "sec_num": "5.2."}, {"text": "SparseTSF Compared to N-HiTS N-HiTS incorporates novel hierarchical interpolation and multi-rate data sampling techniques to achieve better results (<PERSON><PERSON><PERSON> et al., 2023) .", "cite_spans": [{"start": 148, "end": 169, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "Differences Compared to Existing Methods", "sec_num": "5.2."}, {"text": "The downsampling and upsampling techniques proposed in SparseTSF are indeed quite different from those used in N-HiTS, including:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Differences Compared to Existing Methods", "sec_num": "5.2."}, {"text": "• The downsampling and upsampling in SparseTSF occur before and after the model's prediction process, respectively, whereas N-HiTS conducts these operations within internally stacked modules.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Differences Compared to Existing Methods", "sec_num": "5.2."}, {"text": "• SparseTSF's downsampling involves resampling by a factor of w to w subsequences of length L/w, which is technically equivalent to matrix reshaping and transposition, whereas N-HiTS employs downsampling through max-pooling.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Differences Compared to Existing Methods", "sec_num": "5.2."}, {"text": "• SparseTSF's upsampling involves transposing and reshaping the predicted subsequences back to the original sequence, whereas N-HiTS achieves upsampling through interpolation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Differences Compared to Existing Methods", "sec_num": "5.2."}, {"text": "SparseTSF Compared to OneShotSTL Seasonal-trend decomposition (STD) is a classical and powerful tool for time series forecasting, and OneShotSTL makes a great contribution to advancing the lightweight long-term forecasting process, featuring fast, lightweight, and powerful capabilities (<PERSON> et al., 2023) . However, SparseTSF differs significantly from OneShotSTL in several aspects:", "cite_spans": [{"start": 287, "end": 304, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Differences Compared to Existing Methods", "sec_num": "5.2."}, {"text": "• SparseTSF is a neural network model while OneShot-STL is a non-neural network method focused on online forecasting.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Differences Compared to Existing Methods", "sec_num": "5.2."}, {"text": "• OneShotSTL minimizes residuals and calculates trend and seasonal subseries separately from the original sequence with lengths of L, whereas our SparseTSF resamples the original sequence into w subseries of length L/w with a constant period w.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Differences Compared to Existing Methods", "sec_num": "5.2."}, {"text": "• OneShotSTL accelerates inference by optimizing the original computation for online processing, while SparseTSF achieves lightweighting by using parametersharing linear layers for prediction across all subseries.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Differences Compared to Existing Methods", "sec_num": "5.2."}, {"text": "In this paper, we introduce the Cross-Period Sparse Forecasting technique and the corresponding SparseTSF model. Through detailed theoretical analysis and experimental validation, we demonstrated the lightweight nature of the SparseTSF model and its capability to extract periodic features effectively. Achieving competitive or even surpassing the performance of current state-of-the-art models with a minimal parameter scale, SparseTSF emerges as a strong contender for deployment in computation resourceconstrained environments. Additionally, SparseTSF exhibits potent generalization capabilities, opening new possibilities for applications in transferring to small samples and low-quality data scenarios. SparseTSF stands as another milestone in the journey towards lightweight models in the field of long-term time series forecasting. Finally, we aim to further tackle the challenges associated with extracting features from ultra-long-periodic and multi-periodic data in the future, striving to achieve an optimal balance between model performance and parameter size.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "A. More Details of SparseTSF Additionally, intuitively, SparseTSF can be perceived as a sparsely connected linear layer performing sliding prediction across periods, as depicted in Figure 5 . ", "cite_spans": [], "ref_spans": [{"start": 188, "end": 189, "text": "5", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "We implemented SparseTSF in PyTorch (<PERSON><PERSON><PERSON> et al., 2019) and trained it using the Adam optimizer (Kingma & Ba, 2014) for 30 epochs, with a learning rate decay of 0.8 after the initial 3 epochs, and early stopping with a patience of 5. The dataset splitting follows the procedures of FITS and Autoformer, where the ETT datasets are divided into proportions of 6:2:2, while the other datasets are split into proportions of 7:1:2. SparseTSF has minimal hyperparameters due to its simple design. The period w is set to the inherent cycle of the data (e.g., w = 24 for ETTh1) or to a smaller value if the data has an extremely long cycle (e.g., w = 4 for ETTm1). The choice of batch size depends on the size of the data samples (i.e., the number of channels). For datasets with fewer than 100 channels (such as ETTh1), the batch size is set to 256, while for datasets with fewer than 300 channels (such as Electricity), the batch size is set to 128. This setting maximizes the utilization of GPU parallel computing capabilities while avoiding GPU out-of-memory issues (i.e., with NVIDIA RTX 4090, 24GB). Additionally, the learning rate needs to be set relatively large (i.e., 0.02) due to the very small number of learnable parameters in SparseTSF. The complete details can be found in our official repository5 .", "cite_spans": [{"start": 36, "end": 57, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF23"}, {"start": 98, "end": 117, "text": "(Kingma & Ba, 2014)", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "A.2. Experimental Details", "sec_num": null}, {"text": "The baseline results in this paper are from the first version of the FITS paper6 , where FITS adopted a uniform input length of 720 (we also use an input length of 720 for fair comparison with it). Here, the input lengths of other baselines are set to be consistent with their respective official input lengths.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2. Experimental Details", "sec_num": null}, {"text": "Proof of Theorem 3.1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "Proof. The SparseTSF model consists of two main components: a 1D convolutional layer for sliding aggregation and a linear layer for sparse sliding prediction. The number of parameters in the 1D convolutional layer (without bias) is determined by the kernel size, which is 2× w 2 +1. For the linear layer (without bias), the number of parameters is the product of the input and output sizes, which are n = L w and m = H w , respectively. Thus, the total number of parameters in the linear layer is n × m.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "By combining the parameters from both layers, the total count is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "n × m + 2 × w 2 + 1 = L w × H w + 2 × w 2 + 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "Proof of Lemma 3.3 Proof. Given the original time series x t-L+1:t with length L, the downsampling process segments it into w subsequences, each of which contains every w-th data point from the original series. The length of each downsampled subsequence, denoted as n, is therefore L w , as it collects one data point from every w time steps from the original series of length L.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "The SparseTSF model then applies a forecasting function f on each of these downsampled subsequences. The forecasting function f is designed to predict future values of the time series based on its past values. Specifically, it predicts the future subsequence x ′ t+1:t+m using the past subsequence x ′ t-n+1:t . Here, m is the length of the forecast horizon for the downsampled subsequences and is given by H w , where H is the original forecast horizon.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "Therefore, the SparseTSF model effectively reformulates the original forecasting task of predicting x t+1:t+H from x t-L+1:t into a series of smaller tasks. Each of these smaller tasks involves using the downsampled past subsequence x ′ t-n+1:t to predict the downsampled future subsequence x ′ t+1:t+m . This is represented mathematically as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x ′ t+1:t+m = f (x ′ t-n+1:t ). (", "eq_num": "12"}], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "Proof of Theorem 3.4", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "Proof. Theorem 3.4 is established based on the assumption of a time series dataset that can be decomposed into a periodic component P (t) and a trend component T (t), as defined in Definition 3.2. This decomposition implies that any time point in the series X(t) can be expressed as the sum of its periodic and trend components, i.e., X(t) = P (t)+T (t).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "Therefore, for the downsampled subsequences x ′ t-n+1:t and x ′ t+1:t+m based on a periodicity w, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x ′ t-n+1:t = p ′ t-n+1:t + t ′ t-n+1:t , (13) x ′ t+1:t+m = p ′ t+1:t+m + t ′ t+1:t+m .", "eq_num": "(14)"}], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "Hence, by combining with Lemma 3.3, the task formulation of the SparseTSF model can be expressed as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p ′ t+1:t+m + t ′ t+1:t+m = f (p ′ t-n+1:t + t ′ t-n+1:t ). (", "eq_num": "15"}], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "Due to the periodic nature of P (t) as defined in Equation 5, for any two points i and j in the downsampled sequence (where i, j ∈ [t -n + 1 : t + m]), the periodic component remains constant, i.e., p ′ i = p ′ j . This indicates that the task of the SparseTSF model is to predict future trend components while utilizing a constant periodic component as a reference.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Proofs", "sec_num": null}, {"text": "In this section, we specifically examine the performance of the SparseTSF model in scenarios involving multiple periods. Specifically, we study its performance on the Traffic dataset, as traffic flow data not only exhibits distinct daily periodicity but also demonstrates significant weekly cycles. For instance, the morning and evening rush hours represent intra-day cycles, while the different patterns between weekdays and weekends exemplify weekly cycles.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.1. Multi-Period Scenarios", "sec_num": null}, {"text": "Figure 6 displays the autocorrelation in the original and dayperiod downsampled traffic flow data. It can be observed that even after downsampling with a daily period, the data still exhibits a clear weekly cycle (w ′ = 7). Under these circumstances, with SparseTSF only decoupling the primary daily cycle, will it outperform the original fully connected linear model? The results, as shown in Figure 7 , indicate that the SparseTSF model captures stronger daily and weekly periodic patterns (evident as more pronounced equidistant stripes) compared to the original approach. This is because, in the original method, a single linear layer is tasked with extracting both daily and weekly periodic patterns. In contrast, the SparseTSF model, by decoupling the daily cycle, simplifies the task for its inherent linear layer to only extract the remaining weekly periodic features. Therefore, even in scenarios with multiple periods, SparseTSF can still achieve remarkable performance.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "6", "ref_id": "FIGREF5"}, {"start": 401, "end": 402, "text": "7", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "C.1. Multi-Period Scenarios", "sec_num": null}, {"text": "This section is dedicated to examining the SparseTSF model's performance in scenarios characterized by ultralong periods. Specifically, our focus is on the ETTm1&ETTm27 and Weather8 datasets, as detailed in Table 8 . These datasets are distinguished by their primary periods extending up to 96 and 144, respectively. We evaluate the SparseTSF model's performance under various settings of the hyperparameter w.", "cite_spans": [], "ref_spans": [{"start": 213, "end": 214, "text": "8", "ref_id": "TABREF8"}], "eq_spans": [], "section": "C.2. Ultra-Long Period Scenarios", "sec_num": null}, {"text": "As illustrated in Table 9 , when w is set to a large value (for instance, 144, which aligns with the intrinsic primary period of the Weather dataset), the performance of the SparseTSF model tends to deteriorate. This decline is attributed to the excessive sparsity in connections caused by a large w, limiting the information available for the model to base its predictions on, thereby impairing its performance. Interestingly, as w increases, there is a noticeable improvement in the SparseTSF model's performance. This observation suggests that employing denser connections within the SparseTSF framework could be a more viable option for datasets with longer periods.", "cite_spans": [], "ref_spans": [{"start": 24, "end": 25, "text": "9", "ref_id": "TABREF9"}], "eq_spans": [], "section": "C.2. Ultra-Long Period Scenarios", "sec_num": null}, {"text": "Furthermore, an intriguing phenomenon is observed when w = 1, which corresponds to the scenario of employing a fully connected linear layer for prediction. The performance in this case is inferior compared to sparse connection-based predictions. This indicates that an appropriate level of sparsity in connections (even when the sparse interval does not match the dataset's inherent primary period) can enhance the model's predictive accuracy. This could be due to the redundant nature of time series data, especially when data sampling is dense. In such cases, executing sparse predictions might help eliminate some redundant information. However, these findings necessitate further investigation and exploration in future work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2. Ultra-Long Period Scenarios", "sec_num": null}, {"text": "The findings above suggest that employing a denser sparse strategy would be beneficial in such cases. Therefore, we present in Table 10 a comparative performance of Recent research has discovered a long-standing bug in the popular codebase used in the field since the introduction of the Informer (<PERSON> et al., 2021) . This bug, which affected the calculation of test set metrics, caused the data that did not fill an entire batch to be discarded (<PERSON><PERSON> et al., 2024) . As a result, the batch size setting influenced the results. Theoretically, the larger the batch size, the more test data might be discarded, leading to incorrect results. This bug significantly improved the performance on ETTh1 and ETTh2 datasets when the batch size was large, while the impact on other datasets was relatively minor.", "cite_spans": [{"start": 297, "end": 316, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF35"}, {"start": 447, "end": 465, "text": "(<PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF24"}], "ref_spans": [{"start": 133, "end": 135, "text": "10", "ref_id": "TABREF10"}], "eq_spans": [], "section": "C.2. Ultra-Long Period Scenarios", "sec_num": null}, {"text": "To reassess the performance of SparseTSF, we present the performance of SparseTSF and existing models after fixing this bug in Table 11 . Here, we reran FITS under the conditions of lookback L = 720 and cutoff frequency COF = 5 (where the parameter count of SparseTSF is still tens of times smaller than that of FITS) for a fair comparison with SparseTSF. The results for other baselines were sourced from FITS' reproduction, where they reran the baselines' results after fixing the bug (<PERSON> et al., 2024) . As shown, after fixing the code bug, SparseTSF still achieves impressive performance with minimal overhead, aligning with the conclusions of Table 2 .", "cite_spans": [{"start": 487, "end": 504, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF30"}], "ref_spans": [{"start": 133, "end": 135, "text": "11", "ref_id": "TABREF11"}, {"start": 654, "end": 655, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "C.2. Ultra-Long Period Scenarios", "sec_num": null}, {"text": "The look-back length determines the richness of historical information the model can utilize. Generally, models are expected to perform better with longer input lengths if they possess robust long-term dependency modeling capabilities. Table 12 presents the performance of SparseTSF at different look-back lengths.", "cite_spans": [], "ref_spans": [{"start": 242, "end": 244, "text": "12", "ref_id": "TABREF12"}], "eq_spans": [], "section": "D.2. Impacts of Varying Look-Back Length", "sec_num": null}, {"text": "It can be observed that two phenomena occur: (i) longer look-back windows perform better, indicating SparseTSF's ability in long-term dependency modeling, and (ii) the performance of the ETTh1 & ETTh2 datasets remains relatively stable across different look-back windows, while the performance of the Traffic & Electricity datasets varies significantly, especially with a look-back of 96, where the accuracy notably decreases.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2. Impacts of Varying Look-Back Length", "sec_num": null}, {"text": "In fact, we can further discuss the reasons behind the second point. As illustrated in Figure 3 , ETTh1 only exhibits a significant daily periodic pattern (w = 24). In this case, look-back lengths of 96 can achieve good results because they fully encompass the daily periodic pattern. However, as shown in Figure 7 , Traffic not only has a significant daily periodic pattern (w = 24) but also a noticeable weekly periodic pattern (w = 168). In this case, a look-back of 96 cannot cover the entire weekly periodic pattern, leading to a significant performance drop. This underscores the necessity of sufficiently long look-back lengths (at least covering the entire cycle length) for accurate prediction.", "cite_spans": [], "ref_spans": [{"start": 94, "end": 95, "text": "3", "ref_id": "FIGREF2"}, {"start": 313, "end": 314, "text": "7", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "D.2. Impacts of Varying Look-Back Length", "sec_num": null}, {"text": "Given the extreme lightweight nature of SparseTSF, we strongly recommend providing sufficiently long look-back windows whenever feasible.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2. Impacts of Varying Look-Back Length", "sec_num": null}, {"text": "Instance Normalization (IN) strategy has become popular in mainstream methods. We also employ this strategy in SparseTSF to enhance its performance on datasets with significant distribution drift. We showcase the impact of the IN strategy in Table 13 .", "cite_spans": [], "ref_spans": [{"start": 248, "end": 250, "text": "13", "ref_id": "TABREF13"}], "eq_spans": [], "section": "D.3. Impacts of Instance Normalization", "sec_num": null}, {"text": "It can be observed that IN is necessary for smaller datasets, namely ETTh1 and ETTh2 datasets. However, its effect is relatively limited on larger datasets such as Traffic and Electricity datasets. It must be clarified that, although the IN strategy is one of the factors contributing to SparseTSF's success, it is not the key differentiator of SparseTSF's core contributions compared to other models. Here, we present the comparison results between SparseTSF and N-HiTS and OneShotSTL in Table 14 . It can be observed that in most cases, SparseTSF outperforms these methods, demonstrating the superiority of the SparseTSF approach.", "cite_spans": [], "ref_spans": [{"start": 495, "end": 497, "text": "14", "ref_id": "TABREF14"}], "eq_spans": [], "section": "D.3. Impacts of Instance Normalization", "sec_num": null}, {"text": "Recent works discovered a long-standing bug in the current benchmark framework, which may affect model performance on small datasets(<PERSON> et al., 2024;<PERSON><PERSON> et al., 2024). We reporte the comparison results after fixing this bug in Appendix D.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://github.com/lss-1138/SparseTSF", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://arxiv.org/pdf/2307.03756v1.pdf", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://github.com/zhouhaoyi/ETDataset", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://www.bgc-jena.mpg.de/wetter", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "This work is supported by Guangdong Major Project of Basic and Applied Basic Research (2019B030302002), National Natural Science Foundation of China (62072187), Guangzhou Development Zone Science and Technology Project (2021GH10) and the Major Key Project of PCL, China under Grant PCL2023A09.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "An empirical evaluation of generic convolutional and recurrent networks for sequence modeling", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["Z"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "Koltun", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1803.01271"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. An empirical evalua- tion of generic convolutional and recurrent networks for sequence modeling. arXiv preprint arXiv:1803.01271, 2018.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Nhits: Neural hierarchical interpolation for time series forecasting", "authors": [{"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": ["G"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": ["N"], "last": "Oreshkin", "suffix": ""}, {"first": "F", "middle": ["G"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["M"], "last": "Canseco", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "37", "issue": "", "pages": "6989--6997", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Canseco, M. M., and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: Neural hi- erarchical interpolation for time series forecasting. In Proceedings of the AAAI Conference on Artificial Intelli- gence, volume 37, pp. 6989-6997, 2023.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Llm4ts: Aligning pre-trained llms as data-efficient timeseries forecasters", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W.-C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T.-F", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>, T.-F. Llm4ts: Aligning pre-trained llms as data-efficient time- series forecasters, 2024.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Learning phrase representations using rnn encoder-decoder for statistical machine translation", "authors": [{"first": "K", "middle": [], "last": "Cho", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Schwenk", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1406.1078"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, Y. <PERSON>rn- ing phrase representations using rnn encoder-decoder for statistical machine translation. arXiv preprint arXiv:1406.1078, 2014.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Long-term forecasting with tide: Time-series dense encoder", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Kong", "suffix": ""}, {"first": "A", "middle": [], "last": "Leach", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.08424"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Long-term forecasting with tide: Time-series dense encoder. arXiv preprint arXiv:2304.08424, 2023.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "The bigger the better? rethinking the effective model scale in long-term time series forecasting", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Song", "suffix": ""}, {"first": "I", "middle": ["W"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2401.11929"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON>. The bigger the better? rethinking the effective model scale in long-term time series forecasting. arXiv preprint arXiv:2401.11929, 2024.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "A", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "X", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.11929"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint arXiv:2010.11929, 2020.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Lightweight mlp-mixer model for multivariate time series forecasting", "authors": [{"first": "V", "middle": [], "last": "Eka<PERSON>ram", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining", "volume": "", "issue": "", "pages": "459--469", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Lightweight mlp-mixer model for multivariate time series forecasting. In Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, pp. 459-469, 2023.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Unsupervised scalable representation learning for multivariate time series", "authors": [{"first": "J.-Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Dieuleveut", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "32", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>- vised scalable representation learning for multivariate time series. Advances in neural information processing systems, 32, 2019.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "The capacity and robustness trade-off: Revisiting the channel independent strategy for multivariate time series forecasting", "authors": [{"first": "L", "middle": [], "last": "Han", "suffix": ""}, {"first": "H.-<PERSON>", "middle": [], "last": "Ye", "suffix": ""}, {"first": "D.-C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "IEEE Transactions on Knowledge and Data Engineering", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, D<PERSON><PERSON>C. The capacity and ro- bustness trade-off: Revisiting the channel independent strategy for multivariate time series forecasting. IEEE Transactions on Knowledge and Data Engineering, 2024.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Masked autoencoders are scalable vision learners", "authors": [{"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "16000--16009", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Masked autoencoders are scalable vision learners. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 16000-16009, 2022.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Oneshotstl: One-shot seasonal-trend decomposition for online time series anomaly detection and forecasting", "authors": [{"first": "X", "middle": [], "last": "He", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Li", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.01506"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: One-shot seasonal-trend decomposition for online time series anomaly detection and forecasting. arXiv preprint arXiv:2304.01506, 2023.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Hierarchical dependency with extendable patch for multivariate time series forecasting", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Hdmixer", "suffix": ""}], "year": 2024, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "38", "issue": "", "pages": "12608--12616", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Hierarchical dependency with extendable patch for multivariate time series forecasting. In Proceedings of the AAAI Conference on Artificial In- telligence, volume 38, pp. 12608-12616, 2024a.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Confronting noisy multivariate time series via cross interaction refinement", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Crossgnn", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Confronting noisy multivari- ate time series via cross interaction refinement. Advances in Neural Information Processing Systems, 36, 2024b.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Time-llm: Time series forecasting by reprogramming large language models", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Shi", "suffix": ""}, {"first": "P.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y.-F", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "Pan", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.01728"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Time-llm: Time series forecasting by reprogramming large language models. arXiv preprint arXiv:2310.01728, 2023.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Reversible instance normalization for accurate time-series forecasting against distribution shift", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Park", "suffix": ""}, {"first": "J.-<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> instance normalization for accurate time-series forecasting against distribution shift. In International Conference on Learning Representations, 2021.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "A method for stochastic optimization", "authors": [{"first": "D", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ba", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1412.6980"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>: A method for stochastic optimization. arXiv preprint arXiv:1412.6980, 2014.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Petformer: Long-term time series forecasting via placeholderenhanced transformer", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.04791"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Long-term time series forecasting via placeholder- enhanced transformer. arXiv preprint arXiv:2308.04791, 2023a.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Segment recurrent neural network for long-term time series forecasting", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Mo", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Segrnn", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.11200"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Segment recurrent neural network for long-term time series forecasting. arXiv preprint arXiv:2308.11200, 2023b.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Scinet: Time series modeling and forecasting with sample convolution and interaction", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "5816--5828", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Time series modeling and forecasting with sample convolution and interaction. Advances in Neural Information Processing Systems, 35:5816-5828, 2022a.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Pyraformer: Low-complexity pyramidal attention for long-range time series modeling and forecasting", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["X"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Dustdar", "suffix": ""}], "year": 2022, "venue": "International conference on learning representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. Pyraformer: Low-complexity pyramidal atten- tion for long-range time series modeling and forecasting. In International conference on learning representations, 2022b.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Time series analysis", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2007, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> series analysis. CRC Press, 2007.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "A time series is worth 64 words: Long-term forecasting with transformers", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "", "suffix": ""}], "year": 2023, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. A time series is worth 64 words: Long-term forecasting with transformers. In International Conference on Learning Representations, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Pytorch: An imperative style, high-performance deep learning library", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Gross", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Gimelshein", "suffix": ""}, {"first": "L", "middle": [], "last": "Antiga", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Brad<PERSON>, J<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N., <PERSON>, L<PERSON>, et al. Pytorch: An imperative style, high-performance deep learning library. Advances in neural information processing systems, 32, 2019.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Towards comprehensive and fair benchmarking of time series forecasting methods", "authors": [{"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Hu", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2403.20150"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Tfb: Towards comprehensive and fair benchmarking of time series fore- casting methods. arXiv preprint arXiv:2403.20150, 2024.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Attention is all you need", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "Ł", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, I. At- tention is all you need. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Multi-scale local and global context modeling for long-term series forecasting", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Micn", "suffix": ""}], "year": 2022, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Multi-scale local and global context model- ing for long-term series forecasting. In The Eleventh International Conference on Learning Representations, 2022.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Transformers in time series: A survey", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Ma", "suffix": ""}, {"first": "J", "middle": [], "last": "Yan", "suffix": ""}, {"first": "L", "middle": [], "last": "Sun", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2202.07125"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, L. Transformers in time series: A survey. arXiv preprint arXiv:2202.07125, 2022.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Autoformer: Decomposition transformers with auto-correlation for long-term series forecasting. Advances in neural information processing systems", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "34", "issue": "", "pages": "22419--22430", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Autoformer: Decom- position transformers with auto-correlation for long-term series forecasting. Advances in neural information pro- cessing systems, 34:22419-22430, 2021.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Timesnet: Temporal 2d-variation modeling for general time series analysis", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Hu", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Temporal 2d-variation modeling for gen- eral time series analysis. In International Conference on Learning Representations, 2023.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Fits: Modeling time series with 10k parameters", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>: Modeling time series with 10k parameters. In The Twelfth International Conference on Learning Representations, 2024.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Promptcast: A new promptbased learning paradigm for time series forecasting", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": ["D"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "IEEE Transactions on Knowledge and Data Engineering", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> <PERSON>. Promptcast: A new prompt- based learning paradigm for time series forecasting. IEEE Transactions on Knowledge and Data Engineering, 2023.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Are transformers effective for time series forecasting", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "37", "issue": "", "pages": "11121--11128", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Are transformers effective for time series forecasting? In Proceedings of the AAAI conference on artificial intelligence, volume 37, pp. 11121-11128, 2023.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Less is more: Fast multivariate time series forecasting with light sampling-oriented mlp structures", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Bian", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2207.01186"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON> is more: Fast multivariate time series forecasting with light sampling-oriented mlp structures. arXiv preprint arXiv:2207.01186, 2022.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Robust recurrent neural networks for time series forecasting", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": ["W"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Neurocomputing", "volume": "526", "issue": "", "pages": "143--157", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> <PERSON><PERSON> recurrent neural networks for time series forecast- ing. Neurocomputing, 526:143-157, 2023.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Informer: Beyond efficient transformer for long sequence time-series forecasting", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "35", "issue": "", "pages": "11106--11115", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Beyond efficient transformer for long sequence time-series forecasting. In Proceedings of the AAAI conference on artificial intelligence, volume 35, pp. 11106-11115, 2021.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Film: Frequency improved legendre memory model for long-term time series forecasting", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Sun", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "12677--12690", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Film: Frequency improved legendre memory model for long-term time series forecasting. Advances in Neural Information Processing Systems, 35:12677- 12690, 2022a.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Fedformer: Frequency enhanced decomposed transformer for long-term series forecasting", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "R", "middle": [], "last": "", "suffix": ""}], "year": 2022, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "27268--27286", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, R. <PERSON>: Frequency enhanced decomposed trans- former for long-term series forecasting. In Interna- tional conference on machine learning, pp. 27268-27286. PMLR, 2022b.", "links": null}}, "ref_entries": {"FIGREF0": {"num": null, "text": "Figure 1: Comparison of MSE and parameters between SparseTSF and other mainstream models on the Electricity dataset with a forecast horizon of 720.", "uris": null, "type_str": "figure", "fig_num": "1"}, "FIGREF1": {"num": null, "text": "Figure 2: SparseTSF architecture.", "uris": null, "type_str": "figure", "fig_num": "2"}, "FIGREF2": {"num": null, "text": "Figure 3: Comparison of autocorrelation in original and downsampled subsequences for the first channel in the ETTh1 dataset.", "uris": null, "type_str": "figure", "fig_num": "3"}, "FIGREF3": {"num": null, "text": "Figure 4: Visualization of normalized weights of the model trained on the ETTh1 dataset with both look-back length (X-axis) and forecast horizon (Y-axis) of 96.", "uris": null, "type_str": "figure", "fig_num": "4"}, "FIGREF4": {"num": null, "text": "Figure 5: Schematic illustration of SparseTSF.", "uris": null, "type_str": "figure", "fig_num": "5"}, "FIGREF5": {"num": null, "text": "Figure 6: Comparison of autocorrelation in original and downsampled subsequences for the last channel in the Traffic dataset.", "uris": null, "type_str": "figure", "fig_num": "6"}, "FIGREF6": {"num": null, "text": "Figure 7: Visualization of normalized weights of the model trained on the Traffic dataset with both look-back length (X-axis) and forecast horizon (Y-axis) of 336.", "uris": null, "type_str": "figure", "fig_num": "7"}, "TABREF0": {"num": null, "text": "Summary of datasets.", "html": null, "content": "<table><tr><td>Datasets</td><td colspan=\"3\">ETTh1 &amp; ETTh2 Electricity Traffic</td></tr><tr><td>Channels</td><td>7</td><td>321</td><td>862</td></tr><tr><td>Frequency</td><td>hourly</td><td>hourly</td><td>hourly</td></tr><tr><td>Timesteps</td><td>17,420</td><td>26,304</td><td>17,544</td></tr></table>", "type_str": "table"}, "TABREF1": {"num": null, "text": "MSE results of multivariate long-term time series forecasting comparing SparseTSF with other mainstream models. The top two results are highlighted in bold. The reported results of SparseTSF are averaged over 5 runs with standard deviation included. 'Imp.' denotes the improvement compared to the best-performing baseline models.", "html": null, "content": "<table><tr><td>Dataset</td><td/><td colspan=\"2\">ETTh1</td><td/><td/><td colspan=\"2\">ETTh2</td><td/><td/><td colspan=\"2\">Electricity</td><td/><td/><td colspan=\"2\">Traffic</td><td/></tr><tr><td>Horizon</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td></tr><tr><td>Informer (2021)</td><td>0.865</td><td>1.008</td><td>1.107</td><td>1.181</td><td>3.755</td><td>5.602</td><td>4.721</td><td>3.647</td><td>0.274</td><td>0.296</td><td>0.300</td><td>0.373</td><td>0.719</td><td>0.696</td><td>0.777</td><td>0.864</td></tr><tr><td>Autoformer (2021)</td><td>0.449</td><td>0.500</td><td>0.521</td><td>0.514</td><td>0.358</td><td>0.456</td><td>0.482</td><td>0.515</td><td>0.201</td><td>0.222</td><td>0.231</td><td>0.254</td><td>0.613</td><td>0.616</td><td>0.622</td><td>0.660</td></tr><tr><td>Pyraformer (2022b)</td><td>0.664</td><td>0.790</td><td>0.891</td><td>0.963</td><td>0.645</td><td>0.788</td><td>0.907</td><td>0.963</td><td>0.386</td><td>0.386</td><td>0.378</td><td>0.376</td><td>2.085</td><td>0.867</td><td>0.869</td><td>0.881</td></tr><tr><td>FEDformer (2022b)</td><td>0.376</td><td>0.420</td><td>0.459</td><td>0.506</td><td>0.346</td><td>0.429</td><td>0.496</td><td>0.463</td><td>0.193</td><td>0.201</td><td>0.214</td><td>0.246</td><td>0.587</td><td>0.604</td><td>0.621</td><td>0.626</td></tr><tr><td>FiLM (2022a)</td><td>0.371</td><td>0.414</td><td>0.442</td><td>0.465</td><td>0.284</td><td>0.357</td><td>0.377</td><td>0.439</td><td>0.154</td><td>0.164</td><td>0.188</td><td>0.236</td><td>0.416</td><td>0.408</td><td>0.425</td><td>0.520</td></tr><tr><td>TimesNet (2023)</td><td>0.384</td><td>0.436</td><td>0.491</td><td>0.521</td><td>0.340</td><td>0.402</td><td>0.452</td><td>0.462</td><td>0.168</td><td>0.184</td><td>0.198</td><td>0.220</td><td>0.593</td><td>0.617</td><td>0.629</td><td>0.640</td></tr><tr><td>PatchTST (2023)</td><td>0.370</td><td>0.413</td><td>0.422</td><td>0.447</td><td>0.274</td><td>0.341</td><td>0.329</td><td>0.379</td><td>0.129</td><td>0.147</td><td>0.163</td><td>0.197</td><td>0.360</td><td>0.379</td><td>0.392</td><td>0.432</td></tr><tr><td>DLinear (2023)</td><td>0.374</td><td>0.405</td><td>0.429</td><td>0.440</td><td>0.338</td><td>0.381</td><td>0.400</td><td>0.436</td><td>0.140</td><td>0.153</td><td>0.169</td><td>0.203</td><td>0.410</td><td>0.423</td><td>0.435</td><td>0.464</td></tr><tr><td>FITS (2024)</td><td>0.375</td><td>0.408</td><td>0.429</td><td>0.427</td><td>0.274</td><td>0.333</td><td>0.340</td><td>0.374</td><td>0.138</td><td>0.152</td><td>0.166</td><td>0.205</td><td>0.401</td><td>0.407</td><td>0.420</td><td>0.456</td></tr><tr><td>SparseTSF (ours)</td><td colspan=\"16\">0.359 ±0.006 ±0.002 ±0.001 ±0.001 ±0.005 ±0.003 ±0.004 ±0.001 ±0.001 ±0.001 ±0.001 ±0.001 ±0.001 ±0.001 ±0.001 ±0.002 0.397 0.404 0.417 0.267 0.314 0.312 0.370 0.138 0.146 0.164 0.203 0.382 0.388 0.402 0.445</td></tr><tr><td>Imp.</td><td colspan=\"8\">+0.011 +0.008 +0.018 +0.010 +0.007 +0.019 +0.017 +0.004</td><td>-0.009</td><td>+0.001</td><td>-0.001</td><td>-0.006</td><td>-0.022</td><td>-0.009</td><td>-0.010</td><td>-0.013</td></tr><tr><td colspan=\"8\">line models in terms of both static and runtime metrics,</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>including:</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"8\">1. Parameters: The total number of trainable parameters</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"6\">in the model, representing the model's size.</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr></table>", "type_str": "table"}, "TABREF2": {"num": null, "text": "Static and runtime metrics of SparseTSF and other mainstream models on the Electricity Dataset with a forecast horizon of 720. Here, the look-back length for each model is set to be consistent with their respective official papers, such as 336 for DLinear and 720 for FITS.SparseTSF significantly outperforms other mainstream models, rivaling the existing lightweight models (i.e., DLinear and FITS). Herein, DLinear benefits from a shorter lookback length, achieving the lowest overhead, while FITS and SparseTSF incur additional overhead due to extra operations (i.e., Fourier transformation and resampling).", "html": null, "content": "<table><tr><td>Model</td><td>Parameters</td><td>MACs</td><td colspan=\"2\">Max Mem.(MB) Epoch Time(s)</td></tr><tr><td>Informer (2021)</td><td>12.53 M</td><td>3.97 G</td><td>969.7</td><td>70.1</td></tr><tr><td>Autoformer (2021)</td><td>12.22 M</td><td>4.41 G</td><td>2631.2</td><td>107.7</td></tr><tr><td>FEDformer (2022b)</td><td>17.98 M</td><td>4.41 G</td><td>1102.5</td><td>238.7</td></tr><tr><td><PERSON><PERSON> (2022a)</td><td>12.22 M</td><td>4.41 G</td><td>1773.9</td><td>78.3</td></tr><tr><td>PatchTST (2023)</td><td>6.31 M</td><td>11.21 G</td><td>10882.3</td><td>290.3</td></tr><tr><td>DLinear (2023)</td><td>485.3 K</td><td>156.0 M</td><td>123.8</td><td>25.4</td></tr><tr><td>FITS (2024)</td><td>10.5 K</td><td>79.9 M</td><td>496.7</td><td>35.0</td></tr><tr><td>SparseTSF (Ours)</td><td>0.92 K</td><td>12.71 M</td><td>125.2</td><td>31.3</td></tr><tr><td colspan=\"5\">Table 3 displays the comparative results. It is evident that</td></tr><tr><td colspan=\"5\">SparseTSF significantly outperforms other models in terms</td></tr></table>", "type_str": "table"}, "TABREF3": {"num": null, "text": "Comparison of the scale of parameters on Electricity dataset between SparseTSF and FITS models under different configurations of look-back length and forecast horizon, where SparseTSF operates with w = 24 and FITS employs COF at the 2 th harmonic.", "html": null, "content": "<table><tr><td colspan=\"2\">Model</td><td colspan=\"3\">SparseTSF (Ours)</td><td/><td>FITS (2024)</td></tr><tr><td>Horizon</td><td colspan=\"4\">Look-back 96 192 336 720</td><td>96</td><td>192</td><td>336</td><td>720</td></tr><tr><td>96</td><td/><td>41</td><td>57</td><td>81 145</td><td>840</td><td>1,218 2,091 5,913</td></tr><tr><td colspan=\"2\">192</td><td>57</td><td colspan=\"4\">89 137 265 1,260 1,624 2,542 6,643</td></tr><tr><td colspan=\"2\">336</td><td colspan=\"5\">81 137 221 445 1,890 2,233 3,280 7,665</td></tr><tr><td colspan=\"2\">720</td><td colspan=\"5\">145 265 445 925 3,570 3,857 5,125 10,512</td></tr><tr><td colspan=\"7\">Additionally, we conducted a comprehensive comparison</td></tr><tr><td colspan=\"7\">with FITS, a recent milestone work in the field of LTSF</td></tr><tr><td colspan=\"7\">model lightweight progression. The results in Table 4 reveal</td></tr><tr><td colspan=\"7\">that SparseTSF significantly surpasses FITS in terms of pa-</td></tr><tr><td colspan=\"7\">rameter scale under any input-output length configuration.</td></tr><tr><td colspan=\"7\">Therefore, SparseTSF marks another significant advance-</td></tr><tr><td colspan=\"7\">ment in the journey towards lightweight LTSF models.</td></tr></table>", "type_str": "table"}, "TABREF4": {"num": null, "text": "Ablation MSE results of the Sparse technique. All results are collected with a unified channel-independent and instance normalization strategy. The 'Boost' indicates the percentage of performance improvement after incorporating the Sparse technique.<PERSON> et al., 2014) models. As demonstrated in the results of Table5, the incorporation of the Sparse technique significantly enhances the performance of all models, including Linear, Transformer, and GRU. Specifically, the Linear model showed an average improvement of 4.7%, the Transformer by 21.4%, and the GRU by 12.4%. These results emphatically illustrate the efficacy of the Sparse technique. Therefore, the Sparse technique can substantially improve the performance of base models in LTSF tasks.Representation Learning of the Sparse Technique In Section 3.3, we theoretically analyzed the reasons why the Sparse technique can enhance the performance of forecasting tasks. Here, we further reveal the role of the Sparse technique from a representation learning perspective. Figure3shows the distribution of normalized weights for both the trained Linear model and the SparseTSF model. The weight of the Linear model is an L × H matrix, which can be directly obtained. However, as the SparseTSF model is a sparse model, we need to acquire its equivalent weights. To do this, we first input H one-hot encoded vectors of length L into the SparseTSF model (when L equals H, this can be simplified to a diagonal matrix, i.e., diagonal elements are 1, and other elements are 0). We then obtain and transpose the corresponding output to get the equivalent L × H weight matrix of SparseTSF. When L equals H, this process is formulated as:", "html": null, "content": "<table><tr><td>Dataset</td><td/><td colspan=\"2\">ETTh1</td><td/><td/><td>ETTh2</td><td/><td/></tr><tr><td>Horizon</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td></tr><tr><td>Linear</td><td>0.371</td><td>0.460</td><td>0.417</td><td colspan=\"5\">0.424 0.257 0.337 0.336 0.391</td></tr><tr><td>+sparse</td><td>0.359</td><td>0.397</td><td>0.404</td><td colspan=\"5\">0.417 0.267 0.314 0.312 0.370</td></tr><tr><td>Boost</td><td colspan=\"3\">3.3% 13.8% 3.1%</td><td colspan=\"5\">1.7% -3.9% 6.9% 7.1% 5.3%</td></tr><tr><td colspan=\"2\">Transformer 0.697</td><td>0.732</td><td>0.714</td><td colspan=\"5\">0.770 0.340 0.376 0.366 0.468</td></tr><tr><td>+sparse</td><td>0.406</td><td>0.442</td><td>0.446</td><td colspan=\"5\">0.489 0.322 0.380 0.353 0.432</td></tr><tr><td>Boost</td><td colspan=\"8\">41.7% 39.6% 37.5% 36.5% 5.2% -1.0% 3.6% 7.7%</td></tr><tr><td>GRU</td><td>0.415</td><td>0.529</td><td>0.512</td><td colspan=\"5\">0.620 0.296 0.345 0.363 0.454</td></tr><tr><td>+sparse</td><td>0.356</td><td>0.391</td><td>0.437</td><td colspan=\"5\">0.455 0.282 0.332 0.356 0.421</td></tr><tr><td>Boost</td><td colspan=\"5\">14.1% 26.1% 14.7% 26.7% 4.8%</td><td colspan=\"3\">3.7% 1.9% 7.2%</td></tr><tr><td colspan=\"3\">et al., 2017) and GRU (</td><td/><td/><td/><td/><td/><td/></tr></table>", "type_str": "table"}, "TABREF5": {"num": null, "text": "MSE results of SparseTSF on ETTh1 with varied hyperparameters w.", "html": null, "content": "<table><tr><td>Horizon</td><td>SparseTSF (w=6)</td><td>SparseTSF (w=12)</td><td>SparseTSF (w=24)</td><td>SparseTSF (w=48)</td><td>FITS (2024)</td><td>DLinear (2023)</td><td>PatchTST (2023)</td></tr><tr><td>96</td><td>0.376</td><td>0.369</td><td>0.359</td><td>0.380</td><td>0.375</td><td>0.374</td><td>0.370</td></tr><tr><td>192</td><td>0.410</td><td>0.402</td><td>0.397</td><td>0.400</td><td>0.408</td><td>0.405</td><td>0.413</td></tr><tr><td>336</td><td>0.408</td><td>0.406</td><td>0.404</td><td>0.399</td><td>0.429</td><td>0.429</td><td>0.422</td></tr><tr><td>720</td><td>0.427</td><td>0.423</td><td>0.417</td><td>0.427</td><td>0.427</td><td>0.440</td><td>0.447</td></tr><tr><td>Avg.</td><td>0.405</td><td>0.400</td><td>0.394</td><td>0.402</td><td>0.410</td><td>0.412</td><td>0.413</td></tr></table>", "type_str": "table"}, "TABREF6": {"num": null, "text": "", "html": null, "content": "<table><tr><td colspan=\"9\">: Comparison of generalization capabilities between</td></tr><tr><td colspan=\"9\">SparseTSF and other mainstream models. 'Dataset A →</td></tr><tr><td colspan=\"9\">Dataset B' indicates training and validation on the training</td></tr><tr><td colspan=\"9\">and validation sets of Dataset A, followed by testing on the</td></tr><tr><td colspan=\"2\">test set of Dataset B.</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>Dataset</td><td colspan=\"3\">ETTh2 → ETTh1</td><td/><td/><td colspan=\"2\">Electricity → ETTh1</td><td/></tr><tr><td>Horizon</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td></tr><tr><td>Informer (2021)</td><td colspan=\"4\">0.844 0.921 0.898 0.829</td><td>\\</td><td>\\</td><td>\\</td><td>\\</td></tr><tr><td colspan=\"5\">Autoformer (2021) 0.978 1.058 0.944 0.921</td><td>\\</td><td>\\</td><td>\\</td><td>\\</td></tr><tr><td colspan=\"5\">FEDformer (2022b) 0.878 0.927 0.939 0.967</td><td>\\</td><td>\\</td><td>\\</td><td>\\</td></tr><tr><td>FiLM (2022a)</td><td colspan=\"4\">0.876 0.904 0.919 0.925</td><td>\\</td><td>\\</td><td>\\</td><td>\\</td></tr><tr><td>PatchTST (2023)</td><td colspan=\"8\">0.449 0.478 0.482 0.476 0.400 0.424 0.475 0.472</td></tr><tr><td>DLinear (2023)</td><td colspan=\"8\">0.430 0.478 0.458 0.506 0.397 0.428 0.447 0.470</td></tr><tr><td>Fits (2024)</td><td colspan=\"8\">0.419 0.427 0.428 0.445 0.380 0.414 0.440 0.448</td></tr><tr><td>SparseTSF (Ours)</td><td colspan=\"8\">0.370 0.401 0.412 0.419 0.373 0.409 0.433 0.439</td></tr><tr><td colspan=\"9\">Experimental results, as shown in Table 7, reveal that</td></tr><tr><td colspan=\"9\">SparseTSF outperforms other models in both similar do-</td></tr><tr><td colspan=\"9\">main generalization (ETTh2 to ETTh1) and less similar</td></tr></table>", "type_str": "table"}, "TABREF8": {"num": null, "text": "Summary of datasets with ultra-long periods.", "html": null, "content": "<table><tr><td>Datasets</td><td colspan=\"3\">ETTm1 ETTm2 Weather</td></tr><tr><td>Channels</td><td>7</td><td>7</td><td>21</td></tr><tr><td colspan=\"4\">Frequency 15 mins 15 mins 10 mins</td></tr><tr><td>Timesteps</td><td>69,680</td><td>69,680</td><td>52,696</td></tr></table>", "type_str": "table"}, "TABREF9": {"num": null, "text": "MSE results of SparseTSF on ultra-long period datasets with varied hyperparameters w. The forecast horizon is set as 720.", "html": null, "content": "<table><tr><td>Dataset</td><td/><td/><td colspan=\"2\">Parameter w</td><td/><td/><td/></tr><tr><td>144</td><td>72</td><td>48</td><td>24</td><td>12</td><td>6</td><td>2</td><td>1</td></tr><tr><td colspan=\"8\">ETTm1 0.450 0.450 0.422 0.422 0.421 0.415 0.415 0.429</td></tr><tr><td colspan=\"8\">ETTm2 0.375 0.371 0.373 0.352 0.354 0.349 0.349 0.357</td></tr><tr><td colspan=\"8\">Weather 0.332 0.329 0.325 0.321 0.319 0.319 0.318 0.322</td></tr><tr><td colspan=\"8\">SparseTSF against other models under the setting of w = 4,</td></tr><tr><td colspan=\"8\">where SparseTSF ranks within the top 3 in most cases. In</td></tr><tr><td colspan=\"8\">this scenario, SparseTSF remains significantly lighter com-</td></tr><tr><td colspan=\"8\">pared to other mainstream models. This indicates that the</td></tr><tr><td colspan=\"8\">Sparse forecasting technique not only effectively reduces pa-</td></tr><tr><td colspan=\"8\">rameter size but also enhances prediction accuracy in most</td></tr><tr><td>scenarios.</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"5\">D. More Results and Analysis</td><td/><td/><td/></tr><tr><td colspan=\"7\">D.1. Comparison Results after Fixing the Code Bug</td><td/></tr></table>", "type_str": "table"}, "TABREF10": {"num": null, "text": "MSE results on ultra-long period datasets comparing SparseTSF (w = 4) with other mainstream models. The ranking of SparseTSF's performance is shown in parentheses.", "html": null, "content": "<table><tr><td>Dataset</td><td/><td colspan=\"2\">ETTm1</td><td/><td/><td colspan=\"2\">ETTm2</td><td/><td/><td colspan=\"2\">Weather</td><td/></tr><tr><td>Horizon</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td></tr><tr><td>Informer (2021)</td><td>0.672</td><td>0.795</td><td>1.212</td><td>1.166</td><td>0.365</td><td>0.533</td><td>1.363</td><td>3.379</td><td>0.300</td><td>0.598</td><td>0.578</td><td>1.059</td></tr><tr><td>Autoformer (2021)</td><td>0.505</td><td>0.553</td><td>0.621</td><td>0.671</td><td>0.255</td><td>0.281</td><td>0.339</td><td>0.433</td><td>0.266</td><td>0.307</td><td>0.359</td><td>0.419</td></tr><tr><td>Pyraformer (2022b)</td><td>0.543</td><td>0.557</td><td>0.754</td><td>0.908</td><td>0.435</td><td>0.730</td><td>1.201</td><td>3.625</td><td>0.896</td><td>0.622</td><td>0.739</td><td>1.004</td></tr><tr><td>FEDformer (2022b)</td><td>0.379</td><td>0.426</td><td>0.445</td><td>0.543</td><td>0.203</td><td>0.269</td><td>0.325</td><td>0.421</td><td>0.217</td><td>0.276</td><td>0.339</td><td>0.403</td></tr><tr><td>TimesNet (2023)</td><td>0.338</td><td>0.374</td><td>0.410</td><td>0.478</td><td>0.187</td><td>0.249</td><td>0.321</td><td>0.408</td><td>0.172</td><td>0.219</td><td>0.280</td><td>0.365</td></tr><tr><td>PatchTST (2023)</td><td>0.293</td><td>0.333</td><td>0.369</td><td>0.416</td><td>0.166</td><td>0.223</td><td>0.274</td><td>0.362</td><td>0.149</td><td>0.194</td><td>0.245</td><td>0.314</td></tr><tr><td>DLinear (2023)</td><td>0.299</td><td>0.335</td><td>0.369</td><td>0.425</td><td>0.167</td><td>0.221</td><td>0.274</td><td>0.368</td><td>0.176</td><td>0.218</td><td>0.262</td><td>0.323</td></tr><tr><td>FITS (2024)</td><td>0.305</td><td>0.339</td><td>0.367</td><td>0.418</td><td>0.164</td><td>0.217</td><td>0.269</td><td>0.347</td><td>0.145</td><td>0.188</td><td>0.236</td><td>0.308</td></tr><tr><td>SparseTSF (ours)</td><td colspan=\"12\">0.314(4) 0.343(4) 0.369(2) 0.418(2) 0.165(2) 0.218(2) 0.272(2) 0.35(2) 0.172(3) 0.215(3) 0.26(3) 0.318(3)</td></tr></table>", "type_str": "table"}, "TABREF11": {"num": null, "text": "MSE results of multivariate long-term time series forecasting comparing SparseTSF with other mainstream models after fixing code bug. The top two results are highlighted in bold.", "html": null, "content": "<table><tr><td>Dataset</td><td/><td colspan=\"2\">ETTh1</td><td/><td/><td colspan=\"2\">ETTh2</td><td/><td/><td colspan=\"2\">Electricity</td><td/><td/><td colspan=\"2\">Traffic</td><td/></tr><tr><td>Horizon</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td></tr><tr><td colspan=\"17\">FEDformer (2022b) 0.375 0.427 0.459 0.484 0.340 0.433 0.508 0.480 0.188 0.197 0.212 0.244 0.573 0.611 0.621 0.630</td></tr><tr><td>TimesNet (2023)</td><td colspan=\"16\">0.384 0.436 0.491 0.521 0.340 0.402 0.452 0.462 0.168 0.184 0.198 0.220 0.593 0.617 0.629 0.640</td></tr><tr><td>PatchTST (2023)</td><td colspan=\"16\">0.385 0.413 0.440 0.456 0.274 0.338 0.367 0.391 0.129 0.149 0.166 0.210 0.366 0.388 0.398 0.457</td></tr><tr><td>DLinear (2023)</td><td colspan=\"16\">0.384 0.443 0.446 0.504 0.282 0.350 0.414 0.588 0.140 0.153 0.169 0.204 0.413 0.423 0.437 0.466</td></tr><tr><td>FITS (2024)</td><td colspan=\"16\">0.382 0.417 0.436 0.433 0.272 0.333 0.355 0.378 0.145 0.159 0.175 0.212 0.398 0.409 0.421 0.457</td></tr><tr><td>SparseTSF (ours)</td><td colspan=\"16\">0.362 0.403 0.434 0.426 0.294 0.339 0.359 0.383 0.138 0.151 0.166 0.205 0.389 0.398 0.411 0.448</td></tr></table>", "type_str": "table"}, "TABREF12": {"num": null, "text": "MSE results of SparseTSF with varied look-back lengths. .371 0.393 0.354 0.288 0.285 0.272 0.278 0.209 0.160 0.146 0.138 0.672 0.455 0.412 0.383 192 0.433 0.434 0.418 0.398 0.363 0.346 0.323 0.315 0.202 0.166 0.154 0.147 0.608 0.453 0.415 0.388 336 0.447 0.420 0.390 0.405 0.366 0.335 0.314 0.311 0.217 0.184 0.172 0.164 0.609 0.468 0.428 0.403 720 0.451 0.426 0.413 0.418 0.407 0.389 0.372 0.371 0.259 0.223 0.210 0.205 0.650 0.493 0.462 0.446 Avg. 0.428 0.413 0.404 0.394 0.356 0.339 0.320 0.319 0.222 0.183 0.171 0.163 0.635 0.467 0.429 0.405", "html": null, "content": "<table><tr><td colspan=\"2\">Dataset</td><td/><td colspan=\"2\">ETTh1</td><td/><td/><td colspan=\"2\">ETTh2</td><td/><td/><td colspan=\"2\">Electricity</td><td/><td/><td colspan=\"2\">Traffic</td><td/></tr><tr><td>Horizon</td><td>Look-back</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td><td>96</td><td>192</td><td>336</td><td>720</td></tr><tr><td>96</td><td/><td colspan=\"2\">0.380 0</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr></table>", "type_str": "table"}, "TABREF13": {"num": null, "text": "Ablation results of IN strategy in SparseTSF.", "html": null, "content": "<table><tr><td>Dataset</td><td colspan=\"2\">ETTh1</td><td colspan=\"2\">ETTh2</td><td colspan=\"2\">Electricity</td><td colspan=\"2\">Traffic</td></tr><tr><td colspan=\"9\">Horizon w/ IN w/o IN w/ IN w/o IN w/ IN w/o IN w/ IN w/o IN</td></tr><tr><td>96</td><td>0.359</td><td>0.37</td><td>0.267</td><td>0.327</td><td>0.138</td><td>0.138</td><td>0.382</td><td>0.382</td></tr><tr><td>192</td><td>0.397</td><td>0.413</td><td>0.314</td><td>0.426</td><td>0.146</td><td>0.146</td><td>0.388</td><td>0.387</td></tr><tr><td>336</td><td>0.404</td><td>0.431</td><td>0.312</td><td>0.482</td><td>0.164</td><td>0.163</td><td>0.402</td><td>0.401</td></tr><tr><td>720</td><td>0.417</td><td>0.462</td><td>0.37</td><td>0.866</td><td>0.203</td><td>0.198</td><td>0.445</td><td>0.444</td></tr><tr><td colspan=\"7\">D.4. Comparison Results with N-HiTS and</td><td/><td/></tr><tr><td colspan=\"3\">OneShotSTL</td><td/><td/><td/><td/><td/><td/></tr></table>", "type_str": "table"}, "TABREF14": {"num": null, "text": "Comparison Results with N-HiTS and OneShot-STL. In this comparison, SparseTSF and N-HiTS are evaluated based on multivariate prediction results (MSE), while SparseTSF and OneShotSTL are compared using univariate prediction results (MAE). Their results are sourced from their respective official papers.", "html": null, "content": "<table><tr><td>Dataset</td><td colspan=\"5\">Horizon Nhit SparseTSF OneShotSTL SparseTSF</td></tr><tr><td/><td>96</td><td>0.176</td><td>0.165</td><td>0.211</td><td>0.187</td></tr><tr><td>ETTm2</td><td>192 336</td><td>0.245 0.295</td><td>0.218 0.272</td><td>0.244 0.273</td><td>0.233 0.268</td></tr><tr><td/><td>720</td><td>0.401</td><td>0.350</td><td>0.321</td><td>0.324</td></tr><tr><td/><td>96</td><td>0.147</td><td>0.138</td><td>0.331</td><td>0.314</td></tr><tr><td>Electricity</td><td>192 336</td><td>0.167 0.186</td><td>0.146 0.164</td><td>0.355 0.389</td><td>0.334 0.366</td></tr><tr><td/><td>720</td><td>0.243</td><td>0.203</td><td>0.444</td><td>0.416</td></tr><tr><td/><td>96</td><td>0.402</td><td>0.382</td><td>0.181</td><td>0.179</td></tr><tr><td>Traffic</td><td>192 336</td><td>0.42 0.448</td><td>0.388 0.402</td><td>0.181 0.182</td><td>0.175 0.184</td></tr><tr><td/><td>720</td><td>0.539</td><td>0.445</td><td>0.199</td><td>0.203</td></tr></table>", "type_str": "table"}}}}