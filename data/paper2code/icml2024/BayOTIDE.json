{"paper_id": "BayOTIDE", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-24T23:34:42.854337Z"}, "title": "BayOTIDE: Bayesian Online Multivariate Time Series Imputation with Functional Decomposition", "authors": [{"first": "Shikai", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Utah", "location": {"country": "USA"}}, "email": ""}, {"first": "†", "middle": [], "last": "Qingsong", "suffix": "", "affiliation": {}, "email": "<<EMAIL>>."}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Carnegie Mellon University", "location": {"country": "USA"}}, "email": ""}, {"first": "Shandian", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Utah", "location": {"country": "USA"}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "In real-world scenarios such as traffic and energy management, we frequently encounter large volumes of time-series data characterized by missing values, noise, and irregular sampling patterns. While numerous imputation methods have been proposed, the majority tend to operate within a local horizon, which involves dividing long sequences into batches of fixed-length segments for model training. This local horizon often leads to the overlooking of global trends and periodic patterns. More importantly, most methods assume the observations are sampled at regular timestamps, and fail to handle complex irregular sampled time series in various applications. Additionally, most existing methods are learned in an offline manner. Thus, it is not suitable for applications with rapidly arriving streaming data. To address these challenges, we propose BayOTIDE : Bayesian Online Multivariate Time series Imputation with functional decomposition. Our method conceptualizes multivariate time series as the weighted combination of groups of low-rank temporal factors with different patterns. We employ a suite of Gaussian Processes (GPs),each with a unique kernel, as functional priors to model these factors. For computational efficiency, we further convert the GPs into a statespace prior by constructing an equivalent stochastic differential equation (SDE), and developing a scalable algorithm for online inference. The proposed method can not only handle imputation over arbitrary timestamps, but also offer uncertainty quantification and interpretability for the downstream application. We evaluate our method", "pdf_parse": {"paper_id": "BayOTIDE", "_pdf_hash": "", "abstract": [{"text": "In real-world scenarios such as traffic and energy management, we frequently encounter large volumes of time-series data characterized by missing values, noise, and irregular sampling patterns. While numerous imputation methods have been proposed, the majority tend to operate within a local horizon, which involves dividing long sequences into batches of fixed-length segments for model training. This local horizon often leads to the overlooking of global trends and periodic patterns. More importantly, most methods assume the observations are sampled at regular timestamps, and fail to handle complex irregular sampled time series in various applications. Additionally, most existing methods are learned in an offline manner. Thus, it is not suitable for applications with rapidly arriving streaming data. To address these challenges, we propose BayOTIDE : Bayesian Online Multivariate Time series Imputation with functional decomposition. Our method conceptualizes multivariate time series as the weighted combination of groups of low-rank temporal factors with different patterns. We employ a suite of Gaussian Processes (GPs),each with a unique kernel, as functional priors to model these factors. For computational efficiency, we further convert the GPs into a statespace prior by constructing an equivalent stochastic differential equation (SDE), and developing a scalable algorithm for online inference. The proposed method can not only handle imputation over arbitrary timestamps, but also offer uncertainty quantification and interpretability for the downstream application. We evaluate our method", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Multivariate time series data are ubiquitous and generated quickly in many real-world applications (<PERSON> et al., 2023) , such as traffic (<PERSON> et al., 2015) and energy (<PERSON> et al., 2023) . However, the collected data are often incomplete and noisy due to sensor failures, communication errors, or other reasons. The missing values in the time series data can lead to inaccurate downstream analysis. Therefore, it is essential to impute the missing values in the time series data in an efficient way.", "cite_spans": [{"start": 99, "end": 117, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF19"}, {"start": 136, "end": 153, "text": "(<PERSON> et al., 2015)", "ref_id": "BIBREF22"}, {"start": 165, "end": 183, "text": "(<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Most early methods for time series imputation (<PERSON><PERSON><PERSON> & Rodriguez, 2004; Van <PERSON> & Groot<PERSON>-<PERSON>, 2011; <PERSON><PERSON><PERSON> & <PERSON>, 2012) are based on statistical models. DNN-based imputation methods have gotten boosted attention (<PERSON> & Wang, 2020) in recent years, for their ability to capture complex non-linear patterns. Another remarkable direction is to apply diffusion models (<PERSON> et al., 2020; <PERSON> et al., 2020) to handle probabilistic imputation, where filling the missing value can be modeled as a denoising process. Most recent work TIDER (LIU et al., 2023) proposed another imputation direction to apply the matrix factorization and decompose the series into disentangled representations.", "cite_spans": [{"start": 46, "end": 71, "text": "(Acuna & Rodriguez, 2004;", "ref_id": "BIBREF1"}, {"start": 72, "end": 111, "text": "<PERSON> & Groothuis-Oudshoorn, 2011;", "ref_id": "BIBREF39"}, {"start": 112, "end": 135, "text": "Du<PERSON><PERSON> & Ko<PERSON>man, 2012)", "ref_id": "BIBREF12"}, {"start": 228, "end": 247, "text": "(<PERSON> & Wang, 2020)", "ref_id": "BIBREF13"}, {"start": 381, "end": 400, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF37"}, {"start": 401, "end": 417, "text": "<PERSON> et al., 2020)", "ref_id": "BIBREF18"}, {"start": 548, "end": 566, "text": "(LIU et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Despite the success of the proposed methods, they are limited in several aspects. First, most DNN-based and diffusionbased methods are trained by splitting the long sequence into small patches. This local horizon can fail to capture the crucial global patterns (<PERSON><PERSON><PERSON> & Strodthoff, 2022; <PERSON><PERSON> et al., 2022) , such as trends and periodicity, leading to less interpretability. Second, many methods assume the observations are sampled at regular time stamps, and always under-utilize or ignore the real timestamps. Thus, those models can only impute on fixed-step and discretized time points, instead of the arbitrary time stamps at the whole continuous field. Lastly, in real-world applications such as electricity load monitoring, massive time series data are generated quickly and collected in a streaming manner (<PERSON> et al., 2023) . It is extremely costly or even impossible to retrain the model from scratch when new data arrives. Thus, to align with streaming data, the imputation model should work and update in an efficient online manner. However, to the best of our knowledge, all prior imputation methods are designed and optimized in an offline manner, i.e., go through all collected data several epochs for training, which is not suitable for streaming data scenarios.", "cite_spans": [{"start": 261, "end": 289, "text": "(Alcaraz & Strodthoff, 2022;", "ref_id": "BIBREF2"}, {"start": 290, "end": 307, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF43"}, {"start": 814, "end": 832, "text": "(<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "To handle those limitations, we propose BayOTIDE : Bayesian Online multivariate Time series Imputation with functional DEcomposition. BayOTIDE treats the observed values of multivariate time series as the noisy samples from a continuous temporal function. Then, we decompose the function into groups of weighted functional factors, and each factor is aimed to capture a dynamic pattern. We apply Gaussian Processes (GPs) with smooth and periodic kernels as functional priors to fit the factors. Employing the SDE representation of GPs and moment-matching techniques, we develop an online algorithm to infer the running posterior of weights and factors efficiently. As it is a Bayesian model, BayOTIDE can offer uncertainty quantification and robustness against noise. The learned functional factors can provide not only interpretability but also imputation over arbitrary timestamps. We list the comparison of BayOTIDE and other main-stream imputation methods in Table 1 . In summary, we highlight our contributions as follows:", "cite_spans": [], "ref_spans": [{"start": 969, "end": 970, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We propose BayOTIDE , a novel Bayesian method for multivariate time series imputation. BayOTIDE can explicitly learn the function factors representing various global patterns, which offer interpretability and uncertainty quantification. As BayOTIDE is a continuous model, it can utilize the irregularly sampled timestamps and impute over arbitrary timestamps naturally.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• To the best of our knowledge, BayOTIDE is the first online probabilistic imputation method of multivariate time series that could fit streaming data well. Furthermore, we develop a scalable inference algorithm with closed-form update and linear cost via momentmatching techniques.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We extensively evaluate our method on synthetic and real-world datasets, and the results show that Bay-OTIDE outperforms the state-of-the-art methods in terms of accuracy and efficiency.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Disentangled representations of time series. The most classical framework of decomposing time series into disentangled representations is the seasonal-trend decomposition(STL) (<PERSON> et al., 1990) along with its following work (<PERSON> et al., 2019; <PERSON><PERSON><PERSON>, 2020; <PERSON><PERSON> et al., 2021) , which are non-parametric method to decompose the univariate series into seasonal, trend and residual components. (<PERSON><PERSON> et al., 2018) proposed the structural design to extend decomposition into multivariate and probabilistic cases. Recently, CoST (<PERSON><PERSON> et al., 2022) and TIDER (LIU et al., 2023) show the disentangled representations of multivariate series could get significant performance improvement in forecasting and imputation tasks, respectively, with bonus of interpretability. However, they are not flexible enough to handle the continuous time field and observation noise. <PERSON><PERSON><PERSON> & <PERSON> (2021) propose a similar idea to directly utilize the state-space GPs with mixture kernels to estimate the seasonal-trend factors, but is restricted in univariate series. Besides the imputation and forecasting, learning the disentangled representation of time series is a crucial tool for estimating causal-related latent variables in sequential data (<PERSON> et al., 2021; 2022) . A series of recent works on tensor-valued time series analysis (<PERSON> et al., 2022; 2024; <PERSON> et al., 2024 ) also apply the idea of disentangled representations to learn temporal dynamics in latent spaces.", "cite_spans": [{"start": 176, "end": 200, "text": "(<PERSON> et al., 1990)", "ref_id": "BIBREF10"}, {"start": 231, "end": 249, "text": "(<PERSON> et al., 2019;", "ref_id": "BIBREF42"}, {"start": 250, "end": 266, "text": "<PERSON><PERSON><PERSON>, 2020;", "ref_id": "BIBREF0"}, {"start": 267, "end": 288, "text": "<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF3"}, {"start": 404, "end": 422, "text": "(<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF29"}, {"start": 536, "end": 554, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF43"}, {"start": 565, "end": 583, "text": "(LIU et al., 2023)", "ref_id": null}, {"start": 871, "end": 895, "text": "Benavoli & Corani (2021)", "ref_id": "BIBREF4"}, {"start": 1240, "end": 1258, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF44"}, {"start": 1259, "end": 1264, "text": "2022)", "ref_id": null}, {"start": 1330, "end": 1349, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF14"}, {"start": 1350, "end": 1355, "text": "2024;", "ref_id": null}, {"start": 1356, "end": 1373, "text": "<PERSON> et al., 2024", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Bayesian imputation modeling. Bayesian methods are widely used in time series imputation tasks for robust modeling and uncertainty quantification. Early work directly applies a single Bayesian model like Gaussian Process (<PERSON> et al., 2013) and energy models (<PERSON><PERSON><PERSON> et al., 2013) to model the dynamics. With deep learning boosting in the past ten years, it is popular to utilize the probabilistic modules with various deep networks, such as RNN (<PERSON><PERSON><PERSON> et al., 2021) , VAE (Fortuin et al., 2020) and GAN (<PERSON><PERSON> et al., 2018) . Adopting score-based generative models (SGMs) is another promising direction for probabilistic imputation, which could be used as autoregressive denoising (<PERSON><PERSON> et al., 2021) , conditional diffusion (<PERSON><PERSON><PERSON> et al., 2021) , Schrödinger Bridge (<PERSON> et al., 2023) and statespace blocks (Alcaraz & Strodthoff, 2022) . However, most of the above methods are trained in the offline and patchingsequence manner, which lacks interpretability and may not fit streaming scenarios.", "cite_spans": [{"start": 221, "end": 243, "text": "(<PERSON> et al., 2013)", "ref_id": "BIBREF33"}, {"start": 262, "end": 283, "text": "(<PERSON><PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF6"}, {"start": 449, "end": 471, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF28"}, {"start": 478, "end": 500, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF16"}, {"start": 509, "end": 528, "text": "(<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF46"}, {"start": 686, "end": 706, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF31"}, {"start": 731, "end": 753, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF38"}, {"start": 775, "end": 794, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF9"}, {"start": 817, "end": 845, "text": "(Alcaraz & Strodthoff, 2022)", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "The classical multivariate time series imputation problem is formulated as follows. A N -step multivariate time series ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "X = {x 1 , . . . , x N } ∈ R D×N ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "✓ ✗ ✗ @ @ ✓ Interpretability ✓ ✓ ✓ ✗ ✗ Continuous modeling ✓ ✗ ✗ ✗ @ @", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "Inference manner online offline offline offline offline", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "Table 1 : Comparison of BayOTIDE and main-stream multivariate time series imputation methods. @ @ means only partial models in the family have the property, or it's not clear in the original paper. For example, only deep models with probabilistic modules can offer uncertainty quantification, such as GP-VAE (<PERSON><PERSON> et al., 2020) , but most deep models cannot. The diffusion-based CSDI (<PERSON><PERSON><PERSON> et al., 2021) and CSBI (<PERSON> et al., 2023) ", "cite_spans": [{"start": 308, "end": 330, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF16"}, {"start": 387, "end": 409, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF38"}, {"start": 419, "end": 438, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF9"}], "ref_spans": [{"start": 6, "end": 7, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "κ Matérn = σ 2 √ 2ν l α (x, x ′ ) ν Γ(ν)2 ν-1 K ν √ 2ν l α (x, x ′ ) ,", "eq_num": "(1)"}], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "and periodic kernel:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "κ periodic = σ 2 exp -2 sin 2 (πα (x, x ′ ) /p)/l 2 (2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "are versatile choices to model functions with non-linear and cyclical patterns, respectively. {σ 2 , l, ν, p} are hyperparameters determining the variance, length-scale, smoothness, and periodicity of the function. α (•, •) is the Euclidean distance, and K ν is the modified <PERSON><PERSON> function,Γ(•) is the Gamma function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "Despite the flexibility and capacity, full GP is a computationally expensive model with O(n 3 ) inference cost while handlining n observation data, which is not feasible in practice. To sidestep expensive kernel matrix computation, (<PERSON><PERSON><PERSON> & Särkkä, 2010; Särkkä, 2013) applied the spectral analysis and worked out a crucial statement: a temporal GP with a stationary kernel is equivalent to a linear time-invariant stochastic differential equation (LTI-SDE). Specifically, given f (t) ∼ GP (0, κ (t, t ′ )), we can define a vector-valued companion form:", "cite_spans": [{"start": 232, "end": 260, "text": "(Hartikainen & Särkkä, 2010;", "ref_id": "BIBREF17"}, {"start": 261, "end": 274, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 2013)", "ref_id": "BIBREF34"}], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "z(t) = f (t), df (t) dt , . . . , df m (t) dt ⊤ : t → R m+1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": ", where m is the order of the derivative. Then, the GP is equivalent to the solution of the LTI-SDE with canonical form:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dz(t) dt = Fz(t) + Lw(t),", "eq_num": "(3)"}], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "where F is a (m + 1) × (m + 1) matrix, L is a (m + 1) × 1 vector, and w(t) is a white noise process with spectral density q s . On arbitrary collection of timestamps {t 1 , . . . , t N }, the LTI-SDE (3) can be further discretized as the Markov model with Gaussian transition, known as the state-space model (SSM):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(z(t 1 )) = N (z(t 1 )|0, P ∞ ), p(z(t n+1 )|z(t n )) = N (z(t n+1 )|A n z(t n ), Q n ),", "eq_num": "(4)"}], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "A n = exp(F∆ n ), Q n = tn+1 tn A n LL ⊤ A ⊤ n q s dt, ∆ n = t n+1 -t n", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": ", and P ∞ is the steady-state covariance matrix, which can be obtained by solving the <PERSON><PERSON><PERSON><PERSON> equation FP ∞ + P ∞ F ⊤ + LL ⊤ q s = 0 (<PERSON> & <PERSON>, 1995) . SSM (4) can be efficiently solved with linear cost by classical methods like the <PERSON><PERSON> filter (<PERSON>, 1960) . After inference over z(t), we can easily obtain f (t) by a simple projection: f (t) = [1, 0, . . . 0]z(t).", "cite_spans": [{"start": 134, "end": 160, "text": "(Lancaster & Rodman, 1995)", "ref_id": "BIBREF21"}, {"start": 258, "end": 272, "text": "(<PERSON><PERSON>, 1960)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "We highlight that all the parameters of the LTI-SDE (3) and its SSM (4): {m, F, L, q s , P ∞ } are time-invariant constant and can be derived from the given stationary kernel function. In practice, stationary kernels are a common choice for GP, which requires the kernel to be a function of the distance between two inputs. For example, the Matérn and periodic kernels are stationary kernels, and we can work out their closed-form formulas of LTI-SDE and SSM. We omit the specific formulas here and refer the readers to the appendix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multivariate Time Series Imputation", "sec_num": "3.1."}, {"text": "The motivation of BayOTIDE is based on the fact that the different channels of real-world multivariate time series are always correlated, and there may exist shared patterns across channels. Thus, we propose to decompose the series into a set of functional basis (factors) and channel-wise weights. Inspired by the classic seasonal-trend decomposition (<PERSON> et al., 1990) and TIDER (<PERSON><PERSON><PERSON> et al., 2023) , we assume there are two groups of factors representing different temporal patterns. The first group of factors is supposed to capture the nonlinear and long-term patterns, and the second represents the periodic parts, namely, trends and seasonalities. Thus, we decompose the function X(t) : t → R D as the weighted combination of two groups of functional factors. Specifically, assume there are D r trend factors and D s seasonality factors, then we have the following decomposition:", "cite_spans": [{"start": 352, "end": 376, "text": "(<PERSON> et al., 1990)", "ref_id": "BIBREF10"}, {"start": 387, "end": 405, "text": "(LIU et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Functional Decomposition of Time Series", "sec_num": "4.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "X(t) = UV(t) = [U trend , U season ] v trend (t), v season (t) ,", "eq_num": "(5)"}], "section": "Functional Decomposition of Time Series", "sec_num": "4.1."}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Functional Decomposition of Time Series", "sec_num": "4.1."}, {"text": "U = [U trend ∈ R D×Dr , U season ∈ R D×Ds ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Functional Decomposition of Time Series", "sec_num": "4.1."}, {"text": "are the weights of the combination. The trends factor group v season (t) : t → R Ds , and seasonality factor groups v trend (t) : t → R Dr are the concatenation of independent temporal factors over each dimension:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Functional Decomposition of Time Series", "sec_num": "4.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "v trend (t) = concat[v i trend (t)] i=1...Dr , v season (t) = concat[v j season (t)] j=1...Ds ,", "eq_num": "(6)"}], "section": "Functional Decomposition of Time Series", "sec_num": "4.1."}, {"text": "where v i trend (t), v j season (t) are the 1-d temporal function factor, aimed to model the i-th trend and j-th seasonality latent pattern respectively.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Functional Decomposition of Time Series", "sec_num": "4.1."}, {"text": "For the imputation task, if we can estimate the U and V(t) well, we can impute the missing values of X(t) by UV(t) for any t. As TIDER (LIU et al., 2023) proposed a lowrank decomposition similar to (5), our model can be seen as a generalization of TIDER to the continuous-time and functional field with the Bayesian framework.", "cite_spans": [{"start": 135, "end": 153, "text": "(LIU et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Functional Decomposition of Time Series", "sec_num": "4.1."}, {"text": "We assume X(t) is partially observed with missing values and noise on timestamps {t 1 , . . . t N }. We denote the observed values as Y = {y n } N n=1 , where y n ∈ R D , and its value at d-th channel is denoted as y d n . M ∈ {0, 1} D×N is the mask matrix, where 1 for observed values and 0 for missing values. The noise level is assumed to be the same for all the channels. Thus, we set the Gaussian likelihood for the observed values as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GP Prior and Joint Probability of Our Model", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(Y|U, V(t), τ ) = (d,n)∈Ω N (y d n | u d v(t n ), τ -1 ), (", "eq_num": "7"}], "section": "GP Prior and Joint Probability of Our Model", "sec_num": "4.2."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GP Prior and Joint Probability of Our Model", "sec_num": "4.2."}, {"text": "where τ is the inverse of the noise level. Ω is the collection of observed values' location, namely", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GP Prior and Joint Probability of Our Model", "sec_num": "4.2."}, {"text": "Ω = {(d, n) | M d,n = 1}. u d ∈ R 1×(Dr+Ds) is the d-th row of U, and v(t n ) ∈ R (Dr+Ds)×1 is the concatenation of v trend (t n ) and v season (t n ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GP Prior and Joint Probability of Our Model", "sec_num": "4.2."}, {"text": "As v season (t) and v trend (t) are supposed to capture different temporal patterns, we adopt Gaussian Processes (GPs) with different kernels to model them. Specifically, we use the Matérn kernel to model the trend factors, and the periodic kernel to model the seasonality factors:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GP Prior and Joint Probability of Our Model", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "v i trend (t) ∼ GP(0, κ Mat<PERSON>rn ), v j season (t) ∼ GP(0, κ periodic ).", "eq_num": "(8)"}], "section": "GP Prior and Joint Probability of Our Model", "sec_num": "4.2."}, {"text": "We further assume that Gaussian prior for u d and Gamma prior for τ . Then, the joint probability model is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GP Prior and Joint Probability of Our Model", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(Y, V(t), U, τ ) = Gam(τ | a 0 , b 0 ) D d=1 N (u d | 0, I) • Ds j=1 GP(0, κ periodic ) Dr i=1 GP(0, κ Mat<PERSON>rn ) • p(Y|Θ),", "eq_num": "(9)"}], "section": "GP Prior and Joint Probability of Our Model", "sec_num": "4.2."}, {"text": "where Θ = {U, V(t), τ } denotes all model random variables for compactness. As each GP prior term corresponds to a companion form z(t) (see section(3.2)), we define the concatenation of all factors' companion forms as Z(t) and we have p(V(t)) = p(Z(t)) = P (Z(t 1 ))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GP Prior and Joint Probability of Our Model", "sec_num": "4.2."}, {"text": "N -1 i=1 P (Z(t n+1 )|Z(t n )).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GP Prior and Joint Probability of Our Model", "sec_num": "4.2."}, {"text": "With the joint probability ( 9), we further propose an online inference algorithm to estimate the running posterior of Θ.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "We denote all observations up to time t n as D tn , i.e. D tn = {y 1 , . . . , y n }. When a new observation y n+1 arrives at t n+1 , we aimed to update the posterior distribution p(Θ | D tn ∪ y n+1 ) without reusing the previous observations D tn .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "The general principle for online inference is the incremental version of <PERSON><PERSON>'rule, which is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(Θ | D tn ∪ y n+1 ) ∝ p(y n+1 | Θ, D tn )p(Θ | D tn ).", "eq_num": "(10)"}], "section": "Online Inference", "sec_num": "4.3."}, {"text": "However, the exact posterior is not tractable. Thus, we first apply the mean-field factorization to approximate the posterior. Specifically, we approximate the exact posterior as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(Θ | D tn ) ≈ q(τ | D tn ) D d=1 q(u d | D tn )q(Z(t) | D tn ),", "eq_num": "(11)"}], "section": "Online Inference", "sec_num": "4.3."}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "q(u d | D tn ) = N (m d n , V d n ) and q(τ | D tn ) = Gamma(τ | a n , b n )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "are the approx. distributions for U and τ at time t n respectively. For Z(t), we design the approximated posterior as q(Z(t)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "| D tn ) = n i=1 q(Z(t i )), where q(Z(t i )) are the concatenation of q(z(t i )) = N (µ i , S i ) across all factors. {{m d n , V d n }, {µ i , S i }, a n , b n , }", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "are the variational parameters of the approximated posterior to be estimated.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "With mean-field approximation, (10) is still not feasible. It's because the multiple factors and weights interweave in the likelihood, and R.H.S of ( 10) is unnormalized. To solve it, we propose a novel online approach to update q(Θ | D tn ) with a closed form by adopting conditional Expectation Propagation(CEP) (<PERSON>, 2019) and chain structure of Z(t). Specifically, with current q(Θ | D tn ) and prior p(Θ), we can approximate each likelihood term of the newarriving observation y n+1 as several messages factors:", "cite_spans": [{"start": 314, "end": 332, "text": "(<PERSON>, 2019)", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(y d n+1 | Θ) ≈ Zf d n+1 (Z(t n+1 ))f d n+1 (u d )f d n+1 (τ ), (", "eq_num": "12"}], "section": "Online Inference", "sec_num": "4.3."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "where Z is the normalization constant,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "f d n+1 (u d ) = N (u d | md n+1 , Vd n+1 ) and f d n+1 (τ ) = Gamma(τ | ân+1 , bn+1 ), f d n+1 (Z(t n+1 )) = concat[N (μ i , Ŝi )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "] are the approximated message factors of u d , τ , and Z(t n+1 ) respectively.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "Then, we merge all the message factors of U and τ and follow the variational form of (10), and will obtain the update equations:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "q(τ |D tn+1 ) = q(τ |D tn ) D d=1 f d n+1 (τ ),", "eq_num": "(13)"}], "section": "Online Inference", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "q(u d |D tn+1 ) = q(u d |D tn )f d n+1 (u d ). (", "eq_num": "14"}], "section": "Online Inference", "sec_num": "4.3."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "The message approximation ( 12) and the message merging procedure (13)( 14) are based on the conditional momentmatching technique, which can be done in parallel for all channels with closed-form update. We omit the derivation and exact formulas and refer the readers to the appendix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "Then, we present the online update of Z(t). With the chain structure of q(Z(t) | D tn ) and p(Z(t)), we found the update can be conducted sequentially. Specifically:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "q(Z(t n+1 )) = q(Z(t n ))p(Z(t n+1 )|Z(t n )) d=1 f d n+1 (Z(t n+1 )),", "eq_num": "(15)"}], "section": "Online Inference", "sec_num": "4.3."}, {"text": "where p(Z(t n+1 )|Z(t n )) is the concatenation of all factors' transition given in (4). If we treat d f d n+1 (Z(t n+1 )) as the observation of the state space, (15) is the <PERSON><PERSON> filter model (<PERSON>, 1960) . Thus, we can obtain the closedform update of q(Z(t)| | D tn ), which is the running posterior q(Z n |y 1:n ). After going through all observations, we run Rauch-Tung-Striebel (RTS) smoother (<PERSON><PERSON> et al., 1965) to efficiently compute the full posterior of each state q(Z n |y 1:N ) from backward.", "cite_spans": [{"start": 194, "end": 208, "text": "(<PERSON><PERSON>, 1960)", "ref_id": "BIBREF20"}, {"start": 401, "end": 421, "text": "(<PERSON><PERSON> et al., 1965)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "The online algorithm is summarized in Algorithm table 1: we go through all the timestamps, approximate the message factors with moment-matching, and run <PERSON><PERSON> filter and message merging and update sequentially. For each timestamp, we can run moment-matching and posterior update steps iteratively several times with damping trick (<PERSON>, 2001a) for better approximation. The algorithm is very efficient as the message approximation (12) can be parallel for different channels, and all the updates are closed-form. The", "cite_spans": [{"start": 331, "end": 345, "text": "(<PERSON><PERSON>, 2001a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Online Inference", "sec_num": "4.3."}, {"text": "Input: observation Y = {y n } N n=1 over {t n } N n=1 , D s , D r , the kernel hyperparameters. Initialize q(τ ), q(W), {q(Z(t n ))} N n=1 . for t = 1 to N do Approximate messages by ( 12) for all observed channels in parallel. Update posterior of τ and U by ( 13) and ( 14) for all observed channels in parallel. Update posteriors of Z(t) using <PERSON><PERSON> filter by (15). end for Run RTS smoother to obtain the full posterior of Z(t).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 BayOTIDE", "sec_num": null}, {"text": "Return: q(τ ), q(W), {q(Z(t n ))} N n=1 algorithm is with time cost O(N (D s + D r )) and space cost O( Dr+Ds k=1 N (m k + m 2 k ) + D(D s + D r ))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 BayOTIDE", "sec_num": null}, {"text": "where N is the number of timestamps, D is the number of channel of original time series, D r , D s are number of trends and seasonality factors respectively, m k is the order of the companion form of k-th factor's GP prior determined by the kernel types.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 BayOTIDE", "sec_num": null}, {"text": "With the current posterior {q(z(t 1 )) . . . q(z(t N ))} over the observed timestamps, the functional and chain property of GP priors allow us to infer the prediction distribution, namely do probabilistic interpolation at arbitrary time stamps. Specifically, for a never-seen timestamp t ⋆ ∈ (t 1 , t N ), we can identify the closest neighbor of t ⋆ observed in training, i.e, t k < t ⋆ < t k+1 , where t k , t k+1 ∈ {t 1 . . . t N }. Then the predictive distribution at t ⋆ is given by q(z(t ⋆ )) = N (z ⋆ | m ⋆ , V ⋆ ). {m ⋆ , V ⋆ } are defined as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Probabilistic Imputation at Arbitrary Timestamp", "sec_num": "4.4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "V ⋆ = (Q -1 1 + A ⊤ 2 Q -1 1 A 2 ) -1 , m ⋆ = V ⋆ (Q -1 1 A 1 m k + A ⊤ 2 Q -1 2 m k+1 ),", "eq_num": "(16)"}], "section": "Probabilistic Imputation at Arbitrary Timestamp", "sec_num": "4.4."}, {"text": "where m k , m k+1 are the predictive mean of q(z(t k )), q(z(t k+1 )), {A 1 , A 2 , Q 1 , Q 2 } are transition matrices and covariance matrices based on the forwardbackward transitions p(z(t ⋆ )|z(t k )), p(z(t k+1 )|z(t ⋆ )) respectively. Eq.( 16) offers continuous modeling of the time series. We give a detailed derivation in the appendix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Probabilistic Imputation at Arbitrary Timestamp", "sec_num": "4.4."}, {"text": "We first evaluate BayOTIDE on a synthetic task. We first set four temporal functions and a weight matrix, defined as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Synthetic data", "sec_num": "5.1."}, {"text": "U =     1 1 -2 -2 0.4 1 2 -1 -0.3 2 1 -1 -1 1 1 0.5     , V(t) =     10t, sin(20πt), cos(40πt), sin(60πt)     .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Synthetic data", "sec_num": "5.1."}, {"text": "Then, the four-channel time series is defined as X(t) = UV(t), and each channel is a mixture of multiscale trend and seasonality factors. We collected 2000 data points over the 500 irregularly sampled timestamps from [0, 1]. We randomly set only 20% of the data as observed values, and the rest as missing for evaluation. We further add Gaussian noise with a standard deviation 0.1 to the observed data. We use the Matérn kernel with ν = 3/2 as the trend kernel and the periodic kernel with period 20π as the seasonality kernel. We set D r = 1, D s = 3. We highlight that evaluation could be taken on the timestamps that never appear in the training set, known as the all-channel-missing imputation, but we can apply ( 16) to handle such hard cases easily.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Synthetic data", "sec_num": "5.1."}, {"text": "The imputation results are shown in Figure 1a . We can see that BayOTIDE recovers the series well, and the estimated uncertainty is reasonable. We also show the channel-wise estimated factors in Figure 1b ,1c,1d, and 1e. We can see that the estimated factors are close to the real factors, which indicates that BayOTIDE can capture the underlying multiscale patterns of the data.", "cite_spans": [], "ref_spans": [{"start": 43, "end": 45, "text": "1a", "ref_id": null}, {"start": 202, "end": 204, "text": "1b", "ref_id": null}], "eq_spans": [], "section": "Synthetic data", "sec_num": "5.1."}, {"text": "Datasets We evaluate BayOTIDE on three real-world datasets, Traffic-Guangzhou(<PERSON> et al.): traffic speed records in Guangzhou with 214 channels and 500 timestamps. Solar-Power(https://www.nrel.gov/ grid/solar-power-data.html) : 137 channels and 52560 timestamps, which records the solar power generation of 137 PV plants. Uber-Move(https:// movement.uber.com/): 7489 channels and 744 timestamps, recording the average movement of Uber cars along with the road segments in London, Jan 2020. For each dataset, we randomly sample {70%, 50%} of the available data points as observations for model training, and the rest for evaluation. The data process and split strategy are aligned with TIDER (<PERSON><PERSON><PERSON> et al., 2023) .", "cite_spans": [{"start": 692, "end": 710, "text": "(LIU et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Real-world Applications", "sec_num": "5.2."}, {"text": "To the best of our knowledge, there are no online algorithms for multivariate time series imputation. Thus, we set several popular deterministic and probabilistic offline imputation approaches as baselines. The deterministic group includes: (1) SimpleMean (Acuna & Rodriguez, 2004) , impute with column-wise mean values.", "cite_spans": [{"start": 256, "end": 281, "text": "(Acuna & Rodriguez, 2004)", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "Baselines and Settings", "sec_num": null}, {"text": "(2) BRITS (<PERSON> et al., 2018) , the RNN-based model for imputation with time decay (3) NAOMI (<PERSON> et al., 2019) , a Bidirectional RNN model build with adversarial training (4) SAITS (<PERSON> et al., 2023) , a transformer-based model which adopts the self-attention mechanism. ( 5) TIDER (<PERSON><PERSON><PERSON> et al., 2023) . State of art deterministic imputation model based on disentangled temporal representations.", "cite_spans": [{"start": 10, "end": 28, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF7"}, {"start": 92, "end": 110, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF25"}, {"start": 181, "end": 198, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF11"}, {"start": 281, "end": 299, "text": "(LIU et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Baselines and Settings", "sec_num": null}, {"text": "The probabilistic group includes: (1) Multi-Task GP (<PERSON><PERSON> et al., 2008) , the classical multi-output Gaussian process model (2) GP-VAE (<PERSON><PERSON> et al., 2020) , a deep generative model which combines Gaussian Processes(GP) and variational autoencoder(VAE) for imputation (3) CSDI (<PERSON><PERSON><PERSON> et al., 2021) Famous probabilistic approach which apply conditional diffusion model to capture the temporal dependency. (4)CSBI Advanced diffusion method that models the imputation task as a conditional Schrödinger Bridge(SB) (<PERSON> et al., 2023) . We also set BayOTIDE-fix-wight by fixing all weight values as one and BayOTIDE-trend-only, and only using trend factor, respectively for BayOTIDE .", "cite_spans": [{"start": 52, "end": 74, "text": "(<PERSON><PERSON> et al., 2008)", "ref_id": "BIBREF5"}, {"start": 138, "end": 160, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF16"}, {"start": 282, "end": 304, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF38"}, {"start": 517, "end": 536, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Baselines and Settings", "sec_num": null}, {"text": "For most baselines, we use the released implementation provided by the authors. We partially use the results of deterministic methods reported in TIDER (LIU et al., 2023) , as the setting is aligned. To avoid the out-of-memory problem of diffusion-based and deep-based baselines, we follow the preprocess of original papers, which split the whole sequence into small patches and subsample the channels for those methods.", "cite_spans": [{"start": 152, "end": 170, "text": "(LIU et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Baselines and Settings", "sec_num": null}, {"text": "For BayOTIDE , we implemented it by <PERSON><PERSON><PERSON><PERSON> and finetuned the D s , D r , and the kernel hyperparameters to obtain optimal results. Detailed information on hyperparameter settings is provided at Table 5 in the appendix. For the metrics, we use the mean absolute error (MAE) and the root mean squared error (RMSE) as the deterministic evaluation metrics for all methods. We adopt the continuous ranked probability score (CRPS) and the negative log-likelihood (NLLK), for BayOTIDE and all probabilistic baselines. We use 50 samples from the posterior to compute CRPS and NLLK. We repeat all the experiments 5 times and report the average results. For the CEP step at each timestamp in BayOTIDE , we use the damping trick (<PERSON><PERSON>, 2001a) in several inner epochs to avoid numerical instability. For the online imputation, we use the online update equations to obtain the imputation results and run RTS smoother at every evaluation timestamp.", "cite_spans": [{"start": 719, "end": 733, "text": "(<PERSON><PERSON>, 2001a)", "ref_id": null}], "ref_spans": [{"start": 201, "end": 202, "text": "5", "ref_id": "TABREF7"}], "eq_spans": [], "section": "Baselines and Settings", "sec_num": null}, {"text": "Deterministic and Probabilistic performance Table 2 and table 3 show the RMSE, MAE, and CRPS scores of imputation on three datasets with observed ratio = 50% and = 70% respectively. We can see that BayOTIDE , an online method that only processes data once, beats the offline baselines and performs best in most cases. TIDER is the sub-optimal method for most cases. For probabilistic approaches, diffusion-based CSDI and CSBI obtain fair per- ", "cite_spans": [], "ref_spans": [{"start": 50, "end": 51, "text": "2", "ref_id": "TABREF1"}, {"start": 62, "end": 63, "text": "3", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Baselines and Settings", "sec_num": null}, {"text": "Traffic formance, but are costly in memory and need to split the long sequence into small patches for training. BayOTIDEfix-wight is with poor performance, which indicates that the weighted bases mechanism is crucial. BayOTIDE-trendonly is slightly worse than BayOTIDE , showing the modeling of periodic factor is necessary. The results of NLLK score over three datasets can be found at table 6 in the appendix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Observed-ratio=50%", "sec_num": null}, {"text": "Online Imputation We demonstrate the online imputation performance of BayOTIDE on three datasets with observed ratio 50%. Whenever a group of observations at new timestamps have been sequentially fed into the model, we evaluate the test RMSE of the model with the updated weights and temporal factor. We compare the performance of Bay-OTIDE with the BayOTIDE-fix-wight. The online result on Traffic-Guangzhou is shown in Figure 2a . We can see that BayOTIDE shows the reasonable results that the evaluation error drops gradually when more timestamps are processed, meaning the model can continuously learn and improve.", "cite_spans": [], "ref_spans": [{"start": 428, "end": 430, "text": "2a", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Observed-ratio=50%", "sec_num": null}, {"text": "The performance of BayOTIDE-fix-wight is very poor. It indicates the trivial usage of the GP-SS model for multivariate time series imputation may not be feasible. The online results for the other two datasets can be found in the appendix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Observed-ratio=50%", "sec_num": null}, {"text": "We evaluate the scalability of BayOTIDE over data size and factor numbers. We set three different factor numbers: D r + D s = {5, 20, 50}. As for the scalability over series length N , We make synthetic data with channel number D = 1000, increase the N from 1000 to 4000, and measure the training time. The result is shown in Figure 2b . Similarly, we fix the series length N = 1000, increase the series channel D from 1000 to 4000, and then measure the training time. The result is shown in Figure 2c . As we can see, the running time of BayOTIDE grows linearly in both channel and length size, and the factor number determines the slope. Therefore, BayOTIDE enjoys the linear scalability to the data size, which is suitable for large-scale applications.", "cite_spans": [], "ref_spans": [{"start": 333, "end": 335, "text": "2b", "ref_id": "FIGREF2"}, {"start": 499, "end": 501, "text": "2c", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Scalability and Sensitivity", "sec_num": null}, {"text": "We further examine the sensitivity of BayOTIDE with different hyperparameters. We build the model with Matérn kernel with different smoothness ν = {1/2, 3/2} on the Traffic-Guangzhou with observed ratio 70%. We vary three crucial hyperparameters: the number of factors D r +D s , the kernel length scale, and the kernel variance, and check how imputation performance (CRPS) changes. The results are shown in Figure 2d , Figure 2e , and Figure 2f , respectively. We found more factors that will result in better performance in general for both kernel types. We can find that the performance is relatively stable over different hyperparameters for Matérn kernel with smoothness ν = {1/2}. For the Matérn kernel with smoothness ν = {3/2}, the performance is more sensitive, especially to the kernel lengthscale. The smaller kernel lengthscale will result in better performance. The kernel variance is also sensitive, but the performance is relatively stable when the variance is large enough. More results on the sensitivity of BayOTIDE over different combinations of latent space dimensions (D r , D s ) are shown in Table 7 in the appendix.", "cite_spans": [], "ref_spans": [{"start": 415, "end": 417, "text": "2d", "ref_id": "FIGREF2"}, {"start": 427, "end": 429, "text": "2e", "ref_id": "FIGREF2"}, {"start": 443, "end": 445, "text": "2f", "ref_id": "FIGREF2"}, {"start": 1121, "end": 1122, "text": "7", "ref_id": null}], "eq_spans": [], "section": "Scalability and Sensitivity", "sec_num": null}, {"text": "Imputation on Irregular and All-Channel-Missing Timestamps We further show BayOTIDE can work well with irregulate timestamps with functional and continuous design, and therefore can handle the challenging case of all-channel-missing case. We select the observations at {50%, 70%} randomly sampled irregulate timestamps for We highlight that most existing advanced imputation methods cannot handle this hard case well. It's because they are based on the regular-time-interval setting, which assumes there is at least one observation at every timestamp during the training. However, BayOTIDE can apply the Eq. ( 16) and give probabilistic imputation at the arbitrary continuous timestamp. Thus, we only list the results of BayOTIDE on three datasets in Table 4 . We can see the performance is closed or even better than the standard imputation setting shown in Table 2 . ", "cite_spans": [], "ref_spans": [{"start": 757, "end": 758, "text": "4", "ref_id": "TABREF4"}, {"start": 865, "end": 866, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Scalability and Sensitivity", "sec_num": null}, {"text": "We proposed BayOTIDE , a novel Bayesian model for online multivariate time series imputations. We decompose the multivariate time series into a temporal function basis and channel-wise weights, and apply a group of GPs to fit the temporal function basis. An efficient online inference algorithm is developed based on the SDE representation of GPs and moment-matching techniques. Results on both synthetic and real-world datasets show that BayOTIDE outperforms the state-of-the-art methods in terms of both imputation accuracy and uncertainty quantification. We also evaluated the scalability and robustness of BayOTIDE on large-scale real-world datasets with different settings of hyperparameters and missing patterns. . In the future, we plan to extend BayOTIDE to handle more complex patterns and more challenging tasks, such as long-term forecasting with non-stationary patterns.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "This paper is dedicated to innovating time series imputation techniques to push the boundaries of time series analysis further. While our primary objective is to enhance imputation accuracy and computational efficiency, we are also mindful of the broader ethical considerations that accompany technological progress in this area. While immediate societal impacts may not be apparent, we acknowledge the importance of ongoing vigilance regarding the ethical use of these advancements. It is essential to continuously assess and address potential implications to ensure responsible development and application in various scenarios. We take the Matérn kernel as an example to show how to connect GP with LTI-SDE. The Matérn kernel is defined as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "κ ν (t, t ′ ) = a ( √ 2ν ρ ∆) ν Γ(ν)2 ν-1 K ν ( √ 2ν ρ ∆)", "eq_num": "(17)"}], "section": "Impact Statement", "sec_num": null}, {"text": "where ∆ = |t -t ′ |, Γ(•) is the Gamma function, a > 0 and ρ > 0 are the amplitude and length-scale parameters respectively, K ν is the modified <PERSON><PERSON> function of the second kind, and ν > 0 controls the smoothness of sample paths from the GP prior f (t) ∼ GP(0, κ ν (t, t ′ )).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "For a stationary Matérn kernel κ ν (t, t ′ ) = κ ν (t -t ′ ), the energy spectrum density of f (t) can be obtained via the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> theorem by taking the Fourier transform of κ ν (∆):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "S(ω) = σ 2 (α 2 + ω 2 ) m+1", "eq_num": "(18)"}], "section": "Impact Statement", "sec_num": null}, {"text": "where ω is the frequency, α = √ 2ν", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "ρ , and we take ν = m + 1 2 for m ∈ {0, 1, 2, ...}.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Expanding the polynomial gives:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(α + iω) m+1 = m k=0 c k (iω) k + (iω) m+1", "eq_num": "(19)"}], "section": "Impact Statement", "sec_num": null}, {"text": "where c k are coefficients. This allows constructing an equivalent frequency domain system:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "m k=1 c k (iω) k f (ω) + (iω) m+1 f (ω) = β(ω)", "eq_num": "(20)"}], "section": "Impact Statement", "sec_num": null}, {"text": "where f (ω) and β(ω) are Fourier transforms of f (t) and white noise w(t) with spectral density q s respectively.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Taking the inverse Fourier transform yields the time domain SDE:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "m k=1 c k d k f dt k + d m+1 f dt m+1 = w(t)", "eq_num": "(21)"}], "section": "Impact Statement", "sec_num": null}, {"text": "We can further construct a new state z = (f, f (1) , . . . , f (m) ) ⊤ (where each f (k) ∆ = d k f /dt k ) and convert (21) into a linear time-invariant (LTI) SDE,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dz(t) dt = Fz(t) + Lw(t)", "eq_num": "(22)"}], "section": "Impact Statement", "sec_num": null}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "F =      0 1 . . . . . . 0 1 -c 0 . . . -c m-1 -c m      , L =      0 . . . 0 1      .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "The LTI-SDE is particularly useful in that its finite set of states follows a Gauss-Markov chain, namely the state-space prior. Specifically, given arbitrary t 1 < . . . < t L , we have", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "p(z(t 1 ), . . . , z(t L )) = p(z(t 1 )) L-1 k=1 p(z(t k+1 )|z(t k )) where p(z(t 1 )) = N (z(t 1 )|0, P ∞ ), p(z(t n+1 )|z(t n )) = N (z(t n+1 )|A n z(t n ), Q n ) (23) where A n = exp(F∆ n ), Q n = tn+1 tn A n LL ⊤ A ⊤ n q s dt, ∆ n = t n+1 -t n", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": ", and P ∞ is the steady-state covariance matrix of the LTI-SDE 3, which can be obtained by solving the Lyapunov equation FP ∞ + P ∞ F ⊤ + LL ⊤ q s = 0 (<PERSON> & <PERSON>, 1995) , as we claimed in the main paper.", "cite_spans": [{"start": 151, "end": 177, "text": "(Lancaster & Rodman, 1995)", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Note that for other types of stationary kernel functions, such as the periodic kernels, we can approximate the inverse spectral density 1/S(ω) with a polynomial of ω 2 with negative roots (<PERSON><PERSON> & <PERSON>, 2014) , and follow the same way to construct an LTI-SDE and state-space prior.", "cite_spans": [{"start": 188, "end": 210, "text": "(Solin & Särkkä, 2014)", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": ". For <PERSON><PERSON><PERSON> kernel with m = 0, indicating the smoothness is ν = 0 + 1 2 , it becomes the exponential covariance function:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "κ exp (τ ) = σ 2 exp - τ ℓ", "eq_num": "(24)"}], "section": "Impact Statement", "sec_num": null}, {"text": "Then the parameters of the LTI-SDE and state space prior are: {m = 0, F = -1/l, L = 1, q s = 2σ 2 /l, P ∞ = σ 2 } For Matérn kernel with m = 1, indicating the smoothness is ν = 1 + 1 2 = 3/2, the kernel becomes the Matérn 3/2 covariance function:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "κ Mat. (τ ) = σ 2 1 + √ 3τ ℓ exp - √ 3τ ℓ (25)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "and the parameters of the LTI-SDE and state space prior", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "are: m = 1, F = 0 1 -λ 2 -2λ ,L = 0 1 , P ∞ = σ 2 0 0 λ 2 σ 2 , q s = 4λ 3 σ 2 , where λ = √ 3/ℓ.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "For the periodic kernel:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "κ periodic (t, t ′ ) = σ 2 exp - 2 sin 2 (π∆/p) l 2", "eq_num": "(26)"}], "section": "Impact Statement", "sec_num": null}, {"text": "with preset periodicity p, (<PERSON>, 2014) construct corresponding SDE by a sum of n two-dimensional SDE models(m=1) of the following parameters:", "cite_spans": [{"start": 27, "end": 49, "text": "(Solin & Särkkä, 2014)", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "F j = 0 -2π p j 2π p j 0 , L j = 1 0 0 1", "eq_num": "(27)"}], "section": "Impact Statement", "sec_num": null}, {"text": "P ∞,j = q 2 j I 2 , where q 2 j = 2I j ℓ -2 / exp ℓ -2 , for j = 1, 2, . . . , n and q 2 0 = I 0 ℓ -2 / exp ℓ -2 (<PERSON> et al., 2016) .2. Derivative of online update equations by conditional moment matching .", "cite_spans": [{"start": 113, "end": 133, "text": "(<PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "The Expectation Propagation (EP) (<PERSON><PERSON>, 2001b) and Conditional EP (CEP) (<PERSON> & <PERSON>, 2019) frameworks approximate complex probabilistic models with distributions in the exponential family.", "cite_spans": [{"start": 33, "end": 47, "text": "(<PERSON><PERSON>, 2001b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "Consider a model with latent variables θ and observed data D = {y 1 , . . . , y N }. The joint probability is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(θ, D) = p(θ) N n=1 p(y n |θ)", "eq_num": "(28)"}], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "The posterior p(θ|D) is usually intractable. EP approximates each term with an exponential family distribution:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(y n |θ) ≈ c n f n (θ) (29) p(θ) ≈ c 0 f 0 (θ)", "eq_num": "(30)"}], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "where f n (θ) ∝ exp(λ ⊤ n ϕ(θ)) are in the exponential family with natural parameters λ n and sufficient statistics ϕ(θ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "The joint probability is approximated by: probability (41),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(x 1 , . . . , x m , x * , x m+1 , . . . , x M , D) = p(x 1 ) m-1 j=1 p(x j+1 |x j ) • p(x * |x m )p(x m+1 |x * ) • M j=m+1 p(x j+1 |x j ) • p(D|x 1 , . . . , x M ).", "eq_num": "(42)"}], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "Now, we marginalize out x 1:M \\{m,m+1} = {x 1 , . . . , x m-1 , x m+2 , . . . , x M }. Note that since x * does not appear in the likelihood, we can take it out from the integral, ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "Suppose we are able to obtain p(x m , x m+1 |D) ≈ q(x m , x m+1 ). We now need to obtain the posterior of x * .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "In the LTI SDE model, we know that the state transition is a Gaussian jump. Let us denote ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(x * |x m ) = N (x * |A 1 x m , Q 1 ) p(x m+1 |x * ) = N (x m+1 |A 2 x * , Q 2 ).", "eq_num": "(45"}], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(Σ * ) -1 = Q -1 1 + A ⊤ 2 Q -1 2 A 2 , (Σ * ) -1 µ * = Q -1 1 A 1 x m + A ⊤ 2 Q -1 2 x m+1 .", "eq_num": "(47)"}], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "Those are the formulas for probabilistic imputation as arbitrary time stamps.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": ".", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BRIEF INTRODUCTION OF EP AND CEP", "sec_num": "2.1."}, {"text": "The NLLK scores of probabilistic imputation approaches across all datasets with different observed ratios are shown in table 6 . We can see that BayOTIDE , an online method that only processes data once, beats the offline baselines and achieves the best performance in all cases.", "cite_spans": [], "ref_spans": [{"start": 125, "end": 126, "text": "6", "ref_id": "TABREF9"}], "eq_spans": [], "section": "More experimental results", "sec_num": "4."}, {"text": "To further show the sensitivity of BayOTIDE over the different latent space dimensions, we evaluate our method on the Guangzhou-traffic dataset (observed ratio = 70%) with different settings of latent space dimension and evaluate the test CRPC score. The results are shown in table 7 . We found that increasing the latent space dimension, especially the trend factor dimension (D r ), can improve the model performance. However, the improvement is not linear, and the model may suffer from overfitting when the latent space dimension is too large. A seasonal factor dimension (D s ) that is too high may also lead to overfitting and degrading the model performance.", "cite_spans": [], "ref_spans": [{"start": 282, "end": 283, "text": "7", "ref_id": null}], "eq_spans": [], "section": "More experimental results", "sec_num": "4."}, {"text": "For the online imputation, the results on the Solar-Power and Uber-Move is shown in Figure 3a and Figure 3b . ", "cite_spans": [], "ref_spans": [{"start": 91, "end": 93, "text": "3a", "ref_id": "FIGREF6"}, {"start": 105, "end": 107, "text": "3b", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "More experimental results", "sec_num": "4."}], "back_matter": [{"text": "giving a tractable approximate posterior q(θ) ≈ p(θ|D).EP optimizes the approximations f n by repeatedly:1) Computing the calibrated distribution q \\n excluding f n .2) Constructing the tilted distribution p incorporating the true likelihood.3) Projecting p back to the exponential family by moment matching.4) Updating f n ≈ q * q \\n where q * is the projection. The intractable moment matching in Step 3 is key. CEP exploits factorized f n = m f nm (θ m ) with disjoint θ m . It uses nested expectations:The inner expectation is tractable. For the outer expectation, CEP approximates the marginal tilted distribution with the current posterior:If still intractable, the delta method is used to approximate the expectation with a Taylor expansion.Once the conditional moment g(θ \\m ) is obtained, CEP substitutes the expectation E q(θ \\m ) [θ \\m ] to compute the matched moment for constructing q * .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "We then applied the EP and CEP to approximate the running posterior p(Θ | D tn ∪ y n+1 ). With the incremental version of <PERSON><PERSON>'rule (10), the key is to work out the close-form factors in the likelihood approximation (12). In other words, we adopt conditional moment match techniques to handle:Then we follow the standard CEP procedure to compute the conditional moment of {Z(t n+1 ), u d , τ } and updateSpecifically, for f d n+1 (τ ) = Gamma(τ | ân+1 , bn+1 ) we have:), we have:ForAll the expectation is taken over the current approximated posterior q(Θ | D tn ).With these message factors from the new-arriving likelihood, the online update is easy. We follow the ( 13), ( 14) and ( 15) to merge the factors and obtain the closed-form online update for the global posterior..", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Online inference Update", "sec_num": null}, {"text": "Consider a general state space model, which includes a sequence of states x 1 , . . . , x M and the observed data D.The states are at time t 1 , . . . , t M respectively. The key of the state space model is that the prior of the states is a Markov chain. The joint probability has the following form,Note that here we do not assume the data likelihood is factorized over each state, like those typically used in Kalman filtering. In our point process model, the likelihood often couples multiple states together.Suppose we have run some posterior inference to obtain the posterior of these states q(x 1 , . ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Derivation of the PROBABILISTIC IMPUTATION AT ARBITRARY TIME STAMPS", "sec_num": "3."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "A novel hybrid model for forecasting crude oil price based on time series decomposition", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Applied energy", "volume": "267", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, H. A novel hybrid model for forecasting crude oil price based on time series decomposition. Applied energy, 267:115035, 2020.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "The treatment of missing values and its effect on classifier accuracy", "authors": [{"first": "E", "middle": [], "last": "Acuna", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2004, "venue": "Classification, Clustering, and Data Mining Applications: Proceedings of the Meeting of the International Federation of Classification Societies (IFCS)", "volume": "", "issue": "", "pages": "639--647", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. The treatment of missing val- ues and its effect on classifier accuracy. In Classification, Clustering, and Data Mining Applications: Proceedings of the Meeting of the International Federation of Classification Societies (IFCS), Illinois Institute of Technology, Chicago, 15-18 July 2004, pp. 639-647. Springer, 2004.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Diffusion-based time series imputation and forecasting with structured state space models", "authors": [{"first": "J", "middle": ["M L"], "last": "Alcaraz", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2208.09399"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, J. M. L<PERSON> and <PERSON><PERSON><PERSON>, N. Diffusion-based time series imputation and forecasting with structured state space models. arXiv preprint arXiv:2208.09399, 2022.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Mstl: A seasonal-trend decomposition algorithm for time series with multiple seasonal patterns", "authors": [{"first": "K", "middle": [], "last": "Bandara", "suffix": ""}, {"first": "R", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Bergmeir", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2107.13462"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: A seasonal-trend decomposition algorithm for time se- ries with multiple seasonal patterns. arXiv preprint arXiv:2107.13462, 2021.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "State space approximation of gaussian processes for time series forecasting", "authors": [{"first": "A", "middle": [], "last": "Benavoli", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Advanced Analytics and Learning on Temporal Data: 6th ECML PKDD Workshop, AALTD 2021", "volume": "6", "issue": "", "pages": "21--35", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, G. State space approximation of gaussian processes for time series forecasting. In Advanced Analytics and Learning on Temporal Data: 6th ECML PKDD Workshop, AALTD 2021, Bilbao, Spain, September 13, 2021, Revised Selected Papers 6, pp. 21-35. Springer, 2021.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Multi-task Gaussian process prediction", "authors": [{"first": "E", "middle": [], "last": "Bonilla", "suffix": ""}, {"first": "K", "middle": ["M"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2008, "venue": "Advances in Neural Information Processing Systems 20", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, C. Multi-task Gaussian process prediction. In Advances in Neural Information Processing Systems 20. 2008.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Training energy-based models for time-series imputation", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Stroobandt", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "The Journal of Machine Learning Research", "volume": "14", "issue": "1", "pages": "2771--2797", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Train- ing energy-based models for time-series imputation. The Journal of Machine Learning Research, 14(1):2771- 2797, 2013.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Brits: Bidirectional recurrent imputation for time series", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}], "year": 2018, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>: Bidirectional recurrent imputation for time series. Advances in neural information processing systems, 31, 2018.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Urban traffic speed dataset of guangzhou, china", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "He", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"DOI": ["10.5281/zenodo"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Urban traffic speed dataset of guangzhou, china, march 2018. URL https://doi. org/10.5281/zenodo, 1205229.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Provably convergent schr\\\" odinger bridge with applications to probabilistic time series imputation", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Li", "suffix": ""}, {"first": "N", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Nevmyvaka", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.07247"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Provably convergent schr\\\" odinger bridge with ap- plications to probabilistic time series imputation. arXiv preprint arXiv:2305.07247, 2023.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Stl: A seasonal-trend decomposition", "authors": [{"first": "R", "middle": ["B"], "last": "Cleveland", "suffix": ""}, {"first": "W", "middle": ["S"], "last": "Cleveland", "suffix": ""}, {"first": "J", "middle": ["E"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>ning", "suffix": ""}], "year": 1990, "venue": "J. Off. Stat", "volume": "6", "issue": "1", "pages": "3--73", "other_ids": {}, "num": null, "urls": [], "raw_text": "Cleveland, R. B., Cleveland, W. S., <PERSON>c<PERSON>, J. E., and Ter- penning, I. Stl: A seasonal-trend decomposition. J. Off. Stat, 6(1):3-73, 1990.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Saits: Self-attentionbased imputation for time series", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Expert Systems with Applications", "volume": "219", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Self-attention- based imputation for time series. Expert Systems with Applications, 219:119619, 2023.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Time series analysis by state space methods", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "OUP Oxford", "volume": "38", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, S. J. Time series analysis by state space methods, volume 38. OUP Oxford, 2012.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Time series data imputation: A survey on deep learning approaches", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2011.11347"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> series data imputation: A survey on deep learning approaches. arXiv preprint arXiv:2011.11347, 2020.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Bayesian continuous-time tucker decomposition", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "6235--6245", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>an continuous-time tucker decomposition. In International Conference on Machine Learning, pp. 6235-6245. PMLR, 2022.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Streaming factor trajectory learning for temporal tensor decomposition", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Li", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Streaming factor trajectory learning for temporal tensor decomposition. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Gp-vae: Deep probabilistic time series imputation", "authors": [{"first": "V", "middle": [], "last": "Fort<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Mandt", "suffix": ""}], "year": 2020, "venue": "International conference on artificial intelligence and statistics", "volume": "", "issue": "", "pages": "1651--1661", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Gp-vae: Deep probabilistic time series imputation. In International conference on artificial intelligence and statistics, pp. 1651-1661. PMLR, 2020.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "<PERSON><PERSON> filtering and smoothing solutions to temporal gaussian process regression models", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Särkkä", "suffix": ""}], "year": 2010, "venue": "IEEE international workshop on machine learning for signal processing", "volume": "", "issue": "", "pages": "379--384", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> filtering and smoothing solutions to temporal gaussian process re- gression models. In 2010 IEEE international workshop on machine learning for signal processing, pp. 379-384. IEEE, 2010.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Denoising diffusion probabilistic models", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "6840--6851", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON> Denoising diffusion probabilistic models. Advances in neural information processing systems, 33:6840-6851, 2020.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "A survey on graph neural networks for time series: Forecasting, classification, imputation, and anomaly detection", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": ["Y"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Zambon", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": ["I"], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "King", "suffix": ""}, {"first": "S", "middle": [], "last": "Pan", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.03759"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. A survey on graph neural networks for time series: Forecasting, classifica- tion, imputation, and anomaly detection. arXiv preprint arXiv:2307.03759, 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "A new approach to linear filtering and prediction problems", "authors": [{"first": "R", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1960, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> A new approach to linear filtering and pre- diction problems. 1960.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Algebraic riccati equations", "authors": [{"first": "P", "middle": [], "last": "Lancaster", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1995, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> riccati equations. Clarendon press, 1995.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Trend modeling for traffic time series analysis: An integrated study", "authors": [{"first": "L", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": [], "last": "Su", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}], "year": 2015, "venue": "IEEE Transactions on Intelligent Transportation Systems", "volume": "16", "issue": "6", "pages": "3430--3439", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> modeling for traffic time series analysis: An integrated study. IEEE Transactions on Intelligent Transportation Systems, 16 (6):3430-3439, 2015.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "A self-adaptive decomposed interpretable framework for electric load forecasting under extreme events", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Ma", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Sun", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICASSP 2023-2023 IEEE International Conference on Acoustics, Speech and Signal Processing", "volume": "", "issue": "", "pages": "1--5", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: A self-adaptive decomposed inter- pretable framework for electric load forecasting under ex- treme events. In ICASSP 2023-2023 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP), pp. 1-5. IEEE, 2023.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Multivariate time-series imputation with disentangled temporal representations", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Li", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Jiang", "suffix": ""}], "year": 2023, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "L<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and JIA<PERSON>, Y. <PERSON>- tivariate time-series imputation with disentangled tem- poral representations. In The Eleventh International Conference on Learning Representations, 2023.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Non-autoregressive multiresolution sequence imputation", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "32", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Non-autoregressive multiresolution sequence imputation. Advances in neural information processing systems, 32, 2019.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Expectation propagation for approximate bayesian inference", "authors": [{"first": "T", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2001, "venue": "Proceedings of the Seventeenth conference on Uncertainty in artificial intelligence", "volume": "", "issue": "", "pages": "362--369", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> Expectation propagation for approximate bayesian inference. In Proceedings of the Seventeenth conference on Uncertainty in artificial intelligence, pp. 362-369, 2001a.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "A family of algorithms for approximate Bayesian inference", "authors": [{"first": "T", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2001, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, T. P. A family of algorithms for approximate Bayesian inference. PhD thesis, Massachusetts Institute of Technology, 2001b.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Uncertainty-aware variational-recurrent imputation network for clinical time series", "authors": [{"first": "A", "middle": ["W"], "last": "Mulyadi", "suffix": ""}, {"first": "E", "middle": [], "last": "Jun", "suffix": ""}, {"first": "H.-I", "middle": [], "last": "Suk", "suffix": ""}], "year": 2021, "venue": "IEEE Transactions on Cybernetics", "volume": "52", "issue": "9", "pages": "9684--9694", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, H<PERSON><PERSON><PERSON>. Uncertainty-aware variational-recurrent imputation network for clinical time series. IEEE Transactions on Cybernetics, 52(9):9684- 9694, 2021.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Multivariate bayesian structural time series model", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["R"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>ng", "suffix": ""}], "year": 2018, "venue": "J. <PERSON>. Learn. Res", "volume": "19", "issue": "1", "pages": "2744--2776", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, N. Multivariate bayesian structural time series model. J. Mach. Learn. Res., 19(1):2744-2776, 2018.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Gaussian Processes for Machine Learning", "authors": [{"first": "C", "middle": ["E"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["K I"], "last": "<PERSON>", "suffix": ""}], "year": 2006, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> <PERSON><PERSON> <PERSON>. Gaussian Processes for Machine Learning. MIT Press, 2006.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Autoregressive denoising diffusion models for multivariate probabilistic time series forecasting", "authors": [{"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Seward", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "8857--8868", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>- toregressive denoising diffusion models for multivariate probabilistic time series forecasting. In International Conference on Machine Learning, pp. 8857-8868. PMLR, 2021.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Maximum likelihood estimates of linear dynamic systems", "authors": [{"first": "H", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["T"], "last": "St<PERSON>bel", "suffix": ""}], "year": 1965, "venue": "AIAA journal", "volume": "3", "issue": "8", "pages": "1445--1450", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, C. T. Maximum likeli- hood estimates of linear dynamic systems. AIAA journal, 3(8):1445-1450, 1965.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Gaussian processes for time-series modelling", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. <PERSON> processes for time-series modelling, 2013.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Bayesian filtering and smoothing", "authors": [{"first": "S", "middle": [], "last": "Särkkä", "suffix": ""}], "year": 2013, "venue": "", "volume": "3", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> filtering and smoothing. Number 3. Cambridge University Press, 2013.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Explicit link between periodic covariance functions and state space models", "authors": [{"first": "A", "middle": [], "last": "Solin", "suffix": ""}, {"first": "S", "middle": [], "last": "Särkkä", "suffix": ""}], "year": 2014, "venue": "Artificial Intelligence and Statistics", "volume": "", "issue": "", "pages": "904--912", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Explicit link between periodic covariance functions and state space models. In Artificial Intelligence and Statistics, pp. 904-912. PMLR, 2014.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Stochastic differential equation methods for spatio-temporal gaussian process regression", "authors": [{"first": "A", "middle": [], "last": "Solin", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> et al. Stochastic differential equation methods for spatio-temporal gaussian process regression. 2016.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Score-based generative modeling through stochastic differential equations", "authors": [{"first": "Y", "middle": [], "last": "Song", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Poole", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2011.13456"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> <PERSON>, S<PERSON>, and <PERSON>, B. Score-based generative modeling through stochastic differential equations. arXiv preprint arXiv:2011.13456, 2020.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Conditional score-based diffusion models for probabilistic time series imputation", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Song", "suffix": ""}, {"first": "Y", "middle": [], "last": "Song", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Csdi", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "24804--24816", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. Csdi: Con- ditional score-based diffusion models for probabilistic time series imputation. Advances in Neural Information Processing Systems, 34:24804-24816, 2021.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "mice: Multivariate imputation by chained equations in r", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Groothuis-Oudshoorn", "suffix": ""}], "year": 2011, "venue": "Journal of statistical software", "volume": "45", "issue": "", "pages": "1--67", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, S<PERSON> and <PERSON>-<PERSON>, K. mice: Multi- variate imputation by chained equations in r. Journal of statistical software, 45:1-67, 2011.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Conditional expectation propagation", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "UAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, S. Conditional expectation propagation. In UAI, pp. 6, 2019.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Dynamic tensor decomposition via neural diffusion-reaction processes", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "*", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Dynamic tensor decomposition via neural diffusion-reaction processes. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "A robust seasonal-trend decomposition algorithm for long time series", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Gao", "suffix": ""}, {"first": "X", "middle": [], "last": "Song", "suffix": ""}, {"first": "L", "middle": [], "last": "Sun", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Robuststl", "suffix": ""}], "year": 2019, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "33", "issue": "", "pages": "5409--5416", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: A robust seasonal-trend decomposition algorithm for long time series. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 33, pp. 5409-5416, 2019.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Cost: Contrastive learning of disentangled seasonal-trend representations for time series forecasting", "authors": [{"first": "G", "middle": [], "last": "Woo", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Sahoo", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Hoi", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2202.01575"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Contrastive learning of disentangled seasonal-trend rep- resentations for time series forecasting. arXiv preprint arXiv:2202.01575, 2022.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Learning temporally causal latent processes from general temporal data", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Sun", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Sun", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2110.05428"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> Learning temporally causal latent processes from general temporal data. arXiv preprint arXiv:2110.05428, 2021.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Temporally disentangled representation learning", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "26492--26503", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>ly disentangled representation learning. Advances in Neural Information Processing Systems, 35:26492-26503, 2022.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Gain: Missing data imputation using generative adversarial nets", "authors": [{"first": "J", "middle": [], "last": "<PERSON>on", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "5689--5698", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: Missing data imputation using generative adversarial nets. In International conference on machine learning, pp. 5689- 5698. PMLR, 2018.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Unifying electricity forecasting with robust, flexible, and", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. eforecaster: Unifying electricity forecasting with robust, flexible, and", "links": null}}, "ref_entries": {"FIGREF0": {"fig_num": null, "type_str": "figure", "num": null, "uris": null, "text": "where x n ∈ R D is the Dsize value at n-th step and x d n represents it's values at d-th channel. There is a mask matrix M ∈ {0, 1} D×N , indicating whether the series value is observed or missing. The goal is to use the observed values, where M d,n = 1, to estimate the missing values x d n , where M d,n = 0. In the above setting, the interval between two consecutive timestamps is assumed to be constant by default. If the timestamps are irregularly sampled and continuous, the problem becomes Properties / Methods BayOTIDE TIDER Statistic-based DNN-based Diffusion-based Uncertainty-aware"}, "FIGREF1": {"fig_num": null, "type_str": "figure", "num": null, "uris": null, "text": "Figure 1: (a): The multivariate time series recovered from observations. The shaded region indicates two posterior standard deviations. (b)-(e): The weighted trend-seasonality factors learned by BayOTIDE of each channel."}, "FIGREF2": {"fig_num": "2", "type_str": "figure", "num": null, "uris": null, "text": "Figure 2: Online performance, scalability and sensitivity of BayOTIDE"}, "FIGREF3": {"fig_num": null, "type_str": "figure", "num": null, "uris": null, "text": "p(x m , x m+1 , x * , D)= p(x 1 ) m-1 j=1 p(x j+1 |x j ) M j=m+1 p(x j+1 |x j ) • p(D|x 1 , . . . , x M )dx 1:M \\{m,m+1} • p(x * |x m )p(x m+1 |x * ) = p(x m , x m+1 , D)p(x * |x m )p(x m+1 |x * ) p(x m+1 |x m ) . m , x m+1 , x * |D) ∝ p(x m , x m+1 |D)p(x * |x m )p(x m+1 |x * )."}, "FIGREF4": {"fig_num": null, "type_str": "figure", "num": null, "uris": null, "text": ")Then, we can simply merge the natural parameters of the two Gaussian and obtainp(x m , x m+1 , x * |D) = p(x m , x m+1 |D)N (x * |µ * , Σ * ),"}, "FIGREF6": {"fig_num": "3", "type_str": "figure", "num": null, "uris": null, "text": "Figure 3: Online imputation results on Solar-Power and Uber-Move."}, "TABREF0": {"html": null, "type_str": "table", "num": null, "text": "take timestamps as input, but the model is trained with discretized time embedding.", "content": "<table><tr><td>more challenging and the exact timestamps {t 1 , . . . , t N }</td></tr><tr><td>should be considered in the imputation model. In this paper,</td></tr><tr><td>we aimed to learn a general function X(t) : t → R D to</td></tr><tr><td>impute the missing values at any time t ∈ [t 1 , t N ].</td></tr><tr><td>3.2. Gaussian Process (GP) and State-Space Model</td></tr><tr><td>Gaussian Process (GP) (Ra<PERSON> &amp; Williams, 2006)s</td></tr><tr><td>is a powerful Bayesian prior for functional approxima-</td></tr><tr><td>tion, always denoted as f ∼ GP (0, κ (x, x ′ )). As a non-</td></tr><tr><td>parametric model, it's characterized by a mean function,</td></tr><tr><td>here assumed to be zero, and a covariance function or kernel</td></tr><tr><td>κ (x, x ′ ), which is a positive definite function that measures</td></tr><tr><td>the similarity of inputs. The choice of the kernel is crucial</td></tr><tr><td>as it determines the types of functions the GP can model.</td></tr><tr><td>For instance, the Matérn kernel</td></tr></table>"}, "TABREF1": {"html": null, "type_str": "table", "num": null, "text": "RMSE, MAE and CRPS scores of imputation results of all methods on three datasets with observed ratio = 50%.", "content": "<table><tr><td/><td/><td colspan=\"2\">-GuangZhou</td><td/><td>Solar-Power</td><td/><td/><td>Uber-Move</td><td/></tr><tr><td>Metrics</td><td>RMSE</td><td>MAE</td><td>CRPS</td><td>RMSE</td><td>MAE</td><td>CRPS</td><td>RMSE</td><td>MAE</td><td>CRPS</td></tr><tr><td>Deterministic &amp; Offline</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>SimpleMean</td><td>9.852</td><td>7.791</td><td>-</td><td>3.213</td><td>2.212</td><td>-</td><td>5.183</td><td>4.129</td><td>-</td></tr><tr><td>BRITS</td><td>4.874</td><td>3.335</td><td>-</td><td>2.842</td><td>1.985</td><td>-</td><td>2.180</td><td>1.527</td><td>-</td></tr><tr><td>NAOMI</td><td>5.986</td><td>4.543</td><td>-</td><td>2.918</td><td>2.112</td><td>-</td><td>2.343</td><td>1.658</td><td>-</td></tr><tr><td>SAITS</td><td>4.839</td><td>3.391</td><td>-</td><td>2.791</td><td>1.827</td><td>-</td><td>1.998</td><td>1.453</td><td>-</td></tr><tr><td>TIDER</td><td>4.708</td><td>3.469</td><td>-</td><td>1.679</td><td>0.838</td><td>-</td><td>1.959</td><td>1.422</td><td>-</td></tr><tr><td>Probabilistic &amp; Offline</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>Multi-Task GP</td><td>4.887</td><td>3.530</td><td>0.092</td><td>2.847</td><td>1.706</td><td>0.203</td><td>3.625</td><td>2.365</td><td>0.121</td></tr><tr><td>GP-VAE</td><td>4.844</td><td>3.419</td><td>0.084</td><td>3.720</td><td>1.810</td><td>0.368</td><td>5.399</td><td>3.622</td><td>0.203</td></tr><tr><td>CSDI</td><td>4.813</td><td>3.202</td><td>0.076</td><td>2.276</td><td>0.804</td><td>0.166</td><td>1.982</td><td>1.437</td><td>0.072</td></tr><tr><td>CSBI</td><td>4.790</td><td>3.182</td><td>0.074</td><td>2.097</td><td>1.033</td><td>0.153</td><td>1.985</td><td>1.441</td><td>0.075</td></tr><tr><td>Probabilistic &amp; Online</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"2\">BayOTIDE-fix weight 11.032</td><td>9.294</td><td>0.728</td><td>5.245</td><td>2.153</td><td>0.374</td><td>5.950</td><td>4.863</td><td>0.209</td></tr><tr><td>BayOTIDE-trend only</td><td>4.188</td><td>2.875</td><td>0.059</td><td>1.789</td><td>0.791</td><td>0.132</td><td>2.052</td><td>1.464</td><td>0.067</td></tr><tr><td>BayOTIDE</td><td colspan=\"3\">3.820 2.687 0.055</td><td>1.699</td><td colspan=\"5\">0.734 0.122 1.901 1.361 0.062</td></tr></table>"}, "TABREF2": {"html": null, "type_str": "table", "num": null, "text": "RMSE, MAE and CRPS scores of imputation results of all methods on three datasets with observed ratio = 70%.", "content": "<table/>"}, "TABREF4": {"html": null, "type_str": "table", "num": null, "text": "The imputation results of BayOTIDE with settings of irregulate and all-channel-missing timestamps on three datasets with observed ratio = {50%, 70%}.", "content": "<table/>"}, "TABREF5": {"html": null, "type_str": "table", "num": null, "text": "explainable machine learning algorithms. In Proceedings of the AAAI Conference on ArtificialIntelligence, volume 37, pp. 15630-15638, 2023.", "content": "<table><tr><td>Appendix</td></tr><tr><td>.1. LTI-SDE representation of GP with Matérn kernel</td></tr><tr><td>and periodic kernel</td></tr><tr><td>.1.1. CONNECT GP WITH LTI-SDE BY SPECTRAL</td></tr><tr><td>ANALYSIS</td></tr></table>"}, "TABREF6": {"html": null, "type_str": "table", "num": null, "text": "1.2. THE CLOSED-FORM OF LTI-SDE AND STATE SPACE PRIOR WITH MATÉRN KERNEL AND PERIODIC KERNELWith the canonical form of LTI-SDE (22)and state space prior(23) and above derivation, we can work out the closedform of LTI-SDE and state space prior for Matérn kernel and periodic kernel. We present the results in the following.", "content": "<table/>"}, "TABREF7": {"html": null, "type_str": "table", "num": null, "text": "The hyperparameter setting of BayOTIDE for the imputation task.", "content": "<table/>"}, "TABREF9": {"html": null, "type_str": "table", "num": null, "text": "The negative log-likelihood score (NLLK) of all probabilistic imputation methods on all datasets with observed ratio", "content": "<table/>"}}}}