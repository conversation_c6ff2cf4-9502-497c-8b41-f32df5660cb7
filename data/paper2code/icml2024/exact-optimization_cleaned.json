{"paper_id": "exact-optimization", "title": "Towards Efficient Exact Optimization of Language Model Alignment", "abstract": "The alignment of language models with human preferences is vital for their application in realworld tasks. The problem is formulated as optimizing the model's policy to maximize the expected reward that reflects human preferences with minimal deviation from the initial policy. While considered as a straightforward solution, reinforcement learning (RL) suffers from high variance in policy updates, which impedes efficient policy improvement. Recently, direct preference optimization (DPO) was proposed to directly optimize the policy from preference data. However, we show that DPO derived based on the optimal solution of the problem leads to a compromised mean-seeking approximation of the optimal solution in practice. In this paper, we propose efficient exact optimization (EXO) of the alignment objective. EXO is guaranteed to optimize in the same direction as RL algorithms asymptotically for arbitrary policy parametrization. This leads to the same mode-seeking solution, while enables efficient optimization by circumventing the complexities of RL. We also compare our method to DPO with both theoretical and empirical analyses, and further demonstrate the advantages of our method over existing approaches on realistic human preference data. Code is available at https://github.com/haozheji/ exact-optimization.", "pdf_parse": {"paper_id": "exact-optimization", "abstract": [{"text": "The alignment of language models with human preferences is vital for their application in realworld tasks. The problem is formulated as optimizing the model's policy to maximize the expected reward that reflects human preferences with minimal deviation from the initial policy. While considered as a straightforward solution, reinforcement learning (RL) suffers from high variance in policy updates, which impedes efficient policy improvement. Recently, direct preference optimization (DPO) was proposed to directly optimize the policy from preference data. However, we show that DPO derived based on the optimal solution of the problem leads to a compromised mean-seeking approximation of the optimal solution in practice. In this paper, we propose efficient exact optimization (EXO) of the alignment objective. EXO is guaranteed to optimize in the same direction as RL algorithms asymptotically for arbitrary policy parametrization. This leads to the same mode-seeking solution, while enables efficient optimization by circumventing the complexities of RL. We also compare our method to DPO with both theoretical and empirical analyses, and further demonstrate the advantages of our method over existing approaches on realistic human preference data. Code is available at https://github.com/haozheji/ exact-optimization.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Despite the proficiency of large language models, e.g., GPT-3 (<PERSON> et al., 2020) supervision, they are still prone to produce harmful (<PERSON> et al., 2022) , biased (<PERSON><PERSON> et al., 2021) , and unfaithful (<PERSON> et al., 2023c) responses due to the heterogeneous sources of their pre-training corpora. Ensuring the large language models to generate desired responses that are in line with humans' ethical standards and quality preferences is crucial for the development of reliable AI systems.", "section": "Introduction", "sec_num": "1."}, {"text": "The problem, well known as language model (LM) alignment with human preferences (<PERSON><PERSON><PERSON> et al., 2022) , is generally formulated as optimizing the LM policy π θ to maximize the expected reward, which reflects human preferences regarding the completion y for a given prompt x. The practical recipe is to train a reward model r ϕ to predict the human-chosen response from a set of responses generated by an initial LM policy π init . Yet, the challenge of acquiring substantial high-quality preference data often impedes accurate estimation of the ideal reward model. Consequently, this empirically learned reward model may lead to misspecified behaviors, particularly under the distributional shift between its training data and the data generated by π θ (<PERSON> et al., 2023) . Therefore, the final objective of alignment additionally involves minimizing the reverse Kullback-Leibler (KL) divergence of π θ from its initial distribution π init with an intensity β, besides maximizing the expected reward:", "section": "Introduction", "sec_num": "1."}, {"text": "max π θ E x∼D,y∼π θ (y|x) [r ϕ (x, y)] -βD KL (π θ ∥π init ). (1)", "section": "Introduction", "sec_num": "1."}, {"text": "Due to the discrete nature of content generation from an LM (i.e., sampling y from π θ (•|x)), the most widely adopted approach to optimize Eq. ( 1) is reinforcement learning (RL) (<PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2022) . Albeit well studied even before the era of large language models, RL solutions are notorious for their poor stability due to the high variance in estimating the policy gradients or value functions, which potentially worsens sample complexity and thus compromises efficient convergence (<PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2017) .", "section": "Introduction", "sec_num": "1."}, {"text": "As a remedy, direct preference optimization (DPO) was recently proposed to replace the RL solutions (<PERSON><PERSON><PERSON><PERSON> et al., 2023) . Specifically, DPO defines a pair-wise preference loss on the estimated policy π θ by leveraging the following policy-reward mapping in the optimal solution to Eq. ( 1): However, this optimal policy is obtained analytically while not considering the practical parametrization of π θ . When π θ is mis-specified and cannot perfectly capture the target π * β , our analysis demonstrates that DPO leads to a compromised approximation of π * β , which only covers the support of π * β while failing to capture its modes. In this paper, we present an underexplored perspective of the alignment objective in Eq. ( 1): We prove that Eq. ( 1) is equivalent to probability matching between the parametrized policy π θ and the optimal policy π * β defined in Eq. ( 2) measured by the reverse KL divergence D KL (π θ ∥π * β ). Based on the established equivalence, we propose efficient exact optimization (EXO) of the KL-regularized reward maximization objective. Specifically, we rigorously prove that irrespective of the policy's parametrization, EXO is guaranteed to improve π θ in the same direction as the RL solutions asymptotically. In practice, we demonstrate that EXO facilitates efficient optimization towards this alignment objective with finite samples while bypassing the complexities of RL.", "section": "Introduction", "sec_num": "1."}, {"text": "EQUATION", "section": "Introduction", "sec_num": "1."}, {"text": "Under this probability matching perspective, we demonstrate that DPO actually corresponds to minimizing the forward KL divegence D KL (π * β ∥π θ ). Though minimizing both the forward and reverse KL divergences lead to the same analytic solution, it is not necessarily achievable when taking into account the expressivity gap between the model families of π * β and π θ (<PERSON> et al., 2021) . Under this realistic constraint, minimizing these two divergences converge to parametrized policies with different behaviors (<PERSON> & Na<PERSON>, 2006) . As illustrated in Figure 1 , minimizing the reverse KL fosters a mode-seeking policy π θRKL that concentrates to the principal modes of π * β (<PERSON> et al., 2022) , while minimizing the forward KL results in a mean-seeking policy π θFKL that places large mass to the mean of different modes in π * β , which does not necessitate high probabilities under π * β . In the inference stage, π θRKL is preferrably better than π θFKL by capturing the main characteristics of π * β (<PERSON> et al., 2023b) .", "section": "Introduction", "sec_num": "1."}, {"text": "We conduct a series of experiments to verify the effectiveness and scalability of EXO. We first systematically evaluate the efficiency of different approaches in trading off maxi-mizing the oracle reward and minimizing the KL divergence during optimization of the alignment objective. Then, we conduct evaluations on the effectiveness of learning from real human preferences in various alignment benchmarks, involving summarization, dialogue generation, and instruction following tasks. Comprehensive empirical analyses substantiate our theoretical findings and demonstrate the advantageous performance of EXO over DPO and PPO.", "section": "Introduction", "sec_num": "1."}, {"text": "Finally, we summarize our contributions in this paper:", "section": "Introduction", "sec_num": "1."}, {"text": "• We reveal the underexplored equivalence between KLregularizd reward maximization and minimizing the reverse KL divergence against the optimal policy for the language model alignment problem.", "section": "Introduction", "sec_num": "1."}, {"text": "• We propose EXO, an algorithm towards efficient exact optimization of the KL-regularized reward maximization objective for alignment. Both theoretical and empricial results confirm its effectiveness.", "section": "Introduction", "sec_num": "1."}, {"text": "• We show that DPO corresponds to minimizing the forward KL divergence, which is less effective in capturing the essential characteristics of the optimal policy.", "section": "Introduction", "sec_num": "1."}, {"text": "We first formally review the formulation and objective of the alignment problem. Then we review existing approaches that solve this problem via reinforcement learning and direct preference optimization, respectively.", "section": "Preliminaries", "sec_num": "2."}, {"text": "Given a vocabulary V, a language model defines a probability distribution π", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "(x) = n t=1 π(x t |x 1 , • • • , x t-1 ) over a sequence of tokens x = (x 1 , • • • , x n ).", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "We apply π to a conditional generation task of interest with input space X = V m and output space Y = V n modeled by π(y|x) = π(x, y)/π(x).", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "The alignment procedure typically starts from supervised fine-tuning (SFT) the language model on a high-quality dataset D sft via maximum likelihood estimation, which obtains the SFT policy π sft .", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "Then a preference dataset D pref is collected by asking humans to select a better response from (y 1 , y 2 ) ∼ π sft (y|x) given a prompt x from the same domain of D sft . Let y w and y l be the chosen and rejected responses among (y 1 , y 2 ) respectively according to human preferences.", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "A reward model r ϕ : X × Y → R is usually learned on D pref to act as a surrogate to expensive human labeling. The reward model is trained to prioritize y w over y l by minimizing the following pair-wise preference loss: L r (r ϕ ) = E (x,y w ,y l )∼D preflog e r ϕ (x,y w ) e r ϕ (x,y w ) + e r ϕ (x,y l ) .", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "Finally, a policy π θ is learned to maximize the following alignment objective (<PERSON><PERSON> et al., 2019) :", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "EQUATION", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "Given a prompt x sampled from the dataset D pref , the objective seeks to find the π θ that maximizes the expected reward while minimizes its reverse KL divergence against the SFT policy π sft governed by the coefficient β > 0. The KL penalty keeps π θ from moving too far from π sft to avoid over optimization of the reward model.", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "The analytic solution that maximizes J β lhf (π θ ) takes the form of an energy-based model (EBM):", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "EQUATION", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "where", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "Z β (x) = y ′ ∈Y π sft (y ′ |x)e 1 β r ϕ (x,y ′ )", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "is the partition function. In Eq (4), the coefficient β can be considered as the temperature for controlling the strength of the reward model signal when sampling from π * β (y|x).", "section": "Aligning Language Models with Human Preferences", "sec_num": "2.1."}, {"text": "Due to the discrete nature of language generation, the objective in Eq. ( 3) is not differentiable with respect to π θ , which prohibits supervised training. One standard approach is to use RL algorithms to optimize this objective. <PERSON><PERSON><PERSON> et al. (2019) proposed to search for π θ that maximizes a KL-regularized reward r ϕ (x, y)β log π θ (y|x) πsft(y|x) . This can be achieved by policy gradient methods, such as Proximal Policy Optimization (PPO) (<PERSON> et al., 2017) .", "section": "RL Fine-Tuning", "sec_num": "2.2."}, {"text": "To optimize π θ directly using the preference data, <PERSON><PERSON><PERSON><PERSON> et al. (2023) rearranged Eq. ( 4) to express the reward function by the optimal policy π * β ,", "section": "Direct Preference Optimization", "sec_num": "2.3."}, {"text": "EQUATION", "section": "Direct Preference Optimization", "sec_num": "2.3."}, {"text": "Then they proposed to directly optimize the policy π θ by replacing π * β with π θ and substituting the corresponding reward function into a pair-wise preference loss:", "section": "Direct Preference Optimization", "sec_num": "2.3."}, {"text": "L dpo (π θ ) = E (x,y w ,y l )∼D pref -log σ β log π θ (y w |x) π sft (y w |x) -β log π θ (y l |x) π sft (y l |x) . (6)", "section": "Direct Preference Optimization", "sec_num": "2.3."}, {"text": "In this section, we start with a generalized alignment objective and present its equivalent form under the perspective of probability matching. Then we formally derive efficient exact optimization (EXO) of the generalized alignment objective while bypassing the necessity of employing any RL algorithms. Furthermore, we compare against DPO and demonstrate the advantage of our approach in terms of the distributional characteristics of the learned policy. All the proofs are provided in Appendix A.", "section": "Methodology", "sec_num": "3."}, {"text": "We first introduce a generalized alignment objective that distributes the intensity of the KL regularization regarding the SFT policy to both the parametrized policy π θ and the reward model r ϕ , which intuitively connects the regularization setting of DPO (Eq. ( 6)) that only regularizes π θ and PPO (Eq. ( 3)) that only regularizes r ϕ . In the following theorem, we present the formal definition and the property of the generalized alignment objective. Theorem 3.1. Let β π > 0, β r > 0 and β π β r = β. The generalized alignment objective is defined as", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "EQUATION", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "where π βπ θ (y|x) satisfies", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "EQUATION", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": ")", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "Given unlimited model capacity, the optimal π θ * that maximizes", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "J βr lhf (π βπ θ ) satisfies π θ * = π * β .", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "Intuitively, J βr lhf (π βπ θ ) calculates the expectation of the reward regularized with the intensity β r with respect to the policy regularized with the intensity β π . As the total regularization intensity β = β r β π is fixed, J βr lhf (π βπ θ ) maintains the same analytic solution π * β as J β lhf (π θ ). Furthermore, it interpolates the policy regularization setting (β r = 1, β π = β) in the DPO objective 1 and the reward regularization setting (β r = β, β π = 1) in the PPO objective when continuously tunning β r and β π while keeping their product fixed. We also empirically show the effect of β r and β π beyond the impact on their product β = β r β π in Appendix C.2.", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "Next, we derive an equivalent form of the generalized alignment objective by rearranging the elements in Eq. ( 7), which offers a new insight of the alignment problem from the probability matching perspective. The detailed derivation can be", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "1 β log π θ (y|x) π sft (y|x) = log π β θ (y|x)", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "π sft (y|x) + C(x) where C(x) depends only on x, which does not effect the DPO objective in Eq. (6). found in Appendix A.2.", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "EQUATION", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "As the second term is a constant with respect to π θ , Eq. ( 9) reveals that maximizing the generalized alignment objective J βr lhf (π βπ θ ) with respect to π θ is equivalent to minimizing the expected reverse KL divergence D KL (π βπ θ ∥π * βr ), where π βπ θ is a function of π θ as defined in Eq. ( 8). This equivalence implies the possibility of converting the reward maximization problem into a supervised divergence minimization problem, which is able to circumvent the poor stability and low sample efficiency issue caused by high variance in RL solutions (<PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2017) . In the following, we introduce our approach towards exact optimization of this generalized alignment objective by practically realizing the probability matching objective. Without loss of generality, our results remain valid for the original alignment objective in Eq. (3).", "section": "From the Generalized Alignment Objective to Probability Matching", "sec_num": "3.1."}, {"text": "We now formally derive EXO which optimizes the generalized alignment objective J βr lhf (π βπ θ ) by realizing the reverse KL divergence D KL (π βπ θ ∥π * βr ). We start with the general setting of language model alignment which preassumes the existence of a reward model r ϕ ; then we consider the case of learning directly from the preference data.", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "To facilitate policy optimization with straightforward gradient back propagation, we rewrite D KL (π βπ θ ∥π * βr ) under the expectation of the proposal policy π sft :", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "D KL (π βπ θ ∥π * βr ) = E πsft(y|x) π βπ θ (y|x) π sft (y|x) log π βπ θ (y|x) π * βr (y|x)", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": ".", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "The above expression can be further simplified by defining the log ratio as f θ (x, y) = log π βπ θ (y|x)log π sft (y|x):", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "D KL (π βπ θ ∥π * βr ) = E πsft(y|x) e f θ (x,y) log e f θ (x,y) 1 Z βr (x) e r ϕ (x,y) βr", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": ".", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "As the intractable nature of Z βr (x) hinders direct estimation via <PERSON> simulation, we propose a practical way to estimate this term by first drawing multiple samples from π sft , and then calculating the reverse KL between the probability distributions defined by f θ (x, y) and 1 βr r ϕ (x, y) over these samples via self-normalization respectively.", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "• Learning from a reward model. Formally, given K > 1 i.i.d. completions y 1:K = {y 1 , • • • , y K } drawn from π sft (y|x), we define an empirical distribution p f θ by normalizing the exponential reward e f θ (x,y) over the K samples:", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "p f θ (i|y 1:K , x) = e f θ (x,y i )", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "K j=1 e f θ (x,y j ) .", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "(10)", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "Recall that f θ (y|x) = log π βπ θ (y|x)log π sft (y|x) and π βπ θ (y|x) ∝ π θ (y|x) βπ π sft (y|x) 1-βπ , Eq. ( 10) can be rewritten into a form that explicitly depends on π θ :", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "where β π can be regarded as the inverse temperature that modulates the empirical distribution defined by the log ratio between π θ and π sft . Similarly, we define a distribution p r over the K samples modeled by the reward model r ϕ :", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "Finally, we translate the original objective of reward maximization J βr lhf (π βπ θ ) into the reverse KL between p f θ and p r ϕ over y 1:K sampled from π sft :", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": ")", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "The complete form of L exo is presented in Eq. ( 23) in Appendix A.3. Besides its practical simplicity for implementation, we also elucidate its theoretical attributes by characterizing its connection with the generalized alignment objective in Theorem 3.2.", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "Theorem 3.2. Following π * βr , π βπ θ and J βr lhf (π βπ θ ) defined in Eq. (4), (8), and (7), for K → ∞ and arbitary θ, the gradient of L exo (π θ ) satisfies", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": ")", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "Theorem 3.2 suggests the optimization direction for π θ during the intermediate optimization steps for minimizing L exo (π θ ) aligns with the direction required to maximize the generalized alignment objective J βr lhf (π βπ θ ) asymptotically, when sufficient sample population is provided. In §4.1, we show that EXO achieves encouraging convergence in practical scenarios with only a finite K. Again, as a special case, ∇ θ L exo (π θ ) aligns with ∇ θ J β lhf (π θ ) when β π = 1, β r = β, which effectively connects with Eq. ( 3). The result in Theorem 3.2 is crucial, as it sheds light on exact optimization of the generalized alignment objective via a simple density matching approach with strong theoretical guarantees. In Appendix A.5, we provide a mechanistic understanding of the gradient ∇ θ L exo (π θ ) which is a weighted sum of the gradients ∇ θ log π θ (y k |x). The weight is proportional to D KL (p f θ ∥p r ϕ ) which characterizes the distributional gap biased by a point-wise correction on the sample y k .", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "• Learning from human preference data. In situations where only preference data is accessible, we devise an empirical formulation of L exo . Given a preference dataset D pref where each prompt x is paired with y w and y l denoting the chosen and rejected completions. This binary supervision can be effectively transformed into an empirical distribution of p r h defined by the underlying reward r h of human preference. To avoid infinity when calculating KL divergence, we smooth the one-hot distribution into a soft distribution, i.e., p r h (w|y w , y l , x) = 1ε and p r h (l|y w , y l , x) = ε, where ε > 0 is a hyperparameter. p f θ can still be computed according to Eq. ( 11) over y w and y l . As a result, we present the EXO objective on the prefence data by setting K = 2 and substituting r ϕ with r h in Eq. ( 13):", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "In practice, π sft is fine-tuned on either D sft which is collected from the same domain as D pref , or the chosen completions in D pref when D sft is not available. This closes the distributional gap between π sft and the unknown distribution that generates the preference data D pref .", "section": "Efficient Exact Optimization of the Generalized Alignment Objective", "sec_num": "3.2."}, {"text": "Under the perspective of probability matching, we formally demonstrate that the DPO objective corresponds to the forward KL which is essentially different from the reverse KL required by the alignment objective J β lhf (π θ ) in Eq. (3). We then analyze their differences under realistic constraints of model capacities.", "section": "Comparing with DPO under the Perspective of Probability Matching", "sec_num": "3.3."}, {"text": "We first consider the general form of the DPO objective. Given K completions y 1:K = {y 1 , • • • , y K } drawn from π sft (y|x) and a reward model r ϕ that captures human prefence, we generalize L dpo by substituting the sigmoid function with softmax over K responses and replacing the one-hot label with a soft distribution defined by r ϕ :", "section": "Comparing with DPO under the Perspective of Probability Matching", "sec_num": "3.3."}, {"text": "L dpo-rw (π θ ) = E x∼D pref E πsft(y 1:K |x) - K i=1 e 1 βr r ϕ (x,y i ) K j=1 e 1 βr r ϕ (x,y j ) log e βπ log π θ (y i |x) π sft (y i |x) K j=1 e βπ log π θ (y j |x) π sft (y j |x) . (17)", "section": "Comparing with DPO under the Perspective of Probability Matching", "sec_num": "3.3."}, {"text": "Upon substituting r ϕ with r h and setting K = 2, L dpo-rw simplifies to L dpo . In the following, we build connection of L dpo-rw (π θ ) to the forward KL divegence D KL (π * βr ∥π βπ θ ). Theorem 3.3. With π * βr , π βπ θ and p r ϕ defined in Eq. ( 4), ( 8) and (12) respectively, for K → ∞ and arbitary θ, the gradient of L dpo-rw (π θ ) satisfies", "section": "Comparing with DPO under the Perspective of Probability Matching", "sec_num": "3.3."}, {"text": "EQUATION", "section": "Comparing with DPO under the Perspective of Probability Matching", "sec_num": "3.3."}, {"text": "In the following, we consider β π = 1, β r = β to simplify the analysis, while the results still hold for general settings.", "section": "Comparing with DPO under the Perspective of Probability Matching", "sec_num": "3.3."}, {"text": "Putting the results of Theorem 3.2 and Theorem 3.3 together, we readily connect L exo and L dpo-rw with two divergences, i.e., the reverse KL, D KL (π θ ∥π * β ), and the forward KL, D KL (π * β ∥π θ ), respectively. To provide a clear image, we illustrate the interrelationship among the objectives of EXO, DPO, and the objective of alignment in Figure 5 , as presented in Appendix A.6. Although minimizing these two divergences leads to the same analytic solution π * β , they converge to two distinct solutions when considering the expressivity bottleneck of the practical model parametrization.", "section": "Comparing with DPO under the Perspective of Probability Matching", "sec_num": "3.3."}, {"text": "Specifically, the LM policy π θ is commonly parametrized as an auto-regressive (AR) model, which enables efficient sampling due to the employment of local normalization. However, the optimal policy π * β (y|x) ∝ π sft (y|x) exp( 1 β r ϕ (x, y)) defined as an EBM trade-offs sampling efficiency with modeling capacity. Notably, <PERSON> et al. (2021) rigorously proved that AR models cannot perfectly capture all possible distributions defined by EBMs in terms of supports or rankings based on the computational complexity theories. From an empirical view, this result is also intuitive because the reward model as a discriminator is more flexible in distribution modeling than the auto-regressive generator.", "section": "Comparing with DPO under the Perspective of Probability Matching", "sec_num": "3.3."}, {"text": "Under the practical constraint that π θ cannot perfectly represent π * β , minimizing the forward and reverse KL results in two policies π θFKL and π θRKL with different properties. One well-known fact is that π θFKL is mean-seeking while π θRKL is mode-seeking (<PERSON> & <PERSON>, 2006) . In Figure 1 , we consider an illustrative picture of fitting a unimodal π θ to a multi-modal target π * β . To maintain a minimal forward KL divergence, π θFKL must encompass all the modes of π * β (regions where π * β exhibits significant values). However due to the representational constraints, π θFKL tends to overpresents the mean of different modes of π * β , potentially extending into the long tail region of π * β (<PERSON> et al., 2022; <PERSON> et al., 2023a) . On the other hand, π θRKL can select one mode of π * β without causing the reverse KL to explode, meaning that π θRKL will effectively capture the major mode of π * β under realistic model capacity. In §4.1, we empirically demonstrate the results of optimizing these two divergences in practice.", "section": "Comparing with DPO under the Perspective of Probability Matching", "sec_num": "3.3."}, {"text": "Within the context of language model alignment, reverse KL is preferred for generating samples according to the evaluation of the optimal policy. Conversely, forward KL tradeoffs preference evaluation with sample diversity, which is rational only if the samples are valid under the evaluation. To some extent, the reverse KL can also effectively capture this rational diversity, as it maximizes the policy's entropy to prevent distributional collapse to a single point.", "section": "Comparing with DPO under the Perspective of Probability Matching", "sec_num": "3.3."}, {"text": "We verify the effectiveness of EXO via extensive experimentations. In §4.1, we systematically study the frontier of reward maximization and KL minimization achieved by different alignment methods in a controlled text generation task following previous works (<PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON><PERSON> et al., 2023) . We investigate two different settings, including learning directly from preference data governed by a predefined oracle reward model, and 2) learning from a reward model estimated from the preference data. In §4.2, we compare EXO against different approaches on realistic alignment problems including generating human-preferred summaries, helpful dialogue responses, and answers that follow human instructions. Under both settings of learning directly from the preference data and from a reward model, EXO outperforms existing alignment approaches, e.g., DPO and PPO. Next, we briefly describe the experiment settings and leave additional details in Appendix B.", "section": "Experiments", "sec_num": "4."}, {"text": "Experiment Setting. Our experiments consider two practical settings of LM alignment: (i) Directly train on a preference dataset D pref = {x (j) , y", "section": "Experiments", "sec_num": "4."}, {"text": "(j) w , y (j) l } N", "section": "Experiments", "sec_num": "4."}, {"text": "j=1 where y w and y l are the chosen and rejected responses judged by an oracle reward model or human labelers. (ii) Train on a reward dataset D rw = {x (j) , (y", "section": "Experiments", "sec_num": "4."}, {"text": "(j) 1 , r (j) 1 ), • • • , (y (j) K , r (j) K )} N", "section": "Experiments", "sec_num": "4."}, {"text": "j=1 where y k is generated by the SFT policy and r k is a scalar reward provided by a reward model estimated on the given preference dataset. In the controlled text generation task, the policy is optimized to generate a completion y with positive sentiment given a prefix x of a movie review from the IMDB dataset2 (<PERSON><PERSON> et al., 2011) . To systematically evaluate the alignment performance, we train a binary sentiment classifier on the IMDB dataset and define the oracle reward as its log odds following <PERSON><PERSON> et al. (2019) . Both the policy and the reward models are initialized from the GPT-2 large model (<PERSON><PERSON> et al., 2019) . In the summarization task, the policy is required to generate a summary y of the post x from the Reddit forum that is preferred by human annotators. Following <PERSON><PERSON><PERSON> et al. (2020) , we use the same filtered version3 of the Reddit TL;DR summarization dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2017) to train the SFT policy and use their preference dataset4 for the alignment problem. In the dialogue generation task, the policy is learned to generate a helpful response y given multi-turn dialogue history between the user and the assistant denoted as x. We use the helpfulness subset of the Anthropic Helpful and Harmless dialogue dataset5 (<PERSON> et al., 2022) as the preference dataset and train the SFT policy using the chosen responses. For summarization and dialogue generation tasks, we initialize both the policy and the reward model from the Pythia-2.8B (<PERSON><PERSON><PERSON> et al., 2023) following <PERSON><PERSON><PERSON><PERSON> et al. (2023) . To ensure sample quality, we use a temperature of τ = 0.8 to divide the logits of the language model in all experiments. Lastly, for the instruction following task, we create a dataset based on instructions with high demand and representativeness from the real-world application scenarios, featuring 83K pairs of preferences annotated by human labelers and 49K prompts for policy training. The average lengths of the instructions and the answers are 47 and 230 respectively. We curate a diverse set of high-quality test instructions to assess a range of capabilities of the learned LM policy, encompassing multilingual ability, creative writing, open-ended question answering, and role playing. Each category takes the same proportion in the test set. Both the policy and the reward models are initialized from ChatGLM2-6B (Du et al., 2022) .", "section": "Experiments", "sec_num": "4."}, {"text": "Evaluation. In the controlled text generation task, we evaluate the frontier of the oracle reward and the KL divergence achieved by different approaches. This enables us to systematically compare the effectiveness of different methods in maxmizing the oracle reward under the same distributional shift constrained by the reverse KL. For experiments on the public preference datasets of summarization and dialogue generation, we use the reward model trained on the preference dataset as an in-domain proxy of the unknown ground-truth reward and also query GPT-4 for zero-shot pair-wise evaluation, which is shown to be consistent with human judgments (<PERSON><PERSON><PERSON><PERSON> et al., 2023) . The prompts for GPT-4 evaluation are slightly modified based on those used in <PERSON><PERSON><PERSON><PERSON> et al. (2023) , as detailed in Appendix B. We compare the generated outputs against those generated by the SFT policy and the preferred choice in the preference dataset. For the instruction-following task, we report the win rate of directly comparing our method against various baselines as judged by GPT-4. Additionally, we employ human assessment to evaluate criteria including adherence to instruction, correctness, fluency, safety and helpfulness.", "section": "Experiments", "sec_num": "4."}, {"text": "Methods for Comparison. We consider the following methods for aligning language models with human prefer- ences under various settings. Under the setting of learning directly from preferences, we consider the special case of EXO for preference learning L exo-pref (Eq. ( 16)) denoted as EXO pref , and the standard DPO (<PERSON><PERSON><PERSON><PERSON> et al., 2023 ) that minimizes the pair-wise loss L dpo (Eq. ( 6)) on the preference data, which we denoted as DPO pref . Then we consider the setup of alignment with a reward model estimated from the preference dataset, which includes the RL algorithm PPO (<PERSON> et al., 2019) that optimizes the expected reward with a KL penalty (Eq. ( 3)), the general EXO objective L exo (Eq. ( 13)) that performs probability matching by minimizing reverse KL, which is denoted as EXO rw , the general DPO objective L dpo-rw (Eq. ( 17)) that minimizes the forward KL, which is denoted as DPO rw , and the Best-of-N method which first samples N = 128 outputs from the SFT policy and then returns the response with the highest score according to the reward model. Note that the Best-of-N baseline is practically inefficient and can be regarded as an upperbound of exploiting the SFT policy according to the reward model in Eq. ( 4) by trading off the computation.", "section": "Experiments", "sec_num": "4."}, {"text": "To avoid undesirable reward overoptimizaion due to distributional shift, a preferred alignment solution should return a policy that obtains high oracle reward while incuring minimum deviation from π sft . Thereby, we plot the frontier of the oracle reward against KL divergence in Fig- ure 2 . We additionally present the accuracy-KL frontier in Figure 7 in Appendix C.3. Each point represents a checkpoint of the learned policy which is evaluated on 512 prefixes from the test set to complete the response with maximumly 512 tokens. We sample M = 4 completions {y i } M i=1 for each given prompt x to calculate the average oracle reward as well as to reduce the variance of approximating the sequence-level KL divergence", "section": "Alignment with the Oracle Reward", "sec_num": "4.1."}, {"text": "D KL (π θ ∥π sft ) ≈ 1 M M i=1 log π θ (y i |x) -log π sft (y i |x).", "section": "Alignment with the Oracle Reward", "sec_num": "4.1."}, {"text": "Despite aiming to optimize the same alignment objective, the EXO approaches (EXO pref and EXO rw ) yield the most efficient frontiers in their respective settings , evidenced by consistently achieving higher oracle rewards than baselines under the same KL divergence. Specifically, in the setting of directly learning from preference pairs, EXO pref outperforms DPO pref by a large margin, which underscores EXO's better sample efficiency when learning from a constrained source of preference indicators. As K increases, the frontier of EXO rw begins to exhibit convergence, indicating an effective progression towards the intended solution within a finite K. Although DPO rw also improves over DPO pref when K = 4, the frontier becomes worse when K is further increased to 8. This result substantiates our analysis about the mean-seeking behavior of forward KL, which leads to inaccuracy in capturing the modes of the complex target distribution. Finally, we illustrate the strong optimization efficiency of EXO in Figure 8 , evidenced by consistently achieving high and stable oracle rewards within fewer number of training steps compared with PPO and DPO in Appendix C.4.", "section": "Alignment with the Oracle Reward", "sec_num": "4.1."}, {"text": "Next, we compare DPO and EXO from the probability matching perspective by visualizing the probability density of the policies obtained by these two approaches6 . In Figure 3 , we plot the estimated density ratio of the optimal and learned policies by EXO and DPO against π sft given a randomly chosen test prompt \"This Fox spectacle was a big hit when released in\". Since the probability density of an LM policy is defined over a high dimensional space of Y = V n , it is intractable to evaluate every point in this space exhaustively. Thus, we consider the representative data points that are sampled from π sft , and sort them in the ascending order of their log probabilities. Then we compute the empirical distribution under the learned policies over these samples. Formally, given M = 256 samples {y i } M i=1 drawn from π sft conditioned on the prompt x, the empirical distribution πθ is calculated via self-normalized importance sampling over the learned policy π θ :", "section": "Alignment with the Oracle Reward", "sec_num": "4.1."}, {"text": "πθ (y i |x) = M π θ (y i |x) M j=1 π θ (y j |x)/π sft (y j |x)", "section": "Alignment with the Oracle Reward", "sec_num": "4.1."}, {"text": ". For the optimal policy, the empirical distribution reduces to:", "section": "Alignment with the Oracle Reward", "sec_num": "4.1."}, {"text": "π * β (y i |x) = M π sft (y i |x) exp(r(x, y i )/β) M j=1 exp(r(x, y j )/β)", "section": "Alignment with the Oracle Reward", "sec_num": "4.1."}, {"text": ".", "section": "Alignment with the Oracle Reward", "sec_num": "4.1."}, {"text": "Finally, we use kernel density estimation to estimate the probability density π(y|x) of the empirical distribution and plot the density ratio ρ π (y|x) = π(y|x) πsft(y|x) against the log probability of the data points under π sft . From the result, the density ratio of the EXO policy closely matches the optimal policy at the high probability region against π sft , which reflects its ability in capturing the major modes of the optimal policy. However, the DPO policy overestimates the long tail in π sft due to the mean-seeking tendency of forward KL, resulting in a reduced accuracy in fitting the modes of the optimal policy. We present more visualization results in Appendix C.5.", "section": "Alignment with the Oracle Reward", "sec_num": "4.1."}, {"text": "Next, we undertake a series of experiments focused on learning from real human preferences. In the tasks of summarization and dialogue generation, we consider the two settings of learning directly from preference data and from a reward model respectively. We set the same hyperparameters (e.g., β π , β r ) for EXO and DPO across different settings and datasets, and provide the results of tuning these hyperparameters in Appendix C.1 and C.2. Evaluation results on text summaization and dialogue generation are shown in Table 1 and Table 2 respectively. Upon comparison with both the SFT and chosen responses, EXO demonstrates notable improvement over DPO and PPO. This advantage is evident in evaluations using both the in-domain reward model and zeroshot assessment by GPT-4, across both settings of learning from preferences and from a reward model. Notably, EXO is the only practically efficient method to attain a GPT-4 win rate exceeding 60% when compared to the chosen responses that may have been produced by a more advanced language model. Although the Best-of-N baseline achieves comparable or higher reward model win rate by maximizing out the reward model, it suffers from the most significant decline of win rate when assessed by GPT-4. This drop of performance is attributed to its excessive exploitation of the imperfect reward model while neglecting the deviation from the initial SFT policy. We provide examples generated by DPO and EXO on both tasks in Appendix C.6.", "section": "Alignment with Human Preferences", "sec_num": "4.2."}, {"text": "For the instruction-following task, we report the win rates of EXO compared to various baselines in Figure 4 under the setting of learning from the reward model given its advantageous performance observed so far. From the result, we observe that EXO outperforms all baselines by clear margins, thereby underscoring its scalability in practical applications. Notably, EXO achieves 10% and 5% improvement over its closest competitors as judged by GPT-4 and human annotators respectively. ", "section": "Alignment with Human Preferences", "sec_num": "4.2."}, {"text": "Large language models (<PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2023) learned to predict the next tokens on large corpora have significantly improved the performance of various natural language processing (NLP) tasks in zero shot (<PERSON><PERSON> et al., 2019) or few-shot (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2023) settings. To bridge the mismatch between the training objective and users' objective of solving specific NLP tasks, instruction-tuning is proposed, which fine-tunes the language models on human curated instruction-response pairs in a fully supervised manner (<PERSON><PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) . Despite its performance and gen-eralization to unseen instruction sets (<PERSON> et al., 2022) , there have been concerns that the model learned to predict the next token might only capture surficial patterns rather than comprehending the tasks (<PERSON> & <PERSON>g, 2023) .", "section": "Related Work", "sec_num": "5."}, {"text": "To address the aforementioned deficiencies, the framework of reinforcement learning from human feedback (RLHF) is proposed (<PERSON><PERSON> et al., 2019) , which relies on only relative human preferences on response quality and optimizes the language model by RL algorithms (<PERSON>, 1992 ), e.g., PPO (<PERSON><PERSON><PERSON> et al., 2017) , under the supervision of a reward model which encapsulates the implicit preference of humans. Despite the success of RLHF in various tasks where alignment is strongly emphasized, e.g., translation (<PERSON><PERSON><PERSON><PERSON> et al., 2018) , summarization (<PERSON><PERSON><PERSON> et al., 2020) , instruction following (<PERSON><PERSON><PERSON> et al., 2022) , and etc., RL fine-tuning for large language models still faces serious challenges in stability and scalability (<PERSON> et al., 2023b) .", "section": "Related Work", "sec_num": "5."}, {"text": "Thereby, a new trend of recent works proposed to optimize the language model to follow human preferences without applying RL algorithms (<PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2023) . While most approaches are empirically set to model alignment as a re-ranking problem, DPO proposed a theoretically sound way to realize direct policy optimization from preference data based on the analytic optimal solution of the reward maximization objective in RLHF. Afterwards, there have been several studies that extend DPO in various ways, e.g., altering the preference data by sampling from the optimal policy via rejection sampling (<PERSON> et al., 2023) , substituting the point-wise reward with a pairwise preference function (<PERSON><PERSON> et al., 2023) , extending the preference pairs to rankings of preferences of any size (<PERSON> et al., 2023) , and etc. However, these approaches are still based on the formation of DPO. In this work, we propose to learn the policy by exactly optimizing the RLHF objective via probability matching that minimizes the reverse KL.", "section": "Related Work", "sec_num": "5."}, {"text": "In the literature of maximum-entropy RL (<PERSON><PERSON><PERSON> & Levine, 2019; <PERSON><PERSON><PERSON> et al., 2022) , this equivalent form of probability matching was discussed only for analysis purposes, while we are the first to derive a practical way to optimize it. We also revisit DPO under this perspective and recognize that it actually corresponds to minimizing the forward KL in its general form. To this sense, DPO shares the same spirit of weighted regression (<PERSON>, 2007; <PERSON> et al., 2019; <PERSON> et al., 2020) , an algorithm that directly utilizes behavioral actions to supervise the policy in offline RL (<PERSON> et al., 2010; <PERSON> et al., 2023) . However, this approach is known to be suboptimal when the policy model is limited in distributional expressivity (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2023) . We analyze the characteristics of the probability density learned by DPO with both theoretical insight and empirical experimentations.", "section": "Related Work", "sec_num": "5."}, {"text": "The alignment framework proposed by <PERSON><PERSON> et al. (2019) relies on the KL regularization to the SFT policy to prevent the optimized policy from greedily maximizing out the reward model which is estimated from the human preference data. Despite the regularization, our experiments still reveal instances of reward over-optimization, a phenomenon possibly due to insufficient focus on the reward model estimation and use. Importantly, ensuring that the reward model accurately reflects the true modes of the oracle distribution of human is more vital for PPO and EXO that optimizes a mode-seeking objective. This opens possible avenues to improve the alignment framework at a broader scope. For instance, rather than relying on a static preference dataset for reward model training, it could be more effective to dynamically improve the reward model with the development of the policy, thereby offering more precise feedback. Upon the current regularization that solely focuses on proximity to the initial policy, one can take into account the uncertainty of the reward model output to avoid over-exploitation of the reward model. It is beneficial to take into account these aspects to develop efficient and effective method towards closer alignment with human preferences. Additionally, while we already evaluated EXO on advanced language models up to 6B on realistic scenarios, scaling EXO to models that are orders of magnitude larger can present profound implications. At the other end of the spectrum, systematically dissecting and comparing PPO, DPO and EXO, e.g., regarding their variance and bias during optimization is essential to broaden our understanding of these methods.", "section": "Limitations and Future Work", "sec_num": "6."}, {"text": "In this work, we consider the problem of aligning language models with human preferences. Although reinforcement learning (RL) for reward maximization presents the direct and apparent solution, we reframe the problem in a supervised probability matching framework, which underscores the probabilistic interpretation of the alignment procedure. This derives our efficient exact optimization (EXO) of the KL-regularized reward maximization objective of alignment. Formally, we prove the asymptotic equivalence between the EXO objective and the alignment objective. In practice, EXO enables efficient optimization via probability matching between empirical distributions, which avoids the complexities of RL algorithms. We further demonstrate that DPO in its general form actually corresponds to minimizing the forward KL against the optimal policy, which is shown to be less effective in capturing the modes of the optimal policy under realistic model parametrization with both theoretical and empirical justifications. Finally, we demonstrate the effectiveness and scalability of EXO on various text generation tasks with real human preferences.", "section": "Conclusion", "sec_num": "7."}, {"text": "This paper presents a method whose goal is to advance the alignment of language models with human preferences. This endeavor, while technically challenging, carries significant implications for the ethical use and societal impact of artificial intelligence. The goal of alignment aims to mitigate the inherent biases of AI systems and ensure that they reflect diverse human values, goals and intentions that are safe and ethical. It enhances AI's utility in various sectors to make reliable decisions that are in line with organizational goals and ethical standards.", "section": "Impact Statement", "sec_num": null}, {"text": "A.1. <PERSON><PERSON> of Theorem 3.1", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "Proof. We derive the optimal π θ * that maximizes the generalized alignment objective J βr lhf (π βπ θ ) and show that it equals to the optimal solution π * β of J β lhf (π θ ) given unlimited model capacity. First, we restate the formation of J βr lhf (π βπ θ ):", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "where π βπ θ is defined as:", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": ")", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "Then we substitute θ with the optimal θ * in Eq. ( 20) where π θ * maximizes J βr lhf (π βπ θ ):", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": ")", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "Since π βπ θ * is also the optimal policy that maximizes J βr lhf (•), it should satisfy Eq. ( 4) which gives:", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": ")", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "Together with Eq. ( 21) and <PERSON>q. ( 22), we obtain the form of π θ * via some simple algebra: r(x,y) .", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "π θ * (y|x) ∝ (π * βr (y|x)π sft (y|x) βπ-1 ) 1 βπ ∝ (π sft (y|x)e 1 βr r(x,y) ) 1 βπ π sft (y|x) βπ -1 βπ ∝ π sft (y|x) 1 βπ e 1 βr βπ r(x,y) π sft (y|x) βπ -1 βπ ∝ π sft (y|x)e 1 βr βπ", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "By definition, β r β π = β, then π θ * reduces to the same form of the optimal solution of J β lhf (π θ ) defined in Eq. ( 4):", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "π θ * (y|x) = π * β (y|x) ∝ π sft (y|x)e 1 β r(x,y) ,", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "which completes the proof.", "section": "<PERSON><PERSON> Proofs and Derivations", "sec_num": null}, {"text": "We first start by rearranging J βr lhf (π βπ θ ) into the expectation of a log ratio:", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "J βr lhf (π βπ θ ) = E x∼D pref E π βπ θ (y|x) [r ϕ (x, y)] -β r D KL [π βπ θ (y|x)∥π sft (y|x)] = E x∼D pref E π βπ θ (y|x) [r ϕ (x, y)] -β r E π βπ θ (y|x) log π βπ θ (y|x) π sft (y|x) = E x∼D pref β r E y∼π βπ θ (y|x) log e 1 βr r ϕ (x,y) -β r E π βπ θ (y|x) log π βπ θ (y|x) π sft (y|x) = E x∼D pref E y∼π βπ θ (y|x) β r log π sft (y|x)e 1 βr r ϕ (x,y) π βπ θ (y|x)", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "Notice the analytical form of π * βr :", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "π * βr (y|x) = 1 Z βr(x)", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "π sft (y|x)e 1 βr r ϕ (x,y) .", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "We substitute π sft (y|x)e 1 βr r ϕ (x,y) into the expression of J βr lhf (π βπ θ ):", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "J βr lhf (π βπ θ ) = E x∼D pref E y∼π βπ θ (y|x) β r log Z βr(x) π * βr (y|x) π βπ θ (y|x) = β r E x∈D pref -D KL (π βπ θ (•|x)∥π * βr (•|x)) + log Z βr (x) .", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "A.3. <PERSON><PERSON> of Theorem 3.2", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "Proof. We first restate the definition of L exo (π θ ) by substituting Eq. ( 10), ( 12) into ( 13):", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "EQUATION", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "Since f θ (x, y) = log π βπ θ (y|x)log π sft (y|x), we have that:", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "L exo (π θ ) = E x∼D pref E πsft(y 1:K |x) K i=1 e log π βπ θ (y i |x) π sft (y i |x) K j=1 e log π βπ θ (y j |x) π sft (y j |x) log e log π βπ θ (y i |x) π sft (y i |x) K j=1 e log π βπ θ (y j |x) π sft (y j |x) -log e 1 βr r ϕ (x,y i ) K j=1 e 1 βr r ϕ (x,y j ) = E x∼D pref E πsft(y 1:K |x) K i=1 π βπ θ (y i |x) πsft(y i |x) K j=1 π βπ θ (y j |x) πsft(y j |x) log π βπ θ (y i |x) πsft(y i |x) K j=1 π βπ θ (y j |x) πsft(y j |x)", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "log e", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "EQUATION", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "Since ) ]. We consider the following two instantiations of g(•).", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "{y i } K i=1 are sampled from π sft (•|x), when K → ∞, for arbitary function g : Y → R, the estimate 1 K K i=1 g(y i ) is unbiased, i.e., lim K→∞ 1 K K i=1 g(y i ) = E πsft(y|x) [g(y", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "For g(y) = π βπ θ (y|x)", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "πsft(y|x) , we have:", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "K j=1 π βπ θ (y j |x) π sft (y j |x) = KE πsft(y|x) π βπ θ (y|x) π sft (y|x) = K y∈Y π sft (y|x) π βπ θ (y|x) π sft (y|x) = K y∈Y π βπ θ (y|x) = K.", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "For g(y) = e 1 βr r ϕ (x,y) , we have:", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "K j=1 e 1 βr r ϕ (x,y j ) = KE πsft(y|x) e 1 βr r ϕ (x,y) = K y∈Y π sft (y|x)e 1 βr r ϕ (x,y) = KZ βr (x).", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "Then we simplify L exo by substituting the expression of", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "K j=1 π βπ θ (y j |x)", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "πsft(y j |x) and K j=1 e 1 βr r ϕ (x,y j ) when K → ∞ into Eq. ( 24).", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "L exo (π θ ) = E x∼D pref E πsft(y 1:K |x) K i=1 π βπ θ (y i |x) Kπ sft (y i |x) log π βπ θ (y i |x) Kπ sft (y i |x) -log e 1 βr r ϕ (x,y i ) KZ βr (x) = E x∼D pref E πsft(y 1:K |x) 1 K K i=1 π βπ θ (y i |x) π sft (y i |x) log π βπ θ (y i |x) π sft (y i |x)e 1 βr r ϕ (x,y i ) /Z βr (x) .", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "Notice the analytic form of π * βr (y|x) = 1 Z βr (x) π sft (y|x)e 1 βr r ϕ (x,y) , we substitute π * βr into the above equation:", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "L exo (π θ ) = E x∼D pref E πsft(y 1:K |x) 1 K K i=1 π βπ θ (y i |x) π sft (y i |x) log π βπ θ (y i |x) π * βr (y i |x) = E x∼D pref 1 K K i=1 E πsft(y i |x) π βπ θ (y i |x) π sft (y i |x) log π βπ θ (y i |x) π * βr (y i |x) = E x∼D pref 1 K K i=1 y i ∈Y π βπ θ (y i |x) log π βπ θ (y i |x) π * βr (y i |x) = E x∼D pref y∈Y π βπ θ (y|x) log π βπ θ (y|x) π * βr (y|x) = E x∼D pref [D KL (π βπ θ (y|x)∥π * βr (y|x))].", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "Accordingly, by taking the derivative with respect to θ, we complete the proof of Eq. ( 14).", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "To prove Eq. ( 15), we utilize Eq. ( 14) to substitute into Eq. ( 9) to build the connection between J βr lhf (π βπ θ ) and L exo :", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "J βr lhf (π βπ θ ) = -β r L exo (π θ ) + β r E x∼D pref [log Z βr (x)].", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "Then we take the gradient with respect to the parameters θ of the above formulat:", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "∇ θ J βr lhf (π βπ θ ) = -β r ∇ θ L exo (π θ ),", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "which completes the proof of Eq. (15).", "section": "A.2. Derivation of Eq. (9)", "sec_num": null}, {"text": "Proof. We utilize the definition of π βπ θ (y|x) ∝ π θ (y|x) βπ π sft (y|x) 1-βπ in Eq. ( 8) and divide both sides by π sft (y|x):", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "π βπ θ (y|x) π sft (y|x) ∝ π θ (y|x) π sft (y|x)", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "βπ Then we substitute the above equation into L dpo-rw :", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "EQUATION", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "Since {y i } K i=1 are sampled from π sft (•|x), when K → ∞, we follow the proof of Theorem 3.2 and directly give the following results:", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "K j=1 π βπ θ (y j |x) π sft (y j |x) = K, K j=1 e 1 βr r ϕ (x,y j ) = KZ βr (x).", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "Towards Efficient Exact Optimization of Language Model Alignment", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "Then we simplify L dpo-rw by substituting the above results of", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "K j=1 π βπ θ (y j |x)", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "πsft(y j |x) and K j=1 e 1 βr r ϕ (x,y j ) when K → ∞ into Eq. ( 25):", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "L dpo-rw (π θ ) = E x∼D pref E πsft(y 1:K |x) - K i=1 e 1 βr r ϕ (x,y i ) KZ βr (x) log π βπ θ (y i |x) Kπ sft (y i |x)", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "Notice the analytic form of π * βr (y|x) = 1 Z βr (x) π sft (y|x)e 1 βr r ϕ (x,y) , we rearrange π * βr and substitute π sft (y|x) = π * βr (y|x)Z βr (x)e -1 βr r ϕ (x,y) into the above equation to simplify it:", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "L dpo-rw (π θ ) = E x∼D pref E πsft(y 1:K |x) - K i=1 e 1 βr r ϕ (x,y i ) KZ βr (x) log π βπ θ (y i |x) π * βr (y i |x) • e 1 βr r ϕ (x,y i ) KZ βr (x) = E x∼D pref E πsft(y 1:K |x) - K i=1 e 1 βr r ϕ (x,y i ) KZ βr (x) log π βπ θ (y i |x) π * βr (y i |x) - K i=1 e 1 βr r ϕ (x,y i ) KZ βr (x) log e 1 βr r ϕ (x,y i ) KZ βr (x)", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "The second term of the final equality remains constant in relation to θ, and thus can be omitted when computing the derivative with respect to θ. Then we further consider the gradient of L dpo-rw :", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "∇ θ L dpo-rw (π θ ) = ∇ θ E x∼D pref - 1 K K i=1 E πsft(y i |x) e 1 βr r ϕ (x,y i ) Z βr (x) log π βπ θ (y i |x) π * βr (y i |x) = ∇ θ E x∼D pref -E πsft(y|x) e 1 βr r ϕ (x,y) Z βr (x) log π βπ θ (y|x) π * βr (y|x) = ∇ θ E x∼D pref - y∈Y π sft (y|x) e 1 βr r ϕ (x,y) Z βr (x) log π βπ θ (y|x) π * βr (y|x) = ∇ θ E x∼D pref - y∈Y π * βr (y|x) log π βπ θ (y|x) π * βr (y|x) = ∇ θ E x∼D pref D KL (π * βr (•|x)∥π βπ θ (•|x)) ,", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "which completes the proof of Theorem 3.3.", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "A.5. Mechanistic Understanding of ∇ θ L exo (π θ )", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "We present the gradient of L exo (π θ ) defined in Eq. ( 13):", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "EQUATION", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "where p f θ (k) and p r ϕ (k) are short for p f θ (k|y 1:K , x) and p r ϕ (k|y 1:K , x), which are defined in Eq. ( 11) and Eq. ( 12) respectively.", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "Next, we provide a mechanistic understanding of ∇ θ L exo (π θ ), which is the expected weighted sum of the gradients of the log likelihood on samples y 1:K drawn from π sft (•|x). The weight is proportional to the difference between the log probability ratio log", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "p f θ (k)", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "pr ϕ (k) and the KL divergence D KL (p f θ ∥p r ϕ ). Intuitively, if the policy has already correctly weighted the sample y k according to the reward model, i.e., p r ϕ (k) = p f θ (k), then log", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "p f θ (k)", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "pr ϕ (k) = 0 and the weight suggests that it only needs to minimize the overall KL divergence between p f θ and p r ϕ on the distribution level. If the policy π θ overestimates or underestimates the sample y k , i.e., log", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "p f θ (k) pr ϕ (k) > 0 or log p f θ (k)", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "pr ϕ (k) < 0, this log-ratio will be used to calibrate the KL divergence to penalize or encourage the policy to update towards increasing the likelihood of this sample at a faster rate.", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "Finally, this gradient form offers us the insight of when the optimization should stop: the gradient ∇ θ L exo (π θ ) becomes 0 when the two distribution p f θ and p r ϕ are identical.", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "A.6. Illustrating the Relationship among the Objectives in §3", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "We illustrate the relationship among the objectives J β lhf (π θ ), J βr lhf (π βπ θ ), L dpo (π θ ), L dpo-rw (π θ ) and L exo (π θ ) in Figure 5 . ", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "i + x + o k K y u 3 q l F Q 5 M S z y u W M 4 K V d a U b 3 h t U Y l U Q z P V L k 2 q k 6 J H S v M i N e a 9 R R h U 2 A W p Y i l R h 8 R g + h V 1 6 l u n n L j u r + U w u S n v p I 4 M k K + F K b c c J O D V R a t u R F T R", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "i + x + o k K y u 3 q l F Q 5 M S z y u W M 4 K V d a U b 3 h t U Y l U Q z P V L k 2 q k 6 J H S v M i N e a 9 R R h U 2 A W p Y i l R h 8 R g + h V 1 6 l u n n L j u r + U w u S n v p I 4 M k K + F K b c c J O D V R a t u R F T R", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "V p W f 1 a K m c Y H n J c s Y w c p C y U 5 v i Q q s c o K 5 f m 8 S j R Q 9 V 5 r n m T F f N E q p w o k w A a p Z g l R u X 7 9 B b S F j R v A 1 X N H T V L 9 1 7 L T i M 7 k o 7 K X P D Z K s g G v 1 Q y f o 1 E W h b Y U 2 g U E H b B 5 0 B f 6 S K L i s v D D L b q K R m Y o E 1 T k L u v i T L", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "J r lhf (⇡ ⇡ ✓ ) = E x⇠D pref ⇣ E ⇡ ⇡ ✓ (y|x) [r (x, y)] r D KL [⇡ ⇡ ✓ (y|x)k⇡ sft (y|x)]", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "⌘ . ", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "E x⇠D pref ⇣ E ⇡ ⇡ ✓ (y|x) [r (x, y)] r D KL [⇡ ⇡ ✓ (y|x)k⇡ sft (y|x)] ⌘ . < l a t e x i t s h a 1 _ b a s e 6 4 = \" G 2 b / 9 O l M / D W Z O 3 v f g t T 6 D W S 9 D E M = \" > A A A D f H i c n V J d b 9 M w F H U T P k b 5 6 u C R B y w K U i p o l S A 2 e E G a B k g 8 8 D A k u k 2 q Q + Q 4 T m r N j i P b g V X B f 4 K f x h s / h R e E k 1 V l a 5 E Q X C n K z b n n 6 p x 7 c 9 O K M 2 3 C 8 H v P 8 y 9 d v n J 1 6 1 r / + o 2 b t 2 4 P t u 8 c a l k r Q q d E c q m O U 6 w p Z y W d G m Y 4 P a 4 U x S L l 9 C g 9 e d X W j z 5 R p Z k s P 5 h F R W O B i 5 L l j G D j o G S 7 9 x U J b O Y E 8 + a d T R p k 6 K l R o s k q a W 2 A K p Y g M 6 c G j + D L j p e m z R t H C 1 A q e a Y X w r 2 a U / v k / O f C J p / X A T 5 C m g m 4 k n p t P 6 6 k n N 3 c W g v 7 K G V F M Y N j x G X h 6 I X A a J 8 V A U S p M 9 C C E O U K k + a 3 q 2 B d 9 8 t F X y P b k V d S O j f t V H 9 r g u N / 0 O T / o 7 n Z 1 I 4 6 6 j Y Q T 5 L B M J y E X c D N J F o m Q 7 C M g 2 T w D W W S 1 I K W h n C s 9 S w K K x M 3 W B l G O L V 9 V G t a Y X K C C z p z a Y k F 1 X H T H Y + F j x y S w V w q 9 5 Q G d u j 5 j g Y L 3 R p 1 z P b 3 6 f V a C / 6 p N q t N / i J u W F n V h p b k T C i v O T Q S t p c I M 6 Y o M X z h E k w U c 1 4 h m W O 3 b e P u t e + W E K 2 P v J k c P p 1 E O 5 P w / b P h 3 v 5 y H V v g H n g A A h C B 5 2 A P v A U H Y A p I 7 4 d 3 3 w u 8 k f f T f + g / 9 s d n V K + 3 7 L k L L o S / + w v v A S y 6 < / l a t e x i t > L dpo (⇡ ✓ ) = E (x,y w ,y l )⇠D pref  log ⇣ log ⇡ ✓ (y w |x) ⇡ sft (y w |x) log ⇡ ✓ (y l |x) ⇡ sft (y l |x) ⌘ < l a t e x i t s h a 1 _ b a s e 6 4 = \" G 2 b / 9 O l M / D W Z O 3 v f g t T 6 D W S 9 D E M = \" > A A A D f H i c n V J d b 9 M w F H U T P k b 5 6 u C R B y w K U i p o l S A 2 e E G a B k g 8 8 D A k u k 2 q Q + Q 4 T m r N j i P b g V X B f 4 K f x h s / h R e E k 1 V l a 5 E Q X C n K z b n n 6 p x 7 c 9 O K M 2 3 C 8 H v P 8 y 9 d v n J 1 6 1 r / + o 2 b t 2 4 P t u 8 c a l k r Q q d E c q m O U 6 w p Z y W d G m Y 4 P a 4 U x S L l 9 C g 9 e d X W j z 5 R p Z k s P 5 h F R W O B i 5 L l j G D j o G S 7 9 x U J b O Y E 8 + a d T R p k 6 K l R o s k q a W 2 A K p Y g M 6 c G j + D L j p e m z R t H C 1 A q e a Y X w r 2 a U / v k / O f C J p / X A T 5 C m g m 4 k n p t P 6 6 k n N 3 c W g v 7 K G V F M Y N j x G X h 6 I X A a J 8 V A U S p M 9 C C E O U K k + a 3 q 2 B d 9 8 t F X y P b k V d S O j f t V H 9 r g u N / 0 O T / o 7 n Z 1 I 4 6 6 j Y Q T 5 L B M J y E X c D N J F o m Q 7 C M g 2 T w D W W S 1 I K W h n C s 9 S w K K x M 3 W B l G O L V 9 V G t a Y X K C C z p z a Y k F 1 X H T H Y + F j x y S w V w q 9 5 Q G d u j 5 j g Y L 3 R p 1 z P b 3 6 f V a C / 6 p N q t N / i J u W F n V h p b k T C i v O T Q S t p c I M 6 Y o M X z h E k w U c 1 4 h m W O 3 b e P u t e + W E K 2 P v J k c P p 1 E O 5 P w / b P h 3 v 5 y H V v g H n g A A h C B 5 2 A P v A U H Y A p I 7 4 d 3 3 w u 8 k f f T f + g / 9 s d n V K + 3 7 L k L L o S / + w v v A S y 6 < / l a t e x i t > L dpo (⇡ ✓ ) = E (x,y w ,y l )⇠D pref  log ⇣ log ⇡ ✓ (y w |x) ⇡ sft (y w |x) log ⇡ ✓ (y l |x) ⇡ sft (y l |x) ⌘ . º µRKL º § Ø º µFKL º µRKL º Ø º µFKL º µRKL º § Ø º µFKL Optimal policy Solution 1 2 3 < l a t e x i t s h a 1 _ b a s e 6 4 = \" b y Z F M d W D b w 0 j E A 2 H r Y h V k X x p G S w = \" > A A A B / X i c b V D L S s N A F J 3 4 r P U V H z s 3 w S K 4 K o k o u i y 6 c e G i g n 1 A G 8 p k O k m H T m b C z I 1 S Q / F X 3 L h Q x K 3 / 4 c 6 / c d J m o a 0 H L h z O u Z d 7 7 w k S z j S 4 7 r e 1 s L i 0 v L J a W i u v b 2 x u b d s 7 u 0 0 t U 0 V o g 0 g u V T v A m n I m a A M Y c N p O F M V x w G k r G F 7 l f u u e K s 2 k u I N R Q v 0 Y R 4 K F j G A w U s / e 7 9 5 I E X E a g m L R A L B S 8 q H c s y t u 1 Z 3 A m S d e Q S q o Q L 1 n f 3 X 7 k q Q x F U A 4 1 r r j u Q n 4 G V b A C K f j c j f V N M F k i C P a M V T g m G o / m 1 w / d o 6 M 0 n d C q U w J c C b q 7 4 k M x 1 q P 4 s B 0 x h g G e t b L x f + 8 T g r h h Z 8 x k a R A B Z k u C l P u g H T y K J w + U 5 Q A H x m C i W L m V o c M s M I E T G B 5 C N 7 s y / O k e V L 1 z q r u 7 W m l d l n E U U I H 6 B A d I w + d o x q 6 R n X U Q A Q 9 o m f 0 i t 6 s J + v F e r c + p q 0 L V j G z h / 7 A + v w B t E y V Y Q = = <", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "R F F o U M m l a g d E A 2 c C G o Y Z D u 1 Y A Y k C D q 1 g f J 3 5 r Q d Q m k l x b y Y x 9 C I y F C x k l B g r 9 d 1 y 9 1 a K I Y f Q E K X k Y 6 n v V r y q N w N e J n 5 O K i h H v e 9 + d Q e S J h E I Q z n R u u N 7 s e m l R B l G O U x L 3 U R D T O i Y D K F j q S A R 6 F 4 6 O 3 y K j 6 0 y w K F U t o T B M / X 3 R E o i r S d R Y D s j Y k Z 6 0 c v E / 7 x O Y s L L X s p E n B g Q d L 4 o T D g 2 E m c p 4 A F T Q A 2 f W E K o Y v Z W T E d E E W p s V l k I / u L L y 6 R 5 W v X P q 9 7 d W a V 2 l c d R R I f o C J 0 g H 1 2 g G r p B d d R A F C X o G b 2 i N + f J e X H", "section": "A.4. <PERSON><PERSON> of Theorem 3.3", "sec_num": null}, {"text": "Training. In the controlled text generation task, we use β π = 0.1 for EXO pref and DPO pref , and additionally use β r = 0.1 and K ∈ {4, 8} for EXO rw and DPO rw . For the tasks of summarization, dialogue generation, and instruction following, we use β π = 0.5 for EXO pref and DPO pref , and additionally use β r = 0.1 and K = 4 for EXO rw and DPO rw . We provide additional results of tuning β r and β π in Appendix C.1 to justify our choice of hyperparameters. In all experiments, we set the label smoothing hyperparameter ε in EXO pref to 1e-3. For DPO and EXO, we use the Adam optimizer with a universal learning rate of 1e-6 and a batch size of 64 and train for one epoch on each dataset, although both methods converge within one epoch. All the hyperparameters are set to be the same for DPO and EXO for a fair comparison. For PPO, we modify based on the implementation of DeepSpeed-Chat (Yao et al., 2023) , which sets γ = 1 and β = 0.1 by default. We pretrain the critic model for the first few steps while freezing the actor model and find it to improve convergence. We empirically tune the number of actor-freezing steps, total training steps, learning rate for actor and critic model, and the batch size for PPO on each dataset, as PPO is sensitive to these hyperparameters. Specifically, we conduct 15 trials of hyperparameter search on the IMDB dataset and 10 trials on the real human preference datasets in total. In the experiment, we report the PPO performance with the best hyperparameters obtained under constrained number of hyperparameter search trials. We conduct the experiments except for instruction following on 8 V100 GPUs. For instruction following task, we train the models on 8 A100 GPUs. Estimated density ratio of the EXO, DPO and optimal policy given the prompt \"Is this supposed to be serious? I hope not\".", "section": "B. Training and Evaluation Details", "sec_num": null}, {"text": "-1200 -1000 -800 -600 -400 -200 0 log π sft (y|x) 0 50 100 optimal EXO DPO Estimated density ratio of the EXO, DPO and optimal policy given the prompt \"Great book, great movie, great soundtrack.", "section": "B. Training and Evaluation Details", "sec_num": null}, {"text": "<PERSON>\".", "section": "B. Training and Evaluation Details", "sec_num": null}, {"text": "-1200 -1000 -800 -600 -400 -200 Estimated density ratio of the EXO, DPO and optimal policy given the prompt \"Once the slow beginning gets underway, the film kicks\".", "section": "B. Training and Evaluation Details", "sec_num": null}, {"text": "Figure 9 . Visualization of the estimated density ratio of the policy learned by EXO and DPO, and the optimal policy given 10 test prompts randomly sampled from the IMDb dataset.", "section": "B. Training and Evaluation Details", "sec_num": null}, {"text": "https://huggingface.co/datasets/imdb.", "section": "", "sec_num": null}, {"text": "https://huggingface.co/datasets/UCL-DARK/ openai-tldr-filtered.", "section": "", "sec_num": null}, {"text": "https://huggingface.co/datasets/openai/ summarize_from_feedback.", "section": "", "sec_num": null}, {"text": "https://huggingface.co/datasets/Anthropic/ hh-rlhf.", "section": "", "sec_num": null}, {"text": "We consider the setting of learning directly from preferences.", "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by the NSFC projects (with No. 61936010 and No. 62306160). This work was supported by the National Science Foundation for Distinguished Young Scholars (with No. 62125604). This work was also supported by China National Postdoctoral Program for Innovative Talents (No. BX20230194) and China Postdoctoral Science Foundation (No. 2023M731952). We would also like to thank Zhipu AI for sponsoring the computation resources and annotation cost used in this work.", "section": "Acknowledgements", "sec_num": null}, {"text": "Evaluation. At inference time, we sample 4 completions from the learned policy for each prompt and consider 512 prompts from the test set for all datasets. Except for the instruction following task, we sample from the policy with the same temperature τ = 0.8 that is set during training for consistency. For the instruction following task, we use top-p sampling and empirically set p = 0.9 and temperature τ = 0.95 given its performance. To calculate the win rate evaluated by the reward model, we consider all combinations of pairs between the completions generated by the learned policy and the base completions (either generated by the SFT policy or the chosen completion in the dataset) and then compare the scores from the reward model on the pairs of generations. For the evaluations using GPT-4, we sample 100 prompts and 1 completion for each prompt under each policy. To mitigate the position bias of GPT-4, we evaluate one pair of generations twice by swapping the order of responses in each pair. To evaluate the quality of the summaries, we use the concise prompt of <PERSON><PERSON><PERSON><PERSON> et al. (2023) as shown in Table 3 . To evaluate the helpfulness of the generated dialogues, we use the prompt shown in Table 4 , which is modified based on the prompt of <PERSON><PERSON><PERSON><PERSON> et al. (2023) for single-turn dialogue to accommodate the general multi-turn setting. For the instruction-following task, we use the prompt modified from the prompt for reference-guided pairwise comparison provided in <PERSON> et al. (2023a) .Human Assessment. We conduct human assessment to evaluate the instruction following task more thoroughly. Specifically, we select three matchups that pair the generated outputs of EXO with those produced by DPO, PPO and the SFT policy. Given 100 randomly sampled test instructions, each model generates 100 responses, which results in a total of 300 pairs of comparisons. We assign 3 human labelers to each comparison, producing 900 judgements in total. Given the instruction, each human annotator is provided with two generated answers by two systems respectively together with a high-quality reference answer. The annotator is then asked to make a preference among win, tie or lose by comparing the generated answers with the reference answer, considering the criteria including adherence to instruction, correctness, fluency, safety and helpfulness. Specifically, adherence to instruction encapsulates the model's comprehension and following of the prompt's intention. Correctness involves the identification of inaccurate knowledge or logical inconsistencies within the generated responses. Fluency assesses the linguistic coherence, encompassing an examination of sentence completeness, grammatical accuracy, and the presence of a consistent language structure. Safety refers to the inspection for potentially harmful content. Lastly, helpfulness indicates whether the responses provide the information required by the prompt or contribute to problem resolution.Which of the following summaries does a better job of summarizing the most important points in the given forum post, without including unimportant or irrelevant details? A good summary is both precise and concise.", "section": "annex", "sec_num": null}, {"text": "Summary A: <Summary A> Summary B: <Summary B> FIRST provide a one-sentence comparison of the two summaries, explaining which you prefer and why. SECOND, on a new line, state only \"A\" or \"B\" to indicate your choice. Your response should use the format: Comparison: <one-sentence comparison and explanation> Preferred: <\"A\" or \"B\"> Table 3 . Prompt for GPT-4 evaluation on the summarization task. Texts in blue are placeholders to be substituted by the real data.", "section": "Post: <post>", "sec_num": null}, {"text": "We present an ablation study to investigate the performance of EXO rw on the dialogue generation task by varying β r and β π respectively. We execute multiple runs bifurcated into two series. We set β π = 0.5 as the default value and vary β r ∈ {0.1, 0.25, 0.5, 0.75, 1.0}. Subsequently, the process is reversed whereby we fix β r = 0.1, and alter β π ∈ {0.1, 0.25, 0.5, 0.75, 1.0}. We present the results in Figure 6 . 4 . Prompt for GPT-4 evaluation on the dialogue generation task. Texts in blue are placeholders to be substituted by the real data. From Figure 6 (a), we observe that reducing either β r and β π increase the reward model win rate, which is expected as a small β r × β π encourages the policy to optimize the reward model while neglecting the KL regularization. In Figure 6 (b), the GPT-4 win rate starts to decline when β π < 0.5, which suggests where the reward model starts to be over-optimized. The different effects of tuning β π and β r on the performance could be attributed to their different roles in our algorithm, i.e., β π scales the log probability in the parametrized policy while β r scales the reward. Based on the results, we recommend adopting a moderate value for β π and a lower value for β r , for instance, β π = 0.5 and β r = 0.1.", "section": "C.1. Ablation Study of β r and β π", "sec_num": null}, {"text": "To further demonstrate the effect of β r and β π beyond the impact on their product β = β π β r , we fix β = 0.05 while tuning β π ∈ {1, 0.5, 0.1, 0.05} and β r ∈ {0.05, 0.1, 0.5, 1} accordingly. The result is shown in Table 5 . According to the evaluation by GPT-4, the best performnce is obtained at β π = 0.5, β r = 0.1, while neither the hyper-parameter choice of β π = 1, β r = 0.05 (the configuration of PPO) nor the choice of β π = 0.05, β r = 1 (the configuration of DPO) yield the highest win rate evaluated by GPT-4. While using a small β π is more likely to trigger over-optimization of the reward model, which indicates the asymmetric effect of the two hyperparameters β r and β π on the optimization process. ", "section": "C.2. Effect of β r and β", "sec_num": null}, {"text": "We additionally calculate the accuracy of the sentiment classifier by taking the sigmoid of the oracle reward and plot the frontier of the accuracy vs reverse KL in Figure 7 . ", "section": "C.3. Frontier of Classifier Accuarcy against KL Divergence", "sec_num": null}, {"text": "To demonstrate the efficency of optimizing the oracle reward, we plot the curve of the oracle reward with standard deviation on the test set of different training steps. Specifically, the standard deviation is calculated on the 4 samples generated given the same prompt and then averaged across 512 prompts. ", "section": "C.4. Optimization Efficiency", "sec_num": null}, {"text": "We additionally sample more prompts from the test set of the IMDb dataset, and plot the estimated density ratio of the policy learned by EXO and DPO and the optimal policy in Figure 9 . Similar results were obtained among these diverse set of prompts: the density ratio under the EXO policy better matches the optimal policy at the high probability region against π sft than the DPO policy. And very appearently, the DPO policy tends to overestimate the long tail in π sft and thus often loses resolution in capturing the modes of the optimal policy.", "section": "C.5. Additional Visualization Results", "sec_num": null}, {"text": "We present the sample outputs generated by EXO and DPO and the judgments made by GPT-4 when compared to the chosen response in the preference dataset. Table 6 and Table 7 show examples on the task of dialogue generation and summarization respectively.", "section": "C.6. Sample Outputs with GPT-4 Judgments", "sec_num": null}, {"text": "Human: How do I make a fishtail braid? Assistant: Hey human, just walk down the left and then down the right side, then at the end, you pull through. Are you looking to just braid, or a tight ponytail? Human: Yes. Can you tell me the steps to do it?", "section": "Dialogue history", "sec_num": null}, {"text": "Assistant: Sure, here's how to do a side braid:1) Pull hair up to the back of your head, leaving it out from the rest of your hair so you can see it.2) Put one hair on the side where you want to make your braid.3) Hold the braid with your thumb and forefinger 4) Bring your braid through the opposite side (putting the hair to the other side of the braid. 5) Repeat steps 2, 3, and 4 as needed until you've completed your braid.", "section": "Chosen response", "sec_num": null}, {"text": "Assistant: Sure! I'll walk you through it step by step:1. Stand with your left hand on the back of your head. Grab a left side section of hair, using the right hand to pull it through. 2. Take a right side section of hair, and pull it through 3. <PERSON>rab a left side section of hair, and pull it through 4. <PERSON>rab a right side section of hair, and pull it through 5. Repeat steps 2-4 for the last section of hair 6. Wrap the hair around your head and secure it at the nape of your neck.", "section": "EXO response", "sec_num": null}, {"text": "Response A provides a more detailed and step-by-step guide on how to make a fishtail braid, while Response B seems to describe a different type of braid and is less clear.", "section": "GPT-4 Judgment (A = EXO, B = Chosen)", "sec_num": null}, {"text": "Response B is more helpful because it provides a clearer, step-by-step guide on how to make a fishtail braid, whereas Response A seems to describe a different type of braid and is less clear.", "section": "GPT-4 Judgment (A = Chosen, B = EXO)", "sec_num": null}, {"text": "Assistant: Sure, let's walk through it. You'll want to start with a braid about two and a half inches long, then you want to braid it together and pull through, which means you'll somehow thread it through your own hair and tie it off at the other end. Then you're going to braid it again, but this time it will be a fishtail, since you'll pull it through. You'll keep doing this, braiding the fishtail.", "section": "DPO response", "sec_num": null}, {"text": "Response B is more helpful as it provides a clear, step-by-step guide on how to make a fishtail braid, while Response A is confusing and lacks clarity.", "section": "GPT-4 Judgment (A = DPO, B = Chosen)", "sec_num": null}, {"text": "Response A provides a clear, step-by-step guide on how to make a side braid, while Response B gives a vague description of how to make a fishtail braid without clear steps. Table 6 . Responses generated by EXO and DPO given the dialogue history sampled from the Anthropic-HH test set. GPT-4 consistently prefers EXO and disprefers DPO over the chosen response regardless of the order in which the evaluated pairs are presented.Post SUBREDDIT: r/Dogtraining TITLE: Best walks when on a choke chain: dog isn't being choked, but is it bringing up bad memories? POST: My 3 year old <PERSON><PERSON> x <PERSON> rescue stayed with some relatives for a few days while my boyfriend and I moved house, and the relatives found an old choke chain to walk him with, instead of his Mekuti harness. We have had real problems with him pulling on the lead, and the <PERSON><PERSON><PERSON> harness has worked wonders, but didn't eliminate the problem. He hasn't pulled when on the choke chain at all, even when it was first put on apparently, so we think that is what he was trained on (and damage from that process is probably why his barks and whines sound a bit odd). We now think the people who had him before us were violent towards him (if you mime kicking he runs across the room terrified), so, my question is: he is best behaved on walks when wearing a choke chain, and it isn't hurting him at all, but will the harm chokers and the people using them caused him in the past affect him in any way now? I 100% DO NOT want to use a choke chain on him and I don't think anyone should ever, ever train a dog that way, but this chain is the only lead we've used that has completely solved the problem with him pulling, and he hasn't seemed stressed out by it. If he ever started pulling on the lead so that the choker was tight, I would stop using it straight away. The last thing I want is my dog being hurt. If I'd known these relatives were going to put one on him, I wouldn't have let them look after him. Still, it works and doesn't hurt him, my only worry is it'll do psychological damage somehow.", "section": "GPT-4 Judgment (A = Chosen, B = DPO)", "sec_num": null}, {"text": "TL;DR: 3yr old husky rescue pulling on choke chain w/ no damage, but using it on walks occasionally causes him stress. Can this damage actually cause harm in the future? EXO summary TL;DR: Best walks are on a choke chain but dog isn't pulling on lead, has had no problems with pull before, not sure if past violent treatment will affect him now, does it cause harm to dogs being on choke chains/when being walked on them?", "section": "Chosen summary", "sec_num": null}, {"text": "Summary A provides a more comprehensive and accurate portrayal of the post's points, despite a few misplaced details, whereas Summary B incorrectly states the dog is showing stress and pulling on the choke chain.", "section": "GPT-4 Judgment (A = EXO, B = Chosen)", "sec_num": null}, {"text": "Summary B is preferred because it more accurately summarizes the main points of the user's post by mentioning the choke chain's effect on walks, mentioning the absence of negative effects of pull so far, and raising concerns about past violent treatment potentially affecting the dog's behavior.", "section": "GPT-4 Judgment (A = Chosen, B = EXO)", "sec_num": null}, {"text": "TL;DR: If you took a dog and had it wear chokes it's self-choked, will it be traumatized in any way when you put it on and no longer train it to be choked? GPT-4 Judgment (A = DPO, B = Chosen) Summary B is preferred as it captures the key details more accurately, including the dog's breed, the issue of pulling, and the potential future harm, while Summary A is ambiguous and loss a lot of context.", "section": "DPO summary", "sec_num": null}, {"text": "Summary A is more accurate and detailed because it correctly describes the specific situation of the dog and the concerns associated with using a choke chain, while Summary B is vague and confusing in its wording.Table 7 . Summaries generated by EXO and DPO given the post sampled from the TL;DR test set. GPT-4 consistently prefers EXO and disprefers DPO over the chosen summary regardless of the order in which the evaluated pairs are presented.", "section": "GPT-4 Judgment (A = Chosen, B = DPO)", "sec_num": null}], "ref_entries": {"FIGREF0": {"uris": null, "num": null, "type_str": "figure", "fig_num": "1", "text": "Figure 1. Illustration of different characteristics of (a) π θ RKL by minimizing the reverse KL (by EXO) and (b) π θ FKL by minimizing the forward KL (by DPO)."}, "FIGREF1": {"uris": null, "num": null, "type_str": "figure", "fig_num": "2", "text": "Figure 2. The frontier of oracle reward vs reverse KL to the SFT policy of different methods in the controlled experiment."}, "FIGREF2": {"uris": null, "num": null, "type_str": "figure", "fig_num": "3", "text": "Figure3. Visualization of the estimated density ratio between the optimal and learned policy by EXO and DPO and the SFT policy on samples from the SFT policy sorted by their log probabilities."}, "FIGREF3": {"uris": null, "num": null, "type_str": "figure", "fig_num": "4", "text": "Figure 4. Win rates by comparing EXO to various baselines on the instruction-following task judged by GPT-4 and human labelers."}, "FIGREF4": {"uris": null, "num": null, "type_str": "figure", "fig_num": null, "text": "t e x i t s h a 1 _ b a s e 6 4 = \" N 8 N z + L 3 N I K L I L m s + p F I G c e n J O pQ = \" > A A A D O H i c j V J b a x Q x F M 6 M l 9 b 1 t t V H X 4 K L M A u 6 z I h S X 4 R S K 4 i K V H D b w m Q c M t n M T m j m Q p K R L m l + l i / + D N / E F x 8 U 8 d V f Y D J d l k 4 r 6 I G Q j 3 P O 9 5 1 L k j W c S R W G X z z / w s V L l 9 f W rw y u X r t + 4 + Z w 4 9 a e r F t B 6 J T U v B Y H G Z a U s 4 p O F V O c H j S C 4 j L j d D 8 7 f O b"}, "FIGREF5": {"uris": null, "num": null, "type_str": "figure", "fig_num": null, "text": "o m 8 2 D v s B K O j i t t T D H f e m x i U W K m o I F f f / 9 P m u c w A e w 6 3 pV Z W c 1 1 K v X x s T / X R A d u 9 Q l V e b K m H 8 Q E j f d e J I O R + E k 7 A y e B 9 E S j M D S d t P h Z z S r S V v S S h G O p Y y j s F G J x k I x w q k Z o F b S B p N D P K e x h R U u q U x 0 9 / A G 3 r O e G c x r Y U + l Y O c 9 z d C 4 l K 5 F m + n 2 I c / G n P N v s b h V + Z N E s 6 p p F a 3 I S a G 8 5 V D V 0 P 0 i O G O C E s U X F m A i m O 0 V k g I L T J T 9 a w O 7 h O j s y O f B 3 s N J 9 H g S v n 0 0 2 t p e r m M d 3 A F 3 Q Q A i s A m 2 w A u w C 6 a A e B + 9 r 9 5 3 7 4 f / y f / m / / R / n a T 6 3 p J z G / T M / / 0 H n C w V 1 A = = < / l a t e x i t > J lhf (⇡ ✓ ) = E x⇠D pref ⇣ E ⇡✓(y|x) [r (x, y)] D KL [⇡ ✓ (y|x)k⇡ sft (y|x)] ⌘ .< l a t e x i t s h a 1 _ b a s e 6 4 = \" N 8 N z + L 3N I K L I L m s + p F I G c e n J O p Q = \" > A A A D O H i c j V J b a x Q x F M 6 M l 9 b 1 t t V H X 4 K L M A u 6 z I h S X 4 R S K 4 i K V H D b w m Q c M t n M T m j m Q p K R L m l + l i / + D N / E F x 8 U 8 d V f Y D J d l k 4 r 6 I G Q j 3 P O 9 5 1 L k j W c S R W G X z z / w s V L l 9 f W rw y u X r t + 4 + Z w 4 9 a e r F t B 6 J T U v B Y H G Z a U s 4 p O F V O c H j S C 4 j L j d D 8 7 f O b"}, "FIGREF6": {"uris": null, "num": null, "type_str": "figure", "fig_num": null, "text": "o m 8 2 D v s B K O j i t t T D H f e m x i U W K m o I F f f / 9 P m u c w A e w 6 3 pV Z W c 1 1 K v X x s T / X R A d u 9 Q l V e b K m H 8 Q E j f d e J I O R + E k 7 A y e B 9 E S j M D S d t P h Z z S r S V v S S h G O p Y y j s F G J x k I x w q k Z o F b S B p N D P K e x h R U u q U x 0 9 / A G 3 r O e G c x r Y U + l Y O c 9 z d C 4 l K 5 F m + n 2 I c / G n P N v s b h V + Z N E s 6 p p F a 3 I S a G 8 5 V D V 0 P 0 i O G O C E s U X F m A i m O 0 V k g I L T J T 9 a w O 7 h O j s y O f B 3 s N J 9 H g S v n 0 0 2 t p e r m M d 3 A F 3 Q Q A i s A m 2 w A u w C 6 a A e B + 9 r 9 5 3 7 4 f / y f / m / / R / n a T 6 3 p J z G / T M / / 0 H n C w V 1 A = = < / l a t e x i t > J lhf (⇡ ✓ ) = E x⇠D pref ⇣ E ⇡✓(y|x) [r (x, y)] D KL [⇡ ✓ (y|x)k⇡ sft (y|x)] ⌘ .< l a t e x i t s h a 1 _ b a s e 6 4 = \" p e f P K Mj B F M T 9 F g l / S f A Q l / Z F J b 8 = \" > A A A D Z n i c n V J b a x Q x F M 7 O e G l X b b c t 4 o M v w U W Y B V 1 m R N E X o d Q K o j 5 U c N v C Z h w y 2 c x O a O Z C k p E u 2 f x J 3 3 z 2 x Z 9 hs h 2 X T q s I H g g 5 + U 6 + 7 1 w 4 a c 2 Z V G H 4 v e f 5 N 2 7 e u r 2 x 2 b 9 z 9 9 7 W 9 m B n 9 1 h W j S B 0 Q i p e i d M U S 8 p Z S S e K K U 5 P a 0 F x k X J 6 k p 6 9 c f G T r 1 R I"}, "FIGREF7": {"uris": null, "num": null, "type_str": "figure", "fig_num": null, "text": "m s U w 6 e w 7 W m d 9 X D d 9 I e P x k z / s w C 0d M R W S G b q n 4 T Y 9 T 4 a J 4 N h O A 5 X B q 8 7 U e s M Q W t H y e A b m l W k K W i p C M d S T q O w V r H G Q j H C q e m j R t I a k z M 8 p 1 P r l r i g M t a r N T H w s U V m M K u E P a W C K / Q y Q + N C u h L t T z c d e T X m w D / F p o 3 K X s W a l X W j a E k u E m U N h 6 q C b u f g j A l K F F 9 Y B x P B b K 2 Q 5 F h g o u x m 9 u 0 Q o q s tX 3 e O n 4 2 j F + P w 0 / P h / k E 7 j g 3 w E D w C A Y j A S 7 A P 3 o E j M A G k 9 8 P b 9 H a 9 P e + n v + X f 9 x 9 c f P V 6 L W c P d M y H v w A H M C P X < / l a t e x i t >"}, "FIGREF8": {"uris": null, "num": null, "type_str": "figure", "fig_num": null, "text": "/ l a t e x i t > () < l a t e x i t s h a 1 _ b a s e 6 4 = \" 4 9 m a / l 3 4n L F c A M 5 n Z 4 L x f F Q p 4 A Y = \" > A A A B + H i c b V A 9 S w N B E N 2 L X z F + 5 N T S Z j E I V u F O F C 2 D N h Y W E c w H J C Hs b e a S J X u 7 x + 6 e E o / 8 E h s L R W z 9 K X b + G / e S K z T x w c D j v R l m 5 g U x Z 9 p 4 3 r d T W F l d W 9 8 o b p a 2 t n d 2 y + 7 e f l P L"}, "FIGREF9": {"uris": null, "num": null, "type_str": "figure", "fig_num": "45", "text": "Figure 5. Illustration of the relationship among the different objectives discussed in §3. 1 : βr lhf (π βπ θ ) is a generalized version of J β (π θ ) by distributing the KL regularization to both the learned policy π θ and the reward model r ϕ ( §3.1). 2 : Ldpo(π θ ) is based on the optimal policy of J β lhf (π θ ) ( §2.3). 3 : <PERSON><PERSON>(π θ ) is equivalent to J βr lhf (π βπ θ in terms of their optimization directions ( §3.2). 4 : Ldpo-rw is the generalized version of Ldpo by subsituting the pariwise loss with softmax loss over K responses.( §3.3). The optimal policy, denoted by a dotted line, assumes unlimited model capacity. The solution, shown with a solid line, is the practically achievable policy within the realistic constraints of model capacity."}, "FIGREF11": {"uris": null, "num": null, "type_str": "figure", "fig_num": null, "text": "of the EXO, DPO and optimal policy given the prompt \"This is indeed the film that popularized kungof the EXO, DPO and optimal policy given the prompt \"This movie is about a group of people who areof the EXO, DPO and optimal policy given the prompt \"What we have here the standard Disney direct to DVD\"."}, "TABREF0": {"num": null, "type_str": "table", "content": "<table/>", "html": null, "text": "in complex tasks under minimal Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s)."}, "TABREF1": {"num": null, "type_str": "table", "content": "<table><tr><td/><td/><td colspan=\"3\">Towards Efficient Exact Optimization of Language Model Alignment</td></tr><tr><td>Method</td><td colspan=\"4\">Reward Model (%) vs SFT vs Chosen vs SFT vs Chosen GPT-4 (%)</td></tr><tr><td/><td/><td>w/ Preferences</td><td/><td/></tr><tr><td>DPOpref</td><td>68.3</td><td>23.7</td><td>57.0</td><td>30.5</td></tr><tr><td>EXOpref</td><td>92.5</td><td>60.1</td><td>83.0</td><td>55.0</td></tr><tr><td/><td colspan=\"2\">w/ <PERSON>ward Model</td><td/><td/></tr><tr><td>Best-of-N</td><td>99.3</td><td>75.8</td><td>83.5</td><td>60.0</td></tr><tr><td>PPO</td><td>93.2</td><td>58.3</td><td>77.0</td><td>52.0</td></tr><tr><td>DPOrw</td><td>82.7</td><td>39.8</td><td>70.0</td><td>41.0</td></tr><tr><td>EXOrw</td><td>97.3</td><td>76.4</td><td>88.5</td><td>64.0</td></tr></table>", "html": null, "text": "Win rates against the SFT generated texts and the chosen texts on the TL;DR summarization dataset. Best results from the computationally efficient methods are highlighted in boldface."}, "TABREF2": {"num": null, "type_str": "table", "content": "<table><tr><td>Method</td><td colspan=\"4\">Reward Model (%) vs SFT vs Chosen vs SFT vs Chosen GPT-4 (%)</td></tr><tr><td/><td/><td>w/ Preferences</td><td/><td/></tr><tr><td>DPOpref</td><td>66.3</td><td>65.1</td><td>58.0</td><td>37.0</td></tr><tr><td>EXOpref</td><td>76.4</td><td>76.7</td><td>73.0</td><td>51.0</td></tr><tr><td/><td colspan=\"2\">w/ Reward Model</td><td/><td/></tr><tr><td>Best-of-N</td><td>94.6</td><td>98.2</td><td>86.0</td><td>63.0</td></tr><tr><td>PPO</td><td>75.0</td><td>74.0</td><td>66.5</td><td>52.0</td></tr><tr><td>DPOrw</td><td>79.9</td><td>81.3</td><td>75.5</td><td>49.0</td></tr><tr><td>EXOrw</td><td>85.6</td><td>87.2</td><td>83.5</td><td>60.0</td></tr></table>", "html": null, "text": "Win rates against the SFT generated texts and the chosen texts on the Anthropic-HH dataset. Best results from the computationally efficient methods are highlighted in boldface."}}}}