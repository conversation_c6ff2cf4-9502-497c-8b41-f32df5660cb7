{"paper_id": "GRSNN", "title": "Temporal Spiking Neural Networks with Synaptic Delay for Graph Reasoning", "abstract": "Spiking neural networks (SNNs) are investigated as biologically inspired models of neural computation, distinguished by their computational capability and energy efficiency due to precise spiking times and sparse spikes with event-driven computation. A significant question is how SNNs can emulate human-like graph-based reasoning of concepts and relations, especially leveraging the temporal domain optimally. This paper reveals that SNNs, when amalgamated with synaptic delay and temporal coding, are proficient in executing (knowledge) graph reasoning. It is elucidated that spiking time can function as an additional dimension to encode relation properties via a neuralgeneralized path formulation. Empirical results highlight the efficacy of temporal delay in relation processing and showcase exemplary performance in diverse graph reasoning tasks. The spiking model is theoretically estimated to achieve 20× energy savings compared to non-spiking counterparts, deepening insights into the capabilities and potential of biologically inspired SNNs for efficient reasoning. The code is available at https://github.com/pkuxmq/GRSNN.", "pdf_parse": {"paper_id": "GRSNN", "abstract": [{"text": "Spiking neural networks (SNNs) are investigated as biologically inspired models of neural computation, distinguished by their computational capability and energy efficiency due to precise spiking times and sparse spikes with event-driven computation. A significant question is how SNNs can emulate human-like graph-based reasoning of concepts and relations, especially leveraging the temporal domain optimally. This paper reveals that SNNs, when amalgamated with synaptic delay and temporal coding, are proficient in executing (knowledge) graph reasoning. It is elucidated that spiking time can function as an additional dimension to encode relation properties via a neuralgeneralized path formulation. Empirical results highlight the efficacy of temporal delay in relation processing and showcase exemplary performance in diverse graph reasoning tasks. The spiking model is theoretically estimated to achieve 20× energy savings compared to non-spiking counterparts, deepening insights into the capabilities and potential of biologically inspired SNNs for efficient reasoning. The code is available at https://github.com/pkuxmq/GRSNN.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Spiking Neural Networks (SNNs), inspired by the detailed dynamics of biological neurons, are recognized as more biologically plausible models for neural computation and are distinguished as the third generation of neural network models, owing to their advanced computational capabilities derived from spiking time (<PERSON><PERSON>, 1997) . Unlike traditional Artificial Neural Networks (ANNs), SNNs integrate neuronal dynamics using differential equations and leverage sparse spike trains in the temporal domain for information transition (Fig. 1a ), enhancing the encoding of information in biological brains (<PERSON><PERSON><PERSON> & Reid, 2000; <PERSON><PERSON><PERSON> et al., 2003) and exhibiting increased expressive power when incorporating delay variables (<PERSON><PERSON>, 1997) . The utilization of sparse, event-based computation in SNNs facilitates energyefficient operation on neuromorphic hardware with parallel in-/near-memory computing (<PERSON> et al., 2018; <PERSON><PERSON> et al., 2019; <PERSON> et al., 2022) , making SNNs increasingly prominent as powerful and efficient neuro-inspired models in Artificial Intelligence (AI) applications (<PERSON><PERSON> et al., 2017; Shrestha & Orchard, 2018; <PERSON> et al., 2019; <PERSON><PERSON> et al., 2020; Stöckl & Maass, 2021; <PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2024) . Despite these advancements, critical inquiries remain unresolved regarding the solution by SNNs for human-like graph-based reasoning of concepts or relations and an improved utilization of spiking time for information processing.", "section": "Introduction", "sec_num": "1."}, {"text": "Symbolic and relational reasoning is a cornerstone of human intelligence and advanced AI capabilities (<PERSON>, 2008; <PERSON><PERSON> et al., 2017; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2015) and can often be formulated as graph reasoning with tasks like link prediction in knowledge graphs (Fig. 1b ) (<PERSON><PERSON> et al., 2015) . For example, it can be evaluated by machine learning tasks of knowledge graph completion (<PERSON><PERSON> et al., 2015) and inductive relation prediction (<PERSON> et al., 2017; <PERSON><PERSON> et al., 2020) , resembling humans' ability to reason new relations between entities based on commonsense knowledge graphs or generalize relations to new analogous conditions. Investigating how underlying mechanisms of neural computation can realize this reasoning capability is pivotal for understanding human intelligence and advancing AI systems, as graph reasoning is important for extensive AI tasks such as knowledge graphs, recommendation systems, and drug or material design (<PERSON> et al., 2023) . While various machine learning methods, including path-based (<PERSON> & <PERSON>, 2010; <PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2019) , embedding (<PERSON> et al., 2013; <PERSON> et al., 2015; <PERSON> et al., 2019) , and Graph Neural Networks (GNNs) (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2020; <PERSON> et al., 2021) , have been proposed for graph reasoning tasks, the efficacy of bio-inspired models in achieving comparable performance remains largely unexplored. Existing attempts, such as entity embedding by spiking times of single neurons (<PERSON>ld & Garrido, 2021; Dold, 2022) or in-context relational reasoning (<PERSON> et al., 2022) , have not addressed how reasoning paths can be propagated, especially with optimal utilization of temporal information at the network level, and have shown limitations in inductive generalization, interpretability, and performance in large knowledge graphs.", "section": "Introduction", "sec_num": "1."}, {"text": "Moreover, the importance of spiking time in SNNs (<PERSON><PERSON>, 1997; Re<PERSON><PERSON> & Reid, 2000; <PERSON><PERSON><PERSON> et al., 2003) and its potential in AI applications necessitate further exploration. Many previous works have primarily focused on enhancing SNNs as energy-efficient alternatives to ANNs for tasks like image classification (<PERSON><PERSON> et al., 2017; Shrestha & Orchard, 2018; <PERSON> et al., 2022) , with an emphasis on spike counts. Efforts to leverage spiking time have explored encoding information for single neurons by the time to first spike (<PERSON><PERSON>a, 2017; <PERSON><PERSON><PERSON> et al., 2020; Dold & Garrido, 2021) , the interval between spikes (<PERSON><PERSON>, 2022) , or adopting different weight coefficients at different times (<PERSON>öckl & Maass, 2021) , and some have delved into temporal processing tasks like time series classification (<PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2024) . However, more systematic utilization of synaptic delay at the network level and the coding principles embedded in neuronal spike trains are areas that warrant deeper investigation for better understanding and application of SNNs in extensive AI tasks.", "section": "Introduction", "sec_num": "1."}, {"text": "In this work, we introduce Graph Reasoning Spiking Neural Network (GRSNN), a novel method allowing SNNs to adeptly solve knowledge graph reasoning tasks by leverag-ing synaptic delay to encode relational information. This method enables the temporal domain of SNNs to act as an additional dimension to process edge and path properties at the network level, offering a fresh perspective on temporal information processing and coding in SNNs.", "section": "Introduction", "sec_num": "1."}, {"text": "We consider link prediction tasks of knowledge graphs and GRSNN is proposed as a neural generalization to the path formulation of graph algorithms, drawing inspiration from existing works (<PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2021) . Path formulation is important to graph reasoning due to better interpretability and inductive generalization ability (<PERSON> et al., 2021; <PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2019) . We ", "section": "Introduction", "sec_num": "1."}, {"text": "EQUATION", "section": "Introduction", "sec_num": "1."}, {"text": ")", "section": "Introduction", "sec_num": "1."}, {"text": "where I is the input current, V th is the threshold, R is the resistance, and τ m is the membrane time constant. When u reaches V th at time t f , a spike is emitted, and u is reset to the resting potential u = u rest , typically set to zero. The neuron's output spike train is represented as s(t) = t f δ(t -t f ), using the Dirac delta function. Neurons are interconnected through synapses with weight and delay (axonal or synaptic delay, for simplicity, we call synaptic delay in this paper). The model for input current is given by:", "section": "Introduction", "sec_num": "1."}, {"text": "EQUATION", "section": "Introduction", "sec_num": "1."}, {"text": "where w ij and d ij are the synaptic weight and delay from neuron j to neuron i, respectively, b i is a bias term representing background current, and τ c is another time constant.", "section": "Introduction", "sec_num": "1."}, {"text": "Given the reset mechanism, the equivalent SRM form is:", "section": "Introduction", "sec_num": "1."}, {"text": "EQUATION", "section": "Introduction", "sec_num": "1."}, {"text": "with κ(τ ) being the temporal kernel function for input spikes and ν(τ ) = -(V th -u rest )e -τ τm representing the reset kernel. Assuming τ c = τ m , the input kernel becomes κ(τ ) = R τm • τ e -τ τm for τ ≥ 0 and κ(τ ) = 0 for τ < 0. Setting R = e, the kernel simplifies to κ(τ ) = τ τm e 1-τ τm , which is commonly used (Shrestha & Orchard, 2018) .", "section": "Introduction", "sec_num": "1."}, {"text": "In practice, we simulate SNNs using the discrete computational form of the current-based LIF model:", "section": "Introduction", "sec_num": "1."}, {"text": "EQUATION", "section": "Introduction", "sec_num": "1."}, {"text": "where H(x) is the Heaviside step function, s i [t] is the spike signal at discrete time step t, ∆τ is the discretization interval, and α denote the coefficient e∆τ τm . Utilizing the equivalent SRM formulation and surrogate derivatives for the spiking function, gradients for parameters, including w ij and d ij , can be computed through backpropagation over time (<PERSON><PERSON><PERSON><PERSON> & Orchard, 2018) . Specifically, the non-differentiable term ∂si [t] ∂ui [t] is substituted by surrogate derivatives of a smooth function, such as the derivative of the sigmoid function: ∂s ∂u = 1 a1 e (V th -u)/a 1 (1+e (V th -u)/a 1 ) 2 , with a 1 as a hyperparameter. The gradients are then calculated as", "section": "Introduction", "sec_num": "1."}, {"text": "∂L ∂wij = t ∂L ∂si[t] ∂si[t] ∂ui[t] ∂ui[t] ∂wij and ∂L ∂dij = t ∂L ∂si[t] ∂si[t] ∂ui[t] ∂ui[t] ∂rij [t] ∂rij [t] ∂dij , where r ij [t] = t τ =0 κ(τ - d ij )s j [t -τ ],", "section": "Introduction", "sec_num": "1."}, {"text": "and", "section": "Introduction", "sec_num": "1."}, {"text": "∂rij [t] ∂dij = - t τ =0 κ(τ -d ij )s j [t -τ ] (", "section": "Introduction", "sec_num": "1."}, {"text": "κ denotes the derivative of the kernel κ). In a discrete setting, d ij should be integers, and we employ the straight-throughestimator to train a quantized real-valued variable. For additional details, please refer to Appendix A. In this study, we primarily focus on parameters w ij and d ij , leaving the exploration of heterogeneous neurons for future work.", "section": "Introduction", "sec_num": "1."}, {"text": "We consider link prediction tasks of (knowledge) graphs. A knowledge graph is denoted by G = (V, E, R), with V, E, and R representing the sets of graph nodes, graph edges, and relation types, respectively. We also consider homogeneous graphs G = (V, E) as a special case with only one relation type. The task is to predict whether an edge of type q exists between entities x, y (Fig. 2a ), and the common methods are to calculate or learn a pair representation h q (x, y) for prediction, e.g., using paths between two nodes or embedding methods or GNNs, while we explore using SNNs. Many link prediction tasks are transductive, i.e., predicting new links on the training graph, and there is also the inductive setting where training and testing graphs have different entities but the same relation types.", "section": "Link Prediction of Graphs", "sec_num": "2.2."}, {"text": "Some previous works show that the synaptic delay of SNNs can be leveraged to solve traditional graph tasks, providing a parallelizable and efficient neuromorphic computing solution to graph algorithms (<PERSON><PERSON><PERSON> et al., 2021) . For the traditional graph single-source shortest path problem, by assigning a neuron to each graph node and configuring the delay between neurons as the graph edge weight, SNNs can parallelly simulate <PERSON><PERSON><PERSON>'s algorithm. An example is shown in Fig. 2e if we decode the spike train of the target neuron by the time to first spike. We will generalize the thought-delays in SNNs can represent the properties of graph edges-to graph AI reasoning tasks with neural generalization and advanced temporal coding with multiple temporal spikes for diverse paths.", "section": "Synaptic Delay for Traditional Graph Algorithms", "sec_num": "2.3."}, {"text": "In this section, we introduce our graph reasoning spiking neural networks. We first introduce the overview of our model in Section 3.1. Then in Section 3.2, we demonstrate that GRSNN can be viewed as a generalized path formulation for graph reasoning. In Section 3.3, we discuss the comparison with graph neural networks. Finally, we introduce implementation details in Section 3.4.", "section": "Graph Reasoning Spiking Neural Network", "sec_num": "3."}, {"text": "The outline of GRSNN is depicted in Fig. 2 . Each graph node is assigned n spiking neurons, representing each entity by a neuron population (Fig. 2b ). Synaptic connections, corresponding to relation links between entities, are characterized by weight and delay between neuron groups (Fig. 2c ). These synaptic properties, such as delay, are dependent on the graph edge relation and modulated by the query relation (task goal), allowing the integrated properties of paths to be reflected by the spiking time considering delays (Fig. 2e ). Depiction of the temporal domain serving as an additional dimension to process the properties of edges and paths in a network with more propagation paths. In the demonstrated network under a simplified setting where each input spike triggers an output spike for neurons, a spike from the source neuron will lead to four spikes from the target neuron, whose time varies corresponding to four propagation paths with different integrated properties of edges represented by synaptic delay.", "section": "Model Overview", "sec_num": "3.1."}, {"text": "Unlike SNNs for traditional graph tasks, we generalize the model to allow both positive and negative synaptic weights, acting as complementary transformations to learnable synaptic delays that are viewed as an additional dimension to process graph edges and paths.", "section": "Model Overview", "sec_num": "3.1."}, {"text": "For the link prediction task (Fig. 2d ), a constant current I q is injected to the spiking neurons of the source node x for a given query q between nodes x and y, generating spike trains. The network then propagates these spikes, and a spike train s q y (t) from the target node y's neurons is obtained after a time interval. A decoding function D calculates the pair representation h q (x, y) = D(s q y (t)) for link prediction, and we primarily utilize temporal coding D(s q y (t)) = τ λ τ s q y [τ ] / ( τ λ τ ), emphasizing early spiking time. This corresponds to the decoding for various path formulations (refer to Appendix B for more details).", "section": "Model Overview", "sec_num": "3.1."}, {"text": "GRSNN serves as a neural generalization of the path formulation for graphs, allowing for the simultaneous consideration of all paths from a source node without the separate calculation of each one. Path formulation is important to graph reasoning due to better interpretability and inductive generalization ability (<PERSON> et al., 2021; <PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2019) . Traditional path-based algorithms calculate the pair representation between nodes x and y by considering paths from x to y, formulated as a generalized accumulation of path representations (<PERSON> et al., 2021) :", "section": "GRSNN as Generalized Path Formulation", "sec_num": "3.2."}, {"text": "EQUATION", "section": "GRSNN as Generalized Path Formulation", "sec_num": "3.2."}, {"text": "where P xy is the set of paths from x to y, e i is the i-th edge on a path P , and v q (e i ) is the edge representation (e.g., the transition probability of this edge). Various methods like Katz Index (<PERSON>, 1953) , Personalized PageRank (<PERSON> et al., 1999), and Graph Distance (Liben-Nowell & Kleinberg, 2007) follow this modeling.", "section": "GRSNN as Generalized Path Formulation", "sec_num": "3.2."}, {"text": "In GRSNN, spike trains propagate over time, with spikes at different times simultaneously maintaining all paths from the source node. The spike train of y is:", "section": "GRSNN as Generalized Path Formulation", "sec_num": "3.2."}, {"text": "EQUATION", "section": "GRSNN as Generalized Path Formulation", "sec_num": "3.2."}, {"text": ")", "section": "GRSNN as Generalized Path Formulation", "sec_num": "3.2."}, {"text": "where f is the function of spiking neurons, w q z,y and d q z,y are synaptic weights and delays between nodes z and y given the query relation q, N (y) denotes the set of neighbors of y, and f denotes the general composite function for all paths. In some degenerated conditions, the time of a spike is the sum of edge delays on one path, allowing a decoding function F to perform a general summation over all paths represented in the spike train. We show that, with specific settings, GRSNN can solve traditional path-based methods.", "section": "GRSNN as Generalized Path Formulation", "sec_num": "3.2."}, {"text": "Proposition 3.1. Katz Index, Personalized PageRank, and Graph Distance can be solved by GRSNN under specific settings.", "section": "GRSNN as Generalized Path Formulation", "sec_num": "3.2."}, {"text": "The proof is detailed in Appendix B, focusing on the construction of appropriate delay and decoding functions. This proposition illustrates that GRSNN can degenerate to emulate traditional path-based algorithms. By employing parameterized synaptic delays for learnable edge representations, and additional parameters like synaptic weights for transformations in another dimension, GRSNN emerges as a neural generalization of the path formulation for graph reasoning. This sheds light on the capability of SNNs to execute neurosymbolic computation on graph paths utilizing spiking time and synaptic delay. Furthermore, GRSNN, as a generalization of path formulation, extends its important applicability to inductive settings and reasoning path interpretations, distinguishing it from entity embedding methods.", "section": "GRSNN as Generalized Path Formulation", "sec_num": "3.2."}, {"text": "The introduced GRSNN bears a resemblance to the widelyused message-passing GNNs in machine learning, both propagating messages between interconnected nodes. However, notable distinctions exist.", "section": "Comparison with Graph Neural Networks", "sec_num": "3.3."}, {"text": "First, GRSNN incorporates varied temporal synaptic delays in message passing, allowing for the encoding of relational information in spiking times with enhanced spatiotemporal processing. In contrast, GNNs uniformly propagate messages across all edges in each iteration. Second, GRSNN disseminates temporal spike trains throughout the network, as opposed to GNN's real-valued activations. This not only facilitates the representation of multiple paths through diverse spiking times within a spike train but also promotes event-driven energy-efficient computation suitable for neuromorphic hardware. Moreover, while <PERSON> et al. (2021) interprets GNN as a neural counterpart to the Bellman-Ford algorithm, GRSNN is perceived as a neural generalization of <PERSON><PERSON><PERSON>'s algorithm. This parallel between artificial and brain-inspired neural networks in generalizing distinct classical algorithms for analogous objectives is intriguing.", "section": "Comparison with Graph Neural Networks", "sec_num": "3.3."}, {"text": "Once the inherent differences are accounted for, GRSNN can also have a formulation analogous to GNNs. Specifically, at each discrete time step, every node (with spiking neurons) aggregates messages from neighbors. Assuming the sharing of synaptic weights across all edges, akin to GNNs, messages are represented by delayed spikes. The aggregation function then becomes a synthesis of the summation of all messages, a linear transformation, and the spike generation with neuronal dynamics of spiking neurons. Thus, for every node z, the following holds:", "section": "Comparison with Graph Neural Networks", "sec_num": "3.3."}, {"text": "EQUATION", "section": "Comparison with Graph Neural Networks", "sec_num": "3.3."}, {"text": "Here, r signifies the relation from node k to node z, s q k [td q r ] represents the vector of spikes with associated delays d q r , and 1 z=x is an indicator for the current injection to the source node. The time steps can be viewed as the layers of GNNs, with shared weights and delays for all time steps. Consequently, the inference time and space complexity of GRSNN align closely with those of GNNs, except that they are proportional to the number of discrete time steps instead of GNN's layer number.", "section": "Comparison with Graph Neural Networks", "sec_num": "3.3."}, {"text": "Model Detail In practice, our models predominantly adhere to Eq. ( 7). The set of learnable parameters encompasses W and b, symbolizing a shared linear transformation of synaptic weights, and d q r , denoting the delay between the spiking neurons of two nodes, contingent on their relation r and the query relation q. Additionally, r signifies the embedding of relations, utilized for both current injection (I q = r q ) and the ultimate link prediction with a parameterized function to predict links based on h q (x, y) and r q . To differentiate the varying contributions of a relation (edge) in forecasting different query relations, we align with previous studies (<PERSON> et al., 2021) to parameterize the edge representation of relation r as a linear function over the query relation. This is then processed through a sigmoid function with a bound scale β to serve as positive delays, i.e., d q r = βσ(W r r q + b r ). In the context of homogeneous graphs characterized by a singular relation, this simplifies to d q r = βσ(b r ). It undergoes quantization and is trained by the straight-through-estimator. Post-learning, it can be archived in a look-up table, obviating the need for nonlinear computations. This can be analogous to neuromodulation with a superior signal delineating the task objective.", "section": "Implementation Details", "sec_num": "3.4."}, {"text": "In line with prevalent practices for link prediction, the objective is to ascertain the likelihood of a triplet (x, q, y), consisting of the source node, query relation, and target node. The procedure of our model to deduce a triplet (x, q, y) commences with the propagation of spike trains across the graph to secure the pair representation  and WN18RR . Lower values are preferable for MR, while higher values are desirable for MRR, HITS@1, and HITS@10. Detailed values can be found in Appendix F.1.", "section": "Link Prediction Detail", "sec_num": null}, {"text": "h q (x, y), and subsequently, the likelihood score is computed by a parameterized function g given h q (x, y), consistent with prior studies (<PERSON> et al., 2021) . More details can be found in Appendix E.1. The overarching procedure aligns with the conventional graph reasoning paradigm, with our primary focus being on the pivotal step of acquiring the pair representation through SNN propagation.", "section": "Link Prediction Detail", "sec_num": null}, {"text": "Regarding the training procedure, we adhere to the methodologies of preceding works (<PERSON><PERSON> et al., 2013; <PERSON> et al., 2019; <PERSON> et al., 2021) , generating negative samples by corrupting one entity in a positive triplet. Please refer to Appendix E.1 for more details.", "section": "Link Prediction Detail", "sec_num": null}, {"text": "In this section, we conduct experiments on transductive knowledge graph completion, inductive knowledge graph relation prediction, and homogeneous graph link prediction to evaluate the proposed GRSNN model. For knowledge graphs, we consider the commonly used FB15k-237 (Touta<PERSON> & Chen, 2015) and WN18RR (<PERSON><PERSON><PERSON> et al., 2018) with the standard transductive splits and inductive splits (<PERSON><PERSON> et al., 2020) . For homogeneous graphs, we consider Cora, Citeseer, and PubMed (<PERSON> et al., 2008) . The train/valid/test ratio of edges is 85:5:10 following the common practice, and the statistics of datasets can be found in Appendix D.", "section": "Experiments", "sec_num": "4."}, {"text": "For evaluation of knowledge graph completion, we adhere to the filtered ranking protocol (<PERSON><PERSON> et al., 2013) , ranking a test triplet (x, q, y) against all unseen negative triplets and report Mean Rank (MR), Mean Reciprocal Rank (MRR), For MR, the lower the better. For MRR, HITS@1, HITS@3, and HITS@10, the higher the better.", "section": "Experiments", "sec_num": "4."}, {"text": "MR↓ MRR↑ H@1↑ H@3↑ H@10↑ and <PERSON>ITS@N. For inductive knowledge graph relation prediction, the evaluation adheres to the protocols outlined in the literature (<PERSON><PERSON> et al., 2020) , where 50 negative triplets are drawn for each positive one using the filtered ranking, and the results are reported as HITS@10. For homogeneous graph link prediction, we follow Kipf & Welling (2016) ; <PERSON> et al. (2021) to compare the positive edges against the same number of negative edges, and the results are quantified using Area Under the Receiver Operating Characteristic Curve (AUROC) and Average Precision (AP).", "section": "Method", "sec_num": null}, {"text": "More experimental details can be found in Appendix E.3.", "section": "Method", "sec_num": null}, {"text": "We initiate our evaluation with experiments on transductive knowledge graph completion to assess the efficacy of GRSNN. This task, illustrated in Appendix E.2, involves predicting unseen relations between two existing entities in a knowledge graph and serves as a standard for assessing graph reasoning link prediction.", "section": "Transductive Knowledge Graph Completion", "sec_num": "4.1."}, {"text": "We investigate the role of synaptic delay in encoding relational information for rea- 1 , highlight that synaptic delay significantly excels over the baselines, accentuating the merits of incorporating temporal processing with delays in bio-inspired models for effective relational reasoning.", "section": "Advantage of synaptic delay", "sec_num": null}, {"text": "We juxtapose the performance of our bio-inspired GRSNN with various machine learning methods, including pathbased, embedding, and GNN methods, as depicted in Fig. 3 , to underscore its efficacy in knowledge graph reasoning. We derive the results of preceding methods (<PERSON> et al., 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON>ld, 2022) . In essence, GRSNN secures competitive results, surpassing the majority of machine learning methods across all metrics, thereby attesting to the effectiveness of bio-inspired models in solving humanlike advanced knowledge reasoning tasks. NBFNet attains superior performance by employing numerous GNN tricks that we deliberately omitted to preserve the inherent properties of SNNs. If we further integrate some techniques (refer to Appendix E.3), our model, denoted as GRSNN+ in Fig. 3 , also achieves a better performance. Note that the proposed GRSNN prioritizes bio-plausibility, delivering promising performance with augmented efficiency, as will be analyzed in the following.", "section": "Comparison with prevalent machine learning methods", "sec_num": null}, {"text": "Parameter amount Fig. 4a contrasts the parameter quantities of several representative methods, highlighting the notable parameter efficiency of GRSNN in achieving competitive performance compared to other methods.", "section": "Analysis Results", "sec_num": "4.2."}, {"text": "Theoretical estimation of energy GRSNN leverages the energy efficiency inherent to SNNs through spike-based computation. On the test set of FB15k-237, the model ex-hibits a spike rate-the average spike count per discrete time step-of approximately 0.258. This translates to roughly a 4× reduction in synaptic operations compared to equivalent real-valued neural networks. Given that spikes necessitate only Accumulate (AC) operations as opposed to Multiply-and-Accumulate (MAC) operations, there is a substantial reduction in energy costs, as evidenced by the energy consumption of 32-bit FP MAC and AC operations on a 45 nm CMOS processor being 4.6 pJ and 0.9 pJ, respectively (<PERSON><PERSON><PERSON>, 2014) . Fig. 4b provides a concise theoretical estimation of the number of addition and multiplication operations and the associated energy requirements, with the multiplication in SNNs arising due to leaky neuronal dynamics (please refer to Appendix E.3 for calculation details).", "section": "Analysis Results", "sec_num": "4.2."}, {"text": "Based on these estimations, a potential 20× energy reduction is foreseeable, and under certain conditions where AC can be 31× cheaper than MAC (<PERSON> et al., 2021; <PERSON><PERSON>, 2014) , this could extend to around 100×.", "section": "Analysis Results", "sec_num": "4.2."}, {"text": "Note that there can also be costs from synaptic delay. We consider the Ring Buffer as a potential implementation, which is commonly used by digital neuromorphic platforms and analyzed (<PERSON><PERSON><PERSON> et al., 2023) . The additional energy overhead will account for an extremely small proportion of energy-it is estimated as 0.004 mJ, while the energy for synaptic operations estimated above is 1.337 mJ (please refer to Appendix E.3 for calculation details), and this conclusion is consistent with <PERSON><PERSON><PERSON> et al. (2023) . Therefore, the costs of synaptic delay do not affect the substantial energy efficiency.", "section": "Analysis Results", "sec_num": "4.2."}, {"text": "More spike rate statistics on different datasets or tasks are presented in Table 2 , showing that spikes are even sparser on other datasets or tasks introduced in the following. This underscores the substantial potential of GRSNN in enhancing energy efficiency by one to two orders of magnitude.", "section": "Analysis Results", "sec_num": "4.2."}, {"text": "Interpretability To demonstrate the interpretability of GRSNN as neural-generalized path formulation, in Appendix F.2, we visualize the reasoning paths for the final predictions of several examples, based on edge and path importance, determined by the gradient of the prediction w.r.t. edges, and beam search for paths of higher importance (refer to Appendix E.3 for details). Results show that GRSNN is adept at discerning relation relevances and exploiting transitions and analogs.", "section": "Analysis Results", "sec_num": "4.2."}, {"text": "More analysis results such as the impact of temporal discretization steps and the impact of neuron number and parameter amount are in Appendix F.", "section": "Analysis Results", "sec_num": "4.2."}, {"text": "Experiments are also conducted on inductive relation prediction to assess the efficacy of GRSNN. Unlike the transductive setting, which focuses on predicting new links within the training knowledge graph, inductive prediction strives to extrapolate the ability to predict relations from the training graph to a distinct testing graph. This testing graph encompasses different entities but retains the same relation types, as illustrated in Appendix E.2, demonstrating the ability to generalize relational reasoning to new conditions. Traditional entity embedding methods falter under this condition, whereas GRSNN, being a generalized form of path formulation, adeptly manages it.", "section": "Inductive Relation Prediction", "sec_num": "4.3."}, {"text": "The outcomes, depicted in Fig. 5 , reveal that GRSNN surpasses the performance of most machine learning methods in inductive settings, underscoring its proficiency in generalizing reasoning to new entities. The spike rate statistics in Table 2 also indicate the potential for energy efficiency.", "section": "Inductive Relation Prediction", "sec_num": "4.3."}, {"text": "We also assess the GRSNN in the context of link prediction tasks for standard homogeneous graphs, illustrating its versatility across diverse application domains. Homogeneous graphs are essentially a subset of knowledge graphs, characterized by a singular type of relation, i.e., the presence of graph edges, and are ubiquitously observed. In such instances, the representation of edges remains consistent across the graph, and the GRSNN primarily leverages the information pertaining to graph distance in spiking time, as opposed to relation-specific information.", "section": "Homogeneous Graph Link Prediction", "sec_num": "4.4."}, {"text": "The results shown in Fig. 6 reveal that GRSNN manifests competitive performance in comparison to other proficient machine learning models, underscoring its efficacy. The sparse spike rates presented in Table 2 further highlight the efficiency potential. ", "section": "Homogeneous Graph Link Prediction", "sec_num": "4.4."}, {"text": "This study demonstrates the potential of bio-inspired SNNs in addressing graph reasoning through the innovative use of synaptic delay and spiking time. We introduced GRSNN, a model that employs synaptic delays to encode relation information of graph edges and utilizes the temporal domain as an additional dimension for processing graph path properties. This approach can be perceived as a neural generalization of the path formulation with better inductive generalization ability and interpretability. It provides insights into the capabilities of networks with biological neuron models to efficiently facilitate neuro-symbolic reasoning in tasks central to human intelligence, such as relational reasoning of concepts. Additionally, it explores the enhanced role that spiking time can play in AI applications. The promising performance and substantial theoretical energy efficiency of our model underscore the potential of SNNs in a wider array of applications such as efficient reasoning.", "section": "Discussion and Conclusion", "sec_num": "5."}, {"text": "Our approach to temporal coding of spike trains assigns varying weights to different times, which is similar to the methodology in Stöckl & Maass (2021) but in our model, earlier spikes are designed to receive higher weights, which also integrates concepts from the time to first spike paradigm (Mostafa, 2017) . Distinct to these works, our focus extends beyond individual neuron temporal coding to encompass the network level, allowing spiking time to integrate path properties during network propagation, and enabling multiple spikes to represent diverse paths globally. Unlike prior studies on traditional graph algorithms (<PERSON><PERSON><PERSON> et al., 2021), which primarily target the shortest path task, our work delves into the multifaceted realm of graph AI tasks with multiple temporal spikes for diverse paths. Together, our work offers a fresh perspective on temporal information processing in SNNs.", "section": "Discussion and Conclusion", "sec_num": "5."}, {"text": "This study marks an initial exploration of utilizing SNNs for graph reasoning, leveraging the temporal domain, and opens avenues for numerous future directions. First, the reliance on discrete simulation and backpropagation through time for training SNNs is resource-intensive, especially for long simulation times with small discrete intervals. Therefore, this work primarily considers T = 10 discrete time steps, which could sacrifice more precise temporal information. Enhancements in simulation or training methodologies at hardware/coding/algorithm levels may yield improved results given more accurate temporal information. Additionally, the exploration of online training methods conducive to on-chip learning of SNNs (<PERSON><PERSON> et al., 2020; <PERSON> et al., 2022) for learning synaptic delays may offer insights into efficient and more biologically plausible learning of our model. Second, to fulfill the properties of SNNs, many advanced GNN strategies may not be incorporated, such as intricate message and aggregate functions and elaborate network structures. The investigation of SNN-compatible strategies may potentially bridge the performance gap, e.g., heterogeneous neurons and different neuron dynamics may provide more powerful computational properties (Chakraborty & Mukhopadhyay, 2023; <PERSON><PERSON> et al., 2018) . Last, given the prevalence of graph tasks in AI applications, future studies could delve into wider applications of graph reasoning such as drug or material design, investigating the potential of bio-inspired models for efficient applications.", "section": "Discussion and Conclusion", "sec_num": "5."}, {"text": "In conclusion, our study illustrates the capability of braininspired SNNs in efficient symbolic graph reasoning, emphasizing the enhanced role of the temporal domain. Given their neuromorphic attributes, SNNs are poised to achieve substantial energy efficiency and high parallelism on spikebased neuromorphic hardware. It is our aspiration that this research serves as a catalyst for deeper insights and wider applications of biologically inspired efficient SNNs.", "section": "Discussion and Conclusion", "sec_num": "5."}, {"text": "As introduced in Section 2.1, the models for membrane potential and current are described by the following equations:", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "EQUATION", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "EQUATION", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "and the equivalent SRM formulation is:", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "EQUATION", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": ")", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "Let L denote the loss based on the spikes of neurons. With the SRM formulation, the gradients for w ij and d ij can be calculated as follows:", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "EQUATION", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "EQUATION", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "where δ i (t) is the gradient for s i (t) and can be recursively calculated by backpropagation through time as:", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "EQUATION", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "and κ(•) represents the derivative of κ(•).", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "In practice, we simulate SNNs using the discrete computational form of the current-based LIF model:", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "EQUATION", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "The gradients of δ i (t) and ∂L ∂wij can be calculated using the standard backpropagated automatic differentiation framework in deep learning libraries, based on the above formulation. The spiking function is non-differentiable, and ∂si [t] ∂ui[t] can be replaced by a surrogate derivative (<PERSON><PERSON><PERSON><PERSON> & Orchard, 2018) . We consider the derivative of the sigmoid function:", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "EQUATION", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": ")", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "where we take a 1 = 0.25.", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "The automatic differentiation of the above formulation cannot directly handle ∂L ∂dij . We rewrite it in the discrete setting as:", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "EQUATION", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "We can integrate this into the automatic differentiation by tracking the trace", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "tr ij [t] = - t τ =0 κ[τ -d ij ]s j [t -τ", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "] and calculating gradients based on it and the error backpropagated to s j [t -d ij ]. In the discrete setting, d ij should be an integer index. We quantize it in the forward simulation and calculate gradients using the straight-through-estimator.", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "As described in Section 2.1, we consider τ c = τ m , R = e and the input kernel is κ(τ ) = eτ τm exp -τ τm for τ ≥ 0 and κ(τ ) = 0 for τ < 0. Then, κ(τ ) = e τm 1 -τ τm exp -τ τm for τ ≥ 0. In the discrete setting of the current-based LIF model, κ is better described as κ[τ ] = α(τ + 1) exp(-τ τm/∆τ ), τ ≥ 0 (where α = e τm/∆τ ). Correspondingly, we take", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "κ[τ ] = α 1 -τ +1", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "τm/∆τ exp -τ τm/∆τ and calculate the trace tr ij based on it. Proof. We first introduce more details of Katz Index, Personalized PageRank, and Graph Distance. As described in Section 3.2, traditional path-based algorithms for graphs calculate the pair representation between nodes x, y by considering paths from x to y, and this can be formulated as a generalized accumulation of path representations (denoted as ⊗) with a commutative summation operator (denoted as ⊕):", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "EQUATION", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": ")", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "where P xy is the set of paths from x to y, e i is the i-th edge on a path P , and v q (e i ) is the representation of the edge (e.g., the transition probability of this edge). Katz Index is a path formulation with ⊕ = +, ⊗ = ×, v q (e) = β, Personalized PageRank is with ⊕ = +, ⊗ = ×, v q (e) = 1/d out (z) (where d out (z) is the output degree of the start node z of edge e), and Graph Distance is with ⊕ = min, ⊗ = +, v q (e) = 1.", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "We examine these three distinct settings:", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "(1) Graph Distance: In this setting, each graph node is assigned one spiking neuron, and neurons are connected if there exists a graph edge between them, with all synaptic weights and thresholds set to 1. Consequently, each input spike to a neuron will trigger an output spike. The synaptic delay of each edge is set as the corresponding positive graph edge length, allowing the propagation of spikes along edges to accumulate edge length into time. By initiating a spike from the source node at time 0, GRSNN propagates spikes throughout the network, and the time to the first spike of each node represents the shortest distance to the source node. Utilizing the decoding function of the spike train from the target node as the first spiking time allows us to compute the graph distance.", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "(2) Katz Index: The Katz Index necessitates the accumulative multiplication of edge representations. By applying the log operation, this multiplication can be transformed into accumulation. For an edge representation β ∈ (0, 1) of Katz Index, corresponding to an attenuation factor, the synaptic delay is set as d = -log β (potentially scaled). For a spiking time t, 10 -t represents the accumulative multiplication of edge representations in the path. To sum over all paths, the number of paths during spike propagation must be maintained. A single spiking neuron is insufficient for this task as it will only generate one output spike when multiple paths simultaneously propagate to the same node. This limitation can be addressed by employing multiple spiking neurons, assigning N spiking neurons to each graph node, with thresholds set as 1, 2, • • • , N . Neurons connected by graph edges have synaptic weights of 1 and delays as described above. The time and number of spikes of each node correspond to different paths from the source node. After sufficient propagation time, the decoding function of the spike train from the target node is defined as D(s(t)) = τ 10 -τ ( i s i [τ ]), enabling the computation of the Katz Index.", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "(3) Personalized PageRank: This is analogous to the Katz Index, with the edge representation being the transition probability 1/d out (z) ∈ (0, 1). The synaptic delay is similarly set as d = -log(1/d out (z)) (or with a scale). Thus, Personalized PageRank can be computed similarly to the Katz Index.", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "Remark B.2. The crux of the proof revolves around the construction of appropriate synaptic delays and decoding functions.", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "As illustrated in the construction, distinct temporal coding methods naturally arise for varying path formulations. In many scenarios, the significance of edge representations in knowledge graphs can be interpreted as learnable probabilities, making the accumulative multiplication setting (as in Katz Index and Personalized PageRank) particularly advantageous. This results in the adoption of temporal coding in our experiments in the main text, assigning different weights to different spikes, represented as D(s q y (t)) = τ λ τ s q y [τ ] τ λ τ , except a constant factor. A notable distinction is that, instead of a straightforward summation across different neurons, we derive the pair representation as a vector of different neurons. Subsequently, the likelihood is computed using a learnable function g, aligning with the prevalent approaches in graph reasoning methods (refer to Section 3.4). This approach also serves as a broader generalization of the formulation in the construction.", "section": "A. Training Spiking Neural Networks", "sec_num": null}, {"text": "Spiking Neural Networks Recent works mainly study SNNs as energy-efficient alternatives to ANNs by converting ANNs to SNNs for object recognition (<PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON><PERSON> & Maass, 2021; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2022b) and natural language classification (<PERSON><PERSON> et al., 2023) , or direct training SNNs (with surrogate gradients or other methods) for audio or visual perception (Shrestha & Orchard, 2018; <PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2022a; <PERSON> et al., 2024) , time series classification (<PERSON> et al., 2021; <PERSON> et al., 2022) , seizure detection (<PERSON> et al., 2024) , graph classification (<PERSON> et al., 2022; <PERSON> et al., 2023) , etc. Most of them focus on spike counts and hardly leverage the important temporal dimension. Some works explore temporal encoding for single neurons (<PERSON><PERSON><PERSON>, 2017; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2021; <PERSON><PERSON> & Maass, 2021) , or utilizing spiking time for feature binding (<PERSON> et al., 2022) , but how synaptic delay with temporal coding at the network level can be systematically utilized is rarely considered. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2023) and <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2024) learn delays for time series tasks, and <PERSON> et al. (2024) learn delays in network structure design, but they not consider temporal coding for graph tasks. Some works attempt to use SNNs for relational reasoning in knowledge graphs with entity embedding based on spiking times (Dold & Garrido, 2021; Dold, 2022) or population coding combined with reward-modulated STDP (Fang et al., 2022) . They do not consider reasoning paths with synaptic delay and temporal coding, and are limited in inductive generalization and interoperability considering the entity embedding method as well as poor performance in large knowledge graphs. Differently, our novel method is the first to demonstrate the advantage of delays to represent relations with promising performance on real transductive and inductive (knowledge) graphs.", "section": "<PERSON><PERSON> Work", "sec_num": null}, {"text": "Graph Reasoning Graph link prediction is a fundamental graph reasoning task, typically in the context of knowledge graph reasoning. Popular methods include three paradigms: path-based, embedding, and graph neural networks (<PERSON> et al., 2021) . Path-based methods predict links based on paths from the source node to the target node, e.g., the weighted count of paths in homogeneous graphs (<PERSON>, 1953; <PERSON> et al., 1999; Liben-Nowell & Kleinberg, 2007) or paths with learned probabilities or representations in knowledge graphs (<PERSON> & Cohen, 2010; <PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2019) . Embedding methods learn representations for each node and edge which preserve the structure of the graph (<PERSON><PERSON><PERSON> et al., 2014; <PERSON> et al., 2015; <PERSON><PERSON> & Leskovec, 2016; <PERSON><PERSON> et al., 2013; <PERSON> et al., 2015; <PERSON> et al., 2019) . They rely on entities and cannot perform inductive reasoning. GNNs perform message passing between nodes for reasoning based on the learned node or edge representations. For knowledge graphs, R-GCN (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018) and CompGCN (<PERSON><PERSON><PERSON><PERSON> et al., 2020) propagate over all entities with different message functions, while GraIL (<PERSON><PERSON> et al., 2020) propagates in an extracted subgraph. NBFNet (<PERSON> et al., 2021) proposes a framework to integrate path formulation and graph neural networks, achieving state-of-the-art results with GNNs. Different from these works, we focus on exploring SNNs with spiking time.", "section": "<PERSON><PERSON> Work", "sec_num": null}, {"text": "FB15k-237 (<PERSON><PERSON><PERSON> & <PERSON>, 2015 ) is a refined knowledge graph link prediction dataset derived from FB15k. It is meticulously curated to ensure that the test and evaluation datasets are devoid of inverse relation test leakage. Similarly, WN18RR (<PERSON><PERSON><PERSON> et al., 2018) is another knowledge graph link prediction dataset, formulated from WN18 (a subset of WordNet), maintaining the integrity by avoiding inverse relation test leakage.", "section": "D. Datasets statistics", "sec_num": null}, {"text": "For the conventional transductive knowledge graph completion setting, the datasets exhibit varying quantities of entities, relations, and relation triplets across the train, validation, and test sets, as detailed in Table 3 . In the context of the standard inductive relation prediction setting, the statistical breakdown for different splits is depicted in Table 4 .", "section": "D. Datasets statistics", "sec_num": null}, {"text": "Additionally, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>ed (<PERSON> et al., 2008) serve as homogeneous citation graphs, with their respective statistics outlined in Table 5 . (<PERSON> et al., 2008) 2,708 4,614 271 544 CiteSeer (<PERSON> et al., 2008) 3,327 4,022 236 474 PubMed (<PERSON> et al., 2008) In line with prevalent practices for link prediction, the objective is to ascertain the likelihood of a triplet (x, q, y), consisting of the source node, query relation, and target node. Consistent with prior studies (<PERSON> et al., 2021) , we employ a feedforward neural network g to estimate the conditional likelihood of the tail entity y, predicated on the head entity x and query q, utilizing the pair representation h q (x, y), formulated as p(y|x, q) = σ(g(h q (x, y); r q )), where σ denotes the sigmoid function. Analogously, the conditional likelihood of the head entity x, contingent upon y and q, is deduced as p(x|y, q -1 ) = σ(g(h q -1 (y, x); r q -1 )), with q -1 representing the inverted relation. In the scenario of undirected graphs, the representations undergo symmetrization, resulting in p(x, q, y) = σ(g(h q (x, y) + h q (y, x); r q )). Adhering to established methodologies, a two-layer Multi-Layer Perceptron (MLP) with ReLU activation is utilized for g. It is noteworthy that this configuration is also conducive to implementation via a spiking MLP, given the facile conversion of the ReLU function to spiking neurons, achievable through rate or temporal coding (Rueckauer et al., 2017; Stöckl & Maass, 2021) . Appendix F.5 also studies directly training a spiking MLP for g and the results remain about the same.", "section": "D. Datasets statistics", "sec_num": null}, {"text": "In short, the procedure of our model to deduce a triplet (x, q, y) commences with the propagation of spike trains across the graph to secure the pair representation h q (x, y), and subsequently, the likelihood score is computed by g, predicated on h q (x, y). When provided with the head entity x and the query relation r, the model is capable of concurrently computing pair representations and scores for all conceivable tail entities during the forward propagation of SNNs. The overarching procedure aligns with the conventional graph reasoning paradigm, with our primary focus being on the pivotal step of acquiring the pair representation through SNN propagation.", "section": "D. Datasets statistics", "sec_num": null}, {"text": "Regarding the training procedure, we adhere to the methodologies of preceding works (<PERSON><PERSON> et al., 2013; <PERSON> et al., 2019; <PERSON> et al., 2021) , generating negative samples by corrupting one entity in a positive triplet. The training objective is formulated to minimize the negative log-likelihood of both positive and negative triplets:", "section": "D. Datasets statistics", "sec_num": null}, {"text": "EQUATION", "section": "D. Datasets statistics", "sec_num": null}, {"text": ")", "section": "D. Datasets statistics", "sec_num": null}, {"text": "where m is the number of negative samples for each positive one, and (x ′ i , q, y ′ i ) denotes the i-th negative sample.", "section": "D. Datasets statistics", "sec_num": null}, {"text": "We illustrate the tasks of transductive knowledge graph completion and inductive relation prediction in Fig. 7 . For homogeneous graph link prediction, it is similar to transductive knowledge graph completion except that there is only one relation type in homogeneous graphs, i.e., the existence of the edge. ", "section": "E.2. Task Details", "sec_num": null}, {"text": "Datasets and preprocessing We assess our model across various tasks including transductive knowledge graph completion, inductive knowledge graph relation prediction, and homogeneous graph link prediction. For knowledge graphs, we employ the widely recognized FB15k-237 (Toutanova & Chen, 2015) and WN18RR (<PERSON><PERSON><PERSON> et al., 2018) , adhering to the standard transductive (Toutanova & Chen, 2015; <PERSON><PERSON><PERSON> et al., 2018) and inductive splits (<PERSON><PERSON> et al., 2020) . For homogeneous graphs, we utilize Cora, Citeseer, and PubMed (<PERSON> et al., 2008) .", "section": "E.3. Experimental Details", "sec_num": null}, {"text": "In evaluating knowledge graph completion, we adhere to the prevalent filtered ranking protocol (<PERSON><PERSON> et al., 2013) , ranking a test triplet (x, q, y) against all negative triplets (x, q, y ′ ) or (x ′ , q, y) absent in the graph (considering the likelihood score). We report MR, MRR, and HITS at N. For inductive knowledge graph relation prediction, we align with the previous practice (<PERSON><PERSON> et al., 2020) , drawing 50 negative triplets for each positive one using the aforementioned filtered ranking and report HITS@10. In the context of homogeneous graph link prediction, we follow the approaches of Kipf & Welling (2016) , contrasting the positive edges with an equivalent number of negative edges, and report AUROC and AP. The distribution of edges in train/valid/test is maintained at a ratio of 85:5:10, aligning with common practice. The specifics and statistics related to the datasets are available in Appendix D.", "section": "E.3. Experimental Details", "sec_num": null}, {"text": "Regarding data preprocessing, we adhere to the methodologies of prior works (<PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2019; Kipf & Welling, 2016) . In knowledge graphs, each triplet (x, q, y) is augmented with a reversed triplet (y, q -1 , x). In homogeneous graphs, each node is augmented with a self-loop. Additionally, we follow <PERSON> et al. (2021) to exclude edges directly connecting query node pairs during the training phase for the transductive setting of FB15k-237 and homogeneous graphs.", "section": "E.3. Experimental Details", "sec_num": null}, {"text": "Given the substantial computational expense associated with simulating SNNs over a long time, our primary simulations involve T = 10 discrete time steps for SNNs. The hyperparameters for SNNs are designated as τ m /∆τ = 4, V th = 2, with the discrete delay bound β = 4, and λ = 0.95 for the decoding function. This can correspond to τ m = τ c = 20ms with discretization interval ∆τ = 5ms and a total simulation time T × ∆τ = 50ms for SNNs. For experiments analyzing temporal discretization steps in Appendix F.3, hyperparameters are adjusted relative to the discrete step; for instance, for T = 5, we assign τ m /∆τ = 2, β = 2, λ = 0.9 (corresponding to ∆τ = 10ms), and for T = 20, we designate τ m /∆τ = 8, β = 8, λ = 0.97 (corresponding to ∆τ = 2.5ms). Each graph node is represented by n = 32 spiking neurons by default. No normalization or other modifications are applied, and for models on FB15k-237, a linear scale of 0.1 is applied post the linear transformation W.", "section": "Models and training", "sec_num": null}, {"text": "As for the two baseline SNN models that we compare in Section 4 to elucidate the superiority of synaptic delay, the first model abstains from encoding edge relations, and the delay d q r in Eq. ( 7) is not taken into account, i.e., it is assigned a value of zero. The second model opts for encoding relations through synaptic weight instead of synaptic delay. We modify s q k [t -d q r ] in Eq. ( 7) to w q r ⊙ s q k [t] (where w q r is defined analogously to d q r but devoid of the sigmoid function and bound scale, and w q r can be amalgamated into W to formulate the entire synaptic weight). This alteration aligns with the DistMult message function utilized in prior works to multiply messages with edge representations (<PERSON> et al., 2021) .", "section": "Models and training", "sec_num": null}, {"text": "For GRSNN+, we apply layer normalization (LN) after the linear transformation of the aggregated messages as in many GNNs, and encode relations in both synaptic delay and synaptic weight, i.e., the messages are w q r ⊙ s q k [t -d q r ]. For FB15k-237, we further adopt the principal neighborhood aggregation (PNA) as the aggregation function instead of summation, which is a major component for the high performance of NBFNet (<PERSON> et al., 2021) . We show that by integrating these GNN tricks, GRSNN can also achieve a better performance.", "section": "Models and training", "sec_num": null}, {"text": "All models are trained utilizing the Adam optimizer over 20 epochs. The learning rate is 2e -3 for transductive settings (knowledge graph completion and homogeneous graph link prediction) and 5e -3 for inductive settings. The batch size is 32 (30 for transductive FB15k-237), achieved by accumulating gradients across several iterations with smaller mini-batches each iteration.", "section": "Models and training", "sec_num": null}, {"text": "The ratio of negative samples is configured to 256 for FB15k-237 and WN18RR in the transductive setting and 50 in the inductive setting to align more closely with testing conditions, while it is established as 1 for homogeneous graphs, adhering to previous studies. The temperature in self-adversarial negative sampling is determined to be 0.5 and 1 for FB15k-237 and WN18RR, respectively. Model selection is based on validation performance, with MRR serving as the criterion for knowledge graphs and AUROC for homogeneous graphs.", "section": "Models and training", "sec_num": null}, {"text": "Our code implementation leverages the PyTorch framework, and experimental evaluations are executed on one or two NVIDIA GeForce RTX 3090 GPUs.", "section": "Models and training", "sec_num": null}, {"text": "For theoretical inference operation counts and energy estimations, we consider the scenario where neural network models are deployed and mapped directly to individual neurons and synapses. This scenario aligns with the principles of neuromorphic computing and hardware (<PERSON> et al., 2018; <PERSON><PERSON> et al., 2019; <PERSON> et al., 2022) , facilitating in-memory computation and minimizing energy-consuming memory exchanges. Our theoretical analysis predominantly centers on the operations of neurons and synapses, omitting additional hardware-related costs such as memory access.", "section": "Details of theoretical energy estimation", "sec_num": null}, {"text": "For the spiking model, the estimated synaptic operations are given by T × n 2 × f r × |E|, where T represents the discrete time step, n is the number of neurons allocated per graph node, f r denotes the spike rate, and |E| is the count of graph edges. This calculation corresponds to the quantity of synaptic operations instigated by spikes, culminating in an accumulation (addition) operation of post-synaptic current (or membrane potential). Additionally, accounting for neuron dynamics, there will be T ×n×|V| addition operations for the bias term, T ×n×|V| addition operations for the accumulation of membrane potential with current, and 2T × n × |V| multiplication operations due to the leakage of current and membrane potential, where |V| represents the number of graph nodes. The computational cost associated with spike generation and reset is omitted in this estimation. Consequently, the total operations involve 2T × n × |V| multiplications and T", "section": "Details of theoretical energy estimation", "sec_num": null}, {"text": "× n 2 × f r × |E| + 2T × n × |V| additions.", "section": "Details of theoretical energy estimation", "sec_num": null}, {"text": "For the non-spiking counterpart, assuming the replacement of spiking neurons with conventional artificial neurons and disregarding the computational cost of the activation function, the synaptic operations would involve T × n 2 × |E| MAC operations (multiplication + addition), along with T × n × |V| addition operations for the bias term. Thus, the total operations would encompass T × n 2 × |E| multiplications and T × n 2 × (|E| + |V|) additions. ", "section": "Details of theoretical energy estimation", "sec_num": null}, {"text": "× n × |V| × E M AC + (T × n 2 × f r × |E| + 2T × n × |V|) × E AC (", "section": "Costs of synaptic delay", "sec_num": null}, {"text": "analyzed above), the additional overhead is small because the number of synapses is much larger than the number of neurons (n 2 × |E| ≫ n × |V|) in our settings. Specifically, on the test set of FB15k-237, the originally analyzed energy is estimated as 1.337 mJ, while the additional energy for synaptic delay is estimated as 0.004 mJ, which is marginal. Query (x, q, y) : (58th academy awards nominees and winners, honored for, kiss of the spider woman (film))", "section": "Costs of synaptic delay", "sec_num": null}, {"text": "1.482 (58th academy awards nominees and winners, award winner, <PERSON>) ∧ (<PERSON>, film, kiss of the spider woman (film))", "section": "Costs of synaptic delay", "sec_num": null}, {"text": "1.347 (58th academy awards nominees and winners, award winner, <PERSON>) ∧ (<PERSON>, nominated for, kiss of the spider woman (film))", "section": "Costs of synaptic delay", "sec_num": null}, {"text": "Query (x, q, y) : (florida (rapper), profession, artiste) 0.513 (florida (rapper), award, grammy award for album of the year 2010s) ∧ (grammy award for album of the year 2010s, award -1 , kanye west) ∧ (kanye west, profession, artiste) 0.512 (florida (rapper), award, grammy award for album of the year 2010s) ∧ (grammy award for album of the year 2010s, award -1 , witney houston) ∧ (witney houston, profession, artiste) over extended periods, experiments primarily employ T = 10 discrete time steps for GRSNN. The results indicate that a reduced number of time steps (5) with a larger discretization interval significantly impairs performance due to discretization error, while a larger setting (20) with a smaller interval offers marginal improvements, maintaining comparable results to 10 time steps. This demonstrates the model's robustness under relatively low latency with minimal discrete time steps.", "section": "Costs of synaptic delay", "sec_num": null}, {"text": "As described in Appendix E.3, we take n = 32 spiking neurons for each graph node, which is consistent with many previous graph neural network works (<PERSON> et al., 2021) . To further study the impact of neuron number as well as the corresponding parameter amount, we explore results with varying neuron numbers per node in this section. As shown in Table 10 , the performance will grow as parameters increase and 32 neurons per node are not optimal. On the other hand, more neurons lead to a larger computational complexity, and there exists a trade-off between performance and complexity.", "section": "F.4. Impact of Neuron Number and Parameter Amount", "sec_num": null}, {"text": "As explained in Appendix E.1, we leverage a feedforward neural network g to estimate the conditional likelihood of the given pair representation h q (x, y), and following previous work (<PERSON> et al., 2021) , g is implemented as a two-layer MLP with ReLU activation. While this network can be converted to a spiking MLP through rate or temporal coding (<PERSON><PERSON> et al., 2017; Stöckl & Maass, 2021) to enable a fully spiking system, we further study if we can directly and jointly train a spiking network for this link prediction head. To this end, we replace the ReLU activation with a simple non-leaky Integrate and Fire neuron model combined with the simple current model (i.e., the input current is the linear combination with spikes without dynamics, I i (t) = j w ij s j (t) + b i ), consider rate coding over four discrete time steps, and use surrogate derivatives for training. As shown in Table 11 , the performance of spiking MLP is almost the same as that with ReLU activation because the major component of the task is to extract pair representations, which is done by the main GRSNN part.", "section": "F.5. Spiking Link Prediction Head", "sec_num": null}, {"text": "We investigate the error bar of our method on WN18RR based on three runs of experiments with different random seeds.", "section": "F.6. <PERSON><PERSON><PERSON> Bar", "sec_num": null}, {"text": "Table 12 shows that the variance of different runs is extremely small, indicating the robustness of the method.", "section": "F.6. <PERSON><PERSON><PERSON> Bar", "sec_num": null}, {"text": "Knowledge reasoning in human brains involves many neurophysiological processes across many brain areas, and how this is implemented is not fully understood. In this work, we mainly focus on neuro-inspired methods in AI task formulation to investigate how SNNs as computational models can deal with (knowledge) graph reasoning, while it can be future work to study better correspondence with neuroscience.", "section": "<PERSON><PERSON> Discussions", "sec_num": null}, {"text": "For neuromorphic hardware, on-chip memory limitation is an issue, so it may not directly support large-scale graphs. But neuromorphic hardware is rapidly developing for larger memories, for example, Intel's Loihi 2 can support 1 million neurons and 120 million synapses. While this may still not fully support 32 neurons per node for some knowledge graphs, but fewer neurons, such as 16, could be acceptable, and it works for our method with some trade-off for performance (as shown in", "section": "<PERSON><PERSON> Discussions", "sec_num": null}, {"text": "National Key Lab of General AI, School of Intelligence Science and Technology, Peking", "section": "", "sec_num": null}, {"text": "University 2 Institute for Artificial Intelligence,", "section": "", "sec_num": null}, {"text": "Peking University 3 Pazhou Laboratory (Huangpu), Guangzhou, China. Correspondence to: <PERSON><PERSON> <<EMAIL>>.", "section": "", "sec_num": null}, {"text": "Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "section": "", "sec_num": null}], "back_matter": [{"text": "<PERSON><PERSON> was supported by National Key R&D Program of China (2022ZD0160300), the NSF China (No. 62276004), and Qualcomm. D. He was supported by National Science Foundation of China (NSFC62376007).", "section": "Acknowledgements", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.", "section": "Impact Statement", "sec_num": null}, {"text": "Temporal Spiking Neural Networks with Synaptic Delay for Graph Reasoning Table 6 . Detailed Results for Transductive Knowledge Graph Completion. Lower values are preferable for MR, while higher values are preferable for MRR, HITS@1, HITS@3, and HITS@10. *SpikTE is an embedding method based on spiking neurons.", "section": "annex", "sec_num": null}, {"text": "Method FB15k-237 WN18RR MR↓ MRR↑ H@1↑ H@3↑ H@10↑ MR↓ MRR↑ H@1↑ H@3↑ H@10↑ Path-based Path Ranking (Lao & Cohen, 2010) ", "section": "Class", "sec_num": null}, {"text": "The methodology for visualizing reasoning paths in Appendix F.2 is elucidated below.The interpretation of reasoning is predicated on the significance of paths to the concluding prediction score. According to <PERSON> et al. (2021) , this significance or importance can be computed by the gradient of the prediction with respect to the paths, based on the local 1st-order Taylor expansion, and the path importance can be approximated by summing the importance of the edges in the path. This edge importance is computed using automatic differentiation. Specifically, during the forward procedure, the variable of edge weight (initialized to 1) is multiplied to the message transmitted through this edge (i.e., the delayed spikes, with 1 representing a spike and 0 representing no spike). Only when a spike is present will there be a gradient for this variable during backpropagation. Subsequently, during backpropagation, this variable accumulates the gradients of all neurons at every time step, representing the edge importance.For the non-differentiable spiking operation, a distinct surrogate gradient is employed for backpropagation. If the membrane potential u is below the threshold, the gradient is set to 0, as there is no output spike influencing other neurons. Conversely, if the membrane potential surpasses the threshold, the gradient is set as 1/u, normalizing the contribution of inputs to the output based on the membrane potential, as the gradient of the output is for spike 1.The top-k path importance is thus analogous to the top-k longest paths when considering edge importance. We adopt a beam search, as suggested by <PERSON> et al. (2021) , to identify these paths. It is crucial to note that this method provides only a rough approximation, and future research may explore more refined interpretative approaches.", "section": "Visualization of reasoning paths", "sec_num": null}, {"text": "In this section, we furnish detailed results for various experiments. The comprehensive result values for transductive knowledge graph completion are presented in Table 6 . For inductive relation prediction, the detailed results can be referred to in Table 7 . Lastly, the exhaustive result values for homogeneous graph link prediction are available in Table 8 .", "section": "F.1. Detailed Values of Main Results", "sec_num": null}, {"text": "The visualization of the reasoning paths for the final predictions of several examples are shown in Table 9 . It is calculated based on edge and path importance (refer to Appendix E.3). As shown in the results, GRSNN is adept at discerning relation relevances and exploiting transitions, for instance, \"contains\", and analogs, such as individuals with analogous \"award\".", "section": "F.2. Interpretability", "sec_num": null}, {"text": "The impact of temporal discretization with different intervals and steps on GRSNN is explored in Fig. 8 , and the details of hyperparameters are explained in Appendix E.3. Given the substantial computational cost associated with simulating SNNs Appendix F.4). As neuromorphic computing is a rapidly developing field considering both hardware and algorithm, this paper do not restrict algorithms to some existing hardware, but focuses on the algorithm level with theoretical analysis considering hardware (energy), aligning with previous SNNs works. Actually, algorithms can potentially guide future software-hardware co-design (<PERSON><PERSON> et al., 2022) . We hope this work could serve as a catalyst for deeper insights and wider applications of neuromorphic computing systems.", "section": "F.3. Impact of Discretization Steps", "sec_num": null}], "ref_entries": {"FIGREF0": {"uris": null, "num": null, "text": "Figure 1. Depiction of spiking neural networks and knowledge graph reasoning. (a) A representation of biological neural circuits, showcasing spiking neurons, their inherent dynamics, synaptic interconnections, and the propagation of temporal spike trains. (b) The process of relational reasoning of concepts, exemplified through the link prediction task in knowledge graphs.", "fig_num": "1", "type_str": "figure"}, "FIGREF1": {"uris": null, "num": null, "text": "Figure 2. Schematic of GRSNN. (a) Illustration of the graph link prediction task. In GRSNN, (b) each graph entity node is associated with a cluster of spiking neurons, and (c) each relational edge corresponds to the synaptic connections between spiking neurons, with synaptic weight and delay. The weight can exhibit positive or negative values. The delay is contingent on the edge relation and query relation, representing the edge's property and the neuromodulation from the task goal. (d) Visualization of GRSNN.To predict a link, a constant current, dependent on the query relation, is injected into the spiking neurons of the source node, initiating the propagation of spike trains. After a specific time interval, the spike trains emanating from the target node are decoded to predict the query relation. (e) Depiction of the temporal domain serving as an additional dimension to process the properties of edges and paths in a network with more propagation paths. In the demonstrated network under a simplified setting where each input spike triggers an output spike for neurons, a spike from the source neuron will lead to four spikes from the target neuron, whose time varies corresponding to four propagation paths with different integrated properties of edges represented by synaptic delay.", "fig_num": "2", "type_str": "figure"}, "FIGREF2": {"uris": null, "num": null, "text": "Figure3. Results of Transductive Knowledge Graph Completion on FB15k-237 and WN18RR. Lower values are preferable for MR, while higher values are desirable for MRR, HITS@1, and HITS@10. Detailed values can be found in Appendix F.1.", "fig_num": "3", "type_str": "figure"}, "FIGREF3": {"uris": null, "num": null, "text": "Figure 4. Analytical Results for GRSNN. (a) Log-scale comparison of the parameter quantities across different methods, demonstrating the enhanced parameter efficiency of GRSNN. (b) Theoretical estimations of the number of ADD and MUL operations (log scale) and energy consumption on FB15k-237. GRSNN can achieve approximately 20× energy reduction compared to its non-spiking counterpart.", "fig_num": "4", "type_str": "figure"}, "FIGREF4": {"uris": null, "num": null, "text": "Figure 5. Results of Inductive Relation Prediction on FB15k-237 and WN18RR. v1-v4 correspond to the four standard versions of inductive splits. Detailed values can be found in Appendix F.1.", "fig_num": "56", "type_str": "figure"}, "FIGREF5": {"uris": null, "num": null, "text": "Katz Index, Personalized PageRank, and Graph  Distance can be solved by GRSNN under specific settings.", "fig_num": null, "type_str": "figure"}, "FIGREF6": {"uris": null, "num": null, "text": "Figure 7. Illustration of task details. (a) Depiction of the transductive knowledge graph completion process. (b) Illustration of the filtered ranking protocol used to rank the test triplet (x, q, y) against all negative triplets absent from the graph. The triplets (x ′ , q, y) are not shown here for clarity. (c) Illustration of the inductive setting of relation prediction.", "fig_num": "7", "type_str": "figure"}, "FIGREF7": {"uris": null, "num": null, "text": "We consider the Ring Buffer for potential synaptic delay implementation as analyzed in<PERSON><PERSON><PERSON> et al. (2023), which is commonly used by digital neuromorphic platforms. The memory overhead of the ring buffer is the number of neurons multiplied by the maximum synaptic delay M d , and the energy overhead is equal to one extra neural accumulation per time step for each neuron(<PERSON><PERSON><PERSON> et al., 2023). Then, the additional memory overhead (words) is n × |V| × M d and the additional energy overhead is T × n × |V| × E AC . Consider the original memory overhead n × |V| × 2 + n 2 × |E| (neuron states + synapses) and the original energy overhead 2T", "fig_num": null, "type_str": "figure"}, "FIGREF8": {"uris": null, "num": null, "text": "Figure 8. Analysis of the temporal discretization of GRSNN under varying discrete time steps.", "fig_num": "8", "type_str": "figure"}, "TABREF1": {"num": null, "text": "Results of knowledge graph completion on FB15k-237 by SNNs with different methods to represent relation information.", "html": null, "content": "<table/>", "type_str": "table"}, "TABREF3": {"num": null, "text": "soning, illustrated in Table1. Our experiments contrast two baselines. The first baseline does not encode edge relations, focusing solely on the existence of edges. The second encodes edge relations with an additional relation-dependent term in synaptic weights, eschewing synaptic delay, reminiscent of the DistMult message function in GNN. More details are provided in the Appendix E.3. The results, presented in Table", "html": null, "content": "<table/>", "type_str": "table"}, "TABREF4": {"num": null, "text": "More spike rate statistics on different datasets.", "html": null, "content": "<table><tr><td colspan=\"2\">Trans. Know. Graph Compl.</td><td/><td/><td/><td colspan=\"2\">Induc. Relat. Pred.</td><td/><td/><td/><td colspan=\"3\">Homo. Graph Link Pred.</td></tr><tr><td>FB15k-237</td><td>WN18RR</td><td colspan=\"11\">FB. v1 FB. v2 FB. v3 FB. v4 WN. v1 WN. v2 WN. v3 WN. v4 Cora CiteSeer PubMed</td></tr><tr><td>0.258</td><td>0.191</td><td>0.176</td><td>0.189</td><td>0.257</td><td>0.245</td><td>0.175</td><td>0.165</td><td>0.172</td><td>0.149</td><td>0.082</td><td>0.074</td><td>0.189</td></tr></table>", "type_str": "table"}, "TABREF5": {"num": null, "text": "Transductive Knowledge Graph Completion Statistics for FB15k-237 and WN18RR.", "html": null, "content": "<table><tr><td>Dataset</td><td colspan=\"2\">#Entity #Relation</td><td>#Train</td><td>#Triplet #Validation</td><td># Test</td></tr><tr><td colspan=\"2\">FB15k-237 (Toutanova &amp; Chen, 2015) 14,541</td><td>237</td><td>272,115</td><td>17,535</td><td>20,466</td></tr><tr><td>WN18RR (<PERSON><PERSON><PERSON> et al., 2018)</td><td>40,943</td><td>11</td><td>86,835</td><td>3,034</td><td>3,134</td></tr></table>", "type_str": "table"}, "TABREF6": {"num": null, "text": "Inductive Relation Prediction Statistics for FB15k-237 and WN18RR.", "html": null, "content": "<table><tr><td>Dataset &amp; Split</td><td/><td>#Relation</td><td colspan=\"2\">Train #Entity #Query</td><td>#Fact</td><td colspan=\"2\">Validation #Entity #Query</td><td>#Fact</td><td colspan=\"2\">Test #Entity #Query</td><td>#Fact</td></tr><tr><td/><td>v1</td><td>180</td><td>1,594</td><td>4,245</td><td>4,245</td><td>1,594</td><td>489</td><td>4,245</td><td>1,093</td><td>205</td><td>1,993</td></tr><tr><td>FB15k-237 (Teru et al., 2020)</td><td>v2 v3</td><td>200 215</td><td>2,608 3,668</td><td colspan=\"2\">9,739 17,986 17,986 9,739</td><td>2,608 3,668</td><td>1,166 2,194</td><td>9,739 17,986</td><td>1,660 2,501</td><td>478 865</td><td>4,145 7,406</td></tr><tr><td/><td>v4</td><td>219</td><td>4,707</td><td colspan=\"2\">27,203 27,203</td><td>4,707</td><td>3,352</td><td>27,203</td><td>3,051</td><td>1,424</td><td>11,714</td></tr><tr><td/><td>v1</td><td>9</td><td>2,746</td><td>5,410</td><td>5,410</td><td>2,746</td><td>630</td><td>5,410</td><td>922</td><td>188</td><td>1,618</td></tr><tr><td>WN18RR (Teru et al., 2020)</td><td>v2 v3</td><td>10 11</td><td>6,954 12,078</td><td colspan=\"3\">15,262 15,262 25,901 25,901 12,078 6,954</td><td>1,838 3,097</td><td>15,262 25,901</td><td>2,757 5,084</td><td>441 605</td><td>4,011 6,327</td></tr><tr><td/><td>v4</td><td>9</td><td>3,861</td><td>7,940</td><td>7,940</td><td>3,861</td><td>934</td><td>7,940</td><td>7,084</td><td>1,429</td><td>12,334</td></tr></table>", "type_str": "table"}, "TABREF7": {"num": null, "text": "Homogeneous Graph Link Prediction Statistics for Cora, CiteSeer, and PubMed.", "html": null, "content": "<table><tr><td>Dataset</td><td>#Node</td><td>#Edge #Train #Validation # Test</td></tr><tr><td>Cora</td><td/><td/></tr></table>", "type_str": "table"}, "TABREF10": {"num": null, "text": "Detailed Results for Inductive Relation Prediction (HITS@10). v1-v4 correspond to the four standard versions of inductive splits.", "html": null, "content": "<table><tr><td>Class</td><td>Method</td><td>v1</td><td>FB15k-237 v2 v3</td><td>v4</td><td>v1</td><td>WN18RR v2 v3</td><td>v4</td></tr><tr><td/><td>NeuralLP (<PERSON> et al., 2017)</td><td colspan=\"6\">0.529 0.589 0.529 0.559 0.744 0.689 0.462 0.671</td></tr><tr><td>Path-based</td><td colspan=\"7\">DRUM (Sadeg<PERSON> et al., 2019) 0.529 0.587 0.529 0.559 0.744 0.689 0.462 0.671</td></tr><tr><td/><td>RuleN (<PERSON><PERSON><PERSON> et al., 2018)</td><td colspan=\"6\">0.498 0.778 0.877 0.856 0.809 0.782 0.534 0.716</td></tr><tr><td>GNNs</td><td>GraIL (Teru et al., 2020) NBFNet (Zhu et al., 2021)</td><td colspan=\"6\">0.642 0.818 0.828 0.893 0.825 0.787 0.584 0.734 0.834 0.949 0.951 0.960 0.948 0.905 0.893 0.890</td></tr><tr><td>SNNs</td><td>GRSNN (ours)</td><td colspan=\"6\">0.852 0.957 0.958 0.958 0.943 0.892 0.906 0.888</td></tr></table>", "type_str": "table"}, "TABREF11": {"num": null, "text": "Detailed Results for Homogeneous Graph Link Prediction.", "html": null, "content": "<table><tr><td>Class</td><td>Method</td><td>Cora AUROC↑</td><td>AP↑</td><td colspan=\"2\">Citeseer AUROC↑</td><td>AP↑</td><td colspan=\"2\">PubMed AUROC↑</td><td>AP↑</td></tr><tr><td>Path-based</td><td>Katz Index (Katz, 1953) Personalized PageRank (Page et al., 1999)</td><td>0.834 0.845</td><td>0.889 0.899</td><td>0.768 0.762</td><td colspan=\"2\">0.810 0.814</td><td>0.757 0.763</td><td>0.856 0.860</td></tr><tr><td/><td>DeepWalk (<PERSON><PERSON><PERSON> et al., 2014)</td><td>0.831</td><td>0.850</td><td>0.805</td><td colspan=\"2\">0.836</td><td>0.844</td><td>0.841</td></tr><tr><td>Embeddings</td><td>LINE (Tang et al., 2015)</td><td>0.844</td><td>0.876</td><td>0.791</td><td colspan=\"2\">0.826</td><td>0.849</td><td>0.888</td></tr><tr><td/><td>node2vec (Grover &amp; Leskovec, 2016)</td><td>0.872</td><td>0.879</td><td>0.838</td><td colspan=\"2\">0.868</td><td>0.891</td><td>0.914</td></tr><tr><td/><td>VGAE (Kipf &amp; Welling, 2016)</td><td>0.914</td><td>0.926</td><td>0.908</td><td colspan=\"2\">0.920</td><td>0.944</td><td>0.947</td></tr><tr><td/><td>S-VGAE (Davidson et al., 2018)</td><td>0.941</td><td>0.941</td><td>0.947</td><td colspan=\"2\">0.952</td><td>0.960</td><td>0.960</td></tr><tr><td>GNNs</td><td>SEAL (Zhang &amp; Chen, 2018)</td><td>0.933</td><td>0.942</td><td>0.905</td><td colspan=\"2\">0.924</td><td>0.978</td><td>0.979</td></tr><tr><td/><td>TLC-GNN (Yan et al., 2021)</td><td>0.934</td><td>0.931</td><td>0.909</td><td colspan=\"2\">0.916</td><td>0.970</td><td>0.968</td></tr><tr><td/><td>NBFNet (Zhu et al., 2021)</td><td>0.956</td><td>0.962</td><td>0.923</td><td colspan=\"2\">0.936</td><td>0.983</td><td>0.982</td></tr><tr><td>SNNs</td><td>GRSNN (ours)</td><td>0.936</td><td>0.945</td><td>0.915</td><td colspan=\"2\">0.931</td><td>0.982</td><td>0.982</td></tr></table>", "type_str": "table"}, "TABREF12": {"num": null, "text": "Visualization of the top-2 reasoning paths for examples on FB15k237. It is determined by path importances derived from edge importances. The superscript -1 indicates the inverse relation.", "html": null, "content": "<table><tr><td colspan=\"2\">Query (x, q, y) : (england, contains, pontefract)</td></tr><tr><td>0.967</td><td>(england, contains, west yorkshire) ∧ (west yorkshire, contains, pontefract)</td></tr><tr><td>0.671</td><td/></tr></table>", "type_str": "table"}, "TABREF13": {"num": null, "text": "Analysis results of GRSNN with varying neuron number per graph node on WN18RR.", "html": null, "content": "<table><tr><td colspan=\"7\">Neuron number per node Parameters MR↓ MRR↑ H@1↑ H@3↑ H@10↑</td></tr><tr><td>8</td><td>1.9K</td><td>967</td><td>0.452</td><td>0.405</td><td>0.464</td><td>0.551</td></tr><tr><td>16</td><td>6.9K</td><td>819</td><td>0.483</td><td>0.430</td><td>0.500</td><td>0.597</td></tr><tr><td>32</td><td>26K</td><td>720</td><td>0.508</td><td>0.455</td><td>0.528</td><td>0.616</td></tr><tr><td>64</td><td>101K</td><td>668</td><td>0.522</td><td>0.469</td><td>0.544</td><td>0.632</td></tr><tr><td>96</td><td>226K</td><td>648</td><td>0.523</td><td>0.470</td><td>0.546</td><td>0.630</td></tr></table>", "type_str": "table"}}}}