{"paper_id": "SPIN", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:38:04.461321Z"}, "title": "Self-Play Fine-Tuning Converts Weak Language Models to Strong Language Models", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Univer-sity of California", "location": {"postCode": "90095", "settlement": "Los Angeles", "region": "CA", "country": "USA"}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Univer-sity of California", "location": {"postCode": "90095", "settlement": "Los Angeles", "region": "CA", "country": "USA"}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Yuan", "suffix": "", "affiliation": {"laboratory": "", "institution": "Univer-sity of California", "location": {"postCode": "90095", "settlement": "Los Angeles", "region": "CA", "country": "USA"}}, "email": ""}, {"first": "Kaixuan", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Univer-sity of California", "location": {"postCode": "90095", "settlement": "Los Angeles", "region": "CA", "country": "USA"}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>u", "suffix": "", "affiliation": {"laboratory": "", "institution": "Univer-sity of California", "location": {"postCode": "90095", "settlement": "Los Angeles", "region": "CA", "country": "USA"}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Harnessing the power of human-annotated data through Supervised Fine-Tuning (SFT) is pivotal for advancing Large Language Models (LLMs). In this paper, we delve into the prospect of growing a strong LLM out of a weak one without the need for acquiring additional human-annotated data. We propose a new fine-tuning method called Self-Play fIne-tuNing (SPIN), which starts from a supervised fine-tuned model. At the heart of SPIN lies a self-play mechanism, where the LLM refines its capability by playing against instances of itself. More specifically, the LLM generates its own training data from its previous iterations, refining its policy by discerning these self-generated responses from those obtained from human-annotated data. Our method progressively elevates the LLM from a nascent model to a formidable one, unlocking the full potential of human-annotated demonstration data for SFT. Theoretically, we prove that the global optimum to the training objective function of our method is achieved only when the LLM policy aligns with the target data distribution. Empirically, we evaluate our method on several benchmark datasets including the HuggingFace Open LLM Leaderboard, MT-Bench, and datasets from Big-Bench. Our results show that SPIN can significantly improve the LLM's performance across a variety of benchmarks and even outperform models trained through direct preference optimization (DPO) supplemented with extra GPT-4 preference data. This sheds light on the promise of self-play, enabling the achievement of humanlevel performance in LLMs without the need for expert opponents. Codes are available at https://github.com/uclaml/SPIN.", "pdf_parse": {"paper_id": "SPIN", "_pdf_hash": "", "abstract": [{"text": "Harnessing the power of human-annotated data through Supervised Fine-Tuning (SFT) is pivotal for advancing Large Language Models (LLMs). In this paper, we delve into the prospect of growing a strong LLM out of a weak one without the need for acquiring additional human-annotated data. We propose a new fine-tuning method called Self-Play fIne-tuNing (SPIN), which starts from a supervised fine-tuned model. At the heart of SPIN lies a self-play mechanism, where the LLM refines its capability by playing against instances of itself. More specifically, the LLM generates its own training data from its previous iterations, refining its policy by discerning these self-generated responses from those obtained from human-annotated data. Our method progressively elevates the LLM from a nascent model to a formidable one, unlocking the full potential of human-annotated demonstration data for SFT. Theoretically, we prove that the global optimum to the training objective function of our method is achieved only when the LLM policy aligns with the target data distribution. Empirically, we evaluate our method on several benchmark datasets including the HuggingFace Open LLM Leaderboard, MT-Bench, and datasets from Big-Bench. Our results show that SPIN can significantly improve the LLM's performance across a variety of benchmarks and even outperform models trained through direct preference optimization (DPO) supplemented with extra GPT-4 preference data. This sheds light on the promise of self-play, enabling the achievement of humanlevel performance in LLMs without the need for expert opponents. Codes are available at https://github.com/uclaml/SPIN.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Large Language Models (LLMs) have began a groundbreaking era in artificial general intelligence (AGI), demonstrating extraordinary capabilities across a wide range of domains that require intricate reasoning and specialized knowledge. These models excel in areas such as mathematical reasoning/problem solving (<PERSON><PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022) , code generation/programming (<PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON> et al., 2022 ), text generation (<PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2023) , summarization and creative writing, among others. A significant advancement in LLMs is the post-pre-training alignment with the more desirable behaviors (<PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2022) , a process often reliant on the costly human-annotated data. Typical alignment methods include Supervised Fine-Tuning (SFT) (<PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2023a) based on human demonstrations, and Reinforcement Learning from Human Feedback (RLHF) (<PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2022a) based on human preferences.", "cite_spans": [{"start": 310, "end": 330, "text": "(<PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF20"}, {"start": 331, "end": 348, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF89"}, {"start": 349, "end": 372, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF48"}, {"start": 403, "end": 422, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF13"}, {"start": 423, "end": 443, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF3"}, {"start": 444, "end": 459, "text": "<PERSON> et al., 2022", "ref_id": "BIBREF49"}, {"start": 479, "end": 500, "text": "(<PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF11"}, {"start": 501, "end": 519, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF0"}, {"start": 520, "end": 541, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF83"}, {"start": 697, "end": 718, "text": "(<PERSON><PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF58"}, {"start": 719, "end": 739, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF87"}, {"start": 740, "end": 759, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF17"}, {"start": 760, "end": 783, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF82"}, {"start": 909, "end": 930, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF63"}, {"start": 931, "end": 954, "text": "<PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}, {"start": 1040, "end": 1065, "text": "(<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF16"}, {"start": 1066, "end": 1087, "text": "<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF100"}, {"start": 1088, "end": 1110, "text": "<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF79"}, {"start": 1111, "end": 1129, "text": "<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "All the aforementioned alignment methods require a substantial volume of human annotated data. Therefore, there is increasing interest in developing fine-tuning methods that can effectively utilize human data, thereby streamlining the alignment process. This motivates us to study fine-tuning LLMs without the need for additional human-annotated data beyond the fine-tuning dataset. Our study is also related to the broader goal of converting weak models to strong models without the requirement for extra training data, which is of central interest in machine learning that can be traced back to the boosting algorithms (Kearns & Valiant, 1994; Scha<PERSON>, 1990; Freund, 1995; Freund & Schapire, 1997) . The self-training algorithm (<PERSON>ap<PERSON>, 1999; Grandvalet & Bengio, 2004; Lee, 2013) has also been proved to be able to convert weak learners to strong learners in mixture models without the need for additional labeled data (<PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022) . However, the pursuit of autonomously enhancing a weak LLM without external guidance is both intriguing and understudied. This raises the following question:", "cite_spans": [{"start": 621, "end": 645, "text": "(Kearns & Valiant, 1994;", "ref_id": "BIBREF41"}, {"start": 646, "end": 661, "text": "<PERSON><PERSON><PERSON>, 1990;", "ref_id": "BIBREF73"}, {"start": 662, "end": 675, "text": "<PERSON><PERSON><PERSON>, 1995;", "ref_id": "BIBREF26"}, {"start": 676, "end": 700, "text": "Freund & Schapire, 1997)", "ref_id": "BIBREF27"}, {"start": 731, "end": 745, "text": "(<PERSON>ap<PERSON>, 1999;", "ref_id": "BIBREF86"}, {"start": 746, "end": 772, "text": "Grandvalet & Bengio, 2004;", "ref_id": "BIBREF31"}, {"start": 773, "end": 783, "text": "<PERSON>, 2013)", "ref_id": "BIBREF45"}, {"start": 923, "end": 942, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF25"}, {"start": 943, "end": 960, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Can we empower a weak LLM to improve itself without acquiring additional human annotated data?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In this paper, we answer this question affirmatively. Inspired by the success of self-play mechanisms (<PERSON>, 2000) in games, exemplified by AlphaGo Zero (<PERSON> et al., 2017b) , AlphaZero (<PERSON> et al., 2017a) , with historical roots traced back to <PERSON>-<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> et al., 1995) , we propose to convert a weak LLM to a strong one through the lens of self-play, where the model is enhanced by playing against itself without requiring any direct supervision. In particular, we propose a novel fine-tuning method called Self-Play fIne-tuNing (SPIN), which begins from a supervised finetuned model. SPIN allows the LLM to engage in self-play, eliminating the need for an expert annotator such as a human or more advanced LLMs like GPT-4. In detail, with the LLM from previous iteration t denoted by p θt , we employ it to generate responses y ′ to the prompts x in the human-annotated SFT dataset. The subsequent objective is to find a new LLM p θt+1 , capable of distinguishing the responses y ′ generated by p θt from the responses y generated by humans. This process can be seen as a two-player game: the main player, or the new LLM p θt+1 , seeks to discern between the responses of the opponent player p θt and human-generated responses, while the opponent, or the old LLM p θt , generates responses as similar as possible to those in the human-annotated SFT dataset. The new LLM p θt+1 is obtained by fine-tuning the old one p θt to prefer responses from p data over p θt , resulting in a distribution p θt+1 that is more aligned with p data . In the next iteration, the newly obtained LLM p θt+1 becomes the opponent for response generation, with the self-play process aiming for the LLM to eventually converge to p θ * = p data , so that the strongest possible LLM can no longer differentiate the responses generated by its previous version and those generated by the human.", "cite_spans": [{"start": 102, "end": 116, "text": "(<PERSON>, 2000)", "ref_id": "BIBREF71"}, {"start": 155, "end": 177, "text": "(<PERSON> et al., 2017b)", "ref_id": null}, {"start": 190, "end": 212, "text": "(<PERSON> et al., 2017a)", "ref_id": null}, {"start": 262, "end": 284, "text": "(<PERSON><PERSON><PERSON> et al., 1995)", "ref_id": "BIBREF81"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Interestingly, our method exhibits similarity with the recently introduced direct preference optimization (DPO) method (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , with the notable distinction being the self-play nature of our method. Consequently, our approach stands out by eliminating the need for extra human preference data, a requirement present in the DPO method. Additionally, the self-play mechanism in our method resembles the idea of generative adversarial networks (GAN) (<PERSON><PERSON><PERSON> et al., 2014; <PERSON><PERSON><PERSON><PERSON> et al., 2017) , albeit that both the discriminator (main player) and the generator (the opponent) in our method are instances of the same LLM from different iterations. Theoretically, we prove that our method converges when the distribution of the LLM is identical to the target data distribution, i.e., p θt = p data . Our experimental results on zephyr-7b-sft-full (<PERSON><PERSON><PERSON> et al., 2023a) , a finetuned LLM based on Mistral-7B (<PERSON> et al., 2023) , show that while continued training using SFT on its own SFT dataset Ultrachat200k (<PERSON><PERSON> et al., 2023) reaches a performance plateau or even diminished evaluation scores, our method consistently improves zephyr-7b-sft-full across successive iterations while leveraging only a 50k subset of Ultrachat200k dataset. Ultimately, SPIN effectively improves the base model's average score from 58.14 to 63.16 on the HuggingFace Open LLM Leaderboard (<PERSON><PERSON> et al., 2023) with remarkable 10%+ improvement in scores on GSM8k and TruthfulQA, and from 5.94 to 6.78 on MT-Bench (Zheng et al., 2023) . Notably, SPIN achieves results that are even comparable to models trained on additional 62k preference dataset (Tunstall et al., 2023a) Notation. We use lowercase letters and lowercase boldface letters to denote scalars and vectors, respectively. We use [N ] to denote the index set {1, . . . , N }. In the function space, let F be the function class. The symbol q data designates the target data distribution, while p represents the conditional probability of LLM's response (i.e., LLM policy).", "cite_spans": [{"start": 119, "end": 142, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF66"}, {"start": 464, "end": 489, "text": "(<PERSON><PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF30"}, {"start": 490, "end": 512, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF2"}, {"start": 866, "end": 890, "text": "(<PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}, {"start": 929, "end": 949, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF38"}, {"start": 1034, "end": 1053, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF24"}, {"start": 1393, "end": 1416, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}, {"start": 1519, "end": 1539, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF99"}, {"start": 1653, "end": 1677, "text": "(<PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Self-Play. Self-play (<PERSON>, 1959; <PERSON><PERSON><PERSON> et al., 1995) , where the algorithm learns by playing against itself, has gained notable attention due to its effectiveness in multiagent reinforcement learning (MARL). This method involves agents engaging in interactions with copies of themselves, enabling an increasing level of challenge and complexity within the learning environment. A fundamental work in the field of self-play is AlphaGo Zero (<PERSON> et al., 2017b) , which demonstrated exceptional performance against human players using a self-play learning scheme. Subsequent research has expanded upon the concept of self-play, exploring various adaptations and implementations (<PERSON> et al., 2017; <PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON> et al., 2018; <PERSON> et al., 2018; <PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2019) . Our method takes the self-play approach akin to AlphaGo Zero, which can convert a weak model to a strong one without additional human-annotated data. While the effectiveness of self-play in MARL is wellestablished, to our knowledge, our work is the first to apply this approach to the enhancement of LLMs.", "cite_spans": [{"start": 21, "end": 35, "text": "(<PERSON>, 1959;", "ref_id": "BIBREF70"}, {"start": 36, "end": 57, "text": "<PERSON><PERSON><PERSON> et al., 1995)", "ref_id": "BIBREF81"}, {"start": 444, "end": 466, "text": "(<PERSON> et al., 2017b)", "ref_id": null}, {"start": 683, "end": 705, "text": "(<PERSON> et al., 2017;", "ref_id": "BIBREF1"}, {"start": 706, "end": 727, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF44"}, {"start": 728, "end": 748, "text": "<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF6"}, {"start": 749, "end": 777, "text": "<PERSON> et al., 2018;", "ref_id": "BIBREF35"}, {"start": 778, "end": 798, "text": "<PERSON> et al., 2019;", "ref_id": "BIBREF61"}, {"start": 799, "end": 820, "text": "<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Synthetic Data for LLMs. In the context of supervised fine-tuning (SFT) of LLMs, human-crafted data has proven to be a remarkably effective source that enhances the performance of LLMs on tasks such as code generation (<PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) and mathematical reasoning (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) . While human data typically exhibits high quality, acquiring sufficient amount of such data poses a challenge in cost. In light of this consideration, the use of synthetic data has become increasingly popular and considered as a proxy for human data. This approach primarily leverages advanced LLMs such as the GPT series (<PERSON><PERSON> et al., 2019; <PERSON> et al., 2020; OpenAI, 2023) as the guidance to generate high-quality data (<PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON> et al., 2023) . Recent research has also highlighted the rephrasing capability of LLMs in prompting for better LLM response (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) as well as augmenting synthetic data for more effective SFT (<PERSON> et al., 2023; <PERSON> et al., 2023) . In contrast to prior studies that utilized more advanced models for synthetic data generation when pre-training or fine-tuning a target model, our approach directly generates synthetic data from the target model itself.", "cite_spans": [{"start": 218, "end": 240, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF68"}, {"start": 241, "end": 259, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF92"}, {"start": 287, "end": 306, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF95"}, {"start": 307, "end": 324, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF55"}, {"start": 648, "end": 670, "text": "(<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF65"}, {"start": 671, "end": 690, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF10"}, {"start": 691, "end": 704, "text": "OpenAI, 2023)", "ref_id": null}, {"start": 751, "end": 775, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF40"}, {"start": 776, "end": 795, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF80"}, {"start": 796, "end": 816, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF15"}, {"start": 817, "end": 833, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF50"}, {"start": 944, "end": 963, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF23"}, {"start": 964, "end": 984, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF64"}, {"start": 1045, "end": 1062, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF93"}, {"start": 1063, "end": 1080, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF52"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "We consider a Large Language Model (LLM) parameterized by θ and denoted by p θ . The model takes as input a sequence x = [x 1 , . . . , x n ], commonly referred to as the prompt, to generate the corresponding response y = [y 1 , . . . , y m ]. The response y is therefore considered as a sample from the conditional probability distribution p θ (•|x). In LLMs, x i and y j represent individual tokens from a predetermined vocabulary within the sequences x and y, respectively. The auto-regressive model p θ generates tokens sequentially for a given position, leveraging only the sequence of previously generated tokens. This model therefore constitutes a Markov process, where the conditional probability distribution p θ (y|x) can be expressed through a decomposition as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setting and Preliminaries", "sec_num": "3"}, {"text": "p θ (y|x) = m j=1 p θ (y j |x, y <j ),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setting and Preliminaries", "sec_num": "3"}, {"text": "where y <1 is null and y <j = [y 1 , . . . , y j-1 ] for j = 2, . . . , m. In the following, we review two major fine-tuning methods for LLMs: supervised fine-tuning and reinforcement learning (RL) fine-tuning.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setting and Preliminaries", "sec_num": "3"}, {"text": "Supervised fine-tuning (SFT) is employed to tailor a pretrained LLM to specific downstream tasks, leveraging relatively smaller dataset of labeled examples in comparison to the large-scale pre-training data (<PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2023) . In this context, we consider a specific task where the prompts, denoted by x, are derived from a specified distribution q(•). The notation p data (•|x) then represents the probability distribution of the associated highquality responses y from the training data. Consequently, SFT involves training the LLM to minimize the following negative log-likelihood loss associated with these distributions,", "cite_spans": [{"start": 207, "end": 228, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF63"}, {"start": 229, "end": 245, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF93"}], "ref_spans": [], "eq_spans": [], "section": "Supervised Fine-Tuning", "sec_num": "3.1"}, {"text": "L SFT (θ) = -E x∼q(•),y∼p data (•|x) log p θ y|x . (3.1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervised Fine-Tuning", "sec_num": "3.1"}, {"text": "It should be noted that excluding x ∼ q(•) from the expectation term yields the typical cross-entropy loss, expressed as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervised Fine-Tuning", "sec_num": "3.1"}, {"text": "-E y∼p data (•|x) [log p θ (y|x)].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervised Fine-Tuning", "sec_num": "3.1"}, {"text": "L SFT (θ) attains its minimum when the model's predictive distribution p θ (y|x) aligns perfectly with the distribution of the labeled high-quality responses p data (y|x).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervised Fine-Tuning", "sec_num": "3.1"}, {"text": "Consequently, the LLM after SFT is anticipated to generate responses that closely resemble those from p data (y|x). This procedure is therefore expected to significantly enhance the model's performance in generating appropriate responses for a specific task.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervised Fine-Tuning", "sec_num": "3.1"}, {"text": "RL fine-tuning (<PERSON><PERSON> et al., 2017; <PERSON> et al., 2022a; <PERSON> et al., 2023a) offers another method for enhancing the specific capabilities of general-purpose pre-trained models. Typically, RL fine-tuning is employed subsequent to SFT to achieve improved alignment for LLMs (<PERSON><PERSON><PERSON> et al., 2023a) .", "cite_spans": [{"start": 15, "end": 40, "text": "(<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF16"}, {"start": 41, "end": 59, "text": "<PERSON> et al., 2022a;", "ref_id": null}, {"start": 60, "end": 78, "text": "<PERSON> et al., 2023a)", "ref_id": null}, {"start": 275, "end": 299, "text": "(<PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "RL Fine-Tuning", "sec_num": "3.2"}, {"text": "For a given sequence pair (x, y), RL fine-tuning necessitates a deterministic reward function r(x, y). The higher the reward r(x, y), the better the response y is to the given prompt x. The objective of the RL fine-tuning process is then to maximize the following objective function:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RL Fine-Tuning", "sec_num": "3.2"}, {"text": "L RL (θ) = E x∼q(•),y∼p θ (•|x) [r(x, y)] -λE x∼q(•) KL p θ (•|x)||p ref (•|x) ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RL Fine-Tuning", "sec_num": "3.2"}, {"text": "where (<PERSON><PERSON> et al., 2017; <PERSON> et al., 2022a) or strong AI agents, i.e., reinforcement learning from AI feedback (RLAIF) (<PERSON> et al., 2022b) .", "cite_spans": [{"start": 6, "end": 31, "text": "(<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF16"}, {"start": 32, "end": 50, "text": "<PERSON> et al., 2022a)", "ref_id": null}, {"start": 126, "end": 145, "text": "(<PERSON> et al., 2022b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "RL Fine-Tuning", "sec_num": "3.2"}, {"text": "In this section, we introduce a new fine-tuning method for enhancing the performance of LLMs without relying on additional human or AI feedback. Consider a high-quality supervised fine-tuning (SFT) dataset S SFT = {(x, y)} n i=1 , which are sampled from the marginal distribution q(x) and p data (y|x). Given a supervised fine-tuned LLM p θ0 , further application of the SFT approach in (3.1) with S SFT will be ineffective and potentially lead to worse performance. In addition, without human and/or AI feedback, it becomes infeasible to acquire a preference dataset for RL fine-tuning (e.g., RLHF and RLAIF). This hinders the application of RL fine-tuning techniques.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "4"}, {"text": "We evaluate p θ0 against S SFT , where p θ0 is the LLM achieved by SFT using (3.1). We notice a persistent quality gap between the groundtruth response y from S SFT and the LLM-generated response y ′ ∼ p θ (•|x) (refer to Figure 1 ). This disparity indicates that there is still room for improvement over p θ0 . Therefore, we propose to use the synthetic data generated by the LLM to enhance LLM's performance starting from p θ0 iteratively.", "cite_spans": [], "ref_spans": [{"start": 229, "end": 230, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Method", "sec_num": "4"}, {"text": "Let us consider a two-player game, where the main player's objective is to distinguish the responses generated by the LLM and those generated by the human. Meanwhile, the opponent's role is to generate responses that are indistinguishable from the human's responses. The core of our method is the self-play mechanism, where both the main player and the opponent are the same LLM, but from different iterations. More specifically, the opponent is the old LLM from the previous iteration, and the main player is the new LLM to be learned in the current iteration.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "In iteration t+1, the opponent is the LLM from the previous iteration, denoted by p θt , which generates responses y ′ for those prompts x in the SFT dataset according to p θt (•|x). Our method, therefore, consists of the following two steps at iteration t+1: (1) training the main player, and (2) updating the opponent player.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "Training the Main Player. We begin with illustrating how we expect a main player is trained to distinguish LLM responses from human responses. Motivated by integral probability metric (IPM) (Müller, 1997) , we formulate our objective function such that the main player f t+1 maximizes the expected value gap between the target data distribution p data and the opponent player's distribution p θt :", "cite_spans": [{"start": 190, "end": 204, "text": "(<PERSON>, 1997)", "ref_id": "BIBREF60"}], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "f t+1 = argmax f ∈Ft E f (x, y) -f (x, y ′ ) , (4.1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "where the expectation is computed over the distributions", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "x ∼ q(•), y ∼ p data (•|x), y ′ ∼ p θt (•|x)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": ", and F t is a sequence of highly expressive function classes that we will determine in later deduction. The subscript t in F t is due to that the function class is dependent on p θt . Given such a f t+1 and a response sequence y to the prompt x, the value of f t+1 (x, y) reflects the main player's degree of belief that y originates from p data rather than p θt . Ideally, the main player f t+1 should yield a high value when y ∼ p data (•|x) and a low value when y ′ ∼ p θt (•|x), where p θt is the opponent's distribution. Instead of solving (4.1), we can also solve the following more general optimization problem,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "f t+1 = argmin f ∈Ft E ℓ f (x, y) -f (x, y ′ ) , (4.2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "where the expectation is computed over the distribution", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "x ∼ q(•), y ∼ p data (•|x), y ′ ∼ p θt (•|x)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": ", and ℓ(•) is a loss function that is both monotonically decreasing and convex. For example, a linear loss function ℓ(t) = -t reduces (4.2) to the minimization version of (4.1). However, the use of a linear loss function results in an unbounded objective value, which, during continuous training, leads to a negative infinite value of f (x, y ′ ) on the opponent player's responses. Therefore, in our work, we choose the logistic loss function ℓ(t) := log(1+exp(-t)) for its non-negativity, smoothness, and exponentially decaying tail as t → ∞. Such a choice of loss function aids in preventing the excessive growth in the absolute value of f .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "Updating the Opponent Player. Previously we have discussed the training of f t+1 given the opponent player's distribution p θt . Now suppose we have optimized our main player f t+1 that can distinguish p data from p θt , within a certain function class F t , we elaborate how we get parameter θ t+1 of the opponent player. Specifically, when presented with two responses y and y ′ to the same prompt x, f t+1 assesses the values f t+1 (x, y) and f t+1 (x, y ′ ). It then infers that the response with the higher value is from the real data distribution p data and the response with lower value is attributed to the LLM p θt . Subsequently, the objective of the opponent player is to find a better LLM that generates responses indistinguishable from p data for the main player. This is achieved by maximizing the expected value", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "E x∼q(•),y∼p(•|x) [f t+1 (x, y)].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "In addition, to prevent excessive deviation of p θt+1 from p θt and stabilize the self-play, we incorporate a Kullback-Leibler (KL) regularization term.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "Putting these together gives rise to the following optimization problem:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "argmax p E x∼q(•),y∼p(•|x) [f t+1 (x, y)]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "In Southampton, what is the most popular form of transportation for commuters?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "I don't have access to current data. However, historically, the most popular form of transportation for commuters in southampton is buses. The city also has a good network of trains and taxis.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "In Southampton, what is the most popular form of transportation for commuters? 55% of commuters in Southampton use cars as their primary mode of transportation, while 23% use public transportation, 12% use bikes, and 10% walk.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "Chosen: ground truth completion Rejected: model generation @Iter 0 I do not have access to the latest updates and statistics of southampton. However, based on previous reports, the most popular form of transportation for commuters in southampton is the public transport network, which includes buses, trains, and ferries. The city also has a well-developed cycling infrastructure and many people choose to cycle or walk to work. Additionally, private cars and taxis are also commonly used for commuting.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Self-Play Fine-Tuning (SPIN)", "sec_num": "4.1"}, {"text": "Figure 1 . Example of ground truth completion compared to the fine-tuned model generation at iteration 0 and 1. We can observe that the model generation at iteration 0, although fluent, incorrectly quantifies transportation preferences with specific percentages that are potentially hallucinations. The model generation at iteration 1 provides a qualitative summary of the transportation forms at Southampton without specific percentage, aligning more closely with the ground truth while adding more details.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "-", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "λE x∼q(•) KL p(•|x)||p θt (•|x) , (4.3)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "where λ > 0 is the regularization parameter. Notably, (4.3) has a closed-form solution p(•|x):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "p(y|x) ∝ p θt (y|x) exp λ -1 f t+1 (x, y) . (4.4)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "It is worth noting that p(•|x) is not guaranteed to be belong to the LLM space {p θ (•|x)|θ ∈ Θ}. Since we hope that the closed-form solution p in the probability space can be realized by an LLM with parameter θ, i.e., p θ (y|x) = p(y|x), solving for p θ (y|x) ∝ p θt (y|x) exp λ -1 f t+1 (x, y) •|x) . This suggests the following function class F t for f t+1 :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "gives f t+1 (x, y) = λ • log p θ (•|x) p θ t (", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "F t = λ • log p θ (y|x) p θt (y|x) θ ∈ Θ , (4.5)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "where Θ is the parameter space of LLMs being considered.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "Given the choice of F t in (4.5), optimizing (4.2) gives f t+1 parameterized by θ t+1 in the following form:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "f t+1 (x, y) = λ • log p θt+1 (y|x) p θt (y|x) . (4.6)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "Substituting (4.6) into (4.4) yields p(y|x) = p θt+1 (y|x). In other words, θ t+1 learned from (4.2) is exactly the LLM parameter for our ideal opponent selection.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "End-to-end Training Objective. We integrate the previously discussed two steps into a single end-to-end training objective with an update rule of θ t+1 . Specifically, plugging (4.5) into (4.2) arrives at the update rule θ t+1 = argmin θ∈Θ L SPIN (θ, θ t ), where L SPIN is the training objective defined as follows", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L SPIN = E ℓ λ log p θ (y|x) p θt (y|x) -λ log p θ (y ′ |x) p θt (y ′ |x) ,", "eq_num": "(4.7)"}], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "where the expectation is computed over the distribution x ∼ q(•), y ∼ p data (•|x), y ′ ∼ p θt (•|x). We summarize the iterative self-play process of our method SPIN as follows,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": ". . . → p θt (•|x) Opponent Player at t → λ • log p θt+1 (•|x) p θt (•|x) Main Player at t + 1 → p θt+1 (•|x) Opponent Player at t + 1 → . . .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "Namely, the opponent player chosen from the previous iteration t is employed to train the main player at iteration t + 1, resulting in the LLM parameterized by θ t+1 . Then we determine the next opponent player at iteration t + 1 by directly copying the LLM parameter θ t+1 , which is then used in training the main player at iteration t + 2. The detailed algorithm is presented in Algorithm 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model generation @Iter 1", "sec_num": null}, {"text": "In Section 4.1, we propose Self-Play Fine-Tuning (SPIN) with an end-to-end training objective (4.7) for each iteration. (4.7) bears resemblance to direct preference optimization (DPO) (<PERSON><PERSON><PERSON><PERSON> et al., 2023) ", "cite_spans": [{"start": 184, "end": 207, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF66"}], "ref_spans": [], "eq_spans": [], "section": "Comparison between SPIN and DPO", "sec_num": "4.2"}, {"text": "In this section, we provide a theoretical analysis for Algorithm 1 in Section 4. Under monotonicity and convexity assumption of the objective function ℓ, we show that the global optimum is obtained if and only if parameter θ t generates data distribution. We summarize our assumptions as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5"}, {"text": "Assumption 5.1. The loss function ℓ(t) : R → R is monotonically decreasing, i.e., ∀t, ℓ ′ (t) ≤ 0 and satisfies ℓ ′ (0) < 0. In addition, ℓ(t) is a convex function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5"}, {"text": "Assumption 5.1 holds for a wide range of loss functions commonly used in machine learning, including correlation loss ℓ(t) = 1 -t, hinge loss ℓ(t) = max(0, 1 -t), exponential loss ℓ(t) = exp(-t) and logistic loss ℓ(t) = log(1 + exp(-t)). Under Assumptions 5.1, we present the following theorem, which is pivotal in understanding the optimization dynamics of our method.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5"}, {"text": "Theorem 5.2. Under Assumption 5.1, suppose there exists", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5"}, {"text": "p θ (•|x) = p data (•|x), then we have that • (Sufficiency) If p θt (•|x) = p data (•|x)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5"}, {"text": ", then θ t is the global minimum of (4.7) for any λ ≥ 0. • (Necessity) If p θt (•|x) ̸ = p data (•|x), there exists an appropriately chosen λ, such that θ t is not the global minimum of (4.7).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5"}, {"text": "Remark 5.3. Theorem 5.2 suggests that under certain conditions, the optimization process of our method naturally stops at the point p θ (•|x) = p data (•|x), implying the effectiveness of our approach in aligning the LLM's distribution with the target data distribution. Moreover, Theorem 5.2 also indicates that the optimization process only stops when the global optimality is achieved, i.e., the LLM's distribution aligns with the target data distribution.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5"}, {"text": "For the logistic loss function ℓ(t) = log(1 + exp(-t)), the following theorem gives a more precise characterization of the opponent player, enabling a better understanding of SPIN.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5"}, {"text": "<PERSON><PERSON> ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5"}, {"text": "This section provides a detailed empirical analysis of SPIN. Our findings highlight several key points: (1) SPIN markedly enhances model performance across a wide range of evaluation benchmarks by breaking the limit of SFT;", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "6"}, {"text": "(2) even without introducing new human annotated data, SPIN at iteration 0 achieves performance on par to DPO training that utilizes even more data;", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "6"}, {"text": "(3) iterative training is a necessary component in SPIN as it breaks the limit of multi-epoch training.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "6"}, {"text": "Model and Datasets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Setup", "sec_num": "6.1"}, {"text": "In this study, we adopt zephyr-7b-sft-full as our base model. This model derives from the pre-trained Mistral-7B (<PERSON> et al., 2023) and has been further fine-tuned on the SFT dataset Ultra-chat200k 1 by HuggingFace. Ultrachat200k represents a high-quality 200k subset of the larger UltraChat (<PERSON><PERSON> et al., 2023) corpus, which comprises approximately 1.4M dialogues produced using OpenAI's Turbo APIs. From Ultra-Chat200k, We randomly sample 50k prompts and use the base model to generate the synthetic responses. We subsequently follow the optimization method described in Section 4.1 for further training. In multiple iterations, we leverage the synthetic data from the most recent iteration and add to the newly generated synthetic data, therefore resulting in a synthetic dataset size of 50k at iteration 0 and 100k at iteration 1, 2 and 3. At each iteration, we train our model for 2 epochs.", "cite_spans": [{"start": 113, "end": 133, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF38"}, {"start": 294, "end": 313, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF24"}], "ref_spans": [], "eq_spans": [], "section": "Experiment Setup", "sec_num": "6.1"}, {"text": "Evaluation. We employed the widely used Huggingface Open LLM Leaderboard (<PERSON><PERSON> et al., 2023) as our evaluation benchmark, using the same Language Model Evaluation Harness library (<PERSON> et al., 2023b) . This leaderboard encompasses 6 different datasets, each focusing on a a specific capability of LLMs. Collectively, these datasets provide a thorough assessment framework, evaluating LLMs on commonsense reasoning (<PERSON> (<PERSON> et al., 2018) , HellaSwag (<PERSON><PERSON> et al., 2019) , <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON> et al., 2021)), multi-task language understanding (MMLU (<PERSON><PERSON><PERSON><PERSON> et al., 2020) ), human falsehood mimic (TruthfulQA (<PERSON> et al., 2021) ) and math problem solving (GSM8k (<PERSON><PERSON> et al., 2021) ). We leave further implemen-1 https://huggingface.co/datasets/ HuggingFaceH4/ultrachat_200k tation details to Appendix B with detailed evaluation setting adopted by both the leaderboard and our experiments. HuggingFace Open LLM Benchmark For \"SFT\", we report the performance of our base model zephyr-7b-sft-full, which has been fine-tuned on the same dataset we use to generate synthetic data.", "cite_spans": [{"start": 73, "end": 96, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}, {"start": 183, "end": 202, "text": "(<PERSON> et al., 2023b)", "ref_id": null}, {"start": 422, "end": 442, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF19"}, {"start": 455, "end": 477, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF96"}, {"start": 558, "end": 582, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF34"}, {"start": 620, "end": 638, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF51"}, {"start": 673, "end": 693, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "Experiment Setup", "sec_num": "6.1"}, {"text": "In Figure 2 , we demonstrate the effectiveness of SPIN using HuggingFace Open LLM Leaderboard as the evaluation. In Figure 3 , we compare the performance of our finetuned model by SPIN after iterations 0 to 3 with the base model zephyr-7b-sft-full on each task included in the leaderboard. Detailed performances are presented in Table 4 in Appendix B. We can observe that SPIN exhibits remarkable effectiveness in improving the model's performance by further leveraging the SFT dataset, on which the base model has already been fully fine-tuned. At iteration 0, where model responses are generated from zephyr-7b-sft-full, we observe an overall improvement of 2.66% on the average score. The improvement is particularly significant on the TruthfulQA and GSM8k benchmarks, with improvement exceeding 5% and 10% respectively. At iteration 1, we employ the LLM model from iteration 0 to generate new responses for SPIN, adhering to the procedure outlined in Algorithm 1. This iteration yields further enhancements of 1.32% on average, and especially significant on the Arc Challenge and TruthfulQA benchmarks. Subsequent iterations continue this trend of incremental improvement across various tasks. Meanwhile, the improvement at iteration t + 1 is naturally smaller than that at iteration t. As the iterative training progresses, the degree of improvement gradually approaches zero, suggesting that the model has reached a limiting point in the last iteration.", "cite_spans": [], "ref_spans": [{"start": 10, "end": 11, "text": "2", "ref_id": "FIGREF0"}, {"start": 123, "end": 124, "text": "3", "ref_id": "FIGREF1"}, {"start": 335, "end": 336, "text": "4", "ref_id": "TABREF10"}], "eq_spans": [], "section": "SPIN", "sec_num": "6.2"}, {"text": "Comparison with DPO. zephyr-7b-beta is a model derived from zephyr-7b-sft-full, trained with DPO on approximately 62k preference data. This data, the Ultra-Feedback Binarized dataset (C<PERSON> et al., 2023) 2 , comprises both chosen and rejected completions evaluated by GPT-4. We note that, DPO requires either human input or advanced language model feedback to determine the preference, making data generation a rather expensive procedure. In contrast, our SPIN only requires the initial model itself. Moreover, unlike DPO which requires new data source, our method exclusively leverages the existing SFT dataset. In Figure 3 , we show the performance comparison of SPIN at iterations 0 and 1 (employing 50k SFT data) with DPO training, from the same SFT checkpoint. We can observe that, while DPO leverages more data from new sources, SPIN based on the existing SFT data can already achieve comparable average performance to DPO training at iteration 0. From iteration 1, SPIN even surpasses the performance of DPO on the leaderboard benchmark.", "cite_spans": [{"start": 183, "end": 201, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF21"}], "ref_spans": [{"start": 621, "end": 622, "text": "3", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "SPIN", "sec_num": "6.2"}, {"text": "In this subsection, we examine the effect of synthetic dataset size and training epochs within an iteration. Our analysis demonstrates the effectiveness of the synthetic data used by SPIN compared to the SFT data, as well as the necessity of iterative training in SPIN. In Appendix B, we present assessment of SPIN on additional benchmark tasks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation Studies", "sec_num": "6.3"}, {"text": "Training Size. We investigate the effect of varying training data size on the performance of SPIN. In Figure 5 , we demonstrate the effect of training size for SPIN during iteration 0 and additionally compare with SFT with the full original dataset. Specifically, for the SFT baseline, we fully fine-tune Mistral-7B on Ultrachat200k for three epochs and report first epoch performance as the starting point (with x-axis 0) in the figure for SFT. For SPIN, we report the zephyr-7b-sft-full checkpoint as the starting point, which has also been fine-tuned on Ultrachat200k for one epoch. We select the training size of SPIN at iteration 0 to be 14k, 26k, and 50k and generate the data accordingly, ensuring that the larger dataset encompasses the smaller dataset. The performance of SPIN was then evaluated after 1 epoch of self ", "cite_spans": [], "ref_spans": [{"start": 109, "end": 110, "text": "5", "ref_id": null}], "eq_spans": [], "section": "Ablation Studies", "sec_num": "6.3"}, {"text": "This paper introduces a novel fine-tuning method SPIN, to convert a weak LLM to a strong LLM by unleashing the full power of human-annotated data. Central to this method is a self-play mechanism, wherein a main player (the LLM) is fine-tuned to differentiate the responses of opponent player (the LLM from previous iteration) from the target data distribution, and the LLM is iteratively aligned with the target data distribution. Therefore, SPIN facilitates the LLM's iterative self-evaluation and enhancement through self-play.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion and Discussion", "sec_num": "7"}, {"text": "In comparison to supervised fine-tuning and RL fine-tuning methods, SPIN enables the LLM to self-improve without additional human data or feedback from stronger LLMs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion and Discussion", "sec_num": "7"}, {"text": "Empirical results demonstrate that SPIN significantly enhances LLM performance across diverse benchmarks, even outperforming models trained with additional human data or AI feedback.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion and Discussion", "sec_num": "7"}, {"text": "Limitation and Future Work. Our theoretical results demonstrate that the optimization process of SPIN converges if and only if the LLM's distribution aligns with p data . Therefore, our study focuses on a fixed target data distribution generated by humans, which inherently imposes a ceiling on the performance of fine-tuned LLM. Exploring the dynamically changing target data distribution is an important direction to overcome this limitation and elevate the LLM's performance beyond this ceiling or even to a super-human level. Moreover, considering the resource demands of synthetic data generation, another promising avenue for further exploration is to reduce the volume of required synthetic data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion and Discussion", "sec_num": "7"}, {"text": "We thank the anonymous reviewers and area chair for their helpful comments. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgement", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Large Language Models. We believe that our work contribute meaningfully to the field, specifically on leveraging synthetic data to enhance LLM without the requirement for human preference annotations. The synthetic data generated by SPIN may be used to further augment the training of various language models. Moreover, SPIN demonstrated a substantial improvement in LLMs' capabilities, opening new avenues for their application in various downstream tasks. This advancement underscores the transformative potential of LLM fine-tuning in both technological and societal contexts.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Curriculum Learning. In deep learning, it has been observed that training models using data samples arranged in a strategically meaningful order can lead to improved performance compared to training on randomly shuffled data. This approach is commonly known as curriculum learning (<PERSON><PERSON> et al., 2009; <PERSON><PERSON><PERSON> et al., 2022) . Initial studies in curriculum learning introduced efficient algorithms that adhere to an 'easy-to-hard' progression (<PERSON><PERSON><PERSON><PERSON> et al., 2009; <PERSON> et al., 2010; <PERSON>, 2011; <PERSON> et al., 2015) . In the field of Natural Language Processing (NLP), criteria such as sentence length and term frequency are commonly utilized (<PERSON><PERSON><PERSON> et al., 2016; <PERSON> et al., 2018; <PERSON> et al., 2018) . More recent developments include the application of curriculum learning algorithms in multi-modal learning (<PERSON> et al., 2021; <PERSON> et al., 2022) . Our work shares a similar idea to curriculum learning, wherein the training data evolves iteratively-beginning with responses that are easy to distinguish from human-annotated data and gradually progressing to more challenging instances.", "cite_spans": [{"start": 281, "end": 302, "text": "(<PERSON><PERSON> et al., 2009;", "ref_id": "BIBREF9"}, {"start": 303, "end": 324, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF77"}, {"start": 443, "end": 468, "text": "(<PERSON><PERSON> et al., 2009;", "ref_id": "BIBREF78"}, {"start": 469, "end": 488, "text": "<PERSON> et al., 2010;", "ref_id": "BIBREF43"}, {"start": 489, "end": 509, "text": "Lee & G<PERSON>uman, 2011;", "ref_id": "BIBREF47"}, {"start": 510, "end": 529, "text": "<PERSON> et al., 2015)", "ref_id": "BIBREF97"}, {"start": 657, "end": 677, "text": "(<PERSON><PERSON><PERSON> et al., 2016;", "ref_id": "BIBREF18"}, {"start": 678, "end": 697, "text": "<PERSON> et al., 2018;", "ref_id": "BIBREF98"}, {"start": 698, "end": 715, "text": "<PERSON> et al., 2018)", "ref_id": "BIBREF53"}, {"start": 825, "end": 843, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF54"}, {"start": 844, "end": 860, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF90"}], "ref_spans": [], "eq_spans": [], "section": "A Further Related Work", "sec_num": null}, {"text": "Generative Adversarial Networks. Generative Adversarial Networks (GANs) (<PERSON><PERSON><PERSON> et al., 2014) represent a distinct class of generative models, characterized by their unique adversarial process. To enhance training stability and data quality, <PERSON> et al. (2017) introduced the Least Squares GAN, employing a least squares loss function for the discriminator. A significant advancement in GANs involves the use of Integral Probability Metrics (IPM) (<PERSON>, 1997) , particularly highlighted in the development of Wasserstein GAN by <PERSON><PERSON><PERSON><PERSON> et al. (2017) . This model employs IPM in its loss design, enhancing training stability. Since then, IPMs have become crucial in GAN design (Mroueh & Sercu, 2017; <PERSON><PERSON><PERSON><PERSON> et al., 2017) , particularly in constraining the discriminator to a specific function class, thereby preventing it from overpowering the generator. Furthermore, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (2018) generalized IPM-based GANs by introducing relativistic discriminator and proposed Relativistic GAN. It is worth noting that the objective function defined in our (4.2) is similar to Relativistic GAN (<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>, 2018) and reduces to an IPM framework in Wasserstein GAN (<PERSON><PERSON><PERSON><PERSON> et al., 2017) with a linear loss. However, our approach differs in both the choice of the function class and the training procedure. Inspired by GA<PERSON>, <PERSON> et al. (2023) proposed an adversarial learning framework named Adversarial Preference Optimization (APO) that trains the LLM and a reward model in an adversarial game. Similarly related to the spirit of our method, Generative Adversarial Imitation Learning (GAIL) (Ho & Ermon, 2016) was proposed to train separate discriminator and policy networks in each iteration. In contrast to the above methods, SPIN relies on self-play where both the main player and the opponent player are the same LLM from two consecutive iterations.", "cite_spans": [{"start": 72, "end": 97, "text": "(<PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF30"}, {"start": 246, "end": 263, "text": "<PERSON> et al. (2017)", "ref_id": "BIBREF56"}, {"start": 450, "end": 464, "text": "(<PERSON>, 1997)", "ref_id": "BIBREF60"}, {"start": 533, "end": 555, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2017)", "ref_id": "BIBREF2"}, {"start": 682, "end": 704, "text": "(<PERSON>oueh & Sercu, 2017;", "ref_id": "BIBREF59"}, {"start": 705, "end": 728, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF33"}, {"start": 1181, "end": 1204, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF2"}, {"start": 1341, "end": 1360, "text": "<PERSON> et al. (2023)", "ref_id": null}, {"start": 1611, "end": 1629, "text": "(<PERSON> <PERSON>, 2016)", "ref_id": "BIBREF37"}], "ref_spans": [], "eq_spans": [], "section": "A Further Related Work", "sec_num": null}, {"text": "Alignment with AI Feedback. The objective of alignment is to fine-tune LLMs to align with human intentions. In addition to using human demonstrations, AI feedback is emerging as a crucial component in the alignment process. Constitutional AI (<PERSON> et al., 2022b) leveraged AI feedback to align language models through a combination of both supervised learning and reinforcement learning (RL) phases. In the RL phase, (<PERSON> et al., 2022b) applied Reinforcement Learning from AI Feedback (RLAIF), training a reward model using AI-generated preferences, followed by RL using the reward. <PERSON> et al. (2023) demonstrated that AI feedback can achieve comparable or superior performance to human feedback in RL fine-tuning. They also demonstrated that RLAIF can improve upon an SFT policy even when the LLM labeler has the same size as the policy. <PERSON> et al. (2022) studied the scaling properties of self-critique and introduced a framework for comparing the critique ability to generation and discrimination ability. Self-critique models employ the LLM itself to generate natural language critiques through behavioral cloning, assisting human evaluators. We use the Alignment Handbook library (<PERSON><PERSON><PERSON> et al., 2023b) as the codebase for our self-play fine-tuning method SPIN, which includes DeepSpeed ZeRO-3 (<PERSON><PERSON><PERSON><PERSON> et al., 2020) and FlashAttention-2 (<PERSON><PERSON>, 2023) to reduce training cost. We train our models with RMSProp (<PERSON><PERSON> et al., 2012) optimizer with no weight decay for all iterations as commonly used in fine-tuning LLMs for alignment, with a global batch size of 64, 10% warmup steps and bfloat16 precision. We set the peak learning rate to be 5e-7 for iterations 0 and 1, and decay this peak learning rate to 1e-7 for iteration 2 and 3 as we are approaching the end of self-play fine-tuning. Lastly, we choose β = 0.1 and max sequence length to be 2048 tokens as in Tunstall et al. (2023b) . We note that at the last iteration (iter-3) where the model is close to convergence, we increase the value of β to 5.0. We use the Accelerate library (Gugger et al., 2022) to generate our synthetic data using distributed inference with multiple GPUs with a global batch size of 64. We consider the prompting template \"### Instruction: {prompt}\\n\\n### Response: \" as commonly used in Taori et al. (2023) . For Ultrachat200k containing multi-round conversations, we only sample the first round as our prompt and ground truth completion pairs.", "cite_spans": [{"start": 242, "end": 261, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 416, "end": 435, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 582, "end": 599, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF46"}, {"start": 838, "end": 860, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF72"}, {"start": 1189, "end": 1213, "text": "(<PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}, {"start": 1305, "end": 1331, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF67"}, {"start": 1353, "end": 1364, "text": "(Dao, 2023)", "ref_id": "BIBREF22"}, {"start": 1423, "end": 1444, "text": "(<PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF36"}, {"start": 1879, "end": 1902, "text": "<PERSON><PERSON><PERSON> et al. (2023b)", "ref_id": null}, {"start": 2055, "end": 2076, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF32"}, {"start": 2288, "end": 2307, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF80"}], "ref_spans": [], "eq_spans": [], "section": "A Further Related Work", "sec_num": null}, {"text": "The cost overhead introduced by SPIN is mostly the generation of synthetic data from the LLM that we train. The cost of the fine-tuning process remains computationally equal to that of SFT and DPO. We report both the generation and training time for SPIN in Table 2 . Results were obtained using a machine with 8xA100 (80G) GPUs. For per 64 examples, the generation time and training time are 6.69s and 10s respectively. is attributed to the utilization of a double-sized dataset (the combination of 50k synthetic data from the previous iteration and 50k synthetic data in the current iteration), as discussed in our Section 6.1.", "cite_spans": [], "ref_spans": [{"start": 264, "end": 265, "text": "2", "ref_id": "TABREF8"}], "eq_spans": [], "section": "B.2 Training Overhead", "sec_num": null}, {"text": "SPIN requires only the SFT data to improve over the traditional SFT stage and can sit between SFT and RL finetuning. Suppose additional preference data is provided, we can use the additional data to further improve the performance of the model after SPIN using RL fine-tuning.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Additional Experiment Result for SPIN+DPO", "sec_num": null}, {"text": "Starting at SPIN iteration 3, we further train the model with DPO for two epochs on the 62k preference data from the UltraFeedback Binarized dataset (<PERSON><PERSON> et al., 2023) , which consists of both chosen and rejected responses evaluated by GPT-4. Detailed performances are presented in Table 3 . We can observe that the checkpoint trained by SPIN can be further improved using DPO, yielding an enhancement of 0.89% on average. Notably, the improvement is particularly significant on the TruthfulQA benchmark with around 5%.", "cite_spans": [{"start": 149, "end": 167, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF21"}], "ref_spans": [{"start": 288, "end": 289, "text": "3", "ref_id": "TABREF9"}], "eq_spans": [], "section": "B.3 Additional Experiment Result for SPIN+DPO", "sec_num": null}, {"text": "In Table 4 , we show the detailed performance of SPIN at different iterations on each of the task in Open LLM Leaderboard.", "cite_spans": [], "ref_spans": [{"start": 9, "end": 10, "text": "4", "ref_id": "TABREF10"}], "eq_spans": [], "section": "B.4 Further Experiment Results", "sec_num": null}, {"text": "In Table 5 , we also show the performance of SFT from zephyr-7b-sft-full on Ultrachat200k for one epoch. While self-play fine-tuning with synthetic data from zephyr-7b-sft-full effectively improves its performance, simply fine-tuning it again on the SFT data leads to degraded performance, as similarly observed in Figure 5 .", "cite_spans": [], "ref_spans": [{"start": 9, "end": 10, "text": "5", "ref_id": "TABREF11"}, {"start": 322, "end": 323, "text": "5", "ref_id": null}], "eq_spans": [], "section": "B.4 Further Experiment Results", "sec_num": null}, {"text": "Further Investigation on More Tasks. Here, we further investigate the performance of SPIN on a broader variety of tasks, including <PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON> et al., 2023) , <PERSON><PERSON><PERSON><PERSON> (bench authors, 2023) and OpenBookQA (<PERSON><PERSON><PERSON> et al., 2018) in addition to the Open LLM Leaderboard tasks. Specifically, we use the following tasks from Big-Bench-Hard for a more comprehensive evaluation, including Causal Judgment (causal reasoning), Sports Understanding (commonsense reasoning) and Formal Fallacies (logical reasoning). In Table 6 , we show the resulting scores of SPIN on MT-Bench as well as those tasks from Big-Bench. In Figure 6 , we detail the model performances on MT-Bench with regard to different types of questions. We can see a notably robust improvement in the performance of SPIN on various tasks besides the HuggingFace Benchmark, without major degradation. Notably, on MT-Bench, the model fine-tuned by SPIN has surpassed the performance of vicuna-13b-v1.5 (<PERSON> et al., 2023) with a score of 6.57. ", "cite_spans": [{"start": 140, "end": 160, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF99"}, {"start": 210, "end": 233, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF57"}, {"start": 963, "end": 984, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF15"}], "ref_spans": [{"start": 521, "end": 522, "text": "6", "ref_id": "TABREF12"}, {"start": 623, "end": 624, "text": "6", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "B.4 Further Experiment Results", "sec_num": null}, {"text": "In Tables 7 and 8 , we further provide the generation examples of our fine-tuned model by SPIN from different iterations. We can observe an improvement in response quality as compared to the generation of the SFT checkpoint. Meanwhile, the model generations at higher iterations typically becomes more concise than iteration 0 and resemble the ground truth completion better. zephyr-7b=sft-full According to the given material, Horsham was growing \"significantly\" faster than nearby towns, indicating that it was economically thriving.\\n However, the given material does not specify the exact rates or levels of economic growth of Horsham compared to nearby towns. Without this information, it is challenging to determine whether the differences in economic growth between Horsham and nearby towns were significant or minimal.", "cite_spans": [], "ref_spans": [{"start": 10, "end": 11, "text": "7", "ref_id": "TABREF13"}, {"start": 16, "end": 17, "text": "8", "ref_id": null}], "eq_spans": [], "section": "B.5 Generation Examples", "sec_num": null}, {"text": "Iteration 0 I do not have access to the latest economic data for horsham and nearby towns. However, horsham is known to be a prosperous town in west sussex, uk, with a diverse economy that includes agriculture, manufacturing, and services. The town has a strong retail sector, and it is also home to several large companies, including the global pharmaceutical company novartis. In terms of comparison with nearby towns, horsham is generally considered to be one of the more affluent towns in the region. However, the economic growth of horsham and its neighboring towns may vary depending on various factors such as industry, investment, and government policies.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5 Generation Examples", "sec_num": null}, {"text": "Iteration 1 I do not have access to real-time data or information about the most recent economic growth of horsesham and nearby towns. However, according to the recent reports from various sources such as the office for national statistics, horsesham's economy primarily relies on retail, healthcare, education, and public administration industries. Compared to nearby towns such as lewes, chichester, and bognor regis, horsesham's economy may have a different composition and growth rate based on their specific industries and economic policies.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5 Generation Examples", "sec_num": null}, {"text": "Iteration 2 I don't have access to the most recent economic data. However, according to a 2019 report by pwc, horsham's expected economic growth up to 2025 was projected to be around 2.3% per annum, which is slightly below the average for the south east of england (2.8%).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5 Generation Examples", "sec_num": null}, {"text": "Neighbouring towns like crawley and bognor regis were projected to experience similar growth rates. note: this may not reflect the impact of the covid-19 pandemic that occurred in 2020.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5 Generation Examples", "sec_num": null}, {"text": "where KL( where the last equality is by the definition of JSD. This concludes the proof.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5 Generation Examples", "sec_num": null}, {"text": "Lemma C.2 provides a closed-form solution to (4.2) if we ignore the constraint set F t . If this closed-form solution belongs to F t , then it should also be the solution to (4.2). This observation is the key to the proof of Theorem 5.4.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5 Generation Examples", "sec_num": null}, {"text": "Proof of Theorem 5.4. Under the condition of Theorem 5.4, there exists a p θ such that p θ (y|x) ∝ p θt (y|x) p data (y|x)/p θt (y|x) ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5 Generation Examples", "sec_num": null}, {"text": "https://huggingface.co/datasets/ HuggingFaceH4/ultrafeedback_binarized", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "Can you clarify if Poppelsdorfer Allee 45 and the old site of the Mathematical Institute are the same location? Answer according to: Go straight to Poppelsdorfer Allee 45 and sign-in.\\n There is a big lecture hall at the old site of the Mathematical Institute (Wegelerstr. 10, Bonn).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prompt", "sec_num": null}, {"text": "No, Poppelsdorfer Allee 45 and the old site of the Mathematical Institute (Wegelerstr. 10, Bonn) are not the same location. To sign-in, one should go straight to Poppelsdorfer Allee 45. However, there is a big lecture hall at the old site of the Mathematical Institute.zephyr-7b=sft-full Yes, Poppelsdorfer Allee 45 and the old site of the Mathematical Institute are the same location.Iteration 0 Yes, Poppelsdorfer Allee 45 and the old site of the Mathematical Institute (Wegelerstr. 10, Bonn) are the same location. The Mathematical Institute has been relocated and renamed as the Institute for Mathematics and Theoretical Physics, but the building where it used to be is still referred to as the old site. The big lecture hall at the old site still exists and may be used for events or classes. where the inequality is due to <PERSON>'s inequality (recalling that ℓ is convex in Assumption 5.1). Therefore, we have that L SPIN (θ, θ t ) ≥ ℓ(0) = L SPIN (θ t , θ t ), which means that θ t is the global optimum of (4.7). As a consequence, the gradient at the point θ t is zero, which concludes θ t+1 = θ t .Next, we prove the \"Necessity\". Define g(λ) as follows:Then we have g(0) = ℓ(0) andwhere the last inequality is due to the condition that ℓ ′ (0) < 0. Therefore, there exist a λ 0 such that for all 0 < λ < λ 0 , we have g(λ) < ℓ(0). Choose θ * such that p θ * (y|x) = p data (y|x). For those 0 < λ < λ 0 , we have thatwhere the second equality holds by the choice of p θ * (•|x), and the inequality holds due to the choice of λ. Therefore, we conclude that θ t is not the global optimum of (4.7C.2 Proof Theorem 5.4We need the following auxiliary lemma before we prove Theorem 5.4. Proof of Lemma C.1. Define g(t) = aℓ(t) + bℓ(-t) = a log(1 + exp(-t)) + b log(1 + exp(t)), then we haveTherefore, g ′ (t) < 0 when t < log(a/b), g ′ (t) > 0 when t > log(a/b), which indicates that g achieves it minimum at t = log(a/b) which concludes the proof. where JSD(p + ∥p -) represents the Jensen-Shannon divergence which is defined as follows JSD p q = 1 2 KL p p + q 2 + 1 2 KL q p + q 2 ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ground truth", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Palm 2 technical report", "authors": [{"first": "R", "middle": [], "last": "Anil", "suffix": ""}, {"first": "A", "middle": ["M"], "last": "Dai", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Passos", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.10403"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Passos, A., Shake<PERSON>, S., <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Palm 2 technical report. arXiv preprint arXiv:2305.10403, 2023.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Thinking fast and slow with deep learning and tree search", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Tian", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>. Thinking fast and slow with deep learning and tree search. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Wasserstein generative adversarial networks", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Bo<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "214--223", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> gen- erative adversarial networks. In International conference on machine learning, pp. 214-223. PMLR, 2017.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Program synthesis with large language models", "authors": [{"first": "J", "middle": [], "last": "Austin", "suffix": ""}, {"first": "A", "middle": [], "last": "Odena", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "C", "middle": [], "last": "Cai", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "Le", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2108.07732"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Program synthesis with large language models. arXiv preprint arXiv:2108.07732, 2021.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Training a helpful and harmless assistant with reinforcement learning from human feedback", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Fort", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2204.05862"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Fort, S., <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Training a helpful and harmless assistant with rein- forcement learning from human feedback. arXiv preprint arXiv:2204.05862, 2022a.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Kund<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Kernion", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Harmlessness from ai feedback", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2212.08073"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. Constitutional ai: Harmlessness from ai feedback. arXiv preprint arXiv:2212.08073, 2022b.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Emergent complexity via multi-agent competition", "authors": [{"first": "T", "middle": [], "last": "Bansal", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON>. Emergent complexity via multi-agent competi- tion. In International Conference on Learning Represen- tations, 2018.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Beyond the imitation game: Quantifying and extrapolating the capabilities of language models", "authors": [{"first": "B", "middle": [], "last": "", "suffix": ""}], "year": 2023, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {"ISSN": ["2835-8856"]}, "num": null, "urls": [], "raw_text": "bench authors, <PERSON><PERSON> Beyond the imitation game: Quantifying and extrapolating the capabilities of language models. Transactions on Machine Learning Research, 2023. ISSN 2835-8856.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Curriculum learning", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Weston", "suffix": ""}], "year": 2009, "venue": "Proceedings of the 26th annual international conference on machine learning", "volume": "", "issue": "", "pages": "41--48", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, J. Cur<PERSON> learning. In Proceedings of the 26th annual international conference on machine learning, pp. 41-48, 2009.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Language models are few-shot learners", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Subbiah", "suffix": ""}, {"first": "J", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "1877--1901", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Language models are few-shot learners. Advances in neural information processing systems, 33: 1877-1901, 2020.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Sparks of artificial general intelligence: Early experiments with gpt-4", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Gehrke", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.12712"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Sparks of artificial general intel- ligence: Early experiments with gpt-4. arXiv preprint arXiv:2303.12712, 2023.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Weak-to-strong generalization: Eliciting strong capabilities with weak supervision", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["H"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Gao", "suffix": ""}, {"first": "L", "middle": [], "last": "Aschenbrenner", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Weak-to-strong generalization: Eliciting strong capabilities with weak supervision. 2023.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Evaluating large language models trained on code", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Tworek", "suffix": ""}, {"first": "H", "middle": [], "last": "Jun", "suffix": ""}, {"first": "Q", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "H", "middle": ["P D O"], "last": "Pi<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Burda", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2107.03374"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Evaluating large language models trained on code. arXiv preprint arXiv:2107.03374, 2021.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "An open-source chatbot impressing gpt-4 with 90%* chatgpt quality", "authors": [{"first": "W.-L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Stoica", "suffix": ""}, {"first": "E", "middle": ["P"], "last": "Xi<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Vicuna", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Stoica, I., and Xi<PERSON>, E. P. <PERSON>: An open-source chatbot impressing gpt-4 with 90%* chatgpt quality, March 2023.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Deep reinforcement learning from human preferences. Advances in neural information processing systems", "authors": [{"first": "P", "middle": ["F"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Deep reinforcement learning from human preferences. Advances in neural information pro- cessing systems, 30, 2017.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Scaling instruction-finetuned language models", "authors": [{"first": "H", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Longpre", "suffix": ""}, {"first": "B", "middle": [], "last": "Zoph", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.11416"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Scaling instruction-finetuned language models. arXiv preprint arXiv:2210.11416, 2022.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Visualizing and understanding curriculum learning for long short-term memory networks", "authors": [{"first": "V", "middle": [], "last": "Cirik", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L.-P", "middle": [], "last": "Morency", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1611.06204"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, L.-P. Visualizing and understanding curriculum learning for long short-term memory networks. arXiv preprint arXiv:1611.06204, 2016.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Think you have solved question answering? try arc, the ai2 reasoning challenge", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "K<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "Tafjord", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1803.05457"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Schoenick, C., and Tafjord, O. Think you have solved question answering? try arc, the ai2 reasoning challenge. arXiv preprint arXiv:1803.05457, 2018.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Training verifiers to solve math word problems", "authors": [{"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Bavarian", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Jun", "suffix": ""}, {"first": "L", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Tworek", "suffix": ""}, {"first": "J", "middle": [], "last": "Hilton", "suffix": ""}, {"first": "R", "middle": [], "last": "Nakan<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2110.14168"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Training verifiers to solve math word problems. arXiv preprint arXiv:2110.14168, 2021.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Boosting language models with high-quality feedback", "authors": [{"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Sun", "suffix": ""}, {"first": "", "middle": [], "last": "Ultrafeedback", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. Ultrafeedback: Boosting language models with high-quality feedback, 2023.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Flashattention-2: Faster attention with better parallelism and work partitioning", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.08691"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>-2: Faster attention with bet- ter parallelism and work partitioning. arXiv preprint arXiv:2307.08691, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Rephrase and respond: Let large language models ask better questions for themselves", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.04205"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> and respond: Let large language models ask better questions for themselves. arXiv preprint arXiv:2311.04205, 2023.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Enhancing chat language models by scaling high-quality instructional conversations", "authors": [{"first": "N", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Qin", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Hu", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Sun", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.14233"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Enhancing chat language mod- els by scaling high-quality instructional conversations. arXiv preprint arXiv:2305.14233, 2023.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Self-training converts weak learners to strong learners in mixture models", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2022, "venue": "International Conference on Artificial Intelligence and Statistics", "volume": "", "issue": "", "pages": "8003--8021", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, Q. Self-training converts weak learners to strong learners in mixture models. In International Conference on Artificial Intelligence and Statistics, pp. 8003-8021. PMLR, 2022.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Boosting a weak learning algorithm by majority", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1995, "venue": "Information and computation", "volume": "121", "issue": "2", "pages": "256--285", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> a weak learning algorithm by majority. Information and computation, 121(2):256-285, 1995.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "A decision-theoretic generalization of on-line learning and an application to boosting", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["E"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1997, "venue": "Journal of computer and system sciences", "volume": "55", "issue": "1", "pages": "119--139", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, R<PERSON> E. A decision-theoretic general- ization of on-line learning and an application to boosting. Journal of computer and system sciences, 55(1):119-139, 1997.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Scaling laws for reward model overoptimization", "authors": [{"first": "L", "middle": [], "last": "Gao", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Hilton", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "10835--10866", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> laws for reward model overoptimization. In International Conference on Machine Learning, pp. 10835-10866. PMLR, 2023a.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "A framework for few-shot language model evaluation", "authors": [{"first": "L", "middle": [], "last": "Gao", "suffix": ""}, {"first": "J", "middle": [], "last": "Tow", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Black", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Golding", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Le", "middle": [], "last": "<PERSON><PERSON><PERSON>h", "suffix": ""}, {"first": "A", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Ociepa", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Skowron", "suffix": ""}, {"first": "A", "middle": [], "last": "Sutawika", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Thite", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "", "suffix": ""}], "year": null, "venue": "", "volume": "12", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, A. A framework for few-shot language model evaluation, 12 2023b.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Generative adversarial nets", "authors": [{"first": "I", "middle": [], "last": "Goodfellow", "suffix": ""}, {"first": "J", "middle": [], "last": "Pouget-Abadie", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Warde-Farley", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Courville", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "Advances in neural information processing systems", "volume": "27", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, D., Ozair, S., Courville, A., and Bengio, Y. Generative adversarial nets. Advances in neural informa- tion processing systems, 27, 2014.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Semi-supervised learning by entropy minimization", "authors": [{"first": "Y", "middle": [], "last": "Grandvalet", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2004, "venue": "Advances in neural information processing systems", "volume": "17", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, Y. Semi-supervised learning by entropy minimization. Advances in neural information processing systems, 17, 2004.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Accelerate: Training and inference at scale made simple, efficient and adaptable", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>ger", "suffix": ""}, {"first": "L", "middle": [], "last": "Debut", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Sun", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S<PERSON>, <PERSON>, M<PERSON>, and <PERSON><PERSON>, B. Accelerate: Training and inference at scale made simple, efficient and adaptable., 2022.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Improved training of wasserstein gans", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["C"], "last": "Courville", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and Courville, A. C. Improved training of wasserstein gans. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Measuring massive multitask language understanding", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Ma<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Song", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2009.03300"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Measuring mas- sive multitask language understanding. arXiv preprint arXiv:2009.03300, 2020.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Is multiagent deep reinforcement learning the answer or the question? a brief survey", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Kartal", "suffix": ""}, {"first": "M", "middle": ["E"], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "learning", "volume": "21", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. E. Is mul- tiagent deep reinforcement learning the answer or the question? a brief survey. learning, 21:22, 2018.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Neural networks for machine learning lecture 6a overview of mini-batch gradient descent", "authors": [{"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Swersky", "suffix": ""}], "year": 2012, "venue": "Cited on", "volume": "14", "issue": "8", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> for machine learning lecture 6a overview of mini-batch gradient descent. Cited on, 14(8):2, 2012.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Generative adversarial imitation learning", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Advances in neural information processing systems", "volume": "29", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, S. Generative adversarial imitation learn- ing. Advances in neural information processing systems, 29, 2016.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Mistral 7b", "authors": [{"first": "A", "middle": ["Q"], "last": "Jiang", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Bamford", "suffix": ""}, {"first": "D", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["D"], "last": "Casas", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.06825"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, C., Cha<PERSON>lot, D. S., Casas, D. d. l., <PERSON>, F., <PERSON>, G<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Mistral 7b. arXiv preprint arXiv:2310.06825, 2023.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "The relativistic discriminator: a key element missing from standard gan", "authors": [{"first": "A", "middle": [], "last": "Jolicoeur-<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1807.00734"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> The relativistic discriminator: a key element missing from standard gan. arXiv preprint arXiv:1807.00734, 2018.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Exploiting asymmetry for synthetic training data generation: <PERSON><PERSON><PERSON> and the case of information extraction", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Sakota", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "West", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.04132"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, M., and West, R. Ex- ploiting asymmetry for synthetic training data generation: Synthie and the case of information extraction. arXiv preprint arXiv:2303.04132, 2023.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Cryptographic limitations on learning boolean formulae and finite automata", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Valiant", "suffix": ""}], "year": 1994, "venue": "Journal of the ACM (JACM)", "volume": "41", "issue": "1", "pages": "67--95", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> Cryptographic limitations on learning boolean formulae and finite automata. Journal of the ACM (JACM), 41(1):67-95, 1994.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "How does semisupervised learning with pseudo-labelers work? a case study", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2022, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. How does semi- supervised learning with pseudo-labelers work? a case study. In The Eleventh International Conference on Learning Representations, 2022.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Advances in neural information processing systems", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>er", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2010, "venue": "", "volume": "23", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Self-paced learning for latent variable models. Advances in neural informa- tion processing systems, 23, 2010.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "A unified game-theoretic approach to multiagent reinforcement learning", "authors": [{"first": "M", "middle": [], "last": "Lanctot", "suffix": ""}, {"first": "V", "middle": [], "last": "Zambaldi", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Pérolat", "suffix": ""}, {"first": "D", "middle": [], "last": "Silver", "suffix": ""}, {"first": "T", "middle": [], "last": "Graepel", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, T. A uni- fied game-theoretic approach to multiagent reinforcement learning. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Pseudo-label: The simple and efficient semisupervised learning method for deep neural networks", "authors": [{"first": "D.-H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2013, "venue": "ICML Challenges in Representation Learning Workshop", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>: The simple and efficient semi- supervised learning method for deep neural networks. In ICML Challenges in Representation Learning Workshop, 2013.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Scaling reinforcement learning from human feedback with ai feedback", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Phatale", "suffix": ""}, {"first": "H", "middle": [], "last": "Mans<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "Carbune", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.00267"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C<PERSON>, Carbune, V., and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: Scal- ing reinforcement learning from human feedback with ai feedback. arXiv preprint arXiv:2309.00267, 2023.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Learning the easy things first: Self-paced visual category discovery", "authors": [{"first": "Y", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2011, "venue": "CVPR 2011", "volume": "", "issue": "", "pages": "1721--1728", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> Learning the easy things first: Self-paced visual category discovery. In CVPR 2011, pp. 1721-1728. IEEE, 2011.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Solving quantitative reasoning problems with language models", "authors": [{"first": "A", "middle": [], "last": "Lewkowycz", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Anil", "suffix": ""}, {"first": "I", "middle": [], "last": "Schlag", "suffix": ""}, {"first": "T", "middle": [], "last": "Gutman-Solo", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "3843--3857", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, et al. Solving quantitative reasoning problems with language models. Advances in Neural Information Processing Systems, 35:3843-3857, 2022.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Competition-level code generation with alphacode", "authors": [{"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Le<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Eccles", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Gimeno", "suffix": ""}, {"first": "A", "middle": [], "last": "Dal <PERSON>", "suffix": ""}], "year": 2022, "venue": "Science", "volume": "378", "issue": "6624", "pages": "1092--1097", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Competition-level code generation with alpha- code. Science, 378(6624):1092-1097, 2022.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Textbooks are all you need ii: phi-1.5 technical report", "authors": [{"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["D"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": ["T"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, A. D., <PERSON><PERSON>, S., and <PERSON>, Y. T. Textbooks are all you need ii: phi-1.5 technical report, 2023.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Measuring how models mimic human falsehoods", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Hilton", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Truthfulqa", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2109.07958"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Measuring how models mimic human falsehoods. arXiv preprint arXiv:2109.07958, 2021.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Tinygsm: achieving> 80% on gsm8k with small language models", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Ward", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.09241"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>gsm: achieving> 80% on gsm8k with small language models. arXiv preprint arXiv:2312.09241, 2023.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Curriculum learning for natural answer generation", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "He", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "IJCAI", "volume": "", "issue": "", "pages": "4223--4229", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Curriculum learning for natural answer generation. In IJCAI, pp. 4223-4229, 2018.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Competence-based multimodal curriculum learning for medical report generation", "authors": [{"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ge", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing", "volume": "", "issue": "", "pages": "3001--3012", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, X. Competence-based multimodal curriculum learning for medical report generation. In Pro- ceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing (Vol- ume 1: Long Papers), pp. 3001-3012, 2021.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Wizardmath: Empowering mathematical reasoning for large language models via reinforced evol-instruct", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "Sun", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Tao", "suffix": ""}, {"first": "X", "middle": [], "last": "Geng", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.09583"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>- math: Empowering mathematical reasoning for large lan- guage models via reinforced evol-instruct. arXiv preprint arXiv:2308.09583, 2023.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Least squares generative adversarial networks", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "", "suffix": ""}], "year": 2017, "venue": "Proceedings of the IEEE international", "volume": "", "issue": "", "pages": "2794--2802", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Least squares generative adversarial networks. In Proceedings of the IEEE international con- ference on computer vision, pp. 2794-2802, 2017.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Can a suit of armor conduct electricity? a new dataset for open book question answering", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "K<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the 2018 Conference on Empirical Methods in Natural Language Processing", "volume": "", "issue": "", "pages": "2381--2391", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Can a suit of armor conduct electricity? a new dataset for open book question answering. In Proceedings of the 2018 Conference on Empirical Methods in Natural Language Processing, pp. 2381-2391, 2018.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Crosstask generalization via natural language crowdsourcing instructions", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2104.08773"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Cross- task generalization via natural language crowdsourcing instructions. arXiv preprint arXiv:2104.08773, 2021.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Advances in neural information processing systems", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> gan. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Integral probability metrics and their generating classes of functions", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1997, "venue": "Advances in applied probability", "volume": "29", "issue": "2", "pages": "429--443", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> probability metrics and their generating classes of functions. Advances in applied probability, 29 (2):429-443, 1997.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "A generalized training approach for multiagent learning", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Lanctot", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1909.12823"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, D<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. A generalized training approach for multiagent learning. arXiv preprint arXiv:1909.12823, 2019.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Training language models to follow instructions with human feedback", "authors": [{"first": "L", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "D", "middle": [], "last": "Almeida", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "27730--27744", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Training language models to follow instructions with human feedback. Advances in Neural Information Processing Systems, 35:27730-27744, 2022.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Rephrase, augment, reason: Visual grounding of questions for visionlanguage models", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Bansal", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.05861"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>, aug- ment, reason: Visual grounding of questions for vision- language models. arXiv preprint arXiv:2310.05861, 2023.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "Language models are unsupervised multitask learners", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Child", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "OpenAI blog", "volume": "1", "issue": "8", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Language models are unsupervised multitask learners. OpenAI blog, 1(8):9, 2019.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "Direct preference optimization: Your language model is secretly a reward model", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.18290"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C. <PERSON>, and <PERSON>, C. Direct preference optimization: Your language model is secretly a reward model. arXiv preprint arXiv:2305.18290, 2023.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "Memory optimizations toward training trillion parameter models", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "He", "suffix": ""}, {"first": "", "middle": [], "last": "Zero", "suffix": ""}], "year": 2020, "venue": "SC20: International Conference for High Performance Computing, Networking, Storage and Analysis", "volume": "", "issue": "", "pages": "1--16", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>: Memory optimizations toward training trillion parameter models. In SC20: International Conference for High Per- formance Computing, Networking, Storage and Analysis, pp. 1-16. IEEE, 2020.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "Code llama: Open foundation models for code", "authors": [{"first": "B", "middle": [], "last": "Roziere", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "G<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Gat", "suffix": ""}, {"first": "X", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Rapin", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.12950"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Code llama: Open foundation models for code. arXiv preprint arXiv:2308.12950, 2023.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "An adversarial winograd schema challenge at scale", "authors": [{"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["L"], "last": "Bras", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Communications of the ACM", "volume": "64", "issue": "9", "pages": "99--106", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: An adversarial winograd schema challenge at scale. Communications of the ACM, 64(9):99-106, 2021.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "Some studies in machine learning using the game of checkers", "authors": [{"first": "A", "middle": ["L"], "last": "<PERSON>", "suffix": ""}], "year": 1959, "venue": "IBM Journal of research and development", "volume": "3", "issue": "3", "pages": "210--229", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> Some studies in machine learning using the game of checkers. IBM Journal of research and development, 3(3):210-229, 1959.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "Some studies in machine learning using the game of checkers", "authors": [{"first": "A", "middle": ["L"], "last": "<PERSON>", "suffix": ""}], "year": 2000, "venue": "IBM Journal of research and development", "volume": "44", "issue": "1.2", "pages": "206--226", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> Some studies in machine learning using the game of checkers. IBM Journal of research and development, 44(1.2):206-226, 2000.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "Self-critiquing models for assisting human evaluators", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Yeh", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Bills", "suffix": ""}, {"first": "L", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ward", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2206.05802"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Self-critiquing models for assisting human evaluators. arXiv preprint arXiv:2206.05802, 2022.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "The strength of weak learnability", "authors": [{"first": "R", "middle": ["E"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1990, "venue": "Machine learning", "volume": "5", "issue": "", "pages": "197--227", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> The strength of weak learnability. Machine learning, 5:197-227, 1990.", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "Mastering chess and shogi by self-play with a general reinforcement learning algorithm", "authors": [{"first": "D", "middle": [], "last": "Silver", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Lanctot", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>fre", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Graepel", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1712.01815"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Mastering chess and shogi by self-play with a general reinforcement learning algorithm. arXiv preprint arXiv:1712.01815, 2017a.", "links": null}, "BIBREF75": {"ref_id": "b75", "title": "Mastering the game of go without human knowledge", "authors": [{"first": "D", "middle": [], "last": "Silver", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Bolton", "suffix": ""}], "year": 2017, "venue": "nature", "volume": "550", "issue": "7676", "pages": "354--359", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Mastering the game of go without human knowledge. nature, 550(7676):354-359, 2017b.", "links": null}, "BIBREF76": {"ref_id": "b76", "title": "Beyond human data: Scaling self-training for problem-solving with language models", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["D"], "last": "Co<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.06585"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Beyond human data: Scaling self-training for problem-solving with language models. arXiv preprint arXiv:2312.06585, 2023.", "links": null}, "BIBREF77": {"ref_id": "b77", "title": "Curriculum learning: A survey", "authors": [{"first": "P", "middle": [], "last": "So<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Rota", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "International Journal of Computer Vision", "volume": "130", "issue": "6", "pages": "1526--1565", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, N. Cur- riculum learning: A survey. International Journal of Computer Vision, 130(6):1526-1565, 2022.", "links": null}, "BIBREF78": {"ref_id": "b78", "title": "Baby steps: How \"less is more\" in unsupervised dependency parsing", "authors": [{"first": "V", "middle": ["I"], "last": "Spitkovsky", "suffix": ""}, {"first": "H", "middle": [], "last": "Alshawi", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "NIPS 2009 Workshop on Grammar Induction, Representation of Language and Language Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> steps: How \"less is more\" in unsupervised dependency parsing. In NIPS 2009 Workshop on Grammar Induction, Repre- sentation of Language and Language Learning, 2009.", "links": null}, "BIBREF79": {"ref_id": "b79", "title": "Learning to summarize with human feedback", "authors": [{"first": "N", "middle": [], "last": "Stiennon", "suffix": ""}, {"first": "L", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "P", "middle": ["F"], "last": "", "suffix": ""}], "year": 2020, "venue": "Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "3008--3021", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Radford, A., <PERSON>, D., and <PERSON>, P. F. Learning to summarize with human feedback. Ad- vances in Neural Information Processing Systems, 33: 3008-3021, 2020.", "links": null}, "BIBREF80": {"ref_id": "b80", "title": "Stanford alpaca: An instruction-following llama model", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Li", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": ["B"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, T. B. Stanford alpaca: An instruction-following llama model, 2023.", "links": null}, "BIBREF81": {"ref_id": "b81", "title": "Temporal difference learning and tdgammon", "authors": [{"first": "G", "middle": [], "last": "Tesauro", "suffix": ""}], "year": 1995, "venue": "Communications of the ACM", "volume": "38", "issue": "3", "pages": "58--68", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> et al. Temporal difference learning and td- gammon. Communications of the ACM, 38(3):58-68, 1995.", "links": null}, "BIBREF82": {"ref_id": "b82", "title": "Language models for dialog applications", "authors": [{"first": "R", "middle": [], "last": "Thop<PERSON>lan", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Hall", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H.-T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2201.08239"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. <PERSON>: Language models for dialog appli- cations. arXiv preprint arXiv:2201.08239, 2022.", "links": null}, "BIBREF83": {"ref_id": "b83", "title": "Llama 2: Open foundation and finetuned chat models", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Stone", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Batra", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09288"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N., Batra, S., B<PERSON>ga<PERSON>, P., <PERSON>, S., et al. Llama 2: Open foundation and fine- tuned chat models. arXiv preprint arXiv:2307.09288, 2023.", "links": null}, "BIBREF84": {"ref_id": "b84", "title": "Direct distillation of lm alignment", "authors": [{"first": "L", "middle": [], "last": "Tunstall", "suffix": ""}, {"first": "E", "middle": [], "last": "Beeching", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Belkada", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Fourrier", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.16944"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Zephyr: Direct distillation of lm alignment. arXiv preprint arXiv:2310.16944, 2023a.", "links": null}, "BIBREF85": {"ref_id": "b85", "title": "The alignment handbook", "authors": [{"first": "L", "middle": [], "last": "Tunstall", "suffix": ""}, {"first": "E", "middle": [], "last": "Beeching", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, L., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, A. M., and <PERSON>, T. The alignment handbook, 2023b.", "links": null}, "BIBREF86": {"ref_id": "b86", "title": "The nature of statistical learning theory", "authors": [{"first": "V", "middle": [], "last": "Vapnik", "suffix": ""}], "year": 1999, "venue": "Springer science & business media", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>. The nature of statistical learning theory. Springer science & business media, 1999.", "links": null}, "BIBREF87": {"ref_id": "b87", "title": "Multitask prompted training enables zero-shot task generalization", "authors": [{"first": "S", "middle": [], "last": "Victor", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Lintang", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Multitask prompted training enables zero-shot task generalization. In International Conference on Learning Representations, 2022.", "links": null}, "BIBREF89": {"ref_id": "b89", "title": "Chain-of-thought prompting elicits reasoning in large language models", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": ["V"], "last": "Le", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "24824--24837", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Chain-of-thought prompting elicits reasoning in large language models. Advances in Neural Information Processing Systems, 35: 24824-24837, 2022.", "links": null}, "BIBREF90": {"ref_id": "b90", "title": "Scaling multimodal pre-training via cross-modality gradient harmonization", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "36161--36173", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Scaling multimodal pre-training via cross-modality gradient har- monization. Advances in Neural Information Processing Systems, 35:36161-36173, 2022.", "links": null}, "BIBREF91": {"ref_id": "b91", "title": "Some things are more cringe than others: Preference optimization with the pairwise cringe loss", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Sukhbaatar", "suffix": ""}, {"first": "J", "middle": [], "last": "Weston", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.16682"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, S<PERSON>, and <PERSON>, J. Some things are more cringe than others: Preference opti- mization with the pairwise cringe loss. arXiv preprint arXiv:2312.16682, 2023.", "links": null}, "BIBREF92": {"ref_id": "b92", "title": "Decoding data quality via synthetic corruptions: Embedding-guided pruning of code data", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["K"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "G<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C.-J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.02418"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Wu, C.<PERSON>J<PERSON>, <PERSON>, A. S., and <PERSON><PERSON><PERSON>, N. Decoding data quality via syn- thetic corruptions: Embedding-guided pruning of code data. arXiv preprint arXiv:2312.02418, 2023.", "links": null}, "BIBREF93": {"ref_id": "b93", "title": "Metamath: Bootstrap your own mathematical questions for large language models", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "H", "middle": [], "last": "Shi", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["T"], "last": "Kwok", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "A", "middle": [], "last": "Weller", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.12284"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>: Boot- strap your own mathematical questions for large language models. arXiv preprint arXiv:2309.12284, 2023.", "links": null}, "BIBREF94": {"ref_id": "b94", "title": "Self-rewarding language models", "authors": [{"first": "W", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "R", "middle": ["Y"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Cho", "suffix": ""}, {"first": "S", "middle": [], "last": "Sukhbaatar", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Weston", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2401.10020"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. Self-rewarding language models. arXiv preprint arXiv:2401.10020, 2024.", "links": null}, "BIBREF95": {"ref_id": "b95", "title": "Scaling relationship on learning mathematical reasoning with large language models", "authors": [{"first": "Z", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "H", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "C", "middle": [], "last": "Li", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.01825"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> relationship on learning mathematical rea- soning with large language models. arXiv preprint arXiv:2308.01825, 2023.", "links": null}, "BIBREF96": {"ref_id": "b96", "title": "Can a machine really finish your sentence? arXiv preprint", "authors": [{"first": "R", "middle": [], "last": "Zellers", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Bisk", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Hellaswag", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1905.07830"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>: Can a machine really finish your sentence? arXiv preprint arXiv:1905.07830, 2019.", "links": null}, "BIBREF97": {"ref_id": "b97", "title": "A self-paced multiple-instance learning framework for co-saliency detection", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "C", "middle": [], "last": "Li", "suffix": ""}, {"first": "L", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Han", "suffix": ""}], "year": 2015, "venue": "Proceedings of the IEEE international conference on computer vision", "volume": "", "issue": "", "pages": "594--602", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. A self-paced multiple-instance learning framework for co-saliency detection. In Proceedings of the IEEE international conference on computer vision, pp. 594- 602, 2015.", "links": null}, "BIBREF98": {"ref_id": "b98", "title": "An empirical exploration of curriculum learning for neural machine translation", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["J"], "last": "Martindale", "suffix": ""}, {"first": "P", "middle": [], "last": "M<PERSON>namee", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Carpuat", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1811.00739"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, M<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. An empirical exploration of curriculum learning for neural machine translation. arXiv preprint arXiv:1811.00739, 2018.", "links": null}, "BIBREF99": {"ref_id": "b99", "title": "Judging llm-as-a-judge with mt-bench and chatbot arena", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W.-L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "D", "middle": [], "last": "Li", "suffix": ""}, {"first": "E", "middle": [], "last": "Xi<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.05685"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Judging llm-as-a-judge with mt-bench and chatbot arena. arXiv preprint arXiv:2306.05685, 2023.", "links": null}, "BIBREF100": {"ref_id": "b100", "title": "Fine-tuning language models from human preferences", "authors": [{"first": "D", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Stiennon", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1909.08593"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, A<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Fine-tuning language models from human preferences. arXiv preprint arXiv:1909.08593, 2019.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "fig_num": "2", "text": "Figure 2. The average score of SPIN at different iterations on the HuggingFace Open LLM leaderboard datasets.For \"SFT\", we report the performance of our base model zephyr-7b-sft-full, which has been fine-tuned on the same dataset we use to generate synthetic data.", "num": null, "uris": null}, "FIGREF1": {"type_str": "figure", "fig_num": "3", "text": "Figure 3. Performance comparison with DPO training across the six benchmark datasets. Self-play at iteration 0 achieves comparable performance to DPO training with 62k new data. At iteration 1, self-play has already surpassed DPO training on the majority of datasets.", "num": null, "uris": null}, "FIGREF2": {"type_str": "figure", "fig_num": "45", "text": "Figure 4. The SPIN training dynamics of zephyr-7b-sft-full on the 50k synthetic data with regard to the number of training epochs during iteration 0. We can observe that iterative training is pivotal as training for more epochs during iteration 0 reaches a limit and cannot surpass iteration 1.", "num": null, "uris": null}, "FIGREF3": {"type_str": "figure", "fig_num": "6", "text": "Figure 6. Model performance on MT-Bench. We compare SPIN across different iterations with the base SFT model. Starting from iteration 1, our fine-tuned model by SPIN robustly outperforms the SFT checkpoint on all evaluation aspects.", "num": null, "uris": null}, "TABREF2": {"type_str": "table", "text": "Input: {(x i , y i )} i∈[N ] : SFT Dataset, p θ0 : LLM with parameter θ 0 , T : Number of iterations. for t = 0, . . . , T -1 do for i = 1, . . . N do Generate synthetic data y ′ i ∼ p θt (•|x i ). end for Update θ t+1 = argmin θ∈Θ i∈[N ] ℓ λ log p θ (yi|xi) -likelihood of p(y 1 ≻ y 2 |x) by direct policy optimization without explicit reward estimation. In contrast, SPIN relies on maximizing the IPM to compete with an increasingly stronger version of itself. More detailed comparisons are highlighted as follows:1. DPO does not inherently lead to iterative training. More specifically, DPO aims to match the preference probability p(y 1 ≻ y 2 |x) induced from its reward model with the data distribution p", "content": "<table><tr><td/><td>p θ t (yi|xi) -</td></tr><tr><td>λ log</td><td>p θ (y ′ i |xi) p θ t (y ′ i |xi) .</td></tr><tr><td>end for</td><td/></tr><tr><td colspan=\"2\">Output: θ T .</td></tr><tr><td colspan=\"2\">Recently, <PERSON> et al. (2023) proposed to use iterative prefer-</td></tr><tr><td colspan=\"2\">ence optimization with the Pairwise Cringe Loss (PCO), and</td></tr><tr><td colspan=\"2\">generalized DPO to iterative DPO. Concurrent to our work,</td></tr><tr><td colspan=\"2\"><PERSON> et al. (2024) further proposed a framework named</td></tr><tr><td colspan=\"2\">\"self-rewarding language models\", which leverages the LLM</td></tr><tr><td colspan=\"2\">itself as the reward model to provide the preference feed-</td></tr><tr><td colspan=\"2\">back, and employs iterative DPO to train the LLM. Com-</td></tr><tr><td colspan=\"2\">pared with Xu et al. (2023); Yuan et al. (2024), SPIN's</td></tr><tr><td colspan=\"2\">self-assessment is implicit, as no intermediate reward or</td></tr><tr><td colspan=\"2\">preference feedback is required.</td></tr></table>", "html": null, "num": null}, "TABREF3": {"type_str": "table", "text": "5.4. Consider the choice of logistic loss ℓ(t) = log(1 + exp(-t)) in SPIN. Suppose that p θt (y|x) p data (y|x)/p θt (y|x)1/λ lies in the LLM space {p θ (y|x)|θ ∈ Θ} and θ t+1 is global minimum of L SPIN (θ, θ t ), then the opponent player at iteration t + 1 satisfies p θt+1 (y|x) ∝ p θt (y|x) p data (y|x)/p θt (y|x) 1/λ . According to Theorem 5.4, the model update from p θt (y|x) to p θt+1 (y|x) tends to increase the probability p θt+1 (y|x) when p θt (y|x) is less than p data (y|x), and decrease it when p θt (y|x) is greater than p A smaller λ results in a larger change of the opponent player, while a larger λ leads to a smaller change. Therefore, as p θ (•|x) approaches p data (•|x), increasing λ enhances the stability of LLM training. This observation aligns with (4.3), where λ is the regularization parameter of the KL regularization that is employed to control the deviation of the opponent player.", "content": "<table><tr><td>Remark 5.5.</td></tr></table>", "html": null, "num": null}, "TABREF7": {"type_str": "table", "text": "Detailed information of HuggingFace Open LLM Leaderboard. For each evaluation dataset, we present the number of few-shot examples and metric adopted for evaluation.", "content": "<table><tr><td>Datasets</td><td>Arc</td><td colspan=\"5\">TruthfulQA Winogrande GSM8k HellaSwag MMLU</td></tr><tr><td># few-shot</td><td>25</td><td>0</td><td>5</td><td>5</td><td>10</td><td>5</td></tr><tr><td>Metric</td><td>acc_norm</td><td>mc2</td><td>acc</td><td>acc</td><td>acc_norm</td><td>acc</td></tr></table>", "html": null, "num": null}, "TABREF8": {"type_str": "table", "text": "Generation and Training Times for Different Iterations It is evident that the generation time is dominated by the training time at each iteration. The estimated time in Table 2 is based on the fact that we generate 50k examples per iteration. Please note that the doubled training time from iter 1 to iter 3", "content": "<table><tr><td>Iteration</td><td>Iter 0</td><td/><td>Iter 1</td><td/><td>Iter 2</td><td/><td>Iter 3</td><td/></tr><tr><td colspan=\"9\">Process Generation Training Generation Training Generation Training Generation Training</td></tr><tr><td>Time</td><td>1.45h</td><td>4.32h</td><td>1.45h</td><td>8.64h</td><td>1.45h</td><td>8.64h</td><td>1.45h</td><td>8.64h</td></tr></table>", "html": null, "num": null}, "TABREF9": {"type_str": "table", "text": "Performance of SPIN + DPO based on zephyr-7b-sft-full across HuggingFace Open LLM Leaderboard datasets, compared with all baselines. We also denote the average improvement over last iteration in the Average column.", "content": "<table><tr><td>Model</td><td>Arc</td><td colspan=\"5\">TruthfulQA Winogrande GSM8k HellaSwag MMLU</td><td>Average</td></tr><tr><td colspan=\"2\">zephyr-7b-dpo-full 63.65</td><td>55.19</td><td>72.61</td><td>33.43</td><td>84.44</td><td>58.52</td><td>61.31</td></tr><tr><td colspan=\"2\">zephyr-7b-sft-full 60.41</td><td>43.73</td><td>74.19</td><td>26.76</td><td>82.85</td><td>60.92</td><td>58.14</td></tr><tr><td>SPIN iteration 0</td><td>63.40</td><td>49.18</td><td>72.69</td><td>35.10</td><td>84.38</td><td>60.03</td><td>60.80 (+2.66)</td></tr><tr><td>SPIN iteration 1</td><td>65.19</td><td>55.17</td><td>72.30</td><td>35.78</td><td>84.96</td><td>59.34</td><td>62.12 (+1.32)</td></tr><tr><td>SPIN iteration 2</td><td>65.96</td><td>54.91</td><td>73.56</td><td>38.06</td><td>85.41</td><td>59.93</td><td>62.97 (+0.85)</td></tr><tr><td>SPIN iteration 3</td><td>65.87</td><td>54.90</td><td>73.72</td><td>38.97</td><td>85.54</td><td>59.99</td><td>63.16 (+0.19)</td></tr><tr><td>SPIN iteration 3 + DPO</td><td>66.47</td><td>60.07</td><td>78.06</td><td>37.98</td><td>86.17</td><td>59.68</td><td>64.05 (+0.89)</td></tr></table>", "html": null, "num": null}, "TABREF10": {"type_str": "table", "text": "Test performance of SPIN based on zephyr-7b-sft-full across HuggingFace Open LLM Leaderboard datasets. We also denote the average improvement over last iteration in the Average column.", "content": "<table><tr><td>Model</td><td>Arc</td><td colspan=\"5\">TruthfulQA Winogrande GSM8k HellaSwag MMLU</td><td>Average</td></tr><tr><td colspan=\"2\">zephyr-7b-sft-full 60.41</td><td>43.73</td><td>74.19</td><td>26.76</td><td>82.85</td><td>60.92</td><td>58.14</td></tr><tr><td>SPIN iteration 0</td><td>63.40</td><td>49.18</td><td>72.69</td><td>35.10</td><td>84.38</td><td>60.03</td><td>60.80 (+2.66)</td></tr><tr><td>SPIN iteration 1</td><td>65.19</td><td>55.17</td><td>72.30</td><td>35.78</td><td>84.96</td><td>59.34</td><td>62.12 (+1.32)</td></tr><tr><td>SPIN iteration 2</td><td>65.96</td><td>54.91</td><td>73.56</td><td>38.06</td><td>85.41</td><td>59.93</td><td>62.97 (+0.85)</td></tr><tr><td>SPIN iteration 3</td><td>65.87</td><td>54.90</td><td>73.72</td><td>38.97</td><td>85.54</td><td>59.99</td><td>63.16 (+0.19)</td></tr></table>", "html": null, "num": null}, "TABREF11": {"type_str": "table", "text": "Test performance of zephyr-7b-sft-full fine-tuned on Ultrachat200k for 1 more epoch across HuggingFace Open LLM benchmark datasets. SFT fails to further leverage the fine-tuning data for performance enhancement and even results in degraded performance.", "content": "<table><tr><td>Model</td><td>Arc</td><td colspan=\"6\">TruthfulQA Winogrande GSM8k HellaSwag MMLU Average</td></tr><tr><td colspan=\"2\">zephyr-7b-sft-full 60.41</td><td>43.73</td><td>74.19</td><td>26.76</td><td>82.85</td><td>60.92</td><td>58.14</td></tr><tr><td>SFT epoch 1</td><td>57.76</td><td>44.39</td><td>75.77</td><td>25.85</td><td>81.69</td><td>57.89</td><td>57.23</td></tr></table>", "html": null, "num": null}, "TABREF12": {"type_str": "table", "text": "Test performance on other reasoning benchmark datasets for SPIN at different iterations and zephyr-7b-sft-full. We report the average score for MT-Bench and the accuracy score for Big Bench datasets under standard few-shot CoT evaluation. On OpenBookQA, we report acc_norm with 1-shot example as used inAnil et al. (2023). As similar to Open LLM Leaderboard evaluation, we observe a steady improvement in performance on the other benchmark tasks, with no significant degradation.", "content": "<table><tr><td>Model</td><td colspan=\"5\">MT-Bench BB-causal BB-formal BB-sports OpenBookQA</td></tr><tr><td>zephyr-7b-sft-full</td><td>5.94</td><td>56.15</td><td>49.6</td><td>96.0</td><td>45.4</td></tr><tr><td>SPIN iteration 0</td><td>6.46 (+0.52)</td><td>57.75</td><td>51.6</td><td>95.2</td><td>46.8</td></tr><tr><td>SPIN iteration 1</td><td>6.65 (+0.19)</td><td>58.82</td><td>51.2</td><td>95.2</td><td>47.2</td></tr><tr><td>SPIN iteration 2</td><td>6.78 (+0.13)</td><td>59.36</td><td>51.2</td><td>94.4</td><td>47.6</td></tr></table>", "html": null, "num": null}, "TABREF13": {"type_str": "table", "text": "Generation example of our fine-tuned model by SPIN at different iterations.PromptHow does the economic growth of Horsham compare to nearby towns?Ground truth I do not have access to the latest economic data on horsham and nearby towns. However, you can check the latest economic statistics and reports provided by the local government or the official websites of horsham and nearby towns for a detailed comparison of their economic growth.", "content": "<table/>", "html": null, "num": null}, "TABREF14": {"type_str": "table", "text": "•∥•) is KL-divergence. JSD is always non-negative and equals zero if and only if p + and p -are identical. Moreover, the global minimum value log 2 -JSD(p + ∥p -) is achieved by f * if and only if,f * (x, y) = Z(x) + log p data (y|x) p θt (y|x) ,where Z(x) is any function that is possibly dependent on x.Proof of Lemma C.2. We rewrite the objective function in the following formula,2E x∼q(•),y∼p data (•|x),y ′ ∼p θ t (•|x) ℓ f (x, y) -f (x, y ′ ) = q(x)p data (y|x)p θt (y ′ |x) ℓ f (x, y) -f (x, y ′ ) dydy ′ + q(x)p data (y ′ |x)p θt (y|x) ℓ f (x, y ′ ) -f (x, y) dydy ′ = q(x)p data (y|x)p θt (y ′ |x)ℓ f (x, y) -f (x, y ′ ) + q(x)p data (y ′ |x)p θt (y|x)ℓ f (x, y ′ ) -f (x, y) dydy ′ data (y|x)p θt (y ′ |x) log 1 + p data (y ′ |x)p θt (y|x) p data (y|x)p θt (y ′ |x) + q(x)p data (y ′ |x)p θt (y|x) log 1 + p data (y|x)p θt (y ′ |x) p data (y ′ |x)p θt (y|x) dydy Recall that p + (y, y ′ |x) = p data (y|x) • p θt (y|x) and p -(y, y ′ |x) = p θt (y|x) • p data (y|x).Then, the right-hand side of (i) can be written asq(x)p data (y|x)p θt (y ′ |x) log 1 + p data (y ′ |x)p θt (y|x) p data (y|x)p θt (y ′ |x) + q(x)p data (y ′ |x)p θt (y|x) log 1 + p data (y|x)p θt (y ′ |x) p data (y ′ |x)p θt (y|x) dydy ′ = p + (y, y ′ |x) log 1 + p -(y, y ′ |x) p + (y, y ′ |x) + p -(y, y ′ |x) log 1 + p + (y, y ′ |x) p -(y, y ′ |x) dydy = 2 log 2 -KL p + p + + p - 2 -KL p - p + + p - 2 = 2 log 2 -2 • JSD(p + ∥p -),", "content": "<table><tr><td>(i)</td><td/></tr><tr><td>≥</td><td>q(x)p</td></tr></table>", "html": null, "num": null}, "TABREF15": {"type_str": "table", "text": "1/λ .Therefore, there exists a function Z(x) such thatp θ (y|x) = Z(x) • p θt (y|x) p data (y|x)/p θt (y|x) Since θ t+1 is the global minimum of L SPIN (θ, θ t ). Then by (C.4), λ log p θ t+1 (y|x) p θ t (y|x)should be the global minimum of problem (C.3). By Lemma C.2, there exists Z(x) such that which leads to the result that p θt+1 (y|x) ∝ p θt (y|x) p data (y|x)/p θt (y|x) 1/λ .", "content": "<table><tr><td/><td/><td/><td/><td/><td>1/λ .</td><td>(C.2)</td></tr><tr><td colspan=\"4\">Applying logarithm function on both side of (C.2) yields</td><td/></tr><tr><td colspan=\"2\">λ log( Z(x)) + log</td><td colspan=\"2\">p data (y|x) p θt (y|x)</td><td>= λ log</td><td>p θ (y|x) p θt (y|x)</td><td>∈ F t .</td></tr><tr><td>Therefore, we have proved that</td><td/><td/><td/><td/></tr><tr><td>min</td><td/><td/><td/><td/></tr><tr><td/><td/><td/><td/><td/><td>(C.4)</td></tr><tr><td>λ log</td><td colspan=\"2\">p θt+1 (y|x) p θt (y|x)</td><td colspan=\"2\">= Z(x) + log</td><td>p data (y|x) p θt (y|x)</td><td>,</td></tr></table>", "html": null, "num": null}}}}