{"paper_id": "LeanEuclid", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:34:47.096552Z"}, "title": "Autoformalizing Euclidean Geometry", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Toronto", "location": {}}, "email": "<<EMAIL>>"}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": "<<EMAIL>>"}, {"first": "Jialiang", "middle": [], "last": "Sun", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Toronto", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Toronto", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Si", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Toronto", "location": {}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Autoformalization involves automatically translating informal math into formal theorems and proofs that are machine-verifiable. Euclidean geometry provides an interesting and controllable domain for studying autoformalization. In this paper, we introduce a neuro-symbolic framework for autoformalizing Euclidean geometry, which combines domain knowledge, SMT solvers, and large language models (LLMs). One challenge in Euclidean geometry is that informal proofs rely on diagrams, leaving gaps in texts that are hard to formalize. To address this issue, we use theorem provers to fill in such diagrammatic information automatically, so that the LLM only needs to autoformalize the explicit textual steps, making it easier for the model. We also provide automatic semantic evaluation for autoformalized theorem statements. We construct LeanEuclid, an autoformalization benchmark consisting of problems from Euclid's Elements and the UniGeo dataset formalized in the Lean proof assistant. Experiments with GPT-4 and GPT-4V show the capability and limitations of state-of-the-art LLMs on autoformalizing geometry problems. The data and code are available at https://github. com/loganrjmurphy/LeanEuclid.", "pdf_parse": {"paper_id": "LeanEuclid", "_pdf_hash": "", "abstract": [{"text": "Autoformalization involves automatically translating informal math into formal theorems and proofs that are machine-verifiable. Euclidean geometry provides an interesting and controllable domain for studying autoformalization. In this paper, we introduce a neuro-symbolic framework for autoformalizing Euclidean geometry, which combines domain knowledge, SMT solvers, and large language models (LLMs). One challenge in Euclidean geometry is that informal proofs rely on diagrams, leaving gaps in texts that are hard to formalize. To address this issue, we use theorem provers to fill in such diagrammatic information automatically, so that the LLM only needs to autoformalize the explicit textual steps, making it easier for the model. We also provide automatic semantic evaluation for autoformalized theorem statements. We construct LeanEuclid, an autoformalization benchmark consisting of problems from Euclid's Elements and the UniGeo dataset formalized in the Lean proof assistant. Experiments with GPT-4 and GPT-4V show the capability and limitations of state-of-the-art LLMs on autoformalizing geometry problems. The data and code are available at https://github. com/loganrjmurphy/LeanEuclid.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Euclidean geometry is one of the oldest branches of mathematics. It has served as a test of human intelligence for more than two millennia and has recently been used to test AI. Substantial work has focused on solving geometry problems automatically (<PERSON>, 2008) , e.g., AlphaGeometry (<PERSON><PERSON> et al., 2023) can solve some of the IMO geometry problems. These methods consume problems and produce solutions in structured formats. In this work, we address a complementary Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s). task, autoformalization: Can AI understand human-written problems/solutions and translate them automatically into formal theorems/proofs? Specifically, we focus on the setting where formal theorems/proofs can be verified by the <PERSON><PERSON> proof assistant (<PERSON> & <PERSON>, 2021) . Lean provides a language for writing formal proofs. It is popular among mathematicians and has a growing ecosystem of integration with large language models (LLMs), e.g., <PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON> et al., 2023) and <PERSON><PERSON> (<PERSON> et al., 2024) .", "cite_spans": [{"start": 250, "end": 260, "text": "(<PERSON>, 2008)", "ref_id": null}, {"start": 269, "end": 303, "text": "AlphaGeometry (<PERSON><PERSON> et al., 2023)", "ref_id": null}, {"start": 527, "end": 569, "text": "Learning, Vienna, Austria. PMLR 235, 2024.", "ref_id": null}, {"start": 852, "end": 878, "text": "(<PERSON> & <PERSON>, 2021)", "ref_id": null}, {"start": 1052, "end": 1080, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON> et al., 2023)", "ref_id": null}, {"start": 1098, "end": 1117, "text": "(<PERSON> et al., 2024)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "We demonstrate that Euclidean geometry provides an interesting and controllable domain for autoformalization. First, an automatic evaluation of autoformalized theorems is difficult in general but feasible in Euclidean geometry. Second, the logical gaps in informal proofs are well understood in Euclidean geometry, making it easier to faithfully formalize the proofs. Third, combining text-based and diagrammatic reasoning makes Euclidean geometry a natural domain to study multimodal reasoning models. Therefore, autoformalizing Euclidean geometry is an attractive target for AI.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Evaluating Autoformalized Theorem Statements. Despite the promise of machine learning and LLMs in autoformalizing theorems (<PERSON> et al., 2022) , a major roadblock is the lack of reliable and automatic evaluation. Comparing the model output verbatim with ground truth would be too rigid since there are many valid ways to formalize a theorem. Checking the logical equivalence between two theorems is generally intractable. Researchers have resorted to proxy metrics such as the BLEU score (<PERSON><PERSON><PERSON> et al., 2002) . However, LLMs can score high on such metrics without generating correct formalization (<PERSON> et al., 2023b) . Alternatively, human evaluation is widely used as a last resort, but it is costly, especially if we want to use the results to improve the method iteratively.", "cite_spans": [{"start": 123, "end": 140, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 486, "end": 509, "text": "(<PERSON><PERSON><PERSON> et al., 2002)", "ref_id": null}, {"start": 598, "end": 619, "text": "(<PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Our Approach to Evaluating Autoformalization. To overcome the evaluation bottleneck, we introduce a new automatic approach for evaluating the semantics of autoformalized theorems. The key insight is that equivalence checking can be made feasible in specific domains (such as Euclidean geometry) by combining domain knowledge with automated reasoning tools, such as satisfiability modulo theories (SMT) solvers (Barrett & Tinelli, 2018) .", "cite_spans": [{"start": 410, "end": 435, "text": "(<PERSON> & <PERSON>, 2018)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "To evaluate the autoformalized theorems, we develop a sym- The model autoformalizes the problem into a formal theorem (proposition 1'), which is evaluated by checking its logical equivalence with the ground truth (proposition 1), leveraging domain knowledge and a symbolic automated reasoning engine based on SMT (satisfiability modulo theories) solvers. Bottom right: A proof autoformalized by the model. Like <PERSON><PERSON><PERSON>'s proofs, it does not need to handle diagrammatic reasoning explicitly. <PERSON><PERSON> can check the proof to identify a list of diagrammatic reasoning gaps, e.g., \"intersects BCD ACE\". Then, it attempts to fill in all gaps automatically using the symbolic reasoning engine based on SMT solvers.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "bolic reasoning engine based on SMT solvers. As Fig. 1 (Top right) shows, given a ground-truth formal theorem T gt and the autoformalized theorem T pred produced by a language model, we use the symbolic engine to try to prove their equivalence (T gt ⇔ T pred ). If successful, their logical gap is small enough to conclude that T pred is correct. Even if the symbolic engine cannot prove T gt ⇔ T pred , it can provide partial results useful for a more fine-grained analysis. We validate this evaluation protocol by showing it correlates well with human evaluation.", "cite_spans": [], "ref_spans": [{"start": 53, "end": 54, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "LeanEuclid: Formalizing Proofs and Diagrams. We construct LeanEuclid, a benchmark for testing machine learning on autoformalizing Euclidean geometry. As in Fig 1 (Left) , each example in LeanEuclid has an informal theorem, proof, and diagram in L A T E X, as well as a formal theorem and proof in Lean. Data examples in LeanEuclid are manually formalized into Lean from Euclid's Elements (<PERSON><PERSON><PERSON>, 2007) and the UniGeo dataset (<PERSON> et al., 2022) .", "cite_spans": [{"start": 162, "end": 168, "text": "(Left)", "ref_id": null}, {"start": 388, "end": 403, "text": "(<PERSON><PERSON><PERSON>, 2007)", "ref_id": "BIBREF12"}, {"start": 427, "end": 446, "text": "(<PERSON> et al., 2022)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "LeanEuclid serves as a benchmark for autoformalizing not only theorems but also proofs. Geometric proofs are challenging to formalize faithfully. Humans (ancient or modern, including <PERSON><PERSON><PERSON> himself) use diagrams to license proof steps without making every detail explicit. Fig. 1 shows an example of diagrammatic reasoning from <PERSON><PERSON><PERSON>'s Elements.", "cite_spans": [], "ref_spans": [{"start": 278, "end": 279, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Euclid uses the intersection of two circles (C) without proving its existence. Most readers would not find the proof problematic, as the two circles intersect in the diagram. Such implicit diagrammatic reasoning is ubiquitous in informal geometric proofs but needs to be handled explicitly in formal proofs (<PERSON><PERSON> et al., 2019) . Therefore, a naive attempt to autoformalize the proofs would be difficult, as it requires the model to fill in many diagrammatic reasoning gaps, with nothing to reference in the informal texts.", "cite_spans": [{"start": 307, "end": 328, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF6"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "To mitigate diagrammatic gaps, LeanEuclid adopts a formal system named E (<PERSON><PERSON><PERSON><PERSON> et al., 2009) , introduced by philosophers for modeling diagrammatic reasoning in <PERSON><PERSON><PERSON>'s Elements. It teases out a set of diagrammatic rules so that diagrammatic reasoning can be modeled as logical deductions. We implement E in Lean and provide proof automation to fill in diagrammatic reasoning gaps, using the same symbolic reasoning engine developed for equivalence checking. Our system enables formalizing all 48 theorems and proofs from Elements (Book I), following <PERSON><PERSON><PERSON>'s original proofs as closely as possible, with diagrammatic reasoning carried out implicitly and automatically (see Fig. 1 ). The data is included in LeanEuclid, making autoformalizing Euc<PERSON>'s proofs feasible. The language model now only needs to autoformalize the explicit textual proof steps, leaving the \"obvious\" implicit reasoning to the symbolic engine.", "cite_spans": [{"start": 73, "end": 94, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF2"}], "ref_spans": [{"start": 682, "end": 683, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "LLMs for Autoformalization. Using LeanEuclid, we experimentally investigate the capability of GPT-4 (Ope-nAI, 2023) and GPT-4V (with vision) on autoformalizing theorems and proofs in Euclidean geometry. With 5-shot prompting, GPT-4 can autoformalize only 18.9% of the theorems correctly, demonstrating the difficulty of LeanEuclid. GPT-4V performs slightly better (21.0%), showing the potential benefit of multimodal models. Furthermore, GPT-4V can autoformalize 23.1% of LeanEuclid proofs correctly, generating the entire proof without searching the space of individual steps. Finally, our study shows that imperfect autoformalized proofs can often be manually repaired into correct proofs through a few simple modifications.", "cite_spans": [{"start": 94, "end": 115, "text": "GPT-4 (Ope-nAI, 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Contributions. We make three main contributions: First, we introduce an SMT-based symbolic engine for automatically evaluating the semantics of theorem statements in Euclidean geometry. Second, we demonstrate the feasibility of autoformalizing only explicit geometric proof steps, relying on the symbolic engine for implicit diagrammatic reasoning. Third, our experiments reveal the capability and limitations of state-of-the-art LLMs on autoformalizing Euclidean geometry. Overall, our approach demonstrates Euclidean geometry as a promising target for autoformalization. It may facilitate autoformalization research to move from human inspection to automatic evaluation. Such a transition is essential for accelerating the development of machine learning methods on this task and ultimately easing the human effort required by formalization.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Neural Theorem Proving and Autoformalization. Machine learning and LLMs can prove theorems using proof assistants (<PERSON><PERSON> & <PERSON>, 2020; <PERSON> & <PERSON>, 2019; <PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2022) . In theorem proving, everything is formal:", "cite_spans": [{"start": 114, "end": 138, "text": "(Polu & Sutskever, 2020;", "ref_id": null}, {"start": 139, "end": 157, "text": "Yang & <PERSON>, 2019;", "ref_id": null}, {"start": 158, "end": 176, "text": "<PERSON> et al., 2023;", "ref_id": null}, {"start": 177, "end": 196, "text": "First et al., 2023;", "ref_id": null}, {"start": 197, "end": 217, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": null}, {"start": 218, "end": 235, "text": "<PERSON> et al., 2022)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "The model generates a formal proof given a formal theorem.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "In contrast, we focus on autoformalization, which aims to translate informal math into formal theorems and proofs. <PERSON> et al. (2024) provides a comprehensive survey on deep learning for theorem proving and autoformalization.", "cite_spans": [{"start": 115, "end": 131, "text": "<PERSON> et al. (2024)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Machine learning for autoformalization is strained by the lack of informal-formal pairs for training. Manually annotating such a dataset would be prohibitively expensive.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Researchers have explored two approaches to overcome data scarcity. First, we can generate synthetic training data either procedurally (<PERSON> et al., 2018; <PERSON> et al., 2023) or using LLMs (<PERSON> et al., 2023; <PERSON> et al., 2023b) . Second, we can leverage the few-shot capability of LLMs such as GPT-4 (OpenAI, 2023) . We only need to annotate a few examples for in-context demonstration and a relatively small testing set (<PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2023a; <PERSON><PERSON><PERSON><PERSON> et al., 2023) . LeanEuclid follows the latter approach and is intended as a benchmark for testing only.", "cite_spans": [{"start": 135, "end": 154, "text": "(<PERSON> et al., 2018;", "ref_id": null}, {"start": 155, "end": 179, "text": "<PERSON> et al., 2023)", "ref_id": null}, {"start": 194, "end": 213, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF17"}, {"start": 214, "end": 234, "text": "<PERSON> et al., 2023b)", "ref_id": null}, {"start": 301, "end": 321, "text": "GPT-4 (OpenAI, 2023)", "ref_id": null}, {"start": 428, "end": 445, "text": "(<PERSON> et al., 2022;", "ref_id": null}, {"start": 446, "end": 467, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF0"}, {"start": 468, "end": 488, "text": "<PERSON> et al., 2023a;", "ref_id": null}, {"start": 489, "end": 512, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Autoformalized theorems are difficult to evaluate, so all existing works have resorted to human evaluation. In addition, they have used proxy metrics such as compilation rates (<PERSON> et al., 2023b; <PERSON><PERSON><PERSON><PERSON> et al., 2023) and the BLEU score (<PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2023) , none of which is sufficiently accurate. In contrast, we are the first to evaluate the semantics of autoformalized statements reliably and automatically.", "cite_spans": [{"start": 176, "end": 197, "text": "(<PERSON> et al., 2023b;", "ref_id": null}, {"start": 198, "end": 221, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF3"}, {"start": 241, "end": 258, "text": "(<PERSON> et al., 2022;", "ref_id": null}, {"start": 259, "end": 282, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Formalizing Euclidean Geometry. To our knowledge, no machine learning method has attempted to autoformalize Euclidean geometry. <PERSON><PERSON><PERSON>'s (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2013 ) and <PERSON><PERSON>'s axioms (<PERSON><PERSON>, 2013) are two classical axiomatic systems that put Euclidean geometry on a rigorous foundation. <PERSON><PERSON> et al. (2019) uses a variant of <PERSON><PERSON><PERSON>'s system to manually formalize <PERSON><PERSON><PERSON>'s Elements (Book I) in the Coq proof assistant (<PERSON><PERSON> et al., 1997) . Hernandez<PERSON>Espiet ( 2023) is an ongoing work formalizing the book in Lean. However, they require formal proofs to explicitly handle all gaps left by diagrammatic reasoning. As a result, formal proofs in their systems are much more verbose than Euclid's proofs (examples in Appendix C), making their systems unsuitable for studying autoformalization.", "cite_spans": [{"start": 137, "end": 163, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2013", "ref_id": null}, {"start": 187, "end": 202, "text": "(<PERSON><PERSON>, 2013)", "ref_id": "BIBREF14"}, {"start": 293, "end": 313, "text": "<PERSON><PERSON> et al. (2019)", "ref_id": "BIBREF6"}, {"start": 424, "end": 445, "text": "(<PERSON><PERSON> et al., 1997)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Proofs in Euclidean geometry have gaps due to diagrammatic reasoning. However, for more than two millennia, they have seldom troubled Elements' readers around the world. Philosophers have posited many theories aiming at a systematic understanding of diagrammatic reasoning (<PERSON>, 2001; Mumma, 2010) . In particular, we build upon the formal system E (<PERSON><PERSON><PERSON><PERSON> et al., 2009) and are the first to turn this framework into a practical system that includes an automated procedure for diagrammatic reasoning.", "cite_spans": [{"start": 273, "end": 287, "text": "(<PERSON>, 2001;", "ref_id": "BIBREF23"}, {"start": 288, "end": 300, "text": "<PERSON><PERSON>, 2010)", "ref_id": null}, {"start": 352, "end": 373, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Geometric Problem Solving. Automatic geometry problem solvers have been studied extensively (<PERSON><PERSON><PERSON>, 1975; <PERSON> et al., 1995) . <PERSON>'s method (<PERSON>, 2008) and <PERSON><PERSON><PERSON><PERSON> bases method (<PERSON><PERSON><PERSON>, 2005) transform geometric problems into algebraic equations. AlphaGeometry (<PERSON><PERSON> et al., 2023) is a concurrent work that successfully solves many IMO geometry problems. Similar to us, it is a hybrid system using neural networks for difficult proof steps (e.g., constructing new geometric objects) and a symbolic engine for deducing simple facts. However, our method focuses on autoformalizing human-written theorems/proofs instead of finding new proofs. AlphaGeometry consumes problems and produces solutions in structured representations, without handling informal mathematical language. Furthermore, they represent theorems and proofs in a domain-specific language tailored for Euclidean geometry, whereas we use <PERSON><PERSON>: a general-purpose proof assistant. Lean provides a highly trusted foundation for the soundness of our system, 1 and it opens up opportunities to interoperate with other branches of mathematics formalized in <PERSON>n's mathematical library (The mathlib Community, 2020).", "cite_spans": [{"start": 92, "end": 106, "text": "(<PERSON><PERSON><PERSON>, 1975;", "ref_id": "BIBREF25"}, {"start": 107, "end": 126, "text": "<PERSON> et al., 1995)", "ref_id": null}, {"start": 141, "end": 151, "text": "(<PERSON>, 2008)", "ref_id": null}, {"start": 177, "end": 195, "text": "(<PERSON><PERSON><PERSON>, 2005)", "ref_id": "BIBREF7"}, {"start": 265, "end": 285, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Euclidean geometry has also served as benchmarks in machine learning and NLP for multimodal understanding of geometric problems and diagrams (<PERSON> et al., 2021; <PERSON> et al., 2021a; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) . These methods produce informal solutions given informal problem statements, whereas we formalize both problems and solutions. Uni<PERSON><PERSON> (<PERSON> et al., 2022) is a dataset consisting of 9,543 informal theorems and proofs. We formalize 125 random examples from UniGeo and include them in LeanEuclid, which shows the generality of our formalization beyond Euclid's Elements.", "cite_spans": [{"start": 141, "end": 158, "text": "(<PERSON> et al., 2021;", "ref_id": null}, {"start": 159, "end": 178, "text": "<PERSON> et al., 2021a;", "ref_id": null}, {"start": 179, "end": 196, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF9"}, {"start": 197, "end": 215, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 216, "end": 236, "text": "<PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF17"}, {"start": 237, "end": 256, "text": "<PERSON> et al., 2023)", "ref_id": null}, {"start": 392, "end": 411, "text": "(<PERSON> et al., 2022)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "To construct the LeanEuclid benchmark, we implement a variant of system E (<PERSON><PERSON><PERSON><PERSON> et al., 2009) in Lean and use it to formalize Euclidean geometry problems. LeanEuclid has two key features: First, it supports implicit diagrammatic reasoning, which is critical to faithfully formalizing geometric proofs. Second, it can check the logical equivalence between theorems, which is essential for automatically evaluating the autoformalized theorems.", "cite_spans": [{"start": 74, "end": 95, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "Formalizing Euclidean Geometry in Lean", "sec_num": "3."}, {"text": "Proof assistants, a.k.a. interactive theorem provers, are languages and IDEs for humans to write formal proofs that can be checked by computers (<PERSON> et al., 2016; <PERSON><PERSON> et al., 2017) . Widely used proof assistants include <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. They are similar from a machine learning perspective. Fig. 1 (Middle) shows examples of theorems and proofs in Lean. Each theorem has a name and a theorem statement.", "cite_spans": [{"start": 144, "end": 164, "text": "(<PERSON> et al., 2016;", "ref_id": "BIBREF19"}, {"start": 165, "end": 184, "text": "<PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF10"}, {"start": 303, "end": 318, "text": "Fig. 1 (Middle)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "The Lean Proof Assistant", "sec_num": "3.1."}, {"text": "A proof consists of multiple steps, and each step is called a \"tactic\". For example, \"use c\" and \"euclid apply circle from points b a as ACE\" are tactics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Lean Proof Assistant", "sec_num": "3.1."}, {"text": "Given a theorem and its proof, <PERSON><PERSON> can check the proof and output an error message if it is incorrect. <PERSON><PERSON> can also check if the theorem is well-formed. The percentage of well-formed theorems autoformalized by the model is widely used as a proxy metric in prior works (<PERSON> et al., 2023b; <PERSON><PERSON><PERSON><PERSON> et al., 2023) . However, well-formedness does not guarantee correctness, since a predicted theorem statement can be well-formed but have a different meaning from the ground truth.", "cite_spans": [{"start": 270, "end": 291, "text": "(<PERSON> et al., 2023b;", "ref_id": null}, {"start": 292, "end": 315, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "The Lean Proof Assistant", "sec_num": "3.1."}, {"text": "1 Our symbolic reasoning engine (Sec. 3.3) relies on SMT solvers, whose results currently cannot be fully certified by Lean.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Lean Proof Assistant", "sec_num": "3.1."}, {"text": "E is a formal system for modeling theorems and proofs in Euclidean geometry inspired by <PERSON><PERSON><PERSON>'s Elements. It defines basic geometric objects (e.g., points and lines) and relations between them (e.g., a point being on a line). It also prescribes how to formally state and prove theorems. E is a conceptual framework that can be implemented within any proof assistant. We explain our Lean implementation using proposition 1 in Fig. 1 as a running example. For a complete specification of E, please refer to Appendix A and <PERSON>'s paper (Aviga<PERSON> et al., 2009) .", "cite_spans": [{"start": 531, "end": 552, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF2"}], "ref_spans": [{"start": 431, "end": 432, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "Geometric Objects, Relations, and Theorems. E has six types of objects: points, lines, segments, circles, angles, and triangles. They can form various relations, e.g., \"onLine a AB\" means the point a is on the line AB. Some relations are defined as syntactic sugar of more basic relations, e.g., \"distinctPointsOnLine a b AB\" is the conjunction of \"onLine a AB\", \"onLine b AB\", and \"a ̸ = b\". A segment (a--b) is specified by its two endpoints a and b, and it has a length |(a--b)| ∈ R. Using these concepts, it is straightforward to understand the theorem statement of proposition 1:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "theorem proposition_1 : ∀ (a b : Point) (AB : Line), distinctPointsOnLine a b AB → ∃ c : Point, |(c--a)| = |(a--b)| ∧ |(c--b)| = |(a--b)|", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "Like above, most theorems in Euclidean geometry take the form of given existing objects satisfying certain properties, constructing new objects satisfying certain properties. Below is the general form of theorems in E:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "∀ ( - → P : P oint) ( - → L : Line) ( - → C : Circle), m i=1 a i → ∃ ( -→ P ′ : P oint) ( - → L ′ : Line) ( -→ C ′ : Circle), n j=1 a ′ j That is, given points - → P , lines - → L , and circles - → C satisfying m i=1 a i , one can construct points -→ P ′ , lines - → L ′ , and circles -→ C ′ satisfying n j=1 a ′ j . Each clause a i is called a \"precondi- tion\". Logically, it is a disjunction of literals with variables from - → P , - → L , and - → C . Each a ′ j is called a \"postcondition\", which is a disjunction of literals with variables from - → P , - → L , - → C , -→ P ′ , - → L ′", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": ", and -→ C ′ . Variable sets can be empty. For example, in proposition 1, we have", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "- → C = - → L ′ = -→ C ′ = ∅.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "Axioms. Axioms take the same form as theorems, but they are built into the system and do not need to be proved. Appendix A contains the complete list of axioms in our instantiation of the formal system E. They fall into two categories: construction rules and non-construction rules. 2 Construction rules construct new objects, i.e., at least one of -→ P ′ , -→ L ′ , and -→ C ′ must be non-empty. For example, circle from points constructs a circle α given a point a as its center and another point b on it: Non-construction rules deduce properties of existing objects without creating new objects. For examples:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "axiom center_inside_circle : ∀ (a : Point) (α : Circle), isCenter a α → insideCircle a α axiom intersection_circle_circle_2: ∀ (a b : Point) (α β : Circle), (onCircle a α) ∧ (insideCircle b α) ∧ (insideCircle a β) ∧ (onCircle b β) → intersectsCircle α β", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "The distinction between construction and non-construction rules is important for automated reasoning in Sec. 3.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "Proofs. We implement the formal system E in Lean. Therefore, a proof can be made of arbitrary tactics as long as the entire proof can be checked by <PERSON>n. Nevertheless, we provide several customized tactics that can cover most steps used by <PERSON><PERSON><PERSON> (complete list in Appendix A). The most important tactic we provide is euclid apply, which applies a rule (either an axiom or an existing theorem) in the forward direction. Taking intersection circles as an example, when using this rule, one should explicitly specify two circles, say, BCD and ACE:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "intersection_circles BCD ACE : intersectsCircle BCD ACE → ∃ c : Point, (onCircle c BCD) ∧ (onCircle c ACE)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "\"euclid apply intersection circles BCD ACE as c\" instructs <PERSON><PERSON> to check if the precondition \"intersectsCircle BCD ACE\" is a known fact. If so, it introduces a new point c and two new facts, \"onCircle c BCD\" and \"onCircle c ACE\". If not, it triggers diagrammatic reasoning, trying to prove \"intersectsCircle BCD ACE\" using a symbolic 2 \"Non-construction rules\" include diagrammatic, metric, and transfer rules in E's original terminology (<PERSON><PERSON>ga<PERSON> et al., 2009) . automated reasoning engine (Sec. 3.3). If successful, it proceeds as normal. Otherwise, the tactic fails. Fig. 2 shows three proof steps using euclid apply with different rules. The last step triggers diagrammatic reasoning. ", "cite_spans": [{"start": 438, "end": 459, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF2"}], "ref_spans": [{"start": 573, "end": 574, "text": "2", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "The Formal System E", "sec_num": "3.2."}, {"text": "We introduce a symbolic reasoning engine for deducing \"obvious\" facts from existing geometric objects and relations. It has two important uses in LeanEuclid: (1) diagrammatic reasoning, as shown in the previous example, and (2) checking the equivalence between theorems.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Symbolic Reasoning Engine Based on SMT", "sec_num": "3.3."}, {"text": "In Euclidean geometry, a natural way to measure obviousness is whether construction rules are used. Applying construction rules (e.g., drawing auxiliary lines) requires creativity, which is non-obvious and hard to automate. In contrast, non-construction rules are mechanic and more amenable to automated reasoning. Intuitively, they do not introduce new objects and will not explode the search space. Therefore, we design the symbolic engine to be an automated theorem prover equipped with non-construction rules in E.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Symbolic Reasoning Engine Based on SMT", "sec_num": "3.3."}, {"text": "Instead of generic first-order provers (<PERSON><PERSON><PERSON>, 2013) , our symbolic engine builds on top of SMT solvers. They have efficient decision procedures for handling real numbers, which is important for reasoning about geometric quantities such as lengths, angles, and areas. Given a target conclusion (e.g., \"intersectsCircle BCD ACE\" in Fig. 2 ), our symbolic engine proves it by refutation: It generates a list of SMT formulas, including all non-construction rules in E, all existing objects/relations, and the negation of the conclusion. Then, it runs off-the-shelf SMT solvers to check if the list of formulas is satisfiable. The target conclusion is successfully proved if the solver returns UNSAT (not satisfiable).", "cite_spans": [{"start": 39, "end": 53, "text": "(<PERSON><PERSON><PERSON>, 2013)", "ref_id": null}], "ref_spans": [{"start": 337, "end": 338, "text": "2", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Symbolic Reasoning Engine Based on SMT", "sec_num": "3.3."}, {"text": "In constructing LeanEuclid, we manually formalized 173 theorems/proofs in Lean. 48 examples are from <PERSON><PERSON><PERSON>'s Elements (Book I),3 and 125 are adapted from UniGeo (<PERSON> et al., 2022) . Our formal proofs of Elements vindicate <PERSON><PERSON><PERSON>'s use of diagrams, as they follow <PERSON><PERSON><PERSON>'s texts closely and leave diagrammatic reasoning implicit. We identified several small gaps in Elements. <PERSON><PERSON><PERSON> occasionally used a stronger version of a theorem he had proved. Furthermore, when there were multiple cases, <PERSON><PERSON><PERSON> would discuss only one and omit others. Most omissions are acceptable in modern mathematics, e.g., when multiple cases are symmetric. The only omission we find questionable is in the proof of Proposition 24, where <PERSON><PERSON><PERSON> discussed only a simple case but omitted a more challenging case (details in Appendix B).", "cite_spans": [{"start": 162, "end": 181, "text": "(<PERSON> et al., 2022)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "LeanEuclid: Overview and Dataset Statistics", "sec_num": "3.4."}, {"text": "UniGeo contains problems in five categories: triangle, congruent, similarity, quadrilateral, and parallel. LeanEuclid includes 25 random problems from each category. Problems from UniGeo are generally easier than those from Elements. Each problem comes with a diagram and a textual question. Unlike Elements, UniGeo's text does not include complete information about the problem, so we manually add missing diagrammatic details to the text.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LeanEuclid: Overview and Dataset Statistics", "sec_num": "3.4."}, {"text": "Next, we describe our approach to autoformalizing (and evaluating) theorems and proofs targeting LeanEuclid.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Euclidean Geometry with Large Language Models", "sec_num": "4."}, {"text": "Autoformalizing a theorem statement requires taking natural language input and generating the corresponding formal theorem in Lean. We use LLMs' in-context learning capability for autoformalization. Our prompt template contains an overview of LeanEuclid syntax, some examples of wellformed formulas, and various guidelines for the task. For few-shot learning, we also include k examples of informal-formal pairs as in-context demonstrations. The complete prompt template is in Appendix D.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing and Evaluating Theorems", "sec_num": "4.1."}, {"text": "When using our symbolic engine for checking the equivalence between theorems, we call it E3 (Euclidean Equivalence Engine). We also provide a Python wrapper for E3, supporting various configuration options and easy integration with different LLMs. We provide a high-level overview of E3's two primary evaluation procedures: logical equivalence and approximate equivalence.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing and Evaluating Theorems", "sec_num": "4.1."}, {"text": "Logical Equivalence. The logical equivalence procedure takes a predicted formula T pred and ground truth formula T gt and attempts to prove T pred ⇐⇒ T gt using SMT solvers. E3 performs a separate satisfiability check for each direction, each of which can succeed or fail. 4 Of course, T pred may actually be unsatisfiable (meaning it contains a contradiction), in which case we will always be able to prove T pred =⇒ T gt . As such, E3 also performs a separate check to see whether a contradiction can be found in T pred .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing and Evaluating Theorems", "sec_num": "4.1."}, {"text": "Approximate Equivalence. If logical equivalence cannot be proven, we may still want to gauge whether T pred is a semantically \"close\" formalization of T gt . For instance, T pred may differ from T gt by only a single extra precondition or postcondition. To this end, E3 also supports approximate equivalence checking, which is a low-level semantic comparison between the two formulas. Specifically, E3 will attempt to separately prove each of the preconditions and the postconditions of each formula in a clause-by-clause fashion. However, this requires choosing a unification of the bound variables of the two formulas. Since trying each possible unification is infeasible, we instead choose the best n unifications using a string similarity heuristic. For each candidate unification, we check how many pre/postconditions can be proved from one proposition to the other.5 ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing and Evaluating Theorems", "sec_num": "4.1."}, {"text": "To autoformalize proofs, we provide the LLM with a natural language description of the proposition, its natural language proof, and a formalization of the proposition in LeanEuclid. Its task is to produce a formal proof consisting of a sequence of tactics. We provide a prompt template similar to the one used to autoformalize theorem statements. In addition to a brief overview of system E, we describe the custom Lea-nEuclid tactics (e.g., euclid apply), and provide a list of axioms and theorems the model can use in its proof. Beyond the core axioms of E, the list of helper theorems available to the model depends on the proposition being proven. For example, if we are formalizing the proof of Proposition 10 in Elements, then the set of available helper theorems will include all versions of Propositions 1-9. Once the autoformalized proof is generated, we check its correctness using Lean and attempt to fill in diagrammatic reasoning gaps using the symbolic reasoning engine. The complete prompt template is given in Appendix D.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Proofs", "sec_num": "4.2."}, {"text": "Experimental Setup. We evaluated the efficacy of LLMs on autoformalizing theorem statements using GPT-4 and GPT-4V. The input modality for GPT-4 was restricted to textual questions, while GPT-4V extended this by including diagrams as well. Results. Table 1 shows the accuracies of autoformalizing theorem statements, where correctness is measured by using E3 to check logical equivalence. Results on the Elements and UniGeo parts of LeanEuclid are also shown separately. Across both parts, few-shot learning with 5 shots produces correct formalizations at a rate of 21%. We also see a small improvement when visual inputs are included. We emphasize that Table 1 was generated automatically by E3. While some autoformalized theorem statements are syntactic matches of their ground truth and, therefore, easily identified as correct, others are not. Manual evaluation of these instances is expensive and tedious, whereas E3 is able to identify these cases easily.", "cite_spans": [], "ref_spans": [{"start": 255, "end": 256, "text": "1", "ref_id": null}, {"start": 660, "end": 661, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Autoformalizing Theorem Statements", "sec_num": "5.1."}, {"text": "For cases where equivalence fails, E3 reports whether either of T gt =⇒ T pred or T pred =⇒ T gt can be proved, allowing us to partition the failed cases into different classes. For instance, two rounds of autoformalization with (1-shot) yielded 67 rejected predictions, for 31 of these we could prove only T gt =⇒ T pred , for 15 we could prove only T pred =⇒ T gt , and for 21 we could prove neither.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Theorem Statements", "sec_num": "5.1."}, {"text": "Given that only a minority of predictions are provably correct, we can send the remainder to E3's approximate equivalence checker to glean \"close\" formalizations. For brevity, we only showcase this evaluation on the results of GPT-4 (5shot) on Euclid's Elements. Of the 36 rejected predictions, 10 possess the correct quantity and type of bound variables, and so are amenable to approximate analysis. The results of this analysis are shown in Fig. 3 . For each proposition, we show the total number of clauses (preconditions and postconditions of both formulas) and the number of clauses that could be proved. We can see that, in addition to the 7 propositions that were provably equivalent, this round produced 5 propositions for which more than 90% of all proof obligations can be resolved. Some interesting examples produced during our experiments are in Appendix E. Comparison with Manual Evaluation. To compare the accuracy of E3 to manual evaluation, we took a sample of 86 formalizations and investigated them for false negatives. The examples were taken from the results of GPT-4 and GPT-4V (both 5-shot). Among the 86 autoformalized theorem statements, 16 were proved equivalent to the ground truth, and all 16 were judged to be correct by humans. The remaining 70 could not be proved equivalent, but human inspection revealed that 3 should be equivalent to the ground truth. This gives us an estimated false negative rate of 15.8%. These instances are shown in Appendix E.", "cite_spans": [], "ref_spans": [{"start": 448, "end": 449, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Autoformalizing Theorem Statements", "sec_num": "5.1."}, {"text": "With respect to false positives, we did not find indications of soundness bugs in E3. However, a false positive can occur when an autoformalized theorem happens to be provably equivalent to the ground truth, but is unlikely to be identified by a human as a \"faithful\" formalization of the given proposition. We identified only one such case from our experiments, which is also shown in Appendix E.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Theorem Statements", "sec_num": "5.1."}, {"text": "Why is Formalizing Theorem Statements Hard? Compared to previous autoformalization results (<PERSON> et al., 2022) , our experiments show that the models struggle to correctly formalize most of the theorems in our dataset. We suspect this is primarily a result of using the formal system E as a specification language. E is designed primarily as a proof system, and not as a specification language; as noted by Avigad, many basic relation constructs in E (e.g., between, sameSide) are almost never mentioned explicitly in Euclid's actual writing (<PERSON><PERSON><PERSON><PERSON> et al., 2009) . Furthermore, the language can only refer to composite structures (angles, triangles, etc.) in terms of their atomic components (points, lines, etc.) . This makes theorem statements in E relatively verbose, and this verbosity introduces more room for the model to make small mistakes.", "cite_spans": [{"start": 91, "end": 108, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 540, "end": 561, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF2"}, {"start": 691, "end": 712, "text": "(points, lines, etc.)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Theorem Statements", "sec_num": "5.1."}, {"text": "In summary, while state-of-the-art models struggle to successfully autoformalize many of the theorems in our dataset, we see that E3 can successfully identify and quantify the correctness of autoformalized theorem statements; in particular, despite being incomplete by design, E3 only produces a small number of false negatives. We believe that E3 can significantly facilitate the training and validation of autoformalization models targeting Euclidean geometry. Moreover, we believe that similar tools can be developed for other domains if an appropriate formal theory can be defined.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Theorem Statements", "sec_num": "5.1."}, {"text": "To check whether LeanEuclid is a suitable target for autoformalizing proofs, we attempted to autoformalize proofs from Elements and UniGeo using GPT-4 and GPT-4V.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Proofs", "sec_num": "5.2."}, {"text": "Experimental Setup. We tested each model against 43 proofs from Elements and 100 proofs from UniGeo. To demonstrate concretely the capabilities and limitations of the model in writing formal LeanEuclid proofs, we attempted to formalize entire proofs from single queries, rather than using an iterative or search-based autoformalization procedure. We evaluated the formalized UniGeo proofs based on whether it is verified by <PERSON>n as-is, and experimented with 0-shot, 1-shot, and 5-shot prompts. The proofs from Elements are more complex, so we did not anticipate many proofs to be completely correct. Instead, we measured how much effort is required to repair the autoformalized proofs into proofs that are accepted by Lean.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Proofs", "sec_num": "5.2."}, {"text": "We manually repaired each incorrectly autoformalized proof from Elements, attempting to make as few alterations as required. Using our ground truth proof as a reference point, we modified invalid tactics that could be repaired (e.g., by rearranging the order of its arguments), added missing tactics, and removed tactics that could not easily be repaired. Unnecessary but valid tactics were left unchanged. Experiments were conducted in January 2024 using gpt-4-1106-preview and gpt-4-1106-vision-preview.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Proofs", "sec_num": "5.2."}, {"text": "Results. Table 2 shows the results of autoformalizing proofs from UniGeo proofs. Models with 0-shot prompts are not included since they failed to autoformalize any proofs. In general, the performance of the models significantly depends on the type of geometry problems and the number of few-shot demonstrations. Compared to theorem statements, we see a more significant improvement in the success rate when visual inputs are provided for autoformalizing proofs.", "cite_spans": [], "ref_spans": [{"start": 15, "end": 16, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Autoformalizing Proofs", "sec_num": "5.2."}, {"text": "When autoformalizing proofs from Elements with 5-shot prompts, we found that GPT-4 and GPT-4V were both only able to completely formalize the same two proofs (Propositions 1 and Proposition 17). That is to say, when combined with the UniGeo results, GPT-4 formalized correct proofs at a rate of 18.8% on LeanEuclid, while GPT-4V achieved a rate of 23.1%. The remaining 41 proofs from Elements required some degree of repair to be accepted by Lean.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Proofs", "sec_num": "5.2."}, {"text": "While it is difficult to precisely measure the quality of imperfect proofs, we can gain a rough approximation by computing the Levenshtein ratio between the original and repaired proofs. Doing so reveals that, for GPT-4, the autoformalized proofs had a median similarity ratio of 61.7% compared to their repaired versions, with proofs in the 75th percentile scoring at least 75.2%. For GPT-4V, the median similarity ratio was 64.0%, and the proofs in the 75h percentile proofs scored at least 72.9%. Moreover, we found that many of the modifications required to repair the proofs are very simple, such as strengthening a theorem slightly or rearranging tactics arguments; in general, the models are good at choosing relevant theorems, even if they do not invoke them correctly. Additional data and examples are in Appendix F.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Proofs", "sec_num": "5.2."}, {"text": "We believe that these results reflect well on LeanEuclid as a target language for autoformalizing Euclidean proofs. In particular, our tactic language and proof automation allow the model to focus only on explicit reasoning steps in the input text. This means the resulting proofs are much shorter than they would be if all reasoning steps were made explicit (and, as a result, they are easier to repair). Given that these results were obtained from standalone queries, we expect that neural theorem provers or proof repair tools targeting LeanEuclid could be used to successfully formalize a significant portion of the proofs in our dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Autoformalizing Proofs", "sec_num": "5.2."}, {"text": "We have demonstrated Euclidean geometry as an attractive target for autoformalization. With our SMT-based symbolic engine, it is feasible to (1) automatically evaluate autoformalized theorems statements and (2) have the model autoformalize only explicit proof steps, leaving diagrammatic reasoning implicit. We have constructed the LeanEuclid benchmark to facilitate future research on autoformalizing Euclidean geometry.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "LeanEuclid's size (173 examples) is reasonable as a benchmark for testing, e.g., considering HumanEval (<PERSON> et al., 2021b) , the most widely used benchmark for code generation, which has only 164 examples. Nevertheless, more data examples are necessary for finetuning-based approaches. A promising way of collecting examples inexpensively is to automatically translate existing geometry datasets, e.g., the synthetic dataset in AlphaGeometry, into our formalism.", "cite_spans": [{"start": 103, "end": 123, "text": "(<PERSON> et al., 2021b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Limitations and Open Problems", "sec_num": "7."}, {"text": "Even though we use GPT-4 in experiments, data contamination is unlikely since our test data, LeanEuclid, was not publicly available during the experiments. GPT-4 may have seen other formalizations of Elements on the Internet, but they are fundamentally different from LeanEuclid (Sec. 2).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations and Open Problems", "sec_num": "7."}, {"text": "We focus on Euclidean geometry, but findings related to LeanEuclid may be helpful to autoformalization in general. First, LeanEuclid provides a benchmark for developing domain-agnostic ideas for autoformalization, e.g., visual modality, retrieval, and learning from feedback. Second, we choose Euclidean geometry because its domain knowledge enables automated reasoning that can (1) check the equivalence between theorems and (2) fill in implicit reasoning gaps. Other domains with similar characteristics may also benefit from our methodology for constructing LeanEuclid.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations and Open Problems", "sec_num": "7."}, {"text": "Our work focuses on constructing a benchmark for autoformalization rather than introducing a novel model. Nevertheless, the LeanEuclid benchmark paves the way for future modeling efforts, such as having LLMs incorporate feedback from Lean to improve autoformalization.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations and Open Problems", "sec_num": "7."}, {"text": "This work aims to advance autoformalization by studying it in the controlled domain of Euclidean geometry. Autoformalization can be applied to formal mathematics, verification, and autonomous systems (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) . There are many potential societal consequences of these downstream applications, none of which we feel must be specifically highlighted here.", "cite_spans": [{"start": 200, "end": 219, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF17"}, {"start": 220, "end": 240, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 241, "end": 258, "text": "<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Findings of the Association for Computational Linguistics: ACL, 2021a. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, ", "cite_spans": [{"start": 98, "end": 124, "text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "We describe our variant of the formal system E in Lean-like pseudocode, including the complete list of axioms. Our design mostly follows E's original specification (<PERSON><PERSON><PERSON><PERSON> et al., 2009) with a few minor deviations.", "cite_spans": [{"start": 164, "end": 185, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "A. The Formal System E", "sec_num": null}, {"text": "E has six types of geometric objects: points, lines, circles, segments, angles, and triangles. 6 Points, lines, and circles are basic types, whereas segments, angles, and triangles are parameterized by points. Segments, angles, and triangles are associated with metric properties such as length, degree, and size.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1. Geometry Objects", "sec_num": null}, {"text": "length : Segment → R degree : <PERSON>le → R size : Triangle → R", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1. Geometry Objects", "sec_num": null}, {"text": "Right angle ( ) is a special constant, denoting 90 degrees.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1. Geometry Objects", "sec_num": null}, {"text": ": R", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1. Geometry Objects", "sec_num": null}, {"text": "We have nine basic predicates denoting the geometric relations between points, lines, and circles. For example, \"sameSide a b L\" means points a and b are on the same side of the line L. Also, \"between a b c\" means points a, b, and c are collinear, and b lies between a and c.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1. Geometry Objects", "sec_num": null}, {"text": "onLine", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1. Geometry Objects", "sec_num": null}, {"text": ": Point → Line → Prop sameSide : Point → Point → Line → Prop between : Point → Point → Point → Prop onCircle : Point → Circle → Prop insideCircle : Point → Circle → Prop isCentre : Point → Circle → Prop intersectsLine : Line → Line → Prop Line.intersectsCircle : Line → Circle → Prop Circle.intersectsCircle : Circle → Circle → Prop", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1. Geometry Objects", "sec_num": null}, {"text": "In addition to the basic predicates, we have four predicates defined as syntactic sugars. For example, \"opposingSides a b L\" is the conjunction of \"¬(onLine a L)\", \"¬(onLine b L)\", and \"¬(sameSide a b L)\". ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1. Geometry Objects", "sec_num": null}, {"text": "Axioms in E fall into five categories: construction rules, diagrammatic rules, metric rules, transfer rules, and superposition rules (the last four categories are collectively referred to as \"non-construction rules\" in this paper). <PERSON><PERSON><PERSON><PERSON> et al. (2009) has proved E's axioms to be sound and complete through a bi-directional translation between <PERSON> and <PERSON><PERSON><PERSON>'s axioms (<PERSON><PERSON><PERSON><PERSON> et al., 2013 ). E's axioms may have redundancy, as <PERSON><PERSON><PERSON><PERSON> et al. (2009) did not attempt to minimize the number of axioms.", "cite_spans": [{"start": 232, "end": 252, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2009)", "ref_id": "BIBREF2"}, {"start": 367, "end": 393, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2013", "ref_id": null}, {"start": 432, "end": 452, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2009)", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "A.3. Axioms", "sec_num": null}, {"text": "Construction Rules. We have 32 construction rules. Among them, two rules are not from <PERSON>'s paper but convenient for formalizing some proofs: point between points shorter than, extend point longer. One rule is stronger than its original version: point on line same side. Metric Rules. We have 11 metric rules. Unlike diagrammatic rules, metric rules deduce metric facts, e.g., about lengths, angles, and areas. In addition, since we use SMT solvers as the symbolic reasoning engine, their built-in theories on real numbers are also included implicitly as metric rules. Thus, if two triangles have two sides equal to two sides, respectively, but (one) has the angle encompassed by the equal straight-lines greater than the (corresponding) angle (in the other), then (the former triangle) will also have a base greater than the base (of the latter). (Which is) the very thing it was required to show.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3. Axioms", "sec_num": null}, {"text": ") ∧ (onLine c M) ∧ (a ̸ = b) ∧ (a ̸ = c) ∧ ¬(onLine d L) ∧ ¬(onLine d M) ∧ (L ̸ = M) ∧ ( ∠ b:a:c) = (∠ b:a:d) + (∠ d:a:c) → (sameSide b d M) ∧ (sameSide c d L) sum_angles_onlyif : ∀ (a b c d : Point) (L M : Line), (onLine a L) ∧ (onLine a M) ∧ ( onLine b L) ∧ (onLine c M) ∧ (a ̸ = b) ∧ (a ̸ = c) ∧ ¬(onLine d L) ∧ ¬(onLine d M) ∧ (L ̸ = M) ∧ (", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3. Axioms", "sec_num": null}, {"text": "<PERSON><PERSON><PERSON> did not cover the case where D and G are on different sides of EF (Fig. A ). For this case, we come up with the proof below, which is significantly more complicated than <PERSON><PERSON><PERSON>'s proof. Therefore, we argue that <PERSON><PERSON><PERSON>'s proof of Proposition 24 has a logical gap. Note that", "cite_spans": [], "ref_spans": [{"start": 79, "end": 80, "text": "A", "ref_id": "FIGREF11"}], "eq_spans": [], "section": "A.3. Axioms", "sec_num": null}, {"text": "∠ DGE = ∠ DGF + ∠ EGF = α + y. ∠ DF E = 2π -∠ DF G -∠ EF G = 2π -α -x.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3. Axioms", "sec_num": null}, {"text": "Furthermore, Proposition 17 states that the sum of any two angles in a triangle must be smaller than π. Therefore, any angle in a triangle must also be smaller than π, i.e.,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3. Axioms", "sec_num": null}, {"text": "α + y < π 2π -α -x < π", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3. Axioms", "sec_num": null}, {"text": "Simplifying these two inequalities leads to x > y. QED. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3. Axioms", "sec_num": null}, {"text": "In this section, we compare our formalizations of Elements with existing formalizations, with the goal of demonstrating how our proof automation enables formal proofs that are closer in nature to the original natural language proofs. However, it is important to note that we are making a tradeoff between proof automation and end-to-end certification of proofs. As mentioned in Sec. 2, the results of SMT solvers are not certified or reconstructed as Lean proofs; instead, we introduce an unsound axiom that allows us to admit successful SMT queries to prove any proposition. In practical terms, this means that our proofs are certified up to (i) the soundness of the proof assistant kernel, (ii) any errors in our translation from Lean proof contexts to SMT-LIB queries, and (iii) the soundness of the SMT solvers themselves. By contrast, the other formalizations discussed here only require trusting the proof assistant kernel.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> to Existing Formalization of Elements", "sec_num": null}, {"text": "We use Proposition 1 as an example to compare LeanEuclid with existing formalizations of <PERSON><PERSON><PERSON>'s Elements (Hernandez<PERSON><PERSON>, 2023; <PERSON><PERSON> et al., 2019) . Below is <PERSON><PERSON><PERSON>'s proof of Proposition 1 from <PERSON><PERSON><PERSON> (2007) :", "cite_spans": [{"start": 132, "end": 152, "text": "<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF6"}, {"start": 201, "end": 215, "text": "<PERSON><PERSON><PERSON> (2007)", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> to Existing Formalization of Elements", "sec_num": null}, {"text": "B A E D C", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> to Existing Formalization of Elements", "sec_num": null}, {"text": "To construct an equilateral triangle on a given finite straight-line.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> to Existing Formalization of Elements", "sec_num": null}, {"text": "Let AB be the given finite straight-line. Thus, the triangle ABC is equilateral, and has been constructed on the given finite straight-line AB. (Which is) the very thing it was required to do.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> to Existing Formalization of Elements", "sec_num": null}, {"text": "LeanEuclid. Below is our formalization of Proposition 1. Our proof is concise and follows <PERSON><PERSON><PERSON>'s proof closely. It is straightforward to map the tactics in our proofs to sentences in <PERSON><PERSON><PERSON>'s proof. <PERSON><PERSON><PERSON><PERSON><PERSON> (2023). Below is <PERSON><PERSON><PERSON><PERSON><PERSON> (2023)'s formalization. In this formalization, all diagrammatic inferences must be handled explicitly. For instance, in the proof of Proposition 1 (iseqtri sameside of ne), the proof that circles BCD and ACE intersect \"(circlesInter of inside on circle bα aβ (inside circle of center aα) (inside circle of center bβ))\" must be supplied directly by the proof author, even though this assertion is left implicit in the text. In contrast, we use the symbolic engine to prove it automatically and implicitly. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> to Existing Formalization of Elements", "sec_num": null}, {"text": "Here, we share the prompt templates used in our experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D. Autoformalization Prompts", "sec_num": null}, {"text": "To autoformalize theorem statements from Elements and UniGeo, we used the prompt shown below. We also prepend the instructions with a header describing the task (i.e., \"Your task is to take an English statement of a theorem from Euclidean Geometry and formalize it using Lean 4 programming language, adhering to the following structures and guidelines\".", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1. Theorem Statement Formalization", "sec_num": null}, {"text": "One additional intricacy in formalizing theorem statements from Elements is that, unlike in modern mathematics, <PERSON><PERSON><PERSON> does not always state a complete logical theorem statement before beginning a proof. He often begins with an imperative assertion (e.g., Proposition 9: \"To cut a given rectilinear angle in half\") whose proper logical interpretation is clarified at the conclusion of the proof (e.g., \"Thus, angle DAF is equal to angle EAF. Thus, the given rectilinear angle BAC has been cut in half by the straight-line AF\"). In other words, to determine the proper logical statement of the theorem, both the beginning and the end of the proof must sometimes be considered. During preliminary experiments, we attempted to autoformalize theorem statements by giving the model the entire natural language proof, but we found that the resulting theorem statements were often very poor. Instead, we manually removed the majority of the \"proof\" portion of the natural language text, leaving only the portions required to obtain a reasonable formalization of the theorem statement. This context was also included as part of the prompt header when formalizing theorems from Elements. These abbreviated versions of Elements proofs are also available as part of LeanEuclid. P_n → ∃ (...) Q_1 ∧ Q_2 ... ∧ Q_m >>> where where each P_i and Q_i is built from the above building blocks using conjunction (∧) disjunction (∨) and negation (¬). Note that there may be zero existentially quantified variables. 2. Implication: There can be only a single implication in the formula; either side of the implication must be a conjunction of formulae. 3. Numeric Values Restrictions: Denote 90-degree angle by , 180-degree angle by + , etc. Also, when referring to segments, we always mean its length (i.e. |(a--b)|). 4. Quantified Variables: Your quantified variables must be limited to primitive geometric types: points, lines, and circles. ALL bound variables must be mentioned at some point . 5. Intermediate Variables: You should never define an intermediate variable inside the proposition. For example, \"let X := (something);\" is NOT allowed. 6. Axioms: You should only use the provided axioms. For example, Line L is parallel to line M can be expressed as ¬(L.intersectsLine M). Do not use Line.Parallel L M. 7. Response Format: Present your formalized Lean expression within triple angle brackets (<<< Lean expression here >>>). Do not add any annotations/explanations, or markdown syntax.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1. Theorem Statement Formalization", "sec_num": null}, {"text": "To formalize proofs from Elements, we included (1) all inference rules that were used in our manual formalization of Elements and (2) all variations of all theorem statements that precede the target theorem. We also provided 5 hardcoded examples (Propositions 2, 6, 12, 32, 42) which were chosen to showcase a variety of proof formats and tactic invocations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2. Proof Formalization (Elements)", "sec_num": null}, {"text": "The token {PRECEDING THMS} is substituted at formalization time by the list of visible theorems allowed for the target proposition. For instance, when formalizing a proof of Proposition n in Elements, the model is allowed to use proofs of Propositions 1 through n -1, and all their variations. Here are some examples. NOTE: You may not necessarily use the propositoins shown in these proofs, unless they are also listed above.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2. Proof Formalization (Elements)", "sec_num": null}, {"text": "To place a straight-line equal to a given straight-line at a given point (as an extremity). Let $A$ be the given point, and $BC$ the given straight-line. To draw a straight-line perpendicular to a given infinite straight-line from a given point which is not on it.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input: Informal Proof:", "sec_num": null}, {"text": "Let $AB$ be the given infinite straight-line and $C$ the given point, which is not on ($AB$). So it is required to draw a straight-line perpendicular to the given infinite straight-line $AB$ from the given point $C$, which is not on ($AB$). For let point $D$ have been taken at random on the other side (to $C$) of the straight-line $AB$, and let the circle $EFG$ have been drawn with center $C$ and radius $CD$ [Post.˜3], and let the straight-line $EG$ have been cut in half at (point) $H$ [Prop.˜1.10], and let the straight-lines $CG$, $CH$, and $CE$ have been joined. I say that the (straight-line) $CH$ has been drawn perpendicular to the given infinite straight-line $AB$ from the given point $C$, which is not on ($AB$). For since $GH$ is equal to $HE$, and $HC$ (is) common, the two (straight-lines) $GH$, $HC$ are equal to the two (straight-lines) $EH$, $HC$, respectively, and the base $CG$ is equal to the base $CE$. Thus, the angle $CHG$ is equal to the angle $EHC$ [Prop.˜1.8], and they are adjacent. But when a straightline stood on a(nother) straight-line makes the adjacent angles equal to one another , each of the equal angles is a right-angle, and the former straight-line is called a perpendicular to that upon which it stands [Def.˜1.10]. Thus, the (straight-line)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input: Informal Proof:", "sec_num": null}, {"text": "$CH$ has been drawn perpendicular to the given infinite straight-line $AB$ from the given point $C$, which is not on ($AB$). (Which is) the very thing it was required to do. In any triangle, (if) one of the sides (is) produced (then) the external angle is equal to the (sum of the) two internal and opposite (angles), and the (sum of the) three internal angles of the triangle is equal to two right-angles. Let $ABC$ be a triangle , and let one of its sides $BC$ have been produced to $D$. I say that the external angle $ACD$ is equal to the (sum of the) two internal and opposite angles $CAB$ and $ABC$, and the (sum of the) three internal angles of the triangle---$ABC$, $BCA$, and $CAB$---is equal to two right-angles. For let $CE$ have been drawn through point $C$ parallel to the straight-line $AB$ [Prop.˜1.31]. And since $AB$ is parallel to $CE$, and $AC$ has fallen across them, the alternate angles $BAC$ and $ACE$ are equal to one another [Prop.˜1.29]. Again, since $AB$ is parallel to $CE$, and the straight -line $BD$ has fallen across them, the external angle $ECD$ is equal to the internal and opposite (angle) $ABC$ [Prop.˜1.29]. But $ACE$ was also shown (to be) equal to $BAC$. Thus, the whole angle $ACD$ is equal to the (sum of the) two internal and opposite (angles) $BAC$ and $ABC$. Let $ACB$ have been added to both. Thus, (the sum of) $ACD$ and $ACB$ is equal to the (sum of the) three (angles) $ABC$, $BCA$, and $CAB$. But, (the sum of) $ACD$ and $ACB$ is equal to two right-angles [Prop.˜1.13]. Thus, (the sum of) $ACB$, $CBA$, and $CAB$ is also equal to two right-angles. Thus, in any triangle, (if) one of the sides (is) produced (then) the external angle is equal to the (sum of the) two internal and opposite (angles), and the (sum of the) three internal angles of the triangle is equal to two right-angles. (Which is) the If a triangle has two angles equal to one another then the sides subtending the equal angles will also be equal to one another. Let $ABC$ be a triangle having the angle $ABC$ equal to the angle $ACB$. I say that side $AB$ is also equal to side $AC$.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input: Informal Proof:", "sec_num": null}, {"text": "For if $AB$ is unequal to $AC$ then one of them is greater. Let $AB$ be greater. And let $DB$, equal to the lesser $AC$, have been cut off from the greater $AB$ [ Prop.˜1.3]. And let $DC$ have been joined [Post.˜1]. Therefore, since $DB$ is equal to $AC$, and $BC$ (is) common, the two sides $DB$, $BC$ are equal to the two sides $AC$, $CB$, respectively, and the angle $DBC$ is equal to the angle $ACB$. Thus, the base $DC$ is equal to the base $AB$, and the triangle $DBC$ will be equal to the triangle $ACB$ [Prop.˜1.4], the lesser to the greater. The very notion (is) absurd [C.N.˜5]. Thus, $AB$ is not unequal to $AC$. Thus, (it is) equal. Thus, if a triangle has two angles equal to one another then the sides subtending the equal angles will also be equal to one another. (Which is) the very thing it was required to show. Proof Setup: ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input: Informal Proof:", "sec_num": null}, {"text": "To formalize UniGeo proofs, we likewise include those inference rules which were used in our manual formalization, as well as the relevant theorems from Elements . Unlike for proofs from Elements, we randomly select shots from a separate test set of UniGeo proofs. The guidelines are slightly different from Elements, to accommodate the different style of input for UniGeo proofs. 3. euclid_assert <P> It attempts to prove proposition <P> from the current proof context and the above axioms.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3. Proof Formalization (UniGeo)", "sec_num": null}, {"text": "E F) => |(A--B)| = |(D--E)| ∧ |(B--C)| = |(E--F)| ∧ |(A--C)| = |(D--F)| ∧ ∠ A:B:C = ∠ D:E:F ∧ ∠ A:C:B = ∠ D:F:E ∧ ∠ B:A:C = ∠ E:D:F --If", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3. Proof Formalization (UniGeo)", "sec_num": null}, {"text": "It attempts to resolve the proof goal using the current proof context and the above axioms .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "euclid_finish", "sec_num": "4."}, {"text": "This tactic proves an existentially quantified proposition by providing the witness <X> for the quantifier.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "6. have <name> : <claim> := by <proof> This tactic proves a lemma <claim>. The <name> is optional, and you need to use euclid_finish at the end of <proof>. If <claim> is already in premise, you can use directly use.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "In case the <proof> only contains euclid_finish, you can directly use euclid_assert <claim > for simplicity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "---Guidelines --- As mentioned in the paper, some of the successful predictions proven correct by E3 were actually syntactic matches with their ground truth formalizations; these cases are not interesting, since existing syntactic evaluation techniques can identify these are correct. What is interesting are cases where the prediction is not a syntactic match with its ground truth. Even a small deviation (e.g., writing ∠ABC rather than ∠BAC) may or may not affect the semantics of the result, depending on the context. It is these cases for which E3 offers an improvement over syntactic or manual evaluation methods. We highlight some such cases here.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "Proposition 8, GPT-4V, 5-shot. Consider Proposition 8 of Elements, whose abridged version used for autoformalization of theorem statements reads as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "If two triangles have two sides equal to two sides, respectively, and also have the base equal to the base, then they will also have equal the angles encompassed by the equal straight-lines. Let ABC and DEF be two triangles having the two sides AB and AC equal to the two sides DE and DF , respectively. (That is) AB to DE, and AC to DF . Let them also have the base BC equal to the base EF . I say that the angle BAC is also equal to the angle EDF ... So the angle BAC will also coincide with angle EDF , and will be equal to it [C.N. 4]. Thus, if two triangles have two sides equal to two side, respectively, and have the base equal to the base, then they will also have equal the angles encompassed by the equal straight-lines", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "The formalization generated by GPT-4 from this text is as follows, alongside our ground truth formalization: Syntactically, there are four differences between the two propositions: in each of the formTriangle clauses, we have swapped two sets of lines BC → AC and EF → DF , and in the postcondition, we have also swapped points b → a and e → d. This is a good example of a set of simple perturbations whose validity may not be immediately obvious to a human grader. However, <PERSON><PERSON> is easily able to prove that these propositions are indeed equivalent.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "Proposition 10, GPT-4, 1-shot. Consider Proposition 10 of Elements:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "\"To cut a given finite straight-line in half. Let AB be the given finite straight-line. So it is required to cut the finite straight-line AB in half... Thus, the base AD is equal to the base BD [Prop. 1.4 ]. Thus, the given finite straight-line AB has been cut in half at (point) D.\"", "cite_spans": [{"start": 191, "end": 204, "text": "BD [Prop. 1.4", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "When formalizing this statement, one has to specify that point D lies halfway between A and B. Our ground truth formalization formalizes this by asserting that D lies between A and B, and the length |AD| is equal to the length |BD|. E3 is able to fill the gap between the two formulations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "Proposition 20, GPT-4, 1-shot. Consider Proposition 20 of Elements:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "In any triangle, (the sum of) two sides taken together in any (possible way) is greater than the remaining (side). For let ABC be a triangle. I say that in triangle ABC (the sum of) two sides taken together in any (possible way) is greater than the remaining (side)... (So), (the sum of) BA and AC (is greater) than BC, (the sum of) AB and BC than AC, and (the sum of) BC and CA than AB. Thus, (the sum of) BA and AC is greater than BC. Similarly, we can show that (the sum of) AB and BC is also greater than CA, and (the sum of) BC and CA than AB. Thus, in any triangle, (the sum of) two sides taken together in any (possible way) is greater than the remaining (side)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "In the text, <PERSON><PERSON><PERSON> explicitly mentions each combination of distinct line segments explicitly. However, when formalizing the theorem statement, it is not necessary to do so. All combinations are covered by the following concise formulation: Using only a syntactic similarity metric, one might judge the prediction poorly since it nearly doubles the character length of the ground truth formalization. However, E3 correctly identifies them as semantically equivalent.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "use <X>", "sec_num": "5."}, {"text": "As mentioned in the paper, E3 is occasionally unable to verify reasonable predictions. Based on our sample size, we estimate the false negative rate to be approximately 15%. Here, we share the false negatives we found while manually evaluating the results from GPT-4 and GPT-4V (k = 5) in Experiment #1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. False Negatives", "sec_num": null}, {"text": "Proposition 1, Both Models, 5-shot. Two of our false negatives were the same formalization of Proposition 1, whose text reads:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. False Negatives", "sec_num": null}, {"text": "\"To construct an equilateral triangle on a given finite straight-line. Let AB be the given finite straight-line... Thus, the three (straight-lines) CA, AB, and BC are equal to one another. Thus, the triangle ABC is equilateral, and has been constructed on the given finite straight-line AB\".", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. False Negatives", "sec_num": null}, {"text": "As shown in Fig. 3 , the approximate equivalence checker attempts to quantify how \"semantically close\" one theroem statement is to another. The intention is to allow us to distinguish \"close\" formalizations from \"junk\" ones. We provide a more detailed description of how this procedure works.", "cite_spans": [], "ref_spans": [{"start": 17, "end": 18, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "E.5. The Approximate Logical Equivalence Procedure", "sec_num": null}, {"text": "We assume we are given a ground truth formula T gt and a prediction T gt such that the two formulas agree on the quantity and types of their bound variables. The goal is to find a unification ρ between bound variables in T gt and T gt with which we can prove the equivalence of the preconditions and postconditions of the two formulae. Obviously, the number of potential unifications is factorial in the number of variables, so we fix some upper bound n on the number of unifications we consider, and rank candidates using a string similarity heuristic.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.5. The Approximate Logical Equivalence Procedure", "sec_num": null}, {"text": "Having fixed a unification ρ of variables in T pred with variables in T gt , let T ′ pred be the result of renaming the variables in T pred with variables in T gt according to ρ. Assuming T ′ pred and T gt are both well-formed statements in E, we can extract the sets {p i } and {q i } containing the pre/post-conditions of T gt , respectively, and likewise the set {r i } and {s i } containing the pre/postconditions of T ′ pred . We then perform a four-step process: (1) Assuming i p i try to prove each r j (2) Assuming i r i , try to prove each p j (3) Assuming i q i , try to prove each s j (4) Assuming i s i , try to prove each q j . If steps (1) and ( 2) are both successful (meaning that the preconditions of the formulae were proven equivalent), then the preconditions are included as additional assumptions for steps (3) and (4). For each step, E3 records how many obligations were able to be proved. The results are then aggregated for each of the n unifications of bound variables, which can then be evaluated by any number of metrics. For instance, Fig. 3 compares the formalization instances using the ratio of clauses solved across steps (1)-(4).", "cite_spans": [], "ref_spans": [{"start": 1068, "end": 1069, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "E.5. The Approximate Logical Equivalence Procedure", "sec_num": null}, {"text": "As mentioned in the paper, only two proofs from Elements were able to be formalized correctly out-of-the-box. For the remaining 43 proofs, we wished to quantify how \"good\" the proofs are by computing the degree of modification introduced during the repair process, as measured by the Levenshtein ratio. The results of this process for the (incomplete) proofs autoformalized by GPT-4 and GPT-4V are shown in Fig. C and Fig. D , respectively. Obviously, this is not a perfect metricfor instance, a tactic invoking proposition 6 may only differ in a single character with one invoking proposition 7. However, we find that these kinds of major conceptual errors, where the model chooses an entirely irrelevant theorem or rule, are quite rare. The majority of modifications necessary to repair proofs (as shown by the exemplars below) are simply rearranging arguments to tactics or modifying one variant of a theorem for another (e.g., proposition 9 versus the variant proposition 9 ′ ).", "cite_spans": [], "ref_spans": [{"start": 412, "end": 413, "text": "C", "ref_id": "FIGREF28"}, {"start": 423, "end": 424, "text": "D", "ref_id": null}], "eq_spans": [], "section": "F.1. Quantitative Results", "sec_num": null}, {"text": "The same two proofs -Proposition 1 and Proposition 17 -were correctly formalized out-of-the-box by GPT-4 and GPT4-V. Both of these are relatively simple proofs. Nevertheless, they serve as good exemplars of how LeanEuclid's proof language supports simple, elegant and faithful formalizations of the natural language proof. Proposition 17, GPT-4V. Natural language proof: \"For any triangle, (the sum of) two angles taken together in any (possible way) is less than two right-angles. Let ABC be a triangle. I say that (the sum of) two angles of triangle ABC taken together in any (possible way) is less than two right-angles. For let BC have been produced to D. And since the angle ACD is external to triangle ABC, it is greater than the internal and opposite angle ABC [Prop. 1.16].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.2. Correctly Formalized Proofs from Elements", "sec_num": null}, {"text": "Let ACB have been added to both. Thus, the (sum of the angles) ACD and ACB is greater than the (sum of the angles) ABC and BCA. But, (the sum of) ACD and ACB is equal to two right-angles [Prop. 1.13]. Thus, (the sum of) ABC and BCA is less than two right-angles. Similarly, we can show that (the sum of) BAC and ACB is also less than two right-angles, and further (that the sum of) CAB and ABC (is less than two right-angles). Thus, for any triangle, (the sum of) two angles taken together in any (possible way) is less than two right-angles. (Which is) the very thing it was required to show.\"", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.2. Correctly Formalized Proofs from Elements", "sec_num": null}, {"text": "Autoformalized proof: ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.2. Correctly Formalized Proofs from Elements", "sec_num": null}, {"text": "Here, we showcase some attempts at autoformalizing proofs from Elements which, despite being incorrect, require only minor modifications to be repaired. These exemplars demonstrate that, in many cases, the steps required to repair the proof do not require creative insight. Moreover, we believe this process is quite amenable to automation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "Proposition 3, GPT-4. Natural language proof: \"For two given unequal straight-lines, to cut off from the greater a straight-line equal to the lesser. Let AB and C be the two given unequal straight-lines, of which let the greater be AB. To prove the precondition for between if, we need the fact between a e b, which is not provable from the preceding tactics. To repair this proof, we can use a stronger version of intersection circle line on Line 9.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "Repaired Proof: But F C was also shown (to be) equal to GB. So the two (straight-lines) BF , F C are equal to the two (straight-lines) CG, GB, respectively, and the angle BF C (is) equal to the angle CGB, and the base BC is common to them. Thus, the triangle BF C will be equal to the triangle CGB, and the remaining angles subtended by the equal sides will be equal to the corresponding remaining angles [Prop. 1.4 ]. Thus, F BC is equal to GCB, and BCF to CBG. Therefore, since the whole angle ABG was shown (to be) equal to the whole angle ACF , within which CBG is equal to BCF , the remainder ABC is thus equal to the remainder ACB [C.N. 3]. And they are at the base of triangle ABC. And F BC was also shown (to be) equal to GCB. And they are under the base. Thus, for isosceles triangles, the angles at the base are equal to one another, and if the equal sides are produced then the angles under the base will be equal to one another. (Which is) the very thing it was required to show.\"", "cite_spans": [{"start": 405, "end": 415, "text": "[Prop. 1.4", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "def prop3_repaired : ∀ (a b c0 c1 : Point) (AB C : Line), distinctPointsOnLine a b AB ∧ distinctPointsOnLine c0 c0 C ∧ a ̸ = c0 ∧ |(a--b)| > |(c0--c1)| → ∃ e,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "Autoformalized Proof: In order to prove the preconditions for proposition 3, we need to know |ba| < |ce|, which can be obtained via a stronger version of exists point between points on line on Line 2. We also need to rearrange some of the arguments on in the invocations of proposition 3 and proposition 4. Finally, we can remove the invocation of tactic use entirely, since it is inapplicable in this proof context.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "Repaired Proof: The rule proposition 5 takes five points and three lines as argument, whereas the variant proposition 5 ′ takes three of each. It is easiest to fix the fourth and fifth tactics by instead invoking the latter theorem (and tweaking the arguments). The euclid apply within the case analysis also need repairing, since they are using too many arguments. These tactics are actually redundant, since euclid finish can complete the proof. Nevertheless, simply ommitting the last point and line from each tactic is sufficient to make the proof go through.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "Repaired Proof: Proposition 18, GPT-4V. Natural language proof: \"In any triangle, the greater side subtends the greater angle. For let ABC be a triangle having side AC greater than AB. I say that angle ABC is also greater than BCA. For since AC is greater than AB, let AD be made equal to AB [Prop. 1.3],", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "and let BD have been joined. And since angle ADB is external to triangle BCD, it is greater than the internal and opposite (angle) DCB [Prop. 1.16] . But ADB (is) equal to ABD, since side AB is also equal to side AD [Prop. 1.5]. Thus, ABD is also greater than ACB. Thus, ABC is much greater than ACB. Thus, in any triangle, the greater side subtends the greater angle. (Which is) the very thing it was required to show.\"", "cite_spans": [{"start": 131, "end": 147, "text": "DCB [Prop. 1.16]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "Autoformalized Proof: The second tactic does not provide the right number of arguments to proposition 3, but it does for proposition 3 ′ . The preconditions of the invocation of proposition 16 are not provable from the preceding steps, but we can permute points and a, b, c to recover a valid tactic. The invocation of proposition 5 has the same problem as proposition 3 , and is replaced with proposition 5 ′ . Also, the keyword \"as\" can only be used for constructive theorems, which proposition 5 is not. We can simply drop \"as H1\" and the proof goes through.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "Repaired Proof: So ABC and BCD are two triangles having the two angles ABC and BCA equal to the two (angles) BCD and CBD, respectively, and one side equal to one side-the (one) by the equal angles and common to them, (namely) BC. Thus, they will also have the remaining sides equal to the corresponding remaining (sides), and the remaining angle (equal) to the remaining angle [Prop. 1.26 ]. Thus, side AB is equal to CD, and AC to BD. Furthermore, angle BAC is equal to CDB. And since angle ABC is equal to BCD, and CBD to ACB, the whole (angle) ABD is thus equal to the whole (angle) ACD. And BAC was also shown (to be) equal to CDB. Thus, in parallelogrammic figures the opposite sides and angles are equal to one another. And, I also say that a diagonal cuts them in half. For since AB is equal to CD, and BC (is) common, the two (straight-lines) AB, BC are equal to the two (straight-lines) DC, CB, respectively. And angle ABC is equal to angle BCD. Thus, the base AC (is) also equal to DB, and triangle ABC is equal to triangle BCD [Prop. 1.4] .", "cite_spans": [{"start": 377, "end": 388, "text": "[Prop. 1.26", "ref_id": null}, {"start": 1034, "end": 1049, "text": "BCD [Prop. 1.4]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "Thus, the diagonal BC cuts the parallelogram ACDB in half. (Which is) the very thing it was required to show.\"", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "Autoformalized Proof: Similar to previous cases, the first invocation of proposition 29 on is not well-formed, since proposition 29 requires more arguments than the model has supplied. However, the variant proposition 29 ′′′ accepts the number and quantity of variables provided, so we can swap the rule and permute its arguments to make the tactic go through. The next invocation is repaired in the same fashion. Too few arguments are given to proposition 265, but there are no variants of this theorem, so we must infer which arguments can be supplied from the proof context. The preconditions of the tactic using proposition 4 cannot be proved as-is. As a matter of fact, this tactic is unnecessary, since, the proof can be completed already by euclid finish. However, if we want to repair it, we can simply permute a few of its arguments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "Repaired Proof: ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F.3. <PERSON><PERSON><PERSON> Correct Autoformalized Proofs from Elements", "sec_num": null}, {"text": "We use an open-source version of Euclid's Elements at https://github.com/rfitzp/Elements.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "E3 uses not only non-construction rules but also a handful of construction rules (details in Appendix A).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "E3 currently only performs approximate equivalence checking when the predicted formula has the same number of bound variables as the ground truth formula.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Triangles are called \"areas\" in<PERSON><PERSON><PERSON><PERSON> et al. (2009).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "This work is partially supported by the Natural Sciences and Engineering Research Council of Canada (NSERC) through the Discovery Grants (individual) program. <PERSON><PERSON> is partially supported by Caltech's Computing, Data, and Society Postdoctoral Fellowship. <PERSON><PERSON><PERSON> is partially supported by the Canada CIFAR AI Chairs program. We thank <PERSON><PERSON> for his contributions to the conceptualization of this project when <PERSON><PERSON> was a Ph.D. student under his mentorship. We are also thankful to <PERSON> for his invaluable guidance on the formal system E. Special thanks go to <PERSON><PERSON> and <PERSON><PERSON><PERSON> for their insightful feedback on the initial version of this paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "assert (eq B B) by (conclude cn_equalityreflexive). assert (InCirc B K) by (conclude_def InCirc ). assert (Cong A B A B) by (conclude cn_congruencereflexive). assert (OnCirc B J) by (conclude_def OnCirc ). assert (OnCirc D J) by (conclude_def OnCirc ). assert (eq A A) by (conclude cn_equalityreflexive). assert (InCirc A J) by (conclude_def InCirc ). let Tf:=fresh in assert (Tf:exists C, (OnCirc C K /\\ OnCirc C J)) by (conclude postulate_circle_circle); destruct Tf as [C];spliter.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "{ intro. assert (˜Cong A C A B) by (conclude lemma_partnotequalwhole).Our ground truth formalization is as follows:The prediction which E<PERSON> is unable to prove equivalent is Which captures the essence of the theorem. Both theorems are actually true, and are each reasonable formalizations of the given statement, but E3 is unable to prove their equivalence. In addition to revealing the limitations of the tool, this also reflects the difficulty in building a dataset which can anticipate all possible ways of formalizing a natural language theorem.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "assert (Cong A C A B) by (conclude axiom_circle_center_radius). assert (Cong A B A C) by (conclude lemma_congruencesymmetric). assert (Cong B C B A) by (conclude axiom_circle_center_radius). assert (Cong B C A B) by (forward_using lemma_congruenceflip). assert (Cong B C A C) by (conclude lemma_congruencetransitive). assert (Cong A B B C) by (conclude lemma_congruencesymmetric). assert (Cong A C C A) by (conclude cn_equalityreverse). assert (Cong B C C A) by (conclude lemma_congruencetransitive). assert (equilateral A B C) by (conclude_def equilateral ). assert (neq B C) by (conclude axiom_nocollapse). assert (neq C A) by (conclude axiom_nocollapse). assert (˜BetS A C B).", "sec_num": null}, {"text": "As mentioned in the paper, we did not encounter indications of soundness bugs in our experiment results. However, we did encounter one instance of a proposition which was proven correct by E3, despite being an \"incorrect\" formalization of the given proposition. The instance is from the UniGeo/Congruent dataset, which amounts to proving that, with respect to the diagram in Fig., then the triangles formed by points T U V and T V W are congruent.Our ground truth formalization is as follows:One of the predictions generated by GPT-4v is given as follows:1 prediction : In this case, the formalization does not quite line up with the text of the proposition; the statement assumes a quadrilateral rather than two triangles, and the premises refer to U V and W T being parallel rather than T U and V W , despite still assuming T U = V W . Of course, the statement is still true, and in fact E3 is able to prove they are equivalent; however, this is not quite a \"faithful\" formalization of the text.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.3. <PERSON><PERSON><PERSON>", "sec_num": null}, {"text": "E3 did not identify any unsatisfiable predictions during our final rounds of experiments, but we did encounter a few such cases during preliminary experiments and testing; we include them here for the interested reader, as well as to emphasize the importance of rigorous semantic validation.Proposition 11, GPT-4, 5-shot. Consider the following: E3 correctly identifies this proposition as unsatisfiable. The cause is that, in the application of formQuadrilateral, the line BD is given as an argument twice. However, formQuadrilateral requires that all its inputs are distinct objects.Proposition 48, GPT-4, 5-shot. Consider the following prediction, which is stating the converse direction of the Pythagorean theorem: ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.4. Unsatisfiable Statements", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Towards a mathematics formalisation assistant using large language models", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.07524"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Towards a mathematics formalisation as- sistant using large language models. arXiv preprint arXiv:2211.07524, 2022.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "A formal system for <PERSON><PERSON><PERSON>'s Elements", "authors": [{"first": "J", "middle": [], "last": "Avigad", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Notes on \"A formal system for Euclid's Ele- ments\". https://www.andrew.cmu.edu/user/ avigad/Papers/euclid_notes.htm.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "A formal system for <PERSON><PERSON><PERSON>'s Elements. The Review of Symbolic Logic", "authors": [{"first": "J", "middle": [], "last": "Avigad", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. A formal system for <PERSON>uc<PERSON>'s Elements. The Review of Symbolic Logic, 2009.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "ProofNet: Autoformalizing and formally proving undergraduate-level mathematics", "authors": [{"first": "Z", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": ["W"], "last": "Ayers", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Avigad", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.12433"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, J. ProofNet: Autoformalizing and formally proving undergraduate-level mathematics. arXiv preprint arXiv:2302.12433, 2023.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "The Coq proof assistant reference manual: Version 6.1. PhD thesis", "authors": [{"first": "B", "middle": [], "last": "Barras", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J.-C", "middle": [], "last": "Filliatre", "suffix": ""}, {"first": "E", "middle": [], "last": "Gimenez", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Munoz", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1997, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, J<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. The Coq proof assistant reference manual: Version 6.1. PhD thesis, Inria, 1997.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Satisfiability modulo theories", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, C. <PERSON>tis<PERSON>bility modulo theories. 2018.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Proof-checking <PERSON><PERSON><PERSON>", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Annals of Mathematics and Artificial Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>of-checking Euclid. Annals of Mathematics and Artificial Intelligence, 2019.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Applications of <PERSON><PERSON><PERSON><PERSON> bases in non-linear computational geometry", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2005, "venue": "Trends in Computer Algebra", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>. Applications of <PERSON><PERSON><PERSON><PERSON> bases in non-linear computational geometry. In Trends in Computer Algebra, 2005.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "GeoQA: A geometric question answering benchmark towards multimodal numerical reasoning", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Qin", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Xi<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, L. GeoQA: A geometric question answering benchmark towards multimodal numerical reasoning. In", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Solving geometric problem with multi-modal large language model", "authors": [{"first": "J", "middle": [], "last": "Gao", "suffix": ""}, {"first": "R", "middle": [], "last": "Pi", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ye", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Hong", "suffix": ""}, {"first": "J", "middle": [], "last": "Han", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.11370"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. G-LLaVA: Solving geo- metric problem with multi-modal large language model. arXiv preprint arXiv:2312.11370, 2023.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "A formal proof of the <PERSON><PERSON> conjecture", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": ["D"], "last": "<PERSON>g", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Le <PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Kaliszyk", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": ["T"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Forum of Mathematics, Pi", "volume": "5", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. A formal proof of the <PERSON> conjecture. In Forum of Mathematics, Pi, volume 5, 2017.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Proof artifact co-training for theorem proving with language models", "authors": [{"first": "J", "middle": ["M"], "last": "Han", "suffix": ""}, {"first": "J", "middle": [], "last": "Rute", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Ayers", "suffix": ""}, {"first": "S", "middle": [], "last": "Pol<PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> artifact co-training for theorem proving with language models. In International Conference on Learning Repre- sentations (ICLR), 2022.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "<PERSON><PERSON><PERSON>'s Elements", "authors": [{"first": "J", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2007, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>'s Elements. 2007. URL https: //github.com/rfitzp/Elements.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "<PERSON><PERSON> feat: synthetic geometry", "authors": [{"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> feat: synthetic geometry. https: //github.com/leanprover-community/ mathlib4/pull/7300, 2023.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Grundlagen der geometrie", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> der geometrie. Springer-Verlag, 2013.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Draft, Sketch, and Prove: Guiding formal theorem provers with informal proofs", "authors": [{"first": "A", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["P"], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Li", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Jamnik", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>, Sketch, and Prove: Guiding formal theorem provers with infor- mal proofs. In International Conference on Learning Representations (ICLR), 2023a.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Multilingual mathematical autoformalization", "authors": [{"first": "A", "middle": ["Q"], "last": "Jiang", "suffix": ""}, {"first": "W", "middle": [], "last": "Li", "suffix": ""}, {"first": "M", "middle": [], "last": "Jamnik", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.03755"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. Multilingual mathemat- ical autoformalization. arXiv preprint arXiv:2311.03755, 2023b.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "A systematic evaluation of large models for geometric reasoning", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Soricut", "suffix": ""}, {"first": "", "middle": [], "last": "Geomverse", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.12241"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: A systematic evaluation of large models for geometric reasoning. arXiv preprint arXiv:2312.12241, 2023.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Hyper-Tree proof search for neural theorem proving", "authors": [{"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M.-A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Hay<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, X. Hyper- Tree proof search for neural theorem proving. In Neural Information Processing Systems (NeurIPS), 2022.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "CompCert-a formally verified optimizing compiler", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Blazy", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "C", "middle": [], "last": "", "suffix": ""}], "year": 2016, "venue": "Embedded Real Time Software and Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, M<PERSON>, and <PERSON>, <PERSON><PERSON>-a formally verified optimizing compiler. In Embedded Real Time Software and Systems, 2016.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "A survey on deep learning for theorem proving", "authors": [{"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "Su", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Si", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2404.09939"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. A survey on deep learning for theorem proving. arXiv preprint arXiv:2404.09939, 2024.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "UniMath: A foundational and multimodal mathematical reasoner", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Conference on Empirical Methods in Natural Language Processing", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: A foundational and multimodal mathematical reasoner. In Conference on Empirical Methods in Natural Language Processing (EMNLP), 2023.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Inter-GPS: Interpretable geometry problem solving with formal language and symbolic reasoning", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Annual Meeting of the Association for Computational Linguistics (ACL)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S.-c. Inter-GPS: Interpretable geometry problem solving with formal language and symbolic reasoning. In Annual Meeting of the Association for Computational Linguistics (ACL), 2021.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "A diagrammatic formal system for Euclidean geometry", "authors": [{"first": "N", "middle": ["G"], "last": "<PERSON>", "suffix": ""}], "year": 2001, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> G<PERSON> A diagrammatic formal system for Euclidean geometry. 2001.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Plane geometry theorem proving using forward chaining", "authors": [{"first": "A", "middle": ["J"], "last": "Nevins", "suffix": ""}], "year": 1975, "venue": "OpenAI. GPT-4 technical report", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.08774"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> geometry theorem proving using forward chaining. Artificial Intelligence, 1975. OpenAI. GPT-4 technical report. arXiv preprint arXiv:2303.08774, 2023.", "links": null}}, "ref_entries": {"FIGREF0": {"fig_num": "1", "type_str": "figure", "num": null, "text": "Figure 1. Left: Proposition 1 in <PERSON><PERSON><PERSON>'s Elements (Book I). The orange text involves diagrammatic reasoning: <PERSON><PERSON><PERSON> did not explicitly prove the two circles actually intersect, but the reader can use the diagram to implicitly fill in the logical gap. Top right:The model autoformalizes the problem into a formal theorem (proposition 1'), which is evaluated by checking its logical equivalence with the ground truth (proposition 1), leveraging domain knowledge and a symbolic automated reasoning engine based on SMT (satisfiability modulo theories) solvers. Bottom right: A proof autoformalized by the model. Like <PERSON><PERSON><PERSON>'s proofs, it does not need to handle diagrammatic reasoning explicitly. <PERSON><PERSON> can check the proof to identify a list of diagrammatic reasoning gaps, e.g., \"intersects BCD ACE\". Then, it attempts to fill in all gaps automatically using the symbolic reasoning engine based on SMT solvers.", "uris": null}, "FIGREF1": {"fig_num": null, "type_str": "figure", "num": null, "text": "axiom circle_from_points : ∀ (a b : Point), a ̸ = b → ∃ α : Circle, (isCenter a α) ∧ (onCircle b α) circles constructs their intersection: axiom intersection_circles : ∀ (α β : Circle), intersectsCircle α β → ∃ c : Point, (onCircle c α) ∧ (onCircle c β)", "uris": null}, "FIGREF2": {"fig_num": "2", "type_str": "figure", "num": null, "text": "Figure2. Three steps in the proof in Fig.1. Left: Geometric objects and facts. Right: Rules applied to construct new objects and deduce new facts. Each rule has inbound arrows from its preconditions and outbound arrows to its postconditions. Dashed lines: When applying a rule with missing preconditions, we try to fill the gap using a symbolic reasoning engine based on SMT solvers. These implicit steps performed by the symbolic engine can only use non-construction rules, whereas explicit steps performed by humans (or machine learning models) can use any rules.", "uris": null}, "FIGREF3": {"fig_num": "3", "type_str": "figure", "num": null, "text": "Figure 3. Approximate equivalence checking results for theorems from Elements formalized by GPT-4 (5-shots).", "uris": null}, "FIGREF4": {"fig_num": null, "type_str": "figure", "num": null, "text": "For example, points a, b, c can form segment (a--b), angle ∠ a:b:c, and triangle △ a:b:c. → Point → Type Angle : Point → Point → Point → Type Triangle : Point → Point → Point → Type A.2. Functions, Constants, and Relations", "uris": null}, "FIGREF5": {"fig_num": null, "type_str": "figure", "num": null, "text": "opposingSides (a : Point) (b : Point) (l : Line) : Prop := ¬(onLine a l) ∧ ¬(onLine b l) ∧ ¬(sameSide a b l) outsideCircle (p : Point) (c : Circle) : Prop := ¬(insideCircle p c) ∧ ¬(onCircle p c) formTriangle (a b c : Point) (AB BC CA : Line) : Prop := (distinctPointsOnLine a b AB) ∧ ( onLine b BC) ∧ (onLine c BC) ∧ (onLine c CA) ∧ (onLine a CA) ∧ AB ̸ = BC ∧ BC ̸ = CA ∧ CA ̸ = AB formParallelogram (a b c d : Point) (AB CD AC BD : Line) : Prop := (onLine a AB) ∧ (onLine b AB) ∧ (onLine c CD) ∧ (onLine d CD) ∧ (onLine a AC) ∧ (onLine c AC) ∧ ( distinctPointsOnLine b d BD) ∧ (sameSide a c BD) ∧ ¬(intersectsLine AB CD) ∧ ¬( intersectsLine AC BD)", "uris": null}, "FIGREF6": {"fig_num": null, "type_str": "figure", "num": null, "text": "arbitrary_point : ∃ a : Point, true distinct_points : ∀ a : Point, ∃ b : Point, a ̸ = b line_nonempty : ∀ l : Line, ∃ p : Point, onLine p l exists_distincts_points_on_line : ∀ l : Line, ∀ a : Point, ∃ b : Point, a ̸ = b ∧ onLine b l exists_point_between_points_on_line : ∀ (L : Line) (b c : Point), distinctPointsOnLine b c L → ∃ a : Point, (onLine a L) ∧ (between b a c) exists_point_between_points_not_on_line : ∀ (L M : Line) (b c : Point), ( distinctPointsOnLine b c L) ∧ L ̸ = M → ∃ a : Point, (onLine a L) ∧ (between b a c) ∧ ¬(onLine a M) point_between_points_shorter_than : ∀ (L : Line) (b c : Point) (s : Segment), distinctPointsOnLine b c L ∧ (|s| > 0) → ∃ a : Point, (onLine a L) ∧ (between b a c) ∧ (|b--a| < |s|) extend_point : ∀ (L : Line) (b c : Point), distinctPointsOnLine b c L → ∃ a : Point, ( onLine a L) ∧ (between b c a) extend_point_not_on_line : ∀ (L M : Line) (b c : Point), (distinctPointsOnLine b c L) ∧ L ̸ = M → ∃ a : Point, (onLine a L) ∧ (between b c a) ∧ ¬(onLine a M) extend_point_longer : ∀ (L : Line) (b c : Point) (s : Segment), distinctPointsOnLine b c L → ∃ a : Point, (onLine a L) ∧ (between b c a) ∧ (|c--a| > |s|) point_same_side : ∀ (L : Line) (b : Point), ¬(onLine b L) → ∃ a : Point, sameSide a b L distinct_point_same_side: ∀ (L : Line) (b c : Point), ¬(onLine b L) → ∃ a : Point, a ̸ = c ∧ (sameSide a b L) point_on_line_same_side : ∀ (L M : Line) (b : Point), ¬(onLine b L) ∧ (intersectsLine L M) → ∃ a : Point, (onLine a M) ∧ (sameSide a b L) exists_point_opposite : ∀ (L : Line) (b : Point), ¬(onLine b L) → ∃ a : Point, opposingSides a b L exists_distinct_point_opposite_side : ∀ (L : Line) (b c : Point), ¬(onLine b L) → ∃ a : Point, a ̸ = c ∧ (opposingSides a b L) exists_point_on_circle : ∀ (α : Circle), ∃ a : Point, onCircle a α exists_distinct_point_on_circle : ∀ (α : Circle) (b : Point), ∃ a : Point, a ̸ = b ∧ ( onCircle a α) exists_point_inside_circle : ∀ (α : Circle), ∃ a : Point, insideCircle a α exists_distinct_point_inside_circle : ∀ (α : Circle) (b : Point), ∃ a : Point, a ̸ = b ∧ ( insideCircle a α) exists_point_outside_circle : ∀ (α : Circle), ∃ a : Point, outsideCircle a α exists_distinct_point_outside_circle : ∀ (α : Circle) (b : Point), ∃ a : Point, a ̸ = b ∧ (outsideCircle a α) line_from_points : ∀ (a b : Point), a ̸ = b → ∃ L : Line, (onLine a L) ∧ (onLine b L) circle_from_points : ∀ (a b : Point), a ̸ = b → ∃ α : Circle, (isCentre c α) ∧ (onCircle b α) intersection_lines : ∀ (L M : Line), intersectsLine L M → ∃ a : Point, (onLine a L) ∧ ( onLine a M) intersection_circle_line : ∀ (α : Circle) (L : Line), intersectsCircle L α → ∃ a : Point, (onCircle a α) ∧ (onLine a L) intersections_circle_line : ∀ (α : Circle) (L : Line), intersectsCircle L α → ∃ (a b : Point), (onCircle a α) ∧ (onLine a L) ∧ (onCircle b α) ∧ (onLine b L) ∧ a ̸ = b intersection_circle_line_between_points : ∀ (α : Circle) (L : Line) (b c :Point), ( insideCircle b α) ∧ (onLine b L) ∧ (outsideCircle c α) ∧ (onLine c L) → ∃ a : Point, (onCircle a α) ∧ (onLine a L) ∧ (between b a c) intersection_circle_line_extending_points : ∀ (α : Circle) (L : Line) (b c :Point), ( insideCircle b α) ∧ distinctPointsOnLine b c L → ∃ a : Point, (onCircle a α) ∧ ( onLine a L) ∧ (between a b c) intersection_circles : ∀ (α β : Circle), intersectsCircle α β → ∃ a : Point, (onCircle a α) ∧ (onCircle a β) intersections_circles : ∀ (α β : Circle), intersectsCircle α β → ∃ (a b : Point), ( onCircle a α) ∧ (onCircle a β) ∧ (onCircle b α) ∧ (onCircle b β) ∧ a ̸ = b intersection_same_side : ∀ (α β : Circle) (b c d : Point) (L : Line), (intersectsCircle α β) ∧ (isCentre c α) ∧ (isCentre d β) ∧ (onLine c L) ∧ (onLine d L) ∧ ¬(onLine b L) → ∃ a : Point, (onCircle a α) ∧ (onCircle a β) ∧ (sameSide a b L) intersection_opposite_side : ∀ (α β : Circle) (b c d : Point) (L : Line), ( intersectsCircle α β) ∧ (isCentre c α) ∧ (isCentre d β) ∧ (onLine c L) ∧ (onLine d L) ∧ ¬(onLine b L) → ∃ a : Point, (onCircle a α) ∧ (onCircle a β) ∧ (opposingSides a b L) Diagrammatic Rules. We have 36 diagrammatic rules for deducing diagrammatic facts, i.e., relations among objects. They include four rules not in E's paper: intersection lines common point, parallel line unique, intersection symm, and parallelogram same side. two_points_determine_line : ∀ (a b : Point) (L M : Line), distinctPointsOnLine a b L ∧ ( onLine a M) ∧ (onLine b M) → L = M centre_unique : ∀ (a b : Point) (α : Circle), (isCentre c α) ∧ (isCentre b α) → a = b center_inside_circle : ∀ (a : Point) (α : Circle), isCentre c α → insideCircle a α inside_not_on_circle : ∀ (a : Point) (α : Circle), insideCircle a α → ¬(onCircle a α) between_symm : ∀ (a b c : Point), between a b c → (between c b a) ∧ (a ̸ = b) ∧ (a ̸ = c) ∧ ¬(between b a c) between_same_line_out : ∀ (a b c : Point) (L : Line), (between a b c) ∧ (onLine a L) ∧ ( onLine b L) → onLine c L between_same_line_in : ∀ (a b c : Point) (L : Line), (between a b c) ∧ (onLine a L) ∧ ( onLine c L) → onLine b L between_trans_in : ∀ (a b c d : Point), (between a b c) ∧ (between a d b) → between a d c between_trans_out : ∀ (a b c d : Point), (between a b c) ∧ (between b c d) → between a b d between_points : ∀ (a b c : Point) (L : Line), (a ̸ = b) ∧ (b ̸ = c) ∧ (c ̸ = a) ∧ (onLine a L ) ∧ (onLine b L) ∧ (onLine c L) → (between a b c) ∨ (between b a c) ∨ (between a c b) between_not_trans : ∀ (a b c d : Point), (between a b c) ∧ (between a b d) → ¬(between c b d) same_side_rfl : ∀ (a : Point) (L : Line), ¬(onLine a L) → sameSide a a L same_side_symm : ∀ (a b : Point) (L : Line), sameSide a b L → sameSide b a L same_side_not_on_line : ∀ (a b : Point) (L : Line), sameSide a b L → ¬(onLine a L) same_side_trans : ∀ (a b c : Point) (L : Line), (sameSide a b L) ∧ (sameSide a c L) → sameSide b c L same_side_pigeon_hole : ∀ (a b c : Point) (L : Line), ¬(onLine a L) ∧ ¬(onLine b L) ∧ ¬( onLine c L) → (sameSide a b L) ∨ (sameSide a c L) ∨ (sameSide b c L) pasch_1: ∀ (a b c : Point) (L : Line), (between a b c) ∧ (sameSide a c L) → sameSide a b L pasch_2: ∀ (a b c : Point) (L : Line), (between a b c) ∧ (onLine a L) ∧ ¬(onLine b L) → sameSide b c L pasch_3: ∀ (a b c : Point) (L : Line), (between a b c) ∧ (onLine b L) → ¬(sameSide a c L) pasch_4: ∀ (a b c : Point) (L M : Line), (L ̸ = M) ∧ (onLine b L) ∧ (onLine b M) ∧ distinctPointsOnLine a c M ∧ (a ̸ = b) ∧ (c ̸ = b) ∧ ¬(sameSide a c L) → between a b c triple_incidence_1 : ∀ (L M N : Line) (a b c d : Point), (onLine a L) ∧ (onLine a M) ∧ ( onLine a N) ∧ (onLine b L) ∧ (onLine c M) ∧ (onLine d N) ∧ (sameSide c d L) ∧ ( sameSide b c N) → ¬(sameSide b d M) triple_incidence_2 : ∀ (L M N : Line) (a b c d : Point), (onLine a L) ∧ (onLine a M) ∧ ( onLine a N) ∧ (onLine b L) ∧ (onLine c M) ∧ (onLine d N) ∧ (sameSide c d L) ∧ ¬( sameSide b d M) ∧ ¬(onLine d M) ∧ (b ̸ = a) → sameSide b c N triple_incidence_3 : ∀ (L M N : Line) (a b c d e : Point), (onLine a L) ∧ (onLine a M) ∧ ( onLine a N) ∧ (onLine b L) ∧ (onLine c M) ∧ (onLine d N) ∧ (sameSide c d L) ∧ ( sameSide b c N) ∧ (sameSide d e M) ∧ (sameSide c e N) → sameSide c e L circle_line_intersections : ∀ (a b c : Point) (L : Line) (α : Circle), (onLine a L) ∧ ( onLine b L) ∧ (onLine c L) ∧ (insideCircle a α) ∧ (onCircle b α) ∧ (onCircle c α) ∧ ( b ̸ = c) → between b a c circle_points_between : ∀ (a b c : Point) (α : Circle), ¬(outsideCircle a α) ∧ ¬( outsideCircle b α) ∧ (between a c b) → insideCircle c α circle_points_extend : ∀ (a b c : Point) (α : Circle), ¬(outsideCircle a α) ∧ ¬( insideCircle c α) ∧ (between a c b) → (outsideCircle b α) circles_intersections_diff_side : ∀ (a b c d : Point) (α β : Circle) (L : Line), (α ̸ = β) ∧ (onCircle c α) ∧ (onCircle c β) ∧ (onCircle d α) ∧ (onCircle d β) ∧ (c ̸ = d) ∧ ( isCentre c α) ∧ (isCentre b β) ∧ (onLine a L) ∧ (onLine b L) → ¬(sameSide c d L) intersection_lines_opposing: ∀ (a b : Point) (L M : Line), (opposingSides a b L) ∧ (onLine a M) ∧ (onLine b M) → intersectsLine L M intersection_lines_common_point : ∀ (a : Point) (L M : Line), onLine a L ∧ (onLine a M) ∧ (L ̸ = M) → intersectsLine L M parallel_line_unique : ∀ (a : Point) (L M N : Line), ¬(onLine a L) ∧ (onLine a M) ∧ onLine a N ∧ ¬(intersectsLine L M) ∧ ¬(intersectsLine L N) → M = N intersection_symm : ∀ (L M : Line), intersectsLine L M → intersectsLine M L intersection_circle_line_1: ∀ (a b : Point) (α : Circle) (L: Line), ¬(outsideCircle a α) ∧ ¬(outsideCircle b α) ∧ (opposingSides a b L) → intersectsCircle L α intersection_circle_line_2: ∀ (a : Point) (α : Circle) (L: Line), (insideCircle a α) ∧ ( onLine a L) → intersectsCircle L α intersection_circle_circle_1: ∀ (a b : Point) (α β : Circle), ¬(outsideCircle a α) ∧ ¬( outsideCircle b α) ∧ (insideCircle a β) ∧ (outsideCircle b β) → intersectsCircle α β intersection_circle_circle_2: ∀ (a b : Point) (α β : Circle), (onCircle a α) → ( insideCircle b α) → (insideCircle a β) → (onCircle b β) → intersectsCircle α β parallelogram_same_side : ∀ (a b c d : Point) (AB CD AC BD : Line), formParallelogram a b c d AB CD AC BD → sameSide b d AC ∧ sameSide c d AB ∧ sameSide a b CD", "uris": null}, "FIGREF7": {"fig_num": null, "type_str": "figure", "num": null, "text": "zero_segment_if : ∀ (a b : Point), |(a--b)| = 0 → a = b zero_segment_onlyif : ∀ (a b : Point), a = b → |(a--b)| = 0 segment_gte_zero : ∀ (s : Segment), 0 ≤ s.length segment_symmetric : ∀ (a b : Point), |(a--b)| = |(b--a)| angle_symm : ∀ (a b c : Point), (a ̸ = b) ∧ (b ̸ = c) → (∠ a:b:c = ∠ c:b:a) angle_range : ∀ (ang : <PERSON>le), (0 : R) ≤ ang ∧ ang ≤ + degenerated_area : ∀ (a b : Point), Triangle.area △ a:a:b = 0 area_gte_zero : ∀ (ar : Triangle), 0 ≤ Triangle.area ar area_symm_1 : ∀ (a b c : Point), Triangle.area (△a:b:c) = Triangle.area(△c:a:b) area_symm_2 : ∀ (a b c : Point), Triangle.area (△ a:b:c) = Triangle.area (△a:c:b) area_congruence : ∀ (a b c a' b' c' : Point), (a--b) = (a'--b') ∧ (b--c) = (b'--c') ∧ (c --a) = (c'--a') ∧ (∠ a:b:c) = (∠ a':b':c') ∧ (∠ b:c:a) = (∠ b':c':a') ∧ (∠ c:a:b) = ( ∠ c':a':b') → Triangle.area (△ a:b:c) = Triangle.area (△ a':b':c') Transfer Rules. We have 23 transfer rules acting as the bridge between diagrammatic facts and metric facts. They include 5 rules not in E's paper: flat angle if, flat angle onlyif, parallelogram area, sum parallelograms area, and rectangle area. between_if : ∀ (a b c : Point), between a b c → |(a--b)| + |(b--c)| = |(a--c)| equal_circles : ∀ (a b c : Point) (α β : Circle), (isCentre c α) ∧ (isCentre c β) ∧ ( onCircle b α) ∧ (onCircle c β) ∧ |(a--b)| = |(a--c)| → α = β point_on_circle_if : ∀ (a b c : Point) (α : Circle), (isCentre c α) ∧ (onCircle b α) ∧ |( a--c)| = |(a--b)| → onCircle c α point_on_circle_onlyif : ∀ (a b c : Point) (α : Circle), (isCentre c α) ∧ (onCircle b α) ∧ (onCircle c α) → |(a--c)| = |(a--b)| point_in_circle_if : ∀ (a b c : Point) (α : Circle), (isCentre c α) ∧ (onCircle b α) ∧ (|(a--c)| < |(a--b)|) → insideCircle c α point_in_circle_onlyif : ∀ (a b c : Point) (α : Circle), (isCentre c α) ∧ (onCircle b α) ∧ (insideCircle c α) → |(a--c)| < |(a--b)| degenerated_angle_if : ∀ (a b c : Point) (L : Line), (a ̸ = b) ∧ (a ̸ = c) ∧ (onLine a L) ∧ ( onLine b L) ∧ (onLine c L) ∧ ¬(between b a c) → ∠ b:a:c = 0 degenerated_angle_onlyif : ∀ (a b c : Point) (L : Line), (a ̸ = b) ∧ (a ̸ = c) ∧ (onLine a L) ∧ (onLine b L) ∧ (∠ b:a:c = 0) → (onLine c L) ∧ ¬(between b a c) sum_angles_if : ∀ (a b c d : Point) (L M : Line), (onLine a L) ∧ (onLine a M) ∧ (onLine b L", "uris": null}, "FIGREF8": {"fig_num": null, "type_str": "figure", "num": null, "text": "sameSide b d M) ∧ (sameSide c d L) → (∠ b:a:c) = (∠ b:a:d) + (∠ d:a:c) perpendicular_if : ∀ (a b c d : Point) (L : Line), (onLine a L) ∧ (onLine b L) ∧ (between a c b) ∧ ¬(onLine d L) ∧ (∠ a:c:d = ∠ d:c:b) → ∠ a:c:d = perpendicular_onlyif : ∀ (a b c d : Point) (L : Line), (onLine a L) ∧ (onLine b L) ∧ ( between a c b) ∧ ¬(onLine d L) ∧ (∠ a:c:d = ) → ∠ a:c:d = ∠ d:c:b flat_angle_if : ∀ (a b c : Point), a ̸ = b ∧ b ̸ = c ∧ (∠ a:b:c = + ) → between a b c flat_angle_onlyif : ∀ (a b c : Point), between a b c → ∠ a:b:c = + equal_angles : ∀ (a b b' c c' : Point) (L M : Line), (onLine a L) ∧ (onLine b L) ∧ (onLine b' L) ∧ (onLine a M) ∧ (onLine c M) ∧ (onLine c' M) ∧ (b ̸ = a) ∧ (b' ̸ = a) ∧ (c ̸ = a) ∧ (c' ̸ = a) ∧ ¬(between b a b') ∧ ¬(between c a c') → (∠ b:a:c = ∠ b':a:c') lines_intersect : ∀ (a b c d : Point) (L M N : Line), (onLine a L) ∧ (onLine b L) ∧ ( onLine b M) ∧ (onLine c M) ∧ (onLine c N) ∧ (onLine d N) ∧ (b ̸ = c) ∧ (sameSide a d M) ∧ (∠ a:b:c) + (∠ b:c:d) < + → ∃ e : Point, (onLine e L) ∧ (onLine e N) ∧ ( sameSide e a M) degenerated_area_if : ∀ (a b c : Point) (L : Line), distinctPointsOnLine a b L ∧ ( Triangle.area △ a:b:c) = 0 → onLine c L degenerated_area_onlyif : ∀ (a b c : Point) (L : Line), distinctPointsOnLine a b L ∧ ( onLine c L) → (Triangle.area △ a:b:c) = 0 sum_areas_if : ∀ (a b c d : Point) (L : Line), (onLine a L) ∧ (onLine b L) ∧ (onLine c L) ∧ (a ̸ = b) ∧ (a ̸ = c) ∧ (b ̸ = c) ∧ ¬(onLine d L) ∧ (between a c b) → (Triangle.area △ a:c:d + Triangle.area △ d:c:b = Triangle.area △ a:d:b) sum_areas_onlyif : ∀ (a b c d : Point) (L : Line), (onLine a L) ∧ (onLine b L) ∧ (onLine c L) ∧ (a ̸ = b) ∧ (a ̸ = c) ∧ (b ̸ = c) ∧ ¬(onLine d L) ∧ (Triangle.area △ a:c:d + Triangle.area △ d:c:b = Triangle.area △ a:d:b) → between a c b parallelogram_area : ∀ (a b c d : Point) (AB CD AC BD : Line), (formParallelogram a b c d AB CD AC BD) → Triangle.area △ a:c:d + Triangle.area △ a:d:b = Triangle.area △ b:a:c + Triangle.area △ b:c:d sum_parallelograms_area : ∀ (a b c d e f : Point) (AB CD AC BD : Line), (formParallelogram a b c d AB CD AC BD) ∧ (between a e b) ∧ (between c f d) → Triangle.area △ a:c:f + Triangle.area △ a:f:e + Triangle.area △ e:f:d + Triangle.area △ e:d:b = Triangle.area △ a:c:d + Triangle.area △ a:d:b rectangle_area : ∀ (a b c d : Point) (AB CD AC BD : Line), (formParallelogram a b c d AB CD AC BD) ∧ (∠ a:c:d = ) → (Triangle.area △ a:c:d + Triangle.area △ a:b:d = |(a--b) | * |(a--c)|) ∧ (Triangle.area △ b:a:c + Triangle.area △ b:d:c = |(a--b)| * |(a--c)|) Superposition Rule. Finally, we have a superposition rule to handle Euclid's method of superposition for proving Proposition 4 and Proposition 8 in the first book of Elements. The rule follows the idea in Avigad. superposition : ∀ (a b c d g h : Point) (AB BC AC L : Line), (formTriangle a b c AB BC AC) ∧ (distinctPointsOnLine d g L) ∧ ¬(onLine h L) → ∃ (b' c' : Point) (BC' AC' : Line), (∠ b:a:c = ∠ b':d:c') ∧ (∠ a:c:b = ∠ d:c':b') ∧ (∠ c:b:a = ∠ c':b':d) ∧ |(a--b)| = |(d--b')| ∧ |(b--c)| = |(b'--c')| ∧ |(c--a)| = |(c'--d)| ∧ (onLine b' L) ∧ ¬(between b' d g) ∧ (sameSide c' h L) ∧ (distinctPointsOnLine b' c' BC') ∧ (distinctPointsOnLine d c' AC')", "uris": null}, "FIGREF9": {"fig_num": null, "type_str": "figure", "num": null, "text": "have two sides equal to two sides, respectively, but (one) has the angle encompassed by the equal straight-lines greater than the (corresponding) angle (in the other), then (the former triangle) will also have a base greater than the base (of the latter).Let ABC and DEF be two triangles having the two sides AB and AC equal to the two sides DE and DF , respectively. (That is), AB (equal) to DE, and AC to DF . Let them also have the angle at A greater than the angle at D. I say that the base BC is also greater than the base EF . For since angle BAC is greater than angle EDF , let (angle) EDG, equal to angle BAC, have been constructed at the point D on the straight-line DE [Prop. 1.23]. And let DG be made equal to either of AC or DF [Prop. 1.3], and let EG and F G have been joined.Therefore, since AB is equal to DE and AC to DG, the two (straight-lines) BA, AC are equal to the two (straight-lines) ED, DG, respectively. Also the angle BAC is equal to the angle EDG.", "uris": null}, "FIGREF10": {"fig_num": null, "type_str": "figure", "num": null, "text": "Similar to what <PERSON><PERSON><PERSON> did, let's construct △ EDG s.t., ∠ EDG = ∠ BAC, |DG| = |DF |, and |BC| = |EG| (Fig. A). By Proposition 5, we have ∠ DGF = ∠ DF G; let's denote it by α. To prove |BC| > |EF |, we only need to prove |EG| > |EF |. By Proposition 19, this is further reduced to ∠ EF G > ∠ EGF . Let x = ∠ EF G and y = ∠ EGF . We want to prove x > y.", "uris": null}, "FIGREF11": {"fig_num": null, "type_str": "figure", "num": null, "text": "Figure A. The case in Proposition 24 missed by <PERSON><PERSON><PERSON>.", "uris": null}, "FIGREF12": {"fig_num": null, "type_str": "figure", "num": null, "text": "theorem proposition_24 : ∀ (a b c d e f : Point) (AB BC AC DE EF DF : Line), formTriangle a b c AB BC AC ∧ formTriangle d e f DE EF DF ∧ (|(a--b)| = |(d--e)|) ∧ (|(a--c)| = |(d--f)|) ∧ (∠ b:a:c > ∠ e:d:f) → |(b--c)| > |(e--f)| := by euclid_intros euclid_apply (proposition_23' d e a b c f DE AB AC) as g' euclid_apply (line_from_points d g') as DG euclid_apply (extend_point_longer DG d g' (a--c)) as g'' euclid_apply (proposition_3 d g'' a c DG AC) as g euclid_apply (line_from_points e g) as EG euclid_apply (line_from_points f g) as FG euclid_apply (proposition_4 a b c d e g AB BC AC DE EG DG) euclid_apply (proposition_5' d g f DG FG DF) by_cases (d.sameSide g EF) . euclid_assert (∠ d:f:g > ∠ e:g:f) euclid_assert (∠ e:f:g > ∠ e:g:f) euclid_apply (proposition_19 e f g EF FG EG) euclid_finish . --Omitted by Euclid. by_cases g.onLine EF . euclid_finish . euclid_apply (extend_point FG g f) as h euclid_assert ¬(g.onLine DF) euclid_assert ¬(e.onLine DF) euclid_assert (g.opposingSides e DF) euclid_assert h.sameSide e DF euclid_apply (proposition_13 d f g h DF FG) euclid_apply (proposition_13 e f g h EF FG) euclid_apply (proposition_17 d g e DG EG DE) euclid_apply (proposition_17 d f e DF EF DE) euclid_assert (∠ d:g:e < + ) euclid_assert (∠ d:f:e < + ) euclid_assert (∠ e:f:g + ∠ g:f:d + ∠ d:f:e = + + + ) euclid_assert (∠ e:f:g > ∠ e:g:f) euclid_apply (proposition_19 e f g EF FG EG) euclid_finish", "uris": null}, "FIGREF13": {"fig_num": null, "type_str": "figure", "num": null, "text": "So it is required to construct an equilateral triangle on the straight-line AB.Let the circle BCDwith center A and radius AB have been drawn [Post.3], and again let the circle ACE with center B and radius BA have been drawn [Post.3]. And let the straight-lines CA and CB have been joined from the point C, where the circles cut one another, to the points A and B (respectively) [Post.1]. And since the point A is the center of the circle CDB, AC is equal to AB [Def.5]. Again, since the point B is the center of the circle CAE, BC is equal to BA [Def.5]. But CA was also shown (to be) equal to AB. Thus, CA and CB are each equal to AB. But things equal to the same thing are also equal to one another [C.N.1]. Thus, CA is also equal to CB. Thus, the three (straight-lines) CA, AB, and BC are equal to one another.", "uris": null}, "FIGREF14": {"fig_num": null, "type_str": "figure", "num": null, "text": "theorem proposition_1 : ∀ (a b : Point) (AB : Line), distinctPointsOnLine a b AB → ∃ c : Point, |(c--a)| = |(a--b)| ∧ |(c--b)| = |(a--b)| := by euclid_intros euclid_apply circle_from_points a b as BCD euclid_apply circle_from_points b a as ACE euclid_apply intersection_circles BCD ACE as c euclid_apply point_on_circle_onlyif a b c BCD euclid_apply point_on_circle_onlyif b a c ACE use c euclid_finish", "uris": null}, "FIGREF15": {"fig_num": null, "type_str": "figure", "num": null, "text": "theorem online_of_circlesinter (aα : CenterCircle a α) (bβ : CenterCircle b β) (αβ : CirclesInter α β) : ∃ c L, OnLine a L ∧ OnLine b L ∧ OnCircle c α ∧ OnCircle c β ∧ ¬OnLine c L := by rcases line_of_pts a b with ⟨L, aL, bL⟩; rcases not_online_of_line L with ⟨d, dL⟩; rcases pt_sameSide_of_circlesInter aL bL dL aα bβ αβ with ⟨c, cdL, cα, cβ⟩; exact ⟨c, L, aL, bL, cα, cβ, not_onLine_of_sameSide cdL⟩ theorem DiffSide_of_sameside_DiffSide (abL : SameSide a b L) (acL : DiffSide a c L) : DiffSide b c L := by by_contra h; unfold DiffSide at h; push_neg at h; exact acL.2.2 (sameSide_trans (sameSide_symm abL) (h (not_onLine_of_sameSide (sameSide_symm abL)) acL .2.1)) theorem DiffSide_of_circlesinter (aα : CenterCircle a α) (bβ : CenterCircle b β) (αβ : CirclesInter α β) : ∃ c d L, OnLine a L ∧ OnLine b L ∧ OnCircle c α ∧ OnCircle c β ∧ OnCircle d α ∧ OnCircle d β ∧ DiffSide c d L := by rcases online_of_circlesinter aα bβ αβ with ⟨c, L, aL, bL, cα, cβ, cL⟩; rcases diffSide_of_not_onLine cL with ⟨e, eL, ceL⟩; rcases pt_sameSide_of_circlesInter aL bL eL aα bβ αβ with ⟨d, deL, dα, dβ⟩; exact ⟨c, d, L, aL, bL, cα, cβ, dα, dβ, DiffSide_symm (DiffSide_of_sameside_DiffSide (sameSide_symm deL) ⟨eL, cL, not_sameSide_symm ceL⟩)⟩ /--Euclid I.1, construction of two equilateral Triangles -/ theorem iseqtri_iseqtri_DiffSide_of_ne (ab : a ̸ = b) : ∃ c d L, OnLine a L ∧ OnLine b L ∧ DiffSide c d L ∧ EqTri a b c ∧ EqTri a b d := by rcases circle_of_ne ab with ⟨α, aα, bα⟩ rcases circle_of_ne (Ne.symm ab) with ⟨β, bβ, aβ⟩ rcases DiffSide_of_circlesinter aα bβ (circlesInter_of_inside_on_circle bα aβ (inside_circle_of_center aα) (inside_circle_of_center bβ)) with ⟨c, d, L, aL, bL, cα, cβ, dα, dβ, cdL⟩ have ab_ac := (on_circle_iff_length_eq aα bα).mpr cα have bc_ba := (on_circle_iff_length_eq bβ cβ).mpr aβ have ab_ad := (on_circle_iff_length_eq aα bα).mpr dα have bd_ba := (on_circle_iff_length_eq bβ dβ", "uris": null}, "FIGREF16": {"fig_num": null, "type_str": "figure", "num": null, "text": "-Basic Geometric Sorts -/ axiom Point : Type axiom Line : Type axiom Circle : Type /-Inductive Types for Geometric Entities -/ inductive Angle | right | ofPoints (A B C : Point) inductive Segment | endpoints (a b : Point) inductive Triangle | ofPoints (a b c : Point) /-Notations and Macros for Geometric Entities -/ \"|(a--b)|\" means the length of the line segment between point a and point b. \"∠ a:b:c\" means the degree of the angle formed by three points a, b, and c. \" \" means the right angle. \"△ a:b:c\" means the triangle formed from points a, b and c. \"Triangle.area △ a:b:c\" means the area of the triangle formed by points a, b and c. /-Relations and Axioms for Geometric Sorts -/ namespace Point def onLine (a : Point) (L : Line) --point a is on line L. def sameSide (a b : Point) (L : Line) --point a and b are on the same side of line L. def opposingSides (a b : Point) (L : Line) --point a and b are on the opposite sides of line L. def collinear (a b c : Point) --points a, b and c are collinear. end Point namespace Line def intersectsLine (L M : Line) --two lines L and M intersect at some point. def intersectsCircle (L : Line) (C : Circle) --line L and circle C intersect. end Line namespace Circle def onCircle (a: Point) (C: Circle) --point a is on circle C. def insideCircle (a: Point) (C: Circle) --point a is inside circle C. def outsideCircle (a: Point) (C: Circle)--point a is outside circle C. def isCentre (a: Point) (C: Circle) --point a is on the center circle C. def intersectsCircle (C1 C2: Circle) --circle C1 and C2 intersect. end Circle namespace Triangle def congruent (T1 T2 : Triangle) --triangle T1 and T2 are congruent. def similar (T1 T2 : Triangle) --triangle T1 and T2 are similar. end Triangle /-Geometric Relations -/ def distinctPointsOnLine (a b : Point) (L : Line) --points a and b are distinct and on line L. def twoLinesIntersectAtPoint (AB BC : Line) (b : Point) --line AB and BC intersect at point b. def between (a b c : Point) --points a, b and c collinear and cyclically ordered. def formTriangle (a b c : Point) (AB BC CA : Line) --point a, b and c form a triangle, where point a and b are on line AB, point b and c are on line BC, point a and c are on line CA. def formRectilinearAngle (a b c : Point) (AB BC : Line) --point a, b and c form a rectilinear angle where a̸ =b and b̸ =c. def formParallelogram (a b c d : Point) (AB CD AC BD : Line) --point a, b, d and c (in clockwise/counterclockwise order) form a parallelogram, where point a and b are on line AB, where point c and d are on line CD, where point a and c are on line AC, where point b and d are on line BD. def formQuadrilateral (a b c d : Point) (AB CD BC AD : Line) --point a, b, c and d (in clockwise/counterclockwise order) form a quadrilateral, where point a and b are on line AB, where point c and d are on line CD, where point b and c are on line BC, where point a and d are on line AD. /-Guidelines: -/ 1. Proposition Format: Your proposition must be of the form <<< ∀ (...) P_1 ∧ P_2 ... ∧", "uris": null}, "FIGREF17": {"fig_num": null, "type_str": "figure", "num": null, "text": "abbrev Point.opposingSides (a b : Point) (l : Line) := ¬ a.onLine l ∧ ¬ b.onLine l ∧ ¬ sameSide a b l abbrev Point.outsideCircle (p c : Point) := ¬ p.insideCircle c ∧ ¬ p.onCircle c abbrev formTriangle (a b c : Point) (AB BC CA : Line) : Prop := distinctPointsOnLine a b AB ∧ b.onLine BC ∧ c.onLine BC ∧ c.onLine CA ∧ a.onLine CA ∧", "uris": null}, "FIGREF18": {"fig_num": null, "type_str": "figure", "num": null, "text": "So it is required to place a straight-line at point $A$ equal to the given straight-line $BC$ . For let the straight-line $AB$ have been joined from point $A$ to point $B$ [ Post.˜1], and let the equilateral triangle $DAB$ have been been constructed upon it [Prop.˜1.1]. And let the straight-lines $AE$ and $BF$ have been produced in a straight-line with $DA$ and $DB$ (respectively) [Post.˜2]. And let the circle $CGH$ with center $B$ and radius $BC$ have been drawn [Post.˜3], and again let the circle $GKL$ with center $D$ and radius $DG$ have been drawn [Post.˜3]. Therefore, since the point $B$ is the center of (the circle) $CGH$, $BC$ is equal to $BG$ [Def .˜1.15]. Again, since the point $D$ is the center of the circle $GKL$, $DL$ is equal to $DG$ [Def.˜1.15]. And within these, $DA$ is equal to $DB$. Thus, the remainder $AL$ is equal to the remainder $BG$ [C.N.˜3]. But $BC$ was also shown (to be) equal to $BG$. Thus, $AL$ and $BC$ are each equal to $BG$. But things equal to the same thing are also equal to one another [C.N.˜1]. Thus, $AL$ is also equal to $BC$. Thus, the straight-line $AL$, equal to the given straight-line $BC$, has been placed at the given point $A$. (Which is) the very thing it was required to do Proof Setup: def example : ∀ (a b c : Point) (BC : Line), distinctPointsOnLine b c BC ∧ a ̸ = b → ∃ l : Point, |(a--l)| = |(b--c)| := by Your Response: euclid_intros euclid_apply (line_from_points a b) as AB euclid_apply (proposition_1 a b AB) as d euclid_apply (line_from_points d a ) as AE euclid_apply (line_from_points d b ) as BF euclid_apply (circle_from_points b c) as CGH euclid_apply (intersection_circle_line_extending_points CGH BF b d) as g euclid_apply (circle_from_points d g) as GKL euclid_apply (intersection_circle_line_extending_points GKL AE a d) as l euclid_apply (point_on_circle_onlyif b c g CGH) euclid_apply (point_on_circle_onlyif d l g GKL) euclid_apply (between_if l a d ) euclid_apply (between_if g b d )", "uris": null}, "FIGREF19": {"fig_num": null, "type_str": "figure", "num": null, "text": "Proof Setup: def example : ∀ (a b c : Point) (AB : Line), distinctPointsOnLine a b AB ∧ ¬(c.onLine AB ) → ∃ h : Point, h.onLine AB ∧ (∠ a:h:c) = ∨ (∠ b:h:cAB c) as d euclid_apply (circle_from_points c d) as EFG euclid_apply (intersections_circle_line EFG AB) as (e, g) euclid_apply (proposition_10 e g AB) as h euclid_apply (line_from_points c g) as CG euclid_apply (line_from_points c h) as CH euclid_apply (line_from_points c e) as CE use h euclid_apply (proposition_8 h c g h c e CH CG AB CH CE AB) euclid_finish Input:Informal Proof:", "uris": null}, "FIGREF20": {"fig_num": null, "type_str": "figure", "num": null, "text": "very thing it was required to show. Proof Setup: def example: ∀ (a b c d : Point) (AB BC AC : Line), formTriangle a b c AB BC AC ∧ ( between b c d) → ∠ a:c:d = ∠ c:a:b + ∠ a:b:c ∧ ∠ a:b:c + ∠ b:c:a + ∠ c:a:b = + by Your Response: euclid_intros have : (∠ a:c:d) = (∠ c:a:b) + (∠ a:b:c) := by euclid_apply (proposition_31 c a b AB ) as CE euclid_apply (point_on_line_same_side BC CE a ) as e euclid_apply (proposition_29''' b e a c AB CE AC) euclid_apply (proposition_29'''' e a d c b CE AB BC) euclid_finish constructor . assumption . euclid_apply (proposition_13 a c b d AC BC) euclid_finish Input: Informal Proof: To construct a parallelogram equal to a given triangle in a given rectilinear angle. Let $ABC$ be the given triangle, and $D$ the given rectilinear angle. So it is required to construct a parallelogram equal to triangle $ABC$ in the rectilinear angle $D$. Let $BC$ have been cut in half at $E$ [Prop.˜1.10], and let $AE$ have been joined. And let (angle) $CEF$, equal to angle $D$, have been constructed at the point $E$ on the straight-line $EC$ [Prop.˜1.23]. And let $AG$ have been drawn through $A$ parallel to $EC$ [Prop.˜1.31], and let $CG$ have been drawn through $C$ parallel to $EF$ [Prop.˜1.31]. Thus, $FECG$ is a parallelogram. And since $BE$ is equal to $EC$, triangle $ABE$ is also equal to triangle $AEC$. For they are on the equal bases, $BE$ and $EC$, and between the same parallels, $BC$ and $AG$ [Prop.˜1.38]. Thus, triangle $ABC$ is double (the area) of triangle $AEC$. And parallelogram $FECG$ is also double (the area) of triangle $AEC$. For it has the same base as ($AEC$), and is between the same parallels as ($AEC$) [Prop.˜1.41]. Thus, parallelogram $FECG$ is equal to triangle $ABC$. ($FECG$) also has the angle $CEF$ equal to the given ( angle) $D$. Thus, parallelogram $FECG$, equal to the given triangle $ABC$, has been constructed in the angle $CEF$, which is equal to $D$. (Which is) the very thing it was required to do. Theorem Statement: def example : ∀ (a b c d1 d2 d3 : Point) (AB BC AC D12 D23: Line), formTriangle a b c AB BC AC ∧ formRectilinearAngle d1 d2 d3 D12 D23 ∧ (∠ d1:d2:d3) > 0 ∧ (∠ d1:d2:d3) < + → ∃ (f g e c' : Point) (FG EC EF CG : Line), formParallelogram f g e c' FG EC EF CG ∧ (∠ c':e:f = ∠ d1:d2:d3) ∧ (Triangle.area △ f:e:c' + Triangle.area △ f:c':g = Triangle.area △ a:b:c) := by Your Response: euclid_intros euclid_apply (proposition_10 b c BC) as e euclid_apply (line_from_points a e) as AE euclid_apply (proposition_23''' e c d2 d1 d3 a BC D12 D23) as f' euclid_apply (line_from_points e f') as EF euclid_apply (proposition_31 a b c BC) as AG euclid_apply (intersection_lines AG EF) as f euclid_apply (proposition_31 c e f EF) as CG euclid_apply (intersection_lines CG AG) as g euclid_assert (formParallelogram f g e c AG BC EF CG) euclid_apply (proposition_38 a b e a e c AG BC AB AE AE AC) euclid_apply (proposition_41 f e c g a AG BC EF CG AE AC) use f, g, e, c, AG, BC, EF, CG euclid_finish ** NOTICE: in the above example, there is exactly one argument to 'use' for every existentially quantified variables in the theoerem statement.", "uris": null}, "FIGREF21": {"fig_num": null, "type_str": "figure", "num": null, "text": "theorem proposition_6 : ∀ (a b c : Point) (AB BC AC : Line), formTriangle a b c AB BC AC ∧ (∠ a:b:c = ∠ a:c:b) →(|(a--b)| = |(a--c)|) := Your Response: euclid_intros by_contra by_cases |(a--b)| > |(a--c)| . euclid_apply (proposition_3 b a a c AB AC) as d euclid_apply (line_from_points d c) as DC euclid_apply proposition_4 b d c c a b AB DC BC AC AB BC euclid_finish . euclid_apply (proposition_3 c a a b AC AB) as d euclid_apply (line_from_points d b) as DB euclid_apply (proposition_4 c d b b a c AC DB BC AB AC BC) euclid_finish ---End of Instruction ---", "uris": null}, "FIGREF22": {"fig_num": null, "type_str": "figure", "num": null, "text": "for Geometric Entitiesinductive Angle | right | ofPoints (A B C : Point) inductive Segment | endpoints (a b : Point) inductive Triangle | ofPoints (a b c : Point) -Notations and Macros for Geometric Entities -\"|(a--b)|\" means the length of the line segment between point a and point b. \"∠ a:b:c\" means the degree of the angle formed by points a, b, and c. \" \" means the right angle. \"△ a:b:c\" means the triangle formed from points a, b and c. \"Triangle.area △ a:b:c\" means the area of the triangle formed by points a, b and c. -Relations and Axioms for Geometric Sorts -namespace Point def onLine (a : Point) (L : Line) --point a is on line L. def sameSide (a b : Point) (L : Line) --point a and b are on the same side of line L. def opposingSides (a b : Point) (L : Line) --point a and b are on the opposite sides of line L. def collinear : (a b c : Point) --points a, b and c are collinear. end Point namespace Line def intersectsLine (L M : Line) --two lines L and M intersect at some point. def intersectsCircle (L : Line) (C : Circle) --line L and circle C intersect. end Line namespace Circle def onCircle (a: Point) (C: Circle) --point a is on circle C. def insideCircle (a: Point) (C: Circle) --point a is inside circle C. def outsideCircle (a: Point) (C: Circle)--point a is outside circle C. def isCentre (a: Point) (C: Circle) --point a is on the center circle C. def intersectsCircle (C1 C2: Circle) --circle C1 and C2 intersect. end Circle namespace Triangle --triangle T1 and T2 are congruent (i.e satisfies the SAS, AAS, ASA or SSS condition) def congruent (T1 T2 : Triangle) --triangle T1 and T2 are similar. def similar (T1 T2 : Triangle) --If T1 and T2 are congruent, then the corresponding angles and sides are equal. theorem congruent_if (T1 T2: Triangle): congruent T1 T2 → match T1,T2 with | (Triangle.ofPoints A B C) ,(Triangle.ofPoints D", "uris": null}, "FIGREF23": {"fig_num": null, "type_str": "figure", "num": null, "text": "T1 and T2 are similar, then the corresponding angles and the ratio of corresponding sides are equal. theorem similar_if (T1 T2: Triangle): similar T1 T2 → match T1,T2 with | (Triangle.ofPoints A B C) ,(Triangle.ofPoints D E F) => |(A--B)| / |(D--E)| = |(B--C)| / |(E--F)| ∧ |(A--B)| / |(D--E)| = |(B--C)| / |(E--F)| ∧ |(C--A)| / |(F--D)| = |(A--B)| / |(D--E)| ∧ ∠ A:B:C = ∠ D:E:F ∧ ∠ A:C:B = ∠ D:F:E ∧ ∠ B:A:C = ∠ E:D:F -Geometric Relations ---points a and b are distinct and on line L. def distinctPointsOnLine (a b : Point) (L : Line) --line AB and BC intersect at point b. def twoLinesIntersectAtPoint (AB BC : Line) (b : Point) --points a, b and c collinear and cyclically ordered. def between (a b c : Point) --point a, b and c form a triangle, where point a and b are on line AB, point b and c are on line BC, point a and c are on line CA. def formTriangle (a b c : Point) (AB BC CA : Line) --point a, b and c form a rectilinear angle where a̸ =b and b̸ =c. def formRectilinearAngle (a b c : Point) (AB BC : Line) --point a, b, d, c (in clockwise/counterclockwise order) form a parallelogram, a and b are on line AB, c and d are on line CD, a and c are on line AC, b and d are on line BD def formParallelogram (a b c d : Point) (AB CD AC BD : Line) --point a, b, d, c (in clockwise/counterclockwise order) form a quadrilateral, a and b are on line AB, c and d are on line CD, a and c are on line AC, b and d are on line BD def formQuadrilateral (a b c d : Point) (AB CD AC BD : Line) ---Axioms ----Construction Rulesaxiom extend_point : ∀ (L : Line) (b c : Point), distinctPointsOnLine b c L → ∃ a : Point , (a.onLine L) ∧ (between b c a) -Useful Lemmas from Euclid's Elements Book I---In isosceles triangles the angles at the base equal one another, and, if the equal straight lines are produced further, then the angles under the base equal one another. theorem proposition_5 : ∀ (a b c d e : Point) (AB BC AC : Line), formTriangle a b c AB BC AC ∧ (|(a--b)| = |(a--c)|) ∧ (between a b d) ∧ (between a c e) → (∠ a:b:c) = (∠ a:c:b ) ∧ (∠ c:b:d) = (∠ b:c:e) theorem proposition_5' : ∀ (a b c : Point) (AB BC AC : Line), formTriangle a b c AB BC AC ∧ (|(a--b)| = |(a--c)|) → ((∠ a:b:c ) = ∠ a:c:b) --If a straight line stands on a straight line, then it makes either two right angles or angles whose sum equals two right angles. theorem proposition_13 : ∀ (a b c d : Point) (AB CD : Line), AB ̸ = CD ∧ distinctPointsOnLine a b AB ∧ distinctPointsOnLine c d CD ∧ between d b c → ∠ c:b:a + ∠ a:b:d = + --If two straight lines cut one another, then they make the vertical angles equal to one another. theorem proposition_15 : ∀ (a b c d e : Point) (AB CD : Line), distinctPointsOnLine a b AB ∧ distinctPointsOnLine c d CD ∧ e.onLine AB ∧ e.onLine CD ∧ CD ̸ = AB ∧ (between d e c ) ∧ (between a e b) → (∠ a:e:c ) = (∠ d:e:b) ∧ (∠ c:e:b ) = (∠ a:e:d) --If a straight line falling on two straight lines makes the alternate angles equal to one another, then the straight lines are parallel to one another. theorem proposition_27 : ∀ (a b c d e f : Point) (AB CD EF : Line), distinctPointsOnLine a b AB ∧ distinctPointsOnLine c d CD ∧ distinctPointsOnLine e f EF ∧ (between a e b) ∧ (between c f d) ∧ (b.sameSide d EF) ∧ ∠ a:e:f = (∠ e:f:d) → ¬(AB.intersectsLine CD) theorem proposition_27' : ∀ (a d e f : Point) (AB CD EF : Line), distinctPointsOnLine a e AB ∧ distinctPointsOnLine f d CD ∧ distinctPointsOnLine e f EF ∧ a.opposingSides d EF ∧ ∠ a:e:f = (∠ e:f:d) → ¬(AB.intersectsLine CD) --If a straight line falling on two straight lines makes the exterior angle equal to the interior and opposite angle on the same side, or the sum of the interior angles on the same side equal to two right angles, then the straight lines are parallel to one another. theorem proposition_28 : ∀ (a b c d e f g h : Point) (AB CD EF : Line), distinctPointsOnLine a b AB ∧ distinctPointsOnLine c d CD ∧ distinctPointsOnLine e f EF ∧ (between a g b) ∧ (between c h d) ∧ (between e g h) ∧ (between g h f) ∧ (b. sameSide d EF) ∧ (∠ e:g:b = ∠ g:h:d ∨ ∠ b:g:h + ∠ g:h:d = + ) → ¬(AB. intersectsLine CD) --A straight line falling on parallel straight lines makes the alternate angles equal to one another, the exterior angle equal to the interior and opposite angle, and the sum of the interior angles on the same side equal to two right angles. theorem proposition_29 : ∀ (a b c d e f g h : Point) (AB CD EF : Line), distinctPointsOnLine a b AB ∧ distinctPointsOnLine c d CD ∧ distinctPointsOnLine e f EF ∧ (between a g b) ∧ (between c h d) ∧ (between e g h) ∧ (between g h f) ∧ (b. sameSide d EF) ∧ ¬(AB.intersectsLine CD) → ∠ a:g:h = ∠ g:h:d ∧ ∠ e:g:b = ∠ g:h:d ∧ ∠ b:g:h + ∠ g:h:d = + theorem proposition_29' : ∀ (a b c d e g h : Point) (AB CD EF : Line), distinctPointsOnLine a b AB ∧ distinctPointsOnLine c d CD ∧ distinctPointsOnLine g h EF ∧ (between a g b) ∧ (between c h d) ∧ (between e g h) ∧ (b.sameSide d EF) ∧ ¬(AB. intersectsLine CD) → ∠ a:g:h = ∠ g:h:d ∧ ∠ e:g:b = ∠ g:h:d ∧ ∠ b:g:h + ∠ g:h:d = + theorem proposition_29'' : ∀ (a b d g h : Point) (AB CD GH : Line), distinctPointsOnLine a b AB ∧ distinctPointsOnLine h d CD ∧ distinctPointsOnLine g h GH ∧ (between a g b) ∧ (b.sameSide d GH) ∧ ¬(AB.intersectsLine CD) → ∠ a:g:h = ∠ g:h:d ∧ ∠ b:g:h + ∠ g:h:d = + theorem proposition_29''' : ∀ (a d g h : Point) (AB CD GH : Line),distinctPointsOnLine a g AB ∧ distinctPointsOnLine h d CD ∧ distinctPointsOnLine g h GH ∧a.opposingSides d GH ∧ ¬(AB.intersectsLine CD) → ∠ a:g:h = ∠ g:h:d theorem proposition_29'''' : ∀ (b d e g h : Point) (AB CD EF : Line), distinctPointsOnLine g b AB ∧ distinctPointsOnLine h d CD ∧ distinctPointsOnLine e h EF ∧ between e g h ∧ b.sameSide d EF ∧ ¬(AB.intersectsLine CD) → ∠ e:g:b = ∠ g:h:d theorem proposition_29''''' : ∀ (b d g h : Point) (AB CD EF : Line), distinctPointsOnLine g b AB ∧ distinctPointsOnLine h d CD ∧ distinctPointsOnLine g h EF ∧ b.sameSide d EF ∧ ¬(AB.intersectsLine CD) → ∠ b:g:h + ∠ g:h:d = + --In any triangle, if one of the sides is produced, then the exterior angle equals the sum of the two interior and opposite angles, and the sum of the three interior angles of the triangle equals two right angles. theorem proposition_32 : ∀ (a b c d : Point) (AB BC AC : Line), formTriangle a b c AB BC AC ∧ (between b c d) → ∠ a:c:d = ∠ c:a:b + ∠ a:b:c ∧ ∠ a:b:c + ∠ b:c:a + ∠ c:a:b = + --In parallelogrammic areas the opposite sides and angles equal one another, and the diameter bisects the areas. theorem proposition_34 : ∀ (a b c d : Point) (AB CD AC BD BC : Line), formParallelogram a b c d AB CD AC BD ∧ distinctPointsOnLine b c BC → |(a--b)| = |(c--d)| ∧ |(a--c)| = |( b--d)| ∧ ∠ a:b:d = ∠ a:c:d ∧ ∠ b:a:c = ∠ c:d:b ∧ Triangle.area △ a:b:c = Triangle. area △ d:c:b theorem proposition_34' : ∀ (a b c d : Point) (AB CD AC BD : Line), formParallelogram a b c d AB CD AC BD → |(a--b)| = |(c--d)| ∧ |(a--c)| = |(b--d)| ∧ ∠ a:b:d = ∠ a:c:d ∧ ∠ b:a:c = ∠ c:d:b ---Proof DSL ---Your response must be a tactic proof in the LeanEuclid proof DSL. This DSL is built from the following tactics: You need to formalize each key step (lemma) in the given proof and prove it by 'have <name > : <claim> := by <proof>' 1. euclid_intros It introduces universally quantified variables and premises of the current goal into the proof context. No names required. 2. euclid_apply <rule> <args> If <rule> has the form ∀ (<args> : Types) ... P -> Q, it instantiates <rule> with <args>, and attempts to prove premise P from the local proof context using the above axioms. If successful, propsition Q is added to the proof context. usage examples : euclid_apply proposition_13 A B C D AB CD --This proves that ∠ C:B:A + ∠ A:B:D = + If <rule> has the form ∀ (<args> : Types) ... P -> ∃ x . Q(x), this tactic instantiates < rule> with <args>, and attempts to prove premise P from the local proof context using the above axioms. If successful, object x and premise Q(x) are added to the proof context. usage examples: euclid_apply extend_point L a b as c --This creates a point c on line L such that b is between a and c.", "uris": null}, "FIGREF24": {"fig_num": null, "type_str": "figure", "num": null, "text": "c d e f : Point) (AB AC BC DE DF EF : Line), formTriangle a b c AB AC BC ∧ formTriangle d e f DE DF EF ∧ |(a--b)| = |(d--e)| ∧ |(a--c)| = |(d--f)| ∧ |(b--c)| = |(e--f)| → (∠ a:b:c = ∠ d:e:f) ground : ∀ (a b c d e f : Point) (AB BC AC DE EF DF : Line), formTriangle a b c AB BC AC ∧ formTriangle d e f DE EF DF ∧ |(a--b)| = |(d--e)| ∧ |(a--c)| = |(d--f)| ∧ |(b--c)| = |(e--f)| → (∠ b:a:c) = (∠ e:d:f)", "uris": null}, "FIGREF25": {"fig_num": null, "type_str": "figure", "num": null, "text": "b : Point) (AB : Line), distinctPointsOnLine a b AB → ∃ d : Point, between a d b ∧ |(a--d)| = |(d--b)| However, there is another way to specify that D lies exactly halfway between A and B, as suggested by GPT-4 -namely, by specifying that, in addition to |AD| = |BD|, we have |AD| + |BD| = |AB|: prediction: ∀ (a b : Point) (AB : Line), distinctPointsOnLine a b AB → ∃ d : Point, |(a--d)| = |(b--d)| ∧ |(d--b)| + |(a--d)| = |(a--b|", "uris": null}, "FIGREF26": {"fig_num": null, "type_str": "figure", "num": null, "text": "c : Point) (AB BC AC : Line), formTriangle a b c AB BC AC → |(b--a)| + |(a--c)| > |(b--c)|) Since this simplification is not obvious to the model, it will instead attempt to enumerate each for the combinations mentioned: prediction : ∀ (a b c : Point) (AB BC CA : Line), formTriangle a b c AB BC CA → (|(a--b)| + |(b--c)| > |(a--c)|) ∧ (|(a--b)| + |(a--c)| > |(b--c)|) ∧ (|(b--c)| + |(a--c)| > |(a--b)|)", "uris": null}, "FIGREF27": {"fig_num": "1", "type_str": "figure", "num": null, "text": "GPT-4V. Natural language proof: \"To construct an equilateral triangle on a given finite straight-line. Let AB be the given finite straight-line. So it is required to construct an equilateral triangle on the straight-line AB. Let the circle BCD with center A and radius AB have been drawn [Post. 3], and again let the circle ACE with center B and radius BA have been drawn [Post. 3].And let the straight-lines CA and CB have been joined from the point C, where the circles cut one another, to the points A and B (respectively) [Post. 1].And since the point A is the center of the circle CDB, AC is equal to AB [Def. 1.15]. Again,since the point B is the center of the circle CAE, BC is equal to BA [Def. 1.15]. But CA was also shown (to be) equal to AB. Thus, CA and CB are each equal to AB. But things equal to the same thing are also equal to one another [C.N. 1]. Thus, CA is also equal to CB. Thus, the three (straight-lines) CA, AB, and BC are equal to one another.Thus, the triangle ABC is equilateral, and has been constructed on the given finite straight-line AB. (Which is) the very thing it was required to do.\" Autoformalized proof:", "uris": null}, "FIGREF28": {"fig_num": null, "type_str": "figure", "num": null, "text": "Figure C. Lev<PERSON>htein ratio between autoformalized and repaired Elements proofs generated by GPT-4 (5-shot).", "uris": null}, "FIGREF29": {"fig_num": null, "type_str": "figure", "num": null, "text": "theorem prop1_prediction : ∀ (a b : Point) (AB : Line), distinctPointsOnLine a b AB → ∃ c : Point, |(c--a)| = |(a--b)| ∧ |(c--b)| = |(a--b)|:= by euclid_intros euclid_apply circle_from_points a b as BCD euclid_apply circle_from_points b a as ACE euclid_apply intersection_circles BCD ACE as c euclid_apply line_from_points c a as CA euclid_apply line_from_points c b as CB euclid_apply point_on_circle_if a b c BCD euclid_apply point_on_circle_if b a c ACE use c euclid_finish", "uris": null}, "FIGREF30": {"fig_num": null, "type_str": "figure", "num": null, "text": "Figure D. Levenshtein ratio between autoformalized and repaired Elements proofs generated by GPT-4V (5-shot).", "uris": null}, "FIGREF31": {"fig_num": null, "type_str": "figure", "num": null, "text": "So it is required to cut off a straight-line equal to the lesser C from the greater AB. Let the line AD, equal to the straight-line C, have been placed at point A [Prop. 1.2]. And let the circle DEF have been drawn with center A and radius AD [Post. 3]. And since point A is the center of circle DEF , AE is equal to AD [Def. 1.15]. But, C is also equal to AD. Thus, AE and C are each equal to AD. So AE is also equal to C [C.N. 1]. Thus, for two given unequal straight-lines, AB and C, the (straight-line) AE, equal to the lesser C, has been cut off from the greater AB. (Which is) the very thing it was required to do.\" Autoformalized Proof: def prop3_prediction : ∀ (a b c0 c1 : Point) (AB C : Line), distinctPointsOnLine a b AB ∧ distinctPointsOnLine c0 c0 C ∧ a ̸ = c0 ∧ |(a--b)| > |(c0--c1)| → ∃ e, between a e b ∧ |(a--e)| = |(c0--c1)", "uris": null}, "FIGREF32": {"fig_num": null, "type_str": "figure", "num": null, "text": "between a e b ∧ |(a--e)| = |(c0--c1)| := by euclid_intros euclid_apply (proposition_2 a c0 c1 C) as d euclid_apply (circle_from_points a d) as DEF euclid_apply (intersection_circle_line_between_points DEF AB a b) as e euclid_apply (point_on_circle_onlyif a d e DEF) euclid_apply between_if a e b use e euclid_finish Proposition 5, GPT-4. Natural language proof: \"For isosceles triangles, the angles at the base are equal to one another, and if the equal sides are produced then the angles under the base will be equal to one another. Let ABC be an isosceles triangle having the side AB equal to the side AC, and let the straight-lines BD and CE have been produced in a straight-line with AB and AC (respectively) [Post. 2]. I say that the angle ABC is equal to ACB, and (angle) CBD to BCE. For let the point F have been taken at random on BD, and let AG have been cut off from the greater AE, equal to the lesser AF [Prop. 1.3]. Also, let the straight-lines F C and GB have been joined [Post. 1]. In fact, since AF is equal to AG, and AB to AC, the two (straight-lines) F A, AC are equal to the two (straight-lines) GA, AB, respectively. They also encompass a common angle, F AG. Thus, the base F C is equal to the base GB, and the triangle AF C will be equal to the triangle AGB, and the remaining angles subtended by the equal sides will be equal to the corresponding remaining angles [Prop. 1.4]. (That is) ACF to ABG, and AF C to AGB. And since the whole of AF is equal to the whole of AG, within which AB is equal to AC, the remainder BF is thus equal to the remainder CG [C.N. 3].", "uris": null}, "FIGREF33": {"fig_num": null, "type_str": "figure", "num": null, "text": "def prop5_prediction : ∀ (a b c d e : Point) (AB BC AC : Line), formTriangle a b c AB BC AC ∧ |(a--b)| = |(a--c)| ∧ between a b d ∧ between a c e → ∠a:b:c = ∠a:c:b ∧ ∠c:b:d = ∠b:c:e := by euclid_intros euclid_apply (exists_point_between_points_on_line AB b d) as f euclid_apply (proposition_3 e a a f AC AB) as g euclid_apply (line_from_points f c) as FC euclid_apply (line_from_points g b) as GB euclid_apply proposition_4 f a c g a b FC AC GB AB AC AB euclid_apply (between_if f b a) euclid_apply (between_if g c a) euclid_apply (proposition_4 b f c c g b AB FC BC GB BC AB) use f, g, FC, GB euclid_finish", "uris": null}, "FIGREF34": {"fig_num": null, "type_str": "figure", "num": null, "text": "def prop5_repaired : ∀ (a b c d e : Point) (AB BC AC : Line), formTriangle a b c AB BC AC ∧ |(a--b)| = |(a--c)| ∧ between a b d ∧ between a c e → ∠a:b:c = ∠a:c:b ∧ ∠c:b:d = ∠b:c:e := by euclid_intros euclid_apply (point_between_points_shorter_than AB b d (c--e)) as f euclid_apply (proposition_3 a e f a AC AB) as g euclid_apply (line_from_points f c) as FC euclid_apply (line_from_points b g) as GB euclid_apply proposition_4 a f c a g b AB FC AC AC GB AB euclid_apply (between_if f b a) euclid_apply (between_if g c a) euclid_apply (proposition_4 f b c g c b AB BC FC AC BC GB) euclid_finish Proposition 7, GPT-4. Natural language proof: On the same straight-line, two other straight-lines equal, respectively, to two (given) straight-lines (which meet) cannot be constructed (meeting) at a different point on the same side (of the straight-line), but having the same ends as the given straight-lines. For, if possible, let the two straight-lines AC, CB, equal to two other straight-lines AD, DB, respectively, have been constructed on the same straight-line AB, meeting at different points, C and D, on the same side (of AB), and having the same ends (on AB). So CA is equal to DA, having the same end A as it, and CB is equal to DB, having the same end B as it. And let CD have been joined [Post. 1]. Therefore, since AC is equal to AD, the angle ACD is also equal to angle ADC [Prop. 1.5]. Thus, ADC (is) greater than DCB [C.N. 5]. Thus, CDB is much greater than DCB [C.N. 5]. Again, since CB is equal to DB, the angle CDB is also equal to angle DCB [Prop. 1.5]. But it was shown that the former (angle) is also much greater (than the latter). The very thing is impossible. Thus, on the same straight-line, two other straight-lines equal, respectively, to two (given) straight-lines (which meet) cannot be constructed (meeting) at a different point on the same side (of the straight-line), but having the same ends as the given straight-lines. (Which is) the very thing it was required to show. Autoformalized Proof: def prop7_prediction : ∀ (a b c d : Point) (AB AC CB AD DB CD : Line), distinctPointsOnLine a b AB ∧ distinctPointsOnLine a c AC ∧ distinctPointsOnLine c b CB ∧ distinctPointsOnLine a d AD ∧ distinctPointsOnLine d b DB ∧ Point.sameSide c d AB ∧ distinctPointsOnLine c d CD ∧ Point.sameSide a b CD ∧ Point.sameSide d b AC ∧ |(a --c)| = |(a--d)| ∧ |(c--b)| = |(d--b)| → False := by euclid_intros euclid_apply (line_from_points c d) as CD euclid_apply proposition_5 c a d AC AD CD euclid_apply proposition_5 c b d CB DB CD by_cases (∠c:a:d) > (∠ d:c:b) . euclid_apply proposition_5' c a d b AC AD CD AB euclid_finish . by_cases (∠ c:b:d) > (∠ d:c:b) . euclid_apply proposition_5' c b d a CB DB CD AB euclid_finish . euclid_finish", "uris": null}, "FIGREF35": {"fig_num": null, "type_str": "figure", "num": null, "text": "def prop7_repaired : ∀ (a b c d : Point) (AB AC CB AD DB CD : Line), distinctPointsOnLine a b AB ∧ distinctPointsOnLine a c AC ∧ distinctPointsOnLine c b CB ∧ distinctPointsOnLine a d AD ∧ distinctPointsOnLine d b DB ∧ Point.sameSide c d AB ∧ distinctPointsOnLine c d CD ∧ Point.sameSide a b CD ∧ Point.sameSide d b AC ∧ |(a--c)| = |(a--d)| ∧ |(c--b)| = |(d--b)| → False := by euclid_intros euclid_apply line_from_points c d as CD' euclid_apply proposition_5' a c d AC CD AD euclid_apply proposition_5' b c d CB CD DB by_cases (∠ c:a:d) > (∠ d:c:b) . euclid_apply proposition_5' c a d AC AD CD euclid_finish . by_cases (∠ c:b:d) > (∠ d:c:b) . euclid_apply proposition_5' c b d CB DB CD euclid_finish . euclid_finish", "uris": null}, "FIGREF36": {"fig_num": null, "type_str": "figure", "num": null, "text": "def prop18_prediction : ∀ (a b c : Point) (AB BC AC : Line), formTriangle a b c AB BC AC ∧ (|(a --c)| > |(a--b)|) → (∠ a:b:c > ∠ b:c:a):= by euclid_intros euclid_apply proposition_3 a c a b AC AB as d euclid_apply line_from_points b d as BD euclid_apply proposition_16 a b d c AB BD AC euclid_apply proposition_5 a b d AB BD as H1 euclid_apply proposition_6 a b d AB BD AC euclid_finish", "uris": null}, "FIGREF37": {"fig_num": null, "type_str": "figure", "num": null, "text": "def prop18_repaired : ∀ (a b c : Point) (AB BC AC : Line), formTriangle a b c AB BC AC ∧ (|(a --c)| > |(a--b)|) → ((∠ a:b:c) > ∠ b:c:a):= euclid_intros euclid_apply proposition_3' a c a b AC AB as d euclid_apply line_from_points b d as BD euclid_apply proposition_16 b c d a BC AC BD euclid_apply proposition_5' a b d AB BD AC euclid_apply proposition_6 a b d AB BD AC euclid_finish Proposition 34, GPT-4V. Natural language proof: \"In parallelogrammic figures the opposite sides and angles are equal to one another, and a diagonal cuts them in half. Let ACDB be a parallelogrammic figure, and BC its diagonal. I say that for parallelogram ACDB, the opposite sides and angles are equal to one another, and the diagonal BC cuts it in half. For since AB is parallel to CD, and the straight-line BC has fallen across them, the alternate angles ABC and BCD are equal to one another [Prop. 1.29]. Again, since AC is parallel to BD, and BC has fallen across them, the alternate angles ACB and CBD are equal to one another [Prop. 1.29].", "uris": null}, "FIGREF38": {"fig_num": null, "type_str": "figure", "num": null, "text": "def prop34_prediction : ∀ (a b c d : Point) (AB CD AC BD BC : Line), formParallelogram a b c d AB CD AC BD ∧ distinctPointsOnLine b c BC → |(a--b)| = |(c--d)| ∧ |(a--c)| = |(b--d)| ∧ ∠ a:b:d = ∠ a:c:d ∧ ∠ b:a:c = ∠ c:d:b ∧ Triangle.area △ a:b:c = Triangle.area △ d:c:b := by euclid_intros euclid_apply proposition_29 a b c d AB CD AC BD euclid_apply proposition_29 c d a b CD AB BD AC euclid_apply proposition_26 a b c d a c b d AB BC AC BD AB CD euclid_apply proposition_4 a b c d c b AB BC AC BD CD BC euclid_finish", "uris": null}, "FIGREF39": {"fig_num": null, "type_str": "figure", "num": null, "text": "def prop34_prediction : ∀ (a b c d : Point) (AB CD AC BD BC : Line), formParallelogram a b c d AB CD AC BD ∧ distinctPointsOnLine b c BC → |(a--b)| = |(c--d)| ∧ |(a--c)| = |(b--d)| ∧ ∠ a:b:d = ∠ a:c:d ∧ ∠ b:a:c = ∠ c:d:b ∧ Triangle.area △ a:b:c = Triangle.area △ d:c:b := by euclid_intros euclid_apply proposition_29''' a d b c AB CD BC euclid_apply proposition_29''' a d c b AC BD BC euclid_apply proposition_26 a b c d c b AB BC AC CD BC BD euclid_apply (proposition_4 b a c c d b AB AC BC CD BD BC) euclid_finish F.4. Autoformalized Proofs from UniGeo Here, we showcase two examples of correctly autoformalized proofs from the UniGeo dataset. Congruent/Thm07, GPT-4V, 5-shot. The diagrammatic input for this theorem is shown in Fig. E. The theorem asserts that if |W X| = |ZY | and W X is parallel to ZY , then triangle W Y Z is congruent to triangle Y W X. The UniGeo proof makes the following inferences:W X ∥ XY ∧ |W X| = |Y Z| ∴ ∠W Y Z = ∠XW Y |W Y | = |W Y | ∴△W Y Z ∼ = △Y W XGPT-4V correctly translates this semiformal proof to LeanEuclid as follows:", "uris": null}, "FIGREF40": {"fig_num": null, "type_str": "figure", "num": null, "text": "Figure E. Diagrammatic input for UniGeo/Congruent/Thm07", "uris": null}, "FIGREF41": {"fig_num": null, "type_str": "figure", "num": null, "text": "Figure F. Diagrammatic input for UniGeo/Parallel/Thm06", "uris": null}, "TABREF0": {"type_str": "table", "html": null, "num": null, "text": "To implement few-shot learning, we randomly selected five propositions from Euc<PERSON>'s Elements and five problems from each category in the UniGeo dataset, serving as in-context learning examples. We then use E3 to automatically evaluate the results of each round. To see how well E3 correlates with human evaluation, we manually evaluate a sample of formalized theorems from Elements to identify any false negatives/positives.", "content": "<table><tr><td/><td/><td>GPT-4</td><td/><td>GPT-4V</td></tr><tr><td>Dataset</td><td colspan=\"5\">0-shot 1-shot 5-shot 0-shot 1-shot 5-shot</td></tr><tr><td colspan=\"2\">Elements 2.3%</td><td colspan=\"2\">4.7% 16.3% 2.3%</td><td colspan=\"2\">4.7% 20.9%</td></tr><tr><td>UniGeo</td><td>3.0%</td><td colspan=\"4\">9.0% 20.0% 5.0% 10.0% 21.0%</td></tr><tr><td>Overall</td><td>2.8%</td><td colspan=\"2\">7.7% 18.9% 4.2%</td><td colspan=\"2\">8.4% 21.0%</td></tr><tr><td colspan=\"6\">Table 1. Percentage of proved semantic equivalences</td></tr><tr><td colspan=\"6\">from autoformalized theorem statements from Ele-</td></tr><tr><td colspan=\"3\">ments and UniGeo.</td><td colspan=\"3\">Experiments were conducted in</td></tr><tr><td>January</td><td>2024</td><td>using</td><td colspan=\"2\">gpt-4-1106-preview</td><td>and</td></tr><tr><td colspan=\"4\">gpt-4-1106-vision-preview.</td><td/></tr></table>"}, "TABREF2": {"type_str": "table", "html": null, "num": null, "text": "and <PERSON>, <PERSON><PERSON>: Unifying geometry logical reasoning via reformulating mathematical expression. In Conference on Empirical Methods in Natural Language Processing (EMNLP), 2022. <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, H. <PERSON>. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Evaluating large language models trained on code. arXiv preprint arXiv:2107.03374, 2021b. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, C. NL2TL: Transforming natural languages to temporal logics using large language models. In Conference on Empirical Methods in Natural Language Processing (EMNLP), 2023. <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, D. Data-efficient learning of natural language to linear temporal logic translators for robot task specification. In International Conference on Robotics and Automation (ICRA), 2023.<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, W.-J. BLEU: a method for automatic evaluation of machine translation.In Annual Meeting of the Association for Computational Linguistics (ACL), 2002.<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>DRL: A self-learning framework for geometry problem solving using reinforcement learning in deductive reasoning. In Findings of the Association for Computational Linguistics: ACL, 2023.", "content": "<table/>"}, "TABREF3": {"type_str": "table", "html": null, "num": null, "text": "Axioms Used by the Symbolic Engine. Our symbolic reasoning engine (Sec. 3.3) is based on SMT solvers. Therefore, it always has access to all built-in theories in SMT, e.g., the theory of real numbers. When used for diagrammatic reasoning, it can also use all diagrammatic rules, metric rules, and transfer rules. When used for equivalence checking, it additionally has access to nine simple construction rules: arbitrary point, distinct points, line nonempty, exists point between points on line, distinct point same side, exists point opposite, exists point on circle, line from points, and intersection lines.", "content": "<table/>"}, "TABREF4": {"type_str": "table", "html": null, "num": null, "text": "[Prop. 1.5]. Thus, DF G (is) greater than EGF . Thus, EF G is much greater than EGF . And since triangle EF G has angle EF G greater than EGF , and the greater angle is subtended by the greater side[Prop. 1.19], side EG (is) thus also greater than EF . But EG (is) equal to BC. Thus, BC (is) also greater than EF .", "content": "<table/>"}, "TABREF5": {"type_str": "table", "html": null, "num": null, "text": ").mpr aβ exact ⟨c, d, L, aL, bL, cdL, EqTri_of_length_online ab aL bL cdL.1 ab_ac bc_ba,", "content": "<table><tr><td>assert (Cong C A A C) by (conclude cn_equalityreverse).</td></tr><tr><td>assert (Cong C A A B) by (conclude lemma_congruencetransitive).</td></tr><tr><td>assert (Cong A C C A) by (conclude cn_equalityreverse).</td></tr><tr><td>assert (Cong A C A B) by (conclude lemma_congruencetransitive).</td></tr><tr><td>contradict.</td></tr><tr><td>}</td></tr><tr><td>assert (˜BetS A B C).</td></tr><tr><td>{</td></tr><tr><td>intro.</td></tr><tr><td>assert (˜Cong A B A C) by (conclude lemma_partnotequalwhole).</td></tr><tr><td>assert (Cong A B C A) by (conclude lemma_congruencetransitive).</td></tr><tr><td>assert (Cong C A A C) by (conclude cn_equalityreverse).</td></tr><tr><td>assert (Cong A B A C) by (conclude lemma_congruencetransitive).</td></tr><tr><td>contradict.</td></tr><tr><td>}</td></tr><tr><td>assert (˜BetS B A C).</td></tr><tr><td>{</td></tr><tr><td>intro.</td></tr><tr><td>assert (˜Cong B A B C) by (conclude lemma_partnotequalwhole).</td></tr><tr><td>assert (Cong B A A B) by (conclude cn_equalityreverse).</td></tr><tr><td>assert (Cong B A B C) by (conclude lemma_congruencetransitive).</td></tr><tr><td>contradict.</td></tr><tr><td>}</td></tr><tr><td>assert (˜Col A B C).</td></tr><tr><td>{</td></tr><tr><td>intro.</td></tr><tr><td>assert (neq A C) by (conclude lemma_inequalitysymmetric).</td></tr><tr><td>assert ((eq A B \\/ eq A C \\/ eq B C \\/ BetS B A C \\/ BetS A B C \\/ BetS A C B)) by (</td></tr><tr><td>conclude_def Col ).</td></tr><tr><td>contradict.</td></tr><tr><td>}</td></tr><tr><td>assert (Triangle A B C) by (conclude_def Triangle ).</td></tr><tr><td>close.</td></tr><tr><td>Unshelve.</td></tr><tr><td>all: (exact A).</td></tr><tr><td>Qed.</td></tr><tr><td>EqTri_of_length_online ab aL bL cdL.2.1 ab_ad bd_ba⟩</td></tr></table>"}, "TABREF6": {"type_str": "table", "html": null, "num": null, "text": "If you are proving an existentially quantified proposition, you can use the standard Lean tactic ' use <X>' to provide the witness <X> for the quantifier. DO NOT use the tactic 'use' if you are not proving an existentially quantified proposition. You can use standard Lean tactics such as <by_cases>, <cases>, <split_ands> and < constructor> to structure your proof. But, you should not use imperative Lean tactics, such as 'rw' or 'simp'. You should only use the above declarative tactics.", "content": "<table><tr><td>Your response should be exactly of the following form:</td></tr><tr><td>&lt;tac_1&gt;</td></tr><tr><td>&lt;tac_2&gt;</td></tr><tr><td>...</td></tr><tr><td>&lt;tac_n&gt;</td></tr><tr><td>where each &lt;tac_i&gt; is a Lean tactic adhering to the above guidelines.</td></tr><tr><td>DO * NOT * wrap your answer in markdown syntax, e.g. '''lean &lt;contents&gt; '''. It must be</td></tr><tr><td>simply a Lean tactic script that can be inserted into a proof.</td></tr><tr><td>.. P -&gt; ∃ x . Q(x), this</td></tr><tr><td>tactic instantiates &lt;rule&gt; with &lt;args&gt;, and attempts to prove premise P from the local</td></tr><tr><td>proof context using an SMT solver. If successful, object x and premise Q(x) are added</td></tr><tr><td>to the proof context.</td></tr><tr><td>usage examples:</td></tr><tr><td>euclid_apply point_same_side a L as b</td></tr><tr><td>euclid_apply line_from_points p1 p2 as M</td></tr><tr><td>NOTE: You can only use 'euclid_apply &lt;rule&gt; &lt;args&gt; as &lt;X&gt;' if the rule produces an</td></tr><tr><td>existential. You should not name any propsotions introduced using 'euclid_apply' e,g,</td></tr><tr><td>'euclid_apply &lt;rule&gt; &lt;args&gt; as H1'.</td></tr><tr><td>NOTE: It is very important that * all * non-propositional (i.e., universally quantified)</td></tr><tr><td>arguments are provided to the rule when invoking 'euclid_apply'.</td></tr><tr><td>* euclid_finish *</td></tr><tr><td>Attempts to resolve the proof goal using the current proof context using an SMT solver</td></tr><tr><td>.</td></tr></table>"}, "TABREF7": {"type_str": "table", "html": null, "num": null, "text": "1. Response Format: Present your proof within THREE angle brackets by <<<Your Proof>>>. Do not add any annotations/explanations in your proof. Do not use markdown syntax. 2. You should not use other imperative Lean tactics, such as 'rw' or 'simp'.", "content": "<table><tr><td>You should</td></tr><tr><td>only use the above declarative tactics.</td></tr><tr><td>---End of Instruction ---</td></tr><tr><td>E. Theorem Statement Autoformalization</td></tr><tr><td>E.1. Successful Formalizations</td></tr></table>"}}}}