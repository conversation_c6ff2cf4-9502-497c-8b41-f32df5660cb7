{"paper_id": "Complementary-LIF", "title": "CLIF: Complementary Leaky Integrate-and-Fire Neuron for Spiking Neural Networks", "abstract": "Spiking neural networks (SNNs) are promising brain-inspired energy-efficient models. Compared to conventional deep Artificial Neural Networks (ANNs), SNNs exhibit superior efficiency and capability to process temporal information. However, it remains a challenge to train SNNs due to their undifferentiable spiking mechanism. The surrogate gradients method is commonly used to train SNNs, but often comes with an accuracy disadvantage over ANNs counterpart. We link the degraded accuracy to the vanishing of gradient on the temporal dimension through the analytical and experimental study of the training process of Leaky Integrate-and-Fire (LIF) Neuron-based SNNs. Moreover, we propose the Complementary Leaky Integrate-and-Fire (CLIF) Neuron. CLIF creates extra paths to facilitate the backpropagation in computing temporal gradient while keeping binary output. CLIF is hyperparameter-free and features broad applicability. Extensive experiments on a variety of datasets demonstrate CLIF's clear performance advantage over other neuron models. Furthermore, the CLIF's performance even slightly surpasses superior ANNs with identical network structure and training conditions. The code is available at https://github.com/HuuYuLong/Complementary-LIF.", "pdf_parse": {"paper_id": "Complementary-LIF", "abstract": [{"text": "Spiking neural networks (SNNs) are promising brain-inspired energy-efficient models. Compared to conventional deep Artificial Neural Networks (ANNs), SNNs exhibit superior efficiency and capability to process temporal information. However, it remains a challenge to train SNNs due to their undifferentiable spiking mechanism. The surrogate gradients method is commonly used to train SNNs, but often comes with an accuracy disadvantage over ANNs counterpart. We link the degraded accuracy to the vanishing of gradient on the temporal dimension through the analytical and experimental study of the training process of Leaky Integrate-and-Fire (LIF) Neuron-based SNNs. Moreover, we propose the Complementary Leaky Integrate-and-Fire (CLIF) Neuron. CLIF creates extra paths to facilitate the backpropagation in computing temporal gradient while keeping binary output. CLIF is hyperparameter-free and features broad applicability. Extensive experiments on a variety of datasets demonstrate CLIF's clear performance advantage over other neuron models. Furthermore, the CLIF's performance even slightly surpasses superior ANNs with identical network structure and training conditions. The code is available at https://github.com/HuuYuLong/Complementary-LIF.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Spiking Neural Networks (SNNs) (<PERSON><PERSON>, 1997) have captivated the attention of both academic and industrial communities in recent years (<PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2019; Mehonic & Kenyon, 2022; <PERSON><PERSON> et al., 2022) . Drawing inspiration from the biological neuron, SNNs adopt the spiking neuron, like the leaky integrate and fire (LIF) model, utilizing spike-based communication for information transmission (<PERSON><PERSON> et al., 2018) . This fundamental characteristic equips SNNs with the capacity to effectively process information across both temporal and spatial dimensions, excelling in areas of low latency and low power consumption (<PERSON><PERSON> et al., 2022) . Compared to conventional deep Artificial Neural Networks (ANNs), SNNs exhibit superior efficiency and capability to process temporal information, presenting significant implementation potential in edge device applications for real-time applications (<PERSON> et al., 2019; Mehonic & Kenyon, 2022; <PERSON> et al., 2024) .", "section": "Introduction", "sec_num": "1."}, {"text": "Despite the advantages of SNNs, the training of SNNs presents a substantial challenge due to the inherently undifferentiable spiking mechanism. Many scholars have intensively explored this problem, three mainstream training methods have been proposed: the bio-inspired training method (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018) , the ANN-to-SNN conversion method (<PERSON><PERSON> <PERSON>, 2020) and the surrogate gradient (SG) method (<PERSON> et al., 2021a; <PERSON> et al., 2022b; <PERSON> et al., 2023) . The bio-inspired training method bypasses undifferentiable problems by calculating the gradients with respect to the spike time (<PERSON> et al., 2018; <PERSON> et al., 2023) . The ANN-to-SNN method utilizes pre-trained ANN models to approximate the ReLU function with the spike neuron model (<PERSON> et al., 2021a; <PERSON> et al., 2023) . The SG method uses surrogate gradients to approximate the gradients of non-differentiable spike functions during backpropagation (<PERSON><PERSON><PERSON><PERSON> et al., 2019) . This method solves the problem of non-differentiable spike functions, facilitating the direct trainable ability of SNNs (<PERSON> et al., 2023) .", "section": "Introduction", "sec_num": "1."}, {"text": "Each method is attractive in certain aspects but also process certain limitations. SG and ANN-to-SNN methods provide great applicability across various neural network architectures, such as spike-driven MLP (<PERSON> et al., 2022) , SRNN (<PERSON> & Li, 2021) , SCNN (<PERSON> et al., 2021a) and Transformer backbone (<PERSON> et al., 2022; <PERSON> et al., 2024) . In contrast, bio-inspired training is challenging to be effectively applied to deeper network configurations (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018) . SG can reach satisfactory performance within limited timestep, whereas ANN-to-SNN requires a large number of timestep and more spikes to achieve comparable accuracy to the network trained by the SG method (<PERSON><PERSON> et al., 2020) . As such, SG-based SNNs are more attractive in edge scenarios where the inference power is critical (<PERSON> et al., 2019) . Nevertheless, the SG method necessitates the use of inaccurate approximations for computing the gradients, leading to imprecise gradient update values and thus diminishing accuracy (<PERSON> et al., 2023) .", "section": "Introduction", "sec_num": "1."}, {"text": "In this study, we rethink the SNN training process and introduce complementary leaky integrate and fire (CLIF) neuron. The LIF and CLIF neuron model is illustrated in Figure.1(a) and (b). We introduce a complementary membrane potential (m[t] ) in CLIF neuron. The complementary potential captures and maintains information related to the decay of the membrane potential. CLIF creates extra paths to facilitate the data flow in temporal gradient computation, as intuitively seen in Figure .1 (c) and (d). Our experiments demonstrate that for SNNs with vanilla LIF neurons, employing a limited number of temporal gradients can yield comparable accuracy to those achieved by using gradients across much more temporal steps. Our theoretical analysis reveals such limitation is linked to the vanishing of certain temporal gradients. Experiments show CLIF can boost the SNN performance significantly in both static images and dynamic event streams. Impressively, even with moderate timestep to keep SNN's low power advantage, CLIF-based SNN achieves comparable or even superior performance to ANN counterpart with identical network structure and training conditions. Our main contributions are: ", "section": "Introduction", "sec_num": "1."}, {"text": "In SG method, gradients of non-differentiable spike functions are approximated by some surrogate gradients during backpropagation, this method enables SNNs to be trained directly by BPTT (<PERSON><PERSON><PERSON>, 1990) . However, the inaccurate approximations for computing the gradients cause imprecise gradient update (<PERSON> et al., 2023) , and degradation in accuracy. Moreover, as the BPTT method requires iteration of recursive computation over timestep, the training cost grows substantially over large timestep (<PERSON> et al., 2018) .", "section": "Related Work", "sec_num": "2."}, {"text": "To improve the accuracy of the SG method, many efforts have been made. Some studies have advanced the surrogate functions. ASGL method (<PERSON> et al., 2023) introduced an adaptive smoothing gradient to reduce gradient noise.", "section": "Related Work", "sec_num": "2."}, {"text": "LocalZO (<PERSON><PERSON><PERSON><PERSON> et al., 2023) proposed the zeroth-order method to estimate the gradients for neuron. SML method (<PERSON><PERSON> et al., 2023) introduces ANNs module to reduce the gradient noise accumulation when training. Alternatively, enhanced neuron dynamics could also yield in higher SNNs accuracy. For example, PLIF (<PERSON> et al., 2021b) , LTMD (<PERSON> et al., 2022a) and GLIF (<PERSON> et al., 2022) introduced learnability in membrane potential, neuron threshold, and different channels, respectively. Nevertheless, even with those efforts, there is still a performance gap between SNNs and ANNs when implemented with identical network architecture. To enhance the training efficiency, several efficient training methods have been proposed. For instance, e-prop (<PERSON><PERSON> et al., 2020) entirely discards the temporal gradients, and only uses the gradients of spatial dimension for training. SLTT (<PERSON><PERSON> et al., 2023 ) also discards the gradient of the temporal dimension, but randomly chooses a few gradient paths along the spatial dimension. Surprisingly, even after discarding the gradients in the temporal dimension, these methods still obtain comparable performance to the original BPTT approach. We investigate further such counterintuitive phenomena through experiments and conclude the temporal gradient decays too rapidly over multiple timesteps. Details about this observation are given in methods.", "section": "Related Work", "sec_num": "2."}, {"text": "To tackle the rapid temporal gradient decay in SNNs, (<PERSON><PERSON> Rezaabad & Vish<PERSON>, 2020) and (<PERSON> et al., 2024) proposed spiking LSTM and spiking ConvLSTM in respectively. Spiking (Conv)LSTM inherits LSTM's advantage and avoids rapid temporal gradient decay. However, Spiking (Conv)LSTM comes with a significant number of training parameters compared to LIF within each neuron, complicating the network structuring and increasing training effort. Moreover, Spiking (Conv)LSTM restricts the neuron from the critical operation of decay and reset. (<PERSON><PERSON><PERSON><PERSON> et al., 2022a; 2023) proposed spikeGRU preserves the reset process of spike neuron. The SpikeGRU also inherits the gating mechanism of GRU to avoid fast temporal gradient decay, and still keep the number of training parameters. (<PERSON> et al., 2024) increased the parallel connection with trainable parameters between the spiking neurons to learn the long-term dependencies. However, this method also restricts the neuron from reset operation and increases the computation complexity. As such, both methods lose the generosity of SNNs and dilute the high efficiency and low power consumption advantage of SNNs.", "section": "Related Work", "sec_num": "2."}, {"text": "In parallel, several bio-inspired models have been developed, transitioning from biological concepts to neuronal model implementations, with the goal of addressing longterm dependency learning issues. For example, the AHP neuron (<PERSON> et al., 2022) inspired by after-hyperpolarizing currents, the TC-LIF model (<PERSON> et al., 2024) inspired by the <PERSON><PERSON>sky-<PERSON><PERSON>zel pyramidal neuron and the ELM model (<PERSON><PERSON><PERSON> et al., 2023) inspired by the cortical neuron. However, few works demonstrate the potential to apply bioinspired neuron models on large and complex networks. In summary, the methods to improve the temporal gradients not only add significant training complexity but also cannot be generalized to various network backbones.", "section": "Related Work", "sec_num": "2."}, {"text": "The specific notations used in this paper are described in the Appendix.A.", "section": "Preliminary", "sec_num": "3."}, {"text": "In the field of SNNs, the most common neuron model is the Leaky Integrate-and-Fire (LIF) model with iterative expression, as detailed in (<PERSON> et al., 2018) . At each time step t, the neurons in the l-th layer integrate the postsynaptic current c l [t] with their previous membrane potential u l [t -1], the mathematical expression is illustrated in Eq.(1):", "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "EQUATION", "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "where τ is the membrane time constant. τ > 1 as the discrete step size is 1. The postsynaptic current c l [t] = W l * s l-1 [t] is calculated as the product of weights W l and spikes from the preceding layer s l-1 [t], simulating synaptic functionality, with * indicating either a fully connect or convolution's synaptic operation.", "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "Neurons will generate spikes s l [t] by Heaviside function when membrane potential u l [t] exceeds the threshold V th , as shown in Eq.(2):", "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "EQUATION", "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "After the spike, the neuron will reset its membrane potential. Two ways are prominent in Eq.(3):", "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "EQUATION", "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "In this work, we chose the soft reset process because it will keep more temporal information (<PERSON><PERSON> et al., 2023) .", "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "In the SG method, gradients are computed through BPTT (<PERSON> et al., 2018) . This involves considering the temporal dimension, where the gradients at l-th layer for all timestep T are calculated as Eq.( 4):", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "where L represents the loss function. We define the ∂L", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "∂u l [t]", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "as the gradient error in this paper, the gradient error can be evaluated recursively:", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "where the ϵ l [t] for LIF model can be defined as follows in Eq.( 6):", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "In particular, for different layers, we have Eq.( 7):", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "where the ∂u l+1 [t]", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "∂s l [t]", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "= (W l+1 ) ⊤ . The detailed derivations can be found in the Appendix.B. In addition, the non-differentiable problem is solved by approximating", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "∂s l [t] ∂u l [t] ≈ H u l [t]", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "with the surrogate function H(•) (<PERSON><PERSON><PERSON><PERSON> et al., 2019) . In this work, we chose the rectangle function (<PERSON> et al., 2019; <PERSON> et al., 2023) :", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "where 1(•) served as the indicator function. Following (<PERSON><PERSON> et al., 2023) , the hyperparameter α is set to V th . In this case, Eq.( 6) can be rewritten as:", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "where γ ≜ 1 -1 τ , resulting γ ∈ (0, 1). Theoretical Analysis: Figure.2 reveals LIF's limitation of exploiting temporal information over a long period. This phenomenon is further investigated analytically. We separate the gradients into spatial", "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Method", "sec_num": "4."}, {"text": "where P l [t] and T l [t, t ′ ] can be further expanded as:", "section": "Method", "sec_num": "4."}, {"text": "EQUATION", "section": "Method", "sec_num": "4."}, {"text": "EQUATION", "section": "Method", "sec_num": "4."}, {"text": "where the t ′ ∈ [t+1, T ]. By substituting Eq.( 9) into Eq.( 12), we obtain:", "section": "Method", "sec_num": "4."}, {"text": "EQUATION", "section": "Method", "sec_num": "4."}, {"text": ")", "section": "Method", "sec_num": "4."}, {"text": "The temporal gradient error is a production of two parts. For Part I, When the difference between t ′ and t is substantial, Part I gets very close to zero. The γ is defined as 1-1 τ , t denotes the time constant ∈ [1, + inf). Typically, γ is between 0.09 and 0.5. For example, in (<PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2021; <PERSON> et al., 2022) they set τ = 1.1, 1.3, 1.5, 2.0. For large t ′ -t, γ (t ′ -t) could barely contribute to the ϵ. This could explain our observation in Figure.2(a) .", "section": "Method", "sec_num": "4."}, {"text": "Part II can be expressed as:", "section": "Method", "sec_num": "4."}, {"text": "EQUATION", "section": "Method", "sec_num": "4."}, {"text": ")", "section": "Method", "sec_num": "4."}, {"text": "More in-depth discussions and proofs of this equation are available in the Appendix.D. From Eq.( 14), it can be seen part II has binary values of 0 or 1. More specifically, as long as the neuron fires at least once within the temporal range of (t + 1, T ), part II = 0 and the corresponding temporal gradient error will vanish and cannot contribute to the backpropagation training process. This is an unavoidable issue in vanilla-LIF models.", "section": "Method", "sec_num": "4."}, {"text": "The vanishing of part II is also demonstrated experimentally. For example, in the special case with timestep T = 2, part I equal to γ we could examine the influence of part II and the experiment results are given in Section.5.1.", "section": "Method", "sec_num": "4."}, {"text": "To summarize, Eq.( 12) demonstrates the temporal gradient vanishing due to the vanishing ϵ in two folds: the multiplication of gamma at large t ′ -t and the neuron spike between t ′ and t. We define this as the temporal gradient vanishing problem persists with the vanilla-LIF model.", "section": "Method", "sec_num": "4."}, {"text": "To address the temporal gradient errors vanishing problem, we design the Complementary LIF (CLIF) model in-spired by biological principles (See detailed in Appendix.E).", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "Besides membrane potential, we introduce another state, termed \"Complementary potential\". To maintain the efficiency advantage of SNN as well as the broad applicability of our neuron model, our model contains no learnable parameters.", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "Decay of Complementary membrane potential: Between each timestep, the membrane potential is decayed by", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "1 τ u l [t]", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": ". We design our Complementary potential to compensate for the membrane potential decay as follows:", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "EQUATION", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "We choose the Sigmoid function as σ, As σ ∈ (0, 1) and the Complementary potential also decays. Nevertheless, the more the membrane potential decays, the less the Complementary potential decays. This design aims to preserve the decayed portion of the membrane potential into Complementary membrane potential.", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "Increase of Complementary membrane potential: Within each timestep, the Complementary membrane potential is increased by firing", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "EQUATION", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "If the neuron has fired recently, the membrane potential m l [t] gets larger.", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "Redesign Reset process: We revisit the Vanilla LIF model as defined by equation Eq.( 1)-( 3), focusing particularly on LIF's reset process:", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "EQUATION", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "The redesigned reset process is given in Eq.( 18). Compared to the soft reset in Eq.( 17), each time the neuron fires, the membrane potential is subtracted by another term σ(m l [t]) related to the Complementary potential: ", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "EQUATION", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": ")", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "if u = (1 -1 τ )u pre + c ▷ leaky & integrate s = Θ(u -V th ) ▷ fire m = m pre ⊙ σ 1 τ u + s u pre = u -s ⊙ (V th + σ(m)) ▷ reset m pre = m Return s ▷ spike output", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "Summarizing the above principles, the CLIF model can be derived as following:", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "EQUATION", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "The pseudo-code for the CLIF model is shown in Algorithm.1. The simplicity of CLIF is reflected in the fact that we only add two lines of code to LIF neuron model.", "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "To validate the effectiveness of the CLIF model, we examine the CLIF model through both case studies and theoretical analysis.", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "In the case study, we explore the dynamic properties of both LIF and CLIF models. CLIF features spike frequency adaptation and exhibits a lower firing rate compared to the LIF neuron. This phenomenon is similar to the refractory period or hyperpolarization in the biological neuron (Sanchez-V<PERSON> & McCormick, 2000) . More specifically, when the input spikes get dense, the complementary potential gets high, the reset process gets more substantial, as shown in Eq.( 18). The more detailed dynamic analysis are illustrated in the Appendix.F.", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "In the theoretical Analysis, we separate the gradient error into spatial and temporal components in Eq.( 20). The details of this derivation are given in Appendix.G). This separation demonstrates that CLIF not only contains all temporal gradients in LIF but also contains extra temporal gradient terms. We believe these additional temporal terms contribute to the improved performance of CLIF.", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "EQUATION", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "where P l M [t] and T l M [t] presents the Spatial and Temporal parts of CLIF's Gradient Errors in respectively. Meanwhile, the M1 and M2 indicate that the Temporal term is divided into two parts.", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "Firstly, the spatial term of CLIF's gradient errors P l M [t] in Eq.( 20) is identical to the counterpart in LIF neuron in Eq.( 11). The detailed derivation and proof are given in Appendix.G.", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "Secondly, for the temporal term of CLIF's gradient errors, the first temporal part T l M1 [t, t ′ ] expands as:", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "EQUATION", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "where the ξ l [t] is defined as:", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "EQUATION", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "this term can be simplified to a product involving the constant term γ, the same as Eq.( 13). The issue discussed in Part I of Section.4.1, regarding the vanishing of temporal gradients, also applies here. Where ψ l [t] is non-negative (see Appendix.G). ψ l [t] is defined as:", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "EQUATION", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "Finally, for the other temporal item of CLIF's gradient errors, T l M2 [t, t ′ ], can be expressed as:", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "EQUATION", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "This term indicates that additional Temporal Gradient Errors components are generated. We believe this additional part contributes to the performance improvements. As T l M2 does not decay as fast as ξ over timestep, this phenomenon could be observed in dynamic analysis in the Appendix.F. Therefore, this item of gradient errors contributes to compensate for the vanishing of temporal gradients in LIF, leading to a further reduction in the loss. This assertion is also verifiable in Figure .3(a) in the Experiment. ", "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "To validate the effectiveness of the proposed CLIF neuron, we conduct a set of ablation studies. These studies are designed to evaluate the underlying principles of the CLIF model, to examine the effect of various timestep, and to conduct comparative analyses between the CLIF model and other neuron models. Following the ablation study, we extend our experiments to cover a diverse range of data types, including static image datasets and neuromorphic eventbased datasets. Details on the experimental implementation are provided in the Appendix.I.", "section": "Experiment", "sec_num": "5."}, {"text": "We conduct two experiments to compare LIF and CLIF: the loss of CLIF versus LIF via training epochs, and the accuracy of CLIF versus LIF via timestep. For a fair comparison, except for the control variable, the same optimizer setting, random seed, architecture, loss function, weight initialization and all hyperparameters are employed. We extend the loss comparison to various tasks and network backbones (see Appendix.H). In all experiments, CLIF neuron's loss converges faster than LIF's, the converged loss is also lower. As such, one can conclude that CLIF neurons are more effective in capturing error information both precisely and efficiently, suggesting the higher training accuracy and efficiency of CLIF. ", "section": "Ablation and Analysis", "sec_num": "5.1."}, {"text": "We conduct two sets of comparison experiments to ascertain the effectiveness of CLIF: comparison with Different Neurons, and comparison with SOTA methods.", "section": "Comparison and Quantitative analysis", "sec_num": "5.2."}, {"text": "In order to verify whether CLIF is more effective than existing methods, we self-implement and compare vanilla-LIF (<PERSON> et al., 2018) , PLIF (<PERSON> et al., 2021b) , KLIF (Jiang & Zhang, 2023) and GLIF (<PERSON> et al., 2022) . Except for the neuron models, all other experimental conditions are kept identical, including the backbone architecture, random seeds and hyperparameters. CLIF exhibits superior performance over other neuron benchmarks in the CIFAR10 and CIFAR100 datasets, as shown in Figure .4(a) and (b). PLIF and GLIF include additional training parameters, so additional hyperparameters tuning and more training epochs are required to converge.", "section": "Comparison with <PERSON> Neuron", "sec_num": null}, {"text": "Moreover, CLIF can achieve slightly better performance with ReLU-based ANNs.", "section": "Comparison with <PERSON> Neuron", "sec_num": null}, {"text": "Comparison with SOTA methods We compare our approach with state-of-the-art methods in two categories of datasets: static dataset (CIFAR10/100 and Tiny-ImageNet), as summarized in Table .1 and neuromorphic dataset (DVS-Gesture and CIFAR10-DVS), as summarized in Table .2. We not only explore the diversity of datasets but also the diversity in network backbone, including ResNet, VGG and Transformer. We also compare the fire rate and energy consumption of LIF, CLIF and ReLU. In short, CLIF has lower fire rate and similar energy consumption as LIF. Detailed statistics of fire rate and power consumption are described in the Appendix.J. ", "section": "Comparison with <PERSON> Neuron", "sec_num": null}, {"text": "In ", "section": "Conclusion", "sec_num": "6."}, {"text": "Throughout the paper and this Appendix, we use the following notations, which mainly follow this work (<PERSON> et al., 2023) . We follow the conventions representing vectors and matrices with bold italic letters and bold capital letters respectively, such as s and W . For this symbol W ⊤ represents transposing the matrix. For a function f (x) : R d1 → R d2 , we use ∇ x f instead of ∂f ∂x to represent the 1 th derivative gradients of f with respect to the variable x in the absence of ambiguity. For two vectors u 1 and u 2 , we use u 1 ⊙ u 2 to represent the element-wise product.", "section": "A. Notation in the Paper", "sec_num": null}, {"text": "This section is mainly referenced from (<PERSON> et al., 2018; <PERSON><PERSON> et al., 2023) . Firstly, we recall the LIF model Eq.( 1) -( 3). We then rewrite the LIF model with soft reset mechanism:", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "γ is defined as γ ≡ 1 -1 τ , then we recall the gradient in Eq.( 4)-( 6):", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "where L represents the loss function. For the left part we recursively evaluate:", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "where ϵ l [t] for LIF model can be defined as follows:", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "Proof of the Eq.( 5) and Eq.( 7).", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "Proof. Firstly, we only consider the effect of the temporal dimension in Eq.( 28). When t = T , where Eq.( 28) deduce as:", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": ")", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "When 1 ≤ t < T , with the chain rule, the Eq.( 28) can be further calculated recursively:", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "∂L ∂u l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂u l [t + 1] ϵ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] + ∂L ∂u l [t + 2] ϵ l [t + 1] expansion ϵ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] ϵ l [t] + ∂L ∂u l [t + 2] ϵ l [t + 1]ϵ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] ϵ l [t] + ∂L ∂s l [t + 2] ∂s l [t + 2] ∂u l [t + 2] + ∂L ∂u l [t + 3] ϵ l [t + 2] expansion ϵ l [t + 1]ϵ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] ϵ l [t] + ∂L ∂s l [t + 2] ∂s l [t + 2] ∂u l [t + 2] ϵ l [t + 1]ϵ l [t] + ∂L ∂u l [t + 3] ϵ l [t + 2]ϵ l [t + 1]ϵ l [t] =...... = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] ϵ l [t] + ∂L ∂s l [t + 2] ∂s l [t + 2] ∂u l [t + 2] ϵ l [t + 1]ϵ l [t] + ... + ∂L ∂s l [T ] ∂s l [T ] ∂u l [T ] ϵ l [T -1]ϵ l [T -2]...ϵ l [t]", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": ",", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "(31) after iterative expansion, we can inductively summarize the Eq.( 30) and ( 31) to obtain this formula Eq.( 32):", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": ")", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "Secondly, under the t ∈ [1, T ] situation, we consider the different layer. For last layer, we substitute l = L into Eq.( 32) as:", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": ")", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "For the intermediate layer l = L -1, ..., 1, according to the chain rule, the ∂L ∂u l [t] can be obtained from the previous layer ∂L ∂u l+1 [t] , the Eq.( 32) can be shown in:", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "Finally, combining Eq.( 32)-( 34), we conclude the following equations:", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "where", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "The Eq.( 35) and Eq.( 36) is the same as Eq.( 5) and Eq.( 7).", "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "The network accuracy is influenced by the time constant (τ ) and BPTT timestep (k). ", "section": "C. The Details of Experimental Observation", "sec_num": null}, {"text": "= 𝓟 𝒕 + 𝓣 𝒕+𝟏 … … … … Loss Function 𝓛 … … … … … … … … 𝑲 = 𝟐", "section": "C. The Details of Experimental Observation", "sec_num": null}, {"text": "Eq.( 9) can be substituted into formula Eq.( 12) yields:", "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "In this case, the ϵ still converges to 0 after continuous multiplication. The detailed proof is shown below.", "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "We construct the function", "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "f ϵ (n) = n t=1 ϵ l [t], n = 1, 2, . . ., to proof lim n→+∞ f ϵ (n) = 0.", "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "Proof.", "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "where the γ is a constant and 0 < γ < 1, resulting lim n→+∞ γ n = 0.", "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "In Part II, we further deduce as shown in Eq.( 39), drawing from Eq.( 6). The second term also tends toward zero, influenced by the hyperparameter α. To prevent spatial gradient explosion, α is typically set to be larger than or equal to V th (<PERSON> et al., 2019) . When α > V th , the result is 0 < 1 -V th α < 1, which causes the temporal gradients to converge to zero more quickly due to the continuous product. However, many studies retain the default value of α = V th = 1 (<PERSON><PERSON> et al., 2021) . When α = V th , if the membrane potential is within the range of (V th -α 2 , V th + α 2 ), then 1 -V th α equals. In other words, if a spike is generated (or u ≈ V th ) once within the range of (t + 1, T ), the temporal gradient will be 0 in such cases. This signifies a pervasive challenge with temporal gradients in the vanilla LIF model, persisting even with short timestep.", "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "The design inspiration for CLIF neurons primarily comes from the adaptive learning characteristics observed in the biological nervous system, particularly the mechanisms of neural adaptability and dynamic regulation of membrane potential (<PERSON><PERSON> & <PERSON>, 2003) . In biology, neurons adjust their electrophysiological properties to adapt to different environmental stimuli. This capability is crucial for the effective processing of information by neurons, preventing excessive excitability (<PERSON> & <PERSON>, 2008; <PERSON> et al., 2009) . A key biological mechanism is regulation through the activity of GABAergic neurons, which release GABA onto the postsynaptic membrane of the target neuron, leading to hyperpolarization and inhibition of excessive action potential production (<PERSON><PERSON><PERSON> et al., 2002) .", "section": "<PERSON><PERSON>ailed Discussion on Inspired Biological Principles", "sec_num": null}, {"text": "The CLIF model simulates this hyperpolarization process and the regulation of action potential generation by resetting a greater amount of membrane potential after each firing, attempting to replicate this type of adaptive regulation characteristic of biological neurons in a computational model.", "section": "<PERSON><PERSON>ailed Discussion on Inspired Biological Principles", "sec_num": null}, {"text": "In this section, we first discuss the firing dynamic behavior of the CLIF, and then we discuss the auto-correlation for the membrane potential. Finally, we discuss the dynamic difference between the CLIF and the current-based, adaptive threshold model.", "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "Firstly, we analyze the fire rate and auto-correlation of CLIF according to the same Poisson random input. The firing dynamic behavior under the different timestep for the single CLIF neuron is shown in Figure .7. We can find that compared with LIF neurons, CLIF neuron has extra refractory periods resulting lower fire rate. Secondly, we calculate the auto-correlation function using", "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "R x [k] = 1 N N -1 n=0 x[n] • x[n -k].", "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "The results are shown in Figure .8. The auto-correlation function indicates the degree of correlation between a signal and a delayed version of itself. We can observe that the auto-correlation value of the complementary membrane potential decays slower, and its period is longer. This suggests that CLIF can capture more and longer correlations in the temporal dimension than LIF. Finally, CLIF shares more similarities to the Adaptive Threshold model (<PERSON><PERSON> et al., 2020) than to the Current-Base model (<PERSON> & Ganguli, 2018) . As for synaptic input current for both CLIF and adaptive threshold model take the form of W s[t], different from current-base model", "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "I syn [t] = 1 τ I syn [t -1] + W s[t],", "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "adaptive threshold model uses a latent variable to adjust the neurons' firing thresholds, whereas CLIF uses a latent variable (the complementary membrane potential) to adjust neurons reset strength.", "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "The CLIF model can be rewritten as:", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "where defined γ ≡ 1 -1 τ , then the gradients at l layer is calculated as:", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Where the right part could be deduced as:", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "For the left part, we recursively evaluate:", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "∂L ∂u l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] Spatial Gradients + ∂L ∂u l [t + 1] ϵ l [t] + ∂u l [t + 1] ∂m l [t] ψ l [t]", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Temporal Gradients of Membrane Potential", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "+ ∂L ∂m l [t] ψ l [t]", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Temporal Gradients of Complementary (45)", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "The eligibility, this terminology mainly refers to e-prop (<PERSON><PERSON> et al., 2020) , STBP (<PERSON> et al., 2018) . From the LIF model, the equation can be deduced as:", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "The Complementary will also introduce the ψ l [t]:", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Besides, the Complementary gradient line will introduce the recursively part:", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "To better understand the eligibility in Eq.45 and Eq.48, we can refer to the following Figure . Merging the Eq.45 and Eq.48 as matrix computing process:", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "where the blue part is the original gradient of the LIF neuron, and the other parts are introduced by Complementary.", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Firstly, we first recursively expand the Eq.48:", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "By mathematical induction, we can deduce that:", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Secondly, we recursively expand the Eq.( 45):", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Note that similar items in Eq.( 52) can be merged as:", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Here again, the gradient of the original LIF neuron is plotted in blue. We can intuitively see that the temporal gradient contributions from the Complementary component are more significant than those from LIF. Even in the worst case all of ξ → 0, ∂L ∂m l [t] also provides the sum of all temporal gradients as shown in Figure .(51), like shortcut at temporal dimension.", "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Following Figure .3 we extend the loss comparison to various tasks and network backbones. The results are shown in Figure .10, CLIF neuron's loss converges faster than LIF's, the converged loss is also lower. This tendency demonstrates the advantage of the CLIF neuron model. ", "section": "H. The Loss Comparing between LIF and CLIF", "sec_num": null}, {"text": "Unless otherwise specified or for the purpose of comparative experiments, the experiments in this paper adhere to the following settings and data preprocessing: all our self-implementations use Rectangle surrogate functions with α = V th = 1, and the decay constant τ is set to 2.0. All random seed settings are 2022. For all loss functions, we use the TET (<PERSON><PERSON> et al., 2021) with a 0.05 loss lambda, as implemented in (<PERSON><PERSON> et al., 2023) . The following are the detailed default setups and dataset descriptions. To comprehensively and fairly evaluate the energy consumption (<PERSON><PERSON><PERSON><PERSON> et al., 2022b) , we recalculated and analyzed the energy consumption of the proposed CLIF neuron in more detail in Table 7 . We considered memory read and write operations, as well as the data addressing process, as done in (<PERSON><PERSON><PERSON> et al., 2022) . As shown in Table 7 , the memory accesses are actually the dominant factor in energy consumption for SNN. Although the hidden states of LIF and CLIF contribute significantly to the read and write energy consumption of the membrane potential, the sparsity of spikes also greatly reduces the parameters and synaptic operations. Therefore, the energy consumption of LIF and CLIF is still much lower than that of ANN. The detailed computing process can be found in the open-source code. In addition, it is feasible to train a model using CLIF and subsequently deploy it or inference with LIF. We take pre-trained models of CLIF and LIF (Resnet18 with T=6) to perform inference on the CIFAR10 and CIFAR100 tasks. To compensate for CLIF's enhanced reset process, we employ a hard reset with a bias as a hyperparameter. As can be seen in Table , this approach leads to an inference accuracy that surpasses that of a model directly trained with LIF. ", "section": "I. Experiment Description and Dataset Pre-processing", "sec_num": null}], "back_matter": [{"text": "This work was supported in part by the Young Scientists Fund of the National Natural Science Foundation of China (Grant 62305278), by the Guangzhou Municipal Science and Technology Project under Grant 2023A03J0013 and Grant 2024A04J4535.", "section": "Acknowledgments", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Spiking Neural Networks and Neuromorphic Computing. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.", "section": "Impact Statement", "sec_num": null}, {"text": "Optimizer ", "section": "Dataset", "sec_num": null}, {"text": "The CIFAR-10 and CIFAR-100 datasets (<PERSON><PERSON><PERSON><PERSON> et al., 2009) contain 60,000 32×32 color images in 10 and 100 different classes respectively, with each dataset comprising 50,000 training samples and 10,000 testing samples. We normalize the image data to ensure that input images have zero mean and unit variance. For data preprocessing, we directly follow this work (<PERSON><PERSON> et al., 2023) . We apply random cropping with padding of 4 pixels on each border of the image, random horizontal flipping, and cutout (<PERSON> & Taylor, 2017) . Direct encoding (<PERSON>hi & Roy, 2021) is employed to encode the image pixels into time series, wherein pixel values are applied repeatedly to the input layer at each timestep. For the CIFAR classification task, we use Spiking-Resnet18 as the backbone.Tiny-ImageNet Tiny-ImageNet contains 200 categories and 100,000 64×64 colored images for training, which is a more challenging static image dataset than CIFAR datasets. To augment Tiny-ImageNet datasets, we take the same AutoAugment (<PERSON><PERSON><PERSON> et al., 2019) as used in this work (<PERSON> et al., 2023 ), but we do not adopt Cutout (<PERSON><PERSON><PERSON> & <PERSON>, 2017) . For the Tiny-ImageNet classification task, we use Spiking-VGG13 as the backbone.", "section": "CIFAR-10/100", "sec_num": null}, {"text": "The DVS-CIFAR10 dataset (<PERSON> et al., 2017 ) is a neuromorphic dataset converted from CIFAR-10 using a DVS camera. It contains 10,000 event-based images with pixel dimensions expanded to 128×128. The event-to-frame integration is handled with the SpikingJelly (<PERSON> et al., 2023) framework. We do not apply any data augmentation for DVSCIFAR10 and the Spiking-VGG11 is used as the backbone to compare the performance.DVSGesture The DVS128 Gesture dataset (<PERSON> et al., 2017 ) is a challenging neuromorphic dataset that records 11 gestures performed by 29 different participants under three lighting conditions. The dataset comprises 1,342 samples with an average duration of 6.5 ± 1.7 s and all samples are split into a training set (1208 samples) and a test set (134 samples). We follow the method described in (<PERSON> et al., 2021b) to integrate the events into frames. The event-to-frame integration is handled with the SpikingJelly (<PERSON> et al., 2023) framework. We do not applied any data augmentation for DVSGesture and the Spiking-VGG11 is used as the backbone to compare the performance.", "section": "DVSCIFAR10", "sec_num": null}, {"text": "We calculate the fire rate as well as the energy efficiency of all models for five tasks. As shown in Figure .11, the average fire rate of the CLIF model is lower than that of the LIF model. This lower fire rate results in fewer synaptic operations, as evidenced in Table .6. For the evaluation of energy consumption, we follow the convention of the neuromorphic computing community by counting the total synaptic operations (SOP) to estimate the computation overhead of SNN models and compare it to the energy consumption of the ANN counterpart, as done in (<PERSON> et al., 2022; <PERSON> et al., 2024) . Specifically, the SOP with MAC presented in ANNs is constant given a specified structure. However, the SOP in SNN varies with spike sparsity. For SNNs, since the input is binary, the synaptic operation is mostly accumulation (ACs) instead of multiply and accumulation (MACs). ACs is defined aswhere fan-out f l i is the number of outgoing connections to the subsequent layer, and N l is the neuron number of the l-th layer. For ANNs, the similar synaptic operation MACs with more expensive multiply-accumulate is defined as:Here, we select all the testing datasets and estimate the average SOP for SNNs. Meanwhile, we measure 32-bit floating-point ACs by 0.9 pJ per operation and 32-bit floating-point MAC by 4.6 pJ per operation, as done in (<PERSON> et al., 2015) . All the results are summarized in the Table .6, SNN has a significant energy consumption advantage over ANNs. Notably, the ACs operation of CLIF are considerably less than those of LIF, attributable to the lower fire rate. In contrast, the MAC operations of CLIF exceed those of LIF due to the increased number of floating-point operations, a result of the Complementary component introduced in CLIF. The final results indicate that CLIF achieves comparable performance to ANNs models while maintaining similar total energy efficiency to LIF.", "section": "J. Evaluation of Fire Rate and Energy Consumption", "sec_num": null}], "ref_entries": {"FIGREF0": {"type_str": "figure", "text": "Figure 1. (a) Illustration of the LIF neuron model with forward propagation data flow (b) Illustration of the CLIF neuron model with forward propagation data flow (c) Illustration of the LIF's gradient error ∂L ∂u l [t] flow during BPTT. Each path is represented by an arrow. Lighter color in the arrow indicates more decay of gradient error. (d) Illustration of the CLIF's gradient error flow during BPTT. Compared to (c), the additional temporal gradient error is highlighted in red.", "num": null, "fig_num": "1", "uris": null}, "FIGREF1": {"type_str": "figure", "text": "Figure.2(b)  plots the classification accuracy over increasing timestep for both vanilla LIF and our proposed CLIF. The average and standard error are calculated from the results using 4 different random seeds. The accuracy of the vanilla LIF peaks at T = 32 and then declines as the number of timesteps increases. This indicates the temporal gradient from LIF over larger timestep cannot be properly processed. In contrast, the CLIF model demonstrates a sustained improvement of performance over increasing timestep, showcasing CLIF's effectiveness in learning over longer timestep.", "num": null, "fig_num": "22", "uris": null}, "FIGREF2": {"type_str": "figure", "text": "In the first ablation study, we use the Spiking Resnet18 with 6 timestep by BPTT training. The training of the network begins with LIF neuron and later transitions to CLIF neurons at a designated epoch. As shown in Figure.3(a), a few epochs after Exchange to CLIF, the loss decreases significantly compared to LIF. Moreover, the decay of loss over training epochs is much faster when training with CLIF than LIF.", "num": null, "fig_num": null, "uris": null}, "FIGREF3": {"type_str": "figure", "text": "Figure 3. (a) Loss function vs epochs. Each color presents a case of either LIF, CLIF, or exchanging from LIF to CLIF at a given epoch during training. (b) Comparison of the accuracy of LIF and CLIF at various timestep. Both experiments are evaluated on the CIFAR10 task with Spiking ResNet-18.", "num": null, "fig_num": "3", "uris": null}, "FIGREF4": {"type_str": "figure", "text": "Figure 4. Comparative accuracy of Spiking ResNet-18. Panels (a) CIFAR10 using 8 timestep (b) CIFAR100 using 6 timestep with different neuron.", "num": null, "fig_num": "4", "uris": null}, "FIGREF5": {"type_str": "figure", "text": "Figure 5. Illustration of the LIF neuron based SNN's gradient error flow during BPTT. In this example k=2: only the backpropagation from the first two timestep is considered (illustrated by the two red dashed arrows), and backpropagation along further timestep is discarded.", "num": null, "fig_num": "56", "uris": null}, "FIGREF6": {"type_str": "figure", "text": "Figure 7. The dynamic behavior of a single LIF and CLIF neuron at different timestep.", "num": null, "fig_num": "7", "uris": null}, "FIGREF7": {"type_str": "figure", "text": "Figure 8. The autocorrelation of a single LIF and CLIF neuron at different timestep.", "num": null, "fig_num": "8", "uris": null}, "FIGREF8": {"type_str": "figure", "text": "Figure 9. The abstract expression of (a) forward dependency and (b) backward eligibility trace for CLIF neuron.", "num": null, "fig_num": "9", "uris": null}, "FIGREF9": {"type_str": "figure", "text": "Figure 10. Testing loss curves of the training process of LIF-based and CLIF-based for each tasks.", "num": null, "fig_num": "10", "uris": null}, "TABREF2": {"html": null, "content": "<table><tr><td>sub-</td></tr><tr><td>tracted more, suppressing the neuron's firing rate. This</td></tr><tr><td>mechanism achieves spike frequency adaptation, similar</td></tr><tr><td>to the hyper-polarization process in real biological neu-</td></tr><tr><td>rons (McCORMICK &amp; Pape, 1990; Sanchez-Vives &amp; Mc-</td></tr></table>", "type_str": "table", "text": "Core function for CLIF model Input: Input c, Current Time Step t, time constant τ , threshold V th Output: Spike s if t = 0 then Initial u pre and m pre with all zero end", "num": null}, "TABREF3": {"html": null, "content": "<table><tr><td>Dataset</td><td>Method</td><td>Spiking Network</td><td colspan=\"3\">Neuron Model Timestep Accuracy(%)</td></tr><tr><td/><td>ANN / ANN *</td><td>ResNet-18</td><td>ReLU</td><td>1</td><td>95.62 / 96.65</td></tr><tr><td/><td>Dspike (Li et al., 2021b) 'NIPS</td><td>Modified ResNet-18</td><td>LIF</td><td>6</td><td>93.50</td></tr><tr><td/><td>PLIF (Fang et al., 2021b) 'ICCV</td><td>PLIF Net</td><td>PLIF</td><td>6</td><td>94.25</td></tr><tr><td/><td>DSR (Meng et al., 2022) 'CVPR</td><td>RreAct-ResNet-18</td><td>LIF</td><td>20</td><td>95.40</td></tr><tr><td>CIFAR-10</td><td>GLIF (Yao et al., 2022) 'NIPS KLIF (Jiang &amp; Zhang, 2023) 'ArXiv PSN * (Fang et al., 2024) 'NIPS SML (Deng et al., 2023) 'ICML</td><td>ResNet-18 Modified PLIF Net Modified PLIF Net ResNet-18</td><td>GLIF KLIF PSN LIF</td><td>6 10 4 6</td><td>94.88 92.52 95.32 95.12</td></tr><tr><td/><td>ASGL * (Wang et al., 2023) 'ICML</td><td>ResNet-18</td><td>LIF</td><td>4</td><td>95.35</td></tr><tr><td/><td/><td/><td/><td>4</td><td>94.89 / 96.01</td></tr><tr><td/><td>Ours / Ours *</td><td>ResNet-18</td><td>CLIF</td><td>6</td><td>95.41 / 96.45</td></tr><tr><td/><td/><td/><td/><td>8</td><td>95.68 / 96.69</td></tr><tr><td/><td>ANN / ANN *</td><td>ResNet-18</td><td>ReLU</td><td>1</td><td>78.14 / 80.89</td></tr><tr><td/><td>Dspike (Li et al., 2021b) 'NIPS</td><td>Modified ResNet-18</td><td>LIF</td><td>6</td><td>74.24</td></tr><tr><td>CIFAR-100</td><td>DSR (Meng et al., 2022) 'CVPR GLIF (Yao et al., 2022) 'NIPS SML (Deng et al., 2023) 'ICML ASGL * (Wang et al., 2023) 'ICML</td><td>RreAct-ResNet-18 ResNet-18 ResNet-18 ResNet-18</td><td>LIF GLIF LIF LIF</td><td>20 6 6 4 4</td><td>78.50 77.28 78.00 77.74 77.00 / 79.69</td></tr><tr><td/><td>Ours / Ours *</td><td>ResNet-18</td><td>CLIF</td><td>6</td><td>78.36 / 80.58</td></tr><tr><td/><td/><td/><td/><td>8</td><td>78.99 / 80.89</td></tr><tr><td>Tiny-ImageNet</td><td>ANN Online LTL (Yang et al., 2022) 'NIPS Joint A-SNN (Guo et al., 2023) 'Pattern Recognit ASGL (Wang et al., 2023) 'ICML Ours</td><td>VGG-13 VGG-13 VGG-16 VGG-13 VGG-13</td><td>ReLU LIF LIF LIF CLIF</td><td>1 6 4 8 4 6</td><td>59.77 55.37 55.39 56.81 63.16 64.13</td></tr></table>", "type_str": "table", "text": "Comparing the state-of-the-art methods on static image datasets. The asterisk ( * ) indicates the utilization of data augmentation strategies, including auto-augmentation and/or CutMix, our implementation directly following(<PERSON> et al., 2024). The implemented ReLU-based ANN shares identical structures and hyper-parameters with SNN.", "num": null}, "TABREF4": {"html": null, "content": "<table><tr><td>Dataset</td><td>Method</td><td>Spiking Network</td><td colspan=\"3\">Neuron Model T Accuracy(%)</td></tr><tr><td colspan=\"2\">PLIF (<PERSON> et al., 2021b) 'ICCV</td><td>PLIF Net</td><td>PLIF</td><td>20</td><td>97.57</td></tr><tr><td colspan=\"2\">KLIF (Jiang &amp; Zhang, 2023) 'ArXiv</td><td>Modified PLIF Net</td><td>KLIF</td><td>12</td><td>94.10</td></tr><tr><td>DVS-Gesture</td><td>Ours</td><td>Spiking-Vgg11 Spike-Driven-Transformer 1</td><td>LIF CLIF LIF CLIF</td><td>20 16</td><td>97.57 97.92 98.26 99.31</td></tr><tr><td colspan=\"2\">PLIF (Fang et al., 2021b) 'ICCV</td><td>PLIF Net</td><td>PIF</td><td>20</td><td>74.80</td></tr><tr><td colspan=\"2\">KLIF (Jiang &amp; Zhang, 2023) 'ArXiv</td><td>Modified PLIF Net</td><td>KLIF</td><td>15</td><td>70.90</td></tr><tr><td colspan=\"2\">GLIF (Yao et al., 2022) 'NIPS</td><td>7B-wideNet</td><td>GLIF</td><td>16</td><td>78.10</td></tr><tr><td colspan=\"2\">PSN (Fang et al., 2024) 'NIPS</td><td>VGGSNN</td><td>PSN</td><td>10</td><td>85.90</td></tr><tr><td>CIFAR10-DVS</td><td>Ours</td><td>Spiking-Vgg11 VGGSNN 2</td><td>LIF CLIF LIF CLIF</td><td>16 10</td><td>78.05 79.00 84.90 86.10</td></tr><tr><td>6SLNLQJ5HVQHWZLWK7LPHVWHS</td><td/><td/><td/><td/><td/></tr><tr><td>/,)</td><td/><td/><td/><td/><td/></tr><tr><td>&amp;/,)</td><td/><td/><td/><td/><td/></tr><tr><td>/RVV</td><td/><td/><td/><td/><td/></tr><tr><td>(SRFKV</td><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "text": "Comparing the SOTA neuronal models by using neuromorphic datasets. The footnote in the table indicates implementation directly in open source code by only modifying neurons: 1 (<PERSON> et al., 2024), 2 (<PERSON> et al., 2024) with data augmentation. 'T' denotes the number of the timestep employed.", "num": null}, "TABREF6": {"html": null, "content": "<table><tr><td>1, reveals that CLIF always</td></tr><tr><td>outperforms its LIF counterpart and surpasses all other</td></tr><tr><td>SNNs neuron models within the same network backbone.</td></tr><tr><td>CLIF achieves 96.69% accuracy on CIFAR-10 and 80.89%</td></tr><tr><td>on CIFAR-100 datasets, not only outperforming other SNN</td></tr><tr><td>models but also slightly outperforming ReLU-based ANNs</td></tr><tr><td>counterpart. In Tiny-ImageNet, CLIF achieves 64.13% ac-</td></tr><tr><td>curacy with 6 timestep, significantly better than the other</td></tr><tr><td>SNNs and ANN within the same network backbone. These</td></tr><tr><td>results demonstrate CLIF's competitiveness with existing</td></tr><tr><td>neuron models.</td></tr><tr><td>Neuromorphic Datasets To validate that our method can</td></tr><tr><td>handle spatio-temporal error backpropagation properly, we</td></tr><tr><td>conduct experiments on different neuromorphic datasets</td></tr><tr><td>of DVS-Gesture (Amir et al., 2017) and DVSCIFAR10</td></tr><tr><td>(Li et al., 2017). The results are summarized in Table 3.</td></tr><tr><td>For DVS Gesture, CLIF accuracy is 97.92% with Spiking-</td></tr><tr><td>VGG11 as backbone and 99.31% with Spike-Driven Trans-</td></tr><tr><td>former (Yao et al., 2024) as the backbone, surpassing</td></tr><tr><td>LIF based model by 0.35% and 1.05%, respectively. On</td></tr><tr><td>the DVSCIFAR10 dataset, CLIF accuracy is 79.00% with</td></tr><tr><td>Spiking-VGG11 as the backbone and 86.10% with VG-</td></tr><tr><td>GSNN as the backbone, surpassing LIF based model by</td></tr><tr><td>0.95% and 1.20%, respectively. It is worth noting that CLIF</td></tr><tr><td>features the highest accuracy of 86.10% in all methods in</td></tr><tr><td>this dataset. This is achieved by simply replacing the neuron</td></tr><tr><td>model with CLIF in the network architecture.</td></tr></table>", "type_str": "table", "text": "", "num": null}, "TABREF8": {"html": null, "content": "<table><tr><td>Last</td><td/><td/><td/><td/><td/></tr><tr><td>Layer</td><td/><td/><td/><td/><td/></tr><tr><td>𝑾 𝒍+𝟏</td><td/><td/><td/><td/><td/></tr><tr><td>𝒍 Layer 𝝏𝓛 𝓟 𝒕</td><td>𝑺 𝒕 𝑼 𝒕</td><td>∑ 𝓣 𝒕+𝟏</td><td>𝑺 𝒕+𝟏 𝑼 𝒕+𝟏</td><td>… … 𝓣 𝒕+⋯ … …</td><td>𝑺 𝑻 𝓣 𝑻 𝑼 𝑻</td></tr><tr><td>𝝏𝑼 𝒕</td><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "text": "Besides the 5-layer NN in Figure.6, we also evaluate CIFFAR10 with Spiking ResNet18 and DVSGesture with Spiking VGG11. Similar to 5layer SNN, the gradient from further timestep could not contribute to the backpropagation training process, as increasing k above 3 does not substantially enhance the accuracy. The training settings are shown in Table.4:", "num": null}, "TABREF9": {"html": null, "content": "<table><tr><td>k</td><td>τ</td><td>1.1</td><td>1.3</td><td>1.5</td><td>1.8</td><td>2</td><td>k</td><td>τ</td><td>1.1</td><td>1.3</td><td>1.5</td><td>1.8</td><td>2</td></tr><tr><td>1</td><td/><td colspan=\"5\">92.89 90.34 87.27 82.77 80.71</td><td>1</td><td/><td colspan=\"5\">96.88 97.22 95.83 95.49 96.53</td></tr><tr><td>2</td><td/><td colspan=\"5\">93.86 93.91 92.82 91.09 89.07</td><td>4</td><td/><td colspan=\"5\">97.57 97.92 97.22 96.53 96.53</td></tr><tr><td>3</td><td/><td colspan=\"5\">94.24 94.36 94.12 93.31 93.01</td><td>8</td><td/><td colspan=\"5\">97.57 97.57 97.22 97.57 97.22</td></tr><tr><td>4</td><td/><td colspan=\"5\">94.45 94.61 94.64 94.09 93.43</td><td>12</td><td/><td colspan=\"5\">97.22 97.57 97.22 97.22 97.57</td></tr><tr><td>5</td><td/><td colspan=\"5\">94.73 94.69 94.7 94.23 94.06</td><td>16</td><td/><td colspan=\"5\">97.57 97.57 97.92 97.22 97.57</td></tr><tr><td>6</td><td/><td colspan=\"5\">94.80 94.86 94.94 94.36 93.73</td><td>20</td><td/><td colspan=\"5\">97.57 97.57 97.92 97.57 97.22</td></tr></table>", "type_str": "table", "text": "Table.C, and the experiment hyperparameter as shown in Table.4. Left table is the CIFAR10 accuracy performance (%), Right table is the DVS Gesture accuracy performance (%). The random seeds are uniformly fixed across all instances.", "num": null}, "TABREF10": {"html": null, "content": "<table><tr><td>Parameter Datasets</td><td>CIFAR10</td><td>DVS Gesture</td></tr><tr><td>Networks</td><td colspan=\"2\">Spiking ResNet18 Spiking Vgg11</td></tr><tr><td>Time Steps (T)</td><td>6</td><td>20</td></tr><tr><td>Epochs (e)</td><td>200</td><td>300</td></tr><tr><td>Batch Size (bs)</td><td>128</td><td>16</td></tr><tr><td>Optimizer</td><td>SGD</td><td>SGD</td></tr><tr><td>Learning Rate (lr)</td><td>0.1</td><td>0.1</td></tr><tr><td>Weight Decay (wd)</td><td>5 × 10 -5</td><td>5 × 10 -4</td></tr><tr><td>Dropout Rate</td><td>0.0</td><td>0.4</td></tr></table>", "type_str": "table", "text": "Training Parameters", "num": null}, "TABREF12": {"html": null, "content": "<table/>", "type_str": "table", "text": "Training hyperparameters", "num": null}, "TABREF13": {"html": null, "content": "<table><tr><td>CIFAR10 (ResNet18)</td><td>ReLU CLIF LIF</td><td>1 6 6</td><td>11.2 11.2 11.2</td><td>0 68.66 84.86</td><td>557.65 5.12 2.89</td><td>2565.19 85.346 89.668</td></tr><tr><td>CIFAR100 (ResNet18)</td><td>ReLU CLIF LIF</td><td>1 6 6</td><td>11.2 11.2 11.2</td><td>0 55.58 60.28</td><td>557.7 5.16 2.93</td><td>2565.42 73.758 67.73</td></tr><tr><td>Tiny ImageNet (VGG13)</td><td>ReLU CLIF LIF</td><td>1 6 6</td><td>14.4 14.4 14.4</td><td>0 102.1 135.25</td><td>922.56 282.83 278.84</td><td>4243.776 1392.908 1404.389</td></tr><tr><td>DVSGesture</td><td>CLIF</td><td>20</td><td>9.5</td><td>19.16</td><td>1090</td><td>5031.244</td></tr><tr><td>(VGG11)</td><td>LIF</td><td>20</td><td>9.5</td><td>25.09</td><td>1080</td><td>4990.581</td></tr><tr><td>DVSCIFAR10</td><td>CLIF</td><td>10</td><td>9.5</td><td>12.02</td><td>153.87</td><td>718.62</td></tr><tr><td>(VGG11)</td><td>LIF</td><td>10</td><td>9.5</td><td>14.65</td><td>152.5</td><td>714.685</td></tr></table>", "type_str": "table", "text": "The energy consumption of synaptic operation for different tasks with the whole testing datasets.Neuron T Parameters(M) ACs (M) MACs (M) SOP Energy (µJ)", "num": null}, "TABREF14": {"html": null, "content": "<table><tr><td/><td colspan=\"3\">Mem. Read &amp; Write Potential (mJ) Membrane Parameters (mJ) In / Out (mJ)</td><td>Synaptic &amp; Neuron Op. (mJ)</td><td colspan=\"2\">Addr. (µJ) Total (mJ)</td></tr><tr><td/><td>0</td><td>54.9688</td><td>54.9357</td><td>1.7573</td><td>0.1145</td><td>111.6619</td></tr><tr><td>CIFAR10</td><td>22.9987</td><td>11.4994</td><td>0.0013</td><td>0.0190</td><td>12.1394</td><td>34.5304</td></tr><tr><td/><td>55.5172</td><td>9.2529</td><td>0.0007</td><td>0.0389</td><td>19.5184</td><td>64.8293</td></tr><tr><td/><td>0</td><td>54.9735</td><td>54.9357</td><td>1.7574</td><td>0.1145</td><td>111.6667</td></tr><tr><td>CIFAR100</td><td>16.8666</td><td>8.4337</td><td>0.0004</td><td>0.0171</td><td>8.8427</td><td>25.3267</td></tr><tr><td/><td>46.8265</td><td>7.8048</td><td>0.0004</td><td>0.0380</td><td>16.4053</td><td>54.6861</td></tr><tr><td/><td>0</td><td>91.9065</td><td>91.3834</td><td>2.9379</td><td>0.1686</td><td>186.2279</td></tr><tr><td>TinyImagenet</td><td>37.7700</td><td>18.9076</td><td>0.0050</td><td>1.1754</td><td>19.8379</td><td>57.8778</td></tr><tr><td/><td>84.8900</td><td>14.1756</td><td>0.0028</td><td>1.0986</td><td>29.7983</td><td>100.1968</td></tr><tr><td>DVSCIFAR10</td><td>5.7398 14.0891</td><td>2.8701 2.3484</td><td>0.0003 0.0002</td><td>0.0212 0.0451</td><td>2.9340 4.7976</td><td>8.6343 16.4876</td></tr><tr><td>DVSGesture</td><td>13.9212 38.3082</td><td>6.9606 6.3848</td><td>0.0003 0.0003</td><td>0.1682 0.4794</td><td>7.0728 12.9767</td><td>21.0574 45.1856</td></tr></table>", "type_str": "table", "text": "The total energy consumption for different tasks. The neuron and time step are the same as those in Table6.", "num": null}, "TABREF15": {"html": null, "content": "<table><tr><td>CIFAR10</td><td colspan=\"2\">Soft Reset Hard Reset</td><td/><td/><td/><td/><td/></tr><tr><td>Reset Value</td><td>None</td><td>0</td><td>-0.02</td><td>-0.04</td><td>-0.06</td><td>-0.08</td><td>-0.1</td></tr><tr><td>CLIF pretrained (95.41%)</td><td>92.95 %</td><td>93.41 %</td><td colspan=\"5\">94.18 % 94.54 % 95.08 % 94.84 % 94.72 %</td></tr><tr><td>LIF pretrained (94.51%)</td><td>94.51 %</td><td>84.05 %</td><td colspan=\"5\">76.68 % 66.08 % 52.16 % 38.00 % 27.04 %</td></tr><tr><td>CIFAR100</td><td colspan=\"2\">Soft Reset Hard Reset</td><td/><td/><td/><td/><td/></tr><tr><td>Reset Value</td><td>None</td><td>0</td><td>-0.02</td><td>-0.04</td><td>-0.06</td><td>-0.08</td><td>-0.1</td></tr><tr><td>CLIF pretrained (78.36 %)</td><td>68.72 %</td><td>73.04 %</td><td colspan=\"5\">74.64 % 76.63 % 76.55 % 77.00 % 76.54 %</td></tr><tr><td>LIF pretrained (76.23 %)</td><td>76.23 %</td><td>47.74 %</td><td colspan=\"4\">37.04 % 27.56 % 19.83 % 13.22 %</td><td>8.77 %</td></tr></table>", "type_str": "table", "text": "Directly convert the pre-trained CLIF/LIF model to an LIF neuron for inference.", "num": null}}}}