{"paper_id": "Token-level-Direct-Preference-Optimization", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:44:22.692888Z"}, "title": "Token-level Direct Preference Optimization", "authors": [{"first": "Yongcheng", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Guoqing", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "Microsoft Research AI4Science", "institution": "", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>ng", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Haifeng", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": "<<EMAIL>>."}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University College London", "location": {}}, "email": "<<EMAIL>>"}], "year": "", "venue": null, "identifiers": {}, "abstract": "Fine-tuning pre-trained Large Language Models (LLMs) is essential to align them with human values and intentions. This process often utilizes methods like pairwise comparisons and KL divergence against a reference LLM, focusing on the evaluation of full answers generated by the models. However, the generation of these responses occurs in a token level, following a sequential, auto-regressive fashion. In this paper, we introduce Token-level Direct Preference Optimization (TDPO), a novel approach to align LLMs with human preferences by optimizing policy at the token level. Unlike previous methods, which face challenges in divergence efficiency, TDPO incorporates forward KL divergence constraints for each token, improving alignment and diversity. Utilizing the Bradley-Terry model for a token-based reward system, TDPO enhances the regulation of KL divergence, while preserving simplicity without the need for explicit reward modeling. Experimental results across various text tasks demonstrate TDPO's superior performance in balancing alignment with generation diversity. Notably, fine-tuning with TDPO strikes a better balance than DPO in the controlled sentiment generation and single-turn dialogue datasets, and significantly improves the quality of generated responses compared to both DPO and PPO-based RLHF methods. Our code is opensourced at https://github.com/Vance0124/Tokenlevel-Direct-Preference-Optimization.", "pdf_parse": {"paper_id": "Token-level-Direct-Preference-Optimization", "_pdf_hash": "", "abstract": [{"text": "Fine-tuning pre-trained Large Language Models (LLMs) is essential to align them with human values and intentions. This process often utilizes methods like pairwise comparisons and KL divergence against a reference LLM, focusing on the evaluation of full answers generated by the models. However, the generation of these responses occurs in a token level, following a sequential, auto-regressive fashion. In this paper, we introduce Token-level Direct Preference Optimization (TDPO), a novel approach to align LLMs with human preferences by optimizing policy at the token level. Unlike previous methods, which face challenges in divergence efficiency, TDPO incorporates forward KL divergence constraints for each token, improving alignment and diversity. Utilizing the Bradley-Terry model for a token-based reward system, TDPO enhances the regulation of KL divergence, while preserving simplicity without the need for explicit reward modeling. Experimental results across various text tasks demonstrate TDPO's superior performance in balancing alignment with generation diversity. Notably, fine-tuning with TDPO strikes a better balance than DPO in the controlled sentiment generation and single-turn dialogue datasets, and significantly improves the quality of generated responses compared to both DPO and PPO-based RLHF methods. Our code is opensourced at https://github.com/Vance0124/Tokenlevel-Direct-Preference-Optimization.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Large language models (LLMs) (<PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2023) have demonstrated significant generalization capabilities in various domains including text summarization (<PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022) , coding writing (<PERSON> et al., 2021; <PERSON> et al., 2023) , and even following human instructions (<PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022) . In order to align LLMs with human intentions, Reinforcement Learning from Human Feedback (RLHF) (<PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON> et al., 2023) has emerged as a highly effective method, embodying both stylistic and ethical values (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2022) . These approaches typically involve the training of a reward model followed by the fine-tuning of the policy model using reinforcement learning (RL).", "cite_spans": [{"start": 29, "end": 50, "text": "(<PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF0"}, {"start": 51, "end": 71, "text": "<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF5"}, {"start": 178, "end": 201, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF35"}, {"start": 202, "end": 219, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF20"}, {"start": 237, "end": 256, "text": "(<PERSON> et al., 2021;", "ref_id": null}, {"start": 257, "end": 274, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF12"}, {"start": 315, "end": 335, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF9"}, {"start": 336, "end": 356, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF24"}, {"start": 455, "end": 480, "text": "(<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF8"}, {"start": 481, "end": 501, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF24"}, {"start": 502, "end": 520, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF10"}, {"start": 521, "end": 539, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF46"}, {"start": 540, "end": 557, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 644, "end": 662, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF1"}, {"start": 663, "end": 684, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Direct Preference Optimization (DPO) (<PERSON><PERSON><PERSON><PERSON> et al., 2023) introduces a straightforward and effective technique for training LLMs using pairwise comparisons, without the need for explicitly establishing a reward model. DPO utilizes KL divergence to ensure that the training process remains closely aligned with a reference Large Language Model (LLM), preventing significant deviations. In DPO, KL divergence is assessed at the sentence level, reflecting the fact that evaluations are based on complete responses (answers), typically comprising several sentences. However, the generation of these responses occurs sequentially, following an auto-regressive approach. A potential benefit is to examine divergence in relation to a reference LLM on a more granular, token-by-token basis. One approach involves using sequential KL divergence (as defined in Definition 4.3), which monitors the trajectory of the generated responses. As illustrated in Figure 1 , DPO demonstrates a significantly faster increase in KL divergence within the subset of less preferred responses when compared to the subset that is preferred. This results in an expanding gap between the two subsets and also indicates that DPO does not effectively control the KL divergence of the dispreferred response subset. This impacts the model's divergence efficiency and ultimately affects its linguistic capabilities and generative diversity. Such a limitation highlights the decreased effectiveness of employing KL divergence within the DPO framework, suggesting an area for improvement in its methodology. The imbalance in the growth rates of the sequential KL divergence is potentially related to the reverse KL divergence constraint employed by DPO. The mode-seeking property of reverse KL divergence tends to induce diversity reduction during generation, limiting the model's potential to produce diverse and effective responses (Wiher et al., 2022; Khalifa et al., 2020; Glaese et al., 2022; Perez et al., 2022) . Built upon DPO, the f-DPO method (Wang et al., 2023) studies the trade-off between alignment performance and generation diversity of LLMs under different divergence constraints. It highlights the advantages of the mass-covering behavior of forward KL divergence in enhancing model diversity and explores the impact of different divergence constraints. Nevertheless, f-DPO only independently discusses the changes in model behavior under either the reverse KL divergence or the forward KL divergence constraints. Essentially, it does not fundamentally enhance the DPO algorithm itself but rather strikes a balance between alignment performance and generating diversity by simply swapping different KL divergence constraints.", "cite_spans": [{"start": 37, "end": 60, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF28"}, {"start": 1901, "end": 1921, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF43"}, {"start": 1922, "end": 1943, "text": "<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF17"}, {"start": 1944, "end": 1964, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF13"}, {"start": 1965, "end": 1984, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF25"}, {"start": 2020, "end": 2039, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF41"}], "ref_spans": [{"start": 954, "end": 955, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Inspired by the aforementioned observations, we define and examine the problem of aligning with human preferences from a sequential and token-level standpoint. Some concurrent work has also been conducted in this direction (<PERSON><PERSON><PERSON><PERSON> et al., 2024; <PERSON><PERSON> et al., 2024) . We introduce a new method, referred to as Token-level Direct Preference Optimization (TDPO), which aims to strike a better balance between alignment performance and generation diversity by controlling the KL divergence for each token. In order to achieve this, we redefine the objective of maximising restricted rewards in a sequential manner. The connection between sentence-level reward and token-level generation is established by the use of the <PERSON><PERSON> equation. Afterwards, the Bradley-Terry model (<PERSON> & Terry, 1952 ) is converted into a representation at the token level, demonstrating its close relationship with the Regret Preference Model (<PERSON> et al., 2022; 2023) . By utilizing this method, we effectively integrate forward KL divergence restrictions for each token in the final objective function, resulting in improved regulation of KL divergence.", "cite_spans": [{"start": 223, "end": 246, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2024;", "ref_id": "BIBREF29"}, {"start": 247, "end": 266, "text": "<PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF48"}, {"start": 772, "end": 794, "text": "(Bradley & Terry, 1952", "ref_id": "BIBREF3"}, {"start": 922, "end": 941, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF18"}, {"start": 942, "end": 947, "text": "2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "TDPO maintains the simplicity of DPO while offering improved regulation of KL divergence for aligning LLMs with human preferences. Echoing the strategy of DPO, our method directly optimizes the policy without necessitating explicit reward model learning or policy sampling throughout the training phase. Our experimental results demonstrate the effectiveness of TDPO across multiple text tasks, and gain a notable enhancement in the quality of generated responses in comparison to both DPO and PPO-based RLHF methods. In conclusion, TDPO stands out for its ability to not only effectively address the issue of excessive KL divergence but also greatly improve divergence efficiency.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "The emergence of ChatGPT has catalyzed significant advancements in the field of Large Language Models (LLMs), such as OpenAI's GPT-4 (<PERSON><PERSON><PERSON> et al., 2023) , Mistral (<PERSON> et al., 2023) , and Google's Gemini (<PERSON> et al., 2023) . Generally, the training of LLMs involves three stages: initial unsupervised pre-training on massive text corpora to grasp linguistic structures (<PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2020; Workshop et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2023) , followed by supervised fine-tuning with task-specific datasets to enhance the LLMs' probability of producing desired responses (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) . However, due to the typically limited and expensive availability of labeled datasets during the supervised fine-tuning stage, the model may retain biases and inaccuracies, manifesting as societal biases (<PERSON><PERSON> et al., 2021) , ethical concerns (<PERSON><PERSON><PERSON> et al., 2021) , toxicity (<PERSON><PERSON> et al., 2022) , and hallucinations (<PERSON> et al., 2023) , which necessitates a subsequent AI alignment phase. Noteworthy models achieving significant alignment, such as Zephyr (<PERSON><PERSON><PERSON> et al., 2023) and GPT-4 (<PERSON><PERSON><PERSON> et al., 2023) , have demonstrated the effectiveness of techniques like Reinforcement Learning from Human Feedback (RLHF) and Direct Preference Optimization (DPO) algorithms.", "cite_spans": [{"start": 133, "end": 154, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF0"}, {"start": 165, "end": 185, "text": "(<PERSON> et al., 2023)", "ref_id": null}, {"start": 208, "end": 227, "text": "(Team et al., 2023)", "ref_id": "BIBREF37"}, {"start": 374, "end": 395, "text": "(<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF30"}, {"start": 396, "end": 415, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF4"}, {"start": 416, "end": 438, "text": "Workshop et al., 2022;", "ref_id": "BIBREF44"}, {"start": 439, "end": 460, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF38"}, {"start": 590, "end": 610, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 611, "end": 631, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF7"}, {"start": 632, "end": 648, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF40"}, {"start": 854, "end": 874, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF33"}, {"start": 894, "end": 918, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF42"}, {"start": 930, "end": 949, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF31"}, {"start": 971, "end": 991, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF15"}, {"start": 1112, "end": 1135, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF39"}, {"start": 1140, "end": 1167, "text": "GPT-4 (<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Works", "sec_num": "2."}, {"text": "Reinforcement Learning from Human Feedback (RLHF) has emerged as a cornerstone in aligning LLMs with human values, providing a mechanism to refine model outputs based on qualitative feedback (<PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2023) . This approach has shown considerable promise in making models more responsive to human expectations and ethical considerations by iteratively improving their performance through human-generated feedback. However, the complexity of implementing RLHF, compounded by the inaccuracies in human-generated reward models (<PERSON> et al., 2023) , has prompted the exploration of alternative strategies. Methods like Reward Ranked Fine-Tuning (RAFT) (<PERSON> et al., 2023) and Rank Responses to align Human Feedback (RRHF) (<PERSON> et al., 2023) offer streamlined approaches to alignment, circumventing some of RLHF's inherent challenges. Particularly, Direct Preference Optimization (DPO) (<PERSON><PERSON><PERSON><PERSON> et al., 2023) represents a breakthrough in direct policy optimization, addressing the intricacies of balancing model behavior through a nuanced approach to reward function optimization. Nevertheless, the challenge of maintaining linguistic diversity while aligning with human preferences remains a pivotal concern, prompting our proposed Token-level Direct Preference Optimization (TDPO), which seeks to harmonize the dual objectives of alignment accuracy and expressive range in model outputs.", "cite_spans": [{"start": 191, "end": 216, "text": "(<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF8"}, {"start": 217, "end": 237, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF24"}, {"start": 238, "end": 255, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF1"}, {"start": 256, "end": 274, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF34"}, {"start": 275, "end": 296, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF38"}, {"start": 613, "end": 630, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF45"}, {"start": 735, "end": 754, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF10"}, {"start": 805, "end": 824, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF46"}, {"start": 969, "end": 992, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Related Works", "sec_num": "2."}, {"text": "For language generation, a language model (LM) is prompted with prompt (question) x to generate a response (answer) y, where both x and y consist of a sequence of tokens. Direct Preference Optimization (DPO) (<PERSON><PERSON><PERSON><PERSON> et al., 2023) commences with the RL objective from the RLHF:", "cite_spans": [{"start": 208, "end": 231, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "max π θ E x∼D,y∼π θ (•|x) r(x, y) -βD KL π θ (• | x) π ref (• | x) ,", "eq_num": "(1)"}], "section": "Preliminaries", "sec_num": "3."}, {"text": "where D represents the human preference dataset, r(x, y) denotes the reward function, π ref (•|x) serves as a reference model, typically chosen the language model after supervised fine-tuning, π θ represents the model undergoing RL fine-tuning, initialized with π θ = π ref , and β is the coefficient for the reverse KL divergence penalty.", "cite_spans": [{"start": 92, "end": 97, "text": "(•|x)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "By directly deriving from Eq. 1, DPO establishes a mapping between the reward model and the optimal policy under the reverse KL divergence, obtaining a representation of the reward function concerning the policy:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "r(x, y) = β log π θ (y|x) π ref (y|x) + β log Z(x).", "eq_num": "(2)"}], "section": "Preliminaries", "sec_num": "3."}, {"text": "Here, Z(x) is the partition function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "To align with human preference, DPO uses the <PERSON><PERSON><PERSON> model for pairwise comparisons:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "P BT (y 1 ≻ y 2 |x) = exp(r(x, y 1 )) exp(r(x, y 1 )) + exp(r(x, y 2 ))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": ".", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "(3) By substituting Eq. 2 into Eq. 3 and leveraging the negative log-likelihood loss, DPO derives the objective function:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "u(x, y w , y l ) = β log π θ (y w | x) π ref (y w | x) -β log π θ (y l | x) π ref (y l | x) , L DPO (π θ ; π ref ) = -E (x,yw,y l )∼D [log σ (u(x, y w , y l ))] ,", "eq_num": "(4)"}], "section": "Preliminaries", "sec_num": "3."}, {"text": "and the derivative is given as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∇ θ L DPO (π θ ; π ref ) = -E (x,yw,y l )∼D [(-u) ∇ θ u] ,", "eq_num": "(5)"}], "section": "Preliminaries", "sec_num": "3."}, {"text": "where u is the abbreviation of u(x, y w , y l ), y w and y l denotes the preferred and dispreferred completion.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "In this section, we initially reformulate the constrained reward maximization problem into a token-level form. From this, we derive the mapping between the state-action function and the optimal policy. Subsequently, we convert the Bradley-Terry model into token-level representation, establishing its equivalence with the Regret Preference Model. By substituting the mapping relationship into the reward model in token-level format, we obtain the optimization objective solely related to the policy. Finally, we conduct a formalized analysis of this optimization objective in terms of derivatives and, based on this, derive the ultimate loss function for TDPO.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Methodology", "sec_num": "4."}, {"text": "To model the sequential, auto-regressive generation, we extend the sentence-level formulation in Section 3 by considering that the response consists of T tokens y = y <T +1 := [y 1 , y 2 , ..., y T ], where y t ∈ Y, and Y represents the alphabet (vocabulary). Additionally, we assume y <1 = [ ]. Given a prompt x and the first t -1 tokens y <t of the response y, the LM predicts the probability distribution of the next token π θ (•|[x, y <t ]).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "When modeling text generation as a <PERSON>ov decision process (<PERSON><PERSON>, 2014) , a state is a combination of the prompt and the generated response up to the current step, denoted as", "cite_spans": [{"start": 59, "end": 75, "text": "(<PERSON><PERSON>, 2014)", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "s t = [x, y <t ].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "An action corresponds to the next generated token, denoted as a t = y t , and the token-wise reward is defined as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "R t := R(s t , a t ) = R([x, y <t ], y t ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "Expanding on the provided definitions, we establish the state-action function Q π , the state value function V π and the advantage function A π for a policy π:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "Qπ([x, y <t ], y t ) = Eπ ∞ k=0 γ k R t+k st = [x, y <t ], at = y t , Vπ([x, y <t ]) = Eπ Qπ([x, y <t ], y t ) st = [x, y <t ] , Aπ([x, y <t ], y t ) = Qπ([x, y <t ], y t ) -Vπ([x, y <t ]).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "(6) where γ represents the discount factor. In this paper, we set γ = 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "DPO's objective function in Eq. 1 operates at the sentence level. In contrast, we propose an alternative token-level objective function:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "max π θ E x,y <t ∼D,z∼π θ (•|[x,y <t ]) A π ref ([x, y <t ], z) -βD KL π θ (•|[x, y <t ])||π ref (•|[x, y <t ]) . (7)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "The objective function is inspired by Trust Region Policy Optimization (TRPO) (<PERSON><PERSON><PERSON> et al., 2015) . As demonstrated in Lemma 4.1, maximizing the objective function in Eq. 7 will result in policy improvements in terms of expected return.", "cite_spans": [{"start": 78, "end": 101, "text": "(<PERSON><PERSON><PERSON> et al., 2015)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "Lemma 4.1. Given two policies π and π, if for any state", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "s t = [x, y <t ], E z∼π [A π ([x, y <t ], z)] ≥ 0, then we can conclude: E x∼D [V π ([x])] ≥ E x∼D [V π ([x])] ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "The proof is provided in Appendix A.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "Notably, to maintain generation diversity and prevent the model from hacking some high-reward answers, we incorporate reverse KL divergence for each token in our token-level objective function, which prevents the model from deviating too far from the reference model distribution.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "Starting from the token-level objective function in Eq. 7, we can directly derive the mapping between the state-action function Q π and the optimal policy π * θ . We summarize this relationship in the following lemma.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "Lemma 4.2. The constrained problem in Eq. 7 has the closed-form solution:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "π * θ (z|[x, y <t ]) = π ref (z|[x, y <t ]) exp 1 β Q π ref ([x, y <t ], z) Z([x, y <t ]; β) ,", "eq_num": "(8)"}], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "Z([x, y <t ]; β) = E z∼π ref (•|[x,y <t ]) e 1 β Qπ ref ([x,y <t ],z)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "is the partition function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "See Appendix A.2 for more details.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "To obtain the optimal policy π * θ from Eq. 8, we must estimate the state-action function Q π ref and the partition function Z(•). However, ensuring the accuracy of the stateaction function Q π at each state and action is challenging, and estimating the partition function Z(•) is also difficult. Therefore, we reorganize Eq. 8 to obtain the expression of the state-value function in terms of the policy:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Q π ref ([x, y <t ], z) = β log π * θ (z|[x, y <t ]) π ref (z|[x, y <t ]) + β log Z([x, y <t ]; β).", "eq_num": "(9)"}], "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "To facilitate subsequent derivations, we first introduce the sequential KL divergence, as defined in Definition 4.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Definition 4.3. Given two language models π 1 and π 2 , with the input prompt x and output response y, the sequential KL divergence is defined as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "D SeqKL (x,y; π 1 ∥π 2 ) = T t=1 D KL (π 1 (•|[x, y <t ])∥π 2 (•|[x, y <t ])).", "eq_num": "(10)"}], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Given prompts x and pairwise responses (y 1 , y 2 ), the <PERSON><PERSON> model expresses the human preference probability. However, since the <PERSON><PERSON><PERSON> model is formulated at the sentence level, it cannot establish a connection with the token-level mapping presented in Eq. 9. Consequently, we need to derive a token-level preference model. Initiating from the <PERSON><PERSON><PERSON> model, we transform it into a token-level formulation and demonstrate its equivalence with the Regret Preference Model (<PERSON> et al., 2023; 2022) , as shown in the Lemma 4.4.", "cite_spans": [{"start": 496, "end": 515, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF19"}, {"start": 516, "end": 521, "text": "2022)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Lemma 4.4. Given a reward function r(x, y), assuming a relationship between token-wise rewards and the reward function represented by r(x, y) = T t=1 γ t-1 R([x, y <t ], y t ), we can establish the equivalence between the Bradley-<PERSON> model and the Regret Pref-erence Model in the task of text generation alignment, i.e.,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "PBT(y1 ≻ y2|x) = σ T 1 t=1 γ t-1 Aπ([x, y <t 1 ], y t 1 ) - T 2 t=1 γ t-1 Aπ([x, y <t 2 ], y t 2 ) ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "(11) where σ(x) = 1/(1 + exp(-x)) is the logistic sigmoid function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "We prove this lemma in A.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "In Lemma 4.4, we assume that r(x, y)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "= T t=1 γ t-1 R([x, y <t ], y t ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "This assumption is natural in the context of RL, where r(x, y) represents the overall reward for response y given the prompt x. Considering text generation as a sequential decision-making problem, r(x, y) can be viewed as the cumulative reward for the generated text.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "According to the definition of the advantage function in Section 4.1, we can directly establish the relationship between the optimal solution in Eq. 9 and preference optimization objective in Eq. 11. One intractable aspect is that the stateaction function Q π depends on a partition function, which is contingent on both the input prompt x and the output response y. This results in non-identical values of the partition function for a pair of responses (y w , y l ), specifically,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Z([x, y <t w ]; β) ̸ = Z([x, y <t l ]; β).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "As a result, we cannot employ a cancellation strategy similar to DPO, which relies on the property that the Bradley-Terry model depends only on the difference in rewards between two completions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Fortunately, by expanding the advantage function A π and converting the state-value function V π into a form exclusively related to the state-action function Q π , we can offset the partition function naturally. In this way, we ultimately reformulate the Bradley-<PERSON> model to be directly tied to the optimal policy π * θ and the reference policy π ref . This is summarized in the following theorem. Theorem 4.5. In the KL-constrainted advantage function maximization problem corresponding to Eq.7, the Bradley<PERSON><PERSON> model express the human preference probability in terms of the optimal policy π * θ and reference policy π ref :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "P * BT (y1 ≻ y2|x) = σ(u * (x, y1, y2) -δ * (x, y1, y2)),", "eq_num": "(12)"}], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "where, u(x, y 1 , y 2 ) refers to the difference in rewards implicitly defined by the language model π θ and the reference model π ref (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , represented as", "cite_spans": [{"start": 135, "end": 158, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "u(x, y1, y2) = β log π θ (y1 | x) π ref (y1 | x) -β log π θ (y2 | x) π ref (y2 | x) ,", "eq_num": "(13)"}], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "and δ(x, y 1 , y 2 ) refers to the difference in sequential forward KL divergence between two pairs (x, y 1 ) and (x, y 2 ), weighted by β, expressed as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "δ(x, y 1 , y 2 ) =βD SeqKL (x, y 2 ; π ref ∥π θ ) -βD SeqKL (x, y 1 ; π ref ∥π θ ) . (", "eq_num": "14"}], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "The proof is provided in the Appendix A.4.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Drawing on Eq. 12, we reformulate the Bradley-<PERSON> model into a structure solely relevant to the policy. This allows us to formulate a likelihood maximization objective for a parametrized policy π θ , leading to the derivation of the loss function for the initial version of our method, TDPO 1 :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L TDPO1 (π θ ; π ref ) = -E (x,yw,y l )∼D [log σ (u(x, y w , y l ) -δ(x, y w , y l ))] .", "eq_num": "(15)"}], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "Through this approach, we explicitly introduce sequential forward KL divergence into the loss function. Coupled with the implicitly integrated reverse KL divergence, we enhance our ability to balance alignment performance and generation diversity of LLMs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "Subsequently, we conduct a derivative analysis of our method and make specific modifications to the loss function of TDPO. For convenience, we use u to denote u(x, y w , y l ), and δ to represent δ(x, y w , y l ). By employing the formulation of the loss function presented in Eq.15, we compute the gradient of the loss function with respect to the parameters θ:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∇ θ L TDPO1 (π θ ; π ref ) = -E (x,yw,y l )∼D [(-u + δ) [∇ θ u -∇ θ δ]] .", "eq_num": "(16)"}], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "In Eq. 16, (-u + δ) serves as the weighting factor for the gradient. The first part (-u) corresponds to the weight factor in the loss function of DPO. When the language model makes errors in predicting human preferences, i.e., log π θ (y l |x) π ref (y l |x) > log π θ (yw|x) π ref (yw|x) , the value of (-u) will become larger, applying a stronger update for the comparison (y w , y l ). While the second part δ is a distinctive component of our method. As shown in Figure 1 , the KL divergence growth rate for the dispreferred response subset is faster than that for the preferred response subset. With the increasing disparity, the corresponding value of δ rises, thereby amplifying the weight factor (-u + δ). Combined with the subsequent gradient term, our objective function can effectively suppress the difference in KL divergence between pairs of responses with large disparities in KL divergence. Through the collaborative influence of the weight factor δ and the gradient term (-∇ θ δ), our method achieves the purpose of automatic control over the KL divergence balance.", "cite_spans": [], "ref_spans": [{"start": 474, "end": 475, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "The gradient of the loss function in Eq. 16 also consists of two components, ∇ θ u and (-∇ θ δ). ∇ θ u represents the optimization direction of the gradient in DPO. Intuitively, ∇ θ u increases the likelihood of preferred completions y w and decreases the likelihood of dispreferred com-pletions y l . While (-∇ θ δ) tends to narrow the gap between D SeqKL (x, y w ; π ref ∥π θ ) and D SeqKL (x, y l ; π ref ∥π θ ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "However, when considered separately, the gradient of D SeqKL (x, y w ; π ref |π θ ) in the loss function tends to increase the sequential KL divergence between π ref and π θ at (x, y w ) during the optimization process. This is because the sequential forward KL divergence in the loss function is introduced through the state-value function V π , inherently introducing an expectation E z∼π ref log π θ (z|[x,y <t ]) π ref (z|[x,y <t ]) as a baseline at each token. The negative value of this expectation corresponds precisely to a forward KL divergence", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "D KL (π ref (•|[x, y <t ])|π θ (•|[x, y <t ]))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": ", which can be used to constrain the unbalanced growth of KL divergence. For the prompt x and the preferred response y w , at each token, the loss function in Eq. 16 tends to increase the likelihood of log ) . The increase in the expectation implies a smaller forward KL divergence at that token, thereby acting to constrain the growth rate of sequential forward KL divergence. Therefore, for this term, we choose to retain its gradient updates.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "In conclusion, we only propagate the gradient of the D SeqKL (x, y l ; π ref |π θ ) in (-∇ θ δ). When the second part weight factor δ becomes larger, it imposes a stronger suppression on D SeqKL (x, y l ; π ref ∥π θ ) to control the balance of KL divergence. Furthermore, to achieve a better balance between alignment performance and generation diversity in TDPO, we introduce an additional parameter α into the loss function. By adjusting the magnitude of α, we can control the deviation between D SeqKL (x, y w ; π ref ∥π θ ) and D SeqKL (x, y l ; π ref ∥π θ ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "In summary, we modify the loss function of TDPO 1 , resulting in the second version of our method, TDPO 2 , as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "L TDPO2 (π θ ; π ref ) = -E (x,yw,y l )∼D [log σ (u(x, y w , y l ) -αδ 2 (x, y w , y l ))] ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "(17) where α is a parameter, and", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "δ 2 (x, y 1 , y 2 ) =βD SeqKL (x, y 2 ; π ref ∥π θ ) -sg (βD SeqKL (x, y 1 ; π ref ∥π θ )) .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "(18) The sg represents the stop-gradient operator, which blocks the propagation of gradients.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "We summarize the comparison of the loss functions for DPO, TDPO 1 , and TDPO 2 , as presented in Figure 2 . Leveraging the parameter β to regulate the deviation of the language model from the base reference model, and α to control the balance of sequential KL divergence within the language model, our approach achieves superior alignment with human preferences while preserving model generation diversity effectively. We provided the pseudocode in Algorithm 1 and the Pytorch implementation version of TDPO loss in Appendix B.", "cite_spans": [], "ref_spans": [{"start": 104, "end": 105, "text": "2", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "In this section, we demonstrate the superior performance of our algorithm in three different open-sourced datasets: the IMDb sentiment dataset (<PERSON><PERSON> et al., 2011) , the Anthropic HH dataset (<PERSON> et al., 2022) , and MT-bench (<PERSON> et al., 2023) . The IMDb dataset serves as a controlled semantic generation dataset where the model is presented with prompts consisting of prefixes from movie reviews, and required to generate responses with positive sentiment. The Anthropic HH dataset is a single-turn dialogue dataset where the model receives human queries, covering various if Method M is TDPO 1 then θ ← θ + η∇ θ E (x,yw,y l )∼Dm [log σ (u(x, y w , y l ) -αδ 2 (x, y w , y l ))] ▷ Eq.17 end if 18: end for 19: Output: π θ topics such as academic questions or life guidance. The trained model is tasked with providing helpful answers to these questions while avoiding toxic responses. Finally, MT-Bench is a GPT-4-based evaluation benchmark, assessing the proficiency of LLMs in handling multi-turn openended questions. Questions in MT-Bench span eight distinct knowledge domains, from areas such as writing, mathematical reasoning, and humanities. Experimental results demonstrate that MT-Bench achieves consistency with human preferences exceeding 80%.", "cite_spans": [{"start": 143, "end": 162, "text": "(<PERSON><PERSON> et al., 2011)", "ref_id": "BIBREF23"}, {"start": 190, "end": 208, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF1"}, {"start": 224, "end": 244, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF47"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "In this experiment, besides our proposed methods TDPO 1 and TDPO 2 , we also implemented the DPO algorithm for fair comparison. We employed GPT-2 Large (<PERSON><PERSON> et al., 2019) as our base model and the model checkpoint: insub/gpt2-large-IMDb-fine-tuned 1 as the SFT model. During the evaluation, we utilized the pre-trained sentiment classifier siebert/sentiment-roberta-large-english 2 to compute rewards. For DPO, we followed the official implementation (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , setting β at 0.1. To analyze the effectiveness of each algorithm in optimizing the constrained reward maximization objective, we evaluated each algorithm after 100 training steps until convergence, computing its frontier of average reward and average sequential KL divergence with the reference policy.", "cite_spans": [{"start": 152, "end": 174, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF27"}, {"start": 455, "end": 478, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Experiments on IMDb Dataset", "sec_num": "5.1."}, {"text": "1 https://huggingface.co/insub/ gpt2-large-IMDb-fine-tuned 2 https://huggingface.co/siebert/ sentiment-roberta-large-english", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments on IMDb Dataset", "sec_num": "5.1."}, {"text": "The results are depicted in Figure 3 (a). We implement the DPO, TDPO 1 , and different versions of TDPO 2 algorithms with varying the parameter α. From the figure, we notice that although DPO establishes an efficient frontier, TDPO 1 and TDPO 2 outperform DPO in terms of divergence versus reward on the frontier, achieving higher reward while maintaining low KL divergence. We also implemented versions of TDPO 2 with α ∈ {1, 1.5, 2, 5}. However, we found that higher values of α made it difficult to optimize the reward. In Figures 3(b ) to 3(d), we illustrate the curves portraying the sequential KL divergence for different algorithms during the training process. The sequential KL divergence growth rate of DPO on the dispreferred response subset is significantly higher than that on the preferred response subset, leading to an increasing offset between them. In contrast, TDPO 2 exhibits superior control over KL divergence, achieving better divergence efficiency compared to DPO. As analyzed in Section 4.4, TDPO 1 tends to result in an increased sequential KL divergence on the preferred response subset, thereby exhibiting a weaker capacity for KL divergence adjustment compared to TDPO 2 . TDPO 2 maintains a more balanced sequential KL divergence on both dispreferred and preferred response subsets, contributing to its ability to achieve a superior frontier. Although a larger α enhances control over the sequential KL divergence, it also affects the speed and difficulty of optimization. For the remainder of this paper, we set α = 0.5. In Appendix C, we also present graphs of the frontier between the reward and forward KL divergence and the progression curves of the forward KL divergence throughout the training process. ", "cite_spans": [], "ref_spans": [{"start": 35, "end": 36, "text": "3", "ref_id": "FIGREF5"}, {"start": 534, "end": 537, "text": "3(b", "ref_id": "FIGREF8"}], "eq_spans": [], "section": "Experiments on IMDb Dataset", "sec_num": "5.1."}, {"text": "Next, we evaluate the performance of TDPO 1 and TDPO 2 on the Anthropic HH dataset. We use Pythia-2.8B (<PERSON><PERSON><PERSON> et al., 2023) as the base model and fine-tune the base model on chosen completions to train a reference model, such that completions are within the distribution of the model. Subsequently, we train TDPO 1 , TDPO 2 , DPO (<PERSON><PERSON><PERSON><PERSON> et al., 2023) and f-DPO with forward KL divergence constraint (<PERSON> et al., 2023) on this reference model. In this experiment, our primary focus is on two aspects: 1) the trade-off between alignment and diversity in generating responses among different algorithms, and 2) the ability of different algorithms to align with human preferences. For the first part, we utilize automatic metrics for evaluation, while for the second part, we rely on the GPT-4 evaluation. Both evaluations were conducted on the test set of the Anthropic HH dataset.", "cite_spans": [{"start": 103, "end": 127, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": null}, {"start": 334, "end": 357, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF28"}, {"start": 406, "end": 425, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Experiments on Anthropic HH Dataset", "sec_num": "5.2."}, {"text": "To assess the alignment performance of different algorithms in generating responses, we compute the accuracy of gen-erated responses relative to chosen completions in the test dataset. To measure the diversity, we employ nucleus sampling with p = 0.95 to generate 25 responses and utilize the predictive entropy as the evaluation metric. The tradeoff between alignment accuracy and diversity for different algorithms is summarized in Table 1 . TDPO 2 not only surpasses DPO, f-DPO and TDPO 1 in terms of accuracy but also excels in entropy, achieving a superior balance between alignment and diversity.", "cite_spans": [], "ref_spans": [{"start": 440, "end": 441, "text": "1", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Experiments on Anthropic HH Dataset", "sec_num": "5.2."}, {"text": "To further assess the ability of TDPO 1 and TDPO 2 to align with human preferences, we evaluated the win rates of responses generated by models trained with different algorithms against chosen responses on the test set of the HH dataset, the result is illustrated in the Figure 4 . Compared to the SFT model, the DPO, TDPO 1 , and TDPO 2 algorithms better align with human preferences, achieving win rates not less than 50% against chosen responses at temperature 0.75. This demonstrates that both TDPO 1 , and TDPO 2 possess a strong capability to align with human preferences. ", "cite_spans": [], "ref_spans": [{"start": 278, "end": 279, "text": "4", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "Experiments on Anthropic HH Dataset", "sec_num": "5.2."}, {"text": "To comprehensively evaluate TDPO 1 , and TDPO 2 in terms of generation quality, we conducted pairwise comparisons on the MT-Bench using models trained on the Anthropic HH dataset. Following the official MT-Bench implementation, we sampled responses with a temperature coefficient of 0.7 and constrained the maximum number of newly generated tokens to 512. For the PPO baseline, we employed the trlx framework (<PERSON><PERSON><PERSON> et al., 2023) ", "cite_spans": [{"start": 409, "end": 432, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "Experiments on MT-Bench", "sec_num": "5.3."}, {"text": "In this work, we introduced Token-level Direct Preference Optimization (TDPO), an innovative token-level fine-tuning approach for Large Language Models (LLMs) aimed at aligning more closely with human preferences. By employing the token-wise optimization with forward KL divergence constraints and converting the Bradley-Terry model into a token-level preference model, TDPO addresses key challenges in divergence efficiency and content diversity, surpassing traditional methods like Direct Preference Optimization (DPO) and PPO-based RLHF in tasks such as controlled sentiment generation and single-turn dialogues. This marks a substantial advancement in LLM training methodologies, demonstrating the potential of token-level optimization to enhance the alignment, quality, and diversity of LLM outputs, setting a new direction for AI alignment research and the development of nuanced, human-aligned AI systems.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Regarding the future prospects of alignment methodologies, we anticipate that iterative refinement approaches and multiturn conversational alignment strategies will significantly improve the alignment of large language models with human values. By continuously refining these models, we can achieve more precise alignment with complex human preferences. Moreover, multi-turn conversations enable deeper and more nuanced interactions, fostering comprehensive attunement to human intentions. These approaches aim to enhance the quality and relevance of AI responses, making AI systems more harmonized with human values and expectations. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "E x∼D [V π ([x])] ≥ E x∼D [V π ([x])] ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Proof. Let trajectory τ := (x, y 1 , y 2 , ...), and the notation E τ |π [•] indicates that actions are sampled from π to generate τ . So we can get", "cite_spans": [{"start": 73, "end": 76, "text": "[•]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E x∼D [V π ([x])] -E x∼D [V π ([x])] (19) =E τ |π ∞ t=1 γ t-1 R t -V π ([x]) (20) =E τ |π ∞ t=1 γ t-1 R t + γV π ([x, y <t+1 ]) -V π ([x, y <t ]) (21) =E τ |π ∞ t=1 γ t-1 A π ([x, y <t ], y t ) (22) =E τ |π ∞ t=1 γ t-1 E y t ∼π A π ([x, y <t ], y t )", "eq_num": "(23)"}], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Since for any state s t = [x, y <t ], E z∼π [A π ([x, y <t ], z)] ≥ 0, so we can obtain", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E x∼D [V π ([x])] -E x∼D [V π ([x])] ≥ 0 (", "eq_num": "24"}], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Our goal is to maximize the expected return of a parameterized policy π θ . According to Eq.23, what we need to do is max ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Based on the property of KL divergence, we can derive the relationship between the optimal policy and the state-action function: ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "π * θ (z|[x, y <t ]) = π ref (z|[x, y <t ]) exp 1 β Q π ref ([x, y <t ], z) Z([x, y <t ]; β)", "eq_num": "(33)"}], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "where σ(x) = 1/(1 + exp(-x)) is the logistic sigmoid function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Proof. According to the <PERSON><PERSON> model, we have P BT (y 1 ≻ y 2 |x) = exp(r(x, y 1 )) exp(r(x, y 1 )) + exp(r(x, y 2 )) ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "where r(x, y) represents the overall reward of the pair (x, y).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Based on assumption that r(x, y) = ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "https://huggingface.co/Dahoas/ gptj-rm-static", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "The research leading to these results received funding from National Key R&D Program of China (2022ZD0116402). In addition, it received funding from Science and Technology Research and Development Project of China State Railway Group Corporation Limited (P2022X012).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.Text generation is analogous to a deterministic contextual bandit, where the transition to the next state is certain given the current state and action, i.e., p(s t+1 = [x, y <t+1 ]|s t = [x, y <t ], a t = y t ) = 1, so we have:Next, note that y T = EOS denotes the end of the text sequence. Therefore,Substituting Eq.38 to Eq.41 into the Bradley-Terry model, we obtainAdditionally, note that y <1 = [ ], so we can getTherefore,A.4. Deriving the TDPO Objective Under the Bradley-Terry Model Theorem A.4. In the KL-constrainted advantage function maximization problem corresponding to Eq.7, the Bradley-<PERSON> model express the human preference probability in terms of the optimal policy π * θ and reference policy π ref :where, u(x, y 1 , y 2 ) refers to the difference in rewards implicitly defined by the language model π θ and the reference model π ref (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , represented asand δ(x, y 1 , y 2 ) refers to the difference in sequential forward KL divergence between two pairs (x, y 1 ) and (x, y 2 ), weighted by β, expressed as δ(x, y 1 , y 2 ) = βD SeqKL (x, y 2 ;Proof. According to the Lemma 4.2, we haveRearrange Eq.48, we obtainFrom Lemma 4.4, We can getBy leveraging Eq.49, we can deriveSubstituting Eq.59 to Eq.61 into Eq.50, we arrive at:", "cite_spans": [{"start": 1053, "end": 1076, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "PyTorch code for the TDPO loss is provided below:import torch import torch.nn.functional as F def tdpo_loss(pi_logits, ref_logits, yw_idxs, yl_idxs, labels, beta, alpha, if_tdpo2): \"\"\" pi_logits: policy logits. Shape: (batch_size, sequence_length, vocab_size), ref_logits: reference logits. Shape: (batch_size, sequence_length, vocab_size) yw_idxs: preferred completion indices in [0,B-1], shape (T,) yl_idxs: dispreferred completion indices in [0,B-1], shape (T,) labels: labels for which to compute the log probabilities, Shape: (batch_size, sequence_length) beta: temperature controlling strength of KL penalty Each pair of (yw_idxs[i], yl_idxs[i]) represents the indices of a single preference pair. alpha: The weight factor adjusts the influence weight of kl divergence at each token if_tdpo2: Use method TDPO2 by default; if False, switch to TDPO1 \"\"\" pi_vocab_logps = pi_logits.log_softmax(-1) ref_vocab_ps = ref_logits.softmax(-1) ref_vocab_logps = ref_vocab_ps.log() pi_per_token_logps = torch.gather(pi_vocab_logps, dim=2, index=labels.unsqueeze(2)).squeeze(2) ref_per_token_logps = torch.gather(ref_vocab_logps, dim=2, index=labels.unsqueeze( 2 Unless specified otherwise, we use a α = 0.5, β = 0.1, batch size of 64, and the RMSprop optimizer with a learning rate of 5e-6. We linearly warm up the learning rate from 0 to 5e-6 over 150 steps. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. TDPO Implementation Details and Hyperparameters", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Gpt-4 technical report", "authors": [{"first": "J", "middle": [], "last": "Achiam", "suffix": ""}, {"first": "S", "middle": [], "last": "Adler", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Almeida", "suffix": ""}, {"first": "J", "middle": [], "last": "Altenschmidt", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.08774"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, F. L., Almeida, D., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S., et al. Gpt-4 technical report. arXiv preprint arXiv:2303.08774, 2023.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Training a helpful and harmless assistant with reinforcement learning from human feedback", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Fort", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2204.05862"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Fort, S., <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Training a helpful and harmless assistant with rein- forcement learning from human feedback. arXiv preprint arXiv:2204.05862, 2022.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Pythia: A suite for analyzing large language models across training and scaling", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": ["G"], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>'brien", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "U", "middle": ["S"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "2397--2430", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, S<PERSON>, <PERSON>, U. S., <PERSON>, <PERSON>, et al. Pythia: A suite for ana- lyzing large language models across training and scaling. In International Conference on Machine Learning, pp. 2397-2430. PMLR, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Rank analysis of incomplete block designs: I. the method of paired comparisons", "authors": [{"first": "R", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["E"], "last": "<PERSON>", "suffix": ""}], "year": 1952, "venue": "Biometrika", "volume": "39", "issue": "3/4", "pages": "324--345", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> E. Rank analysis of incom- plete block designs: I. the method of paired comparisons. Biometrika, 39(3/4):324-345, 1952.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Language models are few-shot learners", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Subbiah", "suffix": ""}, {"first": "J", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "1877--1901", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Language models are few-shot learners. Advances in neural information processing systems, 33: 1877-1901, 2020.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Sparks of artificial general intelligence: Early experiments with gpt-4", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Gehrke", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.12712"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Sparks of artificial general intel- ligence: Early experiments with gpt-4. arXiv preprint arXiv:2303.12712, 2023.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Vicuna: An open-source chatbot impressing gpt-4 with 90%* chatgpt quality", "authors": [{"first": "W.-L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["E"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "14", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Vicuna: An open-source chatbot impressing gpt-4 with 90%* chatgpt quality. See https://vicuna. lmsys. org (accessed 14 April 2023), 2023.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Deep reinforcement learning from human preferences. Advances in neural information processing systems", "authors": [{"first": "P", "middle": ["F"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Deep reinforcement learning from human preferences. Advances in neural information pro- cessing systems, 30, 2017.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Scaling instruction-finetuned language models", "authors": [{"first": "H", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Longpre", "suffix": ""}, {"first": "B", "middle": [], "last": "Zoph", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.11416"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Scaling instruction-finetuned language models. arXiv preprint arXiv:2210.11416, 2022.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "<PERSON><PERSON> ranked finetuning for generative foundation model alignment", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "R", "middle": [], "last": "Pan", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Raft", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.06767"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: <PERSON><PERSON> ranked fine- tuning for generative foundation model alignment. arXiv preprint arXiv:2304.06767, 2023.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Red teaming language models to reduce harms: Methods, scaling behaviors, and lessons learned", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Kernion", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2209.07858"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Red teaming language models to reduce harms: Methods, scaling behaviors, and lessons learned. arXiv preprint arXiv:2209.07858, 2022.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Pal: Program-aided language models", "authors": [{"first": "L", "middle": [], "last": "Gao", "suffix": ""}, {"first": "A", "middle": [], "last": "Madaan", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "U", "middle": [], "last": "Alon", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Neubig", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, U<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, J<PERSON>, and <PERSON><PERSON>, G. <PERSON>: Program-aided language models, 2023.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Improving alignment of dialogue agents via targeted human judgements", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Trębacz", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Ewalds", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Chadwick", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2209.14375"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, et al. Improving alignment of dialogue agents via targeted human judgements. arXiv preprint arXiv:2209.14375, 2022.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "trlx: A framework for large scale reinforcement learning from human feedback", "authors": [{"first": "A", "middle": [], "last": "Havrilla", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Tow", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Castricato", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 2023 Conference on Empirical Methods in Natural Language Processing", "volume": "", "issue": "", "pages": "8578--8595", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and Castricato, L. trlx: A framework for large scale reinforcement learning from human feedback. In Proceedings of the 2023 Conference on Empirical Methods in Natural Language Processing, pp. 8578-8595, 2023.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "A survey on hallucination in large language models: Principles, taxonomy, challenges, and open questions", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Ma", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Qin", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.05232"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. A survey on hallucination in large language models: Principles, taxonomy, challenges, and open questions. arXiv preprint arXiv:2311.05232, 2023.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "A distributional approach to controlled text generation", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2012.11635"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, M. A distri- butional approach to controlled text generation. arXiv preprint arXiv:2012.11635, 2020.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Models of human preference for learning reward functions", "authors": [{"first": "W", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Hatgis-Kessell", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Stone", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2206.02231"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S<PERSON>, <PERSON>, P<PERSON>, and <PERSON><PERSON>, A. Models of human prefer- ence for learning reward functions. arXiv preprint arXiv:2206.02231, 2022.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Learning optimal advantage from preferences and mistaking it for reward", "authors": [{"first": "W", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Hatgis-Kessell", "suffix": ""}, {"first": "S", "middle": ["O"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Stone", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.02456"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, S. Learning optimal advantage from preferences and mistaking it for reward. arXiv preprint arXiv:2310.02456, 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "An empirical survey on long document summarization: Datasets, models, and metrics", "authors": [{"first": "H", "middle": ["Y"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Pan", "suffix": ""}], "year": 2022, "venue": "ACM Computing Surveys", "volume": "55", "issue": "8", "pages": "1--35", "other_ids": {"DOI": ["10.1145/3545176"], "ISSN": ["1557-7341"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. An empirical survey on long document summarization: Datasets, models, and metrics. ACM Computing Surveys, 55(8):1-35, December 2022. ISSN 1557-7341. doi: 10.1145/3545176. URL http://dx.doi.org/10.1145/3545176.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Crafting papers on machine learning", "authors": [{"first": "P", "middle": [], "last": "Langley", "suffix": ""}], "year": 2000, "venue": "Proceedings of the 17th International Conference on Machine Learning (ICML 2000)", "volume": "", "issue": "", "pages": "1207--1216", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> papers on machine learning. <PERSON> <PERSON>, <PERSON><PERSON> (ed.), Proceedings of the 17th International Conference on Machine Learning (ICML 2000), pp. 1207-1216, Stan- ford, CA, 2000. <PERSON>.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Statistical rejection sampling improves preference optimization", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.06657"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Statistical rejection sam- pling improves preference optimization. arXiv preprint arXiv:2309.06657, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Learning word vectors for sentiment analysis", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Potts", "suffix": ""}], "year": 2011, "venue": "Proceedings of the 49th annual meeting of the association for computational linguistics: Human language technologies", "volume": "", "issue": "", "pages": "142--150", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and Potts, C. Learning word vectors for sentiment analysis. In Proceedings of the 49th annual meeting of the asso- ciation for computational linguistics: Human language technologies, pp. 142-150, 2011.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Training language models to follow instructions with human feedback", "authors": [{"first": "L", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "D", "middle": [], "last": "Almeida", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "27730--27744", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Training language models to follow instructions with human feedback. Advances in Neural Information Processing Systems, 35:27730-27744, 2022.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Red teaming language models with language models", "authors": [{"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Song", "suffix": ""}, {"first": "T", "middle": [], "last": "Cai", "suffix": ""}, {"first": "R", "middle": [], "last": "Ring", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, G. Red team- ing language models with language models, 2022. URL https://arxiv. org/abs/2202.03286, 2022.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Markov decision processes: discrete stochastic dynamic programming", "authors": [{"first": "M", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> decision processes: discrete stochastic dynamic programming. John Wiley & Sons, 2014.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Language models are unsupervised multitask learners", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Child", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "OpenAI blog", "volume": "1", "issue": "8", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Language models are unsupervised multitask learners. OpenAI blog, 1(8):9, 2019.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Direct preference optimization: Your language model is secretly a reward model", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.18290"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C. <PERSON>, and <PERSON>, C. Direct preference optimization: Your language model is secretly a reward model. arXiv preprint arXiv:2305.18290, 2023.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "From r to Q * : Your Language Model is Secretly a Q-Function", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Park", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2404.12358"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, C. From r to Q * : Your Language Model is Secretly a Q-Function. arXiv preprint arXiv:2404.12358, 2024.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Exploring the limits of transfer learning with a unified text-to-text transformer", "authors": [{"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Li", "suffix": ""}, {"first": "P", "middle": ["J"], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "The Journal of Machine Learning Research", "volume": "21", "issue": "1", "pages": "5485--5551", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>. Exploring the limits of transfer learning with a unified text-to-text transformer. The Journal of Machine Learning Research, 21(1):5485-5551, 2020.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Characteristics of harmful text: Towards rigorous benchmarking of language models", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Uesato", "suffix": ""}, {"first": "P.-S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, L. A. Characteristics of harm- ful text: Towards rigorous benchmarking of language models, 2022.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Trust region policy optimization", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Jordan", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "1889--1897", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, M., and <PERSON><PERSON>, P. Trust region policy optimization. In International conference on machine learning, pp. 1889-1897. PMLR, 2015.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Societal biases in language generation: Progress and challenges", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K.-W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2105.04054"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, N. So- cietal biases in language generation: Progress and chal- lenges. arXiv preprint arXiv:2105.04054, 2021.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Preference ranking optimization for human alignment", "authors": [{"first": "F", "middle": [], "last": "Song", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.17492"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, H. Preference ranking optimization for human alignment. arXiv preprint arXiv:2306.17492, 2023.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Learning to summarize from human feedback", "authors": [{"first": "N", "middle": [], "last": "Stiennon", "suffix": ""}, {"first": "L", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "P", "middle": [], "last": "", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, A<PERSON>, <PERSON>, D<PERSON>, and <PERSON><PERSON>, P. Learning to summarize from human feedback, 2022.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "A strong, replicable instruction-following model. Stanford Center for Research on Foundation Models", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Li", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": ["B"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Alpaca", "suffix": ""}], "year": null, "venue": "", "volume": "3", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, T. B. Alpaca: A strong, replicable instruction-following model. Stanford Center for Research on Foundation Models. https://crfm. stanford. edu/2023/03/13/alpaca. html, 3(6):7, 2023.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "a family of highly capable multimodal models", "authors": [{"first": "G", "middle": [], "last": "Team", "suffix": ""}, {"first": "R", "middle": [], "last": "Anil", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J.-B", "middle": [], "last": "Alayrac", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Soricut", "suffix": ""}, {"first": "J", "middle": [], "last": "Schalkwyk", "suffix": ""}, {"first": "A", "middle": ["M"], "last": "Dai", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.11805"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Gemini: a family of highly capable multimodal models. arXiv preprint arXiv:2312.11805, 2023.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Llama 2: Open foundation and finetuned chat models", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Stone", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Batra", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09288"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N., Batra, S., B<PERSON>ga<PERSON>, P., <PERSON>, S., et al. Llama 2: Open foundation and fine- tuned chat models. arXiv preprint arXiv:2307.09288, 2023.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Direct distillation of lm alignment", "authors": [{"first": "L", "middle": [], "last": "Tunstall", "suffix": ""}, {"first": "E", "middle": [], "last": "Beeching", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Belkada", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Fourrier", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.16944"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Zephyr: Direct distillation of lm alignment. arXiv preprint arXiv:2310.16944, 2023.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "An index for quantifying overlaps with pre-training corpora", "authors": [{"first": "T.-T", "middle": [], "last": "Vu", "suffix": ""}, {"first": "X", "middle": [], "last": "He", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Koala", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: An index for quantifying overlaps with pre-training corpora, 2023.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Beyond reverse kl: Generalizing direct preference optimization with diverse divergence constraints", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.16240"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> reverse kl: Generalizing direct preference optimiza- tion with diverse divergence constraints. arXiv preprint arXiv:2309.16240, 2023.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Ethical and social risks of harm from language models", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Uesato", "suffix": ""}, {"first": "P.-S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2112.04359"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Ethical and social risks of harm from language models. arXiv preprint arXiv:2112.04359, 2021.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "On decoding strategies for neural text generators", "authors": [{"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Transactions of the Association for Computational Linguistics", "volume": "10", "issue": "", "pages": "997--1012", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. On decoding strategies for neural text generators. Transactions of the Association for Computational Linguistics, 10:997-1012, 2022.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "A 176b-parameter open-access multilingual language model", "authors": [{"first": "B", "middle": [], "last": "Workshop", "suffix": ""}, {"first": "T", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Fan", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.05100"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. Bloom: A 176b-parameter open-access multilin- gual language model. arXiv preprint arXiv:2211.05100, 2022.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Finegrained human feedback gives better rewards for language model training", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Hu", "suffix": ""}, {"first": "W", "middle": [], "last": "Shi", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Ammanabrolu", "suffix": ""}, {"first": "N", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Ostendorf", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.01693"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, M., and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>. Fine- grained human feedback gives better rewards for language model training. arXiv preprint arXiv:2306.01693, 2023.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Rank responses to align language models with human feedback without tears", "authors": [{"first": "Z", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "H", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Rrhf", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.05302"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Rank responses to align language mod- els with human feedback without tears. arXiv preprint arXiv:2304.05302, 2023.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Judging llm-as-a-judge with mt-bench and chatbot arena", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W.-L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "D", "middle": [], "last": "Li", "suffix": ""}, {"first": "E", "middle": [], "last": "Xi<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.05685"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Judging llm-as-a-judge with mt-bench and chatbot arena. arXiv preprint arXiv:2306.05685, 2023.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "<PERSON><PERSON><PERSON> meets ppo: Reinforced token optimization for rlhf", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "He", "suffix": ""}, {"first": "J", "middle": [], "last": "Bian", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2404.18922"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> meets ppo: Reinforced token optimization for rlhf. arXiv preprint arXiv:2404.18922, 2024.", "links": null}}, "ref_entries": {"FIGREF0": {"num": null, "fig_num": "1", "uris": null, "text": "Figure 1. Sequential KL (SeqKL) divergence of both preferred response and dispreferred responses on IMDb dataset. Figure 1(a) shows the progression of SeqKL divergence on the preferred responses over training steps. Figure 1(b) depicts the evolution of SeqKL divergence on the dispreferred responses over the training steps. Figure 1(c) illustrates the difference between the SeqKL divergence of the dispreferred responses and that of the preferred responses during the training process, namely margin = |DSeqKL(x, yw; π ref ∥π θ ) -DSeqKL(x, y l ; π ref ∥π θ )|. The definition of SeqKL divergence refers to Definition 4.3.", "type_str": "figure"}, "FIGREF1": {"num": null, "fig_num": null, "uris": null, "text": "π ref (y t w |[x,y <t w ]) while simultaneously decreasing the expectation, enlarging the gap between the specified term y t w and the baseline to expedite training. The impact of decreasing the expectation is an increase in the forward KL divergence D KL (π ref (•|[x, y <t w ])|π θ (•|[x, y <t w ])) at each token, leading to an increase in D SeqKL (x, y w ; π ref |π θ ). As we do not aim to accelerate the training speed and prefer to ensure training stability, we modify the loss function by discontinuing the gradient propagation of D SeqKL (x, y w ; π ref |π θ ) and treating it as a baseline term for alignment of D SeqKL (x, y l ; π ref |π θ ). Different from D SeqKL (x, y w ; π ref |π θ ), the gradient of D SeqKL (x, y l ; π ref |π θ ) tends to reduce the sequential KL divergence between π ref and π θ at (x, y l ). For the prompt x and the rejected response y l , the loss function in Eq.16 tends to decrease the likelihood of log π(y t l |[x,y <t l ]) π ref (y t l |[x,y <t l ]) at each token while increasing the expectation E z∼π ref log π θ (z|[x,y <t l ]) π ref (z|[x,y <t l ]", "type_str": "figure"}, "FIGREF2": {"num": null, "fig_num": "2", "uris": null, "text": "Figure 2. Comparison of Loss Functions for DPO, TDPO1 and TDPO2 Methods. The sg denotes the stop-gradient operator. Both TDPO1 and TDPO2 incorporate an additional term for finer-grained control over the KL divergence, compared to DPO.", "type_str": "figure"}, "FIGREF3": {"num": null, "fig_num": "1", "uris": null, "text": "Token-level Direct Preference Optimization (TDPO) 1: Input: Reference model π ref , Policy model π θ , Coefficient α, β, Learning rate η 2: Input: Dataset D = {(x, y w , y l ) i } N i=1 of size N , Method M 3: Initialize: π θ ← π ref 4: for each epoch do 5: Sample mini-batch D m = {(x, y w , y l ) m } M m=1 from D 6: Predict the probabilities π θ (y w |x) and π θ (y l |x) for (x, y w , y l ) in the mini-batch D m using the policy model 7: Predict the probabilities π ref (y w |x) and π ref (y l |x) for (x, y w , y l ) in the mini-batch D m using the reference model 8: Calculate the function u(x, y w , y l ) = β log π θ (yw|x) π ref (yw|x) -β log π θ (y l |x) π ref (y l |x) KL divergence D SeqKL (x, y w ; π ref ∥π θ ) for (x, y w ) in the mini-batch D m 10: Compute the sequential KL divergence D SeqKL (x, y l ; π ref ∥π θ ) for (x, y l ) in the mini-batch D m 11:", "type_str": "figure"}, "FIGREF5": {"num": null, "fig_num": "3", "uris": null, "text": "Figure 3. The experiment on IMDb dataset. Figure 3(a) represents the frontier of expected reward and KL divergence with respect to the reference model. We implemented DPO, TDPO1, and different versions of TDPO2 with respect to the parameter α. Both TDPO1 and TDPO2 outperform DPO in terms of the frontier, with TDPO2 showing further improvement over TDPO1. This demonstrates the effectiveness of our analysis and modifications. Figure 3(b) and Figure 3(c) present the progression of sequential KL divergence on the preferred and dispreferred responses subset over training steps respectively. Figure 3(d) illustrates the difference between the sequential KL divergence on the dispreferred responses subset and that on the preferred responses subset throughout the training process, namely margin = |DSeqKL(x, yw; π ref ∥π θ ) -DSeqKL(x, y l ; π ref ∥π θ )|. TDPO2 exhibit superior regulation over KL divergence compared to the TDPO1 and DPO algorithm.", "type_str": "figure"}, "FIGREF6": {"num": null, "fig_num": "4", "uris": null, "text": "Figure 4. The win rates, computed by GPT-4, in comparison to the chosen responses for Anthropic-HH one-step dialogue.", "type_str": "figure"}, "FIGREF7": {"num": null, "fig_num": null, "uris": null, "text": "y <t ∼D,z∼π θ (•|[x,y <t ]) [A π ref ([x, y <t ], z)].To prevent the excessive degradation of language models, we introduce a reverse KL divergence constraint, forming our objective function:max π θ E x,y <t ∼D,z∼π θ (•|[x,y <t ]) A π ref ([x, y <t ], z) -βD KL π θ (•|[x, y <t ])||π ref (•|[x, y <t ])(25)A.2. Deriving the Mapping between the State-Action Function and the Optimal Policy Lemma A.2. The constrained problem in Eq. 7 has the closed-form solution:π * θ (z|[x, y <t ]) = π ref (z|[x, y <t ]) exp 1 β Q π ref ([x, y <t ], z) Z([x, y <t ]; β) ,(26)whereZ([x, y <t ]; β) = E z∼π ref (•|[x,y <t ]) e1 Qπ ref ([x,y <t ],z) is the partition function.", "type_str": "figure"}, "FIGREF8": {"num": null, "fig_num": "3", "uris": null, "text": "Proving the Equivalence of the Bradley-Terry Model and the Regret Preference Model Lemma A.3. Given a reward function r(x, y), assuming a relationship between token-wise rewards and the reward function represented by r(x, y) = T t=1 γ t-1 R([x, y <t ], y t ), we can establish the equivalence between the <PERSON>-<PERSON> model and the Regret Preference Model in the task of text generation alignment, i.e., ,", "type_str": "figure"}, "FIGREF9": {"num": null, "fig_num": null, "uris": null, "text": "t=1 γ t-1 R([x, y <t ], y t ), we can get: 1 (R([x, y <t ], y t ) + γV π ([x, y <t+1 ]) -γV π ([x, y <t+1 ])) (37) = V π ([x, y <1 ]) + T t=1 γ t-1 R([x, y <t ], y t ) + γV π ([x, y <t+1 ]) -V π ([x, y <t ]) -γ T V π ([x, y <T +1 ])", "type_str": "figure"}, "TABREF0": {"num": null, "text": "-𝛼 𝛽𝐷 SeqKL (𝑥, 𝑦 𝑙 ; 𝜋 ref ||𝜋 𝜃 ) -𝑠𝑔 𝛽𝐷 SeqKL 𝑥, 𝑦 𝑤 ; 𝜋 ref | 𝜋 𝜃", "html": null, "content": "<table><tr><td/><td>𝓛 𝐃𝐏𝐎 𝜋 𝜃 ; 𝜋 ref = -𝔼 log 𝜎 𝛽 log</td><td colspan=\"2\">𝜋 𝜃 (𝑦 𝑤 |𝑥) 𝜋 ref (𝑦 𝑤 |𝑥)</td><td>-𝛽 log</td><td>𝜋 𝜃 (𝑦 𝑙 |𝑥) 𝜋 ref (𝑦 𝑙 |𝑥)</td></tr><tr><td/><td colspan=\"2\">𝓛 𝐓𝐃𝐏𝐎𝟏 𝜋 𝜃 ; 𝜋 ref = -𝔼 log 𝜎 𝛽 log</td><td colspan=\"2\">𝜋 𝜃 (𝑦 𝑤 |𝑥) 𝜋 ref (𝑦 𝑤 |𝑥)</td><td>-𝛽 log</td><td>𝜋 𝜃 (𝑦 𝑙 |𝑥) 𝜋 ref (𝑦 𝑙 |𝑥)</td></tr><tr><td>|𝑥) |𝑥)</td><td colspan=\"4\">-𝛽𝐷 SeqKL (𝑥, 𝑦 𝑙 ; 𝜋 ref ||𝜋 𝜃 ) -𝛽𝐷 SeqKL (𝑥, 𝑦 𝑤 ; 𝜋 ref ||𝜋 𝜃 )</td></tr><tr><td/><td colspan=\"2\">𝓛 𝐓𝐃𝐏𝐎𝟐 𝜋 𝜃 ; 𝜋 ref = -𝔼 log 𝜎 𝛽 log</td><td colspan=\"2\">𝜋 𝜃 (𝑦 𝑤 |𝑥) 𝜋 ref (𝑦 𝑤 |𝑥)</td><td>-𝛽 log</td><td>𝜋 𝜃 (𝑦 𝑙 |𝑥) 𝜋 ref (𝑦 𝑙 |𝑥)</td></tr></table>", "type_str": "table"}, "TABREF1": {"num": null, "text": "Calculate the function δ(x, y w , y l ) = βD SeqKL (x, y l ;π ref ∥π θ ) -βD SeqKL (x, y w ; π ref ∥π θ )▷ Eq.14 13:θ ← θ + η∇ θ E (x,yw,y l )∼Dm [log σ (u(x, y w , y l ) -δ(x, y w , y l ))]Calculate the function δ 2 (x, y w , y l ) = βD SeqKL (x, y l ; π ref ∥π θ ) -sg (βD SeqKL (x, y w ; π ref ∥π θ ))", "html": null, "content": "<table><tr><td>12:</td><td/></tr><tr><td/><td>▷ Eq.15</td></tr><tr><td>14:</td><td>else if Method M is TDPO 2 then</td></tr><tr><td>15:</td><td>▷ Eq.18</td></tr><tr><td>16:</td><td/></tr></table>", "type_str": "table"}, "TABREF2": {"num": null, "text": "Comparison of DPO, TDPO1 and TDPO2 in terms of the trade-off between Alignment (accuracy) and Diversity (entropy) on the Anthropic HH dataset. The ↑ indicates higher values are preferable.", "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">Alignment Accuracy(%) ↑ Entropy ↑ Diversity</td></tr><tr><td>f-DPO(FKL)</td><td>54.71</td><td>4.708</td></tr><tr><td>DPO</td><td>59.43</td><td>3.196</td></tr><tr><td>TDPO1(ours)</td><td>60.08</td><td>4.727</td></tr><tr><td>TDPO2(ours)</td><td>67.33</td><td>4.915</td></tr></table>", "type_str": "table"}, "TABREF4": {"num": null, "text": "A.1. Proving the Relationship between Maximizing the Advantage Function and Enhancing the Expected ReturnsLemma A.1. Given two policies π and π, if for any state s t = [x, y <t ], E z∼π [A π ([x, y <t ], z)] ≥ 0, then we can conclude:", "html": null, "content": "<table/>", "type_str": "table"}, "TABREF5": {"num": null, "text": "E z∼π θ (•|[x,y <t ]) A π ref ([x, y <t ], z) -βD KL π θ (•|[x, y <t ])∥π ref (•|[x, y <t ]) E z∼π θ (•|[x,y <t ]) Q π ref ([x, y <t ], z) -V π ref ([x, y <t ]) + β log π ref (z|[x, y <t ]) π θ (z|[x, y <t ]) Qπ ref ([x,y <t ],z) π θ (z|[x, y <t ]) -V π ref ([x, y <t ]) βE z∼π θ (•|[x,y <t ]) log π ref (z|[x, y <t ])e 1 β Qπ ref ([x,y <t ],z) Z([x, y <t ]; β)π θ (z|[x, y <t ]) -V π ref ([x, y <t ]) + β log Z([x, y <t ]; β) -βD KL π θ (z|[x, y <t ]) π ref (z|[x, y <t ])e 1 β Qπ ref ([x,y <t ],z) Z([x, y <t ]; β) -V π ref ([x, y <t ]) + β log Z([x, y <t ]; β)(31)where Z([x, y <t ]; β) is the partition function:Z([x, y <t ]; β) = E z∼π ref (•|[x,y <t ]) exp 1 β Q π ref ([x, y <t ], z)", "html": null, "content": "<table><tr><td/><td/><td colspan=\"2\">Token-level Direct Preference Optimization</td></tr><tr><td>Proof.</td><td/><td/><td/></tr><tr><td>max π θ</td><td/><td/><td>(27)</td></tr><tr><td>= max π θ</td><td/><td/><td>(28)</td></tr><tr><td>= max π θ</td><td>βE z∼π θ (•|[x,y &lt;t ]) log</td><td>p(z|[x, y &lt;t ])e</td><td>1 β (29)</td></tr><tr><td>= max π θ</td><td/><td/><td>(30)</td></tr><tr><td>= max π θ</td><td/><td/><td/></tr></table>", "type_str": "table"}}}}