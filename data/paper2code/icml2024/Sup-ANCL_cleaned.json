{"paper_id": "Sup-ANCL", "title": "On the Effectiveness of Supervision in Asymmetric Non-Contrastive Learning", "abstract": "Supervised contrastive representation learning has been shown to be effective in various transfer learning scenarios. However, while asymmetric non-contrastive learning (ANCL) often outperforms its contrastive learning counterpart in selfsupervised representation learning, the extension of ANCL to supervised scenarios is less explored. To bridge the gap, we study ANCL for supervised representation learning, coined SUPSIAM and SUPBYOL, leveraging labels in ANCL to achieve better representations. The proposed supervised ANCL framework improves representation learning while avoiding collapse. Our analysis reveals that providing supervision to ANCL reduces intraclass variance, and the contribution of supervision should be adjusted to achieve the best performance. Experiments demonstrate the superiority of supervised ANCL across various datasets and tasks. The code is available at: https: //github.com/JH-Oh-23/Sup-ANCL.", "pdf_parse": {"paper_id": "Sup-ANCL", "abstract": [{"text": "Supervised contrastive representation learning has been shown to be effective in various transfer learning scenarios. However, while asymmetric non-contrastive learning (ANCL) often outperforms its contrastive learning counterpart in selfsupervised representation learning, the extension of ANCL to supervised scenarios is less explored. To bridge the gap, we study ANCL for supervised representation learning, coined SUPSIAM and SUPBYOL, leveraging labels in ANCL to achieve better representations. The proposed supervised ANCL framework improves representation learning while avoiding collapse. Our analysis reveals that providing supervision to ANCL reduces intraclass variance, and the contribution of supervision should be adjusted to achieve the best performance. Experiments demonstrate the superiority of supervised ANCL across various datasets and tasks. The code is available at: https: //github.com/JH-Oh-23/Sup-ANCL.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Self-supervised learning has recently been proven to be an effective paradigm for representation learning (<PERSON> et al., 2020a; <PERSON>, 2021; <PERSON> et al., 2022) . Among various pretext tasks for self-supervised learning, contrastive learning (CL) (<PERSON> et al., 2018; <PERSON> et al., 2020a; <PERSON> et al., 2020) first promised outstanding performance, surpassing the transfer learning performance of supervised pretraining (<PERSON><PERSON><PERSON> et al., 2014) , which learns representations by attracting positive pairs while repelling negative pairs. However, CL requires negative samples to ensure good performance, which might not be possible under limited batch sizes. On the other hand, asymmetric non-contrastive learning (ANCL) (<PERSON><PERSON> et al., 2020; <PERSON> & <PERSON>, 2021) has emerged as a promising alternative to CL, which maximizes the similarity between positive pairs without relying on negative samples. To prevent learned representations from collapsing, ANCL employs an asymmetric structure by placing a predictor after one side of the projector.", "section": "Introduction", "sec_num": "1."}, {"text": "A key component in both CL and ANCL is acquisition of positive pairs, which is typically achieved through data augmentation. Given that datasets for pretraining often include labels, <PERSON><PERSON><PERSON> et al. (2020) proposed to incorporate supervision into CL by treating samples with the same class label as positive pairs as well. Supervised CL has demonstrated superior performance across diverse tasks, such as few-shot learning (<PERSON><PERSON><PERSON> et al., 2021) , long-tail recognition (<PERSON> et al., 2021) , continual learning (<PERSON> et al., 2021) , and natural language processing (<PERSON><PERSON> et al., 2021) .", "section": "Introduction", "sec_num": "1."}, {"text": "While supervision helps to discover more positive samples, it does not directly help to identify effective negative samples. Consequently, ANCL has a better potential to benefit from supervision, as it focuses on positive pairs only. However, in contrast to CL, there are a limited number of studies on leveraging supervision to improve ANCL, despite its strong performance in self-supervised learning. To bridge this gap, we study the effect of supervision in ANCL by introducing the supervised ANCL framework and investigating its behavior through theoretical and empirical analysis.", "section": "Introduction", "sec_num": "1."}, {"text": "To the best of our knowledge, our work is the first to conduct a theoretical analysis of the behavior of representations learned through supervised ANCL. Our experiments confirm the effectiveness of supervision observed through our theoretical analysis, as well as the superiority of representations learned via supervised ANCL across various datasets and tasks. Specifically, as illustrated in Figure 1 , we consider SUPSIAM and SUPBYOL, which are supervised adaptations of the two popular ANCL methods, SIMSIAM (Chen & He, 2021) and BYOL (<PERSON><PERSON> et al., 2020) , respectively. Our contributions are summarized as follows:", "section": "Introduction", "sec_num": "1."}, {"text": "• We propose a supervised ANCL framework for representation learning while avoiding collapse, which surpasses the performance of its self-supervised counterpart when supervision is available.", "section": "Introduction", "sec_num": "1."}, {"text": "• Our analysis demonstrates that incorporating supervision into ANCL reduces the intra-class variance of latent features, and that learning to capture both intra-and interclass variance is crucial for representation learning. We manage a target pool to ensure the existence of positive samples sharing the same class label in the form of z sup 2 . Stop-gradient (sg) applied to z2 and z sup 2 ensures that the gradients propagate through the online branch with the predictor only. The target branch without the predictor either shares parameters with the online branch (SUPSIAM), or exhibits a momentum network (SUPBYOL).", "section": "Introduction", "sec_num": "1."}, {"text": "• Our experiments validate our analysis and demonstrate the superiority of representations learned via supervised ANCL across various datasets and tasks.", "section": "Introduction", "sec_num": "1."}, {"text": "Supervised CL. Although <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> et al., 2020) demonstrated remarkable linear probing performance on pretrained datasets, its efficacy on other downstream datasets is comparable or inferior to that of self-supervised methods.", "section": "Related Works", "sec_num": "2."}, {"text": "In response, subsequent works have been underway to better utilize supervision to enhance representation learning. <PERSON> et al. (2021) proposed to improve CL by taking top-K positive neighbors into account and assigning soft labels to positive samples based on similarity, such that it better reflects task-specific semantic features and task-agnostic appearance features. <PERSON> et al. (2023) argued that naively incorporating supervised signals might conflict with the self-supervised signals. To address this issue, <PERSON> et al. (2023) proposed to impose hierarchical supervisions with an additional projector. <PERSON> et al. (2021) provided both theoretical and empirical evidence demonstrating that the SUPCON loss is minimized when each class collapses to a single point, resulting in poor generalization of learned representations. <PERSON> et al. (2022) found that the SUPCON loss is invariant to class-fixing permutations, indicating that the loss remains unchanged when data points within the same class are arbitrarily permuted in representation space, which also leads to poor generalization of learned representations. <PERSON> et al. (2022) proposed incorporating a weighted class-conditional InfoNCE loss to avoid class collapse, and constraining the encoder, adding a class-conditional autoencoder, and using data augmentation to break permutation invariance. <PERSON><PERSON> et al. (2023) argued that features learned through supervised CL are prone to class collapse, whereas those learned through self-supervised CL suffer from feature suppression, i.e., easy and class-irrelevant features suppress to learn harder and class-relevant features. They claimed that balancing the losses of supervised and self-supervised CL is crucial for improving the quality of learned representations. Notably, these efforts have concentrated on CL, motivating us to investigate the effect of supervision in ANCL. Although several studies on supervised ANCL exist, such as Asadi et al. (2022) and Maser et al. (2023) , their contributions lack a theoretical understanding of the effect of supervision and/or are limited to specific domains.", "section": "Related Works", "sec_num": "2."}, {"text": "Theoretical Analysis on ANCL. While the initial ANCL works (<PERSON><PERSON> et al., 2020; <PERSON> & He, 2021) have demonstrated impressive performance, the learning dynamics that enable effective representation learning without negative pairs while avoiding collapse to trivial solutions remain unclear. <PERSON><PERSON> et al. (2021) elucidated the dynamics of ANCL through the spectral decomposition of the correlation matrix. Specifically, assuming the predictor is linear, they proved that the eigenspace of the learned predictor aligns with the eigenspace of the correlation matrix of the latent features. <PERSON> et al. (2022) empirically observed that, as learning progresses, both the linear predictor and the correlation matrix of latent features converge to a (scaled) identity matrix in ANCL. Based on this observation, they argued that the asymmetric architecture in ANCL implicitly encourages feature decorrelation, achieving a similar effect to symmetric non-CL methods that explicitly decorrelate features such as Barlow Twins (<PERSON><PERSON><PERSON> et al., 2021) and VICReg (<PERSON><PERSON> et al., 2022) . <PERSON><PERSON> et al. (2023) suggested that the predictor in ANCL operates as a low-pass filter, thereby decreasing the rank of the predictor outputs. They argued that the rank difference between the correlation ma-trix of the projector outputs and that of the predictor outputs mitigates dimensional collapse by gradually increasing the effective rank of them as training progresses. Inspired by the prior works on self-supervised ANCL, we analyze supervised ANCL under a similar framework with additional assumptions. On the other hand, <PERSON><PERSON><PERSON> et al. (2023) found that prior works overlook the L2 normalization of projector/predictor outputs, which is a common practice in ANCL, before computing the loss. They investigated the learning dynamics by incorporating the L2 normalization and compared it with the case without the L2 normalization.", "section": "Related Works", "sec_num": "2."}, {"text": "Our work also considers the L2 normalization; however, instead of normalizing the features directly, we consider it as a constraint and employ a La<PERSON>ngian formulation.", "section": "Related Works", "sec_num": "2."}, {"text": "In this section, we first review the problem setting of selfsupervised ANCL. Then, we introduce supervised ANCL. The overall framework is illustrated in Figure 1 .", "section": "Method", "sec_num": "3."}, {"text": "3.1. Preliminary: Self-Supervised ANCL Let f , g, and h be the encoder, projector, and predictor of the online branch, respectively, and f and g be the encoder and projector of the target branch, respectively. For a data point x,", "section": "Method", "sec_num": "3."}, {"text": "let z = (g • f )(x) and p = (h • g • f )(x)", "section": "Method", "sec_num": "3."}, {"text": "be the output of the projector and predictor, respectively. In self-supervised ANCL, two views x 1 and x 2 are generated from the data x through augmentation, and the model learns to minimize the distance between these views encoded at different levels: it compares the prediction of the first view", "section": "Method", "sec_num": "3."}, {"text": "p 1 = (h • g • f )(x 1", "section": "Method", "sec_num": "3."}, {"text": ") with the projection of the second view z 2 = (g • f )(x 2 ). It has been observed that the asymmetric architecture introduced by the predictor h helps prevent representation collapse by predicting the latent feature of the second view z 2 from that of the first view z 1 , i.e., z 2 ≃ p 1 = h(z 1 ) (<PERSON> & <PERSON>, 2021) . The self-supervised ANCL loss ℓ ssl is expressed as:", "section": "Method", "sec_num": "3."}, {"text": "EQUATION", "section": "Method", "sec_num": "3."}, {"text": ")", "section": "Method", "sec_num": "3."}, {"text": "where sg is the stop-gradient operation and p 1 and z 2 are L2normalized. The inclusion of stop-gradient is also crucial for preventing collapsing, making it an essential component of the loss formulation (<PERSON> & He, 2021) .", "section": "Method", "sec_num": "3."}, {"text": "The target branch can either share parameters with the online branch (<PERSON> & He, 2021) , or exhibit a momentum network (<PERSON><PERSON> et al., 2020) . When a momentum network is employed, its parameters follow the exponential moving average (EMA) update rule:", "section": "Method", "sec_num": "3."}, {"text": "θ g• f ← m • θ g• f + (1 -m) • θ g•f ,", "section": "Method", "sec_num": "3."}, {"text": "where m is the momentum, θ g•f is the set of learnable parameters in f and g. The parameters of the target model θ g• f are initialized to those of the online model θ g•f .", "section": "Method", "sec_num": "3."}, {"text": "We propose to enhance supervised ANCL by integrating supervision through an additional loss function: for an anchor x 1 and its supervised target x sup 2 sharing the same label y, the loss minimizes the distance between p 1 = (h•g •f )(x 1 ) and z sup 2 = (g • f )(x sup 2 ). However, the additional loss may not always be effective, because the current batch might not contain any samples sharing the same label as the anchor, particularly when the batch size is small.", "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "To address this issue, we introduce a target pool to ensure the presence of targets sharing the same label as each anchor in the batch, regardless of batch size. Similar to the memory bank utilized in prior works (<PERSON> et al., 2018) , the target pool Q is a queue storing targets z 2 along with their corresponding labels. The target pool offers another advantage that positive samples from the target pool help mitigate collapse because they are updated more slowly than those sampled from the batch, as empirically observed in Table 8 . The proposed target pool is flexible in its design, such that it can be a vanilla queue, a collection of per-class queues ensuring the presence of targets from all labels even when the queue size is small, or a set of learnable class prototypes; the impact of these design choices is investigated in Table 7 . Now, we sample the supervised target z sup 2 sharing the same class as the anchor x from the target pool Q. Specifically, we sample M targets and average them to formulate the supervised ANCL loss ℓ sup :", "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "where p 1 and z ′ 2 are L2-normalized and Q y ⊆ Q is the set of M targets sampled from Q sharing the same label y as x. We sample all positives in Q in experiments, and the effect of M is discussed in Appendix F.2. Finally, the total loss is defined by the convex combination of ℓ ssl and ℓ sup :", "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "where α ∈ [0, 1] adjusts the contribution of ℓ ssl and ℓ sup , and we symmetrize the loss in experiments following the convention. We argue that the introduction of ℓ sup reduces intra-class variance and α adjusts the amount of reduction, where details can be found in Section 4.3.", "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "Note that our strategy for incorporating supervision into the loss differs from that of SUPCON (<PERSON><PERSON><PERSON> et al., 2020) . We first average the supervised loss before combining it with the self-supervised loss, whereas SUPCON weights all persample losses equally, regardless of whether they are selfsupervised or supervised. Since our focus is on analyzing the overall effects of self-supervised and supervised losses rather than per-sample losses, our strategy aligns with the analysis presented in the following section.", "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "In this section, we analyze the effect of supervision in ANCL. We argue that incorporating supervision into ANCL reduces intra-class variance, and that its contribution should be adjusted to achieve better representations. Detailed mathematical proofs are provided in Appendix A.", "section": "Analysis of the Effect of Supervision", "sec_num": "4."}, {"text": "For simplicity in our analysis, we adopt several assumptions from <PERSON><PERSON> et al. (2021) ; <PERSON><PERSON> et al. (2023) :", "section": "Problem Setup", "sec_num": "4.1."}, {"text": "Assumption 4.1. The encoder followed by the projector g • f and the predictor h are linear: z = (g • f )(x) = W x and p = h(z) = W p z, where W p is a symmetric matrix.", "section": "Problem Setup", "sec_num": "4.1."}, {"text": "Assumption 4.2. The distribution of the data augmentation P ( X|X) has a mean X and a covariance matrix σ 2 e I.", "section": "Problem Setup", "sec_num": "4.1."}, {"text": "While previous studies on self-supervised ANCL assume that the distribution of the input data has a zero mean and a scaled identity covariance matrix, class-conditional distributions should be considered when incorporating supervision. Specifically, we assume the class-conditional and class-prior distributions over C classes as follows:", "section": "Problem Setup", "sec_num": "4.1."}, {"text": "Assumption 4.3. The class-prior distribution follows the uniform distribution:", "section": "Problem Setup", "sec_num": "4.1."}, {"text": "P (Y = y) = 1/C.", "section": "Problem Setup", "sec_num": "4.1."}, {"text": "Assumption 4.4. For an input data X and its class Y , the conditional distribution P (X|Y ) is characterized by a mean µ y and a covariance matrix Σ y , where the total mean and total covariance matrix are zero and the identity matrix, respectively: y µ y = 0 and S T = 1 C y µ y µ ⊤ y + Σ y = I.", "section": "Problem Setup", "sec_num": "4.1."}, {"text": "Assumption 4.3 is made for simplicity of analysis; our analysis holds without this assumption, albeit the derivation becomes more complex. Assumption 4.4 can be naturally satisfied through data whitening.", "section": "Problem Setup", "sec_num": "4.1."}, {"text": "For simplicity, assume we sample one target from the pool, i.e., M = 1. We first express the loss in Eq. ( 3) with constraints to ensure the L2 normalization of features:", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "EQUATION", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "where we omit stop-gradient applied to z 2 and z sup 2 for brevity, and the equality in the second line holds due to the linearity of the L2 loss between L2-normalized features with respect to the target (<PERSON> et al., 2021b) . Hence, this optimization can be interpreted as mapping one view z 1 to an interpolated target between another view z 2 and the supervised target z sup 2 . Intuitively, when α = 1, the model cannot determine the exact augmentation applied to x 2 by observing x 1 , such that it predicts z 2 from z 1 through low-rank approximation via principal component analysis (PCA) (<PERSON><PERSON><PERSON> et al., 2023) . Similarly, when α = 0, the model cannot infer the exact supervised target z sup 2 by observing z 1 ; instead, it predicts z sup 2 by mapping z 1 to the class centroid. Here, it has been known that least squares with targets independent of each other (ignoring centering, if applied) is equivalent to linear discriminant analysis (LDA) (<PERSON> & Kim, 2015) , where LDA simultaneously maximizes between-class scatter and minimizes within-class scatter. Hence, we can hypothesize that incorporating supervision into ANCL reduces intra-class variance, and the degree of reduction is controlled by α.", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "To prove that incorporating supervision into ANCL reduces intra-class variance, we establish the following: 1) the optimal predictor W * p generates features of data with reduced intra-class variance by a factor of α, and 2) the optimal W p and W share the same eigenspace, thereby W learns to reduce intra-class variance of features.", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "First, we formulate the <PERSON><PERSON>ngian function of Eq. ( 4) and take the expectation over x 1 , x 2 , and x sup 2 :", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "EQUATION", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "where λ 1 , λ 2 , and λ 3 are the <PERSON><PERSON><PERSON> multipliers. Note that x and x sup are independently sampled from the conditional distribution P (X|Y = y), x 1 and x 2 are independently sampled from P ( X|X = x), and x sup 2 is sampled from P ( X|X = x sup ).", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Proposition 4.5. The covariance matrices of features", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "E z 1 z ⊤ 1 , <PERSON> z 2 z ⊤ 1 , and E z sup 2 z ⊤ 1", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "share the same eigenspace in the data space.", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Proof. From Assumptions 4.1 to 4.4,", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "EQUATION", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "where", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "S B = 1 C y µ y µ ⊤ y is the inter-class covariance, S W = 1 C y Σ y is the intra-class covariance, and S e = σ 2", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "e I is the variance of the augmentation noise. Let S B = V Λ B V ⊤ be the eigendecomposition, where V is an orthogonal matrix and Λ B is a diagonal matrix of the eigenvalues. Then, S T = S B + S W and S e share the same eigenspace with S B , as they are (scaled) identity matrices.", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "EQUATION", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "where Λ W = I -Λ B is the eigenvalue matrix of S W . It can be seen that the covariance matrices of features in Eq. ( 7) share the same eigenspace in the data space.", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Then, we apply the expressions in Proposition 4.5 to the optimal predictor W * p obtained from Eq. ( 5): Theorem 4.6. For an arbitrary W , the optimal predictor W * p that minimizes the loss in Eq. ( 5) is given by", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "W * p = 1 λ 3 W V (Λ B +αΛ W ) Λ B +Λ W +σ 2 e I -1 V ⊤ W + ,", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "where W + is the <PERSON> inverse (<PERSON>, 1955) .", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "From Theorem 4.6, the optimal predictor W * p can be interpreted through a sequence of hypothetical transformations: 1) mapping features to the data space, 2) eliminating the augmentation noise and reducing the intra-class variance by a factor of α, and 3) mapping back to the feature space.", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Next, we show that W * p and W * share the same eigenspace.", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Theorem 4.7. The optimal predictor W * p and the optimal model W * that minimizes the loss in Eq. (5) satisfy", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "W * ⊤ p W * p ≈ W * W * ⊤ .", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Note that Theorem 4.7 holds in self-supervised ANCL as shown in <PERSON><PERSON> et al. (2021) ; <PERSON> et al. (2022) , and it remains valid when supervision is incorporated.", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Finally, we conclude that W * learns to reduce intra-class variance, as W * p generates features of data with reduced intra-class variance by a factor of α from Theorem 4.6, and W * imitates this behavior according to Theorem 4.7.", "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "In the proposed supervised ANCL loss in Eq. ( 3), the coefficient α adjusts the contribution of supervision: decreasing α results in increasing this contribution, thereby reducing intra-class variance, as proved in Section 4.2. Ideally, when intra-class variance is too small, all data within each class converge to a single point, leading to class collapse: data within each class become indistinguishable (<PERSON><PERSON><PERSON> et al., 2020) . Thus, we argue that balancing the contributions of supervision and self-supervision is crucial to achieve semantically aligned yet well-distributed representations in supervised ANCL, leading to the generalization of learned representations; intuitively, the ideal semantic latent space should retain intra-class variance to distinguish data instances.", "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "Table 1 . SUPSIAM results with different α on the toy dataset and ImageNet-100 in several metrics: the self-supervised loss in Eq. ( 1), the supervised loss in Eq. ( 2), the intra-class variance, the relative intra-class variance (%), and the accuracy of k-NN and linear probing (%). For the accuracies, the best results are highlighted in bold and the second-best results are underlined. ", "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "EQUATION", "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": ")", "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "where μy is the y-th class mean and μ is the total mean of features, and the expectation is taken over training dataset.", "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "In Table 1 , ℓ ssl decreases while ℓ sup increases as α increases, which confirms that the contribution of each loss is adjusted as expected. Additionally, the intra-class variance is proportional to α, as proved in Section 4.2. However, the accuracy of k-NN and linear probing exhibits different trends, with the best accuracy achieved when α is between 0 and 1. This supports our claim that while incorporating supervision into ANCL aids in learning semantically aligned representations, excessively reducing intra-class variance may hinder the generalization of learned representations, resulting in diminishing performance on unseen test data.", "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "Figure 2 visualizes the feature space via t-SNE (<PERSON> & <PERSON>, 2008) . When α = 0.5, the class distributions are well-separated while retaining intra-class variance. Decreasing α results in more densely clustered results by skewing the feature space, which might be detrimental to generalization; e.g., the model is overconfident in its predictions for downstream classification tasks. Conversely, increasing α To assess the transferability of learned representations, we conduct transfer learning scenarios in Table 2 . Specifically, we consider three downstream classes, where their means are either interpolated or extrapolated from the pretraining classes, and the scale of the covariance matrix of downstream classes is adjusted by σ to control the difficulty of downstream tasks. As shown in Table 2 , supervised ANCL consistently outperforms self-supervised ANCL (α = 1) across all scenarios, highlighting the effectiveness of incorporating supervision into ANCL. Moreover, the best performance is achieved when 0 < α < 1, suggesting that balancing the contributions of supervision and self-supervision is crucial, i.e., excessively reducing intra-class variance is detrimental to representation learning.", "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "Next, to confirm the scalability of our observations to real-world scenarios, we conduct a similar experiment on ImageNet-100 (<PERSON><PERSON> et al., 2009; <PERSON><PERSON> et al., 2020) by replacing the encoder with ResNet-50 (<PERSON> et al., 2016) and the projector and predictor with MLPs, respectively. As shown in the bottom of Table 1 , the observations remain mostly consistent; although both supervised loss and intra-class variance slightly decrease when α increases from 0.0 to 0.2, we conjecture that this is due to the non-linearity of the optimization. These results further support our claim that balancing the contributions of supervision and self-supervision Similar to the toy experiments, we evaluate the transferability of learned representations in real-world scenarios by conducting transfer learning experiments. Specifically, we apply linear probing to the SUPSIAM-pretrained models on downstream datasets for fine-grained classification tasks, including CUB-200-2011 (<PERSON><PERSON><PERSON> et al., 2010) , Stanford Dogs (<PERSON> et al., 2011) , and Oxford-IIIT Pets (<PERSON> et al., 2012) . As shown in Table 3 , the transfer learning performance exhibits trends similar to those in Table 1 : incorporating supervision into ANCL is beneficial, and balancing the contributions of supervision and self-supervision improves the generalization of representations.", "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "To further elucidate the effect of α in real-world scenarios, we present t-SNE visualizations of latent features from 20 classes, consisting of 15 dogs and 5 birds, subsampled from ImageNet-100. As shown in Figure 3 , classes overlap when no supervision is provided, i.e., when α = 1, and the latent features form more compact clusters as α decreases. Notably, some dog classes (e.g., \"Doberman\" and \"Rottweiler\") overlap when α is small, around 0.0 and 0.2, while they are well-separated when α = 0.5. This implies that excessively reducing intra-class variance with small α might result in collapsing fine-grained classes, which could be detrimental to downstream tasks. ", "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "In this section, we provide experimental results across various datasets and tasks to demonstrate the effectiveness of supervision in ANCL. We also compare CL methods to confirm that ANCL performance is competitive to CL. Detailed experimental settings are provided in Appendix C.", "section": "Experiment", "sec_num": "5."}, {"text": "We consider two ANCL methods, SIMSIAM (<PERSON> & He, 2021) and BYOL (<PERSON><PERSON> et al., 2020) , as our baselines, along with their supervised variations, SUPSIAM and SUPBYOL, as our proposed methods. Additionally, we compare two CL methods, SIMCLR (<PERSON> et al., 2020a) and MOCO-V2 (<PERSON> et al., 2020b) , and their supervised variations, SUPCON (<PERSON><PERSON><PERSON> et al., 2020) and SUPMOCO (<PERSON><PERSON><PERSON> et al., 2021) . Each model consists of a ResNet-50 encoder (<PERSON> et al., 2016) followed by a 2-layer MLP projector and predictor, except for SIMSIAM and SUP<PERSON><PERSON>, which utilize a 3-layer MLP projector following the original configuration by <PERSON> & He (2021) . We pretrain models on ImageNet-100 (<PERSON><PERSON> et al., 2009; <PERSON><PERSON> et al., 2020) for 200 epochs with a batch size of 128. For data augmentation, we apply random crop, random horizontal flip, color jitter, random grayscale, and Gaussian blur, following <PERSON> et al. (2020a) .", "section": "Pretraining", "sec_num": "5.1."}, {"text": "For methods utilizing the target pool, we set the size of the target pool |Q| to 8192 and obtain the supervised target z sup 2 by sampling and averaging all positives in the target pool1 unless otherwise stated. The coefficient α adjusting the contribution of the self-supervised and supervised loss is 0.5, unless otherwise stated. We repeat all experiments with three pretrained models with different random seeds and report the average performance.", "section": "Pretraining", "sec_num": "5.1."}, {"text": "We evaluate the quality of representations on the pretrained distribution through a comparison of linear probing per- formance on ImageNet-100. Specifically, we take the pretrained and frozen backbone, and train a linear classifier on top of it, following the common protocol in prior works (<PERSON> et al., 2020a; b; <PERSON><PERSON> et al., 2020; <PERSON> & He, 2021) .", "section": "Linear Evaluation", "sec_num": "5.2."}, {"text": "As shown in Table 4 , incorporating supervision into ANCL enhances linear probing performance on the pretraining dataset. This suggests that representations learned with supervision more effectively encode the semantic information of the pretrained data distribution.", "section": "Linear Evaluation", "sec_num": "5.2."}, {"text": "To assess the generalizability beyond classification tasks, we evaluate pretraining methods on an object detection task. Following <PERSON> et al. (2020) , we initialize Faster R-CNN (<PERSON> et al., 2015) with each pretrained model and fine-tune it on the VOC07+12 training dataset (<PERSON><PERSON> et al., 2010) . We measure performance using the COCO evaluation metrics (<PERSON> et al., 2014) on the VOC07 test dataset.", "section": "Object Detection", "sec_num": "5.3."}, {"text": "As shown on the right side of Table 4 , incorporating supervision into ANCL improves object detection performance, resulting in the best overall performance. In contrast, the performance gain from supervision in CL is marginal or often detrimental, which aligns with the findings from prior works (<PERSON><PERSON><PERSON> et al., 2020) . This suggests that supervised ANCL yields more generalizable representations, with the potential to achieve superior performance across various downstream tasks.", "section": "Object Detection", "sec_num": "5.3."}, {"text": "For transfer learning, we evaluate the top-1 accuracy across 11 downstream datasets: CIFAR10/CIFAR100 (<PERSON><PERSON><PERSON> & <PERSON>, 2009) , DTD (<PERSON><PERSON><PERSON><PERSON> et al., 2014) , Food (<PERSON><PERSON> et al., 2014) , MIT67 (Quattoni & Torralba, 2009) , SUN397 (<PERSON> et al., 2010 ), Caltech101 (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2004) , CUB200 (<PERSON><PERSON><PERSON> et al., 2010) , Dogs (<PERSON><PERSON> et al., 2011; <PERSON><PERSON> et al., 2009) , Flowers (Nilsback & Zisserman, 2008) , and Pets (<PERSON><PERSON> et al., 2012) , where detailed information is described in Appendix D. For evaluation, we follow the linear probing protocol for transfer learning in prior works (<PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2021a) .", "section": "Transfer Learning via Linear Evaluation", "sec_num": "5.4."}, {"text": "As shown in Table 5 , incorporating supervision improves performance across all pretraining methods. Among them, supervised ANCL methods achieve the best performance: SUPBYOL and SUPSIAM outperform others on 9 out of 11 datasets, demonstrating the superiority of supervised ANCL.", "section": "Transfer Learning via Linear Evaluation", "sec_num": "5.4."}, {"text": "Between supervised ANCL methods, SUPBYOL exhibits better performance than SUPSIAM in terms of the average rank, which might be due to the effect of momentum network. Notably, while the performance gain from incorporating supervision into ANCL is relatively small compared to CL because the self-supervised versions of ANCL already exhibit strong performance, we observe a significant improvement on fine-grained datasets, such as CUB200, Dogs, and Pets. This suggests that learning semantically aligned representations while retaining intra-class variance in ANCL is crucial for recognizing fine-grained information.", "section": "Transfer Learning via Linear Evaluation", "sec_num": "5.4."}, {"text": "To assess the generalizability of learned representations under limited conditions, we conduct transfer learning experi-ments on few-shot classification tasks following the linear probing protocol for few-shot learning in <PERSON> et al. (2021a) .", "section": "Few-Shot Classification", "sec_num": "5.5."}, {"text": "We evaluate the accuracy of 5-way 1-shot and 5-way 5-shot scenarios over 2000 episodes across 8 downstream datasets: Aircraft (<PERSON><PERSON> et al., 2013) , CUB200 (<PERSON><PERSON><PERSON> et al., 2010) , FC100 (<PERSON><PERSON><PERSON> et al., 2018) , Flowers (Nilsback & Zisserman, 2008) , Fungi (Schroeder & Cui, 2018) , Omniglot (Lake et al., 2015) , DTD (Cim<PERSON>i et al., 2014) , and Traffic Signs (<PERSON><PERSON> et al., 2013) . Table 6 shows a similar trend to other experiments that incorporating supervision improves both CL and ANCL, while supervised ANCL achieves the best performance in most cases.", "section": "Few-Shot Classification", "sec_num": "5.5."}, {"text": "In this section, we investigate the design choices for the target pool. In our experiments, the pretraining dataset ImageNet-100 consists of 100 classes, such that the probability of missing any class in the target pool is negligible with a target pool size of 8192. However, with a larger number of classes, some classes might not exist in the target pool if it is updated in a class-agnostic manner. To address this concern, we consider two alternative target pool designs: 1) managing class-wise queues as the target pool, and 2) maintaining learnable class prototypes using the EMA update rule. Additionally, we adjust the size of the class-wise queues to determine the optimal number of latent features required to ensure good performance.", "section": "Ablation Study on Target Pool Design", "sec_num": "5.6."}, {"text": "As shown in Table 7 , performance remains consistent regardless of the target pool design. For the class-wise queues, increasing the number of features stored per class slightly enhances performance, with the best performance observed at 20 features per class, though the gain is overall marginal. In all designs, the size of the target pool grows proportionally to the number of classes and/or the feature dimension, which is equivalent to a linear classifier, such that its memory consumption is negligible; e.g., the linear classifier takes only 2% of the parameters in ResNet-50. Nonetheless, a more sophisticated design of the target pool might be effective, which we leave for future works. Table 8 . Ablation study on the target pool (Pool) and the momentum network (EMA) for avoiding collapse while improving representations learned via supervised ANCL on CIFAR100.", "section": "Ablation Study on Target Pool Design", "sec_num": "5.6."}, {"text": "Pool EMA Collapse k-NN", "section": "Ablation Study on Target Pool Design", "sec_num": "5.6."}, {"text": "✗ ✗ ✓ 1.00 ✓ ✗ ✗ 73.92 ✗ ✓ ✗ 73.32 ✓ ✓ ✗ 74.55", "section": "Ablation Study on Target Pool Design", "sec_num": "5.6."}, {"text": "In this section, we investigate when collapse occurs in supervised ANCL. Specifically, we investigate the effect of the target pool and the momentum network, where the method only with the target pool is essentially SUPSIAM, and the one with both components corresponds to SUPBYOL. We pretrain ResNet-18 followed by a 2-layer MLP projector and predictor on CIFAR100.", "section": "Ablation Study on Representation Collapse", "sec_num": "5.7."}, {"text": "As observed in Table 8 , employing either the target pool or the momentum network effectively prevents collapse. We hypothesize that updating the target differently from the anchor helps to prevent collapse, which is the common behavior of both strategies.", "section": "Ablation Study on Representation Collapse", "sec_num": "5.7."}, {"text": "In this paper, we study supervised asymmetric noncontrastive learning (ANCL) for representation learning.", "section": "Conclusion", "sec_num": "6."}, {"text": "We demonstrate that introducing supervision to ANCL reduces intra-class variance, and that balancing the contributions of the supervised and self-supervised losses is crucial to learn good representations. We experiment the proposed supervised ANCL methods with baselines across various datasets and tasks, demonstrating the effectiveness of supervised ANCL. We believe our work motivates future research to integrate supervised ANCL into their applications.", "section": "Conclusion", "sec_num": "6."}, {"text": "(Non-)contrastive learning typically requires substantial training costs; for instance, training ResNet-50 with MoCo-v2 (<PERSON> et al., 2020b) for 800 epochs requires 9 days on 8 V100 GPUs, raising concerns about environmental impacts, such as carbon emissions. However, the proposed idea of incorporating supervision leads to learning better representations while maintaining similar computational complexity comparable to that of self-supervised learning. This suggests that supervision can mitigate computational demands and potentially address associated environmental concerns.", "section": "Impact Statement", "sec_num": null}, {"text": "A.1. Derivation of Eq. ( 5) To derive this, recall the supervised ANCL loss with constraints in Eq. ( 4):", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "We first expand the loss in Eq. ( 4) and apply constraints to simplify the expression:", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "ℓ = α ∥W p z 1 ∥ 2 2 + ∥z 2 ∥ 2 2 -2z ⊤ 1 W ⊤ p z 2 + (1 -α) ∥W p z 1 ∥ 2 2 + z sup 2 2 2 -2z ⊤ 1 W ⊤ p z sup 2 = α 2 -2z ⊤ 1 W ⊤ p z 2 + (1 -α) 2 -2z ⊤ 1 W ⊤ p z sup 2 = 2 -2α • z ⊤ 1 W ⊤ p z 2 + 2(1 -α) • z ⊤ 1 W ⊤ p z sup 2 . (A.1)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Then, the <PERSON><PERSON>ngian function is formulated as follows:", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "L = 2 -2α • z ⊤ 1 W ⊤ p z 2 -2(1 -α) • z ⊤ 1 W ⊤ p z sup 2 + λ 1 z ⊤ 2 z 2 -1 + λ 2 z sup⊤ 2 z sup 2 -1 + λ 3 z ⊤ 1 W ⊤ p W p z 1 -1 = 2 -2α • tr W ⊤ p z 2 z ⊤ 1 -2(1 -α) • tr W ⊤ p z sup 2 z ⊤ 1 + λ 1 tr z 2 z ⊤ 2 -1 + λ 2 tr z sup 2 z sup⊤ 2 -1 + λ 3 tr W ⊤ p W p z 1 z ⊤ 1 -1 , (A.2)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "where λ 1 , λ 2 and λ 3 are the <PERSON><PERSON><PERSON> multipliers. Finally, taking the expectation over x 1 , x 2 , and x sup 2 yields Eq. ( 5):", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "A.2. Proof of Proposition 4.5 Proposition 4.5. The covariance matrices of features E z 1 z ⊤ 1 , E z 2 z ⊤ 1 , and E z sup 2 z ⊤ 1 share the same eigenspace in the data space.", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Proof. Let S B = 1 C y µ y µ ⊤ y be the inter-class covariance, S W = 1 C y Σ y be the intra-class covariance, and S e = σ 2 e I be the variance of the augmentation noise.", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "On the Effectiveness of Supervision in Asymmetric Non-Contrastive Learning", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Let S B = V Λ B V ⊤ be the eigendecomposition, where V is an orthogonal matrix and Λ B is a diagonal matrix of the eigenvalues. Then, S T = S B + S W and S e share the same eigenspace with S B , as they are (scaled) identity matrices.", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "where Λ W = I -Λ B is the eigenvalue matrix of S W . It can be seen that the covariance matrices of features in Eq. ( 7) share the same eigenspace in the data space.", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "A.3. <PERSON><PERSON> <PERSON> Theorem 4.6", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Theorem 4.6. For an arbitrary W , the optimal predictor W * p that minimizes the loss in Eq. ( 5) is given by", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W * p = 1 λ 3 W V (Λ B +αΛ W ) Λ B +Λ W +σ 2 e I -1 V ⊤ W + ,", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "where W + is the <PERSON> inverse (<PERSON>, 1955) .", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Proof. Recall Eq. ( 5):", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "To derive the optimal W * p , we take the partial derivative ∂L ∂Wp and replace the expression of covariance matrices with Eq. ( 6):", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "∂L ∂W p = -2α • E z 2 z ⊤ 1 -2(1 -α) • E z sup 2 z ⊤ 1 + 2λ 3 W p E z 1 z ⊤ 1 = -2α • W W ⊤ -2(1 -α) • W S B W ⊤ + 2λ 3 1 + σ 2 e • W p W W ⊤ . (A.3)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "By setting ∂L ∂Wp = 0, we obtain the optimal predictor W * p :", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "λ 3 1 + σ 2 e • W * p W W ⊤ = W (αI + (1 -α)S B ) W ⊤ = W (S B + αS W ) W ⊤ . ∴ W * p = 1 λ 3 (1 + σ 2 e I) W (S B + αS W ) W + . (A.4)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Finally, by substituting the covariance matrices with the eigendecomposition as in Eq. ( 6), we obtain the following expression:", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W * p = 1 λ 3 W V (Λ B + αΛ W ) Λ B + Λ W + σ 2 e I -1 V ⊤ W + . (A.5)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "From this expression, the optimal predictor W * p can be interpreted through a sequence of hypothetical transformations: 1) mapping features to the data space, 2) eliminating the augmentation noise and reducing the intra-class variance by a factor of α, and 3) mapping back to the feature space.", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "It is noteworthy that <PERSON><PERSON> et al. (2023) derived an optimal predictor similar to Theorem 4.6. However, their focus was on the elimination of augmentation noise in the feature space in the context of self-supervised learning.", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "A.4. Proof of Theorem 4.7 Theorem 4.7. The optimal predictor W * p and the optimal model W * that minimizes the loss in Eq. (5) satisfy", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W * ⊤ p W * p ≈ W * W * ⊤ .", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Proof. Recall Eq. ( 5):", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "where stop-gradient is applied to z 2 and z sup 2 . Recall the partial derivative ∂L ∂Wp is derived in Eq. (A.3):", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "∂L ∂W p = -2α • E z 2 z ⊤ 1 -2(1 -α) • E z sup 2 z ⊤ 1 + 2λ 3 W p E z 1 z ⊤ 1 = -2α • W W ⊤ -2(1 -α) • W S B W ⊤ + 2λ 3 1 + σ 2 e • W p W W ⊤ . (A.3)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "To derive the partial derivative ∂L ∂W , we express Eq. ( 5) in terms of W 's and x's:", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "L = 2 -2α • tr W ⊤ p W E x 2 x ⊤ 1 W ⊤ -2(1 -α) • tr W ⊤ p W E x sup 2 x ⊤ 1 W ⊤ + λ 1 tr W E x 2 x ⊤ 2 W ⊤ -1 + λ 2 tr W E x sup 2 x sup⊤ 2 W ⊤ -1 + λ 3 tr W ⊤ p W p W E x 1 x ⊤ 1 W ⊤ -1 , (A.6)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "where W 's with stop-gradient are emphasized by W = sg(W ), which are regarded as constants when taking the derivative. Then, the partial derivative ∂L ∂W is derived as follows:", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "∂L ∂W = -2α • W ⊤ p W E x 2 x ⊤ 1 -2(1 -α) • W ⊤ p W E x sup 2 x ⊤ 1 + 2λ 3 • W ⊤ p W p W E x 1 x ⊤ 1 = -2α • W ⊤ p W -2(1 -α) • W ⊤ p W S B + 2λ 3 1 + σ 2 e • W ⊤ p W p W. (A.7)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Left-multiplying Eq. (A.3) by W ⊤ p and right-multiplying Eq. (A.7) by W ⊤ establishes the equality of them:", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W ⊤ p ∂L ∂W p = -2α • W ⊤ p W W ⊤ -2(1 -α) • W ⊤ p W S B W ⊤ + 2λ 3 1 + σ 2 e • W ⊤ p W p W W ⊤ = ∂L ∂W W ⊤ . (A.8)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Now, we consider the update rule with the current iteration number t, the learning rate β, and the weight decay η:", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "dW p dt = -β ∂L ∂W p -ηW p , dW dt = -β ∂L ∂W -ηW. (A.9)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Substituting this expression into Eq. (A.8) results in the following equality:", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W ⊤ p dW p dt + ηW ⊤ p W p = -βW ⊤ p ∂L ∂W p = -β ∂L ∂W W ⊤ = dW dt W ⊤ + ηW W ⊤ . (A.10)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Note that this is a differential equation, where it can be solved by multiplying both side by e 2ηt :", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "d dt e 2ηt W ⊤ p W p = d dt e 2ηt W W ⊤ , (A.11)", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "then, integrating with respect to t and multiplying by e -2ηt yields the solution: We conduct a study on M , which represents the number of positive samples from the target pool. As shown in Table F .2, the model demonstrates robustness to the number of positives. Even when sampling only one positive from the target pool, the performance is similar to sampling many positives. We pretrain SUPSIAM using an increased batch size 256. Additionally, we pretrain SUPCON with a batch size of 256, as the performance in Table 5 pre-trained with a batch size of 128 might be suboptimal. Moreover, to enhance the diversity of positive and negative samples, we also pretrain SUPCON with an additional memory bank (target pool) of size 8192, as described in <PERSON><PERSON><PERSON> et al. (2020) . The learning rate scaled linearly (<PERSON><PERSON> et al., 2017) with the batch size, i.e., for a batch size of 256, the learning rates are set to 0.3 for SUPCON and 0.4 for SUPSI<PERSON>, respectively. As shown in Table F .3, supervised ANCL shows a slight improvement in performance when the batch size is increased to 256, though the gain is overall marginal, and it is not heavily influenced by batch size, similar to its self-supervised counterpart (<PERSON> & He, 2021) . In the case of SUPCON, performance improves as the batch size increases, and memory bank provides performance gain, although this gain seems to be slightly reduced as the batch size increases. However, it still shows lower performance compared to supervised ANCL, which performs well even with a smaller batch size.", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W ⊤ p W p = W W ⊤ + e -2ηt", "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "To verify the independence of our proposed method from the encoder backbone, we conduct experiments using the ViT backbone (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) . In contrastive learning, MOCO-V3 (<PERSON> et al., 2021) utilizes ViT as its backbone, and we benchmark this for implementing models such as SUPMOCO, BYOL, and SUPBYOL. In MOCO-V3, unlike the previous MOCO-V2 (<PERSON> et al., 2020b) , the queue is removed and a predictor is added, resembling ANCL (<PERSON><PERSON> et al., 2020; Chen & He, 2021) . For SUPMOCO with the ViT backbone, we also incorporate the predictor but retain a queue to ensure the existence of features sharing the same label with a size of 8192. Similarly, SUPBYOL employs a target pool with a size of 8192.", "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "We pretrain ViT-Small on ImageNet-100 (<PERSON><PERSON> et al., 2009; <PERSON><PERSON> et al., 2020) for 200 epochs with a batch size of 256.", "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "Common parameter settings include using the AdamW optimizer (<PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2019) with a linear learning rate warm-up for the first 40 epochs, a momentum of 0.9, and a weight decay of 0.1. A cosine learning rate schedule (<PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2017 ) is applied to the encoder and projector. We maintain a constant learning rate without decay for BYOL and SUPBYOL following the prior work (Chen & He, 2021) , while we apply a cosine learning rate schedule to the predictor of MOCO-V3 and SUPMOCO.", "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "• MOCO-V3 (<PERSON> et al., 2021) . We follow the original parameter settings, where the learning rate is set to 1.5e-4 and the temperature parameter for contrastive loss is 0.2. The exponential moving average (EMA) parameter starts from 0.99 and is increased to one during training. The projector consists of 3 MLP layers with an output dimension of 256 and a hidden dimension of 4096. The predictor has 2 MLP layers with a hidden dimension of 4096.", "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "• SUPMOCO (<PERSON><PERSON><PERSON> et al., 2021) . The learning rate is set to 1.5e-3 and the temperature parameter for contrastive loss is 0.2. The EMA parameter starts from 0.99 and is increased to one during training. The projector consists of 3 MLP layers with an output dimension of 256 and a hidden dimension of 4096. The predictor has 2 MLP layers with a hidden dimension of 4096.", "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "• BYOL (<PERSON><PERSON> et al., 2020) . The learning rate is set to 1.5e-3 and the EMA parameter starts from 0.996 and is increased to one during training. The projector consists of 2 MLP layers with an output dimension of 256 and a hidden dimension of 4096. The predictor has 2 MLP layers with a hidden dimension of 4096.", "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "• SUPBYOL. The learning rate is set to 1.5e-3 and the EMA parameter starts from 0.996 and is increased to one during training. The loss parameter α is set to 0.5 and all supervised target is obtained by sampling and averaging all positives in the target pool. The projector consists of 2 MLP layers with an output dimension of 256 and a hidden dimension of 4096. The predictor has 2 MLP layers with a hidden dimension of 4096.", "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "Table G .1. Transfer learning via linear evaluation results on various downstream datasets, where the models trained with the ViT-Small backbone on ImageNet-100. CL, Sup, EMA stand for the cases when negative samples are considered, labels are used for pretraining, and the momentum network is adopted, respectively. Avg.Rank represents the average performance ranking across all datasets. For each dataset, the best results are in bold and the second-best results are underlined. Our proposed methods are marked with †. We observe a slightly lower performance in Table G .1 compared to Table 5 , where results are presented using ResNet-50 (<PERSON> et al., 2016) as the backbone. This discrepancy is likely due to pretraining with ImageNet-100. ViT typically requires more data for effective learning compared to ResNet, and the number of data samples in ImageNet-100 may be slightly insufficient. Nevertheless, supervision in the ANCL framework with the ViT backbone proves effective in enhancing performance. Notably, when compared to supervised contrastive learning, proposed method exhibits slightly better performance across all datasets except one. This underscores the effectiveness of the supervised ANCL approach, which is applicable to the ViT backbone and remains independent of the underlying architecture.", "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "We conduct additional experiments on the CIFAR (<PERSON><PERSON>, 2009) dataset, where the image size was reduced to 32×32. The encoder employes a CIFAR variant of ResNet-18 (<PERSON> et al., 2016) and is trained for a total of 1000 epochs with a batch size of 256. For the ANCL approach, specifically SIMSIAM and SUPSIAM, we utilize a 2-layer MLP projector, and Gaussian blurring is excluded from the augmentation. For contrastive learning, we select SIMCLR (<PERSON> et al., 2020a) and its supervised version SUPCON (<PERSON><PERSON><PERSON> et al., 2020) . For ANCL, SIMSIAM (Chen & He, 2021) and BYOL (<PERSON><PERSON> et al., 2020) and their supervised counterparts SUPSIAM and SUPBYOL are chosen as models. Learning rates are tuned individually for each model: SIMCLR (0.7), SUPCON (0.6), BYOL (0.6), SUPBYOL (0.5), SIMSIAM (0.7), and SUPSIAM (0.7). For supervised ANCL, the target pool size is reduced to 4096, and the loss parameter α is set to 0.5 for SUPSIAM and 0.8 for SUPBYOL. The results in Table H .1 indicate that when the pretext and downstream datasets are the same, the introduction of supervision leads to an increase in linear accuracy. Conversely, in cases where they differ, contrastive learning shows a decline or slight increase in performance. Asymmetric non-contrastive learning, on the other hand, benefits from labels, resulting in increased accuracy and showcasing the best performance. Thus, our proposed supervised ANCL proves to be an effective method for obtaining high-quality representations across various datasets.", "section": "<PERSON>. Pretraining on CIFAR", "sec_num": null}, {"text": "Our proposed methods are robust to the number of positives from the target pool, as shown in Table F.2.", "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by the National Research Foundation of Korea (NRF) grant funded by the Korea government (MSIT) (2022R1A4A1033384) and the Yonsei University Research Fund (2024-22-0148). We thank <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> for helpful discussions.", "section": "Acknowledgements", "sec_num": null}, {"text": "where c is a constant with respect to t. Finally, the constant becomes negligible as t → ∞, i.e., at the optimal state, such that we obtain the following expression:The equality implies that they share the eigenspace.Note that Theorem 4.7 holds in self-supervised ANCL as shown in <PERSON><PERSON> et al. (2021) ; <PERSON> et al. (2022) , and it remains valid when supervision is incorporated.", "section": "annex", "sec_num": null}, {"text": "We provide a detailed description of toy experiments in Section 4.3. We generate a synthetic toy dataset to verify that balancing the contributions of supervision and self-supervision is crucial for the generalization of learned representations. The dataset consists of three classes, each following a Gaussian distribution with orthogonal mean vectors and a shared isotropic covariance matrix with a scale of 0.35. The mean vectors are obtained by taking the left singular vectors of a random matrix sampled from a standard Gaussian distribution. The synthetic data has 2048 dimensions, and data augmentation is performed by replacing 60% of the dimensions with the corresponding dimensions of the overall data mean vector. The training dataset consists of 3000 samples, with 1000 samples per class, and similarly, the test dataset consists of 1500 samples, with 500 samples per class.For the supervised ANCL approach, SUPSIAM is utilized with varying α, where the encoder, projector, and predictor each consist of a linear layer without batch normalization (<PERSON><PERSON><PERSON> & <PERSON>ze<PERSON>y, 2015) . The output dimension of the projector/predictor is set to 128. The model is trained for 200 epochs using the SGD optimizer, with a batch size of 256, learning rate of 0.05, momentum of 0.9, and weight decay of 5e-4. A cosine learning rate schedule (<PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2017) is applied except for the predictor, following the prior work (<PERSON>, 2021) .", "section": "B. Toy Experiment Setup", "sec_num": null}, {"text": "We provide a detailed description of the pretraining setup. Each model consists of a ResNet-50 encoder (<PERSON> et al., 2016) followed by a 2-layer MLP projector and predictor, except for SIMSIAM and SUPSIAM, which utilize a 3-layer MLP projector following the original configuration by <PERSON> & <PERSON> (2021) . We pretrain models on ImageNet-100 (<PERSON><PERSON> et al., 2009; <PERSON><PERSON> et al., 2020) for 200 epochs with a batch size of 128. We utilize the SGD optimizer with a momentum of 0.9, and a weight decay of 1e-4. A cosine learning rate schedule (<PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2017) is applied to the encoder and projector.We maintain a constant learning rate without decay for the predictor, following the prior work (<PERSON> & <PERSON>, 2021) . Other method-specific details are provided below:• SIMCLR (<PERSON> et al., 2020a) . The learning rate is set to 0.1 and the temperature parameter for contrastive loss is 0.1. The projector consists of 2 MLP layers with an output dimension of 128.where z 2 and z a are L2-normalized and Z n is the set of negative pairs of z 1 obtained from the same batch.• SUPCON (<PERSON><PERSON><PERSON> et al., 2020) . The learning rate is set to 0.15 and the temperature parameter for contrastive loss is 0.1. The projector consists of 2 MLP layers with an output dimension of 128.where z 2 and z a are L2-normalized, B ′ is the set of positive pairs of z 1 obtained from the same batch, with a cardinality of M + 1 and Z n is the set of negative pairs of z 1 obatined from the same batch.• MOCO-V2 (Chen et al., 2020b) . The learning rate is set to 0.03 and the temperature parameter for contrastive loss is 0.2. The size of memory bank (target pool) |Q| is 8192, and the exponential moving average (EMA) parameter is 0.999. The projector consists of 2 MLP layers with an output dimension of 128.where z 2 and z a are L2-normalized and Z n is the set of negative pairs of z 1 obtained from the queue.• SUPMOCO (Majumder et al., 2021) . The learning rate is set to 0.1 and temperature parameter is 0.07. The size of memory bank (target pool) |Q| is 8192 and the EMA parameter is 0.999. The projector consists of 2 MLP layers with an output dimension of 128.where z 2 , z a and z j are L2-normalized, Q ′ is the set of positive pairs of z 1 obtained from the same batch and the queue, with a cardinality of M + 1 and Z n is the set of negative pairs of z 1 obtained from the same batch and the queue.• BYOL (Grill et al., 2020) . The learning rate is set to 0.2. The EMA parameter starts from 0.996 and is increased to one during training. The projector consists of 2 MLP layers with an output dimension of 256. The predictor has 2 MLP layers with a hidden dimension of 4096.where p 1 and z 2 are L2-normalized and sg denotes the stop-gradient.• SUPBYOL. The learning rate is set to 0.2. The size of target pool |Q| is 8192 and the supervised target z sup 2 is obtained by sampling and averaging all positives in the target pool. The EMA parameter starts from 0.996 and is increased to one during training. The projector consists of 2 MLP layers with an output dimension of 256, and the predictor has 2 MLP layers with a hidden dimension of 4096.where p 1 , z 2 and z ′ 2 are L2-normalized, sg denotes the stop-gradient, and Q y ⊆ Q is the set of targets of p 1 sampled from the target pool sharing the sample label with p 1 , with a cardinality of M .• SIMSIAM (Chen et al., 2020a) . The learning rate is set to 0.2 with a linear learning rate warm-up for the first 40 epochs.The projector consists of 3 MLP layers with an output dimension of 2048. The predictor has 2 MLP layers with a hidden dimension of 512.where p 1 and z 2 are L2-normalized and sg denotes the stop-gradient.• SUPSIAM. The learning rate is set to 0.2 with a linear learning rate warm-up for the first 40 epochs. The size of target pool |Q| is 8192 and the supervised target z sup 2 is obtained by sampling and averaging all positives in the target pool. The projector consists of 3 MLP layers with an output dimension of 2048, and the predictor has 2 MLP layers with a hidden dimension of 512.where p 1 , z 2 and z ′ 2 are L2-normalized, sg denotes the stop-gradient, and Q y ⊆ Q is the set of targets of p 1 sampled from the target pool sharing the sample label with p 1 , with a cardinality of M .", "section": "C. Pretraining Setup", "sec_num": null}, {"text": "Table D .1 provides a comprehensive overview of datasets, including evaluation metrics for both (a) transfer learning via linear evaluation and (b) few-shot classification. For datasets without an official validation set, a random split is performed using the entire training set. For the few-shot task, the complete dataset is utilized for all datasets except FC100 (<PERSON><PERSON><PERSON> et al., 2018) . In the case of FC100 (<PERSON><PERSON><PERSON> et al., 2018) , a meta-test split is used. Detailed evaluation protocols are outlined in Appendix E.On the Effectiveness of Supervision in Asymmetric Non-Contrastive Learning (Krizhevsky & Hinton, 2009) 100 45000 5000 10000 Top-1 accuracy DTD (split 1) (<PERSON><PERSON><PERSON><PERSON> et al., 2014) 47 1880 1880 1880 Top-1 accuracy Food (<PERSON><PERSON> et al., 2014) 101 68175 7575 25250 Top-1 accuracy MIT67 (<PERSON>ua<PERSON>i & <PERSON>ba, 2009) 67 4690 670 1340 Top-1 accuracy SUN397 (split 1) (<PERSON> et al., 2010) 397 15880 3970 19850 Top-1 accuracy Caltech101 (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2004) 101 2525 505 5647 Mean per-class accuracy CUB200 (<PERSON><PERSON><PERSON> et al., 2010) 200 4990 1000 5794 Mean per-class accuracy Dogs (<PERSON><PERSON><PERSON> et al., 2011; <PERSON><PERSON> et al., 2009) 120 10800 1200 8580 Mean per-class accuracy Flowers (<PERSON><PERSON><PERSON> & <PERSON>, 2008) 102 1020 1020 6149 Mean per-class accuracy (b) Few-shot classification Aircraft (Maji et al., 2013 ) 100 10000 Average accuracy CUB200 (Welinder et al., 2010) 200 11745 Average accuracy FC100 (Oreshkin et al., 2018) 20 12000 Average accuracy Flowers (Nilsback & Zisserman, 2008) 102 8189 Average accuracy Fungi (Schroeder & Cui, 2018) 1394 89760 Average accuracy Omniglot (Lake et al., 2015) 1623 32460 Average accuracy DTD (Cimpoi et al., 2014) 47 5640 Average accuracy Traffic Signs (Houben et al., 2013) 43 12630 Average accuracy", "section": "D. Datasets", "sec_num": null}, {"text": "The linear evaluation protocol for transfer learning follows from <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2019) and <PERSON> et al. (2021a) . Specifically, we divide the entire training dataset into a train set and a validation set to tune the regularization parameter by minimizing the L2-regularized cross-entropy loss using L-BFGS (Liu & Nocedal, 1989) . Train and validation set splits are shown in Table D .1. With the best parameter, we extract the frozen representations of 224 × 224 center-cropped images without data augmentation and train the linear classifier with the entire training dataset, including the validation set.", "section": "E.1. Transfer Learning via Linear Evaluation", "sec_num": null}, {"text": "We adhere to the few-shot classification evaluation protocol outlined by <PERSON> et al. (2021a) . Specifically, we conduct logistic regression using the frozen representations extracted from 224 × 224 images without data augmentation in an N -way K-shot episode. It's important to note that as the encoder remains frozen, this protocol does not involve a fine-tuning approach.", "section": "E.2. Few-Shot Classfication", "sec_num": null}, {"text": "We conduct additional experiments with SUPSIAM, varying the loss parameter α, the number of positives, denoted as M and the batch size. During the experiments on batch size, we also incorporate contrastive learning, specifically SUPCON. Given that the performance recorded in the Table 5 might be suboptimal due to pretraining with a batch size of 128, which could be too small, we re-pretrain SUPCON using an increased batch size. Unless specified otherwise, the remaining settings follow the setup outlined in Appendix C. We apply the evaluation methodology outlined in Appendix E to the dataset introduced in Appendix D.", "section": "<PERSON><PERSON> Additional Experiments", "sec_num": null}, {"text": "We conduct experiments with various α values to explore the relationship between intra-class variance reduction and representation quality. Table F .1 presents the linear evaluation performances for different α values. The model performs best in most cases when α is set to 0.5. Interestingly, the optimal α appears to vary depending on the downstream dataset. Nevertheless, it is crucial to note that α should always fall within the range (0, 1) to effectively capture within-class diversity, thereby proving beneficial for downstream tasks.", "section": "F.1. Transfer Learning with Different α", "sec_num": null}], "ref_entries": {"FIGREF0": {"type_str": "figure", "fig_num": "1", "text": "Figure 1. Our proposed supervised ANCL framework. The components we added to the standard ANCL are highlighted with a red box.We manage a target pool to ensure the existence of positive samples sharing the same class label in the form of z sup 2 . Stop-gradient (sg) applied to z2 and z sup 2 ensures that the gradients propagate through the online branch with the predictor only. The target branch without the predictor either shares parameters with the online branch (SUPSIAM), or exhibits a momentum network (SUPBYOL).", "num": null, "uris": null}, "FIGREF1": {"type_str": "figure", "fig_num": null, "text": "Figure 2. t-SNE visualization of SUPSIAM features with different α on the toy dataset.", "num": null, "uris": null}, "FIGREF2": {"type_str": "figure", "fig_num": "3", "text": "Figure 3. t-SNE visualization of SUPSIAM features with different α on 15 dog and 5 bird classes from ImageNet-100.", "num": null, "uris": null}, "TABREF1": {"type_str": "table", "content": "<table><tr><td>α</td><td/><td>Interpolation</td><td/><td/><td>Extrapolation</td><td/></tr><tr><td/><td colspan=\"6\">σ = 0.2 σ = 0.5 σ = 0.8 σ = 0.2 σ = 0.5 σ = 0.8</td></tr><tr><td>0.0</td><td>43.60</td><td>37.44</td><td>35.40</td><td>96.67</td><td>83.76</td><td>74.42</td></tr><tr><td>0.2</td><td>44.02</td><td>37.24</td><td>35.31</td><td>97.13</td><td>84.71</td><td>75.09</td></tr><tr><td>0.5</td><td>44.25</td><td>37.96</td><td>35.69</td><td>97.60</td><td>85.87</td><td>76.00</td></tr><tr><td>0.8</td><td>44.24</td><td>37.65</td><td>35.98</td><td>97.07</td><td>83.69</td><td>73.73</td></tr><tr><td>1.0</td><td>40.40</td><td>36.60</td><td>35.67</td><td>75.84</td><td>59.67</td><td>53.00</td></tr><tr><td colspan=\"7\">leads to mixed class distributions, impairing classification.</td></tr></table>", "text": "Transfer learning results on toy downstream datasets with different means and varying scale of covariance σ, with SUPSIAMpretraining on the toy dataset. For each scenario, the best results are in bold and the second-best results are underlined.", "num": null, "html": null}, "TABREF2": {"type_str": "table", "content": "<table><tr><td>α</td><td colspan=\"2\">CUB200 Dogs</td><td>Pets</td></tr><tr><td>0.0</td><td>41.46</td><td colspan=\"2\">61.51 80.09</td></tr><tr><td>0.2</td><td>42.07</td><td colspan=\"2\">64.28 82.27</td></tr><tr><td>0.5</td><td>43.48</td><td colspan=\"2\">64.65 82.38</td></tr><tr><td>0.8</td><td>42.16</td><td colspan=\"2\">62.94 81.76</td></tr><tr><td>1.0</td><td>36.10</td><td colspan=\"2\">54.57 75.13</td></tr><tr><td colspan=\"4\">is crucial for the generalization of representations learned</td></tr><tr><td colspan=\"2\">via supervised ANCL.</td><td/></tr></table>", "text": "Transfer learning results on fine-grained classification datasets, where the model is SUPSIAM-pretrained with different α on ImageNet-100. For each dataset, the best results are in bold and the second-best results are underlined.", "num": null, "html": null}, "TABREF3": {"type_str": "table", "content": "<table><tr><td>Dataset</td><td>ImageNet-100</td><td>VOC</td><td/></tr><tr><td>Method</td><td>Top-1</td><td>AP</td><td>AP50</td></tr><tr><td>SIMCLR</td><td>77.35</td><td colspan=\"2\">52.06 ± 0.31 78.70 ± 0.16</td></tr><tr><td>SUPCON</td><td>87.40</td><td colspan=\"2\">52.53 ± 0.47 79.44 ± 0.21</td></tr><tr><td>MOCO-V2</td><td>78.37</td><td colspan=\"2\">52.68 ± 0.04 79.08 ± 0.24</td></tr><tr><td>SUPMOCO</td><td>86.33</td><td colspan=\"2\">52.67 ± 0.04 79.52 ± 0.15</td></tr><tr><td>SIMSIAM</td><td>82.15</td><td colspan=\"2\">53.56 ± 0.10 79.82 ± 0.10</td></tr><tr><td>SUPSIAM  †</td><td>87.31</td><td colspan=\"2\">53.89 ± 0.26 80.28 ± 0.06</td></tr><tr><td>BYOL</td><td>84.93</td><td colspan=\"2\">53.54 ± 0.04 79.57 ± 0.01</td></tr><tr><td>SUPBYOL  †</td><td>87.43</td><td colspan=\"2\">53.69 ± 0.24 80.26 ± 0.17</td></tr></table>", "text": "Top-1 linear probing accuracy on ImageNet-100 and transfer learning performance on VOC object detection. The best results are in bold and the second-best results are underlined. Our proposed methods are marked with †.", "num": null, "html": null}, "TABREF4": {"type_str": "table", "content": "<table><tr><td>Method</td><td colspan=\"13\">CL Sup EMA Avg.Rank CIFAR10 CIFAR100 DTD Food MIT67 SUN397 Caltech CUB200 Dogs Flowers Pets</td></tr><tr><td>SIMCLR</td><td>✓</td><td/><td/><td>7.00</td><td>84.69</td><td>62.86</td><td>64.18 60.91 61.81</td><td>47.10</td><td>77.89</td><td>28.76</td><td>44.33</td><td>84.30</td><td>65.10</td></tr><tr><td>SUPCON</td><td>✓</td><td>✓</td><td/><td>4.73</td><td>88.82</td><td>68.89</td><td>65.18 59.34 63.76</td><td>50.09</td><td>87.30</td><td>35.84</td><td>61.68</td><td>89.05</td><td>80.12</td></tr><tr><td>MOCO-V2</td><td>✓</td><td/><td>✓</td><td>7.82</td><td>83.43</td><td>61.54</td><td>61.81 57.36 59.55</td><td>45.07</td><td>77.26</td><td>27.79</td><td>46.67</td><td>82.35</td><td>68.52</td></tr><tr><td>SUPMOCO</td><td>✓</td><td>✓</td><td>✓</td><td>4.09</td><td>89.05</td><td>69.29</td><td>65.44 59.04 63.46</td><td>50.05</td><td>87.54</td><td>37.75</td><td>62.80</td><td>89.69</td><td>80.81</td></tr><tr><td>SIMSIAM</td><td/><td/><td/><td>4.91</td><td>87.28</td><td>66.41</td><td>66.06 63.44 64.68</td><td>50.69</td><td>85.00</td><td>36.10</td><td>54.57</td><td>88.38</td><td>75.13</td></tr><tr><td>SUPSIAM  †</td><td/><td>✓</td><td/><td>2.27</td><td>89.95</td><td>70.88</td><td>66.51 61.46 64.45</td><td>51.50</td><td>88.86</td><td>43.48</td><td>64.65</td><td>90.27</td><td>82.38</td></tr><tr><td>BYOL</td><td/><td/><td>✓</td><td>3.82</td><td>88.26</td><td>68.08</td><td>67.52 64.63 65.70</td><td>51.21</td><td>85.85</td><td>37.10</td><td>57.80</td><td>88.14</td><td>78.78</td></tr><tr><td>SUPBYOL  †</td><td/><td>✓</td><td>✓</td><td>1.36</td><td>90.85</td><td>72.04</td><td>67.38 64.58 66.64</td><td>52.95</td><td>88.79</td><td>43.24</td><td>65.02</td><td>91.09</td><td>82.68</td></tr></table>", "text": "Transfer learning via linear evaluation results on various downstream datasets, where models are pretrained on ImageNet-100. CL, Sup., EMA stand for the cases when negative samples are considered, labels are used for pretraining, and the momentum network is adopted, respectively. Avg. Rank represents the average performance ranking across all datasets. For each dataset, the best results are in bold and the second-best results are underlined. Our proposed methods are marked with †.", "num": null, "html": null}, "TABREF5": {"type_str": "table", "content": "<table><tr><td>Method</td><td colspan=\"4\">CL Sup EMA Avg.Rank</td><td>Aircraft</td><td>CUB200</td><td>FC100</td><td>Flowers</td><td>Fungi</td><td>Omniglot</td><td>DTD</td><td>Traffic Signs</td></tr><tr><td>5-way 1-shot</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>SIMCLR</td><td>✓</td><td/><td/><td>7.25</td><td colspan=\"6\">29.22 ± 0.34 40.61 ± 0.43 35.53 ± 0.37 68.26 ± 0.50 42.44 ± 0.44 70.46 ± 0.54 55.43 ± 0.45</td><td>48.33 ± 0.43</td></tr><tr><td>SUPCON</td><td>✓</td><td>✓</td><td/><td>3.63</td><td colspan=\"6\">31.44 ± 0.35 48.75 ± 0.49 45.32 ± 0.41 77.99 ± 0.44 47.42 ± 0.45 80.66 ± 0.45 57.57 ± 0.47</td><td>68.66 ± 0.47</td></tr><tr><td>MOCO-V2</td><td>✓</td><td/><td>✓</td><td>7.38</td><td colspan=\"6\">25.54 ± 0.28 41.24 ± 0.46 36.73 ± 0.36 66.48 ± 0.50 41.84 ± 0.44 71.12 ± 0.51 54.75 ± 0.46</td><td>51.05 ± 0.43</td></tr><tr><td>SUPMOCO</td><td>✓</td><td>✓</td><td>✓</td><td>3.25</td><td colspan=\"6\">31.12 ± 0.35 49.04 ± 0.49 44.13 ± 0.41 78.90 ± 0.43 47.12 ± 0.45 83.43 ± 0.42 56.62 ± 0.46</td><td>71.17 ± 0.47</td></tr><tr><td>SIMSIAM</td><td/><td/><td/><td>5.00</td><td colspan=\"6\">30.67 ± 0.35 45.06 ± 0.47 41.51 ± 0.40 75.68 ± 0.47 45.22 ± 0.46 74.64 ± 0.50 58.28 ± 0.47</td><td>60.03 ± 0.45</td></tr><tr><td>SUPSIAM  †</td><td/><td>✓</td><td/><td>1.88</td><td colspan=\"6\">33.12 ± 0.37 49.58 ± 0.49 45.56 ± 0.41 78.12 ± 0.44 47.74± 0.46 84.02 ± 0.41 58.06 ± 0.48</td><td>71.00 ± 0.48</td></tr><tr><td>BYOL</td><td/><td/><td>✓</td><td>5.63</td><td colspan=\"6\">26.38 ± 0.30 46.45 ± 0.49 40.92 ± 0.40 74.27 ± 0.47 45.96 ± 0.46 68.13 ± 0.52 59.75 ± 0.48</td><td>57.44 ± 0.46</td></tr><tr><td>SUPBYOL  †</td><td/><td>✓</td><td>✓</td><td>2.00</td><td colspan=\"6\">32.66 ± 0.37 49.26 ± 0.48 45.28 ± 0.41 78.94 ± 0.43 47.81 ± 0.46 82.62 ± 0.44 59.98 ± 0.48</td><td>70.34 ± 0.48</td></tr><tr><td>5-way 5-shot</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>SIMCLR</td><td>✓</td><td/><td/><td>7.13</td><td colspan=\"6\">39.21 ± 0.44 54.33 ± 0.45 50.96 ± 0.37 86.98 ± 0.30 59.40 ± 0.47 86.72 ± 0.35 73.95 ± 0.36</td><td>69.27 ± 0.40</td></tr><tr><td>SUPCON</td><td>✓</td><td>✓</td><td/><td>3.38</td><td colspan=\"6\">44.63 ± 0.44 64.99 ± 0.46 64.04 ± 0.39 92.80 ± 0.23 66.75 ± 0.47 93.36 ± 0.25 75.60 ± 0.36</td><td>85.93 ± 0.36</td></tr><tr><td>MOCO-V2</td><td>✓</td><td/><td>✓</td><td>7.50</td><td colspan=\"6\">32.84 ± 0.35 53.42 ± 0.47 52.70 ± 0.36 84.72 ± 0.32 57.54 ± 0.48 87.74 ± 0.34 72.66 ± 0.37</td><td>71.93 ± 0.39</td></tr><tr><td colspan=\"11\">SUPMOCO 44.43 SIMSIAM ✓ ✓ ✓ 2.63 5.25 40.34 ± 0.44 60.66 ± 0.48 58.68 ± 0.38 91.04 ± 0.26 62.19 ± 0.49 88.92 ± 0.32 76.22 ± 0.36</td><td>79.50 ± 0.39</td></tr><tr><td>SUPSIAM  †</td><td/><td>✓</td><td/><td>2.38</td><td colspan=\"6\">45.98 ± 0.47 66.70 ± 0.45 64.54 ± 0.39 92.42 ± 0.23 66.61 ± 0.48 94.38 ± 0.23 76.43 ± 0.36</td><td>86.88 ± 0.36</td></tr><tr><td>BYOL</td><td/><td/><td>✓</td><td>5.38</td><td colspan=\"6\">35.30 ± 0.40 60.96 ± 0.49 59.33 ± 0.38 90.38 ± 0.26 63.12 ± 0.49 85.68 ± 0.35 77.60 ± 0.36</td><td>77.07 ± 0.40</td></tr><tr><td>SUPBYOL  †</td><td/><td>✓</td><td>✓</td><td>2.38</td><td>45.81</td><td/><td/><td/><td/></tr></table>", "text": "Few-shot classification accuracy averaged over 2000 episodes on various datasets, where models are pretrained on ImageNet-100. CL, Sup, EMA stand for the cases when negative samples are considered, labels are used for pretraining, and the momentum network is adopted, respectively. Avg.Rank represents the average performance ranking across all datasets. For each dataset, the best results are in bold and the second-best results are underlined. Our proposed methods are marked with †. ± 0.44 65.63 ± 0.46 64.30 ± 0.39 93.35 ± 0.21 66.64 ± 0.47 94.77 ± 0.22 74.73 ± 0.36 87.64 ± 0.34 ± 0.48 66.72 ± 0.46 65.72 ± 0.38 92.78 ± 0.22 66.47 ± 0.48 94.06 ± 0.24 77.57 ± 0.36 86.21 ± 0.37", "num": null, "html": null}, "TABREF6": {"type_str": "table", "content": "<table><tr><td>Target Pool</td><td>Size</td><td>Avg</td><td colspan=\"9\">CIFAR10 CIFAR100 DTD Food MIT67 SUN397 Caltech CUB200 Dogs Flowers Pets</td></tr><tr><td>Class-agnostic</td><td>8192</td><td>70.40</td><td>89.95</td><td>70.88</td><td>66.51 61.46 64.45</td><td>51.50</td><td>88.86</td><td>43.48</td><td>64.65</td><td>90.27</td><td>82.38</td></tr><tr><td>Class-wise</td><td colspan=\"2\">80 × 100 70.21</td><td>89.78</td><td>70.58</td><td>66.49 61.54 64.85</td><td>51.22</td><td>88.64</td><td>42.68</td><td>65.03</td><td>89.86</td><td>81.61</td></tr><tr><td>Class-wise</td><td colspan=\"2\">20 × 100 70.44</td><td>90.02</td><td>71.07</td><td>66.92 61.49 65.11</td><td>51.15</td><td>88.67</td><td>43.19</td><td>65.16</td><td>89.27</td><td>82.39</td></tr><tr><td>Class-wise</td><td colspan=\"2\">5 × 100 70.27</td><td>89.67</td><td>70.88</td><td>66.17 61.32 64.30</td><td>51.49</td><td>88.96</td><td>42.80</td><td>64.82</td><td>89.86</td><td>82.75</td></tr><tr><td>Class-wise</td><td colspan=\"2\">1 × 100 70.23</td><td>89.70</td><td>70.73</td><td>66.06 61.45 64.82</td><td>51.02</td><td>88.97</td><td>43.42</td><td>64.26</td><td>89.71</td><td>82.37</td></tr><tr><td>Learnable</td><td>100</td><td>70.37</td><td>89.91</td><td>70.41</td><td>67.00 61.36 65.15</td><td>51.58</td><td>88.81</td><td>42.97</td><td>65.08</td><td>89.57</td><td>82.28</td></tr></table>", "text": "Transfer learning via linear evaluation results on various downstream datasets, where the model is SUPSIAM-pretrained with different target pool design on ImageNet-100. Avg represents the average performance across each dataset. For each dataset, the best results are in bold and the second-best results are underlined.", "num": null, "html": null}, "TABREF8": {"type_str": "table", "content": "<table><tr><td>α</td><td>Avg</td><td colspan=\"9\">CIFAR10 CIFAR100 DTD Food MIT67 SUN397 Caltech CUB200 Dogs Flowers Pets</td></tr><tr><td colspan=\"2\">0.0 69.33</td><td>89.18</td><td>69.41</td><td>65.53 60.72 65.05</td><td>50.81</td><td>88.83</td><td>41.46</td><td>61.51</td><td>90.04</td><td>80.09</td></tr><tr><td colspan=\"2\">0.2 70.14</td><td>89.89</td><td>70.56</td><td>65.89 61.03 65.25</td><td>51.34</td><td>88.85</td><td>42.07</td><td>64.28</td><td>90.12</td><td>82.27</td></tr><tr><td colspan=\"2\">0.5 70.40</td><td>89.95</td><td>70.88</td><td>66.51 61.46 64.45</td><td>51.50</td><td>88.86</td><td>43.48</td><td>64.65</td><td>90.27</td><td>82.38</td></tr><tr><td colspan=\"2\">0.8 70.28</td><td>89.39</td><td>70.04</td><td>67.08 64.06 66.00</td><td>51.98</td><td>87.45</td><td>42.16</td><td>62.94</td><td>90.26</td><td>81.76</td></tr><tr><td colspan=\"2\">1.0 67.07</td><td>87.28</td><td>66.41</td><td>66.06 63.44 64.68</td><td>50.69</td><td>85.00</td><td>36.10</td><td>54.57</td><td>88.38</td><td>75.13</td></tr><tr><td colspan=\"5\">F.2. Ablation Study: Number of Positives from Target Pool</td><td/><td/><td/><td/><td/></tr></table>", "text": "Table F.1. Transfer learning via linear evaluation results on various downstream datasets, where the model is SUPSIAM pretrained with different α on ImageNet-100. Avg represents the average performance across each dataset. For each dataset, the best results are in bold and the second-best results are underlined.", "num": null, "html": null}, "TABREF9": {"type_str": "table", "content": "<table><tr><td>M</td><td>Avg</td><td colspan=\"9\">CIFAR10 CIFAR100 DTD Food MIT67 SUN397 Caltech CUB200 Dogs Flowers Pets</td></tr><tr><td colspan=\"2\">1 70.13</td><td>89.58</td><td>70.59</td><td>65.75 61.39 64.85</td><td>51.41</td><td>88.57</td><td>42.80</td><td>64.52</td><td>89.92</td><td>82.07</td></tr><tr><td colspan=\"2\">4 70.40</td><td>89.94</td><td>70.63</td><td>66.59 61.66 65.07</td><td>51.32</td><td>88.82</td><td>42.88</td><td>64.78</td><td>89.85</td><td>82.88</td></tr><tr><td colspan=\"2\">16 70.31</td><td>89.94</td><td>70.86</td><td>65.64 61.40 64.95</td><td>51.54</td><td>88.65</td><td>43.17</td><td>65.13</td><td>89.66</td><td>82.42</td></tr><tr><td colspan=\"2\">all 70.40</td><td>89.95</td><td>70.88</td><td>66.51 61.46 64.45</td><td>51.50</td><td>88.86</td><td>43.48</td><td>64.65</td><td>90.27</td><td>82.38</td></tr><tr><td colspan=\"5\">F.3. Transfer Learning with Different Batch Size</td><td/><td/><td/><td/><td/></tr></table>", "text": "2. Transfer learning via linear evaluation results on various downstream datasets, where the model is SUPSIAM-pretrained with different M on ImageNet-100. all stands for sampling all positives in the target pool. Avg represents the average performance across each dataset. For each dataset, the best results are in bold and the second-best results are underlined.", "num": null, "html": null}, "TABREF10": {"type_str": "table", "content": "<table><tr><td>Bsz</td><td>Model</td><td>Avg</td><td colspan=\"9\">CIFAR10 CIFAR100 DTD Food MIT67 SUN397 Caltech CUB200 Dogs Flowers Pets</td></tr><tr><td/><td colspan=\"2\">SUPCON 68.19</td><td>88.82</td><td>68.89</td><td>65.18 59.34 63.76</td><td>50.09</td><td>87.30</td><td>35.84</td><td>61.68</td><td>89.05</td><td>80.12</td></tr><tr><td>128</td><td colspan=\"2\">SUPCON  *  68.97</td><td>89.70</td><td>70.48</td><td>65.65 59.06 63.43</td><td>49.86</td><td>87.97</td><td>38.76</td><td>63.74</td><td>89.22</td><td>80.83</td></tr><tr><td/><td colspan=\"2\">SUPSIAM 70.40</td><td>89.95</td><td>70.88</td><td>66.51 61.46 64.45</td><td>51.50</td><td>88.86</td><td>43.48</td><td>64.65</td><td>90.27</td><td>82.38</td></tr><tr><td/><td colspan=\"2\">SUPCON 68.42</td><td>89.10</td><td>69.40</td><td>65.32 59.21 63.25</td><td>50.63</td><td>88.22</td><td>36.05</td><td>62.60</td><td>89.10</td><td>79.80</td></tr><tr><td>256</td><td colspan=\"2\">SUPCON  *  68.86</td><td>89.46</td><td>70.06</td><td>65.88 58.73 63.92</td><td>50.04</td><td>87.84</td><td>38.28</td><td>63.02</td><td>89.06</td><td>81.22</td></tr><tr><td/><td colspan=\"2\">SUPSIAM 70.44</td><td>90.07</td><td>70.77</td><td>66.28 61.89 65.18</td><td>51.77</td><td>88.83</td><td>43.09</td><td>64.90</td><td>89.99</td><td>82.11</td></tr></table>", "text": "3. Transfer learning via linear evaluation results on various downstream datasets, where the model is pretrained with different batch size on ImageNet-100. Bsz refers to the batch size during pretraining. Avg represents the average performance across each dataset. The model marked with * indicates the inclusion of a memory bank.", "num": null, "html": null}, "TABREF12": {"type_str": "table", "content": "<table><tr><td>Pretext</td><td colspan=\"7\">Downstream SIMCLR SUPCON SIMSIAM SUPSIAM BYOL SUPBYOL</td></tr><tr><td>CIFAR10</td><td>CIFAR10 CIFAR100</td><td>89.58 56.36</td><td>95.15 53.82</td><td>93.36 60.51</td><td>94.73 61.76</td><td>91.56 50.20</td><td>94.88 55.47</td></tr><tr><td>CIFAR100</td><td>CIFAR10 CIFAR100</td><td>80.36 64.77</td><td>79.99 74.03</td><td>78.81 70.63</td><td>85.19 75.05</td><td>78.35 65.42</td><td>80.09 74.36</td></tr></table>", "text": "TableH.1. Comparision of CL and ANCL with their self-supervised / supervised versions with ResNet-18 on CIFAR10 and 100. We run all experiments for 1000 epochs. If the pretext and downstream datasets are aligned, the supervised version shows improved performance. In contrast, when there is a mismatch, performance gains are observed only in the ANCL scenario.", "num": null, "html": null}}}}