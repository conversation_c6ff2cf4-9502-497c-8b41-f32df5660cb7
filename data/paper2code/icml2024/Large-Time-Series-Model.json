{"paper_id": "Large-Time-Series-Model", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:34:53.734684Z"}, "title": "Timer: Generative Pre-trained Transformers Are Large Time Series Models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": "<<EMAIL>>."}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University. Yong <PERSON>", "location": {"settlement": "BNRist"}}, "email": "<<EMAIL>>"}, {"first": "Xiangdong", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University. Yong <PERSON>", "location": {"settlement": "BNRist"}}, "email": ""}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University. Yong <PERSON>", "location": {"settlement": "BNRist"}}, "email": "<<EMAIL>>."}], "year": "", "venue": null, "identifiers": {}, "abstract": "Deep learning has contributed remarkably to the advancement of time series analysis. Still, deep models can encounter performance bottlenecks in real-world data-scarce scenarios, which can be concealed due to the performance saturation with small models on current benchmarks. Meanwhile, large models have demonstrated great powers in these scenarios through large-scale pre-training. Continuous progress has been achieved with the emergence of large language models, exhibiting unprecedented abilities such as few-shot generalization, scalability, and task generality, which are however absent in small deep models. To change the status quo of training scenario-specific small models from scratch, this paper aims at the early development of large time series models (LTSM). During pre-training, we curate large-scale datasets with up to 1 billion time points, unify heterogeneous time series into single-series sequence (S3) format, and develop the GPT-style architecture toward LTSMs. To meet diverse application needs, we convert forecasting, imputation, and anomaly detection of time series into a unified generative task. The outcome of this study is a Time Series Transformer (Timer), which is generative pretrained by next token prediction and adapted to various downstream tasks with promising capabilities as an LTSM. Code and datasets are available at: https://github.com/thuml/Large-Time-Series-Model.", "pdf_parse": {"paper_id": "Large-Time-Series-Model", "_pdf_hash": "", "abstract": [{"text": "Deep learning has contributed remarkably to the advancement of time series analysis. Still, deep models can encounter performance bottlenecks in real-world data-scarce scenarios, which can be concealed due to the performance saturation with small models on current benchmarks. Meanwhile, large models have demonstrated great powers in these scenarios through large-scale pre-training. Continuous progress has been achieved with the emergence of large language models, exhibiting unprecedented abilities such as few-shot generalization, scalability, and task generality, which are however absent in small deep models. To change the status quo of training scenario-specific small models from scratch, this paper aims at the early development of large time series models (LTSM). During pre-training, we curate large-scale datasets with up to 1 billion time points, unify heterogeneous time series into single-series sequence (S3) format, and develop the GPT-style architecture toward LTSMs. To meet diverse application needs, we convert forecasting, imputation, and anomaly detection of time series into a unified generative task. The outcome of this study is a Time Series Transformer (Timer), which is generative pretrained by next token prediction and adapted to various downstream tasks with promising capabilities as an LTSM. Code and datasets are available at: https://github.com/thuml/Large-Time-Series-Model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Time series analysis encompasses a broad range of critical tasks, including time series forecasting (<PERSON> et al., 2015) , imputation (<PERSON>, 1962) , anomaly detection (<PERSON><PERSON><PERSON><PERSON> et al., 2000) , etc. Despite the ubiquity of real-world time series, training samples can be scarce in specific applications. While remarkable advances have been made in deep time series models (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023b) , the accuracy of state-of-the-art deep models (<PERSON><PERSON> et al., 2022) can still deteriorate drastically in such scenarios, even within prevalent benchmarks as shown in Figure 1 . Concurrently, we are witnessing rapid progress of large language models (<PERSON><PERSON> et al., 2018) , involving training on large-scale text corpora and exhibiting remarkable few-shot and zero-shot capabilities (<PERSON><PERSON> et al., 2019) . It can be indicative for the community to develop large time series models (LTSM) that are transferable on various data-scarce scenarios by pre-training on numerous time series data.", "cite_spans": [{"start": 100, "end": 118, "text": "(<PERSON> et al., 2015)", "ref_id": "BIBREF8"}, {"start": 132, "end": 148, "text": "(<PERSON>, 1962)", "ref_id": "BIBREF23"}, {"start": 169, "end": 191, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2000)", "ref_id": "BIBREF9"}, {"start": 372, "end": 389, "text": "(<PERSON> et al., 2022;", "ref_id": null}, {"start": 390, "end": 408, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 409, "end": 427, "text": "<PERSON> et al., 2023b)", "ref_id": null}, {"start": 475, "end": 493, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF40"}, {"start": 675, "end": 697, "text": "(<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF43"}, {"start": 809, "end": 831, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF44"}], "ref_spans": [{"start": 599, "end": 600, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Proceedings", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Further, large models evolved by generative pre-training (GPT) have demonstrated several advanced capabilities that are absent in small models: the generalization ability that one model fits many domains, the versatility that one model copes with various scenarios and tasks, and the scalability that performance improves with the scale of parameters and pre-training corpora. Fascinating capabilities have fostered the advancement of artificial general intelligence (OpenAI, 2023) . Time series holds comparable practical value to natural language. Essentially, they exhibit inherent similarities in generative modeling (<PERSON><PERSON> et al., 2000) and autoregression (Box, 2013) . Consequently, the unprecedented success of the generative pre-trained large language models (<PERSON> et al., 2023) serves as a blueprint for the progress of LTSMs.", "cite_spans": [{"start": 467, "end": 481, "text": "(OpenAI, 2023)", "ref_id": "BIBREF41"}, {"start": 621, "end": 642, "text": "(<PERSON><PERSON> et al., 2000)", "ref_id": "BIBREF4"}, {"start": 662, "end": 673, "text": "(Box, 2013)", "ref_id": "BIBREF7"}, {"start": 768, "end": 787, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF46"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Although unsupervised pre-training on time series data has been widely explored, yielding breakthroughs based on the masked modeling (<PERSON><PERSON><PERSON> et al., 2021) and contrastive learning (<PERSON><PERSON> et al., 2022) , there are still unsolved fundamental issues for developing LTSMs. Firstly, the dataset infrastructure and unified treatment for heterogeneous time series are lagging behind other fields. As a result, prior unsupervised pre-training methods are typically constrained to a small scale and primarily focus on in-dataset transfer (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2022) . Secondly, the architecture of scalable large models remains underexplored in the field of time series. It is observed that non-autoregressive structures, which are prevalent and effective in small time series models, may not be suitable for LTSMs. Thirdly, existing large-scale pre-trained models (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2023b) primarily concentrated on a single task (e.g., forecasting), and have scarcely addressed task unification. Consequently, the applicability of LTSMs remains elevatable.", "cite_spans": [{"start": 133, "end": 155, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": null}, {"start": 181, "end": 199, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": null}, {"start": 528, "end": 548, "text": "(<PERSON> et al., 2022;", "ref_id": null}, {"start": 549, "end": 566, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF40"}, {"start": 866, "end": 884, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 885, "end": 903, "text": "<PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In this paper, we dive into the pre-training and adaptation of large time series models. By aggregating publicly available time series datasets and following curated data processing, we construct Unified Time Series Dataset (UTSD) of hierarchical capacities to facilitate the research on the scalability of LTSMs. To pre-train large models on heterogeneous time series data, we propose the single-series sequence (S3) format that converts multivariate series with reserved patterns into unified token sequences. For better generalization and versatility, we adopt the GPT-style objective that predicts the next token (<PERSON><PERSON> et al., 2000) . Eventually, we present Timer, a large-scale pre-trained Time Series Transformer. Unlike prevalent encoder-only architecture (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2023a) , Timer exhibits similar characteristics as large language models such as flexible context length and autoregressive generation. It also presents notable few-shot generalization, scalability, and task generality, outperforming state-of-the-art task-specific models on forecasting, imputation, and anomaly detection. Overall, our contributions can be summarized as follows:", "cite_spans": [{"start": 617, "end": 638, "text": "(<PERSON><PERSON> et al., 2000)", "ref_id": "BIBREF4"}, {"start": 765, "end": 783, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF40"}, {"start": 784, "end": 800, "text": "<PERSON> et al., 2022;", "ref_id": null}, {"start": 801, "end": 819, "text": "<PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We delve into the LTSM development by curating largescale datasets comprised of 1B time points, proposing a unified sequence format to cope with data heterogeneity, and presenting Timer, a generative pre-trained Transformer for general time series analysis.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We apply Timer on various tasks, which is realized in our unified generative approach. Timer exhibits notable feasibility and generalization in each task, achieving state-of-the-art performance with few samples.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• By pre-training on increasing available time series data, Timer exhibits zero-shot forecasting capability. Quantitative evaluations and quality assessments are provided among concurrent large time series models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Unsupervised pre-training on large-scale data is the essential step for modality understanding for downstream applications, which has achieved substantial success in sequences, covering natural language (<PERSON><PERSON> et al., 2021) , patchlevel image (<PERSON><PERSON> et al., 2021) and video (<PERSON> et al., 2021) . Supported by powerful backbones (<PERSON><PERSON><PERSON><PERSON> et al., 2017) for sequential modeling, the paradigms of unsupervised pre-training on sequences have been extensively studied in recent years, which can be categorized into the masked modeling (<PERSON> et al., 2018) , contrastive learning (<PERSON> et al., 2020) , and generative pre-training (<PERSON><PERSON> et al., 2018) .", "cite_spans": [{"start": 203, "end": 225, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF45"}, {"start": 245, "end": 263, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF3"}, {"start": 274, "end": 292, "text": "(<PERSON> et al., 2021)", "ref_id": null}, {"start": 327, "end": 349, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": null}, {"start": 528, "end": 549, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF18"}, {"start": 573, "end": 592, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF13"}, {"start": 623, "end": 645, "text": "(<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF43"}], "ref_spans": [], "eq_spans": [], "section": "Unsupervised Pre-training on Sequences", "sec_num": "2.1."}, {"text": "Inspired by significant progress achieved in relevant fields, masked modeling and contrastive learning have been welldeveloped for time series. TST (<PERSON><PERSON><PERSON> et al., 2021) and PatchTST (<PERSON><PERSON> et al., 2022) adopt the BERT-style masked pre-training to reconstruct several time points and patches respectively. LaST (<PERSON> et al., 2022b) proposes to learn the representations of decomposed time series based on variational inference. Contrastive learning is also well incorporated in prior works (<PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022) . TF-C (<PERSON> et al., 2022) constrains the time-frequency consistency by temporal variations and frequency spectrums. SimMTM (<PERSON> et al., 2023) combines masked modeling and contrastive approach within the neighbors of time series.", "cite_spans": [{"start": 144, "end": 170, "text": "TST (<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": null}, {"start": 184, "end": 202, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF40"}, {"start": 305, "end": 330, "text": "LaST (<PERSON> et al., 2022b)", "ref_id": null}, {"start": 489, "end": 507, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": null}, {"start": 508, "end": 525, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": null}, {"start": 528, "end": 553, "text": "TF-C (<PERSON> et al., 2022)", "ref_id": null}, {"start": 651, "end": 670, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "Unsupervised Pre-training on Sequences", "sec_num": "2.1."}, {"text": "However, generative pre-training has received relatively less attention in the field of time series despite its prevalence witnessed in developing large language models (<PERSON><PERSON><PERSON><PERSON> et al., 2023; OpenAI, 2023) . Most large language models are generative pre-trained (<PERSON> et al., 2023) with token-level supervision, where each token is generated based on the previous context and independently supervised (<PERSON><PERSON> et al., 2000) . Consequently, they are not constrained by specific input and output lengths and excel at multi-step generation. Furthermore, prior studies (<PERSON> et al., 2022a; <PERSON> et al., 2022) have demonstrated that scalability and generalization largely stem from generative pre-training, which requires more training data than other pre-training paradigms. Thus, our work aims to investigate and revitalize generative pretraining towards LTSMs, facilitated by extensive time series and deftly designed adaptation on downstream tasks.", "cite_spans": [{"start": 169, "end": 191, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 192, "end": 205, "text": "OpenAI, 2023)", "ref_id": "BIBREF41"}, {"start": 262, "end": 281, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF46"}, {"start": 401, "end": 422, "text": "(<PERSON><PERSON> et al., 2000)", "ref_id": "BIBREF4"}, {"start": 564, "end": 584, "text": "(<PERSON> et al., 2022a;", "ref_id": null}, {"start": 585, "end": 602, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "Unsupervised Pre-training on Sequences", "sec_num": "2.1."}, {"text": "Pre-trained models with scalability can evolve to large foundation models (<PERSON><PERSON><PERSON><PERSON> et al., 2021) , featured by increasing model capacity and pre-training scale to solve various data and tasks. Large language models even demonstrate advanced capabilities such as in-context learning and emergent abilities (<PERSON> et al., 2022) . As of present, research on large time series models remains at a nascent stage. Ex- isting efforts towards LTSMs can be categorized into two groups, with one being large language models for time series. FPT (<PERSON> et al., 2023) regards GPT-2 as a representation extractor of sequences, which is respectively fine-tuned on different downstream tasks. LLMTime (<PERSON> et al., 2023) encodes time series into numerical tokens for LLMs, exhibiting model scalability in the forecasting task. Time-LLM (<PERSON> et al., 2023) investigates prompting techniques to enhance prediction, demonstrating the generalization ability of LLMs. Unlike these methods, Timer is pre-trained natively on time series and free from extra modality alignment.", "cite_spans": [{"start": 74, "end": 98, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF6"}, {"start": 307, "end": 325, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF14"}, {"start": 535, "end": 554, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF50"}, {"start": 685, "end": 705, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF11"}, {"start": 821, "end": 839, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF30"}], "ref_spans": [], "eq_spans": [], "section": "Large Time Series Models", "sec_num": "2.2."}, {"text": "Another category includes pre-trained models on large-scale time series. ForecastFPN (<PERSON><PERSON> et al., 2023) Canseco, 2023) releases the first commercial API for zeroshot forecasting. Different from prior works, our UTSD contains 1B real-world time points, which is not a simple aggregation but follows curated data processing. Timer is applicable to downstream tasks beyond forecasting and exhibits promising scalability. We are also the first to establish a zero-shot forecasting benchmark on concurrent LTSMs.", "cite_spans": [{"start": 85, "end": 106, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF20"}, {"start": 107, "end": 121, "text": "Canseco, 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Large Time Series Models", "sec_num": "2.2."}, {"text": "Inspired by the sequential structure inherent in language and time series, we leverage the advancement of large language models for developing LTSMs. In this paper, we advocate the development of large models for time series with (1) the utilization of extensive time series corpora, (2) the adoption of a standardized format for diverse time series data, and (3) the generative pre-training on the decoder-only Transformer that autoregressively predict the next time series token.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Approach", "sec_num": "3."}, {"text": "Large-scale datasets are of paramount importance for pretraining large models. However, the curation of time series datasets can be prohibitively challenging. In spite of their ubiquity, there are numerous data of low quality, including missing values, unpredictability, variance in shape, and irregular frequencies, which significantly impact the efficacy of pre-training. Therefore, we establish the criteria for filtering high-quality data and stacking up the hierarchy of time series corpora. Concretely, we record the statistics of each dataset, including (1) basic properties, such as time steps, variate number, file size, frequency, etc; and (2) time series characteristics: periodicity, stationarity, and predictability. This also allows us to assess the complexity of different datasets and progressively conduct scalable pre-training.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Data", "sec_num": "3.1."}, {"text": "We curate Unified Time Series Dataset (UTSD) as shown in Figure 2 . UTSD is constructed with hierarchical capacities to facilitate the scalability research of large models. UTSD encompasses seven domains with up to 1 billion time points (UTSD-12G), covering typical scenarios of time series analysis. Following the principle of keeping pattern diversity, we include as diverse datasets as possible in each hierarchy, ensure the data size of each domain is nearly balanced when scaling up, and the complexity gradually increases in accordance with the calculated statistics. We release four volumes on https://huggingface.co/datasets/thuml/UTSD.", "cite_spans": [], "ref_spans": [{"start": 64, "end": 65, "text": "2", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Data", "sec_num": "3.1."}, {"text": "Notably, we make our curation applicable to the increasing open-source datasets, which is beneficial for the continuous expansion of time series corpora. Particularly, we conduct the same procedure on the recent LOTSA (<PERSON><PERSON> et al., 2024) , a great endeavor with 27B time points, to explore zero-shot forecasting and establish the benchmark of LTSMs. Detailed construction and statistics are provided in Appendix A.", "cite_spans": [{"start": 218, "end": 236, "text": "(<PERSON><PERSON> et al., 2024)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Data", "sec_num": "3.1."}, {"text": "Different from natural language, which has been facilitated by the well-established discrete tokenization and sequential structure with the regular shape, constructing unified time series sequences is not straightforward due to the heterogeneity of series such as amplitude, frequency, stationarity, and disparities of the datasets in the variate number, series length and purpose. To promote pre-training on extensive time series, we propose to convert heterogeneous time series into single-series sequence (S3), which reserves the patterns of series variations with the unified context length. As depicted in Figure 3 , our initial step involves normalizing and merging at the level of variates. Each series representing a variate will be divided into training and validation splits at a ratio of 9:1 for pre-training. We apply the statistics of the training split to normalize the entire series. The normalized time series are merged into a pool of single-variate series. The time points of single-variate series for training follow the normal distribution, which mitigates the discrepancies in the amplitude and variate numbers across multiple datasets.", "cite_spans": [], "ref_spans": [{"start": 618, "end": 619, "text": "3", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Training Strategy", "sec_num": "3.2."}, {"text": "We uniformly sample sequences from the pool by a window, obtaining single-series sequences with a fixed context length, as the format of S3. The proposed format is essentially an extension of Channel Independence CI (<PERSON><PERSON> et al., 2022) . However, CI necessitates time-aligned multivariate series and flattens the variate dimension to the same batch, thereby requiring the batch of samples to originate from the same dataset. Based on our format, the model observes sequences from different periods and different datasets, thus increasing the pre-training difficulty and directing more attention to the temporal variation. S3 does not require time alignment, which applies to broad univariate and irregular time series. We then employ generative pre-training, where single-series sequences are regarded as standard sentences of time series.", "cite_spans": [{"start": 216, "end": 234, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "Training Strategy", "sec_num": "3.2."}, {"text": "Given the limited exploration of the backbone for large time series models, we extensively evaluate candidate backbones on the same pre-training scale in Section 4.5, which validates Transformer as the scalable choice. Further, we review Transformer-based models in time series forecasting, which have experienced notable development in recent years. They can be categorized into encoder-only and decoder-only ar-chitectures following a similar pipeline. As illustrated in Figure 4 , prevalent small time series forecasters, the encoderonly non-autoregressive models, generate predictions with the globally flattened representation of lookback series. Although direct projection may benefit from end-to-end supervision, flattening can also wipe out sequential dependencies modeled by attention, thereby weakening Transformer layers to reveal the patterns of temporal variations. Inspired by the substantial progress of decode-only LLMs with the ability for iterative generation, we opt for an underexplored autoregressive approach for generative pre-training. As language models autoregressively predict the next token:", "cite_spans": [], "ref_spans": [{"start": 480, "end": 481, "text": "4", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Model Design", "sec_num": "3.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "P (U) = N i=1 p(u i |u <i )", "eq_num": "(1)"}], "section": "Model Design", "sec_num": "3.3."}, {"text": "on the token sequence U = {u 1 , . . . , u N }, we first establish the tokenization of the given single-series sequence (S3) X = {x 1 , . . . , x N S } with the unified context length N S.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model Design", "sec_num": "3.3."}, {"text": "We define the time series token as consecutive time points (segment) of length S that encompass the series variations:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model Design", "sec_num": "3.3."}, {"text": "s i = {x (i-1)S+1 , . . . , x iS } ∈ R S , i = 1, . . . , N. (2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model Design", "sec_num": "3.3."}, {"text": "We adopt the decoder-only Transformer with dimension D and L layers and apply generative pre-training (GPT) on N tokens in the single-series sequence (sentence):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model Design", "sec_num": "3.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "h 0 i = W e s i + TE i , i = 1, . . . , N, H l = TrmBlock(H l-1 ), l = 1, . . . , L, {ŝ i+1 } = H L W d , i = 1, . . . , N,", "eq_num": "(3)"}], "section": "Model Design", "sec_num": "3.3."}, {"text": "where W e , W d ∈ R D×S encode and decode token embeddings in H = {h i } ∈ R N ×D indepedently, and TE i denotes the optional timestamp embedding. Via the causal attention of the decoder-only Transformer, the autoregressively generated ŝi+1 is obtained as the next token of s i . Thus, we formulate the pre-training objective as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model Design", "sec_num": "3.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L MSE = 1 N S ||s i -ŝi || 2 2 , i = 2, ..., N.", "eq_num": "(4)"}], "section": "Model Design", "sec_num": "3.3."}, {"text": "Equation 4 yields token-wise supervising signals, where generated tokens of each position are independently supervised.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model Design", "sec_num": "3.3."}, {"text": "Consequently, generative pre-trained models are endowed with the flexibility to address unfixed context length during inference and excel at multi-step generation by iteratively sliding and enlarging input tokens. While small time series models generally refrain from iterative multi-step prediction to mitigate error accumulations, our experiments reveal that autoregressive models pre-trained on large-scale data can also perform as competitively as direct multi-step predictors.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model Design", "sec_num": "3.3."}, {"text": "We demonstrate Timer as a large time series model in time series forecasting, imputation, and anomaly detection by tackling them in a unified generative scheme, which is described in Figure 5 . We compare Timer with state-of-the-art task-specific models and present the benefit of pre-training on data-scarce scenarios, known as the few-shot ability of large models. Furthermore, we delve into the model scalability, including model/data size, and try to build a comprehensive zero-shot evaluation across concurrent large time series models. All the downstream datasets are not included in the pre-training stage to prevent data leakage. We provide the detailed implementation and model configurations of pre-training and adaptation in Appendix B.1 and B.2. ", "cite_spans": [], "ref_spans": [{"start": 190, "end": 191, "text": "5", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Experiments", "sec_num": "4."}, {"text": "Setups Time series forecasting is essential and presents challenges in real-world applications. To thoroughly evaluate the performance, we elaborately establish the benchmark, including ETT, ECL, Traffic, Weather, and PEMS adopted in <PERSON> et al. (2023b) . We adopt the unified lookback length as 672 and the forecast length as 96. 10 .", "cite_spans": [{"start": 234, "end": 252, "text": "<PERSON> et al. (2023b)", "ref_id": null}], "ref_spans": [{"start": 330, "end": 332, "text": "10", "ref_id": "TABREF21"}], "eq_spans": [], "section": "Time Series Forecasting", "sec_num": "4.1."}, {"text": "As depicted in Figure 6 , we present the results of the pre-trained Timer (solid line) and Timer trained from scratch (dashed line) under different data scarcities. We also evaluate state-of-the-art forecasters by training them on full samples as a competitive baseline. Concretely, we train PatchTST (<PERSON><PERSON> et al., 2022) and iTransformer (<PERSON> et al., 2023b) on each dataset and report the better one as SOTA. Timer fine-tuned on few training samples demonstrates competitive results as advanced small deep forecasters, specifically achieving better results with only 1% available samples from ETTh1, 5% from Traffic, 3% from PEMS03, and 25% from PEMS04 and exhibiting remarkable few-shot ability. data-scarce scenarios, the performance degradation can be alleviated by the few-shot generalization of LTSMs.", "cite_spans": [{"start": 301, "end": 319, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF40"}, {"start": 337, "end": 356, "text": "(<PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [{"start": 22, "end": 23, "text": "6", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Results", "sec_num": null}, {"text": "Setups Imputation is ubiquitous in real-world applications, aiming to fill corrupted time series based on partially observed data. However, while various machine learning algorithms and simple linear interpolation can effectively cope with the corruptions randomly happening at the point level, real-world corruptions typically result from prolonged monitor shutdowns and require a continuous period of recovery. Consequently, imputation can be ever challenging when attempting to recover a span of time points encompassing intricate series variations. In this task, we conduct the segment-level imputation. Each time series is divided into 8 segments and each segment has the length of 24 and the possibility of being completely masked. We obtain Timer on UTSD-4G by generative pre-training with the segment length S = 24 and the token number N = 15. For downstream adaptation, we conduct the denoising autoencoding in T5 (<PERSON><PERSON> et al., 2020) as detailed in Appendix B.2 to recover the masked spans in a generative way. et al., 2022) , each dataset is imputed with four mask ratios in {12.5%, 25%, 37.5%, 50%} and we calculate the average reduced imputation error in MSE relative to training from scratch. Additional results of other data scarcities are provided in Figure 18 .", "cite_spans": [{"start": 923, "end": 944, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": null}, {"start": 1022, "end": 1035, "text": "et al., 2022)", "ref_id": null}], "ref_spans": [{"start": 1275, "end": 1277, "text": "18", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Imputation", "sec_num": "4.2."}, {"text": "We establish a comprehensive segment-level imputation benchmark, which includes 11 datasets with four mask ratios each. Timer is compared with the previous stateof-the-art imputation model (<PERSON> et al., 2022) . As shown in the left of Figure 7 , Timer outperforms in respectively 100.0%, 86.4%, and 56.8% of 44 imputation scenarios under the data scarcities of {5%, 20%, 100%}, validating the effectiveness of Timer on the challenging imputation task. Regarding the benefit of pre-training, we present the promotion as the reduction ratio of imputation errors in Figure 8 , where pre-training consistently yields positive effects with 5% downstream samples. Additional experiments on 20% and 100% available samples are provided in Figure 18 , which still present notable performance improvement.", "cite_spans": [{"start": 189, "end": 206, "text": "(<PERSON> et al., 2022)", "ref_id": null}], "ref_spans": [{"start": 240, "end": 241, "text": "7", "ref_id": null}, {"start": 568, "end": 569, "text": "8", "ref_id": "FIGREF5"}, {"start": 736, "end": 738, "text": "18", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Results", "sec_num": null}, {"text": "Setups Anomaly detection is vital in industry and operations. Previous methods (<PERSON> et al., 2021; <PERSON> et al., 2022) typically tackle the unsupervised scenario in a reconstructive approach, where a model is trained to reconstruct the input series, and the output is regarded as the normal series. Based on our generative model, we cope with anomaly detection in a predictive approach, which utilizes the observed segments to predict the future segment, and the predicted segment will be established as the standard to be compared with the actual value received. Unlike the previous method requiring to collect time series of a period for reconstruction, our predictive approach allows for segment-level anomaly detection on the fly. Thus, the task is converted into a next token prediction task as detailed in Appendix B.2.", "cite_spans": [{"start": 79, "end": 96, "text": "(<PERSON> et al., 2021;", "ref_id": null}, {"start": 97, "end": 113, "text": "<PERSON> et al., 2022)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Anomaly Detection", "sec_num": "4.3."}, {"text": "We introduce UCR Anomaly Archive (<PERSON> & <PERSON>, 2021) that contains 250 tasks. In each task, a single normal time series is provided for training, and the model should locate the position of an anomaly in the test series. We first train a predictive model on the training set and calculate the MSE between the predicted series and ground truth on the test set. By regarding the MSE of all segments as the confidence level, the segments with higher than α quantile of confidence are labeled as potential positions of anomalies.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Anomaly Detection", "sec_num": "4.3."}, {"text": "We evaluate well-acknowledged anomaly detection models, including TimesNet (<PERSON> et al., 2022) and Anomaly Transformer (<PERSON> et al., 2021) . As shown in the right of Figure 7 , we present the number of detected anomalies with given quantiles, where Timer outperforms other advanced anomaly detection models, exhibiting the versatility of our generative time series model. Moreover, Figure 9 compares the detection performance of pre-trained models and from scratch using two indicators. The left figure shows the number of datasets that the model has completed detection within the quantile of 3% and 10%, and the right figure shows the quantile distribution and the averaged quantile of all 250 UCR datasets, where the pre-trained Timer with the smaller averaged quantile works as a more accurate detector. ", "cite_spans": [{"start": 75, "end": 96, "text": "(<PERSON> et al., 2022) and", "ref_id": null}, {"start": 97, "end": 134, "text": "Anomaly Transformer (<PERSON> et al., 2021)", "ref_id": null}], "ref_spans": [{"start": 169, "end": 170, "text": "7", "ref_id": null}, {"start": 385, "end": 386, "text": "9", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "Results", "sec_num": null}, {"text": "Scalability is the essential property that emerges from pretrained models to large models. To investigate the scaling behavior of Timer, we pre-train Timer with increased model size and data size as detailed in Appendix B.1 and evaluate it in downstream forecasting on all subsets of PEMS.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Scalability", "sec_num": "4.4."}, {"text": "Model size We keep UTSD-4G as the pre-training set.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Scalability", "sec_num": "4.4."}, {"text": "Results are presented in Figure 10 . While keeping model dimension D = 256, we increase the number of layers. The growth of parameters from 1M to 4M leads to the decrease in forecasting errors in two few-shot scenarios by an average of 14.7% and 20.6% respectively. Subsequently, we increase the model dimension under the fixed layer number L = 6, enlarging parameters from 3M to 50M, resulting in further improved performance of 25.1% and 18.2%, validating the efficacy of scaling up the model size.", "cite_spans": [], "ref_spans": [{"start": 32, "end": 34, "text": "10", "ref_id": null}], "eq_spans": [], "section": "Scalability", "sec_num": "4.4."}, {"text": "Data scale We pre-train Timer under the same model size with different UTSD sizes, which exhibits steady improvement with the enlarged pre-training scale in Figure 11 . The benefit is relatively small compared to expanding the model size previously, which can be attributed to the performance saturation on these datasets. Compared with large language models, the parameter scale of Timer can still be small, indicating the higher parameter efficiency in the time series", "cite_spans": [], "ref_spans": [{"start": 164, "end": 166, "text": "11", "ref_id": "FIGREF7"}], "eq_spans": [], "section": "Scalability", "sec_num": "4.4."}, {"text": "/D\\HU1XPEHU 06( 0 0 0 0 )LQHWXQHRQ6DPSOHV /D\\HU1XPEHU 06( 0 0 0 0 )LQHWXQHRQ6DPSOHV 0RGHO'LPHQVLRQ 06( 0 0 0 0 )LQHWXQHRQ6DPSOHV 0RGHO'LPHQVLRQ 06( 0 0 0 0 )LQHWXQHRQ6DPSOHV Figure 10", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Scalability", "sec_num": "4.4."}, {"text": ". Larger Timer demonstrates better performance on downstream forecasting. Models are all pre-trained on UTSD-4G. Detailed results of all PEMS subsets are provided in Table 16 . 16 . modality, which is also supported by prior works (<PERSON> et al., 2023b) . As the scaling law (<PERSON> et al., 2020) of large models highlights the significance of synchronized scaling of data with the model parameters, there is still an urgent need to accelerate the data infrastructure in the time series field to promote the development of LTSMs.", "cite_spans": [{"start": 231, "end": 250, "text": "(<PERSON> et al., 2023b)", "ref_id": null}, {"start": 272, "end": 293, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF31"}], "ref_spans": [{"start": 172, "end": 174, "text": "16", "ref_id": "TABREF25"}, {"start": 177, "end": 179, "text": "16", "ref_id": "TABREF25"}], "eq_spans": [], "section": "Scalability", "sec_num": "4.4."}, {"text": "* * * * 'DWDVHW6L]H 06( )LQHWXQHRQ6DPSOHV * * * * 'DWDVHW6L]H 06( )LQHWXQHRQ6DPSOHV", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Scalability", "sec_num": "4.4."}, {"text": "Overall, by increasing the model size and data scale, Timer reduces the prediction error as 0.231 → 0.138 (-40.3%) and 0.194 → 0.123 (-36.6%) under few-shot scenarios, surpassing state-of-the-art multivariate forecaster (<PERSON> et al., 2023b) training on full samples of PEMS datasets (0.139).", "cite_spans": [{"start": 220, "end": 239, "text": "(<PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Scalability", "sec_num": "4.4."}, {"text": "Backbone for LTSM Deep learning approaches have brought the boom of time series analysis, with various backbones for modeling the sequential time series modality being proposed. To validate the appropriate option for large To make sure the evaluation is comparable on different backbones, we maintain the same model configuration, including the model dimension and layer number, and pre-train these backbones on our UTSD-4G respectively. We set the token length S = 96 and the context length as 672 for Timer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model Analysis", "sec_num": "4.5."}, {"text": "For other non-autoregressive backbones, we pre-train them by direct multi-step forecasting in the 672-pred-96 setting. Flexible sequence length Typically, current deep forecasting models are trained on specific lookback and forecast lengths, limiting their versatility. Instead, the decoder-only architecture can provide the flexibility to address different sequence lengths. For instance, one Timer is applicable on different lookback lengths because of token-wise supervision outlined in Equation 4. In addition to the feasibility, it achieves enhanced performance by increasing the lookback length in Figure 13 . As for the forecast length, increasing works (<PERSON> et al., 2024) bring the revival of autoregression 18 .", "cite_spans": [{"start": 661, "end": 679, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF36"}], "ref_spans": [{"start": 611, "end": 613, "text": "13", "ref_id": "FIGREF1"}, {"start": 716, "end": 718, "text": "18", "ref_id": "TABREF6"}], "eq_spans": [], "section": "Model Analysis", "sec_num": "4.5."}, {"text": "(iterative multi-step prediction), enabling the generation of future predictions with arbitrary lengths. We explore this paradigm by rolling one model for all forecast lengths in Figure 15 , where the decoder-only Times exhibits smaller error accumulation, thereby achieving better performance. We conduct rolling forecasting on a single 672-pred-96 model.", "cite_spans": [], "ref_spans": [{"start": 186, "end": 188, "text": "15", "ref_id": "FIGREF10"}], "eq_spans": [], "section": "Model Analysis", "sec_num": "4.5."}, {"text": "There is a growing surge in the development of large models in the field of time series (Garza & Mergenthaler-Canseco, 2023; <PERSON> et al., 2023b; <PERSON><PERSON> et al., 2024; <PERSON><PERSON><PERSON> et al., 2024; <PERSON><PERSON><PERSON> et al., 2024) . One particularly fascinating direction of research is focused on zero-shot forecasting (ZSF), which has the potential to renovate the conventional practice of training small models or fine-tuning language models for each specific scenario. Zero-shot generalization represents a sophisticated capability of large models, necessitating substantial model capacity and pre-training on extensive datasets. Consequently, we are actively expanding our dataset by incorporating the latest data infrastructure (<PERSON><PERSON> et al., 2024) in this field to pre-train Timer on ever larger scales (1B/16B/28B). Given the significant value to researchers and practitioners, we extensively evaluate concurrent large models and establish the first zero-shot forecasting benchmark of LTSMs as detailed in Appendix B.2.", "cite_spans": [{"start": 88, "end": 124, "text": "(Garza & Mergenthaler-Canseco, 2023;", "ref_id": null}, {"start": 125, "end": 143, "text": "<PERSON> et al., 2023b;", "ref_id": null}, {"start": 144, "end": 161, "text": "<PERSON><PERSON> et al., 2024;", "ref_id": null}, {"start": 162, "end": 182, "text": "<PERSON><PERSON><PERSON> et al., 2024;", "ref_id": "BIBREF1"}, {"start": 183, "end": 204, "text": "<PERSON><PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF27"}, {"start": 708, "end": 726, "text": "(<PERSON><PERSON> et al., 2024)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Evaluation of Large Time Series Models", "sec_num": "4.6."}, {"text": "Quality assessments Our evaluation assesses the quality of LTSMs in Table 9 , including (1) fundamental attributes such as pre-training scale, parameters;", "cite_spans": [], "ref_spans": [{"start": 74, "end": 75, "text": "9", "ref_id": "TABREF20"}], "eq_spans": [], "section": "Evaluation of Large Time Series Models", "sec_num": "4.6."}, {"text": "(2) abilities such as applicable tasks, context length, etc. Current LTSMs essentially build upon Transformer, with a significantly smaller number of parameters compared to LLMs. There is still potential to support more tasks and longer contexts.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Evaluation of Large Time Series Models", "sec_num": "4.6."}, {"text": "Quantitative evaluations We apply official checkpoints on seven datasets that do not appear during pre-training. The performance is fairly evaluated using MSE by predicting future 96 points of all windows in each dataset. Figure 14 presents the result and rank of each model, where the topranked LTSMs are Timer, <PERSON><PERSON>a (<PERSON><PERSON> et al., 2024) , and TimesFM (<PERSON> et al., 2023b) . However, the positive correlation between performance and pre-training scale remains relatively weak, highlighting the significance of high-quality data and synchronized scaling of data and model size.", "cite_spans": [{"start": 320, "end": 338, "text": "(<PERSON><PERSON> et al., 2024)", "ref_id": null}, {"start": 353, "end": 372, "text": "(<PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [{"start": 229, "end": 231, "text": "14", "ref_id": "FIGREF9"}], "eq_spans": [], "section": "Evaluation of Large Time Series Models", "sec_num": "4.6."}, {"text": "Real-world time series analysis is increasingly underscoring the demand for large time series models (LTSM ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion and Future Work", "sec_num": "5."}, {"text": "A.1. Datasets Details Unified Time Series Dataset (UTSD) is meticulously assembled from a blend of publicly accessible online data repositories and empirical data derived from real-world machine operations. To enhance data integrity, missing values are systematically addressed using linear interpolation techniques. We follow the unified data storage format (parquet) used in (<PERSON><PERSON> et al., 2024) . For each univariate, multivariate, or irregular-sampled time series, we store them with timestamps and other metainformation in one directory using ARROW format. One dataset may composed of multiple related time series. We continue to expand the UTSD to include data from public datasets such as LOSTA1 for zero-shot forecasting. UTSD encompasses 29 individual datasets as listed with the asterisk mark in Table 2 , intricately representative of a wide range of domains.", "cite_spans": [{"start": 377, "end": 395, "text": "(<PERSON><PERSON> et al., 2024)", "ref_id": null}], "ref_spans": [{"start": 810, "end": 811, "text": "2", "ref_id": "TABREF9"}], "eq_spans": [], "section": "A. Unified Time Series Dataset", "sec_num": null}, {"text": "All datasets can be classified into ten distinct domains by their source: Energy, Environment, Health, Internet of Things (IoT), Nature, Transport, Web, CloudOps, Finance, and Multiple Sources (Misc.), where the first seven domains originally come from our curated UTSD. The datasets exhibit diverse sampling frequencies, ranging from macro intervals such as yearly and quarterly to more fine-grained intervals like hourly and minutely. Notably, several datasets can demonstrate exceptionally high-frequency sampling rates, such as the MotorImagery dataset, which operates at a millisecond frequency.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A. Unified Time Series Dataset", "sec_num": null}, {"text": "In the pursuit of advanced data analysis, we have also analyzed the stationarity manifested as ADF test statistics (<PERSON> et al., 1996) and forecastability (<PERSON><PERSON><PERSON>, 2013) . The rigorous methodologies and intricate details are elaborated in Section A.2. We utilize these statistical indicators to filter four high-quality subsets of UTSD, namely UTSD-1G, UTSD-2G, UTSD-4G, and UTSD-12G. As we expand the dataset, we continuously analyze statistical indicators and employ various methodologies to ensure the selection of high-quality datasets. LOTSA has not been sorted in this hierarchy due to its immensity.", "cite_spans": [{"start": 115, "end": 137, "text": "(<PERSON> et al., 1996)", "ref_id": "BIBREF21"}, {"start": 158, "end": 171, "text": "(<PERSON><PERSON><PERSON>, 2013)", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "A. Unified Time Series Dataset", "sec_num": null}, {"text": "We analyze each dataset within our collection, examining the time series through the lenses of stationarity and forecastability. This approach allows us to characterize the level of complexity inherent to each dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2. Statistics", "sec_num": null}, {"text": "Stationarity The stationarity of time series is a fundamental property that can be rigorously quantified using the Augmented Dickey-Fuller (ADF) test. Notably, a larger ADF test statistic typically signifies a higher degree of non-stationarity within the time series (<PERSON> et al., 1996) . In the context of datasets comprising multiple time series, the challenge of aligning these series arises, particularly when they vary in length. To address this, we implement a length-weighted ADF method that evaluates the stationarity of the entire dataset, taking into consideration the varying lengths of individual series. This approach ensures that the contribution of each series to the overall stationarity metric is proportional to its length, thus reflecting its relative significance within the dataset. By doing so, the length-weighted ADF provides a more granular and accurate depiction of the stationarity of the dataset, highlighting the impact of longer series on the overall stability and ensuring that shorter series do not disproportionately affect the assessment. The weighted statistic is formulated as follows:", "cite_spans": [{"start": 267, "end": 289, "text": "(<PERSON> et al., 1996)", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "A.2. Statistics", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T = C i=1 T i , ADF-Statistic(D) = C i=1 T i T ADF-Statistic(S (i) ),", "eq_num": "(5)"}], "section": "A.2. Statistics", "sec_num": null}, {"text": "where S i ∈ R Ti denotes the i-th series in dataset D, T i is the length of S i and C is the number of time series of dataset D.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2. Statistics", "sec_num": null}, {"text": "Forecastability Forecastability is calculated by subtracting the entropy of the series Fourier decomposition adopted from <PERSON><PERSON><PERSON> (2013) , where a higher forecastability value indicates superior predictability. Just as with the assessment of stationarity, when considering a dataset composed of multiple time series of varying lengths, it is essential to adjust the measure of forecastability to account for these differences. Therefore, we extend the concept of forecastability to a weighted version, analogous to the length-weighted ADF method, to finely tune the predictability assessment to the characteristics of each series. The weighted forecastability for a dataset can be formulated as follows:", "cite_spans": [{"start": 122, "end": 134, "text": "<PERSON><PERSON><PERSON> (2013)", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "A.2. Statistics", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T = C i=1 T i , Forecastability(D) = C i=1 T i T (1 -Entropy(F(S (i) ))),", "eq_num": "(6)"}], "section": "A.2. Statistics", "sec_num": null}, {"text": "where S i ∈ R Ti denotes the i-th time series in dataset D, T i is the length of S i and C is the number of time series in dataset D. F(S (i) ) denotes the Fourier decomposition of series S (i) . ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2. Statistics", "sec_num": null}, {"text": "UTSD is constructed with hierarchical capacities, namely UTSD-1G, UTSD-2G, UTSD-4G, and UTSD-12G, where each smaller dataset is a subset of the larger ones. We adhere to the principle of progressively increasing the complexity and pattern diversity. Hierarchical structuring allows for a nuanced analysis that accounts for different levels of data granularity and complexity, ensuring that the pattern diversity is maintained across each hierarchy of the dataset. This approach not a comprehensive evaluation across different scales but also ensures that each subset within the larger dataset offers a unique and incrementally challenging perspective, thus contributing to a more scalable pre-training.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3. UTSD Composition Analysis", "sec_num": null}, {"text": "We conduct a comprehensive analysis of individual datasets to obtain the stationarity and forecastability measures and construct the UTSD hierarchically regarding these indicators. Code for calculating the statistics is provided in the repository of UTSD. Consequently, based on the ADF-Statistic of each dataset, we categorized the predictive difficulty of the datasets into three levels: Easy, Medium, and Hard. The criteria are listed as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset Complexity", "sec_num": null}, {"text": "• Easy: ADF-Statistic < -15.00;", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset Complexity", "sec_num": null}, {"text": "• Medium: -15.00 ≤ ADF-Statistic < -5.00;", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset Complexity", "sec_num": null}, {"text": "• Hard: -5.00 ≤ ADF-Statistic.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset Complexity", "sec_num": null}, {"text": "For excessively long datasets in the temporal dimension, we additionally adopt the forecastability to assess the complexity of time series across different periods. As the capacity of UTSD increases, the periods with low forecastability will be further incorporated correspondingly. In a nutshell, larger datasets contain a greater proportion of challenging tasks as shown in Figure 16 , thereby escalating the complexity of the pre-training process. The hierarchy reflects an increase in the difficulty of patterns as the dataset size grows. This approach enables a structured examination of the learning challenges presented by different dataset sizes, underlining the intricate balance between data volume and pre-training difficulty.", "cite_spans": [], "ref_spans": [{"start": 383, "end": 385, "text": "16", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Dataset Complexity", "sec_num": null}, {"text": "Pattern Diversity Each dataset within the UTSD collection demonstrates unique patterns, highlighting the importance of maintaining pattern diversity. We build the UTSD dataset in a top-down manner, ensuring that each hierarchy within UTSD comprehensively represents all individual datasets and contains as many patterns as possible. As shown in Figure 17 , we select several representative datasets for visualization analysis:", "cite_spans": [], "ref_spans": [{"start": 352, "end": 354, "text": "17", "ref_id": null}], "eq_spans": [], "section": "Dataset Complexity", "sec_num": null}, {"text": "• AtrialFibrillation: The dataset showcases a fluctuating trend with minimal seasonality. This pattern is an indicator of irregular heart rhythm characteristics, typical in medical recordings related to cardiac health. Such fluctuations, lacking a clear seasonal pattern, are crucial for understanding the unpredictable nature of atrial fibrillation. • PigArtPressure: The dataset reveals a fluctuating trend interspersed with notable seasonality. This pattern is representative of the physiological variations in blood pressure that can occur due to environmental or internal factors. The presence of both fluctuating trends and seasonality in this dataset underscores the complex nature of biological data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset Complexity", "sec_num": null}, {"text": "• US Births: The dataset distinctly exhibits a clear trend alongside pronounced seasonality. This pattern is characteristic of demographic data, where trends and seasonal variations can reflect socio-cultural factors and environmental influences.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset Complexity", "sec_num": null}, {"text": "The consistent trend and seasonality in birth rates provide insights into population dynamics and reproductive behaviors.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset Complexity", "sec_num": null}, {"text": "To avoid selecting trivial temporal variations and provide a comprehensive representation of the varied patterns inherent in the individual datasets, we employ a downsampling technique for individual datasets. For those with a larger number of variates, we selectively choose representative variates that best encapsulate the distinct patterns of respective datasets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset Complexity", "sec_num": null}, {"text": "Similarly, for datasets with considerable temporal length, we resample them by the representative period. This methodical selection process ensures that the integrity and distinctive characteristics of each dataset are preserved, thereby maintaining the diversity of patterns across the hierarchical structure of the UTSD dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset Complexity", "sec_num": null}, {"text": "Forecasting benchmarks In the field of time series forecasting, several classical datasets such as ETT (<PERSON> et al., 2021) , ECL (<PERSON> et al., 2021 ), Traffic (<PERSON> et al., 2021) and Weather (<PERSON> et al., 2021) have become widely recognized benchmarks for evaluating model performance. However, the variability in several datasets, such as ECL, is relatively homogeneous, and they do not adequately address aspects such as non-stationarity and predictability when assessing the strengths and weaknesses of models. Consequently, the development of a new benchmark is essential. Therefore, we have carefully considered factors such as domain, number of variables, frequency, non-stationarity, and predictability, and have selected a subset from the UTSD as the new benchmark. The datasets we have selected are presented in Table 3 . Furthermore, we have evaluated our model along with other baseline models on these benchmarks. The results are presented in Table 4 . Admittedly, relying solely on these benchmarks is not sufficient to comprehensively assess model performance. We also look forward to the proposal of more diverse and comprehensive benchmarks in the future. ", "cite_spans": [{"start": 103, "end": 122, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF48"}, {"start": 125, "end": 145, "text": "ECL (<PERSON> et al., 2021", "ref_id": null}, {"start": 146, "end": 174, "text": "), Traffic (<PERSON> et al., 2021)", "ref_id": null}, {"start": 179, "end": 204, "text": "Weather (<PERSON> et al., 2021)", "ref_id": null}], "ref_spans": [{"start": 821, "end": 822, "text": "3", "ref_id": "TABREF13"}, {"start": 955, "end": 956, "text": "4", "ref_id": "TABREF14"}], "eq_spans": [], "section": "A.4. Experiments", "sec_num": null}, {"text": "To investigate the domain partitioning of UTSD, we use different domains of UTSD as the source and adapt the trained model to different target datasets to establish in-domain and out-of-domain transfer. The results in Table 5 indicate that in-domain transfer can further enhance the downstream performance. Additionally, as the number of downstream data samples increases, the relative improvement of pre-training will gradually diminish, and even lead to negative transfer in some out-of-domain scenarios. It provides a promising direction to develop domain-specific models. Based on the constructed UTSD datasets of different sizes and difficulties in the unified single series sequence (S3) format, Timer is pre-trained with increasing data sizes and model parameters to validate the scalability. Detailed configurations and parameter counts of the pre-trained models involved in this paper are provided in Table 6 . All experiments are implemented in PyTorch (<PERSON><PERSON><PERSON> et al., 2019) and trained using NVIDIA A100 Tensor Core GPU. We use AdamW (Kingma & Ba, 2015) as the optimizer and cosine annealing algorithm for learning rate decay. The base learning rate is 5 × 10 -5 , and the final learning rate is 2 × 10 -6 . The decay steps are proportional to the number of training steps of 10 epochs. During pre-training, we use N = 15 as the number of tokens, and the batch size is set to 8192.", "cite_spans": [{"start": 963, "end": 984, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF42"}, {"start": 1045, "end": 1064, "text": "(Kingma & Ba, 2015)", "ref_id": "BIBREF32"}], "ref_spans": [{"start": 224, "end": 225, "text": "5", "ref_id": "TABREF15"}, {"start": 916, "end": 917, "text": "6", "ref_id": "TABREF16"}], "eq_spans": [], "section": "Domain transfer", "sec_num": null}, {"text": "During the pre-training on the UTSD-1G to UTSD-4G, we adopt a global shuffle strategy by loading the whole time series into the memory. Due to the much greater data scale of UTSD-12G compared to any commonly used time series dataset in the past, it is difficult to load all 12GB of the pre-training dataset into memory for global shuffling. Therefore, we use a local shuffle strategy, which randomly selects and divides the 12GB pre-training dataset into three 4G subsets in the storage space through file selection and segmentation, and then takes turns loading them into memory for pre-training with global steps. In this strategy, we also ensure the continuity of learning rate decay.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Domain transfer", "sec_num": null}, {"text": "We introduce the details of downstream experiments and present the generative scheme for each task, including time series forecast, imputation, and anomaly detection. Configurations for downstream adaptation are listed in Table 8 . Corresponding detailed results are provided in Section C. And showcases of downstream tasks are shown in Figure 19 , 20, and 21.", "cite_spans": [], "ref_spans": [{"start": 228, "end": 229, "text": "8", "ref_id": null}, {"start": 344, "end": 346, "text": "19", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "Forecasting The downstream forecasting task is tested on the real-world datasets, including (1) ETT (<PERSON> et al., 2021) (5) PEMS contains California public transportation network data collected through a 5-minute window with the same four common subsets (PEMS03, PEMS04, PEMS07, PEMS08) used in SCINet (<PERSON> et al., 2022) .", "cite_spans": [{"start": 100, "end": 119, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF48"}, {"start": 302, "end": 320, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "We adopt the autoregressive generation training objective (<PERSON><PERSON> et al., 2000) for downstream forecasting datasets in the fine-tuning stage. Specifically, we divide the lookback length into N = 7 tokens with the segment length S = 96. The model naturally outputs N next tokens, which we calculate the mean squared error (MSE) of the N tokens with corresponding ground truth and backpropagate the loss. During inference, we conduct iterative multi-step forecasting by concatenating the forecasted result with the lookback series and repeatedly adopting the model to generate the next token until the total length of predicted tokens reaches the expected length. If exceeding the predicted length, we will crop the excess value of the end.", "cite_spans": [{"start": 58, "end": 79, "text": "(<PERSON><PERSON> et al., 2000)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "For constructing data-scarce scenarios, we perform retrieval with the uniform interval in the training split according to the sampling ratio and conduct random shuffling at the end of each epoch to train the model. The construction pipeline with the fixed random seed ensures the reproducibility of our experimental results. In order to maintain comparability with previous benchmarks, we keep the same validation and testing sets of original downstream datasets and train the baseline model and Timer with the same set of training samples.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "Imputation Considering the real-world scenario that missing values at time points often appear in succession, we adjust the previous point-level imputation proposed by TimesNet (Wu et al., 2022) and increase the difficulty of the task, that is, changing the masked unit from point to time series segment. The protocol poses challenges to recovering successive points, which underscore higher demands for the model capacity to restore a span of series variations. Concretely, for a time series consisting of N = 8 segments with a length of 24, we randomly mask several segments as zeros except for the first segment, ensuring that the first segment is observed by the model to learn about initial series variations for imputation. For the training objective of downstream adaption, we adopt the denoising autoencoding (<PERSON><PERSON> et al., 2020) , which takes the masked parts as special tokens and unmasked segments as tokens as the model input. Due to the generative capability of Timer acquired by pre-training, we regard outputted tokens as the next predicted tokens and backpropagate the reconstruction error between the generated next token of the masked segment with the ground truth. During inference, we take the MSE of the masked segments as the indicator to evaluate the imputation performance. Based on the above protocol, we conduct the imputation task on the same datasets of the forecasting task in Table 7 .", "cite_spans": [{"start": 168, "end": 194, "text": "TimesNet (<PERSON> et al., 2022)", "ref_id": null}, {"start": 817, "end": 838, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": null}], "ref_spans": [{"start": 1413, "end": 1414, "text": "7", "ref_id": "TABREF18"}], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "Table 8 . Detailed explanation of model hyperparameters and corresponding parameter quantities. We adopt the learning rate schedule strategy with exponential decay at a base of 0.5 under all three downstream tasks. Anomaly detection For anomaly detection, prevalent protocols represented by Anomaly Transformer (<PERSON> et al., 2021) and TimesNet (<PERSON> et al., 2022) adopt the reconstructive approach that learns a feature extractor to reconstruct raw series, and the output is regarded as standard values. With all the mean squared errors between the standard and input series from the datasets, a specific threshold with the given quantile is determined to label the anomalies.", "cite_spans": [{"start": 311, "end": 328, "text": "(<PERSON> et al., 2021)", "ref_id": null}, {"start": 333, "end": 359, "text": "TimesNet (<PERSON> et al., 2022)", "ref_id": null}], "ref_spans": [{"start": 6, "end": 7, "text": "8", "ref_id": null}], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "Considering the prevalent scenarios of anomaly detection by monitoring real-time measurements, the quick judgment of on-the-fly time series anomaly can be more practical in the real world. Therefore, we propose a predictive protocol of anomaly detection based on generative models. Concretely, we use the observed segments to predict the future segment, and the predicted segment will be established as the standard to be compared with the actual value received. We adopt the UCR Anomaly Archive proposed by <PERSON> & <PERSON> (2021) . The task is to find the position of an anomaly in the test series based on a single normal series for training, which is an extremely data-scarce scenario with only one available sample. For downstream adaption, we adopt the same next token prediction as the pre-training, that is, training Timer with the lookback series containing N = 7 segments of the length S = 96 to generate the next token with length 96, which is regarded as the standard value. After training, we record the MSE of all segments in the test set and sort them in descending order. We find the first segment hit the anomaly interval labeled in the dataset within the first α quantile, and we record the quantile. Based on the above protocol, the real-time judgment ability of the model for sudden anomalies can be predictively examined. Detailed quantiles of Timer in 250 tasks are provided in Table 15 . With more complex time series anomalies introduced in UCR Anomaly Archive, we hope to establish a reasonable and challenging benchmark in the field of anomaly detection.", "cite_spans": [{"start": 508, "end": 525, "text": "Wu & Ke<PERSON>h (2021)", "ref_id": null}], "ref_spans": [{"start": 1400, "end": 1402, "text": "15", "ref_id": "TABREF15"}], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "Zero-shot forecasting We conduct zero-shot forecasting experiments on seven datasets from iTransformer (<PERSON> et al., 2023b) . Notably, PEMS datasets are not included, as they have already appeared in the LOSTA dataset for pre-training.", "cite_spans": [{"start": 103, "end": 122, "text": "(<PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "We apply the same data-split strategy as Autoformer (<PERSON> et al., 2021) and calculate the averaged MSE of all predict-96 windows in the test split. We evaluate five open-source large time series models, including Timer, <PERSON><PERSON><PERSON> (<PERSON><PERSON> et al., 2024) , TimesFM (<PERSON> et al., 2023b) , Chronos (<PERSON><PERSON><PERSON> et al., 2024) , and MOMENT (<PERSON><PERSON><PERSON> et al., 2024) . We further assess the qualities in Table 9 , which includes more LTSMs and summarizes several attributes and abilities of large models.", "cite_spans": [{"start": 52, "end": 69, "text": "(<PERSON> et al., 2021)", "ref_id": null}, {"start": 225, "end": 243, "text": "(<PERSON><PERSON> et al., 2024)", "ref_id": null}, {"start": 254, "end": 273, "text": "(<PERSON> et al., 2023b)", "ref_id": null}, {"start": 284, "end": 305, "text": "(<PERSON><PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF1"}, {"start": 319, "end": 341, "text": "(<PERSON><PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF27"}], "ref_spans": [{"start": 385, "end": 386, "text": "9", "ref_id": "TABREF20"}], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "• MOMENT: MOMENT2 trained by masking modeling is applied to zero-shot forecasting by concatenating the lookback series with a mask with the length to be predicted. The mask through the model is regarded as the prediction.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "• Chronos: Chronos3 is a probabilistic forecaster. Chronos-S1 means sampling one prediction trajectory and Chronos-S20 means sampling 20 trajectories and using the average trajectory.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "• TimesFM: We use the official checkpoint from HuggingFace4 , which supports various input and output lengths.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "• Moiria: The Moiria family5 has three different sizes, labeled as Moiria-S, Moiria-M, and Moiria-L.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "• Timer: We provide three versions with increased scopes of pre-training. Timer-1B is pre-trained on UTSD; Timer-16B is pre-trained on UTSD and Buildings900K (<PERSON><PERSON> et al., 2023) ; and Timer-28B is pre-trained on UTSD and LOTSA. ", "cite_spans": [{"start": 158, "end": 178, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "B.2. Downstream Tasks", "sec_num": null}, {"text": "We provide all the results of the forecasting task in Figure 6 . As shown in Table 10 , we include six representative real-world datasets, demonstrating that Timer achieves state-of-the-art forecasting performance and the large-scale pre-training helps to alleviate performance degradation as the available downstream samples decrease. ", "cite_spans": [], "ref_spans": [{"start": 61, "end": 62, "text": "6", "ref_id": "FIGREF4"}, {"start": 83, "end": 85, "text": "10", "ref_id": "TABREF21"}], "eq_spans": [], "section": "C.1. Time Series Forecasting", "sec_num": null}, {"text": "In this section, we provide the detailed results of the imputation task, including Timer trained from scratch and adapting pre-trained models with 5% available samples in Table 11 , 20% samples in Table 12 , and full samples in Table 13 on the downstream task. We also report the results of TimesNet at the above three ratios in Table 14 . Based on the result, we provided an improvement in imputation performance before and after pre-training with {5%, 20%, 100%} samples in Figure 8 and 18, reflecting the benefits of autoregressive pre-training in segment-wise imputation task.", "cite_spans": [], "ref_spans": [{"start": 177, "end": 179, "text": "11", "ref_id": "TABREF22"}, {"start": 203, "end": 205, "text": "12", "ref_id": "TABREF23"}, {"start": 234, "end": 236, "text": "13", "ref_id": "TABREF24"}, {"start": 335, "end": 337, "text": "14", "ref_id": "TABREF14"}, {"start": 483, "end": 484, "text": "8", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "C.2. Imputation", "sec_num": null}, {"text": "In this section, we provide detailed results of anomaly detection in Table 15 , including the results of Timer from scratch and pre-trained. We conducted experiments on all 250 datasets of UCR Anomaly Archive and calculated the corresponding α quantiles. The results show that the pre-trained Timer can detect time series anomalies with smaller α on most datasets.", "cite_spans": [], "ref_spans": [{"start": 75, "end": 77, "text": "15", "ref_id": "TABREF15"}], "eq_spans": [], "section": "C.3. Anomaly Detection", "sec_num": null}, {"text": "We provide detailed downstream forecasting results conducted on PEMS subsets with the scaling of model size (Figure 10 ) and data size (Figure 11 ). As shown in Table 16 , it supports the scalability of our decoder-only Timer trained in GPT-style, ", "cite_spans": [], "ref_spans": [{"start": 116, "end": 118, "text": "10", "ref_id": null}, {"start": 143, "end": 145, "text": "11", "ref_id": "FIGREF7"}, {"start": 167, "end": 169, "text": "16", "ref_id": "TABREF25"}], "eq_spans": [], "section": "C.4. Scalability", "sec_num": null}, {"text": "UTSD is constructed with hierarchical capacities. Though it is helpful to study the scalability of the model, it is not big enough since we have witnessed recent work claims the pre-training on ten and even a hundred billion time points. Therefore, we advocate for the ongoing expansion of data infrastructure while upholding high quality and hierarchy, which may significantly advance the time series community. In terms of the method, this work aims at an early but important development of large models. Despite the generalization, scalability, and task-generality that Timer has achieved, time series classification has not been unified in our generative formulations and Timer does not yet support probabilistic forecasting and specially adapts for multiple variables. It also leaves for better zero-shot generalization and advanced abilities, such as in-context learning and multi-modality, which are scheduled to be developed by ever-large pre-training.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E. <PERSON>s", "sec_num": null}, {"text": "Real-world applications This paper develops large models for the field of time series. We present a general-purpose time series analysis model to handle data-scarce scenarios. Given the state-of-the-art performance of Timer, this model may be applied to many real-world applications, which helps our society prevent risks in advance and make better decisions with limited available samples. Our paper mainly focuses on scientific research and has no obvious negative social impact.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F. Societal Impacts", "sec_num": null}, {"text": "In this paper, we release a high-quality dataset for scalable pre-training. Different from prior works, the dataset is not merely aggregation but follows deftly curation. Based on it, the research on scalable time series architectures and pre-training techniques can be facilitated. Towards large time series models, the proposed Timer shows its generalization and versatility in many tasks. The regime of generative pre-training and autoregression can be instructive for future research. 18 . Zero-shot forecasting evaluation. We extensively evaluate available large time series models. We provided the average rank on all downstream datasets, where the lower is better. For the probabilistic forecaster Chronos: S1 means sampling one trajectory and S20 means sampling 20 trajectories and using the average. '-' indicates that multi-step error accumulation leads to failure predictions. ", "cite_spans": [], "ref_spans": [{"start": 489, "end": 491, "text": "18", "ref_id": "TABREF6"}], "eq_spans": [], "section": "Academic research", "sec_num": null}, {"text": "https://huggingface.co/datasets/Salesforce/lotsa data", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://huggingface.co/AutonLab/MOMENT-1-large", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://huggingface.co/amazon/chronos-t5-large", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://huggingface.co/google/timesfm-1.0-200m", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://huggingface.co/collections/Salesforce/moirai-10-r-models-65c8d3a94c51428c300e0742", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by the National Natural Science Foundation of China (62022050 and U2342217), the BNRist Innovation Fund (BNR2024RC01010), and the National Engineering Research Center for Big Data Software.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "following the scaling law (<PERSON> et al., 2020) towards large time series models.", "cite_spans": [{"start": 26, "end": 47, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF31"}], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "In this section, we provide detailed results of zero-shot forecasting in Table 18 . We conducted experiments on seven datasets that are not included in the pre-training corpora of LTSMs. The results show that the top-ranked LTSMs are Timer, Moiria, and TimesFM. The performance of probabilistic forecaster Chronos can be improved by sampling more trajectories. It can still be an issue that scaling behavior is not evident on some datasets in the zero-shot scenario, and the failure of multi-step prediction can also appear in some models, indicating the development of zero-shot LTSMs is still in the early stage.", "cite_spans": [], "ref_spans": [{"start": 79, "end": 81, "text": "18", "ref_id": null}], "eq_spans": [], "section": "C.5. Zero-shot forecasting", "sec_num": null}, {"text": "To present a clear performance of our proposed Timer, we provide visualizations for downstream forecasting, imputation, and anomaly detection tasks in Figure 19 , 20 and 21. The forecasting and imputation contain experimental results at different sample ratios. For anomaly detection, we provide the position of the anomaly and the generated normal series by Timer. 4 14.3 23.8 16.7 2.4 0.5 14.0 2.0 1.3 0.0 0.4 0.4 6 22.8 4.5 3.0 3.0 1.3 3.0 40.5 4.8 1.7 96.7 30.0 27.1 2.4 2.1 1.7 3.3 1.7 6.2 10.0 0.4 9.7 9.3 29.7 0.9 91.0 7 0.7 1.4 23.5 11.1 4.2 2.6 1.3 17.9 5.1 1.3 68.8 2.8 9.5 15.8 59.1 11.9 2.6 0.5 16.4 20.7 1.3 0.5 36.4 3.3 37.5 8 96.4 1.5 0.3 0.3 15.0 0.7 30.3 23.1 0.1 17.9 16.7 29.3 28.6 57.1 14.8 21.3 0.3 4.6 0.2 3.2 1.6 15.0 14.1 14.8 9.4 9 0.3 21.3 79.6 2.9 14.1 4.0 1.6 6.0 0.1 49.3 31.7 30.1 0.0 0.0 0.1 0.0 0.2 0.0 0.0 0.0 0.0 0.1 17.5 13.3 7.3 10 0.1 10.4 1.0 10.0 18.6 16.1 9.6 0.9 40.1 0.7 24.4 0.7 2.5 2.4 9.8 1.1 7.4 7.6 1.2 0.9 4.4 10.9 19.7 53.8 30.6 TIMER (PRE-TRAINED) 1 3.9 5.8 1.5 1.2 23.8 19.0 7.1 47.6 11.9 2.4 0.5 6.7 2.0 32.9 0.0 0.4 0.4 26.8 3.0 3.0 6.1 1.3 1.5 2.4 4.8 2 1.7 10.0 3.3 6.2 4.8 2.1 1.7 3.3 1.7 2.1 6.7 0.4 2.3 4.7 28.9 3.4 89.3 0.7 1.4 21.0 9.0 3.3 2.6 2.6 7.7 3 5.1 1.3 72.9 2.8 2.4 36.5 45.5 22.4 6.3 3.4 9.8 18.8 7.7 1.9 63.6 8.3 56.2 77.4 1.5 0.3 0.3 24.7 0.7 15.8 8.7 4 0.6 0.3 19.0 14.3 66.9 92.9 27.8 14.3 0.3 3.9 0.2 5.9 6.0 17.0 5.6 13.9 5.3 0.9 1.2 38.1 0.4 1.5 18.3 1.7 0.4 5 31.7 0.7 1.3 26.4 12.4 1.9 54.2 77.5 0.6 1.5 0.6 1.2 16.7 16.7 4.8 35.7 9.5 2.4 0.5 7.3 2.0 29.4 0.0 0.4 0.4 6 20.6 3.0 3.0 1.5 1.3 1.5 2.4 2.4 1.7 18.3 1.7 6.2 2.4 2.1 1.7 3.3 1.7 2.1 6.7 0.4 2.7 4.3 28.5 1.3 90.6 7 0.7 0.9 21.0 9.0 3.3 1.3 1.3 3.8 5.1 1.3 85.4 2.8 3.1 28.9 25.5 20.7 3.0 4.5 13.3 15.8 2.6 1.2 47.0 3.3 47.9 8 47.6 1.5 0.3 0.3 19.7 1.0 13.3 7.2 0.1 0.1 15.6 96.5 96.3 74.1 13.7 19.0 0.3 3.3 0.2 5.4 8.9 13.4 14.1 14.5 4.2 9 0.3 1.2 63.5 0.3 19.2 1.7 0.3 18.6 0.1 29.8 38.9 16.2 0.0 0.0 0.0 0.0 6.5 0.0 0.0 0.0 0.0 0.1 16.5 11.0 5.7 10 0.1 9.2 0.6 8.0 6.9 9.8 1. ", "cite_spans": [{"start": 366, "end": 382, "text": "4 14.3 23.8 16.7", "ref_id": null}], "ref_spans": [{"start": 158, "end": 160, "text": "19", "ref_id": null}], "eq_spans": [], "section": "<PERSON><PERSON> Showcase", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Gluonts: Probabilistic and neural time series modeling in python", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Bohlke-Schneider", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["C"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Rangapuram", "suffix": ""}, {"first": "D", "middle": [], "last": "Salinas", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Journal of Machine Learning Research", "volume": "21", "issue": "116", "pages": "1--6", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, D. C., Rangapuram, S., Salinas, D., <PERSON>, <PERSON>, et al. Glu- onts: Probabilistic and neural time series modeling in python. Journal of Machine Learning Research, 21(116): 1-6, 2020.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Learning the language of time series", "authors": [{"first": "A", "middle": ["F"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Turkmen", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "S<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["S"], "last": "Rangapuram", "suffix": ""}, {"first": "S", "middle": ["P"], "last": "Arango", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2403.07815"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, O., Rangapuram, S. S., Arango, S. P., <PERSON>, <PERSON>, et al. Chronos: Learning the language of time series. arXiv preprint arXiv:2403.07815, 2024.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "An empirical evaluation of generic convolutional and recurrent networks for sequence modeling", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["Z"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "Koltun", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1803.01271"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. An empirical evalua- tion of generic convolutional and recurrent networks for sequence modeling. arXiv preprint arXiv:1803.01271, 2018.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Bert pre-training of image transformers", "authors": [{"first": "H", "middle": [], "last": "Bao", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Pi<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2106.08254"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Bert pre-training of image transformers. arXiv preprint arXiv:2106.08254, 2021.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "A neural probabilistic language model", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2000, "venue": "Advances in neural information processing systems", "volume": "13", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, P. A neural proba- bilistic language model. Advances in neural information processing systems, 13, 2000.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Residential Power and Battery Data", "authors": [{"first": "C", "middle": [], "last": "Bergmeir", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"DOI": ["10.5281/zenodo.8219786"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, P. Res- idential Power and Battery Data, 2023. URL https: //doi.org/10.5281/zenodo.8219786.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "On the opportunities and risks of foundation models", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Arora", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Bohg", "suffix": ""}, {"first": "A", "middle": [], "last": "Bosselut", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2108.07258"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. On the opportunities and risks of foundation models. arXiv preprint arXiv:2108.07258, 2021.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "<PERSON> and jenkins: time series analysis, forecasting and control", "authors": [{"first": "G", "middle": [], "last": "Box", "suffix": ""}], "year": 2013, "venue": "A Very British Affair: Six Britons and the Development of Time Series Analysis During the 20th Century", "volume": "", "issue": "", "pages": "161--215", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and jen<PERSON>: time series analysis, forecasting and control. In A Very British Affair: Six Britons and the Development of Time Series Analysis During the 20th Century, pp. 161-215. Springer, 2013.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Time series analysis: forecasting and control", "authors": [{"first": "G", "middle": ["E"], "last": "Box", "suffix": ""}, {"first": "G", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": ["C"], "last": "Reinsel", "suffix": ""}, {"first": "G", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, G. C., and <PERSON><PERSON>, G. M. Time series analysis: forecasting and control. John Wiley & Sons, 2015.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Lof: identifying density-based local outliers", "authors": [{"first": "M", "middle": ["M"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H.-P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2000, "venue": "Proceedings of the 2000 ACM SIGMOD international conference on Management of data", "volume": "", "issue": "", "pages": "93--104", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: identifying density-based local outliers. In Proceedings of the 2000 ACM SIGMOD international conference on Management of data, pp. 93-104, 2000.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Flu portal dashboard", "authors": [{"first": "", "middle": [], "last": "Cdc", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "CDC. Flu portal dashboard, 2017. URL https://gis.cdc.gov/grasp/fluview/ fluportaldashboard.html. Accessed: [insert date of access].", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Llm4ts: Two-stage fine-tuning for time-series forecasting with pre-trained llms", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W.-C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T.-F", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.08469"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>, T.-<PERSON><PERSON> Llm4ts: Two-stage fine-tuning for time-series forecasting with pre-trained llms. arXiv preprint arXiv:2308.08469, 2023.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Site Air Quality. UCI Machine Learning Repository", "authors": [{"first": "S", "middle": ["Beijing"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Multi", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"DOI": ["10.24432/C5RK5G"]}, "num": null, "urls": [], "raw_text": "<PERSON>, S. Beijing Multi-Site Air Quality. UCI Machine Learning Repository, 2019. DOI: https://doi.org/10.24432/C5RK5G.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "A simple framework for contrastive learning of visual representations", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "1597--1607", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, G. A simple framework for contrastive learning of visual rep- resentations. In International conference on machine learning, pp. 1597-1607. PMLR, 2020.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Why can gpt learn in-context? language models secretly perform gradient descent as meta optimizers", "authors": [{"first": "D", "middle": [], "last": "Dai", "suffix": ""}, {"first": "Y", "middle": [], "last": "Sun", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2212.10559"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Why can gpt learn in-context? language models secretly per- form gradient descent as meta optimizers. arXiv preprint arXiv:2212.10559, 2022.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Longterm forecasting with tide: Time-series dense encoder", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Kong", "suffix": ""}, {"first": "A", "middle": [], "last": "Leach", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.08424"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Long- term forecasting with tide: Time-series dense encoder. arXiv preprint arXiv:2304.08424, 2023a.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "A decoderonly foundation model for time-series forecasting", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Kong", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.10688"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y. A decoder- only foundation model for time-series forecasting. arXiv preprint arXiv:2310.10688, 2023b.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "The ucr time series archive", "authors": [{"first": "H", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Bagnall", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C.-C", "middle": ["M"], "last": "Yeh", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["A"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "IEEE/CAA Journal of Automatica Sinica", "volume": "6", "issue": "6", "pages": "1293--1305", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, S., Ratana<PERSON>atana, C. A., and <PERSON><PERSON>, E. The ucr time series archive. IEEE/CAA Journal of Automatica Sinica, 6(6):1293-1305, 2019.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Pre-training of deep bidirectional transformers for language understanding", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M.-W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1810.04805"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Pre-training of deep bidirectional transformers for lan- guage understanding. arXiv preprint arXiv:1810.04805, 2018.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "A simple pre-training framework for masked time-series modeling", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Simmtm", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.00861"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>: A simple pre-training frame- work for masked time-series modeling. arXiv preprint arXiv:2302.00861, 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Synthetically-trained zero-shot forecasting", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "White", "suffix": ""}, {"first": "", "middle": [], "last": "Forecastpfn", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.01933"]}, "num": null, "urls": [], "raw_text": "<PERSON>, S., <PERSON>, G. S., Mo<PERSON>, C., Naidu, S., and White, C. <PERSON>: Synthetically-trained zero-shot forecasting. arXiv preprint arXiv:2311.01933, 2023.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Efficient tests for an autoregressive unit root", "authors": [{"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["H"], "last": "Stock", "suffix": ""}], "year": 1996, "venue": "Econometrica", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, J. H. Efficient tests for an autoregressive unit root. Econometrica, 1996.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Buildingsbench: A largescale dataset of 900k buildings and benchmark for shortterm load forecasting", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: A large- scale dataset of 900k buildings and benchmark for short- term load forecasting. Advances in Neural Information Processing Systems, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "The interpolation of time series by related series", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1962, "venue": "Journal of the American Statistical Association", "volume": "57", "issue": "300", "pages": "729--757", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>. The interpolation of time series by related series. Journal of the American Statistical Association, 57(300):729-757, 1962.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Monash time series forecasting archive", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Bergmeir", "suffix": ""}, {"first": "G", "middle": ["I"], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Montero-Manso", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2105.06643"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, R<PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> time series forecasting archive. arXiv preprint arXiv:2105.06643, 2021.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Forecastable component analysis", "authors": [{"first": "G", "middle": [], "last": "Goerg", "suffix": ""}], "year": 2013, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "64--72", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> component analysis. In Interna- tional conference on machine learning, pp. 64-72. PMLR, 2013.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Moment: A family of open time-series foundation models", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Cai", "suffix": ""}, {"first": "S", "middle": [], "last": "Li", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2402.03885"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: A family of open time-series foundation models. arXiv preprint arXiv:2402.03885, 2024.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Long short-term memory", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>reiter", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1997, "venue": "Neural computation", "volume": "9", "issue": "8", "pages": "1735--1780", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> short-term memory. Neural computation, 9(8):1735-1780, 1997.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "A unified library towards efficient and comprehensive urban spatial-temporal prediction", "authors": [{"first": "J", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "C", "middle": [], "last": "Han", "suffix": ""}, {"first": "W", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "W", "middle": ["X"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Libcity", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.14343"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>: A unified library towards efficient and compre- hensive urban spatial-temporal prediction. arXiv preprint arXiv:2304.14343, 2023.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Time-llm: Time series forecasting by reprogramming large language models", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Shi", "suffix": ""}, {"first": "P.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y.-F", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "Pan", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.01728"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Time-llm: Time series forecasting by reprogramming large language models. arXiv preprint arXiv:2310.01728, 2023.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Scaling laws for neural language models", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Chess", "suffix": ""}, {"first": "R", "middle": [], "last": "Child", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2001.08361"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> laws for neural language models. arXiv preprint arXiv:2001.08361, 2020.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "A method for stochastic optimization", "authors": [{"first": "D", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ba", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>: A method for stochastic optimization. In ICLR, 2015. URL http://arxiv. org/abs/1412.6980.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Scinet: time series modeling and forecasting with sample convolution and interaction", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: time series modeling and forecasting with sample convolution and interaction. NeurIPS, 2022.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Largest: A benchmark dataset for large-scale traffic forecasting", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Hu", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Largest: A benchmark dataset for large-scale traffic forecasting. In Advances in Neural Information Processing Systems, 2023a.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "itransformer: Inverted transformers are effective for time series forecasting", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Hu", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Ma", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.06625"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. itransformer: Inverted transformers are effective for time series forecasting. arXiv preprint arXiv:2310.06625, 2023b.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Autotimes: Autoregressive time series forecasters via large language models", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Qin", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2402.02370"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. Auto- times: Autoregressive time series forecasters via large lan- guage models. arXiv preprint arXiv:2402.02370, 2024.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "A machine learning approach for forecasting hierarchical time series", "authors": [{"first": "P", "middle": [], "last": "Mancuso", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["M"], "last": "Sudoso", "suffix": ""}], "year": 2021, "venue": "Expert Systems with Applications", "volume": "182", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, A. M. A machine learning approach for forecasting hierarchical time series. Expert Systems with Applications, 182:115102, 2021.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Subseasonalclimateusa: A dataset for subseasonal forecasting and benchmarking", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Orenstein", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Lev<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Subseasonalclimateusa: A dataset for subseasonal forecasting and benchmarking. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Climatelearn: Benchmarking machine learning for weather and climate modeling", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Bansal", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Benchmarking machine learning for weather and climate modeling. Advances in Neural Infor- mation Processing Systems, 36, 2024.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "A time series is worth 64 words: Long-term forecasting with transformers", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": ["H"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.14730"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. A time series is worth 64 words: Long-term forecasting with transformers. arXiv preprint arXiv:2211.14730, 2022.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Gpt-4 technical report", "authors": [{"first": "R", "middle": [], "last": "Openai", "suffix": ""}], "year": 2023, "venue": "View in Article", "volume": "2", "issue": "13", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "OpenAI, R. Gpt-4 technical report. arxiv 2303.08774. View in Article, 2:13, 2023.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "An imperative style, high-performance deep learning library", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Gross", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Gimelshein", "suffix": ""}, {"first": "L", "middle": [], "last": "Antiga", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>son", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Pytor<PERSON>", "suffix": ""}], "year": 2019, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Brad<PERSON>, J<PERSON>, <PERSON>, <PERSON>, <PERSON>, T<PERSON>, <PERSON>, <PERSON>, <PERSON>, N., Antiga, L., Des<PERSON>, A<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, S. <PERSON>: An imperative style, high-performance deep learning library. In NeurIPS, 2019.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Improving language understanding by generative pre-training", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, et al. Improving language understanding by generative pre-training. 2018.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Language models are unsupervised multitask learners", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Child", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "OpenAI blog", "volume": "1", "issue": "8", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Language models are unsupervised multitask learners. OpenAI blog, 1(8):9, 2019.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Learning transferable visual models from natural language supervision", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Hall<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "8748--8763", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S., <PERSON>, G<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Learning transferable visual models from natural language supervision. In International conference on machine learning, pp. 8748-8763. PMLR, 2021.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "A survey of large language models", "authors": [{"first": "W", "middle": ["X"], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Min", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.18223"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. A survey of large language models. arXiv preprint arXiv:2303.18223, 2023.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Forecasting fine-grained air quality based on big data", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Li", "suffix": ""}, {"first": "R", "middle": [], "last": "Li", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Li", "suffix": ""}], "year": 2015, "venue": "Proceedings of the 21th ACM SIGKDD international conference on knowledge discovery and data mining", "volume": "", "issue": "", "pages": "2267--2276", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, T. Forecasting fine-grained air quality based on big data. In Proceedings of the 21th ACM SIGKDD international conference on knowledge discovery and data mining, pp. 2267-2276, 2015.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Informer: Beyond efficient transformer for long sequence time-series forecasting", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "35", "issue": "", "pages": "11106--11115", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Beyond efficient transformer for long sequence time-series forecasting. In Proceedings of the AAAI conference on artificial intelligence, volume 35, pp. 11106-11115, 2021.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "A dataset for spatial dynamic wind power forecasting challenge at kdd cup 2022", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Su", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Ma", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Sdwpf", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2208.04360"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, D. <PERSON>d<PERSON>: A dataset for spatial dynamic wind power forecasting challenge at kdd cup 2022. arXiv preprint arXiv:2208.04360, 2022.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "One fits all: Power general time series analysis by pretrained lm", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "R", "middle": [], "last": "", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.11939"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> fits all: Power general time series analysis by pretrained lm. arXiv preprint arXiv:2302.11939, 2023.", "links": null}}, "ref_entries": {"FIGREF0": {"fig_num": "2", "num": null, "uris": null, "type_str": "figure", "text": "Figure 2. Illustration of Unified Time Series Dataset (UTSD) that is composed of various time series domains with hierarchical capacities."}, "FIGREF1": {"fig_num": "3", "num": null, "uris": null, "type_str": "figure", "text": "Figure 3. Pre-training strategy for heterogeneous time series."}, "FIGREF2": {"fig_num": "4", "num": null, "uris": null, "type_str": "figure", "text": "Figure 4. Architectures of typical Transformer-based forecasters."}, "FIGREF3": {"fig_num": "5", "num": null, "uris": null, "type_str": "figure", "text": "Figure 5. Illustration of our generative task unification: (1) Generative pre-trained Timer can naturally predict the next series by the iterative autoregression; (2) By introducing masked tokens during adaptation, Timer generates imputations with the previous context and assemble them with the observed part; (3) We propose predictive anomaly detection by predicting normal series in advance."}, "FIGREF4": {"fig_num": "6", "num": null, "uris": null, "type_str": "figure", "text": "Figure 6. Forecasting performance of Timer obtained by training from scratch and fine-tuning from the pre-trained model on different data scarcities. State-of-the-art small deep forecasters trained on full samples are provided the SOTA baseline. A smaller MSE indicates better results. Detailed results are provided in Table10."}, "FIGREF5": {"fig_num": "8", "num": null, "uris": null, "type_str": "figure", "text": "Figure8. Pre-training benefit of Timer on the downstream imputation task with 5% available samples. Following TimesNet(<PERSON>  et al., 2022), each dataset is imputed with four mask ratios in {12.5%, 25%, 37.5%, 50%} and we calculate the average reduced imputation error in MSE relative to training from scratch. Additional results of other data scarcities are provided in Figure18."}, "FIGREF6": {"fig_num": "9", "num": null, "uris": null, "type_str": "figure", "text": "Figure 9. Downstream anomaly detection results of Timer obtained by training from scratch and adapting with the pre-trained model."}, "FIGREF7": {"fig_num": "11", "num": null, "uris": null, "type_str": "figure", "text": "Figure 11. Timer trained on larger dataset demonstrates better performance on downstream forecasting. Models are configured with L = 8 and D = 1024. Detailed results are provided in Table16."}, "FIGREF8": {"fig_num": "12", "num": null, "uris": null, "type_str": "figure", "text": "Figure 12. Training loss of candidate backbones. Model dimension and layer number are consistently chosen for a fair comparison."}, "FIGREF9": {"fig_num": "14", "num": null, "uris": null, "type_str": "figure", "text": "Figure 14. Zero-shot evaluation on LTSMs. The top three models for each dataset are highlighted on the leaderboard. Average Rank of each model is calculated on the benchmarks in which the model has participated. Detailed results are provided in Table18."}, "FIGREF10": {"fig_num": "15", "num": null, "uris": null, "type_str": "figure", "text": "Figure15. Performance of Timer/PatchTST for all forecast lengths. We conduct rolling forecasting on a single 672-pred-96 model."}, "FIGREF11": {"fig_num": null, "num": null, "uris": null, "type_str": "figure", "text": "MODEL TIMER-1B TIMER-16B TIMER-28B MOIRAI-S MOIRAI-M MOIRAI-L MOMENT TIMESFM CHRONOS-S1 CHRONOS-S20 ETTH1 0"}, "FIGREF12": {"fig_num": "20", "num": null, "uris": null, "type_str": "figure", "text": "Figure 20. Visualization of anomaly detection results of Timer on partial UCR Anomaly Archive (Wu & Keogh, 2021). The masked part represents the abnormal position, and the model locates the abnormal interval by generating results that deviate from the abnormal series."}, "TABREF0": {"html": null, "num": null, "content": "<table><tr><td>3HUIRUPDQFH'HJUDGHG</td><td>(77K (&amp;/</td><td>:HDWKHU 3(06</td></tr><tr><td/><td>7UDIILF</td><td>3(06</td></tr><tr><td/><td colspan=\"2\">3HUFHQWDJHRI7UDLQLQJ6DPSOHV</td></tr></table>", "type_str": "table", "text": "of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s)."}, "TABREF2": {"html": null, "num": null, "content": "<table><tr><td>Normalized Merging</td><td colspan=\"2\">Single-variate series</td></tr><tr><td/><td/><td/><td>…</td></tr><tr><td/><td>Input</td><td>Model</td><td>Output</td></tr><tr><td/><td>Context Length</td><td/></tr></table>", "type_str": "table", "text": ""}, "TABREF3": {"html": null, "num": null, "content": "<table><tr><td/><td>6'</td><td>7'</td><td>8'</td><td/><td>Predicted</td><td>3'</td><td>4'</td><td>5'</td><td>6'</td><td>…</td></tr><tr><td/><td colspan=\"3\">Projection</td><td/><td>Tokens</td><td/><td colspan=\"3\">Autoregression</td><td/></tr><tr><td/><td colspan=\"3\">Global lookback</td><td/><td/><td>2'</td><td>3'</td><td>4'</td><td>5'</td><td>6'</td></tr><tr><td/><td colspan=\"3\">Flattening</td><td/><td>Token</td><td/><td colspan=\"4\">Token Projection</td></tr><tr><td>1'</td><td>2'</td><td>3'</td><td>4'</td><td>5'</td><td>Embeddings</td><td>2'</td><td>3'</td><td>4'</td><td>5'</td><td>6'</td></tr><tr><td/><td colspan=\"3\">Trm Blocks</td><td/><td/><td/><td colspan=\"2\">Trm Blocks</td><td/><td/></tr><tr><td/><td colspan=\"3\">(No masking)</td><td/><td/><td colspan=\"4\">(Causal masking)</td><td/></tr><tr><td/><td/><td/><td/><td/><td>Tokens</td><td/><td/><td/><td/><td/></tr><tr><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td/></tr><tr><td/><td/><td/><td/><td/><td>Pipeline</td><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "text": ""}, "TABREF5": {"html": null, "num": null, "content": "<table><tr><td>,PSXWDWLRQ</td><td/><td/><td/><td>$QRPDO\\'HWHFWLRQ</td></tr><tr><td>:LQV&amp;RXQW</td><td>7LPHV1HW 7LPHU</td><td>1XPEHURI'HWHFWHG$QRPDOLHV</td><td>7LPHV1HW $QRPDO\\7UDQVIRUPHU</td><td>7LPHU</td></tr><tr><td>'DWD6FDUFLW\\</td><td/><td/><td/><td>4XDQWLOH</td></tr><tr><td>Figure 7.</td><td/><td/><td/><td/></tr><tr><td/><td/><td/><td colspan=\"2\">To assess pre-training benefit, we compare solid and dashed</td></tr><tr><td/><td/><td/><td colspan=\"2\">lines, differing by whether to load the pre-trained checkpoint.</td></tr><tr><td/><td/><td/><td colspan=\"2\">Concretely, the performance of training a random-initialized</td></tr><tr><td/><td/><td/><td colspan=\"2\">Timer on full samples can be achieved by fine-tuning our</td></tr><tr><td/><td/><td/><td colspan=\"2\">pre-trained Timer with only 2% of the training samples in</td></tr><tr><td/><td/><td/><td colspan=\"2\">ETTh1, 5% in ECL, 1% in Weather, and 4% in PEMS03,</td></tr><tr><td/><td/><td/><td colspan=\"2\">which exemplifies the transferable knowledge acquired by</td></tr><tr><td/><td/><td/><td colspan=\"2\">pre-training on UTSD. When all samples are available, the</td></tr><tr><td/><td/><td/><td colspan=\"2\">performance of the pre-trained Timer can also outperform</td></tr><tr><td/><td/><td/><td colspan=\"2\">training it from scratch: the prediction error is reduced as</td></tr><tr><td/><td/><td/><td colspan=\"2\">0.165 → 0.154 on Weather, 0.126 → 0.118 on PEMS03,</td></tr><tr><td/><td/><td/><td colspan=\"2\">and 0.125 → 0.107 on PEMS04. Overall, in widespread</td></tr></table>", "type_str": "table", "text": "Performance comparison with state-of-the-art small deep models. For imputation, Time is compared with TimesNet (<PERSON> et al., 2022) under different data scarcities, each of which contains 44 imputation scenarios. In UCR Anomaly Detection Archive (Wu & Keogh, 2021), we compare the number of detected anomalies under given confidence quantiles. Detailed results are provided inTable 11-15."}, "TABREF6": {"html": null, "num": null, "content": "<table><tr><td>SCENARIO</td><td/><td/><td colspan=\"2\">1% TARGET</td><td/><td/><td colspan=\"2\">5% TARGET</td><td/><td/><td colspan=\"2\">20% TARGET</td></tr><tr><td>ARCHITECTURE</td><td colspan=\"3\">ENCODER</td><td colspan=\"2\">DECODER</td><td colspan=\"2\">ENCODER</td><td colspan=\"2\">DECODER</td><td colspan=\"2\">ENCODER</td><td colspan=\"2\">DECODER</td></tr><tr><td>PRE-TRAINED</td><td colspan=\"2\">NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td></tr><tr><td>PEMS (AVG)</td><td colspan=\"13\">0.286 0.246 0.328 0.180 0.220 0.197 0.215 0.138 0.173 0.164 0.153 0.126</td></tr><tr><td>ECL</td><td colspan=\"13\">0.183 0.168 0.215 0.140 0.150 0.147 0.154 0.132 0.140 0.138 0.137 0.134</td></tr><tr><td>TRAFFIC</td><td colspan=\"13\">0.442 0.434 0.545 0.390 0.392 0.384 0.407 0.361 0.367 0.363 0.372 0.352</td></tr><tr><td>ETT (AVG)</td><td colspan=\"13\">0.367 0.317 0.340 0.295 0.339 0.303 0.321 0.285 0.309 0.301 0.297 0.288</td></tr><tr><td colspan=\"2\">Training Loss</td><td/><td/><td colspan=\"2\">Validation Loss</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>MSE</td><td/><td/><td>MSE</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"2\">Time Points</td><td/><td/><td colspan=\"2\">Time Points</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"2\">Decoder-only Trm (Timer)</td><td colspan=\"2\">Encoder-only Trm</td><td>LSTM</td><td>TiDE</td><td>TCN</td><td/><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "text": "Downstream forecasting results under different data scarcity of the encoder-only and decoder-only Transformer respectively pre-trained on UTST-12G. Datasets are ordered by the degradation in Figure1. Full results of PEMS and ETT can be found in Table17."}, "TABREF7": {"html": null, "num": null, "content": "<table><tr><td/><td colspan=\"4\">We elaborately evaluate two architectures on six benchmarks</td></tr><tr><td/><td colspan=\"4\">in Table 1. In the case of training from scratch (Pre-trained</td></tr><tr><td/><td colspan=\"4\">= None), the encoder-only Transformer will achieve better</td></tr><tr><td/><td colspan=\"4\">performance if the training samples are insufficient (Target</td></tr><tr><td/><td colspan=\"2\">9DULDEOH/RRNEDFN/HQJWK</td><td colspan=\"2\">9DULDEOH/RRNEDFN/HQJWK</td></tr><tr><td/><td>06(3(06</td><td>06(3(06</td><td/></tr><tr><td/><td>3(06</td><td>3(06</td><td>3(06</td><td>3(06</td></tr><tr><td/><td colspan=\"2\">9DULDEOH)RUHFDVW/HQJWK Figure 13.</td><td colspan=\"2\">9DULDEOH)RUHFDVW/HQJWK</td></tr><tr><td>Decoder-only v.s. Encoder-only While a smaller training loss is achieved by the encoder-only Transformer in Fig-</td><td>06((&amp;/</td><td>06(7UDIILF</td><td/></tr><tr><td>ure 12, the progress of large language models indicates that</td><td/><td/><td/></tr><tr><td/><td colspan=\"2\">(QFRGHURQO\\7UP</td><td colspan=\"2\">'HFRGHURQO\\7UP7LPHU</td></tr></table>", "type_str": "table", "text": "Performance of one Timer for all lookback lengths."}, "TABREF8": {"html": null, "num": null, "content": "<table><tr><td>Impact Statement</td><td/></tr><tr><td>Rasul, K., Ashok, A., <PERSON>, A. R., Khorasani, A.,</td><td/></tr><tr><td><PERSON>, <PERSON>, <PERSON>, R., Biloš, M., Ghonia, H.,</td><td/></tr><tr><td><PERSON>, N. V., <PERSON>, A<PERSON>, et al. Lag-llama: Towards</td><td/></tr><tr><td>foundation models for time series forecasting. arXiv preprint arXiv:2310.08278, 2023.</td><td><PERSON><PERSON>, G<PERSON>, <PERSON>, C<PERSON>, <PERSON>, A<PERSON>, Xi<PERSON>, C., <PERSON>, S., and Sahoo, D. Unified training of universal time series fore-</td></tr><tr><td>Tan, C. W., Bergmeir, C., Petitjean, F., and Webb, G. I.</td><td>casting transformers. arXiv preprint arXiv:2402.02592,</td></tr><tr><td>Time series extrinsic regression: Predicting numeric val-</td><td>2024.</td></tr><tr><td>ues from time series data. Data Mining and Knowledge</td><td/></tr><tr><td>Discovery, 35:1032-1060, 2021.</td><td>Wu, H., Xu, J., Wang, J., and Long, M. Autoformer: Decom-</td></tr><tr><td/><td>position transformers with auto-correlation for long-term</td></tr><tr><td>Touvron, H., Lavril, T., Izacard, G., Martinet, X., Lachaux,</td><td>series forecasting. Advances in Neural Information Pro-</td></tr><tr><td/><td>cessing Systems, 34:22419-22430, 2021.</td></tr><tr><td/><td>Wu, H., Hu, T., Liu, Y., Zhou, H., Wang, J., and Long, M.</td></tr><tr><td/><td>Timesnet: Temporal 2d-variation modeling for general</td></tr><tr><td/><td>time series analysis. arXiv preprint arXiv:2210.02186,</td></tr><tr><td/><td>2022.</td></tr><tr><td>lan-</td><td/></tr><tr><td>guage model architecture and pretraining objective works</td><td/></tr><tr><td>best for zero-shot generalization? In International Con-</td><td/></tr><tr><td>ference on Machine Learning, pp. 22964-22984. PMLR,</td><td/></tr><tr><td>2022a.</td><td/></tr><tr><td>Wang, Y., Han, Y., Wang, H., and Zhang, X. Contrast every-</td><td/></tr><tr><td>thing: A hierarchical contrastive framework for medical</td><td/></tr><tr><td>time-series. arXiv preprint arXiv:2310.14017, 2023a.</td><td>Zeng, A., Chen, M., Zhang, L., and Xu, Q. Are transformers</td></tr><tr><td>Wang, Z., Xu, X., Zhang, W., Trajcevski, G., Zhong, T., and</td><td>effective for time series forecasting? In Proceedings of</td></tr><tr><td>Zhou, F. Learning latent seasonal-trend representations</td><td>the AAAI conference on artificial intelligence, volume 37,</td></tr><tr><td>for time series forecasting. Advances in Neural Informa-</td><td>pp. 11121-11128, 2023.</td></tr><tr><td>tion Processing Systems, 35:38775-38787, 2022b.</td><td>Zerveas, G., Jayaraman, S., Patel, D., Bhamidipaty, A., and</td></tr><tr><td>Wang, Z., Wen, Q., Zhang, C., Sun, L., Von Krannich-</td><td>Eickhoff, C. A transformer-based framework for multi-</td></tr><tr><td>feldt, L., and Wang, Y. Benchmarks and custom</td><td>variate time series representation learning. In Proceed-</td></tr><tr><td>package for electrical load forecasting. arXiv preprint</td><td>ings of the 27th ACM SIGKDD conference on knowledge</td></tr><tr><td>arXiv:2307.07191, 2023b.</td><td>discovery &amp; data mining, pp. 2114-2124, 2021.</td></tr></table>", "type_str": "table", "text": "). In this paper, we release a time series dataset with 1 billion time points, propose a unified sequence format to address the heterogeneity of multivariate time series, and develop a generative pre-trained Transformer as a generalizable, scalable, task-general LTSM. Empirically, we evaluate our model in forecasting, imputation, and anomaly detection, yielding state-of-the-art performance and notable pre-training benefits in the data-scarce scenario. Further analysis validates the model scalability, explores the architecture for LTSMs, and highlights the versatility of our autoregressive generation.This paper aims to advance the development of large models for time series. In this work, we release a high-quality and unified time series dataset for scalable pre-training, which can serve as a foundation for pre-training and establishing new benchmarks. The outcome large model demonstrates notable effectiveness of generalization, versatility across various tasks, and scalability to refine performance, offering valuable insights for future investigations and application values for practitioners. Our paper mainly focuses on scientific research and has no obvious negative social impact. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Na<PERSON>, S., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>. Exploring the limits of transfer learning with a unified text-to-text transformer. The Journal of Machine Learning Research, 21(1):5485-5551, 2020. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>: Contrastive learning of disentangled seasonal-trend representations for time series forecasting. arXiv preprint arXiv:2202.01575, 2022. Woo, G., Liu, C., Kumar, A., and Sahoo, D. Pushing the limits of pre-training for time series forecasting in the cloudops domain. arXiv preprint arXiv:2310.05063, 2023. Proceedings of the AAAI Conference on Artificial Intelligence, volume 36, pp. 8980-8987, 2022."}, "TABREF9": {"html": null, "num": null, "content": "<table><tr><td>DOMAIN</td><td>DATASET</td><td colspan=\"2\">TIME POINTS FILE SIZE</td><td>FREQ.</td><td colspan=\"2\">ADF. FORECAST.</td><td>SOURCE</td></tr><tr><td/><td>LONDON SMART METERS *</td><td>166.50M</td><td>636M</td><td colspan=\"3\">HOURLY -13.158 0.173</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>WIND FARMS *</td><td>7.40M</td><td>29M</td><td>4 SEC</td><td colspan=\"2\">-29.174 0.811</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>AUS. ELECTRICITY DEMAND *</td><td>1.16M</td><td>5M</td><td colspan=\"3\">30 MIN -27.554 0.730</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>BDG-2 PANTHER</td><td>0.92M</td><td>4M</td><td colspan=\"2\">HOURLY -6.593</td><td>0.479</td><td>EMAMI ET AL. (2023)</td></tr><tr><td/><td>BDG-2 FOX</td><td>2.32M</td><td>9M</td><td colspan=\"2\">HOURLY -9.191</td><td>0.469</td><td>EMAMI ET AL. (2023)</td></tr><tr><td/><td>BDG-2 RAT</td><td>4.73M</td><td>19M</td><td colspan=\"2\">HOURLY -6.868</td><td>0.456</td><td>EMAMI ET AL. (2023)</td></tr><tr><td/><td>BDG-2 BEAR</td><td>1.48M</td><td>6M</td><td colspan=\"3\">HOURLY -11.742 0.471</td><td>EMAMI ET AL. (2023)</td></tr><tr><td/><td>LOW CARBON LONDON</td><td>9.54M</td><td>37M</td><td colspan=\"3\">HOURLY -12.366 0.134</td><td>EMAMI ET AL. (2023)</td></tr><tr><td>ENERGY</td><td>SMART</td><td>0.10M</td><td>1M</td><td colspan=\"3\">HOURLY -10.755 0.143</td><td>EMAMI ET AL. (2023)</td></tr><tr><td/><td>IDEAL</td><td>1.26M</td><td>5M</td><td colspan=\"3\">HOURLY -11.223 0.106</td><td>EMAMI ET AL. (2023)</td></tr><tr><td/><td>SCEAUX</td><td>0.03M</td><td>1M</td><td colspan=\"3\">HOURLY -14.172 0.143</td><td>EMAMI ET AL. (2023)</td></tr><tr><td/><td>BOREALIS</td><td>0.08M</td><td>1M</td><td colspan=\"2\">HOURLY -6.612</td><td>0.160</td><td>EMAMI ET AL. (2023)</td></tr><tr><td/><td>BUILDINGS900K</td><td colspan=\"4\">15852.22M 60102M HOURLY -8.412</td><td>0.357</td><td>EMAMI ET AL. (2023)</td></tr><tr><td/><td>COVID19 ENERGY</td><td>0.03M</td><td>1M</td><td colspan=\"3\">HOURLY -13.768 0.698</td><td>WANG ET AL. (2023B)</td></tr><tr><td/><td>GEF12</td><td>1.58M</td><td>6M</td><td colspan=\"2\">HOURLY -9.576</td><td>0.566</td><td>WANG ET AL. (2023B)</td></tr><tr><td/><td>GEF14</td><td>0.02M</td><td>1M</td><td colspan=\"2\">HOURLY -9.372</td><td>0.628</td><td>WANG ET AL. (2023B)</td></tr><tr><td/><td>GEF17</td><td>0.28M</td><td>1M</td><td colspan=\"2\">HOURLY -5.976</td><td>0.599</td><td>WANG ET AL. (2023B)</td></tr><tr><td/><td>PDB</td><td>0.04M</td><td>1M</td><td colspan=\"2\">HOURLY -6.453</td><td>0.622</td><td>WANG ET AL. (2023B)</td></tr><tr><td/><td>SPANISH</td><td>0.07M</td><td>1M</td><td colspan=\"3\">HOURLY -13.217 0.770</td><td>WANG ET AL. (2023B)</td></tr><tr><td/><td>ELF</td><td>0.02M</td><td>1M</td><td colspan=\"3\">HOURLY -13.607 0.770</td><td>WANG ET AL. (2023B)</td></tr><tr><td/><td>KDD CUP 2022</td><td>4.73M</td><td>181M</td><td colspan=\"3\">HOURLY -17.017 0.225</td><td>ZHOU ET AL. (2022)</td></tr><tr><td/><td>RESIDENTIAL LOAD POWER</td><td>437.98M</td><td colspan=\"4\">1671M MINUTELY -37.979 0.264</td><td>BERGMEIR ET AL. (2023)</td></tr><tr><td/><td>RESIDENTIAL PV POWER</td><td>373.37M</td><td colspan=\"4\">1435M MINUTELY -31.389 0.421</td><td>BERGMEIR ET AL. (2023)</td></tr><tr><td/><td>AUSTRALIARAINFALL *</td><td>11.54M</td><td>45M</td><td colspan=\"3\">HOURLY -150.10 0.458</td><td>TAN ET AL. (2021)</td></tr><tr><td/><td>BEIJINGPM25QUALITY *</td><td>3.66M</td><td>14M</td><td colspan=\"3\">HOURLY -31.415 0.404</td><td>TAN ET AL. (2021)</td></tr><tr><td>ENVIRONMENT</td><td/><td/><td/><td/><td/></tr><tr><td/><td>BENZENECONCENTRATION *</td><td>16.34M</td><td>63M</td><td colspan=\"3\">HOURLY -65.187 0.526</td><td>TAN ET AL. (2021)</td></tr><tr><td/><td>CHINA AIR QUALITY *</td><td>34.29M</td><td>132M</td><td colspan=\"3\">HOURLY -12.602 0.529</td><td>ZHENG ET AL. (2015)</td></tr><tr><td/><td>BEIJING AIR QUALITY *</td><td>4.62M</td><td>18M</td><td colspan=\"3\">HOURLY -15.758 0.332</td><td>CHEN (2019)</td></tr></table>", "type_str": "table", "text": "Dataset detailed descriptions. Time Points denotes the total number of time points aggregating from all variates if multivariate. File Size denotes the storage that the ARROW format of the dataset occupies. Freq. denotes the sampling interval of time points, where \"-\" indicates no timestamp or irregular interval. ADF. denotes the Augmented Dickey-Fuller test statistics of the dataset. Forecast. denotes the forecastability of the dataset. Source denotes the original paper or resource of the dataset."}, "TABREF10": {"html": null, "num": null, "content": "<table><tr><td/><td/><td colspan=\"4\">CONTINUED FROM PREVIOUS PAGE</td><td/><td/></tr><tr><td>DOMAIN</td><td>DATASET</td><td colspan=\"2\">TIME POINTS FILE SIZE</td><td>FREQ.</td><td colspan=\"2\">ADF. FORECAST.</td><td>SOURCE</td></tr><tr><td/><td>LOOP SEATTLE</td><td>33.95M</td><td>130M</td><td>5 MIN</td><td colspan=\"2\">-32.209 0.535</td><td>JIANG ET AL. (2023)</td></tr><tr><td/><td>SZ-TAXI</td><td>0.46M</td><td>2M</td><td>15 MIN</td><td>-5.900</td><td>0.217</td><td>JIANG ET AL. (2023)</td></tr><tr><td/><td>BEIJING SUBWAY</td><td>0.87M</td><td>22M</td><td>30 MIN</td><td>-8.571</td><td>0.219</td><td>JIANG ET AL. (2023)</td></tr><tr><td>TRANSPORT</td><td>SHMETROY</td><td>5.07M</td><td>20M</td><td>15 MIN</td><td/><td/><td>JIANG ET AL. (2023)</td></tr><tr><td/><td>HZMETRO</td><td>0.38M</td><td>2M</td><td colspan=\"3\">15 MIN -11.254 0.232</td><td>JIANG ET AL. (2023)</td></tr><tr><td/><td>Q-TRAFFIC</td><td>264.39M</td><td>1011M</td><td colspan=\"3\">15 MIN -15.761 0.490</td><td>JIANG ET AL. (2023)</td></tr><tr><td/><td>TAXI</td><td>55.00M</td><td>212M</td><td>30 MIN</td><td>-8.302</td><td>0.146</td><td>ALEXANDROV ET AL. (2020)</td></tr><tr><td/><td>UBER TLC DAILY</td><td>0.05M</td><td>1M</td><td>DAILY</td><td>-1.778</td><td>0.285</td><td>ALEXANDROV ET AL. (2020)</td></tr><tr><td/><td>UBER TLC HOURLY</td><td>1.13M</td><td>5M</td><td colspan=\"2\">HOURLY -9.022</td><td>0.124</td><td>ALEXANDROV ET AL. (2020)</td></tr><tr><td/><td>LARGEST</td><td colspan=\"2\">4452.20M 16988M</td><td>5 MIN</td><td colspan=\"2\">-38.020 0.436</td><td>LIU ET AL. (2023A)</td></tr><tr><td>WEB</td><td>WEB TRAFFIC *</td><td>116.49M</td><td>462M</td><td>DAILY</td><td>-8.272</td><td>0.299</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>WIKI-ROLLING</td><td>40.62M</td><td>157M</td><td>DAILY</td><td>-5.524</td><td>0.242</td><td>ALEXANDROV ET AL. (2020)</td></tr><tr><td/><td colspan=\"2\">ALIBABA CLUSTER TRACE 2018 190.39M</td><td>2909M</td><td>5 MIN</td><td>-5.303</td><td>0.668</td><td>WOO ET AL. (2023)</td></tr><tr><td>CLOUDOPS</td><td>AZURE VM TRACES 2017</td><td colspan=\"2\">885.52M 10140M</td><td>5 MIN</td><td colspan=\"2\">-11.482 0.290</td><td>WOO ET AL. (2023)</td></tr><tr><td/><td>BORG CLUSTER DATA 2011</td><td colspan=\"2\">1073.89M 14362M</td><td>5 MIN</td><td>-8.975</td><td>0.505</td><td>WOO ET AL. (2023)</td></tr><tr><td/><td>M5</td><td>58.33M</td><td>224M</td><td>DAILY</td><td>-6.985</td><td>0.247</td><td>ALEXANDROV ET AL. (2020)</td></tr><tr><td/><td>FAVORITA SALES</td><td>139.18M</td><td>535M</td><td>DAILY</td><td>-6.441</td><td>0.097</td><td>KAGGLE</td></tr><tr><td>SALES</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td/><td>FAVORITA TRANSACTIONS</td><td>0.08M</td><td>1M</td><td>DAILY</td><td>-5.481</td><td>0.362</td><td>KAGGLE</td></tr><tr><td/><td>RESTAURANT</td><td>0.29M</td><td>2M</td><td>DAILY</td><td>-4.650</td><td>0.126</td><td>KAGGLE</td></tr><tr><td/><td>HIERARCHICAL SALES</td><td>0.21M</td><td>1M</td><td>DAILY</td><td>-8.704</td><td>0.078</td><td>MANCUSO ET AL. (2021)</td></tr><tr><td>FINANCE</td><td>GODADDY</td><td>0.26M</td><td>2M</td><td colspan=\"2\">MONTHLY -1.539</td><td>0.784</td><td>KAGGLE</td></tr><tr><td/><td>BITCOIN *</td><td>0.07M</td><td>1M</td><td>DAILY</td><td>-2.493</td><td>0.398</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>M1 YEARLY</td><td>0.00M</td><td>1M</td><td colspan=\"2\">YEARLY -0.791</td><td>0.473</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>M1 QUARTERLY</td><td>0.01M</td><td>1M</td><td colspan=\"2\">QUARTERLY -0.175</td><td>0.572</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>M1 MONTHLY</td><td>0.04M</td><td>1M</td><td colspan=\"2\">MONTHLY -1.299</td><td>0.588</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td>MISC.</td><td>M3 YEARLY</td><td>0.02M</td><td>1M</td><td colspan=\"2\">YEARLY -0.850</td><td>0.524</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>M3 QUARTERLY</td><td>0.04M</td><td>1M</td><td colspan=\"2\">QUARTERLY -0.897</td><td>0.624</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>M3 MONTHLY</td><td>0.1M</td><td>1M</td><td colspan=\"2\">MONTHLY -1.954</td><td>0.635</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>M3 OTHER</td><td>0.01M</td><td>1M</td><td>-</td><td>-0.568</td><td>0.801</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>M4 YEARLY</td><td>0.84M</td><td>4M</td><td colspan=\"2\">YEARLY -0.036</td><td>0.533</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>M4 QUARTERLY</td><td>2.214M</td><td colspan=\"3\">10M QUARTERLY -0.745</td><td>0.696</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>M4 MONTHLY</td><td>10.38M</td><td>41M</td><td colspan=\"2\">MONTHLY -1.358</td><td>0.665</td><td/></tr></table>", "type_str": "table", "text": ""}, "TABREF11": {"html": null, "num": null, "content": "<table><tr><td/><td/><td colspan=\"4\">CONTINUED FROM PREVIOUS PAGE</td><td/><td/></tr><tr><td>DOMAIN</td><td>DATASET</td><td colspan=\"2\">TIME POINTS FILE SIZE</td><td>FREQ.</td><td colspan=\"2\">ADF. FORECAST.</td><td>SOURCE</td></tr><tr><td/><td>M4 WEEKLY</td><td>0.37M</td><td>2M</td><td colspan=\"2\">WEEKLY -0.533</td><td>0.644</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td>MISC.</td><td>M4 DAILY</td><td>9.96M</td><td>39M</td><td>DAILY</td><td>-1.332</td><td>0.841</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td>M4 HOURLY</td><td>0.35M</td><td>2M</td><td colspan=\"2\">HOURLY -2.073</td><td>0.532</td><td>GODAHEWA ET AL. (2021)</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td>Easy</td></tr><tr><td>UTSD-1G</td><td>UTSD-2G</td><td/><td colspan=\"2\">UTSD-4G</td><td colspan=\"2\">UTSD-12G</td><td>Medium</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td>Hard</td></tr><tr><td/><td colspan=\"5\">Figure 16. Dataset complexity in each hierarchy of UTSD.</td><td/><td/></tr></table>", "type_str": "table", "text": "THE AS<PERSON><PERSON><PERSON><PERSON> MARKS THE DATASET THAT ORIGINALLY BELONGS TO UTSD."}, "TABREF13": {"html": null, "num": null, "content": "<table><tr><td>DOMAIN</td><td>DATASET</td><td colspan=\"5\">TIME POINTS VARIATES FREQUENCY ADF STATISTIC FORECASTABILITY</td></tr><tr><td colspan=\"2\">ENVIRONMENT AUSTRALIARAINFALL</td><td>11.54M</td><td>3</td><td>HOURLY</td><td>-150.10</td><td>0.458</td></tr><tr><td>TRANSPORT</td><td>PEDESTRIANCOUNTS</td><td>0.08M</td><td>1</td><td>HOURLY</td><td>-23.462</td><td>0.297</td></tr><tr><td>IOT</td><td>SENSORDATA</td><td>3.24M</td><td>18</td><td>0.002 SEC</td><td>-15.892</td><td>0.917</td></tr><tr><td>HEALTH</td><td>BIDMC32HR</td><td>0.04M</td><td>1000</td><td>-</td><td>-14.135</td><td>0.523</td></tr></table>", "type_str": "table", "text": "Benchmark detailed descriptions. Time Point denotes the total number of time points aggregating from all variates if multivariate. Frequency denotes the sampling interval of time points, where \"-\" indicates no timestamp or irregular interval. ADF Statistic denotes the Augmented Dickey-Fuller test statistics of the dataset. Forecastability denotes the forecastability of the dataset."}, "TABREF14": {"html": null, "num": null, "content": "<table><tr><td>MODELS</td><td colspan=\"2\">TIMER</td><td colspan=\"2\">PATCHTST</td><td colspan=\"2\">ITRANSFORMER</td><td colspan=\"2\">DLINEAR</td></tr><tr><td>METRIC</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td></tr><tr><td>AUSTRALIARAINFALL</td><td>0.800</td><td>0.720</td><td>0.802</td><td>0.720</td><td>0.800</td><td>0.800</td><td>0.804</td><td>0.804</td></tr><tr><td>PEDESTRIANCOUNTS</td><td>0.054</td><td>0.133</td><td>0.058</td><td>0.153</td><td>0.056</td><td>0.143</td><td>0.060</td><td>0.149</td></tr><tr><td>SENSORDATA</td><td>0.049</td><td>0.094</td><td>0.056</td><td>0.094</td><td>0.052</td><td>0.091</td><td>0.057</td><td>0.111</td></tr><tr><td>BIDMC32HR</td><td>0.030</td><td>0.062</td><td>0.188</td><td>0.284</td><td>0.159</td><td>0.249</td><td>0.320</td><td>0.409</td></tr></table>", "type_str": "table", "text": "Forecasting results on well-acknowledged deep forecasters and Timer, where Timer is pre-trained on the held-out datasets and then all models are superwisedly trained on the four datasets in the 672-pred-96 setting."}, "TABREF15": {"html": null, "num": null, "content": "<table><tr><td>TARGET DATASET</td><td/><td/><td>WEATHER</td><td/><td/><td>ECL</td></tr><tr><td colspan=\"3\">SOURCE DOMAIN FROM SCRATCH</td><td>ENERGY</td><td>NATURE</td><td colspan=\"2\">FROM SCRATCH</td><td>NATURE</td><td>ENERGY</td></tr><tr><td>METRIC</td><td>MSE</td><td>MAE</td><td colspan=\"3\">MSE MAE MSE MAE MSE</td><td>MAE</td><td>MSE MAE MSE MAE</td></tr><tr><td>5% TARGET</td><td>0.229</td><td>0.279</td><td colspan=\"3\">0.171 0.220 0.162 0.212 0.179</td><td>0.277</td><td>0.165 0.269 0.141 0.238</td></tr><tr><td>20% TARGET</td><td>0.185</td><td>0.238</td><td colspan=\"3\">0.160 0.212 0.153 0.202 0.145</td><td>0.243</td><td>0.140 0.238 0.133 0.228</td></tr><tr><td>100% TARGET</td><td>0.158</td><td>0.209</td><td colspan=\"3\">0.152 0.199 0.151 0.198 0.130</td><td>0.224</td><td>0.132 0.224 0.131 0.223</td></tr><tr><td colspan=\"3\">B. Implementation Details</td><td/><td/><td/></tr></table>", "type_str": "table", "text": "In-domain and out-of-domain forecasting results by pre-training on the source domain and fine-tuning on the target dataset under different data scarcity. ECL and Weather belong to the Energy and Nature domains respectively."}, "TABREF16": {"html": null, "num": null, "content": "<table><tr><td>SCENARIO</td><td/><td colspan=\"2\">MODEL DIM. SCALE-UP</td><td/><td colspan=\"4\">LAYER NUMBER. SCALE-UP</td><td colspan=\"2\">OTHERS</td></tr><tr><td>SCALE</td><td>3M</td><td>13M</td><td>29M</td><td>51M</td><td>1M</td><td>2M</td><td>3M</td><td>4M</td><td>38M</td><td>67M</td></tr><tr><td>LAYERS</td><td>6</td><td>6</td><td>6</td><td>6</td><td>2</td><td>4</td><td>6</td><td>8</td><td>8</td><td>8</td></tr><tr><td>MODEL DIM.</td><td>256</td><td>512</td><td>768</td><td>1024</td><td>256</td><td>256</td><td>256</td><td>256</td><td>768</td><td>1024</td></tr><tr><td>FFN DIM.</td><td>512</td><td>1024</td><td>1536</td><td>2048</td><td>512</td><td>512</td><td>512</td><td>512</td><td>1536</td><td>2048</td></tr><tr><td>PARAMETERS</td><td>3.21M</td><td>12.72M</td><td>28.51M</td><td>50.59M</td><td>1.10M</td><td>2.16M</td><td>3.21M</td><td>4.27M</td><td>37.97M</td><td>67.40M</td></tr></table>", "type_str": "table", "text": "Detailed model configurations of Timer and corresponding parameter counts. The number of heads for models is fixed as 8."}, "TABREF17": {"html": null, "num": null, "content": "<table/>", "type_str": "table", "text": "contains 7 variates of power transformers, with the period from July 2016 to July 2018, including four subsets and sampling intervals of one hour and fifteen minutes. (2) ECL (<PERSON> et al., 2021) mainly consists of hourly electricity consumption data from 321 customers (3) Traffic (<PERSON> et al., 2021) collected hourly road occupancy rates measured by 862 sensors on the San Francisco Bay Area highway from January 2015 to December 2016. (4) Weather (<PERSON> et al., 2021) consists of 21 meteorological variates collected every 10 minutes from the Max Planck Institute of Biogeochemistry meteorological station in 2020."}, "TABREF18": {"html": null, "num": null, "content": "<table><tr><td>DATASET</td><td>VARIATE</td><td>SPLIT</td><td>FREQUENCY</td><td>INFORMATION</td></tr><tr><td>ETTH1, ETTH2</td><td>7</td><td>(8545, 2881, 2881)</td><td>HOURLY</td><td>ELECTRICITY</td></tr><tr><td>ETTM1, ETTM2</td><td>7</td><td>(34465, 11521, 11521)</td><td>15MIN</td><td>ELECTRICITY</td></tr><tr><td>ECL</td><td>321</td><td>(18317, 2633, 5261)</td><td>HOURLY</td><td>ELECTRICITY</td></tr><tr><td>TRAFFIC</td><td>862</td><td>(12185, 1757, 3509)</td><td>HOURLY</td><td>TRANSPORTATION</td></tr><tr><td>WEATHER</td><td>21</td><td>(36792, 5271, 10540)</td><td>10MIN</td><td>WEATHER</td></tr><tr><td>PEMS03</td><td>358</td><td>(15617, 5135, 5135)</td><td>5MIN</td><td>TRANSPORTATION</td></tr><tr><td>PEMS04</td><td>307</td><td>(10172,3375, 3375)</td><td>5MIN</td><td>TRANSPORTATION</td></tr><tr><td>PEMS07</td><td>883</td><td>(16911, 5622, 5622)</td><td>5MIN</td><td>TRANSPORTATION</td></tr><tr><td>PEMS08</td><td>170</td><td>(10690, 3548, 3548)</td><td>5MIN</td><td>TRANSPORTATION</td></tr></table>", "type_str": "table", "text": "Downstream forecasting dataset descriptions. Split denotes the number of time points in (train, validation, test) splits. Frequency denotes the sampling interval of time points. Information denotes the domain in which the dataset belongs to."}, "TABREF20": {"html": null, "num": null, "content": "<table><tr><td>METHOD</td><td>TIMER (OURS)</td><td>MOIRAI (2024)</td><td>MOMENT (2024)</td><td colspan=\"4\">CHRONOS LAG-LLAMA TIMESFM TIMEGPT-1 (2024) (2023) (2023B) (2023)</td></tr><tr><td>ARCHITECTURE</td><td colspan=\"2\">DECODER ENCODER</td><td>ENCODER DECODER</td><td>ENCODER DECODER</td><td>DECODER</td><td colspan=\"2\">DECODER ENCODER DECODER</td></tr><tr><td>MODEL SIZE</td><td colspan=\"2\">29M, 50M, 14M, 91M, 67M 311M</td><td>40M, 125M 385M</td><td>20M, 46M, 200M, 710M</td><td>200M</td><td colspan=\"2\">17M, 70M, UNKNOWN 200M</td></tr><tr><td/><td colspan=\"4\">FORECAST FORECAST FORECAST IMPUTATION FORECAST</td><td colspan=\"3\">FORECAST FORECAST FORECAST</td></tr><tr><td>SUPPORTED TASKS</td><td>IMPUTATION</td><td/><td>CLASSIFICATION</td><td/><td/><td/><td>DETECTION</td></tr><tr><td/><td>DETECTION</td><td/><td>DETECTION</td><td/><td/><td/><td/></tr><tr><td>PRE-TRAINING SCALE</td><td>28B</td><td>27.65B</td><td>1.13B</td><td>84B</td><td>0.36B</td><td>100B</td><td>100B</td></tr><tr><td>TOKEN TYPE</td><td>SEGMENT</td><td>SEGMENT</td><td>SEGMENT</td><td>POINT</td><td>POINT</td><td colspan=\"2\">SEGMENT SEGMENT</td></tr><tr><td>CONTEXT LENGTH</td><td>≤1440</td><td>≤5000</td><td>= 512</td><td>≤512</td><td>≤1024</td><td>≤512</td><td>UNKNOWN</td></tr><tr><td>VARIABLE LENGTH</td><td>TRUE</td><td>TRUE</td><td>FALSE</td><td>TRUE</td><td>TRUE</td><td>TRUE</td><td>TRUE</td></tr><tr><td>PROBABILISTIC</td><td>FALSE</td><td>TRUE</td><td>FALSE</td><td>TRUE</td><td>TRUE</td><td>TRUE</td><td>TRUE</td></tr></table>", "type_str": "table", "text": "Quality evaluation of large time series models. Architecture denotes the Transformer category. Model size presents the parameter counts. Token type presents the graininess of time series tokens. Context length means the maximum/fixed input length of the model."}, "TABREF21": {"html": null, "num": null, "content": "<table><tr><td>DATASET</td><td colspan=\"2\">ETTH1</td><td>ECL</td><td/><td colspan=\"2\">TRAFFIC</td><td colspan=\"2\">WEATHER</td><td colspan=\"2\">PEMS03</td><td colspan=\"2\">PEMS04</td></tr><tr><td colspan=\"2\">PRE-TRAINED NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td></tr><tr><td>100%</td><td colspan=\"9\">0.363 0.358 0.132 0.136 0.352 0.351 0.165 0.154 0.126</td><td>0.118</td><td colspan=\"2\">0.125 0.107</td></tr><tr><td>75%</td><td colspan=\"9\">0.364 0.358 0.132 0.137 0.353 0.351 0.162 0.157 0.124</td><td>0.114</td><td colspan=\"2\">0.126 0.110</td></tr><tr><td>50%</td><td colspan=\"9\">0.370 0.356 0.132 0.135 0.356 0.352 0.161 0.151 0.129</td><td>0.114</td><td colspan=\"2\">0.131 0.110</td></tr><tr><td>25%</td><td colspan=\"9\">0.387 0.359 0.135 0.134 0.368 0.352 0.162 0.153 0.133</td><td>0.114</td><td colspan=\"2\">0.141 0.117</td></tr><tr><td>20%</td><td colspan=\"9\">0.385 0.359 0.137 0.134 0.372 0.352 0.166 0.151 0.135</td><td>0.116</td><td colspan=\"2\">0.145 0.120</td></tr><tr><td>15%</td><td colspan=\"9\">0.391 0.360 0.141 0.134 0.379 0.353 0.174 0.152 0.138</td><td>0.118</td><td colspan=\"2\">0.152 0.123</td></tr><tr><td>10%</td><td colspan=\"9\">0.426 0.361 0.144 0.133 0.387 0.353 0.182 0.152 0.140</td><td>0.120</td><td colspan=\"2\">0.165 0.126</td></tr><tr><td>5%</td><td colspan=\"9\">0.426 0.362 0.154 0.132 0.407 0.361 0.198 0.151 0.158</td><td colspan=\"3\">0.125 0.195 0.135</td></tr><tr><td>4%</td><td colspan=\"9\">0.424 0.362 0.161 0.135 0.416 0.366 0.208 0.152 0.166</td><td>0.127</td><td colspan=\"2\">0.210 0.138</td></tr><tr><td>3%</td><td colspan=\"9\">0.427 0.363 0.169 0.134 0.431 0.369 0.218 0.153 0.180</td><td>0.131</td><td colspan=\"2\">0.234 0.143</td></tr><tr><td>2%</td><td colspan=\"9\">0.427 0.363 0.186 0.137 0.467 0.380 0.230 0.159 0.201</td><td>0.137</td><td colspan=\"2\">0.257 0.152</td></tr><tr><td>1%</td><td colspan=\"9\">0.428 0.366 0.215 0.140 0.545 0.390 0.246 0.166 0.249</td><td>0.151</td><td colspan=\"2\">0.320 0.172</td></tr><tr><td>SOTA</td><td colspan=\"2\">0.370</td><td colspan=\"2\">0.129</td><td colspan=\"2\">0.360</td><td colspan=\"2\">0.149</td><td colspan=\"2\">0.132</td><td colspan=\"2\">0.115</td></tr></table>", "type_str": "table", "text": "Full forecasting results of Timer obtained by training from scratch (None) and fine-tuning from UTSD-12G pre-trained model. The bold values we use indicate that the pre-trained model results have positive benefits compared to from scratch. We attach the current state-of-the-art results as SOTA in this table, including PatchTST(<PERSON><PERSON> et al., 2022) on ETTh1 and Weather, as well as iTransformer(<PERSON>  et al., 2023b)  on ECL, Traffic, PEMS03, and PEMS04. We adopt the unified lookback length as 672 and the forecast length as 96."}, "TABREF22": {"html": null, "num": null, "content": "<table><tr><td>MASK RATIO</td><td/><td>12.5%</td><td/><td/><td>25.0%</td><td/><td/><td>37.5%</td><td/><td/><td>50.0%</td></tr><tr><td colspan=\"2\">PRE-TRAINED NONE</td><td>12G</td><td>∆%</td><td>NONE</td><td>12G</td><td>∆%</td><td>NONE</td><td>12G</td><td>∆%</td><td>NONE</td><td>12G</td><td>∆%</td></tr><tr><td>ETTH1</td><td colspan=\"2\">0.301 0.292</td><td>+3.08</td><td colspan=\"2\">0.313 0.299</td><td>+4.46</td><td colspan=\"2\">0.322 0.307</td><td>+4.59</td><td colspan=\"2\">0.325 0.325</td><td>0.00</td></tr><tr><td>ETTH2</td><td colspan=\"2\">0.172 0.168</td><td>+2.64</td><td colspan=\"2\">0.182 0.180</td><td>+1.26</td><td colspan=\"2\">0.197 0.190</td><td>+3.22</td><td colspan=\"2\">0.216 0.215</td><td>+0.47</td></tr><tr><td>ETTM1</td><td colspan=\"12\">0.397 0.347 +12.52 0.403 0.332 +17.72 0.428 0.374 +12.77 0.473 0.425 +10.13</td></tr><tr><td>ETTM2</td><td colspan=\"2\">0.118 0.116</td><td>+1.59</td><td colspan=\"2\">0.127 0.121</td><td>+4.69</td><td colspan=\"2\">0.134 0.131</td><td>+2.22</td><td colspan=\"2\">0.147 0.144</td><td>+1.99</td></tr><tr><td>ECL</td><td colspan=\"2\">0.152 0.140</td><td>+7.67</td><td colspan=\"2\">0.162 0.150</td><td>+7.27</td><td colspan=\"2\">0.172 0.161</td><td>+6.76</td><td colspan=\"2\">0.185 0.174</td><td>+6.17</td></tr><tr><td>TRAFFIC</td><td colspan=\"12\">0.538 0.460 +14.60 0.567 0.487 +14.14 0.598 0.520 +13.16 0.633 0.558 +11.91</td></tr><tr><td>WEATHER</td><td colspan=\"2\">0.113 0.117</td><td>-3.18</td><td colspan=\"2\">0.116 0.114</td><td>+2.31</td><td colspan=\"2\">0.128 0.124</td><td>+3.28</td><td colspan=\"3\">0.155 0.136 +12.42</td></tr><tr><td>PEMS03</td><td colspan=\"12\">0.160 0.135 +15.78 0.196 0.168 +14.60 0.257 0.223 +13.51 0.354 0.306 +13.49</td></tr><tr><td>PEMS04</td><td colspan=\"12\">0.193 0.161 +16.80 0.238 0.202 +15.30 0.305 0.258 +15.28 0.410 0.348 +15.14</td></tr><tr><td>PEMS07</td><td colspan=\"12\">0.166 0.139 +16.19 0.210 0.183 +12.89 0.278 0.243 +12.72 0.378 0.326 +13.76</td></tr><tr><td>PEMS08</td><td colspan=\"12\">0.185 0.157 +15.33 0.232 0.195 +15.98 0.303 0.265 +12.75 0.417 0.362 +13.26</td></tr></table>", "type_str": "table", "text": "Downstream imputation with 5% samples. Pre-training benefit ∆% is calculated as the ratio of decreased imputing error in MSE. In the case of 5% samples, our pre-trained model outperforms TimesNet (Table14) in all 44 settings on datasets and masked ratios."}, "TABREF23": {"html": null, "num": null, "content": "<table><tr><td>MASK RATIO</td><td/><td>12.5%</td><td/><td/><td>25.0%</td><td/><td/><td>37.5%</td><td/><td/><td>50.0%</td></tr><tr><td colspan=\"2\">PRE-TRAINED NONE</td><td>12G</td><td>∆%</td><td>NONE</td><td>12G</td><td>∆%</td><td>NONE</td><td>12G</td><td>∆%</td><td>NONE</td><td>12G</td><td>∆%</td></tr><tr><td>ETTH1</td><td colspan=\"2\">0.289 0.278</td><td>3.83</td><td colspan=\"2\">0.293 0.287</td><td>1.91</td><td colspan=\"2\">0.305 0.297</td><td>2.56</td><td colspan=\"2\">0.322 0.314</td><td>2.44</td></tr><tr><td>ETTH2</td><td colspan=\"2\">0.168 0.166</td><td>1.21</td><td colspan=\"2\">0.180 0.178</td><td>1.02</td><td colspan=\"2\">0.192 0.190</td><td>0.73</td><td colspan=\"2\">0.208 0.208</td><td>0.17</td></tr><tr><td>ETTM1</td><td colspan=\"2\">0.349 0.328</td><td>5.98</td><td colspan=\"2\">0.335 0.326</td><td>2.72</td><td colspan=\"2\">0.378 0.360</td><td>4.84</td><td colspan=\"2\">0.426 0.407</td><td>4.34</td></tr><tr><td>ETTM2</td><td colspan=\"2\">0.139 0.133</td><td>4.77</td><td colspan=\"2\">0.158 0.123</td><td>22.30</td><td colspan=\"5\">0.176 0.136 22.95 0.146 0.143</td><td>2.22</td></tr><tr><td>ECL</td><td colspan=\"2\">0.136 0.130</td><td>5.01</td><td colspan=\"2\">0.146 0.138</td><td>5.30</td><td colspan=\"2\">0.157 0.149</td><td>5.04</td><td colspan=\"2\">0.170 0.162</td><td>4.54</td></tr><tr><td>TRAFFIC</td><td colspan=\"2\">0.451 0.420</td><td>6.89</td><td colspan=\"2\">0.481 0.446</td><td>7.26</td><td colspan=\"2\">0.513 0.477</td><td>7.09</td><td colspan=\"2\">0.550 0.511</td><td>7.10</td></tr><tr><td>WEATHER</td><td colspan=\"12\">0.125 0.129 -3.40 0.125 0.147 -17.46 0.154 0.125 18.77 0.141 0.153 -7.93</td></tr><tr><td>PEMS03</td><td colspan=\"5\">0.134 0.120 10.41 0.169 0.150</td><td>11.35</td><td colspan=\"6\">0.221 0.198 10.61 0.305 0.273 10.32</td></tr><tr><td>PEMS04</td><td colspan=\"2\">0.162 0.146</td><td>9.93</td><td colspan=\"2\">0.203 0.184</td><td>9.61</td><td colspan=\"2\">0.262 0.236</td><td>9.86</td><td colspan=\"2\">0.354 0.320</td><td>9.65</td></tr><tr><td>PEMS07</td><td colspan=\"5\">0.140 0.125 10.96 0.182 0.162</td><td>10.85</td><td colspan=\"6\">0.240 0.214 10.77 0.327 0.290 11.57</td></tr><tr><td>PEMS08</td><td colspan=\"5\">0.155 0.139 10.39 0.198 0.174</td><td>12.11</td><td colspan=\"6\">0.268 0.236 11.94 0.366 0.324 11.63</td></tr></table>", "type_str": "table", "text": "Downstream imputation with 20% samples. Pre-training benefit ∆% is calculated as the ratio of decreased imputing error in MSE. In the case of 20% samples, our pre-trained model outperforms TimesNet in 86.4% of 44 settings on datasets and masked ratios."}, "TABREF24": {"html": null, "num": null, "content": "<table><tr><td>MASK RATIO</td><td/><td>12.5%</td><td/><td/><td>25.0%</td><td/><td/><td>37.5%</td><td/><td/><td>50.0%</td></tr><tr><td colspan=\"2\">PRE-TRAINED NONE</td><td>12G</td><td>∆%</td><td>NONE</td><td>12G</td><td>∆%</td><td>NONE</td><td>12G</td><td>∆%</td><td>NONE</td><td>12G</td><td>∆%</td></tr><tr><td>ETTH1</td><td colspan=\"2\">0.274 0.273</td><td>0.34</td><td colspan=\"2\">0.283 0.283</td><td>-0.04</td><td colspan=\"2\">0.295 0.294</td><td>0.52</td><td colspan=\"2\">0.313 0.312</td><td>0.17</td></tr><tr><td>ETTH2</td><td colspan=\"5\">0.207 0.177 14.44 0.186 0.186</td><td>-0.49</td><td colspan=\"5\">0.192 0.195 -1.18 0.210 0.209</td><td>0.57</td></tr><tr><td>ETTM1</td><td colspan=\"5\">0.342 0.352 -3.04 0.359 0.345</td><td>3.87</td><td colspan=\"2\">0.400 0.371</td><td>7.09</td><td colspan=\"2\">0.418 0.413</td><td>1.15</td></tr><tr><td>ETTM2</td><td colspan=\"12\">0.149 0.161 -8.01 0.153 0.171 -11.46 0.173 0.176 -1.53 0.183 0.158 13.27</td></tr><tr><td>ECL</td><td colspan=\"2\">0.125 0.122</td><td>2.98</td><td colspan=\"2\">0.134 0.130</td><td>3.06</td><td colspan=\"2\">0.144 0.139</td><td>3.12</td><td colspan=\"2\">0.157 0.152</td><td>2.87</td></tr><tr><td>TRAFFIC</td><td colspan=\"2\">0.402 0.392</td><td>2.50</td><td colspan=\"2\">0.424 0.414</td><td>2.48</td><td colspan=\"2\">0.454 0.443</td><td>2.46</td><td colspan=\"2\">0.488 0.477</td><td>2.29</td></tr><tr><td>WEATHER</td><td colspan=\"5\">0.144 0.157 -8.67 0.159 0.146</td><td>8.01</td><td colspan=\"2\">0.162 0.147</td><td>9.41</td><td colspan=\"2\">0.168 0.158</td><td>6.15</td></tr><tr><td>PEMS03</td><td colspan=\"2\">0.113 0.108</td><td>4.65</td><td colspan=\"2\">0.143 0.135</td><td>5.30</td><td colspan=\"2\">0.188 0.179</td><td>4.88</td><td colspan=\"2\">0.258 0.248</td><td>3.90</td></tr><tr><td>PEMS04</td><td colspan=\"2\">0.142 0.134</td><td>5.23</td><td colspan=\"2\">0.176 0.166</td><td>5.39</td><td colspan=\"2\">0.227 0.216</td><td>5.24</td><td colspan=\"2\">0.311 0.296</td><td>4.77</td></tr><tr><td>PEMS07</td><td colspan=\"2\">0.121 0.114</td><td>5.81</td><td colspan=\"2\">0.155 0.144</td><td>6.50</td><td colspan=\"2\">0.204 0.189</td><td>7.36</td><td colspan=\"2\">0.277 0.256</td><td>7.85</td></tr><tr><td>PEMS08</td><td colspan=\"2\">0.137 0.129</td><td>5.89</td><td colspan=\"2\">0.169 0.157</td><td>7.29</td><td colspan=\"2\">0.224 0.206</td><td>7.70</td><td colspan=\"2\">0.314 0.288</td><td>8.39</td></tr></table>", "type_str": "table", "text": "Downstream imputation with 100% samples. Pre-training benefit ∆% is calculated as the ratio of decreased imputing error in MSE. In the case of 100% samples, our pre-trained model outperforms TimesNet in 56.8% of 44 settings on datasets and masked ratios."}, "TABREF25": {"html": null, "num": null, "content": "<table><tr><td colspan=\"2\">PRE-TRAINED</td><td/><td>4G</td><td/><td/><td/><td>4G</td><td/><td>1G</td><td>2G</td><td>4G</td><td>12G</td></tr><tr><td colspan=\"2\">MODEL DIM.</td><td/><td>256</td><td/><td/><td>512</td><td>768</td><td>1024</td><td/><td colspan=\"2\">1024</td><td/></tr><tr><td/><td>LAYERS</td><td>2</td><td>4</td><td>6</td><td>8</td><td/><td>6</td><td/><td/><td>8</td><td/><td/></tr><tr><td>5% SAMPLES</td><td>PEMS03 PEMS04 PEMS07 PEMS08</td><td>0.188 0.223 0.147 0.367</td><td>0.174 0.208 0.131 0.339</td><td>0.168 0.200 0.123 0.322</td><td>0.160 0.190 0.120 0.319</td><td>0.146 0.166 0.106 0.289</td><td>0.138 0.154 0.097 0.256</td><td>0.133 0.145 0.092 0.239</td><td>0.130 0.145 0.092 0.228</td><td>0.128 0.142 0.090 0.221</td><td>0.128 0.143 0.090 0.216</td><td>0.125 0.135 0.087 0.204</td></tr><tr><td>20% SAMPLES</td><td>PEMS03 PEMS04 PEMS07 PEMS08</td><td>0.154 0.182 0.115 0.326</td><td>0.141 0.162 0.104 0.277</td><td>0.137 0.155 0.098 0.247</td><td>0.134 0.150 0.095 0.238</td><td>0.127 0.140 0.086 0.206</td><td>0.124 0.132 0.082 0.193</td><td>0.123 0.124 0.080 0.194</td><td>0.121 0.124 0.079 0.193</td><td>0.120 0.123 0.079 0.185</td><td>0.117 0.122 0.078 0.185</td><td>0.114 0.115 0.076 0.187</td></tr></table>", "type_str": "table", "text": "Detailed results for scaling up the pre-trained scale and the parameter of Timer."}, "TABREF26": {"html": null, "num": null, "content": "<table><tr><td>SCENARIO</td><td/><td colspan=\"2\">1% TARGET</td><td/><td/><td colspan=\"2\">5% TARGET</td><td/><td/><td colspan=\"2\">20% TARGET</td><td/></tr><tr><td>ARCHITECTURE</td><td colspan=\"2\">ENCODER</td><td colspan=\"2\">DECODER</td><td colspan=\"2\">ENCODER</td><td colspan=\"2\">DECODER</td><td colspan=\"2\">ENCODER</td><td colspan=\"2\">DECODER</td></tr><tr><td>PRE-TRAINED</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td><td>NONE</td><td>12G</td></tr><tr><td>ETTH1</td><td colspan=\"12\">0.446 0.413 0.428 0.366 0.437 0.405 0.426 0.362 0.409 0.404 0.385 0.359</td></tr><tr><td>ETTH2</td><td colspan=\"12\">0.338 0.304 0.315 0.284 0.329 0.293 0.314 0.280 0.308 0.299 0.294 0.284</td></tr><tr><td>ETTM1</td><td colspan=\"12\">0.463 0.370 0.407 0.345 0.391 0.340 0.354 0.321 0.344 0.323 0.332 0.321</td></tr><tr><td>ETTM2</td><td colspan=\"12\">0.220 0.181 0.207 0.183 0.197 0.174 0.190 0.176 0.177 0.179 0.177 0.187</td></tr><tr><td>PEMS03</td><td colspan=\"12\">0.225 0.196 0.249 0.151 0.165 0.160 0.158 0.125 0.144 0.145 0.135 0.116</td></tr><tr><td>PEMS04</td><td colspan=\"12\">0.253 0.226 0.320 0.172 0.198 0.184 0.195 0.135 0.167 0.161 0.145 0.120</td></tr><tr><td>PEMS07</td><td colspan=\"12\">0.170 0.156 0.179 0.112 0.126 0.125 0.114 0.087 0.102 0.103 0.093 0.077</td></tr><tr><td>PEMS08</td><td colspan=\"12\">0.496 0.405 0.563 0.286 0.389 0.319 0.391 0.204 0.280 0.246 0.241 0.193</td></tr></table>", "type_str": "table", "text": "Additional downstream 672-pred-96 forecasting results of the subsets of PEMS and ETT under different data scarcity of the encoder-only and decoder-only Transformer. The bold part indicates that the result performs best in the current dataset and sample ratio."}}}}