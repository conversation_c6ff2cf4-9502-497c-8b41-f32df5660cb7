{"paper_id": "GDEM", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:41:09.542917Z"}, "title": "Graph Distillation with Eigenbasis Matching", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Posts and Telecommunication", "location": {"settlement": "Bei-jing, Beijing", "country": "China"}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Posts and Telecommunication", "location": {"settlement": "Bei-jing, Beijing", "country": "China"}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Shi", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Posts and Telecommunication", "location": {"settlement": "Bei-jing, Beijing", "country": "China"}}, "email": "<<EMAIL>>."}], "year": "", "venue": null, "identifiers": {}, "abstract": "The increasing amount of graph data places requirements on the efficient training of graph neural networks (GNNs). The emerging graph distillation (GD) tackles this challenge by distilling a small synthetic graph to replace the real large graph, ensuring GNNs trained on real and synthetic graphs exhibit comparable performance. However, existing methods rely on GNN-related information as supervision, including gradients, representations, and trajectories, which have two limitations. First, GNNs can affect the spectrum (i.e., eigenvalues) of the real graph, causing spectrum bias in the synthetic graph. Second, the variety of GNN architectures leads to the creation of different synthetic graphs, requiring traversal to obtain optimal performance. To tackle these issues, we propose Graph Distillation with Eigenbasis Matching (GDEM), which aligns the eigenbasis and node features of real and synthetic graphs. Meanwhile, it directly replicates the spectrum of the real graph and thus prevents the influence of GNNs. Moreover, we design a discrimination constraint to balance the effectiveness and generalization of GDEM. Theoretically, the synthetic graphs distilled by GDEM are restricted spectral approximations of the real graphs. Extensive experiments demonstrate that GDEM outperforms state-of-the-art GD methods with powerful crossarchitecture generalization ability and significant distillation efficiency. Our code is available at https://github.com/liuyang-tian/GDEM.", "pdf_parse": {"paper_id": "GDEM", "_pdf_hash": "", "abstract": [{"text": "The increasing amount of graph data places requirements on the efficient training of graph neural networks (GNNs). The emerging graph distillation (GD) tackles this challenge by distilling a small synthetic graph to replace the real large graph, ensuring GNNs trained on real and synthetic graphs exhibit comparable performance. However, existing methods rely on GNN-related information as supervision, including gradients, representations, and trajectories, which have two limitations. First, GNNs can affect the spectrum (i.e., eigenvalues) of the real graph, causing spectrum bias in the synthetic graph. Second, the variety of GNN architectures leads to the creation of different synthetic graphs, requiring traversal to obtain optimal performance. To tackle these issues, we propose Graph Distillation with Eigenbasis Matching (GDEM), which aligns the eigenbasis and node features of real and synthetic graphs. Meanwhile, it directly replicates the spectrum of the real graph and thus prevents the influence of GNNs. Moreover, we design a discrimination constraint to balance the effectiveness and generalization of GDEM. Theoretically, the synthetic graphs distilled by GDEM are restricted spectral approximations of the real graphs. Extensive experiments demonstrate that GDEM outperforms state-of-the-art GD methods with powerful crossarchitecture generalization ability and significant distillation efficiency. Our code is available at https://github.com/liuyang-tian/GDEM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Graph neural networks (GNNs) are proven effective in a variety of graph-related tasks (Kipf & Welling, 2017; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018) . However, the non-Euclidean nature of graph structure presents challenges to the efficiency and scalability of GNNs (<PERSON> et al., 2017) . To accelerate training, one data-centric approach is to summarize the large-scale graph into a much smaller one. Traditional methods primarily involve sparsification (<PERSON> & Srivastava, 2011; <PERSON> et al., 2022) and coarsening (<PERSON><PERSON>, 2019; <PERSON> et al., 2023) . However, these methods are typically designed to optimize some heuristic metrics, e.g., spectral similarity (<PERSON><PERSON>, 2019) and pair-wise distance (<PERSON> et al., 2020) , which may be irrelevant to downstream tasks, leading to sub-optimal performance.", "cite_spans": [{"start": 86, "end": 108, "text": "(Kipf & Welling, 2017;", "ref_id": "BIBREF18"}, {"start": 109, "end": 133, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF32"}, {"start": 251, "end": 274, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF11"}, {"start": 443, "end": 472, "text": "(<PERSON><PERSON><PERSON> & Srivastava, 2011;", "ref_id": "BIBREF31"}, {"start": 473, "end": 489, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF40"}, {"start": 505, "end": 519, "text": "(<PERSON><PERSON>, 2019;", "ref_id": "BIBREF25"}, {"start": 520, "end": 539, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF20"}, {"start": 650, "end": 664, "text": "(<PERSON><PERSON>, 2019)", "ref_id": "BIBREF25"}, {"start": 688, "end": 708, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Recently, graph distillation (GD), a.k.a., graph condensation, has attracted considerable attention in graph reduction due to its remarkable compression ratio and lossless performance (<PERSON> et al., 2024) . Generally, GD aims to synthesize a small graph wherein GNNs trained on it exhibit comparable performance to those trained on the real large graph. To this end, existing methods are designed to optimize the synthetic graphs by matching some GNN-related information, such as gradients (<PERSON> et al., 2022b; a) , representations (<PERSON> et al., 2022a) , and training trajectories (<PERSON> et al., 2023) , between the real and synthetic graphs. As a result, the synthetic graph aligns its distribution with the real graph and also incorporates information from downstream tasks.", "cite_spans": [{"start": 184, "end": 202, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF7"}, {"start": 488, "end": 507, "text": "(<PERSON> et al., 2022b;", "ref_id": null}, {"start": 508, "end": 510, "text": "a)", "ref_id": null}, {"start": 529, "end": 548, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 577, "end": 597, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF45"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Despite the considerable progress, existing GD methods require pre-selecting a specific GNN as the distillation model, introducing two limitations: (1) GNNs used for distillation affect the real spectrum, leading to spectrum bias in the synthetic graph, i.e., a few eigenvalues dominate the data distribution. Figure 1 illustrates the total variation (TV) (<PERSON><PERSON><PERSON> & Zhou, 2006) of the real and synthetic graphs. Notably, TV reflects the smoothness of the signal over a graph. A small value of TV indicates a low-frequency distribution, and vice versa. We can observe that the values of TV in the synthetic graph distilled by a low-pass filter consistently appear lower than those in the real graph, while the opposite holds for the high-pass filter, thus verifying the existence of spectrum bias (See Section 3 for a theoretical analysis). (2) The optimal performance is obtained by traversing various GNN architectures, resulting in non-negligible computational costs. Table 1 presents the cross-architecture results of GCOND (<PERSON> et al., 2022b) across six well-known GNNs, including GCN (Kipf & Welling, 2017) , SGC (Wu et al., 2019) , PPNP (Klic<PERSON>a et al., 2019) , ChebyNet (Def- PPNP 72.70 70.40 77.46 73.38 70.56 74.02 Cheb. 73.60 70.62 75.10 77.30 77.62 78.10 Bern. 67.68 73.76 74.30 77.20 78.12 78.28 GPR. 76.04 72.20 77.94 75.92 77.12 77.96 ferrard et al., 2016) , BernNet (He et al., 2021) , and GPR-GNN (Chien et al., 2021) . It can be seen that the evaluation performance of different GNNs varies greatly. As a result, existing GD methods need to distill and traverse various GNN architectures to obtain optimal performance, which significantly improves the time overhead. See Appendix A.1 for the definition of TV and Appendix A.2 for more experimental details.", "cite_spans": [{"start": 356, "end": 377, "text": "(<PERSON>ut<PERSON> & Zhou, 2006)", "ref_id": "BIBREF10"}, {"start": 1027, "end": 1046, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 1089, "end": 1111, "text": "(Kipf & Welling, 2017)", "ref_id": "BIBREF18"}, {"start": 1118, "end": 1135, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF35"}, {"start": 1143, "end": 1166, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF19"}, {"start": 1184, "end": 1371, "text": "PPNP 72.70 70.40 77.46 73.38 70.56 74.02 Cheb. 73.60 70.62 75.10 77.30 77.62 78.10 Bern. 67.68 73.76 74.30 77.20 78.12 78.28 GPR. 76.04 72.20 77.94 75.92 77.12 77.96 <PERSON><PERSON><PERSON><PERSON> et al., 2016)", "ref_id": null}, {"start": 1382, "end": 1399, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF12"}, {"start": 1414, "end": 1434, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF4"}], "ref_spans": [{"start": 317, "end": 318, "text": "1", "ref_id": "FIGREF0"}, {"start": 976, "end": 977, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Once the weaknesses of existing methods are identified, it is natural to ask: How to distill graphs without being affected by different GNNs? To answer this question, we propose Graph Distillation with Eigenbasis Matching (GDEM). Specifically, GDEM decomposes the graph structure into eigenvalues and eigenbasis. During distillation, GDEM matches the eigenbasis and node features of real and synthetic graphs, which equally preserves the information of different frequencies, thus addressing the spectrum bias. Additionally, a discrimination loss is jointly optimized to improve the performance of GDEM and balance its effectiveness and generalization. Upon completing the matching, GDEM leverages the real graph spectrum and synthetic eigenbasis to construct a complete synthetic graph, which prevents the spectrum from being affected by GNNs and ensures the uniqueness of the synthetic graph, thus avoiding the traversal requirement and improving the distillation efficiency.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "The contributions of our paper are as follows. (1) We systematically analyze the limitations of existing distillation methods, including spectrum bias and traversal requirement. (2) We propose GDEM, a novel graph distillation framework, which mitigates the dependence on GNNs by matching the eigenbasis instead of the entire graph structure. Additionally, it is theoretically demonstrated that GDEM preserves essential spectral similarity during distillation. (3) Extensive experiments on seven graph datasets validate the superiority of GDEM over state-of-the-art GD methods in terms of effectiveness, generalization, and efficiency.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Before describing our framework in detail, we first introduce some notations and concepts used in this paper. Specifically, we focus on the node classification task, where the goal is to predict the labels of the nodes in a graph. Assume that there is a graph G = (V, E, X), where V is the set of nodes with |V| = N , E indicates the set of edges, and X ∈ R N ×d is the node feature matrix. The adjacency matrix of G is defined as A ∈ {0, 1} N ×N , where A ij = 1 if there is an edge between nodes i and j, and A ij = 0 otherwise. The corresponding normalized Laplacian matrix is defined as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminary", "sec_num": "2."}, {"text": "L = I N -D -1 2 AD -1 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminary", "sec_num": "2."}, {"text": ", where I N is an identity matrix and D is the degree matrix with D ii = j A ij for i ∈ V and D ij = 0 for i ̸ = j. Without loss of generality, we assume that G is undirected and all the nodes are connected.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminary", "sec_num": "2."}, {"text": "Eigenbasis and Eigenvalue. The normalized graph Laplacian can be decomposed as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminary", "sec_num": "2."}, {"text": "L = UΛU ⊤ = N i=1 λ i u i u ⊤ i , where Λ = diag({λ i } N i=1 ) are the eigenvalues and U = [u 1 , • • • , u N ] ∈ R N ×N is the eigenbasis, consisting of a set of eigenvectors. Each eigenvector u i ∈ R N has a cor- responding eigenvalue λ i , such that Lu i = λ i u i . Without loss of generality, we assume 0 ≤ λ 1 ≤ • • • ≤ λ N ≤ 2. Graph Distillation. GD aims to distill a small synthetic graph G ′ = (V ′ , E ′ , X ′ ), where |V ′ | = N ′ ≪ N and X ′ ∈ R N ′ ×d ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminary", "sec_num": "2."}, {"text": "from the real large graph G. Meanwhile, GNNs trained on G and G ′ will have comparable performance, thus accelerating the training of GNNs. Existing frameworks can be divided into three categories: gradient matching, distribution matching, and trajectory matching. See Appendix C for more detailed descriptions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminary", "sec_num": "2."}, {"text": "In this section, we give a detailed analysis of the objective of gradient matching in graph data, which motivates the design of our method. We start with a vanilla example, which adopts a one-layer GCN as the distillation model and simplifies the objective of GNNs into the MSE loss:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L = 1 2 ∥AXW -Y∥ 2 F , (", "eq_num": "1"}], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "where W is the model parameter. The gradients on the real and synthetic graphs are calculated as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "∇ W = (AX) T (AXW -Y) , ∇ ′ W = (A ′ X ′ ) T (A ′ X ′ W -Y ′ ) . (2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "Assume that the objective of gradient matching is the MSE loss between two gradients, i.e., L GM = ∥∇ W -∇ ′ W ∥ 2 F . To further characterize its properties, we analyze the following upper-bound of L GM :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L GM ≤∥W∥ 2 F ∥X ⊤ A 2 X -X ′ ⊤ A ′ 2 X ′ ∥ 2 F + ∥X ⊤ AY -X ′ ⊤ A ′ Y ′ ∥ 2 F ,", "eq_num": "(3)"}], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "where X ⊤ A 2 X and X ⊤ AY are two target distributions in the real graph, which are used to supervise the update of the synthetic graph. However, both of them will be dominated by a few eigenvalues, resulting in spectrum bias.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "Lemma 3.1. The target distribution of GCN is dominated by the smallest eigenvalue after stacking multiple layers.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "Proof. The target distribution can be reformulated as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "X ⊤ A 2t X = N i=1 (1 -λ i ) 2t X ⊤ u i u ⊤ i X, (", "eq_num": "4"}], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "where t is the number of layers. When t goes to infinity, only the smallest eigenvalue λ 0 = 0 preserves its coefficient (1 -λ 0 ) 2t = 1 and other coefficients tend to 0. Hence, the target distribution X ⊤ A 2t X is dominated by X ⊤ u 0 u ⊤ 0 X. The same analysis can be applied for X ⊤ A t Y.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "Lemma 3.2. Suppose the distillation GNN has an analytic filtering function g(•). Then the target distributions will be dominated by the eigenvalues whose filtered values are greater than 1, i.e., g(λ i ) ≥ 1. Proof. The objective function of distillation GNN is L = 1 2 ∥g(L)XW -Y∥ 2 F . Then the target distributions become X ⊤ g(L) 2t X and X ⊤ g(L) t Y as g is analytic. Therefore, the filtered eigenvalues with values g(λ i ) ≥ 1 retain their coefficients and dominate the target distributions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "Lemmas 3.1 and 3.2 state that leveraging the information of GNNs in distillation will introduce a spectral bias in the target distributions. As a result, the synthetic graph can only match part of the data distribution of the real graph, leaving its structural information incomplete.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spectrum Bias in Gradient Matching", "sec_num": "3."}, {"text": "In this section, we introduce the proposed method GDEM. Compared with previous methods, e.g., gradient matching (Figure 2(a) ) and distribution matching (Figure 2 (b)), GDEM, illustrated in 2(c), does not rely on specific GNNs, whose distillation process can be divided into two steps: (1) Matching the eigenbasis and node features between the real and synthetic graphs. (2) Constructing the synthetic graph by using the synthesized eigenbasis and real spectrum. ", "cite_spans": [], "ref_spans": [{"start": 120, "end": 124, "text": "2(a)", "ref_id": "FIGREF2"}, {"start": 161, "end": 162, "text": "2", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "The Proposed Method: GDEM", "sec_num": "4."}, {"text": "𝑢 # 𝑢 # ⊺ 𝑋 𝑋 ⊺ 𝑢 # ! 𝑢 # ! ⊺ 𝑋 ! 𝑋 ! ⊺ (c) Eigenbasis matching", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Proposed Method: GDEM", "sec_num": "4."}, {"text": "The eigenbasis of a graph represents its crucial structural information. For example, eigenvectors corresponding to smaller eigenvalues reflect the global community structure, while eigenvectors corresponding to larger eigenvalues encode local details (<PERSON> et al., 2021) . Generally, the number of eigenvectors is the same as the number of nodes in a graph, suggesting that we cannot preserve all the real eigenbasis in the synthetic graph. Therefore, GDEM is designed to match eigenvectors with the K 1 smallest and the K 2 largest eigenvalues, where K 1 and K 2 are hyperparameters, and K 1 + K 2 = K ≤ N ′ . This approach has been proven effective in both graph coarsening (<PERSON> et al., 2020) and spectral GNNs (<PERSON> et al., 2023) . We initialize a matrix", "cite_spans": [{"start": 252, "end": 269, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF1"}, {"start": 675, "end": 693, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF17"}, {"start": 712, "end": 729, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "Eigenbasis Matching", "sec_num": "4.1."}, {"text": "U ′ K = [u ′ 1 , • • • , u ′ N ′ ] ∈ R N ′ ×K to match the principal eigenbasis of the real graph, denoted as U K = [u 1 , • • • , u K1 , u N -K2 , • • • , u N ] ∈ R N ×K .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Eigenbasis Matching", "sec_num": "4.1."}, {"text": "To eliminate the influence of GNNs, GDEM does not use the spectrum information during distillation. Therefore, the first term in Equation 3 becomes:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Eigenbasis Matching", "sec_num": "4.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L e = K k=1 X ⊤ u k u ⊤ k X -X ′ ⊤ u ′ k u ′ k ⊤ X ′ 2 F ,", "eq_num": "(5)"}], "section": "Eigenbasis Matching", "sec_num": "4.1."}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Eigenbasis Matching", "sec_num": "4.1."}, {"text": "u k u ⊤ k and u ′ k u ′ k", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Eigenbasis Matching", "sec_num": "4.1."}, {"text": "⊤ are the subspaces induced by the k-th eigenvector in the real and synthetic graphs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Eigenbasis Matching", "sec_num": "4.1."}, {"text": "Additionally, as the basis of graph Fourier transform, eigenvectors are naturally normalized and orthogonal to each other. However, directly optimizing U ′ K via gradient de-scent cannot preserve this property. Therefore, an additional regularization is used to constrain the representation space:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Eigenbasis Matching", "sec_num": "4.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L o = U ′ K ⊤ U ′ K -I K 2 F .", "eq_num": "(6)"}], "section": "Eigenbasis Matching", "sec_num": "4.1."}, {"text": "See Appendix A.3 for more implementation details.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Eigenbasis Matching", "sec_num": "4.1."}, {"text": "In practice, we find that eigenbasis matching improves the cross-architecture generalization of GDEM but contributes less to the performance of node classification as it only preserves the global distribution, i.e., X ⊤ uu ⊤ X, without considering the information of downstream tasks. Therefore, we need to approximate the second term in Equation 3. Interestingly, we find that X ⊤ AY ∈ R d×C indicates the category-level representations, which assigns each category a d-dimensional representation. However, the MSE loss only emphasizes the intra-class similarity between the real and synthetic graphs and ignores the inter-class dissimilarity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discrimination Constraint", "sec_num": "4.2."}, {"text": "Based on this discovery, we design a discrimination constraint to effectively preserve the category-level information, which can also be treated as a class-aware regularization technique (<PERSON> et al., 2023; <PERSON> et al., 2022) . Specifically, we first learn the category-level representations of the real and synthetic graphs:", "cite_spans": [{"start": 187, "end": 206, "text": "(<PERSON> et al., 2023;", "ref_id": null}, {"start": 207, "end": 225, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Discrimination Constraint", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "H = Y ⊤ AX, H ′ = Y ′ ⊤ K k=1 (1 -λ k )u ′ k u ′ k ⊤ X ′ ,", "eq_num": "(7)"}], "section": "Discrimination Constraint", "sec_num": "4.2."}, {"text": "where λ k is the k-th eigenvalue of the real graph Laplacian.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discrimination Constraint", "sec_num": "4.2."}, {"text": "We then constrain the cosine similarity between H and H ′ :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discrimination Constraint", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L d = C i=1 1 - H ⊤ i • H ′ i ||H i || ||H ′ i || + C i,j=1 i̸ =j H ⊤ i • H ′ j ||H i || ||H ′ j || .", "eq_num": "(8)"}], "section": "Discrimination Constraint", "sec_num": "4.2."}, {"text": "Note that the discrimination constraint introduces the spectrum information in the distillation process, which conflicts with the eigenbasis matching. However, we find that adjusting the weights of eigenbasis matching and the discrimination constraint can balance the performance and generalization of GDEM. Ablation studies can be seen in Section 6.5.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discrimination Constraint", "sec_num": "4.2."}, {"text": "In summary, the overall loss function of GDEM is formulated as the weighted sum of three regularization terms:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Final Objective and Synthetic Graph Construction", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L total = αL e + βL d + γL o ,", "eq_num": "(9)"}], "section": "Final Objective and Synthetic Graph Construction", "sec_num": "4.3."}, {"text": "where α, β, and γ are the hyperparameters. The pseudocode of GDEM is presented in Algorithm 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Final Objective and Synthetic Graph Construction", "sec_num": "4.3."}, {"text": "Upon minimizing the total loss function, the outputs of GDEM are the eigenbasis and node features of the synthetic  and L d via Eqs. 5, 6, and 8 Compute", "cite_spans": [], "ref_spans": [{"start": 115, "end": 115, "text": "", "ref_id": null}, {"start": 120, "end": 138, "text": "L d via Eqs. 5, 6,", "ref_id": "TABREF6"}, {"start": 143, "end": 144, "text": "8", "ref_id": null}], "eq_spans": [], "section": "Final Objective and Synthetic Graph Construction", "sec_num": "4.3."}, {"text": "Algorithm 1 GDEM for Graph Distillation Input: Real graph G = (A, X, Y) with eigenvalues {λ i } K i=1 and eigenbasis U K Init: Synthetic graph G ′ with eigenbasis U ′ K , node fea- tures X ′ , and labels Y ′ for t = 1 to T do Compute L e , L o ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Final Objective and Synthetic Graph Construction", "sec_num": "4.3."}, {"text": "L total = αL e + βL d + γL o if t%(τ 1 + τ 2 ) < τ 1 then Update U ′ K ← U ′ K -η 1 ∇ U ′ K L total else Update X ′ ← X ′ -η 2 ∇ X ′ L total end if end for Compute A ′ = K k=1 (1 -λ k )u ′ k u ′ k ⊤ Return: A ′ , X ′", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Final Objective and Synthetic Graph Construction", "sec_num": "4.3."}, {"text": "graph. However, the data remains incomplete due to the absence of the graph spectrum. Essentially, the graph spectrum encodes the global shape of a graph (<PERSON> et al., 2022) . Ideally, if the synthetic graph preserves the distribution of the real graph, they should have similar spectrums. Therefore, we directly replicate the real spectrum for the synthetic graph to construct its Laplacian matrix or adjacency matrix:", "cite_spans": [{"start": 154, "end": 178, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "Final Objective and Synthetic Graph Construction", "sec_num": "4.3."}, {"text": "L ′ = K k=1 λ k u ′ k u ′ k ⊤ , A ′ = K k=1 (1 -λ k )u ′ k u ′ k ⊤ . (10)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Final Objective and Synthetic Graph Construction", "sec_num": "4.3."}, {"text": "Complexity. The complexity of decomposition is O(N 3 ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion", "sec_num": "4.4."}, {"text": "However, given that we only utilize the K smallest or largest eigenvalues, the complexity reduces to O(KN 2 ). Additionally, u ⊤ k X in Equation 5 Relation to Message Passing. Message-passing (MP) is the most popular paradigm for GNNs. Although GDEM does not explicitly perform message-passing during distillation, eigenbasis matching already encodes the information of neighbors as most MP operators rely on the combination of the out product of eigenvectors, e.g., L = N i=1 λ i u i u ⊤ i . Therefore, GDEM not only inherits the expressive power of MP but also addresses the weaknesses of the previous distillation methods.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion", "sec_num": "4.4."}, {"text": "Limitations. Hereby we discuss the limitations of GDEM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion", "sec_num": "4.4."}, {"text": "(1) The decomposition of the real graph introduces additional computational costs for distillation. (2) In scenarios with extremely high compression rates, the synthetic graphs can only match a limited number of real eigenbasis, resulting in performance degradation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion", "sec_num": "4.4."}, {"text": "In this section, we give a theoretical analysis of GDEM and prove that it preserves the restricted spectral similarity. Definition 5.1. (Spectral Similarity (<PERSON><PERSON><PERSON> & <PERSON>, 2011) ) Let A, B ∈ R N ×N be two square matrices. Matrix B is considered a spectral approximation of A if there exists a positive constant ϵ, such that for any vector x ∈ R N , the following inequality holds:", "cite_spans": [{"start": 157, "end": 186, "text": "(<PERSON><PERSON><PERSON> & Srivas<PERSON>va, 2011)", "ref_id": "BIBREF31"}], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "(", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "1 -ϵ)x ⊤ Ax < x ⊤ Bx < (1 + ϵ)x ⊤ Ax.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "However, it is impossible to satisfy this condition for all x ∈ R N (<PERSON>, 2019) . Therefore, we only consider a restricted version of spectral similarity in the feature space. Definition 5.2. (Restricted Spectral Similarity, RSS1 ) The synthetic graph Laplacian L ′ preserves RSS of the real graph Laplacian L, if there exists an ϵ > 0 such that:", "cite_spans": [{"start": 68, "end": 82, "text": "(<PERSON><PERSON>, 2019)", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "(1-ϵ)x ⊤ Lx < x ′ ⊤ L ′ x ′ < (1+ϵ)x ⊤ Lx ∀x, x ′ ∈ X, X ′ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "Proposition 5.3. The synthetic graph distilled by GDEM is a restricted ϵ-spectral approximation of the real graph.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "Proof. We first characterize the spectral similarity of node features in the real and synthetic graphs, respectively. Notably, here we use the principal K eigenvalues and eigenvectors as a truncated representation of the real graph", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x ⊤ Lx = x ⊤ N k=1 λ k u k u ⊤ k x ≈ K k=1 λ k x ⊤ u k u ⊤ k x,", "eq_num": "(11)"}], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x ′ ⊤ L ′ x ′ = x ′ ⊤ ( N ′ k=1 λ k u ′ k u ′ k ⊤ + ŨΛ Ũ⊤ )x ′ ≈ K k=1 λ k x ′ ⊤ u ′ k u ′ k ⊤ x ′ + ∆,", "eq_num": "(12)"}], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "where ∆ = x ′ ⊤ ŨΛ Ũ⊤ x ′ and Ũ represents the nonorthogonal terms of the eigenbasis U ′ K , which means that U ′ K + Ũ is strictly orthogonal. Combining Equations 11 and 12, we have", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x ⊤ Lx -x ′ ⊤ L ′ x ′ ≈ K k=1 λ k x ⊤ u k u ⊤ k x - K k=1 λ k x ′ ⊤ u ′ k u ′ k ⊤ x ′ -∆ ≤ K k=1 λ k x ⊤ u k u ⊤ k x -x ′ ⊤ u ′ k u ′ k ⊤ x ′ Le + |∆| Lo . (", "eq_num": "13"}], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "The above inequality shows that the objective of eigenbasis matching is the upper bound of the spectral discrepancy between the real and synthetic graphs. Optimizing L e and L o makes the bound tighter and preserves the spectral similarity of the real graph. The synthetic graph is a restricted ϵ-spectral approximation of the real graph with", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "ϵ = K k=1 λ k x ⊤ u k u ⊤ k x -x ′ ⊤ u ′ k u ′ k ⊤ x ′ + |∆|.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical Analysis", "sec_num": "5."}, {"text": "In this section, we conduct experiments on a variety of graph datasets to validate the effectiveness, generalization, and efficiency of the proposed GDEM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "6."}, {"text": "Datasets. To evaluate the effectiveness of our GDEM, we select seven representative graph datasets, including five homophilic graphs, i.e., <PERSON><PERSON><PERSON>, Pubmed (Kipf & Welling, 2017) , Ogbn-arxiv (<PERSON> et al., 2020) , <PERSON><PERSON><PERSON>r (<PERSON><PERSON> et al., 2020) , and Reddit (<PERSON> et al., 2017) , and two heterophilic graphs, i.e., Squirrel (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) and Gamers (<PERSON> et al., 2021) .", "cite_spans": [{"start": 157, "end": 179, "text": "(Kipf & Welling, 2017)", "ref_id": "BIBREF18"}, {"start": 193, "end": 210, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF14"}, {"start": 220, "end": 239, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF41"}, {"start": 253, "end": 276, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF11"}, {"start": 323, "end": 350, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": null}, {"start": 362, "end": 380, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "6.1."}, {"text": "Baselines. We benchmark our model against several competitive baselines, which can be divided into two categories: (1) Traditional graph reduction methods, including three coreset methods, i.e., Random, Herding, and K-Center (<PERSON>ing, 2009; <PERSON><PERSON> & <PERSON>, 2018) , and one coarsening method (<PERSON><PERSON>, 2019) . ( 2) Graph distillation methods, including two gradient matching methods, i.e., GCOND (<PERSON> et al., 2022b) and SGDD (<PERSON> et al., 2023) , and one trajectory matching method, i.e., SFGC (<PERSON> et al., 2023) . See Appendix A.6 for more details.", "cite_spans": [{"start": 225, "end": 240, "text": "(<PERSON>ing, 2009;", "ref_id": "BIBREF34"}, {"start": 241, "end": 264, "text": "<PERSON><PERSON>, 2018)", "ref_id": "BIBREF30"}, {"start": 293, "end": 307, "text": "(<PERSON><PERSON>, 2019)", "ref_id": "BIBREF25"}, {"start": 396, "end": 415, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 425, "end": 444, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF37"}, {"start": 494, "end": 514, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF45"}], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "6.1."}, {"text": "Evaluation Protocol. To fairly evaluate the quality of synthetic graphs, we perform the following two steps for all methods: (1) Distillation step, where we apply the distillation methods in the training set of the real graphs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "6.1."}, {"text": "(2) Evaluation step, where we train GNNs on the synthetic graph from scratch and then evaluate their performance on the test set of real graphs. In the node classification experiment (Section 6.2), we follow the settings of the original papers (<PERSON> et al., 2022b; <PERSON> et al., 2023; <PERSON> et al., 2023) . In the generalization experiment (Section 6.3), we use six representative GNNs, including three spatial GNNs, i.e., GCN, SGC, and PPNP, and three spectral GNNs, i.e., ChebyNet, BernNet, and GPR-GNN. See Appendix A.7 for more detailed description.", "cite_spans": [{"start": 244, "end": 263, "text": "(<PERSON> et al., 2022b;", "ref_id": null}, {"start": 264, "end": 283, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF45"}, {"start": 284, "end": 302, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF37"}], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "6.1."}, {"text": "Settings and Hyperparameters. To eliminate randomness, in the distillation step, we run the distillation methods 10 times and yield 10 synthetic graphs. Moreover, we set", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "6.1."}, {"text": "K 1 + K 2 = N ′ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "6.1."}, {"text": "To reduce the tuning complexity, we treat r k = {0.8, 0.85, 0.9, 0.95, 1.0} as a hyperparameter and set", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "6.1."}, {"text": "K 1 = r k N ′ , K 2 = (1 -r k )N ′ for eigenbasis matching.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "6.1."}, {"text": "In the evaluation step, spatial GNNs have two aggregation layers and the polynomial order of spectral GNNs is set to 10. For more details, see Appendix A.8. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "6.1."}, {"text": "(A ′ , X ′ ) Coarsening (A ′ , X ′ ) Herding (A ′ , X ′ ) K-Center (A ′ , X ′ ) GCOND (A ′ , X ′ ) SFGC (X ′ ) SGDD (A ′ , X ′ ) GDEM (U ′ , X ′ )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "6.1."}, {"text": "The node classification performance is reported in Table 2 , in which we have the following observations:", "cite_spans": [], "ref_spans": [{"start": 57, "end": 58, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Node Classification", "sec_num": "6.2."}, {"text": "First, the GD methods consistently outperform the traditional methods, including coreset and coarsening. The reasons are two-fold: On the one hand, GD methods can leverage the powerful representation learning ability of GNNs to synthesize the graph data. On the other hand, the distillation process involves the downstream task information.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Node Classification", "sec_num": "6.2."}, {"text": "In contrast, the traditional methods can only leverage the structural information.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Node Classification", "sec_num": "6.2."}, {"text": "Second, GDEM achieves state-of-the-art performance in 6 out of 7 graph datasets, demonstrating its effectiveness in preserving the distribution of real graphs. Existing GD methods heavily rely on the information of GNNs to distill synthetic graphs. However, the results of GDEM reveal that matching eigenbasis can also yield good synthetic graphs. Furthermore, some results of GDEM are better than those on the entire dataset, which may be due to the use of highfrequency information.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Node Classification", "sec_num": "6.2."}, {"text": "Third, GDEM performs slightly worse on Ogbn-arxiv but achieves promising results on other large-scale graphs. We conjecture this is because, under the compression ratios of 0.05% -0.50%, there are only hundreds of eigenvectors for eigenbasis matching, which is not enough to cover all the useful subspaces in Ogbn-arxiv. See Appendix A.9 for further experimental verification.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Node Classification", "sec_num": "6.2."}, {"text": "We evaluate the generalization ability of the synthetic graphs distilled by four different GD methods, including GCOND, SFGC, SGDD, and GDEM. In particular, each synthetic graph is evaluated by six GNNs, and the average accuracy and variance of the evaluation results are shown in Table 3 .", "cite_spans": [], "ref_spans": [{"start": 287, "end": 288, "text": "3", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Cross-architecture Generalization", "sec_num": "6.3."}, {"text": "First, GDEM stands out by exhibiting the highest average accuracy across datasets except for Ogbn-arxiv, indicating that the synthetic graphs distilled by GDEM can consistently benefit a variety of GNNs. Moreover, GDEM significantly reduces the performance gap between different GNNs. For example, the variance of GCOND is 2-6 times higher than that of GDEM. On the other hand, SGDD broadcasts the structural information to synthetic graphs and exhibits better generalization ability than GCOND, implying that preserving graph structures can improve the generalization of synthetic graphs. SFGC proposes structure-free distillation. However, this strategy may lead to restricted application scenarios due to the lack of explicit graph structures. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Cross-architecture Generalization", "sec_num": "6.3."}, {"text": "We compare the optimal performance and time overhead of different GD methods by traversing various GNN architectures in the Pubmed dataset. Since GDEM does not use GNNs during distillation, we remove the inner-and outer-loop of GCOND and SGDD when calculating the time overhead for a fair comparison. Therefore, the running time is faster than the results in <PERSON> et al. (2023) .", "cite_spans": [{"start": 359, "end": 377, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF37"}], "ref_spans": [], "eq_spans": [], "section": "Optimal Performance and Time Overhead", "sec_num": "6.4."}, {"text": "In Table 4 , we can find that both GCOND and SGDD improve their performance by traversing different GNNs compared to the results in Table 3 . However, this strategy also introduces additional computation costs in the distillation stage. As shown in Table 5 , the complexity of GCOND and SGDD is related to the complexity of distillation GNNs. Notably, when choosing GNNs with high complexity, e.g., BernNet, their time overhead will increase significantly.", "cite_spans": [], "ref_spans": [{"start": 9, "end": 10, "text": "4", "ref_id": "TABREF4"}, {"start": 138, "end": 139, "text": "3", "ref_id": "TABREF3"}, {"start": 255, "end": 256, "text": "5", "ref_id": "TABREF5"}], "eq_spans": [], "section": "Optimal Performance and Time Overhead", "sec_num": "6.4."}, {"text": "On the other hand, GDEM still exhibits remarkable performance compared to the traversal results of GCOND and SGDD. More importantly, the complexity of GDEM will not be affected by GNNs, which eliminates the traversal requirement of previous methods. As a result, the overall time overhead of GDEM is significantly smaller than GCOND and SGDD, which validates the efficiency of GDEM. See Appendix A.2 for more generalization results of GCOND and SGDD. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Optimal Performance and Time Overhead", "sec_num": "6.4."}, {"text": "We perform ablation studies in the Pubmed and Gamers datasets to verify the effectiveness of different regularization terms, i.e., L e , L o , and L d .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation Study", "sec_num": "6.5."}, {"text": "Model Analysis. Table 6 shows the roles of different regularization terms. First, all of them contribute to both the effectiveness and generalization of GDEM. Specifically, L e and L o primarily govern the generalization ability of GDEM, as the variance of GNNs increases significantly when removing either of them. Second, we observe that L d hurts the generalization of GDEM. The reason is that the discrimination constraint uses the information of the graph spectrum and introduces the low-frequency preference. But it also improves the performance of GDEM. Therefore, GDEM needs to carefully balance these two loss functions.", "cite_spans": [], "ref_spans": [{"start": 22, "end": 23, "text": "6", "ref_id": "TABREF6"}], "eq_spans": [], "section": "Ablation Study", "sec_num": "6.5."}, {"text": "Parameters Analysis. We conduct an additional parameter analysis to further demonstrate the influence of L e and L d , as illustrated in Figure 3 . Specifically, we observe that with the increase in α, the variance of GDEM gradually decreases. However, a higher value of α also leads to performance degeneration. On the other hand, increasing the value of β will continue to increase the variance of GDEM but the accuracy decreases when β surpasses a specific threshold.", "cite_spans": [], "ref_spans": [{"start": 144, "end": 145, "text": "3", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Ablation Study", "sec_num": "6.5."}, {"text": "We visualize the data distribution of synthetic graphs for a better understanding of our model. Specifically, Figure 4 illustrates the synthetic graphs distilled by GCOND, SGDD, and GDEM, from which we can observe that the value of TV in GDEM is the closest to the real graph. SGDD is closer to the distribution of the real graph than GCOND, implying that SGDD can better preserve the structural information. However, the performance is still not as good as GDEM, which validates the effectiveness of eigenbasis matching.", "cite_spans": [], "ref_spans": [{"start": 117, "end": 118, "text": "4", "ref_id": null}], "eq_spans": [], "section": "Visualization", "sec_num": "6.6."}, {"text": "Besides, we also visualize the synthetic graphs distilled by GDEM at different epochs in Figure 5 . We can find that with the optimization of GDEM, the value of TV in the synthetic graphs is approaching the real graph (0.42 → 0.73 → 0.88), which validates Proposition 5.3 that GDEM can preserve the spectral similarity of the real graph.", "cite_spans": [], "ref_spans": [{"start": 96, "end": 97, "text": "5", "ref_id": null}], "eq_spans": [], "section": "Visualization", "sec_num": "6.6."}, {"text": "Graph Neural Networks aim to design effective convolution operators to exploit the node features and topology structure information adequately. GNNs have achieved great success in graph learning and play a vital role in diverse realworld applications (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2017) . Existing methods are roughly divided into spatial and spectral approaches. Spatial GNNs focus on neighbor aggregation strategies in the vertical domain (Kipf & Welling, 2017; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018; <PERSON> et al., 2017) . Spectral GNNs aim to design filters in the spectral domain to extract certain frequencies for the downstream tasks (<PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2016; <PERSON> et al., 2023; <PERSON> et al., 2022; 2021) .", "cite_spans": [{"start": 251, "end": 270, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF27"}, {"start": 271, "end": 289, "text": "<PERSON> et al., 2017)", "ref_id": "BIBREF38"}, {"start": 444, "end": 466, "text": "(Kipf & Welling, 2017;", "ref_id": "BIBREF18"}, {"start": 467, "end": 491, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF32"}, {"start": 492, "end": 514, "text": "<PERSON> et al., 2017)", "ref_id": "BIBREF11"}, {"start": 632, "end": 652, "text": "(<PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF4"}, {"start": 653, "end": 677, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2016;", "ref_id": "BIBREF5"}, {"start": 678, "end": 694, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF2"}, {"start": 695, "end": 711, "text": "He et al., 2022;", "ref_id": "BIBREF13"}, {"start": 712, "end": 717, "text": "2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "7."}, {"text": "Dataset Distillation (DD) has shown great potential in reducing data redundancy and accelerating model training (Sachdeva & McAuley, 2023; Lei & Tao, 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) . DD aims to generate small yet informative synthetic training data by matching the model gradient (<PERSON> et al., 2021; <PERSON> et al., 2022b) , data distribution (<PERSON> & <PERSON>, 2023; <PERSON> et al., 2022) , and training trajectory (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2023) between the real and synthetic data. As a result, models trained on the real and synthetic data will have comparable performance.", "cite_spans": [{"start": 112, "end": 138, "text": "(Sachdeva & McAuley, 2023;", "ref_id": "BIBREF29"}, {"start": 139, "end": 155, "text": "Lei & Tao, 2023;", "ref_id": "BIBREF21"}, {"start": 156, "end": 174, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF8"}, {"start": 175, "end": 191, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF39"}, {"start": 291, "end": 310, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF43"}, {"start": 311, "end": 329, "text": "<PERSON> et al., 2022b)", "ref_id": null}, {"start": 350, "end": 370, "text": "(<PERSON> & Bilen, 2023;", "ref_id": "BIBREF42"}, {"start": 371, "end": 389, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF33"}, {"start": 416, "end": 442, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF3"}, {"start": 443, "end": 460, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "7."}, {"text": "DD has been widely used for graph data, including nodelevel tasks, e.g., GCond (<PERSON> et al., 2022b) , SFGC (<PERSON> et al., 2023) , GCDM (<PERSON> et al., 2022a) and MCond (<PERSON> et al., 2023) , and graph-level tasks, e.g., DosCond (<PERSON> et al., 2022a) and KIDD (<PERSON> et al., 2023) . GCond is the first GD method based on gradient matching, which needs to optimize GNNs during the distillation procedure, resulting in inefficient computation. DosCond further provides one-step gradient matching to approximate gradient matching, thereby avoiding the bi-level optimization. GCDM proposes distribution matching for GD, which views the receptive fields of a graph as its distribution. Additionally, SFGC proposes structure-free GD to compress the structural information into the node features. KIDD utilizes the kernel ridge regression to further reduce the computational cost. However, all these methods do not consider the influence of GNNs, resulting in spectrum bias and traversal requirement.", "cite_spans": [{"start": 79, "end": 98, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 106, "end": 126, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF45"}, {"start": 134, "end": 153, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 164, "end": 182, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF6"}, {"start": 222, "end": 241, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 251, "end": 268, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "7."}, {"text": "In this paper, we propose eigenbasis matching for graph distillation, which only aligns the eigenbasis and node features of the real and synthetic graphs, thereby alleviating the spectrum bias and traversal requirement of the previous methods. Theoretically, GDEM preserves the restricted spectral similarity of the real graphs. Extensive experiments on both homophilic and heterophilic graphs validate the effectiveness, generalization, and efficiency of the proposed method.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "8."}, {"text": "A promising future work is to explore eigenbasis matching without the need for explicit eigenvalue decomposition. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "8."}, {"text": "- O(r L N d 2 ) + O(N ′2 d 2 ) SGDD - O(r L N d 2 ) + O(N ′2 N ) SFGC O(M S(LEd + LN d 2 )) O(LN ′ d 2 ) GDEM O(KN 2 + KN d + Ed) O(KN ′2 + KN ′ d + (K + C)d 2 )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "8."}, {"text": "A.5. Statistics of Datasets", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "8."}, {"text": "In the experiments, we use seven graph datasets to validate the effectiveness of GDEM. For homophilic graphs, we use the public data splits. For heterophilic graphs, we use the splitting with training/validation/test sets accounting for 2.5%/2.5/%95% on Squirrel, and 50%/25%25% on Gamers. The detailed statistical information of each dataset is shown in Table 10 . For a fair comparison of performance, we adopt the results of baselines reported in their papers, which are evaluated through meticulous experimental design and careful hyperparameter tuning. The experimental details are as follows: (1) GCOND employs a 2-layer SGC for distillation and a 2-layer GCN with 256 hidden units for evaluation.", "cite_spans": [], "ref_spans": [{"start": 361, "end": 363, "text": "10", "ref_id": "TABREF8"}], "eq_spans": [], "section": "Conclusion", "sec_num": "8."}, {"text": "(2) SGDD employs a 2-layer SGC for distillation and a 2-layer GCN with 256 hidden units for evaluation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "8."}, {"text": "(3) SFGC employs 2-layer GCNs with 256 hidden units both for distillation and evaluation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "8."}, {"text": "Performance Evaluation. For comparison with baselines, we report the performance of GDEM evaluated with a 2-layer GCN with 256 hidden units. Specifically, we generate 10 synthetic graphs with different seeds on the original graph. Then we train the GCN using these 10 synthetic graphs and report the average results of the best performance evaluated on test sets of the original graph.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.7. Evaluation Details", "sec_num": null}, {"text": "Generalization Evaluation. For generalization evaluation, we train 6 GNNs using the synthetic graphs generated by different distillation methods. For SGC, GCN, and APPNP, we use 2-layer aggregations. For ChebyNet, we set the convolution layers to 2 with propagation steps from {2, 3, 5}. For BernNet and GPRGNN, we set the polynomial order to 10. The hidden units of both convolution layers and linear feature transformation are 256.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.7. Evaluation Details", "sec_num": null}, {"text": "Hyperparameter details are listed in Table 11 . τ 1 and τ 2 are steps for alternating updates of node features and eigenvectors. α, β, and γ denote the weights in Equation 9. lr feat and lr eigenvecs are the learning rates of node features and eigenvectors, respectively. To investigate the reason why GDEM performs slightly worse on Obgn-arxiv but achieves promising results on other large-scale graphs, we evaluate the number of useful eigenbasis in both Ogbn-arxiv and Reddit. Specifically, we first truncate the graph structures of Ogbn-arxiv and Reddit by:", "cite_spans": [], "ref_spans": [{"start": 43, "end": 45, "text": "11", "ref_id": "TABREF9"}], "eq_spans": [], "section": "A.8. <PERSON><PERSON><PERSON><PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "A ′ = K1 k=1 λ k u k u ⊤ k + N k=N -K2+1 λ k u k u ⊤ k (", "eq_num": "17"}], "section": "A.8. <PERSON><PERSON><PERSON><PERSON><PERSON>", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.8. <PERSON><PERSON><PERSON><PERSON><PERSON>", "sec_num": null}, {"text": "where K 1 = r k K and K 2 = (1 -r k )K. We then gradually increase the value of K and train a 2-layer SGC on each truncated graph structure. The results are shown in Table 12 .", "cite_spans": [], "ref_spans": [{"start": 172, "end": 174, "text": "12", "ref_id": "TABREF10"}], "eq_spans": [], "section": "A.8. <PERSON><PERSON><PERSON><PERSON><PERSON>", "sec_num": null}, {"text": "We can observe that in Reddit, only 1,000 eigenvectors are enough to match the performance of the full graph (93.45 / 94.51 ≈ 98.9%), while in Ogbn-arxiv, a large number of eigenvectors (5,000) is required to approximate the full graph (69.22 / 70.02 ≈ 98.9%). Thus, we speculate that the structure information of Ogbn-arxiv is more widely distributed in the eigenbasis, making it challenging for GDEM to compress the entire distribution in synthetic data with an extremely small compression rate.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.8. <PERSON><PERSON><PERSON><PERSON><PERSON>", "sec_num": null}, {"text": "We further theoretically analyze whether the gradient matching method can preserve the restricted spectral similarity. Given x ′ and L ′ learned by the gradient matching method, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Analysis of RSS for Gradient Matching", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x ⊤ Lx -x ′ ⊤ L ′ x ′ = K k=0 λ k x ⊤ u k u ⊤ k x - K k=0 λ ′ k x ′ ⊤ u ′ k u ′ k ⊤ x ′ = K k=0 λ k x ⊤ u k u ⊤ k x - K k=0 λ k x ′ ⊤ u ′ k u ′ k ⊤ x ′ + K k=0 λ k x ′ ⊤ u ′ k u ′ k ⊤ x ′ - K k=0 λ ′ k x ′ ⊤ u ′ k u ′ k ⊤ x ′ ⩽ K k=0 λ k x ⊤ u k u ⊤ k x -x ′ ⊤ u ′ k u ′ k ⊤ x ′ + K k=0 |λ k -λ ′ k | x ′ ⊤ u ′ k u ′ k ⊤ x ′", "eq_num": "(18)"}], "section": "B. Theoretical Analysis of RSS for Gradient Matching", "sec_num": null}, {"text": "Combining with Lemma 3.1, when the number of GCN layers goes to infinity, the objective optimization based on gradient matching is dominated by x ⊤ u 0 u ⊤ 0 xx ′ ⊤ u ′ 0 u ′ 0 ⊤ x ′ , while paying less attention to the optimization of", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Analysis of RSS for Gradient Matching", "sec_num": null}, {"text": "x ⊤ u k u ⊤ k x -x ′ ⊤ u ′ k u ′ k ⊤", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Analysis of RSS for Gradient Matching", "sec_num": null}, {"text": "x ′ , when k ̸ = 0. Thus, gradient matching fails to constrain the first term of the upper bound of RSS. Moreover, gradient matching introduces spectrum bias causing λ ′ k ̸ = λ k , thus failing to constrain the second term of the upper bound. In summary, the gradient matching method is unable to preserve the restricted spectral similarity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Theoretical Analysis of RSS for Gradient Matching", "sec_num": null}, {"text": "Gradient Matching (<PERSON> et al., 2022b; a) generates the synthetic graph and node features by minimizing the differences between model gradients on G and G ′ , which can be formulated as:", "cite_spans": [{"start": 18, "end": 37, "text": "(<PERSON> et al., 2022b;", "ref_id": null}, {"start": 38, "end": 40, "text": "a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "C. G<PERSON> Distiilation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "min A ′ ,X ′ E θ∼P θ [D (∇ θ L (Φ θ (A ′ , X ′ ) , Y ′ ) , ∇ θ L (Φ θ (A, X) , Y))] ,", "eq_num": "(19)"}], "section": "C. G<PERSON> Distiilation", "sec_num": null}, {"text": "where Φ θ is the condensation GNNs with parameters θ, ∇ θ indicates the model gradients, D is a metric to measure their differences, and L is the loss function. For clarity, we omit the subscript that indicates the training data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. G<PERSON> Distiilation", "sec_num": null}, {"text": "Distribution Matching (<PERSON> et al., 2022a) aims to align the distributions of node representations in each GNN layer to generate the synthetic graph, which can be expressed as:", "cite_spans": [{"start": 22, "end": 41, "text": "(<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "C. G<PERSON> Distiilation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "min A ′ ,X ′ E θ∼P θ L t=1 D Φ t θ (A ′ , X ′ ) , Φ t θ (A, X) ,", "eq_num": "(20)"}], "section": "C. G<PERSON> Distiilation", "sec_num": null}, {"text": "where Φ t θ is the t-th layer in GNNs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. G<PERSON> Distiilation", "sec_num": null}, {"text": "Trajectory Matching (<PERSON> et al., 2023) aligns the long-term GNN learning behaviors between the original graph and the synthetic graph:", "cite_spans": [{"start": 20, "end": 40, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF45"}], "ref_spans": [], "eq_spans": [], "section": "C. G<PERSON> Distiilation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "min A ′ ,X ′ E θ * ,i t ∼P Θ T L meta-tt θ * t | p t=t0 , θt | q t=t0 . (", "eq_num": "21"}], "section": "C. G<PERSON> Distiilation", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. G<PERSON> Distiilation", "sec_num": null}, {"text": "where θ * t | p t=t0 and θt | q t=t0 is the parameters of GNN T and GNN S , L meta-tt calculates certain parameter training intervals within θ * ,i t0 , θ * ,i t0+p and θt0 , θt0+q .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. G<PERSON> Distiilation", "sec_num": null}, {"text": "Optimizer. We use the <PERSON> optimizer for all experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. General <PERSON>s", "sec_num": null}, {"text": "RSS defined in this paper is different from<PERSON><PERSON><PERSON> (2019), which limits the signal x in the eigenvector space.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "This work is supported in part by the National Natural Science Foundation of China (No. U20B2045, 62192784, U22B2038, 62002029, 62172052).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "This paper presents work aiming to advance the field of efficient graph learning and will save social resources by diminishing computation and storage energy consumption. There are many potential societal consequences of our work, none of which we feel must be specifically highlighted here.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "A.1. Visualization of Synthetic Graphs Distillation Details with Low-pass and High-pass Filters. We use GCOND to distill two synthetic graphs on Pubmed by replacing SGC with a low-pass filter F L = AXW and a high-pass filter F H = LXW, respectively.Visualization Details Once we generate the synthetic graphs, we calculate the value of total variation (TV) for each dimension. TV is a widely used metric to represent the distribution, i.e., smoothness, of a signal on the graph:Note that the edge number of synthetic graphs and the original graph is different, so we normalize node features and laplacian matrix first:where x i is the i-th dimension node feature. Then we substitute xi and L into Equation 14 calculating the TV of the graph. Additionally, we report the average TV of all dimensions as reported in the legend of the visualization figures.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A. Experimental Details", "sec_num": null}, {"text": "To verify the cross-architecture performance of the GCOND and SGDD, we generate six synthetic graphs on Pubmed under a 0.15% compression ratio, using six GNNs for the distillation procedure. Then we train these GNNs on the six synthetic graphs and evaluate their performance. Experimental settings are as follows.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2. Cross-architecture Performance of GCOND and SGDD", "sec_num": null}, {"text": "Step. For spatial GNNs, i.e., GCN, SGC, and PPNP, we set the aggregation layers to 2. For GCN, we use 256 hidden units for each convolutional layer. For spectral GNNs, i.e., ChebyNet, BernNet, and GPR-GNN, we set the polynomial order to 10. The linear feature transformation layers of all GNNs are set to 1. For hyper-parameters tuning, we select training epochs from {400, 500, 600}, learning rates of node feature and topology structure from {0.0001, 0.0005, 0.001, 0.005, 0.05}, outer loop from {25, 20, 15, 10}, and inner loop from {15, 10, 5, 1}.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Distillation", "sec_num": null}, {"text": "Step. For spatial GNNs, we use two aggregation layers. For spectral GNNs, we set the polynomial order to 10. The hidden units of convolutional layers and linear feature transformation layers are both set to 256. We train each GNN for 2000 epochs and select the model parameters with the best performance on validation sets for evaluation. , where N c is the number of nodes with label c. The setting will make the label distribution of the synthetic graph consistent with the real graph.Initialization of Synthetic Graphs. Different from previous GD methods that directly learn the adjacency matrix of the synthetic graph, GDEM aims to generate its eigenbasis. To ensure that the initialized eigenbasis is valid, we first use the stochastic block model (SBM) to randomly generate the adjacency matrix of the synthetic graph, and then decompose it to produce the top-K eigenvectors as the initialized eigenbasis U ′ K ∈ R N ′ ×K . Moreover, to initialize the synthetic node features X ′ ∈ R N ′ ×d , we first train an MLP ρ(•) in the real node features. Then we freeze the well-trained MLP and feed the synthetic node features into it to minimize the classification objective. This process can be formulated as:where θ indicates the parameters of MLP.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": null}, {"text": "We analyze the complexity of different methods and give the final complexity in Table 9 . We use E to present the number of edges. For simplicity, we use d to denote both feature dimension and hidden units of GNNs. t is the number of GNN layers and r is the number of sampled neighbors per node. θ t denotes the model parameters of the GNNs. For SFGC, M is the number of training trajectories and S is the length of each trajectory.", "cite_spans": [], "ref_spans": [{"start": 86, "end": 87, "text": "9", "ref_id": null}], "eq_spans": [], "section": "A.4. Complexity of Different Methods", "sec_num": null}, {"text": "(1) Pre-processing: The complexity of decomposition is O(KN 2 ). It's noteworthy that the decomposition is performed once per graph and can be repeatedly used for subsequent training, inference, and hyperparameter tuning. Therefore, the time overhead of decomposition should be amortized by the entire experiment rather than simply summarized them. Additionally, we pre-process u ⊤ k X in Equation 5andThe final complexity can be simplified as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Complexity of GDEM.", "sec_num": null}, {"text": "(1) Pre-processing: GCOND doesn't need special pre-processing.(2) Inference for A ′ : O(N ′2 d 2 ).(3) Forward process of SGC on the original graph: O(r t N d 2 ). That on the synthetic graph: O(tN ′2 d + tN ′ d).(4) Calculation of second-order derivatives in backward propagation:The final complexity can be simplified as O(r t N d 2 ) + O(N ′2 d 2 ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Complexity of GCOND.", "sec_num": null}, {"text": "(1) Pre-processing: SGDD doesn't need special pre-processing.(2) Inference for A ′ : O(N ′2 d 2 ).(3) Forward process of SGC on the original graph: O(r t N d 2 ). That on the synthetic graph: O(tN ′2 d + tN ′ d).(4) Calculation of second-order derivatives in backward propagation:The final complexity can be simplified as O(r t N d 2 ) + O(N ′2 N ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Complexity of SGDD.", "sec_num": null}, {"text": "(1) Pre-processing: O(M S(tEd + tN d 2 )). Note that M S is usually very large, so it cannot be omitted.(2) Forward process of GCN on the synthetic graph: O(tN ′ d 2 + tN ′ d). Note that SFGC pre-trains the trajectories on GCN, so there is no need to calculate the forward process on the original graph.(3) Backward propagation: SFGC uses a MTT (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022) method, which results in bi-level optimization (<PERSON> et al., 2023) Resources. The address and licenses of all datasets are as follows:• Citeseer: https://github.com/kimiyoung/planetoid (MIT License)• Pubmed: https://github.com/kimiyoung/planetoid (MIT License)• Ogbn-arxiv: https://github.com/snap-stanford/ogb (MIT License)• Flickr: https://github.com/GraphSAINT/GraphSAINT (MIT License)• Reddit: https://github.com/williamleif/GraphSAGE (MIT License)• Squirrel: https://github.com/benedekrozemberczki/MUSAE (GPL-3.0 license)• Gamers: https://github.com/benedekrozemberczki/datasets (MIT License)", "cite_spans": [{"start": 345, "end": 371, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF3"}, {"start": 419, "end": 436, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF39"}], "ref_spans": [], "eq_spans": [], "section": "Complexity of SFGC.", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Graph spanners: A tutorial review", "authors": [{"first": "A", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": ["D"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Hamm", "suffix": ""}, {"first": "M", "middle": ["J L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["G"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Comput. Sci. Rev", "volume": "37", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, S. <PERSON>, and <PERSON>, <PERSON><PERSON> spanners: A tutorial review. Comput. Sci. Rev., 37:100253, 2020.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Beyond lowfrequency information in graph convolutional networks", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Shi", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "AAAI", "volume": "", "issue": "", "pages": "3950--3957", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Beyond low- frequency information in graph convolutional networks. In AAAI, pp. 3950-3957. AAAI Press, 2021.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Specformer: Spectral graph neural networks meet transformers", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Shi", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Spectral graph neural networks meet transformers. In ICLR, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Dataset distillation by matching training trajectories", "authors": [{"first": "G", "middle": [], "last": "Cazenavette", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Torralba", "suffix": ""}, {"first": "A", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "10708--10717", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, A. <PERSON>, and <PERSON>, <PERSON>. Dataset distillation by matching training trajecto- ries. In CVPR, pp. 10708-10717. IEEE, 2022.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Adaptive universal generalized pagerank graph neural network", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Li", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "In ICLR. OpenReview.net", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. <PERSON>ptive universal generalized pagerank graph neural network. In ICLR. OpenReview.net, 2021.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Convolutional neural networks on graphs with fast localized spectral filtering", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "NIPS", "volume": "", "issue": "", "pages": "3837--3845", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>. Con- volutional neural networks on graphs with fast localized spectral filtering. In NIPS, pp. 3837-3845, 2016.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Graph condensation for inductive node representation learning", "authors": [{"first": "X", "middle": [], "last": "Gao", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": ["V H"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> condensation for inductive node representation learning. ArXiv, abs/2307.15967, 2023.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Graph condensation: A survey", "authors": [{"first": "X", "middle": [], "last": "Gao", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> condensation: A survey. ArXiv, abs/2401.11720, 2024.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "A survey on dataset distillation: Approaches, applications and future directions", "authors": [{"first": "J", "middle": [], "last": "Geng", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "IJCAI", "volume": "", "issue": "", "pages": "6610--6618", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, C. A survey on dataset distillation: Approaches, applications and future directions. In IJCAI, pp. 6610-6618. ijcai.org, 2023.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Towards lossless dataset distillation via difficulty-aligned trajectory matching", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Cazenavette", "suffix": ""}, {"first": "H", "middle": [], "last": "Li", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "You", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.05773"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Towards lossless dataset distillation via difficulty-aligned trajectory matching. arXiv preprint arXiv:2310.05773, 2023.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Laplacian energy of a graph", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2006, "venue": "Linear Algebra and its applications", "volume": "414", "issue": "1", "pages": "29--37", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>cian energy of a graph. Linear Algebra and its applications, 414(1):29-37, 2006.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Inductive representation learning on large graphs", "authors": [{"first": "W", "middle": ["L"], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "NIPS", "volume": "", "issue": "", "pages": "1024--1034", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, J. Inductive representation learning on large graphs. In NIPS, pp. 1024-1034, 2017.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Learning arbitrary graph spectral filters via <PERSON><PERSON><PERSON> approximation", "authors": [{"first": "M", "middle": [], "last": "He", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Bernnet", "suffix": ""}], "year": 2021, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "14239--14251", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Learning arbitrary graph spectral filters via bernstein approxima- tion. In NeurIPS, pp. 14239-14251, 2021.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Convolutional neural networks on graphs with ch<PERSON><PERSON><PERSON><PERSON> approximation, revisited", "authors": [{"first": "M", "middle": [], "last": "He", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Convolutional neural networks on graphs with cheb<PERSON><PERSON><PERSON> approximation, revisited. In NeurIPS, 2022.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Open graph benchmark: Datasets for machine learning on graphs", "authors": [{"first": "W", "middle": [], "last": "Hu", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Zitnik", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Ren", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Catasta", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, J. Open graph benchmark: Datasets for machine learning on graphs. In NeurIPS, 2020.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Condensing graphs via one-step gradient matching", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "KDD", "volume": "", "issue": "", "pages": "720--730", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Condensing graphs via one-step gradient matching. In KDD, pp. 720-730, 2022a.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Graph condensation for graph neural networks", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> condensation for graph neural networks. In ICLR, 2022b.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Graph coarsening with preserved spectral properties", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["F"], "last": "Jájá", "suffix": ""}], "year": 2020, "venue": "AISTATS", "volume": "108", "issue": "", "pages": "4452--4462", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, J. F. G<PERSON> coarsening with preserved spectral properties. In AISTATS, volume 108, pp. 4452-4462. PMLR, 2020.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Semi-supervised classification with graph convolutional networks", "authors": [{"first": "T", "middle": ["N"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Welling", "suffix": ""}], "year": 2017, "venue": "ICLR. OpenReview.net", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON>. Semi-supervised classification with graph convolutional networks. In ICLR. OpenRe- view.net, 2017.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Predict then propagate: Graph neural networks meet personalized pagerank", "authors": [{"first": "J", "middle": [], "last": "<PERSON>lic<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "ICLR. OpenReview.net", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>dict then propagate: Graph neural networks meet personalized pagerank. In ICLR. OpenReview.net, 2019.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Featured graph coarsening with similarity guarantees", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "ICML", "volume": "202", "issue": "", "pages": "17953--17975", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>- tured graph coarsening with similarity guarantees. In ICML, volume 202 of Proceedings of Machine Learning Research, pp. 17953-17975. PMLR, 2023.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "A comprehensive survey of dataset distillation", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Tao", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, S. and <PERSON>, D. A comprehensive survey of dataset distillation. ArXiv, abs/2301.05603, 2023.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Large scale learning on nonhomophilous graphs: New benchmarks and strong simple methods", "authors": [{"first": "D", "middle": [], "last": "Lim", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": ["L"], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Lim", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "20887--20902", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Large scale learning on non- homophilous graphs: New benchmarks and strong simple methods. In NeurIPS, pp. 20887-20902, 2021.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Graph condensation via receptive field distribution matching", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Song", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> G<PERSON> conden- sation via receptive field distribution matching. ArXiv, abs/2206.13697, 2022a.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Dataset distillation via factorization", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ye", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Dataset distillation via factorization. In NeurIPS, 2022b.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Graph reduction with spectral and cut guarantees", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "J. <PERSON>. Learn. Res", "volume": "20", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> reduction with spectral and cut guarantees. J. <PERSON>. Learn. Res., 20:116:1-116:42, 2019.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "SPECTRE: spectral conditioning helps to overcome the expressivity limits of one-shot graph generators", "authors": [{"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "ICML", "volume": "162", "issue": "", "pages": "15159--15179", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>ECTR<PERSON>: spectral conditioning helps to overcome the expressivity limits of one-shot graph generators. In ICML, volume 162, pp. 15159-15179. PMLR, 2022.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Robust preference-guided denoising for graph based social recommendation", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>uan", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "C", "middle": [], "last": "Gao", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "1097--1108", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y. Ro- bust preference-guided denoising for graph based social recommendation. In WWW, pp. 1097-1108. ACM, 2023.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Multi-scale attributed node embedding", "authors": [{"first": "B", "middle": [], "last": "Rozemberczki", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Sarka<PERSON>", "suffix": ""}], "year": null, "venue": "J. Complex Networks", "volume": "9", "issue": "2", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, R. Multi-scale attributed node embedding. J. Complex Networks, 9(2), 2021.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Data distillation: A survey", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, J. Data distillation: A survey. ArXiv, abs/2301.04272, 2023.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Active learning for convolutional neural networks: A core-set approach", "authors": [{"first": "O", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "ICLR. OpenReview.net", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, S. Active learning for convolu- tional neural networks: A core-set approach. In ICLR. OpenReview.net, 2018.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Graph sparsification by effective resistances", "authors": [{"first": "D", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2011, "venue": "SIAM J. Comput", "volume": "40", "issue": "6", "pages": "1913--1926", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, N. Graph sparsification by effective resistances. SIAM J. Comput., 40(6):1913-1926, 2011.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Graph attention networks", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>ò", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>., and <PERSON><PERSON>, Y. Graph attention networks. In ICLR, 2018.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "CAFE: learning to condense dataset by aligning features", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "You", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "12186--12195", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y. CAFE: learning to condense dataset by aligning features. In CVPR, pp. 12186-12195. IEEE, 2022.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Herding dynamical weights to learn", "authors": [{"first": "M", "middle": [], "last": "Welling", "suffix": ""}], "year": 2009, "venue": "ICML", "volume": "382", "issue": "", "pages": "1121--1128", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> dynamical weights to learn. In ICML, volume 382, pp. 1121-1128. ACM, 2009.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Simplifying graph convolutional networks", "authors": [{"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["H S"], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Fifty", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": ["Q"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "ICML", "volume": "97", "issue": "", "pages": "6861--6871", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> <PERSON>. Simplifying graph convolutional networks. In ICML, volume 97, pp. 6861-6871. PMLR, 2019.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Kernel ridge regression-based graph dataset distillation", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Pan", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "KDD", "volume": "", "issue": "", "pages": "2850--2861", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> ridge regression-based graph dataset distillation. In KDD, pp. 2850-2861, 2023.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Does graph distillation see like vision dataset counterpart?", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "Sun", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "You", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}], "year": 2023, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Does graph distillation see like vision dataset counterpart? In NeurIPS, 2023.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "A neural network approach to jointly modeling social networks and mobile trajectories", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Sun", "suffix": ""}, {"first": "W", "middle": ["X"], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "ACM Transactions on Information Systems (TOIS)", "volume": "35", "issue": "4", "pages": "1--28", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, E. <PERSON>. A neural network approach to jointly modeling social networks and mobile trajectories. ACM Transactions on Information Systems (TOIS), 35(4):1-28, 2017.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Dataset distillation: A comprehensive review", "authors": [{"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, X. Dataset distillation: A com- prehensive review. ArXiv, abs/2301.07014, 2023.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Principle of relevant information for graph sparsification", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["C"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "UAI", "volume": "180", "issue": "", "pages": "2331--2341", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, J. C. <PERSON> of relevant information for graph sparsification. In UAI, volume 180 of Proceedings of Machine Learning Research, pp. 2331-2341. PMLR, 2022.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Graph sampling based inductive learning method", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": ["K"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "ICLR. OpenReview.net", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>: Graph sampling based induc- tive learning method. In ICLR. OpenReview.net, 2020.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Dataset condensation with distribution matching", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "WACV", "volume": "", "issue": "", "pages": "6503--6512", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Dataset condensation with distribu- tion matching. In WACV, pp. 6503-6512. IEEE, 2023.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Dataset condensation with gradient matching", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": ["R"], "last": "Mopuri", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICLR. OpenReview.net", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Dataset condensation with gradient matching. In ICLR. OpenReview.net, 2021.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Improved distribution matching for dataset condensation", "authors": [{"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "Qin", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "CVPR", "volume": "", "issue": "", "pages": "7856--7865", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Improved distribution matching for dataset condensation. In CVPR, pp. 7856- 7865. IEEE, 2023.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Structure-free graph condensation: From large-scale graphs to condensed graph-free data", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": ["V H"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Pan", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Structure-free graph condensation: From large-scale graphs to condensed graph-free data. ArXiv, abs/2306.02664, 2023.", "links": null}}, "ref_entries": {"FIGREF0": {"uris": null, "text": "Figure 1. Data distribution of the real and synthetic graphs in Pubmed dataset, where the average TV of the real graph is 0.87. Left: Synthetic graph distilled by a low-pass filter has a lower value of TV (0.75). Right: Synthetic graph distilled by a high-pass filter has a higher value of TV (1.02). For clarity, only the first 100-dimensional features are visualized. Best viewed in color.", "type_str": "figure", "fig_num": "1", "num": null}, "FIGREF2": {"uris": null, "text": "Figure 2. Comparison between different graph distillation methods, where the red characters represent the synthetic data, the solid black lines, and red dotted lines indicate the forward and backward passes, respectively.", "type_str": "figure", "fig_num": "2", "num": null}, "FIGREF3": {"uris": null, "text": "and H in Equation 8 cost O(KN d) and O(Ed) in pre-processing. During distillation, the complexity of L e , L d and L o are O(KN ′ d+Kd 2 ), O(KN ′ d ′ + Cd 2 ), and O(KN ′2 ), respectively.", "type_str": "figure", "fig_num": null, "num": null}, "FIGREF4": {"uris": null, "text": "Figure 3. Influence of Le and L d in GDEM.", "type_str": "figure", "fig_num": "3", "num": null}, "FIGREF5": {"uris": null, "text": "Figure 4. TVs of synthetic graphs distilled by different methods.", "type_str": "figure", "fig_num": "45", "num": null}, "TABREF0": {"text": "Cross-architecture performance (%) of GCOND with various distillation (D) and evaluation (E) GNNs in Pubmed dataset. Bold indicates the best in each column.D ⧹ E GCN SGC PPNP Cheb. Bern. GPR.", "type_str": "table", "content": "<table><tr><td>GCN</td><td>74.57 71.70 75.53 70.13 68.40 71.73</td></tr><tr><td>SGC</td><td>77.72 77.60 77.34 76.03 74.42 76.52</td></tr></table>", "html": null, "num": null}, "TABREF1": {"text": "Node classification performance of different distillation methods, mean accuracy (%) ± standard deviation. Bold indicates the best performance and underline means the runner-up.", "type_str": "table", "content": "<table><tr><td>Dataset</td><td>Ratio (r)</td><td>Random</td><td>Traditional Methods</td><td>Graph Distillation Methods</td><td>Whole Dataset</td></tr></table>", "html": null, "num": null}, "TABREF3": {"text": "Generalization of different distillation methods across GNNs. ↑ means higher the better and ↓ means lower the better. Avg., Std., and Impro. indicate average accuracy, standard deviation, and absolute performance improvement.", "type_str": "table", "content": "<table><tr><td>Dataset (Ratio)</td><td>Methods</td><td colspan=\"5\">Spatial GNNs GCN SGC PPNP ChebyNet BernNet GPR-GNN Spectral GNNs</td><td colspan=\"3\">Avg. (↑) Std. (↓) Impro. (↑)</td></tr><tr><td/><td>GCOND</td><td>70.5 70.3</td><td>69.6</td><td>68.3</td><td>63.1</td><td>67.2</td><td>68.17</td><td>2.54</td><td>(+) 4.21</td></tr><tr><td>Citeseer</td><td>SFGC</td><td>71.6 71.8</td><td>70.5</td><td>71.8</td><td>71.1</td><td>71.7</td><td>71.42</td><td>0.47</td><td>(+) 0.96</td></tr><tr><td>(r = 1.80%)</td><td>SGDD</td><td>70.2 71.3</td><td>69.2</td><td>70.5</td><td>64.7</td><td>69.7</td><td>69.27</td><td>2.14</td><td>(+) 3.11</td></tr><tr><td/><td>GDEM</td><td>72.6 72.1</td><td>72.6</td><td>71.4</td><td>72.6</td><td>73.0</td><td>72.38</td><td>0.51</td><td>-</td></tr><tr><td/><td>GCOND</td><td>77.7 77.6</td><td>77.3</td><td>76.0</td><td>74.4</td><td>76.5</td><td>76.58</td><td>1.15</td><td>(+) 1.34</td></tr><tr><td>Pubmed</td><td>SFGC</td><td>77.5 77.4</td><td>77.6</td><td>77.3</td><td>76.4</td><td>78.6</td><td>77.47</td><td>0.64</td><td>(+) 0.45</td></tr><tr><td>(r = 0.15%)</td><td>SGDD</td><td>78.0 76.6</td><td>78.7</td><td>76.9</td><td>75.5</td><td>77.0</td><td>77.12</td><td>1.02</td><td>(+) 0.80</td></tr><tr><td/><td>GDEM</td><td>78.4 76.1</td><td>78.1</td><td>78.1</td><td>78.2</td><td>78.6</td><td>77.92</td><td>0.83</td><td>-</td></tr><tr><td/><td>GCOND</td><td>63.2 63.7</td><td>63.4</td><td>54.9</td><td>55.0</td><td>60.5</td><td>60.12</td><td>3.80</td><td>(+) 2.90</td></tr><tr><td>Ogbn-arxiv</td><td>SFGC</td><td>65.1 64.8</td><td>63.9</td><td>60.7</td><td>63.8</td><td>64.9</td><td>63.87</td><td>1.50</td><td>(-) 0.85</td></tr><tr><td>(r = 0.25%)</td><td>SGDD</td><td>65.8 64.0</td><td>63.6</td><td>56.4</td><td>62.0</td><td>64.0</td><td>62.63</td><td>3.00</td><td>(+) 0.39</td></tr><tr><td/><td>GDEM</td><td>63.8 62.9</td><td>63.5</td><td>62.4</td><td>61.9</td><td>63.6</td><td>63.02</td><td>0.69</td><td>-</td></tr><tr><td/><td>GCOND</td><td>47.1 46.1</td><td>45.9</td><td>42.8</td><td>44.3</td><td>46.4</td><td>45.43</td><td>1.45</td><td>(+) 3.90</td></tr><tr><td>Flickr</td><td>SFGC</td><td>47.1 42.5</td><td>40.7</td><td>45.4</td><td>45.7</td><td>46.4</td><td>44.63</td><td>2.27</td><td>(+) 4.70</td></tr><tr><td>(r = 0.50%)</td><td>SGDD</td><td>47.1 46.5</td><td>44.3</td><td>45.3</td><td>46.0</td><td>46.8</td><td>46.00</td><td>0.96</td><td>(+) 3.33</td></tr><tr><td/><td>GDEM</td><td>49.4 50.3</td><td>49.4</td><td>48.3</td><td>49.6</td><td>49.0</td><td>49.33</td><td>0.60</td><td>-</td></tr><tr><td/><td>GCOND</td><td>89.4 89.6</td><td>87.8</td><td>75.5</td><td>67.1</td><td>78.8</td><td>81.37</td><td>8.35</td><td>(+) 10.10</td></tr><tr><td>Reddit</td><td>SFGC</td><td>89.7 89.5</td><td>88.3</td><td>82.8</td><td>87.8</td><td>85.4</td><td>87.25</td><td>2.44</td><td>(+) 4.22</td></tr><tr><td>(r = 0.10%)</td><td>SGDD</td><td>91.0 89.4</td><td>89.2</td><td>78.4</td><td>72.4</td><td>81.4</td><td>83.63</td><td>6.80</td><td>(+) 7.84</td></tr><tr><td/><td>GDEM</td><td>93.1 90.0</td><td>92.6</td><td>90.0</td><td>92.7</td><td>90.4</td><td>91.47</td><td>1.35</td><td>-</td></tr><tr><td/><td>GCOND</td><td>25.7 27.2</td><td>23.2</td><td>23.3</td><td>26.0</td><td>26.6</td><td>25.33</td><td>1.55</td><td>(+) 1.89</td></tr><tr><td>Squirrel</td><td>SFGC</td><td>26.9 24.2</td><td>27.2</td><td>25.3</td><td>25.5</td><td>26.6</td><td>25.95</td><td>1.04</td><td>(+) 1.27</td></tr><tr><td>(r = 1.20%)</td><td>SGDD</td><td>24.7 27.2</td><td>22.4</td><td>24.5</td><td>24.7</td><td>27.3</td><td>25.13</td><td>1.69</td><td>(+) 2.09</td></tr><tr><td/><td>GDEM</td><td>28.2 28.0</td><td>25.4</td><td>26.1</td><td>28.2</td><td>27.4</td><td>27.22</td><td>1.09</td><td>-</td></tr><tr><td/><td>GCOND</td><td>58.9 54.2</td><td>60.1</td><td>60.3</td><td>59.1</td><td>59.3</td><td>58.65</td><td>2.05</td><td>(+) 1.57</td></tr><tr><td>Gamers</td><td>SFGC</td><td>58.8 55.0</td><td>56.3</td><td>57.2</td><td>57.5</td><td>59.8</td><td>57.43</td><td>1.57</td><td>(+) 2.79</td></tr><tr><td>(r = 0.25%)</td><td>SGDD</td><td>57.7 54.6</td><td>56.0</td><td>57.3</td><td>58.8</td><td>58.6</td><td>57.17</td><td>1.47</td><td>(+) 3.05</td></tr><tr><td/><td>GDEM</td><td>60.8 59.5</td><td>61.0</td><td>59.9</td><td>59.8</td><td>60.3</td><td>60.22</td><td>0.54</td><td>-</td></tr></table>", "html": null, "num": null}, "TABREF4": {"text": "Optimal performance of different methods.", "type_str": "table", "content": "<table><tr><td colspan=\"6\">Evaluation GCN SGC PPNP Cheb. Bern. GPR.</td></tr><tr><td>GCOND</td><td>77.7 77.6</td><td>77.9</td><td>77.3</td><td>78.2</td><td>78.3</td></tr><tr><td>SGDD</td><td>78.0 76.6</td><td>78.7</td><td>77.5</td><td>78.0</td><td>78.3</td></tr><tr><td>GDEM</td><td>78.4 76.1</td><td>78.1</td><td>78.1</td><td>78.2</td><td>78.6</td></tr></table>", "html": null, "num": null}, "TABREF5": {"text": "Time overhead (s) of different methods.", "type_str": "table", "content": "<table><tr><td colspan=\"8\">Distillation GCN SGC PPNP Cheb. Bern. GPR. Overall</td></tr><tr><td>GCOND</td><td colspan=\"2\">1.99 1.36</td><td>1.52</td><td colspan=\"3\">3.89 56.94 3.05</td><td>68.75</td></tr><tr><td>SGDD</td><td colspan=\"2\">2.95 2.18</td><td>2.33</td><td colspan=\"3\">4.95 58.07 4.28</td><td>74.76</td></tr><tr><td>GDEM</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>1.79</td></tr></table>", "html": null, "num": null}, "TABREF6": {"text": "Ablation studies on Pubmed / Gamers.", "type_str": "table", "content": "<table><tr><td colspan=\"2\">Pubmed</td><td>GCN (↑)</td><td>GPR. (↑)</td><td>Avg. (↑)</td><td>Var. (↓)</td></tr><tr><td colspan=\"2\">GDEM</td><td colspan=\"4\">78.4 / 60.8 78.6 / 60.3 77.92 / 60.22 0.69 / 0.29</td></tr><tr><td colspan=\"2\">w/o L e</td><td colspan=\"4\">76.1 / 56.5 76.9 / 59.8 76.13 / 58.93 1.18 / 2.39</td></tr><tr><td colspan=\"2\">w/o L o</td><td colspan=\"4\">77.9 / 59.0 76.4 / 58.9 77.07 / 58.85 2.15 / 2.34</td></tr><tr><td colspan=\"2\">w/o L d</td><td colspan=\"4\">76.7 / 59.9 77.2 / 60.3 76.77 / 59.78 0.21 / 0.13</td></tr><tr><td>75</td><td colspan=\"3\">1e-4 1e-3 1e-2 1e-1 1e+0 1e+1</td><td/></tr></table>", "html": null, "num": null}, "TABREF7": {"text": "Complexity of different distillation methods.", "type_str": "table", "content": "<table><tr><td>Method</td><td>Pre-processing</td><td>Training</td></tr><tr><td>GCOND</td><td/><td/></tr></table>", "html": null, "num": null}, "TABREF8": {"text": "Statistics of datasets.", "type_str": "table", "content": "<table><tr><td>Dataset</td><td>Nodes</td><td colspan=\"5\">Edges Classes Features Training/Validation/Test Edge hom.</td><td>LCC</td></tr><tr><td>Citeseer</td><td>3,327</td><td>4,732</td><td>6</td><td>3,703</td><td>120/500/1000</td><td>0.74</td><td>2,120</td></tr><tr><td>Pubmed</td><td>19,717</td><td>44,338</td><td>3</td><td>500</td><td>60/500/1,000</td><td>0.80</td><td>19,717</td></tr><tr><td colspan=\"2\">Ogbn-arxiv 169,343</td><td>1,166,243</td><td>40</td><td>128</td><td>90,941/29,799/48,603</td><td colspan=\"2\">0.66 169,343</td></tr><tr><td>Flickr</td><td>89,250</td><td>899,756</td><td>7</td><td>500</td><td>44,625/22,312/22,313</td><td>0.33</td><td>89,250</td></tr><tr><td>Reddit</td><td colspan=\"2\">232,965 57,307,946</td><td>41</td><td>602</td><td>153,932/23,699/55,334</td><td colspan=\"2\">0.78 231,371</td></tr><tr><td>Squirrel</td><td>5,201</td><td>396,846</td><td>5</td><td>2,089</td><td>130/130/4,941</td><td>0.22</td><td>5,201</td></tr><tr><td>Gamers</td><td colspan=\"2\">168,114 13,595,114</td><td>2</td><td>7</td><td>84,056/42,028/42,030</td><td colspan=\"2\">0.55 168,114</td></tr><tr><td>A.6. Baselines</td><td/><td/><td/><td/><td/><td/><td/></tr></table>", "html": null, "num": null}, "TABREF9": {"text": "Hyper-parameters of GDEM.", "type_str": "table", "content": "<table><tr><td>Dataset</td><td>Ratio</td><td colspan=\"5\">epochs K 1 K 2 τ 1 τ 2</td><td>α</td><td>β</td><td>γ</td><td colspan=\"2\">lr feat lr eigenvecs</td></tr><tr><td/><td>0.90%</td><td>500</td><td>30</td><td>0</td><td>5</td><td>1</td><td>1.0</td><td colspan=\"3\">1e-05 1.0 0.0001</td><td>0.01</td></tr><tr><td>Citeseer</td><td>1.80%</td><td>1500</td><td>48</td><td colspan=\"3\">12 10 15</td><td>0.05</td><td colspan=\"3\">1e-05 0.5 0.0005</td><td>0.0005</td></tr><tr><td/><td>3.60%</td><td>500</td><td>114</td><td>6</td><td colspan=\"2\">1 10</td><td>0.01</td><td colspan=\"2\">1e-06 0.1</td><td>0.001</td><td>0.0001</td></tr><tr><td/><td>0.08%</td><td>1000</td><td>15</td><td>0</td><td colspan=\"6\">15 5 0.0001 1e-07 0.01 0.0001</td><td>0.0005</td></tr><tr><td>Pubmed</td><td>0.15%</td><td>1500</td><td>30</td><td>0</td><td>5</td><td>5</td><td>1.0</td><td colspan=\"3\">1e-05 0.01 0.0005</td><td>0.01</td></tr><tr><td/><td>0.30%</td><td>1500</td><td>57</td><td>3</td><td colspan=\"2\">20 1</td><td>0.01</td><td colspan=\"2\">1e-07 0.5</td><td>0.001</td><td>0.0001</td></tr><tr><td/><td>0.05%</td><td>500</td><td>86</td><td>4</td><td>1</td><td colspan=\"5\">5 0.0001 1e-02 0.01 0.0005</td><td>0.0005</td></tr><tr><td>Ogbn-arxiv</td><td>0.25%</td><td>2000</td><td colspan=\"4\">409 45 10 5</td><td>0.01</td><td colspan=\"3\">1e-04 0.01 0.0001</td><td>0.0001</td></tr><tr><td/><td>0.50%</td><td>1000</td><td colspan=\"3\">773 136 1</td><td>5</td><td colspan=\"4\">0.001 1e-04 1.0 0.0001</td><td>0.005</td></tr><tr><td/><td>0.10%</td><td>2000</td><td>44</td><td>0</td><td colspan=\"2\">5 10</td><td>0.01</td><td colspan=\"3\">1e-07 0.05 0.0001</td><td>0.05</td></tr><tr><td>Flickr</td><td>0.50%</td><td>2000</td><td>223</td><td>0</td><td colspan=\"2\">5 10</td><td>0.01</td><td colspan=\"3\">1e-07 0.05 0.0001</td><td>0.05</td></tr><tr><td/><td>1.00%</td><td>2000</td><td>446</td><td>0</td><td colspan=\"2\">5 10</td><td>0.01</td><td colspan=\"3\">1e-07 0.05 0.0001</td><td>0.05</td></tr><tr><td/><td>0.05%</td><td>1000</td><td>76</td><td>0</td><td colspan=\"2\">20 5</td><td>1.0</td><td colspan=\"3\">1e-06 0.01 0.0001</td><td>0.0001</td></tr><tr><td>Reddit</td><td>0.10%</td><td>500</td><td>153</td><td>0</td><td colspan=\"2\">15 10</td><td>0.5</td><td>1e-06</td><td>05</td><td>0.0005</td><td>0.005</td></tr><tr><td/><td>0.50%</td><td>1000</td><td colspan=\"2\">693 76</td><td>5</td><td>5</td><td>1.0</td><td colspan=\"3\">1e-06 0.5 0.0005</td><td>0.0001</td></tr><tr><td/><td>0.60%</td><td>1000</td><td>31</td><td>1</td><td>5</td><td>1</td><td>1.0</td><td colspan=\"3\">1e-07 0.01 0.0001</td><td>0.005</td></tr><tr><td>Squirrel</td><td>1.20%</td><td>500</td><td>62</td><td>3</td><td colspan=\"2\">10 5</td><td>1.0</td><td colspan=\"3\">1e-07 0.01 0.0001</td><td>0.0001</td></tr><tr><td/><td>2.05%</td><td>2000</td><td colspan=\"2\">104 26</td><td>5</td><td colspan=\"5\">1 0.0001 1e-05 0.05 0.0001</td><td>0.01</td></tr><tr><td/><td>0.05%</td><td>2000</td><td>80</td><td>4</td><td colspan=\"6\">15 1 0.0001 1e-07 0.05 0.0001</td><td>0.01</td></tr><tr><td>Gamers</td><td>0.25%</td><td>2000</td><td>420</td><td>0</td><td colspan=\"6\">20 20 0.0001 1e-07 0.05 0.0001</td><td>0.005</td></tr><tr><td/><td>0.50%</td><td>500</td><td colspan=\"8\">756 84 15 1 0.0001 1e-07 0.05 0.0001</td><td>0.0001</td></tr></table>", "html": null, "num": null}, "TABREF10": {"text": "The node classification performance of Ogbn-arxiv and Reddit on various truncated graph structures.", "type_str": "table", "content": "<table><tr><td>Dataset</td><td>K = 500</td><td>K = 1000</td><td>K = 3000</td><td>K = 5000 Full Graph</td></tr><tr><td>Reddit</td><td colspan=\"4\">92.41±0.49 93.45±0.48 93.94±0.41 94.07±0.37 94.51±0.24</td></tr><tr><td colspan=\"5\">Ogbn-arxiv 61.87±0.89 64.65±1.20 67.32±1.11 69.22±0.93 70.02±1.19</td></tr><tr><td colspan=\"3\">A.9. Analysis of the Worse Performance on Obgn-arxiv</td><td/><td/></tr></table>", "html": null, "num": null}}}}