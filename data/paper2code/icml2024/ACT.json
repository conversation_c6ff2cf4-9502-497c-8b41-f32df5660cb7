{"paper_id": "ACT", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:41:12.272177Z"}, "title": "Unveiling and Harnessing Hidden Attention Sinks: Enhancing Large Language Models without Training through Attention Calibration", "authors": [{"first": "Zhongzhi", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Georgia Institute of Technology", "location": {}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Georgia Institute of Technology", "location": {}}, "email": ""}, {"first": "Yonggan", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Georgia Institute of Technology", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Shi", "suffix": "", "affiliation": {"laboratory": "", "institution": "Georgia Institute of Technology", "location": {}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Georgia Institute of Technology", "location": {}}, "email": ""}, {"first": "Celine", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Georgia Institute of Technology", "location": {}}, "email": "<<EMAIL>>."}], "year": "", "venue": null, "identifiers": {}, "abstract": "Attention is a fundamental component behind the remarkable achievements of large language models (LLMs). However, our current understanding of the attention mechanism, especially regarding how attention distributions are established, remains limited. Inspired by recent studies that explore the presence of attention sink in the initial token, which receives disproportionately large attention scores despite their lack of semantic importance, this work delves deeper into this phenomenon. We aim to provide a more profound understanding of the existence of attention sinks within LLMs and to uncover ways to enhance the achievable accuracy of LLMs by directly optimizing the attention distributions, without the need for weight finetuning. Specifically, this work begins with comprehensive visualizations of the attention distributions in LLMs during inference across various inputs and tasks. Based on these visualizations, to the best of our knowledge, we are the first to discover that (1) attention sinks occur not only at the start of sequences but also within later tokens of the input, and ( 2) not all attention sinks have a positive impact on the achievable accuracy of LLMs. Building upon our findings, we propose a training-free Attention Calibration Technique (ACT) that automatically optimizes the attention distributions on the fly during inference in an input-adaptive manner. Extensive experiments validate that ACT consistently enhances the accuracy of various LLMs across different applications. Specifically, ACT achieves an average improvement of up to 7.30% in accuracy across different datasets when applied to Llama-30B. Our code is available at https: //github.com/GATECH-EIC/ACT.", "pdf_parse": {"paper_id": "ACT", "_pdf_hash": "", "abstract": [{"text": "Attention is a fundamental component behind the remarkable achievements of large language models (LLMs). However, our current understanding of the attention mechanism, especially regarding how attention distributions are established, remains limited. Inspired by recent studies that explore the presence of attention sink in the initial token, which receives disproportionately large attention scores despite their lack of semantic importance, this work delves deeper into this phenomenon. We aim to provide a more profound understanding of the existence of attention sinks within LLMs and to uncover ways to enhance the achievable accuracy of LLMs by directly optimizing the attention distributions, without the need for weight finetuning. Specifically, this work begins with comprehensive visualizations of the attention distributions in LLMs during inference across various inputs and tasks. Based on these visualizations, to the best of our knowledge, we are the first to discover that (1) attention sinks occur not only at the start of sequences but also within later tokens of the input, and ( 2) not all attention sinks have a positive impact on the achievable accuracy of LLMs. Building upon our findings, we propose a training-free Attention Calibration Technique (ACT) that automatically optimizes the attention distributions on the fly during inference in an input-adaptive manner. Extensive experiments validate that ACT consistently enhances the accuracy of various LLMs across different applications. Specifically, ACT achieves an average improvement of up to 7.30% in accuracy across different datasets when applied to Llama-30B. Our code is available at https: //github.com/GATECH-EIC/ACT.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "In recent days, large language models (LLMs) have garnered significant attention due to their impressive performance across a wide range of tasks (<PERSON><PERSON><PERSON><PERSON> et al., 2023a; b; Ope-nAI, 2023a; <PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023; OpenAI, 2023b) . One of the key components contributing to the remarkable performance of LLMs is the attention mechanism, which effectively identifies relationships among tokens in a sequence. This ability enables LLMs to comprehend intricate contexts and details, greatly enhancing their capacity to process and generate text that closely resembles human language (<PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON> et al., 2018) . However, despite the immense potential of the attention mechanism, our current understanding of how attention distributions are established and their relationship to the achievable performance of LLMs remains inadequately explored.", "cite_spans": [{"start": 146, "end": 169, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023a;", "ref_id": null}, {"start": 170, "end": 172, "text": "b;", "ref_id": null}, {"start": 173, "end": 188, "text": "Ope-nAI, 2023a;", "ref_id": null}, {"start": 189, "end": 211, "text": "<PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF43"}, {"start": 212, "end": 228, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF12"}, {"start": 229, "end": 243, "text": "OpenAI, 2023b)", "ref_id": null}, {"start": 594, "end": 616, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF40"}, {"start": 617, "end": 638, "text": "<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Along this direction, a pioneering study, StreamLLM (<PERSON> et al., 2023) , has undertaken an initial investigation and improved our understanding of attention distributions by uncovering the existence of attention sinks. In particular, they find that the initial token of an input text receives a disproportionately large attention score, despite often lacking semantic significance. This phenomenon arises from the visibility of the initial token to almost all subsequent tokens in autoregressive language modeling, causing them to become the recipients of these \"unnecessary\" attention values. Motivated by the impact of attention sinks on attention distributions, we aim to delve deeper into their general existence to gain a better understanding of how they affect LLMs' reasoning and generation capabilities. This, in turn, will inspire new strategies to enhance the achievable accuracy of LLMs. To achieve this goal, we pose the following three intriguing research questions: Q1: Does an attention sink only exist in the initial token? Q2: Will preserving attention sinks always benefit LLMs' accuracy in different scenarios? Q3: Can we enhance LLMs' accuracy by solely manipulating attention sinks without any weight finetuning?", "cite_spans": [{"start": 52, "end": 71, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In our endeavor to address the aforementioned three questions, we make the following contributions:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We conduct comprehensive visualizations of the attention distributions in LLMs across a variety of tasks and inputs. To the best of our knowledge, we are the first to discover that attention sinks manifest not only in the initial token but also within subsequent tokens throughout the input context. Intriguingly, similar to the attention sink observed in the initial token by (<PERSON> et al., 2023) , attention sinks in later tokens also tend to be concentrated on tokens of less semantic importance.", "cite_spans": [{"start": 379, "end": 398, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• Excited by the above observation, we further probe into the relationship between attention sinks at different locations and the accuracy of the generated content at those respective locations. Interestingly, we discover that not all attention sinks have a positive impact on maintaining LLMs' performance, which complements the findings in (<PERSON> et al., 2023 ). • Leveraging the findings above, we have developed a training-free Attention Calibration Technique, named ACT, that automatically optimizes attention distributions on the fly during inference in an input-adaptive manner, improving the achievable accuracy of pretrained LLMs on downstream tasks. Additionally, it can even lead to a comparable accuracy as compared to the commonly used in-context learning technique, and further be combined with the latter for boosted accuracy. As such, our ACT has provided an alternative new design knob for LLM enhancement. • Extensive experiments and ablation studies validate that our proposed method can achieve up to a 7.30% higher accuracy than the vanilla inference baseline across various tasks. Furthermore, ACT is capable of improving LLMs' performance in challenging multiround conversation tasks. Specifically, applying ACT to different variants of Llama2 boosts the achievable score by up to 0.13 on the challenging MT-Bench dataset.", "cite_spans": [{"start": 342, "end": 360, "text": "(<PERSON> et al., 2023", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Transformer-based language models (<PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2022) have demonstrated their remarkable ability to effectively extract relationships among tokens from complex input sequences, thanks to the utilization of the attention mechanism in their model architecture. Furthermore, their attentioncentric design enables decent scalability (<PERSON> et al., 2023; <PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2023) : as the model size and pretraining dataset scale increase, the performance of transformer-based language models continues to improve. This phenomenon has given rise to the emergence of LLMs. One of the earliest impressive LLMs is GPT-3 (<PERSON> et al., 2020) , which showcases remarkable zero-shot and fewshot in-context learning capabilities. This achievement has further fueled the development of various LLMs, such as OPT (<PERSON> et al., 2022) , Llama (Touvron et al., 2023a) , Llama2 (Touvron et al., 2023b) , BLOOM (Workshop et al., 2022) , GPT-J (Wang & Komatsuzaki, 2021) , Pythia (<PERSON><PERSON><PERSON> et al., 2023) , and GLM (<PERSON> et al., 2021) . These models have further pushed the boundaries of deep learning, gradually moving us toward achieving artificial general intelligence.", "cite_spans": [{"start": 34, "end": 56, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF40"}, {"start": 57, "end": 77, "text": "<PERSON> et al., 2018;", "ref_id": "BIBREF9"}, {"start": 78, "end": 98, "text": "<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF29"}, {"start": 99, "end": 120, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF32"}, {"start": 396, "end": 414, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF27"}, {"start": 415, "end": 435, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF17"}, {"start": 436, "end": 458, "text": "<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF0"}, {"start": 696, "end": 716, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF2"}, {"start": 883, "end": 903, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF56"}, {"start": 912, "end": 935, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}, {"start": 945, "end": 968, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}, {"start": 977, "end": 1000, "text": "(Workshop et al., 2022)", "ref_id": "BIBREF46"}, {"start": 1009, "end": 1035, "text": "(Wang & Komatsuzaki, 2021)", "ref_id": "BIBREF45"}, {"start": 1045, "end": 1068, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF0"}, {"start": 1079, "end": 1096, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "Large language models", "sec_num": "2.1."}, {"text": "Despite the promising zero-shot and few-shot capabilities of LLMs, one common approach to achieving strong performance in real-world applications is to finetune pretrained LLMs for downstream tasks. However, the enormous size of LLMs makes traditional weight tuning computationally expensive, requiring significant storage and memory overhead.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Parameter-efficient tuning", "sec_num": "2.2."}, {"text": "To address this challenge, various parameter-efficient tuning (PET) methods have been proposed (<PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON> et al., 2020; <PERSON> et al., 2022; <PERSON> et al., 2023a; <PERSON> et al., 2022) . Specifically, instead of updating all parameters in the target LLM, PET selectively updates a small set of learnable modules during finetuning (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2024; <PERSON> et al., 2024; <PERSON> et al., 2023b; 2024; <PERSON> et al., 2023a; <PERSON> et al., 2023) . While PET methods can reduce computational, storage, and memory overheads, even state-of-the-art (SOTA) PET methods still face challenges in efficiently finetuning LLMs (<PERSON><PERSON><PERSON> et al., 2023) . Our proposed method is orthogonal to PET: we aim to enhance the performance of LLMs by directly optimizing attention distributions on the fly during inference, eliminating the need for weight finetuning.", "cite_spans": [{"start": 95, "end": 112, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF15"}, {"start": 113, "end": 133, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF19"}, {"start": 134, "end": 153, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF53"}, {"start": 154, "end": 172, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF36"}, {"start": 173, "end": 190, "text": "<PERSON> et al., 2023a;", "ref_id": null}, {"start": 191, "end": 207, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF11"}, {"start": 353, "end": 370, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF26"}, {"start": 371, "end": 388, "text": "<PERSON><PERSON> et al., 2024;", "ref_id": "BIBREF47"}, {"start": 389, "end": 407, "text": "<PERSON> et al., 2024;", "ref_id": "BIBREF58"}, {"start": 408, "end": 425, "text": "<PERSON> et al., 2023b;", "ref_id": null}, {"start": 426, "end": 431, "text": "2024;", "ref_id": null}, {"start": 432, "end": 452, "text": "<PERSON> et al., 2023a;", "ref_id": null}, {"start": 453, "end": 469, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF20"}, {"start": 641, "end": 664, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "Parameter-efficient tuning", "sec_num": "2.2."}, {"text": "Despite being one of the key components of LLMs, the understanding of the attention mechanism has been slow to evolve compared to the rapid advancement of LLMs themselves. Early works focus on studying attention in small-scale transformers. For instance, (<PERSON> et al., 2019b) visualizes specific types of attention patterns in pretrained BERT (<PERSON> et al., 2018) , and (<PERSON><PERSON>, 2019) identifies biases and localized relevant attention heads. Additionally, (Sun & Lu, 2020) discovers that the degree of association between a word token and a class label affects their attention score. However, the exploration of the unique attention distribution in LLMs with larger model sizes and datasets is still in its infancy. Along this trajectory, some pioneering works have made interesting observations related to the attention mechanism in LLMs. For instance, (<PERSON><PERSON> et al., 2023) finds that the attention distribution in LLMs differs from that in humans, and (<PERSON> et al., 2023b) observes that increasing the attention score of manually defined tokens at specific heads can improve LLMs' ability to follow instructions. However, determining the relationship between attention distributions and the achievable performance of LLMs, as well as automating the enhancement of LLMs' performance by calibrating attention distributions during inference, still remain open challenges. ", "cite_spans": [{"start": 255, "end": 276, "text": "(<PERSON> et al., 2019b)", "ref_id": null}, {"start": 344, "end": 365, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF9"}, {"start": 372, "end": 383, "text": "(<PERSON>ig, 2019)", "ref_id": "BIBREF41"}, {"start": 456, "end": 472, "text": "(Sun & Lu, 2020)", "ref_id": "BIBREF35"}, {"start": 854, "end": 872, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF18"}, {"start": 952, "end": 973, "text": "(<PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Observations regarding LLMs' attention", "sec_num": "2.3."}, {"text": "LLMs and multi-head attention. LLMs (<PERSON><PERSON><PERSON><PERSON> et al., 2023a; <PERSON> et al., 2020; OpenAI, 2023b) are typically constructed by stacking L transformer blocks, each comprising a feed-forward network (FFN) and a multi-head attention (MHA) module that captures the pairwise relationships among all N input tokens in the input sequence. Specifically, for a given input X l ∈ R N ×d to the l-th block, the output feature F l h ∈ R N ×d generated at head h can be represented as:", "cite_spans": [{"start": 36, "end": 59, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023a;", "ref_id": null}, {"start": 60, "end": 79, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF2"}, {"start": 80, "end": 94, "text": "OpenAI, 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "A l h = Softmax f l Q (X l ) • f l K (X l ) T √ d k , F l h = A l h • f l V (X l ),", "eq_num": "(1)"}], "section": "Preliminaries", "sec_num": "3."}, {"text": "where f l Q , f l K , and f l V are projection layers, d k = d/h is the embedding dimension of each head, and A l h ∈ R N ×N is the attention map generated at head h. Each element A l h [i, j] represents the relationship between the i-th and j-th tokens in X l . The attention score is defined as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "a l h = [ i j=1 A l h [i, j]/i, ∀i ∈ {1, • • • , N }]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": ", and a l h [i] denotes the attention score for the i-th token at head h, layer l.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "Next, the features F l h of each head h are combined to generate the output O l of MHA by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "O l = f l O (Concat(F l 1 , • • • , F l h )),", "eq_num": "(2)"}], "section": "Preliminaries", "sec_num": "3."}, {"text": "where f l O represents a projection layer. In the remainder of this paper, we primarily utilize the distribution of A l h generated by various inputs X l for all h and l within the LLM as the key knob to address the three research questions outlined in Sec. 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "StreamLLM and the attention sink. <PERSON>LLM (<PERSON> et al., 2023) identifies the presence of an attention sink, which is a token that receives a significantly higher attention score than other tokens but provides limited semantic information. StreamLLM observes that the attention sink only exists in the initial token and suggests always preserving these tokens when processing long input sequences to prevent forgetting.", "cite_spans": [{"start": 44, "end": 63, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "Overview. We aim to investigate the general existence of attention sinks and explore their impact on the reasoning and generation process of LLMs. To achieve this goal, we adopt a deductive approach by sequentially addressing three intriguing research questions outlined in Sec. 1: Firstly, we address Q1 to investigate whether attention sinks are limited to the initial token or if they persist in various locations, as discussed in Sec. 4.1. Secondly, we explore Q2 to shed light on the effects of these identified attention sinks on the achievable accuracy of LLMs, as discussed in Sec. 4.2. Finally, building upon the findings gained from Q1 and Q2, we address Q3 by developing the ACT to enhance the performance of LLMs in a training-free manner during inference, as discussed in Sec. 4.3. Unless otherwise specified, for the remainder of this section, our exploration is based on one of the SOTA LLMs, Llama2-7B-chat (<PERSON><PERSON><PERSON><PERSON> et al., 2023b) . 4.1. Q1: Do attention sinks only exist in the initial token?", "cite_spans": [{"start": 923, "end": 946, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Unveil and Harness Hidden Attention Sinks", "sec_num": "4."}, {"text": "The attention sink has been observed at the initial token of LLMs (<PERSON> et al., 2023) . However, the presence and distribution of attention sinks in later tokens remain an open yet crucial question, especially considering that these tokens contain ample semantic information. Therefore, our objective is to investigate the overall existence of attention sinks that consistently draw significant attention across the entire input sequence.", "cite_spans": [{"start": 66, "end": 85, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "Unveil and Harness Hidden Attention Sinks", "sec_num": "4."}, {"text": "Settings. To address Q1, we first visualize two metrics:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unveil and Harness Hidden Attention Sinks", "sec_num": "4."}, {"text": "(1) the averaged attention maps across all heads and layers, denoted as (  and  (2) the averaged attention maps of each layer, i.e., (", "cite_spans": [], "ref_spans": [{"start": 74, "end": 74, "text": "", "ref_id": null}, {"start": 79, "end": 79, "text": "", "ref_id": null}], "eq_spans": [], "section": "Unveil and Harness Hidden Attention Sinks", "sec_num": "4."}, {"text": "H h=1 L l=1 A l h )/(H • L), on different datasets,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unveil and Harness Hidden Attention Sinks", "sec_num": "4."}, {"text": "H h=1 A l h )/H),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unveil and Harness Hidden Attention Sinks", "sec_num": "4."}, {"text": "when processing a single input sample, as illustrated in Fig. 1 . Additional visualizations on various datasets and models can be found in Appendix C. To generalize these observations across a larger range of datasets, we first visualize the distribution of token-wise attention scores across different datasets to validate the significant gap between high-attention and normal tokens. We further determine that the i-th token has a significantly higher attention score if a l h [i] > α/N (i.e., more than α times the average attention score) and is considered an attention sink. Specifically, we set α = 5 based on our upcoming visualization in Fig. 2 unless otherwise specified. We summarize the frequency of tokens exhibiting significantly higher attention scores across all samples in a mixed dataset comprising 100 samples collected from each of the 18 datasets mentioned in Sec. 5.1.", "cite_spans": [], "ref_spans": [{"start": 62, "end": 63, "text": "1", "ref_id": "FIGREF0"}, {"start": 651, "end": 652, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Unveil and Harness Hidden Attention Sinks", "sec_num": "4."}, {"text": "Observations. We can draw the following observations from Fig. 1 : Obs-(1) several tokens consistently attract significantly higher attention values than other tokens. Moreover, as visualized in Fig. 2 , the distribution of highattention tokens' attention values has a notable boundary with those of other tokens across different datasets, validating that the difference in attention scores between identified high-attention tokens and other tokens is significant; Obs-(2) as illustrated in Table 1 , aside from the initial token <S>, which corresponds exactly to the attention sink observed in StreamLLM (<PERSON> et al., 2023) , there also exist a nontrivial number of other attention sinks that contain limited semantic information (e.g., \".\", \":\", and \"< 0x0A >\"), yet frequently draw significantly higher attention scores at vari- Our answer to Q1. In complement to the observations made in StreamLLM, we conclude that attention sinks are found not only in the initial token but also in later tokens, particularly during the intermediate layers of LLMs.", "cite_spans": [{"start": 605, "end": 624, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF48"}], "ref_spans": [{"start": 63, "end": 64, "text": "1", "ref_id": "FIGREF0"}, {"start": 200, "end": 201, "text": "2", "ref_id": "FIGREF1"}, {"start": 497, "end": 498, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Unveil and Harness Hidden Attention Sinks", "sec_num": "4."}, {"text": "LLMs' accuracy in different scenarios?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "Considering that the newly identified attention sinks in later tokens, with their substantial attention values, divert a significant portion of attention away from other non-attentionsink tokens, it is imperative to investigate the impact of this notable diversion on the reasoning and generation capabilities of LLMs. While StreamLLM (<PERSON> et al., 2023) suggests preserving the attention sink of the initial token, it remains unclear whether preserving later attention sinks also enhances the accuracy of LLMs. Therefore, in this subsection, we delve into the impact of attention sinks on LLMs' accuracy in downstream tasks.", "cite_spans": [{"start": 335, "end": 354, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "Settings. We make a heuristic attempt to verify the influence of attention sinks by decreasing the attention scores of each attention head associated with attention sinks and examining whether this can enhance the accuracy achieved by LLMs on the MMLU dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2020) . Taking into account the various layer-wise attention patterns discussed in Sec. 4.1-Obs-(3), we only apply this operation to attention heads between the third layer and the second-to-last layer.", "cite_spans": [{"start": 260, "end": 284, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "To effectively reduce the attention scores of attention sink tokens and leverage the reduced attention scores to improve the achievable performance of the target LLM by distribut-ing them across other tokens, we propose a simple calibration technique comprising three steps:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "1. Identify a set of attention sink tokens ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "S l h = {t ∈ {1, • • • , T } | a l h [t] > α • 1/N },", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "[k, s] = A l h [k, s] × β for all s ∈ S l", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "h for each row k in the attention map A l h , where β is a hyperparameter controlling the extent to which we want to eliminate the excessive attention scores of attention sinks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "3. To leverage the reduced attention scores, we propose to maintain the target LLM's original attention distribution to preserve token-wise relationships while slightly increasing the attention scores to enforce greater focus on the semantic information of non-attention sink tokens by setting", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "Âl h [k, s] = A l h [k, t] + ( s∈S l h Âl h [k, s] -A l h [k, s]) × A l h [k, t] i∈1,••• ,T -S l h A l h [k, i]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "for all s / ∈ S l h , which ensures that the sum of each row k remains one.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "Observations. As demonstrated in Fig. 3 , we can make two observations: Obs-(1) despite the simplicity of the calibration technique we propose, in more than 76.8% of cases, the LLM after attention calibration can achieve better accuracy compared to the vanilla inference baseline; and Obs-( 2) not all heads can benefit from the calibration, for instance, calibrating certain heads can result in an accuracy drop as significant as 0.39%.", "cite_spans": [], "ref_spans": [{"start": 38, "end": 39, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "Our answer to Q2. In contrast to the observation made in StreamLLM (<PERSON> et al., 2023) that suggests preserving attention sinks to enhance LLMs' achievable accuracy, we highlight that not all attention sinks are beneficial for LLMs. Specifically, for the majority of attention sinks occurring in the middle or later parts of inputs, reducing their attention scores can result in improved accuracy. We suspect this is because frequently occurring attention sinks excessively divert attention and reducing them can effectively allocate more attention to tokens with richer semantic information.", "cite_spans": [{"start": 67, "end": 86, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "Q2: Will preserving attention sinks always benefit", "sec_num": "4.2."}, {"text": "The observations in Sec. 4.2 highlight the potential for enhancing LLMs' achievable accuracy by simply calibrating attention sinks in specific heads, even without fine-tuning. This introduces a new design parameter for improving LLMs' accuracy. However, the challenge lies in identifying the heads that require calibration, especially given that improperly reducing attention sinks in certain heads can significantly degrade LLMs' accuracy. Therefore, the remaining research question pertains to developing a technique that can automatically identify and calibrate attention sinks in the appropriate heads to enhance LLM accuracy. Our solution to addressing Q3. To enhance LLMs' accuracy without the need for finetuning, by directly optimizing attention sinks, we introduce an effective and low-cost attention calibration technique, dubbed ACT. ACT first filters out the heads that need to preserve all the corresponding attention sinks they process offline and then calibrates the attention in the remaining heads during inference.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q3: Can we enhance LLMs' accuracy by solely manipulating attention sinks without finetuning?", "sec_num": "4.3."}, {"text": "Specifically, in the first head filtering step, we aim to determine the set of attention heads that need to preserve all the processed attention sinks, meaning that these heads should not undergo any attention calibration during inference. This filtering process can be formally described as follows: For each task", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q3: Can we enhance LLMs' accuracy by solely manipulating attention sinks without finetuning?", "sec_num": "4.3."}, {"text": "T = {D 1 , • • • , D Q }, consisting of Q different datasets,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q3: Can we enhance LLMs' accuracy by solely manipulating attention sinks without finetuning?", "sec_num": "4.3."}, {"text": "we initially create a small held-out dataset C by uniformly sampling data samples from each dataset D q ∈ T , ensuring that ∥C ∩ D q ∥ = M, ∀D q ∈ T (i.e., each dataset D q has M samples in C). Next, we execute the attention calibration steps as proposed in Sec. 4.2, individually on each attention head, and evaluate the resulting performance on the held-out dataset C. Finally, we can identify a set of heads H that can enhance the accuracy of the target LLM after the calibration process.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q3: Can we enhance LLMs' accuracy by solely manipulating attention sinks without finetuning?", "sec_num": "4.3."}, {"text": "In the second attention calibration step, we calibrate all a l h [t] ∀(l, h) ∈ H on the fly during inference in an inputadaptive manner, leveraging the proposed attention calibration steps in Sec. 4.2 to reduce excessive attention at attention sinks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q3: Can we enhance LLMs' accuracy by solely manipulating attention sinks without finetuning?", "sec_num": "4.3."}, {"text": "Models, tasks, and datasets. Models: We evaluate ACT on seven models, including Llama2-7B/13B-chat (Tou- (<PERSON> et al., 2023) , Llama-30B (<PERSON><PERSON><PERSON><PERSON> et al., 2023a) , GPT-J-6B (Wang & Ko<PERSON>zaki, 2021) , OPT-2.7B (<PERSON> et al., 2022) , and Vicuna-7B (<PERSON> et al., 2023) . Tasks and datasets: To provide a thorough evaluation of ACT, we benchmark ACT on three types of commonly used tasks with 18 different datasets, including Hellaswag (Zellers et al., 2019) , ARCE (<PERSON> et al., 2018) , PIQA (<PERSON><PERSON> et al., 2020) , OB (<PERSON><PERSON><PERSON> et al., 2018) , ARCC (<PERSON> et al., 2018) , COPA (<PERSON> et al., 2019) , CQA (<PERSON><PERSON><PERSON> et al., 2018) , and MMLU (<PERSON><PERSON><PERSON><PERSON> et al., 2020) for domain-specific multiple-choice; SST2 (<PERSON><PERSON> et al., 2013) , SST5 (<PERSON><PERSON> et al., 2013) , MR (Pang & Lee, 2005) , AGNews (<PERSON> et al., 2015) , TREC (Voorhees & Tice, 2000) Baselines and evaluation metrics. Baselines: We benchmark ACT against the vanilla inference baseline under different shot settings, including zero-shot and 1/3/5-shot incontext learning as the baseline settings. Evaluation metrics:", "cite_spans": [{"start": 105, "end": 125, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF16"}, {"start": 138, "end": 161, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}, {"start": 173, "end": 199, "text": "(Wang & Komatsuzaki, 2021)", "ref_id": "BIBREF45"}, {"start": 211, "end": 231, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF56"}, {"start": 248, "end": 269, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF3"}, {"start": 436, "end": 458, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF52"}, {"start": 466, "end": 486, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF6"}, {"start": 494, "end": 513, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF1"}, {"start": 519, "end": 542, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF21"}, {"start": 550, "end": 570, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF6"}, {"start": 578, "end": 597, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF44"}, {"start": 604, "end": 625, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF37"}, {"start": 637, "end": 661, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF14"}, {"start": 704, "end": 725, "text": "(<PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF34"}, {"start": 733, "end": 754, "text": "(<PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF34"}, {"start": 760, "end": 778, "text": "(<PERSON><PERSON> & <PERSON>, 2005)", "ref_id": "BIBREF25"}, {"start": 788, "end": 808, "text": "(<PERSON> et al., 2015)", "ref_id": "BIBREF57"}, {"start": 816, "end": 839, "text": "(Voorhees & Tice, 2000)", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "Evaluation settings", "sec_num": "5.1."}, {"text": "We use accuracy as the metric for domain-specific multiple choice and text classification tasks, and F1 score with exact match score for the open-ended question-answering task.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Evaluation settings", "sec_num": "5.1."}, {"text": "Implementation details. We implement our ACT framework on top of PyTorch and Huggingface. For all datasets, we use the standard prompting template provided in (<PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2021; <PERSON><PERSON> et al., 2022) . Detailed prompts we used can be found in Appendix B. In all our experiments, unless otherwise specified, we use β = 0.4 and ∥C∥ = 1000×Q, which is less than 10% of the size of the validation datasets. During head filtering, regardless of the number of shots we evaluate, we only perform head filtering with samples using zero-shot prompts.", "cite_spans": [{"start": 159, "end": 180, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF24"}, {"start": 181, "end": 199, "text": "<PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF33"}, {"start": 200, "end": 217, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF13"}], "ref_spans": [], "eq_spans": [], "section": "Evaluation settings", "sec_num": "5.1."}, {"text": "Domain-specific multiple choice. We first validate ACT on a set of commonly used domain-specific multiplechoice datasets under different settings as shown in Table 2. ACT on average achieves an accuracy improvement of 0.30%∼7.30% across different models and numbers of shots. The accuracy improvement can be as high as 13.26% on a single dataset (i.e., leveraging ACT to boost Llama-30B on Hellaswag (<PERSON><PERSON><PERSON> et al., 2019) under the zero-shot setting), and applying ACT for PIQA (<PERSON><PERSON> et al., 2020) under a zero-shot setting can achieve a 1.96% higher accuracy than the vanilla inference baseline under the 5-shot in-context learning setting. Moreover, it is worth noticing that ACT has a strong ability to adapt to different evaluation settings. Specifically, although ACT only performs head filtering using samples with a zero-shot setting, ACT not only achieves average accuracy improvements of 1.72% and 2.61% when applied to Llama2-7B-chat and Llama2-13B-chat under the zero-shot setting, respectively, but also achieves average accuracy improvements of 1.26%, 0.66%, and 0.65% when enhancing the two models under 1/3/5-shots, respectively.", "cite_spans": [{"start": 400, "end": 422, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF52"}, {"start": 479, "end": 498, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "Enhancing LLM accuracy with ACT", "sec_num": "5.2."}, {"text": "To further validate ACT's versatility and effectiveness in enhancing the performance of different types of LLMs, we apply ACT to four different kinds of LLMs including Llama2-7B-chat (<PERSON><PERSON><PERSON><PERSON> et al., 2023b) , GPT-J-6B (Wang & Komatsuzaki, 2021) , OPT-2.7B (<PERSON> et al., 2022) , and Vicuna-7B (<PERSON> et al., 2023) , and evaluate their achieved accuracy on the representative MMLU dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2020) . As shown in Table 3 , despite different model se- lections, ACT consistently achieves a 0.32%∼1.09% higher accuracy over the vanilla inference baseline, proving that our proposed ACT is a general framework capable of enhancing the performance of different kinds of LLMs despite their pretraining processes, finetuning techniques, model structures, and model sizes.", "cite_spans": [{"start": 183, "end": 206, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}, {"start": 218, "end": 244, "text": "(Wang & Komatsuzaki, 2021)", "ref_id": "BIBREF45"}, {"start": 256, "end": 276, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF56"}, {"start": 293, "end": 314, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF3"}, {"start": 389, "end": 413, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF14"}], "ref_spans": [{"start": 434, "end": 435, "text": "3", "ref_id": "TABREF4"}], "eq_spans": [], "section": "Enhancing LLM accuracy with ACT", "sec_num": "5.2."}, {"text": "Text classification. We further validate ACT on a set of text classification datasets under different numbers of shots and across Llama2-7B/13B-chat as shown in Table 4 . ACT shows consistent accuracy improvement over the vanilla inference baseline across different numbers of shots, datasets, and models. Under the zero-shot setting, ACT achieves average accuracy improvements of 1.29%, 3.16%, 0.74%, and 5.12% for Llama2-7B-chat, Llama2-13B-chat, Mistral-7B, and Llama-30B, respectively. Remarkably, the application of ACT leads to a peak accuracy improvement of 16.16% when boosting the Llama-30B model on the AG-News dataset (<PERSON> et al., 2015) under the zero-shot condition. This set of experiments further validates the robustness of ACT in transferring between different validation scenarios. Despite its primary application of head filtering in the zero-shot scenario, ACT not only procures average accuracy improvements of 1.29% and 3.16% with the Llama2-7B-chat and Llama2-13B-chat models, respectively, under zero-shot conditions, but also facilitates average accuracy gains of 1.38%, 1.43%, and 0.58% across 1-shot, 3-shot, and 5-shot settings for the Llama2-7B-chat. Similarly, for the Llama2-13B-chat model, ACT achieves average accuracy enhancements of 1.37%, 1.21%, and 0.64% across the 1-shot, 3-shot, and 5-shot configurations, respectively.", "cite_spans": [{"start": 629, "end": 649, "text": "(<PERSON> et al., 2015)", "ref_id": "BIBREF57"}], "ref_spans": [{"start": 167, "end": 168, "text": "4", "ref_id": "TABREF5"}], "eq_spans": [], "section": "Enhancing LLM accuracy with ACT", "sec_num": "5.2."}, {"text": "Open-ended question-answering. To better validate ACT's ability to enhance LLM accuracy across different application scenarios, we further evaluate our proposed ACT performance on open-ended question-answering task using widely used SQuADv1 (<PERSON><PERSON><PERSON> et al., 2016) and SQuADv2 (<PERSON><PERSON><PERSON> et al., 2018) datasets, and a more challenging multi-round conversation dataset from MT-Bench (<PERSON> et al., 2023) . As shown in Table 5 , ACT consistently achieves superior performance in all metrics of MT-Bench and SQuAD v1/v2 compared to vanilla inference. Specifically, ACT achieves a 0.088∼0.134 higher MT-Bench score, a 1.73∼10.14 higher exact match score, and a 1.13∼16.42 higher F1 score over the benchmarked vanilla LLMs, respectively. It is also worth noting that an improvement of 0.088∼0.134 on MT-Bench achieved by ACT is non-trivial. The difference in MT-Bench scores between Llama2-7B-chat and Llama2-13B-chat is 0.38, while the difference between Llama2-13B-chat and Llama2-70B-chat is 0.21. This suggests that applying ACT can mitigate around one-third of the difference between a smaller model and its larger counterpart. This proves that for the more complicated autoregressive generation task, the phenomenon that attention sinks appears in the middle part of the input sequence and draws an excessive amount of attention, sabotaging the achievable performance of LLMs still exists. Moreover, using our proposed ACT can calibrate the attention and enhance the generation quality of LLMs.", "cite_spans": [{"start": 241, "end": 265, "text": "(<PERSON><PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF30"}, {"start": 278, "end": 302, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF31"}, {"start": 383, "end": 403, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF59"}], "ref_spans": [{"start": 424, "end": 425, "text": "5", "ref_id": "TABREF6"}], "eq_spans": [], "section": "Enhancing LLM accuracy with ACT", "sec_num": "5.2."}, {"text": "Ways to calibrate attention. We further validate whether our answers to Q2 in Sec. 4.2 and Q3 in Sec. 4.3 is correct. Specifically, we assess whether reducing the attention score at attention sinks helps improve LLM performance. To this end, we evaluate the performance of our method against three other methods: (1) Temp, which directly applies a temperature θ = 1.1 to all tokens except the attention sink at the initial token;", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "(2) Inv-temp, similar to temp but with θ = 1/1.1; and (3) Inv-ours, the inverse process of our proposed method, which reduces the attention value of other tokens and redistributes it to the attention sink. As shown in Table 6 : (1) Our method achieves better results on MMLU compared to Temp. We attribute this improvement to our method's superior ability to preserve the original attention distribution across other tokens.", "cite_spans": [], "ref_spans": [{"start": 224, "end": 225, "text": "6", "ref_id": "TABREF7"}], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "(2) Inv-temp and inv-ours perform worse than temp and our method, respectively, on MMLU, indicating the importance of reducing the attention values of attention sinks in the middle part of the input.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "Ways to distribute the additional attention. After reducing the excessive attention value at attention sinks, how to distribute them across other tokens is an important question. Considering that the input of the multiple-choice dataset MMLU consists of a question and a set of choices, we evaluate three different ways to distribute the additional attention on MMLU: (1) uniform, where we uniformly distribute the additional attention across all tokens;", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "(2) question-only, where we apply the additional attention only to the tokens corresponding to the questions; and (3) choice-only, where we apply the additional attention only to the tokens corresponding to the provided choices. As shown in Table 7 , we observe that distributing attention to all tokens (i.e., uniform and our method) is important for preserving performance.", "cite_spans": [], "ref_spans": [{"start": 247, "end": 248, "text": "7", "ref_id": "TABREF8"}], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "We suspect this is because drastically changing the attention distribution across too many tokens should be avoided.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "α selection. α defines the criteria of attention sink in ACT.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "In this paper, we empirically set α = 5 based on the visualization of the attention score distribution across different tokens, as shown in Fig. 2 . To better understand the robustness of ACT across different selections of α, we test ACT under the zero-shot setting with Llama2-7B-chat using various values of α. As shown in Table 8 , despite different selections of α, ACT consistently achieves similar performance with a steady 1.24%∼1.29% higher average accuracy than the vanilla Llama2-7B-chat baseline. This demonstrates that the attention sinks identified in our work have distinct values compared to other non-attention sink tokens, and thus, the selection of α plays a minor role in the performance of ACT.", "cite_spans": [], "ref_spans": [{"start": 145, "end": 146, "text": "2", "ref_id": "FIGREF1"}, {"start": 331, "end": 332, "text": "8", "ref_id": "TABREF9"}], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "β selection. β determines how drastically we want to reduce the attention sinks that occur in the middle of the input.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "In this paper, we set β = 0.4, but we want to explore the impact of β selection on the final achieved accuracy on MMLU with Llama2-7B-chat. As shown in Table 9 , despite different selections of β result in varied accuracies, they all achieve better accuracies than the vanilla inference baseline, showing ACT is robust to different hyperparameter selections.", "cite_spans": [], "ref_spans": [{"start": 158, "end": 159, "text": "9", "ref_id": "TABREF10"}], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "Size of C. We ablate the appropriate size of ∥C∥, which controls nearly the only source of overhead in ACT. We ablate different selections of ∥C∥ by sampling different numbers of samples in each D q ∈ Q (i.e., ∥C∥/Q) and evaluating their achieved accuracy on the MMLU dataset using Llama2-7Bchat. As shown in Table 10 , a larger C helps with ACT's performance, but when ∥C∥/Q scales up to around 1000 (e.g., more than 10 times smaller than the validation dataset), the further performance improvement is marginal.", "cite_spans": [], "ref_spans": [{"start": 315, "end": 317, "text": "10", "ref_id": "TABREF11"}], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "Number of heads to calibrate. To verify whether the performance improvement achieved by calibrating each individual attention head as in Fig. 3 can be accumulated, we validate ACT's performance when calibrating on subsets of H of different sizes. As shown in Table 11 , the achieved performance of attention calibration gradually increases as the size of the subsets increases, validating that the effectiveness of calibrating each attention head in H can be accumulated and that calibrating all heads in H leads to optimal performance.", "cite_spans": [], "ref_spans": [{"start": 142, "end": 143, "text": "3", "ref_id": "FIGREF2"}, {"start": 265, "end": 267, "text": "11", "ref_id": "TABREF12"}], "eq_spans": [], "section": "Ablation studies", "sec_num": "5.3."}, {"text": "To better understand the role of our proposed ACT in reducing the excessive attention at attention sinks in the middle of inputs, we further visualize the attention map of Llama2-7B-chat before and after performing ACT with the same input sample. As shown in Fig. 4 , after performing ACT, the original attention sink that occurs in the middle of the input sequence is almost eliminated, while the attention distribution of other tokens remains the same.", "cite_spans": [], "ref_spans": [{"start": 264, "end": 265, "text": "4", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Attention map visualization before and after ACT", "sec_num": "5.4."}, {"text": "In this paper, we conduct comprehensive visualizations of the attention distributions in LLMs during inference across various inputs and tasks. Based on these visualizations, for the first time, we discover that (1) attention sinks occur not only at the start of sequences but also within later tokens of the input, and (2) not all attention sinks have a positive impact on the achievable accuracy of LLMs. Building upon our findings, we propose a training-free technique, dubbed ACT, that automatically optimizes the attention distributions on the fly during inference in an input-adaptive manner. Extensive experiments validate that ACT consistently enhances the accuracy of various LLMs across different applications.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "The recent advancements in LLMs have triggered various application scenarios that require an affordable LLM with superior performance to serve as a backbone. This calls for (1) LLMs with better performance under comparable computation costs and (2) a better understanding of the behavior of LLMs, facilitating a trustworthy generation process. In this paper, we cater to both of the aforementioned calls.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "For (1), our proposed ACT can improve the performance of LLMs on downstream tasks not only in a training-free manner but also with almost no additional inference cost.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "The proposed ACT leverages the design knob on attention manipulation, which is also orthogonal to most techniques improving the performance of LLMs, such as in-context learning, prompting, and finetuning, making ACT a generally applicable technique.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "For (2), we have conducted comprehensive visualization and analysis of the attention generated by LLMs during inference with different inputs from various tasks. Moreover, to the best of our knowledge, we are the first to discover that attention sinks manifest not only in the initial token but also in subsequent tokens throughout the input context. This observation deepens our understanding of the intrinsic mechanism of LLMs and thus can potentially facilitate the trustworthy generation process.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "To better understand the attention sink distribution, we profile all of the attention sink that occurred during inference with Llama2-7B-chat on all 17 datasets mentioned in Sec. 5.1. As shown in Fig. 5 , despite the attention sink at the initial token occurring the most frequently, there are many other positions that are prone to have attention sink, further proving the wide existence of attention sink phenomenon throughout the input sequence. ", "cite_spans": [], "ref_spans": [{"start": 201, "end": 202, "text": "5", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "<PERSON><PERSON> on the position of attention sinks", "sec_num": null}, {"text": "Here, we list all the prompts we used in this paper on different datasets:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Prompts used for each dataset", "sec_num": null}, {"text": "For multiple choice task (i.e., on hellaswag, ARCE, PIQA, OB, ARCC, COPA, CQA datasets), we use the following prompt:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Prompts used for each dataset", "sec_num": null}, {"text": "• \"Complete the following sentence with an appropriate ending. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Prompts used for each dataset", "sec_num": null}], "back_matter": [{"text": "This work is supported by the National Science Foundation (NSF) through the CCRI funding (Award number: 2016727) and CoCoSys, one of seven centers in JUMP 2.0, a Semiconductor Research Corporation (SRC) program sponsored by DARPA.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "For text classification, we use different prompts for different datasets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "-\"Classify the sentiment of the user's message into one of the following categories:'positive' or 'negative'.-Sentence: <sentence> Sentiment: \"• SST5:-\"Classify the sentiment of the user's message into one of the following categories:'terrible', 'negative', 'neutral', 'positive', or 'great'.-Sentence: <sentence> Sentiment: \"• MR:-\"Classify the sentiment of the movie's review into one of the following categories:'positive' or 'negative'.-Review: <sentence> Sentiment: \"• AGNews:-\"Classify the news articles into the categories of 'World', 'Sports', 'Business', or 'Technology'.-Article: <sentence> Category: \"• TREC:-\"Classify the given questions into the following categories of 'Description', 'Entity', 'Expression', 'Person', 'Number', or 'Location'.-Question: <sentence> Type: \"• CB:-\"Read the following paragraph and determine if the hypothesis is true.-Premise: <premise> Hypothesis: <hypothesis>. Answer: \"• BoolQ:-\"Read the text and answer the question by True or False.-Text: <passage> Question: <question>? Answer: \"For open-ended question answering (i.e., SQuADv1/v2), we use the following prompt:• Answer question using information in the preceding background paragraph. If there is not enough information provided, answer with \"Not in background.\" C. More visualizations on attention mapsWe conduct more visualization on different LLMs as shown in Fig. 6 , Fig. 7 , and Fig. 8 for Llama2-7B-chat, Vicuna-7B, and OPT-2.7B, respectively.", "cite_spans": [], "ref_spans": [{"start": 1369, "end": 1370, "text": "6", "ref_id": null}, {"start": 1378, "end": 1379, "text": "7", "ref_id": null}, {"start": 1391, "end": 1392, "text": "8", "ref_id": null}], "eq_spans": [], "section": "• SST2:", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Pythia: A suite for analyzing large language models across training and scaling", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": ["G"], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>'brien", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "U", "middle": ["S"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "2397--2430", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, S<PERSON>, <PERSON>, U. S., <PERSON>, <PERSON>, et al. Pythia: A suite for analyzing large language models across training and scal- ing. In International Conference on Machine Learning, pp. 2397-2430. PMLR, 2023.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "<PERSON><PERSON>: Reasoning about physical commonsense in natural language", "authors": [{"first": "Y", "middle": [], "last": "Bisk", "suffix": ""}, {"first": "R", "middle": [], "last": "Zellers", "suffix": ""}, {"first": "J", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "34", "issue": "", "pages": "7432--7439", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. <PERSON>: Reason- ing about physical commonsense in natural language. In Proceedings of the AAAI conference on artificial intelligence, volume 34, pp. 7432-7439, 2020.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Language models are few-shot learners", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Subbiah", "suffix": ""}, {"first": "J", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "1877--1901", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Language models are few-shot learners. Advances in neural information processing systems, 33: 1877-1901, 2020.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "An open-source chatbot impressing gpt-4 with 90%* chatgpt quality", "authors": [{"first": "W.-L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Stoica", "suffix": ""}, {"first": "E", "middle": ["P"], "last": "Xi<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Vicuna", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Stoica, I., and Xi<PERSON>, E. P. <PERSON>: An open-source chatbot impressing gpt-4 with 90%* chatgpt quality, March 2023. URL https://lmsys.org/blog/ 2023-03-30-vicuna/.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Exploring the surprising difficulty of natural yes/no questions", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M.-W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Boolq", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1905.10044"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: Exploring the surpris- ing difficulty of natural yes/no questions. arXiv preprint arXiv:1905.10044, 2019a.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "What does bert look at? an analysis of bert's attention", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "U", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["D"], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1906.04341"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, C. D. What does bert look at? an analysis of bert's attention. arXiv preprint arXiv:1906.04341, 2019b.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Think you have solved question answering? try arc, the ai2 reasoning challenge", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "K<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "Tafjord", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1803.05457"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Schoenick, C., and Tafjord, O. Think you have solved question answering? try arc, the ai2 reasoning challenge. arXiv preprint arXiv:1803.05457, 2018.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "The commitmentbank: Investigating projection in naturally occurring discourse", "authors": [{"first": "M.-C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "proceedings of Sinn und Bedeutung", "volume": "23", "issue": "", "pages": "107--124", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. The commitmentbank: Investigating projection in nat- urally occurring discourse. In proceedings of Sinn und Bedeutung, volume 23, pp. 107-124, 2019.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Efficient finetuning of quantized llms", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Qlora", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.14314"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: Efficient finetuning of quantized llms. arXiv preprint arXiv:2305.14314, 2023.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Pre-training of deep bidirectional transformers for language understanding", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M.-W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1810.04805"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Pre-training of deep bidirectional transformers for lan- guage understanding. arXiv preprint arXiv:1810.04805, 2018.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "General language model pretraining with autoregressive blank infilling", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Glm", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2103.10360"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. <PERSON>: General language model pretrain- ing with autoregressive blank infilling. arXiv preprint arXiv:2103.10360, 2021.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Losses can be blessings: Routing selfsupervised speech representations towards efficient multilingual and multitask speech processing", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Ye", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C.-I", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "20902--20920", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> Loss<PERSON> can be blessings: Routing self- supervised speech representations towards efficient mul- tilingual and multitask speech processing. Advances in Neural Information Processing Systems, 35:20902- 20920, 2022.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Gpt4aigchip: Towards next-generation ai accelerator design automation via large language models", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Li", "suffix": ""}, {"first": "Z", "middle": [], "last": "Ye", "suffix": ""}, {"first": "C", "middle": [], "last": "Li", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": ["C"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "2023 IEEE/ACM International Conference on Computer Aided Design (ICCAD)", "volume": "", "issue": "", "pages": "1--9", "other_ids": {"DOI": ["10.1109/ICCAD57390.2023.10323953"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y. C. Gpt4aigchip: Towards next-generation ai accelerator design automation via large language mod- els. In 2023 IEEE/ACM International Conference on Computer Aided Design (ICCAD), pp. 1-9, 2023. doi: 10.1109/ICCAD57390.2023.10323953.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Structured prompting: Scaling in-context learning to 1,000 examples", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "Y", "middle": [], "last": "Sun", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Han", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2212.06713"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, F. Structured prompting: Scaling in-context learning to 1,000 examples. arXiv preprint arXiv:2212.06713, 2022.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Measuring massive multitask language understanding", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Ma<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Song", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2009.03300"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Measuring mas- sive multitask language understanding. arXiv preprint arXiv:2009.03300, 2020.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Low-rank adaptation of large language models", "authors": [{"first": "E", "middle": ["J"], "last": "Hu", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2106.09685"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Low-rank adaptation of large language models. arXiv preprint arXiv:2106.09685, 2021.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Mistral 7b", "authors": [{"first": "A", "middle": ["Q"], "last": "Jiang", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Bamford", "suffix": ""}, {"first": "D", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["D"], "last": "Casas", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.06825"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, C., Cha<PERSON>lot, D. S., Casas, D. d. l., <PERSON>, F., <PERSON>, G<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Mistral 7b. arXiv preprint arXiv:2310.06825, 2023.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Scaling laws for neural language models", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Chess", "suffix": ""}, {"first": "R", "middle": [], "last": "Child", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2001.08361"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> laws for neural language models. arXiv preprint arXiv:2001.08361, 2020.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Is model attention aligned with human attention? an empirical study on large language models for code generation", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Ma", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.01220"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Is model attention aligned with human attention? an empiri- cal study on large language models for code generation. arXiv preprint arXiv:2306.01220, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "The power of scale for parameter-efficient prompt tuning", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Al-Rfou", "suffix": ""}, {"first": "N", "middle": [], "last": "Constant", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2104.08691"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, N. The power of scale for parameter-efficient prompt tuning. arXiv preprint arXiv:2104.08691, 2021.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Lora-fine-tuning-aware quantization for large language models", "authors": [{"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "He", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Loftq", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.08659"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Lora-fine-tuning-aware quantization for large language models. arXiv preprint arXiv:2310.08659, 2023.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Can a suit of armor conduct electricity? a new dataset for open book question answering", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "K<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1809.02789"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Can a suit of armor conduct electricity? a new dataset for open book question answering. arXiv preprint arXiv:1809.02789, 2018.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Language model for dialogue generation", "authors": [{"first": "", "middle": [], "last": "Openai", "suffix": ""}, {"first": "", "middle": [], "last": "Chatgpt", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "OpenAI. Chatgpt: Language model for dialogue gener- ation, 2023a. URL https://www.openai.com/ chatgpt/.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "OpenAI. Gpt-4 technical report", "authors": [], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.08774"]}, "num": null, "urls": [], "raw_text": "OpenAI. Gpt-4 technical report. arXiv preprint arXiv:2303.08774, 2023b.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Training language models to follow instructions with human feedback", "authors": [{"first": "L", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "D", "middle": [], "last": "Almeida", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "27730--27744", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Training language models to follow instructions with human feedback. Advances in Neural Information Processing Systems, 35:27730-27744, 2022.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Seeing stars: Exploiting class relationships for sentiment categorization with respect to rating scales", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2005, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> stars: Exploiting class relation- ships for sentiment categorization with respect to rating scales. arXiv preprint cs/0506075, 2005.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Pillow: Enhancing efficient instruction fine-tuning via prompt matching", "authors": [{"first": "Z", "middle": [], "last": "Qi", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Shi", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Qi", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.05621"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Enhancing efficient instruction fine-tuning via prompt matching. arXiv preprint arXiv:2312.05621, 2023.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Scaling transnormer to 175 billion parameters", "authors": [{"first": "Z", "middle": [], "last": "Qin", "suffix": ""}, {"first": "D", "middle": [], "last": "Li", "suffix": ""}, {"first": "W", "middle": [], "last": "Sun", "suffix": ""}, {"first": "W", "middle": [], "last": "Sun", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Han", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Lv", "suffix": ""}, {"first": "F", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.14995"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Scaling transnormer to 175 billion parameters. arXiv preprint arXiv:2307.14995, 2023.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Improving language understanding by generative pre-training", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, et al. Improving language understanding by generative pre-training. 2018.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Exploring the limits of transfer learning with a unified text-to-text transformer", "authors": [{"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Li", "suffix": ""}, {"first": "P", "middle": ["J"], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Journal of Machine Learning Research", "volume": "21", "issue": "140", "pages": "1--67", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, P. J. Ex- ploring the limits of transfer learning with a unified text-to-text transformer. Journal of Machine Learning Research, 21(140):1-67, 2020. URL http://jmlr. org/papers/v21/20-074.html.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Squad: 100,000+ questions for machine comprehension of text", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1606.05250"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, P. Squad: 100,000+ questions for machine comprehension of text. arXiv preprint arXiv:1606.05250, 2016.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Know what you don't know: Unanswerable questions for squad", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1806.03822"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> Know what you don't know: Unanswerable questions for squad. arXiv preprint arXiv:1806.03822, 2018.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Scaling up models and data with t5x and seqio", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Lev<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "D", "middle": [], "last": "And<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Gaffney", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Hawthorne", "suffix": ""}, {"first": "A", "middle": [], "last": "Lewkowycz", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Austin", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": ["B"], "last": "Soares", "suffix": ""}, {"first": "H", "middle": [], "last": "Hu", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Chowdhery", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Passos", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Saeta", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>lan", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2203.17189"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON>, D., Na<PERSON>, S., <PERSON>, B., Gaffney, C., <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, C., Le<PERSON>, A., Sal<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, L<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, S<PERSON>, <PERSON>, A<PERSON>, <PERSON>, J<PERSON>, <PERSON>, J<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, K<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, B., Sepassi, R., <PERSON>, A., Newlan, J., and <PERSON><PERSON>- <PERSON>, <PERSON><PERSON>aling up models and data with t5x and seqio. arXiv preprint arXiv:2203.17189, 2022. URL https://arxiv.org/abs/2203.17189.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Multitask prompted training enables zero-shot task generalization", "authors": [{"first": "V", "middle": [], "last": "San<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>son", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["H"], "last": "Bach", "suffix": ""}, {"first": "L", "middle": [], "last": "Sutawika", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2110.08207"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Multitask prompted training enables zero-shot task generalization. arXiv preprint arXiv:2110.08207, 2021.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Recursive deep models for semantic compositionality over a sentiment treebank", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Potts", "suffix": ""}], "year": 2013, "venue": "Proceedings of the 2013 conference on empirical methods in natural language processing", "volume": "", "issue": "", "pages": "1631--1642", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. <PERSON>, and Potts, C. Recursive deep models for semantic compositionality over a sentiment treebank. In Proceedings of the 2013 conference on empirical methods in natural language processing, pp. 1631-1642, 2013.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Understanding attention for text classification", "authors": [{"first": "X", "middle": [], "last": "Sun", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics", "volume": "", "issue": "", "pages": "3418--3428", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> Understanding attention for text clas- sification. In Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics, pp. 3418- 3428, 2020.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Ladder side-tuning for parameter and memory efficient transfer learning", "authors": [{"first": "Y.-L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Cho", "suffix": ""}, {"first": "M", "middle": [], "last": "Bansal", "suffix": ""}, {"first": "", "middle": [], "last": "Lst", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "12991--13005", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. <PERSON>: Ladder side-tuning for parameter and memory efficient transfer learning. Advances in Neural Information Processing Systems, 35: 12991-13005, 2022.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Commonsenseqa: A question answering challenge targeting commonsense knowledge", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Herzig", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1811.00937"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>: A question answering challenge targeting com- monsense knowledge. arXiv preprint arXiv:1811.00937, 2018.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Open and efficient foundation language models", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "I<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M.-A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "E", "middle": [], "last": "Hambro", "suffix": ""}, {"first": "F", "middle": [], "last": "Azhar", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.13971"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, T<PERSON>, <PERSON>, B., Goyal, N., <PERSON>, E<PERSON>, <PERSON>, <PERSON>, et al. Llama: Open and efficient foundation lan- guage models. arXiv preprint arXiv:2302.13971, 2023a.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Llama 2: Open foundation and fine-tuned chat models", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Stone", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Batra", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09288"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Y<PERSON>, <PERSON>, N., Batra, S., Bhargava, P., B<PERSON>, S., et al. Llama 2: Open foundation and fine-tuned chat models. arXiv preprint arXiv:2307.09288, 2023b.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Attention is all you need", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "Ł", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, I. At- tention is all you need. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "A multiscale visualization of attention in the transformer model", "authors": [{"first": "J", "middle": [], "last": "Vig", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1906.05714"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> A multiscale visualization of attention in the trans- former model. arXiv preprint arXiv:1906.05714, 2019.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Building a question answering test collection", "authors": [{"first": "E", "middle": ["M"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["M"], "last": "Tice", "suffix": ""}], "year": 2000, "venue": "Proceedings of the 23rd annual international ACM SIGIR conference on Research and development in information retrieval", "volume": "", "issue": "", "pages": "200--207", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON>, D. M. Building a question an- swering test collection. In Proceedings of the 23rd annual international ACM SIGIR conference on Research and development in information retrieval, pp. 200-207, 2000.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Google's ai chatbot \"bard\": a side-by-side comparison with chatgpt and its utilization in ophthalmology", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Zaman", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["G"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Tavak<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Eye", "volume": "", "issue": "", "pages": "1--4", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, A. Google's ai chatbot \"bard\": a side-by-side comparison with chatgpt and its utilization in ophthalmology. Eye, pp. 1-4, 2023.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Superglue: A stickier benchmark for general-purpose language understanding systems", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Pruksachatkun", "suffix": ""}, {"first": "N", "middle": [], "last": "Nangia", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Hill", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Super- glue: A stickier benchmark for general-purpose language understanding systems. Advances in neural information processing systems, 32, 2019.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Gpt-j-6b: A 6 billion parameter autoregressive language model", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>zaki", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, A. Gpt-j-6b: A 6 billion param- eter autoregressive language model, 2021.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "A 176b-parameter open-access multilingual language model", "authors": [{"first": "B", "middle": [], "last": "Workshop", "suffix": ""}, {"first": "T", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Fan", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.05100"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. Bloom: A 176b-parameter open-access multilin- gual language model. arXiv preprint arXiv:2211.05100, 2022.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Chain of lora: Efficient finetuning of language models via residual learning", "authors": [{"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Qin", "suffix": ""}, {"first": "E", "middle": [], "last": "Hazan", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2401.04151"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, E. Chain of lora: Efficient fine- tuning of language models via residual learning. arXiv preprint arXiv:2401.04151, 2024.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Efficient streaming language models with attention sinks", "authors": [{"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Tian", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Han", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.17453"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. Ef- ficient streaming language models with attention sinks. arXiv preprint arXiv:2309.17453, 2023.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Hintaug: Drawing hints from foundation vision transformers towards boosted few-shot parameter-efficient tuning", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": ["C"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "11102--11112", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> <PERSON>- aug: Drawing hints from foundation vision transform- ers towards boosted few-shot parameter-efficient tun- ing. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 11102- 11112, 2023a.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Master-asr: achieving multilingual scalability and low-resource adaptation in asr with modular learning", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": ["C"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "40475--40487", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y. C. Master-asr: achieving multilingual scalability and low-resource adaptation in asr with modular learn- ing. In International Conference on Machine Learning, pp. 40475-40487. PMLR, 2023b.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Edge-llm: Enabling efficient large language model adaptation on edge devices via unified compression and adaptive layer voting", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "R", "middle": [], "last": "Gao", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": ["R"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": ["K"], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": ["C"], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Y<PERSON>, and <PERSON>, Y. C. Edge-llm: Enabling efficient large language model adaptation on edge devices via unified compression and adaptive layer voting. 2024.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Can a machine really finish your sentence? arXiv preprint", "authors": [{"first": "R", "middle": [], "last": "Zellers", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Bisk", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Hellaswag", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1905.07830"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>: Can a machine really finish your sentence? arXiv preprint arXiv:1905.07830, 2019.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Side-tuning: a baseline for network adaptation via additive side networks", "authors": [{"first": "J", "middle": ["O"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Sax", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Computer Vision-ECCV 2020: 16th European Conference", "volume": "", "issue": "", "pages": "698--714", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Side-tuning: a baseline for network adaptation via additive side networks. In Computer Vision-ECCV 2020: 16th European Conference, Glasgow, UK, August 23-28, 2020, Proceedings, Part III 16, pp. 698-714. Springer, 2020.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Memory-efficient low-rank adaptation for large language models fine-tuning", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Shi", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Li", "suffix": ""}, {"first": "", "middle": [], "last": "Lora-Fa", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.03303"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>: Memory-efficient low-rank adaptation for large language models fine-tuning. arXiv preprint arXiv:2308.03303, 2023a.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Tell your model where to attend: Post-hoc attention steering for llms", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Gao", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.02262"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Tell your model where to attend: Post-hoc atten- tion steering for llms. arXiv preprint arXiv:2311.02262, 2023b.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Open pre-trained transformer language models", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Roller", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "M", "middle": [], "last": "Artetxe", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>ab", "suffix": ""}, {"first": "X", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": ["V"], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2205.01068"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Opt: Open pre-trained transformer language models. arXiv preprint arXiv:2205.01068, 2022.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Character-level convolutional networks for text classification", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Lecun", "suffix": ""}], "year": 2015, "venue": "Advances in neural information processing systems", "volume": "28", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y. Character-level con- volutional networks for text classification. Advances in neural information processing systems, 28, 2015.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Apt: Adaptive pruning and tuning pretrained language models for efficient training and inference", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2401.12200"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>. Apt: Adaptive prun- ing and tuning pretrained language models for efficient training and inference. arXiv preprint arXiv:2401.12200, 2024.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Judging llm-as-a-judge with mt-bench and chatbot arena", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W.-L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "D", "middle": [], "last": "Li", "suffix": ""}, {"first": "E", "middle": [], "last": "Xi<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.05685"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Judging llm-as-a-judge with mt-bench and chatbot arena. arXiv preprint arXiv:2306.05685, 2023.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Judging llm-as-a-judge with mt-bench and chatbot arena", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W.-L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "D", "middle": [], "last": "Li", "suffix": ""}, {"first": "E", "middle": [], "last": "Xi<PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Judging llm-as-a-judge with mt-bench and chatbot arena. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}}, "ref_entries": {"FIGREF0": {"fig_num": "1", "uris": null, "num": null, "type_str": "figure", "text": "Figure 1. Upper: Visualization of the averaged attention maps across all heads and layers of Llama2-7B-chat on different datasets. Lower: Visualization of the averaged attention maps across all heads in each layer when processing a sample from SST2 with Llama2-7B-chat. Identified attention sinks in the averaged attention map from SST2 are bounded with green boxes."}, "FIGREF1": {"fig_num": "2", "uris": null, "num": null, "type_str": "figure", "text": "Figure 2. Attention score distribution of the initial token (i.e., the attention sink observed in StreamLLM (<PERSON> et al., 2023)), noninitial high attention tokens, and other tokens for classification tasks (top) and multiple-choice tasks (bottom). ous locations; and Obs-(3) attention sinks often manifest in the intermediate layers of LLMs, while the first two layers exhibit more evenly distributed attention scores, and the final layer focuses more on local information with diagonal attention patterns."}, "FIGREF2": {"fig_num": "3", "uris": null, "num": null, "type_str": "figure", "text": "Figure 3. Visualization of accuracy improvement in the MMLU dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2020) achieved by reducing the attention score of attention sinks in the middle of input sequences for each individual head separately."}, "FIGREF3": {"fig_num": "4", "uris": null, "num": null, "type_str": "figure", "text": "Figure 4. Visualization on the model's averaged attention map before (left) and after (right) our proposed ACT."}, "FIGREF4": {"fig_num": "5", "uris": null, "num": null, "type_str": "figure", "text": "Figure 5. Histogram of the positions of attention sinks throughout all 17 datasets used in our paper."}, "FIGREF6": {"fig_num": "678", "uris": null, "num": null, "type_str": "figure", "text": "Figure 6. Visualization on the attention map of each layer in Llama2-7B-chat model when processing the following input sample: 'Read the text and answer the question by True or False.\\n\\nText: Riverdale (2017 TV series) -The series debuted on January 26, 2017 to positive reviews. A 22-episode second season premiered on October 11, 2017, and concluded on May 16, 2018. On April 2, 2018, The CW renewed the series for a third season, which is set to premiere October 10, 2018. Question: is there going to be any more episodes of riverdale? \\n Answer: '"}, "TABREF0": {"html": null, "num": null, "type_str": "table", "text": "Frequency of tokens appear with significantly higher attention scores.", "content": "<table><tr><td colspan=\"2\">Token name '&lt; s &gt;'</td><td>'.'</td><td>'&lt; 0x0A &gt;'</td><td>':'</td><td>'Answer'</td></tr><tr><td>Frequency</td><td colspan=\"2\">1621135 958992</td><td>636902</td><td>65078</td><td>46297</td></tr><tr><td>Ratio</td><td>48.2%</td><td>28.5%</td><td>18.9%</td><td>1.9%</td><td>1.3%</td></tr><tr><td>Token name</td><td>' '</td><td>'Type'</td><td>'iment'</td><td>'D'</td><td>Total</td></tr><tr><td>Frequency</td><td>21841</td><td>4430</td><td>3896</td><td>2644</td><td>3363296</td></tr><tr><td>Ratio</td><td>0.6%</td><td>0.1%</td><td>0.1%</td><td>0.1%</td><td>100%</td></tr></table>"}, "TABREF1": {"html": null, "num": null, "type_str": "table", "text": "where α = 5 by default.", "content": "<table><tr><td>2. Reduce the attention scores of attention sinks located in</td></tr><tr><td>later tokens by setting Âl h</td></tr></table>"}, "TABREF2": {"html": null, "num": null, "type_str": "table", "text": "ACT on domain-specific multiple choice datasets", "content": "<table><tr><td>Model</td><td colspan=\"5\">Setting Method Hellaswag ARCE PIQA</td><td>OB</td><td colspan=\"4\">ARCC COPA CQA Avg.</td></tr><tr><td/><td/><td>Vanilla</td><td>41.65</td><td colspan=\"4\">75.61 63.22 57.20 52.17</td><td colspan=\"3\">85.00 59.71 62.08</td></tr><tr><td/><td>0-shot</td><td>ACT</td><td>42.70</td><td colspan=\"4\">75.79 66.54 59.00 53.85</td><td colspan=\"3\">89.00 59.71 63.80</td></tr><tr><td/><td/><td>Improv.</td><td>1.05</td><td>0.18</td><td>3.32</td><td>1.80</td><td>1.68</td><td>4.00</td><td>0.00</td><td>1.72</td></tr><tr><td/><td/><td>Vanilla</td><td>30.99</td><td colspan=\"4\">75.44 59.25 54.20 53.51</td><td colspan=\"3\">72.00 59.54 57.85</td></tr><tr><td/><td>1-shot</td><td>ACT</td><td>31.52</td><td colspan=\"4\">75.79 60.55 57.00 54.52</td><td colspan=\"3\">76.00 60.04 59.35</td></tr><tr><td>Llama2-7B-chat</td><td/><td>Improv.</td><td>0.53</td><td>0.35</td><td>1.30</td><td>2.80</td><td>1.01</td><td>4.00</td><td>0.50</td><td>1.50</td></tr><tr><td/><td/><td>Vanilla</td><td>42.46</td><td colspan=\"4\">77.54 65.56 56.40 55.52</td><td colspan=\"3\">69.00 62.49 61.28</td></tr><tr><td/><td>3-shot</td><td>ACT</td><td>42.93</td><td colspan=\"4\">77.19 66.27 56.60 57.19</td><td colspan=\"3\">69.00 63.14 61.76</td></tr><tr><td/><td/><td>Improv.</td><td>0.47</td><td>-0.35</td><td>0.71</td><td>0.20</td><td>1.67</td><td>0.00</td><td>0.65</td><td>0.48</td></tr><tr><td/><td/><td>Vanilla</td><td>44.62</td><td colspan=\"4\">77.02 64.58 59.00 60.54</td><td colspan=\"3\">69.00 62.98 62.53</td></tr><tr><td/><td>5-shot</td><td>ACT</td><td>45.58</td><td colspan=\"4\">77.72 65.02 59.60 62.54</td><td colspan=\"3\">71.00 63.23 63.53</td></tr><tr><td/><td/><td>Improv.</td><td>0.96</td><td>0.70</td><td>0.44</td><td>0.60</td><td>2.00</td><td>2.00</td><td>0.25</td><td>0.99</td></tr><tr><td/><td/><td>Vanilla</td><td>41.80</td><td colspan=\"4\">79.82 69.80 63.20 64.21</td><td colspan=\"3\">77.00 64.70 65.79</td></tr><tr><td/><td>0-shot</td><td>ACT</td><td>48.28</td><td colspan=\"4\">78.77 69.21 63.80 64.88</td><td colspan=\"3\">89.00 64.86 68.40</td></tr><tr><td/><td/><td>Improv.</td><td>6.48</td><td>-1.05</td><td>-0.59</td><td>0.60</td><td>0.67</td><td>12.00</td><td>0.16</td><td>2.61</td></tr><tr><td/><td/><td>Vanilla</td><td>47.27</td><td colspan=\"4\">78.07 69.86 62.80 65.89</td><td colspan=\"3\">85.00 60.28 67.02</td></tr><tr><td/><td>1-shot</td><td>ACT</td><td>50.49</td><td colspan=\"4\">77.19 70.51 62.60 68.23</td><td colspan=\"3\">87.00 60.20 68.03</td></tr><tr><td>Llama2-13B-chat</td><td/><td>Improv.</td><td>3.22</td><td>-0.88</td><td>0.65</td><td>-0.20</td><td>2.34</td><td>2.00</td><td>-0.08</td><td>1.01</td></tr><tr><td/><td/><td>Vanilla</td><td>48.26</td><td colspan=\"4\">82.28 69.86 66.40 68.90</td><td colspan=\"3\">85.00 66.83 69.65</td></tr><tr><td/><td>3-shot</td><td>ACT</td><td>51.64</td><td colspan=\"4\">82.82 70.95 67.60 68.90</td><td colspan=\"3\">85.00 66.53 70.49</td></tr><tr><td/><td/><td>Improv.</td><td>3.38</td><td>0.54</td><td>1.09</td><td>1.20</td><td>0.00</td><td>0.00</td><td>-0.30</td><td>0.84</td></tr><tr><td/><td/><td>Vanilla</td><td>51.26</td><td colspan=\"4\">82.81 67.19 69.60 68.23</td><td colspan=\"3\">91.00 66.34 70.92</td></tr><tr><td/><td>5-shot</td><td>ACT</td><td>52.67</td><td colspan=\"4\">82.11 67.46 68.80 69.23</td><td colspan=\"3\">91.00 67.24 71.22</td></tr><tr><td/><td/><td>Improv.</td><td>1.41</td><td>-0.70</td><td>0.27</td><td>-0.80</td><td>1.00</td><td>0.00</td><td>0.90</td><td>0.30</td></tr><tr><td/><td/><td>Vanilla</td><td>49.68</td><td colspan=\"4\">85.96 72.31 72.00 76.25</td><td colspan=\"3\">87.00 69.21 73.20</td></tr><tr><td>Mistral-7B</td><td>0-shot</td><td>ACT</td><td>55.82</td><td colspan=\"4\">87.19 79.22 74.00 77.26</td><td colspan=\"3\">95.00 70.60 77.01</td></tr><tr><td/><td/><td>Improv.</td><td>6.14</td><td>1.23</td><td>6.91</td><td>2.00</td><td>1.01</td><td>8.00</td><td>1.39</td><td>3.79</td></tr><tr><td/><td/><td>Vanilla</td><td>42.18</td><td colspan=\"4\">81.75 55.44 53.40 64.55</td><td colspan=\"3\">82.60 53.32 61.89</td></tr><tr><td>Llama-30B</td><td>0-shot</td><td>ACT</td><td>55.44</td><td colspan=\"4\">83.16 67.46 62.80 67.89</td><td colspan=\"3\">90.40 57.17 69.19</td></tr><tr><td/><td/><td>Improv.</td><td>13.26</td><td>1.41</td><td colspan=\"2\">12.02 9.40</td><td>3.34</td><td>7.80</td><td>3.85</td><td>7.30</td></tr><tr><td colspan=\"2\">vron et al., 2023b), Mistral-7B</td><td/><td/><td/><td/><td/><td/><td/><td/></tr></table>"}, "TABREF4": {"html": null, "num": null, "type_str": "table", "text": "ACT in boosting different LLMs on the MMLU dataset", "content": "<table><tr><td>Model</td><td colspan=\"5\">Llama2 7B GPT-J 7B Vicuna-7B opt-2.7B Average</td></tr><tr><td>zero-shot</td><td>46.50</td><td>26.53</td><td>48.73</td><td>25.46</td><td>36.80</td></tr><tr><td>zero-shot-aug</td><td>46.82</td><td>27.62</td><td>49.15</td><td>25.94</td><td>37.38</td></tr><tr><td>Improv.</td><td>0.32</td><td>1.09</td><td>0.42</td><td>0.48</td><td>0.58</td></tr></table>"}, "TABREF5": {"html": null, "num": null, "type_str": "table", "text": "ACT on text classification datasets", "content": "<table><tr><td>Model</td><td colspan=\"4\">Setting Method SST2 SST5</td><td>MR</td><td colspan=\"2\">AGNews TREC</td><td>CB</td><td>BoolQ Avg.</td></tr><tr><td/><td/><td colspan=\"4\">Van<PERSON> 92.78 47.87 90.99</td><td>78.17</td><td colspan=\"3\">11.80 69.64 77.68 65.07</td></tr><tr><td/><td>0-shot</td><td>ACT</td><td colspan=\"3\">93.23 47.59 91.74</td><td>81.76</td><td colspan=\"3\">18.80 69.64 76.48 66.36</td></tr><tr><td/><td/><td colspan=\"2\">Improv. 0.45</td><td>-0.28</td><td>0.75</td><td>3.59</td><td>7.00</td><td>0.00</td><td>-1.20</td><td>1.29</td></tr><tr><td/><td/><td colspan=\"4\">Vanilla 87.50 44.69 82.93</td><td>84.87</td><td colspan=\"3\">21.60 76.79 38.87 61.11</td></tr><tr><td/><td>1-shot</td><td>ACT</td><td colspan=\"3\">89.33 45.69 84.33</td><td>85.62</td><td colspan=\"3\">23.00 78.57 41.74 62.49</td></tr><tr><td>Llama2-7B-chat</td><td/><td colspan=\"2\">Improv. 1.83</td><td>1.00</td><td>1.40</td><td>0.75</td><td>1.40</td><td>1.78</td><td>2.87</td><td>1.38</td></tr><tr><td/><td/><td colspan=\"4\">Vanilla 92.08 42.62 92.87</td><td>75.09</td><td colspan=\"3\">24.20 67.86 68.42 64.35</td></tr><tr><td/><td>3-shot</td><td>ACT</td><td colspan=\"3\">92.78 42.51 92.21</td><td>76.36</td><td colspan=\"3\">25.00 73.21 72.52 65.78</td></tr><tr><td/><td/><td colspan=\"2\">Improv. 0.70</td><td colspan=\"2\">-0.11 -0.66</td><td>1.27</td><td>0.80</td><td>5.35</td><td>4.10</td><td>1.43</td></tr><tr><td/><td/><td colspan=\"4\">Vanilla 93.69 46.87 90.62</td><td>85.59</td><td colspan=\"3\">29.60 69.64 81.55 67.79</td></tr><tr><td/><td>5-shot</td><td>ACT</td><td colspan=\"3\">94.04 46.62 90.71</td><td>86.04</td><td colspan=\"3\">30.60 69.64 81.58 68.38</td></tr><tr><td/><td/><td colspan=\"2\">Improv. 0.35</td><td>-0.25</td><td>0.09</td><td>0.45</td><td>1.00</td><td>0.00</td><td>0.03</td><td>0.58</td></tr><tr><td/><td/><td colspan=\"4\">Vanilla 91.86 46.23 90.71</td><td>81.07</td><td colspan=\"3\">18.00 66.07 80.76 67.81</td></tr><tr><td/><td>0-shot</td><td>ACT</td><td colspan=\"3\">92.20 46.16 90.43</td><td>82.37</td><td colspan=\"3\">29.00 75.00 81.68 70.98</td></tr><tr><td/><td/><td colspan=\"2\">Improv. 0.34</td><td colspan=\"2\">-0.07 -0.28</td><td>1.30</td><td>11.00</td><td>8.93</td><td>0.92</td><td>3.16</td></tr><tr><td/><td/><td colspan=\"4\">Vanilla 93.69 42.69 86.59</td><td>82.51</td><td colspan=\"3\">17.20 75.00 64.74 66.06</td></tr><tr><td/><td>1-shot</td><td>ACT</td><td colspan=\"3\">94.27 42.96 87.05</td><td>83.57</td><td colspan=\"3\">23.40 75.00 65.75 67.43</td></tr><tr><td>Llama2-13B-chat</td><td/><td colspan=\"2\">Improv. 0.58</td><td>0.27</td><td>0.46</td><td>1.06</td><td>6.20</td><td>0.00</td><td>1.01</td><td>1.37</td></tr><tr><td/><td/><td colspan=\"4\">Vanilla 92.09 48.14 87.52</td><td>80.36</td><td colspan=\"3\">15.20 82.14 76.87 68.90</td></tr><tr><td/><td>3-shot</td><td>ACT</td><td colspan=\"3\">92.78 48.23 87.62</td><td>80.36</td><td colspan=\"3\">22.40 82.14 77.29 70.12</td></tr><tr><td/><td/><td colspan=\"2\">Improv. 0.69</td><td>0.09</td><td>0.10</td><td>0.00</td><td>7.20</td><td>0.00</td><td>0.42</td><td>1.21</td></tr><tr><td/><td/><td colspan=\"4\">Vanilla 93.23 47.96 92.87</td><td>85.95</td><td colspan=\"3\">16.40 73.21 81.55 70.17</td></tr><tr><td/><td>5-shot</td><td>ACT</td><td colspan=\"3\">93.46 47.59 93.06</td><td>85.97</td><td colspan=\"3\">17.20 76.79 81.58 70.81</td></tr><tr><td/><td/><td colspan=\"2\">Improv. 0.23</td><td>-0.37</td><td>0.19</td><td>0.02</td><td>0.80</td><td>3.58</td><td>0.03</td><td>0.64</td></tr><tr><td/><td/><td colspan=\"4\">Vanilla 92.43 44.96 89.02</td><td>85.09</td><td colspan=\"3\">22.00 91.07 85.84 72.91</td></tr><tr><td>Mistral-7B</td><td>0-shot</td><td>ACT</td><td colspan=\"3\">92.78 47.14 90.02</td><td>85.59</td><td colspan=\"3\">23.00 91.07 85.96 73.65</td></tr><tr><td/><td/><td colspan=\"2\">Improv. 0.35</td><td>2.18</td><td>1.00</td><td>0.50</td><td>1.00</td><td>0.00</td><td>0.12</td><td>0.74</td></tr><tr><td/><td/><td colspan=\"4\">Vanilla 80.53 41.78 81.05</td><td>64.37</td><td colspan=\"3\">28.60 42.86 65.17 60.25</td></tr><tr><td>Llama-30B</td><td>0-shot</td><td>ACT</td><td colspan=\"3\">85.09 45.59 85.37</td><td>80.53</td><td colspan=\"3\">29.80 41.07 65.85 65.37</td></tr><tr><td/><td/><td colspan=\"2\">Improv. 4.56</td><td>3.81</td><td>5.32</td><td>16.16</td><td>1.20</td><td>-1.79</td><td>0.68</td><td>5.12</td></tr></table>"}, "TABREF6": {"html": null, "num": null, "type_str": "table", "text": "ACT on open-ended question-answering datasets using Llama2-chat with different sizes. Each result for SQuADv1/v2 is presented as the exact match score/F1 score.", "content": "<table><tr><td>Model</td><td colspan=\"3\">Method MT-Bench SQuAD v1</td><td>SQuAD v2</td></tr><tr><td/><td>Vanilla</td><td>6.272</td><td colspan=\"2\">31.64/47.88 4.36/24.42</td></tr><tr><td>Llama2-7B-chat</td><td>ACT</td><td>6.406</td><td colspan=\"2\">41.78/64.30 19.52/31.30</td></tr><tr><td/><td>Improv.</td><td>0.134</td><td>10.14/16.42</td><td>5.16/6.88</td></tr><tr><td/><td>Vanilla</td><td>6.602</td><td colspan=\"2\">41.77/56.00 19.69/27.02</td></tr><tr><td>Llama2-13B-chat</td><td>ACT</td><td>6.690</td><td colspan=\"2\">45.89/58.57 21.42/28.15</td></tr><tr><td/><td>Improv.</td><td>0.088</td><td>4.12/2.57</td><td>1.73/1.13</td></tr></table>"}, "TABREF7": {"html": null, "num": null, "type_str": "table", "text": "Ablate on attention calibration methodsCalibrate method Temp Inv-temp Inv-ours Ours", "content": "<table><tr><td>Acc.</td><td>44.89</td><td>44.06</td><td>46.21</td><td>46.82</td></tr></table>"}, "TABREF8": {"html": null, "num": null, "type_str": "table", "text": "Ablate on how to distribute the additional attention.", "content": "<table><tr><td colspan=\"5\">Method Uniform Question-only Choices-only Ours</td></tr><tr><td>Acc.</td><td>46.49</td><td>46.10</td><td>45.24</td><td>46.82</td></tr></table>"}, "TABREF9": {"html": null, "num": null, "type_str": "table", "text": "Ablate on α selection.", "content": "<table><tr><td>α</td><td>SST2 SST5</td><td>MR</td><td colspan=\"2\">AGNews TREC</td><td>CB</td><td>BoolQ</td></tr><tr><td colspan=\"3\">Vanilla 92.78 47.87 90.99</td><td>78.17</td><td colspan=\"3\">11.80 69.64 77.68</td></tr><tr><td>3</td><td colspan=\"2\">93.23 47.59 91.74</td><td>81.74</td><td colspan=\"3\">19.00 69.64 76.26</td></tr><tr><td>5</td><td colspan=\"2\">93.23 47.59 91.74</td><td>81.76</td><td colspan=\"3\">18.80 69.64 76.48</td></tr><tr><td>7</td><td colspan=\"2\">93.12 47.68 91.74</td><td>81.29</td><td colspan=\"3\">18.80 69.64 76.62</td></tr></table>"}, "TABREF10": {"html": null, "num": null, "type_str": "table", "text": "Ablate on β selection.", "content": "<table><tr><td>β</td><td>Vanilla</td><td>0.7</td><td>0.5</td><td>0.4 (Ours)</td><td>0.3</td><td>0.1</td></tr><tr><td colspan=\"4\">Acc. 46.50 46.77 46.81</td><td>46.82</td><td colspan=\"2\">46.79 46.65</td></tr></table>"}, "TABREF11": {"html": null, "num": null, "type_str": "table", "text": "Ablate on M selection.", "content": "<table><tr><td>M</td><td>Vanilla</td><td>300</td><td>600</td><td>1000</td><td>All</td></tr><tr><td colspan=\"6\">Acc. 46.50 46.50 46.56 46.82 46.91</td></tr></table>"}, "TABREF12": {"html": null, "num": null, "type_str": "table", "text": "Ablate on the performance of ACT when only calibrating on a subset of the selected attention heads.", "content": "<table><tr><td>Subset size</td><td colspan=\"3\">SST2 AGNews PIQA ARCC Avg.</td></tr><tr><td colspan=\"2\">0% (Vanilla) 92.78</td><td>78.17</td><td>63.22 52.10 71.57</td></tr><tr><td>40%</td><td>92.78</td><td>80.16</td><td>66.92 53.51 73.34</td></tr><tr><td>60%</td><td>92.89</td><td>81.12</td><td>65.34 52.17 72.88</td></tr><tr><td>80%</td><td>93.23</td><td>81.08</td><td>66.63 52.84 73.44</td></tr><tr><td colspan=\"2\">100% (ACT) 93.23</td><td>81.76</td><td>66.54 53.85 73.84</td></tr></table>"}}}}