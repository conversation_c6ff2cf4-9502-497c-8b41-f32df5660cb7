{"paper_id": "FiT", "title": "FiT: Flexible Vision Transformer for Diffusion Model", "abstract": "Nature is infinitely resolution-free. In the context of this reality, existing diffusion models, such as Diffusion Transformers, often face challenges when processing image resolutions outside of their trained domain. To overcome this limitation, we present the Flexible Vision Transformer (FiT), a transformer architecture specifically designed for generating images with unrestricted resolutions and aspect ratios. Unlike traditional methods that perceive images as static-resolution", "pdf_parse": {"paper_id": "FiT", "abstract": [{"text": "Nature is infinitely resolution-free. In the context of this reality, existing diffusion models, such as Diffusion Transformers, often face challenges when processing image resolutions outside of their trained domain. To overcome this limitation, we present the Flexible Vision Transformer (FiT), a transformer architecture specifically designed for generating images with unrestricted resolutions and aspect ratios. Unlike traditional methods that perceive images as static-resolution", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "grids, FiT conceptualizes images as sequences of dynamically-sized tokens. This perspective enables a flexible training strategy that effortlessly adapts to diverse aspect ratios during both training and inference phases, thus promoting resolution generalization and eliminating biases induced by image cropping. Enhanced by a meticulously adjusted network structure and the integration of training-free extrapolation techniques, FiT exhibits remarkable flexibility in resolution extrapolation generation. Comprehensive experiments demonstrate the exceptional performance of FiT across a broad range of resolutions, showcasing its effectiveness both within and beyond its training resolution distribution. Repository available at https://github.com/whlzy/FiT.", "section": "", "sec_num": null}, {"text": "Current image generation models struggle with generalizing across arbitrary resolutions. The Diffusion Transformer (DiT) (Peebles & Xie, 2023) family, while excelling within certain resolution ranges, falls short when dealing with images of varying resolutions. This limitation stems from the fact that DiT can not utilize dynamic resolution images during its training process, hindering its ability to adapt to different token lengths or resolutions effectively.", "section": "Introduction", "sec_num": "1."}, {"text": "To overcome this limitation, we introduce the Flexible Vision Transformer (FiT), which is adept at generating images at unrestricted resolutions and aspect ratios. The key motivation is a novel perspective on image data modeling: rather than treating images as static grids of fixed dimensions, FiT conceptualizes images as sequences of variablelength tokens. This approach allows FiT to dynamically adjust the sequence length, thereby facilitating the generation of images at any desired resolution without being constrained by pre-defined dimensions. By efficiently managing variable-length token sequences and padding them to a maximum specified length, FiT unlocks the potential for resolution-independent image generation. FiT represents this paradigm shift through significant advancements in flexible training pipeline, network architecture, and inference processes.", "section": "Introduction", "sec_num": "1."}, {"text": "Flexible Training Pipeline. FiT uniquely preserves the original image aspect ratio during training, by viewing the image as a sequence of tokens. This unique perspective allows FiT to adaptively resize high-resolution images to fit within a predefined maximum token limit, ensuring that no image, regardless of its original resolution, is cropped or disproportionately scaled. This method ensures that the integrity of the image resolution is maintained, as shown in Figure 2 , facilitating the ability to generate high-fidelity images at various resolutions. To the best of our knowledge, FiT is the first transformer-based generation model to maintain diverse image resolutions throughout training.", "section": "Introduction", "sec_num": "1."}, {"text": "Network Architecture. The FiT model evolves from the DiT architecture but addresses its limitations in resolution extrapolation. One essential network architecture adjustment to handle diverse image sizes is the adoption of 2D Rotary Positional Embedding (RoPE) (<PERSON> et al., 2024) , inspired by its success in enhancing large language models (LLMs) for length extrapolation (<PERSON> et al., 2023) . We also introduce Swish-Gated Linear Unit (SwiGLU) (<PERSON><PERSON><PERSON><PERSON>, 2020) in place of the traditional Multilayer Perceptron (MLP) and replace DiT's Multi-Head Self-Attention (MHSA) with Masked MHSA to efficiently manage padding tokens within our flexible training pipeline.", "section": "Introduction", "sec_num": "1."}, {"text": "Inference Process. While large language models employ token length extrapolation techniques (<PERSON><PERSON> et al., 2023 ; LocalLLaMA) for generating text of arbitrary lengths, a direct application of these technologies to FiT yields suboptimal results. We tailor these techniques for 2D RoPE, thereby enhancing FiT's performance across a spectrum of resolutions and aspect ratios. In summary, our contributions lie in the novel introduction of FiT, a flexible vision transformer tailored for diffusion models, capable of generating images at any resolution and aspect ratio. We present three innovative design features in FiT, including a flexible training pipeline that eliminates the need for cropping, a unique transformer architecture for dynamic token length modeling, and a training-free resolution extrapolation method for arbitrary resolution generation.", "section": "Introduction", "sec_num": "1."}, {"text": "Strict experiments demonstrate that the FiT-XL/2 model achieves state-of-the-art performance across a variety of resolution and aspect ratio settings.", "section": "Introduction", "sec_num": "1."}, {"text": "Diffusion Models. Denoising Diffusion Probabilistic Models (DDPMs) (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2021) and score-based models (Hyvä<PERSON><PERSON> & <PERSON>, 2005; <PERSON> et al., 2020b) have exhibited remarkable progress in the context of image generation tasks. The Denoising Diffusion Implicit Model (DDIM) Song et al. (2020a) , offers An accelerated sampling procedure. Latent Diffusion Models (LDMs) (<PERSON><PERSON><PERSON> et al., 2022) establishes a new benchmark of training deep generative models to reverse a noise process in the latent space, through the use of VQ-VAE (<PERSON><PERSON> et al., 2021) .", "section": "Related Work", "sec_num": "2."}, {"text": "Transformer Models. The Transformer model (<PERSON><PERSON><PERSON><PERSON> et al., 2017) , has successfully supplanted domain-specific architectures in a variety of fields including, but not limited to, language (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2023a) , vision (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020) , and multi-modality (<PERSON> et al., 2023) . In vision perception research, most efforts (<PERSON><PERSON><PERSON><PERSON> et al., 2019; 2021; <PERSON> et al., 2021; 2022) that focus on resolution are aimed at accelerating pretraining using a fixed, low resolution. On the other hand, NaViT (<PERSON><PERSON><PERSON><PERSON> et al., 2023) implements the 'Patch n' Pack' technique to train ViT using images at their natural, 'native' resolution. Notably, transformers have been also explored in the denoising diffusion probabilistic models (<PERSON> et al., 2020) to synthesize images. DiT (Peebles & Xie, 2023) is the seminal work that utilizes a vision transformer as the backbone of LDMs and can serve as a strong baseline.", "section": "Related Work", "sec_num": "2."}, {"text": "Based on DiT architecture, MDT (<PERSON> et al., 2023) introduces a masked latent modeling approach, which requires two forward-runs in training and inference. U-ViT (<PERSON><PERSON> et al., 2023) treats all inputs as tokens and incorporates U-Net architectures into the ViT backbone of LDMs. Dif-fiT (<PERSON><PERSON><PERSON><PERSON> et al., 2023) introduces a time-dependent self-attention module into the DiT backbone to adapt to different stages of the diffusion process. We follow the LDM paradigm of the above methods and further propose a novel flexible image synthesis pipeline.", "section": "Related Work", "sec_num": "2."}, {"text": "Length Extrapolation in LLMs. RoPE (Rotary Position Embedding) (<PERSON> et al., 2024) is a novel positional embedding that incorporates relative position information into absolute positional embedding. It has recently become the dominant positional embedding in a wide range of LLM (Large Language Model) designs (<PERSON><PERSON><PERSON> et al., 2023b; <PERSON><PERSON><PERSON><PERSON> et al., 2023a; b) . Although RoPE enjoys valuable properties, such as the flexibility of sequence length, its performance drops when the input sequence surpasses the training length. Many approaches have been proposed to solve this issue. PI (Position Interpolation) (<PERSON> et al., 2023) linearly down-scales the input position indices to match the original context window size, while NTK-aware (LocalL-LaMA) changes the rotary base of RoPE. YaRN (Yet another RoPE extensioN) (<PERSON><PERSON> et al., 2023) is an improved method to efficiently extend the context window. RandomPE (<PERSON><PERSON><PERSON> et al., 2023) sub-samples an ordered set of positions from a much larger range of positions than originally observed in training or inference. xPos (Sun et al., 2022) incorporates long-term decay into RoPE and uses blockwise causal attention for better extrapolation performance. Our work delves deeply into the implementation of RoPE in vision generation and on-the-fly resolution extrapolation methods.", "section": "Related Work", "sec_num": "2."}, {"text": "3.1. Preliminary 1-D RoPE (Rotary Positional Embedding) (<PERSON> et al., 2024) is a type of position embedding that unifies absolute and relative PE, exhibiting a certain degree of extrapolation capability in LLMs. Given the m-th key and n-th query vector as q m , k n ∈ R |D| , 1-D RoPE multiplies the bias to the key or query vector in the complex vector space:", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "f q (q m , m) = e imΘ q m , f k (k n , n) = e inΘ k n (1)", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "where", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "Θ = Diag(θ 1 , • • • , θ |D|/2", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": ") is rotary frequency matrix with θ d = b -2d/|D| and rotary base b = 10000. In real space, given l = |D|/2, the rotary matrix e imΘ equals to:", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "EQUATION", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "The attention score with 1-D RoPE is calculated as: ", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "EQUATION", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "EQUATION", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": ")", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "where the scale factor s is defined as:", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "EQUATION", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "YaRN (Yet another RoPE extensioN) Interpolation (<PERSON><PERSON> et al., 2023) introduces the ratio of dimension d as r(d) = L train /(2πb 2d/|D| ), and modifies the rotary frequency as:", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "EQUATION", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": ")", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "where s is the aforementioned scale factor, and γ(r(d)) is a ramp function with extra hyper-parameters α, β:", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "EQUATION", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "Besides, it incorporates a 1D-RoPE scaling term as: 0, 0 0, 1 0, 2 0, 3 1, 0 1, 1 1, 2 1, 3 2, 0 2, 1 2, 2 2, 0, 0 0, 1 0, 2 0, 3 1, 0 1, 1 1, 2 1, 3 2, 0 2, 1 2, 2 2, 3 Tokens Denoised Tokens Define Generated Image Position 0, 0 0, 1 0, 2 0, 3   1, 0 1, 1 1, 2 1, ", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "EQUATION", "section": "Flexible Vision Transformer for Diffusion", "sec_num": "3."}, {"text": "Modern deep learning models, constrained by the characteristics of GPU hardware, are required to pack data into batches of uniform shape for parallel processing. Due to the diversity in image resolutions, as shown in Fig. 4 , DiT resizes and crops the images to a fixed resolution 256 × 256. While resizing and cropping as a means of data augmentation is a common practice, this approach introduces certain biases into the input data. These biases will directly affect the final images generated by the model, including blurring effects from the transition from low to high resolution and information lost due to the cropping (more failure samples can be found in Appendix D).", "section": "Flexible Training and Inference Pipeline", "sec_num": "3.2."}, {"text": "To this end, we propose a flexible training and inference pipeline, as shown in Fig. 3 (a, b ). In the preprocessing phase, we avoid cropping images or resizing low-resolution images to a higher resolution. Instead, we only resize highresolution images to a predetermined maximum resolution limit, HW ⩽ 256 2 . In the training phase, FiT first encodes an image into latent codes with a pre-trained VAE encoder. By patchfiying latent codes to latent tokens, we can get sequences with different lengths L. To pack these sequences into a batch, we pad all these sequences to the maximum token length L max using padding tokens. Here we set L max = 256 to match the fixed token length of DiT. The same as the latent tokens, we also pad the positional embeddings to the maximum length for packing. Finally, we calculate the loss function only for the denoised output tokens, while discarding all other padding tokens. In the inference phase, we firstly define the position map of the generated image and sample noisy tokens from the Gaussian distribution as input. After completing K iterations of the denoising process, we reshape and unpatchfiy the denoised tokens according to the predefined position map to get the final generated image.", "section": "Flexible Training and Inference Pipeline", "sec_num": "3.2."}, {"text": "Building upon the flexible training pipeline, our goal is to find an architecture that can stably train across various resolutions and generate images with arbitrary resolutions and aspect ratios, as shown in Figure 3 (c) . Motivated by some significant architectural advances in LLMs, we conduct a series of experiments to explore architectural modifications based on DiT, see details in Section 4.2.", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "Replacing MHSA with Masked MHSA. The flexible training pipeline introduces padding tokens for flexibly packing dynamic sequences into a batch. During the forward phase of the transformer, it is crucial to facilitate interactions among noised tokens while preventing any interaction between noised tokens and padding tokens. The Multi-Head Self-Attention (MHSA) mechanism of original DiT is incapable of distinguishing between noised tokens and padding tokens. To this end, we use Masked MHSA to replace the standard MHSA. We utilize the sequence mask M for Masked Attention, where noised tokens are assigned the value of 0, and padding tokens are assigned the value of negative infinity (-inf ), which is defined as follows:", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "Masked Attn.(Q i , K i , V i ) = Softmax Q i K T i √ d k + M V i (9)", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "where Q i , K i , V i are the query, key, and value matrices for the i-th head.", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "Replacing Absolute PE with 2D RoPE. We observe that vision transformer models with absolute positional embedding fail to generalize well on images beyond the training resolution, as in Sections 4.3 and 4.5. Inspired by the success of 1D-RoPE in LLMs for length extrapolation (<PERSON> et al., 2023) , we utilize 2D-RoPE to facilitate the resolution generalization in vision transformer models. Formally, we calculate the 1-D RoPE for the coordinates of height and width separately. Then such two 1-D RoPEs are concatenated in the last dimension. Given 2-D coordinates of width and height as {(w, h) 1 ⩽ w ⩽ W, 1 ⩽ h ⩽ H}, the 2-D RoPE is defined as:", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "EQUATION", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "where ", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "Θ = Diag(θ 1 , • • • , θ |D|/4 ),", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "EQUATION", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": ")", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "It is noteworthy that there is no cross-term between h and w in 2D-RoPE and attention score A n , so we can further decouple the rotary frequency as Θ h and Θ w , resulting in the decoupled 2D-RoPE, which will be discussed in Section 3.4 and more details can be found in Appendix B.", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "Replacing MLP with SwiGLU. We follow recent LLMs like LLaMA (<PERSON><PERSON><PERSON><PERSON> et al., 2023a; b) , and replace the MLP in FFN with SwiGLU, which is defined as follows:", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "EQUATION", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": ")", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "where ⊗ denotes Hadmard Product, W 1 , W 2 , and W 3 are the weight matrices without bias, SiLU(x) = x ⊗ σ(x).", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "Here we will use SwiGLU as our choice in each FFN block.", "section": "Flexible Vision Transformer Architecture", "sec_num": "3.3."}, {"text": "We denote the inference resolution as (H test , W test ). Our FiT can handle various resolutions and aspect ratios during training, so we denote training resolution as", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "L train = √ L max .", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "By changing the scale factor in Equation ( 5) to s = max(max(H test , W test )/L train , 1.0), we can directly implement the positional interpolation methods in large language model extrapolation on 2D-RoPE, which we call vanilla NTK and YaRN implementation. Furthermore, we propose vision RoPE interpolation methods by using the decoupling attribute in decoupled 2D-RoPE. We modify Equation (10) to:", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "EQUATION", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "where", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "Θ h = {θ h d = b -2d/|D| h , 1 ⩽ d ⩽ |D| 2 } and Θ w = {θ w d = b -2d/|D| w , 1 ⩽ d ⩽ |D| 2 } are calculated separately.", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "Accordingly, the scale factor of height and width is defined separately as", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "EQUATION", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": ")", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "Definition 3.1. The Definition of VisionNTK Interpolation is a modification of NTK-aware Interpolation by using Equation (13) with the following rotary base.", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "EQUATION", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "where b = 10000 is the same with Equation (1)", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "Definition 3.2. The Definition of VisionYaRN Interpolation is a modification of YaRN Interpolation by using Equation (13) with the following rotary frequency.", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "EQUATION", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "where γ(r(d)) is the same with Equation ( 6).", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "It is worth noting that VisionNTK and VisionYaRN are training-free positional embedding interpolation approaches, used to alleviate the problem of position embedding out of distribution in extrapolation. When the aspect ratio equals one, they are equivalent to the vanilla implementation of NTK and YaRN. They are especially effective in generating images with arbitrary aspect ratios, see Section 4.3.", "section": "Training Free Resolution Extrapolation", "sec_num": "3.4."}, {"text": "We present the implementation details of FiT, including model architecture, training details, and evaluation metrics.", "section": "FiT Implementation", "sec_num": "4.1."}, {"text": "Model architecture. We follow DiT-B and DiT-XL to set the same layers, hidden size, and attention heads for base model FiT-B and xlarge model FiT-XL. As DiT reveals stronger synthesis performance when using a smaller patch size, we use a patch size p=2, denoted by FiT-B/2 and FiT-XL/2. FiT adopts the same off-the-shelf pre-trained VAE (<PERSON><PERSON> et al., 2021) as DiT provided by the Stable Diffusion (<PERSON> et al., 2022) high-resolution images to meet the HW ⩽ 256 2 limitation while maintaining the aspect ratio. We follow DiT to use Horizontal Flip Augmentation. We use the same training setting as DiT: a constant learning rate of 1 × 10 -4 using AdamW (<PERSON><PERSON> & Hutter, 2017) , no weight decay, and a batch size of 256. Following common practice in the generative modeling literature, we adopt an exponential moving average (EMA) of model weights over training with a decay of 0.9999. All results are reported using the EMA model. We retain the same diffusion hyper-parameters as DiT.", "section": "FiT Implementation", "sec_num": "4.1."}, {"text": "Evaluation details and metrics. We evaluate models with some commonly used metrics, i.e. Fre'chet Inception Distance (FID) (<PERSON><PERSON><PERSON> et al., 2017) , sFID (<PERSON> et al., 2021) , Inception Score (IS) (<PERSON><PERSON><PERSON> et al., 2016) , improved Precision and Recall (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2019) . For fair comparisons, we follow DiT to use the TensorFlow evaluation from ADM (Dhariwal & Nichol, 2021) and report FID-50K with 250 DDPM sampling steps. FID is used as the major metric as it measures both diversity and fidelity. We additionally report IS, sFID, Precision, and Recall as secondary metrics. For FiT architecture experiment (Section 4.2) and resolution extrapolation ablation experiment (Section 4.3), we report the results without using classifierfree guidance (Ho & Salimans, 2021) .", "section": "FiT Implementation", "sec_num": "4.1."}, {"text": "Evaluation resolution. Unlike previous work that mainly conducted experiments on a fixed aspect ratio of 1 : 1, we conducted experiments on different aspect ratios, which are 1 : 1, 1 : 2, and 1 : 3, respectively. On the other hand, we divide the experiment into resolution within the training distribution and resolution out of the training distribution. For the resolution in distribution, we mainly use 256×256 (1:1), 160 × 320 (1:2), and 128 × 384 (1:3) for evaluation, with 256, 200, 192 latent tokens respectively. All token lengths are smaller than or equal to 256, leading to respective resolutions within the training distribution. For the resolution out of distribution, we mainly use 320 × 320 (1:1), 224 × 448 (1:2), and 160 × 480 (1:3) for evaluation, with 400, 392, 300 latent tokens respectively. All token lengths are larger than 256, resulting in the resolutions out of training distribution. Through such division, we holistically evaluate the image synthesis and resolution extrapolation ability of FiT at various resolutions and aspect ratios.", "section": "FiT Implementation", "sec_num": "4.1."}, {"text": "In this part, we conduct an ablation study to verify the architecture designs in FiT. We report the results of various variant FiT-B/2 models at 400K training steps and use FID-50k, sFID, IS, Precision, and Recall as the evaluation metrics. We conduct experiments at three different resolutions: 256 × 256, 160 × 320, and 224 × 448. These resolutions are chosen to encompass different aspect ratios, as well as to include resolutions both in and out of the distribution. ", "section": "FiT Architecture Design", "sec_num": "4.2."}, {"text": "In this part, we adopt the DiT-B/2 and FiT-B/2 models at 400K training steps to evaluate the extrapolation performance on three out-of-distribution resolutions: 320 × 320, 224 × 448 and 160 × 480. Direct extrapolation does not perform well on larger resolution out of training distribution. So we conduct a comprehensive benchmarking analysis focused on positional embedding interpolation methods.", "section": "FiT Resolution Extrapolation Design", "sec_num": "4.3."}, {"text": "PI and EI. PI (Position Interpolation) and EI (Embedding Interpolation) are two baseline positional embedding interpolation methods for resolution extrapolation. PI linearly down-scales the inference position coordinates to match the original coordinates. EI resizes the positional embedding with bilinear interpolation 1 . Following ViT (<PERSON><PERSON><PERSON><PERSON> et al., 2020) , EI is used for absolute positional embedding.", "section": "FiT Resolution Extrapolation Design", "sec_num": "4.3."}, {"text": "NTK and YaRN. We set the scale factor to s = max(max(H test , W test )/ √ 256) and adopt the vanilla implementation of the two methods, as in Section 3.1. For YaRN, we set α = 1, β = 32 in Equation ( 7).", "section": "FiT Resolution Extrapolation Design", "sec_num": "4.3."}, {"text": "VisionNTK and VisionYaRN. These two methods are defined detailedly in Definitions 3.1 and 3.2. Note that when the aspect ratio equals one, the VisionNTK and VisionYaRN are equivalent to NTK and YaRN, respectively.", "section": "FiT Resolution Extrapolation Design", "sec_num": "4.3."}, {"text": "Analysis. We present in Tab. 4 that our FiT-B/2 shows stable performance when directly extrapolating to larger resolutions. When combined with PI, the extrapolation performance of FiT-B/2 at all three resolutions decreases. When combined with YaRN, the FID score reduces by 16.77 on 320 × 320, but the performance on 224 × 448 and 168 × 480 descends. Our VisionYaRN solves this dilemma and reduces the FID score by 40.27 on 224 × 448 and by 41.22 at 160 × 480 compared with YaRN. NTK interpolation method demonstrates stable extrapolation performance but increases the FID score slightly at 224 × 448 and 160 × 480 resolutions. Our VisionNTK method alleviates this problem and exceeds the performance of direct extrapolation at all three resolutions. In conclusion, our FiT-B/2 has a strong extrapolation ability, which can be further enhanced when combined with VisionYaRN and VisionNTK methods. However, DiT-B/2 demonstrates poor extrapolation ability. When combined with PI, the FID score achieves 72.47 at 320×320 resolution, which still falls behind our FiT-B/2. At 224×448 and 160×480 resolutions, PI and EI interpolation methods cannot improve the extrapolation performance.", "section": "FiT Resolution Extrapolation Design", "sec_num": "4.3."}, {"text": "Following our former analysis, we train our highest Gflops model, FiT-XL/2, for 1.8M steps. We conduct experiments to evaluate the performance of FiT at three different in distribution resolutions: 256×256, 160×320, and 128×384. We show samples from the FiT in Fig 1 , and we compare against some state-of-the-art class-conditional generative models: BigGAN (<PERSON> et al., 2018) , StyleGAN-XL (<PERSON> et al., 2022) , MaskGIT (<PERSON> et al., 2022) , CDM (<PERSON> et al., 2022) , U-ViT (Ba<PERSON> et al., 2023) , ADM (Dhariwal & Nichol, 2021) , LDM (<PERSON><PERSON><PERSON> et al., 2022) , MDT (Gao et al., 2023) , and DiT (Peebles & Xie, 2023) . When generating images of 160 × 320 and 128 × 384 resolution, we adopt PI on the positional embedding of the DiT model, as stated in Section 4.3. EI is employed in the positional embedding of U-ViT and MDT models, as they use learnable positional embedding. ADM and LDM can directly synthesize images with resolutions different from the training resolution.", "section": "FiT In-Distribution Resolution Results", "sec_num": "4.4."}, {"text": "1 torch.nn.functional.interpolate(pe, (h,w), method='bilinear')", "section": "FiT In-Distribution Resolution Results", "sec_num": "4.4."}, {"text": "As shown in Tab. 1, FiT-XL/2 outperforms all prior diffusion models, decreasing the previous best FID-50K of 6.93 achieved by U-ViT-H/2-G to 5.74 at 160 × 320 resolution. For 128 × 384 resolution, FiT-XL/2 shows significant superiority, decreasing the previous SOTA FID-50K of 29.67 to 16.81. The FID score of FiT-XL/2 increases slightly at 256 × 256 resolution, compared to other models that have undergone longer training steps.", "section": "FiT In-Distribution Resolution Results", "sec_num": "4.4."}, {"text": "We evaluate our FiT-XL/2 on three different out-ofdistribution resolutions: 320×320, 224×448, and 160×480 and compare against some SOTA class-conditional generative models: U-ViT, ADM, LDM-4, MDT, and DiT. PI is employed in DiT, while EI is adopted in U-ViT and MDT, as in Section 4.4. U-Net-based methods, such as ADM and LDM-4 can directly generate images with resolution out of distribution. As shown in Table 2 , FiT-XL/2 achieves the best FID-50K and IS, on all three resolutions, indicating its outstanding extrapolation ability. In terms of other metrics, as sFID, FiT-XL/2 demonstrates competitive performance.", "section": "FiT Out-Of-Distribution Resolution Results", "sec_num": "4.5."}, {"text": "LDMs with transformer backbones are known to have difficulty in generating images out of training resolution, such as DiT, U-ViT, and MDT. More seriously, MDT has almost no ability to generate images beyond the training resolution. We speculate this is because both learnable absolute PE and learnable relative PE are used in MDT. DiT and U-ViT show a certain degree of extrapolation ability and achieve FID scores of 9.98 and 7.65 respectively at 320x320 resolution. However, when the aspect ratio is not equal to one, their generation performance drops significantly, as 224 × 448 and 160×480 resolutions. Benefiting from the advantage of the local receptive field of the Convolution Neural Network, ADM and LDM show stable performance at these out-ofdistribution resolutions. Our FiT-XL/2 solves the problem of insufficient extrapolation capabilities of the transformer in image synthesis. At 320 × 320, 224 × 448, and 160 × 480 resolutions, FiT-XL/2 exceeds the previous SOTA LDM on FID-50K by 0.82, 0.65, and 3.52 respectively.", "section": "FiT Out-Of-Distribution Resolution Results", "sec_num": "4.5."}, {"text": "In this work, we aim to contribute to the ongoing research on flexible generating arbitrary resolutions and aspect ratio. We propose Flexible Vision Transformer (FiT) for diffusion model, a refined transformer architecture with flexible training pipeline specifically designed for generating images with arbitrary resolutions and aspect ratios. FiT surpasses all previous models, whether transformer-based or CNN-based, across various resolutions. With our resolution extrapolation method, VisionNTK, the performance of FiT has been significantly enhanced further. ", "section": "Conclusion", "sec_num": "5."}, {"text": "The model is downloaded in https://huggingface.co/stabilityai/sd-vae-ft-ema", "section": "", "sec_num": null}, {"text": "https://github.com/openai/guided-diffusion/tree/main/evaluations", "section": "", "sec_num": null}], "back_matter": [{"text": "We provide detailed network configurations and performance of all models, which are listed in Tab. 5. 2D RoPE defines a vector-valued complex function f (x, h m , w m ) in Equation ( 10) as follows:The self-attention score A n injected with 2D RoPE in Equation ( 11) is detailed defined as follows:where 2-D coordinates of width and height as {(w, h) 1 ⩽ w ⩽ W, 1 ⩽ h ⩽ H}, the subscripts of q and k denote the dimensions of the attention head, θ n = 10000 -2n/d . There is no cross-term between h and w in 2D-RoPE and attention score A n , so we can further decouple the rotary frequency as, resulting in the decoupled 2D-RoPE, as follows:So we can reformulate the vector-valued complex function f (x, h m , w m ) in Equation ( 13) as follows:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Constrained by limited computational resources, we only train FiT-XL/2 for 1800K steps. At the resolution of 256x256, the performance of FiT-XL/2 is slightly inferior compared to the DiT-XL/2 model. On the other hand, we have not yet thoroughly explored the generative capabilities of the FiT-XL/2 model when training with higher resolutions (larger token length limitation). Additionally, we only explore resolution extrapolation techniques that are training-free, without delving into other resolution extrapolation methods that require additional training. We believe that FiT will enable a range of interesting studies that have been infeasible before and encourage more attention towards generating images with arbitrary resolutions and aspect ratios.", "section": "C. Limitations and Future Work", "sec_num": null}, {"text": "We show samples from our FiT-XL/2 models at resolutions of 256 × 256, 224 × 448 and 448 × 224, trained for 1.8M (generated with 250 DDPM sampling steps and the ft-EMA VAE decoder). Fig. 6 shows uncurated samples from FiT-XL/2 with classifier-free guidance scale 4.0 and class label \"loggerhead turtle\" (33). Fig. 7 shows uncurated samples from FiT-XL/2 with classifier-free guidance scale 4.0 and class label \"Cacatua galerita\" (89). Fig. 8 shows uncurated samples from FiT-XL/2 with classifier-free guidance scale 4.0 and class label \"golden retriever\" (207). Fig. 9 shows uncurated samples from FiT-XL/2 with classifier-free guidance scale 4.0 and class label \"white fox\" (279). Fig. 10 shows uncurated samples from FiT-XL/2 with classifier-free guidance scale 4.0 and class label \"otter\" (360). Fig. 11 shows uncurated samples from FiT-XL/2 with classifier-free guidance scale 4.0 and class label \"volcano\" (980).We also show some failure samples from DiT-XL/2, as shown in Fig. 5 .", "section": "<PERSON><PERSON> Model Samples", "sec_num": null}], "ref_entries": {"FIGREF0": {"fig_num": "2", "text": "Figure 2: Pipeline comparison between (a) DiT and (b) FiT.", "uris": null, "type_str": "figure", "num": null}, "FIGREF1": {"fig_num": null, "text": "NTK-aware Interpolation (LocalLLaMA) is a trainingfree length extrapolation technique in LLMs. To handle the larger context length L test than maximum training length L train , it modifies the rotary base of 1-D RoPE as follows:", "uris": null, "type_str": "figure", "num": null}, "FIGREF2": {"fig_num": null, "text": "where 1 √ t = 0.1 ln(s) + 1.", "uris": null, "type_str": "figure", "num": null}, "FIGREF6": {"fig_num": "3", "text": "Figure 3: Overview of (a) flexible training pipeline, (b) flexible inference pipeline, and (c) FiT block.", "uris": null, "type_str": "figure", "num": null}, "FIGREF7": {"fig_num": "4", "text": "Figure 4: Height/Width distribution of the original ImageNet (<PERSON><PERSON> et al., 2009) dataset.", "uris": null, "type_str": "figure", "num": null}, "FIGREF8": {"fig_num": "5", "text": "Figure 5: Uncurated failure samples from DiT-XL/2.", "uris": null, "type_str": "figure", "num": null}, "FIGREF9": {"fig_num": "6", "text": "Figure 6: Uncurated samples from FiT-XL/2 models at resolutions of 256 × 256, 224 × 448 and 448 × 224.", "uris": null, "type_str": "figure", "num": null}, "FIGREF10": {"fig_num": "7", "text": "Figure 7: Uncurated samples from FiT-XL/2 models at resolutions of 256 × 256, 224 × 448 and 448 × 224.", "uris": null, "type_str": "figure", "num": null}, "FIGREF11": {"fig_num": "8", "text": "Figure 8: Uncurated samples from FiT-XL/2 models at resolutions of 256 × 256, 224 × 448 and 448 × 224.", "uris": null, "type_str": "figure", "num": null}, "FIGREF12": {"fig_num": "9", "text": "Figure 9: Uncurated samples from FiT-XL/2 models at resolutions of 256 × 256, 224 × 448 and 448 × 224.", "uris": null, "type_str": "figure", "num": null}, "FIGREF13": {"fig_num": "10", "text": "Figure 10: Uncurated samples from FiT-XL/2 models at resolutions of 256 × 256, 224 × 448 and 448 × 224.", "uris": null, "type_str": "figure", "num": null}, "FIGREF14": {"fig_num": "11", "text": "Figure 11: Uncurated samples from FiT-XL/2 models at resolutions of 256 × 256, 224 × 448 and 448 × 224.", "uris": null, "type_str": "figure", "num": null}, "TABREF1": {"content": "<table/>", "text": "and ∥ denotes concatenate two vectors in the last dimension. Note that we divide the |D|-dimension space into |D|/4-dimension subspace to ensure the consistency of dimension, which differs from |D|/2-dimension subspace in 1-D RoPE. Analogously, the attention score with 2-D RoPE is:", "html": null, "type_str": "table", "num": null}, "TABREF2": {"content": "<table><tr><td>to encode/decode the im-</td></tr></table>", "text": "Benchmarking class-conditional image generation with in-distribution resolution on ImageNet dataset. \"-G\" denotes the results with classifier-free guidance. † : MDT-G adpots an improved classifier-free guidance strategy(<PERSON> et al., 2023):w t = (1 -cos π( t tmax ) s )w/2, where w = 3.8 is the maximum guidance scale and s = 4 is the controlling factor. * : FiT-XL/2-G adopts VisionNTK for resolution extrapolation.", "html": null, "type_str": "table", "num": null}, "TABREF3": {"content": "<table/>", "text": "Benchmarking class-conditional image generation with out-of-distribution resolution on ImageNet dataset. \"-G\" denotes the results with classifier-free guidance.", "html": null, "type_str": "table", "num": null}, "TABREF4": {"content": "<table><tr><td>Arch.</td><td>Pos. Embed.</td><td/><td>FFN</td><td>Train</td><td colspan=\"11\">256×256 (i.d.) FID↓ sFID↓ IS↑ Prec.↑ Rec.↑ FID↓ sFID↓ 160×320 (i.d.) IS↑ Prec.↑ Rec.↑ FID↓ sFID↓ 224×448 (o.o.d.) IS↑ Prec.↑ Rec.↑</td></tr><tr><td>DiT-B</td><td>Abs. PE</td><td/><td>MLP</td><td>Fixed</td><td>44.83</td><td>8.49</td><td>32.05</td><td>0.48</td><td>0.63</td><td colspan=\"3\">91.32 66.66 14.02</td><td>0.21</td><td>0.45</td><td>109.1 110.71 14.00</td><td>0.18</td><td>0.31</td></tr><tr><td>Config A</td><td>Abs. PE</td><td/><td>MLP</td><td colspan=\"4\">Flexible 43.34 11.11 32.23</td><td>0.48</td><td>0.61</td><td colspan=\"3\">50.51 10.36 25.26</td><td>0.42</td><td>0.60</td><td>52.55 16.05 28.69</td><td>0.42</td><td>0.58</td></tr><tr><td>Config B</td><td>Abs. PE</td><td/><td colspan=\"5\">SwiGLU Flexible 41.75 11.53 34.55</td><td>0.49</td><td>0.61</td><td colspan=\"3\">48.66 10.65 26.76</td><td>0.41</td><td>0.60</td><td>52.34 17.73 30.01</td><td>0.41</td><td>0.57</td></tr><tr><td colspan=\"3\">Config C Abs. PE + 2D RoPE</td><td>MLP</td><td colspan=\"4\">Flexible 39.11 10.79 36.35</td><td>0.51</td><td>0.61</td><td colspan=\"3\">46.71 10.32 27.65</td><td>0.44</td><td>0.61</td><td>46.60 15.84 33.99</td><td>0.46</td><td>0.58</td></tr><tr><td>Config D</td><td>2D RoPE</td><td/><td>MLP</td><td colspan=\"4\">Flexible 37.29 10.62 38.34</td><td>0.53</td><td>0.61</td><td>45.06</td><td>9.82</td><td>28.87</td><td>0.43</td><td>0.62</td><td>46.16 23.72 35.28</td><td>0.46</td><td>0.55</td></tr><tr><td>FiT-B</td><td>2D RoPE</td><td/><td colspan=\"5\">SwiGLU Flexible 36.36 11.08 40.69</td><td>0.52</td><td>0.62</td><td colspan=\"3\">43.96 10.26 30.45</td><td>0.43</td><td>0.62</td><td>44.67 24.09 37.10</td><td>0.49</td><td>0.53</td></tr><tr><td>Method</td><td/><td colspan=\"6\">320×320 (1:1) FID↓ sFID↓ IS↑ Prec.↑ Rec.↑</td><td>FID↓</td><td colspan=\"5\">224×448 (1:2) sFID↓ IS↑ Prec.↑ Rec.↑</td><td>FID↓</td><td>160×480 (1:3) sFID↓ IS↑ Prec.↑ Rec.↑</td></tr><tr><td>DiT-B</td><td/><td colspan=\"3\">95.47 108.68 18.38</td><td>0.26</td><td/><td>0.40</td><td colspan=\"4\">109.1 110.71 14.00</td><td>0.18</td><td>0.31</td><td colspan=\"2\">143.8 122.81 8.93</td><td>0.073</td><td>0.20</td></tr><tr><td>DiT-B + EI</td><td/><td colspan=\"3\">81.48 62.25 20.97</td><td>0.25</td><td/><td>0.47</td><td>133.2</td><td colspan=\"3\">72.53 11.11</td><td>0.11</td><td>0.29</td><td>160.4</td><td>93.91</td><td>7.30</td><td>0.054</td><td>0.16</td></tr><tr><td>DiT-B + PI</td><td/><td colspan=\"3\">72.47 54.02 24.15</td><td>0.29</td><td/><td>0.49</td><td>133.4</td><td colspan=\"3\">70.29 11.73</td><td>0.11</td><td>0.29</td><td>156.5</td><td>93.80</td><td>7.80</td><td>0.058</td><td>0.17</td></tr><tr><td>FiT-B</td><td/><td colspan=\"3\">61.35 30.71 31.01</td><td>0.41</td><td/><td>0.53</td><td>44.67</td><td>24.09</td><td>37.1</td><td/><td>0.49</td><td>0.52</td><td>56.81</td><td>22.07 25.25</td><td>0.38</td><td>0.49</td></tr><tr><td>FiT-B + PI</td><td/><td colspan=\"3\">65.76 65.45 29.32</td><td>0.32</td><td/><td>0.45</td><td colspan=\"3\">175.42 114.39 8.45</td><td/><td>0.14</td><td>0.06</td><td colspan=\"2\">224.83 123.45 5.89</td><td>0.02</td><td>0.06</td></tr><tr><td colspan=\"2\">FiT-B + YaRN</td><td colspan=\"3\">44.76 38.04 44.70</td><td>0.51</td><td/><td>0.51</td><td>82.19</td><td colspan=\"3\">75.48 29.68</td><td>0.40</td><td>0.29</td><td colspan=\"2\">104.06 72.97 20.76</td><td>0.21</td><td>0.31</td></tr><tr><td colspan=\"2\">FiT-B + NTK</td><td colspan=\"3\">57.31 31.31 33.97</td><td>0.43</td><td/><td>0.55</td><td>45.24</td><td colspan=\"3\">29.38 38.84</td><td>0.47</td><td>0.52</td><td>59.19</td><td>26.54 26.01</td><td>0.36</td><td>0.49</td></tr><tr><td colspan=\"5\">FiT-B + VisionYaRN 44.76 38.04 44.70</td><td>0.51</td><td/><td>0.51</td><td>41.92</td><td colspan=\"3\">42.79 45.87</td><td>0.50</td><td>0.48</td><td>62.84</td><td>44.82 27.84</td><td>0.36</td><td>0.42</td></tr><tr><td colspan=\"2\">FiT-B + VisionNTK</td><td colspan=\"3\">57.31 31.31 33.97</td><td>0.43</td><td/><td>0.55</td><td>43.84</td><td colspan=\"3\">26.25 39.22</td><td>0.48</td><td>0.52</td><td>56.76</td><td>24.18 26.40</td><td>0.37</td><td>0.49</td></tr></table>", "text": "Flexible training vs. Fixed training. Flexible training pipeline significantly improves the performance across var-Ablation results from DiT-B/2 to FiT-B/2 at 400K training steps without using classifier-free guidance.", "html": null, "type_str": "table", "num": null}, "TABREF5": {"content": "<table><tr><td/><td>that Config C performs worse. For resolutions of 256x256,</td></tr><tr><td/><td>160x320, and 224x448, Config C increases FID scores of</td></tr><tr><td/><td>1.82, 1.65, and 0.44, respectively, compared to Config D.</td></tr><tr><td>Config A is the original DiT-B/2 model only with flexible</td><td>Therefore, only 2D RoPE is used for positional embedding</td></tr><tr><td>training, which slightly improves the performance (-1.49</td><td>in our implementation.</td></tr><tr><td>FID) compared with DiT-B/2 with fixed resolution training at 256 × 256 resolution. Config A demonstrates a signif-icant performance improvement through flexible training. Compared to DiT-B/2, FID scores are reduced by 40.81 and 56.55 at resolutions 160 × 320 and 224 × 448, respectively.</td><td>Putting it together. FiT demonstrates significant and com-prehensive superiority across various resolution settings, compared to original DiT. FiT has achieved state-of-the-art performance across various configurations. Compared to DiT-B/2, FiT-B/2 reduces the FID score by 8.47 on the most</td></tr><tr><td>SwiGLU vs. MLP. SwiGLU slightly improves the perfor-</td><td>common resolution of 256 × 256 in Tab. 3. Furthermore,</td></tr><tr><td>mance across various resolutions, compared to MLP. Config</td><td>FiT-B/2 has made significant performance gains at resolu-</td></tr><tr><td>B is the FiT-B/2 flexible training model replacing MLP with</td><td>tions of 160×320 and 224×448, decreasing the FID scores</td></tr><tr><td>SwiGLU. Compared to Config A, Config B demonstrates</td><td>by 47.36 and 64.43, respectively.</td></tr><tr><td>notable improvements across various resolutions. Specifi-</td><td/></tr><tr><td>cally, for resolutions of 256×256, 160×320, and 224×448,</td><td/></tr><tr><td>Config B reduces the FID scores by 1.59, 1.85, and 0.21 in</td><td/></tr><tr><td>Tab. 3, respectively. So FiT uses SwiGLU in FFN.</td><td/></tr><tr><td>2D RoPE vs. Absolute PE. 2D RoPE demonstrates greater</td><td/></tr><tr><td>efficiency compared to absolute position encoding, and it</td><td/></tr><tr><td>possesses significant extrapolation capability across various</td><td/></tr><tr><td>resolutions. Config D is the FiT-B/2 flexible training model</td><td/></tr><tr><td>replacing absolute PE with 2D RoPE. For resolutions within</td><td/></tr><tr><td>the training distribution, specifically 256 × 256 and 160 ×</td><td/></tr><tr><td>320, Config D reduces the FID scores by 6.05, and 5.45 in</td><td/></tr><tr><td>Tab. 3, compared to Config A. For resolution beyond the</td><td/></tr><tr><td>training distribution, 224 × 448, Config D shows significant</td><td/></tr><tr><td>extrapolation capability (-6.39 FID) compared to Config A.</td><td/></tr><tr><td>Config C retains both absolute PE and 2D RoPE. However,</td><td/></tr><tr><td>in a comparison between Config C and Config D, we observe</td><td/></tr></table>", "text": "Benchmarking class-conditional image generation with out-of-distribution resolution on ImageNet. The FiT-B/2 and DiT-B/2 at 400K training steps are adopted in this experiment. Metrics are calculated without using classifier-free guidance. YaRN and NTK mean the vanilla implementation of such two methods. Our FiT-B/2 demonstrates stable extrapolation performance, which can be further improved combined with VisionNTK and VisionYaRN methods.ious resolutions. This improvement is evident not only within the in-distribution resolutions but also extends to resolutions out of the training distribution, as shown in Tab. 3.", "html": null, "type_str": "table", "num": null}}}}