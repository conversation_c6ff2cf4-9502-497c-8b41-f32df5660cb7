{"paper_id": "GEAM", "title": "Drug Discovery with Dynamic Goal-aware Fragments", "abstract": "Fragment-based drug discovery is an effective strategy for discovering drug candidates in the vast chemical space, and has been widely employed in molecular generative models. However, many existing fragment extraction methods in such models do not take the target chemical properties into account or rely on heuristic rules. Additionally, the existing fragment-based generative models cannot update the fragment vocabulary with goal-aware fragments newly discovered during the generation. To this end, we propose a molecular generative framework for drug discovery, named Goal-aware fragment Extraction, Assembly, and Modification (GEAM). GEAM consists of three modules, each responsible for goalaware fragment extraction, fragment assembly, and fragment modification. The fragment extraction module identifies important fragments contributing to the desired target properties with the information bottleneck principle, thereby constructing an effective goal-aware fragment vocabulary. Moreover, GEAM can explore beyond the initial vocabulary with the fragment modification module, and the exploration is further enhanced through the dynamic goal-aware vocabulary update. We experimentally demonstrate that GEAM effectively discovers drug candidates through the generative cycle of the three modules in various drug discovery tasks. Our code is available at https://github.com/ SeulLee05/GEAM. Drug Discovery with Dynamic Goal-aware Fragments Encoder Node embedding Fragment pooling Fragment embedding 𝑤 𝑤 predictor Graph Training set Top-𝐾", "pdf_parse": {"paper_id": "GEAM", "abstract": [{"text": "Fragment-based drug discovery is an effective strategy for discovering drug candidates in the vast chemical space, and has been widely employed in molecular generative models. However, many existing fragment extraction methods in such models do not take the target chemical properties into account or rely on heuristic rules. Additionally, the existing fragment-based generative models cannot update the fragment vocabulary with goal-aware fragments newly discovered during the generation. To this end, we propose a molecular generative framework for drug discovery, named Goal-aware fragment Extraction, Assembly, and Modification (GEAM). GEAM consists of three modules, each responsible for goalaware fragment extraction, fragment assembly, and fragment modification. The fragment extraction module identifies important fragments contributing to the desired target properties with the information bottleneck principle, thereby constructing an effective goal-aware fragment vocabulary. Moreover, GEAM can explore beyond the initial vocabulary with the fragment modification module, and the exploration is further enhanced through the dynamic goal-aware vocabulary update. We experimentally demonstrate that GEAM effectively discovers drug candidates through the generative cycle of the three modules in various drug discovery tasks. Our code is available at https://github.com/ SeulLee05/GEAM. Drug Discovery with Dynamic Goal-aware Fragments Encoder Node embedding Fragment pooling Fragment embedding 𝑤 𝑤 predictor Graph Training set Top-𝐾", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Drug discovery aims to find molecules with desired properties within the vast chemical space. Fragment-based drug 1 KAIST 2 National University of Singapore 3 DeepAuto.ai. Correspondence to: <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>.", "section": "Introduction", "sec_num": "1."}, {"text": "Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s). discovery (FBDD) has been considered an effective strategy in recent decades as a means of exploring the chemical space and has led to the discovery of many potent compounds against various targets (Li, 2020) . Inspired by the effectiveness of FBDD, many molecular generative models have also adopted it to narrow down the search space and simplify the generation process, resulting in meaningful success (<PERSON> et al., 2018; 2020a; b; <PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2022; Kong et al., 2022; <PERSON><PERSON> et al., 2023) .", "section": "Introduction", "sec_num": "1."}, {"text": "The first step of FBDD, fragment library construction, directly impacts the final generation results (<PERSON> <PERSON>, 2019) as the constructed fragments are used in the entire generation process. However, existing fragment extraction or motif mining methods suffer from two limitations: they (1) do not take the target chemical properties of drug discovery problems into account and/or (2) rely on heuristic fragment selection rules. For example, it is a common strategy to randomly select fragments (<PERSON> et al., 2021) or extract fragments based on frequency (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2023) without considering the target properties. <PERSON> et al. (2020b) proposed to find molecular substructures that satisfy the given properties, but the extraction process is computationally very expensive and the substructures cannot be assembled together.", "section": "Introduction", "sec_num": "1."}, {"text": "To this end, we first propose a novel deep learningbased goal-aware fragment extraction method, namely, Fragment-wise Graph Information Bottleneck (FGIB, Figure 1(a) ). There is a strong connection between molecular structures and their activity, referred to as structure-activity relationship (SAR) (Crum-Brown & Fraser, 1865; <PERSON><PERSON><PERSON> et al., 1996) . Inspired by SAR, FGIB utilizes the graph information bottleneck (GIB) theory to identify important subgraphs in the given molecular graphs for predicting the target chemical property. These identified subgraphs serve as building blocks in the subsequent generation. As shown in Figure 1 (b), using the proposed goal-aware fragments extracted by FGIB significantly improves the optimization performance and outperforms existing FBDD methods. Furthermore, we theoretically analyze how FGIB helps in identifying high-quality and novel graphs.", "section": "Introduction", "sec_num": "1."}, {"text": "To effectively utilize the extracted fragments in molecular generation, we next construct a generative model consisting of a fragment assembly module and a fragment modi- fication module. In this work, we employ soft-actor critic (SAC) for the assembly module and a genetic algorithm (GA) for the modification module. Through the interplay of the two modules, the generative model can both exploit the extracted goal-aware fragments and explore beyond the initial fragment vocabulary. Moreover, to further enhance molecular novelty and diversity, we propose to extract new fragments on-the-fly during the generation using FGIB and dynamically update the fragment vocabulary.", "section": "Introduction", "sec_num": "1."}, {"text": "Taken as a whole, the fragment extraction module, the fragment assembly module, and the fragment modification module in the form of FGIB, SAC, and GA, respectively, collectively constitute the generative framework which we refer to as Goal-aware fragment Extraction, Assembly, and Modification (GEAM). As illustrated in Figure 2 , GEAM generates molecules through an iterative process that sequentially runs each module as follows: (1) After FGIB constructs an initial goal-aware fragment vocabulary, SAC assembles these fragments and generates a new molecule.", "section": "Introduction", "sec_num": "1."}, {"text": "(2) GEAM keeps track of the top generated molecules as the initial population of GA, and GA generates an offspring molecule from the population. (3) As a consequence of the crossover and mutation, the offspring molecule contains new subgraphs that cannot be constructed from the current fragment vocabulary, and FGIB extracts the meaningful subgraphs from the offspring molecule and updates the vocabulary. Through the collaboration of the three modules where FGIB provides goal-aware fragments to SAC, SAC provides high-quality population to GA, and GA provides novel fragments to FGIB, GEAM effectively explores the chemical space to discover novel drug candidates.", "section": "Introduction", "sec_num": "1."}, {"text": "We experimentally validate the proposed GEAM on various molecular optimization tasks that simulate real-world drug discovery scenarios. The experimental results show that GEAM significantly outperforms existing state-of-theart methods, demonstrating its effectiveness in addressing real-world drug discovery problems. We summarize our contributions as follows:", "section": "Introduction", "sec_num": "1."}, {"text": "• We propose FGIB, a novel goal-aware fragment extraction method using the GIB theory to construct a fragment vocabulary for target chemical properties. ", "section": "Introduction", "sec_num": "1."}, {"text": "Fragment extraction Fragment extraction methods fragmentize the given molecules into molecular substructures, i.e., fragments, for subsequent generation. <PERSON> et al. (2021) chose to randomly select fragments after breaking bonds in the given molecules with a predefined rule. <PERSON><PERSON> et al. (2020) and <PERSON><PERSON><PERSON> et al. (2022) proposed to obtain fragments by breaking some of the bonds with a predefined rule (e.g., acyclic single bonds), then select the most frequent fragments. <PERSON> et al. (2022) and <PERSON><PERSON> et al. (2023) utilized merge-and-update rules to find the frequent fragments in the given molecules. All of these methods do not consider the target properties. On the other hand, <PERSON> et al. (2020b) proposed to find molecular substructures that satisfy the given properties. However, the approach requires an expensive oracle call to examine each building block candidate in a brute-force manner, and the substructures are not actually fragments in that they are already full molecules that have chemical properties and are not assembled together. Consequently, the identified substructures are large in size and often few in number, resulting in low novelty and diversity of the generated molecules.", "section": "Related Work", "sec_num": "2."}, {"text": "Fragment-based molecule generation Fragment-based molecular generative models refers to models that use the extracted fragments as building blocks and learn to assemble the blocks into molecules. <PERSON><PERSON> et al. (2020) proposed to use MCMC sampling during assembly or deletion of fragments. <PERSON> et al. (2021) proposed to use a reinforcement learning (RL) model and view fragment addition as actions. <PERSON><PERSON><PERSON> et al. (2022) , <PERSON> et al. (2022) and <PERSON><PERSON> et al. (2023) proposed to use a VAE to assemble the fragments. The model of <PERSON> et al. (2020b) learns to complete the obtained molecular substructures into final molecules by adding molecular branches. Besides, the fragment-based strategy has been also applied to the generation of 3D molecules with meaningful success (<PERSON> & <PERSON>, 2023; <PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2023) .", "section": "Related Work", "sec_num": "2."}, {"text": "Subgraph recognition Given a graph, subgraph recognition aims to identify a compressed subgraph that contains salient information to predict the property of the graph. Graph information bottleneck (GIB) (<PERSON> et al., 2020) addressed this problem by treating the subgraph as a bottleneck random variable and applying the information bottleneck theory. <PERSON> et al. (2022) proposed to inject Gaussian noise into node representations to confine the information and recognize important subgraphs, while <PERSON><PERSON> et al. (2022) proposed to consider the subgraph attention process as the information bottleneck. <PERSON> et al. (2023a) applied the GIB principle to molecular relational learning tasks. In practice, it is common for these methods to recognize dis-connected substructures rather than connected fragments. Subgraph recognition by GIB has been only employed in classification and regression tasks, and this is the first work that applies GIB to fragment extraction.", "section": "Related Work", "sec_num": "2."}, {"text": "We introduce our Goal-aware fragment Extraction, Assembly, and Modification (GEAM) framework which aims to generate molecules that satisfy the target properties with goal-aware fragments. We first describe the goal-aware fragment extraction method in Sec. 3.1. Then we describe the fragment assembly method in Sec. 3.2. Finally, we describe the fragment modification method, the dynamic vocabulary update, and the resulting GEAM in Sec. 3.3.", "section": "Method", "sec_num": "3."}, {"text": "Assume we are given a set of N molecular graphs G i with their corresponding properties Y i ∈ [0, 1], denoted as", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "D = {(G i , Y i )} N i=1 . Each graph G i = (X i , A i", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": ") consists of n nodes with a node feature matrix X i ∈ R n×d and an adjacency matrix A i ∈ R n×n . Let V be a set of all nodes from the graphs G = {G i } N i=1 and let E be a set of all edges from G. Our goal is to extract goal-aware fragments from G such that we can assemble these fragments to synthesize graphs with desired properties. To achieve this goal, we propose Fragment-wise Graph Information Bottleneck (FGIB), a model that learns to identify salient fragments of G i for predicting the target property Y i .", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "Concretely, we first decompose a set of graphs G into M candidate fragments, denoted as F with BRICS (<PERSON> et al., 2008) , a popular method that fragmentizes molecules into retrosynthetically interesting substructures. Each fragment F = (V, E) ∈ F is comprised of vertices V ⊂ V and edges E ⊂ E. Then each graph G can be represented as m fragments, {F j = (V j , E j )} m j=1 , with F j ∈ F. Inspired by graph information bottleneck (<PERSON> et al., 2020) , FGIB aims to identify a subset of fragments G sub that is maximally informative for predicting the target property Y while maximally compressing the original graph G:", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "EQUATION", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "where β > 0 and I(X, Y ) denotes the mutual information between the random variables X and Y .", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "FGIB first calculates the node embeddings {h} n i=1 from the graph G with MPNN (<PERSON> et al., 2017) and use average pooling to obtain the fragment embedding e j of the fragment F j = (V j , E j ) as follows:", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "EQUATION", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "where v l denotes the node whose corresponding node embedding is h l . Using a MLP with a sigmoid activation function at the last layer, we obtain w j ∈ [0, 1], the importance of the fragment F j for predicting the target property Y , as w j = MLP(e j ). We denote θ as the parameters of the MPNN and the MLP. Following <PERSON> et al. (2022) , we inject a noise to the fragment embedding e j according to w j to control the information flow from F j as follows: ẽj = w j e j + (1 -w j ) μ + ϵ,", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "EQUATION", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "where μ ∈ R d and Σ ∈ R d×d denote an empirical mean vector and a diagonal covariance matrix estimated from {e j } m j=1 , respectively. Intuitively, the more a fragment is considered irrelevant for predicting the target property (i.e., small weight w), the more the transmission of the fragment information is blocked. Let Z = vec([ẽ 1 • • • ẽm ]) be the embedding of the perturbed fragments, which is a Gaussian-distributed random variable, i.e., p θ (Z|G) = N (µ θ (G), Σ θ (G)). Here, vec is a vectorization of a matrix, and µ θ (G) and Σ θ (G) denote the mean and the covariance induced by the MPNN and the MLP with the noise ϵ, respectively. Assuming no information loss in the fragments after encoding them, our objective function in Eq. ( 1) becomes to optimize the parameters θ such that we can still predict the property Y from the embedding Z while minimizing the mutual information between G and Z:", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "min θ -I(Z, Y ; θ) + βI(Z, G; θ) LIB(θ)", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": ".", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "(4)", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "Following <PERSON><PERSON><PERSON> et al. (2017) , we derive the upper bound of L IB (θ) with variational inference (<PERSON><PERSON><PERSON> et al., 2016) :", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "EQUATION", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "where q ϕ is a property predictor that takes the perturbed fragment embedding Z as an input, u(Z) is a variational distribution that approximates the marginal p θ (Z), and", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "Z i is drawn from p θ (Z|G i ) = N (µ θ (G i ), Σ θ (G i )) for i ∈ {1, . . . , N }.", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "We optimize θ and ϕ to minimize the objective function L(θ, ϕ). Note that the variational distribution u(•) is chosen to be Gaussian, enabling analytic computation of the KL divergence. A detail proof is included in Sec. B.", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "After training FGIB, we calculate score(F j ) ∈ [0, 1] of each fragment F j = (V j , E j ) ∈ F with FGIB as follows:", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "EQUATION", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "where", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "S(F j ) = {(G, Y ) ∈ D : F j is a subgraph of G} and w j (G, F j", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": ") is an importance of the fragment F j in the graph G, computed as Eq. ( 3). Intuitively, the score quantifies the extent to which a fragment contributes to achieving a high target property. Specifically, the term w j (G, F j )/ |V j | measures how much a fragment contributes to its whole molecule in terms of the target property, while the term Y measures the property of the molecule. As the number of nodes of the fragment becomes larger, FGIB is more likely to consider it important when predicting the property. To normalize the effect of the fragment size, we include |V j | in the first term. Based on the scores of all fragments, we choose the top-K fragments as the goal-aware vocabulary S ⊂ F for the subsequent generation of molecular graphs with desired properties.", "section": "Goal-aware Fragment Extraction", "sec_num": "3.1."}, {"text": "The next step is to generate molecules with the extracted goal-aware fragment vocabulary. For generation, we introduce the fragment assembly module, Soft actor-critic (SAC) (<PERSON><PERSON><PERSON><PERSON> et al., 2018) , that learns to assemble the fragments to generate molecules with desired properties.", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "We formulate fragment assembly as a RL problem following <PERSON> et al. (2021) . Given a partially generated molecule g t which becomes a state s t at time step t, a policy network adds a fragment to g t through a sequence of three actions: (1) the attachment site of g t to use in forming a new bond, (2) the fragment F ∈ S to be attached to g t , and (3) the attachment site of F to use in forming a new bond. Following <PERSON> et al. (2021) , we encode the nodes of the graph g t with GCN (<PERSON> & Welling, 2017) as H = GCN(g t ) and obtain the graph embedding with sum pooling as h gt = Sum(H). We parameterize the policy network π with three sub-policy networks to sequentially choose actions conditioned on previous ones:", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "pπ 1 (•|st) = π1(Z1), Z1 = f1(hg t , Hatt) pπ 2 (•|a1, st) = π2(Z2), Z2 = f2(z1,a 1 , ECFP(S)) (7) pπ 3 (•|a1:2, st) = π3(Z3), Z3 = f3(Sum(GCN(Fa 2 )), Hatt,F a 2 )", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "where H att denotes the node embeddings of the attachment sites and a 1:2 = (a 1 , a 2 ). We employ multiplicative interactions (<PERSON> et al., 2020) for f 1 , f 2 and f 3 to fuse two inputs from heterogeneous spaces. The first policy network π 1 outputs categorical distribution over attachment sites of the current graph g t conditioned on h gt and H att , and chooses the attachment site with a 1 ∼ p π1 (•|s t ). The second policy network π 2 selects the fragment F a2 ∈ S with a 2 ∼ p π2 (•|a 1 , s t ), conditioned on the embedding of the previously chosen attachment site z 1,a1 and the ECFPs of all the fragments ECFP(S). Then we encode the node embeddings of the fragment F a2 with the same GCN as H Fa 2 = GCN(F a2 ), and get the fragment embedding h Fa 2 = Sum(H Fa 2 ). The policy network π 3 chooses the attachment site of the fragment F a2 with a 3 ∼ p π3 (•|a 1:2 , s t ), conditioned on the fragment embedding h Fa 2 and the attachment site embeddings of the fragment H att,Fa 2 . Finally, we attach the fragment F a2 to the current graph g t with the chosen attachment sites a 1 and a 3 , resulting in a new graph g t+1 . With T steps of sampling actions (a 1 , a 2 , a 3 ) using these policy networks, we generate a new molecule g T = G, call the oracle to evaluate the molecule G and calculate the reward r T .", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "With the SAC objective (<PERSON><PERSON><PERSON><PERSON> et al., 2018) , we train the policy network π as follows: (<PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2017) to optimize Eq. ( 8).", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "Let π be a policy that is at least as good as a random policy with respect to the choice of fragments and optimal with respect to other actions given a chosen fragment, i.e., p π2 (a 2,t |a t,1 , s t ) is not worse than the uniform distribution, and p π1 (a t,1 |s t ) and p π3 (a 3,t |a 1:2,t , s t ) are optimal. Define φ(S) to be the set of all optimal and novel molecule graphs that can be constructed from a given S and |S| T to be the size of the all possible combination of actions without restricting the validity. Proposition 3.1 shows that the failure probability decreases when |S| decreases or φ(S) increases at the rate of", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "1 + N log( |S| T |S| T -φ(S) ) -1 . The proof is presented in Sec. C.", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "1 We set the intermediate rewards to 0.05, so that only final molecules are evaluated by the oracle.", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "Proposition 3.1. The probability of π failing to generate at least one optimal G ∈ φ(S) is at most p where p =", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "(1 + N log( |S| T |S| T -φ(S) )) -1 if |S| T ̸ = φ(S) and p = 0 if |S| T = φ(S). Since I(G sub , G) = I(G sub , Y ) + I(G sub , G | Y ), FGIB maximizes (1 -β)I(G sub , Y ) -βI(G sub , G | Y ).", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "Thus, FGIB is designed to maximize φ(S) by maximizing I(G sub , Y ) while minimizing |S| by minimizing", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "I(G sub , G | Y ).", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "The benefit of FGIB is that it maximizes φ(S) when compared to previous methods of selecting a random set S (with the same size |S|), and that it minimizes |S| when compared to the approach of using S as S, where S is the set of all fragments of training data. However, φ(S) is upper bounded by φ(S) with the combination of FGIB and SAC. To further increase φ(S) beyond the value of φ(S), we propose a novel approach of the dynamic update of S in the next subsection.", "section": "Fragment Assembly", "sec_num": "3.2."}, {"text": "With the fragment assembly module only, we cannot generate molecules consisting of fragments not included in the predefined vocabulary, which hinders generation of diverse molecules and precludes exploration beyond the vocabulary. To overcome this problem, we introduce the fragment modification module, which utilizes a genetic algorithm (GA) to generate molecules that contain novel fragments.", "section": "Fragment Modification and Dynamic Vocabulary Update", "sec_num": "3.3."}, {"text": "Specifically, we employ a graph-based GA (Jensen, 2019) .", "section": "Fragment Modification and Dynamic Vocabulary Update", "sec_num": "3.3."}, {"text": "At the first round of the GA, we initialize the population with the top-P molecules generated by the fragment assembly module. The GA then selects parent molecules from the population and generates offspring molecules by performing crossover and mutation. As a consequence of the crossover and mutation operations, the generated offspring molecules contain novel fragments not in the initial vocabulary. In the subsequent rounds, we choose the top-P molecules generated so far by both SAC and GA to construct the GA population of the next round.", "section": "Fragment Modification and Dynamic Vocabulary Update", "sec_num": "3.3."}, {"text": "We iteratively run the fragment assembly module described in Sec. 3.2 and the fragment modification in turn, and this generative scheme is referred to as GEAM-static. To further enhance molecular diversity and novelty, we propose incorporating the fragment extraction module into this generative cycle. Concretely, in each cycle after the fragment assembly and the fragment modification modules generate molecules, FGIB extracts novel goal-aware fragments S ′ from the offspring molecules as described in Sec. 3.1. Then the vocabulary is dynamically updated as S ∪ S ′ . When the size of the vocabulary becomes larger than the maximum size L, we choose the top-L fragments as the vocabulary based on the scores in Eq. ( 6). The fragment assembly module assembles fragments of the updated vocabulary in the next iteration, and we refer to this generative framework as GEAM. The single generation cycle of GEAM is described in Algorithm 1 in Sec. A.", "section": "Fragment Modification and Dynamic Vocabulary Update", "sec_num": "3.3."}, {"text": "We demonstrate the efficacy of GEAM in two sets of multiobjective molecular optimization tasks simulating realworld drug discovery problems. We first conduct experiments to generate novel molecules that have high binding affinity, drug-likeness, and synthesizability in Sec. 4.1.", "section": "Experiments", "sec_num": "4."}, {"text": "We then experiment on the practical molecular optimization benchmark in Sec. 4.2. We further conduct extensive ablation studies and qualitative analysis in Sec. 4.3. ", "section": "Experiments", "sec_num": "4."}, {"text": "EQUATION", "section": "Experiments", "sec_num": "4."}, {"text": "where DS and SA are the normalized DS and the normalized SA, respectively (Eq. ( 14)). We use ZINC250k (<PERSON> et al., 2012) to train FGIB to predict Y and extract initial fragments. Optimization performance is evaluated with 3,000 generated molecules using the following metrics. Novel hit ratio (%) measures the fraction of unique and novel hits among the generated molecules.", "section": "Experiments", "sec_num": "4."}, {"text": "Here, novel molecules are defined as the molecules that have the maximum Tanimoto similarity less than 0.4 with the molecules in the training set, and hits are the molecules that satisfy the following criteria: DS < (the median DS of known active molecules), QED > 0.5 and SA < 5. Novel top 5% DS (kcal/mol) measures the average DS of the top 5% unique, novel hits. parp1, fa7, 5ht1b, braf and jak2 are used as the protein targets the docking scores are calculated for. In addition, we evaluate the fraction of novel molecules, novelty (%), and the extent of chemical space covered, #Circles (<PERSON><PERSON> et al., 2023) of the generated hits. The experimental details are provided in Sec. D.1 and D.2.", "section": "Experiments", "sec_num": "4."}, {"text": "Baselines REINVENT (<PERSON><PERSON><PERSON> et al., 2017 ) is a SMILES-based RL model with a pretrained prior. Graph GA (Jensen, 2019 ) is a GA-based model that utilizes predefined crossover and mutation rules. MORLD (Jeon & Kim, 2020 ) is a RL model that uses the MolDQN algorithm (<PERSON> et al., 2019) . HierVAE (<PERSON> et al., 2020a ) is a VAE-based model that uses the hierarchical motif representation of molecules. RationaleRL (<PERSON> et al., 2020b ) is a RL model that first identifies subgraphs that are likely responsible for the target properties (i.e., rationale) and then extends them to complete molecules. FREED (<PERSON> et al., 2021 ) is a RL model that assembles the fragments obtained using CReM (<PERSON><PERSON>, 2020) . PS-VAE (<PERSON> et al., 2022 ) is a VAE-based model that uses the mined principal subgraphs as the building blocks. MOOD (<PERSON> et al., 2023b ) is a diffusion model that incorporates an out-ofdistribution control to enhance novelty. The results of additional baselines are included in Table 7 and Table 8 .", "section": "Experiments", "sec_num": "4."}, {"text": "The results are shown in Table 1 and Table 2 . GEAM and GEAM-static significantly outperform all the baselines in all the tasks, demonstrating that the proposed goal-aware extraction method and the proposed combination of SAC and GA are highly effective in discovering novel, drug-like, and synthesizable drug candidates that have high binding affinity. GEAM shows comparable or better performance than GEAM-static, and as shown in Table 3 and Table 4 , the usage of the dynamic vocabulary update enhances novelty and diversity without degrading optimization performance. There is a general trend that the more powerful the molecular optimization model, the less likely it is to generate diverse molecules (Gao et al., 2022) , but GEAM effectively overcomes this trade-off by discovering novel and high-quality goal-aware fragments on-thefly. Note that the high novelty values of MORLD are trivial due to its poor optimization performance and very low diversity. Similarly, the high diversity values of RationaleRL on the target proteins 5ht1b and jak2 are not meaningful due to its poor optimization performance and novelty.", "section": "Results", "sec_num": null}, {"text": "Setup We validate GEAM in the seven multi-property objective (MPO) optimization tasks of the practical molecular optimization (PMO) benchmark (<PERSON> et al., 2022) , which are the tasks in the Guacamol benchmark (<PERSON> et al., 2019) with the constraints of the number of oracle calls to simulate realistic drug discovery. The experimental details are provided in Sec. D.1 and D.3.", "section": "Optimization of Multi-property Objectives in PMO Benchmark", "sec_num": "4.2."}, {"text": "Baselines We use the top three models reported by <PERSON> et al. (2022) as our baselines. Note that since there are a total of 25 baselines in the paper of <PERSON> et al. (2022) , outperforming against the top three is equivalent to outperforming against the 25 baselines. In addition to REIN-VENT (<PERSON><PERSON><PERSON> et al., 2017) and Graph GA (Jensen, 2019) , STONED (<PERSON><PERSON> et al., 2021 ) is a GA-based model that manipulates SELFIES strings.", "section": "Optimization of Multi-property Objectives in PMO Benchmark", "sec_num": "4.2."}, {"text": "Table 1 : Novel hit ratio (%) results. The results are the means and the standard deviations of 3 runs. The results for the baselines except for RationaleRL and PS-VAE are taken from <PERSON> et al. (2023b) . The best results are highlighted in bold.", "section": "Optimization of Multi-property Objectives in PMO Benchmark", "sec_num": "4.2."}, {"text": "Target protein parp1 fa7 5ht1b braf jak2 REINVENT (<PERSON><PERSON><PERSON> et al., 2017) 0.480 (± 0.344) 0.213 (± 0.081) 2.453 (± 0.561) 0.127 (± 0.088) 0.613 (± 0.167) Graph GA (Jensen, 2019) 4.811 (± 1.661) 0.422 (± 0.193) 7.011 (± 2.732) 3.767 (± 1.498) 5.311 (± 1.667) MORLD (Jeon & Kim, 2020) 0.047 (± 0.050) 0.007 (± 0.013) 0.880 (± 0.735) 0.047 (± 0.040) 0.227 (± 0.118) HierVAE (<PERSON> et al., 2020a) 0.553 (± 0.214) 0.007 (± 0.013) 0.507 (± 0.278) 0.207 (± 0.220) 0.227 (± 0.127) RationaleRL (<PERSON> et al., 2020b) 4.267 (± 0.450) 0.900 (± 0.098) 2.967 (± 0.307) 0.000 (± 0.000) 2.967 (± 0.196) FREED (<PERSON> et al., 2021) 4.627 (± 0.727) 1.332 (± 0.113) 16.767 (± 0.897) 2.940 (± 0.359) 5.800 (± 0.295) PS-VAE (Kong et al., 2022) 1.644 (± 0.389) 0.478 (± 0.140) 12.622 (± 1.437) 0.367 (± 0.047) 4.178 (± 0.933) MOOD (<PERSON> et al., 2023b) 7.017 (Jin et al., 2020b) 9.300 (± 0.354) 9.802 (± 0.166) 7.133 (± 0.141) 0.000 (± 0.000) 7.389 (± 0.220) FREED (Yang et al., 2021) 74.640 (± 2.953) 78.787 (± 2.132) 75.027 (± 5.194) 73.653 (± 4.312) 75.907 (± 5.916) PS-VAE (Kong et al., 2022) 60.822 (± 2.251) (Olivecrona et al., 2017) 44.2 (± 15.5) 23.2 (± 6.6) 138.8 (± 19.4) 18.0 (± 2.1) 59.6 (± 8.1) MORLD (Jeon & Kim, 2020) 1.4 (± 1.5) 0.2 (± 0.4) 22.2 (± 16.1) 1.4 (± 1.2) 6.6 (± 3.7) HierVAE (Jin et al., 2020a) 4.8 (± 1.6) 0.8 (± 0.7) 5.8 (± 1.0) 3.6 (± 1.4) 4.8 (± 0.7) RationaleRL (Jin et al., 2020b) 61.3 (± 1.2) 2.0 (± 0.0) 312.7 (± 6.3) 1.0 (± 0.0) 199.3 (± 7.1) FREED (Yang et al., 2021) 34.8 (± 4.9) 21.2 (± 4.0) 88.2 (± 13.4) 34.4 (± 8.2) 59.6 (± 8.2) PS-VAE (Kong et al., 2022) 38.0 (± 6.4) 18.0 (± 5.9) 180.7 (± 11.6) 16.0 (± 0.8) 83.7 (± 11.9) MOOD (Lee et al., 2023b) 86.4 (± 11.2) 19.2 (± 4.0) 144.4 (± 15.1) 50.8 (± 3.8) 81.8 (± 5.7) GEAM-static (ours) 114.0 (± 2.9) 60.7 (± 4.0) 134.7 (± 8.5) 70.0 (± 2.2) 99.3 (± 1.7) GEAM (ours) 123.0 (± 7.8) 79.0 (± 9.2) 144.3 (± 8.6) 84.7 (± 8.6) 118.3 (± 0.9) Results As shown in Table 5 , GEAM outperform the baselines in most of the tasks, demonstrating its applicability to various drug discovery problems. Notably, GEAM distinctly improves the performance of GEAM-static in some tasks. Furthermore, as shown in Table 6 , GEAM shows higher levels of novelty and diversity than others. Especially, GEAM generates more novel and diverse molecules than GEAM-static, reaffirming the dynamic vocabulary update of GEAM effectively improves novelty and diversity without degrading optimization performance.", "section": "Method", "sec_num": null}, {"text": "Effect of the goal-aware fragment extraction To examine the effect of the proposed goal-aware fragment extraction method with FGIB, in Figure 3 (a), we compare FREED with FREED (FGIB), a variant of FREED that uses the fragments extracted by FGIB as described in Sec. 3.1. FREED (FGIB) outperforms FREED by a large margin, indicating the goal-aware fragment extraction method with FGIB largely boosts the optimization performance. We also compare GEAM against GEAM with different fragment vocabularies in Figure 3 (b). GEAM (FREED), GEAM (MiCaM), GEAM (BRICS) are the GEAM variants that use the FREED vocabulary, the MiCaM (Geng et al., 2023) vocabulary, the random BRICS (<PERSON> et al., 2008) vocabulary, respectively. GEAM (property) is GEAM which only uses the property instead of Eq. ( 6) when scoring fragments, i.e., score(F j ) = 1 |S(Fj )| (G,Y )∈S(Fj ) Y . GEAM significantly outperforms all the variants, verifying the importance of our goal-aware fragment vocabulary. Notably, GEAM (property) uses the topmost fragments in terms of the target property, but performs worse than GEAM because it does not use FGIB to find important subgraphs that contribute to the property.", "section": "Ablation Studies and Qualitative Analysis", "sec_num": "4.3."}, {"text": "To examine the effect of the proposed combinatorial use of the assembly and the modification modules, we compare GEAM with GEAM-w/o A and GEAM-w/o M in Figure 3(c) . GEAM-w/o A constructs its population as the top-P molecules from ZINC250k without using the assembly module, while GEAM-w/o M omits the modification module. GEAM-random A uses random fragment assembly instead of SAC. GEAM-w/o A significantly underperforms as the fragment modification module alone cannot take the advantage of the goal-aware fragments. GEAMrandom A largely improves over GEAM-w/o A. However, GEAM outperforms all the ablated variants, highlighting the importance of jointly employing the fragment assembly module and the fragment modification module. Effect of the dynamic vocabulary update To thoroughly examine the effect of the dynamic update of the fragment vocabulary, we compare the generation progress of GEAM and GEAM-static in Figure 4 . GEAM-static-1000 is GEAM-static with the vocabulary size K = 1,000.", "section": "Effect of the fragment assembly and modification", "sec_num": null}, {"text": "When the initial vocabulary size K = 300 and the maximum vocabulary size L = 1,000, the vocabulary size of GEAM increases during generation from 300 to 1,000 as GEAM dynamically collects fragments on-the-fly. Meanwhile, the vocabulary sizes of GEAM-static and GEAMstatic-1000 are fixed. As expected, GEAM-static-1000 shows the worst optimization performance since its vocabulary consists of top-1,000 fragments instead of top-300 from the same training molecules, and shows the highest diversity as it utilizes more fragments than GEAM and GEAM-static throughout the generation process. GEAM shows the best optimization performance and novelty thanks to the vocabulary update that constantly incorporates novel fragments outside the training molecules, as well as improved diversity compared to GEAM-static.", "section": "Effect of the fragment assembly and modification", "sec_num": null}, {"text": "We qualitatively analyze the extracted goal-aware fragments. In Figure 3 (d), we present an example of the binding interactions of a molecule and the target protein jak2 using the protein-ligand interaction profiler (PLIP) (<PERSON><PERSON><PERSON> et al., 2021) . More case studies are provided in Figure 7 . Additionally, we show the fragments of the molecule and w of the fragments calculated by FGIB. We observe that the important fragments identified by FGIB with high w (red and blue) indeed play crucial role for interacting with the target protein, while the fragments with low w (gray) are not involved in the interactions. This analysis validates the efficacy of the proposed goal-aware fragment extraction method using FGIB and suggests the application of FGIB as a means to improve the explainability of drug discovery.", "section": "Qualitative analysis", "sec_num": null}, {"text": "We proposed GEAM, a fragment-based molecular generative framework for drug discovery. GEAM consists of three modules, FGIB, SAC, and GA, each responsible for goal-aware fragment extraction, fragment assembly, and fragment modification. In the generative cycle of GEAM, FGIB provides goal-aware fragments to SAC, SAC provides high-quality population to GA, and GA provides novel fragments to FGIB, enabling GEAM to achieve superior performance with high novelty and diversity on a variety of drug discovery tasks. These results highlight strong applicability of GEAM to real-world drug discovery.", "section": "Conclusion", "sec_num": "5."}, {"text": "While GEAM has been shown to be effective in real-world drug discovery tasks, there is a risk that it could be used maliciously to generate harmful or toxic molecules. The potential for generating toxic samples is a common challenge across various domains such as molecules, graphs, images and languages. Nevertheless, this risk can be effectively mitigated by excluding toxic fragments from the generation cycle or by incorporating an auxiliary toxicity detection module that filters out the generated toxic molecules in the model service stage. For example, ChatGPT (Ope-nAI, 2022) employs a moderation tool2 to filter out hateful or discriminatory sentences. 6) Output: Generated molecules (s T and o), updated vocabulary S B. Proof of Equation 5In this section, we prove that our objective function L(θ, ϕ) in Eq. ( 5) is the upper bound of the intractable objective L IB (θ) in Eq. (4). At this point, we only have joint data distribution p(G, Y ) and the stochastic encoder p θ (Z|G) = N (µ θ (G), Σ θ (G)).", "section": "Impact Statement", "sec_num": null}, {"text": "Proof. Following standard practice in Information Bottleneck literature (<PERSON><PERSON><PERSON> et al., 2017) , we assume Markov Chains so that joint distribution p θ (G, Z, Y ) factorizes as:", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "EQUATION", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "Firstly, we derive the upper bound of the mutual information between Z and Y :", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "I(Z, Y ; θ) = p θ (y, z) log p θ (y, z) p(y)p θ (z) dydz = p θ (y, z) log p θ (y|z) p(y) dydz,", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "where y and z are realization of random variables Y and Z, respectively. The posterior is fully defined as:", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "p θ (y|z) = g p θ (g, y|z) = g p(y|g)p θ (g|z) = g p(y|g)p θ (z|g)p(g) p θ (z) ,", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "where g is a realization of the random variable G. Since this posterior p θ (y|z) is intractable, we utilize a variational distribution q ϕ (y|z) to approximate the posterior. Since KL divergence is always non-negative, we get the following inequality:", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "D KL (p θ (Y |Z = z) ∥ q ϕ (Y |Z = z)) ≥ 0 ⇒ p θ (y|z) log p θ (y|z)dy ≥ p θ (y|z) log q ϕ (y|z)dy.", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "With this inequality, we get the lower bound of I(Z, Y ):", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "I(Z, Y ; θ) = p θ (y, z) log p θ (y|z) p(y) dydz ≥ p θ (y, z) log q ϕ (y|z) p(y) dydz = p θ (y, z) log q ϕ (y|z)dydz -p θ (y, z) log p(y)dydz = p θ (y, z) log q ϕ (y|z)dydz -log p(y) p θ (y, z)dzdy = p θ (y, z) log q ϕ (y|z)dydz -p(y) log p(y)dy", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "= p θ (y, z) log q ϕ (y|z)dydz + H(Y ),", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "where H(Y ) is the entropy of labels Y . Since Y is ground truth label, it is independent of our parameter θ. It means the entropy is constant for our optimization problem and thus we can ignore it. By the assumption in Eq. ( 10),", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "p θ (y, z) = g p θ (g, y, z) = g p(g)p(y|g)p θ (z|g).", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "Thus, we get the lower bound as follows:", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "EQUATION", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "Now, we derive the upper bound of the mutual information between Z and G:", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "EQUATION", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "The marginal distribution p θ (z) is intractable since", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "p θ (z) = g p θ (z|g)p(g).", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "We utilize another variational distribution u(z) that approximate the marginal. Since KL divergence is always non-negative,", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "D KL (p θ (Z) ∥ r(Z)) ≥ 0 ⇒ p θ (z) log p θ (z)dz ≥ p θ (z) log u(z)dz.", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "Combining this inequality with Eq. ( 12), we get the upper bound as:", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "EQUATION", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "Combining Eq. ( 11) and Eq. ( 13), and using the empirical data distribution p(g, y)", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "= 1 N N n=1 δ Gi (g)δ Yi (y), we get L IB (θ) = -I(Z, Y ; θ) + βI(Z, G; θ) ≤ - p(g)p(y|g)p θ (z|g)dydz + β g p θ (z|g)p(g) log p θ (z|g) u(z) ≈ 1 N N i=1 -p θ (z|G i ) log q ϕ (Y i |z) + βp θ (z|G i ) log p θ (z|g) u(z) dz = 1 N N i=1 E p θ (Z|Gi) [-log q ϕ (Y i |Z)] + βD KL (p θ (Z|G i ) ∥ u(Z)) ≈ 1 N N i=1 (-log q ϕ (Y i |Z i ) + βD KL (p θ (Z|G i ) ∥ u(Z))) = L(θ, ϕ),", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "where we sample", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "Z i from N (µ θ (G i ), Σ θ (G i )) = p θ (Z|G i ). Therefore, we conclude that L(θ, ϕ) is the upper bound of L IB (θ).", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "<PERSON><PERSON>of of Proposition 3.1", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "Proof. Let q π be the distribution over molecule graphs G induced by the policy π. Then, from the assumption that p π2 (a 2,t |a t,1 , s t ) is at least as good as the uniform distribution,", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "Pr G∼qπ [G ∈ φ(S)] ≥ Pr G∼qu [G ∈ φ(S)],", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "where q u is the distribution over G induced by replacing p π2 with p u π2 , which is the the uniform distribution over actions. Here, an action is to chose a fragment in S. Thus, p u π2 (a 2,t |a t,1 , s t ) = 1 |S|-r ′ ≥ 1 |S| for all valid actions a 2,t where r ′ ≥ 0 is the number of invalid actions. Similarly,", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "Pr G∼qu [G ∈ φ(S)] = φ(S) |S| T -r ≥ φ(S) |S| T ,", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "where |S| T is the size of the all possible combinations of actions without restricting the validity and r is the size of invalid combinations of actions. This implies that Pr G∼qπ [G ∈ φ(S)] ≥ φ(S) |S| T , and", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "Pr G∼qπ [G / ∈ φ(S)] = 1 -Pr G∼qπ [G ∈ φ(S)] ≤ 1 -Pr G∼qu [G ∈ φ(S)] ≤ 1 - φ(S) |S| T . Thus, Pr G1,...,G N ∼qπ [∀i ∈ [N ], G i / ∈ φ(S)] ≤ 1 - φ(S) |S| T N .", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "We consider the three cases separately: cases of φ(S) |S| T = 0, φ(S) |S| T = 1, and φ(S) |S| T ∈ (0, 1). In the case of φ(S)", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "|S| T = 0, we have Pr G1,...,G N ∼qπ [∀i ∈ [N ], G i / ∈ φ(S)] ≤ (1 -0) N = 1. In the case of φ(S) |S| T = 1, we have Pr G1,...,G N ∼qπ [∀i ∈ [N ], G i / ∈ φ(S)] ≤ (1 -1) N = 0.", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "For the case of φ(S) |S| T ∈ (0, 1), we define", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "ζ = log( 1 1- φ(S) |S| T", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "). Then, since φ(S) |S| T ∈ (0, 1), we have 1 -φ(S) |S| T ∈ (0, 1),", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "1 1- φ(S) |S| T", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "> 1, and ζ > 0. Thus, we have 1 -φ(S)", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "|S| T N = 1 exp(N ζ) = 1 ∞ k=0 (N ζ) k k! ≤ 1 1+N ζ . This yields that Pr G1,...,G N ∼qπ [∀i ∈ [N ], G i / ∈ φ(S)] ≤ 1 1 + N ζ = 1 1 + N log( |S| T |S| T -φ(S) )", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": ".", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "For the case of the case of φ(S) |S| T = 0 (which implies that φ(S) = 0), this expression recovers the same result as Pr", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "G1,...,G N ∼qπ [∀i ∈ [N ], G i / ∈ φ(S)] ≤ 1 1+N log( |S| T |S| T -φ(S) ) = 1 1+N log(1) = 1. Since φ(S) |S| T = 1 iff φ(S) = |S| T , these implies that Pr G1,...,G N ∼qπ [∀i ∈ [N ], G i / ∈ φ(S)] ≤    1 1+N log( |S| T |S| T -φ(S) ) if φ(S) ̸ = |S| T 0 if φ(S) = |S| T .", "section": "A. Generation Process of GEAM", "sec_num": null}, {"text": "Here, we describe the common implementation details of GEAM throughout the experiments. Following <PERSON> et al. (2021) , <PERSON> et al. (2023b) and <PERSON> et al. (2022) , we used the ZINC250k (<PERSON> et al., 2012) dataset with the same train/test split used by <PERSON><PERSON> et al. (2017) in all the experiments. To calculate novelty, we used the RDKit (Landrum et al., 2016) library to calculate similarities between Morgan fingerprints of radius 2 and 1024 bits. To calculate #Circles, we used the public code3 and set the threshold to 0.75 as suggested by <PERSON><PERSON> et al. (2023) .", "section": "D. Experimental Details D.1. Common Experimental Details", "sec_num": null}, {"text": "The fragment extraction module Regarding the architecture of FGIB, we set the number of message passing in the MPNN to 3 and the number of layers of the MLP to 2. Given the perturbed fragment embedding Z, the property predictor q ϕ first get the perturbed graph embedding with average pooling and pass it through an MLP of 3 layers as Ŷ = MLP ϕ (AvgPool(Z)). FGIB was trained to 10 epochs in each of the task with a learning rate of 1e -3 and β of 1e -5. The initial vocabulary size was set to K = 300. Regarding the dynamic vocabulary update, the maximum vocabulary update in a single cycle was set to 50 and the maximum vocabulary size was set to L = 1,000. Following <PERSON> et al. (2021) , fragments that induce the sanitization error of the RDKit (<PERSON> et al., 2016) library are filtered out in the fragment extraction step.", "section": "D. Experimental Details D.1. Common Experimental Details", "sec_num": null}, {"text": "The fragment assembly module Following <PERSON> et al. (2021) , we formulated fragment assembly as a RL problem and employed soft actor-critic (SAC), because it has been proven to be superior to proximal policy optimization (PPO), another popular RL method, in fragment assembly. Following <PERSON> et al. (2021) , we allowed GEAM to randomly generate molecules during the first 4,000 SAC steps to collect experience. Note that unlike <PERSON> et al. (2021) , we included these molecules in the final evaluation to equalize the total number of oracle calls for a fair comparison. SAC starts each of the generation episode from benzene with attachment points on the ortho-, meta-, para-positions, i.e., c1( * )c( * )ccc( * )c1. We set the termination number of atoms in the SAC to n SAC = 40, so that an episode ends when the size of the current molecule exceeds 40. Other architectural details followed <PERSON> et al. (2021) .", "section": "D. Experimental Details D.1. Common Experimental Details", "sec_num": null}, {"text": "The fragment modification module The population size of the GA was set to P = 100 and the mutation rate was set to 0.1. The minimum number of atoms of generated molecules was set to 15. The crossover and the mutation rules followed those of <PERSON> (2019).", "section": "D. Experimental Details D.1. Common Experimental Details", "sec_num": null}, {"text": "We used the RDKit (<PERSON><PERSON> et al., 2016) library to calculate QED and SA. We used QuickVina 2 (<PERSON><PERSON><PERSON><PERSON> et al., 2015) , a popular docking program, to calculate docking scores with the exhaustiveness of 1. Following <PERSON> et al. (2023b) , we first clip DS in the range [-20, 0] and compute DS and SA to normalize each of the properties in Eq. ( 9) as follows:", "section": "D.2. Optimization of Binding Affinity under QED, SA and Novelty Constraints", "sec_num": null}, {"text": "EQUATION", "section": "D.2. Optimization of Binding Affinity under QED, SA and Novelty Constraints", "sec_num": null}, {"text": "In this way, each property in Eq. ( 9), DS, QED, SA, as well as the total property Y are confined to the range [0, 1].", "section": "D.2. Optimization of Binding Affinity under QED, SA and Novelty Constraints", "sec_num": null}, {"text": "For the baselines, we mostly followed the settings in the respective original paper. For RationaleRL, we used the official code4 . Following the instruction, we extracted the rationales for parp1, fa7, 5ht1b, braf and jak2, respectively, then filtered them out with the QED > 0.5 and the SA < 5 constraints. Each rationale was expanded for 200 times and the model was trained for 50 iterations during the finetune. To generate 3,000 molecules, each rationale was expanded for ⌊ 3,000 # of rationales ⌋ times, then 3,000 molecules were randomly selected. For FREED, we used the official code5 and used the predictive error-PER model. We used the provided vocabulary of 91 fragments extracted by CReM (Polishchuk, 2020) with the ZINC250k dataset and set the target property to Eq. ( 9). Note that this was referred to as FREED-QS in the paper of <PERSON> et al. (2023b) . For PS-VAE, we used the official code6 and trained the model with the target properties described in Eq. ( 9), then generated 3,000 molecules with the trained model. For MiCaM, we used the official code7 to extract fragments from the ZINC250k training set. As this resulted in a vocabulary of large size and worsened the performance when applied to GEAM, we randomly selected K = 300 to construct the final vocabulary. The code regarding goal-directed generation is not publicly available at this time.", "section": "D.2. Optimization of Binding Affinity under QED, SA and Novelty Constraints", "sec_num": null}, {"text": "We directly used the score function in each of the tasks as the property function Y of FGIB. We set the number of the GA reproduction per one SAC episode to 3. For the baselines, the results in Table 5 were taken from <PERSON> et al. (2022) and the novelty and the #Circles results in Table 6 were obtained using the official repository of <PERSON> et al. (2022) 8 . ", "section": "D.3. Optimization of Multi-property Objectives in PMO Benchmark", "sec_num": null}, {"text": "https://platform.openai.com/docs/guides/ moderation/overview", "section": "", "sec_num": null}, {"text": "Scientific reports, 9(1):1-10, 2019.", "section": "", "sec_num": null}, {"text": "https://openreview.net/forum?id=Yo06F8kfMa1", "section": "", "sec_num": null}, {"text": "https://github.com/wengong-jin/multiobj-rationale", "section": "", "sec_num": null}, {"text": "https://github.com/AITRICS/FREED", "section": "", "sec_num": null}, {"text": "https://github.com/THUNLP-MT/PS-VAE", "section": "", "sec_num": null}, {"text": "https://github.com/MIRALab-USTC/AI4Sci-MiCaM", "section": "", "sec_num": null}, {"text": "https://github.com/wenhao-gao/mol_opt", "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by Institute for Information & communications Technology Promotion(IITP) grant funded by the Korea government(MSIT) (No. 2019-0-00075, Artificial Intelligence Graduate School Program(KAIST)), the National Research Foundation of Korea(NRF) grant funded by the Korea government(MSIT) (No. RS-2023-00256259), the National Research Foundation Singapore under the AI Singapore Programme (AISG Award No: AISG2-TC-2023-010-SGIL) and the Singapore Ministry of Education Academic Research Fund Tier 1 (Award No: T1 251RES2207).", "section": "Acknowledgements", "sec_num": null}, {"text": "Table 7 : Novel hit ratio (%) results of additional baselines. The results are the means and the standard deviations of 3 runs. The results for the baselines except for RetMol are taken from <PERSON> et al. (2023b) . The best results are highlighted in bold.", "section": "annex", "sec_num": null}, {"text": "Target protein parp1 fa7 5ht1b braf jak2 GCPN (<PERSON> et al., 2018) 0.056 (± 0.016) 0.444 (± 0.333) 0.444 (± 0.150) 0.033 (± 0.027) 0.256 (± 0.087) JTVAE (<PERSON> et al., 2018) 0.856 (± 0.211) 0.289 (± 0.016) 4.656 (± 1.406) 0.144 (± 0.068) 0.815 (± 0.044) GraphAF (<PERSON> et al., 2019) 0.689 (± 0.166) 0.011 (± 0.016) 3.178 (± 0.393) 0.956 (± 0.319) 0.767 (± 0.098) GA+D (<PERSON><PERSON> et al., 2020) 0.044 (± 0.042) 0.011 (± 0.016) 1.544 (± 0.273) 0.800 (± 0.864) 0.756 (± 0.204) MARS (<PERSON><PERSON> et al., 2020) 1.178 (± 0.299) 0.367 (± 0.072) 6.833 (± 0.706) 0.478 (± 0.083) 2.178 (± 0.545) GEGL (<PERSON><PERSON> et al., 2020) 0.789 (± 0.150) 0.256 (± 0.083) 3.167 (± 0.260) 0.244 (± 0.016) 0.933 (± 0.072) GraphDF (Luo et al., 2021) 0.044 (± 0.031) 0.000 (± 0.000) 0.000 (± 0.000) 0.011 (± 0.016) 0.011 (± 0.016) LIMO (Eckmann et al., 2022) 0.455 (± 0.057) 0.044 (± 0.016) 1.189 (± 0.181) 0.278 (± 0.134) 0.689 (± 0.319) GDSS (Jo et al., 2022) 1.933 (± 0.208) 0.368 (± 0.103) 4.667 (± 0.306) 0.167 (± 0.134) 1.167 (± 0.281) RetMol (Wang et al., 2023) 0.011 (± 0.016) 0.000 (± 0.000) 0.033 (± 0.027) 0.000 (± 0.000) 0.011 ", "section": "Method", "sec_num": null}, {"text": "Target protein parp1 fa7 5ht1b braf jak2 GCPN (<PERSON> et al., 2018) -7.464 (± 0.089) -7.024 (± 0.629) -7.632 (± 0.058) -7.691 (± 0.197) -7.533 (± 0.140) JTVAE (<PERSON> et al., 2018) -9.482 (± 0.132) -7.683 (± 0.048) -9.382 (± 0.332) -9.079 (± 0.069) -8.885 (± 0.026) GraphAF (<PERSON> et al., 2019) -9.327 (± 0.030) -7.084 (± 0.025) -9.113 (± 0.126) -9.896 (± 0.226) -8.267 (± 0.101) GA+D (<PERSON><PERSON> et al., 2020) -8.365 (± 0.201) -6.539 (± 0.297) -8.567 (± 0.177) -9.371 (± 0.728) -8.610 (± 0.104) MARS (<PERSON><PERSON> et al., 2020) -9.716 (± 0.082) -7.839 (± 0.018) -9.804 (± 0.073) -9.569 (± 0.078) -9.150 (± 0.114) GEGL (<PERSON><PERSON> et al., 2020) -9.329 (± 0.170) -7.470 (± 0.013) -9.086 (± 0.067) -9.073 (± 0.047) -8.601 (± 0.038) GraphDF (Luo et al., 2021) -6.823 (± 0.134) -6.072 (± 0.081) -7.090 (± 0.100) -6.852 (± 0.318) -6.759 (± 0.111) LIMO (Eckmann et al., 2022 ) -8.984 (± 0.223) -6.764 (± 0.142) -8.422 (± 0.063) -9.046 (± 0.316) -8.435 (± 0.273) GDSS (Jo et al., 2022) -9.967 (± 0.028) -7.775 (± 0.039) -9.459 (± 0.101) -9.224 (± 0.068) -8.926 (± 0.089) RetMol (Wang et al., 2023) -8.590 (± 0.475) -5.448 (± 0.688) -6.980 (± 0.740) -8.811 (± 0.574) -7.133 (± 0.242) GEAM (ours) -12.891 (± 0.158) -9.890 (± 0.116) -12.374 (± 0.036) -12.342 (± 0.095) -11.816 (± 0.067)", "section": "Method", "sec_num": null}, {"text": "We include the novel hit ratio and the novel top 5% DS results of the additional baselines in Table 7 and Table 8 . As shown in the tables, the proposed GEAM outperforms all the baselines by a large margin.We also provide examples of the generated novel hits by GEAM for each protein target in Figure 5 . The examples were collected without curation.", "section": "E.1. Optimization of Binding Affinity under QED, SA and Novelty Constraints", "sec_num": null}, {"text": "We provide examples of the generated top-5 molecules by GEAM for each task in Figure 6 . The examples are from a single run with a random seed for each task.", "section": "E.2. Optimization of Multi-property Objectives in PMO Benchmark", "sec_num": null}, {"text": "We provide additional PLIP (<PERSON><PERSON><PERSON> et al., 2021) visualizations in Figure 7 . Unlike the other target proteins from DUD-E (<PERSON><PERSON><PERSON> et al., 2012) , the target protein 5ht1b is from ChEMBL (<PERSON><PERSON><PERSON> et al., 2012) , and is omitted as it is incompatible with PLIP. ", "section": "E.3. Qualitative Analysis", "sec_num": null}], "ref_entries": {"FIGREF0": {"uris": null, "fig_num": "1", "text": "Figure 1: (a) The architecture of FGIB. Using the GIB theory, FGIB aims to identify the important subgraphs that contribute much to the target chemical property in the given molecular graphs. The trained FGIB is then used to extract fragments in a molecular dataset in the goal-aware manner. (b) Performance comparison of GEAM and other FBDD methods on the jak2 ligand generation task.", "type_str": "figure", "num": null}, "FIGREF1": {"uris": null, "fig_num": "2", "text": "Figure 2: The overall framework of GEAM. GEAM consists of three modules, FGIB, SAC, and GA for fragment extraction, fragment assembly, and fragment modification, respectively.", "type_str": "figure", "num": null}, "FIGREF2": {"uris": null, "fig_num": "3", "text": "Figure 3: (a-c) Ablation studies on FGIB, SAC and GA on the ligand generation task with the target protein jak2 and (d) the PLIP image showing interactions between an example molecule and jak2.", "type_str": "figure", "num": null}, "FIGREF3": {"uris": null, "fig_num": "4", "text": "Figure 4: The generation progress of GEAM and GEAM-static on the ligand generation task against jak2.", "type_str": "figure", "num": null}, "FIGREF4": {"uris": null, "fig_num": "5", "text": "Figure 5: The examples of the generated novel hits by GEAM. The values of docking score (kcal/mol), QED, SA, and the maximum similarity with the training molecules are provided at the bottom of each molecule.", "type_str": "figure", "num": null}, "TABREF4": {"text": "Novel top 5% docking score (kcal/mol) results. The results are the means and the standard deviations of 3 runs. The results for the baselines except for RationaleRL and PS-VAE are taken from<PERSON><PERSON> et al. (2023b). The best results are highlighted in bold.", "type_str": "table", "html": null, "content": "<table><tr><td>Method</td></tr></table>", "num": null}, "TABREF5": {"text": "Novelty", "type_str": "table", "html": null, "content": "<table><tr><td>Method</td></tr></table>", "num": null}, "TABREF7": {"text": "#Circles of generated hit molecules. The #Circles threshold is set to 0.75. The results are the means and the standard deviations of 3 runs. The results for the baselines except for RationaleRL and PS-VAE are taken from<PERSON><PERSON> et al. (2023b). The best results are highlighted in bold.", "type_str": "table", "html": null, "content": "<table><tr><td>Method</td></tr></table>", "num": null}, "TABREF8": {"text": "PMO MPO AUC top-100 results. The results are the means of 3 runs. The results for the baselines are taken from<PERSON><PERSON> et al. (2022). The best results are highlighted in bold.", "type_str": "table", "html": null, "content": "<table><tr><td>Method</td></tr></table>", "num": null}, "TABREF9": {"text": "PMO MPO novelty (%) / #Circles results. The results are the means of 3 runs. The best results are highlighted in bold.<PERSON><PERSON><PERSON> et al., 2017) 17.0 / 303.7 13.4 / 343.3 25.0 / 452.3 33.1 / 318.3 15.6 / 253.3 15.7 / 398.3 7.6 / 275.3 Graph GA (Jensen, 2019) 61.1 / 258.7 76.2 / 333.3 64.1 / 270.3 44.4 / 278.7 78.2 / 364.7 88.0 / 306.3 41.3 / 272.7 STONED (<PERSON><PERSON> et al., 2021) 82.7 / 303.7 91.6 / 330.3 88.1 / 301.3 65.8 / 301.0 92.4 / 316.7 89.5 / 326.3 63.1 / 280.3 GEAM-static (ours) 83.1 / 412.0 97.6 / 397.7 94.5 / 315.3 93.2 / 318.0 68.9 / 256.7 73.7 / 233.0 76.2 / 267.0 GEAM (ours) 84.2 / 424.0 98.0 / 502.0 97.0 / 435.0 95.3 / 377.3 82.7 / 295.3 86.9 / 257.0 81.7 / 336.0", "type_str": "table", "html": null, "content": "<table><tr><td>Method</td></tr></table>", "num": null}, "TABREF10": {"text": "Algorithm 1 A Single Generation Cycle of GEAM Input: Fragment vocabulary S, termination number of atoms in SAC n SAC , trained FGIB, population size of GA P , maximum vocabulary size L ▷ Fragment assembly Initialize s 0 = benzene for t = 0, 1, . . . do Sample a t from p π1 , p π2 and p π3 in Eq. (7) with S Construct s t+1 by taking a t on s t if no attachment point left in s t+1 or n t+1 > n SAC then T ← t + 1 break end if end for Calculate the property Y of s T Set r T ← Y Train SAC with Eq. (8) ▷ Fragment modification Initialize a population with the top-P molecules generated so far Select parent molecules from the population Perform crossover and mutation to generate an offspring o Calculate the property Y of o ▷ Fragment extraction Extract fragments S ′ from o with FGIB Set S ← the top-L fragments in S ∪ S ′ in terms of Eq. (", "type_str": "table", "html": null, "content": "<table/>", "num": null}}}}