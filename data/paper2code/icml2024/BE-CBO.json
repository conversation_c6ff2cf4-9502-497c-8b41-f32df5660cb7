{"paper_id": "BE-CBO", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:33:41.664155Z"}, "title": "Boundary Exploration for Bayesian Optimization With Unknown Physical Constraints", "authors": [{"first": "Yunsheng", "middle": [], "last": "Tian", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Ane", "middle": [], "last": "Zuniga", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Xi<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": ["P D"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Payel", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Matusik", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Mina", "middle": ["<PERSON><PERSON><PERSON>"], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Bayesian optimization has been successfully applied to optimize black-box functions where the number of evaluations is severely limited. However, in many real-world applications, it is hard or impossible to know in advance which designs are feasible due to some physical or system limitations. These issues lead to an even more challenging problem of optimizing an unknown function with unknown constraints. In this paper, we observe that in such scenarios optimal solution typically lies on the boundary between feasible and infeasible regions of the design space, making it considerably more difficult than that with interior optima. Inspired by this observation, we propose BE-CBO, a new Bayesian optimization method that efficiently explores the boundary between feasible and infeasible designs. To identify the boundary, we learn the constraints with an ensemble of neural networks that outperform the standard Gaussian Processes for capturing complex boundaries. Our method demonstrates superior performance against stateof-the-art methods through comprehensive experiments on synthetic and real-world benchmarks.", "pdf_parse": {"paper_id": "BE-CBO", "_pdf_hash": "", "abstract": [{"text": "Bayesian optimization has been successfully applied to optimize black-box functions where the number of evaluations is severely limited. However, in many real-world applications, it is hard or impossible to know in advance which designs are feasible due to some physical or system limitations. These issues lead to an even more challenging problem of optimizing an unknown function with unknown constraints. In this paper, we observe that in such scenarios optimal solution typically lies on the boundary between feasible and infeasible regions of the design space, making it considerably more difficult than that with interior optima. Inspired by this observation, we propose BE-CBO, a new Bayesian optimization method that efficiently explores the boundary between feasible and infeasible designs. To identify the boundary, we learn the constraints with an ensemble of neural networks that outperform the standard Gaussian Processes for capturing complex boundaries. Our method demonstrates superior performance against stateof-the-art methods through comprehensive experiments on synthetic and real-world benchmarks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Many optimization problems involve the need to optimize black-box functions, where the performance of a sample can only be determined through physical experimentation or time-expensive simulation, which may take even on the order of weeks or months per single experiment. Thus, the total number of evaluations that can be conducted is limited. In this scenario, Bayesian optimization (BO) (<PERSON> et al., 1998; <PERSON><PERSON><PERSON> et al., 2016) has proven to be a successful approach that guides the search for an optimal solution by iteratively proposing which experiment to evaluate that may lead to the highest performance increase.", "cite_spans": [{"start": 389, "end": 409, "text": "(<PERSON> et al., 1998;", "ref_id": "BIBREF33"}, {"start": 410, "end": 433, "text": "<PERSON><PERSON><PERSON> et al., 2016)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In addition to optimizing unknown functions, many practical problems include unknown constraints. For untested samples, it is impossible to determine if a particular combination of design parameters will lead to feasible or infeasible design. In such cases, infeasible regions are typically discovered when pushing the limits of what is physically possible, and the optimal solution lies on the boundary of feasible regions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "For example, consider the problem of designing an airplane wing. The goal is to make it as lightweight as possible while also being durable and structurally strong to sustain the forces. Hence, the optimization needs to reduce the amount of used material until the wing starts breaking under applied forces. The optimal design will be found just before we step into the infeasible range. Similarly, imagine the task of moving a linear stage to a desired position by applying an acceleration followed by deceleration. To optimize this process, the optimizer might progressively increase the voltage applied to achieve faster and more efficient movement. However, if the voltage is continuously increased without considering the motor's limitations, it can lead to excessive heat generation, effectively burning the motor and causing performance degradation or failure. Comparable examples can be found in chemistry and materials science, especially in formulation development where often certain amounts/combinations of ingredients are needed to yield feasible materials, and the actual properties of interest can be measured only for feasible materials. This leads to a situation in which the optimizer is proposing a candidate for which no performance feedback can be obtained, resulting in a BO iteration in which no new information is retrieved. Indeed, while analyzing many real-world benchmark problems, we observe that all these problems have the optimal solution on the boundary between feasible and infeasible designs (further details in Section 5.1).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Motivated by this observation, we argue that a good model of the boundary and efficient search around it can significantly improve efficacy and lead to the discovery of better solutions compared to exploring the entire design space. We propose a novel BO algorithm incorporating a boundary exploration strategy tailored for exploiting this problem structure, which is prevalent in real-world problems with unknown physical constraints. In case of unknown physical constraints we need to model the space of feasible and infeasible designs. In physical experiments a sample is considered infeasible if it fails before being able to measure its performance, therefore such sample does not provide any continuous value in return. Hence, we train a binary classifier to represent all constraints that may appear in the system. Furthermore, it is important to note the difference between imposed constraints and scenarios where the constraints are unknown. In the former, when the constraints are specified by the user, imposed artificially, the optimum solution may be found in the interior of the feasible region. The case of boundary optima is considerably more challenging than that of interior optima, more even so for BO, where gradients are infeasible to evaluate and the number of function evaluations is subject to a limited budget. For unknown constraints, a surrogate needs to be sufficiently accurate (at least in the vicinity of the optimum) so that the identified solution is sufficiently close to the feasible side of the boundary while not trespassing on the other side.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "While existing approaches (<PERSON><PERSON><PERSON><PERSON> et al., 2014; <PERSON> & Poloczek, 2021; <PERSON>, 2021) have addressed BO with unknown constraints, our method is the first to explicitly consider the boundary issue and it demonstrates superior performance. In summary, our main contributions are the following:", "cite_spans": [{"start": 26, "end": 48, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF18"}, {"start": 49, "end": 75, "text": "Eriksson & Poloczek, 2021;", "ref_id": "BIBREF11"}, {"start": 76, "end": 90, "text": "Antonio, 2021)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We introduce BE-CBO (Boundary Exploration for Constrained Bayesian Optimization) for optimizing blackbox functions with a limited evaluation budget and unknown constraints. Our key insight is that accurately modeling and efficiently exploring the boundary between feasible and infeasible designs is crucial in discovering better performing designs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• To model the unknown constraint boundary, we propose using Deep Ensembles and demonstrate its superior modeling capability by comparing against the most common method in surrogate modeling for BO, i.e., Gaussian Processes.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• Comprehensive experiments and ablation studies on synthetic functions and real-world benchmark problems showing the efficiency of our method and stateof-the-art performance on practical setups.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Constrained Bayesian Optimization BO has proven to be a powerful methodology for global optimization of blackbox functions with expensive evaluations (<PERSON><PERSON><PERSON> et al., 2016) . It has demonstrated remarkable success in various applications, including robotics (<PERSON><PERSON> et al., 2007) , resource allocation (<PERSON><PERSON><PERSON> et al., 2019) , hyperparameter tuning (<PERSON><PERSON><PERSON> et al., 2012) , experimental design (<PERSON><PERSON><PERSON> et al., 2010) , and clinical drug trials (<PERSON> et al., 2019) . In classical BO, the feasible set X is assumed to be known and easy to evaluate, e.g., they are either a hyper-cubes or a simplex (<PERSON>, 1975; <PERSON><PERSON>, 2018) . Recent research faces a more challenging scenario called constrained Bayesian optimization (CBO) , where the feasiblility of optimization variables is unknown or hard to evaluate. In these cases, the evaluation of the feasibility of one solution is also time costly as the objective function. Two slightly different settings are considered in CBO: 1) continuous-valued constraint and 2) binary-valued constraint. In continuousvalued CBO, the constraints take the form of inequality constraint R(x) ≥ 0, where R : R d → R is a continuousvalued function. It considers the scenario where both the function value and the constraints' values can be obtained from the experiments, even if the sample falls into the infeasible region. In the binary-constraint case, the outcome of the experiment becomes 1 R(x)≥0 f (x), that we only observe the function value when the sample is feasible (R(x) ≥ 0), and when the sample falls into the infeasible region, we observe a failure with no function value.", "cite_spans": [{"start": 150, "end": 174, "text": "(<PERSON><PERSON><PERSON> et al., 2016)", "ref_id": null}, {"start": 260, "end": 282, "text": "(<PERSON><PERSON> et al., 2007)", "ref_id": "BIBREF45"}, {"start": 305, "end": 327, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF29"}, {"start": 352, "end": 372, "text": "(<PERSON><PERSON><PERSON> et al., 2012)", "ref_id": null}, {"start": 395, "end": 418, "text": "(<PERSON><PERSON><PERSON> et al., 2010)", "ref_id": null}, {"start": 446, "end": 463, "text": "(<PERSON> et al., 2019)", "ref_id": null}, {"start": 596, "end": 610, "text": "(<PERSON><PERSON><PERSON>, 1975;", "ref_id": "BIBREF48"}, {"start": 611, "end": 625, "text": "<PERSON><PERSON><PERSON>, 2018)", "ref_id": "BIBREF14"}, {"start": 719, "end": 724, "text": "(CBO)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Prior works mainly consider continuous-valued constraints. <PERSON> et al. (2014) proposes EIC that multiples the Expected Improvement (EI) (<PERSON><PERSON><PERSON>, 1975) with the probability of constraint satisfaction as the acquisition function. PESC (<PERSON> et al., 2015) extends Predicted Entropy Search (PES) (<PERSON> et al., 2014) to the constrained case. ADMMBO (<PERSON><PERSON> et al., 2019) uses ADMM to alternatively optimize the objective value and feasibility of the solution. SCBO (Eriksson & Poloczek, 2021) uses a trust region optimizer to scale up to high dimensional problems with constraints. 2-OPT-C (<PERSON> et al., 2021) applies a multi-step lookahead approach instead of the standard myopic approach, which encourages sampling the boundary between feasible and infeasible regions, sharing a similar motivation as our method. All methods use Gaussian Process (GP) regressors to model the unknown constraints.", "cite_spans": [{"start": 59, "end": 80, "text": "<PERSON> et al. (2014)", "ref_id": "BIBREF15"}, {"start": 139, "end": 153, "text": "(<PERSON><PERSON><PERSON>, 1975)", "ref_id": "BIBREF48"}, {"start": 236, "end": 267, "text": "(<PERSON> et al., 2015)", "ref_id": "BIBREF28"}, {"start": 307, "end": 338, "text": "(<PERSON> et al., 2014)", "ref_id": "BIBREF27"}, {"start": 371, "end": 393, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF1"}, {"start": 488, "end": 515, "text": "(Eriksson & Poloczek, 2021)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "The binary-valued CBO is much less considered in the prior works, though the setup is prevalent in practice for optimizing physical systems. Modeling the binary-valued constraint of the feasibility set is fundamentally different from modeling the continuous-valued constraints. Similar to <PERSON> et al. (2014 ), <PERSON><PERSON><PERSON><PERSON> et al. (2014) proposes CEI that mul-tiples the EI with the probability of constraint satisfaction. Lindberg & Lee (2015) uses a slightly different formulation by using asymmetric entropy as the feasibility score for efficient exploration. However, these methods use GP classifiers (GPC) for relatively simple and low dimensional constraints and the efficacy on complicated constraints is not demonstrated. A recent work SVM-CBO (Antonio, 2021) proposes a two-stage approach. In the first stage, it trains an SVM classifier to explore the constraint boundary, and in the second stage, the algorithm performs BO in the feasible region captured by the SVM.", "cite_spans": [{"start": 289, "end": 309, "text": "<PERSON> et al. (2014", "ref_id": "BIBREF15"}, {"start": 310, "end": 334, "text": "), <PERSON><PERSON><PERSON><PERSON> et al. (2014)", "ref_id": "BIBREF18"}, {"start": 420, "end": 441, "text": "Lindberg & Lee (2015)", "ref_id": "BIBREF43"}, {"start": 749, "end": 764, "text": "(Antonio, 2021)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "In some scenarios, ensuring the safety and reliability of evaluations have become important concerns when running BO. To address these concerns, safe BO techniques have emerged as a promising solution (<PERSON><PERSON> et al., 2015; <PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2019) . A representative application of safe BO can be found in robotics (<PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) due to the concern of robot hardware damage during optimization. However, although constrained BO and safe BO share a similar spirit, the main objective in constrained BO is to achieve higher performance while safe BO puts more priority on being safe, i.e., conservative on violating the constraint and exploring infeasible design space, which will be less effective in terms of finding the optimum.", "cite_spans": [{"start": 201, "end": 219, "text": "(<PERSON><PERSON> et al., 2015;", "ref_id": null}, {"start": 220, "end": 243, "text": "<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": null}, {"start": 244, "end": 267, "text": "<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF37"}, {"start": 335, "end": 357, "text": "(<PERSON><PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF4"}, {"start": 358, "end": 382, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF7"}], "ref_spans": [], "eq_spans": [], "section": "Safe Bayesian Optimization", "sec_num": null}, {"text": "Neural Networks for Classification Though GPC has been used in CBO algorithms for modeling the constraints (<PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> & Lee, 2015) , they face the issue of high computational complexity in both theory and lack of supporting software packages, and require a careful choice of the GP kernel, or otherwise the resulting constraint boundaries might be too smooth and lack details.", "cite_spans": [{"start": 107, "end": 128, "text": "(<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF2"}, {"start": 129, "end": 150, "text": "Lin<PERSON>berg & Lee, 2015)", "ref_id": "BIBREF43"}], "ref_spans": [], "eq_spans": [], "section": "Safe Bayesian Optimization", "sec_num": null}, {"text": "In contrast, Neural Networks (NN) have been widely used and have reached great success in classification problems with both low-and high-dimensional data with complex boundaries, including manually selected features (<PERSON><PERSON> et al., 2012) and raw data such as images (<PERSON> <PERSON>, 2007) , texts (<PERSON><PERSON> et al., 2021), and graphs (<PERSON> et al., 2018) . Despite impressive classification accuracies in supervised learning benchmarks, naive NNs are poor at quantifying predictive uncertainty (<PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON><PERSON> et al., 2023) , thus cannot be directly applied to CBO for constraint modeling. Recently, the prediction uncertainty of NNs has been studied in several approaches, including Bayesian NN (<PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2019) and ensemble methods (<PERSON><PERSON><PERSON><PERSON> et al., 2017) , have been proposed. Additionally, the ensemble method has shown strong promise in few-shot training (<PERSON><PERSON> et al., 2018) where the number of training samples is small.", "cite_spans": [{"start": 216, "end": 236, "text": "(<PERSON><PERSON> et al., 2012)", "ref_id": null}, {"start": 265, "end": 282, "text": "(<PERSON>, 2007)", "ref_id": "BIBREF46"}, {"start": 291, "end": 317, "text": "(<PERSON><PERSON> et al., 2021), and", "ref_id": "BIBREF47"}, {"start": 318, "end": 345, "text": "graphs (<PERSON> et al., 2018)", "ref_id": null}, {"start": 485, "end": 516, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF40"}, {"start": 517, "end": 542, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF17"}, {"start": 715, "end": 734, "text": "(<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF39"}, {"start": 735, "end": 753, "text": "<PERSON><PERSON> et al., 2019)", "ref_id": null}, {"start": 775, "end": 806, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF40"}, {"start": 909, "end": 930, "text": "(<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF6"}], "ref_spans": [], "eq_spans": [], "section": "Safe Bayesian Optimization", "sec_num": null}, {"text": "We are interested in efficiently optimizing black-box functions with costly evaluations and unknown constraints. Bayesian Optimization (BO) is a powerful framework for such scenario (<PERSON> et al., 1998; <PERSON><PERSON><PERSON> et al., 2016) .", "cite_spans": [{"start": 182, "end": 202, "text": "(<PERSON> et al., 1998;", "ref_id": "BIBREF33"}, {"start": 203, "end": 226, "text": "<PERSON><PERSON><PERSON> et al., 2016)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "In the typical setup, BO addresses the challenge of finding the global optimum of an expensive objective function f : X ⊂ R d → R, where direct gradient information is unavailable. In addition, the total number of performed function evaluations is often limited to several dozens.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "The optimization process begins with an initial set of N evaluations", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "Y 0 = {f (x i )} N i=1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": ", where x i is sampled at random from the design space X , to explore the function's behavior. A surrogate model f (x|Y 0 ), typically a Gaussian Process (GP), is then used to approximate the unknown function f using existing observations Y 0 . The main strength of BO lies in the strategy for selecting the subsequent evaluations by balancing exploration and exploitation. This balancing is defined with an acquisition function q that guides the search by trading off between exploiting promising regions and exploring uncertain regions of the function. Popular acquisition functions include Expected Improvement (EI) (<PERSON>, 1975) , Entropy Search (ES) (Hennig & Schuler, 2012) , Predictive Entropy Search (PES) (<PERSON> et al., 2014) , and <PERSON> (TS) (<PERSON>, 1933) . In this work, we use EI, but the proposed method is straightforward to generalize to other acquisition functions. EI measures the expected amount of improvement over the current best value f ′ by observing at the sampling point: (<PERSON> et al., 1998) , thus widely used in practice. Finally, the sample for next-step evaluation is selected as x + = arg max x EI(x). The set of observed samples is updated as", "cite_spans": [{"start": 619, "end": 633, "text": "(<PERSON><PERSON><PERSON>, 1975)", "ref_id": "BIBREF48"}, {"start": 656, "end": 680, "text": "(<PERSON><PERSON><PERSON> & <PERSON>, 2012)", "ref_id": "BIBREF25"}, {"start": 715, "end": 746, "text": "(<PERSON> et al., 2014)", "ref_id": "BIBREF27"}, {"start": 776, "end": 792, "text": "(<PERSON>, 1933)", "ref_id": null}, {"start": 1024, "end": 1044, "text": "(<PERSON> et al., 1998)", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "EI(x) = E[max{0, f (x|Y i ) -f ′ }]. When f (x) is GP, EI has a closed-form expression", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "Y i+1 = Y i ∪ {f (x + )}, and f is recomputed on Y i+1 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "Another layer of complexity is added to our problem by incorporating unknown constraints. In this work, we are interested in practical applications where design samples can be feasible and infeasible. Unlike other works with constraints that are continuous functions giving a real value even for samples that do not satisfy the constraints (<PERSON><PERSON> & Poloczek, 2021) , in our setup, it is impossible to obtain any objective value when the design is infeasible. These designs are impossible to create and evaluate. They either contradict the forces of physics or fail during the evaluation time. Hence, we model the constraints as a binary function c : X → {0, 1}, where x is feasible if c(x) = 1 and infeasible otherwise. Our final goal can be formalized as", "cite_spans": [{"start": 340, "end": 367, "text": "(Eriksson & Poloczek, 2021)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "arg min x∈X f (x) s.t. c(x) = 1,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "where both f and c are unknown. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "Our proposed method implements the BO pipeline with a few modifications described below. In summary, our method consists of the following steps: We first conduct initial evaluations on a random set of samples. We then fit a GP (as outlined in <PERSON><PERSON><PERSON> & <PERSON> (2005) ) on the evaluated data to model the objective function. In parallel, we fit another surrogate model to approximate the constraints described in Section 4.1. To select which sample to evaluate next, we optimize an acquisition function EI (see Section 3) with a constrained optimization approach introduced in Section 4.2. Finally, we evaluate the proposed sample and iterate until we reach the budget for the number of function evaluations.", "cite_spans": [{"start": 243, "end": 270, "text": "<PERSON><PERSON><PERSON> & Williams (2005)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Proposed Method", "sec_num": "4."}, {"text": "As described in Section 3, we consider the problem of representing constraints as a binary function, i.e., a classifier that predicts for each design sample whether it is feasible or infeasible. Specifically, we aim to train a classifier C : X ⊂ R d → [0, 1] that measures a probability of sample x being feasible. When C(x) > 0.5, sample x is considered more likely to be feasible.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Modeling Constraints", "sec_num": "4.1."}, {"text": "For many practical problems, it is difficult even to find a single feasible solution due to the non-convex nature of the feasible set. Hence, fitting a good surrogate model for the unknown constraints is a delicate task. Many previous works utilize GPs to approximate the constraints based on a given set of evaluations (<PERSON><PERSON> & Poloczek, 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2014) . However, since we observe that the optimal solution typically lies on the boundary between feasible and infeasible regions (see Section 4.2), having a higher-accuracy surrogate model is crucial. To the best of our knowledge, we are the first to propose the use of Deep Ensembles (DE) (<PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON> et al., 2019) for this purpose in Bayesian optimization. Please note the difference between ensembles previously used in Bayesian optimization that were in the context of Gaussian Process Ensembles (<PERSON> et al., 2018) and Acquisition Functions Ensembles (<PERSON> et al., 2011; <PERSON><PERSON><PERSON> et al., 2020) . ", "cite_spans": [{"start": 320, "end": 347, "text": "(Eriksson & Poloczek, 2021;", "ref_id": "BIBREF11"}, {"start": 348, "end": 369, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF18"}, {"start": 656, "end": 687, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF40"}, {"start": 688, "end": 706, "text": "<PERSON> et al., 2019)", "ref_id": "BIBREF13"}, {"start": 891, "end": 910, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF8"}, {"start": 947, "end": 969, "text": "(<PERSON> et al., 2011;", "ref_id": "BIBREF30"}, {"start": 970, "end": 993, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF34"}], "ref_spans": [], "eq_spans": [], "section": "Modeling Constraints", "sec_num": "4.1."}, {"text": "Deep", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Modeling Constraints", "sec_num": "4.1."}, {"text": "(x) = 1 N N i M θ ⋆ i (x)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Modeling Constraints", "sec_num": "4.1."}, {"text": ", where θ ⋆ i are trained weights for each model. In addition, we can compute the variance of the prediction as σ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Modeling Constraints", "sec_num": "4.1."}, {"text": "E (x) 2 = 1 N -1 N i (M θ ⋆ i (x) -µ E (x)) 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Modeling Constraints", "sec_num": "4.1."}, {"text": "To evaluate the feasibility probability C(x) we are essentially computing C(x) = µ E (x). See details in Appendix A.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Modeling Constraints", "sec_num": "4.1."}, {"text": "Benefits of Deep Ensembles GP-based constraint modeling has several limitations which can be addressed by using DE. First, GPs inherently assume a degree of smoothness in the underlying function they model, as evidenced by the choice of kernel function (e.g., RBF, Matern). This smoothness assumption can lead to difficulties when trying to capture sharp boundaries or discontinuities of the constraint. Second, the kernel structure is chosen a priori and remains fixed throughout the optimization. Although parameters of the kernel can be fitted to data, the form of the kernel itself is fixed and might not be well-suited to represent complex, non-linear boundaries. This limitation Figure 2 : Example evaluation of the LSQ synthetic benchmark problem guided by our algorithm BE-CBO. Top row illustrates the objective function, while the bottom row illustrates the classifier. In the left plot, the ground truth is shown with the boundary and optimal solution. In the second plot, the state of the surrogate is shown for both the constraint and the objective, based on the first initial 10 samples. The following three plots demonstrate the state of the surrogate models for the objective and the constraint after 50, 120, and 200 evaluations.", "cite_spans": [], "ref_spans": [{"start": 692, "end": 693, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Modeling Constraints", "sec_num": "4.1."}, {"text": "is discussed in depth in the context of model selection and adaptation (<PERSON><PERSON><PERSON> et al., 2013) . Thus, compared to GP, DE is more flexible and powerful in representing complex constraint functions (see Figure 1 and (<PERSON><PERSON> et al., 2018) for examples). Besides, DE is also fast to train due to easy parallelization and simple architectures (see Section 5 for runtime comparisons). Please refer to Appendix D.1 for more detailed comparison between GP and DE.", "cite_spans": [{"start": 71, "end": 94, "text": "(<PERSON><PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF10"}, {"start": 215, "end": 236, "text": "(<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF6"}], "ref_spans": [{"start": 209, "end": 210, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Modeling Constraints", "sec_num": "4.1."}, {"text": "Training Deep Ensembles In practice, we find that training DE with the popular maximum likelihood estimation (e.g., using binary cross-entropy loss) as suggested by <PERSON><PERSON><PERSON><PERSON> et al. (2017) leads to poorly calibrated uncertainties and thus deteriorates the BO performance. Instead, we find training DE with variational inference, specifically, evidence lower bound (ELBO), provides better-calibrated uncertainties and improves performance by a large margin, as aligned with observations from <PERSON> et al. (2018) . This approach requires the model to output continuous latent values that can be transformed to probabilities using Bernoulli likelihood. Please see Appendix A.1.2 for computation details and experimental validations.", "cite_spans": [{"start": 165, "end": 195, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2017)", "ref_id": "BIBREF40"}, {"start": 498, "end": 519, "text": "<PERSON><PERSON><PERSON> et al. (2018)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Modeling Constraints", "sec_num": "4.1."}, {"text": "For many practical problems, there is no information about constraints, and discovering the feasible and infeasible regions of space is intertwined with the optimization process. These infeasible regions occur when the system has physical or implicit limitations not artificially imposed by the users. In such scenarios, we discover infeasible regions by pushing the limit of what is feasible as we try to optimize the designs further. This reasoning, and many examples we have seen in practice, lead us to observe that the optimum most frequently (if not always) lies on or very close to the boundary between feasible and infeasible regions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Boundary Exploration", "sec_num": "4.2."}, {"text": "To ensure exploration around the boundary, we formulate a constrained optimization problem:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Boundary Exploration", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "arg max x q(x) s.t. l(x) ≤ C(x) ≤ u(x)", "eq_num": "(1)"}], "section": "Boundary Exploration", "sec_num": "4.2."}, {"text": "where q is an acquisition function (see Section 3), C is the classifier (Section 4.1), and l and u are lower and upper bound functions respectively. As discussed in Section 3, we implement EI for the acquisition function, but note that Eq. 1 is not in any way tied to the particular formulation of EI. The proposed method can easily generalize to other acquisition functions. Function l determines how far into the infeasible region can we sample. By allowing the samples to be queried in the infeasible part it encourages pushing the boundary and discovering new regions. However, querying many infeasible samples is especially problematic for a small sampling budget as those samples do not return any value. Hence, we limit l to remain close to the boundary, accounting for uncertainty in the prediction of the boundary, as discussed in the following paragraph. Function u, however, determines how far into the feasible region we can sample. Since all feasible values return additional information for the GP modeling the objective function, we allow exploration of the entire feasible region and set u(x) = 1 (recall that the upper bound for C is equal to 1 since it represents the probability). Please note that all the functions are updated in every iteration of BO to fit the new data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Boundary Exploration", "sec_num": "4.2."}, {"text": "A trivial approach to defining the lower bound around the boundary would be to set a constant value, say l(x) = 0.4. This bound leaves a 10% margin on the infeasible side of the boundary to allow some space for exploration and address-ing the inaccuracy of surrogate model predictions. However, in the initial iterations of BO, the surrogate models for classifier and objective function typically exhibit very high inaccuracy due to the small number of samples for fitting and nonlinear functions to approximate (see, for example, Figure 2 ). The accuracy can quickly increase with more samples being evaluated. Hence, the bound should also account for this change. It should be wider when the accuracy is low. We propose a dynamic bound strategy, where the exploration region around the boundary directly relates to the uncertainty of the fitted classifier. Inspired by <PERSON><PERSON> (<PERSON><PERSON><PERSON> et al., 2010) , we define the dynamic bound as:", "cite_spans": [{"start": 875, "end": 898, "text": "(<PERSON><PERSON><PERSON> et al., 2010)", "ref_id": null}], "ref_spans": [{"start": 538, "end": 539, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Boundary Exploration", "sec_num": "4.2."}, {"text": "l(x) = 0.5 -σ E (x)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Boundary Exploration", "sec_num": "4.2."}, {"text": "where σ E (x) is the standard deviation of the Deep Ensembles C for input x.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Boundary Exploration", "sec_num": "4.2."}, {"text": "With this formulation, we incorporate the uncertainty of the classifier into the selection strategy, while the uncertainty of the surrogate of the objective function is captured in the acquisition function (<PERSON><PERSON><PERSON>, 1975 ). We demonstrate one example of the optimization progress lead by BE-CBO over 200 evaluations in Figure 2 . It qualitatively shows that accurate constraint modeling plus active boundary exploration is effective in discovering both the correct constraint boundary and the global optimum.", "cite_spans": [{"start": 206, "end": 219, "text": "(<PERSON><PERSON><PERSON>, 1975", "ref_id": "BIBREF48"}], "ref_spans": [{"start": 325, "end": 326, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Boundary Exploration", "sec_num": "4.2."}, {"text": "We conduct comprehensive experiments to evaluate the performance of our methods and compare them to the relevant state-of-the-art methods on both synthetic test functions and real-world benchmark problems.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Evaluation", "sec_num": "5."}, {"text": "Algorithms We compare our algorithm to several baseline algorithms described in Section 2 that can be applied to binary constraints: CEI (<PERSON><PERSON><PERSON><PERSON> et al., 2014) , SCBO (Eriksson & Poloczek, 2021) , SVM-CBO (Antonio, 2021) , and random search. We implement and compare CEI and SCBO in our Python codebase, built upon the BoTorch (<PERSON><PERSON> et al., 2020) BO framework. We conduct SVM-CBO experiments using their framework. Our code will be released with a reproducibility guarantee. See more details in Appendix B.2.", "cite_spans": [{"start": 137, "end": 159, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF18"}, {"start": 167, "end": 194, "text": "(Eriksson & Poloczek, 2021)", "ref_id": "BIBREF11"}, {"start": 205, "end": 220, "text": "(Antonio, 2021)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Experimental Evaluation", "sec_num": "5."}, {"text": "Benchmark Problems Our benchmark includes three synthetic test functions and nine real-world engineering design problems: 2D Townsend function (<PERSON>, 2014), 2D Simionescu function (<PERSON><PERSON><PERSON><PERSON>, 2014), 2D LSQ function (<PERSON><PERSON> et al., 2016) , 2D three-bar truss design (<PERSON> & <PERSON>, 2001) , 3D tension-compression string design (<PERSON><PERSON> et al., 2006) , 4D welded beam design (<PERSON><PERSON> et al., 2006) , 4D gas transmission compressor design (<PERSON><PERSON> et al., 2009) , 4D pressure vessel design (Coello & Montes, 2002) , 7D speed reducer design (<PERSON><PERSON> et al., 2010) , 9D planetary gear train design (<PERSON> et al., 2012) , 10D rolling element bearing design (<PERSON> et al., 2007) , and 30D cantilever beam design (<PERSON> et al., 2018) . Please refer to Appendix B.1 for more detailed descriptions.", "cite_spans": [{"start": 220, "end": 242, "text": "(<PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF19"}, {"start": 271, "end": 290, "text": "(<PERSON> & <PERSON>, 2001)", "ref_id": null}, {"start": 330, "end": 350, "text": "(<PERSON><PERSON> et al., 2006)", "ref_id": "BIBREF24"}, {"start": 375, "end": 395, "text": "(<PERSON><PERSON> et al., 2006)", "ref_id": "BIBREF24"}, {"start": 436, "end": 455, "text": "(<PERSON><PERSON> et al., 2009)", "ref_id": null}, {"start": 484, "end": 507, "text": "(Coello & Montes, 2002)", "ref_id": "BIBREF9"}, {"start": 534, "end": 556, "text": "(<PERSON><PERSON> et al., 2010)", "ref_id": "BIBREF41"}, {"start": 590, "end": 608, "text": "(<PERSON> et al., 2012)", "ref_id": null}, {"start": 646, "end": 666, "text": "(<PERSON> et al., 2007)", "ref_id": "BIBREF22"}, {"start": 700, "end": 720, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "Experimental Evaluation", "sec_num": "5."}, {"text": "Performance We monitor the current best value f ′ over the number of evaluations performed. For every algorithm, we run experiments with 10 different random seeds and the same 10 initial samples for a total of 200 evaluations. We average the results over these 10 experiments for every algorithm, and plot the mean and the standard deviation across them, as presented in Figure 3 .", "cite_spans": [], "ref_spans": [{"start": 378, "end": 379, "text": "3", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Results and Discussion", "sec_num": "5.1."}, {"text": "Test problems are chosen to assess the efficacy of the method in handling a wide range of function characteristics, including concave, convex, disconnected, and varying complexities in design space. As shown in Figure 3 , BE-CBO consistently exhibits top performance and low variance compared to other algorithms that oscillates across different problems.", "cite_spans": [], "ref_spans": [{"start": 218, "end": 219, "text": "3", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Results and Discussion", "sec_num": "5.1."}, {"text": "Figure 4 shows qualitative comparisons on sample distributions of algorithms at different time steps when evaluating on the <PERSON><PERSON><PERSON><PERSON> function. Our method effectively explores the boundary region, classifies the complex constraint landscape well, and discovers both optima of the function.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "4", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Results and Discussion", "sec_num": "5.1."}, {"text": "Besides, our method also places samples occasionally on other parts of the boundary to capture other local optima that are potentially better than the best discovered optima. See more qualitative examples in Appendix C.1. We benchmark algorithm runtime in Appendix C.2, which shows that BE-CBO exhibits stable runtime across all problem dimensions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Results and Discussion", "sec_num": "5.1."}, {"text": "Overall, BE-CBO performs robustly on all of our benchmark problems, and we have not observed a case in which it completely fails or performs much worse than other baselines. However, we noticed that BE-CBO is slightly outperformed by SCBO on our 30D cantilever beam design problem. We speculate that this is because SCBO's key advantage in high-dimensional optimization is its usage of TURBO (<PERSON><PERSON> et al., 2019) , a well-designed optimizer for high-dimensional BO based on the idea of using several local surrogates instead of a single global surrogate, along with its restart strategy when TURBO gets stuck. In BE-CBO, we adopt a relatively standard SLSQP optimizer with a global surrogate for acquisition optimization, which may be suboptimal for practical high-dimensional optimization. Although our core idea of BE-CBO is independent of the choice of acquisition optimizers, we believe that integrating effective ones such as TURBO with BE-CBO is technically feasible and promising for combining the best of both worlds. Feasibility Ratio We measure the feasibility ratio in Figure 5 by comparing the number of feasible and infeasible points sampled by each algorithm. This is to keep track of how many samples did not retrieve any information on the objective value due to falling in the infeasible region. These samples do not improve the objective surrogate model, but they do improve the classifier and push the boundary further.", "cite_spans": [{"start": 392, "end": 415, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Results and Discussion", "sec_num": "5.1."}, {"text": "While the feasibility ratio has not been studied in CBO, there is abundant research on constrained evolutionary algorithms that focuses the search around the boundary and demonstrate improved performance (<PERSON><PERSON> et al., 2008; <PERSON> et al., 2009; <PERSON><PERSON> et al., 2019) . <PERSON><PERSON> et al. (2019) empirically shows that 50% is the most reliable feasibility ratio for evolutionary algorithms on global optimization problems with different function characteristics.", "cite_spans": [{"start": 204, "end": 225, "text": "(<PERSON><PERSON> et al., 2008;", "ref_id": "BIBREF31"}, {"start": 226, "end": 243, "text": "<PERSON> et al., 2009;", "ref_id": null}, {"start": 244, "end": 262, "text": "<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF32"}, {"start": 265, "end": 283, "text": "<PERSON><PERSON> et al. (2019)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Results and Discussion", "sec_num": "5.1."}, {"text": "Interestingly, our algorithm outperforms the ones focusing the search in the feasible region (SCBO) or penalizing points less likely to be feasible (CEI). It is worth pointing to the examples where our algorithm is able to discover larger feasible regions and better performing designs due to the improved accuracy in constraint modeling and narrowed search around the boundary on the infeasible side.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Results and Discussion", "sec_num": "5.1."}, {"text": "Boundary Optimality We focus on practical problems where the constraints are unknown and imposed by a physical system. The design keeps improving until it reaches the limits of the physical system. Hence, the optimum is often found on the boundary of feasible region. In the prior literature, this observation has been exploited in multiple con-texts, such as designing effective constrained evolutionary algorithms (<PERSON><PERSON> et al., 2008; <PERSON> et al., 2009; <PERSON> et al., 2021) , designing test functions that resemble real-world problems (<PERSON><PERSON> et al., 2021) , and active set methods in constrained optimization (<PERSON><PERSON><PERSON> et al., 2011) .", "cite_spans": [{"start": 416, "end": 437, "text": "(<PERSON><PERSON> et al., 2008;", "ref_id": "BIBREF31"}, {"start": 438, "end": 455, "text": "<PERSON> et al., 2009;", "ref_id": null}, {"start": 456, "end": 473, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF44"}, {"start": 535, "end": 558, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": null}, {"start": 612, "end": 634, "text": "(<PERSON><PERSON><PERSON> et al., 2011)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "Results and Discussion", "sec_num": "5.1."}, {"text": "We acknowledge that this property does not hold for every problem, such as synthetically constructed functions with interior optima and problems with user-specified constraints. Therefore, to test the generality of our method, we modified the three synthetic benchmark problems such that their optima are shifted to inside the feasible region. Results in Appendix C.3 shows that BE-CBO successfully reaches the optimal region and exhibits competitive performance.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Results and Discussion", "sec_num": "5.1."}, {"text": "In addition, we investigate if active constraints exist at the global optima of all nine real-world problems in our benchmark, which cover most of the mechanical design problems with continuous parameters and unknown inequality constraints collected in a real-world non-convex constrained problem suite (<PERSON> et al., 2020) . For problems that do not have a known global optimum, we run CMA-ES (<PERSON> et al., 2003) with millions of evaluations to approximate a global optimum. As a result, we found that all nine problems have the global optimum on the constraint boundary, i.e., at least one active constraint. In those scenarios, the active constraints represent physical limitations that determine the upper bound of the mechanical design's performance. See more details of these experiments in Appendix B.1.3.", "cite_spans": [{"start": 303, "end": 323, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF38"}, {"start": 394, "end": 415, "text": "(<PERSON> et al., 2003)", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "Results and Discussion", "sec_num": "5.1."}, {"text": "To systematically study how effective each component of BE-CBO is, we conduct comprehensive ablation study ex- ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation Studies", "sec_num": "5.2."}, {"text": "We use EI as the acquisition function in BE-CBO mainly because of its popularity and good balance between exploration and exploitation. However, BE-CBO is compatible with any standard acquisition functions and is not tied to EI. One can also use upper confidence bound (UCB) when more explicit control over the exploration is preferred. In Appendix D.3, we compare the performance of using EI and UCB for BE-CBO respectively, and the results suggest that they perform similarly well on our benchmark problems overall with some differences in particular problems.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "How does BE-CBO perform with other acquisition functions than EI?", "sec_num": "4."}, {"text": "We investigate two variants of BE-CBO where similar constraint treatments to CEI and SCBO are applied instead of the proposed boundary exploration. In Appendix D.4, the results show that our boundary exploration is the most robust strategy in coupling the constraints with the acquisition function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "How does boundary exploration perform compared to other forms of constrained acquisition functions?", "sec_num": "5."}, {"text": "We introduced a novel CBO method that aims to find a global optimum of an unknown function under unknown constraints. We specifically address the problem in which the infeasible designs cannot be evaluated, hence returning no information for the objective function nor a continuous value for the constraint. Such problems are common, for example, in chemistry and materials science in which specific combinations of synthesis parameters may lead to invalid materials, making it impossible to measure any output quantities. This results in a BO iteration with no information gain which can cause the optimizer to get stuck in the worst case. Coupling the BO optimizer with a classifier that can distinguish the feasible from the infeasible regions allows the optimizer to also draw information from failed experi- ments and to converge quickly to the actual optimum most often located at the constrained boundary, paving the way to more efficient experimentation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion and Future Work", "sec_num": "6."}, {"text": "We employed Deep Ensembles for the classification of the constraints representing binary feasible/infeasible regions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion and Future Work", "sec_num": "6."}, {"text": "To the best of our knowledge, we are the first to propose using DE for modeling unknown constraints in BO. DE exhibits improved accuracy in the classification, allowing our method to focus the search for optima on the boundary. However, we share the same observation as <PERSON> et al. (2023) that using DE for modeling objectives (i.e., regression) does not lead to improved performance. Finally, we present a boundary exploration strategy that efficiently discovers better designs. We performed extensive tests on both synthetic test functions and real-world problems. We found that our approach outperforms other methods and works particularly well on practical problems.", "cite_spans": [{"start": 270, "end": 286, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "Conclusion and Future Work", "sec_num": "6."}, {"text": "We acknowledge two main limitations of our work. Firstly, our algorithm was exclusively tested in a continuous design space, focusing on a single objective. To broaden its applicability, a future direction would involve extending the algorithm to handle categorical variables and explore multi-objective BO. Secondly, our work does not account for the potential costs linked to evaluating infeasible samples. For instance, costs associated with material breakage during evaluation or motor damage caused by excessively high voltage are not considered. Furthermore, to the best of our knowledge, we are unable to find theoretical analysis on global convergence rate in the constrained BO literature when unknown constraints are involved due to the added complexity of constraint surrogates and the interplay between constraints and objectives. We leave these exciting directions for future work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion and Future Work", "sec_num": "6."}, {"text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Optimization of mechanical design problems using improved differential evolution algorithm. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion and Future Work", "sec_num": "6."}, {"text": "A.1. Deep Ensembles", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A. Method Implementation Details", "sec_num": null}, {"text": "We implement an ensemble of Multi-layer Perceptrons (MLPs) for modeling the unknown constraints. For each MLP in the ensemble, we use a simple and standard structure of 4 fully connected layers with 64⌊log 2 (d)⌋ neurons in each hidden layer where d is the problem dimension. The network gets larger as the problem dimension gets higher. We use ReLU nonlinearity between each pair of fully connected layers.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.1. NEURAL NETWORK ARCHITECTURE AND PARAMETERS", "sec_num": null}, {"text": "Similar to training GP classifiers using a variational framework (<PERSON><PERSON><PERSON> et al., 2015) , we use the variational evidence lower bound (ELBO) to approximate the posterior and marginal likelihood of Deep Ensemble classifiers given the <PERSON><PERSON><PERSON> likelihood. The ensemble is optimized for maximal marginal log likelihood using the <PERSON> optimizer with a 3 × 10 -4 learning rate for 1,000 iterations. Note that we do not apply regularization or dropout as suggested by <PERSON><PERSON> et al. (2017) .", "cite_spans": [{"start": 65, "end": 87, "text": "(<PERSON><PERSON><PERSON> et al., 2015)", "ref_id": "BIBREF26"}, {"start": 463, "end": 493, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2017)", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "A.1.1. NEURAL NETWORK ARCHITECTURE AND PARAMETERS", "sec_num": null}, {"text": "Overall, the architecture we use is very simple and straightforward to implement with almost no hyper-parameters, but works robustly across a wide range of benchmark problems as shown in our experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.1. NEURAL NETWORK ARCHITECTURE AND PARAMETERS", "sec_num": null}, {"text": "In Section 4.1 of the main paper, we introduced the mean and variance computation following the original formulation of Deep Ensembles (<PERSON><PERSON> et al., 2017) . In practice, we empirically observed an alternative implementation of mean and uncertainty computation leads to better performance.", "cite_spans": [{"start": 135, "end": 166, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "A.1.2. MEAN AND UNCERTAINTY COMPUTATION", "sec_num": null}, {"text": "As mentioned in Section 4.1, instead of letting the network ensemble directly output the probability value between [0, 1] for the constraint feasibility and training the ensemble using maximum likelihood estimation (MLE, specifically, binary cross-entropy loss), we treat the network ensemble as a real-valued latent constraint function g(x) such that the constraint is satisfied if and only if g(x) ≥ 0. In other words, we let the network ensemble output a latent Gaussian distribution in a continuous space and later transform the output to a probability between [0, 1] by standard normal CDF Φ, following the approach taken by CEI (<PERSON><PERSON><PERSON> et al., 2014) . The transformed probability is essentially the mean prediction. With such treatment, we can optimize the Deep Ensembles in a similar way as training GP classifiers based on a variational inference (VI) framework, as described in Section A.1.1.", "cite_spans": [{"start": 634, "end": 656, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "A.1.2. MEAN AND UNCERTAINTY COMPUTATION", "sec_num": null}, {"text": "For uncertainty computation, since we can easily get the real-valued mean µ g and standard deviation σ g from the latent Gaussian distribution, we can transform the one-sigma confidence interval [µ g -σ g , µ g + σ g ] by the standard normal CDF Φ and obtain a corresponding confidence interval in the transformed probability space [Φ(µ g -σ g ), Φ(µ g + σ g )] and define the transformed standard deviation as σ E = (Φ(µ g + σ g ) -Φ(µ g -σ g ))/2. In practice, we use this formula to obtain the dynamic bounds as described in Section 4.2 of the main paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.2. MEAN AND UNCERTAINTY COMPUTATION", "sec_num": null}, {"text": "In practice, we find that models trained by MLE are overly confident, i.e., usually output extremely low uncertainties, which leads to the poor performance. On the contrary, training with VI produces much more reasonable uncertainty estimation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.2. MEAN AND UNCERTAINTY COMPUTATION", "sec_num": null}, {"text": "Figure 6 shows empirical comparisons between training DE with VI and MLE respectively for BE-CBO, which shows a clear advantage of VI.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "6", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "A.1.2. MEAN AND UNCERTAINTY COMPUTATION", "sec_num": null}, {"text": "For numerical optimization of the constrained acquisition function in BE-CBO, we specify the dynamic bound constraint as a nonlinear inequality constraint to the acquisition optimizer in BoTorch (<PERSON><PERSON><PERSON> et al., 2020) , which calls an underlying SLSQP optimization from SciPy, a standard and robust choice for handling nonlinear inequality constraints. However, the SLSQP optimizer requires constraint-satisfying initial solutions as input. Since randomly generated initial solutions can easily violate the constraint, to deal with this problem, we use the Adam optimizer (Kingma & Ba, 2014) to find constraint-satisfying samples through gradient descent. We use a standard mean squared error (MSE) loss to measure the difference between the sample's feasibility probability and 0.5. By optimizing such MSE loss, the sample gets closer to the constraint boundary by pushing the feasibility probability to 0.5, thus the sample becomes closer to satisfying the dynamic band constraint. ", "cite_spans": [{"start": 195, "end": 218, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF3"}, {"start": 573, "end": 592, "text": "(Kingma & Ba, 2014)", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "A.2. Constrained Optimization on Acquisition Functions", "sec_num": null}, {"text": "Due to the large number of benchmark problems and random seeds, the experiments are conducted in parallel on a distributed server with Intel Xeon Platinum 8260 CPUs with 4GB RAM per core, where each individual experiment runs on a single CPU thread without GPU.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Experimental Setup", "sec_num": null}, {"text": "For surrogate modeling of the objective in BE-CBO and all baseline algorithms, we directly use the implementation of GP regressors in BoTorch (<PERSON><PERSON><PERSON> et al., 2020) , where Matern 5/2 kernel is used with their default hyperparameters1 . For the GP constraint classifiers in all baseline algorithms and also ablation studies in BE-CBO, we leverage the implementation from GPyTorch (<PERSON> et al., 2018) , where RBF kernel is used with their default hyperparameters. 2 We use the standard Bernoulli likelihood in GP classifiers for representing posterior probability distributions, and use the variational evidence lower bound (ELBO) to optimize the GP classifiers (<PERSON><PERSON><PERSON> et al., 2015) .", "cite_spans": [{"start": 142, "end": 165, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF3"}, {"start": 381, "end": 403, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF16"}, {"start": 665, "end": 687, "text": "(<PERSON><PERSON><PERSON> et al., 2015)", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "B. Experimental Setup", "sec_num": null}, {"text": "In this section, we briefly introduce the properties of each problem, including the dimensions of the design space X ⊂ R d . The problem descriptions for 3 synthetic functions and 9 real-world problems are described respectively. We perform 10 independent test runs with 10 different random seeds for each problem on each algorithm. For each test run of one problem, we use the same initial set of samples for every algorithm, which is generated by a scrambled quasirandom Sobol sequence using the same random seed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. Benchmark Problems", "sec_num": null}, {"text": "We represents all benchmark problems in the form of minimizing f (x) subject to multiple underlying constraints c 1 (x) ≥ 0, ..., c n (x) ≥ 0. Note that we aim to solve the problem where the constraints are unknown and the algorithm only learns whether a design point is feasible or infeasible. Hence, multiple constraints can exist, but their formulas are invisible and they are all captured with one classifier that outputs a binary value.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. Benchmark Problems", "sec_num": null}, {"text": "<PERSON> function (<PERSON>, 2014) is a trigonometric function f", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.1. SYNTHETIC TEST FUNCTIONS", "sec_num": null}, {"text": "(x) = -[cos((x 1 -0.1)x 2 )] 2 -x 1 sin(3x 1 + x 2 ) constrained by c(x) = (2 cos t -1 2 cos 2t -1 4 cos 3t -1 8 cos 4t) 2 + (2 sin t) 2 -x 2 1 -x 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.1. SYNTHETIC TEST FUNCTIONS", "sec_num": null}, {"text": "2 where t = arctan2(x 1 , x 2 ) and with bounds -2.25 ≤ x 1 ≤ 2.25 and -2.5 ≤ x 2 ≤ 1.75.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.1. SYNTHETIC TEST FUNCTIONS", "sec_num": null}, {"text": ") is a hyperbolic paraboloid function f (x) = 0.1x 1 x 2 constrained by c(x) = (r T + r S cos(n arctan x y )) 2 -x 2 1 -x 2 2 where r T = 1, r S = 0.2 and n = 8, in the domain [-1.25, 1.25] 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Si<PERSON><PERSON><PERSON> function (<PERSON><PERSON><PERSON><PERSON>, 2014", "sec_num": null}, {"text": "LSQ function (<PERSON><PERSON> et al., 2016 ) is a linear objective function f (x) = x 1 + x 2 with sinusoidal and quadratic constraints c 1 (", "cite_spans": [{"start": 13, "end": 34, "text": "(<PERSON><PERSON> et al., 2016", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "Si<PERSON><PERSON><PERSON> function (<PERSON><PERSON><PERSON><PERSON>, 2014", "sec_num": null}, {"text": "x) = x 1 + 2x 2 + 1 2 sin(2π(x 2 1 -2x 2 )) -3 2 and c 2 (x) = 3 2 -x 2 1 -x 2 2 bounded by [0, 1] 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Si<PERSON><PERSON><PERSON> function (<PERSON><PERSON><PERSON><PERSON>, 2014", "sec_num": null}, {"text": "Three bar truss design (<PERSON>, 2001) minimizes the volume of the truss structure subject to stress constraints. The analytical formula is given as f (x) = l(2 √ 2x 1 + x 2 ) with two variables 0 ≤ x 1 , x 2 ≤ 1, subject to three constraints:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "• c 1 (x) = 2 - √ 2x1+x2 √ 2x 2 1 +2x1x2 • c 2 (x) = 2 - 1 x1+ √ 2x2 • c 3 (x) = 2 - x2 √ 2x 2 1 +2x1x2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "Tension-compression string design (<PERSON><PERSON> et al., 2006 ) is a three dimensional design problem where the weight of a tension-compression string, given by f (x) = (x 1 + 2)x 2 x 2 3 , needs to be minimized, where: 2 ≤ x 1 ≤ 15 is the number of active coils (integer), 0.25 ≤ x 2 ≤ 1.3 is the wire diameter, and 0.05 ≤ x 3 ≤ 2 is the mean coil diameter. This minimization is constrained by the minimum deflection, shear stress, surge frequency, and limits on the outside diameter (<PERSON><PERSON> & Montes, 2002) :", "cite_spans": [{"start": 34, "end": 53, "text": "(<PERSON><PERSON> et al., 2006", "ref_id": "BIBREF24"}, {"start": 477, "end": 500, "text": "(Coello & Montes, 2002)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "• c 1 (x) = x 3 2 x1 71785x 4 3 -1 • c 2 (x) = 1 - 4x 2 2 -x3x2 12566(x2x 3 3 -x 4 3 ) -1 5108x 2 3 • c 3 (x) = 140.45x3 x 2 2 x1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "-1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "• c 4 (x) = 1 -x2+x3 1.5", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "Welded beam design (<PERSON>eg<PERSON><PERSON> & A<PERSON>, 1985) aims to minimize the cost of the beam, f (x) = 1.10471x 2 1 x 2 + 0.04811x 3 x 4 (14 + x 2 ), where 0.125 ≤ x 1 ≤ 10 and 0.1 ≤ x 2 , x 3 , x 4 ≤ 10 are four design variables referring to physical dimensions of the beam. This optimization is subject to constraints on the shear stress, bending stress in the beam, buckling load on the bar, the end deflection of the beam, and a side constraint (<PERSON><PERSON> et al., 2006) :", "cite_spans": [{"start": 19, "end": 44, "text": "(Belegundu & Arora, 1985)", "ref_id": "BIBREF5"}, {"start": 438, "end": 458, "text": "(<PERSON><PERSON> et al., 2006)", "ref_id": "BIBREF24"}], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "• c 1 (x) = 13000 -τ (x) • c 2 (x) = 30000 -σ(x) • c 3 (x) = P c (x) -6000 • c 4 (x) = 0.25 -δ(x) • c 5 (x) = x 4 -x 1 where τ (x) = (τ 1 (x)) 2 + (τ 2 (x)) 2 + x2τ1(x)τ2(x) √ 0.25[x 2 2 +(x1+x3) 2 ] , τ 1 (x) = 6000 √ 2x1x2 , τ 2 (x) = 6000(14+0.5x2) √ 0.25[x 2 2 +(x1+x3) 2 ] 2[0.707x1x2(x 2 2 /12+0.25(x1+x3) 2 )] , σ(x) = 504000 x 2 3 x4 , P c (x) = 64746.022(1 -0.0282346x 3 )x 3 x 3 4 , δ(x) = 2.1953 x 3 3 x4 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "Gas transmission compressor design (<PERSON><PERSON> et al., 2009) aims to minimize the total annual cost of a gas pipeline transmission system and its operation, given by f (x) = (8.61", "cite_spans": [{"start": 35, "end": 54, "text": "(<PERSON><PERSON> et al., 2009)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": ")10 5 x 1/2 1 x 2 x -2/3 3 x -1/2 4 + (3.69)10 4 x 3 + (7.72)10 8 x -1 1 x 0.219 2 - (765.43)10 6 x -1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "1 , where 20 ≤ x 1 ≤ 50 is the length between compressor stations, 1 ≤ x 2 ≤ 10 is the compression ratio, 20 ≤ x 3 ≤ 50 is the inside diameter of the pipe, and 0.1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "≤ x 4 ≤ 60 is a non-dimensional parameter, subject to c(x) = 1 -x 4 x -2 2 -x -1 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "Pressure vessel design (<PERSON><PERSON> & Montes, 2002) minimize the total cost f (x) = 0.6224x 1 x 3 x 4 + 1.7781x 2 x 2 3 + 3.1661x 2 1 x 4 + 19.84x 2 1 x 3 , including the cost of the material, forming and welding. There are four design variables: Ts (thickness of the shell), Th (thickness of the head), R (inner radius) and L (length of the cylindrical section of the vessel, not including the head). The constraints are given as:", "cite_spans": [{"start": 23, "end": 46, "text": "(Coello & Montes, 2002)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "• c 1 (x) = x1 -0.0193x 3 • c 2 (x) = x2 -0.00954x 3 • c 3 (x) = πx 2 3 x 4 + 4 3 πx 3 3 -1296000 • c 4 (x) = 240 -x 4", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "Speed reducer design (<PERSON><PERSON> et al., 2010 ) is a seven dimensional problem that seeks to minimize the weight of a speed reducer. The design variables are: the face width (2.6 ≤ x 1 ≤ 3.6), the module of teeth (0.7 ≤ x 2 ≤ 0.8), the number of teeth on pinion (integer) (17 ≤ x 3 ≤ 28), the length of the shaft 1 between the bearings (7.3 ≤ x 4 ≤ 8.3), the length of the shaft 2 between the bearings (7.3 ≤ x 5 ≤ 8.3), the diameter of the shaft 1 (2.9 ≤ x 6 ≤ 3.9), and the diameter of the shaft 2 (5 ≤ x 7 ≤ 5.5).", "cite_spans": [{"start": 21, "end": 42, "text": "(<PERSON> et al., 2010", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "The weight is given by: f (x) = 0.7854x 1 x 2 2 (3.3333x 2 3 + 14.9334x 3 -43.0934) -1.508x 1 (x 2 6 + x7 2 ) + 7.4777(x 3 6 + x 3 7 ) + 0.7854(x 4 x 2 6 + x 5 x 2 7 ) and is subject to the following mechanical constraints:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "• c 1 (x) = 1 -27x -1 1 x -2 2 x -1 3 , • c 2 (x) = 1 -397.5x -1 1 x -2 2 x -2 3 , • c 3 (x) = 1 -1.93x -1 2 x -1 3 x -3 4 x -4 6 , • c 4 (x) = 1 -1.93x -1 2 x -1 3 x -3 5 x -4 7 , • c 5 (x) = 1100 -[ 745x4", "eq_num": "x2x3"}], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "2 + (16.9)10 6 ] 0.5 1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "0.1x 3 6 , • c 6 (x) = 850 -[ 745x5", "eq_num": "x2x3"}], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "2 + (157.5)10 6 ] 0.5 1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "0.1x 3 7 , • c 7 (x) = 40 -x 2 x 3 , • c 8 (x) = x 1 /x 2 -5, • c 9 (x) = 12 -x 1 /x 2 , • c 10 (x) = 1 -(1.5x 6 + 1.9)x -1 4 , • c 11 (x) = 1 -(1.1x 7 + 1.9)x -1 5 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "Planetary gear train design (<PERSON>, 2012) minimizes the gear ratio errors which can be stated as:", "cite_spans": [{"start": 28, "end": 43, "text": "(<PERSON>, 2012)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "f (x) = max |i k -i 0k | where k = {1, 2, R}, i 1 = N6 N4 , i 01 = 3.11, i 2 = N6(N1N3+N2N4) N1N3(N6-N4) , i 02 = 1.84, i R = N2N6 N1N3 , i 0R = -3.11.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "The design variables are defined as x = (ρ, N 6 , N 5 , N 4 , N 3 , N 2 , N 1 , m 2 , m 1 ). It is also subject to the following constraints:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "• c 1 (x) = D max -m 3 (N 6 + 2.5) • c 2 (x) = D max -m 1 (N 1 + N 2 ) -m 1 (N 2 + 2) • c 3 (x) = D max -m 3 (N 4 + N 5 ) -m 3 (N 5 + 2) • c 4 (x) = (N 1 + N 2 ) sin 7 ρ -N 2 -2 -δ 22 • c 5 (x) = (N 6 -N 3 ) sin 7 ρ -N 3 -2 -δ 33 • c 6 (x) = (N 4 + N 5 ) sin 7 ρ -N 5 -2 -δ 55 • c 7 (x) = (N 6 -N 3 ) 2 -(N 3 + N 5 + 2 + δ 35 ) 2 -(N 4 + N 5 ) 2 -2(N 6 -N 3 )(N 4 + N 5 ) cos 2π ρ -β • c 8 (x) = -N 4 + N 6 -2N 5 -2δ 56 -4 • c 9 (x) = -2N 3 + N 6 -N 4 + 2δ 34 + 4", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "δ 22 = δ 33 = δ 55 = δ 35 = δ 56 = 0.5, β = cos -1 (N4+N5) 2 +(N6-N3) 2 -(N3+N5) 2 2(N6-N3)(N4+N5)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": ", and D max = 220, with bounds: p = (3, 4, 5), m 1 = (1. 75, 2.0, 2.25, 2.5, 2.75, 3.0) , m 3 = (1.75, 2.0, 2.25, 2.5, 2.75, 3.0), 17 ≤ N 1 ≤ 96, 14 ≤ N 2 ≤ 54, 14 ≤ N 3 ≤ 51, 17 ≤ N 4 ≤ 46, 14 ≤ N 5 ≤ 51, 48 ≤ N 6 ≤ 124, and N i is an integer. We apply continuous optimization methods on this problem by rounding the continuous variables into integer ones.", "cite_spans": [{"start": 57, "end": 87, "text": "75, 2.0, 2.25, 2.5, 2.75, 3.0)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "Rolling element bearing design (<PERSON> et al., 2007) optimizes the dynamic capacity of a rolling bearing. The design parameter vector can be written as ", "cite_spans": [{"start": 31, "end": 51, "text": "(<PERSON> et al., 2007)", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "x = (D m , D b , Z, f i , f o , K Dmin , K Dmax , ε,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "C d = f c Z 2/3 D 1.8 b , D b ≤ 25.4 mm 3.647f c Z 2/3 D 1.4 b , D b > 25.4 mm f c = 37.91   1 + 1.04 (1 -γ) 1.72 (1 + γ) 1.72 f i (2f o -1) f o (2f i -1) 0.41 10/3 0.3   γ 0.3 (1 -γ) 1.39 (1 + γ) 1/3 2f i 2f i -1 0.41 , γ = D b cos α D m .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "The constraints are defined as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "• c 1 (x) = ϕ0 2 sin -1 D b Dm -Z + 1 • c 2 (x) = 2D b -K Dmin (D -d) • c 3 (x) = K Dmax (D -d) -2D b • c 4 (x) = ξ Bw -D b • c 5 (x) = D m -0.5(D + d) • c 6 (x) = (0.5 + e)(D + d) -D m • c 7 (x) = 0.5(D -D m -D b ) -eD b", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "ϕ 0 = 2π -2 cos -1 (D -d)/2 -3(T /4) 2 2 + {(D/2 -(T /4) -D b )} 2 -(d/2 + (T /4)) 2 2{(D -d)/2 -3(T /4)}{(D/2 -(T /4) -D b )} , T = D -d -2D b .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "Cantilever beam design (<PERSON> et al., 2018) minimizes the tip deflection of a stepped cantilever beam subject to constraints. Due to the complexity of formulas, please refer to the original paper for details. For real-world design optimization problems with unknown physical constraints, the optimal solutions usually lie on the boundary of feasible regions since the performance upper bound is usually constrained by physical limits. Though this is often the experience from practitioners, to further validate whether this observation is true, we check whether the global optimum is on the constraint boundary for each problem in our collected nine benchmark problems. Table 1 summarizes the problem description, the optimum information and active constraints at the optimum. All problems have at least one active constraint at the optimum and some problems have multiple ones.", "cite_spans": [{"start": 23, "end": 43, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF8"}], "ref_spans": [{"start": 676, "end": 677, "text": "1", "ref_id": "TABREF4"}], "eq_spans": [], "section": "B.1.2. REAL TEST FUNCTIONS", "sec_num": null}, {"text": "In this section, we provide a description of the other baseline algorithms that we used for comparison with our own algorithm. Specifically, we compare our algorithms to several BO baseline algorithms that are discussed in Results Section of the main paper. These baseline algorithms are designed or have been modified to handle binary unknown constraints. Here we describe the differences between our adoption of these algorithms and their original formulation in the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. <PERSON><PERSON>", "sec_num": null}, {"text": "CEI (<PERSON><PERSON><PERSON><PERSON> et al., 2014) We implement this algorithm in BoTorch. The acquisition function remains unchanged to its original form, with one notable modification regarding the constraint classification. In contrast to the paper's setup where individual constraints provide separate responses, our evaluation process only allows for obtaining a single feasibility response. Therefore, we have modified the implementation to utilize a single binary classifier as a constraint surrogate instead of multiple classifiers for multiple constraints as described in the original paper.", "cite_spans": [{"start": 4, "end": 26, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "B.2. <PERSON><PERSON>", "sec_num": null}, {"text": "SCBO (<PERSON><PERSON> & Poloczek, 2021) We implement this algorithm in BoTorch as there exists a reference implementation in the BoTorch documentation1 . In contrast to the original setup, where multiple continuous responses can be obtained from individual constraints during evaluation, our approach assumes a binary feasibility response. Consequently, we employ a single binary classifier in our setup, similar to the adoption of CEI, instead of multiple regressors for multiple constraints as described in the paper. Due to the binary nature of the constraint, we do not apply the Bilog transformation to the constraint values. However, we still apply the Gaussian copula transformation to the objective values and implement the restart of new trust regions according to the paper's descriptions.", "cite_spans": [{"start": 5, "end": 32, "text": "(Eriksson & Poloczek, 2021)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "B.2. <PERSON><PERSON>", "sec_num": null}, {"text": "SVM-CBO (Antonio, 2021) To conduct the SVM-CBO algorithm experiments, we utilized the code and framework provided by the authors. The experimental setup described in their paper involved 100 evaluations, comprising of 10 initial samples, 60 evaluations in phase 1, and 10 evaluations in phase 2. In our extended evaluations, where we employed 20 initial samples and a total of 200 evaluations, we maintained the same ratio as described in the original paper. Thus, we performed 127 evaluations in phase 1 and 63 evaluations in phase 2. We show more qualitative comparison of sample distributions on extra two 2D functions (LSQ and Townsend) in Figure 7 and 8 besides the <PERSON><PERSON><PERSON><PERSON> shown in the main paper. Same as the main paper, in each figure, the true function landscape is on the left, where darker color means a higher objective value and the white region means infeasible. On the right is the predicted function landscape (top: CEI, middle: SCBO, bottom: BE-CBO) where darker color means a higher objective value and the contour means the feasibility boundary (feasible inside, infeasible outside). Initial samples (grey), the rest evaluated samples (black) and the global optima (red) are also displayed.", "cite_spans": [{"start": 8, "end": 23, "text": "(Antonio, 2021)", "ref_id": "BIBREF0"}], "ref_spans": [{"start": 651, "end": 652, "text": "7", "ref_id": null}], "eq_spans": [], "section": "B.2. <PERSON><PERSON>", "sec_num": null}, {"text": "For the LSQ function, the objective function is relatively smooth so all three algorithms discover the global optimum in the end. BE-CBO discovers the global optimum more efficiently at 100 evaluations and also classifies a much more accurate constraint boundary compared to the other two algorithms. For the Townsend function, both CEI and SCBO get stuck in the two local optima in the middle, while BE-CBO sucessfully discovers the true global optimum on the upper right corner and also classifies the constraint boundary well.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Comparison", "sec_num": null}, {"text": "In order to empirically assess the speed efficiency of various algorithms, we collect and analyze the runtime statistics measured in seconds, shown in Figure 9 . In general, BE-CBO exhibits stable runtime across different problem dimensions ", "cite_spans": [], "ref_spans": [{"start": 158, "end": 159, "text": "9", "ref_id": null}], "eq_spans": [], "section": "C.2. Runtime Comparison", "sec_num": null}, {"text": "Although our motivation of developing BE-CBO is the observation that most real-world problems have their optima on the constraint boundary due to physical limits, to test the generality of our method, we construct synthetic functions with global optimum located at the interior of the feasible region. We modified the objective functions of <PERSON>, <PERSON><PERSON><PERSON><PERSON> and LSQ while leaving their constraint functions unchanged. Figure 10 shows the landscape of the modified functions. The analytical forms of the modified objective functions are as follows:", "cite_spans": [], "ref_spans": [{"start": 429, "end": 431, "text": "10", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "C.3. Synthetic Benchmark Problems With Interior Optima", "sec_num": null}, {"text": "• Townsend: f (x) = -[cos(((x 1 + 1) -0.1)x 2 )] 2 -(x 1 + 1) sin(3(x 1 + 1) + x 2 )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.3. Synthetic Benchmark Problems With Interior Optima", "sec_num": null}, {"text": "• <PERSON><PERSON><PERSON><PERSON>: f (x) = 0.1x 1 x 2 + 0.1(x 1 -x 2 + 1) 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.3. Synthetic Benchmark Problems With Interior Optima", "sec_num": null}, {"text": "• LSQ:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.3. Synthetic Benchmark Problems With Interior Optima", "sec_num": null}, {"text": "f (x) = (x 1 -0.4) 2 + (x 2 -0.45) 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.3. Synthetic Benchmark Problems With Interior Optima", "sec_num": null}, {"text": "We conduct experiments on these modified functions using CEI, SCBO and BE-CBO. The comparison results are shown in Figure 11 . The results show that BE-CBO can effectively discover interior optima in different functions. Though BE-CBO encourages exploration on the constraint boundary, we leverage the underlying Expected Improvement acquisition to decide where is the most promising region considering both the dynamic band and the whole predicted feasible region. In other words, whether to explore the boundary region or the interior feasible region depends on the predicted objective landscape, thus both boundary and interior optima can be discovered. We use a surrogate model to approximate the unknown constraints. This surrogate model needs to work as a binary classifier that predicts whether a point is feasible since we do not get any additional information when the point is infeasible. Instead of using a popular choice for surrogate models in Bayesian optimization, which are Gaussian Processes, here we show how Deep Ensembles can benefit the overall performance of our Bayesian optimization framework.", "cite_spans": [], "ref_spans": [{"start": 122, "end": 124, "text": "11", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "C.3. Synthetic Benchmark Problems With Interior Optima", "sec_num": null}, {"text": "We demonstrate how the classification accuracy of Deep Ensembles is more stable and in general outperforms the accuracy of Gaussian Processes (GPs). In real-world problems, it is often hard to find any feasible points due to complex constraints, which is reflected in some of the benchmark problems we used to test our approach (see further details in Section B.1). In such cases, we will typically obtain imbalanced data where most of the points are infeasible. Hence, to properly compare the classification accuracy, we use the Balanced Accuracy (<PERSON><PERSON><PERSON> et al., 2020) metric, defined as", "cite_spans": [{"start": 548, "end": 571, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "D.1.1. CLASSIFICATION PERFORMANCE COMPARISON", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T P R + T N R", "eq_num": "2"}], "section": "D.1.1. CLASSIFICATION PERFORMANCE COMPARISON", "sec_num": null}, {"text": "where T P R = true positive total positive is a true positive rate measuring the sensitivity and T N R = true negative total negative is a true negative rate measuring the specificity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1.1. CLASSIFICATION PERFORMANCE COMPARISON", "sec_num": null}, {"text": "The experiments are conducted over the same 10 random seeds for both Deep Ensembles and GPs. To test the GPs we simply replace the Deep Ensembles with GPs in our algorithm BE-CBO to train the classifier which models the constraints. All the other steps of our Bayesian optimization framework are identical. Starting from 10 initial random samples, we perform 200 function evaluations in each test run and check the classification accuracy after each evaluation when the classifiers are updated. The results are reported in Figure 12 . Note that GPs oscillate in accuracy between different iterations and perform poorly on complex real-world problems, which immediately affects the overall algorithm performance shown in Figure 13 . We compare the performance of our algorithm BE-CBO when using different surrogate models for the unknown constraints.", "cite_spans": [], "ref_spans": [{"start": 530, "end": 532, "text": "12", "ref_id": "FIGREF9"}, {"start": 727, "end": 729, "text": "13", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "D.1.1. CLASSIFICATION PERFORMANCE COMPARISON", "sec_num": null}, {"text": "In one setup, we run our proposed algorithm consisting of Deep Ensembles for modeling the constraints, while in the other setup, we replace the Deep Ensembles with Gaussian Processes (GPs). Both setups are tested on the same 10 initial random samples and we report the average performance and the standard deviation from 10 random seeds in Figure 13 . Deep Ensembles and GPs have comparable performance for simpler benchmark problems, while Deep Ensembles demonstrate superior performance in higher dimensions and Tension/Compression String design that is known to be challenging to model and find any feasible designs. Furthermore, we note that GPs perform well only when they are able to closely approximate the true boundary between the feasible and infeasible region of the design space. This finding is reflected in the classification accuracy of different surrogate models, as seen in Section D.1.1. One hyperparameter in our network ensemble is the number of MLPs used. However, we observed that varying this parameter does not have much significant effect on the overall performance of our algorithm. In our experiments, we use 5 MLPs in an ensemble for efficiency in memory and computing resources. Our tests show that increasing the number of MLPs up to 8 or decreasing down to 3 mostly has little effect on the overall performance (see Figure 14 ). ", "cite_spans": [], "ref_spans": [{"start": 347, "end": 349, "text": "13", "ref_id": "FIGREF6"}, {"start": 1354, "end": 1356, "text": "14", "ref_id": "FIGREF10"}], "eq_spans": [], "section": "D.1.1. CLASSIFICATION PERFORMANCE COMPARISON", "sec_num": null}, {"text": "The effect of the number of layers on the algorithm is shown in Figure 15 . We tested 1, 2, 3, 4 hidden layers with 64 log 2 d neurons in each layer where d is the problem dimension. The result shows that one hidden layer is insufficient in some cases since the network is too shallow which leads to underfitting. Two or more hidden layers perform similarly in general. The performance of BE-CBO is not particularly sensitive to the number of layers as long as it has more than one hidden layer.", "cite_spans": [], "ref_spans": [{"start": 71, "end": 73, "text": "15", "ref_id": "FIGREF11"}], "eq_spans": [], "section": "D.2.2. NUMBER OF HIDDEN LAYERS", "sec_num": null}, {"text": "The effect of the number of layers on the algorithm is shown in Figure 16 . To effectively learn across all problem dimensions, we scale the number of neurons in each layer w.r.t. the problem dimension following a logarithmic formula", "cite_spans": [], "ref_spans": [{"start": 71, "end": 73, "text": "16", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "D.2.3. NUMBER OF NEURONS IN A LAYER", "sec_num": null}, {"text": "N (d) = C log 2 (d)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2.3. NUMBER OF NEURONS IN A LAYER", "sec_num": null}, {"text": "where N is the number of neurons, d is the problem dimension and C is a constant factor. We tested C = 16, 32, 64, 128, 256 with 2 hidden layers. The results suggest that on most problems, the number of neurons do not matter much, but in some problems, networks with a low capacity (C = 16 or 32) are outperformed by networks with more neurons and it seems like C = 64 is a stable choice across all problems.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2.3. NUMBER OF NEURONS IN A LAYER", "sec_num": null}, {"text": "In the paper, we did experiments with a 3e-4 learning rate, which is a common choice for this hyperparameter. We now test 3e-3, 1e-3, 3e-4, 1e-4, 3e-5 learning rates and show results in Figure 17 . Since we set a fixed number of training iterations (1000), small learning rates 1e-4 and 3e-5 are outperformed by larger rates because the network training does not converge. Larger learning rates (3e-3, 1e-3, 3e-4) perform similarly with slight variations on different problems.", "cite_spans": [], "ref_spans": [{"start": 193, "end": 195, "text": "17", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "D.2.4. LEARNING RATE", "sec_num": null}, {"text": "To understand the performance of BE-CBO on more acquisition functions, we switch from EI to UCB (with beta = 0.1 as the default value in BoTorch) in our method and run empirical comparisons. As shown in Figure 18 , the results suggest that EI and UCB perform similarly well on our benchmark problems overall, with some performance differences in particular problems. In practice, users may select the proper acquisition function to be used in BE-CBO based on the desired properties (such as the explicit control over exploration in UCB).", "cite_spans": [], "ref_spans": [{"start": 210, "end": 212, "text": "18", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "D.3. Acquisition Function Choices", "sec_num": null}, {"text": "To validate whether the proposed boundary exploration strategy is successful, we modify our BE-CBO with different methods of coupling the constraint into the acquisition function instead of doing boundary exploration. For example, CEI proposes to multiply the feasible probability with the acquisition function, and SCBO applies a 0.5 upper bound on the feasible probability as a constraint on top of the acquisition function. Specifically, when ignoring the rest of their approaches, they can be written as optimizing: CEI: arg max x C(x)q(x) SCBO: arg max x q(x) s.t. C(x) ≥ 0.5", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.4. Dynamic Adaptation of Bounds for Boundary Exploration", "sec_num": null}, {"text": "For reference, our approach is optimizing: BE-CBO: arg max ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.4. Dynamic Adaptation of Bounds for Boundary Exploration", "sec_num": null}, {"text": "MIT CSAIL, USA", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "MIT-IBM Watson AI Lab, IBM Research, USA", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Evonik Operations GmbH, Germany. Correspondence to: <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>.Proceedings of the", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Note that SCBO applies a Gaussian copula transform to the objective function to magnify the optimum region.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://botorch.org/tutorials/scalable_constrained_bo", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://notebook.community/jrg365/gpytorch/examples/02_Simple_GP_Classification/Simple_ GP_Classification", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://botorch.org/tutorials/scalable_constrained_bo", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "We thank the reviewers for their constructive suggestions. This work is supported by the MIT-IBM Watson AI Lab, and its member company, Evonik. This work also received the support of a fellowship from \"la Caixa\" Foundation (ID *********). The fellowship code is LCF/BQ/EU21/11890103.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "https://github.com/ yunshengtian/BE-CBO", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "availability", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here. Thus, we develop two variants of BE-CBO based on CEI and SCBO's optimization objective and call them BE-CBO-M (M stands for multiplication of C(x)) and BE-CBO-C (C for the 0.5 cutoff on C(x)), while the rest of the algorithm (e.g., surrogate models) share the same design choices as BE-CBO. Figure 19 compares the performance between these two variants and BE-CBO on all benchmark problems.The results show that BE-CBO-M is clearly outperformed by BE-CBO-C and BE-CBO. Even though the multiplicative form of BE-CBO-M is a reasonable design in theory, in practice, the inaccuracy in constraint modeling can lead to failure cases easily. On one hand, multiplying C(x) with the acquisition may encourage sampling in the safe region instead of exploration; On the other hand, when overestimation of the acquisition value happens in the infeasible region, even with a small probability of C(x), it may still sample very far away in the infeasible region where the predicted q(x) is huge. The algorithm gets stuck in such scenario because the new evaluated infeasible point does not update the classifier much since it already has a low feasibility prediction, then in the next iteration, the algorithm will keep proposing points in the similar region.BE-CBO-C shares a similar performance with BE-CBO in low dimensional problems, but the performance starts to deteriorate when evaluated on high dimensional problems. Especially on the 30D Cantilever Beam Design problem, having a 0.5 cutoff bound in the objective makes BE-CBO-C explores much slower compared to BE-CBO. For more thorough evaluations, we implemented four additional high-dimensional real-world benchmark problems, including 10D multi-product batch plant design (Grossmann & Sargent, 1979) , 10D synchronous optimal pulsewidth modulation control for 9-level and 11-level inverters (Rathore et al., 2010) , and 14D industrial refrigeration system design (Paul H., 1987) . Besides the dimensionality, we would like to highlight the feasibility ratio (FR) as an important property of our benchmark problems, which indicates the ratio of feasible samples among a massive amount of random samples in the parameter space. As shown in Figure 20 , BE-CBO greatly outperforms BE-CBO-C on high-dimensional problems with a low feasibility ratio. To further validate this observation, we conduct controlled experiments on standard synthetic Ackley functions (constrained in the same way following SCBO) with varying dimensions. Figure 21 shows that BE-CBO becomes more advantageous than BE-CBO-C on problems with higher dimensions and lower feasibility ratios, and BE-CBO-C completely fails to improve in challenging cases. In such scenarios, exploration, or the ability to jump out of local minima, is crucial to the algorithm's performance. BE-CBO promotes exploration through our proposed dynamic bound strategy, while the fixed and conservative bound of BE-CBO-C discourages it from effective exploration.", "cite_spans": [{"start": 1921, "end": 1948, "text": "(Grossmann & Sargent, 1979)", "ref_id": "BIBREF21"}, {"start": 2040, "end": 2062, "text": "(<PERSON><PERSON> et al., 2010)", "ref_id": null}, {"start": 2112, "end": 2127, "text": "(<PERSON>, 1987)", "ref_id": null}], "ref_spans": [{"start": 497, "end": 499, "text": "19", "ref_id": null}, {"start": 2394, "end": 2396, "text": "20", "ref_id": null}, {"start": 2682, "end": 2684, "text": "21", "ref_id": null}], "eq_spans": [], "section": "Impact Statement", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Sequential model based optimization of partially defined functions under unknown constraints", "authors": [{"first": "C", "middle": [], "last": "Antonio", "suffix": ""}], "year": 2021, "venue": "Journal of Global Optimization", "volume": "79", "issue": "2", "pages": "281--303", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>. Sequential model based optimization of par- tially defined functions under unknown constraints. <PERSON><PERSON><PERSON> <PERSON><PERSON> of Global Optimization, 79(2):281-303, 2021.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Admmbo: Bayesian optimization with unknown constraints using admm", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Coll-Font", "suffix": ""}, {"first": "D", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["G"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "J. <PERSON>. Learn. Res", "volume": "20", "issue": "123", "pages": "1--26", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, J. G. Ad- mmbo: Bayesian optimization with unknown constraints using admm. J. Mach. Learn. Res., 20(123):1-26, 2019.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Gaussian process optimization with failures: classification and convergence proof", "authors": [{"first": "F", "middle": [], "last": "Bach<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Journal of Global Optimization", "volume": "78", "issue": "", "pages": "483--506", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> pro- cess optimization with failures: classification and con- vergence proof. Journal of Global Optimization, 78:483- 506, 2020.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "BoTorch: A Framework for Efficient Monte-Carlo Bayesian Optimization", "authors": [{"first": "M", "middle": [], "last": "Balandat", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["R"], "last": "Jiang", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["G"], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: A Frame- work for Efficient Monte-Carlo Bayesian Optimization. In Advances in Neural Information Processing Systems 33, 2020. URL http://arxiv.org/abs/1910. 06403.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Globally optimal safe robot learning", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Turch<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Trimpe", "suffix": ""}, {"first": "", "middle": [], "last": "Gosafe", "suffix": ""}], "year": 2021, "venue": "2021 IEEE International Conference on Robotics and Automation (ICRA)", "volume": "", "issue": "", "pages": "4452--4458", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: Globally optimal safe robot learning. In 2021 IEEE International Conference on Robotics and Automa- tion (ICRA), pp. 4452-4458. IEEE, 2021.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "A study of mathematical programming methods for structural optimization. part i: Theory", "authors": [{"first": "A", "middle": ["D"], "last": "Belegundu", "suffix": ""}, {"first": "J", "middle": ["S"], "last": "Arora", "suffix": ""}], "year": 1985, "venue": "International Journal for Numerical Methods in Engineering", "volume": "21", "issue": "9", "pages": "1583--1599", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON><PERSON>, J. S. A study of mathematical programming methods for structural optimization. part i: Theory. International Journal for Numerical Methods in Engineering, 21(9):1583-1599, 1985.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "The power of ensembles for active learning in image classification", "authors": [{"first": "W", "middle": ["H"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "9368--9377", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, J. <PERSON>. The power of ensembles for active learning in image classification. In Proceedings of the IEEE conference on computer vision and pattern recognition, pp. 9368-9377, 2018.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Bayesian optimization with safety constraints: safe and automatic parameter tuning in robotics", "authors": [{"first": "F", "middle": [], "last": "Berkenkamp", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["P"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Machine Learning", "volume": "112", "issue": "10", "pages": "3713--3747", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, A. P. Bayesian optimization with safety constraints: safe and automatic parameter tuning in robotics. Machine Learning, 112(10): 3713-3747, 2023.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "An adaptive aggregation-based approach for expensively constrained black-box optimization problems", "authors": [{"first": "G", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Gjernes", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "", "suffix": ""}], "year": 2018, "venue": "Journal of Mechanical Design", "volume": "140", "issue": "9", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>. An adaptive aggregation-based approach for expensively constrained black-box optimization problems. Journal of Mechanical Design, 140(9):091402, 2018.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Constraint-handling in genetic algorithms through the use of dominance-based tournament selection", "authors": [{"first": "C", "middle": ["A C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": ["M"], "last": "Montes", "suffix": ""}], "year": 2002, "venue": "Advanced Engineering Informatics", "volume": "16", "issue": "3", "pages": "193--203", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON> and <PERSON>, E. M. Constraint-handling in genetic algorithms through the use of dominance-based tournament selection. Advanced Engineering Informatics, 16(3):193-203, 2002.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Structure discovery in nonparametric regression through compositional kernel search", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Grosse", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "1166--1174", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Structure discovery in nonparametric regres- sion through compositional kernel search. In Interna- tional Conference on Machine Learning, pp. 1166-1174. PMLR, 2013.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Scalable constrained bayesian optimization", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Artificial Intelligence and Statistics", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> constrained bayesian optimization. In International Conference on Artificial Intelligence and Statistics, 2021.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Scalable global optimization via local bayesian optimization", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "32", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>ble global optimization via local bayesian optimization. Advances in neural information processing systems, 32, 2019.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Deep ensembles: A loss landscape perspective", "authors": [{"first": "S", "middle": [], "last": "Fort", "suffix": ""}, {"first": "H", "middle": [], "last": "Hu", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1912.02757"]}, "num": null, "urls": [], "raw_text": "Fort, S., <PERSON>, <PERSON>, and <PERSON><PERSON>, B. <PERSON> en- sembles: A loss landscape perspective. arXiv preprint arXiv:1912.02757, 2019.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Bayesian optimization", "authors": [{"first": "P", "middle": ["I"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Recent advances in optimization and modeling of contemporary problems", "volume": "", "issue": "", "pages": "255--278", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> optimization. In Recent advances in optimization and modeling of contemporary problems, pp. 255-278. Informs, 2018.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Bayesian optimization with inequality constraints", "authors": [{"first": "J", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": ["Q"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["P"], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "ICML", "volume": "2014", "issue": "", "pages": "937--945", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> <PERSON>. Bay<PERSON>an optimization with in- equality constraints. In ICML, volume 2014, pp. 937-945, 2014.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Gpytorch: Blackbox matrix-matrix gaussian process inference with gpu acceleration", "authors": [{"first": "J", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": ["Q"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["G"], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> <PERSON><PERSON>: Blackbox matrix-matrix gaus- sian process inference with gpu acceleration. In Advances in Neural Information Processing Systems, 2018.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "A survey of uncertainty in deep neural networks", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["R N"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "K<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Artificial Intelligence Review", "volume": "56", "issue": "Suppl 1", "pages": "1513--1589", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. A survey of uncertainty in deep neural networks. Artificial Intelligence Review, 56(Suppl 1):1513-1589, 2023.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Bayesian optimization with unknown constraints", "authors": [{"first": "M", "middle": ["A"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["P"], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "30th Conference on Uncertainty in Artificial Intelligence, UAI 2014", "volume": "", "issue": "", "pages": "250--259", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, R. P. Bayesian opti- mization with unknown constraints. In 30th Conference on Uncertainty in Artificial Intelligence, UAI 2014, pp. 250-259. AUAI Press, 2014.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Modeling an augmented lagrangian for blackbox constrained optimization", "authors": [{"first": "R", "middle": ["B"], "last": "Gramacy", "suffix": ""}, {"first": "G", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Le Digabel", "suffix": ""}, {"first": "H", "middle": ["K"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Ra<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": ["M"], "last": "Wild", "suffix": ""}], "year": 2016, "venue": "Technometrics", "volume": "58", "issue": "1", "pages": "1--11", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>., and <PERSON>, S. M. Modeling an aug- mented lagrangian for blackbox constrained optimization. Technometrics, 58(1):1-11, 2016.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "An active-set trust-region method for derivative-free nonlinear boundconstrained optimization", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": ["L"], "last": "Toint", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2011, "venue": "Optimization Methods and Software", "volume": "26", "issue": "4-5", "pages": "873--894", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>. An active-set trust-region method for derivative-free nonlinear bound- constrained optimization. Optimization Methods and Software, 26(4-5):873-894, 2011.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Optimum design of multipurpose chemical plants", "authors": [{"first": "I", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["W"], "last": "Sargent", "suffix": ""}], "year": 1979, "venue": "Industrial & Engineering Chemistry Process Design and Development", "volume": "18", "issue": "2", "pages": "343--348", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> <PERSON>. Optimum design of multipurpose chemical plants. Industrial & Engineering Chemistry Process Design and Development, 18(2):343- 348, 1979.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Multi-objective design optimisation of rolling bearings using genetic algorithms", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["B"], "last": "<PERSON>", "suffix": ""}], "year": 2007, "venue": "Mechanism and Machine Theory", "volume": "42", "issue": "10", "pages": "1418--1443", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, S. B. Multi-objective de- sign optimisation of rolling bearings using genetic algo- rithms. Mechanism and Machine Theory, 42(10):1418- 1443, 2007.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Reducing the time complexity of the derandomized evolution strategy with covariance matrix adaptation (cma-es)", "authors": [{"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2003, "venue": "Evolutionary computation", "volume": "11", "issue": "1", "pages": "1--18", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>uc- ing the time complexity of the derandomized evolution strategy with covariance matrix adaptation (cma-es). Evo- lutionary computation, 11(1):1-18, 2003.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Derivative-free filter simulated annealing method for constrained continuous global optimization", "authors": [{"first": "A.-R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Fukushima", "suffix": ""}], "year": 2006, "venue": "Journal of Global optimization", "volume": "35", "issue": "4", "pages": "521--550", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Derivative-free filter simulated annealing method for constrained continuous global optimization. Journal of Global optimization, 35 (4):521-550, 2006.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Entropy search for informationefficient global optimization", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "Journal of Machine Learning Research", "volume": "13", "issue": "6", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, C<PERSON> J. Entropy search for information- efficient global optimization. Journal of Machine Learn- ing Research, 13(6), 2012.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Scalable variational gaussian process classification", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Artificial Intelligence and Statistics", "volume": "", "issue": "", "pages": "351--360", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ble variational gaussian process classification. In Artificial Intelligence and Statistics, pp. 351-360. PMLR, 2015.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Predictive entropy search for efficient global optimization of black-box functions", "authors": [{"first": "J", "middle": ["M"], "last": "Hernández<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "Advances in neural information processing systems", "volume": "27", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Predictive entropy search for efficient global optimization of black-box functions. Advances in neural information processing systems, 27, 2014.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Predictive entropy search for bayesian optimization with unknown constraints", "authors": [{"first": "J", "middle": ["M"], "last": "Hernández<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "1699--1707", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Predictive entropy search for bayesian optimization with unknown constraints. In International conference on machine learning, pp. 1699- 1707. PMLR, 2015.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Investigating bayesian optimization for rail network optimization", "authors": [{"first": "R", "middle": [], "last": "Hickish", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "International Journal of Rail Transportation", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Investigat- ing bayesian optimization for rail network optimiza- tion. International Journal of Rail Transportation, Octo- ber 2019. URL http://eprints.whiterose.ac. uk/151331/.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Portfolio allocation for bayesian optimization", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2011, "venue": "UAI", "volume": "", "issue": "", "pages": "327--336", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Portfolio allocation for bayesian optimization. In UAI, pp. 327-336, 2011.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Blessings of maintaining infeasible solutions for constrained multi-objective optimization problems", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2008, "venue": "IEEE Congress on Evolutionary Computation", "volume": "", "issue": "", "pages": "2780--2787", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> of maintain- ing infeasible solutions for constrained multi-objective optimization problems. In 2008 IEEE Congress on Evo- lutionary Computation (IEEE World Congress on Com- putational Intelligence), pp. 2780-2787. IEEE, 2008.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "A feasible-ratio control technique for constrained optimization", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Li", "suffix": ""}], "year": 2019, "venue": "Information Sciences", "volume": "502", "issue": "", "pages": "201--217", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, C. A feasible-ratio control tech- nique for constrained optimization. Information Sciences, 502:201-217, 2019.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Efficient global optimization of expensive black-box functions", "authors": [{"first": "D", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": ["J"], "last": "<PERSON>", "suffix": ""}], "year": 1998, "venue": "Journal of Global optimization", "volume": "13", "issue": "4", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>. Efficient global optimization of expensive black-box functions. Journal of Global optimization, 13(4):455, 1998.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Tuning hyperparameters without grad students: Scalable and robust bayesian optimisation with dragonfly", "authors": [{"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": ["R"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Pa<PERSON>", "suffix": ""}, {"first": "C", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": ["P"], "last": "Xi<PERSON>", "suffix": ""}], "year": 2020, "venue": "The Journal of Machine Learning Research", "volume": "21", "issue": "1", "pages": "3098--3124", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>., and <PERSON><PERSON>, E. P. Tuning hyperparameters without grad students: Scalable and robust bayesian optimisation with dragonfly. The Journal of Machine Learning Research, 21(1):3098- 3124, 2020.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Fundamentals of machine learning for predictive data analytics: algorithms, worked examples, and case studies", "authors": [{"first": "J", "middle": ["D"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>- mentals of machine learning for predictive data analytics: algorithms, worked examples, and case studies. MIT press, 2020.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "A method for stochastic optimization", "authors": [{"first": "D", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ba", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1412.6980"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>: A method for stochastic optimization. arXiv preprint arXiv:1412.6980, 2014.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Adaptive and safe bayesian optimization in high dimensions via one-dimensional subspaces", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Ischebeck", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "3429--3438", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, R., and <PERSON>, A. Adaptive and safe bayesian optimization in high dimensions via one-dimensional subspaces. In International Conference on Machine Learning, pp. 3429- 3438. PMLR, 2019.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "A test-suite of non-convex constrained optimization problems from the real-world and some baseline results. Swarm and Evolutionary Computation", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["Z"], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": ["N"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "56", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, P. <PERSON>, and <PERSON>, S. A test-suite of non-convex con- strained optimization problems from the real-world and some baseline results. Swarm and Evolutionary Compu- tation, 56:100693, 2020.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Uncertainty quantification using bayesian neural networks in classification: Application to biomedical image segmentation", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J.-<PERSON>", "middle": [], "last": "Won", "suffix": ""}, {"first": "B", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Computational Statistics & Data Analysis", "volume": "142", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> <PERSON> Uncer- tainty quantification using bayesian neural networks in classification: Application to biomedical image segmen- tation. Computational Statistics & Data Analysis, 142: 106816, 2020.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Simple and scalable predictive uncertainty estimation using deep ensembles", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON> and scalable predictive uncertainty estimation using deep ensembles. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Constrained optimization problems in mechanical engineering design using a real-coded steady-state genetic algorithm", "authors": [{"first": "A", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": ["B"], "last": "<PERSON>", "suffix": ""}], "year": 2010, "venue": "Mecánica Computacional", "volume": "29", "issue": "95", "pages": "9287--9303", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, C. C., and <PERSON>, F. B. Constrained optimization problems in mechanical engineering design using a real-coded steady-state genetic algorithm. Mecánica Computacional, 29(95):9287-9303, 2010.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "A study of bayesian neural network surrogates for bayesian optimization", "authors": [{"first": "Y", "middle": ["L"], "last": "Li", "suffix": ""}, {"first": "T", "middle": ["G"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["G"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.20028"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, A. G. A study of bayesian neural network surrogates for bayesian opti- mization. arXiv preprint arXiv:2305.20028, 2023.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Optimization under constraints by applying an asymmetric entropy measure", "authors": [{"first": "D", "middle": ["V"], "last": "Lind<PERSON>", "suffix": ""}, {"first": "H", "middle": ["K"], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "Journal of Computational and Graphical Statistics", "volume": "24", "issue": "2", "pages": "379--393", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> <PERSON> Optimization under con- straints by applying an asymmetric entropy measure. Journal of Computational and Graphical Statistics, 24 (2):379-393, 2015.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Handling constrained multiobjective optimization problems via bidirectional coevolution", "authors": [{"first": "Z.-Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B.-C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "IEEE Transactions on Cybernetics", "volume": "52", "issue": "10", "pages": "10163--10176", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> constrained multiobjective optimization problems via bidirectional coevolution. IEEE Transactions on Cybernetics, 52(10): 10163-10176, 2021.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Automatic gait optimization with gaussian process regression", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Bowling", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2007, "venue": "Proceedings of the 20th International Joint Conference on Artifical Intelligence, IJCAI'07", "volume": "", "issue": "", "pages": "944--949", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Automatic gait optimization with gaussian process regres- sion. In Proceedings of the 20th International Joint Con- ference on Artifical Intelligence, IJCAI'07, pp. 944-949, San Francisco, CA, USA, 2007. Morgan Kaufmann Pub- lishers Inc.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "A survey of image classification methods and techniques for improving classification performance", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2007, "venue": "International journal of Remote sensing", "volume": "28", "issue": "5", "pages": "823--870", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. A survey of image classification methods and techniques for improving classification per- formance. International journal of Remote sensing, 28 (5):823-870, 2007.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Deep learning-based text classification: a comprehensive review", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Cambria", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Gao", "suffix": ""}], "year": 2021, "venue": "ACM computing surveys (CSUR)", "volume": "54", "issue": "3", "pages": "1--40", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>., Cambria, E<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Deep learning-based text classification: a comprehensive review. ACM computing surveys (CSUR), 54(3):1-40, 2021.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "On bayesian methods for seeking the extremum", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1974, "venue": "Optimization Techniques IFIP Technical Conference", "volume": "", "issue": "", "pages": "400--404", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> On bayesian methods for seeking the extremum. In Optimization Techniques IFIP Technical Conference: Novosibirsk, July 1-7, 1974, pp. 400-404. Springer, 1975.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "fig_num": "1", "uris": null, "text": "Figure 1: Comparison of modeling constraints between Gaussian Processes (GP) and Deep Ensembles (DE) on the LSQ problem with three sets of random sample evaluations. Green dots represent feasible designs and red dots represent infeasible designs. We observe that DE tends to be more robust than GP in capturing complex boundaries. (a) Failure case of GP; (b) Successful case of GP; (c) Both GP and DE are not fitted well due to the poor sample distribution but DE is closer to the ground truth.", "num": null}, "FIGREF1": {"type_str": "figure", "fig_num": "3", "uris": null, "text": "Figure 3: Quantitative comparison of different algorithms including BE-CBO on all benchmark problems. The current best value is shown w.r.t. the number of function evaluations. Each experiment has 10 initial random samples and 200 total evaluations. The curve is averaged over 10 different random seeds and the standard deviation is shown as a shaded region.", "num": null}, "FIGREF2": {"type_str": "figure", "fig_num": "4", "uris": null, "text": "Figure 4: Qualitative comparison of sample distributions from different algorithms on the Si<PERSON><PERSON><PERSON> benchmark. Left: The true function landscape, where darker color means a higher objective value and the white region is infeasible. Right: The predicted function landscape (top: CEI, middle: SCBO 1 , bottom: BE-CBO) where darker color means a higher objective value and the contour represents the feasibility boundary (feasible inside, infeasible outside). Grey: initial samples, black: evaluated samples guided by each algorithm, and red: the global optima.", "num": null}, "FIGREF3": {"type_str": "figure", "fig_num": "5", "uris": null, "text": "Figure 5: Feasibility ratio comparison of different algorithms including our BE-CBO on synthetic test functions and real-world problems. The error bar charts represent the mean and variance over 10 different random seeds.", "num": null}, "FIGREF4": {"type_str": "figure", "fig_num": "6", "uris": null, "text": "Figure 6: Comparisons between BE-CBO trained with VI and MLE respectively, averaged over 10 random seeds.", "num": null}, "FIGREF5": {"type_str": "figure", "fig_num": null, "uris": null, "text": "e, ξ) where fi = ri D b and fo = ro D b . The bounds of the paramters are D m ∈ [125, 150], D b ∈ [10.5, 31.5], Z ∈ [4, 50], f i ∈ [0.515, 0.6], f o ∈ [0.515, 0.6], <PERSON> Dmin ∈ [0.4, 0.5], K Dmax ∈ [0.6, 0.7], ε ∈ [0.3, 0.4], e ∈ [0.02, 0.1], ξ ∈ [0.6, 0.85]. The objective function is defined as f (x) = C d where", "num": null}, "FIGREF6": {"type_str": "figure", "fig_num": "1", "uris": null, "text": "Figure 7: Qualitative comparison of sample distributions from different algorithms on the LSQ benchmark.", "num": null}, "FIGREF7": {"type_str": "figure", "fig_num": "89", "uris": null, "text": "Figure 8: Qualitative comparison of sample distributions from different algorithms on the Townsend benchmark.", "num": null}, "FIGREF8": {"type_str": "figure", "fig_num": "1011", "uris": null, "text": "Figure 10: Landscape of the modified Townsend, <PERSON><PERSON><PERSON><PERSON> and LSQ functions. The red star indicates the global optimum location, which is inside the feasible region instead of on the constraint boundary in their original forms.", "num": null}, "FIGREF9": {"type_str": "figure", "fig_num": "12", "uris": null, "text": "Figure 12: Comparison of classification accuracy between Gaussian Processes and Deep Ensembles when used as classifiers in our Bayesian optimization framework. Classification accuracy is computed with the Balanced Accuracy metric. Curves show the average over 10 random seeds and shaded regions represent standard deviation.", "num": null}, "FIGREF10": {"type_str": "figure", "fig_num": "14", "uris": null, "text": "Figure14: Performance comparison of our algorithm when using different number of MLPs in ensemble. The current best value found by an algorithm is shown w.r.t. the number of function evaluations and the performance is averaged over 10 initial seeds for each experiment.", "num": null}, "FIGREF11": {"type_str": "figure", "fig_num": "15", "uris": null, "text": "Figure 15: Comparison of BE-CBO with different number of hidden layers for the Deep Ensemble classifier. The current best value is shown w.r.t. the number of function evaluations. The curve is averaged over 10 different initial random seeds.The performance of BE-CBO is not particularly sensitive to the number of layers as long as it has more than one hidden layer.", "num": null}, "FIGREF12": {"type_str": "figure", "fig_num": "1617", "uris": null, "text": "Figure 16: Comparison of BE-CBO with different number of neurons in a layer for the Deep Ensemble classifier. The current best value is shown w.r.t. the number of function evaluations. The curve is averaged over 10 different initial random seeds. The performance of BE-CBO is not sensitive to the number of neurons as long as it has at least 64 neurons in a layer.", "num": null}, "FIGREF13": {"type_str": "figure", "fig_num": "19", "uris": null, "text": "Figure 19: Comparison of BE-CBO with different variants of constraint coupling in the acquisition function. The current best value is shown w.r.t. the number of function evaluations. The curve is averaged over 10 different initial random seeds.", "num": null}, "FIGREF14": {"type_str": "figure", "fig_num": "2021", "uris": null, "text": "Figure 20: Comparison between BE-CBO and BE-CBO-C on additional high-dimensional real-world problems.", "num": null}, "TABREF2": {"type_str": "table", "text": "International Journal of Recent Trends in Engineering, 1(5):21, 2009. <PERSON>, <PERSON><PERSON> design of an industrial refrigeration system. In Proc. of Int. Conf. on Optimization Techniques and Applications, pp. 427-435, 1987. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Safe exploration for optimization with gaussian processes. In International conference on machine learning, pp. 997-1005. PMLR, 2015.<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Dash, S., and Mo<PERSON><PERSON><PERSON>, A. An approach for iris plant classification using neural network.", "html": null, "content": "<table><tr><td/><td>International Journal on Soft Computing, 3(1):79, 2012.</td></tr><tr><td><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, R. <PERSON>., and <PERSON>, V. J.</td><td><PERSON>, <PERSON><PERSON>. On the likelihood that one unknown</td></tr><tr><td>Advanced Optimization Techniques. Springer, 2012.</td><td>probability exceeds another in view of the evidence of</td></tr><tr><td/><td>two samples. Biometrika, 25(3-4):285-294, 1933.</td></tr><tr><td><PERSON><PERSON>, C. E. and <PERSON>, C. K. I. <PERSON> Pro-cesses for Machine Learning (Adaptive Computation</td><td>Tom<PERSON>, M. B., Swaroop, S., and Turner, R. E. <PERSON>eural</td></tr><tr><td>and Machine Learning). The MIT Press, 2005. ISBN 026218253X.</td><td>network ensembles and variational inference revisited. In 1st Symposium on Advances in Approximate Bayesian Inference, pp. 1-11, 2018.</td></tr><tr><td>Rathore, A. K., <PERSON>z, J., and Boller, T. Synchronous opti-mal pulsewidth modulation for low-switching-frequency control of medium-voltage multilevel inverters. IEEE</td><td>Townsend, A. Constrained optimization in chebfun. chebfun.org, 2014. Last accessed 2023-05-16.</td></tr><tr><td>Transactions on Industrial Electronics, 57(7):2374-2381, 2010.</td><td>Tran, D., Dusenberry, M., Van Der Wilk, M., and Hafner, D. Bayesian layers: A module for neural network uncertainty.</td></tr><tr><td>Ray, T. and Saini, P. Engineering design optimization using a swarm with an intelligent information sharing among</td><td>Advances in neural information processing systems, 32, 2019.</td></tr><tr><td>individuals. Engineering Optimization, 33(6):735-748,</td><td/></tr><tr><td>2001.</td><td/></tr><tr><td>Ray, T., Singh, H. K., Isaacs, A., and Smith, W. Infeasibility</td><td/></tr><tr><td>driven evolutionary algorithm for constrained optimiza-</td><td/></tr><tr><td>tion. Constraint-handling in evolutionary optimization,</td><td/></tr><tr><td>pp. 145-165, 2009.</td><td/></tr><tr><td>Sergeyev, Y. D., Kvasov, D. E., and Mukhametzhanov, M. S.</td><td/></tr><tr><td>A generator of multiextremal test classes with known</td><td>Yu, Z., Ramakrishnan, V., and Meinzer, C. Simulation</td></tr><tr><td>solutions for black-box-constrained global optimization.</td><td>optimization for bayesian multi-arm multi-stage clinical</td></tr><tr><td>IEEE Transactions on Evolutionary Computation, 26(6):</td><td>trial with binary endpoints. Journal of Biopharmaceutical</td></tr><tr><td>1261-1270, 2021.</td><td>Statistics, 29:1-12, 02 2019. doi: 10.1080/10543406.</td></tr><tr><td/><td>2019.1577682.</td></tr><tr><td>Shahriari, B., Swersky, K., Wang, Z., Adams, R. P., and de</td><td/></tr><tr><td>Freitas, N. Taking the human out of the loop: A review</td><td>Zhang, M., Cui, Z., Neumann, M., and Chen, Y. An end-to-</td></tr><tr><td>of bayesian optimization. Proceedings of the IEEE, 104</td><td>end deep learning architecture for graph classification. In</td></tr><tr><td>(1):148-175, 2016.</td><td>Proceedings of the AAAI conference on artificial intelli-</td></tr><tr><td/><td>gence, volume 32, 2018.</td></tr><tr><td>Simionescu, P. A. Computer-aided graphing and simulation</td><td/></tr><tr><td>tools for AutoCAD users, volume 32. CRC Press, 2014.</td><td>Zhang, Y., Zhang, X., and Frazier, P. Constrained two-step</td></tr><tr><td/><td>look-ahead bayesian optimization. Advances in Neural</td></tr><tr><td>Snoek, J., Larochelle, H., and Adams, R. P. Practical</td><td>Information Processing Systems, 34:12563-12575, 2021.</td></tr><tr><td>bayesian optimization of machine learning algorithms.</td><td/></tr><tr><td>In Advances in neural information processing systems,</td><td/></tr><tr><td>pp. 2951-2959, 2012.</td><td/></tr></table>", "num": null}, "TABREF4": {"type_str": "table", "text": "Details of the 9 real-world design optimization problems. d is the dimension of design variables, g is the number of unknown inequality constraints, g * is the number of active constraints at the optimum, f (x * ) is the optimal performance value.", "html": null, "content": "<table><tr><td colspan=\"4\">Problem Name g  Three-bar truss design problem d g 2 3 1</td><td>2.6389E+02</td></tr><tr><td>Tension/compression spring design</td><td>3</td><td>3</td><td>2</td><td>1.2665E-02</td></tr><tr><td>Welded beam design</td><td>4</td><td>5</td><td>1</td><td>2.4453E+00</td></tr><tr><td colspan=\"2\">Gas transmission compressor design 4</td><td>1</td><td>1</td><td>2.9648E+06</td></tr><tr><td>Pressure vessel design</td><td>4</td><td>4</td><td>2</td><td>5.8853E+03</td></tr><tr><td>Speed reducer design</td><td>7</td><td colspan=\"2\">11 2</td><td>2.9944E+03</td></tr><tr><td>Planetary gear train design</td><td>9</td><td>9</td><td>1</td><td>3.6859E+00</td></tr><tr><td>Rolling element bearing</td><td colspan=\"2\">10 9</td><td>3</td><td>8.3918E+04</td></tr><tr><td>Cantilever beam design</td><td colspan=\"3\">30 21 1</td><td>1.5731E+02</td></tr></table>", "num": null}, "TABREF5": {"type_str": "table", "text": "Figure13: Performance of our algorithm BE-CBO when using Deep Ensembles vs Gaussian Processes for modeling unknown constraints. The current best value found by an algorithm is shown w.r.t. the number of function evaluations. Experiments are run independently from 10 random seeds. The bold curves represent the average in performance over all 10 seeds and shaded areas reflect the standard deviation.", "html": null, "content": "<table><tr><td>50 Townsend Function 100 150 (2D) 50 100 150 Number of evaluations 200 200 1e7 Gas Transmission Compressor Design 2.0 1.9 1.8 1.7 1.6 1.5 1.4 0.4 0.6 0.8 1.0 Objective Objective (4D) 20000 0.06 0.05 0.04 0.03 0.02 0.07 40000 100000 80000 60000</td><td>50 Simionescu Function 100 150 (2D) 50 100 150 Number of evaluations 200 200 Pressure Vessel Design (4D) Gaussian Processes 0.8 0.9 1.0 1.1 LSQ Function (2D) 0.7 50 100 150 0.6 4200 4400 4600 Speed Reducer Design (7D) 4000 50 100 150 Number of evaluations 200 200 3000 3400 3800 3600 3200 Deep Ensembles 290 Three Bar Truss Design (2D) 310 300 280 270 50 100 150 6.0 Planetary Gear Train (9D) 6.5 5.5 5.0 4.5 50 100 150 Number of evaluations 200 200 4.0 3.5 Global optimum Tension/Compression String Design (3D) 0.07 0.06 0.05 0.04 0.03 0.02 50 100 150 200 0.01 Rolling Element Bearing Design (10D) 30000 40000 50000 60000 70000 80000 50 100 150 200 Number of evaluations</td><td>140 120 100 80 60 40 20 0 0.12 0.10 0.08 0.06 0.04 0.02</td><td>Welded Beam Design (4D) 50 100 150 Cantilever Beam Design (30D) 50 100 150 Number of evaluations 200 200</td></tr></table>", "num": null}}}}