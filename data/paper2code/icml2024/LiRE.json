{"paper_id": "LiRE", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:39:42.962161Z"}, "title": "Listwise Reward Estimation for Offline Preference-based Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Seoul National University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Seoul National University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ahn", "suffix": "", "affiliation": {"laboratory": "", "institution": "Seoul National University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Moon", "suffix": "", "affiliation": {"laboratory": "", "institution": "Seoul National University", "location": {}}, "email": "<<EMAIL>>."}], "year": "", "venue": null, "identifiers": {}, "abstract": "In Reinforcement Learning (RL), designing precise reward functions remains to be a challenge, particularly when aligning with human intent. Preference-based RL (PbRL) was introduced to address this problem by learning reward models from human feedback. However, existing PbRL methods have limitations as they often overlook the second-order preference that indicates the relative strength of preference. In this paper, we propose Listwise Reward Estimation (LiRE), a novel approach for offline PbRL that leverages secondorder preference information by constructing a Ranked List of Trajectories (RLT), which can be efficiently built by using the same ternary feedback type as traditional methods. To validate the effectiveness of LiRE, we propose a new offline PbRL dataset that objectively reflects the effect of the estimated rewards. Our extensive experiments on the dataset demonstrate the superiority of LiRE, i.e., outperforming state-of-the-art baselines even with modest feedback budgets and enjoying robustness with respect to the number of feedbacks and feedback noise. Our code is available at https://github.com/chwoong/LiRE", "pdf_parse": {"paper_id": "LiRE", "_pdf_hash": "", "abstract": [{"text": "In Reinforcement Learning (RL), designing precise reward functions remains to be a challenge, particularly when aligning with human intent. Preference-based RL (PbRL) was introduced to address this problem by learning reward models from human feedback. However, existing PbRL methods have limitations as they often overlook the second-order preference that indicates the relative strength of preference. In this paper, we propose Listwise Reward Estimation (LiRE), a novel approach for offline PbRL that leverages secondorder preference information by constructing a Ranked List of Trajectories (RLT), which can be efficiently built by using the same ternary feedback type as traditional methods. To validate the effectiveness of LiRE, we propose a new offline PbRL dataset that objectively reflects the effect of the estimated rewards. Our extensive experiments on the dataset demonstrate the superiority of LiRE, i.e., outperforming state-of-the-art baselines even with modest feedback budgets and enjoying robustness with respect to the number of feedbacks and feedback noise. Our code is available at https://github.com/chwoong/LiRE", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Reinforcement Learning (RL) has demonstrated considerable success in various domains such as robotics (<PERSON><PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2018) , game (<PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2013; <PERSON><PERSON><PERSON> et al., 2019) , autonomous driving (<PERSON> et al., 2021) , and real-world tasks (<PERSON> et al., 2018; <PERSON><PERSON><PERSON><PERSON> et al., 2019 ). An essential component of RL is to define suitable and precise reward functions so that an RL agent can be trained successfully (<PERSON><PERSON><PERSON> et al., 2017) .", "cite_spans": [{"start": 102, "end": 125, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF15"}, {"start": 126, "end": 151, "text": "<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF21"}, {"start": 159, "end": 180, "text": "(<PERSON> et al., 2017;", "ref_id": "BIBREF42"}, {"start": 181, "end": 199, "text": "<PERSON><PERSON><PERSON> et al., 2013;", "ref_id": "BIBREF35"}, {"start": 200, "end": 221, "text": "<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF52"}, {"start": 243, "end": 263, "text": "(<PERSON> et al., 2021)", "ref_id": null}, {"start": 287, "end": 305, "text": "(<PERSON> et al., 2018;", "ref_id": "BIBREF48"}, {"start": 306, "end": 327, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2019", "ref_id": "BIBREF9"}, {"start": 459, "end": 479, "text": "(<PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF55"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "However, designing the reward function is time-consuming, especially if we want to align it with human intent (<PERSON><PERSON><PERSON> & <PERSON>, 2024) . This shortcoming has led to research on learning the reward model from human feedback without explicitly designing the reward function. While expert demonstration is one type of human feedback (<PERSON><PERSON><PERSON> & <PERSON>, 2004) , recent papers use preference feedback on which of a pair of trajectory segments is preferred since it is a significantly easier type of feedback to collect (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) . More specifically, the common approach for the Preference-based RL (PbRL) consists of two steps: (1) learn a reward model using preference feedback from trajectory segment pairs, then (2) apply ordinary RL algorithms with the learned reward model. After successfully training a robot agent with PbRL (<PERSON><PERSON> et al., 2017) , it was shown that novel behaviors aligned with human intent, e.g., backflips, can also be learned (<PERSON> et al., 2021b) , while learning such behavior would be extremely hard from explicitly hand-coded rewards. The PbRL framework has gained popularity in both online (<PERSON> et al., 2021; <PERSON> et al., 2021) and offline (<PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON><PERSON><PERSON> & <PERSON>igh, 2024) settings, in which the former allows the agents to interact with their environments, while the latter does not.", "cite_spans": [{"start": 110, "end": 132, "text": "(Hejna & Sadigh, 2024)", "ref_id": null}, {"start": 328, "end": 347, "text": "(<PERSON><PERSON><PERSON> & Ng, 2004)", "ref_id": "BIBREF0"}, {"start": 506, "end": 529, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF23"}, {"start": 530, "end": 550, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF8"}, {"start": 853, "end": 878, "text": "(<PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF11"}, {"start": 979, "end": 998, "text": "(<PERSON> et al., 2021b)", "ref_id": null}, {"start": 1146, "end": 1165, "text": "(<PERSON> et al., 2021;", "ref_id": null}, {"start": 1166, "end": 1185, "text": "<PERSON> et al., 2021)", "ref_id": null}, {"start": 1198, "end": 1216, "text": "(<PERSON> et al., 2022;", "ref_id": null}, {"start": 1217, "end": 1235, "text": "<PERSON> et al., 2022;", "ref_id": null}, {"start": 1236, "end": 1252, "text": "An et al., 2023;", "ref_id": null}, {"start": 1253, "end": 1274, "text": "Hejna & Sadigh, 2024)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In this paper, we focus on the offline PbRL setting, in which the goal is to find an optimal policy solely from the previously collected preference feedbacks on the pairs of trajectories obtained from some past, fixed policy. This setting is challenging since the preference feedback cannot be actively collected on the trajectories generated by the current, updated policy. Hence, developing effective methods for collecting maximally informative preference feedback data from the past policy as well as devising efficient reward learning schemes is indispensable.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "The current norm is to collect ternary preference feedback (i.e., more/less/equally preferred) for independently sampled pairs of trajectories, and then employ the standard Bradley<PERSON><PERSON> (BT) model (<PERSON> & Terry, 1952) on the collected data to learn the reward function. While the above approach was shown to be effective to some extent, a critical limitation also exists. Namely, due to the independent sampling of the pairs of trajectories and simple ternary feedback, the secondorder preference, which stands for the relative strengths of the preferences, cannot be utilized. There exists a long line of work in several areas asserting that utilizing such second-order preference is indeed effective for more accurate learning (<PERSON><PERSON> et al., 2008; <PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2024) . However, the majority of these works presume the availability of more sophisticated preference feedback types, which are considerably more laborious and expensive to obtain than the above-mentioned ternary feedback.", "cite_spans": [{"start": 198, "end": 221, "text": "(<PERSON> & Terry, 1952)", "ref_id": "BIBREF4"}, {"start": 733, "end": 751, "text": "(<PERSON><PERSON> et al., 2008;", "ref_id": "BIBREF56"}, {"start": 752, "end": 773, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF51"}, {"start": 774, "end": 793, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 794, "end": 812, "text": "<PERSON> et al., 2024)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "To that end, we propose to construct a Ranked List of Trajectories (RLT) while collecting preference feedback data to exploit the second-order preference when learning a reward function. The key novelty and strength of our method is to use exactly the same feedback type and budget as before and not require any additional sophistication in collecting the preference feedback. As outlined in Figure 1 , the main idea of building such a ranked list is to sample a trajectory and sequentially obtain the preference feedback by comparing it with existing trajectories in the ranked list multiple times to find its correct rank in the list. Hence, our method ends up sampling fewer trajectories for a fixed feedback budget compared to the conventional independent pair sampling. However, once the complete RLT is built, the second-order preference can be extracted and exploited for estimating the reward function, which, as we show in our experiments, results in a significant performance boost of offline PbRL.", "cite_spans": [], "ref_spans": [{"start": 399, "end": 400, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "The superiority of our method, dubbed as LiRE (Listwise Reward Estimation), is demonstrated through extensive experimental validation. We first created an offline RL dataset using Meta-World (<PERSON> et al., 2020) and DeepMind Control Suite (DMControl) (<PERSON><PERSON> et al., 2018) environments that can objectively compare the reward estimation quality of offline PbRL methods. This is motivated by (<PERSON> et al., 2023) , which pointed out that the offline RL performance can be high in some popular benchmark datasets even with wrong or constant reward functions. On our proposed datasets, we show that many tasks cannot be properly learned with existing offline PbRL methods, even with a large preference feedback budget. In contrast, we showcase our LiRE can outperform those baselines on most of the tasks with significant margins even with a modest preference feedback budget. We conduct comprehensive experimental analyses to investigate the impact of several factors, including the score function of the BT model, the number of preference feedbacks, and the number of trajectories in the RLT. The experimental results show that the degree to which secondorder information is utilized has a significant positive impact on the performance of offline PbRL. Furthermore, the results of the real human preference feedback experiments, along with experiments on the level of preference feedback noise and feedback granularity, demonstrate the effectiveness of LiRE in practical scenarios. These analyses provide substantial evidence supporting the strength and robustness of LiRE.", "cite_spans": [{"start": 191, "end": 208, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF58"}, {"start": 248, "end": 268, "text": "(<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF50"}, {"start": 387, "end": 404, "text": "(<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Due to the difficulty of defining rewards in reinforcement learning (<PERSON> & <PERSON>, 2018; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) , PbRL uses comparison information between trajectories to learn a reward function (<PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2012; <PERSON> et al., 2012; <PERSON><PERSON><PERSON><PERSON> et al., 2012; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2020) . However, the human preference feedback required for PbRL is expensive to obtain. Thus, several PbRL approaches have been devised to reduce the number of expensive human feedbacks, such as using additional expert demonstrations (<PERSON><PERSON><PERSON> et al., 2018) , meta-learning (Hejna III & Sadigh, 2023) , semi-supervised learning or data augmentation (<PERSON> et al., 2021) , unsupervised pre-training (<PERSON> et al., 2021b) , exploration based on reward uncertainty (<PERSON> et al., 2021) , and using sequen-tial preference ranking (<PERSON><PERSON> et al., 2023) . Offline PbRL assumes a more challenging problem setting where agents cannot interact with the environment, unlike online PbRL where preference feedback can be obtained while interacting with the environment.", "cite_spans": [{"start": 68, "end": 90, "text": "(Sutton & Barto, 2018;", "ref_id": "BIBREF46"}, {"start": 91, "end": 113, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF34"}, {"start": 197, "end": 222, "text": "(<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF11"}, {"start": 223, "end": 246, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2012;", "ref_id": "BIBREF13"}, {"start": 247, "end": 267, "text": "<PERSON> et al., 2012;", "ref_id": "BIBREF54"}, {"start": 268, "end": 288, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2012;", "ref_id": "BIBREF1"}, {"start": 289, "end": 309, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": null}, {"start": 310, "end": 332, "text": "<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF45"}, {"start": 562, "end": 582, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF20"}, {"start": 599, "end": 625, "text": "(<PERSON><PERSON>a III & Sadigh, 2023)", "ref_id": null}, {"start": 674, "end": 693, "text": "(<PERSON> et al., 2021)", "ref_id": null}, {"start": 722, "end": 741, "text": "(<PERSON> et al., 2021b)", "ref_id": null}, {"start": 784, "end": 804, "text": "(<PERSON> et al., 2021)", "ref_id": null}, {"start": 848, "end": 868, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Offline Preference-based RL", "sec_num": "2.1."}, {"text": "In offline PbRL, the two kinds of data are provided: offline data obtained from an unknown policy and preference feedbacks on the pairs of trajectories. Also, traditional offline PbRL methods have two phases; they train a reward model using the preference feedback and then perform RL with the trained reward model without interacting with the environment. On the other hand, recent works propose performing offline PbRL without the reward model by directly optimizing policies (<PERSON> et al., 2023; <PERSON> et al., 2023) , or learning state-action value function or regret from preference labels (<PERSON><PERSON><PERSON> & <PERSON>, 2024; <PERSON><PERSON><PERSON> et al., 2023) . However, due to the constraint of no interaction with the environment, obtaining the most informative preference feedback from the offline dataset is as important as developing a new training method without the reward model or designing the structure of the reward model well (e.g., non-Markovian reward modeling (<PERSON> et al., 2022) ). An active query selection method has been proposed to obtain informative preference pairs (<PERSON> et al., 2022) , but their method did not attempt to obtain second-order preference.", "cite_spans": [{"start": 478, "end": 495, "text": "(<PERSON> et al., 2023;", "ref_id": null}, {"start": 496, "end": 514, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 590, "end": 612, "text": "(Hejna & Sadigh, 2024;", "ref_id": null}, {"start": 613, "end": 632, "text": "<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF17"}, {"start": 948, "end": 966, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 1060, "end": 1079, "text": "(<PERSON> et al., 2022)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Offline Preference-based RL", "sec_num": "2.1."}, {"text": "Most offline PbRL papers have validated their algorithms on the D4RL dataset (<PERSON> et al., 2020) . However, it has been shown that typical offline RL algorithms can produce good policies on D4RL even with a completely wrong reward (e.g., zero, random, negative reward) due to the pessimism and survival instinct of offline RL algorithms (<PERSON> et al., 2022; <PERSON> et al., 2023) . Hence, to properly evaluate how well offline PbRL algorithms learn the reward model, we need to validate them on a new dataset, on which the policy cannot be easily learned due to survival instincts.", "cite_spans": [{"start": 77, "end": 94, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF12"}, {"start": 335, "end": 354, "text": "(<PERSON> et al., 2022;", "ref_id": null}, {"start": 355, "end": 371, "text": "<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Offline Preference-based RL", "sec_num": "2.1."}, {"text": "While typical approaches in PbRL only focus on the firstorder preference (i.e., ternary labels including bad, equal, and good), several approaches in the NLP and RL domains have recently been proposed to utilize second-order preference about the relative difference between preferences. One approach is to directly obtain a relative rating for each trajectory pair (e.g., significantly better or slightly better) or an absolute rating for each trajectory (e.g., very good or good) (<PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2021; <PERSON> et al., 2023) . However, the more granular the preferences are, the more expensive they are than just ternary labels.", "cite_spans": [{"start": 481, "end": 503, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF51"}, {"start": 504, "end": 521, "text": "<PERSON> et al., 2021;", "ref_id": null}, {"start": 522, "end": 541, "text": "<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Second-order Preference Feedback", "sec_num": "2.2."}, {"text": "There is a rich Learning-to-Rank literature that learns the ranking given second-order preference feedback in the form of absolute ratings (<PERSON><PERSON><PERSON> et al., 2005; <PERSON><PERSON> et al., 2008; <PERSON> <PERSON>, 2007; <PERSON><PERSON><PERSON><PERSON> et al., 2021) , but they do not address how to obtain second-order preference only with ternary labels. Another approach is to obtain the second-order preference between samples from a fully-ranked list for multiple trajectories (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2019; <PERSON> et al., 2023; <PERSON> et al., 2024; <PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2019) . However, they do not address how to efficiently obtain the fully-ranked list in terms of the number of feedbacks. Since naively constructing a fullyranked list would require a large number of feedbacks that increase quadratically with the number of trajectories, developing a more efficient list construction method is crucial. Accordingly, some recent studies have developed how to obtain partially-ranked lists that only know the rankings among a few trajectories (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) . Perhaps, one of the closest research to ours is Sequential Preference Ranking (SeqRank) (<PERSON><PERSON> et al., 2023) , which sequentially collects the preference feedback between a newly observed segment and a previously collected segment. However, since their method builds partially-ranked lists rather than fully-ranked lists, the short length of the lists limits the ability to fully utilize second-order information.", "cite_spans": [{"start": 139, "end": 160, "text": "(<PERSON><PERSON><PERSON> et al., 2005;", "ref_id": "BIBREF6"}, {"start": 161, "end": 178, "text": "<PERSON><PERSON> et al., 2008;", "ref_id": "BIBREF56"}, {"start": 179, "end": 193, "text": "Xu & Li, 2007;", "ref_id": "BIBREF57"}, {"start": 194, "end": 214, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": null}, {"start": 430, "end": 449, "text": "(<PERSON> et al., 2022;", "ref_id": null}, {"start": 450, "end": 469, "text": "<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF38"}, {"start": 470, "end": 487, "text": "<PERSON> et al., 2023;", "ref_id": null}, {"start": 488, "end": 506, "text": "<PERSON> et al., 2024;", "ref_id": null}, {"start": 507, "end": 526, "text": "<PERSON> et al., 2022;", "ref_id": null}, {"start": 527, "end": 546, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF3"}, {"start": 547, "end": 566, "text": "<PERSON> et al., 2019)", "ref_id": "BIBREF5"}, {"start": 1035, "end": 1054, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF62"}, {"start": 1055, "end": 1074, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": null}, {"start": 1165, "end": 1185, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Second-order Preference Feedback", "sec_num": "2.2."}, {"text": "An RL algorithm considers a Markov decision process (MDP) and aims to find the optimum policy that maximizes the cumulative discounted rewards. MDP is defined by a tuple (S, A, P, r, γ) where S, A are state, action space, P = P (•|s, a) is the environment transition dynamics, r = r(s, a) is reward function, and γ is discount factor. In offline PbRL, we assume that we do not know the true reward r, but we have a pre-collected dataset that is a set of tuples, D o := {(s, a, s ′ )|(s, a) ∼ µ, s ′ ∼ P (•|s, a)}. In general, the policy µ from which the data was collected is unknown. We are allowed to ask for preference feedbacks to obtain preference labels for two distinct trajectory segments sampled from", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "D s := {σ | σ = (s 0 , a 0 , s 1 , a 1 , • • • , s T -1 , a T -1 ), (s t , a t , s t+1 ) ∈ D o }.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "Annotators assign a ternary label l given a pair of segments σ 1 , σ 2 ∈ D s ; l = 0 indicates that σ 1 is preferred over σ 2 (i.e., σ 1 ≻ σ 2 ), l = 1 indicates the opposite preference (i.e., σ 1 ≺ σ 2 ), and l = 0.5 indicates that σ 1 and σ 2 are equally preferred (i.e., σ 1 = σ 2 ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "The goal of acquiring preference labels is to learn the unknown reward function. Conventional offline PbRL methods use a preference model that defines the probability that one segment is better than the other as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "P θ (σ 1 ≻ σ 2 ) = ϕ r θ (σ 1 ) ϕ r θ (σ 1 ) + ϕ r θ (σ 2 ) (1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "in which r θ (σ i ) = (st,at)∈σi r θ (s t , a t ) and θ is the parameter of the reward model. The score function ϕ(x) = exp(x) is commonly used in the BT model (Bradley & Terry, 1952) . Given the trajectory segment preference dataset,", "cite_spans": [{"start": 160, "end": 183, "text": "(<PERSON> & Terry, 1952)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "D pref := {(σ i1 , σ i2 , l i )} K i=1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": ", the parameter θ is learned by minimizing following cross-entropy loss:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "L(θ) = - E (σi 1 ,σi 2 ,li) ∈D pref (1 -l i ) log P θ (σ i1 ≻ σ i2 ) + l i log P θ (σ i1 ≺ σ i2 ) . (2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "As mentioned in Section 1, the conventional offline PbRL approaches cannot utilize the second-order information of the preference feedback. In order to describe our method, we begin by stating the mild assumptions we make.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LiRE: Listwise Reward Estimation", "sec_num": "4."}, {"text": "Assumption 4.1. (Completeness) For any two segments σ i , σ j , the human feedbacks are provided in the following three ways, σ i ≻ σ j or σ i ≺ σ j or σ i = σ j .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LiRE: Listwise Reward Estimation", "sec_num": "4."}, {"text": "(Transitivity) For any three segments σ i , σ j , and σ k , if", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LiRE: Listwise Reward Estimation", "sec_num": "4."}, {"text": "σ i = σ j and σ j = σ k , then σ i = σ k . Also, if σ i ≻ σ j and σ j ≻ σ k , then σ i ≻ σ k .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LiRE: Listwise Reward Estimation", "sec_num": "4."}, {"text": "Remarks: These assumptions are a generalization of Se-q<PERSON><PERSON><PERSON> (<PERSON><PERSON> et al., 2023) to include equal labels. While the transitivity assumption may not always hold in practice, we demonstrate that our method is robust both in the presence of feedback noise (Section 5.4.3) and in real human experiments (Section 5.5), even when the transitivity assumption may not hold.", "cite_spans": [{"start": 60, "end": 80, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "LiRE: Listwise Reward Estimation", "sec_num": "4."}, {"text": "Our goal is to obtain an RLT in which the segments σ are ordered by their level of preference. We represent RLT, L, in the following form:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "L = [g 1 ≺ g 2 ≺ • • • ≺ g s ],", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "in which", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "g i = {σ i1 , • • • , σ i k } is a group of segments with", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "the same preference level and s is the number of groups in the list. Namely, if m > n, we note any segment σ i ∈ g m is preferred over any segment σ j ∈ g n .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "Since we assume to have exactly the same type of ternary feedback defined in Section 3, we cannot build RLT by obtaining the listwise feedback at once. Hence, we construct by sequentially obtaining the labels as we describe below.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "We start with an initial list [{σ 1 }] by selecting a random segment σ 1 from D s . We then repeat the process of sequentially sampling the new segment σ 2 , σ 3 , • • • ∈ D s and placing it in the appropriate position in the list until the feedback budget limit is reached. To place a newly sampled σ i in the RLT, we compare it with a segment σ k ∈ g m for some group g m in the list and obtain the ternary preference feedback. Depending on the feedback, we proceed as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "Table 1 : Feedback efficiency and sample diversity of independent pairwise sampling, SeqRank, and RLT.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "Independent SeqRank RLT Feedback efficiency 1 1.392 O(M/ log M ) Sample diversity 2 O(1) O(1/ log M ) • If σ i = σ k , add σ i to the group g m . • If σ i ≺ σ k , find the position within g 1 , • • • , g m-1 . • If σ i ≻ σ k , find the position within g m+1 , • • • , g s .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "For the latter two cases, we use a binary search so that we can recursively find the correct group for each segment.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "Namely, the RLT construction algorithm is based on a binary insertion sort and the pseudocode is summarized in Algorithm 1 (Appendix). We note that while we can also adopt merge sort or quick sort to construct an RLT after collecting multiple segments, if we already have a partially constructed RLT, binary insertion sort would be more feedback-efficient.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "Feedback efficiency and sample diversity Note that by design, we need to obtain multiple preference feedbacks for each new segment σ i . Therefore, for a fixed feedback budget, our method samples fewer segments. However, from the constructed RLT, we can generate many preference pairs by exploiting the second-order information encoded in the list; namely, σ i is preferred to all the segments in the groups that rank lower than the group that σ i belongs to.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "To that end, we analyze the feedback efficiency and sample diversity of RLT. Feedback efficiency is defined in <PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON> et al., 2023) as the ratio of the number of total preference pairs generated to the number of preference feedbacks obtained. We also define sample diversity as the ratio of the total number of sampled segments to the number of preference feedbacks obtained. Suppose we obtain preference feedbacks until we collect a total of M segments in the preference dataset. Constructing an RLT with M segments requires O(M log M ) feedbacks because we use an efficient sorting method based on binary search. In this case, the number of all possible preference pairs (including ties) that can be generated from the RLT is M 2 . Table 1 summarizes the feedback efficiency and sample diversity of independent pairwise sampling, SeqRank, and RLT. Note our method has a faster rate of increase in the feedback efficiency even with diminishing sample diversity as the number of segments M in RLT increases.", "cite_spans": [{"start": 119, "end": 139, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [{"start": 748, "end": 749, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Constructing a Ranked List of Trajectories (RLT)", "sec_num": "4.1."}, {"text": "Algorithm 1 places all the segments in a single ranked list. Instead of constructing one long list, we devise a variant that generates multiple lists by setting a limit (Q) on the feedback budget for each list. The reason for generating multiple lists is that as the length of the list increases, the number of preference feedbacks required by the binary search process increases. Hence, we increase the sample diversity within the total feedback budget by generating multiple RLTs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Constructing multiple RLTs", "sec_num": null}, {"text": "Once the RLT is constructed, we construct the preference dataset, D l = {(σ i1 , σ i2 , l i )} K i=1 with all the pairs we can obtain from the RLT. Specifically, when σ i1 ∈ g m and σ i2 ∈ g n , the preference label l i is as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Listwise Reward Estimation from RLT", "sec_num": "4.2."}, {"text": "l i = 0.5 if m = n, l i = 0 if m > n, and l i = 1 if m < n.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Listwise Reward Estimation from RLT", "sec_num": "4.2."}, {"text": "The key difference from traditional pairwise PbRL methods is that, instead of independently sampling segment pairs, we derive preference pairs from the RLT. To compare with the independent sampling, suppose that the RLT has segments with the relationship, σ a < σ b < σ c . If we sample all pairs from the RLT, then (σ a , σ b , 1), (σ b , σ c , 1), (σ a , σ c , 1) ∈ D l . From these preference pairs, it can be inferred that the degree to which σ c is preferred over σ a is stronger than the degree to which σ c is preferred over σ b . Consequently, the reward model trained with pairwise loss in (2) can learn second-order preference between each pair of segments. In contrast, the reward model learned from independent sampling cannot learn second-order preference because each segment is not compared to multiple other segments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Listwise Reward Estimation from RLT", "sec_num": "4.2."}, {"text": "We use pairwise loss in our main experiments, but we can also train the reward model with listwise loss since the segments are ranked in the RLT. To train the reward model with listwise loss, we assume the segments follow a P<PERSON>ckett-<PERSON> model (<PERSON><PERSON>, 1975) which defines the probability distribution of objects in a ranked list. We discuss listwise loss more in detail in Appendix A.3 -but, our experimental results show that training with pairwise loss performs better than listwise loss in most cases.", "cite_spans": [{"start": 244, "end": 260, "text": "(<PERSON><PERSON><PERSON>, 1975)", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "Listwise Reward Estimation from RLT", "sec_num": "4.2."}, {"text": "Our proposed LiRE trains the reward model with linear score function ϕ(x) = x in (1). The choice of linear score function has the same effect as setting the reward to be the exponent of the optimal reward value obtained through training with an exponential score function ϕ(x) = exp(x). Therefore, the linear score function amplifies the difference in reward values, particularly in regions with high reward values, compared to the exponential score function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Listwise Reward Estimation from RLT", "sec_num": "4.2."}, {"text": "Bounding reward model If ϕ(x) = exp(x), then adding a constant to the reward function rθ does not affect the resulting probability distribution. To align the scaling of the learned rθ in ensemble reward models, a common choice for the reward model is using the Tanh activation, i.e., rθ (σ) = t rθ (s t , a t ) = t tanh(f θ (s t , a t )) (<PERSON> et al., 2021b; <PERSON><PERSON> & <PERSON>, 2024) , to bound the output of the reward model.", "cite_spans": [{"start": 338, "end": 357, "text": "(<PERSON> et al., 2021b;", "ref_id": null}, {"start": 358, "end": 379, "text": "Hejna & Sadigh, 2024)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Listwise Reward Estimation from RLT", "sec_num": "4.2."}, {"text": "In the case of ϕ(x) = x, scaling the reward function by a constant does not affect the probability distribution. Similarly, we use the same Tanh activation function for ϕ(x) = x to bound the output of the reward model. Specifically, we set rθ (σ) = t 1 + tanh(f θ (s t , a t )) > 0 to ensure that the probability defined in (1) is positive.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Listwise Reward Estimation from RLT", "sec_num": "4.2."}, {"text": "Dataset Previous offline PbRL papers are evaluated mainly on D4RL (<PERSON> et al., 2020) , but D4RL has the problem that RL performance can be high even when wrong rewards are used (<PERSON> et al., 2023; <PERSON> et al., 2022) . To that end, we newly collect the offline PbRL dataset with Meta-World (<PERSON> et al., 2020) and DeepMind Control Suite (DMControl) (<PERSON><PERSON> et al., 2018) following the protocols of previous work: medium-replay dataset, e.g., (<PERSON> et al., 2021a; <PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020) and medium-expert dataset, e.g., (<PERSON> et al., 2021b; <PERSON><PERSON> et al., 2022; He<PERSON>a & Sadigh, 2024; <PERSON> et al., 2023) .", "cite_spans": [{"start": 66, "end": 83, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF12"}, {"start": 176, "end": 193, "text": "(<PERSON> et al., 2023;", "ref_id": null}, {"start": 194, "end": 212, "text": "<PERSON> et al., 2022)", "ref_id": null}, {"start": 286, "end": 303, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF58"}, {"start": 343, "end": 363, "text": "(<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF50"}, {"start": 435, "end": 453, "text": "(<PERSON> et al., 2021a;", "ref_id": null}, {"start": 454, "end": 475, "text": "<PERSON><PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 476, "end": 498, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF14"}, {"start": 532, "end": 550, "text": "(<PERSON> et al., 2021b;", "ref_id": null}, {"start": 551, "end": 570, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": null}, {"start": 571, "end": 592, "text": "Hejna & Sadigh, 2024;", "ref_id": null}, {"start": 593, "end": 609, "text": "<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Settings", "sec_num": "5.1."}, {"text": "The medium-replay dataset collects data from replay buffers used in online RL algorithms, such as the SAC (<PERSON><PERSON><PERSON><PERSON> et al., 2018) , and the medium-expert dataset collects trajectories generated by the noisy perturbed expert policy. We experiment on both datasets while our main analyses are done on medium-replay; see Appendix C.2 for complete details on constructing them. The prior works (<PERSON> et al., 2022; <PERSON>, 2023) have created datasets that consider survival instinct. However, their dataset was evaluated with only 100 or fewer preference feedbacks, whereas we use 500, 1000, or more feedbacks.", "cite_spans": [{"start": 106, "end": 129, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF15"}, {"start": 390, "end": 409, "text": "(<PERSON> et al., 2022;", "ref_id": null}, {"start": 410, "end": 422, "text": "Zhang, 2023)", "ref_id": "BIBREF61"}], "ref_spans": [], "eq_spans": [], "section": "Settings", "sec_num": "5.1."}, {"text": "Baselines In our experiments, we consider five baselines: Markovian Reward (MR), Preference Transformer (PT) (<PERSON> et al., 2022) , Offline Preference-based Reward Learning (OPRL) (<PERSON> et al., 2022) , Inverse Preference Learning (IPL) (Hejna & Sadigh, 2024) , and Direct Preference-based Policy Optimization (DPPO) (<PERSON> et al., 2023) . MR refers to the method trained with the MLP layer with the Markovian reward assumption, which is the baseline model used in PT. OPRL learns multiple reward models to select the query actively with the highest preference disagreement. Lastly, IPL and DPPO are algorithms that learn policies without the reward model.", "cite_spans": [{"start": 109, "end": 127, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 178, "end": 197, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 234, "end": 256, "text": "(Hejna & Sadigh, 2024)", "ref_id": null}, {"start": 314, "end": 331, "text": "(<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Settings", "sec_num": "5.1."}, {"text": "All the above five baselines belong to pairwise PbRL because they all train based on the BT model given first-order preference feedbacks sampled as independent pairs. In addition to pairwise PbRL, we also compare with the sequential pairwise comparison method proposed by <PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON> et al., 2023) .", "cite_spans": [{"start": 280, "end": 300, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Settings", "sec_num": "5.1."}, {"text": "Implementation details For LiRE, we use the linear score function and set Q = 100 as the default feedback budget for each list. Therefore, if the total number of feedbacks is 500, then five RLTs will be constructed. All baseline methods, including ours, can be applied to any offline RL algorithm, but, as in previous works, we use IQL (<PERSON><PERSON><PERSON><PERSON> et al., 2021) . The hyperparameters for each algorithm and the criteria for the equally preferred label threshold of scripted teacher can be found in the Appendix C.4. ", "cite_spans": [{"start": 336, "end": 360, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Settings", "sec_num": "5.1."}, {"text": "We compare LiRE with the baselines mainly on the Meta-World medium-replay dataset. Table 2 summarizes the results of offline RL performance using ground-truth (GT) rewards and preference feedbacks respectively. For many tasks, such as button-press-topdown and box-close, MR performs poorly compared to training with GT rewards, even with 1000 preference feedbacks. The problem of poor performance remains even if we replace the reward model with a more complex transformer architecture, PT. PT improves performance in dial-turn and lever-pull tasks, but for other tasks, the performance worsens. OPRL generally performs better than MR due to the increased consistency of the reward models, but the performance improvement is small. Lastly, DPPO and IPL perform better than MR on only a few tasks. We note that existing offline PbRL methods are rarely better than MR when validated on our new dataset.", "cite_spans": [], "ref_spans": [{"start": 89, "end": 90, "text": "2", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Evaluation on the Offline PbRL Benchmark", "sec_num": "5.2."}, {"text": "In contrast, LiRE shows a significant performance improvement over MR except for the sweep task. We demonstrate the importance of RLT and the linear score function by achieving high performance even when compared to SeqRank, which is also not an independent pairwise method. In addition, policies trained with preference data outperform policies trained with GT rewards on the buttonpress-topdown-wall task, suggesting that reward models trained with preference data may be more effective, as also reported in prior works (<PERSON><PERSON> et al., 2017; <PERSON> et al., 2022; <PERSON> et al., 2023) . The results of the Meta-World medium-expert dataset and full learning curves are shown in the Appendix A.", "cite_spans": [{"start": 522, "end": 547, "text": "(<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF11"}, {"start": 548, "end": 565, "text": "<PERSON> et al., 2022;", "ref_id": null}, {"start": 566, "end": 582, "text": "<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Evaluation on the Offline PbRL Benchmark", "sec_num": "5.2."}, {"text": "We conduct an ablation study to verify if the performance improvement of LiRE is due to two factors: the linear score function and the RLT construction. Additionally, the bottom two rows of Table 3 show that constructing RLT improves performance by constructing the preference list and exploiting the second-order information.", "cite_spans": [], "ref_spans": [{"start": 196, "end": 197, "text": "3", "ref_id": "TABREF1"}], "eq_spans": [], "section": "FACTORS OF PERFORMANCE IMPROVEMENT", "sec_num": "5.3.1."}, {"text": "In particular, using the linear score function with RLT has a synergistic effect, resulting in even greater improvement.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FACTORS OF PERFORMANCE IMPROVEMENT", "sec_num": "5.3.1."}, {"text": "We examine the estimated reward values of the learned reward models. From the figure, we clearly observe that the estimated rewards in Figure 2 (b) are more highly correlated than those in Figure 2(a) . Namely, by constructing the RLT, LiRE exploits the second-order preference, and the high and low reward segments are more clearly distinguished by the reward estimates than vanilla MR. Additionally, when training the reward model with the linear score function, there is a larger gap in the estimated rewards within the reward region for higher GT rewards, as shown in Figure 2(d) . We speculate that using the linear score function and RLT makes the estimated reward discern the optimal and suboptimal segments (with respect to the GT rewards) more clearly, hence, the policy learned with the estimated reward turns out to perform much better.", "cite_spans": [], "ref_spans": [{"start": 142, "end": 143, "text": "2", "ref_id": "FIGREF1"}, {"start": 196, "end": 200, "text": "2(a)", "ref_id": "FIGREF1"}, {"start": 579, "end": 583, "text": "2(d)", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "EFFECT OF RLT AND SCORE FUNCTION ON R<PERSON>WARD ESTIMATION", "sec_num": "5.3.2."}, {"text": "We evaluate how the performances of the offline PbRL algorithms are affected by the number of feedbacks. Namely, we measure the average success rate of the sweep-into, box-close, and button-press-topdown-wall tasks of the medium-replay dataset while varying the number of the preference feedbacks from 50 to 2000. We note that most previous works (<PERSON> et al., 2022; <PERSON><PERSON><PERSON> & <PERSON>, 2024; <PERSON> et al., 2023) using D4RL only use up to 500 preference feedbacks. As shown in Figure 3 , we observe that the typical baseline, MR with exponential score function (denoted as MR w/ exp), cannot achieve a success rate higher than 50% for all three tasks even with 2000 preference feedbacks.", "cite_spans": [{"start": 347, "end": 365, "text": "(<PERSON> et al., 2022;", "ref_id": null}, {"start": 366, "end": 387, "text": "Hejna & Sadigh, 2024;", "ref_id": null}, {"start": 388, "end": 404, "text": "<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [{"start": 476, "end": 477, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "VARYING THE NUMBER OF FEEDBACKS", "sec_num": "5.4.1."}, {"text": "When we instead use the linear score function, we observe that MR w/ linear performs much better than MR w/ exp, but the success rates sometimes still remain to be low (e.g., box-close with 500 feedbacks and button-press-topdownwall for most of the times). In contrast, it is evident that LiRE mostly surpasses the two baselines with large margins, even with fewer number of preference feedbacks. Specifically, for the button-press-topdown-wall task, LiRE with only 100 feedbacks outperforms not only the baselines with 2000 feedbacks but also the policy learned using the GT reward. Again, we can confirm that the high feedback efficiency enabled by RLT makes LiRE very effective even with a smaller number of feedbacks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "VARYING THE NUMBER OF FEEDBACKS", "sec_num": "5.4.1."}, {"text": "In Section 4.1, we described that multiple RLTs can be constructed by putting the budget limit (Q) in order to increase the sample diversity. In this subsection, we show the effect of Q. Table 4 shows the performance change of LiRE when varying the Q budget to 1, 2, 10, 20, 50, 100, and 500 while setting the total number of preference feedbacks to 500. Hence, for example, Q = 100 results in five lists, and Q = 500 results in a single list. Table 4 also shows the result of SeqRank (with linear score function). From the table, we observe that since the utilization of the secondorder information increases with higher values of Q, the offline PbRL performance correspondingly improves, as expected. We also note that the performance of SeqRank is similar to that of LiRE with Q = 2 since SeqRank creates approximately 2.3 groups in the ranked list, as detailed in Table 5 . This result indicates that SeqRank does not fully utilize second-order preference due to only building partially-ranked lists. A more in-depth comparison with SeqRank is given in Section 5.4.5.", "cite_spans": [], "ref_spans": [{"start": 193, "end": 194, "text": "4", "ref_id": "TABREF3"}, {"start": 450, "end": 451, "text": "4", "ref_id": "TABREF3"}, {"start": 874, "end": 875, "text": "5", "ref_id": "TABREF4"}], "eq_spans": [], "section": "VARYING Q BUDGET", "sec_num": "5.4.2."}, {"text": "If the preference feedback used in PbRL models human preference labeling, it would be reasonable to assume that the preference feedback may be noisy. To that end, we experiment to assess the robustness of the offline PbRL performance of LiRE with respect to the preference feedback noise. We assume that the preference feedback can be noisy with probability p (i.e., if l i = 0 or 1, the label is flipped to 1 -l i with probability p, and for tie labels, we flip to l i = 0 or 1 with probability p/2, respectively). We varied the noise probability p from 0 to 0.3, and Figure 4 compares the success rates of MR w/ linear and LiRE. From the figure, we confirm that the performance of LiRE does not drop as severely as MR w/ linear when p increases. In particular, for lever-pull task, we observe that LiRE with feedback noise of p = 0.3 even results in a higher success rate than MR w/ linear with no noise, highlighting the robustness of LiRE with respect to feedback noise.", "cite_spans": [], "ref_spans": [{"start": 576, "end": 577, "text": "4", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "ROBUSTNESS TO FEEDBACK NOISE", "sec_num": "5.4.3."}, {"text": "In Figure 5 , we compare the performance of LiRE based on the threshold that determines the tie between the segments. Specifically, we adjust the threshold value for the reward difference that indicates whether two segments are equally preferred. Namely, a higher threshold value means that more segment pairs are labeled as equally preferred, result- ing in less granular preference feedback. We note that the threshold value used in Table 2 is 12.5 (see Appendix C.4 for details). Figure 5 shows that using a smaller threshold (i.e., more granular feedback) improves the performance of LiRE, while the performance becomes similar to that of MR w/ linear (e.g., button-press-topdown task with threshold 25) when the threshold increases. Thus, we confirm that the more granular preference labels generate additional secondorder preference information, which would positively affect the performance of LiRE.", "cite_spans": [], "ref_spans": [{"start": 10, "end": 11, "text": "5", "ref_id": "FIGREF5"}, {"start": 441, "end": 442, "text": "2", "ref_id": "TABREF0"}, {"start": 490, "end": 491, "text": "5", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "IMPACT OF FEEDBACK GRANULARITY", "sec_num": "5.4.4."}, {"text": "Here, we compare LiRE with SeqRank, which also utilizes partially-ranked lists. We also employed the linear score function for SeqRank since it gave better results than using the exponential function and led to a fair comparison with LiRE. We evaluate the average success rates of SeqRank and LiRE on the Meta-World medium-replay dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "COMPARISON WITH SEQRANK", "sec_num": "5.4.5."}, {"text": "The experimental results in Table 5 show that LiRE clearly achieves higher performance than SeqRank. We argue that SeqRank does not fully utilize the second-order information because SeqRank does not construct a fully-ranked list. Indeed, the third column of Table 5 shows that the number of groups in the ranked lists averages less than 3 with the SeqRank, whereas it increases to about 9 on average with LiRE. The last two columns of Table 5 compare feedback efficiency and sample diversity. LiRE achieves a sample diversity of approximately 0.47 through the use of binary search, and the feedback efficiency increases significantly to 11.33 by constructing RLT. Additionally, Table 6 shows the superiority of LiRE over SeqRank on the DM-Control medium-replay dataset. We note SeqRank also performs similarly to MR w/ linear on walker-walk and humanoid-walk tasks, while LiRE achieves much higher performance gains on all three tasks. Thus, we confirm that constructing RLT and leveraging second-order preference is effective for locomotion as well as manipulation tasks.", "cite_spans": [], "ref_spans": [{"start": 34, "end": 35, "text": "5", "ref_id": "TABREF4"}, {"start": 265, "end": 266, "text": "5", "ref_id": "TABREF4"}, {"start": 442, "end": 443, "text": "5", "ref_id": "TABREF4"}, {"start": 685, "end": 686, "text": "6", "ref_id": "TABREF5"}], "eq_spans": [], "section": "COMPARISON WITH SEQRANK", "sec_num": "5.4.5."}, {"text": "To check the compatibility of LiRE with other methods, we tested the performance of LiRE when combined with OPRL and PT, respectively. First, to apply OPRL and LiRE simultaneously, we trained a reward model each time an RLT was newly constructed, and then actively sampled based on the disagreement of the reward models (following the method of OPRL) when constructing the next RLT. In Figure 6 (a), we observe that LiRE+OPRL outperforms LiRE in sweep-into and lever-pull tasks but performs worse in button-press-topdown task. This discrepancy suggests that while the OPRL method enhances the consistency of the reward model, it may lead to oversampling similar segments that are challenging to distinguish depending on the task. Second, as shown in Figure 6 (b), LiRE does not necessarily gain improvements when combined with PT. That is, since PT was originally designed to capture temporal dependencies of segments in reward modeling, it seems to struggle in accurately capturing the second-order preference information from RLT possibly due to overfitting to the sequence of past segments. effective in practical scenarios with real human preference feedback, as in LLM alignment.", "cite_spans": [], "ref_spans": [{"start": 393, "end": 394, "text": "6", "ref_id": "FIGREF6"}, {"start": 757, "end": 758, "text": "6", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "COMPATIBILITY WITH OTHER METHODS", "sec_num": "5.4.6."}, {"text": "We believe there are two limitations of LiRE. First, LiRE lacks the ability to parallelize the construction of RLT since there are dependencies between the order in which feedbacks are obtained to construct a fully-ranked list. Therefore, in scenarios where parallel feedback collection is feasible, constructing an RLT could be more time-consuming compared to collecting preference feedbacks independently in pairs. Nevertheless, the results presented in Appendix A.2 show that LiRE with only 200 feedbacks outperforms the independent pairwise sampling method using 1000 feedbacks, suggesting the importance of constructing RLT. Second, LiRE relies on the transitivity assumption outlined in Assumption 4.1. Although our experiments with feedback noise indicate LiRE's robustness to noise that violates this assumption, transitivity violations can occur even with noiseless labels in real-world applications. This issue is not unique to LiRE but affects other preference-based RL methods as well. Addressing transitivity violation remains a challenge for scalar reward models, so future research could explore solutions by using multi-dimensional preference feedback to construct RLTs for each dimension.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitation", "sec_num": "6."}, {"text": "In this paper, we propose a novel Listwise Reward Estimation (LiRE) method for offline preference-based RL. While obtaining second-order preference from a traditional framework is challenging, we demonstrate that LiRE efficiently exploits second-order preference by constructing an RLT using ordinary, simple ternary feedback. Our experiments demonstrate the significant performance gains achieved by LiRE on our new offline PbRL dataset, specifically designed to objectively reflect the effect of estimated rewards. Notably, the reward model trained with LiRE outperforms traditional pairwise feedback methods, even with fewer preference feedbacks, highlighting the importance of second-order preference information. Moreover, our findings suggest that constructing ranked lists can be straightforward without complex second-order preference feedback, indicating the broad applicability of LiRE to more challenging tasks and real-world applications.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "7."}, {"text": "We believe our LiRE can be potentially applied to aligning the RL agent with more fine-grained human intent and preference. Such applications can bring significant societal consequences by enhancing the precision and effectiveness of AI systems in various fields such as health care and education. By ensuring that AI systems more closely reflect and respond to the detailed intentions of their users, LiRE has the potential to foster trust and acceptance of AI technologies, ultimately contributing to their more widespread and ethical adoption.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Our goal is to minimize the following objective:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "D KL (P s (π)∥P θ (π)) = D KL n i=1 ϕ(s(σ πi )) n j=i ϕ(s(σ πj )) n i=1 ϕ r θ (σ πi ) n j=i ϕ r θ (σ πj ) .", "eq_num": "(4)"}], "section": "Impact Statement", "sec_num": null}, {"text": "Since the number of permutations grows by n!, computing the permutation probability demands a high computational cost. Thus, we minimize the following objective using the top one probability proposed by ListNet (<PERSON><PERSON> et al., 2008) :", "cite_spans": [{"start": 211, "end": 229, "text": "(<PERSON><PERSON> et al., 2008)", "ref_id": "BIBREF56"}], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n i=1 D KL (P s (i)∥P θ (i))", "eq_num": "(5)"}], "section": "Impact Statement", "sec_num": null}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "P s (i) = P s (π 1 = i) = ϕ(s(σ i )) n j=1 ϕ(s(σ j ))", "eq_num": "(6)"}], "section": "Impact Statement", "sec_num": null}, {"text": "and", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "P θ (i) = P θ (π 1 = i) = ϕ(r θ (σ i )) n j=1 ϕ(r θ (σ j )) .", "eq_num": "(7)"}], "section": "Impact Statement", "sec_num": null}, {"text": "We train the reward model by sampling n = 10 segments from the RLT at each gradient descent. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "As described in Appendix C.5, the epochs experimented with in Table 2 is 300. Table 11 shows the performance when we increase the epochs to 5000. Both MR and LiRE tend to perform better with more epochs, but LiRE still performs better than MR. The performance gap between using the exponential score function and the linear score function for LiRE is smaller at 5000 epochs than at 300 epochs. However, when the epoch is 5000, the linear score function has a significant performance improvement on the dial-turn and button-press-topdown-wall tasks and performs better or similar to the exponential score function on other tasks. ", "cite_spans": [], "ref_spans": [{"start": 68, "end": 69, "text": "2", "ref_id": "TABREF0"}, {"start": 84, "end": 86, "text": "11", "ref_id": "TABREF9"}], "eq_spans": [], "section": "A.4. Increasing Epochs of Reward Model Training", "sec_num": null}, {"text": "Following offline RL data collection approach, we collect offline RL data from different policies in two ways: medium-replay dataset and medium-expert dataset. medium-replay dataset We use the replay buffer collected while training online RL as an offline RL dataset. We train with 3 seeds using the online SAC (<PERSON><PERSON><PERSON><PERSON> et al., 2018) implemented in PEBBLE (<PERSON> et al., 2021b) with ground-truth implementation code1 . We use IQL because it is the default offline RL algorithm in previous offline PbRL papers, and IQL is also one of the strongest offline algorithms according to CORL. We use the same hyperparameters that were used to train Gym-MuJoCO in CORL. For PT, we follow their implementation2 for the training reward model and use the CORL library for training offline RL. We follow the official implementations of DPPO3 and IPL4 with the hyperparameters they use in the Gym-MuJoCo and Metaworld dataset. The hyperparameters for each baseline, including IQL, are listed in Table 18 . The total number of gradient descent steps in the offline RL is 250,000 and we evaluate the success rate for 50 episodes every 5000 steps. We run six seeds for all baselines and our method. We then report the average success rate of the last 5 trained policies. We use a single NVIDIA RTX A5000 GPU and 32 CPU cores (AMD EPYC 7513 @ 2.60GHz) in our experiments.", "cite_spans": [{"start": 311, "end": 334, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF15"}, {"start": 357, "end": 376, "text": "(<PERSON> et al., 2021b)", "ref_id": null}], "ref_spans": [{"start": 986, "end": 988, "text": "18", "ref_id": null}], "eq_spans": [], "section": "C.2. Creating Offline PbRL Dataset", "sec_num": null}, {"text": "https://github.com/tinkoff-ai/CORL", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://github.com/csmile-1006/PreferenceTransformer", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://github.com/snu-mllab/DPPO", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://github.com/jhejna/inverse-preference-learning", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported in part by the National Research Foundation of Korea (NRF) grant [No.2021R1A2C2007884] and by Institute of Information & communications Technology Planning & Evaluation (IITP) grants [RS-2021-II211343, RS-2021-II212068, RS-2022-II220113, RS-2022-II220959] funded by the Korean government (MSIT). It was also supported by AOARD Grant No. FA2386-23-1-4079.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgments", "sec_num": null}, {"text": "We summarize the experimental results on the Meta-World medium-expert dataset in Table 8 . LiRE outperforms significantly baselines for sweep and hammer tasks. While DPPO performs better than LiRE in the case of box-close task, DPPO performs poorly compared to basic MR in other tasks. (<PERSON> et al., 2022) 2.33 ± 3.54 57.33 ± 8.92 1.67 ± 2.92 OPRL (<PERSON> et al., 2022) 10.00 ± 9.87 30.00 ± 8.87 6.00 ± 4.62 DPPO (<PERSON> et al., 2023) 41.00 ± 9.50 12.33 ± 8.44 8.00 ± 10.07 IPL (Hejna & Sadigh, 2024) 7.00 ± 9.50 15.33 ± 10.37 4.33 ± 4.23 SeqRank (<PERSON><PERSON> et al., 2023) 10 (<PERSON> et al., 2022) 13.00 ± 12.26 21.00 ± 15.44 1.33 ± 2.98 OPRL (<PERSON> et al., 2022) 11.33 ± 6.80 44.33 ± 6.67 6.33 ± 5.47 DPPO (<PERSON> et al., 2023) 42.67 ± 15.52 14.33 ± 13.19 5.33 ± 4.85 IPL (<PERSON><PERSON><PERSON> & <PERSON>, 2024) 10.67 ± 6.90 16.67 ± 11.64 9.33 ± 9.14 <PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON> et al., 2023) 13. ", "cite_spans": [{"start": 286, "end": 304, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 347, "end": 366, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 410, "end": 427, "text": "(<PERSON> et al., 2023)", "ref_id": null}, {"start": 471, "end": 493, "text": "(Hejna & Sadigh, 2024)", "ref_id": null}, {"start": 540, "end": 560, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}, {"start": 564, "end": 582, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 628, "end": 647, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 691, "end": 708, "text": "(<PERSON> et al., 2023)", "ref_id": null}, {"start": 753, "end": 775, "text": "(Hejna & Sadigh, 2024)", "ref_id": null}, {"start": 823, "end": 843, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [{"start": 87, "end": 88, "text": "8", "ref_id": null}], "eq_spans": [], "section": "A.1. Experimental Results on medium-expert Dataset", "sec_num": null}, {"text": "If we have pre-collected independent pairwise preference data, MR can use the entire preference data. However, LiRE has the disadvantage of requiring additional feedbacks between segments for constructing RLT. Nevertheless, Table 9 shows the importance of RLT to obtain second-order preference. The performance of LiRE with 200 feedbacks is better than the performance using 1000 independent pairwise feedbacks. Section 4.2 describes how to train the reward model with pairwise loss from constructed RLT. However, we can apply listwise loss in addition to pairwise loss since a ranked list is constructed. In this section, we introduce how to train the reward model with listwise loss. Suppose that we have n segments, (σ 1 , σ 2 , • • • , σ n ) and denote the rewards of the segments, r(σ 1 ), r(σ 2 ), • • • , r(σ n ) . We assume the probability of permutation of n segments follows a Plackett-Luce (PL) model (<PERSON><PERSON>, 1975) :where ϕ is an increasing and strictly positive function and πHere, P (π) is the probability distribution in which n segments are ranked in order of permutation π, indicating the likelihood of segment σ i being ranked π i -th.Since we do not know the true probability of permutation, we set the score of the segment based on the ranks. Specifically, given k ranks in the list, let s(σ) = (k + 1 -m)R/k be the score of the segment σ that belongs to the m-th preferred rank (i.e., σ ∈ g k+1-m ) where m ∈ {1, • • • , k} and R is constant. In our implementation, the constant R is set to the maximum boundary of the output of the reward model, which is bounded by [0, R] by the Tanh function.the survey paper of PbRL (Wirth et al., 2017) . Additionally, (Song et al., 2024) have demonstrated that alternative score functions are effective in RLHF. We also present the performance of baselines using the linear score function instead of the exponential function across four Meta-World medium-replay tasks in Table 12 . Table 12 reveals that LiRE, when utilizing the linear score function, surpasses all other baselines, even when these baselines also use the linear score function. For PT or DPPO, there is no performance improvement when using a linear score function. We leave it as future work to analyze which score functions are effective depending on the model structure or training method. A.6. Online PbRLFigure 7 depicts the experimental results of online PbRL. We compare the online PbRL performance by using a linear score function and an exponential score function. The increase in performance when using the linear score function suggests that the BT model using the exponential score function may not be the optimum choice for PbRL. We used the code implemented in PEBBLE (Lee et al., 2021b) . ", "cite_spans": [{"start": 912, "end": 928, "text": "(<PERSON><PERSON><PERSON>, 1975)", "ref_id": "BIBREF40"}, {"start": 1643, "end": 1663, "text": "(<PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF55"}, {"start": 1680, "end": 1699, "text": "(<PERSON> et al., 2024)", "ref_id": null}, {"start": 2711, "end": 2730, "text": "(<PERSON> et al., 2021b)", "ref_id": null}], "ref_spans": [{"start": 230, "end": 231, "text": "9", "ref_id": null}, {"start": 1939, "end": 1941, "text": "12", "ref_id": null}, {"start": 1950, "end": 1952, "text": "12", "ref_id": null}, {"start": 2345, "end": 2346, "text": "7", "ref_id": null}], "eq_spans": [], "section": "A.2. Li<PERSON> with Fewer Preference Feedbacks", "sec_num": null}, {"text": "Full learning curves for the Meta-World medium-replay dataset are shown in Figure 8 and for the Meta-World medium-expert dataset are shown in Figure 9 . We plot the results for MR, PT (<PERSON> et al., 2022) , OPRL (<PERSON> et al., 2022) , DPPO (An et al., 2023) , IPL (Hejna & Sadigh, 2024) , and LiRE. The average success rates reported in Table 2 are obtained with the last 5 trained policies. Although the performance of LiRE for the medium-replay sweep task is relatively low, the full learning curve shows that the performance of the best-trained policy is competitive.", "cite_spans": [{"start": 184, "end": 202, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 210, "end": 229, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 237, "end": 254, "text": "(<PERSON> et al., 2023)", "ref_id": null}, {"start": 261, "end": 283, "text": "(Hejna & Sadigh, 2024)", "ref_id": null}], "ref_spans": [{"start": 82, "end": 83, "text": "8", "ref_id": null}, {"start": 149, "end": 150, "text": "9", "ref_id": null}, {"start": 340, "end": 341, "text": "2", "ref_id": null}], "eq_spans": [], "section": "B.1. Full Learning Curves of Each Method", "sec_num": null}, {"text": "We evaluate the success rate by the following: (1) with MR or with LiRE and (2) exponential or linear score function. Table 13 shows that both constructing RLT and using linear function improve offline PbRL performance.", "cite_spans": [], "ref_spans": [{"start": 124, "end": 126, "text": "13", "ref_id": null}], "eq_spans": [], "section": "B.2. Ablation Study of LiRE", "sec_num": null}, {"text": "Similar to Figure 2 , in button-press-topdown task, Figure 10 shows that constructing RLT and using a linear score function can better distinguish the rewards between segments with relatively high preference. ", "cite_spans": [], "ref_spans": [{"start": 18, "end": 19, "text": "2", "ref_id": null}, {"start": 59, "end": 61, "text": "10", "ref_id": null}], "eq_spans": [], "section": "B.3. Effect of RLT and Score Function on Reward Estimation", "sec_num": null}, {"text": "Table 14 shows the success rate of each task in Table 5 . <PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON> et al., 2023) improves feedback efficiency but constructs a shorter length of the ranked list, so LiRE is better at utilizing second-order preference.", "cite_spans": [{"start": 66, "end": 86, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [{"start": 6, "end": 8, "text": "14", "ref_id": null}, {"start": 54, "end": 55, "text": "5", "ref_id": null}], "eq_spans": [], "section": "B.4. <PERSON><PERSON><PERSON>on with SeqRank", "sec_num": null}, {"text": "To construct RLT, We can use any sorting method such as binary insertion sort, mergesort, or quicksort. However, if the RLT is already partially constructed, a binary insertion sort is an efficient way to find the rank of each segment. The pseudocode for the binary insertion sort we use to construct the RLT is summarized in Algorithm 1. rewards. We stop collecting replay buffers when the average success rate of the online RL's performance is near 50. (For the DMControl dataset, collect until the episode returns are about in the middle of the convergence value.) We measure the online RL performance every 50,000 steps, so depending on the training speed of the online RL, the average success rate of the online RL may be less or more than 50 at the end of the replay buffer collection. Table 15 shows the average success rate when the collection of the replay buffers ends. medium-expert dataset We collect medium-expert dataset following approaches by prior works (<PERSON><PERSON>a & <PERSON>igh, 2024; Zhang, 2023) : collect 50 trajectories from the expert policy provided by Meta-World (<PERSON> et al., 2020) , collect 50 trajectories from the expert policy for a different randomized object and goals positions, collect 100 trajectories from the expert policy for a different task out of 50 Meta-World tasks, collect 200 trajectories from a random policy, and finally, collect 200 trajectories from the ϵ-greedy policy that samples an action from the expert policy with 50% probability and from the random policy with the remaining 50% probability. We also add Gaussian noise with a mean of 0 and a standard deviation of 1 for each policy.", "cite_spans": [{"start": 971, "end": 993, "text": "(Hejna & Sadigh, 2024;", "ref_id": null}, {"start": 994, "end": 1006, "text": "Zhang, 2023)", "ref_id": "BIBREF61"}, {"start": 1079, "end": 1096, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF58"}], "ref_spans": [{"start": 798, "end": 800, "text": "15", "ref_id": null}], "eq_spans": [], "section": "C.1. RLT Construction", "sec_num": null}, {"text": "For each dataset, we verify that there is a difference in RL performance when trained with GT reward versus wrong rewards because if offline RL achieves high performance with wrong rewards, the dataset is not appropriate for offline PbRL. We use the three wrong rewards chosen by (<PERSON> et al., 2023) : zero rewards, where all rewards r(s, a) = 0; random rewards, where all reward values are sampled from a uniform distribution U (0, 1); and negative rewards, set to -r(s, a). The performance of offline RL with GT reward and wrong rewards on each dataset is shown in Table 16 and Table 17 .", "cite_spans": [{"start": 280, "end": 297, "text": "(<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [{"start": 571, "end": 573, "text": "16", "ref_id": null}, {"start": 578, "end": 586, "text": "Table 17", "ref_id": null}], "eq_spans": [], "section": "C.3. RL Performance between GT Reward and Wrong Rewards", "sec_num": null}, {"text": "We set the length of segment σ used in the preference label to 25, denoted as). We use the GT reward to label the preference between segment pairs. Considering that GT reward in Meta-World ranges from 0 to 10, segments with GT reward differences less than 12.5 are labeled as equally preferred segments. This threshold is equivalent to the threshold provided by B-pref (<PERSON> et al., 2021a) , which is used as an online PbRL benchmark, when the policy has an average return of 5 (that is, medium performance).", "cite_spans": [{"start": 369, "end": 388, "text": "(<PERSON> et al., 2021a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "C.4. Preference Label", "sec_num": null}, {"text": "Reward model The reward model used in our method and the standard pairwise PbRL and MR reward model use the same reward model structure. We ensemble three reward models and finally predicted the reward in the offline RL dataset by averaging the estimated reward values from the three reward models. The details of the hyperparameters are shown in In our experiments, MR, PT, and OPRL are two-step PbRL methods that first train the reward model and learn the offline RL with the trained reward model. We use the trained reward model to estimate the reward for every (s, a) in the offline RL dataset and apply min-max normalization to the reward values in the dataset so that the minimum and maximum values are 0 and 1. We also apply min-max normalization to the experiments with GT rewards and wrong rewards for a fair comparison.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.5. Hyperparameters", "sec_num": null}, {"text": "We choose IQL for the default offline RL algorithm and CORL (<PERSON><PERSON><PERSON> et al., 2023) for the ", "cite_spans": [{"start": 60, "end": 82, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "Implementation details", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Apprenticeship Learning via Inverse Reinforcement Learning", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}], "year": 2004, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> <PERSON>. Apprenticeship Learning via Inverse Reinforcement Learning. In International Con- ference on Machine Learning (ICML), 2004.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "APRIL: Active Preference-learning based Reinforcement Learning", "authors": [{"first": "R", "middle": [], "last": "Akrour", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Sebag", "suffix": ""}], "year": 2012, "venue": "European Conference on Machine Learning and Principles and Practice of Knowledge Discovery in Databases (ECML PKDD)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, M. APRIL: Active Preference-learning based Reinforcement Learning. In European Conference on Machine Learning and Princi- ples and Practice of Knowledge Discovery in Databases (ECML PKDD), 2012.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Direct Preference-based Policy Optimization without Reward Modeling", "authors": [{"first": "G", "middle": [], "last": "An", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K.<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": ["O"], "last": "Song", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, H. O. Direct Preference-based Policy Optimiza- tion without Reward Modeling. In Advances in Neural Information Processing Systems (NeurIPS), 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "The Green Choice: Learning and Influencing Human Decisions on Shared Roads", "authors": [{"first": "E", "middle": [], "last": "Bıyık", "suffix": ""}, {"first": "D", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "IEEE Conference on Decision and Control (CDC)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, R. The Green Choice: Learning and Influencing Human Deci- sions on Shared Roads. In IEEE Conference on Decision and Control (CDC), 2019.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Rank Analysis of Incomplete Block Designs: I. The Method of Paired Comparisons", "authors": [{"first": "R", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["E"], "last": "<PERSON>", "suffix": ""}], "year": 1952, "venue": "Biometrika", "volume": "39", "issue": "3/4", "pages": "324--345", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> <PERSON><PERSON> Rank Analysis of Incomplete Block Designs: I. The Method of Paired Comparisons. Biometrika, 39(3/4):324-345, 1952.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Extrapolating Beyond Suboptimal Demonstrations via Inverse Reinforcement Learning from Observations", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Goo", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, S. <PERSON>p- olating Beyond Suboptimal Demonstrations via Inverse Reinforcement Learning from Observations. In Interna- tional Conference on Machine Learning (ICML), 2019.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Learning to Rank using Gradient Descent", "authors": [{"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Shaked", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Deeds", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2005, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N., and <PERSON>, G. Learning to Rank using Gradient Descent. In International Conference on Machine Learning (ICML), 2005.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Weak Human Preference Supervision For Deep Reinforcement Learning", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C.-T", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "IEEE Transactions on Neural Networks and Systems (TNNLS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ak Human Preference Supervision For Deep Reinforcement Learning. In IEEE Transactions on Neural Networks and Systems (TNNLS), 2021.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Open Problems and Fundamental Limitations of Reinforcement Learning from Human Feedback", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Shi", "suffix": ""}, {"first": "T", "middle": ["K"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Scheurer", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>dman", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.15217"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, et al. Open Problems and Fundamental Limitations of Reinforcement Learning from Human Feedback. In arXiv preprint arXiv:2307.15217, 2023.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Closing the Sim-to-Real Loop: Adapting Simulation Randomization with Real World Experience", "authors": [{"first": "Y", "middle": [], "last": "Chebotar", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Issac", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Fox", "suffix": ""}], "year": 2019, "venue": "International Conference on Robotics and Automation (ICRA)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, D. Closing the Sim-to- Real Loop: Adapting Simulation Randomization with Real World Experience. In International Conference on Robotics and Automation (ICRA), 2019.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Human-in-the-loop: Provably Efficient Preference-based Reinforcement Learning with General Function Approximation", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Human-in-the-loop: Provably Efficient Preference-based Reinforcement Learning with General Function Approxi- mation. In International Conference on Machine Learn- ing (ICML), 2022.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Deep Reinforcement Learning from Human Preferences", "authors": [{"first": "P", "middle": ["F"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, D. Deep Reinforcement Learning from Human Preferences. In Advances in Neural Information Processing Systems (NeurIPS), 2017.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "D4RL: Datasets for Deep Data-Driven Reinforcement Learning", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2004.07219"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. D4RL: Datasets for Deep Data-Driven Reinforcement Learning. In arXiv preprint arXiv:2004.07219, 2020.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Preference-based Reinforcement Learning: A Formal Framework and a Policy Iteration Algorithm", "authors": [{"first": "J", "middle": [], "last": "Fürnkranz", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S.-H", "middle": [], "last": "Park", "suffix": ""}], "year": 2012, "venue": "Machine Learning", "volume": "89", "issue": "", "pages": "123--156", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S.- H. Preference-based Reinforcement Learning: A Formal Framework and a Policy Iteration Algorithm. In Machine Learning, volume 89, pp. 123-156. Springer, 2012.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "RL Unplugged: A Suite of Benchmarks for Offline Reinforcement Learning", "authors": [{"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Zolna", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["S"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, J<PERSON>, <PERSON>, D<PERSON>, <PERSON><PERSON>, <PERSON>, et al. RL Unplugged: A Suite of Bench- marks for Offline Reinforcement Learning. In Advances in Neural Information Processing Systems (NeurIPS), 2020.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Soft Actor-Critic: Off-policy Maximum Entropy Deep Reinforcement Learning with a Stochastic Actor", "authors": [{"first": "T", "middle": [], "last": "Haarnoja", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>. Soft Actor-Critic: Off-policy Maximum Entropy Deep Rein- forcement Learning with a Stochastic Actor. In Interna- tional Conference on Machine Learning (ICML), 2018.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Inverse Preference Learning: Preference-based RL without a Reward Function", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> Inverse Preference Learning: Preference-based RL without a Reward Function. In Advances in Neural Information Processing Systems (NeurIPS), 2024.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Contrastive Preference Learning: Learning from Human Feedback without RL", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Sikchi", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.13639"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, D. Contrastive Preference Learning: Learning from Human Feedback without RL. In arXiv preprint arXiv:2310.13639, 2023.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Few-Shot Preference Learning for Human-in-the-Loop RL", "authors": [{"first": "Iii", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "", "suffix": ""}], "year": null, "venue": "Conference on Robot Learning (CoRL)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON>-Shot Preference Learn- ing for Human-in-the-Loop RL. In Conference on Robot Learning (CoRL), 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Sequential Preference Ranking for Efficient Reinforcement Learning from Human Feedback", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Oh", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Sequential Preference Ranking for Efficient Reinforce- ment Learning from Human Feedback. In Advances in Neural Information Processing Systems (NeurIPS), 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Reward Learning from Human Preferences and Demonstrations in Atari", "authors": [{"first": "B", "middle": [], "last": "Ibarz", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Learning from Human Preferences and Demonstrations in Atari. In Advances in Neural Information Processing Systems (NeurIPS), 2018.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Scalable Deep Reinforcement Learning for Vision-Based Robotic Manipulation", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Pastor", "suffix": ""}, {"first": "J", "middle": [], "last": "Ibarz", "suffix": ""}, {"first": "A", "middle": [], "last": "Herzog", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Conference on Robot Learning (CoRL)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Scalable Deep Reinforcement Learning for Vision-Based Robotic Manipulation. In Conference on Robot Learning (CoRL), 2018.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Offline Preference-guided Policy Optimization. In arXiv preprint", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Shi", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "He", "suffix": ""}, {"first": "D", "middle": ["Beyond"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.16217"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Beyond Reward: Offline Preference-guided Policy Optimization. In arXiv preprint arXiv:2305.16217, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "A Survey of Reinforcement Learning from Human Feedback", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.14925"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, E. A Survey of Reinforcement Learning from Human Feed- back. In arXiv preprint arXiv:2312.14925, 2023.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Preference Transformer: Modeling Human Preferences using Transformers for RL", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Park", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, K. Preference Transformer: Modeling Human Preferences using Transformers for RL. In International Conference on Learning Representations (ICLR), 2022.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "A Method for Stochastic Optimization", "authors": [{"first": "D", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ba", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1412.6980"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>: A Method for Stochastic Optimization. In arXiv preprint arXiv:1412.6980, 2014.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Deep Reinforcement Learning for Autonomous Driving: A Survey", "authors": [{"first": "B", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON>b<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Mannion", "suffix": ""}, {"first": "A", "middle": ["A"], "last": "Al Sallab", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "IEEE Transactions on Intelligent Transportation Systems (TITS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>. <PERSON> Reinforce- ment Learning for Autonomous Driving: A Survey. In IEEE Transactions on Intelligent Transportation Systems (TITS), 2021.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Offline Reinforcement Learning with Implicit Q-Learning", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems Workshop (NeurIPS Workshop)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>. Offline Reinforcement Learning with Implicit Q-Learning. In Advances in Neu- ral Information Processing Systems Workshop (NeurIPS Workshop), 2021.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Benchmarking Preference-Based Reinforcement Learning", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "B-Pref", "suffix": ""}], "year": null, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, P. B-Pref: Benchmarking Preference-Based Reinforcement Learn- ing. In Neural Information Processing Systems (NeurIPS), 2021a.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Pebble: Feedback-Efficient Interactive Reinforcement Learning via Relabeling Experience and Unsupervised Pre-training", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. <PERSON>: Feedback- Efficient Interactive Reinforcement Learning via Rela- beling Experience and Unsupervised Pre-training. In International Conference on Machine Learning (ICML), 2021b.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Survival Instinct in Offline Reinforcement Learning", "authors": [{"first": "A", "middle": [], "last": "Li", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Kolobov", "suffix": ""}, {"first": "C.-A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, C.<PERSON><PERSON>. Survival Instinct in Offline Reinforcement Learning. In Advances in Neural Information Processing Systems (NeurIPS), 2023.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Reward Uncertainty for Exploration in Preference-based Reinforcement Learning", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Shu", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Uncer- tainty for Exploration in Preference-based Reinforcement Learning. In International Conference on Learning Rep- resentations (ICLR), 2021.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Decoupled Weight Decay Regularization", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON>pled Weight Decay Regularization. In International Conference on Learning Representations (ICLR), 2018.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Contrastive Value Learning: Implicit Models for Simple Offline RL", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Eysenbach", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Conference on Robot Learning (CoRL)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Contrastive Value Learning: Implicit Models for Simple Offline RL. In Conference on Robot Learning (CoRL), 2023.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "On The Fragility of Learned Reward Functions", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Gleave", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2301.03652"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> On The Fragility of Learned Reward Functions. In arXiv preprint arXiv:2301.03652, 2023.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Playing Atari with Deep Reinforcement Learning", "authors": [{"first": "V", "middle": [], "last": "<PERSON>ni<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Kavukcuoglu", "suffix": ""}, {"first": "D", "middle": [], "last": "Silver", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1312.5602"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, D., and <PERSON><PERSON>, <PERSON>. Play- ing Atari with Deep Reinforcement Learning. In arXiv preprint arXiv:1312.5602, 2013.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Learning Multimodal Rewards from Rankings", "authors": [{"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Biyik", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Conference on Robot Learning (CoRL)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, D. Learning Multimodal Rewards from Rankings. In Conference on Robot Learning (CoRL), 2022.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Training Language Models to Follow Instructions with Human Feedback", "authors": [{"first": "L", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "D", "middle": [], "last": "Almeida", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Training Language Models to Follow Instructions with Human Feedback. In Advances in Neural Informa- tion Processing Systems (NeurIPS), 2022.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Learning Reward Functions by Integrating Human Demonstrations and Preferences", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Robotics: Science and Systems (RSS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, D. Learning Reward Functions by Integrating Human Demonstrations and Preferences. In Robotics: Science and Systems (RSS), 2019.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "SURF: Semi-supervised Reward Learning with Data Augmentation for Feedback-efficient Preference-based Reinforcement Learning", "authors": [{"first": "J", "middle": [], "last": "Park", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, K. SURF: Semi-supervised Reward Learning with Data Augmentation for Feedback-efficient Preference-based Reinforcement Learning. In International Conference on Learning Representations (ICLR), 2021.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "The Analysis of Permutations", "authors": [{"first": "R", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1975, "venue": "Journal of the Royal Statistical Society Series C: Applied Statistics", "volume": "24", "issue": "2", "pages": "193--202", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> The Analysis of Permutations. Journal of the Royal Statistical Society Series C: Applied Statistics, 24(2):193-202, 1975.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Benchmarks and Algorithms for Offline Preference-Based Reward Learning", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["S"], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "In Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, D. S. Benchmarks and Al- gorithms for Offline Preference-Based Reward Learning. In Transactions on Machine Learning Research (TMLR), 2022.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Mastering the Game of Go without Human Knowledge", "authors": [{"first": "D", "middle": [], "last": "Silver", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Bolton", "suffix": ""}], "year": 2017, "venue": "Nature", "volume": "550", "issue": "7676", "pages": "354--359", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Mastering the Game of Go without Human Knowledge. Nature, 550(7676):354-359, 2017.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "S4RL: Surprisingly Simple Self-Supervision for Offline Reinforcement Learning in Robotics", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>arg", "suffix": ""}], "year": null, "venue": "Conference on Robot Learning (CoRL)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, A. S4RL: Surpris- ingly Simple Self-Supervision for Offline Reinforcement Learning in Robotics. In Conference on Robot Learning (CoRL), 2022.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Preference Ranking Optimization for Human Alignment", "authors": [{"first": "F", "middle": [], "last": "Song", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Association for the Advancement of Artificial Intelligence (AAAI)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, H. Preference Ranking Optimization for Human Align- ment. In Association for the Advancement of Artificial Intelligence (AAAI), 2024.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Learning to Summarize from Human Feedback", "authors": [{"first": "N", "middle": [], "last": "Stiennon", "suffix": ""}, {"first": "L", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "P", "middle": ["F"], "last": "", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Radford, A., <PERSON>, D., and <PERSON>, P. F. Learning to Summarize from Human Feedback. In Advances in Neural Information Processing Systems (NeurIPS), 2020.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Reinforcement Learning: An Introduction", "authors": [{"first": "R", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["G"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON>, A<PERSON> G. Reinforcement Learning: An Introduction. MIT press, 2018.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Scalable Learning to Rank via Differentiable Sorting", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. Pi<PERSON>ank: Scalable Learning to Rank via Differentiable Sorting. In Advances in Neural Information Processing Systems (NeurIPS), 2021.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Learning Agile Locomotion for Quadruped Robots. In arXiv preprint", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Iscen", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Sim-To-Real", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1804.10332"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S., and <PERSON>, V. Sim-to-Real: Learn- ing Agile Locomotion for Quadruped Robots. In arXiv preprint arXiv:1804.10332, 2018.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "CORL: Research-oriented Deep Offline Reinforcement Learning Library", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Advances in Neural Information Processing Systems (NeurIPS) Datasets and Benchmarks Track", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, S. CORL: Research-oriented Deep Offline Reinforcement Learning Library. In Advances in Neural Information Processing Systems (NeurIPS) Datasets and Benchmarks Track, 2023.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "DeepMind Control Suite. In arXiv preprint", "authors": [{"first": "Y", "middle": [], "last": "Tassa", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "D", "middle": ["D L"], "last": "Casas", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>den", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1801.00690"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Casas, D. d. L., Budden, D., <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. DeepMind Control Suite. In arXiv preprint arXiv:1801.00690, 2018.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Llama 2: Open Foundation and Fine-Tuned Chat Models", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Stone", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Batra", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09288"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N., Batra, S., Bharga<PERSON>, P., <PERSON>, S., et al. Llama 2: Open Foundation and Fine- Tuned Chat Models. In arXiv preprint arXiv:2307.09288, 2023.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Grandmaster level in StarCraft II using multi-agent reinforcement learning", "authors": [{"first": "O", "middle": [], "last": "<PERSON>yal<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": ["M"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Ewalds", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Nature", "volume": "575", "issue": "7782", "pages": "350--354", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Grandmaster level in StarCraft II using multi-agent reinforcement learning. Nature, 575 (7782):350-354, 2019.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Rating-based Reinforcement Learning", "authors": [{"first": "D", "middle": [], "last": "White", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": ["R"], "last": "Waytowich", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning Workshop (ICML Workshop", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, V., Waytowich, N. R., and <PERSON>, Y. Rating-based Reinforcement Learn- ing. In International Conference on Machine Learning Workshop (ICML Workshop), 2023.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "A Bayesian Approach for Policy Learning from Trajectory Preference Queries", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Fern", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, P. A Bayesian Approach for Policy Learning from Trajectory Preference Queries. In Advances in Neural Information Processing Systems (NeurIPS), 2012.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "A Survey of Preference-Based Reinforcement Learning Methods", "authors": [{"first": "C", "middle": [], "last": "Wirth", "suffix": ""}, {"first": "R", "middle": [], "last": "Akrour", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Fürnkranz", "suffix": ""}], "year": 2017, "venue": "In Journal of Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. A Survey of Preference-Based Reinforcement Learning Methods. In Journal of Machine Learning Research (JMLR), 2017.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Listwise Approach to Learning to Rank: Theory and Algorithm", "authors": [{"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Li", "suffix": ""}], "year": 2008, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Listwise Approach to Learning to Rank: Theory and Algorithm. In International Conference on Machine Learning (ICML), 2008.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Adarank: A Boosting Algorithm for Information Retrieval", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Li", "suffix": ""}], "year": 2007, "venue": "International ACM SIGIR Conference on Research and Development in Information Retrieval (SIGIR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>: A Boosting Algorithm for Information Retrieval. In International ACM SIGIR Con- ference on Research and Development in Information Retrieval (SIGIR), 2007.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "World: A Benchmark and Evaluation for Multi-Task and Meta Reinforcement Learning", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "He", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Meta", "suffix": ""}], "year": 2020, "venue": "Conference on Robot Learning (CoRL)", "volume": "", "issue": "", "pages": "1094--1100", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>-<PERSON>: A Benchmark and Evalua- tion for Multi-Task and Meta Reinforcement Learning. In Conference on Robot Learning (CoRL), pp. 1094-1100. PMLR, 2020.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Conservative Data Sharing for Multi-Task Offline Reinforcement Learning", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Chebotar", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, C. Conservative Data Sharing for Multi-Task Offline Reinforcement Learning. In Advances in Neural Information Processing Systems (NeurIPS), 2021a.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Conservative Offline Model-Based Policy Optimization", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Combo", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Conservative Offline Model-Based Policy Optimization. In Advances in Neural Information Processing Systems (NeurIPS), 2021b.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Efficient Offline Preference-Based Reinforcement Learning with Transition-Dependent Discounting", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>fficient Offline Preference-Based Reinforce- ment Learning with Transition-Dependent Discounting, 2023. URL https://openreview.net/forum? id=7kKyELnAhn.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Sequence Likelihood Calibration with Human Feedback. In arXiv preprint", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Slic-Hf", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.10425"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, P. J. SLiC-HF: Sequence Likelihood Calibration with Human Feedback. In arXiv preprint arXiv:2305.10425, 2023.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Principled Reinforcement Learning with Human Feedback from Pairwise or K-wise Comparisons", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Jordan", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>pled Reinforcement Learning with Human Feedback from Pairwise or K-wise Comparisons. In International Conference on Machine Learning (ICML), 2023.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "num": null, "text": "Figure 1: An overview of LiRE. The figure shows an example of a button-press-topdown task. We sample a trajectory segment and sequentially obtain the preference feedback for existing trajectories in RLT. We use binary search to find the correct rank (left) efficiently. Multiple preference pairs are generated from RLT to learn the reward model (right).", "fig_num": "1", "uris": null}, "FIGREF1": {"type_str": "figure", "num": null, "text": "Figure 2: Scatter plots of the estimated rewards for the segments used for box-close task. The reward models are trained with MR or LiRE using the exp or linear score function. The Pearson correlation coefficient, r, is presented.", "fig_num": "2", "uris": null}, "FIGREF2": {"type_str": "figure", "num": null, "text": "Figure3: Average success rates of each method while varying the number of preference feedbacks. The black dotted line represents the average success rates when trained with GT reward.", "fig_num": "3", "uris": null}, "FIGREF3": {"type_str": "figure", "num": null, "text": "Figure 2 scatter plots the estimated rewards (y-axis), learned with 500 preference feedbacks, of the segments in box-close task against the GT rewards (x-axis). Note our LiRE uses fewer segments to train the reward model, so Figure 2(b) contains fewer dots than Figure 2(a). Each segment has a length of 25 and both GT and the estimated rewards are normalized to values between [0, 25].", "fig_num": null, "uris": null}, "FIGREF4": {"type_str": "figure", "num": null, "text": "Figure 4: Robustness of LiRE w.r.t the feedback noise.", "fig_num": "4", "uris": null}, "FIGREF5": {"type_str": "figure", "num": null, "text": "Figure 5: Effect of the granularity of preference feedback.", "fig_num": "5", "uris": null}, "FIGREF6": {"type_str": "figure", "num": null, "text": "Figure 6: Combining LiRE with other baselines.", "fig_num": "6", "uris": null}, "FIGREF7": {"type_str": "figure", "num": null, "text": "Figure 10: Estimated rewards for the segments used in preference learning for box-close task. We train the reward model with MR or LiRE using the exp or linear score function. The Pearson correlation coefficient, r is presented.", "fig_num": "10", "uris": null}, "TABREF0": {"html": null, "type_str": "table", "num": null, "text": "Average success rates on medium-replay dataset over six random seeds. We use 500 and 1000 preference feedbacks and report the average performance of the last five trained policies. The yellow and gray shading represent the best and second-best performances, respectively.", "content": "<table><tr><td># of feedbacks</td><td>Algorithm</td><td>button-press -topdown</td><td>box-close</td><td>dial-turn</td><td>sweep</td><td>button-press -topdown-wall</td><td colspan=\"2\">sweep-into drawer-open</td><td>lever-pull</td></tr><tr><td>-</td><td>IQL with GT rewards</td><td>88.33 ± 4.76</td><td>93.40 ± 3.10</td><td>75.40 ± 5.47</td><td>98.33 ± 1.87</td><td>56.27 ± 6.32</td><td>78.80 ± 7.96</td><td>100.00 ± 0.00</td><td>98.47 ± 1.77</td></tr><tr><td/><td>MR</td><td>9.60 ± 5.74</td><td>10.33 ± 8.23</td><td colspan=\"2\">50.20 ± 8.51 79.80 ± 13.36</td><td>0.13 ± 0.50</td><td>24.80 ± 5.28</td><td>98.07 ± 3.20</td><td>50.53 ± 8.55</td></tr><tr><td/><td>PT (Kim et al., 2022)</td><td>22.87 ± 9.06</td><td colspan=\"3\">0.33 ± 1.16 68.67 ± 12.39 43.07 ± 24.57</td><td>0.87 ± 1.43</td><td>20.53 ± 8.26</td><td colspan=\"2\">88.73 ± 11.64 82.40 ± 22.69</td></tr><tr><td/><td>OPRL (Shin et al., 2022)</td><td>12.13 ± 5.75</td><td colspan=\"2\">4.73 ± 3.24 54.33 ± 11.47</td><td>94.13 ± 5.95</td><td>0.20 ± 0.60</td><td>25.87 ± 8.58</td><td colspan=\"2\">94.13 ± 6.41 54.67 ± 12.79</td></tr><tr><td>500</td><td>DPPO (An et al., 2023)</td><td colspan=\"4\">3.93 ± 4.34 10.20 ± 11.47 26.67 ± 22.23 10.47 ± 15.84</td><td>0.80 ± 1.51</td><td>23.07 ± 7.02</td><td colspan=\"2\">35.93 ± 11.18 10.13 ± 12.19</td></tr><tr><td/><td>IPL (Hejna &amp; Sadigh, 2024)</td><td>34.73 ± 13.92</td><td colspan=\"3\">5.93 ± 5.81 31.53 ± 12.50 27.20 ± 23.81</td><td>8.93 ± 9.84</td><td>32.20 ± 7.35</td><td colspan=\"2\">19.00 ± 13.63 31.20 ± 15.76</td></tr><tr><td/><td>SeqRank (Hwang et al., 2023)</td><td>17.6 ± 11.94</td><td>13.2 ± 12.72</td><td>65.6 ± 12.84</td><td>83.4 ± 9.76</td><td colspan=\"2\">1.73 ± 1.98 25.67 ± 11.02</td><td>99.53 ± 0.36</td><td>95.67 ± 4.04</td></tr><tr><td/><td>LiRE (ours)</td><td colspan=\"4\">67.20 ± 18.97 51.53 ± 18.48 79.07 ± 10.96 77.53 ± 10.50</td><td colspan=\"2\">79.13 ± 15.19 49.13 ± 15.85</td><td>99.40 ± 1.65</td><td>95.67 ± 6.26</td></tr><tr><td/><td>MR</td><td>9.27 ± 5.30</td><td>17.07 ± 9.56</td><td>59.07 ± 7.57</td><td>90.80 ± 9.74</td><td>0.60 ± 1.87</td><td>26.07 ± 8.57</td><td colspan=\"2\">96.47 ± 4.02 50.87 ± 10.89</td></tr><tr><td/><td>PT (Kim et al., 2022)</td><td>18.27 ± 10.62</td><td>2.27 ± 2.86</td><td colspan=\"2\">68.80 ± 5.50 29.13 ± 14.55</td><td>2.13 ± 2.96</td><td>20.27 ± 7.84</td><td colspan=\"2\">95.40 ± 7.27 72.93 ± 10.16</td></tr><tr><td/><td>OPRL (Shin et al., 2022)</td><td colspan=\"3\">11.00 ± 7.84 15.07 ± 11.19 51.33 ± 10.08</td><td>85.53 ± 5.43</td><td>0.33 ± 0.75</td><td>28.27 ± 6.40</td><td>99.20 ± 1.42</td><td>53.20 ± 6.67</td></tr><tr><td>1000</td><td>DPPO (An et al., 2023)</td><td>3.20 ± 3.04</td><td colspan=\"2\">9.33 ± 9.60 36.40 ± 21.95</td><td>8.73 ± 16.37</td><td>0.27 ± 0.85</td><td>23.33 ± 7.80</td><td>36.47 ± 7.30</td><td>8.53 ± 9.96</td></tr><tr><td/><td>IPL (Hejna &amp; Sadigh, 2024)</td><td>36.67 ± 17.40</td><td colspan=\"3\">6.73 ± 8.41 43.93 ± 13.37 38.33 ± 24.87</td><td>14.07 ± 11.47</td><td>30.40 ± 7.74</td><td colspan=\"2\">28.53 ± 18.37 40.40 ± 17.38</td></tr><tr><td/><td>SeqRank (Hwang et al., 2023)</td><td colspan=\"2\">13.93 ± 8.11 46.60 ±12.53</td><td>70.67 ± 7.58</td><td>74.93 ±22.35</td><td colspan=\"2\">2.47 ±2.67 29.33 ± 11.59</td><td>98.6 ± 3.92</td><td>95.47 ± 3.86</td></tr><tr><td/><td>LiRE (ours)</td><td>83.07 ± 6.38</td><td>89.13 ± 6.02</td><td>76.93 ± 7.55</td><td>75.87 ± 6.81</td><td colspan=\"2\">81.47 ± 10.04 57.73 ± 13.11</td><td>99.73 ± 0.85</td><td>99.47 ± 1.15</td></tr></table>"}, "TABREF1": {"html": null, "type_str": "table", "num": null, "text": "Average success rates on medium-replay with 500 preference feedbacks. ± 5.74 10.33 ± 8.23 50.20 ± 8.51 50.53 ± 8.55 ✓ exp(x) 12.87 ± 7.86 22.73 ± 10.40 65.87 ± 9.46 57.87 ± 11.28", "content": "<table><tr><td>RLT ϕ(x)</td><td>button-press -topdown</td><td>box-close</td><td>dial-turn</td><td>lever-pull</td></tr><tr><td>✗ exp(x)</td><td>9.60</td><td/><td/><td/></tr></table>"}, "TABREF2": {"html": null, "type_str": "table", "num": null, "text": "Table 3 demonstrates that using the linear score function (bottom two rows) clearly", "content": "<table/>"}, "TABREF3": {"html": null, "type_str": "table", "num": null, "text": "Average success rates of LiRE when adjusting the Q budget. We use a total of 500 preference feedbacks.", "content": "<table><tr><td>Dataset</td><td>Q=1 (MR w/ linear)</td><td>Q=2</td><td>Q=10</td><td>Q=20</td><td>Q=50</td><td>Q=100</td><td>Q=500</td><td>SeqRank w/ linear</td></tr><tr><td>button-press-topdown</td><td>36.87 ± 13.75</td><td colspan=\"6\">59.47 ± 2.18 53.60 ± 10.82 65.13 ± 14.24 71.26 ± 12.95 67.20 ± 18.97 77.67 ± 18.13</td><td>54.87 ± 9.89</td></tr><tr><td>lever-pull</td><td>70.20 ±18.03</td><td colspan=\"2\">69.80 ± 3.79 70.47 ±18.19</td><td>92.7 ±7.16</td><td>93.4 ±7.90</td><td>95.67 ± 6.26</td><td>99.33 ± 1.18</td><td>74.33 ± 18.50</td></tr><tr><td colspan=\"4\">brings a significant performance improvement compared</td><td/><td/><td/><td/><td/></tr><tr><td colspan=\"4\">to the default exponential score function (first two rows).</td><td/><td/><td/><td/><td/></tr></table>"}, "TABREF4": {"html": null, "type_str": "table", "num": null, "text": "Comparison results between SeqRank and LiRE on Meta-World medium-replay dataset.", "content": "<table><tr><td># of feedbacks</td><td>Algorithm</td><td>Avg success rate</td><td># of groups in the list</td><td>Feedback efficiency</td><td>Sample diversity</td></tr><tr><td>500</td><td colspan=\"2\">SeqRank w/ linear 61.84 ± 15.80 LiRE 74.83 ± 12.23</td><td>2.3 ± 0.09 9.3 ± 1.83</td><td colspan=\"2\">2.20 ± 0.78 11.33 ± 3.39 0.474 ± 0.069 1.002</td></tr><tr><td>1000</td><td colspan=\"2\">SeqRank w/ linear 67.49 ± 13.56 LiRE 82.92 ± 6.48</td><td>2.3 ± 0.09 9.3 ± 1.84</td><td colspan=\"2\">2.18 ± 0.75 11.33 ± 3.28 0.474 ± 0.067 1.001</td></tr></table>"}, "TABREF5": {"html": null, "type_str": "table", "num": null, "text": "Average episode returns of MR, SeqRank, and LiRE on DMControl medium-replay dataset.", "content": "<table><tr><td colspan=\"2\">Algorithm</td><td colspan=\"3\">hopper-hop walker-walk humanoid-walk</td></tr><tr><td colspan=\"4\">IQL with GT rewards 157.95 ± 9.64</td><td>839.6 ± 36.57</td><td>250.9 ± 11.62</td></tr><tr><td colspan=\"2\">MR w/ linear</td><td colspan=\"3\">53.96 ± 24.42 677.38 ± 88.14</td><td>84.35 ± 23.23</td></tr><tr><td colspan=\"2\">SeqRank w/ linear</td><td colspan=\"3\">80.84 ± 27.67 698.81 ± 91.71</td><td>80.68 ± 14.67</td></tr><tr><td colspan=\"2\">LiRE</td><td colspan=\"3\">99.14 ± 12.28 822.27 ± 50.83</td><td>104.08 ± 17.45</td></tr><tr><td>0 20 40 60 80 100 Success rate (%)</td><td colspan=\"2\">sweep-into lever-pull button-press -topdown OPRL LiRE LiRE + OPRL</td><td>0 20 40 60 80 100 Success rate (%)</td><td>sweep-into lever-pull drawer-open PT LiRE LiRE + PT</td></tr><tr><td/><td colspan=\"2\">(a) LiRE with OPRL</td><td/><td>(b) LiRE with PT</td></tr></table>"}, "TABREF6": {"html": null, "type_str": "table", "num": null, "text": "Table7presents the results with real human preference feedback on the new button-press-topdown offline RL dataset, which is distinct from the dataset used in Table2. Namely, we collected 200 preference feedbacks from one of the authors for each of the three feedback collection methods: MR, SeqRank, and LiRE. For LiRE, we used the feedback budget of Q = 100, resulting in two RLTs. The results again indicate that LiRE dominates other baselines and gets stronger when the linear score function is used. We believe this result shows the potential of LiRE that it can be very Average success rates of a button-press-topdown task with 200 of real human feedbacks. ± 8.85 40.93 ± 10.72 78.27 ± 6.59x 43.33 ± 25.72 74.13 ± 9.96 90.67 ± 7.57", "content": "<table><tr><td>ϕ(x)</td><td>MR</td><td>SeqRank</td><td>LiRE</td></tr><tr><td>exp(x)</td><td>38.00</td><td/><td/></tr></table>"}, "TABREF7": {"html": null, "type_str": "table", "num": null, "text": "Table 10 compares the performance of LiRE trained with listwise and pairwise losses. As shown in Table 10, training the reward model with pairwise loss is more stable and performs better in most cases, except for sweep and sweep-into tasks.", "content": "<table/>"}, "TABREF8": {"html": null, "type_str": "table", "num": null, "text": "Average success rates on medium-replay dataset when using the listwise loss for training the reward model.", "content": "<table><tr><td># of feedbacks</td><td>Algorithm</td><td>button-press -topdown</td><td>box-close</td><td>dial-turn</td><td>sweep</td><td>button-press -topdown-wall</td><td colspan=\"2\">sweep-into drawer-open</td><td>lever-pull</td></tr><tr><td>-</td><td>IQL with GT rewards</td><td>88.33 ± 4.76</td><td>93.40 ± 3.10</td><td>75.40 ± 5.47</td><td>98.33 ± 1.87</td><td>56.27 ± 6.32</td><td>78.80 ± 7.96</td><td>100.00 ± 0.00</td><td>98.47 ± 1.77</td></tr><tr><td>500</td><td><PERSON><PERSON> w/ listwise LiRE w/ pairwise</td><td colspan=\"4\">53.13 ± 10.63 55.07 ± 16.11 67.20 ± 18.97 51.53 ± 18.48 79.07 ± 10.96 77.53 ± 10.50 63.87 ± 8.39 99.53 ± 1.12</td><td colspan=\"2\">17.73 ± 11.51 63.47 ± 11.47 79.13 ± 15.19 49.13 ± 15.85</td><td colspan=\"2\">98.60 ± 3.27 84.53 ± 10.33 99.40 ± 1.65 95.67 ± 6.26</td></tr><tr><td>1000</td><td>LiRE w/ listwise LiRE w/ pairwise</td><td>55.73 ± 8.57 83.07 ± 6.38</td><td>68.07 ± 9.06 89.13 ± 6.02</td><td>68.20 ± 9.37 76.93 ± 7.55</td><td>99.07 ± 1.44 75.87 ± 6.81</td><td colspan=\"2\">23.93 ± 7.31 62.60 ± 12.21 81.47 ± 10.04 57.73 ± 13.11</td><td>99.40 ± 2.08 99.73 ± 0.85</td><td>83.80 ± 7.97 99.47 ± 1.15</td></tr></table>"}, "TABREF9": {"html": null, "type_str": "table", "num": null, "text": "Average success rates on Meta-World medium-replay dataset with increased epochs. There is a performance improvement when training with longer epochs. .5. Applying a Linear Score Function to Other Baselines Many existing studies utilize the exponential score function for PbRL using human feedback(<PERSON><PERSON> et al., 2017; <PERSON>  et al., 2021b). Nevertheless, numerous other score functions are also prevalent in the PbRL literature such as Table 1 in ± 5.74 12.87 ± 7.86 36.87 ± 13.75 67.20 ± 18.97 box-close 10.33 ± 8.23 22.73 ± 10.40 11.27 ± 14.91 51.53 ± 18.48 dial-turn 50.20 ± 8.51 65.87 ± 9.46 77.27 ± 11.90 79.07 ± 10.96 sweep 79.80 ± 13.36 82.67 ± 19.86 78.47 ± 10.88 77.53 ± 10.50 button-press-topdown-wall 0.13 ± 0.50 1.33 ± 2.15 8.27 ± 8.64 79.13 ± 15.19 sweep-into 24.80 ± 5.28 24.87 ± 8.39 49.73 ± 13.52 49.13 ± 15.85 drawer-open 98.07 ± 3.20 98.67 ± 1.89 97.20 ± 5.88 99.40 ± 1.65 lever-pull 50.53 ± 8.55 57.87 ± 11.28 70.20 ± 18.03 95.67 ± 6.26", "content": "<table><tr><td colspan=\"2\">Epochs Algorithm</td><td>button-press -topdown</td><td>box-close</td><td>dial-turn</td><td>sweep</td><td>button-press -topdown-wall</td><td colspan=\"2\">sweep-into drawer-open</td><td>lever-pull</td></tr><tr><td>-</td><td>IQL with GT rewards</td><td>88.33 ± 4.76</td><td>93.40 ± 3.10</td><td>75.40 ± 5.47</td><td>98.33 ± 1.87</td><td>56.27 ± 6.32</td><td>78.80 ± 7.96</td><td>100.00 ± 0.00</td><td>98.47 ± 1.77</td></tr><tr><td/><td>MR</td><td>9.60 ± 5.74</td><td>10.33 ± 8.23</td><td colspan=\"2\">50.20 ± 8.51 79.80 ± 13.36</td><td>0.13 ± 0.50</td><td>24.80 ± 5.28</td><td>98.07 ± 3.20</td><td>50.53 ± 8.55</td></tr><tr><td>300</td><td>LiRE w/ exp</td><td colspan=\"2\">12.87 ± 7.86 22.73 ± 10.40</td><td colspan=\"2\">65.87 ± 9.46 82.67 ± 19.86</td><td>1.33 ± 2.15</td><td>24.87 ± 8.39</td><td colspan=\"2\">98.67 ± 1.89 57.87 ± 11.28</td></tr><tr><td/><td>LiRE w/ linear</td><td colspan=\"4\">67.20 ± 18.97 51.53 ± 18.48 79.07 ± 10.96 77.53 ± 10.50</td><td colspan=\"2\">79.13 ± 15.19 49.13 ± 15.85</td><td>99.40 ± 1.65</td><td>95.67 ± 6.26</td></tr><tr><td/><td>MR</td><td colspan=\"2\">32.87 ± 9.94 31.80 ± 12.65</td><td>60.33 ± 8.34</td><td>93.5 ± 6.61</td><td>25.40 ± 10.25</td><td>36.00 ± 9.58</td><td>98.00 ± 4.00</td><td>75.93 ± 6.46</td></tr><tr><td>5000</td><td>LiRE w/ exp</td><td colspan=\"2\">68.33 ± 16.33 83.13 ± 10.36</td><td>77.53 ± 6.25</td><td>91.87 ± 7.02</td><td colspan=\"2\">36.80 ± 14.17 59.53 ± 14.99</td><td>99.93 ± 0.36</td><td>79.47 ± 8.61</td></tr><tr><td/><td>LiRE w/ linear</td><td>77.27 ± 13.52</td><td>76.6 ± 17.16</td><td>88.33 ± 5.49</td><td>87.6 ± 15.45</td><td colspan=\"2\">77.27 ± 13.52 60.67 ± 11.96</td><td>97.07 ± 5.63</td><td>83.4 ± 6.43</td></tr></table>"}}}}