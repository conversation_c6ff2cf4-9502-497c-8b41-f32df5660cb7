{"paper_id": "neuralsph", "title": "Neural SPH: Improved Neural Modeling of Lagrangian Fluid Dynamics", "abstract": "Smoothed particle hydrodynamics (SPH) is omnipresent in modern engineering and scientific disciplines. SPH is a class of Lagrangian schemes that discretize fluid dynamics via finite material points that are tracked through the evolving velocity field. Due to the particle-like nature of the simulation, graph neural networks (GNNs) have emerged as appealing and successful surrogates. However, the practical utility of such GNN-based simulators relies on their ability to faithfully model physics, providing accurate and stable predictions over long time horizons -which is a notoriously hard problem. In this work, we identify particle clustering originating from tensile instabilities as one of the primary pitfalls. Based on these insights, we enhance both training and rollout inference of state-of-the-art GNNbased simulators with varying components from standard SPH solvers, including pressure, viscous, and external force components. All Neural SPH-enhanced simulators achieve better performance than the baseline GNNs, often by orders of magnitude in terms of rollout error, allowing for significantly longer rollouts and significantly better physics modeling. Code available under https://github.com/tumaer/neuralsph.", "pdf_parse": {"paper_id": "neuralsph", "abstract": [{"text": "Smoothed particle hydrodynamics (SPH) is omnipresent in modern engineering and scientific disciplines. SPH is a class of Lagrangian schemes that discretize fluid dynamics via finite material points that are tracked through the evolving velocity field. Due to the particle-like nature of the simulation, graph neural networks (GNNs) have emerged as appealing and successful surrogates. However, the practical utility of such GNN-based simulators relies on their ability to faithfully model physics, providing accurate and stable predictions over long time horizons -which is a notoriously hard problem. In this work, we identify particle clustering originating from tensile instabilities as one of the primary pitfalls. Based on these insights, we enhance both training and rollout inference of state-of-the-art GNNbased simulators with varying components from standard SPH solvers, including pressure, viscous, and external force components. All Neural SPH-enhanced simulators achieve better performance than the baseline GNNs, often by orders of magnitude in terms of rollout error, allowing for significantly longer rollouts and significantly better physics modeling. Code available under https://github.com/tumaer/neuralsph.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "In the sciences, considerable efforts have led to the development of highly complex mathematical models of our world, with many naturally formulated as partial differential equations (PDEs). Over the past years, deep neu- ral network-based PDE surrogates have gained significant momentum as a more computationally efficient solution methodology (<PERSON><PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> & Ku<PERSON>, 2023) , transforming amongst others computational fluid dynamics (<PERSON> et al., 2016; <PERSON><PERSON> et al., 2021; <PERSON> et al., 2021; Gupta & Brandstetter, 2022; <PERSON><PERSON> et al., 2024) , weather forecasting (Rasp & Thuerey, 2021; <PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2024) , and molecular modeling (<PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) .", "section": "Introduction", "sec_num": "1."}, {"text": "In computational fluid dynamics (CFD), we broadly categorize numerical simulation methods into two distinct families: particle-based and grid-based, better known as Lagrangian and Eulerian discretization schemes. In Eulerian schemes, space is discretized, i.e., fixed finite nodes or control volumes lead to grid-based or mesh-based models. In Lagrangian schemes, the discretization happens on finite material points, commonly known as particles, which dynamically move with the local deformation of the continuum. One of the most prominent Lagrangian discretization schemes is smoothed particle hydrodynamics (SPH), originally proposed by <PERSON> (1977) and <PERSON><PERSON><PERSON> & <PERSON> (1977) for applications in astrophysics. In contrast to grid-and mesh-based approaches, SPH approximates the field properties using radial kernel interpolations over adjacent particles. The strength of the SPH method is that it does not require connectivity constraints, e.g., meshes, which is particularly useful for simulating systems with large deformations. Since its foundation, SPH has been greatly extended and is the preferred method to simulate problems with (a) free surfaces (<PERSON> et al., 2011; Viol<PERSON> & Rogers, 2016) , (b) complex boundaries (<PERSON><PERSON> et al., 2012) , (c) multi-phase flows (<PERSON> & <PERSON>, 2007) , and (d) fluid-structure interactions (<PERSON><PERSON><PERSON> et al., 2007) .", "section": "Introduction", "sec_num": "1."}, {"text": "In deep learning, graph neural networks (GNNs) (<PERSON><PERSON><PERSON> et al., 2008; <PERSON> & Welling, 2017) are an obvious fit to model particle-based dynamics. Often, predicted accelerations at the nodes are numerically integrated to model the time evolution of the particles or the mesh, i.e., dynamics are updated in a hybrid neural-numerical fashion (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2023) . Most recent applications of GNN-based simulators involve Lagrangian fluid simulations (<PERSON><PERSON><PERSON> et al., 2023a; 2024a; Winchenbach & Thuerey, 2024) . One downside of these simulators is the risk of non-physical instabilities during rollout, which affects the neural and numerical components.", "section": "Introduction", "sec_num": "1."}, {"text": "It is known that already standard SPH schemes exhibit tensile instability, i.e., numerical errors leading to particle clumping and void regions when negative pressure occurs within what should be an incompressible fluid (<PERSON>, 2012) . This has led to the development of improved SPH schemes explicitly targeting regularity of particle distribution (<PERSON><PERSON> et al., 2013; <PERSON> et al., 2017b) . A review of SPH literature indicates that even methods seeking to improve other properties, like reducing artificial dissipation (<PERSON> et al., 2017a) or handling violent water flows (<PERSON><PERSON> et al., 2011) , may also improve the particle distribution.", "section": "Introduction", "sec_num": "1."}, {"text": "In this work, we present a large-scale analysis of Lagrangian physical modeling capabilities of various GNN-based simulators, i.e., a non-equivariant and an equivariant one. We identify a shared pitfall, i.e., particle clustering effects that are similar to those known from SPH schemes. Particle clustering in GNN-based simulators limits stable rollouts and accurate physics modeling. Based on these insights, we draw inspiration from numerical SPH solvers and enhance both training and inference of state-of-the-art GNN-based simulators with varying components from standard SPH solvers, including (i) pressure, (ii) viscous, and (iii) external force components -all implemented in JAX (<PERSON> et al., 2018) . Methodologically, our main contributions are two: We demonstrate the efficacy of Neural SPH-enhanced Lagrangian simulators by achieving better performance on seven diverse 2D and 3D Lagrangian datasets -sometimes by orders of magnitude in terms of rollout error -than the baseline GNN, allowing for significantly better physical modeling capabilities. We note that the introduced Neural SPH techniques may apply to a wide range of physics scenarios beyond GNNs and SPH. Our source code is available at https://github.com/tumaer/neuralsph.", "section": "Introduction", "sec_num": "1."}, {"text": "Smoothed particle hydrodynamics. Smoothed particle hydrodynamics (SPH) approximates the incompressible Navier-Stokes equations (NSE) by the so-called weakly compressible NSE. This is necessary because the density of the fluid is defined by radial kernel summation ρ i = j m j W (r ij |h), where m j represents the mass of the adjacent particles j, and W the radial interpolation kernel with smoothing length h that operates on the scalar distance r ij . This summation may violate strict incompressibility. However, the weak compressibility assumption typically allows for up to ∼ 1% density deviation (<PERSON>, 2005) . This ∼ 1% is also enforced for the weakly compressible SPH method, while evolving density and momentum:", "section": "Simulating Lagrangian dynamics", "sec_num": "2."}, {"text": "EQUATION", "section": "Simulating Lagrangian dynamics", "sec_num": "2."}, {"text": "d dt (u) = - 1 ρ ∇p pressure + ν V ref L ref ∇ 2 u viscosity + g ext. force", "section": "Simulating Lagrangian dynamics", "sec_num": "2."}, {"text": ".", "section": "Simulating Lagrangian dynamics", "sec_num": "2."}, {"text": "(2)", "section": "Simulating Lagrangian dynamics", "sec_num": "2."}, {"text": "Herein, ρ is the density, u the velocity vector, p the pressure, g the external force, ν the viscosity, and U ref , L ref the reference velocity and length scale. Without loss of generality, we consider", "section": "Simulating Lagrangian dynamics", "sec_num": "2."}, {"text": "U ref = 1, L ref = 1.", "section": "Simulating Lagrangian dynamics", "sec_num": "2."}, {"text": "We note that either density summation with kernel averaging, or density evolution (Eq. ( 1)) is used to compute the density, and as we explain later, the former is the preferred and the latter the more general approach. To evolve the system in time, the above equation(s) are integrated in time by, e.g., semiimplicit Euler (see Appendix F). However, solving these equations with standard SPH methods may still produce artifacts, most notably when particle clumping exceeds the 1% density-fluctuation requirement (<PERSON><PERSON> et al., 2013) .", "section": "Simulating Lagrangian dynamics", "sec_num": "2."}, {"text": "The term responsible for a homogeneous particle distribution in the SPH method is the pressure gradient term 1 ρ ∇p in the momentum equation Eq. ( 2). In weakly compressible SPH, the pressure is computed from density through the equation of state", "section": "SPH particle redistribution.", "sec_num": null}, {"text": "EQUATION", "section": "SPH particle redistribution.", "sec_num": null}, {"text": "Thus, for a reliable approximation of the density ρ, the pressure term ensures a repulsive force of scale p ref whenever the density exceeds the given reference value ρ ref , where typically ρ ref = 1. However, the pressure term is not necessarily sufficient for producing a good particle distribution, as we can see in the bottom part of Fig. 9 in <PERSON><PERSON><PERSON> et al. (2024a) . For this reason, more advanced SPH schemes have been developed, distinguishing between the physical velocity field and the velocity by which particles are shifted (<PERSON><PERSON> et al., 2013; <PERSON> et al., 2017b) . These schemes are related to Arbitrary Lagrangian-Eulerian methods (<PERSON><PERSON> et al., 1974) instead of being fully Lagrangian.", "section": "SPH particle redistribution.", "sec_num": null}, {"text": "Challenges of density computation at free surfaces. Accurately computing the density at free surfaces is a difficult task for SPH methods. In the standard SPH formulation, the density at each particle is calculated by a kernel-weighted summation of the mass of adjacent particles (<PERSON><PERSON><PERSON> & <PERSON>, 1977) . However, particles at free surfaces have low density when using density summation, which leads to incorrect pressure values (<PERSON>, 1994) . The lowdensity inconsistency can be corrected for by globally and locally conservative least-squares interpolation (<PERSON><PERSON>, 2000) , adaptive kernel estimation procedure (<PERSON><PERSON><PERSON> et al., 2006) , or by initializing the simulation by first evolving particles with a heavily damped version of the momentum conservation (<PERSON> & <PERSON>, 2007) . However, most SPH methods for free surface flows resort to the continuity equation to represent the rate of change in density (<PERSON>, 1994; <PERSON> & Lok, 1999) . In this density evolution formulation, density derivatives are integrated over time (see Eq. ( 1)). On top of the density evolution, density filters, such as periodic re-initialization, are applied (<PERSON> et al., 2010; Cola<PERSON>ssi & <PERSON>rini, 2003; <PERSON>, 1968) .", "section": "SPH particle redistribution.", "sec_num": null}, {"text": "GNN-based simulators. The formulation of the learning problem is based on <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> et al., 2024a) . We look at the task of autoregressive acceleration prediction of a Lagrangian particle system, which we then integrate twice using semi-implicit Euler integration to evolve the system over time (see Appendix F). The datasets consist of particle types per particle and particle coordinates P t k over k ∈ (0, K) steps, where each frame", "section": "SPH particle redistribution.", "sec_num": null}, {"text": "P t is made up of n ∈ (1, N ) particles p t n ∈ R d in dimension d.", "section": "SPH particle redistribution.", "sec_num": null}, {"text": "The inputs to the learned surrogate are state vectors X t k-H :t k , with history size H, each of which contains the past velocities U k = [u k,1 , ..., u k,N ] inferred using the finite difference approximation of past coordinates, as well as optional features like external force vector g, e.g., gravity.", "section": "SPH particle redistribution.", "sec_num": null}, {"text": "We use the default configuration files from LagrangeBench for training, including random walk noise (<PERSON><PERSON><PERSON> et al., 2020) and the pushforward trick (<PERSON><PERSON><PERSON> et al., 2022b) . These default configurations provide the baseline models, on top of which we add our methods. To qualitatively understand clustering, in Fig. 2 , we plot the histogram of the per-particle number of neighbors corresponding to the left graphic of the 2D lid-driven cavity from Fig. 5 , which also has regions with high particle density. In Fig. 2 , we see a pronounced increase in the number of particles with 8-10 neighbors, indicating clustering artifacts. The problem of external forces. We observe that in roughly 8 out of 25 dam break test trajectories at step 80, the front of the wave spreads out as if a virtual wall exists way in front of the actual wall -see Fig. 1 and Appendix A. Such behavior has been discussed in literature (<PERSON><PERSON><PERSON><PERSON> et al., 2022) , and the current consensus is that the GNNbased simulators learn to infer the dynamics from velocity correlations. Thus, when the velocity reaches a given threshold, it has learned to model the presence of a wall. In the following, we demonstrate that by forcing the network to predict a target acceleration that excludes the external force part, the overall dynamics become more physical, and significantly fewer artifacts occur.", "section": "SPH particle redistribution.", "sec_num": null}, {"text": "In this section, we introduce Neural SPH, which improves both training and rollout inference of temporally coarsened GNN-based simulators. Neural SPH comprises a routine to correct for induced modeling errors due to external forces, and inference-time refinement steps of the system state based on SPH relaxation methods.", "section": "Neural SPH", "sec_num": "3."}, {"text": "Correction of external forces. In the learning problem formulation by <PERSON><PERSON><PERSON> et al. (2024a) , the GNN-based simulators receive as node inputs a time sequence of the H most recent historic velocities stacked to u k-H:k = [u k-H , ...u k ] and an optional external force vector. Consequently, the GNN-based simulators are confronted with the underlying instantaneous force and not the effective force, i.e., the force that acts on the particles upon temporal coarsening. We make two observations:", "section": "Neural SPH", "sec_num": "3."}, {"text": "1. The impact of the external force g is already included in the dynamics given by the past velocities u k-H:k . Thus, providing a constant force vector, i.e., gravitational force, as model input might be necessary when training equivariant models, but as <PERSON> et al. (2020) show in their appendix C2, the GNS model does not improve when external force information is added. However, in the general case of systems with spatially varying forces, having force vectors as inputs is crucial. An example is the reverse Poiseuille flow, which has a positive force in x direction when y > 1 and a negative force when y < 1 (see Appendix D).", "section": "Neural SPH", "sec_num": "3."}, {"text": "2. By predicting the full acceleration a, the GNN-based simulators are forced to model gravity implicitly. One might argue that gravity is just a bias term in the last decoder layer, and thus, a GNN-based simulator should be able to model gravitational effects quite easily. However, we observe that for a GNS model trained on dam break (see Fig. 1 top part), the bias term in the last layer is more than an order of magnitude smaller than the respective gravitational acceleration.", "section": "Neural SPH", "sec_num": "3."}, {"text": "Especially the latter observation indicates that GNN-based simulators indeed mainly learn velocity correlations as suggested by <PERSON><PERSON><PERSON><PERSON> et al. (2022) . Referring to the structure of Eq. ( 2), and motivated by operator splitting, we suggest to bracket terms on the right-hand side of this equation as [...] + g. If considering temporal coarsening of GNN-based simulators over M SPH steps, and given that the dataset is generated by running an SPH simulation with a constant time step ∆t SP H , the steps over which the GNN-based simulator integrates are M ∆t SP H . In the case of a constant force g, this leads to an effective external force after M SPH steps of g F D M = (M ∆t SP H ) 2 g, as by double integration of acceleration to positions with a finite difference time step ∆t F D = 1, see Appendix F. Thus, when removing the accumulated external force from the full acceleration, i.e.,", "section": "Neural SPH", "sec_num": "3."}, {"text": "EQUATION", "section": "Neural SPH", "sec_num": "3."}, {"text": "the model is forced to disentangle the interactions between external forces and internal dynamics, i.e., the other two terms on the right-hand side of Eq. ( 2). We attain a powerful formulation of the learning problem since the dynamics are controlled more explicitly, as shown in Fig. 1 and in Figs. 6 to 9 of Appendix A.", "section": "Neural SPH", "sec_num": "3."}, {"text": "However, if the force g varies over space or time, it cannot be independently integrated over M time steps. In this case, modeling the correct effective external force requires (i) precise information on the forces that act on a given particle over each of the M steps we want to coarse-grain over, and (ii) taking the average over these contributions, i.e., g", "section": "Neural SPH", "sec_num": "3."}, {"text": "F D M = (M ∆t SP H ) 2 1 M M m=1 g m .", "section": "Neural SPH", "sec_num": "3."}, {"text": "Since we typically do not have access to such information, we propose a convolution-based solution. In the case of a spatially varying but constant in time force field, we use the standard deviation of velocities over the dataset σ u as a proxy of how much a particle moves perpendicularly to the force field, as this perpendicular motion is what we want to smoothen for. We then convolve the force function with a Gaussian distribution N (0, σ 2 u ) with the standard deviation σ u and thus smoothen the force function to account for the effective force exerted on a particle that moves across regions with variable forcing. This convolution can be implemented in two ways: (i) If the function is simple enough, i.e., an analytical solution exists, we can use it directly. (ii) Alternatively, we may evaluate the instantaneous external force at the current particle coordinates and then apply an SPH kernel convolution, which is very similar to a convolution with a Gaussian, except that it has compact support. Applying a kernel W (r|h) with h = σ u enables us to effectively smoothen any given force function. As a side remark, applying a convolution with an SPH kernel W (•|h) of a particular h over the mass of each adjacent particle is exactly what density summation does.", "section": "Neural SPH", "sec_num": "3."}, {"text": "In order to correct the pathological particle clustering of learned GNN-based simulators, we add an intermediate step during the rollout of a learned Lagrangian solver, namely an SPH relaxation step. The idea is that if the learned solver pushes the system to an unphysical particle configuration, we can reduce density fluctuations by running an SPH relaxation simulation of up to 5 steps. By SPH relaxation, we refer to the process of taking the point cloud right after the temporal update of the learned model, and then -solely based on the particle coordinates -applying an SPH update with the assumption of zero initial velocities (<PERSON><PERSON><PERSON><PERSON> et al., 2015; <PERSON> et al., 2024) . We can apply SPH relaxation using the pressure term in Eq. (2) or the viscous term in Eq. (2). One update step of relaxation corresponds to", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "EQUATION", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "EQUATION", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "where we hide the time step and the pre-factors in the hyperparameters α and β. Adding and fine-tuning these hy-perparameters is essential for various reasons: (a) in SPH, it proves challenging to identify a reference velocity, which is needed for determining the time step size; (b) adhering to the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (CFL) condition (<PERSON><PERSON><PERSON> et al., 1928) would most certainly result in smaller time steps, and most importantly, (c) the step size is implicitly determined by how much the GNN-based simulator distorts the system. This largest distortion depends on many factors, such as temporal coarsening steps M and the choice of the GNN-based simulator. We propose fine-tuning these hyperparameters as shown later in this section.", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "Correction of density at walls and free surfaces. Recall that also existing SPH methods encounter challenges when predicting the density at free surfaces. On the one hand, density summation, which is the preferred method for density computation due to implicit mass conservation, is not directly applicable to free surfaces since it encounters density inconsistencies. On the other hand, density-transport equations abandon exact mass conservation.", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "For GNN-based simulators, we propose a novel way of estimating the density of a system at free surfaces. Our approach combines the SPH requirement that density fluctuations should not exceed ∼ 1% -which we round up to 2% -with density summation. We extend density summation by Our approach is closely related to cavitation modeling, where it is common to use tensile instability control (TIC) (<PERSON> et al., 2018) to avoid negative pressure values that increase the particle disorder and eventually lead to the occurrence of particle clustering and clumping (<PERSON><PERSON> et al., 2022) . The main idea of TIC is to change the pressure gradient formulation according to the particle location, e.g., at a free surface, and the sign of its pressure value (<PERSON> et al., 2018) . With this novel density computation routine, we can easily work with wall discretizations consisting of one wall layer, whereas standard SPH typically requires three or more wall layers (<PERSON><PERSON> et al., 2012) . To complete the discussion on wall boundaries, we use the generalized wall boundary condition approach by <PERSON><PERSON> et al. (2012) to enforce the impermeability of the walls.", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "SPH Relaxation parameter tuning. We propose a threestep parameter-tuning process for the SPH relaxation parameters (see Appendix G.2 for examples):", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "1. Tune α while number of relaxation steps l = 1 and β = 0. Typically, α ∈ (0.005, 0.05).", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "2. Tune l with optimal α and β = 0. Typically, l ∈ (1, 5).", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "3. Tune β with optimal α and l. Typically, β ∈ (0.1, 1).", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "The measures we use while tuning are the position MSE, Sinkhorn divergence, kinetic energy MSE, MAE of density deviation from the reference ρ ref , Dirichlet energy (<PERSON> & <PERSON>, 2005) of the density field, and <PERSON><PERSON>fer distance, see Appendix G for more details.", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "Related work. We want to stress that except for the proposed treatment of external forces, our method does not require retraining the GNN-based simulator. This differentiates our work from an orthogonal line of research, which has experienced a surge in recent years, namely using differentiable solvers as part of the machine learning model (<PERSON> et al., 2020) . On the spectrum of classical numerical solvers to black-box end-to-end ML models, one also finds the class of hybrid models, which are ML models utilizing algorithmic ideas from classical solvers (<PERSON><PERSON><PERSON> et al., 2023b; <PERSON><PERSON> & <PERSON>, 2022; <PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2021; <PERSON>, 2022; <PERSON> et al., 2022b) . Yet, all of these approaches construct a neural network that needs to be trained, whereas our SPH relaxation happens only during inference.", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "Conceptually closest to our work is the recent PDE-Refiner model class (<PERSON><PERSON> et al., 2024) . PDE-Refiner draws inspiration from diffusion models to apply a small number of refinement steps on learned Eulerian solvers. The refinement steps substantially improve the modeling of high frequency components, which yields more stable long-term predictions and better physics modeling, at the cost of increased inference time and a dedicated training routine. We point out that because PDE-Refiner is designed for Eulerian systems, it does not have the notion of dynamic particle coordinates underlying Lagrangian methods. Thus, extending PDE-Refiner to the Lagrangian description is not trivial, as one could choose to refine the accelerations or velocities or directly the particle coordinates, and such investigations are beyond the scope of this work. Furthermore, for particle systems, we do not have efficient ways to accurately evaluate high spatial frequencies over point clouds akin to the FFT on grids, and additionally, the physical setup of our problems does not involve high spatial frequencies.", "section": "Correction of particle distribution via SPH relaxation.", "sec_num": null}, {"text": "Our analyses are based on the datasets of <PERSON><PERSON><PERSON> & <PERSON> (2024) , accompanying the LagrangeBench paper (<PERSON><PERSON><PERSON> et al., 2024a) . These datasets represent challenging coarsegrained temporal dynamics and contain long trajectories, i.e., up to thousands of steps. We test the performance difference of two popular GNN-based simulators when: (i) external forces are removed from the model target (□ g ), (ii) an SPH relaxation with pressure term is applied (□ p ), and (iii) an SPH relaxation with viscous term is applied (□ ν ).", "section": "Experiments", "sec_num": "4."}, {"text": "GNN-based simulators. The Graph Network-based Simulator (GNS) model (<PERSON> et al., 2020 ) is a popular learned surrogate for physical particle-based simulations and our main model. The architecture is kept simple, based on the encoder-processor-decoder principle, where the processor consists of multiple graph network blocks (<PERSON><PERSON><PERSON> et al., 2018) . Our second model, the Steerable E(3)-equivariant Graph Neural Network (SEGNN) (<PERSON><PERSON><PERSON> et al., 2022a ) is a general implementation of an E(3) equivariant GNN, where layers are directly conditioned on steerable attributes for both nodes and edges. The main building block is the steerable MLP, i.e., a stack of learnable linear Clebsch-Gordan tensor products interleaved with gated non-linearities (<PERSON><PERSON> et al., 2018) . SEGNN layers are message-passing layers (<PERSON><PERSON> et al., 2017) where steerable MLPs replace the traditional non-equivariant MLPs for both message and node update functions. These two models were chosen as they present the current state-of-the-art surrogates for Lagrangian fluid dynamics (<PERSON><PERSON><PERSON> et al., 2024a) , and also because they are representative of two fundamentally different classes of GNNs: non-equivariant (GNS) and equivariant (SEGNN).", "section": "Experiments", "sec_num": "4."}, {"text": "Implementation of SPH relaxation. In our experience, it suffices to perform the relaxation operation for 1-5 iterations (l), depending on the problem. We summarize the used hyperparameters in Table 3 and Appendix B. Given that the learned surrogate is trained on every 100th SPH step, these additional SPH relaxation steps only marginally increase the rollout time -by a factor of 1.05-1.15 per relaxation step for a 10-layer 128-dimensional GNS model simulating the 2D RPF case, see Table 4 and Appendix E. In the same table, we observe an increase in runtime for 3D RPF and GNS-10-128 of roughly 1.4x per relaxation step, but we believe that this comes from the much more compute-intense neighbor search, which is reevaluated at every relaxation step. However, as the relaxation does not need to be implemented in a differentiable framework (we currently adopt JAX-SPH (<PERSON><PERSON><PERSON> et al., 2024b)), more efficient implementations, e.g. in C++, can significantly reduce these runtimes. For more compute-intense models like SEGNN the slowdown factor reduces, as the relaxation has a fixed computational cost independent of the particular GNN model.", "section": "Experiments", "sec_num": "4."}, {"text": "Most of the computational overhead of the relaxation is due to its neighbor list, which has significantly more edges than the default neighbor list of the GNN-based simulators. The GNN graph generation uses the default radial cutoff distance from LagrangeBench, which corresponds to roughly 1.5 average particle distances. In contrast, the SPH relaxation uses the Quintic spline kernel with a cutoff of 3 average particle distances, i.e., the SPH relaxation operates on 2 d more edges, with dimension d ∈ {2, 3}. Therefore, our approach can be regarded as a multiscale approach, similar to the learned multi-scale interatomic potential presented by (<PERSON> et al., 2023a) . The difference is that in our approach, only the part using the smaller cutoff is a neural network, and the longer-range interactions simply stabilize the system in terms of better density distributions.", "section": "Experiments", "sec_num": "4."}, {"text": "Training with SPH relaxation. An appealing idea is to use the SPH relaxation as a regularization during training, in the hope that we can omit running relaxations at inference time.", "section": "Experiments", "sec_num": "4."}, {"text": "We tried various ways of implementing this idea, but none of them improved rollout performance, see Appendix H.", "section": "Experiments", "sec_num": "4."}, {"text": "Overview of results. Our results on 400-step rollouts using the GNS model are summarized in Table 1 and are averaged over all test trajectories and over the trajectory length. See Table 2 for the SEGNN results. As error measures, we use (a) the mean-squared error of positions (MSE 400 ), (b) the Sin<PERSON>orn divergence, which quantifies the conservation of the particle distribution, and (c) the kinetic energy error (MSE Ekin ) as a global measure of the physical behavior. The viscous term is shown only for reverse Poiseuille flow because it did not improve the performance on the other datasets. We note that by splitting the test sets into sequences of length 400, we obtain only 12-25 test trajectories, leading to noisy performance estimates. We discuss the necessity for larger datasets later in this section. For various parameter ablations, the evolution of error metrics with error bounds, and three more error metrics (density MAE, Dirichlet energy, and Chamfer distance), see Appendix G.", "section": "Experiments", "sec_num": "4."}, {"text": "Overall, all Neural SPH-enhanced simulators achieve better performance than the baseline GNNs, often by orders of magnitude, allowing for significantly longer rollouts and significantly better physics modeling.", "section": "Experiments", "sec_num": "4."}, {"text": "Note on error thresholds. We note that upon tuning the parameters of our method, it either improves performance or converges to the baseline, with the latter being what mainly happens to RPF 3D according to Appendix B. We hypothesize that the baseline already produces very good particle distributions, and there is little potential for improvement. It thus seems necessary to define a threshold of when a learned simulator performs well enough in the sense of the requirements of the downstream task of interest. We refer to physical thresholds like the chemical accuracy in computational chemistry or the energy and forces within threshold measure used in the Open Catalyst project (<PERSON><PERSON><PERSON> et al., 2021) , both of which are designed to quantify whether a computational model is useful for practical applications. We stress the importance and leave the derivations of such thresholds for Lagrangian fluid simulations to future work.", "section": "Experiments", "sec_num": "4."}, {"text": "In this section, we study the influence of the proposed external force treatment without combining it with the SPH relaxation. As only the dam break and reverse Poiseuille flow datasets have external force features, we focus on them. Dam break (DAM). We saw a major performance boost on dam break when removing external forces from the target (GNS g ), see Table 1 and Appendix Force smoothing in reverse Poiseuille Flow. The external force of the reverse Poiseuille flow datasets is provided as a function corresponding to the instantaneous force, but when we train towards the effective dynamics over multiple original solver steps, we need to adjust this force. In particular, when predicting the dynamics over M = 100 temporal coarse-graining steps provided by LagrangeBench, an RPF particle might jump back and forth across the boundary separating the left-and right-ward forcing. Thus, it is not possible to infer the aggregated external force directly only knowing the particle coordinates at step M . We, therefore, apply a convolution of a Gaussian function with the force function. Since the forcing in RPF is a step function, this specific convolution has an analytical solution, i.e., the error function erf(•). We use erf(•) as a replacement for the original force function. See Appendix D for more details and visualization of the force before and after the convolution.", "section": "External Force Treatment", "sec_num": "4.1."}, {"text": "Reverse Poiseuille flow (RPF). See Fig. 3 for a subset of our ablation results on RPF 2D with GNS-10-128, or the full results on RPF 2D/3D and GNS/SEGNN in Appendix G.3. When removing external forces from the target of the GNS model (GNS g ), we observed that using the original, i.e., not smoothed, force leads to highly unstable dynamics in the shearing region, which causes the failure of the dynamics after less than 50 steps, see GNS graw in Figs. 27 and 28 .", "section": "External Force Treatment", "sec_num": "4.1."}, {"text": "When switching to the smoothed force function, the system becomes much more stable to perturbations and significantly improves the kinetic energy error. It is important to note that the kinetic energy is paramount to RPF, as this physical system is characterized by constant kinetic energy up to small fluctuations.", "section": "External Force Treatment", "sec_num": "4.1."}, {"text": "Looking at the 20-step position MSE reported in La-grangeBench, the GNS g training leads to worse performance, roughly by a factor of 1.5 (see the beginning of the evolution in Fig. 3 ). This is important to note because we trade off worse short-term behavior in favor of better long-rollout performance, with the latter being the practical use-case we target. In this context, the LagrangeBench datasets pre-define a split of 50/25/25, which is far from sufficient if we want stable error estimates on rollouts of 400-step length, as also discussed, e.g., in Fu et al. (2023b) . ", "section": "External Force Treatment", "sec_num": "4.1."}, {"text": "This section presents the results of our SPH relaxation on its own, and also in combination with the proposed external force treatment. We divide the discussion based on common characteristics of the datasets into periodic boundary cases, cases with wall boundaries, and free surface problems.", "section": "SPH Relaxation", "sec_num": "4.2."}, {"text": "Taylor-Green vortex (TGV). We did not expect the SPH relaxation to be very beneficial to the Taylor-Green vortex cases because (a) the trajectories are rather short with 125 and 60 steps in the 2D and 3D cases, respectively, and also (b) TGV represents a decaying problem, making it less prone to clustering in later stages of the trajectory. But according to Table 1 , we get a consistent improvement of the position error MSE 400 of ∼ 5% and significant Sinkhorn divergence improvements on the 2D and 3D datasets.", "section": "PERIODIC BOUNDARIES", "sec_num": "4.2.1."}, {"text": "Viscous term. In addition to external force subtraction, we found it beneficial to use the pressure (p) and viscous (ν) terms during relaxation, termed GNS p,ν . Viscosity, which manifests itself in shearing forces, in general, refers to the idea that if two fluid elements are close to each other but move in opposite directions, then they should both decelerate. Thus, to apply viscosity, we need to again approximate velocities by finite differences between consecutive positions of particles.", "section": "PERIODIC BOUNDARIES", "sec_num": "4.2.1."}, {"text": "Reverse Poiseuille flow (RPF). In Figs. 4 and 10, we show histograms over velocity magnitudes to quantify how the different RPF correction terms impact the dynamics. Firstly, the original GNS model loses its high-velocity components over time, resembling a diffusion process, which makes it more stable with respect to perturbations, but, at the same time, leads to wrong kinetic energy. Secondly, simply changing the training objective by removing the external force (see GNS g ) already mitigates the problem of missing high velocities. And by adding the viscous term, which is especially relevant in the shearing region, to the pressure gradient term, we almost perfectly recover the target velocity distribution. See Fig. 3 and Appendix G.3 for further details. discussed yet is how to ensure that particles do not escape the computational domain by passing through the walls. We use the simple and effective approach laid out in the generalized wall boundary condition paper by <PERSON><PERSON> et al. (2012) . The idea of this approach is to enforce the impermeability of the walls by setting the pressure of the dummy wall particles to the average pressure of their adjacent fluid neighbors, see Eq. ( 27) in <PERSON><PERSON> et al. (2012) , and, thus, constructing a setting of zero pressure gradients normal to the walls. With this boundary condition implementation, we obtain the following one-step relaxation algorithm: 1. density computation for fluid particles, 2. pressure computation for fluid particles through the equation of state, 3. computation of pressure of wall particles via weighted summation over the pressure of adjacent fluid particles, and 4. evaluation of the pressure gradient term, which gives the forces used to integrate the momentum equation Eq. ( 5) through Eq. ( 6).", "section": "PERIODIC BOUNDARIES", "sec_num": "4.2.1."}, {"text": "Lid-driven cavity (LDC). In the lid-driven cavity example, we observe that the learned model pushes particles away from the fast-moving lid into the lower half of the domain, which has profound consequences. On the one hand, the pressure at the bottom increases to an extent such that one or more particles gradually pass through the bottom wall.", "section": "PERIODIC BOUNDARIES", "sec_num": "4.2.1."}, {"text": "On the other hand, since too few particles reside close to the lid, the shearing forces are underrepresented, yielding a loss of kinetic energy, i.e., dynamics are lost. We fix both these issues with an SPH relaxation, forcing particles to be homogeneously distributed within the domain, see Figs. 5 and 8 . See Appendix G.2 for various hyperparameter sensitivity ablations on LDC 2D/3D and GNS/SEGNN. While tuning the parameters is crucial, once tuned, they seem to work fairly reliably.", "section": "PERIODIC BOUNDARIES", "sec_num": "4.2.1."}, {"text": "A major difference between dam break and the other datasets we benchmark is that in dam break we not only care about the particle distribution within the fluid, but also about the volume filled with fluid. The latter is the focus of this section, and it is reflected in the MSE 400 and Sinkhorn divergence measures, but not in MSE Ekin .", "section": "FREE SURFACES", "sec_num": "4.2.3."}, {"text": "Dam break (DAM). Interestingly, by either our external force treatment or the SPH relaxation, we seem to fix the problem of the fan-like spreading of the wavefront. We interpret this as a confirmation that the reason for this failure mode is the high compression at the tip. However, fixing the high compression levels in the bulk fluid requires our SPH relaxation, which we run with as few as three steps.", "section": "FREE SURFACES", "sec_num": "4.2.3."}, {"text": "The GNS g,p setup then recovers the correct dynamics with a significantly higher precision as measured by the Sinkhorn divergence, but also the kinetic energy MSE, indicating that the fluid also evolves more physically. Regarding the fluid surface, if we carefully look at the height of the fluid in Figs. 6 to 9, we see that the GNS g,p case very closely resembles the ground truth. See Appendix G.1 for ablations.", "section": "FREE SURFACES", "sec_num": "4.2.3."}, {"text": "We applied the same external force treatment and SPH relaxations to the SEGNN model (<PERSON><PERSON><PERSON> et al., 2022a) without further tuning of the Neural SPH hyperparameters (see Appendix B) and summarize the results in Table 2 . This comparison is useful not only for better comparability but also to show that proper SPH relaxation often depends more on the dataset than on the model -for example, moving the external force out of the 2D RPF case results in a 40 times lower kinetic energy error. However, in some cases, the GNS and SEGNN models behave quite differently. In most cases, SEGNN performs on par with GNS on long trajectories, with the notable SEGNN blowups on LDC 2D, DAM 2D, and RPF 3D. In particular, when we change the treatment of the external force in dam break without applying additional wall boundary conditions, we observe many particles falling through the bottom wall around step 200.", "section": "SEGNN Results", "sec_num": "4.3."}, {"text": "Adding the relaxation with wall boundary conditions solves this problem, but investigating the qualitative differences between GNS and SEGNN would be an interesting future work. See Appendix G for our hyperparameter ablations.", "section": "SEGNN Results", "sec_num": "4.3."}, {"text": "We introduce Neural SPH, a framework for improved training and inference of GNN-based simulators for Lagrangian fluid dynamics simulations. We demonstrate the utility of our toolkit on seven diverse 2D and 3D datasets and on two state-of-the-art GNN-based simulators, GNS and SEGNN. We identify particle clustering originating from tensile in- stabilities as one of the primary pitfalls of GNN-based simulators. Through the proposed external force treatment and SPH relaxation step, distribution-induced errors are minimized, leading to more robust and physically consistent dynamics. Compared to other methods, Neural SPH does not require a differentiable solver and increases the inference time only by a fixed and rather small amount.", "section": "Concluding Remarks", "sec_num": "5."}, {"text": "Limitations and future work. We observe that tuning the hyperparameters of the particle relaxation is crucial since redistributing the particles inherently translates to modified velocity histories, which directly enter the next autoregressive update step. Thus, the learned solver may become unstable by bringing the past velocities out-of-distribution. Although using the proposed hyperparameter tuning recipe leads to a fairly stable inference routine of the learned solvers, further improving this recipe might be beneficial. Another potential limitation concerns the handling of external forces, namely, that information on the timestep and coarsening level of the dataset is required. Finally, and related to the parameter tuning, we point out the necessity of defining physical thresholds akin to the energy and force within threshold by (<PERSON><PERSON><PERSON> et al., 2021) , to identify whether our Neural SPH improvements are needed in the first place. Our work shows what is possible by integrating machine learning models with established simulation routines like enforcing boundary conditions or improving particle spreading, but one can extend this idea by adding arbitrarily many terms from the enormous body of literature on classical numerics. We point out that the proposed alternation of learned and classical solver terms is a framework, applicable to any combination of compatible methods, extending beyond GNNs and Lagrangian systems.", "section": "Concluding Remarks", "sec_num": "5."}, {"text": "Smoothed particle hydrodynamics plays a crucial role in computational fluid dynamics. Examples can be found in aerodynamics, astrophysics, or plasma physics. Given the widespread application of computational fluid dynamics, obtaining shortcuts or alternatives for computationally expensive simulations is essential for advancing scientific research, and has direct or indirect implications for reducing our carbon footprint. However, it is important to note that relying on simulations always necessitates thorough cross-checks and monitoring, especially when employing a \"learning to simulate\" methodology.", "section": "Impact Statement", "sec_num": null}, {"text": "In this section, we show some more examples of dam break trajectories. Roughly one-third of GNS trajectories have the same artifacts at step 80 as test trajectory 0 (see Figs. 6 and 7 ). Roughly half of the GNS trajectories show large amounts of particles leaving the box on the right at step 80 (see Fig. 8 ). Only a few GNS simulations behave better at step 80 (see Fig. 9 ).", "section": "<PERSON>. <PERSON> Break Plots", "sec_num": null}, {"text": "GNS", "section": "<PERSON>. <PERSON> Break Plots", "sec_num": null}, {"text": "1.0 1.1 1.2 1.3 1.0 1.1 1.2 1.3 GNS g 1.0 1.1 1.2 1.3 1.0 1.1 1.2 1.3 GNS g,p 1.0 1.1 1.2 1.3 1.0 1.1 1.2 1.3 SPH 1.0 1.1 1.2 1.3 1.0 1.1 1.2 1.3", "section": "<PERSON>. <PERSON> Break Plots", "sec_num": null}, {"text": "Step 80", "section": "<PERSON>. <PERSON> Break Plots", "sec_num": null}, {"text": "Step 240 Step 80", "section": "<PERSON>. <PERSON> Break Plots", "sec_num": null}, {"text": "Step 240 Step 80", "section": "<PERSON>. <PERSON> Break Plots", "sec_num": null}, {"text": "Step 240 Step 80", "section": "<PERSON>. <PERSON> Break Plots", "sec_num": null}, {"text": "Step 240 We first convert these two standard deviation vectors to their isotropic versions, assuming that the velocity components are independent Gaussian random variables, i.e., using the quadratic mean. This leads to σ 2D = 0.025 and σ 3D = 0.043. We round the numbers and use the values σ 2D = 0.025 and σ 3D = 0.05 in our experiments. The result of this smoothing procedure can be seen in Fig. 11 . ", "section": "<PERSON>. <PERSON> Break Plots", "sec_num": null}, {"text": "We measured the inference speed of GNS-10-128 and SEGNN-10-64 on the 2D and 3D reverse Poiseuille flow datasets with 0, 1, 3, or 5 relaxation steps l and summarize the results in ", "section": "E. Inference Speed", "sec_num": null}, {"text": "Semi-implicit Euler:", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "= p 0 + ∆t2u 0 + ∆t 2 (2a 0 + a 1 ) (16) . . .", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "If a m is a constant number, we can simplify the last part to:", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "If we now compute the target effective acceleration by finite differences of positions, we end up with", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": ")", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "By substituting the semi-implicit <PERSON><PERSON><PERSON> rule after <PERSON> steps into this finite differences approximation and setting ∆t F D = 1 for simplicity, we get an effective acceleration of", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>ening", "sec_num": null}, {"text": "We extend the results from the main paper by running multiple ablation studies mainly focusing on (a) the individual and combined impact of SPH relaxation and force treatment on the example of dam break, (b) the sensitivity of the parameters governing the proposed SPH relaxation on the example of lid-driven cavity, and (c) the impact of smoothing the external force function on the example of the reverse Poiseuille flow datasets. We believe that this exhaustive analysis of the hyperparameters is essential for practitioners who would consider using our proposed methods. To increase the value of the analysis we add (A) the evolution of the metrics over the simulation length, (B) error bars representing the 0.25 and 0.75 quantiles over the test trajectories, and (C) three more metrics compared to the main paper. The six metrics we use are:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "1. MSE 400 -position MSE over 400 steps.", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "2. MSE Ekin -kinetic energy MSE between the predicted and ground truth frames.", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "3. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> divergence between the particle distribution of predicted and ground truth frames. Measures how much effort it would take to move the particle mass between the two states. Scales as O(N 2 ) with the number of particles N and is more compute intense than the model inference on all our datasets. For all these measures applies: lower is better, and 0.0 is best.", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "We compare the impact of our external force treatment (□ g ), our SPH relaxation with parameters from Table 3 (□ p ), and combination of both (□ g,p ) on the dam break dataset using the GNS (Fig. 12 ) and SEGNN (Fig. 13 ). On the MSE Ekin , we see that only through the combination of our force treatment and SPH relaxation we achieve significant performance boosts with both the GNS and SEGNN models. ", "section": "G.1. Dam Break", "sec_num": null}, {"text": "S E G N N S E G N N g S E G N N p S E G N N g ,p 10 4 10 3 10 2 10 1 MAE S E G N N S E G N N g S E G N N p S E G N N g ,p 10 0 10 1 10 2 Dirichlet S E G N N S E G N N g S E G N N p S E G N N g ,p", "section": "G.1. Dam Break", "sec_num": null}, {"text": "We investigate the influence of the relaxation hyperparameters α and β from Eq. ( 5) and the number of relaxation steps/loops. The evolution of the six error measures over the 400 steps is shown on the left, and the average for each hyperparameter configuration is shown on the right. Intervals indicate the 0.25 and 0.75 quantiles over the 12 test trajectories (left) and the average of those values over the 400 steps (right).", "section": "G.2. Lid-<PERSON><PERSON>", "sec_num": null}, {"text": "Based on Fig. 14 , we choose α = 0.03 as beyond this value, the Dirichlet energy starts increasing, indicating instabilities. In Fig. 15 , we see on M SE 400 and M SE Ekin that beyond 5 iterations the accuracy drops, so we choose l = 5 loops. In Fig. 16 , we do not see performance gains using the viscous term, so we decide not to use it. ", "section": "G.2.1. LDC 2D WITH GNS", "sec_num": null}, {"text": "We again stress that the relaxation hyperparameters were optimized on GNS and we only ablate their influence on the performance of SEGNN. But we indeed observe similar behavior between GNS and SEGNN. We do stress the dramatic improvement in performance upon 5 and more relaxation steps visible in Fig. 18 . In contrast to GNS, we do see positive impact of the viscous term on SEGNN, and would recommend using β = 0.5, see Fig. 19 . ", "section": "G.2.2. LDC 2D WITH SEGNN", "sec_num": null}, {"text": "These plots agree with our choice of hyperparameters from Table 3 and show the sensitivity with respect to the relaxation parameters. ", "section": "G.2.3. LDC 3D WITH GNS", "sec_num": null}, {"text": "We compare all variants of RPF model from the main paper with the case of not smoothing the external force, denoted □ graw . The main message with regard to excluding the external force from the training target (all methods with □ g ) is that not smoothing the force function when it has discontinuities leads to highly unstable models, see <PERSON><PERSON> E<PERSON> in Figs. 27 and 28. It is probably a matter of too few test trajectories that we do not observe such blow-ups in Figs. 26 and 29 . ", "section": "G.3. <PERSON><PERSON>", "sec_num": null}, {"text": "We also explored to idea of incorporating the SPH relaxation during training, hoping that the learned model can be regularized toward predicting better particle distributions, which could make the SPH relaxation during inference unnecessary. We explored two degrees of freedom when training a GNS-10-128 model on the 2D LDC dataset: (a) dependence on the relaxation parameter α, and (b) performance when trained with relaxation but evaluated with or without it.", "section": "<PERSON>. Training with Relaxations", "sec_num": null}, {"text": "Basic setup. We remind the reader that according to Table 3 , the optimal relaxation parameters on 2D LDC are α = 0.03 and 5 relaxation steps, but from the ablation in Fig. 14 , we see that even one relaxation step significantly improves the dynamics. Thus, for simplicity, we use α = 0.03 with 1 relaxation step for our training with relaxation. We implemented this training scheme by adding the relaxation to every forward call of the model, i.e. when pushforward is applied, the relaxation is executed at every pushforward step.", "section": "<PERSON>. Training with Relaxations", "sec_num": null}, {"text": "Training with \"negative\" relaxation. One highly appealing idea is to train the model with what we call \"negative\" relaxation, i.e. flipping the sign of the relaxation term by setting α to a negative value, by which the model would learn to over-correct unfavorable distributions. However, the results for α < 0 in Fig. 30 are rather discouraging.", "section": "<PERSON>. Training with Relaxations", "sec_num": null}, {"text": "Training and inference with relaxation. Similar to subtracting the external force from the learning target, which we discussed in length and seems very useful, we investigated how the model would perform when it can predict an even worse particle distribution, which is then corrected through a relaxation both during training and inference, see α > 0 in Fig. 31 .", "section": "<PERSON>. Training with Relaxations", "sec_num": null}, {"text": "But also here, we get worse results than only applying relaxation during inference. In addition, training with relaxation requires separate retraining until α is tuned, which is not the case with our inference time relaxation. ", "section": "<PERSON>. Training with Relaxations", "sec_num": null}, {"text": "Table3. SPH relaxation hyperparameters used in our experiments. These hyperparameters were tuned on the GNS-10-128 model.", "section": "", "sec_num": null}], "back_matter": [{"text": "The authors thank <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> for helpful discussions on SPH at free surfaces.", "section": "Acknowledgements", "sec_num": null}, {"text": "<PERSON><PERSON>T. conceived the ideas of SPH relaxation and the proposed external force treatment, implemented them, ran the experiments, and wrote the first version of the manuscript. J.E. contributed the Dirichlet energy metric and wrote the literature review on density summation at free surfaces. N.A. and J.B. supervised the project from conception to design of experiments and analysis of the results. All authors contributed to the manuscript. ", "section": "Author Contributions", "sec_num": null}, {"text": "The forcing step function of the reverse Poiseuille flow (RPF) is given by:For the two-dimensional case, the z value can be ignored. We use the analytical solution of the convolution of the forcing step function with a Gaussian kernel of width that corresponds to the standard deviation of the velocities over the dataset. In this special case, the convolution has an analytical solution given by the error function erf. For the jump in the middle, we obtain the solutionWe use the finite difference approximation between consecutive coordinate frames to approximate the standard deviation of the velocity. For 2D RPF, the velocity standard deviation is [0.036, 0.00069], and for 3D RPF [0.074, 0.0014, 0.0011].", "section": "<PERSON>. Forcing of Reverse Poiseuille Flow", "sec_num": null}], "ref_entries": {"FIGREF0": {"text": "Figure 1. Neural SPH improves Lagrangian fluid dynamics, showcased by physics modeling of the 2D dam break example after 80 rollout steps. Different models exhibit different physics behaviors. From top to bottom: GNS (<PERSON> et al., 2020), GNS with corrected force only (GNSg), full SPH enhanced GNS (GNSg,p), and the ground truth SPH simulation. The colors correspond to the density deviation from the reference density; the system is considered physical within 0.98-1.02.", "fig_num": "1", "uris": null, "num": null, "type_str": "figure"}, "FIGREF1": {"text": "(a) novel external force treatment during training, and (b) an additional SPH relaxation routine during inference.", "fig_num": null, "uris": null, "num": null, "type_str": "figure"}, "FIGREF2": {"text": "Pathological particle clustering during long rollouts for GNN-based simulators. The entering point to our analysis is the realization that simulated rollouts of a learned Graph Network-based Simulators (GNS) (<PERSON> et al., 2020) severely violate the 1% compressibility requirement present in weakly compressible SPH methods -see top part of Fig. 1. This figure shows compression of as much as 1.4 • ρ ref in the left part, which is not only unphysical regarding the density itself but might also lead to unphysical dynamics in the sense of periodic compressions and expansions later in the rollout, see Section 4. The violation -although much worse -resembles pressure inaccuracies in classical numerical SPH solvers.", "fig_num": null, "uris": null, "num": null, "type_str": "figure"}, "FIGREF3": {"text": "Figure 2. Number of neighbors mismatch due to particle clustering. Histogram of the number of neighbors of the 2D lid-driven cavity experiment after 400 rollout steps (average over all test rollouts).", "fig_num": "2", "uris": null, "num": null, "type_str": "figure"}, "FIGREF4": {"text": "(a) setting all values < 0.98ρ ref to ρ ref , and (b) clipping all values > 1.02ρ ref , i.e. setting them to 1.02ρ ref . Modification (a) guarantees that particles at free surfaces are set to the reference condition, preventing surface instabilities. Modification (b) truncates large outliers akin to gradient clipping when training a neural network, stabilizing the relaxation dynamics.", "fig_num": null, "uris": null, "num": null, "type_str": "figure"}, "FIGREF5": {"text": "G.1. This simple modification of the training objective improves all considered measures by at least a factor of 2 and by as much as a factor of 5 on a rollout of the full dam break trajectory, i.e., 400 steps. For up to 20-step rollouts, GNS g training does not improve the position error, which is in accordance with<PERSON><PERSON><PERSON><PERSON> et al. (2020) and their Fig.C1. However, as the simulation length goes beyond 50 steps, numerical errors quickly accumulate and lead to artifacts like the one visible in the top part of Fig.1. This particular failure mode in the front part of the dam break wave develops by first compressing the fluid to as much as 1.5ρ ref , and then the smallest instability in the tip causes particles to detach from the free surface. From there on, GNS starts acting as if the right wall has already been reached and fails to model the double wave structure from the reference solution, see Appendix A.", "fig_num": null, "uris": null, "num": null, "type_str": "figure"}, "FIGREF6": {"text": "Figure 3. Ablations on RPF 2D with GNS-10-128 over the simulation length. Adapted from Fig. 26 in Appendix G.3.", "fig_num": "3", "uris": null, "num": null, "type_str": "figure"}, "FIGREF7": {"text": "Figure 4. Velocity magnitudes histogram of 2D reverse Poiseuille flow after 400 rollout steps (averaged over all rollouts). Our GNSg,p,ν matches the ground truth distribution of SPH.", "fig_num": "45", "uris": null, "num": null, "type_str": "figure"}, "FIGREF8": {"text": "Figure 6. Dam break steps 80 and 240 of test rollout 0. Extends Fig. 1.", "fig_num": "6", "uris": null, "num": null, "type_str": "figure"}, "FIGREF9": {"text": "Figure 7. Dam break steps 80 and 240 of test rollout 13.", "fig_num": "7", "uris": null, "num": null, "type_str": "figure"}, "FIGREF10": {"text": "Figure 8. Dam break steps 80 and 240 of test rollout 14.", "fig_num": "8", "uris": null, "num": null, "type_str": "figure"}, "FIGREF11": {"text": "Figure 9. Dam break steps 80 and 240 of test rollout 15.", "fig_num": "9", "uris": null, "num": null, "type_str": "figure"}, "FIGREF12": {"text": "Figure 11. Forcing step function of the 2D reverse Poiseuille flow before (blue) and after convolution with normal distribution N (0, 0.025 2 ) (orange).", "fig_num": "11", "uris": null, "num": null, "type_str": "figure"}, "FIGREF13": {"text": "4. MAE ρ -density MAE error measuring the deviation of the density from the reference density ρ ref . In all our experiments ρ ref = 1.0.5. Dirichlet -Dirichlet energy(<PERSON>, 2005) of density field E D (ρ; <PERSON> et al. (2011). It measures both high-frequency (e.g. clustering) and low-frequency (e.g. instabilities) density fluctuations. Lower is better and means less steep gradients (<PERSON><PERSON> & <PERSON>, 2020; <PERSON> et al., 2022). 6. Chamfer -symmetric Chamfer distance d CD (X, Y ) = x∈X min y∈Y ||x -y|| 2 2 + y∈Y min x∈X ||x -y|| 2 2 between predicted and ground truth frames. Similar to Sinkhorn, but only considers nearest neighbors, and thus much more compute efficient.", "fig_num": null, "uris": null, "num": null, "type_str": "figure"}, "FIGREF14": {"text": "Figure 12. Ablations on DAM 2D with GNS-10-128 over the simulation length (left) and the average thereof (right).", "fig_num": "12", "uris": null, "num": null, "type_str": "figure"}, "FIGREF16": {"text": "Figure 13. Ablations on DAM 2D with SEGNN-10-64 over the simulation length (left) and the average thereof (right).", "fig_num": "13", "uris": null, "num": null, "type_str": "figure"}, "FIGREF17": {"text": "Figure 14. Ablations on LDC 2D with GNS-10-128 (l = 1) regarding relaxation parameter α.", "fig_num": "14", "uris": null, "num": null, "type_str": "figure"}, "FIGREF18": {"text": "Figure 15. Ablations on LDC 2D with GNS-10-128 (α = 0.03) regarding the number of relaxation steps/loops.", "fig_num": "15", "uris": null, "num": null, "type_str": "figure"}, "FIGREF19": {"text": "Figure 16. Ablations on LDC 2D with GNS-10-128 (α = 0.03, l = 5) regarding relaxation parameter β.", "fig_num": "16", "uris": null, "num": null, "type_str": "figure"}, "FIGREF20": {"text": "Figure 17. Ablations on LDC 2D with SEGNN-10-64 (l = 1) regarding relaxation parameter α.", "fig_num": "1718", "uris": null, "num": null, "type_str": "figure"}, "FIGREF21": {"text": "Figure 19. Ablations on LDC 2D with SEGNN-10-64 (α = 0.03, l = 5) regarding relaxation parameter β.", "fig_num": "19", "uris": null, "num": null, "type_str": "figure"}, "FIGREF22": {"text": "Figure 20. Ablations on LDC 3D with GNS-10-128 (l = 1) regarding relaxation parameter α.", "fig_num": "20", "uris": null, "num": null, "type_str": "figure"}, "FIGREF23": {"text": "Figure 21. Ablations on LDC 3D with GNS-10-128 (α = 0.02) regarding the number of relaxation steps/loops.", "fig_num": "21", "uris": null, "num": null, "type_str": "figure"}, "FIGREF24": {"text": "Figure 22. Ablations on LDC 3D with GNS-10-128 (α = 0.02, l = 1) regarding relaxation parameter β.", "fig_num": "22", "uris": null, "num": null, "type_str": "figure"}, "FIGREF25": {"text": "Figure 23. Ablations on LDC 3D with SEGNN-10-64 (l = 1) regarding relaxation parameter α.", "fig_num": "23", "uris": null, "num": null, "type_str": "figure"}, "FIGREF26": {"text": "Figure 24. Ablations on LDC 3D with SEGNN-10-64 (α = 0.02) regarding the number of relaxation steps/loops.", "fig_num": "2425", "uris": null, "num": null, "type_str": "figure"}, "FIGREF27": {"text": "Figure 26. Ablations on RPF 2D with GNS-10-128 over the simulation length (left) and the average thereof (right).", "fig_num": "26", "uris": null, "num": null, "type_str": "figure"}, "FIGREF28": {"text": "Figure 27. Ablations on RPF 2D with SEGNN-10-64 over the simulation length (left) and the average thereof (right).", "fig_num": "27", "uris": null, "num": null, "type_str": "figure"}, "FIGREF29": {"text": "Figure 28. Ablations on RPF 3D with GNS-10-128 over the simulation length (left) and the average thereof (right).", "fig_num": "28", "uris": null, "num": null, "type_str": "figure"}, "FIGREF30": {"text": "Figure 29. Ablations on RPF 3D with SEGNN-10-64 over the simulation length (left) and the average thereof (right).", "fig_num": "29", "uris": null, "num": null, "type_str": "figure"}, "FIGREF31": {"text": "Figure30. GNS-10-128 trained on 2D LDC with relaxation, and but evaluated without relaxation. We denote with αtr that the model has experienced relaxation only during training and with αinf only during inference. Metrics over the simulation length (left) and the average thereof (right).", "fig_num": "30", "uris": null, "num": null, "type_str": "figure"}, "FIGREF32": {"text": "Figure31. GNS-10-128 trained on 2D LDC with relaxation, and also evaluated with relaxation. We denote with αtr,inf that the model has experienced relaxation both during training and inference and with αinf only during inference. Metrics over the simulation length (left) and the average thereof (right).", "fig_num": "31", "uris": null, "num": null, "type_str": "figure"}, "TABREF0": {"text": "Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "html": null, "content": "<table/>", "num": null, "type_str": "table"}, "TABREF1": {"text": "Performance measures averaged over a rollout of 400steps. An additional subscript g indicates that external forces are removed from the model outputs, subscript p indicates that the SPH relaxation has a pressure term, and subscript ν that the viscosity term is added to the SPH relaxation. The numbers in the table are averaged over all test trajectories. MSE400 corresponds to: MSE120 for 2D TGV, MSE55 for 3D TGV, and MSE395 for 2D DAM, as these are the full trajectory lengths excluding initial history size H = 5.", "html": null, "content": "<table><tr><td/><td>Model</td><td>MSE400</td><td>Sinkhorn MSEEkin</td></tr><tr><td>2D</td><td>GNS</td><td colspan=\"2\">5.3e -4 5.4e -7 5.6e -7</td></tr><tr><td>TGV</td><td>GNSp</td><td colspan=\"2\">4.8e -4 1.7e -8 4.8e -7</td></tr><tr><td/><td>GNS</td><td colspan=\"2\">2.7e -2 3.6e -7 4.3e -3</td></tr><tr><td>2D</td><td>GNSg</td><td colspan=\"2\">2.7e -2 2.7e -7 3.7e -4</td></tr><tr><td>RPF</td><td>GNSg,p</td><td colspan=\"2\">2.7e -2 2.9e -8 4.1e -4</td></tr><tr><td/><td colspan=\"3\">GNSg,p,ν 2.7e -2 3.0e -8 1.4e -4</td></tr><tr><td>2D</td><td>GNS</td><td colspan=\"2\">3.3e -2 3.1e -4 1.1e -4</td></tr><tr><td>LDC</td><td>GNSp</td><td colspan=\"2\">1.6e -2 2.8e -7 1.2e -6</td></tr><tr><td/><td>GNS</td><td colspan=\"2\">1.9e -1 3.8e -2 4.6e -2</td></tr><tr><td>2D</td><td>GNSg</td><td colspan=\"2\">8.0e -2 1.3e -2 9.4e -3</td></tr><tr><td>DAM</td><td>GNSp</td><td colspan=\"2\">9.7e -2 7.1e -3 5.8e -3</td></tr><tr><td/><td>GNSg,p</td><td colspan=\"2\">8.4e -2 7.5e -3 2.1e -3</td></tr><tr><td>3D</td><td>GNS</td><td colspan=\"2\">4.8e -2 4.1e -6 3.6e -2</td></tr><tr><td>TGV</td><td>GNSp</td><td colspan=\"2\">4.6e -2 9.0e -7 4.2e -2</td></tr><tr><td>3D RPF</td><td>GNS GNSg GNSp</td><td colspan=\"2\">2.3e -2 4.4e -7 1.7e -5 2.3e -2 4.4e -7 4.1e -5 2.3e -2 1.0e -7 1.5e -5</td></tr><tr><td/><td>GNSg,p</td><td colspan=\"2\">2.3e -2 1.3e -7 4.1e -5</td></tr><tr><td>3D</td><td>GNS</td><td colspan=\"2\">3.2e -2 2.0e -5 1.3e -7</td></tr><tr><td>LDC</td><td>GNSp</td><td colspan=\"2\">3.2e -2 1.1e -6 2.9e -8</td></tr></table>", "num": null, "type_str": "table"}, "TABREF2": {"text": ". Same structure as Table1.", "html": null, "content": "<table><tr><td/><td>Model</td><td>MSE400</td><td>Sinkhorn MSEEkin</td></tr><tr><td>2D</td><td>SEGNN</td><td colspan=\"2\">4.0e -4 4.4e -7 3.9e -7</td></tr><tr><td>TGV</td><td>SEGNNp</td><td colspan=\"2\">3.8e -4 1.5e -8 2.8e -7</td></tr><tr><td/><td>SEGNN</td><td colspan=\"2\">2.7e -2 3.3e -7 4.3e -3</td></tr><tr><td>2D</td><td>SEGNNg</td><td colspan=\"2\">2.8e -2 3.3e -7 1.2e -4</td></tr><tr><td>RPF</td><td>SEGNNg,p</td><td colspan=\"2\">2.8e -2 3.5e -8 1.6e -4</td></tr><tr><td/><td colspan=\"3\">SEGNNg,p,ν 2.8e -2 3.8e -8 7.3e -4</td></tr><tr><td>2D</td><td>SEGNN</td><td colspan=\"2\">7.6e -2 2.3e -3 9.1e + 0</td></tr><tr><td>LDC</td><td>SEGNNp</td><td colspan=\"2\">1.8e -2 5.8e -7 1.6e -5</td></tr><tr><td/><td>SEGNN</td><td colspan=\"2\">1.5e -1 3.4e -2 1.9e -2</td></tr><tr><td>2D</td><td>SEGNNg</td><td colspan=\"2\">1.6e -1 2.1e -2 1.9e + 1</td></tr><tr><td>DAM</td><td>SEGNNp</td><td colspan=\"2\">1.2e -1 9.4e -3 1.2e -2</td></tr><tr><td/><td>SEGNNg,p</td><td colspan=\"2\">8.6e -2 4.9e -3 2.6e -3</td></tr><tr><td>3D</td><td>SEGNN</td><td colspan=\"2\">4.2e -2 6.1e -6 2.4e -2</td></tr><tr><td>TGV</td><td>SEGNNp</td><td colspan=\"2\">4.1e -2 6.0e -7 2.7e -2</td></tr><tr><td/><td>SEGNN</td><td colspan=\"2\">1.2e -1 1.0e -4 1.5e + 3</td></tr><tr><td>3D</td><td>SEGNNp</td><td colspan=\"2\">2.6e -2 1.3e -5 1.8e -2</td></tr><tr><td>RPF</td><td>SEGNNg</td><td colspan=\"2\">2.7e -2 2.6e -6 9.5e -3</td></tr><tr><td/><td>SEGNNg,p</td><td colspan=\"2\">2.6e -2 7.9e -7 5.7e -3</td></tr><tr><td>3D</td><td>SEGNN</td><td colspan=\"2\">3.3e -2 2.3e -5 1.7e -7</td></tr><tr><td>LDC</td><td>SEGNNp</td><td colspan=\"2\">3.3e -2 2.0e -6 1.8e -7</td></tr></table>", "num": null, "type_str": "table"}, "TABREF3": {"text": "This table provides more quantitative results to the discussion on inference speed in Section 4.", "html": null, "content": "<table><tr><td colspan=\"2\">Dataset t gt [ms]</td><td>Model</td><td colspan=\"4\">t l=0 [ms] t l=1 [ms] t l=3 [ms] t l=5 [ms]</td></tr><tr><td>2D RPF</td><td>43.0</td><td>GNS SEGNN</td><td>10.7 24.9</td><td>11.0 25.9</td><td>13.3 28.4</td><td>14.4 30.4</td></tr><tr><td>3D RPF</td><td>424</td><td>GNS SEGNN</td><td>23.8 97.9</td><td>32.5 106</td><td>50.4 124</td><td>68.0 141</td></tr></table>", "num": null, "type_str": "table"}, "TABREF4": {"text": "Timing experiments on RPF datasets with GNS-10-128 model. With tgt, we denote the time the ground truth SPH solver takes to simulate 100 steps, as the LagrangeBench datasets consist of every 100th solver state. We took the values tgt = 43.0 and tgt = 424 from <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2024a). Timing runs are averaged over 10k forward calls to the model and consecutive position relaxations.", "html": null, "content": "<table/>", "num": null, "type_str": "table"}}}}