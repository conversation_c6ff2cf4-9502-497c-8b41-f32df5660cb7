{"paper_id": "PFA", "title": "Scribble-Supervised Semantic Segmentation with Prototype-based Feature Augmentation", "abstract": "Scribble-supervised semantic segmentation presents a cost-effective training method that utilizes annotations generated through scribbling. It is valued in attaining high performance while minimizing annotation costs, which has made it highly regarded among researchers. Scribble supervision propagates information from labeled pixels to the surrounding unlabeled pixels, enabling semantic segmentation for the entire image. However, existing methods often ignore the features of classified pixels during feature propagation. To address these limitations, this paper proposes a prototype-based feature augmentation method that leverages feature prototypes to augment scribble supervision. Experimental results demonstrate that our approach achieves state-of-the-art performance on the PASCAL VOC 2012 dataset in scribble-supervised semantic segmentation tasks. The code is available at https://github.com/TranquilChan/PFA.", "pdf_parse": {"paper_id": "PFA", "abstract": [{"text": "Scribble-supervised semantic segmentation presents a cost-effective training method that utilizes annotations generated through scribbling. It is valued in attaining high performance while minimizing annotation costs, which has made it highly regarded among researchers. Scribble supervision propagates information from labeled pixels to the surrounding unlabeled pixels, enabling semantic segmentation for the entire image. However, existing methods often ignore the features of classified pixels during feature propagation. To address these limitations, this paper proposes a prototype-based feature augmentation method that leverages feature prototypes to augment scribble supervision. Experimental results demonstrate that our approach achieves state-of-the-art performance on the PASCAL VOC 2012 dataset in scribble-supervised semantic segmentation tasks. The code is available at https://github.com/TranquilChan/PFA.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "In recent years, the rapid progress of deep learning techniques has propelled deep neural networks to achieve significant advancements in image semantic segmentation tasks. These networks play a pivotal role in aiding human comprehension of image content, providing precise pixel-level segmentation. As one of the most intricate tasks in the field of computer vision, training semantic segmentation models typically requires a large number of high-quality annotated samples. However, annotating samples at the pixel level demands a substantial amount of manpower and time, and the annotation process can be tedious. Therefore, in scenarios where data dependency is strong, researchers are increasingly focusing on methods that utilize scribble labels for supervised learning. Training with scribble labels falls under weakly supervised learning, allowing annotators to mark regions in the image using simple lines or sketches and assign corresponding labels to those regions. Compared to pixel-level annotation, using scribble labels can significantly reduce the workload of annotation and improve efficiency. Additionally, compared to point (<PERSON><PERSON> et al., 2016; <PERSON> et al., 2021) , bounding box (<PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2021) , and image-level (<PERSON> et al., 2020; <PERSON> et al., 2022) labels, scribble labels provide more crucial semantic information to the models, helping them better learn the semantic structure of the images.", "section": "Introduction", "sec_num": "1."}, {"text": "As shown in Figure 1 , existing methods mainly rely on regularization loss (<PERSON> et al., 2018a; b; <PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2019; <PERSON> et al., 2022) , consistency loss (<PERSON> et al., 2021; <PERSON> et al., 2021) , pseudo proposal (<PERSON> et al., 2016; <PERSON> et al., 2021; <PERSON> et al., 2021) , auxiliary tasks (<PERSON> et al., 2019; <PERSON> et al., 2021) , and label diffu-sion (<PERSON> et al., 2023; <PERSON> et al., 2024) . However, these methods exhibit certain drawbacks. Regularization methods often overlook leveraging high-level semantic information. Consistency loss does not provide direct supervision at the category level. Pseudo-labeling methods require multi-stage training, which are time-consuming. Auxiliary tasks introduce additional data and predictive errors of introduced data can influence the final outcomes. Label diffusion primarily relies on local information and fails to utilize global information to leverage the features of correctly classified pixels. In fact, the features of pixels that have been correctly classified can play a pivotal role in guiding the classification of pixels in boundary regions. However, many existing methods based on scribble supervision ignore this role.", "section": "Introduction", "sec_num": "1."}, {"text": "In the context of semi-supervised classification tasks, <PERSON>at-Match (<PERSON><PERSON> et al., 2020) learns and extracts feature prototypes from labeled samples, subsequently enhancing the features of unlabeled data with these prototypes to improve the classification of unlabeled samples. Inspired by this, we seek to extend its application to scribble-level weakly supervised semantic segmentation. Nevertheless, in our scenario of scribble-level weakly supervised segmentation, the labels are assigned at the pixel level, unlike the image-level labels in semi-supervised tasks. Our approach entails the initial learning from labeled pixels, extraction of feature prototypes from accurately classified pixels, and the subsequent utilization of these prototypes to guide the classification of remaining pixels.", "section": "Introduction", "sec_num": "1."}, {"text": "Specifically, our method initiates with prototype extraction.", "section": "Introduction", "sec_num": "1."}, {"text": "In contrast to conventional clustering methods employed in semi-supervised and unsupervised approaches, we directly extract feature prototypes from high-confidence regions of initial predictions. In summary, our contributions are as follows:", "section": "Introduction", "sec_num": "1."}, {"text": "• We propose a prototype-based feature augmentation method for scribble-level weak supervision, extend-ing the application of the method from image-level to pixel-level and significantly enhancing the efficacy of scribble supervision.", "section": "Introduction", "sec_num": "1."}, {"text": "• We propose a dynamic augmentation strategy employing local and global prototypes. This synergistic approach maximizes the utilization of information in scribble-supervised semantic segmentation and mitigates the challenge of limited prototype representation at various training stages.", "section": "Introduction", "sec_num": "1."}, {"text": "• We validate the components of our method through experiments and report the state-of-the-art performance on the PASCAL VOC 2012 dataset.", "section": "Introduction", "sec_num": "1."}, {"text": "Scribble-supervised semantic segmentation, a form of weakly supervised semantic segmentation, employs scribbles as annotations to label image regions. This approach presents a cost-effective alternative to fully supervised labeling. Compared to other weakly supervised annotation methods, including point, bounding-box, and image-level labels, scribble supervision imparts more comprehensive and detailed information, resulting in better performance.", "section": "Scribble-Supervised Semantic Segmentation", "sec_num": "2.1."}, {"text": "In scribble-supervised semantic segmentation, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON> et al., 2016) first introduced the concept of using scribble labels for semantic segmentation and provided the ScribbleSup dataset. They propagated the scribble label information to surrounding pixels using superpixels and designed corresponding loss functions for supervision. <PERSON> et al. proposed methods based on regularization losses, such as Normalized Cut loss (<PERSON> et al., 2018a) and Kernel Cut loss (<PERSON> et al., 2018b) , for model training. Gated CRF (<PERSON><PERSON><PERSON><PERSON> et al., 2019) augmented the efficiency by introducing gating operations on top of the Kernel Cut loss. RAWKS (Vernaza & Chandraker, 2017) and BPG (<PERSON> et al., 2019) utilized boundary detectors to assist the models in achieving better results. URSS (<PERSON> et al., 2021) reduced uncertainty through neural representation and selfsupervision in the neural feature space. SPML (<PERSON> et al., 2021) improved performance by using metric learning methods and introducing a contour detector as additional supervision. PSI (<PERSON> et al., 2021) utilized latent contextual dependency to enhance and refine segmentation results from partially known seeds. A2GNN (<PERSON> et al., 2021) introduced a graph neural network approach to generate pseudolabels and applied multi-level supervision. TEL (<PERSON> et al., 2022) proposed a novel tree energy loss method that provides semantic guidance to unlabeled pixels. AGMM (<PERSON> et al., 2023) of labeled pixels and unlabeled pixels sharing similar feature distributions. CDSP (<PERSON> et al., 2024) utilized pseudolabels supervised by image-level class labels and global semantics.", "section": "Scribble-Supervised Semantic Segmentation", "sec_num": "2.1."}, {"text": "Feature prototypes can be seen as 'exemplars' of different categories in the feature space. Their essence lies in being feature vectors widely used in computer vision tasks to augment the model's recognition capability of different types of features. In fully supervised semantic segmentation tasks, OCRNet (<PERSON> et al., 2020) , ACFNet (<PERSON> et al., 2019) , and CondNet (<PERSON> et al., 2021) aggregate category feature embeddings by considering the initial segmented regions. Mask2former (<PERSON> et al., 2022) and CFT (<PERSON> et al., 2023) focus on category features through masking. The main idea of these methods is to augment features through prototypes, but their methods are relatively common, such as convolution, multiplication, and attention. In weakly supervised semantic segmentation, EPS (<PERSON><PERSON> et al., 2021) utilizes prototypes to guide the model in learning more accurate feature representations, thereby improving segmentation results. PPC (<PERSON> et al., 2022) provides pixel-level supervisory signals by contrasting pixels with prototypes to narrow the gap between classification and segmentation. SIPE (<PERSON> et al., 2022) proposes an image-specific prototype exploration method to capture complete regional information in the image. They delve into the use of feature prototypes, but they do not use prototypes for feature augmentation, or their employed fusion approaches are relatively simple, thereby failing to harness the guiding role of prototype. FeatMatch (<PERSON><PERSON> et al., 2020) augments features by fusing them with prototypes, but it is a semi-supervised classification task. <PERSON> et al. further introduces prototype-augmented methods into the field of semantic segmentation (<PERSON> et al., 2022) , but it remains a semi-supervised task. The goal of our work is to incorporate this idea into weakly supervised semantic segmentation, introducing prototype augmentation at the pixel level.", "section": "Prototype-based Method", "sec_num": "2.2."}, {"text": "Figure 2 illustrates the overall framework of our approach, leveraging a Vision Transformer (<PERSON><PERSON> et al., 2021) as the backbone for extracting initial feature maps. These feature maps are then input to a decoder for generating semantic segmentation prediction maps. Details of the encoder and decoder are discussed in Section 4.2. Supervised by scribble labels through partial cross-entropy loss, the semantic segmentation prediction maps are refined. Subsequently, high-confidence regions from the initial prediction maps are identified as accurately predicted pixels, and corresponding feature vectors in the initial feature maps are extracted.", "section": "Overview", "sec_num": "3.1."}, {"text": "Local prototypes are formed by weighting and averaging these feature vectors based on predicted values. Throughout training iterations, these local prototypes update global prototypes, elucidated in Section 3.2. Both the global prototypes and local prototypes are used to augment the initial features through prototype-based feature augmenters. The augmented feature maps are then passed through the decoder to generate augmented prediction maps, with the weights of the three decoders shared during this process. The consistency loss is applied between the two augmented prediction maps and the initial prediction maps for supervision, detailed in Section 3.3.", "section": "Overview", "sec_num": "3.1."}, {"text": "Augmenting features via prototypes depends heavily on precisely defined prototypes. The creation of these prototypes is vital. We classify prototypes into two types: local prototypes and global prototypes. Local prototypes are extracted from image features within each batch during training iterations and are specifically for augmenting features within that batch. In contrast, global prototypes encompass more comprehensive information and can be dynamically updated globally. Global prototypes augment information diversity by updating with local prototypes, rendering them more suitable for feature augmentation.", "section": "SETTING OF THE PROTOTYPE", "sec_num": "3.2.1."}, {"text": "Here, we outline the process of extracting prototypes from features, namely generating local prototypes. Currently, methods for extracting prototypes from image features are mostly employed in semi-supervised and unsupervised semantic segmentation techniques. Many of these methods (<PERSON><PERSON> et al., 2020; <PERSON> et al., 2022) utilize clustering as an effective approach in scenarios where labels are either scarce or lacking. However, the primary goal of prototype extraction is to derive highly representative features from the data as the basis for prototype generation. Using clustering methods often fails to effectively align with the actual categories.", "section": "EXTRACTION OF THE PROTOTYPE", "sec_num": "3.2.2."}, {"text": "In a weakly supervised setting, we believe that relying solely on clustering methods does not fully leverage the semantic information provided by scribble labels. These labels can significantly contribute by offering valuable insights into the representative regions required for prototype extraction.", "section": "EXTRACTION OF THE PROTOTYPE", "sec_num": "3.2.2."}, {"text": "Therefore, we utilize the initial prediction values, represented as p, generated by the model as confidence scores.", "section": "EXTRACTION OF THE PROTOTYPE", "sec_num": "3.2.2."}, {"text": "Assisted by category labels, we compute prototypes from high-confidence pixel-level features of the categories in the current image. Initially, we select the top K confident points for each category. Unlike conventional methods (<PERSON> et al., 2022) , we exclusively employ features associated with categories present in the current image for prototype computation by utilizing category labels. This approach aids in eliminating prototype interference from irrelevant categories during subsequent feature augmentation. The formula for computing feature prototypes is:", "section": "EXTRACTION OF THE PROTOTYPE", "sec_num": "3.2.2."}, {"text": "EQUATION", "section": "EXTRACTION OF THE PROTOTYPE", "sec_num": "3.2.2."}, {"text": "Here, t represents one of all categories. The sets Ω p and Ω f respectively denote the collections of feature values and prediction values for all categories present in the current image. The feature prototype fp t is the weighted average of feature embedding, normalized accordingly.", "section": "EXTRACTION OF THE PROTOTYPE", "sec_num": "3.2.2."}, {"text": "When extracting prototypes solely from each batch, it limits the consideration of diversity within prototypes of the same category. This restriction confines the analysis to the current batch's image features, failing to effectively aid the model in comprehending new features. ", "section": "DEFINITION AND UPDATE OF GLOBAL PROTOTYPES", "sec_num": "3.2.3."}, {"text": "EQUATION", "section": "DEFINITION AND UPDATE OF GLOBAL PROTOTYPES", "sec_num": "3.2.3."}, {"text": "Inspired by the feature prototype method <PERSON>atM<PERSON> (<PERSON><PERSON> et al., 2020) used in semi-supervised learning classification, we have introduced a feature-prototype-based classification approach into scribble-supervised semantic segmentation tasks. In semi-supervised classification tasks, prototypes are derived through clustering. During augmentation, prototypes for each category are involved, aiming to extract valuable information from labeled data and propagate it to unlabeled data. However, for our task, prototypes are extracted from high-confidence regions in the current image features. Our goal is to propagate valuable information from correctly classified pixels to those yet to be classified. Therefore, we believe other category prototypes aren't necessary for this augmentation process.", "section": "PROTOTYPES", "sec_num": null}, {"text": "Feature augmentation based on local prototypes occurs within each batch. Therefore, when augmenting using local prototypes, it is essential to select the prototypes corresponding to the categories of the current features for reinforcement.", "section": "PROTOTYPES", "sec_num": null}, {"text": "In the aforementioned extraction phase, prototypes unrelated to categories present in the current image were not extracted, resulting in these category prototypes being zero-valued. To select the prototypes corresponding to categories within the current batch, we initially filter the local prototypes fp by defining fp * = select(fp), thereby removing irrelevant zerovalue prototypes. Subsequently, for each feature f within the current batch and its corresponding prototype fp, we project them into an embedding space, obtaining e f = linear(f) and e f p = linear(fp). Then, we compute their attention weight ω, derived from the matrix multiplication of e f and e f p , normalized using sof tmax. The formula is expressed as: ω = sof tmax(matmul(e T f , e f p ))", "section": "PROTOTYPES", "sec_num": null}, {"text": "Then, weighting e f p using attention weight ω . Next, concatenating the weighted prototype with image features and subjecting them to a linear layer transformation is performed. This process aids in propagating information from the prototype to the image features, thereby enhancing the features:", "section": "PROTOTYPES", "sec_num": null}, {"text": "e * f = ReLu(linear(concat(matmul(ω, e f p ), e f )) (7)", "section": "PROTOTYPES", "sec_num": null}, {"text": "Finally, the last feature is obtained by using residual connections between the initial features and the augmented features:", "section": "PROTOTYPES", "sec_num": null}, {"text": "EQUATION", "section": "PROTOTYPES", "sec_num": null}, {"text": "As shown in the Figure 3 , the overall process of feature augmentation based on global prototypes is similar to that based on local prototypes, with the main difference lying in the initial handling of prototypes. However, compared to local prototypes, global prototypes contain n ordinary prototypes for each category. Hence, before augmentation, it is necessary to perform a merge process on the global prototypes to facilitate subsequent computations.", "section": "PROTOTYPES", "sec_num": null}, {"text": "Considering that our prototypes are extracted from features, the quality of the extracted features might not be assured during the early stages of training. Consequently, relying solely on prototypes for feature augmentation might not yield optimal results. Therefore, we have established a warm-up period during which we refrain from utilizing prototypes for feature augmentation, instead employing basic loss constraints for training. Once this warm-up period concludes, we extract local prototypes from the features. These local prototypes contribute to enhancing the features of the current batch. Meanwhile, the global prototypes might still contain empty values due to ongoing updates. Hence, we have set a condition where the global prototypes do not partake in feature augmentation until they are completely filled. It is only after the global prototypes have been fully updated that we utilize them for augmentation.", "section": "THE SETTING FOR AUGMENTATION", "sec_num": "3.3.3."}, {"text": "For the design of the loss function, as shown in Figure 2 , the overall loss function consists of two parts: partial crossentropy loss L pce and consistency loss L con . The consistency loss further includes the consistency loss between the initial predicted values and the predicted values augmented using local prototypes L total , as well as the consistency loss between the initial predicted values and the predicted values augmented using global prototypes L global .", "section": "Loss Function", "sec_num": "3.4."}, {"text": "Partial cross-entropy loss is obtained between the predicted values and the scribble labels, and its expression is as follows (<PERSON> et al., 2018b; <PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2021; <PERSON> et al., 2022) :", "section": "Loss Function", "sec_num": "3.4."}, {"text": "EQUATION", "section": "Loss Function", "sec_num": "3.4."}, {"text": "where Ω L represents the set of all labeled pixels, C represents all the categories, y c i and p c i represent the ground truth and predicted values of category c at pixel i, respectively.", "section": "Loss Function", "sec_num": "3.4."}, {"text": "Consistency loss is employed to constrain the feature prediction values before and after augmentation. We utilize mean squared error (MSE) (<PERSON> & <PERSON>, 1999) as the consistency loss function to restrict the relationship between these two prediction values, expressed as:", "section": "Loss Function", "sec_num": "3.4."}, {"text": "EQUATION", "section": "Loss Function", "sec_num": "3.4."}, {"text": "Here, h and w represent the height and width of the predicted values, where p i and p aug i denote the initial predicted value and the augmented predicted value at the same position, respectively.", "section": "Loss Function", "sec_num": "3.4."}, {"text": "Total loss varies at different stages of training. In the warm-up period, when neither the local prototype nor the global prototype is utilized, the overall loss is represented as: L total = L pce . During the phase when prototypes start being used but the global prototype hasn't been fully updated, only the local prototype contributes to augmentation. Hence, the overall loss at this stage is: L total = L pce + λ l L con-l . Finally, when both the global and local prototypes are engaged in training, the overall loss is defined as: L total = L pce + λ l L con-l + λ g L con-g . Here, λ l and λ g represent the loss weights for L con-l and L con-g respectively.", "section": "Loss Function", "sec_num": "3.4."}, {"text": "We use the PASCAL-Scribble Dataset, which was initially introduced by <PERSON> et al. in ScribbleSup (<PERSON> et al., 2016) . The PASCAL-Scribble Dataset is a dataset with scribble annotations applied to the PASCAL VOC 2012 (<PERSON><PERSON> et al., 2010) . The PASCAL VOC 2012 dataset consists of 12,031 images with scribble annotations. The training set contains 10,582 images, and the validation set contains 1,449 images. The PASCAL VOC 2012 dataset has 21 categories, including 20 object categories and one background category. Unless otherwise specified, all ablation experiments are conducted on the PASCAL VOC 2012 dataset.", "section": "Dataset and Evaluation Metric", "sec_num": "4.1."}, {"text": "The evaluation metric used is the mean Intersection-over-Union (mIoU).", "section": "Dataset and Evaluation Metric", "sec_num": "4.1."}, {"text": "Our method consists of five main modules: encoder, decoder, prototype extraction, prototype updating, and feature augmentation. Given the efficient and excellent performance of the vision transformer in semantic segmentation tasks, we adopt the Mix Transformer proposed in Segformer (<PERSON><PERSON> et al., 2021) , which is specifically optimized for semantic segmentation tasks and achieves superior performance compared to vanilla transformers. The backbone parameters of the model are initialized with ImageNet (<PERSON><PERSON> et al., 2009) pretrained weights, while the remaining parameters are randomly initialized. We utilize the AdamW optimizer with an initial learning rate of 3 × 10 -5 . We employ a multi-step scheduler for learning rate decay during iterations, with a decay weight of 0.01. Additionally, the learning rate for other parameters is set to 10 times that of the backbone network. Regarding data preprocessing, all training images undergo random scaling (0.5 to 2), random rotation (-10 to 10 degrees), random flipping, and Gaussian blurring. Finally, the images are cropped to a size of 512×512. In Equation ( 5), the momentum of prototype updating α, is set to 0.99, and the number of global prototypes is set to 5. The batch size is set to 16. Our experimental code is based on the PyTorch framework (<PERSON><PERSON><PERSON> et al., 2019) , and all experiments are conducted on an NVIDIA RTX 3090 GPU.", "section": "Implementation Details", "sec_num": "4.2."}, {"text": "As shown in Table 1 , we compare our method with existing approaches and demonstrate significant improvements in PASCAL VOC 2012 val set. To ensure fairness, we chose MiT-B1 as the backbone, which achieved a score of 79.2% on fully supervised data, similar to other methods using ResNet101 (<PERSON> et al., 2016) as the backbone. MiT-B1 is slightly lower than them. Of course, we can also choose larger MiT series backbones, but this would compromise the fairness of the comparison. Therefore, we will discuss this in detail in Section 4.4.", "section": "Comparison with State-of-the-Art Methods", "sec_num": "4.3."}, {"text": "As shown in Table 1 , we compare our method with other state-of-the-art approaches. Our method adopts a singlestage training framework, which does not require the use of additional supervised data during the training process and does not use CRF (Krähenbühl & Koltun, 2011) . <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON> et al., 2016) introduced scribble labels into the semantic segmentation field for the first time and achieved an mIoU of 63.1%. Methods based on the design principle of regularization loss guide pixel classification by extracting pairwise relationships from low-level image information. They also achieve good results, reaching up to 75.0%. BPG (<PERSON> et al., 2019) and SPML (<PERSON> et al., 2021) use edge detectors to assist semantic segmentation, but this requires additional data. URSS (Pan et al., 2021) and A2GNN (<PERSON> et al., 2021 ) also achieve good results, surpassing 76% mIoU, but they require multi-stage learning. AGMM (<PERSON> et al., 2023) and CDSP (<PERSON> et al., 2024) leverage label diffusion methodologies to achieve notable performance. However, it is noteworthy that their respective mIoU values have not exceeded the threshold of 77%. TEL (<PERSON> et al., 2022) is the best method in recent years, proposing a tree energy loss to guide the classification of unlabeled pixels, achieving an mIoU of 77.3%. Compared to the current state-of-the-art method TEL, despite our backbone network, MiT-B1, performing slightly weaker than theirs on the fully supervised dataset, our approach still achieves a 0.6% improvement in mIoU.", "section": "Comparison with State-of-the-Art Methods", "sec_num": "4.3."}, {"text": "In this section, we investigate all the operations discussed in Section 3. All training and validation are conducted on the Pascal VOC 2012 dataset.", "section": "Ablation Study and Analysis", "sec_num": "4.4."}, {"text": "Effectiveness of each component. First, we investigate the components of our method, which involve the usage of three loss functions. We employ the basic MiT-B1 as the backbone. We designate the use of only partial crossentropy as the baseline for our method, and then conduct ablation studies on the methods with local prototype augmentation and global prototype augmentation. As shown in Figure 2 , our method has three prediction maps, and all three prediction maps can generate results. When using only local prototypes, there are two prediction maps: the basic one and the one augmented with local prototypes. Similarly, when using global prototype augmentation, there are also two prediction maps: the basic one and the one augmented with global prototypes. As shown in Table 2 , when using only local prototype augmentation, our method achieves a 9.4% mIoU improvement compared to the baseline. When using only global prototype augmentation, there is a 9.9% mIoU improvement. When both methods are used together, the best performance is achieved with a 10.4% mIoU improvement. Therefore, we select the simultaneous augmentation of both prototypes as our final method. In Section 3.4, due to the different choices of L pce at different stages, when both augmentation methods are used, we also conduct ablation experiments on the two choices. The results show that the prediction map augmented with global prototype augmentation, guided by partial cross-entropy loss with scribble labels, achieves the best results. Additionally, we can observe that using both augmentation methods can significantly improve the basic prediction map.", "section": "Ablation Study and Analysis", "sec_num": "4.4."}, {"text": "As shown in Figure 4 , we present the visualization results for different composition components. Compared to the baseline results, the results augmented with prototypes show significant improvements, especially in regions of other categories, such as the \"fire hydrant\" in the second image and the green area in the fourth image. Through prototype guidance, the interference from these other categories is weakened or eliminated. There is little difference between using only local prototypes and using only global prototypes, with the global prototypes slightly emphasizing details. Optimal results are achieved when both methods are used, as evidenced by the improvement in the hand region in the first image and the details of the occluded edges in the fourth image. For more detailed information, please refer to the supplementary. Setting of prototype. In our approach, the number of global prototypes represents the number of local prototypes that should be included for each class. To assess the impact of increasing the number of global prototypes on the results, we conducted experiments using only global prototypes for augmentation and not utilizing local prototypes. From Figure 5, it can be observed that as the number of prototypes increases to around 5, the increase in mIoU becomes satu-rated. Therefore, we have set this value as the default in our method.", "section": "Ablation Study and Analysis", "sec_num": "4.4."}, {"text": "We also conduct tests on the top-k percentage involved in Equations ( 1) and ( 2) for prototype extraction. The fewer pixels involved in the computation indicate the use of more confident pixels for prototype extraction. However, if the percentage is too small, we may lose more useful information. Similarly, to evaluate the impact of varying the value of k on the performance, we conduct experiments using only local prototypes. As shown in Figure 6 , our method performs better when the k percentage is 8%. Impact of backbone. In Section 4.3, for a fair comparison, we only compare our method with the state-of-the-art methods using MIT-B1. Here, we investigated the backbone of our method. We select the backbones commonly used in existing methods, such as DeepLabv2 (<PERSON> et al., 2017) , DeepLabv3+ (<PERSON> et al., 2018) and LTF (<PERSON> et al., 2019) based on ResNet101 (<PERSON> et al., 2016) , as well as Segformer (<PERSON><PERSON> et al., 2021) based on a larger backbone network that performs better in fully supervised scenarios. As shown in Table 3 , we present the results obtained in both fully supervised and scribble supervised settings using the Pascal VOC 2012 dataset, as well as the parameter counts and floating-point operation (FLOPs) of each model during inference. The table demonstrates that MiT-B1 Segformer achieves comparable performance to the ResNet101-based LTF despite its significantly lower parameter counts and computational complexity. And under the premise of using ResNet101 as backbone, our approach is superior to all current approaches. We observed that our method exhibits superior upper bound performance on the Transformer (77.9%/79.2%) compared to ResNet (78.2%/80.9%). This characteristic, coupled with its exceptional efficiency, constitutes a key factor in our selection of Segformer. When utilizing a model with a similar parameter count, MiT-B3 attains a mIoU score of 79.8%. Additionally, opting for MiT-B5 with the highest parameter count yields a peak mIoU score of 81.5%, significantly surpassing existing methods. However, due to the unfair advantage introduced by the backbone, it is not included in Table 1 .", "section": "Ablation Study and Analysis", "sec_num": "4.4."}, {"text": "This paper introduces a prototype-based feature augmentation method for scribble supervision. We extract prototypes from the confident portion of the initial results provided by scribble supervision. By utilizing the extracted prototypes, we augment the initial features and employ different prototype strategies tailored to the specific setting of scribble supervision. The method utilizes generated prototypes from correctly classified pixels to guide the classification of misclassified pixels, resulting in improved prediction performance. Experimental results demonstrate that our method achieves state-of-the-art performance. In the future, we plan to apply our method to other tasks to harness its significant potential and application value.", "section": "Conclusion", "sec_num": "5."}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.", "section": "Impact Statement", "sec_num": null}, {"text": "<PERSON><PERSON> More Technical Details As shown in Figure 7 , the structure of our encoder and decoder utilizes the Segfomer (<PERSON><PERSON> et al., 2021) based on MiT-B1, and its efficiency is one of the main reasons why we chose it. We select the features from four levels, which are fused by the MLPLayer, as our feature map F because it can contain information from multiple levels. We use the feature map F and the prediction map P as the basis for prototype extraction. The features augmented through prototype refinement are then passed through a feature augmenter to maintain the same size as F.", "section": "Impact Statement", "sec_num": null}, {"text": "During feature extraction, we extract feature prototypes from the feature values corresponding to the top K values of each category contained in the prediction of the current image. Due to the uncertainty of predictions, it is inevitable to extract prototypes from other misclassified categories. In this case, when using only local prototype augmentation, although the prototypes are extracted from the current predicted category, they only reinforce the probability of misclassification for those misclassified images, leading to worse prediction performance. When using global prototypes, we retrieve global prototypes from the prototype memory bank for augmentation. Global prototypes include prototypes for each category, and if we do not select the prototypes of the correct category contained in the current image, it will also increase the chances of model misjudgment, resulting in counterproductive effects. Therefore, during the training process, we use category labels to filter the prototypes and select out those irrelevant prototypes to better utilize the effects of prototypes. The scribble labels contain category information, so we can easily obtain category labels from the scribble labels. Therefore, these can be achieved quite well during the training process, but they pose challenges during the inference process.", "section": "A.2. More Details of Feature Augmenter and Inference", "sec_num": null}, {"text": "As depicted in Table 4 , employing the correct class labels to guide prototype augmentation yields a mIoU score of 80.4%, surpassing even the predictions of MiT-B1 on the fully supervised dataset. However, this approach violates inference rules, as during inference, we can only access images without direct class label information. To address this, our initial strategy involves filtering preliminary prediction maps of unused prototypes by setting a threshold, because these pixels with small quantities are often misclassified. Experimenting with various thresholds, as shown in Table 4 , the optimal result occurs with a threshold of 500, resulting in a mIoU of 75.7%. Nevertheless, this is lower than the 76.8% mIoU achieved without prototype augmentation. Consequently, we incorporate the classification results of established multi-label classification methods, Q2L (<PERSON> et al., 2021) and MCAR (Gao & Zhou, 2021) , attaining respective mAP scores of 96.6% and 94.3% on the Pascal VOC 2012 val dataset. Utilizing their classification results, we achieve mIoU scores of 77.9% and 76.2%, respectively. This underscores the influence of superior classification results on enhancing prototype augmentation.", "section": "A.2. More Details of Feature Augmenter and Inference", "sec_num": null}, {"text": "Conversely, incorrect classification results can misguide the model's understanding of semantic information by employing prototypes from incorrect categories, resulting in inferior performance. Given the potential for improving segmentation results through category labels, our future work will focus on refining the guidance of the prototype and incorporating this approach into multimodal tasks. The form of Scribble. Since doodling can vary greatly in style from person to person, it is inherently subjective. Therefore, it becomes imperative to conduct robustness tests on the model with different degrees of drop and shrink ratios. In Figure 8 , we present the results of our method's ablation experiments using scribble labels with varying degrees of drop and shrink.", "section": "A.2. More Details of Feature Augmenter and Inference", "sec_num": null}, {"text": "The results clearly demonstrate that, as the drop rate and shrink ratio increase, the model's performance declines. The method based on global prototypes exhibits relatively greater stability compared to the method employing local prototypes. However, overall, the model performs well even when the scribble annotations are reduced to mere dots.", "section": "A.2. More Details of Feature Augmenter and Inference", "sec_num": null}, {"text": "Weight of the loss. In Table 5 , we conducted an ablation study on the weighting of loss functions. We adjusted the weight ratios between different loss functions. Through this study, we determined the optimal weighting configuration for the loss functions as λ l = 0.01, λ g = 0.01. ", "section": "A.2. More Details of Feature Augmenter and Inference", "sec_num": null}, {"text": "Figure 9 illustrates failed cases, offering a more intuitive understanding of the impact of prototype augmentation. To deepen the analysis, we present four types of outcomes: results without prototype augmentation, inference based on category labels, and inference utilizing two classification results. In the upper section of Figure 9 , without prototype augmentation, the model identifies various semantic categories, including 'person', 'chair', 'sofa', 'table', 'plant', 'boat', among others, with many misclassifications. Given that the category labels only specify 'person', 'sofa', and 'bottle', they direct the model to diminish the representation of other categories in the initial prediction, especially those on the prediction periphery like 'table' and 'plant', which vanish after augmentation. Conversely, the category 'bottle', absent in the initial results, emerges after being guided by the prototype. Notably, although the category label does not contain 'chair', a small portion is retained after prototype augmentation. In the Q2L (<PERSON> et al., 2021) and MCAR (Gao & Zhou, 2021) classification results, 'person', 'chair', 'sofa', and 'person', 'chair', 'sofa', 'bottle' are respectively identified. The segmentation results also align with the classification results, weakening the performance of other categories. In the lower section of Figure 9 , the results without prototype augmentation include three categories: 'cat', 'dog', and a minimal representation of 'sofa'. Both category labels and Q2L classification include 'sofa', leading to a significant increase in the sofa's representation after prototype augmentation. However, as MCAR's classification does not involve 'sofa', the remaining small representation of 'sofa' disappears after prototype augmentation. Overall, the prototype augmented the model's understanding and inference capabilities of complex semantics by guiding the identification of edge-class categories using category labels to refine initial results.", "section": "B.2. Failure Cases and Analysis", "sec_num": null}, {"text": "As shown in Table 6 , we also present the data comparison of our method with other methods on each category of Pascal VOC 2012 val set. Our approach has yielded the best results in most categories. In Figure 10 , we present the visualization results of our method, along with the baseline and the SOTA methods. From the images, it can be observed that the SOTA method is able to identify certain regions. However, the identified regions are relatively small. This is where the advantage of our method comes into play. With the guidance of the prototype, our method can guide the segmentation of other pixels, resulting in superior segmentation results in the boundary regions. For instance, in the first image, the ears of the cow; in the second image, the 'chair' region; in the third image, the 'bottle' region; and in the fourth image, the 'sofa' region. These areas effectively demonstrate the guiding role of the prototype, as they direct the classification of other pixels and augment the classification performance in the boundary regions.", "section": "B.3. More Quantitative Results", "sec_num": null}, {"text": "We have provided more visualization results of different composition components in Figure 11 . ", "section": "B.3. More Quantitative Results", "sec_num": null}], "back_matter": [{"text": "This work is funded by the National Natural Science Foundation of China under Grant No.62272145 and No.U21B2016.", "section": "Acknowledgements", "sec_num": null}, {"text": " (<PERSON> et al., 2018b) (2) ECCV'18 Scribble --√ 75.0 GridCRF Loss (<PERSON> et al., 2019) (2) ICCV'19 Scribble ---72.8 GatedCRF (<PERSON><PERSON><PERSON><PERSON> et al., 2019) (3) NeurIPS'19 Scribble √ --75.5 BPG (<PERSON> et al., 2019) (2) IJCAI'19 Scribble √ √ -76.0 SPML (<PERSON> et al., 2021) (2) ICLR'21 Scribble √ √ √ 76.1 URSS (<PERSON> et al., 2021) (2) ICCV'21 Scribble --√ 76.1 PSI (<PERSON> et al., 2021) (3) ICCV'21 Scribble √ --74.9 Seminar (<PERSON> et al., 2021) ( ", "section": "annex", "sec_num": null}], "ref_entries": {"FIGREF0": {"fig_num": null, "uris": null, "text": "Key Laboratory of Water Big Data Technology of Ministry of Water Resources, Hohai University, Nanjing, China 2 College of Computer Science and Software Engineering, Hohai University, Nanjing, China 3 School of Computing Technologies, RMIT University, Melbourne, Australia. Correspondence to: <PERSON><PERSON><PERSON> <<EMAIL>>. Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "type_str": "figure", "num": null}, "FIGREF1": {"fig_num": "1", "uris": null, "text": "Figure 1. Illustration of existing scribble-supervised semantic segmentation approaches. F represents the feature map, and P represents the prediction map.", "type_str": "figure", "num": null}, "FIGREF2": {"fig_num": "3", "uris": null, "text": "Figure 3. The structural diagram of the prototype-based feature augmenter. When the global prototypes are not fully updated, only the local prototypes are used to augment the features. Once the global prototypes are fully updated, both the local and global prototypes are used to augment the features.", "type_str": "figure", "num": null}, "FIGREF3": {"fig_num": "4", "uris": null, "text": "Figure 4. Visualizing the Components of Our Method through Ablation Experiments.(a) Input image. (b) Baseline (Only use partial cross-entropy). (c) Augmented only by local prototypes. (d) Augmented only by global prototypes. (e) Augmented by two types of prototypes. (f) Ground truth.", "type_str": "figure", "num": null}, "FIGREF4": {"fig_num": "5", "uris": null, "text": "Figure 5. Impact of the number of prototypes contained in each class of global prototypes.", "type_str": "figure", "num": null}, "FIGREF5": {"fig_num": "6", "uris": null, "text": "Figure 6. Impact of different top-k percentage values on the computation of prototype in Equations (1) and (2).", "type_str": "figure", "num": null}, "FIGREF6": {"fig_num": "8", "uris": null, "text": "Figure 8. The experiments on scribble-drop and scribble-shrink dataset with different drop or shrink ratios.", "type_str": "figure", "num": null}, "FIGREF7": {"fig_num": "9", "uris": null, "text": "Figure 9. Failure cases of the proposed method on Pascal VOC val dataset. (a) Input image. (b) Baseline (Inference without prototype augmentation). (c) Inference with using category labels (d) Inference with using Q2L results. (e) Inference with using MCAR results. (f) Ground truth.", "type_str": "figure", "num": null}, "FIGREF8": {"fig_num": "1011", "uris": null, "text": "Figure 10. Visualization of our method and SOTA. (a) Input image. (b) Baseline (Inference without prototype augmentation). (c) URSS(<PERSON> et al., 2021). (d) TEL(<PERSON> et al., 2023). (e) Ours. (f) Ground truth.", "type_str": "figure", "num": null}, "TABREF1": {"content": "<table/>", "text": "implemented supervision by constructing Gaussian mixture models based on the feature 2. The overall framework of our approach. Initially, the image undergoes encoding to produce a feature map. Subsequently, this feature map is fed into the decoder to generate a semantic segmentation prediction map. Scribble labels are employed to impose constraints using partial cross-entropy loss. Next, local prototypes are extracted from the initial prediction map and the feature map, while global prototypes are updated throughout the training iterations. The initial feature map is augmented separately using these two types of prototypes, and predictions are generated using the decoder. Consistency loss is used to constrain between two predicted maps and the initial predicted map. During the warm-up phase, partial cross-entropy is black. It turns green when global prototypes are inactive, and yellow when global prototypes are in use.", "html": null, "type_str": "table", "num": null}, "TABREF3": {"content": "<table><tr><td colspan=\"3\">3.3. Prototype-based Feature Augmentation</td><td/></tr><tr><td>Softmax</td><td>Concat</td><td>Linear</td><td>ReLu</td></tr></table>", "text": "", "html": null, "type_str": "table", "num": null}, "TABREF4": {"content": "<table><tr><td>Method</td><td colspan=\"3\">Lpce L con-l Lcon-g</td><td colspan=\"3\">mIoU(%) basic local global</td></tr><tr><td>Baseline</td><td>√</td><td/><td/><td>67.5</td><td>-</td><td>-</td></tr><tr><td>Ours</td><td>Lpce Lpce Lpce Lpce</td><td>√ √ √</td><td>√ √ √</td><td>75.7 76.0 76.4 76.8</td><td>76.9 -77.7 77.3</td><td>-77.4 77.1 77.9</td></tr></table>", "text": "Ablation studies of the components of our proposed method. \"basic\" means the basic result, \"local\" means the result augmented with local prototypes, and \"global\" means the result augmented with global prototypes. The Lpce and Lpce represent different Lpce in Figure2, while the blue and red represent the top two results.", "html": null, "type_str": "table", "num": null}, "TABREF5": {"content": "<table><tr><td>Model</td><td>Backbone</td><td colspan=\"2\">Params FLOPs</td><td colspan=\"2\">mIOU(%) full scribble</td></tr><tr><td>DeeplabV2</td><td colspan=\"2\">ResNet101 43.6M</td><td>75.2G</td><td>77.7</td><td>76.2</td></tr><tr><td colspan=\"5\">DeeplabV3+ ResNet101 60.8M 102.3G 80.2</td><td>78.1</td></tr><tr><td>LTF</td><td/><td colspan=\"3\">91.7M 138.4G 80.9</td><td>78.2</td></tr><tr><td>Segformer</td><td>MiT-B1</td><td>13.6M</td><td>19.4G</td><td>79.2</td><td>77.9</td></tr><tr><td>Segformer</td><td>MiT-B3</td><td>44.6M</td><td>44.5G</td><td>81.9</td><td>79.8</td></tr><tr><td>Segformer</td><td>MiT-B5</td><td>82.0M</td><td>72.9G</td><td>83.9</td><td>81.5</td></tr></table>", "text": "Impact of the backbone.", "html": null, "type_str": "table", "num": null}, "TABREF6": {"content": "<table><tr><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"2\">Encoder</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"2\">Decoder</td><td/><td/><td/></tr><tr><td/><td>𝐻𝐻 4</td><td>×</td><td>𝑊𝑊 4</td><td>× 𝐶𝐶 1</td><td>𝐻𝐻 8</td><td>×</td><td>𝑊𝑊 8</td><td>× 𝐶𝐶 2</td><td>𝐻𝐻 16</td><td>×</td><td>𝑊𝑊 16</td><td>× 𝐶𝐶 3</td><td>𝐻𝐻 32</td><td>×</td><td>𝑊𝑊 32</td><td>× 𝐶𝐶 4</td><td>𝐻𝐻 4</td><td>×</td><td>𝑊𝑊 4</td><td>× 4𝐶𝐶</td><td>𝐻𝐻 4</td><td>×</td><td>𝑊𝑊 4</td><td>× 𝑁𝑁</td></tr><tr><td>Block</td><td>Transformer</td><td/><td/><td>Block</td><td>Transformer</td><td/><td/><td>Block</td><td>Transformer</td><td/><td/><td>Block</td><td>Transformer</td><td/><td/><td>Layer</td><td>MLP</td><td/><td/><td>MLP</td><td/><td/><td/></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"2\">F</td><td/><td/><td/><td colspan=\"2\">P</td></tr><tr><td/><td/><td/><td/><td/><td colspan=\"13\">Figure 7. The overall framework of encoder and decoder.</td><td/><td/><td/><td/><td/><td/></tr></table>", "text": "A.1. More Details of Encoder and Decoder", "html": null, "type_str": "table", "num": null}, "TABREF7": {"content": "<table><tr><td>Method</td><td colspan=\"3\">Num mAP(%) mIoU(%)</td></tr><tr><td>Category Labels</td><td>-</td><td>-</td><td>80.4</td></tr><tr><td>Without Augmented</td><td>-</td><td>-</td><td>76.8</td></tr><tr><td>Q2L(Liu et al., 2021)</td><td>-</td><td>96.6</td><td>77.9</td></tr><tr><td>MCAR(Gao &amp; Zhou, 2021)</td><td>-</td><td>94.3</td><td>76.2</td></tr><tr><td/><td>0</td><td>-</td><td>74.1</td></tr><tr><td>Prediction Map Filtering</td><td>100 500</td><td>--</td><td>75.2 75.7</td></tr><tr><td/><td>1000</td><td>-</td><td>74.3</td></tr></table>", "text": "The impact of class labels during inference.", "html": null, "type_str": "table", "num": null}, "TABREF8": {"content": "<table><tr><td/><td>λ l</td><td>λg</td><td>mIoU(%)</td></tr><tr><td>default</td><td>0.01</td><td>0.01</td><td>77.9</td></tr><tr><td/><td>0.005</td><td/><td>77.7</td></tr><tr><td/><td>0.02</td><td/><td>77.2</td></tr><tr><td/><td>0.05</td><td/><td>76.8</td></tr><tr><td/><td/><td>0.005</td><td>77.4</td></tr><tr><td/><td/><td>0.02</td><td>77.6</td></tr><tr><td/><td/><td>0.05</td><td>77.1</td></tr></table>", "text": "Impact of the weights of loss terms.", "html": null, "type_str": "table", "num": null}, "TABREF9": {"content": "<table><tr><td>method</td><td colspan=\"3\">bkg aero bike bird boat bottle</td><td>bus</td><td>car</td><td>cat</td><td colspan=\"6\">chair cow table dog horse mbike person plant sheep</td><td>sofa</td><td>train</td><td>tv</td><td>Mean</td></tr><tr><td>KernelCut (Tang et al., 2018b)</td><td>-</td><td>86.2 37.3 85.5 69.4</td><td>77.8</td><td colspan=\"4\">91.7 85.1 91.2 38.8 85.1 55.5 85.6</td><td>85.8</td><td>81.7</td><td>84.1</td><td>61.4</td><td>84.3</td><td>43.1 81.4 74.2</td><td>75.0</td></tr><tr><td>BPG (<PERSON> et al., 2019)</td><td colspan=\"2\">93.4 84.8 38.4 84.6 65.5</td><td>78.8</td><td colspan=\"4\">91.4 85.9 89.5 41.0 87.3 58.3 84.1</td><td>85.2</td><td>83.7</td><td>83.6</td><td>64.9</td><td>88.3</td><td>46.0</td><td>86.3 73.9</td><td>76.0</td></tr><tr><td>SPML (Ke et al., 2021)</td><td>-</td><td>89.0 38.4 86.0 72.6</td><td>77.9</td><td colspan=\"4\">90.0 83.9 91.0 40.0 88.3 57.7 87.7</td><td>82.8</td><td>79.1</td><td>86.5</td><td>57.1</td><td>87.4</td><td>50.5 81.2 76.9</td><td>76.1</td></tr><tr><td>Ours-MiT-B1</td><td colspan=\"2\">93.9 89.7 35.7 87.6 69.2</td><td>84.8</td><td colspan=\"4\">90.3 84.7 89.9 43.2 87.9 60.7 87.1</td><td>82.6</td><td>80.6</td><td>86.5</td><td>71.2</td><td>82.5</td><td>54.3 89.0 84.3</td><td>77.9</td></tr><tr><td colspan=\"3\">B.4. More Qualitative Results</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr></table>", "text": "Per-class comparison between our approach and others on PASCAL VOC 2012 val dataset.", "html": null, "type_str": "table", "num": null}}}}