{"paper_id": "QuoVadisTAD", "title": "Position: <PERSON><PERSON>, Unsupervised Time Series Anomaly Detection?", "abstract": "The current state of machine learning scholarship in Timeseries Anomaly Detection (TAD) is plagued by the persistent use of flawed evaluation metrics, inconsistent benchmarking practices, and a lack of proper justification for the choices made in novel deep learning-based model designs. Our paper presents a critical analysis of the status quo in TAD, revealing the misleading track of current research and highlighting problematic methods, and evaluation practices. Our position advocates for a shift in focus from solely pursuing novel model designs to improving benchmarking practices, creating non-trivial datasets, and critically evaluating the utility of complex methods against simpler baselines. Our findings demonstrate the need for rigorous evaluation protocols, the creation of simple baselines, and the revelation that state-of-the-art deep anomaly detection models effectively learn linear mappings. These findings suggest the need for more exploration and development of simple and interpretable TAD methods. The increment of model complexity in the state-of-the-art deeplearning based models unfortunately offers very little improvement. We offer insights and suggestions for the field to move forward.", "pdf_parse": {"paper_id": "QuoVadisTAD", "abstract": [{"text": "The current state of machine learning scholarship in Timeseries Anomaly Detection (TAD) is plagued by the persistent use of flawed evaluation metrics, inconsistent benchmarking practices, and a lack of proper justification for the choices made in novel deep learning-based model designs. Our paper presents a critical analysis of the status quo in TAD, revealing the misleading track of current research and highlighting problematic methods, and evaluation practices. Our position advocates for a shift in focus from solely pursuing novel model designs to improving benchmarking practices, creating non-trivial datasets, and critically evaluating the utility of complex methods against simpler baselines. Our findings demonstrate the need for rigorous evaluation protocols, the creation of simple baselines, and the revelation that state-of-the-art deep anomaly detection models effectively learn linear mappings. These findings suggest the need for more exploration and development of simple and interpretable TAD methods. The increment of model complexity in the state-of-the-art deeplearning based models unfortunately offers very little improvement. We offer insights and suggestions for the field to move forward.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Time series anomaly detection (TAD) is an active field of machine learning with applications across multiple industries. For instance, many real-world systems such as vehicles, manufacturing plants, robots, and patient monitoring systems, involve a large number of interconnected sensors producing a great amount of data over time that can be used to detect anomalous behaviour. The anomalies can manifest as single irregular points or groups of such points whose interpretation as anomalous might depend on the system's operational history or on the inter-connectivity among sub-modules.", "section": "Introduction", "sec_num": "1."}, {"text": "Given the complexity of the problem and inspired from the successes in other areas, such as natural language or audio processing, many state-of-the-art deep-learning architectures have been adjusted and applied to it. Such approaches aim to learn a latent representation of the normal time-series data, e.g. LSTM (<PERSON> et al., 2017) , Transformer (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2022) , and sometimes explicitly model the inter-dependency among the sub-components in the system, e.g. graph neural networks (Deng & Hooi, 2021; <PERSON> et al., 2021) . Based on the assumption that the anomalies constitute unseen patterns which will not be modelled during reconstruction of the series from the model, the difference between the original and reconstructed series is used to detect them.", "section": "Introduction", "sec_num": "1."}, {"text": "Although it is well intended, this line of research has never provided evidence of the necessity of deep-learning, which has been challenged namely in <PERSON><PERSON> et al. (2022) . The state-of-the-art (SOTA) deep-learning approaches proceeded to introduce models of increased complexity using questionable validation processes. Those processes involve unsuitable benchmark datasets (<PERSON>, 2022) and, most harmful to this field, the use of flawed evaluation protocols (<PERSON> et al., 2022) . The protocol which introduced the most pitfalls is the point adjustment (PA) applied on the point-wise F1 score which practically favors noisy predictions. It was gradually introduced in a series of papers (<PERSON> et al., 2018; <PERSON><PERSON> et al., 2020; <PERSON> et al., 2020; <PERSON> et al., 2019c) with the original intention of calibrating the anomaly detection threshold on a hold-out dataset, but it was subsequently demonstrated in <PERSON> et al. (2022) that uniformly random predictions outperform SOTA methods and their performance tends to one as the average length of the anomalies increases. Although using the standard F1 score without point-adjust avoids those pitfalls, it still leaves a gap by only focusing on point-wise time-stamp level detection versus anomaly instance level detection, which led to the introduction of new complementary rangebased metrics such as the ones in Ta<PERSON><PERSON> et al. (2018) , <PERSON> et al. (2023) .", "section": "Introduction", "sec_num": "1."}, {"text": "The goal of this paper is to guide the TAD community to-wards more meaningful progress through rigorous benchmarking practices and a focus on studying the utility of their models by drawing useful but simple baselines. We achieve this with the following contributions: 1.) We introduce simple and effective baselines and demonstrate that they perform on par or better than the SOTA methods, thus challenging the efficiency and effectiveness of increasing model complexity to solve TAD problems. 2.) We reinforce this position by reducing trained SOTA models to linear models which are distillations of them but still perform on par. Thus from the point of view of the TAD task on the current datasets, those models perform roughly a linear separation of the anomalies from the nominal data.", "section": "Introduction", "sec_num": "1."}, {"text": "Our code 1 is available on GitHub to easily run the baselines and benchmarks.", "section": "Introduction", "sec_num": "1."}, {"text": "Anomaly detection in time series data has been extensively studied, with methods ranging from univariate to multivariate and including complex deep-learning models (<PERSON> et al., 2019; <PERSON> et al., 2019; <PERSON> et al., 2020; <PERSON> et al., 2019a; <PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2018a; <PERSON> & <PERSON>, 2021; <PERSON> et al., 2021) . These models are trained to forecast or reconstruct presumed normal system states and then deployed to detect anomalies in unseen test datasets. The anomaly score defined as the magnitude of prediction or reconstruction errors serves as an indicator of abnormality at each time stamp. Model performance is often evaluated as a binary classification problem, with the anomaly scores thresholded into binary labels. A comprehensive review of anomaly detection methods can be found in (<PERSON><PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) .", "section": "Related Work", "sec_num": "2."}, {"text": "Classical machine learning methods: A basic approach to anomaly detection in time-series data involves treating sample points of each sensor as independent and using classical statistical methods on the individual univariate series. For instance, regression models are used for the prediction from other sensor measurements (<PERSON> et al., 2014) . Principal Component Analysis (PCA) is utilized for dimensionality reduction and reconstruction (<PERSON><PERSON><PERSON> et al., 2006) . Other methods for anomaly detection on time series data take temporal dependency or correlation among sensors into account. These include modeling families of hidden Markov chains (Patcha & Park, 2007) or graph theory (<PERSON><PERSON> et al., 2020) . Signal transformation (<PERSON><PERSON><PERSON><PERSON> et al., 2017) , isolation forest (Bandaragoda et al., 2018; <PERSON> et al., 2008) , Auto-Regressive Integrated Moving Average (ARIMA) (<PERSON><PERSON><PERSON> et al., 2010) and clustering (Ang<PERSON><PERSON> & <PERSON>uti, 2002; <PERSON><PERSON> et al., 2021; <PERSON><PERSON> et al., 2020) . Time-series discord discovery has recently emerged as a fa-1 Code: https://github.com/ssarfraz/QuoVadisTAD vored choice for univariate data analysis. A recent method MERLIN (<PERSON> et al., 2020 ) is considered to be the state-of-the-art for univariate anomaly detection, as it iteratively varies the length of a subsequence and searches for those that are greatly different from their nearest neighbors as candidates of abnormality. Also see (<PERSON><PERSON><PERSON><PERSON> et al., 2022) for a comprehensive performance comparison of different classical TAD methods on univariate data.", "section": "Related Work", "sec_num": "2."}, {"text": "Deep learning methods: Anomaly in time series might be hidden in peculiar dependencies among sub-modules in a system or over its operation history that are hard to detect with manual feature engineering. Modern deeplearning models that can learn temporal dependency via recursive networks (e.g. LSTM) or attention mechanisms (e.g. Transformer) or by explicitly representing the correlation among sensors (e.g. Graph Neural Networks) have been proposed as the cutting-edge methods for TAD. For instance, LSTM-VAE (Park et al., 2017) used a variational autoencoder that is based on LSTM and reconstructs the test data with variational inferences. DAGMM (<PERSON><PERSON> et al., 2018) utilized deep autoencoders and Gaussian mixture model to jointly model a low-dimensional representation which is then used to reconstruct each time stamp. It computes the reconstruction error for anomaly detection. OmniAnomaly (Su et al., 2019a) modeled the time series data as stochastic random process with variational autoencoders (VAE) and established reconstruction likelihood as an anomaly score. Another approach, USAD (<PERSON><PERSON> et al., 2020) , introduced a two-phase training paradigm in which two autoencoders and two decoders are trained under the adversarial game-style. Among the more recent methods that currently represent the state-of-the-art deep models on anomaly detection are GDN (Deng & Hooi, 2021) and TranAD (<PERSON><PERSON> et al., 2022) . GDN (Deng & Hooi, 2021 ) models the inter-connectivity among sensors as a graph and used graph attention network to forecast the sensor measurement. The deviation between true observation and model predictions is then used to quantify anomalies. TranAD (Tuli et al., 2022) is a transformer based approach that proposed a new transformer architecture for anomaly detection. It introduced several components with a two transformer-based encoder and decoders using multihead attention blocks. The approach then proposed a twophase training scheme utilizing adversarial and meta learning procedures. Another recent transformer based approach Anomaly Transformer (Xu et al., 2022) introduced a new attention block and a min-max loss which helps learn two separate series associations, one prior which aims to capture local associations which in cases of anomaly would be caused by the continuity around it and series associations which should encode deeper information about the temporal context. Overall both methods results in complicated schemes. A similar approach in designing a transformer based model along with meta learning objectives and optimal transport has been presented in (Li et al., 2023) .", "section": "Related Work", "sec_num": "2."}, {"text": "Aside from the anomaly detection approaches, many efforts has been put in creating useful anomaly detection benchmarks. Some recent studies, for instance (<PERSON>, 2022) have shown how some of these datasets suffer from potential flaws, such as triviality, unrealistic density of anomaly, or mislabeling.", "section": "Related Work", "sec_num": "2."}, {"text": "Among the numerous anomaly detection approaches presented in the past, there is often something consistent -they tend to overlook simpler baselines in pursuit of novelty. This leads to overly complex engineered solutions without much utility and a good rationale. Towards this end, we propose simple methods that exceed the performance of current best-published anomaly detection approaches. As a result, these baselines help us to understand the complexity of the underlying problem and provide a solid foundation for further investigation. Of note, our contribution is properly setting up these known methods and creating a set of strong baselines.", "section": "Methods", "sec_num": "3."}, {"text": "We introduce some notations which are used to formally define the task of unsupervised TAD and describe the methods used. The training data consist of a time series X = [x 1 , . . . x T ] ∈ R T •F which only contains non-anomalous timestamps. Here T is the number of timestamps and F the number of features. The test set, X = [x 1 , . . . x T ] ∈ R T •F contains both normal and anomalous timestamps and ŷ = [ŷ 1 , . . . , ŷ T ] ∈ {0, 1} T represents their labels, where ŷt = 0 denotes a normal and ŷt = 1 an anomalous timestamp t. Then the task of anomaly detection is to select a function f θ : X → R such that f θ (x t ) = ỹt estimates the anomaly value ŷt2 . The (potentially empty) set of parameters θ is estimated using the training data X. In most methods, usually an intermediate error vector function err θ : X → R F is estimated which computes vectors representing an error along all sensors, we also denote by E = err θ ( X) the predicted test error vectors.", "section": "Preliminaries", "sec_num": "3.1."}, {"text": "The error vectors E estimated from any of the methods provide a measure of the deviation of the test features from normality. Normalization of error vectors sometimes is necessary before detecting anomalies due to variations in error behavior across sensors. Two normalization methods are often used: scaling using robust statistics such as median and inter-quartile range (<PERSON> & <PERSON>, 2021) and scaling using mean and standard deviation. The choice of normalization approach can impact anomaly detection accuracy, and careful consideration should be given to the selected method. The impact of error vector normalization on datasets is demonstrated through an ablation study in section 4.4. Once the error vectors are normalized, the final output is a measure of the vector sizes. Given that we are working on the anomaly detection scenario, the most fitting metric is L ∞ which computes the largest absolute error between the different sensors, ∥e t ∥ ∞ = max i≤F {|e i t |}.", "section": "Preliminaries", "sec_num": "3.1."}, {"text": "Sensor range deviation: The range of sensor values observed during normal operation can be useful in identifying out-of-distribution (OOD) samples. Anomalies in time series data can occur when the sensor values deviate from their usual range. Therefore, if the sensor values in a test data point fall outside the observed range, it may indicate the presence of an anomaly. Formally this is defined as:", "section": "Proposed simple and effective baselines", "sec_num": "3.2."}, {"text": "f (x t ) = 0 if xt ∈ [min(X), max(X)] 1 otherwise", "section": "Proposed simple and effective baselines", "sec_num": "3.2."}, {"text": "This represents a minimum level of detection performance that any advanced method should be able to surpass.", "section": "Proposed simple and effective baselines", "sec_num": "3.2."}, {"text": "L2-norm: Magnitude of the observed time stamp: In the case of multivariate time series data, the magnitude of the vector at a particular timestamp may serve as a relevant statistic for detecting OOD samples. This can be easily computed by taking the L2-norm of the vector, thus f (x t ) = ∥x t ∥ 2 . By using the magnitude as an anomaly score, we have discovered that it can be an effective and robust baseline for identifying anomalies in multivariate datasets.", "section": "Proposed simple and effective baselines", "sec_num": "3.2."}, {"text": "NN-distance: Nearest neighbor distance to the normal training data: A sample that deviates from normal data should have a greater distance from it. Therefore, using the nearest-neighbor distance between each test time-stamp and the train data as an anomaly score can serve as a reliable baseline. In fact, in many cases, this method outperforms several state-of-the-art techniques.", "section": "Proposed simple and effective baselines", "sec_num": "3.2."}, {"text": "PCA reconstruction error: Our simplest reconstruction method can be seen as an outlier detection on a lower dimensional linear approximation of the train dataset single timestamp features.", "section": "Proposed simple and effective baselines", "sec_num": "3.2."}, {"text": "After centering the training set X on its mean, using PCA, we compute the principal components of its features. This defines an affine approximation of X centered on the origin which can be expressed by the eigenvector matrix U ∈ R F •F ′ , where F ′ < F is a fixed number of the first principle components. Then the test set X is transformed to ", "section": "Proposed simple and effective baselines", "sec_num": "3.2."}, {"text": "K V GCN-LSTM Block Figure 1. Proposed simple neural-network baselines X = XU T U ∈ R T •F and we consider the reconstruction error vectors E = err U ( X) = X -X.", "section": "Proposed simple and effective baselines", "sec_num": "3.2."}, {"text": "There are two ways to interpret this transform. The first one is as a linear reconstruction of the test data, which is equivalent to using a linear autoencoder trained with the mean squared error loss on the training set, see (<PERSON><PERSON><PERSON> & <PERSON>, 1988) and (<PERSON><PERSON><PERSON> & <PERSON>, 1989) . The second way is to interpret it as the projection of each vector of X to the linear subspace S = span(cols(U)) ⊂ R F formed by the principal components in U. This interpretation highlights the linearity and simplicity of the method as each error vector e t connects x t with S and is perpendicular to S, thus expresses the distance between x t and S.", "section": "Proposed simple and effective baselines", "sec_num": "3.2."}, {"text": "Contemporary anomaly detection techniques based on deep learning utilize modern neural networks to create solutions with varying levels of sophistication. Among the commonly employed architectures are auto-encoders (AE), long short-term memory (LSTM) networks, multi-layer perceptrons (MLPs), graph convolution networks (GCN), and Transformers. These neural network structures serve as the foundational components for designing intricate models intended for anomaly detection. In order to provide context for the usefulness of the more elaborate solutions, we utilize these architectures in their most basic form as a set of baselines. It is reasonable to expect that any solution which employs these as foundational components should perform better, provided they are trained on rich enough datasets of normal examples. Our experiments demonstrate that, in most cases, these basic baselines perform better than models that incorporate a combination of these structures for the purpose of anomaly detection. Therefore, establishing such baselines may help understand the rationale behind the development of more complex models.", "section": "Proposed neural network blocks baselines", "sec_num": "3.3."}, {"text": "1-layer linear MLP as auto-encoder: As the first simplest neural baseline we use a single hidden-layer MLP without any activation as an auto-encoder.", "section": "Proposed neural network blocks baselines", "sec_num": "3.3."}, {"text": "Single block MLP-Mixer: Among the more modern variants of MLPs, the MLPMixer (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) has been shown to perform quite well on many vision problems. The architecture includes several MLP layers, called MLP-Mixer blocks. Each MLP-Mixer block consists of two sub-layers: a token-mixing sub-layer and a channelmixing sub-layer. These operate on the spatial dimension and the channel dimension of the input feature maps. The entire architecture consists of stacking several MLP-Mixer blocks, allowing the network to capture increasingly complex spatial and cross-channel dependencies in the input.", "section": "Proposed neural network blocks baselines", "sec_num": "3.3."}, {"text": "We include a single standard block of MLPMixer as our baseline.", "section": "Proposed neural network blocks baselines", "sec_num": "3.3."}, {"text": "Single Transformer block: Since transformers are increasingly used in several recent anomaly detection methods, we use a basic transformer block with one single-head attention and one fully connected layer as a feed-forward output. This serves as the simplest and basic single transformer block baseline. Figure 1 illustrates the proposed baseline neural network blocks. These baseline models are trained and compared in both reconstruction and forecasting modes.", "section": "Proposed neural network blocks baselines", "sec_num": "3.3."}, {"text": "Univariate time series data consist of a single observation at each timestamp, and most deep-learning methods designed for multivariate data are not directly applicable. Consequently, the most effective approaches for analyzing univariate data are typically focused on identifying unusual subsequences, or discords, within the time series. State-ofthe-art discord discovery methods, for instance (<PERSON> et al., 2020) , focus on optimizing the complexity and parameters of such methods that typically involve comparing windowed distances between timestamps. In this work, we use a similar yet effective representation for univariate time series data that allows the discovery of anomalies. Specifically, we represent each timestamp as a vector in R w+1 , where w denotes the number of preceding time stamps. This representation can be efficiently computed in a sliding window fashion and has linear time complexity, making it efficient for practical use. In section A.2.1, we demonstrate that the impact of the window size on performance is relatively low and a small fixed window of w = 4 suffice for the considered univariate datasets.", "section": "Univariate time series representation", "sec_num": "3.4."}, {"text": "A lot of papers introduced and criticised different metrics.", "section": "Evaluation metrics", "sec_num": "3.5."}, {"text": "In our view, anomaly detection shares a lot with object detection and semantic segmentation in computer vision, therefore it would need two metrics to fully capture model performance. The point-wise which captures the quality of the detection of individual anomalies and range-wise which expresses the quality of the anomaly segmentation.", "section": "Evaluation metrics", "sec_num": "3.5."}, {"text": "For the point-wise anomaly detection, we use the standard F1 score, which actually equals to the 1-dimensional Dice coefficient. For completeness, we also include the flawed and commonly used F1 score with point adjustment denoted as F1 PA . For the range-wise metrics, we followed the work in this direction starting with the Time-series precision and recall metrics defined in (<PERSON><PERSON><PERSON> et al., 2018) and then corrected for bias in (<PERSON> et al., 2023) and we use the latter to compute an F1 score denoted as F1 T .", "section": "Evaluation metrics", "sec_num": "3.5."}, {"text": "Below are the definitions of the three scores we use together with the corresponding testing protocols: F1 PA : The final F1 score is computed exactly as before. This metric is different in its evaluation protocol which adjusts the predictions using the ground truth. Namely, for every contiguous anomaly interval A = [t 1 , . . . , t 2 ] in the ground truth, if there is at least one i ∈ A such that ỹi = 1, then for every j ∈ A, ỹj is set to 1. In other words, if an anomaly interval is hit once by the predictions, then all predictions in the interval are corrected to match the ground truth.", "section": "Evaluation metrics", "sec_num": "3.5."}, {"text": "F1: Let [ŷ 1 , . . . , ŷ T ]", "section": "Evaluation metrics", "sec_num": "3.5."}, {"text": "F1 T : Let A, P be respectively the set of all ground truth and prediction anomaly intervals. Also let P A = {P ∈ P | |A ∩ P | > 0} be the prediction intervals intersected by A. Then precision and recall are defined as follows:", "section": "Evaluation metrics", "sec_num": "3.5."}, {"text": "P rec T (A, P) = 1 |P| P ∈P γ(|A P |, P ) | A ∩ P | |P | Rec T (A, P) = 1 |A| A∈A γ(|P A |, A) | P ∩ A| |A|", "section": "Evaluation metrics", "sec_num": "3.5."}, {"text": "The above definition is consistent with both (<PERSON><PERSON><PERSON> et al., 2018) and (<PERSON> et al., 2023) . The full formula in the latter paper for recall is", "section": "Evaluation metrics", "sec_num": "3.5."}, {"text": "Rec T (A, P) = 1 |A| A∈A [α1(|P A | > 0) +(1 -α)γ(|P A |, A) P ∈P t∈P ∩A δ(t -min A, |A|) t∈A δ(t -min A, |A|) ],", "section": "Evaluation metrics", "sec_num": "3.5."}, {"text": "where 0 ≤ α ≤ 1, δ ≥ 1 and P rec T (A, P) = Rec T (P, A). (<PERSON> et al., 2023) proposed to fix the parameters α, δ to 0 and a constant function, in order to derive their formula for γ.", "section": "Evaluation metrics", "sec_num": "3.5."}, {"text": "Under this assumption, we simplified those formulas to make them more comprehensible. Here we use the corrected γ(n, A) = ( |A|-1 |A| ) n-1 which guarantees that recall is increasing relative to the threshold of the anomaly detector. To provide some intuition, e.g. the recall computes an average of the fraction of ground truth intervals overlapped by the prediction which expresses the amount of discovery success. Every term is weighted though by γ which decreases in value as multiple predictions hit the same ground truth interval, thus penalizing duplicates. Note that P rec T (A, P) = Rec T (P, A), i.e. precision measures the recall of prediction intervals by the ground truth. Finally, the F1-score denoted by F 1 T is defined as usual using P rec T and Rec T . The F1 scores are calculated using the best threshold computed on the test dataset and this threshold is also used to compute the corresponding precision and recall. Though we are not content with the threshold tuning, we choose this in order to follow the same protocol used in the published methods we have included for comparison. Here, it is important to also include the Area Under the Precision Recall Curve (AUPRC) metric instead of only the F1 score obtained with an optimal threshold. AUPRC provides a more realistic estimation of how well a method would perform in practical settings, where an estimated threshold based on a hold-out set would be used. In our appendix, we include tables ( 9, 10, 11, 12) with the separate precision, recall, and AUPRC values.", "section": "Evaluation metrics", "sec_num": "3.5."}, {"text": "Time series datasets: Overall, we used six commonly used benchmark datasets in our study. Here, we report the details (Table 1 ) and results from three multivariate datasets (SWaT, WADI, and SMD) and four univariate datasets (UCR/Internal Bleeding). The other two commonly used multivariate datasets (SMAP and MSL) have been identified in (Wu & <PERSON>, 2022) as potentially flawed containing trivial and unrealistic density of anomalies. For completeness, the descriptions and results of these two datasets are included in the appendix section A.3.", "section": "Analysis", "sec_num": "4."}, {"text": "Univariate HexagonML (UCR) datasets -InternalBleeding (IB) (Guillame-Bert & Dub<PERSON>ski, 2017): contains four univariate traces as the vital signs (arterial blood pressure). The anomalies are synthetic by adding a series of sine waves to one cycle or by injecting random numbers to a certain segment (Figure 2 ). The unique and well-controlled anomalies in each trace allow a clean and sound evaluation among different approaches (<PERSON> & <PERSON>, 2022) .", "section": "Analysis", "sec_num": "4."}, {"text": "Secure Water Treatment (SWaT) (<PERSON><PERSON> & <PERSON>, 2016) and Water Distribution (WADI) (<PERSON> et al., 2017) datasets: contain sensor measurements of a water treatment test-bed. Although SWaT is commonly used as a benchmark in recent publications, it should be noted that its use as a benchmark should be discontinued as it is flawed and unreliable <PERSON><PERSON><PERSON> (personal communication, 7 May, 2024), see also (<PERSON> et al., 2023) . The WADI dataset demonstrates the inconsistency in reporting performance comparisons in the TAD literature. The complete set of WADI contains 127 sensors (denoted as WADI-127 in our study). However, some recent methods (<PERSON><PERSON> et al., 2022; Deng & Hooi, 2021; <PERSON> et al., 2022; <PERSON> et al., 2021; <PERSON> & <PERSON>, 2021 ) use a specific subset of sensors when making comparisons without specifying the exact used sensors nor the reasons for such selection. Furthermore, in many cases, the selected subsets are inconsistent among competing methods. In order to provide a fair overview of this impact on performance, we conducted our experiments on all the 127 WADI sensors (denoted as WADI-127) and on the subset of 112 sensors used in some recent studies (<PERSON>g & Hooi, 2021 ) (denoted as WADI-112), separately.", "section": "Analysis", "sec_num": "4."}, {"text": "Server Machine Dataset (SMD) (<PERSON> et al., 2019c) : contains 38 sensors from 28 machines for 10 days. Table 1 reports the average length of each trace. Following the protocol, all models are trained on each machine separately and the results are averaged from 28 different models.", "section": "Analysis", "sec_num": "4."}, {"text": "Evaluation: We evaluate several state of the art representative deep learning based methods on commonly used timeseries benchmarks. To clearly show their utility, we evaluate these 1). under point-adjust F1 PA which is the common metric increasingly used in recent proposals. 2.) standard point-wise F1 and 3.) Time-series range-wise metric F1 T . See section 3.5 for the definitions. To highlight the prevalent use of flawed point-adjust F1 PA , similar to (<PERSON> et al., 2022) , we also evaluate a random prediction: Random: The F1 PA protocol considers the whole interval of an anomaly as correctly predicted, as soon as the prediction considers a single point of the interval as anomalous.", "section": "Analysis", "sec_num": "4."}, {"text": "The random prediction directly shows that, under the pointadjust evaluation, methods might achieve high scores just because they have very noisy outputs. In the random baseline setting, each timestamp is predicted anomalous with probability 0.5 and we report the score achieved over five independent runs.", "section": "Analysis", "sec_num": "4."}, {"text": "In this section we summarize our data preprocessing steps and the hyperparameters used to train the models. The features were scaled to the interval [0, 1] in the training dataset and the learned scaling parameters were used to scale the testing dataset. For all of our NN baselines, when trained in forecasting mode, we used a time window of size 5. We used a 90/10 split to make the train and the validation set. The validation set is only used for early stopping to avoid over-fitting and the Adam optimizer with learning rate 0.001 and a batch size of 512 were used.", "section": "Model setup", "sec_num": "4.1."}, {"text": "PCA reconstruction error: For multivariate data, this method uses the first 30 principal components when data", "section": "Model setup", "sec_num": "4.1."}, {"text": "SWaT WADI 127 WADI 112 SMD F 1 P A F 1 F 1 T F 1 P A F 1 F 1 T F 1 P A F 1 F 1 T F 1 P A F 1 F 1 T", "section": "Model setup", "sec_num": "4.1."}, {"text": "MERLIN (<PERSON> et al., 2020) 0.934 0.217 0.286 0.560 0.335 0.354 0.699 0.473 0.503 0.886 0.384 0.473 DAGMM (<PERSON><PERSON> et al., 2018) 0.830 0.770 0.402 0.363 0.279 0.406 0.829 0.520 0.609 0.840 0.435 0.379 OmniAnomaly (Su et al., 2019b) 0.831 0.773 0.367 0.387 0.281 0.410 0.742 0.441 0.496 0.804 0.415 0.353 USAD (<PERSON><PERSON> et al., 2020) 0.827 0.772 0.413 0.375 0.279 0.406 0.778 0.535 0.573 0.841 0.426 0.364 GDN (Deng & Hooi, 2021) 0.866 0.810 0.385 0.767 0.347 0.434 0.833 0.571 0.588 0.929 0.526 0.570 TranAD (<PERSON><PERSON> et al., 2022) 0.865 0.799 0.425 0.671 0.340 0.353 0.680 0.511 0.589 0.827 0.457 0.390 AnomalyTransformer (<PERSON> et al., 2022) 0 has more than 50 sensors and 10 otherwise. On univariate datasets, the first 2 principal components with a window size of 5 are used.", "section": "SOTA methods", "sec_num": null}, {"text": "1-layer Linear MLP: A hidden layer of size 32 is used.", "section": "SOTA methods", "sec_num": null}, {"text": "Single block MLP-Mixer and Single Transformer block both use an embedding of 128 for the hidden layer.", "section": "SOTA methods", "sec_num": null}, {"text": "The dimension for the GCN output nodes is set to 10 and for LSTM layer to 64 units.", "section": "1-layer GCN-LSTM block:", "sec_num": null}, {"text": "Our neural network baselines are trained in the forecasting mode, similar to most other methods we are comparing with. We also provide their performance for the reconstruction mode in the appendix section A.2.2.", "section": "1-layer GCN-LSTM block:", "sec_num": null}, {"text": "Hyperparameter sensitivity: Most of the simple baselines don't have tunable hyperparameters. The only exceptions are the projection dimension of the PCA method and the sliding window for univariate series. We have included their ablations in sections 4.5 and A.2.1. We trained our neural network baseline models using the same hyperparameters as stated above on all multivariate datasets. The purpose of this analysis was to demonstrate that even with basic hyperparameters, these simple neural networks can achieve comparable performance to SOTA deep learning models. The fact that the hyperparameters of the SOTA models were optimized for each respective datasets, while the simple NN baseline models used the same set of hyperparameters, highlights less reliance on dataset-specific tuning.", "section": "1-layer GCN-LSTM block:", "sec_num": null}, {"text": "Published SOTA methods: All methods were trained with the hyper-parameters recommended in their respective papers, where possible, with their official implementations or the implementations provided in (<PERSON><PERSON> et al., 2022) . GDN (Deng & Hooi, 2021) on WADI-112 is not re-trained since the authors provided the trained checkpoint of their official model.", "section": "1-layer GCN-LSTM block:", "sec_num": null}, {"text": "Table 2 outlines the model performance on the three multivariate benchmark datasets, SWaT, WADI, and SMD.", "section": "Model performance overview", "sec_num": "4.2."}, {"text": "First, it is evident that all methods have higher scores on the predominantly used point-adjusted F 1 P A metric including the random prediction which performs better in almost all comparisons. This artificial advantage created by pointadjust is not present on the pure F1 score protocols which do not favour noisy random predictions. On both standard point-wise F 1 and range-wise F 1 T metrics, the simple baselines such as PCA reconstruction error performs better on all datasets while other baselines such as 1-NN distance and L2-norm are often very close to the best performing methods. Furthermore, the NN-baselines in most cases outperform the more complex SOTA deep models which are build using these as basic building blocks. This is a strong evidence that the complicated solutions introduced to solve the TAD task do not provide a benefit compared to such simple baselines. Finally, one can notice the interplay between the point-wise and range-wise metrics. In datasets like SWAT, where there is a small number of long anomaly intervals, the F 1 score is much higher than the F 1 T score, on noisy datasets with more consistent anomaly lengths, like WADI 127, F 1 T is tendentially higher, while on cleaner datasets with frequent short anomalies, like univariate UCR datasets, the two scores are comparable.", "section": "Model performance overview", "sec_num": "4.2."}, {"text": "Table 3 provides a comparison on univariate UCR datasets with our simple baselines. Here we include two representative univariate TAD methods, a highly effective classic method Local Outlier Factor (LOF) (<PERSON><PERSON><PERSON><PERSON> et al., 2000) and a more recent SOTA method Merlin (<PERSON> et al., 2020) . As shown in Figure 2 the normal periodical phase-UCR/IB-16 UCR/IB-17 UCR/IB-18 UCR/IB-19 (<PERSON><PERSON><PERSON><PERSON> et al., 2000) 0.878 0.476 0.476 1.000 0.959 0.955 1.000 0.915 0.911 1.000 0.857 0.857 MERLIN (<PERSON> et al., 2020) 1.000 0.846 0.846 1.000 0.987 0.987 1.000 0.795 0.795 1.000 0.870 0.870", "section": "Model performance overview", "sec_num": "4.2."}, {"text": "F 1 P A F 1 F 1 T F 1 P A F 1 F 1 T F 1 P A F 1 F 1 T F 1 P A F 1 F 1 T LOF", "section": "Model performance overview", "sec_num": "4.2."}, {"text": "Random 0.151 0.005 0.030 0.941 0.041 0.116 0.887 0.039 0.039 0.488 0.030 0.030 Sensor range deviation 0.000 0.003 0.000 0.902 0.085 0.094 0.000 0.038 0.038 0.000 0.004 0.004 L2-norm 0.014 0.011 0.021 0.276 0.058 0.164 0.241 0.061 0.061 0.028 0.017 0.017 1-NN distance 0.828 0.786 0.786 1.000 0.973 0.969 1.000 0.889 0.889 1.000 0.870 0.870 PCA Error 0.889 0.750 0.750 1.000 0.974 0.974 1.000 0.990 0.990 1.000 1.000 1.000", "section": "Simple baselines", "sec_num": null}, {"text": "Table 3 . Comparison of simple baselines on four univariate UCR/InternalBleeding datasets. shift and magnitude changes, which are considered normal in the light of physiology, are misclassified as anomalies by such methods in contrast to the simple PCA-Error baseline.", "section": "Simple baselines", "sec_num": null}, {"text": "The consistently better results of the simple methods raises the question of what type of functions are learned by the more complicated deep learning models. To investigate this, we try to approximate the behavior of the most prominent of the deep learning models by linear functions. We achieve this by performing a simple form of distillation.", "section": "Analysis of the deep models learned function", "sec_num": "4.3."}, {"text": "Given a deep learning model M θ trained on the training data X, we compute its predictions M (X) ⊂ R F and then train a linear model L on the data/target tuple (X, M (X)) using a mean squared error (MSE) loss. The linear model in this case is simply a 1-layer perceptron. Upon evaluating both M and L on the test set on the anomaly detection task, we observed that their scores are very close and they exhibited high agreement on their predictions. this with the linear model L marked as 'Line' and the corresponding deep learning model M marked as 'Orig'. The performance of distilled linear version of the complex models suggests that even though the learned functions may be complex and may improve forecasting, their ability to distinguish anomalies can still be effectively captured by linearizing them.", "section": "Analysis of the deep models learned function", "sec_num": "4.3."}, {"text": "Anomaly detection methods for multivariate datasets often employ normalization and smoothing techniques to address abrupt changes in prediction scores that are not accurately predicted. However, the choice of normalization method before thresholding can impact performance on different datasets. In Table 5 , we compared the performance with and without normalization. We consider two normalization methods, mean-standard deviation and median-IQR, on two datasets. Our analysis shows that median-IQR normalization, which is also utilized in the GDN (Deng & Hooi, 2021 ) method, improves performance on noisier datasets such as WADI. In Table 2 , we have presented the best performance achieved by each method, including our baselines and considered state-of-the-art models, using either none or one of these normalisation, whichever is applicable.", "section": "Ablation: Impact of normalization", "sec_num": "4.4."}, {"text": "On all the multivariate datasets with more than 50 sensors (i.e., SWaT and WADI) our PCA Error baseline approach utilized the first 30 eigenvectors for the PCA projection. In Figure 3 , we present the performance as a function of vary- ing PCA projection dimensions. It is observed that higher projection dimensions may be more beneficial for WADI (127-dimensional) compared to SWaT (51-dimensional). However, the optimal projection dimension should be determined using a validation set as it may impact performance. Unlike more sophisticated techniques with several hyperparameters specifically configured for each dataset, the baseline approach of using PCA with a fixed number of eigenvectors is relatively simple and easily tunable.", "section": "Ablation: PCA Error projection dimension", "sec_num": "4.5."}, {"text": "As we have demonstrated, a plethora of deep learning approaches introduced to solve the task of TAD were outperformed by simple neural networks and linear baselines. Furthermore, when distilling some of those methods to linear models, their performance remained almost unchanged.", "section": "Quo vadis", "sec_num": "5."}, {"text": "There could be several causes for this issue for example the over-fitting on the normal data or the existence of too high aleatoric uncertainty which makes it hard to separate the difficult anomalies from normal sections. In any case, the main takeaway is that those methods, though potentially useful for other time-series tasks such as forecasting, do not bring much additional value for the task of TAD and their complexity is definitely not justified. What is even more worrisome, is that they managed to create up to now an illusion of progress due to the use of a flawed evaluation protocol, inadequate metrics and the lack or low quality of benchmarking with simpler methods.", "section": "Quo vadis", "sec_num": "5."}, {"text": "We cannot stress enough the fact that almost all the recent deep-learning based methods use the point-adjust postprocessing step often without clearly stating this. Under this evaluation these models implicitly optimize for near random predictions where their high performance is used as evidence of their proposed model's utility. An example of this trend presented at recent leading Machine Learning venues is (<PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2023) . Another common malpractice, is the use of mismatched evaluation metrics in tables i.e., applying pointadjust and directly comparing their results to other methods which were scored without it. Similar issues are observed in dataset discrepancies like the introduction of new versions of a dataset which use a subset of the sensors and result in higher scores.", "section": "Quo vadis", "sec_num": "5."}, {"text": "Aside from exposing the limitations of these methods, we provide a comprehensive set of simple benchmarks which can help re-start investigations in TAD starting on a solid baseline. We think that those methods will pinpoint which anomalies are easy to detect and which ones are the challenging ones that should be detected if any progress is to be made. This is further reinforced by the fact that there seem to be a high agreement between detected and undetected anomalies between all methods investigated. We provide an analysis of this agreement in the appendix section A.1. This agreement leads us to believe that the current datasets used in TAD are, in some sense, simultaneously too hard and too easy. The fact that so many complex deep learning architectures have been developed to tackle the hard anomalies in those datasets, but failed, is unsatisfactory, but maybe not unexpected. More comprehensive datasets with a spread spectrum of difficulty in anomalies could provide an incremental improvement path and means of properly comparing methods.", "section": "Quo vadis", "sec_num": "5."}, {"text": "Furthermore, we believe that evaluation using both pointwise and range-wise methods will help better compare methods and identify their strengths and weaknesses.", "section": "Quo vadis", "sec_num": "5."}, {"text": "We hope our work will help improve the research efforts on TAD by triggering focus on the introduction of new and richer datasets, increasing awareness of limitations of current evaluation protocols, and encouraging caution in the premature adoption of complex tools for the task.", "section": "Quo vadis", "sec_num": "5."}, {"text": "\"This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.\" (IOU) of the index sets H ỹ1 , H ỹ2 for r. Finally, the average agreement between two predictions ỹ1 and ỹ2 is the mean of their agreements over all the thresholds from 0.2 to 0.95 with step 0.05.", "section": "Impact Statement", "sec_num": null}, {"text": "In Figures 4a and 4b the matrices of the agreements between all models and the ground truth are displayed for the SWAT and WADI-112 datasets. In both cases, the agreement between different models is much higher compared to the agreement to the ground truth, indicating that the models learn to recognize similar anomalies. Only the GDN model and even more the PCA Error baseline seem to have a comparably higher agreement with the ground truth.", "section": "Impact Statement", "sec_num": null}, {"text": "A.2.1. ABLATION WINDOW SIZE FOR UNIVARIATE DATA As outlined in section 3.2 of the main paper, we created an effective univariate data representation by concatenating past observations with the current timestamp using a sliding window approach. We discovered that this basic representation yielded effective results with a window size of w = 4 leading to a 5-dimensional representation space. Figure 5 displays the performance impact based on the window size. This plot illustrates that a smaller window over 4-5 past observations is a reasonable choice for the UCR datasets, while larger window dimensions do not add any further advantage. We opted to use our simple 1-NN distance approach and varied the window sizes to avoid manipulating any other parameters.", "section": "A.2. Additional evaluations/ablations", "sec_num": null}, {"text": "In our main paper, we demonstrated the effectiveness of our simple neural network baselines when trained in forecasting mode, which is in line with most state-of-the-art deep learning models we compared with. During training, the output before the final target dense regression layer has a shape of (batch-size, sequence, embedding-dim). In forecasting mode, we use a 1-D global average pooling to project it to (batch-size, 1, embedding-dim). However, we can skip the averagepooling operation and train these models in a reconstruction (auto-encoding) fashion. For completeness, we present their performance in reconstruction mode in Table 6 . Our results show that the performance of these models in reconstruction mode is comparable to that in forecasting mode, particularly considering the impact of random seed between training runs. Therefore, there does not appear to be any significant advantage in training these models in forecasting mode, at least for the datasets we considered.", "section": "A.2.2. NN-BASELINES: RECONSTRUCTION VS FORECASTING MODE", "sec_num": null}, {"text": "Finally, we provide tables which contain the detailed scores of all models in terms of precision, recall, F1-score and area under the precision-recall curve (AUPRC). For the multivariate time series datasets, Table 9 shows the evaluation under point-wise metrics; Table 10 shows the evaluation under time series range-wise metrics (<PERSON> et al., 2023) . Similarly, for the univariate datasets, Table 11 evaluates under point-wise and Table 12 provides the performance under range-wise metrics.", "section": "A.2.3. DETAILED PERFORMANCE COMPARISON", "sec_num": null}, {"text": "A.3. Performance of our simple baselines on SMAP and MSL datasets Soil Moisture Active Passive (SMAP) and Mars Science Laboratory (MSL) datasets, collected from a spacecraft of NASA (<PERSON><PERSON><PERSON> et al., 2018b) , are another two widely utilized benchmark datasets in the literature. The SMAP dataset contains information on soil samples and telemetry of the Mars rover; the MSL dataset comes from the actuator and sensor data for the Mars rover itself. Although these benchmark datasets are widely used in the literature, their quality and validity suffer from several pitfalls, such as triviality, mislabeling, and unrealistic density of anomaly (see <PERSON> (2022) for details). The statistics profile of each dataset is listed in Table 7 . Since each dataset contains traces with various lengths in both the training and test sets, we report the average length of traces and the average number of anomalies among all traces per dataset. We also report the total number of data points and anomalies per dataset for the clarity of comparison in the literature.", "section": "A.2.3. DETAILED PERFORMANCE COMPARISON", "sec_num": null}, {"text": "Dataset (<PERSON> et al., 2019a) 0.899 0.207 0.805 0.227 USAD (<PERSON><PERSON> et al., 2020) 0.927 0.211 0.818 0.228 GDN (Deng & Hooi, 2021) 0.903 0.217 0.708 0.252", "section": "A.2.3. DETAILED PERFORMANCE COMPARISON", "sec_num": null}, {"text": "Table 8 . Simple baselines outperform the SOTA deep-learning models on MSL and SMAP datasets. SOTA model performance is taken from <PERSON> et al. (2022) . Bold: the best performance; underline: the second-best performance.", "section": "A.2.3. DETAILED PERFORMANCE COMPARISON", "sec_num": null}, {"text": "Mercedes-Benz Tech Innovation, Ulm, Germany", "section": "", "sec_num": null}, {"text": "Karlsruhe Institute of Technology, Karlsruhe, Germany. Correspondence to: <PERSON><PERSON> <<EMAIL>>. Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "section": "", "sec_num": null}, {"text": "The range of ỹt values may differ from ŷt ∈ 0, 1, necessitating thresholding before obtaining actual predictions. Typically, the threshold which yields the best score on the training or validation data is selected.", "section": "", "sec_num": null}], "back_matter": [{"text": "In the following appendix, we present several analyses and ablation studies related to the results discussed in the main paper. It is structured as follows:1. Analysis: We analyze the agreement on the detected anomalies between the different models (Figures 4a and 4b ).2. Additional evaluations/ablations: Several studies are presented related to the evaluation of the model performances:• Ablation window size for Univariate data: We show the impact of the sliding window size on the performance of our simple baselines on univariate data (Figure 5 ). • NN-baselines: reconstruction vs forecasting mode: We show the performance of our neural network baselines when trained in reconstruction and forecasting mode (Table 6 ). • Detailed performance comparison: At the end, we include detailed tables (Table 9, Table 10, Table 11, Table 12) with performance comparison of all methods reporting their F1, precision, recall and AUPRC under both standard point-wise and time-series range-wise metrics.3. Performance of our simple baselines on SMAP and MSL datsets: We include a comparison of our simple baseline methods and various SOTA methods on the additional multivariate SMAP and MSL datasets (Table 8 ).", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "We have noticed a very high agreement on the anomalies detected by the different methods. Those agreements are especially pronounced between the SOTA deep learning methods. In order to quantify them, we compute a score similar to mAP in object detection which measures the agreement between two different predictions restricted to the ground truth anomaly intervals. The score is defined as follows: For a given prediction ỹ and a hit ratio threshold r, the detected anomaly intervals is the index list H ỹ = {i 1 , . . . i L } ⊆ [1, L] of intervals for which the prediction has hit ratio above r. For two different predictions ỹ1 and ỹ2 , the agreement between them on a given hit ratio threshold r is defined as the intersection over union (<PERSON><PERSON><PERSON> et al., 2000) 0.476 0.555 0.416 0.196 0.959 0.955 0.963 0.944 0.916 0.920 0.911 0.832 0.857 0.818 0.900 0.939 MERLIN (<PERSON> et al., 2020) ", "section": "A.1. Analysis of model agreement on the detected anomalies", "sec_num": null}], "ref_entries": {"FIGREF0": {"fig_num": null, "type_str": "figure", "text": "be the ground truth per time-stamp on the test set and [ỹ thr 1 , . . . , ỹthr T ] the corresponding predictions set to 1 when ỹi > thr else to 0. The hits are defined as T P thr = |{i ≤ T | ỹthr i = ŷi }|, F P thr = |{i ≤ T | ỹthr i = 1 and ŷi = 0}| and F N thr = |{i ≤ T | ỹthr i = 0 and ŷi = 1}|. Then the precision P rec thr , recall Rec thr and F1-score F 1 thr are defined as usual based on those values. The final score is then F 1 = max thr∈R F 1 thr .", "uris": null, "num": null}, "FIGREF1": {"fig_num": "2", "type_str": "figure", "text": "Figure 2. Visual comparison: The gray shaded areas denote the ground truth anomalies. (a) UCR/IB-18 dataset with a series of sine waves added as anomaly. (b) UCR/IB-19 dataset with random numbers added as anomaly.", "uris": null, "num": null}, "FIGREF2": {"fig_num": "3", "type_str": "figure", "text": "Figure 3. Point-wise F1 score as a function of the PCA dimension for the PCA Error method, evaluated on the SWAT and WADI 127 datasets.", "uris": null, "num": null}, "FIGREF3": {"fig_num": "5", "type_str": "figure", "text": "Figure 5. Impact of sliding window size to generate univariate data representation on the two UCR dataset traces UCR/IB-17 and UCR/IB-18.", "uris": null, "num": null}, "TABREF2": {"content": "<table><tr><td>Dataset</td><td>Sensors (traces)</td><td>Train</td><td>Test</td><td>#Anomalies (%)</td></tr><tr><td>UCR/IB-16</td><td>1</td><td>1200</td><td>6301</td><td>12 (0.19%)</td></tr><tr><td>UCR/IB-17</td><td>1</td><td>1600</td><td>5900</td><td>111 (1.88%)</td></tr><tr><td>UCR/IB-18</td><td>1</td><td>2300</td><td>5200</td><td>102 (1.96%)</td></tr><tr><td>UCR/IB-19</td><td>1</td><td>3000</td><td>4500</td><td>10 (0 .22%)</td></tr><tr><td>SWaT</td><td>51</td><td colspan=\"3\">47520 44991 4589 (12.20%)</td></tr><tr><td>WADI-127</td><td>127</td><td colspan=\"2\">118750 17280</td><td>1633 (9.45%)</td></tr><tr><td>WADI-112</td><td>112</td><td colspan=\"2\">118750 17280</td><td>918 (5.31%)</td></tr><tr><td>SMD</td><td>38 (28)</td><td colspan=\"2\">25300 25300</td><td>1050 (4.21%)</td></tr></table>", "type_str": "table", "text": "The statistical profile of the datasets in the experiment.", "num": null, "html": null}, "TABREF3": {"content": "<table><tr><td/><td/><td/><td/><td colspan=\"2\">.941 0.765 0.331 0.560 0.209 0.219 0.817 0.503 0.555</td><td>0.923 0.426 0.351</td></tr><tr><td/><td/><td/><td>Random</td><td>0.963 0.218 0.217</td><td>0.783 0.101 0.106</td><td>0.907 0.101 0.106</td><td>0.894 0.080 0.080</td></tr><tr><td>Simple</td><td>baselines</td><td/><td>Sensor range deviation L2-norm 1-NN distance PCA Error</td><td colspan=\"2\">0.234 0.231 0.230 0.129 0.101 0.098 0.632 0.465 0.526 0.847 0.782 0.366 0.353 0.281 0.410 0.749 0.513 0.607 0.847 0.782 0.372 0.372 0.281 0.410 0.751 0.568 0.618 0.895 0.833 0.574 0.621 0.501 0.557 0.783 0.655 0.699</td><td>0.297 0.132 0.116 0.799 0.404 0.338 0.833 0.463 0.384 0.921 0.572 0.580</td></tr><tr><td/><td/><td/><td>1-Layer MLP</td><td colspan=\"2\">0.856 0.771 0.519 0.295 0.267 0.384 0.601 0.502 0.558 0.829 0.514 0.487</td></tr><tr><td>NN</td><td>base-</td><td>lines</td><td>Single block MLPMixer Single Transformer block</td><td colspan=\"2\">0.865 0.780 0.549 0.854 0.787 0.526 0.471 0.289 0.416 0.646 0.534 0.575 0.781 0.489 0.420 0.335 0.275 0.396 0.597 0.497 0.552 0.819 0.512 0.472</td></tr><tr><td/><td/><td/><td>1-Layer GCN-LSTM</td><td colspan=\"2\">0.905 0.829 0.532 0.593 0.439 0.540</td><td>0.748 0.596 0.645</td><td>0.847 0.550 0.535</td></tr></table>", "type_str": "table", "text": "Experimental results for SWaT, WADI, and SMD datasets. The bold and underline marks the best and second-best value. F 1P A: F1 score with point-adjust; F 1: the standard point-wise F1 score; F 1T : time-series range-wise F1 score", "num": null, "html": null}, "TABREF4": {"content": "<table><tr><td>Methods</td><td colspan=\"2\">SWaT</td><td colspan=\"2\">WADI 112</td></tr><tr><td/><td>Orig</td><td>Line</td><td>Orig</td><td>Line</td></tr><tr><td>Single block MLPMixer</td><td colspan=\"4\">0.780 0.770 0.497 0.500</td></tr><tr><td>Single Transformer block</td><td colspan=\"4\">0.787 0.772 0.534 0.521</td></tr><tr><td>1-Layer GCN-LSTM</td><td colspan=\"4\">0.829 0.794 0.596 0.587</td></tr><tr><td>TranAD (<PERSON><PERSON> et al., 2022)</td><td colspan=\"4\">0.799 0.800 0.511 0.572</td></tr><tr><td colspan=\"5\">GDN (Deng &amp; Hooi, 2021) 0.810 0.808 0.571 0.543</td></tr></table>", "type_str": "table", "text": "", "num": null, "html": null}, "TABREF5": {"content": "<table/>", "type_str": "table", "text": "Linear approximation of complex models on two datasets. Orig: original model Line: linear approximated mode. Performance is reported on the standard point-wise F 1 score.", "num": null, "html": null}, "TABREF6": {"content": "<table><tr><td/><td/><td colspan=\"2\">SWAT</td><td/><td/><td/><td>WADI-112</td></tr><tr><td>None</td><td/><td colspan=\"2\">Mean-STD</td><td colspan=\"2\">Median-IQR</td><td>None</td><td>Mean-STD</td><td>Median-IQR</td></tr><tr><td colspan=\"2\">F1 AUPRC</td><td colspan=\"2\">F1 AUPRC</td><td colspan=\"2\">F1 AUPRC</td><td colspan=\"2\">F1 AUPRC</td><td>F1 AUPRC</td><td>F1 AUPRC</td></tr><tr><td>GDN (Deng &amp; Hooi, 2021) 0.767</td><td colspan=\"2\">0.724 0.685</td><td colspan=\"2\">0.473 0.810</td><td colspan=\"2\">0.762 0.549</td><td>0.492 0.456</td><td>0.353 0.571</td><td>0.519</td></tr><tr><td>TranAD (Tuli et al., 2022) 0.769</td><td colspan=\"2\">0.708 0.742</td><td colspan=\"2\">0.638 0.799</td><td colspan=\"2\">0.764 0.511</td><td>0.529 0.448</td><td>0.312 0.509</td><td>0.554</td></tr><tr><td>PCA Error 0.802</td><td colspan=\"2\">0.725 0.833</td><td colspan=\"2\">0.744 0.756</td><td colspan=\"2\">0.721 0.600</td><td>0.591 0.513</td><td>0.351 0.654</td><td>0.570</td></tr><tr><td>1-Layer GCN-LSTM 0.770</td><td colspan=\"2\">0.715 0.775</td><td colspan=\"2\">0.660 0.829</td><td colspan=\"2\">0.792 0.592</td><td>0.520 0.520</td><td>0.411 0.596</td><td>0.535</td></tr></table>", "type_str": "table", "text": "Impact of normalization on scores. Normalisation of prediction scores before thresholding impacts performance. Performance is reported on the point-wise F 1 and AU P RC score.", "num": null, "html": null}, "TABREF8": {"content": "<table/>", "type_str": "table", "text": "NN-Baselines: Reconstruction vs Forecasting.", "num": null, "html": null}, "TABREF9": {"content": "<table><tr><td>MSL</td><td>55 (27)</td><td>2159 (58317)</td><td>2730 (73729)</td><td>286 (11.97%)</td><td>7730 (10.48%)</td></tr><tr><td>SMAP</td><td>25 (54 )</td><td>2555 (138004)</td><td>8070 (435826)</td><td>1034 (12.40%)</td><td>55854 (12.82%)</td></tr></table>", "type_str": "table", "text": "No. Sensors (Traces) Avg. Train (Total) Avg. Test (Total) Avg. Anomalies (%) Total Anomalies (%)", "num": null, "html": null}, "TABREF10": {"content": "<table/>", "type_str": "table", "text": "The statistical profile of the datasets: MSL and SMAP.", "num": null, "html": null}, "TABREF11": {"content": "<table><tr><td/><td/><td/><td/><td/><td>Datasets</td></tr><tr><td/><td/><td/><td>Method</td><td colspan=\"2\">MSL F 1 P A F 1</td><td>SMAP F 1 P A F 1</td></tr><tr><td/><td/><td/><td>Random</td><td>0.931</td><td>0.190 0.961</td><td>0.227</td></tr><tr><td>Simple</td><td>base-</td><td>lines</td><td>Sensor range deviation L2-norm 1-NN distance</td><td colspan=\"2\">0.441 0.328 0.389 0.273 0.854 0.395 0.745 0.351 0.912 0.404 0.818 0.352</td></tr><tr><td/><td/><td/><td>PCA Error</td><td>0.843</td><td>0.426 0.811</td><td>0.387</td></tr><tr><td>SOTA</td><td>Meth-</td><td>ods</td><td>DAGMM (Zong et al., 2018) OmniAnomaly</td><td colspan=\"2\">0.701 0.199 0.712 0.333</td></tr></table>", "type_str": "table", "text": "summarizes the point-adjust F1 and standard F1 scores of simple baseline models and the published performance of the SOTA models. The performance of each proposed simple baseline model is averaged over all traces per dataset. The results of SOTA methods are taken from<PERSON><PERSON> et al.2022  in which only the best F1 scores are reported per method. The simple baselines, namely PCA-error and 1-NN distance, yield the best and second-best performance on both datasets, respectively.", "num": null, "html": null}}}}