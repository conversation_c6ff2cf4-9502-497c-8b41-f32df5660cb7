{"paper_id": "sam<PERSON>", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-24T23:34:46.117910Z"}, "title": "SAMformer: Unlocking the Potential of Transformers in Time Series Forecasting with Sharpness-Aware Minimization and Channel-Wise Attention", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "Huawei Noah's Ark Lab", "institution": "", "location": {"settlement": "Paris", "country": "France"}}, "email": "<<EMAIL>>"}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Odonnat", "suffix": "", "affiliation": {"laboratory": "Huawei Noah's Ark Lab", "institution": "", "location": {"settlement": "Paris", "country": "France"}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "Huawei Noah's Ark Lab", "institution": "", "location": {"settlement": "Paris", "country": "France"}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "Huawei Noah's Ark Lab", "institution": "", "location": {"settlement": "Paris", "country": "France"}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "Huawei Noah's Ark Lab", "institution": "", "location": {"settlement": "Paris", "country": "France"}}, "email": ""}, {"first": "<PERSON>is", "middle": [], "last": "Palpanas", "suffix": "", "affiliation": {"laboratory": "LIPADE", "institution": "Paris Descartes University", "location": {"settlement": "Paris", "country": "France"}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "Huawei Noah's Ark Lab", "institution": "", "location": {"settlement": "Paris", "country": "France"}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Transformer-based architectures achieved breakthrough performance in natural language processing and computer vision, yet they remain inferior to simpler linear baselines in multivariate long-term forecasting. To better understand this phenomenon, we start by studying a toy linear forecasting problem for which we show that transformers are incapable of converging to their true solution despite their high expressive power. We further identify the attention of transformers as being responsible for this low generalization capacity. Building upon this insight, we propose a shallow lightweight transformer model that successfully escapes bad local minima when optimized with sharpness-aware optimization. We empirically demonstrate that this result extends to all commonly used realworld multivariate time series datasets. In particular, SAMformer surpasses current state-ofthe-art methods and is on par with the biggest foundation model MOIRAI while having significantly fewer parameters. The code is available at https://github.com/romilbert/samformer.", "pdf_parse": {"paper_id": "sam<PERSON>", "_pdf_hash": "", "abstract": [{"text": "Transformer-based architectures achieved breakthrough performance in natural language processing and computer vision, yet they remain inferior to simpler linear baselines in multivariate long-term forecasting. To better understand this phenomenon, we start by studying a toy linear forecasting problem for which we show that transformers are incapable of converging to their true solution despite their high expressive power. We further identify the attention of transformers as being responsible for this low generalization capacity. Building upon this insight, we propose a shallow lightweight transformer model that successfully escapes bad local minima when optimized with sharpness-aware optimization. We empirically demonstrate that this result extends to all commonly used realworld multivariate time series datasets. In particular, SAMformer surpasses current state-ofthe-art methods and is on par with the biggest foundation model MOIRAI while having significantly fewer parameters. The code is available at https://github.com/romilbert/samformer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Multivariate time series forecasting is a classical learning problem that consists of analyzing time series to predict future trends based on historical information. In particular, long-term forecasting is notoriously challenging due to feature correlations and long-term temporal dependencies in time series. This learning problem is prevalent in those real-world applications where observations are Oracle is the optimal solution, Transformer is a base transformer, σReparam is a Transformer with weight rescaling (<PERSON><PERSON> et al., 2023) and Transformer + SAM is Transformer trained with sharpness-aware minimization.", "cite_spans": [{"start": 516, "end": 535, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF59"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Transformer overfits, σReparam improves slightly but fails to reach Oracle while Transformer+SAM generalizes perfectly. This motivates SAMformer, a shallow transformer combining SAM and best practices in time series forecasting.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "gathered sequentially, such as medical data ( <PERSON><PERSON><PERSON><PERSON><PERSON> & Lukoševič<PERSON>ū<PERSON>ė, 2016) , electricity consumption (UCI, 2015) , temperatures (Max Planck Institute, 2021) , or stock prices (<PERSON><PERSON><PERSON><PERSON> et al., 2023) . A plethora of methods have been developed for this task, from classical mathematical tools (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2007; Chen & Tao, 2021) and statistical approaches like ARIMA (Box & Jenkins, 1990; <PERSON> et al., 1974) to more recent deep learning ones (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , including recurrent and convolutional neural networks (<PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2020; <PERSON> et al., 2019; <PERSON> et al., 2018a; <PERSON> et al., 2019 <PERSON> et al., ). <PERSON><PERSON><PERSON><PERSON> et al., 2021;; <PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2021) , achieving breakthrough performance in both domains. Transformers are known to be particularly efficient in dealing with sequential data, a property that naturally calls for their application on time series. Unsurprisingly, many works attempted to propose time seriesspecific transformer architectures to benefit from their capacity to capture temporal interactions (<PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2023) . However, the current state-of-the-art in multivariate time series forecasting is achieved with a simpler MLP-based model (<PERSON> et al., 2023) , which significantly outperforms transformer-based methods. Moreover, <PERSON><PERSON> et al. (2023) have recently found that linear networks can be on par or better than transformers for the forecasting task, questioning their practical utility. This curious finding serves as a starting point for our work.", "cite_spans": [{"start": 44, "end": 79, "text": "( Čepulionis & Lukoševičiūtė, 2016)", "ref_id": "BIBREF8"}, {"start": 106, "end": 117, "text": "(UCI, 2015)", "ref_id": "BIBREF53"}, {"start": 133, "end": 161, "text": "(Max Planck Institute, 2021)", "ref_id": null}, {"start": 180, "end": 203, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF48"}, {"start": 297, "end": 320, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2007;", "ref_id": "BIBREF49"}, {"start": 321, "end": 338, "text": "Chen & Tao, 2021)", "ref_id": "BIBREF10"}, {"start": 377, "end": 398, "text": "(Box & Jenkins, 1990;", "ref_id": "BIBREF3"}, {"start": 399, "end": 416, "text": "<PERSON> et al., 1974)", "ref_id": "BIBREF4"}, {"start": 451, "end": 474, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF7"}, {"start": 531, "end": 556, "text": "(<PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF43"}, {"start": 557, "end": 578, "text": "<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF45"}, {"start": 579, "end": 596, "text": "<PERSON> et al., 2019;", "ref_id": "BIBREF19"}, {"start": 597, "end": 615, "text": "<PERSON> et al., 2018a;", "ref_id": null}, {"start": 616, "end": 632, "text": "<PERSON> et al., 2019", "ref_id": "BIBREF47"}, {"start": 633, "end": 670, "text": "<PERSON> et al., ). <PERSON><PERSON><PERSON><PERSON> et al., 2021;;", "ref_id": null}, {"start": 671, "end": 690, "text": "<PERSON><PERSON> et al., 2021;", "ref_id": null}, {"start": 691, "end": 712, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF50"}, {"start": 1080, "end": 1099, "text": "(<PERSON> et al., 2021;", "ref_id": null}, {"start": 1100, "end": 1116, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF56"}, {"start": 1117, "end": 1135, "text": "<PERSON> et al., 2022;", "ref_id": null}, {"start": 1136, "end": 1153, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF38"}, {"start": 1277, "end": 1296, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF11"}, {"start": 1368, "end": 1386, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF58"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Limitation of current approaches. Recent works applying transformers to time series data have mainly focused on either (i) efficient implementations reducing the quadratic cost of attention (<PERSON> et al., 2019; <PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2021; <PERSON> et al., 2021) or (ii) decomposing time series to better capture the underlying patterns in them (<PERSON> et al., 2021; <PERSON> et al., 2022) . Surprisingly, none of these works have specifically addressed a well-known issue of transformers related to their training instability, particularly present in the absence of large-scale data (<PERSON> et al., 2020; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) .", "cite_spans": [{"start": 190, "end": 207, "text": "(<PERSON> et al., 2019;", "ref_id": "BIBREF32"}, {"start": 208, "end": 225, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF33"}, {"start": 226, "end": 247, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2022;", "ref_id": null}, {"start": 248, "end": 268, "text": "<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF29"}, {"start": 269, "end": 287, "text": "<PERSON> et al., 2021;", "ref_id": null}, {"start": 288, "end": 304, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF56"}, {"start": 387, "end": 404, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF56"}, {"start": 405, "end": 423, "text": "<PERSON> et al., 2022)", "ref_id": null}, {"start": 618, "end": 636, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF32"}, {"start": 637, "end": 662, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Trainability of transformers. In computer vision and NLP, it has been found that attention matrices can suffer from entropy or rank collapse (<PERSON> et al., 2021) . Then, several approaches have been proposed to overcome these issues (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2023) . However, in the case of time series forecasting, open questions remain about how transformer architectures can be trained effectively without a tendency to overfit. We aim to show that by eliminating training instability, transformers can excel in multivariate long-term forecasting, contrary to previous beliefs of their limitations.", "cite_spans": [{"start": 141, "end": 160, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF16"}, {"start": 232, "end": 251, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF12"}, {"start": 252, "end": 270, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF59"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Summary of our contributions. Our proposal puts forward the following contributions:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "1. We show that even when the transformer architecture is tailored to solve a simple toy linear forecasting problem, it still generalizes poorly and converges to sharp local minima. We further identify that attention is mainly responsible for this phenomenon;", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "2. We propose a shallow transformer model, termed SAMformer, that incorporates the best practices proposed in the research community including reversible instance normalization (Rev<PERSON>, <PERSON> et al. 2021b ) and channel-wise attention (<PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022) recently introduced in computer vision community. We show that optimizing such a simple transformer with sharpness-aware minimization (SAM) allows convergence to local minima with better generalization;", "cite_spans": [{"start": 185, "end": 201, "text": "<PERSON> et al. 2021b", "ref_id": null}, {"start": 231, "end": 251, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF60"}, {"start": 252, "end": 271, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF57"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "3. We empirically demonstrate the superiority of our approach on common multivariate long-term forecasting datasets. SAMformer surpasses current state-of-theart methods and is on par with the biggest foundation model MOIRAI while having significantly fewer parameters.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Notations. We represent scalar values with regular letters (e.g., parameter λ), vectors with bold lowercase letters (e.g., vector x), and matrices with bold capital letters (e.g., matrix M). We denote by M ⊤ the transpose of M and likewise for vectors. The rank of a matrix M is denoted by rank(M), and its Frobenius norm by ∥M∥ F . We let ñ = min{n, m}, and denote by ∥M∥ * = ñ i=1 σ i (M) the nuclear norm of M with σ i (M) being its singular values, and by ∥M∥ 2 = σ max (M) its spectral norm. The identity matrix of size n × n is denoted by I n . The notation M ≽ 0 indicates that M is positive semi-definite.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Proposed Approach", "sec_num": "2."}, {"text": "We consider the multivariate long-term forecasting framework: given a D-dimensional time series of length L (lookback window), arranged in a matrix X ∈ R D×L to facilitate channel-wise attention, our objective is to predict its next H values (prediction horizon), denoted by Y ∈ R D×H . We assume that we have access to a training set that consists of N observations (X , Y) = ({X (i) } N i=0 , {Y (i) } N i=0 ), and denote by X", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "2.1."}, {"text": "(i) d ∈ R 1×L (respectively Y (i) d ∈ R 1×H", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "2.1."}, {"text": ") the d-th feature of the i-th input (respectively target) time series. We aim to train a predictor f ω : R D×L → R D×H parameterized by ω that minimizes the mean squared error (MSE) on the training set:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "2.1."}, {"text": "L train (ω) = 1 N D N i=0 ∥Y (i) -f ω (X (i) )∥ 2 F .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "2.1."}, {"text": "(1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "2.1."}, {"text": "Recently, <PERSON><PERSON> et al. (2023) showed that transformers perform on par with, or are worse than, simple linear neural networks trained to directly project the input to the output. We use this observation as a starting point by considering the following generative model for our toy regression problem mimicking a time series forecasting setup considered later:", "cite_spans": [{"start": 10, "end": 28, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF58"}], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Y = XW toy + ε.", "eq_num": "(2)"}], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "We let L = 512, H = 96, D = 7 and W toy ∈ R L×H , ϵ ∈ R D×H having random normal entries and generate 15000 input-target pairs (X, Y) (10000 for train and 5000 for validation), with X ∈ R D×L having random normal entries.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "Given this generative model, we would like to develop a transformer architecture that can efficiently solve the problem in Eq. ( 2) without unnecessary complexity. To achieve this, we propose to simplify the usual transformer encoder by applying attention to X and incorporating a residual connection that adds X to the attention's output. Instead of adding a feedforward block on top of this residual connection, we directly employ a linear layer for output prediction. Formally, our model is defined as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "f (X) = [X + A(X)XW V W O ]W,", "eq_num": "(3)"}], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "with", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "W ∈ R L×H , W V ∈ R L×dm , W O ∈ R dm×L and", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "A(X) being the attention matrix of an input sequence X ∈ R D×L defined as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "A(X) = softmax XW Q W ⊤ K X ⊤ √ d m ∈ R D×D (4)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "where the softmax is row-wise,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "W Q ∈ R L×dm , W K ∈ R L×dm", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": ", and d m is the dimension of the model. The softmax makes A(X) right stochastic, with each row describing a probability distribution. To ease the notations, in contexts where it is unambiguous, we refer to the attention matrix simply as A, omitting X.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "We term this architecture Transformer and briefly comment on it. First, the attention matrix is applied channelwise, which simplifies the problem and reduces the risk of overparametrization, as the matrix W has the same shape as in Eq. ( 2) and the attention matrix becomes much smaller due to L > D. In addition, channel-wise attention is more relevant than temporal attention in this scenario, as data generation follows an i.i.d. process according to Eq. ( 2). We formally establish the identifiability of W toy by our model below. The proof is deferred to Appendix E.2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "Proposition 2.1 (Existence of optimal solutions). Assume W Q , W K , W V and W O are fixed and let P =", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "X + A(X)XW V W O ∈ R D×L .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "Then, there exists a matrix W ∈ R L×H such that PW = XW toy if, and only if, rank([P XW toy ]) = rank(P) where L+H) is a block matrix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "[P XW toy ] ∈ R D×(", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "The assumption made above is verified if P is full rank and D < H, which is the case in this toy experiment.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "Consequently, the optimization problem of fitting a transformer on data generated with Eq. ( 2) theoretically admits infinitely many optimal classifiers W.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "We would now like to identify the role of attention in solving the problem from Eq. (3). To this end, we consider a model, termed Random Transformer, where only W is optimized, while self-attention weights W Q , W K , W V , W O are fixed during training and initialized following <PERSON><PERSON><PERSON> & <PERSON> (2010) . This effectively makes the considered transformer act like a linear model. Finally, we compare the local minima obtained by these two models after their optimization using <PERSON> with the Oracle model that corresponds to the least squares solution of Eq. ( 2). We present the validation loss for both models in Figure 2 .", "cite_spans": [{"start": 280, "end": 302, "text": "Glorot & Bengio (2010)", "ref_id": "BIBREF21"}], "ref_spans": [{"start": 621, "end": 622, "text": "2", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "A first surprising finding is that both transformers fail to recover W toy , highlighting that optimizing even such a simple architecture with a favorable design exhibits a strong lack of generalization. When fixing the self-attention matrices, the problem is alleviated to some extent, although Random Transformer remains suboptimal. This observation remains consistent across various optimizers (see Figure 15 in Appendix C) and values of learning rate, suggesting that this phenomenon is not attributable to suboptimal optimizer hyperparameters or the specific choice of the optimizer. As there is only a 2% increase in the number of parameters between the Random Transformer and the Transformer, it is not due to overfitting either. Hence, we deduce from Figure 1 that the poor generalization capabilities of Transformer are mostly due to the trainability issues of the attention module.", "cite_spans": [], "ref_spans": [{"start": 409, "end": 411, "text": "15", "ref_id": "FIGREF12"}, {"start": 766, "end": 767, "text": "1", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Motivational Example", "sec_num": "2.2."}, {"text": "Intuition. In the previous section, we concluded that the attention was at fault for the poor generalization of Transformer observed above. To develop our intuition behind this phenomenon, we plot in Figure 3a the atten-tion matrices at different epochs of training. We can see that the attention matrix is close to the identity matrix right after the very first epoch and barely changes afterward, especially with the softmax amplifying the differences in the matrix values. It shows the emergence of attention's entropy collapse with a full-rank attention matrix, which was identified in <PERSON><PERSON> et al. (2023) as one of the reasons behind the hardness of training transformers. This work also establishes a relationship between entropy collapse and the sharpness of the transformers' loss landscape which we confirm in Figure 3b (a similar behavior is obtained on real data in Figure 5a . The Transformer converges to a sharper minimum than the Random Transformer while having a significantly lower entropy (the attention being fixed at initialization for the latter, its entropy remains constant along training). These pathological patterns suggest that the Transformer fails because of the entropy collapse and the sharpness of its training loss. In the next paragraph, we investigate the existing solutions in the literature to alleviate those issues.", "cite_spans": [{"start": 590, "end": 608, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF59"}], "ref_spans": [{"start": 207, "end": 209, "text": "3a", "ref_id": "FIGREF4"}, {"start": 825, "end": 827, "text": "3b", "ref_id": "FIGREF4"}, {"start": 883, "end": 885, "text": "5a", "ref_id": "FIGREF7"}], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "Existing solutions. Recent studies have demonstrated that the loss landscape of transformers is sharper compared to other residual architectures (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2023) . This may explain training instability and subpar performance of transformers, especially when trained on small-scale datasets. The sharpness of transformers was observed and quantified differently: while <PERSON> et al.", "cite_spans": [{"start": 145, "end": 164, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF12"}, {"start": 165, "end": 183, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF59"}], "ref_spans": [], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "(2022) computes λ max , the largest eigenvalue of the loss function's <PERSON><PERSON>, <PERSON><PERSON> et al. (2023) gauges the entropy of the attention matrix to demonstrate its collapse with high sharpness. Both these metrics are evaluated, and their results are illustrated in Figure 3b . This visualization confirms our hypothesis, revealing both detrimental phenomena at once. On the one hand, the sharpness of the transformer with fixed attention is orders of magnitude lower than the sharpness of the transformer that converges to the identity attention matrix. On the other hand, the entropy of the transformer's attention matrix is dropping sharply along the epochs when compared to the initialization.", "cite_spans": [{"start": 79, "end": 97, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF59"}], "ref_spans": [{"start": 268, "end": 270, "text": "3b", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "To identify an appropriate solution allowing a better generalization performance and training stability, we explore both remedies proposed by <PERSON> et al. (2022) and <PERSON><PERSON> et al. (2023) . The first approach involves utilizing the recently proposed sharpness-aware minimization framework (<PERSON><PERSON> et al., 2021) which replaces the training objective L train of Eq. (1) by", "cite_spans": [{"start": 142, "end": 160, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF12"}, {"start": 165, "end": 183, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF59"}, {"start": 285, "end": 305, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "L SAM train (ω) = max ∥ϵ∥<ρ L train (ω + ϵ) ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "where ρ > 0 is an hyper-parameter (see Remark D.1 of Appendix D), and ω are the parameters of the model. More details on SAM can be found in Appendix D.2. The second approach involves reparameterizing all weight matri-ces with spectral normalization and an additional learned scalar, a technique termed σReparam by <PERSON><PERSON> et al. (2023) .", "cite_spans": [{"start": 315, "end": 333, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF59"}], "ref_spans": [], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "More formally, we replace each weight matrix W as follows", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "W = γ ∥W∥ 2 W,", "eq_num": "(5)"}], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "where γ ∈ R is a learnable parameter initialized at 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "The results depicted in Figure 1 highlight our transformer's successful convergence to the desired solution. Surprisingly, this is only achieved with SAM, as σReparam doesn't manage to approach the optimal performance despite maximizing the entropy of the attention matrix. In addition, one can observe in Figure 3b that the sharpness with SAM is several orders of magnitude lower than the Transformer while the entropy of the attention obtained with SAM remains close to that of a base Transformer with a slight increase in the later stages of the training. It suggests that entropy collapse as introduced in <PERSON><PERSON> et al. ( 2023) is benign in this scenario.", "cite_spans": [], "ref_spans": [{"start": 31, "end": 32, "text": "1", "ref_id": "FIGREF1"}, {"start": 313, "end": 315, "text": "3b", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "To better understand the failure of σReparam, it can be useful to recall how Eq. ( 5) was derived. <PERSON><PERSON> et al. (2023) departed from a tight lower bound on the attention entropy and showed that it increases exponentially fast when (<PERSON><PERSON> et al., 2023 , see Theorem 3.1). Eq. ( 5) was proposed as a simple way to minimize this quantity. In the case of channel-wise attention, however, it can be shown that this has a detrimental effect on the rank of the attention matrix, which would consequently exclude certain features from being considered by the attention mechanism. We formalize this intuition in the following Proposition 2.2, where we consider the nuclear norm, a sum of the singular values, as a smooth proxy of the algebraic rank, which is a common practice (<PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2021) . The proof is deferred to Appendix E.3. Proposition 2.2 (Upper bound on the nuclear norm). Let X ∈ R D×L be an input sequence. Assuming", "cite_spans": [{"start": 99, "end": 117, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF59"}, {"start": 230, "end": 248, "text": "(<PERSON><PERSON> et al., 2023", "ref_id": "BIBREF59"}, {"start": 766, "end": 791, "text": "(<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF14"}, {"start": 792, "end": 810, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "∥W Q W ⊤ K ∥ 2 is minimized", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "W Q W ⊤ K = W K W ⊤ Q ≽ 0, we have ∥XW Q W ⊤ K X ⊤ ∥ * ≤ ∥W Q W ⊤ K ∥ 2 ∥X∥ 2 F .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "Note that the assumption made above holds when W Q = W K and has been previously studied by <PERSON> et al. (2021a) . The theorem confirms that employing σReparam to decrease ∥W Q W ⊤ K ∥ 2 reduces the nuclear norm of the numerator of attention matrix defined by Eq. (4). While the direct link between matrix rank and this nuclear norm does not always hold, nuclear norm regularization is commonly used to encourage a low-rank structure in compressed sensing (<PERSON> et al., 2010; <PERSON>, 2011; Can<PERSON>ès & Recht, 2012) . Although Proposition 2.2 cannot be directly applied to the attention matrix A(X), we point out that in the extreme case when σReparam leads to the attention scores XW Q W ⊤ K X ⊤ to be rank-1 with identical rows as studied in (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022) , that the attention matrix stays rank-1 after application of the row-wise softmax. Thus, σReparam may induce a collapse of the attention rank that we empirically observe in terms of nuclear norm in Figure 7 . With these findings, we present a new simple transformer model with high performance and training stability for multivariate time series forecasting.", "cite_spans": [{"start": 92, "end": 110, "text": "<PERSON> et al. (2021a)", "ref_id": null}, {"start": 454, "end": 474, "text": "(<PERSON><PERSON> et al., 2010;", "ref_id": "BIBREF44"}, {"start": 475, "end": 487, "text": "<PERSON><PERSON>, 2011;", "ref_id": null}, {"start": 488, "end": 509, "text": "Candès & Recht, 2012)", "ref_id": "BIBREF5"}, {"start": 738, "end": 765, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF2"}], "ref_spans": [{"start": 972, "end": 973, "text": "7", "ref_id": null}], "eq_spans": [], "section": "Transformer's Loss Landscape", "sec_num": "2.3."}, {"text": "The proposed SAMformer is based on Eq. (3) with two important modifications. First, we equip it with Reversible Instance Normalization (<PERSON><PERSON>, <PERSON> et al. (2021b) ) applied to X as this technique was shown to be efficient in handling the shift between the training and testing data in time series. Second, as suggested by our explorations above, we optimize the model with SAM to make it converge to flatter local minima. Overall, this gives the shallow transformer model with one encoder in Figure 4 . We highlight that SAMformer keeps the channel-wise attention represented by a matrix D × D as in Eq. (3), contrary to spatial (or temporal) attention given by L×L matrix used in other models. This brings two important benefits: (i) it ensures feature permutation invariance, eliminating the need for positional encoding, commonly preceding the attention layer; (ii) it leads to a reduced time and memory complexity as D ≤ L in most of the real-world datasets. Our channel-wise attention examines the average impact of each feature on the others throughout all timesteps. An ablation study, detailed in Appendix C.4, validates the effectiveness of this implementation. We are now ready to evaluate SAMformer on common multivariate time series forecasting benchmarks, demonstrating its superior t", "cite_spans": [{"start": 143, "end": 161, "text": "<PERSON> et al. (2021b)", "ref_id": null}], "ref_spans": [{"start": 498, "end": 499, "text": "4", "ref_id": null}], "eq_spans": [], "section": "SAMformer: Putting It All Together", "sec_num": "2.4."}, {"text": "In this section, we empirically demonstrate the quantitative and qualitative superiority of SAMformer in multivariate long-term time series forecasting on common benchmarks. We show that SAMformer surpasses the current multivariate state-of-the-art TSMixer (<PERSON> et al., 2023) by 14.33% while having ∼ 4 times fewer parameters. All the implementation details are provided in Appendix A.1.", "cite_spans": [{"start": 257, "end": 276, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "3."}, {"text": "Datasets. We conduct our experiments on 8 publicly available datasets of real-world multivariate time series, commonly used for long-term forecasting (<PERSON> et al., 2021; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) : the four Electricity Transformer Temperature datasets ETTh1, ETTh2, ETTm1 and ETTm2 (<PERSON> et al., 2021) , Electricity (UCI, 2015), Exchange (Lai et al., 2018b ), Traffic (California Department of Transportation, 2021) , and Weather (Max Planck Institute, 2021) datasets. All time series are segmented with input length L = 512, prediction horizons H ∈ {96, 192, 336, 720}, and a stride of 1, meaning that each subsequent window is shifted by one step. A more detailed description of the datasets and time series preparation can be found in Appendix A.2.", "cite_spans": [{"start": 150, "end": 167, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF56"}, {"start": 168, "end": 186, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF11"}, {"start": 187, "end": 204, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF38"}, {"start": 205, "end": 223, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF58"}, {"start": 310, "end": 329, "text": "(<PERSON> et al., 2021)", "ref_id": null}, {"start": 366, "end": 384, "text": "(<PERSON> et al., 2018b", "ref_id": "BIBREF31"}, {"start": 385, "end": 443, "text": "), Traffic (California Department of Transportation, 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "3."}, {"text": "Baselines. We compare SAMformer with Transformer presented earlier and T<PERSON>ixer (<PERSON> et al., 2023) , a state-of-the-art multivariate baseline entirely built on MLPs. It should be noted that <PERSON> et al. (2023) displayed the performance of TSMixer for a fixed seed while in Table 1 , we report the performance over several runs with different seeds, resulting in a more reliable evaluation. For a fair comparison, we also include the performance of TSMixer trained with <PERSON> et al., 2021) . All the reported results are obtained using RevIN (<PERSON> et al., 2021b) for a more equitable comparison between SAMformer and its competitors. More detailed information on these baselines can be found in Appendix A.3.", "cite_spans": [{"start": 79, "end": 98, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF11"}, {"start": 190, "end": 208, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF11"}, {"start": 468, "end": 484, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF56"}, {"start": 537, "end": 556, "text": "(<PERSON> et al., 2021b)", "ref_id": null}], "ref_spans": [{"start": 278, "end": 279, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Experiments", "sec_num": "3."}, {"text": "Evaluation. All models are trained to minimize the MSE loss defined in Eq. ( 1). The average MSE on the test set, together with the standard deviation over 5 runs with different seeds is reported. Additional details and results, including the Mean Absolute Error (MAE), can be found in Table 6 of Appendix B.1. Except specified otherwise, all our results are also obtained over 5 runs with different seeds.", "cite_spans": [], "ref_spans": [{"start": 292, "end": 293, "text": "6", "ref_id": "TABREF6"}], "eq_spans": [], "section": "Experiments", "sec_num": "3."}, {"text": "SAMformer improves over state-of-the-art. The experimental results are detailed in ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Main Takeaways", "sec_num": "3.1."}, {"text": "Choices of implementation. We empirically compared our architecture, which is channel-wise attention (Eq. ( 3)), with temporal-wise attention. Table 9 of Appendix C.4 shows the superiority of our approach in the considered setting. We conducted our experiments with <PERSON> (Kingma & Ba, 2015) , the de-facto optimizers for transformers (<PERSON><PERSON> et al., 2023; <PERSON> & Li, 2022; <PERSON> et al., 2022; 2021; <PERSON> et al., 2022) . We provide an in-depth ablation study in Appendix C.3 that motivates this choice. As expected (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2020; Pan & Li, 2022; <PERSON> et al., 2020) , SGD (<PERSON><PERSON><PERSON>, 1983) fails to converge and AdamW (<PERSON><PERSON> & Hu<PERSON>, 2019) leads to similar performance but is very sensitive to the choice of the weight decay strength.", "cite_spans": [{"start": 271, "end": 290, "text": "(Kingma & Ba, 2015)", "ref_id": "BIBREF28"}, {"start": 334, "end": 352, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 353, "end": 368, "text": "Pan & Li, 2022;", "ref_id": "BIBREF39"}, {"start": 369, "end": 387, "text": "<PERSON> et al., 2022;", "ref_id": null}, {"start": 388, "end": 393, "text": "2021;", "ref_id": null}, {"start": 394, "end": 412, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF12"}, {"start": 509, "end": 527, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 528, "end": 545, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF32"}, {"start": 546, "end": 561, "text": "Pan & Li, 2022;", "ref_id": "BIBREF39"}, {"start": 562, "end": 581, "text": "<PERSON> et al., 2020)", "ref_id": "BIBREF61"}, {"start": 588, "end": 604, "text": "(<PERSON><PERSON><PERSON>, 1983)", "ref_id": "BIBREF37"}, {"start": 633, "end": 660, "text": "(<PERSON><PERSON><PERSON><PERSON> & Hutter, 2019)", "ref_id": "BIBREF36"}], "ref_spans": [{"start": 149, "end": 150, "text": "9", "ref_id": "TABREF12"}], "eq_spans": [], "section": "Ablation Study and Sensitivity Analysis", "sec_num": "3.4."}, {"text": "Sensitivity to the neighborhood size ρ. Appendix C.2 as a function of the neighborhood size ρ. It appears that TSMixer, with its quasi-linear architecture, exhibits less sensitivity to ρ compared to SAMformer. This behavior is consistent with the understanding that, in linear models, the sharpness does not change with respect to ρ, given the constant nature of the loss function's Hessian. Consequently, TSMixer benefits less from changes in ρ than SAMformer. Our observations consistently show that a sufficiently large ρ, generally above 0.7 enables SAMformer to achieve lower MSE than TSMixer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation Study and Sensitivity Analysis", "sec_num": "3.4."}, {"text": "SAM vs σReparam. We mentioned previously that σReparam doesn't improve the performance of a transformer on a simple toy example, although it makes it comparable to the performance of a transformer with fixed random attention. To further show that σReparam doesn't provide an improvement on real-world datasets, we show in Figure 8a that on ETTh1 and Exchange, σReparam alone fails to match SAMformer's improvements, even underperforming Transformer in some cases. A potential improvement may come from combining SAM and σReparam to smooth a rather sparse matrix obtained with SAM. However, as Figure 8b illustrates, this combination does not surpass the performance of using SAM alone. Furthermore, combining SAM and σReparam significantly increases training time and memory usage, especially for larger datasets and longer horizons (see Appendix Figure 11 ), indicating its inefficiency as a method.", "cite_spans": [], "ref_spans": [{"start": 329, "end": 331, "text": "8a", "ref_id": "FIGREF8"}, {"start": 600, "end": 602, "text": "8b", "ref_id": "FIGREF8"}, {"start": 854, "end": 856, "text": "11", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Ablation Study and Sensitivity Analysis", "sec_num": "3.4."}, {"text": "In this work, we demonstrated how simple transformers can reclaim their place as state-of-the-art models in long-term multivariate series forecasting from their MLP-based competitors. Rather than concentrating on new architectures and attention mechanisms, we analyzed the current pitfalls of transformers in this task and addressed them by carefully designing an appropriate training strategy. Our find-ings suggest that even a simple shallow transformer has a very sharp loss landscape which makes it converge to poor local minima. We analyzed popular solutions proposed in the literature to address this issue and showed which of them work or fail. Our proposed SAMformer, optimized with sharpness-aware minimization, leads to a substantial performance gain compared to the existing forecasting baselines, including the current largest foundation model MOIRAI, and benefits from a high versatility and robustness across datasets and prediction horizons. Finally, we also showed that channel-wise attention in time series forecasting can be more efficient -both computationally and performance-wise -than temporal attention commonly used previously. We believe that this surprising finding may spur many further works building on top of our simple architecture to improve it even further.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion and Future Work", "sec_num": "4."}, {"text": "for long sequence time-series forecasting. 1 from SAMformer, TSMixer, and Transformer come from our own experiments, conducted over 5 runs with 5 different seeds. The reader might notice that the results of TSMixer without SAM slightly differ from the ones reported in the original paper (<PERSON> et al., 2023) . It comes from the fact that the authors reported results from a single seed, while we report average performance with standard deviation on multiple runs for a better comparison of methods. We perform a Student's t-test in Table 7 for a more thorough comparison of SAMformer and TSMixer with SAM. It should be noted that, unlike our competitors including TSMixer, the architecture of SAMformer remains the same for all the datasets. This highlights the robustness of our method and its advantage as no heavy hyperparameter tuning is required. For a fair comparison of models, we also report results from other baselines in the literature that we did not run ourselves. For Informer (<PERSON> et al., 2021), Autoformer (<PERSON> et al., 2021), and Fedformer (<PERSON> et al., 2022) , the results on all datasets, except Exchange, are reported from <PERSON> et al. (2023) . Results on the Exchange dataset for those 5 baselines come from the original corresponding papers and hence refer to the models without RevIN. For iTransformer (<PERSON> et al., 2024) and PacthTST (<PERSON><PERSON> et al., 2023) , results are reported from <PERSON> et al. (2024) . Those baselines also make use of RevIn (<PERSON> et al., 2021b) . It should be noted that iTransformer (<PERSON> et al., 2024) uses both temporal and channel-wise attention. Our large-scale experimental evaluation ensures a comprehensive and comparative analysis across various established models in multivariate long-term time series forecasting.", "cite_spans": [{"start": 288, "end": 307, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF11"}, {"start": 1024, "end": 1046, "text": "(<PERSON> et al., 2021), and", "ref_id": "BIBREF56"}, {"start": 1047, "end": 1076, "text": "Fedformer (<PERSON> et al., 2022)", "ref_id": null}, {"start": 1143, "end": 1161, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF11"}, {"start": 1324, "end": 1342, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF34"}, {"start": 1356, "end": 1374, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF38"}, {"start": 1403, "end": 1420, "text": "<PERSON> et al. (2024)", "ref_id": "BIBREF34"}, {"start": 1462, "end": 1481, "text": "(<PERSON> et al., 2021b)", "ref_id": null}, {"start": 1521, "end": 1539, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF34"}], "ref_spans": [{"start": 43, "end": 44, "text": "1", "ref_id": "TABREF0"}, {"start": 539, "end": 540, "text": "7", "ref_id": "TABREF7"}], "eq_spans": [], "section": "Discussion and Future Work", "sec_num": "4."}, {"text": "In this section, we provide additional experiments to showcase, quantitatively and qualitatively, the superiority of our approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Additional Experiments", "sec_num": null}, {"text": "In this section, we provide the performance comparison of the different baselines with the Mean Absolute Error (MAE). We display the results in Table 6 . The conclusion is similar to the one made in the main paper in Table 1 and confirms the superiority of SAMformer compared to its competitors, including very recent baselines like TSMixer (<PERSON> et al., 2023) , iTransformer (<PERSON> et al., 2024) and PatchTST (<PERSON><PERSON> et al., 2023) . ", "cite_spans": [{"start": 341, "end": 360, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF11"}, {"start": 376, "end": 394, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF34"}, {"start": 408, "end": 426, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF38"}], "ref_spans": [{"start": 150, "end": 151, "text": "6", "ref_id": "TABREF6"}, {"start": 223, "end": 224, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "B.1. MAE Results", "sec_num": null}, {"text": "In this section, we showcase the computational efficiency of our approach. We compare in Table 8 the number of parameters of SAMformer and TSMixer on the several benchmarks used in our experiments. We also display the ratio between the number of parameters of TSMixer and the number of parameters of SAMformer. Overall, SAMformer has ∼ 4 times fewer parameters than TSMixer while outperforming it by 14.33% on average.", "cite_spans": [], "ref_spans": [{"start": 95, "end": 96, "text": "8", "ref_id": "TABREF8"}], "eq_spans": [], "section": "B.3. Computational Efficiency of SAMformer", "sec_num": null}, {"text": "In this section, we demonstrate that SAMformer has a strong generalization capacity. In particular, Transformer heavily depends on the initialization, which might be due to bad local minima as its loss landscape is sharper than the one of SAMformer. We display in Figure 9 and Figure 10 the distribution of the test MSE on 5 runs on the datasets used in our experiments (Table 5 ) and various prediction horizons H ∈ {96, 192, 336, 720}. We can see that SAMformer has strong and stable performance across the datasets and horizons, regardless of the seed. On the contrary, the performance Transformer is unstable with a large generalization gap depending on the seed.", "cite_spans": [], "ref_spans": [{"start": 271, "end": 272, "text": "9", "ref_id": "FIGREF9"}, {"start": 284, "end": 286, "text": "10", "ref_id": "FIGREF10"}, {"start": 377, "end": 378, "text": "5", "ref_id": "TABREF5"}], "eq_spans": [], "section": "B.4. Strong Generalization Regardless of the Initialization", "sec_num": null}, {"text": "In this section, we consider Transformer, SAMformer, σReparam, which corresponds to Transformer with the rescaling proposed by <PERSON><PERSON> et al. (2023) and SAMformer + σReparam which is SAMformer with the rescaling proposed by <PERSON><PERSON> et al. (2023) . We plot a batch of attention matrices after training with prediction horizon H = 96 (our primary study does not identify significant changes with the value of horizon) on Weather in Figure 12 . While Transformer tends to ignore the importance of a feature on itself by having low values on the diagonal, we can see in the bottom left of Figure 12 that SAMformer strongly encourages these feature-to-feature correlations. A very distinctive pattern is observable: a near-identity attention reminiscent of <PERSON> et al. (2023) and <PERSON><PERSON><PERSON> & Kolter (2023) . The former showed that pretrained vision models present similar patterns and both identified the benefits of such attention matrices for the propagation of information along the layers of deep transformers in NLP and computer vision. While in our setting, we have a single-layer transformer, this figure indicates that at the end of the training, self-information from features to themselves is not lost. In contrast, we see that σReparam leads to almost rank-1 matrices with identical columns. This confirms the theoretical insights from Theorem 2.2 that showed how rescaling the trainable weights with σReparam to limit the magnitude of that naively combining SAMformer with σReparam does not solve the issues: while some diagonal patterns remain, most of the information has been lost. Moreover, combining both σReparam and SAMformer heavily increases the training time, as shown in Figure 11 . Figure 14 : Evolution of the test MSE with the neighborhood size ρ of SAM (Remark D.1). We display the average test MSE with a 95% confidence interval. Overall, SAMformer has a smooth behavior with ρ, with a decreasing MSE and less variance. On the contrary, TSMixer is less stable and fluctuates more. On most of the datasets, the range of neighborhood seizes ρ such that SAMformer is below TSMixer is large. The first value ρ = 0 amounts to the usual minimization with Adam, which confirms that SAM always improves the performance of SAMformer. In addition, and despite its lightweight (Table 8 ), SAMformer achieves the lowest MSE on 7 out of 8 datasets, as shown in Table 1 and Table 7 . It should be noted that compared to similar studies in computer vision (Chen et al., 2022) , values of ρ must be higher to effectively improve the generalization and flatten the loss landscapes.", "cite_spans": [{"start": 127, "end": 145, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF59"}, {"start": 221, "end": 239, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF59"}, {"start": 746, "end": 762, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF22"}, {"start": 767, "end": 791, "text": "Trockman & Kolter (2023)", "ref_id": "BIBREF52"}, {"start": 2455, "end": 2474, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF12"}], "ref_spans": [{"start": 431, "end": 433, "text": "12", "ref_id": "FIGREF2"}, {"start": 586, "end": 588, "text": "12", "ref_id": "FIGREF2"}, {"start": 1687, "end": 1689, "text": "11", "ref_id": "FIGREF1"}, {"start": 1699, "end": 1701, "text": "14", "ref_id": "FIGREF1"}, {"start": 2287, "end": 2288, "text": "8", "ref_id": "TABREF8"}, {"start": 2368, "end": 2369, "text": "1", "ref_id": "TABREF0"}, {"start": 2374, "end": 2381, "text": "Table 7", "ref_id": "TABREF7"}], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "∥W Q W ⊤ K ∥ 2 could hamper the rank of XW Q W ⊤ K X ⊤", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "96", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "C.3. Sensitivity to the Change of the Optimizer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "In our work, we considered the <PERSON> optimizer (Kingma & Ba, 2015) as it is the de-facto optimizer for transformer-based models (<PERSON><PERSON> et al., 2023; <PERSON> & Li, 2022; <PERSON> et al., 2022; 2021; <PERSON> et al., 2022) . The superiority of <PERSON> to optimize networks with attention has been empirically and theoretically studied, where recent works show that the SGD (<PERSON><PERSON><PERSON>, 1983) was not suitable for attention-based models (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2020; <PERSON> & <PERSON>, 2022; <PERSON> et al., 2020) .", "cite_spans": [{"start": 46, "end": 65, "text": "(Kingma & Ba, 2015)", "ref_id": "BIBREF28"}, {"start": 127, "end": 145, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 146, "end": 161, "text": "Pan & Li, 2022;", "ref_id": "BIBREF39"}, {"start": 162, "end": 180, "text": "<PERSON> et al., 2022;", "ref_id": null}, {"start": 181, "end": 186, "text": "2021;", "ref_id": null}, {"start": 187, "end": 205, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF12"}, {"start": 353, "end": 369, "text": "(<PERSON><PERSON><PERSON>, 1983)", "ref_id": "BIBREF37"}, {"start": 414, "end": 432, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 433, "end": 450, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF32"}, {"start": 451, "end": 466, "text": "Pan & Li, 2022;", "ref_id": "BIBREF39"}, {"start": 467, "end": 486, "text": "<PERSON> et al., 2020)", "ref_id": "BIBREF61"}], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "To ensure the thoroughness of our investigation, we conducted experiments on the synthetic dataset introduced in Eq. ( 2) and reported the results in Figure 15a . As expected, we see that using SGD leads to high-magnitude losses and divergence. We also conducted the same experiments with the AdamW (Loshchilov & Hutter, 2019 ) that incorporates the weight decay scheme in the adaptive optimizer Adam (Kingma & Ba, 2015) . We display the results obtained with weight decay factors wd = 1e-3 in Figure 15a and with wd ∈ {1e-5, 1e-4} in Figure 15b . When wd = 1e-3, we observe that it does not converge. However, with wd ∈ {1e-5, 1e-4}, we observe a similar behavior for Transformer than when it is trained with <PERSON> (Figure 2 ). Hence, using AdamW does not lead to the significant benefits brought by SAM (Figure 1 . As the optimization is very sensitive to the value of weight decay wd, it motivates us to conduct our experiments with <PERSON>. This ablation study contrasts two variants of our model to showcase the effectiveness of Sharpness-Aware Minimization (SAM) and our attention approach. Identity Attention represents SAMformer with an attention weight matrix constrained to identity, illustrating that SAM does not simply reduce the attention weight matrix to identity, as performance surpasses this configuration. Temporal Attention is compared to our Transformer without SAM, highlighting our focus on treating feature correlations in the attention mechanism rather than temporal correlations. and test sets for the forecasting task. The RevIN normalization scheme is now widespread in deep learning approaches for time series forecasting (Chen et al., 2023; Nie et al., 2023) . The RevIN normalization involves trainable parameters (β, γ) ∈ R K ×R K and consists of two parts: a normalization step and a symmetric denormalization step. Before presenting them, we introduce for a given input time series X", "cite_spans": [{"start": 299, "end": 325, "text": "(<PERSON>h<PERSON><PERSON> & Hutter, 2019", "ref_id": "BIBREF36"}, {"start": 401, "end": 420, "text": "(Kingma & Ba, 2015)", "ref_id": "BIBREF28"}, {"start": 1647, "end": 1666, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF11"}, {"start": 1667, "end": 1684, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF38"}], "ref_spans": [{"start": 157, "end": 160, "text": "15a", "ref_id": "FIGREF12"}, {"start": 501, "end": 504, "text": "15a", "ref_id": "FIGREF12"}, {"start": 542, "end": 545, "text": "15b", "ref_id": "FIGREF12"}, {"start": 723, "end": 724, "text": "2", "ref_id": "FIGREF2"}, {"start": 812, "end": 813, "text": "1", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "(i) ∈ X the empirical mean μ[X (i) k ] and empirical standard deviation σ2 [X (i) k ] of its k-th feature X (i) k ∈ R 1×L as follows:    μ X (i) k = 1 L L t=1 X (i) kj σ2 X (i) k = 1 L L t=1 (X (i) kj -μ[X (i) k ]) 2 . (6)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "The first one acts on the input sequence X (i) and outputs the corresponding normalized sequence X(i) ∈ R K×L such that for all k, t,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "X(i) kt = γ k     X (i) kt -μ X (i) k σ2 X (i) k + ε     + β k ,", "eq_num": "(7)"}], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "where ε > 0 is a small constant to avoid dividing by 0. The neural network's input is then X(i) , instead of X (i) . The second step is applied to the output of the neural network Ỹ(i) , such that the final output considered for the forecasting is the denormalized sequence Ŷ(i) ∈ R K×H such that for all k, t,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Ŷ(i) kt = σ2 X (i) k + ε • Ỹ(i) kt -β k γ k + μ X (i) k .", "eq_num": "(8)"}], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "As stated in <PERSON> et al. (2021b) , μ, σ2 , β and γ contain the non-stationary information of the input sequences X (i) .", "cite_spans": [{"start": 13, "end": 31, "text": "<PERSON> et al. (2021b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "End-to-end closed form with linear model and RevIN. We consider a simple linear neural network. Formally, for any input sequence X ∈ R D×L , the prediction of f lin : R D×L → R D×H simply writes", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "f lin (X) = XW.", "eq_num": "(9)"}], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "When combined with RevIN, the neural network f lin is not directly applied to the input sequence but after the first normalization step of RevIN (Eq. ( 7)). An interesting benefit of the simplicity of f lin is that it enables us to write its prediction in closed form, even when with RevIN. The proof is deferred to Appendix E.4.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "Proposition D.1 (Closed-form formulation). For any input sequence X ∈ R K×L , the output of the linear model", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Ŷ = f lin (X) ∈ R K×H has entries Ŷkt = μ[X k ] + L j=1 (X kj -μ[X k ])W jt - β k γ k σ2 [X k ] + ε   1 - L j=1 W jt   ,", "eq_num": "(10)"}], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "Proposition D.1 highlights the fact that the k-th variable of the outputs Ŷ only depends on k-th variable of the input sequence X. It leads to channel-independent forecasting, although we did not explicitly enforce it. (10) can be seen as a linear interpolation around the mean μ with a regularization term on the network parameters W involving the nonstationary information σ2 , β, γ. Moreover, the output sequence Ŷ can be written in a more compact and convenient matrix formulation as follows", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Ŷ = XW + ξ (X,W,β,γ) ,", "eq_num": "(11)"}], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "ξ (X,W,β,γ) ∈ R K×H with entry μ[X k ] -β k γ k σ2 [X k ] + ε 1 - L j=1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "W jt in the k-th row and t-th column. The proof is deferred to Appendix E.5. With this formulation, the predicted sequence can be seen as a sum of a linear term XW and a residual term ξ (X,W,β,γ) that takes into account the first and second moments of each variable X k , which is reminiscent of the linear regression model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5. Faithful Signal Propagation", "sec_num": null}, {"text": "Regularizing with the sharpness. Standard approaches consider a parametric family of models f ω and aim to find parameters ω that minimize a training objective L train (ω), used as a tractable proxy to the true generalization error L test (ω). Most deep learning pipelines rely on first-order optimizers, e.g. <PERSON>G<PERSON> (<PERSON><PERSON><PERSON>, 1983) or <PERSON> (Kingma & Ba, 2015) , that disregard higher-order information such as the curvature, despite its connection to generalization (Dzi<PERSON>ite & Roy, 2017; <PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON> et al., 2017) . As L train is usually non-convex in ω, with multiple local or global minima, solving min ω L train (ω) may still lead to high generalization error L test (ω). To alleviate this issue, <PERSON><PERSON> et al. (2021) propose to regularize the training objective with the sharpness, defined as follows ", "cite_spans": [{"start": 314, "end": 330, "text": "(<PERSON><PERSON><PERSON>, 1983)", "ref_id": "BIBREF37"}, {"start": 339, "end": 358, "text": "(Kingma & Ba, 2015)", "ref_id": "BIBREF28"}, {"start": 465, "end": 488, "text": "(Dziugaite & Roy, 2017;", "ref_id": "BIBREF18"}, {"start": 489, "end": 512, "text": "<PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF9"}, {"start": 513, "end": 533, "text": "<PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF25"}, {"start": 720, "end": 739, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "D.2. Sharpness-aware minimization (SAM)", "sec_num": null}, {"text": "Gradient updates. As the exact solution to the inner maximization in Eq. ( 13) is hard to compute, the authors of (<PERSON><PERSON> et al., 2021) approximate it with the following first-order Taylor expansion ", "cite_spans": [{"start": 114, "end": 134, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "D.2. Sharpness-aware minimization (SAM)", "sec_num": null}, {"text": "where the solution of ( 14) writes ε(ω) = ρ ∇Ltrain(ω) ∥∇Ltrain(ω)∥2 . It leads to the following gradient update", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2. Sharpness-aware minimization (SAM)", "sec_num": null}, {"text": "ω t+1 = ω t -η∇L train ω t + ρ ∇L train (ω) ∥∇L train (ω)∥ 2 ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2. Sharpness-aware minimization (SAM)", "sec_num": null}, {"text": "where η is the learning rate.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2. Sharpness-aware minimization (SAM)", "sec_num": null}, {"text": "To ease the readability of the proofs, we recall the following notations. We denote scalar values by regular letters (e.g., parameter λ), vectors by bold lowercase letters (e.g., vector x), and matrices by bold capital letters (e.g., matrix M).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Proofs E.1. Notations", "sec_num": null}, {"text": "For a matrix M ∈ R n×m , we denote by M i its i-th row, by M •,j its j-th column, by m ij its entries and by M ⊤ its transpose. We denote the trace of a matrix M by Tr(M), its rank by rank(M) and its Frobenius norm by ∥M∥ F . We denote σ(M) := (σ 1 (M), . . . , σ ñ(M)) the vector of singular values of M in non-decreasing order, with ñ = min{n, m} and the specific notation σ min (M), σ max (M) for the minimum and maximum singular values, respectively. We denote by ∥M∥ * = ñ i=1 σ i (M) its nuclear norm and by ∥M∥ 2 = σ max (M) its spectral norm. When M is square with n = m, we denote λ(M) := (λ 1 (M), . . . , λ n (M)) the vector of singular values of M in non-decreasing order and the specific notation λ min (M), λ max (M) for the minimum and maximum singular values, respectively. For a vector x, its transpose writes x ⊤ and its usual Euclidean norm writes ∥x∥. The identity matrix of size n × n is denoted by I n . The vector of size n with each entry equal to 1 is denoted by 1 n . The notation M ≽ 0 indicates that M is positive semi-definite.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON>. Proofs E.1. Notations", "sec_num": null}, {"text": "We first recall the following technical lemmas. Proof. Let F 1 := {Su|u ∈ R m } ⊂ R n and F 2 := {(SB)u|u ∈ R m } ⊂ R n be the vector spaces generated by the columns of S and SB respectively. By definition, the rank of a matrix is the dimension of the vector space generated by its columns (equivalently by its rows). We will show that F 1 and F 2 coincides. Let v ∈ F 1 , i.e., there exists u ∈ R m such that v = Su. As B is full rank, the operator x → Bx is bijective. It follows that there always exists some z ∈ R m such that u = Bz. Then, we have v = Su = S(Bz) = (SB)z, which means that v ∈ F 2 . As v was taken arbitrarily in F 1 , we have proved that F 1 ⊂ F 2 . Conversely, consider y ∈ F 2 , i.e., we can write y = (SB)z for some z ∈ R m . It can then be seen that y = (SB)z = S(Bz), which means that y ∈ F 1 . Again, as y was taken arbitrarily, we have proved that F 1 ⊂ F 2 . In the end, we demonstrated that F 1 and F 2 coincide, hence they have the same dimension. By definition of the rank, S and SB have the same rank. Similar arguments can be used to show that S and BS have the same rank, which concludes the proof.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "The next lemma is a well-known result in matrix analysis and can be found in <PERSON> & Johnson (1991, Theorem 4.4.5) . For the sake of self-consistency, we recall it below along with a sketch of the original proof.", "cite_spans": [{"start": 77, "end": 113, "text": "<PERSON> & <PERSON> (1991, <PERSON><PERSON> 4.4.5)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "Lemma E.2. (see <PERSON> & Johnson, 1991, Theorem 4.4.5, p. 281) ", "cite_spans": [{"start": 16, "end": 60, "text": "<PERSON> & Johnson, 1991, Theorem 4.4.5, p. 281)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "We would like to write the bij with respect to the p ij , b ij the elements of P, B, respectively. As P is orthogonal, we know that its columns (e i ) n i=0 form an orthonormal basis of R n . Hence, the entry (i, j) of ΛP ⊤ BP, writes as follows: Combining this last equality with Eq. ( 17) and Eq. ( 18) concludes the proof, i.e., ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "Lemma E.4 (Power of symmetric matrices). Let S ∈ R n×n be symmetric. The spectral theorem ensures the existence of P ∈ R n×n orthogonal, i.e., P ⊤ P = PP ⊤ = I n , and Λ ∈ R n×n diagonal with the eigenvalues of S as entries such that S = PΛP ⊤ . For any integer n ≥ 1, we have", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "S n = PΛ n P ⊤ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "In particular, the eigenvalues of S n are equal to the eigenvalues of S to the power of n.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "Proof. Let n ≥ 1 be an integer. We have Proof. Let k ∈ 1, K and t ∈ 1, H . We have", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "S n = PΛP ⊤ n = PΛP ⊤ × PΛP ⊤ × • • • × PΛP ⊤ ×", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "Ŷkt = σ2 [X k ] + ε • ỹkt -β k γ k + μ[X k ],", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "(from (8))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "= σ2 [x k ] + ε • L j=1 Xkj W jt -β k γ k + μ[X k ], (from (9)) = σ2 [X k ] + ε γ k • L j=1 Xkj W jt - β k γ k σ2 [X k ] + ε + μ[X k ] = σ2 [X k ] + ε γ k • L j=1 γ k X kj -μ[x k ] σ2 [X k ] + ε + β k W jt - β k γ k σ2 [x k ] + ε + μ[X k ],", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "(from ( 7))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "= L j=1 (X kj -μ[X k ])W jt + β k γ k σ2 [X k ] + ε   L j=1 W jt -1   + μ[X k ] = μ[X k ] + L j=1 (X kj -μ[X k ])W jt - β k γ k σ2 [X k ] + ε   1 - L j=1 W jt   .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "E.5. Matrix formulation of Ŷ in Eq. (11)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "Proof. Let k ∈ 1, K and t ∈ 1, H . From Proposition D.1, we have", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "Ŷkt = μ[X k ] + L j=1 (X kj -μ[X k ])W jt - β k γ k σ2 [X k ] + ε   1 - L j=1 W jt   = L j=1 X kj W jt + μ[X k ] - β k γ k σ2 [X k ] + ε •   1 - L j=1 W jt   .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "Gathering in matrix formulation concludes the proof.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2. Proof of Proposition 2.1", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none of which we feel must be specifically highlighted here.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "The authors would like to thank the machine learning community for providing open-source baselines and datasets. The authors thank the anonymous reviewers and metareviewers for their time and constructive feedback. This work was enabled thanks to open-source software such as Python (<PERSON> & Drake Jr, 1995) , PyTorch (<PERSON><PERSON><PERSON> et al., 2019) , TensorFlow (<PERSON><PERSON><PERSON> et al., 2015) , Scikitlearn (Pedregosa et al., 2011) and Matplotlib (Hunter, 2007) .", "cite_spans": [{"start": 283, "end": 312, "text": "(Van Rossum & Drake Jr, 1995)", "ref_id": "BIBREF54"}, {"start": 323, "end": 344, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF40"}, {"start": 358, "end": 378, "text": "(<PERSON><PERSON><PERSON> et al., 2015)", "ref_id": "BIBREF0"}, {"start": 393, "end": 417, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2011)", "ref_id": "BIBREF41"}, {"start": 433, "end": 447, "text": "(<PERSON>, 2007)", "ref_id": "BIBREF24"}], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "Roadmap. In this appendix, we provide the detailed experimental setup in Section A, additional experiments in Section B, and a thorough ablation study and sensitivity analysis in Section C. Additional background knowledge is available in Section D and proofs of the main theoretical results are provided in Section E. We display the corresponding table of contents below.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A<PERSON>ndix", "sec_num": null}, {"text": "A.1. Architecture and Training Parameters Architecture. We follow <PERSON> et al. (2023) ; <PERSON><PERSON> et al. (2023) , and to ensure a fair comparison of baselines, we apply the reversible instance normalization (RevIN) of <PERSON> et al. (2021b) (see Appendix D.1 for more details). The network used in SAMformer and Transformer is a simplified one-layer transformer with one head of attention and without feed-forward. Its neural network function follows Eq. (3), while RevIN normalization and denormalization are applied respectively before and after the neural network function, see Figure 4 . We display the inference step of SAMformer in great detail in Algorithm 1. For the sake of clarity, we describe the application of the neural network function sequentially on each element of the batches but in practice, the operations are parallelized and performed batch per batch. For SAMformer and Transformer, the dimension of the model is d m = 16 and remains the same in all our experiments. For TSMixer, we used the official implementation that can be found at here. Network trainable parameters:RevIN normalization: X ← X following Eq. ( 7). The output is a tensor Bin of dimension bs × L × D. Transposition of the batch: Bin is reshaped in dimension bs × D × L. Applying the neural network of Eq. (3): for each X ∈ Bin do 1. Attention layer Rescale the input with the attention matrix (Eq. ( 4)).The outputSum the input X and the output of the attention layer. The output", "cite_spans": [{"start": 66, "end": 84, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF11"}, {"start": 87, "end": 104, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF38"}, {"start": 211, "end": 229, "text": "<PERSON> et al. (2021b)", "ref_id": null}], "ref_spans": [{"start": 577, "end": 578, "text": "4", "ref_id": null}], "eq_spans": [], "section": "A. Experimental Setup", "sec_num": null}, {"text": "Apply a linear layer on the output of the skip connection.The output Training parameters. For all of our experiments, we train our baselines (SAMformer, Transformer, TSMixer with SAM, TSMixer without SAM) with the Adam optimizer (Kingma & Ba, 2015) , a batch size of 32, a cosine annealing scheduler (Loshchilov & Hutter, 2017 ) and the learning rates summarized in Table 3 . ", "cite_spans": [{"start": 229, "end": 248, "text": "(Kingma & Ba, 2015)", "ref_id": "BIBREF28"}, {"start": 300, "end": 326, "text": "(Losh<PERSON>lov & Hutter, 2017", "ref_id": "BIBREF35"}], "ref_spans": [{"start": 372, "end": 373, "text": "3", "ref_id": null}], "eq_spans": [], "section": "Linear layer", "sec_num": "3."}, {"text": "We conduct our experiments on 8 publicly available datasets of real-world time series, widely used for multivariate longterm forecasting (<PERSON> et al., 2021; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) . The 4 Electricity Transformer Temperature datasets ", "cite_spans": [{"start": 137, "end": 154, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF56"}, {"start": 155, "end": 173, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF11"}, {"start": 174, "end": 191, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF38"}], "ref_spans": [], "eq_spans": [], "section": "A.2. Datasets", "sec_num": null}, {"text": "C.1. Sensitivity to the Prediction Horizon H.In Figure 13 , we show that SAMformer outperforms its best competitor, TSMixer trained with SAM, on 7 out of 8 datasets for all values of prediction horizon H. This demonstrates the robustness of SAMformer.C.2. Sensitivity to the Neighborhood Size ρ.In Figure 14 , we display the evolution of test MSE of SAMformer and TSMixer with the values of neighborhood size ρ for SAM. Overall, SAMformer has a smooth behavior with ρ, with a decreasing MSE and less variance. On the contrary, TSMixer is less stable and fluctuates more. On most of the datasets, the range of neighborhood seizes ρ such that SAMformer is below TSMix<PERSON> is large. The first value ρ = 0 amounts to the usual minimization with <PERSON>, which confirms that SAM always improves the performance of SAMformer. In addition, and despite its lightweight (Table 8 ), SAMformer achieves the lowest MSE on 7 out of 8 datasets, as shown in Table 1 and Table 7 . It should be noted that compared to similar studies in computer vision (<PERSON> et al., 2022) , values of ρ must be higher to effectively improve the generalization and flatten the loss landscapes. This follows from the high sharpness λ max observed in time series forecasting (Figure 5a ) compared to computer vision models (<PERSON> et al., 2022) .Using Lemma E.1 on the right-hand-side of Eq. ( 15), we obtainUsing SY -ZB = C concludes the proof for the first implication of the equivalence.To prove the opposite direction, the authors of <PERSON> & <PERSON> (1991) assume that rank S C 0 B = rank S 0 0 B .Since two matrices have the same rank if, and only if, they are equivalent, we know that there exists Q ∈ R (n+p)×(n+p) , U ∈ R (m+q)×(m+q) non-singular such thatThe rest of the proof in Horn & Johnson (1991) is constructive and relies on Eq. ( 16) to exhibit Y ∈ R m×q and Z ∈ R n×p such that SY -ZB = C. This concludes the proof of the equivalence.We now proceed to the proof of Proposition 2.1.Proof. Applying Lemma E.2 with S = P, B = 0, C = XW toy and W in the role of Y ensures that there exists W ∈ R L×H such that PW = XW toy if and only if rank([P XW toy ]) = rank(P), which concludes the proof.", "cite_spans": [{"start": 1031, "end": 1050, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF12"}, {"start": 1282, "end": 1301, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF12"}, {"start": 1495, "end": 1516, "text": "<PERSON> & Johnson (1991)", "ref_id": "BIBREF23"}, {"start": 1745, "end": 1766, "text": "<PERSON> & Johnson (1991)", "ref_id": "BIBREF23"}], "ref_spans": [{"start": 55, "end": 57, "text": "13", "ref_id": null}, {"start": 305, "end": 307, "text": "14", "ref_id": null}, {"start": 863, "end": 864, "text": "8", "ref_id": null}, {"start": 944, "end": 945, "text": "1", "ref_id": null}, {"start": 950, "end": 957, "text": "Table 7", "ref_id": null}, {"start": 1242, "end": 1244, "text": "5a", "ref_id": null}], "eq_spans": [], "section": "C. Ablation Study and Sensitivity Analysis", "sec_num": null}, {"text": "We first prove the following technical lemmas. While these lemmas are commonly used and, for most of them, straightforward to prove, they are very useful to demonstrate Proposition 2.2. Lemma E.5 (Case of equality between eigenvalues and singular values). Let S ∈ R n×n be symmetric and positive semi-definite. Then the i-th eigenvalue and the i-th singular value of S are equal, i.e., for all i ∈ 1, n , we have λ i (S) = σ i (S).Proof. Let i ∈ 1, n . By definition of singular value, we haveLemma E.6. Let X ∈ R D×L be an input sequence and S ∈ R L×L be a positive semi-definite matrix. Then, XSX ⊤ is positive semi-definite.Proof. It is clear that XSX ⊤ ∈ R L×L is symmetric. Let u ∈ R L . We have:As u was arbitrarily chosen, we have proved that XSX ⊤ is positive semi-definite.We now proceed to the proof of Theorem 2.2.Proof. We recall that W Q W ⊤ K is symmetric and positive semi-definite, we have(cyclic property of the trace)Using the fact that X ⊤ X is positive semi-definite (Lemma E.6 with S = I L ), and that W Q W ⊤ K is symmetric, Lemma E.3 can be applied with M = W Q W ⊤ K and B = X ⊤ X. It leads to:2 by definition of the spectral norm ∥•∥ 2 . Recalling that by definition, Tr X ⊤ X = ∥X∥ 2 F concludes the proof, i.e.,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.3. Proof of Proposition 2.2", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "TensorFlow: Large-scale machine learning on heterogeneous systems", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Citro", "suffix": ""}, {"first": "G", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ghemawat", "suffix": ""}, {"first": "I", "middle": [], "last": "Goodfellow", "suffix": ""}, {"first": "A", "middle": [], "last": "Harp", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Isard", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "M", "middle": [], "last": "Kudlur", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Monga", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Talwar", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Viégas", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>yal<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Warden", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C., <PERSON>, G. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, S., Goodfellow, I<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>- <PERSON>, D<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, X. TensorFlow: Large-scale machine learning on heterogeneous systems, 2015. URL http://tensorflow.org/. Software available from tensorflow.org.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Linear attention is (maybe) all you need", "authors": [{"first": "K", "middle": [], "last": "Ahn", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Song", "suffix": ""}, {"first": "C", "middle": [], "last": "Yun", "suffix": ""}, {"first": "A", "middle": [], "last": "Jadbabaie", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, S. Linear attention is (maybe) all you need (to un- derstand transformer optimization), 2023.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Signal propagation in transformers: Theoretical perspectives and the role of rank collapse", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Biggio", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Orvieto", "suffix": ""}, {"first": "S", "middle": ["P"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Signal propagation in transform- ers: Theoretical perspectives and the role of rank col- lapse. In Oh, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> (eds.), Advances in Neural Information Processing Systems, 2022. URL https://openreview.net/ forum?id=FxVH7iToXS.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Time Series Analysis, Forecasting and Control", "authors": [{"first": "G", "middle": ["E P"], "last": "Box", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1990, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Box, G<PERSON> E<PERSON> <PERSON><PERSON> and <PERSON>, G. Time Series Analysis, Fore- casting and Control. Holden-Day, Inc., USA, 1990. ISBN 0816211043.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Some Recent Advances in Forecasting and Control", "authors": [{"first": "G", "middle": ["E P"], "last": "Box", "suffix": ""}, {"first": "G", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["F"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1974, "venue": "Journal of the Royal Statistical Society Series C", "volume": "23", "issue": "2", "pages": "2--158", "other_ids": {"DOI": ["10.2307/2346997"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>. Some Recent Advances in Forecasting and Con- trol. Journal of the Royal Statistical Society Se- ries C, 23(2):158-179, June 1974. doi: 10.2307/ 2346997. URL https://ideas.repec.org/a/ bla/jorssc/v23y1974i2p158-179.html.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Exact matrix completion via convex optimization", "authors": [{"first": "E", "middle": [], "last": "Candès", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "Commun. ACM", "volume": "55", "issue": "6", "pages": "111--119", "other_ids": {"DOI": ["10.1145/2184319.2184343"], "ISSN": ["0001-0782"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> Exact matrix completion via convex optimization. Commun. ACM, 55(6):111-119, jun 2012. ISSN 0001-0782. doi: 10.1145/2184319. 2184343. URL https://doi.org/10.1145/ 2184319.2184343.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Emerging properties in selfsupervised vision transformers", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Proceedings of the International Conference on Computer Vision (ICCV)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, A. Emerging properties in self- supervised vision transformers. In Proceedings of the International Conference on Computer Vision (ICCV), 2021.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Deep learning for time series forecasting: Advances and open problems", "authors": [{"first": "A", "middle": [], "last": "Casolaro", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Camastra", "suffix": ""}], "year": 2023, "venue": "Information", "volume": "14", "issue": "11", "pages": "", "other_ids": {"DOI": ["10.3390/info14110598"], "ISSN": ["2078- 2489"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and Camastra, F. Deep learning for time series forecasting: Advances and open problems. Information, 14(11), 2023. ISSN 2078- 2489. doi: 10.3390/info14110598. URL https:// www.mdpi.com/2078-2489/14/11/598.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Electrocardiogram time series forecasting and optimization using ant colony optimization algorithm", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Lu<PERSON>ševič<PERSON>", "suffix": ""}], "year": 2016, "venue": "Mathematical Models in Engineering", "volume": "2", "issue": "", "pages": "69--77", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> time series forecasting and optimization using ant colony optimization algorithm. Mathematical Models in Engi- neering, 2(1):69-77, Jun 2016. ISSN 2351-5279. URL https://www.extrica.com/article/17229.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Entropy-SGD: Biasing gradient descent into wide valleys", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Choromanska", "suffix": ""}, {"first": "S", "middle": [], "last": "Soatto", "suffix": ""}, {"first": "Y", "middle": [], "last": "Lecun", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Zecchina", "suffix": ""}], "year": 2017, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, S<PERSON>, <PERSON>, <PERSON>, <PERSON>, C., <PERSON>, C., <PERSON>, J<PERSON>, <PERSON>, L., and Zecchina, R. Entropy-SGD: Biasing gradient descent into wide valleys. In International Conference on Learning Representations, 2017. URL https:// openreview.net/forum?id=B1YfAfcgl.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Data-driven prediction of general hamiltonian dynamics via learning exactly-symplectic maps", "authors": [{"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Tao", "suffix": ""}], "year": 2021, "venue": "Proceedings of the 38th International Conference on Machine Learning", "volume": "139", "issue": "", "pages": "18--24", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. Data-driven prediction of general hamiltonian dynamics via learning exactly-symplectic maps. In <PERSON>, <PERSON><PERSON> and <PERSON>, T. (eds.), Pro- ceedings of the 38th International Conference on Ma- chine Learning, volume 139 of Proceedings of Ma- chine Learning Research, pp. 1717-1727. PMLR, 18- 24 Jul 2021. URL https://proceedings.mlr. press/v139/chen21r.html.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "TSMixer: An all-MLP architecture for time series forecasting", "authors": [{"first": "S.-A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C.-L", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": ["O"], "last": "Arik", "suffix": ""}, {"first": "N", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {"ISSN": ["2835-8856"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, N. C., and <PERSON><PERSON><PERSON>, T. TSMix<PERSON>: An all-MLP architecture for time series forecasting. Transactions on Machine Learn- ing Research, 2023. ISSN 2835-8856. URL https: //openreview.net/forum?id=wbpxTuXgm0.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "When vision transformers outperform resnets without pre-training or strong data augmentations", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C.-J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON>. When vision transformers outperform resnets without pre-training or strong data augmentations. In International Confer- ence on Learning Representations, 2022. URL https: //openreview.net/forum?id=LtKcMgGOeLt.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Triangular, variable-specific attentions for long sequence multivariate time series forecasting", "authors": [{"first": "R.-G", "middle": [], "last": "Cirstea", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Pan", "suffix": ""}, {"first": "", "middle": [], "last": "Triformer", "suffix": ""}], "year": null, "venue": "Proceedings of the Thirty-First International Joint Conference on Artificial Intelligence, IJCAI-22", "volume": "7", "issue": "", "pages": "", "other_ids": {"DOI": ["10.24963/ijcai.2022/277"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Triformer: Triangular, variable-specific atten- tions for long sequence multivariate time series forecast- ing. <PERSON>, <PERSON><PERSON> <PERSON><PERSON> (ed.), Proceedings of the Thirty- First International Joint Conference on Artificial Intel- ligence, IJCAI-22, pp. 1994-2001. International Joint Conferences on Artificial Intelligence Organization, 7 2022. doi: 10.24963/ijcai.2022/277. URL https: //doi.org/10.24963/ijcai.2022/277. Main Track.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Batch normalization provably avoids rank collapse for randomly initialised deep networks", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Bach", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the 34th International Conference on Neural Information Processing Systems, NIPS'20", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> normalization provably avoids rank collapse for randomly initialised deep networks. In Proceedings of the 34th International Conference on Neural Information Processing Systems, NIPS'20, Red Hook, NY, USA, 2020. Curran Associates Inc. ISBN 9781713829546.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Pre-training of deep bidirectional transformers for language understanding", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M.-W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Pre-training of deep bidirectional transformers for lan- guage understanding, 2018. URL http://arxiv. org/abs/1810.04805.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Attention is not all you need: pure attention loses rank doubly exponentially with depth", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J.-B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the 38th International Conference on Machine Learning", "volume": "139", "issue": "", "pages": "18--24", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Attention is not all you need: pure attention loses rank doubly expo- nentially with depth. In <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, T. (eds.), Proceedings of the 38th International Conference on Machine Learning, volume 139 of Proceedings of Ma- chine Learning Research, pp. 2793-2803. PMLR, 18- 24 Jul 2021. URL https://proceedings.mlr. press/v139/dong21a.html.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "A", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "X", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, J<PERSON>, and <PERSON><PERSON><PERSON>, N. An image is worth 16x16 words: Transformers for image recognition at scale. In International Conference on Learning Representations, 2021. URL https:// openreview.net/forum?id=YicbFdNTTy.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Computing nonvacuous generalization bounds for deep (stochastic) neural networks with many more parameters than training data", "authors": [{"first": "G", "middle": ["K"], "last": "Dziugaite", "suffix": ""}, {"first": "D", "middle": ["M"], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "Proceedings of the 33rd Annual Conference on Uncertainty in Artificial Intelligence (UAI)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, D. M. Computing nonvacuous generalization bounds for deep (stochastic) neural net- works with many more parameters than training data. In Proceedings of the 33rd Annual Conference on Uncer- tainty in Artificial Intelligence (UAI), 2017.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Multihorizon time series forecasting with temporal attention learning", "authors": [{"first": "C", "middle": [], "last": "Fan", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Pan", "suffix": ""}, {"first": "X", "middle": [], "last": "Li", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Pei", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 25th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining, KDD '19", "volume": "", "issue": "", "pages": "2527--2535", "other_ids": {"DOI": ["10.1145/3292500.3330662"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, H. Multi- horizon time series forecasting with temporal attention learning. In Proceedings of the 25th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining, KDD '19, pp. 2527-2535, New York, NY, USA, 2019. Association for Computing Machin- ery. ISBN 9781450362016. doi: 10.1145/3292500. 3330662. URL https://doi.org/10.1145/ 3292500.3330662.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Sharpness-aware minimization for efficiently improving generalization", "authors": [{"first": "P", "middle": [], "last": "Foret", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Mo<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Sharpness-aware minimization for efficiently improv- ing generalization. In International Conference on Learning Representations, 2021. URL https:// openreview.net/forum?id=6Tm1mposlrM.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Understanding the difficulty of training deep feedforward neural networks", "authors": [{"first": "X", "middle": [], "last": "Glorot", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2010, "venue": "Proceedings of the Thirteenth International Conference on Artificial Intelligence and Statistics", "volume": "9", "issue": "", "pages": "13--15", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON>. Understanding the diffi- culty of training deep feedforward neural networks. In <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> (eds.), Proceed- ings of the Thirteenth International Conference on Ar- tificial Intelligence and Statistics, volume 9 of Pro- ceedings of Machine Learning Research, pp. 249- 256, Chia Laguna Resort, Sardinia, Italy, 13-15 May 2010. PMLR. URL https://proceedings.mlr. press/v9/glorot10a.html.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Deep transformers without shortcuts: Modifying self-attention for faithful signal propagation", "authors": [{"first": "B", "middle": [], "last": "He", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": ["L"], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": ["W"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, Y. W. Deep transformers with- out shortcuts: Modifying self-attention for faithful sig- nal propagation. In The Eleventh International Confer- ence on Learning Representations, 2023. URL https: //openreview.net/forum?id=NPrsUQgMjKK.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Topics in Matrix Analysis", "authors": [{"first": "R", "middle": ["A"], "last": "Horn", "suffix": ""}, {"first": "C", "middle": ["R"], "last": "<PERSON>", "suffix": ""}], "year": 1991, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> <PERSON> in Matrix Analysis. Cambridge University Press, 1991.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Matplotlib: A 2d graphics environment", "authors": [{"first": "J", "middle": ["D"], "last": "<PERSON>", "suffix": ""}], "year": 2007, "venue": "Computing in Science & Engineering", "volume": "9", "issue": "3", "pages": "90--95", "other_ids": {"DOI": ["10.1109/MCSE.2007.55"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>: A 2d graphics environment. Com- puting in Science & Engineering, 9(3):90-95, 2007. doi: 10.1109/MCSE.2007.55.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "On large-batch training for deep learning: Generalization gap and sharp minima", "authors": [{"first": "N", "middle": ["S"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Nocedal", "suffix": ""}, {"first": "M", "middle": [], "last": "Smelyanskiy", "suffix": ""}, {"first": "P", "middle": ["T P"], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, P. T. P. On large-batch training for deep learning: Generalization gap and sharp minima. In International Conference on Learning Representations, 2017. URL https://openreview.net/forum? id=H1oyRlYgg.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "The lipschitz constant of self-attention", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>ni<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the 38th International Conference on Machine Learning", "volume": "139", "issue": "", "pages": "18--24", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> The lipschitz constant of self-attention. In <PERSON>, <PERSON><PERSON> and <PERSON>, T. (eds.), Proceedings of the 38th International Confer- ence on Machine Learning, volume 139 of Proceedings of Machine Learning Research, pp. 5562-5571. PMLR, 18-24 Jul 2021a. URL https://proceedings. mlr.press/v139/kim21i.html.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Reversible instance normalization for accurate time-series forecasting against distribution shift", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Park", "suffix": ""}, {"first": "J.-<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations, 2021b", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, J. <PERSON>ersible instance normalization for ac- curate time-series forecasting against distribution shift. In International Conference on Learning Representa- tions, 2021b. URL https://openreview.net/ forum?id=cGDAkQo1C0p.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "A method for stochastic optimization", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ba", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>: A method for stochastic optimization. In International Conference on Learning Representations (ICLR), San Diega, CA, USA, 2015.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Reformer: The efficient transformer", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "A", "middle": [], "last": "Lev<PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: The efficient transformer. In International Conference on Learning Representations, 2020. URL https:// openreview.net/forum?id=rkgNKkHtvB.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Modeling long-and short-term temporal patterns with deep neural networks", "authors": [{"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W.-C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "The 41st International ACM SI-GIR Conference on Research & Development in Information Retrieval, SIGIR '18", "volume": "", "issue": "", "pages": "95--104", "other_ids": {"DOI": ["10.1145/3209978.3210006"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Model- ing long-and short-term temporal patterns with deep neural networks. In The 41st International ACM SI- GIR Conference on Research & Development in In- formation Retrieval, SIGIR '18, pp. 95-104, New York, NY, USA, 2018a. Association for Computing Machinery. ISBN 9781450356572. doi: 10.1145/ 3209978.3210006. URL https://doi.org/10. 1145/3209978.3210006.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Modeling long-and short-term temporal patterns with deep neural networks", "authors": [{"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W.-C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Association for Computing Machinery, SIGIR '18", "volume": "", "issue": "", "pages": "95--104", "other_ids": {"DOI": ["10.1145/3209978.3210006"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Model- ing long-and short-term temporal patterns with deep neural networks. In Association for Computing Ma- chinery, SIGIR '18, pp. 95-104, New York, NY, USA, 2018b. ISBN 9781450356572. doi: 10.1145/ 3209978.3210006. URL https://doi.org/10. 1145/3209978.3210006.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Enhancing the locality and breaking the memory bottleneck of transformer on time series forecasting", "authors": [{"first": "S", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y.-X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Yan", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Beygelzimer", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Gao", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Han", "suffix": ""}], "year": 2019, "venue": "Advances in Neural Information Processing Systems", "volume": "32", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>-<PERSON><PERSON>, and <PERSON>, <PERSON>. Enhancing the locality and breaking the memory bottleneck of transformer on time series forecasting. In <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> (eds.), Advances in Neural Information Processing Systems, volume 32. Curran Associates, Inc., 2019. URL https://proceedings.neurips. cc/paper_files/paper/2019/file/ 6775a0635c302542da2c32aa19d86be0-Paper. pdf. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Under- standing the difficulty of training transformers. In Pro- ceedings of the 2020 Conference on Empirical Methods in Natural Language Processing (EMNLP 2020), 2020.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Pyraformer: Low-complexity pyramidal attention for long-range time series modeling and forecasting", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["X"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Dustdar", "suffix": ""}], "year": 2022, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. Pyraformer: Low-complexity pyramidal at- tention for long-range time series modeling and forecast- ing. In International Conference on Learning Represen- tations, 2022. URL https://openreview.net/ forum?id=0EXmFzUn5I.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "itransformer: Inverted transformers are effective for time series forecasting", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Hu", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Ma", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. itransformer: Inverted transformers are effective for time series forecasting. In The Twelfth International Conference on Learning Representations, 2024. URL https://openreview.net/forum? id=JePfAI8fah.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "SGDR: Stochastic gradient descent with warm restarts", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>: Stochastic gradient descent with warm restarts. In International Conference on Learning Representations, 2017. URL https:// openreview.net/forum?id=Skq89Scxx.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Decoupled weight decay regularization", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON>pled weight decay regu- larization. In International Conference on Learning Rep- resentations, 2019. URL https://openreview. net/forum?id=Bkg6RiCqY7.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "A method for solving the convex programming problem with convergence rate", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1983, "venue": "Proceedings of the USSR Academy of Sciences", "volume": "269", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, Y. A method for solving the convex program- ming problem with convergence rate o(1/k 2 ). Proceed- ings of the USSR Academy of Sciences, 269:543-547, 1983. URL https://api.semanticscholar. org/CorpusID:145918791.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "A time series is worth 64 words: Long-term forecasting with transformers", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": ["H"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, J. A time series is worth 64 words: Long-term forecasting with transformers. In The Eleventh International Confer- ence on Learning Representations, 2023. URL https: //openreview.net/forum?id=Jbdc0vTOcol.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Toward understanding why adam converges faster than SGD for transformers", "authors": [{"first": "Y", "middle": [], "last": "Pan", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}], "year": 2022, "venue": "OPT 2022: Optimization for Machine Learning (NeurIPS 2022 Workshop)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "OpenAI. Gpt-4 technical report, 2023. <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. Toward understanding why adam con- verges faster than SGD for transformers. In OPT 2022: Optimization for Machine Learning (NeurIPS 2022 Workshop), 2022. URL https://openreview. net/forum?id=Sf1NlV2r6PO.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "An imperative style, high-performance deep learning library", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Gross", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Gimelshein", "suffix": ""}, {"first": "L", "middle": [], "last": "Antiga", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>son", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Pytor<PERSON>", "suffix": ""}], "year": 2019, "venue": "Advances in Neural Information Processing Systems", "volume": "32", "issue": "", "pages": "8024--8035", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Brad<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N., Anti<PERSON>, L., <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: An imperative style, high-performance deep learning library. In Advances in Neural Information Processing Systems 32, pp. 8024- 8035. Curran Associates, Inc., 2019.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Scikit-learn: Machine learning in Python", "authors": [{"first": "F", "middle": [], "last": "Pedregosa", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Gramfort", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Blondel", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "Dubourg", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>p<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Passos", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Duchesnay", "suffix": ""}], "year": 2011, "venue": "Journal of Machine Learning Research", "volume": "12", "issue": "", "pages": "2825--2830", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>-learn: Machine learning in Python. Journal of Machine Learning Research, 12:2825-2830, 2011.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Improving language understanding by generative pretraining", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "OpenAI Tech Report", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Improving language understanding by generative pre- training. 2018 OpenAI Tech Report, 2018.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Deep state space models for time series forecasting", "authors": [{"first": "S", "middle": ["S"], "last": "Rangapuram", "suffix": ""}, {"first": "M", "middle": ["W"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2011, "venue": "Advances in Neural Information Processing Systems", "volume": "31", "issue": "", "pages": "3413--3430", "other_ids": {}, "num": null, "urls": [], "raw_text": "Rangapuram, S. S., <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, T. Deep state space models for time series forecasting. In Bengio, S., <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> (eds.), Advances in Neural Information Processing Systems, volume 31. Curran Associates, Inc., 2018. URL https://proceedings.neurips. cc/paper_files/paper/2018/file/ 5cf68969fb67aa6082363a6d4e6468e2-Paper. pdf. <PERSON>, B. A simpler approach to matrix completion. J. Mach. Learn. Res., 12(null):3413-3430, dec 2011. ISSN 1532-4435.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Guaranteed minimum-rank solutions of linear matrix equations via nuclear norm minimization", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Fazel", "suffix": ""}, {"first": "P", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2010, "venue": "SIAM Review", "volume": "52", "issue": "3", "pages": "471--501", "other_ids": {"DOI": ["10.1137/070697835"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> <PERSON> Guaranteed minimum-rank solutions of linear matrix equations via nuclear norm minimization. SIAM Review, 52(3):471- 501, 2010. doi: 10.1137/070697835. URL https: //doi.org/10.1137/070697835.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Probabilistic forecasting with autoregressive recurrent networks", "authors": [{"first": "D", "middle": [], "last": "Salinas", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Deep<PERSON>", "suffix": ""}], "year": 2020, "venue": "International Journal of Forecasting", "volume": "36", "issue": "3", "pages": "1181--1191", "other_ids": {"DOI": ["10.1016/j.ijforecast.2019.07"], "ISSN": ["0169- 2070"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>: Probabilistic forecasting with autore- gressive recurrent networks. International Journal of Forecasting, 36(3):1181-1191, 2020. ISSN 0169- 2070. doi: https://doi.org/10.1016/j.ijforecast.2019.07.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "URL", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "URL https://www.sciencedirect.com/ science/article/pii/S0169207019301888.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Think globally, act locally: a deep neural network approach to highdimensional time series forecasting", "authors": [{"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H.-F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 33rd International Conference on Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, I. Think globally, act locally: a deep neural network approach to high- dimensional time series forecasting. In Proceedings of the 33rd International Conference on Neural Informa- tion Processing Systems, Red Hook, NY, USA, 2019. Curran Associates Inc.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Forecasting stock market prices using machine learning and deep learning models: A systematic review, performance analysis and discussion of implications", "authors": [{"first": "G", "middle": [], "last": "Sonkavde", "suffix": ""}, {"first": "D", "middle": ["S"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["T"], "last": "Deokate", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["K"], "last": "Bhat", "suffix": ""}], "year": 2023, "venue": "International Journal of Financial Studies", "volume": "11", "issue": "3", "pages": "", "other_ids": {"DOI": ["10.3390/ijfs11030094"], "ISSN": ["2227-7072"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, S. K. Forecasting stock market prices using machine learning and deep learning models: A systematic review, performance analysis and discussion of implications. International Journal of Fi- nancial Studies, 11(3), 2023. ISSN 2227-7072. doi: 10.3390/ijfs11030094. URL https://www.mdpi. com/2227-7072/11/3/94.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Methodology for long-term prediction of time series", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Lendasse", "suffix": ""}], "year": 2007, "venue": "Neurocomputing", "volume": "70", "issue": "16", "pages": "2861--2869", "other_ids": {"DOI": ["10.1016/j.neucom.2006.06.015"], "ISSN": ["0925-2312"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Methodology for long-term pre- diction of time series. Neurocomputing, 70 (16):2861-2869, 2007. ISSN 0925-2312. doi: https://doi.org/10.1016/j.neucom.2006.06.015. URL https://www.sciencedirect.com/ science/article/pii/S0925231207001610. Neural Network Applications in Electrical Engineering Selected papers from the 3rd International Work- Conference on Artificial Neural Networks (IWANN 2005).", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Training data-efficient image transformers and distillation through attention", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the 38th International Conference on Machine Learning", "volume": "139", "issue": "", "pages": "18--24", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, H. Training data-efficient image transformers and distillation through atten- tion. In <PERSON>, <PERSON><PERSON> and <PERSON>, T<PERSON> (eds.), Proceed- ings of the 38th International Conference on Ma- chine Learning, volume 139 of Proceedings of Ma- chine Learning Research, pp. 10347-10357. PMLR, 18- 24 Jul 2021. URL https://proceedings.mlr. press/v139/touvron21a.html.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Llama: Open and efficient foundation language models", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "I<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M.-A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "E", "middle": [], "last": "Hambro", "suffix": ""}, {"first": "F", "middle": [], "last": "Azhar", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Grave", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, T., <PERSON>, B., Goyal, N., Hambro, E., <PERSON>, F<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, E., and <PERSON>, G. <PERSON>: Open and efficient foundation lan- guage models, 2023. URL http://arxiv.org/ abs/2302.13971. cite arxiv:2302.13971.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Mimetic initialization of self-attention layers", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["Z"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 40th International Conference on Machine Learning, ICML'23", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> <PERSON>. Mimetic initialization of self-attention layers. In Proceedings of the 40th Inter- national Conference on Machine Learning, ICML'23. JMLR.org, 2023.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Electricity dataset", "authors": [{"first": "", "middle": [], "last": "Uci", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "UCI. Electricity dataset, 2015. URL https: //archive.ics.uci.edu/dataset/321/ electricityloaddiagrams20112014.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Python reference manual. Centrum voor Wiskunde en Informatica Amsterdam", "authors": [{"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": ["L"], "last": "<PERSON>", "suffix": ""}], "year": 1995, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, F. L. Python reference man- ual. Centrum voor Wiskunde en Informatica Amster- dam, 1995.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "f5ee243547dee91fbd053c1c4a845aa-Paper", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": ["U"], "last": "Kaiser", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Guyon", "suffix": ""}, {"first": "U", "middle": ["V"], "last": "Luxburg", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Woo", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Sahoo", "suffix": ""}], "year": 2017, "venue": "Advances in Neural Information Processing Systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Attention is all you need. In Guyon, I., Luxburg, U. V., <PERSON>gio, S., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON> (eds.), Advances in Neural Information Processing Systems, volume 30. Curran Associates, Inc., 2017. URL https://proceedings.neurips. cc/paper_files/paper/2017/file/ 3f5ee243547dee91fbd053c1c4a845aa-Paper. pdf. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, D. Unified training of universal time series fore- casting transformers, 2024.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Autoformer: Decomposition transformers with Auto-Correlation for long-term series forecasting", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. Autoformer: Decomposition transformers with Auto-Correlation for long-term series forecasting. In Advances in Neural In- formation Processing Systems, 2021.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Efficient transformer for high-resolution image restoration", "authors": [{"first": "S", "middle": ["W"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Arora", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Hay<PERSON>", "suffix": ""}, {"first": "F", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "M.<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, S. <PERSON>., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, F. S., and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>: Efficient transformer for high-resolution image restoration. In CVPR, 2022.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Are transformers effective for time series forecasting", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Are transform- ers effective for time series forecasting? In Proceedings of the AAAI Conference on Artificial Intelligence, 2023.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Stabilizing transformer training by preventing attention entropy collapse", "authors": [{"first": "S", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Littwin", "suffix": ""}, {"first": "D", "middle": [], "last": "Busbridge", "suffix": ""}, {"first": "J", "middle": [], "last": "Ramapuram", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "J", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 40th International Conference on Machine Learning", "volume": "202", "issue": "", "pages": "23--29", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Busbridge, D., Ramapuram, J<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. M. Stabilizing transformer training by preventing attention entropy collapse. In <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, J. (eds.), Proceedings of the 40th International Conference on Machine Learning, volume 202 of Proceedings of Ma- chine Learning Research, pp. 40770-40803. PMLR, 23- 29 Jul 2023. URL https://proceedings.mlr. press/v202/zhai23a.html.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Resnest: Split-attention networks", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Sun", "suffix": ""}, {"first": "T", "middle": [], "last": "He", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Li", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "2736--2746", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: Split-attention networks. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR) Workshops, pp. 2736-2746, June 2022.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Why are adaptive methods good for attention models?", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": ["P"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Veit", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Informer", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "15383--15393", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Why are adaptive methods good for attention models? In <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON> (eds.), Advances in Neural Information Processing Systems, volume 33, pp. 15383-15393. Curran Associates, Inc., 2020. URL https://proceedings.neurips. cc/paper_files/paper/2020/file/ b05b57f6add810d3b7490866d74c0053-Paper. pdf. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, W. Informer: Beyond efficient transformer", "links": null}}, "ref_entries": {"FIGREF0": {"num": null, "fig_num": null, "text": "Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "type_str": "figure", "uris": null}, "FIGREF1": {"num": null, "fig_num": "1", "text": "Figure1: Illustration of our approach on synthetic data. Oracle is the optimal solution, Transformer is a base transformer, σReparam is a Transformer with weight rescaling(<PERSON><PERSON> et al., 2023) and Transformer + SAM is Transformer trained with sharpness-aware minimization.Transformer overfits, σReparam improves slightly but fails to reach Oracle while Transformer+SAM generalizes perfectly. This motivates SAMformer, a shallow transformer combining SAM and best practices in time series forecasting.", "type_str": "figure", "uris": null}, "FIGREF2": {"num": null, "fig_num": "2", "text": "Figure 2: Poor generalization. Despite its simplicity, Transformer suffers from severe overfitting. Fixing the attention weights in Random Transformer improves the generalization, hinting at the role of attention in preventing convergence to optimal local minima.", "type_str": "figure", "uris": null}, "FIGREF3": {"num": null, "fig_num": null, "text": "Sharpness at the end of the training, Entropy collapse.", "type_str": "figure", "uris": null}, "FIGREF4": {"num": null, "fig_num": "3", "text": "Figure 3: Transformer's loss landscape analysis for linear regression. (a) The attention matrices of Transformer get stuck to identity from the first epoch. (b, left) Transformer converges to sharper minimum than Transformer+SAM with much larger λ max (∼ ×10 4 ), while Random Transformer has a smooth loss landscape. (b, right) Transformer suffers from entropy collapse during training confirming the high sharpness of its loss landscape.", "type_str": "figure", "uris": null}, "FIGREF5": {"num": null, "fig_num": null, "text": "Figure 4: SAMformer", "type_str": "figure", "uris": null}, "FIGREF6": {"num": null, "fig_num": null, "text": "Performance across runs of SAMformer and Transformer.", "type_str": "figure", "uris": null}, "FIGREF7": {"num": null, "fig_num": "5", "text": "Figure 5: (a) SAMformer has a smoother loss landscape than Transformer. (b) SAMformer consistently generalize well for every initialization while Transformer is unstable and heavily depends on the seed.", "type_str": "figure", "uris": null}, "FIGREF8": {"num": null, "fig_num": "8", "text": "Figure 8: Suboptimality of σReparam. (a) σReparam alone does not bring improvement on Transformer and is clearly outperformed by SAMformer. Combining σReparam with SAMformer does not bring significant improvement but heavily increases the training time (see Figure 11).", "type_str": "figure", "uris": null}, "FIGREF9": {"num": null, "fig_num": "9", "text": "Figure 9: Test Mean Squared error on all datasets for a prediction horizon H ∈ {96, 192} across five different seed values for Transformer and SAMformer. This plot reveals a significant variance for the Transformer, as opposed to the minimal variance of SAMformer, showing the high impact of weight initialization on Transformer and the high resilience of SAMformer.", "type_str": "figure", "uris": null}, "FIGREF10": {"num": null, "fig_num": "10", "text": "Figure 10: Test Mean Squared error on all datasets for a prediction horizon H ∈ {336, 720} across five different seed values for Transformer and SAMformer. This plot reveals a significant variance for the Transformer, as opposed to the minimal variance of SAMformer, showing the high impact of weight initialization on Transformer and the high resilience of SAMformer.", "type_str": "figure", "uris": null}, "FIGREF11": {"num": null, "fig_num": null, "text": "AdamW with wd ∈ {1e-5, 1e-4}.", "type_str": "figure", "uris": null}, "FIGREF12": {"num": null, "fig_num": "15", "text": "Figure 15: Illustration of different optimizers on synthetic data generated with Eq. (2) where Oracle is the least-square solution. We saw in Figure 1 that with <PERSON>, Transformer overfits and has poor performance while SAMformer smoothly reaches the oracle. (a) We can see that using SG<PERSON> and <PERSON> with weight decay wd = 1e-5 leads to huge loss magnitudes and fails to converge. (b) With well-chosen weight decays (wd ∈ {1e-3, 1e-4}), training Transformer with Adam<PERSON> leads to similar performance than <PERSON>. The overfitting is noticeable and the training is unstable. AdamW does not bring more stabilization and is very sensitive to the hyperparameters. Hence, this toy example motivates us to conduct our thorough experiments with the optimizer Adam. C.4. Ablation on the Implementation.", "type_str": "figure", "uris": null}, "FIGREF13": {"num": null, "fig_num": null, "text": "Definition D.2 (<PERSON><PERSON>, <PERSON> et al. (2021)). For a given ρ ≥ 0, the sharpness of L train at ω writes s(ω, ρ) := max ∥ϵ∥2≤ρ L train (ω + ϵ) -L train (ω). (12) Remark D.1 (Interpretation of ρ). Instead of simply minimizing the training objective L train , SAM searches for parameters ω achieving both low training loss and low curvature in a ball B(ω, ρ). The hyperparameter ρ ≥ 0 corresponds to the size of the neighborhood on which the parameters search is done. In particular, taking ρ = 0 is equivalent to the usual minimization of L train . In particular, SAM incorporates sharpness in the learning objective, resulting in the problem of minimizing w.r.t ω L SAM train (ω) := max ∥ϵ∥2≤ρ L train (ω + ϵ) =Ltrain(ω)+s(ω,ρ).", "type_str": "figure", "uris": null}, "FIGREF14": {"num": null, "fig_num": null, "text": "train (ω) + ϵ ⊤ ∇L train (ω) = arg max ∥ϵ∥2≤ρ ϵ ⊤ ∇L train (ω) ,", "type_str": "figure", "uris": null}, "FIGREF15": {"num": null, "fig_num": null, "text": "Lemma E.1. Let S ∈ R n×m and B ∈ R m×m . If B has full rank, then rank(SB) = rank(BS) = rank(S).", "type_str": "figure", "uris": null}, "FIGREF16": {"num": null, "fig_num": null, "text": ". Let S ∈ R n×m , B = R p×q and C ∈ R n×q . There exists matrices Y ∈ R m×q and Z ∈ R n×p such that SY -ZB = C if, and only if, Assume that there exists Y ∈ R m×q and Z ∈ R n×p such that SY -ZB = C. Recall that the following equality holds S SY -", "type_str": "figure", "uris": null}, "FIGREF17": {"num": null, "fig_num": null, "text": "ki [Be j ] k = e ⊤ i Be j ≥ 0. (B ≽ 0) Hence, as B is positive semi-definite, the bij are nonnegative. It follows that λ min (S) the definition of B, the orthogonality of P and the cyclic property of the trace operation, we have i bii = Tr B = Tr P ⊤ BP = Tr", "type_str": "figure", "uris": null}, "FIGREF18": {"num": null, "fig_num": null, "text": "min (S) Tr(B) ≤ Tr(SB) ≤ λ max (S) Tr(B).", "type_str": "figure", "uris": null}, "FIGREF19": {"num": null, "fig_num": null, "text": "PΛP ⊤ ×n = PΛ × ΛP ⊤ . . . PΛ × ΛP ⊤ ×n (orthogonality of P) = P Λ × Λ × • • • × Λ × Λ ×n P ⊤ (orthogonality of P) = PΛ n P ⊤ .The diagonality of Λ suffices to deduct the remark on the eigenvalues of S n .E.4. Proof of Proposition D.1", "type_str": "figure", "uris": null}, "TABREF0": {"num": null, "html": null, "text": "Performance comparison between our model (SAMformer) and baselines for multivariate long-term forecasting with different horizons H. Results marked with \" † \" are obtained from<PERSON><PERSON> et al. (2024) and those marked with \" * \" are obtained from<PERSON><PERSON> et al. (2023), along with the publication year of the respective methods. Transformer-based models are abbreviated by removing the \"former\" part of their name. We display the average test MSE with standard deviation obtained on 5 runs with different seeds. Best results are in bold, second best are underlined. approaches, benefiting from a shallow lightweight implementation, i.e., a single layer with one attention head. The number of parameters of SAMformer and TSMixer is detailed in Appendix Table8. We observe that, on average, SAMformer has ∼ 4 times fewer parameters than TSMixer, which makes this approach even more remarkable. Importantly, TSMixer itself is recognized as a computationally efficient architecture compared to the transformer-based baselines(<PERSON> et al.,  2023, Table 6).", "type_str": "table", "content": "<table><tr><td>Transformer</td><td>Reparam</td><td>SAMformer</td></tr><tr><td/><td/><td>0.8</td></tr><tr><td/><td/><td>0.5</td></tr><tr><td/><td/><td>0.2</td></tr></table>"}, "TABREF1": {"num": null, "html": null, "text": "Comparison performance of SAMformer and MOIRAI (<PERSON><PERSON> et al., 2024) for multivariate long-term forecasting. We display the test MSE averaged over horizons {96, 192, 336, 720}. Best results are in bold, second best are underlined.", "type_str": "table", "content": "<table><tr><td>Dataset</td><td>Full-shot</td><td colspan=\"3\">Zero-shot (Woo et al., 2024).</td></tr><tr><td/><td colspan=\"4\">SAMformer MOIRAI Small MOIRAI Base MOIRAI Large</td></tr><tr><td>ETTh1</td><td>0.410</td><td>0.400</td><td>0.434</td><td>0.510</td></tr><tr><td>ETTh2</td><td>0.344</td><td>0.341</td><td>0.345</td><td>0.354</td></tr><tr><td>ETTm1</td><td>0.373</td><td>0.448</td><td>0.381</td><td>0.390</td></tr><tr><td>ETTm2</td><td>0.269</td><td>0.300</td><td>0.272</td><td>0.276</td></tr><tr><td>Electricity</td><td>0.181</td><td>0.233</td><td>0.188</td><td>0.188</td></tr><tr><td>Weather</td><td>0.260</td><td>0.242</td><td>0.238</td><td>0.259</td></tr><tr><td colspan=\"2\">Overall MSE improvement</td><td>6.9%</td><td>1.1%</td><td>7.6%</td></tr></table>"}, "TABREF2": {"num": null, "html": null, "text": "The test MSE of SAMformer and TSMixer is depicted in Figure14 of", "type_str": "table", "content": "<table><tr><td/><td/><td>ETTh1</td><td/><td/><td>Exchange</td><td/><td/><td>ETTh1</td><td/><td/><td>Exchange</td></tr><tr><td/><td>0.63</td><td/><td>1.46</td><td/><td/><td/><td>0.43</td><td/><td>1.0</td><td/><td/></tr><tr><td>Test MSE</td><td>0.51</td><td/><td>0.82</td><td/><td/><td>Test MSE</td><td>0.41</td><td/><td>0.59</td><td/><td/></tr><tr><td/><td>0.39</td><td/><td>0.19</td><td/><td/><td/><td>0.38</td><td/><td>0.18</td><td/><td/></tr><tr><td/><td>96</td><td>192 Prediction Horizon H 336</td><td>720</td><td>96</td><td>192 Prediction Horizon H 336</td><td>720</td><td>96</td><td>192 Prediction Horizon H 336</td><td>720</td><td>96</td><td>192 Prediction Horizon H 336</td><td>720</td></tr><tr><td/><td/><td>Reparam</td><td>Transformer</td><td/><td>SAMformer</td><td/><td/><td>SAMformer</td><td colspan=\"3\">SAMformer + Reparam</td></tr><tr><td colspan=\"7\">(a) Comparison of Transformer, σReparam and SAMformer.</td><td/><td/><td/><td/><td/></tr></table>"}, "TABREF3": {"num": null, "html": null, "text": "In The Thirty-Fifth AAAI Conference on Artificial Intelligence, AAAI 2021, Virtual Conference, volume 35, pp. 11106-11115. AAAI Press, 2021.", "type_str": "table", "content": "<table><tr><td><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Sun, L., and <PERSON>,</td></tr><tr><td>R. FEDformer: Frequency enhanced decomposed trans-</td></tr><tr><td>former for long-term series forecasting. In Proc. 39th</td></tr><tr><td>International Conference on Machine Learning (ICML</td></tr><tr><td>2022), 2022.</td></tr></table>"}, "TABREF4": {"num": null, "html": null, "text": "Architecture and Training Parameters . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 16 A.2 Datasets . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 16 A.3 More Details on the Baselines . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 17 Neighborhood size ρ * at which SAMformer and TSMixer achieve their best performance on the benchmarks. <PERSON> et al., 2021) contain the time series collected by electricity transformers from July 2016 to July 2018. Whenever possible, we refer to this set of 4 datasets as ETT. Electricity (UCI, 2015) contains the time series of electricity consumption from 321 clients from 2012 to 2014. Exchange (Lai et al., 2018b) contains the time series of daily exchange rates between 8 countries from 1990 to 2016. Traffic (California Department of Transportation, 2021) contains the time series of road occupancy rates captured by 862 sensors from January 2015 to December 2016. Last but not least, Weather (Max Planck Institute, 2021) contains the time series of meteorological information recorded by 21 weather indicators in 2020. It should be noted that Electricity, Traffic, and Weather are large-scale datasets. The ETT datasets can be downloaded here while the 4 other datasets can be downloaded here. Table 5 sums up the characteristics of the datasets used in our experiments.", "type_str": "table", "content": "<table><tr><td>Table of Contents</td></tr><tr><td>A Experimental Setup</td></tr></table>"}, "TABREF5": {"num": null, "html": null, "text": "Characteristics of the multivariate time series datasets used in our experiments with various sizes and dimensions.", "type_str": "table", "content": "<table><tr><td>Dataset</td><td colspan=\"6\">ETTh1/ETTh2 ETTm1/ETTm2 Electricity Exchange Traffic Weather</td></tr><tr><td># features</td><td>7</td><td>7</td><td>321</td><td>8</td><td>862</td><td>21</td></tr><tr><td># time steps</td><td>17420</td><td>69680</td><td>26304</td><td colspan=\"3\">7588 17544 52696</td></tr><tr><td>Granularity</td><td>1 hour</td><td>15 minutes</td><td>1 hour</td><td colspan=\"3\">1 day 1 hour 10 minutes</td></tr></table>"}, "TABREF6": {"num": null, "html": null, "text": "Performance comparison between our model (SAMformer) and baselines for multivariate long-term forecasting with different horizons H. Results marked with \" † \" are obtained from<PERSON><PERSON> et al. (2024) and those marked with \" * \" are obtained from<PERSON><PERSON> et al. (2023), along with the publication year of the respective methods. Transformer-based models are abbreviated by removing the \"former\" part of their name. We display the average test MAE with standard deviation obtained on 5 runs with different seeds. Best results are in bold, second best are underlined.", "type_str": "table", "content": "<table><tr><td/><td/><td colspan=\"2\">with SAM</td><td/><td/><td colspan=\"2\">without SAM</td><td/></tr><tr><td colspan=\"2\">Dataset H</td><td colspan=\"6\">SAMformer TSMixer Transformer TSMixer iTrans  † PatchTST  †</td><td>In  *</td><td>Auto  *  FED  *</td></tr><tr><td/><td/><td>-</td><td>-</td><td>-</td><td>2023</td><td>2024</td><td>2023</td><td>2021</td><td>2021</td><td>2022</td></tr><tr><td>ETTh1</td><td colspan=\"3\">96 0.402 ±0.001 0.408 ±0.001 192 0.418 ±0.001 0.426 ±0.002 336 0.425 ±0.000 0.434 ±0.001 720 0.449 ±0.002 0.459 ±0.004</td><td>0.619 ±0.203 0.513 ±0.024 0.529 ±0.008 0.553 ±0.021</td><td>0.414 ±0.004 0.428 ±0.001 0.434 ±0.001 0.506 ±0.064</td><td>0.405 0.436 0.458 0.491</td><td>0.419 0.445 0.466 0.488</td><td>0.769 0.786 0.784 0.857</td><td>0.446 0.415 0.457 0.446 0.487 0.462 0.517 0.492</td></tr><tr><td>ETTh2</td><td colspan=\"3\">96 0.358 ±0.002 0.367 ±0.002 192 0.386 ±0.003 0.393 ±0.001 336 0.395 ±0.002 0.404 ±0.004 720 0.428 ±0.001 0.435 ±0.002</td><td>0.416 ±0.025 0.435 ±0.019 0.434 ±0.014 0.448 ±0.006</td><td>0.367 ±0.003 0.395 ±0.003 0.404 ±0.002 0.441 ±0.005</td><td>0.349 0.400 0.432 0.445</td><td>0.348 0.400 0.433 0.446</td><td>0.952 1.542 1.642 1.619</td><td>0.368 0.374 0.434 0.446 0.479 0.447 0.490 0.469</td></tr><tr><td>ETTm1</td><td colspan=\"3\">96 0.363 ±0.001 0.363 ±0.001 192 0.378 ±0.003 0.381 ±0.002 336 0.394 ±0.001 0.397 ±0.002 720 0.418 ±0.000 0.425 ±0.001</td><td>0.395 ±0.024 0.414 ±0.027 0.445 ±0.009 0.456 ±0.035</td><td>0.371 ±0.002 0.384 ±0.003 0.399 ±0.003 0.429 ±0.002</td><td>0.368 0.391 0.420 0.459</td><td>0.367 0.385 0.410 0.439</td><td>0.560 0.619 0.741 0.845</td><td>0.492 0.390 0.495 0.415 0.492 0.425 0.493 0.458</td></tr><tr><td>ETTm2</td><td colspan=\"3\">96 0.274 ±0.010 0.284 ±0.004 192 0.306 ±0.001 0.320 ±0.001 336 0.338 ±0.001 0.350 ±0.001 720 0.390 ±0.001 0.402 ±0.002</td><td>0.290 ±0.026 0.347 ±0.025 0.360 ±0.017 0.424 ±0.014</td><td>0.302 ±0.013 0.323 ±0.005 0.352 ±0.003 0.402 ±0.003</td><td>0.264 0.309 0.348 0.407</td><td>0.259 0.302 0.343 0.400</td><td>0.462 0.586 0.871 1.267</td><td>0.293 0.271 0.336 0.318 0.379 0.364 0.419 0.420</td></tr><tr><td>Electricity</td><td colspan=\"3\">96 0.252 ±0.002 0.273 ±0.001 192 0.263 ±0.001 0.292 ±0.011 336 0.277 ±0.000 0.297 ±0.007 720 0.306 ±0.000 0.321 ±0.006</td><td>0.288 ±0.013 0.304 ±0.033 0.315 ±0.018 0.330 ±0.014</td><td>0.277 ±0.003 0.304 ±0.027 0.317 ±0.018 0.333 ±0.015</td><td>----</td><td>----</td><td>0.393 0.417 0.422 0.427</td><td>0.313 0.302 0.324 0.311 0.327 0.328 0.342 0.344</td></tr><tr><td>Exchange</td><td colspan=\"3\">96 0.306 ±0.006 0.363 ±0.013 192 0.371 ±0.008 0.437 ±0.021 336 0.453 ±0.004 0.515 ±0.006 720 0.750 ±0.006 0.777 ±0.064</td><td>0.369 ±0.049 0.416 ±0.041 0.491 ±0.036 0.823 ±0.040</td><td colspan=\"2\">0.436 ±0.054 0.437 ±0.021 0.299 0.206 0.523 ±0.029 0.417 0.818 ±0.007 0.691</td><td>0.205 0.299 0.397 0.714</td><td>0.752 0.895 1.036 1.310</td><td>0.323 0.276 0.369 0.369 0.524 0.464 0.941 0.800</td></tr><tr><td>Traffic</td><td colspan=\"3\">96 0.292 ±0.001 0.300 ±0.020 192 0.294 ±0.005 0.317 ±0.012 336 0.292 ±0.000 0.299 ±0.000</td><td>0.306 ±0.033 0.321 ±0.034 0.348 ±0.093</td><td colspan=\"2\">0.300 ±0.020 0.268 0.419 ±0.218 0.276 0.501 ±0.163 0.283</td><td>0.295 0.296 0.304</td><td>0.410 0.435 0.434</td><td>0.371 0.359 0.382 0.380 0.387 0.375</td></tr><tr><td/><td colspan=\"3\">720 0.311 ±0.003 0.344 ±0.026</td><td>0.325 ±0.023</td><td colspan=\"2\">0.458 ±0.159 0.302</td><td>0.322</td><td>0.466</td><td>0.395 0.375</td></tr><tr><td>Weather</td><td colspan=\"3\">96 0.249 ±0.001 0.242 ±0.002 192 0.277 ±0.000 0.272 ±0.003 336 0.304 ±0.001 0.299 ±0.001 720 0.342 ±0.000 0.341 ±0.002</td><td>0.281 ±0.018 0.302 ±0.020 0.310 ±0.012 0.363 ±0.002</td><td colspan=\"2\">0.271 ±0.009 0.214 0.275 ±0.003 0.254 0.307 ±0.009 0.296 0.351 ±0.021 0.347</td><td>0.218 0.259 0.297 0.348</td><td>0.405 0.434 0.543 0.705</td><td>0.329 0.314 0.370 0.329 0.391 0.377 0.426 0.409</td></tr><tr><td colspan=\"3\">Overall MAE improvement</td><td>3.99%</td><td>11.63%</td><td>9.60%</td><td>2.05%</td><td>2.75%</td><td colspan=\"2\">53.00% 15.67% 9.93%</td></tr></table>"}, "TABREF7": {"num": null, "html": null, "text": "Significance test with Student's t-test and performance comparison between SAMformer and T<PERSON>ixer trained with SAM across various datasets and prediction horizons. We display the average and standard deviation of the test MSE obtained on 5 runs (mean ±std ). The performance of the best model is in bold when the improvement is statistically significant at the level 0.05 (p-value < 0.05). ±0.003 0.295 ±0.002 0.329 ±0.001 0.181 ±0.005 0.155 ±0.002 0.161 ±0.007 0.407 ±0.001 0.197 ±0.001 TSMixer 0.388 ±0.001 0.305 ±0.007 0.327 ±0.002 0.190 ±0.003 0.171 ±0.001 0.233 ±0.016 0.409 ±0.016 0.189 ±0.003 192 SAMformer 0.409 ±0.002 0.340 ±0.002 0.353 ±0.006 0.233 ±0.002 0.168 ±0.001 0.246 ±0.009 0.415 ±0.005 0.235 ±0.000 TSMixer 0.421 ±0.002 0.350 ±0.002 0.356 ±0.004 0.250 ±0.002 0.191 ±0.010 0.342 ±0.031 0.433 ±0.009 0.228 ±0.004 336 SAMformer 0.423 ±0.001 0.350 ±0.000 0.382 ±0.001 0.285 ±0.001 0.183 ±0.000 0.368 ±0.006 0.421 ±0.001 0.276 ±0.001 TSMixer 0.430 ±0.002 0.360 ±0.002 0.387 ±0.004 0.301 ±0.003 0.198 ±0.006 0.474 ±0.014 0.424 ±0.000 0.271 ±0.001 720 SAMformer 0.427 ±0.002 0.391 ±0.001 0.429 ±0.000 0.375 ±0.001 0.219 ±0.000 1.003 ±0.018 0.456 ±0.003 0.334 ±0.000 TSMixer 0.440 ±0.005 0.402 ±0.002 0.441 ±0.002 0.389 ±0.002 0.230 ±0.005 1.078 ±0.179 0.488 ±0.028 0.331 ±0.001", "type_str": "table", "content": "<table><tr><td>H</td><td>Model</td><td>ETTh1</td><td>ETTh2</td><td>ETTm1</td><td>ETTm2</td><td>Electricity</td><td>Exchange</td><td>Traffic</td><td>Weather</td></tr><tr><td>96</td><td colspan=\"2\">SAMformer 0.381</td><td/><td/><td/><td/><td/><td/><td/></tr></table>"}, "TABREF8": {"num": null, "html": null, "text": "Comparison of the number of parameters between SAMformer and TSMixer on the datasets described in Table5for prediction horizons H ∈ {96, 192, 336, 720}. We also compute the ratio between the number of parameters of TSMixer and the number of parameters of SAMformer. A ratio of 10 means that TSMixer has 10 times more parameters than SAMformer. For each dataset, we display in the last cell of the corresponding row the ratio averaged over all the horizons H. The overall ratio over all datasets and horizons is displayed in bold in the bottom right-hand cell.", "type_str": "table", "content": "<table><tr><td>Dataset</td><td colspan=\"2\">H = 96</td><td colspan=\"2\">H = 192</td><td colspan=\"2\">H = 336</td><td colspan=\"2\">H = 720</td><td>Total</td></tr><tr><td/><td colspan=\"8\">SAMformer TSMixer SAMformer TSMixer SAMformer TSMixer SAMformer TSMixer</td><td/></tr><tr><td>ETT</td><td>50272</td><td>124142</td><td>99520</td><td>173390</td><td>173392</td><td>247262</td><td>369904</td><td>444254</td><td>-</td></tr><tr><td>Exchange</td><td>50272</td><td>349344</td><td>99520</td><td>398592</td><td>173392</td><td>472464</td><td>369904</td><td>669456</td><td>-</td></tr><tr><td>Weather</td><td>50272</td><td>121908</td><td>99520</td><td>171156</td><td>173392</td><td>245028</td><td>369904</td><td>442020</td><td>-</td></tr><tr><td>Electricity</td><td>50272</td><td>280676</td><td>99520</td><td>329924</td><td>173392</td><td>403796</td><td>369904</td><td>600788</td><td>-</td></tr><tr><td>Traffic</td><td>50272</td><td>793424</td><td>99520</td><td>842672</td><td>173392</td><td>916544</td><td>369904</td><td>1113536</td><td>-</td></tr><tr><td>Avg. Ratio</td><td>6.64</td><td/><td>3.85</td><td/><td>2.64</td><td/><td>1.77</td><td/><td>3.73</td></tr></table>"}, "TABREF10": {"num": null, "html": null, "text": "and of the attention matrix. Finally, we observe", "type_str": "table", "content": "<table><tr><td/><td/><td>ETTh1</td><td>ETTh2</td><td>ETTm1</td><td>ETTm2</td></tr><tr><td/><td/><td>0.62</td><td>0.43</td><td>0.5</td><td>0.34</td></tr><tr><td colspan=\"2\">Test MSE</td><td>0.51</td><td>0.38</td><td>0.43</td><td>0.31</td></tr><tr><td/><td/><td>Transformer SAMformer 0.4</td><td>Transformer SAMformer 0.34</td><td>Transformer SAMformer 0.36</td><td>Transformer SAMformer 0.28</td></tr><tr><td/><td/><td>Electricity</td><td>Exchange</td><td>Traffic</td><td>Weather</td></tr><tr><td/><td/><td>0.24</td><td>0.56</td><td>0.83</td><td>0.28</td></tr><tr><td colspan=\"2\">Test MSE</td><td>0.21</td><td>0.44</td><td>0.6</td><td>0.28</td></tr><tr><td/><td/><td>Transformer SAMformer 0.17</td><td>Transformer SAMformer 0.33</td><td>Transformer SAMformer 0.36</td><td>Transformer SAMformer 0.27</td></tr><tr><td/><td/><td/><td>Transformer</td><td>SAMformer</td></tr><tr><td/><td/><td>ETTh1</td><td colspan=\"2\">(a) Prediction horizon H = 336. ETTh2 ETTm1</td><td>ETTm2</td></tr><tr><td/><td colspan=\"2\">0.68</td><td>0.44</td><td>0.53</td><td>0.47</td></tr><tr><td>Test MSE</td><td colspan=\"2\">0.53</td><td>0.41</td><td>0.47</td><td>0.41</td></tr><tr><td/><td colspan=\"2\">Transformer SAMformer 0.39</td><td>Transformer SAMformer 0.38</td><td>Transformer SAMformer 0.41</td><td>Transformer SAMformer 0.36</td></tr><tr><td/><td/><td>Electricity</td><td>Exchange</td><td>Traffic</td><td>Weather</td></tr><tr><td/><td colspan=\"2\">0.27</td><td>1.77</td><td>0.51</td><td>0.36</td></tr><tr><td>Test MSE</td><td colspan=\"2\">0.24</td><td>1.32</td><td>0.48</td><td>0.34</td></tr><tr><td/><td colspan=\"2\">Transformer SAMformer 0.21</td><td>Transformer SAMformer 0.88</td><td>Transformer SAMformer 0.44</td><td>Transformer SAMformer 0.33</td></tr><tr><td/><td/><td/><td>Transformer</td><td>SAMformer</td></tr></table>"}, "TABREF11": {"num": null, "html": null, "text": "Evolution of the test MSE on all datasets for a prediction horizon H ∈ {96, 192, 336, 720}. We display the average test MSE with a 95% confidence interval. We see that SAMformer consistently performs well with a low variance. Despite its lightweight (Table8), SAMformer surpasses TSMixer (trained with SAM) on 7 out of 8 datasets as shown in Table1 and Table 7.", "type_str": "table", "content": "<table><tr><td/><td/><td/><td/><td/><td>ETTh1</td><td/><td/><td>ETTh2</td><td/><td/><td colspan=\"2\">ETTm1</td><td/><td/><td>ETTm2</td></tr><tr><td/><td/><td/><td/><td>0.44</td><td/><td/><td>0.4</td><td/><td/><td>0.44</td><td/><td/><td>0.39</td><td/><td/></tr><tr><td/><td/><td/><td>Test MSE</td><td>0.41</td><td/><td/><td>0.35</td><td/><td/><td>0.38</td><td/><td/><td>0.28</td><td/><td/></tr><tr><td/><td/><td/><td/><td>0.38</td><td/><td/><td>0.3</td><td/><td/><td>0.33</td><td/><td/><td>0.18</td><td/><td/></tr><tr><td/><td/><td/><td/><td/><td colspan=\"2\">192 336 720</td><td colspan=\"3\">96 192 336 720</td><td/><td colspan=\"3\">96 192 336 720</td><td colspan=\"3\">96 192 336 720</td></tr><tr><td/><td/><td/><td/><td/><td>Electricity</td><td/><td/><td colspan=\"2\">Exchange</td><td/><td colspan=\"2\">Traffic</td><td/><td/><td>Weather</td></tr><tr><td/><td/><td/><td/><td>0.23</td><td/><td/><td>1.21</td><td/><td/><td>0.51</td><td/><td/><td>0.33</td><td/><td/></tr><tr><td/><td/><td/><td>Test MSE</td><td>0.19</td><td/><td/><td>0.7</td><td/><td/><td>0.45</td><td/><td/><td>0.26</td><td/><td/></tr><tr><td/><td/><td/><td/><td>0.16</td><td/><td/><td>0.18</td><td/><td/><td>0.4</td><td/><td/><td>0.19</td><td/><td/></tr><tr><td/><td/><td/><td/><td colspan=\"3\">96 192 336 720 Prediction Horizon H</td><td colspan=\"3\">96 192 336 720 Prediction Horizon H</td><td/><td colspan=\"3\">96 192 336 720 Prediction Horizon H</td><td colspan=\"3\">96 192 336 720 Prediction Horizon H</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"2\">TSMixer</td><td/><td>SAMformer</td><td/><td/><td/><td/></tr><tr><td colspan=\"3\">0.45 0.53 Figure 13: 0.38 H=96 Test MSE</td><td>ETTh1</td><td>0.29 0.35 0.41</td><td>ETTh2</td><td>0.32 0.36 0.4</td><td>ETTm1</td><td>0.17 0.2 0.23</td><td>ETTm2</td><td>0.15 0.18 0.21</td><td>Electricity</td><td>0.16 0.43 0.69</td><td>Exchange</td><td>0.3 0.64 0.98</td><td>Traffic</td><td>0.17 0.22 0.28</td><td>Weather</td></tr><tr><td/><td/><td>0.57</td><td/><td>0.42</td><td/><td>0.43</td><td/><td>0.28</td><td/><td>0.26</td><td/><td>0.54</td><td/><td>1.01</td><td/><td>0.31</td></tr><tr><td>H=192</td><td>Test MSE</td><td>0.49</td><td/><td>0.38</td><td/><td>0.39</td><td/><td>0.26</td><td/><td>0.21</td><td/><td>0.4</td><td/><td>0.64</td><td/><td>0.26</td></tr><tr><td/><td/><td>0.41</td><td/><td>0.34</td><td/><td>0.35</td><td/><td>0.23</td><td/><td>0.16</td><td/><td>0.25</td><td/><td>0.27</td><td/><td>0.22</td></tr><tr><td/><td/><td>0.58</td><td/><td>0.41</td><td/><td>0.47</td><td/><td>0.33</td><td/><td>0.33</td><td/><td>0.56</td><td/><td>1.05</td><td/><td>0.31</td></tr><tr><td>H=336</td><td>Test MSE</td><td>0.5</td><td/><td>0.38</td><td/><td>0.43</td><td/><td>0.31</td><td/><td>0.25</td><td/><td>0.46</td><td/><td>0.71</td><td/><td>0.29</td></tr><tr><td/><td/><td>0.43</td><td/><td>0.35</td><td/><td>0.38</td><td/><td>0.28</td><td/><td>0.18</td><td/><td>0.36</td><td/><td>0.37</td><td/><td>0.26</td></tr><tr><td/><td/><td>0.63</td><td/><td>0.43</td><td/><td>0.5</td><td/><td>0.45</td><td/><td>0.3</td><td/><td>1.48</td><td/><td>1.46</td><td/><td>0.37</td></tr><tr><td>H=720</td><td>Test MSE</td><td>0.53</td><td/><td>0.41</td><td/><td>0.46</td><td/><td>0.41</td><td/><td>0.26</td><td/><td>1.21</td><td/><td>0.87</td><td/><td>0.35</td></tr><tr><td/><td/><td>0.43</td><td/><td>0.39</td><td/><td>0.41</td><td/><td>0.38</td><td/><td>0.22</td><td/><td>0.94</td><td/><td>0.27</td><td/><td>0.32</td></tr><tr><td/><td/><td colspan=\"3\">0 Neighbourhood Size 0.5 1</td><td>0 Neighbourhood Size 0.5 1</td><td colspan=\"2\">0 Neighbourhood Size 0.5 1</td><td colspan=\"2\">0 Neighbourhood Size 0.5 1</td><td/><td>0 Neighbourhood Size 0.5 1</td><td/><td colspan=\"2\">0 Neighbourhood Size 0.5 1</td><td>0 Neighbourhood Size 0.5 1</td><td>0 Neighbourhood Size 0.5 1</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td>TSMixer</td><td colspan=\"2\">SAMformer</td><td/><td/><td/><td/></tr></table>"}, "TABREF12": {"num": null, "html": null, "text": "The Temporal Attention model is benchmarked against our Transformer model, which employs feature-based attention rather than time-step-based attention. We report in the last column the Overall improvement in MSE and MAE of Transformer over the Temporal Attention. This comparison reveals that channel-wise attention, i.e., focusing on features pairwise correlations, significantly boosts the performance, with a 12.97% improvement in MSE and 18.09% in MAE across all considered datasets. ±0.009 0.401 ±0.011 0.542 ±0.063 0.330 ±0.034 0.291 ±0.025 0.684 ±0.218 0.933 ±0.188 0.225 ±0.005 12.97% 192 0.510 ±0.014 0.414 ±0.020 0.615 ±0.056 0.394 ±0.033 0.294 ±0.024 0.434 ±0.063 0.647 ±0.131 0.254 ±0.001 336 0.549 ±0.017 0.396 ±0.014 0.620 ±0.046 0.436 ±0.081 0.290 ±0.016 0.473 ±0.014 0.656 ±0.113 0.292 ±0.000 720 0.604 ±0.017 0.396 ±0.010 0.694 ±0.055 0.469 ±0.005 0.307 ±0.014 1.097 ±0.084 ±0.010 0.443 ±0.015 0.566 ±0.032 0.421 ±0.019 0.385 ±0.014 0.498 ±0.033 0.467 ±0.072 0.294 ±0.001 336 0.517 ±0.012 0.440 ±0.012 0.550 ±0.024 0.443 ±0.039 0.383 ±0.009 0.517 ±0.008 0.469 ±0.070 0.320 ±0.000 720 0.556 ±0.009 0.442 ±0.006 0.584 ±0.027 0.459 ±0.004 0.396 ±0.012 0.782 ±0.041", "type_str": "table", "content": "<table><tr><td colspan=\"3\">Model Metrics H</td><td>ETTh1</td><td>ETTh2</td><td>ETTm1</td><td>ETTm2</td><td>Electricity</td><td>Exchange</td><td>Traffic</td><td>Weather</td><td>Overall Improvement</td></tr><tr><td>Temporal Attention</td><td>MSE MAE</td><td colspan=\"9\">96 0.496 -96 0.488 ±0.007 0.434 ±0.006 0.525 ±0.040 0.393 ±0.020 0.386 ±0.014 0.589 ±0.096 0.598 ±0.072 0.277 ±0.004 0.346 ±0.000 192 0.492 -0.356 ±0.000</td><td>18.09%</td></tr><tr><td colspan=\"5\">D. Additional Background</td><td/><td/><td/><td/><td/></tr></table>"}, "TABREF13": {"num": null, "html": null, "text": "Identity Attention represents our SAMformer with the attention weight matrix constrained to an identity matrix. We report in the last column the Overall improvement in MSE and MAE of SAMformer over the Identity Attention. This setup demonstrates that naively fixing the attention matrix to the identity does not enable to match the performance of SAM, despite the near-identity attention matrices SAM showcases (see Appendix B.5 for more details). In particular, we observe an overall improvement of 11.93% in MSE and 4.18% in MAE across all the datasets. ±0.074 0.374 ±0.031 0.384 ±0.042 0.248 ±0.016 0.189 ±0.022 0.320 ±0.070 0.437 ±0.041 0.236 ±0.002 336 0.512 ±0.070 0.372 ±0.024 0.408 ±0.032 0.303 ±0.022 0.211 ±0.019 0.443 ±0.071 0.500 ±0.155 0.277 ±0.003 720 0.505 ±0.107 0.405 ±0.012 0.466 ±0.043 0.397 ±0.029 0.233 ±0.019 1.123 ±0.076 0.468 ±0.021 0.338 ±0.009 MAE 96 0.473 ±0.041 0.395 ±0.033 0.376 ±0.019 0.294 ±0.027 0.283 ±0.023 0.320 ±0.023 0.301 ±0.039 0.259 ±0.021 4.18% 192 0.463 ±0.055 0.413 ±0.022 0.399 ±0.030 0.321 ±0.012 0.291 ±0.029 0.418 ±0.043 0.314 ±0.042 0.278 ±0.002 336 0.490 ±0.049 0.413 ±0.015 0.411 ±0.019 0.354 ±0.018 0.309 ±0.021 0.498 ±0.041 0.350 ±0.106 0.305 ±0.003 720 0.496 ±0.066 0.438 ±0.008 0.444 ±0.030 0.406 ±0.017 0.322 ±0.021 0.788 ±0.021 0.325 ±0.023 0.347 ±0.009", "type_str": "table", "content": "<table><tr><td colspan=\"3\">Model Metrics H</td><td>ETTh1</td><td>ETTh2</td><td>ETTm1</td><td>ETTm2</td><td>Electricity</td><td>Exchange</td><td>Traffic</td><td>Weather</td><td>Overall Improvement</td></tr><tr><td>Identity Attention</td><td>MSE</td><td colspan=\"9\">96 0.477 ±0.059 0.346 ±0.055 0.345 ±0.027 0.201 ±0.035 0.175 ±0.015 0.179 ±0.031 0.416 ±0.037 0.206 ±0.019 192 0.467</td><td>11.93%</td></tr></table>"}}}}