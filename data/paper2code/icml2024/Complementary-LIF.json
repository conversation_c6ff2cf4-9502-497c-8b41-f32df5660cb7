{"paper_id": "Complementary-LIF", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-24T23:34:38.517505Z"}, "title": "CLIF: Complementary Leaky Integrate-and-Fire Neuron for Spiking Neural Networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Hongwei", "middle": [], "last": "Ren", "suffix": "", "affiliation": {"laboratory": "", "institution": "The Hong Kong Univer-sity of Science and Technology (Guangzhou)", "location": {"settlement": "Guangzhou", "country": "China"}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "The Hong Kong Univer-sity of Science and Technology (Guangzhou)", "location": {"settlement": "Guangzhou", "country": "China"}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "The Hong Kong Univer-sity of Science and Technology (Guangzhou)", "location": {"settlement": "Guangzhou", "country": "China"}}, "email": ""}, {"first": "Zunch<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "The Hong Kong Univer-sity of Science and Technology (Guangzhou)", "location": {"settlement": "Guangzhou", "country": "China"}}, "email": ""}, {"first": "Biao", "middle": [], "last": "Pan", "suffix": "", "affiliation": {"laboratory": "", "institution": "Beihang University", "location": {"settlement": "Beijing", "country": "China"}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "The Hong Kong Univer-sity of Science and Technology (Guangzhou)", "location": {"settlement": "Guangzhou", "country": "China"}}, "email": "<<EMAIL>>."}], "year": "", "venue": null, "identifiers": {}, "abstract": "Spiking neural networks (SNNs) are promising brain-inspired energy-efficient models. Compared to conventional deep Artificial Neural Networks (ANNs), SNNs exhibit superior efficiency and capability to process temporal information. However, it remains a challenge to train SNNs due to their undifferentiable spiking mechanism. The surrogate gradients method is commonly used to train SNNs, but often comes with an accuracy disadvantage over ANNs counterpart. We link the degraded accuracy to the vanishing of gradient on the temporal dimension through the analytical and experimental study of the training process of Leaky Integrate-and-Fire (LIF) Neuron-based SNNs. Moreover, we propose the Complementary Leaky Integrate-and-Fire (CLIF) Neuron. CLIF creates extra paths to facilitate the backpropagation in computing temporal gradient while keeping binary output. CLIF is hyperparameter-free and features broad applicability. Extensive experiments on a variety of datasets demonstrate CLIF's clear performance advantage over other neuron models. Furthermore, the CLIF's performance even slightly surpasses superior ANNs with identical network structure and training conditions. The code is available at https://github.com/HuuYuLong/Complementary-LIF.", "pdf_parse": {"paper_id": "Complementary-LIF", "_pdf_hash": "", "abstract": [{"text": "Spiking neural networks (SNNs) are promising brain-inspired energy-efficient models. Compared to conventional deep Artificial Neural Networks (ANNs), SNNs exhibit superior efficiency and capability to process temporal information. However, it remains a challenge to train SNNs due to their undifferentiable spiking mechanism. The surrogate gradients method is commonly used to train SNNs, but often comes with an accuracy disadvantage over ANNs counterpart. We link the degraded accuracy to the vanishing of gradient on the temporal dimension through the analytical and experimental study of the training process of Leaky Integrate-and-Fire (LIF) Neuron-based SNNs. Moreover, we propose the Complementary Leaky Integrate-and-Fire (CLIF) Neuron. CLIF creates extra paths to facilitate the backpropagation in computing temporal gradient while keeping binary output. CLIF is hyperparameter-free and features broad applicability. Extensive experiments on a variety of datasets demonstrate CLIF's clear performance advantage over other neuron models. Furthermore, the CLIF's performance even slightly surpasses superior ANNs with identical network structure and training conditions. The code is available at https://github.com/HuuYuLong/Complementary-LIF.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Spiking Neural Networks (SNNs) (<PERSON><PERSON>, 1997) have captivated the attention of both academic and industrial communities in recent years (<PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2019; Mehonic & Kenyon, 2022; <PERSON><PERSON> et al., 2022) . Drawing inspiration from the biological neuron, SNNs adopt the spiking neuron, like the leaky integrate and fire (LIF) model, utilizing spike-based communication for information transmission (<PERSON><PERSON> et al., 2018) . This fundamental characteristic equips SNNs with the capacity to effectively process information across both temporal and spatial dimensions, excelling in areas of low latency and low power consumption (<PERSON><PERSON> et al., 2022) . Compared to conventional deep Artificial Neural Networks (ANNs), SNNs exhibit superior efficiency and capability to process temporal information, presenting significant implementation potential in edge device applications for real-time applications (<PERSON> et al., 2019; Mehonic & Kenyon, 2022; <PERSON> et al., 2024) .", "cite_spans": [{"start": 31, "end": 44, "text": "(<PERSON><PERSON>, 1997)", "ref_id": "BIBREF31"}, {"start": 135, "end": 158, "text": "(<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF47"}, {"start": 159, "end": 176, "text": "<PERSON> et al., 2019;", "ref_id": "BIBREF41"}, {"start": 177, "end": 200, "text": "Mehonic & Kenyon, 2022;", "ref_id": "BIBREF33"}, {"start": 201, "end": 222, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF43"}, {"start": 416, "end": 437, "text": "(<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF48"}, {"start": 642, "end": 664, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF43"}, {"start": 916, "end": 934, "text": "(<PERSON> et al., 2019;", "ref_id": "BIBREF41"}, {"start": 935, "end": 958, "text": "Mehonic & Kenyon, 2022;", "ref_id": "BIBREF33"}, {"start": 959, "end": 977, "text": "<PERSON> et al., 2024)", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Despite the advantages of SNNs, the training of SNNs presents a substantial challenge due to the inherently undifferentiable spiking mechanism. Many scholars have intensively explored this problem, three mainstream training methods have been proposed: the bio-inspired training method (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018) , the ANN-to-SNN conversion method (<PERSON><PERSON> <PERSON>, 2020) and the surrogate gradient (SG) method (<PERSON> et al., 2021a; <PERSON> et al., 2022b; <PERSON> et al., 2023) . The bio-inspired training method bypasses undifferentiable problems by calculating the gradients with respect to the spike time (<PERSON> et al., 2018; <PERSON> et al., 2023) . The ANN-to-SNN method utilizes pre-trained ANN models to approximate the ReLU function with the spike neuron model (<PERSON> et al., 2021a; <PERSON> et al., 2023) . The SG method uses surrogate gradients to approximate the gradients of non-differentiable spike functions during backpropagation (<PERSON><PERSON><PERSON><PERSON> et al., 2019) . This method solves the problem of non-differentiable spike functions, facilitating the direct trainable ability of SNNs (<PERSON> et al., 2023) .", "cite_spans": [{"start": 285, "end": 312, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF21"}, {"start": 348, "end": 365, "text": "(<PERSON>g & Gu, 2020)", "ref_id": "BIBREF8"}, {"start": 405, "end": 423, "text": "(<PERSON> et al., 2021a;", "ref_id": null}, {"start": 424, "end": 443, "text": "<PERSON> et al., 2022b;", "ref_id": null}, {"start": 444, "end": 463, "text": "<PERSON> et al., 2023)", "ref_id": null}, {"start": 594, "end": 614, "text": "(<PERSON> et al., 2018;", "ref_id": "BIBREF63"}, {"start": 615, "end": 633, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF12"}, {"start": 751, "end": 769, "text": "(<PERSON> et al., 2021a;", "ref_id": null}, {"start": 770, "end": 789, "text": "<PERSON> et al., 2023)", "ref_id": null}, {"start": 921, "end": 942, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF37"}, {"start": 1065, "end": 1082, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF56"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Each method is attractive in certain aspects but also process certain limitations. SG and ANN-to-SNN methods provide great applicability across various neural network architectures, such as spike-driven MLP (<PERSON> et al., 2022) , SRNN (<PERSON> & Li, 2021) , SCNN (<PERSON> et al., 2021a) and Transformer backbone (<PERSON> et al., 2022; <PERSON> et al., 2024) . In contrast, bio-inspired training is challenging to be effectively applied to deeper network configurations (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018) . SG can reach satisfactory performance within limited timestep, whereas ANN-to-SNN requires a large number of timestep and more spikes to achieve comparable accuracy to the network trained by the SG method (<PERSON><PERSON> et al., 2020) . As such, SG-based SNNs are more attractive in edge scenarios where the inference power is critical (<PERSON> et al., 2019) . Nevertheless, the SG method necessitates the use of inaccurate approximations for computing the gradients, leading to imprecise gradient update values and thus diminishing accuracy (<PERSON> et al., 2023) .", "cite_spans": [{"start": 207, "end": 224, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF27"}, {"start": 232, "end": 250, "text": "(<PERSON> & <PERSON>, 2021)", "ref_id": null}, {"start": 258, "end": 278, "text": "(<PERSON> et al., 2021a)", "ref_id": null}, {"start": 304, "end": 323, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF65"}, {"start": 324, "end": 341, "text": "<PERSON> et al., 2024)", "ref_id": "BIBREF59"}, {"start": 453, "end": 480, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF21"}, {"start": 688, "end": 707, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": null}, {"start": 809, "end": 827, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF41"}, {"start": 1011, "end": 1030, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF51"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In this study, we rethink the SNN training process and introduce complementary leaky integrate and fire (CLIF) neuron. The LIF and CLIF neuron model is illustrated in Figure.1(a) and (b). We introduce a complementary membrane potential (m[t] ) in CLIF neuron. The complementary potential captures and maintains information related to the decay of the membrane potential. CLIF creates extra paths to facilitate the data flow in temporal gradient computation, as intuitively seen in Figure .1 (c) and (d). Our experiments demonstrate that for SNNs with vanilla LIF neurons, employing a limited number of temporal gradients can yield comparable accuracy to those achieved by using gradients across much more temporal steps. Our theoretical analysis reveals such limitation is linked to the vanishing of certain temporal gradients. Experiments show CLIF can boost the SNN performance significantly in both static images and dynamic event streams. Impressively, even with moderate timestep to keep SNN's low power advantage, CLIF-based SNN achieves comparable or even superior performance to ANN counterpart with identical network structure and training conditions. Our main contributions are: ", "cite_spans": [{"start": 167, "end": 178, "text": "Figure.1(a)", "ref_id": null}, {"start": 236, "end": 241, "text": "(m[t]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In SG method, gradients of non-differentiable spike functions are approximated by some surrogate gradients during backpropagation, this method enables SNNs to be trained directly by BPTT (<PERSON><PERSON><PERSON>, 1990) . However, the inaccurate approximations for computing the gradients cause imprecise gradient update (<PERSON> et al., 2023) , and degradation in accuracy. Moreover, as the BPTT method requires iteration of recursive computation over timestep, the training cost grows substantially over large timestep (<PERSON> et al., 2018) .", "cite_spans": [{"start": 187, "end": 201, "text": "(<PERSON><PERSON><PERSON>, 1990)", "ref_id": "BIBREF52"}, {"start": 303, "end": 322, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF51"}, {"start": 500, "end": 517, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF53"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "To improve the accuracy of the SG method, many efforts have been made. Some studies have advanced the surrogate functions. ASGL method (<PERSON> et al., 2023) introduced an adaptive smoothing gradient to reduce gradient noise.", "cite_spans": [{"start": 135, "end": 154, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF51"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "LocalZO (<PERSON><PERSON><PERSON><PERSON> et al., 2023) proposed the zeroth-order method to estimate the gradients for neuron. SML method (<PERSON><PERSON> et al., 2023) introduces ANNs module to reduce the gradient noise accumulation when training. Alternatively, enhanced neuron dynamics could also yield in higher SNNs accuracy. For example, PLIF (<PERSON> et al., 2021b) , LTMD (<PERSON> et al., 2022a) and GLIF (<PERSON> et al., 2022) introduced learnability in membrane potential, neuron threshold, and different channels, respectively. Nevertheless, even with those efforts, there is still a performance gap between SNNs and ANNs when implemented with identical network architecture. To enhance the training efficiency, several efficient training methods have been proposed. For instance, e-prop (<PERSON><PERSON> et al., 2020) entirely discards the temporal gradients, and only uses the gradients of spatial dimension for training. SLTT (<PERSON><PERSON> et al., 2023 ) also discards the gradient of the temporal dimension, but randomly chooses a few gradient paths along the spatial dimension. Surprisingly, even after discarding the gradients in the temporal dimension, these methods still obtain comparable performance to the original BPTT approach. We investigate further such counterintuitive phenomena through experiments and conclude the temporal gradient decays too rapidly over multiple timesteps. Details about this observation are given in methods.", "cite_spans": [{"start": 8, "end": 30, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF36"}, {"start": 113, "end": 132, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF10"}, {"start": 313, "end": 333, "text": "(<PERSON> et al., 2021b)", "ref_id": null}, {"start": 341, "end": 361, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 371, "end": 389, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF60"}, {"start": 753, "end": 774, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF1"}, {"start": 885, "end": 903, "text": "(<PERSON><PERSON> et al., 2023", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "To tackle the rapid temporal gradient decay in SNNs, (<PERSON><PERSON> Rezaabad & Vish<PERSON>, 2020) and (<PERSON> et al., 2024) proposed spiking LSTM and spiking ConvLSTM in respectively. Spiking (Conv)LSTM inherits LSTM's advantage and avoids rapid temporal gradient decay. However, Spiking (Conv)LSTM comes with a significant number of training parameters compared to LIF within each neuron, complicating the network structuring and increasing training effort. Moreover, Spiking (Conv)LSTM restricts the neuron from the critical operation of decay and reset. (<PERSON><PERSON><PERSON><PERSON> et al., 2022a; 2023) proposed spikeGRU preserves the reset process of spike neuron. The SpikeGRU also inherits the gating mechanism of GRU to avoid fast temporal gradient decay, and still keep the number of training parameters. (<PERSON> et al., 2024) increased the parallel connection with trainable parameters between the spiking neurons to learn the long-term dependencies. However, this method also restricts the neuron from reset operation and increases the computation complexity. As such, both methods lose the generosity of SNNs and dilute the high efficiency and low power consumption advantage of SNNs.", "cite_spans": [{"start": 53, "end": 88, "text": "(<PERSON><PERSON> & Vishwanath, 2020)", "ref_id": "BIBREF30"}, {"start": 93, "end": 110, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF57"}, {"start": 544, "end": 571, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022a;", "ref_id": null}, {"start": 572, "end": 577, "text": "2023)", "ref_id": null}, {"start": 785, "end": 804, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "In parallel, several bio-inspired models have been developed, transitioning from biological concepts to neuronal model implementations, with the goal of addressing longterm dependency learning issues. For example, the AHP neuron (<PERSON> et al., 2022) inspired by after-hyperpolarizing currents, the TC-LIF model (<PERSON> et al., 2024) inspired by the <PERSON><PERSON>sky-<PERSON><PERSON>zel pyramidal neuron and the ELM model (<PERSON><PERSON><PERSON> et al., 2023) inspired by the cortical neuron. However, few works demonstrate the potential to apply bioinspired neuron models on large and complex networks. In summary, the methods to improve the temporal gradients not only add significant training complexity but also cannot be generalized to various network backbones.", "cite_spans": [{"start": 229, "end": 247, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF39"}, {"start": 309, "end": 329, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF62"}, {"start": 396, "end": 418, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF45"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "The specific notations used in this paper are described in the Appendix.A.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminary", "sec_num": "3."}, {"text": "In the field of SNNs, the most common neuron model is the Leaky Integrate-and-Fire (LIF) model with iterative expression, as detailed in (<PERSON> et al., 2018) . At each time step t, the neurons in the l-th layer integrate the postsynaptic current c l [t] with their previous membrane potential u l [t -1], the mathematical expression is illustrated in Eq.(1):", "cite_spans": [{"start": 137, "end": 154, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF53"}], "ref_spans": [], "eq_spans": [], "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "u l [t] = (1 - 1 τ )u l [t -1] + c l [t],", "eq_num": "(1)"}], "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "where τ is the membrane time constant. τ > 1 as the discrete step size is 1. The postsynaptic current c l [t] = W l * s l-1 [t] is calculated as the product of weights W l and spikes from the preceding layer s l-1 [t], simulating synaptic functionality, with * indicating either a fully connect or convolution's synaptic operation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "Neurons will generate spikes s l [t] by Heaviside function when membrane potential u l [t] exceeds the threshold V th , as shown in Eq.(2):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "s l [t] = Θ(u l [t] -V th ) = 1, if u l [t] ≥ V th 0, otherwise .", "eq_num": "(2)"}], "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "After the spike, the neuron will reset its membrane potential. Two ways are prominent in Eq.(3):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "u l [t] = u l [t] -V th s l [t], soft reset u l [t] ⊙ 1 -s l [t] , hard reset .", "eq_num": "(3)"}], "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "In this work, we chose the soft reset process because it will keep more temporal information (<PERSON><PERSON> et al., 2023) .", "cite_spans": [{"start": 93, "end": 112, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "SNN Neuron Model", "sec_num": "3.1."}, {"text": "In the SG method, gradients are computed through BPTT (<PERSON> et al., 2018) . This involves considering the temporal dimension, where the gradients at l-th layer for all timestep T are calculated as Eq.( 4):", "cite_spans": [{"start": 54, "end": 71, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF53"}], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∇ W l L = T t=1 ∂L ∂u l [t] ∂u l [t] ∂W l , l = L, L -1, • • • , 1,", "eq_num": "(4)"}], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "where L represents the loss function. We define the ∂L", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "∂u l [t]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "as the gradient error in this paper, the gradient error can be evaluated recursively:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂u l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + T t ′ =t+1 ∂L ∂s l [t ′ ] ∂s l [t ′ ] ∂u l [t ′ ] t ′ -t t ′′ =1 ϵ l [t ′ -t ′′ ],", "eq_num": "(5)"}], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "where the ϵ l [t] for LIF model can be defined as follows in Eq.( 6):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ϵ l [t] ≡ ∂u l [t + 1] ∂u l [t] + ∂u l [t + 1] ∂s l [t] ∂s l [t] ∂u l [t] .", "eq_num": "(6)"}], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "In particular, for different layers, we have Eq.( 7):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂s l [t] = ∂L ∂s l [t] if l = L ∂L ∂u l+1 [t] ∂u l+1 [t] ∂s l [t] if l = L -1, • • • , 1 ,", "eq_num": "(7)"}], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "where the ∂u l+1 [t]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "∂s l [t]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "= (W l+1 ) ⊤ . The detailed derivations can be found in the Appendix.B. In addition, the non-differentiable problem is solved by approximating", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "∂s l [t] ∂u l [t] ≈ H u l [t]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "with the surrogate function H(•) (<PERSON><PERSON><PERSON><PERSON> et al., 2019) . In this work, we chose the rectangle function (<PERSON> et al., 2019; <PERSON> et al., 2023) :", "cite_spans": [{"start": 33, "end": 54, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF37"}, {"start": 103, "end": 120, "text": "(<PERSON> et al., 2019;", "ref_id": "BIBREF54"}, {"start": 121, "end": 137, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF46"}], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂s l [t] ∂u l [t] ≈ H u l [t] = 1 α 1 u l [t] -V th < α 2 ,", "eq_num": "(8)"}], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "where 1(•) served as the indicator function. Following (<PERSON><PERSON> et al., 2023) , the hyperparameter α is set to V th . In this case, Eq.( 6) can be rewritten as:", "cite_spans": [{"start": 55, "end": 74, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ϵ l [t] = γ 1 -V th H u l [t]", "eq_num": "(9)"}], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "where γ ≜ 1 -1 τ , resulting γ ∈ (0, 1). Theoretical Analysis: Figure.2 reveals LIF's limitation of exploiting temporal information over a long period. This phenomenon is further investigated analytically. We separate the gradients into spatial", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SNN Training with Sur<PERSON> G<PERSON>ient", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "P l [t] component and temporal component T l [t], as shown in ∂L ∂u l [t] = P l [t] + T t ′ =t+1 T l [t, t ′ ],", "eq_num": "(10)"}], "section": "Method", "sec_num": "4."}, {"text": "where P l [t] and T l [t, t ′ ] can be further expanded as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "P l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] ,", "eq_num": "(11)"}], "section": "Method", "sec_num": "4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T l [t, t ′ ] = ∂L ∂s l [t ′ ] ∂s l [t ′ ] ∂u l [t ′ ] t ′ -t t ′′ =1 ϵ l [t ′ -t ′′ ],", "eq_num": "(12)"}], "section": "Method", "sec_num": "4."}, {"text": "where the t ′ ∈ [t+1, T ]. By substituting Eq.( 9) into Eq.( 12), we obtain:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂s l [t ′ ] ∂s l [t ′ ] ∂u l [t ′ ] γ (t ′ -t) Part I t ′ -t t ′′ =1 1 -V th H u l [t ′ -t ′′ ] Part II . (", "eq_num": "13"}], "section": "Method", "sec_num": "4."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "4."}, {"text": "The temporal gradient error is a production of two parts. For Part I, When the difference between t ′ and t is substantial, Part I gets very close to zero. The γ is defined as 1-1 τ , t denotes the time constant ∈ [1, + inf). Typically, γ is between 0.09 and 0.5. For example, in (<PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2021; <PERSON> et al., 2022) they set τ = 1.1, 1.3, 1.5, 2.0. For large t ′ -t, γ (t ′ -t) could barely contribute to the ϵ. This could explain our observation in Figure.2(a) .", "cite_spans": [{"start": 280, "end": 299, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF35"}, {"start": 300, "end": 318, "text": "<PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF9"}, {"start": 319, "end": 337, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF55"}, {"start": 472, "end": 483, "text": "Figure.2(a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "4."}, {"text": "Part II can be expressed as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "1 -V th H u l [t] = 0 if 1 2 V th < u l [t] < 3 2 V th 1 otherwise . (", "eq_num": "14"}], "section": "Method", "sec_num": "4."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "4."}, {"text": "More in-depth discussions and proofs of this equation are available in the Appendix.D. From Eq.( 14), it can be seen part II has binary values of 0 or 1. More specifically, as long as the neuron fires at least once within the temporal range of (t + 1, T ), part II = 0 and the corresponding temporal gradient error will vanish and cannot contribute to the backpropagation training process. This is an unavoidable issue in vanilla-LIF models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "4."}, {"text": "The vanishing of part II is also demonstrated experimentally. For example, in the special case with timestep T = 2, part I equal to γ we could examine the influence of part II and the experiment results are given in Section.5.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "4."}, {"text": "To summarize, Eq.( 12) demonstrates the temporal gradient vanishing due to the vanishing ϵ in two folds: the multiplication of gamma at large t ′ -t and the neuron spike between t ′ and t. We define this as the temporal gradient vanishing problem persists with the vanilla-LIF model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "4."}, {"text": "To address the temporal gradient errors vanishing problem, we design the Complementary LIF (CLIF) model in-spired by biological principles (See detailed in Appendix.E).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "Besides membrane potential, we introduce another state, termed \"Complementary potential\". To maintain the efficiency advantage of SNN as well as the broad applicability of our neuron model, our model contains no learnable parameters.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "Decay of Complementary membrane potential: Between each timestep, the membrane potential is decayed by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "1 τ u l [t]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": ". We design our Complementary potential to compensate for the membrane potential decay as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "m l [t] = m l [t -1] ⊙ σ 1 τ u l [t] .", "eq_num": "(15)"}], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "We choose the Sigmoid function as σ, As σ ∈ (0, 1) and the Complementary potential also decays. Nevertheless, the more the membrane potential decays, the less the Complementary potential decays. This design aims to preserve the decayed portion of the membrane potential into Complementary membrane potential.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "Increase of Complementary membrane potential: Within each timestep, the Complementary membrane potential is increased by firing", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "m l [t] = m l [t] + s l [t].", "eq_num": "(16)"}], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "If the neuron has fired recently, the membrane potential m l [t] gets larger.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "Redesign Reset process: We revisit the Vanilla LIF model as defined by equation Eq.( 1)-( 3), focusing particularly on LIF's reset process:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "u l [t] = u l [t] -s l [t] ⊙ V th .", "eq_num": "(17)"}], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "The redesigned reset process is given in Eq.( 18). Compared to the soft reset in Eq.( 17), each time the neuron fires, the membrane potential is subtracted by another term σ(m l [t]) related to the Complementary potential: ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "u l [t] = u l [t] -s l [t] ⊙ V th + σ(m l [t]) . (", "eq_num": "18"}], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "if u = (1 -1 τ )u pre + c ▷ leaky & integrate s = Θ(u -V th ) ▷ fire m = m pre ⊙ σ 1 τ u + s u pre = u -s ⊙ (V th + σ(m)) ▷ reset m pre = m Return s ▷ spike output", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "Summarizing the above principles, the CLIF model can be derived as following:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "                 u l [t] = (1 - 1 τ )u l [t -1] + c l [t], s l [t] = Θ(u l [t] -V th ), m l [t] = m l [t -1] ⊙ σ 1 τ u l [t] + s l [t], u l [t] = u l [t] -s l [t] ⊙ V th + σ(m l [t]) .", "eq_num": "(19)"}], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "The pseudo-code for the CLIF model is shown in Algorithm.1. The simplicity of CLIF is reflected in the fact that we only add two lines of code to LIF neuron model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The Design of Complementary LIF Model", "sec_num": "4.2."}, {"text": "To validate the effectiveness of the CLIF model, we examine the CLIF model through both case studies and theoretical analysis.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "In the case study, we explore the dynamic properties of both LIF and CLIF models. CLIF features spike frequency adaptation and exhibits a lower firing rate compared to the LIF neuron. This phenomenon is similar to the refractory period or hyperpolarization in the biological neuron (Sanchez-V<PERSON> & McCormick, 2000) . More specifically, when the input spikes get dense, the complementary potential gets high, the reset process gets more substantial, as shown in Eq.( 18). The more detailed dynamic analysis are illustrated in the Appendix.F.", "cite_spans": [{"start": 282, "end": 315, "text": "(Sanchez-Vives & McCormick, 2000)", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "In the theoretical Analysis, we separate the gradient error into spatial and temporal components in Eq.( 20). The details of this derivation are given in Appendix.G). This separation demonstrates that CLIF not only contains all temporal gradients in LIF but also contains extra temporal gradient terms. We believe these additional temporal terms contribute to the improved performance of CLIF.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂u l [t] = P l M [t]+ T t ′ =t+1 T l M1 [t, t ′ ] + T t ′ =t+1 T l M2 [t, t ′ ] ψ l [t],", "eq_num": "(20)"}], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "where P l M [t] and T l M [t] presents the Spatial and Temporal parts of CLIF's Gradient Errors in respectively. Meanwhile, the M1 and M2 indicate that the Temporal term is divided into two parts.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "Firstly, the spatial term of CLIF's gradient errors P l M [t] in Eq.( 20) is identical to the counterpart in LIF neuron in Eq.( 11). The detailed derivation and proof are given in Appendix.G.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "Secondly, for the temporal term of CLIF's gradient errors, the first temporal part T l M1 [t, t ′ ] expands as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T l M1 = ∂L ∂s l [t ′ ] ∂s l [t ′ ] ∂u l [t ′ ] + ∂L ∂m l [t ′ ] ψ l [t ′ ] t ′ -t t ′′ =1 ξ l [t ′ -t ′′ ],", "eq_num": "(21)"}], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "where the ξ l [t] is defined as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ξ l [t] =ϵ l [t] + ∂u l [t + 1] ∂m l [t] ψ l [t],", "eq_num": "(22)"}], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "this term can be simplified to a product involving the constant term γ, the same as Eq.( 13). The issue discussed in Part I of Section.4.1, regarding the vanishing of temporal gradients, also applies here. Where ψ l [t] is non-negative (see Appendix.G). ψ l [t] is defined as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ψ l [t] ≡ ∂m l [t] ∂u l [t] + ∂m l [t] ∂s l [t] ∂s l [t] ∂u l [t] .", "eq_num": "(23)"}], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "Finally, for the other temporal item of CLIF's gradient errors, T l M2 [t, t ′ ], can be expressed as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T l M2 [t, t ′ ] = ∂L ∂u l [t ′ ] ∂u l [t ′ ] ∂m l [t ′ -1] t ′ -t t ′′ =2 ρ l [t ′ -t ′′ ].", "eq_num": "(24)"}], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "This term indicates that additional Temporal Gradient Errors components are generated. We believe this additional part contributes to the performance improvements. As T l M2 does not decay as fast as ξ over timestep, this phenomenon could be observed in dynamic analysis in the Appendix.F. Therefore, this item of gradient errors contributes to compensate for the vanishing of temporal gradients in LIF, leading to a further reduction in the loss. This assertion is also verifiable in Figure .3(a) in the Experiment. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dynamic and Theoretical Analysis", "sec_num": "4.3."}, {"text": "To validate the effectiveness of the proposed CLIF neuron, we conduct a set of ablation studies. These studies are designed to evaluate the underlying principles of the CLIF model, to examine the effect of various timestep, and to conduct comparative analyses between the CLIF model and other neuron models. Following the ablation study, we extend our experiments to cover a diverse range of data types, including static image datasets and neuromorphic eventbased datasets. Details on the experimental implementation are provided in the Appendix.I.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment", "sec_num": "5."}, {"text": "We conduct two experiments to compare LIF and CLIF: the loss of CLIF versus LIF via training epochs, and the accuracy of CLIF versus LIF via timestep. For a fair comparison, except for the control variable, the same optimizer setting, random seed, architecture, loss function, weight initialization and all hyperparameters are employed. We extend the loss comparison to various tasks and network backbones (see Appendix.H). In all experiments, CLIF neuron's loss converges faster than LIF's, the converged loss is also lower. As such, one can conclude that CLIF neurons are more effective in capturing error information both precisely and efficiently, suggesting the higher training accuracy and efficiency of CLIF. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation and Analysis", "sec_num": "5.1."}, {"text": "We conduct two sets of comparison experiments to ascertain the effectiveness of CLIF: comparison with Different Neurons, and comparison with SOTA methods.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Comparison and Quantitative analysis", "sec_num": "5.2."}, {"text": "In order to verify whether CLIF is more effective than existing methods, we self-implement and compare vanilla-LIF (<PERSON> et al., 2018) , PLIF (<PERSON> et al., 2021b) , KLIF (Jiang & Zhang, 2023) and GLIF (<PERSON> et al., 2022) . Except for the neuron models, all other experimental conditions are kept identical, including the backbone architecture, random seeds and hyperparameters. CLIF exhibits superior performance over other neuron benchmarks in the CIFAR10 and CIFAR100 datasets, as shown in Figure .4(a) and (b). PLIF and GLIF include additional training parameters, so additional hyperparameters tuning and more training epochs are required to converge.", "cite_spans": [{"start": 115, "end": 132, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF53"}, {"start": 140, "end": 160, "text": "(<PERSON> et al., 2021b)", "ref_id": null}, {"start": 168, "end": 189, "text": "(<PERSON> & Zhang, 2023)", "ref_id": "BIBREF19"}, {"start": 199, "end": 217, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF60"}], "ref_spans": [], "eq_spans": [], "section": "Comparison with <PERSON> Neuron", "sec_num": null}, {"text": "Moreover, CLIF can achieve slightly better performance with ReLU-based ANNs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Comparison with <PERSON> Neuron", "sec_num": null}, {"text": "Comparison with SOTA methods We compare our approach with state-of-the-art methods in two categories of datasets: static dataset (CIFAR10/100 and Tiny-ImageNet), as summarized in Table .1 and neuromorphic dataset (DVS-Gesture and CIFAR10-DVS), as summarized in Table .2. We not only explore the diversity of datasets but also the diversity in network backbone, including ResNet, VGG and Transformer. We also compare the fire rate and energy consumption of LIF, CLIF and ReLU. In short, CLIF has lower fire rate and similar energy consumption as LIF. Detailed statistics of fire rate and power consumption are described in the Appendix.J. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Comparison with <PERSON> Neuron", "sec_num": null}, {"text": "In ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Throughout the paper and this Appendix, we use the following notations, which mainly follow this work (<PERSON> et al., 2023) . We follow the conventions representing vectors and matrices with bold italic letters and bold capital letters respectively, such as s and W . For this symbol W ⊤ represents transposing the matrix. For a function f (x) : R d1 → R d2 , we use ∇ x f instead of ∂f ∂x to represent the 1 th derivative gradients of f with respect to the variable x in the absence of ambiguity. For two vectors u 1 and u 2 , we use u 1 ⊙ u 2 to represent the element-wise product.", "cite_spans": [{"start": 102, "end": 121, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF51"}], "ref_spans": [], "eq_spans": [], "section": "A. Notation in the Paper", "sec_num": null}, {"text": "This section is mainly referenced from (<PERSON> et al., 2018; <PERSON><PERSON> et al., 2023) . Firstly, we recall the LIF model Eq.( 1) -( 3). We then rewrite the LIF model with soft reset mechanism:", "cite_spans": [{"start": 39, "end": 56, "text": "(<PERSON> et al., 2018;", "ref_id": "BIBREF53"}, {"start": 57, "end": 75, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "u l [t] = (1 - 1 τ ) u l [t -1] -V th s l [t -1] + W l s l-1 [t],", "eq_num": "(25)"}], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "s l [t] = Θ(u l [t] -V th ) = 1, if u l [t] ≥ V th 0, otherwise", "eq_num": "(26)"}], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "γ is defined as γ ≡ 1 -1 τ , then we recall the gradient in Eq.( 4)-( 6):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∇ W l L = T t=1 ∂L ∂u l [t] ∂u l [t] ∂W l , l = L, L -1, • • • , 1,", "eq_num": "(27)"}], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "where L represents the loss function. For the left part we recursively evaluate:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂u l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂u l [t + 1] ϵ l [t],", "eq_num": "(28)"}], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "where ϵ l [t] for LIF model can be defined as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ϵ l [t] ≡ ∂u l [t + 1] ∂u l [t] + ∂u l [t + 1] ∂s l [t] ∂s l [t] ∂u l [t] ,", "eq_num": "(29)"}], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "Proof of the Eq.( 5) and Eq.( 7).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "Proof. Firstly, we only consider the effect of the temporal dimension in Eq.( 28). When t = T , where Eq.( 28) deduce as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂u l [T ] = ∂L ∂s l [T ] ∂s l [T ] ∂u l [T ] . (", "eq_num": "30"}], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "When 1 ≤ t < T , with the chain rule, the Eq.( 28) can be further calculated recursively:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "∂L ∂u l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂u l [t + 1] ϵ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] + ∂L ∂u l [t + 2] ϵ l [t + 1] expansion ϵ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] ϵ l [t] + ∂L ∂u l [t + 2] ϵ l [t + 1]ϵ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] ϵ l [t] + ∂L ∂s l [t + 2] ∂s l [t + 2] ∂u l [t + 2] + ∂L ∂u l [t + 3] ϵ l [t + 2] expansion ϵ l [t + 1]ϵ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] ϵ l [t] + ∂L ∂s l [t + 2] ∂s l [t + 2] ∂u l [t + 2] ϵ l [t + 1]ϵ l [t] + ∂L ∂u l [t + 3] ϵ l [t + 2]ϵ l [t + 1]ϵ l [t] =...... = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] ϵ l [t] + ∂L ∂s l [t + 2] ∂s l [t + 2] ∂u l [t + 2] ϵ l [t + 1]ϵ l [t] + ... + ∂L ∂s l [T ] ∂s l [T ] ∂u l [T ] ϵ l [T -1]ϵ l [T -2]...ϵ l [t]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": ",", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "(31) after iterative expansion, we can inductively summarize the Eq.( 30) and ( 31) to obtain this formula Eq.( 32):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂u l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + T t ′ =t+1 ∂L ∂s l [t ′ ] ∂s l [t ′ ] ∂u l [t ′ ] t ′ -t t ′′ =1 ϵ l [t ′ -t ′′ ]. (", "eq_num": "32"}], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "Secondly, under the t ∈ [1, T ] situation, we consider the different layer. For last layer, we substitute l = L into Eq.( 32) as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂u L [t] = ∂L ∂s L [t] ∂s L [t] ∂u L [t] + T t ′ =t+1 ∂L ∂s L [t ′ ] ∂s L [t ′ ] ∂u L [t ′ ] t ′ -t t ′′ =1 ϵ L [t ′ -t ′′ ]. (", "eq_num": "33"}], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "For the intermediate layer l = L -1, ..., 1, according to the chain rule, the ∂L ∂u l [t] can be obtained from the previous layer ∂L ∂u l+1 [t] , the Eq.( 32) can be shown in:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂u l [t] = ∂L ∂u l+1 [t] ∂u l+1 [t] ∂s l [t] ∂s l [t] ∂u l [t] + T t ′ =t+1 ∂L ∂u l+1 [t ′ ] ∂u l+1 [t ′ ] ∂s l [t ′ ] ∂s l [t ′ ] ∂u l [t ′ ] t ′ -t t ′′ =1 ϵ l [t ′ -t ′′ ].", "eq_num": "(34)"}], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "Finally, combining Eq.( 32)-( 34), we conclude the following equations:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂u l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + T t ′ =t+1 ∂L ∂s l [t ′ ] ∂s l [t ′] ] ∂u l [t ′] ] t ′ -t t ′′ =1 ϵ l [t ′ -t ′′ ],", "eq_num": "(35)"}], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂s l [t] = ∂L ∂s L [t] if l = L ∂L ∂u l+1 [t] ∂u l+1 [t] ∂s l [t] if l = L -1, • • • , 1 ,", "eq_num": "(36)"}], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "The Eq.( 35) and Eq.( 36) is the same as Eq.( 5) and Eq.( 7).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. LIF-Based BPTT with Surrogate Gradient", "sec_num": null}, {"text": "The network accuracy is influenced by the time constant (τ ) and BPTT timestep (k). ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. The Details of Experimental Observation", "sec_num": null}, {"text": "= 𝓟 𝒕 + 𝓣 𝒕+𝟏 … … … … Loss Function 𝓛 … … … … … … … … 𝑲 = 𝟐", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. The Details of Experimental Observation", "sec_num": null}, {"text": "Eq.( 9) can be substituted into formula Eq.( 12) yields:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T l [t, t ′ ] = ∂L ∂s l [t ′ ] ∂s l [t ′ ] ∂u l [t ′ ] γ (t ′ -t) Part I t ′ -t t ′′ =1 1 -V th H u l [t ′ -t ′′ ] Part II ,", "eq_num": "(37)"}], "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "In this case, the ϵ still converges to 0 after continuous multiplication. The detailed proof is shown below.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "We construct the function", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "f ϵ (n) = n t=1 ϵ l [t], n = 1, 2, . . ., to proof lim n→+∞ f ϵ (n) = 0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "Proof.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "lim n→+∞ f ϵ (n) = lim n→+∞ n t=1 ϵ l [t] = lim n→+∞ n t=1 γ 1 -s l [t] ⊙ H u l [t] = lim n→+∞ γ n n t=1 1 -s l [t] ⊙ H u l [t] = 0", "eq_num": "(38)"}], "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "where the γ is a constant and 0 < γ < 1, resulting lim n→+∞ γ n = 0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "1 -= 1 -V th α , if V th -α 2 ≤ u l [t] ≤ V th + α 2 1, otherwise .", "eq_num": "(39)"}], "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "In Part II, we further deduce as shown in Eq.( 39), drawing from Eq.( 6). The second term also tends toward zero, influenced by the hyperparameter α. To prevent spatial gradient explosion, α is typically set to be larger than or equal to V th (<PERSON> et al., 2019) . When α > V th , the result is 0 < 1 -V th α < 1, which causes the temporal gradients to converge to zero more quickly due to the continuous product. However, many studies retain the default value of α = V th = 1 (<PERSON><PERSON> et al., 2021) . When α = V th , if the membrane potential is within the range of (V th -α 2 , V th + α 2 ), then 1 -V th α equals. In other words, if a spike is generated (or u ≈ V th ) once within the range of (t + 1, T ), the temporal gradient will be 0 in such cases. This signifies a pervasive challenge with temporal gradients in the vanilla LIF model, persisting even with short timestep.", "cite_spans": [{"start": 243, "end": 260, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF54"}, {"start": 475, "end": 494, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Discussion on Temporal Gradient Errors", "sec_num": null}, {"text": "The design inspiration for CLIF neurons primarily comes from the adaptive learning characteristics observed in the biological nervous system, particularly the mechanisms of neural adaptability and dynamic regulation of membrane potential (<PERSON><PERSON> & <PERSON>, 2003) . In biology, neurons adjust their electrophysiological properties to adapt to different environmental stimuli. This capability is crucial for the effective processing of information by neurons, preventing excessive excitability (<PERSON> & <PERSON>, 2008; <PERSON> et al., 2009) . A key biological mechanism is regulation through the activity of GABAergic neurons, which release GABA onto the postsynaptic membrane of the target neuron, leading to hyperpolarization and inhibition of excessive action potential production (<PERSON><PERSON><PERSON> et al., 2002) .", "cite_spans": [{"start": 238, "end": 258, "text": "(Benda & Herz, 2003)", "ref_id": "BIBREF2"}, {"start": 488, "end": 517, "text": "(Klausberger & Somogyi, 2008;", "ref_id": "BIBREF22"}, {"start": 518, "end": 538, "text": "<PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF38"}, {"start": 782, "end": 804, "text": "(<PERSON><PERSON><PERSON> et al., 2002)", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>ailed Discussion on Inspired Biological Principles", "sec_num": null}, {"text": "The CLIF model simulates this hyperpolarization process and the regulation of action potential generation by resetting a greater amount of membrane potential after each firing, attempting to replicate this type of adaptive regulation characteristic of biological neurons in a computational model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>ailed Discussion on Inspired Biological Principles", "sec_num": null}, {"text": "In this section, we first discuss the firing dynamic behavior of the CLIF, and then we discuss the auto-correlation for the membrane potential. Finally, we discuss the dynamic difference between the CLIF and the current-based, adaptive threshold model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "Firstly, we analyze the fire rate and auto-correlation of CLIF according to the same Poisson random input. The firing dynamic behavior under the different timestep for the single CLIF neuron is shown in Figure .7. We can find that compared with LIF neurons, CLIF neuron has extra refractory periods resulting lower fire rate. Secondly, we calculate the auto-correlation function using", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "R x [k] = 1 N N -1 n=0 x[n] • x[n -k].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "The results are shown in Figure .8. The auto-correlation function indicates the degree of correlation between a signal and a delayed version of itself. We can observe that the auto-correlation value of the complementary membrane potential decays slower, and its period is longer. This suggests that CLIF can capture more and longer correlations in the temporal dimension than LIF. Finally, CLIF shares more similarities to the Adaptive Threshold model (<PERSON><PERSON> et al., 2020) than to the Current-Base model (<PERSON> & Ganguli, 2018) . As for synaptic input current for both CLIF and adaptive threshold model take the form of W s[t], different from current-base model", "cite_spans": [{"start": 452, "end": 473, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF1"}, {"start": 505, "end": 528, "text": "(Zenke & Ganguli, 2018)", "ref_id": "BIBREF61"}], "ref_spans": [], "eq_spans": [], "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "I syn [t] = 1 τ I syn [t -1] + W s[t],", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "adaptive threshold model uses a latent variable to adjust the neurons' firing thresholds, whereas CLIF uses a latent variable (the complementary membrane potential) to adjust neurons reset strength.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F. Dynamic Analysis of CLIF neuron", "sec_num": null}, {"text": "The CLIF model can be rewritten as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "u l [t] = γ u l [t -1] -s l [t -1] ⊙ V th + σ(m l [t -1]) + W l s l-1 [t] (40) s l [t] = Θ(u l [t] -V th ) (41) m l [t] = m l [t -1] ⊙ σ 1 τ u l [t] + s l [t]", "eq_num": "(42)"}], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "where defined γ ≡ 1 -1 τ , then the gradients at l layer is calculated as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∇ W l L = T t=1 ∂L ∂u l [t] ∂u l [t] ∂W l , l = L, L -1, • • • , 1.", "eq_num": "(43)"}], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Where the right part could be deduced as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂u l [t] ∂W l = s l-1 [t]", "eq_num": "(44)"}], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "For the left part, we recursively evaluate:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "∂L ∂u l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] Spatial Gradients + ∂L ∂u l [t + 1] ϵ l [t] + ∂u l [t + 1] ∂m l [t] ψ l [t]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Temporal Gradients of Membrane Potential", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "+ ∂L ∂m l [t] ψ l [t]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Temporal Gradients of Complementary (45)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "The eligibility, this terminology mainly refers to e-prop (<PERSON><PERSON> et al., 2020) , STBP (<PERSON> et al., 2018) . From the LIF model, the equation can be deduced as:", "cite_spans": [{"start": 58, "end": 79, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF1"}, {"start": 87, "end": 104, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF53"}], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ϵ l [t] ≡ ∂u l [t + 1] ∂u l [t] + ∂u l [t + 1] ∂s l [t] ∂s l [t] ∂u l [t]", "eq_num": "(46)"}], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "The Complementary will also introduce the ψ l [t]:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ψ l [t] ≡ ∂m l [t] ∂u l [t] + ∂m l [t] ∂s l [t] ∂s l [t] ∂u l [t] = 1 τ m l [t -1] ≥0 ⊙ σ ′ 1 τ u l [t] ∈(0,1) + H u l [t] ≥0", "eq_num": "(47)"}], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Besides, the Complementary gradient line will introduce the recursively part:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂m l [t] = ∂L ∂u l [t + 1] ∂u l [t + 1] ∂m l [t] + ∂L ∂m l [t + 1] ∂m l [t + 1] ∂m l [t] + ψ l [t + 1] ∂u l [t + 1] ∂m l [t]", "eq_num": "(48)"}], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "To better understand the eligibility in Eq.45 and Eq.48, we can refer to the following Figure . Merging the Eq.45 and Eq.48 as matrix computing process:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "1 -ψ l [t] 0 1 ∂L ∂u l [t] ∂L ∂m l [t] =   ϵ l [t] + ∂u l [t+1] ∂m l [t] ψ l [t] 0 ∂u l [t+1] ∂m l [t] ∂m l [t+1] ∂m l [t] + ψ l [t + 1] ∂u l [t+1] ∂m l [t]   ∂L ∂u l [t+1] ∂L ∂m l [t+1] + ∂L ∂s l [t] ∂s l [t] ∂u l [t] 0", "eq_num": "(49)"}], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "where the blue part is the original gradient of the LIF neuron, and the other parts are introduced by Complementary.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Firstly, we first recursively expand the Eq.48:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂m l [t] = ∂L ∂u l [t + 1] ∂u l [t + 1] ∂m l [t] + ∂L ∂m l [t + 1] ∂m l [t + 1] ∂m l [t] + ψ l [t + 1] ∂u l [t + 1] ∂m l [t] Define as ρ l [t] = ∂L ∂u l [t + 1] ∂u l [t + 1] ∂m l [t] + ∂L ∂u l [t + 2] ∂u l [t + 2] ∂m l [t + 1] + ∂L ∂m l [t + 2] ρ l [t + 1] expansion ρ l [t] = ∂L ∂u l [t + 1] ∂u l [t + 1] ∂m l [t] + ∂L ∂u l [t + 2] ∂u l [t + 2] ∂m l [t + 1] ρ l [t] + ∂L ∂m l [t + 2] expansion ρ l [t + 1]ρ l [t] = ∂L ∂u l [t + 1] ∂u l [t + 1] ∂m l [t] + ∂L ∂u l [t + 2] ∂u l [t + 2] ∂m l [t + 1] ρ l [t] + ∂L ∂u l [t + 3] ∂u l [t + 3] ∂m l [t + 2] ρ l [t + 1]ρ l [t] + ∂L ∂m l [t + 3] expansion until T ρ l [t + 2] . . . ρ l [t] = ∂L ∂u l [t + 1] ∂u l [t + 1] ∂m l [t] + ∂L ∂u l [t + 2] ∂u l [t + 2] ∂m l [t + 1] ρ l [t] + ... + ∂L ∂u l [T ] ∂u l [T ] ∂m l [T -1] ρ l [T -2]...ρ l [t + 1]ρ l [t]", "eq_num": "(50)"}], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "By mathematical induction, we can deduce that:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂m l [t] = T t ′ =t+1 ∂L ∂u l [t ′ ] ∂u l [t ′ ] ∂m l [t ′ -1] t ′ -t t ′′ =2 ρ l [t ′ -t ′′ ]", "eq_num": "(51)"}], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Secondly, we recursively expand the Eq.( 45):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂u l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] Spatial Gradients + ∂L ∂u l [t + 1] ϵ l [t] + ∂u l [t + 1] ∂m l [t] ψ l [t] Temporal Gradients of Membrane Potential + ∂L ∂m l [t] ψ l [t] Temporal Gradients of Complementary = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂u l [t + 1] expansion ϵ l [t] + ∂u l [t + 1] ∂m l [t] ψ l [t] Define as ξ l [t] + ∂L ∂m l [t] ψ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] + ∂L ∂u l [t + 2] ξ l [t + 1] + ∂L ∂m l [t + 1] ψ l [t + 1] ξ l [t] + ∂L ∂m l [t] ψ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] ξ l [t] + ∂L ∂u l [t + 2] expansion utill T ξ l [t + 1]ξ l [t] + ∂L ∂m l [t + 1] ψ l [t + 1]ξ l [t] + ∂L ∂m l [t] ψ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] ξ l [t] + • • • + ∂L ∂s l [T ] ∂s l [T ] ∂u l [T ] ξ l [T -1] . . . ξ l [t] + ∂L ∂m l [T ] ψ l [T ]ξ l [T -1] . . . ξ l [t] + ∂L ∂m l [T -1] ψ l [T -1]ξ l [T -2] . . . ξ l [t] + • • • + ∂L ∂m l [t + 1] ψ l [t + 1]ξ l [t] + ∂L ∂m l [t] ψ l [t]", "eq_num": "(52)"}], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Note that similar items in Eq.( 52) can be merged as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂u l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂m l [t] ψ l [t] + ∂L ∂s l [t + 1] ∂s l [t + 1] ∂u l [t + 1] + ∂L ∂m l [t + 1] ψ l [t + 1] ξ l [t] + • • • + ∂L ∂s l [T ] ∂s l [T ] ∂u l [T ] + ∂L ∂m l [T ] ψ l [T ] ξ l [T -1]ξ l [T -2] . . . ξ l [t + 1]ξ l [t] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂m l [t] ψ l [t] + T t ′ =t+1 ∂L ∂s l [t ′ ] ∂s l [t ′ ] ∂u l [t ′ ] + ∂L ∂m l [t ′ ] ψ l [t ′ ] t ′ -t t ′′ =1 ξ l [t ′ -t ′′ ] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂m l [t] ψ l [t] + T t ′ =t+1 ∂L ∂s l [t ′ ] ∂s l [t ′ ] ∂u l [t ′ ] + ∂L ∂m l [t ′ ] ψ l [t ′ ] t ′ -t t ′′ =1 ϵ l [t ′ -t ′′ ] + ∂u l [t ′ -t ′′ + 1] ∂m l [t ′ -t ′′ ] ψ l [t ′ -t ′′ ] = ∂L ∂s l [t] ∂s l [t] ∂u l [t] + ∂L ∂m l [t] ψ l [t] + T t ′ =t+1 ∂L ∂s l [t ′ ] ∂s l [t ′ ] ∂u l [t ′ ] t ′ -t t ′′ =1 ϵ l [t ′ -t ′′ ] + ∂u l [t ′ -t ′′ + 1] ∂m l [t ′ -t ′′ ] ψ l [t ′ -t ′′ ] + T t ′ =t+1 ∂L ∂m l [t ′ ] ψ l [t ′ ] t ′ -t t ′′ =1 ϵ l [t ′ -t ′′ ] + ∂u l [t ′ -t ′′ + 1] ∂m l [t ′ -t ′′ ] ψ l [t ′ -t ′′ ]", "eq_num": "(53)"}], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Here again, the gradient of the original LIF neuron is plotted in blue. We can intuitively see that the temporal gradient contributions from the Complementary component are more significant than those from LIF. Even in the worst case all of ξ → 0, ∂L ∂m l [t] also provides the sum of all temporal gradients as shown in Figure .(51), like shortcut at temporal dimension.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G. The Gradients of CLIF Neuron", "sec_num": null}, {"text": "Following Figure .3 we extend the loss comparison to various tasks and network backbones. The results are shown in Figure .10, CLIF neuron's loss converges faster than LIF's, the converged loss is also lower. This tendency demonstrates the advantage of the CLIF neuron model. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "H. The Loss Comparing between LIF and CLIF", "sec_num": null}, {"text": "Unless otherwise specified or for the purpose of comparative experiments, the experiments in this paper adhere to the following settings and data preprocessing: all our self-implementations use Rectangle surrogate functions with α = V th = 1, and the decay constant τ is set to 2.0. All random seed settings are 2022. For all loss functions, we use the TET (<PERSON><PERSON> et al., 2021) with a 0.05 loss lambda, as implemented in (<PERSON><PERSON> et al., 2023) . The following are the detailed default setups and dataset descriptions. To comprehensively and fairly evaluate the energy consumption (<PERSON><PERSON><PERSON><PERSON> et al., 2022b) , we recalculated and analyzed the energy consumption of the proposed CLIF neuron in more detail in Table 7 . We considered memory read and write operations, as well as the data addressing process, as done in (<PERSON><PERSON><PERSON> et al., 2022) . As shown in Table 7 , the memory accesses are actually the dominant factor in energy consumption for SNN. Although the hidden states of LIF and CLIF contribute significantly to the read and write energy consumption of the membrane potential, the sparsity of spikes also greatly reduces the parameters and synaptic operations. Therefore, the energy consumption of LIF and CLIF is still much lower than that of ANN. The detailed computing process can be found in the open-source code. In addition, it is feasible to train a model using CLIF and subsequently deploy it or inference with LIF. We take pre-trained models of CLIF and LIF (Resnet18 with T=6) to perform inference on the CIFAR10 and CIFAR100 tasks. To compensate for CLIF's enhanced reset process, we employ a hard reset with a bias as a hyperparameter. As can be seen in Table , this approach leads to an inference accuracy that surpasses that of a model directly trained with LIF. ", "cite_spans": [{"start": 357, "end": 376, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF9"}, {"start": 420, "end": 439, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF35"}, {"start": 576, "end": 603, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022b)", "ref_id": null}, {"start": 813, "end": 835, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF24"}], "ref_spans": [{"start": 710, "end": 711, "text": "7", "ref_id": "TABREF14"}, {"start": 856, "end": 857, "text": "7", "ref_id": "TABREF14"}], "eq_spans": [], "section": "I. Experiment Description and Dataset Pre-processing", "sec_num": null}], "back_matter": [{"text": "This work was supported in part by the Young Scientists Fund of the National Natural Science Foundation of China (Grant 62305278), by the Guangzhou Municipal Science and Technology Project under Grant 2023A03J0013 and Grant 2024A04J4535.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgments", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Spiking Neural Networks and Neuromorphic Computing. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Optimizer ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset", "sec_num": null}, {"text": "The CIFAR-10 and CIFAR-100 datasets (<PERSON><PERSON><PERSON><PERSON> et al., 2009) contain 60,000 32×32 color images in 10 and 100 different classes respectively, with each dataset comprising 50,000 training samples and 10,000 testing samples. We normalize the image data to ensure that input images have zero mean and unit variance. For data preprocessing, we directly follow this work (<PERSON><PERSON> et al., 2023) . We apply random cropping with padding of 4 pixels on each border of the image, random horizontal flipping, and cutout (<PERSON> & Taylor, 2017) . Direct encoding (<PERSON>hi & Roy, 2021) is employed to encode the image pixels into time series, wherein pixel values are applied repeatedly to the input layer at each timestep. For the CIFAR classification task, we use Spiking-Resnet18 as the backbone.Tiny-ImageNet Tiny-ImageNet contains 200 categories and 100,000 64×64 colored images for training, which is a more challenging static image dataset than CIFAR datasets. To augment Tiny-ImageNet datasets, we take the same AutoAugment (<PERSON><PERSON><PERSON> et al., 2019) as used in this work (<PERSON> et al., 2023 ), but we do not adopt Cutout (<PERSON><PERSON><PERSON> & <PERSON>, 2017) . For the Tiny-ImageNet classification task, we use Spiking-VGG13 as the backbone.", "cite_spans": [{"start": 36, "end": 61, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF23"}, {"start": 366, "end": 385, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF35"}, {"start": 506, "end": 530, "text": "(DeVries & Taylor, 2017)", "ref_id": "BIBREF11"}, {"start": 549, "end": 568, "text": "(<PERSON><PERSON> & Roy, 2021)", "ref_id": "BIBREF40"}, {"start": 1015, "end": 1035, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF3"}, {"start": 1057, "end": 1075, "text": "(<PERSON> et al., 2023", "ref_id": "BIBREF51"}, {"start": 1106, "end": 1130, "text": "(DeVries & Taylor, 2017)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "CIFAR-10/100", "sec_num": null}, {"text": "The DVS-CIFAR10 dataset (<PERSON> et al., 2017 ) is a neuromorphic dataset converted from CIFAR-10 using a DVS camera. It contains 10,000 event-based images with pixel dimensions expanded to 128×128. The event-to-frame integration is handled with the SpikingJelly (<PERSON> et al., 2023) framework. We do not apply any data augmentation for DVSCIFAR10 and the Spiking-VGG11 is used as the backbone to compare the performance.DVSGesture The DVS128 Gesture dataset (<PERSON> et al., 2017 ) is a challenging neuromorphic dataset that records 11 gestures performed by 29 different participants under three lighting conditions. The dataset comprises 1,342 samples with an average duration of 6.5 ± 1.7 s and all samples are split into a training set (1208 samples) and a test set (134 samples). We follow the method described in (<PERSON> et al., 2021b) to integrate the events into frames. The event-to-frame integration is handled with the SpikingJelly (<PERSON> et al., 2023) framework. We do not applied any data augmentation for DVSGesture and the Spiking-VGG11 is used as the backbone to compare the performance.", "cite_spans": [{"start": 24, "end": 40, "text": "(<PERSON> et al., 2017", "ref_id": "BIBREF26"}, {"start": 258, "end": 277, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF15"}, {"start": 453, "end": 471, "text": "(<PERSON> et al., 2017", "ref_id": "BIBREF0"}, {"start": 810, "end": 830, "text": "(<PERSON> et al., 2021b)", "ref_id": null}, {"start": 932, "end": 951, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF15"}], "ref_spans": [], "eq_spans": [], "section": "DVSCIFAR10", "sec_num": null}, {"text": "We calculate the fire rate as well as the energy efficiency of all models for five tasks. As shown in Figure .11, the average fire rate of the CLIF model is lower than that of the LIF model. This lower fire rate results in fewer synaptic operations, as evidenced in Table .6. For the evaluation of energy consumption, we follow the convention of the neuromorphic computing community by counting the total synaptic operations (SOP) to estimate the computation overhead of SNN models and compare it to the energy consumption of the ANN counterpart, as done in (<PERSON> et al., 2022; <PERSON> et al., 2024) . Specifically, the SOP with MAC presented in ANNs is constant given a specified structure. However, the SOP in SNN varies with spike sparsity. For SNNs, since the input is binary, the synaptic operation is mostly accumulation (ACs) instead of multiply and accumulation (MACs). ACs is defined aswhere fan-out f l i is the number of outgoing connections to the subsequent layer, and N l is the neuron number of the l-th layer. For ANNs, the similar synaptic operation MACs with more expensive multiply-accumulate is defined as:Here, we select all the testing datasets and estimate the average SOP for SNNs. Meanwhile, we measure 32-bit floating-point ACs by 0.9 pJ per operation and 32-bit floating-point MAC by 4.6 pJ per operation, as done in (<PERSON> et al., 2015) . All the results are summarized in the Table .6, SNN has a significant energy consumption advantage over ANNs. Notably, the ACs operation of CLIF are considerably less than those of LIF, attributable to the lower fire rate. In contrast, the MAC operations of CLIF exceed those of LIF due to the increased number of floating-point operations, a result of the Complementary component introduced in CLIF. The final results indicate that CLIF achieves comparable performance to ANNs models while maintaining similar total energy efficiency to LIF.", "cite_spans": [{"start": 558, "end": 577, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF65"}, {"start": 578, "end": 595, "text": "<PERSON> et al., 2024)", "ref_id": "BIBREF59"}, {"start": 1340, "end": 1358, "text": "(<PERSON> et al., 2015)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "J. Evaluation of Fire Rate and Energy Consumption", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "A low power, fully event-based gesture recognition system", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Taba", "suffix": ""}, {"first": "D", "middle": [], "last": "Berg", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Mckinstry", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Nayak", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Mendoza", "suffix": ""}], "year": 2017, "venue": "Proceedings of the IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "7243--7252", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. A low power, fully event-based gesture recognition system. In Proceedings of the IEEE conference on computer vision and pattern recognition, pp. 7243-7252, 2017.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "A solution to the learning dilemma for recurrent networks of spiking neurons", "authors": [{"first": "G", "middle": [], "last": "Bellec", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Hajek", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Legenstein", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Nature communications", "volume": "11", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, W. A solution to the learn- ing dilemma for recurrent networks of spiking neurons. Nature communications, 11(1):3625, 2020.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "A universal model for spikefrequency adaptation", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["V"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2003, "venue": "Neural computation", "volume": "15", "issue": "11", "pages": "2523--2564", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, A<PERSON>. A universal model for spike- frequency adaptation. Neural computation, 15(11):2523- 2564, 2003.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Learning augmentation strategies from data", "authors": [{"first": "E", "middle": ["D"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Zoph", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": ["V"], "last": "Le", "suffix": ""}, {"first": "", "middle": [], "last": "Autoaugment", "suffix": ""}], "year": 2019, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "113--123", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, Q. V. Autoaugment: Learning augmentation strategies from data. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 113-123, 2019.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Investigating current-based and gating approaches for accurate and energy-efficient spiking recurrent neural networks", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Mesquida", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Artificial Neural Networks", "volume": "", "issue": "", "pages": "359--370", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, L. Investigating current-based and gating approaches for accurate and energy-efficient spiking recurrent neural net- works. In International Conference on Artificial Neural Networks, pp. 359-370. Springer, 2022a.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Are snns really more energy-efficient than anns? an in-depth hardware-aware study", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Mesquida", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "IEEE Transactions on Emerging Topics in Computational Intelligence", "volume": "7", "issue": "3", "pages": "731--741", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, L. Are snns really more energy-efficient than anns? an in-depth hardware-aware study. IEEE Transactions on Emerging Topics in Computational Intelligence, 7(3):731- 741, 2022b.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Leveraging sparsity with spiking recurrent neural networks for energy-efficient keyword spotting", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Mesquida", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICASSP 2023-2023 IEEE International Conference on Acoustics, Speech and Signal Processing", "volume": "", "issue": "", "pages": "1--5", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Leveraging sparsity with spiking recurrent neural networks for energy-efficient keyword spotting. In ICASSP 2023-2023 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP), pp. 1-5. IEEE, 2023.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Rethinking the performance comparison between snns and anns", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Hu", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "G", "middle": [], "last": "Li", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Neural networks", "volume": "121", "issue": "", "pages": "294--307", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Rethinking the performance com- parison between snns and anns. Neural networks, 121: 294-307, 2020.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Optimal conversion of conventional artificial neural networks to spiking neural networks", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2020, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, S. Optimal conversion of conventional artificial neural networks to spiking neural networks. In International Conference on Learning Representations, 2020.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Temporal efficient training of spiking neural network via gradient reweighting", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Temporal effi- cient training of spiking neural network via gradient re- weighting. In International Conference on Learning Rep- resentations, 2021.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Surrogate module learning: Reduce the gradient error accumulation in training spiking neural networks", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "7645--7657", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Surrogate module learn- ing: Reduce the gradient error accumulation in training spiking neural networks. In International Conference on Machine Learning, pp. 7645-7657. PMLR, 2023.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Improved regularization of convolutional neural networks with cutout", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": ["W"], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1708.04552"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>roved regularization of convolutional neural networks with cutout. arXiv preprint arXiv:1708.04552, 2017.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "An unsupervised stdp-based spiking neural network inspired by biologically plausible learning rules and connections", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Neural Networks", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> unsupervised stdp-based spiking neural network inspired by biologi- cally plausible learning rules and connections. Neural Networks, 2023.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Deep residual learning in spiking neural networks", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Masquelier", "suffix": ""}, {"first": "Y", "middle": [], "last": "Tian", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "21056--21069", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Deep residual learning in spiking neural net- works. Advances in Neural Information Processing Sys- tems, 34:21056-21069, 2021a.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Incorporating learnable membrane time constant to enhance learning of spiking neural networks", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Masquelier", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Tian", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "2661--2671", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Incorporating learnable membrane time constant to enhance learning of spiking neural networks. In Pro- ceedings of the IEEE/CVF international conference on computer vision, pp. 2661-2671, 2021b.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "An open-source machine learning infrastructure platform for spike-based intelligence", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Masquelier", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "Tian", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Science Advances", "volume": "9", "issue": "40", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: An open-source machine learning infrastructure platform for spike-based intelligence. Science Advances, 9(40): eadi1480, 2023.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Parallel spiking neurons with high efficiency and ability to learn long-term dependencies", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Ma", "suffix": ""}, {"first": "T", "middle": [], "last": "Masquelier", "suffix": ""}, {"first": "Y", "middle": [], "last": "Tian", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> spiking neurons with high efficiency and ability to learn long-term dependen- cies. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Joint a-snn: Joint training of artificial and spiking neural networks via self-distillation and weight factorization", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Ma", "suffix": ""}], "year": 2023, "venue": "Pattern Recognition", "volume": "142", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Joint a-snn: Joint training of artificial and spiking neural networks via self-distillation and weight factorization. Pattern Recognition, 142:109639, 2023.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Learning both weights and connections for efficient neural network", "authors": [{"first": "S", "middle": [], "last": "Han", "suffix": ""}, {"first": "J", "middle": [], "last": "Pool", "suffix": ""}, {"first": "J", "middle": [], "last": "Tran", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Advances in neural information processing systems", "volume": "28", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Learning both weights and connections for efficient neural network. Advances in neural information processing systems, 28, 2015.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Klif: An optimized spiking neuron unit for tuning surrogate gradient slope and membrane potential", "authors": [{"first": "C", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.09238"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>: An optimized spiking neuron unit for tuning surrogate gradient slope and membrane potential. arXiv preprint arXiv:2302.09238, 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "A unified optimization framework of ann-snn conversion: towards optimal mapping from activation values to firing rates", "authors": [{"first": "H", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "14945--14974", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, B. A unified optimization framework of ann-snn conversion: towards optimal mapping from activation values to firing rates. In International Conference on Machine Learning, pp. 14945-14974. PMLR, 2023.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Stdp-based spiking deep convolutional neural networks for object recognition", "authors": [{"first": "S", "middle": ["R"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Masquelier", "suffix": ""}], "year": 2018, "venue": "Neural Networks", "volume": "99", "issue": "", "pages": "56--67", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, T. Stdp-based spiking deep convolutional neural networks for object recognition. Neural Networks, 99:56-67, 2018.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Neuronal diversity and temporal dynamics: the unity of hippocampal circuit operations", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2008, "venue": "Science", "volume": "321", "issue": "5885", "pages": "53--57", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>. <PERSON>onal diversity and temporal dynamics: the unity of hippocampal circuit operations. Science, 321(5885):53-57, 2008.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Learning multiple layers of features from tiny images", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Learning multiple layers of features from tiny images. 2009.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "An analytical estimation of spiking neural networks energy efficiency", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P.-E", "middle": [], "last": "Novac", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Neural Information Processing", "volume": "", "issue": "", "pages": "574--587", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, B. An analytical estimation of spiking neural networks energy efficiency. In Interna- tional Conference on Neural Information Processing, pp. 574-587. Springer, 2022.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Origin of gabaergic neurons in the human neocortex", "authors": [{"first": "K", "middle": [], "last": "Letinic", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Rakic", "suffix": ""}], "year": 2002, "venue": "Nature", "volume": "417", "issue": "6889", "pages": "645--649", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Origin of gabaergic neurons in the human neocortex. Nature, 417(6889): 645-649, 2002.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Cifar10-dvs: an event-stream dataset for object classification", "authors": [{"first": "H", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Li", "suffix": ""}, {"first": "L", "middle": [], "last": "Shi", "suffix": ""}], "year": 2017, "venue": "Frontiers in neuroscience", "volume": "11", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>10-dvs: an event-stream dataset for object classification. Frontiers in neuroscience, 11:309, 2017.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Braininspired multilayer perceptron with spiking neurons", "authors": [{"first": "W", "middle": [], "last": "Li", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "783--793", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Brain- inspired multilayer perceptron with spiking neurons. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 783-793, 2022.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "A free lunch from ann: Towards efficient, accurate spiking neural networks calibration", "authors": [{"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": null, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. A free lunch from ann: Towards efficient, accurate spiking neu- ral networks calibration. In International conference on machine learning, pp. 6316-6325. PMLR, 2021a.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Differentiable spike: Rethinking gradient-descent for training spiking neural networks", "authors": [{"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "23426--23439", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. Dif- ferentiable spike: Rethinking gradient-descent for train- ing spiking neural networks. Advances in Neural Infor- mation Processing Systems, 34:23426-23439, 2021b.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Long short-term memory spiking networks and their applications", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Neuromorphic Systems", "volume": "", "issue": "", "pages": "1--9", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, S. Long short-term memory spiking networks and their applications. In In- ternational Conference on Neuromorphic Systems 2020, pp. 1-9, 2020.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Networks of spiking neurons: the third generation of neural network models", "authors": [{"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1997, "venue": "Neural networks", "volume": "10", "issue": "9", "pages": "1659--1671", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> Networks of spiking neurons: the third genera- tion of neural network models. Neural networks, 10(9): 1659-1671, 1997.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Properties of a hyperpolarization-activated cation current and its role in rhythmic oscillation in thalamic relay neurones", "authors": [{"first": "D", "middle": ["A"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H.-C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1990, "venue": "The Journal of physiology", "volume": "431", "issue": "1", "pages": "291--318", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, H.-C. Properties of a hyperpolarization-activated cation current and its role in rhythmic oscillation in thalamic relay neurones. The Journal of physiology, 431(1):291-318, 1990.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Brain-inspired computing needs a master plan", "authors": [{"first": "A", "middle": [], "last": "Mehonic", "suffix": ""}, {"first": "A", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Nature", "volume": "604", "issue": "7905", "pages": "255--260", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> <PERSON>. Brain-inspired computing needs a master plan. Nature, 604(7905):255-260, 2022.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Training high-performance low-latency spiking neural networks by differentiation on spike representation", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Yan", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z.-Q", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "12444--12453", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Z.- Q. Training high-performance low-latency spiking neural networks by differentiation on spike representation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 12444-12453, 2022.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Towards memory-and time-efficient backpropagation for training spiking neural networks", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Yan", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z.-Q", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "6166--6176", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>- Q. Towards memory-and time-efficient backpropagation for training spiking neural networks. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 6166-6176, 2023.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Direct training of snn using local zeroth order method", "authors": [{"first": "B", "middle": [], "last": "Mukhoty", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2023, "venue": "Thirty-seventh Conference on Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Direct training of snn using local zeroth order method. In Thirty-seventh Conference on Neural Information Processing Systems, 2023.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Surrogate gradient learning in spiking neural networks: Bringing the power of gradient-based optimization to spiking neural networks", "authors": [{"first": "E", "middle": ["O"], "last": "Neftci", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "IEEE Signal Processing Magazine", "volume": "36", "issue": "6", "pages": "51--63", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Surrogate gradient learning in spiking neural networks: Bringing the power of gradient-based optimization to spiking neural networks. IEEE Signal Processing Magazine, 36(6):51-63, 2019.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Habituation revisited: an updated and revised description of the behavioral characteristics of habituation", "authors": [{"first": "C", "middle": ["H"], "last": "Rankin", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Bhatnagar", "suffix": ""}, {"first": "D", "middle": ["F"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Colombo", "suffix": ""}, {"first": "G", "middle": [], "last": "Coppola", "suffix": ""}, {"first": "M", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Marsland", "suffix": ""}], "year": 2009, "venue": "Neurobiology of learning and memory", "volume": "92", "issue": "2", "pages": "135--138", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Bhatnagar, S., Clayton, D. <PERSON>., Colombo, J., Coppola, G<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, D. <PERSON>, Marsland, S., et al. Habituation revis- ited: an updated and revised description of the behavioral characteristics of habituation. Neurobiology of learning and memory, 92(2):135-138, 2009.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "A long shortterm memory for ai applications in spike-based neuromorphic hardware", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Plank", "suffix": ""}, {"first": "A", "middle": [], "last": "Wild", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Nature Machine Intelligence", "volume": "4", "issue": "5", "pages": "467--479", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, W. A long short- term memory for ai applications in spike-based neuro- morphic hardware. Nature Machine Intelligence, 4(5): 467-479, 2022.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Diet-snn: A low-latency spiking neural network with direct input encoding and leakage and threshold optimization", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "IEEE Transactions on Neural Networks and Learning Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>: A low-latency spiking neural network with direct input encoding and leakage and threshold optimization. IEEE Transactions on Neural Networks and Learning Systems, 2021.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Towards spike-based machine intelligence with neuromorphic computing", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Jaiswal", "suffix": ""}, {"first": "P", "middle": [], "last": "Panda", "suffix": ""}], "year": 2019, "venue": "Nature", "volume": "575", "issue": "7784", "pages": "607--617", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, P. Towards spike-based ma- chine intelligence with neuromorphic computing. Nature, 575(7784):607-617, 2019.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Cellular and network mechanisms of rhythmic recurrent activity in neocortex", "authors": [{"first": "M", "middle": ["V"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["A"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2000, "venue": "Nature neuroscience", "volume": "3", "issue": "10", "pages": "1027--1034", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> <PERSON>. Cellular and network mechanisms of rhythmic recurrent activity in neocortex. Nature neuroscience, 3(10):1027-1034, 2000.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Opportunities for neuromorphic computing algorithms and applications", "authors": [{"first": "C", "middle": ["D"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["R"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Parsa", "suffix": ""}, {"first": "J", "middle": ["P"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Date", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Nature Computational Science", "volume": "2", "issue": "1", "pages": "10--19", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, B. Opportunities for neuromorphic computing algorithms and applications. Nature Compu- tational Science, 2(1):10-19, 2022.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Efficient spiking neural networks with sparse selective activation for continual learning", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "38", "issue": "", "pages": "611--619", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, H. Efficient spiking neural networks with sparse selective activation for con- tinual learning. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 38, pp. 611-619, 2024.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "The expressive leaky memory neuron: an efficient and expressive phenomenological neuron model can solve long-horizon tasks", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. The expressive leaky memory neuron: an efficient and expressive phenomenological neuron model can solve long-horizon tasks. In The Twelfth International Conference on Learning Representations, 2023.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Deep directly-trained spiking neural networks for object detection", "authors": [{"first": "Q", "middle": [], "last": "Su", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Hu", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Li", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "6555--6565", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> directly-trained spiking neural networks for object detection. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 6555-6565, 2023.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Deep learning in spiking neural networks", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["R"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Masquelier", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "A", "middle": [], "last": "", "suffix": ""}], "year": 2019, "venue": "Neural networks", "volume": "111", "issue": "", "pages": "47--63", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> learning in spiking neural networks. Neural networks, 111:47-63, 2019.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Generalized leaky integrate-and-fire models classify multiple neuron types", "authors": [{"first": "C", "middle": [], "last": "Teeter", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Berg", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Nature communications", "volume": "9", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Generalized leaky integrate-and-fire models classify multiple neuron types. Nature communications, 9(1):709, 2018.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Learning improvement of spiking neural networks with learnable thresholding neurons and moderate dropout", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "M.<PERSON><PERSON>", "middle": [], "last": "Lim", "suffix": ""}, {"first": "", "middle": [], "last": "Ltmd", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "28350--28362", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>: Learning improvement of spiking neural networks with learnable thresholding neurons and moderate dropout. Advances in Neural Information Processing Systems, 35:28350- 28362, 2022a.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Signed neuron with memory: Towards simple, accurate and highefficient ann-snn conversion", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2022, "venue": "International Joint Conference on Artificial Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Signed neu- ron with memory: Towards simple, accurate and high- efficient ann-snn conversion. In International Joint Con- ference on Artificial Intelligence, 2022b.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Adaptive smoothing gradient learning for spiking neural networks", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Yan", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "35798--35816", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Adaptive smoothing gradient learning for spiking neural networks. In International Conference on Machine Learning, pp. 35798-35816. PMLR, 2023.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Backpropagation through time: what it does and how to do it", "authors": [{"first": "P", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1990, "venue": "Proceedings of the IEEE", "volume": "78", "issue": "10", "pages": "1550--1560", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>ropagation through time: what it does and how to do it. Proceedings of the IEEE, 78(10):1550- 1560, 1990.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Spatiotemporal backpropagation for training high-performance spiking neural networks", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Li", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Shi", "suffix": ""}], "year": 2018, "venue": "Frontiers in neuroscience", "volume": "12", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>tio- temporal backpropagation for training high-performance spiking neural networks. Frontiers in neuroscience, 12: 331, 2018.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Direct training for spiking neural networks: Faster, larger, better", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Li", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Shi", "suffix": ""}], "year": 2019, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "33", "issue": "", "pages": "1311--1318", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Direct training for spiking neural networks: Faster, larger, bet- ter. In Proceedings of the AAAI conference on artificial intelligence, volume 33, pp. 1311-1318, 2019.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Online training through time for spiking neural networks", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "He", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. On- line training through time for spiking neural networks. Advances in Neural Information Processing Systems, 35: 20717-20730, 2022.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Constructing deep spiking neural networks from artificial neural networks with knowledge distillation", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["K"], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Pan", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "7886--7895", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Constructing deep spiking neural networks from artificial neural networks with knowledge distillation. In Proceed- ings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 7886-7895, 2023.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Enhancing adaptive history reserving by spiking convolutional block attention module in recurrent neural networks", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Gao", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Pan", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, G. Enhancing adaptive history reserving by spiking convolutional block attention module in recurrent neural networks. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Training spiking neural networks with local tandem learning", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Li", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "12662--12676", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> Training spiking neural networks with local tandem learning. Advances in Neural Information Processing Systems, 35:12662-12676, 2022.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Spike-driven transformer", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Hu", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "Y", "middle": [], "last": "Tian", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Li", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Spike-driven transformer. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "A unified gated leaky integrate-and-fire neuron for spiking neural networks", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Li", "suffix": ""}, {"first": "Z", "middle": [], "last": "Mo", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "32160--32171", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>: A unified gated leaky integrate-and-fire neuron for spiking neural networks. Advances in Neural Information Processing Systems, 35:32160-32171, 2022.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Supervised learning in multilayer spiking neural networks", "authors": [{"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Superspike", "suffix": ""}], "year": 2018, "venue": "Neural computation", "volume": "30", "issue": "", "pages": "1514--1541", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, S. Superspike: Supervised learning in multilayer spiking neural networks. Neural computa- tion, 30(6):1514-1541, 2018.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Tc-lif: A two-compartment spiking neuron model for long-term sequential modelling", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Ma", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Li", "suffix": ""}, {"first": "K", "middle": ["C"], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "38", "issue": "", "pages": "16838--16847", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, K. C. Tc-lif: A two-compartment spiking neuron model for long-term sequential modelling. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 38, pp. 16838-16847, 2024.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Brain-inspired balanced tuning for spiking neural networks", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "In IJCAI", "volume": "7", "issue": "", "pages": "1653--1659", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Brain-inspired balanced tuning for spiking neural networks. In IJCAI, volume 7, pp. 1653-1659. Stockholm, 2018.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Skip-connected self-recurrent spiking neural networks with joint intrinsic parameter and synaptic weight training", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Li", "suffix": ""}], "year": 2021, "venue": "Neural computation", "volume": "33", "issue": "7", "pages": "1886--1913", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>-connected self-recurrent spik- ing neural networks with joint intrinsic parameter and synaptic weight training. Neural computation, 33(7): 1886-1913, 2021.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "Spikformer: When spiking neural network meets transformer", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "He", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Shuicheng", "suffix": ""}, {"first": "Y", "middle": [], "last": "Tian", "suffix": ""}, {"first": "L", "middle": [], "last": "Yuan", "suffix": ""}], "year": 2022, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>: When spiking neural net- work meets transformer. In The Eleventh International Conference on Learning Representations, 2022.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "text": "Figure 1. (a) Illustration of the LIF neuron model with forward propagation data flow (b) Illustration of the CLIF neuron model with forward propagation data flow (c) Illustration of the LIF's gradient error ∂L ∂u l [t] flow during BPTT. Each path is represented by an arrow. Lighter color in the arrow indicates more decay of gradient error. (d) Illustration of the CLIF's gradient error flow during BPTT. Compared to (c), the additional temporal gradient error is highlighted in red.", "num": null, "fig_num": "1", "uris": null}, "FIGREF1": {"type_str": "figure", "text": "Figure.2(b)  plots the classification accuracy over increasing timestep for both vanilla LIF and our proposed CLIF. The average and standard error are calculated from the results using 4 different random seeds. The accuracy of the vanilla LIF peaks at T = 32 and then declines as the number of timesteps increases. This indicates the temporal gradient from LIF over larger timestep cannot be properly processed. In contrast, the CLIF model demonstrates a sustained improvement of performance over increasing timestep, showcasing CLIF's effectiveness in learning over longer timestep.", "num": null, "fig_num": "22", "uris": null}, "FIGREF2": {"type_str": "figure", "text": "In the first ablation study, we use the Spiking Resnet18 with 6 timestep by BPTT training. The training of the network begins with LIF neuron and later transitions to CLIF neurons at a designated epoch. As shown in Figure.3(a), a few epochs after Exchange to CLIF, the loss decreases significantly compared to LIF. Moreover, the decay of loss over training epochs is much faster when training with CLIF than LIF.", "num": null, "fig_num": null, "uris": null}, "FIGREF3": {"type_str": "figure", "text": "Figure 3. (a) Loss function vs epochs. Each color presents a case of either LIF, CLIF, or exchanging from LIF to CLIF at a given epoch during training. (b) Comparison of the accuracy of LIF and CLIF at various timestep. Both experiments are evaluated on the CIFAR10 task with Spiking ResNet-18.", "num": null, "fig_num": "3", "uris": null}, "FIGREF4": {"type_str": "figure", "text": "Figure 4. Comparative accuracy of Spiking ResNet-18. Panels (a) CIFAR10 using 8 timestep (b) CIFAR100 using 6 timestep with different neuron.", "num": null, "fig_num": "4", "uris": null}, "FIGREF5": {"type_str": "figure", "text": "Figure 5. Illustration of the LIF neuron based SNN's gradient error flow during BPTT. In this example k=2: only the backpropagation from the first two timestep is considered (illustrated by the two red dashed arrows), and backpropagation along further timestep is discarded.", "num": null, "fig_num": "56", "uris": null}, "FIGREF6": {"type_str": "figure", "text": "Figure 7. The dynamic behavior of a single LIF and CLIF neuron at different timestep.", "num": null, "fig_num": "7", "uris": null}, "FIGREF7": {"type_str": "figure", "text": "Figure 8. The autocorrelation of a single LIF and CLIF neuron at different timestep.", "num": null, "fig_num": "8", "uris": null}, "FIGREF8": {"type_str": "figure", "text": "Figure 9. The abstract expression of (a) forward dependency and (b) backward eligibility trace for CLIF neuron.", "num": null, "fig_num": "9", "uris": null}, "FIGREF9": {"type_str": "figure", "text": "Figure 10. Testing loss curves of the training process of LIF-based and CLIF-based for each tasks.", "num": null, "fig_num": "10", "uris": null}, "TABREF2": {"html": null, "content": "<table><tr><td>sub-</td></tr><tr><td>tracted more, suppressing the neuron's firing rate. This</td></tr><tr><td>mechanism achieves spike frequency adaptation, similar</td></tr><tr><td>to the hyper-polarization process in real biological neu-</td></tr><tr><td>rons (McCORMICK &amp; Pape, 1990; Sanchez-Vives &amp; Mc-</td></tr></table>", "type_str": "table", "text": "Core function for CLIF model Input: Input c, Current Time Step t, time constant τ , threshold V th Output: Spike s if t = 0 then Initial u pre and m pre with all zero end", "num": null}, "TABREF3": {"html": null, "content": "<table><tr><td>Dataset</td><td>Method</td><td>Spiking Network</td><td colspan=\"3\">Neuron Model Timestep Accuracy(%)</td></tr><tr><td/><td>ANN / ANN *</td><td>ResNet-18</td><td>ReLU</td><td>1</td><td>95.62 / 96.65</td></tr><tr><td/><td>Dspike (Li et al., 2021b) 'NIPS</td><td>Modified ResNet-18</td><td>LIF</td><td>6</td><td>93.50</td></tr><tr><td/><td>PLIF (Fang et al., 2021b) 'ICCV</td><td>PLIF Net</td><td>PLIF</td><td>6</td><td>94.25</td></tr><tr><td/><td>DSR (Meng et al., 2022) 'CVPR</td><td>RreAct-ResNet-18</td><td>LIF</td><td>20</td><td>95.40</td></tr><tr><td>CIFAR-10</td><td>GLIF (Yao et al., 2022) 'NIPS KLIF (Jiang &amp; Zhang, 2023) 'ArXiv PSN * (Fang et al., 2024) 'NIPS SML (Deng et al., 2023) 'ICML</td><td>ResNet-18 Modified PLIF Net Modified PLIF Net ResNet-18</td><td>GLIF KLIF PSN LIF</td><td>6 10 4 6</td><td>94.88 92.52 95.32 95.12</td></tr><tr><td/><td>ASGL * (Wang et al., 2023) 'ICML</td><td>ResNet-18</td><td>LIF</td><td>4</td><td>95.35</td></tr><tr><td/><td/><td/><td/><td>4</td><td>94.89 / 96.01</td></tr><tr><td/><td>Ours / Ours *</td><td>ResNet-18</td><td>CLIF</td><td>6</td><td>95.41 / 96.45</td></tr><tr><td/><td/><td/><td/><td>8</td><td>95.68 / 96.69</td></tr><tr><td/><td>ANN / ANN *</td><td>ResNet-18</td><td>ReLU</td><td>1</td><td>78.14 / 80.89</td></tr><tr><td/><td>Dspike (Li et al., 2021b) 'NIPS</td><td>Modified ResNet-18</td><td>LIF</td><td>6</td><td>74.24</td></tr><tr><td>CIFAR-100</td><td>DSR (Meng et al., 2022) 'CVPR GLIF (Yao et al., 2022) 'NIPS SML (Deng et al., 2023) 'ICML ASGL * (Wang et al., 2023) 'ICML</td><td>RreAct-ResNet-18 ResNet-18 ResNet-18 ResNet-18</td><td>LIF GLIF LIF LIF</td><td>20 6 6 4 4</td><td>78.50 77.28 78.00 77.74 77.00 / 79.69</td></tr><tr><td/><td>Ours / Ours *</td><td>ResNet-18</td><td>CLIF</td><td>6</td><td>78.36 / 80.58</td></tr><tr><td/><td/><td/><td/><td>8</td><td>78.99 / 80.89</td></tr><tr><td>Tiny-ImageNet</td><td>ANN Online LTL (Yang et al., 2022) 'NIPS Joint A-SNN (Guo et al., 2023) 'Pattern Recognit ASGL (Wang et al., 2023) 'ICML Ours</td><td>VGG-13 VGG-13 VGG-16 VGG-13 VGG-13</td><td>ReLU LIF LIF LIF CLIF</td><td>1 6 4 8 4 6</td><td>59.77 55.37 55.39 56.81 63.16 64.13</td></tr></table>", "type_str": "table", "text": "Comparing the state-of-the-art methods on static image datasets. The asterisk ( * ) indicates the utilization of data augmentation strategies, including auto-augmentation and/or CutMix, our implementation directly following(<PERSON> et al., 2024). The implemented ReLU-based ANN shares identical structures and hyper-parameters with SNN.", "num": null}, "TABREF4": {"html": null, "content": "<table><tr><td>Dataset</td><td>Method</td><td>Spiking Network</td><td colspan=\"3\">Neuron Model T Accuracy(%)</td></tr><tr><td colspan=\"2\">PLIF (<PERSON> et al., 2021b) 'ICCV</td><td>PLIF Net</td><td>PLIF</td><td>20</td><td>97.57</td></tr><tr><td colspan=\"2\">KLIF (Jiang &amp; Zhang, 2023) 'ArXiv</td><td>Modified PLIF Net</td><td>KLIF</td><td>12</td><td>94.10</td></tr><tr><td>DVS-Gesture</td><td>Ours</td><td>Spiking-Vgg11 Spike-Driven-Transformer 1</td><td>LIF CLIF LIF CLIF</td><td>20 16</td><td>97.57 97.92 98.26 99.31</td></tr><tr><td colspan=\"2\">PLIF (Fang et al., 2021b) 'ICCV</td><td>PLIF Net</td><td>PIF</td><td>20</td><td>74.80</td></tr><tr><td colspan=\"2\">KLIF (Jiang &amp; Zhang, 2023) 'ArXiv</td><td>Modified PLIF Net</td><td>KLIF</td><td>15</td><td>70.90</td></tr><tr><td colspan=\"2\">GLIF (Yao et al., 2022) 'NIPS</td><td>7B-wideNet</td><td>GLIF</td><td>16</td><td>78.10</td></tr><tr><td colspan=\"2\">PSN (Fang et al., 2024) 'NIPS</td><td>VGGSNN</td><td>PSN</td><td>10</td><td>85.90</td></tr><tr><td>CIFAR10-DVS</td><td>Ours</td><td>Spiking-Vgg11 VGGSNN 2</td><td>LIF CLIF LIF CLIF</td><td>16 10</td><td>78.05 79.00 84.90 86.10</td></tr><tr><td>6SLNLQJ5HVQHWZLWK7LPHVWHS</td><td/><td/><td/><td/><td/></tr><tr><td>/,)</td><td/><td/><td/><td/><td/></tr><tr><td>&amp;/,)</td><td/><td/><td/><td/><td/></tr><tr><td>/RVV</td><td/><td/><td/><td/><td/></tr><tr><td>(SRFKV</td><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "text": "Comparing the SOTA neuronal models by using neuromorphic datasets. The footnote in the table indicates implementation directly in open source code by only modifying neurons: 1 (<PERSON> et al., 2024), 2 (<PERSON> et al., 2024) with data augmentation. 'T' denotes the number of the timestep employed.", "num": null}, "TABREF6": {"html": null, "content": "<table><tr><td>1, reveals that CLIF always</td></tr><tr><td>outperforms its LIF counterpart and surpasses all other</td></tr><tr><td>SNNs neuron models within the same network backbone.</td></tr><tr><td>CLIF achieves 96.69% accuracy on CIFAR-10 and 80.89%</td></tr><tr><td>on CIFAR-100 datasets, not only outperforming other SNN</td></tr><tr><td>models but also slightly outperforming ReLU-based ANNs</td></tr><tr><td>counterpart. In Tiny-ImageNet, CLIF achieves 64.13% ac-</td></tr><tr><td>curacy with 6 timestep, significantly better than the other</td></tr><tr><td>SNNs and ANN within the same network backbone. These</td></tr><tr><td>results demonstrate CLIF's competitiveness with existing</td></tr><tr><td>neuron models.</td></tr><tr><td>Neuromorphic Datasets To validate that our method can</td></tr><tr><td>handle spatio-temporal error backpropagation properly, we</td></tr><tr><td>conduct experiments on different neuromorphic datasets</td></tr><tr><td>of DVS-Gesture (Amir et al., 2017) and DVSCIFAR10</td></tr><tr><td>(Li et al., 2017). The results are summarized in Table 3.</td></tr><tr><td>For DVS Gesture, CLIF accuracy is 97.92% with Spiking-</td></tr><tr><td>VGG11 as backbone and 99.31% with Spike-Driven Trans-</td></tr><tr><td>former (Yao et al., 2024) as the backbone, surpassing</td></tr><tr><td>LIF based model by 0.35% and 1.05%, respectively. On</td></tr><tr><td>the DVSCIFAR10 dataset, CLIF accuracy is 79.00% with</td></tr><tr><td>Spiking-VGG11 as the backbone and 86.10% with VG-</td></tr><tr><td>GSNN as the backbone, surpassing LIF based model by</td></tr><tr><td>0.95% and 1.20%, respectively. It is worth noting that CLIF</td></tr><tr><td>features the highest accuracy of 86.10% in all methods in</td></tr><tr><td>this dataset. This is achieved by simply replacing the neuron</td></tr><tr><td>model with CLIF in the network architecture.</td></tr></table>", "type_str": "table", "text": "", "num": null}, "TABREF8": {"html": null, "content": "<table><tr><td>Last</td><td/><td/><td/><td/><td/></tr><tr><td>Layer</td><td/><td/><td/><td/><td/></tr><tr><td>𝑾 𝒍+𝟏</td><td/><td/><td/><td/><td/></tr><tr><td>𝒍 Layer 𝝏𝓛 𝓟 𝒕</td><td>𝑺 𝒕 𝑼 𝒕</td><td>∑ 𝓣 𝒕+𝟏</td><td>𝑺 𝒕+𝟏 𝑼 𝒕+𝟏</td><td>… … 𝓣 𝒕+⋯ … …</td><td>𝑺 𝑻 𝓣 𝑻 𝑼 𝑻</td></tr><tr><td>𝝏𝑼 𝒕</td><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "text": "Besides the 5-layer NN in Figure.6, we also evaluate CIFFAR10 with Spiking ResNet18 and DVSGesture with Spiking VGG11. Similar to 5layer SNN, the gradient from further timestep could not contribute to the backpropagation training process, as increasing k above 3 does not substantially enhance the accuracy. The training settings are shown in Table.4:", "num": null}, "TABREF9": {"html": null, "content": "<table><tr><td>k</td><td>τ</td><td>1.1</td><td>1.3</td><td>1.5</td><td>1.8</td><td>2</td><td>k</td><td>τ</td><td>1.1</td><td>1.3</td><td>1.5</td><td>1.8</td><td>2</td></tr><tr><td>1</td><td/><td colspan=\"5\">92.89 90.34 87.27 82.77 80.71</td><td>1</td><td/><td colspan=\"5\">96.88 97.22 95.83 95.49 96.53</td></tr><tr><td>2</td><td/><td colspan=\"5\">93.86 93.91 92.82 91.09 89.07</td><td>4</td><td/><td colspan=\"5\">97.57 97.92 97.22 96.53 96.53</td></tr><tr><td>3</td><td/><td colspan=\"5\">94.24 94.36 94.12 93.31 93.01</td><td>8</td><td/><td colspan=\"5\">97.57 97.57 97.22 97.57 97.22</td></tr><tr><td>4</td><td/><td colspan=\"5\">94.45 94.61 94.64 94.09 93.43</td><td>12</td><td/><td colspan=\"5\">97.22 97.57 97.22 97.22 97.57</td></tr><tr><td>5</td><td/><td colspan=\"5\">94.73 94.69 94.7 94.23 94.06</td><td>16</td><td/><td colspan=\"5\">97.57 97.57 97.92 97.22 97.57</td></tr><tr><td>6</td><td/><td colspan=\"5\">94.80 94.86 94.94 94.36 93.73</td><td>20</td><td/><td colspan=\"5\">97.57 97.57 97.92 97.57 97.22</td></tr></table>", "type_str": "table", "text": "Table.C, and the experiment hyperparameter as shown in Table.4. Left table is the CIFAR10 accuracy performance (%), Right table is the DVS Gesture accuracy performance (%). The random seeds are uniformly fixed across all instances.", "num": null}, "TABREF10": {"html": null, "content": "<table><tr><td>Parameter Datasets</td><td>CIFAR10</td><td>DVS Gesture</td></tr><tr><td>Networks</td><td colspan=\"2\">Spiking ResNet18 Spiking Vgg11</td></tr><tr><td>Time Steps (T)</td><td>6</td><td>20</td></tr><tr><td>Epochs (e)</td><td>200</td><td>300</td></tr><tr><td>Batch Size (bs)</td><td>128</td><td>16</td></tr><tr><td>Optimizer</td><td>SGD</td><td>SGD</td></tr><tr><td>Learning Rate (lr)</td><td>0.1</td><td>0.1</td></tr><tr><td>Weight Decay (wd)</td><td>5 × 10 -5</td><td>5 × 10 -4</td></tr><tr><td>Dropout Rate</td><td>0.0</td><td>0.4</td></tr></table>", "type_str": "table", "text": "Training Parameters", "num": null}, "TABREF12": {"html": null, "content": "<table/>", "type_str": "table", "text": "Training hyperparameters", "num": null}, "TABREF13": {"html": null, "content": "<table><tr><td>CIFAR10 (ResNet18)</td><td>ReLU CLIF LIF</td><td>1 6 6</td><td>11.2 11.2 11.2</td><td>0 68.66 84.86</td><td>557.65 5.12 2.89</td><td>2565.19 85.346 89.668</td></tr><tr><td>CIFAR100 (ResNet18)</td><td>ReLU CLIF LIF</td><td>1 6 6</td><td>11.2 11.2 11.2</td><td>0 55.58 60.28</td><td>557.7 5.16 2.93</td><td>2565.42 73.758 67.73</td></tr><tr><td>Tiny ImageNet (VGG13)</td><td>ReLU CLIF LIF</td><td>1 6 6</td><td>14.4 14.4 14.4</td><td>0 102.1 135.25</td><td>922.56 282.83 278.84</td><td>4243.776 1392.908 1404.389</td></tr><tr><td>DVSGesture</td><td>CLIF</td><td>20</td><td>9.5</td><td>19.16</td><td>1090</td><td>5031.244</td></tr><tr><td>(VGG11)</td><td>LIF</td><td>20</td><td>9.5</td><td>25.09</td><td>1080</td><td>4990.581</td></tr><tr><td>DVSCIFAR10</td><td>CLIF</td><td>10</td><td>9.5</td><td>12.02</td><td>153.87</td><td>718.62</td></tr><tr><td>(VGG11)</td><td>LIF</td><td>10</td><td>9.5</td><td>14.65</td><td>152.5</td><td>714.685</td></tr></table>", "type_str": "table", "text": "The energy consumption of synaptic operation for different tasks with the whole testing datasets.Neuron T Parameters(M) ACs (M) MACs (M) SOP Energy (µJ)", "num": null}, "TABREF14": {"html": null, "content": "<table><tr><td/><td colspan=\"3\">Mem. Read &amp; Write Potential (mJ) Membrane Parameters (mJ) In / Out (mJ)</td><td>Synaptic &amp; Neuron Op. (mJ)</td><td colspan=\"2\">Addr. (µJ) Total (mJ)</td></tr><tr><td/><td>0</td><td>54.9688</td><td>54.9357</td><td>1.7573</td><td>0.1145</td><td>111.6619</td></tr><tr><td>CIFAR10</td><td>22.9987</td><td>11.4994</td><td>0.0013</td><td>0.0190</td><td>12.1394</td><td>34.5304</td></tr><tr><td/><td>55.5172</td><td>9.2529</td><td>0.0007</td><td>0.0389</td><td>19.5184</td><td>64.8293</td></tr><tr><td/><td>0</td><td>54.9735</td><td>54.9357</td><td>1.7574</td><td>0.1145</td><td>111.6667</td></tr><tr><td>CIFAR100</td><td>16.8666</td><td>8.4337</td><td>0.0004</td><td>0.0171</td><td>8.8427</td><td>25.3267</td></tr><tr><td/><td>46.8265</td><td>7.8048</td><td>0.0004</td><td>0.0380</td><td>16.4053</td><td>54.6861</td></tr><tr><td/><td>0</td><td>91.9065</td><td>91.3834</td><td>2.9379</td><td>0.1686</td><td>186.2279</td></tr><tr><td>TinyImagenet</td><td>37.7700</td><td>18.9076</td><td>0.0050</td><td>1.1754</td><td>19.8379</td><td>57.8778</td></tr><tr><td/><td>84.8900</td><td>14.1756</td><td>0.0028</td><td>1.0986</td><td>29.7983</td><td>100.1968</td></tr><tr><td>DVSCIFAR10</td><td>5.7398 14.0891</td><td>2.8701 2.3484</td><td>0.0003 0.0002</td><td>0.0212 0.0451</td><td>2.9340 4.7976</td><td>8.6343 16.4876</td></tr><tr><td>DVSGesture</td><td>13.9212 38.3082</td><td>6.9606 6.3848</td><td>0.0003 0.0003</td><td>0.1682 0.4794</td><td>7.0728 12.9767</td><td>21.0574 45.1856</td></tr></table>", "type_str": "table", "text": "The total energy consumption for different tasks. The neuron and time step are the same as those in Table6.", "num": null}, "TABREF15": {"html": null, "content": "<table><tr><td>CIFAR10</td><td colspan=\"2\">Soft Reset Hard Reset</td><td/><td/><td/><td/><td/></tr><tr><td>Reset Value</td><td>None</td><td>0</td><td>-0.02</td><td>-0.04</td><td>-0.06</td><td>-0.08</td><td>-0.1</td></tr><tr><td>CLIF pretrained (95.41%)</td><td>92.95 %</td><td>93.41 %</td><td colspan=\"5\">94.18 % 94.54 % 95.08 % 94.84 % 94.72 %</td></tr><tr><td>LIF pretrained (94.51%)</td><td>94.51 %</td><td>84.05 %</td><td colspan=\"5\">76.68 % 66.08 % 52.16 % 38.00 % 27.04 %</td></tr><tr><td>CIFAR100</td><td colspan=\"2\">Soft Reset Hard Reset</td><td/><td/><td/><td/><td/></tr><tr><td>Reset Value</td><td>None</td><td>0</td><td>-0.02</td><td>-0.04</td><td>-0.06</td><td>-0.08</td><td>-0.1</td></tr><tr><td>CLIF pretrained (78.36 %)</td><td>68.72 %</td><td>73.04 %</td><td colspan=\"5\">74.64 % 76.63 % 76.55 % 77.00 % 76.54 %</td></tr><tr><td>LIF pretrained (76.23 %)</td><td>76.23 %</td><td>47.74 %</td><td colspan=\"4\">37.04 % 27.56 % 19.83 % 13.22 %</td><td>8.77 %</td></tr></table>", "type_str": "table", "text": "Directly convert the pre-trained CLIF/LIF model to an LIF neuron for inference.", "num": null}}}}