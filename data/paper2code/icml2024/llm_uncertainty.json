{"paper_id": "llm_uncertainty", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-24T23:34:51.296818Z"}, "title": "Decomposing Uncertainty for Large Language Models through Input Clarification Ensembling", "authors": [{"first": "Bairu", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Kaizhi", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Uncertainty decomposition refers to the task of decomposing the total uncertainty of a predictive model into aleatoric (data) uncertainty, resulting from inherent randomness in the data-generating process, and epistemic (model) uncertainty, resulting from missing information in the model's training data. In large language models (LLMs) specifically, identifying sources of uncertainty is an important step toward improving reliability, trustworthiness, and interpretability, but remains an important open research question. In this paper, we introduce an uncertainty decomposition framework for LLMs, called input clarification ensembling, which can be applied to any pre-trained LLM. Our approach generates a set of clarifications for the input, feeds them into an LLM, and ensembles the corresponding predictions. We show that, when aleatoric uncertainty arises from ambiguity or under-specification in LLM inputs, this approach makes it possible to factor an (un-clarified) LLM's predictions into separate aleatoric and epistemic terms, using a decomposition similar to the one employed by Bayesian neural networks. Empirical evaluations demonstrate that input clarification ensembling provides accurate and reliable uncertainty quantification on several language processing tasks. Code and data are available at https://github.com/ UCSB-NLP-Chang/llm_uncertainty.", "pdf_parse": {"paper_id": "llm_uncertainty", "_pdf_hash": "", "abstract": [{"text": "Uncertainty decomposition refers to the task of decomposing the total uncertainty of a predictive model into aleatoric (data) uncertainty, resulting from inherent randomness in the data-generating process, and epistemic (model) uncertainty, resulting from missing information in the model's training data. In large language models (LLMs) specifically, identifying sources of uncertainty is an important step toward improving reliability, trustworthiness, and interpretability, but remains an important open research question. In this paper, we introduce an uncertainty decomposition framework for LLMs, called input clarification ensembling, which can be applied to any pre-trained LLM. Our approach generates a set of clarifications for the input, feeds them into an LLM, and ensembles the corresponding predictions. We show that, when aleatoric uncertainty arises from ambiguity or under-specification in LLM inputs, this approach makes it possible to factor an (un-clarified) LLM's predictions into separate aleatoric and epistemic terms, using a decomposition similar to the one employed by Bayesian neural networks. Empirical evaluations demonstrate that input clarification ensembling provides accurate and reliable uncertainty quantification on several language processing tasks. Code and data are available at https://github.com/ UCSB-NLP-Chang/llm_uncertainty.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "With the widespread application of large language models (LLMs), it is becoming crucial to ensure predictions from LLMs are trustworthy. One critical dimension of trustworthiness is the ability to indicate when generated text is reliable 1 UC Santa Barbara 2 MIT-IBM Watson AI Lab, IBM Research 3 MIT CSAIL. Correspondence to: <PERSON><PERSON> <<EMAIL>>.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s). and correct, which may be formalized as the problem of uncertainty quantification (UQ). Uncertainty quantification aims to measure the confidence level of neural networks in their predictions (<PERSON><PERSON> et al., 2016; <PERSON><PERSON><PERSON> et al., 2021; Hüllermeier & Waegeman, 2021) . A higher uncertainty implies the output of LLMs should be clarified, manually evaluated, or rejected.", "cite_spans": [{"start": 61, "end": 103, "text": "Learning, Vienna, Austria. PMLR 235, 2024.", "ref_id": null}, {"start": 329, "end": 347, "text": "(<PERSON><PERSON> et al., 2016;", "ref_id": "BIBREF8"}, {"start": 348, "end": 367, "text": "<PERSON><PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF0"}, {"start": 368, "end": 397, "text": "Hüllermeier & Waegeman, 2021)", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Quantifying LLMs' total uncertainty has been the focus of increasing research attention. Existing work observes that LLMs are relatively well-calibrated, especially when predictions are obtained by ensembling multiple reasoning chains (<PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2023) or prompts (<PERSON> et al., 2023) , or when LLMs are prompted to directly output their confidence levels (<PERSON><PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2023) . Many other methods have been proposed to quantify the uncertainty of LLMs (<PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON> & <PERSON>, 2023; <PERSON> et al., 2023) . Accurate quantification of the uncertainty can be used for various applications, such as out-of-distribution detection and misclassified data detection.", "cite_spans": [{"start": 235, "end": 254, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF55"}, {"start": 255, "end": 274, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF17"}, {"start": 275, "end": 291, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF51"}, {"start": 303, "end": 323, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF20"}, {"start": 395, "end": 418, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF22"}, {"start": 419, "end": 436, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF31"}, {"start": 437, "end": 455, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF54"}, {"start": 532, "end": 550, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF31"}, {"start": 551, "end": 569, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF56"}, {"start": 570, "end": 588, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF25"}, {"start": 589, "end": 606, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF32"}, {"start": 607, "end": 625, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF6"}, {"start": 626, "end": 645, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF18"}, {"start": 646, "end": 663, "text": "<PERSON> & Kim, 2023;", "ref_id": "BIBREF45"}, {"start": 664, "end": 681, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF47"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "However, measuring uncertainty is just the first step towards understanding uncertainty in LLM predictions. For many applications, it is necessary to distinguish between different types of uncertainty and decompose the source into these types, a problem we refer to as uncertainty decomposition.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "As discussed more formally below, it is always possible to decompose a predictive model's uncertainty into two components: aleatoric (data) uncertainty and epistemic (model) uncertainty. Epistemic uncertainty arises when correct outputs are predictable in principle, but models lack the knowledge required for prediction. For example, the question What is 2+3? requires the knowledge of algebraic operations. Without such knowledge, the uncertainty will be high. On the other hand, aleatoric uncertainty arises from ambiguity or inherent randomness in the data-generating process itself: in language processing applications, it may result from ambiguous questions (<PERSON> et al., 2020; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2023) and unclear task instructions (<PERSON><PERSON> et al., 2022) . In particular, an important source of aleatoric uncertainty is the input ambiguity. For example, the answer to the input question Who is the president of this country will have a high aleatoric uncertainty because it is ambiguous what country and time the question intends to query about. This paper aims to obtain a finer-grained uncertainty measurement by determining how much of the total uncertainty can be attributed to aleatoric uncertainty due to input ambiguity. Aleatoric uncertainty due to input ambiguity is irreducible no matter how well a model learns. For example, to answer the question Who is the president of this country?, without any context, the uncertainty would be high regardless of how well the LLM learns, because the question itself is ambiguous. Uncertainty decomposition provides important insights for users to improve the performance of LLM. If epistemic uncertainty is high, users could supply the model with adequate knowledge through model adaptation, in-context learning, etc.; if the aleatoric uncertainty is high, then users should modify the query to make it more concrete.", "cite_spans": [{"start": 664, "end": 682, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF39"}, {"start": 683, "end": 700, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF11"}, {"start": 701, "end": 719, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF26"}, {"start": 750, "end": 771, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF52"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Despite existing work aimed at quantifying total uncertainty in LLMs, decomposing this uncertainty for LLMs remains understudied. Existing methods for uncertainty decomposition in other models cannot be directly applied, due to the black-box nature of LLMs and the prohibitive cost of inference. For example, Bayesian Neural Networks (BNNs) (<PERSON>, 2012; <PERSON><PERSON><PERSON> et al., 2015; <PERSON>, 2011; Louizos & Welling, 2016; Hernández-Lobato & Adams, 2015; <PERSON><PERSON><PERSON> et al., 2017; <PERSON> et al., 2015) specify a prior distribution over the model parameters and approximate the posterior distribution given the training data. DEEP ENSEMBLES (<PERSON><PERSON><PERSON> et al., 2017; Fort et al., 2019) decompose the uncertainty by training different variants of models, e.g., with different random seeds, to with the proper scoring rules (e.g., negative log-likelihood loss for the classification task) in the target task and then ensembling them.", "cite_spans": [{"start": 341, "end": 353, "text": "(<PERSON>, 2012;", "ref_id": "BIBREF41"}, {"start": 354, "end": 376, "text": "<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF1"}, {"start": 377, "end": 390, "text": "Graves, 2011;", "ref_id": "BIBREF10"}, {"start": 391, "end": 415, "text": "Louizos & Welling, 2016;", "ref_id": "BIBREF34"}, {"start": 416, "end": 447, "text": "Hernández-Lobato & Adams, 2015;", "ref_id": "BIBREF15"}, {"start": 448, "end": 473, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF12"}, {"start": 474, "end": 490, "text": "<PERSON> et al., 2015)", "ref_id": "BIBREF30"}, {"start": 629, "end": 660, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF29"}, {"start": 661, "end": 679, "text": "<PERSON> et al., 2019)", "ref_id": "BIBREF7"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Despite their effectiveness, Bayesian Neural Networks require substantial modifications to the training procedure, while DEEP ENSEMBLES necessitate training multiple variants of LLMs. Both approaches are generally impractical or prohibitively expensive. Given these challenges, we aim to address the following question: How can we effectively quantify and decompose uncertainty in LLMs?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In this paper, we propose a framework for uncertainty decomposition that we call input clarification ensembling. Our approach shares many intuitions and structural similarities with BNN-based approaches, but avoid the need to modify LLM parameters or inference procedures. Our approach is motivated by the observation that, although it is very challenging to modify LLM's parameters, it is relatively easy to manipulate the input to LLMs. Inspired by this, rather than ensembling different model variants that minimize the epistemic uncertainty, we introduce a set of input clarifications which can minimize the aleatoric uncertainty. We then ensemble an LLM's predictions under different clarifications. Figure 1 shows the general pipeline. For example, for the question Who is the president of this country?, a possible clarification is 'This country' refers to the US. By ruling out the aleatoric uncertainty by clarification, we can ascribe the remaining uncertainty of each individual prediction to epistemic uncertainty. Furthermore, by measuring the disagreement of the model predictions under different clarifications, we can gauge the aleatoric uncertainty. Our experiments verify that the proposed method provide accurate uncertainty quantification results on both total uncertainty and its decomposition.", "cite_spans": [], "ref_spans": [{"start": 712, "end": 713, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Uncertainty quantification. Uncertainty quantification for machine learning models has been widely studied to quantify the reliability of model predictions (<PERSON><PERSON> et al., 2016; <PERSON><PERSON> & <PERSON>, 2016; <PERSON>nin & Gales, 2018; <PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON> et al., 2020; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2023) . Uncertainty in model predictions can have numerous causes. Given the total uncertainty in model predictions, one can further decompose it into epistemic uncertainty (due to lack of knowledge in the model) and aleatoric uncertainty (due to the inherent randomness and noise in data).", "cite_spans": [{"start": 156, "end": 174, "text": "(<PERSON><PERSON> et al., 2016;", "ref_id": "BIBREF8"}, {"start": 175, "end": 198, "text": "<PERSON><PERSON> <PERSON>, 2016;", "ref_id": "BIBREF8"}, {"start": 199, "end": 221, "text": "Malinin & Gales, 2018;", "ref_id": "BIBREF35"}, {"start": 222, "end": 242, "text": "<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF44"}, {"start": 243, "end": 264, "text": "<PERSON><PERSON> et al., 2020;", "ref_id": null}, {"start": 265, "end": 282, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF31"}, {"start": 283, "end": 301, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF25"}, {"start": 302, "end": 319, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF32"}, {"start": 320, "end": 338, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Depending on how the uncertainty is obtained, existing uncertainty quantification methods can be categorized into intrinsic and extrinsic methods. Intrinsic methods adopt machine learning models to provide an inherent uncertainty estimate, such as Bayesian approaches and ensemblebased approaches (<PERSON><PERSON> & Gales, 2018) . Bayesian approaches (<PERSON><PERSON><PERSON> et al., 2015; <PERSON><PERSON> <PERSON>, 2016; <PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON> et al., 2020; <PERSON> et al., 2020) and ensemble-based approaches (<PERSON><PERSON><PERSON><PERSON> et al., 2017; Fort et al., 2019) can quantify both aleatoric and epistemic uncertainty. In comparison, extrinsic methods quantify the uncertainty in a post-hoc manner using auxiliary models (<PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2022) . Our method belongs to the intrinsic family of methods and is directly motivated by Bayesian neural network approaches.", "cite_spans": [{"start": 297, "end": 320, "text": "(Malinin & Gales, 2018)", "ref_id": "BIBREF35"}, {"start": 343, "end": 366, "text": "(<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF1"}, {"start": 367, "end": 390, "text": "<PERSON><PERSON> <PERSON>, 2016;", "ref_id": "BIBREF8"}, {"start": 391, "end": 409, "text": "<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF53"}, {"start": 410, "end": 430, "text": "<PERSON><PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF40"}, {"start": 431, "end": 461, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF29"}, {"start": 462, "end": 483, "text": "<PERSON><PERSON> et al., 2020;", "ref_id": null}, {"start": 484, "end": 500, "text": "He et al., 2020)", "ref_id": "BIBREF13"}, {"start": 531, "end": 562, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF29"}, {"start": 563, "end": 581, "text": "<PERSON> et al., 2019)", "ref_id": "BIBREF7"}, {"start": 739, "end": 763, "text": "(<PERSON><PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF24"}, {"start": 764, "end": 784, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "LLMs With the wide application of LLMs, how to accurately quantify the predictive uncertainty has also drawn attention (<PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON> & <PERSON>, 2023; <PERSON><PERSON> et al., 2018; Malinin & Gales, 2020) . Semantic Uncertainty (<PERSON> et al., 2022) clusters string-valued LLM outputs by synonymy for better uncertainty quantification. <PERSON> et al. (2023) explores uncertainty quantification within the challenging black-box context, where the token generation probability is inaccessible. In this pursuit, BSDETECTOR (Chen & Mueller, 2023) combines two strategies to estimate the model's predictive uncertainty. The first approach involves sampling multiple answers from the LLM and assessing their consistency, while the second directly queries the LLM for its confidence in the generated answer. Although there have been some explorations in this direction, existing methods can only estimate the total uncertainty. In comparison, we propose a more principled framework that can both quantify the total uncertainty and decompose it into aleatoric uncertainty and epistemic uncertainty, leading to a more fine-grained understanding of LLMs.", "cite_spans": [{"start": 119, "end": 138, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF56"}, {"start": 139, "end": 156, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF31"}, {"start": 157, "end": 177, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF38"}, {"start": 178, "end": 196, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF58"}, {"start": 197, "end": 216, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF18"}, {"start": 217, "end": 235, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF6"}, {"start": 236, "end": 257, "text": "Chen & Mueller, 2023;", "ref_id": "BIBREF2"}, {"start": 258, "end": 275, "text": "<PERSON><PERSON> et al., 2018;", "ref_id": null}, {"start": 276, "end": 298, "text": "Malinin & Gales, 2020)", "ref_id": "BIBREF36"}, {"start": 322, "end": 341, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF25"}, {"start": 428, "end": 445, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF32"}, {"start": 608, "end": 630, "text": "(Chen & Mueller, 2023)", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "Uncertainty Quantification and Model Calibration for", "sec_num": null}, {"text": "Another line of research is model calibration for LLMs. Model calibration is the process of ensuring that the predicted probabilities or confidence scores from a machine learning model align with the true probabilities or likelihoods of events occurring (i.e., the prediction is correct). Well-calibrated model predictions help improve the reliability of uncertainty quantification. Using existing model calibration methods (<PERSON><PERSON><PERSON><PERSON> & Gimpel, 2016; <PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON><PERSON> et al., 2018; Desai & Durrett, 2020) , prior work (<PERSON> et al., 2022; <PERSON> et al., 2023; 2021; <PERSON> <PERSON>, 2022) has shown that LLMs are relatively well-calibrated on factual QA and complex reasoning tasks when properly prompted. Specifically, <PERSON><PERSON><PERSON><PERSON> et al. (2022) ; <PERSON><PERSON> et al. (2023) estimate the prediction confidence of LLMs by prompting LLMs to output their confidence of their answers. For complex reasoning tasks, LLMs may output both the reasoning chains and the final answer. To estimate the confidence score, previous approaches (<PERSON> et al., 2022) sample multiple outputs for the input question and use the answer frequency to indicate the confidence. Researchers further ensemble multiple prompts for better calibration performance (<PERSON> et al., 2023) . Our uncertainty quantification is based on the well-calibrated predictions of LLMs, which lead to a more precise and accurate quantification result.", "cite_spans": [{"start": 424, "end": 450, "text": "(Hendrycks & Gimpel, 2016;", "ref_id": "BIBREF14"}, {"start": 451, "end": 468, "text": "<PERSON> et al., 2017;", "ref_id": "BIBREF11"}, {"start": 469, "end": 489, "text": "<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF44"}, {"start": 490, "end": 512, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF48"}, {"start": 513, "end": 535, "text": "Desai & Durrett, 2020)", "ref_id": "BIBREF5"}, {"start": 549, "end": 569, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF17"}, {"start": 570, "end": 589, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF20"}, {"start": 590, "end": 595, "text": "2021;", "ref_id": null}, {"start": 596, "end": 615, "text": "Ye & Durrett, 2022)", "ref_id": "BIBREF57"}, {"start": 747, "end": 769, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF22"}, {"start": 772, "end": 790, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF54"}, {"start": 1044, "end": 1064, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF17"}, {"start": 1250, "end": 1270, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "Uncertainty Quantification and Model Calibration for", "sec_num": null}, {"text": "Modeling Ambiguity with language models Ambiguity is a longstanding issue in the NLP domain, extensively explored in tasks such as syntactic and semantic parsing (<PERSON> et al., 2008) , open-domain questionanswering (<PERSON> et al., 2020; <PERSON> et al., 2023) , conversational question-answering (<PERSON> et al., 2021) and natural language inference (<PERSON> et al., 2023) . Prior work, such as AmbigQA (<PERSON> et al., 2020) and AmbigEnt (<PERSON> et al., 2023) , have identified the widespread ambiguities and established benchmarks with ambiguous inputs. These studies have demonstrated that existing language models lack the capability to effectively recognize and manage ambiguities. Our work models the ambiguity from the perspective of uncertainty quantification, where a high aleatoric uncertainty can indicate the potential existence of input ambiguity. By decomposing the aleatoric uncertainty, we show that it is possible to enhance the ambiguity detection performance of existing LLMs.", "cite_spans": [{"start": 162, "end": 183, "text": "(<PERSON> et al., 2008)", "ref_id": "BIBREF23"}, {"start": 216, "end": 234, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF39"}, {"start": 235, "end": 253, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF4"}, {"start": 290, "end": 308, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF11"}, {"start": 340, "end": 358, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF33"}, {"start": 389, "end": 407, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF39"}, {"start": 421, "end": 439, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Uncertainty Quantification and Model Calibration for", "sec_num": null}, {"text": "Denote by X and Y the input and output target of a given task and θ as the parameters of an LLM. Denote by p(Y |X) and q(Y |X, θ) the ground-truth and predicted distribution of Y given X.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Notations and Problem Formulation", "sec_num": "3.1."}, {"text": "We first introduce three uncertainty concepts. First, the total uncertainty is defined as the entropy of the predicted distribution, i.e., U total = H(q(Y |X; θ)). If the overall uncertainty is high, then it means the LLM has low confidence in its output. The total uncertainty can be further decomposed into two different types of uncertainties.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Notations and Problem Formulation", "sec_num": "3.1."}, {"text": "The first type of uncertainty is referred to as the epistemic uncertainty, which characterizes how well the LLM approaches the ground truth distribution, and thus learns the knowledge therein. For example, to answer 'What is 2+3?', if the LLM were able to learn the true knowledge of the algebraic operation, it would be able to answer with certainty; otherwise, the uncertainty would be high.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Notations and Problem Formulation", "sec_num": "3.1."}, {"text": "The second type of uncertainty is referred to as the aleatoric uncertainty, which characterizes the fundamental uncertainty residing in the ground-truth distribution, and is irreducible no matter how well the LLM learns. For example, to answer 'Who is the president of this country?', even if the LLM were well acquainted with politics, it still could not answer it confidently, because this question is inherently ambiguous. The data aleatoric is often quantified by the entropy in the ground-truth distribution, i.e., H(p(Y |X)).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Notations and Problem Formulation", "sec_num": "3.1."}, {"text": "The goal of this paper is to estimate both the epistemic and aleatoric uncertainties in LLMs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Notations and Problem Formulation", "sec_num": "3.1."}, {"text": "The possible solutions to our task is to apply the canonical Bayesian Neural Network (BNN) approach (<PERSON><PERSON><PERSON> et al., 2015; <PERSON>, 2011) or DEEP ENSEMBLES (<PERSON><PERSON><PERSON><PERSON> et al., 2017) , which are standard approaches to uncertainty decomposition. Instead of having one set of parameters, BNNs model the parameter distribution of a neural network. With the Bayesian formalism, the posterior distribution can be approximated give the training data via techniques such as variational inference (<PERSON><PERSON><PERSON> et al., 2015; <PERSON>, 2011) . Due to the prohibitive computational cost of BNNs, non-Bayesian methods such as DEEP ENSEMBLES are proposed with better scalability. DEEP ENSEMBLES maintain K models, each parameterized as θ (k) . Each of the k models seeks to minimize the training loss, usually the cross entropy loss for classification tasks, which is equivalent to solving the following optimization problem", "cite_spans": [{"start": 100, "end": 123, "text": "(<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF1"}, {"start": 124, "end": 137, "text": "Graves, 2011)", "ref_id": "BIBREF10"}, {"start": 156, "end": 187, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF29"}, {"start": 494, "end": 517, "text": "(<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF1"}, {"start": 518, "end": 531, "text": "Graves, 2011)", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "Background: Bayesian Neural Networks and DEEP ENSEMBLES", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "min θ KL(p(Y |X)∥q(Y |X, θ))).", "eq_num": "(1)"}], "section": "Background: Bayesian Neural Networks and DEEP ENSEMBLES", "sec_num": "3.2."}, {"text": "In DEEP ENSEMBLES, different models have slightly different initialization values and thus the optimized values, {θ ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Background: Bayesian Neural Networks and DEEP ENSEMBLES", "sec_num": "3.2."}, {"text": "Y |X) = E q(θ|D) [q(Y |X, θ)].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Background: Bayesian Neural Networks and DEEP ENSEMBLES", "sec_num": "3.2."}, {"text": "Then we can decompose the predictive uncertainty as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Background: Bayesian Neural Networks and DEEP ENSEMBLES", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "H(q(Y |X)) = I(Y ; θ|X) ① + E q(θ|D) H(q(Y |X, θ)) ② ,", "eq_num": "(2)"}], "section": "Background: Bayesian Neural Networks and DEEP ENSEMBLES", "sec_num": "3.2."}, {"text": "where I denotes the mutual information under the q distribution. ① measures the disagreement among the different models; ② measures the average uncertainty of each individual model. The above equation can be straightforwardly derived from the definition of conditional mutual information. Under certain assumptions, ① and ② can approximate the epistemic and aleatoric uncertainties, respectively (<PERSON><PERSON> et al., 2016) . An illustration of the DEEP ENSEMBLES framework is shown in the upper panel of Figure 1 .", "cite_spans": [{"start": 396, "end": 414, "text": "(<PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF8"}], "ref_spans": [{"start": 503, "end": 504, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Background: Bayesian Neural Networks and DEEP ENSEMBLES", "sec_num": "3.2."}, {"text": "Here is an intuitive explanation of why this is the case. According to Eq. 1, the goal of each model is to approach the ground-truth distribution, and thus can be viewed as the process of reducing the epistemic uncertainty. Therefore, if the optimization is successful, all the models will learn the true distribution, i.e., q(Y |X, θ (k) ) = p(Y |X), ∀k, which, by definition, results in zero epistemic uncertainty. Meanwhile, ① will also be zero because all the models produce the same prediction. Thus ① equals epistemic uncertainty in this case. ② would also equal the aleatoric uncertainty because the predicted distribution is equal to the true distribution.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Background: Bayesian Neural Networks and DEEP ENSEMBLES", "sec_num": "3.2."}, {"text": "On the other hand, if the models fail to learn the true distribution, in which case the epistemic uncertainty will be large, ① will also be large since different models have different hyperparameter settings and will be stuck in very different local optima.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Background: Bayesian Neural Networks and DEEP ENSEMBLES", "sec_num": "3.2."}, {"text": "Our goal of decomposing uncertainty for LLMs would be easily achieved if these methods were readily applicable to LLMs. Unfortunately, this is not the case. For BNNs, we need to significantly modify the training method of LLMs. For DEEP ENSEMBLES, the learning process in Eq. 1 is also very challenging for LLMs. Specifically, there are two types of methods for adapting LLMs to a particular task, supervised fine-tuning and prompting/in-context learning.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Do BNN and DEEP ENSEMBLES work for LLMs?", "sec_num": "3.3."}, {"text": "Directly fine-tuning the model according to Eq. 1 is usually infeasible due to the limited access to model parameters and its huge requirement for computation. Even if it is feasible, it would be very time-consuming because it requires finetuning multiple LLMs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Do BNN and DEEP ENSEMBLES work for LLMs?", "sec_num": "3.3."}, {"text": "On the other hand, the in-context learning method, though feasible, does not fit into the DEEP ENSEMBLES framework because it does not directly aim to optimize Eq. 1, so the decomposition will be very inaccurate. To demonstrate this, we perform a simple experiment on the AmbigQA (Min et al., 2020) dataset, which contains both ambiguous questions with multiple answers and unambiguous questions.", "cite_spans": [{"start": 280, "end": 298, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF39"}], "ref_spans": [], "eq_spans": [], "section": "Do BNN and DEEP ENSEMBLES work for LLMs?", "sec_num": "3.3."}, {"text": "We use the BNN method to decompose the uncertainty of ChatGPT, where the different individual model is derived by providing different in-context examples. If the decomposition method is accurate, we would expect to see that the aleatoric uncertainty for the ambiguous questions is significantly larger than that of the unambiguous ones. However, as shown in Figure 2 , the gap between the uncertainties of the two groups of questions is very small. More experiment details can be found in Section 4.", "cite_spans": [], "ref_spans": [{"start": 365, "end": 366, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Do BNN and DEEP ENSEMBLES work for LLMs?", "sec_num": "3.3."}, {"text": "While the BNN and DEEP ENSEMBLE framework do not work for LLMs, it inspires us to design an alternative framework that is almost completely symmetrical to the BNN approach, as discussed in the next subsection.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Do BNN and DEEP ENSEMBLES work for LLMs?", "sec_num": "3.3."}, {"text": "Although modifying or adapting LLMs is challenging, it is relatively straightforward to modify the input to LLMs. different models that minimize epistemic uncertainty (Eq. 1), can we design a framework that ensembles different inputs to minimize aleatoric uncertainty?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "This is the motivation behind our framework, which consists of the following two steps.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "Step 1: Input Clarification. Given an input X, we first generate a set of texts, C (k) , called clarifications. Each clarification C (k) seeks to minimize the ambiguity in X (and thus the aleatoric uncertainty) when appended to X. Formally, we denote one clarification result as X ⊕ C k , where ⊕ denotes concatenation. In the aforementioned example, 'Who is the president of this country?', possible clarifications include 'This country refers to the US.' and many other countries. Since there can be many valid clarifications for the input, {C (k) } is a set.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "Step 2: Ensemble. We denote the distribution of the aforementioned input clarifications as q(C|X) given a particular input X. Then, we define the predictive model q(Y |X) as an ensemble of predictions conditional on each clarified input, i.e., q(Y |X)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "= E q(C|X) [q(Y |X ⊕ C, θ)].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "(Model parameters θ are kept constant, and thus will be omitted for brevity below.)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "We then propose to decompose the uncertainty of the ensembled model as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "H(q(Y |X)) = I(Y ; C|X) ① ′ + E q(C|X) H(q(Y |X ⊕ C)) ② ′ .", "eq_num": "(3)"}], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "We claim that ① ′ , which computes the mutual information between the model output distribution and the clarifications, can approximate the aleatoric uncertainty caused by input ambiguity. In contrast, ② ′ is the average entropy of the output distribution given different clarifications, representing the model's uncertainty across clarified versions of the input. Assuming that input ambiguity is the sole source of aleatoric uncertainty, we may consider it as an estimate of the epistemic uncertainty for the LLM given the original input. This interpretation, however, diverges from the traditional definition of epistemic uncertainty, and we mainly focus on decomposing the aleatoric uncertainty (① ′ ) in this paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "By comparing the above process against Eqs. 1 and 2, we can notice the symmetry between our framework and DEEP ENSEMBLES's -DEEP ENSEMBLES seeks to pin down epistemic uncertainty whereas ours aleatoric uncertainty; Eq. 3 takes almost an identical form to Eq. 2 but the corresponding uncertainties are swapped. Figure 1 also shows such symmetry.", "cite_spans": [], "ref_spans": [{"start": 317, "end": 318, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "Accordingly, the same explanation of why it works applies here. When the input is already very clear, and hence aleatoric uncertainty is low, the clarifications will be identically empty, so ① ′ will approach zero. When the input is very ambiguous, the clarifications will be very different (think about the aforementioned president example), and so would the answers produced with different clarifications.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "In this case, ① ′ will be very high. On the other hand, ② ′ measures the average uncertainty on clarified input, which rules out most of the aleatoric uncertainty, so the remaining uncertainty can mostly be ascribed to epistemic uncertainty.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification Ensembling", "sec_num": "3.4."}, {"text": "Unlike the conventional neural networks, the input to LLMs usually contains multiple components, including instructions, in-context examples, questions etc. Therefore, we can separately measure the aleatoric uncertainties caused by different input components by clarifying only the corresponding components. For example, to measure the aleatoric uncertainty resulting from ambiguous instructions, we can clarify only the instruction. For the aleatoric uncertainty studied in this work, we will focus on the uncertainty caused by instruction ambiguity and question ambiguity, but the framework is readily generalizable to other input components.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification", "sec_num": "3.5."}, {"text": "To derive clarifications that approximately minimize the ambiguity in step 1 above, we introduce a clarification LLM, where we provide an instruction and in-context example to guide the LLM to perform adequate clarification. Therefore, the above input clarification distribution q(C|X) in Equation 3 is the output distribution of the clarification LLM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification", "sec_num": "3.5."}, {"text": "Note that the clarification LLM can be different from the LLM for prediction. In this work, we propose the following design choices for the clarification LLM to to ensure the quality of clarification:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification", "sec_num": "3.5."}, {"text": "• Prompting an LLM with task instructions and in-context examples. We design instructions for the clarification generation task and provide the model (gpt-3.5-turbo and gpt-4) with several in-context examples.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification", "sec_num": "3.5."}, {"text": "• Supervised fine-tuning. We can also fine-tune a opensourced language model on datasets that contains the ambiguous inputs and their corresponding clarifications. We fine-tune the Llama-3-8b-instruct model on the training set of the AmbigQA (Min et al., 2020) dataset.", "cite_spans": [{"start": 242, "end": 260, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF39"}], "ref_spans": [], "eq_spans": [], "section": "Input Clarification", "sec_num": "3.5."}, {"text": "Further implementation details are provided in Section 4 and Appendix A.2. For both design choices, we show that we can easily adapt the LLMs for clarification generation and quantify the uncertainty using our proposed framework.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Input Clarification", "sec_num": "3.5."}, {"text": "Our framework not only provides a way of decomposing the uncertainties, but can also enable an interpretable and effective human-LLM interaction experience. Currently, one of the major ways for humans to interact with LLMs is designing appropriate input. However, the input designed by humans may not be clear enough to LLMs, often resulting in undesirable answers given by LLMs. With the proposed input clarification framework, we can design an interaction paradigm that alleviates this problem.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Improving Performance via Soliciting Clarifications", "sec_num": "3.6."}, {"text": "Given an input query, we can first gauge the uncertainties of different input components. If one of the components, say the instruction, contributes to high uncertainty (exceeding a threshold), we can provide feedback to the user that the LLM is not sure about the answer because the instruction is ambiguous, along with several clarification options produced by the clarification LLM for the user to choose from. This would help the user to perform directed improvement of the input query and obtain the desirable answer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Improving Performance via Soliciting Clarifications", "sec_num": "3.6."}, {"text": "In this section, we conduct empirical evaluations to demonstrate the validity and effectiveness of the proposed method. Specifically, we aims to answer the following two questions:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4."}, {"text": "1. Can the proposed UQ framework quantify total uncertainty effectively and correctly?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4."}, {"text": "2. Can the proposed UQ framework decompose the uncertainty effectively and correctly?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4."}, {"text": "To answer the first question, we conduct the mistake detection experiment, which will be introduced in Section 4.2. To answer the second question, we conduct three experiments: ambiguity detection, monotonicity check, and recall of correct answers, which will be presented in Sections 4.3-4.5, respectively.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4."}, {"text": "We use gpt-3.5-turbo-0613 as the default LLM for all experiments. We sample 10 model predictions with tem-perature 0.5 and use the answer frequency to estimate the output distribution. Since all the datasets we use are openended generation tasks, different generated answers could have the exactly same meaning. For example, to answer the question 'When did the world's population reach 7 billion?', the LLM may generate several different answers such as 'December 2017' and 'The world's population reached 7 billion in December 2017', which are essentially the same meaning. Regarding these two answers as distinct answers can lead to an overestimation of the entropy of output distribution. Previous work (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2023) uses a natural language inference model to cluster different generated sequences with the same semantic meanings into one group for better output distribution estimation. We empirically find that LLMs can achieve better clustering performance. Therefore, we prompt the LLM to cluster output answers into different groups for output distribution estimation on question-answering datasets. More details about this process can be found in Appendix A.6.", "cite_spans": [{"start": 707, "end": 726, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF25"}, {"start": 727, "end": 744, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Experiment Configurations", "sec_num": "4.1."}, {"text": "For all the experiments, we introduce the following baselines: Semantic Uncertainty (<PERSON> et al., 2022) (denoted as SE) directly computes the entropy of the output distribution as the estimated (total) uncertainty (named semantic entropy in their paper). <PERSON><PERSON> et al. (2023) first queries the LLM for the answer and then queries the LLM again for the confidence of the correctness of the answer. We denote this method as ASK4CONF. We also slightly modify the prompt for the ambiguity detection task to query LLM for the confidence of the ambiguity of the input (denoted as ASK4CONF-D). The DEEP ENSEMBLES method (denoted as ENSEMBLES * for brevity) is implemented by ensembling the output distributions of multiple different in-context example sets (we use 5 different sets). We add * here since this method is different from standard DEEP ENSEMBLES and does not directly optimize Eq. 1. We provide more details of the prompts used in the experiments in Appendix A.4.", "cite_spans": [{"start": 84, "end": 103, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF25"}, {"start": 255, "end": 273, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF54"}], "ref_spans": [], "eq_spans": [], "section": "Experiment Configurations", "sec_num": "4.1."}, {"text": "Correctly quantifying the total uncertainty is the premise of correctly decomposing the uncertainty. If the estimated total uncertainty is inaccurate, so will the estimated aleatoric and epistemic uncertainty. A reliable total uncertainty measure should have a close correspondence to the model's prediction accuracy. For model predictions whose total uncertainty is high, the chances that the predictions are incorrect should also be high. Therefore, we will evaluate the total uncertainty quantification using the mistake detection task, following the previous work (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2023) .", "cite_spans": [{"start": 568, "end": 587, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF25"}, {"start": 588, "end": 605, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Quantifying Total Uncertainty", "sec_num": "4.2."}, {"text": "Evaluation Settings We evaluate the total uncertainty on the Natural Question (NQ) dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2019) and GSM8K (<PERSON> et al., 2021) . For each dataset, we randomly sample 200 examples from the validation set for evaluation. The total uncertainty on each example is used to predict whether the model's answer is correct. We report the area under the receiver operator characteristic curve (AUROC) as well as the best F1 score when using the total uncertainty to predict the correctness of the model answer. We use 5-shot in-context examples on the NQ dataset and 2-shot on the GSM8K dataset with chain-ofthoughts. We prompt the LLM to rephrase the input question to generate the clarification set. The detailed prompts are listed in Appendix A.4.", "cite_spans": [{"start": 91, "end": 117, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF27"}, {"start": 128, "end": 148, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Quantifying Total Uncertainty", "sec_num": "4.2."}, {"text": "The experiment results are shown in Table 1 , which confirms that the total uncertainty measured by the proposed approach is reliable. Specifically, we highlight the following observations. First, our method achieves comparable uncertainty quantification performance compared to the baselines, achieving a similar AUROC and F1 score. Second, as the proposed method shares a symmetry form with the DEEP ENSEMBLES method, one would expect the total uncertainty quantification of the two should be similar. The above experimental results verify that the quantification results of these two methods are very close. Third, although ASK4CONF performs well on factual QA tasks, it provides a poor uncertainty estimation for the complex reasoning task (GSM8K), while our method can still provide good mistake detection performance.", "cite_spans": [], "ref_spans": [{"start": 42, "end": 43, "text": "1", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Results", "sec_num": null}, {"text": "Now we can proceed to evaluate whether the decomposed uncertainty is reliable. As discussed, one of the main causes of aleatoric uncertainty is the ambiguity of the input. Therefore, we will test how well the measured aleatoric uncertainty is predictive of whether an input is ambiguous. In particular, we focus on two input components, the instruction and the question, and separately predict the ambiguity within each component using the respective aleatoric uncertainty (see Section 3.5).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Uncertainty Decomposition", "sec_num": "4.3."}, {"text": "Datasets For ambiguity detection of the question, we select the AmbigQA dataset (<PERSON> et al., 2020) , which has annotations on the ambiguity of questions. The questions in AmbigQA are extracted from the NQ dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2019) . For ambiguity detection of the instruction, since there is no existing dataset, we create a dataset, AmbigInst, where we generate ambiguous instructions, their disambiguation, and the input-output pairs using Chat-GPT. Each instruction is paired with around 15 questions. Since the focus of AmbigInst is to detect ambiguous instructions, we do not introduce ambiguity to the paired questions. More details about AmbigInst can be found in Appendix B. We use the full AmbigInst dataset and randomly sample 200 examples from the validation set of AmbigQA for evaluation.", "cite_spans": [{"start": 80, "end": 98, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF39"}, {"start": 213, "end": 239, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Uncertainty Decomposition", "sec_num": "4.3."}, {"text": "Evaluation Settings We use 5-shot in-context examples on the AmbigQA dataset similar to the experiment on the NQ dataset. Since the questions in AmbigInst are relatively easy and straightforward, we directly prompt LLMs in a zero-shot setting. For ambiguous question detection, we perform clarifications on the input question only. We evaluate two clarification LLMs on the AmbigQA dataset, including the Llama-3-8B-Instruct fine-tuned on the training set of AmbigQA and gpt-4. we retrieve the most similar 16 questions as in-context examples when prompting the gpt-4 to generate clarifications for a particular input question. The similarity between two questions is measured by the cosine similarity of their sentence embeddings from SENTENCE-BERT1 (Reime<PERSON> & Gurevych, 2019) . For the AmbigInst dataset, we directly prompt gpt-3.5-turbo-0613 to generate instruction clarifications (See Appendix A.4 for more details). We also include the performance of our method when using ground-truth disambiguation from the two datasets for reference (denoted as OURS * ).", "cite_spans": [{"start": 751, "end": 777, "text": "(Reimers & Gurevych, 2019)", "ref_id": "BIBREF46"}], "ref_spans": [], "eq_spans": [], "section": "Uncertainty Decomposition", "sec_num": "4.3."}, {"text": "The baselines are similar to the methods in the mistake detection task. The main difference is we use the quantified uncertainty to predict whether the input contains ambiguity. The total uncertainty for SE is used for ambiguity prediction in this task. Also, we test both the aleatoric uncertainty and total uncertainty quantified by the DEEP ENSEMBLES method, denoted by ENSEMBLES * (aleatoric) and ENSEM-BLES * (total) respectively. For our method, we use the aleatoric uncertainty for ambiguity prediction. DEEP EN-SEMBLES is not included on the AmbigInst dataset since we do not include in-context examples on that dataset. We also incorporate results with additional methods from previous work (<PERSON> et al., 2022; <PERSON> et al., 2023) in Appendix A.1. Results The experiment results are shown in Table 2 . We emphasize two observations. First, our method achieves the best ambiguity detection performance and significantly outperforms the baselines. Note that all the baselines, except for ENSEMBLES * (aleatoric), use the total uncertainty for ambiguity detection, and thus could not disentangle epistemic uncertainty from the aleatoric uncertainty. Therefore, these results verify the importance of uncertainty decomposition. Second, even a small model can also be efficiently adapted for the clarification generation task. When using the fine-tuned LLaMA model, Our method can still outperform baselines significantly. This adaptation process was remarkably efficient, requiring less than 10 minutes on 4×80G H100 GPUs. Third, the DEEP ENSEMBLES * (aleatoric) method is not effective in the black-box LLM setting. As we have discussed in Section 3.3, simply varying the incontext examples cannot accurately estimate the parameter posterior distribution, while the proposed framework is specially designed for the black-box LLMs.", "cite_spans": [{"start": 700, "end": 717, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF50"}, {"start": 718, "end": 736, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF4"}], "ref_spans": [{"start": 804, "end": 805, "text": "2", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Uncertainty Decomposition", "sec_num": "4.3."}, {"text": "Another observation is that ambiguity detection performance varies across different datasets. On the AmbigQA dataset (<PERSON> et al., 2020) , the ambiguities are more implicit and hard to find by the clarification models, which makes the detection performance relatively low (although still higher than baselines significantly). <PERSON> et al. (2020) also note that the ambiguity in the dataset is \"sometimes subtle\" and \"many (ambiguities) are only apparent after examining one or more Wikipedia pages\". In comparison, on the AmbigInst dataset where we design ambiguities to be very explicit (see Appendix B for more examples), the clarification model can generate effective clarifications for most cases, leading to a good detection performance. Finally, the performance of our method can be further improved when using with the ground-truth disambiguation from the two datasets as the input clarifications, demonstrating that the clarification model is still worth exploring. ", "cite_spans": [{"start": 117, "end": 135, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF39"}, {"start": 325, "end": 342, "text": "<PERSON> et al. (2020)", "ref_id": "BIBREF39"}], "ref_spans": [], "eq_spans": [], "section": "Uncertainty Decomposition", "sec_num": "4.3."}, {"text": "We further present a visual representation of various uncertainty quantification results in Figure 3 . These examples have been grouped according to the levels of aleatoric and epistemic uncertainty the LLM exhibits. Our uncertainty quantification framework enables a clear understanding of the sources of uncertainty in each example. For instance, consider the question \"What is the lowest # on the FM dial\" from the AmbigQA dataset. This question lacks specificity regarding the country and region, leading to ambiguity in the input. Our uncertainty quantification illustrates that the predominant source of uncertainty in the model's prediction stems from aleatoric uncertainty in this case. In contrast, despite a clear question, the model struggles to answer a query about the \"Royal Proclamation\" (as shown in the upper left example), resulting in a high level of epistemic uncertainty. Interestingly, we have identified a few examples within the GSM8K dataset where the uncertainty in the LLM prediction is attributed to data-related factors. For instance, the upper right example in the figure raises ambiguity about whether the word \"this\" refers solely to watching shows or encompasses both activities (the ground-truth annotation uses the second interpretation). More details about these examples can be found in Appendix A.5.", "cite_spans": [], "ref_spans": [{"start": 99, "end": 100, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Qualitative Results", "sec_num": null}, {"text": "To further evaluate the reliability of our aleatoric uncertainty measure, particularly the clarification module, we perform a monotonicity check experiment. Ideally, the clarified input should contribute to a much lower aleatoric uncertainty than the original ambiguous input. To test this, we perform two rounds of aleatoric uncertainty measuring. In the first round, we measure the aleatoric uncertainty by clarifying the original input segments (question or instruction). In the second round, we measure the aleatoric uncertainty of the clarified inputs obtained in the first round. Our goal is to check whether the aleatoric uncertainty measured in the second round is much smaller than that in the first round. This experiment is performed on the AmbigQA and AmbigInst datasets. In both rounds, we use the same clarification prompt to generate the clarifications.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Monotonicity Check", "sec_num": "4.4."}, {"text": "Figure 4 (a) visualizes the change in uncertainty on both datasets. As can be observed, the aleatoric uncertainty drops significantly after the input is clarified, which verifies the effectiveness of the clarification network.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "4", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Monotonicity Check", "sec_num": "4.4."}, {"text": "As discussed in Section 3.6, our framework can be used to improve the performance in the presence of ambiguous input by asking users to choose from a set of clarified versions of the input. To make this happen, our methods must be able to cover a good proportion of the possible answers resulting from different clarifications of a given ambiguous input. Also, the number of required clarifications should be smaller, as the users might not want to select the responses from a large set of choices.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Recall of Correct Answers", "sec_num": "4.5."}, {"text": "To test this, we use the ambiguous questions and instructions from AmbigQA and AmbigInst respectively. For each input, we collect all the possible labeled answers from the ground-truth annotations. Then we select one answer as the target answer that the user is asking for. In our pipeline, the LLM will generate multiple answers given the generated clarifications. Therefore, we inspect how well these generated answers cover the target answer given different numbers of clarifications. We separately compute the recall of the target answer with the different numbers of clarifications. As a baseline, we introduce a vanilla version, where we directly query the LLM with ambiguous input without any clarification.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Recall of Correct Answers", "sec_num": "4.5."}, {"text": "The results are illustrated in Figure 4 (b). We can consistently observe an increase of recall given more clarifications. Similar to the ambiguity detection performance, the recall improvement on the AmbigInst dataset is more significant compared to the AmbigQA dataset, which is due to the subtlety of the AmbigQA dataset as discussed. Nevertheless, the proposed clarification framework is able to significantly improve the answer recall over the vanilla version without the clarification.", "cite_spans": [], "ref_spans": [{"start": 38, "end": 39, "text": "4", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Recall of Correct Answers", "sec_num": "4.5."}, {"text": "In this paper, we focus on the uncertainty quantification of LLMs and propose a new framework for decomposing the uncertainty. With a symmetric structure of the BNN methods, our framework leverages input clarifications for uncertainty quantification, which is more suitable for blackbox LLMs. experimental results affirm that our proposed method not only yields reliable uncertainty quantification but also effectively decompose the total uncertainty into aleatoric and epistemic uncertainty. In the future, we will further explore how to build a more effective clarification module to boost the effectiveness of our method.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "5."}, {"text": "In this paper, our primary objective is to develop an innovative uncertainty quantification framework, which aims to empower users in pinpointing the sources of uncertainty in LLMs accurately. The principal application scenario for our approach is to improve the trustworthiness and reliability of LLMs. Consequently, the likelihood of unintended usage or potential risks arising from our proposed method is considerably reduced. We also assess both the evaluations and datasets to ensure they are devoid of any harmful content or adverse impacts.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Nonetheless, it's imperative to note that our method relies on the well-calibrated nature of LLMs when applied to factoid QA and mathematical reasoning tasks. A well-calibrated LLM helps improve the reliability of uncertainty quantification. There exists a possibility that LLMs may not exhibit the same level of calibration on particular downstream tasks, and we are committed to continually refining the calibration of LLMs to bolster the reliability and trustworthiness of <|begin_of_text|><|start_header_id|>user<|end_header_id|> In this task, you will receive a question that may contain ambiguities. First analyze the following aspects to find if there is any ambiguities according to the real-world facts: -entities, objects, or events has multiple references or interpretations -Unclear timestamps -Unclear locations -Unclear answer types (e.g., \"When\" refers to \"which year or what date\", and \"Who\" refers to \" which person or which team\") If there is any ambiguities, you need to remove ambiguities by adding some clarifications to the question. Each clarification is an additional condition or explanations to the concept in the question that resolve its ambiguity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "-You are only allowed to add conditions or explanations, and you cannot change the original intent or semantics of the question.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "-The conditions and explanations must be ground to real-word facts.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "If there is no ambiguities, you only need to output the original question as it is.<|eot_id|><| start_header_id|>assistant<|end_header_id|> Sure. Please provide me with the question so that I can identify whether it is ambiguous and clarify it.<|eot_id|><|start_header_id|>user<|end_header_id|>", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Original Question: {original_question}<|eot_id|><|start_header_id|>assistant<|end_header_id|> Question after adding condition: {ground_truth_clarification}<|eot_id|> Ambiguity detection For the mistake detection task, we use the total uncertainty for SEMANTIC UNCERTAINTY (<PERSON><PERSON> et al., 2022) , aleatoric uncertainty from BNN * , and the confidence score of the ambiguity from ASK4CONF (<PERSON><PERSON> et al., 2023) to predict whether the input is ambiguous or not. We slightly modify the prompt of ASK4CONF as follows:", "cite_spans": [{"start": 272, "end": 291, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF25"}, {"start": 385, "end": 404, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF54"}], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "A.4. Prompts for Our Clarification Model", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "We list the prompts we used for clarification generation on each dataset as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "• Input clarification prompt on Natural Question and GSM8K is shown in Figure 8 .", "cite_spans": [], "ref_spans": [{"start": 78, "end": 79, "text": "8", "ref_id": null}], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "• Input clarification prompt on AmbigQA is shown in Figure 9 .", "cite_spans": [], "ref_spans": [{"start": 59, "end": 60, "text": "9", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "• Input clarification prompt on AmbigInst is shown in Figure 10 .", "cite_spans": [], "ref_spans": [{"start": 61, "end": 63, "text": "10", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Due to space limit, we truncate the example from GSM8K visualized in Figure 3 . The whole question is: \"<PERSON> decides to do several activities while out on vacation. He spends 6 hours boating and half that time swimming. He also watched 3 different shows which were 2 hours each. This was 30% of the time he spent. He spent 40% of his time sightseeing. How much time did he spend sightseeing?\".", "cite_spans": [], "ref_spans": [{"start": 76, "end": 77, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "A.5. Details of the Qualitative Results", "sec_num": null}, {"text": "When generating the clarifications, we directly prompt the LLM to paraphrase the question with the following prompt: \"I am confused with the following question. Please paraphrase it so that it is easier to understand and solve.\" Then we sample 5 responses from the LLM as the clarifications for uncertainty quantification.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.5. Details of the Qualitative Results", "sec_num": null}, {"text": "As we have discussed in Section 4.1, different outputs generated by the LLM may have the same meaning in the free-form text generation setting. Unlike previous work (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2023) that map semantics-equivalent answers Given the collected ambiguous task descriptions and their clarifications, we then prompt the model to generate input-output pairs for each task. Specifically, 15 inputs are generated for each task, and each input is further paired with different output answers depending on the ground-truth clarifications. We additionally add a post-processing step where we filter out the inputs that have exactly the same answer given different clarifications. The final ambiguous instructions consist of 15 tasks with 214 input questions in total.", "cite_spans": [{"start": 165, "end": 184, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF25"}, {"start": 185, "end": 202, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "A.6. Prompt the LLM for Answer Extraction", "sec_num": null}, {"text": "We take 10 tasks from the Instruction induction dataset (<PERSON><PERSON> et al., 2022) as the unambiguous tasks, including letters list, first word letter, second word letter, orthography starts with, larger animal, singular to plural, diff, num to verbal, antonyms, and sum.", "cite_spans": [{"start": 56, "end": 79, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "A.6. Prompt the LLM for Answer Extraction", "sec_num": null}, {"text": "We manually add some clarifications to the 10 instructions to remove potential ambiguities. For example, given the original instruction \"Break the input word into letters, separated by spaces\", we clarify it with \"Write the inputted word with a space between each letter\", since \"separated by spaces\" might cause ambiguities of how many spaces should be added between two letters. Each task is also paired with 15 input-output pairs. Overall, the AmbigInst dataset contains 25 tasks and 364 different inputs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.6. Prompt the LLM for Answer Extraction", "sec_num": null}, {"text": "We list several examples from the synthetic dataset with ambiguous instructions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "▷ 1. Rearrange the objects on the table in ascending order.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "Input: The following table lists the objects on my desk: In what follows, you will be given some questions that might be ambiguous. These ambiguities can arise from various factors, including but not limited to:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "1. Ambiguous references to entities in the question. 2. Multiple properties of objects/entities in the question leading to different interpretations .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "3. Ambiguities due to unclear timestamps. 4. Ambiguities stemming from unclear locations. 5. Multiple valid answer types based on the question.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "For each question, you are to provide at least two distinct rephrasings that resolve these ambiguities. By \"rephrasing,\" we mean you should reformulate the question to be clear and direct, eliminating any possible ambiguity without altering the original intent of the question . You should not seek further information or produce a binary (yes-no) question as a result of the clarification. Instead, you must create a direct question (wh-question) that aims to obtain a specific answer. Input: The quick brown fox jumps over the lazy dog.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "▷ 4. Sort the names alphabetically.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "Input: <PERSON>, <PERSON>, <PERSON>, <PERSON>.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "▷ 5. Identify the subject in the sentence.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "Input: The CEO of the company gave a speech about the future of technology.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "▷ 6. Count the number of objects in the given list of objects.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "Input: Forks, <PERSON>poons, <PERSON>nives, Plates, Cups, Spoons, Forks, Spoons, Cups. In this task, you will receive both a question and multiple sentences. Each sentence contains an answer to the question. Your primary goal is to extract a concise answer, which can be a single word or a short phrase, from each sentence. Again, ensure you only extract a short answer! If a short answer cannot be directly extracted, then summarize the whole sentence into a single word or a short phrase.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "Additionally, while extracting answers, your secondary goal is to create an \"answer set\" that contains all distinct answers from previous questions. If the extracted answer has not appeared in the answer set, add it to the answer set.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "** Important Rules ** 1. If there is an answer in the answer set that is semantically equivalent to the extracted answer, use the answer from the answer set as the result. Do not introduce a new, slightly different answer. For example, if the answer set already contains \"the matrix (1999),\" and you extract an answer from a sentence like \"The popular movie in 1999... is the matrix,\" your extraction should be \"the matrix (1999)\" rather than \"the matrix.\"", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "2. Separate different answers in the answer set using \"|\".", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "3. Also, extract the answer as \"Unknown\" for the following cases:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "-The sentence claims that there is no answer to the question -The sentence claims it lacks sufficient information to answer the question -The sentence claims it depends on various factors and the answer cannot be determined Input : 12, 20, 35, 46, 52, 66, 74, 81 ▷ 12. Determine the square root of a number.", "cite_spans": [{"start": 230, "end": 262, "text": ": 12, 20, 35, 46, 52, 66, 74, 81", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "Input: 81 ▷ 13. Find the capital of a country.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "Input: South Africa ▷ 14. Classify a movie based on its rating.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "Input: The movie \"Toy Story 4\" has an MPAA rating of G, an IMDb rating of 7.8, and a Rotten Tomatoes rating of 97%.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Dataset Examples", "sec_num": null}, {"text": "We use the pre-trained sentence transformer model https://huggingface.co/sentence-transformers/all-mpnet-base-v2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "The work of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> was partially supported by National Science Foundation (NSF) Grant IIS-2207052, NSF Grant IIS-2302730, and CAHSI-Google Research Award.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgment", "sec_num": null}, {"text": "In this section, we include additional methods that estimate the model's confidence on its answer to study whether the confidence score can be used for ambiguity detection. Specifically, <PERSON> et al. (2022) uses a self-consistency frequency to estimate the LLM confidence (denoted as SELFCONSISTENCY), where multiple answers are sampled from the LLM given the original question and the highest answer frequency are used as the confidence score. In addition, <PERSON> et al. (2023) incorporate two features to estimates the LLM confidence: the answer repetition and the answer diversity. The answer repetition parallels the implementation of <PERSON> et al. (2022) , and the answer diversity is estimated by counting the number of unique answers from multiple sampled answers. We denote the two methods as SAMPLE REPETITION and SAMPLE DIVERSITY. We include these 3 confidence scores for ambiguity detection. We implement these methods as follows.For SELFCONSISTENCY, we use the default hyperparameters in the official implementation (temperature = 0.7, sample 10 answers from the model) to compute the confidence.When predict the input ambiguities, we use 1-confidence as the input, since a lower confidence implies either the model's answer is wrong or there are multiple valid answers (i.e., the input is ambiguous). For SAM-PLEDIVERSITY, we use the default hyperparameters in the official implementation (temperature = 0.5, sample 10 answers from the model). We computed the number of unique answers, using this metric to gauge the ambiguity of the input question. For SAMPLEREP-ETITION, this method parallels SELFCONSISTENCY, where answers are generated using greedy decoding and then re-sampled (temperature = 0.5, sample 10 answers) to assess the initial answer's confidence.The experiment results is as below:We visualize the results in Table 3 . These results underscore that model confidence alone is insufficient for accurately identifying ambiguous inputs, as evidenced by the AUROC scores of all three methods being under 60. We will also include these findings and citations in the final version of the paper.", "cite_spans": [{"start": 187, "end": 203, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF50"}, {"start": 455, "end": 473, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF4"}, {"start": 634, "end": 650, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF50"}], "ref_spans": [{"start": 1836, "end": 1837, "text": "3", "ref_id": null}], "eq_spans": [], "section": "A.1. Additional Results", "sec_num": null}, {"text": "We fine-tuning the Llama-3-8B-Instruction on the full training set of AmbigQA dataset on 4×NVIDIA H100 80GB HBM3 GPU. We organize the training data using the template in Figure 5 . We use PyTorch Lightning, DeepSpeed Stage 1, and flash-attention 2 to train the model. We train the model with batch size 16, learning rate 2e-5, and cosine learning rate scheduler for 5 epochs. The loss is only computed on the output tokens. We evaluate the model on the validation set and take the model that achieves lowest validation loss (epoch = 2) for testing.", "cite_spans": [], "ref_spans": [{"start": 177, "end": 178, "text": "5", "ref_id": null}], "eq_spans": [], "section": "A.2. Supervised Fine-tuning for Clarification Generation", "sec_num": null}, {"text": "Mistake detection For the mistake detection task, we strictly follow the experiment settings from <PERSON><PERSON> et al. (2022) and <PERSON> et al. (2023) . For each example, we estimate the output distribution and take the answer with the highest frequency as the final answer. Then we use the method (and the prompt) from <PERSON> et al. (2023) to determine whether the answer is correct by prompting ChatGPT. Based on the total uncertainty and correctness of the answer, we compute the AUROC and conduct a grid search to find the best threshold for the F1 score, where the correct answers are regarded as positive examples.For the implementation of ASK4CONF (T<PERSON> et al., 2023) in the mistake detection task, we use the \"Verb. 2S top-1\" method (and the corresponding prompts) to estimate the confidence of the language model. Rather than asking the LLM to directly generate an answer, we sample multiple answers and take the most frequent one as the answer. After that, we prompt the LLM for the confidence of the most frequent answer. The prompt we use is:Answer the following question. Question: {The testing question} Answer: {The most frequent answer} Provide the probability that your answer is correct. Give ONLY the probability, no other words or explanation.For example:Probability: <the probability between 0.0 and 1.0 that your solution is correct, without any extra commentary whatsoever; just the probability!> Figure 6 . The prompt for mistake detection (ASK4CONF).Read the following question: Question: {question} Provide the probability that this question is ambiguous due to factors such as ambiguous entities, ambiguous event references, or ambiguity over the answer type. Give ONLY the probability, no other words or explanation.For example:Probability: <the probability between 0.0 and 1.0 that the question is ambiguous (1.0 means the question is absolutely ambiguous), without any extra commentary whatsoever; just the probability!> into a unique set using the NLI models, we empirically find that the LLMs provide better performance on this task. The prompt we use is in Figure 11 .After we extract and cluster the semantically equivalent answers, we further post-process the answers as follows. First, the clarifications generated by the clarification LLM may be invalid and have no answers. In such cases, the LLM may refuse to respond to the question and reply with phrases like \"I'm sorry, but I couldn't find any information about the question\" or other similar replies. The answer extraction prompt in Figure 11 maps these answers to a special answer, \"Unknown.\" To ensure these answers are mapped to \"Unknown,\" we adopt a keyword-matching approach that defines a set of key phrases signaling a refusal to respond, such as \"I'm sorry,\" \"cannot be determined,\" and \"invalid question.\" Answers containing these key phrases are mapped to \"Unknown\". Second, if all answers to a particular clarification are mapped to \"Unknown\", we regard this clarification as invalid and directly drop it when ensembling the outputs for uncertainty quantification. Otherwise, the appearance of the answer \"Unknown\" indicates the model's insufficient knowledge regarding the question, contributing to the epistemic uncertainty. Therefore, when computing the frequency to estimate the output distribution, we count the occurrences of each unique answer, excluding the special \"Unknown\". Then, we normalize these counts by dividing each by the total number of answers. For every appearance of the special \"Unknown\", we increase the normalized frequencies of all other answers by 1 N , where N is the number of unique answers excluding the special answer. This adjustment ensures the special answer's impact is evenly distributed across the other answers and increases the epistemic uncertainty.", "cite_spans": [{"start": 98, "end": 116, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF25"}, {"start": 121, "end": 138, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF32"}, {"start": 308, "end": 325, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF32"}, {"start": 640, "end": 659, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF54"}], "ref_spans": [{"start": 1412, "end": 1413, "text": "6", "ref_id": null}, {"start": 2082, "end": 2084, "text": "11", "ref_id": null}, {"start": 2519, "end": 2521, "text": "11", "ref_id": null}], "eq_spans": [], "section": "A.3. Implementation details for baselines", "sec_num": null}, {"text": "We generate ambiguous instructions following the pipeline of SELF-INSTRUCTION (<PERSON> et al., 2022) . Specifically, we first query CHATGPT with several manually designed ambiguous task descriptions as in-context examples. For better verification of the ambiguity, we also prompt CHATGPT to output the cause of the ambiguity. Among the ambiguous ▷ 15. Select the longest sentence from the following choices, and output the sentence index.Input: The following sentences are listed: 1. To be, or not to be, that is the question.2. Whether 'tis nobler in the mind to suffer the slings and arrows of outrageous fortune.3. Or to take arms against a sea of troubles and by opposing end them.", "cite_spans": [{"start": 78, "end": 97, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF55"}], "ref_spans": [], "eq_spans": [], "section": "B.1. Dataset Creation", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Uncertainty as a form of transparency: Measuring, communicating, and using uncertainty", "authors": [{"first": "U", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "An<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": ["V"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Melanc ¸on", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "Tickoo", "suffix": ""}], "year": 2021, "venue": "Proceedings of the 2021 AAAI/ACM Conference on AI, Ethics, and Society", "volume": "", "issue": "", "pages": "401--413", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Uncertainty as a form of transparency: Measuring, communicating, and using uncertainty. In Proceedings of the 2021 AAAI/ACM Conference on AI, Ethics, and Society, pp. 401-413, 2021.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Weight uncertainty in neural network", "authors": [{"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Cornebise", "suffix": ""}, {"first": "K", "middle": [], "last": "Kavukcuoglu", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, D. Weight uncertainty in neural network. In International conference on machine learning, 2015.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Quantifying uncertainty in answers from any language model via intrinsic and extrinsic confidence assessment", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.16175"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. Quantifying uncertainty in answers from any language model via intrinsic and extrinsic con- fidence assessment. arXiv preprint arXiv:2308.16175, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Training verifiers to solve math word problems", "authors": [{"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Bavarian", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Jun", "suffix": ""}, {"first": "L", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Tworek", "suffix": ""}, {"first": "J", "middle": [], "last": "Hilton", "suffix": ""}, {"first": "R", "middle": [], "last": "Nakan<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2110.14168"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Training verifiers to solve math word problems. arXiv preprint arXiv:2110.14168, 2021.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Selectively answering ambiguous questions", "authors": [{"first": "J", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["M"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "The 2023 Conference on Empirical Methods in Natural Language Processing", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, B<PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Selectively answering am- biguous questions. In The 2023 Conference on Empirical Methods in Natural Language Processing, 2023.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Calibration of pre-trained transformers", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing (EMNLP)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> Cal<PERSON>ration of pre-trained trans- formers. In Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing (EMNLP), 2020.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Shifting attention to relevance", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Zavalny", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Towards the uncertainty estimation of large language models. arXiv", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> Shifting attention to relevance: Towards the uncertainty estimation of large language models. arXiv, 2023.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Deep ensembles: A loss landscape perspective", "authors": [{"first": "S", "middle": [], "last": "Fort", "suffix": ""}, {"first": "H", "middle": [], "last": "Hu", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1912.02757"]}, "num": null, "urls": [], "raw_text": "Fort, S., <PERSON>, <PERSON>, and <PERSON><PERSON>, B. <PERSON> en- sembles: A loss landscape perspective. arXiv preprint arXiv:1912.02757, 2019.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Dropout as a bayesian approximation: Representing model uncertainty in deep learning", "authors": [{"first": "Y", "middle": [], "last": "Gal", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "1050--1059", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Dropout as a bayesian approx- imation: Representing model uncertainty in deep learn- ing. In international conference on machine learning, pp. 1050-1059. PMLR, 2016.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Uncertainty in deep learning", "authors": [{"first": "Y", "middle": [], "last": "Gal", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> et al. Uncertainty in deep learning, 2016.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Practical variational inference for neural networks. Advances in neural information processing systems", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2011, "venue": "", "volume": "24", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> variational inference for neural net- works. Advances in neural information processing sys- tems, 24, 2011.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Abgcoqa: Clarifying ambiguity in conversational question answering", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Sun", "suffix": ""}, {"first": "K", "middle": ["Q"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "", "suffix": ""}], "year": 2017, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "1321--1330", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, K. <PERSON>. On calibration of modern neural networks. In International conference on machine learning, pp. 1321-1330. PMLR, 2017. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: Clarifying ambiguity in conversational question answering. In 3rd Conference on Automated Knowledge Base Construction, 2021.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Distributed bayesian learning with stochastic natural gradient expectation propagation and the posterior server", "authors": [{"first": "L", "middle": [], "last": "Hasenclever", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": ["W"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Journal of Machine Learning Research", "volume": "18", "issue": "106", "pages": "1--37", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, C., and <PERSON><PERSON>, Y<PERSON> <PERSON>. Distributed bayesian learning with stochastic natural gradient expec- tation propagation and the posterior server. Journal of Machine Learning Research, 18(106):1-37, 2017.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Bayesian deep ensembles via the neural tangent kernel", "authors": [{"first": "B", "middle": [], "last": "He", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": ["W"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "1010--1022", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> deep ensembles via the neural tangent kernel. Advances in neural information processing systems, 33:1010-1022, 2020.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "A baseline for detecting misclassified and out-of-distribution examples in neural networks", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1610.02136"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, K. <PERSON> baseline for detecting misclassified and out-of-distribution examples in neural networks. arXiv preprint arXiv:1610.02136, 2016.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Probabilistic backpropagation for scalable learning of bayesian neural networks", "authors": [{"first": "J", "middle": ["M"], "last": "Hernández<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "1861--1869", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>babilistic back- propagation for scalable learning of bayesian neural net- works. In International conference on machine learning, pp. 1861-1869. PMLR, 2015.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Instruction induction: From few examples to natural language task descriptions", "authors": [{"first": "O", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "U", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2205.10782"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Instruction induction: From few examples to natural language task descriptions. arXiv preprint arXiv:2205.10782, 2022.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Large language models can self-improve", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": ["S"], "last": "<PERSON>u", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Han", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.11610"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Large language models can self-improve. arXiv preprint arXiv:2210.11610, 2022.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Look before you leap: An exploratory study of uncertainty measurement for large language models", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Song", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Ma", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.10236"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Look before you leap: An exploratory study of uncertainty measurement for large language models. arXiv preprint arXiv:2307.10236, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Aleatoric and epistemic uncertainty in machine learning: An introduction to concepts and methods", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Machine Learning", "volume": "110", "issue": "", "pages": "457--506", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and epistemic uncertainty in machine learning: An introduction to con- cepts and methods. Machine Learning, 110:457-506, 2021.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Calibrating language models via augmented prompt ensembles", "authors": [{"first": "M", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>uan", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["B"], "last": "Grosse", "suffix": ""}, {"first": "J", "middle": [], "last": "Ba", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, S., <PERSON>, S., <PERSON>, R. B., and <PERSON>, J. Calibrating language models via aug- mented prompt ensembles. 2023.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "How can we know when language models know? on the calibration of language models for question answering", "authors": [{"first": "Z", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "G", "middle": [], "last": "Neubig", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. How can we know when language models know? on the calibration of language models for question answering. Transactions of the Association for Computational Linguistics, 2021.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Language models (mostly) know what they know", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Conerly", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Hatfield-<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Das<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2207.05221"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Language models (mostly) know what they know. arXiv preprint arXiv:2207.05221, 2022.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Regular tree grammars as a formalism for scope underspecification", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2008, "venue": "Proceedings of ACL-08: HLT", "volume": "", "issue": "", "pages": "218--226", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, S. Regular tree gram- mars as a formalism for scope underspecification. In Proceedings of ACL-08: HLT, pp. 218-226, 2008.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Learnable uncertainty under laplace approximations", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Hein", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Uncertainty in Artificial Intelligence", "volume": "", "issue": "", "pages": "344--353", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>ble uncer- tainty under laplace approximations. In Uncertainty in Artificial Intelligence, pp. 344-353. PMLR, 2021.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Semantic uncertainty: Linguistic invariances for uncertainty estimation in natural language generation", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Gal", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. <PERSON> uncertainty: Linguistic invariances for uncertainty estimation in nat- ural language generation. In The Eleventh International Conference on Learning Representations, 2022.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Clam: Selective clarification for ambiguous questions with generative language models", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Gal", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. <PERSON>: Selective clarifi- cation for ambiguous questions with generative language models. 2023.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Natural questions: a benchmark for question answering research", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Transactions of the Association for Computational Linguistics", "volume": "7", "issue": "", "pages": "452--466", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Natural questions: a benchmark for ques- tion answering research. Transactions of the Association for Computational Linguistics, 7:452-466, 2019.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Deup: Direct epistemic uncertainty prediction", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Nekoei", "suffix": ""}, {"first": "V", "middle": ["I"], "last": "Butoi", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Direct epistemic uncertainty prediction. Transactions on Machine Learning Research, 2022.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Simple and scalable predictive uncertainty estimation using deep ensembles", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON> and scalable predictive uncertainty estimation using deep ensembles. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Stochastic expectation propagation. Advances in neural information processing systems", "authors": [{"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "J", "middle": ["M"], "last": "Hernández<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["E"], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "", "volume": "28", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> <PERSON><PERSON>- tic expectation propagation. Advances in neural informa- tion processing systems, 28, 2015.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Teaching models to express their uncertainty in words", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Hilton", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Teaching models to express their uncertainty in words. Transactions on Machine Learning Research, 2022.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Generating with confidence: Uncertainty quantification for black-box large language models", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.19187"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> with confidence: Uncertainty quantification for black-box large language models. arXiv preprint arXiv:2305.19187, 2023.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "We're afraid language models aren't modeling ambiguity", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "West", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "The 2023 Conference on Empirical Methods in Natural Language Processing", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. <PERSON>, and <PERSON>, <PERSON>. We're afraid language models aren't modeling ambiguity. In The 2023 Conference on Empirical Methods in Natural Language Processing, 2023.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Structured and efficient variational deep learning with matrix gaussian posteriors", "authors": [{"first": "C", "middle": [], "last": "Louizos", "suffix": ""}, {"first": "M", "middle": [], "last": "Welling", "suffix": ""}], "year": 2016, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "1708--1716", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> Structured and efficient varia- tional deep learning with matrix gaussian posteriors. In International conference on machine learning, pp. 1708- 1716. PMLR, 2016.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Predictive uncertainty estimation via prior networks", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. Predictive uncertainty estima- tion via prior networks. Advances in neural information processing systems, 31, 2018.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Uncertainty estimation in autoregressive structured prediction", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> Uncertainty estimation in autore- gressive structured prediction. In International Confer- ence on Learning Representations, 2020.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Uncertainty in gradient boosting via ensembles", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>- tainty in gradient boosting via ensembles. In International Conference on Learning Representations, 2020.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Reducing conversational agents' overconfidence through linguistic calibration", "authors": [{"first": "S", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Szlam", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y.-L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, Y.-L. Reducing conversational agents' overconfidence through linguistic calibration. Transactions of the Association for Computational Linguistics, 2022.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Answering ambiguous open-domain questions", "authors": [{"first": "S", "middle": [], "last": "Min", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Ambigqa", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2004.10645"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>: Answering ambiguous open-domain questions. arXiv preprint arXiv:2004.10645, 2020.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Dropconnect is effective in modeling uncertainty of bayesian deep networks", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "S", "middle": ["K"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>arg", "suffix": ""}, {"first": "C", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Scientific reports", "volume": "11", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, H. Dropcon<PERSON> is effective in modeling uncertainty of bayesian deep networks. Scientific reports, 11:5458, 2021.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Bayesian learning for neural networks", "authors": [{"first": "R", "middle": ["M"], "last": "<PERSON>", "suffix": ""}], "year": 2012, "venue": "", "volume": "118", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>an learning for neural networks, volume 118. Springer Science & Business Media, 2012.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Analyzing uncertainty in neural machine translation", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Grangier", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "3956--3965", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Analyzing uncertainty in neural machine translation. In Interna- tional Conference on Machine Learning, pp. 3956-3965.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Can you trust your model's uncertainty? evaluating predictive uncertainty under dataset shift", "authors": [{"first": "Y", "middle": [], "last": "Ovadia", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ren", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Can you trust your model's uncertainty? evalu- ating predictive uncertainty under dataset shift. Advances in neural information processing systems, 32, 2019.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Pac neural prediction set learning to quantify the uncertainty of generative language models", "authors": [{"first": "S", "middle": [], "last": "Park", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09254"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> neural prediction set learning to quantify the uncertainty of generative language models. arXiv preprint arXiv:2307.09254, 2023.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Sentence-bert: Sentence embeddings using siamese bert-networks", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP-IJCNLP)", "volume": "", "issue": "", "pages": "3982--3992", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>-bert: Sentence em- beddings using siamese bert-networks. In Proceedings of the 2019 Conference on Empirical Methods in Natu- ral Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP- IJCNLP), pp. 3982-3992, 2019.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Robots that ask for help: Uncertainty alignment for large language model planners", "authors": [{"first": "A", "middle": [], "last": "Ren", "suffix": ""}, {"first": "A", "middle": [], "last": "Di<PERSON>t", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Tu", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "2nd Workshop on Language and Robot Learning: Language as Grounding", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Robots that ask for help: Uncertainty alignment for large language model planners. In 2nd Workshop on Language and Robot Learning: Language as Grounding, 2023.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Deep bayesian bandits showdown: An empirical comparison of bayesian deep networks for thompson sampling", "authors": [{"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, J. <PERSON> bayesian bandits showdown: An empirical comparison of bayesian deep networks for thompson sampling. In International Conference on Learning Representations, 2018.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Post-hoc uncertainty learning using a dirichlet meta-model", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Bu", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ghosh", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "37", "issue": "", "pages": "9772--9781", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, G. Post-hoc uncertainty learning using a dirichlet meta-model. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 37, pp. 9772-9781, 2023.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Prompting gpt-3 to be reliable", "authors": [{"first": "C", "middle": [], "last": "Si", "suffix": ""}, {"first": "Z", "middle": [], "last": "Gan", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["L"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> Prompting gpt-3 to be reliable. In The Eleventh International Conference on Learning Rep- resentations, 2022.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Prompting GPT-3 to be reliable", "authors": [{"first": "C", "middle": [], "last": "Si", "suffix": ""}, {"first": "Z", "middle": [], "last": "Gan", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["L"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>. Prompting GPT-3 to be reliable. In The Eleventh International Conference on Learning Representations, 2023.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Task ambiguity in humans and language models", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>. Task ambiguity in humans and language models. In The Eleventh International Conference on Learning Repre- sentations, 2022.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Bayesian uncertainty estimation for batch normalized deep networks", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "4907--4916", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> uncer- tainty estimation for batch normalized deep networks. In International Conference on Machine Learning, pp. 4907-4916. PMLR, 2018.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Just ask for calibration: Strategies for eliciting calibrated confidence scores from language models fine-tuned with human feedback", "authors": [{"first": "K", "middle": [], "last": "Tian", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["D"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.14975"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, C. D. Just ask for calibra- tion: Strategies for eliciting calibrated confidence scores from language models fine-tuned with human feedback. arXiv preprint arXiv:2305.14975, 2023.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Selfconsistency improves chain of thought reasoning in language models", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": ["V"], "last": "Le", "suffix": ""}, {"first": "E", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Chowdhery", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>. Self- consistency improves chain of thought reasoning in lan- guage models. In The Eleventh International Conference on Learning Representations, 2022.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Uncertainty quantification with pre-trained language models: A large-scale empirical analysis", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": ["P"], "last": "<PERSON>", "suffix": ""}, {"first": "U", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L.-P", "middle": [], "last": "Morency", "suffix": ""}], "year": 2022, "venue": "Findings of the Association for Computational Linguistics: EMNLP 2022", "volume": "", "issue": "", "pages": "7273--7284", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, L.-P. Uncertainty quantification with pre-trained language models: A large-scale empir- ical analysis. In Findings of the Association for Com- putational Linguistics: EMNLP 2022, pp. 7273-7284, 2022.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Can explanations be useful for calibrating black box models?", "authors": [{"first": "X", "middle": [], "last": "Ye", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON>. Can explanations be useful for calibrating black box models?, 2022.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Navigating the grey area: Expressions of overconfidence and uncertainty in language models", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.13439"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Navigating the grey area: Expressions of overconfidence and uncertainty in language models. arXiv preprint arXiv:2302.13439, 2023. Disambiguations: 1. [Disambiguated task description 1.]", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "If the task description is clear and unambiguous", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Disambiguated task description 3.] ... If the task description is clear and unambiguous, simply output: Disambiguations:", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Sort the data in alphabetical order. Input: <PERSON>, <PERSON>", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Sort the data in alphabetical order. Input: <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>var<PERSON>.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Identify the largest city in the set. Input: The following table lists the cities in the set: Name 10. Organize the files by date. Input: Files to be organized: Filename Creation Date Last Modified Date conference-recording", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "▷ 9. Identify the largest city in the set. Input: The following table lists the cities in the set: Name 10. Organize the files by date. Input: Files to be organized: Filename Creation Date Last Modified Date conference-recording.avi 11/10/2020 11/12/2020 birthday-video.mp4 05/05/2021 05/06/2021 budget.xlsx 12/31/2022 01/10/2023", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Find the middle value in a list of numbers", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "▷ 11. Find the middle value in a list of numbers.", "links": null}}, "ref_entries": {"FIGREF0": {"num": null, "fig_num": "1", "type_str": "figure", "text": "Figure 1. The uncertainty quantification frameworks of DEEP EN-SEMBLES (upper) and input clarification ensembling (lower).", "uris": null}, "FIGREF1": {"num": null, "fig_num": null, "type_str": "figure", "text": "k) }, are different. Denote the resulting distribution of the model parameters θ as p(θ|D) (either approximated by BNNs or DEEP ENSEMBLES) where D is the training dataset. Then the ensembled distribution of BNN can be represented as q(", "uris": null}, "FIGREF3": {"num": null, "fig_num": "3", "type_str": "figure", "text": "Figure 3. The uncertainty quantification examples using the proposed method. The instances are selected from existing datasets including Natural Question (NQ) (<PERSON><PERSON><PERSON><PERSON> et al., 2019), AmbigQA (<PERSON> et al., 2020), and GSM8K (<PERSON> et al., 2021).", "uris": null}, "FIGREF4": {"num": null, "fig_num": "4", "type_str": "figure", "text": "Figure 4. (Left) Average aleatoric uncertainty of the ambiguous inputs and their clarifications. (Right) Performance improvement via Soliciting clarifications. AmbigQA-Orig and AmbigInst-Orig refer to the recall of correct answers when directly answering the original input. AmbigQA-Clarify and AmbigInst-Clarify refer to the recall of correct answers using different number of input clarifications.", "uris": null}, "FIGREF5": {"num": null, "fig_num": "5", "type_str": "figure", "text": "Figure5. The prompt template for the fine-tuning of Llama-3-8B-Instruction. The {original question} and {ground truth clarification} are two placeholders that will be filled with the original question (either ambiguous or not) and the ground-truth clarifications.", "uris": null}, "FIGREF6": {"num": null, "fig_num": "9", "type_str": "figure", "text": "Figure 9. The prompt for question disambiguation on the AmbigQA dataset.", "uris": null}, "FIGREF7": {"num": null, "fig_num": "7", "type_str": "figure", "text": "Rank the football players based on their performance.Input: The following table lists the statistics of football players:", "uris": null}, "FIGREF8": {"num": null, "fig_num": "11", "type_str": "figure", "text": "Figure 11.  The prompt for answer extraction using LLMs for the Natural Question and AmbigQA datasets.", "uris": null}, "TABREF2": {"num": null, "type_str": "table", "text": "Uncertainty quantification for mistake detection. Entropy (✔) refers to the average total uncertainty of questions with correct answers, while Entropy (✘) refers to the average total uncertainty of question with wrong answers.", "content": "<table><tr><td>Method</td><td colspan=\"4\">AUROC F1 Score Entropy (✔) Entropy (✘)</td></tr><tr><td/><td colspan=\"2\">Natural Question</td><td/><td/></tr><tr><td>SEMANTIC ENTROPY</td><td>63.8</td><td>77.9</td><td>0.29</td><td>0.56</td></tr><tr><td>ASK4CONF</td><td>70.4</td><td>83.9</td><td>-</td><td>-</td></tr><tr><td>ENSEMBLES  *</td><td>69.7</td><td>79.7</td><td>0.46</td><td>0.88</td></tr><tr><td>OURS</td><td>72.3</td><td>80.2</td><td>0.58</td><td>1.18</td></tr><tr><td/><td/><td>GSM8K</td><td/><td/></tr><tr><td>SEMANTIC ENTROPY</td><td>88.2</td><td>92.4</td><td>0.32</td><td>1.46</td></tr><tr><td>ASK4CONF</td><td>58.1</td><td>92.3</td><td>-</td><td>-</td></tr><tr><td>ENSEMBLES  *</td><td>88.3</td><td>94.6</td><td>0.57</td><td>1.94</td></tr><tr><td>OURS</td><td>89.7</td><td>94.7</td><td>0.42</td><td>1.82</td></tr></table>", "html": null}, "TABREF3": {"num": null, "type_str": "table", "text": "Uncertainty quantification for ambiguity detection. Avg. AU (✔) refers to the average aleatoric uncertainty of unambiguous questions, while Avg. AU (✘) refers to the average aleatoric uncertainty of ambiguous questions.", "content": "<table><tr><td/><td/><td>AmbigQA</td><td/><td/></tr><tr><td>SEMANTIC ENTROPY</td><td>54.9</td><td>46.8</td><td>0.24</td><td>0.47</td></tr><tr><td>ASK4CONF-D</td><td>55.0</td><td>64.3</td><td>-</td><td>-</td></tr><tr><td>ENSEMBLES  *  (aleatoric)</td><td>53.6</td><td>53.0</td><td>0.13</td><td>0.13</td></tr><tr><td>ENSEMBLES  *  (total)</td><td>55.4</td><td>55.0</td><td>0.50</td><td>0.41</td></tr><tr><td>OURS (GPT)</td><td>71.7</td><td>70.1</td><td>0.28</td><td>0.67</td></tr><tr><td>OURS (LLaMA)</td><td>67.1</td><td>71.8</td><td>0.55</td><td>0.91</td></tr><tr><td>OURS  *</td><td>89.8</td><td>85.6</td><td>0.53</td><td>1.52</td></tr><tr><td/><td colspan=\"2\">AmbigInst</td><td/><td/></tr><tr><td>SEMANTIC ENTROPY</td><td>66.0</td><td>53.7</td><td>0.07</td><td>0.50</td></tr><tr><td>ASK4CONF-D</td><td>57.9</td><td>75.4</td><td>-</td><td>-</td></tr><tr><td>OURS (GPT)</td><td>81.3</td><td>77.9</td><td>0.10</td><td>0.75</td></tr><tr><td>OURS  *</td><td>96.7</td><td>92.6</td><td>0.10</td><td>1.04</td></tr></table>", "html": null}, "TABREF4": {"num": null, "type_str": "table", "text": "In this task, you will receive a single question, and your goal is to generate multiple versions of it that convey the same meaning as the original. Please format your responses as Figure 8. The prompt for question rephrase on the Natural Question dataset descriptions generated by CHATGPT, we manually filter out those that have an open-ended output space such as Write a report on the new marketing campaign. The final dataset contains 15 ambiguous task descriptions. After that, we query CHATGPT again to generate ground-truth clarifications based on the cause of ambiguities generated in the first query.", "content": "<table><tr><td>follows:</td></tr><tr><td>Rephrase 1: [Your rephrased question]</td></tr><tr><td>Rephrase 2: [Another rephrased question]</td></tr><tr><td>Rephrase 3: [Yet another rephrased question]</td></tr><tr><td>....</td></tr><tr><td>Ensure that each rephrased question is distinct from the others.\"</td></tr><tr><td>Here are two examples:</td></tr><tr><td>(examples skipped)</td></tr></table>", "html": null}, "TABREF5": {"num": null, "type_str": "table", "text": "Calculate the average of the numbers in the given list, rounding to the nearest whole number.", "content": "<table><tr><td>Name</td><td colspan=\"4\">Size Weight Color Date of Manufacture</td><td>Price</td></tr><tr><td>Pen</td><td colspan=\"2\">14cm 0.02kg</td><td>blue</td><td>01/15/2022</td><td>$1.50</td></tr><tr><td>Book</td><td>23cm</td><td>0.5kg</td><td>red</td><td>08/10/2020</td><td>$15.00</td></tr><tr><td colspan=\"2\">Laptop 38cm</td><td>1.8kg</td><td>silver</td><td>05/04/2021</td><td>$1200.00</td></tr><tr><td>▷ 2.</td><td/><td/><td/><td/><td/></tr></table>", "html": null}, "TABREF6": {"num": null, "type_str": "table", "text": "Please format your responses as follows (with at least two rephrasings per question):If the original question is already clear and unambiguous, you should indicate this by stating, \"No clarification needed.\"", "content": "<table><tr><td>Clarifications:</td></tr><tr><td>1. [First rephrased question]</td></tr><tr><td>2. [Second rephrased question]</td></tr><tr><td>3. [Third rephrased question]</td></tr><tr><td>...</td></tr></table>", "html": null}}}}