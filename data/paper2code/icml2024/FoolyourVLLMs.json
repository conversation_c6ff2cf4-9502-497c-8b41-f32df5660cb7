{"paper_id": "FoolyourVLLMs", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:39:54.453703Z"}, "title": "Fool Your (Vision and) Language Model with Embarrassingly Simple Permutations", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Tingyang", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Large language and vision-language models are rapidly being deployed in practice thanks to their impressive capabilities in instruction following, in-context learning, and so on. This raises an urgent need to carefully analyse their robustness so that stakeholders can understand if and when such models are trustworthy enough to be relied upon in any given application. In this paper, we highlight a specific vulnerability in popular models, namely permutation sensitivity in multiple-choice question answering (MCQA). Specifically, we show empirically that popular models are vulnerable to adversarial permutation in answer sets for multiple-choice prompting, which is surprising as models should ideally be as invariant to prompt permutation as humans are. These vulnerabilities persist across various model sizes, and exist in very recent language and vision-language models. Code is available at https://github.com/ ys-zong/FoolyourVLLMs.", "pdf_parse": {"paper_id": "FoolyourVLLMs", "_pdf_hash": "", "abstract": [{"text": "Large language and vision-language models are rapidly being deployed in practice thanks to their impressive capabilities in instruction following, in-context learning, and so on. This raises an urgent need to carefully analyse their robustness so that stakeholders can understand if and when such models are trustworthy enough to be relied upon in any given application. In this paper, we highlight a specific vulnerability in popular models, namely permutation sensitivity in multiple-choice question answering (MCQA). Specifically, we show empirically that popular models are vulnerable to adversarial permutation in answer sets for multiple-choice prompting, which is surprising as models should ideally be as invariant to prompt permutation as humans are. These vulnerabilities persist across various model sizes, and exist in very recent language and vision-language models. Code is available at https://github.com/ ys-zong/FoolyourVLLMs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Large language models (LLMs) (<PERSON> et al., 2020; Ope-nAI, 2023a; <PERSON><PERSON><PERSON><PERSON> et al., 2023a) and large visionlanguage models (VLLMs) (<PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2023c) have made astonishing progress in recent years. They have attained strong capabilities across a diverse array of language tasks, enabling nuanced text generation, sophisticated instruction following, and natural dialogue with multimodal input and output. One task where they demonstrate particular prowess is multiple-choice question answering (MCQA) (Robinson & Wingate, 2023) . This is an important capability with many real-world applications, from education to recruitment exams. Current LLMs and VLLMs have widely utilized the task format of MCQA for benchmarking and evaluation (<PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2022) . This has built confidence that they can generate accurate and robust answers, underpinned claims of LLM competence at professional level human qualifications such as the bar exam (OpenAI, 2023b) , and even led to reports of surpassing human-level performance on various tasks.", "cite_spans": [{"start": 29, "end": 49, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF3"}, {"start": 50, "end": 65, "text": "Ope-nAI, 2023a;", "ref_id": null}, {"start": 66, "end": 88, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}, {"start": 129, "end": 151, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF0"}, {"start": 152, "end": 169, "text": "<PERSON> et al., 2023c)", "ref_id": null}, {"start": 521, "end": 547, "text": "(Robinson & Wingate, 2023)", "ref_id": "BIBREF30"}, {"start": 754, "end": 778, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF11"}, {"start": 779, "end": 795, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF22"}, {"start": 796, "end": 815, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF47"}, {"start": 816, "end": 835, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF16"}, {"start": 836, "end": 857, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF33"}, {"start": 1039, "end": 1054, "text": "(OpenAI, 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Surprisingly, contrary to the confidence instilled by highperformance metrics on established benchmarks, these models are surprisingly brittle when subjected to simple permutations in the answer choices, i.e., randomly changing the option positions. In this paper, we show that even a simple permutation of the answer sets, as illustrated in Figure 1 , can lead to a dramatic decline in accuracy for both LLMs and VLLMs in a wide range of MCQA datasets, sometimes even below the random chance levels. For instance, Llama2-13B (<PERSON><PERSON><PERSON><PERSON> et al., 2023a ) experiences a 33.89% degradation in accuracy on the MMLU dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2020) following random permutation of option positions, with results falling below the random chance. A wide variety of popular LLMs and VLLMs, suffer significantly from this vulnerability, as summarised in Figure 1 b. Furthermore, our investigations reveal an even more discon-certing aspect: the vulnerability to permutations persists in LLMs and VLLMs even when multiple distractor options are deliberately removed from the answer sets. Intuitively, one expects that by eliminating incorrect choices, the task should become simpler due to increasing chance performance, thereby enhancing the models' performance. However, our empirical findings contradict this notion. Even with a reduced number of distractors, the performance of both LLMs and VLLMs remains susceptible to degradation, affirming the deeply ingrained nature of this vulnerability.", "cite_spans": [{"start": 526, "end": 548, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023a", "ref_id": null}, {"start": 616, "end": 640, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF11"}], "ref_spans": [{"start": 349, "end": 350, "text": "1", "ref_id": "FIGREF0"}, {"start": 849, "end": 850, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "To further investigate the source of the brittleness, we demonstrate through our adversarial attack that it is not merely a selection bias towards/against certain positions, such as moving correct answers to a fixed position that a given model is biased against picking. While positional factors may moderately influence model performance, they do not explain the strength of our adversarial attack results, suggesting a more systemic issue that extends beyond simple position bias.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "This issue should be of intrinsic concern to those seeking to understand and design trustworthy and reliable LLMs and VLLMs, or emulate human capabilities. However, one might speculate that the issue could be mitigated in practice through the engineering solution of majority voting across different permutations or by employing calibration strategies as suggested in previous work (<PERSON> et al., 2021) . However, our findings indicate that while majority voting may offer some degree of improvement, the resulting performance still lags behind the original metrics, despite incurring a k!× computational cost of the original inference time. Additionally, calibration techniques such as calibrate-before-use (<PERSON> et al., 2021) fail to alleviate this problem effectively.", "cite_spans": [{"start": 382, "end": 401, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF44"}, {"start": 707, "end": 726, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In summary, our research unveils a glaring yet often overlooked vulnerability in large language models and visionlanguage models, specifically within the domain of multiplechoice question answering (MCQA). Despite their impressive metrics on well-established benchmarks, these models reveal a disconcerting fragility when faced with simple manipulations such as option permutations. Existing mitigation strategies fall short of effectively resolving this issue. Our observations not only raise pivotal questions about the models' robustness but also accentuate the necessity for heightened scrutiny in assessing their MCQA capabilities. We argue that stakeholders should be vigilant in relying on such models until these vulnerabilities are adequately addressed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In this section, we analyse the brittleness of a broad array of large language models and vision-language models to random adversarial attacks in MCQA. By simply shuffling answer choices, we find that these models fail to maintain their performance, revealing a critical vulnerability.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Simple Adversarial Attack Breaks LLMs and VLLMs", "sec_num": "2."}, {"text": "In an ideal scenario, robust models should offer consistent predictions that are invariant to permutations that have no semantic influence on the question being posed. To test this, we simply iterate through the possible permutations of MCQ options. A robust model should be correct in every case. While there are k! possible combinations in total, we cease permutation once the model produces an incorrect prediction (succumbs to the permutation attack), which usually requires far less than k! attempts1 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Setup", "sec_num": "2.1."}, {"text": "Formally, Given a question q and an answer list A = {a 1 , a 2 , . . . , a k }, the permutation adversarial attack can be described by the Equation 1. We maximize the loss function (L) with respect to all possible permutations (Π) of the answer list. Here, prompt(q, A) prompts the model with the given query and answer list, and the model's response is then evaluated by the loss.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Setup", "sec_num": "2.1."}, {"text": "Maximize: L (prompt(q, A * )) Models We evaluate a wide range of LLMs and VLLMs of diverse sizes, different pretrained backbones, and both auto-regressive pretrained and instruction-following finetuned models. Specifically, for LLMs, we have evaluated LLaMA-2 (7B/13B) (<PERSON><PERSON><PERSON><PERSON> et al., 2023b) , Vicuna (7B/13B) (<PERSON> et al., 2023) , WizardLM-13B (<PERSON> et al., 2023) , InternLM-20B (Team, 2023a) , Falcon-7B (Pened<PERSON> et al., 2023) , and MPT-7B (Team, 2023b) . For VLLMs, InstructBLIP (Vicuna-based, 7B/13B) (<PERSON> et al., 2023) , Open-Flamingo (MPT-based, 9B) (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , Otter (Llama-based, MPT-based) (<PERSON> et al., 2023b) , LLaVA (7B/13B) (<PERSON> et al., 2023b) , LLaVA-v1.5 (7B/13B) (<PERSON> et al., 2023a) , Emu2-<PERSON><PERSON> (<PERSON> et al., 2024) , <PERSON><PERSON> (7B) (<PERSON><PERSON><PERSON> et al., 2023) , and mPLUG-Owl (pretraining, intruction) (<PERSON> et al., 2023) are used for evaluation.", "cite_spans": [{"start": 269, "end": 292, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}, {"start": 311, "end": 332, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF5"}, {"start": 348, "end": 365, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF41"}, {"start": 381, "end": 394, "text": "(Team, 2023a)", "ref_id": null}, {"start": 407, "end": 428, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF29"}, {"start": 442, "end": 455, "text": "(Team, 2023b)", "ref_id": null}, {"start": 505, "end": 523, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF8"}, {"start": 556, "end": 579, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF2"}, {"start": 613, "end": 631, "text": "(<PERSON> et al., 2023b)", "ref_id": null}, {"start": 649, "end": 668, "text": "(<PERSON> et al., 2023b)", "ref_id": null}, {"start": 691, "end": 710, "text": "(<PERSON> et al., 2023a)", "ref_id": null}, {"start": 723, "end": 741, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF35"}, {"start": 756, "end": 778, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF24"}, {"start": 821, "end": 838, "text": "(<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experiment Setup", "sec_num": "2.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "s.t. A * ∈ Π(A)", "eq_num": "(1)"}], "section": "Experiment Setup", "sec_num": "2.1."}, {"text": "Datasets We utilize a diverse array of language and vision-language MCQA datasets for comprehensive evaluation. These datasets cover multiple domains and require different aspects of the models to give correct answers, ensuring our findings are generalizable. Specifically, for LLMs, we utilize MMLU (<PERSON><PERSON><PERSON><PERSON> et al., 2020) , ARC challenge (ARC-c) (<PERSON> et al., 2018) , BoolQ (<PERSON> et al., 2019) , SocialiQA (<PERSON><PERSON> et al., 2019) , and MedMCQA (Pa<PERSON> et al., 2022) . For VLLMs, we use ScienceQA (Lu et al., 2022) , A-OKVQA (<PERSON><PERSON><PERSON><PERSON> et al., 2022) , MMBench (<PERSON> et al., 2023c) , and SEED-Bench (Li et al., 2023a) . We use the questions in ScienceQA that have corresponding images, the MCQA subsets of MMBench, and the image-based MC-QAs in SEED-Bench.", "cite_spans": [{"start": 300, "end": 324, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF11"}, {"start": 349, "end": 369, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF7"}, {"start": 378, "end": 398, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF6"}, {"start": 411, "end": 429, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF32"}, {"start": 444, "end": 462, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF28"}, {"start": 493, "end": 510, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF22"}, {"start": 521, "end": 543, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF33"}, {"start": 554, "end": 573, "text": "(<PERSON> et al., 2023c)", "ref_id": null}, {"start": 591, "end": 609, "text": "(<PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experiment Setup", "sec_num": "2.1."}, {"text": "Evaluations We use accuracy as our primary metric. During testing, we prompt the model to generate the possible option symbols (e.g., A to D) and extract the probability assigned to each choice in the first position. The option with the highest probability is then selected as the model's answer for that specific question. For both LLMs and VLLMs, we use greedy decoding to ensure reproducibility. All of the datasets we use are publicly available. All of the model weights (except GPT-3.5-Turbo) can be obtained from the HuggingFace model zoo or the original official Github repositories. GPT-3.5-Turbo can be accessed from OpenAI API. Experiments are conducted on A100-80GB GPUs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Setup", "sec_num": "2.1."}, {"text": "We present the main results in Table 3 and 4 for language and vision-language models respectively.", "cite_spans": [], "ref_spans": [{"start": 37, "end": 38, "text": "3", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Main Results", "sec_num": "2.2."}, {"text": "Language Models In our experiments, large language models manifested a significant susceptibility to adversarial permutations, a finding consistent across various MCQA benchmarks. Our evaluation extended beyond the typical four-option MCQA datasets to include more diverse formats like the two-option BoolQ (<PERSON> et al., 2019) and the three-option SocialIQA (<PERSON><PERSON> et al., 2019) that are naturally more resilient to the permutations. Intriguingly, the presence of only one or two distractor options did not mitigate the model's vulnerability to permutations. For instance, Llama2-7B's accuracy on BoolQ plummeted from 61.79% to a mere 8.23%, a performance even worse than random chance. Moreover, out of 50 experiments conducted with large language models, only 12 non-GPT-3.5-turbo models managed to perform better than random chance. And all of them, including GPT-3.5-turbo, suffer from significant performance decreases.", "cite_spans": [{"start": 307, "end": 327, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF6"}, {"start": 359, "end": 377, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Main Results", "sec_num": "2.2."}, {"text": "Vision-Language Models In the vision-language model evaluations, the susceptibility to adversarial permutations is also severe. Despite the presence of visual context, which may intuitively add a layer of resilience, the VLLMs were not spared from the adverse effects of our permutation attacks. Among 48 experiments, more than half of them fell below random chance performance after the adversarial attack. While InstructBLIP (<PERSON> et al., 2023) and LLaVA-v1.5 (<PERSON> et al., 2023a) show relatively strong robustness to the adversarial attack, all of the models experienced significant accuracy drops ranging from 20% to 45%.", "cite_spans": [{"start": 427, "end": 445, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF8"}, {"start": 461, "end": 480, "text": "(<PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Main Results", "sec_num": "2.2."}, {"text": "Further Observations We note that within the same model family but with varying parameter sizes (e.g., InstructBLIP-7B v.s. InstructBLIP-13B), scaling up generally enhances both the baseline performance and resilience to adversarial attacks with relatively smaller declines in accuracy. We can also observe that models have different success rates over random chance in different datasets. For example, all of the LLMs failed the adversarial attack on MedMCQA dataset except GPT-3.5-turbo, which is also only slightly above the random chance. It shows the challenges of LLMs to generalize to out-of-domain data, and suggests caution about their use in unconstrained practical scenarios.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Main Results", "sec_num": "2.2."}, {"text": "In this subsection, we examine the impact of a stricter test condition on MCQA, specifically by reducing the number of distractor options, while obviously retaining the true answer. This is expected to improve baseline performance by increasing random chance level, but also we expected it to reduce vulnerability to adversarial permutation by substantially reducing the degrees of freedom that the permutation attack can explore. However, we found that models remain highly susceptible to even the few permutations available in the reduced set of options.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer Set Pruning", "sec_num": "2.3."}, {"text": "Experiment Setup Specifically, we constrain the answer set by reducing the number of total choices from four to either three or two, inclusive of the ground-truth answer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer Set Pruning", "sec_num": "2.3."}, {"text": "We then compare the performance metrics between these pruned sets in both permuted and non-permuted conditions to assess the relative susceptibility of the models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer Set Pruning", "sec_num": "2.3."}, {"text": "We present the results of answer set pruning of MMLU datasets in Table 5 and other datasets in the appendix. As can be seen from Table 5 , reducing the number of options increases the base prediction accuracy as expected, but performing adversarial permutation on the reduced answer set still dramatically reduces the accuracy even in the 2-option cases. In most cases, the performance is below the chance level given the number of options. This means that, surprisingly, even in the simplest case of a binary choice, models are not robust to whether the true answer is presented as the first or second option.", "cite_spans": [], "ref_spans": [{"start": 71, "end": 72, "text": "5", "ref_id": "TABREF4"}, {"start": 135, "end": 136, "text": "5", "ref_id": "TABREF4"}], "eq_spans": [], "section": "Results", "sec_num": null}, {"text": "In this section, we delve into a detailed analysis of the potential causes behind the demonstrated vulnerability and examine related attack types and potential symbol-content spurious correlation. We also refer readers to the Appendix A.2 for the effect of different prompting and attack techniques and A.4for qualitative results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Vulnerability Causes", "sec_num": "3."}, {"text": "A concurrent study to ours argued for the existence of position bias in language model MCQA (<PERSON> et al., 2023a) . For example, in an A/B/C/D MCQ situation, a given model might have a predisposition to selecting a particular option such as \"C\" and an aversion to selecting some other option such as \"A\", irrespective of the correctness of the answer associated with each label. Position bias could potentially explain adversarial permutation vulnerability if a model is so averse to selecting a particular option, that rotating the true answer into that slot would reliably cause it to fail.", "cite_spans": [{"start": 92, "end": 113, "text": "(<PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Posisition Bias and Other Attacks", "sec_num": "3.1."}, {"text": "To analyse whether position bias can explain our results, we compare our adversarial permutation results to the performance of each LLM under position bias analysis -always rotating the correct answer to a specific slot (A/B/C/D) in the answer list.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Posisition Bias and Other Attacks", "sec_num": "3.1."}, {"text": "From the results in Table 6 , we do see the position bias effect remarked upon by <PERSON> et al. (2023a) . The models tested exhibit varying degrees of position bias, as results fluctuate with respect to original performance (left column). For example, Vicuna suffers limited position bias, while Falcon-7B is highly position biased. Falcon-7B's baseline accuracy of 31% rises to 70.9% when the true answer is placed in slot A -indicating a strong preference for choosing <PERSON>; but drops to 3.7% when the true answer is placed in slot B, indicating a strong aversion to selecting B.", "cite_spans": [{"start": 82, "end": 102, "text": "<PERSON> et al. (2023a)", "ref_id": null}], "ref_spans": [{"start": 26, "end": 27, "text": "6", "ref_id": "TABREF5"}], "eq_spans": [], "section": "Posisition Bias and Other Attacks", "sec_num": "3.1."}, {"text": "Comparing the observed position bias to the impact of our adversarial permutation, we can see that our adversarial permutation has a much stronger effect. The results after permutation (right column) are substantially worse than the position bias results. For example, Llama2-7B performs above chance level for answers in every possible position (A/B/C/D), but is reduced to below chance by our adversarial permutation. Thus we conclude that the impact of our adversarial permutation is not explainable by position bias. Evidently, models rely on the relationships between choices, including the distractors, which the adversarial permutation manipulates to fool them. I.e., it is not just the true answer, and the location of the true answer (position bias), but also the pattern of the distractor answers around the true answer (as explored by adversarial permutations) that determine model success or failure. This reveals a complex and concerning form of vulnerability.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Posisition Bias and Other Attacks", "sec_num": "3.1."}, {"text": "Additionally, to further investigate the potential causes of the vulnerability and compare with other types of attacks, we consider circular evaluation (CircularEval) (<PERSON> et al., 6 , while these attacks degrade performance to some extent, our adversarial attack exhibits the most substantial impact and causes the largest performance drop.", "cite_spans": [], "ref_spans": [{"start": 180, "end": 181, "text": "6", "ref_id": "TABREF5"}], "eq_spans": [], "section": "Posisition Bias and Other Attacks", "sec_num": "3.1."}, {"text": "In this subsection, we investigate the potential existence of shortcut correlations between option symbols and content. We ask whether the permutation required to fool the model's prediction is independent of the chosen symbols or not. Here we consider two sets of symbols, Capital letters vs. Lowercase letters, and Capital letters vs. Roman Numerals. For each set of symbols, we calculated the correlations across all permutations of test set predictions. Specifically, we examined the similarity in response patterns to permutations between different symbol sets, with a high correlation indicating similar responses to permutations across two sets, and a low correlation indicating the opposite.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Symbol-Content Spurious Correlation", "sec_num": "3.2."}, {"text": "Our findings reveal a notably low correlation between the sets of capital letters (A/B/C/D) and Roman numerals (I/II/III/IV), in stark contrast to the correlation observed between capital letters (A/B/C/D) and lowercase letters (a/b/c/d). This discrepancy suggests that, while the baseline and permuted accuracies remain largely consistent across different symbol sets, their responses to permutations diverge significantly. Such behavior implies that the model might be exploiting symbol-answer shortcuts (<PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2023) and spurious correlations (<PERSON><PERSON> et al., 2020) inadvertently learned during training, indicating another potential underlying cause of our observed vulner- In summary: not only are the models not invariant to permutation, but the specific way in which they are not invariant is dependent on the choice of symbols. ", "cite_spans": [{"start": 506, "end": 528, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF10"}, {"start": 529, "end": 545, "text": "<PERSON> et al., 2023)", "ref_id": null}, {"start": 572, "end": 593, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF31"}], "ref_spans": [], "eq_spans": [], "section": "Symbol-Content Spurious Correlation", "sec_num": "3.2."}, {"text": "The previous analysis of adversarial permutation vulnerability should be concerning to stakeholders interested in trustworthy and reliable AI, and suggests a new focus for researchers in developing models with improved intrinsic permutation robustness. Nevertheless, one might ask whether any post-hoc engineering fixes or fine-tuning could alleviate this issue in practice for existing models. We explore this question in this section.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Exploring Mitigation Strategies", "sec_num": "4."}, {"text": "For post-hoc strategies, we consider three strategies that have previously proven effective in improving model performance, namely, majority voting (<PERSON> et al., 2023) , contextual calibration (<PERSON> et al., 2021) and confidence-based voting, and ask whether they can alleviate adversarial permutation vulnerability.", "cite_spans": [{"start": 148, "end": 167, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF40"}, {"start": 193, "end": 212, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "Post-hoc Mitigation Strategies", "sec_num": "4.1."}, {"text": "Setup Majority voting (<PERSON> et al., 2023) has been shown highly successful in self-ensembling over stochastic predictions. In our context, we apply it by obtaining the predictions for all possible permutations and then selecting the most frequent prediction. If most permutations lead to a correct prediction and there are only one or two pathological permutations that lead to an incorrect prediction, then majority voting should provide complete robustness to adversarial permutation. Contextual calibration (<PERSON> et al., 2021) is designed to mitigate the prior bias introduced from the in-context examples by estimating the model's bias toward each answer with a \"content-free\" query and fitting a calibration parameter. Here we consider the input question and options as the language prior bias. We first feed the model with content-free options (e.g. \"N/A\") as the content-free input, and then calibrate the real prediction based on the calibration parameters calculated from the content-free input. Additionally, we also apply confidence voting by taking the output that has maximum confidence among all permutations as the final prediction (M-confidence).", "cite_spans": [{"start": 22, "end": 41, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF40"}, {"start": 510, "end": 529, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "Post-hoc Mitigation Strategies", "sec_num": "4.1."}, {"text": "From the results in Table 8 for LLMs, we can see that neither defense proved effective at restoring the original performance levels. The majority voting and Mconfidence certainly ameliorated the permutation attack as expected, but still fell short of the baseline accuracy with only very few models gaining improvement. This is despite their being highly impractical defenses due to imposing a k!fold increase in inference cost. We also additionally analyze the pattern of majority vote in Appendix Table 18 and 19 .", "cite_spans": [], "ref_spans": [{"start": 26, "end": 27, "text": "8", "ref_id": "TABREF7"}, {"start": 505, "end": 507, "text": "18", "ref_id": "TABREF7"}, {"start": 512, "end": 514, "text": "19", "ref_id": "TABREF8"}], "eq_spans": [], "section": "Results", "sec_num": null}, {"text": "Contextual calibration, on the other hand, completely failed to make a meaningful impact on mitigating the adversarial attack. This re-confirms that the position bias is not the primary reason for models' permutation vulnerability.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Results", "sec_num": null}, {"text": "We additionally examine whether <PERSON> (<PERSON> et al., 2023a) can alleviate this issue, which introduces an inference-time debiasing method to mitigate token selection bias. Here, we debiased the prediction distribution of all possible permutations using their method and report original option order accuracy and permuted accuracy on the MMLU dataset in Table 9 . Although PriDe can indeed improve accuracy for the original order, it cannot effectively restore the performance with the permutation attack. It is understandable why PriDE almost completely fails: It learns a recalibration matrix that adjusts the relative probabilities of options (A, B, C, D). But this recalibration matrix is not input dependent, making it in the end a sophisticated amelioration against fixed positional/option bias. However, as we show here, the different permutations can lead to radically different preferences over options. In this case, no single fixed recalibration matrix can possibly alleviate this attack.", "cite_spans": [{"start": 38, "end": 59, "text": "(<PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [{"start": 359, "end": 360, "text": "9", "ref_id": "TABREF8"}], "eq_spans": [], "section": "Results", "sec_num": null}, {"text": "While (V)LLMs are typically evaluated in zero-shot on the MCQ benchmarks without within-dataset fine-tuning, we study whether fine-tuning on the training set can enhance the robustness to the permutation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Fine-tuning on Training Set", "sec_num": "4.2."}, {"text": "Setup As many of the benchmarks do not provide a training set, we conduct two fine-tuning experiments using Llama2-7B on two datasets that do provide training sets: ARC-Challenge (<PERSON> et al., 2018) and MedMCQA (<PERSON><PERSON> et al., 2022) . We also consider two fine-tuning variations: using the original training data and augmenting the training data with option permutations (n!) during fine-tuning. The latter is inspired by adversarial training (<PERSON><PERSON> et al., 2018) , to ensure that the model learns robustness to permutations during training. We fine-tune with LoRA (<PERSON> et al., 2022) for 1 epoch.", "cite_spans": [{"start": 179, "end": 199, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF7"}, {"start": 212, "end": 230, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF28"}, {"start": 441, "end": 461, "text": "(<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF23"}, {"start": 563, "end": 580, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Fine-tuning on Training Set", "sec_num": "4.2."}, {"text": "We present the results before and after fine-tuning in Table 10 . We observe that fine-tuning indeed improves the baseline accuracy and the permuted accuracy. However, regular fine-tuning is not a very effective solution because the fine-tuned models still suffer substantially from permutation vulnerability, and not all datasets have training set for fine-tuning. On the other hand, fine-tuning with permutation, while does not reliably defeat the attack, substantially alleviates it compared to the zero-shot and regular finetuning baseline. Although this is not a universal solution as we expect the model to generalize in a zero-shot manner, this shows the potential of mitigating the sensitivity during the pre-training or supervised fine-tuning stage with more robust training strategies.", "cite_spans": [], "ref_spans": [{"start": 61, "end": 63, "text": "10", "ref_id": "TABREF9"}], "eq_spans": [], "section": "Results", "sec_num": null}, {"text": "Large Language Models and Vision-Language Models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "5."}, {"text": "In recent years, the natural language processing community has seen astonishing progress in large language models (LLMs) with billions of trained parameters, such as GPT-3 (<PERSON> et al., 2020) and Llama (<PERSON><PERSON><PERSON><PERSON> et al., 2023a; b) , and become more intelligent after instruction-following finetuning (<PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2023b) . With the strong capabilities of LLMs, there is a growing interest in grounding vision with LLMs to enable the models to perceive multimodal information (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023c) , usually by utilizing pretrained language and vision encoders with trainable alignment modules to connect them. Such models have shown strong capabilities across a diverse range of tasks including multimodal generation, question-answering, dialogue, and more.", "cite_spans": [{"start": 172, "end": 192, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF3"}, {"start": 203, "end": 226, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023a;", "ref_id": null}, {"start": 227, "end": 229, "text": "b)", "ref_id": null}, {"start": 299, "end": 320, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF27"}, {"start": 321, "end": 341, "text": "<PERSON> et al., 2023b)", "ref_id": null}, {"start": 496, "end": 514, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF43"}, {"start": 515, "end": 533, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF49"}, {"start": 534, "end": 551, "text": "<PERSON> et al., 2023c)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "5."}, {"text": "Multiple-Choice Question Answering (MCQA) requires selecting the correct option from a set of choices and is prevalent in numerous real-world applications, making it a key performance metric for both LLMs and VLLMs. Various benchmarks such as MMLU (<PERSON><PERSON><PERSON><PERSON> et al., 2020) , AGI-Eval (<PERSON><PERSON> et al., 2023) , MedMCQA (<PERSON><PERSON> et al., 2022) , and SocialIQA (<PERSON><PERSON> et al., 2019) have been designed to assess MCQA proficiency across different domains. Different prompting approaches approaches have been considered for MCQA with multiple-choice prompting being the currently recommended state of the art (Robinson & Wingate, 2023) . On these benchmarks, LLMs and VLLMs frequently achieve, or even surpass, human-level accuracy (<PERSON><PERSON> et al., 2023; OpenAI, 2023b) , suggesting a high degree of reliability and robustness. However, we cast doubt on this presumed robustness, exposing the underlying fragility of these models in MCQA scenarios.", "cite_spans": [{"start": 248, "end": 272, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF11"}, {"start": 284, "end": 304, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF47"}, {"start": 315, "end": 333, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF28"}, {"start": 350, "end": 368, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF32"}, {"start": 593, "end": 619, "text": "(Robinson & Wingate, 2023)", "ref_id": "BIBREF30"}, {"start": 716, "end": 735, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF1"}, {"start": 736, "end": 750, "text": "OpenAI, 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Multiple-Choice Question Answering (MCQA).", "sec_num": null}, {"text": "Despite their impressive capabilities, concerns remain about the robustness and reliability of LLMs and VLLMs (<PERSON> et al., 2023d) . Pre- vious studies have revealed the sensitivity of LLMs to various factors including prompt (<PERSON> et al., 2023) , in-context examples (<PERSON> et al., 2021; <PERSON> et al., 2021) , irrelevant context (<PERSON> et al., 2023) , etc. Despite its significance, the robustness of MCQA has been relatively unexamined, particularly for VLLMs. Our research addresses this gap by scrutinizing a specific, yet pervasive, vulnerability to answer choice permutations in MCQA across both model types. Concurrent work (<PERSON> et al., 2023a) discusses positionbias in MCQA and (<PERSON> et al., 2023c) proposes circular evaluation. Our results show that adversarial permutation vulnerability is a much deeper problem than position bias, and position calibration strategy (e.g., (<PERSON> et al., 2023a )) cannot solve this issue.", "cite_spans": [{"start": 110, "end": 129, "text": "(<PERSON> et al., 2023d)", "ref_id": null}, {"start": 225, "end": 243, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF48"}, {"start": 266, "end": 284, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF19"}, {"start": 285, "end": 303, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF44"}, {"start": 325, "end": 343, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF34"}, {"start": 624, "end": 645, "text": "(<PERSON> et al., 2023a)", "ref_id": null}, {"start": 681, "end": 700, "text": "(<PERSON> et al., 2023c)", "ref_id": null}, {"start": 877, "end": 897, "text": "(<PERSON> et al., 2023a", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Robustness of LLMs and VLLMs.", "sec_num": null}, {"text": "In this paper, we present a comprehensive empirical analysis that unveils a critical but often overlooked vulnerability in both large language models (LLMs) and large visionlanguage models (VLLMs) in the context of multiple-choice question answering (MCQA). Despite their seemingly robust performance on established MCQA benchmarks, these models are highly susceptible to simple manipulations like option permutations. Our findings raise concerns about the widespread practice of evaluating and deploying these models based on MCQA tasks, urging caution in interpreting high benchmark scores as evidence of robust capabilities. We highlight the need for future work to develop training strategies and/or architectures that lead to intrinsic robustness to such adversarial attacks and develop parameter-efficient tuning approaches that can fine-tune or align existing pretrained LLMs and VLLMs to be invariant to permutations. Notably, although we do not offer a solution, we believe that our findings will be of interest to many researchers in the field and spur future works. Furthermore, these insights are crucial for industry professionals, particularly when deploying Large Language Models (LLMs) and Vision-Language Models (VLLMs) in real-world scenarios.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion", "sec_num": "6."}, {"text": "As a large number of LLM and VLLM benchmarks are based on multiple-choice question answering, the finding in our paper, that the MCQ evaluation is highly unstable and can be easily brought to below chance level through very simple permutations, is like to have a high impact on encouraging researchers to rethink the evaluation protocols and re-assess the capabilities of the developed LLMs and VLLMs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "model still predicts the answer to be A and shows strong confidence in terms of the token probabilities (right part of the figure). This might show the model's preference for the first option as a recency bias.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Analysis on Permutation Distribution While our main focus has been on the permutation-robustness of LLMs and VLMs, we can also ask about the distribution of responses as a function of permutation. For example, is there only one specific pathological permutation among all k! options, or are there many mistake-inducing permutations? To analyse this we report in Figure 4 , a histogram over the questions in ARC-challenge where each bin represents the number of questions where the specified proportion of permutations led to the correct answer that are originally correctly answered. For example, we see that Llama2-70B has a large number of questions that succeed for almost all permutations, while several models have a substantial batch of questions that are only correctly answered for around 30% of the potential permutations. Interestingly, most models have a substantial minority of questions that are only correctly answered for a small fraction of the permutations (leftmost bin). ", "cite_spans": [], "ref_spans": [{"start": 369, "end": 370, "text": "4", "ref_id": null}], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Table 19 . Majority vote performance when considering the easy sample with respect to a certain model to be the ones where 25% of the permutations are correct on the MMLU dataset. If we consider the easy sample with respect to a certain model to be the ones where 25% of the permutations are correct, the majority vote accuracy of difficult samples will be 0%, and we calculate the accuracy for majority vote on the easy samples. As shown in the both models have improved with majority vote on easy samples but the final performance may or may not be improved overall when considering the average accuracy across both easy and difficult samples. ", "cite_spans": [], "ref_spans": [{"start": 6, "end": 8, "text": "19", "ref_id": "TABREF8"}], "eq_spans": [], "section": "Model Original 4 Choices 3 Choices 2 Choices", "sec_num": null}, {"text": "Since typical MCQA benchmarks use k = 4, the brute force algorithm is cheaper than a gradient-based solution. But gradientbased solutions could be used if the attack needs to scale to substantially larger k.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "<PERSON><PERSON><PERSON> is supported by the United Kingdom Research and Innovation (grant EP/S02431X/1), UKRI Centre for Doctoral Training in Biomedical AI at the University of Edinburgh, School of Informatics. For the purpose of open access, the author has applied a creative commons attribution (CC BY) licence to any author accepted manuscript version arising.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgement", "sec_num": null}, {"text": "We present the additional results of the answer set pruning of all language and vision-language datasets in Table 11 to 16.", "cite_spans": [], "ref_spans": [{"start": 114, "end": 116, "text": "11", "ref_id": null}], "eq_spans": [], "section": "A.1. Additional Results on Answer Set Pruning", "sec_num": null}, {"text": "In this subsection, we investigate the effect of different prompting techniques on the models' vulnerability to MCQA. The findings are summarized below.• Table 17 presents the average number of permutations needed to break the predictions of ARC-Challenge and MedMCQA datasets. Observations are two fold: (1) stronger LLMs require a larger number of permutations to break, and (2) LLMs are easier to break on more difficult datasets (ARC-Challenge is relatively easier than MedMCQA for LLMs).• Table 18 and 19 analyze the reason for the seemingly counterintuitive ineffectiveness of the majority vote with Llama2-7B (decrease with majority vote) and InternLM-20B (increase with majority vote). The intuitive summary is as follows (consistent with (<PERSON> et al., 2024) ): For easy queries where an LLM call's output is correct more than 50% of the time, the probability of a correct majority vote goes 1 with infinite LLM calls. Conversely, for hard queries (e.g., with less than a 50% correctness rate), the majority vote's accuracy trends towards 0 as LLM calls increase.• Table 20 compares the performance before and after adversarial attack with in-context learning. Although in-context learning can improve the original performance, the models still suffer substantially from the performance drop after adversarial permutations.• Table 21 to Table 25 presents different attack strategies with in-context learning, i.e. permutation of in-context examples and searching for worst-case in-context examples. While they can decrease the performance, our adversarial attack has the biggest impact on the final performance and causes the largest performance drop.• Table 26 compares different sampling strategies and temperatures. The performance of the other decoding strategies is even worse before and after the permutations compared to the greedy decoding we adopted. Therefore we can ensure our experiments were conducted properly and the findings can generalize to other decoding strategies.• Table 27 to Table 29 presents the effect of in-context learning on the position bias. Our findings indicate that while the original model exhibited a preference for option B, this preference persisted even after introducing in-context examples with answers set to positions A, B, C, and D. This suggests that while in-context examples can modify the distribution across various options, they do not entirely override the inherent position bias of the model.", "cite_spans": [{"start": 747, "end": 766, "text": "(<PERSON> et al., 2024)", "ref_id": "BIBREF4"}], "ref_spans": [{"start": 160, "end": 162, "text": "17", "ref_id": null}, {"start": 500, "end": 502, "text": "18", "ref_id": null}, {"start": 1079, "end": 1081, "text": "20", "ref_id": null}, {"start": 1339, "end": 1341, "text": "21", "ref_id": null}, {"start": 1351, "end": 1353, "text": "25", "ref_id": null}, {"start": 1667, "end": 1669, "text": "26", "ref_id": null}, {"start": 2001, "end": 2003, "text": "27", "ref_id": null}, {"start": 2013, "end": 2015, "text": "29", "ref_id": null}], "eq_spans": [], "section": "A.2. Additional Results on Different Prompting and Attack Strategies", "sec_num": null}, {"text": "We present analysis on vision-language dataset A-OKVQA (<PERSON><PERSON><PERSON><PERSON> et al., 2022) in Table 30 and 31 about position bias and different strategies for mitigation. The additional analysis further ensures that our findings on LLMs can also be generalized to VLLMs.", "cite_spans": [{"start": 55, "end": 77, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF33"}], "ref_spans": [{"start": 87, "end": 89, "text": "30", "ref_id": null}], "eq_spans": [], "section": "A.3. Further Analysis on Vision-Language Dataset", "sec_num": null}, {"text": "To illustrate the permutation attack, we present qualitative results for LLMs in Table 32 and VLLMs in Fig. 3 .", "cite_spans": [], "ref_spans": [{"start": 87, "end": 89, "text": "32", "ref_id": null}, {"start": 108, "end": 109, "text": "3", "ref_id": null}], "eq_spans": [], "section": "A.4. Qualitative Results", "sec_num": null}, {"text": "In Table 32 , we showcase an MCQA example from the ARC-challenge dataset (<PERSON> et al., 2018) , with the original answer order alongside two permutations. The ground-truth answer is underlined in each configuration. We use Llama-13B for this experiment. The model gives the correct prediction for the original option order. For permutation 1, if we only swap the position of option C and D, i.e., moving the ground-truth position to C, the model can still successfully give the prediction. However, for permutation 2, even if we do not move the ground-truth answer but only swap option A and B, the model incorrectly predicts A as the answer. This qualitative example underscores that the model's vulnerability extends beyond mere positional bias and even minor changes in option ordering can result in completely different predictions.Vision-Language Models In Appendix Figure 3 , we present a visual MCQA example from ScienceQA dataset using Otter-Llama model. In this example, we simply move the ground truth \"Asia\" from option A to option C. However, the Analysis on permutation distribution. The histogram shows the number of questions for which the corresponding proportion of permutations leads to the correct answer (ideal is a full bar at the 100% bin, indicating that all permutations are correctly answered for all questions). The distribution of bins suggests that many questions have multiple adversarial permutations. 18 . Majority vote performance when considering the easy sample with respect to a certain model to be the ones where 50% of the permutations are correct. If we consider the easy sample with respect to a certain model to be the ones where 50% of the permutations are correct, the majority vote accuracy of easy samples will be 100%, and we calculate the accuracy of the majority of the difficult samples.For both Llama2-7B and InternLM-20B, the majority vote accuracies of difficult samples are very low. However, there are more easy samples wrt InternLM-20B compared to Llama2-7B, and therefore InternLM-20B overall improves with the majority vote while the accuracy of Llama2-7B decreases with the majority vote. ", "cite_spans": [{"start": 73, "end": 93, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF7"}], "ref_spans": [{"start": 9, "end": 11, "text": "32", "ref_id": null}, {"start": 878, "end": 879, "text": "3", "ref_id": null}, {"start": 1432, "end": 1434, "text": "18", "ref_id": null}], "eq_spans": [], "section": "Language Models", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Flamingo: a visual language model for few-shot learning", "authors": [{"first": "J.-B", "middle": [], "last": "Alayrac", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Luc", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Barr", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Len<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Millican", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Flamingo: a visual language model for few-shot learning. NeurIPS, 2022.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Palm 2 technical report", "authors": [{"first": "R", "middle": [], "last": "Anil", "suffix": ""}, {"first": "A", "middle": ["M"], "last": "Dai", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Passos", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.10403"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Passos, A., Shake<PERSON>, S., <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Palm 2 technical report. arXiv preprint arXiv:2305.10403, 2023.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "An opensource framework for training large autoregressive visionlanguage models", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Gao", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Han<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Bitton", "suffix": ""}, {"first": "S", "middle": [], "last": "Gadre", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": ["W"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Ilharco", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Openflamingo", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.01390"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, K<PERSON>, Bitton, Y., <PERSON>, S., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, L. <PERSON>f<PERSON>: An open- source framework for training large autoregressive vision- language models. arXiv preprint arXiv:2308.01390, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Language models are few-shot learners", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Subbiah", "suffix": ""}, {"first": "J", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Language models are few-shot learners. NeurIPS, 2020.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Are more llm calls all you need? towards scaling laws of compound inference systems", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["Q"], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Stoica", "suffix": ""}, {"first": "M", "middle": [], "last": "Zaharia", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Are more llm calls all you need? towards scaling laws of compound inference systems. arXiv, 2024.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Vicuna: An open-source chatbot impressing gpt-4 with 90%* chatgpt quality", "authors": [{"first": "W.-L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["E"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "14", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Vicuna: An open-source chatbot impressing gpt-4 with 90%* chatgpt quality. See https://vicuna. lmsys. org (accessed 14 April 2023), 2023.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Exploring the surprising difficulty of natural yes/no questions", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M.-W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Boolq", "suffix": ""}], "year": 2019, "venue": "NAACL", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>: Exploring the surprising difficulty of natural yes/no questions. In NAACL, 2019.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Think you have solved question answering? try arc, the ai2 reasoning challenge", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "K<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "Tafjord", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1803.05457"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Schoenick, C., and Tafjord, O. Think you have solved question answering? try arc, the ai2 reasoning challenge. arXiv preprint arXiv:1803.05457, 2018.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Towards general-purpose vision-language models with instruction tuning", "authors": [{"first": "W", "middle": [], "last": "Dai", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}, {"first": "D", "middle": [], "last": "Li", "suffix": ""}, {"first": "A", "middle": ["M <PERSON>"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Li", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "S", "middle": [], "last": "Hoi", "suffix": ""}, {"first": "", "middle": [], "last": "Instructblip", "suffix": ""}], "year": 2023, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>., and <PERSON>, S. Instructblip: Towards general-purpose vision-language models with instruction tuning. In NeurIPS, 2023.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Shortcut learning of large language models in natural language understanding", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "He", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Tao", "suffix": ""}, {"first": "X", "middle": [], "last": "Hu", "suffix": ""}], "year": null, "venue": "Communications of the ACM (CACM)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Shortcut learning of large language models in natural language understanding. Communications of the ACM (CACM), 2023.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Shortcut learning in deep neural networks", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J.-<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Brendel", "suffix": ""}, {"first": "M", "middle": [], "last": "Bethge", "suffix": ""}, {"first": "F", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Nature Machine Intelligence", "volume": "2", "issue": "11", "pages": "665--673", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>. Shortcut learn- ing in deep neural networks. Nature Machine Intelligence, 2(11):665-673, 2020.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Measuring massive multitask language understanding", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Ma<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Song", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2009.03300"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Measuring mas- sive multitask language understanding. arXiv preprint arXiv:2009.03300, 2020.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Low-rank adaptation of large language models. ICLR", "authors": [{"first": "E", "middle": ["J"], "last": "Hu", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Low-rank adaptation of large language models. ICLR, 2022.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Seed-bench: Benchmarking multimodal llms with generative comprehension", "authors": [{"first": "B", "middle": [], "last": "Li", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Ge", "suffix": ""}, {"first": "Y", "middle": [], "last": "Ge", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.16125"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Seed-bench: Benchmarking multimodal llms with gener- ative comprehension. arXiv preprint arXiv:2307.16125, 2023a.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "A multi-modal model with in-context instruction tuning", "authors": [{"first": "B", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Otter", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.03726"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: A multi-modal model with in-context instruction tuning. arXiv preprint arXiv:2305.03726, 2023b.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Blip-2: Bootstrapping language-image pre-training with frozen image encoders and large language models", "authors": [{"first": "J", "middle": [], "last": "Li", "suffix": ""}, {"first": "D", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Hoi", "suffix": ""}], "year": 2023, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Blip-2: Bootstrapping language-image pre-training with frozen image encoders and large language models. ICML, 2023c.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Holistic evaluation of language models", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.09110"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Holistic evaluation of language models. arXiv preprint arXiv:2211.09110, 2022.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Improved baselines with visual instruction tuning", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": ["J"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.03744"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y. J. Improved base- lines with visual instruction tuning. arXiv preprint arXiv:2310.03744, 2023a.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Visual instruction tuning", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Li", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": ["J"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.08485"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y. J. Visual instruction tuning. arXiv:2304.08485, 2023b.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "What makes good in-context examples for gpt-3?", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>in", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2101.06804"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. What makes good in-context examples for gpt-3? arXiv preprint arXiv:2101.06804, 2021.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Is your multi-modal model an all-around player? arXiv preprint", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "He", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.06281"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Mmbench: Is your multi-modal model an all-around player? arXiv preprint arXiv:2307.06281, 2023c.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Trustworthy llms: a survey and guideline for evaluating large language models' alignment", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J.-F", "middle": [], "last": "Ton", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": ["G H"], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["F"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Li", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.05374"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, M. <PERSON>, and <PERSON>, <PERSON><PERSON> llms: a survey and guideline for evaluating large language models' alignment. arXiv preprint arXiv:2308.05374, 2023d.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Learn to explain: Multimodal reasoning via thought chains for science question answering", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K.-W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S.-C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "Tafjord", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Kalyan", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "2507--2521", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S.<PERSON>, Tafjord, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> to explain: Multimodal reasoning via thought chains for science question answering. Advances in Neural Infor- mation Processing Systems, 35:2507-2521, 2022.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Towards deep learning models resistant to adversarial attacks", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Towards deep learning models resistant to adversarial attacks. In ICLR, 2018.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Linearly mapping from image to text space", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Castricato", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> mapping from image to text space. ICLR, 2023.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Gpt-4 technical report. arXiv", "authors": [{"first": "R", "middle": [], "last": "Openai", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "2303--08774", "other_ids": {}, "num": null, "urls": [], "raw_text": "OpenAI, R. Gpt-4 technical report. arXiv, pp. 2303-08774, 2023b.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Training language models to follow instructions with human feedback", "authors": [{"first": "L", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "D", "middle": [], "last": "Almeida", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Training language models to follow instructions with human feedback. Advances in Neural Information Processing Systems, 2022.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "A large-scale multi-subject multi-choice dataset for medical domain question answering", "authors": [{"first": "A", "middle": [], "last": "Pal", "suffix": ""}, {"first": "L", "middle": ["K"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Medmcqa", "suffix": ""}], "year": 2022, "venue": "Conference on Health, Inference, and Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, M. <PERSON>: A large-scale multi-subject multi-choice dataset for medi- cal domain question answering. In Conference on Health, Inference, and Learning. PMLR, 2022.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "The refinedweb dataset for falcon llm: outperforming curated corpora with web data, and web data only", "authors": [{"first": "G", "middle": [], "last": "Penedo", "suffix": ""}, {"first": "Q", "middle": [], "last": "Malartic", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Almaz<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Launay", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.01116"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, E., and <PERSON>, J. The refinedweb dataset for falcon llm: out- performing curated corpora with web data, and web data only. arXiv preprint arXiv:2306.01116, 2023.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Leveraging large language models for multiple choice question answering", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Wingate", "suffix": ""}], "year": 2023, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> Leveraging large language models for multiple choice question answering. In The Eleventh International Conference on Learning Repre- sentations, 2023.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "An investigation of why overparameterization exacerbates spurious correlations", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": ["W"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "8346--8356", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, P. An investigation of why overparameterization exacerbates spurious correlations. In International Conference on Machine Learning, pp. 8346-8356. PMLR, 2020.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Commonsense reasoning about social interactions", "authors": [{"first": "M", "middle": [], "last": "<PERSON>p", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Le Bras", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Iqa", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP-IJCNLP)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y. Social IQa: Commonsense reasoning about social interactions. In Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP-IJCNLP), 2019.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "A-okvqa: A benchmark for visual question answering using world knowledge", "authors": [{"first": "D", "middle": [], "last": "Schwenk", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Marino", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "European Conference on Computer Vision", "volume": "", "issue": "", "pages": "146--162", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, R. A-okvqa: A benchmark for visual ques- tion answering using world knowledge. In European Conference on Computer Vision, pp. 146-162. Springer, 2022.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Large language models can be easily distracted by irrelevant context", "authors": [{"first": "F", "middle": [], "last": "Shi", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Scales", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Sch<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "31210--31227", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, D. Large language models can be easily distracted by irrelevant context. In Inter- national Conference on Machine Learning, pp. 31210- 31227. PMLR, 2023.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Generative multimodal models are in-context learners", "authors": [{"first": "Q", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Generative multimodal models are in-context learners. CVPR, 2024.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Internlm: A multilingual language model with progressively enhanced capabilities", "authors": [{"first": "I", "middle": [], "last": "Team", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Team, I. Internlm: A multilingual language model with pro- gressively enhanced capabilities. https://github. com/InternLM/InternLM, 2023a.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Introducing mpt-7b: A new standard for open-source, commercially usable llms", "authors": [{"first": "M", "middle": ["N"], "last": "Team", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Team, M. N. Introducing mpt-7b: A new standard for open-source, commercially usable llms, 2023b. URL www.mosaicml.com/blog/mpt-7b.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Open and efficient foundation language models", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "I<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M.-A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "E", "middle": [], "last": "Hambro", "suffix": ""}, {"first": "F", "middle": [], "last": "Azhar", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.13971"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, T<PERSON>, <PERSON>, B., Goyal, N., <PERSON>, E<PERSON>, <PERSON>, <PERSON>, et al. Llama: Open and efficient foundation lan- guage models. arXiv preprint arXiv:2302.13971, 2023a.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Llama 2: Open foundation and finetuned chat models", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Stone", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Batra", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09288"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N., Batra, S., Bharga<PERSON>, P., <PERSON>, S., et al. Llama 2: Open foundation and fine- tuned chat models. arXiv preprint arXiv:2307.09288, 2023b.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Selfconsistency improves chain of thought reasoning in language models", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": ["V"], "last": "Le", "suffix": ""}, {"first": "E", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Chowdhery", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>. Self- consistency improves chain of thought reasoning in lan- guage models. In The Eleventh International Confer- ence on Learning Representations, 2023. URL https: //openreview.net/forum?id=1PL1NIMMrw.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Empowering large language models to follow complex instructions", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "Sun", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Geng", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Tao", "suffix": ""}, {"first": "D", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.12244"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>., and <PERSON>, <PERSON><PERSON>: Empowering large language models to follow complex instructions. arXiv preprint arXiv:2304.12244, 2023.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "A survey on multimodal large language models", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": [], "last": "Sun", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.13549"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, E. A survey on multimodal large language models. arXiv preprint arXiv:2306.13549, 2023.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Calibrate before use: Improving few-shot performance of language models", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Cal<PERSON>rate before use: Improving few-shot performance of language models. In ICML, 2021.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "On large language models' selection bias in multi-choice questions", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.03882"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. On large language models' selection bias in multi-choice questions. arXiv preprint arXiv:2309.03882, 2023a.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Judging llm-as-a-judge with mt-bench and chatbot arena", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W.-L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "D", "middle": [], "last": "Li", "suffix": ""}, {"first": "E", "middle": ["P"], "last": "Xi<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Stoica", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Judging llm-as-a-judge with mt-bench and chatbot arena, 2023b.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Agieval: A human-centric benchmark for evaluating foundation models", "authors": [{"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.06364"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: A human-centric benchmark for evaluating foundation mod- els. arXiv preprint arXiv:2304.06364, 2023.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Promptbench: Towards evaluating the robustness of large language models on adversarial prompts", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Ye", "suffix": ""}, {"first": "N", "middle": ["Z"], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.04528"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Prompt- bench: Towards evaluating the robustness of large lan- guage models on adversarial prompts. arXiv preprint arXiv:2306.04528, 2023.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Selfsupervised multimodal learning: A survey", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "arXiv", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Self- supervised multimodal learning: A survey. arXiv, 2023.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Comparisons of different attacks on the MMLU Dataset. In-context learning (ICL) improves the zero-shot performance, and attacks on in-context examples can decrease the performance. However, our adversarial attack has the biggest impact on the final performance (largest drop). Model Original 0-shot ICL ICL Permutation ICL Search Permutation Attack", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Table 21. Comparisons of different attacks on the MMLU Dataset. In-context learning (ICL) improves the zero-shot performance, and attacks on in-context examples can decrease the performance. However, our adversarial attack has the biggest impact on the final performance (largest drop). Model Original 0-shot ICL ICL Permutation ICL Search Permutation Attack", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "InternLM-20B", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "InternLM-20B", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Comparisons of different attacks on the ARC-Challenge Dataset. In-context learning (ICL) improves the zero-shot performance, and the attack on in-context examples can decrease the performance. However, our adversarial attack has the biggest impact on the final performance (largest performance drop). Model Original 0-shot ICL ICL Permutation ICL Search Permutation Attack", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Table 22. Comparisons of different attacks on the ARC-Challenge Dataset. In-context learning (ICL) improves the zero-shot performance, and the attack on in-context examples can decrease the performance. However, our adversarial attack has the biggest impact on the final performance (largest performance drop). Model Original 0-shot ICL ICL Permutation ICL Search Permutation Attack", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Comparisons of different attacks on the BoolQ Dataset. In-context learning (ICL) improves the zero-shot performance, and the attack on in-context examples can decrease the performance. However, our adversarial attack has the biggest impact on the final performance (largest performance drop). Model Original 0-shot ICL ICL Permutation ICL Search Permutation Attack", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Table 23. Comparisons of different attacks on the BoolQ Dataset. In-context learning (ICL) improves the zero-shot performance, and the attack on in-context examples can decrease the performance. However, our adversarial attack has the biggest impact on the final performance (largest performance drop). Model Original 0-shot ICL ICL Permutation ICL Search Permutation Attack", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "InternLM-7B 65", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "InternLM-7B 65.83", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Comparisons of different attacks on the SocialIQA Dataset. In-context learning (ICL) improves the zero-shot performance, and the attack on in-context examples can decrease the performance. However, our adversarial attack has the biggest impact on the final performance (largest performance drop). Model Original 0-shot ICL ICL Permutation ICL Search Permutation Attack", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Table 24. Comparisons of different attacks on the SocialIQA Dataset. In-context learning (ICL) improves the zero-shot performance, and the attack on in-context examples can decrease the performance. However, our adversarial attack has the biggest impact on the final performance (largest performance drop). Model Original 0-shot ICL ICL Permutation ICL Search Permutation Attack", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Comparisons of different attacks on the MedMCQA Dataset. In-context learning (ICL) improves the zero-shot performance, and the attack on in-context examples can decrease the performance. However, our adversarial attack has the biggest impact on the final performance (largest performance drop). Model Original 0-shot ICL ICL Permutation ICL Search Permutation Attack", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Table 25. Comparisons of different attacks on the MedMCQA Dataset. In-context learning (ICL) improves the zero-shot performance, and the attack on in-context examples can decrease the performance. However, our adversarial attack has the biggest impact on the final performance (largest performance drop). Model Original 0-shot ICL ICL Permutation ICL Search Permutation Attack", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Language Model with Embarrassingly Simple Permutations Table 26. Results of using different sampling strategies and temperatures on MMLU dataset: before/after permutation. Model Greedy Decoding Temperature=0", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Fool Your (Vision and) Language Model with Embarrassingly Simple Permutations Table 26. Results of using different sampling strategies and temperatures on MMLU dataset: before/after permutation. Model Greedy Decoding Temperature=0.5 Temperature=1.5 Top-k Sampling Nucleus Sampling", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "InstructBLIP-13B", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "InstructBLIP-13B", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "LLaVA-13B", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "LLaVA-13B", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Impact of majority vote, contextual calibration (C-Calibration), and maximum confidence (M-Confidence) defenses against the permutation attack on the A-OKVQA dataset. Contextual calibration fails completely. Majority vote and M-Confidence ameliorates the attack, but do not completely restore performance. Red shading indicates below-chance results. Method Original Adversarial Attack Majority Vote C-Calibration M-Confidence", "authors": [], "year": null, "venue": "", "volume": "27", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Table 31. Impact of majority vote, contextual calibration (C-Calibration), and maximum confidence (M-Confidence) defenses against the permutation attack on the A-OKVQA dataset. Contextual calibration fails completely. Majority vote and M-Confidence ameliorates the attack, but do not completely restore performance. Red shading indicates below-chance results. Method Original Adversarial Attack Majority Vote C-Calibration M-Confidence InstructBLIP-7B 74.06 51.62 (22.44 ↓) 57.47 (16.59 ↓) 38.12 (35.94 ↓) 69.79(4.27 ↓)", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Question: A physicist wants to determine the speed a car must reach to jump over a ramp. The physicist conducts three trials. In trials two and three, the speed of the car is increased by 20 miles per hour. What is the physicist investigating when he changes the speed? True Answer: the independent (manipulated) variable", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Table 32. Qualitative results of permutations of answer options and corresponding predictions (Llama2-7B) from ARC-challenge dataset. Question: A physicist wants to determine the speed a car must reach to jump over a ramp. The physicist conducts three trials. In trials two and three, the speed of the car is increased by 20 miles per hour. What is the physicist investigating when he changes the speed? True Answer: the independent (manipulated) variable. Original Answer Set: A. the control B. the hypothesis statement C. the dependent (responding) variable D. the independent (manipulated) variable. Model Prediction: D. Permutation 1: A. the control B. the hypothesis statement C. the independent (manipulated) variable D. the dependent (responding) variable Model Prediction: C. Permutation 2: A. the hypothesis statement B. the control C. the dependent (responding) variable D. the independent (manipulated) variable. Model Prediction: A.", "links": null}}, "ref_entries": {"FIGREF0": {"text": "Figure 1. a. Schematic Illustration of an MCQA permutation attack. b. <PERSON><PERSON> of MCQA adversarial attack results for both LLMs and VLLMs. The values are average accuracy across all benchmarking datasets.", "num": null, "uris": null, "fig_num": "1", "type_str": "figure"}, "TABREF0": {"text": "Statistics of the language datasets evaluated.", "num": null, "html": null, "content": "<table><tr><td/><td colspan=\"2\"># Choices # QA Pairs</td><td>Task</td></tr><tr><td>MMLU</td><td>4</td><td>14079</td><td>Aggregated</td></tr><tr><td>ARC-c</td><td>4</td><td>1165</td><td>Commonsense Reasoning</td></tr><tr><td>BoolQ</td><td>2</td><td>3270</td><td>Reading Comprehension</td></tr><tr><td>SocialiQA</td><td>3</td><td>1954</td><td>Commonsense Reasoning</td></tr><tr><td>MedMCQ</td><td>4</td><td>2816</td><td>Out-of-domain</td></tr></table>", "type_str": "table"}, "TABREF1": {"text": "Statistics of the vision-language datasets evaluated.", "num": null, "html": null, "content": "<table><tr><td/><td colspan=\"2\"># Choices # QA pairs</td><td>Task</td></tr><tr><td>ScienceQA</td><td>2,3,4,5</td><td>2021</td><td>Scientific QA</td></tr><tr><td>A-OKVQA</td><td>4</td><td>1145</td><td>Commonsense Reasoning</td></tr><tr><td>MMBench</td><td>4</td><td>4377</td><td>Aggregated</td></tr><tr><td>SEED-Bench</td><td>4</td><td>14233</td><td>Aggregated</td></tr></table>", "type_str": "table"}, "TABREF2": {"text": "Performance comparisons of LLMs before and after adversarial attack. Numbers in each represent original accuracy, accuracy after adversarial attack, and relative performance drop. Red shading indicates experiments where the permutation attack reduced performance below chance level. All models suffer substantially with most experiments leading to below chance performance.", "num": null, "html": null, "content": "<table><tr><td>Method</td><td>MMLU</td><td>ARC-c</td><td>BoolQ</td><td>SocialiQA</td><td>MedMCQA</td></tr><tr><td>Llama2-7B</td><td colspan=\"5\">40.91/ 6.17 (34.74 ↓) 47.04/ 7.98 (39.06 ↓) 61.79/ 8.23 (53.56 ↓) 52.00/15.71 (36.29 ↓) 37.96/ 1.60 (36.36 ↓)</td></tr><tr><td>Llama2-13B</td><td colspan=\"5\">52.22/18.33 (33.89 ↓) 61.80/21.63 (40.17 ↓) 67.16/38.29 (28.87 ↓) 61.21/34.14 (27.07 ↓) 39.78/ 7.35 (32.43 ↓)</td></tr><tr><td>Llama2-70B</td><td colspan=\"5\">64.68/33.16 (31.52 ↓) 80.00/51.50 (28.50 ↓) 76.39/56.21 (20.18 ↓) 71.60/49.85 (21.75 ↓) 49.61/ 7.35 (32.43 ↓)</td></tr><tr><td>Vicuna-v1.5</td><td colspan=\"5\">48.57/18.09 (30.48 ↓) 58.37/23.43 (34.94 ↓) 64.04/29.60 (34.44 ↓) 64.99/38.33 (26.66 ↓) 39.28/ 7.67 (31.61 ↓)</td></tr><tr><td colspan=\"6\">Vicuna-v1.5-13B 54.68/26.27 (28.41 ↓) 69.27/38.80 (30.47 ↓) 68.96/42.14 (26.82 ↓) 66.07/44.42 (21.65 ↓) 41.80/11.90 (29.90 ↓)</td></tr><tr><td>WizardLM-13B</td><td colspan=\"5\">48.60/15.87 (32.73 ↓) 58.20/21.12 (37.08 ↓) 67.49/42.11 (25.38 ↓) 63.46/31.78 (31.68 ↓) 34.87/ 6.32 (28.55 ↓)</td></tr><tr><td>InternLM-7B</td><td colspan=\"5\">45.72/10.45 (35.27 ↓) 56.14/17.34 (38.80 ↓) 65.83/26.41 (39.42 ↓) 59.47/30.30 (29.17 ↓) 32.63/ 2.56 (30.07 ↓)</td></tr><tr><td>InternLM-20B</td><td colspan=\"5\">59.14/29.52 (29.62 ↓) 78.28/54.42 (23.86 ↓) 85.20/82.91 ( 2.29 ↓) 79.48/65.97 (13.51 ↓) 43.61/13.92 (29.69 ↓)</td></tr><tr><td>Falcon-7b</td><td colspan=\"5\">31.66/ 2.49 (29.17 ↓) 34.74/ 0.09 (34.65 ↓) 55.35/ 2.66 (52.69 ↓) 36.29/ 0.55 (35.74 ↓) 28.12/ 0.07 (28.05 ↓)</td></tr><tr><td>MPT-7B</td><td colspan=\"5\">35.60/ 3.52 (32.08 ↓) 37.76/ 1.06 (36.70 ↓) 58.46/ 7.03 (51.43 ↓) 41.61/ 2.53 (39.08 ↓) 26.31/ 1.60 (24.71 ↓)</td></tr><tr><td>GPT-3.5-turbo</td><td colspan=\"5\">64.81/40.39 (24.42 ↓) 82.23/61.55 (20.68 ↓) 87.92/81.35 ( 6.57 ↓) 70.62/56.29 (14.33 ↓) 52.22/32.07 (20.15 ↓)</td></tr><tr><td colspan=\"2\">Random Chance 25.0</td><td>25.0</td><td>50.0</td><td>33.33</td><td>25.0</td></tr></table>", "type_str": "table"}, "TABREF3": {"text": "Performance comparisons of VLLMs before and after adversarial attack. Numbers in each cell represent original accuracy, accuracy after adversarial attack, and relative performance drop. Red shading indicates performance below chance level after the permutation attack. All models suffer substantially with most experiments leading to below chance performance.", "num": null, "html": null, "content": "<table><tr><td>Method</td><td>ScienceQA</td><td>A-OKVQA</td><td>SEED-Bench</td><td>M<PERSON>ench</td></tr><tr><td>InstructBLIP-7B</td><td colspan=\"4\">59.46/33.31 (26.15 ↓) 74.06/51.62 (22.44 ↓) 51.61/25.68 (25.93 ↓) 64.91/41.01 (23.90 ↓)</td></tr><tr><td>InstructBLIP-13B</td><td colspan=\"4\">64.15/41.84 (22.31 ↓) 77.90/55.38 (22.52 ↓) 53.65/28.79 (24.86 ↓) 67.12/45.49 (21.63 ↓)</td></tr><tr><td>OpenFlamingo</td><td>39.43/1.37 (38.06 ↓)</td><td>46.90/3.58 (43.32 ↓)</td><td colspan=\"2\">37.99/0.87 (37.12 ↓) 38.99/5.18 (33.81 ↓)</td></tr><tr><td>Otter-Llama7B</td><td colspan=\"3\">59.92/32.54 (27.38 ↓) 57.99/28.30 (29.69 ↓) 40.77/9.91 (30.86 ↓)</td><td>55.24/19.67 (35.57 ↓)</td></tr><tr><td>Otter-MPT7B</td><td colspan=\"4\">63.11/31.38 (31.73 ↓) 68.21/43.19 (25.02 ↓) 46.76/10.82 (35.94 ↓) 61.31/36.46 (24.85 ↓)</td></tr><tr><td>LLaVA-7B</td><td>45.20/2.28 (42.92 ↓)</td><td colspan=\"2\">52.91/ 0.09 (52.82 ↓) 38.36/5.67 (43.03 ↓)</td><td>46.03/5.07 (40.96 ↓)</td></tr><tr><td>LLaVA-13B</td><td colspan=\"4\">60.63/46.53 (14.10 ↓) 63.14/25.85 (37.29 ↓) 44.00/13.68 (30.32 ↓) 59.13/31.30 (27.83 ↓)</td></tr><tr><td>LLaVA-v1.5-7B</td><td colspan=\"4\">67.78/45.61 (22.17 ↓) 81.31/36.24 (45.07 ↓) 59.17/12.44 (46.73 ↓) 69.57/27.39 (42.18 ↓)</td></tr><tr><td>LLaVA-v1.5-13B</td><td colspan=\"4\">71.60/52.55 (19.05 ↓) 83.32/47.25 (36.07 ↓) 61.50/18.95 (42.55 ↓) 72.33/58.42 (13.91 ↓)</td></tr><tr><td>Limber</td><td colspan=\"2\">49.33/14.03 (35.30 ↓) 39.57/1.22 (38.35 ↓)</td><td>31.50/0.26 (31.24 ↓)</td><td>34.93/1.62 (33.31 ↓)</td></tr><tr><td>mPLUG-Owl-pt</td><td colspan=\"2\">53.24/10.20 (43.04 ↓) 39.91/1.83 (38.08 ↓)</td><td>35.57/0.91 (34.66 ↓)</td><td>42.57/8.54 (34.03 ↓)</td></tr><tr><td colspan=\"3\">mPLUG-Owl-instr 54.87/11.43 (43.44 ↓) 37.12/2.01 (35.11 ↓)</td><td>36.74/2.72 (34.02 ↓)</td><td>43.74/6.12 (37.62 ↓)</td></tr><tr><td>Emu2-Chat</td><td colspan=\"4\">64.60/44.27 (20.33↓) 81.91/63.67 (18.24↓) 62.11/38.02 (24.09↓) 73.21/52.44 (20.77↓)</td></tr><tr><td>Random Chance</td><td>Min 20.0</td><td>25.0</td><td>25.0</td><td>25.0</td></tr></table>", "type_str": "table"}, "TABREF4": {"text": "Performance of LLMs on the MMLU dataset under answer set pruning. Numbers in each cell represent original accuracy, accuracy after adversarial attack, and relative performance drop. Baseline performances improve as the number of distractors is reduced, but performance is reduced below chance after adversarial permutation.", "num": null, "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">4 Choices 3 Choices</td><td>2 Choices</td></tr><tr><td>Llama2-7B</td><td>40.91</td><td colspan=\"2\">48.75/ 8.67 (39.08↓) 63.33/20.26 (43.07↓)</td></tr><tr><td>Llama2-13B</td><td>52.22</td><td colspan=\"2\">70.77/22.85 (47.92↓) 71.13/31.85 (39.28↓)</td></tr><tr><td>Llama2-70B</td><td>64.68</td><td colspan=\"2\">69.90/35.34 (34.56↓) 75.23/45.88 (29.35↓)</td></tr><tr><td>Vicuna-v1.5-7B</td><td>48.57</td><td colspan=\"2\">56.65/30.60 (26.97↓) 68.81/32.60 (36.21↓)</td></tr><tr><td colspan=\"2\"><PERSON>una-v1.5-13B 54.68</td><td colspan=\"2\">61.75/29.02 (32.66↓) 72.97/28.06 (44.91↓)</td></tr><tr><td>WizardLM-13B</td><td>48.60</td><td colspan=\"2\">56.57/17.74 (38.83↓) 69.09/28.96 (40.13↓)</td></tr><tr><td>InternLM-7B</td><td>45.72</td><td colspan=\"2\">51.76/12.39 (39.37↓) 65.88/19.65 (46.23↓)</td></tr><tr><td>InternLM-20B</td><td>59.14</td><td colspan=\"2\">65.25/30.48 (34.67↓) 76.09/43.51 (32.58↓)</td></tr><tr><td>Falcon-7b</td><td>31.66</td><td colspan=\"2\">52.88/ 5.92 (46.96↓) 58.31/11.41 (46.90↓)</td></tr><tr><td>MPT-7B</td><td>35.60</td><td colspan=\"2\">53.31/ 6.27 (47.03↓) 58.31/15.44 (42.87↓)</td></tr><tr><td>GPT-3.5-turbo</td><td>64.81</td><td colspan=\"2\">70.80/42.99 (27.81↓) 79.30/50.82 (28.48↓)</td></tr><tr><td colspan=\"2\">Random Chance 25.0</td><td>33.33</td><td>50.0</td></tr><tr><td colspan=\"4\">2023c) and symbol attack. Specifically, CircularEval in-</td></tr><tr><td colspan=\"4\">volves rotating options while maintaining their relative po-</td></tr></table>", "type_str": "table"}, "TABREF5": {"text": "Comparison of positional bias, circular evaluation, symbol attack, and our adversarial permutation on MMLU dataset. Position bias and other attacks have moderate impact. In contrast, our adversarial permutation severely degrades performance, usually below random chance level.", "num": null, "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">Original A</td><td>B</td><td>C</td><td>D</td><td colspan=\"2\">CircularEval Symbol Attack Permutation Attack</td></tr><tr><td>Llama2-7B</td><td>40.91</td><td colspan=\"4\">60.02 37.28 30.69 35.43</td><td>27.26</td><td>25.70</td><td>6.17</td></tr><tr><td>Llama2-13B</td><td>52.22</td><td colspan=\"4\">36.15 58.69 59.08 54.91</td><td>35.80</td><td>30.76</td><td>18.33</td></tr><tr><td>Llama2-70B</td><td>64.68</td><td colspan=\"4\">63.63 64.28 67.45 62.43</td><td>48.18</td><td>47.40</td><td>33.16</td></tr><tr><td>Vicuna-7B</td><td>48.57</td><td colspan=\"4\">49.83 63.22 45.46 37.85</td><td>20.23</td><td>33.85</td><td>18.09</td></tr><tr><td>Vicuna-13B</td><td>54.68</td><td colspan=\"4\">47.33 70.00 51.73 52.04</td><td>41.42</td><td>45.40</td><td>26.27</td></tr><tr><td colspan=\"2\">WizardLM-13B 48.60</td><td colspan=\"4\">34.75 56.38 45.86 57.56</td><td>22.42</td><td>29.07</td><td>15.87</td></tr><tr><td>InternLM-7B</td><td>45.72</td><td colspan=\"4\">37.23 65.12 41.49 42.33</td><td>25.23</td><td>29.38</td><td>10.45</td></tr><tr><td>InternLM-20B</td><td>59.14</td><td colspan=\"4\">51.05 68.75 53.47 62.35</td><td>34.99</td><td>47.06</td><td>29.52</td></tr><tr><td>Falcon-7B</td><td>31.66</td><td>70.86</td><td colspan=\"3\">3.77 10.52 14.85</td><td>7.69</td><td>14.38</td><td>2.49</td></tr><tr><td>MPT-7B</td><td>35.60</td><td colspan=\"3\">0.82 75.35 34.72</td><td>2.03</td><td>2.44</td><td>21.62</td><td>3.52</td></tr><tr><td>GPT-3.5-turbo</td><td>64.81</td><td colspan=\"4\">65.84 67.77 73.81 56.55</td><td>58.21</td><td>63.99</td><td>40.39</td></tr><tr><td colspan=\"6\">ability. Figure 2 illustrates these dynamics, showcasing</td><td/></tr><tr><td colspan=\"6\">the Llama2-13B model's predictions and correlation across</td><td/></tr><tr><td>various symbol sets.</td><td/><td/><td/><td/><td/><td/></tr></table>", "type_str": "table"}, "TABREF6": {"text": "Comparisons of Pearson correlation scores of different symbol sets on ARC-Challenge dataset averaged over different permutations.", "num": null, "html": null, "content": "<table><tr><td/><td colspan=\"2\">Symbol Set</td><td>Correlation</td><td>Original Acc.</td><td>Permuted Acc.</td></tr><tr><td/><td colspan=\"2\">Capital vs. Lowercase</td><td>0.76</td><td>55.06 vs. 54.87 23.73 vs. 21.68</td></tr><tr><td/><td colspan=\"2\">Capital vs. Roman</td><td>0.36</td><td>55.06 vs. 52.49 23.73 vs. 19.33</td></tr><tr><td/><td/><td/><td colspan=\"2\">Llama2-13B Correlation at Each Permutation</td></tr><tr><td/><td>0.8</td><td/><td/><td>Capitals vs Lowers Capitals vs Roman</td></tr><tr><td>Pearson Correlation</td><td>0.4 0.6</td><td/><td/></tr><tr><td/><td>0.2</td><td/><td/></tr><tr><td/><td>0.0</td><td colspan=\"3\">Position 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24</td></tr><tr><td colspan=\"5\">Figure 2. The correlation analysis of Llama2-13B model's predic-</td></tr><tr><td colspan=\"5\">tions across different pairs of options symbols of each permutation</td></tr><tr><td colspan=\"5\">reveals a notable finding: the low correlation score between permu-</td></tr></table>", "type_str": "table"}, "TABREF7": {"text": "Impact of majority vote, contextual calibration (C-Calibration), and maximum confidence (M-Confidence) defenses against the permutation attack on the MMLU dataset. Contextual calibration fails completely. Majority vote and M-Confidence ameliorate the attack, but do not completely restore performance. Red shading indicates below-chance results.", "num": null, "html": null, "content": "<table><tr><td>Method</td><td colspan=\"4\">Original Permutation Attack Majority Vote C-Calibration M-Confidence</td></tr><tr><td>Llama2-7B</td><td>40.91</td><td>6.17 (34.74 ↓)</td><td>33.64 (7.27 ↓)</td><td>5.24 (35.67 ↓) 22.62 (18.29 ↓)</td></tr><tr><td>Llama2-13B</td><td>52.22</td><td>18.33 (33.89 ↓)</td><td>48.53 (3.69 ↓)</td><td>20.02 (32.20 ↓) 50.83 (1.39 ↓)</td></tr><tr><td>Llama2-70B</td><td>64.68</td><td>33.16 (31.52 ↓)</td><td>65.37 (0.69 ↑)</td><td>35.77 (28.91 ↓) 64.20 (0.48 ↓)</td></tr><tr><td>Vicuna-v1.5-7B</td><td>48.57</td><td>18.09 (30.48 ↓)</td><td>44.10 (4.47 ↓)</td><td>11.33 (37.24 ↓) 38.29 (10.28 ↓)</td></tr><tr><td colspan=\"2\">Vicuna-v1.5-13B 54.68</td><td>26.27 (28.41 ↓)</td><td>52.03 (2.65 ↓)</td><td>18.10 (36.58 ↓) 55.58 (0.90 ↑)</td></tr><tr><td>WizardLM-13B</td><td>48.60</td><td>15.87 (32.73 ↓)</td><td>30.17 (18.43 ↓)</td><td>8.23 (40.37 ↓) 37.81 (11.21 ↓)</td></tr><tr><td>InternLM-20B</td><td>59.14</td><td>29.52 (29.62 ↓)</td><td>60.33 (1.19 ↑)</td><td>28.94 (30.20 ↓) 64.80 (5.66 ↑)</td></tr><tr><td>Falcon-7b</td><td>31.66</td><td>2.49 (29.17 ↓)</td><td>4.38 (27.28 ↓)</td><td>3.59 (28.07 ↓) 21.10 (10.56 ↓)</td></tr><tr><td>MPT-7B</td><td>35.60</td><td>3.52 (32.08 ↓)</td><td>13.80 (21.80 ↓)</td><td>6.24 (29.36 ↓) 21.42 (14.18 ↓)</td></tr></table>", "type_str": "table"}, "TABREF8": {"text": "Baseline and permuted accuracy with PriDe on MMLU. Pride cannot alleviate the vulnerability caused by full permutation.", "num": null, "html": null, "content": "<table><tr><td>Models</td><td>PriDe (Baseline/Permuted Accuracy)</td></tr><tr><td>Llama2-7B</td><td>42.60/ 6.43 (36.17↓)</td></tr><tr><td>Llama2-13B</td><td>52.41/19.26 (33.15↓)</td></tr><tr><td>Vicuna-7B</td><td>49.37/18.42 (30.95↓)</td></tr><tr><td>Vicuna-13B</td><td>55.27/27.68 (27.59↓)</td></tr></table>", "type_str": "table"}, "TABREF9": {"text": "Comparison of baseline and permuted accuracy for different fine-tuning strategies across two benchmarks with Llama2-7B. Fine-tuning with permutation can enhance the robustness to the permutation attacks compared to the zero-shot and regular fine-tuning baseline.", "num": null, "html": null, "content": "<table><tr><td>Fine-tuning Strategy</td><td>ARC-Challenge</td><td>MedMCQA</td></tr><tr><td>Zero-shot</td><td>47.04/ 7.98 (39.06↓)</td><td>37.96/ 1.60 (36.36↓)</td></tr><tr><td>Regular Fine-tuning</td><td colspan=\"2\">51.42/15.02 (36.40↓) 45.03/14.38 (30.65↓)</td></tr><tr><td colspan=\"3\">Fine-tuning with Permutation 67.64/47.73 (19.91↓) 46.78/26.07 (20.71↓)</td></tr></table>", "type_str": "table"}, "TABREF10": {"text": "Results of answer set pruning on A-OKVQA dataset. Numbers in each cell represent original accuracy, accuracy after adversarial permutation attack, and relative performance drop.", "num": null, "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">4 Choices 3 Choices</td><td>2 Choices</td></tr><tr><td>InstructBLIP7B</td><td>74.06</td><td colspan=\"2\">79.21/45.92 (33.29↓) 85.07/54.85 (30.22↓)</td></tr><tr><td>InstructBLIP13B</td><td>77.90</td><td colspan=\"2\">81.66/48.33 (33.33↓) 88.56/56.52 (32.04↓)</td></tr><tr><td>OpenFlamingo</td><td>46.90</td><td>54.18/4.88 (49.30↓)</td><td>66.90/5.09 (61.81↓)</td></tr><tr><td>Otter-Llama7B</td><td>57.99</td><td colspan=\"2\">64.98/33.10 (31.88↓) 75.02/39.74 (35.28↓)</td></tr><tr><td>Otter-MPT7B</td><td>68.21</td><td colspan=\"2\">76.16/46.11 (30.05↓) 81.48/51.44 (30.04↓)</td></tr><tr><td>Llava-7B</td><td>52.91</td><td>42.86/9.44 (33.42↓)</td><td>63.55/12.90 (50.65↓)</td></tr><tr><td>Llava-13B</td><td>63.14</td><td colspan=\"2\">71.09/33.37 (37.72↓) 76.24/41.22 (35.02↓)</td></tr><tr><td>Limber</td><td>39.91</td><td>49.69/4.54 (45.15↓)</td><td>65.68/18.08 (47.60↓)</td></tr><tr><td>mPLUG-Owl-pt</td><td>39.91</td><td>45.59/4.95 (40.64↓)</td><td>56.42/10.57 (45.85↓)</td></tr><tr><td>mPLUG-Owl-instr</td><td>37.12</td><td>47.86/5.15 (42.71↓)</td><td>58.92/16.77 (42.15↓)</td></tr><tr><td>Random Chance</td><td>25.0</td><td>33.33</td><td>50.0</td></tr></table>", "type_str": "table"}, "TABREF11": {"text": "Results of answer set pruning on SEED-Bench dataset. Numbers in each cell represent original accuracy, accuracy after adversarial permutation attack, and relative performance drop.", "num": null, "html": null, "content": "<table/>", "type_str": "table"}, "TABREF12": {"text": "Performance (original/majority vote) Easy sample Acc. (# samples) Difficult sample Acc. (# samples)", "num": null, "html": null, "content": "<table><tr><td>Llama2-7B</td><td>40.91/33.64</td><td>46.08 (10252)</td><td>0 (3790)</td></tr><tr><td>InternLM-20B</td><td>59.14/60.33</td><td>75.28 (11530)</td><td>0 (2512)</td></tr></table>", "type_str": "table"}, "TABREF13": {"text": "Performance comparisons of LLMs before and after adversarial attack with in-context learning prompt. Numbers in each represent original accuracy, accuracy after adversarial attack, and relative performance drop. Red shading indicates experiments where the permutation attack reduced performance below chance level. All models suffer substantially with most experiments leading to below chance performance.", "num": null, "html": null, "content": "<table><tr><td>Method</td><td>MMLU</td><td>ARC-c</td><td>BoolQ</td><td>SocialiQA</td><td>MedMCQA</td></tr></table>", "type_str": "table"}, "TABREF14": {"text": "Comparisons of position bias of Vicuna-13B with setting ground truth answers of in-context examples to specific positions. Moving ICL answers to A 57.98 63.28 55.28 47.37 Moving ICL answers to B 58.13 61.39 56.22 49.02 Moving ICL answers to C 56.81 63.61 54.49 48.68 Moving ICL answers to D 58.02 60.64 54.45 50.99", "num": null, "html": null, "content": "<table><tr><td/><td>A</td><td>B</td><td>C</td><td>D</td></tr><tr><td>Original positional bias</td><td colspan=\"4\">47.33 70.00 51.73 52.04</td></tr></table>", "type_str": "table"}, "TABREF15": {"text": "Comparisons of position bias of InternLM-7B with setting ground truth answers of in-context examples to specific positions. Moving ICL answers to A 31.55 74.76 44.70 43.42 Moving ICL answers to B 38.71 73.49 43.94 42.47 Moving ICL answers to C 31.58 69.05 49.27 46.47 Moving ICL answers to D 32.63 69.39 44.34 51.22", "num": null, "html": null, "content": "<table><tr><td/><td>A</td><td>B</td><td>C</td><td>D</td></tr><tr><td>Original positional bias</td><td colspan=\"4\">45.72 37.23 65.12 41.49</td></tr></table>", "type_str": "table"}, "TABREF16": {"text": "Comparisons of position bias of InternLM-20B with setting ground truth answers of in-context examples to specific positions. Moving ICL answers to A 51.33 72.52 62.81 55.29 Moving ICL answers to B 49.70 73.07 64.34 56.31 Moving ICL answers to C 48.01 70.00 64.36 60.61 Moving ICL answers to D 46.99 67.54 62.20 65.72", "num": null, "html": null, "content": "<table><tr><td/><td>A</td><td>B</td><td>C</td><td>D</td></tr><tr><td>Original positional bias</td><td colspan=\"4\">51.05 68.75 53.47 62.35</td></tr></table>", "type_str": "table"}, "TABREF17": {"text": "Comparison of positional bias and our adversarial permutation attack on A-OKVQA dataset. While position bias exists, its impact is moderate. In contrast, our adversarial method severely degrades performance, usually below random chance level.", "num": null, "html": null, "content": "<table><tr><td>Method</td><td>Original A</td><td>B</td><td>C</td><td>D</td><td>Permutation Attack</td></tr></table>", "type_str": "table"}}}}