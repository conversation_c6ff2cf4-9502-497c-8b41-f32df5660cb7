{"paper_id": "LPFInISR", "title": "Exploring the Low-Pass Filtering Behavior in Image Super-Resolution", "abstract": "Deep neural networks for image super-resolution (ISR) have shown significant advantages over traditional approaches like the interpolation. However, they are often criticized as 'black boxes' compared to traditional approaches with solid mathematical foundations. In this paper, we attempt to interpret the behavior of deep neural networks in ISR using theories from the field of signal processing. First, we report an intriguing phenomenon, referred to as 'the sinc phenomenon.' It occurs when an impulse input is fed to a neural network. Then, building on this observation, we propose a method named Hybrid Response Analysis (HyRA) to analyze the behavior of neural networks in ISR tasks. Specifically, HyRA decomposes a neural network into a parallel connection of a linear system and a non-linear system and demonstrates that the linear system functions as a low-pass filter while the non-linear system injects high-frequency information. Finally, to quantify the injected highfrequency information, we introduce a metric for image-to-image tasks called Frequency Spectrum Distribution Similarity (FSDS). FSDS reflects the distribution similarity of different frequency components and can capture nuances that traditional metrics may overlook. Code, videos and raw experimental results for this paper can be found in: https://github.com/RisingEntropy/LPFInISR.", "pdf_parse": {"paper_id": "LPFInISR", "abstract": [{"text": "Deep neural networks for image super-resolution (ISR) have shown significant advantages over traditional approaches like the interpolation. However, they are often criticized as 'black boxes' compared to traditional approaches with solid mathematical foundations. In this paper, we attempt to interpret the behavior of deep neural networks in ISR using theories from the field of signal processing. First, we report an intriguing phenomenon, referred to as 'the sinc phenomenon.' It occurs when an impulse input is fed to a neural network. Then, building on this observation, we propose a method named Hybrid Response Analysis (HyRA) to analyze the behavior of neural networks in ISR tasks. Specifically, HyRA decomposes a neural network into a parallel connection of a linear system and a non-linear system and demonstrates that the linear system functions as a low-pass filter while the non-linear system injects high-frequency information. Finally, to quantify the injected highfrequency information, we introduce a metric for image-to-image tasks called Frequency Spectrum Distribution Similarity (FSDS). FSDS reflects the distribution similarity of different frequency components and can capture nuances that traditional metrics may overlook. Code, videos and raw experimental results for this paper can be found in: https://github.com/RisingEntropy/LPFInISR.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "The goal of image super-resolution (ISR) is to reconstruct low-resolution (LR) images into high-resolution (HR) im-Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s). prisingly, the answer is A. We name this phenomenon as the sinc phenomenon. In this paper, we give a possible explanation for this phenomenon.", "section": "Introduction", "sec_num": "1."}, {"text": "ages through various techniques. In recent years, with advances in deep learning, growing ISR methods using neural networks are proposed, bringing the development of ISR into a new level. While impressive results persistently arise, the mechanism under ISR networks remain largely unexplored, leading to criticism that they are considered black boxes. In comparison, traditional methods, such as interpolation or filtering, have strong interpretability. Despite the principles of traditional methods and neural networks are different, we can still attempt to explain the behavior of ISR networks using theories from traditional methods. In this paper, following this line of thought, we successfully utilize theories from the field of signal processing techniques to explain the performance of neural networks in the ISR task.", "section": "Introduction", "sec_num": "1."}, {"text": "The target of the ISR task is to upsample a two-dimensional signal. In traditional signal processing theory (<PERSON><PERSON> & <PERSON>, 2009; <PERSON><PERSON><PERSON> et al., 1996) , a feasible method for upsample involves restoring a discrete low-samplingrate signal to a continuous signal using a low-pass filter, and then sampling the continuous signal at a higher rate to obtain a high-sampling-rate signal. An intriguing aspect of this process is that when we try to upsample a Dirac δ signal, we will finally get a sinc signal since the sinc signal is the time-domain waveform of a low-pass filter, (for details about this, please refer to Sec. 3 ). Given this, we can conjecture: if neural networks exhibit similar behavior, then when attempt to super-resolve a Dirac δ signal, the resultant outcome would also be a sinc signal. As shown in Fig. 1 , we indeed observe this phenomenon, and we name it as 'the sinc phenomenon'. This phenomenon establishes a connection between traditional signal processing theory and the interpretability of neural networks, thus helping us form a deeper understanding of ISR networks.", "section": "Introduction", "sec_num": "1."}, {"text": "Building upon the sinc phenomenon, we further propose a method named HyRA 1 , which stands for Hybrid Response Analysis. HyRA considers the neural network as a parallel combination of a linear system and a non-linear system with a zero impulse response. It further indicates that this linear system functions as a low-pass filter, while the nonlinear system utilizes the learned prior knowledge to inject high-frequency information. By employing HyRA, we can analyze performance bottlenecks in neural networks, discerning whether the issue lies in inadequate preservation of low-frequency components or insufficient injection of highfrequency components. This analysis facilitates the proposal of targeted improvements for enhanced adaptability.", "section": "Introduction", "sec_num": "1."}, {"text": "Given that the non-linear component is injecting highfrequency information, there is a pressing need for a metric to quantitatively describe the extent of the injected high frequencies. Previous metrics, like PSNR, SSIM (<PERSON> et al., 2004) and LPIPS (<PERSON> et al., 2018a) , have not approached the evaluation of images from a frequency perspective. Therefore, we propose the frequency spectrum distribution similarity (FSDS), a metric that evaluates image quality based on the power distribution in the frequency spectrum.", "section": "Introduction", "sec_num": "1."}, {"text": "In summary, our contribution can be concluded as:", "section": "Introduction", "sec_num": "1."}, {"text": "• We report an intriguing phenomenon: the impulse responses of image super-resolution (ISR) networks are sinc functions, representing the temporal waveform of a low-pass filter. We name it the 'sinc phenomenon'. This observation helps to establish a connection between signal processing theory and neural networks. Moreover, we find that for a network, the more similar the impulse response is to the sinc function, the better performance it produces.", "section": "Introduction", "sec_num": "1."}, {"text": "• In order to further explain the performance of neural networks in the ISR task through this phenomenon, we introduce HyRA. HyRA considers the neural network as a parallel combination of a linear system and a nonlinear system with a zero impulse response. It points out that the linear system operates as a low-pass filter, while the non-linear system injects high-frequency 1 Pronounce as [haI'rA:] information.", "section": "Introduction", "sec_num": "1."}, {"text": "• To quantitatively describe the injection of high frequencies, we introduce the FSDS metric. FSDS measures image quality using frequency spectrum produced by FFT and can reflect high-frequency distortions that previous metrics fail to capture.", "section": "Introduction", "sec_num": "1."}, {"text": "Recent review articles in ISR include fixed-scale superresolution (<PERSON> et al., 2019) and arbitrary-scale superresolution review (<PERSON> et al., 2023) . There are various architectures of mainstream ISR backbone networks, including CNN-style backbones (<PERSON><PERSON> et al., 2018; <PERSON> et al., 2019; <PERSON> et al., 2017; <PERSON> et al., 2018b; c) , transformer-style backbones (<PERSON> et al., 2021; <PERSON> et al., 2023) and GANstyle backbone networks (<PERSON> et al., 2018) , etc. Based on these backbones, researchers have proposed quantitative modules with various functions. For example, ArbSR (<PERSON> et al., 2021) (<PERSON><PERSON><PERSON><PERSON> et al., 2012) , Urban100 (<PERSON> et al., 2015) , Flickr2K (<PERSON> et al., 2014) , SCI1K (<PERSON> et al., 2021) , DIV2K (Agustsson & Timofte, 2017) , etc. We evaluate the effectiveness of our proposed FSDS metric on DIV2K dataset. The large size of the DIV2K dataset contributes to increased reliability in our conclusions. in the literature (<PERSON>, 2018; 2020; <PERSON> et al., 2019; <PERSON> et al., 2019) . Notably, <PERSON> et al. (<PERSON> et al., 2019) propose the Frequency-Principle, claiming its relevance to both convolutional neural networks (CNNs) and fully-connected deep neural networks. According to their proposition, these networks inherently adhere to the Frequency-Principle, wherein training data is systematically acquired in a sequential manner, progressing from low to high frequency. Unlike previous approaches these approaches, HyRA distinguishes itself by employing impulse response to probe the potential mechanisms of deep neural networks in the context of the ISR task.", "section": "Super Resolution Using Neural Networks", "sec_num": "2.1."}, {"text": "Appx. We can employ signal recovery methods to achieve image super-resolution (ISR). Initially, we conceptualize an image as a series of impulse trains in a two-dimensional continuous space, with varying densities representing different resolutions. Then, for the low-resolution image, we begin by implementing low-pass filtering, following the procedure outlined in Appx. B.2, to obtain the continuous image I cont , This process can be mathematically described as:", "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "section": "Preliminaries", "sec_num": "3."}, {"text": "where * denotes convolution, I LR x,y is the low resolution image with variant x, y and I cont x,y is the continuous signal. sinc ω", "section": "Preliminaries", "sec_num": "3."}, {"text": "x,y is a two-dimensional sinc function with parameter ω 2 , whose frequency spectrum is an ideal low-pass filter with a passband of 0 ∼ ω. Subsequently, we sample the 'conceptually continuous signal' at an elevated sampling rate to acquire a more densely populated two-dimensional sequence of impulse trains, i.e., an image with higher resolution denoted as I SR :", "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "section": "Preliminaries", "sec_num": "3."}, {"text": "In the equation, s ∆X,∆Y", "section": "Preliminaries", "sec_num": "3."}, {"text": "x,y denotes the two-dimensional impulse trains with intervals of ∆X in x axis and ∆Y in y axis.", "section": "Preliminaries", "sec_num": "3."}, {"text": "In fact, commonly used interpolation kernels for ISR, such as nearest-neighbor interpolation, linear interpolation, cubic interpolation, etc., can be seen as approximations of the sinc function considering a balance between computational complexity and effectiveness, as illustrated in Fig. 2 . Taking into account the similarity of these interpolation kernels, in this paper, we collectively refer to these parameter-free methods as low-pass filter-based super-resolution methods.", "section": "Preliminaries", "sec_num": "3."}, {"text": "4.1. Hybrid Response Analysis (HyRA)", "section": "Method", "sec_num": "4."}, {"text": "In this section, we describe the proposed Hybrid Response Analysis (HyRA), which treats the neural network as a combination of a linear system and a non-linear system. Through the impulse response, we can calculate a linear time invariant (LTI) system's output from any input using the convolution operation (see Appx. B.1). However, since neural networks are nonlinear systems, we cannot apply convolution to analyze them. To further explore the network features, we need to split it into a linear system and a non-linear system, i.e., HyRA. The core concept HyRA is illustrated in Fig. 3 . We denote an ISR network as N (I), where I is the input image. N (I) is a non-linear system that can be expressed as the sum of a linear system and a non-linear system: In the equation, H(I) represents a linear system, and G(I) represents a non-linear system. Without constraints, such a representation is meaningless because H(I) can be arbitrarily chosen, leading to an infinite variety of representations with the same form but different meanings. To give meaning to this representation, we introduce a constraint: the impulse response of G(I) is zero. With this constraint, both H(I) and G(I) can be uniquely determined. Lemma 4.1 demonstrates that under this constraint, N (I) can still be expressed in the form of Eq. 3. This straightforward method is the essence of HyRA.", "section": "Method", "sec_num": "4."}, {"text": "EQUATION", "section": "Method", "sec_num": "4."}, {"text": "For the ISR task, there is a distinctive property known as a 'spatially invariant system' (<PERSON> et al., 1992) associated with it. Consider the definition of time-invariant systems as mentioned in Appx. B.1, we can naturally extend the concept of in-variance from one-dimensional to twodimensional space and the definition of spatially invariant systems is: when the input is I x,y , the output is G(I x,y ) = O(x, y); when the input becomes I ′ = I x-x0,y-y0 , the output should be G(I ′ ) = O(x -x 0 , y -y 0 ). For convolution based architectures, we can easily prove its spatial invariance (see the proof below). For transformer-based architectures, we can still use experiments to prove the spatial invariance (see Fig. 15 ).", "section": "Method", "sec_num": "4."}, {"text": "Proof. A convolution operation can be defined as:", "section": "Method", "sec_num": "4."}, {"text": "Conv i,j = p,q I i-p,j-1 K p,q .", "section": "Method", "sec_num": "4."}, {"text": "Then, the shifting operation can be defined as:", "section": "Method", "sec_num": "4."}, {"text": "Sh(i, j) → (i + k, j + l).", "section": "Method", "sec_num": "4."}, {"text": "Combine these two, we then have:", "section": "Method", "sec_num": "4."}, {"text": "Conv Sh(i,j) = Conv i+k,j+l = p,q I i+k-p,j+l-q K p,q = Sh( I i-p,j-q K p,q ) = Sh(Conv i,j ).", "section": "Method", "sec_num": "4."}, {"text": "This is the invariance of a single convolution layer, and still holds for more layers.", "section": "Method", "sec_num": "4."}, {"text": "According to HyRA, when we input a Dirac δ signal to the neural network, we can get the impulse response of the linear system (please recall Appx. B.1), denoted as H(δ).", "section": "Method", "sec_num": "4."}, {"text": "For any input I, the response of the linear space invariant system can be obtained by convolving the input with the obtained impulse response, which can be expressed as:", "section": "Method", "sec_num": "4."}, {"text": "EQUATION", "section": "Method", "sec_num": "4."}, {"text": "where * means the convolution operation. Although the response of the non-linear component cannot be directly computed, if we obtain the final output of the neural network, the non-linear part can be deduced by subtracting the response of the linear component from the final output, namely the non-linear response can be computed as:", "section": "Method", "sec_num": "4."}, {"text": "EQUATION", "section": "Method", "sec_num": "4."}, {"text": "Lemma 4.1. A neural network N (I) can be expressed as a combination of a linear system H(I) and a non-linear system with an impulse response of zero, i.e., N (I) = H(I) + G(I), where G(δ) = 0. Here, δ represents the Dirac delta function.", "section": "Method", "sec_num": "4."}, {"text": "Proof.", "section": "Method", "sec_num": "4."}, {"text": "1) When G(δ) = 0, the conclusion holds.", "section": "Method", "sec_num": "4."}, {"text": "2", "section": "Method", "sec_num": "4."}, {"text": ") When G(δ) ̸ = 0, Let H 1 (I) = H(I) + G(δ) * I and G 1 (I) = G(I) -G(δ) * I.", "section": "Method", "sec_num": "4."}, {"text": "In this case, H 1 (I) remains a linear system and G ′ (I) remains a non-linear system. The equation N (I) = H 1 (I) + G 1 (I) holds, and it satisfies G 1 (δ) = 0.", "section": "Method", "sec_num": "4."}, {"text": "In Sec. 3, we mention that a simple low-pass filter achieves ISR functionality. Do neural networks possess low-pass filters internally? If this hypothesis is valid, according to the principle of HyRA, when we input a Dirac δ signal into the neural network N (I), the output should be the impulse response of the low-pass filter, i.e., the sinc function (please recall Appx. B.1 and Tab. 3). In the experiment section (Sec. 5.2), we conduct tests on three mainstream ISR backbones and some derived methods. We find that their impulse responses are sinc functions3 . Now, with both the impulse response and spatial invariance property, we can compute the response of the linear system H(I) to any input through convolution:", "section": "H(I) IS A LOW-PASS FILTER", "sec_num": "4.1.1."}, {"text": "EQUATION", "section": "H(I) IS A LOW-PASS FILTER", "sec_num": "4.1.1."}, {"text": "In a practical scenario, when dealing with a two-dimensional impulse array represented by I, the integration process can be effectively substituted with summation, incorporating appropriate padding. Despite the convolution operator in PyTorch (<PERSON><PERSON><PERSON> et al., 2019) being inherently a correlation operator, the symmetric nature of the sinc function allows for its seamless utilization within such an operator. We present a toy example in Fig. 4 in which we compute the response of the linear component of the EDSR network (<PERSON> et al., 2017) during ISR. Observing the experimental results, we notice that the linear function H(I) essentially achieves super-resolution, but there are some issues: edge blurring and the presence of grid-like distortions.", "section": "H(I) IS A LOW-PASS FILTER", "sec_num": "4.1.1."}, {"text": "The edge is blurred because the low-pass filter removes some high-frequency details. In the frequency spectrum, it is manifested as a relatively small range of diffusion of the central bright spot towards the surroundings. This implies that the image has more low-frequency components and fewer high-frequency components. Such an outcome is the inevitable consequence of applying the low-pass filter.", "section": "H(I) IS A LOW-PASS FILTER", "sec_num": "4.1.1."}, {"text": "When computing the response of the linear system, we first perform zero-interpolation on the low-resolution image to achieve the target spatial size. This operation leads to periodic extension in the frequency spectrum 4 . Since this low-pass filter is not a complete ideal filter, but an ideal filter truncated by a certain window function, its filtering 4 Please refer to Appx. F in the Appendix for details about the periodic extension.", "section": "H(I) IS A LOW-PASS FILTER", "sec_num": "4.1.1."}, {"text": "The red filter is the best option", "section": "The green filter contains too little high frequency content", "sec_num": null}, {"text": "The blue filter contains too much aliased high frequency content performance is weakened by the window function. The weakened filter cannot completely eliminate the extended spectrum, meaning the attenuation in the stopband is insufficient, as referred to in signal processing, thus causing such gird-like distortions.", "section": "The green filter contains too little high frequency content", "sec_num": null}, {"text": "In summary, the linear system H(I) (the low-pass filter approximated by the neural network) can achieve superresolution functionality, but it is not perfect. On one hand, the low-pass filter determines that the image is blurred, lacking high frequencies. On the other hand, the filter is windowed, leading to a weakened filtering performance and resulting in grid-like distortions. These issues will be compensated for by the nonlinear system G(I).", "section": "The green filter contains too little high frequency content", "sec_num": null}, {"text": "Though a low-pass filter can achieve ISR (please refer to Sec. 3), its performance can never surpass a well-trained neural network. The outcome of a low-pass filter varies with respect to the passband width, as depicted in Fig. 5 . However, information outside the passband will be completely wiped out, causing an observable detail loss in high-frequency components. On the contrary, the non-linear part of neural networks is able to inject information in high-frequency domain based on learned or structural priors. Moreover, it can compensate the grid-like distortions brought by the windowed low-pass filter. Together with the linear part, neural networks function as the superset of low-pass filter, retaining both high and low frequency information.", "section": "G(I) INJECTS HIGH-FREQUENCY INFORMATION", "sec_num": "4.1.2."}, {"text": "We compute the non-linear response and its frequency spectrum of the neural network using the proposed HyRA paradigm. In the toy example presented in Fig. 4 , it can be noticed that the response of the non-linear component exhibits sharper edges. Compared with the frequency spectrum of the ISR results, the central bright spot in the response of G(I) spreads to a larger range, indicating that more power is distributed into the high-frequency domain. Almost all the components of the high-frequency part in the final ISR result are contributed by the non-linear component.", "section": "G(I) INJECTS HIGH-FREQUENCY INFORMATION", "sec_num": "4.1.2."}, {"text": "As mentioned in Sec. 4.1.1, the non-linear component also plays a crucial role in compensating for the distortion intro-duced by H(I). Examining the response of G(I), we note that it also exhibits grid-like distortions, matching those in the response of H(I). This allows for the cancellation of the grid-like distortions, achieving the final goal of ISR. As shown in Fig. 4 , upon observing the frequency spectrum, bright spots corresponding to the amplitude spectrum of H(I) exist in all four corners of the amplitude spectrum of G(I). However, the phase spectrum of G(I) is in compensation of the phase spectrum of H(I), indicating that the grid-like distortion is 'erased' here.", "section": "G(I) INJECTS HIGH-FREQUENCY INFORMATION", "sec_num": "4.1.2."}, {"text": "In summary, the non-linear component G(I) serves to inject high-frequency details learned during training to compensate for the loss of high frequencies introduced by the lowpass filter. Simultaneously, it addresses distortions arising from the imperfect performance of the low-pass filter.", "section": "G(I) INJECTS HIGH-FREQUENCY INFORMATION", "sec_num": "4.1.2."}, {"text": "In this section, we introduce the FSDS metric to quantitatively describe the so called the 'injected high frequencies' as discussed in Sec. 4.1.2. Since we need to measure the components of injected high frequencies, we must delve into the issue from a frequency spectrum perspective. However, commonly used metrics such as PSNR, SSIM (<PERSON> et al., 2004) , and LPIPS (<PERSON> et al., 2018a) do not measure the quality of an image from a spectral perspective.", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "Additionally, we've noted that the frequency domain distribution in the ISR field can significantly impact downstream applications (<PERSON> et al., 2020; <PERSON> et al., 2023) . Consequently, we propose that evaluating the ISR effectiveness of a network requires a thorough assessment of its performance in the frequency spectrum. This involves examining the similarity in frequency spectrum between the low-resolution image and the high-resolution image. The Frequency Spectrum Distribution Similarity (FSDS) metric integrates the power distribution maps of the spectrum for both images.", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "The difference is then calculated to generate an error map, and the total sum of its absolute values is computed.", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "For an image I HR x,y , to minimize the impact of the data input range on the results, we normalize the input data and then perform a two-dimensional Fourier transform to obtain I HR jω1,jω2 , which can be mathematically described as:", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "EQUATION", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "where E(I HR ) and σ(I HR ) are the mean value and variance of I HR respectively. Similarly, we perform a Fourier transform on the ISR image to obtain I SR jω1,jω2 . It is worth noting that unlike other metrics, such as PSNR and SSIM (<PERSON> et al., 2004) , which do not incorporate normalization, FSDS is specifically designed to accentuate numerical variations due to its emphasis on numerical changes rather than absolute numerical values. Then, the complex integration of the two spectrum is performed, providing the power distribution map D HR , which is defined as:", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "EQUATION", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": ")", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "Similarly, we can obtain D SR . Subsequently, the difference between D HR and D SR is calculated, providing a difference map D diff of their power distribution:", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "EQUATION", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": ")", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "Finally, we define the frequency spectrum distribution similarity (FSDS) as:", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "EQUATION", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": ")", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "where | • | represents taking the magnitude of a complex number. Considering a more concise description of a larger dynamic range, logarithm is taken. A larger FSDS value indicates that the two images are closer, thereby suggesting better ISR results. ", "section": "Frequency Spectrum Distribution Similarity (FSDS)", "sec_num": "4.2."}, {"text": "Previous image evaluation metrics, such as PSNR, SSIM (<PERSON> et al., 2004) , have focused on statistical or structural features of images, but no work has evaluated images from the perspective of their frequency spectrum. The spectrum is the concentrated expression of components with different changing rates in a signal or image. It is crucial for capturing details, eliminating noise, and comprehensively understanding image features. In image processing, spectrum analysis provides a more accurate evaluation, particularly playing a key role in applications sensitive to details. Due to the nature of Fourier transformation, which involves every pixel of the image in the computation, it encompasses not only information such as signal-to-noise ratio and structural similarity but also the overall similarity of the entire image. Therefore, evaluating image quality from the perspective of the spectrum is highly reasonable and necessary. Our FSDS metric can reflect distribution differences by employing a paradigm of integrating first in the frequency spectrum and then comparing. In other words, FSDS not only reflects the signal-to-noise ratio captured by the PSNR metric and the structural similarity indicated by the SSIM metric, but also captures features that these two metrics cannot represent. In the next paragraph, we will use two toy examples to demonstrate the rationale and advantages of FSDS.", "section": "THE MERITS OF FSDS", "sec_num": "4.2.2."}, {"text": "From Fig. 6 , it can be observed that images obtained by different ISR methods have different proportions of highfrequency components (the center of the spectrum figure represents low frequency, while higher frequencies extend outward). After integration, this is reflected in the varying widths of the dark cross-shaped patterns in the center. A narrower width indicates a higher proportion of low-frequency components in the spectrum, and vice versa. Existing methods may not effectively capture the loss of high-frequency components with low power in the frequency spectrum. Performing information steganography in the frequency spectrum can effectively highlight this aspect. As shown in Fig. 7 , we embed some content in the frequency spectrum of the image. Such steganography causes our FSDS metric to drop to 26.37dB while the SSIM metric remains in a high level of 0.995. we can observe that after applying specific steganography to the spectrum of an image, the image exhibits some blurring and oscillation. Such oscillations are actually the Gibbs phenomenon, a typical oscillation phenomenon caused by the loss of high-frequency information.", "section": "THE MERITS OF FSDS", "sec_num": "4.2.2."}, {"text": "Meanwhile, when we apply JEPG compression to the image5 , when FSDS drops to 26.39dB, SSIM together drops to 0.842. This toy example demonstrates that there indeed exists some feature SSIM cannot reflect while that can be reflected by FSDS.", "section": "THE MERITS OF FSDS", "sec_num": "4.2.2."}, {"text": "In summary, previous methods may not effectively reflect the situation in the image frequency spectrum, while our proposed FSDS metric can sensitively detect distortions in the frequency spectrum.", "section": "THE MERITS OF FSDS", "sec_num": "4.2.2."}, {"text": "Due to the page limitation, we can only present three of the most crucial experiments in this section, namely: 1) the To enhance the clarity of the visualization, the curve has been smoothed using a moving average with a window length of 10.", "section": "Experiments", "sec_num": "5."}, {"text": "relationship between the low-pass filter passband width and ISR performance; 2) various network impulse responses;", "section": "Experiments", "sec_num": "5."}, {"text": "3) a comparison of FSDS metrics with PSNR, SSIM and LPIPS (<PERSON> et al., 2018a) metrics on the DIV2K dataset.", "section": "Experiments", "sec_num": "5."}, {"text": "For more experiments, please refer to Appx. C.", "section": "Experiments", "sec_num": "5."}, {"text": "In Sec. 4.1.2, we mention that a vanilla low-pass filter can achieve ISR, we now present an experiment on the relationship between the low-pass filter passband width and ISR performance. As shown in Fig. 8 , we utilized various lowpass filters to perform ×2 ISR on the validation set from of DIV2K dataset. Subsequently, we evaluated the ISR results using the PSNR and SSIM metrics. When ω = 48, PSNR reaches its maximum value of 31.40. When ω = 45.8, SSIM reaches its maximum value of 0.87. We assert that, in terms of neural network performance, for ×2 ISR, the PSNR should not fall below 31.40, and the SSIM should not be lower than 0.87. Otherwise, it can be considered that the neural network may not effectively capture both low-frequency and high-frequency information.", "section": "Experiment on Low-pass Filtering Super-resolution Performance", "sec_num": "5.1."}, {"text": "We select several mainstream backbones and their derivatives commonly used for the ISR task (<PERSON> et al., 2021; <PERSON> et al., 2019; <PERSON>, 2022; <PERSON> et al., 2021; <PERSON> et al., 2017; <PERSON> et al., 2023; <PERSON> & <PERSON>, 2023; <PERSON> et al., 2018b; c) and conduct impulse response tests. The experimental results are compared with the sinc function and depicted in Fig. 9 . The input image is an 11 × 11 image where only the pixel at position (5, 5) is white (the values for all three channels at this position are 255, with indices starting from 0), and the rest of the image is black (with values of 0). According to Tab. 3, it can be observed that as the ISR factor increases, the central peak of the output sinc function becomes wider and more pronounced. To balance visual saliency and the maximum ISR factor achievable by certain networks, we opted for a 4x ISR factor. Observing the experimental results, we can notice that regardless of the neural network structure used for ISR, whether it's a CNN or a transformer, the impulse response exhibits some degree of similarity to the two-dimensional sinc function. This similarity is particularly pronounced in networks like RDN (<PERSON> et al., 2018c) and RCAN (<PERSON> et al., 2018b) . Despite some distortion in comparison to the sinc function, EDSR (<PERSON> et al., 2017) , EQSR (<PERSON> et al., 2023) , and their derivatives still exhibit significant features of the sinc function, including the central bright spot and elongated bright patches in the cardinal directions. From Tab. 1, we observe that networks exhibiting superior performance tend to generate impulse responses that closely resemble the sinc function. This observation suggests that preserving lowfrequency information more effectively can also enhance performance. However, few previous works has focus on low-frequency, giving us a new idea for furture ISR networks.", "section": "Experiment on Impulse Response", "sec_num": "5.2."}, {"text": "We conducted tests on the validation set of the DIV2K dataset (Agustsson & Timofte, 2017) From Sec. 4.2, we claim that previous metrics are not sensitive to high-frequency information, while FSDS does. This can be proven by Tab. 1. In the case of slight high-frequency loss, such as on scales × 2 to × 4, FSDS responds differently compared to previous metrics. In cases that suffer from severe high-frequency loss, such as on × 6 and × 12 scales, FSDS shows consistency with previous metrics. This is because when high-frequency loss is slight, previous metrics fail to reflect such high-frequency loss and while the loss becomes more severe, they start to capture such loss. (<PERSON> et al., 2004) , LPIPS (<PERSON> et al., 2018a) and FSDS metrics for different methods on the DIV2K dataset (Agus<PERSON><PERSON> & Timofte, 2017) . Items with the highest and the second-highest mean values are highlighted in red and blue, respectively. The gray superscripts are the order of each method.", "section": "Experiment on FSDS Metric", "sec_num": "5.3."}, {"text": "This observation shows the necessity of applying the FSDS metrics to assess image quality objectively.", "section": "Experiment on FSDS Metric", "sec_num": "5.3."}, {"text": "SwinIR-Real ESRGAN", "section": "Some Exceptions to Impulse Responses", "sec_num": "5.4."}, {"text": "Figure 10 . The impulse response of SwinIR-Real (<PERSON> et al., 2021) and ESRGAN (<PERSON> et al., 2018) is not an obvious sinc function.", "section": "Some Exceptions to Impulse Responses", "sec_num": "5.4."}, {"text": "We observe that not all impulse response of networks is 'sinc' function, as shown in Fig. 10 . SwinIR-Real (<PERSON> et al., 2021) and ESRGAN (<PERSON> et al., 2018) are trained using adversarial loss, while methods in Fig. 9 uses loss like ℓ 1 or ℓ 2 loss. Therefore, we believe the 'sinc' impulse response is related to the loss function.", "section": "Some Exceptions to Impulse Responses", "sec_num": "5.4."}, {"text": "In this paper, we report an intriguing observation. i.e., the sinc phenomenon, which reveals that the impulse response of ISR networks act as low-pass filters. Building on this observation, we introduce a novel approach called Hybrid Response Analysis (HyRA) to explore the hidden behavior of ISR networks. HyRA treats a neural network as a combination of a linear system and a non-linear system with a zero impulse response. The linear system functions as a low-pass filter, while the non-linear system utilizes prior knowledge to inject high-frequency details. To assess the neural network's information recovery across the frequency spectrum, we propose the Frequency Spectrum Distribution Similarity (FSDS) metric. FSDS uncovers properties overlooked by previous metrics, and experiments validate the rationality and necessity of it. Fourier transform of I x,y x(t)", "section": "Conclusion", "sec_num": "6."}, {"text": "1-D signal with variant t X(jω)", "section": "A. Notation Conventions", "sec_num": null}, {"text": "Fourier transform of x(t), jω is a notation, ω is the variant", "section": "A. Notation Conventions", "sec_num": null}, {"text": "x[n] Discrete signal with index n X[k] DFT of x[n] F[x(t)] Fourier transform operator, X(jω) = F[x(t)] F -1 [X(jω)] Inverse Fourier transform, x(t) = F -1 [X(jω)] Signals δ(t) Dirac δ function sinc ω (t)", "section": "A. Notation Conventions", "sec_num": null}, {"text": "The sinc funtion with parameter ω, sinc ω (t) = sin(ωt) πt . The sinc function is the time-domain waveform of an ideal low-pass filter. sinc ω x,y 2-D sinc function with parameter ω, sinc ω", "section": "A. Notation Conventions", "sec_num": null}, {"text": "x,y = sin(ωx) πx", "section": "A. Notation Conventions", "sec_num": null}, {"text": "• sin(ωy) πy s ∆T (t) 1-D sample signal with a sample interval of ∆T , s ∆T (t) = ∞ n=-∞ δ(t -nT )", "section": "A. Notation Conventions", "sec_num": null}, {"text": "Table 2 . Notation Conventions", "section": "A. Notation Conventions", "sec_num": null}, {"text": "We briefly introduce some related concepts and methods used in this paper in this section.", "section": "B. Signal Processing Theories", "sec_num": null}, {"text": "The word 'system' has many meanings and interpretations. This paper views a system as a process in which input signals are transformed by the system or cause the system to respond in some way, resulting in other signals as output (<PERSON><PERSON> et al., 1996) . Systems can be divided into linear systems and nonlinear systems according to their mathematical properties.", "section": "B.1. System and Response", "sec_num": null}, {"text": "A linear system refers to a system with such a property: the response of the system to the input x 1 (t), x 2 (t) is y 1 (t), y 2 (t) respectively, then when the input is x 1 (t) + x 2 (t), the response of the system is y 1 (t) + y 2 (t).", "section": "B.1. System and Response", "sec_num": null}, {"text": "Systems can also be divided into time-variant ones and time-invariant ones according to their temporal properties. A time-invariant system refers to that the properties of the system do not change with time, that is, the system has the same impulse response at any time. It satisfies such a relationship: when the input is x(t), the output is y(t), and when the input is x(t -t 0 ), the output is y(t -t 0 ).", "section": "B.1. System and Response", "sec_num": null}, {"text": "A system with both linear and time-invariant properties is a linear time-invariant (LTI) system. For an LTI system, we can use 'impulse response' to uniquely describe it: systems with the same impulse response are the same system, vice versa. The impulse response h(t) is defined as the output of the system when the input signal is δ(t) (Dirac delta function). The response of a linear system to an arbitrary input signal can be computed through the convolution operation of its impulse response and the input signal, namely:", "section": "B.1. System and Response", "sec_num": null}, {"text": "EQUATION", "section": "B.1. System and Response", "sec_num": null}, {"text": "In the equation, * is the convolution operator, y(t) is the system output and x(t) is the input signal. When we apply Fourier transform to the impulse response h(t), then we can obtain the transfer function H(jω) of the system. The transfer function describes the frequency domain waveform of the impulse response. According to the convolution theorem, the response of a linear system can also be obtained by multiplying the Fourier transform of the input signal by the transfer function of the system and then performing the inverse Fourier transform. In summary, given the impulse response of an LTI system, we can calculate the system's response to any output.", "section": "B.1. System and Response", "sec_num": null}, {"text": "Time domain:", "section": "B.2. Signal Sampling and Recovery", "sec_num": null}, {"text": "Frequency domain: ", "section": "B.2. Signal Sampling and Recovery", "sec_num": null}, {"text": "Frequency domain: Signal sampling and sample recovery are very common operations, and in this section, we will briefly analyze this process from the perspective of both the time-domain and frequency-domain. The upper part of Fig. 11 shows the time domain waveform variation of signal sampling process, and the lower part shows the frequency domain waveform variation of signal sampling process. To sample a continuous signal, the sampling process can be regarded as the multiplication of the original signal x(t)and an impulse train signal s ∆T (t). It can be described as:", "section": "Time domain:", "sec_num": null}, {"text": "EQUATION", "section": "Time domain:", "sec_num": null}, {"text": "where T denotes the sampling interval, x ′ (t) denotes the sampled signal. According to the convolution theorem, the frequency domain change of the sampling process can be described in the following way:", "section": "Time domain:", "sec_num": null}, {"text": "EQUATION", "section": "Time domain:", "sec_num": null}, {"text": "That is, the sampling process is reflected in the frequency spectrum as a periodic extension of the frequency spectrum of the original signal x(t).", "section": "Time domain:", "sec_num": null}, {"text": "Fig. 12 shows the time domain and frequency domain waveform variation during the recovery process. For sampling recovery, in order to restore the sampled signal x ′ (t) to the original signal x(t), from the perspective of frequency domain, a low-pass filter is all we needed, that is, convolving the sampled signal with a low-pass filter h LP (t). This process can be expressed as:", "section": "Time domain:", "sec_num": null}, {"text": "EQUATION", "section": "Time domain:", "sec_num": null}, {"text": "where in the equation, h LP (t) = sinc ω0 (t) = sin(ω0t) πt is the time domain response of the ideal low-pass filter, and its frequency-domain waveform H LP (jω) is a rectangular window. Spectrum aliasing is a manifestation of information loss. Fig. 13 depicts the time-domain and frequency-domain scenarios of no frequency overlapping and frequency overlapping, respectively. When the sampling rate is lower than the Nyquist sampling rate 6 (<PERSON><PERSON><PERSON>, 1928) . When the sampling rate is below the Nyquist sampling rate, the approach mentioned in Appx. B.2 cannot completely restore the original signal x(t). From Tab. 3, we can see that for the sample signal s ∆T (t), the larger T is, the sparser its time domain impulse train gets, while in the frequency spectrum the impulse trains gets denser. When the impulse trains in the frequency domain become sufficiently dense, and the spectrum of the original signal is periodically extended, overlapping occurs, preventing the complete recovery of the original signal. In ISR tasks, spectrum aliasing is manifested when restoring a low-resolution image to a high-resolution image, resulting in the loss of high-frequency information such as details and textures.", "section": "Time domain:", "sec_num": null}, {"text": "C.1. Linear and Non-linear Responses", "section": "C. Extra Experiments", "sec_num": null}, {"text": "In Fig. 14 , we present the linear and nonlinear responses of various ISR networks along with their corresponding spectrums.", "section": "C. Extra Experiments", "sec_num": null}, {"text": "From the figure, it is evident that different networks exhibit varying filtering effects in their linear components. EDSR demonstrates a pronounced removal of high-frequency components, and compared to other methods, it exhibits the smallest area of brightness diffusion around the central bright spot in its spectrum. From the nonlinear responses, it can be observed that the nonlinear components of the networks are all involved in supplementing high-frequency information and correcting distortions. ", "section": "C. Extra Experiments", "sec_num": null}, {"text": "We conducted spatial invariance testing on RDN (<PERSON> et al., 2018c) (For the concept of spatial invariance, please refer to Sec. 4.1). The input data consists of an image where only one pixel is white (the pixel value is 1), and all other pixels are black (the pixel value is 0). By shifting the position of this white pixel, we obtain I(x -∆x, y -∆y). This shifted input I(x -∆x, y -∆y) is then fed into the neural network, and we obtain its shifted impulse response, as illustrated in Fig. 15 .", "section": "C.2. Space Invariance", "sec_num": null}, {"text": "Observing the experimental results, we find that the responses to different I(x -∆x, y -∆y) are consistent, with the only difference being their position. This demonstrates that, for ISR networks, the linear component in HyRA exhibits spatial invariance.", "section": "C.2. Space Invariance", "sec_num": null}, {"text": "As shown in Fig. 16 , we visualize the output features of different components in the EDSR (<PERSON> et al., 2017) network for analysis. We observe that the approximate shape of the sinc function begins to take form after the Upsampler module, and after a convolution, it essentially forms the shape of a sinc function. Interestingly, in the EDSR network, the Upsample (<PERSON> et al., 2017) network layers. The sinc-like pattern start to take shapes after the sub-pixel convolution, before the last convolution layer. module uses sub-pixel convolution (convolution + pixel shuffle) for upsampling without any interpolation. This indicates that the low-pass filter present in the network is learned by the network itself and not introduced by interpolation kernels. ", "section": "C.3. Exploration of the Positional Origin of Sinc-like Patterns", "sec_num": null}, {"text": "∞ n=-∞ δ(t -nT ) 2π T ∞ k=-∞ δ(ω -k 2π T )", "section": "<PERSON>. The Fourier Transform Pairs Involved in This Paper", "sec_num": null}, {"text": "Ideal Low-pass filter Sec. 4.1.1 x(t) = sinc ω0 (t) = sin(ω0t) πt , ω 0 is called the cut-off frequency The time-domain waveform of an ideal low-pass filter is a sinc function. The sinc function is defined over [-∞, ∞], and the number of zero crossings is countable. This implies that in reality, an ideal low-pass filter does not exist. In discrete-time signal processing, truncating a designed filter using a window function is common. There are many window functions, such as the rectangular window, Hanning window, Blackman window, and so on. Fig. 17 illustrates some commonly used window functions. Observing the experimental results and analyzing the relationship between the peak values of the main lobe and the first side lobe, we find that the impulse response of the neural network seems to undergo windowing. However, different networks appear to adopt different window functions.", "section": "<PERSON>. The Fourier Transform Pairs Involved in This Paper", "sec_num": null}, {"text": "X(jω) = 1, |ω| < ω 0 0, |ω| > ω 0 δ(t) Sec. 4.1.1 lim τ →0 +τ -τ δ(t) = 1 1 Table 3.", "section": "<PERSON>. The Fourier Transform Pairs Involved in This Paper", "sec_num": null}, {"text": "When considering integer factor ISR, our approach to computing the linear component response is as follows: first, upsample the low-resolution image to the high-resolution image through zero-padding, and then convolve it with the impulse response to obtain the response. During the zero-padding process, it leads to period extension in the frequency spectrum. For a signal", "section": "F. Frequency Spectrum Period Extension Caused by Zero Padding", "sec_num": null}, {"text": "Figure 18 . Performing zero-padding on an image to reach the target size will result in periodic extension in the frequency spectrum obtained through its Discrete Fourier Transform.", "section": "D u p li c a te", "sec_num": null}, {"text": "x[n] of length N undergoing DFT to obtain X[k], we have:", "section": "D u p li c a te", "sec_num": null}, {"text": "EQUATION", "section": "D u p li c a te", "sec_num": null}, {"text": "Then, zero-padding is applied to x", "section": "D u p li c a te", "sec_num": null}, {"text": "EQUATION", "section": "D u p li c a te", "sec_num": null}, {"text": "Perform DFT to x 2 [n] to obtain X ′ [k], then we have:", "section": "D u p li c a te", "sec_num": null}, {"text": "EQUATION", "section": "D u p li c a te", "sec_num": null}, {"text": "When k < N , there exists:", "section": "D u p li c a te", "sec_num": null}, {"text": "EQUATION", "section": "D u p li c a te", "sec_num": null}, {"text": "Therefore,", "section": "D u p li c a te", "sec_num": null}, {"text": "EQUATION", "section": "D u p li c a te", "sec_num": null}, {"text": "Thus, zero-padding causes period extension in the frequency spectrum. Ideally, the extended spectrum would be filtered out by the low-pass filter used in the ISR process. However, due to the limited filtering capability of the potential filters within the neural network, the stopband attenuation is low, and the extended spectrum cannot be completely filtered out.", "section": "D u p li c a te", "sec_num": null}, {"text": "We compare our proposed FSDS metric with ℓ 1 norm, and ℓ 2 norm on both frequency domain and image domain as depicted in Tab. 4 and Tab. 5. From these two figures, we can observe that ℓ 1 norm and ℓ 2 norm produces similar ranking orders when they are calculated on the same domain, indicating that ℓ 1 norm is equivalent to ℓ 2 norm when assessing image quality. However, the ranking orders produced by FSDS is distinctive to that of ℓ 1 and ℓ 2 . This means our FSDS metric reflects image quality in a unique way. ", "section": "<PERSON><PERSON> Between FSDS and ℓ 1 , ℓ 2 norms", "sec_num": null}, {"text": "To better explain how G(I) is trained and how G(I) and H(I) vary dependently, we conduct a new experiment on observing their varying progress during training. We train a vanilla RDN (<PERSON> et al., 2018c) network from scratch and obtain the impulse response, H(I), G(I), and N (I) from each epoch (see Fig. 19 ). As shown in the figure, in the second row, the sinc phenomenon becomes clearer along with the training process, this indicates that the network is gradually learning the low-pass filter. The same conclusion can be further supported by observing the variation in H(I) as shown in the third row. In this row, H(I) illustrates the phenomenon of the grid-like distortion vanishing while low-frequency areas getting smoother. The fourth row depict the plot of G(I). This row demonstrates that G(I) is capturing more and more high-frequency information, such as edges. This is very significant especially when comparing the result from Epoch 1 and Epoch 50. Such increase in high-frequncy information can also be found in the frequency spectrum. During the entire training process, the network fails to recover the words on the wall (please see the magnified area, there are words on the wall next to the blue awning). Observing G(I), we can find no sign of words as well. This indicates that the network treats it as low-frequency information and ignores it, pointing the way for future network improvements. ", "section": "H. How G(I) is Trained and How G(I) and H(I) Vary Dependently", "sec_num": null}, {"text": "University of Electronic Science and Technology of China.Correspondence to: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.", "section": "", "sec_num": null}, {"text": "Strictly speaking, it is a windowed sinc function. Regarding the windowing operation, please refer to Appx. E", "section": "", "sec_num": null}, {"text": "In this example, the compression quality is set to 10.", "section": "", "sec_num": null}, {"text": "The minimum sampling rate that can completely restore the origin of the sampled from sampled signal, which is twice the highest frequency of the original signal", "section": "", "sec_num": null}, {"text": "Exploring the Low-Pass Filtering Behavior in Image Super-Resolution", "section": "", "sec_num": null}], "back_matter": [{"text": "We appreciate anonymous reviewers for their previous suggestions to help this paper better. Moreover, we would like to express our sincere gratitude to <PERSON><PERSON><PERSON><PERSON> (<EMAIL>) for his generous support in GPUs. Without his support, it is hard for us to do experiments using full-scale DIV2K dataset. This work is supported by NSFC (12271083).", "section": "Acknowledgements", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the interpretability of neural networks in Image super-resolution. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.", "section": "Impact Statement", "sec_num": null}], "ref_entries": {"FIGREF0": {"fig_num": "1", "text": "Figure 1. I is an image in which only the central pixel is 1 and the other pixels are 0. What would the result look like if image I is super-resolved using a neural network, A, B, C, or D? Sur-prisingly, the answer is A. We name this phenomenon as the sinc phenomenon. In this paper, we give a possible explanation for this phenomenon.", "type_str": "figure", "uris": null, "num": null}, "FIGREF2": {"fig_num": "3", "text": "Figure 3. Conceptual diagram of HyRA's core idea.", "type_str": "figure", "uris": null, "num": null}, "FIGREF3": {"fig_num": "4", "text": "Figure 4. Top row: a super-resolved image by (<PERSON> et al., 2017) can be viewed as the summation of a linear response obtained by convolving impulse response with the input and the non-linear response gained by subtracting linear-part from the ISR result. Second row: the corresponding frequency spectrum amplitude of the top row. Third row: the corresponding frequency spectrum phase of the top row. The phase compensation indicates that the non-linear part is compensating distortion.", "type_str": "figure", "uris": null, "num": null}, "FIGREF4": {"fig_num": "5", "text": "Figure 5. An illustration of how the passband width of a low-pass filter affects its ISR results. A too wide passband or a too narrow passband can result in a decline in performance.", "type_str": "figure", "uris": null, "num": null}, "FIGREF5": {"fig_num": "6", "text": "Figure 6. X-FFT-Σ denotes the integrated frequency spectrum, the integration path is from origin to infinty in every quadrant. Columns 1 and 2 in the figure respectively show that the differences in the results of different ISR methods can be reflected in the frequency spectrum. Column 3 presents the integral of the spectrum from low to high frequencies in a contour plot. The distribution of contours visually represents the distinct distribution of different frequency components.", "type_str": "figure", "uris": null, "num": null}, "FIGREF6": {"fig_num": "7", "text": "Figure 7. A comparison of SSIM and FSDS in JPEG compression and steganography. As can be seen, SSIM fails to reflect distortion brought by steganography, while FSDS captures both cases of distortion.", "type_str": "figure", "uris": null, "num": null}, "FIGREF7": {"fig_num": "8", "text": "Figure8. The ISR performance using a low-pass filter shows variations with the cutoff frequency ω. This figure illustrates the results obtained from the ×2 ISR task conducted on the DIV2K dataset. To enhance the clarity of the visualization, the curve has been smoothed using a moving average with a window length of 10.", "type_str": "figure", "uris": null, "num": null}, "FIGREF8": {"fig_num": "9", "text": "Figure 9. Comparison of impulse responses and the sinc function for several mainstream backbone networks and their derivatives. The impulse response of the bicubic interpolation result is presented as a reference.", "type_str": "figure", "uris": null, "num": null}, "FIGREF10": {"fig_num": "11", "text": "Figure11. Time-domain to frequency-domain waveform variation of the continuous signal sampling process. The sampling function s δT is an impulse train sequence with an interval of T , and S δT (t) is its frequency domain waveform, which is also an impulse train sequence. Sampling a signal causes duplication in the frequency domain.", "type_str": "figure", "uris": null, "num": null}, "FIGREF11": {"fig_num": "12", "text": "Figure 12. Time-domain to frequency-domain waveform variation in the process of sampling signal recovery. hLP (t) is the time-domain impulse response of a low-pass filter, and HLP (jω) is its frequency-domain waveform.", "type_str": "figure", "uris": null, "num": null}, "FIGREF12": {"fig_num": "13", "text": "Figure13. The illustration of spectrum aliasing. On the left, there is no aliasing as the sampling rate is sufficiently high. On the right, aliasing occurs due to an insufficient sampling rate.", "type_str": "figure", "uris": null, "num": null}, "FIGREF13": {"fig_num": "14", "text": "Figure 14. Linear and non-linear responses and their corresponding frequency spectrum of various ISR methods.", "type_str": "figure", "uris": null, "num": null}, "FIGREF14": {"fig_num": "1516", "text": "Figure15. Spatial invariance experiment conducted on SwinIR(<PERSON> et al., 2021). When we feed the SwinIR network with impulses at positions, the ISR results demonstrate that the RDN exhibits spatial invariance.", "type_str": "figure", "uris": null, "num": null}, "FIGREF15": {"fig_num": "17", "text": "Figure 17. Various window functions.", "type_str": "figure", "uris": null, "num": null}, "FIGREF16": {"fig_num": null, "text": "Figure 19. N (I), H(I) and G(I) from different epoches during training.", "type_str": "figure", "uris": null, "num": null}, "TABREF2": {"text": "B.1-Appx. B.3 provide a brief overview of signal processing concepts for readers who are not familiar with it.", "type_str": "table", "content": "<table/>", "num": null, "html": null}, "TABREF5": {"text": "<PERSON> et al., 2017) 34.55 14 30.92 15 28.98 15 26.76 4 23.75 4 0.937 15 0.874 14 0.819 14 0.741 4 0.633 4 0.043 15 0.100 13 0.153 13 0.243 4 0.428 2 39.37 13 34.53 6 31.32 9 28.45 4 23.15 3 EDSR-OPESR(<PERSON> et al.Lim et al., 2017)   34.72 9 31.05 10 29.13 10 26.90 3 23.87 3 0.939 9 0.876 10 0.822 11 0.746 3 0.638 3 0.041 8 0.098 10 0.149 10 0.241 3 0.437 4 39.53 11 34.53 7 31.45 6 28.46 3 22.78 4 EDSR-LTE(<PERSON> et al.<PERSON> et al., 2018c) 34.86 8 31.21 8 29.26 9 26.99 2 23.93 2 0.939 8 0.879 8 0.826 9 0.749 2 0.639 2 0.041 10 0.096 8 0.147 8 0.231 1 0.406 1 39.69 9 34.83 3 31.83 3 28.89 1 23.78 1 RDN-OPESR(<PERSON> et al., 2018c) 34.52 15 31.19 9 29.28 8 <PERSON> et al., 2018c) 34.91 7 31.26 7 29.31 7 27.05 1 23.99 1 0.939 7 0.879 7 0.827 7 0.750 1 0.641 1 0.041 7 0.095 6 0.144 6 0.233 2 0.431 3 39.82 5 34.75 4 31.84 2 28.65 2 23.35 2 Swin<PERSON>-classical(<PERSON> et al., 2021) 35.34 5 31.64 5 29.63 4 Yang et al., 2021) 32.67 17 30.49 17 28.73 17 26.64 5 23.72 5 0.922 17 0.866 17 0.813 17 0.736 5 0.630 5 0.052 17 0.113 17 0.167 17 0.271 5 0.469 5 31.25 18 26.18 18 25.88 18 25.62 5 21.57 5 HAT-S(Chen et al., 2023) 35.46 2 31.72 3 29.72 3 18 28.25 18 26.69 18 24.87 6 22.34 6 0.893 18 0.813 18 0.752 18 0.675 6 0.587 6 0.096 18 0.191 18 0.291 18 0.439 6 0.613 6 32.79 17 28.90 17 26.57 17 23.45 6 18.74 6 Comparison of PSNR, SSIM", "type_str": "table", "content": "<table><tr><td/><td/><td/><td>PSNR</td><td/><td/><td/><td/><td>SSIM</td><td/><td/><td/><td/><td>LPIPS</td><td/><td/><td/><td/><td>FSDS (Ours)</td><td/><td/></tr><tr><td>Method</td><td>×2</td><td>×3</td><td>×4</td><td>×6</td><td>×12</td><td>×2</td><td>×3</td><td>×4</td><td>×6</td><td>×12</td><td>×2</td><td>×3</td><td>×4</td><td>×6</td><td>×12</td><td>×2</td><td>×3</td><td>×4</td><td>×6</td><td>×12</td></tr><tr><td>EDSR(Lim et al., 2017)</td><td colspan=\"3\">34.63 12 30.95 14 28.87 16</td><td>-</td><td>-</td><td colspan=\"3\">0.937 12 0.874 13 0.816 16</td><td>-</td><td>-</td><td colspan=\"3\">0.042 12 0.101 14 0.155 16</td><td>-</td><td>-</td><td colspan=\"3\">39.21 15 34.15 10 31.38 7</td><td>-</td><td>-</td></tr><tr><td>EDSR-LIIF(, 2017)</td><td colspan=\"3\">34.34 16 30.96 12 29.04 12</td><td>-</td><td>-</td><td colspan=\"3\">0.936 16 0.875 11 0.820 13</td><td>-</td><td>-</td><td colspan=\"3\">0.043 13 0.100 11 0.153 14</td><td>-</td><td>-</td><td colspan=\"3\">39.80 6 34.63 5 31.29 11</td><td>-</td><td>-</td></tr><tr><td>EDSR-SRNO(, 2017)</td><td colspan=\"3\">34.61 13 30.97 11 29.03 14</td><td>-</td><td>-</td><td colspan=\"3\">0.937 14 0.874 12 0.820 12</td><td>-</td><td>-</td><td colspan=\"3\">0.043 14 0.100 12 0.152 12</td><td>-</td><td>-</td><td colspan=\"3\">39.29 14 34.33 8 31.30 10</td><td>-</td><td>-</td></tr><tr><td>RDN(Zhang et al., 2018c)</td><td colspan=\"3\">34.69 10 30.58 16 29.12 11</td><td>-</td><td>-</td><td colspan=\"3\">0.938 10 0.867 16 0.823 10</td><td>-</td><td>-</td><td colspan=\"3\">0.041 9 0.106 16 0.150 11</td><td>-</td><td>-</td><td colspan=\"3\">40.02 3 32.95 16 31.65 4</td><td>-</td><td>-</td></tr><tr><td colspan=\"5\">RDN-LIIF(-</td><td>-</td><td colspan=\"3\">0.938 11 0.879 9 0.826 8</td><td>-</td><td>-</td><td colspan=\"3\">0.042 11 0.096 7 0.148 9</td><td>-</td><td>-</td><td colspan=\"3\">40.19 2 34.96 2 31.48 5</td><td>-</td><td>-</td></tr><tr><td colspan=\"5\">RDN-LTE(-</td><td>-</td><td colspan=\"3\">0.943 5 0.885 5 0.835 5</td><td>-</td><td>-</td><td colspan=\"3\">0.038 5 0.092 4 0.140 5</td><td>-</td><td>-</td><td colspan=\"3\">40.37 1 35.13 1 32.37 1</td><td>-</td><td>-</td></tr><tr><td colspan=\"5\">ITSRN(-</td><td>-</td><td colspan=\"3\">0.944 2 0.887 3 0.837 3</td><td>-</td><td>-</td><td colspan=\"3\">0.038 2 0.092 3 0.139 3</td><td>-</td><td>-</td><td colspan=\"3\">39.78 7 33.80 13 31.06 14</td><td>-</td><td>-</td></tr><tr><td>HAT(Chen et al., 2023)</td><td colspan=\"3\">35.46 2 31.77 2 29.75 2</td><td>-</td><td>-</td><td colspan=\"3\">0.944 2 0.887 2 0.837 2</td><td>-</td><td>-</td><td colspan=\"3\">0.038 2 0.090 2 0.138 2</td><td>-</td><td>-</td><td colspan=\"3\">39.78 7 33.91 11 31.20 12</td><td>-</td><td>-</td></tr><tr><td>HDSRNet(Tian et al., 2024)</td><td colspan=\"3\">34.64 11 30.95 13 29.04 13</td><td>-</td><td>-</td><td colspan=\"3\">0.937 13 0.873 15 0.819 15</td><td>-</td><td>-</td><td colspan=\"3\">0.043 16 0.103 15 0.154 15</td><td>-</td><td>-</td><td colspan=\"3\">39.46 12 34.20 9 31.33 8</td><td>-</td><td>-</td></tr><tr><td>GRLBase(Li et al., 2023)</td><td colspan=\"3\">35.66 1 31.93 1 29.91 1</td><td>-</td><td>-</td><td colspan=\"3\">0.945 1 0.889 1 0.841 1</td><td>-</td><td>-</td><td colspan=\"3\">0.037 1 0.089 1 0.135 1</td><td>-</td><td>-</td><td colspan=\"3\">39.99 4 33.84 12 31.12 13</td><td>-</td><td>-</td></tr><tr><td>GRLSmall(Li et al., 2023)</td><td colspan=\"3\">35.39 4 31.65 4 29.63 5</td><td>-</td><td>-</td><td colspan=\"3\">0.943 4 0.886 4 0.835 4</td><td>-</td><td>-</td><td colspan=\"3\">0.038 4 0.092 5 0.140 4</td><td>-</td><td>-</td><td colspan=\"3\">39.54 10 33.68 14 31.03 15</td><td>-</td><td>-</td></tr><tr><td>GRLTiny(Li et al., 2023)</td><td colspan=\"3\">35.17 6 31.41 6 29.40 6</td><td>-</td><td>-</td><td colspan=\"3\">0.942 6 0.882 6 0.830 6</td><td>-</td><td>-</td><td colspan=\"3\">0.039 6 0.096 9 0.146 7</td><td>-</td><td>-</td><td colspan=\"3\">39.20 16 33.20 15 30.56 16</td><td>-</td><td>-</td></tr><tr><td>Bicubic</td><td>31.04</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr></table>", "num": null, "html": null}, "TABREF7": {"text": "Comparison between our proposed FSDS metric and ℓ1 norm in both frequency domain and image domain. Items with the highest mean values are highlighted in red and secondary mean values in blue. The gray superscripts denote the ranking order. <PERSON> et al., 2017) 39.371 13 34.535 6 31.321 9 28.446 4 23.147 3 4967.727 14 11163.112 14 16947.235 15 27007.748 4 49280.883 4 4967.727 14 11163.113 14 16947.236 15 27007.749 4 49280.886 4 EDSR-OPESR(<PERSON> et al.<PERSON> et al., 2021) 31.254 18 26.178 18 25.876 18 25.619 5 21.566 5 7483.582 17 12244.897 17 17878.399 17 27664.054 5 49417.061 5 7483.582 17 12244.898 17 17878.400 17 27664.056 5 49417.064 5 Bicubic 32.790 17 28.896 17 26.568 17 23.450 6 18.736 6 10873.900 18 19564.473 18 26817.580 18 38541.675 6 64011.698 6 10873.901 18 19564.474 18 26817.581 18 38541.677 6 64011.702 6 HAT-S(<PERSON> et al.", "type_str": "table", "content": "<table><tr><td/><td/><td/><td/><td colspan=\"2\">FSDS (Ours)</td><td/><td/><td/><td colspan=\"3\">ℓ1 Norm in Frequency Domain</td><td/><td/><td colspan=\"2\">ℓ1 Norm</td></tr><tr><td>Method</td><td/><td>×2</td><td>×3</td><td>×4</td><td colspan=\"2\">×6</td><td>×12</td><td>×2</td><td>×3</td><td>×4</td><td>×6</td><td>×12</td><td>×2</td><td>×3</td><td>×4</td><td>×6</td><td>×12</td></tr><tr><td>EDSR(Lim et al., 2017)</td><td/><td colspan=\"3\">39.210 15 34.148 10 31.380 7</td><td>-</td><td/><td>-</td><td colspan=\"3\">35.190 12 51.676 12 62.478 16</td><td>-</td><td>-</td><td colspan=\"3\">0.012 10 0.019 13 0.023 16</td><td>-</td><td>-</td></tr><tr><td>EDSR-LIIF(Lim et al., 2017)</td><td/><td colspan=\"2\">39.371 13 34.535 6</td><td colspan=\"13\">31.321 9 28.446 4 23.147 3 35.369 14 51.886 14 61.996 15 73.963 4 88.203 5 0.013 15 0.019 15 0.023 15 0.029 4 0.042 4</td></tr><tr><td colspan=\"2\">EDSR-OPESR(Lim et al., 2017)</td><td>39.798 6</td><td colspan=\"2\">34.630 5 31.286 11</td><td>-</td><td/><td>-</td><td colspan=\"3\">36.796 16 51.948 15 61.711 14</td><td>-</td><td>-</td><td colspan=\"3\">0.013 16 0.018 11 0.023 12</td><td>-</td><td>-</td></tr><tr><td>EDSR-SRNO(Lim et al., 2017)</td><td/><td colspan=\"2\">39.533 11 34.527 7</td><td colspan=\"13\">31.448 6 28.458 3 22.778 4 34.688 10 51.113 10 61.140 11 73.136 3 87.704 3 0.012 11 0.018 10 0.023 10 0.029 3 0.041 3</td></tr><tr><td>EDSR-LTE(Lim et al., 2017)</td><td/><td colspan=\"3\">39.290 14 34.328 8 31.303 10</td><td>-</td><td/><td>-</td><td colspan=\"3\">35.110 11 51.556 11 61.706 12</td><td>-</td><td>-</td><td colspan=\"3\">0.013 14 0.019 14 0.023 14</td><td>-</td><td>-</td></tr><tr><td>RDN(Zhang et al., 2018c)</td><td/><td colspan=\"3\">40.022 3 32.946 16 31.646 4</td><td>-</td><td/><td>-</td><td colspan=\"3\">34.595 9 53.431 16 61.069 10</td><td>-</td><td>-</td><td colspan=\"3\">0.013 12 0.019 16 0.023 11</td><td>-</td><td>-</td></tr><tr><td>RDN-LIIF(Zhang et al., 2018c)</td><td/><td>39.690 9</td><td>34.831 3</td><td colspan=\"5\">31.832 3 28.894 1 23.778 1 34.224 8</td><td>50.434 8</td><td colspan=\"4\">60.563 9 72.753 2 87.488 2 0.012 8</td><td>0.018 9</td><td colspan=\"2\">0.022 9 0.029 2 0.041 2</td></tr><tr><td colspan=\"2\">RDN-OPESR(Zhang et al., 2018c)</td><td>40.188 2</td><td>34.959 2</td><td>31.475 5</td><td>-</td><td/><td>-</td><td colspan=\"2\">36.126 15 50.792 9</td><td>60.388 8</td><td>-</td><td>-</td><td colspan=\"2\">0.013 13 0.018 7</td><td>0.022 7</td><td>-</td><td>-</td></tr><tr><td>RDN-LTE(Zhang et al., 2018c)</td><td/><td>39.825 5</td><td>34.749 4</td><td colspan=\"5\">31.837 2 28.654 2 23.346 2 33.997 7</td><td>50.106 7</td><td colspan=\"4\">60.229 7 72.301 1 87.090 1 0.012 7</td><td>0.018 8</td><td colspan=\"2\">0.022 8 0.029 1 0.041 1</td></tr><tr><td colspan=\"3\">SwinIR-classical(Liang et al., 2021) 40.372 1</td><td>35.125 1</td><td>32.370 1</td><td>-</td><td/><td>-</td><td>32.750 5</td><td>48.380 4</td><td>58.579 4</td><td>-</td><td>-</td><td>0.012 5</td><td>0.017 5</td><td>0.021 5</td><td>-</td><td>-</td></tr><tr><td>ITSRN(Yang et al., 2021)</td><td/><td colspan=\"15\">31.254 18 26.178 18 25.876 18 25.619 5 21.566 5 41.928 17 53.459 17 62.927 17 74.260 5 88.160 4 0.015 17 0.020 17 0.024 17 0.030 5 0.042 5</td></tr><tr><td>Bicubic</td><td/><td colspan=\"15\">32.790 17 28.896 17 26.568 17 23.450 6 18.736 6 50.571 18 64.949 18 73.412 18 82.567 6 93.012 6 0.018 18 0.024 18 0.029 18 0.036 6 0.050 6</td></tr><tr><td>HAT-S(Chen et al., 2023)</td><td/><td colspan=\"3\">39.784 7 33.799 13 31.057 14</td><td>-</td><td/><td>-</td><td>32.387 2</td><td>48.047 3</td><td>58.232 3</td><td>-</td><td>-</td><td>0.011 2</td><td>0.017 3</td><td>0.021 3</td><td>-</td><td>-</td></tr><tr><td>HAT(Chen et al., 2023)</td><td/><td colspan=\"3\">39.784 7 33.913 11 31.196 12</td><td>-</td><td/><td>-</td><td>32.387 2</td><td>47.815 2</td><td>58.052 2</td><td>-</td><td>-</td><td>0.011 2</td><td>0.017 2</td><td>0.021 2</td><td>-</td><td>-</td></tr><tr><td>HDSRNet(Tian et al., 2024)</td><td/><td colspan=\"2\">39.458 12 34.203 9</td><td>31.325 8</td><td>-</td><td/><td>-</td><td colspan=\"3\">35.245 13 51.690 13 61.709 13</td><td>-</td><td>-</td><td colspan=\"3\">0.012 9 0.019 12 0.023 13</td><td>-</td><td>-</td></tr><tr><td>GRLBase(Li et al., 2023)</td><td/><td colspan=\"3\">39.990 4 33.840 12 31.121 13</td><td>-</td><td/><td>-</td><td>31.807 1</td><td>47.263 1</td><td>57.296 1</td><td>-</td><td>-</td><td>0.011 1</td><td>0.017 1</td><td>0.021 1</td><td>-</td><td>-</td></tr><tr><td>GRLSmall(Li et al., 2023)</td><td/><td colspan=\"3\">39.544 10 33.679 14 31.027 15</td><td>-</td><td/><td>-</td><td>32.714 4</td><td>48.593 5</td><td>58.805 5</td><td>-</td><td>-</td><td>0.012 4</td><td>0.017 4</td><td>0.021 4</td><td>-</td><td>-</td></tr><tr><td>GRLTiny(Li et al., 2023)</td><td/><td colspan=\"3\">39.205 16 33.197 15 30.556 16</td><td>-</td><td/><td>-</td><td>33.394 6</td><td>49.550 6</td><td>59.835 6</td><td>-</td><td>-</td><td>0.012 6</td><td>0.018 6</td><td>0.022 6</td><td>-</td><td>-</td></tr><tr><td/><td/><td/><td>FSDS (Ours)</td><td/><td/><td/><td/><td colspan=\"3\">ℓ2 Norm in Frequency Domain</td><td/><td/><td/><td colspan=\"2\">ℓ2 Norm</td></tr><tr><td>Method</td><td>×2</td><td>×3</td><td>×4</td><td>×6</td><td>×12</td><td>×2</td><td/><td>×3</td><td>×4</td><td>×6</td><td>×12</td><td>×2</td><td>×3</td><td>×4</td><td/><td>×6</td><td>×12</td></tr><tr><td>EDSR(Lim et al., 2017)</td><td colspan=\"3\">39.210 15 34.148 10 31.380 7</td><td>-</td><td>-</td><td colspan=\"4\">4966.905 13 11163.148 15 17206.009 16</td><td>-</td><td>-</td><td colspan=\"4\">4966.906 13 11163.149 15 17206.010 16</td><td>-</td><td>-</td></tr><tr><td>EDSR-LIIF(, 2017)</td><td>39.798 6</td><td colspan=\"2\">34.630 5 31.286 11</td><td>-</td><td>-</td><td colspan=\"2\">5247.229 16</td><td colspan=\"2\">11117.694 12 16818.598 14</td><td>-</td><td>-</td><td>5247.229 16</td><td colspan=\"3\">11117.695 12 16818.599 14</td><td>-</td><td>-</td></tr><tr><td>EDSR-SRNO(Lim et al., 2017)</td><td colspan=\"2\">39.533 11 34.527 7</td><td colspan=\"3\">31.448 6 28.458 3 22.778 4</td><td colspan=\"2\">4794.903 9</td><td colspan=\"4\">10845.390 10 16453.701 10 26236.246 3 48097.246 3</td><td>4794.903 9</td><td colspan=\"4\">10845.391 10 16453.702 10 26236.247 3 48097.249 3</td></tr><tr><td>EDSR-LTE(Lim et al., 2017)</td><td colspan=\"3\">39.290 14 34.328 8 31.303 10</td><td>-</td><td>-</td><td colspan=\"2\">4907.855 11</td><td colspan=\"2\">11032.252 11 16781.621 13</td><td>-</td><td>-</td><td>4907.855 11</td><td colspan=\"3\">11032.252 11 16781.622 13</td><td>-</td><td>-</td></tr><tr><td>RDN(Zhang et al., 2018c)</td><td colspan=\"3\">40.022 3 32.946 16 31.646 4</td><td>-</td><td>-</td><td colspan=\"2\">4820.540 10</td><td colspan=\"2\">12055.745 16 16462.594 11</td><td>-</td><td>-</td><td>4820.541 10</td><td colspan=\"3\">12055.746 16 16462.595 11</td><td>-</td><td>-</td></tr><tr><td>RDN-LIIF(Zhang et al., 2018c)</td><td>39.690 9</td><td>34.831 3</td><td colspan=\"3\">31.832 3 28.894 1 23.778 1</td><td colspan=\"2\">4627.806 8</td><td>10477.303 8</td><td colspan=\"3\">15993.797 8 25720.766 2 47650.681 2</td><td>4627.806 8</td><td>10477.304 8</td><td colspan=\"3\">15993.798 8 25720.768 2 47650.683 2</td></tr><tr><td>RDN-OPESR(Zhang et al., 2018c)</td><td>40.188 2</td><td>34.959 2</td><td>31.475 5</td><td>-</td><td>-</td><td colspan=\"2\">5061.535 15</td><td>10609.168 9</td><td>16034.925 9</td><td>-</td><td>-</td><td>5061.535 15</td><td>10609.169 9</td><td colspan=\"2\">16034.926 9</td><td>-</td><td>-</td></tr><tr><td>RDN-LTE(Zhang et al., 2018c)</td><td>39.825 5</td><td>34.749 4</td><td colspan=\"3\">31.837 2 28.654 2 23.346 2</td><td colspan=\"2\">4576.652 7</td><td>10349.273 7</td><td colspan=\"3\">15808.890 7 25384.597 1 46955.729 1</td><td>4576.652 7</td><td>10349.274 7</td><td colspan=\"3\">15808.891 7 25384.598 1 46955.731 1</td></tr><tr><td colspan=\"2\">SwinIR-classical(Liang et al., 2021) 40.372 1</td><td>35.125 1</td><td>32.370 1</td><td>-</td><td>-</td><td colspan=\"2\">4222.226 5</td><td>9617.212 5</td><td>14804.533 5</td><td>-</td><td>-</td><td>4222.226 5</td><td>9617.212 5</td><td colspan=\"2\">14804.534 5</td><td>-</td><td>-</td></tr><tr><td>ITSRN(, 2023)</td><td colspan=\"3\">39.784 7 33.799 13 31.057 14</td><td>-</td><td>-</td><td colspan=\"2\">4107.382 2</td><td>9423.447 3</td><td>14498.509 3</td><td>-</td><td>-</td><td>4107.383 2</td><td>9423.448 3</td><td colspan=\"2\">14498.510 3</td><td>-</td><td>-</td></tr><tr><td>HAT(Chen et al., 2023)</td><td colspan=\"3\">39.784 7 33.913 11 31.196 12</td><td>-</td><td>-</td><td colspan=\"2\">4107.382 2</td><td>9326.613 2</td><td>14407.457 2</td><td>-</td><td>-</td><td>4107.383 2</td><td>9326.613 2</td><td colspan=\"2\">14407.458 2</td><td>-</td><td>-</td></tr><tr><td>HDSRNet(Tian et al., 2024)</td><td colspan=\"2\">39.458 12 34.203 9</td><td>31.325 8</td><td>-</td><td>-</td><td colspan=\"2\">4948.237 12</td><td colspan=\"2\">11121.912 13 16697.185 12</td><td>-</td><td>-</td><td>4948.238 12</td><td colspan=\"3\">11121.912 13 16697.186 12</td><td>-</td><td>-</td></tr><tr><td>GRLBase(Li et al., 2023)</td><td colspan=\"3\">39.990 4 33.840 12 31.121 13</td><td>-</td><td>-</td><td colspan=\"2\">3937.013 1</td><td>9073.913 1</td><td>13959.078 1</td><td>-</td><td>-</td><td>3937.013 1</td><td>9073.913 1</td><td colspan=\"2\">13959.079 1</td><td>-</td><td>-</td></tr><tr><td>GRLSmall(Li et al., 2023)</td><td colspan=\"3\">39.544 10 33.679 14 31.027 15</td><td>-</td><td>-</td><td colspan=\"2\">4170.853 4</td><td>9585.326 4</td><td>14742.787 4</td><td>-</td><td>-</td><td>4170.853 4</td><td>9585.327 4</td><td colspan=\"2\">14742.788 4</td><td>-</td><td>-</td></tr><tr><td>GRLTiny(Li et al., 2023)</td><td colspan=\"3\">39.205 16 33.197 15 30.556 16</td><td>-</td><td>-</td><td colspan=\"2\">4384.364 6</td><td>10079.017 6</td><td>15488.316 6</td><td>-</td><td>-</td><td>4384.364 6</td><td>10079.018 6</td><td colspan=\"2\">15488.317 6</td><td>-</td><td>-</td></tr></table>", "num": null, "html": null}, "TABREF8": {"text": "Comparison between our proposed FSDS metric and ℓ2 norm in both frequency domain and image domain. Items with the highest mean values are highlighted in red and secondary mean values in blue. The gray superscripts denote the ranking order. To demonstrate <PERSON><PERSON><PERSON>'s theorem, we omit the mean operation when calculating ℓ2 norm.", "type_str": "table", "content": "<table/>", "num": null, "html": null}}}}