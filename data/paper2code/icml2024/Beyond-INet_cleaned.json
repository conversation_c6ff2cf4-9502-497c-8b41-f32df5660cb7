{"paper_id": "Beyond-INet", "title": "ConvNet vs Transformer, Supervised vs CLIP: Beyond ImageNet Accuracy", "abstract": "Modern computer vision offers a great variety of models to practitioners, and selecting a model from multiple options for specific applications can be challenging. Conventionally, competing model architectures and training protocols are compared by their classification accuracy on Ima-geNet. However, this single metric does not fully capture performance nuances critical for specialized tasks. In this work, we conduct an in-depth comparative analysis of model behaviors beyond ImageNet accuracy, for both ConvNet and Vision Transformer architectures, each across supervised and CLIP training paradigms. Although our selected models have similar ImageNet accuracies and compute requirements, we find that they differ in many other aspects: types of mistakes, output calibration, transferability, and feature invariance, among others. This diversity in model characteristics, not captured by traditional metrics, highlights the need for more nuanced analysis when choosing among different models. Code is available at github.com/kirill-vish/Beyond-INet.", "pdf_parse": {"paper_id": "Beyond-INet", "abstract": [{"text": "Modern computer vision offers a great variety of models to practitioners, and selecting a model from multiple options for specific applications can be challenging. Conventionally, competing model architectures and training protocols are compared by their classification accuracy on Ima-geNet. However, this single metric does not fully capture performance nuances critical for specialized tasks. In this work, we conduct an in-depth comparative analysis of model behaviors beyond ImageNet accuracy, for both ConvNet and Vision Transformer architectures, each across supervised and CLIP training paradigms. Although our selected models have similar ImageNet accuracies and compute requirements, we find that they differ in many other aspects: types of mistakes, output calibration, transferability, and feature invariance, among others. This diversity in model characteristics, not captured by traditional metrics, highlights the need for more nuanced analysis when choosing among different models. Code is available at github.com/kirill-vish/Beyond-INet.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "mary metric for evaluating model performance. It has driven remarkable progress since it ignited the deep learning revolution (<PERSON><PERSON><PERSON><PERSON> et al., 2012) . However, this metric is becoming increasingly insufficient. While ImageNet is useful to measure a model's general capability, it does not capture the nuanced differences arising from varying architectures, training paradigms, and data -models with different properties may appear similar if judged solely based on ImageNet accuracy (Fig. 1 ). This limitation becomes more pronounced as models start to overfit the idiosyncrasies of ImageNet with saturated accuracies (<PERSON><PERSON> et al., 2020) .", "section": "Introduction", "sec_num": "1."}, {"text": "A particularly noteworthy example is CLIP. Despite having a similar ImageNet accuracy as a ResNet (<PERSON> et al., 2016 ), CLIP's vision encoder exhibits much better robustness and transferability. This has sparked research that explores and builds upon the unique strengths of CLIP (<PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2023) , which were not evident from the ImageNet metric alone. This demonstrates that analyzing alternative properties could help discover useful models.", "section": "Introduction", "sec_num": "1."}, {"text": "In addition to fundamental research, the growing integration of vision models into production systems also calls for a deep understanding of their behaviors. Conventional metrics do not fully capture models' ability to handle real-world vision challenges like varying camera poses, lighting condi- tions, or occlusions. For instance, models trained on datasets such as ImageNet often struggle (<PERSON>mada & Otani, 2022) to transfer their performance to real-world applications where conditions and scenarios are more diverse.", "section": "Introduction", "sec_num": "1."}, {"text": "To bridge this gap, we conduct an in-depth exploration focusing on model behaviors beyond ImageNet accuracy. We analyze four leading models in the computer vision: Con-vNeXt (<PERSON> et al., 2022) , as a representative ConvNet, and Vision Transformer (ViT) (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020) , each under supervised and CLIP training. The selected models are similar in parameter counts and show nearly identical accuracy on ImageNet-1K within each training paradigm, ensuring a fair comparison. Our study delves into a wide array of model characteristics, such as types of prediction errors, generalization capabilities, invariances of the learned representations, calibration, and many others. Importantly, our focus is on properties exhibited by the model without additional training or finetuning, providing insights for practitioners interested in using pretrained models directly.", "section": "Introduction", "sec_num": "1."}, {"text": "In our analysis, we discover substantial variations in model behaviors among different architectures and training paradigms. For example, CLIP models make fewer classification errors relative to their ImageNet performance. However, supervised models are better calibrated and superior on ImageNet robustness benchmarks. ConvNeXt has an advantage on synthetic data but is more texture-biased than ViT. We also find that supervised ConvNeXt excels on many benchmarks and achieves transferability comparable to that of CLIP models. Based on these findings, it becomes evident that various models demonstrate their strengths in unique ways that are not captured by a single metric. Our research emphasizes the need for more detailed evaluation metrics for accurate, context-specific model selection and the creation of new benchmarks unrelated to ImageNet.", "section": "Introduction", "sec_num": "1."}, {"text": "For analyzing ConvNets and Transformers, many previous works (<PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON> et al., 2021) compare ResNet and ViT. This comparison is often disadvantageous for ConvNet since ViTs are typically trained with more advanced recipes, achieving higher ImageNet accuracy. ViT also has architecture design elements, e.g., LayerNorm (<PERSON> et al., 2016) , that were not incorporated in ResNet when it was invented years ago. For a more balanced evaluation, we compare ViT with Con-vNeXt (<PERSON> et al., 2022) , a modern of ConvNet that matches Transformers' performance.", "section": "Models", "sec_num": "2."}, {"text": "As for the training paradigms, we compare supervised and CLIP. Supervised models continue to show state-of-the-art performance in computer vision (<PERSON><PERSON><PERSON><PERSON> et al., 2023) . CLIP models, on the other hand, excel in generalization and transferability, and offer intriguing representational properties that connect vision and language. Self-supervised models (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2023) are not included in the results as they showed behaviors similar to supervised models in our preliminary tests. This could be due to their final ImageNet-1K supervised finetuning, which is necessary for studying many properties.", "section": "Models", "sec_num": "2."}, {"text": "The selected models have similar ImageNet-1K validation accuracies within their respective training paradigms, ensuring a fair comparison. For CLIP models, these indicate their zero-shot accuracies. The models also have similar sizes and computational requirements, and are publicly available. Since we are using pretrained models, we cannot control for the number and quality of data samples seen during training.", "section": "Models", "sec_num": "2."}, {"text": "For supervised models, we use a pretrained DeiT3-Base/16 (<PERSON><PERSON><PERSON><PERSON> et al., 2022) for ViT, which shares the same architecture as ViT-Base/16 with an improved training recipe, and ConvNeXt-Base (<PERSON> et al., 2022) . For CLIP models, we use vision encoders of ViT-Base/16 and ConvNeXt-Base from OpenCLIP (<PERSON>har<PERSON> et al., 2021) . Note that these models have a slightly different performance from the original OpenAI models (<PERSON><PERSON> et al., 2021) . A detailed model comparison is given in Table 1 .", "section": "Models", "sec_num": "2."}, {"text": "We recognize there are other ViT CLIP models pretrained on larger datasets such as LAION-2B, DataComp (<PERSON><PERSON><PERSON> et al., 2023) , DFN (<PERSON> et al., 2023a) , which show a better performance. However, OpenCLIP offers only a few pretrained ConvNeXt models and for most of them there is no matching ViT counterpart in terms of ImageNet accuracy, pretraining dataset and parameter count. Therefore, we chose CLIP models pretrained on LAION-400M, offering the most fair comparison between ConvNet and ViT.", "section": "Models", "sec_num": "2."}, {"text": "Additional models of different sizes. In the Appendix A, we present the performance results for models of different sizes to evaluate the impact of model size on performance. These models are assessed across several benchmarks, including ImageNet-X, PUG-ImageNet, calibration, invariance, and shape/texture bias. ", "section": "Models", "sec_num": "2."}, {"text": "Our analysis is designed to investigate model behaviors that can be evaluated without the need for further training or finetuning. This approach is particularly relevant for practitioners with limited compute resources, who often rely on pretrained models. While we recognize the value of downstream tasks like object detection, our focus is on properties that offer insights with minimal computational demands and reflect behaviors important for real-world applications.", "section": "Property Analysis", "sec_num": "3."}, {"text": "In image classification, a model mistake is an incorrect label assignment, such as misclassifying a cat as a dog. Simply identifying mistaken object classes might not offer actionable insights for model improvement. The key aspect, therefore, is finding the specific reasons for these mistakes. For instance, some models may be particularly sensitive to certain aspects of the data distribution, like texture variations. In this case, a model might consistently make mistakes when the texture of the object differs from what it has been trained on. Identifying mistake types allows for targeted data collection and retraining, improving over a black-box approach.", "section": "Model Mistakes", "sec_num": "3.1."}, {"text": "The ImageNet-X dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2022) offers detailed human annotations for 16 factors of variation, such as pose, style, and others. This allows a focused analysis of models' mistake types. The annotations enable measuring model error ratios for each factor independently: error ratio(factor) = 1-accuracy(factor) 1-accuracy(overall) , where accuracy(overall) is the overall ImageNet-1K validation accuracy, and accuracy(factor) is the accuracy on all the images where the factor was highlighted. This metric measures the model performance on a given factor relative to its overall performance. Lower error ratios indicate better performance, implying higher accuracy for the specific factor. Our results on ImageNet-X are presented in Fig. 2 .", "section": "Model Mistakes", "sec_num": "3.1."}, {"text": "CLIP models make fewer mistakes relative to their Im-ageNet accuracy than supervised. The diagram in Fig. 2 shows that CLIP models have a smaller error ratio, indicating a significant advantage over supervised models. However, it is important to note that the error ratio is relative to overall ImageNet accuracy, where a significant 18% gap exists between supervised and CLIP zero-shot models. In particular, CLIP models are much more robust towards shape, subcategory, texture, object blocking, and darker factors.", "section": "Model Mistakes", "sec_num": "3.1."}, {"text": "The key reason for the success of CLIP models is likely the more diverse data used for training.", "section": "Model Mistakes", "sec_num": "3.1."}, {"text": "All models suffer mostly from complex factors like occlusion. For CLIP models, there are three factors with dissimilar performance between ConvNeXt and ViT: multiple objects, style, and darker. For the first two, the ConvNeXt has a higher error ratio, while for the latter, it has an advantage over ViT. For supervised models, the performance only diverges for style and person blocking. Except for these factors, models largely have similar error ratios. The six factors for which all the models have a high error ratio are smaller, object blocking, person blocking, shape, subcategory, and texture. High error ratio factors usually involve complex visual scenarios, which helps to explain why models often make mistakes in these situations. For example, in occlusion, the model often misclassifies due to focusing on the visible, obscuring object. Texture is the most challenging factor for all models.", "section": "Model Mistakes", "sec_num": "3.1."}, {"text": "Interestingly, all models in our analysis have the largest error ratio on the texture factor. It refers to images where the texture of the object differs from its standard appearance. This suggests that models of the current generation largely suffer because of texture bias.", "section": "Model Mistakes", "sec_num": "3.1."}, {"text": "In contrast to humans, who generally use high-level visual cues for recognition, neural networks often rely on more brittle shortcut features (<PERSON><PERSON><PERSON><PERSON> et al., 2020) . The study of shape-texture bias (<PERSON><PERSON><PERSON><PERSON> et al., 2018) serves to highlight this phenomenon by examining model behavior on cue-conflict images, which contain a shape from one class superimposed with the texture from another (Fig. 4 ). Two key metrics are introduced to quantify this bias: the shape and the texture fractions. The shape fraction calculates the proportion of decisions leaning towards the class represented by the shape, while the texture fraction measures those for the texture class. These metrics reveal whether the classifier favors shape or texture when they conflict.", "section": "Shape / Texture Bias", "sec_num": "3.2."}, {"text": "The study in (<PERSON><PERSON><PERSON><PERSON> et al., 2018) showed that ConvNets have a strong bias towards texture, as opposed to shape, which differs from humans. Subsequent work (<PERSON><PERSON><PERSON> et al., 2021) concluded that ViT is less biased towards the texture than ConvNet by comparing the first generation of DeiT-S (<PERSON><PERSON><PERSON><PERSON> et al., 2021) and ResNet-50. Remarkably, scaling large Transformer models has led to shape biases comparable to human level (<PERSON><PERSON><PERSON><PERSON> et al., 2023) .", "section": "Shape / Texture Bias", "sec_num": "3.2."}, {"text": "We evaluate shape-texture bias in our models using cueconflict images and display the findings in Fig. 3 . Dashed lines represent average shape bias aggregated over all the categories. Individual markers on horizontal lines depict shape bias for the particular class, which is identified by a corresponding logo on the y-axis. The shape fraction is represented on the top x-axis of the diagrams, while the bottom x-axis indicates the texture fraction.", "section": "Shape / Texture Bias", "sec_num": "3.2."}, {"text": "CLIP models have smaller texture bias than supervised.", "section": "Shape / Texture Bias", "sec_num": "3.2."}, {"text": "In Fig. 3 , we can observe that ViTs exhibit stronger shape bias than ConvNeXts for both supervised and CLIP models. This is possibly because ConvNeXt is more inclined to learn local features related to textures due to the local nature of convolution operation. However, the gap between ViT and ConvNeXt is much smaller for CLIP-based models. Notably, the shape bias in CLIP models improved by 7% and 12% for both architectures, prompting questions about the benefits of further scaling the training data. ConvNets typically exhibit lower shape bias compared to ViT, however, the gap for CLIP models is marginal. In (<PERSON><PERSON><PERSON> et al., 2023) , it has been shown that a 22B parameter ViT model can achieve 87% shape bias. In our analysis, the ViT CLIP model achieved a maximum shape bias of 46.4%, suggesting that the model size might also play an important role.", "section": "Shape / Texture Bias", "sec_num": "3.2."}, {"text": "Besides vulnerability to shortcut features, poor model performance can often be attributed to miscalibration, where a model's confidence in its predictions does not align with actual accuracy. Model calibration is a metric that quantifies the reliability of a model's predicted confidence levels (<PERSON> et al., 2017) . A model's confidence for a prediction is defined as the max probability among all classes in its output distribution. We are interested in determining whether the model is overly confident or too uncertain in its predictions.", "section": "Model Calibration", "sec_num": "3.3."}, {"text": "For instance, if the network deems a set of predictions to be 80% confident, does the actual accuracy hover around 80%?", "section": "Model Calibration", "sec_num": "3.3."}, {"text": "The calibration rate can be quantified by Expected Calibration Error (ECE). To calculate ECE, predictions first need to be separated into the M bins B 1 , . . . , B M based on their confidence. For instance, one bin can include all the predictions with confidence between 50% and 60% and so on. Each bin's confidence and accuracy are calculated as the average confidence and accuracy of predictions in B i , represented as conf(B i ) and acc(B i ). Then, ECE can be defined as: ECE", "section": "Model Calibration", "sec_num": "3.3."}, {"text": "= M i |Bi| n |acc(B i ) -conf(B i )| , where |B i | is the size of the i-th bin.", "section": "Model Calibration", "sec_num": "3.3."}, {"text": "Model calibration is also often assessed through visualizations, including reliability diagrams and confidence histograms. Reliability diagrams plot the predicted confidence against accuracy; a well-calibrated model would show a graph where points closely align with the diagonal. Confidence histograms display how often different confidence levels occur in the model's predictions.", "section": "Model Calibration", "sec_num": "3.3."}, {"text": "For a balanced evaluation, we present calibration metrics on two different datasets: ImageNet-1K for in-distribution data and ImageNet-R (<PERSON><PERSON><PERSON><PERSON> et al., 2021a) for outof-distribution data. We select ImageNet-R as the out-ofdistribution because CLIP models show higher accuracy on it than supervised. In all experiments, we use M = 15 bins. We plot confidence histograms (1 and 3 rows), reliability diagrams (2 and 4 rows), and ECE in Fig. 5 .", "section": "Model Calibration", "sec_num": "3.3."}, {"text": "CLIP models are overconfident and supervised models are slightly underconfident. In Fig. 5 , we observe that CLIP models have bars consistently below the diagonal in reliability diagrams and a notably high last bar in the confidence histogram, signaling overconfidence in both in-distribution and out-of-distribution data. Although (<PERSON><PERSON> et al., 2021) attributes calibration performance mainly to architecture, our results suggest otherwise: higher ECE scores in CLIP models, despite superior accuracy on ImageNet-R, indicate that training data and objectives could be more influential factors. We also highlight that our results are different from (<PERSON><PERSON> et al., 2021) for CLIP models presumably because they use checkpoints from Ope-nAI (<PERSON><PERSON> et al., 2021) and we use from OpenCLIP (Ilhar<PERSON> et al., 2021) . In the lower part of Fig. 5 related to ImageNet-R, we note that supervised models exhibit a higher density in the lower confidence intervals of the confidence histograms (3 row). Additionally, these models show elevated accuracy levels in the initial bins of the reliability diagrams (4 row). These findings suggest that supervised models tend to be slightly underconfident on ImageNet-R. This discrepancy is because (<PERSON><PERSON> et al., 2021) focused on older ConvNet architectures, such as ResNet, while we use a more modern one. For CLIP models, we find that ViT is only slightly better than ConvNeXt.", "section": "Model Calibration", "sec_num": "3.3."}, {"text": "A model may excel on data from its training distribution but struggle to generalize to a distribution shift (<PERSON> et al., 2019) . These shifts can arise from natural perturbations such as atmospheric conditions (e.g., fog, rain), camera noise, or variations in object location and orientation. Model robustness quantifies a model's capability to adapt to changes in data distributions. A robust model should maintain high accuracy with these perturbations. This is particularly important for applications where reliability is a primary concern.", "section": "Rob<PERSON><PERSON>", "sec_num": "3.4."}, {"text": "We evaluate the robustness on several ImageNet variants that feature many types of natural variations and corruptions: V2 (<PERSON><PERSON> et al., 2019) , A (<PERSON><PERSON><PERSON><PERSON> et al., 2021b) , C (Hendrycks & Dietterich, 2019) , R (<PERSON><PERSON><PERSON><PERSON> et al., 2021a) , Sketch (<PERSON> et al., 2019) , Real (<PERSON> et al., 2020) , and Hard (<PERSON><PERSON><PERSON> et al.) . We also provide ImageNet-1K validation accuracy for reference (INet-Val) . The results are shown in Fig. 6 (top).", "section": "Rob<PERSON><PERSON>", "sec_num": "3.4."}, {"text": "Supervised models are better than CLIP on most of the robustness benchmarks. In Fig. 6 , we can see that supervised models perform better than CLIP on most datasets except ImageNet-R and ImageNet-Sketch. CLIP models' success on ImageNet-R and ImageNet-Sketch suggests they handle abstract or creative visuals better than supervised models. The advantage of supervised models is likely related to the fact that all robustness datasets share the same set of classes as the original ImageNet-1K, on which they were finetuned. This underscores the need for the development of new robustness benchmarks that are not directly related to ImageNet. Additionally, CLIP models may achieve higher performance when pretrained on larger datasets (Fang et al., 2023a; <PERSON><PERSON><PERSON> et al., 2023) . ViT and ConvNeXt, on average, have similar performance across both supervised and CLIP.", "section": "Rob<PERSON><PERSON>", "sec_num": "3.4."}, {"text": "The transfer learning performance of a model indicates its ability to adapt to new tasks and datasets beyond its original training domain (<PERSON><PERSON> et al., 2020) . Good transferability allows for rapid finetuning with minimal additional effort, making it easier to scale the model to a wide range of real-world applications. The ability of a model to adapt to these shifts without significant degradation in performance serves as a valuable metric for its utility and generalization capabilities. For instance, consider a model that has been originally trained on ImageNet, which primarily consists of natural images. A test of its transferability would be to evaluate how well this model performs when applied to a vastly different domain, such as medical imaging.", "section": "Transferability", "sec_num": "3.5."}, {"text": "To assess the transferability, we adopted a VTAB benchmark (<PERSON><PERSON> et al., 2019) . It comprises 19 diverse datasets grouped into three subcategories: natural, specialized, and structured. We conduct a linear probing evaluation on frozen features, following the protocol from (<PERSON><PERSON><PERSON> et al., 2021) . The results are shown in Fig. 6 (bottom) and Table 2 Supervised ConvNeXt has great transferability, almost matching the performance of CLIP models. We find that ConvNeXt strongly outperforms ViT for supervised. Interestingly the performance of supervised ConvNeXt is not very far from CLIP models, both of which have the same average accuracy. For CLIP, ViT and ConvNeXt demonstrate similar average accuracy, with many datasets showing a performance gap of less than 1%. CLIP models generally show better transferability on all three subgroups of VTAB (Table 2 ), which is different from the robustness experiments. The superiority of CLIP can be attributed to the larger and more diverse volume of pretraining data (<PERSON><PERSON><PERSON> et al., 2023) .", "section": "Transferability", "sec_num": "3.5."}, {"text": "While two previous sections focused on robustness and transferability, they did not cover the new and promising area of training models with synthetic data (<PERSON><PERSON> et al., 2023) . Unlike human-annotated data, synthetic datasets allow precise control over the content and quality of data.", "section": "Synthetic Data", "sec_num": "3.6."}, {"text": "PUG-ImageNet (<PERSON><PERSON> et al., 2023) is a synthetic dataset of photorealistic images of ImageNet classes that provides labels for a set of factors. The images are generated using a software that allows systematically varying factors like pose, size, texture, and others for each object. In our experiments, we provide top-1 accuracy results for ten different factors in PUG-ImageNet and their average in Fig. 7 .", "section": "Synthetic Data", "sec_num": "3.6."}, {"text": "ConvNeXt is better than ViT on synthetic data. Intriguingly, ConvNeXt outperforms ViT on PUG-ImageNet for nearly all factors. This suggests: ConvNeXt is better than ViT on synthetic data. CLIP models have lower accuracy compared to supervised, which is likely related to their inferior performance on the original ImageNet.", "section": "Synthetic Data", "sec_num": "3.6."}, {"text": "In real-world scenarios, data often undergo transformations that preserve its semantic meaning or class. We aim to ensure that the model's representations are invariant to these transformations. Achieving various types of invariance is desirable because it enables the network to generalize well across different but semantically similar inputs, thereby enhancing its robustness and predictive power. In previous literature (<PERSON><PERSON><PERSON> & <PERSON>, 2018; Zhang, 2019) , it has been shown that the performance of neural networks can be highly unstable even under simple input data transformations, such as shifting an input image by a few pixels.", "section": "Transformation Invariance", "sec_num": "3.7."}, {"text": "We conduct experiments to assess three types of invariance: scale, shift, and resolution. We analyze the model's accuracy trends on the ImageNet-1K validation set as a function of varying scale / shift magnitude and image resolution. In scale invariance analysis, the image is first resized according to a given scale factor, and then a central crop is taken.", "section": "Transformation Invariance", "sec_num": "3.7."}, {"text": "In shift experiments, we adjust the crop location in the original image space and then take a crop, shifting along the longer side of the image. In resolution experiments with ViT model, we interpolate positional embeddings to match the new applied resolution.", "section": "Transformation Invariance", "sec_num": "3.7."}, {"text": "Architecture analysis. Several works compared ViTs and ConvNeXt from the perspective of internal representations (<PERSON><PERSON><PERSON> et al., 2021) , synthetic data (<PERSON> et al., 2022) , transferability (<PERSON> et al., 2021) , and robustness (<PERSON> et al., 2022; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2021) . Other studies included analysis of Transformer properties (<PERSON><PERSON><PERSON> et al., 2021) and impact of neural network width and depth on learned representations (<PERSON><PERSON><PERSON> et al., 2020) . ViTs and ConvNets were also evaluated on Im-ageNet, showing that Transformers are more aligned with human error patterns (<PERSON><PERSON> et al., 2021) . A large variety of backbones, trained with various methods, were benchmarked in (<PERSON><PERSON><PERSON> et al., 2024) across a diverse set of computer vision tasks, including classification, detection, and retrieval. In contrast to studies that analyze a single property, our work extensively compares models across many, maintaining a fair comparison by evaluating models with similar ImageNet accuracies.", "section": "Related Work", "sec_num": "4."}, {"text": "Training objective analysis. A comprehensive analysis was conducted in (<PERSON><PERSON><PERSON> et al., 2023) , comparing ViTs trained with supervised, self-supervised, and CLIP objectives. Anal-ysis of the representations of models trained with supervised and self-supervised objectives was presented in (<PERSON><PERSON><PERSON> et al., 2021; Gwilliam & Shrivastava, 2022) . Two works (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) focused on investigating the effect of training objective in self-supervised learning.", "section": "Related Work", "sec_num": "4."}, {"text": "Unlike studies emphasizing self-supervised models, our work compares supervised and CLIP models.", "section": "Related Work", "sec_num": "4."}, {"text": "Limitations of ImageNet. Recent research (<PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2021) highlighted issues with the reliability and quality of ImageNet labels. Two studies (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2021) showed a strong relationship between performance on ImageNet and other datasets, although this can depend on the model's architecture and training methods.", "section": "Related Work", "sec_num": "4."}, {"text": "Other studies (<PERSON> et al., 2023; <PERSON> et al., 2023b) showed that high ImageNet accuracy does not ensure good performance on diverse datasets. Current robustification training techniques were found to overfit (Yamada & Otani, 2022) to ImageNet evaluations. In addition, ImageNet suffers from dichotomous data difficulty (<PERSON><PERSON> et al., 2021) , obscuring differences between models. Our analysis does not directly address data-related problems of ImageNet but instead studies alternative properties.", "section": "Related Work", "sec_num": "4."}, {"text": "Our study examined ConvNets and Transformers with supervised and CLIP training from multiple perspectives beyond the standard ImageNet accuracy. We found that models with similar ImageNet accuracies have vastly different properties. This suggests that model selection should depend on the target use cases, as standard metrics may overlook key nuances. In addition, it is crucial to develop new benchmarks with data distributions that closely mirror real-world scenarios. This will help both in training models for better (2) Supervised models are better at robustness benchmarks, likely because these are ImageNet variants. (3) CLIP models have a higher shape bias and make less classification errors relative to their ImageNet accuracy.", "section": "Conclusion", "sec_num": "5."}, {"text": "As a result of our analysis, we suggest using supervised ConvNeXt when the target task distribution is not very different from ImageNet as this model provides competitive performance among many benchmarks. In case of a serious domain shift, we recommend using CLIP models.", "section": "Conclusion", "sec_num": "5."}], "back_matter": [{"text": "Acknowledgments. We would like to thank <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>urbek Tastan for their valuable feedback and suggestions. Use of all datasets and all experiments conducted by MBZUAI.", "section": "acknowledgement", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the fields of Machine Learning, Deep Learning and Computer Vision. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.", "section": "Impact Statement", "sec_num": null}, {"text": "Supervised ConvNeXt is the most invariant model to the data transformations. We display our results in Fig. 8 , observing a consistent trend of ConvNeXt outperforming ViT under supervised training. Interestingly, supervised ConvNeXt has better performance on 336 pixel resolution than on the original resolution of 224 pixels. Overall, all models are robust to shifting and less robust to scaling and resolution transforms. For practical use cases requiring high transform invariance, our results indicate that supervised ConvNeXt will be the best choice among analyzed models.", "section": "annex", "sec_num": null}, {"text": "Besides having the base sized models in the main part of our analysis, we additionally include the following models to see the effect of the model size on the performance:• Supervised ConvNeXt: Tiny, Small, Large, Huge (XLarge).• Supervised ViT (DeiT 3): Small, Large, Huge (XLarge). Note that no Tiny version is provided for DeiT 3 by the original authors (<PERSON><PERSON><PERSON><PERSON> et al., 2022) .• CLIP ConvNeXt: Large, Huge (XLarge).• CLIP ViT: Large, Huge.All new additional CLIP models are pretrained on the LAION-2B dataset, while the CLIP models from the main part of the manuscript (Table 1 ) are pretrained on LAION-400M. All CLIP models are taken from OpenCLIP, and supervised models are taken from the respective original works (<PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2022) . Additional models are evaluated on PUG-ImageNet (Table 4 ), ImageNet-X (Table 5 ), calibration (Table 6 and 7 ), transformation invariance (Table 8 ), and shape / texture bias (Table 9 ). Based on the results from new models we make the following observations:• On PUG-ImageNet (Table 4 ) ConvNeXt is better than ViT in 6 out of 7 comparison, suggesting its clear advantage over ViT on synthetic data.• ImageNet-X performance is largely determined by the training method (Table 5 ). CLIP models are clearly better than supervised.• On calibration (Table 6 and 7 ) ConvNeXt has lower ECE value compared to its ViT counterpart in most cases. This solidifies our initial conclusion in the main part that ConvNeXt is better calibrated than ViT.• Shape bias greatly improves with scale (Table 9 ). Interestingly, the ConvNeXt is better than ViT on large-scale CLIP models (Large and Huge). For Huge CLIP models ConvNeXt has an advantage of almost 10% over ViT. This suggests that training method and model size has a noticeable influence on the shape bias of the model. Moreover, this result also highlights that ConvNeXt has the potential to exceed a ViT on this benchmark.• Even small supervised models are robust to shift transformation (Table 8 middle part). For example, the smallest supervised model ConvNeXt-Tiny has a tiny degradation of < 3% which is comparable to the huge CLIP models.• Huge supervised models are quite reliable to the resolution change (Table 8 right part).• In general, large models provide a decent improvement over the base models. However, huge models provide only marginal improvement over the large models. ", "section": "Appendix A. Results for Models of Different Sizes", "sec_num": null}], "ref_entries": {"FIGREF0": {"num": null, "type_str": "figure", "text": "Figure 1. Models are often compared only by their ImageNet accuracy, without looking at many other important behaviors. In our work, we analyze models with similar ImageNet accuracies and find that they have vastly different properties.", "fig_num": "1", "uris": null}, "FIGREF1": {"num": null, "type_str": "figure", "text": "Figure 2. Model mistakes on ImageNet-X. Lower is better. ConvNeXt and ViT perform similarly within each training category. CLIP models achieve lower error ratios compared to supervised.", "fig_num": "2", "uris": null}, "FIGREF2": {"num": null, "type_str": "figure", "text": "Figure3. Fraction of shape vs texture decisions on cue-conflict dataset. ViT models show a higher shape bias. CLIP models are less texture-biased than their supervised counterparts. All models still have a significant fraction of texture decisions.", "fig_num": "3", "uris": null}, "FIGREF3": {"num": null, "type_str": "figure", "text": "Figure4. A cue-conflict image(<PERSON><PERSON><PERSON><PERSON> et al., 2018).", "fig_num": "4", "uris": null}, "FIGREF4": {"num": null, "type_str": "figure", "text": "Figure 5. Calibration results: confidence histograms (1 and 3 row), reliability diagrams (2 and 4 row), and ECE on ImageNet-1K (top) and ImageNet-R (bottom). Supervised models have lower ECE in both cases. CLIP models have bars under diagonal and many high confidence predictions, indicating overconfidence. ConvNeXt is better (ImageNet-1K) or competitive (ImageNet-R) to ViT.", "fig_num": "5", "uris": null}, "FIGREF5": {"num": null, "type_str": "figure", "text": "Figure 7. Results on synthetic data from PUG-ImageNet. ConvNeXt is superior on almost every factor for both supervised and CLIP.", "fig_num": "7", "uris": null}, "FIGREF6": {"num": null, "type_str": "figure", "text": "ConvNet vs Transformer, Supervised vs CLIP: Beyond ImageNet Accuracy real-world performance and in more accurately evaluating their effectiveness in such environments.ConvNet vs Transformer.(1) Supervised ConvNeXt is superior to supervised ViT: it is more invariant to data transformations, and demonstrates better transferability, robustness and calibration. (2) ConvNeXt outperforms ViT on synthetic data. (3) ViT has a higher shape bias.Supervised vs CLIP.(1) Supervised ConvNeXt competes well with CLIP in transferability, showing potential of supervised models.", "fig_num": null, "uris": null}, "TABREF0": {"html": null, "num": null, "type_str": "table", "text": "Model summary in our analysis. We select ConvNeXt and ViT with similar ImageNet accuracies within each training paradigm.", "content": "<table><tr><td>Model</td><td>Architecture</td><td>Pretraining</td><td>Finetuning</td><td>Paradigm</td><td>FLOPs</td><td>#Param</td><td>INet-1K val%</td></tr><tr><td>ViT-sup</td><td>ViT-B/16</td><td>ImageNet-21K</td><td>ImageNet-1K</td><td>supervised</td><td>17.5G</td><td>87M</td><td>85.5</td></tr><tr><td>ConvNeXt-sup</td><td>ConvNeXt-B</td><td>ImageNet-21K</td><td>ImageNet-1K</td><td>supervised</td><td>15.4G</td><td>89M</td><td>85.5</td></tr><tr><td>ViT-clip</td><td>ViT-B/16</td><td>LAION-400M</td><td>-</td><td>CLIP</td><td>17.5G</td><td>87M</td><td>67.0</td></tr><tr><td>ConvNeXt-clip</td><td>ConvNeXt-B</td><td>LAION-400M</td><td>-</td><td>CLIP</td><td>15.4G</td><td>89M</td><td>66.3</td></tr></table>"}, "TABREF1": {"html": null, "num": null, "type_str": "table", "text": "", "content": "<table><tr><td>ConvNet vs Transformer, Supervised vs CLIP: Beyond ImageNet Accuracy 0% 10% 20% Difference in Performance -10% Caltech101 +0.6 / 90.3% CIFAR-100 +2.5 / 84.5% DTD +5.7 / 73.5% Flowers102 +3.1 / 97.9% Pets +0.7 / 94.1% Sun397 +2.6 / 98.5% SVHN +6.3 / 71.3% Camelyon +0.4 / 82.8% EuroSAT +0.9 / 95.9% Resisc45 +2.1 / 90.7% Retinopathy +0.2 / 70.6% Clevr-Count +6.7 / 63.3% Clevr-Dist +6.1 / 56.1% DMLAB +2.2 / 49.1% dSpr-Loc dSpr-Ori +10.5 / 88.3% -20% +19.1 / 65.5% KITTI-Dist +11.7 / 43.9% sNORB-Azim +2.6 / 15.0% sNORB-Elev +1.5 / 30.7% Average +3.2 / 71.0 INet-Val +0.0 / 85.5% V2 +0.6 / 76.2% A +7.1 / 60.0% C +7.0 / 66.8% Hard +0.9 / 29.4% R +0.7 / 62.3% Sketch +2.5 / 48.6% Real +0.3 / 89.5% Average +0.5 / 60.7% ConvNeXt-sup ViT-sup Robustness Transferability -10% -8% -6% -4% -2% 0% Difference in Performance 2% Caltech101 +0.5 / 95.8% CIFAR-100 DTD +0.1 / 79.4% Flowers102 +0.1 / 96.4% Pets +1.0 / 91.7% 4% Sun397 +0.3 / 97.7% SVHN +7.5 / 78.1% Camelyon +1.6 / 83.6% EuroSAT +0.5 / 96.4% Resisc45 +1.2 / 94.1% Retinopathy +0.3 / 76.9% Clevr-Count +3.5 / 71.9% 6% +4.9 / 82.1% 8% Clevr-Dist +3.4 / 55.3% DMLAB +0.4 / 51.8% dSpr-Loc +0.9 / 55.7% dSpr-Ori +6.2 / 83.6% KITTI-Dist +3.8 / 47.4% sNORB-Azim +0.3 / 14.2% sNORB-Elev +1.5 / 38.2% Average +0.0 / 72.2 INet-Val +0.7 / 67.0% V2 +0.9 / 59.5% A +2.3 / 33.2% C +1.8 / 40.8% Hard +0.3 / 5.8% R +2.0 / 77.8% Sketch +0.3 / 52.3% Real +0.5 / 74.3% Average +1.1 /</td></tr></table>"}, "TABREF2": {"html": null, "num": null, "type_str": "table", "text": "ConvNet vs Transformer, Supervised vs CLIP: Beyond ImageNet Accuracy Worlds Cam Pitch Cam Yaw Cam Roll Obj Pitch Obj Yaw Obj Roll Obj Scale Obj Texture Scene Light Average 0", "content": "<table><tr><td>Accuracy</td><td>20 40</td><td>ViT-sup ConvNeXt-sup</td></tr><tr><td>Accuracy</td><td>20 40</td><td>ViT-clip ConvNeXt-clip</td></tr><tr><td/><td>0</td><td>Worlds Cam Pitch Cam Yaw Cam Roll Obj Pitch Obj Yaw Obj Roll Obj Scale Obj Texture Scene Light Average</td></tr></table>"}, "TABREF3": {"html": null, "num": null, "type_str": "table", "text": ". Transferability results on VTAB in subgroups. CLIP models are better on each of the dataset subgroups. For supervised models, ConvNeXt outperforms ViT by a large margin.", "content": "<table><tr><td>Model</td><td colspan=\"4\">Natural Specialized Structured Overall</td></tr><tr><td>ViT-sup</td><td>84.2</td><td>84.2</td><td>45.4</td><td>67.8</td></tr><tr><td>ConvNeXt-sup</td><td>87.1</td><td>85.0</td><td>50.0</td><td>71.0</td></tr><tr><td>ViT-clip</td><td>87.6</td><td>87.8</td><td>50.9</td><td>72.2</td></tr><tr><td>ConvNeXt-clip</td><td>87.8</td><td>86.9</td><td>51.2</td><td>72.2</td></tr></table>"}}}}