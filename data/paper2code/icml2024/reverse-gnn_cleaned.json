{"paper_id": "reverse-gnn", "title": "Mitigating Oversmoothing Through Reverse Process of GNNs for Heterophilic Graphs", "abstract": "Graph Neural Network (GNN) resembles the diffusion process, leading to the over-smoothing of learned representations when stacking many layers. Hence, the reverse process of message passing can produce the distinguishable node representations by inverting the forward message propagation. The distinguishable representations can help us to better classify neighboring nodes with different labels, such as in heterophilic graphs. In this work, we apply the design principle of the reverse process to the three variants of the GNNs. Through the experiments on heterophilic graph data, where adjacent nodes need to have different representations for successful classification, we show that the reverse process significantly improves the prediction performance in many cases. Additional analysis reveals that the reverse mechanism can mitigate the over-smoothing over hundreds of layers. Our code is available at https://github.com/ ml-postech/reverse-gnn.", "pdf_parse": {"paper_id": "reverse-gnn", "abstract": [{"text": "Graph Neural Network (GNN) resembles the diffusion process, leading to the over-smoothing of learned representations when stacking many layers. Hence, the reverse process of message passing can produce the distinguishable node representations by inverting the forward message propagation. The distinguishable representations can help us to better classify neighboring nodes with different labels, such as in heterophilic graphs. In this work, we apply the design principle of the reverse process to the three variants of the GNNs. Through the experiments on heterophilic graph data, where adjacent nodes need to have different representations for successful classification, we show that the reverse process significantly improves the prediction performance in many cases. Additional analysis reveals that the reverse mechanism can mitigate the over-smoothing over hundreds of layers. Our code is available at https://github.com/ ml-postech/reverse-gnn.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Graph neural networks (GNNs) have emerged as an important tool for learning relational data. Earlier attempts aim to learn the node representations from graphs based on a message-passing mechanism. The message-passing neural network framework shows partial success with the homophilic graphs, where the nodes with the same labels are likely to be connected. When the heterophilic graphs, where node labels significantly differ from those of their neighbors, are considered, the models based on the homophilic assumption (<PERSON> et al., 2001) often perform worse than the naive neural network architectures without considering the relationship between nodes (<PERSON> et al., 2020) .", "section": "Introduction", "sec_num": "1."}, {"text": "To learn the node representations of heterophilic graphs, a GNN needs to capture long-range interactions between nodes, leading to the stacking of multiple message-passing layers (<PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2023) . However, many studies empirically and theoretically identify that GNN tends to smooth the node representations over the layers, and eventually, the learned representations are likely to be similar, known as the over-smoothing issue (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2023) . Furthermore, GRAND (<PERSON> et al., 2021) shows that GNN can be seen as a discretization of a heat diffusion equation. The diffusion perspective implies that learning distinguishable node representation with deep GNN is challenging since heat only diffuses to reach equilibrium, where node representation becomes indistinguishable.", "section": "Introduction", "sec_num": "1."}, {"text": "In this work, we claim not to forcefully correct the diffusive nature of the GNNs. Instead, we propose to use the reverse process of the aggregation. The aggregation process is known to make the node representations similar; hence, its reverse process can make the neighborhood representations more distinguishable. Revisiting the diffusion perspective, applying the reverse process means learning the states in the past, which are further away from equilibrium and more distinguishable.", "section": "Introduction", "sec_num": "1."}, {"text": "To illustrate our intuition, we showcase our experimental results on the Minesweeper dataset, a well-known heterophilic dataset (<PERSON><PERSON> et al., 2023b) , in Figure 1 . In Minesweeper, a board is a grid-structured graph where each node is initialized with the number of mines in the adjacent nodes X (0) , and the goal is to classify the location of mine Y correctly. The top row visualizes the learned node representations with our approach, and the bottom row visualizes the representations with the GCN (Kipf & Welling, 2017) . With the forward-only method, such as GCN, the learned representations often fail to obtain distinguishable representations for classification. However, when the reverse process is applied to the initial features, we can obtain a distinguishable representation from the backward process.", "section": "Introduction", "sec_num": "1."}, {"text": "X (8) X (') X (&) X (:) X (;) X (<&) X (<;) X (<'=) X (<=:)", "section": "Introduction", "sec_num": "1."}, {"text": "Forward Process", "section": "Reverse Process", "sec_num": null}, {"text": "No Reverse Process", "section": "Reverse Process", "sec_num": null}, {"text": "Figure 1 . Visualized node representations over the forward and reverse processes in Minesweeper. Top: our approach with the forward and reverse processes. Bottom: a classical GCN with a forward process only. The original node features are smoothed over the forward process, whereas the features are more distinguishable over the reverse process. Visualization details are provided in Section 4.1.3.", "section": "Reverse Process", "sec_num": null}, {"text": "To this end, we propose the framework of reverse process GNNs utilizing the inversion of forward messagepassing layers. Specifically, we provide three variants of reverse process GNN based on three backbone models: 1) GRAND (<PERSON> et al., 2021) , 2) GCN (Kipf & Welling, 2017) , and 3) GAT (<PERSON><PERSON><PERSON> et al., 2018) . For GRAND, we directly use the numerical method to obtain the representations in a backward direction. For GCN and GAT, we adopt the idea of iResNet (<PERSON><PERSON> et al., 2019) to make invertible message-passing layers that can model the reverse process.", "section": "Reverse Process", "sec_num": null}, {"text": "The experimental results on heterophilic datasets show that the reverse process improves the prediction performance compared with the forward-only models. Our investigation reveals that the reverse process produces distinguishable representations and enables the stacking of hundreds, even a thousand layers, mitigating over-smoothing. Successfully stacking deep layers allows for the capture of long-range dependencies, which are crucial for performance on heterophilic datasets. The experiments on homophilic datasets confirm that the reverse process does not harm the prediction performance when the aggregation mechanism is sufficient.", "section": "Reverse Process", "sec_num": null}, {"text": "Most studies on heterophilic data focus on identifying nodes with similar characteristics even among non-adjacent ones for aggregation. GPR-GNN (<PERSON><PERSON> et al., 2020) utilizes a trainable generalized PageRank for feature aggregation, learning important neighborhood ranges from the data and emphasizing information within those ranges for aggregation. CPGNN (<PERSON> et al., 2021) introduces a learnable compatibility matrix to capture the information of non-adjacent homophilic nodes. FSGNN (<PERSON><PERSON><PERSON> et al., 2022) proposes soft feature selection, wherein it adaptively selects neighbors to aggregate with different hop distances. GloGNN (Li et al., 2022) employs a coefficient matrix that represents node-to-node relationships for aggregation, allowing the aggregation of information from all nodes. GBK-GNN (<PERSON> et al., 2022) proposes a bi-kernel graph neural network that separately handles homophilic and heterophilic nodes. It uses a selection gate to predict whether a node is homophilic or heterophilic and obtains features using the corresponding kernel based on the prediction. LRGNN (<PERSON> et al., 2023) uses a low-rank approximation to compute a label relationship matrix, employing it for signed message passing.", "section": "Related Work", "sec_num": "2."}, {"text": "However, aggregation still causes global node representations to become similar, known as over-smoothing, leading to performance degradation on heterophilic datasets. To overcome this issue, H 2 GCN (<PERSON> et al., 2020) and Ordered GNN (<PERSON> et al., 2023) proposes to preserve nonaggregated representation separately. H 2 GCN learns node representation by separating ego-embedding and neighborembedding and employing intermediate representations. Ordered GNN proposes ordering message passing to prevent the mixing of messages from different hops.", "section": "Related Work", "sec_num": "2."}, {"text": "Several studies propose methods to adaptively learn appropriate filters that can handle various graph structures. FAGCN (<PERSON> et al., 2021) introduces a GNN framework with a self-gating mechanism to adaptively use low-frequency and high-frequency signals. JacobiConv (Wang & Zhang, 2022) uses Jacobi bases for spectral filter, whose orthogonality and flexibility enable adaptation to a wide range of graph signal densities. ACM-GCN (<PERSON><PERSON> et al., 2022) utilizes a filterbank which combines low-pass and high-pass filters, and adaptively considers node-wise local information.", "section": "Related Work", "sec_num": "2."}, {"text": "On the other hand, several studies tackle the over-smoothing issue. GRAND (<PERSON> et al., 2021) enhances the understanding of over-smoothing from the perspective of the resemblance between GNN structures and the heat diffusion equation. PairNorm (Zhao & A<PERSON>glu, 2020) proposes a normalization layer that remains the total pairwise feature distances constant. Drop<PERSON>dge (<PERSON><PERSON> et al., 2020) randomly removes edges from the graph to cut off messages passing between adjacent nodes. Half-Hop (<PERSON><PERSON><PERSON><PERSON> et al., 2023) up-sample edges and slow down the message passing. While delaying or limiting over-smoothing can make node representations less smooth, learning difference-enhanced representations between adjacent nodes, which is helpful for heterophilic data, is still challenging.", "section": "Related Work", "sec_num": "2."}, {"text": "We aim to introduce a framework that can learn distinct node representations between adjacent nodes through reverse diffusion. We first provide the overall framework of our approach and then show three substantiations of the framework for three baseline models.", "section": "Method", "sec_num": "3."}, {"text": "We consider a graph G = (V, E), where V and E are a set of nodes and edges respectively, with additional d-dimensional node features represented as X ∈ R |V|×d for all node, and x i ∈ R d denotes the feature of node i. It is a well-known fact that a typical GNN layer f tends to learn similar representations between neighboring nodes, leading to the issue of over-smoothing. <PERSON> et al. (2021) highlighted that this is due to the diffusive property inherent in GNN structures.", "section": "Framework", "sec_num": "3.1."}, {"text": "In contrast to a typical GNN layer, we propose a GNN layer g that performs the opposite role by reversing diffusion and re-concentrating diffused information. Our main idea is to design an inverse function of a message-passing GNN layer f , with g = f -1 to perform the reversion. Due to the diffusive nature of the GNN layer f , its inverse form g is expected to have two properties: 1) g cancels the smoothing effect, producing distinguishable representations that mitigate the over-smoothing issue and thus 2) leading to stacking multiple layers of g.", "section": "Framework", "sec_num": "3.1."}, {"text": "Formally, with the inverse of multiple message passing layers, our framework predicts node label Y as follows:", "section": "Framework", "sec_num": "3.1."}, {"text": "Ŷ = ϕ(f (L) • • • • • f (1) (X (0) )∥g (1) • • • • • g (L) (X (0) )),", "section": "Framework", "sec_num": "3.1."}, {"text": "where X (0) is input node features, f (ℓ) is the ℓ-th forward message-passing layer with g (ℓ) = f (ℓ) -1 , L is the number of layers, ∥ denotes concatenation, and ϕ is a prediction function based on the forward and reverse processes of the input features. We concatenate the representations from both directions for prediction to utilize advantage of difference enhanced representation and smooth representaiton at the same time. In practice, we can set the number of forward and reverse layers differently as L F and L R , and share the parameters of different layers.", "section": "Framework", "sec_num": "3.1."}, {"text": "In the following sections, we propose a range of methods to develop a reverse diffusion function for GRAND and two variants of GNN with residual connections.", "section": "Framework", "sec_num": "3.1."}, {"text": "In this section, we suggest a reverse diffusion function based on GRAND. In GRAND, a GNN is interpreted as a discretization of the heat diffusion process. This is modeled by the following heat diffusion equation on the graph:", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "where A(X) ∈ R |V|×|V| represents the learnable attention matrix. Here, [A(X)] ij = 0 for any (i, j) / ∈ E.", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "Within the framework of diffusion, the time parameter serves as a continuous layer, similar to the concept used in NeuralODEs (<PERSON> et al., 2018) . Using Equation (1), GRAND produce node representations at time T F > 0 by:", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "where numerical techniques like the <PERSON><PERSON><PERSON> method are used to solve integration. Since Equation (1) models the property of heat reaching equilibrium over time, the node representations obtained through Equation ( 2) become diffused as time progresses. Conversely, tracing back in time allows us to observe the concentrated form of heat before diffusion.", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "Utilizing Equation ( 1), node representations at a past time T R < 0 can be calculated as follows:", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "Equation ( 3) reverses the diffusion process, enabling us to obtain distinguishable representations.", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "In experiments, we utilize the GRAND-l model, where the learnable attention matrix remains constant throughout the diffusion process, A(X(t)) = A, which is known to be parameter-efficient and robust to overfitting. Following the original work, we use the scaled dot product attention to calculate the learnable attention matrix A(X), which is given as follows:", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "where W K and W Q are d ′ × d learnable parameters. When multi-head attention is employed, we use the average of attentions, i.e., A(X)", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "= 1 K k A (k) (X)", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": ", where A (k) is the attention with k-th head.", "section": "Reverse Diffusion Based on GRAND", "sec_num": "3.2."}, {"text": "We have explored the design of reverse process in widely used two message-passing GNN structures: graph convolutional network (GCN) (Kipf & Welling, 2017) and graph attention network (GAT) (<PERSON><PERSON><PERSON> et al., 2018) . Both GCN and GAT model a node representation through an aggregation step, where neighborhood representations are combined, and an update step, where the aggregated representations are merged into the target node representation. Let Â ∈ R |V|×|V| encode neighborhood structure in a graph, and W ∈ R d×d be a matrix of learnable parameters. With the application of skip-connections (<PERSON> et al., 2016) , the GCN and GAT layers can be formalized as", "section": "Reverse Process Based on GNN with Residual Connections", "sec_num": "3.3."}, {"text": "EQUATION", "section": "Reverse Process Based on GNN with Residual Connections", "sec_num": "3.3."}, {"text": "where σ(•) represents a non-linear activation function. Â is the renormalized adjacency matrix in GCN and a learnable attention matrix in GAT. We note that when using multihead attention, we take averaging approach as in GRAND to keep invertibility.", "section": "Reverse Process Based on GNN with Residual Connections", "sec_num": "3.3."}, {"text": "According to <PERSON><PERSON><PERSON> et al. (2019) , the inverse of the GNN layer g = f -1 exists if the Lip(h) < 1, where Lip(h) is Lipschitz constant of h. With the contractive nonlinear activations like ReLU, ELU, and tanh, Lip(h", "section": "Reverse Process Based on GNN with Residual Connections", "sec_num": "3.3."}, {"text": "EQUATION", "section": "Reverse Process Based on GNN with Residual Connections", "sec_num": "3.3."}, {"text": "where ∥•∥ F denotes Frobenius norm. When the condition is guaranteed, g(X (ℓ) ) can be computed via fixed point iteration as described in Algorithm 1, resulting in X (ℓ-1) .", "section": "Reverse Process Based on GNN with Residual Connections", "sec_num": "3.3."}, {"text": "To ensure the invertibility of f throughout the entire training procedure, we enforce the weight matrix W to satisfy the condition. Since it is difficult to optimize the weight matrix while satisfying the condition, we normalize the weight matrix after each gradient descent step. Specifically, given that the left side of Equation ( 7) is upper bounded by", "section": "Reverse Process Based on GNN with Residual Connections", "sec_num": "3.3."}, {"text": "EQUATION", "section": "Reverse Process Based on GNN with Residual Connections", "sec_num": "3.3."}, {"text": "where ∥•∥ 2 denotes spectral norm, we use the upper bound to normalize the weight matrix.", "section": "Reverse Process Based on GNN with Residual Connections", "sec_num": "3.3."}, {"text": "Note that the spectral norm of Â is straightforward for the two models that we considered as baselines: GCN and GAT.", "section": "Reverse Process Based on GNN with Residual Connections", "sec_num": "3.3."}, {"text": "In GCN, Â = D-1 2 Ã D-1 2 where Ã = A + I is the adjacency matrix A with added self-loops and D is the diagonal degree matrix of Ã. A spectral norm of renormalized adjacency matrix ∥ Â∥ 2 = 1. In GAT, ∥ Â∥ 2 = 1 since Â is right-stochastic. Therefore, normalizing the weight matrix through its Frobenius norm is sufficient to guarantee the condition in Equation ( 7). The upper bound normalization reduces the time complexity at the expense of the exact supremum calculation. In experiments, we find that the Frobenius upper bound can still result in a competitive performance.", "section": "Reverse Process Based on GNN with Residual Connections", "sec_num": "3.3."}, {"text": "Input: output of residual layer X (ℓ) , residual block h, the number of fixed-point iterations M Output: input of residual layer", "section": "Algorithm 1 Inverse of GNN via fixed-point iteration", "sec_num": null}, {"text": "X (ℓ-1) X ← X (ℓ) for m = 1, . . . , M do X ← X -h(X) end for Return X When the scaling coefficient c < 1 is given, W is normal- ized to cW ∥W∥ F if c < ∥W∥ F , in order to satisfy Lip(h) < c. When multi-head attention with K heads is employed for GAT, parameters of k-th head W (k) is normalized to cW (k) 1 K K k=1 ∥W (k) ∥ F", "section": "Algorithm 1 Inverse of GNN via fixed-point iteration", "sec_num": null}, {"text": "for all k, since the upper bound result in", "section": "Algorithm 1 Inverse of GNN via fixed-point iteration", "sec_num": null}, {"text": "1 K K k=1 ∥W (k) ∥ F .", "section": "Algorithm 1 Inverse of GNN via fixed-point iteration", "sec_num": null}, {"text": "The derivation of the upper bound for multi-head attention is provided in Appendix C. Since residual block h is an operator on a Banach space, and we constraint the Lip(h) < 1, the convergence of Algorithm 1 is guaranteed by the Banach fixed point theorem (<PERSON><PERSON><PERSON> et al., 2019) . Inversion error in practice are reported in Section 4.2.", "section": "Algorithm 1 Inverse of GNN via fixed-point iteration", "sec_num": null}, {"text": "While the time complexity of a GCN mainly depends on the number of the forward layers L F , the complexity of the reverse process depends on the number of fixed point iterations M and of the number of reverse layers L R . In our implementation, we run the fixed point iteration until convergence and backpropagate over the iterations. We provide the time and memory complexity analysis in Table 1 , and the proof is provided in Appendix A. An analysis on M and the run-time with varying L R in real experiments are provided in Section 4.3.", "section": "Algorithm 1 Inverse of GNN via fixed-point iteration", "sec_num": null}, {"text": "The experimental section focuses on validating two research questions: 1) Can the reverse process produce distinguishable representations? 2) Does the reverse process alleviate over-smoothing problems, enabling the construction of deeper layers? Throughout this section, we denote models with additional reverse layers by ReP (Reverse Process). For example, GCN+ReP indicates the GCN backbone with the reverse process. We adopt weight sharing approach of GRAND-l for all experiments using ReP.", "section": "Experiments", "sec_num": "4."}, {"text": "In this section, we validate the effectiveness of our framework on node classification. Our primary focus is on assessing performance improvements in heterophilic datasets, while we have also evaluated performance on homophilic", "section": "Node Classification", "sec_num": "4.1."}, {"text": "GCN with Residual Connection + Reverse Process Forward Time O(L F |E|d + L F |V|d 2 ) + O(M 2 L R |E|d + M L R |V|d 2 + M 2 L R d 3 ) Forward Memory O(|E| + L F |V|d + d 2 ) + O(L R |V|d) Backward Time O(L 2 F |E|d + L F |V|d 2 + L 2 F d 3 ) + O(M 2 L 3 R |E|d + M L 2 R |V|d 2 + M 2 L 3 R d 3 ) Backward Memory O(|E| + |V|d + d 2 ) ✗ Table 1.", "section": "Node Classification", "sec_num": "4.1."}, {"text": "Space and time complexity of GCN for the forward and reverse processes. We show an additional complexity when using reverse process, for simplicity. ✗ denotes there are no additional complexity. LF and LR represent the number of forward and reverse layers, respectively, M is the number of fixed point iterations, and d is the dimensionality of the node representation.", "section": "Node Classification", "sec_num": "4.1."}, {"text": "datasets.", "section": "Node Classification", "sec_num": "4.1."}, {"text": "For the node classification task, we utilize a diverse set of datasets to assess our model. For heterophilic data, we explore two Wikipedia graphs, Chameleon and Squirrel, and five additional datasets, Roman-Empire, Amazon-Ratings, Minesweeper, Tolokers, and Questions, introduced by <PERSON><PERSON> et al. (2023b) . We adopted the filtering process for Chameleon and Squirrel to prevent train-test data leakage as recommended by <PERSON><PERSON> et al. (2023b) . In the case of homophilic data, our selection includes three citation graphs: Cora, CiteSeer, and PubMed, along with two Amazon co-purchase graphs, Computers and Photo. The statistics of the datasets are summarized in Appendix B.", "section": "DATASETS", "sec_num": "4.1.1."}, {"text": "For the heterophilic datasets, we adopt the experimental setup from <PERSON><PERSON> et al. (2023b) , which provides ten random train/validation/test splits. We train a model with cross-entropy loss and report mean accuracy and standard deviation for multi-class classification datasets, including Chameleon, Squirrel, Roman-Empire, and Amazon-Ratings.", "section": "EXPERIMENTAL SETUP AND BASELINES", "sec_num": "4.1.2."}, {"text": "For binary classification datasets, including Minesweeper, Tolokers, and Questions, binary cross-entropy loss is used, and mean ROC-AUC and standard deviation are reported.", "section": "EXPERIMENTAL SETUP AND BASELINES", "sec_num": "4.1.2."}, {"text": "We benchmark several neural architectures as baselines, including classic GNN models like GCN (Kipf & Welling, 2017) , GraphSAGE (Hamilton et al., 2017) , GAT (<PERSON><PERSON><PERSON> et al., 2018) , and Graph Transformer (GT) (<PERSON> et al., 2020) for more complex attention mechanisms. These baselines are augmented with skip connections and layer normalization. In addition, modifications proposed in <PERSON> et al. (2020) are made to GAT and GT, resulting in GAT-sep and GT-sep models. For heterophily-specific models, we use 10 models including H 2 GCN (<PERSON> et al., 2020) , CPGNN (<PERSON> et al., 2021) , GPR-GNN (<PERSON><PERSON> et al., 2020) , FSGNN (<PERSON><PERSON><PERSON> et al., 2022) , GloGNN (<PERSON> et al., 2022) , FAGCN (<PERSON> et al., 2021) , GBK-GNN (<PERSON> et al., 2022) , JacobiConv (Wang & Zhang, 2022) , LRGNN (<PERSON> et al., 2023) , Ordered GNN (<PERSON> et al., 2023) , ACM-GCN (<PERSON><PERSON> et al., 2022) , and Dir<PERSON>G<PERSON><PERSON> (<PERSON> et al., 2023) .", "section": "EXPERIMENTAL SETUP AND BASELINES", "sec_num": "4.1.2."}, {"text": "For the homophilic datasets, we adopt the experimental setup from <PERSON> et al. (2021) , splitting datasets into 60%/20%/20% train/validation/test sets and using ten random splits for averaging results. We compare our framework against seven baselines: MLP, GCN (Kipf & Welling, 2017) , GAT (<PERSON><PERSON><PERSON> et al., 2018) , APPNP (<PERSON><PERSON><PERSON> et al., 2018) , ChebNet (<PERSON><PERSON><PERSON><PERSON> et al., 2016) , GPR-GNN (<PERSON><PERSON> et al., 2020) , and BernNet (<PERSON> et al., 2021) .", "section": "EXPERIMENTAL SETUP AND BASELINES", "sec_num": "4.1.2."}, {"text": "Validation For all experiments, we set the number of epochs to 1,000 and apply early stopping when there is no performance improvement for 100 consecutive epochs. For GRAND+ReP, we validate the hyperparameters that maximize the validation metric in the following ranges: learning rate ∈ [10 -5 , 10 Table 2 . Test performance and standard deviation on heterophilic datasets. ∆ indicates the difference with and without ReP. We also report the number of forward and reverse layers below the performance of GCN+ReP and GAT+ReP. The best and the second-best are bolded and underlined, respectively.", "section": "EXPERIMENTAL SETUP AND BASELINES", "sec_num": "4.1.2."}, {"text": "-1 ], T F , T R ∈ [0, 10], d ∈ {16,", "section": "EXPERIMENTAL SETUP AND BASELINES", "sec_num": "4.1.2."}, {"text": "Table 3 shows node classification performance on the homophilic datasets. Due to spacing, we only report the results on three datasets. All results are reported in Appendix D. No significant performance changes were observed when ReP applied on homophilic node classification. The results confirm that the distinguishable representations do not harm the prediction performance for homophily datasets where the forward aggregation is sufficient.", "section": "EXPERIMENTAL SETUP AND BASELINES", "sec_num": "4.1.2."}, {"text": "To investigate whether many layers of reverse process improve performance in heterophilic datasets and compare its effect with that of many forward layers, we trained GCN+ReP with varying pairs of steps on two datasets: Chameleon and Minesweeper. Specifically, we vary the number of layers from 1 to 1024 in one direction.", "section": "Analysis on the Number of Forward and Reverse Layers", "sec_num": null}, {"text": "The prediction performances with varying numbers of layers are reported in Figure 2 . In both datasets, the prediction performance keeps increasing as the number of reverse layers increases. These results indicate that the reverse process is capable of deep stacking to mitigate over-smoothing. This enables the models to capture long-range dependencies effectively, which is known to be important, especially in heterophilic graphs. The prediction performance also tends to increase as we increase the number of forward steps up to 1024 in the Chameleon dataset.", "section": "Analysis on the Number of Forward and Reverse Layers", "sec_num": null}, {"text": "Over-Smoothing Analysis We evaluate whether the proposed reverse process mitigates the over-smoothing issue.", "section": "Analysis on the Number of Forward and Reverse Layers", "sec_num": null}, {"text": "To measure the degree of over-smoothing, we adopt Graph Smoothness Level (GSL) proposed by <PERSON> et al. (2022) defined as: where x i is the representation of node i. The GSL represents the average cosine similarity across all pairs of nodes in the graph. A GSL value closer to one indicates more severe over-smoothing.", "section": "Analysis on the Number of Forward and Reverse Layers", "sec_num": null}, {"text": "EQUATION", "section": "Analysis on the Number of Forward and Reverse Layers", "sec_num": null}, {"text": "Figure 3 shows GSL of GCN+ReP and GCN with varying numbers of layers on Squirrel and Chameleon datasets. In both datasets, the GSL of GCN+ReP remains below 0.6 up to 1024 reverse layers, whereas the learned representations from GCN with 32 and 64 layers tend to become similar even after eight layers. GCN with 16 layers shows relatively low GSL values yet still exceeds 0.9 after eight layers. Compared with CGN, GCN+ReP shows relatively less GSL, showing that the reverse process can mitigate the over-smoothing in the forward processes as well.", "section": "Analysis on the Number of Forward and Reverse Layers", "sec_num": null}, {"text": "To validate that the reverse process produces a distinguishable representation, we visualize label predictions on the Minesweeper dataset. The Minesweeper dataset is a binary classification task on a grid-structured graph, where the node with a positive label indicates the location of a mine. Each node, unless located on the boundaries, is connected to eight adjacent nodes, including the ones in the diagonal directions. The node feature is initialized with the number of mines in the adjacent nodes, and a one-hot representation of the feature is used as an initial representation of the node for learning.", "section": "Qualitative Analysis on Minesweeper Dataset", "sec_num": null}, {"text": "Based on the representations obtained from the forward and reverse processes, we trained a GCN+ReP model with a single-layer MLP as a prediction head. In Figure 4 , we visualize the prediction results of two randomly sampled 7 × 7 sub-grids from 100 × 100 grid structure. For each example, we visualize the prediction results with node representations from 1) forward process, 2) reverse process, and 3) both directions, as well as 4) the true labels, displayed from upper left to lower right. In the visualization of the true labels, black cells indicate the presence of mine, and white cells indicate its absence. In the visualization of the prediction results, the darker the cell, the higher the predicted probability of a mine being present. Since the prediction head needs concatenated representations for prediction, to visualize the prediction results focused on a forward or reverse representation, we set the other node representation to be zero, e.g., to predict the mine using node representation at layer ℓ > 0, (X (ℓ) ∥0) is fed into the prediction head. We visualize the prediction from 1) forward, 2) reverse, and 3) both representations, along with 4) the ground truth labels.", "section": "Qualitative Analysis on Minesweeper Dataset", "sec_num": null}, {"text": "In both examples, the prediction results from the reverse process appear distinguishable, while those from the forward process tend to be smooth. Additionally, the distinguishable prediction created by the reverse process significantly contributes to label prediction. Although in some cases, the reverse process can perfectly classify the location of mines, e.g., Example 1, the other cases require representations from both directions to classify the mines correctly, e.g., Example 2.", "section": "Qualitative Analysis on Minesweeper Dataset", "sec_num": null}, {"text": "Figure 1 shows the changes in predictions over the number of layers on the 10 × 10 sampled sub-grids. We follow the same visualization procedure with Figure 4 . As expected, the predictions and representations tend to be more distinguishable as the number of reverse layers increases.", "section": "Qualitative Analysis on Minesweeper Dataset", "sec_num": null}, {"text": "Although the invertibility of Algorithm 1 is guaranteed in theory, the inversion may not be achieved due to numerical errors. To verify the fixed point method, we conduct the experiment to restore the inputs from the outputs of GCN and GAT at a depth of 64. We set the scaling coefficient c to 0.99999 and the number of fixed point iterations to eight, which is challenging due to the large coefficient (note that the Lipschitz of 1 is non-invertible) and the small number of iterations. Table 4 shows the mean absolute error between the original inputs and restored input data. As the results show, the inversion error is negligible in practice.", "section": "Inversion Error", "sec_num": "4.2."}, {"text": "We first measure how many iterations are required for the fixed point iterations to be converged. Figure 5 shows the difference between consecutive representations over the fixed point iterations with two datasets in terms of mean absolute difference. As shown in the figure, the fixed point method converges after seven iterations in general. In addition, we GCN+ReP GAT+ReP Squirrel 3.36 × 10 -5 3.28 × 10 -5", "section": "Run Time Analysis", "sec_num": "4.3."}, {"text": "Chameleon 2.23 × 10 -5 2.41 × 10 -5", "section": "Run Time Analysis", "sec_num": "4.3."}, {"text": "Roman-empire 2.79 × 10 -5 3.56 × 10 -5", "section": "Run Time Analysis", "sec_num": "4.3."}, {"text": "Amazon-ratings 4.31 × 10 -5 3.46 × 10 -5", "section": "Run Time Analysis", "sec_num": "4.3."}, {"text": "Table 4 . Mean absolute error between the original inputs and restored input data by Algorithm 1. ", "section": "Run Time Analysis", "sec_num": "4.3."}, {"text": "In this work, we propose a reverse process for the messagepassing-based graph neural networks. Through extensive empirical analysis, we have found that the reverse process can mitigate over-smoothing issues and allow long-distance nodes to interact with each other. Especially for the heterophilic datasets where the long-range interaction is necessary for a better prediction, the proposed method achieves outstanding results against many baseline models.", "section": "Conclusion", "sec_num": "5."}, {"text": "Future Work To ensure invertibility, the Lipschitz constant of the forward process must be restricted, and the hidden dimension of weight parameters must remain constant. These restrictions limit the representation power and design choices. Investigating less restrictive invertible forms could lead to performance improvements.", "section": "Conclusion", "sec_num": "5."}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none of which we feel must be specifically highlighted here.", "section": "Impact Statement", "sec_num": null}, {"text": "The forward and reverse processes of a GCN with residual connections and weight sharing are as follows:", "section": "A. Complexity Analysis", "sec_num": null}, {"text": "X (ℓ+1) = X (ℓ) + ÂX (ℓ) W, (forward process), ( 10)", "section": "A. Complexity Analysis", "sec_num": null}, {"text": "EQUATION", "section": "A. Complexity Analysis", "sec_num": null}, {"text": ")", "section": "A. Complexity Analysis", "sec_num": null}, {"text": "where M is the number of fixed point iterations. For simplicity, we ignore an activation function.", "section": "A. Complexity Analysis", "sec_num": null}, {"text": "A ", "section": "A. Complexity Analysis", "sec_num": null}, {"text": "The time complexity of the backward pass is primarily determined by the computation cost of ∂L ∂W . By applying the chain rule, the gradient can be expressed as:", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "EQUATION", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "By sequentially multiplying ∂X (n) ∂X (n-1) for n = L F , • • • , 2 on the right side of ∂L ∂X (L F ) , we derive:", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "EQUATION", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": ")", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "To demonstrate Equation ( 13), we illustrate part of the sequential multiplication process. Multiplying ∂X (L F ) ∂X (L F -1) to the right side of ∂L ∂X (L F ) , we get:", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "EQUATION", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": ")", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "Next, we multiply ∂X (L F -1) ∂X (L F -2) on the right side of Equation ( 14), yielding:", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "EQUATION", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": ")", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "Repeating the process above, we can derive Equation (13).", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "Finally, ∂L ∂W is derived by multiplying Equation (13) with ∂X (1) ∂W :", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "EQUATION", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": ")", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "The time complexity of the term in the summation is O(L F + ℓ|E|d + |V|d 2 + ℓd 3 ). Therefore, the overall time complexity for computing ", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "∂L ∂W is O(L 2 F d 3 + L F |V|d 2 + L 2 F |E|d),", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "L R |E|d + M L R |V|d 2 + M 2 L R d 3", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": ") comparing to forward pass without reverse process.", "section": "A.2. Backward Pass of Forward Process", "sec_num": null}, {"text": "The time complexity of the backward pass is primarily determined by the computation cost of ∂L ∂W . By applying the chain rule, the gradient can be expressed as:", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "EQUATION", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "By sequentially multiplying ∂X (-n) ∂X (-n+1) for n = L R , • • • , 2 to the right side of ∂L ∂X (-L R ) , we derive:", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "EQUATION", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "where m i = 1, • • • , M for all i. To demonstrate Equation ( 18), we show part of the sequential multiplication process.", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "EQUATION", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "which can also be obtained by substitute L R = 2 in Equation ( 18).", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "Next, we multiply ∂X (-L R +1) ∂X (-L R +2) on the right side of Equation ( 19), yielding:", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "EQUATION", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "Repeating the process above, we can derive Equation (18).", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "Finally, ∂L ∂W is derived by multiplying Equation ( 18) with ∂X (-1) ∂W :", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "EQUATION", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "where", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "m i = 1, • • • , M for all i. The time complexity of the first term is O(M 2 |E|d + M |V|d 2 + M 2 d 3 ). In the case of the second term, the time complexity is O(M 2 L 3 R |E|d + M L 2 R |V|d 2 + M 2 L 3 R d 3 )", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": ", since there are up to 2ℓM possible outcomes for ℓ i=1 m i . Therefore, the overall time complexity of the reverse process is O( <PERSON><PERSON> et al., 2019; <PERSON> et al., 2020) and adjusted homophily (<PERSON><PERSON> et al., 2023a) . Edge homophily denotes the proportion of edges connecting nodes with the same label, formally expressed as:", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "(L 2 F + M 2 L 3 R )|E|d + (L 2 F + M L 2 R )|V|d 2 + M 2 L 3 R d 3 ). The memory complexity is O(|V|d + |E| + d 2 ).", "section": "A.4. Backward Pass of Reverse Process", "sec_num": null}, {"text": "h edge = | (u, v) ∈ E : y u = y v } | |E| ,", "section": "B. Dataset Statistics", "sec_num": null}, {"text": "where E is the set of edges, and y n is the label of node n. However, edge homophily is acknowledged to be meaningless in graphs with imbalanced labels. To address this issue, adjusted homophily is introduced. Formally, adjusted homophily is defined as:", "section": "B. Dataset Statistics", "sec_num": null}, {"text": "h adj = h edge - C k=1 D 2 k /(2|E|) 2 1 - C k=1 D 2 k /(2|E|) 2", "section": "B. Dataset Statistics", "sec_num": null}, {"text": ", where D k is the total degree of nodes of class k, and C is the number of classes. We employed seven heterophilic datasets characterized by low adjusted homophily and five commonly used homophilic datasets exhibiting high edge homophily and adjusted homophily.", "section": "B. Dataset Statistics", "sec_num": null}, {"text": "GAT calculates an attention matrix as follows:", "section": "C. Multi-Head Attention for GAT", "sec_num": null}, {"text": "EQUATION", "section": "C. Multi-Head Attention for GAT", "sec_num": null}, {"text": ")", "section": "C. Multi-Head Attention for GAT", "sec_num": null}, {"text": "where a ∈ R 2d is a learnable parameter. Our framework also adopts averaging when using multi-head attention and remains hidden dimension constant to ensure invertibility, resulting in: In this case, the Lipschitz constant of h, Lip(h) < 1 is satisfied if", "section": "C. Multi-Head Attention for GAT", "sec_num": null}, {"text": "h(X (ℓ) ) = σ( 1 K K k=1 Â(k) X (ℓ) W (k)", "section": "C. Multi-Head Attention for GAT", "sec_num": null}, {"text": "EQUATION", "section": "C. Multi-Head Attention for GAT", "sec_num": null}, {"text": ")", "section": "C. Multi-Head Attention for GAT", "sec_num": null}, {"text": "The upper bound of left side is computed by:", "section": "C. Multi-Head Attention for GAT", "sec_num": null}, {"text": "EQUATION", "section": "C. Multi-Head Attention for GAT", "sec_num": null}, {"text": "since ∥ X∥ F ≤ ∥X∥ F .", "section": "C. Multi-Head Attention for GAT", "sec_num": null}, {"text": "We provide the experimental results of all homophilic datasets in Table 6 . ", "section": "<PERSON><PERSON> Full Results of Homophily Datasets", "sec_num": null}, {"text": "Graduate School of Artificial Intelligence, Pohang University of Science and Technology (POSTECH), Pohang, Republic of", "section": "", "sec_num": null}, {"text": "Korea 2 Computer Science and Engineering, Pohang University of Science and Technology (POSTECH), Pohang, Republic of Korea. Correspondence to: <PERSON><PERSON><PERSON> <<EMAIL>>. Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by Institute of Information & communications Technology Planning & Evaluation (IITP) grant funded by the Korea government(MSIT) (RS-2019-II191906, Artificial Intelligence Graduate School Program(POSTECH)) and supported by the National Research Foundation of Korea(NRF) grant funded by the Korea government(MSIT)(RS-2024-00337955 and RS-2023-00217286).", "section": "Acknowledgements", "sec_num": null}], "ref_entries": {"FIGREF0": {"num": null, "uris": null, "fig_num": "2", "text": "Figure2. Prediction performance with varying the number of forward and reverse layers. We vary the number of layers (depth) in one direction. Due to memory constraints, we restricted the reverse depth used in Minesweeper to 256 or less.", "type_str": "figure"}, "FIGREF1": {"num": null, "uris": null, "fig_num": "3", "text": "Figure3. Over-smoothing levels measured by GSL over the number of layers (depth). ReP (forward) denotes the measured GSL in the forward process of GCN+ReP. We compare the results with GCN of three different depths: 16, 32, 64.", "type_str": "figure"}, "FIGREF2": {"num": null, "uris": null, "fig_num": "4", "text": "Figure 4. Visualization of node prediction on the Minesweeper dataset.We visualize the prediction from 1) forward, 2) reverse, and 3) both representations, along with 4) the ground truth labels.", "type_str": "figure"}, "FIGREF3": {"num": null, "uris": null, "fig_num": "5", "text": "Figure 5. The mean absolute difference between two consecutive representations in the fixed-point method.", "type_str": "figure"}, "FIGREF4": {"num": null, "uris": null, "fig_num": "6", "text": "Figure 6. Average training time for the single epoch. The number of forward layers are shown next to the model. measure the training time for a single epoch and plot the results in Figure 6. The results show that the training time increases linearly as we increase the number of reverse layers coincided with the complexity analysis in Section 3.3.", "type_str": "figure"}, "TABREF0": {"content": "<table><tr><td/><td>Squirrel</td><td>Chameleon</td><td>Roman-empire</td><td colspan=\"2\">Amazon-Minesweeper ratings</td><td>Tolokers</td><td>Questions</td></tr><tr><td>SAGE*</td><td>36.09±1.99</td><td>37.77±4.14</td><td>85.74±0.67</td><td>53.63±0.39</td><td>93.51±0.57</td><td colspan=\"2\">82.43±0.44 76.44±0.62</td></tr><tr><td>GAT-sep*</td><td>35.46±3.10</td><td>39.26±2.50</td><td>88.75±0.41</td><td>52.70±0.62</td><td>93.91±0.35</td><td colspan=\"2\">83.78±0.43 76.79±0.71</td></tr><tr><td>GT*</td><td>36.30±1.98</td><td>38.87±3.66</td><td>86.51±0.73</td><td>51.17±0.66</td><td>91.85±0.76</td><td colspan=\"2\">83.23±0.64 77.95±0.68</td></tr><tr><td>GT-sep*</td><td>36.66±1.63</td><td>40.31±3.01</td><td>87.32±0.39</td><td>52.18±0.80</td><td>92.29±0.47</td><td colspan=\"2\">82.52±0.92 78.05±0.93</td></tr><tr><td>H 2 GCN *</td><td>35.10±1.15</td><td>26.75±3.64</td><td>60.11±0.52</td><td>36.47±0.23</td><td>89.71±0.31</td><td colspan=\"2\">73.35±1.01 63.59±1.46</td></tr><tr><td>CPGNN*</td><td>30.04±2.03</td><td>33.00±3.15</td><td>63.96±0.62</td><td>39.79±0.77</td><td>52.03±5.46</td><td colspan=\"2\">73.36±1.01 65.96±1.95</td></tr><tr><td>GPR-GNN*</td><td>38.95±1.99</td><td>39.93±3.30</td><td>64.85±0.27</td><td>44.88±0.34</td><td>86.24±0.61</td><td colspan=\"2\">72.94±0.97 55.48±0.91</td></tr><tr><td>FSGNN*</td><td>35.92±1.32</td><td>40.61±2.97</td><td>79.92±0.56</td><td>52.74±0.83</td><td>90.08±0.70</td><td colspan=\"2\">82.76±0.61 78.86±0.92</td></tr><tr><td>GloGNN*</td><td>35.11±1.24</td><td>25.90±3.58</td><td>59.63±0.69</td><td>36.89±0.14</td><td>51.08±1.23</td><td colspan=\"2\">73.39±1.17 65.74±1.19</td></tr><tr><td>FAGCN*</td><td>41.08±2.27</td><td>41.90±2.72</td><td>65.22±0.56</td><td>44.12±0.30</td><td>88.17±0.73</td><td colspan=\"2\">77.75±1.05 77.24±1.26</td></tr><tr><td>GBK-GNN*</td><td>35.51±1.65</td><td>39.61±2.60</td><td>74.57±0.47</td><td>45.98±0.71</td><td>90.85±0.58</td><td colspan=\"2\">81.01±0.67 74.47±0.86</td></tr><tr><td>JacobiConv*</td><td>29.71±1.66</td><td>39.00±4.20</td><td>71.14±0.42</td><td>43.55±0.48</td><td>89.66±0.40</td><td colspan=\"2\">68.66±0.65 73.88±1.16</td></tr><tr><td>LRGNN</td><td>39.51±2.12</td><td>41.24±2.95</td><td>40.88±1.84</td><td>42.23±4.85</td><td>52.66±6.40</td><td colspan=\"2\">74.24±1.37 66.41±1.75</td></tr><tr><td colspan=\"2\">Ordered GNN 38.96±2.19</td><td>38.04±5.55</td><td>80.12±1.22</td><td>49.66±1.01</td><td>90.21±1.15</td><td colspan=\"2\">81.42±0.65 73.36±1.09</td></tr><tr><td>ACM-GCN</td><td>33.07±3.03</td><td>31.78±3.35</td><td colspan=\"2\">69.66±0.62  † 32.26±2.06</td><td>90.53±0.56</td><td colspan=\"2\">79.18±0.77 62.50±4.05</td></tr><tr><td>Dir-GNN</td><td>40.39±1.11</td><td>41.26±2.00</td><td colspan=\"2\">91.23±0.32  † 44.88±0.84</td><td>91.35±0.65</td><td colspan=\"2\">81.78±0.83 76.30±0.99</td></tr><tr><td>GRAND</td><td>35.94±1.64</td><td>37.71±4.48</td><td>75.19±0.56</td><td>49.34±0.72</td><td>90.41±0.78</td><td colspan=\"2\">78.38±1.91 76.22±1.06</td></tr><tr><td colspan=\"2\">GRAND+ReP 40.75±2.44</td><td>42.14±3.62</td><td>77.53±0.62</td><td>48.30±0.60</td><td>91.42±0.78</td><td colspan=\"2\">80.44±1.64 76.41±1.04</td></tr><tr><td>∆</td><td>+4.81 (↑)</td><td>+4.43 (↑)</td><td colspan=\"2\">+2.34 (↑) -1.04 (↓)</td><td>+1.01 (↑)</td><td colspan=\"2\">+2.06 (↑) +0.19 (↑)</td></tr><tr><td>GAT*</td><td>35.62±2.06</td><td>39.21±3.08</td><td>80.87±0.30</td><td>49.09±0.63</td><td>92.01±0.68</td><td colspan=\"2\">83.70±0.47 77.43±1.20</td></tr><tr><td>GAT+ReP</td><td>39.66±2.00</td><td>43.24±4.48</td><td>85.87±0.64</td><td>52.68±0.27</td><td>94.89±0.33</td><td colspan=\"2\">84.52±0.56 76.21±0.74</td></tr><tr><td/><td>(32/32)</td><td>(64/32)</td><td>(64/4)</td><td>(16/2)</td><td>(32/16)</td><td>(8/1)</td><td>(64/1)</td></tr><tr><td>∆</td><td>+4.04 (↑)</td><td>+4.03 (↑)</td><td colspan=\"2\">+5.00 (↑) +3.59 (↑)</td><td>+2.11 (↑)</td><td colspan=\"2\">+0.82 (↑) -1.22 (↓)</td></tr><tr><td>GCN*</td><td>39.47±1.47</td><td>40.89±4.12</td><td>73.69±0.74</td><td>48.70±0.63</td><td>89.75±0.52</td><td colspan=\"2\">83.64±0.67 76.09±1.27</td></tr><tr><td>GCN+ReP</td><td>45.89±1.45</td><td>47.57±3.90</td><td>86.43±0.74</td><td>52.75±0.62</td><td>96.05±0.19</td><td colspan=\"2\">86.08±0.84 77.96±0.96</td></tr><tr><td/><td colspan=\"2\">(256/256) (128/256)</td><td>(256/16)</td><td>(32/1)</td><td>(1/256)</td><td>(128/64)</td><td>(128/64)</td></tr><tr><td>∆</td><td>+6.42 (↑)</td><td colspan=\"3\">+6.68 (↑) +12.74 (↑) +4.05 (↑)</td><td>+6.30 (↑)</td><td colspan=\"2\">+2.44 (↑) +1.87 (↑)</td></tr></table>", "num": null, "html": null, "text": "We fix the non-linear activation function to ReLU.4.1.3. RESULTSTable2shows node classification results on the heterophilic datasets. Results marked with * and † in Table2are obtained from<PERSON><PERSON><PERSON><PERSON> et al. (2023b) and<PERSON><PERSON><PERSON> et al. (2023), and * in <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2021). Applying ReP shows performance improvement for all backbones across most heterophilic datasets, with the most significant and consistent improvement observed in GCN. GCN+ReP achieves state-of-the-art performance in four out of seven datasets and the second-best performance in one. In the datasets where GCN+ReP attained state-of-the-art performance, the number of reverse layers was consistently above 64. This observation shows that deep layers of GNNs with ReP can achieve superior performance without over-smoothing.", "type_str": "table"}, "TABREF2": {"content": "<table/>", "num": null, "html": null, "text": "Test accuracy and standard deviation on homophilic datasets. ∆ indicates the difference with and without ReP. We also report the number of forward and reverse layers below the performance of GCN+ReP and GAT+ReP. The best and the second-best are bolded and underlined, respectively.", "type_str": "table"}, "TABREF3": {"content": "<table/>", "num": null, "html": null, "text": ".1. Forward Pass of Forward Process Equation 10 involves three key operations: matrix multiplication between X and W with complexity O(|V|d 2 ), matrix multiplication between sparse matrix Â and X (ℓ) W with complexity O(|E|d), and matrix addition with complexity O(|V|d). Overall, the time complexity for a single layer is O(|V|d 2 + |E|d). Therefore, the total time complexity over L F forward layers is O(L F |V|d 2 + L F |E|d). Memory cost is calculated as O(L F |V|d + |E| + d 2 ).", "type_str": "table"}, "TABREF4": {"content": "<table/>", "num": null, "html": null, "text": "with a memory cost of O(|V|d + |E| + d 2 ).A.3. Forward Pass of Reverse ProcessTo calculate Equation11, the initial step involves computing (-1) m Âm X (ℓ) W m . The time complexity for this part is O(m|E|d + |V|d 2 + md 3 ). Summing over m from 0 to M , the overall complexity becomes O(M 2 |E|d + M |V|d 2 + M 2 d 3 ). With L R representing the number of reverse process layers, additional time complexity becomes O(M 2", "type_str": "table"}, "TABREF5": {"content": "<table/>", "num": null, "html": null, "text": "", "type_str": "table"}, "TABREF6": {"content": "<table><tr><td>(23)</td></tr></table>", "num": null, "html": null, "text": "). Statistics of the dataset utilized in the experiments.", "type_str": "table"}, "TABREF7": {"content": "<table><tr><td/><td/><td>CiteSeer</td><td>PubMed</td><td>Computers</td><td>Photo</td></tr><tr><td colspan=\"6\">MLP* 76.GAT+ReP 87.93±1.60 77.06±1.60 89.94±0.61 91.03±0.62 95.44±0.71</td></tr><tr><td/><td>(64/32)</td><td>(32/128)</td><td>(8/8)</td><td>(16/8)</td><td>(32/8)</td></tr><tr><td>∆</td><td colspan=\"3\">+0.26(↑) -0.30(↓) +0.28(↑)</td><td>-1.12(↓)</td><td>-0.42(↓)</td></tr><tr><td>GCN</td><td colspan=\"5\">88.00±1.42 77.15±1.44 89.37±0.52 91.87±0.57 95.35±0.47</td></tr><tr><td>GCN+ReP</td><td colspan=\"5\">87.63±1.40 77.33±1.65 89.96±0.55 90.92±0.52 95.50±0.63</td></tr><tr><td/><td>(32/512)</td><td>(8/32)</td><td>(32/32)</td><td>(32/64)</td><td>(32/128)</td></tr><tr><td>∆</td><td colspan=\"3\">-0.37(↓) +0.18(↑) +0.59(↑)</td><td>-0.95(↓)</td><td>+0.15(↑)</td></tr></table>", "num": null, "html": null, "text": "96±0.95 76.58±0.88 85.94±0.22 82.85±0.38 84.72±0.34 vanilla-GCN* 87.14±1.01 79.86±0.67 86.74±0.27 83.32±0.33 88.26±0.73 vanilla-GAT* 88.03±0.79 80.52±0.71 87.04±0.24 83.32±0.39 90.94±0.68 APPNP* 88.14±0.73 80.47±0.74 88.12±0.31 85.32±0.37 88.51±0.31 ChevNet* 86.67±0.82 79.11±0.75 87.95±0.28 87.54±0.43 93.77±0.32 GPR-GNN* 88.57±0.69 80.12±0.83 88.46±0.33 86.85±0.25 93.85±0.28 Bern<PERSON>* 88.52±0.95 80.09±0.79 88.48±0.41 87.64±0.44 93.63±0.35 GRAND 85.53±0.64 74.95±1.37 88.81±0.69 90.28±0.47 94.01±0.73 GRAND+ReP 85.73±1.39 75.78±1.48 89.03±0.61 89.51±0.78 94.48±0.61 ∆ +0.20 (↑) +0.83 (↑) +0.22 (↑) -0.77 (↓) +0.47 (↑) GAT 87.67±0.84 77.36±1.59 89.66±0.60 92.15±0.30 95.86±0.58", "type_str": "table"}, "TABREF8": {"content": "<table/>", "num": null, "html": null, "text": "Test accuracy and standard deviation on homophilic datasets. ∆ indicates the difference with and without ReP. We also report the number of forward and reverse layers below the performance of GCN+ReP and GAT+ReP. The best and the second-best are bolded and underlined, respectively.", "type_str": "table"}}}}