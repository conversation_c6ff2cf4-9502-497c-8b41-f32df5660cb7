{"paper_id": "SMM", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-24T23:34:47.419706Z"}, "title": "Sample-specific Masks for Visual Reprogramming-based Prompting", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Cai", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ye", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Jianzhong", "middle": [], "last": "Qi", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Visual reprogramming (VR) is a prompting technique that aims to re-purpose a pre-trained model (e.g., a classifier on ImageNet) to target tasks (e.g., medical data prediction) by learning a small-scale pattern added into input images instead of tuning considerable parameters within the model. The location of the pattern within input samples is usually determined by a pre-defined mask shared across all samples. In this paper, we show that the shared mask potentially limits VR's generalization and increases its approximation error due to the lack of sample-level adaptation. Motivated by this finding, we design a new framework for VR called sample-specific multi-channel masks (SMM). Specifically, SMM employs a lightweight ConvNet and patch-wise interpolation to generate sample-specific three-channel masks instead of a shared and pre-defined mask. Since we generate different masks for individual samples, SMM is theoretically shown to reduce approximation error for the target tasks compared with existing state-of-the-art VR methods. We also empirically demonstrate its performance gain on both ResNet and ViT. The success of SMM further highlights the broader applicability of VR in leveraging the latent knowledge of pre-trained models for various target tasks. Our code is available at https: //github.com/tmlr-group/SMM.", "pdf_parse": {"paper_id": "SMM", "_pdf_hash": "", "abstract": [{"text": "Visual reprogramming (VR) is a prompting technique that aims to re-purpose a pre-trained model (e.g., a classifier on ImageNet) to target tasks (e.g., medical data prediction) by learning a small-scale pattern added into input images instead of tuning considerable parameters within the model. The location of the pattern within input samples is usually determined by a pre-defined mask shared across all samples. In this paper, we show that the shared mask potentially limits VR's generalization and increases its approximation error due to the lack of sample-level adaptation. Motivated by this finding, we design a new framework for VR called sample-specific multi-channel masks (SMM). Specifically, SMM employs a lightweight ConvNet and patch-wise interpolation to generate sample-specific three-channel masks instead of a shared and pre-defined mask. Since we generate different masks for individual samples, SMM is theoretically shown to reduce approximation error for the target tasks compared with existing state-of-the-art VR methods. We also empirically demonstrate its performance gain on both ResNet and ViT. The success of SMM further highlights the broader applicability of VR in leveraging the latent knowledge of pre-trained models for various target tasks. Our code is available at https: //github.com/tmlr-group/SMM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Recent studies have shown that, by taking advantage of and re-purposing well-trained/pre-trained models, one can address new tasks (i.e., target tasks) without training a taskspecific model from scratch (<PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) . In visual tasks, due to the expensive training costs even just to finetune pre-trained models, visual reprogramming (VR) (<PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2024) , or adversarial reprogramming (<PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2020) , has been proposed to reuse pre-trained models on target tasks. Concretely, VR is a prompting method that fixes a pre-trained model and only alters the input space by adding some learnable patterns (usually some noise) to target images. The location of the patterns to be learned is usually determined by a pre-defined binary mask that is shared across all samples (<PERSON><PERSON> et al., 2018; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2022) . The key benefit of VR methods is that learning the pattern whose size is around the image size requires much less computing resource than finetuning considerable parameters within the model, posing VR as a promising research area in using pre-trained models (<PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2024) .", "cite_spans": [{"start": 203, "end": 222, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF1"}, {"start": 223, "end": 243, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF19"}, {"start": 244, "end": 264, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF23"}, {"start": 388, "end": 411, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF25"}, {"start": 412, "end": 430, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF34"}, {"start": 431, "end": 449, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF3"}, {"start": 450, "end": 468, "text": "<PERSON><PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF32"}, {"start": 500, "end": 522, "text": "(<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF8"}, {"start": 523, "end": 541, "text": "<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF31"}, {"start": 908, "end": 930, "text": "(<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF8"}, {"start": 931, "end": 949, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF37"}, {"start": 950, "end": 968, "text": "<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF31"}, {"start": 969, "end": 988, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF0"}, {"start": 1249, "end": 1268, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF3"}, {"start": 1269, "end": 1287, "text": "<PERSON><PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In this paper, we show that the shared mask often leads to poor generalization capability of VR, as demonstrated in Figures 1 and 2 . In both figures, we use a representative VR method, watermarking (<PERSON><PERSON> et al., 2022) , to re-purpose an ImageNet-pretrained classifier to classify images in the Ox-fordPets datasets (<PERSON><PERSON> et al., 2012) . In Figure 1 , we first find that the optimal masks vary among individual images. We apply three kinds of masks (full, medium, and narrow) in watermarking. By observing the classification confidence on three cat images: Sphynx, Abyssinian, and Bengal, we see that the medium mask is the best for Sphynx, the full mask for Abyssinian, and the narrow mask for Bengal. This suggests that different masks are needed for individual images. In Figure 2 , we then find that watermarking with a single shared mask may cause the training loss of many individual samples to rise (see the red part in Figure 2 ). This phenomenon reveals that VR methods' learning capacity is much less than finetuning all parameters of the pre-trained model (see the blue part in Figure 2 ).", "cite_spans": [{"start": 199, "end": 219, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF0"}, {"start": 317, "end": 338, "text": "(<PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF27"}], "ref_spans": [{"start": 124, "end": 125, "text": "1", "ref_id": "FIGREF0"}, {"start": 130, "end": 131, "text": "2", "ref_id": "FIGREF1"}, {"start": 351, "end": 352, "text": "1", "ref_id": "FIGREF0"}, {"start": 785, "end": 786, "text": "2", "ref_id": "FIGREF1"}, {"start": 937, "end": 938, "text": "2", "ref_id": "FIGREF1"}, {"start": 1099, "end": 1100, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "The examples above show a significant disadvantage of using a single shared mask for VR. This motivates our new VR framework called sample-specific multi-channel masks (SMM). SMM replaces the fixed binary mask applied in existing works with generative three-channel masks that can vary across different samples (shown in Figure 3 ). SMM has two modules: a mask generator module and a patch-wise interpolation module. The mask generator is a lightweight convolutional neural network (CNN) that takes resized individual target-domain images (i.e., samples) as the input and outputs different masks for each sample. The last layer of the generator is designed to generate a threechannel mask, which allows better performance for both rich-color images (i.e., CIFAR10/100 (<PERSON><PERSON><PERSON><PERSON>, 2009) ) and monotonous-color images (i.e., SVHN (<PERSON>, 2011) ). Since the generated masks should be the same size as the pattern to be learned, when the size of masks is inconsistent with that of the pattern, the patch-wise interpolation module will be utilized to re-scale the generated masks to fit the pattern, facilitating the training process of the mask generator (detailed in Section 3).", "cite_spans": [{"start": 768, "end": 786, "text": "(<PERSON><PERSON><PERSON><PERSON>, 2009)", "ref_id": "BIBREF21"}, {"start": 824, "end": 842, "text": "SVHN (Yu<PERSON>, 2011)", "ref_id": null}], "ref_spans": [{"start": 328, "end": 329, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "To understand why SMM is effective, we theoretically analyze the approximation error of different hypothesis sets for VR. Three hypothesis sets are considered: shared pattern with a pre-defined binary mask, sample-specific patterns without masks, and our SMM. We show that SMM has a smaller approximation error (Proposition 4.3), which confirms the effectiveness of SMM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "To further substantiate the efficacy of SMM, we conduct empirical evaluations spanning 11 widely used datasets, incorporating ablation studies that discern the impact of individual SMM components. This is complemented by analysis and interpretations of the generated masks, alongside a comparative visualization of feature spaces. Notably, we demonstrate the effectiveness of SMM with both pretrained ResNet (<PERSON> et al., 2016) and ViT (<PERSON><PERSON><PERSON><PERSON> et al., 2020) (Table 1 and 2 ), validating that SMM is compatible with commonly used classifier architectures.", "cite_spans": [{"start": 408, "end": 425, "text": "(<PERSON> et al., 2016)", "ref_id": "BIBREF11"}, {"start": 434, "end": 460, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF7"}], "ref_spans": [{"start": 468, "end": 469, "text": "1", "ref_id": "TABREF1"}, {"start": 474, "end": 475, "text": "2", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Both the theoretical analysis and promising experimental results provide solid evidence that, when powered by SMM, VR can efficiently leverage knowledge within a welltrained/pre-trained model for various target tasks, shedding new light on the explanatory analysis of VR and opening avenues for future research.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Model reprogramming (Chen, 2022 ) offers an efficient transfer learning paradigm for adapting pre-trained models to resource-constrained target tasks. This paradigm repurposes existing knowledge by strategically transforming inputs and outputs, bypassing extensive model parameter finetuning. In what follows, we will present a formal problem setting for model reprogramming.", "cite_spans": [{"start": 20, "end": 31, "text": "(Chen, 2022", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "Problem Setting of Model Reprogramming", "sec_num": "2.1."}, {"text": "Let D T represent the data distribution of a target task defined over X T × Y T , where X T ⊆ R dT is the data space and Y T = {1, . . . , k T } is the label space, and let {(x T i , y ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setting of Model Reprogramming", "sec_num": "2.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "1 n n i=1 ℓ(f out (f P (f in (x T i |θ))|Y P sub , ω), y T i ),", "eq_num": "(1)"}], "section": "Problem Setting of Model Reprogramming", "sec_num": "2.1."}, {"text": "where f in (.|θ) : X T → X P , f out (.|Y P sub , ω) : Y P sub → Y T are the input transformation and output label mapping function with parameters θ ∈ Θ and ω ∈ Ω, Y P sub ⊆ Y P can be determined by different methods (<PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2020; <PERSON> et al., 2023) , and ℓ : Y T × Y T → R + ∪ {0} is a loss function. Reprogramming techniques have been widely applied in visual (<PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2020) , text (<PERSON>eek<PERSON> et al., 2018; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) , speech (<PERSON> et al., 2021; 2023; <PERSON><PERSON> et al., 2023) , music (<PERSON> et al., 2023) , and cross-modal tasks (<PERSON><PERSON><PERSON> et al., 2022) in the past few years.", "cite_spans": [{"start": 218, "end": 240, "text": "(<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF8"}, {"start": 241, "end": 259, "text": "<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF31"}, {"start": 260, "end": 278, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF3"}, {"start": 391, "end": 413, "text": "(<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF8"}, {"start": 414, "end": 432, "text": "<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF31"}, {"start": 440, "end": 463, "text": "(<PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF24"}, {"start": 464, "end": 491, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF9"}, {"start": 501, "end": 520, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF37"}, {"start": 521, "end": 526, "text": "2023;", "ref_id": null}, {"start": 527, "end": 544, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": null}, {"start": 553, "end": 572, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF15"}, {"start": 597, "end": 620, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "Problem Setting of Model Reprogramming", "sec_num": "2.1."}, {"text": "In the context of visual tasks, reprogramming has demonstrated potential in bio-medical measurement (<PERSON><PERSON> et al., 2020) , machine learning fairness (<PERSON> et al., 2022) , as well as out-of-distribution detection through watermarking (<PERSON> et al., 2022) . Moving beyond application prospects, we next discuss the technical details of the specific input and output mapping functions (f in and f out ).", "cite_spans": [{"start": 100, "end": 119, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF31"}, {"start": 148, "end": 168, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF40"}, {"start": 233, "end": 252, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF34"}], "ref_spans": [], "eq_spans": [], "section": "Problem Setting of Model Reprogramming", "sec_num": "2.1."}, {"text": "General prompting methods in visual tasks, predominantly applied to the ViT architecture (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020) , introduce extra parameters to a pre-trained model for enhanced training efficiency. Prompts are flexible in their placement. For example, visual prompt tuning (<PERSON><PERSON> et al., 2022) positions prompts alongside image embedding before the encoder layers, while effective and efficient visual prompt tuning (<PERSON> et al., 2023) extends this by incorporating parameters within self-attention layers as well. Transformer with hierarchical prompting (<PERSON> et al., 2023 ) also learns prompt tokens to represent the coarse image classes.", "cite_spans": [{"start": 89, "end": 115, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF7"}, {"start": 277, "end": 295, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF16"}, {"start": 418, "end": 436, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF10"}, {"start": 556, "end": 574, "text": "(<PERSON> et al., 2023", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Prompting and Input Visual Reprogramming", "sec_num": "2.2."}, {"text": "Meanwhile, prompting goes beyond vision foundation models to vision-language frameworks such as CLIP (<PERSON><PERSON> et al., 2021) . Methods like CoOP (<PERSON> et al., 2022b) and CoCoOP (<PERSON> et al., 2022a) replace textual prompts with learnable vectors for enhanced adaptability to the target task, conditioned on input images. <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON> et al., 2023) further bridges vision and language by learning layerspecific mapping functions. These methods vary from each other in terms of both prompt placements and functions.", "cite_spans": [{"start": 101, "end": 123, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF28"}, {"start": 144, "end": 164, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 176, "end": 196, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 325, "end": 347, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "Prompting and Input Visual Reprogramming", "sec_num": "2.2."}, {"text": "In contrast, VR provides a model-agnostic prompting technique, by adding trainable noise to the input image patterns before the forward propagation, without altering their visual essence. Originally proposed by <PERSON><PERSON> et al. (2018) , VR has been evolving to include padding-based methods (<PERSON><PERSON> et al., 2020; <PERSON> et al., 2023) and watermarking that facilitate downstream target tasks (<PERSON><PERSON> et al., 2022) . AutoVP (<PERSON><PERSON><PERSON> et al., 2024) stands out with its scalable pre-padding images. A critical limitation in existing VR research is the use of shared noise patterns across all target samples, neglecting sample-level characteristics and compromising generalization. We propose SMM to manage this gap.", "cite_spans": [{"start": 211, "end": 232, "text": "<PERSON><PERSON> et al. (2018)", "ref_id": "BIBREF8"}, {"start": 289, "end": 308, "text": "(<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF31"}, {"start": 309, "end": 327, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF3"}, {"start": 385, "end": 405, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF0"}, {"start": 415, "end": 434, "text": "(<PERSON><PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Prompting and Input Visual Reprogramming", "sec_num": "2.2."}, {"text": "Learning-based output mapping, i.e., model f out , as proposed by <PERSON> et al. (2023) , can be simplified as a one-toone mapping from a subset of Y P to Y T . Therefore, no additional parameters are required. One implementation of this mapping is random label mapping (Rlm), where f out is a randomly assigned injective function (<PERSON><PERSON> et al., 2018; <PERSON> et al., 2023) , formulated as", "cite_spans": [{"start": 66, "end": 84, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF3"}, {"start": 328, "end": 350, "text": "(<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF8"}, {"start": 351, "end": 369, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Output Mapping of Reprogramming", "sec_num": "2.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "f Rlm out (y|Y P sub ) = rand({0, 1, ..., k T }),", "eq_num": "(2)"}], "section": "Output Mapping of Reprogramming", "sec_num": "2.3."}, {"text": "where rand({0, 1, ..., k T }) means randomly selecting one element from the set {0, 1, ..., k T }, and Y P sub is of the same size with Y T (i.e., k T ), randomly chosen from Y P prior to the minimization of Eq. (1). Note that, since f Rlm out is injective, it ensures f Rlm out (y 1 |Y P sub ) ̸ = f Rlm out (y 2 |Y P sub ) for two distinct elements y 1 ̸ = y 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Output Mapping of Reprogramming", "sec_num": "2.3."}, {"text": "Other representative output-mapping methods determine Y P sub and f out for different target tasks. For example, one is based on the frequency of label assignment in the pre-trained model and the target data (<PERSON><PERSON> et al., 2020) , called frequent label mapping (Flm). <PERSON> et al. (2023) propose iterative label mapping (Ilm) that updates f out in each training iteration, reflecting changes in label mapping throughout the learning of f in . Detailed procedures and the pseudo-code of f Flm out and f Ilm out are in Appendix A.4.", "cite_spans": [{"start": 208, "end": 227, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF31"}, {"start": 267, "end": 285, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Output Mapping of Reprogramming", "sec_num": "2.3."}, {"text": "We focus on f in , while treating f out as a non-parametric mapping, in line with <PERSON> et al. (2023) . We thus limit our discussion of trainable parameters to θ ∈ Θ in Eq. (1). A flowchart in Appendix A.1 provides an overview of the problem structure of Input VR.", "cite_spans": [{"start": 82, "end": 100, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Sample-specific Multi-channel Masks", "sec_num": "3."}, {"text": "To allow both shared parameters over the whole dataset and variability among individual samples, it is intuitive to Our method, on the other hand, takes a more dynamic and tailored approach. We resize each target image and apply a different three-channel mask accordingly, driven by a lightweight f mask with an interpolation up-scaling module, allowing for more variability in individual samples.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Framework of SMM", "sec_num": "3.1."}, {"text": "consider the following VR hypothesis:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Framework of SMM", "sec_num": "3.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "f in (x i |ϕ, δ) = r(x i ) + δ ⊙ f mask (r(x i )|ϕ),", "eq_num": "(3)"}], "section": "Framework of SMM", "sec_num": "3.1."}, {"text": "where r : X T → R dP is the resizing function, typically implemented as bilinear interpolation upsampling (Wikipedia contributors, 2023) that scales image dimension from d T to d P , and r(x i ) ∈ R dP is the resized image corresponding to x i . The mask generation function f mask : R dP → R dP , parameterized by ϕ ∈ Φ, produces a mask indicating the noise placements for each image. We denote a trainable noise pattern added to the image by δ ∈ R dP . The rationale for applying this hypothesis is elaborated in Proposition 4.3 and validated in ablation studies (cf. Table 3 ). This casts the training objective of our SMM framework (θ = {ϕ, δ}) to find the optimal ϕ * and δ * such that arg min", "cite_spans": [], "ref_spans": [{"start": 576, "end": 577, "text": "3", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Framework of SMM", "sec_num": "3.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ϕ∈Φ,δ∈R d P E (xi,yi)∼DT [ℓ(f out (f P (r(x i )+ δ ⊙ f mask (r(x i )|ϕ))), y i )].", "eq_num": "(4)"}], "section": "Framework of SMM", "sec_num": "3.1."}, {"text": "Note that δ is shared by all images in the dataset following <PERSON><PERSON> et al. (2022) and <PERSON> et al. (2023) , while f mask uniquely generates sample-specific multi-channel masks for each individual image, enabling sample-specific adaptation.", "cite_spans": [{"start": 61, "end": 80, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF0"}, {"start": 85, "end": 103, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Framework of SMM", "sec_num": "3.1."}, {"text": "Figure 3 illustrates the workflow of SMM, as well as previous padding-based and resizing-based (i.e., watermarking) VR methods. Compared with previous works, SMM features f mask (•|ϕ), integrating a mask generator module and a patch-wise interpolation module. Concretely, SMM starts by resizing target images, followed by their processing through the mask generator to create corresponding threechannel masks. For generated masks smaller than the pattern size, the patch-wise interpolation module performs upsampling, which omits the derivation step in back-propagation and facilitates training. Afterward, the learnable pattern δ is multiplied with the mask on a pixel-wise basis and added to the image. The resulting image is fed into the fixed pretrained classifier. We discuss further details on the mask generator (Section 3.2), the patch-wise interpolation module (Section 3.3), and the overall learning strategy presented in Eq. (4) (Section 3.4).", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Framework of SMM", "sec_num": "3.1."}, {"text": "The mask generator f mask is supposed to output a mask that has the same size as the input image while prioritizing different locations for δ to allow more variability. We employ a CNN as the mask generator. This choice stems from the proficiency of CNNs in mirroring localized visual perception (<PERSON> et al., 2016) with fewer parameters than most deep learning structures, e.g., multilayer perceptrons.", "cite_spans": [{"start": 296, "end": 313, "text": "(<PERSON> et al., 2016)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Lightweight Mask Generator Module", "sec_num": "3.2."}, {"text": "The input of CNN is a resized image r(x i ). Applying our bespoke CNN architecture shown in Appendix A.2, the output will be a three-channel mask with dimensions H 2 l × W 2 l , where H and W denote image height and width, respectively, and l denotes the number of pooling layers. The analysis of input/output sizes and parameter quantity statistics are in Appendix A.2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Lightweight Mask Generator Module", "sec_num": "3.2."}, {"text": "The patch-wise interpolation module upscales CNNgenerated masks from H 2 l × W 2 l back to the original size H × W per channel (it is omitted when l = 0). Considering the inherent consistency in adjacent image areas and the benefits of concise operations for gradient calculations, we employ a grid of H 2 l × W 2 l patches in the upsampling process, each sized 2 l × 2 l , ensuring the same values within each patch, with non-divisible cases mirroring the closest patches. Therefore, after obtaining the output of CNN, we Algorithm 1 Visual Reprogramming with SMM 1: Input: Pre-trained model f P , loss ℓ, label-mapping function f", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Patch-wise Interpolation Module", "sec_num": "3.3."}, {"text": "(j) out for iteration j, target domain training data {(x i , y i )} n i=1 , maximum number of iterations E, learning rate α 1 for δ and α 2 for ϕ 2: Output: Optimal δ * , ϕ * 3: Initialize ϕ randomly; set δ ← {0} dP 4: for j = 1 to E do 5: # Step1: Compute individual marks using the mask generator # Step2: Resize masks using the patch-wise interpolation module", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Patch-wise Interpolation Module", "sec_num": "3.3."}, {"text": "f in (x i ; δ, ϕ) ← r(x i ) + δ ⊙ f mask (r(x i )|ϕ), ∀i = 1, 2, ..., n 6: # Compute the classification loss L(δ, ϕ) ← 1 n n i=1 ℓ(f (j) out (f P (f in (x i ; δ, ϕ))), y i ) 7: δ ← δ -α 1 ∇ δ L(δ, ϕ) 8: ϕ ← ϕ -α 2 ∇ ϕ L(δ, ϕ) 9:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Patch-wise Interpolation Module", "sec_num": "3.3."}, {"text": "end for enlarge each pixel to 2 l × 2 l pixels by padding the same value of a pixel to its surrounding areas within the patch.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Patch-wise Interpolation Module", "sec_num": "3.3."}, {"text": "Unlike traditional interpolation methods which may introduce complicated derivation computations, our module simplifies the training by directly assigning values. The advantage of patch-wise interpolation over traditional interpolation methods will be discussed in Appendix A.3. The effect of patch size 2 l will be discussed in Section 5.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Patch-wise Interpolation Module", "sec_num": "3.3."}, {"text": "The learning process for the shared noise pattern δ and the mask generator f mask is shown in Algorithm 1. The parameters δ and ϕ are iteratively updated in each epoch. To mitigate the impact of initialization, δ is set to be a zero matrix before training, noted as {0} dP .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Learning Strategy", "sec_num": "3.4."}, {"text": "Reprogramming for Classification", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "In this section, we will demonstrate that SMM enables stronger model learning capacity than previous representative VR methods, via showing reduced approximation error in the probably approximately correct (PAC) learning framework (<PERSON><PERSON><PERSON> & <PERSON>, 1994) . We first present the definition of the approximation error in PAC learning.", "cite_spans": [{"start": 231, "end": 256, "text": "(Kearns & Vazirani, 1994)", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "Definition 4.1 (Approximation Error). Consider an input space X , a discrete label space Y, a random variable (X, Y ) whose distribution D is defined on X × Y with a joint probability density function p(x, y), and a hypothesis space", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "F = {f : X → Y}. The approximation error of F on D is Err apx D (F) = inf f ∈F E (X,Y )∼D ℓ(f (X), Y ) -R * D ,", "eq_num": "(5)"}], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "where ℓ : Y × Y → R + ∪ {0} is a loss function, and R * D is the <PERSON><PERSON> risk (<PERSON><PERSON><PERSON>, 1995) on D defined by", "cite_spans": [{"start": 76, "end": 94, "text": "(<PERSON><PERSON><PERSON> & Xu, 1995)", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "R * D = X 1 -sup y∈Y Pr(y|x) p X (x)dx.", "eq_num": "(6)"}], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "Here, Pr(y|x) is the posterior probability of class y conditioned on observing x, and p X (x) = y∈Y p(x, y) is the marginal distribution of X.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "The approximation error of a hypothesis space F measures the closeness of the minimum achievable error by F to the theoretical minimum error on distribution D. In general, increasing the complexity of F tends to reduce the approximation error. In the following theorem, we show a connection between two approximation errors when hypothesis spaces exhibit a subset relation. Theorem 4.2. Given an input space X , a discrete label space Y, and a distribution D over X × Y, if there are two hypothesis spaces", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "F 1 ⊆ {f : X → Y} and F 2 ⊆ {f : X → Y} satisfying that F 1 ⊆ F 2 , then we have Err apx D (F 1 ) ≥ Err apx D (F 2 ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "Theorem 4.2 (proof in Appendix B.1) shows that understanding the subset relation between two hypothesis spaces is key to deriving their connections in their approximation errors. Next, we will define two hypothesis spaces: one induced by a shared mask and the other induced by SMM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "Hypothesis Space Induced by A Shared Mask. VR methods with a shared mask (<PERSON>, 2022; <PERSON><PERSON> et al., 2022) assume that, for each sample x i , the mask is a constant matrix M ∈ {0, 1} dP . Thus, given a fixed pre-trained model f P and a fixed output mapping function f out (for simplicity, we use f ′ P to represent f out • f P in this section), the hypothesis space induced by a shared mask is", "cite_spans": [{"start": 73, "end": 85, "text": "(Chen, 2022;", "ref_id": "BIBREF4"}, {"start": 86, "end": 105, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "F shr (f ′ P ) = {f |f (x) = f ′ P (r(x) + M ⊙ δ), ∀x ∈ X },", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "where δ ∈ R dP . In padding-based reprogramming methods, M is a fixed mask determined by the location of the target image (Chen, 2022 ). The locations where x i is placedusually the center of r(x i ) -are denoted as {i : M i = 0}, which are excluded from further training. The rest of the locations, denoted by {i : M i = 1}, indicate trainable parameters δ. In watermarking-based methods (<PERSON><PERSON> et al., 2022) , x i is up-sampled to r(x i ), and {i : M i = 1} denotes effective locations of δ added to r(x i ).", "cite_spans": [{"start": 122, "end": 133, "text": "(Chen, 2022", "ref_id": "BIBREF4"}, {"start": 389, "end": 409, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "Hypothesis Space Induced by SMM. Based on Eq. ( 4), we can obtain the hypothesis space used in SMM: ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "F smm (f ′ P ) = {f |f (x) = f ′ P (r(x) + f mask (r(x)) ⊙ δ), ∀x ∈ X }.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Err apx DT (F shr (f ′ P )) ≥ Err apx DT (F smm (f ′ P )),", "eq_num": "(7)"}], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "f ′ P = f out • f P , f mask used in F smm (f ′ P", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": ") is a CNN demonstrated in Section 3.2, and D T denotes the distribution of the target task.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "Proposition 4.3 (see its proof in Appendix B.2) shows that SMM achieves a lower approximation error than previous shared-mask VR methods.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "Estimation Error Analysis of SMM. While a lower approximation error does not suffice to guarantee a lower excess risk, the model complexity added to F smm (f ′ P ) is manageable in this VR setting, since f mask introduces less than 0.2% extra parameters 1 relative to f ′ P . Such dominance of f ′ P suggests that the estimation error of F smm (f ′ P ) does not significantly exceed that of F shr (f ′ P ) and is unlikely to offset its advantage in approximation error. We also provide an empirical justification from the standpoint of over-fitting to show that the additional estimation error of F smm (f ′ P ) is negligible in Appendix D.3. By comparing the disparities in training and testing performance, we demonstrate that SMM does not increase the risk of model over-fitting, implying negligible estimation error.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "1 See Table 4 for statistics on network sizes.", "cite_spans": [], "ref_spans": [{"start": 12, "end": 13, "text": "4", "ref_id": "TABREF4"}], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "Excess Risk Analysis of SMM. According to excess risk decomposition2 , SMM is also expected to have a lower excess risk and, consequently, superior generalization capability compared to shared-mask VR methods.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "Analysis Based on Sample-specific Patterns. Having built the concept of \"sample-specific\", we also investigate an alternative to the proposed SMM: directly learning a samplespecific pattern for each image without involving δ. The hypothesis space in this context can be expressed by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "F sp (f ′ P ) = {f |f (x) = f ′ P (r(x) + f mask (r(x))), ∀x ∈ X }.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "It is easy to check that F sp (f ′ P ) ⊆ F smm (f ′ P ), implying that Err apx DT (F sp (f ′ P )) ≥ Err apx DT (F smm (f ′ P )) (proof in Appendix B.3). Namely, SMM has a lower approximation error compared to directly learning a sample-specific pattern.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Understanding Masks in Visual", "sec_num": "4."}, {"text": "Pre-trained Models and Target Tasks. Following <PERSON> et al. ( 2023), we use ResNet-18, and ResNet-50 (<PERSON> et al., 2016) as the pre-trained model. Performance on pre-trained ViT-B32 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020 ) is also tested. All these models are pre-trained on ImageNet-1K (<PERSON><PERSON> et al., 2009) , and target tasks include CIFAR10, CIFAR100 (<PERSON><PERSON><PERSON><PERSON>, 2009) , SVHN (<PERSON><PERSON>, 2011) , GTSRB (<PERSON><PERSON> et al., 2013) , Flowers102 (<PERSON><PERSON>back & Zisserman, 2008) , DTD (Cim<PERSON>i et al., 2014) , UCF101 (<PERSON><PERSON><PERSON> et al., 2012) , Food101 (<PERSON><PERSON> et al., 2014) , EuroSAT (<PERSON><PERSON><PERSON> et al., 2019) , Ox-fordPets (<PERSON><PERSON> et al., 2012) , SUN397 (<PERSON> et al., 2010) . Moreover, StanfordCars (<PERSON>ra<PERSON> et al., 2013) , which is revealed to be unsuitable for VR, is also discussed in Appendix D.4. We follow <PERSON> et al. (2023) to split the datasets. Detailed dataset information is included in Appendix C.", "cite_spans": [{"start": 100, "end": 117, "text": "(<PERSON> et al., 2016)", "ref_id": "BIBREF11"}, {"start": 179, "end": 204, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020", "ref_id": "BIBREF7"}, {"start": 271, "end": 290, "text": "(<PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF6"}, {"start": 336, "end": 354, "text": "(<PERSON><PERSON><PERSON><PERSON>, 2009)", "ref_id": "BIBREF21"}, {"start": 357, "end": 375, "text": "SVHN (Yu<PERSON>, 2011)", "ref_id": null}, {"start": 384, "end": 405, "text": "(<PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF13"}, {"start": 419, "end": 447, "text": "(<PERSON><PERSON><PERSON> & Zisserman, 2008)", "ref_id": "BIBREF26"}, {"start": 454, "end": 475, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF5"}, {"start": 485, "end": 506, "text": "(<PERSON><PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF30"}, {"start": 517, "end": 539, "text": "(<PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF2"}, {"start": 550, "end": 571, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF12"}, {"start": 586, "end": 607, "text": "(<PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF27"}, {"start": 617, "end": 636, "text": "(<PERSON> et al., 2010)", "ref_id": "BIBREF36"}, {"start": 662, "end": 683, "text": "(<PERSON><PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF20"}, {"start": 774, "end": 792, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "Baselines. We compare our method with both paddingbased (<PERSON> et al., 2023) and resizing-based methods (<PERSON><PERSON> et al., 2022) , including: (1) Pad: centering the original image and adding the noise pattern around the images, (2) Narrow: adding a narrow padding binary mask with a width of 28 ( 18 of the input image size) to the noise pattern that covers the whole image (watermark), (3) Medium: adding a mask being a quarter of the size (the width is 56) of watermarks and (4) Full: full watermarks that cover the whole images following <PERSON> et al. (2022) . To ensure that all the methods are fairly compared, in training the shared noise pattern, we apply the same learning rate and milestones following <PERSON> et al. ( 2023), with 0.01 being the initial learning rate and 0.1 being the learning rate decay. Two hundred epochs are run in total, and the 100th and the 145th epochs are the milestones. The training details of the mask generator are included in Appendix C. Experiments are run with three seeds on a single A100 GPU and the averaged test accuracy is reported. Due to page limits, we report here only the results obtained with the output mapping f Ilm out . See Appendix D.1 for the results using f Rlm out and f Flm out . Results on ResNets. Table 1 reports the accuracy of ResNet-18 and ResNet-50 using VR methods with the baseline shared marks and our proposed SMM method. It can be observed that our SMM yields higher accuracy for both models on all datasets tested except for ResNet-18 on DTD. The advantage is more pronounced on the datasets where the target domains are more different from the original domain, such as SVHN, Flowers102, and EuroSAT. On SVHN, 6.1% and 3.1% improvements have been witnessed for ResNet-18 and ResNet-50, respectively, while over 10% improvement is observed on the Flowers102. On DTD, the padding-based method has better results for ResNet-18. This is likely to be due to the noisy watermarks adversely impacting the texture that needs to be classified, leading to the disadvantages of resizing-based methods. Even in this challenging setting, our SMM method leads to higher accuracy when applied on the larger pre-trained model ResNet-50.", "cite_spans": [{"start": 56, "end": 75, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF3"}, {"start": 103, "end": 123, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF0"}, {"start": 536, "end": 554, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF34"}], "ref_spans": [{"start": 1259, "end": 1260, "text": "1", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "Results on ViT. Recall that input reprogramming can be applied to diverse pre-trained classifiers, we next turn our focus on ViT. Detailed in Table 2 , our comparative study with baselines reveals substantial performance gains in datasets like Flowers102 (21.8%), Food101 (15.4%), and SUN397 (7.3%). These results suggest that SMM may yield even higher performance gains for larger pre-trained models. Exceptions do exist, like on EuroSAT, where all resizingbased methods show marginal under-performance, possibly a result of over-fitting on relatively simpler datasets. On UCF101, our SMM initially lags behind other strategies like narrow or medium masking but, after choosing appropriate learning rate parameters (See Appendix C), could achieve a leading 49.9% accuracy. Overall, the experiments above show the applicability of SMM over different pre-trained Impact of Masking. We first investigate the impact of different masking strategies. We take three variants against the proposed SMM into comparison: (i) Shared-pattern VR f in (x i ) = r(x i ) + δ, with M being an all-one matrix equal to the image dimension for maximal flexibility in δ. It defaults to the \"full watermarks\" baseline without using f mask . (ii) Sample-specific pattern without masking", "cite_spans": [], "ref_spans": [{"start": 148, "end": 149, "text": "2", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "f in (x i ) = r(x i ) + f mask (r(x i )). (iii) Single-channel ver- sion of SMM f in (x i ) = r(x i )+δ⊙f s mask (r(x i ))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": ", averaging the penultimate-layer output of the mask generator. These variants refer to the first three columns of Table 3 , respectively. They help evaluate the impact of sample specificity, masking, and multiple channels introduced by SMM in the context of input VR.", "cite_spans": [], "ref_spans": [{"start": 121, "end": 122, "text": "3", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "As shown in Table 3 , SMM consistently stands out as the best performer on all datasets. A key observation is that only keeping shared pattern δ reduces VR effectiveness in featurerich datasets (e.g., CIFAR10, Flowers102, and UCF101). Besides, using only f mask without δ, leads to suboptimal performance on datasets with enough training data per class, including CIFAR10, SVHN, GTSRB, and SUN397. Moreover, the single-channel method is less effective, especially on datasets where images have fewer varying color palettes (e.g., GTSRB and Flowers102). Overall, we find that the shared noise in SMM boosts model performance if sufficient training data is provided, whereas the sample-specific f mask enables specificity for classification tasks demanding detailed feature discrimination. Lastly, the multi-channel allows for adjusting to channel-specific priorities.", "cite_spans": [], "ref_spans": [{"start": 18, "end": 19, "text": "3", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "Impact of Patch Size. As an important hyperparameter in SMM, number of Max-Pooling layers, l, can vary, which means different patch sizes 2 l . Since the 5-layer mask generator neural network has at most 4 Max-Pooling layers, we examine the impact of patch sizes in {2 0 , 2 1 , 2 2 , 2 3 , 2 4 }. Results are shown in Figure 4 . As the patch size increases, the accuracy of the SMM increases first, followed by a plateau or decline. This suggests that overly small patches may cause over-fitting, while overly large patch sizes could result in a loss of details in SMM. We thus have set the patch size to be 8 across all datasets.", "cite_spans": [], "ref_spans": [{"start": 326, "end": 327, "text": "4", "ref_id": null}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "Visualization of SMM, shared patterns and output reprogrammed images. Visualization results on Flowers102 dataset is shown in Figure 5 . It can be observed that when classifying passion flowers, where pedals are important for classification accuracy, the masks tend to mask out the noise pattern over the pedals, which protects useful information from being shadowed by noise. Other features such as flower pistils in passion flowers are also widely present in various similar classes such as 'oxeye', 'daisy' and 'orange dahlia', making the centers of flowers potential sources of interference in classification. Thus, for passion flowers, noise in the center of the flowers is not masked out. When classifying 'water lily', SMM will enhance the noise on interfering objects in the image. Similarly, when classifying 'cyclamen', similar stems are also commonly found in other classes such as 'gaura' and 'rose', which hinders accurate classification. Therefore, it is reasonable for SMM to introduce more noise to these interfering components. These results show that SMM is able to retain the important parts of the image and remove the interference.", "cite_spans": [], "ref_spans": [{"start": 133, "end": 134, "text": "5", "ref_id": null}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "Feature Space Visualization Results. Figure 6 shows the tSNE (<PERSON> & <PERSON>, 2008) visualization results of the output layer feature before the label mapping layer. Before applying VR methods, the target domain's output feature space shows limited class separation. With the baseline methods, we observe enhanced but incomplete separations, where certain class pairs (such as '3, 5' and '6, 8' in SVHN, 'River' and 'highway or road' in EuroSAT) remain indistinguishable in the feature space. By applying f mask , our method successfully resolves incorrectly clustered classes, underscoring the effectiveness of SMM.", "cite_spans": [{"start": 386, "end": 389, "text": "'3,", "ref_id": null}, {"start": 390, "end": 400, "text": "5' and '6,", "ref_id": null}, {"start": 401, "end": 412, "text": "8' in SVHN,", "ref_id": null}, {"start": 413, "end": 454, "text": "'River' and 'highway or road' in EuroSAT)", "ref_id": null}], "ref_spans": [{"start": 44, "end": 45, "text": "6", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "Comparison with Finetuning-based Methods. In Appendix E, we compare our SMM with two prevalent finetuning approaches: finetuning fully connected layers and low-rank adaptation (<PERSON> et al., 2023) . This comparison highlights two key benefits of input VR: (1) its efficacy in target tasks with lower-resolution images and (2) its orthogonal relationship to, yet compatibility with, finetuning methods. Additionally, Appendix E provides a comprehensive discussion on the strengths and weaknesses of Input VR in comparison to finetuning techniques.", "cite_spans": [{"start": 176, "end": 194, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF43"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "More Experiments. The training curves are plotted and analyzed in Appendix D.2. The effectiveness of SMM when learning with different f out is discussed in Appendix D.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "In this paper, we identified significant shortcomings in the use of a shared mask across all samples in previous VR practices, notably its failure to accommodate sample diversity, leading to increased training loss of particular samples. In response, we proposed a new SMM learning framework, integrating a lightweight neural net-based mask generator to generate three-channel masks per sample, and a patch-wise interpolation module that resizes and aligns masks to model input. Both theoretical justification and experimental results validated the effectiveness of our proposed method. and W +2p-k s + 1. As shown in Figure 10 , when s = 1, p = 1, k = 3, the size of a single channel remains unchanged; when s = 2, p = 0, k = 2, the size of a channel is reduced by half in each dimension. In other words, by only using 3 × 3 convolution layers, f mask (.|ϕ) can retain the original size of a single channel. However, if we introduce Max-Pooling layers to remove redundant information, the output size will be shrunk and another patch-wise interpolation module should be included in f mask (.|ϕ) for resizing. Assuming that l Max-Pooling layers are used, the output size of a single channel becomes H 2 l × W 2 l . Parameter Statistics. The parameter statistics of the mask generator, f mask , are summarized in Table 4 . This includes a detailed breakdown of f mask across different pre-trained backbone models, a relative size comparison with the watermarking reprogramming method, and the number of trainable parameters added to frozen pre-trained models by f mask . From the size point of view, our mask generator is indeed lightweight and efficient: the CNN architectures contribute only 17.6% and 23.13% of the additional trainable parameters required by watermarking reprogramming. Moreover, relative to the total parameters in pre-trained models, the additional contribution of mask generators is trivial, ranging from 0.1% to 0.23% of parameters, which highlights its minimal footprint. To assess the efficiency of patch-wise interpolation, we compare it with bilinear and bicubic methods, employing the following numerical metrics for evaluation: (1) Number of Pixel Accesses: The count of times pixel values are retrieved per image during an interpolation algorithm. The fewer, the better. (2) Time Per Batch: The time cost for processing a batch of 256-sized images. The fewer, the better.", "cite_spans": [], "ref_spans": [{"start": 625, "end": 627, "text": "10", "ref_id": "FIGREF0"}, {"start": 1318, "end": 1319, "text": "4", "ref_id": "TABREF4"}], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "As shown in Table 5 , the patch-wise interpolation module excels across all metrics. This module exclusively involves copying operations, thus avoiding floating-point calculations and avoiding backpropagation gradient computations during training. Consequently, it is more efficient.", "cite_spans": [], "ref_spans": [{"start": 18, "end": 19, "text": "5", "ref_id": "TABREF5"}], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "A.4. Detailed Explanation of Ouptput Mapping Methods f Flm out and f Ilm out", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "The inverse function of f out regarding Flm is an injective function:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "y P Flm = arg max y∈Y P P r (xi,yi)∼DT {y = f P (f in (x i |θ))|y i = y T },", "eq_num": "(8)"}], "section": "Conclusion", "sec_num": "6."}, {"text": "where y P Flm is the optimal y P given the target label y T , f P (f in (x i |θ)) is the predicted label given the input image x i . For all images with the label y T , the predicted y P with the highest probability will be y P Flm for a given y T . Flm remains unchanged throughout iterations. For a specific y T , Flm determines the correspondence between y T and the most frequently assigned class y P in Y P , utilizing the well-trained network for all target training samples of the class y T , thus obtaining f Flm out , shown in Algorithm 3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "As the label mapping may change from time to time when learning f in , <PERSON> et al. (2023) proposed an iterative label mapping (Ilm) method that updates f out (•) after each training iteration. Let y P,(j) Ilm be the optimal y P in the jth training epoch. We have:", "cite_spans": [{"start": 71, "end": 89, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "y P,(j+1) Ilm = arg max y∈Y P P r (xi,yi)∼DT {y = f P (f (j) in (x i |θ (j) ))|y i = y T },", "eq_num": "(9) where f (j)"}], "section": "Conclusion", "sec_num": "6."}, {"text": "in (•|θ (j) ) is the parameters of the jth epoch. The output mapping function is updated after each iteration until convergence.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Algorithm 2 Computing Frequency Distribution of [f P (f in (x i |θ)), y T ] 1: Input: Target training set {(x T i , y T i )} n i=1 , given input VR f in (•|θ) and pre-trained model f P (•) 2: Output: Frequency distribution matrix d ∈ Z |Y P |×|Y T | 3: Initialize d ← {0} |Y P |×|Y T | 4: # Compute frequency distribution d 5: for i = 1...n do 6: ŷP i ← f P (f in (x T i |θ))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "7: The approximation error of F 1 and F 2 can be formulated as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "d ŷP i ,y T i ← d ŷP i ,y T i + 1 8: end for Algorithm 3 Frequent Label Mapping (f Flm out ) 1: Input: Label space of the pre-trained task Y P , label space of the target task Y T , target training set {(x T i , y T i )} n i=1 , given pre-trained model f P (•) 2: Output: Flm f Flm out : Y P sub → Y T 3: Initialize f Flm out (•) ← 0, subset Y P sub ← ∅ to", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Err apx D (F 1 ) = inf f ∈F1 E (X,Y )∼D ℓ(f (X), Y ) -R * D , Err apx D (F 2 ) = inf f ∈F2 E (X,Y )∼D ℓ(f (X), Y ) -R * D ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Straightforwardly,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "F 1 ⊇ F 2 ⇔ ∀f ∈ F 2 , f ∈ F 1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Algorithm 4 Iterative Label Mapping (f Ilm out ) 1: Input: Label space of the pre-trained task Y P , label space of the target task Y T , target training set {(x T i , y T i )} n i=1 , given pre-trained model f P (•), total iteration number E, learning rate α 2: Output: Ilm f Ilm,(j) out : Y P sub → Y T for iteration j 3: Initialize f Ilm,(j) out (•) ← 0, subset Y P sub ← ∅ to store matched labels, initialize f in (•|θ) to be an identity function (θ ← 0) 4: for j = 1...E do end while", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "15: # Train f in (•|θ) for iteration j 16: θ ← θ -α • ∇ θ 1 n n i=1 ℓ(f Ilm,(j) out (f P (f in (x T i |θ))), y T i ) 17: end for Given F 1 ⊆ F 2 , we have: ∀f ∈ F 1 , f ∈ F 2 , ⇒ inf f ∈F1 E (X,Y )∼D ℓ(f (X), Y ) ≥ inf f ∈F2 E (X,Y )∼D ℓ(f (X), Y ) ⇒Err apx D (F 1 ) ≥ Err apx D (F 2 ) B.2. Proof of Proposition 4.3", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "We prove Proposition 4.3 as follows.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Proof. With specially designed kernel and padding sizes, the output of CNN can be reshaped to match the size of the input image. Assuming", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "d P = H × W × C, we define M ′ ∈ {0, 1} H * W * C×1 and f ′ mask (•) ∈ R H * W * C×1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "as transposed flattened M and f mask (•), respectively. As the last layer of f ′ mask (•) is CNN, if the input of CNN is the resized image r(x), with x ∈ X T (and r(x) ∈ R dP ), we have f ′ mask (r(x)) = W last f ′′ mask (r(x)) + b last , with b last being the bias of the last layer, and W last being the mapping from the flattened input of the last CNN layer (i.e., f ′′ mask (r(x))) to the flattened output without adding the bias, which can be derived using the parameters of the last CNN layer. With the set of any possible W last being represented by {W last }, and all-zero matrix being O, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "b last ∈ R H * W * C×1 , M ′ ∈ {0, 1} H * W * C×1 ⇒ ∀M ′ , M ′ ∈ {b last |b last ∈ R H * W * C×1 } (10) O ∈ {W last }(When all weights in the last CNN layer is 0, W last is a zero matrix) ⇒ f (x) = O H * W * C×1 ∈ {f |f (x) = W last f ′′ mask (r(x)), ∀x ∈ X T } (11) ⇒ {f |f (x) = M ′ , ∀x ∈ X T } ⊆ {f |f (x) = f ′ mask (r(x))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": ", ∀x ∈ X T }(Given Eq. ( 10) and Eq. ( 11))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "⇒ {f |f (x) = M, ∀x ∈ X T } ⊆ {f |f (x) = f mask (r(x)), ∀x ∈ X T } ⇒ {f |f (x) = M ⊙ δ, ∀x ∈ X T } ⊆ {f |f (x) = f mask (r(x)) ⊙ δ, ∀x ∈ X T } ⇒ F shr (f ′ P ) ⊆ F smm (f ′ P )(since f ′ P is fixed) ⇒ Err apx DT (F smm (f ′ P )) ≤ Err apx DT (F shr (f ′ P ))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "We will then prove Proposition B.1. for any fixed f ′ P , it holds that F sp (f ′ P ) ⊆ F smm (f ′ P ), and consequently, Err apx DT (F smm (f ′ P )) ≤ Err apx DT (F sp (f ′ P )).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3. SMM and Sample-specific Patterns", "sec_num": null}, {"text": "Proof. Let ∆ be the set of possible δ, with all-one matrix being denoted as J, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3. SMM and Sample-specific Patterns", "sec_num": null}, {"text": "⇒ J dP ∈ ∆ ⇒ {f |f (x) = f mask (r(x)) ⊙ J dP , ∀x ∈ X T } ⊆ {f |f (x) = f mask (r(x)) ⊙ δ, ∀x ∈ X T } ⇒ {f |f (x) = f mask (r(x)), ∀x ∈ X T } ⊆ {f |f (x) = f mask (r(x)) ⊙ δ, ∀x ∈ X T } ⇒ F sp (f ′ P ) ⊆ F smm (f ′ P )(Since f ′ P is fixed) ⇒ Err apx DT (F smm (f ′ P )) ≤ Err apx DT (F sp (f ′ P ))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3. SMM and Sample-specific Patterns", "sec_num": null}, {"text": "C. Additional Experimental Setup The 11 datasets used for the experiments are summarized in Table 6 , while the corresponding training parameters are listed in Table 9 . When learning the ResNet tasks, we follow the same learning strategies as <PERSON> et al. (2023) . When learning ViT-B32, we choose the initial learning rate α and the learning rate decay γ with a training parameter searching experiment, with results presented in Table 7 . Sharing the same α and γ may not be optimal for all datasets. As shown in Table 8 , on UCF101, using α = 0.001 and γ = 1 derived from Table 7 leads to sub-optimal model performance. Nevertheless, for uniformity and fairness in this paper, we still use a single set of unified training parameters for all datasets. 12 , employing a more sophisticated pre-trained network such as ViT with a mask generator f mask shown in Figure 9 across some tasks like CIFAR10, SVHN, and GTSRB, the training accuracy tends towards 100% for both shared patterns F shr (f ′ P ) (i.e., 'Watermarking' in Figure 12 ) and SMM patterns F smm (f ′ P ) (i.e., 'Ours' in Figure 12 ). Despite this, F smm (f ′ P ) maintains a test accuracy that is not inferior to that of shared patterns. It suggests that our method SMM does not suffer from more significant over-fitting than shared masking, resulting in negligible potential estimation error.", "cite_spans": [{"start": 244, "end": 262, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF3"}], "ref_spans": [{"start": 98, "end": 99, "text": "6", "ref_id": "TABREF6"}, {"start": 166, "end": 167, "text": "9", "ref_id": null}, {"start": 436, "end": 437, "text": "7", "ref_id": "TABREF7"}, {"start": 520, "end": 521, "text": "8", "ref_id": "TABREF8"}, {"start": 580, "end": 581, "text": "7", "ref_id": "TABREF7"}, {"start": 754, "end": 756, "text": "12", "ref_id": "FIGREF1"}, {"start": 867, "end": 868, "text": "9", "ref_id": null}, {"start": 1031, "end": 1033, "text": "12", "ref_id": "FIGREF1"}, {"start": 1092, "end": 1094, "text": "12", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "B.3. SMM and Sample-specific Patterns", "sec_num": null}, {"text": "More Discussion of SMM Abnormal Cases. In Section 5, we have briefly analyzed abnormal performance in Table 1 and Table 2 . In this section, we will provide a more comprehensive discussion. Here, we outline detailed discussions regarding abnormal performance:", "cite_spans": [], "ref_spans": [{"start": 108, "end": 109, "text": "1", "ref_id": "TABREF1"}, {"start": 114, "end": 121, "text": "Table 2", "ref_id": "TABREF2"}], "eq_spans": [], "section": "D.4. Further Analysis of the Performance of SMM", "sec_num": null}, {"text": "• ResNet-18, DTD: As shown in Figure 18 , the DTD dataset contains a significant amount of texture features. Therefore, for relatively simple well-trained models, introducing reprogramming noise in the form of watermarking may affect the original features of the images. It can be observed that when the watermarking area is small (Narrow), the effect is better compared to when it is large (Full), and our method is also affected by this factor. However, the padding-based method preserves the original pixels of the image and only introduces reprogramming noise around them, thereby achieving relatively good results.", "cite_spans": [], "ref_spans": [{"start": 37, "end": 39, "text": "18", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "D.4. Further Analysis of the Performance of SMM", "sec_num": null}, {"text": "• ViT-B32, EuroSAT: This is because EuroSAT is one of the target tasks with the least task complexity. When using a large-scale network like ViT, the resizing-based method leads to over-fitting. As evident in the third column of the second row in Figure 12 , the training accuracy is already close to 1. Therefore, in this scenario, the padding-based method yields slightly better test results compared to our method (which also belongs to resizing-based methods). In this section, we will compare the results of our SMM with finetuning-based methods to show the advantages of input VR in dealing with distorted input images. Low-rank adaptation (LoRA) (<PERSON> et al., 2021) is an efficient finetuning-based transfer method proposed based on large language models for natural language processing, which has been adapted to ViT (<PERSON> et al., 2023) . Here, we compare SMM for ViT with LoRA for ViT, which are representative methods that belong to input VR and finetuning, respectively.", "cite_spans": [{"start": 653, "end": 670, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF14"}, {"start": 823, "end": 841, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF43"}], "ref_spans": [{"start": 254, "end": 256, "text": "12", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "D.4. Further Analysis of the Performance of SMM", "sec_num": null}, {"text": "Since LoRA for ViT already includes finetuning the fully connected layers, we also incorporate it in SMM. All training settings are kept the same. We set the rank of LoRA to be six, resulting in an additional parameter number being 0.60M (without counting the fully connected layers), which will be comparable to that of input VR and SMM (being 0.54M) for fairness. ViT-Large with the input size being 384×384 is applied, and the learning rate is 0.01, running 10 epochs in total.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.4. Further Analysis of the Performance of SMM", "sec_num": null}, {"text": "Therefore, for both methods, the target training samples will be resized before input. VR mainly trains parameters in the input space before well-trained models, whereas LoRA injects parameters into layers of ViT. Results are listed in Table 13 .", "cite_spans": [], "ref_spans": [{"start": 242, "end": 244, "text": "13", "ref_id": "TABREF11"}], "eq_spans": [], "section": "D.4. Further Analysis of the Performance of SMM", "sec_num": null}, {"text": "The results of target tasks with the input size being 128×128 are similar. However, it is observed that for those target tasks with lower resolution (e.g., CIFAR10/100, SVHN, GTSRB), our SMM appears to perform better. This is likely because when a 32×32 image is resized to 384×384, it may become distorted, thus affecting the performance of target tasks. This distortion is especially noticeable on tasks with simple features, such as SVHN and GTSRB. Since VR modifies the input space, it effectively addresses this issue of significant differences in the input image sizes of pre-trained and target tasks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.4. Further Analysis of the Performance of SMM", "sec_num": null}, {"text": "E.2. Advantages of VR in Being Orthogonal to Finetuning-based Methods Since finetuning and reprogramming are orthogonal because finetuning modifies the model while reprogramming modifies the input and output spaces. Input VR can also be combined with finetuning-based methods. In this section, we will add the input VR module (i.e, using SMM as an example) to finetuning-based methods and analyze the performance gain. A widely-used method -finetuning the fully connected layer (named 'Finetuning-FC') -is employed as the baseline method.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.4. Further Analysis of the Performance of SMM", "sec_num": null}, {"text": "Using ResNet-50 as the well-trained model, we add our SMM input VR module to 'Finetuning-FC' to demonstrate the effectiveness of our module.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.4. Further Analysis of the Performance of SMM", "sec_num": null}, {"text": "Results are shown in Tabel 14. Utilizing our module achieves an average accuracy of about 4% higher than solely finetuning the fully connected layers. Conclusively, input VR can be attached to finetuning-based methods to improve performance.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.4. Further Analysis of the Performance of SMM", "sec_num": null}, {"text": "This part includes a conclusion of the strengths and weaknesses of Input VR, compared with finetuning-based methods.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.3. Strengths and Weaknesses of Input Reprogramming in Visual Tasks", "sec_num": null}, {"text": "• The parameter numbers of VR tend to be negligible considering the size of well-trained models. Besides, the parameter numbers in VR are solely determined by the size of a single input image, independent of well-trained models, and remain fixed as the well-trained model size grows.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.3.1. STRENGTHS", "sec_num": null}, {"text": "• VR is suitable for all well-trained models, regardless of the architecture, whereas finetuning-based methods are usually designed for a specific architecture (e.g., LoRA is specifically designed for ViT).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.3.1. STRENGTHS", "sec_num": null}, {"text": "• VR improves the performance of the target task by altering the input and output space, and analyzing these changes may help understand why the model can also perform well in the target domain.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.3.1. STRENGTHS", "sec_num": null}, {"text": "• By changing the input and output spaces while fixing the well-trained model, VR avoids practical issues such as catastrophic forgetting (i.e., the well-trained model may lose previously learned representations when being finetuned for new tasks).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.3.1. STRENGTHS", "sec_num": null}, {"text": "• VR can be attached to most mainstream finetuning methods to further improve performance.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.3.1. STRENGTHS", "sec_num": null}, {"text": "• In future research, VR could also utilize the well-trained model as a black box. This approach might prove useful for re-purposing models that only offer an application programming interface.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.3.1. STRENGTHS", "sec_num": null}, {"text": "Our Method -ResNet50 • When target tasks are more challenging than the tasks well-trained models have been trained on, merely adjusting the input space may not be sufficient for satisfied performance. This poses a challenge for VR.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Original Image Our Method -ResNet18", "sec_num": null}, {"text": "• For better performance approaching re-training or fully finetuning, integrating VR with other finetuning methods appears necessary (e.g., VR may be combined with finetuning the fully connected layer). How to train the combined model more effectively remains a task for future research.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Original Image Our Method -ResNet18", "sec_num": null}, {"text": "Figure 13 -23 show sample images of the VR results of SMM on 11 datasets. These figures show that (1) our VR method does not alter the input space heavily; it only adds noise within a limited range, which ensures that the original images remain intact;", "cite_spans": [], "ref_spans": [{"start": 7, "end": 9, "text": "13", "ref_id": "FIGREF8"}], "eq_spans": [], "section": "F. Additional Visualization Results", "sec_num": null}, {"text": "(2) the more different the target domain is (e.g., GTSRB and SVHN), the more pronounced the noise pattern will be;", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F. Additional Visualization Results", "sec_num": null}, {"text": "(3) on datasets that prefer VR to be a narrow padding-sized watermark, SMM will convergence to a similar situation, that is, the noise at the outer frame of the images is much greater than that inside the images (e.g., UCF101, Food101, OxfordPets and SUN397).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F. Additional Visualization Results", "sec_num": null}, {"text": "Our Method -ResNet50 ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Original Image Our Method -ResNet18", "sec_num": null}, {"text": "School of Computing and Information Systems, The University of", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Melbourne 2 Information Systems Technology and Design Pillar, Singapore University of Technology and Design. Corre-", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "The excess risk is equal to the sum of approximation error and estimation error(<PERSON><PERSON>, 2014).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "CYC and FL are supported by the Australian Research Council (ARC) with grant number DE240101089, and FL is also supported by the ARC with grant number DP230101540 and the NSF&CSIRO Responsible AI program with grant number 2303037. JZQ is supported by ARC with grant number DP240101006. This research is also supported by The University of Melbourne's Research Computing Services and the Petascale Campus Initiative. We sincerely appreciate the time and dedication of the reviewers in carefully reviewing our manuscript.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "Training and Testing Accuracy -CIFAR10, ViT-B32 Training and Testing Accuracy -CIFAR100, ViT-B32 Training and Testing Accuracy -SVHN, ViT-B32 Training and Testing Accuracy -GTSRB, ViT-B32 Training and Testing Accuracy -Food101, ViT-B32 Training and Testing Accuracy -Flowers102, ViT-B32 Training and Testing Accuracy -EuroSAT, ViT-B32 Accuracy (%) Accuracy (%) Accuracy (%) Accuracy (%) Accuracy (%) Accuracy (%) Accuracy (%)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "funding", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none of which we feel must be specifically highlighted here. The task of VR is to reuse the fixed, well-trained model toward a target task. As shown in Figure 7 , the VR module is added before the pre-trained model into the input space. To gap the difference between the source label and target label, an output mapping function without parameters is also used, taking a source label as the input and outputting a target label. Therefore, regardless of the architecture, a well-trained model on the source dataset can be transferred to the target task without editing. [0, 100, 145] 0.01 0.1 0.001 1  FLOWERS102 256 [0, 100, 145] 0.01 0.1 0 ", "cite_spans": [], "ref_spans": [{"start": 300, "end": 301, "text": "7", "ref_id": null}, {"start": 710, "end": 713, "text": "[0,", "ref_id": null}, {"start": 714, "end": 718, "text": "100,", "ref_id": null}, {"start": 719, "end": 760, "text": "145] 0.01 0.1 0.001 1  FLOWERS102 256 [0,", "ref_id": null}, {"start": 761, "end": 765, "text": "100,", "ref_id": null}, {"start": 766, "end": 781, "text": "145] 0.01 0.1 0", "ref_id": null}], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "As mentioned before, and as shown in Appendix A.1, input VR is agnostic of the output label mapping method. Thus, our SMM can be applied to different output label methods other than Ilm. Experimental results are presented in Table 10 .Our method improves the performance of all output mapping methods. In most cases, the worse the output mapping method is, the more pronounced the improvement of SMM will be. When there is sufficient training data (e.g., GTSRB, SVHN, CIFAR10 and Food101), adding SMM can compensate for the worse-performing label mapping methods. With SMM, these methods also produce competitive results.", "cite_spans": [], "ref_spans": [{"start": 231, "end": 233, "text": "10", "ref_id": null}], "eq_spans": [], "section": "D.1. Applying SMM with Different f out", "sec_num": null}, {"text": "Figure 11 shows the training accuracy and loss throughout learning iterations using ResNet-18 as the pre-trained backbone. We see that our SMM yields a higher training accuracy and lower loss for most cases.When using a more sophisticated pre-trained network, e.g., ViT, as is shown in Figure 12 , the training accuracy without SMM may meet with or even exceed that of using SMM. However, this appears to be a case of over-fitting, where training accuracy is approaching 1 and test accuracy is still low without using SMM.In general, for smaller classifiers such as ResNet-18, adding our model helps better reduce training loss and improve accuracy, while for more sophisticated classifiers such as ViT-B32 where the training accuracy is already high, adding our However, when f mask is enlarged with increased number of parameters, the additional estimation error of F smm (f ′ P ) may no longer be negligible and will impact the excess risk. The relationship between the number of parameters in f mask and the estimation error is influenced by various factors, including the specific target tasks, the volume of training data, the size of well-trained models, and the design of our generation model, etc. Through experiments, we will be able to estimate when the number of parameters begins to impact estimation error, potentially leading to over-fitting. For instance, in Table 11 , we employ our generation model f mask on the EuroSAT dataset, with ResNet-18 being the well-trained model. By progressively doubling the number of intermediate channels while maintaining the architecture of f mask , we investigate how the model size affects performance.Through the results of Table 11 , we come to the following conclusions: (1) As the number of parameters continues to increase, although the training accuracy slowly increases, the test accuracy may even decrease, implying that the estimation error becomes more and more noticeable. (2) Under this situation (i.e., EuroSAT, ResNet-18), when the size of f mask is close to the same order of magnitude as the well-trained model, the estimation error should not be overlooked. (3) A larger model with the best test accuracy may not be optimal because of too many parameters. Our work strikes a balance between the number of parameters and test accuracy.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 9, "text": "11", "ref_id": null}, {"start": 293, "end": 295, "text": "12", "ref_id": null}, {"start": 1381, "end": 1383, "text": "11", "ref_id": null}, {"start": 1685, "end": 1687, "text": "11", "ref_id": null}], "eq_spans": [], "section": "D.2. Analysis of Learning Curves", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Exploring visual prompts for adapting large-scale models", "authors": [{"first": "H", "middle": [], "last": "Bahng", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Isola", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2203.17274"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, S., and <PERSON><PERSON>, P. Exploring visual prompts for adapting large-scale models. arXiv preprint arXiv:2203.17274, 2022.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Efficient equivariant transfer learning from pretrained models", "authors": [{"first": "S", "middle": [], "last": "Basu", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": ["R"], "last": "<PERSON><PERSON>-<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": ["R"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, L. R. Efficient equivariant transfer learning from pretrained models. In NeurIPS, 2023.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Food-101-mining discriminative components with random forests", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, L. Food-101- mining discriminative components with random forests. In ECCV, 2014.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Understanding and improving visual prompting: A labelmapping perspective", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Un- derstanding and improving visual prompting: A label- mapping perspective. In CVPR, 2023.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Model reprogramming: Resource-efficient cross-domain machine learning", "authors": [{"first": "P.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2202.10629"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>.-<PERSON>. Model reprogramming: Resource-efficient cross-domain machine learning. arXiv preprint arXiv:2202.10629, 2022.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Describing textures in the wild", "authors": [{"first": "M", "middle": [], "last": "Cimpoi", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Kokkinos", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Describing textures in the wild. In CVPR, 2014.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Imagenet: A large-scale hierarchical image database", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L.-J", "middle": [], "last": "Li", "suffix": ""}, {"first": "K", "middle": [], "last": "Li", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, L. Imagenet: A large-scale hierarchical image database. In CVPR, 2009.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "A", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "X", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.11929"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint arXiv:2010.11929, 2020.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Adversarial reprogramming of neural networks", "authors": [{"first": "G", "middle": ["F"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Goodfellow", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "In ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, J. Ad- versarial reprogramming of neural networks. In ICLR, 2018.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Warp: Word-level adversarial reprogramming", "authors": [{"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "May", "suffix": ""}], "year": 2021, "venue": "ACL-IJCNLP", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>: Word-level adversarial reprogramming. In ACL-IJCNLP, 2021.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Eˆ2vpt: An effective and efficient approach for visual prompt tuning", "authors": [{"first": "C", "middle": [], "last": "Han", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Qi", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.13770"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, D<PERSON> Eˆ2vpt: An effective and efficient approach for visual prompt tuning. arXiv preprint arXiv:2307.13770, 2023.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Deep residual learning for image recognition", "authors": [{"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ren", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}], "year": 2016, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. Deep residual learning for image recognition. In CVPR, 2016.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Eurosat: A novel dataset and deep learning benchmark for land use and land cover classification", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Bischke", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, D. Eurosat: A novel dataset and deep learning benchmark for land use and land cover classification. IEEE Journal of Se- lected Topics in Applied Earth Observations and Remote Sensing, 2019.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Detection of traffic signs in real-world images: The german traffic sign detection benchmark", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Stallkamp", "suffix": ""}, {"first": "J", "middle": [], "last": "Sal<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Schl<PERSON>sing", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, M<PERSON>, and <PERSON><PERSON>, C. Detection of traffic signs in real-world images: The german traffic sign detection benchmark. In IJCNN, 2013.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Lora: Low-rank adaptation of large language models", "authors": [{"first": "E", "middle": ["J"], "last": "Hu", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Lora: Low-rank adaptation of large language models. In International Conference on Learn- ing Representations, 2021.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Low-resource music genre classification with crossmodal neural model reprogramming", "authors": [{"first": "Y.-N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C.-H", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "P.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICASSP", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, A. Low-resource music genre classification with cross- modal neural model reprogramming. In ICASSP, 2023.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Visual prompt tuning", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B.-C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Belongie", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S.-N", "middle": [], "last": "Lim", "suffix": ""}], "year": 2022, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, C., <PERSON>, S., <PERSON>, B., and Lim, S.-N. Visual prompt tuning. In ECCV, 2022.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "An introduction to computational learning theory", "authors": [{"first": "M", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "U", "middle": [], "last": "Vazirani", "suffix": ""}], "year": 1994, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON><PERSON>, U. An introduction to computa- tional learning theory. MIT press, 1994.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Multi-modal prompt learning", "authors": [{"first": "M", "middle": ["U"], "last": "Khattak", "suffix": ""}, {"first": "H", "middle": [], "last": "Rasheed", "suffix": ""}, {"first": "M", "middle": [], "last": "Maaz", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Maple", "suffix": ""}], "year": 2023, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, F. S. Maple: Multi-modal prompt learning. In CVPR, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Three towers: Flexible contrastive learning with pretrained image models", "authors": [{"first": "J", "middle": [], "last": "Kossen", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Jenatton", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.16999"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, R., and <PERSON><PERSON><PERSON><PERSON>, E. Three towers: Flexible contrastive learning with pretrained image models. arXiv preprint arXiv:2305.16999, 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "3d object representations for fine-grained categorization", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "ICCV workshops", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, L. 3d object representations for fine-grained categorization. In ICCV workshops, 2013.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Learning multiple layers of features from tiny images", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Learning multiple layers of features from tiny images. Master's thesis, University of Tront, 2009.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "An interactive journey into machine learning", "authors": [{"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> An interactive journey into machine learning, 2014. URL https://mlweb.loria.fr/book/ en/estimationapproximationerrors.html.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Equivariant adaptation of large pre-trained models", "authors": [{"first": "A", "middle": ["K"], "last": "Mondal", "suffix": ""}, {"first": "S", "middle": ["S"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S.-O", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.01647"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, S. S., <PERSON>, S.-O<PERSON>, <PERSON>, S., and <PERSON><PERSON><PERSON>, S. Equivariant adaptation of large pre-trained models. arXiv preprint arXiv:2310.01647, 2023.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Adversarial reprogramming of text classification neural networks", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1809.01829"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, F. Adversarial reprogramming of text classification neural networks. arXiv preprint arXiv:1809.01829, 2018.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Cross-modal adversarial reprogramming", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, F., and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>. Cross-modal adversarial reprogramming. In WACV, 2022.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Automated flower classification over a large number of classes", "authors": [{"first": "M.-<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2008, "venue": "Indian Conference on Computer Vision, Graphics & Image Processing", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Automated flower classi- fication over a large number of classes. In Indian Confer- ence on Computer Vision, Graphics & Image Processing, 2008.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Cats and dogs", "authors": [{"first": "O", "middle": ["M"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Jawahar", "suffix": ""}], "year": 2012, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, C. Cats and dogs. In CVPR, 2012.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Learning transferable visual models from natural language supervision", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Hall<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S., <PERSON>, G<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Learning transferable visual models from natural language supervision. In ICML, 2021.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Estimating the bayes risk from sample data", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1995, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. Estimating the bayes risk from sample data. In NeurIPS, 1995.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "A dataset of 101 human actions classes from videos in the wild", "authors": [{"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["R"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2012, "venue": "", "volume": "101", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1212.0402"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, M. Ucf101: A dataset of 101 human actions classes from videos in the wild. arXiv preprint arXiv:1212.0402, 2012.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Transfer learning without knowing: Reprogramming black-box machine learning models with scarce data and limited resources", "authors": [{"first": "Y.-Y", "middle": [], "last": "Tsai", "suffix": ""}, {"first": "P.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON>.-Y. Transfer learning without knowing: Reprogramming black-box machine learning models with scarce data and limited resources. In ICML, 2020.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "AutoVP: an automated visual prompting framework and benchmark", "authors": [{"first": "H.-A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, T.-Y. AutoVP: an automated visual prompting framework and benchmark. In ICLR, 2024.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Visualizing data using t-sne", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2008, "venue": "Journal of Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. Visualizing data using t-sne. Journal of Machine Learning Research, 2008.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Watermarking for out-of-distribution detection", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Han", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, B. Watermarking for out-of-distribution detection. NeurIPS, 2022.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Wikipedia contributors. Bilinear interpolation -Wikipedia, the free encyclopedia", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Sun", "suffix": ""}, {"first": "W", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Transhp", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.06385"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>: Image classification with hierarchical prompting. arXiv preprint arXiv:2304.06385, 2023. Wikipedia contributors. Bilinear interpolation - Wikipedia, the free encyclopedia, 2023. URL https://en.wikipedia.org/w/index.php? title=Bilinear_interpolation&oldid= 1170546721.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Sun database: Large-scale scene recognition from abbey to zoo", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Oliva", "suffix": ""}, {"first": "A", "middle": [], "last": "Torralba", "suffix": ""}], "year": 2010, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>liva, A., and Torralba, A. Sun database: Large-scale scene recognition from abbey to zoo. In CVPR, 2010.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Voice2series: Reprogramming acoustic models for time series classification", "authors": [{"first": "C.-H", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "Y.-Y", "middle": [], "last": "Tsai", "suffix": ""}, {"first": "P.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>, P.<PERSON>Y. Voice2series: Reprogramming acoustic models for time series classifi- cation. In ICML, 2021.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "From english to more languages: Parameter-efficient model reprogramming for cross-lingual speech recognition", "authors": [{"first": "C.-H", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": ["N"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICASSP", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> From english to more languages: Parameter-efficient model reprogramming for cross-lingual speech recognition. In ICASSP, 2023.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Neural model reprogramming with similarity based mapping for low-resource spoken command recognition. In INTERSPEECH, 2023. <PERSON><PERSON>, N. Reading digits in natural images with unsupervised feature learning", "authors": [{"first": "H", "middle": [], "last": "Yen", "suffix": ""}, {"first": "P.-J", "middle": [], "last": "Ku", "suffix": ""}, {"first": "C.-H", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Hu", "suffix": ""}, {"first": "S", "middle": ["M"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2011, "venue": "NIPS Workshop", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, Y. Neural model reprogramming with similarity based mapping for low-resource spoken command recognition. In INTERSPEECH, 2023. <PERSON><PERSON>, <PERSON>. Reading digits in natural images with unsuper- vised feature learning. In NIPS Workshop, 2011.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Fairness reprogramming. NeurIPS", "authors": [{"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Fan", "suffix": ""}, {"first": "Q", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Fairness reprogramming. NeurIPS, 2022.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Conditional prompt learning for vision-language models", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Conditional prompt learning for vision-language models. In CVPR, 2022a.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Learning to prompt for vision-language models. International Journal of Computer Vision", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> to prompt for vision-language models. International Jour- nal of Computer Vision, 2022b.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Low-rank adaptation is better than fine-tuning for medical image diagnosis", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Melo", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.08236"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Low-rank adaptation is better than fine-tuning for medical image diagnosis. arXiv preprint arXiv:2311.08236, 2023.", "links": null}}, "ref_entries": {"FIGREF0": {"text": "Figure 1. Drawback of shared masks over individual images. We demonstrate the use of watermarking (<PERSON> et al., 2022), a representative VR method, to re-purpose an ImageNet-pretrained classifier for the OxfordPets dataset, with different shared masks (full, medium, and narrow) in VR. An evaluation of classification confidence across three cat images -Sphynx, Abyssinian, and Bengal -indicates a sample-specific mask preference: Sphynx with medium, Abyssinian with full, and Bengal with narrow. It shows that different masks are needed for individual images.", "uris": null, "num": null, "fig_num": "1", "type_str": "figure"}, "FIGREF1": {"text": "Figure 2. Drawback of shared masks in the statistical view. Optimal learning methods like finetuning usually result in loss decreases for all samples (see the blue part). But when applying the same mask in reprogramming, part of the loss changes are observed to be positive (see the red part) according to the distribution of [final loss -initial loss], which means the training loss for some samples even rises.", "uris": null, "num": null, "fig_num": "2", "type_str": "figure"}, "FIGREF2": {"text": "Figure 3. Comparison between (a) existing methods and (b) our method. Previous padding-based reprogramming adds zeros around the target image, while resizing-based reprogramming adjusts image dimensions to fit the required input size. Both methods use a pre-determined shared mask to indicate the valid location of pattern δ.Our method, on the other hand, takes a more dynamic and tailored approach. We resize each target image and apply a different three-channel mask accordingly, driven by a lightweight f mask with an interpolation up-scaling module, allowing for more variability in individual samples.", "uris": null, "num": null, "fig_num": "3", "type_str": "figure"}, "FIGREF3": {"text": "Figure 4. Comparative results of different patch sizes (2 l ). ResNet-18 is used as the pre-trained model as an example.", "uris": null, "num": null, "fig_num": "45", "type_str": "figure"}, "FIGREF4": {"text": "Figure 6. TSNE visualization results of the feature space on (a) SVHN and (b) EuroSAT datasets. ResNet-18 is used as the pretrained model as an example.", "uris": null, "num": null, "fig_num": "6", "type_str": "figure"}, "FIGREF5": {"text": "Figure 9. Architecture of the 6-layer mask generator designed for ViT", "uris": null, "num": null, "fig_num": "910", "type_str": "figure"}, "FIGREF6": {"text": "store matched labels, initialize f in (•|θ) to be an identity function (θ ← 0) 4: # Compute frequency distribution d 5: Use Algorithm 2 to obtain d 6: # Compute output mapping f Flm out 7: while size of Y P sub is not |Y T | do 8: Find the maximum d y P ,y T in d Flm out (y P ) ← y T # Update the label mapping function 11: d y P ,t ← 0 for t = 1, 2, ..., |Y T | # Avoiding illegal assignment to the injective function 12: d s,y T ← 0 for s = 1, 2, ..., |Y P | 13: end while Il<PERSON> evolves with iterations, being an improved version of Flm. As is shown in Algorithm 4, before training the reprogramming pattern θ in each epoch, <PERSON><PERSON> updates the one-to-one mapping from Y P to Y T with the training samples incorporating the current pattern, iteratively until convergence. B. Additional Theoretical Proof B.1. Proof of Theorem 4.2", "uris": null, "num": null, "fig_num": null, "type_str": "figure"}, "FIGREF7": {"text": ") ← y T # Update the label mapping function for iteration j 12: d y P ,t ← 0 for t = 1, 2, ..., |Y T | # Avoiding illegal assignment to the injective function 13: d s,y T ← 0 for s = 1, 2, ..., |Y P | 14:", "uris": null, "num": null, "fig_num": null, "type_str": "figure"}, "FIGREF8": {"text": "Figure 13. Original Images and Visual Reprogramming Results on CIFAR10Original ImageOur Method -ResNet18 Our Method -ResNet50", "uris": null, "num": null, "fig_num": "13", "type_str": "figure"}, "FIGREF9": {"text": "Figure 15. Original Images and Visual Reprogramming Results on SVHN", "uris": null, "num": null, "fig_num": "151821", "type_str": "figure"}, "TABREF0": {"text": "T i )} n i=1be the observations of D T (i.e., the training set in the target task). Meanwhile, we have a pre-trained model f P : X P → Y P , where X P ⊆ R dP and Y P (s.t. |Y T | ≤ |Y P |, with the label space of the pre-trained task being larger than that of the target task) represent the data and label spaces used for training f P . Then, in model reprogramming, the training objective can be formulated as", "content": "<table><tr><td>min θ∈Θ,ω∈Ω</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF1": {"text": "Performance Comparison of Different Input Reprogramming Methods on Pre-trained ResNet (Mean % ± Std %, the average results across all datasets are highlighted in grey)", "content": "<table><tr><td>PRE-TRAINED</td><td/><td colspan=\"3\">RESNET-18 (IMAGENET-1K)</td><td/><td/><td colspan=\"3\">RESNET-50 (IMAGENET-1K)</td><td/></tr><tr><td>METHODS</td><td>PAD</td><td colspan=\"2\">NARROW MEDIUM</td><td>FULL</td><td>OURS</td><td>PAD</td><td colspan=\"2\">NARROW MEDIUM</td><td>FULL</td><td>OURS</td></tr><tr><td>CIFAR10</td><td colspan=\"2\">65.5 ±0.1 68.6 ±2.8</td><td colspan=\"4\">68.8 ±1.1 68.9 ±0.4 72.8 ±0.7 76.6±0.3</td><td>77.4±0.5</td><td>77.8+0.2</td><td colspan=\"2\">79.3±0.3 81.4±0.6</td></tr><tr><td>CIFAR100</td><td>24.8±0.1</td><td>36.9±0.6</td><td>34.9±0.2</td><td>33.8±0.2</td><td>39.4±0.6</td><td>38.9±0.3</td><td>42.5±0.2</td><td>43.8±0.2</td><td colspan=\"2\">47.2±0.1 49.0±0.2</td></tr><tr><td>SVHN</td><td>75.2±0.2</td><td>58.5±1.1</td><td>71.1±1.0</td><td>78.3±0.3</td><td>84.4±2.0</td><td>75.8±0.4</td><td>59.1±1.3</td><td>71.5±0.8</td><td colspan=\"2\">79.5±0.5 82.6±2.0</td></tr><tr><td>GTSRB</td><td>52.0±1.2</td><td>46.1±1.5</td><td>56.4±1.0</td><td>76.8±0.9</td><td>80.4±1.2</td><td>52.5±1.4</td><td>38.9±1.3</td><td>52.6±1.3</td><td colspan=\"2\">76.5±1.3 78.2±1.1</td></tr><tr><td>FLOWERS102</td><td>27.9±0.7</td><td>22.1±0.1</td><td>22.6±0.5</td><td>23.2±0.5</td><td>38.7±0.7</td><td>24.6±0.6</td><td>19.9±0.6</td><td>20.9±0.6</td><td colspan=\"2\">22.6±0.1 35.9±0.5</td></tr><tr><td>DTD</td><td>35.3±0.9</td><td>33.1±1.3</td><td>31.7±0.5</td><td>29.0±0.7</td><td>33.6±0.4</td><td>40.5±0.5</td><td>37.8±0.7</td><td>38.4±0.2</td><td colspan=\"2\">34.7±1.3 41.1±1.1</td></tr><tr><td>UCF101</td><td>23.9±0.5</td><td>27.2±0.9</td><td>26.1±0.3</td><td>24.4±0.9</td><td>28.7±0.8</td><td>34.6±0.2</td><td>38.4±0.2</td><td>37.2±0.2</td><td colspan=\"2\">35.2±0.2 38.9±0.5</td></tr><tr><td>FOOD101</td><td>14.8±0.2</td><td>14.0±0.1</td><td>14.4±0.3</td><td>13.2±0.1</td><td>17.5±0.1</td><td>17.0±0.3</td><td>18.3±0.2</td><td>18.3±0.2</td><td colspan=\"2\">16.7±0.2 19.8±0.0</td></tr><tr><td>SUN397</td><td>13.0±0.2</td><td>15.3±0.1</td><td>14.2±0.1</td><td>13.4±0.2</td><td>16.0±0.3</td><td>20.3±0.2</td><td>22.0±0.1</td><td>21.5±0.1</td><td colspan=\"2\">21.1±0.1 22.9±0.0</td></tr><tr><td>EUROSAT</td><td>85.2±0.6</td><td>82.8±0.4</td><td>83.8±0.5</td><td>84.3±0.5</td><td>92.2±0.2</td><td>83.6±0.7</td><td>83.7±0.4</td><td>85.8±0.1</td><td colspan=\"2\">86.9±0.3 92.0±0.6</td></tr><tr><td>OXFORDPETS</td><td>65.4±0.7</td><td>73.7±0.2</td><td>71.4±0.2</td><td>70.0±0.6</td><td>74.1±0.4</td><td>76.2±0.6</td><td>76.4±0.3</td><td>75.6±0.3</td><td colspan=\"2\">73.4±0.3 78.1±0.2</td></tr><tr><td>AVERAGE</td><td>43.91</td><td>43.48</td><td>45.04</td><td>46.85</td><td>52.53</td><td>49.15</td><td>46.76</td><td>49.39</td><td>52.10</td><td>56.35</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF2": {"text": "Performance Comparison of Different Input Reprogramming Methods on Pre-trained ViT (Mean %, the average results are highlighted in grey)", "content": "<table><tr><td>PRE-TRAINED</td><td/><td colspan=\"3\">VIT-B32 (IMAGENET-1K)</td><td/></tr><tr><td>METHOD</td><td>PAD</td><td colspan=\"3\">NARROW MEDIUM FULL</td><td>OURS</td></tr><tr><td>CIFAR10</td><td>62.4</td><td>96.6</td><td>96.5</td><td>95.8</td><td>97.4</td></tr><tr><td>CIFAR100</td><td>31.6</td><td>74.4</td><td>75.3</td><td>75.0</td><td>82.6</td></tr><tr><td>SVHN</td><td>80.2</td><td>85.0</td><td>87.4</td><td>87.8</td><td>89.7</td></tr><tr><td>GTSRB</td><td>62.3</td><td>57.8</td><td>68.6</td><td>75.5</td><td>80.5</td></tr><tr><td>FLOWERS102</td><td>57.3</td><td>55.3</td><td>56.6</td><td>55.9</td><td>79.1</td></tr><tr><td>DTD</td><td>43.7</td><td>37.3</td><td>38.5</td><td>37.7</td><td>45.6</td></tr><tr><td>UCF101</td><td>33.6</td><td>44.5</td><td>44.8</td><td>40.9</td><td>42.6</td></tr><tr><td>FOOD101</td><td>37.4</td><td>47.3</td><td>48.6</td><td>49.4</td><td>64.8</td></tr><tr><td>SUN397</td><td>21.8</td><td>29.0</td><td>29.4</td><td>28.8</td><td>36.7</td></tr><tr><td>EUROSAT</td><td>95.9</td><td>90.9</td><td>90.9</td><td>89.1</td><td>93.5</td></tr><tr><td colspan=\"2\">OXFORDPETS 57.6</td><td>82.5</td><td>81.0</td><td>75.3</td><td>83.8</td></tr><tr><td>AVERAGE</td><td>53.1</td><td>63.7</td><td>65.2</td><td>64.7</td><td>72.4</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF3": {"text": "Ablation Studies (Mean % ± Std %, with ResNet-18 as an example, and the average results are highlighted in grey)", "content": "<table><tr><td/><td>ONLY δ</td><td>ONLY fmask</td><td>SINGLE-mask f s CHANNEL</td><td>OURS</td></tr><tr><td>CIFAR10</td><td colspan=\"2\">68.9±0.4 59.0±1.6</td><td>72.6±2.6</td><td>72.8±0.7</td></tr><tr><td>CIFAR100</td><td colspan=\"2\">33.8±0.2 32.1±0.3</td><td>38.0±0.6</td><td>39.4±0.6</td></tr><tr><td>SVHN</td><td colspan=\"2\">78.3±0.3 51.1±3.1</td><td>78.4±0.2</td><td>84.4±2.0</td></tr><tr><td>GTSRB</td><td colspan=\"2\">76.8±0.9 55.7±1.2</td><td>70.7±0.8</td><td>80.4±1.2</td></tr><tr><td colspan=\"3\">FLOWERS102 23.2±0.5 32.2±0.4</td><td>30.2±0.4</td><td>38.7±0.7</td></tr><tr><td>DTD</td><td colspan=\"2\">29.0±0.7 27.2±0.5</td><td>32.7±0.5</td><td>33.6±0.4</td></tr><tr><td>UCF101</td><td colspan=\"2\">24.4±0.9 25.7±0.3</td><td>28.0±0.3</td><td>28.7±0.8</td></tr><tr><td>FOOD101</td><td colspan=\"2\">13.2±0.1 13.3±0.1</td><td>15.8±0.1</td><td>17.5±0.1</td></tr><tr><td>SUN397</td><td colspan=\"2\">13.4±0.2 10.5±0.1</td><td>15.9±0.1</td><td>16.0±0.3</td></tr><tr><td>EUROSAT</td><td colspan=\"2\">84.3±0.5 89.2±0.9</td><td>90.6±0.5</td><td>92.2±0.2</td></tr><tr><td colspan=\"3\">OXFORDPETS 70.0±0.6 72.5±0.3</td><td>73.8±0.6</td><td>74.1±0.4</td></tr><tr><td>AVERAGE</td><td>46.85</td><td>42.59</td><td>49.70</td><td>52.53</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF4": {"text": "Statistics of Mask Generator Parameter Size", "content": "<table><tr><td>PRE-TRAINED</td><td>INPUT IMAGE SIZE</td><td>f mask CNN LAYERS</td><td>EXTRA PARAMETERS OF OUR f mask</td><td>OUR EXTRA PARAMETERS ÷ REPROGRAMMING PARAMETERS</td><td>OUR EXTRA PARAMETERS ÷ PRE-TRAINED MODEL PARAMETERS</td></tr><tr><td>RESNET-18</td><td>224×224×2</td><td>5</td><td>26,499</td><td>17.60%</td><td>0.23%</td></tr><tr><td>RESNET-50</td><td>224×224×3</td><td>5</td><td>26,499</td><td>17.60%</td><td>0.10%</td></tr><tr><td>VIT-B32</td><td>384×384×3</td><td>6</td><td>102,339</td><td>23.13%</td><td>0.12%</td></tr><tr><td colspan=\"3\">A.3. Advantage of Patch-wise Interpolation</td><td/><td/><td/></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF5": {"text": "Comparison of Patch-wise Interpolation and Other Interpolation Methods", "content": "<table><tr><td/><td/><td>BILINEAR INTERPOLATION</td><td>BICUBIC INTERPOLATION</td><td>OURS</td></tr><tr><td/><td>NUMBER OF</td><td/><td/></tr><tr><td/><td>PIXEL</td><td>0.602</td><td>2.408</td><td>0.151</td></tr><tr><td>RESNET -18/50</td><td>ACCESSES (1E6)</td><td/><td/></tr><tr><td/><td>TIME PER BATCH (S)</td><td>0.062±0.001</td><td>0.195±0.013</td><td>0.026±0.004</td></tr><tr><td/><td>REQUIRE BACKPROPAGATION</td><td>YES</td><td>YES</td><td>NO</td></tr><tr><td/><td>NUMBER OF</td><td/><td/></tr><tr><td/><td>PIXEL</td><td>1.769</td><td>7.078</td><td>0.442</td></tr><tr><td>VIT -B32</td><td>ACCESSES (1E6)</td><td/><td/></tr><tr><td/><td>TIME PER BATCH (S)</td><td>0.165±0.009</td><td>0.486±0.026</td><td>0.069±0.004</td></tr><tr><td/><td>REQUIRE BACKPROPAGATION</td><td>YES</td><td>YES</td><td>NO</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF6": {"text": "Detailed", "content": "<table><tr><td>Dataset Information</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF7": {"text": "Tuning Initial Learning Rate and Learning Rate Decay Using CIFAR10 and ViT-B32 (Accucracy %)", "content": "<table><tr><td>γ|α</td><td>0.1</td><td>0.01</td><td>0.001</td><td>0.0001</td></tr><tr><td>1</td><td colspan=\"4\">0.9542 0.9577 0.9745 0.9734</td></tr><tr><td colspan=\"5\">0.1 0.9516 0.9572 0.9738 0.9727</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF8": {"text": "Results", "content": "<table><tr><td colspan=\"4\">on UCF101 with Different Training Parameters (using ViT-B32)</td></tr><tr><td/><td>α</td><td>γ</td><td>SMM ACCURACY (%)</td></tr><tr><td>UNIFIED LEARNING PARAMETERS</td><td>0.001</td><td>1</td><td>42.6</td></tr><tr><td>SPECIFIC LEARNING PARAMETERS</td><td>0.01</td><td>0.1</td><td>49.9</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF9": {"text": "Training and Testing Accuracy with Enlarged f mask (using EuroSAT,", "content": "<table><tr><td>f mask</td><td colspan=\"2\">SMALL MEDIUM (OURS)</td><td>LARGE</td><td>X-LARGE</td><td>XX-LARGE</td><td>XXX-LARGE</td></tr><tr><td>PARAMETERS</td><td>7203</td><td>26499</td><td>101379</td><td>396291</td><td>1566723</td><td>6230019</td></tr><tr><td>TRAINING ACCURACY (%)</td><td>94.9</td><td>96.2</td><td>96.4</td><td>97.3</td><td>97.7</td><td>98.1</td></tr><tr><td>TESTING ACCURACY (%)</td><td>91.7</td><td>92.2</td><td>92.2</td><td>93.1</td><td>93.5</td><td>93.2</td></tr><tr><td colspan=\"3\">D.3. More Discussion about the Estimation Error</td><td/><td/><td/><td/></tr><tr><td colspan=\"7\">A higher estimation error generally implies an increased risk of model over-fitting to the training data. This observation can</td></tr><tr><td colspan=\"7\">be corroborated by comparing the disparities in training and testing performance. For instance, as depicted in Figure</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF10": {"text": "An Ineffective Case of Input Reprogramming -StanfordCars (Mean % ± Std %)SMM on An Ineffective Case of Input Reprogramming. All input visual reprogramming methods seem ineffective on fine-grained recognition tasks where subtle appearance differences should be detected. As shown in Table12, in the classification of StanfordCars, where 196 types of cars are to be classified, the accuracy of all input VR methods is below 10 %, indicating the failure of VR methods in this fine-grained recognition tasks. Adding our SMM module will not improve performance when VR methods fail.", "content": "<table><tr><td>METHOD</td><td>PAD</td><td colspan=\"2\">NARROW MEDIUM</td><td>FULL</td><td>OURS</td></tr><tr><td colspan=\"2\">RESNET-18 4.5±0.1</td><td>3.6±0.1</td><td>3.6±0.1</td><td colspan=\"2\">3.4±0.1 2.9±0.2</td></tr><tr><td colspan=\"2\">RESNET-50 4.7±0.2</td><td>4.7±0.1</td><td>4.7±0.2</td><td colspan=\"2\">4.6±0.1 3.0±0.6</td></tr><tr><td>VIT-B32</td><td>4.7±0.6</td><td>7.7±0.2</td><td>8.3±0.3</td><td colspan=\"2\">5.0±0.0 4.8±0.9</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF11": {"text": "Performance of Finetuning (LoRA)  and SMM Facing Target Tasks with Different Input Image Sizes (Accyracy %, using ViT-L with a 384×384 input as the well-trained model, average results are calculated on all four tasks with 32×32 inputs and all seven tasks with 128×128 inputs)", "content": "<table><tr><td/><td>EXTRA PARAMETERS</td><td colspan=\"4\">CIFAR10 CIFAR100 SVHN GTSRB</td><td>AVERAGE (32×32)</td><td>AVERAGE (128×128)</td></tr><tr><td>FINETUNING-LORA</td><td>0.60M</td><td>95.9</td><td>83.6</td><td>65.3</td><td>66.6</td><td>77.9</td><td>83.4</td></tr><tr><td>OUR SMM</td><td>0.54M</td><td>97.4</td><td>87.3</td><td>91.0</td><td>84.2</td><td>90.0</td><td>83.5</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF12": {"text": "Performance of Finetuning the Fully-Connected Layers (Finetuning-FC) without or with our SMM Module (Accuracy %, using", "content": "<table><tr><td>ResNet-50 as the well-trained model)</td><td/><td/><td/><td/><td/><td/></tr><tr><td/><td colspan=\"2\">CIFAR10 CIFAR100</td><td>SVHN</td><td>GTSRB</td><td>FLOWERS102</td><td>DTD</td></tr><tr><td>FINETUNING-FC</td><td>90.1</td><td>70.7</td><td>63.5</td><td>77.8</td><td>90.9</td><td>67.6</td></tr><tr><td>FINETUNING-FC + OUR SMM</td><td>91.2</td><td>72.4</td><td>86.9</td><td>85.2</td><td>90.9</td><td>68.2</td></tr><tr><td/><td>UCF101</td><td>FOOD101</td><td colspan=\"4\">SUN397 EUROSAT OXFORDPETS AVERAGE</td></tr><tr><td>FINETUNING-FC</td><td>70.8</td><td>57.6</td><td>53.5</td><td>95.7</td><td>90.4</td><td>75.3</td></tr><tr><td>FINETUNING-FC + OUR SMM</td><td>72.0</td><td>59.6</td><td>57.9</td><td>95.8</td><td>90.6</td><td>79.2</td></tr></table>", "html": null, "num": null, "type_str": "table"}}}}