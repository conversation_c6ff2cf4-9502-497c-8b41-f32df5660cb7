{"paper_id": "GOUB", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:31:30.244623Z"}, "title": "Image Restoration Through Generalized Ornstein-Uhlenbeck Bridge", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Zheng<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Pengxu", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Diffusion models exhibit powerful generative capabilities enabling noise mapping to data via reverse stochastic differential equations. However, in image restoration, the focus is on the mapping relationship from low-quality to high-quality images. Regarding this issue, we introduce the Generalized Ornstein-Uhlenbeck Bridge (GOUB) model. By leveraging the natural mean-reverting property of the generalized OU process and further eliminating the variance of its steady-state distribution through the Doo<PERSON>'s h-transform, we achieve diffusion mappings from point to point enabling the recovery of high-quality images from low-quality ones. Moreover, we unravel the fundamental mathematical essence shared by various bridge models, all of which are special instances of GOUB and empirically demonstrate the optimality of our proposed models. Additionally, we present the corresponding Mean-ODE model adept at capturing both pixel-level details and structural perceptions. Experimental outcomes showcase the state-of-the-art performance achieved by both models across diverse tasks, including inpainting, deraining, and super-resolution. Code is available at https: //github.com/Hammour-steak/GOUB.", "pdf_parse": {"paper_id": "GOUB", "_pdf_hash": "", "abstract": [{"text": "Diffusion models exhibit powerful generative capabilities enabling noise mapping to data via reverse stochastic differential equations. However, in image restoration, the focus is on the mapping relationship from low-quality to high-quality images. Regarding this issue, we introduce the Generalized Ornstein-Uhlenbeck Bridge (GOUB) model. By leveraging the natural mean-reverting property of the generalized OU process and further eliminating the variance of its steady-state distribution through the Doo<PERSON>'s h-transform, we achieve diffusion mappings from point to point enabling the recovery of high-quality images from low-quality ones. Moreover, we unravel the fundamental mathematical essence shared by various bridge models, all of which are special instances of GOUB and empirically demonstrate the optimality of our proposed models. Additionally, we present the corresponding Mean-ODE model adept at capturing both pixel-level details and structural perceptions. Experimental outcomes showcase the state-of-the-art performance achieved by both models across diverse tasks, including inpainting, deraining, and super-resolution. Code is available at https: //github.com/Hammour-steak/GOUB.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Image restoration involves the restoring of high-quality (HQ) images from their low-quality (LQ) version (Banham & Katsaggelos, 1997; <PERSON> et al., 1988; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2023b) , which is often characterized as an ill-posed inverse problem due to the loss of crucial information during the degradation from high-quality images to low-quality images. It encompasses a suite of classical tasks, including image deraining (<PERSON>, 2017; <PERSON> et al., 2020; <PERSON> et al., 2022) , denoising (<PERSON> et al., 2018a; <PERSON> et al., 2022; <PERSON><PERSON> & Cho, 2022; <PERSON> et al., 2023a) , deblurring (<PERSON> et al., 2007; <PERSON> et al., 2023) , inpainting (<PERSON> et al., 2023; <PERSON> et al., 2023b) , and super-resolution (<PERSON> et al., 2015; <PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) , among others.", "cite_spans": [{"start": 105, "end": 133, "text": "(Banham & Katsaggelos, 1997;", "ref_id": "BIBREF5"}, {"start": 134, "end": 152, "text": "<PERSON> et al., 1988;", "ref_id": "BIBREF76"}, {"start": 153, "end": 172, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF36"}, {"start": 173, "end": 191, "text": "<PERSON><PERSON> et al., 2023b)", "ref_id": null}, {"start": 434, "end": 455, "text": "(<PERSON> & Patel, 2017;", "ref_id": "BIBREF71"}, {"start": 456, "end": 474, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF65"}, {"start": 475, "end": 493, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF63"}, {"start": 506, "end": 527, "text": "(<PERSON> et al., 2018a;", "ref_id": null}, {"start": 528, "end": 544, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF34"}, {"start": 545, "end": 561, "text": "<PERSON><PERSON> & Cho, 2022;", "ref_id": "BIBREF51"}, {"start": 562, "end": 582, "text": "<PERSON> et al., 2023a)", "ref_id": null}, {"start": 596, "end": 615, "text": "(<PERSON> et al., 2007;", "ref_id": "BIBREF66"}, {"start": 616, "end": 634, "text": "Kong et al., 2023)", "ref_id": "BIBREF32"}, {"start": 648, "end": 667, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF26"}, {"start": 668, "end": 688, "text": "<PERSON> et al., 2023b)", "ref_id": null}, {"start": 712, "end": 731, "text": "(<PERSON> et al., 2015;", "ref_id": "BIBREF17"}, {"start": 732, "end": 752, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF67"}, {"start": 753, "end": 770, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF62"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Diffusion models (<PERSON><PERSON><PERSON><PERSON> et al., 2015; <PERSON> et al., 2020; Song & Ermon, 2019; <PERSON> et al., 2021b; <PERSON><PERSON><PERSON> et al., 2022) have also been applied to image restoration, yielding favorable results (<PERSON> <PERSON>, 2021; <PERSON> et al., 2023; <PERSON> et al., 2022; <PERSON> et al., 2024) . They mainly follow the standard forward process, diffusing images to pure noise and using low-quality images as conditions to facilitate the generation process of high-quality images (<PERSON> & Ni<PERSON>l, 2021; <PERSON>, 2021; <PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2022b; a; <PERSON> et al., 2023) . However, these approaches require the integration of substantial prior knowledge specific to each task such as degradation matrices, limiting their universality.", "cite_spans": [{"start": 17, "end": 46, "text": "(<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF52"}, {"start": 47, "end": 63, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF25"}, {"start": 64, "end": 83, "text": "<PERSON> & Ermon, 2019;", "ref_id": "BIBREF53"}, {"start": 84, "end": 103, "text": "<PERSON> et al., 2021b;", "ref_id": null}, {"start": 104, "end": 124, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF28"}, {"start": 197, "end": 218, "text": "(Ho & Sal<PERSON>s, 2021;", "ref_id": "BIBREF24"}, {"start": 219, "end": 237, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF60"}, {"start": 238, "end": 254, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF56"}, {"start": 255, "end": 272, "text": "<PERSON> et al., 2024)", "ref_id": "BIBREF50"}, {"start": 458, "end": 483, "text": "(Dhariwal & Nichol, 2021;", "ref_id": null}, {"start": 484, "end": 504, "text": "Ho & Salimans, 2021;", "ref_id": "BIBREF24"}, {"start": 505, "end": 524, "text": "<PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF29"}, {"start": 525, "end": 546, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF47"}, {"start": 547, "end": 566, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF30"}, {"start": 567, "end": 587, "text": "<PERSON> et al., 2022b;", "ref_id": null}, {"start": 588, "end": 590, "text": "a;", "ref_id": null}, {"start": 591, "end": 609, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF60"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Furthermore, some studies have attempted to establish a point-to-point mapping from low-quality to high-quality images, learning the general degradation and restoration process and thus circumventing the need for additional prior information for modeling specific tasks (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2024) . In terms of diffusion models, this mapping can be realized through the bridge (<PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2023a) , a stochastic process with fixed starting and ending points. By assigning high-quality and low-quality images to the starting and ending points, and initiating with the low-quality images, high-quality images can be obtained by applying the reverse diffusion process, thereby enabling image restoration. However, some bridge models face challenges in learning likelihoods (<PERSON> et al., 2022) , necessitating reliance on cumbersome iterative approximation methods (<PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON> et al., 2024) , which pose significant constraints in practical applications; others do not consider the selection of diffusion process and ignore the optimality of diffusion process (<PERSON> et al., 2023a; <PERSON> et al., 2023; <PERSON> et al., 2024) , thus may introducing unnecessary costs and limiting the performance of the model. This paper proposed a novel image restoration bridge model, the Generalized Ornstein-Uhlenbeck Bridge (GOUB), de-picted in Figure 1 . Owing to the mean-reverting properties of the Generalized Ornstein-Uhlenbeck (GOU) process, it gradually diffuses the HQ image into a noisy LQ state (denoted as x T + λϵ in Figure 1 ). By applying Doob's h-transform on GOU, we modify the diffusion process to eliminate noise on x T to directly bridge the HQ image and its LQ counterpart. The model initiates a point-to-point forward diffusion process and learns its reverse through maximum likelihood estimation, thereby ensuring it can restore a low-quality image to the corresponding high-quality image avoiding the limitation of generality and costly iterative approximation. Our main contributions can be summarized as follows:", "cite_spans": [{"start": 270, "end": 289, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF7"}, {"start": 290, "end": 307, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF13"}, {"start": 308, "end": 325, "text": "<PERSON> et al., 2024)", "ref_id": "BIBREF33"}, {"start": 406, "end": 424, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF38"}, {"start": 425, "end": 441, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF56"}, {"start": 442, "end": 460, "text": "<PERSON> et al., 2023a)", "ref_id": null}, {"start": 834, "end": 852, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF38"}, {"start": 924, "end": 949, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF14"}, {"start": 950, "end": 966, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF56"}, {"start": 967, "end": 984, "text": "<PERSON> et al., 2024)", "ref_id": "BIBREF50"}, {"start": 1154, "end": 1173, "text": "(<PERSON> et al., 2023a;", "ref_id": null}, {"start": 1174, "end": 1190, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF35"}, {"start": 1191, "end": 1209, "text": "<PERSON> et al., 2024)", "ref_id": null}], "ref_spans": [{"start": 1424, "end": 1425, "text": "1", "ref_id": "FIGREF0"}, {"start": 1608, "end": 1609, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We introduce a novel image restoration bridge model GOUB which eliminates variance of the ending point on the GOU process, directly connecting the high and low-quality images and is particularly expressive in deep visual features and diversity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• Benefiting from the distinctive features of the parameterization mechanism, we introduce the corresponding Mean-ODE model, demonstrating a strong ability to capture pixel-level details and structural perceptions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We uncover the mathematical essence of several bridge models, all of which are special cases of the GOUB, and empirically demonstrate the optimality of our proposed models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• Our model has achieved state-of-the-art results on numerous image restoration tasks, such as inpainting, deraining, and super-resolution.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "The score-based diffusion model (<PERSON><PERSON><PERSON> et al., 2015; <PERSON> et al., 2020; <PERSON> et al., 2021b ) is a category of generative model that seamlessly transitions data into noise via a diffusion process and generates samples by learning and adapting the reverse process (<PERSON>, 1982) . Assuming a dataset consists of n dimensional independent identically distributed (i.i.d.) samples, following an unknown distribution denoted by p(x 0 ). The time-dependent forward process of the diffusion model can be described by the following SDE:", "cite_spans": [{"start": 32, "end": 61, "text": "(<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF52"}, {"start": 62, "end": 78, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF25"}, {"start": 79, "end": 97, "text": "<PERSON> et al., 2021b", "ref_id": null}, {"start": 269, "end": 285, "text": "(<PERSON>, 1982)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx t = f (x t , t) dt + g t dw t ,", "eq_num": "(1)"}], "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "where f : R n → R n is the drift coefficient, g t : R → R is the scalar diffusion coefficient and w t denotes the standard Brownian motion. Typically, p(x 0 ) evolves over time t from 0 to a sufficiently large T into p(x T ) through the SDE, such that p(x T ) will approximate a standard Gaussian distribution p prior (x). Meanwhile, the forward SDE has a corresponding reverse time SDE (<PERSON>, 1982) whose closed form is given by:", "cite_spans": [{"start": 387, "end": 403, "text": "(<PERSON>, 1982)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "dx t = f (x t , t) -g 2 t ∇ xt log p(x t ) dt + g t dw t . (2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "Starting from time T , p(x T ) can progressively transform to p(x 0 ) by traversing the trajectory of the reverse SDE.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "The score ∇ xt log p(x t ) can generally be parameterized as s θ (x t , t) and employ conditional score matching (<PERSON>, 2011) as the loss function for training:", "cite_spans": [{"start": 113, "end": 128, "text": "(<PERSON>, 2011)", "ref_id": "BIBREF59"}], "ref_spans": [], "eq_spans": [], "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L = 1 2 T 0 Ex t λ (t) ∇x t log p (xt) -s θ (xt, t) 2 dt ∝ 1 2 T 0 Ex 0 ,x t λ (t) ∇x t log p (xt | x0) -s θ (xt, t) 2 dt,", "eq_num": "(3)"}], "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "where λ(t) serves as a weighting function, and if selected as g 2 t that yields a more optimal upper bound on the negative log-likelihood (<PERSON> et al., 2021a) . The second line is actually the most commonly used, as the conditional probability p(x t | x 0 ) is generally accessible. Ultimately, one can sample x T from the prior distribution p(x T ) ≈ p prior (x) and obtain the x 0 through the numerical solution of Equation (2) via iterative steps, thereby completing the generation process.", "cite_spans": [{"start": 138, "end": 158, "text": "(<PERSON> et al., 2021a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "The Generalized Ornstein-Uhlenbeck (GOU) process is the time-varying OU process (<PERSON>, 1988) . It is a stationary Gaussian-Markov process, whose marginal distribution gradually tends towards a stable mean and variance over time. The GOU process is generally defined as follows:", "cite_spans": [{"start": 80, "end": 93, "text": "(<PERSON>, 1988)", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx t = θ t (µ -x t ) dt + g t dw t ,", "eq_num": "(4)"}], "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "where µ is a given state vector, θ t denotes a scalar drift coefficient and g t represents the diffusion coefficient. At the same time, we require θ t , g t to satisfy the specified relationship 2λ 2 = g 2 t /θ t , where λ 2 is a given constant scalar. As a result, its transition probability possesses a closed-form analytical solution:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "p (x t | x s ) = N ( ms:t , σ2 s:t I) = N µ + (x s -µ) e -θs:t , g 2 t 2θ t", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "1 -e -2 θs:t I , θs:t = t s θ z dz.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "(5)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "A simple proof is provided in Appendix C. For the sake of simplicity in subsequent representations, we denote θ0:t and σ0:t as θt and σt respectively. Consequently, p(x t ) will steadily converge towards a Gaussian distribution with the mean of µ and the variance of λ 2 as time t progresses meaning that it exhibits the mean-reverting property. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "<PERSON><PERSON>'s h-transform (Särkkä & Solin, 2019 ) is a mathematical technique applied to stochastic processes. It involves transforming the original process by incorporating a specific h-function into the drift term of the SDE, modifying the process to pass through a predetermined terminal point. More precisely, given the SDE (1), if it is desired to pass through the given fixed point x T at t = T , an additional drift term must be incorporated into the original SDE:", "cite_spans": [{"start": 19, "end": 40, "text": "(Särkkä & Solin, 2019", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": "2.3."}, {"text": "dx t = f (x t , t) + g 2 t h(x t , t,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": "2.3."}, {"text": "x T , T ) dt + g t dw t , (6) where h(x t , t, x T , T ) = ∇ xt log p(x T | x t ) and x 0 starts from p (x 0 | x T ). A simple proof can be found in Appendix D. In comparison to (1), the marginal distribution of ( 6) is conditioned on x T , with its forward conditional probability density given by p(x t | x 0 , x T ) satisfying the forward Kolmogorov equation that is defined by (6). Intuitively, p(x T | x 0 , x T ) = 1 at t = T , ensuring that the SDE invariably passes through the specified point x T for any initial state x 0 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": "2.3."}, {"text": "The GOU process (4) is characterized by mean-reverting properties that if we consider the initial state x 0 to represent a high-quality image and the corresponding low-quality image x T = µ as the final condition, then the high-quality image will gradually converge to a Gaussian distribution with the low-quality image as its mean and a stable variance λ 2 . This naturally connects some information between high and low-quality images, offering an inherent advantage in image restoration. However, the initial state of the reverse process necessitates the artificial addition of noise to lowquality images, resulting in certain information loss and thus affecting the performance (<PERSON><PERSON> et al., 2023a) .", "cite_spans": [{"start": 682, "end": 701, "text": "(<PERSON><PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "GOUB", "sec_num": "3."}, {"text": "In actuality, we are more focused on the connections between points (<PERSON> et al., 2022; <PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2024) in image restoration. Coincidentally, the <PERSON><PERSON>'s h-transform technique can modify an SDE such that it passes through a specified x T at terminal time T . Accordingly, it is crucial to note that the application of the h-transform to the GOU process effectively eliminates the impact of terminal noise, directly bridging a point-to-point relationship between highquality and low-quality images.", "cite_spans": [{"start": 68, "end": 86, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF38"}, {"start": 87, "end": 111, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF14"}, {"start": 112, "end": 128, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF56"}, {"start": 129, "end": 145, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF35"}, {"start": 146, "end": 164, "text": "<PERSON> et al., 2024)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "GOUB", "sec_num": "3."}, {"text": "Applying the h-transform, we can readily derive the forward process of the GOUB, leading to the following proposition: Proposition 3.1. Let x t be a finite random variable describing by the given generalized Ornstein-Uhlenbeck process (4), suppose x T = µ, the evolution of its marginal distribution p(x t | x T ) satisfies the following SDE:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "dx t = θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t )dt + g t dw t . (7)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "Additionally, the forward transition p(x t | x 0 , x T ) is given by:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "p(x t | x 0 , x T ) = N ( m′ t , σ′2 t I), m′ t = e -θt σ2 t:T σ2 T x 0 + 1 -e -θt σ2 t:T σ2 T + e -2 θt:T σ2 t σ2 T x T σ′2 t = σ2 t σ2 t:T σ2 T (8)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "The derivation of the proposition is provided in the Appendix A.1. With Proposition 3.1, there is no need to perform multi-step forward iteration using the SDE; instead, we can directly use its closed-form solution for one-step forward sampling.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "Similarly, applying the previous SDE theory enables us to easily derive the reverse process, which leads to the following Proposition 3.2:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "Proposition 3.2. The reverse SDE of equation ( 7) has a marginal distribution p(x t | x T ), and is given by:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx t = θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t ) -g 2 t ∇ xt log p(x t | x T ) dt + g t dw t ,", "eq_num": "(9)"}], "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "and exists a probability flow ODE:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx t = θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t ) - 1 2 g 2 t ∇ xt log p(x t | x T ) dt.", "eq_num": "(10)"}], "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "We are capable of initiating from a low-quality image x T and proceeding to utilize Euler sampling solving the reverse SDE or ODE for restoration purposes.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "The score term ∇ xt log p(x t | x T ) can be parameterized by a neural network s θ (x t , x T , t) and can be estimated using the loss function (3). Unfortunately, training the score function for SDEs generally presents a significant challenge. Nevertheless, since the analytical form of GOUB is directly obtainable, we will introduce the use of maximum likelihood for training, which yields a more stable loss function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "We first discretize the continuous time interval [0, T ] into N sufficiently fine-grained intervals in a reasonable manner, denoted as {x t } t∈[0,N ] , x N = x T . We are concerned with maximizing the log-likelihood, which leads us to the following proposition:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "Proposition 3.3. Let x t be a finite random variable describing by the given generalized Ornstein-<PERSON>beck process (4), for a fixed x T , the expectation of log-likelihood", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "E p(x0) [log p θ (x 0 | x T )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "] possesses an Evidence Lower Bound (ELBO):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ELBO = E p(x 0 ) E p(x 1 |x 0 ) [log p θ (x0 | x1, xT )] - T t=2 E p(x t |x 0 ) [KL (p (xt-1 | x0, xt, xT ) ||p θ (xt-1 | xt, xT ))]", "eq_num": "(11)"}], "section": "Training object", "sec_num": "3.2."}, {"text": "Assuming", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "p θ (x t-1 | x t , x T ) is a Gaussian distribution with a constant variance N (µ θ,t-1 , σ 2 θ,t-1 I)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": ", maximizing the ELBO is equivalent to minimizing:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L = E t,x0,xt,x T 1 2σ 2 θ,t-1 ∥µ t-1 -µ θ,t-1 ∥ 2 , (", "eq_num": "12"}], "section": "Training object", "sec_num": "3.2."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "where µ t-1 represents the mean of p (x t-1 | x 0 , x t , x T ): µt-1 = 1 σ′2 t σ′2 t-1 (xt -bxT )a + (σ ′2 t -σ′2 t-1 a 2 ) m′ t ,", "eq_num": "(13)"}], "section": "Training object", "sec_num": "3.2."}, {"text": "where,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "a = e -θt-1:t σ2 t:T σ2 t-1:T , b = 1 σ2 T (1 -e -θt )σ 2 t:T + e -2 θt:T σ2 t -(1 -e -θt-1 )σ 2 t-1:T + e -2 θt-1:T σ2 t-1 a", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "The derivation of the proposition is provided in the Appendix A.2. With Proposition 3.3, we can easily construct the training objective. In this work, we try to parameterized µ θ,t-1 from differential of SDE which can be derived from equation ( 9):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x t-1 =x t -θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t ) + g 2 t ∇ xt log p(x t | x T ) -g t ϵ t ,", "eq_num": "(14)"}], "section": "Training object", "sec_num": "3.2."}, {"text": "where ϵ t ∼ N (0, dtI), therefore:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "µ θ,t-1 =x t -θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t ) + g 2 t ∇ xt log p θ (x t | x T ), σ θ,t-1 =g t . (15)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "Inspired by conditional score matching, we can parameterize noise as ϵ θ (x t , x T , t), thus the score ∇ xt log p θ (x t | x T ) can be represented as -ϵ θ (x t , x T , t)/σ ′ t . In addition, during our empirical research, we found that utilizing L1 loss yields enhanced image reconstruction outcomes (<PERSON>, 2004; <PERSON><PERSON> et al., 2009) . This approach enables the model to learn pixel-level details more easily, resulting in markedly improved visual quality. Therefore, the final training object is:", "cite_spans": [{"start": 304, "end": 331, "text": "(Boyd & Vandenberghe, 2004;", "ref_id": "BIBREF6"}, {"start": 332, "end": 352, "text": "<PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L = E t,x0,xt,x T 1 2g 2 t 1 σ′2 t σ′2 t-1 (x t -bx T )a +(σ ′2 t -σ′2 t-1 a 2 ) m′ t -x t + θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t ) + g 2 t σ′ t ϵ θ (x t , x T , t)", "eq_num": "(16)"}], "section": "Training object", "sec_num": "3.2."}, {"text": "Consequently, if we obtain the optimal ϵ * θ (x t , x T , t), we can compute the score", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "∇ xt log p(x t | x T ) ≈ -ϵ * θ (x t ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "x T , t)/σ ′ t for reverse process. Starting from a lowquality image x T , we can recover x 0 by using Equation ( 9) to perform reverse iteration.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training object", "sec_num": "3.2."}, {"text": "Unlike normal diffusion models, our parameterization of the mean µ θ,t-1 is derived from the differential of SDE which effectively combines the characteristics of discrete diffusion models and continuous score-based generative models. In the reverse process, the value of each sampling step will approximated to the true mean during training. Therefore, we propose a Mean-ODE model, which omits the Brownian drift term:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Mean-ODE", "sec_num": "3.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx t = θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t ) -g 2 t ∇ xt log p(x t | x T ) dt,", "eq_num": "(17)"}], "section": "Mean-ODE", "sec_num": "3.3."}, {"text": "To simplify the expression, we use GOUB to represent the GOUB (SDE) sampling model and Mean-ODE to represent the GOUB (Mean-ODE) sampling model. Our following experiments have demonstrated that the Mean-ODE is more effective than the corresponding Score-ODE at capturing the pixel details and structural perceptions of images, playing a pivotal role in image restoration tasks. Concurrently, the SDE model ( 9) is more focused on deep visual features and diversity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Mean-ODE", "sec_num": "3.3."}, {"text": "We conduct experiments under three popular image restoration tasks: image inpainting, image deraining, and image super-resolution. Four metrics are employed for the model Image Inpainting. Image inpainting involves filling in missing or damaged parts of an image, to restore or enhance the overall visual effect of the image. We have selected the CelebA-HQ 256×256 datasets (<PERSON><PERSON><PERSON> et al., 2018) for both training and testing with 100 thin masks. We compare our models with several current baseline inpainting approaches such as PromptIR (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , DDRM (<PERSON><PERSON> et al., 2022) and IR-SDE (<PERSON><PERSON> et al., 2023a) . The relevant experimental results are shown in the Table 1 and Figure 2. It is observed that the two proposed models achieved stateof-the-art results in their respective areas of strength and also delivered highly competitive outcomes on other metrics. From a visual perspective, our model excels in capturing details such as eyebrows, eyes, and image backgrounds.", "cite_spans": [{"start": 374, "end": 395, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF27"}, {"start": 538, "end": 563, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF45"}, {"start": 571, "end": 591, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF30"}, {"start": 603, "end": 622, "text": "(<PERSON><PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [{"start": 682, "end": 683, "text": "1", "ref_id": "TABREF0"}, {"start": 688, "end": 697, "text": "Figure 2.", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Experiments", "sec_num": "4."}, {"text": "Image Deraining. We have selected the Rain100H datasets (<PERSON> et al., 2017) for our training and testing, which includes 1800 pairs of training data and 100 images for testing. It is important to note that in this task, similar to other deraining models, we present the PSNR and SSIM scores specifically on the Y channel (YCbCr space). We report state-of-the-art approaches for comparison: MPRNet (<PERSON><PERSON><PERSON> et al., 2021) , M3SNet-32 (<PERSON> et al., 2023) , MAXIM (<PERSON> et al., 2022) , MHNet (Gao & Dang, 2023) , IR-SDE (<PERSON><PERSON> et al., 2023a) . The relevant experimental results are shown in the Table 2 and Figure 3 . Similarly, both models achieved SOTA results respectively in the deraining task. Visually, it can be also observed that our model excels in capturing details such as the moon, the sun, and tree branches.", "cite_spans": [{"start": 56, "end": 75, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF64"}, {"start": 397, "end": 417, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF68"}, {"start": 430, "end": 448, "text": "(<PERSON> et al., 2023)", "ref_id": null}, {"start": 457, "end": 474, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF58"}, {"start": 483, "end": 501, "text": "(Gao & <PERSON>, 2023)", "ref_id": null}, {"start": 511, "end": 530, "text": "(<PERSON><PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [{"start": 590, "end": 591, "text": "2", "ref_id": "TABREF1"}, {"start": 603, "end": 604, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Experiments", "sec_num": "4."}, {"text": "Image Super-Resolution. Single image super-resolution aims to recover a higher resolution and clearer version from a low-resolution image. We conducted training and evalua- tion on the DIV2K validation set for 4× upscaling (Agustsson & Timofte, 2017) and all low-resolution images were bicubically rescaled to the same size as their corresponding high-resolution images. To show that our models are in line with the state-of-the-art, we compare to the DDRM (<PERSON><PERSON> et al., 2022) and IR-SDE (<PERSON><PERSON> et al., 2023a) . The relevant experimental results are provided in Table 3 and Figure 4 . As can be seen, our GOUB is superior to benchmarks in various indicators and handles visual details better such as edges and hair.", "cite_spans": [{"start": 457, "end": 477, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF30"}, {"start": 489, "end": 508, "text": "(<PERSON><PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [{"start": 567, "end": 568, "text": "3", "ref_id": "TABREF2"}, {"start": 573, "end": 581, "text": "Figure 4", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Experiments", "sec_num": "4."}, {"text": "Superiority of Mean-ODE. Additionally, we conduct ablation experiments using the corresponding Score-ODE (10) model to demonstrate the superiority of our proposed Mean-ODE model in image restoration. From Table 4 , it is evident that the performance of Mean-ODE is significantly superior to that of the corresponding Score-ODE. This is because the sampling results of each sampling step of Mean-ODE directly approximate the true mean during the training process, as opposed to the parameterized approach such as DDPM, which relies on expectations. Consequently, our proposed Mean-ODE demonstrates better reconstruction effects and is more suitable for image restoration tasks. ", "cite_spans": [], "ref_spans": [{"start": 211, "end": 212, "text": "4", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Experiments", "sec_num": "4."}, {"text": "The <PERSON><PERSON>'s h-transform of the generalized <PERSON><PERSON>-<PERSON> process, also known as the conditional GOU process has been an intriguing topic in previous applied mathematical research (<PERSON><PERSON><PERSON>, 1984; <PERSON><PERSON><PERSON><PERSON> et al., 2003; <PERSON><PERSON> et al., 2021) . On account of the mean-reverting property of the GOU process, applying the h-transform makes it most straightforward to eliminate the variance and drive it towards a Dirac distribution in its steady state which is highly advantageous for its applications in image restoration. In previous research on diffusion models, there has been limited focus on the cases of f or g, and generally used the VE process (<PERSON> et al., 2021b) represented by NCSN (Song & Ermon, 2019) or the VP process (<PERSON> et al., 2021b) represented by DDPM (<PERSON> et al., 2020) .", "cite_spans": [{"start": 183, "end": 199, "text": "(<PERSON><PERSON><PERSON>, 1984;", "ref_id": "BIBREF48"}, {"start": 200, "end": 223, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2003;", "ref_id": "BIBREF8"}, {"start": 224, "end": 242, "text": "<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF22"}, {"start": 651, "end": 671, "text": "(<PERSON> et al., 2021b)", "ref_id": null}, {"start": 692, "end": 712, "text": "(Song & Ermon, 2019)", "ref_id": "BIBREF53"}, {"start": 731, "end": 751, "text": "(<PERSON> et al., 2021b)", "ref_id": null}, {"start": 772, "end": 789, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "Analysis", "sec_num": "5."}, {"text": "In this section, we demonstrate that the mathematical essence of several recent meaningful diffusion bridge models is the same (<PERSON> et al., 2023; <PERSON> et al., 2024; <PERSON> et al., 2023a) and they all represent Brownian bridge (<PERSON>, 2009) models, details are provided in the Appendix B.1. Then, we also found that the VE and VP processes are special cases of GOU, leading to the following proposition:", "cite_spans": [{"start": 127, "end": 144, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF35"}, {"start": 145, "end": 163, "text": "<PERSON> et al., 2024;", "ref_id": null}, {"start": 164, "end": 182, "text": "<PERSON> et al., 2023a)", "ref_id": null}, {"start": 222, "end": 234, "text": "(<PERSON>, 2009)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Analysis", "sec_num": "5."}, {"text": "Proposition 5.1. For a given GOU process (4), there exists relationships:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Analysis", "sec_num": "5."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "lim θ t →0 GOU = VE lim µ→0,λ→1 GOU = VP", "eq_num": "(18)"}], "section": "Analysis", "sec_num": "5."}, {"text": "Details are provided in the Appendix B.2. Therefore, we conduct experiments on VE Bridge (VEB) (<PERSON> et al., 2023; <PERSON> et al., 2024; <PERSON> et al., 2023a) and VP Bridge (VPB) (<PERSON> et al., 2024) to demonstrate the optimality of our proposed GOUB model in image restoration. We keep all the model hyperparameters consistent and results are shown in Table 5 and Figure 5. It can be seen that under the same configuration of model hyperparameters, the performance of the GOUB is notably superior to the other two types of bridge models, which demonstrates the optimality of GOUB and also highlights the importance of the choice of diffusion process in diffusion models.", "cite_spans": [{"start": 95, "end": 112, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF35"}, {"start": 113, "end": 131, "text": "<PERSON> et al., 2024;", "ref_id": null}, {"start": 132, "end": 150, "text": "<PERSON> et al., 2023a)", "ref_id": null}, {"start": 171, "end": 190, "text": "(<PERSON> et al., 2024)", "ref_id": null}], "ref_spans": [{"start": 350, "end": 351, "text": "5", "ref_id": "TABREF4"}, {"start": 356, "end": 365, "text": "Figure 5.", "ref_id": "TABREF4"}], "eq_spans": [], "section": "Analysis", "sec_num": "5."}, {"text": "Conditional Generation. As previously highlighted, in the work of image restoration using diffusion models, the focus of some research has predominantly been on using low- quality images as conditional inputs y to guide the generation process. They (<PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2022a; b; 2023; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) all endeavor to solve or approximate the classifier log ∇ xt p(y | x t ), necessitating the incorporation of additional prior knowledge to model specific degradation processes which both complex and lacking in universality.", "cite_spans": [{"start": 249, "end": 269, "text": "(<PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF29"}, {"start": 270, "end": 291, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF47"}, {"start": 292, "end": 311, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF30"}, {"start": 312, "end": 332, "text": "<PERSON> et al., 2022a;", "ref_id": null}, {"start": 333, "end": 335, "text": "b;", "ref_id": "BIBREF4"}, {"start": 336, "end": 341, "text": "2023;", "ref_id": null}, {"start": 342, "end": 360, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF74"}, {"start": 361, "end": 381, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF43"}, {"start": 382, "end": 400, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "Related Works", "sec_num": "6."}, {"text": "Diffusion Bridge. This segment of work obviates the need for prior knowledge, constructing a diffusion bridge model from high-quality to low-quality images, thereby learning the degradation process. The previously mentioned approach (<PERSON> et al., 2022; <PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON> et al., 2023a; <PERSON> et al., 2024; <PERSON> et al., 2023; <PERSON> et al., 2024; <PERSON><PERSON><PERSON> et al., 2023) fall into this class and are characterized by the issues of significant computational expense in solution seeking and also not the optimal model framework. Additionally, some models of flow category (<PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023b; <PERSON> et al., 2023; Albergo & Vanden-<PERSON>den, 2023; Delbracio & Milanfar, 2023 ) also belong to the diffusion bridge models and face the similar issue.", "cite_spans": [{"start": 233, "end": 251, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF38"}, {"start": 252, "end": 276, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF14"}, {"start": 277, "end": 293, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF56"}, {"start": 294, "end": 312, "text": "<PERSON> et al., 2023a;", "ref_id": null}, {"start": 313, "end": 330, "text": "<PERSON> et al., 2024;", "ref_id": "BIBREF50"}, {"start": 331, "end": 347, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF35"}, {"start": 348, "end": 366, "text": "<PERSON> et al., 2024;", "ref_id": null}, {"start": 367, "end": 388, "text": "<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF3"}, {"start": 588, "end": 609, "text": "(<PERSON><PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 610, "end": 628, "text": "<PERSON> et al., 2023b;", "ref_id": null}, {"start": 629, "end": 647, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF57"}, {"start": 648, "end": 679, "text": "Albergo & Vanden-Eijnden, 2023;", "ref_id": "BIBREF3"}, {"start": 680, "end": 706, "text": "Delbracio & Milanfar, 2023", "ref_id": "BIBREF15"}], "ref_spans": [], "eq_spans": [], "section": "Related Works", "sec_num": "6."}, {"text": "In this paper, we introduced the Generalized Ornstein-Uhlenbeck Bridge (GOUB) model, a diffusion bridge model that applies the Doob's h-transform to the GOU process. This model can address general image restoration tasks without the need for specific prior knowledge. Furthermore, we have uncovered the mathematical essence of several bridge models and empirically demonstrated the optimality of our proposed model. In addition, considering our unique mean parameterization mechanism, we proposed the Mean-ODE model. Experimental results indicate that both models achieve state-of-the-art results in their respective areas of strength on various tasks, including inpainting, deraining, and super-resolution. We believe that the exploration of diffusion process and bridge models holds significant importance not only in the field of image restoration but also in advancing the study of generative diffusion models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "7."}, {"text": "A.1. Proof of Proposition 3.1 Proposition 3.1. Let x t be a finite random variable describing by the given generalized Ornstein-Uh<PERSON>beck process (4), suppose x T = µ, the evolution of its marginal distribution p(x t | x T ) satisfies the following SDE:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx t = θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t )dt + g t dw t ,", "eq_num": "(7)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "additionally, the forward transition p(x t | x 0 , x T ) is given by:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(x t | x 0 , x T ) = N ( m′ t , σ′2 t I) = N e -θt σ2 t:T σ2 T x 0 + 1 -e -θt σ2 t:T σ2 T + e -2 θt:T σ2 t σ2 T x T , σ2 t σ2 t:T σ2 T I", "eq_num": "(8)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Proof : Based on (5), we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "p (x t | x 0 ) = N x T + (x 0 -x T ) e -θt , σ2 t I (19) p (x T | x t ) = N x T + (x t -x T ) e -θt:T , σ2 t:T I (20) p (x T | x 0 ) = N x T + (x 0 -x T ) e -θT , σ2 T I (21)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Firstly, the h function can be directly compute:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "h(x t , t, x T , T ) = ∇ xt log p(x T | x t ) = -∇ xt (x t -x T ) 2 e -2 θt:T 2σ 2 t:T = (x T -x t ) e -2 θt:T σ2 t:T (22)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Therefore, followed by <PERSON><PERSON>'s h-transform (6), the SDE of marginal distribution p(x t | x T ) satisfied is :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "dx t = f (x t , t) + g 2 t h(x t , t, x T , T ) dt + g t dw t = θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t )dt + g t dw t (23)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Furthermore, we can derive the following transition probability of x t using <PERSON><PERSON>' formula:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "p(x t | x 0 , x T ) = p(x T | x t , x 0 )p(x t | x 0 ) p(x T | x 0 ) = p(x T | x t )p(x t | x 0 ) p(x T | x 0 ) (24)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Since each component is independently and identically distributed (i.i.d), by considering a single dimension, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(x t | x 0 , x T ) ∝ 1 √ 2πσ t σt:T /σ T exp - (x t -[x T + (x 0 -x T ) e -θt ]) 2 2σ 2 t + (x T -[x T + (x t -x T ) e -θt:T ]) 2 2σ 2 t:T = 1 √ 2πσ t σt:T /σ T exp - (x t -[x T + (x 0 -x T ) e -θt ]) 2 2σ 2 t + (x t -x T ) 2 e -2 θt:T 2σ 2 t:T ∝ 1 √ 2πσ t σt:T /σ T exp - 1 2σ 2 t + e -2 θt:T 2σ 2 t:T x 2 t - x T -(x 0 -x T ) e -θt σ2 t + x T e -2 θt:T σ2 t:T x t", "eq_num": "(25)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Notice that:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "1 2σ 2 t + e -2 θt:T 2σ 2 t:T = σ 2 t:T + σ2 t e -2 θt:T 2σ 2 t σ2 t:T = λ 2 (1 -e -2 θt:T ) + (1 -e -2 θt )e -2 θt:T 2σ 2 t σ2 t:T = λ 2 (1 -e -2 θt:T ) + (e -2 θt:T -e -2 θT ) 2σ 2 t σ2 t:T = σ2 T 2σ 2 t σ2 t:T", "eq_num": "(26)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Bringing it back to (25), squaring the terms and reorganizing the equation, we obtain:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(x t | x 0 , x T ) ∝ 1 √ 2πσ t σt:T /σ T exp - σ2 T 2σ 2 t σ2 t:T x 2 t - x T -(x 0 -x T ) e -θt σ2 t + x T e -2 θt:T σ2 t:T x t = 1 √ 2πσ t σt:T /σ T exp -    x 2 t -x T -(x 0 -x T ) e -θt 2σ 2 t:T σ2 T + e -2 θt:T 2σ 2 t σ2 T x T x t 2(σ t σt:T /σ T ) 2    ∝ 1 √ 2πσ t σt:T /σ T exp - x t -e -θt σ2 t:T σ2 T x 0 -1 -e -θt σ2 t:T σ2 T + e -2 θt:T σ2 t σ2 T x T 2 2(σ t σt:T /σ T ) 2 = N e -θt σ2 t:T σ2 T x 0 + 1 -e -θt σ2 t:T σ2 T + e -2 θt:T σ2 t σ2 T x T , σ2 t σ2 t:T σ2 T I", "eq_num": "(27)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "This concludes the proof of the Proposition 3.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "A.2. Proof of Proposition 3.3 Proposition 3.3. Let x t be a finite random variable describing by the given generalized Ornstein-<PERSON>beck process (4), for a fixed x T , the expectation of log-likelihood E p(x0) [log p θ (x 0 | x T )] possesses an Evidence Lower Bound (ELBO):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ELBO = E p(x0) E p(x1|x0) [log p θ (x 0 | x 1 , x T )] - T t=2 KL (p (x t-1 | x 0 , x t , x T ) ||p θ (x t-1 | x t , x T ))", "eq_num": "(11)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Assuming p θ (x t-1 | x t , x T ) is a Gaussian distribution with a constant variance N (µ θ,t-1 , σ 2 θ,t-1 I), maximizing the ELBO is equivalent to minimizing:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L = E t,x0,xt,x T 1 2σ 2 θ,t-1 ∥µ t-1 -µ θ,t-1 ∥ 2 , (", "eq_num": "12"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "where µ t-1 represents the mean of p (x t-1 | x 0 , x t , x T ):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "µ t-1 = 1 σ′2 t σ′2 t-1 (x t -bx T )a + (σ ′2 t -σ′2 t-1 a 2 ) m′ t ,", "eq_num": "(13)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "where,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "a = e -θt-1:t σ2 t:T σ2 t-1:T , b = 1 σ2 T (1 -e -θt )σ 2 t:T + e -2 θt:T σ2 t -(1 -e -θt-1 )σ 2 t-1:T + e -2 θt-1:T σ2 t-1 a", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Proof : Firstly, followed by the theorem in DDPM (<PERSON> et al., 2020) :", "cite_spans": [{"start": 49, "end": 66, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E p(x0) [log p θ (x 0 )] ≥E p(x0) -KL(p(x T | x 0 )||p(x T )) + E p(x1|x0) [log p θ (x 0 | x 1 )] - T t=2 E p(xt|x0) [KL (p (x t-1 | x 0 , x t ) ||p θ (x t-1 | x t ))]", "eq_num": "(28)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Similarly, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E p(x0) [log p θ (x 0 | x T )] ≥ E p(x0) -KL(p(x T | x 0 , x T )||p(x T | x T )) + E p(x1|x0) [log p θ (x 0 | x 1 , x T )] - T t=2 E p(xt|x0) [KL (p (x t-1 | x 0 , x t , x T ) ||p θ (x t-1 | x t , x T ))] = E p(x0) E p(x1|x0) [log p θ (x 0 | x 1 , x T )] - T t=2 E p(xt|x0) [KL (p (x t-1 | x 0 , x t , x T ) ||p θ (x t-1 | x t , x T ))] = ELBO", "eq_num": "(29)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "From <PERSON><PERSON>' formula, we can infer that:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p (x t-1 | x 0 , x t , x T ) = p(x t | x 0 , x t-1 , x T )p(x t-1 | x 0 , x T ) p(x t | x 0 , x T ) = p(x t | x t-1 , x T )p(x t-1 | x 0 , x T ) p(x t | x 0 , x T )", "eq_num": "(30)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Since p(x t-1 | x 0 , x T ) and p(x t | x 0 , x T ) are Gaussian distributions (8), by employing the reparameterization technique:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x t-1 = e -θt-1 σ2 t-1:T σ2 T x 0 + 1 -e -θt-1 σ2 t-1:T σ2 T + e -2 θt-1:T σ2 t-1 σ2 T x T + σ′ t-1 ϵ t-1 = m(t -1)x 0 + n(t -1)x T + σ′ t-1 ϵ t-1 x t = e -θt σ2 t:T σ2 T x 0 + 1 -e -θt σ2 t:T σ2 T + e -2 θt:T σ2 t σ2 T x T + σ′ t ϵ t = m(t)x 0 + n(t)x T + σ′ t ϵ t", "eq_num": "(31)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Therefore,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "x t = m(t) m(t -1) x t-1 + n(t) - m(t) m(t -1) n(t -1) x T + σ′2 t - m(t) 2 m(t -1) 2 σ′2 t-1 ϵ = ax t-1 + [n(t) -an(t -1)] x T + σ′2 t -a 2 σ′2 t-1 ϵ = ax t-1 + bx T + σ′2 t -a 2 σ′2 t-1 ϵ (32) Thus, p(x t | x t-1 , x T ) = N (ax t-1 + bx T , σ′2 t -a 2 σ′2 t-1 I", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": ") is also a Gaussian distribution. Bring it back to equation (30) we can easily obtain :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "µ t-1 = 1 σ′2 t σ′2 t-1 (x t -bx T )a + (σ ′2 t -σ′2 t-1 a 2 ) m′ t ,", "eq_num": "(13)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Accordingly,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "KL (p (x t-1 | x 0 , x t , x T ) ||p θ (x t-1 | x t , x T )) =E p(xt-1|x0,xt,x T )   log 1 √ 2πσt-1 e -(xt-1-µt-1) 2 /2σ 2 t-1 1 √ 2πσ θ,t-1 e -(xt-1-µ θ,t-1 ) 2 /2σ 2 θ,t-1   =E p(xt-1|x0,xt,x T ) log σ θ,t-1 -log σ t-1 -(x t-1 -µ t-1 ) 2 /2σ 2 t-1 + (x t-1 -µ θ,t-1 ) 2 /2σ 2 θ,t-1 = log σ θ,t-1 -log σ t-1 - 1 2 + σ 2 t-1 2σ 2 θ,t-1 + (µ t-1 -µ θ,t-1 ) 2 2σ 2 θ,t-1", "eq_num": "(33)"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Ignoring unlearnable constant, the training object that involves minimizing the negative ELBO is :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L = E t,x0,xt,x T 1 2σ 2 θ,t-1 ∥µ t-1 -µ θ,t-1 ∥ 2 , (", "eq_num": "34"}], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "This concludes the proof of the Proposition 3.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "In this section, we will show the mathematical essence of some other bridge models, some of which are all equivalent. Proposition B.1. The mathematical essence of BBDM (<PERSON> et al., 2023) , DDBM (VE) (<PERSON> et al., 2024) and I 2 SB (<PERSON> et al., 2023a ) are all equivalent to the Brownian bridge.", "cite_spans": [{"start": 168, "end": 185, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF35"}, {"start": 198, "end": 217, "text": "(<PERSON> et al., 2024)", "ref_id": null}, {"start": 229, "end": 247, "text": "(<PERSON> et al., 2023a", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "Proof : Firstly, it is easy to understand that BBDM uses the Brownian bridge as its fundamental model architecture.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "The DDBM (VE) model is derived as the <PERSON><PERSON>'s h-transform of VE-SDE, and we begin by specifying the SDE:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "dx t = dw t (35)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "Its transition probability is given by: p", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(x t | x s ) = N (x s , t -s)", "eq_num": "(36)"}], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "Since, the h-function of SDE ( 35) is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "h(x t , t, x T , T ) = ∇ xt log p(x T | x t ) = x T -x t T -t", "eq_num": "(37)"}], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "Therefore, the <PERSON><PERSON>'s h-transform of ( 35) is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "dx t = x T -x t T -t dt + dw t (38)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "That is the definition of Brownian bridge. Hence, DDBM (VE) is a Brownian bridge model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "Furthermore, the transition kernel of ( 38) is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(x t | x 0 , x T ) = p(x T | x t , x 0 )p(x t | x 0 ) p(x T | x 0 ) = p(x T | x t )p(x t | x 0 ) p(x T | x 0 ) = N (x t , T -t)N (x 0 , t) N (x 0 , T ) = N 1 - t T x 0 + t T x T , t(T -t) T I", "eq_num": "(39)"}], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "This precisely corresponds to the sampling process of I 2 SB, thus confirming that I 2 SB also represents a Brownian bridge.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "This concludes the proof of the Proposition B.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "The following proposition will show us that both VE and VP processes are special cases of GOU process:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "Proposition 5.1. For a given GOU process (4), there exists relationships:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "lim θt→0 GOU = VE lim µ→0,λ→1 GOU = VP", "eq_num": "(18)"}], "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "Proof : It's easy to know:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "lim θt→0 GOU = lim θt→0 {dx t = θ t (µ -x t ) dt + g t dw t } = lim θt→0 {dx t = g t dw t } = VE,", "eq_num": "(40)"}], "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "where g t will be controlled by λ 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "Besides, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "lim µ→0,λ→1 GOU = lim µ→0,λ→1 {dx t = θ t (µ -x t ) dt + g t dw t } = lim µ→0,λ→1 {dx t = θ t µdt -θ t x t dt + g t dw t } = lim µ→0,λ→1 dx t = - 1 2 g 2 t x t dt + g t dw t = VP,", "eq_num": "(41)"}], "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "where g t will be controlled by θ t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "This concludes the proof of the Proposition 5.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "Theorem C.1. For a given GOU process:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. GOU Process", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx t = θ t (µ -x t ) dt + g t dw t (", "eq_num": "4"}], "section": "C. GOU Process", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. GOU Process", "sec_num": null}, {"text": "where µ is a given state vector, θ t denotes a scalar drift coefficient and g t represents the diffusion coefficient. It possesses a closed-form analytical solution: Therefore:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. GOU Process", "sec_num": null}, {"text": "p (x t | x s ) = N µ + (x s -µ) e -θs:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. GOU Process", "sec_num": null}, {"text": "x t e θtx s e θs = e θt -e θs µ + N 0, λ 2 e 2 θt -e 2 θs I", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. GOU Process", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x t = µ + (x s -µ) e -θs:t + N 0, g 2 t 2θ t 1 -e -2 θs:t I", "eq_num": "(46)"}], "section": "C. GOU Process", "sec_num": null}, {"text": "This concludes the proof of the Theorem C.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. GOU Process", "sec_num": null}, {"text": "Theorem D.1. For a given SDE:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx t = f (x t , t) dt + g t dw t , x 0 ∼ p (x 0 ) ,", "eq_num": "(1)"}], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "For a fixed x T , the evolution of conditional probability p(x t | x T ) follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx t = f (x t , t) + g 2 t h(x t , t, x T , T ) dt + g t dw t , x 0 ∼ p (x 0 | x T ) ,", "eq_num": "(6)"}], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "where h(x t , t, x T , T ) = ∇ xt log p(x T | x t ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "Proof : p(x t | x 0 ) satisfies Kolmogorov Forward Equation (KFE) also called Fokker-Planck equation (<PERSON><PERSON> & Risken, 1996) :", "cite_spans": [{"start": 101, "end": 124, "text": "(Risken & Risken, 1996)", "ref_id": "BIBREF46"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂ ∂t p(x t | x 0 ) = -∇ xt • [f (x t , t)p(x t | x 0 )] + 1 2 g 2 t ∇ xt • ∇ xt p(x t | x 0 )", "eq_num": "(47)"}], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "Similarly, p(x T | x t ) satisfies <PERSON><PERSON><PERSON><PERSON> Backward Equation (KBE) (<PERSON><PERSON> & Risken, 1996) :", "cite_spans": [{"start": 70, "end": 93, "text": "(Risken & Risken, 1996)", "ref_id": "BIBREF46"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "- ∂ ∂t p(x T | x t ) = f (x t , t) • ∇ xt p(x T | x t ) + 1 2 g 2 t ∇ xt • ∇ xt p(x T | x t )", "eq_num": "(48)"}], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "Using <PERSON><PERSON>' rule, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(x t | x 0 , x T ) = p(x T | x t , x 0 )p(x t | x 0 ) p(x T | x 0 ) = p(x T | x t )p(x t | x 0 ) p(x T | x 0 )", "eq_num": "(49)"}], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "Therefore, the derivative of conditional transition probability p(x t | x 0 , x T ) with time follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂ ∂t p(x t | x 0 , x T ) = p(x t | x 0 ) p(x T | x 0 ) ∂ ∂t p(x T | x t ) + p(x T | x t ) p(x T | x 0 ) ∂ ∂t p(x t | x 0 ) = p(x t | x 0 ) p(x T | x 0 ) -f (x t , t) • ∇ xt p(x T | x t ) - 1 2 g 2 t ∇ xt • ∇ xt p(x T | x t ) + p(x T | x t ) p(x T | x 0 ) -∇ xt • [f (x t , t)p(x t | x 0 )] + 1 2 g 2 t ∇ xt • ∇ xt p(x t | x 0 ) = - p(x t | x 0 ) p(x T | x 0 ) f (x t , t) • ∇ xt p(x T | x t ) + p(x T | x t ) p(x T | x 0 ) f (x t , t)∇ xt p(x t | x 0 ) + p(x T | x t ) p(x T | x 0 ) p(x t | x 0 )∇ xt • f (x t , t) + 1 2 g 2 t p(x T | x t ) p(x T | x 0 ) ∇ xt • ∇ xt p(x t | x 0 ) - p(x t | x 0 ) p(x T | x 0 ) ∇ xt • ∇ xt p(x T | x t ) = -[f (x t , t) • ∇ xt p(x t | x 0 , x T ) + p(x t | x 0 , x T ) • ∇ xt f (x t , t)] + 1 2 g 2 t p(x T | x t ) p(x T | x 0 ) ∇ xt • ∇ xt p(x t | x 0 ) - p(x t | x 0 ) p(x T | x 0 ) ∇ xt • ∇ xt p(x T | x t ) = -∇ xt • [f (x t , t)p(x t | x 0 , x T )] + 1 2 g 2 t p(x T | x t ) p(x T | x 0 ) ∇ xt • ∇ xt p(x t | x 0 ) - p(x t | x 0 ) p(x T | x 0 ) ∇ xt • ∇ xt p(x T | x t )", "eq_num": "(50)"}], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "For the second term, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "1 2 g 2 t p(x T | x t ) p(x T | x 0 ) ∇ xt • ∇ xt p(x t | x 0 ) - p(x t | x 0 ) p(x T | x 0 ) ∇ xt • ∇ xt p(x T | x t ) = 1 2 g 2 t p(x T | x t ) p(x T | x 0 ) ∇ xt • ∇ xt p(x t | x 0 ) + 1 p(x T | x 0 ) ∇ xt p(x T | x t ) • ∇ xt p(x t | x 0 ) + 1 p(x T | x 0 ) ∇ xt p(x T | x t ) • ∇ xt p(x t | x 0 ) + p(x t | x 0 ) p(x T | x 0 ) ∇ xt • ∇ xt p(x T | x t ) -g 2 t 1 p(x T | x 0 ) ∇ xt p(x T | x t ) • ∇ xt p(x t | x 0 ) + p(x t | x 0 ) p(x T | x 0 ) ∇ xt • ∇ xt p(x T | x t ) = 1 2 g 2 t 1 p(x T | x 0 ) ∇ xt • [p(x T | x t )∇ xt p(x t | x 0 )] + 1 p(x T | x 0 ) ∇ xt • [p(x t | x 0 )∇ xt p(x T | x t )] -g 2 t 1 p(x T | x 0 ) ∇ xt • [p(x t | x 0 )∇ xt p(x T | x t )] = 1 2 g 2 t [∇ xt • [p(x t | x 0 , x T )∇ xt log p(x t | x 0 )] + ∇ xt • [p(x t | x 0 , x T )∇ xt log p(x T | x t )]] -g 2 t ∇ xt • [p(x t | x 0 , x T )∇ xt log p(x T | x t )] = 1 2 g 2 t [∇ xt • [p(x t | x 0 , x T )∇ xt log p(x t | x 0 , x T )]] -g 2 t ∇ xt • [p(x t | x 0 , x T )∇ xt log p(x T | x t )] = 1 2 g 2 t ∇ xt • ∇ xt p(x t | x 0 , x T ) -g 2 t ∇ xt • [p(x t | x 0 , x T )∇ xt log p(x T | x t )]", "eq_num": "(51)"}], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "Bring it back to (50): This concludes the proof of the Theorem D.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "∂ ∂t p(x t | x 0 , x T ) = -∇ xt • [f (x t ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "For all experiments, we use the same noise network, with the network architecture and mainly training parameters consistent with the paper (<PERSON><PERSON> et al., 2023a) . This network is similar to a U-Net structure but without group normalization layers and self-attention layers. The steady variance level λ 2 was set to 30 (over 255), and the sampling step number T was set to 100.", "cite_spans": [{"start": 139, "end": 158, "text": "(<PERSON><PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E. Experimental Details", "sec_num": null}, {"text": "In the training process, we set the patch size = 128 with batch size = 8 and use <PERSON> (Kingma & Ba, 2015) optimizer with parameters β 1 = 0.9 and β 2 = 0.99. The total training steps are 900 thousand with the initial learning rate set to 10 -4 , and it decays by half at iterations 300, 500, 600, and 700 thousand. For the setting of θ t , we employ a flipped version of cosine noise schedule (Nichol & Dhariwal, 2021) , enabling θ t to change from 0 to 1 over time. Notably, to address the issue of θ t being too smooth when t closed to 1, we let the coefficient e -θT to be a small enough value δ = 0.005 instead of zero, which represents θT ≈ T i=0 θ i dt = -log δ, as well as dt = -log δ/ T i=0 θ i . Our models are trained on a single 3090 GPU with 24GB memory for about 2.5 days. ", "cite_spans": [{"start": 86, "end": 105, "text": "(Kingma & Ba, 2015)", "ref_id": "BIBREF31"}, {"start": 393, "end": 418, "text": "(Nichol & Dhariwal, 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E. Experimental Details", "sec_num": null}, {"text": "Department of Computer Science, Sun Yat-sen University, Guangzhou, Guangdong, China.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Pengcheng Laboratory. Correspondence to: <PERSON><PERSON> <<EMAIL>>. Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "This work is supported in part by National Natural Science Foundation of China (NSFC) under Grant No. 62376292, U21A20470, and Guangdong Basic and Applied Basic Research Foundation under Grant No. 2024A1515011741.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Ntire 2017 challenge on single image super-resolution: Dataset and study", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Proceedings of the IEEE conference on computer vision and pattern recognition workshops", "volume": "", "issue": "", "pages": "126--135", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> 2017 challenge on single image super-resolution: Dataset and study. In Proceedings of the IEEE conference on computer vision and pattern recognition workshops, pp. 126-135, 2017.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Introduction to stochastic differential equations", "authors": [{"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1988, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> Introduction to stochastic differential equations, 1988.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Building normalizing flows with stochastic interpolants", "authors": [{"first": "M", "middle": [], "last": "Albergo", "suffix": ""}, {"first": "E", "middle": [], "last": "Vanden-Eijnden", "suffix": ""}], "year": null, "venue": "Proceedigns of International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. Building normalizing flows with stochastic interpolants. In In Proceedigns of International Conference on Learning Representations (ICLR), 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Stochastic interpolants: A unifying framework for flows and diffusions", "authors": [{"first": "M", "middle": ["S"], "last": "Albergo", "suffix": ""}, {"first": "N", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Vanden-Eijnden", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.08797"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>ochastic interpolants: A unifying framework for flows and diffusions. arXiv preprint arXiv:2303.08797, 2023.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Reverse-time diffusion equation models", "authors": [{"first": "B", "middle": ["D"], "last": "<PERSON>", "suffix": ""}], "year": 1982, "venue": "Stochastic Processes and their Applications", "volume": "12", "issue": "", "pages": "313--326", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> Reverse-time diffusion equation models. Stochastic Processes and their Applications, 12(3):313- 326, 1982.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Digital image restoration", "authors": [{"first": "M", "middle": ["R"], "last": "Ban<PERSON>", "suffix": ""}, {"first": "A", "middle": ["K"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1997, "venue": "IEEE signal processing magazine", "volume": "14", "issue": "2", "pages": "24--41", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, A. K. Digital image restoration. IEEE signal processing magazine, 14(2): 24-41, 1997.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Convex optimization", "authors": [{"first": "S", "middle": ["P"], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>denberghe", "suffix": ""}], "year": 2004, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON>, L. Convex <PERSON>. Cambridge university press, 2004.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Simple baselines for image restoration", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}], "year": 2022, "venue": "European Conference on Computer Vision", "volume": "", "issue": "", "pages": "17--33", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Simple base- lines for image restoration. In European Conference on Computer Vision, pp. 17-33. Springer, 2022.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Fractional ornstein-uhlenbeck processes", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2003, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and Mae<PERSON>, M. Fractional ornstein-uhlenbeck processes. 2003.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Brownian bridge", "authors": [{"first": "W", "middle": ["C"], "last": "<PERSON>", "suffix": ""}], "year": 2009, "venue": "Wiley interdisciplinary reviews: computational statistics", "volume": "1", "issue": "3", "pages": "325--332", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>. Wiley interdisciplinary reviews: computational statistics, 1(3):325-332, 2009.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Diffusion posterior sampling for general noisy inverse problems", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["T"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["C"], "last": "Ye", "suffix": ""}], "year": 2022, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, J. C. Diffusion posterior sampling for general noisy inverse problems. In The Eleventh International Confer- ence on Learning Representations, 2022a.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Improving diffusion models for inverse problems using manifold constraints", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>m", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["C"], "last": "Ye", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "25683--25696", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> <PERSON>. Improving diffusion models for inverse problems using manifold constraints. Advances in Neural Information Processing Systems, 35:25683-25696, 2022b.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Parallel diffusion models of operator and image for blind inverse problems", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["C"], "last": "Ye", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "6059--6069", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> <PERSON><PERSON> diffusion models of operator and image for blind inverse problems. In Proceedings of the IEEE/CVF Conference on Com- puter Vision and Pattern Recognition, pp. 6059-6069, 2023.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Focal network for image restoration", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Ren", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Knoll", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF international conference on computer vision", "volume": "", "issue": "", "pages": "13001--13011", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, A. <PERSON> network for image restoration. In Proceedings of the IEEE/CVF international conference on computer vision, pp. 13001- 13011, 2023.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Diffusion schrödinger bridge with applications to score-based generative modeling", "authors": [{"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Heng", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "17695--17709", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> bridge with applications to score-based generative modeling. Advances in Neural Information Processing Systems, 34:17695-17709, 2021.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Inversion by direct iteration: An alternative to denoising diffusion for image restoration", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Milanfar", "suffix": ""}], "year": 2023, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> Inversion by direct iteration: An alternative to denoising diffusion for image restoration. Transactions on Machine Learning Research, 2023.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Diffusion models beat gans on image synthesis", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in neural information processing systems", "volume": "34", "issue": "", "pages": "8780--8794", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> models beat gans on image synthesis. Advances in neural information processing systems, 34:8780-8794, 2021.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Image superresolution using deep convolutional networks", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "IEEE transactions on pattern analysis and machine intelligence", "volume": "38", "issue": "", "pages": "295--307", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, X. Image super- resolution using deep convolutional networks. IEEE transactions on pattern analysis and machine intelligence, 38(2):295-307, 2015.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Score-based diffusion models as principled priors for inverse imaging", "authors": [{"first": "B", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": ["T"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "10520--10531", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, W. <PERSON>. Score-based diffusion models as principled priors for inverse imaging. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 10520-10531, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Mixed hierarchy network for image restoration", "authors": [{"first": "H", "middle": [], "last": "Gao", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>g", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.09554"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON> and <PERSON>, <PERSON>. Mixed hierarchy network for image restoration. arXiv preprint arXiv:2302.09554, 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "A mountain-shaped single-stage network for accurate image restoration", "authors": [{"first": "H", "middle": [], "last": "Gao", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>g", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.05146"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, D. A mountain-shaped single-stage network for accu- rate image restoration. arXiv preprint arXiv:2305.05146, 2023.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "The elements of statistical learning: data mining, inference, and prediction", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["H"], "last": "<PERSON>", "suffix": ""}], "year": 2009, "venue": "", "volume": "2", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>. The elements of statistical learning: data mining, inference, and prediction, volume 2. Springer, 2009.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Simulating diffusion bridges with score matching", "authors": [{"first": "J", "middle": [], "last": "Heng", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2111.07243"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>- ulating diffusion bridges with score matching. arXiv preprint arXiv:2111.07243, 2021.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Gans trained by a two time-scale update rule converge to a local nash equilibrium", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>reiter", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, S. Gans trained by a two time-scale update rule converge to a local nash equilibrium. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Classifier-free diffusion guidance", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "NeurIPS 2021 Workshop on Deep Generative Models and Downstream Applications", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, T. Classifier-free diffusion guidance. In NeurIPS 2021 Workshop on Deep Generative Models and Downstream Applications, 2021.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Denoising diffusion probabilistic models", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "6840--6851", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Denoising diffusion proba- bilistic models. Advances in neural information process- ing systems, 33:6840-6851, 2020.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Keys to better image inpainting: Structure and texture go hand in hand", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Shi", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision", "volume": "", "issue": "", "pages": "208--217", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> to better im- age inpainting: Structure and texture go hand in hand. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision, pp. 208-217, 2023.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Progressive growing of gans for improved quality, stability, and variation", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedigns of International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>- sive growing of gans for improved quality, stability, and variation. In Proceedigns of International Conference on Learning Representations (ICLR), 2018.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Elucidating the design space of diffusion-based generative models", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "26565--26577", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Elucidating the design space of diffusion-based generative models. Advances in Neural Information Processing Systems, 35: 26565-26577, 2022.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Snips: Solving noisy inverse problems stochastically", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "21757--21769", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Solving noisy inverse problems stochastically. Advances in Neural Information Processing Systems, 34:21757-21769, 2021.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Denoising diffusion restoration models", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Song", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "23593--23606", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>ing diffusion restoration models. Advances in Neural Infor- mation Processing Systems, 35:23593-23606, 2022.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "A method for stochastic optimization", "authors": [{"first": "D", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ba", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "Proceedigns of International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>: A method for stochastic optimization. In Proceedigns of International Conference on Learning Representations (ICLR), 2015.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Efficient frequency domain-based transformers for high-quality image deblurring", "authors": [{"first": "L", "middle": [], "last": "Kong", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ge", "suffix": ""}, {"first": "M", "middle": [], "last": "Li", "suffix": ""}, {"first": "J", "middle": [], "last": "Pan", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "5886--5895", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. <PERSON>fficient frequency domain-based transformers for high-quality image deblurring. In Proceedings of the IEEE/CVF Con- ference on Computer Vision and Pattern Recognition, pp. 5886-5895, 2023.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Ugpnet: Universal generative prior for image restoration", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S.-H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Cho", "suffix": ""}], "year": 2024, "venue": "Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision", "volume": "", "issue": "", "pages": "1598--1608", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>, <PERSON>. <PERSON>: Universal generative prior for image restoration. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision, pp. 1598-1608, 2024.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Allin-one image restoration for unknown corruption", "authors": [{"first": "B", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Hu", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Lv", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "17452--17462", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. All- in-one image restoration for unknown corruption. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 17452-17462, 2022.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Image-toimage translation with brownian bridge diffusion models", "authors": [{"first": "B", "middle": [], "last": "Li", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y.-K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Bbdm", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "1952--1961", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y.-K. Bbdm: Image-to- image translation with brownian bridge diffusion models. In Proceedings of the IEEE/CVF Conference on Com- puter Vision and Pattern Recognition, pp. 1952-1961, 2023.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Image restoration using swin transformer", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Sun", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF international conference on computer vision", "volume": "", "issue": "", "pages": "1833--1844", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Image restoration using swin trans- former. In Proceedings of the IEEE/CVF international conference on computer vision, pp. 1833-1844, 2021.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Flow matching for generative modeling", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Le", "suffix": ""}], "year": null, "venue": "Proceedigns of International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Flow matching for generative modeling. In Proceed- igns of International Conference on Learning Represen- tations (ICLR), 2023.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Deep generalized schr<PERSON><PERSON>er bridge", "authors": [{"first": "G.-H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "So", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "9374--9388", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> generalized schrödinger bridge. Advances in Neural In- formation Processing Systems, 35:9374-9388, 2022.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "A. I2sb: image-to-image schr<PERSON>dinger bridge", "authors": [{"first": "G.-H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Vah<PERSON>t", "suffix": ""}, {"first": "D.-A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 40th International Conference on Machine Learning", "volume": "", "issue": "", "pages": "22042--22062", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, A. I2sb: image-to-image schr<PERSON><PERSON><PERSON> bridge. In Proceedings of the 40th Inter- national Conference on Machine Learning, pp. 22042- 22062, 2023a.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Flow straight and fast: Learning to generate and transfer data with rectified flow", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedigns of International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Flow straight and fast: Learning to generate and transfer data with rectified flow. In Proceedigns of International Conference on Learning Representations (ICLR), 2023b.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Image restoration with mean-reverting stochastic differential equations", "authors": [{"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": ["K"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": ["B"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "23045--23066", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, T. B. Image restoration with mean-reverting stochastic differential equations. In International Conference on Machine Learning, pp. 23045-23066. PMLR, 2023a.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Refusion: Enabling large-size realistic image restoration with latent-space diffusion models", "authors": [{"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": ["K"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": ["B"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "1680--1691", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, T. B. Refusion: Enabling large-size realistic im- age restoration with latent-space diffusion models. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 1680-1691, 2023b.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Gibbsddrm: a partially collapsed gibbs sampler for solving blind inverse problems with denoising diffusion restoration", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Sai<PERSON>", "suffix": ""}, {"first": "C.-H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 40th International Conference on Machine Learning", "volume": "", "issue": "", "pages": "25501--25522", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: a partially col- lapsed gibbs sampler for solving blind inverse problems with denoising diffusion restoration. In Proceedings of the 40th International Conference on Machine Learning, pp. 25501-25522, 2023.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Improved denoising diffusion probabilistic models", "authors": [{"first": "A", "middle": ["Q"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "8162--8171", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON>mproved denoising diffusion probabilistic models. In International Conference on Machine Learning, pp. 8162-8171. PMLR, 2021.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Prompting for all-in-one blind image restoration", "authors": [{"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["W"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Promptir", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.13090"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, F. S. Promptir: Prompting for all-in-one blind image restora- tion. arXiv preprint arXiv:2306.13090, 2023.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Fokker-planck equation", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1996, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON>-<PERSON>ck equation. Springer, 1996.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Image super-resolution via iterative refinement", "authors": [{"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["J"], "last": "Fleet", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "IEEE Transactions on Pattern Analysis and Machine Intelligence", "volume": "45", "issue": "4", "pages": "4713--4726", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, D. J<PERSON>, and <PERSON><PERSON><PERSON>, M. Image super-resolution via iterative refinement. IEEE Transactions on Pattern Analysis and Machine Intelligence, 45(4):4713-4726, 2022.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "On conditional ornstein-uhlenbeck processes", "authors": [{"first": "P", "middle": [], "last": "Salminen", "suffix": ""}], "year": 1984, "venue": "Advances in Applied Probability", "volume": "16", "issue": "4", "pages": "920--922", "other_ids": {"ISSN": ["00018678"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> On conditional ornstein-uhlenbeck processes. Advances in Applied Probability, 16(4):920-922, 1984. ISSN 00018678. URL http://www.jstor.org/ stable/1427347.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Applied stochastic differential equations", "authors": [{"first": "S", "middle": [], "last": "Särkkä", "suffix": ""}, {"first": "A", "middle": [], "last": "Solin", "suffix": ""}], "year": 2019, "venue": "", "volume": "10", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> Applied stochastic differential equations, volume 10. Cambridge University Press, 2019.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Diffusion schr<PERSON>dinger bridge matching", "authors": [{"first": "Y", "middle": [], "last": "Shi", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> bridge matching. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Variational deep image restoration", "authors": [{"first": "J", "middle": ["W"], "last": "Soh", "suffix": ""}, {"first": "N", "middle": ["I"], "last": "Cho", "suffix": ""}], "year": 2022, "venue": "IEEE Transactions on Image Processing", "volume": "31", "issue": "", "pages": "4363--4376", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, N. I. Variational deep image restoration. IEEE Transactions on Image Processing, 31:4363-4376, 2022.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Deep unsupervised learning using nonequilibrium thermodynamics", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "2256--2265", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, S. <PERSON> unsupervised learning using nonequi- librium thermodynamics. In International conference on machine learning, pp. 2256-2265. PMLR, 2015.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Generative modeling by estimating gradients of the data distribution", "authors": [{"first": "Y", "middle": [], "last": "Song", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "32", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, S. Generative modeling by estimating gradients of the data distribution. Advances in neural information processing systems, 32, 2019.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Maximum likelihood training of score-based diffusion models", "authors": [{"first": "Y", "middle": [], "last": "Song", "suffix": ""}, {"first": "C", "middle": [], "last": "Durkan", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "1415--1428", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. Maximum likelihood training of score-based diffusion models. Ad- vances in Neural Information Processing Systems, 34: 1415-1428, 2021a.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Score-based generative modeling through stochastic differential equations", "authors": [{"first": "Y", "middle": [], "last": "Song", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Poole", "suffix": ""}], "year": null, "venue": "Proceedigns of International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> <PERSON>, S<PERSON>, and <PERSON>, B. Score-based generative modeling through stochastic differential equations. In Proceedigns of International Conference on Learning Representations (ICLR), 2021b.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Dual diffusion implicit bridges for image-to-image translation", "authors": [{"first": "X", "middle": [], "last": "Su", "suffix": ""}, {"first": "J", "middle": [], "last": "Song", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. Dual diffusion implicit bridges for image-to-image translation. In The Eleventh International Conference on Learning Repre- sentations, 2022.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Simulationfree schr<PERSON>dinger bridges via score and flow matching", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Fatras", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICML Workshop on New Frontiers in Learning, Control, and Dynamical Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, Y. Simulation- free schrödinger bridges via score and flow matching. In ICML Workshop on New Frontiers in Learning, Control, and Dynamical Systems, 2023.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Multi-axis mlp for image processing", "authors": [{"first": "Z", "middle": [], "last": "Tu", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Milanfar", "suffix": ""}, {"first": "A", "middle": [], "last": "Bovik", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "", "middle": [], "last": "Maxim", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "5769--5780", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Multi-axis mlp for image pro- cessing. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 5769- 5780, 2022.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "A connection between score matching and denoising autoencoders", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2011, "venue": "Neural computation", "volume": "23", "issue": "7", "pages": "1661--1674", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> A connection between score matching and de- noising autoencoders. Neural computation, 23(7):1661- 1674, 2011.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Unlimited-size diffusion restoration", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "1160--1167", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Unlimited-size diffusion restoration. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 1160-1167, 2023.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Image quality assessment: from error visibility to structural similarity", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["C"], "last": "Bovik", "suffix": ""}, {"first": "H", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": ["P"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2004, "venue": "IEEE transactions on image processing", "volume": "13", "issue": "4", "pages": "600--612", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, E. P. Image quality assessment: from error visibility to struc- tural similarity. IEEE transactions on image processing, 13(4):600-612, 2004.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Taylor neural network for real-world image super-resolution", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Li", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1942, "venue": "IEEE Transactions on Image Processing", "volume": "32", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> neural network for real-world image super-resolution. IEEE Transactions on Image Processing, 32:1942-1951, 2023.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Image de-raining transformer", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z.-J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "IEEE Transactions on Pattern Analysis and Machine Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, Z.-J. Image de-raining transformer. IEEE Transactions on Pattern Analysis and Machine Intelligence, 2022.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Deep joint rain detection and removal from a single image", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Yan", "suffix": ""}], "year": 2017, "venue": "Proceedings of the IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "1357--1366", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Deep joint rain detection and removal from a single im- age. In Proceedings of the IEEE conference on computer vision and pattern recognition, pp. 1357-1366, 2017.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "Single image deraining: From model-based to data-driven and beyond", "authors": [{"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "IEEE Transactions on pattern analysis and machine intelligence", "volume": "43", "issue": "11", "pages": "4059--4077", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> image deraining: From model-based to data-driven and beyond. IEEE Transactions on pattern analysis and machine intelligence, 43(11):4059-4077, 2020.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "Image deblurring with blurred/noisy image pairs", "authors": [{"first": "L", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>uan", "suffix": ""}, {"first": "H.-Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2007, "venue": "ACM SIGGRAPH 2007 papers", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, H.-Y. Image deblur- ring with blurred/noisy image pairs. In ACM SIGGRAPH 2007 papers, pp. 1-es. 2007.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "Towards realtime 4k image super-resolution", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["V"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "1522--1532", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, R. Towards real- time 4k image super-resolution. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 1522-1532, 2023.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "Multi-stage progressive image restoration", "authors": [{"first": "S", "middle": ["W"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Arora", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Hay<PERSON>", "suffix": ""}, {"first": "F", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "M.<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "14821--14831", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, M.<PERSON>H<PERSON>, and <PERSON><PERSON>, L. Multi-stage progressive image restoration. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 14821- 14831, 2021.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "Mm-bsn: Selfsupervised image denoising for real-world with multimask based on blind-spot network", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "4188--4197", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Z. Mm-bsn: Self- supervised image denoising for real-world with multi- mask based on blind-spot network. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 4188-4197, 2023a.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "Towards coherent image inpainting using denoising diffusion implicit models", "authors": [{"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": ["S"], "last": "Jaakkola", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, T. S., and <PERSON>, <PERSON> Towards coherent image inpainting using de- noising diffusion implicit models. 2023b.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "Convolutional sparse and lowrank coding-based rain streak removal", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": ["M"], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "IEEE", "volume": "", "issue": "", "pages": "1259--1267", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, V<PERSON> M. Convolutional sparse and low- rank coding-based rain streak removal. In 2017 IEEE Winter conference on applications of computer vision (WACV), pp. 1259-1267. IEEE, 2017.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "Ffdnet: Toward a fast and flexible solution for cnn-based image denoising", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "IEEE Transactions on Image Processing", "volume": "27", "issue": "9", "pages": "4608--4622", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>: Toward a fast and flexible solution for cnn-based image denoising. IEEE Transactions on Image Processing, 27(9):4608- 4622, 2018a.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "The unreasonable effectiveness of deep features as a perceptual metric", "authors": [{"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Isola", "suffix": ""}, {"first": "A", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "586--595", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. The unreasonable effectiveness of deep features as a perceptual metric. In Proceedings of the IEEE conference on computer vision and pattern recognition, pp. 586-595, 2018b.", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "Ddfm: denoising diffusion model for multi-modality image fusion", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "8082--8093", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, L. Ddfm: denoising diffusion model for multi-modality im- age fusion. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 8082-8093, 2023.", "links": null}, "BIBREF75": {"ref_id": "b75", "title": "Denoising diffusion bridge models", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Proceedigns of International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. Denoising diffusion bridge models. In Proceedigns of International Conference on Learning Representations (ICLR), 2024.", "links": null}, "BIBREF76": {"ref_id": "b76", "title": "Image restoration using a neural network", "authors": [{"first": "Y.-T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Vaid", "suffix": ""}, {"first": "B", "middle": ["K"], "last": "<PERSON>", "suffix": ""}], "year": 1988, "venue": "IEEE transactions on acoustics, speech, and signal processing", "volume": "36", "issue": "7", "pages": "1141--1151", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, B. K. Image restoration using a neural network. IEEE transac- tions on acoustics, speech, and signal processing, 36(7): 1141-1151, 1988.", "links": null}}, "ref_entries": {"FIGREF0": {"fig_num": "1", "num": null, "uris": null, "text": "Figure 1. Overview of the proposed GOUB for image restoration. The GOU process is capable of transferring an HQ image into a noisy LQ image. Additionally, through the application of h-transform, we can eliminate the noise on LQ, enabling the GOUB model to precisely bridge the gap between HQ and LQ.", "type_str": "figure"}, "FIGREF1": {"fig_num": "2", "num": null, "uris": null, "text": "Figure 2. Qualitative comparison of the visual results of different inpainting methods on the CelebA-HQ dataset with thin mask.", "type_str": "figure"}, "FIGREF2": {"fig_num": "3", "num": null, "uris": null, "text": "Figure 3. Qualitative comparison of the visual results of different deraining methods on the Rain100H dataset.", "type_str": "figure"}, "FIGREF3": {"fig_num": "4", "num": null, "uris": null, "text": "Figure 4. Qualitative comparison of the visual results of different 4x super-resolution methods on the DIV2K dataset.", "type_str": "figure"}, "FIGREF4": {"fig_num": "5", "num": null, "uris": null, "text": "Figure 5. Qualitative comparison with the different bridge models in many tasks.", "type_str": "figure"}, "TABREF0": {"html": null, "num": null, "text": "Image Inpainting. Qualitative comparison with the relevant baselines on CelebA-HQ.", "content": "<table><tr><td colspan=\"5\">METHOD PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>PromptIR</td><td>30.22</td><td>0.9180</td><td>0.068</td><td>32.69</td></tr><tr><td>DDRM</td><td>27.16</td><td>0.8993</td><td>0.089</td><td>37.02</td></tr><tr><td>IR-SDE</td><td>28.37</td><td>0.9166</td><td>0.046</td><td>25.13</td></tr><tr><td>GOUB</td><td>28.98</td><td>0.9067</td><td>0.037</td><td>4.30</td></tr><tr><td>Mean-ODE</td><td>31.39</td><td>0.9392</td><td>0.052</td><td>12.24</td></tr><tr><td colspan=\"5\">evaluation, i.e., Peak Signal-to-Noise Ratio (PSNR) for as-</td></tr><tr><td colspan=\"5\">sessing reconstruction quality, Structural Similarity Index</td></tr><tr><td colspan=\"5\">(SSIM) (Wang et al., 2004) for gauging structural percep-</td></tr><tr><td colspan=\"5\">tion, Learned Perceptual Image Patch Similarity (LPIPS)</td></tr><tr><td colspan=\"5\">(Zhang et al., 2018b) for evaluating the depth and quality</td></tr><tr><td colspan=\"5\">of features, and Fréchet Inception Distance (FID) (Heusel</td></tr><tr><td colspan=\"5\">et al., 2017) to measure the diversity in generated images.</td></tr><tr><td colspan=\"5\">More experiment details are present in Appendix E.</td></tr></table>", "type_str": "table"}, "TABREF1": {"html": null, "num": null, "text": "Image Deraining. Qualitative comparison with the relevant baselines on Rain100H.", "content": "<table><tr><td colspan=\"5\">METHOD PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>MPRNet</td><td>30.41</td><td>0.8906</td><td>0.158</td><td>61.59</td></tr><tr><td>M3SNet-32</td><td>30.64</td><td>0.8920</td><td>0.154</td><td>60.26</td></tr><tr><td>MAXIM</td><td>30.81</td><td>0.9027</td><td>0.133</td><td>58.72</td></tr><tr><td>MHNet</td><td>31.08</td><td>0.8990</td><td>0.126</td><td>57.93</td></tr><tr><td>IR-SDE</td><td>31.65</td><td>0.9041</td><td>0.047</td><td>18.64</td></tr><tr><td>GOUB</td><td>31.96</td><td>0.9028</td><td>0.046</td><td>18.14</td></tr><tr><td>Mean-ODE</td><td>34.56</td><td>0.9414</td><td>0.077</td><td>32.83</td></tr></table>", "type_str": "table"}, "TABREF2": {"html": null, "num": null, "text": "Image 4× Super-Resolution. Qualitative comparison with the relevant baselines on DIV2K.", "content": "<table><tr><td colspan=\"5\">METHOD PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>DDRM</td><td>24.35</td><td>0.5927</td><td>0.364</td><td>78.71</td></tr><tr><td>IR-SDE</td><td>25.90</td><td>0.6570</td><td>0.231</td><td>45.36</td></tr><tr><td>GOUB</td><td>26.89</td><td>0.7478</td><td>0.220</td><td>20.85</td></tr><tr><td>Mean-ODE</td><td>28.50</td><td>0.8070</td><td>0.328</td><td>22.14</td></tr></table>", "type_str": "table"}, "TABREF3": {"html": null, "num": null, "text": "Qualitative comparison with the corresponding Score-ODE on various tasks.", "content": "<table><tr><td>METHOD</td><td/><td colspan=\"2\">Image Inapinting</td><td/><td/><td colspan=\"2\">Image Deraining</td><td/><td colspan=\"4\">Image 4× Super-Resolution</td></tr><tr><td/><td colspan=\"12\">PSNR↑ SSIM↑ LPIPS↓ FID↓ PSNR↑ SSIM↑ LPIPS↓ FID↓ PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>Score-ODE</td><td>18.23</td><td>0.6266</td><td>0.389</td><td>161.54</td><td>13.64</td><td>0.7404</td><td>0.338</td><td>191.15</td><td>28.14</td><td>0.7993</td><td>0.344</td><td>25.51</td></tr><tr><td>Mean-ODE</td><td>31.39</td><td>0.9392</td><td>0.052</td><td>12.24</td><td>34.56</td><td>0.9414</td><td>0.077</td><td>32.83</td><td>28.50</td><td>0.8070</td><td>0.328</td><td>22.14</td></tr></table>", "type_str": "table"}, "TABREF4": {"html": null, "num": null, "text": "Qualitative comparison with the different bridge models on CelebA-HQ, Rain100H, and DIV2K datasets.", "content": "<table><tr><td>METHOD</td><td/><td colspan=\"2\">Image Inapinting</td><td/><td/><td colspan=\"2\">Image Deraining</td><td/><td colspan=\"4\">Image 4× Super-Resolution</td></tr><tr><td/><td colspan=\"12\">PSNR↑ SSIM↑ LPIPS↓ FID↓ PSNR↑ SSIM↑ LPIPS↓ FID↓ PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>VEB</td><td>27.75</td><td>0.8943</td><td>0.056</td><td>13.70</td><td>30.39</td><td>0.8975</td><td>0.059</td><td>28.54</td><td>24.21</td><td>0.5808</td><td>0.384</td><td>36.55</td></tr><tr><td>VPB</td><td>27.32</td><td>0.8841</td><td>0.049</td><td>11.87</td><td>30.89</td><td>0.8847</td><td>0.051</td><td>23.36</td><td>25.40</td><td>0.6041</td><td>0.342</td><td>29.17</td></tr><tr><td>GOUB</td><td>28.98</td><td>0.9067</td><td>0.037</td><td>4.30</td><td>31.96</td><td>0.9028</td><td>0.046</td><td>18.14</td><td>26.89</td><td>0.7478</td><td>0.220</td><td>20.85</td></tr></table>", "type_str": "table"}, "TABREF5": {"html": null, "num": null, "text": ", t) = x t θ t e θt dt + e θt dx t = x t θ t e θt dt + e θt [θ t (µ -x t ) dt + g t dw t ] = e θt θ t µ + e θt g t dw tIt's obvious that the transition kernel is a Gaussian distribution. Since dw z ∼ N (0, dzI), we have:", "content": "<table><tr><td>t , f (x t , t) = x t e θt g 2 t 2θ t 1 -e -2 θs:t I , df (x t (43) θs:t = t s θ z dz (5) (42) Using Ito differential formula, we get: Proof : Writing: Integrating from s to t we get: s e θz g z dw z = N 0, t s e 2 θz g 2 z dzI s = N 0, λ 2 e 2 θt -e 2 θs I x t = N 0, λ 2 t e 2 θz 2θ t dzI (45)</td></tr></table>", "type_str": "table"}, "TABREF6": {"html": null, "num": null, "text": "This is the definition of FP equation of conditional transition probability p(x t | x 0 , x T ), which represents the evolution follows the SDE:dx t = f (x t , t) + g 2 t ∇ xt log p(x T | x t ) dt + g t dw t (53)", "content": "<table/>", "type_str": "table"}, "TABREF7": {"html": null, "num": null, "text": "Image Inpainting. Qualitative comparison with the relevant baselines on CelebA-HQ with thick mask.", "content": "<table><tr><td colspan=\"5\">METHOD PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>DDRM</td><td>19.48</td><td>0.8154</td><td colspan=\"2\">0.1487 26.24</td></tr><tr><td>IRSDE</td><td>21.12</td><td>0.8499</td><td colspan=\"2\">0.1046 11.12</td></tr><tr><td>GOUB</td><td>22.27</td><td>0.8754</td><td>0.0914</td><td>5.64</td></tr></table>", "type_str": "table"}, "TABREF8": {"html": null, "num": null, "text": "Image Deraining. Qualitative comparison with the relevant baselines on Rain100L.", "content": "<table><tr><td colspan=\"5\">METHOD PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>PRENET</td><td>37.48</td><td>0.9792</td><td>0.020</td><td>10.9</td></tr><tr><td>MAXIM</td><td>38.06</td><td>0.9770</td><td>0.048</td><td>19.0</td></tr><tr><td>IRSDE</td><td>38.30</td><td>0.9805</td><td>0.014</td><td>7.94</td></tr><tr><td>GOUB</td><td>39.79</td><td>0.9830</td><td>0.009</td><td>5.18</td></tr></table>", "type_str": "table"}, "TABREF9": {"html": null, "num": null, "text": "Image 8× Super-Resolution. Qualitative comparison with the relevant baselines on DIV2K.", "content": "<table><tr><td colspan=\"4\">METHOD PSNR↑ SSIM↑ LPIPS↓</td><td>Training Datasets</td></tr><tr><td>SRFlow</td><td>23.05</td><td>0.57</td><td>0.272</td><td>DIV2K + Flickr2K</td></tr><tr><td>IRSDE</td><td>22.34</td><td>0.55</td><td>0.331</td><td>DIV2K</td></tr><tr><td>GOUB</td><td>23.17</td><td>0.60</td><td>0.310</td><td>DIV2K</td></tr></table>", "type_str": "table"}}}}