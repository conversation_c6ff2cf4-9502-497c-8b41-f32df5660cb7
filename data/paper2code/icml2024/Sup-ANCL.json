{"paper_id": "Sup-ANCL", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:32:58.908872Z"}, "title": "On the Effectiveness of Supervision in Asymmetric Non-Contrastive Learning", "authors": [{"first": "Je<PERSON>he<PERSON>", "middle": [], "last": "Oh", "suffix": "", "affiliation": {"laboratory": "", "institution": "Yonsei University", "location": {}}, "email": ""}, {"first": "Kibok", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Yonsei University", "location": {}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Supervised contrastive representation learning has been shown to be effective in various transfer learning scenarios. However, while asymmetric non-contrastive learning (ANCL) often outperforms its contrastive learning counterpart in selfsupervised representation learning, the extension of ANCL to supervised scenarios is less explored. To bridge the gap, we study ANCL for supervised representation learning, coined SUPSIAM and SUPBYOL, leveraging labels in ANCL to achieve better representations. The proposed supervised ANCL framework improves representation learning while avoiding collapse. Our analysis reveals that providing supervision to ANCL reduces intraclass variance, and the contribution of supervision should be adjusted to achieve the best performance. Experiments demonstrate the superiority of supervised ANCL across various datasets and tasks. The code is available at: https: //github.com/JH-Oh-23/Sup-ANCL.", "pdf_parse": {"paper_id": "Sup-ANCL", "_pdf_hash": "", "abstract": [{"text": "Supervised contrastive representation learning has been shown to be effective in various transfer learning scenarios. However, while asymmetric non-contrastive learning (ANCL) often outperforms its contrastive learning counterpart in selfsupervised representation learning, the extension of ANCL to supervised scenarios is less explored. To bridge the gap, we study ANCL for supervised representation learning, coined SUPSIAM and SUPBYOL, leveraging labels in ANCL to achieve better representations. The proposed supervised ANCL framework improves representation learning while avoiding collapse. Our analysis reveals that providing supervision to ANCL reduces intraclass variance, and the contribution of supervision should be adjusted to achieve the best performance. Experiments demonstrate the superiority of supervised ANCL across various datasets and tasks. The code is available at: https: //github.com/JH-Oh-23/Sup-ANCL.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Self-supervised learning has recently been proven to be an effective paradigm for representation learning (<PERSON> et al., 2020a; <PERSON>, 2021; <PERSON> et al., 2022) . Among various pretext tasks for self-supervised learning, contrastive learning (CL) (<PERSON> et al., 2018; <PERSON> et al., 2020a; <PERSON> et al., 2020) first promised outstanding performance, surpassing the transfer learning performance of supervised pretraining (<PERSON><PERSON><PERSON> et al., 2014) , which learns representations by attracting positive pairs while repelling negative pairs. However, CL requires negative samples to ensure good performance, which might not be possible under limited batch sizes. On the other hand, asymmetric non-contrastive learning (ANCL) (<PERSON><PERSON> et al., 2020; <PERSON> & <PERSON>, 2021) has emerged as a promising alternative to CL, which maximizes the similarity between positive pairs without relying on negative samples. To prevent learned representations from collapsing, ANCL employs an asymmetric structure by placing a predictor after one side of the projector.", "cite_spans": [{"start": 106, "end": 126, "text": "(<PERSON> et al., 2020a;", "ref_id": null}, {"start": 127, "end": 143, "text": "<PERSON> & He, 2021;", "ref_id": null}, {"start": 144, "end": 160, "text": "He et al., 2022)", "ref_id": "BIBREF21"}, {"start": 247, "end": 274, "text": "(<PERSON> et al., 2018;", "ref_id": "BIBREF54"}, {"start": 275, "end": 294, "text": "<PERSON> et al., 2020a;", "ref_id": null}, {"start": 295, "end": 311, "text": "He et al., 2020)", "ref_id": "BIBREF20"}, {"start": 423, "end": 446, "text": "(<PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF48"}, {"start": 722, "end": 742, "text": "(<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF16"}, {"start": 743, "end": 759, "text": "<PERSON> & He, 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "A key component in both CL and ANCL is acquisition of positive pairs, which is typically achieved through data augmentation. Given that datasets for pretraining often include labels, <PERSON><PERSON><PERSON> et al. (2020) proposed to incorporate supervision into CL by treating samples with the same class label as positive pairs as well. Supervised CL has demonstrated superior performance across diverse tasks, such as few-shot learning (<PERSON><PERSON><PERSON> et al., 2021) , long-tail recognition (<PERSON> et al., 2021) , continual learning (<PERSON> et al., 2021) , and natural language processing (<PERSON><PERSON> et al., 2021) .", "cite_spans": [{"start": 183, "end": 203, "text": "<PERSON><PERSON><PERSON> et al. (2020)", "ref_id": "BIBREF26"}, {"start": 421, "end": 444, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF40"}, {"start": 469, "end": 488, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF24"}, {"start": 510, "end": 528, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF3"}, {"start": 563, "end": 583, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "While supervision helps to discover more positive samples, it does not directly help to identify effective negative samples. Consequently, ANCL has a better potential to benefit from supervision, as it focuses on positive pairs only. However, in contrast to CL, there are a limited number of studies on leveraging supervision to improve ANCL, despite its strong performance in self-supervised learning. To bridge this gap, we study the effect of supervision in ANCL by introducing the supervised ANCL framework and investigating its behavior through theoretical and empirical analysis.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "To the best of our knowledge, our work is the first to conduct a theoretical analysis of the behavior of representations learned through supervised ANCL. Our experiments confirm the effectiveness of supervision observed through our theoretical analysis, as well as the superiority of representations learned via supervised ANCL across various datasets and tasks. Specifically, as illustrated in Figure 1 , we consider SUPSIAM and SUPBYOL, which are supervised adaptations of the two popular ANCL methods, SIMSIAM (Chen & He, 2021) and BYOL (<PERSON><PERSON> et al., 2020) , respectively. Our contributions are summarized as follows:", "cite_spans": [{"start": 513, "end": 530, "text": "(<PERSON> & He, 2021)", "ref_id": null}, {"start": 540, "end": 560, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF16"}], "ref_spans": [{"start": 402, "end": 403, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We propose a supervised ANCL framework for representation learning while avoiding collapse, which surpasses the performance of its self-supervised counterpart when supervision is available.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• Our analysis demonstrates that incorporating supervision into ANCL reduces the intra-class variance of latent features, and that learning to capture both intra-and interclass variance is crucial for representation learning. We manage a target pool to ensure the existence of positive samples sharing the same class label in the form of z sup 2 . Stop-gradient (sg) applied to z2 and z sup 2 ensures that the gradients propagate through the online branch with the predictor only. The target branch without the predictor either shares parameters with the online branch (SUPSIAM), or exhibits a momentum network (SUPBYOL).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• Our experiments validate our analysis and demonstrate the superiority of representations learned via supervised ANCL across various datasets and tasks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Supervised CL. Although <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> et al., 2020) demonstrated remarkable linear probing performance on pretrained datasets, its efficacy on other downstream datasets is comparable or inferior to that of self-supervised methods.", "cite_spans": [{"start": 31, "end": 52, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "Related Works", "sec_num": "2."}, {"text": "In response, subsequent works have been underway to better utilize supervision to enhance representation learning. <PERSON> et al. (2021) proposed to improve CL by taking top-K positive neighbors into account and assigning soft labels to positive samples based on similarity, such that it better reflects task-specific semantic features and task-agnostic appearance features. <PERSON> et al. (2023) argued that naively incorporating supervised signals might conflict with the self-supervised signals. To address this issue, <PERSON> et al. (2023) proposed to impose hierarchical supervisions with an additional projector. <PERSON> et al. (2021) provided both theoretical and empirical evidence demonstrating that the SUPCON loss is minimized when each class collapses to a single point, resulting in poor generalization of learned representations. <PERSON> et al. (2022) found that the SUPCON loss is invariant to class-fixing permutations, indicating that the loss remains unchanged when data points within the same class are arbitrarily permuted in representation space, which also leads to poor generalization of learned representations. <PERSON> et al. (2022) proposed incorporating a weighted class-conditional InfoNCE loss to avoid class collapse, and constraining the encoder, adding a class-conditional autoencoder, and using data augmentation to break permutation invariance. <PERSON><PERSON> et al. (2023) argued that features learned through supervised CL are prone to class collapse, whereas those learned through self-supervised CL suffer from feature suppression, i.e., easy and class-irrelevant features suppress to learn harder and class-relevant features. They claimed that balancing the losses of supervised and self-supervised CL is crucial for improving the quality of learned representations. Notably, these efforts have concentrated on CL, motivating us to investigate the effect of supervision in ANCL. Although several studies on supervised ANCL exist, such as Asadi et al. (2022) and Maser et al. (2023) , their contributions lack a theoretical understanding of the effect of supervision and/or are limited to specific domains.", "cite_spans": [{"start": 115, "end": 132, "text": "<PERSON> et al. (2021)", "ref_id": "BIBREF56"}, {"start": 371, "end": 389, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF55"}, {"start": 515, "end": 533, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF55"}, {"start": 609, "end": 627, "text": "<PERSON> et al. (2021)", "ref_id": "BIBREF15"}, {"start": 831, "end": 849, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF4"}, {"start": 1120, "end": 1138, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF4"}, {"start": 1360, "end": 1377, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF60"}, {"start": 1947, "end": 1966, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF0"}, {"start": 1971, "end": 1990, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Related Works", "sec_num": "2."}, {"text": "Theoretical Analysis on ANCL. While the initial ANCL works (<PERSON><PERSON> et al., 2020; <PERSON> & He, 2021) have demonstrated impressive performance, the learning dynamics that enable effective representation learning without negative pairs while avoiding collapse to trivial solutions remain unclear. <PERSON><PERSON> et al. (2021) elucidated the dynamics of ANCL through the spectral decomposition of the correlation matrix. Specifically, assuming the predictor is linear, they proved that the eigenspace of the learned predictor aligns with the eigenspace of the correlation matrix of the latent features. <PERSON> et al. (2022) empirically observed that, as learning progresses, both the linear predictor and the correlation matrix of latent features converge to a (scaled) identity matrix in ANCL. Based on this observation, they argued that the asymmetric architecture in ANCL implicitly encourages feature decorrelation, achieving a similar effect to symmetric non-CL methods that explicitly decorrelate features such as Barlow Twins (<PERSON><PERSON><PERSON> et al., 2021) and VICReg (<PERSON><PERSON> et al., 2022) . <PERSON><PERSON> et al. (2023) suggested that the predictor in ANCL operates as a low-pass filter, thereby decreasing the rank of the predictor outputs. They argued that the rank difference between the correlation ma-trix of the projector outputs and that of the predictor outputs mitigates dimensional collapse by gradually increasing the effective rank of them as training progresses. Inspired by the prior works on self-supervised ANCL, we analyze supervised ANCL under a similar framework with additional assumptions. On the other hand, <PERSON><PERSON><PERSON> et al. (2023) found that prior works overlook the L2 normalization of projector/predictor outputs, which is a common practice in ANCL, before computing the loss. They investigated the learning dynamics by incorporating the L2 normalization and compared it with the case without the L2 normalization.", "cite_spans": [{"start": 59, "end": 79, "text": "(<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF16"}, {"start": 80, "end": 96, "text": "<PERSON> & He, 2021)", "ref_id": null}, {"start": 291, "end": 309, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF53"}, {"start": 586, "end": 603, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF35"}, {"start": 1013, "end": 1035, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF61"}, {"start": 1047, "end": 1068, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF1"}, {"start": 1071, "end": 1089, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF62"}, {"start": 1600, "end": 1622, "text": "<PERSON><PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "Related Works", "sec_num": "2."}, {"text": "Our work also considers the L2 normalization; however, instead of normalizing the features directly, we consider it as a constraint and employ a La<PERSON>ngian formulation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Works", "sec_num": "2."}, {"text": "In this section, we first review the problem setting of selfsupervised ANCL. Then, we introduce supervised ANCL. The overall framework is illustrated in Figure 1 .", "cite_spans": [], "ref_spans": [{"start": 160, "end": 161, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Method", "sec_num": "3."}, {"text": "3.1. Preliminary: Self-Supervised ANCL Let f , g, and h be the encoder, projector, and predictor of the online branch, respectively, and f and g be the encoder and projector of the target branch, respectively. For a data point x,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3."}, {"text": "let z = (g • f )(x) and p = (h • g • f )(x)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3."}, {"text": "be the output of the projector and predictor, respectively. In self-supervised ANCL, two views x 1 and x 2 are generated from the data x through augmentation, and the model learns to minimize the distance between these views encoded at different levels: it compares the prediction of the first view", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3."}, {"text": "p 1 = (h • g • f )(x 1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3."}, {"text": ") with the projection of the second view z 2 = (g • f )(x 2 ). It has been observed that the asymmetric architecture introduced by the predictor h helps prevent representation collapse by predicting the latent feature of the second view z 2 from that of the first view z 1 , i.e., z 2 ≃ p 1 = h(z 1 ) (<PERSON> & <PERSON>, 2021) . The self-supervised ANCL loss ℓ ssl is expressed as:", "cite_spans": [{"start": 301, "end": 318, "text": "(<PERSON> & He, 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ℓ ssl (p 1 , z 2 ) = ∥p 1 -sg (z 2 )∥ 2 2 , (", "eq_num": "1"}], "section": "Method", "sec_num": "3."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3."}, {"text": "where sg is the stop-gradient operation and p 1 and z 2 are L2normalized. The inclusion of stop-gradient is also crucial for preventing collapsing, making it an essential component of the loss formulation (<PERSON> & He, 2021) .", "cite_spans": [{"start": 205, "end": 222, "text": "(<PERSON> & He, 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3."}, {"text": "The target branch can either share parameters with the online branch (<PERSON> & He, 2021) , or exhibit a momentum network (<PERSON><PERSON> et al., 2020) . When a momentum network is employed, its parameters follow the exponential moving average (EMA) update rule:", "cite_spans": [{"start": 69, "end": 86, "text": "(<PERSON> & He, 2021)", "ref_id": null}, {"start": 119, "end": 139, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3."}, {"text": "θ g• f ← m • θ g• f + (1 -m) • θ g•f ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3."}, {"text": "where m is the momentum, θ g•f is the set of learnable parameters in f and g. The parameters of the target model θ g• f are initialized to those of the online model θ g•f .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3."}, {"text": "We propose to enhance supervised ANCL by integrating supervision through an additional loss function: for an anchor x 1 and its supervised target x sup 2 sharing the same label y, the loss minimizes the distance between p 1 = (h•g •f )(x 1 ) and z sup 2 = (g • f )(x sup 2 ). However, the additional loss may not always be effective, because the current batch might not contain any samples sharing the same label as the anchor, particularly when the batch size is small.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "To address this issue, we introduce a target pool to ensure the presence of targets sharing the same label as each anchor in the batch, regardless of batch size. Similar to the memory bank utilized in prior works (<PERSON> et al., 2018) , the target pool Q is a queue storing targets z 2 along with their corresponding labels. The target pool offers another advantage that positive samples from the target pool help mitigate collapse because they are updated more slowly than those sampled from the batch, as empirically observed in Table 8 . The proposed target pool is flexible in its design, such that it can be a vanilla queue, a collection of per-class queues ensuring the presence of targets from all labels even when the queue size is small, or a set of learnable class prototypes; the impact of these design choices is investigated in Table 7 . Now, we sample the supervised target z sup 2 sharing the same class as the anchor x from the target pool Q. Specifically, we sample M targets and average them to formulate the supervised ANCL loss ℓ sup :", "cite_spans": [{"start": 213, "end": 230, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF58"}], "ref_spans": [{"start": 533, "end": 534, "text": "8", "ref_id": null}, {"start": 843, "end": 844, "text": "7", "ref_id": "TABREF6"}], "eq_spans": [], "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ℓ sup (p 1 ,z sup 2 ) = p 1 -sg z sup 2 2 2 , z sup 2 = 1 M z ′ 2 ∈Qy z ′ 2 ,", "eq_num": "(2)"}], "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "where p 1 and z ′ 2 are L2-normalized and Q y ⊆ Q is the set of M targets sampled from Q sharing the same label y as x. We sample all positives in Q in experiments, and the effect of M is discussed in Appendix F.2. Finally, the total loss is defined by the convex combination of ℓ ssl and ℓ sup :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ℓ(p 1 , z 2 , z sup 2 ) = α•ℓ ssl (p 1 , z 2 )+(1-α)•ℓ sup (p 1 , z sup 2 ),", "eq_num": "(3)"}], "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "where α ∈ [0, 1] adjusts the contribution of ℓ ssl and ℓ sup , and we symmetrize the loss in experiments following the convention. We argue that the introduction of ℓ sup reduces intra-class variance and α adjusts the amount of reduction, where details can be found in Section 4.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "Note that our strategy for incorporating supervision into the loss differs from that of SUPCON (<PERSON><PERSON><PERSON> et al., 2020) . We first average the supervised loss before combining it with the self-supervised loss, whereas SUPCON weights all persample losses equally, regardless of whether they are selfsupervised or supervised. Since our focus is on analyzing the overall effects of self-supervised and supervised losses rather than per-sample losses, our strategy aligns with the analysis presented in the following section.", "cite_spans": [{"start": 95, "end": 116, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "Supervised ANCL", "sec_num": "3.2."}, {"text": "In this section, we analyze the effect of supervision in ANCL. We argue that incorporating supervision into ANCL reduces intra-class variance, and that its contribution should be adjusted to achieve better representations. Detailed mathematical proofs are provided in Appendix A.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Analysis of the Effect of Supervision", "sec_num": "4."}, {"text": "For simplicity in our analysis, we adopt several assumptions from <PERSON><PERSON> et al. (2021) ; <PERSON><PERSON> et al. (2023) :", "cite_spans": [{"start": 66, "end": 84, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF53"}, {"start": 87, "end": 105, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF62"}], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "4.1."}, {"text": "Assumption 4.1. The encoder followed by the projector g • f and the predictor h are linear: z = (g • f )(x) = W x and p = h(z) = W p z, where W p is a symmetric matrix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "4.1."}, {"text": "Assumption 4.2. The distribution of the data augmentation P ( X|X) has a mean X and a covariance matrix σ 2 e I.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "4.1."}, {"text": "While previous studies on self-supervised ANCL assume that the distribution of the input data has a zero mean and a scaled identity covariance matrix, class-conditional distributions should be considered when incorporating supervision. Specifically, we assume the class-conditional and class-prior distributions over C classes as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "4.1."}, {"text": "Assumption 4.3. The class-prior distribution follows the uniform distribution:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "4.1."}, {"text": "P (Y = y) = 1/C.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "4.1."}, {"text": "Assumption 4.4. For an input data X and its class Y , the conditional distribution P (X|Y ) is characterized by a mean µ y and a covariance matrix Σ y , where the total mean and total covariance matrix are zero and the identity matrix, respectively: y µ y = 0 and S T = 1 C y µ y µ ⊤ y + Σ y = I.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "4.1."}, {"text": "Assumption 4.3 is made for simplicity of analysis; our analysis holds without this assumption, albeit the derivation becomes more complex. Assumption 4.4 can be naturally satisfied through data whitening.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Problem Setup", "sec_num": "4.1."}, {"text": "For simplicity, assume we sample one target from the pool, i.e., M = 1. We first express the loss in Eq. ( 3) with constraints to ensure the L2 normalization of features:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ℓ = α ∥W p z 1 -z 2 ∥ 2 2 + (1 -α) W p z 1 -z sup 2 2 2 = W p z 1 -α • z 2 + (1 -α) • z sup 2 2 2 + const s.t. ∥z 2 ∥ 2 2 = z sup 2 2 2 = ∥W p z 1 ∥ 2 2 = 1,", "eq_num": "(4)"}], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "where we omit stop-gradient applied to z 2 and z sup 2 for brevity, and the equality in the second line holds due to the linearity of the L2 loss between L2-normalized features with respect to the target (<PERSON> et al., 2021b) . Hence, this optimization can be interpreted as mapping one view z 1 to an interpolated target between another view z 2 and the supervised target z sup 2 . Intuitively, when α = 1, the model cannot determine the exact augmentation applied to x 2 by observing x 1 , such that it predicts z 2 from z 1 through low-rank approximation via principal component analysis (PCA) (<PERSON><PERSON><PERSON> et al., 2023) . Similarly, when α = 0, the model cannot infer the exact supervised target z sup 2 by observing z 1 ; instead, it predicts z sup 2 by mapping z 1 to the class centroid. Here, it has been known that least squares with targets independent of each other (ignoring centering, if applied) is equivalent to linear discriminant analysis (LDA) (<PERSON> & Kim, 2015) , where LDA simultaneously maximizes between-class scatter and minimizes within-class scatter. Hence, we can hypothesize that incorporating supervision into ANCL reduces intra-class variance, and the degree of reduction is controlled by α.", "cite_spans": [{"start": 204, "end": 223, "text": "(<PERSON> et al., 2021b)", "ref_id": null}, {"start": 595, "end": 619, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF50"}, {"start": 957, "end": 974, "text": "(<PERSON> & <PERSON>, 2015)", "ref_id": "BIBREF31"}], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "To prove that incorporating supervision into ANCL reduces intra-class variance, we establish the following: 1) the optimal predictor W * p generates features of data with reduced intra-class variance by a factor of α, and 2) the optimal W p and W share the same eigenspace, thereby W learns to reduce intra-class variance of features.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "First, we formulate the <PERSON><PERSON>ngian function of Eq. ( 4) and take the expectation over x 1 , x 2 , and x sup 2 :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L=2-2α•tr W ⊤ p E z 2 z ⊤ 1 -2(1-α)•tr W ⊤ p E z sup 2 z ⊤ 1 +λ 1 tr E z 2 z ⊤ 2 -1 +λ 2 tr E z sup 2 z sup⊤ 2 -1 +λ 3 tr W ⊤ p W p E z 1 z ⊤ 1 -1 ,", "eq_num": "(5)"}], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "where λ 1 , λ 2 , and λ 3 are the <PERSON><PERSON><PERSON> multipliers. Note that x and x sup are independently sampled from the conditional distribution P (X|Y = y), x 1 and x 2 are independently sampled from P ( X|X = x), and x sup 2 is sampled from P ( X|X = x sup ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Proposition 4.5. The covariance matrices of features", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "E z 1 z ⊤ 1 , <PERSON> z 2 z ⊤ 1 , and E z sup 2 z ⊤ 1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "share the same eigenspace in the data space.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Proof. From Assumptions 4.1 to 4.4,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E z 1 z ⊤ 1 = W (S B + S W + S e ) W ⊤ , E z 2 z ⊤ 1 = W (S B + S W ) W ⊤ = W W ⊤ , E z sup 2 z ⊤ 1 = W S B W ⊤ ,", "eq_num": "(6)"}], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "S B = 1 C y µ y µ ⊤ y is the inter-class covariance, S W = 1 C y Σ y is the intra-class covariance, and S e = σ 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "e I is the variance of the augmentation noise. Let S B = V Λ B V ⊤ be the eigendecomposition, where V is an orthogonal matrix and Λ B is a diagonal matrix of the eigenvalues. Then, S T = S B + S W and S e share the same eigenspace with S B , as they are (scaled) identity matrices.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E z 1 z ⊤ 1 = W V Λ B + Λ W + σ 2 e I V ⊤ W ⊤ , E z 2 z ⊤ 1 = W V (Λ B + Λ W ) V ⊤ W ⊤ , E z sup 2 z ⊤ 1 = W V Λ B V ⊤ W ⊤ ,", "eq_num": "(7)"}], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "where Λ W = I -Λ B is the eigenvalue matrix of S W . It can be seen that the covariance matrices of features in Eq. ( 7) share the same eigenspace in the data space.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Then, we apply the expressions in Proposition 4.5 to the optimal predictor W * p obtained from Eq. ( 5): Theorem 4.6. For an arbitrary W , the optimal predictor W * p that minimizes the loss in Eq. ( 5) is given by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "W * p = 1 λ 3 W V (Λ B +αΛ W ) Λ B +Λ W +σ 2 e I -1 V ⊤ W + ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "where W + is the <PERSON> inverse (<PERSON>, 1955) .", "cite_spans": [{"start": 39, "end": 54, "text": "(<PERSON><PERSON>, 1955)", "ref_id": "BIBREF46"}], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "From Theorem 4.6, the optimal predictor W * p can be interpreted through a sequence of hypothetical transformations: 1) mapping features to the data space, 2) eliminating the augmentation noise and reducing the intra-class variance by a factor of α, and 3) mapping back to the feature space.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Next, we show that W * p and W * share the same eigenspace.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Theorem 4.7. The optimal predictor W * p and the optimal model W * that minimizes the loss in Eq. (5) satisfy", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "W * ⊤ p W * p ≈ W * W * ⊤ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Note that Theorem 4.7 holds in self-supervised ANCL as shown in <PERSON><PERSON> et al. (2021) ; <PERSON> et al. (2022) , and it remains valid when supervision is incorporated.", "cite_spans": [{"start": 64, "end": 82, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF53"}, {"start": 85, "end": 102, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "Finally, we conclude that W * learns to reduce intra-class variance, as W * p generates features of data with reduced intra-class variance by a factor of α from Theorem 4.6, and W * imitates this behavior according to Theorem 4.7.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supervision Reduces Intra-Class Variance", "sec_num": "4.2."}, {"text": "In the proposed supervised ANCL loss in Eq. ( 3), the coefficient α adjusts the contribution of supervision: decreasing α results in increasing this contribution, thereby reducing intra-class variance, as proved in Section 4.2. Ideally, when intra-class variance is too small, all data within each class converge to a single point, leading to class collapse: data within each class become indistinguishable (<PERSON><PERSON><PERSON> et al., 2020) . Thus, we argue that balancing the contributions of supervision and self-supervision is crucial to achieve semantically aligned yet well-distributed representations in supervised ANCL, leading to the generalization of learned representations; intuitively, the ideal semantic latent space should retain intra-class variance to distinguish data instances.", "cite_spans": [{"start": 407, "end": 428, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "Table 1 . SUPSIAM results with different α on the toy dataset and ImageNet-100 in several metrics: the self-supervised loss in Eq. ( 1), the supervised loss in Eq. ( 2), the intra-class variance, the relative intra-class variance (%), and the accuracy of k-NN and linear probing (%). For the accuracies, the best results are highlighted in bold and the second-best results are underlined. ", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "S W = E y,z ∥z -μy ∥ 2 2 , S T = E z ∥z -μ∥ 2 2 , (", "eq_num": "8"}], "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "where μy is the y-th class mean and μ is the total mean of features, and the expectation is taken over training dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "In Table 1 , ℓ ssl decreases while ℓ sup increases as α increases, which confirms that the contribution of each loss is adjusted as expected. Additionally, the intra-class variance is proportional to α, as proved in Section 4.2. However, the accuracy of k-NN and linear probing exhibits different trends, with the best accuracy achieved when α is between 0 and 1. This supports our claim that while incorporating supervision into ANCL aids in learning semantically aligned representations, excessively reducing intra-class variance may hinder the generalization of learned representations, resulting in diminishing performance on unseen test data.", "cite_spans": [], "ref_spans": [{"start": 9, "end": 10, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "Figure 2 visualizes the feature space via t-SNE (<PERSON> & <PERSON>, 2008) . When α = 0.5, the class distributions are well-separated while retaining intra-class variance. Decreasing α results in more densely clustered results by skewing the feature space, which might be detrimental to generalization; e.g., the model is overconfident in its predictions for downstream classification tasks. Conversely, increasing α To assess the transferability of learned representations, we conduct transfer learning scenarios in Table 2 . Specifically, we consider three downstream classes, where their means are either interpolated or extrapolated from the pretraining classes, and the scale of the covariance matrix of downstream classes is adjusted by σ to control the difficulty of downstream tasks. As shown in Table 2 , supervised ANCL consistently outperforms self-supervised ANCL (α = 1) across all scenarios, highlighting the effectiveness of incorporating supervision into ANCL. Moreover, the best performance is achieved when 0 < α < 1, suggesting that balancing the contributions of supervision and self-supervision is crucial, i.e., excessively reducing intra-class variance is detrimental to representation learning.", "cite_spans": [{"start": 48, "end": 71, "text": "(Maaten & Hinton, 2008)", "ref_id": "BIBREF38"}], "ref_spans": [{"start": 7, "end": 8, "text": "2", "ref_id": null}, {"start": 520, "end": 521, "text": "2", "ref_id": "TABREF1"}, {"start": 807, "end": 808, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "Next, to confirm the scalability of our observations to real-world scenarios, we conduct a similar experiment on ImageNet-100 (<PERSON><PERSON> et al., 2009; <PERSON><PERSON> et al., 2020) by replacing the encoder with ResNet-50 (<PERSON> et al., 2016) and the projector and predictor with MLPs, respectively. As shown in the bottom of Table 1 , the observations remain mostly consistent; although both supervised loss and intra-class variance slightly decrease when α increases from 0.0 to 0.2, we conjecture that this is due to the non-linearity of the optimization. These results further support our claim that balancing the contributions of supervision and self-supervision Similar to the toy experiments, we evaluate the transferability of learned representations in real-world scenarios by conducting transfer learning experiments. Specifically, we apply linear probing to the SUPSIAM-pretrained models on downstream datasets for fine-grained classification tasks, including CUB-200-2011 (<PERSON><PERSON><PERSON> et al., 2010) , Stanford Dogs (<PERSON> et al., 2011) , and Oxford-IIIT Pets (<PERSON> et al., 2012) . As shown in Table 3 , the transfer learning performance exhibits trends similar to those in Table 1 : incorporating supervision into ANCL is beneficial, and balancing the contributions of supervision and self-supervision improves the generalization of representations.", "cite_spans": [{"start": 126, "end": 145, "text": "(<PERSON> et al., 2009;", "ref_id": "BIBREF10"}, {"start": 146, "end": 164, "text": "<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF52"}, {"start": 205, "end": 222, "text": "(<PERSON> et al., 2016)", "ref_id": "BIBREF19"}, {"start": 951, "end": 963, "text": "CUB-200-2011", "ref_id": null}, {"start": 964, "end": 987, "text": "(<PERSON><PERSON><PERSON> et al., 2010)", "ref_id": "BIBREF57"}, {"start": 1004, "end": 1025, "text": "(<PERSON><PERSON><PERSON> et al., 2011)", "ref_id": "BIBREF25"}, {"start": 1049, "end": 1070, "text": "(<PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF45"}], "ref_spans": [{"start": 312, "end": 313, "text": "1", "ref_id": null}, {"start": 1091, "end": 1092, "text": "3", "ref_id": "TABREF2"}, {"start": 1171, "end": 1172, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "To further elucidate the effect of α in real-world scenarios, we present t-SNE visualizations of latent features from 20 classes, consisting of 15 dogs and 5 birds, subsampled from ImageNet-100. As shown in Figure 3 , classes overlap when no supervision is provided, i.e., when α = 1, and the latent features form more compact clusters as α decreases. Notably, some dog classes (e.g., \"Doberman\" and \"Rottweiler\") overlap when α is small, around 0.0 and 0.2, while they are well-separated when α = 0.5. This implies that excessively reducing intra-class variance with small α might result in collapsing fine-grained classes, which could be detrimental to downstream tasks. ", "cite_spans": [], "ref_spans": [{"start": 214, "end": 215, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Effect of Reducing Intra-Class Variance", "sec_num": "4.3."}, {"text": "In this section, we provide experimental results across various datasets and tasks to demonstrate the effectiveness of supervision in ANCL. We also compare CL methods to confirm that ANCL performance is competitive to CL. Detailed experimental settings are provided in Appendix C.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment", "sec_num": "5."}, {"text": "We consider two ANCL methods, SIMSIAM (<PERSON> & He, 2021) and BYOL (<PERSON><PERSON> et al., 2020) , as our baselines, along with their supervised variations, SUPSIAM and SUPBYOL, as our proposed methods. Additionally, we compare two CL methods, SIMCLR (<PERSON> et al., 2020a) and MOCO-V2 (<PERSON> et al., 2020b) , and their supervised variations, SUPCON (<PERSON><PERSON><PERSON> et al., 2020) and SUPMOCO (<PERSON><PERSON><PERSON> et al., 2021) . Each model consists of a ResNet-50 encoder (<PERSON> et al., 2016) followed by a 2-layer MLP projector and predictor, except for SIMSIAM and SUP<PERSON><PERSON>, which utilize a 3-layer MLP projector following the original configuration by <PERSON> & He (2021) . We pretrain models on ImageNet-100 (<PERSON><PERSON> et al., 2009; <PERSON><PERSON> et al., 2020) for 200 epochs with a batch size of 128. For data augmentation, we apply random crop, random horizontal flip, color jitter, random grayscale, and Gaussian blur, following <PERSON> et al. (2020a) .", "cite_spans": [{"start": 38, "end": 55, "text": "(<PERSON> & He, 2021)", "ref_id": null}, {"start": 65, "end": 85, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF16"}, {"start": 240, "end": 260, "text": "(<PERSON> et al., 2020a)", "ref_id": null}, {"start": 273, "end": 293, "text": "(<PERSON> et al., 2020b)", "ref_id": null}, {"start": 336, "end": 357, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF26"}, {"start": 370, "end": 393, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF40"}, {"start": 439, "end": 456, "text": "(<PERSON> et al., 2016)", "ref_id": "BIBREF19"}, {"start": 618, "end": 634, "text": "<PERSON> & He (2021)", "ref_id": null}, {"start": 672, "end": 691, "text": "(<PERSON> et al., 2009;", "ref_id": "BIBREF10"}, {"start": 692, "end": 710, "text": "<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF52"}, {"start": 882, "end": 901, "text": "<PERSON> et al. (2020a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Pretraining", "sec_num": "5.1."}, {"text": "For methods utilizing the target pool, we set the size of the target pool |Q| to 8192 and obtain the supervised target z sup 2 by sampling and averaging all positives in the target pool1 unless otherwise stated. The coefficient α adjusting the contribution of the self-supervised and supervised loss is 0.5, unless otherwise stated. We repeat all experiments with three pretrained models with different random seeds and report the average performance.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Pretraining", "sec_num": "5.1."}, {"text": "We evaluate the quality of representations on the pretrained distribution through a comparison of linear probing per- formance on ImageNet-100. Specifically, we take the pretrained and frozen backbone, and train a linear classifier on top of it, following the common protocol in prior works (<PERSON> et al., 2020a; b; <PERSON><PERSON> et al., 2020; <PERSON> & He, 2021) .", "cite_spans": [{"start": 291, "end": 311, "text": "(<PERSON> et al., 2020a;", "ref_id": null}, {"start": 312, "end": 314, "text": "b;", "ref_id": null}, {"start": 315, "end": 334, "text": "<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF16"}, {"start": 335, "end": 351, "text": "<PERSON> & He, 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Linear Evaluation", "sec_num": "5.2."}, {"text": "As shown in Table 4 , incorporating supervision into ANCL enhances linear probing performance on the pretraining dataset. This suggests that representations learned with supervision more effectively encode the semantic information of the pretrained data distribution.", "cite_spans": [], "ref_spans": [{"start": 18, "end": 19, "text": "4", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Linear Evaluation", "sec_num": "5.2."}, {"text": "To assess the generalizability beyond classification tasks, we evaluate pretraining methods on an object detection task. Following <PERSON> et al. (2020) , we initialize Faster R-CNN (<PERSON> et al., 2015) with each pretrained model and fine-tune it on the VOC07+12 training dataset (<PERSON><PERSON> et al., 2010) . We measure performance using the COCO evaluation metrics (<PERSON> et al., 2014) on the VOC07 test dataset.", "cite_spans": [{"start": 131, "end": 147, "text": "<PERSON> et al. (2020)", "ref_id": "BIBREF20"}, {"start": 177, "end": 195, "text": "(<PERSON> et al., 2015)", "ref_id": "BIBREF49"}, {"start": 273, "end": 298, "text": "(<PERSON><PERSON> et al., 2010)", "ref_id": "BIBREF12"}, {"start": 358, "end": 376, "text": "(<PERSON> et al., 2014)", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Object Detection", "sec_num": "5.3."}, {"text": "As shown on the right side of Table 4 , incorporating supervision into ANCL improves object detection performance, resulting in the best overall performance. In contrast, the performance gain from supervision in CL is marginal or often detrimental, which aligns with the findings from prior works (<PERSON><PERSON><PERSON> et al., 2020) . This suggests that supervised ANCL yields more generalizable representations, with the potential to achieve superior performance across various downstream tasks.", "cite_spans": [{"start": 297, "end": 318, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF26"}], "ref_spans": [{"start": 36, "end": 37, "text": "4", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Object Detection", "sec_num": "5.3."}, {"text": "For transfer learning, we evaluate the top-1 accuracy across 11 downstream datasets: CIFAR10/CIFAR100 (<PERSON><PERSON><PERSON> & <PERSON>, 2009) , DTD (<PERSON><PERSON><PERSON><PERSON> et al., 2014) , Food (<PERSON><PERSON> et al., 2014) , MIT67 (Quattoni & Torralba, 2009) , SUN397 (<PERSON> et al., 2010 ), Caltech101 (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2004) , CUB200 (<PERSON><PERSON><PERSON> et al., 2010) , Dogs (<PERSON><PERSON> et al., 2011; <PERSON><PERSON> et al., 2009) , Flowers (Nilsback & Zisserman, 2008) , and Pets (<PERSON><PERSON> et al., 2012) , where detailed information is described in Appendix D. For evaluation, we follow the linear probing protocol for transfer learning in prior works (<PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2021a) .", "cite_spans": [{"start": 102, "end": 129, "text": "(<PERSON><PERSON><PERSON><PERSON> & Hinton, 2009)", "ref_id": "BIBREF28"}, {"start": 136, "end": 157, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF9"}, {"start": 165, "end": 187, "text": "(<PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF2"}, {"start": 196, "end": 223, "text": "(Quattoni & Torralba, 2009)", "ref_id": "BIBREF47"}, {"start": 233, "end": 251, "text": "(<PERSON> et al., 2010", "ref_id": "BIBREF59"}, {"start": 252, "end": 288, "text": "), Caltech101 (<PERSON><PERSON><PERSON><PERSON> et al., 2004)", "ref_id": null}, {"start": 298, "end": 321, "text": "(<PERSON><PERSON><PERSON> et al., 2010)", "ref_id": "BIBREF57"}, {"start": 329, "end": 350, "text": "(<PERSON><PERSON><PERSON> et al., 2011;", "ref_id": "BIBREF25"}, {"start": 351, "end": 369, "text": "<PERSON> et al., 2009)", "ref_id": "BIBREF10"}, {"start": 380, "end": 408, "text": "(<PERSON><PERSON><PERSON> & Zisserman, 2008)", "ref_id": "BIBREF42"}, {"start": 420, "end": 441, "text": "(<PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF45"}, {"start": 590, "end": 614, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF27"}, {"start": 615, "end": 633, "text": "<PERSON> et al., 2021a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Transfer Learning via Linear Evaluation", "sec_num": "5.4."}, {"text": "As shown in Table 5 , incorporating supervision improves performance across all pretraining methods. Among them, supervised ANCL methods achieve the best performance: SUPBYOL and SUPSIAM outperform others on 9 out of 11 datasets, demonstrating the superiority of supervised ANCL.", "cite_spans": [], "ref_spans": [{"start": 18, "end": 19, "text": "5", "ref_id": "TABREF4"}], "eq_spans": [], "section": "Transfer Learning via Linear Evaluation", "sec_num": "5.4."}, {"text": "Between supervised ANCL methods, SUPBYOL exhibits better performance than SUPSIAM in terms of the average rank, which might be due to the effect of momentum network. Notably, while the performance gain from incorporating supervision into ANCL is relatively small compared to CL because the self-supervised versions of ANCL already exhibit strong performance, we observe a significant improvement on fine-grained datasets, such as CUB200, Dogs, and Pets. This suggests that learning semantically aligned representations while retaining intra-class variance in ANCL is crucial for recognizing fine-grained information.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Transfer Learning via Linear Evaluation", "sec_num": "5.4."}, {"text": "To assess the generalizability of learned representations under limited conditions, we conduct transfer learning experi-ments on few-shot classification tasks following the linear probing protocol for few-shot learning in <PERSON> et al. (2021a) .", "cite_spans": [{"start": 222, "end": 240, "text": "<PERSON> et al. (2021a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Few-Shot Classification", "sec_num": "5.5."}, {"text": "We evaluate the accuracy of 5-way 1-shot and 5-way 5-shot scenarios over 2000 episodes across 8 downstream datasets: Aircraft (<PERSON><PERSON> et al., 2013) , CUB200 (<PERSON><PERSON><PERSON> et al., 2010) , FC100 (<PERSON><PERSON><PERSON> et al., 2018) , Flowers (Nilsback & Zisserman, 2008) , Fungi (Schroeder & Cui, 2018) , Omniglot (Lake et al., 2015) , DTD (Cim<PERSON>i et al., 2014) , and Traffic Signs (<PERSON><PERSON> et al., 2013) . Table 6 shows a similar trend to other experiments that incorporating supervision improves both CL and ANCL, while supervised ANCL achieves the best performance in most cases.", "cite_spans": [{"start": 126, "end": 145, "text": "(<PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF39"}, {"start": 155, "end": 178, "text": "(<PERSON><PERSON><PERSON> et al., 2010)", "ref_id": "BIBREF57"}, {"start": 187, "end": 210, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF43"}, {"start": 221, "end": 249, "text": "(<PERSON><PERSON><PERSON> & Zisserman, 2008)", "ref_id": "BIBREF42"}, {"start": 258, "end": 281, "text": "(Schroeder & Cui, 2018)", "ref_id": "BIBREF51"}, {"start": 293, "end": 312, "text": "(<PERSON> et al., 2015)", "ref_id": "BIBREF29"}, {"start": 319, "end": 340, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF9"}, {"start": 361, "end": 382, "text": "(<PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF22"}], "ref_spans": [{"start": 391, "end": 392, "text": "6", "ref_id": "TABREF5"}], "eq_spans": [], "section": "Few-Shot Classification", "sec_num": "5.5."}, {"text": "In this section, we investigate the design choices for the target pool. In our experiments, the pretraining dataset ImageNet-100 consists of 100 classes, such that the probability of missing any class in the target pool is negligible with a target pool size of 8192. However, with a larger number of classes, some classes might not exist in the target pool if it is updated in a class-agnostic manner. To address this concern, we consider two alternative target pool designs: 1) managing class-wise queues as the target pool, and 2) maintaining learnable class prototypes using the EMA update rule. Additionally, we adjust the size of the class-wise queues to determine the optimal number of latent features required to ensure good performance.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation Study on Target Pool Design", "sec_num": "5.6."}, {"text": "As shown in Table 7 , performance remains consistent regardless of the target pool design. For the class-wise queues, increasing the number of features stored per class slightly enhances performance, with the best performance observed at 20 features per class, though the gain is overall marginal. In all designs, the size of the target pool grows proportionally to the number of classes and/or the feature dimension, which is equivalent to a linear classifier, such that its memory consumption is negligible; e.g., the linear classifier takes only 2% of the parameters in ResNet-50. Nonetheless, a more sophisticated design of the target pool might be effective, which we leave for future works. Table 8 . Ablation study on the target pool (Pool) and the momentum network (EMA) for avoiding collapse while improving representations learned via supervised ANCL on CIFAR100.", "cite_spans": [], "ref_spans": [{"start": 18, "end": 19, "text": "7", "ref_id": "TABREF6"}, {"start": 703, "end": 704, "text": "8", "ref_id": null}], "eq_spans": [], "section": "Ablation Study on Target Pool Design", "sec_num": "5.6."}, {"text": "Pool EMA Collapse k-NN", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation Study on Target Pool Design", "sec_num": "5.6."}, {"text": "✗ ✗ ✓ 1.00 ✓ ✗ ✗ 73.92 ✗ ✓ ✗ 73.32 ✓ ✓ ✗ 74.55", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation Study on Target Pool Design", "sec_num": "5.6."}, {"text": "In this section, we investigate when collapse occurs in supervised ANCL. Specifically, we investigate the effect of the target pool and the momentum network, where the method only with the target pool is essentially SUPSIAM, and the one with both components corresponds to SUPBYOL. We pretrain ResNet-18 followed by a 2-layer MLP projector and predictor on CIFAR100.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation Study on Representation Collapse", "sec_num": "5.7."}, {"text": "As observed in Table 8 , employing either the target pool or the momentum network effectively prevents collapse. We hypothesize that updating the target differently from the anchor helps to prevent collapse, which is the common behavior of both strategies.", "cite_spans": [], "ref_spans": [{"start": 21, "end": 22, "text": "8", "ref_id": null}], "eq_spans": [], "section": "Ablation Study on Representation Collapse", "sec_num": "5.7."}, {"text": "In this paper, we study supervised asymmetric noncontrastive learning (ANCL) for representation learning.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "We demonstrate that introducing supervision to ANCL reduces intra-class variance, and that balancing the contributions of the supervised and self-supervised losses is crucial to learn good representations. We experiment the proposed supervised ANCL methods with baselines across various datasets and tasks, demonstrating the effectiveness of supervised ANCL. We believe our work motivates future research to integrate supervised ANCL into their applications.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "(Non-)contrastive learning typically requires substantial training costs; for instance, training ResNet-50 with MoCo-v2 (<PERSON> et al., 2020b) for 800 epochs requires 9 days on 8 V100 GPUs, raising concerns about environmental impacts, such as carbon emissions. However, the proposed idea of incorporating supervision leads to learning better representations while maintaining similar computational complexity comparable to that of self-supervised learning. This suggests that supervision can mitigate computational demands and potentially address associated environmental concerns.", "cite_spans": [{"start": 120, "end": 140, "text": "(<PERSON> et al., 2020b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "A.1. Derivation of Eq. ( 5) To derive this, recall the supervised ANCL loss with constraints in Eq. ( 4):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ℓ = α ∥W p z 1 -z 2 ∥ 2 2 + (1 -α) W p z 1 -z sup 2 2 2 s.t. ∥z 2 ∥ 2 2 = z sup 2 2 2 = ∥W p z 1 ∥ 2 2 = 1.", "eq_num": "(4)"}], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "We first expand the loss in Eq. ( 4) and apply constraints to simplify the expression:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "ℓ = α ∥W p z 1 ∥ 2 2 + ∥z 2 ∥ 2 2 -2z ⊤ 1 W ⊤ p z 2 + (1 -α) ∥W p z 1 ∥ 2 2 + z sup 2 2 2 -2z ⊤ 1 W ⊤ p z sup 2 = α 2 -2z ⊤ 1 W ⊤ p z 2 + (1 -α) 2 -2z ⊤ 1 W ⊤ p z sup 2 = 2 -2α • z ⊤ 1 W ⊤ p z 2 + 2(1 -α) • z ⊤ 1 W ⊤ p z sup 2 . (A.1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Then, the <PERSON><PERSON>ngian function is formulated as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "L = 2 -2α • z ⊤ 1 W ⊤ p z 2 -2(1 -α) • z ⊤ 1 W ⊤ p z sup 2 + λ 1 z ⊤ 2 z 2 -1 + λ 2 z sup⊤ 2 z sup 2 -1 + λ 3 z ⊤ 1 W ⊤ p W p z 1 -1 = 2 -2α • tr W ⊤ p z 2 z ⊤ 1 -2(1 -α) • tr W ⊤ p z sup 2 z ⊤ 1 + λ 1 tr z 2 z ⊤ 2 -1 + λ 2 tr z sup 2 z sup⊤ 2 -1 + λ 3 tr W ⊤ p W p z 1 z ⊤ 1 -1 , (A.2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "where λ 1 , λ 2 and λ 3 are the <PERSON><PERSON><PERSON> multipliers. Finally, taking the expectation over x 1 , x 2 , and x sup 2 yields Eq. ( 5):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L = 2 -2α • tr W ⊤ p E z 2 z ⊤ 1 -2(1 -α) • tr W ⊤ p E z sup 2 z ⊤ 1 + λ 1 tr E z 2 z ⊤ 2 -1 + λ 2 tr E z sup 2 z sup⊤ 2 -1 + λ 3 tr W ⊤ p W p E z 1 z ⊤ 1 -1 .", "eq_num": "(5)"}], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "A.2. Proof of Proposition 4.5 Proposition 4.5. The covariance matrices of features E z 1 z ⊤ 1 , E z 2 z ⊤ 1 , and E z sup 2 z ⊤ 1 share the same eigenspace in the data space.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Proof. Let S B = 1 C y µ y µ ⊤ y be the inter-class covariance, S W = 1 C y Σ y be the intra-class covariance, and S e = σ 2 e I be the variance of the augmentation noise.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E x1 z 1 z ⊤ 1 = W E X E X|X x 1 x ⊤ 1 W ⊤ = W E X xx ⊤ + σ 2 e I W ⊤ = W E Y E X|Y xx ⊤ + σ 2 e I W ⊤ = W E Y µ y µ ⊤ y + Σ y + σ 2 e I W ⊤ = W 1 C y µ y µ ⊤ y + Σ y + σ 2 e I W ⊤ = W (S B + S W + S e ) W ⊤ = (1 + σ 2 e )W W ⊤ , E x1,x2 z 2 z ⊤ 1 = W E X E X|X x 2 x ⊤ 1 W ⊤ = W E X E X|X [x 2 ] E X|X x ⊤ 1 W ⊤ = W E X xx ⊤ W ⊤ = W E Y E X|Y xx ⊤ W ⊤ = W E Y µ y µ ⊤ y + Σ y W ⊤ = W 1 C y µ y µ ⊤ y + Σ y W ⊤ = W (S B + S W ) W ⊤ = W W ⊤ , E x1,x sup 2 z sup 2 z ⊤ 1 = W E X E X|X x sup 2 x ⊤ 1 W ⊤ = W E X E X|X x sup 2 E X|X x ⊤ 1 W ⊤ = W E X x sup x ⊤ W ⊤ = W E Y E X|Y x sup x ⊤ W ⊤ = W E Y E X|Y [x sup ] E X|Y x ⊤ W ⊤ = W E Y µ y µ ⊤ y W ⊤ = W 1 C y µ y µ ⊤ y W ⊤ = W S B W ⊤ .", "eq_num": "(6)"}], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "On the Effectiveness of Supervision in Asymmetric Non-Contrastive Learning", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Let S B = V Λ B V ⊤ be the eigendecomposition, where V is an orthogonal matrix and Λ B is a diagonal matrix of the eigenvalues. Then, S T = S B + S W and S e share the same eigenspace with S B , as they are (scaled) identity matrices.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E z 1 z ⊤ 1 = W (S B + S W + S e ) W ⊤ = W V Λ B + Λ W + σ 2 e I V ⊤ W ⊤ , E z 2 z ⊤ 1 = W (S B + S W ) W ⊤ = W V (Λ B + Λ W ) V ⊤ W ⊤ , E z sup 2 z ⊤ 1 = W S B W ⊤ = W V Λ B V ⊤ W ⊤ ,", "eq_num": "(7)"}], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "where Λ W = I -Λ B is the eigenvalue matrix of S W . It can be seen that the covariance matrices of features in Eq. ( 7) share the same eigenspace in the data space.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "A.3. <PERSON><PERSON> <PERSON> Theorem 4.6", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Theorem 4.6. For an arbitrary W , the optimal predictor W * p that minimizes the loss in Eq. ( 5) is given by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W * p = 1 λ 3 W V (Λ B +αΛ W ) Λ B +Λ W +σ 2 e I -1 V ⊤ W + ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "where W + is the <PERSON> inverse (<PERSON>, 1955) .", "cite_spans": [{"start": 39, "end": 54, "text": "(<PERSON><PERSON>, 1955)", "ref_id": "BIBREF46"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Proof. Recall Eq. ( 5):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L = 2 -2α • tr W ⊤ p E z 2 z ⊤ 1 -2(1 -α) • tr W ⊤ p E z sup 2 z ⊤ 1 + λ 1 tr E z 2 z ⊤ 2 -1 + λ 2 tr E z sup 2 z sup⊤ 2 -1 + λ 3 tr W ⊤ p W p E z 1 z ⊤ 1 -1 .", "eq_num": "(5)"}], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "To derive the optimal W * p , we take the partial derivative ∂L ∂Wp and replace the expression of covariance matrices with Eq. ( 6):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "∂L ∂W p = -2α • E z 2 z ⊤ 1 -2(1 -α) • E z sup 2 z ⊤ 1 + 2λ 3 W p E z 1 z ⊤ 1 = -2α • W W ⊤ -2(1 -α) • W S B W ⊤ + 2λ 3 1 + σ 2 e • W p W W ⊤ . (A.3)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "By setting ∂L ∂Wp = 0, we obtain the optimal predictor W * p :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "λ 3 1 + σ 2 e • W * p W W ⊤ = W (αI + (1 -α)S B ) W ⊤ = W (S B + αS W ) W ⊤ . ∴ W * p = 1 λ 3 (1 + σ 2 e I) W (S B + αS W ) W + . (A.4)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Finally, by substituting the covariance matrices with the eigendecomposition as in Eq. ( 6), we obtain the following expression:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W * p = 1 λ 3 W V (Λ B + αΛ W ) Λ B + Λ W + σ 2 e I -1 V ⊤ W + . (A.5)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "From this expression, the optimal predictor W * p can be interpreted through a sequence of hypothetical transformations: 1) mapping features to the data space, 2) eliminating the augmentation noise and reducing the intra-class variance by a factor of α, and 3) mapping back to the feature space.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "It is noteworthy that <PERSON><PERSON> et al. (2023) derived an optimal predictor similar to Theorem 4.6. However, their focus was on the elimination of augmentation noise in the feature space in the context of self-supervised learning.", "cite_spans": [{"start": 22, "end": 40, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF62"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "A.4. Proof of Theorem 4.7 Theorem 4.7. The optimal predictor W * p and the optimal model W * that minimizes the loss in Eq. (5) satisfy", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W * ⊤ p W * p ≈ W * W * ⊤ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Proof. Recall Eq. ( 5):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L = 2 -2α • tr W ⊤ p E z 2 z ⊤ 1 -2(1 -α) • tr W ⊤ p E z sup 2 z ⊤ 1 + λ 1 tr E z 2 z ⊤ 2 -1 + λ 2 tr E z sup 2 z sup⊤ 2 -1 + λ 3 tr W ⊤ p W p E z 1 z ⊤ 1 -1 ,", "eq_num": "(5)"}], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "where stop-gradient is applied to z 2 and z sup 2 . Recall the partial derivative ∂L ∂Wp is derived in Eq. (A.3):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "∂L ∂W p = -2α • E z 2 z ⊤ 1 -2(1 -α) • E z sup 2 z ⊤ 1 + 2λ 3 W p E z 1 z ⊤ 1 = -2α • W W ⊤ -2(1 -α) • W S B W ⊤ + 2λ 3 1 + σ 2 e • W p W W ⊤ . (A.3)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "To derive the partial derivative ∂L ∂W , we express Eq. ( 5) in terms of W 's and x's:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "L = 2 -2α • tr W ⊤ p W E x 2 x ⊤ 1 W ⊤ -2(1 -α) • tr W ⊤ p W E x sup 2 x ⊤ 1 W ⊤ + λ 1 tr W E x 2 x ⊤ 2 W ⊤ -1 + λ 2 tr W E x sup 2 x sup⊤ 2 W ⊤ -1 + λ 3 tr W ⊤ p W p W E x 1 x ⊤ 1 W ⊤ -1 , (A.6)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "where W 's with stop-gradient are emphasized by W = sg(W ), which are regarded as constants when taking the derivative. Then, the partial derivative ∂L ∂W is derived as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "∂L ∂W = -2α • W ⊤ p W E x 2 x ⊤ 1 -2(1 -α) • W ⊤ p W E x sup 2 x ⊤ 1 + 2λ 3 • W ⊤ p W p W E x 1 x ⊤ 1 = -2α • W ⊤ p W -2(1 -α) • W ⊤ p W S B + 2λ 3 1 + σ 2 e • W ⊤ p W p W. (A.7)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Left-multiplying Eq. (A.3) by W ⊤ p and right-multiplying Eq. (A.7) by W ⊤ establishes the equality of them:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W ⊤ p ∂L ∂W p = -2α • W ⊤ p W W ⊤ -2(1 -α) • W ⊤ p W S B W ⊤ + 2λ 3 1 + σ 2 e • W ⊤ p W p W W ⊤ = ∂L ∂W W ⊤ . (A.8)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Now, we consider the update rule with the current iteration number t, the learning rate β, and the weight decay η:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "dW p dt = -β ∂L ∂W p -ηW p , dW dt = -β ∂L ∂W -ηW. (A.9)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Substituting this expression into Eq. (A.8) results in the following equality:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W ⊤ p dW p dt + ηW ⊤ p W p = -βW ⊤ p ∂L ∂W p = -β ∂L ∂W W ⊤ = dW dt W ⊤ + ηW W ⊤ . (A.10)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "Note that this is a differential equation, where it can be solved by multiplying both side by e 2ηt :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "d dt e 2ηt W ⊤ p W p = d dt e 2ηt W W ⊤ , (A.11)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "then, integrating with respect to t and multiplying by e -2ηt yields the solution: We conduct a study on M , which represents the number of positive samples from the target pool. As shown in Table F .2, the model demonstrates robustness to the number of positives. Even when sampling only one positive from the target pool, the performance is similar to sampling many positives. We pretrain SUPSIAM using an increased batch size 256. Additionally, we pretrain SUPCON with a batch size of 256, as the performance in Table 5 pre-trained with a batch size of 128 might be suboptimal. Moreover, to enhance the diversity of positive and negative samples, we also pretrain SUPCON with an additional memory bank (target pool) of size 8192, as described in <PERSON><PERSON><PERSON> et al. (2020) . The learning rate scaled linearly (<PERSON><PERSON> et al., 2017) with the batch size, i.e., for a batch size of 256, the learning rates are set to 0.3 for SUPCON and 0.4 for SUPSI<PERSON>, respectively. As shown in Table F .3, supervised ANCL shows a slight improvement in performance when the batch size is increased to 256, though the gain is overall marginal, and it is not heavily influenced by batch size, similar to its self-supervised counterpart (<PERSON> & He, 2021) . In the case of SUPCON, performance improves as the batch size increases, and memory bank provides performance gain, although this gain seems to be slightly reduced as the batch size increases. However, it still shows lower performance compared to supervised ANCL, which performs well even with a smaller batch size.", "cite_spans": [{"start": 749, "end": 769, "text": "<PERSON><PERSON><PERSON> et al. (2020)", "ref_id": "BIBREF26"}, {"start": 806, "end": 826, "text": "(<PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF14"}, {"start": 1210, "end": 1227, "text": "(<PERSON> & He, 2021)", "ref_id": null}], "ref_spans": [{"start": 197, "end": 198, "text": "F", "ref_id": "TABREF9"}, {"start": 521, "end": 522, "text": "5", "ref_id": "TABREF4"}, {"start": 977, "end": 978, "text": "F", "ref_id": "TABREF9"}], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "W ⊤ p W p = W W ⊤ + e -2ηt", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Proofs for Section 4", "sec_num": null}, {"text": "To verify the independence of our proposed method from the encoder backbone, we conduct experiments using the ViT backbone (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) . In contrastive learning, MOCO-V3 (<PERSON> et al., 2021) utilizes ViT as its backbone, and we benchmark this for implementing models such as SUPMOCO, BYOL, and SUPBYOL. In MOCO-V3, unlike the previous MOCO-V2 (<PERSON> et al., 2020b) , the queue is removed and a predictor is added, resembling ANCL (<PERSON><PERSON> et al., 2020; Chen & He, 2021) . For SUPMOCO with the ViT backbone, we also incorporate the predictor but retain a queue to ensure the existence of features sharing the same label with a size of 8192. Similarly, SUPBYOL employs a target pool with a size of 8192.", "cite_spans": [{"start": 123, "end": 149, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF11"}, {"start": 185, "end": 204, "text": "(<PERSON> et al., 2021)", "ref_id": null}, {"start": 357, "end": 377, "text": "(<PERSON> et al., 2020b)", "ref_id": null}, {"start": 443, "end": 463, "text": "(<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF16"}, {"start": 464, "end": 480, "text": "<PERSON> & He, 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "We pretrain ViT-Small on ImageNet-100 (<PERSON><PERSON> et al., 2009; <PERSON><PERSON> et al., 2020) for 200 epochs with a batch size of 256.", "cite_spans": [{"start": 38, "end": 57, "text": "(<PERSON> et al., 2009;", "ref_id": "BIBREF10"}, {"start": 58, "end": 76, "text": "<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF52"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "Common parameter settings include using the AdamW optimizer (<PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2019) with a linear learning rate warm-up for the first 40 epochs, a momentum of 0.9, and a weight decay of 0.1. A cosine learning rate schedule (<PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2017 ) is applied to the encoder and projector. We maintain a constant learning rate without decay for BYOL and SUPBYOL following the prior work (Chen & He, 2021) , while we apply a cosine learning rate schedule to the predictor of MOCO-V3 and SUPMOCO.", "cite_spans": [{"start": 60, "end": 87, "text": "(<PERSON><PERSON><PERSON><PERSON> & Hutter, 2019)", "ref_id": "BIBREF37"}, {"start": 227, "end": 253, "text": "(Losh<PERSON>lov & Hutter, 2017", "ref_id": "BIBREF36"}, {"start": 394, "end": 411, "text": "(<PERSON> & He, 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "• MOCO-V3 (<PERSON> et al., 2021) . We follow the original parameter settings, where the learning rate is set to 1.5e-4 and the temperature parameter for contrastive loss is 0.2. The exponential moving average (EMA) parameter starts from 0.99 and is increased to one during training. The projector consists of 3 MLP layers with an output dimension of 256 and a hidden dimension of 4096. The predictor has 2 MLP layers with a hidden dimension of 4096.", "cite_spans": [{"start": 10, "end": 29, "text": "(<PERSON> et al., 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "• SUPMOCO (<PERSON><PERSON><PERSON> et al., 2021) . The learning rate is set to 1.5e-3 and the temperature parameter for contrastive loss is 0.2. The EMA parameter starts from 0.99 and is increased to one during training. The projector consists of 3 MLP layers with an output dimension of 256 and a hidden dimension of 4096. The predictor has 2 MLP layers with a hidden dimension of 4096.", "cite_spans": [{"start": 10, "end": 33, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "• BYOL (<PERSON><PERSON> et al., 2020) . The learning rate is set to 1.5e-3 and the EMA parameter starts from 0.996 and is increased to one during training. The projector consists of 2 MLP layers with an output dimension of 256 and a hidden dimension of 4096. The predictor has 2 MLP layers with a hidden dimension of 4096.", "cite_spans": [{"start": 7, "end": 27, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "• SUPBYOL. The learning rate is set to 1.5e-3 and the EMA parameter starts from 0.996 and is increased to one during training. The loss parameter α is set to 0.5 and all supervised target is obtained by sampling and averaging all positives in the target pool. The projector consists of 2 MLP layers with an output dimension of 256 and a hidden dimension of 4096. The predictor has 2 MLP layers with a hidden dimension of 4096.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "Table G .1. Transfer learning via linear evaluation results on various downstream datasets, where the models trained with the ViT-Small backbone on ImageNet-100. CL, Sup, EMA stand for the cases when negative samples are considered, labels are used for pretraining, and the momentum network is adopted, respectively. Avg.Rank represents the average performance ranking across all datasets. For each dataset, the best results are in bold and the second-best results are underlined. Our proposed methods are marked with †. We observe a slightly lower performance in Table G .1 compared to Table 5 , where results are presented using ResNet-50 (<PERSON> et al., 2016) as the backbone. This discrepancy is likely due to pretraining with ImageNet-100. ViT typically requires more data for effective learning compared to ResNet, and the number of data samples in ImageNet-100 may be slightly insufficient. Nevertheless, supervision in the ANCL framework with the ViT backbone proves effective in enhancing performance. Notably, when compared to supervised contrastive learning, proposed method exhibits slightly better performance across all datasets except one. This underscores the effectiveness of the supervised ANCL approach, which is applicable to the ViT backbone and remains independent of the underlying architecture.", "cite_spans": [{"start": 641, "end": 658, "text": "(<PERSON> et al., 2016)", "ref_id": "BIBREF19"}], "ref_spans": [{"start": 6, "end": 7, "text": "G", "ref_id": null}, {"start": 570, "end": 571, "text": "G", "ref_id": null}, {"start": 593, "end": 594, "text": "5", "ref_id": "TABREF4"}], "eq_spans": [], "section": "<PERSON><PERSON> <PERSON>", "sec_num": null}, {"text": "We conduct additional experiments on the CIFAR (<PERSON><PERSON>, 2009) dataset, where the image size was reduced to 32×32. The encoder employes a CIFAR variant of ResNet-18 (<PERSON> et al., 2016) and is trained for a total of 1000 epochs with a batch size of 256. For the ANCL approach, specifically SIMSIAM and SUPSIAM, we utilize a 2-layer MLP projector, and Gaussian blurring is excluded from the augmentation. For contrastive learning, we select SIMCLR (<PERSON> et al., 2020a) and its supervised version SUPCON (<PERSON><PERSON><PERSON> et al., 2020) . For ANCL, SIMSIAM (Chen & He, 2021) and BYOL (<PERSON><PERSON> et al., 2020) and their supervised counterparts SUPSIAM and SUPBYOL are chosen as models. Learning rates are tuned individually for each model: SIMCLR (0.7), SUPCON (0.6), BYOL (0.6), SUPBYOL (0.5), SIMSIAM (0.7), and SUPSIAM (0.7). For supervised ANCL, the target pool size is reduced to 4096, and the loss parameter α is set to 0.5 for SUPSIAM and 0.8 for SUPBYOL. The results in Table H .1 indicate that when the pretext and downstream datasets are the same, the introduction of supervision leads to an increase in linear accuracy. Conversely, in cases where they differ, contrastive learning shows a decline or slight increase in performance. Asymmetric non-contrastive learning, on the other hand, benefits from labels, resulting in increased accuracy and showcasing the best performance. Thus, our proposed supervised ANCL proves to be an effective method for obtaining high-quality representations across various datasets.", "cite_spans": [{"start": 47, "end": 74, "text": "(<PERSON><PERSON><PERSON><PERSON> & Hinton, 2009)", "ref_id": "BIBREF28"}, {"start": 177, "end": 194, "text": "(<PERSON> et al., 2016)", "ref_id": "BIBREF19"}, {"start": 456, "end": 476, "text": "(<PERSON> et al., 2020a)", "ref_id": null}, {"start": 511, "end": 532, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF26"}, {"start": 553, "end": 570, "text": "(<PERSON> & He, 2021)", "ref_id": null}, {"start": 580, "end": 600, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF16"}], "ref_spans": [{"start": 975, "end": 976, "text": "H", "ref_id": null}], "eq_spans": [], "section": "<PERSON>. Pretraining on CIFAR", "sec_num": null}, {"text": "Our proposed methods are robust to the number of positives from the target pool, as shown in Table F.2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by the National Research Foundation of Korea (NRF) grant funded by the Korea government (MSIT) (2022R1A4A1033384) and the Yonsei University Research Fund (2024-22-0148). We thank <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> for helpful discussions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "where c is a constant with respect to t. Finally, the constant becomes negligible as t → ∞, i.e., at the optimal state, such that we obtain the following expression:The equality implies that they share the eigenspace.Note that Theorem 4.7 holds in self-supervised ANCL as shown in <PERSON><PERSON> et al. (2021) ; <PERSON> et al. (2022) , and it remains valid when supervision is incorporated.", "cite_spans": [{"start": 281, "end": 299, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF53"}, {"start": 302, "end": 319, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "We provide a detailed description of toy experiments in Section 4.3. We generate a synthetic toy dataset to verify that balancing the contributions of supervision and self-supervision is crucial for the generalization of learned representations. The dataset consists of three classes, each following a Gaussian distribution with orthogonal mean vectors and a shared isotropic covariance matrix with a scale of 0.35. The mean vectors are obtained by taking the left singular vectors of a random matrix sampled from a standard Gaussian distribution. The synthetic data has 2048 dimensions, and data augmentation is performed by replacing 60% of the dimensions with the corresponding dimensions of the overall data mean vector. The training dataset consists of 3000 samples, with 1000 samples per class, and similarly, the test dataset consists of 1500 samples, with 500 samples per class.For the supervised ANCL approach, SUPSIAM is utilized with varying α, where the encoder, projector, and predictor each consist of a linear layer without batch normalization (<PERSON><PERSON><PERSON> & <PERSON>ze<PERSON>y, 2015) . The output dimension of the projector/predictor is set to 128. The model is trained for 200 epochs using the SGD optimizer, with a batch size of 256, learning rate of 0.05, momentum of 0.9, and weight decay of 5e-4. A cosine learning rate schedule (<PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2017) is applied except for the predictor, following the prior work (<PERSON>, 2021) .", "cite_spans": [{"start": 1059, "end": 1082, "text": "(Ioffe & Szegedy, 2015)", "ref_id": "BIBREF23"}, {"start": 1333, "end": 1360, "text": "(<PERSON>h<PERSON><PERSON> & Hutter, 2017)", "ref_id": "BIBREF36"}, {"start": 1423, "end": 1440, "text": "(<PERSON> & He, 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "B. Toy Experiment Setup", "sec_num": null}, {"text": "We provide a detailed description of the pretraining setup. Each model consists of a ResNet-50 encoder (<PERSON> et al., 2016) followed by a 2-layer MLP projector and predictor, except for SIMSIAM and SUPSIAM, which utilize a 3-layer MLP projector following the original configuration by <PERSON> & <PERSON> (2021) . We pretrain models on ImageNet-100 (<PERSON><PERSON> et al., 2009; <PERSON><PERSON> et al., 2020) for 200 epochs with a batch size of 128. We utilize the SGD optimizer with a momentum of 0.9, and a weight decay of 1e-4. A cosine learning rate schedule (<PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2017) is applied to the encoder and projector.We maintain a constant learning rate without decay for the predictor, following the prior work (<PERSON> & <PERSON>, 2021) . Other method-specific details are provided below:• SIMCLR (<PERSON> et al., 2020a) . The learning rate is set to 0.1 and the temperature parameter for contrastive loss is 0.1. The projector consists of 2 MLP layers with an output dimension of 128.where z 2 and z a are L2-normalized and Z n is the set of negative pairs of z 1 obtained from the same batch.• SUPCON (<PERSON><PERSON><PERSON> et al., 2020) . The learning rate is set to 0.15 and the temperature parameter for contrastive loss is 0.1. The projector consists of 2 MLP layers with an output dimension of 128.where z 2 and z a are L2-normalized, B ′ is the set of positive pairs of z 1 obtained from the same batch, with a cardinality of M + 1 and Z n is the set of negative pairs of z 1 obatined from the same batch.• MOCO-V2 (Chen et al., 2020b) . The learning rate is set to 0.03 and the temperature parameter for contrastive loss is 0.2. The size of memory bank (target pool) |Q| is 8192, and the exponential moving average (EMA) parameter is 0.999. The projector consists of 2 MLP layers with an output dimension of 128.where z 2 and z a are L2-normalized and Z n is the set of negative pairs of z 1 obtained from the queue.• SUPMOCO (Majumder et al., 2021) . The learning rate is set to 0.1 and temperature parameter is 0.07. The size of memory bank (target pool) |Q| is 8192 and the EMA parameter is 0.999. The projector consists of 2 MLP layers with an output dimension of 128.where z 2 , z a and z j are L2-normalized, Q ′ is the set of positive pairs of z 1 obtained from the same batch and the queue, with a cardinality of M + 1 and Z n is the set of negative pairs of z 1 obtained from the same batch and the queue.• BYOL (Grill et al., 2020) . The learning rate is set to 0.2. The EMA parameter starts from 0.996 and is increased to one during training. The projector consists of 2 MLP layers with an output dimension of 256. The predictor has 2 MLP layers with a hidden dimension of 4096.where p 1 and z 2 are L2-normalized and sg denotes the stop-gradient.• SUPBYOL. The learning rate is set to 0.2. The size of target pool |Q| is 8192 and the supervised target z sup 2 is obtained by sampling and averaging all positives in the target pool. The EMA parameter starts from 0.996 and is increased to one during training. The projector consists of 2 MLP layers with an output dimension of 256, and the predictor has 2 MLP layers with a hidden dimension of 4096.where p 1 , z 2 and z ′ 2 are L2-normalized, sg denotes the stop-gradient, and Q y ⊆ Q is the set of targets of p 1 sampled from the target pool sharing the sample label with p 1 , with a cardinality of M .• SIMSIAM (Chen et al., 2020a) . The learning rate is set to 0.2 with a linear learning rate warm-up for the first 40 epochs.The projector consists of 3 MLP layers with an output dimension of 2048. The predictor has 2 MLP layers with a hidden dimension of 512.where p 1 and z 2 are L2-normalized and sg denotes the stop-gradient.• SUPSIAM. The learning rate is set to 0.2 with a linear learning rate warm-up for the first 40 epochs. The size of target pool |Q| is 8192 and the supervised target z sup 2 is obtained by sampling and averaging all positives in the target pool. The projector consists of 3 MLP layers with an output dimension of 2048, and the predictor has 2 MLP layers with a hidden dimension of 512.where p 1 , z 2 and z ′ 2 are L2-normalized, sg denotes the stop-gradient, and Q y ⊆ Q is the set of targets of p 1 sampled from the target pool sharing the sample label with p 1 , with a cardinality of M .", "cite_spans": [{"start": 103, "end": 120, "text": "(<PERSON> et al., 2016)", "ref_id": "BIBREF19"}, {"start": 282, "end": 298, "text": "<PERSON> & He (2021)", "ref_id": null}, {"start": 336, "end": 355, "text": "(<PERSON> et al., 2009;", "ref_id": "BIBREF10"}, {"start": 356, "end": 374, "text": "<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF52"}, {"start": 529, "end": 556, "text": "(<PERSON>h<PERSON><PERSON> & Hutter, 2017)", "ref_id": "BIBREF36"}, {"start": 692, "end": 709, "text": "(<PERSON> & He, 2021)", "ref_id": null}, {"start": 770, "end": 790, "text": "(<PERSON> et al., 2020a)", "ref_id": null}, {"start": 1073, "end": 1094, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF26"}, {"start": 1478, "end": 1498, "text": "(<PERSON> et al., 2020b)", "ref_id": null}, {"start": 1890, "end": 1913, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF40"}, {"start": 2385, "end": 2405, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF16"}, {"start": 3340, "end": 3360, "text": "(<PERSON> et al., 2020a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "C. Pretraining Setup", "sec_num": null}, {"text": "Table D .1 provides a comprehensive overview of datasets, including evaluation metrics for both (a) transfer learning via linear evaluation and (b) few-shot classification. For datasets without an official validation set, a random split is performed using the entire training set. For the few-shot task, the complete dataset is utilized for all datasets except FC100 (<PERSON><PERSON><PERSON> et al., 2018) . In the case of FC100 (<PERSON><PERSON><PERSON> et al., 2018) , a meta-test split is used. Detailed evaluation protocols are outlined in Appendix E.On the Effectiveness of Supervision in Asymmetric Non-Contrastive Learning (Krizhevsky & Hinton, 2009) 100 45000 5000 10000 Top-1 accuracy DTD (split 1) (<PERSON><PERSON><PERSON><PERSON> et al., 2014) 47 1880 1880 1880 Top-1 accuracy Food (<PERSON><PERSON> et al., 2014) 101 68175 7575 25250 Top-1 accuracy MIT67 (<PERSON>ua<PERSON>i & <PERSON>ba, 2009) 67 4690 670 1340 Top-1 accuracy SUN397 (split 1) (<PERSON> et al., 2010) 397 15880 3970 19850 Top-1 accuracy Caltech101 (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2004) 101 2525 505 5647 Mean per-class accuracy CUB200 (<PERSON><PERSON><PERSON> et al., 2010) 200 4990 1000 5794 Mean per-class accuracy Dogs (<PERSON><PERSON><PERSON> et al., 2011; <PERSON><PERSON> et al., 2009) 120 10800 1200 8580 Mean per-class accuracy Flowers (<PERSON><PERSON><PERSON> & <PERSON>, 2008) 102 1020 1020 6149 Mean per-class accuracy (b) Few-shot classification Aircraft (Maji et al., 2013 ) 100 10000 Average accuracy CUB200 (Welinder et al., 2010) 200 11745 Average accuracy FC100 (Oreshkin et al., 2018) 20 12000 Average accuracy Flowers (Nilsback & Zisserman, 2008) 102 8189 Average accuracy Fungi (Schroeder & Cui, 2018) 1394 89760 Average accuracy Omniglot (Lake et al., 2015) 1623 32460 Average accuracy DTD (Cimpoi et al., 2014) 47 5640 Average accuracy Traffic Signs (Houben et al., 2013) 43 12630 Average accuracy", "cite_spans": [{"start": 367, "end": 390, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF43"}, {"start": 414, "end": 437, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF43"}, {"start": 599, "end": 626, "text": "(<PERSON><PERSON><PERSON><PERSON> & Hinton, 2009)", "ref_id": "BIBREF28"}, {"start": 677, "end": 698, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF9"}, {"start": 737, "end": 759, "text": "(<PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF2"}, {"start": 802, "end": 829, "text": "(Quattoni & Torralba, 2009)", "ref_id": "BIBREF47"}, {"start": 879, "end": 898, "text": "(<PERSON> et al., 2010)", "ref_id": "BIBREF59"}, {"start": 946, "end": 968, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2004)", "ref_id": "BIBREF13"}, {"start": 1018, "end": 1041, "text": "(<PERSON><PERSON><PERSON> et al., 2010)", "ref_id": "BIBREF57"}, {"start": 1090, "end": 1111, "text": "(<PERSON><PERSON><PERSON> et al., 2011;", "ref_id": "BIBREF25"}, {"start": 1112, "end": 1130, "text": "<PERSON> et al., 2009)", "ref_id": "BIBREF10"}, {"start": 1183, "end": 1211, "text": "(<PERSON><PERSON><PERSON> & Zisserman, 2008)", "ref_id": "BIBREF42"}, {"start": 1292, "end": 1310, "text": "(<PERSON><PERSON> et al., 2013", "ref_id": "BIBREF39"}, {"start": 1347, "end": 1370, "text": "(<PERSON><PERSON><PERSON> et al., 2010)", "ref_id": "BIBREF57"}, {"start": 1404, "end": 1427, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF43"}, {"start": 1462, "end": 1490, "text": "(<PERSON><PERSON><PERSON> & Zisserman, 2008)", "ref_id": "BIBREF42"}, {"start": 1523, "end": 1546, "text": "(Schroeder & Cui, 2018)", "ref_id": "BIBREF51"}, {"start": 1584, "end": 1603, "text": "(<PERSON> et al., 2015)", "ref_id": "BIBREF29"}, {"start": 1636, "end": 1657, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF9"}, {"start": 1697, "end": 1718, "text": "(<PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF22"}], "ref_spans": [{"start": 6, "end": 7, "text": "D", "ref_id": null}], "eq_spans": [], "section": "D. Datasets", "sec_num": null}, {"text": "The linear evaluation protocol for transfer learning follows from <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2019) and <PERSON> et al. (2021a) . Specifically, we divide the entire training dataset into a train set and a validation set to tune the regularization parameter by minimizing the L2-regularized cross-entropy loss using L-BFGS (Liu & Nocedal, 1989) . Train and validation set splits are shown in Table D .1. With the best parameter, we extract the frozen representations of 224 × 224 center-cropped images without data augmentation and train the linear classifier with the entire training dataset, including the validation set.", "cite_spans": [{"start": 66, "end": 89, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2019)", "ref_id": "BIBREF27"}, {"start": 94, "end": 112, "text": "<PERSON> et al. (2021a)", "ref_id": null}, {"start": 307, "end": 328, "text": "(Liu & Nocedal, 1989)", "ref_id": "BIBREF34"}], "ref_spans": [{"start": 382, "end": 383, "text": "D", "ref_id": null}], "eq_spans": [], "section": "E.1. Transfer Learning via Linear Evaluation", "sec_num": null}, {"text": "We adhere to the few-shot classification evaluation protocol outlined by <PERSON> et al. (2021a) . Specifically, we conduct logistic regression using the frozen representations extracted from 224 × 224 images without data augmentation in an N -way K-shot episode. It's important to note that as the encoder remains frozen, this protocol does not involve a fine-tuning approach.", "cite_spans": [{"start": 73, "end": 91, "text": "<PERSON> et al. (2021a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E.2. Few-Shot Classfication", "sec_num": null}, {"text": "We conduct additional experiments with SUPSIAM, varying the loss parameter α, the number of positives, denoted as M and the batch size. During the experiments on batch size, we also incorporate contrastive learning, specifically SUPCON. Given that the performance recorded in the Table 5 might be suboptimal due to pretraining with a batch size of 128, which could be too small, we re-pretrain SUPCON using an increased batch size. Unless specified otherwise, the remaining settings follow the setup outlined in Appendix C. We apply the evaluation methodology outlined in Appendix E to the dataset introduced in Appendix D.", "cite_spans": [], "ref_spans": [{"start": 286, "end": 287, "text": "5", "ref_id": null}], "eq_spans": [], "section": "<PERSON><PERSON> Additional Experiments", "sec_num": null}, {"text": "We conduct experiments with various α values to explore the relationship between intra-class variance reduction and representation quality. Table F .1 presents the linear evaluation performances for different α values. The model performs best in most cases when α is set to 0.5. Interestingly, the optimal α appears to vary depending on the downstream dataset. Nevertheless, it is crucial to note that α should always fall within the range (0, 1) to effectively capture within-class diversity, thereby proving beneficial for downstream tasks.", "cite_spans": [], "ref_spans": [{"start": 146, "end": 147, "text": "F", "ref_id": null}], "eq_spans": [], "section": "F.1. Transfer Learning with Different α", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Tackling online one-class incremental learning by removing negative contrasts", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2203.13307"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Tackling online one-class incremental learning by removing negative con- trasts. arXiv preprint arXiv:2203.13307, 2022.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Vicreg: Varianceinvariance-covariance regularization for self-supervised learning", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ponce", "suffix": ""}, {"first": "Y", "middle": [], "last": "Lecun", "suffix": ""}], "year": 2022, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Variance- invariance-covariance regularization for self-supervised learning. In ICLR, 2022.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Food-101 -mining discriminative components with random forests", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": ["V"], "last": "Gool", "suffix": ""}], "year": 2014, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, L. V. Food-101 - mining discriminative components with random forests. In ECCV, 2014.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Co2l: Contrastive continual learning", "authors": [{"first": "H", "middle": [], "last": "Cha", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Contrastive continual learning. In ICCV, 2021.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Perfectly balanced: Improving transfer and robustness of supervised contrastive learning", "authors": [{"first": "M", "middle": ["F"], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Song", "suffix": ""}, {"first": "K", "middle": [], "last": "Fat<PERSON>alian", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Perfectly balanced: Improving transfer and robustness of supervised contrastive learning. In ICML, 2022.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "A simple framework for contrastive learning of visual representations", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and Hi<PERSON>, G. A simple framework for contrastive learning of visual rep- resentations. In ICML, 2020a.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Exploring simple siamese representation learning", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "He", "suffix": ""}], "year": 2021, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> Exploring simple siamese representa- tion learning. In CVPR, 2021.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Improved baselines with momentum contrastive learning", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Fan", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "He", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2101.11058"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>mproved baselines with momentum contrastive learning. arXiv preprint arXiv:2101.11058, 2020b.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "An empirical study of training self-supervised vision transformers", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "He", "suffix": ""}], "year": 2021, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> empirical study of training self-supervised vision transformers. In ICCV, 2021.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Describing textures in the wild", "authors": [{"first": "M", "middle": [], "last": "Cimpoi", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Kokkinos", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Describing textures in the wild. In CVPR, 2014.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Imagenet: A large-scale hierarchical image database", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L.-J", "middle": [], "last": "Li", "suffix": ""}, {"first": "K", "middle": [], "last": "Li", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, L. Imagenet: A large-scale hierarchical image database. In CVPR, 2009.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "A", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "X", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, J<PERSON>, and <PERSON><PERSON><PERSON>, N. An image is worth 16x16 words: Transformers for image recognition at scale. In ICLR, 2021.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "The pascal visual object classes (voc) challenge", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": ["V"], "last": "Gool", "suffix": ""}, {"first": "C", "middle": ["K I"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Winn", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2010, "venue": "IJCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. The pascal visual object classes (voc) challenge. IJCV, 2010.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Learning generative visual models from few training examples: An incremental bayesian approach tested on 101 object categories", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Perona", "suffix": ""}], "year": 2004, "venue": "CVPR Workshop", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, P. Learning generative visual models from few training examples: An incremen- tal bayesian approach tested on 101 object categories. In CVPR Workshop, 2004.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "large minibatch SGD: training imagenet in 1 hour", "authors": [{"first": "P", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Kyrola", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "", "middle": [], "last": "Accurate", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1706.02677"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>, large minibatch SGD: training imagenet in 1 hour. arXiv preprint arXiv:1706.02677, 2017.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Dissecting supervised constrastive learning", "authors": [{"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>ct- ing supervised constrastive learning. In ICML, 2021.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Bootstrap your own latent: A new approach to self-supervised learning", "authors": [{"first": "J.-B", "middle": [], "last": "Grill", "suffix": ""}, {"first": "F", "middle": [], "last": "Strub", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Tallec", "suffix": ""}, {"first": "P", "middle": [], "last": "Richemond", "suffix": ""}, {"first": "E", "middle": [], "last": "Buchatskaya", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Avila Pires", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Tallec, C., Rich<PERSON>d, P., Buchatskaya, E., <PERSON>, C., <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, et al. Bootstrap your own latent: A new approach to self-supervised learning. In NeurIPS, 2020.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Supervised contrastive learning for pre-trained language model finetuning", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Supervised contrastive learning for pre-trained language model fine- tuning. In ICLR, 2021.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Implicit variance regularization in non-contrastive ssl", "authors": [{"first": "M", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Labor<PERSON>x", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Implicit variance regularization in non-contrastive ssl. In NeurIPS, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Deep residual learning for image recognition", "authors": [{"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ren", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}], "year": 2016, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. Deep residual learning for image recognition. In CVPR, 2016.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Momentum contrast for unsupervised visual representation learning", "authors": [{"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "H", "middle": [], "last": "Fan", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> contrast for unsupervised visual representation learning. In CVPR, 2020.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Masked autoencoders are scalable vision learners", "authors": [{"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Masked autoencoders are scalable vision learners. In CVPR, 2022.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Detection of traffic signs in real-world images: The german traffic sign detection benchmark", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Stallkamp", "suffix": ""}, {"first": "J", "middle": [], "last": "Sal<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Schl<PERSON>sing", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "International Joint Conference on Neural Networks", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, M<PERSON>, and <PERSON><PERSON>, C. Detection of traffic signs in real-world images: The german traffic sign detection benchmark. In Interna- tional Joint Conference on Neural Networks, 2013.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Batch normalization: Accelerating deep network training by reducing internal covariate shift", "authors": [{"first": "S", "middle": [], "last": "Ioffe", "suffix": ""}, {"first": "C", "middle": [], "last": "Szegedy", "suffix": ""}], "year": 2015, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, S<PERSON> and <PERSON>, <PERSON><PERSON> normalization: Accelerating deep network training by reducing internal covariate shift. In ICML, 2015.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Exploring balanced feature spaces for representation learning", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Exploring balanced feature spaces for representation learning. In ICLR, 2021.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Novel dataset for fine-grained image categorization", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2011, "venue": "CVPR Workshop", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, L. Novel dataset for fine-grained image categorization. In CVPR Workshop, 2011.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Supervised contrastive learning", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Teterwak", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Sarna", "suffix": ""}, {"first": "Y", "middle": [], "last": "Tian", "suffix": ""}, {"first": "P", "middle": [], "last": "Isola", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Supervised contrastive learning. In NeurIPS, 2020.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Do better imagenet models transfer better", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": ["V"], "last": "Le", "suffix": ""}], "year": 2019, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, Q. V. Do better imagenet models transfer better? In CVPR, 2019.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Learning multiple layers of features from tiny images", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, G. Learning multiple layers of features from tiny images. Technical report, University of Toronto, 2009.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Human-level concept learning through probabilistic program induction", "authors": [{"first": "B", "middle": ["M"], "last": "Lake", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["B"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Science", "volume": "350", "issue": "6266", "pages": "1332--1338", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, J. B. Human-level concept learning through probabilistic pro- gram induction. Science, 350(6266):1332-1338, 2015.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Improving transferability of representations via augmentation-aware self-supervision", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Improving transferability of representations via augmentation-aware self-supervision. In NeurIPS, 2021a.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "On the equivalence of linear discriminant analysis and least squares", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "AAAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON> and <PERSON>, <PERSON>. On the equivalence of linear discrimi- nant analysis and least squares. In AAAI, 2015.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "i-mix: A domain-agnostic strategy for contrastive representation learning", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C.-L", "middle": [], "last": "Li", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, H. i-mix: A domain-agnostic strategy for contrastive representation learning. In ICLR, 2021b.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Microsoft coco: Common objects in context", "authors": [{"first": "T.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Belongie", "suffix": ""}, {"first": "L", "middle": [], "last": "Bour<PERSON>v", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Perona", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, P. Microsoft coco: Common objects in context. In ECCV, 2014.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "On the limited memory bfgs method for large scale optimization", "authors": [{"first": "D", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Nocedal", "suffix": ""}], "year": 1989, "venue": "Mathematical programming", "volume": "45", "issue": "1-3", "pages": "503--528", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> On the limited memory bfgs method for large scale optimization. Mathematical pro- gramming, 45(1-3):503-528, 1989.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Bridging the gap from asymmetry tricks to decorrelation principles in non-contrastive self-supervised learning", "authors": [{"first": "K.<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Okata<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Bridging the gap from asymmetry tricks to decorrelation principles in non-contrastive self-supervised learning. In NeurIPS, 2022.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Sgdr: Stochastic gradient descent with warm restarts", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, F. Sgdr: Stochastic gradient descent with warm restarts. In ICLR, 2017.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Decoupled weight decay regularization", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>pled weight decay regu- larization. In ICLR, 2019.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Visualizing data using t-sne", "authors": [{"first": "L", "middle": ["V D"], "last": "Maaten", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2008, "venue": "JMLR", "volume": "9", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, L. v. d. and Hinton, G. Visualizing data using t-sne. JMLR, 9(Nov), 2008.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Fine-grained visual classification of aircraft", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1306.5151"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, A. Fine-grained visual classification of aircraft. arXiv preprint arXiv:1306.5151, 2013.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Supervised momentum contrastive learning for few-shot classification", "authors": [{"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Soatto", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2101.11058"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, S. Supervised momentum contrastive learning for few-shot classification. arXiv preprint arXiv:2101.11058, 2021.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Non-contrastive auxiliary loss for learning from molecular conformers", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["W"], "last": "Park", "suffix": ""}, {"first": "J", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.07754"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Non-contrastive auxiliary loss for learning from molecular conformers. arXiv preprint arXiv:2302.07754, 2023.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Automated flower classification over a large number of classes", "authors": [{"first": "M.-<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2008, "venue": "Proceedings of the Indian Conference of Computer Visions, Graphics and Image Processing", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Automated flower clas- sification over a large number of classes. In Proceedings of the Indian Conference of Computer Visions, Graphics and Image Processing, 2008.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Tadam: Task dependent adaptive metric for improved few-shot learning", "authors": [{"first": "B", "middle": ["N"], "last": "Oreshkin", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Lacoste", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Task dependent adaptive metric for improved few-shot learning. In NeurIPS, 2018.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Prevalence of neural collapse during the terminal phase of deep learning training", "authors": [{"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Han", "suffix": ""}, {"first": "D", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the National Academy of Sciences", "volume": "117", "issue": "40", "pages": "24652--24663", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, D. L. Prevalence of neural collapse during the terminal phase of deep learn- ing training. Proceedings of the National Academy of Sciences, 117(40):24652-24663, 2020.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Cats and dogs", "authors": [{"first": "O", "middle": ["M"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Jawahar", "suffix": ""}], "year": 2012, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, C. Cats and dogs. In CVPR, 2012.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "A generalized inverse for matrices", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1955, "venue": "Mathematical proceedings of the Cambridge philosophical society", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> A generalized inverse for matrices. In Math- ematical proceedings of the Cambridge philosophical society, 1955.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Recognizing indoor scenes", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Torralba", "suffix": ""}], "year": 2009, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Recognizing indoor scenes. In CVPR, 2009.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Cnn features off-the-shelf: an astounding baseline for recognition", "authors": [{"first": "A", "middle": ["S"], "last": "Razavian", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "CVPR DeepVision Workshop", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. Cnn features off-the-shelf: an astounding baseline for recognition. In CVPR DeepVision Workshop, 2014.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Faster r-cnn: Towards real-time object detection with region proposal networks", "authors": [{"first": "S", "middle": [], "last": "Ren", "suffix": ""}, {"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> r-cnn: Towards real-time object detection with region proposal networks. In NeurIPS, 2015.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "The edge of orthogonality: a simple view of what makes byol tick", "authors": [{"first": "P", "middle": ["H"], "last": "Richemond", "suffix": ""}, {"first": "A", "middle": [], "last": "Tam", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Strub", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Hill", "suffix": ""}], "year": 2023, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, P. H., Tam, A., Tang, Y., St<PERSON>b, F., <PERSON>, B., and Hill, F. The edge of orthogonality: a simple view of what makes byol tick. In ICML, 2023.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Fgvcx fungi classification challenge 2018", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, Y. Fgvcx fungi classification chal- lenge 2018. github.com/visipedia/fgvcx_ fungi_comp, 2018.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Contrastive multiview coding", "authors": [{"first": "Y", "middle": [], "last": "Tian", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Isola", "suffix": ""}], "year": 2020, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, P. Contrastive multiview coding. In ECCV, 2020.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Understanding selfsupervised learning dynamics without contrastive pairs", "authors": [{"first": "Y", "middle": [], "last": "Tian", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> Understanding self- supervised learning dynamics without contrastive pairs. In ICML, 2021.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Representation learning with contrastive predictive coding", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>yal<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1807.03748"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> learning with contrastive predictive coding. arXiv preprint arXiv:1807.03748, 2018.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Opera: Omni-supervised representation learning with hierarchical supervisions", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. <PERSON>: Omni-supervised representation learning with hierarchi- cal supervisions. In ICCV, 2023.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Can semantic labels assist selfsupervised visual representation learning", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "He", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Li", "suffix": ""}, {"first": "Q", "middle": [], "last": "Tian", "suffix": ""}], "year": 2021, "venue": "AAAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Can semantic labels assist self- supervised visual representation learning? In AAAI, 2021.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Caltech-UCSD Birds 200", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Wah", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Belongie", "suffix": ""}, {"first": "P", "middle": [], "last": "Perona", "suffix": ""}], "year": 2010, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, S., and <PERSON><PERSON>, P. Caltech-UCSD Birds 200. Tech- nical report, California Institute of Technology, 2010.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Unsupervised feature learning via non-parametric instance discrimination", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["X"], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> Unsupervised fea- ture learning via non-parametric instance discrimination. In CVPR, 2018.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Sun database: Large-scale scene recognition from abbey to zoo", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["H"], "last": "Oliva", "suffix": ""}, {"first": "A", "middle": [], "last": "Torralba", "suffix": ""}], "year": 2010, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, abd <PERSON><PERSON><PERSON> abd <PERSON><PERSON>, J<PERSON> H., and Tor<PERSON>ba, A. Sun database: Large-scale scene recognition from abbey to zoo. In CVPR, 2010.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Which features are learnt by contrastive learning? on the role of simplicity bias in class collapse and feature suppression", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Gan", "suffix": ""}, {"first": "P.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Mirzasoleiman", "suffix": ""}], "year": 2023, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Which features are learnt by contrastive learning? on the role of simplicity bias in class collapse and feature suppression. In ICML, 2023.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Barlow twins: Self-supervised learning via redundancy reduction", "authors": [{"first": "J", "middle": [], "last": "Zbontar", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Lecun", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> twins: Self-supervised learning via redundancy reduction. In ICML, 2021.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Towards a unified theoretical understanding of non-contrastive learning via rank differential mechanism", "authors": [{"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Towards a unified theoretical understanding of non-contrastive learning via rank differential mechanism. In ICLR, 2023.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "fig_num": "1", "text": "Figure 1. Our proposed supervised ANCL framework. The components we added to the standard ANCL are highlighted with a red box.We manage a target pool to ensure the existence of positive samples sharing the same class label in the form of z sup 2 . Stop-gradient (sg) applied to z2 and z sup 2 ensures that the gradients propagate through the online branch with the predictor only. The target branch without the predictor either shares parameters with the online branch (SUPSIAM), or exhibits a momentum network (SUPBYOL).", "num": null, "uris": null}, "FIGREF1": {"type_str": "figure", "fig_num": null, "text": "Figure 2. t-SNE visualization of SUPSIAM features with different α on the toy dataset.", "num": null, "uris": null}, "FIGREF2": {"type_str": "figure", "fig_num": "3", "text": "Figure 3. t-SNE visualization of SUPSIAM features with different α on 15 dog and 5 bird classes from ImageNet-100.", "num": null, "uris": null}, "TABREF1": {"type_str": "table", "content": "<table><tr><td>α</td><td/><td>Interpolation</td><td/><td/><td>Extrapolation</td><td/></tr><tr><td/><td colspan=\"6\">σ = 0.2 σ = 0.5 σ = 0.8 σ = 0.2 σ = 0.5 σ = 0.8</td></tr><tr><td>0.0</td><td>43.60</td><td>37.44</td><td>35.40</td><td>96.67</td><td>83.76</td><td>74.42</td></tr><tr><td>0.2</td><td>44.02</td><td>37.24</td><td>35.31</td><td>97.13</td><td>84.71</td><td>75.09</td></tr><tr><td>0.5</td><td>44.25</td><td>37.96</td><td>35.69</td><td>97.60</td><td>85.87</td><td>76.00</td></tr><tr><td>0.8</td><td>44.24</td><td>37.65</td><td>35.98</td><td>97.07</td><td>83.69</td><td>73.73</td></tr><tr><td>1.0</td><td>40.40</td><td>36.60</td><td>35.67</td><td>75.84</td><td>59.67</td><td>53.00</td></tr><tr><td colspan=\"7\">leads to mixed class distributions, impairing classification.</td></tr></table>", "text": "Transfer learning results on toy downstream datasets with different means and varying scale of covariance σ, with SUPSIAMpretraining on the toy dataset. For each scenario, the best results are in bold and the second-best results are underlined.", "num": null, "html": null}, "TABREF2": {"type_str": "table", "content": "<table><tr><td>α</td><td colspan=\"2\">CUB200 Dogs</td><td>Pets</td></tr><tr><td>0.0</td><td>41.46</td><td colspan=\"2\">61.51 80.09</td></tr><tr><td>0.2</td><td>42.07</td><td colspan=\"2\">64.28 82.27</td></tr><tr><td>0.5</td><td>43.48</td><td colspan=\"2\">64.65 82.38</td></tr><tr><td>0.8</td><td>42.16</td><td colspan=\"2\">62.94 81.76</td></tr><tr><td>1.0</td><td>36.10</td><td colspan=\"2\">54.57 75.13</td></tr><tr><td colspan=\"4\">is crucial for the generalization of representations learned</td></tr><tr><td colspan=\"2\">via supervised ANCL.</td><td/></tr></table>", "text": "Transfer learning results on fine-grained classification datasets, where the model is SUPSIAM-pretrained with different α on ImageNet-100. For each dataset, the best results are in bold and the second-best results are underlined.", "num": null, "html": null}, "TABREF3": {"type_str": "table", "content": "<table><tr><td>Dataset</td><td>ImageNet-100</td><td>VOC</td><td/></tr><tr><td>Method</td><td>Top-1</td><td>AP</td><td>AP50</td></tr><tr><td>SIMCLR</td><td>77.35</td><td colspan=\"2\">52.06 ± 0.31 78.70 ± 0.16</td></tr><tr><td>SUPCON</td><td>87.40</td><td colspan=\"2\">52.53 ± 0.47 79.44 ± 0.21</td></tr><tr><td>MOCO-V2</td><td>78.37</td><td colspan=\"2\">52.68 ± 0.04 79.08 ± 0.24</td></tr><tr><td>SUPMOCO</td><td>86.33</td><td colspan=\"2\">52.67 ± 0.04 79.52 ± 0.15</td></tr><tr><td>SIMSIAM</td><td>82.15</td><td colspan=\"2\">53.56 ± 0.10 79.82 ± 0.10</td></tr><tr><td>SUPSIAM  †</td><td>87.31</td><td colspan=\"2\">53.89 ± 0.26 80.28 ± 0.06</td></tr><tr><td>BYOL</td><td>84.93</td><td colspan=\"2\">53.54 ± 0.04 79.57 ± 0.01</td></tr><tr><td>SUPBYOL  †</td><td>87.43</td><td colspan=\"2\">53.69 ± 0.24 80.26 ± 0.17</td></tr></table>", "text": "Top-1 linear probing accuracy on ImageNet-100 and transfer learning performance on VOC object detection. The best results are in bold and the second-best results are underlined. Our proposed methods are marked with †.", "num": null, "html": null}, "TABREF4": {"type_str": "table", "content": "<table><tr><td>Method</td><td colspan=\"13\">CL Sup EMA Avg.Rank CIFAR10 CIFAR100 DTD Food MIT67 SUN397 Caltech CUB200 Dogs Flowers Pets</td></tr><tr><td>SIMCLR</td><td>✓</td><td/><td/><td>7.00</td><td>84.69</td><td>62.86</td><td>64.18 60.91 61.81</td><td>47.10</td><td>77.89</td><td>28.76</td><td>44.33</td><td>84.30</td><td>65.10</td></tr><tr><td>SUPCON</td><td>✓</td><td>✓</td><td/><td>4.73</td><td>88.82</td><td>68.89</td><td>65.18 59.34 63.76</td><td>50.09</td><td>87.30</td><td>35.84</td><td>61.68</td><td>89.05</td><td>80.12</td></tr><tr><td>MOCO-V2</td><td>✓</td><td/><td>✓</td><td>7.82</td><td>83.43</td><td>61.54</td><td>61.81 57.36 59.55</td><td>45.07</td><td>77.26</td><td>27.79</td><td>46.67</td><td>82.35</td><td>68.52</td></tr><tr><td>SUPMOCO</td><td>✓</td><td>✓</td><td>✓</td><td>4.09</td><td>89.05</td><td>69.29</td><td>65.44 59.04 63.46</td><td>50.05</td><td>87.54</td><td>37.75</td><td>62.80</td><td>89.69</td><td>80.81</td></tr><tr><td>SIMSIAM</td><td/><td/><td/><td>4.91</td><td>87.28</td><td>66.41</td><td>66.06 63.44 64.68</td><td>50.69</td><td>85.00</td><td>36.10</td><td>54.57</td><td>88.38</td><td>75.13</td></tr><tr><td>SUPSIAM  †</td><td/><td>✓</td><td/><td>2.27</td><td>89.95</td><td>70.88</td><td>66.51 61.46 64.45</td><td>51.50</td><td>88.86</td><td>43.48</td><td>64.65</td><td>90.27</td><td>82.38</td></tr><tr><td>BYOL</td><td/><td/><td>✓</td><td>3.82</td><td>88.26</td><td>68.08</td><td>67.52 64.63 65.70</td><td>51.21</td><td>85.85</td><td>37.10</td><td>57.80</td><td>88.14</td><td>78.78</td></tr><tr><td>SUPBYOL  †</td><td/><td>✓</td><td>✓</td><td>1.36</td><td>90.85</td><td>72.04</td><td>67.38 64.58 66.64</td><td>52.95</td><td>88.79</td><td>43.24</td><td>65.02</td><td>91.09</td><td>82.68</td></tr></table>", "text": "Transfer learning via linear evaluation results on various downstream datasets, where models are pretrained on ImageNet-100. CL, Sup., EMA stand for the cases when negative samples are considered, labels are used for pretraining, and the momentum network is adopted, respectively. Avg. Rank represents the average performance ranking across all datasets. For each dataset, the best results are in bold and the second-best results are underlined. Our proposed methods are marked with †.", "num": null, "html": null}, "TABREF5": {"type_str": "table", "content": "<table><tr><td>Method</td><td colspan=\"4\">CL Sup EMA Avg.Rank</td><td>Aircraft</td><td>CUB200</td><td>FC100</td><td>Flowers</td><td>Fungi</td><td>Omniglot</td><td>DTD</td><td>Traffic Signs</td></tr><tr><td>5-way 1-shot</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>SIMCLR</td><td>✓</td><td/><td/><td>7.25</td><td colspan=\"6\">29.22 ± 0.34 40.61 ± 0.43 35.53 ± 0.37 68.26 ± 0.50 42.44 ± 0.44 70.46 ± 0.54 55.43 ± 0.45</td><td>48.33 ± 0.43</td></tr><tr><td>SUPCON</td><td>✓</td><td>✓</td><td/><td>3.63</td><td colspan=\"6\">31.44 ± 0.35 48.75 ± 0.49 45.32 ± 0.41 77.99 ± 0.44 47.42 ± 0.45 80.66 ± 0.45 57.57 ± 0.47</td><td>68.66 ± 0.47</td></tr><tr><td>MOCO-V2</td><td>✓</td><td/><td>✓</td><td>7.38</td><td colspan=\"6\">25.54 ± 0.28 41.24 ± 0.46 36.73 ± 0.36 66.48 ± 0.50 41.84 ± 0.44 71.12 ± 0.51 54.75 ± 0.46</td><td>51.05 ± 0.43</td></tr><tr><td>SUPMOCO</td><td>✓</td><td>✓</td><td>✓</td><td>3.25</td><td colspan=\"6\">31.12 ± 0.35 49.04 ± 0.49 44.13 ± 0.41 78.90 ± 0.43 47.12 ± 0.45 83.43 ± 0.42 56.62 ± 0.46</td><td>71.17 ± 0.47</td></tr><tr><td>SIMSIAM</td><td/><td/><td/><td>5.00</td><td colspan=\"6\">30.67 ± 0.35 45.06 ± 0.47 41.51 ± 0.40 75.68 ± 0.47 45.22 ± 0.46 74.64 ± 0.50 58.28 ± 0.47</td><td>60.03 ± 0.45</td></tr><tr><td>SUPSIAM  †</td><td/><td>✓</td><td/><td>1.88</td><td colspan=\"6\">33.12 ± 0.37 49.58 ± 0.49 45.56 ± 0.41 78.12 ± 0.44 47.74± 0.46 84.02 ± 0.41 58.06 ± 0.48</td><td>71.00 ± 0.48</td></tr><tr><td>BYOL</td><td/><td/><td>✓</td><td>5.63</td><td colspan=\"6\">26.38 ± 0.30 46.45 ± 0.49 40.92 ± 0.40 74.27 ± 0.47 45.96 ± 0.46 68.13 ± 0.52 59.75 ± 0.48</td><td>57.44 ± 0.46</td></tr><tr><td>SUPBYOL  †</td><td/><td>✓</td><td>✓</td><td>2.00</td><td colspan=\"6\">32.66 ± 0.37 49.26 ± 0.48 45.28 ± 0.41 78.94 ± 0.43 47.81 ± 0.46 82.62 ± 0.44 59.98 ± 0.48</td><td>70.34 ± 0.48</td></tr><tr><td>5-way 5-shot</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>SIMCLR</td><td>✓</td><td/><td/><td>7.13</td><td colspan=\"6\">39.21 ± 0.44 54.33 ± 0.45 50.96 ± 0.37 86.98 ± 0.30 59.40 ± 0.47 86.72 ± 0.35 73.95 ± 0.36</td><td>69.27 ± 0.40</td></tr><tr><td>SUPCON</td><td>✓</td><td>✓</td><td/><td>3.38</td><td colspan=\"6\">44.63 ± 0.44 64.99 ± 0.46 64.04 ± 0.39 92.80 ± 0.23 66.75 ± 0.47 93.36 ± 0.25 75.60 ± 0.36</td><td>85.93 ± 0.36</td></tr><tr><td>MOCO-V2</td><td>✓</td><td/><td>✓</td><td>7.50</td><td colspan=\"6\">32.84 ± 0.35 53.42 ± 0.47 52.70 ± 0.36 84.72 ± 0.32 57.54 ± 0.48 87.74 ± 0.34 72.66 ± 0.37</td><td>71.93 ± 0.39</td></tr><tr><td colspan=\"11\">SUPMOCO 44.43 SIMSIAM ✓ ✓ ✓ 2.63 5.25 40.34 ± 0.44 60.66 ± 0.48 58.68 ± 0.38 91.04 ± 0.26 62.19 ± 0.49 88.92 ± 0.32 76.22 ± 0.36</td><td>79.50 ± 0.39</td></tr><tr><td>SUPSIAM  †</td><td/><td>✓</td><td/><td>2.38</td><td colspan=\"6\">45.98 ± 0.47 66.70 ± 0.45 64.54 ± 0.39 92.42 ± 0.23 66.61 ± 0.48 94.38 ± 0.23 76.43 ± 0.36</td><td>86.88 ± 0.36</td></tr><tr><td>BYOL</td><td/><td/><td>✓</td><td>5.38</td><td colspan=\"6\">35.30 ± 0.40 60.96 ± 0.49 59.33 ± 0.38 90.38 ± 0.26 63.12 ± 0.49 85.68 ± 0.35 77.60 ± 0.36</td><td>77.07 ± 0.40</td></tr><tr><td>SUPBYOL  †</td><td/><td>✓</td><td>✓</td><td>2.38</td><td>45.81</td><td/><td/><td/><td/></tr></table>", "text": "Few-shot classification accuracy averaged over 2000 episodes on various datasets, where models are pretrained on ImageNet-100. CL, Sup, EMA stand for the cases when negative samples are considered, labels are used for pretraining, and the momentum network is adopted, respectively. Avg.Rank represents the average performance ranking across all datasets. For each dataset, the best results are in bold and the second-best results are underlined. Our proposed methods are marked with †. ± 0.44 65.63 ± 0.46 64.30 ± 0.39 93.35 ± 0.21 66.64 ± 0.47 94.77 ± 0.22 74.73 ± 0.36 87.64 ± 0.34 ± 0.48 66.72 ± 0.46 65.72 ± 0.38 92.78 ± 0.22 66.47 ± 0.48 94.06 ± 0.24 77.57 ± 0.36 86.21 ± 0.37", "num": null, "html": null}, "TABREF6": {"type_str": "table", "content": "<table><tr><td>Target Pool</td><td>Size</td><td>Avg</td><td colspan=\"9\">CIFAR10 CIFAR100 DTD Food MIT67 SUN397 Caltech CUB200 Dogs Flowers Pets</td></tr><tr><td>Class-agnostic</td><td>8192</td><td>70.40</td><td>89.95</td><td>70.88</td><td>66.51 61.46 64.45</td><td>51.50</td><td>88.86</td><td>43.48</td><td>64.65</td><td>90.27</td><td>82.38</td></tr><tr><td>Class-wise</td><td colspan=\"2\">80 × 100 70.21</td><td>89.78</td><td>70.58</td><td>66.49 61.54 64.85</td><td>51.22</td><td>88.64</td><td>42.68</td><td>65.03</td><td>89.86</td><td>81.61</td></tr><tr><td>Class-wise</td><td colspan=\"2\">20 × 100 70.44</td><td>90.02</td><td>71.07</td><td>66.92 61.49 65.11</td><td>51.15</td><td>88.67</td><td>43.19</td><td>65.16</td><td>89.27</td><td>82.39</td></tr><tr><td>Class-wise</td><td colspan=\"2\">5 × 100 70.27</td><td>89.67</td><td>70.88</td><td>66.17 61.32 64.30</td><td>51.49</td><td>88.96</td><td>42.80</td><td>64.82</td><td>89.86</td><td>82.75</td></tr><tr><td>Class-wise</td><td colspan=\"2\">1 × 100 70.23</td><td>89.70</td><td>70.73</td><td>66.06 61.45 64.82</td><td>51.02</td><td>88.97</td><td>43.42</td><td>64.26</td><td>89.71</td><td>82.37</td></tr><tr><td>Learnable</td><td>100</td><td>70.37</td><td>89.91</td><td>70.41</td><td>67.00 61.36 65.15</td><td>51.58</td><td>88.81</td><td>42.97</td><td>65.08</td><td>89.57</td><td>82.28</td></tr></table>", "text": "Transfer learning via linear evaluation results on various downstream datasets, where the model is SUPSIAM-pretrained with different target pool design on ImageNet-100. Avg represents the average performance across each dataset. For each dataset, the best results are in bold and the second-best results are underlined.", "num": null, "html": null}, "TABREF8": {"type_str": "table", "content": "<table><tr><td>α</td><td>Avg</td><td colspan=\"9\">CIFAR10 CIFAR100 DTD Food MIT67 SUN397 Caltech CUB200 Dogs Flowers Pets</td></tr><tr><td colspan=\"2\">0.0 69.33</td><td>89.18</td><td>69.41</td><td>65.53 60.72 65.05</td><td>50.81</td><td>88.83</td><td>41.46</td><td>61.51</td><td>90.04</td><td>80.09</td></tr><tr><td colspan=\"2\">0.2 70.14</td><td>89.89</td><td>70.56</td><td>65.89 61.03 65.25</td><td>51.34</td><td>88.85</td><td>42.07</td><td>64.28</td><td>90.12</td><td>82.27</td></tr><tr><td colspan=\"2\">0.5 70.40</td><td>89.95</td><td>70.88</td><td>66.51 61.46 64.45</td><td>51.50</td><td>88.86</td><td>43.48</td><td>64.65</td><td>90.27</td><td>82.38</td></tr><tr><td colspan=\"2\">0.8 70.28</td><td>89.39</td><td>70.04</td><td>67.08 64.06 66.00</td><td>51.98</td><td>87.45</td><td>42.16</td><td>62.94</td><td>90.26</td><td>81.76</td></tr><tr><td colspan=\"2\">1.0 67.07</td><td>87.28</td><td>66.41</td><td>66.06 63.44 64.68</td><td>50.69</td><td>85.00</td><td>36.10</td><td>54.57</td><td>88.38</td><td>75.13</td></tr><tr><td colspan=\"5\">F.2. Ablation Study: Number of Positives from Target Pool</td><td/><td/><td/><td/><td/></tr></table>", "text": "Table F.1. Transfer learning via linear evaluation results on various downstream datasets, where the model is SUPSIAM pretrained with different α on ImageNet-100. Avg represents the average performance across each dataset. For each dataset, the best results are in bold and the second-best results are underlined.", "num": null, "html": null}, "TABREF9": {"type_str": "table", "content": "<table><tr><td>M</td><td>Avg</td><td colspan=\"9\">CIFAR10 CIFAR100 DTD Food MIT67 SUN397 Caltech CUB200 Dogs Flowers Pets</td></tr><tr><td colspan=\"2\">1 70.13</td><td>89.58</td><td>70.59</td><td>65.75 61.39 64.85</td><td>51.41</td><td>88.57</td><td>42.80</td><td>64.52</td><td>89.92</td><td>82.07</td></tr><tr><td colspan=\"2\">4 70.40</td><td>89.94</td><td>70.63</td><td>66.59 61.66 65.07</td><td>51.32</td><td>88.82</td><td>42.88</td><td>64.78</td><td>89.85</td><td>82.88</td></tr><tr><td colspan=\"2\">16 70.31</td><td>89.94</td><td>70.86</td><td>65.64 61.40 64.95</td><td>51.54</td><td>88.65</td><td>43.17</td><td>65.13</td><td>89.66</td><td>82.42</td></tr><tr><td colspan=\"2\">all 70.40</td><td>89.95</td><td>70.88</td><td>66.51 61.46 64.45</td><td>51.50</td><td>88.86</td><td>43.48</td><td>64.65</td><td>90.27</td><td>82.38</td></tr><tr><td colspan=\"5\">F.3. Transfer Learning with Different Batch Size</td><td/><td/><td/><td/><td/></tr></table>", "text": "2. Transfer learning via linear evaluation results on various downstream datasets, where the model is SUPSIAM-pretrained with different M on ImageNet-100. all stands for sampling all positives in the target pool. Avg represents the average performance across each dataset. For each dataset, the best results are in bold and the second-best results are underlined.", "num": null, "html": null}, "TABREF10": {"type_str": "table", "content": "<table><tr><td>Bsz</td><td>Model</td><td>Avg</td><td colspan=\"9\">CIFAR10 CIFAR100 DTD Food MIT67 SUN397 Caltech CUB200 Dogs Flowers Pets</td></tr><tr><td/><td colspan=\"2\">SUPCON 68.19</td><td>88.82</td><td>68.89</td><td>65.18 59.34 63.76</td><td>50.09</td><td>87.30</td><td>35.84</td><td>61.68</td><td>89.05</td><td>80.12</td></tr><tr><td>128</td><td colspan=\"2\">SUPCON  *  68.97</td><td>89.70</td><td>70.48</td><td>65.65 59.06 63.43</td><td>49.86</td><td>87.97</td><td>38.76</td><td>63.74</td><td>89.22</td><td>80.83</td></tr><tr><td/><td colspan=\"2\">SUPSIAM 70.40</td><td>89.95</td><td>70.88</td><td>66.51 61.46 64.45</td><td>51.50</td><td>88.86</td><td>43.48</td><td>64.65</td><td>90.27</td><td>82.38</td></tr><tr><td/><td colspan=\"2\">SUPCON 68.42</td><td>89.10</td><td>69.40</td><td>65.32 59.21 63.25</td><td>50.63</td><td>88.22</td><td>36.05</td><td>62.60</td><td>89.10</td><td>79.80</td></tr><tr><td>256</td><td colspan=\"2\">SUPCON  *  68.86</td><td>89.46</td><td>70.06</td><td>65.88 58.73 63.92</td><td>50.04</td><td>87.84</td><td>38.28</td><td>63.02</td><td>89.06</td><td>81.22</td></tr><tr><td/><td colspan=\"2\">SUPSIAM 70.44</td><td>90.07</td><td>70.77</td><td>66.28 61.89 65.18</td><td>51.77</td><td>88.83</td><td>43.09</td><td>64.90</td><td>89.99</td><td>82.11</td></tr></table>", "text": "3. Transfer learning via linear evaluation results on various downstream datasets, where the model is pretrained with different batch size on ImageNet-100. Bsz refers to the batch size during pretraining. Avg represents the average performance across each dataset. The model marked with * indicates the inclusion of a memory bank.", "num": null, "html": null}, "TABREF12": {"type_str": "table", "content": "<table><tr><td>Pretext</td><td colspan=\"7\">Downstream SIMCLR SUPCON SIMSIAM SUPSIAM BYOL SUPBYOL</td></tr><tr><td>CIFAR10</td><td>CIFAR10 CIFAR100</td><td>89.58 56.36</td><td>95.15 53.82</td><td>93.36 60.51</td><td>94.73 61.76</td><td>91.56 50.20</td><td>94.88 55.47</td></tr><tr><td>CIFAR100</td><td>CIFAR10 CIFAR100</td><td>80.36 64.77</td><td>79.99 74.03</td><td>78.81 70.63</td><td>85.19 75.05</td><td>78.35 65.42</td><td>80.09 74.36</td></tr></table>", "text": "TableH.1. Comparision of CL and ANCL with their self-supervised / supervised versions with ResNet-18 on CIFAR10 and 100. We run all experiments for 1000 epochs. If the pretext and downstream datasets are aligned, the supervised version shows improved performance. In contrast, when there is a mismatch, performance gains are observed only in the ANCL scenario.", "num": null, "html": null}}}}