{"paper_id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "CurBench: Curriculum Learning Benchmark", "abstract": "Curriculum learning is a training paradigm where machine learning models are trained in a meaningful order, inspired by the way humans learn curricula. Due to its capability to improve model generalization and convergence, curriculum learning has gained considerable attention and has been widely applied to various research domains. Nevertheless, as new curriculum learning methods continue to emerge, it remains an open issue to benchmark them fairly. Therefore, we develop CurBench, the first benchmark that supports systematic evaluations for curriculum learning. Specifically, it consists of 15 datasets spanning 3 research domains: computer vision, natural language processing, and graph machine learning, along with 3 settings: standard, noise, and imbalance. To facilitate a comprehensive comparison, we establish the evaluation from 2 dimensions: performance and complexity. CurBench also provides a unified toolkit that plugs automatic curricula into general machine learning processes, enabling the implementation of 15 core curriculum learning methods. On the basis of this benchmark, we conduct comparative experiments and make empirical analyses of existing methods.", "pdf_parse": {"paper_id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "abstract": [{"text": "Curriculum learning is a training paradigm where machine learning models are trained in a meaningful order, inspired by the way humans learn curricula. Due to its capability to improve model generalization and convergence, curriculum learning has gained considerable attention and has been widely applied to various research domains. Nevertheless, as new curriculum learning methods continue to emerge, it remains an open issue to benchmark them fairly. Therefore, we develop CurBench, the first benchmark that supports systematic evaluations for curriculum learning. Specifically, it consists of 15 datasets spanning 3 research domains: computer vision, natural language processing, and graph machine learning, along with 3 settings: standard, noise, and imbalance. To facilitate a comprehensive comparison, we establish the evaluation from 2 dimensions: performance and complexity. CurBench also provides a unified toolkit that plugs automatic curricula into general machine learning processes, enabling the implementation of 15 core curriculum learning methods. On the basis of this benchmark, we conduct comparative experiments and make empirical analyses of existing methods.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Throughout the development of machine learning, a large number of works have been greatly influenced by human learning. Curriculum learning is such a research topic within machine learning that draws inspiration from a remarkable aspect of human learning: curriculum, i.e., learning in a pur-Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "section": "Introduction", "sec_num": "1."}, {"text": "poseful and meaningful order (<PERSON> et al., 2021a; <PERSON><PERSON><PERSON> et al., 2022) . In contrast to conventional machine learning methods where training examples are randomly input, curriculum learning aims to facilitate learning by gradually increasing the difficulty of data or tasks experienced by the model (<PERSON><PERSON> et al., 2009) . Since this easy-to-hard training paradigm is verified to bring the advantage of enhancing model generalization and accelerating convergence speed (<PERSON> et al., 2016; <PERSON><PERSON><PERSON> et al., 2018) , it has aroused widespread interest among researchers in harnessing its potential across diverse application domains, such as computer vision (CV) (<PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2017) , natural language processing (NLP) (Platanios et al., 2019; <PERSON><PERSON> et al., 2019; <PERSON> et al., 2018) , graph machine learning (<PERSON> et al., 2023; <PERSON> et al., 2021b; <PERSON> et al., 2023; <PERSON> et al., 2024; <PERSON> et al., 2024) , multimodal learning (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON> et al., 2023) , recommender systems (<PERSON> et al., 2021b; a; <PERSON> et al., 2023; <PERSON> et al., 2023a) , reinforcement learning (RL) (<PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2017; <PERSON> et al., 2018b) , and others (<PERSON> et al., 2022; <PERSON> et al., 2022b) .", "section": "Introduction", "sec_num": "1."}, {"text": "Despite the significant progress and the wide application of curriculum learning, the increasing number of works has posed challenges in terms of their comparison and evaluation, mainly due to the differences in their experimental setups including datasets, backbone models, and settings. For instance, DCL (<PERSON><PERSON><PERSON> et al., 2019) and DDS (<PERSON> et al., 2020) use the same WideResNet-28-10 model (Zagoruyk<PERSON> & Komodakis, 2016) , but perform experiments on different datasets: CIFAR-100 and CIFAR-10 ( <PERSON><PERSON><PERSON><PERSON> et al., 2009) respectively. Similarly, DI-HCL (<PERSON> et al., 2020) and CBS (<PERSON><PERSON> et al., 2020) leverage the same ImageNet (<PERSON><PERSON> et al., 2009) dataset, but employ distinct models: ResNet-50 and ResNet-18 (<PERSON> et al., 2016) respectively. Furthermore, while MCL (Zhou & Bilmes, 2018) and LRE (<PERSON> et al., 2018a) utilize the same MNIST dataset and LeNet model (<PERSON><PERSON><PERSON> et al., 1998) , they adopt different settings: standard and imbalanced labels respectively. Consequently, their experimental results cannot be compared directly, which makes it challenging to conduct a fair evaluation. The absence of a standardized evaluation not only hinders researchers from accurately assessing their own contributions when they propose a new method but also poses barriers for users when they seek a suitable method for their specific tasks.", "section": "Introduction", "sec_num": "1."}, {"text": "To deal with this issue, researchers have made notable efforts to evaluate and summarize existing works. From a theoretical perspective, there have been surveys covering general curriculum learning (<PERSON> et al., 2021a; <PERSON><PERSON><PERSON> et al., 2022) as well as specific ones for graph (<PERSON> et al., 2023) and RL (<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2020) , all of which manage to formulate and categorize relevant methods comprehensively. Although they offer valuable theoretical insights, current surveys do not incorporate any practical implementation or experimental results. From an empirical perspective, there has been an open-source library on curriculum learning (<PERSON> et al., 2022a) , which reproduces multiple related methods through a unified framework. Although it provides empirical results of the implemented methods, this library only supports the classification task on CIFAR-10, limited in experimental setups. In conclusion, the related works fail to address the open issue of evaluating and comparing curriculum learning methods completely.", "section": "Introduction", "sec_num": "1."}, {"text": "In order to address the absence of benchmarks in this field, we propose CurBench, the first benchmark for systematic evaluations of curriculum learning, as shown in Figure 1 . Concretely, it encompasses 15 prevalent datasets, spanning 3 research domains including CV, NLP, and graph to ensure the reliability of evaluation. These datasets are further preprocessed into 3 settings including standard, noise, and imbalance to reveal the capability of methods to enhance model generalization and robustness. Without loss of generality, a total of 9 prevalent backbone models of varying types and scales adapted to the above datasets are employed in an appropriate manner, incorporating corresponding hyperparameters, optimizers, and so on. Most of the datasets, settings, and models are commonly used in previous re-lated works, while the rest are supplemented in this work to investigate how these methods can adapt to the tasks in other domains. For ease of use, this benchmark also provides a unified toolkit that plugs automatic curricula into general machine learning processes and reproduces a collection of 15 core curriculum learning approaches. Based on these implementations in CurBench, we further perform a comprehensive evaluation from 2 dimensions including performance and complexity, presenting the improvements the methods bring and the additional resources they consume.", "section": "Introduction", "sec_num": "1."}, {"text": "Furthermore, we delve into our benchmark, organize experimental outcomes, conduct in-depth analyses, and obtain some intriguing findings. First, there has been no such method that outperforms others all the time, and the effectiveness depends on specific scenarios. Second, curriculum learning brings more significant improvements in noise settings than in standard and imbalance ones. Third, methods by teacher transferring have edges in noise settings, while methods by reweighting perform relatively well in imbalance settings. Lastly, methods involving gradient calculation and extra learnable networks generally have higher time and space complexity.", "section": "Introduction", "sec_num": "1."}, {"text": "Our contributions are summarized as follows:", "section": "Introduction", "sec_num": "1."}, {"text": "• We propose CurBench, the first benchmark on curriculum learning to the best of our knowledge.", "section": "Introduction", "sec_num": "1."}, {"text": "• We conduct extensive experiments to impartially evaluate and compare the performance and complexity of existing curriculum learning methods under various experimental setups.", "section": "Introduction", "sec_num": "1."}, {"text": "• We make in-depth analyses and demonstrate intriguing observations on curriculum learning based on empirical results derived from CurBench. 1 . The statistics of 15 datasets adopted in CurBench, which covers a wide range of scales across 3 research domains in 3 settings. \"<PERSON><PERSON><PERSON>\" and \"<PERSON>\" refers to the correlation coefficient. \"Noise-0.4\" means 40% data samples are independently attached with random incorrect labels. \"Imbalance-50\" means a ratio of 50 between the number of samples in the largest class and that in the smallest class in a long-tailed dataset where the number of samples for each class follows a geometric sequence. The imbalance setting is not applied to NLP and graph datasets, which are imbalanced originally.", "section": "Introduction", "sec_num": "1."}, {"text": "Curriculum learning, much like many other topics in machine learning, draws inspiration from human learning. It refers to a training strategy where models learn from input data in a meaningful order, imitating the way humans learn from curricula. The emergence of this idea could at least be traced back to <PERSON><PERSON>'s work (<PERSON><PERSON>, 1993) in 1993, which advocated the importance of starting small. In 2009 , <PERSON><PERSON> et al. (<PERSON><PERSON> et al., 2009) first introduced a formal definition of curriculum learning and explored when, why, and how a curriculum could benefit machine learning. In the early stages, curricula for models were entirely predefined by humans, and the most typical method was named Baby Step (<PERSON><PERSON><PERSON> et al., 2010) . However, this type of predefined approach is not flexible and general enough for widespread applications. In 2010, <PERSON> et al. (<PERSON> et al., 2010) proposed self-paced learning (SPL), enabling automatic curriculum scheduling by ordering data according to their training loss. Subsequently, a variety of automatic curriculum learning methods have continued to emerge. For example, transfer learning methods (<PERSON><PERSON> et al., 2018; Ha<PERSON> & Weinshall, 2019) employ teacher models to offer student models curricula. Reinforcement learning methods (<PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2020) allow teacher models to adapt curriculum based on the feedback from student models. In addition, there are other ones based on Bayesian optimization (<PERSON><PERSON><PERSON><PERSON> et al., 2016) , meta-learning (<PERSON> et al., 2018a; <PERSON> et al., 2019) , and adversarial learning (<PERSON> et al., 2020) for implementing automatic curriculum learning.", "section": "Curriculum Learning", "sec_num": "2.1."}, {"text": "To the best of our knowledge, CurBench is the first benchmark on curriculum learning. Despite no related benchmarks, there have been numerous efforts to investigate and summarize the curriculum learning methods from different perspectives. For example, <PERSON> et al. (<PERSON> et al., 2021a) survey curriculum learning and propose a general framework to cover the related methods by abstracting them into two key components, i.e., a difficulty measurer to tell what data or task is easy or hard to learn and a learning scheduler to decide when to learn the easier or harder part, and further categorize the methods according to the implementation of these two components. <PERSON><PERSON><PERSON> et al. (<PERSON><PERSON><PERSON> et al., 2022) also survey curriculum learning and propose a generic algorithm for it based on the definition of machine learning, i.e., data, modal, and task, and organize the methods according to their application domains and tasks. <PERSON><PERSON><PERSON> et al. (<PERSON><PERSON><PERSON> et al., 2020) survey the relevant methods applied to RL and abstract them into three steps, i.e., task generation, sequencing, and transfer learning. <PERSON><PERSON><PERSON> et al. (<PERSON><PERSON><PERSON> et al., 2020 ) also focus on curriculum learning for RL, and classify the methods based on three questions, i.e., why, what control, and what optimize. <PERSON> et al. (<PERSON> et al., 2023) review the tailored methods for graph, and group them according to the tasks, i.e., node-level, link-level, and graph-level. However, these works only summarize and analyze the methods from the theoretical aspect. On the other hand, <PERSON> et al. (<PERSON> et al., 2022a) develop CurML, a code library for curriculum learning, which designs a unified framework for the reproduction and comparison of existing methods from the empirical aspect. Nevertheless, it can only conduct experiments on a single task within a specific domain, significantly limiting its generality and reliability. Therefore, it is necessary to develop a benchmark across diverse experimental setups for a fair, reliable, and systematic study on curriculum learning.", "section": "Summative Work on Curriculum Learning", "sec_num": "2.2."}, {"text": "In this section, we describe our design for the benchmark in detail. First, we clarify the scope of this benchmark in Section 3.1. Then, we introduce the adopted datasets in Section 3.2, followed by the corresponding settings in Section 3.3 and the backbone models in Section 3.4. Lastly, we elaborate on the evaluation dimensions in Section 3.5.", "section": "Curriculum Learning Benchmark", "sec_num": "3."}, {"text": "CurBench focuses on benchmarking existing prevalent curriculum learning methods for supervised tasks in CV, NLP, and graph domains. This is because CV and NLP are representative research domains in machine learning, with datasets in these areas frequently used to validate the performance of curriculum learning methods, as shown in Table 6 .", "section": "Benchmark Scope", "sec_num": "3.1."}, {"text": "Graph data, being structured, differs from the unstructured data of images and text, contributing to the diversity of CurBench, and curriculum learning in the graph domain has gained significant attention recently. Besides, the main challenge of the tasks included in CurBench lies in designing appropriate curricula at the data level so that the models can be guided to better cope with standard, noisy, and imbalanced datasets. In contrast, the methods designed at the task level and specifically targeting the RL domain are not within the scope of this work. We plan to expand the scope of CurBench in a future version, as stated in Section 6.", "section": "Benchmark Scope", "sec_num": "3.1."}, {"text": "Table 1 outlines the datasets included in CurBench, all of which are publicly available and widely used in their respective domains. Besides, they vary in scale from hundreds of samples to hundreds of thousands. A brief introduction to the datasets and our preprocessing is listed as follows.", "section": "Dataset", "sec_num": "3.2."}, {"text": "CV Domain: CIFAR-10 and CIFAR-100 (<PERSON><PERSON><PERSON><PERSON> et al., 2009) consist of 32 × 32 × 3 color images in 10 and 100 classes respectively. Tiny-ImageNet (<PERSON> <PERSON>, 2015) is a subset of the ILSVRC2012 version of ImageNet (<PERSON><PERSON> et al., 2009) and consists of 64 × 64 × 3 down-sampled images. Since the test set of Tiny-ImageNet is not released with labels, we use the validation set as the test set. For these 3 datasets, we split the original training set into a new training set and a validation set with a 9:1 ratio.", "section": "Dataset", "sec_num": "3.2."}, {"text": "NLP Domain: All 8 datasets are sourced from GLUE (<PERSON> et al., 2018) , which is a collection of tools for evaluating models across diverse natural language understanding tasks. GLUE originally contains 9 datasets, and we follow BERT (<PERSON> et al., 2018) , excluding the problematic WNLI set and using the remaining 8 datasets. Since the test sets are not released with labels, we report the results on the validation sets.", "section": "Dataset", "sec_num": "3.2."}, {"text": "Graph Domain: The ogbg-molhiv dataset belongs to Open Graph Benchmark (OGB) (<PERSON> et al., 2020) , a collection of realistic, large-scale, and diverse benchmark datasets for graphs. We strictly follow its origin split scheme, split ratios, and metrics. The other 3 datasets come from TU-Dataset (<PERSON> et al., 2020) , a collection that consists of over 120 graph datasets of varying sizes from a wide range of applications. Since there are no established training and test set split, we randomly divide the original datasets into training, validation, and test sets with an 8:1:1 ratio.", "section": "Dataset", "sec_num": "3.2."}, {"text": "To robustly evaluate the curriculum learning methods, we establish the 3 settings as follows.", "section": "Setting", "sec_num": "3.3."}, {"text": "Standard: After dividing the datasets into training, validation, and test sets as mentioned above, we do not perform any further data processing.", "section": "Setting", "sec_num": "3.3."}, {"text": "Noise-p: We follow previous works (<PERSON> et al., 2016; <PERSON> et al., 2018a; <PERSON> et al., 2019) and apply uniform noise by independently changing the label of each sample in the training set to a random one with a probability of p ∈ (0.0, 1.0]. When p = 0, it degenerates to the standard setting.", "section": "Setting", "sec_num": "3.3."}, {"text": "Imbalance-r: We follow previous works (<PERSON><PERSON> et al., 2019; <PERSON> et al., 2019) to form a long-tailed dataset by reducing the number of samples per class in the training set. Let c ∈ {0, 1, 2, ..., C -1} be the class index, C be the number of classes, n c be the number of samples in the c th class, and then an originally balanced dataset satisfies", "section": "Setting", "sec_num": "3.3."}, {"text": "n 0 ≈ n 1 ≈ ... ≈ n C-1 .", "section": "Setting", "sec_num": "3.3."}, {"text": "We implement the imbalance setting by requiring n c to follow the exponential function n c = n 0 d c where d ∈ (0, 1) and define the imbalance factor r = n 0 : n C-1 as the ratio between the number of samples in the largest class and that in the smallest class. When r = 1, it degenerates to the standard setting.", "section": "Setting", "sec_num": "3.3."}, {"text": "LeNet Convolution ∼ 0.07M ResNet-18 Convolution ∼ 11.2M ViT Attention ∼ 9.6M NLP LSTM Recurrent ∼ 10.4M BERT Attention ∼ 109M GPT2 Attention ∼ 124M Graph GCN Convolution ∼ 0.01M GAT Attention ∼ 0.14M GIN Isomorphism ∼ 0.01M", "section": "CV", "sec_num": null}, {"text": "Table 2 . The statistics of 9 backbone models adopted in CurBench, which covers various mechanisms and scales. \"∼\" signifies an approximation, and \"M\" represents million. (<PERSON> et al., 2018) is developed based on <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> test theory and emphasizes the importance of summation as the readout function.", "section": "CV", "sec_num": null}, {"text": "To ensure a comprehensive analysis of existing methods, we consider the following 2 evaluation dimensions.", "section": "Evaluation", "sec_num": "3.5."}, {"text": "Performance: We adopt the widely accepted metrics on each dataset, such as accuracy on image, F1 score, <PERSON><PERSON><PERSON> Correlation, and <PERSON> Correlation on the GLUE benchmark, AUC (<PERSON> et al., 2021) on graph. To display the results clearly, we report the average and standard deviation of the metric over 5 runs for each dataset.", "section": "Evaluation", "sec_num": "3.5."}, {"text": "Complexity: It is essential to examine the time and space complexity of each method because they always cost extra computational time and sources to assess model competence and data difficulty for appropriate curricula design. We record the training time and maximum memory consumption on the same GPU device as the indicators of the complexity.", "section": "Evaluation", "sec_num": "3.5."}, {"text": "To facilitate the use of our CurBench, we develop a companion toolkit based on CurML (<PERSON> et al., 2022a) for the entire pipeline of applying curriculum learning to various machine learning tasks, reproducing 15 core methods. Compared to CurML, this toolkit extends the methods to accommodate inputs in various data formats and diverse output evaluation metrics and provides searched hyperparameters for each method. As illustrated in Figure 2 , we summarize and abstract the whole toolkit into 5 modules: data processing, model loading, objective fitting, curriculum learning, and evaluation.", "section": "<PERSON><PERSON><PERSON>", "sec_num": "4.1."}, {"text": "Data Processing: This module aims to prepare data according to the specified dataset and setting. Given a data name in a format like \"cifar10\", \"cifar100-noise-p\" or \"tinyimagenetimbalance-r\", this module can automatically parse it, split the dataset into training, validation, and test set, and process the training set by adding noise with probability p or forming imbalance with factor r.", "section": "<PERSON><PERSON><PERSON>", "sec_num": "4.1."}, {"text": "Model Loading: This module is used to initialize the model based on the model name and the target dataset. For instance, CV models need to modify their input layer to accommodate input images and patch sizes. Similarly, graph models require node features and edge relationships when construct- ing graph convolutional layers. Besides, the class number of the dataset determines the models' output layer.", "section": "<PERSON><PERSON><PERSON>", "sec_num": "4.1."}, {"text": "Objective Fitting: This module handles the process where models learn and fit datasets to accomplish target tasks. For different research domains, we select tailored hyperparameters, optimizers, loss functions, and so on. Unlike common machine learning, the training procedure in this module is guided by the curriculum learning module.", "section": "Performance", "sec_num": null}, {"text": "Curriculum Learning: This module integrates 15 core curriculum learning methods, all of which are abstracted as a class for easy plug-in into the objective fitting module. This design of abstracting methods as classes ensures that the module is extensible for new methods. Currently, we divide the existing methods into the following 3 categories. It is worth noting that this categorization is intended to facilitate the implementation and extension of various methods within a unified framework, but it does not imply that methods within the same category necessarily share similar properties or performance.", "section": "Performance", "sec_num": null}, {"text": "• via Data Selection: The primary approach to implementing curriculum is through data selection so that models can progressively learn from a subset to the entire dataset in a meaningful order. The methods belong to this category are vanilla SPL (<PERSON> et al., 2010) , DIHCL (<PERSON> et al., 2020) , and so on (<PERSON><PERSON> et al., 2018; Zhou & Bilmes, 2018; <PERSON> et al., 2019; <PERSON> et al., 2021; <PERSON> et al., 2023b) . Some methods select data subsets based on sample difficulty, while others select data based on sample class.", "section": "Performance", "sec_num": null}, {"text": "• via Model Adjustment: An innovative idea for designing curricula is to regulate the amount of data information the model receives by modifying its architecture. CBS (<PERSON><PERSON> et al., 2020) , which employs a Gaussian filter to manage information intake, is a typical one.", "section": "Performance", "sec_num": null}, {"text": "• via Loss Reweighting: Loss reweighting can be regarded as a \"soft\" version of data selection. Intuitively, assigning a low weight to a data sample is almost equivalent to disregarding it. A common practice to reweight loss is through meta-learning (<PERSON> et al., 2017) , such as LRE (<PERSON> et al., 2018a) , MW-Net (<PERSON> et al., 2019) , and DDS (<PERSON> et al., 2020) , all of which employ a meta-network to assess the weights of losses and optimize the meta-network with the validation set. Additionally, there are other approaches, such as variants of SPL (<PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2020) , DCL (<PERSON><PERSON><PERSON> et al., 2019) , ScreenerNet (Kim & Choi, 2018) , and SuperLoss (<PERSON><PERSON><PERSON> et al., 2020) .", "section": "Performance", "sec_num": null}, {"text": "Evaluation: This module is utilized to report results from 2 aspects, i.e., performance and complexity, in order to respectively demonstrate the effectiveness and efficiency of different methods. The performance metrics depend on the target datasets and tasks, and the complexity metrics include training time and maximum GPU memory consumption.", "section": "Performance", "sec_num": null}, {"text": "Figure 3 illustrates the python-like sample code of our CurBench toolkit, where an object of the SPLTrainer class is instantiated given the essential parameters, including a CIFAR-10 dataset name with the noise setting for data processing and a ResNet-18 net name for model loading. All of the above are put together to fit and evaluate the final result.", "section": "Example Usage", "sec_num": "4.2."}, {"text": "With only a few lines of code, a dozen curriculum learning methods can be easily implemented and reproduced. On the basis of this tool, we conduct a multitude of experiments, and we will report the experimental setups and results in the next section.", "section": "Example Usage", "sec_num": "4.2."}, {"text": "To ensure a fair and reproducible evaluation, we fix all possible confounding factors and report the average and standard deviation results of 5 runs with different fixed random seeds for each combination of datasets, backbone models, and settings. The detailed hyperparameters for both training processes and curriculum learning methods are presented in the Appendix.", "section": "Experimental Setup", "sec_num": "5.1."}, {"text": "Table 3 presents the overall performances with and without curriculum learning under different combinations of backbone models, datasets, and settings. The detailed results of each specific curriculum learning method are attached in the Appendix, and we report the best ones among them in this table. The imbalance setting is not applied to NLP and graph datasets, where the number of samples in each class is imbalanced originally.", "section": "Main Results", "sec_num": "5.2.1."}, {"text": "It is observed that curriculum learning can bring consistent improvement across domains. Compared to standard and imbalance settings, curriculum learning benefits much more in noise settings. This phenomenon is consistent with existing theoretical analysis, where curriculum learning is able to denoise and guide machine learning by discarding the difficult and possibly noisy data in the early stages of training. Besides, there is no such method that can outperform the others all the time, and the effectiveness of curriculum learning methods still depends on the target scenarios. For example, ScreenerNet (Kim & Choi, 2018) exhibits superior performance on CV datasets compared to graph datasets, and TTCL (<PERSON> et al., 2018) performs better in noise settings than in standard and imbalance ones. Therefore, it is essential to explore more general methods while also researching methods tailored to specific environments.", "section": "Main Results", "sec_num": "5.2.1."}, {"text": "Figure 4 demonstrates the performances of curriculum learning methods on datasets with different noise ratios p ∈ {0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8}. Without loss of generality, we select a backbone model and a dataset from each research domain. Some methods such as CBS, LGL, C2F, and EfficientTrain are only applied to CV datasets and not to NLP and graph datasets due to the following reasons. CBS (<PERSON><PERSON> et al., 2020) requires convolutional layers in backbone models, and such models in CurBench are only within the CV domain. LGL (<PERSON> et al., 2019) and C2F (<PERSON><PERSON><PERSON> et al., 2021) require multiple classes for clustering, but most NLP and graph datasets in CurBench have only two classes. EfficientTrain (<PERSON> et al., 2023b) is based on data augmentation techniques on images.", "section": "Results in Noise Settings", "sec_num": "5.2.2."}, {"text": "We can observe that TTCL (<PERSON><PERSON><PERSON> et al., 2018) , the method by teacher transferring, obtains competitive performances regardless of the noise ratio, thanks to the guidance from the teacher model pretrained on the clean dataset. In contrast, SPL (<PERSON> et al., 2010) , which is similar to TTCL but guides the learning by itself, performs relatively poorly. It is because a model not fully trained is not that competent to accurately distinguish noisy or hard data.", "section": "Results in Noise Settings", "sec_num": "5.2.2."}, {"text": "Figure 5 depicts the performances on CIFAR-10 with varying imbalance factor r ∈ {1, 10, 20, 50, 100, 200}.", "section": "Results in Imbalance Settings", "sec_num": "5.2.3."}, {"text": "It is observed that all methods achieve similar performances under different imbalance ratios. When the imbalance factor r increases, the differences between the methods become evident. Relatively speaking, the methods by data reweighting, such as DCL (<PERSON><PERSON><PERSON> et al., 2019) and SuperLoss (<PERSON><PERSON><PERSON> et al., 2020) , perform well because they can mitigate the impact of imbalanced classes by reassigning the weight of data or even class.", "section": "Results in Imbalance Settings", "sec_num": "5.2.3."}, {"text": "Compared with noise settings, curriculum learning brings less significant improvements and shows less variation between methods in imbalance settings. This is primarily because most curriculum learning methods focus on the difficulty of samples instead of classes, leading to overall better performances in noise settings than in imbalance settings. Additionally, the differences in judging difficult or noisy samples result in larger performance disparities among methods in noise settings. Table 3 . The empirical performances of 9 backbone models over 15 datasets in 3 settings with and without curriculum learning methods. The rows with \"+ CL\" present the best performances achieved among the methods involved in this benchmark. The bold font highlights the superior performances brought by curriculum learning. The imbalance setting is not applied to NLP and graph datasets, which are imbalanced originally. Note: The detailed performances of each method are reported in Table 9 -11 in the Appendix. ", "section": "Results in Imbalance Settings", "sec_num": "5.2.3."}, {"text": "Figure 6 shows the time and space complexity of each method in the case of ResNet-18 and CIFAR-10, measured by GPU training time (Hour) and maximum GPU memory consumption (GB).", "section": "Complexity", "sec_num": "5.3."}, {"text": "The whole figure can be divided into 3 parts. The first is the upper right corner, which contains the methods requiring gradient calculation and meta-network training, resulting in high time and space complexity. The second is the middle part with the point of ScreenerNet, which also introduces an extra network but only requires once backward, leading to less complexity. The third is the lower left corner, which includes most of the methods consuming similarly small amounts of training time and GPU memory because they measure data difficulty and schedule curriculum in a relatively intuitive way and do not demand a learnable network with a large number of parameters. 8 in the Appendix.", "section": "Complexity", "sec_num": "5.3."}, {"text": "In this paper, we propose CurBench, the first benchmark for curriculum learning. It covers a broad range of research domains, datasets, backbone models, settings, and evaluation dimensions, ensuring a fair, reliable, and systematic evaluation of existing curriculum learning methods. For convenient utilization, it is complemented by a toolkit that implements essential related works in a unified pipeline and applies them to various machine learning tasks. Through empirical results and theoretical analyses, we provide valuable findings on curriculum learning. In conclusion, CurBench holds the potential to benefit future research and suggest promising directions.", "section": "Conclusion", "sec_num": "6."}, {"text": "Limitations: Despite the benefits of our CurBench, we also recognize the following limitations in this version and intend to refine them in future expansions.", "section": "Conclusion", "sec_num": "6."}, {"text": "• CurBench mainly covers supervised learning in CV, NLP, and graph domains, but has not incorporated the datasets, backbone models, and tasks related to other domains such as audio processing, multimodal learning, recommender systems, and robotics. Additionally, CurBench has not involved unsupervised, semi-supervised, and reinforcement learning. Given the importance of these topics in the context of curriculum learning applications, they will be integrated as a significant part of future versions.", "section": "Conclusion", "sec_num": "6."}, {"text": "• CurBench currently employs publicly available datasets that are commonly used in their respective domains. However, CurBench has not yet introduced any new datasets. Designing specialized datasets for curriculum learning is essential because these datasets can better align with the unique requirements and objectives of curriculum learning methodologies. We recognize the importance of this task and intend to undertake it in the future.", "section": "Conclusion", "sec_num": "6."}, {"text": "• CurBench has not evaluated the performance of curriculum learning on large models, which deserves in-depth exploration in this era of large models. Considering that large models often encounter vast amounts of data with varying quality when learning, it is suitable to utilize curriculum learning for guidance and denoising. We plan to include the prevalent large-scale language and multimodal models in our future work.", "section": "Conclusion", "sec_num": "6."}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none of which we feel must be specifically highlighted here.", "section": "Impact Statement", "sec_num": null}, {"text": "In this appendix, we first list the essential information of the datasets in Section B and backbone models in Section C. Then we summarize the curriculum methods implemented in this work in Section D to present how these methods were evaluated when they were proposed. After providing the training hyperparameters in Section E and method hyperparameters in Section F, we report the detailed performance and complexity of each method in various experimental setups in Section G.", "section": "Impact Statement", "sec_num": null}, {"text": "All the datasets included in CurBench are publicly available for research. To eliminate the risk of ethical or license issues, we list the essential information of the datasets, such as their home pages, common download links, and licenses. ", "section": "B. Datasets", "sec_num": null}, {"text": "For the standardization and reliability of CurBench, we implement all backbone models by referencing highly recognized code repositories as shown in Table 5 . Among these models, BERT and GPT2 are initiated with the pretrained parameters from Hugging Face and finetuned in this work, while others are trained from scratch.", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "When designing CurBench, we are inclined to the datasets and models used in previous works for evaluation. Therefore, we have surveyed what datasets and models are commonly employed and completed the Table 6 .", "section": "D. Curriculum Learning Methods", "sec_num": null}, {"text": "It can be obviously found that when researchers propose a curriculum learning method, they always conduct experiments on image classification tasks for performance evaluation. Only a few authors will try to apply their methods to the datasets for object detection or neural machine translation. Besides, not all works take different settings, such as noise or imbalance, into consideration.", "section": "D. Curriculum Learning Methods", "sec_num": null}, {"text": "Therefore, as stated in the main text, we not only select the datasets and models in the CV domain, which are commonly used in previous related works, but also supplement those in the NLP and graph domains to investigate how the methods can adapt to various scenarios.", "section": "D. Curriculum Learning Methods", "sec_num": null}, {"text": "To ensure a fair evaluation, we run 5 times with fixed different random seeds s ∈ {42, 666, 777, 888, 999}, and report the average and standard deviation results. Besides, we strictly set the training hyperparameters as follows:", "section": "E. Training Hyperparameters", "sec_num": null}, {"text": "LeNet, ResNet-18, ViT: We choose a batch size of 50, and use an Adam optimizer to train the model with a constant learning rate of 0.0001 for 200 epochs.", "section": "E. Training Hyperparameters", "sec_num": null}, {"text": "We choose a batch size of 50, and use a SGD optimizer to train the model with a cosine annealing learning rate of 0.00001∼1 for 10 epochs.", "section": "LSTM:", "sec_num": null}, {"text": "BERT, GPT2: We choose a batch size of 50, and use an AdamW optimizer to train the model with a constant learning rate of 0.00002 for 3 epochs.", "section": "LSTM:", "sec_num": null}, {"text": "We choose a batch size of 50, and use an Adam optimizer to train the model for 200 epochs with learning rates of 0.01 for TUDataset and 0.001 for OGB.", "section": "GCN, GAT, GIN:", "sec_num": null}, {"text": "For a reproducible evaluation, we demonstrate the hyperparameters that we select for curriculum learning methods in Table 7 . It should be noted that this table includes the hyperparameters for the experiments with 200 epochs. For text domain tasks trained for 3 or 10 epochs, we sightly adjust some epoch-related hyperparameters to adapt the tasks, such as grow epochs, warm epochs, and schedule epochs.", "section": "<PERSON><PERSON> Hyperparameters", "sec_num": null}, {"text": "Tables from 8 to 11 report complexity and performance. ", "section": "<PERSON><PERSON> Detailed Complexity and Performance", "sec_num": null}], "back_matter": [{"text": "Acknowledgements This work is supported by the National Key Research and Development Program of China No.2023YFF1205001, National Natural Science Foundation of China (No. 62222209, 62250008, 62102222), Beijing National Research Center for Information Science and Technology under Grant No. BNR2023RC01003, BNR2023TD03006, and Beijing Key Lab of Networked Multimedia.", "section": "acknowledgement", "sec_num": null}], "ref_entries": {"FIGREF0": {"uris": null, "num": null, "text": "Figure 1. CurBench includes 15 datasets spanning 3 research domains, 9 backbone models, 3 training settings, and 2 evaluation dimensions, providing a comprehensive benchmark for existing curriculum learning methods.", "fig_num": "1", "type_str": "figure"}, "FIGREF1": {"uris": null, "num": null, "text": "Figure 2. Our CurBench toolkit, which is composed of 5 modules, offers a unified and complete pipeline from initiation to evaluation, aiming for easy implementation and reproduction of curriculum learning methods. This figure showcases an example of noisy CIFAR-10.", "fig_num": "2", "type_str": "figure"}, "FIGREF2": {"uris": null, "num": null, "text": "Figure 3. Python-like sample code for an example of Self-Paced Learning applied to image classification with CurBench Toolkit.", "fig_num": "3", "type_str": "figure"}, "FIGREF3": {"uris": null, "num": null, "text": "Figure 4. The performances as a function of noise ratio p for different curriculum learning methods on datasets from 3 research domains.", "fig_num": "4", "type_str": "figure"}, "FIGREF4": {"uris": null, "num": null, "text": "Figure 5. The performances as a function of imbalance factor r.", "fig_num": "5", "type_str": "figure"}, "FIGREF5": {"uris": null, "num": null, "text": "Figure 6. Time and space complexity of different methods in the case of ResNet-18 and CIFAR-10. Note: The numerical results of 3 different cases are reported inTable 8 in the Appendix.", "fig_num": "6", "type_str": "figure"}, "TABREF0": {"html": null, "content": "<table><tr><td colspan=\"2\">Domain Dataset</td><td>Setting</td><td>Training</td><td>Validation</td><td colspan=\"2\">Test Class Metrics</td></tr><tr><td/><td>CIFAR-10</td><td>Standard / Noise-0.4 Imbalance-50</td><td>45,000 12,536</td><td colspan=\"2\">5,000 10,000 5,000 10,000</td><td>10 Accuracy 10 Accuracy</td></tr><tr><td>CV</td><td>CIFAR-100</td><td>Standard / Noise-0.4 Imbalance-50</td><td>45,000 12,536</td><td colspan=\"2\">5,000 10,000 5,000 10,000</td><td>100 Accuracy 100 Accuracy</td></tr><tr><td/><td>Tiny-ImageNet</td><td>Standard / Noise-0.4 Imbalance-50</td><td>90,000 22,700</td><td colspan=\"2\">10,000 10,000 10,000 10,000</td><td>200 Accuracy 200 Accuracy</td></tr><tr><td/><td>RTE</td><td>Standard / Noise-0.4</td><td>2,490</td><td>277</td><td>-</td><td>2 Accuracy</td></tr><tr><td/><td>MRPC</td><td>Standard / Noise-0.4</td><td>3,668</td><td>408</td><td>-</td><td>2 F1 Score</td></tr><tr><td/><td>STS-B</td><td>Standard / Noise-0.4</td><td>5,749</td><td>1,500</td><td>-</td><td>6 Spearman</td></tr><tr><td>NLP</td><td>CoLA SST-2</td><td>Standard / Noise-0.4 Standard / Noise-0.4</td><td>8,551 67,349</td><td>1,043 872</td><td>--</td><td>2 Matthews 2 Accuracy</td></tr><tr><td/><td>QNLI</td><td>Standard / Noise-0.4</td><td>104,743</td><td>5,463</td><td>-</td><td>2 Accuracy</td></tr><tr><td/><td>QQP</td><td>Standard / Noise-0.4</td><td>363,846</td><td>40,430</td><td>-</td><td>2 F1 Score</td></tr><tr><td/><td colspan=\"2\">MNLI-(m/mm) Standard / Noise-0.4</td><td colspan=\"2\">392,702 9,815/9,832</td><td>-</td><td>3 Accuracy</td></tr><tr><td/><td>MUTAG</td><td>Standard / Noise-0.4</td><td>150</td><td>19</td><td>19</td><td>2 Accuracy</td></tr><tr><td/><td>PROTEINS</td><td>Standard / Noise-0.4</td><td>890</td><td>111</td><td>112</td><td>2 Accuracy</td></tr><tr><td>Graph</td><td>NCI1</td><td>Standard / Noise-0.4</td><td>3,288</td><td>411</td><td>411</td><td>2 Accuracy</td></tr><tr><td/><td>ogbg-molhiv</td><td>Standard / Noise-0.4</td><td>32,901</td><td>4,113</td><td>4,113</td><td>2 ROC-AUC</td></tr></table>", "text": "", "num": null, "type_str": "table"}, "TABREF1": {"html": null, "content": "<table><tr><td>NLP Domain: LSTM (Hochreiter &amp; Schm<PERSON>, 1997) is</td></tr><tr><td>a typical recurrent neural network (RNN), which introduces</td></tr><tr><td>gate functions to control what to remember and what to</td></tr><tr><td>forget in the face of long sequences. BERT (<PERSON> et al.,</td></tr><tr><td>2018) is a deep bidirectional Transformer pretrained by</td></tr><tr><td>masked language model task and it excels at semantic repre-</td></tr><tr><td>sentation due to its encoder-based architecture. GPT2 (Rad-</td></tr><tr><td>ford et al., 2019) is a decoder-based Transformer pretrained</td></tr><tr><td>through left-to-right language modeling objectives, and as a</td></tr><tr><td>result, works well on text generation. BERT and GPT2 in</td></tr><tr><td>CurBench are pretrained because training them from scratch</td></tr><tr><td>would result in poor performance, making it difficult to</td></tr><tr><td>maintain consistency with their suggested performance.</td></tr></table>", "text": "Table2overviews the backbone models that we employ in CurBench. All the values in the last column are approximations because the number of parameters varies depending on the input sizes and output classes. All of the models are commonly applied to the aforementioned datasets, and they are distinct from each other in mechanism and model size. first-order approximation of spectral graph convolutions. GAT(<PERSON><PERSON><PERSON> et al., 2017) introduces masked self-attentional layers based on GCN to enable implicitly specifying different weights to different nodes in a neighborhood. GIN", "num": null, "type_str": "table"}, "TABREF4": {"html": null, "content": "<table><tr><td/><td/><td>CIFAR-10</td><td/><td/><td colspan=\"2\">CIFAR-100</td><td>Tiny-ImageNet</td></tr><tr><td/><td colspan=\"7\">Standard Noise-0.4 Imbalance-50 Standard Noise-0.4 Imbalance-50 Standard Noise-0.4 Imbalance-50</td></tr><tr><td>LeNet</td><td colspan=\"2\">69.95 1.00 65.02 1.12</td><td colspan=\"2\">44.93 0.56</td><td colspan=\"2\">35.46 0.70 29.59 0.40</td><td>19.57 0.64</td><td>22.08 0.61 18.63 0.43</td><td>11.65 0.30</td></tr><tr><td>LeNet + CL</td><td colspan=\"2\">70.43 0.41 65.93 0.57</td><td colspan=\"2\">45.28 0.56</td><td colspan=\"2\">35.63 0.78 30.87 0.48</td><td>19.74 0.17</td><td>22.83 0.44 19.91 0.26</td><td>12.36 0.47</td></tr><tr><td>ResNet-18</td><td colspan=\"2\">92.33 0.16 82.75 2.06</td><td colspan=\"2\">75.49 0.87</td><td colspan=\"2\">69.97 0.27 52.14 0.39</td><td>42.57 0.68</td><td>51.41 1.74 39.42 0.21</td><td>28.83 0.38</td></tr><tr><td colspan=\"3\">ResNet-18 + CL 92.88 0.23 86.92 0.20</td><td colspan=\"2\">76.43 0.96</td><td colspan=\"2\">71.31 0.14 58.56 0.60</td><td>43.47 0.43</td><td>53.61 0.48 43.64 0.72</td><td>30.82 0.36</td></tr><tr><td>ViT</td><td colspan=\"2\">79.90 0.38 64.19 0.51</td><td colspan=\"2\">52.12 0.81</td><td colspan=\"2\">51.05 0.62 35.25 0.24</td><td>26.05 0.52</td><td>38.16 0.53 24.90 0.26</td><td>17.15 0.31</td></tr><tr><td>ViT + CL</td><td colspan=\"2\">80.66 0.27 69.83 0.53</td><td colspan=\"2\">52.85 0.81</td><td colspan=\"2\">51.93 0.64 39.15 0.30</td><td>26.40 0.34</td><td>38.92 0.53 29.76 0.34</td><td>17.47 0.14</td></tr><tr><td/><td/><td>RTE</td><td/><td/><td>MRPC</td><td/><td>STS-B</td><td>CoLA</td></tr><tr><td/><td/><td colspan=\"6\">Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4</td></tr><tr><td colspan=\"2\">LSTM</td><td colspan=\"2\">52.95 1.34 53.43 1.77</td><td colspan=\"2\">81.43 0.14 81.22 0.00</td><td colspan=\"2\">12.73 0.72 10.90 1.19</td><td>11.29 1.27</td><td>3.27 1.68</td></tr><tr><td colspan=\"4\">LSTM + CL 53.07 1.29 54.22 1.77</td><td colspan=\"2\">81.54 0.18 81.24 0.05</td><td colspan=\"2\">14.11 2.21 11.75 1.61</td><td>12.65 1.21</td><td>8.55 2.10</td></tr><tr><td colspan=\"2\">BERT</td><td colspan=\"2\">64.62 3.33 54.22 3.14</td><td colspan=\"2\">88.54 0.45 81.89 0.83</td><td colspan=\"2\">85.26 0.22 80.71 1.01</td><td>57.39 1.30 32.35 0.79</td></tr><tr><td colspan=\"4\">BERT + CL 66.35 1.76 56.32 5.04</td><td colspan=\"2\">88.69 1.24 81.94 0.55</td><td colspan=\"2\">85.42 0.22 81.31 0.25</td><td>57.80 1.96 45.79 1.64</td></tr><tr><td colspan=\"2\">GPT2</td><td colspan=\"2\">65.34 1.95 52.92 4.49</td><td colspan=\"2\">85.49 0.86 78.23 1.72</td><td colspan=\"2\">76.44 1.20 69.65 1.85</td><td>37.00 3.72</td><td>5.86 1.69</td></tr><tr><td colspan=\"2\">GPT2 + CL</td><td colspan=\"2\">66.35 2.10 57.40 3.39</td><td colspan=\"2\">86.29 0.36 82.55 0.88</td><td colspan=\"2\">80.82 1.39 71.57 1.74</td><td>39.95 3.16 12.54 2.75</td></tr><tr><td/><td/><td>SST-2</td><td/><td>QNLI</td><td/><td>QQP</td><td>MNLI-(m/mm)</td></tr><tr><td/><td colspan=\"7\">Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4</td><td>Standard</td><td>Noise-0.4</td></tr><tr><td>LSTM</td><td colspan=\"5\">81.67 0.85 64.36 1.12 75.69 0MUTAG 50.54 0.00 50.62 0.16 PROTEINS</td><td/><td>NCI1</td><td>ogbg-molhiv</td></tr><tr><td/><td/><td colspan=\"6\">Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4</td></tr><tr><td colspan=\"2\">GCN</td><td colspan=\"2\">73.68 2.11 66.31 7.14</td><td colspan=\"2\">70.71 4.20 63.57 6.45</td><td colspan=\"2\">69.59 1.23 55.23 3.21</td><td>75.84 1.02 64.29 4.55</td></tr><tr><td colspan=\"4\">GCN + CL 74.74 3.94 71.58 5.37</td><td colspan=\"2\">73.21 4.41 71.61 6.62</td><td colspan=\"2\">71.39 1.29 67.98 2.01</td><td>77.41 1.15 72.81 1.14</td></tr><tr><td colspan=\"2\">GAT</td><td colspan=\"2\">69.47 6.14 65.26 5.37</td><td colspan=\"2\">64.46 2.96 65.71 9.13</td><td colspan=\"2\">56.74 2.86 53.77 2.12</td><td>68.07 2.34 65.37 2.66</td></tr><tr><td colspan=\"6\">GAT + CL 72.63 8.42 69.47 10.21 69.82 7.13 69.11 3.77</td><td colspan=\"2\">59.37 1.59 55.67 4.70</td><td>72.64 1.16 66.73 1.84</td></tr><tr><td>GIN</td><td/><td colspan=\"2\">86.84 7.90 78.95 3.72</td><td colspan=\"2\">74.11 4.24 69.82 1.73</td><td colspan=\"2\">79.32 1.40 60.24 3.92</td><td>74.72 1.36 63.07 3.73</td></tr><tr><td colspan=\"2\">GIN + CL</td><td colspan=\"2\">88.42 2.10 81.58 4.56</td><td colspan=\"2\">77.14 4.88 73.93 1.82</td><td colspan=\"2\">82.04 1.90 62.14 6.47</td><td>76.53 1.97 65.53 1.61</td></tr></table>", "text": ".27 60.72 0.79 61.38 0.30 / 61.21 0.45 44.41 0.51 / 44.83 0.90 LSTM + CL 82.87 0.88 78.58 1.64 51.02 0.46 50.83 0.45 75.73 0.21 66.47 0.72 62.47 0.36 / 62.33 0.42 58.59 0.54 / 58.50 0.64 BERT 92.66 0.28 87.22 0.82 91.21 0.24 81.21 0.76 88.05 0.12 76.23 0.48 83.89 0.31 / 84.38 0.29 78.65 0.70 / 79.21 0.62 BERT + CL 92.82 0.16 91.25 0.59 91.49 0.13 89.45 0.44 88.16 0.13 84.50 0.25 84.27 0.07 / 84.40 0.42 81.73 0.31 / 82.25 0.40 GPT2 91.95 0.49 85.83 0.57 87.92 0.31 78.72 0.37 86.00 0.23 75.40 0.84 81.53 0.21 / 82.40 0.21 76.56 0.15 / 77.69 0.15 GPT2 + <PERSON>L 92.25 0.42 90.34 0.53 88.17 0.67 84.00 0.70 86.68 0.16 82.16 0.35 81.90 0.23 / 82.59 0.35 78.36 0.19 / 79.62 0.44", "num": null, "type_str": "table"}, "TABREF5": {"html": null, "content": "<table><tr><td/><td>Home Page</td><td>Download Link</td><td>License</td></tr><tr><td>CV</td><td colspan=\"2\">CIFAR Tiny-ImageNet CS231n PyTorch</td><td>MIT MIT</td></tr><tr><td>NLP</td><td>GLUE</td><td>Hugging Face</td><td>Various</td></tr><tr><td>Graph</td><td>TUDataset OGB</td><td colspan=\"2\">PyTorch Geometric Various OGB Dataset MIT</td></tr><tr><td colspan=\"4\">Concretely, in this work, we download CIFAR via PyTorch</td></tr><tr><td colspan=\"4\">API, GLUE via Hugging Face API, TUDataset via PyTorch</td></tr><tr><td colspan=\"4\">Geometric (PyG) API, and OGB dataset via OGB API. For</td></tr><tr><td colspan=\"4\">Tiny-ImageNet, we download the zip file from CS231n,</td></tr><tr><td colspan=\"4\">and adjust its file structure to the same form as CIFAR for</td></tr><tr><td colspan=\"4\">easier loading with the help of the tool code from Github:</td></tr><tr><td colspan=\"2\">lromor/tinyimagenet.py.</td><td/><td/></tr></table>", "text": "The home pages, download links, and licenses of datasets.", "num": null, "type_str": "table"}, "TABREF7": {"html": null, "content": "<table/>", "text": "The implementation references of backbone models.", "num": null, "type_str": "table"}, "TABREF8": {"html": null, "content": "<table><tr><td/><td colspan=\"2\">Conference Datasets</td><td>Models</td><td colspan=\"3\">Settings Std Noi Imb</td></tr><tr><td>SPL (<PERSON> et al., 2010)</td><td>NIPS, 2010</td><td>MUC6, UniProbe, MNIST, Mammals</td><td>SSVM</td><td>✓</td><td/><td/></tr><tr><td>TTCL (Weins<PERSON> et al., 2018)</td><td colspan=\"2\">ICML, 2018 CIFAR-100, STL-10</td><td>CNN</td><td>✓</td><td/><td/></tr><tr><td>MCL (Zhou &amp; Bilmes, 2018)</td><td>ICLR, 2018</td><td>News-20, MNIST, SVHN, Fashion CIFAR-10, STL-10,</td><td>Le<PERSON><PERSON>, CNN</td><td>✓</td><td/><td/></tr><tr><td>ScreenerNet (Kim &amp; Choi, 2018)</td><td>ArXiv, 2018</td><td>Cart-pole-v0, Pascal VOC CIFAR-10, MNIST,</td><td>DDQN, CNN</td><td>✓</td><td/><td/></tr><tr><td>LRE (Ren et al., 2018a)</td><td>ICML, 2018</td><td>MNIST, CIFAR-10, CIFAR-100</td><td>LeNet, ResNet-32, WideResNet-28-10</td><td/><td>✓</td><td>✓</td></tr><tr><td>MW-Net (Shu et al., 2019)</td><td>NIPS, 2019</td><td>CIFAR-10, CIFAR-100, Clothing1M</td><td>ResNet-32, ResNet-50, WideResNet-28-10</td><td>✓</td><td>✓</td><td>✓</td></tr><tr><td>DCL (Saxena et al., 2019)</td><td>NIPS, 2019</td><td>CIFAR-10, CIFAR-100, KITTI ImageNet, WebVision,</td><td>VGG-16, SSDNet, WideResNet-28-10 ResNet-18,</td><td>✓</td><td>✓</td><td/></tr><tr><td>LGL (Cheng et al., 2019)</td><td>CVPR, 2019</td><td>CIFAR-10, CIFAR-100, ImageNet</td><td>VGG-16, ResNet-50</td><td>✓</td><td/><td/></tr><tr><td>DDS (Wang et al., 2020)</td><td>ICML, 2020</td><td>CIFAR-10, ImageNet, TED</td><td>LSTM, ResNet-50, WideResNet-28-10</td><td>✓</td><td/><td>✓</td></tr><tr><td/><td/><td>CIFAR-10, CIFAR-100,</td><td/><td/><td/><td/></tr><tr><td/><td/><td>ImageNet, Food-101,</td><td>ResNet-50,</td><td/><td/><td/></tr><tr><td>DIHCL (Zhou et al., 2020)</td><td>NIPS, 2020</td><td>FGVC Aircraft, Birdsnap, FMNIST, Stanford Cars,</td><td>WideResNet-16-8, ResNeXt50-32x4d, WideResNet-28-10,</td><td>✓</td><td/><td/></tr><tr><td/><td/><td>KMNIST, STL10,</td><td>PreActResNet34</td><td/><td/><td/></tr><tr><td/><td/><td>SVHN</td><td/><td/><td/><td/></tr><tr><td>SuperLoss (Castells et al., 2020)</td><td>NIPS, 2020</td><td>MNIST, UTKFace, CIFAR-10, CIFAR-100, Revisited Oxford and Paris WebVision, Pascal VOC,</td><td>ResNet-18, ResNet-50, ResNet-101, RetinaNet Faster R-CNN, WideResNet-28-10,</td><td>✓</td><td>✓</td><td/></tr><tr><td/><td/><td>CIFAR-10, CIFAR-100,</td><td>VGG-16, ResNet-18,</td><td/><td/><td/></tr><tr><td>CBS (Sinha et al., 2020)</td><td>NIPS, 2020</td><td>ImageNet, SVHN, CelebA, Pascal VOC,</td><td>Wide-ResNet-50, ResNeXt-50,</td><td>✓</td><td/><td/></tr><tr><td/><td/><td>MNIST, USPS</td><td>VAE, β-VAE</td><td/><td/><td/></tr><tr><td>C2F (Stretcu et al., 2021)</td><td>ArXiv, 2021</td><td>CIFAR-10, CIFAR-100, Shapes, Tiny-ImageNet</td><td>Resnet-18, Resnet-50, WideResnet-28-10</td><td>✓</td><td/><td/></tr><tr><td>Adaptive CL (Kong et al., 2021)</td><td>ICCV, 2021</td><td>CIFAR-10, CIFAR-100, Subset of ImageNet</td><td>MLP, HNN, ResNet-v1-14 VGG-16, ResNet-18</td><td>✓</td><td/><td/></tr><tr><td>EfficientTrain (Wang et al., 2023b)</td><td>ICCV, 2023</td><td>ImageNet-1K/22K, CIFAR, Stanford Dogs MS COCO, Flowers-102,</td><td>ResNet, ConvNeXt, Swin, CSWin DeiT, PVT,</td><td>✓</td><td/><td/></tr></table>", "text": "Summary of the methods reproduced in CurBench, where we overview the datasets and models involved in the related works.", "num": null, "type_str": "table"}, "TABREF9": {"html": null, "content": "<table/>", "text": "The default hyperparameters we set for each method when the number of training epochs is 200.", "num": null, "type_str": "table"}, "TABREF11": {"html": null, "content": "<table><tr><td>MUTAG</td><td>PROTEINS</td><td>NCI1</td><td>ogbg-molhiv</td></tr><tr><td colspan=\"2\">Standard Noise-0.4 (a) GCN</td><td/><td/></tr><tr><td>MUTAG</td><td>PROTEINS</td><td>NCI1</td><td>ogbg-molhiv</td></tr><tr><td colspan=\"2\">Standard Noise-0.4 (b) GAT</td><td/><td/></tr><tr><td>MUTAG</td><td>PROTEINS</td><td>NCI1</td><td>ogbg-molhiv</td></tr><tr><td colspan=\"2\">Standard Noise-0.4 (c) GIN</td><td/><td/></tr></table>", "text": "Time and space complexity, measured by training time and GPU memory usage on NVIDIA V100 GPU. Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 SPL 71.58 7.14 62.10 3.94 69.46 5.91 65.54 6.48 68.42 1.90 60.05 2.38 77.41 1.15 60.87 3.09 TTCL 70.52 7.14 71.58 5.37 72.68 7.63 71.61 6.62 70.90 2.21 67.98 2.01 75.89 0.81 72.81 1.14 MCL 71.58 7.14 71.58 8.55 70.54 5.15 65.00 4.71 68.56 1.04 54.50 2.85 74.10 1.40 64.26 4.17 ScreenerNet 72.63 3.94 64.21 5.16 71.96 5.61 67.14 4.05 69.78 2.22 56.06 5.14 73.71 0.45 61.00 7.79 LRE 70.52 9.18 61.40 4.96 68.03 6.17 66.61 5.25 58.23 1.60 51.22 2.38 73.74 1.48 57.92 7.98 MW-Net 74.73 2.11 63.16 4.71 70.54 4.55 66.79 4.13 68.71 1.78 56.01 1.37 75.57 1.03 62.81 6.19 <PERSON><PERSON> 74.73 2.11 61.05 13.56 71.96 3.46 63.57 6.32 70.51 0.66 56.69 1.58 75.78 1.39 61.26 3.57 DDS 74.74 3.94 64.21 5.16 73.21 4.41 64.11 6.50 71.39 1.29 58.10 3.28 70.48 3.02 57.09 4.80 DIHCL 71.58 5.37 68.42 7.44 73.03 3.59 63.22 7.02 67.40 1.71 57.86 2.04 70.47 2.10 61.20 4.67 SuperLoss 71.58 5.37 69.47 6.14 72.32 3.44 65.89 3.84 70.22 2.00 57.17 3.38 75.97 1.03 61.21 5.12 Adaptive CL 73.68 3.33 66.31 7.88 72.68 4.84 65.71 4.51 69.88 2.17 58.44 5.29 75.49 1.13 60.95 7.96 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 SPL 64.21 11.72 65.26 5.37 69.29 6.93 67.14 3.85 56.49 2.61 54.74 4.10 69.69 2.38 64.88 2.73 TTCL 69.47 6.98 65.26 10.84 69.82 7.13 64.46 1.31 56.79 1.40 55.47 2.73 68.27 2.04 66.73 1.84 MCL 64.21 11.24 68.42 8.81 69.64 6.29 66.96 6.89 57.56 2.63 55.23 4.73 69.25 3.06 63.20 3.03 ScreenerNet 64.21 8.42 65.26 7.88 65.71 5.25 69.11 3.77 54.55 2.64 55.28 1.53 71.13 2.07 65.94 2.61 LRE 66.31 11.34 63.16 4.71 66.43 1.84 66.07 3.19 54.11 2.32 52.94 2.36 66.59 2.45 63.74 2.61 MW-Net 61.84 9.40 65.26 7.14 66.78 3.01 67.14 4.42 57.56 2.29 55.33 1.18 68.54 3.76 62.39 2.60 DCL 67.37 14.28 69.47 10.21 68.03 7.39 64.28 3.84 59.37 1.59 55.33 1.72 72.64 1.16 62.22 3.98 DDS 66.31 7.14 67.37 6.14 67.14 3.31 66.78 9.69 53.24 1.81 54.45 3.35 68.50 2.05 62.22 5.88 DIHCL 72.63 8.42 66.32 8.55 65.00 6.81 68.57 6.93 57.18 1.73 55.67 4.70 69.07 2.79 66.38 2.78 SuperLoss 67.37 13.06 68.42 7.44 63.93 2.63 66.07 7.23 57.08 2.27 55.13 2.39 70.58 1.52 60.92 2.13 Adaptive CL 67.37 10.21 66.32 7.14 68.39 3.07 64.47 6.37 57.61 2.22 55.08 2.02 69.71 1.84 62.98 2.53 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 SPL 82.10 5.37 72.37 6.84 72.86 5.13 72.86 2.37 77.54 1.69 56.87 4.93 76.53 1.97 63.35 2.34 TTCL 84.21 7.44 81.58 4.56 75.71 2.36 73.93 1.82 80.24 1.67 56.27 4.27 75.13 1.55 62.16 3.07 MCL 84.21 6.45 73.69 5.27 75.72 3.93 70.00 2.68 75.67 1.00 57.73 4.11 74.20 0.48 63.82 3.85 ScreenerNet 82.10 7.14 75.00 5.74 75.71 1.82 68.39 4.88 79.61 1.09 55.57 5.11 74.39 1.24 61.07 2.33 LRE 78.95 3.72 80.27 2.28 72.68 5.46 66.43 6.48 71.41 1.71 54.08 1.72 73.49 2.36 63.30 4.44 MW-Net 88.42 2.10 75.00 4.37 73.75 4.10 66.61 8.54 79.22 1.21 55.52 4.78 75.22 0.80 65.43 2.70 DCL 85.26 8.42 76.32 4.56 74.11 3.14 64.46 4.39 79.66 1.39 56.06 3.79 75.23 2.22 61.65 3.38 DDS 85.26 3.94 80.26 5.73 70.31 2.78 65.89 6.69 77.62 3.58 54.89 4.85 72.85 2.67 63.38 3.91 DIHCL 85.53 4.36 73.68 3.72 73.75 4.54 71.61 4.28 76.55 1.70 53.33 1.28 72.43 1.80 62.23 5.86 SuperLoss 88.42 5.16 77.63 4.37 77.14 4.88 71.25 5.69 82.04 1.90 62.14 6.47 74.51 1.47 65.53 1.61 Adaptive CL 86.31 4.21 80.26 9.40 75.89 3.79 70.36 3.97 79.32 1.90 62.05 1.67 76.17 1.46 61.81 4.81", "num": null, "type_str": "table"}, "TABREF12": {"html": null, "content": "<table/>", "text": "The performances of each curriculum learning method in the graph research domain.", "num": null, "type_str": "table"}}}}