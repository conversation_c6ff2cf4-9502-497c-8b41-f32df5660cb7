{"paper_id": "cospgd", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:41:36.201807Z"}, "title": "CosPGD: an efficient white-box adversarial attack for pixel-wise prediction tasks", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Steffen", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "While neural networks allow highly accurate predictions in many tasks, their lack of robustness towards even slight input perturbations often hampers their deployment. Adversarial attacks such as the seminal projected gradient descent (PGD) offer an effective means to evaluate a model's robustness and dedicated solutions have been proposed for attacks on semantic segmentation or optical flow estimation. While they attempt to increase the attack's efficiency, a further objective is to balance its effect, so that it acts on the entire image domain instead of isolated pointwise predictions. This often comes at the cost of optimization stability and thus efficiency. Here, we propose CosPGD, an attack that encourages more balanced errors over the entire image domain while increasing the attack's overall efficiency. To this end, CosPGD leverages a simple alignment score computed from any pixelwise prediction and its target to scale the loss in a smooth and fully differentiable way. It leads to efficient evaluations of a model's robustness for semantic segmentation as well as regression models (such as optical flow, disparity estimation, or image restoration), and it allows it to outperform the previous SotA attack on semantic segmentation. We provide code for the CosPGD algorithm and example usage at https://github. com/shashankskagnihotri/cospgd.", "pdf_parse": {"paper_id": "cospgd", "_pdf_hash": "", "abstract": [{"text": "While neural networks allow highly accurate predictions in many tasks, their lack of robustness towards even slight input perturbations often hampers their deployment. Adversarial attacks such as the seminal projected gradient descent (PGD) offer an effective means to evaluate a model's robustness and dedicated solutions have been proposed for attacks on semantic segmentation or optical flow estimation. While they attempt to increase the attack's efficiency, a further objective is to balance its effect, so that it acts on the entire image domain instead of isolated pointwise predictions. This often comes at the cost of optimization stability and thus efficiency. Here, we propose CosPGD, an attack that encourages more balanced errors over the entire image domain while increasing the attack's overall efficiency. To this end, CosPGD leverages a simple alignment score computed from any pixelwise prediction and its target to scale the loss in a smooth and fully differentiable way. It leads to efficient evaluations of a model's robustness for semantic segmentation as well as regression models (such as optical flow, disparity estimation, or image restoration), and it allows it to outperform the previous SotA attack on semantic segmentation. We provide code for the CosPGD algorithm and example usage at https://github. com/shashankskagnihotri/cospgd.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Deep Neural Networks (DNNs) have been gaining popularity for estimating solutions to various complex tasks includ- † Authors contributed theoretically and practically to this paper. Please refer to the Author Contribution section for details. 1 Data and Web Science Group, University of Mannheim, Germany 2 Max-Planck-Institute for Informatics, Saarland Informatics Campus, Germany. Correspondence to: Shashank Agnihotri <<EMAIL>>. ing numerous vision tasks like classification (<PERSON><PERSON><PERSON><PERSON> et al., 2012; <PERSON> et al., 2015; <PERSON><PERSON> et al., 2016; <PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2023a) , generative models (<PERSON> & <PERSON>, 2020; 2021; <PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2023b) , image segmentation (<PERSON><PERSON> et al., 2015; <PERSON> et al., 2017; <PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2023) , or disparity (<PERSON> et al., 2021) and optical flow (<PERSON> et al., 2015; <PERSON><PERSON> et al., 2016; <PERSON><PERSON> & <PERSON>g, 2020; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) estimation, due to their overall precise predictions. However, DNNs are inherently black-box function approximators (<PERSON><PERSON><PERSON><PERSON> et al., 2019) , known to find shortcuts to map the input to a target (<PERSON><PERSON><PERSON><PERSON> et al., 2020) , to learn biases (<PERSON><PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON><PERSON> et al., 2024) and to lack robustness (Szegedy et al., 2014; <PERSON> et al., 2021) .", "cite_spans": [{"start": 505, "end": 530, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2012;", "ref_id": "BIBREF46"}, {"start": 531, "end": 547, "text": "<PERSON> et al., 2015;", "ref_id": "BIBREF30"}, {"start": 548, "end": 565, "text": "<PERSON><PERSON> et al., 2016;", "ref_id": "BIBREF87"}, {"start": 566, "end": 583, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF49"}, {"start": 584, "end": 606, "text": "<PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}, {"start": 627, "end": 648, "text": "(<PERSON> & <PERSON>, 2020;", "ref_id": "BIBREF39"}, {"start": 649, "end": 654, "text": "2021;", "ref_id": null}, {"start": 655, "end": 676, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF50"}, {"start": 677, "end": 696, "text": "<PERSON> et al., 2023b)", "ref_id": null}, {"start": 718, "end": 744, "text": "(<PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF63"}, {"start": 745, "end": 763, "text": "<PERSON> et al., 2017;", "ref_id": "BIBREF92"}, {"start": 764, "end": 782, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF41"}, {"start": 783, "end": 807, "text": "<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF72"}, {"start": 823, "end": 840, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF48"}, {"start": 858, "end": 880, "text": "(<PERSON> et al., 2015;", "ref_id": "BIBREF19"}, {"start": 881, "end": 898, "text": "<PERSON><PERSON> et al., 2016;", "ref_id": "BIBREF34"}, {"start": 899, "end": 917, "text": "Te<PERSON> & Deng, 2020;", "ref_id": "BIBREF76"}, {"start": 918, "end": 942, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": null}, {"start": 1059, "end": 1084, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF6"}, {"start": 1140, "end": 1162, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF23"}, {"start": 1181, "end": 1203, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF22"}, {"start": 1204, "end": 1226, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF21"}, {"start": 1250, "end": 1272, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF75"}, {"start": 1273, "end": 1295, "text": "<PERSON> et al., 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "An adversarial attack adds a crafted, small (epsilon-sized) perturbation to the input of a neural network that aims to alter the prediction, thus assessing a network's robustness as in the benchmarks by <PERSON><PERSON><PERSON> et al. (2021) ; <PERSON> et al. (2023a) . Due to the practical relevance to evaluating and analyzing DNN models, such attacks have been extensively studied (<PERSON><PERSON><PERSON> et al., 2014; <PERSON><PERSON><PERSON> et al., 2017; <PERSON> et al., 2020b; <PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON><PERSON> et al., 2015; <PERSON><PERSON><PERSON> et al., 2016; <PERSON><PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2023b; <PERSON><PERSON><PERSON><PERSON> et al., 2022; 2023; <PERSON><PERSON><PERSON> et al., 2023b) .", "cite_spans": [{"start": 203, "end": 222, "text": "<PERSON><PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF14"}, {"start": 225, "end": 244, "text": "<PERSON> et al. (2023a)", "ref_id": null}, {"start": 361, "end": 386, "text": "(<PERSON><PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF24"}, {"start": 387, "end": 408, "text": "<PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF47"}, {"start": 409, "end": 428, "text": "<PERSON> et al., 2020b;", "ref_id": null}, {"start": 429, "end": 448, "text": "<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF53"}, {"start": 449, "end": 479, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF57"}, {"start": 480, "end": 501, "text": "<PERSON><PERSON><PERSON> et al., 2016;", "ref_id": null}, {"start": 502, "end": 523, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF71"}, {"start": 524, "end": 548, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023b;", "ref_id": null}, {"start": 549, "end": 572, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF25"}, {"start": 573, "end": 578, "text": "2023;", "ref_id": null}, {"start": 579, "end": 601, "text": "<PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Proceedings of the", "sec_num": null}, {"text": "Existing approaches predominantly focus on attacking image classification models. However, arguably, the robustness of models for pixel-wise prediction tasks is highly relevant for many safety-critical applications such as motion estimation in autonomous driving or semantic segmentation. The application of existing attacks to pixel-wise prediction tasks such as semantic segmentation or optical flow estimation is possible in principle (e.g. as in <PERSON><PERSON><PERSON> et al. (2017) ), albeit carrying only limited information since the pixel-specific loss information is not fully leveraged. In Figure 1 , we illustrate this effect for a targeted attack on optical flow estimation and show that classical classification attacks such as PGD (see Figure 1(e) ) only fool the network predictions to some extent: PGD tends to only fit the target (all zeros, i.e. white) in parts of the optical flow, while a few predictions remain intact.", "cite_spans": [{"start": 450, "end": 469, "text": "<PERSON><PERSON><PERSON> et al. (2017)", "ref_id": "BIBREF4"}], "ref_spans": [{"start": 590, "end": 591, "text": "1", "ref_id": "FIGREF1"}, {"start": 740, "end": 744, "text": "1(e)", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Proceedings of the", "sec_num": null}, {"text": "For semantic segmentation, <PERSON><PERSON> et al. (2022) showed that harnessing pixel-wise information for adversarial attacks leads to much stronger attacks. They argue that, during the attack, the loss to be backpropagated needs to be altered such that already flipped pixel predictions are less important for the gradient computation. Thus, SegPGD (<PERSON><PERSON> et al., 2022) makes a binary decision for each pixel based on the classification result at this location, to weigh the attack loss for incorrect and correct model predictions individually. While this is intuitive for semantic segmentation, it can not extend to pixel-wise regression tasks by definition. Furthermore, due to the discrete nature of the loss scaling, SegPGD faces stability issues and has to fade back in the loss of already incorrectly predicted pixels over time (<PERSON><PERSON> et al., 2022) .", "cite_spans": [{"start": 27, "end": 43, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF27"}, {"start": 338, "end": 355, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}, {"start": 820, "end": 837, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Proceedings of the", "sec_num": null}, {"text": "In this work, we propose CosPGD, an efficient white-box adversarial attack that considers the cosine-alignment between the prediction and target for each pixel, leading to a smooth and fully differentiable attack objective. Due to its principled formulation, CosPGD can be used for a wide range of pixel-wise prediction tasks beyond semantic segmentation. Figure 1 (f) shows its effect on optical flow estimation, where, in contrast to PGD, it can fit the target at almost all locations. Since it leverages the (continuous) posterior distribution of the prediction to allow for a smooth and differentiable loss computation, it can significantly outperform SegPGD on semantic segmentation. The main contributions of this work are as follows:", "cite_spans": [], "ref_spans": [{"start": 363, "end": 364, "text": "1", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Proceedings of the", "sec_num": null}, {"text": "• We propose CosPGD, an efficient white-box adversarial attack, that can be applied to any pixel-wise prediction task, and thus allows for an efficient evaluation of their robustness in a unified setting.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Proceedings of the", "sec_num": null}, {"text": "• We provide theoretical and empirical proofs for the stability and spatial balancing of CosPGD during attack optimization.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Proceedings of the", "sec_num": null}, {"text": "• For semantic segmentation, we compare CosPGD to the recently proposed SegPGD which also uses pixelwise information for generating attacks. CosPGD outperforms SegPGD by a significant margin.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Proceedings of the", "sec_num": null}, {"text": "• To demonstrate CosPGD's versatility, we also evaluate it as a targeted attack and as a non-targeted attack, for both ℓ 2 and ℓ ∞ bounds on semantic segmentation, optical flow estimation and image restoration in several settings and datasets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Proceedings of the", "sec_num": null}, {"text": "The vulnerability of DNNs to adversarial attacks was first explored in (<PERSON><PERSON><PERSON> et al., 2014) for image classification, proposing the Fast Gradient Sign Method (FGSM).", "cite_spans": [{"start": 71, "end": 96, "text": "(<PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF24"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2."}, {"text": "FGSM is a single-step (one iteration) white-box adversarial attack that perturbs the input in the direction of its gradient, generated from backpropagating the loss, with a small step size, such that the model prediction becomes incorrect. Due to its fast computation, it is still a widely used approach. Numerous subsequent works have been directed towards generating effective adversarial attacks for diverse tasks including NLP (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2018) , or 3D tasks (<PERSON> et al., 2021; <PERSON> et al., 2021 ). Yet, the high input dimensionality of image classification models results in the striking effectiveness of adversarial attacks in this field (<PERSON><PERSON><PERSON> et al., 2014; <PERSON><PERSON> et al., 2022) . A vast line of work has been dedicated to assessing the quality and robustness of representations learned by the network, including the curation of dedicated evaluation data for particular tasks (<PERSON> et al., 2019; Hendrycks & Dietterich, 2019; <PERSON><PERSON><PERSON><PERSON> et al., 2019) or the crafting of effective adversarial attacks. These adversarial attacks can be image-wide or localized in a small region or patch. These perturbations are in a small region of the image and are called Patch Attacks (e.g. (<PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2024) ),while methods such as proposed in (<PERSON><PERSON><PERSON> et al., 2014; <PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON> et al., 2017; <PERSON> et al., 2020b; <PERSON><PERSON><PERSON><PERSON> et al., 2015; <PERSON><PERSON><PERSON> & Hein, 2020; And<PERSON>hchenko et al., 2020; <PERSON>ini & <PERSON>, 2017; <PERSON>y et al., 2019; <PERSON> et al., 2018) argue in a Lipschitz continuity motivated way that a robust network's prediction should not change drastically if the perturbed image is within the epsilon-ball of the original image and thus optimize attacks globally within the epsilon neighborhood of the original input. Our proposed CosPGD follows this line of work.", "cite_spans": [{"start": 431, "end": 452, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF58"}, {"start": 453, "end": 474, "text": "<PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF62"}, {"start": 475, "end": 494, "text": "<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF36"}, {"start": 509, "end": 529, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF90"}, {"start": 530, "end": 546, "text": "<PERSON> et al., 2021", "ref_id": "BIBREF74"}, {"start": 691, "end": 716, "text": "(<PERSON><PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF24"}, {"start": 717, "end": 734, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF37"}, {"start": 932, "end": 951, "text": "(<PERSON> et al., 2019;", "ref_id": "BIBREF44"}, {"start": 952, "end": 981, "text": "Hendrycks & Dietterich, 2019;", "ref_id": "BIBREF31"}, {"start": 982, "end": 1005, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2019)", "ref_id": null}, {"start": 1231, "end": 1251, "text": "(<PERSON> et al., 2017;", "ref_id": null}, {"start": 1252, "end": 1274, "text": "<PERSON><PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF67"}, {"start": 1311, "end": 1336, "text": "(<PERSON><PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF24"}, {"start": 1337, "end": 1358, "text": "<PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF47"}, {"start": 1359, "end": 1378, "text": "<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF53"}, {"start": 1379, "end": 1398, "text": "<PERSON> et al., 2020b;", "ref_id": null}, {"start": 1399, "end": 1429, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF57"}, {"start": 1430, "end": 1449, "text": "<PERSON><PERSON><PERSON> & Hein, 2020;", "ref_id": null}, {"start": 1450, "end": 1478, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF3"}, {"start": 1479, "end": 1502, "text": "Carlini & Wagner, 2017;", "ref_id": "BIBREF8"}, {"start": 1503, "end": 1521, "text": "<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF64"}, {"start": 1522, "end": 1540, "text": "<PERSON> et al., 2018)", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2."}, {"text": "White-box attacks assume full access to the model and its gradients (<PERSON><PERSON><PERSON> et al., 2014; <PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON> et al., 2017; <PERSON> et al., 2020b; <PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2015; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2018; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022a) while black-box attacks optimize perturbations in a randomized way (<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2023) . The proposed CosPGD derives its optimization from PGD (<PERSON><PERSON><PERSON> et al., 2017) and is a white-box attack.", "cite_spans": [{"start": 68, "end": 93, "text": "(<PERSON><PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF24"}, {"start": 94, "end": 115, "text": "<PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF47"}, {"start": 116, "end": 135, "text": "<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF53"}, {"start": 136, "end": 155, "text": "<PERSON> et al., 2020b;", "ref_id": null}, {"start": 156, "end": 172, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF27"}, {"start": 173, "end": 203, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF57"}, {"start": 204, "end": 222, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF65"}, {"start": 223, "end": 241, "text": "<PERSON> et al., 2018;", "ref_id": "BIBREF16"}, {"start": 242, "end": 267, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022a)", "ref_id": null}, {"start": 335, "end": 364, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF3"}, {"start": 365, "end": 384, "text": "<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF35"}, {"start": 385, "end": 401, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF60"}, {"start": 458, "end": 480, "text": "(<PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF47"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2."}, {"text": "Further, one distinguishes between targeted attacks (e.g. (<PERSON> et al., 2020a; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022b) ) that turn the network predictions towards a specific target and untargeted (or non-targeted) attacks that optimize the attack to cause any incorrect prediction. PGD (<PERSON><PERSON><PERSON> et al., 2017) , and CosPGD by extension, allows for both settings (<PERSON><PERSON> et al., 2022) .", "cite_spans": [{"start": 58, "end": 78, "text": "(<PERSON> et al., 2020a;", "ref_id": null}, {"start": 79, "end": 99, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF20"}, {"start": 100, "end": 125, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022b)", "ref_id": null}, {"start": 293, "end": 315, "text": "(<PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF47"}, {"start": 368, "end": 385, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF77"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2."}, {"text": "While previous attacks predominantly focus on classification tasks, only a few approaches specifically address the analysis of pixel-wise prediction tasks such as semantic segmentation, optical flow, or disparity estimation. For example, PCFA (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022b) was applied to the estimation of optical flow and specifically minimizes the average end-point error (AEE) to a target flow field. A notable exception of pixel-wise white-box adversarial attack is proposed in (<PERSON><PERSON> et al., 2022) . The SegPGD attack could showcase the importance of pixel-wise attacks for semantic segmentation. In this work, we propose CosPGD to provide a principled and efficient adversarial attack, that can be applied to a wide range of pixel-wise prediction tasks and provides stable optimization. CosPGD outperforms SegPGD by a significant margin when attacking semantic segmentation models while preserving its efficiency and extending it to other pixel-wise prediction tasks.", "cite_spans": [{"start": 243, "end": 269, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022b)", "ref_id": null}, {"start": 479, "end": 496, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2."}, {"text": "The projected gradient descent (PGD) (<PERSON><PERSON><PERSON> et al., 2017) attack is an iterative white box adversarial attack. It is known to be a strong attack and builds the basis for followup methods such as (<PERSON> et al., 2020b) . Such methods leverage the gradients of a model's loss to create strong adversarial attacks, e.g. the PGD update is given as", "cite_spans": [{"start": 37, "end": 59, "text": "(<PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF47"}, {"start": 197, "end": 217, "text": "(<PERSON> et al., 2020b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "X advt+1 = X advt +α•sign∇ X adv t L(f θ (X advt ), Y ) (1) δ = ϕ ϵ (X advt+1 -X clean ),", "eq_num": "(2)"}], "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "X advt+1 = ϕ r (X clean + δ)", "eq_num": "(3)"}], "section": "Preliminaries", "sec_num": "3."}, {"text": "Here, L(•) is a function (differentiable at least once) of the model prediction and the target, which defines the loss the model f θ aims to minimize, X advt+1 is a new adversarial example for time step t + 1, generated using X advt , the adversarial example at time step t and initial clean sample X clean . Y is the ground truth label for non-targeted attacks and the target for targeted attacks, α is the step size for the perturbation (α is multiplied by -1 for targeted attacks to take a step in the direction of the target), and the function ϕ ϵ is clipping the δ in ϵ-ball for ℓ ∞ -norm bounded attacks or the ϵ-projection in l 2 -norm bounded attacks, complying with the ℓ ∞ -norm or l 2 -norm constraints, respectively. ϕ r is clipping the generated example in the valid input range (usually between [0, 1]). ∇ X adv t L(•) denotes the gradient of X advt generated by backpropagating the loss and is used to determine the direction of the perturbation step.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "Originally, PGD has been conceived to attack image classification models. For pixel-wise prediction tasks, its update in Equation 1 considers the sum of pixel-wise losses L, i.e.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "X advt+1 =X advt + (4) α • sign∇ X adv t i∈H×W L f θ (X advt ) i , Y i", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "where i iterates over all positions in the prediction f θ (X) with f θ (X), Y ∈ R H×W ×M for images of size H × W and M output dimensions (e.g. M classes for semantic segmentation). The update in PGD thus aims to increase the overall loss maximally summing over all locations. It does not take into account that the prediction in some locations might remain correct while it further increases the loss in other locations (that might already be predicted incorrectly).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "3."}, {"text": "We argue that the above formulation neglects an interesting aspect: It does not facilitate inducing equally manipulated predictions in all locations. This can be disadvantageous for targeted attacks, where one wants to ensure that the target is fit at all locations equally. In particular, it is however problematic for, for example, attacks on semantic segmentation where models use cross-entropy-like losses that do not saturate. Thus, after flipping a few point-wise label predictions, PGD-based attacks might continue to increase the overall loss even without altering any further labels. Thus, we argue that the alignment between the current prediction and the target or ground truth has to be taken into account to efficiently compute strong adversaries.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "In the following, we introduce CosPGD. Its goal is to employ a continuous pixel-wise measure of prediction alignment inside the computation of the attack update step so that the gradient-based CosPGD iterations smoothly converge to a strong adversary that acts on all pixel locations. The update step in CosPGD is defined as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "X advt+1 = X advt + α • sign∇ X adv t (5) i∈H×W cos ψ(f θ (X advt ) i ), Y i • L f θ (X advt ) i , Y i ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "where ψ is a continuously differentiable, monotonous function that can be used to normalize the model output, i.e. we assume ψ(f θ (X)) = 1 ∀f θ (X), and", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "cos(P , Y ) = P • Y ∥P ∥ • ∥Y ∥ (6)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "is the cosine similarity between two vectors, in this case a (normalized) network prediction P and the target or ground truth Y ∈ R M . For the example of semantic segmentation, Y is usually one-hot encoded and therefore normalized.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "Cosine similarity provides a measure of similarity between the direction of two vectors and should therefore be wellsuited to represent the alignment of the prediction with the target at the posterior level. It scales in a fixed range [-1, 1], such that no further normalization of the scaling is needed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "As the loss in CosPGD is scaled with a pixel-wise measure of alignment between the current prediction and the target in Equation 5, the resulting gradient update emphasizes on changing those pixel-wise predictions that are correct in the current prediction.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "This yields several desirable properties. First, it facilitates to optimize adversaries to pixel-wise tasks so that the prediction in all pixels is affected. As such, it is a stronger attack than PGD on tasks such as semantic segmentation. Further, it can be applied to pixel-wise classification and regression tasks in a principled way. Second, the loss is scaled with a smooth scaling function, i.e. if the prediction changes only a little, the change in the proposed alignment score will also be small, specifically Proposition 4.1. For any two pixel-wise network predictions f θ (X) i and f θ (", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "X) i ∈ R M , a target Y i ∈ R M and a continuously differentiable function ψ : R M → R M with ψ(f θ (X)) = 1 ∀f θ (X), it is d • ∥f θ (X) i -f θ ( X) i ∥ ≥ ∥cos (ψ(f θ (X) i ), Y i ) -cos ψ(f θ ( X) i ), Y i ∥ for a real, constant d ≥ 0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "The proof is given in the appendix. As a result of the above proposition, the gradient in Equation 5 will change smoothly over the attack iterations for a sufficiently small step-size α and allow for fast convergence properties, i.e. CosPGD should provide strong adversaries with relatively few iterations while providing a balance over the pixel locations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "Untargeted versus Targeted Attacks. Untargeted attacks intend to drive the model's predictions away from the model's intended target (ground truth). Specifically, for non-targeted attacks, CosPGD, therefore, scales the loss pixel-wise in proportion to the pixel-wise predictions' similarity to the ground truth, while also accounting for the decrease in similarity over iterations. Using cosine similarity as an alignment measure, pixels at which the network predictions are closer to the intended target (ground truth), have a higher similarity (approaching 1) and thus higher loss. Pixels with lower similarity, have a lower loss but are not rendered benign. In contrast, for the targeted setting, the attack aims to drive predictions towards the target at all locations, such that pixels at which the network predictions are closer to the target and have higher similarity should have a lower loss that pixels with lower similarity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "To scale the loss by the dissimilarity of the prediction to the target prediction, for targeted settings, the targeted CosPGD update step is given by Eqn 7 in analogy to Eqn 5.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "X advt+1 = X advt + α • sign∇ X adv t (7) i 1 -cos ψ(f θ (X advt ) i ), Y i • L f θ (X advt ) i , Y i", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "Choice of ψ and Algorithm Description. In Equation 5, we require ψ to be monotonically increasing, differentiable, and, to ensure smooth convergence, smooth. To obtain a distribution over the predictions, we calculate the softmax of the predictions before taking the argmax", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ψ(f θ (X)) = softmax(f θ (X)),", "eq_num": "(8)"}], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "where, softmax", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(x i ) = exp(x i ) j exp(x j ) .", "eq_num": "(9)"}], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "Thus, in Algorithm 1 (given in Appendix A.2) and Equation 5, ψ is the softmax function. In the case of semantic segmentation, we obtain the distribution of the target Y i for every point i by generating a one-hot encoded vector of the label (i.e. encoding the argmax label) while we also apply softmax to compute Y i from continuous targets, e.g. for optical flow or disparity estimation. One-hot encoding and softmax to represent Y i are summarized by function Ψ ′ in Algorithm 1. X adv is initialized to the clean input sample X clean with added randomized noise in the range [-ϵ, +ϵ], ϵ being the maximum allowed perturbation. Over attack iterations X = X advt , the adversarial example generated at iteration t, such that t ∈ [0, T ), where T is the total number of attack iterations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "Loss Scaling in Previous Approaches. When optimizing δ for an adversarial attack for semantic segmentation, <PERSON><PERSON> et al. (2022) have argued before that pixels which are already misclassified by the model are less relevant than pixels correctly classified by the model, because the intention of the attack is to make the model misclassify as many pixels as possible while perturbing the δ inside the ϵ-ball. As a consequence, they make a hard decision based on each pixels argmax prediction as of whether it is taken into account for attack computation. In (<PERSON><PERSON> et al., 2022) , the PGD update from Equation 4 is thus modified to (10) where P T is the set of correctly classified pixels and P F is the set of wrongly classified pixels, λ is a scaling factor between the two parts of the loss that is set heuristically, and Y is the one-hot encoded ground truth for semantic segmentation. See their equation (4) for details.", "cite_spans": [{"start": 108, "end": 124, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF27"}, {"start": 553, "end": 570, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}, {"start": 624, "end": 628, "text": "(10)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "sign∇ X adv t (1 -λ) i∈P T L f θ (X advt ) i , Y i + λ k∈P F L f θ (X advt ) k , Y k ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "For positive λ and for categorical labels (i.e. Y one-hot encoded), we can rewrite the SegPGD update as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "sign∇ X adv t i 1 -λ - |(argmax (f θ (X adv t )i) -Y i| 2 • L f θ (X advt ) i , Y i", "eq_num": "(11)"}], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "for all locations i ∈ P T ∪ P F , i.e. |λ -|(argmax(f (X advt )) -Y |/2| equals 1 -λ for incorrect predictions, it equals λ for correct predictions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "Thus, the approach by <PERSON><PERSON> et al. (2022) resembles a discrete approximation of the proposed CosPGD. Yet, the discrete nature of this weighting scheme has several disadvantages: First, it limits SegPGD to applications where the correctness of the prediction can be evaluated in a binary way, and it disregards the actual prediction scores. For pixel-wise regression tasks (like optical flow, or image reconstruction) there is no absolute measure of correctness, so SegPGD can not be directly applied. Second, as the number of misclassified pixels increases, the attack loses effectiveness if it only focuses on correctly classified pixels in a binary way. The λ scaling in (<PERSON><PERSON> et al., 2022) has been proposed as a heuristical remedy. It scales the loss over iterations such that the impact of the proposed scheme decays over time. At the end of the attack iterations, λ ≈ 1/2. This avoids the concern of the attack becoming benign after a few iterations, yet it fades out the effect of SegPGD and may reduce its efficiency. CosPGD, operating on continuous predictions, does not require such a heuristic.", "cite_spans": [{"start": 22, "end": 38, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF27"}, {"start": 670, "end": 687, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "Last, but maybe most importantly, the scaling based on discrete labels is not smooth, i.e. the argmax operation in Equation 11 is not differentiable, such that, during the iterations, the direction of the gradient update can fluctuate, potentially leading to slower convergence of the SegPGD attack, compared to the proposed CosPGD. We show empirical evidence for this issue in Figure 2 where we report the change in gradients and their directions during the attack optimization for PGD, SegPGD and the proposed CosPGD. ", "cite_spans": [], "ref_spans": [{"start": 385, "end": 386, "text": "2", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Prediction Alignment Scaling -CosPGD", "sec_num": "4."}, {"text": "To demonstrate the wide applicability of CosPGD, we conduct our experiments on distinct downstream tasks: semantic segmentation, optical flow estimation, and image restoration. For semantic segmentation, we compare CosPGD to SegPGD and PGD and empirically validate its improved stability over the attack iterations. Further, we verify that CosPGD indeed encourages the attack to act on the entire image domain, with quantitative and qualitative results on non-targeted attacks on semantic segmentation and targeted attacks on optical flow. For optical flow estimation and other tasks (such as image deblurring and image denoising), we compare CosPGD to PGD in the main paper. The subsequent experiments provide evidence of CosPGD being a strong adversarial attack in diverse tasks and setups. In the main paper, we report ℓ ∞ -norm constrained attacks with ϵ ≈ 8 255 for CosPGD, SegPGD, and PGD. For α, we follow (<PERSON><PERSON> et al., 2022) 1 provides an overview. Please also refer to the Appendix A.3 for all details on the experimental setup.", "cite_spans": [{"start": 913, "end": 930, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}], "ref_spans": [{"start": 931, "end": 932, "text": "1", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "We evaluate the stability of CosPGD on semantic segmentation PASCAL VOC 2012 (<PERSON><PERSON> et al., 2012) . To further analyze the effect on the optimization, Figure 2 (bottom) shows the respective change in gradient direction (note that PGD, SegPGD, and CosPGD update all consider the sign of the gradient). The evaluation verifies that the CosPGD updates are more stable over the iterations, such that we can expect faster convergence, i.e. a stronger attack at fewer iterations.", "cite_spans": [{"start": 77, "end": 102, "text": "(<PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF18"}], "ref_spans": [{"start": 163, "end": 164, "text": "2", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Stability during Attack Optimization", "sec_num": "5.1."}, {"text": "An indication of the potential benefit can be seen for example in Table 11 (Appendix), where we observe that at low attack iterations (iterations=3) SegPGD implies that PSPNet is more adversarially robust than DeepLabV3. However, after more attack iterations (iterations≥5), SegPGD reveals that DeepLabV3 is more robust than PSPNet. Contrary to this, CosPGD even at low attack iterations correctly predicts DeepLabV3 to be more robust than PSPNet. This is an insight that CosPGD provides with considerably fewer iterations, thus lower overall computation time, while compute costs per iteration are comparable, see ). Yet, the region with this label is predicted correctly. Here, only CosPGD also changes the prediction in this region to a third class.", "cite_spans": [], "ref_spans": [{"start": 72, "end": 74, "text": "11", "ref_id": "TABREF10"}], "eq_spans": [], "section": "Stability during Attack Optimization", "sec_num": "5.1."}, {"text": "In the following, we show empirically that CosPGD encourages the attack to alter predictions over the entire image domain while PGD and SegPGD are weaker in this respect. Semantic Segmentation. We first discuss the spatial balancing of CosPGD for untargeted attacks on semantic segmentation on PASCAL VOC2012, the standard setting evaluated in (<PERSON><PERSON> et al., 2022) .", "cite_spans": [{"start": 344, "end": 361, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Spatial Balancing of the Attack", "sec_num": "5.2."}, {"text": "Therefore, we consider the mean Intersection over Union (mIoU) and mean accuracy (mACC) over the attack iterations as reported in Figure 3 . The first observation is that CosPGD yields a much stronger attack compared to PGD or SegPGD for both DeepLabV3 (<PERSON> et al., 2017) and PSP-Net (<PERSON> et al., 2017) . Second, we observe that CosPGD pushes the mIoU to values close to zero even in the first attack iterations, meaning that almost all pixel labels are flipped, while the mIoU for PGD stagnates at a high level as it decreases slowly for SegPGD, leading to significantly higher mIoUs even after 100 iterations, that for CosPGD.", "cite_spans": [{"start": 253, "end": 272, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF10"}, {"start": 285, "end": 304, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF92"}], "ref_spans": [{"start": 137, "end": 138, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Spatial Balancing of the Attack", "sec_num": "5.2."}, {"text": "For example in Figure 4 after 40 attack iterations, all attacks are considerably fooling the network into making incorrect predictions. However, once the dominant class label is changed by SegPGD or PGD, they do not further opti-mize over small regions of correct predictions. In contrast, CosPGD successfully fools the model into making incorrect predictions even in these small regions by either swapping the region prediction with an already existing class or forcing the model into predicting a different class.", "cite_spans": [], "ref_spans": [{"start": 22, "end": 23, "text": "4", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Spatial Balancing of the Attack", "sec_num": "5.2."}, {"text": "PGD can bring down the mIoU of DeepLabV3 to 6.79%. SegPGD, by naïvely utilizing the pixel-wise segmentation error, deteriorates the model performance further to 2.69%. However, CosPGD can fool the network into making incorrect predictions for almost all pixels, bringing down the model performance to almost 0% after 100 iterations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spatial Balancing of the Attack", "sec_num": "5.2."}, {"text": "Optical Flow. The evaluation of whether an attack alters the prediction in all regions is less trivial to conduct than for semantic segmentation, since there is no absolute measure of correctness. Therefore, in Figure 5 , we evaluate CosPGD versus PGD for targeted attacks on optical flow (using RAFT (Teed & Deng, 2020) ) on the KITTI-2015 validation set such that we see how many of the point-wise flow predictions have an end point error (epe) to the target that is below a certain threshold. Ideally, we would see a curve that is rising to the maximum value very quickly, indicating that all predictions are very close to the target. Figure 5 indicates that CosPGD achieves to bring more pixel-wise predictions very close to the target whereas only few predictions have larger epe. For PGD, more predictions remain with higher epe to the target. SegPGD can not directly be compared to in this regard, since it is conceived for semantic segmentation and requires an absolute measure of correctness (i.e. is the predicted label correct).", "cite_spans": [{"start": 301, "end": 320, "text": "(<PERSON><PERSON> & <PERSON>, 2020)", "ref_id": "BIBREF76"}], "ref_spans": [{"start": 218, "end": 219, "text": "5", "ref_id": null}, {"start": 645, "end": 646, "text": "5", "ref_id": null}], "eq_spans": [], "section": "Spatial Balancing of the Attack", "sec_num": "5.2."}, {"text": "A comparison of CosPGD to PGD in terms of epe over the iterations is shown in Figure 6 . Here, we quantitatively observe better performance of CosPGD compared to PGD. As this is the targeted setting, we intend to close the gap between the target prediction and the model predictions, thus a lower epe of the model prediction w.r.t. the target prediction is desired. As the attack iterations increase, across datasets, CosPGD can significantly fool the network into making predictions closer to the target, bringing down the epe to as low as 1.55 for Sintel (final) (see Appendix C).", "cite_spans": [], "ref_spans": [{"start": 85, "end": 86, "text": "6", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "Spatial Balancing of the Attack", "sec_num": "5.2."}, {"text": "We qualitatively observe in Figure 7 that the initial optical flow estimation by the model (which is substantially different to the target) is only moderately changed when the model is attacked with PGD. As the attack was designed for classification tasks, the model is not substantially fooled even as the intensity of the attack is increased to 40 iterations. Figure 7 KITTI-2015 (left) and Sintel (clean → right) validation datasets as ℓ ∞ -norm constrained targeted attacks using RAFT. CosPGD is a stronger targeted attack than PGD for optical flow. We also report these results in Table 13 in Appendix C. still preserved, for example, the bark of the tree. This is in contrast to when the model is attacked with CosPGD, a method that utilizes pixel-wise information. In Figure 7 (e), we observe that even at a small number of attack iterations (5), the model predictions are significantly different from the initial predictions, especially in the background and the shape of the moving car. The model is incorrectly predicting the motion of the pixels around the moving car. At high attack intensity, as shown in Figure 7 (f) with 40 iterations, the model's optical flow predictions are significantly inaccurate and exceedingly different from the initial predictions and very close to the target of -→ 0 . The model fails to differentiate the moving car from its background, moreover, the bark of the tree has completely vanished. In a real-world scenario, this vulnerability of the model to a relatively small pertur- bation (ϵ = 8 255 ) could be hazardous. CosPGD provides us with this new insight. A similar observation is made for the Sintel dataset as shown in Figure 1 . The benefit of CosPGD over PGD for optical flow can be quantitatively seen in Figure 6 and Table 13 in Appendix C.", "cite_spans": [{"start": 371, "end": 388, "text": "KITTI-2015 (left)", "ref_id": null}], "ref_spans": [{"start": 35, "end": 36, "text": "7", "ref_id": null}, {"start": 369, "end": 370, "text": "7", "ref_id": null}, {"start": 592, "end": 594, "text": "13", "ref_id": "TABREF12"}, {"start": 782, "end": 783, "text": "7", "ref_id": null}, {"start": 1125, "end": 1126, "text": "7", "ref_id": null}, {"start": 1678, "end": 1679, "text": "1", "ref_id": "FIGREF1"}, {"start": 1767, "end": 1768, "text": "6", "ref_id": "FIGREF6"}, {"start": 1779, "end": 1781, "text": "13", "ref_id": "TABREF12"}], "eq_spans": [], "section": "Spatial Balancing of the Attack", "sec_num": "5.2."}, {"text": "Semantic Segmentation. We observed the strength of CosPGD as a ℓ ∞ -norm constrained attack in Figures 3 & 4 . Furthermore, we show that the improved performance of CosPGD is not limited to ℓ ∞ -norm constrained attacks. Figure 10 in Appendix B.6.1 demonstrates the versatility of CosPGD as an ℓ 2 -norm constrained attack.", "cite_spans": [], "ref_spans": [{"start": 103, "end": 104, "text": "3", "ref_id": "FIGREF3"}, {"start": 107, "end": 108, "text": "4", "ref_id": "FIGREF5"}, {"start": 228, "end": 230, "text": "10", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Benchmarking on Further Tasks and Settings", "sec_num": "5.3."}, {"text": "We observe that across ℓ p -norm constraints, the gap in performance of CosPGD w.r.t other adversarial attacks significantly increases when increasing the number of attack iterations. This demonstrates that CosPGD can utilize the increase in attack iterations best and highlights the significance of scaling the pixel-wise loss with the cosine alignment of predictions rather than using a heuristic, argmaxbased scaling as in SegPGD.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Benchmarking on Further Tasks and Settings", "sec_num": "5.3."}, {"text": "Thus, we successfully demonstrate the benefit of CosPGD over existing adversarial attacks for semantic segmentation. We provide more results on ℓ ∞ -norm and ℓ 2 -norm constrained non-targeted adversarial attacks for semantic segmentation using UNet (<PERSON> et al., 2015) with ConvNeXt backbone on CityScapes (<PERSON><PERSON><PERSON> et al., 2016) in Appendix B.5, further confirming the benefit of CosPGD.", "cite_spans": [{"start": 250, "end": 276, "text": "(<PERSON><PERSON> et al., 2015)", "ref_id": "BIBREF63"}, {"start": 314, "end": 335, "text": "(<PERSON><PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Benchmarking on Further Tasks and Settings", "sec_num": "5.3."}, {"text": "Additionally, we ablate over the attack step size α for ℓ ∞norm constrained attacks on DeepLabV3 using PASCAL VOC2012 validation dataset in Appendix B.6.2 and over multiple attack step size α and permissible perturbation ϵ for l 2 -norm constrained attacks on DeepLabV3 using PASCAL VOC2012 validation dataset in Appendix B.6. We show in Appendix B.6.1 that CosPGD outperforms both PGD and SegPGD (for segmentation) in the ℓ 2 -norm constraint settings under all commonly used ϵ and α values.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Benchmarking on Further Tasks and Settings", "sec_num": "5.3."}, {"text": "Optical Flow. In addition to the results discussed in Section 5.2, we provide results comparing CosPGD to PGD as a ℓ ∞ -constrained non-targeted attack for optical flow estimation in Appendix C.2. We also provide a comparison to PCFA (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022b) in Appendix. C.3. Image Deblurring. To demonstrate CosPGD's versatility, we last consider the vision transformer-based image restoration model NAFNet (Chen et al., 2022) . NAFNet outperforms Restormer (<PERSON><PERSON><PERSON> et al., 2022) for image restoration tasks like image de-blurring and image denoising on clean data, thus implying that NAFNet learns good representations. Figure 8 depicts results for NAFNet on image deblurring of the GoPro dataset images. We observe that CosPGD is a significantly stronger attack than both PGD and SegPGD on this task. We provide further discussion and results on Restormer (<PERSON><PERSON><PERSON> et al., 2022) and the \"Baseline network\" (<PERSON> et al., 2022) in Appendix D.1.", "cite_spans": [{"start": 234, "end": 260, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022b)", "ref_id": null}, {"start": 411, "end": 430, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF9"}, {"start": 462, "end": 482, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF89"}, {"start": 861, "end": 881, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF89"}, {"start": 909, "end": 928, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF9"}], "ref_spans": [{"start": 631, "end": 632, "text": "8", "ref_id": "FIGREF8"}], "eq_spans": [], "section": "Benchmarking on Further Tasks and Settings", "sec_num": "5.3."}, {"text": "In this work, we demonstrated across different downstream tasks and architectures that our proposed adversarial attack, CosPGD, is significantly more effective than other existing and commonly used adversarial attacks on several pixelwise prediction tasks. We provide a new algorithm for evaluating the adversarial robustness of models on pixel-wise tasks. By comparing CosPGD to attacks like PGD, which were originally proposed for image classification tasks, we expanded on the work by <PERSON><PERSON> et al. (2022) and highlighted the need and effectiveness of attacks specifically designed for pixel-wise prediction tasks beyond segmentation. We illustrated the intuition behind using cosine similarity as a measure for generating stronger adversaries and leveraging more information from the model and backed it with experimental results from different downstream tasks. This further highlights the simplicity and principled formulation of CosPGD, making it applicable to a wide range of pixel-wise prediction tasks and in principle extendable to all Lipschitz continuous bounds as a targeted as well as a non-targeted attack.", "cite_spans": [{"start": 488, "end": 504, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Limitations. Most white-box adversarial attacks require access to ground truth labels (<PERSON><PERSON><PERSON> et al., 2014; <PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON> et al., 2017; <PERSON> et al., 2020b; <PERSON><PERSON> et al., 2022) . While this is beneficial for generating adversaries, it limits the applications of the non-targeted attacks like SegPGD as many benchmark datasets (<PERSON><PERSON> & Geiger, 2015; <PERSON> et al., 2012; <PERSON><PERSON><PERSON> et al., 2012; <PERSON><PERSON> et al., 2012) do not provide the ground truth for test data. The wide-applicability of CosPGD allows it to be used as a targeted attack thus mitigating this limitation to a great extent. Yet, it would be interesting to study the attack on the ground truth test images in the non-targeted setting as well, due to the potential slight distribution shifts preexisting in the test data. We discuss additional limitations of CosPGD in Appendix E.", "cite_spans": [{"start": 86, "end": 111, "text": "(<PERSON><PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF24"}, {"start": 112, "end": 133, "text": "<PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF47"}, {"start": 134, "end": 153, "text": "<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF53"}, {"start": 154, "end": 173, "text": "<PERSON> et al., 2020b;", "ref_id": null}, {"start": 174, "end": 190, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}, {"start": 340, "end": 362, "text": "(Menze & Geiger, 2015;", "ref_id": "BIBREF56"}, {"start": 363, "end": 383, "text": "<PERSON> et al., 2012;", "ref_id": "BIBREF7"}, {"start": 384, "end": 403, "text": "<PERSON><PERSON><PERSON> et al., 2012;", "ref_id": "BIBREF7"}, {"start": 404, "end": 428, "text": "<PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "We have carefully read the ICML 2024 Code of Ethics and confirm that we adhere to it. The proposed work is original and novel. To the best of our knowledge, all literature used in this work has been referenced correctly. Our work did not involve any human subjects and does not pose a threat to humans or the environment.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "Assessing the quality of representations learned by a machine learning model is of paramount importance. This makes sure that the model is not learning shortcuts from the input distribution to the target distribution (<PERSON><PERSON><PERSON><PERSON> et al., 2020) but learning something meaningful. Adversarial attacks are a reliable tool for gauging the quality of a model's learned representations. However adversarial attacks are time and computation exhaustive. Thus, our proposed adversarial attack, CosPGD helps in this regard as it can provide new insights into a model's robustness and vulnerabilities with much less time and thus computation and is theoretically motivated. Thus, our work helps advance the field of machine learning.", "cite_spans": [{"start": 217, "end": 239, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "CosPGD: an efficient and unified white-box adversarial attack for pixel-wise prediction tasks", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CosPGD", "sec_num": null}, {"text": "We include the following information in the supplementary material:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "• Section A Additional Details:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "-Section A.1: We provide the proof for proposition 4.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "-Section A.2: Algorithm of CosPGD.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "-Section A.3: Hardware details -Section A.3.1: Implementation details including code and example usage.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "-Section A.3.3: We provide additional experimental details for the image deblurring experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "-Section A.3.4: We compare the time taken by different adversarial attacks for different tasks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "-Section A.3.2: Details on calculating epe-f1-all.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "• Section B: Semantic Segmentation Additional Results:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "-Section B.1: We provide additional experimental results using SegFormer (<PERSON><PERSON> et al., 2021) on ADE20K (<PERSON> et al., 2017; 2019) . • Section D: Image Restoration Results:", "cite_spans": [{"start": 73, "end": 91, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF86"}, {"start": 102, "end": 121, "text": "(<PERSON> et al., 2017;", "ref_id": "BIBREF93"}, {"start": 122, "end": 127, "text": "2019)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "*", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "-Section D.1: We report the findings on the adversarial robustness of many recently proposed transformer-based image deblurring models. -Section D.2: We report the results on many recently proposed transformer-based image denoising models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "• Section E: A detailed discussion on limitations of CosPGD", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "In Table 1 , we provide a look-up table for all experiments considered in this supplementary material. We provide details on the downstream tasks, models, targeted and non-targeted attack settings, and l ∞ -norm constrained and l 2 -norm constrained settings considered respectively do demonstrate the wide-applicability of CosPGD. We are to show that, for any two pixel-wise network predictions f θ (X) i and f θ (", "cite_spans": [], "ref_spans": [{"start": 9, "end": 10, "text": "1", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Supplementary Material", "sec_num": null}, {"text": "X) i ∈ R M , a target Y i ∈ R M and a continuously differentiable function ψ : R M → R M with ψ(f θ (X)) = 1 ∀f θ (X), there exists a real, constant d ≥ 0 so that d • ∥f θ (X) i -f θ ( X) i ∥ ≥ ∥cos (ψ(f θ (X) i ), Y i ) -cos ψ(f θ ( X) i ), Y i ∥.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Proof. The function ψ : R M → R M as well as the cosine similarity cos : R M × R M → [-1, 1] are both continuously differentiable functions. From the continuous differentiability of ψ, it follows that is it <PERSON><PERSON><PERSON>tz continuous, i.e. there exists a real constant d 1 ≥ 0 so that", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "d 1 • ∥f θ (X) i -f θ ( X) i ∥ ≥ ∥ψ(f θ (X) i ) -ψ(f θ ( X) i )∥", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "for any f θ (X) i and f θ ( X) i ∈ R M . Further, the cosine similarity effectively computes the norm of the projection of the normalized model predictions onto the target vector, which is again a continuously differentiable operation, i.e. is again <PERSON><PERSON><PERSON><PERSON> continuous", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "d 2 • ∥ψ(f θ (X) i ) -ψ(f θ ( X) i )∥ ≥ ∥cos (ψ(f θ (X) i ), Y i ) -cos ψ(f θ ( X) i ), Y i ∥.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "for a real constant d 2 ≥ 0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Following we present the algorithm for CosPGD. Algorithm 1 provides a general overview of the implementation of CosPGD.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2. Algorithm for CosPGD", "sec_num": null}, {"text": "It demonstrates that CosPGD is downstream-task agnostic, l p -norm agnostic, and agnostic to targeted or non-targeted application.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2. Algorithm for CosPGD", "sec_num": null}, {"text": "Semantic Segmentation We use PASCAL VOC 2012 (<PERSON><PERSON> et al., 2012) , which contains 20 object classes and one background class, with 1464 training images, and 1449 validation images. We follow common practice (<PERSON><PERSON><PERSON> et al., 2015; <PERSON><PERSON> et al., 2022; <PERSON>, 2019; <PERSON> et al., 2017) , and use work by <PERSON><PERSON><PERSON> et al. (2011) , augmenting the training set to 10,582 images. We evaluate on the validation set. Architectures used for our evaluations are PSPNet (<PERSON> et al., 2017) and DeepLabV3 (<PERSON> et al., 2017) , both with ResNet50 (<PERSON> et al., 2015) encoders, and UNet (<PERSON><PERSON><PERSON> et al., 2015) with a ConvNeXt tiny encoder (<PERSON> et al., 2022) . Results are reported in Appendix B.5. We report mean Intersection over Union (mIoU) and mean pixel accuracy (mAcc).", "cite_spans": [{"start": 45, "end": 70, "text": "(<PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF18"}, {"start": 213, "end": 237, "text": "(<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF29"}, {"start": 238, "end": 254, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF27"}, {"start": 255, "end": 266, "text": "Zhao, 2019;", "ref_id": null}, {"start": 267, "end": 285, "text": "<PERSON> et al., 2017)", "ref_id": "BIBREF92"}, {"start": 304, "end": 327, "text": "<PERSON><PERSON><PERSON> et al. (2011)", "ref_id": "BIBREF28"}, {"start": 461, "end": 480, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF92"}, {"start": 495, "end": 514, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF10"}, {"start": 536, "end": 553, "text": "(<PERSON> et al., 2015)", "ref_id": "BIBREF30"}, {"start": 573, "end": 599, "text": "(<PERSON><PERSON> et al., 2015)", "ref_id": "BIBREF63"}, {"start": 629, "end": 647, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "A.3. Further Experimental Details on Hardware and Metrics", "sec_num": null}, {"text": "Hardware. For the experiments on DeepLabV3, we used NVIDIA Quadro RTX 8000 GPUs. For PSPNet, we used NVIDIA A100 GPUs. For the experiments with UNet, we used NVIDIA GeForce RTX 3090 GPUs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3. Further Experimental Details on Hardware and Metrics", "sec_num": null}, {"text": "CosPGD adversarial attacks on both the Baseline network and NAFNet.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3. Further Experimental Details on Hardware and Metrics", "sec_num": null}, {"text": "Dataset. Similar to (<PERSON> et al., 2022) , for the image de-blurring task, we use the GoPro dataset (<PERSON> et al., 2017) which consists of 3124 realistically blurry images of resolution 1280×720 and corresponding ground truth sharp images obtained using a high-speed camera. The images are split into 2103 training images and 1111 test images. For the image denoising task, we use the Smartphone Image Denoising Dataset (SSID) (<PERSON><PERSON><PERSON> et al., 2018) . This dataset consists of 160 noisy images taken from 5 different smartphones and their corresponding high-quality ground truth images.", "cite_spans": [{"start": 20, "end": 39, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF9"}, {"start": 99, "end": 117, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF59"}, {"start": 424, "end": 449, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "A.3. Further Experimental Details on Hardware and Metrics", "sec_num": null}, {"text": "Metrics. For both the image restoration tasks, we report the P SN R and SSIM scores of the reconstructed images w.r.t. to the ground truth images, averaged over all images. P SN R stands for Peak Signal-to-Noise ratio, a higher P SN R indicates a better quality image or an image closer to the image to which it is being compared. SSIM stands for Structural similarity (<PERSON> et al., 2004) .", "cite_spans": [{"start": 369, "end": 388, "text": "(<PERSON> et al., 2004)", "ref_id": "BIBREF78"}], "ref_spans": [], "eq_spans": [], "section": "A.3. Further Experimental Details on Hardware and Metrics", "sec_num": null}, {"text": "Following, we report the approximate time taken by each attack in minutes. Please note, this time includes time taken for data-loading and saving of experimental results including images. For a given task, network, and dataset, the time taken by different attacks is comparable and representative of the time taken by the attacks as they followed the same attack procedures. We observe in Table 2 that the difference in time taken by the different attacks at the same number of iterations is negligible. This is because operations like one-hot encoding and softmax take negligible time.", "cite_spans": [], "ref_spans": [{"start": 395, "end": 396, "text": "2", "ref_id": "TABREF0"}], "eq_spans": [], "section": "A.3.4. COMPARING TIME TAKEN BY DIFFERENT ADVERSARIAL ATTACKS", "sec_num": null}, {"text": "Thus, the ability of CosPGD to provide valuable insights into model robustness with significantly less iterations than other methods, as discussed in Section 5.2 and Section 5.3 is a compelling advantage. Since ADE20K has 150 classes, making it a more difficult distribution to learn, it is not usually considered to evaluate attack methods. We expect CosPGD to be a significantly stronger attack than SegPGD or the simple PGD on this data because it can smoothly align the loss to the posterior distribution. In Table 3 we confirm this by providing additional experiments We observe that CosPGD is a significantly stronger attack than SegPGD for ADE20K and SegFormer. Please also note that white-box attacks are extremely useful in exposing a model's vulnerabilities, however, they are very expensive to run, and thus 40 or more attack iterations are generally considered to be a very high number of attack iterations in white-box attack literature (please refer to PGD, APGD, PCFA, SegPGD, AutoAttack, MI-FGSM). Here, CosPGD required merely 10 attack iterations to bring the model mIoU to absolute 0.00, whereas SegPGD is not able to achieve this even when using 100 iterations (increasing the attack cost by a factor of 10). Our current understanding is that given a reasonable perturbation attack, and step size smaller than this budget (so that the perturbations are not clipped away by the budget), all attacks should optimize the adversary in the best possible way. We have shown that CosPGD is better at this optimization than the other white-box attacks for various step-sizes(α) and various ϵ values.", "cite_spans": [], "ref_spans": [{"start": 519, "end": 520, "text": "3", "ref_id": "TABREF4"}], "eq_spans": [], "section": "A.3.4. COMPARING TIME TAKEN BY DIFFERENT ADVERSARIAL ATTACKS", "sec_num": null}, {"text": "For ℓ ∞ -norm we have shown this for ϵ = 8 255 . The maximum permissible perturbation budget should not affect the relative performance of different attacks. We further solidify this claim here by providing additional experiments using SegFormer on ADE20K with ℓ ∞ -norm bounded ϵ = { increases, but is measurable across attack iterations. We show this in Table 5 . In Table 6 , we report the results on the evaluation of CosPGD on (<PERSON> et al., 2021) . Here we observe that defense methods as in (<PERSON> et al., 2021) might help in reducing some effect of the attacks but not nearly strong enough to negate them and CosPGD is still the strongest adversarial attack.", "cite_spans": [{"start": 432, "end": 449, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF88"}, {"start": 495, "end": 512, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF88"}], "ref_spans": [{"start": 362, "end": 363, "text": "5", "ref_id": "TABREF6"}, {"start": 375, "end": 376, "text": "6", "ref_id": "TABREF7"}], "eq_spans": [], "section": "A.3.4. COMPARING TIME TAKEN BY DIFFERENT ADVERSARIAL ATTACKS", "sec_num": null}, {"text": "Please note, we observed some errors in the white-box attack implementation in the official GitHub repository of (<PERSON> et al., 2021) . Thus, we were able to reproduce their reported clean accuracies of the three models, i.e. PSPNet with No Defense during training, PSPNet trained with SAT and PSPNet trained with DDC-AT (<PERSON> et al., 2021) . However, as their attack implementation code is wrong, specifically, the normalization done assumes the images to be in the space [0, 1], but in reality they are in [0, 255] . Thus, the performance reported by (<PERSON> et al., 2021) , under white-box adversarial attacks is incorrect. Therefore, we correct these errors and re-run their experiments and extend to them, going as far as 10 attack iterations. We correct the code from (<PERSON> et al., 2021) and provide the corrected code here: https://github.com/shashankskagnihotri/adv-corrected-ddcat-cospgd.", "cite_spans": [{"start": 113, "end": 130, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF88"}, {"start": 318, "end": 335, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF88"}, {"start": 503, "end": 506, "text": "[0,", "ref_id": null}, {"start": 507, "end": 511, "text": "255]", "ref_id": null}, {"start": 548, "end": 565, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF88"}, {"start": 765, "end": 782, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF88"}], "ref_spans": [], "eq_spans": [], "section": "B.3. Evaluating against Defense Methods", "sec_num": null}, {"text": "In Table 7 , we present this evaluation on (<PERSON><PERSON><PERSON> et al., 2023) against their robust \"UPerNet (<PERSON> et al., 2018) with a ConvNext-tiny backbone\" encoder checkpoint that they make available in their official GitHub repository. We modify their Segmentation Ensemble Attack (SEA) (<PERSON><PERSON><PERSON> et al., 2023) to only include the respective attack mentioned for the given number of attack iterations. The optimizer they used is always APGD.", "cite_spans": [{"start": 43, "end": 63, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF15"}, {"start": 94, "end": 113, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF84"}, {"start": 277, "end": 297, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF15"}], "ref_spans": [{"start": 9, "end": 10, "text": "7", "ref_id": null}], "eq_spans": [], "section": "B.3. Evaluating against Defense Methods", "sec_num": null}, {"text": "We extent Table 7 in Table 8 , here we report the results for ϵ = 4 255 and observe that the performance is comparable at the extremely high number of iterations i.e. 1200 attack iterations. W.r.t. the comparison to (<PERSON><PERSON><PERSON> et al., 2023) for ϵ = 4/255 and very high number of iterations, we would like to highlight that, since the model is trained for this value, the differences between the attacks are actually small. Indeed, for high attack iterations, SegPGD is slightly stronger, yielding a maximum difference of 0.25% in mAcc for 300 iterations versus CosPGD, while at 10 attack iterations, CosPGD is also only slightly stronger than SegPGD in the same range. However, assuming that Table 7 : Attacking Robust UPerNet (<PERSON> et al., 2018) with ConvNeXt-tiny encoder from (<PERSON><PERSON><PERSON> et al., 2023) (<PERSON><PERSON><PERSON> et al., 2023) APGD 1200", "cite_spans": [{"start": 216, "end": 236, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF15"}, {"start": 723, "end": 742, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF84"}, {"start": 775, "end": 795, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF15"}, {"start": 796, "end": 816, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF15"}], "ref_spans": [{"start": 16, "end": 17, "text": "7", "ref_id": null}, {"start": 27, "end": 28, "text": "8", "ref_id": "TABREF8"}, {"start": 694, "end": 695, "text": "7", "ref_id": null}], "eq_spans": [], "section": "B.3. Evaluating against Defense Methods", "sec_num": null}, {"text": "63.800 88.300 SEA (<PERSON><PERSON><PERSON> et al., 2023) reproduced by us 63.670 88.320 replacing SegPGD with CosPGD(softmax) in SEA (<PERSON><PERSON><PERSON> et al., 2023) 63.700 88.300 (<PERSON><PERSON><PERSON> et al., 2023) does not only aim for robustness w.r.t. ϵ = 4/255 but aims to generalize (which we infer from their evaluation), it is fair to consider the range of improvement CosPGD reaches over SegPGD for ϵ = 12/255 or ϵ = 16/255 (scenarios considered in (<PERSON><PERSON><PERSON> et al., 2023) as well). There, CosPGD decreases the mAcc by almost 10% more than SegPGD (for 30 iterations), and be more than 3% more for 300 iterations. The general tendency is also that with really high numbers of attack iterations (>100 iterations: not commonly considered by peer-reviewed white-box attack works), the differences between CosPGD and SegPGD become smaller, even for ϵ bounds for which the model has not been trained. This is in line with our expectation, coming from the point that CosPGD has smoother gradients and allows to compute better attacks with few iterations, as discussed in Section 4.", "cite_spans": [{"start": 18, "end": 38, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF15"}, {"start": 115, "end": 135, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF15"}, {"start": 150, "end": 170, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF15"}, {"start": 413, "end": 433, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF15"}], "ref_spans": [], "eq_spans": [], "section": "B.3. Evaluating against Defense Methods", "sec_num": null}, {"text": "In Table 9 , we show that when we attack a DeepLabV3 with a ResNet50 encoder on PASCAL VOC2012 images, and transfer the 100 iterations attack to SAM (<PERSON><PERSON><PERSON> et al., 2023) , only the CosPGD attack can cause failures in the segmentation masks. SegPGD fails to create failures in the segmentation masks of SAM, when compared to its segmentation masks on a clean image.", "cite_spans": [{"start": 149, "end": 172, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [{"start": 9, "end": 10, "text": "9", "ref_id": null}], "eq_spans": [], "section": "B.4. Evaluating Attacks against SAM", "sec_num": null}, {"text": "Note that these are just random sample results, as quantitative evaluation would be invalid. This is because the publicly low attack iterations correctly predicts DeepLabV3 to be more robust than PSPNet. This is an insight that CosPGD provides with considerably less computation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.4. Evaluating Attacks against SAM", "sec_num": null}, {"text": "In Figure 13 we show the segmentation masks predicted by UNet after being adversarially trained. We observe that even after 100 attack iterations, the model adversarially trained using CosPGD is making reasonable predictions. However, the model trained with SegPGD is merely predicting a blob.", "cite_spans": [], "ref_spans": [{"start": 10, "end": 12, "text": "13", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "B.8. Adversarial Training", "sec_num": null}, {"text": "In Table 12 we report the performance of models trained with various adversarial attacks against different commonly used adversarial attacks across multiple attack iterations. We observe that the model trained with CosPGD performs the best against all considered adversarial attacks. The models were trained with 3 attack iterations of the respective \"Training Method\" attack during training.", "cite_spans": [], "ref_spans": [{"start": 9, "end": 11, "text": "12", "ref_id": "TABREF11"}], "eq_spans": [], "section": "B.8. Adversarial Training", "sec_num": null}, {"text": "In Figure 12 Here we report the extended results from Figure 6 comparing CosPGD to PGD as a targeted attack using RAFT for KITTI15 and Sintel datasets in 14 and in tabular form in Table 13 . We observe that CosPGD is more effective than PGD to change the predictions toward the targeted prediction. During a low number of iterations (iterations = 3 and 5), PGD is on par with CosPGD in increasing the epe values of the predictions compared to the initial predictions on non-attacked images. However, as the number of iterations increases, CosPGD outperforms PGD for this metric as well. In the following, we report further results and compare CosPGD to a recently proposed sophisticated l 2 -norm constrained targeted attack PCFA.", "cite_spans": [], "ref_spans": [{"start": 10, "end": 12, "text": "12", "ref_id": "FIGREF13"}, {"start": 61, "end": 62, "text": "6", "ref_id": "FIGREF6"}, {"start": 186, "end": 188, "text": "13", "ref_id": "TABREF12"}], "eq_spans": [], "section": "B.8. Adversarial Training", "sec_num": null}, {"text": "For l ∞ -norm constrained non-targeted attacks, CosPGD changes pixels values temperately over a larger region of the image, while PGD changes it drastically but only for a small region in the image. This can be observed in Figure 15 when CosPGD and PGD are compared as l ∞ -norm constrained non-targeted attacks for optical flow estimation. We observe that both CosPGD and PGD are performing at par as both have very similar epe values across iterations. However, CosPGD across iterations has a lower epe-f 1-all value. As shown by Equation 12 in Section A.3.2, epe-f 1-all is the measure of average overall epe values that are above a modest threshold. Therefore, both CosPGD and PGD have very similar epe scores while CosPGD has a significantly lower epe-f 1-all compared to PGD. This implies that CosPGD and PGD are performing at par, however, PGD is drastically changing epe values at certain pixels, while CosPGD is changing epe values temperately over considerably more pixels. Figure 16 shows this qualitatively for 4 randomly chosen samples.", "cite_spans": [], "ref_spans": [{"start": 230, "end": 232, "text": "15", "ref_id": "FIGREF15"}, {"start": 991, "end": 993, "text": "16", "ref_id": "FIGREF16"}], "eq_spans": [], "section": "C.2. Non-targeted attacks for optical flow estimation", "sec_num": null}, {"text": "Further, we compare CosPGD as a l 2 -norm constrained targeted attack to the recently proposed state-of-the-art l 2 -norm constrained targeted attack PCFA (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022b) . For comparison. we use the same settings as those used by the authors for both attacks, for 20 attack iterations (steps), generating adversarial patches for each image individually, bounded under the change of variables methods proposed by <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2022b) . Here, we observe that a sophisticated l 2 -norm constrained targeted attack, PCFA that does not utilise pixel-wise information for generating adversarial patches over all considered networks and datasets, performs similar to CosPGD. We compare over the performance over RAFT, PWCNet (Sun et al., 2018) , GMA (Jiang et al., 2021) and SpyNet (Ranjan & Black, 2017) We consider both targeted settings proposed by <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2022b) , i.e. target being a zero vector -→ 0 and target being the negative of the initial prediction (negative flow). We compare the average epe over all images. A lower AEE is w.r.t. Target and higher AEE w.r.t. initial indicate a stronger attack. In Table 14 (currently included at the end of the appendix to not disturb the table numbers), we compare PCFA and CosPGD on multiple datasets, multiple networks over 3 random seeds.", "cite_spans": [{"start": 155, "end": 181, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022b)", "ref_id": null}, {"start": 424, "end": 449, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2022b)", "ref_id": null}, {"start": 735, "end": 753, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF73"}, {"start": 760, "end": 780, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF38"}, {"start": 792, "end": 814, "text": "(Ranjan & Black, 2017)", "ref_id": "BIBREF61"}, {"start": 862, "end": 887, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2022b)", "ref_id": null}], "ref_spans": [{"start": 1140, "end": 1142, "text": "14", "ref_id": "TABREF14"}], "eq_spans": [], "section": "C.3. <PERSON><PERSON><PERSON><PERSON> to PCFA", "sec_num": null}, {"text": "Figure 17 , provides an overview of the comparison between the two methods, using targets as -→ 0 and negative flow. Figures 18, 19, provide further details compares both methods when using -→ 0 and negative flow as the target, respectively.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 9, "text": "17", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "C.3. <PERSON><PERSON><PERSON><PERSON> to PCFA", "sec_num": null}, {"text": "In Table 14 , we include the results in a tabular form. CosPGD is a stronger targeted attack than PGD for optical flow. We also report these results in Table 13 in Appendix C. It would be interesting to extend these evaluations to newer optical flow datasets such as Spring (<PERSON> et al., 2023) .", "cite_spans": [{"start": 274, "end": 293, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF55"}], "ref_spans": [{"start": 9, "end": 11, "text": "14", "ref_id": "TABREF14"}, {"start": 158, "end": 160, "text": "13", "ref_id": "TABREF12"}], "eq_spans": [], "section": "C.3. <PERSON><PERSON><PERSON><PERSON> to PCFA", "sec_num": null}, {"text": "Following, we provide further results and discussion on the two considered image restoration tasks namely, Image Deblurring in Section D.1 and Image Denoising in Section D.2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D. Image Restoration Tasks", "sec_num": null}, {"text": "In Figure 20 for the Baseline network, we observe that both CosPGD and PGD are performing at par. While for the newly proposed NAFNet, PGD is still estimating NAFNet's adversarial robustness to be very similar to the Baseline network and only after 20 attack iterations it is estimating correctly that NAFNet is not as robust as the Baseline network. However, CosPGD reveals that NAFNet is not as robust as the baseline even at a low number of iterations (3 attack iterations). This valuable insight regarding model robustness of newly proposed transformer-based image restoration models is provided by CosPGD with considerably less computation.", "cite_spans": [], "ref_spans": [{"start": 10, "end": 12, "text": "20", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "D.1. Image Deblurring models", "sec_num": null}, {"text": "To enable the applicability of SegPGD on this task, we implement SegPGD by comparing the equality of the pixel values to use their proposed loss for comparison. Following the discussion from Section 5.3, in Figure 8 for the Baseline network we also observe that SegPGD here is significantly weaker due to its limitation to image classification tasks as discussed in Section 4. However, for NAFNet, from 5 attack iterations onwards SegPGD is outperforming PGD, while still being weaker than CosPGD. This, interesting improvement in the performance of SegPGD as an adversarial attack can be attributed to the pixel-wise nature of the attack, similar to CosPGD further highlighting the benefits of utilizing pixel-wise information when crafting adversarial attacks for pixel-wise prediction tasks.", "cite_spans": [], "ref_spans": [{"start": 214, "end": 215, "text": "8", "ref_id": "FIGREF8"}], "eq_spans": [], "section": "D.1. Image Deblurring models", "sec_num": null}, {"text": "Additionally, we report the findings on many recently proposed state-of-the-art image restoration models using CosPGD in Table 15 . ", "cite_spans": [], "ref_spans": [{"start": 127, "end": 129, "text": "15", "ref_id": "TABREF15"}], "eq_spans": [], "section": "D.1. Image Deblurring models", "sec_num": null}, {"text": "Dataset. For the image denoising task, following work from (<PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022) we use the Smartphone Image Denoising Dataset (SSID) (<PERSON><PERSON><PERSON><PERSON> et al., 2018) . This dataset consists of 160 noisy images taken from 5 different smartphones and their corresponding high-quality ground truth images. Similar to the image deblurring task, we report the P SN R and SSIM values as metrics for this image restoration task as well.", "cite_spans": [{"start": 59, "end": 78, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF9"}, {"start": 79, "end": 98, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF89"}, {"start": 152, "end": 177, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "D.2. Non-targeted Attacks for Image Denoising Task", "sec_num": null}, {"text": "Discussion. Further extending the findings from Section C.2 we report l ∞ -norm constrained non-targeted attacks for the image denoising on the SSID dataset using the Baseline network and NAFNet (as proposed by (<PERSON> et al., 2022) ) in Figure . 21. We observe that both CosPGD and PGD are performing at par for both, the Baseline network and NAFNet. Additionally, similar to findings in Section 5.3, SegPGD is unable to perform at par with CosPGD and PGD.", "cite_spans": [{"start": 211, "end": 230, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "D.2. Non-targeted Attacks for Image Denoising Task", "sec_num": null}, {"text": "After both CosPGD and PGD attacks it appears that the image denoising networks are relatively more robust than image deblurring networks. These findings also correlate with (<PERSON><PERSON> et al., 2019) , as they report that feature denonising improves model robustness against adversarial attacks.", "cite_spans": [{"start": 173, "end": 191, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF85"}], "ref_spans": [], "eq_spans": [], "section": "D.2. Non-targeted Attacks for Image Denoising Task", "sec_num": null}, {"text": "Similar to most white-box adversarial attacks (<PERSON><PERSON><PERSON> et al., 2014; <PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON> et al., 2017; <PERSON> et al., 2020b; <PERSON><PERSON> et al., 2022) , CosPGD currently requires access to the model's gradients for generating adversarial examples. While this is beneficial for generating adversaries, it limits the applications of the non-targeted settings as many benchmark datasets (<PERSON> & Gei<PERSON>, 2015; <PERSON> et al., 2012; <PERSON><PERSON><PERSON> et al., 2012; <PERSON><PERSON> et al., 2012) do not provide the ground truth for test data. Evaluations of the validation datasets certainly show the merit of the attack method. CosPGD mitigates this limitation by also being applicable as an effective targeted attack. Nevertheless, it would be interesting to study the attack on test images as well in an untargeted setting, due to the potential slight distribution shifts pre-existing in the test data. While CosPGD is significantly more efficient than other existing adversarial attacks, all white-box adversarial attacks are time and memory consuming and benchmarking them across multiple downstream tasks, datasets, and networks is a very time-consuming process. Additionally, there are settings, especially for non-targeted attacks, where approaches like pixel-wise PGD would work at par with CosPGD as the epe can be increased equally well by either changing all pixel-wise regression estimates slightly (sophisticated attack like CosPGD) or by changing only a few of them drastically (brute force attacks like PGD). This can also be seen in the results in C.2. ", "cite_spans": [{"start": 46, "end": 71, "text": "(<PERSON><PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF24"}, {"start": 72, "end": 93, "text": "<PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF47"}, {"start": 94, "end": 113, "text": "<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF53"}, {"start": 114, "end": 133, "text": "<PERSON> et al., 2020b;", "ref_id": null}, {"start": 134, "end": 150, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}, {"start": 384, "end": 406, "text": "(Menze & Geiger, 2015;", "ref_id": "BIBREF56"}, {"start": 407, "end": 427, "text": "<PERSON> et al., 2012;", "ref_id": "BIBREF7"}, {"start": 428, "end": 447, "text": "<PERSON><PERSON><PERSON> et al., 2012;", "ref_id": "BIBREF7"}, {"start": 448, "end": 472, "text": "<PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Discuss<PERSON> on limitations of CosPGD", "sec_num": null}, {"text": "available version of SAM does not perform semantic segmentation (which is segmentation with class labels). SAM merely predicts segmentation masks without assigning them any class labels, and current variants of SAM used for Semantic Segmentation, for example in this GitHub repository perform worse than the other models we considered for this task. Furthermore, the masks produced by SAM are often finer than the ground truth masks of most datasets, making the calculation of metrics like mIoU invalid.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "S.J and M.K acknowledge funding by the DFG Research Unit 5336 -Learning to Sense. The OMNI cluster of University of Siegen was used for some of the initial computations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "The idea for CosPGD was conceptualized by <PERSON><PERSON><PERSON><PERSON> and improved by discussions with <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON> led the development, with inputs from <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON> provided supervision and contributed significantly to the writing. <PERSON><PERSON><PERSON> additionally made notable and significant contributions with experiments for non-targeted attacks on semantic segmentation, especially experiments with PSPNet, DeepLabV3 and Robust UPerNet. <PERSON><PERSON><PERSON><PERSON> performed the remaining experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Author Contribution", "sec_num": null}, {"text": "Algorithm 1 Algorithm for generating adversarial examples using CosPGD.Require: model fnet(•), clean samples X clean , perturbation range ϵ, step size α, attack iterations T , ground truth/target Y X adv 0 = X clean + U(-ϵ, +ϵ)▷ initialize adversarial example and clip to valid ℓ∞ or l2 bound for t ← 0 to T-1 do ▷ loop over attack iterations▷ scaling the pixel-wise loss for sample updates▷ add δ to X clean and clip into valid image range end for P = fnet(X adv T )▷ make predictions on adversarial examplesOptical Flow We use RAFT (Teed & Deng, 2020) and follow the evaluation procedure used therein. Evaluations are performed on KITTI2015 (<PERSON><PERSON> & Geiger, 2015) and MPI Sintel (<PERSON> et al., 2012; <PERSON><PERSON><PERSON> et al., 2012) validation sets.We use the networks pre-trained on FlyingChairs (<PERSON><PERSON><PERSON><PERSON> et al., 2015) and FlyingThings (<PERSON> et al., 2016) and fine-tuned on training datasets of the specific evaluation, as provided by Teed & Deng (2020) . For Sintel we report the end-point error (epe) on both clean and final subsets, while for KITTI15 we report the epe and epe-f 1-all. In Appendix C.3 we compare CosPGD to PCFA across different networks.Hardware. We used NVIDIA V100 GPUs, a single GPU was used for each run.Image Restoration Following the regime of (<PERSON> et al., 2022; Zamir et al., 2022; Agnihotri et al., 2023a) , for the image de-blurring task we use the GoPro dataset (Nah et al., 2017) as in (Chen et al., 2022) . The images are split into 2103 training images and 1111 test images. We consider the \"Baseline network\" and NAFNet as proposed by (Chen et al., 2022) . For the image restoration tasks we report the P SN R and SSIM scores of the reconstructed images w.r.t. to the ground truth images, averaged over all images. We provide further details in Appendix D.1.Hardware. For the experiments on Image de-blurring tasks, we used NVIDIA GeForce RTX 3090 GPUs. A single GPU was used for each run.", "cite_spans": [{"start": 534, "end": 553, "text": "(<PERSON><PERSON> & <PERSON>, 2020)", "ref_id": "BIBREF76"}, {"start": 643, "end": 665, "text": "(<PERSON>ze & Geiger, 2015)", "ref_id": "BIBREF56"}, {"start": 681, "end": 702, "text": "(<PERSON> et al., 2012;", "ref_id": "BIBREF7"}, {"start": 703, "end": 722, "text": "<PERSON><PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF7"}, {"start": 787, "end": 813, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2015)", "ref_id": "BIBREF17"}, {"start": 831, "end": 851, "text": "(<PERSON> et al., 2016)", "ref_id": "BIBREF54"}, {"start": 931, "end": 949, "text": "Teed & Deng (2020)", "ref_id": "BIBREF76"}, {"start": 1266, "end": 1285, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF9"}, {"start": 1286, "end": 1305, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF89"}, {"start": 1306, "end": 1330, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}, {"start": 1389, "end": 1407, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF59"}, {"start": 1414, "end": 1433, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF9"}, {"start": 1566, "end": 1585, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "CosPGD", "sec_num": null}, {"text": "The code for the functions used for generating adversarial samples using CosPGD and other considered adversarial attacks in the main paper is available at https://github.com/shashankskagnihotri/cospgd.Additionally, we provide sample code demonstrating the usage of the packages for a UNet-like architecture with detailed instructions at https://github.com/shashankskagnihotri/cospgd.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3.1. CODE FOR THE ATTACK", "sec_num": null}, {"text": "Following the work by Teed & Deng (2020) , f 1 -all is calculated by averaging out over all the predicted optical flows. out is calculated using Equation ( 12),Where, mag = f low ground truth 2 and epe is the Euclidean distance between the two vectors. In the following, we provide extra results on semantic segmentation with UNet on the Cityscapes dataset.", "cite_spans": [{"start": 22, "end": 40, "text": "Teed & Deng (2020)", "ref_id": "BIBREF76"}], "ref_spans": [], "eq_spans": [], "section": "A.3.2. CALCULATING EPE-F1-ALL", "sec_num": null}, {"text": "In this evaluation, we use a UNet architecture (<PERSON><PERSON> et al., 2015) with a ConvNeXt tiny encoder (<PERSON> et al., 2022) . We extend the implementation from (username: mberkay0, 2023)(www.github.com) to implement CosPGD, PGD, and SegPGD non-targeted l ∞ -norm and l 2 -norm attacks.We do these evaluations on the Cityscapes dataset (<PERSON><PERSON><PERSON> et al., 2016) . Cityscapes contains a total of 5000 high-quality images and pixel-wise annotations for urban scene understanding. The dataset is split into 2975, 500, and 1525 images for training, validation, and testing respectively. The model is trained on the test split and attacks are evaluated on the validation split.", "cite_spans": [{"start": 47, "end": 73, "text": "(<PERSON><PERSON> et al., 2015)", "ref_id": "BIBREF63"}, {"start": 103, "end": 121, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF49"}, {"start": 333, "end": 354, "text": "(<PERSON><PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "B.5.1. IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "In Figure 9 , we report results from the comparison of non-targeted CosPGD to PGD and SegPGD attacks across iterations and across l p -norm constraints: l ∞ -norm and l 2 -norm using UNet architecture with a ConvNeXt tiny encoder on Cityscapes validation dataset. For the l ∞ -norm constraint, we use the same α = 0.01 and ϵ ≈ 8 255 as in all previous evaluations. For the l 2 -norm constraint we follow common work (<PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2023) and use the same ϵ for CosPGD, SegPGD, and PGD i.e. ϵ ≈{ 64 255 , 128 255 } and α 0.2}. SegPGD has been proposed as an l ∞ -norm constrained attack. We extend it to the l 2 -norm constraint merely for complete comparison and curiosity.We observe in Figure 9 that CosPGD is a significantly stronger attack than both PGD and SegPGD, across iterations and l p -norm constraints, and α and ϵ values. Even at low attack iterations, it outperforms previous methods significantly, making it particularly efficient. Especially as an l 2 -norm constrained attack, as shown before in Figure 10 for DeepLabV3 on PASCAL VOC 2012 dataset and discussed before in Section 5.2, as attack iterations increase, CosPGD can increase the performance gap quite significantly.", "cite_spans": [{"start": 416, "end": 436, "text": "(<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": null}, {"start": 437, "end": 455, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF79"}], "ref_spans": [{"start": 10, "end": 11, "text": "9", "ref_id": null}, {"start": 712, "end": 713, "text": "9", "ref_id": null}, {"start": 1037, "end": 1039, "text": "10", "ref_id": null}], "eq_spans": [], "section": "B.5.2. EXPERIMENTAL RESULTS AND DISCUSSION", "sec_num": null}, {"text": "Further, we provide additional experimental results and ablation studies using DeepLabV3 for semantic segmentation on the PASCAL VOC 2012 validation dataset. B.6.1. l 2 -NORM CONSTRAINED ADVERSARIAL ATTACKS Further in Figure 10 , we report l 2 -norm constrained attack evaluations on commonly used (<PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2023) values of ϵ ≈{ 64 255 , 128 255 } and α ={0.1, 0.2}. Additionally, in Table 10 we provide comparison to C&W (Carlini & Wagner, 2017) and other l 2 -norm constrained adversarial attacks with α=0.2 and epsilon ≈ 128 255 on PASCAL VOC 2012 validation dataset using DeepLabV3 with a ResNet50 backbone. B.6.2. l ∞ -NORM CONSTRAINED ADVERSARIAL ATTACKS Following, we ablate over the attack step size α for the l ∞ -norm constrained adversarial attacks and report the findings in Figure 11 . We consider α ∈ {0.005, 0.01, 0.02, 0.04, 0.1}. We can observe that the scaling in CosPGD ensures less susceptibility to the choice of step size given that it is set small enough (α ≤ ϵ). In our work, we use step size α=0.01 to maintain consistency with previous work (<PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON> et al., 2022) .", "cite_spans": [{"start": 298, "end": 318, "text": "(<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": null}, {"start": 319, "end": 337, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF79"}, {"start": 446, "end": 470, "text": "(Carlini & Wagner, 2017)", "ref_id": "BIBREF8"}, {"start": 1091, "end": 1113, "text": "(<PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF47"}, {"start": 1114, "end": 1130, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}], "ref_spans": [{"start": 225, "end": 227, "text": "10", "ref_id": null}, {"start": 414, "end": 416, "text": "10", "ref_id": null}, {"start": 818, "end": 820, "text": "11", "ref_id": null}], "eq_spans": [], "section": "B.6. Ablation on Attack Step Size α", "sec_num": null}, {"text": "Here we report the quantitative results that have already been presented in the main paper in Figures 3in tabular form. For the results reported in Figure 3 , we report the results in tables 11. Here we observe that at low attack iterations (iterations=3) SegPGD implies that PSPNet is more adversarially robust than both DeepLabV3. However, after more attack iterations (iterations ≥ 5), SegPGD correctly implies that DeepLabV3 is more robust than PSPNet. Contrary to this, CosPGD even at (<PERSON> et al., 2022) as the state-of-the-art networks for image restoration tasks. The \"Baseline network\" is significantly more robust than the NAFNet and thus the performance of the Baseline network against CosPGD attack is at par with its performance against PGD. However, PGD indicates at low attack iterations (iterations ≤ 10) that NAFNet is more robust than \"Baseline network\" and only after 20 attack iterations its correctly indicates that NAFNet is less robust. However, CosPGD is able to draw this conclusion at merely 3 attack iterations.", "cite_spans": [{"start": 490, "end": 509, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF9"}], "ref_spans": [{"start": 155, "end": 156, "text": "3", "ref_id": null}], "eq_spans": [], "section": "B.7. Tabular Results", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "A highquality denoising dataset for smartphone cameras", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["S"], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "2018 IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "1692--1700", "other_ids": {"DOI": ["10.1109/CVPR.2018.00182"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. S. A high- quality denoising dataset for smartphone cameras. In 2018 IEEE/CVF Conference on Computer Vision and Pat- tern Recognition, pp. 1692-1700, 2018. doi: 10.1109/ CVPR.2018.00182.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "On the unreasonable vulnerability of transformers for image restoration -and an easy fix", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": ["V"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> On the unreasonable vul- nerability of transformers for image restoration -and an easy fix, 2023a.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Improving stability during upsampling-on the importance of spatial context", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.17524"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Improving stability during upsampling-on the importance of spatial context. arXiv preprint arXiv:2311.17524, 2023b.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Square attack: a query-efficient black-box adversarial attack via random search", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Flammarion", "suffix": ""}, {"first": "M", "middle": [], "last": "Hein", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "484--501", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> attack: a query-efficient black-box adversarial attack via random search. In European conference on computer vision, pp. 484-501. Springer, 2020.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "On the robustness of semantic segmentation models to adversarial attacks", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": ["H S"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, P. H. S. On the ro- bustness of semantic segmentation models to adversar- ial attacks, 2017. URL https://arxiv.org/abs/ 1711.09856.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Analysis of explainers of black box deep neural networks for computer vision: A survey", "authors": [{"first": "V", "middle": [], "last": "Buhrmester", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Arens", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, M. Analysis of ex- plainers of black box deep neural networks for computer vision: A survey, 2019. URL https://arxiv.org/ abs/1911.12116.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "A naturalistic open source movie for optical flow evaluation", "authors": [{"first": "D", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["J"], "last": "Black", "suffix": ""}], "year": 2012, "venue": "European Conf. on Computer Vision (ECCV)", "volume": "7577", "issue": "", "pages": "611--625", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, M<PERSON>. A naturalistic open source movie for optical flow evaluation. In <PERSON><PERSON> et al. (Eds.) (ed.), European Conf. on Computer Vision (ECCV), Part IV, LNCS 7577, pp. 611- 625. Springer-Verlag, October 2012.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Towards evaluating the robustness of neural networks", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "2017 ieee symposium on security and privacy (sp)", "volume": "", "issue": "", "pages": "39--57", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. Towards evaluating the robust- ness of neural networks. In 2017 ieee symposium on security and privacy (sp), pp. 39-57. IEEE, 2017.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Simple baselines for image restoration", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. Simple baselines for image restoration, 2022.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Rethinking atrous convolution for semantic image segmentation", "authors": [{"first": "L.-C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> Rethinking atrous convolution for semantic image seg- mentation, 2017.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "The cityscapes dataset for semantic urban scene understanding", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Enzweiler", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "U", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, U<PERSON>, <PERSON>, S., and <PERSON><PERSON><PERSON>, B. The cityscapes dataset for semantic urban scene under- standing, 2016.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Reliable evaluation of adversarial robustness with an ensemble of diverse parameter-free attacks", "authors": [{"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Hein", "suffix": ""}], "year": 2020, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> evaluation of adversarial robustness with an ensemble of diverse parameter-free attacks. In ICML, 2020.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Robustbench: a standardized adversarial robustness benchmark", "authors": [{"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Flammarion", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Mittal", "suffix": ""}, {"first": "M", "middle": [], "last": "Hein", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: a standardized adversarial robustness benchmark. CoRR, abs/2010.09670, 2020. URL https://arxiv.org/ abs/2010.09670.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Robustbench: a standardized adversarial robustness benchmark", "authors": [{"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Flammarion", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Mittal", "suffix": ""}, {"first": "M", "middle": [], "last": "Hein", "suffix": ""}], "year": 2021, "venue": "Thirty-fifth Conference on Neural Information Processing Systems Datasets and Benchmarks Track (Round 2)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: a standardized adversarial robustness benchmark. In Thirty-fifth Conference on Neural Infor- mation Processing Systems Datasets and Benchmarks Track (Round 2), 2021. URL https://openreview. net/forum?id=SSKZPJCt7B.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Robust semantic segmentation: Strong adversarial attacks and fast training of robust models", "authors": [{"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Hein", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> semantic segmentation: Strong adversarial attacks and fast training of robust models, 2023. URL https://arxiv.org/ abs/2306.12941.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Boosting adversarial attacks with momentum", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Su", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Hu", "suffix": ""}, {"first": "J", "middle": [], "last": "Li", "suffix": ""}], "year": 2018, "venue": "2018 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "9185--9193", "other_ids": {"DOI": ["10.1109/CVPR.2018.00957"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>ing adversarial attacks with momentum. In 2018 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pp. 9185-9193, Los Alamitos, CA, USA, jun 2018. IEEE Computer Society. doi: 10.1109/CVPR.2018.00957. URL https://doi.ieeecomputersociety.org/ 10.1109/CVPR.2018.00957.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Flownet: Learning optical flow with convolutional networks", "authors": [{"first": "A", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Il<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "¸", "middle": [], "last": "Ha<PERSON><PERSON>rbas", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "Smagt", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Brox", "suffix": ""}, {"first": "T", "middle": [], "last": "", "suffix": ""}], "year": 2015, "venue": "IEEE International Conference on Computer Vision (ICCV)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, v<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, T. Flownet: Learning optical flow with convolutional networks. In IEEE International Con- ference on Computer Vision (ICCV), 2015. URL http://lmb.informatik.uni-freiburg. de/Publications/2015/DFIB15.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "The PASCAL Visual Object Classes Challenge", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["K I"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Winn", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. The PASCAL Visual Object Classes Challenge 2012 (VOC2012) Results. http://www.pascal- network.org/challenges/VOC/voc2012/workshop/index.html, 2012.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Flownet: Learning optical flow with convolutional networks", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "E", "middle": [], "last": "Il<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "¸", "middle": [], "last": "Ha<PERSON><PERSON>rbas", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Brox", "suffix": ""}, {"first": "T", "middle": [], "last": "", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, D<PERSON>, and <PERSON><PERSON><PERSON>, T. Flownet: Learning optical flow with convolutional networks, 2015. URL https://arxiv.org/abs/ 1504.06852.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Generating targeted adversarial attacks and assessing their effectiveness in fooling deep neural networks", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Mandal", "suffix": ""}], "year": 2022, "venue": "2022 IEEE International Conference on Signal Processing and Communications (SPCOM)", "volume": "", "issue": "", "pages": "1--5", "other_ids": {"DOI": ["10.1109/SPCOM55316.2022.9840784"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, S<PERSON>, <PERSON>, <PERSON>, <PERSON>, S., and Mandal, S. Gener- ating targeted adversarial attacks and assessing their ef- fectiveness in fooling deep neural networks. In 2022 IEEE International Conference on Signal Processing and Communications (SPCOM), pp. 1-5, 2022. doi: 10.1109/SPCOM55316.2022.9840784.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Are vision language models texture or shape biased and can we steer them? arXiv preprint", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Lukas<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2403.09193"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Are vision language models texture or shape biased and can we steer them? arXiv preprint arXiv:2403.09193, 2024.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Imagenet-trained cnns are biased towards texture; increasing shape bias improves accuracy and robustness", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Bethge", "suffix": ""}, {"first": "F", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Brendel", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, F. A., and <PERSON><PERSON><PERSON>, W. Imagenet-trained cnns are biased towards texture; increasing shape bias im- proves accuracy and robustness, 2018. URL https: //arxiv.org/abs/1811.12231.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Shortcut learning in deep neural networks", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J.-<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Brendel", "suffix": ""}, {"first": "M", "middle": [], "last": "Bethge", "suffix": ""}, {"first": "F", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Nature Machine Intelligence", "volume": "2", "issue": "11", "pages": "665--673", "other_ids": {"DOI": ["10.1038/s42256-020-00257"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>. Short- cut learning in deep neural networks. Nature Machine Intelligence, 2(11):665-673, nov 2020. doi: 10.1038/ s42256-020-00257-z. URL https://doi.org/10. 1038%2Fs42256-020-00257-z.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Explaining and harnessing adversarial examples", "authors": [{"first": "I", "middle": ["J"], "last": "Goodfellow", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Szegedy", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, C. Explaining and harnessing adversarial examples, 2014. URL https: //arxiv.org/abs/1412.6572.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Frequencylowcut pooling-plug & play against catastrophic overfitting", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2204.00491"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>- que<PERSON>lowcut pooling-plug & play against catastrophic overfitting. arXiv preprint arXiv:2204.00491, 2022. CosPGD", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Fix your downsampling asap! be natively more robust via aliasing and spectral artifact free pooling", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>x your down- sampling asap! be natively more robust via aliasing and spectral artifact free pooling, 2023.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "An effective and efficient adversarial attack for evaluating and boosting segmentation robustness", "authors": [{"first": "J", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "Tresp", "suffix": ""}, {"first": "P", "middle": ["H"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Segpgd", "suffix": ""}], "year": 2022, "venue": "European Conference on Computer Vision", "volume": "", "issue": "", "pages": "308--325", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> <PERSON>. Segpgd: An ef- fective and efficient adversarial attack for evaluating and boosting segmentation robustness. In European Confer- ence on Computer Vision, pp. 308-325. Springer, 2022.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Semantic contours from inverse detectors", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Bour<PERSON>v", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2011, "venue": "International Conference on Computer Vision (ICCV)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> contours from inverse detectors. In International Conference on Computer Vision (ICCV), 2011.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Hypercolumns for object segmentation and fine-grained localization", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "2015 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "447--456", "other_ids": {"DOI": ["10.1109/CVPR.2015.7298642"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON> for object segmentation and fine-grained localization. In 2015 IEEE Conference on Computer Vision and Pattern Recognition (CVPR), pp. 447-456, 2015. doi: 10.1109/CVPR.2015.7298642.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Deep residual learning for image recognition", "authors": [{"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ren", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. Deep residual learning for image recognition, 2015. URL https:// arxiv.org/abs/1512.03385.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Benchmarking neural network robustness to common corruptions and perturbations", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON>. <PERSON>marking neural network robustness to common corruptions and pertur- bations, 2019. URL https://arxiv.org/abs/ 1903.12261.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Natural adversarial examples", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Song", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, D. Natural adversarial examples, 2019. URL https://arxiv.org/abs/1907.07174.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Towards improving robustness of compressed cnns", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Brox", "suffix": ""}], "year": null, "venue": "ICML Workshop on Uncertainty and Robustness in Deep Learning (UDL)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, T. Towards improving robustness of compressed cnns. In ICML Work- shop on Uncertainty and Robustness in Deep Learning (UDL), 2021.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Flownet 2.0: Evolution of optical flow estimation with deep networks", "authors": [{"first": "E", "middle": [], "last": "Il<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "T", "middle": [], "last": "Brox", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, T. Flownet 2.0: Evolution of optical flow estimation with deep networks, 2016. URL https: //arxiv.org/abs/1612.01925.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Black-box adversarial attacks with limited queries and information", "authors": [{"first": "A", "middle": [], "last": "Ilyas", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Athalye", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the 35th International Conference on Machine Learning, ICML 2018", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>-box adversarial attacks with limited queries and information. In Proceedings of the 35th International Conference on Machine Learning, ICML 2018, July 2018. URL https: //arxiv.org/abs/1804.08598.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Adversarial example generation with syntactically controlled paraphrase networks", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the 2018 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies", "volume": "1", "issue": "", "pages": "1875--1885", "other_ids": {"DOI": ["10.18653/v1/N18-1170"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, L. Ad- versarial example generation with syntactically controlled paraphrase networks. In Proceedings of the 2018 Confer- ence of the North American Chapter of the Association for Computational Linguistics: Human Language Tech- nologies, Volume 1 (Long Papers), pp. 1875-1885, New Orleans, Louisiana, June 2018. Association for Compu- tational Linguistics. doi: 10.18653/v1/N18-1170. URL https://aclanthology.org/N18-1170.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Provably robust multi-label classification against adversarial examples", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Multiguard", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "10150--10163", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, N. <PERSON>: Provably robust multi-label classification against adversarial examples. Advances in Neural Information Processing Systems, 35: 10150-10163, 2022.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Learning to estimate hidden motions with global motion aggregation", "authors": [{"first": "S", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Li", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> Learning to estimate hidden motions with global motion aggregation, 2021.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Spectral distribution aware image generation", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> distribution aware image generation, 2020. URL https://arxiv.org/abs/ 2012.03110.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Internalized biases in fréchet inception distance", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "NeurIPS 2021 Workshop on Distribution Shifts: Connecting Methods and Applications", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. Internalized biases in fréchet incep- tion distance. In NeurIPS 2021 Workshop on Distribution Shifts: Connecting Methods and Applications, 2021.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Optimizing edge detection for image segmentation with multicut penalties", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Ka<PERSON>ost", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "DAGM German Conference on Pattern Recognition", "volume": "", "issue": "", "pages": "182--197", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>- mizing edge detection for image segmentation with mul- ticut penalties. In DAGM German Conference on Pattern Recognition, pp. 182-197. Springer, 2022.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Neural architecture design and robustness: A dataset", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Lukas<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.06712"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>al architec- ture design and robustness: A dataset. arXiv preprint arXiv:2306.06712, 2023a.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Happy people-image synthesis as black-box optimization problem in the discrete latent space of deep generative models", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["C"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Sc<PERSON>ings", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.06684"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> people-image synthesis as black-box optimization problem in the discrete latent space of deep generative models. arXiv preprint arXiv:2306.06684, 2023b.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Testing robustness against unforeseen adversaries", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Sun", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Testing robustness against unforeseen ad- versaries, 2019. URL https://arxiv.org/abs/ 1908.08016.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Imagenet classification with deep convolutional neural networks", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "Advances in Neural Information Processing Systems", "volume": "25", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> <PERSON><PERSON>- genet classification with deep convolutional neural networks. In Pereira, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> (eds.), Advances in Neural Information Processing Systems, volume 25. Curran As- sociates, Inc., 2012. URL https://proceedings. neurips.cc/paper/2012/file/ c399862d3b9d6b76c8436e924a68c45b-Paper. pdf. <PERSON>, <PERSON><PERSON>, Goodfellow, I., and Bengio, S. Adversarial examples in the physical world, 2016. URL https: //arxiv.org/abs/1607.02533.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Adversarial machine learning at scale", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Goodfellow", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"DOI": ["10.48550/arXiv.1611.01236"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, S. Adversarial machine learning at scale, 2017. URL https://doi. org/10.48550/arXiv.1611.01236.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Revisiting stereo depth estimation from a sequence-to-sequence perspective with transformers", "authors": [{"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "F", "middle": ["X"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)", "volume": "", "issue": "", "pages": "6197--6206", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Revisiting stereo depth estimation from a sequence-to-sequence perspective with transformers. In Proceedings of the IEEE/CVF Interna- tional Conference on Computer Vision (ICCV), pp. 6197- 6206, October 2021.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "A convnet for the 2020s", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "11976--11986", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. A convnet for the 2020s. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 11976-11986, 2022.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Learning where to look-generative nas is surprisingly efficient", "authors": [{"first": "J", "middle": [], "last": "Lukas<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "European Conference on Computer Vision", "volume": "", "issue": "", "pages": "257--273", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Learning where to look-generative nas is surprisingly efficient. In European Conference on Computer Vision, pp. 257-273. Springer, 2022.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Improving native cnn robustness with filter frequency regularization", "authors": [{"first": "J", "middle": [], "last": "Lukas<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, M. <PERSON>- proving native cnn robustness with filter frequency regu- larization. Transactions on Machine Learning Research, 2023a.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "An evaluation of zero-cost proxies-from neural architecture performance prediction to model robustness", "authors": [{"first": "J", "middle": [], "last": "Lukas<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "DAGM German Conference on Pattern Recognition", "volume": "", "issue": "", "pages": "624--638", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. An evaluation of zero-cost proxies-from neural architecture performance prediction to model robustness. In DAGM German Con- ference on Pattern Recognition, pp. 624-638. Springer, 2023b.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Towards deep learning models resistant to adversarial attacks", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Towards deep learning models resistant to ad- versarial attacks, 2017. URL https://arxiv.org/ abs/1706.06083.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "A large dataset to train convolutional networks for disparity, optical flow, and scene flow estimation", "authors": [{"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Il<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "T", "middle": [], "last": "Brox", "suffix": ""}], "year": 2016, "venue": "IEEE International Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1512.02134"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, T. A large dataset to train convolutional networks for disparity, op- tical flow, and scene flow estimation. In IEEE International Conference on Computer Vision and Pattern Recognition (CVPR), 2016. URL http://lmb.informatik.uni-freiburg. de/Publications/2016/MIFDB16. arXiv:1512.02134.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Spring: A high-resolution high-detail dataset and benchmark for scene flow, optical flow and stereo", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "4981--4991", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, A. Spring: A high-resolution high-detail dataset and benchmark for scene flow, optical flow and stereo. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 4981-4991, 2023.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Object scene flow for autonomous vehicles", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> Object scene flow for autonomous vehicles. In Conference on Computer Vision and Pattern Recognition (CVPR), 2015.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Deepfool: a simple and accurate method to fool deep neural networks", "authors": [{"first": "S.-M", "middle": [], "last": "Moosavi-<PERSON><PERSON><PERSON>oli", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>- fool: a simple and accurate method to fool deep neural networks, 2015. URL https://arxiv.org/abs/ 1511.04599.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Textattack: A framework for adversarial attacks, data augmentation, and adversarial training in nlp", "authors": [{"first": "J", "middle": ["X"], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Lifland", "suffix": ""}, {"first": "J", "middle": ["Y"], "last": "<PERSON>o", "suffix": ""}, {"first": "J", "middle": [], "last": "Grigsby", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Qi", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>attack: A framework for adversarial attacks, data augmentation, and adversarial training in nlp, 2020. URL https://arxiv.org/abs/2005.05909.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Deep multi-scale convolutional neural network for dynamic scene deblurring", "authors": [{"first": "S", "middle": [], "last": "Nah", "suffix": ""}, {"first": "T", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": ["M"], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, K. M. Deep multi-scale con- volutional neural network for dynamic scene deblurring. In CVPR, July 2017.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "A certified radius-guided attack framework to image segmentation models", "authors": [{"first": "W", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.02693"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, B. A certified radius-guided attack framework to image segmentation models. arXiv preprint arXiv:2304.02693, 2023.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Optical flow estimation using a spatial pyramid network", "authors": [{"first": "A", "middle": [], "last": "Ra<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["J"], "last": "Black", "suffix": ""}], "year": 2017, "venue": "Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, M<PERSON> J. Optical flow estimation using a spatial pyramid network. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, 2017.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Semantically equivalent adversarial rules for debugging NLP models", "authors": [{"first": "M", "middle": ["T"], "last": "Ribeiro", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics", "volume": "", "issue": "", "pages": "856--865", "other_ids": {"DOI": ["10.18653/v1/P18-1079"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> cally equivalent adversarial rules for debugging NLP models. In Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Vol- ume 1: Long Papers), pp. 856-865, Melbourne, Aus- tralia, July 2018. Association for Computational Lin- guistics. doi: 10.18653/v1/P18-1079. URL https: //aclanthology.org/P18-1079.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "U-net: Convolutional networks for biomedical image segmentation", "authors": [{"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Brox", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, T. U-net: Convolu- tional networks for biomedical image segmentation, 2015. URL https://arxiv.org/abs/1505.04597.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Decoupling direction and norm for efficient gradient-based l2 adversarial attacks and defenses", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": ["G"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": ["B"], "last": "Ayed", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, L. S., <PERSON><PERSON>, I. <PERSON>., <PERSON><PERSON>, R., and <PERSON><PERSON>, <PERSON><PERSON> Decoupling direction and norm for efficient gradient-based l2 adversarial attacks and defenses, 2019.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "Proximal splitting adversarial attack for semantic segmentation", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Ayed", "suffix": ""}], "year": 2023, "venue": "2023 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "20524--20533", "other_ids": {"DOI": ["10.1109/CVPR52729.2023.01966"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Proximal splitting adversarial attack for semantic segmentation. In 2023 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pp. 20524-20533, Los Alamitos, CA, USA, jun 2023. IEEE Computer Society. doi: 10.1109/CVPR52729.2023.01966. URL https://doi.ieeecomputersociety.org/", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "Detection defenses: An empty promise against adversarial patch attacks on optical flow", "authors": [{"first": "E", "middle": [], "last": "Scheurer", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision", "volume": "", "issue": "", "pages": "6489--6498", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> defenses: An empty promise against adversar- ial patch attacks on optical flow. In Proceedings of the IEEE/CVF Winter Conference on Applications of Com- puter Vision, pp. 6489-6498, 2024.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "Attacking motion estimation with adversarial snow", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.11242"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Attacking mo- tion estimation with adversarial snow. arXiv preprint arXiv:2210.11242, 2022a.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "A perturbationconstrained adversarial attack for evaluating the robustness of optical flow", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "European Conference on Computer Vision", "volume": "", "issue": "", "pages": "183--200", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, A. A perturbation- constrained adversarial attack for evaluating the robust- ness of optical flow. In European Conference on Com- puter Vision, pp. 183-200. Springer, 2022b.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "Distracting downpour: Adversarial weather attacks for motion estimation", "authors": [{"first": "J", "middle": [], "last": "Cospg<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "10106--10116", "other_ids": {}, "num": null, "urls": [], "raw_text": "CosPG<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> Distracting down- pour: Adversarial weather attacks for motion estimation. In Proceedings of the IEEE/CVF International Confer- ence on Computer Vision, pp. 10106-10116, 2023.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "Towards understanding adversarial robustness of optical flow networks", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Brox", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "8916--8924", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Towards understand- ing adversarial robustness of optical flow networks. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 8916-8924, 2022.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "Differentiable sensor layouts for end-to-end learning of task-specific camera parameters", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.14736"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, A. Differentiable sensor layouts for end-to-end learning of task-specific camera parameters. arXiv preprint arXiv:2304.14736, 2023.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "Pwc-net: Cnns for optical flow using pyramid, warping, and cost volume", "authors": [{"first": "D", "middle": [], "last": "Sun", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M.-<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, J. <PERSON>-net: Cnns for optical flow using pyramid, warping, and cost volume, 2018.", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "Local aggressive adversarial attacks on 3d point cloud", "authors": [{"first": "Y", "middle": [], "last": "Sun", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. Local aggressive adversarial attacks on 3d point cloud, 2021. URL https: //arxiv.org/abs/2105.09090.", "links": null}, "BIBREF75": {"ref_id": "b75", "title": "Intriguing properties of neural networks", "authors": [{"first": "C", "middle": [], "last": "Szegedy", "suffix": ""}, {"first": "W", "middle": [], "last": "Zaremba", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Goodfellow", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, I<PERSON>, and <PERSON>, <PERSON>. Intriguing properties of neural networks, 2014.", "links": null}, "BIBREF76": {"ref_id": "b76", "title": "Raft: Recurrent all-pairs field transforms for optical flow", "authors": [{"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "mberkay0, <PERSON><PERSON> <PERSON><PERSON> mberkay0/pretrainedbackbones-unet", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>: Recurrent all-pairs field trans- forms for optical flow, 2020. URL https://arxiv. org/abs/2003.12039. username: mberkay0, B. M. mberkay0/pretrained- backbones-unet. https://github.com/ mberkay0/pretrained-backbones-unet, 2023.", "links": null}, "BIBREF77": {"ref_id": "b77", "title": "Multiclass asma vs targeted pgd attack in image segmentation", "authors": [{"first": "J", "middle": [], "last": "Vo", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, S. Multiclass asma vs targeted pgd attack in image segmentation, 2022. URL https: //arxiv.org/abs/2208.01844.", "links": null}, "BIBREF78": {"ref_id": "b78", "title": "Image quality assessment: from error visibility to structural similarity", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Bovik", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2004, "venue": "IEEE Transactions on Image Processing", "volume": "13", "issue": "4", "pages": "600--612", "other_ids": {"DOI": ["10.1109/TIP.2003.819861"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, E. Image quality assessment: from error visibility to structural similarity. IEEE Transactions on Image Processing, 13 (4):600-612, 2004. doi: 10.1109/TIP.2003.819861.", "links": null}, "BIBREF79": {"ref_id": "b79", "title": "Better diffusion models further improve adversarial training", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Yan", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Better diffusion models further improve adversarial training, 2023.", "links": null}, "BIBREF80": {"ref_id": "b80", "title": "Targeted adversarial perturbations for monocular depth prediction", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Cicek", "suffix": ""}, {"first": "S", "middle": [], "last": "Soatto", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, S. Targeted adversarial per- turbations for monocular depth prediction. In Advances in neural information processing systems, 2020a.", "links": null}, "BIBREF81": {"ref_id": "b81", "title": "Fast is better than free: Revisiting adversarial training", "authors": [{"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Rice", "suffix": ""}, {"first": "J", "middle": ["Z"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, J<PERSON>. Fast is better than free: Revisiting adversarial training, 2020b. URL https: //arxiv.org/abs/2001.03994.", "links": null}, "BIBREF82": {"ref_id": "b82", "title": "Lessons and insights from creating a synthetic optical flow benchmark", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["J"], "last": "Black", "suffix": ""}], "year": null, "venue": "ECCV Workshop on Unsolved Problems in Optical Flow and Stereo Estimation, Part II", "volume": "7584", "issue": "", "pages": "168--177", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> and insights from creating a synthetic optical flow benchmark. In <PERSON><PERSON> et al. (Eds.) (ed.), ECCV Workshop on Unsolved Problems in Optical Flow and Stereo Estimation, Part II, LNCS 7584, pp. 168-177.", "links": null}, "BIBREF84": {"ref_id": "b84", "title": "Unified perceptual parsing for scene understanding", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}], "year": 2018, "venue": "European Conference on Computer Vision", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. Unified perceptual parsing for scene understanding. In European Conference on Computer Vision. Springer, 2018.", "links": null}, "BIBREF85": {"ref_id": "b85", "title": "Feature denoising for improving adversarial robustness", "authors": [{"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Yuille", "suffix": ""}, {"first": "K", "middle": [], "last": "He", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> Feature denoising for improving adversarial robustness, 2019.", "links": null}, "BIBREF86": {"ref_id": "b86", "title": "Segformer: Simple and efficient design for semantic segmentation with transformers", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in neural information processing systems", "volume": "34", "issue": "", "pages": "12077--12090", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Simple and efficient design for semantic segmentation with transformers. Advances in neural information processing systems, 34:12077-12090, 2021.", "links": null}, "BIBREF87": {"ref_id": "b87", "title": "Aggregated residual transformations for deep neural networks", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Tu", "suffix": ""}, {"first": "K", "middle": [], "last": "He", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>- gregated residual transformations for deep neural net- works, 2016. URL https://arxiv.org/abs/ 1611.05431.", "links": null}, "BIBREF88": {"ref_id": "b88", "title": "Dynamic divide-and-conquer adversarial training for robust semantic segmentation", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "2021 IEEE/CVF International Conference on Computer Vision (ICCV)", "volume": "", "issue": "", "pages": "7466--7475", "other_ids": {"DOI": ["10.1109/ICCV48922.2021.00739"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, J. <PERSON> divide-and-conquer adversarial training for robust semantic segmentation. In 2021 IEEE/CVF International Conference on Com- puter Vision (ICCV), pp. 7466-7475, 2021. doi: 10.1109/ ICCV48922.2021.00739.", "links": null}, "BIBREF89": {"ref_id": "b89", "title": "Efficient transformer for high-resolution image restoration", "authors": [{"first": "S", "middle": ["W"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Arora", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Hay<PERSON>", "suffix": ""}, {"first": "F", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "M.<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, S. <PERSON>., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, F. S., and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>: Efficient transformer for high-resolution image restoration. In CVPR, 2022.", "links": null}, "BIBREF90": {"ref_id": "b90", "title": "3d adversarial attacks beyond point cloud", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>g", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, Y. 3d adversarial attacks be- yond point cloud, 2021. URL https://arxiv.org/ abs/2104.12146.", "links": null}, "BIBREF92": {"ref_id": "b92", "title": "Pyramid scene parsing network", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Shi", "suffix": ""}, {"first": "X", "middle": [], "last": "Qi", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, J. Pyramid scene parsing network. In CVPR, 2017.", "links": null}, "BIBREF93": {"ref_id": "b93", "title": "Scene parsing through ade20k dataset", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Fidler", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Torralba", "suffix": ""}], "year": 2017, "venue": "Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, A. Scene parsing through ade20k dataset. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, 2017.", "links": null}, "BIBREF94": {"ref_id": "b94", "title": "Semantic understanding of scenes through the ade20k dataset", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Fidler", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Torralba", "suffix": ""}], "year": 2019, "venue": "International Journal of Computer Vision", "volume": "127", "issue": "3", "pages": "302--321", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> understanding of scenes through the ade20k dataset. International Journal of Computer Vision, 127(3):302-321, 2019.", "links": null}}, "ref_entries": {"FIGREF0": {"text": "41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "type_str": "figure", "uris": null, "fig_num": null, "num": null}, "FIGREF1": {"text": "Figure 1: Optical flow predictions using RAFT (<PERSON><PERSON> & <PERSON>, 2020) on Sintel (<PERSON> et al., 2012; <PERSON><PERSON><PERSON> et al., 2012) validation. (a) and (b) show two consecutive frames for which the initial optical flow in (d) was predicted. The results of attacking the model with target -→ 0 (c) are depicted in (e) for PGD and (f) for CosPGD. For the same perturbation magnitude and number of iterations, the proposed CosPGD alters the estimated optical flow more strongly and brings it closer to target (c).", "type_str": "figure", "uris": null, "fig_num": "1", "num": null}, "FIGREF2": {"text": "Figure2: Change in pixel-wise image gradients over attack iterations on DeepLabV3 performing semantic segmentation on PASCAL VOC 2012 validation subset. We observe that the absolute difference between gradient values (top) is larger for PGD and increasing for SegPGD, while being stable for CosPGD. Further, CosPGD has fewer changes in gradient direction over attack iterations (bottom) compared to PGD and SegPGD. This shows CosPGD is more stable during optimization compared to PGD and SegPGD.", "type_str": "figure", "uris": null, "fig_num": "2", "num": null}, "FIGREF3": {"text": "Figure 3: CosPGD versus PGD and SegPGD (ℓ ∞ -norm constrained) for semantic segmentation on PASCAL VOC2012 validation set on DeepLabV3 and PSPNet. CosPGD outperforms competing attacks even in early iterations by a large margin. See also Table 11 in Appendix B. pendix B.8), Transfer Attacks (Appendix B.2) including attacks on SAM (<PERSON><PERSON><PERSON> et al., 2023) (Appendix B.4), Attack on Robust Models (Appendix B.3), comparison of CosPGD to recently proposed PCFA for optical flow estimation over various architectures (Appendix C.3) and Image Denoising (Appendix D), are provided in the Appendix, Table1<PERSON><PERSON>ides an overview. Please also refer to the Appendix A.3 for all details on the experimental setup.", "type_str": "figure", "uris": null, "fig_num": "3", "num": null}, "FIGREF4": {"text": "shows the change in gradients (i.e. the absolute distance between gradients in two subsequent iterations) due to PGD, SegPGD and CosPGD over 100 iterations. Both PGD and CosPGD gradients change constantly over time, with PGD having much stronger change. Yet, as expected, the change in gradients of SegPGD increases over the iterations, potentially leading to oscillations in the optimization.", "type_str": "figure", "uris": null, "fig_num": null, "num": null}, "FIGREF5": {"text": "Figure 4: Example predictions of DeepLabV3 on PASCAL VOC 2012 val set after ℓ ∞ PGD, SegPGD, and CosPGD attacks with 40 iters. The ground truth segmentations are given on the left. Both PGD and SegPGD are able to successfully change most of the predicted labels to one of the ground truth labels (here in green). Yet, the region with this label is predicted correctly. Here, only CosPGD also changes the prediction in this region to a third class.", "type_str": "figure", "uris": null, "fig_num": "4", "num": null}, "FIGREF6": {"text": "Figure 6: Comparison of performance of CosPGD to PGD for optical flow estimation overKITTI-2015 (left)  and Sintel (clean → right) validation datasets as ℓ ∞ -norm constrained targeted attacks using RAFT. CosPGD is a stronger targeted attack than PGD for optical flow. We also report these results in Table13in Appendix C.", "type_str": "figure", "uris": null, "fig_num": "6", "num": null}, "FIGREF7": {"text": "Figure 7: Comparing PGD and CosPGD as a targeted ℓ ∞ -norm constrained attack on RAFT using KITTI15 validation set over various iterations. (a) shows the targeted prediction, a -→ 0 , and (d) shows the initial optical flow estimation by the network before adversarial attacks. EPEs between the target and the final prediction are reported, thus lower epe is better. (b) and (c) show flow predictions after PGD attack over 5 and 40 iterations respectively, while figures (e) and (f) show flow predictions after CosPGD attack over 5 and 40 iterations respectively. CosPGD significantly reduces the gap to target (a).", "type_str": "figure", "uris": null, "fig_num": null, "num": null}, "FIGREF8": {"text": "Figure 8: Non-targeted ℓ ∞ -norm constrained CosPGD, PGD, and SegPGD attacks on NAFNet, recently proposed by (<PERSON> et al., 2022) as the state-of-the-art network for image de-blurring on the GoPro dataset.CosPGD significantly outperforms the other attacks. Lower PSNR and SSIM indicate a worse restoration and thus a stronger attack.", "type_str": "figure", "uris": null, "fig_num": "8", "num": null}, "FIGREF9": {"text": "Section B.1.2: We report an ablation study over multiple ϵ values for ℓ ∞ -norm bounded attacks -Section B.2: We provide evaluations on transferring adversarial attacks between a DeepLabV3 and a PSPNet model on PASCALVOC2012 dataset. -Section B.3: We report the performance of adversarial attacks against some SotA defense methods. -Section B.4: Here we report transfer attacks from a DeepLabV3 to Segment Anything Model (SAM) (<PERSON><PERSON><PERSON> et al., 2023). -Section B.5: We provide extra l ∞ -norm and l 2 -norm constrained non-targeted adversarial attack results from Semantic Segmentation using the UNet architecture with ConvNeXt backbone on the CityScapes dataset (<PERSON><PERSON><PERSON> et al., 2016). -Section B.6: We provide an ablation study on attack step size α and ϵ for l 2 -norm bounded for non-targeted adversarial attack results from Semantic Segmentation using DeepLabV3 on the PASCAL VOC 2012 dataset. -Section B.6.2: We provide an ablation study on attack step size α for l ∞ -norm bounded for non-targeted adversarial attack results from Semantic Segmentation using DeepLabV3 on the PASCAL VOC 2012 dataset. -Section B.7: We report results from Figure 3 in a tabular form. -Section B.8: We report the results of adversarial training for semantic segmentation. • Section C: Optical Flow Additional Results: -Section C.1: We report results from Figure 6 in a tabular form. -Section C.2: We provide extra results comparing CosPGD to PGD as a l ∞ -norm constrained non-targeted adversarial attack for optical flow estimation. -Section C.3: We provide a comparison to the l 2 -constrained PCFA(Schmalfuss et al., 2022b), which is a dedicated attack for optical flow.", "type_str": "figure", "uris": null, "fig_num": null, "num": null}, "FIGREF10": {"text": "Figure 9: Comparing non-targeted CosPGD to PGD and SegPGD attacks across iterations and l p -norm constraints, and α and ϵ values using UNet architecture with a ConvNeXt tiny encoder on Cityscapes validation dataset. CosPGD significantly outperforms previous methods by a large margin, even at few attack iterations.", "type_str": "figure", "uris": null, "fig_num": "9", "num": null}, "FIGREF12": {"text": "Figure 10: Comparing CosPGD to PGD and SegPGD across iterations as l 2 -norm constrained attacks, and across α and ϵ values using DeepLabV3 architecture with a ResNet50 on PASCAL VOC 2012 validation dataset. Again, CosPGD outperforms previous attacks be a large margin at all attack iterations.", "type_str": "figure", "uris": null, "fig_num": "1011", "num": null}, "FIGREF13": {"text": "Figure 12: DeepLabV3 adversarially trained using different adversarial attacks for 3 iterations during training using 50% of the minibatch for generating adversarial samples. All checkpoints are evaluated against 10 attack iterations of the respective attacks. We observe that the model trained with CosPGD outperforms all other adversarial training methods considered against all attacks.", "type_str": "figure", "uris": null, "fig_num": "12", "num": null}, "FIGREF14": {"text": "Figure 14: An extension to Figure 6. Comparison of performance of CosPGD to PGD for optical flow estimation overKITTI-2015 (left)  and Sintel (clean → left and final → right) validation datasets as ℓ ∞ -norm constrained targeted attacks using RAFT. CosPGD is a stronger targeted attack than PGD for optical flow. We also report these results in Table13in Appendix C.", "type_str": "figure", "uris": null, "fig_num": "14", "num": null}, "FIGREF15": {"text": "Figure 15: Comparing CosPGD and PGD as l ∞ -norm constrained non-targeted attacks for optical flow estimation using RAFT on KITTI 2015 validation dataset.", "type_str": "figure", "uris": null, "fig_num": "15", "num": null}, "FIGREF16": {"text": "Figure 16: Comparing change in pixel-wise epe values w.r.t. initial epe values after 40 iterations of PGD and CosPGD as non-targeted ℓ ∞ -norm constrained attacks on RAFT using KITTI15 validation set. The values for each image are: |epe adv -epe initial | max(epe adv ) where epe adv & epe initial are pixel-wise epe values of the final adversarial sample and the initial nonattacked image, respectively.", "type_str": "figure", "uris": null, "fig_num": "16", "num": null}, "FIGREF17": {"text": "Figure 18: Comparison of PCFA and CosPGD when using -→ 0 as the target. A lower AEE is w.r.t. Target and a higher AEE w.r.t. initial indicate a stronger attack.", "type_str": "figure", "uris": null, "fig_num": null, "num": null}, "FIGREF18": {"text": "", "type_str": "figure", "uris": null, "fig_num": null, "num": null}, "FIGREF19": {"text": "Figure 19: Comparison of PCFA and CosPGD when using negative flow as the target. A lower AEE is w.r.t. Target and a higher AEE w.r.t. initial indicate a stronger attack.", "type_str": "figure", "uris": null, "fig_num": null, "num": null}, "FIGREF21": {"text": "Figure 21: Comparing CosPGD to PGD and SegPGD as l ∞ -norm constrained non-targeted attacks for the image denoising task using Baseline network (top row) and NAFNet (bottom row) on SSID dataset. A lower value of PSNR and SSIM indicate a stronger attack.", "type_str": "figure", "uris": null, "fig_num": "21", "num": null}, "TABREF0": {"num": null, "text": "Appendix).", "type_str": "table", "content": "<table><tr><td>Ground Truth</td><td>PGD</td><td>SegPGD</td><td>CosPGD</td></tr><tr><td/><td>mIoU= 6.79%</td><td>mIoU= 2.69%</td><td>mIoU= 0.08%</td></tr></table>", "html": null}, "TABREF1": {"num": null, "text": "∞ -norm constrained targeted 40 iterations CosPGD and PGD attacks on RAFT for optical flow estimation over KITTI-2015 validation dataset. A lower epe w.r.t. Target flow is desirable. We observe that CosPGD can reduce the gap to Target for more pixels than the PGD attack. Moreover, the highest epe w.r.t. Target after a CosPGD attack is significantly lower than after a PGD attack.", "type_str": "table", "content": "<table><tr><td>Cumulative Distribution Function</td><td>10 0 6 × 10 1 7 × 10 1 8 × 10 1 9 × 10 1</td><td>0</td><td>50</td><td>epe w.r.t. Target 0 100 150 200 250 300 350 CosPGD PGD</td></tr><tr><td colspan=\"5\">Figure 5: Comparing the distributions of epe w.r.t. Target flow -→ 0 after ℓ</td></tr></table>", "html": null}, "TABREF2": {"num": null, "text": "Look-up table for considered experiments in this appendix.", "type_str": "table", "content": "<table><tr><td>Downstream Task</td><td>Networks</td><td>Dataset</td><td>Study</td><td colspan=\"3\">Non-targeted Attack l∞-norm constraint l2-norm constraint l∞-norm constraint l2-norm constraint Targeted Attack</td></tr><tr><td/><td>DeepLabV3</td><td/><td>various ϵ and α values</td><td>Sec. B.6.1</td><td/><td/></tr><tr><td/><td>PSPNet</td><td>PASCAL VOC 2012, Cityscapes</td><td>Non-targeted Attacks</td><td>Sec. B.6.2</td><td/><td/></tr><tr><td/><td>UNet</td><td/><td>Non-targeted Attacks</td><td/><td/><td/></tr><tr><td>Semantic Segmentation</td><td>SegFormer Robust UPerNet (Croce et al., 2023)</td><td>ADE20K PASCAL VOC 2012</td><td>various ϵ values Performance against Defense Methods</td><td>Sec. B.1.2 Sec. B.3</td><td/><td/></tr><tr><td/><td>Robust PSPNet (Xu et al., 2021)</td><td>PASCAL VOC 2012</td><td>Performance against Robust Models</td><td>Sec. B.3</td><td/><td/></tr><tr><td/><td>DeepLabV3 → SAM</td><td>PASCAL VOC 2012</td><td>Transfer Attack on SAM</td><td>Sec. B.4</td><td/><td/></tr><tr><td/><td>DeepLabV3 → PSPNet</td><td>PASCAL VOC 2012</td><td>Transfer Attacks</td><td>Sec. B.2</td><td/><td/></tr><tr><td/><td>PSPNet → DeepLabV3</td><td>PASCAL VOC 2012</td><td>Transfer Attacks</td><td>Sec. B.2</td><td/><td/></tr><tr><td>Optical Flow Estimation</td><td>RAFT PWCNet, GMA, SpyNet</td><td>KITTI 2015, Sintel (clean and final)</td><td>Targeted Attacks Comparison to PCFA</td><td>Sec. C.2</td><td>Sec. C</td><td>Sec. C.3</td></tr><tr><td>Image Deblurring</td><td>Restormer, Baseline net, NAFNet</td><td>GoPro</td><td>Non-targeted Attacks</td><td>Sec. D.1</td><td/><td/></tr><tr><td>Image Denoising</td><td>Baseline net, NAFNet</td><td>SSID</td><td>Non-targeted Attacks</td><td>Sec. D.2</td><td/><td/></tr><tr><td colspan=\"2\">A.1. Proof of Proposition 4.1</td><td/><td/><td/><td/><td/></tr></table>", "html": null}, "TABREF3": {"num": null, "text": "Comparison of time taken in minutes by different attacks on different downstream tasks for different amount of iterations. The computation times are comparable.", "type_str": "table", "content": "<table><tr><td/><td/><td/><td/><td/><td/><td>Attack iterations</td><td/><td/></tr><tr><td>Task</td><td colspan=\"2\">Network Dataset</td><td>Attack method</td><td>3</td><td>5</td><td>10</td><td>20</td><td>40</td></tr><tr><td/><td/><td/><td/><td colspan=\"5\">Time (mins) Time (mins) Time (mins) Time (mins) Time (mins)</td></tr><tr><td>Semantic Segmenation</td><td>UNet</td><td>PASCAL VOC 2012</td><td>SegPGD CosPGD</td><td>28.73 26.67</td><td>36.33 36.75</td><td>58.72 54.45</td><td>88.93 97.08</td><td>163.15 165.35</td></tr><tr><td>Optical Flow</td><td>RAFT</td><td>KITTI2012</td><td>PGD CosPGD</td><td>5.90 6.00</td><td>7.73 7.85</td><td>12.23 12.15</td><td>20.98 21.03</td><td>37.45 38.28</td></tr><tr><td/><td/><td>Sintel (clean +</td><td>PGD</td><td>69.87</td><td>97.47</td><td>158.28</td><td>297.40</td><td>557.97</td></tr><tr><td/><td/><td>final)</td><td>CosPGD</td><td>73.68</td><td>102.77</td><td>160.40</td><td>287.82</td><td>602.08</td></tr><tr><td colspan=\"2\">B. Semantic Segmentation</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"9\">Following we provide additional Semantic Segmentaion evaluations, including study on different ϵ values, different α values,</td></tr><tr><td colspan=\"5\">using different tasks and transfer attacks on SAM using a DeepLabV3.</td><td/><td/><td/><td/></tr><tr><td colspan=\"4\">B.1. Semantic Segmentation with SegFormer on ADE20k</td><td/><td/><td/><td/><td/></tr><tr><td colspan=\"3\">B.1.1. IMPLEMENTATION DETAILS</td><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"9\">For experiments with SegFormer (Xie et al., 2021) with MIT-B0 backbone, we use the ADE20k dataset (Zhou et al., 2019).</td></tr><tr><td colspan=\"8\">This dataset has 150 classes and is split into 25,574 training images and 2,000 validation images.</td><td/></tr><tr><td colspan=\"9\">We perform ℓ ∞ -bounded PGD, SegPGD and CosPGD with various ϵ values ∈ { 2 255 , 4 255 , 6 255 , 8 255 , 10 255 , 12 255 }, over various</td></tr><tr><td colspan=\"3\">attack iterations ∈ {3, 5, 10, 20, 40, 100}.</td><td/><td/><td/><td/><td/><td/></tr></table>", "html": null}, "TABREF4": {"num": null, "text": "Attacking SegFormer with a MIT-B0 backbone using ADE20K with different ℓ ∞ bounded ϵ values and with different adversarial attacks. ∞ -norm bounded ϵ = 8 255 attacks with α=0.01 for Untargeted Attacks. Note that the chosen attack settings are the default values proposed in SegPGD.", "type_str": "table", "content": "<table><tr><td/><td/><td/><td/><td/><td/><td/><td colspan=\"2\">Attack Iterations</td><td/><td/><td/><td/><td/></tr><tr><td>Attack Method</td><td>ϵ 255 value</td><td>3</td><td/><td>5</td><td/><td>10</td><td/><td>20</td><td/><td>40</td><td/><td>100</td><td/></tr><tr><td/><td/><td colspan=\"12\">mIoU (%) mAcc (%) mIoU (%) mAcc (%) mIoU (%) mAcc (%) mIoU (%) mAcc (%) mIoU (%) mAcc (%) mIoU (%) mAcc (%)</td></tr><tr><td>PGD</td><td/><td>8.45</td><td>14.44</td><td>6.62</td><td>11.49</td><td>5.36</td><td>9.45</td><td>4.21</td><td>7.51</td><td>3.8</td><td>6.73</td><td>3.3</td><td>6.12</td></tr><tr><td>SegPGD</td><td>2</td><td>5.80</td><td>10.15</td><td>4.88</td><td>8.68</td><td>3.69</td><td>6.56</td><td>2.91</td><td>5.18</td><td>2.41</td><td>4.49</td><td>2.19</td><td>4.02</td></tr><tr><td>CosPGD</td><td/><td>5.37</td><td>10.06</td><td>3.75</td><td>7.26</td><td>2.18</td><td>4.3</td><td>1.87</td><td>3.55</td><td>1.68</td><td>3.01</td><td>1.37</td><td>2.46</td></tr><tr><td>PGD</td><td/><td>5.11</td><td>9.48</td><td>2.94</td><td>5.63</td><td>1.66</td><td>3.34</td><td>1.01</td><td>2.21</td><td>0.79</td><td>1.79</td><td>0.6</td><td>1.38</td></tr><tr><td>SegPGD</td><td>4</td><td>3.29</td><td>6.15</td><td>1.83</td><td>3.7</td><td>0.89</td><td>1.9</td><td>0.47</td><td>1.18</td><td>0.3</td><td>0.86</td><td>0.26</td><td>0.68</td></tr><tr><td>CosPGD</td><td/><td>1.66</td><td>3.45</td><td>0.55</td><td>1.28</td><td>0.09</td><td>0.22</td><td>0.05</td><td>0.09</td><td>0.05</td><td>0.09</td><td>0.04</td><td>0.06</td></tr><tr><td>PGD</td><td/><td>3.97</td><td>7.5</td><td>2.05</td><td>4.1</td><td>1.07</td><td>2.28</td><td>0.67</td><td>1.57</td><td>0.41</td><td>1.14</td><td>0.36</td><td>0.88</td></tr><tr><td>SegPGD</td><td>6</td><td>2.64</td><td>5.10</td><td>1.22</td><td>2.71</td><td>0.47</td><td>1.24</td><td>0.21</td><td>0.7</td><td>0.13</td><td>0.49</td><td>0.09</td><td>0.35</td></tr><tr><td>CosPGD</td><td/><td>1.11</td><td>2.39</td><td>0.18</td><td>0.52</td><td>0.01</td><td>0.04</td><td>0.0</td><td>0.01</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td></tr><tr><td>PGD</td><td/><td>3.38</td><td>6.48</td><td>1.76</td><td>3.63</td><td>0.82</td><td>1.95</td><td>0.46</td><td>1.28</td><td>0.37</td><td>1.04</td><td>0.2</td><td>0.7</td></tr><tr><td>SegPGD</td><td>8</td><td>2.31</td><td>4.54</td><td>0.90</td><td>2.06</td><td>0.33</td><td>1.03</td><td>0.15</td><td>0.61</td><td>0.09</td><td>0.35</td><td>0.05</td><td>0.28</td></tr><tr><td>CosPGD</td><td/><td>0.98</td><td>2.21</td><td>0.08</td><td>0.25</td><td>0.00</td><td>0.02</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td></tr><tr><td>PGD</td><td/><td>3.29</td><td>6.28</td><td>1.74</td><td>3.58</td><td>0.79</td><td>1.99</td><td>0.47</td><td>1.27</td><td>0.34</td><td>1.01</td><td>0.24</td><td>0.74</td></tr><tr><td>SegPGD</td><td>10</td><td>1.91</td><td>3.88</td><td>0.89</td><td>2.09</td><td>0.32</td><td>0.96</td><td>0.18</td><td>0.65</td><td>0.08</td><td>0.38</td><td>0.05</td><td>0.27</td></tr><tr><td>CosPGD</td><td/><td>0.81</td><td>1.82</td><td>0.11</td><td>0.41</td><td>0.00</td><td>0.01</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td></tr><tr><td>PGD</td><td/><td>3.16</td><td>5.95</td><td>1.49</td><td>2.98</td><td>0.72</td><td>1.79</td><td>0.45</td><td>1.27</td><td>0.31</td><td>0.93</td><td>0.24</td><td>0.69</td></tr><tr><td>SegPGD</td><td>12</td><td>1.83</td><td>3.77</td><td>1.83</td><td>3.77</td><td>0.26</td><td>0.83</td><td>0.14</td><td>0.6</td><td>0.1</td><td>0.44</td><td>0.04</td><td>0.26</td></tr><tr><td>CosPGD</td><td/><td>0.72</td><td>1.68</td><td>0.08</td><td>0.22</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td></tr><tr><td colspan=\"3\">using SegFormer with ℓ</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr></table>", "html": null}, "TABREF5": {"num": null, "text": "Transfer Attacks on DeepLabV3 and PSPNet using 20 iterations attacks with ℓ ∞ -norm bounded ϵ = 8 255 and α=0.01 using PASCAL VOC 2012 validation dataset.CosPGD optimized on a particular network, also cause a failure in the other. Thus in Table4, we report results for the PASCAL VOC 2012 dataset when attacking PSPNet using DeepLabV3, and vice versa, both with a ResNet50 encoder. We observe that CosPGD is a significantly better attack even in this black-box setting. Here we consider ℓ ∞ -norm bounded ϵ = 8 255 attacks with α=0.01. The benefit of CosPGD over previous methods becomes more significant as the number of attack iterations", "type_str": "table", "content": "<table><tr><td/><td colspan=\"4\">2 255 , 4 255 , 6 255 , 8 255 , 10 255 , 12 255 } attack settings with α=0.01 for Untargeted Attacks</td></tr><tr><td>in Table 3.</td><td/><td/><td/><td/></tr><tr><td>B.2. Evaluating Transfer Attacks</td><td/><td/><td/><td/></tr><tr><td>Attacked Model</td><td>Attacking Model</td><td colspan=\"3\">Attack Method mIoU (%) mAcc (%)</td></tr><tr><td>DeepLabV3 ResNet50</td><td>PSPNet ResNet50</td><td>CosPGD SegPGD</td><td>1.67 1.93</td><td>3.59 5.72</td></tr><tr><td>(Clean mIoU: 76.17)</td><td/><td>PGD</td><td>5.11</td><td>12.75</td></tr><tr><td>PSPNet ResNet50</td><td>DeepLabV3 ResNet50</td><td>CosPGD SegPGD</td><td>1.21 1.77</td><td>3.33 5.62</td></tr><tr><td>(Clean mIoU: 76.78)</td><td/><td>PGD</td><td>4.58</td><td>12.07</td></tr></table>", "html": null}, "TABREF6": {"num": null, "text": "Transfer Attacks from DeepLabV3 on PSPNet over various iterations with ℓ ∞ -norm bounded ϵ = 8 255 and α=0.01 using PASCAL VOC 2012 validation dataset.", "type_str": "table", "content": "<table><tr><td/><td/><td/><td/><td/><td/><td colspan=\"2\">Attack Iterations</td><td/><td/><td/></tr><tr><td>Attacked Model</td><td>Attacking Model</td><td>Attack Method</td><td>3</td><td/><td>10</td><td/><td>20</td><td/><td>40</td><td/></tr><tr><td/><td/><td/><td colspan=\"8\">mIoU (&amp;) mAcc (%) mIoU (&amp;) mAcc (%) mIoU (%) mAcc (%) mIoU (&amp;) mAcc (%)</td></tr><tr><td>PSPNet ResNet50</td><td>DeepLabV3 ResNet50</td><td>CosPGD SegPGD</td><td>9.66 9.92</td><td>19.39 19.79</td><td>2.39 2.40</td><td>5.91 6.67</td><td>1.21 1.77</td><td>3.33 5.62</td><td>1.00 1.23</td><td>2.59 4.40</td></tr><tr><td>(Clean mIoU: 76.78)</td><td/><td>PGD</td><td>14.67</td><td>27.79</td><td>5.56</td><td>13.60</td><td>4.58</td><td>12.07</td><td>4.35</td><td>11.81</td></tr></table>", "html": null}, "TABREF7": {"num": null, "text": "Comparing the \"Robust\" PSPNet from(<PERSON> et al., 2021) against white-box adversarial attacks over different number of iterations. Here, same as(<PERSON> et al., 2021), ϵ = 8 255 and α=0.01. We use the model weights provided by(<PERSON> et al., 2021) in their official GitHub repository.", "type_str": "table", "content": "<table><tr><td/><td colspan=\"2\">Clean Performance</td><td/><td/><td/><td/><td colspan=\"2\">Attack Iterations</td><td/><td/><td/></tr><tr><td>Training Method</td><td/><td/><td>Attack Method</td><td>2</td><td/><td>4</td><td/><td>6</td><td/><td>10</td><td/></tr><tr><td/><td colspan=\"2\">mIoU (%) mAcc (%)</td><td/><td colspan=\"8\">mIoU (%) mAcc (%) mIoU (%) mAcc (%) mIoU (%) mAcc (%) mIoU (%) mAcc (%)</td></tr><tr><td/><td/><td/><td>CosPGD</td><td>9.11</td><td>20.77</td><td>1.56</td><td>5.02</td><td>0.54</td><td>2.03</td><td>0.13</td><td>0.40</td></tr><tr><td>No Defense</td><td>76.90</td><td>84.60</td><td>SegPGD</td><td>10.39</td><td>22.14</td><td>3.86</td><td>9.69</td><td>2.62</td><td>6.97</td><td>1.88</td><td>5.36</td></tr><tr><td/><td/><td/><td>BIM</td><td>18.90</td><td>34.92</td><td>7.59</td><td>18.61</td><td>5.57</td><td>14.98</td><td>4.14</td><td>12.22</td></tr><tr><td/><td/><td/><td>CosPGD</td><td>64.68</td><td>80.13</td><td>42.74</td><td>64.96</td><td>29.17</td><td>52.66</td><td>17.05</td><td>38.75</td></tr><tr><td>SAT (Xu et al., 2021)</td><td>74.78</td><td>83.36</td><td>SegPGD</td><td>66.24</td><td>81.72</td><td>42.71</td><td>65.75</td><td>30.74</td><td>54.31</td><td>20.59</td><td>43.13</td></tr><tr><td/><td/><td/><td>BIM</td><td>69.89</td><td>86.68</td><td>48.62</td><td>67.34</td><td>31.54</td><td>50.80</td><td>20.67</td><td>40.05</td></tr><tr><td/><td/><td/><td>CosPGD</td><td>66.93</td><td>77.60</td><td>50.79</td><td>65.13</td><td>36.12</td><td>53.26</td><td>23.04</td><td>41.02</td></tr><tr><td>DDC-AT (Xu et al., 2021)</td><td>75.98</td><td>84.72</td><td>SegPGD</td><td>67.09</td><td>78.36</td><td>50.89</td><td>65.14</td><td>37.70</td><td>54.48</td><td>25.40</td><td>42.72</td></tr><tr><td/><td/><td/><td>BIM</td><td>74.04</td><td>83.09</td><td>51.57</td><td>65.67</td><td>39.07</td><td>55.97</td><td>26.90</td><td>45.27</td></tr></table>", "html": null}, "TABREF8": {"num": null, "text": "with different fixed attacks in the Segmentation Ensemble Attack (SEA) over different permissible perturbation budgets (ϵ) and attack iterations. Bold results are the strongest attacks, while Underlined results are second strongest. Attacking Robust UPerNet with a ConvNeXt-tiny encoder from(<PERSON><PERSON><PERSON> et al., 2023) with CosPGD for extremely high number of iterations i.e. 1200 iterations with ϵ = 4", "type_str": "table", "content": "<table><tr><td/><td/><td/><td/><td/><td/><td/><td>ϵ</td><td/><td/><td/></tr><tr><td>Attack Used</td><td colspan=\"2\">Optimizer Used Attack Iterations</td><td>4</td><td/><td>8</td><td/><td>255</td><td>12</td><td/><td>16</td></tr><tr><td/><td/><td/><td colspan=\"9\">mIoU (%) mAcc (%) mIoU (%) mAcc (%) mIoU (%) mAcc (%) mIoU (%) mAcc (%)</td></tr><tr><td/><td/><td>10</td><td>64.17</td><td>88.52</td><td>43.73</td><td>76.36</td><td>21.51</td><td/><td>55.27</td><td>11.20</td><td>41.40</td></tr><tr><td/><td/><td>20</td><td>64.15</td><td>88.53</td><td>41.94</td><td>74.89</td><td>16.27</td><td/><td>45.71</td><td>6.54</td><td>24.93</td></tr><tr><td>SEA:</td><td/><td>30</td><td>64.15</td><td>88.51</td><td>40.90</td><td>74.36</td><td>14.79</td><td/><td>42.05</td><td>5.05</td><td>18.31</td></tr><tr><td>only CosPGD (with Softmax) (OURS)</td><td>APGD</td><td>40</td><td>64.13</td><td>88.50</td><td>40.61</td><td>74.08</td><td>14.01</td><td/><td>39.99</td><td>4.80</td><td>16.53</td></tr><tr><td>in (Croce et al., 2020)</td><td/><td>50</td><td>64.10</td><td>88.50</td><td>40.77</td><td>73.97</td><td>13.74</td><td/><td>39.12</td><td>4.30</td><td>14.82</td></tr><tr><td/><td/><td>100</td><td>64.06</td><td>88.48</td><td>39.99</td><td>73.29</td><td>12.67</td><td/><td>35.97</td><td>3.29</td><td>10.69</td></tr><tr><td/><td/><td>300</td><td>64.05</td><td>88.48</td><td>39.52</td><td>72.81</td><td>12.66</td><td/><td>34.63</td><td>2.90</td><td>8.78</td></tr><tr><td/><td/><td>10</td><td>64.48</td><td>88.60</td><td>48.60</td><td>79.47</td><td>31.92</td><td/><td>65.45</td><td>21.59</td><td>53.70</td></tr><tr><td/><td/><td>20</td><td>64.43</td><td>88.59</td><td>46.31</td><td>77.72</td><td>26.37</td><td/><td>57.98</td><td>15.35</td><td>41.19</td></tr><tr><td>SEA:</td><td/><td>30</td><td>64.41</td><td>88.58</td><td>45.78</td><td>77.22</td><td>24.35</td><td/><td>54.46</td><td>13.18</td><td>34.70</td></tr><tr><td>only CosPGD (with Sigmoid)</td><td>APGD</td><td>40</td><td>64.39</td><td>88.58</td><td>45.16</td><td>76.82</td><td>22.89</td><td/><td>52.09</td><td>12.43</td><td>30.88</td></tr><tr><td>in (Croce et al., 2020)</td><td/><td>50</td><td>64.39</td><td>88.58</td><td>44.95</td><td>76.57</td><td>22.54</td><td/><td>50.91</td><td>11.59</td><td>28.78</td></tr><tr><td/><td/><td>100</td><td>64.37</td><td>88.58</td><td>44.40</td><td>76.13</td><td>21.57</td><td/><td>48.74</td><td>10.53</td><td>24.87</td></tr><tr><td/><td/><td>300</td><td>64.37</td><td>88.57</td><td>44.05</td><td>75.96</td><td>21.09</td><td/><td>47.39</td><td>10.23</td><td>22.58</td></tr><tr><td/><td/><td>10</td><td>64.38</td><td>88.66</td><td>44.46</td><td>77.21</td><td>22.17</td><td/><td>58.12</td><td>11.37</td><td>45.04</td></tr><tr><td/><td/><td>20</td><td>64.23</td><td>88.59</td><td>42.46</td><td>75.74</td><td>17.89</td><td/><td>51.40</td><td>8.11</td><td>33.86</td></tr><tr><td>SEA:</td><td/><td>30</td><td>64.21</td><td>88.56</td><td>41.71</td><td>75.09</td><td>16.11</td><td/><td>48.30</td><td>6.61</td><td>28.27</td></tr><tr><td>only SegPGD</td><td>APGD</td><td>40</td><td>64.09</td><td>88.52</td><td>40.85</td><td>74.52</td><td>45.05</td><td/><td>14.84</td><td>5.63</td><td>23.90</td></tr><tr><td>in (Croce et al., 2020)</td><td/><td>50</td><td>64.01</td><td>88.49</td><td>40.46</td><td>74.30</td><td>13.98</td><td/><td>42.97</td><td>4.90</td><td>20.85</td></tr><tr><td/><td/><td>100</td><td>63.95</td><td>88.45</td><td>39.47</td><td>73.54</td><td>12.78</td><td/><td>39.34</td><td>4.04</td><td>16.26</td></tr><tr><td/><td/><td>300</td><td>63.80</td><td>88.41</td><td>38.69</td><td>72.90</td><td>11.27</td><td/><td>35.85</td><td>3.36</td><td>12.17</td></tr><tr><td/><td/><td/><td>255</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td/><td>Attack Method</td><td/><td/><td/><td colspan=\"2\">Optimizer Used</td><td colspan=\"2\">Attack Iterations</td><td colspan=\"3\">ϵ= 4 255 mIoU (%) mAcc (%)</td></tr><tr><td colspan=\"2\">SEA reported by</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr></table>", "html": null}, "TABREF9": {"num": null, "text": "we present the training curves for training DeepLabV3 on the PASCAL VOC2012 training dataset using adversarial training with 50% minibatch being used for generating adversarial samples. All models are evaluated against 10 attack iterations of the respective attack. Predictions using UNet with ConvNeXt backbone on PASCAL VOC2012 validation dataset after 100 iterations adversarial attacks on adversarially trained models. We observe that the models adversarially trained with CosPGD are predicting reasonable masks even after 100 attack iterations, while the model trained with SegPGD is providing much worse results under both SegPGD and CosPGD attacks. Comparison of performance of CosPGD to SegPGD, PGD and C&W as a l 2 -norm constrained attack with α=0.2 and ϵ ≈ 128 255 where applicable for semantic segmentation over PASCAL VOC2012 validation dataset. We observe that CosPGD is a significantly stronger attack compared to all the other attacks for both metrics.", "type_str": "table", "content": "<table><tr><td>CosPGD</td></tr></table>", "html": null}, "TABREF10": {"num": null, "text": "Comparison of performance of CosPGD to SegPGD for semantic segmentation over PASCAL VOC2012 validation dataset. We observe that CosPGD is a significantly stronger attack compared to SegPGD for both metrics and all models.", "type_str": "table", "content": "<table><tr><td/><td/><td/><td/><td/><td/><td/><td colspan=\"2\">Attack iterations</td><td/><td/><td/><td/><td/></tr><tr><td>Network</td><td>Attack method</td><td>3</td><td/><td>5</td><td/><td>10</td><td/><td>20</td><td/><td>40</td><td/><td>100</td><td/></tr><tr><td/><td/><td colspan=\"12\">mIoU(%) mAcc(%) mIoU(%) mAcc(%) mIoU(%) mAcc(%) mIoU(%) mAcc(%) mIoU(%) mAcc(%) mIoU(%) mAcc(%)</td></tr><tr><td>UNet</td><td>SegPGD CosPGD</td><td>12.38 9.67</td><td>32.41 29.46</td><td>7.75 3.71</td><td>25.27 15.89</td><td>4.46 0.61</td><td>18.36 3.39</td><td>2.98 0.06</td><td>14.24 0.38</td><td>2.20 0.03</td><td>11.66 0.16</td><td>1.55 0.01</td><td>8.66 0.04</td></tr><tr><td/><td>PGD</td><td>13.79</td><td>31.91</td><td>7.59</td><td>21.15</td><td>5.44</td><td>16.96</td><td>4.48</td><td>14.78</td><td>3.80</td><td>13.13</td><td>3.72</td><td>13.21</td></tr><tr><td>PSPNet</td><td>SegPGD</td><td>9.19</td><td>23.25</td><td>4.70</td><td>14.25</td><td>2.72</td><td>9.50</td><td>1.82</td><td>7.39</td><td>1.30</td><td>5.77</td><td>0.83</td><td>3.86</td></tr><tr><td/><td>CosPGD</td><td>7.03</td><td>19.73</td><td>2.15</td><td>7.60</td><td>0.408</td><td>1.44</td><td>0.04</td><td>0.11</td><td>0.005</td><td>0.021</td><td>0.0002</td><td>0.0007</td></tr><tr><td/><td>PGD</td><td>10.69</td><td>28.76</td><td>8.00</td><td>25.29</td><td>7.02</td><td>24.05</td><td>6.84</td><td>23.87</td><td>6.79</td><td>23.81</td><td>7.01</td><td>24.13</td></tr><tr><td/><td>BIM</td><td>10.86</td><td>29.39</td><td>7.75</td><td>24.97</td><td>6.95</td><td>24.06</td><td>6.67</td><td>23.52</td><td>6.57</td><td>23.48</td><td>-</td><td>-</td></tr><tr><td>DeepLabV3</td><td>APGD</td><td>13.74</td><td>29.79</td><td>8.67</td><td>22.46</td><td>6.50</td><td>19.82</td><td>6.11</td><td>18.99</td><td>5.30</td><td>17.04</td><td>5.14</td><td>16.72</td></tr><tr><td/><td>SegPGD</td><td>6.76</td><td>19.78</td><td>4.86</td><td>16.49</td><td>3.84</td><td>14.29</td><td>3.31</td><td>12.40</td><td>2.69</td><td>10.81</td><td>2.15</td><td>9.25</td></tr><tr><td/><td>CosPGD</td><td>4.44</td><td>14.97</td><td>1.84</td><td>7.89</td><td>0.69</td><td>3.18</td><td>0.12</td><td>0.48</td><td>0.08</td><td>0.25</td><td>0.005</td><td>0.16</td></tr></table>", "html": null}, "TABREF11": {"num": null, "text": "Evaluating the adversarial performance of models on PASCAL VOC2012 validation dataset that are adversarially trained using PASCAL VOC2012 training dataset. \"Training method\" specifies the adversarial attack used during training, such that \"Clean\" stands for no adversarial attack being used during training. During training, 3 attack iterations were used for all adversarial attacks with α=0.01 and ϵ ≈ 8 255 . These models were evaluated against multiple adversarial attacks denoted by \"Attack method\". We observe that models trained with CosPGD substantially outperform all the other adversarial training methods.", "type_str": "table", "content": "<table><tr><td/><td/><td/><td/><td/><td>Attack iterations</td><td/><td/><td/></tr><tr><td>Network</td><td>Training method</td><td>Attack method</td><td>3</td><td>5</td><td>10</td><td>20</td><td>40</td><td>100</td></tr><tr><td/><td/><td/><td colspan=\"6\">mIoU(%) mAcc(%) mIoU(%) mAcc(%) mIoU(%) mAcc(%) mIoU(%) mAcc(%) mIoU(%) mAcc(%) mIoU(%) mAcc(%)</td></tr><tr><td/><td>Clean</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td/><td/><td>PGD</td><td/><td/><td/><td/><td/><td/></tr><tr><td>UNet</td><td/><td/><td/><td/><td/><td/><td/><td/></tr></table>", "html": null}, "TABREF12": {"num": null, "text": "Comparison of performance of CosPGD to PGD as a targeted attack for optical flow estimation over KITTI15 and Sintel validation datasets using RAFT for different numbers of attack iterations. epe values are compared, with respect to both, the Target i.e.-→ 0 where a lower epe indicates a better attack and Initial flow prediction (optical flow estimated by the model before any adversarial attack) where a higher epe indicates a better attack. CosPGD and PGD perform similarly for a low number of iterations, where CosPGD fits the target slightly better. CosPGD significantly outperforms PGD from the 10 th iteration onwards on both metrics.", "type_str": "table", "content": "<table><tr><td>Attack</td><td/><td/><td colspan=\"2\">KITTI 2015</td><td/><td/><td/><td/><td/><td/><td/><td colspan=\"2\">MPI Sintel</td><td/><td/><td/><td/><td/></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td>clean</td><td/><td/><td/><td/><td/><td>final</td><td/><td/><td/></tr><tr><td>Iterations</td><td colspan=\"18\">SegPGD Target↓ Initial↑ Target↓ Initial↑ Target↓ Initial↑ Target↓ Initial↑ Target↓ Initial↑ Target↓ Initial↑ Target↓ Initial↑ Target↓ Initial↑ Target↓ Initial↑ PGD CosPGD SegPGD PGD CosPGD SegPGD PGD CosPGD</td></tr><tr><td>3</td><td>20.57</td><td>11.28</td><td>20.7</td><td>11.4</td><td>20.6</td><td>11.2</td><td>8.35</td><td>6.83</td><td>8.3</td><td>6.8</td><td>8.1</td><td>6.6</td><td>7.58</td><td>7.52</td><td>7.6</td><td>7.3</td><td>7.5</td><td>7.3</td></tr><tr><td>5</td><td>14.33</td><td>17.75</td><td>14.4</td><td>17.8</td><td>14.3</td><td>17.7</td><td>6.06</td><td>8.97</td><td>6.1</td><td>9.0</td><td>5.8</td><td>8.8</td><td>5.44</td><td>9.43</td><td>5.6</td><td>9.4</td><td>5.2</td><td>9.3</td></tr><tr><td>10</td><td>11.08</td><td>21.36</td><td>10.5</td><td>22.1</td><td>9.0</td><td>23.4</td><td>3.51</td><td>11.16</td><td>3.4</td><td>11.2</td><td>2.9</td><td>11.4</td><td>3.13</td><td>11.32</td><td>3.1</td><td>11.3</td><td>2.6</td><td>11.5</td></tr><tr><td>20</td><td>7.76</td><td>24.55</td><td>8.1</td><td>24.6</td><td>6.5</td><td>25.8</td><td>2.97</td><td>11.61</td><td>2.8</td><td>11.7</td><td>2.0</td><td>12.1</td><td>2.62</td><td>11.7</td><td>2.5</td><td>11.8</td><td>1.6</td><td>12.1</td></tr><tr><td>40</td><td>7.53</td><td>24.89</td><td>7.3</td><td>25.0</td><td>4.8</td><td>27.4</td><td>2.66</td><td>11.8</td><td>2.8</td><td>11.7</td><td>1.6</td><td>12.4</td><td>2.4</td><td>11.83</td><td>2.6</td><td>12.3</td><td>1.3</td><td>12.3</td></tr></table>", "html": null}, "TABREF13": {"num": null, "text": "AEE w.r.t. Target, lower is better AEE w.r.t. Initial, higher is better Figure 17: Comparison of mean and standard deviation of the results using different targets, -→ 0 and negative flow for CosPGD and PCFA. A lower AEE is w.r.t. Target and a higher AEE w.r.t. initial indicate a stronger attack.", "type_str": "table", "content": "<table><tr><td/><td/><td/><td/><td colspan=\"3\">CosPGD</td><td/><td/><td/></tr><tr><td/><td/><td/><td/><td>CosPGD</td><td/><td>PCFA</td><td/><td/><td/></tr><tr><td>AEE w.r.t. Target</td><td>0 5 10 15</td><td>KITTI 2015</td><td>MPI Sintel (clean) Dataset RAFT</td><td>MPI Sintel (final)</td><td>AEE w.r.t. Initial</td><td>0 20 40</td><td>KITTI 2015</td><td>MPI Sintel (clean) Dataset RAFT</td><td>MPI Sintel (final)</td></tr><tr><td>AEE w.r.t. Target</td><td>0 5 10</td><td>KITTI 2015</td><td>MPI Sintel (clean) Dataset PWCNet</td><td>MPI Sintel (final)</td><td>AEE w.r.t. Initial</td><td>0 10 20 30</td><td>KITTI 2015</td><td>MPI Sintel (clean) Dataset PWCNet</td><td>MPI Sintel (final)</td></tr><tr><td>AEE w.r.t. Target</td><td>0 5 10 15</td><td>KITTI 2015</td><td>MPI Sintel (clean) Dataset GMA</td><td>MPI Sintel (final)</td><td>AEE w.r.t. Initial</td><td>0 20 40</td><td>KITTI 2015</td><td>MPI Sintel (clean) Dataset GMA</td><td>MPI Sintel (final)</td></tr><tr><td>AEE w.r.t. Target</td><td>0 5 10 15</td><td>KITTI 2015</td><td>MPI Sintel (clean) Dataset SpyNet</td><td>MPI Sintel (final)</td><td>AEE w.r.t. Initial</td><td>0 5 10 15</td><td>KITTI 2015</td><td>MPI Sintel (clean) Dataset SpyNet</td><td>MPI Sintel (final)</td></tr></table>", "html": null}, "TABREF14": {"num": null, "text": "Comparison of performance of CosPGD to PCFA as a targeted l 2 -norm constrained attack for optical flow estimation over KITTI2015 and Sintel validation datasets using different optical flow models over 3 random seeds. Average epe values are compared, with respect to both, the Target where a lower epe indicates a better attack and Initial flow prediction (optical flow estimated by the model before any adversarial attack) where a higher epe indicates a better attack. We compare over both targets used by(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022b), i.e. zero vector -→ 0 and Negative of the Initial Flow. CosPGD and PCFA performance is very comparable. ± 0.12 28.67 ± 0.17 3.89 ± 0.09 3.89 ± 0.15 47.00 ± 0.40 47.08 ± 0.69 19.22 ± 0.53 19.20 ± 0.57 PWCNet 19.13 ± 0.04 18.96 ± 0.08 3.25 ± 0.08 3.47 ± 0.14 33.13 ± 0.25 33.13 ± 0.26 12.01 ± 0.20 12.02 ± 0.22 RAFT 29.09 ± 0.03 29.17 ± 0.11 3.75 ± 0.05 3.63 ± 0.10 48.83 ± 0.35 48.93 ± 0.29 17.97 ± 0.29 17.81 ± 0.27 SpyNet 9.00 ± 0.01 9.01 ± 0.03 5.31 ± 0.01 5.35 ± 0.06 12.10 ± 0.02 12.08 ± 0.05 16.47 ± 0.03 16.44 ± 0.05 MPI Sintel (clean) GMA 16.87 ± 0.14 16.76 ± 0.11 1.75 ± 0.15 1.85 ± 0.10 29.25 ± 0.38 29.05 ± 0.38 8.58 ± 0.34 8.82 ± 0.37 PWCNet 12.20 ± 0.21 12.18 ± 0.07 4.87 ± 0.17 4.75 ± 0.12 20.57 ± 0.21 20.43 ± 0.21 13.20 ± 0.13 13.21 ± 0.29 RAFT 16.42 ± 0.03 16.46 ± 0.05 1.69 ± 0.04 1.65 ± 0.06 29.01 ± 0.11 29.20 ± 0.01 7.67 ± 0.11 7.47 ± 0.05 SpyNet 9.69 ± 0.01 9.75 ± 0.07 6.40 ± 0.05 6.35 ± 0.00 13.08 ± 0.01 13.17 ± 0.03 18.75 ± 0.02 18.76 ± 0.06 MPI Sintel (final) GMA 17.34 ± 0.07 17.31 ± 0.11 0.53 ± 0.07 0.54 ± 0.11 32.11 ± 0.20 32.04 ± 0.24 4.57 ± 0.22 4.64 ± 0.24 PWCNet 13.61 ± 0.10 13.44 ± 0.14 3.52 ± 0.13 3.66 ± 0.12 23.00 ± 0.30 23.01 ± 0.06 10.84 ± 0.28 10.75 ± 0.05 RAFT 17.38 ± 0.04 17.36 ± 0.03 0.55 ± 0.09 0.50 ± 0.03 32.72 ± 0.22 32.72 ± 0.14 3.71 ± 0.21 3.75 ± 0.13 SpyNet 11.56 ± 0.01 11.59 ± 0.03 4.97 ± 0.01 4.97 ± 0.01 16.51 ± 0.01 16.55 ± 0.06 16.52 ± 0.01 16.47 ± 0.05", "type_str": "table", "content": "<table><tr><td/><td/><td>Target</td><td>-→ 0</td><td/><td/><td colspan=\"2\">Negative Initial Flow</td></tr><tr><td>Model</td><td colspan=\"2\">AEE wrt Target↓</td><td colspan=\"2\">AEE wrt Initial↑</td><td colspan=\"2\">AEE wrt Target↓</td><td colspan=\"2\">AEE wrt Initial↑</td></tr><tr><td/><td>CosPGD</td><td>PCFA</td><td>CosPGD</td><td>PCFA</td><td>CosPGD</td><td>PCFA</td><td>CosPGD</td><td>PCFA</td></tr><tr><td/><td/><td/><td/><td colspan=\"2\">KITTI 2015</td><td/><td/></tr><tr><td>GMA</td><td>28.69</td><td/><td/><td/><td/><td/><td/></tr></table>", "html": null}, "TABREF15": {"num": null, "text": "Comparison of clean and adversarial performance of image reconstruction models, as considered by(<PERSON><PERSON><PERSON><PERSON>  et al., 2023a). '+ADV' denotes FGSM adversarial training with a 50-50 mini-batch split for generating an adversarial sample.", "type_str": "table", "content": "<table><tr><td/><td>Clean</td><td/><td/><td colspan=\"2\">CosPGD</td><td/><td/><td/><td/><td colspan=\"2\">PGD</td></tr><tr><td>Architecture</td><td/><td colspan=\"2\">5 attack itrs</td><td colspan=\"2\">10 attack itrs</td><td colspan=\"2\">20 attack itrs</td><td colspan=\"2\">5 attack itrs</td><td colspan=\"2\">10 attack itrs</td><td>20 attack itrs</td></tr><tr><td/><td colspan=\"12\">PSNR SSIM PSNR SSIM PSNR SSIM PSNR SSIM PSNR SSIM PSNR SSIM PSNR SSIM</td></tr><tr><td colspan=\"4\">Restormer(Zamir et al., 2022) 31.99 0.9635 11.36 0.3236</td><td>9.05</td><td>0.2242</td><td>7.59</td><td colspan=\"3\">0.1548 11.41 0.3256</td><td>9.04</td><td>0.2234</td><td>7.58</td><td>0.1543</td></tr><tr><td>+ ADV</td><td colspan=\"2\">30.25 0.9453 24.49</td><td>0.81</td><td>23.48</td><td>0.78</td><td colspan=\"2\">21.58 0.7317</td><td>24.5</td><td>0.8079</td><td>23.5</td><td colspan=\"2\">0.7815 21.58 0.7315</td></tr><tr><td>Baseline(Chen et al., 2022)</td><td colspan=\"3\">32.48 0.9575 10.15 0.2745</td><td>8.71</td><td>0.2095</td><td>7.85</td><td colspan=\"3\">0.1685 10.15 0.2745</td><td>8.71</td><td>0.2094</td><td>7.85</td><td>0.1693</td></tr><tr><td>+ ADV</td><td colspan=\"12\">30.37 0.9355 15.47 0.5216 13.75 0.4593 12.25 0.4032 15.47 0.5215 13.75 0.4592 12.24 0.4026</td></tr><tr><td>NAFNet(Chen et al., 2022)</td><td>32.87 0.9606</td><td>8.67</td><td>0.2264</td><td>6.68</td><td>0.1127</td><td>5.81</td><td colspan=\"3\">0.0617 10.27 0.3179</td><td>8.66</td><td>0.2282</td><td>5.95</td><td>0.0714</td></tr><tr><td>+ ADV</td><td colspan=\"4\">29.91 0.9291 17.33 0.6046 14.68</td><td>0.509</td><td colspan=\"7\">12.30 0.4046 15.76 0.5228 13.91 0.4445 12.73 0.3859</td></tr></table>", "html": null}}}}