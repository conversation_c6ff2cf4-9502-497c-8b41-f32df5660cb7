{"paper_id": "Token-level-Direct-Preference-Optimization", "title": "Token-level Direct Preference Optimization", "abstract": "Fine-tuning pre-trained Large Language Models (LLMs) is essential to align them with human values and intentions. This process often utilizes methods like pairwise comparisons and KL divergence against a reference LLM, focusing on the evaluation of full answers generated by the models. However, the generation of these responses occurs in a token level, following a sequential, auto-regressive fashion. In this paper, we introduce Token-level Direct Preference Optimization (TDPO), a novel approach to align LLMs with human preferences by optimizing policy at the token level. Unlike previous methods, which face challenges in divergence efficiency, TDPO incorporates forward KL divergence constraints for each token, improving alignment and diversity. Utilizing the Bradley-Terry model for a token-based reward system, TDPO enhances the regulation of KL divergence, while preserving simplicity without the need for explicit reward modeling. Experimental results across various text tasks demonstrate TDPO's superior performance in balancing alignment with generation diversity. Notably, fine-tuning with TDPO strikes a better balance than DPO in the controlled sentiment generation and single-turn dialogue datasets, and significantly improves the quality of generated responses compared to both DPO and PPO-based RLHF methods. Our code is opensourced at https://github.com/Vance0124/Tokenlevel-Direct-Preference-Optimization.", "pdf_parse": {"paper_id": "Token-level-Direct-Preference-Optimization", "abstract": [{"text": "Fine-tuning pre-trained Large Language Models (LLMs) is essential to align them with human values and intentions. This process often utilizes methods like pairwise comparisons and KL divergence against a reference LLM, focusing on the evaluation of full answers generated by the models. However, the generation of these responses occurs in a token level, following a sequential, auto-regressive fashion. In this paper, we introduce Token-level Direct Preference Optimization (TDPO), a novel approach to align LLMs with human preferences by optimizing policy at the token level. Unlike previous methods, which face challenges in divergence efficiency, TDPO incorporates forward KL divergence constraints for each token, improving alignment and diversity. Utilizing the Bradley-Terry model for a token-based reward system, TDPO enhances the regulation of KL divergence, while preserving simplicity without the need for explicit reward modeling. Experimental results across various text tasks demonstrate TDPO's superior performance in balancing alignment with generation diversity. Notably, fine-tuning with TDPO strikes a better balance than DPO in the controlled sentiment generation and single-turn dialogue datasets, and significantly improves the quality of generated responses compared to both DPO and PPO-based RLHF methods. Our code is opensourced at https://github.com/Vance0124/Tokenlevel-Direct-Preference-Optimization.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Large language models (LLMs) (<PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2023) have demonstrated significant generalization capabilities in various domains including text summarization (<PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022) , coding writing (<PERSON> et al., 2021; <PERSON> et al., 2023) , and even following human instructions (<PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022) . In order to align LLMs with human intentions, Reinforcement Learning from Human Feedback (RLHF) (<PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON> et al., 2023) has emerged as a highly effective method, embodying both stylistic and ethical values (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2022) . These approaches typically involve the training of a reward model followed by the fine-tuning of the policy model using reinforcement learning (RL).", "section": "Introduction", "sec_num": "1."}, {"text": "Direct Preference Optimization (DPO) (<PERSON><PERSON><PERSON><PERSON> et al., 2023) introduces a straightforward and effective technique for training LLMs using pairwise comparisons, without the need for explicitly establishing a reward model. DPO utilizes KL divergence to ensure that the training process remains closely aligned with a reference Large Language Model (LLM), preventing significant deviations. In DPO, KL divergence is assessed at the sentence level, reflecting the fact that evaluations are based on complete responses (answers), typically comprising several sentences. However, the generation of these responses occurs sequentially, following an auto-regressive approach. A potential benefit is to examine divergence in relation to a reference LLM on a more granular, token-by-token basis. One approach involves using sequential KL divergence (as defined in Definition 4.3), which monitors the trajectory of the generated responses. As illustrated in Figure 1 , DPO demonstrates a significantly faster increase in KL divergence within the subset of less preferred responses when compared to the subset that is preferred. This results in an expanding gap between the two subsets and also indicates that DPO does not effectively control the KL divergence of the dispreferred response subset. This impacts the model's divergence efficiency and ultimately affects its linguistic capabilities and generative diversity. Such a limitation highlights the decreased effectiveness of employing KL divergence within the DPO framework, suggesting an area for improvement in its methodology. The imbalance in the growth rates of the sequential KL divergence is potentially related to the reverse KL divergence constraint employed by DPO. The mode-seeking property of reverse KL divergence tends to induce diversity reduction during generation, limiting the model's potential to produce diverse and effective responses (Wiher et al., 2022; Khalifa et al., 2020; Glaese et al., 2022; Perez et al., 2022) . Built upon DPO, the f-DPO method (Wang et al., 2023) studies the trade-off between alignment performance and generation diversity of LLMs under different divergence constraints. It highlights the advantages of the mass-covering behavior of forward KL divergence in enhancing model diversity and explores the impact of different divergence constraints. Nevertheless, f-DPO only independently discusses the changes in model behavior under either the reverse KL divergence or the forward KL divergence constraints. Essentially, it does not fundamentally enhance the DPO algorithm itself but rather strikes a balance between alignment performance and generating diversity by simply swapping different KL divergence constraints.", "section": "Introduction", "sec_num": "1."}, {"text": "Inspired by the aforementioned observations, we define and examine the problem of aligning with human preferences from a sequential and token-level standpoint. Some concurrent work has also been conducted in this direction (<PERSON><PERSON><PERSON><PERSON> et al., 2024; <PERSON><PERSON> et al., 2024) . We introduce a new method, referred to as Token-level Direct Preference Optimization (TDPO), which aims to strike a better balance between alignment performance and generation diversity by controlling the KL divergence for each token. In order to achieve this, we redefine the objective of maximising restricted rewards in a sequential manner. The connection between sentence-level reward and token-level generation is established by the use of the <PERSON><PERSON> equation. Afterwards, the Bradley-Terry model (<PERSON> & Terry, 1952 ) is converted into a representation at the token level, demonstrating its close relationship with the Regret Preference Model (<PERSON> et al., 2022; 2023) . By utilizing this method, we effectively integrate forward KL divergence restrictions for each token in the final objective function, resulting in improved regulation of KL divergence.", "section": "Introduction", "sec_num": "1."}, {"text": "TDPO maintains the simplicity of DPO while offering improved regulation of KL divergence for aligning LLMs with human preferences. Echoing the strategy of DPO, our method directly optimizes the policy without necessitating explicit reward model learning or policy sampling throughout the training phase. Our experimental results demonstrate the effectiveness of TDPO across multiple text tasks, and gain a notable enhancement in the quality of generated responses in comparison to both DPO and PPO-based RLHF methods. In conclusion, TDPO stands out for its ability to not only effectively address the issue of excessive KL divergence but also greatly improve divergence efficiency.", "section": "Introduction", "sec_num": "1."}, {"text": "The emergence of ChatGPT has catalyzed significant advancements in the field of Large Language Models (LLMs), such as OpenAI's GPT-4 (<PERSON><PERSON><PERSON> et al., 2023) , Mistral (<PERSON> et al., 2023) , and Google's Gemini (<PERSON> et al., 2023) . Generally, the training of LLMs involves three stages: initial unsupervised pre-training on massive text corpora to grasp linguistic structures (<PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2020; Workshop et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2023) , followed by supervised fine-tuning with task-specific datasets to enhance the LLMs' probability of producing desired responses (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) . However, due to the typically limited and expensive availability of labeled datasets during the supervised fine-tuning stage, the model may retain biases and inaccuracies, manifesting as societal biases (<PERSON><PERSON> et al., 2021) , ethical concerns (<PERSON><PERSON><PERSON> et al., 2021) , toxicity (<PERSON><PERSON> et al., 2022) , and hallucinations (<PERSON> et al., 2023) , which necessitates a subsequent AI alignment phase. Noteworthy models achieving significant alignment, such as Zephyr (<PERSON><PERSON><PERSON> et al., 2023) and GPT-4 (<PERSON><PERSON><PERSON> et al., 2023) , have demonstrated the effectiveness of techniques like Reinforcement Learning from Human Feedback (RLHF) and Direct Preference Optimization (DPO) algorithms.", "section": "Related Works", "sec_num": "2."}, {"text": "Reinforcement Learning from Human Feedback (RLHF) has emerged as a cornerstone in aligning LLMs with human values, providing a mechanism to refine model outputs based on qualitative feedback (<PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2023) . This approach has shown considerable promise in making models more responsive to human expectations and ethical considerations by iteratively improving their performance through human-generated feedback. However, the complexity of implementing RLHF, compounded by the inaccuracies in human-generated reward models (<PERSON> et al., 2023) , has prompted the exploration of alternative strategies. Methods like Reward Ranked Fine-Tuning (RAFT) (<PERSON> et al., 2023) and Rank Responses to align Human Feedback (RRHF) (<PERSON> et al., 2023) offer streamlined approaches to alignment, circumventing some of RLHF's inherent challenges. Particularly, Direct Preference Optimization (DPO) (<PERSON><PERSON><PERSON><PERSON> et al., 2023) represents a breakthrough in direct policy optimization, addressing the intricacies of balancing model behavior through a nuanced approach to reward function optimization. Nevertheless, the challenge of maintaining linguistic diversity while aligning with human preferences remains a pivotal concern, prompting our proposed Token-level Direct Preference Optimization (TDPO), which seeks to harmonize the dual objectives of alignment accuracy and expressive range in model outputs.", "section": "Related Works", "sec_num": "2."}, {"text": "For language generation, a language model (LM) is prompted with prompt (question) x to generate a response (answer) y, where both x and y consist of a sequence of tokens. Direct Preference Optimization (DPO) (<PERSON><PERSON><PERSON><PERSON> et al., 2023) commences with the RL objective from the RLHF:", "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "section": "Preliminaries", "sec_num": "3."}, {"text": "where D represents the human preference dataset, r(x, y) denotes the reward function, π ref (•|x) serves as a reference model, typically chosen the language model after supervised fine-tuning, π θ represents the model undergoing RL fine-tuning, initialized with π θ = π ref , and β is the coefficient for the reverse KL divergence penalty.", "section": "Preliminaries", "sec_num": "3."}, {"text": "By directly deriving from Eq. 1, DPO establishes a mapping between the reward model and the optimal policy under the reverse KL divergence, obtaining a representation of the reward function concerning the policy:", "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "section": "Preliminaries", "sec_num": "3."}, {"text": "Here, Z(x) is the partition function.", "section": "Preliminaries", "sec_num": "3."}, {"text": "To align with human preference, DPO uses the <PERSON><PERSON><PERSON> model for pairwise comparisons:", "section": "Preliminaries", "sec_num": "3."}, {"text": "P BT (y 1 ≻ y 2 |x) = exp(r(x, y 1 )) exp(r(x, y 1 )) + exp(r(x, y 2 ))", "section": "Preliminaries", "sec_num": "3."}, {"text": ".", "section": "Preliminaries", "sec_num": "3."}, {"text": "(3) By substituting Eq. 2 into Eq. 3 and leveraging the negative log-likelihood loss, DPO derives the objective function:", "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "section": "Preliminaries", "sec_num": "3."}, {"text": "and the derivative is given as follows:", "section": "Preliminaries", "sec_num": "3."}, {"text": "EQUATION", "section": "Preliminaries", "sec_num": "3."}, {"text": "where u is the abbreviation of u(x, y w , y l ), y w and y l denotes the preferred and dispreferred completion.", "section": "Preliminaries", "sec_num": "3."}, {"text": "In this section, we initially reformulate the constrained reward maximization problem into a token-level form. From this, we derive the mapping between the state-action function and the optimal policy. Subsequently, we convert the Bradley-Terry model into token-level representation, establishing its equivalence with the Regret Preference Model. By substituting the mapping relationship into the reward model in token-level format, we obtain the optimization objective solely related to the policy. Finally, we conduct a formalized analysis of this optimization objective in terms of derivatives and, based on this, derive the ultimate loss function for TDPO.", "section": "Methodology", "sec_num": "4."}, {"text": "To model the sequential, auto-regressive generation, we extend the sentence-level formulation in Section 3 by considering that the response consists of T tokens y = y <T +1 := [y 1 , y 2 , ..., y T ], where y t ∈ Y, and Y represents the alphabet (vocabulary). Additionally, we assume y <1 = [ ]. Given a prompt x and the first t -1 tokens y <t of the response y, the LM predicts the probability distribution of the next token π θ (•|[x, y <t ]).", "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "When modeling text generation as a <PERSON>ov decision process (<PERSON><PERSON>, 2014) , a state is a combination of the prompt and the generated response up to the current step, denoted as", "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "s t = [x, y <t ].", "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "An action corresponds to the next generated token, denoted as a t = y t , and the token-wise reward is defined as", "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "R t := R(s t , a t ) = R([x, y <t ], y t ).", "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "Expanding on the provided definitions, we establish the state-action function Q π , the state value function V π and the advantage function A π for a policy π:", "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "Qπ([x, y <t ], y t ) = Eπ ∞ k=0 γ k R t+k st = [x, y <t ], at = y t , Vπ([x, y <t ]) = Eπ Qπ([x, y <t ], y t ) st = [x, y <t ] , Aπ([x, y <t ], y t ) = Qπ([x, y <t ], y t ) -Vπ([x, y <t ]).", "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "(6) where γ represents the discount factor. In this paper, we set γ = 1.", "section": "Markov Decision Process under <PERSON><PERSON>", "sec_num": "4.1."}, {"text": "DPO's objective function in Eq. 1 operates at the sentence level. In contrast, we propose an alternative token-level objective function:", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "max π θ E x,y <t ∼D,z∼π θ (•|[x,y <t ]) A π ref ([x, y <t ], z) -βD KL π θ (•|[x, y <t ])||π ref (•|[x, y <t ]) . (7)", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "The objective function is inspired by Trust Region Policy Optimization (TRPO) (<PERSON><PERSON><PERSON> et al., 2015) . As demonstrated in Lemma 4.1, maximizing the objective function in Eq. 7 will result in policy improvements in terms of expected return.", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "Lemma 4.1. Given two policies π and π, if for any state", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "s t = [x, y <t ], E z∼π [A π ([x, y <t ], z)] ≥ 0, then we can conclude: E x∼D [V π ([x])] ≥ E x∼D [V π ([x])] ,", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "The proof is provided in Appendix A.1.", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "Notably, to maintain generation diversity and prevent the model from hacking some high-reward answers, we incorporate reverse KL divergence for each token in our token-level objective function, which prevents the model from deviating too far from the reference model distribution.", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "Starting from the token-level objective function in Eq. 7, we can directly derive the mapping between the state-action function Q π and the optimal policy π * θ . We summarize this relationship in the following lemma.", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "Lemma 4.2. The constrained problem in Eq. 7 has the closed-form solution:", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "EQUATION", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "where", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "Z([x, y <t ]; β) = E z∼π ref (•|[x,y <t ]) e 1 β Qπ ref ([x,y <t ],z)", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "is the partition function.", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "See Appendix A.2 for more details.", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "To obtain the optimal policy π * θ from Eq. 8, we must estimate the state-action function Q π ref and the partition function Z(•). However, ensuring the accuracy of the stateaction function Q π at each state and action is challenging, and estimating the partition function Z(•) is also difficult. Therefore, we reorganize Eq. 8 to obtain the expression of the state-value function in terms of the policy:", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "EQUATION", "section": "Token-Level Optimization", "sec_num": "4.2."}, {"text": "To facilitate subsequent derivations, we first introduce the sequential KL divergence, as defined in Definition 4.3.", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Definition 4.3. Given two language models π 1 and π 2 , with the input prompt x and output response y, the sequential KL divergence is defined as:", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "EQUATION", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Given prompts x and pairwise responses (y 1 , y 2 ), the <PERSON><PERSON> model expresses the human preference probability. However, since the <PERSON><PERSON><PERSON> model is formulated at the sentence level, it cannot establish a connection with the token-level mapping presented in Eq. 9. Consequently, we need to derive a token-level preference model. Initiating from the <PERSON><PERSON><PERSON> model, we transform it into a token-level formulation and demonstrate its equivalence with the Regret Preference Model (<PERSON> et al., 2023; 2022) , as shown in the Lemma 4.4.", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Lemma 4.4. Given a reward function r(x, y), assuming a relationship between token-wise rewards and the reward function represented by r(x, y) = T t=1 γ t-1 R([x, y <t ], y t ), we can establish the equivalence between the Bradley-<PERSON> model and the Regret Pref-erence Model in the task of text generation alignment, i.e.,", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "PBT(y1 ≻ y2|x) = σ T 1 t=1 γ t-1 Aπ([x, y <t 1 ], y t 1 ) - T 2 t=1 γ t-1 Aπ([x, y <t 2 ], y t 2 ) ,", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "(11) where σ(x) = 1/(1 + exp(-x)) is the logistic sigmoid function.", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "We prove this lemma in A.3.", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "In Lemma 4.4, we assume that r(x, y)", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "= T t=1 γ t-1 R([x, y <t ], y t ).", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "This assumption is natural in the context of RL, where r(x, y) represents the overall reward for response y given the prompt x. Considering text generation as a sequential decision-making problem, r(x, y) can be viewed as the cumulative reward for the generated text.", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "According to the definition of the advantage function in Section 4.1, we can directly establish the relationship between the optimal solution in Eq. 9 and preference optimization objective in Eq. 11. One intractable aspect is that the stateaction function Q π depends on a partition function, which is contingent on both the input prompt x and the output response y. This results in non-identical values of the partition function for a pair of responses (y w , y l ), specifically,", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Z([x, y <t w ]; β) ̸ = Z([x, y <t l ]; β).", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "As a result, we cannot employ a cancellation strategy similar to DPO, which relies on the property that the Bradley-Terry model depends only on the difference in rewards between two completions.", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Fortunately, by expanding the advantage function A π and converting the state-value function V π into a form exclusively related to the state-action function Q π , we can offset the partition function naturally. In this way, we ultimately reformulate the Bradley-<PERSON> model to be directly tied to the optimal policy π * θ and the reference policy π ref . This is summarized in the following theorem. Theorem 4.5. In the KL-constrainted advantage function maximization problem corresponding to Eq.7, the Bradley<PERSON><PERSON> model express the human preference probability in terms of the optimal policy π * θ and reference policy π ref :", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "EQUATION", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "where, u(x, y 1 , y 2 ) refers to the difference in rewards implicitly defined by the language model π θ and the reference model π ref (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , represented as", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "EQUATION", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "and δ(x, y 1 , y 2 ) refers to the difference in sequential forward KL divergence between two pairs (x, y 1 ) and (x, y 2 ), weighted by β, expressed as", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "EQUATION", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": ")", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "The proof is provided in the Appendix A.4.", "section": "BT Model Reformulation via Advantage Function", "sec_num": "4.3."}, {"text": "Drawing on Eq. 12, we reformulate the Bradley-<PERSON> model into a structure solely relevant to the policy. This allows us to formulate a likelihood maximization objective for a parametrized policy π θ , leading to the derivation of the loss function for the initial version of our method, TDPO 1 :", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "EQUATION", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "Through this approach, we explicitly introduce sequential forward KL divergence into the loss function. Coupled with the implicitly integrated reverse KL divergence, we enhance our ability to balance alignment performance and generation diversity of LLMs.", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "Subsequently, we conduct a derivative analysis of our method and make specific modifications to the loss function of TDPO. For convenience, we use u to denote u(x, y w , y l ), and δ to represent δ(x, y w , y l ). By employing the formulation of the loss function presented in Eq.15, we compute the gradient of the loss function with respect to the parameters θ:", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "EQUATION", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "In Eq. 16, (-u + δ) serves as the weighting factor for the gradient. The first part (-u) corresponds to the weight factor in the loss function of DPO. When the language model makes errors in predicting human preferences, i.e., log π θ (y l |x) π ref (y l |x) > log π θ (yw|x) π ref (yw|x) , the value of (-u) will become larger, applying a stronger update for the comparison (y w , y l ). While the second part δ is a distinctive component of our method. As shown in Figure 1 , the KL divergence growth rate for the dispreferred response subset is faster than that for the preferred response subset. With the increasing disparity, the corresponding value of δ rises, thereby amplifying the weight factor (-u + δ). Combined with the subsequent gradient term, our objective function can effectively suppress the difference in KL divergence between pairs of responses with large disparities in KL divergence. Through the collaborative influence of the weight factor δ and the gradient term (-∇ θ δ), our method achieves the purpose of automatic control over the KL divergence balance.", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "The gradient of the loss function in Eq. 16 also consists of two components, ∇ θ u and (-∇ θ δ). ∇ θ u represents the optimization direction of the gradient in DPO. Intuitively, ∇ θ u increases the likelihood of preferred completions y w and decreases the likelihood of dispreferred com-pletions y l . While (-∇ θ δ) tends to narrow the gap between D SeqKL (x, y w ; π ref ∥π θ ) and D SeqKL (x, y l ; π ref ∥π θ ).", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "However, when considered separately, the gradient of D SeqKL (x, y w ; π ref |π θ ) in the loss function tends to increase the sequential KL divergence between π ref and π θ at (x, y w ) during the optimization process. This is because the sequential forward KL divergence in the loss function is introduced through the state-value function V π , inherently introducing an expectation E z∼π ref log π θ (z|[x,y <t ]) π ref (z|[x,y <t ]) as a baseline at each token. The negative value of this expectation corresponds precisely to a forward KL divergence", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "D KL (π ref (•|[x, y <t ])|π θ (•|[x, y <t ]))", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": ", which can be used to constrain the unbalanced growth of KL divergence. For the prompt x and the preferred response y w , at each token, the loss function in Eq. 16 tends to increase the likelihood of log ) . The increase in the expectation implies a smaller forward KL divergence at that token, thereby acting to constrain the growth rate of sequential forward KL divergence. Therefore, for this term, we choose to retain its gradient updates.", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "In conclusion, we only propagate the gradient of the D SeqKL (x, y l ; π ref |π θ ) in (-∇ θ δ). When the second part weight factor δ becomes larger, it imposes a stronger suppression on D SeqKL (x, y l ; π ref ∥π θ ) to control the balance of KL divergence. Furthermore, to achieve a better balance between alignment performance and generation diversity in TDPO, we introduce an additional parameter α into the loss function. By adjusting the magnitude of α, we can control the deviation between D SeqKL (x, y w ; π ref ∥π θ ) and D SeqKL (x, y l ; π ref ∥π θ ).", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "In summary, we modify the loss function of TDPO 1 , resulting in the second version of our method, TDPO 2 , as follows:", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "L TDPO2 (π θ ; π ref ) = -E (x,yw,y l )∼D [log σ (u(x, y w , y l ) -αδ 2 (x, y w , y l ))] ,", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "(17) where α is a parameter, and", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "δ 2 (x, y 1 , y 2 ) =βD SeqKL (x, y 2 ; π ref ∥π θ ) -sg (βD SeqKL (x, y 1 ; π ref ∥π θ )) .", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "(18) The sg represents the stop-gradient operator, which blocks the propagation of gradients.", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "We summarize the comparison of the loss functions for DPO, TDPO 1 , and TDPO 2 , as presented in Figure 2 . Leveraging the parameter β to regulate the deviation of the language model from the base reference model, and α to control the balance of sequential KL divergence within the language model, our approach achieves superior alignment with human preferences while preserving model generation diversity effectively. We provided the pseudocode in Algorithm 1 and the Pytorch implementation version of TDPO loss in Appendix B.", "section": "Loss Function and Formal Analysis", "sec_num": "4.4."}, {"text": "In this section, we demonstrate the superior performance of our algorithm in three different open-sourced datasets: the IMDb sentiment dataset (<PERSON><PERSON> et al., 2011) , the Anthropic HH dataset (<PERSON> et al., 2022) , and MT-bench (<PERSON> et al., 2023) . The IMDb dataset serves as a controlled semantic generation dataset where the model is presented with prompts consisting of prefixes from movie reviews, and required to generate responses with positive sentiment. The Anthropic HH dataset is a single-turn dialogue dataset where the model receives human queries, covering various if Method M is TDPO 1 then θ ← θ + η∇ θ E (x,yw,y l )∼Dm [log σ (u(x, y w , y l ) -αδ 2 (x, y w , y l ))] ▷ Eq.17 end if 18: end for 19: Output: π θ topics such as academic questions or life guidance. The trained model is tasked with providing helpful answers to these questions while avoiding toxic responses. Finally, MT-Bench is a GPT-4-based evaluation benchmark, assessing the proficiency of LLMs in handling multi-turn openended questions. Questions in MT-Bench span eight distinct knowledge domains, from areas such as writing, mathematical reasoning, and humanities. Experimental results demonstrate that MT-Bench achieves consistency with human preferences exceeding 80%.", "section": "Experiments", "sec_num": "5."}, {"text": "In this experiment, besides our proposed methods TDPO 1 and TDPO 2 , we also implemented the DPO algorithm for fair comparison. We employed GPT-2 Large (<PERSON><PERSON> et al., 2019) as our base model and the model checkpoint: insub/gpt2-large-IMDb-fine-tuned 1 as the SFT model. During the evaluation, we utilized the pre-trained sentiment classifier siebert/sentiment-roberta-large-english 2 to compute rewards. For DPO, we followed the official implementation (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , setting β at 0.1. To analyze the effectiveness of each algorithm in optimizing the constrained reward maximization objective, we evaluated each algorithm after 100 training steps until convergence, computing its frontier of average reward and average sequential KL divergence with the reference policy.", "section": "Experiments on IMDb Dataset", "sec_num": "5.1."}, {"text": "1 https://huggingface.co/insub/ gpt2-large-IMDb-fine-tuned 2 https://huggingface.co/siebert/ sentiment-roberta-large-english", "section": "Experiments on IMDb Dataset", "sec_num": "5.1."}, {"text": "The results are depicted in Figure 3 (a). We implement the DPO, TDPO 1 , and different versions of TDPO 2 algorithms with varying the parameter α. From the figure, we notice that although DPO establishes an efficient frontier, TDPO 1 and TDPO 2 outperform DPO in terms of divergence versus reward on the frontier, achieving higher reward while maintaining low KL divergence. We also implemented versions of TDPO 2 with α ∈ {1, 1.5, 2, 5}. However, we found that higher values of α made it difficult to optimize the reward. In Figures 3(b ) to 3(d), we illustrate the curves portraying the sequential KL divergence for different algorithms during the training process. The sequential KL divergence growth rate of DPO on the dispreferred response subset is significantly higher than that on the preferred response subset, leading to an increasing offset between them. In contrast, TDPO 2 exhibits superior control over KL divergence, achieving better divergence efficiency compared to DPO. As analyzed in Section 4.4, TDPO 1 tends to result in an increased sequential KL divergence on the preferred response subset, thereby exhibiting a weaker capacity for KL divergence adjustment compared to TDPO 2 . TDPO 2 maintains a more balanced sequential KL divergence on both dispreferred and preferred response subsets, contributing to its ability to achieve a superior frontier. Although a larger α enhances control over the sequential KL divergence, it also affects the speed and difficulty of optimization. For the remainder of this paper, we set α = 0.5. In Appendix C, we also present graphs of the frontier between the reward and forward KL divergence and the progression curves of the forward KL divergence throughout the training process. ", "section": "Experiments on IMDb Dataset", "sec_num": "5.1."}, {"text": "Next, we evaluate the performance of TDPO 1 and TDPO 2 on the Anthropic HH dataset. We use Pythia-2.8B (<PERSON><PERSON><PERSON> et al., 2023) as the base model and fine-tune the base model on chosen completions to train a reference model, such that completions are within the distribution of the model. Subsequently, we train TDPO 1 , TDPO 2 , DPO (<PERSON><PERSON><PERSON><PERSON> et al., 2023) and f-DPO with forward KL divergence constraint (<PERSON> et al., 2023) on this reference model. In this experiment, our primary focus is on two aspects: 1) the trade-off between alignment and diversity in generating responses among different algorithms, and 2) the ability of different algorithms to align with human preferences. For the first part, we utilize automatic metrics for evaluation, while for the second part, we rely on the GPT-4 evaluation. Both evaluations were conducted on the test set of the Anthropic HH dataset.", "section": "Experiments on Anthropic HH Dataset", "sec_num": "5.2."}, {"text": "To assess the alignment performance of different algorithms in generating responses, we compute the accuracy of gen-erated responses relative to chosen completions in the test dataset. To measure the diversity, we employ nucleus sampling with p = 0.95 to generate 25 responses and utilize the predictive entropy as the evaluation metric. The tradeoff between alignment accuracy and diversity for different algorithms is summarized in Table 1 . TDPO 2 not only surpasses DPO, f-DPO and TDPO 1 in terms of accuracy but also excels in entropy, achieving a superior balance between alignment and diversity.", "section": "Experiments on Anthropic HH Dataset", "sec_num": "5.2."}, {"text": "To further assess the ability of TDPO 1 and TDPO 2 to align with human preferences, we evaluated the win rates of responses generated by models trained with different algorithms against chosen responses on the test set of the HH dataset, the result is illustrated in the Figure 4 . Compared to the SFT model, the DPO, TDPO 1 , and TDPO 2 algorithms better align with human preferences, achieving win rates not less than 50% against chosen responses at temperature 0.75. This demonstrates that both TDPO 1 , and TDPO 2 possess a strong capability to align with human preferences. ", "section": "Experiments on Anthropic HH Dataset", "sec_num": "5.2."}, {"text": "To comprehensively evaluate TDPO 1 , and TDPO 2 in terms of generation quality, we conducted pairwise comparisons on the MT-Bench using models trained on the Anthropic HH dataset. Following the official MT-Bench implementation, we sampled responses with a temperature coefficient of 0.7 and constrained the maximum number of newly generated tokens to 512. For the PPO baseline, we employed the trlx framework (<PERSON><PERSON><PERSON> et al., 2023) ", "section": "Experiments on MT-Bench", "sec_num": "5.3."}, {"text": "In this work, we introduced Token-level Direct Preference Optimization (TDPO), an innovative token-level fine-tuning approach for Large Language Models (LLMs) aimed at aligning more closely with human preferences. By employing the token-wise optimization with forward KL divergence constraints and converting the Bradley-Terry model into a token-level preference model, TDPO addresses key challenges in divergence efficiency and content diversity, surpassing traditional methods like Direct Preference Optimization (DPO) and PPO-based RLHF in tasks such as controlled sentiment generation and single-turn dialogues. This marks a substantial advancement in LLM training methodologies, demonstrating the potential of token-level optimization to enhance the alignment, quality, and diversity of LLM outputs, setting a new direction for AI alignment research and the development of nuanced, human-aligned AI systems.", "section": "Conclusion", "sec_num": "6."}, {"text": "Regarding the future prospects of alignment methodologies, we anticipate that iterative refinement approaches and multiturn conversational alignment strategies will significantly improve the alignment of large language models with human values. By continuously refining these models, we can achieve more precise alignment with complex human preferences. Moreover, multi-turn conversations enable deeper and more nuanced interactions, fostering comprehensive attunement to human intentions. These approaches aim to enhance the quality and relevance of AI responses, making AI systems more harmonized with human values and expectations. ", "section": "Conclusion", "sec_num": "6."}, {"text": "E x∼D [V π ([x])] ≥ E x∼D [V π ([x])] ,", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Proof. Let trajectory τ := (x, y 1 , y 2 , ...), and the notation E τ |π [•] indicates that actions are sampled from π to generate τ . So we can get", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Since for any state s t = [x, y <t ], E z∼π [A π ([x, y <t ], z)] ≥ 0, so we can obtain", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": ")", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Our goal is to maximize the expected return of a parameterized policy π θ . According to Eq.23, what we need to do is max ", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Based on the property of KL divergence, we can derive the relationship between the optimal policy and the state-action function: ", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "where σ(x) = 1/(1 + exp(-x)) is the logistic sigmoid function.", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Proof. According to the <PERSON><PERSON> model, we have P BT (y 1 ≻ y 2 |x) = exp(r(x, y 1 )) exp(r(x, y 1 )) + exp(r(x, y 2 )) ,", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "where r(x, y) represents the overall reward of the pair (x, y).", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "Based on assumption that r(x, y) = ", "section": "<PERSON>. Mathematical Derivations", "sec_num": null}, {"text": "https://huggingface.co/Dahoas/ gptj-rm-static", "section": "", "sec_num": null}], "back_matter": [{"text": "The research leading to these results received funding from National Key R&D Program of China (2022ZD0116402). In addition, it received funding from Science and Technology Research and Development Project of China State Railway Group Corporation Limited (P2022X012).", "section": "Acknowledgements", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.Text generation is analogous to a deterministic contextual bandit, where the transition to the next state is certain given the current state and action, i.e., p(s t+1 = [x, y <t+1 ]|s t = [x, y <t ], a t = y t ) = 1, so we have:Next, note that y T = EOS denotes the end of the text sequence. Therefore,Substituting Eq.38 to Eq.41 into the Bradley-Terry model, we obtainAdditionally, note that y <1 = [ ], so we can getTherefore,A.4. Deriving the TDPO Objective Under the Bradley-Terry Model Theorem A.4. In the KL-constrainted advantage function maximization problem corresponding to Eq.7, the Bradley-<PERSON> model express the human preference probability in terms of the optimal policy π * θ and reference policy π ref :where, u(x, y 1 , y 2 ) refers to the difference in rewards implicitly defined by the language model π θ and the reference model π ref (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , represented asand δ(x, y 1 , y 2 ) refers to the difference in sequential forward KL divergence between two pairs (x, y 1 ) and (x, y 2 ), weighted by β, expressed as δ(x, y 1 , y 2 ) = βD SeqKL (x, y 2 ;Proof. According to the Lemma 4.2, we haveRearrange Eq.48, we obtainFrom Lemma 4.4, We can getBy leveraging Eq.49, we can deriveSubstituting Eq.59 to Eq.61 into Eq.50, we arrive at:", "section": "Impact Statement", "sec_num": null}, {"text": "PyTorch code for the TDPO loss is provided below:import torch import torch.nn.functional as F def tdpo_loss(pi_logits, ref_logits, yw_idxs, yl_idxs, labels, beta, alpha, if_tdpo2): \"\"\" pi_logits: policy logits. Shape: (batch_size, sequence_length, vocab_size), ref_logits: reference logits. Shape: (batch_size, sequence_length, vocab_size) yw_idxs: preferred completion indices in [0,B-1], shape (T,) yl_idxs: dispreferred completion indices in [0,B-1], shape (T,) labels: labels for which to compute the log probabilities, Shape: (batch_size, sequence_length) beta: temperature controlling strength of KL penalty Each pair of (yw_idxs[i], yl_idxs[i]) represents the indices of a single preference pair. alpha: The weight factor adjusts the influence weight of kl divergence at each token if_tdpo2: Use method TDPO2 by default; if False, switch to TDPO1 \"\"\" pi_vocab_logps = pi_logits.log_softmax(-1) ref_vocab_ps = ref_logits.softmax(-1) ref_vocab_logps = ref_vocab_ps.log() pi_per_token_logps = torch.gather(pi_vocab_logps, dim=2, index=labels.unsqueeze(2)).squeeze(2) ref_per_token_logps = torch.gather(ref_vocab_logps, dim=2, index=labels.unsqueeze( 2 Unless specified otherwise, we use a α = 0.5, β = 0.1, batch size of 64, and the RMSprop optimizer with a learning rate of 5e-6. We linearly warm up the learning rate from 0 to 5e-6 over 150 steps. ", "section": "B. TDPO Implementation Details and Hyperparameters", "sec_num": null}], "ref_entries": {"FIGREF0": {"num": null, "fig_num": "1", "uris": null, "text": "Figure 1. Sequential KL (SeqKL) divergence of both preferred response and dispreferred responses on IMDb dataset. Figure 1(a) shows the progression of SeqKL divergence on the preferred responses over training steps. Figure 1(b) depicts the evolution of SeqKL divergence on the dispreferred responses over the training steps. Figure 1(c) illustrates the difference between the SeqKL divergence of the dispreferred responses and that of the preferred responses during the training process, namely margin = |DSeqKL(x, yw; π ref ∥π θ ) -DSeqKL(x, y l ; π ref ∥π θ )|. The definition of SeqKL divergence refers to Definition 4.3.", "type_str": "figure"}, "FIGREF1": {"num": null, "fig_num": null, "uris": null, "text": "π ref (y t w |[x,y <t w ]) while simultaneously decreasing the expectation, enlarging the gap between the specified term y t w and the baseline to expedite training. The impact of decreasing the expectation is an increase in the forward KL divergence D KL (π ref (•|[x, y <t w ])|π θ (•|[x, y <t w ])) at each token, leading to an increase in D SeqKL (x, y w ; π ref |π θ ). As we do not aim to accelerate the training speed and prefer to ensure training stability, we modify the loss function by discontinuing the gradient propagation of D SeqKL (x, y w ; π ref |π θ ) and treating it as a baseline term for alignment of D SeqKL (x, y l ; π ref |π θ ). Different from D SeqKL (x, y w ; π ref |π θ ), the gradient of D SeqKL (x, y l ; π ref |π θ ) tends to reduce the sequential KL divergence between π ref and π θ at (x, y l ). For the prompt x and the rejected response y l , the loss function in Eq.16 tends to decrease the likelihood of log π(y t l |[x,y <t l ]) π ref (y t l |[x,y <t l ]) at each token while increasing the expectation E z∼π ref log π θ (z|[x,y <t l ]) π ref (z|[x,y <t l ]", "type_str": "figure"}, "FIGREF2": {"num": null, "fig_num": "2", "uris": null, "text": "Figure 2. Comparison of Loss Functions for DPO, TDPO1 and TDPO2 Methods. The sg denotes the stop-gradient operator. Both TDPO1 and TDPO2 incorporate an additional term for finer-grained control over the KL divergence, compared to DPO.", "type_str": "figure"}, "FIGREF3": {"num": null, "fig_num": "1", "uris": null, "text": "Token-level Direct Preference Optimization (TDPO) 1: Input: Reference model π ref , Policy model π θ , Coefficient α, β, Learning rate η 2: Input: Dataset D = {(x, y w , y l ) i } N i=1 of size N , Method M 3: Initialize: π θ ← π ref 4: for each epoch do 5: Sample mini-batch D m = {(x, y w , y l ) m } M m=1 from D 6: Predict the probabilities π θ (y w |x) and π θ (y l |x) for (x, y w , y l ) in the mini-batch D m using the policy model 7: Predict the probabilities π ref (y w |x) and π ref (y l |x) for (x, y w , y l ) in the mini-batch D m using the reference model 8: Calculate the function u(x, y w , y l ) = β log π θ (yw|x) π ref (yw|x) -β log π θ (y l |x) π ref (y l |x) KL divergence D SeqKL (x, y w ; π ref ∥π θ ) for (x, y w ) in the mini-batch D m 10: Compute the sequential KL divergence D SeqKL (x, y l ; π ref ∥π θ ) for (x, y l ) in the mini-batch D m 11:", "type_str": "figure"}, "FIGREF5": {"num": null, "fig_num": "3", "uris": null, "text": "Figure 3. The experiment on IMDb dataset. Figure 3(a) represents the frontier of expected reward and KL divergence with respect to the reference model. We implemented DPO, TDPO1, and different versions of TDPO2 with respect to the parameter α. Both TDPO1 and TDPO2 outperform DPO in terms of the frontier, with TDPO2 showing further improvement over TDPO1. This demonstrates the effectiveness of our analysis and modifications. Figure 3(b) and Figure 3(c) present the progression of sequential KL divergence on the preferred and dispreferred responses subset over training steps respectively. Figure 3(d) illustrates the difference between the sequential KL divergence on the dispreferred responses subset and that on the preferred responses subset throughout the training process, namely margin = |DSeqKL(x, yw; π ref ∥π θ ) -DSeqKL(x, y l ; π ref ∥π θ )|. TDPO2 exhibit superior regulation over KL divergence compared to the TDPO1 and DPO algorithm.", "type_str": "figure"}, "FIGREF6": {"num": null, "fig_num": "4", "uris": null, "text": "Figure 4. The win rates, computed by GPT-4, in comparison to the chosen responses for Anthropic-HH one-step dialogue.", "type_str": "figure"}, "FIGREF7": {"num": null, "fig_num": null, "uris": null, "text": "y <t ∼D,z∼π θ (•|[x,y <t ]) [A π ref ([x, y <t ], z)].To prevent the excessive degradation of language models, we introduce a reverse KL divergence constraint, forming our objective function:max π θ E x,y <t ∼D,z∼π θ (•|[x,y <t ]) A π ref ([x, y <t ], z) -βD KL π θ (•|[x, y <t ])||π ref (•|[x, y <t ])(25)A.2. Deriving the Mapping between the State-Action Function and the Optimal Policy Lemma A.2. The constrained problem in Eq. 7 has the closed-form solution:π * θ (z|[x, y <t ]) = π ref (z|[x, y <t ]) exp 1 β Q π ref ([x, y <t ], z) Z([x, y <t ]; β) ,(26)whereZ([x, y <t ]; β) = E z∼π ref (•|[x,y <t ]) e1 Qπ ref ([x,y <t ],z) is the partition function.", "type_str": "figure"}, "FIGREF8": {"num": null, "fig_num": "3", "uris": null, "text": "Proving the Equivalence of the Bradley-Terry Model and the Regret Preference Model Lemma A.3. Given a reward function r(x, y), assuming a relationship between token-wise rewards and the reward function represented by r(x, y) = T t=1 γ t-1 R([x, y <t ], y t ), we can establish the equivalence between the <PERSON>-<PERSON> model and the Regret Preference Model in the task of text generation alignment, i.e., ,", "type_str": "figure"}, "FIGREF9": {"num": null, "fig_num": null, "uris": null, "text": "t=1 γ t-1 R([x, y <t ], y t ), we can get: 1 (R([x, y <t ], y t ) + γV π ([x, y <t+1 ]) -γV π ([x, y <t+1 ])) (37) = V π ([x, y <1 ]) + T t=1 γ t-1 R([x, y <t ], y t ) + γV π ([x, y <t+1 ]) -V π ([x, y <t ]) -γ T V π ([x, y <T +1 ])", "type_str": "figure"}, "TABREF0": {"num": null, "text": "-𝛼 𝛽𝐷 SeqKL (𝑥, 𝑦 𝑙 ; 𝜋 ref ||𝜋 𝜃 ) -𝑠𝑔 𝛽𝐷 SeqKL 𝑥, 𝑦 𝑤 ; 𝜋 ref | 𝜋 𝜃", "html": null, "content": "<table><tr><td/><td>𝓛 𝐃𝐏𝐎 𝜋 𝜃 ; 𝜋 ref = -𝔼 log 𝜎 𝛽 log</td><td colspan=\"2\">𝜋 𝜃 (𝑦 𝑤 |𝑥) 𝜋 ref (𝑦 𝑤 |𝑥)</td><td>-𝛽 log</td><td>𝜋 𝜃 (𝑦 𝑙 |𝑥) 𝜋 ref (𝑦 𝑙 |𝑥)</td></tr><tr><td/><td colspan=\"2\">𝓛 𝐓𝐃𝐏𝐎𝟏 𝜋 𝜃 ; 𝜋 ref = -𝔼 log 𝜎 𝛽 log</td><td colspan=\"2\">𝜋 𝜃 (𝑦 𝑤 |𝑥) 𝜋 ref (𝑦 𝑤 |𝑥)</td><td>-𝛽 log</td><td>𝜋 𝜃 (𝑦 𝑙 |𝑥) 𝜋 ref (𝑦 𝑙 |𝑥)</td></tr><tr><td>|𝑥) |𝑥)</td><td colspan=\"4\">-𝛽𝐷 SeqKL (𝑥, 𝑦 𝑙 ; 𝜋 ref ||𝜋 𝜃 ) -𝛽𝐷 SeqKL (𝑥, 𝑦 𝑤 ; 𝜋 ref ||𝜋 𝜃 )</td></tr><tr><td/><td colspan=\"2\">𝓛 𝐓𝐃𝐏𝐎𝟐 𝜋 𝜃 ; 𝜋 ref = -𝔼 log 𝜎 𝛽 log</td><td colspan=\"2\">𝜋 𝜃 (𝑦 𝑤 |𝑥) 𝜋 ref (𝑦 𝑤 |𝑥)</td><td>-𝛽 log</td><td>𝜋 𝜃 (𝑦 𝑙 |𝑥) 𝜋 ref (𝑦 𝑙 |𝑥)</td></tr></table>", "type_str": "table"}, "TABREF1": {"num": null, "text": "Calculate the function δ(x, y w , y l ) = βD SeqKL (x, y l ;π ref ∥π θ ) -βD SeqKL (x, y w ; π ref ∥π θ )▷ Eq.14 13:θ ← θ + η∇ θ E (x,yw,y l )∼Dm [log σ (u(x, y w , y l ) -δ(x, y w , y l ))]Calculate the function δ 2 (x, y w , y l ) = βD SeqKL (x, y l ; π ref ∥π θ ) -sg (βD SeqKL (x, y w ; π ref ∥π θ ))", "html": null, "content": "<table><tr><td>12:</td><td/></tr><tr><td/><td>▷ Eq.15</td></tr><tr><td>14:</td><td>else if Method M is TDPO 2 then</td></tr><tr><td>15:</td><td>▷ Eq.18</td></tr><tr><td>16:</td><td/></tr></table>", "type_str": "table"}, "TABREF2": {"num": null, "text": "Comparison of DPO, TDPO1 and TDPO2 in terms of the trade-off between Alignment (accuracy) and Diversity (entropy) on the Anthropic HH dataset. The ↑ indicates higher values are preferable.", "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">Alignment Accuracy(%) ↑ Entropy ↑ Diversity</td></tr><tr><td>f-DPO(FKL)</td><td>54.71</td><td>4.708</td></tr><tr><td>DPO</td><td>59.43</td><td>3.196</td></tr><tr><td>TDPO1(ours)</td><td>60.08</td><td>4.727</td></tr><tr><td>TDPO2(ours)</td><td>67.33</td><td>4.915</td></tr></table>", "type_str": "table"}, "TABREF4": {"num": null, "text": "A.1. Proving the Relationship between Maximizing the Advantage Function and Enhancing the Expected ReturnsLemma A.1. Given two policies π and π, if for any state s t = [x, y <t ], E z∼π [A π ([x, y <t ], z)] ≥ 0, then we can conclude:", "html": null, "content": "<table/>", "type_str": "table"}, "TABREF5": {"num": null, "text": "E z∼π θ (•|[x,y <t ]) A π ref ([x, y <t ], z) -βD KL π θ (•|[x, y <t ])∥π ref (•|[x, y <t ]) E z∼π θ (•|[x,y <t ]) Q π ref ([x, y <t ], z) -V π ref ([x, y <t ]) + β log π ref (z|[x, y <t ]) π θ (z|[x, y <t ]) Qπ ref ([x,y <t ],z) π θ (z|[x, y <t ]) -V π ref ([x, y <t ]) βE z∼π θ (•|[x,y <t ]) log π ref (z|[x, y <t ])e 1 β Qπ ref ([x,y <t ],z) Z([x, y <t ]; β)π θ (z|[x, y <t ]) -V π ref ([x, y <t ]) + β log Z([x, y <t ]; β) -βD KL π θ (z|[x, y <t ]) π ref (z|[x, y <t ])e 1 β Qπ ref ([x,y <t ],z) Z([x, y <t ]; β) -V π ref ([x, y <t ]) + β log Z([x, y <t ]; β)(31)where Z([x, y <t ]; β) is the partition function:Z([x, y <t ]; β) = E z∼π ref (•|[x,y <t ]) exp 1 β Q π ref ([x, y <t ], z)", "html": null, "content": "<table><tr><td/><td/><td colspan=\"2\">Token-level Direct Preference Optimization</td></tr><tr><td>Proof.</td><td/><td/><td/></tr><tr><td>max π θ</td><td/><td/><td>(27)</td></tr><tr><td>= max π θ</td><td/><td/><td>(28)</td></tr><tr><td>= max π θ</td><td>βE z∼π θ (•|[x,y &lt;t ]) log</td><td>p(z|[x, y &lt;t ])e</td><td>1 β (29)</td></tr><tr><td>= max π θ</td><td/><td/><td>(30)</td></tr><tr><td>= max π θ</td><td/><td/><td/></tr></table>", "type_str": "table"}}}}