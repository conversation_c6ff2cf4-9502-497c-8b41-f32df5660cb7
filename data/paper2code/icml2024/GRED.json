{"paper_id": "GRED", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:43:26.260058Z"}, "title": "Recurrent Distance Filtering for Graph Representation Learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Antonio", "middle": [], "last": "Orvieto", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "He", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Graph neural networks based on iterative onehop message passing have been shown to struggle in harnessing the information from distant nodes effectively. Conversely, graph transformers allow each node to attend to all other nodes directly, but lack graph inductive bias and have to rely on ad-hoc positional encoding. In this paper, we propose a new architecture to reconcile these challenges. Our approach stems from the recent breakthroughs in long-range modeling provided by deep state-space models: for a given target node, our model aggregates other nodes by their shortest distances to the target and uses a linear RNN to encode the sequence of hop representations. The linear RNN is parameterized in a particular diagonal form for stable long-range signal propagation and is theoretically expressive enough to encode the neighborhood hierarchy. With no need for positional encoding, we empirically show that the performance of our model is comparable to or better than that of state-of-the-art graph transformers on various benchmarks, with a significantly reduced computational cost. Our code is open-source at https: //github.com/skeletondyh/GRED.", "pdf_parse": {"paper_id": "GRED", "_pdf_hash": "", "abstract": [{"text": "Graph neural networks based on iterative onehop message passing have been shown to struggle in harnessing the information from distant nodes effectively. Conversely, graph transformers allow each node to attend to all other nodes directly, but lack graph inductive bias and have to rely on ad-hoc positional encoding. In this paper, we propose a new architecture to reconcile these challenges. Our approach stems from the recent breakthroughs in long-range modeling provided by deep state-space models: for a given target node, our model aggregates other nodes by their shortest distances to the target and uses a linear RNN to encode the sequence of hop representations. The linear RNN is parameterized in a particular diagonal form for stable long-range signal propagation and is theoretically expressive enough to encode the neighborhood hierarchy. With no need for positional encoding, we empirically show that the performance of our model is comparable to or better than that of state-of-the-art graph transformers on various benchmarks, with a significantly reduced computational cost. Our code is open-source at https: //github.com/skeletondyh/GRED.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Graphs are ubiquitous for representing complex interactions between individual entities, such as in social networks (<PERSON> et al., 2009) , recommender systems (<PERSON> et al., 2018) and molecules (<PERSON><PERSON> et al., 2017) , and have thus attracted a lot of interest from researchers seeking to apply deep learning to graph data. Message passing neural networks (MPNNs) (<PERSON><PERSON> et al., 2017) have been the dominant approach in this field. These models iteratively update the representation of a target node by aggregating the representations of its neighbors. Despite progress in semi-supervised node classification tasks (Kipf & Welling, 2017; <PERSON><PERSON><PERSON> et al., 2018) , MPNNs have been shown to have difficulty in effectively harnessing the information of distant nodes (Alon & Yahav, 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2022b) . To reach a node that is k hops away from the target node, an MPNN needs at least k layers. As a result, the receptive field for the target node grows exponentially with k, including many duplicates of nodes that are close to the target node. The information from such an exponentially growing receptive field is compressed into a fixed-size representation, making it insensitive to the signals from distant nodes (a.k.a. oversquashing (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2023) ). This limitation may hinder the application of MPNNs to tasks that require long-range reasoning.", "cite_spans": [{"start": 116, "end": 135, "text": "(<PERSON> et al., 2009)", "ref_id": "BIBREF49"}, {"start": 158, "end": 177, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF63"}, {"start": 192, "end": 213, "text": "(<PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF22"}, {"start": 361, "end": 382, "text": "(<PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF22"}, {"start": 613, "end": 635, "text": "(Kipf & Welling, 2017;", "ref_id": "BIBREF34"}, {"start": 636, "end": 660, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF56"}, {"start": 763, "end": 783, "text": "(Alon & Yahav, 2021;", "ref_id": "BIBREF2"}, {"start": 784, "end": 806, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2022b)", "ref_id": null}, {"start": 1244, "end": 1266, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF53"}, {"start": 1267, "end": 1292, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Inspired by the success of attention-based transformer architectures in modeling natural languages (<PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON> et al., 2019) and images (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) , several recent works have adapted transformers for graph representation learning to address the aforementioned issue (<PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2023) . Graph transformers allow each node to attend to all other nodes directly through a global atten-tion mechanism, and therefore make the information flow between distant nodes easier. However, a naive global attention mechanism alone doesn't encode any structural information about the underlying graph. Hence, state-of-the-art graph transformers rely on ad hoc positional encoding (e.g., eigenvectors of the graph Laplacian) as extra features to incorporate the graph inductive bias. There is no consensus yet on the optimal type of positional encoding. Which positional encoding to use and its associated hyper-parameters need to be tuned carefully (<PERSON><PERSON><PERSON> et al., 2022) . Besides, while graph transformers have empirically shown improvement on some graph benchmarks compared with classical MPNNs, the former are much more computationally expensive (<PERSON><PERSON><PERSON><PERSON> et al., 2022b) .", "cite_spans": [{"start": 99, "end": 121, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF55"}, {"start": 122, "end": 142, "text": "<PERSON> et al., 2019)", "ref_id": "BIBREF13"}, {"start": 154, "end": 180, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF15"}, {"start": 300, "end": 319, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF62"}, {"start": 320, "end": 337, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF33"}, {"start": 338, "end": 356, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF7"}, {"start": 357, "end": 373, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF40"}, {"start": 1025, "end": 1048, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF47"}, {"start": 1227, "end": 1250, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Captivated by the above challenges and the need for powerful, theoretically sound and computationally efficient approaches to graph representation learning, we propose a new model, Graph Recurrent Encoding by Distance (GRED).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Each layer of our model consists of a permutation-invariant neural network (<PERSON><PERSON><PERSON> et al., 2017) and a linear recurrent neural network (<PERSON><PERSON><PERSON> et al., 2023b) that is parameterized in a particular diagonal form following the recent advances in state space models (<PERSON><PERSON> et al., 2022b; <PERSON> et al., 2023) .", "cite_spans": [{"start": 75, "end": 96, "text": "(<PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF64"}, {"start": 135, "end": 158, "text": "(<PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}, {"start": 263, "end": 281, "text": "(<PERSON><PERSON> et al., 2022b;", "ref_id": null}, {"start": 282, "end": 301, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "To generate the representation for a target node, our model categorizes all other nodes into multiple sets according to their shortest distances to the target node. The permutationinvariant neural network generates a representation for each set of nodes that share the same shortest distance to the target node, and then the linear recurrent neural network encodes the sequence of the set representations, starting from the set with the maximum shortest distance and ending at the target node itself. Since the order of the sequence is naturally encoded by the recurrent neural network, our model can encode the neighborhood hierarchy of the target node without the need for positional encoding. The architecture of GRED is illustrated in Figure 2 .", "cite_spans": [], "ref_spans": [{"start": 746, "end": 747, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "The diagonal parameterization of the linear RNN (<PERSON><PERSON><PERSON> et al., 2023b) has been shown to make long-range signal propagation more stable than a vanilla RNN, and enables our model to effectively harness the information of distant nodes. More specifically, it enables our model to directly learn the eigenvalues of the transition matrix, which control how fast the signals from distant nodes decay as they propagate towards the target node (see Figure 1 for an illustration), and at the same time allows efficient computation with parallel scans. Furthermore, while the use of a linear recurrent neural network is motivated by long-range signal propagation, we theoretically prove its expressive power in terms of injective functions over sequences, which is of independent interest, and based on that we conclude that our model is more expressive than 1-WL (<PERSON> et al., 2019) . We evaluate our model on a series of graph benchmarks to support its efficacy. The performance of our model is signif-icantly better than that of MPNNs, and is comparable to or better than that of state-of-the-art graph transformers while requiring no positional encoding and significantly reducing computation time.", "cite_spans": [{"start": 48, "end": 71, "text": "(<PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}, {"start": 856, "end": 873, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF61"}], "ref_spans": [{"start": 450, "end": 451, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "To summarize, the main contributions of our paper are as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We propose a principled new model for graph representation learning that can effectively and efficiently harness the information of distant nodes. The architecture is composed of permutation-invariant neural networks and linear recurrent neural networks with diagonal parameterization.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We theoretically prove that a linear recurrent neural network is able to express an injective mapping over sequences, which makes our architecture more expressive than 1-WL.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• Without the need for positional encoding, our model has achieved strong empirical performance on multiple widely used graph benchmarks, which is comparable to or better than that of state-of-the-art graph transformers, with higher training efficiency.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "We review below the literature on expanding MPNN's receptive field, including multi-hop MPNNs and graph transformers, as well as current trends in recurrent models for long-range reasoning on sequential data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Multi-hop MPNNs. Multi-hop MPNNs leverage the information of multiple hops for each layer. Among existing works, MixHop (<PERSON> et al., 2019) uses powers of the normalized adjacency matrix to access k-hop nodes. k-hop GNN (<PERSON><PERSON><PERSON><PERSON> et al., 2020) Information of the distant nodes is propagated to the target node through a linear RNN -specifically an LRU (<PERSON><PERSON><PERSON> et al., 2023b) . (b) Depiction of the GRED layer operation for two different target nodes. The gray rectangular boxes indicate the application of multiset aggregation. Finally, the new representation for the target node is computed from the RNN output through an MLP.", "cite_spans": [{"start": 120, "end": 147, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF1"}, {"start": 228, "end": 254, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": null}, {"start": 363, "end": 386, "text": "(<PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "Graph transformers. Graph transformers (<PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2023) have recently attracted a lot of interest because the global attention mechanism allows each node to directly attend to all other nodes. To bake in the graph structural information, graph transformers typically use positional encoding (<PERSON> et al., 2020; <PERSON><PERSON><PERSON><PERSON> et al., 2022a) as extra features. More specifically, Graphormer (<PERSON> et al., 2021) adds learnable biases to the attention matrix for different shortest distances. However, the sequential order of hops is not encoded into the model, and <PERSON>raphor<PERSON> still needs node degrees to augment node features. SAT (<PERSON> et al., 2022) and GraphTrans (<PERSON> et al., 2021) stack message passing layers and self-attention layers together to obtain local information before the global attention. <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2022) empirically compare different configurations of positional encoding, message passing and global attention. <PERSON> et al. (2023) suggest the use of resistance distance as relative positional encoding. <PERSON> et al. (2023) use learnable positional encoding initialized with random walk probabilities. <PERSON> et al. (2023) use MPNNs to encode graph patches generated by a graph clustering algorithm and apply MLP-Mixer (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) /ViT (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) to patch embeddings, but require node/patch positional encoding and selecting the number of patches.", "cite_spans": [{"start": 39, "end": 58, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF62"}, {"start": 59, "end": 75, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF60"}, {"start": 76, "end": 94, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF7"}, {"start": 95, "end": 117, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF47"}, {"start": 118, "end": 137, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF65"}, {"start": 138, "end": 154, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF40"}, {"start": 390, "end": 407, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF37"}, {"start": 408, "end": 430, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2022a)", "ref_id": null}, {"start": 480, "end": 499, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF62"}, {"start": 719, "end": 738, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF7"}, {"start": 754, "end": 771, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF60"}, {"start": 893, "end": 915, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF47"}, {"start": 1023, "end": 1042, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF65"}, {"start": 1115, "end": 1131, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF40"}, {"start": 1210, "end": 1226, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF31"}, {"start": 1323, "end": 1348, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF51"}, {"start": 1354, "end": 1380, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF15"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "State space models and linear RNNs. Efficient processing of long sequences is one of the paramount challenges in contemporary deep learning. Attention-based transform-ers (<PERSON><PERSON><PERSON><PERSON> et al., 2017) provide a scalable approach to sequential modeling but suffer from quadratically increasing inference/memory complexity as the sequence length grows. While many approaches exist to alleviate this issue, like efficient memory management (<PERSON><PERSON> et al., 2022; <PERSON><PERSON>, 2024) and architectural modifications (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2020) , the sequence length in modern large language models is usually kept to 2k/4k tokens for this reason (e.g. Llama2 (<PERSON><PERSON><PERSON><PERSON> et al., 2023) ). On top of high inference and memory cost, the attention mechanism often does not provide the correct inductive bias for longrange reasoning beyond text (<PERSON><PERSON> et al., 2021) . Due to the issues outlined above, the community has witnessed the rise of innovative recurrent alternatives to the attention mechanism, named state space models (SSMs). The first SSM, S4, was introduced by <PERSON><PERSON> et al. (2022a) based on the theory of polynomial signal approximation (<PERSON><PERSON> et al., 2020; 2023) and significantly surpassed all modern transformer variants on the challenging long-range benchmark (<PERSON><PERSON> et al., 2021) . Since then, a plethora of variants have been proposed (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) . Deep SSMs have reached outstanding results in various domains, including language (Fu et al., 2023) , vision (Nguyen et al., 2022) and audio (Goel et al., 2022) . At inference time, all SSMs coincide with a stack of linear RNNs, interleaved with position-wise MLPs and normalization layers. The linearity of the RNNs enables fast parallel processing using FFTs (Gu et al., 2022a) or parallel scans (Smith et al., 2023) . The connection between SSMs and linear RNNs is reinforced by Linear Recurrent Unit (LRU) (Orvieto et al., 2023b ) that matches the performance of deep SSMs. While SSMs rely on the discretization of a structured continuous-time latent dynamical system, LRU is directly designed for a discrete-time system. The main difference between LRU and a standard linear RNN is that LRU operates in the complex domain and its diagonal transition matrix is trained using polar parameterization for stable signal propagation.", "cite_spans": [{"start": 171, "end": 193, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF55"}, {"start": 430, "end": 448, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF11"}, {"start": 449, "end": 459, "text": "Dao, 2024)", "ref_id": "BIBREF10"}, {"start": 492, "end": 511, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF57"}, {"start": 512, "end": 532, "text": "<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF35"}, {"start": 533, "end": 552, "text": "<PERSON> et al., 2019;", "ref_id": "BIBREF8"}, {"start": 553, "end": 574, "text": "<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF3"}, {"start": 575, "end": 591, "text": "<PERSON> et al., 2020)", "ref_id": "BIBREF59"}, {"start": 707, "end": 729, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF54"}, {"start": 885, "end": 903, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF50"}, {"start": 1112, "end": 1129, "text": "<PERSON><PERSON> et al. (2022a)", "ref_id": null}, {"start": 1185, "end": 1202, "text": "(<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF24"}, {"start": 1203, "end": 1208, "text": "2023)", "ref_id": null}, {"start": 1309, "end": 1327, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF50"}, {"start": 1384, "end": 1405, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF30"}, {"start": 1406, "end": 1425, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF28"}, {"start": 1426, "end": 1445, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF48"}, {"start": 1446, "end": 1464, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF46"}, {"start": 1549, "end": 1566, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF21"}, {"start": 1576, "end": 1597, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF42"}, {"start": 1608, "end": 1627, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF23"}, {"start": 1828, "end": 1846, "text": "(<PERSON><PERSON> et al., 2022a)", "ref_id": null}, {"start": 1865, "end": 1885, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF48"}, {"start": 1977, "end": 1999, "text": "(<PERSON><PERSON><PERSON> et al., 2023b", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2."}, {"text": "In this section, we present the GRED layer, which is the building unit of our architecture. We start with some preliminary notations and then describe how our layer computes node representations. Finally, we analyze its computational complexity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "Preliminaries. Let G = (V, E) denote an undirected and unweighted graph, where V denotes the set of nodes and E denotes the set of edges. For any two nodes v, u ∈ V , we use d(v, u) to represent the shortest distance between v and u, and we let d(v, v) = 0. For each target node v, we categorize all other nodes into different hops according to their shortest distances to v:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "N k (v) = {u | d(v, u) = k} for k = 0, 1, . . . , K (1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "where K is the diameter of G or a hyper-parameter specified for the task in hand. {N k (v)} K k=1 can be obtained for every node v ∈ V by running the <PERSON>-<PERSON> algorithm (<PERSON>, 1962; <PERSON>, 1962) in parallel during data preprocessing and they are saved as masks.", "cite_spans": [{"start": 175, "end": 188, "text": "(<PERSON>, 1962;", "ref_id": "BIBREF20"}, {"start": 189, "end": 204, "text": "Warshall, 1962)", "ref_id": "BIBREF58"}], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "GRED layer. The input to the ℓ-th layer is a set of node representations {{h", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "(ℓ-1) v ∈ R d | v ∈ V }}. To compute the output representation h (ℓ)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "v of this layer for a generic target node v, the layer first generates a representation for each set of nodes that share the same shortest distance to v (grey dashed boxes in Figure 2 ):", "cite_spans": [], "ref_spans": [{"start": 182, "end": 183, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x (ℓ) v,k = AGG h (ℓ-1) u | u ∈ N k (v)", "eq_num": "(2)"}], "section": "Architecture", "sec_num": "3."}, {"text": "where {{•}} denotes a multiset, and AGG is an injective multiset function which we parameterize with two wide multilayer perceptrons (MLPs)1 , as usual in the literature (<PERSON><PERSON><PERSON> et al., 2017; <PERSON> et al., 2019) :", "cite_spans": [{"start": 170, "end": 191, "text": "(<PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF64"}, {"start": 192, "end": 208, "text": "<PERSON> et al., 2019)", "ref_id": "BIBREF61"}], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x (ℓ) v,k = MLP 2 u∈N k (v) MLP 1 h (ℓ-1) u ∈ R d . (3) These set representations (x (ℓ) v,0 , x (ℓ) v,1 , . . . , x", "eq_num": "(ℓ)"}], "section": "Architecture", "sec_num": "3."}, {"text": "v,K ) naturally form a sequence according to the shortest distances. Then we encode this sequence using a linear RNN:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "s (ℓ) v,k = As (ℓ) v,k-1 + Bx (ℓ) v,K-k for k = 0, . . . , K (4)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "where s v,K of the RNN. Note that as in Figure 2(b) , different nodes have different sequences to describe their respective neighborhoods, and the RNN computations for all nodes can be batched. Although for a particular target node, some edges between hop k (k ≥ 1) and hop k + 1 are omitted by converting its neighborhood into a sequence, those edges would be taken into account for other target nodes. Therefore, considering all node representations as a whole, our model preserves the full graph structural information. We theoretically prove the expressiveness of the linear RNN and our model in Section 4.", "cite_spans": [], "ref_spans": [{"start": 47, "end": 51, "text": "2(b)", "ref_id": null}], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "In our model, we parameterize the linear RNN in a particular diagonal form. Recall that, over the space of d s × d s non-diagonal real matrices, the set of non-diagonalizable (in the complex domain) matrices has measure zero (<PERSON><PERSON><PERSON>, 2013) . Hence, with probability one over random initializations, A is diagonalizable, i.e., A = V ΛV -1 , where Λ = diag(λ 1 , . . . , λ ds ) ∈ C ds×ds gathers the eigenvalues of A, and columns of V are the corresponding eigenvectors. Equation ( 4) is then equivalent to the following diagonal recurrence in the complex domain, up to a linear transformation of the hidden state s which can be merged with the output projection W out (Equation ( 7)):", "cite_spans": [{"start": 225, "end": 239, "text": "(<PERSON><PERSON><PERSON>, 2013)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "s (ℓ) v,k = Λs (ℓ) v,k-1 + W in x (ℓ) v,K-k (5)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "W in = V -1 B ∈ C ds×d .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "Unrolling the recurrence, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "s (ℓ) v,K = K k=0 Λ k W in x (ℓ) v,k .", "eq_num": "(6)"}], "section": "Architecture", "sec_num": "3."}, {"text": "Equation ( 6) can be thought of as a filter over the hops from the target node (Figure 1 ), and the filter weights are determined by the magnitudes of the eigenvalues Λ and the shortest distances to the target node. Following the modern literature on deep SSMs (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2022b) , we directly initialize (without loss of generality) the system in the diagonal form and have Λ and W in as trainable parameters2 . To guarantee stability (the eigenvalues should be bounded by the unit disk), we adopt the recently introduced LRU initialization (<PERSON><PERSON><PERSON> et al., 2023b ) that parameterizes the eigenvalues with log-transformed polar coordinates. Through directly learning eigenvalues Λ, our model learns to control the influence of signals from distant nodes on the target node, and thus addresses over-squashing caused by iterative 1-hop mixing. Another advantage of the diagonal linear recurrence is that it can leverage parallel scans (<PERSON><PERSON><PERSON><PERSON>, 1990; <PERSON> et al., 2023) to avoid computing s sequentially on modern hardware.", "cite_spans": [{"start": 261, "end": 281, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF28"}, {"start": 282, "end": 299, "text": "<PERSON><PERSON> et al., 2022b)", "ref_id": null}, {"start": 562, "end": 584, "text": "(<PERSON><PERSON><PERSON> et al., 2023b", "ref_id": null}, {"start": 954, "end": 970, "text": "(<PERSON><PERSON><PERSON><PERSON>, 1990;", "ref_id": "BIBREF5"}, {"start": 971, "end": 990, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF48"}], "ref_spans": [{"start": 87, "end": 88, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "The output representation h", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "(ℓ)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "v is generated by a non-linear transformation of the last hidden state s", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(ℓ) v,K : h (ℓ) v = MLP 3 ℜ W out s (ℓ) v,K", "eq_num": "(7)"}], "section": "Architecture", "sec_num": "3."}, {"text": "where W out ∈ C d×ds is a trainable weight matrix and ℜ[•] denotes the real part of a complex-valued vector. While sufficiently wide MLPs with one hidden layer can parameterize any non-linear mapping, following again the literature on state-space models we choose to place here a gated linear unit (GLU) (<PERSON><PERSON><PERSON> et al., 2017) : GLU(x) = (W 1 x) ⊙ σ(W 2 x), with σ the sigmoid function and ⊙ the element-wise product.", "cite_spans": [{"start": 304, "end": 326, "text": "(<PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "The final architecture is composed of stacking several of such layers described above. In practice, we merge MLP 1 in Equation ( 3) with the non-linear transformation in Equation (7) of the previous layer (or of the feature encoder) to make the entire architecture more compact. We add skip connections to both the MLP and the LRU and apply layer normalization to the input of each residual branch. The overall architecture is illustrated in Figure 2 (a).", "cite_spans": [], "ref_spans": [{"start": 449, "end": 450, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "Computational complexity. For each distance k, the complexity of aggregating the representations of nodes from N k (v) for every v ∈ V is that of one round of message passing, which is O(|E|). So the total complexity of Equation (3) for all nodes and distances is O(K|E|). In practice, since {N k (v)} K k=1 are pre-computed, Equation (3) for different k's can be performed in parallel to speed up the computation. The sequential computation of Equation ( 5) has total complexity O(K|V |). However, the linearity of the recurrence and the diagonal state transition matrix enable fast parallel scans to further improve the efficiency. In the above analysis, K is upper bounded by the graph diameter, which is usually much smaller than the number of nodes in real-world datasets. Even in the worst case where the diameter is large, we can keep the complexity of each layer tractable with a smaller constant K and still access the global information by ensuring the product of model depth and K is no smaller than the diameter. As a result of the compact and parallelizable architectural design, our model is highly efficient during training, as evidenced by our experimental results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Architecture", "sec_num": "3."}, {"text": "In this section, we theoretically analyze the expressive capabilities of the linear RNN (Equation ( 5)) and the overall model. Wide enough linear RNNs have been shown to be able to approximate convolutional filters (<PERSON> et al., 2022) , and model non-linear dynamic systems when interleaved with MLPs (<PERSON><PERSON><PERSON> et al., 2023a) . In the context of this paper, we are interested in whether the linear RNN can accurately encode the sequence of hop representations (generated by Equation (3)) that describes the neighborhood hierarchy of the target node. To answer this question, in the following, we prove that if the hidden state is large enough, a linear RNN can express an injective mapping over sequences:", "cite_spans": [{"start": 215, "end": 232, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF38"}, {"start": 299, "end": 322, "text": "(<PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Expressiveness Analysis", "sec_num": "4."}, {"text": "Theorem 4.1 (Injectivity of linear RNNs). Let {x v = (x v,0 , x v,1 , x v,2 , . . . , x v,Kv ) | v ∈ V } be a set of sequences (of different lengths K v ≤ K) of", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Expressiveness Analysis", "sec_num": "4."}, {"text": "vectors with a (possibly uncountable) set of features X ⊂ R d . Consider a diagonal linear complex-valued RNN with d s -dimensional Table 1 . Test classification accuracy (in percent) of our model in comparison with baselines. Performance of baselines is reported by the benchmark (<PERSON><PERSON><PERSON><PERSON> et al., 2023) or their original papers. \"-\" indicates the baseline didn't report its performance on that dataset. We follow the parameter budget ≈ 500K. ", "cite_spans": [{"start": 281, "end": 303, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF18"}], "ref_spans": [{"start": 138, "end": 139, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Expressiveness Analysis", "sec_num": "4."}, {"text": "v,k = Λs v,k-1 + W in x v,Kv-k , initialized at s v,-1 = 0 ∈ R ds for each v ∈ V . If d s ≥ (K + 1)d, then there exist Λ, W in such that the map R : (x v,0 , x v,1 , x v,2 , . . . , x v,K ) → s v,K (with zero right-padding if K v < K) is bijective. Moreover, if the set of RNN inputs has countable cardinality |X | = N ≤ ∞, then selecting d s ≥ d is sufficient", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "for the existence of an injective linear RNN mapping R.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "The proof can be found in Appendix A. Here we assume zero-padding for K v < K (for mini-batch training). If some nodes coincidentally have zero-valued features, we can select a special token which is not in the dictionary of node features as the padding token. In practice, such an operation is not necessary because node representations are first fed into an MLP before the linear RNN, which can learn to shift them away from zero.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "Based on Theorem 4.1, and the well-known conclusion that the parameterization given by Equation (3) can express an injective multiset function (<PERSON> et al., 2019) , we have the following corollary:", "cite_spans": [{"start": 143, "end": 160, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF61"}], "ref_spans": [], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "Corollary 4.2. A wide enough GRED layer is capable of expressing an injective mapping of the list", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "(h v , { {h u | u ∈ N 1 (v)} }, { {h u | u ∈ N 2 (v)} }, . . . , { {h u | u ∈ N Kv (v)} }) for each v ∈ V .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "This corollary in turn implies the following result:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "Corollary 4.3 (Expressiveness of GRED). When K > 1, one wide enough GRED layer is more expressive than any 1-hop message passing layer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "Proof. We note that 1-WL assumes an injective mapping of 1-hop neighborhood, i.e., (", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "h v , { {h u | u ∈ N 1 (v)} }), which", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "is a special case of GRED (K = 1). When K > 1, the output of one GRED layer at node v, given the injectivity of the linear RNN and AGG, provides a more detailed characterization of its neighborhood than 1-hop message passing. This means that if v's 1-hop neighborhood changes, the output of the GRED layer will also be different. Therefore, GRED is able to distinguish any two non-isomorphic graphs that are distinguishable by 1-WL. Moreover, GRED can distinguish two non-isomorphic graphs which 1-WL cannot (see Figure 7 in the appendix for an example).", "cite_spans": [], "ref_spans": [{"start": 520, "end": 521, "text": "7", "ref_id": null}], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "We note that <PERSON> et al. (2022) have already proven that multi-hop MPNNs are more expressive than 1-WL, but are upper bounded by 3-WL, which also applies to our model. Different from them, we achieve such expressiveness with a compact and parameter-efficient architecture (i.e., the number of parameters does not increase with K), which is of independent interest and bridges the gap between theory and practice.", "cite_spans": [{"start": 13, "end": 31, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "Model", "sec_num": null}, {"text": "In this section, we evaluate our model on widely used graph benchmarks (<PERSON><PERSON><PERSON><PERSON> et al., 2023; 2022b) . In all experiments, we train our model using the Adam optimizer with weight decay (<PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2019) and use the cosine annealing schedule with linear warm-up for the first 5% epochs. We compare our model against popular MPNNs including GCN (Kipf & Welling, 2017) , GAT (<PERSON><PERSON><PERSON> et al., 2018) , GIN (<PERSON> et al., 2019) , GatedGCN (Bresson & Laurent, 2017) , and multi-hop MPNN variants (<PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2023) , as well as several state-of-the-art graph transformers including Graphormer (<PERSON> et al., 2021) , SAT (<PERSON> et al., 2022) , GPS (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022) , Graph MLP-Mixer (<PERSON> et al., (<PERSON><PERSON><PERSON> et al., 2018) 0.384±0.007 GIN (<PERSON> et al., 2019) 0.387±0.015 GatedGCN (Bresson & Laurent, 2017) 0.282±0.015 PNA (<PERSON><PERSON><PERSON> et al., 2020) 0.188±0.004 KP-GIN (<PERSON> et al., 2022) 0.093±0.007 PathNN (<PERSON> et al., 2023) 0.090±0.004", "cite_spans": [{"start": 71, "end": 93, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF18"}, {"start": 94, "end": 100, "text": "2022b)", "ref_id": null}, {"start": 185, "end": 212, "text": "(<PERSON><PERSON><PERSON><PERSON> & Hutter, 2019)", "ref_id": "BIBREF39"}, {"start": 353, "end": 375, "text": "(Kipf & Welling, 2017)", "ref_id": "BIBREF34"}, {"start": 382, "end": 407, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF56"}, {"start": 414, "end": 431, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF61"}, {"start": 443, "end": 468, "text": "(Bresson & Laurent, 2017)", "ref_id": "BIBREF6"}, {"start": 499, "end": 518, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF19"}, {"start": 519, "end": 539, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF41"}, {"start": 540, "end": 564, "text": "<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF29"}, {"start": 643, "end": 662, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF62"}, {"start": 669, "end": 688, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF7"}, {"start": 695, "end": 718, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF47"}, {"start": 749, "end": 774, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF56"}, {"start": 791, "end": 808, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF61"}, {"start": 830, "end": 855, "text": "(Bresson & Laurent, 2017)", "ref_id": "BIBREF6"}, {"start": 872, "end": 892, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF9"}, {"start": 912, "end": 931, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF19"}, {"start": 951, "end": 972, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "SAN (<PERSON><PERSON><PERSON><PERSON> et al., 2021) 0.139±0.006 Graphormer (<PERSON> et al., 2021) 0.122±0.006 K-subgraph SAT (<PERSON> et al., 2022) 0.094±0.008 GPS (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022) 0.070±0.004 Graph MLP-Mixer (<PERSON> et al., 2023) 0.073±0.001 GRIT (<PERSON> et al., 2023) 0.059±0.002 GRED (Ours) 0.077±0.002", "cite_spans": [{"start": 4, "end": 26, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF36"}, {"start": 50, "end": 69, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF62"}, {"start": 97, "end": 116, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF7"}, {"start": 133, "end": 156, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF47"}, {"start": 185, "end": 202, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF31"}, {"start": 220, "end": 237, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "2023) and GRIT (<PERSON> et al., 2023) . We also measure the training time and memory consumption of GRED to demonstrate its high efficiency. We use three distinct colors to indicate the performance of our model, the best MPNN, and the best graph transformer. We detail the hyper-parameters used for our model in the appendix (Table 5 ). In Appendix B, we validate GRED's robustness to over-squashing and compare GRED with SPN (<PERSON><PERSON><PERSON> et al., 2022) .", "cite_spans": [{"start": 15, "end": 32, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF40"}, {"start": 421, "end": 442, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF0"}], "ref_spans": [{"start": 327, "end": 328, "text": "5", "ref_id": null}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "Benchmarking GNNs. We first evaluate our model on the node classification datasets: PATTERN and CLUSTER, and graph classification datasets: MNIST and CIFAR10 from (<PERSON><PERSON><PERSON><PERSON> et al., 2023) . To get the representation for the entire graph, we simply do average pooling over all node representations. Our model doesn't use any positional encoding. We train our model four times with different random seeds and report the average accuracy with standard deviation. The results are shown in Table 1 . From the table we see that graph transformers generally perform better than MPNNs. Among the four datasets, PATTERN models communities in social networks and all nodes are reachable within 3 hops, which we conjecture is why the performance gap between graph transformers and MPNNs is only marginal. For a more difficult task, like CIFAR10, that requires information from a relatively larger neighborhood, graph transformers work more effectively. GRED performs well on all four datasets and consistently outperforms MPNNs. Notably, on MNIST and CIFAR10, GRED achieves the best accuracy, outperforming state-of-the-art models Graph MLP-Mixer and GRIT, which validates that our model can effectively aggregate information beyond the local neighborhood.", "cite_spans": [{"start": 163, "end": 185, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF18"}], "ref_spans": [{"start": 489, "end": 490, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "ZINC 12K. Next, we report the test MAE of our model on ZINC 12K (<PERSON><PERSON><PERSON><PERSON> et al., 2023) . The average MAE 2 along with baseline performance from their original papers. From Table 2 we can observe that the performance of our model is significantly better than that of existing MPNNs. In particular, GRED outperforms other multi-hop MPNN variants (<PERSON> et al., 2022; <PERSON> et al., 2023) , which shows our architecture is more effective in aggregating multi-hop information. Comparing GRED with graph transformers, we find that it outperforms several graph transformer variants (SAN, Graphormer, and K-subgraph SAT) and approaches the state-of-the-art model. This is impressive given that our model doesn't require any positional encoding. These results evidence that our model can encode graph structural information through the natural inductive bias of recurrence.", "cite_spans": [{"start": 64, "end": 86, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF18"}, {"start": 344, "end": 363, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF19"}, {"start": 364, "end": 384, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF41"}], "ref_spans": [{"start": 105, "end": 106, "text": "2", "ref_id": "TABREF2"}, {"start": 178, "end": 179, "text": "2", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "Long Range Graph Benchmark. To further test the longrange modeling capability of GRED, we evaluate it on the Peptides-func and Peptides-struct datasets from (<PERSON><PERSON><PERSON><PERSON> et al., 2022b) . We follow the 500K parameter budget and train our model four times with different random seeds. The results are displayed in Table 3 . The performance of GCN, GINE and GatedGCN (marked with * ) comes from a recent report (<PERSON><PERSON><PERSON> et al., 2023) that extensively tuned their hyper-parameters with positional encoding. Performance of other baselines is reported by respective papers. We can observe that, even without positional encoding, GRED significantly outperforms all baselines except DRew+LapPE on Peptides-func, and its performance on Peptides-struct also matches that of the best graph transformer. Note that on Peptides-struct, DRew+LapPE performs worse than GRED. These results demonstrate the strong long-range modeling capability of our architecture itself. As a supplement, we test GRED+LapPE by concatenating Laplacian positional encoding with node features, and we find it slightly im- proves the performance. We leave the combination of more advanced positional encoding with GRED to future work.", "cite_spans": [{"start": 157, "end": 180, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022b)", "ref_id": null}, {"start": 404, "end": 427, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF52"}], "ref_spans": [{"start": 314, "end": 315, "text": "3", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "To illustrate how GRED can learn to preserve long-range information, we examine the eigenvalues learned by the linear RNN (i.e., Λ in Equation ( 5)) after training, as shown in Figure 3 . We observe from the figure that the eigenvalues are pushed close to 1 for the long-range task Peptides-func, which prevent the signals of distant nodes from decaying too fast. Compared with Peptides-func, CIFAR10 requires the model to utilize more information from the local neighborhood, so the magnitudes of the eigenvalues become smaller. Effect of K on performance. Recall that the length of recurrence K can be regarded as a hyper-parameter in GRED.", "cite_spans": [], "ref_spans": [{"start": 184, "end": 185, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "In Figure 4 , we show how different K values affect the performance of GRED on CIFAR10, ZINC and Peptides-func, keeping the depth and hidden dimension of the architecture unchanged (without positional encoding). On CIFAR10 and ZINC, while directly setting K as the diameter already outperforms classical MPNNs, we find that the optimal K value that yields the best performance lies strictly between 1 and the diameter. This may be because information that is too far away is less important for these two tasks (interestingly, the best K value for CIFAR10 is similar to the width of a convolutional kernel on a normal image). On Peptides-func, the performance is more monotonic with K. When K = 40, GRED outperforms the best graph transformer GRIT. We observe no further performance gain on Peptides-func when we increase K to 60. Comparing RNNs of different flavors. Finally, we highlight the necessity of the LRU component (Equation ( 5)) of GRED by replacing it with a vanilla RNN, a standard LSTM cell or 8-head self-attention. The performance of different variants is shown in Figure 5 . We use the same number of layers and K for all models and tune the learning rate, weight decay and dropout rate in the same grid. None of the variants use positional encoding. We can observe that GRED LSTM performs better than GRED RNN on CIFAR10 and Peptides-func. Since LSTM can alleviate the training instability of the vanilla RNN, the improvement of GRED LSTM over GRED RNN is particularly large on the long-range dataset Peptides-func. GRED Attn allows direct interaction with each hop and thus also yields good performance on Peptides-func. However, self-attention cannot provide good inductive bias because it cannot model the order of the hop sequence, which can explain why the performance of GRED Attn is the worst on ZINC. GRED LRU consistently outperforms the other variants, attributed to its advanced parameterization for stable signal propagation and great expressive power.", "cite_spans": [], "ref_spans": [{"start": 10, "end": 11, "text": "4", "ref_id": "FIGREF4"}, {"start": 1088, "end": 1089, "text": "5", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Experiments", "sec_num": "5."}, {"text": "In <PERSON><PERSON> of Theorem 4.1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Proof. For now, let us assume for ease of exposition that all sequences are of length K. Also, let us, for simplicity, omit the dependency on v ∈ V and talk about generic sequences.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "The proof simply relies on the idea of writing the linear recurrence in matrix form (<PERSON><PERSON> et al., 2022b; <PERSON><PERSON><PERSON> et al., 2023a) . Note that for a generic input x = (x 0 , x 1 , x 2 , . . . , x K ) ∈ R d×(K+1) , the recurrence output can be rewritten in terms of powers of Λ = diag(λ 1 , λ 2 , . . . , λ ds ) as follows:", "cite_spans": [{"start": 84, "end": 102, "text": "(<PERSON><PERSON> et al., 2022b;", "ref_id": null}, {"start": 103, "end": 125, "text": "<PERSON>vie<PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "s K = K k=0 Λ k W in x k .", "eq_num": "(8)"}], "section": "Conclusion", "sec_num": "6."}, {"text": "We now present sufficient conditions for the map R : (x 0 , x 1 , x 2 , . . . , x K ) → s K to be injective or bijective. The proof for bijectivity does not require the set of node features to be in a countable set, and it is simpler.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Bijective mapping. First, let us design a proper matrix W in ∈ R ds×d . We choose d s = (K + 1)d and set W in = I d×d ⊗ 1 (K+1)×1 . As a result, the RNN will independently process each dimension of the input with a sub-RNN of size (K + 1). The resulting s K ∈ R (K+1)d will gather each sub-RNN output by concatenation. We can then restrict our attention to the first dimension of the input sequence:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(s K ) 1:(K+1) = K k=0 diag(λ 1 , λ 2 , . . . , λ K+1 ) k 1 (K+1)×1 x k,1 .", "eq_num": "(9)"}], "section": "Conclusion", "sec_num": "6."}, {"text": "This sum can be written conveniently by multiplication using a <PERSON><PERSON><PERSON><PERSON> matrix:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(s K ) 1:(K+1) =      λ K 1 λ K-1 1 • • • λ 1 1 λ K 2 λ K-1 2 • • • λ 2 1 . . . . . . . . . . . . . . . λ K K+1 λ K-1 K+1 • • • λ K+1 1      x ← 0:K,1 .", "eq_num": "(10)"}], "section": "Conclusion", "sec_num": "6."}, {"text": "where x ← 0:K,1 is the input sequence (first dimension) in reverse order. The proof is concluded by noting that Vandermonde matrices of size (K + 1) × (K + 1) are full-rank since they have non-zero determinant 1≤i<j≤(K+1) (λ i -λ j ) ̸ = 0, under the assumption that all λ i are distinct. Note that one does not need complex eigenvalues to achieve this, both Λ and W in can be real. However, as discussed by <PERSON><PERSON><PERSON> et al. (2023a) , complex eigenvalues improve conditioning of the <PERSON><PERSON><PERSON><PERSON> matrix.", "cite_spans": [{"start": 408, "end": 430, "text": "<PERSON><PERSON><PERSON> et al. (2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Injective mapping. The condition for injectivity is that if x ̸ = x, then R(x) ̸ = R( x). In formulas,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "s K -ŝK = K k=0 Λ k W in (x k -xk ) ̸ = 0 (11)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Let us assume the state dimension coincides with the input dimension, and let us set W in = I d×d . Then, we have the condition:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "s K -ŝK = K k=0 Λ k (x k -xk ) ̸ = 0. (", "eq_num": "12"}], "section": "Conclusion", "sec_num": "6."}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Since Λ = diag(λ 1 , λ 2 , . . . , λ d ) is diagonal, we can study each component of s K -ŝK separately. We therefore require s K,i -ŝK,i = K k=0 λ k i (x k,i -xk,i ) ̸ = 0 ∀i ∈ {1, 2, . . . , d}.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "We can then restrict our attention to linear one-dimensional RNNs (i.e. filters) with one-dimensional input x ∈ R 1×(K+1) . We would like to choose λ ∈ C such that Note that dim(z ⊥ ) = K, so dim(Z ⊥ ) = K due to the countability assumption -in other words the Lebesgue measure vanishes: µ(Z ⊥ ; R K+1 ) = 0. If λ were an arbitrary vector, we would be done since we can pick it at random and with probability one λ / ∈ Z ⊥ . But λ is structured (lives on a 1-dimensional manifold), so we need one additional step.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Note that λ is parametrized by λ, and in particular R ∋ λ → λ ∈ R K+1 is a curve in R K+1 , we denote this as γ λ . Now, crucially, note that the support of γ λ is a smooth curved manifold for K > 1. In addition, crucially, 0 / ∈ γ λ . We are done: it is impossible for the γ λ curve to live in a K dimensional space composed of a union of hyperplanes; it indeed has to span the whole R K+1 , without touching the zero vector (see Figure 6 ). The reason why it spans the whole R K+1 comes from the Vandermonde determinant! Let {λ 1 , λ 2 , • • • , λ K+1 } be a set of K + 1 distinct λ values. The Vandermonde matrix", "cite_spans": [], "ref_spans": [{"start": 438, "end": 439, "text": "6", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "     λ K 1 λ K-1 1 • • • λ 1 1 λ K 2 λ K-1 2 • • • λ 2 1 . . . . . . . . . . . . . . . λ K K+1 λ K-1 K+1 • • • λ K+1 1     ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "has determinant 1≤i<j≤(K+1) (λ i -λ j ) ̸ = 0 -it's full rank, meaning that the vectors λ1 , λ2 , . . . , λK+1 span the whole R K+1 . Note that λ → λ is a continuous function, so even though certain λi might live on Z ⊥ there exists a value in between them which is not contained in Z ⊥ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "To validate the robustness of GRED to over-squashing, we consider the Tree-NeighborsMatch task proposed by Alon & Yahav (2021) . Following the same experimental setup as Alon & Yahav (2021) , we report the training accuracy of GRED in Table 6 to show how well GRED can harness long-range information to fit the data. As a comparison, we quote", "cite_spans": [{"start": 107, "end": 126, "text": "Alon & Yahav (2021)", "ref_id": "BIBREF2"}, {"start": 170, "end": 189, "text": "Alon & Yahav (2021)", "ref_id": "BIBREF2"}], "ref_spans": [{"start": 241, "end": 242, "text": "6", "ref_id": null}], "eq_spans": [], "section": "<PERSON>. Additional Results", "sec_num": null}, {"text": "Department of Computer Science, ETH Zürich", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "ELLIS Institute Tübingen, MPI-IS, Tübingen AI Center. Correspondence to: <PERSON><PERSON> <<EMAIL>>. Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "In practice, with just one hidden layer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "As done in all state-space models(<PERSON><PERSON> et al., 2022a;<PERSON> et al., 2023), we do not optimize over the complex numbers but instead parameterize, for instance, real and imaginary components of Win as real parameters. The imaginary unit i is then used to aggregate the two components in the forward pass.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "We thank the anonymous reviewers for their valuable feedback, which helped us improve the paper. <PERSON> acknowledges the financial support of the Hector Foundation. <PERSON><PERSON> would like to personally thank <PERSON><PERSON><PERSON> for her support during the stressful time before the deadline.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none of which we feel must be specifically highlighted here. the performance of GIN which uses the same multiset aggregation as GRED. For GIN, a network with r + 1 layers is trained for each tree depth in the original paper (Alon & Yahav, 2021) , while for GRED the number of layers is only around half of the tree depth, with an appropriate K > 1 to avoid under-reaching. Over-squashing starts to affect GIN at r = 4, preventing the model from effectively using distant information to perfectly fit the data. On the contrary, GRED is not affected by over-squashing across different tree depths.We further evaluate GRED on NCI1 and PROTEINS from TUDataset. We follow the experimental setup of SPN (<PERSON><PERSON><PERSON> et al., 2022) , and report the average test accuracy and standard deviation across 10 train/val/test splits, as shown in Table 7 . We use the same K for GRED as for SPN and cite the performance reported by the SPN paper (<PERSON><PERSON><PERSON> et al., 2022) . Our model generalizes well to TUDataset and shows good performance. Furthermore, GRED outperforms SPN (<PERSON><PERSON><PERSON> et al., 2022) with the same number of hops, which verifies that GRED is a better architecture for aggregating large neighborhoods. ", "cite_spans": [{"start": 365, "end": 385, "text": "(Alon & Yahav, 2021)", "ref_id": "BIBREF2"}, {"start": 838, "end": 859, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF0"}, {"start": 1066, "end": 1087, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF0"}, {"start": 1192, "end": 1213, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF0"}], "ref_spans": [{"start": 973, "end": 974, "text": "7", "ref_id": null}], "eq_spans": [], "section": "Impact Statement", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Shortest path networks for graph property prediction", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": ["I"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Learning on Graphs Conference", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>. Shortest path networks for graph property prediction. In Learning on Graphs Conference, 2022.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Higher-order graph convolutional architectures via sparsified neighborhood mixing", "authors": [{"first": "S", "middle": [], "last": "Abu-El-Haija", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Mixhop", "suffix": ""}], "year": 2019, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>: Higher-order graph convolutional ar- chitectures via sparsified neighborhood mixing. In ICML, 2019.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "On the bottleneck of graph neural networks and its practical implications", "authors": [{"first": "U", "middle": [], "last": "Alon", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> On the bottleneck of graph neural networks and its practical implications. In ICLR, 2021.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "The long-document transformer", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Long<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2004.05150"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>- former: The long-document transformer. arXiv preprint arXiv:2004.05150, 2020.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Matrix analysis", "authors": [{"first": "R", "middle": [], "last": "Bhatia", "suffix": ""}], "year": 2013, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> Matrix analysis. Springer Science & Business Media, 2013.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Prefix sums and their applications", "authors": [{"first": "G", "middle": ["E"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1990, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, G. E. Prefix sums and their applications, 1990.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Residual gated graph convnets", "authors": [{"first": "X", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1711.07553"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> gated graph convnets. arXiv preprint arXiv:1711.07553, 2017.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Structure-aware transformer for graph representation learning", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "O'bray", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, K. Structure-aware transformer for graph representation learning. In ICML, 2022.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Generating long sequences with sparse transformers", "authors": [{"first": "R", "middle": [], "last": "Child", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1904.10509"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, I. Gen- erating long sequences with sparse transformers. arXiv preprint arXiv:1904.10509, 2019.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Principal neighbourhood aggregation for graph nets", "authors": [{"first": "G", "middle": [], "last": "Corso", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>ò", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, P. Principal neighbourhood aggregation for graph nets. In NeurIPS, 2020.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Flashattention-2: Faster attention with better parallelism and work partitioning", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>-2: Faster attention with better paral- lelism and work partitioning. In ICLR, 2024.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Flashattention: Fast and memory-efficient exact attention with io-awareness", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>., <PERSON><PERSON>, A., and <PERSON><PERSON>, <PERSON><PERSON>- tention: Fast and memory-efficient exact attention with io-awareness. In NeurIPS, 2022.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Language modeling with gated convolutional networks", "authors": [{"first": "Y", "middle": ["N"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Fan", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Grangier", "suffix": ""}], "year": 2017, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, D. <PERSON>- gua<PERSON> modeling with gated convolutional networks. In ICML, 2017.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Pre-training of deep bidirectional transformers for language understanding", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M.-W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "NAACL", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, K. <PERSON>: Pre-training of deep bidirectional transformers for lan- guage understanding. In NAACL, 2019.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "On over-squashing in message passing neural networks: The impact of width, depth, and topology", "authors": [{"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, M. M. On over-squashing in message passing neural networks: The impact of width, depth, and topology. In ICML, 2023.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "A", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "X", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. An image is worth 16x16 words: Transformers for image recognition at scale. In ICLR, 2021.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Graph neural networks with learnable structural and positional representations", "authors": [{"first": "V", "middle": ["P"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> <PERSON>, X. Graph neural networks with learnable structural and positional representations. In ICLR, 2022a.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Long range graph benchmark", "authors": [{"first": "V", "middle": ["P"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Parviz", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Long range graph bench- mark. In NeurIPS, 2022b.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Benchmarking graph neural networks", "authors": [{"first": "V", "middle": ["P"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["K"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "JMLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. <PERSON>chmarking graph neural networks. JMLR, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "How powerful are k-hop message passing graph neural networks", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Li", "suffix": ""}, {"first": "A", "middle": [], "last": "Sarka<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>. How powerful are k-hop message passing graph neural net- works. In NeurIPS, 2022.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Algorithm 97: shortest path", "authors": [{"first": "R", "middle": ["W"], "last": "<PERSON>", "suffix": ""}], "year": 1962, "venue": "Communications of the ACM", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> 97: shortest path. Communications of the ACM, 1962.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Hungry hungry hippos: Towards language modeling with state space models", "authors": [{"first": "D", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": ["K"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Re", "suffix": ""}], "year": 2023, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, A., and <PERSON>, <PERSON><PERSON> hungry hippos: Towards language modeling with state space models. In ICLR, 2023.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Neural message passing for quantum chemistry", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["S"], "last": "Schoenholz", "suffix": ""}, {"first": "P", "middle": ["F"], "last": "<PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>yal<PERSON>", "suffix": ""}, {"first": "G", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, O<PERSON>, and <PERSON>, G. E. Neural message passing for quantum chem- istry. In ICML, 2017.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "It's raw! audio generation with state-space models", "authors": [{"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, K., <PERSON><PERSON>, A., Don<PERSON>, C., and Ré, C. It's raw! audio generation with state-space models. In ICML, 2022.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Hippo: Recurrent memory with optimal polynomial projections", "authors": [{"first": "A", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Recurrent memory with optimal polynomial projections. In NeurIPS, 2020.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Efficiently modeling long sequences with structured state spaces", "authors": [{"first": "A", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Re", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Efficiently modeling long sequences with structured state spaces. In ICLR, 2022a.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "On the parameterization and initialization of diagonal state space models", "authors": [{"first": "A", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> On the parameteri- zation and initialization of diagonal state space models. In NeurIPS, 2022b.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "How to train your hippo: State space models with generalized orthogonal basis projections", "authors": [{"first": "A", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> How to train your hippo: State space models with gener- alized orthogonal basis projections. In ICLR, 2023.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Diagonal state spaces are as effective as structured state spaces", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON>. <PERSON> state spaces are as effective as structured state spaces. In NeurIPS, 2022.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Dynamically rewired message passing with delay", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Dynamically rewired message passing with delay. In ICML, 2023.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Liquid structural state-space models", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T.-H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>ini", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>us", "suffix": ""}], "year": 2023, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, D. <PERSON> structural state-space models. In ICLR, 2023.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "A generalization of vit/mlp-mixer to graphs", "authors": [{"first": "X", "middle": [], "last": "He", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Lecun", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, X. A generalization of vit/mlp-mixer to graphs. In ICML, 2023.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Global self-attention as a replacement for graph convolution", "authors": [{"first": "M", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Subrama<PERSON>", "suffix": ""}], "year": 2022, "venue": "SIGKDD", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, D. Global self-attention as a replacement for graph convolution. In SIGKDD, 2022.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Pure transformers are powerful graph learners", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Min", "suffix": ""}, {"first": "S", "middle": [], "last": "Cho", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Hong", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and Hong, S. Pure transformers are powerful graph learners. In NeurIPS, 2022.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Semi-supervised classification with graph convolutional networks", "authors": [{"first": "T", "middle": ["N"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Welling", "suffix": ""}], "year": 2017, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> Semi-supervised classification with graph convolutional networks. In ICLR, 2017.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Reformer: The efficient transformer", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ł", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "A", "middle": [], "last": "Lev<PERSON>", "suffix": ""}], "year": 2020, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: The efficient transformer. In ICLR, 2020.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Rethinking graph transformers with spectral attention", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, P. Rethinking graph transformers with spectral attention. In NeurIPS, 2021.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Distance encoding: Design provably more powerful neural networks for graph representation learning", "authors": [{"first": "P", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. <PERSON> en- coding: Design provably more powerful neural networks for graph representation learning. In NeurIPS, 2020.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Approximation and optimization theory for linear continuous-time recurrent neural networks", "authors": [{"first": "Z", "middle": [], "last": "Li", "suffix": ""}, {"first": "J", "middle": [], "last": "Han", "suffix": ""}, {"first": "E", "middle": [], "last": "", "suffix": ""}, {"first": "W", "middle": [], "last": "Li", "suffix": ""}, {"first": "Q", "middle": [], "last": "", "suffix": ""}], "year": 2022, "venue": "JMLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Q. Approximation and op- timization theory for linear continuous-time recurrent neural networks. JMLR, 2022.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Decoupled weight decay regularization", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>pled weight decay regu- larization. In ICLR, 2019.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Graph inductive biases in transformers without message passing", "authors": [{"first": "L", "middle": [], "last": "Ma", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Lim", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": ["K"], "last": "Dokania", "suffix": ""}, {"first": "M", "middle": [], "last": "Coates", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S.-N", "middle": [], "last": "Lim", "suffix": ""}], "year": 2023, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S.-N. Graph inductive biases in transformers without message passing. In ICML, 2023.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Path neural networks: Expressive and accurate graph neural networks", "authors": [{"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["F"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Path neural networks: Expressive and accurate graph neural networks. In ICML, 2023.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "S4nd: Modeling images and videos as multidimensional signals using state spaces", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "G", "middle": ["W"], "last": "Downs", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["A"], "last": "Baccus", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, S. A., and <PERSON><PERSON>, C. S4nd: Modeling images and videos as multidimensional signals using state spaces. In NeurIPS, 2022.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "On the universality of linear recurrences followed by nonlinear projections", "authors": [{"first": "A", "middle": [], "last": "Orvieto", "suffix": ""}, {"first": "S", "middle": [], "last": "De", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Pa<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["L"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.11888"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. L. On the universality of linear recurrences followed by nonlinear projections. arXiv preprint arXiv:2307.11888, 2023a.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Resurrecting recurrent neural networks for long sequences", "authors": [{"first": "A", "middle": [], "last": "Orvieto", "suffix": ""}, {"first": "S", "middle": ["L"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Pa<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "De", "suffix": ""}], "year": 2023, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, R., and <PERSON>, <PERSON>. Resurrecting recurrent neural networks for long sequences. In ICML, 2023b.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Reinventing rnns for the transformer era", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Alcaide", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Arcadinho", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": ["K"], "last": "Gv", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.13048"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Rwkv: Reinventing rnns for the transformer era. arXiv preprint arXiv:2305.13048, 2023.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Recipe for a general, powerful, scalable graph transformer", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": ["P"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> for a general, powerful, scal- able graph transformer. In NeurIPS, 2022.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Simplified state space layers for sequence modeling", "authors": [{"first": "J", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Warrington", "suffix": ""}, {"first": "S", "middle": ["W"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. W. Simpli- fied state space layers for sequence modeling. In ICLR, 2023.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Social influence analysis in large-scale networks", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2009, "venue": "SIGKDD", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Z. Social influence analysis in large-scale networks. In SIGKDD, 2009.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Long range arena: A benchmark for efficient transformers", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S., and <PERSON><PERSON>, D. Long range arena: A benchmark for efficient transformers. In ICLR, 2021.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Mlp-mixer: An all-mlp architecture for vision", "authors": [{"first": "I", "middle": ["O"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Uszkoreit", "suffix": ""}], "year": 2021, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, J<PERSON>, et al. Mlp-mixer: An all-mlp architec- ture for vision. In NeurIPS, 2021.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Where did the gap go? reassessing the long-range graph benchmark", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Ritzert", "suffix": ""}, {"first": "E", "middle": [], "last": "Rosenbluth", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.00367"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Where did the gap go? reassessing the long-range graph benchmark. arXiv preprint arXiv:2309.00367, 2023.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Understanding over-squashing and bottlenecks on graphs via curvature", "authors": [{"first": "J", "middle": [], "last": "Topping", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": ["P"], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. M. Understanding over-squashing and bottlenecks on graphs via curvature. In ICLR, 2022.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Llama 2: Open foundation and finetuned chat models", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Stone", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Batra", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09288"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N., Batra, S., B<PERSON>ga<PERSON>, P., <PERSON>, S., et al. Llama 2: Open foundation and fine- tuned chat models. arXiv preprint arXiv:2307.09288, 2023.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Attention is all you need", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "Ł", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Attention is all you need. In NeurIPS, 2017.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Graph attention networks", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, Y. Graph attention networks. In ICLR, 2018.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Self-attention with linear complexity", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": ["Z"], "last": "Li", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Ma", "suffix": ""}, {"first": "", "middle": [], "last": "Lin<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2006.04768"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Self-attention with linear complexity. arXiv preprint arXiv:2006.04768, 2020.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "A theorem on boolean matrices", "authors": [{"first": "S", "middle": [], "last": "Warshall", "suffix": ""}], "year": 1962, "venue": "Journal of the ACM (JACM)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> A theorem on boolean matrices. Journal of the ACM (JACM), 1962.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Lite transformer with long-short range attention", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Han", "suffix": ""}], "year": 2020, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. <PERSON> transformer with long-short range attention. In ICLR, 2020.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Representing long-range context for graph neural networks with global attention", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Stoica", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, I. Representing long-range context for graph neural networks with global attention. In NeurIPS, 2021.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "How powerful are graph neural networks?", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Hu", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Jegelka", "suffix": ""}], "year": 2019, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, S. How powerful are graph neural networks? In ICLR, 2019.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Do transformers really perform badly for graph representation", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Cai", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "He", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, T.-Y. Do transformers really perform badly for graph representation? In NeurIPS, 2021.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Graph convolutional neural networks for web-scale recommender systems", "authors": [{"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "He", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Eksombatchai", "suffix": ""}, {"first": "W", "middle": ["L"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "SIGKDD", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, J. G<PERSON> convolutional neural net- works for web-scale recommender systems. In SIGKDD, 2018.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Deep sets", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ko<PERSON>ur", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["R"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>. <PERSON> sets. In NeurIPS, 2017.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "Rethinking the expressive power of gnns via graph biconnectivity", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "He", "suffix": ""}], "year": 2023, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Rethinking the expressive power of gnns via graph biconnectivity. In ICLR, 2023.", "links": null}}, "ref_entries": {"FIGREF0": {"text": "Figure 1. Illustration of the filtering effect on the neighborhood, induced by the linear RNN. The filter weight is determined by the eigenvalues Λ of the transition matrix and the shortest distance to the target node. We expand on this in Section 3.", "type_str": "figure", "uris": null, "fig_num": "1", "num": null}, "FIGREF1": {"text": "Figure 2. (a) Sketch of the architecture. MLPs and Layer Normalization operate independently at each node or aggregated multiset.Information of the distant nodes is propagated to the target node through a linear RNN -specifically an LRU(<PERSON><PERSON><PERSON> et al., 2023b). (b) Depiction of the GRED layer operation for two different target nodes. The gray rectangular boxes indicate the application of multiset aggregation. Finally, the new representation for the target node is computed from the RNN output through an MLP.", "type_str": "figure", "uris": null, "fig_num": null, "num": null}, "FIGREF2": {"text": "(ℓ) v,k ∈ R ds represents the hidden state of the RNN and s (ℓ) v,-1 = 0. A ∈ R ds×ds denotes the state transition matrix and B ∈ R ds×d is a matrix to transform the input of the RNN. Here in Equation (4) the RNN encoding starts from x (ℓ) v,K , proceeds from right to left, and ends at x (ℓ) v,0 , which corresponds to the signals from distant nodes propagating towards the target node. The neighborhood hierarchy of the target node v would then be encoded into the final hidden state s (ℓ)", "type_str": "figure", "uris": null, "fig_num": null, "num": null}, "FIGREF3": {"text": "Figure 3. Learned (complex) eigenvalues of the first GRED layer on CIFAR10 and Peptides-func.", "type_str": "figure", "uris": null, "fig_num": "3", "num": null}, "FIGREF4": {"text": "Figure 4. Effect of K on performance.", "type_str": "figure", "uris": null, "fig_num": "4", "num": null}, "FIGREF5": {"text": "Figure 5. Performance of GRED using RNNs of different flavors.", "type_str": "figure", "uris": null, "fig_num": "5", "num": null}, "FIGREF6": {"text": "Figure 6. Proof illustration for Theorem 4.1. The set Z ⊥ is depicted as union of hyperplanes, living in R K+1 and here sketched in three dimensions. The curve γ λ: λ → (1, λ, λ 2 , • • • , λ K) is shown as a blue line. The proof shows that, for λ ∈ R, the support of γ λ is not entirely contained in Z ⊥ .", "type_str": "figure", "uris": null, "fig_num": "6", "num": null}, "TABREF0": {"text": "", "type_str": "table", "content": "<table><tr><td>(a)</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"4\">Multiset aggregation</td><td colspan=\"5\">Linear Recurrent Network</td></tr><tr><td/><td/><td/><td colspan=\"2\">Encoder</td><td/><td/><td colspan=\"5\">(Nodes at same distance w.r.t. target)</td><td/><td/><td/><td/></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td>skip</td><td/><td/><td/><td/><td>skip</td><td/></tr><tr><td/><td/><td colspan=\"2\">LN</td><td colspan=\"2\">MLP</td><td/><td/><td>sum</td><td>LN</td><td>MLP</td><td/><td/><td>LN</td><td>LRU</td><td colspan=\"2\">MLP</td><td>Task Head</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"3\">x number of layers</td><td/><td/></tr><tr><td>(b)</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td/><td/><td/><td/><td>7</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td/><td>6</td><td>2 3</td><td>1</td><td>4 5</td><td>8</td><td>1</td><td>+</td><td>= MLP</td><td>W in</td><td>1</td><td>+ΛW in</td><td>2 3 4</td><td colspan=\"2\">+Λ 2 W in</td><td>6 7 8</td><td>=</td><td>Mutiset Aggreg.</td></tr><tr><td/><td/><td/><td/><td>9</td><td/><td/><td/><td/><td/><td/><td/><td>5</td><td/><td/><td>9</td></tr><tr><td/><td/><td/><td/><td>7</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td/><td/><td>4</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>6</td><td colspan=\"2\">3 1 2</td><td>5</td><td>9</td><td>8</td><td>5</td><td>+</td><td/><td>W in</td><td>5</td><td>+ΛW in</td><td>9 1</td><td colspan=\"2\">+Λ 2 W in</td><td>2 3 4</td><td>+Λ 3 W in</td><td>7 6</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td>8</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td>iteratively applies</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">MLPs to combine two consecutive hops and propagates</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">information towards the target node. Feng et al. (2022) the-</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">oretically analyze the expressive power of general k-hop</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">MPNNs and enhance it with subgraph information. These</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">works proved that higher-hop information can improve the</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">expressiveness of MPNNs, but they didn't address how to</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">preserve long-range information during propagation as we</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">do. SPN (Abboud et al., 2022) is shown to alleviate over-</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">squashing empirically. It first aggregates neighbors of the</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">same hop but simply uses weighted summation to combine</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">hop representations, which cannot guarantee the expressive-</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">ness of the model. On the contrary, we prove that our model,</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">capable of modeling long-range dependency, is also theoreti-</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">cally expressive. PathNN (Michel et al., 2023) encodes each</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">individual path that emanates from a node and aggregates</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">these paths to compute the node representation. DRew (Gut-</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">teridge et al., 2023) gradually aggregates more hops at each</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td colspan=\"6\">layer and allows skip connections between different nodes.</td></tr></table>", "html": null, "num": null}, "TABREF2": {"text": "Test MAE on ZINC 12K with parameter budget ≈ 500K.", "type_str": "table", "content": "<table><tr><td>Model</td><td>Test MAE ↓</td></tr><tr><td>GCN (Kipf &amp; Welling, 2017)</td><td>0.278±0.003</td></tr><tr><td>GAT</td><td/></tr></table>", "html": null, "num": null}, "TABREF3": {"text": "Test performance on Peptides-func/struct.", "type_str": "table", "content": "<table><tr><td>Model</td><td>Peptides-func Test AP ↑</td><td>Peptides-struct Test MAE ↓</td></tr><tr><td>GCN  *</td><td colspan=\"2\">0.6860±0.0050 0.2460±0.0007</td></tr><tr><td>GINE  *</td><td colspan=\"2\">0.6621±0.0067 0.2473±0.0017</td></tr><tr><td>GatedGCN  *</td><td colspan=\"2\">0.6765±0.0047 0.2477±0.0009</td></tr><tr><td>PathNN</td><td colspan=\"2\">0.6816±0.0026 0.2540±0.0046</td></tr><tr><td>DRew</td><td colspan=\"2\">0.6996±0.0076 0.2781±0.0028</td></tr><tr><td>DRew+LapPE</td><td colspan=\"2\">0.7150±0.0044 0.2536±0.0015</td></tr><tr><td>SAN+LapPE</td><td colspan=\"2\">0.6384±0.0121 0.2683±0.0043</td></tr><tr><td>GPS</td><td colspan=\"2\">0.6535±0.0041 0.2500±0.0005</td></tr><tr><td colspan=\"3\">Graph-MLPMixer 0.6970±0.0080 0.2475±0.0015</td></tr><tr><td>GRIT</td><td colspan=\"2\">0.6988±0.0082 0.2460±0.0012</td></tr><tr><td>GRED (Ours)</td><td colspan=\"2\">0.7085±0.0027 0.2503±0.0019</td></tr><tr><td>GRED+LapPE</td><td colspan=\"2\">0.7133±0.0011 0.2455±0.0013</td></tr><tr><td colspan=\"3\">and standard deviation of four runs with different random</td></tr><tr><td colspan=\"2\">seeds are shown in Table</td><td/></tr></table>", "html": null, "num": null}, "TABREF4": {"text": "Average training time per epoch and GPU memory consumption for GRIT and GRED.", "type_str": "table", "content": "<table><tr><td>Model</td><td colspan=\"3\">ZINC 12K CIFAR10 Peptides-func</td></tr><tr><td>GRIT</td><td>23.9s 1.9GB</td><td>244.4s 4.6GB</td><td>225.6s 22.5GB</td></tr><tr><td>GRED</td><td>3.7s 1.5GB</td><td>27.8s 1.4GB</td><td>158.9s 18.5GB</td></tr><tr><td>Speedup</td><td>6.5×</td><td>8.8×</td><td>1.4×</td></tr></table>", "html": null, "num": null}}}}