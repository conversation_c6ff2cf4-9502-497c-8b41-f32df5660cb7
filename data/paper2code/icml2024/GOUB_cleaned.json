{"paper_id": "GOUB", "title": "Image Restoration Through Generalized Ornstein-Uhlenbeck Bridge", "abstract": "Diffusion models exhibit powerful generative capabilities enabling noise mapping to data via reverse stochastic differential equations. However, in image restoration, the focus is on the mapping relationship from low-quality to high-quality images. Regarding this issue, we introduce the Generalized Ornstein-Uhlenbeck Bridge (GOUB) model. By leveraging the natural mean-reverting property of the generalized OU process and further eliminating the variance of its steady-state distribution through the Doo<PERSON>'s h-transform, we achieve diffusion mappings from point to point enabling the recovery of high-quality images from low-quality ones. Moreover, we unravel the fundamental mathematical essence shared by various bridge models, all of which are special instances of GOUB and empirically demonstrate the optimality of our proposed models. Additionally, we present the corresponding Mean-ODE model adept at capturing both pixel-level details and structural perceptions. Experimental outcomes showcase the state-of-the-art performance achieved by both models across diverse tasks, including inpainting, deraining, and super-resolution. Code is available at https: //github.com/Hammour-steak/GOUB.", "pdf_parse": {"paper_id": "GOUB", "abstract": [{"text": "Diffusion models exhibit powerful generative capabilities enabling noise mapping to data via reverse stochastic differential equations. However, in image restoration, the focus is on the mapping relationship from low-quality to high-quality images. Regarding this issue, we introduce the Generalized Ornstein-Uhlenbeck Bridge (GOUB) model. By leveraging the natural mean-reverting property of the generalized OU process and further eliminating the variance of its steady-state distribution through the Doo<PERSON>'s h-transform, we achieve diffusion mappings from point to point enabling the recovery of high-quality images from low-quality ones. Moreover, we unravel the fundamental mathematical essence shared by various bridge models, all of which are special instances of GOUB and empirically demonstrate the optimality of our proposed models. Additionally, we present the corresponding Mean-ODE model adept at capturing both pixel-level details and structural perceptions. Experimental outcomes showcase the state-of-the-art performance achieved by both models across diverse tasks, including inpainting, deraining, and super-resolution. Code is available at https: //github.com/Hammour-steak/GOUB.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Image restoration involves the restoring of high-quality (HQ) images from their low-quality (LQ) version (Banham & Katsaggelos, 1997; <PERSON> et al., 1988; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2023b) , which is often characterized as an ill-posed inverse problem due to the loss of crucial information during the degradation from high-quality images to low-quality images. It encompasses a suite of classical tasks, including image deraining (<PERSON>, 2017; <PERSON> et al., 2020; <PERSON> et al., 2022) , denoising (<PERSON> et al., 2018a; <PERSON> et al., 2022; <PERSON><PERSON> & Cho, 2022; <PERSON> et al., 2023a) , deblurring (<PERSON> et al., 2007; <PERSON> et al., 2023) , inpainting (<PERSON> et al., 2023; <PERSON> et al., 2023b) , and super-resolution (<PERSON> et al., 2015; <PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) , among others.", "section": "Introduction", "sec_num": "1."}, {"text": "Diffusion models (<PERSON><PERSON><PERSON><PERSON> et al., 2015; <PERSON> et al., 2020; Song & Ermon, 2019; <PERSON> et al., 2021b; <PERSON><PERSON><PERSON> et al., 2022) have also been applied to image restoration, yielding favorable results (<PERSON> <PERSON>, 2021; <PERSON> et al., 2023; <PERSON> et al., 2022; <PERSON> et al., 2024) . They mainly follow the standard forward process, diffusing images to pure noise and using low-quality images as conditions to facilitate the generation process of high-quality images (<PERSON> & Ni<PERSON>l, 2021; <PERSON>, 2021; <PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2022b; a; <PERSON> et al., 2023) . However, these approaches require the integration of substantial prior knowledge specific to each task such as degradation matrices, limiting their universality.", "section": "Introduction", "sec_num": "1."}, {"text": "Furthermore, some studies have attempted to establish a point-to-point mapping from low-quality to high-quality images, learning the general degradation and restoration process and thus circumventing the need for additional prior information for modeling specific tasks (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2024) . In terms of diffusion models, this mapping can be realized through the bridge (<PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2023a) , a stochastic process with fixed starting and ending points. By assigning high-quality and low-quality images to the starting and ending points, and initiating with the low-quality images, high-quality images can be obtained by applying the reverse diffusion process, thereby enabling image restoration. However, some bridge models face challenges in learning likelihoods (<PERSON> et al., 2022) , necessitating reliance on cumbersome iterative approximation methods (<PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON> et al., 2024) , which pose significant constraints in practical applications; others do not consider the selection of diffusion process and ignore the optimality of diffusion process (<PERSON> et al., 2023a; <PERSON> et al., 2023; <PERSON> et al., 2024) , thus may introducing unnecessary costs and limiting the performance of the model. This paper proposed a novel image restoration bridge model, the Generalized Ornstein-Uhlenbeck Bridge (GOUB), de-picted in Figure 1 . Owing to the mean-reverting properties of the Generalized Ornstein-Uhlenbeck (GOU) process, it gradually diffuses the HQ image into a noisy LQ state (denoted as x T + λϵ in Figure 1 ). By applying Doob's h-transform on GOU, we modify the diffusion process to eliminate noise on x T to directly bridge the HQ image and its LQ counterpart. The model initiates a point-to-point forward diffusion process and learns its reverse through maximum likelihood estimation, thereby ensuring it can restore a low-quality image to the corresponding high-quality image avoiding the limitation of generality and costly iterative approximation. Our main contributions can be summarized as follows:", "section": "Introduction", "sec_num": "1."}, {"text": "• We introduce a novel image restoration bridge model GOUB which eliminates variance of the ending point on the GOU process, directly connecting the high and low-quality images and is particularly expressive in deep visual features and diversity.", "section": "Introduction", "sec_num": "1."}, {"text": "• Benefiting from the distinctive features of the parameterization mechanism, we introduce the corresponding Mean-ODE model, demonstrating a strong ability to capture pixel-level details and structural perceptions.", "section": "Introduction", "sec_num": "1."}, {"text": "• We uncover the mathematical essence of several bridge models, all of which are special cases of the GOUB, and empirically demonstrate the optimality of our proposed models.", "section": "Introduction", "sec_num": "1."}, {"text": "• Our model has achieved state-of-the-art results on numerous image restoration tasks, such as inpainting, deraining, and super-resolution.", "section": "Introduction", "sec_num": "1."}, {"text": "The score-based diffusion model (<PERSON><PERSON><PERSON> et al., 2015; <PERSON> et al., 2020; <PERSON> et al., 2021b ) is a category of generative model that seamlessly transitions data into noise via a diffusion process and generates samples by learning and adapting the reverse process (<PERSON>, 1982) . Assuming a dataset consists of n dimensional independent identically distributed (i.i.d.) samples, following an unknown distribution denoted by p(x 0 ). The time-dependent forward process of the diffusion model can be described by the following SDE:", "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "EQUATION", "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "where f : R n → R n is the drift coefficient, g t : R → R is the scalar diffusion coefficient and w t denotes the standard Brownian motion. Typically, p(x 0 ) evolves over time t from 0 to a sufficiently large T into p(x T ) through the SDE, such that p(x T ) will approximate a standard Gaussian distribution p prior (x). Meanwhile, the forward SDE has a corresponding reverse time SDE (<PERSON>, 1982) whose closed form is given by:", "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "dx t = f (x t , t) -g 2 t ∇ xt log p(x t ) dt + g t dw t . (2)", "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "Starting from time T , p(x T ) can progressively transform to p(x 0 ) by traversing the trajectory of the reverse SDE.", "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "The score ∇ xt log p(x t ) can generally be parameterized as s θ (x t , t) and employ conditional score matching (<PERSON>, 2011) as the loss function for training:", "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "EQUATION", "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "where λ(t) serves as a weighting function, and if selected as g 2 t that yields a more optimal upper bound on the negative log-likelihood (<PERSON> et al., 2021a) . The second line is actually the most commonly used, as the conditional probability p(x t | x 0 ) is generally accessible. Ultimately, one can sample x T from the prior distribution p(x T ) ≈ p prior (x) and obtain the x 0 through the numerical solution of Equation (2) via iterative steps, thereby completing the generation process.", "section": "Score-based Diffusion Model", "sec_num": "2.1."}, {"text": "The Generalized Ornstein-Uhlenbeck (GOU) process is the time-varying OU process (<PERSON>, 1988) . It is a stationary Gaussian-Markov process, whose marginal distribution gradually tends towards a stable mean and variance over time. The GOU process is generally defined as follows:", "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "EQUATION", "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "where µ is a given state vector, θ t denotes a scalar drift coefficient and g t represents the diffusion coefficient. At the same time, we require θ t , g t to satisfy the specified relationship 2λ 2 = g 2 t /θ t , where λ 2 is a given constant scalar. As a result, its transition probability possesses a closed-form analytical solution:", "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "p (x t | x s ) = N ( ms:t , σ2 s:t I) = N µ + (x s -µ) e -θs:t , g 2 t 2θ t", "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "1 -e -2 θs:t I , θs:t = t s θ z dz.", "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "(5)", "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "A simple proof is provided in Appendix C. For the sake of simplicity in subsequent representations, we denote θ0:t and σ0:t as θt and σt respectively. Consequently, p(x t ) will steadily converge towards a Gaussian distribution with the mean of µ and the variance of λ 2 as time t progresses meaning that it exhibits the mean-reverting property. ", "section": "Generalized Ornstein-Uhlenbeck process", "sec_num": "2.2."}, {"text": "<PERSON><PERSON>'s h-transform (Särkkä & Solin, 2019 ) is a mathematical technique applied to stochastic processes. It involves transforming the original process by incorporating a specific h-function into the drift term of the SDE, modifying the process to pass through a predetermined terminal point. More precisely, given the SDE (1), if it is desired to pass through the given fixed point x T at t = T , an additional drift term must be incorporated into the original SDE:", "section": "<PERSON><PERSON>'s h-transform", "sec_num": "2.3."}, {"text": "dx t = f (x t , t) + g 2 t h(x t , t,", "section": "<PERSON><PERSON>'s h-transform", "sec_num": "2.3."}, {"text": "x T , T ) dt + g t dw t , (6) where h(x t , t, x T , T ) = ∇ xt log p(x T | x t ) and x 0 starts from p (x 0 | x T ). A simple proof can be found in Appendix D. In comparison to (1), the marginal distribution of ( 6) is conditioned on x T , with its forward conditional probability density given by p(x t | x 0 , x T ) satisfying the forward Kolmogorov equation that is defined by (6). Intuitively, p(x T | x 0 , x T ) = 1 at t = T , ensuring that the SDE invariably passes through the specified point x T for any initial state x 0 .", "section": "<PERSON><PERSON>'s h-transform", "sec_num": "2.3."}, {"text": "The GOU process (4) is characterized by mean-reverting properties that if we consider the initial state x 0 to represent a high-quality image and the corresponding low-quality image x T = µ as the final condition, then the high-quality image will gradually converge to a Gaussian distribution with the low-quality image as its mean and a stable variance λ 2 . This naturally connects some information between high and low-quality images, offering an inherent advantage in image restoration. However, the initial state of the reverse process necessitates the artificial addition of noise to lowquality images, resulting in certain information loss and thus affecting the performance (<PERSON><PERSON> et al., 2023a) .", "section": "GOUB", "sec_num": "3."}, {"text": "In actuality, we are more focused on the connections between points (<PERSON> et al., 2022; <PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2024) in image restoration. Coincidentally, the <PERSON><PERSON>'s h-transform technique can modify an SDE such that it passes through a specified x T at terminal time T . Accordingly, it is crucial to note that the application of the h-transform to the GOU process effectively eliminates the impact of terminal noise, directly bridging a point-to-point relationship between highquality and low-quality images.", "section": "GOUB", "sec_num": "3."}, {"text": "Applying the h-transform, we can readily derive the forward process of the GOUB, leading to the following proposition: Proposition 3.1. Let x t be a finite random variable describing by the given generalized Ornstein-Uhlenbeck process (4), suppose x T = µ, the evolution of its marginal distribution p(x t | x T ) satisfies the following SDE:", "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "dx t = θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t )dt + g t dw t . (7)", "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "Additionally, the forward transition p(x t | x 0 , x T ) is given by:", "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "p(x t | x 0 , x T ) = N ( m′ t , σ′2 t I), m′ t = e -θt σ2 t:T σ2 T x 0 + 1 -e -θt σ2 t:T σ2 T + e -2 θt:T σ2 t σ2 T x T σ′2 t = σ2 t σ2 t:T σ2 T (8)", "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "The derivation of the proposition is provided in the Appendix A.1. With Proposition 3.1, there is no need to perform multi-step forward iteration using the SDE; instead, we can directly use its closed-form solution for one-step forward sampling.", "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "Similarly, applying the previous SDE theory enables us to easily derive the reverse process, which leads to the following Proposition 3.2:", "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "Proposition 3.2. The reverse SDE of equation ( 7) has a marginal distribution p(x t | x T ), and is given by:", "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "EQUATION", "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "and exists a probability flow ODE:", "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "EQUATION", "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "We are capable of initiating from a low-quality image x T and proceeding to utilize Euler sampling solving the reverse SDE or ODE for restoration purposes.", "section": "Forward and backward process", "sec_num": "3.1."}, {"text": "The score term ∇ xt log p(x t | x T ) can be parameterized by a neural network s θ (x t , x T , t) and can be estimated using the loss function (3). Unfortunately, training the score function for SDEs generally presents a significant challenge. Nevertheless, since the analytical form of GOUB is directly obtainable, we will introduce the use of maximum likelihood for training, which yields a more stable loss function.", "section": "Training object", "sec_num": "3.2."}, {"text": "We first discretize the continuous time interval [0, T ] into N sufficiently fine-grained intervals in a reasonable manner, denoted as {x t } t∈[0,N ] , x N = x T . We are concerned with maximizing the log-likelihood, which leads us to the following proposition:", "section": "Training object", "sec_num": "3.2."}, {"text": "Proposition 3.3. Let x t be a finite random variable describing by the given generalized Ornstein-<PERSON>beck process (4), for a fixed x T , the expectation of log-likelihood", "section": "Training object", "sec_num": "3.2."}, {"text": "E p(x0) [log p θ (x 0 | x T )", "section": "Training object", "sec_num": "3.2."}, {"text": "] possesses an Evidence Lower Bound (ELBO):", "section": "Training object", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Training object", "sec_num": "3.2."}, {"text": "Assuming", "section": "Training object", "sec_num": "3.2."}, {"text": "p θ (x t-1 | x t , x T ) is a Gaussian distribution with a constant variance N (µ θ,t-1 , σ 2 θ,t-1 I)", "section": "Training object", "sec_num": "3.2."}, {"text": ", maximizing the ELBO is equivalent to minimizing:", "section": "Training object", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Training object", "sec_num": "3.2."}, {"text": ")", "section": "Training object", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Training object", "sec_num": "3.2."}, {"text": "where,", "section": "Training object", "sec_num": "3.2."}, {"text": "a = e -θt-1:t σ2 t:T σ2 t-1:T , b = 1 σ2 T (1 -e -θt )σ 2 t:T + e -2 θt:T σ2 t -(1 -e -θt-1 )σ 2 t-1:T + e -2 θt-1:T σ2 t-1 a", "section": "Training object", "sec_num": "3.2."}, {"text": "The derivation of the proposition is provided in the Appendix A.2. With Proposition 3.3, we can easily construct the training objective. In this work, we try to parameterized µ θ,t-1 from differential of SDE which can be derived from equation ( 9):", "section": "Training object", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Training object", "sec_num": "3.2."}, {"text": "where ϵ t ∼ N (0, dtI), therefore:", "section": "Training object", "sec_num": "3.2."}, {"text": "µ θ,t-1 =x t -θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t ) + g 2 t ∇ xt log p θ (x t | x T ), σ θ,t-1 =g t . (15)", "section": "Training object", "sec_num": "3.2."}, {"text": "Inspired by conditional score matching, we can parameterize noise as ϵ θ (x t , x T , t), thus the score ∇ xt log p θ (x t | x T ) can be represented as -ϵ θ (x t , x T , t)/σ ′ t . In addition, during our empirical research, we found that utilizing L1 loss yields enhanced image reconstruction outcomes (<PERSON>, 2004; <PERSON><PERSON> et al., 2009) . This approach enables the model to learn pixel-level details more easily, resulting in markedly improved visual quality. Therefore, the final training object is:", "section": "Training object", "sec_num": "3.2."}, {"text": "EQUATION", "section": "Training object", "sec_num": "3.2."}, {"text": "Consequently, if we obtain the optimal ϵ * θ (x t , x T , t), we can compute the score", "section": "Training object", "sec_num": "3.2."}, {"text": "∇ xt log p(x t | x T ) ≈ -ϵ * θ (x t ,", "section": "Training object", "sec_num": "3.2."}, {"text": "x T , t)/σ ′ t for reverse process. Starting from a lowquality image x T , we can recover x 0 by using Equation ( 9) to perform reverse iteration.", "section": "Training object", "sec_num": "3.2."}, {"text": "Unlike normal diffusion models, our parameterization of the mean µ θ,t-1 is derived from the differential of SDE which effectively combines the characteristics of discrete diffusion models and continuous score-based generative models. In the reverse process, the value of each sampling step will approximated to the true mean during training. Therefore, we propose a Mean-ODE model, which omits the Brownian drift term:", "section": "Mean-ODE", "sec_num": "3.3."}, {"text": "EQUATION", "section": "Mean-ODE", "sec_num": "3.3."}, {"text": "To simplify the expression, we use GOUB to represent the GOUB (SDE) sampling model and Mean-ODE to represent the GOUB (Mean-ODE) sampling model. Our following experiments have demonstrated that the Mean-ODE is more effective than the corresponding Score-ODE at capturing the pixel details and structural perceptions of images, playing a pivotal role in image restoration tasks. Concurrently, the SDE model ( 9) is more focused on deep visual features and diversity.", "section": "Mean-ODE", "sec_num": "3.3."}, {"text": "We conduct experiments under three popular image restoration tasks: image inpainting, image deraining, and image super-resolution. Four metrics are employed for the model Image Inpainting. Image inpainting involves filling in missing or damaged parts of an image, to restore or enhance the overall visual effect of the image. We have selected the CelebA-HQ 256×256 datasets (<PERSON><PERSON><PERSON> et al., 2018) for both training and testing with 100 thin masks. We compare our models with several current baseline inpainting approaches such as PromptIR (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , DDRM (<PERSON><PERSON> et al., 2022) and IR-SDE (<PERSON><PERSON> et al., 2023a) . The relevant experimental results are shown in the Table 1 and Figure 2. It is observed that the two proposed models achieved stateof-the-art results in their respective areas of strength and also delivered highly competitive outcomes on other metrics. From a visual perspective, our model excels in capturing details such as eyebrows, eyes, and image backgrounds.", "section": "Experiments", "sec_num": "4."}, {"text": "Image Deraining. We have selected the Rain100H datasets (<PERSON> et al., 2017) for our training and testing, which includes 1800 pairs of training data and 100 images for testing. It is important to note that in this task, similar to other deraining models, we present the PSNR and SSIM scores specifically on the Y channel (YCbCr space). We report state-of-the-art approaches for comparison: MPRNet (<PERSON><PERSON><PERSON> et al., 2021) , M3SNet-32 (<PERSON> et al., 2023) , MAXIM (<PERSON> et al., 2022) , MHNet (Gao & Dang, 2023) , IR-SDE (<PERSON><PERSON> et al., 2023a) . The relevant experimental results are shown in the Table 2 and Figure 3 . Similarly, both models achieved SOTA results respectively in the deraining task. Visually, it can be also observed that our model excels in capturing details such as the moon, the sun, and tree branches.", "section": "Experiments", "sec_num": "4."}, {"text": "Image Super-Resolution. Single image super-resolution aims to recover a higher resolution and clearer version from a low-resolution image. We conducted training and evalua- tion on the DIV2K validation set for 4× upscaling (Agustsson & Timofte, 2017) and all low-resolution images were bicubically rescaled to the same size as their corresponding high-resolution images. To show that our models are in line with the state-of-the-art, we compare to the DDRM (<PERSON><PERSON> et al., 2022) and IR-SDE (<PERSON><PERSON> et al., 2023a) . The relevant experimental results are provided in Table 3 and Figure 4 . As can be seen, our GOUB is superior to benchmarks in various indicators and handles visual details better such as edges and hair.", "section": "Experiments", "sec_num": "4."}, {"text": "Superiority of Mean-ODE. Additionally, we conduct ablation experiments using the corresponding Score-ODE (10) model to demonstrate the superiority of our proposed Mean-ODE model in image restoration. From Table 4 , it is evident that the performance of Mean-ODE is significantly superior to that of the corresponding Score-ODE. This is because the sampling results of each sampling step of Mean-ODE directly approximate the true mean during the training process, as opposed to the parameterized approach such as DDPM, which relies on expectations. Consequently, our proposed Mean-ODE demonstrates better reconstruction effects and is more suitable for image restoration tasks. ", "section": "Experiments", "sec_num": "4."}, {"text": "The <PERSON><PERSON>'s h-transform of the generalized <PERSON><PERSON>-<PERSON> process, also known as the conditional GOU process has been an intriguing topic in previous applied mathematical research (<PERSON><PERSON><PERSON>, 1984; <PERSON><PERSON><PERSON><PERSON> et al., 2003; <PERSON><PERSON> et al., 2021) . On account of the mean-reverting property of the GOU process, applying the h-transform makes it most straightforward to eliminate the variance and drive it towards a Dirac distribution in its steady state which is highly advantageous for its applications in image restoration. In previous research on diffusion models, there has been limited focus on the cases of f or g, and generally used the VE process (<PERSON> et al., 2021b) represented by NCSN (Song & Ermon, 2019) or the VP process (<PERSON> et al., 2021b) represented by DDPM (<PERSON> et al., 2020) .", "section": "Analysis", "sec_num": "5."}, {"text": "In this section, we demonstrate that the mathematical essence of several recent meaningful diffusion bridge models is the same (<PERSON> et al., 2023; <PERSON> et al., 2024; <PERSON> et al., 2023a) and they all represent Brownian bridge (<PERSON>, 2009) models, details are provided in the Appendix B.1. Then, we also found that the VE and VP processes are special cases of GOU, leading to the following proposition:", "section": "Analysis", "sec_num": "5."}, {"text": "Proposition 5.1. For a given GOU process (4), there exists relationships:", "section": "Analysis", "sec_num": "5."}, {"text": "EQUATION", "section": "Analysis", "sec_num": "5."}, {"text": "Details are provided in the Appendix B.2. Therefore, we conduct experiments on VE Bridge (VEB) (<PERSON> et al., 2023; <PERSON> et al., 2024; <PERSON> et al., 2023a) and VP Bridge (VPB) (<PERSON> et al., 2024) to demonstrate the optimality of our proposed GOUB model in image restoration. We keep all the model hyperparameters consistent and results are shown in Table 5 and Figure 5. It can be seen that under the same configuration of model hyperparameters, the performance of the GOUB is notably superior to the other two types of bridge models, which demonstrates the optimality of GOUB and also highlights the importance of the choice of diffusion process in diffusion models.", "section": "Analysis", "sec_num": "5."}, {"text": "Conditional Generation. As previously highlighted, in the work of image restoration using diffusion models, the focus of some research has predominantly been on using low- quality images as conditional inputs y to guide the generation process. They (<PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2022a; b; 2023; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) all endeavor to solve or approximate the classifier log ∇ xt p(y | x t ), necessitating the incorporation of additional prior knowledge to model specific degradation processes which both complex and lacking in universality.", "section": "Related Works", "sec_num": "6."}, {"text": "Diffusion Bridge. This segment of work obviates the need for prior knowledge, constructing a diffusion bridge model from high-quality to low-quality images, thereby learning the degradation process. The previously mentioned approach (<PERSON> et al., 2022; <PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON> et al., 2023a; <PERSON> et al., 2024; <PERSON> et al., 2023; <PERSON> et al., 2024; <PERSON><PERSON><PERSON> et al., 2023) fall into this class and are characterized by the issues of significant computational expense in solution seeking and also not the optimal model framework. Additionally, some models of flow category (<PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023b; <PERSON> et al., 2023; Albergo & Vanden-<PERSON>den, 2023; Delbracio & Milanfar, 2023 ) also belong to the diffusion bridge models and face the similar issue.", "section": "Related Works", "sec_num": "6."}, {"text": "In this paper, we introduced the Generalized Ornstein-Uhlenbeck Bridge (GOUB) model, a diffusion bridge model that applies the Doob's h-transform to the GOU process. This model can address general image restoration tasks without the need for specific prior knowledge. Furthermore, we have uncovered the mathematical essence of several bridge models and empirically demonstrated the optimality of our proposed model. In addition, considering our unique mean parameterization mechanism, we proposed the Mean-ODE model. Experimental results indicate that both models achieve state-of-the-art results in their respective areas of strength on various tasks, including inpainting, deraining, and super-resolution. We believe that the exploration of diffusion process and bridge models holds significant importance not only in the field of image restoration but also in advancing the study of generative diffusion models.", "section": "Conclusion", "sec_num": "7."}, {"text": "A.1. Proof of Proposition 3.1 Proposition 3.1. Let x t be a finite random variable describing by the given generalized Ornstein-Uh<PERSON>beck process (4), suppose x T = µ, the evolution of its marginal distribution p(x t | x T ) satisfies the following SDE:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "additionally, the forward transition p(x t | x 0 , x T ) is given by:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Proof : Based on (5), we have:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "p (x t | x 0 ) = N x T + (x 0 -x T ) e -θt , σ2 t I (19) p (x T | x t ) = N x T + (x t -x T ) e -θt:T , σ2 t:T I (20) p (x T | x 0 ) = N x T + (x 0 -x T ) e -θT , σ2 T I (21)", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Firstly, the h function can be directly compute:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "h(x t , t, x T , T ) = ∇ xt log p(x T | x t ) = -∇ xt (x t -x T ) 2 e -2 θt:T 2σ 2 t:T = (x T -x t ) e -2 θt:T σ2 t:T (22)", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Therefore, followed by <PERSON><PERSON>'s h-transform (6), the SDE of marginal distribution p(x t | x T ) satisfied is :", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "dx t = f (x t , t) + g 2 t h(x t , t, x T , T ) dt + g t dw t = θ t + g 2 t e -2 θt:T σ2 t:T (x T -x t )dt + g t dw t (23)", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Furthermore, we can derive the following transition probability of x t using <PERSON><PERSON>' formula:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "p(x t | x 0 , x T ) = p(x T | x t , x 0 )p(x t | x 0 ) p(x T | x 0 ) = p(x T | x t )p(x t | x 0 ) p(x T | x 0 ) (24)", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Since each component is independently and identically distributed (i.i.d), by considering a single dimension, we have:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Notice that:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Bringing it back to (25), squaring the terms and reorganizing the equation, we obtain:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "This concludes the proof of the Proposition 3.1.", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "A.2. Proof of Proposition 3.3 Proposition 3.3. Let x t be a finite random variable describing by the given generalized Ornstein-<PERSON>beck process (4), for a fixed x T , the expectation of log-likelihood E p(x0) [log p θ (x 0 | x T )] possesses an Evidence Lower Bound (ELBO):", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Assuming p θ (x t-1 | x t , x T ) is a Gaussian distribution with a constant variance N (µ θ,t-1 , σ 2 θ,t-1 I), maximizing the ELBO is equivalent to minimizing:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": ")", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "where µ t-1 represents the mean of p (x t-1 | x 0 , x t , x T ):", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "where,", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "a = e -θt-1:t σ2 t:T σ2 t-1:T , b = 1 σ2 T (1 -e -θt )σ 2 t:T + e -2 θt:T σ2 t -(1 -e -θt-1 )σ 2 t-1:T + e -2 θt-1:T σ2 t-1 a", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Proof : Firstly, followed by the theorem in DDPM (<PERSON> et al., 2020) :", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Similarly, we have:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "From <PERSON><PERSON>' formula, we can infer that:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Since p(x t-1 | x 0 , x T ) and p(x t | x 0 , x T ) are Gaussian distributions (8), by employing the reparameterization technique:", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Therefore,", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "x t = m(t) m(t -1) x t-1 + n(t) - m(t) m(t -1) n(t -1) x T + σ′2 t - m(t) 2 m(t -1) 2 σ′2 t-1 ϵ = ax t-1 + [n(t) -an(t -1)] x T + σ′2 t -a 2 σ′2 t-1 ϵ = ax t-1 + bx T + σ′2 t -a 2 σ′2 t-1 ϵ (32) Thus, p(x t | x t-1 , x T ) = N (ax t-1 + bx T , σ′2 t -a 2 σ′2 t-1 I", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": ") is also a Gaussian distribution. Bring it back to equation (30) we can easily obtain :", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Accordingly,", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "Ignoring unlearnable constant, the training object that involves minimizing the negative ELBO is :", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": ")", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "This concludes the proof of the Proposition 3.3.", "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "In this section, we will show the mathematical essence of some other bridge models, some of which are all equivalent. Proposition B.1. The mathematical essence of BBDM (<PERSON> et al., 2023) , DDBM (VE) (<PERSON> et al., 2024) and I 2 SB (<PERSON> et al., 2023a ) are all equivalent to the Brownian bridge.", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "Proof : Firstly, it is easy to understand that BBDM uses the Brownian bridge as its fundamental model architecture.", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "The DDBM (VE) model is derived as the <PERSON><PERSON>'s h-transform of VE-SDE, and we begin by specifying the SDE:", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "dx t = dw t (35)", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "Its transition probability is given by: p", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "Since, the h-function of SDE ( 35) is:", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "Therefore, the <PERSON><PERSON>'s h-transform of ( 35) is:", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "dx t = x T -x t T -t dt + dw t (38)", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "That is the definition of Brownian bridge. Hence, DDBM (VE) is a Brownian bridge model.", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "Furthermore, the transition kernel of ( 38) is:", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "EQUATION", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "This precisely corresponds to the sampling process of I 2 SB, thus confirming that I 2 SB also represents a Brownian bridge.", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "This concludes the proof of the Proposition B.1.", "section": "B.1. <PERSON><PERSON>", "sec_num": null}, {"text": "The following proposition will show us that both VE and VP processes are special cases of GOU process:", "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "Proposition 5.1. For a given GOU process (4), there exists relationships:", "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "EQUATION", "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "Proof : It's easy to know:", "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "EQUATION", "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "where g t will be controlled by λ 2 .", "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "Besides, we have:", "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "EQUATION", "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "where g t will be controlled by θ t .", "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "This concludes the proof of the Proposition 5.1.", "section": "B.2. Connections Between GOU, VE and VP", "sec_num": null}, {"text": "Theorem C.1. For a given GOU process:", "section": "C. GOU Process", "sec_num": null}, {"text": "EQUATION", "section": "C. GOU Process", "sec_num": null}, {"text": ")", "section": "C. GOU Process", "sec_num": null}, {"text": "where µ is a given state vector, θ t denotes a scalar drift coefficient and g t represents the diffusion coefficient. It possesses a closed-form analytical solution: Therefore:", "section": "C. GOU Process", "sec_num": null}, {"text": "p (x t | x s ) = N µ + (x s -µ) e -θs:", "section": "C. GOU Process", "sec_num": null}, {"text": "x t e θtx s e θs = e θt -e θs µ + N 0, λ 2 e 2 θt -e 2 θs I", "section": "C. GOU Process", "sec_num": null}, {"text": "EQUATION", "section": "C. GOU Process", "sec_num": null}, {"text": "This concludes the proof of the Theorem C.1.", "section": "C. GOU Process", "sec_num": null}, {"text": "Theorem D.1. For a given SDE:", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "For a fixed x T , the evolution of conditional probability p(x t | x T ) follows:", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "where h(x t , t, x T , T ) = ∇ xt log p(x T | x t ).", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "Proof : p(x t | x 0 ) satisfies Kolmogorov Forward Equation (KFE) also called Fokker-Planck equation (<PERSON><PERSON> & Risken, 1996) :", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "Similarly, p(x T | x t ) satisfies <PERSON><PERSON><PERSON><PERSON> Backward Equation (KBE) (<PERSON><PERSON> & Risken, 1996) :", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "Using <PERSON><PERSON>' rule, we have:", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "Therefore, the derivative of conditional transition probability p(x t | x 0 , x T ) with time follows:", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "For the second term, we have:", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "EQUATION", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "Bring it back to (50): This concludes the proof of the Theorem D.1.", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "∂ ∂t p(x t | x 0 , x T ) = -∇ xt • [f (x t ,", "section": "<PERSON><PERSON>'s h-transform", "sec_num": null}, {"text": "For all experiments, we use the same noise network, with the network architecture and mainly training parameters consistent with the paper (<PERSON><PERSON> et al., 2023a) . This network is similar to a U-Net structure but without group normalization layers and self-attention layers. The steady variance level λ 2 was set to 30 (over 255), and the sampling step number T was set to 100.", "section": "E. Experimental Details", "sec_num": null}, {"text": "In the training process, we set the patch size = 128 with batch size = 8 and use <PERSON> (Kingma & Ba, 2015) optimizer with parameters β 1 = 0.9 and β 2 = 0.99. The total training steps are 900 thousand with the initial learning rate set to 10 -4 , and it decays by half at iterations 300, 500, 600, and 700 thousand. For the setting of θ t , we employ a flipped version of cosine noise schedule (Nichol & Dhariwal, 2021) , enabling θ t to change from 0 to 1 over time. Notably, to address the issue of θ t being too smooth when t closed to 1, we let the coefficient e -θT to be a small enough value δ = 0.005 instead of zero, which represents θT ≈ T i=0 θ i dt = -log δ, as well as dt = -log δ/ T i=0 θ i . Our models are trained on a single 3090 GPU with 24GB memory for about 2.5 days. ", "section": "E. Experimental Details", "sec_num": null}, {"text": "Department of Computer Science, Sun Yat-sen University, Guangzhou, Guangdong, China.", "section": "", "sec_num": null}, {"text": "Pengcheng Laboratory. Correspondence to: <PERSON><PERSON> <<EMAIL>>. Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "section": "", "sec_num": null}], "back_matter": [{"text": "This work is supported in part by National Natural Science Foundation of China (NSFC) under Grant No. 62376292, U21A20470, and Guangdong Basic and Applied Basic Research Foundation under Grant No. 2024A1515011741.", "section": "Acknowledgements", "sec_num": null}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none which we feel must be specifically highlighted here.", "section": "Impact Statement", "sec_num": null}], "ref_entries": {"FIGREF0": {"fig_num": "1", "num": null, "uris": null, "text": "Figure 1. Overview of the proposed GOUB for image restoration. The GOU process is capable of transferring an HQ image into a noisy LQ image. Additionally, through the application of h-transform, we can eliminate the noise on LQ, enabling the GOUB model to precisely bridge the gap between HQ and LQ.", "type_str": "figure"}, "FIGREF1": {"fig_num": "2", "num": null, "uris": null, "text": "Figure 2. Qualitative comparison of the visual results of different inpainting methods on the CelebA-HQ dataset with thin mask.", "type_str": "figure"}, "FIGREF2": {"fig_num": "3", "num": null, "uris": null, "text": "Figure 3. Qualitative comparison of the visual results of different deraining methods on the Rain100H dataset.", "type_str": "figure"}, "FIGREF3": {"fig_num": "4", "num": null, "uris": null, "text": "Figure 4. Qualitative comparison of the visual results of different 4x super-resolution methods on the DIV2K dataset.", "type_str": "figure"}, "FIGREF4": {"fig_num": "5", "num": null, "uris": null, "text": "Figure 5. Qualitative comparison with the different bridge models in many tasks.", "type_str": "figure"}, "TABREF0": {"html": null, "num": null, "text": "Image Inpainting. Qualitative comparison with the relevant baselines on CelebA-HQ.", "content": "<table><tr><td colspan=\"5\">METHOD PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>PromptIR</td><td>30.22</td><td>0.9180</td><td>0.068</td><td>32.69</td></tr><tr><td>DDRM</td><td>27.16</td><td>0.8993</td><td>0.089</td><td>37.02</td></tr><tr><td>IR-SDE</td><td>28.37</td><td>0.9166</td><td>0.046</td><td>25.13</td></tr><tr><td>GOUB</td><td>28.98</td><td>0.9067</td><td>0.037</td><td>4.30</td></tr><tr><td>Mean-ODE</td><td>31.39</td><td>0.9392</td><td>0.052</td><td>12.24</td></tr><tr><td colspan=\"5\">evaluation, i.e., Peak Signal-to-Noise Ratio (PSNR) for as-</td></tr><tr><td colspan=\"5\">sessing reconstruction quality, Structural Similarity Index</td></tr><tr><td colspan=\"5\">(SSIM) (Wang et al., 2004) for gauging structural percep-</td></tr><tr><td colspan=\"5\">tion, Learned Perceptual Image Patch Similarity (LPIPS)</td></tr><tr><td colspan=\"5\">(Zhang et al., 2018b) for evaluating the depth and quality</td></tr><tr><td colspan=\"5\">of features, and Fréchet Inception Distance (FID) (Heusel</td></tr><tr><td colspan=\"5\">et al., 2017) to measure the diversity in generated images.</td></tr><tr><td colspan=\"5\">More experiment details are present in Appendix E.</td></tr></table>", "type_str": "table"}, "TABREF1": {"html": null, "num": null, "text": "Image Deraining. Qualitative comparison with the relevant baselines on Rain100H.", "content": "<table><tr><td colspan=\"5\">METHOD PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>MPRNet</td><td>30.41</td><td>0.8906</td><td>0.158</td><td>61.59</td></tr><tr><td>M3SNet-32</td><td>30.64</td><td>0.8920</td><td>0.154</td><td>60.26</td></tr><tr><td>MAXIM</td><td>30.81</td><td>0.9027</td><td>0.133</td><td>58.72</td></tr><tr><td>MHNet</td><td>31.08</td><td>0.8990</td><td>0.126</td><td>57.93</td></tr><tr><td>IR-SDE</td><td>31.65</td><td>0.9041</td><td>0.047</td><td>18.64</td></tr><tr><td>GOUB</td><td>31.96</td><td>0.9028</td><td>0.046</td><td>18.14</td></tr><tr><td>Mean-ODE</td><td>34.56</td><td>0.9414</td><td>0.077</td><td>32.83</td></tr></table>", "type_str": "table"}, "TABREF2": {"html": null, "num": null, "text": "Image 4× Super-Resolution. Qualitative comparison with the relevant baselines on DIV2K.", "content": "<table><tr><td colspan=\"5\">METHOD PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>DDRM</td><td>24.35</td><td>0.5927</td><td>0.364</td><td>78.71</td></tr><tr><td>IR-SDE</td><td>25.90</td><td>0.6570</td><td>0.231</td><td>45.36</td></tr><tr><td>GOUB</td><td>26.89</td><td>0.7478</td><td>0.220</td><td>20.85</td></tr><tr><td>Mean-ODE</td><td>28.50</td><td>0.8070</td><td>0.328</td><td>22.14</td></tr></table>", "type_str": "table"}, "TABREF3": {"html": null, "num": null, "text": "Qualitative comparison with the corresponding Score-ODE on various tasks.", "content": "<table><tr><td>METHOD</td><td/><td colspan=\"2\">Image Inapinting</td><td/><td/><td colspan=\"2\">Image Deraining</td><td/><td colspan=\"4\">Image 4× Super-Resolution</td></tr><tr><td/><td colspan=\"12\">PSNR↑ SSIM↑ LPIPS↓ FID↓ PSNR↑ SSIM↑ LPIPS↓ FID↓ PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>Score-ODE</td><td>18.23</td><td>0.6266</td><td>0.389</td><td>161.54</td><td>13.64</td><td>0.7404</td><td>0.338</td><td>191.15</td><td>28.14</td><td>0.7993</td><td>0.344</td><td>25.51</td></tr><tr><td>Mean-ODE</td><td>31.39</td><td>0.9392</td><td>0.052</td><td>12.24</td><td>34.56</td><td>0.9414</td><td>0.077</td><td>32.83</td><td>28.50</td><td>0.8070</td><td>0.328</td><td>22.14</td></tr></table>", "type_str": "table"}, "TABREF4": {"html": null, "num": null, "text": "Qualitative comparison with the different bridge models on CelebA-HQ, Rain100H, and DIV2K datasets.", "content": "<table><tr><td>METHOD</td><td/><td colspan=\"2\">Image Inapinting</td><td/><td/><td colspan=\"2\">Image Deraining</td><td/><td colspan=\"4\">Image 4× Super-Resolution</td></tr><tr><td/><td colspan=\"12\">PSNR↑ SSIM↑ LPIPS↓ FID↓ PSNR↑ SSIM↑ LPIPS↓ FID↓ PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>VEB</td><td>27.75</td><td>0.8943</td><td>0.056</td><td>13.70</td><td>30.39</td><td>0.8975</td><td>0.059</td><td>28.54</td><td>24.21</td><td>0.5808</td><td>0.384</td><td>36.55</td></tr><tr><td>VPB</td><td>27.32</td><td>0.8841</td><td>0.049</td><td>11.87</td><td>30.89</td><td>0.8847</td><td>0.051</td><td>23.36</td><td>25.40</td><td>0.6041</td><td>0.342</td><td>29.17</td></tr><tr><td>GOUB</td><td>28.98</td><td>0.9067</td><td>0.037</td><td>4.30</td><td>31.96</td><td>0.9028</td><td>0.046</td><td>18.14</td><td>26.89</td><td>0.7478</td><td>0.220</td><td>20.85</td></tr></table>", "type_str": "table"}, "TABREF5": {"html": null, "num": null, "text": ", t) = x t θ t e θt dt + e θt dx t = x t θ t e θt dt + e θt [θ t (µ -x t ) dt + g t dw t ] = e θt θ t µ + e θt g t dw tIt's obvious that the transition kernel is a Gaussian distribution. Since dw z ∼ N (0, dzI), we have:", "content": "<table><tr><td>t , f (x t , t) = x t e θt g 2 t 2θ t 1 -e -2 θs:t I , df (x t (43) θs:t = t s θ z dz (5) (42) Using Ito differential formula, we get: Proof : Writing: Integrating from s to t we get: s e θz g z dw z = N 0, t s e 2 θz g 2 z dzI s = N 0, λ 2 e 2 θt -e 2 θs I x t = N 0, λ 2 t e 2 θz 2θ t dzI (45)</td></tr></table>", "type_str": "table"}, "TABREF6": {"html": null, "num": null, "text": "This is the definition of FP equation of conditional transition probability p(x t | x 0 , x T ), which represents the evolution follows the SDE:dx t = f (x t , t) + g 2 t ∇ xt log p(x T | x t ) dt + g t dw t (53)", "content": "<table/>", "type_str": "table"}, "TABREF7": {"html": null, "num": null, "text": "Image Inpainting. Qualitative comparison with the relevant baselines on CelebA-HQ with thick mask.", "content": "<table><tr><td colspan=\"5\">METHOD PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>DDRM</td><td>19.48</td><td>0.8154</td><td colspan=\"2\">0.1487 26.24</td></tr><tr><td>IRSDE</td><td>21.12</td><td>0.8499</td><td colspan=\"2\">0.1046 11.12</td></tr><tr><td>GOUB</td><td>22.27</td><td>0.8754</td><td>0.0914</td><td>5.64</td></tr></table>", "type_str": "table"}, "TABREF8": {"html": null, "num": null, "text": "Image Deraining. Qualitative comparison with the relevant baselines on Rain100L.", "content": "<table><tr><td colspan=\"5\">METHOD PSNR↑ SSIM↑ LPIPS↓ FID↓</td></tr><tr><td>PRENET</td><td>37.48</td><td>0.9792</td><td>0.020</td><td>10.9</td></tr><tr><td>MAXIM</td><td>38.06</td><td>0.9770</td><td>0.048</td><td>19.0</td></tr><tr><td>IRSDE</td><td>38.30</td><td>0.9805</td><td>0.014</td><td>7.94</td></tr><tr><td>GOUB</td><td>39.79</td><td>0.9830</td><td>0.009</td><td>5.18</td></tr></table>", "type_str": "table"}, "TABREF9": {"html": null, "num": null, "text": "Image 8× Super-Resolution. Qualitative comparison with the relevant baselines on DIV2K.", "content": "<table><tr><td colspan=\"4\">METHOD PSNR↑ SSIM↑ LPIPS↓</td><td>Training Datasets</td></tr><tr><td>SRFlow</td><td>23.05</td><td>0.57</td><td>0.272</td><td>DIV2K + Flickr2K</td></tr><tr><td>IRSDE</td><td>22.34</td><td>0.55</td><td>0.331</td><td>DIV2K</td></tr><tr><td>GOUB</td><td>23.17</td><td>0.60</td><td>0.310</td><td>DIV2K</td></tr></table>", "type_str": "table"}}}}