{"paper_id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:40:09.876177Z"}, "title": "CurBench: Curriculum Learning Benchmark", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Pan", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "Xin", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": "<EMAIL>>"}, {"first": "Hong", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "Fangzhou", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Peiyang", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "Shengna<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": "<<EMAIL>>."}, {"first": "Dataset", "middle": [], "last": "Metrics", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Curriculum learning is a training paradigm where machine learning models are trained in a meaningful order, inspired by the way humans learn curricula. Due to its capability to improve model generalization and convergence, curriculum learning has gained considerable attention and has been widely applied to various research domains. Nevertheless, as new curriculum learning methods continue to emerge, it remains an open issue to benchmark them fairly. Therefore, we develop CurBench, the first benchmark that supports systematic evaluations for curriculum learning. Specifically, it consists of 15 datasets spanning 3 research domains: computer vision, natural language processing, and graph machine learning, along with 3 settings: standard, noise, and imbalance. To facilitate a comprehensive comparison, we establish the evaluation from 2 dimensions: performance and complexity. CurBench also provides a unified toolkit that plugs automatic curricula into general machine learning processes, enabling the implementation of 15 core curriculum learning methods. On the basis of this benchmark, we conduct comparative experiments and make empirical analyses of existing methods.", "pdf_parse": {"paper_id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_pdf_hash": "", "abstract": [{"text": "Curriculum learning is a training paradigm where machine learning models are trained in a meaningful order, inspired by the way humans learn curricula. Due to its capability to improve model generalization and convergence, curriculum learning has gained considerable attention and has been widely applied to various research domains. Nevertheless, as new curriculum learning methods continue to emerge, it remains an open issue to benchmark them fairly. Therefore, we develop CurBench, the first benchmark that supports systematic evaluations for curriculum learning. Specifically, it consists of 15 datasets spanning 3 research domains: computer vision, natural language processing, and graph machine learning, along with 3 settings: standard, noise, and imbalance. To facilitate a comprehensive comparison, we establish the evaluation from 2 dimensions: performance and complexity. CurBench also provides a unified toolkit that plugs automatic curricula into general machine learning processes, enabling the implementation of 15 core curriculum learning methods. On the basis of this benchmark, we conduct comparative experiments and make empirical analyses of existing methods.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Throughout the development of machine learning, a large number of works have been greatly influenced by human learning. Curriculum learning is such a research topic within machine learning that draws inspiration from a remarkable aspect of human learning: curriculum, i.e., learning in a pur-Proceedings of the 41 st International Conference on Machine Learning, Vienna, Austria. PMLR 235, 2024. Copyright 2024 by the author(s).", "cite_spans": [{"start": 353, "end": 395, "text": "Learning, Vienna, Austria. PMLR 235, 2024.", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "poseful and meaningful order (<PERSON> et al., 2021a; <PERSON><PERSON><PERSON> et al., 2022) . In contrast to conventional machine learning methods where training examples are randomly input, curriculum learning aims to facilitate learning by gradually increasing the difficulty of data or tasks experienced by the model (<PERSON><PERSON> et al., 2009) . Since this easy-to-hard training paradigm is verified to bring the advantage of enhancing model generalization and accelerating convergence speed (<PERSON> et al., 2016; <PERSON><PERSON><PERSON> et al., 2018) , it has aroused widespread interest among researchers in harnessing its potential across diverse application domains, such as computer vision (CV) (<PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2017) , natural language processing (NLP) (Platanios et al., 2019; <PERSON><PERSON> et al., 2019; <PERSON> et al., 2018) , graph machine learning (<PERSON> et al., 2023; <PERSON> et al., 2021b; <PERSON> et al., 2023; <PERSON> et al., 2024; <PERSON> et al., 2024) , multimodal learning (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON> et al., 2023) , recommender systems (<PERSON> et al., 2021b; a; <PERSON> et al., 2023; <PERSON> et al., 2023a) , reinforcement learning (RL) (<PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2017; <PERSON> et al., 2018b) , and others (<PERSON> et al., 2022; <PERSON> et al., 2022b) .", "cite_spans": [{"start": 29, "end": 49, "text": "(<PERSON> et al., 2021a;", "ref_id": null}, {"start": 50, "end": 71, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF47"}, {"start": 300, "end": 321, "text": "(<PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF0"}, {"start": 470, "end": 489, "text": "(<PERSON> et al., 2016;", "ref_id": "BIBREF15"}, {"start": 490, "end": 513, "text": "<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF60"}, {"start": 662, "end": 680, "text": "(<PERSON> et al., 2018;", "ref_id": "BIBREF18"}, {"start": 681, "end": 702, "text": "<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF46"}, {"start": 703, "end": 720, "text": "<PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF17"}, {"start": 757, "end": 781, "text": "(<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF36"}, {"start": 782, "end": 799, "text": "<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF50"}, {"start": 800, "end": 817, "text": "<PERSON> et al., 2018)", "ref_id": "BIBREF31"}, {"start": 843, "end": 860, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF30"}, {"start": 861, "end": 880, "text": "<PERSON> et al., 2021b;", "ref_id": null}, {"start": 881, "end": 898, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF59"}, {"start": 899, "end": 916, "text": "<PERSON> et al., 2024;", "ref_id": "BIBREF38"}, {"start": 917, "end": 934, "text": "<PERSON> et al., 2024)", "ref_id": "BIBREF64"}, {"start": 957, "end": 975, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF27"}, {"start": 976, "end": 994, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF3"}, {"start": 995, "end": 1013, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF73"}, {"start": 1036, "end": 1056, "text": "(<PERSON> et al., 2021b;", "ref_id": null}, {"start": 1057, "end": 1059, "text": "a;", "ref_id": null}, {"start": 1060, "end": 1076, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF61"}, {"start": 1077, "end": 1096, "text": "<PERSON> et al., 2023a)", "ref_id": null}, {"start": 1127, "end": 1150, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF14"}, {"start": 1151, "end": 1173, "text": "<PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF34"}, {"start": 1174, "end": 1192, "text": "<PERSON> et al., 2018b)", "ref_id": null}, {"start": 1206, "end": 1226, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF68"}, {"start": 1227, "end": 1246, "text": "<PERSON> et al., 2022b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Despite the significant progress and the wide application of curriculum learning, the increasing number of works has posed challenges in terms of their comparison and evaluation, mainly due to the differences in their experimental setups including datasets, backbone models, and settings. For instance, DCL (<PERSON><PERSON><PERSON> et al., 2019) and DDS (<PERSON> et al., 2020) use the same WideResNet-28-10 model (Zagoruyk<PERSON> & Komodakis, 2016) , but perform experiments on different datasets: CIFAR-100 and CIFAR-10 ( <PERSON><PERSON><PERSON><PERSON> et al., 2009) respectively. Similarly, DI-HCL (<PERSON> et al., 2020) and CBS (<PERSON><PERSON> et al., 2020) leverage the same ImageNet (<PERSON><PERSON> et al., 2009) dataset, but employ distinct models: ResNet-50 and ResNet-18 (<PERSON> et al., 2016) respectively. Furthermore, while MCL (Zhou & Bilmes, 2018) and LRE (<PERSON> et al., 2018a) utilize the same MNIST dataset and LeNet model (<PERSON><PERSON><PERSON> et al., 1998) , they adopt different settings: standard and imbalanced labels respectively. Consequently, their experimental results cannot be compared directly, which makes it challenging to conduct a fair evaluation. The absence of a standardized evaluation not only hinders researchers from accurately assessing their own contributions when they propose a new method but also poses barriers for users when they seek a suitable method for their specific tasks.", "cite_spans": [{"start": 307, "end": 328, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF43"}, {"start": 337, "end": 356, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF54"}, {"start": 393, "end": 422, "text": "(Zagoruyko & Komodakis, 2016)", "ref_id": "BIBREF65"}, {"start": 497, "end": 521, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF25"}, {"start": 554, "end": 573, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF71"}, {"start": 582, "end": 602, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF45"}, {"start": 630, "end": 649, "text": "(<PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF7"}, {"start": 687, "end": 728, "text": "ResNet-50 and ResNet-18 (<PERSON> et al., 2016)", "ref_id": null}, {"start": 766, "end": 787, "text": "(Zhou & Bilmes, 2018)", "ref_id": "BIBREF70"}, {"start": 796, "end": 815, "text": "(<PERSON> et al., 2018a)", "ref_id": null}, {"start": 863, "end": 883, "text": "(<PERSON><PERSON><PERSON> et al., 1998)", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "To deal with this issue, researchers have made notable efforts to evaluate and summarize existing works. From a theoretical perspective, there have been surveys covering general curriculum learning (<PERSON> et al., 2021a; <PERSON><PERSON><PERSON> et al., 2022) as well as specific ones for graph (<PERSON> et al., 2023) and RL (<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2020) , all of which manage to formulate and categorize relevant methods comprehensively. Although they offer valuable theoretical insights, current surveys do not incorporate any practical implementation or experimental results. From an empirical perspective, there has been an open-source library on curriculum learning (<PERSON> et al., 2022a) , which reproduces multiple related methods through a unified framework. Although it provides empirical results of the implemented methods, this library only supports the classification task on CIFAR-10, limited in experimental setups. In conclusion, the related works fail to address the open issue of evaluating and comparing curriculum learning methods completely.", "cite_spans": [{"start": 198, "end": 218, "text": "(<PERSON> et al., 2021a;", "ref_id": null}, {"start": 219, "end": 240, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF47"}, {"start": 276, "end": 293, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF30"}, {"start": 301, "end": 324, "text": "(<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF35"}, {"start": 325, "end": 347, "text": "<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF37"}, {"start": 664, "end": 684, "text": "(<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "In order to address the absence of benchmarks in this field, we propose CurBench, the first benchmark for systematic evaluations of curriculum learning, as shown in Figure 1 . Concretely, it encompasses 15 prevalent datasets, spanning 3 research domains including CV, NLP, and graph to ensure the reliability of evaluation. These datasets are further preprocessed into 3 settings including standard, noise, and imbalance to reveal the capability of methods to enhance model generalization and robustness. Without loss of generality, a total of 9 prevalent backbone models of varying types and scales adapted to the above datasets are employed in an appropriate manner, incorporating corresponding hyperparameters, optimizers, and so on. Most of the datasets, settings, and models are commonly used in previous re-lated works, while the rest are supplemented in this work to investigate how these methods can adapt to the tasks in other domains. For ease of use, this benchmark also provides a unified toolkit that plugs automatic curricula into general machine learning processes and reproduces a collection of 15 core curriculum learning approaches. Based on these implementations in CurBench, we further perform a comprehensive evaluation from 2 dimensions including performance and complexity, presenting the improvements the methods bring and the additional resources they consume.", "cite_spans": [], "ref_spans": [{"start": 172, "end": 173, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Furthermore, we delve into our benchmark, organize experimental outcomes, conduct in-depth analyses, and obtain some intriguing findings. First, there has been no such method that outperforms others all the time, and the effectiveness depends on specific scenarios. Second, curriculum learning brings more significant improvements in noise settings than in standard and imbalance ones. Third, methods by teacher transferring have edges in noise settings, while methods by reweighting perform relatively well in imbalance settings. Lastly, methods involving gradient calculation and extra learnable networks generally have higher time and space complexity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Our contributions are summarized as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We propose CurBench, the first benchmark on curriculum learning to the best of our knowledge.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We conduct extensive experiments to impartially evaluate and compare the performance and complexity of existing curriculum learning methods under various experimental setups.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "• We make in-depth analyses and demonstrate intriguing observations on curriculum learning based on empirical results derived from CurBench. 1 . The statistics of 15 datasets adopted in CurBench, which covers a wide range of scales across 3 research domains in 3 settings. \"<PERSON><PERSON><PERSON>\" and \"<PERSON>\" refers to the correlation coefficient. \"Noise-0.4\" means 40% data samples are independently attached with random incorrect labels. \"Imbalance-50\" means a ratio of 50 between the number of samples in the largest class and that in the smallest class in a long-tailed dataset where the number of samples for each class follows a geometric sequence. The imbalance setting is not applied to NLP and graph datasets, which are imbalanced originally.", "cite_spans": [], "ref_spans": [{"start": 141, "end": 142, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Introduction", "sec_num": "1."}, {"text": "Curriculum learning, much like many other topics in machine learning, draws inspiration from human learning. It refers to a training strategy where models learn from input data in a meaningful order, imitating the way humans learn from curricula. The emergence of this idea could at least be traced back to <PERSON><PERSON>'s work (<PERSON><PERSON>, 1993) in 1993, which advocated the importance of starting small. In 2009 , <PERSON><PERSON> et al. (<PERSON><PERSON> et al., 2009) first introduced a formal definition of curriculum learning and explored when, why, and how a curriculum could benefit machine learning. In the early stages, curricula for models were entirely predefined by humans, and the most typical method was named Baby Step (<PERSON><PERSON><PERSON> et al., 2010) . However, this type of predefined approach is not flexible and general enough for widespread applications. In 2010, <PERSON> et al. (<PERSON> et al., 2010) proposed self-paced learning (SPL), enabling automatic curriculum scheduling by ordering data according to their training loss. Subsequently, a variety of automatic curriculum learning methods have continued to emerge. For example, transfer learning methods (<PERSON><PERSON> et al., 2018; Ha<PERSON> & Weinshall, 2019) employ teacher models to offer student models curricula. Reinforcement learning methods (<PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2020) allow teacher models to adapt curriculum based on the feedback from student models. In addition, there are other ones based on Bayesian optimization (<PERSON><PERSON><PERSON><PERSON> et al., 2016) , meta-learning (<PERSON> et al., 2018a; <PERSON> et al., 2019) , and adversarial learning (<PERSON> et al., 2020) for implementing automatic curriculum learning.", "cite_spans": [{"start": 320, "end": 333, "text": "(<PERSON><PERSON>, 1993)", "ref_id": "BIBREF10"}, {"start": 393, "end": 400, "text": "In 2009", "ref_id": null}, {"start": 401, "end": 438, "text": ", <PERSON><PERSON> et al. (<PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF0"}, {"start": 702, "end": 727, "text": "(<PERSON><PERSON><PERSON> et al., 2010)", "ref_id": "BIBREF48"}, {"start": 858, "end": 878, "text": "(<PERSON> et al., 2010)", "ref_id": "BIBREF26"}, {"start": 1137, "end": 1161, "text": "(<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF60"}, {"start": 1162, "end": 1188, "text": "Hacohen & Weinshall, 2019)", "ref_id": "BIBREF19"}, {"start": 1277, "end": 1298, "text": "(<PERSON> et al., 2017;", "ref_id": "BIBREF16"}, {"start": 1299, "end": 1321, "text": "<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF32"}, {"start": 1322, "end": 1340, "text": "<PERSON> et al., 2020)", "ref_id": "BIBREF69"}, {"start": 1490, "end": 1513, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF51"}, {"start": 1530, "end": 1549, "text": "(<PERSON> et al., 2018a;", "ref_id": null}, {"start": 1550, "end": 1567, "text": "<PERSON> et al., 2019)", "ref_id": "BIBREF44"}, {"start": 1595, "end": 1615, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF67"}], "ref_spans": [], "eq_spans": [], "section": "Curriculum Learning", "sec_num": "2.1."}, {"text": "To the best of our knowledge, CurBench is the first benchmark on curriculum learning. Despite no related benchmarks, there have been numerous efforts to investigate and summarize the curriculum learning methods from different perspectives. For example, <PERSON> et al. (<PERSON> et al., 2021a) survey curriculum learning and propose a general framework to cover the related methods by abstracting them into two key components, i.e., a difficulty measurer to tell what data or task is easy or hard to learn and a learning scheduler to decide when to learn the easier or harder part, and further categorize the methods according to the implementation of these two components. <PERSON><PERSON><PERSON> et al. (<PERSON><PERSON><PERSON> et al., 2022) also survey curriculum learning and propose a generic algorithm for it based on the definition of machine learning, i.e., data, modal, and task, and organize the methods according to their application domains and tasks. <PERSON><PERSON><PERSON> et al. (<PERSON><PERSON><PERSON> et al., 2020) survey the relevant methods applied to RL and abstract them into three steps, i.e., task generation, sequencing, and transfer learning. <PERSON><PERSON><PERSON> et al. (<PERSON><PERSON><PERSON> et al., 2020 ) also focus on curriculum learning for RL, and classify the methods based on three questions, i.e., why, what control, and what optimize. <PERSON> et al. (<PERSON> et al., 2023) review the tailored methods for graph, and group them according to the tasks, i.e., node-level, link-level, and graph-level. However, these works only summarize and analyze the methods from the theoretical aspect. On the other hand, <PERSON> et al. (<PERSON> et al., 2022a) develop CurML, a code library for curriculum learning, which designs a unified framework for the reproduction and comparison of existing methods from the empirical aspect. Nevertheless, it can only conduct experiments on a single task within a specific domain, significantly limiting its generality and reliability. Therefore, it is necessary to develop a benchmark across diverse experimental setups for a fair, reliable, and systematic study on curriculum learning.", "cite_spans": [{"start": 265, "end": 285, "text": "(<PERSON> et al., 2021a)", "ref_id": null}, {"start": 681, "end": 703, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF47"}, {"start": 940, "end": 963, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF35"}, {"start": 1100, "end": 1138, "text": "<PERSON><PERSON><PERSON> et al. (<PERSON><PERSON><PERSON> et al., 2020", "ref_id": "BIBREF37"}, {"start": 1278, "end": 1305, "text": "<PERSON> et al. (<PERSON> et al., 2023)", "ref_id": "BIBREF30"}, {"start": 1551, "end": 1571, "text": "(<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Summative Work on Curriculum Learning", "sec_num": "2.2."}, {"text": "In this section, we describe our design for the benchmark in detail. First, we clarify the scope of this benchmark in Section 3.1. Then, we introduce the adopted datasets in Section 3.2, followed by the corresponding settings in Section 3.3 and the backbone models in Section 3.4. Lastly, we elaborate on the evaluation dimensions in Section 3.5.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Curriculum Learning Benchmark", "sec_num": "3."}, {"text": "CurBench focuses on benchmarking existing prevalent curriculum learning methods for supervised tasks in CV, NLP, and graph domains. This is because CV and NLP are representative research domains in machine learning, with datasets in these areas frequently used to validate the performance of curriculum learning methods, as shown in Table 6 .", "cite_spans": [], "ref_spans": [{"start": 339, "end": 340, "text": "6", "ref_id": "TABREF8"}], "eq_spans": [], "section": "Benchmark Scope", "sec_num": "3.1."}, {"text": "Graph data, being structured, differs from the unstructured data of images and text, contributing to the diversity of CurBench, and curriculum learning in the graph domain has gained significant attention recently. Besides, the main challenge of the tasks included in CurBench lies in designing appropriate curricula at the data level so that the models can be guided to better cope with standard, noisy, and imbalanced datasets. In contrast, the methods designed at the task level and specifically targeting the RL domain are not within the scope of this work. We plan to expand the scope of CurBench in a future version, as stated in Section 6.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Benchmark Scope", "sec_num": "3.1."}, {"text": "Table 1 outlines the datasets included in CurBench, all of which are publicly available and widely used in their respective domains. Besides, they vary in scale from hundreds of samples to hundreds of thousands. A brief introduction to the datasets and our preprocessing is listed as follows.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Dataset", "sec_num": "3.2."}, {"text": "CV Domain: CIFAR-10 and CIFAR-100 (<PERSON><PERSON><PERSON><PERSON> et al., 2009) consist of 32 × 32 × 3 color images in 10 and 100 classes respectively. Tiny-ImageNet (<PERSON> <PERSON>, 2015) is a subset of the ILSVRC2012 version of ImageNet (<PERSON><PERSON> et al., 2009) and consists of 64 × 64 × 3 down-sampled images. Since the test set of Tiny-ImageNet is not released with labels, we use the validation set as the test set. For these 3 datasets, we split the original training set into a new training set and a validation set with a 9:1 ratio.", "cite_spans": [{"start": 34, "end": 59, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF25"}, {"start": 146, "end": 163, "text": "(<PERSON>, 2015)", "ref_id": "BIBREF28"}, {"start": 214, "end": 233, "text": "(<PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF7"}], "ref_spans": [], "eq_spans": [], "section": "Dataset", "sec_num": "3.2."}, {"text": "NLP Domain: All 8 datasets are sourced from GLUE (<PERSON> et al., 2018) , which is a collection of tools for evaluating models across diverse natural language understanding tasks. GLUE originally contains 9 datasets, and we follow BERT (<PERSON> et al., 2018) , excluding the problematic WNLI set and using the remaining 8 datasets. Since the test sets are not released with labels, we report the results on the validation sets.", "cite_spans": [{"start": 49, "end": 68, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF53"}, {"start": 233, "end": 254, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "Dataset", "sec_num": "3.2."}, {"text": "Graph Domain: The ogbg-molhiv dataset belongs to Open Graph Benchmark (OGB) (<PERSON> et al., 2020) , a collection of realistic, large-scale, and diverse benchmark datasets for graphs. We strictly follow its origin split scheme, split ratios, and metrics. The other 3 datasets come from TU-Dataset (<PERSON> et al., 2020) , a collection that consists of over 120 graph datasets of varying sizes from a wide range of applications. Since there are no established training and test set split, we randomly divide the original datasets into training, validation, and test sets with an 8:1:1 ratio.", "cite_spans": [{"start": 76, "end": 93, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF21"}, {"start": 292, "end": 313, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Dataset", "sec_num": "3.2."}, {"text": "To robustly evaluate the curriculum learning methods, we establish the 3 settings as follows.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Setting", "sec_num": "3.3."}, {"text": "Standard: After dividing the datasets into training, validation, and test sets as mentioned above, we do not perform any further data processing.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Setting", "sec_num": "3.3."}, {"text": "Noise-p: We follow previous works (<PERSON> et al., 2016; <PERSON> et al., 2018a; <PERSON> et al., 2019) and apply uniform noise by independently changing the label of each sample in the training set to a random one with a probability of p ∈ (0.0, 1.0]. When p = 0, it degenerates to the standard setting.", "cite_spans": [{"start": 34, "end": 54, "text": "(<PERSON> et al., 2016;", "ref_id": "BIBREF66"}, {"start": 55, "end": 73, "text": "<PERSON> et al., 2018a;", "ref_id": null}, {"start": 74, "end": 91, "text": "<PERSON> et al., 2019)", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "Setting", "sec_num": "3.3."}, {"text": "Imbalance-r: We follow previous works (<PERSON><PERSON> et al., 2019; <PERSON> et al., 2019) to form a long-tailed dataset by reducing the number of samples per class in the training set. Let c ∈ {0, 1, 2, ..., C -1} be the class index, C be the number of classes, n c be the number of samples in the c th class, and then an originally balanced dataset satisfies", "cite_spans": [{"start": 38, "end": 56, "text": "(<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF6"}, {"start": 57, "end": 74, "text": "<PERSON> et al., 2019)", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "Setting", "sec_num": "3.3."}, {"text": "n 0 ≈ n 1 ≈ ... ≈ n C-1 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Setting", "sec_num": "3.3."}, {"text": "We implement the imbalance setting by requiring n c to follow the exponential function n c = n 0 d c where d ∈ (0, 1) and define the imbalance factor r = n 0 : n C-1 as the ratio between the number of samples in the largest class and that in the smallest class. When r = 1, it degenerates to the standard setting.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Setting", "sec_num": "3.3."}, {"text": "LeNet Convolution ∼ 0.07M ResNet-18 Convolution ∼ 11.2M ViT Attention ∼ 9.6M NLP LSTM Recurrent ∼ 10.4M BERT Attention ∼ 109M GPT2 Attention ∼ 124M Graph GCN Convolution ∼ 0.01M GAT Attention ∼ 0.14M GIN Isomorphism ∼ 0.01M", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CV", "sec_num": null}, {"text": "Table 2 . The statistics of 9 backbone models adopted in CurBench, which covers various mechanisms and scales. \"∼\" signifies an approximation, and \"M\" represents million. (<PERSON> et al., 2018) is developed based on <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> test theory and emphasizes the importance of summation as the readout function.", "cite_spans": [{"start": 171, "end": 188, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF62"}], "ref_spans": [{"start": 6, "end": 7, "text": "2", "ref_id": null}], "eq_spans": [], "section": "CV", "sec_num": null}, {"text": "To ensure a comprehensive analysis of existing methods, we consider the following 2 evaluation dimensions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "3.5."}, {"text": "Performance: We adopt the widely accepted metrics on each dataset, such as accuracy on image, F1 score, <PERSON><PERSON><PERSON> Correlation, and <PERSON> Correlation on the GLUE benchmark, AUC (<PERSON> et al., 2021) on graph. To display the results clearly, we report the average and standard deviation of the metric over 5 runs for each dataset.", "cite_spans": [{"start": 178, "end": 197, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF63"}], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "3.5."}, {"text": "Complexity: It is essential to examine the time and space complexity of each method because they always cost extra computational time and sources to assess model competence and data difficulty for appropriate curricula design. We record the training time and maximum memory consumption on the same GPU device as the indicators of the complexity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "3.5."}, {"text": "To facilitate the use of our CurBench, we develop a companion toolkit based on CurML (<PERSON> et al., 2022a) for the entire pipeline of applying curriculum learning to various machine learning tasks, reproducing 15 core methods. Compared to CurML, this toolkit extends the methods to accommodate inputs in various data formats and diverse output evaluation metrics and provides searched hyperparameters for each method. As illustrated in Figure 2 , we summarize and abstract the whole toolkit into 5 modules: data processing, model loading, objective fitting, curriculum learning, and evaluation.", "cite_spans": [{"start": 85, "end": 105, "text": "(<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [{"start": 442, "end": 443, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "4.1."}, {"text": "Data Processing: This module aims to prepare data according to the specified dataset and setting. Given a data name in a format like \"cifar10\", \"cifar100-noise-p\" or \"tinyimagenetimbalance-r\", this module can automatically parse it, split the dataset into training, validation, and test set, and process the training set by adding noise with probability p or forming imbalance with factor r.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "4.1."}, {"text": "Model Loading: This module is used to initialize the model based on the model name and the target dataset. For instance, CV models need to modify their input layer to accommodate input images and patch sizes. Similarly, graph models require node features and edge relationships when construct- ing graph convolutional layers. Besides, the class number of the dataset determines the models' output layer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "4.1."}, {"text": "Objective Fitting: This module handles the process where models learn and fit datasets to accomplish target tasks. For different research domains, we select tailored hyperparameters, optimizers, loss functions, and so on. Unlike common machine learning, the training procedure in this module is guided by the curriculum learning module.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Performance", "sec_num": null}, {"text": "Curriculum Learning: This module integrates 15 core curriculum learning methods, all of which are abstracted as a class for easy plug-in into the objective fitting module. This design of abstracting methods as classes ensures that the module is extensible for new methods. Currently, we divide the existing methods into the following 3 categories. It is worth noting that this categorization is intended to facilitate the implementation and extension of various methods within a unified framework, but it does not imply that methods within the same category necessarily share similar properties or performance.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Performance", "sec_num": null}, {"text": "• via Data Selection: The primary approach to implementing curriculum is through data selection so that models can progressively learn from a subset to the entire dataset in a meaningful order. The methods belong to this category are vanilla SPL (<PERSON> et al., 2010) , DIHCL (<PERSON> et al., 2020) , and so on (<PERSON><PERSON> et al., 2018; Zhou & Bilmes, 2018; <PERSON> et al., 2019; <PERSON> et al., 2021; <PERSON> et al., 2023b) . Some methods select data subsets based on sample difficulty, while others select data based on sample class.", "cite_spans": [{"start": 246, "end": 266, "text": "(<PERSON> et al., 2010)", "ref_id": "BIBREF26"}, {"start": 275, "end": 294, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF71"}, {"start": 307, "end": 331, "text": "(<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF60"}, {"start": 332, "end": 352, "text": "Zhou & Bilmes, 2018;", "ref_id": "BIBREF70"}, {"start": 353, "end": 372, "text": "<PERSON> et al., 2019;", "ref_id": "BIBREF5"}, {"start": 373, "end": 391, "text": "Kong et al., 2021;", "ref_id": "BIBREF24"}, {"start": 392, "end": 411, "text": "<PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Performance", "sec_num": null}, {"text": "• via Model Adjustment: An innovative idea for designing curricula is to regulate the amount of data information the model receives by modifying its architecture. CBS (<PERSON><PERSON> et al., 2020) , which employs a Gaussian filter to manage information intake, is a typical one.", "cite_spans": [{"start": 167, "end": 187, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF45"}], "ref_spans": [], "eq_spans": [], "section": "Performance", "sec_num": null}, {"text": "• via Loss Reweighting: Loss reweighting can be regarded as a \"soft\" version of data selection. Intuitively, assigning a low weight to a data sample is almost equivalent to disregarding it. A common practice to reweight loss is through meta-learning (<PERSON> et al., 2017) , such as LRE (<PERSON> et al., 2018a) , MW-Net (<PERSON> et al., 2019) , and DDS (<PERSON> et al., 2020) , all of which employ a meta-network to assess the weights of losses and optimize the meta-network with the validation set. Additionally, there are other approaches, such as variants of SPL (<PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2020) , DCL (<PERSON><PERSON><PERSON> et al., 2019) , ScreenerNet (Kim & Choi, 2018) , and SuperLoss (<PERSON><PERSON><PERSON> et al., 2020) .", "cite_spans": [{"start": 250, "end": 269, "text": "(<PERSON> et al., 2017)", "ref_id": null}, {"start": 284, "end": 303, "text": "(<PERSON> et al., 2018a)", "ref_id": null}, {"start": 313, "end": 331, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF44"}, {"start": 342, "end": 361, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF54"}, {"start": 552, "end": 570, "text": "(<PERSON> et al., 2017;", "ref_id": "BIBREF11"}, {"start": 571, "end": 593, "text": "<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF1"}, {"start": 600, "end": 621, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF43"}, {"start": 636, "end": 654, "text": "(<PERSON>, 2018)", "ref_id": "BIBREF22"}, {"start": 671, "end": 694, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "Performance", "sec_num": null}, {"text": "Evaluation: This module is utilized to report results from 2 aspects, i.e., performance and complexity, in order to respectively demonstrate the effectiveness and efficiency of different methods. The performance metrics depend on the target datasets and tasks, and the complexity metrics include training time and maximum GPU memory consumption.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Performance", "sec_num": null}, {"text": "Figure 3 illustrates the python-like sample code of our CurBench toolkit, where an object of the SPLTrainer class is instantiated given the essential parameters, including a CIFAR-10 dataset name with the noise setting for data processing and a ResNet-18 net name for model loading. All of the above are put together to fit and evaluate the final result.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Example Usage", "sec_num": "4.2."}, {"text": "With only a few lines of code, a dozen curriculum learning methods can be easily implemented and reproduced. On the basis of this tool, we conduct a multitude of experiments, and we will report the experimental setups and results in the next section.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Example Usage", "sec_num": "4.2."}, {"text": "To ensure a fair and reproducible evaluation, we fix all possible confounding factors and report the average and standard deviation results of 5 runs with different fixed random seeds for each combination of datasets, backbone models, and settings. The detailed hyperparameters for both training processes and curriculum learning methods are presented in the Appendix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setup", "sec_num": "5.1."}, {"text": "Table 3 presents the overall performances with and without curriculum learning under different combinations of backbone models, datasets, and settings. The detailed results of each specific curriculum learning method are attached in the Appendix, and we report the best ones among them in this table. The imbalance setting is not applied to NLP and graph datasets, where the number of samples in each class is imbalanced originally.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "3", "ref_id": null}], "eq_spans": [], "section": "Main Results", "sec_num": "5.2.1."}, {"text": "It is observed that curriculum learning can bring consistent improvement across domains. Compared to standard and imbalance settings, curriculum learning benefits much more in noise settings. This phenomenon is consistent with existing theoretical analysis, where curriculum learning is able to denoise and guide machine learning by discarding the difficult and possibly noisy data in the early stages of training. Besides, there is no such method that can outperform the others all the time, and the effectiveness of curriculum learning methods still depends on the target scenarios. For example, ScreenerNet (Kim & Choi, 2018) exhibits superior performance on CV datasets compared to graph datasets, and TTCL (<PERSON> et al., 2018) performs better in noise settings than in standard and imbalance ones. Therefore, it is essential to explore more general methods while also researching methods tailored to specific environments.", "cite_spans": [{"start": 610, "end": 628, "text": "(<PERSON>, 2018)", "ref_id": "BIBREF22"}, {"start": 711, "end": 735, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF60"}], "ref_spans": [], "eq_spans": [], "section": "Main Results", "sec_num": "5.2.1."}, {"text": "Figure 4 demonstrates the performances of curriculum learning methods on datasets with different noise ratios p ∈ {0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8}. Without loss of generality, we select a backbone model and a dataset from each research domain. Some methods such as CBS, LGL, C2F, and EfficientTrain are only applied to CV datasets and not to NLP and graph datasets due to the following reasons. CBS (<PERSON><PERSON> et al., 2020) requires convolutional layers in backbone models, and such models in CurBench are only within the CV domain. LGL (<PERSON> et al., 2019) and C2F (<PERSON><PERSON><PERSON> et al., 2021) require multiple classes for clustering, but most NLP and graph datasets in CurBench have only two classes. EfficientTrain (<PERSON> et al., 2023b) is based on data augmentation techniques on images.", "cite_spans": [{"start": 412, "end": 432, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF45"}, {"start": 546, "end": 566, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF5"}, {"start": 575, "end": 597, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF49"}, {"start": 721, "end": 741, "text": "(<PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [{"start": 7, "end": 8, "text": "4", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Results in Noise Settings", "sec_num": "5.2.2."}, {"text": "We can observe that TTCL (<PERSON><PERSON><PERSON> et al., 2018) , the method by teacher transferring, obtains competitive performances regardless of the noise ratio, thanks to the guidance from the teacher model pretrained on the clean dataset. In contrast, SPL (<PERSON> et al., 2010) , which is similar to TTCL but guides the learning by itself, performs relatively poorly. It is because a model not fully trained is not that competent to accurately distinguish noisy or hard data.", "cite_spans": [{"start": 25, "end": 49, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF60"}, {"start": 248, "end": 268, "text": "(<PERSON> et al., 2010)", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "Results in Noise Settings", "sec_num": "5.2.2."}, {"text": "Figure 5 depicts the performances on CIFAR-10 with varying imbalance factor r ∈ {1, 10, 20, 50, 100, 200}.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "5", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Results in Imbalance Settings", "sec_num": "5.2.3."}, {"text": "It is observed that all methods achieve similar performances under different imbalance ratios. When the imbalance factor r increases, the differences between the methods become evident. Relatively speaking, the methods by data reweighting, such as DCL (<PERSON><PERSON><PERSON> et al., 2019) and SuperLoss (<PERSON><PERSON><PERSON> et al., 2020) , perform well because they can mitigate the impact of imbalanced classes by reassigning the weight of data or even class.", "cite_spans": [{"start": 252, "end": 273, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF43"}, {"start": 288, "end": 311, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "Results in Imbalance Settings", "sec_num": "5.2.3."}, {"text": "Compared with noise settings, curriculum learning brings less significant improvements and shows less variation between methods in imbalance settings. This is primarily because most curriculum learning methods focus on the difficulty of samples instead of classes, leading to overall better performances in noise settings than in imbalance settings. Additionally, the differences in judging difficult or noisy samples result in larger performance disparities among methods in noise settings. Table 3 . The empirical performances of 9 backbone models over 15 datasets in 3 settings with and without curriculum learning methods. The rows with \"+ CL\" present the best performances achieved among the methods involved in this benchmark. The bold font highlights the superior performances brought by curriculum learning. The imbalance setting is not applied to NLP and graph datasets, which are imbalanced originally. Note: The detailed performances of each method are reported in Table 9 -11 in the Appendix. ", "cite_spans": [], "ref_spans": [{"start": 498, "end": 499, "text": "3", "ref_id": null}, {"start": 982, "end": 983, "text": "9", "ref_id": null}], "eq_spans": [], "section": "Results in Imbalance Settings", "sec_num": "5.2.3."}, {"text": "Figure 6 shows the time and space complexity of each method in the case of ResNet-18 and CIFAR-10, measured by GPU training time (Hour) and maximum GPU memory consumption (GB).", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "6", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Complexity", "sec_num": "5.3."}, {"text": "The whole figure can be divided into 3 parts. The first is the upper right corner, which contains the methods requiring gradient calculation and meta-network training, resulting in high time and space complexity. The second is the middle part with the point of ScreenerNet, which also introduces an extra network but only requires once backward, leading to less complexity. The third is the lower left corner, which includes most of the methods consuming similarly small amounts of training time and GPU memory because they measure data difficulty and schedule curriculum in a relatively intuitive way and do not demand a learnable network with a large number of parameters. 8 in the Appendix.", "cite_spans": [], "ref_spans": [{"start": 675, "end": 676, "text": "8", "ref_id": "TABREF11"}], "eq_spans": [], "section": "Complexity", "sec_num": "5.3."}, {"text": "In this paper, we propose CurBench, the first benchmark for curriculum learning. It covers a broad range of research domains, datasets, backbone models, settings, and evaluation dimensions, ensuring a fair, reliable, and systematic evaluation of existing curriculum learning methods. For convenient utilization, it is complemented by a toolkit that implements essential related works in a unified pipeline and applies them to various machine learning tasks. Through empirical results and theoretical analyses, we provide valuable findings on curriculum learning. In conclusion, CurBench holds the potential to benefit future research and suggest promising directions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "Limitations: Despite the benefits of our CurBench, we also recognize the following limitations in this version and intend to refine them in future expansions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "• CurBench mainly covers supervised learning in CV, NLP, and graph domains, but has not incorporated the datasets, backbone models, and tasks related to other domains such as audio processing, multimodal learning, recommender systems, and robotics. Additionally, CurBench has not involved unsupervised, semi-supervised, and reinforcement learning. Given the importance of these topics in the context of curriculum learning applications, they will be integrated as a significant part of future versions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "• CurBench currently employs publicly available datasets that are commonly used in their respective domains. However, CurBench has not yet introduced any new datasets. Designing specialized datasets for curriculum learning is essential because these datasets can better align with the unique requirements and objectives of curriculum learning methodologies. We recognize the importance of this task and intend to undertake it in the future.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "• CurBench has not evaluated the performance of curriculum learning on large models, which deserves in-depth exploration in this era of large models. Considering that large models often encounter vast amounts of data with varying quality when learning, it is suitable to utilize curriculum learning for guidance and denoising. We plan to include the prevalent large-scale language and multimodal models in our future work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6."}, {"text": "This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, none of which we feel must be specifically highlighted here.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "In this appendix, we first list the essential information of the datasets in Section B and backbone models in Section C. Then we summarize the curriculum methods implemented in this work in Section D to present how these methods were evaluated when they were proposed. After providing the training hyperparameters in Section E and method hyperparameters in Section F, we report the detailed performance and complexity of each method in various experimental setups in Section G.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impact Statement", "sec_num": null}, {"text": "All the datasets included in CurBench are publicly available for research. To eliminate the risk of ethical or license issues, we list the essential information of the datasets, such as their home pages, common download links, and licenses. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B. Datasets", "sec_num": null}, {"text": "For the standardization and reliability of CurBench, we implement all backbone models by referencing highly recognized code repositories as shown in Table 5 . Among these models, BERT and GPT2 are initiated with the pretrained parameters from Hugging Face and finetuned in this work, while others are trained from scratch.", "cite_spans": [], "ref_spans": [{"start": 155, "end": 156, "text": "5", "ref_id": "TABREF7"}], "eq_spans": [], "section": "<PERSON><PERSON>", "sec_num": null}, {"text": "When designing CurBench, we are inclined to the datasets and models used in previous works for evaluation. Therefore, we have surveyed what datasets and models are commonly employed and completed the Table 6 .", "cite_spans": [], "ref_spans": [{"start": 206, "end": 207, "text": "6", "ref_id": "TABREF8"}], "eq_spans": [], "section": "D. Curriculum Learning Methods", "sec_num": null}, {"text": "It can be obviously found that when researchers propose a curriculum learning method, they always conduct experiments on image classification tasks for performance evaluation. Only a few authors will try to apply their methods to the datasets for object detection or neural machine translation. Besides, not all works take different settings, such as noise or imbalance, into consideration.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D. Curriculum Learning Methods", "sec_num": null}, {"text": "Therefore, as stated in the main text, we not only select the datasets and models in the CV domain, which are commonly used in previous related works, but also supplement those in the NLP and graph domains to investigate how the methods can adapt to various scenarios.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D. Curriculum Learning Methods", "sec_num": null}, {"text": "To ensure a fair evaluation, we run 5 times with fixed different random seeds s ∈ {42, 666, 777, 888, 999}, and report the average and standard deviation results. Besides, we strictly set the training hyperparameters as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E. Training Hyperparameters", "sec_num": null}, {"text": "LeNet, ResNet-18, ViT: We choose a batch size of 50, and use an Adam optimizer to train the model with a constant learning rate of 0.0001 for 200 epochs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E. Training Hyperparameters", "sec_num": null}, {"text": "We choose a batch size of 50, and use a SGD optimizer to train the model with a cosine annealing learning rate of 0.00001∼1 for 10 epochs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LSTM:", "sec_num": null}, {"text": "BERT, GPT2: We choose a batch size of 50, and use an AdamW optimizer to train the model with a constant learning rate of 0.00002 for 3 epochs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LSTM:", "sec_num": null}, {"text": "We choose a batch size of 50, and use an Adam optimizer to train the model for 200 epochs with learning rates of 0.01 for TUDataset and 0.001 for OGB.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GCN, GAT, GIN:", "sec_num": null}, {"text": "For a reproducible evaluation, we demonstrate the hyperparameters that we select for curriculum learning methods in Table 7 . It should be noted that this table includes the hyperparameters for the experiments with 200 epochs. For text domain tasks trained for 3 or 10 epochs, we sightly adjust some epoch-related hyperparameters to adapt the tasks, such as grow epochs, warm epochs, and schedule epochs.", "cite_spans": [], "ref_spans": [{"start": 122, "end": 123, "text": "7", "ref_id": "TABREF9"}], "eq_spans": [], "section": "<PERSON><PERSON> Hyperparameters", "sec_num": null}, {"text": "Tables from 8 to 11 report complexity and performance. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON> Detailed Complexity and Performance", "sec_num": null}], "back_matter": [{"text": "Acknowledgements This work is supported by the National Key Research and Development Program of China No.2023YFF1205001, National Natural Science Foundation of China (No. 62222209, 62250008, 62102222), Beijing National Research Center for Information Science and Technology under Grant No. BNR2023RC01003, BNR2023TD03006, and Beijing Key Lab of Networked Multimedia.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "acknowledgement", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Curriculum learning", "authors": [{"first": "Y", "middle": [], "last": "References <PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Weston", "suffix": ""}], "year": 2009, "venue": "Proceedings of the 26th annual international conference on machine learning", "volume": "", "issue": "", "pages": "41--48", "other_ids": {}, "num": null, "urls": [], "raw_text": "References <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, J. Curric<PERSON> learning. In Proceedings of the 26th annual international conference on machine learning, pp. 41-48, 2009.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Superloss: A generic loss for robust curriculum learning", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>ep<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "4308--4319", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: A generic loss for robust curriculum learning. Advances in Neural Information Processing Systems, 33:4308-4319, 2020.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Curriculum disentangled recommendation with noisy multi-feedback", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "26924--26936", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, W. Curric<PERSON> disentangled recommendation with noisy multi-feedback. Advances in Neural Informa- tion Processing Systems, 34:26924-26936, 2021a.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Curriculum-listener: Consistency-and complementarity-aware audio-enhanced temporal sentence grounding", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Lan", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 31st ACM International Conference on Multimedia", "volume": "", "issue": "", "pages": "3117--3128", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>-listener: Consistency-and complementarity-aware audio-enhanced temporal sen- tence grounding. In Proceedings of the 31st ACM In- ternational Conference on Multimedia, pp. 3117-3128, 2023.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Curriculum meta-learning for next poi recommendation", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Fan", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the 27th ACM SIGKDD Conference on Knowledge Discovery & Data Mining", "volume": "", "issue": "", "pages": "2692--2702", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, W. <PERSON>urriculum meta-learning for next poi recom- mendation. In Proceedings of the 27th ACM SIGKDD Conference on Knowledge Discovery & Data Mining, pp. 2692-2702, 2021b.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Local to global learning: Gradually adding classes for training deep neural networks", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Gao", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Geng", "suffix": ""}], "year": 2019, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "4748--4756", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y. Local to global learning: Gradually adding classes for training deep neural networks. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 4748-4756, 2019.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Classbalanced loss based on effective number of samples", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T.-Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Song", "suffix": ""}, {"first": "S", "middle": [], "last": "Belongie", "suffix": ""}], "year": 2019, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "9268--9277", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. Class- balanced loss based on effective number of samples. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 9268-9277, 2019.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Imagenet: A large-scale hierarchical image database", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L.-J", "middle": [], "last": "Li", "suffix": ""}, {"first": "K", "middle": [], "last": "Li", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "2009 IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "248--255", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, L. Imagenet: A large-scale hierarchical image database. In 2009 IEEE conference on computer vision and pattern recognition, pp. 248-255. Ieee, 2009.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Pre-training of deep bidirectional transformers for language understanding", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M.-W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1810.04805"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>: Pre-training of deep bidirectional transformers for lan- guage understanding. arXiv preprint arXiv:1810.04805, 2018.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "A", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "X", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.11929"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint arXiv:2010.11929, 2020.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Learning and development in neural networks: The importance of starting small", "authors": [{"first": "J", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1993, "venue": "Cognition", "volume": "48", "issue": "1", "pages": "71--99", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> Learning and development in neural networks: The importance of starting small. Cognition, 48(1):71-99, 1993.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Self-paced learning: An implicit regularization perspective", "authors": [{"first": "Y", "middle": [], "last": "Fan", "suffix": ""}, {"first": "R", "middle": [], "last": "He", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Hu", "suffix": ""}], "year": 2017, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Self-paced learning: An implicit regularization perspective. In Proceedings of the AAAI Conference on Artificial Intelligence, vol- ume 31, 2017.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Model-agnostic metalearning for fast adaptation of deep networks", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "1126--1135", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>-agnostic meta- learning for fast adaptation of deep networks. In Interna- tional conference on machine learning, pp. 1126-1135.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Reverse curriculum generation for reinforcement learning", "authors": [{"first": "C", "middle": [], "last": "Florensa", "suffix": ""}, {"first": "D", "middle": [], "last": "Held", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Conference on robot learning", "volume": "", "issue": "", "pages": "482--495", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>erse curriculum generation for reinforce- ment learning. In Conference on robot learning, pp. 482- 495. PMLR, 2017.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Why curriculum learning & self-paced learning work in big/noisy data: A theoretical perspective", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2016, "venue": "Big Data & Information Analytics", "volume": "1", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Why curricu- lum learning & self-paced learning work in big/noisy data: A theoretical perspective. Big Data & Information Analytics, 1(1):111, 2016.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Automated curriculum learning for neural networks", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["G"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Kavukcuoglu", "suffix": ""}], "year": 2017, "venue": "In international conference on machine learning", "volume": "", "issue": "", "pages": "1311--1320", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Automated curriculum learning for neural networks. In international conference on machine learning, pp. 1311-1320. PMLR, 2017.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Curriculum learning for facial expression recognition", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L.-P", "middle": [], "last": "Morency", "suffix": ""}], "year": 2017, "venue": "IEEE International Conference on Automatic Face & Gesture Recognition", "volume": "", "issue": "", "pages": "505--511", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, L.-P. Curriculum learning for facial expression recognition. In 2017 12th IEEE International Conference on Automatic Face & Ges- ture Recognition (FG 2017), pp. 505-511. IEEE, 2017.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Curriculumnet: Weakly supervised learning from large-scale web images", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the European conference on computer vision (ECCV)", "volume": "", "issue": "", "pages": "135--150", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Weakly supervised learning from large-scale web images. In Proceedings of the European conference on computer vision (ECCV), pp. 135-150, 2018.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "On the power of curriculum learning in training deep networks", "authors": [{"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Weinshall", "suffix": ""}, {"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ren", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}], "year": 2016, "venue": "Proceedings of the IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "770--778", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> On the power of curriculum learning in training deep networks. In International Con- ference on Machine Learning, pp. 2535-2544. PMLR, 2019. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Deep residual learn- ing for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition, pp. 770-778, 2016.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Long short-term memory", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>reiter", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1997, "venue": "Neural computation", "volume": "9", "issue": "8", "pages": "1735--1780", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> short-term memory. Neural computation, 9(8):1735-1780, 1997.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Open graph benchmark: Datasets for machine learning on graphs", "authors": [{"first": "W", "middle": [], "last": "Hu", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Zitnik", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Ren", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Catasta", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2005.00687"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, J. Open graph benchmark: Datasets for machine learning on graphs. arXiv preprint arXiv:2005.00687, 2020.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Learning self-paced curriculum for deep neural networks", "authors": [{"first": "T.-H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Screenernet", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1801.00904"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON>: Learning self-paced curriculum for deep neural networks. arXiv preprint arXiv:1801.00904, 2018.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Semi-supervised classification with graph convolutional networks", "authors": [{"first": "T", "middle": ["N"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Welling", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1609.02907"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> Semi-supervised classifica- tion with graph convolutional networks. arXiv preprint arXiv:1609.02907, 2016.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Adaptive curriculum learning", "authors": [{"first": "Y", "middle": [], "last": "Kong", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Tao", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "5067--5076", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, D. Adaptive curriculum learning. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 5067-5076, 2021.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Learning multiple layers of features from tiny images", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Learning multiple layers of features from tiny images. 2009.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Advances in neural information processing systems", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>er", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2010, "venue": "", "volume": "23", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Self-paced learning for latent variable models. Advances in neural informa- tion processing systems, 23, 2010.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Curriculum multi-negative augmentation for debiased video grounding", "authors": [{"first": "X", "middle": [], "last": "Lan", "suffix": ""}, {"first": "Y", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "37", "issue": "", "pages": "1213--1221", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>urric<PERSON> multi-negative augmentation for debiased video grounding. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 37, pp. 1213-1221, 2023.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Tiny imagenet visual recognition challenge", "authors": [{"first": "Y", "middle": [], "last": "Le", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "CS", "volume": "231", "issue": "7", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> imagenet visual recognition chal- lenge. CS 231N, 7(7):3, 2015.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Gradientbased learning applied to document recognition. Proceedings of the IEEE", "authors": [{"first": "Y", "middle": [], "last": "Lecun", "suffix": ""}, {"first": "L", "middle": [], "last": "Bo<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1998, "venue": "", "volume": "86", "issue": "", "pages": "2278--2324", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, P. Gradient- based learning applied to document recognition. Proceed- ings of the IEEE, 86(11):2278-2324, 1998.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Curriculum graph machine learning: A survey", "authors": [{"first": "H", "middle": [], "last": "Li", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.02926"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, W. Curriculum graph machine learning: A survey. arXiv preprint arXiv:2302.02926, 2023.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Curriculum learning for natural answer generation", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "He", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "IJCAI", "volume": "", "issue": "", "pages": "4223--4229", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Curriculum learning for natural answer generation. In IJCAI, pp. 4223-4229, 2018.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Teacher-student curriculum learning", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "IEEE transactions on neural networks and learning systems", "volume": "31", "issue": "9", "pages": "3732--3740", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, J. Teacher-student curriculum learning. IEEE transactions on neural networks and learning systems, 31(9):3732- 3740, 2019.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Tudataset: A collection of benchmark datasets for learning with graphs", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": ["M"], "last": "Kriege", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2007.08663"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: A collection of bench- mark datasets for learning with graphs. arXiv preprint arXiv:2007.08663, 2020.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Autonomous task sequencing for customized curriculum design in reinforcement learning", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Stone", "suffix": ""}], "year": 2017, "venue": "IJCAI", "volume": "", "issue": "", "pages": "2536--2542", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, P. Autonomous task sequencing for customized curriculum design in reinforce- ment learning. In IJCAI, pp. 2536-2542, 2017.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Curriculum learning for reinforcement learning domains: A framework and survey", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Stone", "suffix": ""}], "year": 2020, "venue": "The Journal of Machine Learning Research", "volume": "21", "issue": "1", "pages": "7382--7431", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, P. Curriculum learning for reinforce- ment learning domains: A framework and survey. The Journal of Machine Learning Research, 21(1):7382-7431, 2020.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Competence-based curriculum learning for neural machine translation", "authors": [{"first": "E", "middle": ["A"], "last": "Platanios", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Neubig", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": ["M"], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1903.09848"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, T. M. Competence-based curriculum learning for neural machine translation. arXiv preprint arXiv:1903.09848, 2019.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Automatic curriculum learning for deep rl: A short survey", "authors": [{"first": "R", "middle": [], "last": "Portelas", "suffix": ""}, {"first": "C", "middle": [], "last": "Colas", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P.-Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2003.04664"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, R<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, P.-Y. Automatic curriculum learning for deep rl: A short survey. arXiv preprint arXiv:2003.04664, 2020.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Multitask graph neural architecture search with task-aware collaboration and curriculum. Advances in neural information processing systems", "authors": [{"first": "Y", "middle": [], "last": "Qin", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, W. Multi- task graph neural architecture search with task-aware collaboration and curriculum. Advances in neural infor- mation processing systems, 36, 2024.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Language models are unsupervised multitask learners", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Child", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "OpenAI blog", "volume": "1", "issue": "8", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Language models are unsupervised multitask learners. OpenAI blog, 1(8):9, 2019.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Learning to reweight examples for robust deep learning", "authors": [{"first": "M", "middle": [], "last": "Ren", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "4334--4343", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> to reweight examples for robust deep learning. In Interna- tional conference on machine learning, pp. 4334-4343.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Self-paced prioritized curriculum learning with coverage penalty in deep reinforcement learning", "authors": [{"first": "Z", "middle": [], "last": "Ren", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Li", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "IEEE transactions on neural networks and learning systems", "volume": "29", "issue": "6", "pages": "2216--2226", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Self-paced priori- tized curriculum learning with coverage penalty in deep reinforcement learning. IEEE transactions on neural networks and learning systems, 29(6):2216-2226, 2018b.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Data parameters: A new family of parameters for learning a differentiable curriculum", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Decoste", "suffix": ""}], "year": 2019, "venue": "Advances in Neural Information Processing Systems", "volume": "32", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, D. Data parameters: A new family of parameters for learning a differentiable curriculum. Advances in Neural Information Processing Systems, 32, 2019.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Meta-weight-net: Learning an explicit mapping for sample weighting", "authors": [{"first": "J", "middle": [], "last": "Shu", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>g", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> Meta-weight-net: Learning an explicit mapping for sample weighting. Advances in neural information processing systems, 32, 2019.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Curriculum by smoothing", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>arg", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "21653--21664", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON>. <PERSON> by smoothing. Advances in Neural Information Processing Systems, 33:21653-21664, 2020.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Image difficulty curriculum for generative adversarial networks (cugan)", "authors": [{"first": "P", "middle": [], "last": "So<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the IEEE/CVF winter conference on applications of computer vision", "volume": "", "issue": "", "pages": "3463--3472", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Image difficulty curriculum for generative adversarial networks (cugan). In Proceedings of the IEEE/CVF win- ter conference on applications of computer vision, pp. 3463-3472, 2020.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Curriculum learning: A survey", "authors": [{"first": "P", "middle": [], "last": "So<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Rota", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "International Journal of Computer Vision", "volume": "130", "issue": "6", "pages": "1526--1565", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, N. Cur- riculum learning: A survey. International Journal of Computer Vision, 130(6):1526-1565, 2022.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "From baby steps to leapfrog: How \"less is more\" in unsupervised dependency parsing", "authors": [{"first": "V", "middle": ["I"], "last": "Spitkovsky", "suffix": ""}, {"first": "H", "middle": [], "last": "Alshawi", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2010, "venue": "Human Language Technologies: The 2010 Annual Conference of the North American Chapter of the Association for Computational Linguistics", "volume": "", "issue": "", "pages": "751--759", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>. From baby steps to leapfrog: How \"less is more\" in unsupervised de- pendency parsing. In Human Language Technologies: The 2010 Annual Conference of the North American Chapter of the Association for Computational Linguistics, pp. 751-759, 2010.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Coarse-to-fine curriculum learning", "authors": [{"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": ["A"], "last": "Platanios", "suffix": ""}, {"first": "T", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2106.04072"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, T. <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Coarse-to-fine curriculum learning. arXiv preprint arXiv:2106.04072, 2021.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Simple and effective curriculum pointer-generator networks for reading comprehension over long narratives", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1905.10847"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S. C., and <PERSON>, <PERSON><PERSON> and ef- fective curriculum pointer-generator networks for read- ing comprehension over long narratives. arXiv preprint arXiv:1905.10847, 2019.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Learning the curriculum with bayesian optimization for task-specific word representation learning", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Faru<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1605.03852"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, C. Learning the curriculum with bayesian optimiza- tion for task-specific word representation learning. arXiv preprint arXiv:1605.03852, 2016.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Graph attention networks", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1710.10903"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>gio, Y. Graph attention networks. arXiv preprint arXiv:1710.10903, 2017.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Glue: A multi-task benchmark and analysis platform for natural language understanding", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Hill", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": ["R"], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1804.07461"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. <PERSON>. Glue: A multi-task benchmark and anal- ysis platform for natural language understanding. arXiv preprint arXiv:1804.07461, 2018.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Optimizing data usage via differentiable rewards", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Neubig", "suffix": ""}], "year": 2020, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "9983--9995", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, G. Optimizing data usage via differentiable rewards. In International Conference on Machine Learning, pp. 9983-9995. PMLR, 2020.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "A survey on curriculum learning", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "IEEE Transactions on Pattern Analysis and Machine Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. A survey on curriculum learning. IEEE Transactions on Pattern Analysis and Machine Intelligence, 2021a.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Curriculum co-disentangled representation learning across multiple environments for social recommendation", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Pan", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Ge", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "36174--36192", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, W. Curriculum co-disentangled representation learning across multiple environments for social recommendation. In International Conference on Machine Learning, pp. 36174-36192. PMLR, 2023a.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Curgraph: Curriculum learning for graph classification", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Cai", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the Web Conference 2021", "volume": "", "issue": "", "pages": "1238--1248", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON>- graph: Curriculum learning for graph classification. In Proceedings of the Web Conference 2021, pp. 1238-1248, 2021b.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Exploring generalized curriculum learning for training visual backbones", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Song", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Efficienttrain", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "5852--5864", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. <PERSON>fficienttra<PERSON>: Exploring generalized curriculum learning for training visual backbones. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 5852-5864, 2023b.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Clnode: Curriculum learning for node classification", "authors": [{"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Hu", "suffix": ""}], "year": 2023, "venue": "Proceedings of the Sixteenth ACM International Conference on Web Search and Data Mining", "volume": "", "issue": "", "pages": "670--678", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Curriculum learning for node classification. In Proceedings of the Sixteenth ACM International Con- ference on Web Search and Data Mining, pp. 670-678, 2023.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Curriculum learning by transfer learning: Theory and experiments with deep networks", "authors": [{"first": "D", "middle": [], "last": "Weinshall", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "5238--5246", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, D. Curriculum learn- ing by transfer learning: Theory and experiments with deep networks. In International Conference on Machine Learning, pp. 5238-5246. PMLR, 2018.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Diff4rec: Sequential recommendation with curriculum-scheduled diffusion augmentation", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Li", "suffix": ""}, {"first": "Y", "middle": [], "last": "Han", "suffix": ""}, {"first": "L", "middle": [], "last": "Sun", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 31st ACM International Conference on Multimedia", "volume": "", "issue": "", "pages": "9329--9335", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>4rec: Sequential recommendation with curriculum-scheduled diffusion augmentation. In Pro- ceedings of the 31st ACM International Conference on Multimedia, pp. 9329-9335, 2023.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "How powerful are graph neural networks? arXiv preprint", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Hu", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Jegelka", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1810.00826"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. How powerful are graph neural networks? arXiv preprint arXiv:1810.00826, 2018.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Learning with multiclass auc: Theory and algorithms", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Bao", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "IEEE Transactions on Pattern Analysis and Machine Intelligence", "volume": "44", "issue": "11", "pages": "7747--7763", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>. Learning with multiclass auc: Theory and algorithms. IEEE Trans- actions on Pattern Analysis and Machine Intelligence, 44 (11):7747-7763, 2021.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Data-augmented curriculum graph neural architecture search under distribution shifts", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Qin", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "38", "issue": "", "pages": "16433--16441", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, H. Data-augmented curriculum graph neural architec- ture search under distribution shifts. Proceedings of the AAAI Conference on Artificial Intelligence, 38(15):16433- 16441, Mar. 2024.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "Wide residual networks", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1605.07146"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, S. and <PERSON>, N. Wide residual networks. arXiv preprint arXiv:1605.07146, 2016.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "Understanding deep learning requires rethinking generalization", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>yal<PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, O. Understanding deep learning requires rethinking generalization. ArXiv, abs/1611.03530, 2016. URL https://api.semanticscholar. org/CorpusID:6212000.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "Few-cost salient object detection with adversarial-paced learning", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Tian", "suffix": ""}, {"first": "J", "middle": [], "last": "Han", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "12236--12247", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>-cost salient object de- tection with adversarial-paced learning. Advances in Neu- ral Information Processing Systems, 33:12236-12247, 2020.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "Learning to solve travelling salesman problem with hardness-adaptive curriculum", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "36", "issue": "", "pages": "9136--9144", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. Learning to solve travelling salesman problem with hardness-adaptive curriculum. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 36, pp. 9136-9144, 2022.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "Reinforced curriculum learning on pre-trained neural machine translation models", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "34", "issue": "", "pages": "9652--9659", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>inforced curriculum learning on pre-trained neural machine trans- lation models. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 34, pp. 9652-9659, 2020.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "Minimax curriculum learning: Machine teaching with desirable difficulties and scheduled diversity", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> curriculum learning: Ma- chine teaching with desirable difficulties and scheduled diversity. In International Conference on Learning Rep- resentations, 2018.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "Curriculum learning by dynamic instance hardness", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "8602--8613", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, J. Curriculum learning by dynamic instance hardness. Advances in Neural Informa- tion Processing Systems, 33:8602-8613, 2020.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "Curml: A curriculum machine learning library", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "Pan", "suffix": ""}, {"first": "C", "middle": [], "last": "Yan", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the 30th ACM International Conference on Multimedia", "volume": "", "issue": "", "pages": "7359--7363", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: A curriculum machine learning library. In Proceedings of the 30th ACM International Conference on Multimedia, pp. 7359-7363, 2022a.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "Curriculum-nas: Curriculum weight-sharing neural architecture search", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Guan", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the 30th ACM International Conference on Multimedia", "volume": "", "issue": "", "pages": "3724--3735", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>-nas: Curriculum weight-sharing neural architecture search. In Proceedings of the 30th ACM International Conference on Multimedia, pp. 6792-6801, 2022b. <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, W. Intra- and inter-modal curriculum for multimodal learning. In Proceedings of the 31st ACM International Conference on Multimedia, pp. 3724-3735, 2023. CIFAR-10 CIFAR-100 Tiny-ImageNet Standard Noise-0.4 Imbalance-50 Standard Noise-0.4 Imbalance-50 Standard Noise-0.4 Imbalance-50", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "The performances of each curriculum learning method in the CV research domain", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "The performances of each curriculum learning method in the CV research domain. RTE MRPC STS-B CoLA Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 SPL 52..58 TTCL 52.78 0.14 53.79 1.81 81.54 0.18 81.22 0.00 14.11 2.21 11.10 2.25 12.44 2.22", "links": null}, "BIBREF75": {"ref_id": "b75", "title": "(a) LSTM RTE MRPC STS-B CoLA Standard Noise-0.4 Standard Noise", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Adaptive CL 82.74 0.75 64.22 2.23 50.54 0.00 50.70 0.23 74.85 0.45 60.05 1.30 61.39 0.34 41.43 1.69 60.65 0.45 42.10 1.82 (a) LSTM RTE MRPC STS-B CoLA Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 SPL 61.37 3.63 51.34 3.48 87.21 2.02 80.57 1.61 85.07 0.49 80.91 0.63 56.07 4.94 15.08 6.41 TTCL 66.35 1.76 56.32 5.04 88.63 1.88 81.79 0.57 84.91 0.68 80.74 1.66 57.26 0.87 45.79 1.64 MCL 66.35 2.02 55.09 2.22 88.69 1.24 78.94 2.59 85.42 0.22 79.21 0.65 56.24 2.37 30.20 5.94", "links": null}, "BIBREF76": {"ref_id": "b76", "title": "SST-2 QNLI QQP MNLI-(m) MNLI-(mm) Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 Standard Noise", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Adaptive CL 65.85 1.18 54.80 4.51 87.54 0.61 81.64 0.64 85.27 0.35 79.72 0.64 57.80 1.96 31.58 3.18 SST-2 QNLI QQP MNLI-(m) MNLI-(mm) Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 SPL 91.49 1.78 85.13 2.62 90.28 0.62 80.98 1.29 87.30 0.34 76.17 1.30 83.87 0.61 77.63 0.63 84.25 0.61 78.59 0.71 TTCL 92.48 0.41 91.25 0.59 91.37 0.16 89.45 0.44 87.45 0.46 84.50 0.25 83.99 0.31 81.73 0.31 84.34 0.45 82.25 0.40 MCL 92.41 0.20 84.33 0.91 91.24 0.23 80.71 1.08 88.16 0.13 74.19 1.02 83.86 0.42 76.85 0.79 84.11 0.29 77.92 0.79", "links": null}, "BIBREF77": {"ref_id": "b77", "title": "(c) GPT2 Table 10. The performances of each curriculum learning method in the NLP research domain", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Adaptive CL 92.11 0.24 85.78 1.42 87.79 0.18 78.14 1.66 85.72 0.21 75.72 0.57 81.38 0.11 76.04 0.41 82.38 0.34 77.44 0.37 (c) GPT2 Table 10. The performances of each curriculum learning method in the NLP research domain.", "links": null}}, "ref_entries": {"FIGREF0": {"uris": null, "num": null, "text": "Figure 1. CurBench includes 15 datasets spanning 3 research domains, 9 backbone models, 3 training settings, and 2 evaluation dimensions, providing a comprehensive benchmark for existing curriculum learning methods.", "fig_num": "1", "type_str": "figure"}, "FIGREF1": {"uris": null, "num": null, "text": "Figure 2. Our CurBench toolkit, which is composed of 5 modules, offers a unified and complete pipeline from initiation to evaluation, aiming for easy implementation and reproduction of curriculum learning methods. This figure showcases an example of noisy CIFAR-10.", "fig_num": "2", "type_str": "figure"}, "FIGREF2": {"uris": null, "num": null, "text": "Figure 3. Python-like sample code for an example of Self-Paced Learning applied to image classification with CurBench Toolkit.", "fig_num": "3", "type_str": "figure"}, "FIGREF3": {"uris": null, "num": null, "text": "Figure 4. The performances as a function of noise ratio p for different curriculum learning methods on datasets from 3 research domains.", "fig_num": "4", "type_str": "figure"}, "FIGREF4": {"uris": null, "num": null, "text": "Figure 5. The performances as a function of imbalance factor r.", "fig_num": "5", "type_str": "figure"}, "FIGREF5": {"uris": null, "num": null, "text": "Figure 6. Time and space complexity of different methods in the case of ResNet-18 and CIFAR-10. Note: The numerical results of 3 different cases are reported inTable 8 in the Appendix.", "fig_num": "6", "type_str": "figure"}, "TABREF0": {"html": null, "content": "<table><tr><td colspan=\"2\">Domain Dataset</td><td>Setting</td><td>Training</td><td>Validation</td><td colspan=\"2\">Test Class Metrics</td></tr><tr><td/><td>CIFAR-10</td><td>Standard / Noise-0.4 Imbalance-50</td><td>45,000 12,536</td><td colspan=\"2\">5,000 10,000 5,000 10,000</td><td>10 Accuracy 10 Accuracy</td></tr><tr><td>CV</td><td>CIFAR-100</td><td>Standard / Noise-0.4 Imbalance-50</td><td>45,000 12,536</td><td colspan=\"2\">5,000 10,000 5,000 10,000</td><td>100 Accuracy 100 Accuracy</td></tr><tr><td/><td>Tiny-ImageNet</td><td>Standard / Noise-0.4 Imbalance-50</td><td>90,000 22,700</td><td colspan=\"2\">10,000 10,000 10,000 10,000</td><td>200 Accuracy 200 Accuracy</td></tr><tr><td/><td>RTE</td><td>Standard / Noise-0.4</td><td>2,490</td><td>277</td><td>-</td><td>2 Accuracy</td></tr><tr><td/><td>MRPC</td><td>Standard / Noise-0.4</td><td>3,668</td><td>408</td><td>-</td><td>2 F1 Score</td></tr><tr><td/><td>STS-B</td><td>Standard / Noise-0.4</td><td>5,749</td><td>1,500</td><td>-</td><td>6 Spearman</td></tr><tr><td>NLP</td><td>CoLA SST-2</td><td>Standard / Noise-0.4 Standard / Noise-0.4</td><td>8,551 67,349</td><td>1,043 872</td><td>--</td><td>2 Matthews 2 Accuracy</td></tr><tr><td/><td>QNLI</td><td>Standard / Noise-0.4</td><td>104,743</td><td>5,463</td><td>-</td><td>2 Accuracy</td></tr><tr><td/><td>QQP</td><td>Standard / Noise-0.4</td><td>363,846</td><td>40,430</td><td>-</td><td>2 F1 Score</td></tr><tr><td/><td colspan=\"2\">MNLI-(m/mm) Standard / Noise-0.4</td><td colspan=\"2\">392,702 9,815/9,832</td><td>-</td><td>3 Accuracy</td></tr><tr><td/><td>MUTAG</td><td>Standard / Noise-0.4</td><td>150</td><td>19</td><td>19</td><td>2 Accuracy</td></tr><tr><td/><td>PROTEINS</td><td>Standard / Noise-0.4</td><td>890</td><td>111</td><td>112</td><td>2 Accuracy</td></tr><tr><td>Graph</td><td>NCI1</td><td>Standard / Noise-0.4</td><td>3,288</td><td>411</td><td>411</td><td>2 Accuracy</td></tr><tr><td/><td>ogbg-molhiv</td><td>Standard / Noise-0.4</td><td>32,901</td><td>4,113</td><td>4,113</td><td>2 ROC-AUC</td></tr></table>", "text": "", "num": null, "type_str": "table"}, "TABREF1": {"html": null, "content": "<table><tr><td>NLP Domain: LSTM (Hochreiter &amp; Schm<PERSON>, 1997) is</td></tr><tr><td>a typical recurrent neural network (RNN), which introduces</td></tr><tr><td>gate functions to control what to remember and what to</td></tr><tr><td>forget in the face of long sequences. BERT (<PERSON> et al.,</td></tr><tr><td>2018) is a deep bidirectional Transformer pretrained by</td></tr><tr><td>masked language model task and it excels at semantic repre-</td></tr><tr><td>sentation due to its encoder-based architecture. GPT2 (Rad-</td></tr><tr><td>ford et al., 2019) is a decoder-based Transformer pretrained</td></tr><tr><td>through left-to-right language modeling objectives, and as a</td></tr><tr><td>result, works well on text generation. BERT and GPT2 in</td></tr><tr><td>CurBench are pretrained because training them from scratch</td></tr><tr><td>would result in poor performance, making it difficult to</td></tr><tr><td>maintain consistency with their suggested performance.</td></tr></table>", "text": "Table2overviews the backbone models that we employ in CurBench. All the values in the last column are approximations because the number of parameters varies depending on the input sizes and output classes. All of the models are commonly applied to the aforementioned datasets, and they are distinct from each other in mechanism and model size. first-order approximation of spectral graph convolutions. GAT(<PERSON><PERSON><PERSON> et al., 2017) introduces masked self-attentional layers based on GCN to enable implicitly specifying different weights to different nodes in a neighborhood. GIN", "num": null, "type_str": "table"}, "TABREF4": {"html": null, "content": "<table><tr><td/><td/><td>CIFAR-10</td><td/><td/><td colspan=\"2\">CIFAR-100</td><td>Tiny-ImageNet</td></tr><tr><td/><td colspan=\"7\">Standard Noise-0.4 Imbalance-50 Standard Noise-0.4 Imbalance-50 Standard Noise-0.4 Imbalance-50</td></tr><tr><td>LeNet</td><td colspan=\"2\">69.95 1.00 65.02 1.12</td><td colspan=\"2\">44.93 0.56</td><td colspan=\"2\">35.46 0.70 29.59 0.40</td><td>19.57 0.64</td><td>22.08 0.61 18.63 0.43</td><td>11.65 0.30</td></tr><tr><td>LeNet + CL</td><td colspan=\"2\">70.43 0.41 65.93 0.57</td><td colspan=\"2\">45.28 0.56</td><td colspan=\"2\">35.63 0.78 30.87 0.48</td><td>19.74 0.17</td><td>22.83 0.44 19.91 0.26</td><td>12.36 0.47</td></tr><tr><td>ResNet-18</td><td colspan=\"2\">92.33 0.16 82.75 2.06</td><td colspan=\"2\">75.49 0.87</td><td colspan=\"2\">69.97 0.27 52.14 0.39</td><td>42.57 0.68</td><td>51.41 1.74 39.42 0.21</td><td>28.83 0.38</td></tr><tr><td colspan=\"3\">ResNet-18 + CL 92.88 0.23 86.92 0.20</td><td colspan=\"2\">76.43 0.96</td><td colspan=\"2\">71.31 0.14 58.56 0.60</td><td>43.47 0.43</td><td>53.61 0.48 43.64 0.72</td><td>30.82 0.36</td></tr><tr><td>ViT</td><td colspan=\"2\">79.90 0.38 64.19 0.51</td><td colspan=\"2\">52.12 0.81</td><td colspan=\"2\">51.05 0.62 35.25 0.24</td><td>26.05 0.52</td><td>38.16 0.53 24.90 0.26</td><td>17.15 0.31</td></tr><tr><td>ViT + CL</td><td colspan=\"2\">80.66 0.27 69.83 0.53</td><td colspan=\"2\">52.85 0.81</td><td colspan=\"2\">51.93 0.64 39.15 0.30</td><td>26.40 0.34</td><td>38.92 0.53 29.76 0.34</td><td>17.47 0.14</td></tr><tr><td/><td/><td>RTE</td><td/><td/><td>MRPC</td><td/><td>STS-B</td><td>CoLA</td></tr><tr><td/><td/><td colspan=\"6\">Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4</td></tr><tr><td colspan=\"2\">LSTM</td><td colspan=\"2\">52.95 1.34 53.43 1.77</td><td colspan=\"2\">81.43 0.14 81.22 0.00</td><td colspan=\"2\">12.73 0.72 10.90 1.19</td><td>11.29 1.27</td><td>3.27 1.68</td></tr><tr><td colspan=\"4\">LSTM + CL 53.07 1.29 54.22 1.77</td><td colspan=\"2\">81.54 0.18 81.24 0.05</td><td colspan=\"2\">14.11 2.21 11.75 1.61</td><td>12.65 1.21</td><td>8.55 2.10</td></tr><tr><td colspan=\"2\">BERT</td><td colspan=\"2\">64.62 3.33 54.22 3.14</td><td colspan=\"2\">88.54 0.45 81.89 0.83</td><td colspan=\"2\">85.26 0.22 80.71 1.01</td><td>57.39 1.30 32.35 0.79</td></tr><tr><td colspan=\"4\">BERT + CL 66.35 1.76 56.32 5.04</td><td colspan=\"2\">88.69 1.24 81.94 0.55</td><td colspan=\"2\">85.42 0.22 81.31 0.25</td><td>57.80 1.96 45.79 1.64</td></tr><tr><td colspan=\"2\">GPT2</td><td colspan=\"2\">65.34 1.95 52.92 4.49</td><td colspan=\"2\">85.49 0.86 78.23 1.72</td><td colspan=\"2\">76.44 1.20 69.65 1.85</td><td>37.00 3.72</td><td>5.86 1.69</td></tr><tr><td colspan=\"2\">GPT2 + CL</td><td colspan=\"2\">66.35 2.10 57.40 3.39</td><td colspan=\"2\">86.29 0.36 82.55 0.88</td><td colspan=\"2\">80.82 1.39 71.57 1.74</td><td>39.95 3.16 12.54 2.75</td></tr><tr><td/><td/><td>SST-2</td><td/><td>QNLI</td><td/><td>QQP</td><td>MNLI-(m/mm)</td></tr><tr><td/><td colspan=\"7\">Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4</td><td>Standard</td><td>Noise-0.4</td></tr><tr><td>LSTM</td><td colspan=\"5\">81.67 0.85 64.36 1.12 75.69 0MUTAG 50.54 0.00 50.62 0.16 PROTEINS</td><td/><td>NCI1</td><td>ogbg-molhiv</td></tr><tr><td/><td/><td colspan=\"6\">Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4</td></tr><tr><td colspan=\"2\">GCN</td><td colspan=\"2\">73.68 2.11 66.31 7.14</td><td colspan=\"2\">70.71 4.20 63.57 6.45</td><td colspan=\"2\">69.59 1.23 55.23 3.21</td><td>75.84 1.02 64.29 4.55</td></tr><tr><td colspan=\"4\">GCN + CL 74.74 3.94 71.58 5.37</td><td colspan=\"2\">73.21 4.41 71.61 6.62</td><td colspan=\"2\">71.39 1.29 67.98 2.01</td><td>77.41 1.15 72.81 1.14</td></tr><tr><td colspan=\"2\">GAT</td><td colspan=\"2\">69.47 6.14 65.26 5.37</td><td colspan=\"2\">64.46 2.96 65.71 9.13</td><td colspan=\"2\">56.74 2.86 53.77 2.12</td><td>68.07 2.34 65.37 2.66</td></tr><tr><td colspan=\"6\">GAT + CL 72.63 8.42 69.47 10.21 69.82 7.13 69.11 3.77</td><td colspan=\"2\">59.37 1.59 55.67 4.70</td><td>72.64 1.16 66.73 1.84</td></tr><tr><td>GIN</td><td/><td colspan=\"2\">86.84 7.90 78.95 3.72</td><td colspan=\"2\">74.11 4.24 69.82 1.73</td><td colspan=\"2\">79.32 1.40 60.24 3.92</td><td>74.72 1.36 63.07 3.73</td></tr><tr><td colspan=\"2\">GIN + CL</td><td colspan=\"2\">88.42 2.10 81.58 4.56</td><td colspan=\"2\">77.14 4.88 73.93 1.82</td><td colspan=\"2\">82.04 1.90 62.14 6.47</td><td>76.53 1.97 65.53 1.61</td></tr></table>", "text": ".27 60.72 0.79 61.38 0.30 / 61.21 0.45 44.41 0.51 / 44.83 0.90 LSTM + CL 82.87 0.88 78.58 1.64 51.02 0.46 50.83 0.45 75.73 0.21 66.47 0.72 62.47 0.36 / 62.33 0.42 58.59 0.54 / 58.50 0.64 BERT 92.66 0.28 87.22 0.82 91.21 0.24 81.21 0.76 88.05 0.12 76.23 0.48 83.89 0.31 / 84.38 0.29 78.65 0.70 / 79.21 0.62 BERT + CL 92.82 0.16 91.25 0.59 91.49 0.13 89.45 0.44 88.16 0.13 84.50 0.25 84.27 0.07 / 84.40 0.42 81.73 0.31 / 82.25 0.40 GPT2 91.95 0.49 85.83 0.57 87.92 0.31 78.72 0.37 86.00 0.23 75.40 0.84 81.53 0.21 / 82.40 0.21 76.56 0.15 / 77.69 0.15 GPT2 + <PERSON>L 92.25 0.42 90.34 0.53 88.17 0.67 84.00 0.70 86.68 0.16 82.16 0.35 81.90 0.23 / 82.59 0.35 78.36 0.19 / 79.62 0.44", "num": null, "type_str": "table"}, "TABREF5": {"html": null, "content": "<table><tr><td/><td>Home Page</td><td>Download Link</td><td>License</td></tr><tr><td>CV</td><td colspan=\"2\">CIFAR Tiny-ImageNet CS231n PyTorch</td><td>MIT MIT</td></tr><tr><td>NLP</td><td>GLUE</td><td>Hugging Face</td><td>Various</td></tr><tr><td>Graph</td><td>TUDataset OGB</td><td colspan=\"2\">PyTorch Geometric Various OGB Dataset MIT</td></tr><tr><td colspan=\"4\">Concretely, in this work, we download CIFAR via PyTorch</td></tr><tr><td colspan=\"4\">API, GLUE via Hugging Face API, TUDataset via PyTorch</td></tr><tr><td colspan=\"4\">Geometric (PyG) API, and OGB dataset via OGB API. For</td></tr><tr><td colspan=\"4\">Tiny-ImageNet, we download the zip file from CS231n,</td></tr><tr><td colspan=\"4\">and adjust its file structure to the same form as CIFAR for</td></tr><tr><td colspan=\"4\">easier loading with the help of the tool code from Github:</td></tr><tr><td colspan=\"2\">lromor/tinyimagenet.py.</td><td/><td/></tr></table>", "text": "The home pages, download links, and licenses of datasets.", "num": null, "type_str": "table"}, "TABREF7": {"html": null, "content": "<table/>", "text": "The implementation references of backbone models.", "num": null, "type_str": "table"}, "TABREF8": {"html": null, "content": "<table><tr><td/><td colspan=\"2\">Conference Datasets</td><td>Models</td><td colspan=\"3\">Settings Std Noi Imb</td></tr><tr><td>SPL (<PERSON> et al., 2010)</td><td>NIPS, 2010</td><td>MUC6, UniProbe, MNIST, Mammals</td><td>SSVM</td><td>✓</td><td/><td/></tr><tr><td>TTCL (Weins<PERSON> et al., 2018)</td><td colspan=\"2\">ICML, 2018 CIFAR-100, STL-10</td><td>CNN</td><td>✓</td><td/><td/></tr><tr><td>MCL (Zhou &amp; Bilmes, 2018)</td><td>ICLR, 2018</td><td>News-20, MNIST, SVHN, Fashion CIFAR-10, STL-10,</td><td>Le<PERSON><PERSON>, CNN</td><td>✓</td><td/><td/></tr><tr><td>ScreenerNet (Kim &amp; Choi, 2018)</td><td>ArXiv, 2018</td><td>Cart-pole-v0, Pascal VOC CIFAR-10, MNIST,</td><td>DDQN, CNN</td><td>✓</td><td/><td/></tr><tr><td>LRE (Ren et al., 2018a)</td><td>ICML, 2018</td><td>MNIST, CIFAR-10, CIFAR-100</td><td>LeNet, ResNet-32, WideResNet-28-10</td><td/><td>✓</td><td>✓</td></tr><tr><td>MW-Net (Shu et al., 2019)</td><td>NIPS, 2019</td><td>CIFAR-10, CIFAR-100, Clothing1M</td><td>ResNet-32, ResNet-50, WideResNet-28-10</td><td>✓</td><td>✓</td><td>✓</td></tr><tr><td>DCL (Saxena et al., 2019)</td><td>NIPS, 2019</td><td>CIFAR-10, CIFAR-100, KITTI ImageNet, WebVision,</td><td>VGG-16, SSDNet, WideResNet-28-10 ResNet-18,</td><td>✓</td><td>✓</td><td/></tr><tr><td>LGL (Cheng et al., 2019)</td><td>CVPR, 2019</td><td>CIFAR-10, CIFAR-100, ImageNet</td><td>VGG-16, ResNet-50</td><td>✓</td><td/><td/></tr><tr><td>DDS (Wang et al., 2020)</td><td>ICML, 2020</td><td>CIFAR-10, ImageNet, TED</td><td>LSTM, ResNet-50, WideResNet-28-10</td><td>✓</td><td/><td>✓</td></tr><tr><td/><td/><td>CIFAR-10, CIFAR-100,</td><td/><td/><td/><td/></tr><tr><td/><td/><td>ImageNet, Food-101,</td><td>ResNet-50,</td><td/><td/><td/></tr><tr><td>DIHCL (Zhou et al., 2020)</td><td>NIPS, 2020</td><td>FGVC Aircraft, Birdsnap, FMNIST, Stanford Cars,</td><td>WideResNet-16-8, ResNeXt50-32x4d, WideResNet-28-10,</td><td>✓</td><td/><td/></tr><tr><td/><td/><td>KMNIST, STL10,</td><td>PreActResNet34</td><td/><td/><td/></tr><tr><td/><td/><td>SVHN</td><td/><td/><td/><td/></tr><tr><td>SuperLoss (Castells et al., 2020)</td><td>NIPS, 2020</td><td>MNIST, UTKFace, CIFAR-10, CIFAR-100, Revisited Oxford and Paris WebVision, Pascal VOC,</td><td>ResNet-18, ResNet-50, ResNet-101, RetinaNet Faster R-CNN, WideResNet-28-10,</td><td>✓</td><td>✓</td><td/></tr><tr><td/><td/><td>CIFAR-10, CIFAR-100,</td><td>VGG-16, ResNet-18,</td><td/><td/><td/></tr><tr><td>CBS (Sinha et al., 2020)</td><td>NIPS, 2020</td><td>ImageNet, SVHN, CelebA, Pascal VOC,</td><td>Wide-ResNet-50, ResNeXt-50,</td><td>✓</td><td/><td/></tr><tr><td/><td/><td>MNIST, USPS</td><td>VAE, β-VAE</td><td/><td/><td/></tr><tr><td>C2F (Stretcu et al., 2021)</td><td>ArXiv, 2021</td><td>CIFAR-10, CIFAR-100, Shapes, Tiny-ImageNet</td><td>Resnet-18, Resnet-50, WideResnet-28-10</td><td>✓</td><td/><td/></tr><tr><td>Adaptive CL (Kong et al., 2021)</td><td>ICCV, 2021</td><td>CIFAR-10, CIFAR-100, Subset of ImageNet</td><td>MLP, HNN, ResNet-v1-14 VGG-16, ResNet-18</td><td>✓</td><td/><td/></tr><tr><td>EfficientTrain (Wang et al., 2023b)</td><td>ICCV, 2023</td><td>ImageNet-1K/22K, CIFAR, Stanford Dogs MS COCO, Flowers-102,</td><td>ResNet, ConvNeXt, Swin, CSWin DeiT, PVT,</td><td>✓</td><td/><td/></tr></table>", "text": "Summary of the methods reproduced in CurBench, where we overview the datasets and models involved in the related works.", "num": null, "type_str": "table"}, "TABREF9": {"html": null, "content": "<table/>", "text": "The default hyperparameters we set for each method when the number of training epochs is 200.", "num": null, "type_str": "table"}, "TABREF11": {"html": null, "content": "<table><tr><td>MUTAG</td><td>PROTEINS</td><td>NCI1</td><td>ogbg-molhiv</td></tr><tr><td colspan=\"2\">Standard Noise-0.4 (a) GCN</td><td/><td/></tr><tr><td>MUTAG</td><td>PROTEINS</td><td>NCI1</td><td>ogbg-molhiv</td></tr><tr><td colspan=\"2\">Standard Noise-0.4 (b) GAT</td><td/><td/></tr><tr><td>MUTAG</td><td>PROTEINS</td><td>NCI1</td><td>ogbg-molhiv</td></tr><tr><td colspan=\"2\">Standard Noise-0.4 (c) GIN</td><td/><td/></tr></table>", "text": "Time and space complexity, measured by training time and GPU memory usage on NVIDIA V100 GPU. Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 SPL 71.58 7.14 62.10 3.94 69.46 5.91 65.54 6.48 68.42 1.90 60.05 2.38 77.41 1.15 60.87 3.09 TTCL 70.52 7.14 71.58 5.37 72.68 7.63 71.61 6.62 70.90 2.21 67.98 2.01 75.89 0.81 72.81 1.14 MCL 71.58 7.14 71.58 8.55 70.54 5.15 65.00 4.71 68.56 1.04 54.50 2.85 74.10 1.40 64.26 4.17 ScreenerNet 72.63 3.94 64.21 5.16 71.96 5.61 67.14 4.05 69.78 2.22 56.06 5.14 73.71 0.45 61.00 7.79 LRE 70.52 9.18 61.40 4.96 68.03 6.17 66.61 5.25 58.23 1.60 51.22 2.38 73.74 1.48 57.92 7.98 MW-Net 74.73 2.11 63.16 4.71 70.54 4.55 66.79 4.13 68.71 1.78 56.01 1.37 75.57 1.03 62.81 6.19 <PERSON><PERSON> 74.73 2.11 61.05 13.56 71.96 3.46 63.57 6.32 70.51 0.66 56.69 1.58 75.78 1.39 61.26 3.57 DDS 74.74 3.94 64.21 5.16 73.21 4.41 64.11 6.50 71.39 1.29 58.10 3.28 70.48 3.02 57.09 4.80 DIHCL 71.58 5.37 68.42 7.44 73.03 3.59 63.22 7.02 67.40 1.71 57.86 2.04 70.47 2.10 61.20 4.67 SuperLoss 71.58 5.37 69.47 6.14 72.32 3.44 65.89 3.84 70.22 2.00 57.17 3.38 75.97 1.03 61.21 5.12 Adaptive CL 73.68 3.33 66.31 7.88 72.68 4.84 65.71 4.51 69.88 2.17 58.44 5.29 75.49 1.13 60.95 7.96 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 SPL 64.21 11.72 65.26 5.37 69.29 6.93 67.14 3.85 56.49 2.61 54.74 4.10 69.69 2.38 64.88 2.73 TTCL 69.47 6.98 65.26 10.84 69.82 7.13 64.46 1.31 56.79 1.40 55.47 2.73 68.27 2.04 66.73 1.84 MCL 64.21 11.24 68.42 8.81 69.64 6.29 66.96 6.89 57.56 2.63 55.23 4.73 69.25 3.06 63.20 3.03 ScreenerNet 64.21 8.42 65.26 7.88 65.71 5.25 69.11 3.77 54.55 2.64 55.28 1.53 71.13 2.07 65.94 2.61 LRE 66.31 11.34 63.16 4.71 66.43 1.84 66.07 3.19 54.11 2.32 52.94 2.36 66.59 2.45 63.74 2.61 MW-Net 61.84 9.40 65.26 7.14 66.78 3.01 67.14 4.42 57.56 2.29 55.33 1.18 68.54 3.76 62.39 2.60 DCL 67.37 14.28 69.47 10.21 68.03 7.39 64.28 3.84 59.37 1.59 55.33 1.72 72.64 1.16 62.22 3.98 DDS 66.31 7.14 67.37 6.14 67.14 3.31 66.78 9.69 53.24 1.81 54.45 3.35 68.50 2.05 62.22 5.88 DIHCL 72.63 8.42 66.32 8.55 65.00 6.81 68.57 6.93 57.18 1.73 55.67 4.70 69.07 2.79 66.38 2.78 SuperLoss 67.37 13.06 68.42 7.44 63.93 2.63 66.07 7.23 57.08 2.27 55.13 2.39 70.58 1.52 60.92 2.13 Adaptive CL 67.37 10.21 66.32 7.14 68.39 3.07 64.47 6.37 57.61 2.22 55.08 2.02 69.71 1.84 62.98 2.53 Standard Noise-0.4 Standard Noise-0.4 Standard Noise-0.4 SPL 82.10 5.37 72.37 6.84 72.86 5.13 72.86 2.37 77.54 1.69 56.87 4.93 76.53 1.97 63.35 2.34 TTCL 84.21 7.44 81.58 4.56 75.71 2.36 73.93 1.82 80.24 1.67 56.27 4.27 75.13 1.55 62.16 3.07 MCL 84.21 6.45 73.69 5.27 75.72 3.93 70.00 2.68 75.67 1.00 57.73 4.11 74.20 0.48 63.82 3.85 ScreenerNet 82.10 7.14 75.00 5.74 75.71 1.82 68.39 4.88 79.61 1.09 55.57 5.11 74.39 1.24 61.07 2.33 LRE 78.95 3.72 80.27 2.28 72.68 5.46 66.43 6.48 71.41 1.71 54.08 1.72 73.49 2.36 63.30 4.44 MW-Net 88.42 2.10 75.00 4.37 73.75 4.10 66.61 8.54 79.22 1.21 55.52 4.78 75.22 0.80 65.43 2.70 DCL 85.26 8.42 76.32 4.56 74.11 3.14 64.46 4.39 79.66 1.39 56.06 3.79 75.23 2.22 61.65 3.38 DDS 85.26 3.94 80.26 5.73 70.31 2.78 65.89 6.69 77.62 3.58 54.89 4.85 72.85 2.67 63.38 3.91 DIHCL 85.53 4.36 73.68 3.72 73.75 4.54 71.61 4.28 76.55 1.70 53.33 1.28 72.43 1.80 62.23 5.86 SuperLoss 88.42 5.16 77.63 4.37 77.14 4.88 71.25 5.69 82.04 1.90 62.14 6.47 74.51 1.47 65.53 1.61 Adaptive CL 86.31 4.21 80.26 9.40 75.89 3.79 70.36 3.97 79.32 1.90 62.05 1.67 76.17 1.46 61.81 4.81", "num": null, "type_str": "table"}, "TABREF12": {"html": null, "content": "<table/>", "text": "The performances of each curriculum learning method in the graph research domain.", "num": null, "type_str": "table"}}}}