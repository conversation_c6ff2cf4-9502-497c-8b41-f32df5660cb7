{"paper_id": "ParamRepulsor", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:15:48.794548Z"}, "title": "Navigating the Effect of Parametrization for Dimensionality Reduction", "authors": [{"first": "Haiyang", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Duke University", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Duke University", "location": {}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Duke University", "location": {}}, "email": "<EMAIL>"}], "year": "", "venue": null, "identifiers": {}, "abstract": "Parametric dimensionality reduction methods have gained prominence for their ability to generalize to unseen datasets, an advantage that traditional approaches typically lack. Despite their growing popularity, there remains a prevalent misconception among practitioners about the equivalence in performance between parametric and non-parametric methods. Here, we show that these methods are not equivalent -parametric methods retain global structure but lose significant local details. To explain this, we provide evidence that parameterized approaches lack the ability to repulse negative pairs, and the choice of loss function also has an impact. Addressing these issues, we developed a new parametric method, ParamRepulsor, that incorporates Hard Negative Mining and a loss function that applies a strong repulsive force. This new method achieves state-of-the-art performance on local structure preservation for parametric methods without sacrificing the fidelity of global structural representation. Our code is available at https://github.com/hyhuang00/ParamRepulsor. * Equal contribution.\n38th Conference on Neural Information Processing Systems (NeurIPS 2024).\nWe provide essential background on Neighborhood Embedding (NE) methods and notation. We notate the high dimensional data as X = x 1 . . . x n ∈ R D , where n is the number of data points, and D is the dimension. NE algorithms aim to preserve predefined high-dimensional similarities within a low-dimensional embedding to reveal the local and global structure of X. Specifically,", "pdf_parse": {"paper_id": "ParamRepulsor", "_pdf_hash": "", "abstract": [{"text": "Parametric dimensionality reduction methods have gained prominence for their ability to generalize to unseen datasets, an advantage that traditional approaches typically lack. Despite their growing popularity, there remains a prevalent misconception among practitioners about the equivalence in performance between parametric and non-parametric methods. Here, we show that these methods are not equivalent -parametric methods retain global structure but lose significant local details. To explain this, we provide evidence that parameterized approaches lack the ability to repulse negative pairs, and the choice of loss function also has an impact. Addressing these issues, we developed a new parametric method, ParamRepulsor, that incorporates Hard Negative Mining and a loss function that applies a strong repulsive force. This new method achieves state-of-the-art performance on local structure preservation for parametric methods without sacrificing the fidelity of global structural representation. Our code is available at https://github.com/hyhuang00/ParamRepulsor. * Equal contribution.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "38th Conference on Neural Information Processing Systems (NeurIPS 2024).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "We provide essential background on Neighborhood Embedding (NE) methods and notation. We notate the high dimensional data as X = x 1 . . . x n ∈ R D , where n is the number of data points, and D is the dimension. NE algorithms aim to preserve predefined high-dimensional similarities within a low-dimensional embedding to reveal the local and global structure of X. Specifically,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Dimension reduction (DR) methods are incredibly useful for data analysis. They provide a bird's eye view of a dataset that shows clusters and their relationships. These algorithms have been used for examining and processing images [1] , text documents [2, 3] , and biological datasets [4] [5] [6] [7] [8] . The successes of modern DR methods can mostly be attributed to neighborhood embedding (NE), which is the basis for modern DR methods [9] including t-SNE, LargeVis, UMAP, and PaCMAP [10] [11] [12] [13] . These algorithms aim to optimize the low-dimensional layout of the data, such that the high dimensional local structure (i.e., neighborhoods) are preserved.", "cite_spans": [{"start": 231, "end": 234, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 252, "end": 255, "text": "[2,", "ref_id": "BIBREF1"}, {"start": 256, "end": 258, "text": "3]", "ref_id": "BIBREF2"}, {"start": 285, "end": 288, "text": "[4]", "ref_id": "BIBREF3"}, {"start": 289, "end": 292, "text": "[5]", "ref_id": "BIBREF4"}, {"start": 293, "end": 296, "text": "[6]", "ref_id": "BIBREF5"}, {"start": 297, "end": 300, "text": "[7]", "ref_id": "BIBREF6"}, {"start": 301, "end": 304, "text": "[8]", "ref_id": "BIBREF7"}, {"start": 440, "end": 443, "text": "[9]", "ref_id": "BIBREF8"}, {"start": 488, "end": 492, "text": "[10]", "ref_id": "BIBREF9"}, {"start": 493, "end": 497, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 498, "end": 502, "text": "[12]", "ref_id": "BIBREF11"}, {"start": 503, "end": 507, "text": "[13]", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "A major weakness of existing NE algorithms is that they struggle with adaptability to large, incrementally updated datasets. These algorithms depend on a K-Nearest Neighbor graph, encompassing the entire dataset, to generate the embedding. Consequently, the introduction of new data necessitates a complete re-computation of the embedding, leading to significant time and computational resource demands for large datasets. Although recent adaptations have been developed to optimize only the additional data [14, 13] , these modifications potentially alter the original algorithm's objective function, thereby compromising the embedding's quality.", "cite_spans": [{"start": 508, "end": 512, "text": "[14,", "ref_id": "BIBREF13"}, {"start": 513, "end": 516, "text": "13]", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Addressing these challenges, recent developments in combining neural networks with NE algorithms have shown promise. These algorithms maintain the same objectives as traditional NE methods but leverage neural networks to optimize the projection of high-dimensional data into lower-dimensional spaces [16] [17] [18] . The integration of neural networks allows these NE algorithms to be effectively trained on large datasets and generalize to unseen data. Throughout this paper, we refer to this class of algorithms as parametric algorithms. However, as shown in Fig. 1 , despite the similarity in loss Figure 1 : Dimensionality reduction results on the MNIST digit dataset [15] . Parametric methods (bottom row) fail to preserve the local structure of the dataset compared to their non-parametric counterparts (top row). Our method, ParamRepulsor, effectively resolves this problem via Hard Negative Mining. functions between the non-parametric and parametric versions, their outcomes are often completely different, and such difference has been largely overlooked by machine learning practitioners. This paper aims to illuminate and explain these differences, highlighting that parametrization often leads to worse local structure and visualization. Our investigation reveals that parameterized approaches lack the ability to identify cluster boundaries and separate negatives compared to nonparametric approaches. We further show that DR algorithms using Negative Sampling (NEG)-style loss functions exhibit greater adaptability to parametrization than others using Noise Contrastive Estimation (NCE) or InfoNCE loss. This observation is noteworthy as such discrepancies are not observed in nonparametric approaches.", "cite_spans": [{"start": 300, "end": 304, "text": "[16]", "ref_id": "BIBREF15"}, {"start": 305, "end": 309, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 310, "end": 314, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 672, "end": 676, "text": "[15]", "ref_id": "BIBREF14"}], "ref_spans": [{"start": 566, "end": 567, "text": "1", "ref_id": null}, {"start": 608, "end": 609, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Building on these insights, we propose a novel parametric DR method that effectively mines hard negatives without relying on labels. Our approach incorporates additional repulsive forces, placing even greater emphasis on pairs we identify as hard negatives. This enhancement ensures better separation and structure preservation, significantly improving the performance of parametric DR. We select a loss function tailored for optimizing the parametric case, addressing local structure preservation. Our new DR algorithm, ParamRepulsor, approaches the performance of leading nonparametric methods while surpassing existing parametric approaches in preserving both local and global structure. It offers a functional mapping from high-to low-dimensional space, ensuring superior scalability, adaptability, and generalization to unseen data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "To summarize, our contributions in this study are:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• We conduct a comprehensive analysis of the impact of parametrization on the performance of DR methods, demonstrating that it may compromise local structure. Our findings attribute this issue to insufficient repulsive forces on negative pairs in the parametric setting. Notably, algorithms employing NEG-style loss functions (e.g., UMAP, PaCMAP) exhibit greater adaptability to parametrization than those using NCE-style loss functions (e.g., InfoNC-t-SNE, NCVis). • Inspired by contrastive learning, we propose ParamRepulsor, a new method that uses hard negative sampling to improve the handling of negative pairs, combined with a contrastive loss tailored for the parametric setting. ParamRepulsor is a novel, fast algorithm that achieves excellent local structure preservation while maintaining global structure.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "NE methods identify a mapping function f θ that constructs the corresponding low dimensional embedding Y = y 1 . . . y n ∈ R d , where y i = f θ (x i ). We will use y i and f θ (x i ) interchangably. For nonparametric DR methods, the function f θ is not defined outside of x 1 , . . . , x n , though it is possible to interpolate. For visualization purposes, d is usually set to 2 or 3. Since the introduction of t-SNE [10] , these algorithms have become widely used due to their ability to identify clusters and manifolds within high-dimensional data. They typically have two stages:", "cite_spans": [{"start": 419, "end": 423, "text": "[10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Similarity Construction Phase. For all pairs of points (i, j), their high-dimensional similarity, s ij , is captured by a similarity function Φ(x i , x j ) related to their distance. Due to the curse of dimensionality, the Euclidean distance metric fails to accurately represent distances along the data manifold in high-dimensional spaces [19] . A common solution to this issue is to only consider similarities between K nearest neighbors: s ij is set to be non-zero iff x i or x j are within the K nearest neighbors of each other, where K is a hyperparameter, usually 15-30.", "cite_spans": [{"start": 340, "end": 344, "text": "[19]", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Embedding Optimization Phase. After constructing the graph, NE algorithms try to optimize a function f θ . The objective is encoded by a loss function L(θ):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L(θ) = E ij∈N N L N N (∥f θ (x i ) -f θ (x j )∥ 2 ) + E ik / ∈N N L F P (∥f θ (x i ) -f θ (x k )∥ 2 ),", "eq_num": "(1)"}], "section": "Introduction", "sec_num": "1"}, {"text": "where L N N denotes the loss for i, j that are similar (among the K-nearest neighbors), and L F P denotes the loss for pairs that are not nearest neighbors in the high-dimensional space. Typically, L N N decreases when ∥y i -y j ∥ 2 decreases, and L F P decreases when ∥y i -y k ∥ 2 increases. Their gradients therefore act like forces that attract or repulse the N N or F P pairs, respectively.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Relationship to Contrastive Learning. The similarity of the decomposition to self-supervised contrastive learning has recently been noted by [20, 18] . Specifically, loss functions of major NE algorithms can be considered as cases of Noise Contrastive Estimation (NCE) [21] , Info-Noise Contrastive Estimation (InfoNCE) [22] or Negative Sampling (NEG) [23] .", "cite_spans": [{"start": 141, "end": 145, "text": "[20,", "ref_id": "BIBREF19"}, {"start": 146, "end": 149, "text": "18]", "ref_id": "BIBREF17"}, {"start": 269, "end": 273, "text": "[21]", "ref_id": "BIBREF20"}, {"start": 320, "end": 324, "text": "[22]", "ref_id": "BIBREF21"}, {"start": 352, "end": 356, "text": "[23]", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Using the framework above, we dive into details of t-SNE [10] , NCVis [24] , UMAP [12] and PaCMAP [13] , which are four major recent NE algorithms.", "cite_spans": [{"start": 57, "end": 61, "text": "[10]", "ref_id": "BIBREF9"}, {"start": 70, "end": 74, "text": "[24]", "ref_id": "BIBREF23"}, {"start": 82, "end": 86, "text": "[12]", "ref_id": "BIBREF11"}, {"start": 98, "end": 102, "text": "[13]", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Both NCE-and InfoNCE-based approaches assume the high-dimensional data similarities (the s ij 's) follow an underlying data similarity pattern, represented by an unknown distribution p. These methods learn a function f θ that generates a similar low-dimensional similarity pattern, described by a distribution q, aiming to match p. q decreases as the pairwise distances in the low-dimensional space increase, though their exact relationship can vary. Since q represents a probability distribution, it must be normalized to ensure all possibilities sum up to 1. The only difference is that NCE uses a logistic loss, whereas InfoNCE uses a cross-entropy loss for the data distribution match.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "Approximating p as a <PERSON><PERSON><PERSON> distribution [24] with value 1 for NN pairs and 0 for FP pairs. Assuming that for each step in the optimization, we optimize a batch that contains one NN pair and m FP pairs, q should minimize:", "cite_spans": [{"start": 44, "end": 48, "text": "[24]", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "L N CE = -E ij∈N N,ikc=1...m / ∈N N log q ij q ij + c=1...m q ikc -m log 1 - q ij q ij + c=1...m q ikc", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "(2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L Inf oN CE = -E ij∈N N,ik1...m / ∈N N log q ij -log q ij + c=1...m q ikc .", "eq_num": "(3)"}], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "t-SNE, the most popular NE algorithm, utilizes a loss defined over the full data set. The raw t-SNE loss is usually written as the KL-divergence between high-dimensional and low-dimensional conditional probability distributions p and q. Here, we separate the loss following [25, 13] . [18] notes that the exact values of the p ij 's have limited impact and can be treated as binary weights without impacting outcomes. To simplify the calculation and allow for mini-batch stochastic gradient descent, [18] rewrote this loss as an InfoNCE loss [22] . Denoting", "cite_spans": [{"start": 274, "end": 278, "text": "[25,", "ref_id": "BIBREF24"}, {"start": 279, "end": 282, "text": "13]", "ref_id": "BIBREF12"}, {"start": 285, "end": 289, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 500, "end": 504, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 542, "end": 546, "text": "[22]", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "d 2 (i, j) = ∥f θ (x i ) -f θ (x j )∥ 2 2 + 1 = ∥y i -y j ∥ 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "2 + 1, the t-SNE loss function can be rewritten as an InfoNCE loss:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L t-SN E (θ) = -E ij∈N N,ik1...m / ∈N N log 1 d 2 (i, j) -log 1 d 2 (i, j) + c=1...m 1 d 2 (i, k c ) .", "eq_num": "(4)"}], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "Following [18] , we call the mini-batch variant Info-NC-t-SNE. We will use it from now on since the vanilla t-SNE loss requires computing pairwise distances between all points in a dataset, and it is challenging to incorporate that into the mini-batch parametric DR framework.", "cite_spans": [{"start": 10, "end": 14, "text": "[18]", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "NCVis [24] uses an NCE [21] loss. We denote the number of negative pairs in a batch as m, and set q ij = 1 d2(i,j) . The NCVis loss is:", "cite_spans": [{"start": 6, "end": 10, "text": "[24]", "ref_id": "BIBREF23"}, {"start": 23, "end": 27, "text": "[21]", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "L N CV is (θ) = -E ij∈N N,ik1...m / ∈N N   log 1 1 + c=1...m d2(i,j) d2(i,kc) -m log   1 - 1 1 + c=1...m d2(i,j) d2(i,kc)     .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "(5) [18] provides a modern implementation for both algorithms. It also provides parametric versions that adopt multilayer perceptrons (MLP) with [100, 100, 100] hidden neurons and ReLU activation.", "cite_spans": [{"start": 4, "end": 8, "text": "[18]", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "NCE/InfoNCE-based: t-SNE and NCVis", "sec_num": "2.1"}, {"text": "Negative Sampling (NEG) [23] simplifies the modeling process. Define q θ to be a similarity function in the low dimensional space:", "cite_spans": [{"start": 24, "end": 28, "text": "[23]", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "NEG-based: UMAP", "sec_num": "2.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L N EG (θ) = -E ij∈N N log q ij 1 + q ij -mE ij∈E log 1 1 + q ij .", "eq_num": "(6)"}], "section": "NEG-based: UMAP", "sec_num": "2.2"}, {"text": "UMAP [12] is a DR algorithm that utilizes the NEG loss [18] . Its loss function is", "cite_spans": [{"start": 5, "end": 9, "text": "[12]", "ref_id": "BIBREF11"}, {"start": 55, "end": 59, "text": "[18]", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "NEG-based: UMAP", "sec_num": "2.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L U M AP (θ) = -E ij∈N N log q U M AP θ (i, j) 1 + q U M AP θ (i, j) -mE ij / ∈N N log 1 1 + q U M AP θ (i, j) .", "eq_num": "(7)"}], "section": "NEG-based: UMAP", "sec_num": "2.2"}, {"text": "which is NEG with the similarity kernel", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "NEG-based: UMAP", "sec_num": "2.2"}, {"text": "q U M AP θ (i, j) = 1 d2(i,j)-1 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "NEG-based: UMAP", "sec_num": "2.2"}, {"text": "PaCMAP [13] is another recent DR algorithm that achieves high-quality data visualization. Compared to other NE algorithms, PaCMAP's loss function is designed to follow several mathematical design principles, but does not have a probabilistic explanation. The loss function (omitting the mid-near pairs term, as it is only relevant during the initial epochs, see Appendix D) is defined as follows:", "cite_spans": [{"start": 7, "end": 11, "text": "[13]", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "PaCMAP", "sec_num": "2.3"}, {"text": "L ij∈N N = W N N d 2 (i, j) d 2 (i, j) + C 1 , L ik∈F P = W F P 1 d 2 (i, j) + C 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PaCMAP", "sec_num": "2.3"}, {"text": "in which the W weights change based on the epoch, and C 1 and C 2 are set to 10 and 1, respectively.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PaCMAP", "sec_num": "2.3"}, {"text": "To study the effect of parametrization on DR algorithms, we extend the PaCMAP framework to incorporate an MLP to map the high-dimensional input to the low-dimensional embedding. We refer to the resulting parametric algorithm as ParamPaCMAP. Implementation details are in Appendix F..", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PaCMAP", "sec_num": "2.3"}, {"text": "Machine learning practitioners have long believed that parametric NE algorithms behave similarly to their non-parametric counterparts [17, 18] . In this section, we investigate the performance of the aforementioned parametric and non-parametric versions of these algorithms. To understand the effect of parametrization more thoroughly, for each parametric DR method, we additionally implemented three new versions, using a neural network with 0 hidden layers (i.e., a linear model), 1 hidden layer, or 2 hidden layers as a projector. We fix the number of neurons for each layer to 100.", "cite_spans": [{"start": 134, "end": 138, "text": "[17,", "ref_id": "BIBREF16"}, {"start": 139, "end": 142, "text": "18]", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "Effect of Parametrization on DR Results", "sec_num": "3"}, {"text": "Observation 1: Parametric NE algorithms typically lead to worse visual effects as well as worse local structure preservation. Our results indicate that parametric NE algorithms often fail to produce embeddings of the same quality as their non-parametric counterparts, even on simple datasets such as MNIST [15] . The non-parametric methods in the rightmost column of Fig. 2 are able to separate the clusters fairly well, but from the first four columns of Fig. 2 , we see that all four parametric algorithms generate clusters that are densely packed with indistinct boundaries, despite the fact that clusters in MNIST are actually separated. These blurred boundaries result in poorer preservation of local structure, with the possible exception of Parametric PaCMAP. [15] dataset generated by various DR methods with different numbers of hidden layers: 0 (Linear), 1, 2, or 3, or non-parametric variant. See Section 5.1 for details of SVM Acc. It is helpful to envision these images in black and white (without labels) to see when clusters would be difficult to visually separate. More datasets/methods can be found in App. C.", "cite_spans": [{"start": 306, "end": 310, "text": "[15]", "ref_id": "BIBREF14"}, {"start": 767, "end": 771, "text": "[15]", "ref_id": "BIBREF14"}], "ref_spans": [{"start": 372, "end": 373, "text": "2", "ref_id": "FIGREF0"}, {"start": 461, "end": 462, "text": "2", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Effect of Parametrization on DR Results", "sec_num": "3"}, {"text": "The challenge of accurately preserving local structure is exacerbated in scenarios where ground truth labels are unknown, especially in large-scale biological and chemical data, where dimensionality reduction is widely used for data exploration. In these scenarios, users may struggle to identify potential clusters within the large, indistinct conglomerates produced by the NE algorithms.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Effect of Parametrization on DR Results", "sec_num": "3"}, {"text": "Figure 2 quantitatively evaluates the quality of the embedding via the SVM accuracy, which measures local structure preservation (described in Section 5.1.) Fig. 3 further quantifies the observation. Here, we sample three kinds of pairs from the points with labels \"3\" and \"8\" from the embedding, and calculate the pairwise distance for each kind of pair. NN denotes the pairs of points that are 10-nearest neighbors, and FP denotes pairs of points that are uniformly sampled from the population. MN denotes \"mid-near\" pairs that are further than NNs but still relatively close (detailed in Sec. 4.) For each embedding, we scale the distance with respect to the scale of the embedding, and calculate the ratio of the mean FP distance to NN distance, as well as the mean MN distance to NN distance. Our analysis reveals that, in comparison to the non-parametric methods, all the parametric counterparts (App. H.1) have a smaller FP distance ratio, meaning further pairs are positioned closer together, which explains the blurred boundaries between clusters. ", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "2", "ref_id": "FIGREF0"}, {"start": 162, "end": 163, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Effect of Parametrization on DR Results", "sec_num": "3"}, {"text": "ParamRepulsor Figure 3 : The low-dimensional scaled distance distribution between various types of point pairs with labels \"3\" and \"8\" in the embedding of the MNIST digit dataset [15] Observation 2: NE algorithms with NEG loss perform better when parameterized. A widely accepted explanation for the failure of small neural network projectors, such as those used here, is that they lack the capacity to capture the complexity of the data. While adding more layers to the projector is believed to effectively mitigate the loss in local structure preservation, our experimental results in App. A shows that adding additional layers beyond three yields diminishing returns. As we discuss in App. B, adjusting hyperparameters for parametric DR algorithms-such as the number of nearest neighbors used in NN-graph construction-also had minimal impact on the resulting embeddings. In all cases, the visual quality of the embedding remained suboptimal compared to nonparametric DR methods. While all four algorithms achieve comparable performance on the MNIST dataset in the nonparametric setting, their ability to adapt to the parametric setting varies significantly. Specifically, NE methods that optimize the NEG loss (UMAP and PaCMAP) perform substantially better than those that optimize the InfoNCE/NCE loss (Info-NC-t-SNE and NCVis). As illustrated in the first four columns of Figure 2 , Info-NC-t-SNE and NCVis continue to struggle with local structure preservation in the embeddings when the number of hidden layers is one or two, whereas UMAP and PaCMAP are already capable of grouping similar samples together effectively. Why is this the case? We hypothesize that it is because UMAP and PaCMAP use NEG losses rather than InfoNCE/NCE losses. are", "cite_spans": [{"start": 179, "end": 183, "text": "[15]", "ref_id": "BIBREF14"}], "ref_spans": [{"start": 21, "end": 22, "text": "3", "ref_id": "FIGREF2"}, {"start": 1384, "end": 1385, "text": "2", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "7.874", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "q N N θ (y i , y j ) = exp( -C1 d2(i,j)+C1 ) 1 -exp( -C1 d2(i,j)+C1 ) , q F P θ (y i , y j ) = 1 -exp( d2(i,j) d2(i,j)+C2 ) exp( d2(i,j) d2(i,j)+C2 ) .", "eq_num": "(9)"}], "section": "7.874", "sec_num": null}, {"text": "Proof: see Appendix D.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "7.874", "sec_num": null}, {"text": "To better understand the difference in performance between the NCE, InfoNCE and NEG losses, we compare their terms (Eq 2, 3, 6). The term that attracts the nearest neighbors in these algorithms is consistently in the form of log q ij . This similarity is also evident in the fact that UMAP and t-SNE share the same loss function for nearest neighbors. The key distinction lies in the treatment of negative pairs or points that are not nearest neighbors. For NEG-based UMAP, the FP loss for each negative pair (i, j) is -log 1 -1 d2(i,j) , and for PaCMAP it is", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "7.874", "sec_num": null}, {"text": "1 d2(i,j)+C2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "7.874", "sec_num": null}, {"text": ". This term solely depends on a negative pair, ensuring that the gradient is large when the negative pair becomes close. This is in contrast to both NCE and InfoNCE losses, where each negative pair term is based on all pairs. Theoretical studies [26, 27] in standard contrastive learning have found that such design of loss functions may lead to a reduced gradient and worse performance under a multi-layer perceptron (MLP) model. On the other hand, the NEG loss effectively penalizes the proximity of negative pairs, enhancing the separation between dissimilar points.", "cite_spans": [{"start": 246, "end": 250, "text": "[26,", "ref_id": "BIBREF25"}, {"start": 251, "end": 254, "text": "27]", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "7.874", "sec_num": null}, {"text": "While our ParamPaCMAP algorithm preserves better local structure, the embedding space remains suboptimal, with some clusters that should be distinct still merged together. To solve the problem from its root cause, we propose ParamRepulsor, a novel parametric algorithm built upon our ParamPaCMAP. Pseudocode for ParamRepulsor is found in Alg. 1 and detailed in Alg. 2 in App. F.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ParamRepulsor", "sec_num": "4"}, {"text": "There are several major differences of ParamRepulsor from other methods, the major one being the use of Hard Negative Mining in the repulsive terms. Our goal is to learn from Hard Negative (HN) Samples -pairs whose DR projections are close but should be far apart. Efficiently sampling HNs could be challenging. Existing approaches either rely on ground truth labels that are not applicable in the unsupervised DR setting [28, 29] , or rely on the InfoNCE loss [30] which is less useful for NEG losses. We select mid-near (MN) pairs for HN sampling (for the opposite purpose they are used in PaCMAP, where they exert attractive forces). A MN point for point i is identified through the following process: 1) sample h ∼ Uniform{1, n} points from the high-dimensional data, and 2) select the second closest point from the sampled set. Here, we use h = 6. We justify the use of MN pairs as HN samples based on two key observations. Observation 3: Using MN for HN sampling reduces the probability for false negatives. Existing DR algorithms sample the negative pairs from an (approximately) uniform distribution over all possible pairs. While this approach enhances computational efficiency, it often results in false negatives, which is known to be problematic for contrastive learning [31, 30] . We show that MN pairs are ideal candidates for negatives as they rarely become false negatives.", "cite_spans": [{"start": 422, "end": 426, "text": "[28,", "ref_id": "BIBREF27"}, {"start": 427, "end": 430, "text": "29]", "ref_id": "BIBREF28"}, {"start": 461, "end": 465, "text": "[30]", "ref_id": "BIBREF29"}, {"start": 1283, "end": 1287, "text": "[31,", "ref_id": "BIBREF30"}, {"start": 1288, "end": 1291, "text": "30]", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "ParamRepulsor", "sec_num": "4"}, {"text": "Algorithm 1 Simplified Pseudocode for ParamRepulsor Require:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ParamRepulsor", "sec_num": "4"}, {"text": "X, n N B , n M N , n F P , n epochs , f θ , η, bsz, w N B , w M N , w F P 1: Initialize neural network projector f θ with parameter θ 2: for i ← 1 to N do for batch ← 1 to n batches do 7:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ParamRepulsor", "sec_num": "4"}, {"text": "Sample x = x 1 . . . , x bsz from training data, x N N = N N (x 1 . . . , x bsz ) from nearest neighbors of each point in x, x M N = M N (x 1 . . . , x bsz ) from mid-near points (see Sec.4). Sample x F P = F P (x 1 . . . , x bsz ) from uniform distribution.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ParamRepulsor", "sec_num": "4"}, {"text": "8: Calculate y = f θ (x), y N N = f θ (x N N ), y M N = f θ (x M N ), y F P = f θ (x F P ). 9: L = 0. 10: for k ← 1 to b do 11: L = L + w N B t=1...n N B d2(y k ,y N N t k ) 10+d2(y k ,y N N t k ) -w M N t=1...n M N d2(y k ,y M N t k ) 1+d2(y k ,y M N t k ) - w F P t=1...n F P d2(y k ,y F P t k ) 1+d2(y k ,y F P t k ) .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ParamRepulsor", "sec_num": "4"}, {"text": "12:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ParamRepulsor", "sec_num": "4"}, {"text": "end for 13:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ParamRepulsor", "sec_num": "4"}, {"text": "Calculate gradients ∇ θ L.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ParamRepulsor", "sec_num": "4"}, {"text": "Update parameters θ using Adam optimizer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "14:", "sec_num": null}, {"text": "end for 16: end for 17: return f θ (X) Theorem 4.1. The probability that a sampled MN point is a false negative in a dataset of size n converges to 0 at a rate of O( 1n 2 ). Corollary 4.2. MN points are less likely to be false negatives than uniformly sampled points in datasets with n ≳ 10 3 . See Appendix E for empirical results and Fig. 21 in Appendix E for projection.", "cite_spans": [], "ref_spans": [{"start": 341, "end": 343, "text": "21", "ref_id": "FIGREF21"}], "eq_spans": [], "section": "15:", "sec_num": null}, {"text": "Proof: see Appendix E<PERSON> 4.1 and Corollary 4.2 state that the likelihood for an MN to be a false negative is low. Furthermore, the simplicity of MN sampling ensures efficiency: the sampling cost is still constant for each mid-near point.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "15:", "sec_num": null}, {"text": "Observation 4: MN pairs are challenging negatives that provide better gradients for local structure preservation. The shallow parametrization used in NE DR methods ensures that distances in the high-dimensional space remain correlated with those in the low-dimensional embedding. As shown in Fig. 3 , in the blurred boundaries of clusters \"3\" and \"8,\" MN pairs tend to be closer than normal FP pairs in the embeddings of all methods (see Fig. 22 in App. H.1 for other methods). This proximity makes MN pairs challenging negatives for the algorithm, resulting in large gradients during the loss calculation. Fig. 4 illustrates the representations learned by repulsing MN hard negatives. Our approach improves the boundaries between clusters, while maintaining the proximity between close clusters. It not only achieves state-of-the-art cluster separation in parametric methods but also outperforms several non-parametric methods. ", "cite_spans": [], "ref_spans": [{"start": 297, "end": 298, "text": "3", "ref_id": "FIGREF2"}, {"start": 443, "end": 445, "text": "22", "ref_id": "FIGREF23"}, {"start": 612, "end": 613, "text": "4", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "15:", "sec_num": null}, {"text": "Here, we evaluate the performance of our ParamPaCMAP and ParamRespulsor algorithms empirically. To contextualize our findings, we juxtapose our results against those obtained from other contemporary parametric DR algorithms. Visualization for the embeddings generated by all algorithms can be found in App. C.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "Datasets. We use a wide-ranging collection of datasets across various disciplines. For image analysis, we analyzed the MNIST [15] and Fashion-MNIST (F-MNIST) [32] datasets, along with COIL-20 [33] and COIL-100 [34] . In the domain of computational biology, our assessment leveraged single-cell RNA-sequencing (scRNA-seq) datasets from studies by [35] , [36] , [37] , [38] . Further diversifying our dataset selection, the 20 Newsgroups (20NG) [39] text dataset was included for textual data analysis. The preprocessing of scRNA-seq datasets adhered to the methodology outlined by [40] . Additionally, simulated datasets featuring predefined known structures -such as Circle, Mammoth [41, 42] , Gaussian Lineage, and Gaussian Hierarchical [43] -were integrated into our analysis. See Section G.1 for more details. This multifaceted dataset compilation enables a thorough examination of the DR algorithms' performance across a spectrum of datasets.", "cite_spans": [{"start": 125, "end": 129, "text": "[15]", "ref_id": "BIBREF14"}, {"start": 158, "end": 162, "text": "[32]", "ref_id": "BIBREF31"}, {"start": 192, "end": 196, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 210, "end": 214, "text": "[34]", "ref_id": "BIBREF33"}, {"start": 346, "end": 350, "text": "[35]", "ref_id": "BIBREF34"}, {"start": 353, "end": 357, "text": "[36]", "ref_id": "BIBREF35"}, {"start": 360, "end": 364, "text": "[37]", "ref_id": "BIBREF36"}, {"start": 367, "end": 371, "text": "[38]", "ref_id": "BIBREF37"}, {"start": 443, "end": 447, "text": "[39]", "ref_id": "BIBREF38"}, {"start": 580, "end": 584, "text": "[40]", "ref_id": "BIBREF39"}, {"start": 683, "end": 687, "text": "[41,", "ref_id": "BIBREF40"}, {"start": 688, "end": 691, "text": "42]", "ref_id": "BIBREF41"}, {"start": 738, "end": 742, "text": "[43]", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "Algorithms. Besides the two algorithms we proposed in this work, ParamPaCMAP (P-PaCMAP) and ParamRepulsor (P-Rep), we also perform experiments on other recent Parametric NE algorithms: Parametric UMAP (P-UMAP) [17] , Parametric Info-NC-t-SNE (P-ItSNE) [18] , Parametric Neg-t-SNE (P-NtSNE), and Parametric NCVis (P-NCVis) [24, 18] . Besides NE algorithms, we also compare against Geometric Autoencoder (GeoAE) [44] , an autoencoder-based DR algorithm. While we note that there are many other parametric DR algorithms, they either aim to serve as an intermediate representation for downstream tasks (i.e., not visualization) [45, 46] , or focus only on image dataset only [1] . We refer readers to Section 6 for more details. We compare these algorithms on local and global structure preservation. For each algorithm, we use the hyperparameter settings and the network structure suggested in their implementation. Coincidentally, all the parametric algorithms in our experiment (except for GeoAE) are equipped with a 3-layer 100-neuron fully-connected neural network as their parametric projector f θ .", "cite_spans": [{"start": 210, "end": 214, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 252, "end": 256, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 322, "end": 326, "text": "[24,", "ref_id": "BIBREF23"}, {"start": 327, "end": 330, "text": "18]", "ref_id": "BIBREF17"}, {"start": 410, "end": 414, "text": "[44]", "ref_id": "BIBREF43"}, {"start": 624, "end": 628, "text": "[45,", "ref_id": "BIBREF44"}, {"start": 629, "end": 632, "text": "46]", "ref_id": "BIBREF45"}, {"start": 671, "end": 674, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "Other setup. For each experiment, we ran each DR algorithm 10 times using different random seeds to obtain 10 embeddings. We report the average metric measured across these 10 embeddings, highlighting the highest value in bold. An independent t-test with a significance level of p = 0.05 was conducted to assess significant differences between methods. Metrics not significantly different from the highest value are in italics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "We first look into the local structure of the embedding, which examines DR algorithms' ability to discover the cluster structure. Following previous works [47, 48, 13, 43] , we evaluate local structure using three approaches, with results below. All visualizations can be found in App. C. We achieve state-of-the-art performance in local structure preservation.", "cite_spans": [{"start": 155, "end": 159, "text": "[47,", "ref_id": "BIBREF46"}, {"start": 160, "end": 163, "text": "48,", "ref_id": "BIBREF47"}, {"start": 164, "end": 167, "text": "13,", "ref_id": "BIBREF12"}, {"start": 168, "end": 171, "text": "43]", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "Local Structure Evaluation", "sec_num": "5.1"}, {"text": "Local Structure 1: k-NN Accuracy. Here, DR is performed and the labels are revealed afterwards. A k-NN model then classifies points in the DR projection, with its accuracy as the metric of interest. We perform leave-one-out cross validation, and utilize a k-NN classifier to predict the label of the point. For embedding data with good local structure, points that belong to the same class should be close to each other, which would yield a higher k-NN accuracy. In this study, we use k = 10. Table 1 presents the 10-NN accuracy of each DR algorithm. ParamRepulsor achieves the highest accuracy on 10 out of 14 datasets and comes close to the highest accuracy on the remaining datasets, demonstrating its strong performance in preserving local structure.", "cite_spans": [], "ref_spans": [{"start": 499, "end": 500, "text": "1", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Local Structure Evaluation", "sec_num": "5.1"}, {"text": "Local Structure 2: SVM Accuracy. Table 2 in App. H.3 illustrates the SVM accuracy, estimated using 5-fold cross-validation with an SVM classifier. ParamRepulsor achieves the highest accuracy on 9 out of the 14 datasets and achieves near-highest accuracy on the remaining datasets. These results demonstrate that ParamRepulsor attains state-of-the-art performance in preserving local structure.", "cite_spans": [], "ref_spans": [{"start": 39, "end": 40, "text": "2", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Local Structure Evaluation", "sec_num": "5.1"}, {"text": "Local Structure 3: Nearest Neighbor Kept. We further evaluate the ability of DR methods to maintain high-dimensional k-NN in the low-dimensional space. We use k = 30 to provide a more robust estimate of neighborhood preservation ability. Using a larger k = 30 value ensures that even if the first nearest neighbor in the high-dimensional space is placed as the tenth nearest neighbor in the embedding, it is still considered preserved. This approach mitigates the effects of the reduced dimensionality of the embedding, where small shifts can otherwise result in the loss of neighborhood relationships. Table 3 in App. H.3 demonstrates that ParamRepulsor achieves the highest accuracy on 10 out of 14 datasets and nearly the highest on 3 others, showcasing its strong performance in preserving local structure. Additionally, our implementation of ParamPaCMAP performs comparably to the best methods on all but 2 datasets.", "cite_spans": [], "ref_spans": [{"start": 609, "end": 610, "text": "3", "ref_id": "TABREF4"}], "eq_spans": [], "section": "Local Structure Evaluation", "sec_num": "5.1"}, {"text": "We evaluate global structure by evaluating the preservation of cluster-level triplet relationships. Cluster-level triplet relationship preservation is important, particularly for computational biologists performing lineage analysis. Following [43] , the metric for this is a Spearman (rank correlation). To compute it, we take one cluster centroid c and rank all other centroids based on low-dimensional distance to c. We also repeat this for all C cluster centroids and place these rankings in a single vector. We repeat the process for the high-dimensional space and place these rankings in another vector. The Spearman correlation between these two vectors is the result, shown in Table 4 in App. H.3. Out of the 14 datasets, ParamPaCMAP achieves the highest correlation on 5 of them, whereas ParamRepulsor achieves the highest on 4. These results suggest our ideas are powerful for global structure preservation.", "cite_spans": [{"start": 243, "end": 247, "text": "[43]", "ref_id": "BIBREF42"}], "ref_spans": [{"start": 690, "end": 691, "text": "4", "ref_id": "TABREF5"}], "eq_spans": [], "section": "Global Structure Evaluation", "sec_num": "5.2"}, {"text": "The evolution of DR algorithms can be broadly categorized into two distinct phases. In the initial phase, the focus was on the development of methods that preserved only global structure. Key techniques in this category include Principal Components Analysis (PCA) [49] , Multidimensional Scaling [50] , and Non-negative Matrix Factorization [51] . While these methods effectively maintain the global layout of the data, their primary limitation is that they often fail to retain the inherent neighborhoods and clusters of the data.", "cite_spans": [{"start": 264, "end": 268, "text": "[49]", "ref_id": "BIBREF48"}, {"start": 296, "end": 300, "text": "[50]", "ref_id": "BIBREF49"}, {"start": 341, "end": 345, "text": "[51]", "ref_id": "BIBREF50"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "6"}, {"text": "Subsequent DR methods were developed to address the shortcomings by emphasizing the preservation of local structure, specifically focusing on preserving k nearest neighbor relationships in the original dataset. These local methods, such as Isomap [52] , Local Linear Embedding (LLE) [53] , Laplacian Eigenmap [54] , and more recent Neighborhood Embedding (NE) algorithms like t-SNE [10] and UMAP [12] , are particularly adept at maintaining cluster structure. However, they may not adequately preserve the overall spatial layout of clusters. NE methods are more frequently used because they show clusters and manifolds in the high-dimensional space that are difficult to see any other way.", "cite_spans": [{"start": 247, "end": 251, "text": "[52]", "ref_id": "BIBREF51"}, {"start": 283, "end": 287, "text": "[53]", "ref_id": "BIBREF52"}, {"start": 309, "end": 313, "text": "[54]", "ref_id": "BIBREF53"}, {"start": 382, "end": 386, "text": "[10]", "ref_id": "BIBREF9"}, {"start": 396, "end": 400, "text": "[12]", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "6"}, {"text": "NE methods are typically non-parametric, creating a low-dimensional embedding that maps each data point to a location in 2D, but there does not exist a function that maps from the original (highdimensional) space to the embedding space. To map new points to the low dimensional space, one typically creates a nonparametric map from high to low dimensions that places new points near their high-dimensional neighbors (assuming one does not want to rerun the algorithm when adding new points). This approach creates crowding problems, where many high-dimensional points map to the same location in low dimensions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "6"}, {"text": "To address the challenges posed by non-parametric NE algorithms, parametric NE algorithms have emerged as an effective solution. These algorithms focus on learning a function that maps data from a high-dimensional space into a low-dimensional embedding, typically using a neural network.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "6"}, {"text": "Examples of this approach include the Multi-layer Perceptron based Parametric t-SNE [16] , DEC [55] , kernel t-SNE [56] and Parametric UMAP [17] . Furthermore, recent advancements have integrated concepts from Contrastive Learning and Representation Learning, with significant contributions from TopoAE [57] , GeoAE [44] , t-SimCNE [1] , and Parametric InfoNC-t-SNE [18] .", "cite_spans": [{"start": 84, "end": 88, "text": "[16]", "ref_id": "BIBREF15"}, {"start": 95, "end": 99, "text": "[55]", "ref_id": "BIBREF54"}, {"start": 115, "end": 119, "text": "[56]", "ref_id": "BIBREF55"}, {"start": 140, "end": 144, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 303, "end": 307, "text": "[57]", "ref_id": "BIBREF56"}, {"start": 316, "end": 320, "text": "[44]", "ref_id": "BIBREF43"}, {"start": 332, "end": 335, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 366, "end": 370, "text": "[18]", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "6"}, {"text": "Recently, [13] , [1] and [18] discussed the effect of the loss function forces in NE algorithms. Our work differs from them; in our work, we discuss the effect of parametrization, which is not discussed in previous works.", "cite_spans": [{"start": 10, "end": 14, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 17, "end": 20, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 25, "end": 29, "text": "[18]", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "6"}, {"text": "Learning from Hard Negatives has proven effective in supervised learning [28] , metric learning [29] , as well as contrastive learning [30] . To the best of our knowledge, our work is the first that explores the effect of Hard Negative Mining in dimensionality reduction.", "cite_spans": [{"start": 73, "end": 77, "text": "[28]", "ref_id": "BIBREF27"}, {"start": 96, "end": 100, "text": "[29]", "ref_id": "BIBREF28"}, {"start": 135, "end": 139, "text": "[30]", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "6"}, {"text": "Parameterization of DR methods has major practical advantages. It allows for new data to be mapped directly from the high-dimensional space to the low-dimensional space by a function. We introduced a new method called ParamRepulsor, which demonstrates enhanced preservation of local structure without compromising global structure metrics, making it applicable across a broad spectrum of scientific inquiry.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion and Limitations", "sec_num": "7"}, {"text": "We note that our method also exhibit limitations. Although ParamRepulsor outperforms Parametric UMAP in terms of speed, it requires more computational time than Parametric Info-NC-t-SNE. Other open questions that are not resolved by this work include the design of evaluation metrics that better reflect performance, and choosing the optimal architecture for both preservation and generalization.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion and Limitations", "sec_num": "7"}, {"text": "A Discussion on the Depth of of Neural Network Projector show that further increasing the number of layers beyond increasing the number of layers beyond three yields only diminishing and negligible improvements in local structure on all three methods. Fig. 5 shows the effect of further increasing the number of hidden layers beyond three in the neural network projector for Parametric Info-NC-t-SNE (P-ItSNE) [18] , UMAP (P-UMAP) [17] , and PaCMAP (P-PaCMAP). P-ItSNE receives little benefits from further increasing layers, while P-UMAP and P-PaCMAP do not receive any further improvements. The magnitude of local structure accuracy increase is rapidly diminishing, and the visual effect is still suboptimal compared to their non-parametric counterpart.", "cite_spans": [{"start": 410, "end": 414, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 431, "end": 435, "text": "[17]", "ref_id": "BIBREF16"}], "ref_spans": [{"start": 257, "end": 258, "text": "5", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Discussion and Limitations", "sec_num": "7"}, {"text": "B Discussion on the Hyperparameter settings of Parametric DR algorithm ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion and Limitations", "sec_num": "7"}, {"text": "In this section we provide visualizations for the output of all DR methods. Fig. 7 visualizes the output of ParamRepulsor. Notably, ParamRepulsor performs well on all datasets and achieves state-of-the-art on both local and global structure preservation. On MNIST, ParamRepulsor is the only parametric algorithm that separates the clusters with clear boundaries. Compared to nonparametric algorithms, ParamRepulsor has better global structure preservation, as it is able to keep the structure of the mammoth on Mammoth dataset, and keep the structure of the hierarchy on the Hierarchy dataset. ", "cite_spans": [], "ref_spans": [{"start": 81, "end": 82, "text": "7", "ref_id": "FIGREF7"}], "eq_spans": [], "section": "C Visualizations from all DR methods", "sec_num": null}, {"text": "Recall that the NEG loss follows the form", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L N EG (θ) = -E ij∈N N log q θ (y i , y j ) 1 + q θ (y i , y j ) -mE ij∈E log 1 1 + q θ (y i , y j ) (", "eq_num": "10"}], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "where m is the number of negative samples of each batch and q θ is the similarity function defined in the low dimensional space. Now, we consider the PaCMAP loss. While the PaCMAP loss optimization process involves three stages with different emphasis on the loss terms, the first two stages are essentially equivalent to the early exaggeration used in t-SNE and UMAP [13] . Therefore we consider only the last stage of the PaCMAP optimization process that involves only the NN and FP losses:", "cite_spans": [{"start": 368, "end": 372, "text": "[13]", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L P aCM AP (θ) = L ij∈N N + L ij∈F P = ij∈N N d 2 (i, j) d 2 (i, j) + 10 + ij∈F P 1 d 2 (i, j) + 1 . (", "eq_num": "11"}], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "While PaCMAP samples the repulsion using a pre-defined set of further pairs, the further pairs themselves are uniformly sampled from all the points that are not nearest neighbors. The number of neighbors are usually tiny compared to the size of the dataset. In our experiments, adding the nearest neighbors back to the further pairs candidate set does not generate any major impact to the datasets. Therefore, here we consider it to be essentially the same as sampling from ij ∈ E.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "Since the purpose of the loss function is to find θ that minimizes it, applying any affine transformation will not affect the optimum. Recall that in PaCMAP, the set F P is m times of the size of N N . Thus, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L P aCM AP (θ) = ij∈N N d 2 (i, j) d 2 (i, j) + 10 + ij∈F P 1 d 2 (i, j) + 1 (12) = #N N • E ij∈N N d 2 (i, j) d 2 (i, j) + 10 + mE ij∈E 1 d 2 (i, j) + 1", "eq_num": "(13)"}], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∝ E ij∈N N d 2 (i, j) d 2 (i, j) + 10 + mE ij∈E 1 d 2 (i, j) + 1 (14) ∝ -E ij∈N N 10 d 2 (i, j) + 10 -mE ij∈E d 2 (i, j) d 2 (i, j) + 1 (15) = -E ij∈N N log exp 10 d 2 (i, j) + 10 -mE ij∈E log exp d 2 (i, j) d 2 (i, j) + 1 .", "eq_num": "(16)"}], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "Due to the different choices of normalizing constant in the NN loss and the FP loss, the PaCMAP actually utilizes a different kernel to model the similarity between the NN and FPs. Solving for the functions q N N θ and q F P θ , we have the result:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "q N N θ (y i , y j ) = exp( -10 d2(i,j)+10 ) 1 -exp( -10 d2(i,j)+10 )", "eq_num": "(17)"}], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "q F P θ (y i , y j ) = 1 -exp( d2(i,j) d2(i,j)+1 ) exp( d2(i,j) d2(i,j)+1 ) . (", "eq_num": "18"}], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "E Proof that the Mid-Near Hard Negative False Negative Rate converges to 0 quadratically", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "For simplicity, we consider a dataset of size n + 1, so that each point will sample from a pool of n points. For each point, we consider its k = 10 nearest neighbors as its positive points, and the rest of the points as negative points. Now, we consider the mid-near sample process. Recall that the mid-near point samples the second closest point from a pool of 6 points. Denote the event that a mid-near point being a false negative as A. A essentially means that there exist more than one point from the k nearest neighbors being sampled. Therefore, we know that Ā means there is at most one point in the samples comes from the k nearest neighbors, which essentially gives us:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "P( Ā) = n-10 6 n", "eq_num": "6"}], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "All points sampled are not NN", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "+ 10 • n-10 5 n", "eq_num": "6"}], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "Only one point comes from NN (19)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "= 6!(n -6)!(n -10)! n!6!(n -16)! + 6!(n -6)!(n -10)! • 10 n!5!(n -15)! (20) = (n -6)!(n -10)! n!(n -16)! + 60 n -15 • (n -6)!(n -10)! n!(n -16)! (21) = n + 45 n -15 • (n -6)!(n -10)! n!(n -16)! (22) = (n + 45)(n -10)(n -11)(n -12)(n -13)(n -14) n(n -1)(n -2)(n -3)(n -4)(n -5) (23) = n 6 -15n 5 -1265n 4 + O(n 3 ) n 6 -15n 5 + 85n 4 + O(n 3 ) . (", "eq_num": "24"}], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "It follows that lim n→∞ P( Ā) = 1, and it converges at the rate of O( 1 n 2 ). Because P(A) = 1 -P( Ā), we know that P(A) also converges to 0 at the rate of O( 1n 2 ). This is much faster than the uniform sampling. For uniform sampling, the false negative probability is always 10 n , which is linear. This is particularly bad for DR algorithms: the number of negative samples is usually linear w.r.t. n. Fig. 21 illustrates the probability of false negatives sampled from a dataset. We found that as long as we have more than 1330 points in our dataset, mid-near sampling can generate less false negatives than uniform sampling. This is a particularly small number to achieve in the era of big data. In order to faithfully reflect the impact of parametrization on the embedding, our baseline parametric PaCMAP is written in a way to keep as much detail unchanged from the non-parametric version.", "cite_spans": [], "ref_spans": [{"start": 410, "end": 412, "text": "21", "ref_id": "FIGREF21"}], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "ParamRepulsor implementation is written based on the Parametric PaCMAP, but many changes are made to enhance the performance on local structure.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "Network Structure. In line with existing parametric DR method implementation [17, 18] , we parametrize the projector with a shallow Multi-layer Perceptron (MLP). Unless otherwise specified, we utilize a network of three hidden layers with [100, 100, 100] neurons. ParamRepulsor utilizes SiLU as the activation function, whereas ParamPaCMAP utilizes ReLU just as the other methods.", "cite_spans": [{"start": 77, "end": 81, "text": "[17,", "ref_id": "BIBREF16"}, {"start": 82, "end": 85, "text": "18]", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "Besides utilizing basic MLP as the projector, both ParamRepulsor and ParamPaCMAP can use other network structures. We provide implementation for MLP with residual connection, convolutional neural networks, We also allow using an embedding layer as the projector so that the network behavior is similar to non-parametric version.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "Initialization. As a non-parametric algorithm, PaCMAP directly optimizes the low-dimensional embedding, and utilizes the first two principal components of the data as its initialization. After the introduction of the neural network projector, we can no longer use this initialization. For both ParamRepulsor and ParamPaCMAP, we initialize all our neural network parameters with Kaiming Initialization [61] .", "cite_spans": [{"start": 401, "end": 405, "text": "[61]", "ref_id": "BIBREF60"}], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "Optimization Schedule. Since our neural network optimization schedule is performed by minibatch stochastic gradient descent, we are unable to optimize the full embedding at once as in non-parametric PaCMAP. Therefore, at each step, we sample a batch of points, and find NN, MN, and FP points for each element in the sample.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "All the points sampled will be sent to the neural network to calculate the standard PaCMAP loss. We adopt the Adam Optimizer [62] with β = (0.9, 0.999) and a batch size of 1024. Refer to the algorithm below for more details.", "cite_spans": [{"start": 125, "end": 129, "text": "[62]", "ref_id": "BIBREF61"}], "ref_spans": [], "eq_spans": [], "section": "D Proof that PaCMAP's loss follows NEG", "sec_num": null}, {"text": "G.1 Datasets Used MNIST. MNIST [15] is a hand-written digits dataset containing 70,000 grayscale images of the shape 28×28. The images are flattened.", "cite_spans": [{"start": 31, "end": 35, "text": "[15]", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "G Experimental Details", "sec_num": null}, {"text": "F-MNIST. Fashion-MNIST (F-MNIST) [32] is a dataset containing 70,000 grayscale fashion images of the shape 28×28. The images are flattened.", "cite_spans": [{"start": 33, "end": 37, "text": "[32]", "ref_id": "BIBREF31"}], "ref_spans": [], "eq_spans": [], "section": "G Experimental Details", "sec_num": null}, {"text": "USPS. USPS [63] is a dataset containing 9298 written digit images of the shape 16×16. The images are flattened.", "cite_spans": [{"start": 11, "end": 15, "text": "[63]", "ref_id": "BIBREF62"}], "ref_spans": [], "eq_spans": [], "section": "G Experimental Details", "sec_num": null}, {"text": "The COIL-20 [33] dataset is a is a database of 1440 gray-scale images of 20 objects. The images are flattened.", "cite_spans": [{"start": 12, "end": 16, "text": "[33]", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "COIL-20", "sec_num": null}, {"text": "The COIL-100 [34] dataset is a is a database of 7200 color images of 100 objects. The images are flattened.", "cite_spans": [{"start": 13, "end": 17, "text": "[34]", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "COIL-100", "sec_num": null}, {"text": "The 20NewsGroup [39] dataset contains about 18000 newsgroups posts on 20 topics. We utilize scikit-learn [64] TF-IDF vectorizer to convert each post into a vector.", "cite_spans": [{"start": 16, "end": 20, "text": "[39]", "ref_id": "BIBREF38"}, {"start": 105, "end": 109, "text": "[64]", "ref_id": "BIBREF63"}], "ref_spans": [], "eq_spans": [], "section": "20NG", "sec_num": null}, {"text": "<PERSON> et al. The <PERSON> et al. [35] dataset contains scRNA-seq data from 13999 cells and 14053 genes, with 13 types identified by scientists. The first 50 principal components from the raw data are used.", "cite_spans": [{"start": 28, "end": 32, "text": "[35]", "ref_id": "BIBREF34"}], "ref_spans": [], "eq_spans": [], "section": "20NG", "sec_num": null}, {"text": "<PERSON><PERSON> et al. The <PERSON><PERSON> et al. [36] dataset contains scRNA-seq data from 59286 cells and 16980 genes, with 7 types identified by scientists. The first 50 principal components from the raw data are used.", "cite_spans": [{"start": 30, "end": 34, "text": "[36]", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "20NG", "sec_num": null}, {"text": "Algorithm 2 Detailed Pseudocode for ParamRepulsor Require: X -high-dimensional data matrix of the shape (N, D). n N B , n M N , n F P -the number of neighbor pairs, mid-near pairs, further pairs n epochs -the number of epochs for optimization p θ -neural network projector with parameter θ. η -learning rate. b -mini batch size. w N B , w M N , w F P -the weights associated with neighbor, mid-near, and further pairs at epoch t. 1: Initialize neural network projector p θ with parameter θ 2: for i ← 1 to N do 12:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "20NG", "sec_num": null}, {"text": "x N N = N N (x 1 . . . , x b ) from the nearest neighbors of x.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "20NG", "sec_num": null}, {"text": "x M N = M N (x 1 . . . , x b ) from the mid nears of x.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "13:", "sec_num": null}, {"text": "14:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "13:", "sec_num": null}, {"text": "x F P = F P (x 1 . . . , x b ), in which x F P k = x t , t ∼ Uniform(1, n). 15: Calculate y = f θ (x), y N N = f θ (x N N ), y M N = f θ (x M N ), y F P = f θ (x F P ). 16: L = 0. 17: for k ← 1 to b do 18: L = L + w N B t=1...n N B d2(yi,y N N t i ) 10+d2(yi,y N N t i ) -w M N t=1...n M N d2(yi,y M N t i ) 1+d2(yi,y M N t i ) - w F P t=1...n F P d2(yi,y F P t i ) 1+d2(yi,y F P t i ) . 19:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "13:", "sec_num": null}, {"text": "end for 20:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "13:", "sec_num": null}, {"text": "Calculate gradients ∇ θ L.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "13:", "sec_num": null}, {"text": "Update parameters θ using Adam optimizer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "21:", "sec_num": null}, {"text": "end for 23: end for 24: return f θ (X) <PERSON><PERSON><PERSON> et al. The <PERSON><PERSON><PERSON> et al. [37] dataset contains scRNA-seq data from 2282 cells and 18962 genes, with 9 types identified by scientists. The first 50 principal components from the raw data are used.", "cite_spans": [{"start": 71, "end": 75, "text": "[37]", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "22:", "sec_num": null}, {"text": "<PERSON> et al. The <PERSON> et al. [38] dataset contains scRNA-seq data from 30672 cells and 17009 genes, with 25 types identified by scientists. The first 50 principal components from the raw data are used.", "cite_spans": [{"start": 32, "end": 36, "text": "[38]", "ref_id": "BIBREF37"}], "ref_spans": [], "eq_spans": [], "section": "22:", "sec_num": null}, {"text": "Circle The Circle dataset comprises of 5000 points uniformly sampled from a 2D circle with radius 1. The circle is divided into ten arcs of the same length, and each point receives a label that represents the index of the arc it belongs to.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "22:", "sec_num": null}, {"text": "Mammoth The mammoth dataset [42, 41] contains 10k points from a 3D woolly mammoth skeleton.", "cite_spans": [{"start": 28, "end": 32, "text": "[42,", "ref_id": "BIBREF41"}, {"start": 33, "end": 36, "text": "41]", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "22:", "sec_num": null}, {"text": "Lineage The Gaussian Lineage dataset [5, 43] contains 10000 points in twenty 50-dimensional Gaussians, equally separated on a line.", "cite_spans": [{"start": 37, "end": 40, "text": "[5,", "ref_id": "BIBREF4"}, {"start": 41, "end": 44, "text": "43]", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "22:", "sec_num": null}, {"text": "Hierarchy The Gaussian Hierarchical Dataset [43] contains 12500 points. The points belongs to 125 micro clusters, arranged into 5 macro and 25 meso clusters. Each micro cluster includes 100 observations.", "cite_spans": [{"start": 44, "end": 48, "text": "[43]", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "22:", "sec_num": null}, {"text": "All experiments are conducted with an Exxact TensorEX 2U Server with 2 Intel Xeon Ice Lake Gold 5317 Processors @ 3.0GHz. We limit the RAM usage to be 32GB. Parallel computation are performed over a single Nvidia RTX A5000 GPU.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G.2 Computation Platforms", "sec_num": null}, {"text": "Fig. 22 provides a comprehensive analysis over distances between different kinds of pairs, generated by multiple DR algorithms. We can see that all parametric methods generate a shorter FP distance compared against their non-parametric counterpart. MN pairs, though should be classified as FPs, tend to be harder to optimize, resulting in a shorter distance on average. ", "cite_spans": [], "ref_spans": [{"start": 5, "end": 7, "text": "22", "ref_id": "FIGREF23"}], "eq_spans": [], "section": "H.1 Additional Analysis on Distance Distribution in Embedding", "sec_num": null}, {"text": "As datasets grows larger, scalability becomes more important. We evaluate the time consumed by ParamInfo-NC-t-SNE, ParamRepulsor and ParamUMAP on two extremely large datasets, from [65] and [4] . The dataset sizes are 1, 306, 127 and 2, 058, 652, respectively. The results are shown in Figure 23 . We can see that ParamRepulsor outperforms ParamUMAP in terms of scalability. While ParamRepulsor is slower than ParamInfo-NC-t-SNE, the speed is still comparable in terms of magnitude. We note that ParamInfo-NC-t-SNE utilizes smaller number of epochs, which gives it a higher speed, but at the cost of an underoptimized embedding, as shown in Section 5. The computational efficiency of ParamRepulsor can be further improved by a better optimization schedule as well as computational improvements, which we leave for future works. Table 2,  3, 5, 4 measure the SVM accuracy, k-nearest neighbor preservation ratio, Triplet preservation ratio, and cluster centroid distance correlation. We note that GeoAE performs particularly well on Triplet preservation. This is expected: as an autoencoder-based method, GeoAE aims to preserve the geographical distance information in the high-dimensional space, usually at the cost of the local structure. As a result, its local structure performance is particularly low. However, our method, ParamPaCMAP and ParamRepulsor, achieve comparable result on this metric. • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.", "cite_spans": [{"start": 181, "end": 185, "text": "[65]", "ref_id": "BIBREF64"}, {"start": 190, "end": 193, "text": "[4]", "ref_id": "BIBREF3"}], "ref_spans": [{"start": 293, "end": 295, "text": "23", "ref_id": "FIGREF24"}, {"start": 828, "end": 836, "text": "Table 2,", "ref_id": "TABREF3"}, {"start": 837, "end": 837, "text": "", "ref_id": null}], "eq_spans": [], "section": "H.2 Computational Speed Evaluation", "sec_num": null}, {"text": "Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the results?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Answer: [Yes] Justification: Experimental setting is disclosed and can be found in our implementation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Justification: We conduct multiple rounds of experiments and performed t-test over the results to ensure statistical significance.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction). 10. Broader Impacts Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed? Answer: [NA] Justification: There is no specific societal impact of the work. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Justification: The paper does not pose such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Justification: Existing assets are properly cited and we also provide details on dataset and implementation used in the appendix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}], "back_matter": [{"text": "We acknowledge funding from the National Science Foundation under grants IIS-2130250, IIS-2147061, DGE-2022040 and the National Institutes of Health under grant 5R01-DA054994.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgement", "sec_num": null}, {"text": "Implementations of ParamRepulsor/ParamPaCMAP discussed in this paper, along with the code for the experiments, are available at https://github.com/hyhuang00/ParamRepulsor. The datasets used in our study are publicly accessible from their original publications.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code and data availability", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?Answer: [Yes] Justification: We provide detailed analysis and experimental results, both in main text as well as the appendix, to support the observation, theoretical, and experimental results in the abstract and introduction.Guidelines:• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "cite_spans": [{"start": 130, "end": 135, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?Answer: [Yes] Justification: We discussed limitation of our work in the discussion section and the appendix.Guidelines:• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [{"start": 94, "end": 99, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?Justification: Detailed proof is provided in the appendix.Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?Answer: [Yes] Justification: We provide our implementation alongside our submission so that the experimental results can be reproduced.", "cite_spans": [{"start": 271, "end": 276, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "• If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets?Answer: [Yes] Justification: New code comes with proper documentation.Guidelines:• The answer NA means that the paper does not release new assets. Guidelines:• The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Including this information in the supplemental material is fine, but if the main contribution of the paper involves human subjects, then as much detail as possible should be included in the main paper. ", "cite_spans": [{"start": 128, "end": 133, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Unsupervised visualization of image datasets using contrastive learning", "authors": [{"first": "J", "middle": ["N"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Kobak", "suffix": ""}], "year": 2023, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Unsupervised visualization of image datasets using contrastive learning. In International Conference on Learning Representations. 2023.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "All-but-the-Top: Simple and Effective Postprocessing for Word Representations", "authors": [{"first": "J", "middle": [], "last": "Mu", "suffix": ""}, {"first": "S", "middle": [], "last": "Bhat", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. All-but-the-Top: Simple and Effective Postprocessing for Word Representations. In International Conference on Learning Representations. 2018.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Effective Dimensionality Reduction for Word Embeddings", "authors": [{"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Metze", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 4th Workshop on Representation Learning for NLP (RepL4NLP-2019)", "volume": "", "issue": "", "pages": "235--243", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Effective Dimensionality Reduction for Word Embeddings. In Proceedings of the 4th Workshop on Representation Learning for NLP (RepL4NLP-2019), pages 235-243. 2019.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "The single-cell transcriptional landscape of mammalian organogenesis", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Nature", "volume": "566", "issue": "7745", "pages": "496--502", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. The single-cell transcriptional landscape of mammalian organogenesis. Nature, 566(7745):496-502, 2019.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Dimensionality reduction for visualizing single-cell data using UMAP", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Mcinnes", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Nature Biotechnology", "volume": "37", "issue": "1", "pages": "38--44", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Dimensionality reduction for visualizing single-cell data using UMAP. Nature Biotechnology, 37(1):38-44, 2019.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Orchestrating Single-Cell Analysis with Bioconductor", "authors": [{"first": "R", "middle": ["A"], "last": "Amezqui<PERSON>", "suffix": ""}, {"first": "A", "middle": ["T"], "last": "<PERSON>n", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Nature Methods", "volume": "17", "issue": "2", "pages": "137--145", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, et al. Orchestrating Single-Cell Analysis with Biocon- ductor. Nature Methods, 17(2):137-145, 2020.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Giotto: a toolbox for integrative analysis and visualization of spatial expression data", "authors": [{"first": "R", "middle": [], "last": "Dries", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Genome Biology", "volume": "22", "issue": "", "pages": "1--31", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. <PERSON>: a toolbox for integrative analysis and visualization of spatial expression data. Genome Biology, 22:1-31, 2021.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Model-based evaluation of spatiotemporal data reduction methods with unknown ground truth through optimal visualization and interpretability metrics", "authors": [{"first": "K", "middle": [], "last": "Atitey", "suffix": ""}, {"first": "A", "middle": ["A"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Anchang", "suffix": ""}], "year": 2024, "venue": "Briefings in Bioinformatics", "volume": "25", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>. Model-based evaluation of spatiotemporal data reduction methods with unknown ground truth through optimal visualization and interpretability metrics. Briefings in Bioinformatics, 25(1):bbad455, 2024.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Attraction-Repulsion Spectrum in Neighbor Embeddings", "authors": [{"first": "J", "middle": ["N"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Kobak", "suffix": ""}], "year": 2022, "venue": "Journal of Machine Learning Research", "volume": "23", "issue": "1", "pages": "4118--4149", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Attraction-Repulsion Spectrum in Neighbor Embeddings. Journal of Machine Learning Research, 23(1):4118-4149, 2022.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Visualizing Data using t-SNE", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2008, "venue": "Journal of Machine Learning Research", "volume": "9", "issue": "", "pages": "2579--2605", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Visualizing Data using t-SNE. Journal of Machine Learning Research, 9:2579-2605, 2008.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Visualizing Large-Scale and High-Dimensional Data", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2016, "venue": "Proceedings of the 25th International Conference on the World Wide Web", "volume": "", "issue": "", "pages": "287--297", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Visualizing Large-Scale and High-Dimensional Data. In Proceedings of the 25th International Conference on the World Wide Web, pages 287-297. 2016.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "UMAP: Uniform Manifold Approximation and Projection for Dimension Reduction", "authors": [{"first": "L", "middle": [], "last": "Mcinnes", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1802.03426"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. UMAP: Uniform Manifold Approximation and Projection for Dimension Reduction. arXiv e-prints, arXiv:1802.03426, 2018.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Understanding How Dimension Reduction Tools Work: An Empirical Approach to Deciphering t-SNE, UMAP, TriMAP, and PaCMAP for Data Visualization", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Journal of Machine Learning Research", "volume": "22", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Understanding How Dimension Reduction Tools Work: An Empirical Approach to Deciphering t-SNE, UMAP, TriMAP, and PaCMAP for Data Visualization. Journal of Machine Learning Research, 22, 2021.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Github -lmcinnes/umap: Uniform Manifold Approximation and Projection (UMAP)", "authors": [{"first": "Leland", "middle": [], "last": "Mcinnes", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "McInnes, Leland. Github -lmcinnes/umap: Uniform Manifold Approximation and Projection (UMAP). https://github.com/lmcinnes/umap, 2020.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "MNIST handwritten digit database", "authors": [{"first": "Y", "middle": [], "last": "Lecun", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2010, "venue": "ATT Labs", "volume": "", "issue": "2", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. MNIST handwritten digit database. ATT Labs [Online]. Available: http://yann. lecun. com/exdb/mnist, 2, 2010.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Learning a Parametric Embedding by Preserving Local Structure", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2009, "venue": "Artificial Intelligence and Statistics", "volume": "", "issue": "", "pages": "384--391", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> a Parametric Embedding by Preserving Local Structure. In Artificial Intelligence and Statistics, pages 384-391. PMLR, 2009.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Parametric UMAP embeddings for representation and semisupervised learning", "authors": [{"first": "T", "middle": [], "last": "Sainburg", "suffix": ""}, {"first": "L", "middle": [], "last": "Mcinnes", "suffix": ""}, {"first": "T", "middle": ["Q"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Neural Computation", "volume": "33", "issue": "11", "pages": "2881--2907", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>. Parametric UMAP embeddings for representation and semisupervised learning. Neural Computation, 33(11):2881-2907, 2021.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "From t-SNE to UMAP with contrastive learning", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["N"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": ["A"], "last": "Hamprecht", "suffix": ""}], "year": 2023, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, et al. From t-SNE to UMAP with contrastive learning. In International Conference on Learning Representations. 2023.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "On the Surprising Behavior of Distance Metrics in High Dimensional Space", "authors": [{"first": "C", "middle": ["C"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Hinneburg", "suffix": ""}, {"first": "D", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2001, "venue": "Database Theory-ICDT 2001: 8th International Conference", "volume": "8", "issue": "", "pages": "420--434", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>. On the Surprising Behavior of Distance Metrics in High Dimensional Space. In Database Theory-ICDT 2001: 8th International Conference London, UK, January 4-6, 2001 Proceedings 8, pages 420-434. Springer, 2001.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Your Contrastive Learning is Secretly Doing Stochastic Neighbor Embedding", "authors": [{"first": "T", "middle": [], "last": "Hu", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Your Contrastive Learning is Secretly Doing Stochastic Neighbor Embedding. In International Conference on Learning Representations. 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Noise-contrastive estimation: A new estimation principle for unnormalized statistical models", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Hyvärinen", "suffix": ""}], "year": 2010, "venue": "Proceedings of the International Conference on Artificial Intelligence and Statistics", "volume": "", "issue": "", "pages": "297--304", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Noise-contrastive estimation: A new estimation principle for unnormalized statistical models. In Proceedings of the International Conference on Artificial Intelligence and Statistics, pages 297-304. 2010.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Representation Learning with Contrastive Predictive Coding", "authors": [{"first": "A", "middle": ["V"], "last": "Oord", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>yal<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1807.03748"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Representation Learning with Contrastive Predictive Coding. arXiv preprint arXiv:1807.03748, 2018.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Distributed Representations of Words and Phrases and their Compositionality", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2013, "venue": "Advances in Neural Information Processing Systems", "volume": "26", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Distributed Representations of Words and Phrases and their Compositionality. Advances in Neural Information Processing Systems, 26, 2013.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Ncvis: Noise Contrastive Approach for Scalable Visualization", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of The Web Conference", "volume": "", "issue": "", "pages": "2941--2947", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>: Noise Contrastive Approach for Scalable Visualization. In Proceedings of The Web Conference, pages 2941-2947. 2020.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Accelerating t-SNE using Tree-Based Algorithms", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "Journal of Machine Learning Research", "volume": "15", "issue": "", "pages": "3221--3245", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> Accelerating t-SNE using Tree-Based Algorithms. Journal of Machine Learning Research, 15:3221-3245, 2014.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Decoupled contrastive learning", "authors": [{"first": "C.-H", "middle": [], "last": "Yeh", "suffix": ""}, {"first": "C.-Y", "middle": [], "last": "Hong", "suffix": ""}, {"first": "Y.-C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "European Conference on Computer Vision", "volume": "", "issue": "", "pages": "668--684", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, et al. Decoupled contrastive learning. In European Conference on Computer Vision, pages 668-684. Springer, 2022.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Understanding deep contrastive learning via coordinate-wise optimization", "authors": [{"first": "Y", "middle": [], "last": "Tian", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>. Understanding deep contrastive learning via coordinate-wise optimization. Advances in Neural Information Processing Systems, 35:19511-19522, 2022.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Facenet: A unified embedding for face recognition and clustering", "authors": [{"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "815--823", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Facenet: A unified embedding for face recognition and clustering. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, pages 815-823. 2015.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Deep metric learning via lifted structured feature embedding", "authors": [{"first": "Oh", "middle": [], "last": "Song", "suffix": ""}, {"first": "H", "middle": [], "last": "", "suffix": ""}, {"first": "Y", "middle": [], "last": "Xiang", "suffix": ""}, {"first": "S", "middle": [], "last": "Jegelka", "suffix": ""}], "year": 2016, "venue": "Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "4004--4012", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Deep metric learning via lifted structured feature em- bedding. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, pages 4004-4012. 2016.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Contrastive learning with hard negative samples", "authors": [{"first": "J", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "C.-Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. Contrastive learning with hard negative samples. In International Conference on Learning Representations. 2020.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Debiased contrastive learning", "authors": [{"first": "C.-Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y.-C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "8765--8775", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, et al. Debiased contrastive learning. Advances in Neural Information Processing Systems, 33:8765-8775, 2020.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Fashion-MNIST: a Novel Image Dataset for Benchmarking Machine Learning Algorithms", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1708.07747"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Fashion-MNIST: a Novel Image Dataset for Benchmarking Machine Learning Algorithms. arXiv preprint arXiv:1708.07747, 2017.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Columbia Object Image Library", "authors": [{"first": "S", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["K"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1996, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>. Columbia Object Image Library (coil-20). Tech. rep., Technical Report CUCS-005-96, 1996.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Columbia Object Image Library (coil-100)", "authors": [], "year": 1996, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "-. Columbia Object Image Library (coil-100). Tech. rep., Technical Report CUCS-006-96, 1996.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Multiplexed droplet single-cell RNA-sequencing using natural genetic variation", "authors": [{"first": "H", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Subramaniam", "suffix": ""}, {"first": "S", "middle": [], "last": "Targ", "suffix": ""}], "year": 2018, "venue": "Nature Biotechnology", "volume": "36", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Multiplexed droplet single-cell RNA-sequencing using natural genetic variation. Nature Biotechnology, 36(1):89, 2018.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Integrated single-cell analysis of multicellular immune dynamics during hyperacute HIV-1 infection", "authors": [{"first": "S", "middle": ["W"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["M"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Nature Medicine", "volume": "26", "issue": "4", "pages": "511--518", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, et al. Integrated single-cell analysis of multicellular immune dynamics during hyperacute HIV-1 infection. Nature Medicine, 26(4):511-518, 2020.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "A single-cell transcriptome atlas of the human pancreas", "authors": [{"first": "M", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Cell Systems", "volume": "3", "issue": "4", "pages": "385--394", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. A single-cell transcriptome atlas of the human pancreas. Cell Systems, 3(4):385-394, 2016.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Comprehensive integration of single-cell data", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Cell", "volume": "177", "issue": "7", "pages": "1888--1902", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Comprehensive integration of single-cell data. Cell, 177(7):1888-1902, 2019.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Learning to Filter Netnews", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Newsweeder", "suffix": ""}], "year": 1995, "venue": "Proceedings of the International Conference on Machine Learning", "volume": "", "issue": "", "pages": "331--339", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>: Learning to Filter Netnews. In Proceedings of the International Conference on Machine Learning, pages 331-339. 1995.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Feature selection and dimension reduction for single-cell rna-seq based on a multinomial model", "authors": [{"first": "F", "middle": ["W"], "last": "Townes", "suffix": ""}, {"first": "S", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Genome Biology", "volume": "20", "issue": "1", "pages": "1--16", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, et al. Feature selection and dimension reduction for single-cell rna-seq based on a multinomial model. Genome Biology, 20(1):1-16, 2019.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "<PERSON><PERSON><PERSON><PERSON> primigenius (blumbach)", "authors": [], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "The Smithsonian Institute. Mammuthus primigenius (blumbach). https://3d.si.edu/object/3d/mammuthus-primigenius-blumbach: 341c96cd-f967-4540-8ed1-d3fc56d31f12, 2020.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Understanding UMAP", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Understanding UMAP. https://pair-code.github.io/ understanding-umap/, 2019.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Towards a comprehensive evaluation of dimension reduction methods for transcriptomic data visualization", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Communications Biology", "volume": "5", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Towards a comprehensive evaluation of dimension reduction methods for transcriptomic data visualization. Communications Biology, 5(1):719, 2022.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Geometric autoencoders-what you see is what you decode", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": ["A"], "last": "Hamprecht", "suffix": ""}], "year": 2023, "venue": "Proceedings of International Conference on Machine Learning", "volume": "", "issue": "", "pages": "25834--25857", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>. Geometric autoencoders-what you see is what you decode. In Proceedings of International Conference on Machine Learning, pages 25834-25857. PMLR, 2023.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Joint Embedding Self-supervised Learning in the Kernel Regime", "authors": [{"first": "B", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2209.14884"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Joint Embedding Self-supervised Learning in the Kernel Regime. arXiv preprint arXiv:2209.14884, 2022.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Contrastive and Non-Contrastive Self-Supervised Learning Recover Global and Local Spectral Embedding methods", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Lecun", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "26671--26685", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Contrastive and Non-Contrastive Self-Supervised Learning Recover Global and Local Spectral Embedding methods. Advances in Neural Information Processing Systems, 35:26671-26685, 2022.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "The art of using t-SNE for single-cell transcriptomics", "authors": [{"first": "D", "middle": [], "last": "Kobak", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Nature Communication", "volume": "10", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. The art of using t-SNE for single-cell transcriptomics. Nature Communi- cation, 10:5416, 2019.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "TriMAP: Large-scale Dimensionality Reduction Using Triplets. arXiv e-prints", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["K"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1910.00204"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>. TriMAP: Large-scale Dimensionality Reduction Using Triplets. arXiv e-prints, arXiv:1910.00204, 2019.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "On lines and planes of closest fit to systems of points in space", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1901, "venue": "Philosophical Magazine", "volume": "2", "issue": "11", "pages": "559--572", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> On lines and planes of closest fit to systems of points in space. Philosophical Magazine, 2(11):559-572, 1901.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Multidimensional scaling: I Theory and method", "authors": [{"first": "W", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1952, "venue": "Psychometrika", "volume": "17", "issue": "4", "pages": "401--419", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> scaling: I Theory and method. Psychometrika, 17(4):401-419, 1952.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Learning the parts of objects by non-negative matrix factorization", "authors": [{"first": "D", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": ["S"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1999, "venue": "Nature", "volume": "401", "issue": "6755", "pages": "788--791", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON> <PERSON><PERSON>. Learning the parts of objects by non-negative matrix factorization. Nature, 401(6755):788-791, 1999.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "A Global Geometric Framework for Nonlinear Dimensionality Reduction", "authors": [{"first": "J", "middle": ["B"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["C"], "last": "Langford", "suffix": ""}], "year": 2000, "venue": "Science", "volume": "290", "issue": "5500", "pages": "2319--2323", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>. A Global Geometric Framework for Nonlinear Dimensionality Reduction. Science, 290(5500):2319-2323, 2000.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Nonlinear Dimensionality Reduction by Locally Linear Embedding", "authors": [{"first": "S", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": ["K"], "last": "<PERSON>", "suffix": ""}], "year": 2000, "venue": "Science", "volume": "290", "issue": "5500", "pages": "2323--2326", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>. Nonlinear Dimensionality Reduction by Locally Linear Embedding. Science, 290(5500):2323-2326, 2000.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Laplacian Eigenmaps and Spectral Techniques for Embedding and Clustering", "authors": [{"first": "M", "middle": [], "last": "Belkin", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2001, "venue": "Advances in Neural Information Processing Systems", "volume": "14", "issue": "", "pages": "585--591", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Laplacian Eigenmaps and Spectral Techniques for Embedding and Clustering. In Advances in Neural Information Processing Systems, vol. 14, pages 585-591. MIT Press, 2001.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Unsupervised Deep Embedding for Clustering Analysis", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Proceedings of International Conference on Machine Learning", "volume": "", "issue": "", "pages": "478--487", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Unsupervised Deep Embedding for Clustering Analysis. In Proceedings of International Conference on Machine Learning, pages 478-487. PMLR, 2016.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Parametric nonlinear dimensionality reduction using kernel t-SNE", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Hammer", "suffix": ""}], "year": 2015, "venue": "Neurocomputing", "volume": "147", "issue": "", "pages": "71--82", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Parametric nonlinear dimensionality reduction using kernel t-SNE. Neurocomputing, 147:71-82, 2015.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Topological Autoencoders", "authors": [{"first": "M", "middle": [], "last": "Moor", "suffix": ""}, {"first": "M", "middle": [], "last": "Horn", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "7045--7054", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Topological Autoencoders. In International Conference on Machine Learning, pages 7045-7054. PMLR, 2020.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "How to use t-SNE effectively", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Viégas", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2016, "venue": "Distill", "volume": "1", "issue": "10", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. How to use t-SNE effectively. Distill, 1(10):e2, 2016.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Automated optimized parameters for tdistributed stochastic neighbor embedding improve visualization and analysis of large datasets", "authors": [{"first": "A", "middle": ["C"], "last": "Belkina", "suffix": ""}, {"first": "C", "middle": ["O"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Nature Communications", "volume": "10", "issue": "5415", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, et al. Automated optimized parameters for t- distributed stochastic neighbor embedding improve visualization and analysis of large datasets. Nature Communications, 10(5415), 2019.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Automatic selection of t-SNE perplexity", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1708.03229"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Automatic selection of t-SNE perplexity. arXiv preprint arXiv:1708.03229, 2017.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Deep Residual Learning for Image Recognition", "authors": [{"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ren", "suffix": ""}], "year": 2016, "venue": "Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "770--778", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Deep Residual Learning for Image Recognition. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, pages 770-778. 2016.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Adam: A Method for Stochastic Optimization", "authors": [{"first": "D", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ba", "suffix": ""}], "year": 2015, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>: A Method for Stochastic Optimization. In International Conference on Learning Representations. 2015.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "A database for handwritten text recognition research", "authors": [{"first": "J", "middle": ["J"], "last": "Hull", "suffix": ""}], "year": 1994, "venue": "IEEE Transactions on Pattern Analysis and Machine Intelligence", "volume": "16", "issue": "5", "pages": "550--554", "other_ids": {}, "num": null, "urls": [], "raw_text": "Hull, J. J. A database for handwritten text recognition research. IEEE Transactions on Pattern Analysis and Machine Intelligence, 16(5):550-554, 1994.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Scikit-learn: Machine learning in Python", "authors": [{"first": "F", "middle": [], "last": "Pedregosa", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Gramfort", "suffix": ""}], "year": 2011, "venue": "Journal of Machine Learning Research", "volume": "12", "issue": "", "pages": "2825--2830", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Scikit-learn: Machine learning in Python. Journal of Machine Learning Research, 12:2825-2830, 2011.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Massively parallel digital transcriptional profiling of single cells", "authors": [{"first": "G", "middle": ["X"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Belgrader", "suffix": ""}], "year": 2017, "venue": "Nature Communications", "volume": "8", "issue": "1", "pages": "1--12", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, et al. Massively parallel digital transcriptional profiling of single cells. Nature Communications, 8(1):1-12, 2017.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "text": "Figure 2: Embeddings of the MNIST [15] dataset generated by various DR methods with different numbers of hidden layers: 0 (Linear), 1, 2, or 3, or non-parametric variant. See Section 5.1 for details of SVM Acc. It is helpful to envision these images in black and white (without labels) to see when clusters would be difficult to visually separate. More datasets/methods can be found in App. C.", "fig_num": "2", "uris": null, "num": null}, "FIGREF1": {"type_str": "figure", "text": "Figure 3: The low-dimensional scaled distance distribution between various types of point pairs with labels \"3\" and \"8\" in the embedding of the MNIST digit dataset [15], generated by PaCMAP, ParamPaCMAP, and ParamRepulsor (other methods in App. H.1.) See definitions in Sec. 2 & 4.", "fig_num": null, "uris": null, "num": null}, "FIGREF2": {"type_str": "figure", "text": "Sample n N B -nearest neighbors, n M N mid-near points. 4: end for 5: for epoch ← 1 to n epochs do 6:", "fig_num": "3", "uris": null, "num": null}, "FIGREF3": {"type_str": "figure", "text": "Figure 4: Effect of Hard Negative Mining on MNIST. We progressively increase the coefficient of the repulsive force applied to MN hard negatives. Close clusters are circled. Results indicate that Hard Negative Mining alone effectively preserves local structure while maintaining relative proximities. Besides adopting Hard Negative Mining, we made other technical improvements to further enhance repulsive forces. More details can be found in Appendix F.", "fig_num": "4", "uris": null, "num": null}, "FIGREF4": {"type_str": "figure", "text": "Figure 5: Effect of the number of layers on the MNIST dataset. As a supplement to Fig. 2, we extend the number of layers beyond three for Into-NC-t-SNE, UMAP and PaCMAP. Here, the local metric represents 10-NN accuracy, while the global metric denotes the random triplet preservation. Results show that further increasing the number of layers beyond increasing the number of layers beyond three yields only diminishing and negligible improvements in local structure on all three methods.", "fig_num": "5", "uris": null, "num": null}, "FIGREF5": {"type_str": "figure", "text": "Figure 6: Impact of varying the number of nearest neighbors during NN-graph construction on the MNIST dataset. As in Fig. 5, we evaluate the embeddings using 10-NN accuracy and random triplet preservation to assess local and global structure retention, respectively. The results indicate that, unlike non-parametric algorithms, altering the number of nearest neighbors has minimal effect on the embedding's quality, except for Info-t-SNE, which exhibits structural distortion when NN = 60.", "fig_num": "6", "uris": null, "num": null}, "FIGREF6": {"type_str": "figure", "text": "Fig. 6 illustrates the effect of varying the number of nearest neighbors (NN) in P-ItSNE, P-UMAP, and P-PaCMAP. Adjusting the number of NNs during NN-graph construction is commonly regarded as a key mechanism for controlling the local-global structure trade-off in nonparametric DR algorithms [58-60]. Nevertheless, in the parametric setting, modifying the number of NNs had minimal influence on the resulting embeddings. Notably, increasing the number of NNs beyond the typical range (e.g., to 60) can severely disrupt the structure, as observed in the P-ItSNE case.", "fig_num": "6", "uris": null, "num": null}, "FIGREF7": {"type_str": "figure", "text": "Figure 7: All dimensionality reduction results of ParamRepulsor.", "fig_num": "7", "uris": null, "num": null}, "FIGREF8": {"type_str": "figure", "text": "Figure 8: All dimensionality reduction results of ParamUMAP.", "fig_num": "8", "uris": null, "num": null}, "FIGREF9": {"type_str": "figure", "text": "Figure 9: All dimensionality reduction results of ParamInfo-NC-t-SNE.", "fig_num": "9", "uris": null, "num": null}, "FIGREF10": {"type_str": "figure", "text": "Figure 10: All dimensionality reduction results of ParamPaCMAP.", "fig_num": "10", "uris": null, "num": null}, "FIGREF11": {"type_str": "figure", "text": "Figure 11: All dimensionality reduction results of ParamNCVis.", "fig_num": "11", "uris": null, "num": null}, "FIGREF12": {"type_str": "figure", "text": "Figure 12: All dimensionality reduction results of ParamNeg-t-SNE.", "fig_num": "12", "uris": null, "num": null}, "FIGREF13": {"type_str": "figure", "text": "Figure 13: All dimensionality reduction results of ParamPaCMAP with a linear projector.", "fig_num": "13", "uris": null, "num": null}, "FIGREF14": {"type_str": "figure", "text": "Figure 14: All dimensionality reduction results of ParamPaCMAP with 1 hidden layer.", "fig_num": "14", "uris": null, "num": null}, "FIGREF15": {"type_str": "figure", "text": "Figure 15: All dimensionality reduction results of ParamPaCMAP with 2 hidden layers.", "fig_num": "15", "uris": null, "num": null}, "FIGREF16": {"type_str": "figure", "text": "Figure 16: All dimensionality reduction results of nonparametric UMAP.", "fig_num": "16", "uris": null, "num": null}, "FIGREF17": {"type_str": "figure", "text": "Figure 17: All dimensionality reduction results of nonparametric PaCMAP.", "fig_num": "17", "uris": null, "num": null}, "FIGREF18": {"type_str": "figure", "text": "Figure 18: All dimensionality reduction results of nonparametric Info-NC-t-SNE.", "fig_num": "18", "uris": null, "num": null}, "FIGREF19": {"type_str": "figure", "text": "Figure 19: All dimensionality reduction results of nonparametric NCVis.", "fig_num": "19", "uris": null, "num": null}, "FIGREF20": {"type_str": "figure", "text": "Figure 20: All dimensionality reduction results of nonparametric Neg-t-SNE.", "fig_num": "20", "uris": null, "num": null}, "FIGREF21": {"type_str": "figure", "text": "Figure 21: Expectation of the number of false negatives generated by Uniform Sampling and Mid-near Hard Negative Sampling on different dataset sizes.", "fig_num": "21", "uris": null, "num": null}, "FIGREF22": {"type_str": "figure", "text": "closest point as the j-th mid-near point 7: end for 8: end for 9: for i ← 1 to n epochs do 10: for j ← 1 to n batches do 11: Sample x = x 1 . . . , x b from training data.", "fig_num": null, "uris": null, "num": null}, "FIGREF23": {"type_str": "figure", "text": "Figure22: The low-dimensional scaled distance distribution between various types of point pairs with labels \"3\" and \"8\" in the embedding of the MNIST digit dataset[15], generated by Info-NC-t-SNE, NCVis, UMAP, PaCMAP, and their parametric counterpart. See definition in Sec.2 & 4.", "fig_num": "22", "uris": null, "num": null}, "FIGREF24": {"type_str": "figure", "text": "Figure 23: Time consumed by parametric DR methods compared to the size of the dataset. Para-mUMAP cannot finish the Cao et. al dataset under the time constraint of 6 hours.", "fig_num": "23", "uris": null, "num": null}, "TABREF1": {"type_str": "table", "text": "10-NN Accuracy of DR methods measured on various datasets. The absence of values indicate the method failed to produce a valid embedding.", "content": "<table><tr><td>METHOD</td><td colspan=\"7\">P-UMAP P-ITSNE P-NTSNE P-NCVIS GEOAE P-PACMAP P-REP</td></tr><tr><td>MNIST</td><td>0.965</td><td>0.830</td><td>0.862</td><td>0.829</td><td>0.791</td><td>0.968</td><td>0.969</td></tr><tr><td>F-MNIST</td><td>0.733</td><td>0.714</td><td>0.714</td><td>0.626</td><td>0.718</td><td>0.744</td><td>0.778</td></tr><tr><td>USPS</td><td>0.957</td><td>0.939</td><td>0.940</td><td>0.938</td><td>0.846</td><td>0.960</td><td>0.957</td></tr><tr><td>COIL-20</td><td>0.843</td><td>-</td><td>-</td><td>-</td><td>0.724</td><td>0.853</td><td>0.887</td></tr><tr><td>COIL-100</td><td>0.145</td><td>-</td><td>-</td><td>-</td><td>0.611</td><td>0.896</td><td>0.928</td></tr><tr><td>20NG</td><td>0.505</td><td>0.340</td><td>0.401</td><td>0.442</td><td>0.061</td><td>0.437</td><td>0.460</td></tr><tr><td>KANG</td><td>0.954</td><td>0.956</td><td>0.956</td><td>0.955</td><td>0.468</td><td>0.960</td><td>0.961</td></tr><tr><td>KAZER</td><td>0.939</td><td>0.937</td><td>0.937</td><td>0.937</td><td>0.700</td><td>0.940</td><td>0.939</td></tr><tr><td>MURARO</td><td>0.960</td><td>0.961</td><td>0.961</td><td>0.961</td><td>0.565</td><td>0.961</td><td>0.962</td></tr><tr><td>STUART</td><td>0.851</td><td>0.854</td><td>0.853</td><td>0.854</td><td>0.394</td><td>0.855</td><td>0.856</td></tr><tr><td>CIRCLE</td><td>0.901</td><td>0.900</td><td>0.904</td><td>0.911</td><td>0.898</td><td>0.904</td><td>0.895</td></tr><tr><td>MAMMOTH</td><td>0.934</td><td>0.916</td><td>0.914</td><td>0.915</td><td>0.962</td><td>0.915</td><td>0.938</td></tr><tr><td>LINEAGE</td><td>1.000</td><td>1.000</td><td>1.000</td><td>1.000</td><td>1.000</td><td>1.000</td><td>1.000</td></tr><tr><td>HIERARCHY</td><td>1.000</td><td>1.000</td><td>1.000</td><td>1.000</td><td>0.976</td><td>1.000</td><td>1.000</td></tr></table>", "html": null, "num": null}, "TABREF3": {"type_str": "table", "text": "SVM Accuracy of DR methods measured on various datasets. The absence of values indicates that the method failed to produce a valid embedding. Preservation of Triplets Preservation of global structure also involves the preservation of distances. Particularly, we would like the relative relationship of distances to be preserved: if point A and B are closer than A and C in the high dimensional space, they should be closer in the embedding as well. Following[13], we evaluate each method's ability to preserve distance relationships between randomly sampled triplets. The result is in Table5in App. H.3.", "content": "<table><tr><td>METHOD</td><td colspan=\"7\">P-UMAP P-ITSNE P-NTSNE P-NCVIS GEOAE P-PACMAP P-REP</td></tr><tr><td>MNIST</td><td>0.964</td><td>0.836</td><td>0.865</td><td>0.836</td><td>0.787</td><td>0.966</td><td>0.968</td></tr><tr><td>F-MNIST</td><td>0.725</td><td>0.716</td><td>0.716</td><td>0.640</td><td>0.714</td><td>0.719</td><td>0.749</td></tr><tr><td>USPS</td><td>0.953</td><td>0.931</td><td>0.934</td><td>0.933</td><td>0.835</td><td>0.948</td><td>0.955</td></tr><tr><td>COIL-20</td><td>0.813</td><td>-</td><td>-</td><td>-</td><td>0.661</td><td>0.822</td><td>0.856</td></tr><tr><td>COIL-100</td><td>0.237</td><td>-</td><td>-</td><td>-</td><td>0.493</td><td>0.825</td><td>0.862</td></tr><tr><td>20NG</td><td>0.462</td><td>0.355</td><td>0.384</td><td>0.416</td><td>0.065</td><td>0.419</td><td>0.457</td></tr><tr><td>KANG</td><td>0.931</td><td>0.936</td><td>0.936</td><td>0.932</td><td>0.482</td><td>0.947</td><td>0.955</td></tr><tr><td>KAZER</td><td>0.938</td><td>0.935</td><td>0.935</td><td>0.936</td><td>0.758</td><td>0.930</td><td>0.935</td></tr><tr><td>MURARO</td><td>0.955</td><td>0.961</td><td>0.961</td><td>0.957</td><td>0.589</td><td>0.960</td><td>0.961</td></tr><tr><td>STUART</td><td>0.768</td><td>0.832</td><td>0.834</td><td>0.832</td><td>0.425</td><td>0.789</td><td>0.832</td></tr><tr><td>CIRCLE</td><td>0.899</td><td>0.904</td><td>0.902</td><td>0.910</td><td>0.894</td><td>0.905</td><td>0.894</td></tr><tr><td>MAMMOTH</td><td>0.902</td><td>0.886</td><td>0.886</td><td>0.891</td><td>0.936</td><td>0.887</td><td>0.895</td></tr><tr><td>LINEAGE</td><td>1.000</td><td>0.999</td><td>0.999</td><td>1.000</td><td>1.000</td><td>1.000</td><td>1.000</td></tr><tr><td>HIERARCHY</td><td>0.345</td><td>0.424</td><td>0.424</td><td>0.357</td><td>0.200</td><td>0.555</td><td>0.622</td></tr><tr><td colspan=\"4\">Additional Global Structure Preservation:</td><td/><td/><td/><td/></tr></table>", "html": null, "num": null}, "TABREF4": {"type_str": "table", "text": "Ratio of 30-NN kept by the embedding measured on various datasets.", "content": "<table><tr><td>METHOD</td><td colspan=\"7\">P-UMAP P-ITSNE P-NTSNE P-NCVIS GEOAE P-PACMAP P-REP</td></tr><tr><td>MNIST</td><td>0.084</td><td>0.055</td><td>0.071</td><td>0.038</td><td>0.074</td><td>0.090</td><td>0.106</td></tr><tr><td>F-MNIST</td><td>0.088</td><td>0.079</td><td>0.044</td><td>0.043</td><td>0.081</td><td>0.097</td><td>0.121</td></tr><tr><td>USPS</td><td>0.317</td><td>0.313</td><td>0.286</td><td>0.298</td><td>0.224</td><td>0.280</td><td>0.306</td></tr><tr><td>COIL-20</td><td>0.710</td><td>-</td><td>-</td><td>-</td><td>0.490</td><td>0.701</td><td>0.713</td></tr><tr><td>COIL-100</td><td>0.067</td><td>-</td><td>-</td><td>-</td><td>0.348</td><td>0.523</td><td>0.593</td></tr><tr><td>20NG</td><td>0.220</td><td>0.156</td><td>0.180</td><td>0.192</td><td>0.003</td><td>0.140</td><td>0.105</td></tr><tr><td>KANG</td><td>0.100</td><td>0.113</td><td>0.100</td><td>0.111</td><td>0.008</td><td>0.094</td><td>0.121</td></tr><tr><td>KAZER</td><td>0.051</td><td>0.059</td><td>0.050</td><td>0.057</td><td>0.004</td><td>0.047</td><td>0.065</td></tr><tr><td>MURARO</td><td>0.393</td><td>0.416</td><td>0.368</td><td>0.321</td><td>0.062</td><td>0.387</td><td>0.429</td></tr><tr><td>STUART</td><td>0.081</td><td>0.099</td><td>0.086</td><td>0.098</td><td>0.005</td><td>0.083</td><td>0.099</td></tr><tr><td>CIRCLE</td><td>0.901</td><td>0.896</td><td>0.899</td><td>0.896</td><td>0.895</td><td>0.898</td><td>0.901</td></tr><tr><td>MAMMOTH</td><td>0.559</td><td>0.559</td><td>0.552</td><td>0.555</td><td>0.593</td><td>0.545</td><td>0.571</td></tr><tr><td>LINEAGE</td><td>0.077</td><td>0.095</td><td>0.076</td><td>0.094</td><td>0.076</td><td>0.076</td><td>0.095</td></tr><tr><td>HIERARCHY</td><td>0.367</td><td>0.370</td><td>0.364</td><td>0.367</td><td>0.352</td><td>0.362</td><td>0.365</td></tr></table>", "html": null, "num": null}, "TABREF5": {"type_str": "table", "text": "Centroid Distance Correlation of DR methods measured on various datasets. The absence of values indicates that the method failed to produce a valid embedding. The highest values are displayed in bold. Values that has no significant difference from the highest (measured by an independent t-test) are shown in italics.", "content": "<table><tr><td>METHOD</td><td colspan=\"7\">P-UMAP P-ITSNE P-NTSNE P-NCVIS GEOAE P-PACMAP P-REP</td></tr><tr><td>MNIST</td><td>0.707</td><td>0.697</td><td>0.705</td><td>0.663</td><td>0.759</td><td>0.784</td><td>0.732</td></tr><tr><td>F-MNIST</td><td>0.920</td><td>0.897</td><td>0.897</td><td>0.894</td><td>0.864</td><td>0.922</td><td>0.907</td></tr><tr><td>USPS</td><td>0.911</td><td>0.851</td><td>0.910</td><td>0.917</td><td>0.843</td><td>0.879</td><td>0.816</td></tr><tr><td>COIL-20</td><td>0.538</td><td>-</td><td>-</td><td>-</td><td>0.780</td><td>0.767</td><td>0.824</td></tr><tr><td>COIL-100</td><td>0.607</td><td>-</td><td>-</td><td>-</td><td>0.677</td><td>0.697</td><td>0.760</td></tr><tr><td>20NG</td><td>0.751</td><td>0.811</td><td>0.799</td><td>0.768</td><td>0.319</td><td>0.832</td><td>0.686</td></tr><tr><td>KANG</td><td>0.549</td><td>0.556</td><td>0.556</td><td>0.515</td><td>0.547</td><td>0.521</td><td>0.457</td></tr><tr><td>KAZER</td><td>0.682</td><td>0.647</td><td>0.643</td><td>0.678</td><td>0.554</td><td>0.618</td><td>0.544</td></tr><tr><td>MURARO</td><td>0.754</td><td>0.795</td><td>0.776</td><td>0.755</td><td>0.587</td><td>0.658</td><td>0.576</td></tr><tr><td>STUART</td><td>0.292</td><td>0.391</td><td>0.329</td><td>0.357</td><td>0.295</td><td>0.432</td><td>0.322</td></tr><tr><td>CIRCLE</td><td>0.957</td><td>0.879</td><td>0.898</td><td>0.969</td><td>0.937</td><td>0.918</td><td>0.953</td></tr><tr><td>MAMMOTH</td><td>0.929</td><td>0.986</td><td>0.987</td><td>0.974</td><td>0.908</td><td>0.972</td><td>0.972</td></tr><tr><td>LINEAGE</td><td>1.000</td><td>1.000</td><td>1.000</td><td>1.000</td><td>0.996</td><td>1.000</td><td>1.000</td></tr><tr><td>HIERARCHY</td><td>0.350</td><td>0.464</td><td>0.571</td><td>0.489</td><td>0.647</td><td>0.708</td><td>0.738</td></tr></table>", "html": null, "num": null}, "TABREF6": {"type_str": "table", "text": "Triplet Preservation of DR methods measured on various datasets. The absence of values indicates that the method failed to produce a valid embedding. The highest values are displayed in bold. Values with no statistically significant difference from the highest (as determined by an independent t-test) are highlighted in italics.Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? The answer NA means that paper does not include experiments requiring code.• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).", "content": "<table><tr><td>METHOD</td><td colspan=\"7\">P-UMAP P-ITSNE P-NTSNE P-NCVIS GEOAE P-PACMAP P-REP</td></tr><tr><td>MNIST</td><td>0.600</td><td>0.611</td><td>0.615</td><td>0.588</td><td>0.628</td><td>0.604</td><td>0.605</td></tr><tr><td>F-MNIST</td><td>0.720</td><td>0.738</td><td>0.738</td><td>0.747</td><td>0.789</td><td>0.722</td><td>0.706</td></tr><tr><td>USPS</td><td>0.663</td><td>0.665</td><td>0.669</td><td>0.672</td><td>0.686</td><td>0.651</td><td>0.658</td></tr><tr><td>COIL-20</td><td>0.612</td><td>-</td><td>-</td><td>-</td><td>0.739</td><td>0.678</td><td>0.719</td></tr><tr><td>COIL-100</td><td>0.615</td><td>-</td><td>-</td><td>-</td><td>0.730</td><td>0.687</td><td>0.720</td></tr><tr><td>20NG</td><td>0.655</td><td>0.674</td><td>0.666</td><td>0.658</td><td>0.528</td><td>0.678</td><td>0.607</td></tr><tr><td>KANG</td><td>0.772</td><td>0.746</td><td>0.755</td><td>0.775</td><td>0.638</td><td>0.792</td><td>0.772</td></tr><tr><td>KAZER</td><td>0.768</td><td>0.770</td><td>0.771</td><td>0.774</td><td>0.752</td><td>0.784</td><td>0.761</td></tr><tr><td>MURARO</td><td>0.693</td><td>0.719</td><td>0.717</td><td>0.721</td><td>0.663</td><td>0.763</td><td>0.742</td></tr><tr><td>STUART</td><td>0.628</td><td>0.688</td><td>0.689</td><td>0.660</td><td>0.629</td><td>0.739</td><td>0.713</td></tr><tr><td>CIRCLE</td><td>0.980</td><td>0.900</td><td>0.905</td><td>0.983</td><td>0.945</td><td>0.932</td><td>0.975</td></tr><tr><td>MAMMOTH</td><td>0.878</td><td>0.933</td><td>0.934</td><td>0.915</td><td>0.864</td><td>0.917</td><td>0.905</td></tr><tr><td>LINEAGE</td><td>0.995</td><td>0.995</td><td>0.995</td><td>0.995</td><td>0.991</td><td>0.994</td><td>0.993</td></tr><tr><td>HIERARCHY</td><td>0.625</td><td>0.707</td><td>0.736</td><td>0.674</td><td>0.741</td><td>0.788</td><td>0.789</td></tr></table>", "html": null, "num": null}, "TABREF7": {"type_str": "table", "text": "• It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. 8. Experiments Compute Resources Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments? Answer: [Yes] Justification: We provide compute resource details in our appendix. Guidelines: • The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper). 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines? Answer: [Yes] Justification: Experiments and research conducted conform with the NeurIPS code of ethics. Guidelines: • The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "content": "<table/>", "html": null, "num": null}}}}