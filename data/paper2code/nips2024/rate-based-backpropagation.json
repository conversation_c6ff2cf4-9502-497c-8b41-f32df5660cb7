{"paper_id": "rate-based-backpropagation", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:06:17.473799Z"}, "title": "Advancing Training Efficiency of Deep Spiking Neural Networks through Rate-based Backpropagation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Zhejiang University", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "ZJU-UIUC Institute", "institution": "Zhejiang University", "location": {}}, "email": ""}, {"first": "Gaoang", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "ZJU-UIUC Institute", "institution": "Zhejiang University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>ing", "middle": [], "last": "Li", "suffix": "", "affiliation": {"laboratory": "", "institution": "Zhejiang University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Zhejiang University", "location": {}}, "email": "<EMAIL>"}], "year": "", "venue": null, "identifiers": {}, "abstract": "Recent insights have revealed that rate-coding is a primary form of information representation captured by surrogate-gradient-based Backpropagation Through Time (BPTT) in training deep Spiking Neural Networks (SNNs). Motivated by these findings, we propose rate-based backpropagation, a training strategy specifically designed to exploit rate-based representations to reduce the complexity of BPTT. Our method minimizes reliance on detailed temporal derivatives by focusing on averaged dynamics, streamlining the computational graph to reduce memory and computational demands of SNNs training. We substantiate the rationality of the gradient approximation between BPTT and the proposed method through both theoretical analysis and empirical observations. Comprehensive experiments on CIFAR-10, CIFAR-100, ImageNet, and CIFAR10-DVS validate that our method achieves comparable performance to BPTT counterparts, and surpasses state-of-the-art efficient training techniques. By leveraging the inherent benefits of rate-coding, this work sets the stage for more scalable and efficient SNNs training within resource-constrained environments. Our code is available at https://github.com/Tab-ct/rate-based-backpropagation.", "pdf_parse": {"paper_id": "rate-based-backpropagation", "_pdf_hash": "", "abstract": [{"text": "Recent insights have revealed that rate-coding is a primary form of information representation captured by surrogate-gradient-based Backpropagation Through Time (BPTT) in training deep Spiking Neural Networks (SNNs). Motivated by these findings, we propose rate-based backpropagation, a training strategy specifically designed to exploit rate-based representations to reduce the complexity of BPTT. Our method minimizes reliance on detailed temporal derivatives by focusing on averaged dynamics, streamlining the computational graph to reduce memory and computational demands of SNNs training. We substantiate the rationality of the gradient approximation between BPTT and the proposed method through both theoretical analysis and empirical observations. Comprehensive experiments on CIFAR-10, CIFAR-100, ImageNet, and CIFAR10-DVS validate that our method achieves comparable performance to BPTT counterparts, and surpasses state-of-the-art efficient training techniques. By leveraging the inherent benefits of rate-coding, this work sets the stage for more scalable and efficient SNNs training within resource-constrained environments. Our code is available at https://github.com/Tab-ct/rate-based-backpropagation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Spiking Neural Networks (SNNs) are conceptualized as biologically inspired neural systems, incorporating spiking neurons that closely mimic biological neural dynamics [46, 56] . Unlike Artificial Neural Networks (ANNs) based on continuous data representations, SNNs adopt spike-coding strategies to facilitate data transmission through discrete binary spike trains [52] . The intrinsic binary mechanism eliminates the need for the extensive multiply-accumulate operations typically required for synaptic connectivity [56] , thereby enhancing energy efficiency and inference speed when deployed on neuromorphic hardware systems [1, 10, 54] .", "cite_spans": [{"start": 167, "end": 171, "text": "[46,", "ref_id": "BIBREF45"}, {"start": 172, "end": 175, "text": "56]", "ref_id": "BIBREF55"}, {"start": 365, "end": 369, "text": "[52]", "ref_id": "BIBREF51"}, {"start": 517, "end": 521, "text": "[56]", "ref_id": "BIBREF55"}, {"start": 627, "end": 630, "text": "[1,", "ref_id": "BIBREF0"}, {"start": 631, "end": 634, "text": "10,", "ref_id": "BIBREF9"}, {"start": 635, "end": 638, "text": "54]", "ref_id": "BIBREF53"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "The mainstream training methods for SNNs primarily utilize Backpropagation Through Time (BPTT) with surrogate gradients to overcome non-differentiable spike events, allowing SNNs to achieve comparable results with ANNs counterparts [51, 62, 74] . However, the direct training method necessitates the storage of all temporal activations for backward propagation across the network's depth and duration, leading to high training costs in terms of both computational time and memory demands [43, 86, 35, 77, 76, 47, 13] . To alleviate memory burdens, online training techniques have been developed that partially decouple the time dependencies of backward computations in BPTT [2, 4, 76, 48, 89] . However, online methods still require iterative computations based on the time dimension, increasing training time complexity as the number of timesteps grows.", "cite_spans": [{"start": 232, "end": 236, "text": "[51,", "ref_id": "BIBREF50"}, {"start": 237, "end": 240, "text": "62,", "ref_id": "BIBREF61"}, {"start": 241, "end": 244, "text": "74]", "ref_id": "BIBREF73"}, {"start": 488, "end": 492, "text": "[43,", "ref_id": "BIBREF42"}, {"start": 493, "end": 496, "text": "86,", "ref_id": "BIBREF85"}, {"start": 497, "end": 500, "text": "35,", "ref_id": "BIBREF34"}, {"start": 501, "end": 504, "text": "77,", "ref_id": "BIBREF76"}, {"start": 505, "end": 508, "text": "76,", "ref_id": "BIBREF75"}, {"start": 509, "end": 512, "text": "47,", "ref_id": "BIBREF46"}, {"start": 513, "end": 516, "text": "13]", "ref_id": "BIBREF12"}, {"start": 674, "end": 677, "text": "[2,", "ref_id": "BIBREF1"}, {"start": 678, "end": 680, "text": "4,", "ref_id": "BIBREF3"}, {"start": 681, "end": 684, "text": "76,", "ref_id": "BIBREF75"}, {"start": 685, "end": 688, "text": "48,", "ref_id": "BIBREF47"}, {"start": 689, "end": 692, "text": "89]", "ref_id": "BIBREF88"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Observed across most biological sensory systems, rate coding is a phenomenon where information is encoded through the rate of neuronal spikes, regardless of precise spike timing [52, 64, 23] . Recent explorations into spike representation have demonstrated the significant role of rate coding in enhancing the robustness of SNNs, further confirming its dominant position as the encoding representation in networks [38, 60, 18] . A significant observation has shown that BPTT-trained SNNs on static benchmark exhibit spike representation primarily following the rate-coding manner by highlighting strong similarities in representation between SNNs and their ANN counterparts [44] . A similar conclusion resonated with findings in fields of adversarial attacks, where recent methods significantly benefit from rate-based representations to enhance attack effectiveness [6, 29, 50] .", "cite_spans": [{"start": 178, "end": 182, "text": "[52,", "ref_id": "BIBREF51"}, {"start": 183, "end": 186, "text": "64,", "ref_id": "BIBREF63"}, {"start": 187, "end": 190, "text": "23]", "ref_id": "BIBREF22"}, {"start": 414, "end": 418, "text": "[38,", "ref_id": "BIBREF37"}, {"start": 419, "end": 422, "text": "60,", "ref_id": "BIBREF59"}, {"start": 423, "end": 426, "text": "18]", "ref_id": "BIBREF17"}, {"start": 674, "end": 678, "text": "[44]", "ref_id": "BIBREF43"}, {"start": 867, "end": 870, "text": "[6,", "ref_id": "BIBREF5"}, {"start": 871, "end": 874, "text": "29,", "ref_id": "BIBREF28"}, {"start": 875, "end": 878, "text": "50]", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Motivated by rate coding's status as the most effective and predominant form of representation in SNNs, we posit that targeted training based on rate-based information could offer a high costeffectiveness ratio. We propose to decouple BPTT based on rate-coding approximation and simplify rate-based derivative computations to a single spatial backpropagation. We further provide theoretical analysis and empirical evidence to reveal the rationality of the gradient approximation between BPTT and the proposed method. Experimental results demonstrate that the proposed method achieves performance comparable to BPTT counterparts while significantly reducing memory and computational demands. Comparison results also indicate that the proposed method outperforms state-of-the-art efficient training methods on benchmarks. We expect our work to facilitate more efficient and scalable training for SNNs in resource-constrained environments. Our main contributions are as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• We propose rate-based backpropagation that leverages rate-coded information for efficient training of deep SNNs. This method simplifies the computational graph by decoupling and compressing temporal dependencies, reducing training time and memory requirements.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• Alongside the proposed method, we conduct theoretical analysis and empirical validation to demonstrate its effectiveness in approximating the gradient computations performed by BPTT-based SNNs training.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• We conduct experiments on CIFAR-10, CIFAR-100, CIFAR10-DVS, and ImageNet, demonstrating that our proposed method matches the comparable performance of the BPTT counterpart and achieves state-of-the-art results among efficient SNN training methods.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Training Methods for Deep SNNs. Deep SNNs are trained primarily through two principal strategies:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "(1) conversion methods that establish links between SNNs and ANNs through equivalent closed-form mappings, and (2) direct training from scratch utilizing Backpropagation Through Time (BPTT). Conversion methods develop closed-form formulations for spike representations [39, 67, 72, 88, 73, 47] , enabling seamless transitions of pre-trained ANNs into SNNs and facilitating comparable performance on large-scale datasets [8, 15, 27, 59, 57, 12, 42, 16] . However, the precision of these mappings under ultra-low latency conditions is not consistently reliable, often necessitating extensive time steps to accumulate spikes, which may compromise performance [7, 40, 31, 28, 34] . Direct training methods permit SNNs' performance with extremely low time steps by employing BPTT along with surrogate gradients to compute derivatives of discrete spiking events [51, 62, 74, 22, 81, 87, 85, 43, 66, 69, 13] . The strategy fosters innovation in SNN-specific modules, including optimized neurons, synapses, and network architectures, thereby enhancing performance [25, 21, 20, 17, 79, 83, 24, 80, 61] . Despite the advantages of low latency, direct training imposes substantial memory and time burdens to maintain the backward computational graph [43, 86, 35, 77, 76, 47, 13] .", "cite_spans": [{"start": 269, "end": 273, "text": "[39,", "ref_id": "BIBREF38"}, {"start": 274, "end": 277, "text": "67,", "ref_id": "BIBREF66"}, {"start": 278, "end": 281, "text": "72,", "ref_id": "BIBREF71"}, {"start": 282, "end": 285, "text": "88,", "ref_id": "BIBREF87"}, {"start": 286, "end": 289, "text": "73,", "ref_id": "BIBREF72"}, {"start": 290, "end": 293, "text": "47]", "ref_id": "BIBREF46"}, {"start": 420, "end": 423, "text": "[8,", "ref_id": "BIBREF7"}, {"start": 424, "end": 427, "text": "15,", "ref_id": "BIBREF14"}, {"start": 428, "end": 431, "text": "27,", "ref_id": "BIBREF26"}, {"start": 432, "end": 435, "text": "59,", "ref_id": "BIBREF58"}, {"start": 436, "end": 439, "text": "57,", "ref_id": "BIBREF56"}, {"start": 440, "end": 443, "text": "12,", "ref_id": "BIBREF11"}, {"start": 444, "end": 447, "text": "42,", "ref_id": "BIBREF41"}, {"start": 448, "end": 451, "text": "16]", "ref_id": "BIBREF15"}, {"start": 656, "end": 659, "text": "[7,", "ref_id": "BIBREF6"}, {"start": 660, "end": 663, "text": "40,", "ref_id": "BIBREF39"}, {"start": 664, "end": 667, "text": "31,", "ref_id": "BIBREF30"}, {"start": 668, "end": 671, "text": "28,", "ref_id": "BIBREF27"}, {"start": 672, "end": 675, "text": "34]", "ref_id": "BIBREF33"}, {"start": 856, "end": 860, "text": "[51,", "ref_id": "BIBREF50"}, {"start": 861, "end": 864, "text": "62,", "ref_id": "BIBREF61"}, {"start": 865, "end": 868, "text": "74,", "ref_id": "BIBREF73"}, {"start": 869, "end": 872, "text": "22,", "ref_id": "BIBREF21"}, {"start": 873, "end": 876, "text": "81,", "ref_id": "BIBREF80"}, {"start": 877, "end": 880, "text": "87,", "ref_id": "BIBREF86"}, {"start": 881, "end": 884, "text": "85,", "ref_id": "BIBREF84"}, {"start": 885, "end": 888, "text": "43,", "ref_id": "BIBREF42"}, {"start": 889, "end": 892, "text": "66,", "ref_id": "BIBREF65"}, {"start": 893, "end": 896, "text": "69,", "ref_id": "BIBREF68"}, {"start": 897, "end": 900, "text": "13]", "ref_id": "BIBREF12"}, {"start": 1056, "end": 1060, "text": "[25,", "ref_id": "BIBREF24"}, {"start": 1061, "end": 1064, "text": "21,", "ref_id": "BIBREF20"}, {"start": 1065, "end": 1068, "text": "20,", "ref_id": "BIBREF19"}, {"start": 1069, "end": 1072, "text": "17,", "ref_id": "BIBREF16"}, {"start": 1073, "end": 1076, "text": "79,", "ref_id": "BIBREF78"}, {"start": 1077, "end": 1080, "text": "83,", "ref_id": "BIBREF82"}, {"start": 1081, "end": 1084, "text": "24,", "ref_id": "BIBREF23"}, {"start": 1085, "end": 1088, "text": "80,", "ref_id": "BIBREF79"}, {"start": 1089, "end": 1092, "text": "61]", "ref_id": "BIBREF60"}, {"start": 1239, "end": 1243, "text": "[43,", "ref_id": "BIBREF42"}, {"start": 1244, "end": 1247, "text": "86,", "ref_id": "BIBREF85"}, {"start": 1248, "end": 1251, "text": "35,", "ref_id": "BIBREF34"}, {"start": 1252, "end": 1255, "text": "77,", "ref_id": "BIBREF76"}, {"start": 1256, "end": 1259, "text": "76,", "ref_id": "BIBREF75"}, {"start": 1260, "end": 1263, "text": "47,", "ref_id": "BIBREF46"}, {"start": 1264, "end": 1267, "text": "13]", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "To mitigate training costs associated with direct methods, light training strategies have attracted considerable attention [49, 35, 86, 55, 70] . Several studies have explored the concept of decoupling the forward and backward passes in SNNs, which generally assumes that neuronal dynamics follow deterministic processes and aims to establish closed-form fixed-point equivalences between spike representations and corresponding rate-based activations [72, 73, 77, 47, 68] . Drawing on online training techniques from recurrent neural networks, several studies have adapted the principles of Real-time Recurrent Learning (RTRL) [71] to streamline the online training process for SNNs, aiming to decrease memory demands while preserving biologically plausible online properties of the networks [84, 2, 4, 82, 55, 76, 47, 89] . The online methodologies have proven effective in large-scale tasks [76, 47, 89] . Nevertheless, the significant time costs associated with training methods continue to challenge SNNs' broader application. Spike Coding in SNNs. SNNs transmit information through spike trains [52] , with encoding mechanisms classified into temporal and rate coding. Temporal coding is defined on firing times, employed by several direct trainings [49, 75, 88] and ANN-to-SNN conversions [26, 65] , is noted for its low energy consumption due to sparse spiking. However, temporal coding schemes often require specialized neuron configurations and are generally effective only on simpler datasets [26, 65, 88] .", "cite_spans": [{"start": 123, "end": 127, "text": "[49,", "ref_id": "BIBREF48"}, {"start": 128, "end": 131, "text": "35,", "ref_id": "BIBREF34"}, {"start": 132, "end": 135, "text": "86,", "ref_id": "BIBREF85"}, {"start": 136, "end": 139, "text": "55,", "ref_id": "BIBREF54"}, {"start": 140, "end": 143, "text": "70]", "ref_id": "BIBREF69"}, {"start": 451, "end": 455, "text": "[72,", "ref_id": "BIBREF71"}, {"start": 456, "end": 459, "text": "73,", "ref_id": "BIBREF72"}, {"start": 460, "end": 463, "text": "77,", "ref_id": "BIBREF76"}, {"start": 464, "end": 467, "text": "47,", "ref_id": "BIBREF46"}, {"start": 468, "end": 471, "text": "68]", "ref_id": "BIBREF67"}, {"start": 627, "end": 631, "text": "[71]", "ref_id": "BIBREF70"}, {"start": 792, "end": 796, "text": "[84,", "ref_id": "BIBREF83"}, {"start": 797, "end": 799, "text": "2,", "ref_id": "BIBREF1"}, {"start": 800, "end": 802, "text": "4,", "ref_id": "BIBREF3"}, {"start": 803, "end": 806, "text": "82,", "ref_id": "BIBREF81"}, {"start": 807, "end": 810, "text": "55,", "ref_id": "BIBREF54"}, {"start": 811, "end": 814, "text": "76,", "ref_id": "BIBREF75"}, {"start": 815, "end": 818, "text": "47,", "ref_id": "BIBREF46"}, {"start": 819, "end": 822, "text": "89]", "ref_id": "BIBREF88"}, {"start": 893, "end": 897, "text": "[76,", "ref_id": "BIBREF75"}, {"start": 898, "end": 901, "text": "47,", "ref_id": "BIBREF46"}, {"start": 902, "end": 905, "text": "89]", "ref_id": "BIBREF88"}, {"start": 1100, "end": 1104, "text": "[52]", "ref_id": "BIBREF51"}, {"start": 1255, "end": 1259, "text": "[49,", "ref_id": "BIBREF48"}, {"start": 1260, "end": 1263, "text": "75,", "ref_id": "BIBREF74"}, {"start": 1264, "end": 1267, "text": "88]", "ref_id": "BIBREF87"}, {"start": 1295, "end": 1299, "text": "[26,", "ref_id": "BIBREF25"}, {"start": 1300, "end": 1303, "text": "65]", "ref_id": "BIBREF64"}, {"start": 1503, "end": 1507, "text": "[26,", "ref_id": "BIBREF25"}, {"start": 1508, "end": 1511, "text": "65,", "ref_id": "BIBREF64"}, {"start": 1512, "end": 1515, "text": "88]", "ref_id": "BIBREF87"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Conversely, rate coding is widely adopted across both conversion [12, 15, 16, 27, 36, 57, 59, 78] and direct training approaches [73, 77, 47] , consistently achieving superior performance and facilitating low-latency operations [77, 47] . Moreover, rate coding has demonstrated significant potential in enhancing the robustness of SNNs against adversarial attacks [38, 60, 18] , with attack methods specifically designed to exploit rate-based representations showing promise in surpassing benchmarks for SNNs defense against attacks [6, 30, 50] . By employing representation similarity analysis to compare BPTT-trained SNNs with their ANN counterparts, <PERSON> et al. [44] has indicated that rate coding serves as the primary mode of information representation [44] . Inspired by previous findings, we consider that rate-coded information represents the most effective and predominant form of signal expression in SNNs, and the targeted training based on rate-based spike representations may offer a high cost-effectiveness ratio. Therefore, we propose to decouple BPTT towards rate-based backpropagation with the purpose of enhancing the efficiency of SNNs training.", "cite_spans": [{"start": 65, "end": 69, "text": "[12,", "ref_id": "BIBREF11"}, {"start": 70, "end": 73, "text": "15,", "ref_id": "BIBREF14"}, {"start": 74, "end": 77, "text": "16,", "ref_id": "BIBREF15"}, {"start": 78, "end": 81, "text": "27,", "ref_id": "BIBREF26"}, {"start": 82, "end": 85, "text": "36,", "ref_id": "BIBREF35"}, {"start": 86, "end": 89, "text": "57,", "ref_id": "BIBREF56"}, {"start": 90, "end": 93, "text": "59,", "ref_id": "BIBREF58"}, {"start": 94, "end": 97, "text": "78]", "ref_id": "BIBREF77"}, {"start": 129, "end": 133, "text": "[73,", "ref_id": "BIBREF72"}, {"start": 134, "end": 137, "text": "77,", "ref_id": "BIBREF76"}, {"start": 138, "end": 141, "text": "47]", "ref_id": "BIBREF46"}, {"start": 228, "end": 232, "text": "[77,", "ref_id": "BIBREF76"}, {"start": 233, "end": 236, "text": "47]", "ref_id": "BIBREF46"}, {"start": 364, "end": 368, "text": "[38,", "ref_id": "BIBREF37"}, {"start": 369, "end": 372, "text": "60,", "ref_id": "BIBREF59"}, {"start": 373, "end": 376, "text": "18]", "ref_id": "BIBREF17"}, {"start": 533, "end": 536, "text": "[6,", "ref_id": "BIBREF5"}, {"start": 537, "end": 540, "text": "30,", "ref_id": "BIBREF29"}, {"start": 541, "end": 544, "text": "50]", "ref_id": "BIBREF49"}, {"start": 663, "end": 667, "text": "[44]", "ref_id": "BIBREF43"}, {"start": 756, "end": 760, "text": "[44]", "ref_id": "BIBREF43"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Inspired by the brain's ability to transmit information through discrete spikes, the Leaky Integrateand-Fire (LIF) model serves as the basic building block of SNNs due to its simplicity. For practical implementation of SNNs based on connected spiking neurons, the dynamics of the LIF model are typically rendered in a discrete iterative format:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Spiking Neural Networks", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "u l t = λ(u l t-1 -V th s l t-1 ) + W l s l-1 t , s l t = H(u l t -V th )", "eq_num": "(1)"}], "section": "Spiking Neural Networks", "sec_num": "3.1"}, {"text": "where u l t and s l t represent the membrane potential and output spike of neurons in layer l at time t, respectively. W l denotes the linear synaptic connections between layers l -1 and l, and λ acts as the decay term for the membrane potential. The Heaviside step function, H(•), determines spike generation, ensuring s l t in binary forms. Noting that H(•) is not differentiable, SNNs' direct training employs surrogate gradients to achieve error propagation by creating various pseudo-derivatives [51, 74, 19] , following the basic idea of Straight-Through Estimator (STE) [3] .", "cite_spans": [{"start": 501, "end": 505, "text": "[51,", "ref_id": "BIBREF50"}, {"start": 506, "end": 509, "text": "74,", "ref_id": "BIBREF73"}, {"start": 510, "end": 513, "text": "19]", "ref_id": "BIBREF18"}, {"start": 577, "end": 580, "text": "[3]", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "Spiking Neural Networks", "sec_num": "3.1"}, {"text": "The network outputs at each timestep t are given by o t = W L s L t , where W L denotes the classifier's weights. Classification is based on the average of these outputs across all timesteps, computed as y pred = 1 T T t=1 o t . The loss function L is defined over averaged outputs and is typically formulated as L = ℓ 1 T T t=1 o t , y , where y represents the true labels and ℓ could be the cross-entropy function, as noted in various studies [87, 48, 19, 69] . BPTT unfolds the iterations described in Eq. ( 1), and propagates gradients back along the computational graphs across both temporal and spatial dimensions, as illustrated in Fig. 1a . The gradients of the membrane potential u incorporate elements Incorporating rate-based representation. Under the rate coding assumption, essential information is effectively encapsulated within the spike frequency averages. We start by defining the rate-based representation as an approximation for the forward procedure in SNNs, as shown in Figure 2 . The average firing rate at each layer l, denoted as r l , is calculated as the expected value of the spike outputs s l t over the temporal dimension", "cite_spans": [{"start": 445, "end": 449, "text": "[87,", "ref_id": "BIBREF86"}, {"start": 450, "end": 453, "text": "48,", "ref_id": "BIBREF47"}, {"start": 454, "end": 457, "text": "19,", "ref_id": "BIBREF18"}, {"start": 458, "end": 461, "text": "69]", "ref_id": "BIBREF68"}], "ref_spans": [{"start": 644, "end": 646, "text": "1a", "ref_id": "FIGREF0"}, {"start": 999, "end": 1000, "text": "2", "ref_id": "FIGREF10"}], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "r l = E[s l t ] = 1 T t≤T s l t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "Considering the forward propagation through linear operators with weights W l that compute the inputs as I l t = W l s l-1 t , instead of transmitting distinct spikes over multiple timesteps, we transform the average rates into average inputs c l in the approximate representation:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "c l = E[I l t ] = E[W l s l-1 t ] = W l E[s l-1 t ] = W l r l-1 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "Supposing input representations are well captured within c l , we approximate the exact inputs with the average inputs for all timesteps, I l t ≈ c l , and follow the neuronal dynamics in Eq. ( 1) to derive the output rates r l = E[s l t ]. With the rate-coding approximation in place, we can derive the gradients with respect to the weights in the linear part based on the error propagated through the average inputs:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∇ W l L rate ≡ ∂L ∂c l ∂c l ∂W l = ∂L ∂c l r l-1 ⊤", "eq_num": "(3)"}], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "Handling temporal dependency during backward. For back-propagating the error, the linear parts operate smoothly as ∂c l ∂r l-1 = W l ⊤ . The next step is to define the correlation between the averages of inputs and output spike rates, ∂r l ∂c l , within the neurons of layer l. Since there is no deterministic relationship between r l and c l , we first look into the influence of separated inputs following the exact gradients in Eq. ( 2):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂s l τ ∂I l t = ∂s l τ ∂u l t =        ∂s l τ ∂u l τ t i=τ -1 ∂u l i+1 ∂u l i + ∂u l i+1 ∂s l i ∂s l i ∂u l i if τ ≥ t ∂s l t ∂u l t if τ = t 0 if τ < t", "eq_num": "(4)"}], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "By accumulating the intricate dynamics over time, we can derive the gradients of the overall spikes with respect to the inputs at time t:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "κ κ κ l t = τ ∂s l τ ∂I l t = ∂s l t ∂u l t + τ >t ∂s l τ ∂u l τ t i=τ -1 ∂u l i+1 ∂u l i + ∂u l i+1 ∂s l i ∂s l i ∂u l i", "eq_num": "(5)"}], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "Here, with rate-coding approximating I l t ≈ c l , we follow the idea of Straight-Through Estimator [3] and define the backward gradients as ∂I l t ∂c l = Id, with Id representing the identity matrix. Then, we can derive the surrogate gradients of neural dynamics through the mean estimator:", "cite_spans": [{"start": 100, "end": 103, "text": "[3]", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "∂r l ∂c l rate ≡ τ ∂(E s l t ) ∂I l τ ∂I l τ ∂c l = 1 T t τ ∂s l t ∂I l τ = E κ κ κ l t (6)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "With the compressed gradients of neuron parts, the error backpropagation of the rate-based representation is then determined, dependent only on the spatial domain:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂c l rate = ∂L ∂c L l i=L-1 ∂c i+1 ∂r i ∂r i ∂c i rate = ∂L ∂c L l i=L-1 W i ⊤ E κ κ κ l t", "eq_num": "(7)"}], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "where we define the objective", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "L = 1 T ℓ(E[o t ], y) = 1 T ℓ(c L , y).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "Note that the rate-based representation, while instrumental in constructing the backward computational graph for learning, does not necessitate actual implementation during the forward pass.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training SNNs with BPTT", "sec_num": "3.2"}, {"text": "As previously discussed, rate-based backpropagation can be executed on spatial-dimension computation by decoupling BPTT. We now show how rate-based backpropagation can be efficiently implemented within the overall learning framework. As depicted in Figure 2b , online schemes apply eligibility traces e l t locally within neurons to store historical information, effectively blocking backward access to past gradients. The gradient computation is optimized by compressing all past temporal dependencies into e l t . Similarly, we utilize iterative variables {g l t } l≤L and {e l t } l≤L as the accumulated post-and pre-synaptic dependencies, synchronously recorded during the neural dynamics computations. The iteration of {e l t } l≤L dynamically records the firing rates, where e l t = 1 t ((t -1)e l t-1 + s l t ), and it is straightforward to derive r l = e l T . Considering the surrogate gradients of neural dynamics, ∂r l", "cite_spans": [], "ref_spans": [{"start": 256, "end": 258, "text": "2b", "ref_id": "FIGREF10"}], "eq_spans": [], "section": "Rate-based Gradient Computation for Memory and Time Efficiency", "sec_num": "4.2"}, {"text": "∂c l , to estimate future-dependent terms outlined in Eq. 5, we first construct equivalent eligibility trace forms, {ρ l t } t≤T , with iterative expressions starting at ρ l 1 = 1:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Rate-based Gradient Computation for Memory and Time Efficiency", "sec_num": "4.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ρ l t = 1 + ρ l t-1 ∂u l t ∂u l t-1 + ∂u l t ∂s l t-1 ∂s l t-1 ∂u l t-1 = 1 + τ <t τ i=t-1 ∂u l i+1 ∂u l i + ∂u l i+1 ∂s l i ∂s l i ∂u l i (", "eq_num": "8"}], "section": "Rate-based Gradient Computation for Memory and Time Efficiency", "sec_num": "4.2"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Rate-based Gradient Computation for Memory and Time Efficiency", "sec_num": "4.2"}, {"text": "with the equivalence that:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Rate-based Gradient Computation for Memory and Time Efficiency", "sec_num": "4.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "t κ κ κ l t = t ∂s l t ∂u l t + τ >t ∂s l τ ∂u l τ t i=τ -1 ∂u l i+1 ∂u l i + ∂u l i+1 ∂s l i ∂s l i ∂u l i = t ∂s l t ∂u l t 1 + τ <t τ i=t-1 ∂u l i+1 ∂u l i + ∂u l i+1 ∂s l i ∂s l i ∂u l i = t ∂s l t ∂u l t ρ l t", "eq_num": "(9)"}], "section": "Rate-based Gradient Computation for Memory and Time Efficiency", "sec_num": "4.2"}, {"text": "By iteratively accumulating", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Rate-based Gradient Computation for Memory and Time Efficiency", "sec_num": "4.2"}, {"text": "g l t = 1 t ((t -1)g l t-1 + ∂s l t ∂u l t ρ t ), we obtain g l T = E[ ∂s l t ∂u l t ρ l t ] = E[κ κ κ l t ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Rate-based Gradient Computation for Memory and Time Efficiency", "sec_num": "4.2"}, {"text": ". Now, we have collapsed the required computation graph through the iterative calculation to complexity O(L). The rate-based propagation is then conducted in one go, relying only on the intermediate variables e l T , g l T , and W l , within one-time spatial-dimension backpropagation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Rate-based Gradient Computation for Memory and Time Efficiency", "sec_num": "4.2"}, {"text": "Having derived the fundamental form of rate-based backpropagation through the rate-encoding approximation, we now explore potential divergences with BPTT during error propagation. Although rate-based backpropagation is derived from the approximated forward pass, it still provides valid gradients for the original network parameters.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "The primary divergence between rate-back and BPTT in backward computation primarily arises from the assumptions regarding the approximation of rate-based representation through mean estimators, as outlined in Eq.( 3) and Eq.( 6). The rate-coding motivations establish equivalence with BPTT by assuming temporal components are independent, which is formalized in Theorem 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "Theorem 1. Given δ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "(s l ) t = ∂L ∂s l t", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "that refers to gradients computed following the chain rule of BPTT in Eq. ( 2), and", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "κ l t = τ ∂s l t ∂I l τ (where E κ l t = E κ κ κ l t in Eq.(6-7)) , if E δ (s l ) t κ l t = E δ (s l ) t E κ l t holds for ∀l, we have E δ (s l ) t", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "= ∂L ∂r l rate . Furthermore, given δ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "(I l ) t = ∂L ∂I l t , if E δ (I l ) t s l-1 t = E δ (I l ) t E[s l-1 t ] for ∀l, we then obtain (∇ W l L) rate = 1 T (∇ W l L).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "Here, E [x t ] = 1 T t x t refers the mean value of tensor x t over temporal dimension T .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "To confirm our hypotheses, we carried out empirical experiments, the results of which are detailed in the experimental section. Our empirical findings support the core assumptions outlined in Theorem 1, demonstrating the relative independence between δ (s l ) t and κ l t (Figure 3a ,b), as well as between δ (I l ) t and s l t (Figure 3c ). For minor discrepancies that may arise, we introduced Theorem 2, which tolerates small deviations and confirms that approximation errors in rate-based backpropagation can be effectively bounded, ensuring the robustness of training under practical conditions. Theorem 2. For gradients δ", "cite_spans": [], "ref_spans": [{"start": 280, "end": 282, "text": "3a", "ref_id": "FIGREF9"}, {"start": 336, "end": 338, "text": "3c", "ref_id": "FIGREF9"}], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "(s l ) t = ∂L ∂s l t and κ l t = τ ∂s l t ∂I l τ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": ", given the approximation error bound", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "ϵ > 0 s.t. E δ (s l ) t κ l t -E δ (s l ) t E κ l t ≤ ϵ(1 + E δ (s l ) t", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": ") for ∀l. Denote the stacked tensor", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "I l = [I l 1 , ..., I l T ] and s l = [s l 1 , ..., s l T ].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "Assuming the backward procedure follows non-expansivity s.t.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Connecting Error Backward of Rate-based Backpropagation to BPTT", "sec_num": "4.3"}, {"text": "∂I l = W l+1 ⊤ ∂s l ∂I l is 1-lipschitz continuous without loss of generality and the biases are bounded uniformly by B, i.e. x ∂I l+1 ∂I l -x ∂I l+1 ∂I l ≤ xx for ∀x, x. Define δ l rate = ∂L ∂c l rate as the error propagated through Eq. ( 7), and δ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "∂I l+1", "sec_num": null}, {"text": "(I l ) t = ∂L ∂I l t", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "∂I l+1", "sec_num": null}, {"text": "as the error propagated through BPTT, with", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "∂I l+1", "sec_num": null}, {"text": "δ L rate = E[δ (I L ) t", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "∂I l+1", "sec_num": null}, {"text": "]. We have the gradient difference bounded by δ L-k rate -E[δ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "∂I l+1", "sec_num": null}, {"text": "(I L-k ) t ] = O(k 2 ϵ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "∂I l+1", "sec_num": null}, {"text": "Theorem 2 elucidates the stability of rate-based backpropagation relative to BPTT, showing that the proposed method can provide a bound on the overall objective solution. The bounded error could further be interpreted as a form of randomness suitable for stochastic optimization. The similarity measurement of the descent directions between the two methods provides empirical evidence for the effectiveness of the proposed method (Figure 3d ). Detailed proof is provided in Appendix A. Another aspect of our implementation concerns handling batch normalization (BN), especially given its critical role in BPTT, which adjusts mean and variance statistics during the forward pass. The application of BN varies depending on the training mode. In the multi-step mode, BN benefits from access to information across all timesteps and can normalize based on statistics aggregated over temporal dimensions. We employed tdBN [87] in rate M since it has been widely adopted in direct training on various benchmarks. In contrast, the single-step mode limits BN to current timestep inputs, necessitating normalization across spatial dimensions only. In line with online schemes, SLTT [48] demonstrates the feasibility of implementing spatial BN iteratively across timesteps, an approach we adopt for rate S . Further details on the BN implementation are provided in Appendix B. ", "cite_spans": [{"start": 916, "end": 920, "text": "[87]", "ref_id": "BIBREF86"}, {"start": 1172, "end": 1176, "text": "[48]", "ref_id": "BIBREF47"}], "ref_spans": [{"start": 438, "end": 440, "text": "3d", "ref_id": "FIGREF9"}], "eq_spans": [], "section": "∂I l+1", "sec_num": null}, {"text": "In this section, we conduct experiments on CIFAR-10 [37], CIFAR-100 [37] , ImageNet [11] , and CIFAR10-DVS [41] to evaluate the proposed training method. We implement SNNs training on the Pytorch [53] and SpikingJelly [19] frameworks. We set V th = 1, λ = 0.2, and employ the sigmoid-based surrogate function [19] for LIF neurons. Detailed setups are provided in Appendix C.", "cite_spans": [{"start": 68, "end": 72, "text": "[37]", "ref_id": "BIBREF36"}, {"start": 84, "end": 88, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 107, "end": 111, "text": "[41]", "ref_id": "BIBREF40"}, {"start": 196, "end": 200, "text": "[53]", "ref_id": "BIBREF52"}, {"start": 218, "end": 222, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 309, "end": 313, "text": "[19]", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "Empirical experiments are conducted to support the preconditions of theorems discussed in Section 4.3. These preconditions assert the independence of paired variables across the temporal dimension: E δ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "(s l ) t κ l t = E δ (s l ) t E κ l t (A1) and E δ (I l ) t s l-1 t = E δ (I l ) t E[s l-1 t ] (A2).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "To explore these relationships, we conducted experiments training ResNet-18 on CIFAR-100 using BPTT. Cosine similarity measures were employed to compare the empirical expectation products, cos⟨E δ 3a , where values approaching 1 indicate a high degree of alignment, suggesting that the variables' directions are similar. Additionally, the correlation coefficient, ρ was measured to further assess the independence of these variables ρ = COV(κt,δ", "cite_spans": [], "ref_spans": [{"start": 197, "end": 199, "text": "3a", "ref_id": "FIGREF9"}], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "(s l ) t κ l t , E δ (s l ) t E κ l t ⟩ as shown in Figure", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "(s l ) t ) var(κt)var(δ (s l ) t )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "where COV(κ t , δ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "(s l ) t ) = E δ (s l ) t κ l t -E δ (s l ) t E κ l t", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "It is clear that ρ equals the cosine distance between the variables after centering by their means, ρ = cos⟨δ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "(s l ) t -E[δ (s l ) t ], κ l t -E κ l t ⟩.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "Results, shown in Figure 3b , reveal that the correlation coefficients are constrained within a very small range, typically around the magnitude of ∼ 10 -5 , supporting the hypothesis of their relative independence. We also conducted cosine similarity measurements to validate the assumption", "cite_spans": [], "ref_spans": [{"start": 25, "end": 27, "text": "3b", "ref_id": "FIGREF9"}], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "E δ (I l ) t s l-1 t = E δ (I l ) t E[s l-1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "t ], as shown in Figure 3c . Additionally, we implement both BPTT and the proposed method simultaneously within the same training iteration, allowing direct observation of the gradient descent directions. The relation 3d , which revealed that the convergence directions for rate-based backpropagation and BPTT are closely aligned. Remarkably, all tests consistently demonstrate that configurations with T=6 better adhere to the theoretical assumptions than T=4, suggesting that the proposed method can more closely mimic BPTT computations as the timestep increases. This observation also highlights the intrinsic link between our method and rate-coding, suggesting that a larger temporal window may facilitate more stable manifestations of rate-coding.", "cite_spans": [], "ref_spans": [{"start": 24, "end": 26, "text": "3c", "ref_id": "FIGREF9"}, {"start": 218, "end": 220, "text": "3d", "ref_id": "FIGREF9"}], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "(∇ W l L) rate = 1 T (∇ W l L) (A3) was visualized in Figure", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Empirical Validation", "sec_num": "5.1"}, {"text": "We present comparison results in Table 1 . In single-step mode, rate S offers fair comparisons with online schemes, while rate M in multi-step mode competes fairly with other methods employing one-step backpropagation. Unlike online methods such as OTTT [76] , SLTT [48] , and OS [89] , which necessitate spatial backpropagation at every timestep, our proposed method conducts this process only once at the final timestep. Although methods of DSR [47] and SSF [68] delay decoupled backpropagation until the final timestep, allowing for parallel processing across all timesteps to enhance computational speed, they still require each timestep's backpropagation to be managed independently within the backward computation graph. In contrast, our method fully compresses the temporal dimension, achieving one-step time-independent spatial backpropagation. As shown in Table 1 , our method yields comparable performance with BPTT counterparts on benchmarks, showcasing promising capabilities compared to other efficient training methods. While our theoretical analysis and motivation primarily adhere to rate-coding approximations, the performance on static datasets aligns with expectations. The results on the dynamic dataset CIFAR10-DVS also achieve comparable levels, implying a significant presence of rate-based representation within CIFAR10-DVS. More results regarding the performance comparisons between the proposed method and BPTT across various architectures and settings have been detailed in Appendix D.", "cite_spans": [{"start": 254, "end": 258, "text": "[76]", "ref_id": "BIBREF75"}, {"start": 266, "end": 270, "text": "[48]", "ref_id": "BIBREF47"}, {"start": 280, "end": 284, "text": "[89]", "ref_id": "BIBREF88"}, {"start": 447, "end": 451, "text": "[47]", "ref_id": "BIBREF46"}, {"start": 460, "end": 464, "text": "[68]", "ref_id": "BIBREF67"}], "ref_spans": [{"start": 39, "end": 40, "text": "1", "ref_id": "TABREF0"}, {"start": 871, "end": 872, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Comparison with the State-of-the-Art", "sec_num": "5.2"}, {"text": "we assess the impact of extending timesteps on both accuracy and training efficiency. Figure 4a validates that our method capably manages increased timesteps, thereby confirming the scalability of the proposed method for larger T values. Figure 4b displays the computational and memory expenses incurred during the backward phase, which, as anticipated, do not escalate with increasing T . ", "cite_spans": [], "ref_spans": [{"start": 93, "end": 95, "text": "4a", "ref_id": "FIGREF4"}, {"start": 245, "end": 247, "text": "4b", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Impact of Time Expansion", "sec_num": "5.3"}, {"text": "Our method, derived from the principles of rate-based representation, necessitates examining the impact of rate coding on model behavior. Following an insightful approach from [6] , we assess the robustness of our models by shuffling the temporal order of spike sequences while maintaining their rate consistency. This experiment, designed to disrupt temporal information without changing the firing rate, was applied to models trained using rate-based backpropagation. During inference on the test dataset, we introduced perturbations by randomly shuffling the temporal dimensions of input tensors across all neurons, as reported in Table 2 . Notably, models mostly resisted these changes to some degree, which suggests that they follow the basic rules of rate coding, where the reordering of timesteps does not significantly impact overall accuracy. Furthermore, we tracked the average firing rates across each layer over time, presented in Figure 5 . As layers increase, the average spike rates per layer are closely aligned with the temporal mean, validating the idea of rate-coding approximation.", "cite_spans": [{"start": 176, "end": 179, "text": "[6]", "ref_id": "BIBREF5"}], "ref_spans": [{"start": 640, "end": 641, "text": "2", "ref_id": "TABREF1"}, {"start": 950, "end": 951, "text": "5", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Analysis of Rate Statistics", "sec_num": "5.4"}, {"text": "Those two experiments support the notion that rate-based backpropagation proficiently captures rate-based representations during training. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Analysis of Rate Statistics", "sec_num": "5.4"}, {"text": "In this work, we propose rate-based backpropagation, utilizing rate-coding approximation to streamline the gradient computational graph, significantly reducing both memory usage and training time.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Through theoretical analyses and empirical validation, we show the method's feasibility in approximating the optimization direction of BPTT. Experimental results across benchmarks reveal that our method achieves comparable performance with BPTT and surpasses other state-of-the-art efficient training methods. We expect our work to pave the way for more scalable and resource-efficient training of SNNs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "A Proof of Theorems = ∂L ∂r l rate . Furthermore, given δ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "(I l ) t = ∂L ∂I l t , if E δ (I l ) t s l-1 t = E δ (I l ) t E[s l-1 t ] for ∀l, we then obtain (∇ W l L) rate = 1 T (∇ W l L).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Here, E [x t ] = 1 T t x t refers the mean value of tensor x t over temporal dimension T .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Proof. Given δ , we establish the mean gradients through neural dynamics based on the chain rule:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E ∂L ∂I l t = E τ ∂L ∂s l τ ∂s l τ ∂I l t = 1 T t τ ∂L ∂s l τ ∂s l τ ∂I l t = E[δ (s l ) t κ l t ],", "eq_num": "(10)"}], "section": "Conclusion", "sec_num": "6"}, {"text": "Considering the output layer l = L, the objective for BPTT can be expressed as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "L = ℓ(E[o t ], y) = ℓ(E[W L s L t ], y) = ℓ(W L E[s L t ], y) = ℓ(W L r L , y). Under the rate-based objective L = 1 T ℓ(c L , y) = 1 T ℓ(W L r L-1 , y), it is clear that E[δ (s L-1 ) t ] = ∂L ∂r L-1 rate . Applying the precon- dition E[δ (s L-1 ) t κ L t ] = E[δ (s L-1 ) t ]E[κ L-1 t", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "], we obtain:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂r L-1 ∂r L-1 ∂c L-2 ∂c L-2 ∂r L-2 rate = ∂L ∂r L-1 rate E[κ L-1 t ]W (L-1) ⊤ =E[δ (s L-1 ) t ]E[κ L-1 t ]W (L-1) ⊤ = E[δ (s L-1 ) t κ L-1 t ]W (L-1) ⊤ =E ∂L ∂I L-1 t W (L-1) ⊤ = E ∂L ∂I L-1 t W (L-1) ⊤ =E ∂L ∂s L-2 t = E[δ (s L-2 ) t ],", "eq_num": "(11)"}], "section": "Conclusion", "sec_num": "6"}, {"text": "Continuing this induction process, we can derive that E[δ (s l ) t ] = ∂L ∂r l rate for all layers l. Further, given δ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "(I l ) t = ∂L ∂I l t", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": ", the gradient for the weight matrix under BPTT:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "∇ W l L = t ∂L ∂I l t ∂I l t ∂W l = t δ (I l ) t s l-1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "t . The gradients passing through the linear parts maintain the equivalence:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂c l rate = ∂L ∂r l+1 ∂r l+1 ∂c l rate = ∂L ∂r l+1 W l+1 ⊤ rate = ∂L ∂r l+1 rate W l+1 ⊤ = E[δ (s l+1 ) t W l+1 ⊤ ] = E[δ (I l ) t ].", "eq_num": "(12)"}], "section": "Conclusion", "sec_num": "6"}, {"text": "With the precondition that E[δ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "(I l ) t s l-1 t ] = E[δ (I l ) t ]E[s l-1 t ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "holds for ∀l, we obtain: Save e l T , g l T and W l for backwards, and free intermediate variables. Compute input currents through linear operators I l t = W l s l-1 t ;", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(∇ W l L) rate = ∂L ∂c l ∂c l ∂W l rate = ∂L ∂c l rate r l-1 = E[δ (I l ) t ]E[s l-1 t ] = E[δ (I l ) t s l-1 t ] = 1 T ∇ W l L.", "eq_num": "(13"}], "section": "Conclusion", "sec_num": "6"}, {"text": "19 Initialize ρ l 0 = 0, g l 0 = 0, e l 0 = 0; [48] design for the single-step mode, which computes mean and variance statistics independently at each time step t:", "cite_spans": [{"start": 47, "end": 51, "text": "[48]", "ref_id": "BIBREF47"}], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Ĩt = BN(I t ) = γ I t -µ t σ 2 t + ϵ + β, where µ t = 1 B b I (b) t and σ 2 t = 1 B b (I (b) t -µ t ) 2 . (", "eq_num": "17"}], "section": "Conclusion", "sec_num": "6"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Defining", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "χ (I) t = ∂ Ĩt ∂It , χ (γ) t = ∂ Ĩt ∂γ , χ", "eq_num": "(β) t"}], "section": "Conclusion", "sec_num": "6"}, {"text": "= ∂ Ĩt ∂β , the following expressions are obtained:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "χ (I) t = γ 1 σ 2 t + ϵ + ∂ Ĩl t ∂σ 2 t ∂σ 2 t ∂I l t + ∂ Ĩl t ∂µ t ∂µ t ∂I l t , χ (γ) t = I l t -µ t σ 2 t + ϵ , χ (β) t = Id. (", "eq_num": "18"}], "section": "Conclusion", "sec_num": "6"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "For the backward derivation of BN in a rate-based setting based on mean estimations through time, we implement", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "∂L ∂c = ∂L ∂ c E[χ (I) t ], ∂L ∂γ = ∂L ∂ c E[χ (γ) t ], ∂L ∂β = ∂L ∂ c E[χ (β) t ] = ∂L ∂ c .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Since gradient computation at each timestep is independent, the dynamic estimations of E[χ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "(I) t ] and E[χ (γ)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "t ] are performed in the same manner of {e l t } t≤T and {g l t } t≤T . In the multi-step mode, tdBN [87] accounts for mean and variance statistics over the entire time horizon:", "cite_spans": [{"start": 101, "end": 105, "text": "[87]", "ref_id": "BIBREF86"}], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Ĩt = BN(I t ) = γ I t -µ √ σ 2 + ϵ + β, where µ = 1 BT t b I (b) t , σ 2 = 1 BT t b (I (b) t -µ) 2 . (", "eq_num": "19"}], "section": "Conclusion", "sec_num": "6"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "The rate-based representation integrates the input across the time dimension, with the mean µ c = b c (b) , and variance", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "σ 2 c = 1 B b (c (b) -µ c ) 2 . Since c is the temporal mean of inputs, it is clear that µ c = µ and σ 2 c ≤ σ 2 . Note that ∂σ 2 ∂It = 1 BT t b (I (b) t -µ) = 1 B b (c (b) -µ c ) = ∂σ 2 c", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "∂c . Assuming ∂It ∂c = Id, we derive ∂µ ∂It = ∂µc ∂c . For the forward approximation specifically tailored for tdBN in rate-based backpropagation, we define:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "c = BN(c) = γ c -µ σ2 c + ϵ + β,", "eq_num": "(20)"}], "section": "Conclusion", "sec_num": "6"}, {"text": "where γ and β refer to the same intrinsic parameters shared with BN(I t ), and σ2 c is defined distinctly in forward and backward passes: σ2 c = σ 2 in forward and ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "C.1 Datasets CIFAR-10 and CIFAR-100. The CIFAR-10 and CIFAR-100 [37] datasets contain 32x32 color images across different classes, licensed under MIT. CIFAR-10 includes 60,000 images across 10 classes, with 50,000 for training and 10,000 for testing, whereas CIFAR-100 is spread over 100 classes. Both datasets have been normalized for zero mean and unit variance. Image data augmentation is applied using AutoAugment [9] and Cutout [14] strategies, similar to the implementations in recent studies [42, 7, 24, 69, 13] . The pixel values are directly fed into the input layer at each timestep as direct encoding [55] .", "cite_spans": [{"start": 64, "end": 68, "text": "[37]", "ref_id": "BIBREF36"}, {"start": 418, "end": 421, "text": "[9]", "ref_id": "BIBREF8"}, {"start": 433, "end": 437, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 499, "end": 503, "text": "[42,", "ref_id": "BIBREF41"}, {"start": 504, "end": 506, "text": "7,", "ref_id": "BIBREF6"}, {"start": 507, "end": 510, "text": "24,", "ref_id": "BIBREF23"}, {"start": 511, "end": 514, "text": "69,", "ref_id": "BIBREF68"}, {"start": 515, "end": 518, "text": "13]", "ref_id": "BIBREF12"}, {"start": 612, "end": 616, "text": "[55]", "ref_id": "BIBREF54"}], "ref_spans": [], "eq_spans": [], "section": "C Experimental Settings", "sec_num": null}, {"text": "ImageNet. The ImageNet-1K dataset [11] comprises 1,281,167 training images and 50,000 validation images distributed across 1,000 classes, licensed for non-commercial use. ImageNet-1K images are normalized for zero mean and unit variance. Training images undergo random resized cropping to 224x224 pixels and horizontal flipping, while validation images are resized to 256x256 and then center-cropped to 224x224. The images are transformed into time sequences through direct encoding [55] , following the approach used for CIFAR datasets. CIFAR10-DVS. The CIFAR10-DVS dataset [41] is a neuromorphic version of CIFAR-10, which includes 10,000 event-based images captured by the DVS camera with pixel dimensions expanded to 128×128, licensed under CC BY 4.0. We split the whole dataset into 9000 training images and 1000 testing images. Data preprocessing involves integrating events into frames [21, 19] and reducing the spatial resolution to 48x48 through interpolation. Additional data augmentation includes random horizontal flips and random rolls within a 5-pixel range, mirroring previous methods [76, 48] .", "cite_spans": [{"start": 34, "end": 38, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 483, "end": 487, "text": "[55]", "ref_id": "BIBREF54"}, {"start": 575, "end": 579, "text": "[41]", "ref_id": "BIBREF40"}, {"start": 893, "end": 897, "text": "[21,", "ref_id": "BIBREF20"}, {"start": 898, "end": 901, "text": "19]", "ref_id": "BIBREF18"}, {"start": 1100, "end": 1104, "text": "[76,", "ref_id": "BIBREF75"}, {"start": 1105, "end": 1108, "text": "48]", "ref_id": "BIBREF47"}], "ref_spans": [], "eq_spans": [], "section": "C Experimental Settings", "sec_num": null}, {"text": "Network Architectures. For the CIFAR-10, CIFAR-100, and CIFAR10-DVS datasets, our method is tested on standard network architectures, including ResNet-18, ResNet-19, and VGG-11 [63, 32, 87, 76, 19, 69] . On the ImageNet dataset, we adapt two variations on ResNet architecture [32] , SEW-ResNet-34 [87] specially proposed for SNNs, and ResNet-34 with pre-activation residual blocks [33] , aligning with previous works [76, 48, 89] . While OTTT [76] and SLTT [48] frameworks utilize normalization-free techniques under the ResNet-34 framework [5] , <PERSON> et al. [89] substitute these with their custom-designed batch normalization. We directly employ tdBN [87] instead of normalization-free methods in our experiments.", "cite_spans": [{"start": 177, "end": 181, "text": "[63,", "ref_id": "BIBREF62"}, {"start": 182, "end": 185, "text": "32,", "ref_id": "BIBREF31"}, {"start": 186, "end": 189, "text": "87,", "ref_id": "BIBREF86"}, {"start": 190, "end": 193, "text": "76,", "ref_id": "BIBREF75"}, {"start": 194, "end": 197, "text": "19,", "ref_id": "BIBREF18"}, {"start": 198, "end": 201, "text": "69]", "ref_id": "BIBREF68"}, {"start": 276, "end": 280, "text": "[32]", "ref_id": "BIBREF31"}, {"start": 297, "end": 301, "text": "[87]", "ref_id": "BIBREF86"}, {"start": 381, "end": 385, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 417, "end": 421, "text": "[76,", "ref_id": "BIBREF75"}, {"start": 422, "end": 425, "text": "48,", "ref_id": "BIBREF47"}, {"start": 426, "end": 429, "text": "89]", "ref_id": "BIBREF88"}, {"start": 443, "end": 447, "text": "[76]", "ref_id": "BIBREF75"}, {"start": 457, "end": 461, "text": "[48]", "ref_id": "BIBREF47"}, {"start": 541, "end": 544, "text": "[5]", "ref_id": "BIBREF4"}, {"start": 558, "end": 562, "text": "[89]", "ref_id": "BIBREF88"}, {"start": 652, "end": 656, "text": "[87]", "ref_id": "BIBREF86"}], "ref_spans": [], "eq_spans": [], "section": "C.2 Training Setup", "sec_num": null}, {"text": "Training Details. This work utilizes the widely adopted sigmoid-based surrogate gradient [19] to approximate the Heaviside step function using h(x, α) = 1 1+e αx and sets α = 4 to ensure the maximum derivative of the surrogate function is 1 for preventing gradient explosion. All implementations are based on the PyTorch [53] and SpikingJelly [19] frameworks. The experiments on CIFAR-10, CIFAR-100, and CIFAR10-DVS datasets run on one NVIDIA GeForce RTX 3090 GPU. For ImageNet, distributed data parallel processing is utilized across eight NVIDIA GeForce RTX 4090 GPUs. We use the SGD optimizer [58] with a momentum of 0.9 for all tasks, integrating a cosine annealing strategy [45] for the learning rate schedule. Other hyperparameters are listed in Table 3 . ", "cite_spans": [{"start": 89, "end": 93, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 321, "end": 325, "text": "[53]", "ref_id": "BIBREF52"}, {"start": 343, "end": 347, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 596, "end": 600, "text": "[58]", "ref_id": "BIBREF57"}, {"start": 679, "end": 683, "text": "[45]", "ref_id": "BIBREF44"}], "ref_spans": [{"start": 758, "end": 759, "text": "3", "ref_id": "TABREF5"}], "eq_spans": [], "section": "C.2 Training Setup", "sec_num": null}, {"text": "As shown in Figure 6 , we extend conduct empirical experiments on CIFAR10-DVS as avalidation in the case of dynamic datasets. The observations confirm that, even in data with a degree of temporal information, the empirical validation of the assumptions remains consistent with expectations. This alignment emphasizes that the approximate relationship between rate-based backpropagation and BPTT remains substantially consistent. As a result, this stability ensures that our approach continues to effectively extract rate-based representations from neuromorphic datasets with a degree of temporal dynamics, thereby maintaining robust performance across diverse data scenarios.", "cite_spans": [], "ref_spans": [{"start": 19, "end": 20, "text": "6", "ref_id": "FIGREF11"}], "eq_spans": [], "section": "D.1 Empirical Validation on CIFAR10-DVS", "sec_num": null}, {"text": "We conduct additional experiments to illustrate the comparative performance of rate-based backpropagation versus BPTT, as presented in Table 4 for CIFAR-10 and Table 5 for CIFAR-100. These experiments span various configurations, including different network architectures-ResNet-18, ", "cite_spans": [], "ref_spans": [{"start": 141, "end": 142, "text": "4", "ref_id": "TABREF6"}, {"start": 166, "end": 167, "text": "5", "ref_id": null}], "eq_spans": [], "section": "D.2 Extended Performance Comparisons with BPTT", "sec_num": null}, {"text": "To enhance the understanding of the scalability of the proposed method, we extended our analysis to include training costs across the CIFAR-100 and ImageNet datasets, utilizing additional network architectures as detailed in Table 6 . This comprehensive evaluation aimed to assess the impact of varying time steps on performance, memory, and time costs. We integrated the computation of eligibility traces during the forward process, ensuring a fair comparison by incorporating these iterative computations into the overall cost assessment. The results reveal that the total cost of rate-based backpropagation demonstrates a clear advantage over BPTT when timesteps T ≥ 2, which underscores the efficiency of the proposed method approach in managing computational resources while maintaining comparative performance across various datasets and network architectures.", "cite_spans": [], "ref_spans": [{"start": 231, "end": 232, "text": "6", "ref_id": "TABREF7"}], "eq_spans": [], "section": "D.3 Comprehensive Evaluation of Training Costs", "sec_num": null}, {"text": "There Justification: See Abstract and Section 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Social Impacts and Limitations", "sec_num": null}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Social Impacts and Limitations", "sec_num": null}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Social Impacts and Limitations", "sec_num": null}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Justification: See Appendix E.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Answer Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)? Answer: [NA] Justification: No such risks. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort. 12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected? Answer: [Yes] Justification: See Appendix C. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}], "back_matter": [{"text": "This work was supported by the National Natural Science Foundation of China (Grant No. 62304203), the Natural Science Foundation of Zhejiang Province, China (Grant No. LQ22F010011), and the ZJU-UIUC Center for Heterogeneously Integrated Brain-Inspired Computing.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgment", "sec_num": null}, {"text": "error propagated through Eq. (7) , and δ (I l ) t = ∂L ∂I l t as the error propagated through BPTT, with]. We have the gradient difference bounded by δ L-k rate -E[δProof. Given that the error backpropagation ∂I l+1 ∂I l follows a 1-Lipschitz condition with biases bounded by B for all l, we can derive E[δ] + B by non-expansivity. Then, by induction, we obtain the gradient bound between the intermediate layers and the final layer:(E[δ] .Since ∂I l+1 ∂I l t = W l+1 ⊤ κ l t is also 1-Lipschitz continuous without loss of generality, given the approximated approximated error ϵ > 0 s.t.", "cite_spans": [{"start": 29, "end": 32, "text": "(7)", "ref_id": "BIBREF6"}], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "we haveBy induction, we obtain", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E[δ", "sec_num": null}, {"text": "The pseudocode for rate-based backpropagation, illustrating the implementations for both rate M and rate S , is provided in Algorithm 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Implementation Details B.1 Pseudocode of the Rate-based Backpropagation", "sec_num": null}, {"text": "In direct training, two distinct implementation modes are recognized, activation-based and time-based [19] , differing fundamentally in their handling of the simulation timestep T . The activation-based, also known as multi-step mode, processes the T loop separately within each layer, transmitting inter-layer tensors within dimensions [T, B, S], where B and S refer to batch and spatial dimensions, respectively. The configuration enables the multi-step mode to enhance computational efficiency by reformatting the tensor dimensions as [T × B, S] to optimize parallelism in linear parts. However, the coupled processing with temporal calculations embedded within the layers increases memory retention on GPUs, potentially obscuring the benefits of memory cost optimization in both online training and our proposed methods. In contrast, the time-based mode externalizes the T loop, facilitating single-step forward computations at each timestep. This single-step mode aligns well with the dynamic modeling of temporal dimensions and facilitates memory optimization strategies more effectively. However, its restriction on parallel computation in linear components compared to multi-step mode necessitates increased forward time on GPUs, albeit with enhanced support for memory optimization. Our proposed method has been adapted to operate effectively within both frameworks to ensure comprehensiveness, as shown in Algorithm Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [{"start": 102, "end": 106, "text": "[19]", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "B.2 About Training Modes in Rate-based Backpropagation", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: See Section 4.4, Appendix B and Appendix C. Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?Answer • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Truenorth: Design and tool flow of a 65 mw 1 million neuron programmable neurosynaptic chip", "authors": [{"first": "Jun", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sawada", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Na<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Yutaka", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Nam", "suffix": ""}], "year": 2015, "venue": "", "volume": "34", "issue": "", "pages": "1537--1557", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, et al. Truenorth: Design and tool flow of a 65 mw 1 million neuron programmable neurosynaptic chip. IEEE transactions on computer-aided design of integrated circuits and systems, 34(10):1537-1557, 2015.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "<PERSON>, and <PERSON>. A solution to the learning dilemma for recurrent networks of spiking neurons", "authors": [{"first": "<PERSON>", "middle": [], "last": "Bellec", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hajek", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Nature communications", "volume": "11", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. A solution to the learning dilemma for recurrent networks of spiking neurons. Nature communications, 11(1):3625, 2020.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Estimating or propagating gradients through stochastic neurons for conditional computation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Courville", "suffix": ""}], "year": 2013, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1308.3432"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Estimating or propagating gradients through stochastic neurons for conditional computation. arXiv preprint arXiv:1308.3432, 2013.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Online spatio-temporal learning in deep neural networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "Bo<PERSON>stingl", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "W<PERSON>źniak", "suffix": ""}, {"first": "Angeliki", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Eleftheriou", "suffix": ""}], "year": 2022, "venue": "IEEE Transactions on Neural Networks and Learning Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Online spatio-temporal learning in deep neural networks. IEEE Transactions on Neural Networks and Learning Systems, 2022.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "High-performance large-scale image recognition without normalization", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "De", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "1059--1071", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. High-performance large-scale image recognition without normalization. In International Conference on Machine Learning, pages 1059-1071. PMLR, 2021.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Rate gradient approximation attack threats deep spiking neural networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "Bu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Zecheng", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "7896--7906", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Rate gradient approximation attack threats deep spiking neural networks. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 7896-7906, 2023.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Optimal ann-snn conversion for high-accuracy and ultra-low-latency spiking neural networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "Bu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Dai", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.04347"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Optimal ann-snn conversion for high-accuracy and ultra-low-latency spiking neural networks. arXiv preprint arXiv:2303.04347, 2023.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Spiking deep convolutional neural networks for energy-efficient object recognition", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "International Journal of Computer Vision", "volume": "113", "issue": "", "pages": "54--66", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Spiking deep convolutional neural networks for energy-efficient object recognition. International Journal of Computer Vision, 113:54-66, 2015.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Learning augmentation strategies from data", "authors": [{"first": "Barret", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Dandelion", "middle": [], "last": "Zoph", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Quoc V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Le", "suffix": ""}, {"first": "", "middle": [], "last": "Autoaugment", "suffix": ""}], "year": 2019, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "113--123", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> <PERSON>. Autoaugment: Learning augmentation strategies from data. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 113-123, 2019.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Loihi: A neuromorphic manycore processor with on-chip learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Tsung-Han", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Chinya", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Sri", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Na<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "38", "issue": "", "pages": "82--99", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. <PERSON>: A neuromorphic manycore processor with on-chip learning. Ieee Micro, 38(1):82-99, 2018.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Imagenet: A largescale hierarchical image database", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "2009 IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "248--255", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Imagenet: A large- scale hierarchical image database. In 2009 IEEE conference on computer vision and pattern recognition, pages 248-255. Ieee, 2009.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Optimal conversion of conventional artificial neural networks to spiking neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Shi", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2103.00476"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. Optimal conversion of conventional artificial neural networks to spiking neural networks. arXiv preprint arXiv:2103.00476, 2021.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Surrogate module learning: Reduce the gradient error accumulation in training spiking neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Shi", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "7645--7657", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Surrogate module learning: Reduce the gradient error accumulation in training spiking neural networks. In International Conference on Machine Learning, pages 7645-7657. PMLR, 2023.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Improved regularization of convolutional neural networks with cutout", "authors": [{"first": "Terrance", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1708.04552"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Improved regularization of convolutional neural networks with cutout. arXiv preprint arXiv:1708.04552, 2017.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Fast-classifying, high-accuracy spiking deep networks through weight and threshold balancing", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "2015 International joint conference on neural networks (IJCNN)", "volume": "", "issue": "", "pages": "1--8", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Fast-classifying, high-accuracy spiking deep networks through weight and threshold balancing. In 2015 International joint conference on neural networks (IJCNN), pages 1-8. ieee, 2015.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Optimal ann-snn conversion for fast and accurate inference in deep spiking neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yong<PERSON>", "middle": [], "last": "Tian", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2105.11654"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Optimal ann-snn conversion for fast and accurate inference in deep spiking neural networks. arXiv preprint arXiv:2105.11654, 2021.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Temporal effective batch normalization in spiking neural networks", "authors": [{"first": "Chaoteng", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "34377--34390", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Temporal effective batch normalization in spiking neural networks. Advances in Neural Information Processing Systems, 35:34377-34390, 2022.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Securing deep spiking neural networks against adversarial attacks through inherent structural parameters", "authors": [{"first": "Rida", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "2021 Design, Automation & Test in Europe Conference & Exhibition (DATE)", "volume": "", "issue": "", "pages": "774--779", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Securing deep spiking neural networks against adversarial attacks through inherent structural parameters. In 2021 Design, Automation & Test in Europe Conference & Exhibition (DATE), pages 774-779. IEEE, 2021.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Spikingjelly: An open-source machine learning infrastructure platform for spike-based intelligence", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Timothée", "middle": [], "last": "Masquelier", "suffix": ""}, {"first": "<PERSON>g", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Liwei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hui<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Yong<PERSON>", "middle": [], "last": "Tian", "suffix": ""}], "year": 2023, "venue": "Science Advances", "volume": "9", "issue": "40", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Spikingjelly: An open-source machine learning infrastructure platform for spike-based intelligence. Science Advances, 9(40):eadi1480, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Deep residual learning in spiking neural networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Timothée", "middle": [], "last": "Masquelier", "suffix": ""}, {"first": "Yong<PERSON>", "middle": [], "last": "Tian", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "21056--21069", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep residual learning in spiking neural networks. Advances in Neural Information Processing Systems, 34:21056-21069, 2021.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Incorporating learnable membrane time constant to enhance learning of spiking neural networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Timothée", "middle": [], "last": "Masquelier", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yong<PERSON>", "middle": [], "last": "Tian", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF international conference on computer vision", "volume": "", "issue": "", "pages": "2661--2671", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Incorporating learnable membrane time constant to enhance learning of spiking neural networks. In Proceedings of the IEEE/CVF international conference on computer vision, pages 2661-2671, 2021.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "<PERSON>, and <PERSON><PERSON><PERSON>. Stca: Spatio-temporal credit assignment with delayed feedback in deep spiking neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "In IJCAI", "volume": "15", "issue": "", "pages": "1366--1372", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Stca: Spatio-temporal credit assignment with delayed feedback in deep spiking neural networks. In IJCAI, volume 15, pages 1366-1372, 2019.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Neural coding in spiking neural networks: A comparative study for robust neuromorphic systems", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "El<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Na<PERSON>", "suffix": ""}, {"first": "Salama", "middle": [], "last": "", "suffix": ""}], "year": 2021, "venue": "Frontiers in Neuroscience", "volume": "15", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Neural coding in spiking neural networks: A comparative study for robust neuromorphic systems. Frontiers in Neuroscience, 15:638474, 2021.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Im-loss: information maximization loss for spiking neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuanpei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "156--166", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Im-loss: information maximization loss for spiking neural networks. Advances in Neural Information Processing Systems, 35:156-166, 2022.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Direct learning-based deep spiking neural networks: a review", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}], "year": 2023, "venue": "Frontiers in Neuroscience", "volume": "17", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Direct learning-based deep spiking neural networks: a review. Frontiers in Neuroscience, 17:1209795, 2023.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Deep spiking neural network: Energy efficiency through time based coding", "authors": [{"first": "<PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "European Conference on Computer Vision", "volume": "", "issue": "", "pages": "388--404", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. Deep spiking neural network: Energy efficiency through time based coding. In European Conference on Computer Vision, pages 388-404. Springer, 2020.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Rmp-snn: Residual membrane potential neuron for enabling deeper high-accuracy and low-latency spiking neural network", "authors": [{"first": "<PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "13558--13567", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Rmp-snn: Residual membrane potential neuron for enabling deeper high-accuracy and low-latency spiking neural network. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 13558-13567, 2020.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Reducing ann-snn conversion error through residual membrane potential", "authors": [{"first": "Zecheng", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "37", "issue": "", "pages": "11--21", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Reducing ann-snn conversion error through residual membrane potential. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 37, pages 11-21, 2023.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Threaten spiking neural networks through combining rate and temporal information", "authors": [{"first": "Zecheng", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bu", "suffix": ""}, {"first": "Xinyu", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Threaten spiking neural networks through combining rate and temporal information. In The Twelfth International Conference on Learning Representations.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Threaten spiking neural networks through combining rate and temporal information", "authors": [{"first": "Zecheng", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bu", "suffix": ""}, {"first": "Xinyu", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Threaten spiking neural networks through combining rate and temporal information. In The Twelfth International Conference on Learning Representations.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Bridging the gap between anns and snns by calibrating offset spikes", "authors": [{"first": "Zecheng", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.10685"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Bridging the gap between anns and snns by calibrating offset spikes. arXiv preprint arXiv:2302.10685, 2023.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Deep residual learning for image recognition", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "Xiangyu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shaoqing", "middle": [], "last": "Ren", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}], "year": 2016, "venue": "Proceedings of the IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "770--778", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep residual learning for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 770-778, 2016.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Identity mappings in deep residual networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "Xiangyu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shaoqing", "middle": [], "last": "Ren", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}], "year": 2016, "venue": "Computer Vision-ECCV 2016: 14th European Conference", "volume": "", "issue": "", "pages": "630--645", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Identity mappings in deep residual networks. In Computer Vision-ECCV 2016: 14th European Conference, Amsterdam, The Netherlands, October 11-14, 2016, Proceedings, Part IV 14, pages 630-645. Springer, 2016.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "A unified optimization framework of ann-snn conversion: Towards optimal mapping from activation values to firing rates", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Giulia", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Bin", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "14945--14974", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. A unified optimization framework of ann-snn conversion: Towards optimal mapping from activation values to firing rates. In International Conference on Machine Learning, pages 14945-14974. PMLR, 2023.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Unifying activation-and timing-based learning rules for spiking neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Kyungsu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "19534--19544", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Unifying activation-and timing-based learning rules for spiking neural networks. Advances in neural information processing systems, 33:19534- 19544, 2020.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Spiking-yolo: spiking neural network for energy-efficient object detection", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Park", "suffix": ""}, {"first": "Byunggook", "middle": [], "last": "Na", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>on", "suffix": ""}], "year": 2020, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "34", "issue": "", "pages": "11270--11277", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Spiking-yolo: spiking neural network for energy-efficient object detection. In Proceedings of the AAAI conference on artificial intelligence, volume 34, pages 11270-11277, 2020.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Learning multiple layers of features from tiny images", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, et al. Learning multiple layers of features from tiny images. 2009.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Hire-snn: Harnessing the inherent robustness of energy-efficient deep spiking neural networks by training with crafted input noise", "authors": [{"first": "Souvik", "middle": [], "last": "Kund<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "5209--5218", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Hire-snn: Harnessing the inherent robustness of energy-efficient deep spiking neural networks by training with crafted input noise. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 5209-5218, 2021.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Training deep spiking neural networks using backpropagation", "authors": [{"first": "Jun", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Delbruck", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Frontiers in neuroscience", "volume": "10", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Training deep spiking neural networks using backpropagation. Frontiers in neuroscience, 10:228000, 2016.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Quantization framework for fast spiking neural networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Furber", "suffix": ""}], "year": 2022, "venue": "Frontiers in Neuroscience", "volume": "16", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Quantization framework for fast spiking neural networks. Frontiers in Neuroscience, 16:918793, 2022.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Cifar10-dvs: an eventstream dataset for object classification", "authors": [{"first": "Hongmin", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiangyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Shi", "suffix": ""}], "year": 2017, "venue": "Frontiers in neuroscience", "volume": "11", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Cifar10-dvs: an event- stream dataset for object classification. Frontiers in neuroscience, 11:244131, 2017.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "A free lunch from ann: Towards efficient, accurate spiking neural networks calibration", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shi", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2021, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "6316--6325", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A free lunch from ann: Towards efficient, accurate spiking neural networks calibration. In International conference on machine learning, pages 6316-6325. PMLR, 2021.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Differentiable spike: Rethinking gradient-descent for training spiking neural networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shan<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yongqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shi", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "23426--23439", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Differ- entiable spike: Rethinking gradient-descent for training spiking neural networks. Advances in Neural Information Processing Systems, 34:23426-23439, 2021.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Uncovering the representation of spiking neural networks trained with surrogate gradient", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>eu<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.13098"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Uncovering the representation of spiking neural networks trained with surrogate gradient. arXiv preprint arXiv:2304.13098, 2023.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Stochastic gradient descent with warm restarts", "authors": [{"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Sgdr", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1608.03983"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Sgdr: Stochastic gradient descent with warm restarts. arXiv preprint arXiv:1608.03983, 2016.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Networks of spiking neurons: the third generation of neural network models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1997, "venue": "Neural networks", "volume": "10", "issue": "9", "pages": "1659--1671", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Networks of spiking neurons: the third generation of neural network models. Neural networks, 10(9):1659-1671, 1997.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Training high-performance low-latency spiking neural networks by differentiation on spike representation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Mingqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Yan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "12444--12453", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Training high-performance low-latency spiking neural networks by differentiation on spike representation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 12444-12453, 2022.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Towards memory-and time-efficient backpropagation for training spiking neural networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Mingqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Yan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "6166--6176", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Towards memory-and time-efficient backpropagation for training spiking neural networks. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 6166-6176, 2023.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Supervised learning based on temporal coding in spiking neural networks", "authors": [{"first": "Hesham", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "IEEE transactions on neural networks and learning systems", "volume": "29", "issue": "7", "pages": "3227--3235", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> Mostafa. Supervised learning based on temporal coding in spiking neural networks. IEEE transactions on neural networks and learning systems, 29(7):3227-3235, 2017.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Certified adversarial robustness for rate encoded spiking neural networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Giulia", "middle": [], "last": "Alquabeh", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Bin", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2023, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Certified adversarial robustness for rate encoded spiking neural networks. In The Twelfth International Conference on Learning Representations, 2023.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Surrogate gradient learning in spiking neural networks: Bringing the power of gradient-based optimization to spiking neural networks", "authors": [{"first": "Hesham", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "IEEE Signal Processing Magazine", "volume": "36", "issue": "6", "pages": "51--63", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Surrogate gradient learning in spiking neural networks: Bringing the power of gradient-based optimization to spiking neural networks. IEEE Signal Processing Magazine, 36(6):51-63, 2019.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "A unified approach to the study of temporal, correlational, and rate coding", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON>", "suffix": ""}], "year": 2001, "venue": "Neural Computation", "volume": "13", "issue": "6", "pages": "1311--1349", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. A unified approach to the study of temporal, correlational, and rate coding. Neural Computation, 13(6):1311-1349, 2001.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Pytorch: An imperative style, high-performance deep learning library", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "Gross", "suffix": ""}, {"first": "Francisco", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Natalia", "middle": [], "last": "Gimelshein", "suffix": ""}, {"first": "Luca", "middle": [], "last": "Antiga", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Pytorch: An imperative style, high-performance deep learning library. Advances in neural information processing systems, 32, 2019.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Towards artificial general intelligence with hybrid tianjic chip architecture", "authors": [{"first": "<PERSON>", "middle": [], "last": "Pei", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Zhenzhi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "He", "suffix": ""}], "year": 2019, "venue": "Nature", "volume": "572", "issue": "", "pages": "106--111", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Towards artificial general intelligence with hybrid tianjic chip architecture. Nature, 572(7767):106-111, 2019.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Diet-snn: A low-latency spiking neural network with direct input encoding and leakage and threshold optimization", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "IEEE Transactions on Neural Networks and Learning Systems", "volume": "34", "issue": "6", "pages": "3174--3182", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>. Diet-snn: A low-latency spiking neural network with direct input encoding and leakage and threshold optimization. IEEE Transactions on Neural Networks and Learning Systems, 34(6):3174-3182, 2021.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Towards spike-based machine intelligence with neuromorphic computing", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Jaiswal", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Panda", "suffix": ""}], "year": 2019, "venue": "Nature", "volume": "575", "issue": "7784", "pages": "607--617", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Towards spike-based machine intelligence with neuromorphic computing. Nature, 575(7784):607-617, 2019.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Conversion of continuous-valued deep networks to efficient event-driven networks for image classification", "authors": [{"first": "Bodo", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Lu<PERSON><PERSON>", "suffix": ""}, {"first": "Yuhuang", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "Frontiers in neuroscience", "volume": "11", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Conversion of continuous-valued deep networks to efficient event-driven networks for image classification. Frontiers in neuroscience, 11:294078, 2017.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Learning representations by back-propagating errors", "authors": [{"first": "<PERSON>", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1986, "venue": "nature", "volume": "323", "issue": "6088", "pages": "533--536", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Learning representations by back-propagating errors. nature, 323(6088):533-536, 1986.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Going deeper in spiking neural networks: Vgg and residual architectures", "authors": [{"first": "Abhr<PERSON>l", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ye", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Frontiers in neuroscience", "volume": "13", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Going deeper in spiking neural networks: Vgg and residual architectures. Frontiers in neuroscience, 13:95, 2019.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Inherent adversarial robustness of deep spiking neural networks: Effects of discrete input encoding and non-linear activations", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Panda", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Computer Vision-ECCV 2020: 16th European Conference", "volume": "", "issue": "", "pages": "399--414", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Inherent adversarial robustness of deep spiking neural networks: Effects of discrete input encoding and non-linear activations. In Computer Vision-ECCV 2020: 16th European Conference, Glasgow, UK, August 23-28, 2020, Proceedings, Part XXIX 16, pages 399-414. Springer, 2020.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Exploiting high performance spiking neural networks with efficient spiking patterns", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dongcheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2301.12356"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Exploiting high performance spiking neural networks with efficient spiking patterns. arXiv preprint arXiv:2301.12356, 2023.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Slayer: Spike layer error reassignment in time", "authors": [{"first": "B", "middle": [], "last": "Sumit", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Orchard", "suffix": ""}], "year": 2018, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> <PERSON> and <PERSON><PERSON><PERSON>. Slayer: Spike layer error reassignment in time. Advances in neural information processing systems, 31, 2018.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Very deep convolutional networks for large-scale image recognition", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1409.1556"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Very deep convolutional networks for large-scale image recognition. arXiv preprint arXiv:1409.1556, 2014.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Motor control by precisely timed spike patterns", "authors": [{"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Pack", "suffix": ""}, {"first": "P", "middle": ["H"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Proceedings of the National Academy of Sciences", "volume": "114", "issue": "5", "pages": "1171--1176", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Motor control by precisely timed spike patterns. Proceedings of the National Academy of Sciences, 114(5):1171-1176, 2017.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Optimized spiking neurons can classify images with high accuracy through temporal coding with two spikes", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature Machine Intelligence", "volume": "3", "issue": "3", "pages": "230--238", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Optimized spiking neurons can classify images with high accuracy through temporal coding with two spikes. Nature Machine Intelligence, 3(3):230-238, 2021.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "S3nn: Time step reduction of spiking surrogate gradients for training energy efficient single-step spiking neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ikegawa", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Sawada", "suffix": ""}], "year": 2023, "venue": "Neural Networks", "volume": "159", "issue": "", "pages": "208--219", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. S3nn: Time step reduction of spiking surrogate gradients for training energy efficient single-step spiking neural networks. Neural Networks, 159:208-219, 2023.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "Spikegrad: An annequivalent computation model for implementing backpropagation with spikes", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1906.00851"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Spikegrad: An ann- equivalent computation model for implementing backpropagation with spikes. arXiv preprint arXiv:1906.00851, 2019.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "Ssf: Accelerating training of spiking neural networks with stabilized spiking flow", "authors": [{"first": "Jing<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "Yuxi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "5982--5991", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Ssf: Accelerating training of spiking neural networks with stabilized spiking flow. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 5982-5991, 2023.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "Adaptive smoothing gradient learning for spiking neural networks", "authors": [{"first": "Zim<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Yan", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "35798--35816", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Adaptive smoothing gradient learning for spiking neural networks. In International Conference on Machine Learning, pages 35798-35816. PMLR, 2023.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "Towards lossless ann-snn conversion under ultra-low latency with dual-phase optimization", "authors": [{"first": "Zim<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Yan", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2205.07473"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Towards lossless ann-snn conversion under ultra-low latency with dual-phase optimization. arXiv preprint arXiv:2205.07473, 2022.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "A learning algorithm for continually running fully recurrent neural networks", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Zipser", "suffix": ""}], "year": 1989, "venue": "Neural computation", "volume": "1", "issue": "2", "pages": "270--280", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. A learning algorithm for continually running fully recurrent neural networks. Neural computation, 1(2):270-280, 1989.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "Training spiking neural networks with accumulated spiking flow", "authors": [{"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Wenming", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yongting", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "35", "issue": "", "pages": "10320--10328", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Training spiking neural networks with accumulated spiking flow. In Proceedings of the AAAI conference on artificial intelligence, volume 35, pages 10320-10328, 2021.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "A tandem learning rule for effective training and rapid inference of deep spiking neural networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yansong", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Haizhou", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2021, "venue": "IEEE Transactions on Neural Networks and Learning Systems", "volume": "34", "issue": "1", "pages": "446--460", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. A tandem learning rule for effective training and rapid inference of deep spiking neural networks. IEEE Transactions on Neural Networks and Learning Systems, 34(1):446-460, 2021.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "Spatio-temporal backpropagation for training high-performance spiking neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Shi", "suffix": ""}], "year": 2018, "venue": "Frontiers in neuroscience", "volume": "12", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Spatio-temporal backpropagation for training high-performance spiking neural networks. Frontiers in neuroscience, 12:323875, 2018.", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "Event-based backpropagation can compute exact gradients for spiking neural networks", "authors": [{"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Scientific Reports", "volume": "11", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Event-based backpropagation can compute exact gradients for spiking neural networks. Scientific Reports, 11(1):12829, 2021.", "links": null}, "BIBREF75": {"ref_id": "b75", "title": "Online training through time for spiking neural networks", "authors": [{"first": "Mingqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Zongpeng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Di", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in neural information processing systems", "volume": "35", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Online training through time for spiking neural networks. Advances in neural information processing systems, 35:20717-20730, 2022.", "links": null}, "BIBREF76": {"ref_id": "b76", "title": "Training feedback spiking neural networks by implicit differentiation on the equilibrium state", "authors": [{"first": "Mingqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Zongpeng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in neural information processing systems", "volume": "34", "issue": "", "pages": "14516--14528", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Training feedback spiking neural networks by implicit differentiation on the equilibrium state. Advances in neural information processing systems, 34:14516-14528, 2021.", "links": null}, "BIBREF77": {"ref_id": "b77", "title": "Near lossless transfer learning for spiking neural networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Yan", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "35", "issue": "", "pages": "10577--10584", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Near lossless transfer learning for spiking neural networks. In Proceedings of the AAAI conference on artificial intelligence, volume 35, pages 10577-10584, 2021.", "links": null}, "BIBREF78": {"ref_id": "b78", "title": "Temporal-wise attention spiking neural networks for event streams classification", "authors": [{"first": "Man", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Huanhuan", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "10221--10230", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Temporal-wise attention spiking neural networks for event streams classification. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 10221- 10230, 2021.", "links": null}, "BIBREF79": {"ref_id": "b79", "title": "Glif: A unified gated leaky integrate-andfire neuron for spiking neural networks", "authors": [{"first": "Xingting", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Fanrong", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Mo", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "32160--32171", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Glif: A unified gated leaky integrate-and- fire neuron for spiking neural networks. Advances in Neural Information Processing Systems, 35:32160-32171, 2022.", "links": null}, "BIBREF80": {"ref_id": "b80", "title": "Effective and efficient computation with multiple-timescale spiking recurrent neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Neuromorphic Systems", "volume": "", "issue": "", "pages": "1--8", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Effective and efficient computation with multiple-timescale spiking recurrent neural networks. In International Conference on Neuromorphic Systems 2020, pages 1-8, 2020.", "links": null}, "BIBREF81": {"ref_id": "b81", "title": "Accurate online training of dynamical spiking neural networks through forward propagation through time", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Nature Machine Intelligence", "volume": "5", "issue": "5", "pages": "518--527", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Accurate online training of dynamical spiking neural networks through forward propagation through time. Nature Machine Intelligence, 5(5):518-527, 2023.", "links": null}, "BIBREF82": {"ref_id": "b82", "title": "Stsc-snn: Spatio-temporal synaptic connection with temporal convolution and attention for spiking neural networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "Da", "middle": [], "last": "Li", "suffix": ""}, {"first": "Gaoang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>ing", "middle": [], "last": "Li", "suffix": ""}], "year": 2022, "venue": "Frontiers in Neuroscience", "volume": "16", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Stsc-snn: Spatio-temporal synaptic connection with temporal convolution and attention for spiking neural networks. Frontiers in Neuroscience, 16:1079357, 2022.", "links": null}, "BIBREF83": {"ref_id": "b83", "title": "Superspike: Supervised learning in multilayer spiking neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Surya", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Neural computation", "volume": "30", "issue": "6", "pages": "1514--1541", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>. Superspike: Supervised learning in multilayer spiking neural networks. Neural computation, 30(6):1514-1541, 2018.", "links": null}, "BIBREF84": {"ref_id": "b84", "title": "The remarkable robustness of surrogate gradient learning for instilling complex function in spiking neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["P"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Neural computation", "volume": "33", "issue": "4", "pages": "899--925", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. The remarkable robustness of surrogate gradient learning for instilling complex function in spiking neural networks. Neural computation, 33(4):899-925, 2021.", "links": null}, "BIBREF85": {"ref_id": "b85", "title": "Temporal spike sequence learning via backpropagation for deep spiking neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "12022--12033", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>. Temporal spike sequence learning via backpropagation for deep spiking neural networks. Advances in neural information processing systems, 33:12022-12033, 2020.", "links": null}, "BIBREF86": {"ref_id": "b86", "title": "Going deeper with directly-trained larger spiking neural networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}], "year": 2021, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "35", "issue": "", "pages": "11062--11070", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Going deeper with directly-trained larger spiking neural networks. In Proceedings of the AAAI conference on artificial intelligence, volume 35, pages 11062-11070, 2021.", "links": null}, "BIBREF87": {"ref_id": "b87", "title": "Temporalcoded deep spiking neural network with easy training and robust performance", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiaohua", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Arinda<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "35", "issue": "", "pages": "11143--11151", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Temporal- coded deep spiking neural network with easy training and robust performance. In Proceedings of the AAAI conference on artificial intelligence, volume 35, pages 11143-11151, 2021.", "links": null}, "BIBREF88": {"ref_id": "b88", "title": "Online stabilization of spiking neural networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiaodong", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Online stabilization of spiking neural networks. In The Twelfth International Conference on Learning Representations.", "links": null}}, "ref_entries": {"FIGREF0": {"num": null, "text": "Figure 1: Illustration of the forward and backward procedures of different training methods.", "type_str": "figure", "uris": null, "fig_num": "1"}, "FIGREF1": {"num": null, "text": "Figure 2: The implementation of rate-based backpropagation across layers. A rate-coding approximation is utilized for the forward procedure to connect average inputs with rate outputs, enabling fast rate-based error backpropagation throughout the training process. from both (spatial) spike generation and (temporal) potential accumulation, expressed as: ∂L ∂u l t = ∂L ∂s l t", "type_str": "figure", "uris": null, "fig_num": "21"}, "FIGREF2": {"num": null, "text": "training, two distinct training modes are recognized: (multi-step) activation-based and (single-step) time-based [19], differing fundamentally in handling the timesteps loop. We implement our rate-based propagation in both modes: rate M denotes the multi-step training mode where T loops are embedded within layers, and rate S refers to the single-step training mode with T loops outside the layers. A detailed discussion of training modes is included in Appendix B.", "type_str": "figure", "uris": null, "fig_num": null}, "FIGREF3": {"num": null, "text": "Figure 3: Empirical measurements conducted on the training procedure of BPTT. The experiments are carried out on the CIFAR-100 dataset using ResNet-18. Each subplot is labeled according to the naming convention \"A{test#}-T{timesteps#}-{target}-L{layer#}B{block#}N{LIF#}/C{conv#}.\"", "type_str": "figure", "uris": null, "fig_num": "3"}, "FIGREF4": {"num": null, "text": "Figure 4: Results of BPTT and rate M across various timesteps.Table 2: Performance w/o and w/ temporal shuffle for models trained by rate M", "type_str": "figure", "uris": null, "fig_num": "4"}, "FIGREF5": {"num": null, "text": "Figure 5: Firing rates statistics for models trained by rate M .", "type_str": "figure", "uris": null, "fig_num": "5"}, "FIGREF7": {"num": null, "text": "0 = 0, g l 0 = 0, e l 0 = 0 for all l ∈ [1, L].", "type_str": "figure", "uris": null, "fig_num": null}, "FIGREF8": {"num": null, "text": "to L do 18", "type_str": "figure", "uris": null, "fig_num": "16171"}, "FIGREF9": {"num": null, "text": "Implementation of Batch Normalization in Rate-based Backpropagation In the forward pass, batch normalization (BN) precedes neuron activation, scaling inputs I l t and introduces a bias in the average inputs c = E[I t ]. We denote c to represent the biased average inputs as c = E[ Ĩt ] = E[BN(I t )] instead of c. Note that BN acts as a linear operation during inference, where c = E[ Ĩt ] = E[BN(I t )] = BN(E[I t ]) = BN(c). Implementing rate-based propagation requires considering how gradients pass through the BN layers and affect their intrinsic parameters during training. Initially, we explore the spatial BN", "type_str": "figure", "uris": null, "fig_num": "3"}, "FIGREF10": {"num": null, "text": "Id in backward. The implementation utilizes gradient replacement with the detach operation in PyTorch: σ2 c = detach(σ 2 -σ 2 c )+σ 2 c . Thus, in the forward phase, c = BN(c) = E[BN(I t )], and in the backward phase, ∂ c ∂c = E[ ∂ c ∂It ], aligning perfectly with the foundational principles of rate-based backpropagation.", "type_str": "figure", "uris": null, "fig_num": "2"}, "FIGREF11": {"num": null, "text": "Figure 6: Empirical measurements conducted on the CIFAR10-DVS dataset.", "type_str": "figure", "uris": null, "fig_num": "6"}, "TABREF0": {"num": null, "content": "<table><tr><td/><td>Training</td><td>Method</td><td>Model</td><td>Timesteps</td><td>Top-1 Acc (%)</td></tr><tr><td/><td>QCFS [7]</td><td>ANN2SNN</td><td>ResNet-18</td><td>8</td><td>94.82</td></tr><tr><td/><td>DSR [47]</td><td>one-step</td><td>PreAct-ResNet-18</td><td>20</td><td>95.10±0.15</td></tr><tr><td/><td>SSF [68]</td><td>one-step</td><td>PreAct-ResNet-18</td><td>20</td><td>94.90</td></tr><tr><td/><td>BPTT M</td><td>BPTT</td><td>ResNet-18</td><td>4</td><td>95.64</td></tr><tr><td/><td>rate M (ours)</td><td>one-step</td><td>ResNet-18</td><td>4</td><td>95.61±0.02(95.64)</td></tr><tr><td>CIFAR10</td><td>OTTT [76] SLTT [48] OS [89]</td><td>online online online</td><td>VGG-11  *  ResNet-18 VGG-11 ResNet-19</td><td>6 6 4 4</td><td>93.52±0.06 94.44±0.21 94.35 95.20</td></tr><tr><td/><td>BPTT S</td><td>BPTT</td><td>ResNet-18</td><td>4</td><td>95.53</td></tr><tr><td/><td/><td/><td>VGG-11</td><td>4</td><td>95.61</td></tr><tr><td/><td>rate S (ours)</td><td>one-step</td><td>ResNet-18</td><td>4</td><td>95.42±0.11(95.56)</td></tr><tr><td/><td/><td/><td>VGG-11</td><td>4</td><td>95.57±0.08(95.68)</td></tr><tr><td/><td>DSR [47]</td><td>one-step</td><td>PreAct-ResNet-18</td><td>20</td><td>78.50±0.12</td></tr><tr><td/><td>SSF [68]</td><td>one-step</td><td>PreAct-ResNet-18</td><td>20</td><td>75.48</td></tr><tr><td/><td>BPTT M</td><td>BPTT</td><td>ResNet-18</td><td>4</td><td>77.93</td></tr><tr><td/><td>rate M (ours)</td><td>one-step</td><td>ResNet-18</td><td>4</td><td>78.26±0.12(78.38)</td></tr><tr><td>CIFAR100</td><td>OTTT [76] SLTT [48] OS [89]</td><td>online online online</td><td>VGG-11  *  ResNet-18 VGG-11 ResNet-19</td><td>6 6 4 4</td><td>71.05±0.04 74.38±0.30 76.48 77.86</td></tr><tr><td/><td>BPTT S</td><td>BPTT</td><td>ResNet-18</td><td>4</td><td>77.72</td></tr><tr><td/><td/><td/><td>VGG-11</td><td>4</td><td>77.82</td></tr><tr><td/><td>rate S (ours)</td><td>one-step</td><td>ResNet-18</td><td>4</td><td>77.73±0.28(77.93)</td></tr><tr><td/><td/><td/><td>VGG-11</td><td>4</td><td>77.87±0.35(78.13)</td></tr><tr><td/><td>OTTT [76]</td><td>online</td><td>PreAct-ResNet-34*</td><td>6</td><td>65.15</td></tr><tr><td/><td>SLTT [48]</td><td>online</td><td>PreAct-ResNet-34*</td><td>6</td><td>66.19</td></tr><tr><td>ImageNet</td><td>OS [89] SEW-ResNet [20] rate S (ours)</td><td>online BPTT one-step</td><td>SEW-ResNet-34 PreAct-ResNet-34 SEW-ResNet-34 SEW-ResNet-34 PreAct-ResNet-34</td><td>4 4 4 4 4</td><td>64.14 67.54 67.04 65.66 69.58</td></tr><tr><td/><td>rate M (ours)</td><td>one-step</td><td>SEW-ResNet-34 PreAct-ResNet-34</td><td>4 4</td><td>65.84 70.01</td></tr><tr><td>CIFAR10-DVS</td><td>DSR [47] SSF [68] OTTT [76] SLTT [48] BPTT S BPTT M</td><td>one-step online BPTT</td><td>VGG-11 VGG-11 VGG-11  *  VGG-11 VGG-11 VGG-11</td><td>20 20 10 10 10 10</td><td>77.27±0.24 78.0 76.63±0.34 77.17±0.23 76.73 76.86</td></tr><tr><td/><td>rate S (ours) rate M (ours)</td><td>one-step</td><td>VGG-11 VGG-11</td><td>10 10</td><td>76.48±0.23(76.71) 76.96±0.13(77.13)</td></tr></table>", "text": "Performance on CIFAR-10, CIFAR-100, ImageNet, and CIFAR10-DVS. Results are averaged over three runs of experiments, except for single crop evaluations on ImageNet. Models marked with ( * ) employ scaled weight standardization, adapting to normalizer-free architectures.", "type_str": "table", "html": null}, "TABREF1": {"num": null, "content": "<table><tr><td>Dataset</td><td>Model</td><td colspan=\"2\">Timesteps Accuracy</td><td>Shuffled</td></tr><tr><td/><td/><td>2</td><td>94.77</td><td>94.63±0.04</td></tr><tr><td/><td>ResNet-18</td><td>4</td><td>95.51</td><td>95.50±0.04</td></tr><tr><td>CIFAR-10</td><td/><td>6 2</td><td>95.97 95.13</td><td>95.95±0.09 95.10±0.05</td></tr><tr><td/><td>VGG-11</td><td>4</td><td>95.37</td><td>95.37±0.03</td></tr><tr><td/><td/><td>6</td><td>95.77</td><td>95.79±0.05</td></tr><tr><td/><td/><td>2</td><td>76.27</td><td>75.59±0.11</td></tr><tr><td/><td>ResNet-18</td><td>4</td><td>78.32</td><td>77.72±0.15</td></tr><tr><td>CIFAR-100</td><td/><td>6 2</td><td>79.10 77.46</td><td>79.10±0.14 77.21±0.12</td></tr><tr><td/><td>VGG-11</td><td>4</td><td>77.88</td><td>77.78±0.16</td></tr><tr><td/><td/><td>6</td><td>77.97</td><td>78.02±0.09</td></tr><tr><td>ImageNet</td><td>SEW-ResNet-34 PreAct-ResNet-34</td><td>4 4</td><td>65.84 70.01</td><td>65.11±0.11 69.78±0.10</td></tr><tr><td>CIFAR10-DVS</td><td>VGG-11</td><td>10</td><td>76.50</td><td>74.69±0.17</td></tr></table>", "text": "Performance w/o and w/ temporal shuffle for models trained by rate M", "type_str": "table", "html": null}, "TABREF3": {"num": null, "content": "<table><tr><td colspan=\"2\">2 if rate M then</td><td/><td/><td/><td/><td/></tr><tr><td>3</td><td colspan=\"2\">for l = 1 to L do</td><td/><td/><td/><td/></tr><tr><td>4</td><td colspan=\"7\">Compute input currents through linear operators I l t = W l s l-1 t</td><td>for all t ∈ [1, T ];</td></tr><tr><td>5</td><td colspan=\"5\">Initialize ρ l 0 = 0, g l 0 = 0, e l 0 = 0.</td><td/></tr><tr><td>6</td><td colspan=\"2\">for t = 1 to T do</td><td/><td/><td/><td/></tr><tr><td>7</td><td colspan=\"7\">Compute output spikes s l t from I l t following neural dynamics in Eq. (1);</td></tr><tr><td>8</td><td colspan=\"6\">Compute the eligibility trace ρ l t = 1 + ρ l t-1</td><td>∂u l t ∂u l t-1</td><td>+</td><td>∂u l t ∂s l t-1</td><td>∂s l t-1 t-1 ∂u l</td><td>in Eq. (8);</td></tr><tr><td>9 10</td><td colspan=\"5\">Accumulate e l t = 1 t ((t -1)e l t-1 + s l t ); Accumulate g l t = 1 t ((t -1)g l t-1 + ∂s l t ∂u l t</td><td>ρ t ).</td></tr><tr><td/><td colspan=\"2\">Theorem 2. For gradients δ</td><td>(s l ) t</td><td>= ∂L ∂s l t</td><td colspan=\"2\">and κ l t = τ</td><td>∂s l t τ ∂I l</td><td>, given the approximation error bound</td></tr><tr><td/><td>ϵ &gt; 0 s.t. E δ t (s l )</td><td colspan=\"2\">κ l t -E δ t (s l )</td><td>E κ l</td><td/><td/></tr></table>", "text": ") Trainable parameters {W l } l≤L ; Training Mini-batch {(x 0 t , y)}; Training Mode rate S or rate M . Output: Updated parameters {W l } l≤L 1 Initialize input spikes s 0 t = x 0 t for all t ∈ [1, T ].", "type_str": "table", "html": null}, "TABREF5": {"num": null, "content": "<table><tr><td/><td colspan=\"4\">CIFAR-10 CIFAR-100 ImageNet CIFAR10-DVS</td></tr><tr><td>Epoch</td><td>300</td><td>300</td><td>100</td><td>300</td></tr><tr><td>Learning rate</td><td>0.1</td><td>0.1</td><td>0.2</td><td>0.1</td></tr><tr><td>Batch size</td><td>128</td><td>128</td><td>512</td><td>128</td></tr><tr><td>Weight decay</td><td>5e-4</td><td>5e-4</td><td>2e-5</td><td>5e-4</td></tr></table>", "text": "Training hyperparameters.", "type_str": "table", "html": null}, "TABREF6": {"num": null, "content": "<table><tr><td>Training</td><td>Model</td><td>Timesteps</td><td>Top-1 Acc (%)</td></tr><tr><td/><td/><td>2</td><td>95.02</td></tr><tr><td/><td>ResNet-18</td><td>4</td><td>95.53</td></tr><tr><td/><td/><td>6</td><td>95.68</td></tr><tr><td>BPTT S</td><td>ResNet-19</td><td>2 4</td><td>96.12 96.38</td></tr><tr><td/><td/><td>6</td><td>96.57</td></tr><tr><td/><td/><td>2</td><td>95.27</td></tr><tr><td/><td>VGG-11</td><td>4</td><td>95.61</td></tr><tr><td/><td/><td>6</td><td>95.63</td></tr><tr><td/><td/><td>2</td><td>94.82±0.07(94.89)</td></tr><tr><td/><td>ResNet-18</td><td>4</td><td>95.42±0.11(95.56)</td></tr><tr><td/><td/><td>6</td><td>95.73±0.03(95.78)</td></tr><tr><td>rate S</td><td>ResNet-19</td><td>2 4</td><td>96.11±0.05(96.18) 96.32±0.04(96.38)</td></tr><tr><td/><td/><td>6</td><td>96.38±0.06(96.45)</td></tr><tr><td/><td/><td>2</td><td>95.44±0.02(95.46)</td></tr><tr><td/><td>VGG-11</td><td>4</td><td>95.57±0.08(95.68)</td></tr><tr><td/><td/><td>6</td><td>95.64±0.12(95.76)</td></tr><tr><td/><td/><td>2</td><td>94.93</td></tr><tr><td/><td>ResNet-18</td><td>4</td><td>95.64</td></tr><tr><td/><td/><td>6</td><td>96.03</td></tr><tr><td>BPTT M</td><td>ResNet-19</td><td>2 4</td><td>96.16 96.49</td></tr><tr><td/><td/><td>6</td><td>96.70</td></tr><tr><td/><td/><td>2</td><td>95.31</td></tr><tr><td/><td>VGG-11</td><td>4</td><td>95.67</td></tr><tr><td/><td/><td>6</td><td>95.64</td></tr><tr><td/><td/><td>2</td><td>94.75±0.05(94.82)</td></tr><tr><td/><td>ResNet-18</td><td>4</td><td>95.61±0.02(95.64)</td></tr><tr><td/><td/><td>6</td><td>95.90±0.07(96.01)</td></tr><tr><td>rate M</td><td>ResNet-19</td><td>2 4</td><td>96.23±0.10(96.33) 96.26±0.03(96.29)</td></tr><tr><td/><td/><td>6</td><td>96.38±0.02(96.40)</td></tr><tr><td/><td/><td>2</td><td>95.17±0.12(95.35)</td></tr><tr><td/><td>VGG-11</td><td>4</td><td>95.30±0.06(95.37)</td></tr><tr><td/><td/><td>6</td><td>95.23±0.06(95.32)</td></tr></table>", "text": "Performance comparison of rate-based backpropagation and BPTT on CIFAR-10.", "type_str": "table", "html": null}, "TABREF7": {"num": null, "content": "<table><tr><td/><td/><td/><td/><td/><td/><td>Timesteps</td><td/></tr><tr><td>Datasets</td><td>Network</td><td>Method</td><td/><td>T=1</td><td>T=2</td><td>T=4</td><td>T=8</td><td>T=16</td></tr><tr><td/><td/><td/><td colspan=\"2\">Time of Eligibility Track 0.003</td><td>0.004</td><td>0.007</td><td>0.015</td><td>0.027</td></tr><tr><td/><td/><td/><td>Time of Backward</td><td>0.034</td><td>0.035</td><td>0.036</td><td>0.034</td><td>0.036</td></tr><tr><td/><td/><td>rate M</td><td>Time of both</td><td>0.037</td><td>0.039</td><td>0.043</td><td>0.049</td><td>0.063</td></tr><tr><td/><td>ResNet-18</td><td/><td>Memory Allocated Top-1 Acc [%]</td><td colspan=\"3\">1.8492 1.8488 1.8473 74.60 76.04 78.24</td><td>1.8496 79.24</td><td>1.8483 79.37</td></tr><tr><td/><td/><td/><td>Time of Backward</td><td>0.023</td><td>0.044</td><td>0.098</td><td>0.199</td><td>0.564</td></tr><tr><td/><td/><td>BPTT M</td><td>Memory Allocated</td><td colspan=\"3\">1.4272 2.4454 4.4804</td><td>8.0460</td><td>15.685</td></tr><tr><td/><td/><td/><td>Top-1 Acc [%]</td><td>74.38</td><td>76.65</td><td>78.49</td><td>78.35</td></tr><tr><td/><td/><td/><td colspan=\"2\">Time of Eligibility Track 0.006</td><td>0.012</td><td>0.020</td><td>0.041</td></tr><tr><td/><td/><td/><td>Time of Backward</td><td>0.083</td><td>0.083</td><td>0.082</td><td>0.083</td></tr><tr><td/><td/><td>rate M</td><td>Time of both</td><td>0.089</td><td>0.095</td><td>0.102</td><td>0.124</td></tr><tr><td>CIFAR100</td><td>ResNet-19</td><td/><td colspan=\"4\">Memory Allocated [GB] 4.4787 4.4798 4.4788 Top-1 Acc [%] 78.3 80.00 80.65</td><td>4.4784 81.31</td></tr><tr><td/><td/><td/><td>Time of Backward</td><td>0.046</td><td>0.111</td><td>0.285</td><td>0.552</td></tr><tr><td/><td/><td>BPTT M</td><td colspan=\"5\">Memory Allocated [GB] 3.2556 5.6636 10.8978 20.3862</td></tr><tr><td/><td/><td/><td>Top-1 Acc [%]</td><td>78.39</td><td>80.06</td><td>81.11</td><td>81.13</td></tr><tr><td/><td/><td/><td colspan=\"2\">Time of Eligibility Track 0.003</td><td>0.003</td><td>0.006</td><td>0.011</td><td>0.020</td></tr><tr><td/><td/><td/><td>Time of Backward</td><td>0.017</td><td>0.017</td><td>0.017</td><td>0.017</td><td>0.018</td></tr><tr><td/><td/><td>rate M</td><td>Time of both</td><td>0.020</td><td>0.020</td><td>0.023</td><td>0.028</td><td>0.038</td></tr><tr><td/><td>VGG11</td><td/><td colspan=\"4\">Memory Allocated [GB] 1.3624 1.3607 1.3619 Top-1 Acc [%] 76.13 77.59 77.75</td><td>1.3613 78.34</td><td>1.3601 78.65</td></tr><tr><td/><td/><td/><td>Time of Backward</td><td>0.010</td><td>0.021</td><td>0.054</td><td>0.135</td><td>0.384</td></tr><tr><td/><td/><td>BPTT M</td><td colspan=\"4\">Memory Allocated [GB] 0.9911 1.6784 3.7363</td><td colspan=\"2\">6.6141 12.3768</td></tr><tr><td/><td/><td/><td>Top-1 Acc [%]</td><td>76.34</td><td>77.20</td><td>77.98</td><td>78.26</td><td>78.37</td></tr><tr><td/><td/><td/><td colspan=\"2\">Time of Eligibility Track 0.012</td><td>0.014</td><td>0.023</td><td/></tr><tr><td/><td/><td>rate M</td><td>Time of Backward Time of both</td><td>0.074 0.086</td><td>0.074 0.088</td><td>0.074 0.097</td><td/></tr><tr><td/><td>SEW-ResNet-34</td><td/><td colspan=\"4\">Memory Allocated [GB] 5.7887 5.7898 5.7883</td><td/></tr><tr><td/><td/><td/><td>Time of Backward</td><td>0.046</td><td>0.095</td><td>0.233</td><td/></tr><tr><td/><td/><td>BPTT M</td><td colspan=\"4\">Memory Allocated [GB] 3.9858 6.8654 12.5597</td><td/></tr><tr><td>ImageNet</td><td/><td/><td colspan=\"2\">Time of Eligibility Track 0.007</td><td>0.009</td><td>0.020</td><td/></tr><tr><td/><td/><td>rate M</td><td>Time of Backward Time of both</td><td>0.072 0.079</td><td>0.071 0.080</td><td>0.072 0.092</td><td/></tr><tr><td/><td>PreAct-ResNet-34</td><td/><td colspan=\"4\">Memory Allocated [GB] 5.4995 5.4982 5.4942</td><td/></tr><tr><td/><td/><td/><td>Time of Backward</td><td>0.046</td><td>0.088</td><td>0.211</td><td/></tr><tr><td/><td/><td>BPTT M</td><td colspan=\"4\">Memory Allocated [GB] 3.7017 6.4778 11.969</td><td/></tr><tr><td colspan=\"9\">ResNet-19, and VGG-11-and timesteps (T=2, 4, 6). The results demonstrate that rate-based back-</td></tr><tr><td colspan=\"9\">propagation maintains competitive accuracy with BPTT across different architectures and timestep</td></tr><tr><td colspan=\"3\">settings on benchmark datasets.</td><td/><td/><td/><td/><td/></tr></table>", "text": "Comparison results of performance and training costs across various timesteps. All units for time measurements are in seconds per batch. Experiments were conducted on NVIDIA GeForce RTX 4090, with training settings consistent with other experiments.", "type_str": "table", "html": null}, "TABREF8": {"num": null, "content": "<table><tr><td>NeurIPS Paper Checklist</td></tr><tr><td>1. Claims</td></tr><tr><td>Question: Do the main claims made in the abstract and introduction accurately reflect the</td></tr><tr><td>paper's contributions and scope?</td></tr><tr><td>Answer: [Yes]</td></tr></table>", "text": "is no direct negative societal impact since this work centers on enhancing the training efficiency of SNNs. SNNs inherently require less energy for inference compared to ANNs, helping reduce carbon dioxide emissions. The methods developed in this work further optimize SNNs training by improving both memory and time efficiency, potentially reducing the overall resource consumption and environmental footprint of training processes. Regarding limitations, this work primarily compares with BPTT baselines, and there is potential for incorporating techniques in future work. Moreover, the proposed method is tailored for tasks that utilize rate-coding, designed to efficiently capture spatial rate-based feature representations to enhance training; therefore, it necessitates further adaptation to effectively manage sequential tasks. Future efforts may need to delve deeper into adapting the dynamic characteristics of spikes and robustly designing training hyperparameters, ensuring compatibility with rate-based backpropagation and extending applicability to a wider range of applications.", "type_str": "table", "html": null}, "TABREF9": {"num": null, "content": "<table><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: See Supplementary Materials.</td></tr><tr><td>Guidelines:</td></tr><tr><td>• results? Answer: [Yes] Justification: See Appendix C. Guidelines: • The answer NA means that the paper does not include experiments. • The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material. 7. Experiment Statistical Significance Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [Yes] Justification: See Section 5. Guidelines: • The answer NA means that the paper does not include experiments. • The authors should answer \"Yes\" if the results are accompanied by error bars, confi-dence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). Question: For each experiment, does the paper provide sufficient information on the com-puter resources (type of compute workers, memory, time of execution) needed to reproduce the experiments? Answer: [Yes] Justification: See Appendix C. Guidelines: • 8. Experiments Compute Resources •</td></tr><tr><td>: [Yes]</td></tr></table>", "text": "The answer NA means that paper does not include experiments requiring code.• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable). • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted. 6. Experimental Setting/Details Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper). 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines? Answer: [Yes] Justification: This research conforms to the NeurIPS Code of Ethics. Guidelines: • The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics. • If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction). 10. Broader Impacts Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed? Answer: [Yes] Justification: See Appendix E.", "type_str": "table", "html": null}}}}