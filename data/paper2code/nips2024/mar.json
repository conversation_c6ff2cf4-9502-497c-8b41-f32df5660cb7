{"paper_id": "mar", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-24T23:41:55.288591Z"}, "title": "Autoregressive Image Generation without Vector Quantization", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Tian", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "He", "middle": [], "last": "Li", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "Mingyang", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Csail", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "Google", "middle": [], "last": "Deepmind", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Conventional wisdom holds that autoregressive models for image generation are typically accompanied by vector-quantized tokens. We observe that while a discrete-valued space can facilitate representing a categorical distribution, it is not a necessity for autoregressive modeling. In this work, we propose to model the per-token probability distribution using a diffusion procedure, which allows us to apply autoregressive models in a continuous-valued space. Rather than using categorical cross-entropy loss, we define a Diffusion Loss function to model the pertoken probability. This approach eliminates the need for discrete-valued tokenizers. We evaluate its effectiveness across a wide range of cases, including standard autoregressive models and generalized masked autoregressive (MAR) variants. By removing vector quantization, our image generator achieves strong results while enjoying the speed advantage of sequence modeling. We hope this work will motivate the use of autoregressive generation in other continuous-valued domains and applications. Code is available at https://github.com/LTH14/mar.", "pdf_parse": {"paper_id": "mar", "_pdf_hash": "", "abstract": [{"text": "Conventional wisdom holds that autoregressive models for image generation are typically accompanied by vector-quantized tokens. We observe that while a discrete-valued space can facilitate representing a categorical distribution, it is not a necessity for autoregressive modeling. In this work, we propose to model the per-token probability distribution using a diffusion procedure, which allows us to apply autoregressive models in a continuous-valued space. Rather than using categorical cross-entropy loss, we define a Diffusion Loss function to model the pertoken probability. This approach eliminates the need for discrete-valued tokenizers. We evaluate its effectiveness across a wide range of cases, including standard autoregressive models and generalized masked autoregressive (MAR) variants. By removing vector quantization, our image generator achieves strong results while enjoying the speed advantage of sequence modeling. We hope this work will motivate the use of autoregressive generation in other continuous-valued domains and applications. Code is available at https://github.com/LTH14/mar.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Autoregressive models are currently the de facto solution to generative models in natural language processing [38, 39, 3] . These models predict the next word or token in a sequence based on the previous words as input. Given the discrete nature of languages, the inputs and outputs of these models are in a categorical, discrete-valued space. This prevailing approach has led to a widespread belief that autoregressive models are inherently linked to discrete representations.", "cite_spans": [{"start": 110, "end": 114, "text": "[38,", "ref_id": "BIBREF37"}, {"start": 115, "end": 118, "text": "39,", "ref_id": "BIBREF38"}, {"start": 119, "end": 121, "text": "3]", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "As a result, research on generalizing autoregressive models to continuous-valued domains-most notably, image generation-has intensely focused on discretizing the data [6, 13, 40] . A commonly adopted strategy is to train a discrete-valued tokenizer on images, which involves a finite vocabulary obtained by vector quantization (VQ) [51, 41] . Autoregressive models are then operated on the discrete-valued token space, analogous to their language counterparts.", "cite_spans": [{"start": 167, "end": 170, "text": "[6,", "ref_id": "BIBREF5"}, {"start": 171, "end": 174, "text": "13,", "ref_id": "BIBREF12"}, {"start": 175, "end": 178, "text": "40]", "ref_id": "BIBREF39"}, {"start": 332, "end": 336, "text": "[51,", "ref_id": "BIBREF50"}, {"start": 337, "end": 340, "text": "41]", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In this work, we aim to address the following question: Is it necessary for autoregressive models to be coupled with vector-quantized representations? We note that the autoregressive nature, i.e., \"predicting next tokens based on previous ones\", is independent of whether the values are discrete or continuous. What is needed is to model the per-token probability distribution, which can be measured by a loss function and used to draw samples from. Discrete-valued representations can be conveniently modeled by a categorical distribution, but it is not conceptually necessary. If alternative models for per-token probability distributions are presented, autoregressive models can be approached without vector quantization.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "With this observation, we propose to model the per-token probability distribution by a diffusion procedure operating on continuous-valued domains. Our methodology leverages the principles of diffusion models [45, 24, 33, 10] for representing arbitrary probability distributions. Specifically, our method autoregressively predicts a vector z for each token, which serves as a conditioning for a denoising network (e.g., a small MLP). The denoising diffusion procedure enables us to represent an underlying distribution p(x|z) for the output x (Figure 1 ). This small denoising network is 38th Conference on Neural Information Processing Systems (NeurIPS 2024). Given a continuous-valued token x to be predicted, the autoregressive model produces a vector z, which serves as the condition of a denoising diffusion network (a small MLP). This offers a way to model the probability distribution p(x|z) of this token. This network is trained jointly with the autoregressive model by backpropagation. At inference time, with a predicted z, running the reverse diffusion procedure can sample a token following the distribution: x ∼ p(x|z). This method eliminates the need for discrete-valued tokenizers.", "cite_spans": [{"start": 208, "end": 212, "text": "[45,", "ref_id": "BIBREF44"}, {"start": 213, "end": 216, "text": "24,", "ref_id": "BIBREF23"}, {"start": 217, "end": 220, "text": "33,", "ref_id": "BIBREF32"}, {"start": 221, "end": 224, "text": "10]", "ref_id": "BIBREF9"}], "ref_spans": [{"start": 550, "end": 551, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "trained jointly with the autoregressive model, with continuous-valued tokens as the input and target. Conceptually, this small prediction head, applied to each token, behaves like a loss function for measuring the quality of z. We refer to this loss function as Diffusion Loss.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Our approach eliminates the need for discrete-valued tokenizers. Vector-quantized tokenizers are difficult to train and are sensitive to gradient approximation strategies [51, 41, 40, 27] . Their reconstruction quality often falls short compared to continuous-valued counterparts [42] . Our approach allows autoregressive models to enjoy the benefits of higher-quality, non-quantized tokenizers.", "cite_spans": [{"start": 171, "end": 175, "text": "[51,", "ref_id": "BIBREF50"}, {"start": 176, "end": 179, "text": "41,", "ref_id": "BIBREF40"}, {"start": 180, "end": 183, "text": "40,", "ref_id": "BIBREF39"}, {"start": 184, "end": 187, "text": "27]", "ref_id": "BIBREF26"}, {"start": 280, "end": 284, "text": "[42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "To broaden the scope, we further unify standard autoregressive (AR) models [13] and masked generative models [4, 29] into a generalized autoregressive framework (Figure 3 ). Conceptually, masked generative models predict multiple output tokens simultaneously in a randomized order, while still maintaining the autoregressive nature of \"predicting next tokens based on known ones\". This leads to a masked autoregressive (MAR) model that can be seamlessly used with Diffusion Loss.", "cite_spans": [{"start": 75, "end": 79, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 109, "end": 112, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 113, "end": 116, "text": "29]", "ref_id": "BIBREF28"}], "ref_spans": [{"start": 169, "end": 170, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "We demonstrate by experiments the effectiveness of Diffusion Loss across a wide variety of cases, including AR and MAR models. It eliminates the need for vector-quantized tokenizers and consistently improves generation quality. Our loss function can be flexibly applied with different types of tokenizers. Further, our method enjoys the advantage of the fast speed of sequence models. Our MAR model with Diffusion Loss can generate at a rate of < 0.3 second per image while achieving a strong FID of < 2.0 on ImageNet 256×256. Our best model can approach 1.55 FID.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "The effectiveness of our method reveals a largely uncharted realm of image generation: modeling the interdependence of tokens by autoregression, jointly with the per-token distribution by diffusion. This is in contrast with typical latent diffusion models [42, 37] in which the diffusion process models the joint distribution of all tokens. Given the effectiveness, speed, and flexibility of our method, we hope that the Diffusion Loss will advance autoregressive image generation and be generalized to other domains in future research.", "cite_spans": [{"start": 256, "end": 260, "text": "[42,", "ref_id": "BIBREF41"}, {"start": 261, "end": 264, "text": "37]", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Sequence Models for Image Generation. Pioneering efforts on autoregressive image models [17, 50, 49, 36, 7, 6] operate on sequences of pixels. Autoregression can be performed by RNNs [50] , CNNs [49, 7] , and, most lately and popularly, Transformers [36, 6] . Motivated by language models, another series of works [51, 41, 13, 40] model images as discrete-valued tokens. Autoregressive [13, 40] and masked generative models [4, 29] can operate on the discrete-valued token space. But discrete tokenizers are difficult to train, which has recently drawn special focus [27, 54, 32] . Related to our work, the recent work on GIVT [48] also focuses on continuous-valued tokens in sequence models. GIVT and our work both reveal the significance and potential of this direction. In GIVT, the token distribution is represented by Gaussian mixture models. It uses a pre-defined number of mixtures, which can limit the types of distributions it can represent. In contrast, our method leverages the effectiveness of the diffusion process for modeling arbitrary distributions.", "cite_spans": [{"start": 88, "end": 92, "text": "[17,", "ref_id": "BIBREF16"}, {"start": 93, "end": 96, "text": "50,", "ref_id": "BIBREF49"}, {"start": 97, "end": 100, "text": "49,", "ref_id": "BIBREF48"}, {"start": 101, "end": 104, "text": "36,", "ref_id": "BIBREF35"}, {"start": 105, "end": 107, "text": "7,", "ref_id": "BIBREF6"}, {"start": 108, "end": 110, "text": "6]", "ref_id": "BIBREF5"}, {"start": 183, "end": 187, "text": "[50]", "ref_id": "BIBREF49"}, {"start": 195, "end": 199, "text": "[49,", "ref_id": "BIBREF48"}, {"start": 200, "end": 202, "text": "7]", "ref_id": "BIBREF6"}, {"start": 250, "end": 254, "text": "[36,", "ref_id": "BIBREF35"}, {"start": 255, "end": 257, "text": "6]", "ref_id": "BIBREF5"}, {"start": 314, "end": 318, "text": "[51,", "ref_id": "BIBREF50"}, {"start": 319, "end": 322, "text": "41,", "ref_id": "BIBREF40"}, {"start": 323, "end": 326, "text": "13,", "ref_id": "BIBREF12"}, {"start": 327, "end": 330, "text": "40]", "ref_id": "BIBREF39"}, {"start": 386, "end": 390, "text": "[13,", "ref_id": "BIBREF12"}, {"start": 391, "end": 394, "text": "40]", "ref_id": "BIBREF39"}, {"start": 424, "end": 427, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 428, "end": 431, "text": "29]", "ref_id": "BIBREF28"}, {"start": 567, "end": 571, "text": "[27,", "ref_id": "BIBREF26"}, {"start": 572, "end": 575, "text": "54,", "ref_id": "BIBREF53"}, {"start": 576, "end": 579, "text": "32]", "ref_id": "BIBREF31"}, {"start": 627, "end": 631, "text": "[48]", "ref_id": "BIBREF47"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Diffusion for Representation Learning. The denoising diffusion process has been explored as a criterion for visual self-supervised learning. For example, DiffMAE [53] replaces the L2 loss in the original MAE [21] with a denoising diffusion decoder; DARL [30] trains autoregressive models with a denoising diffusion patch decoder. These efforts have been focused on representation learning, rather than image generation. In their scenarios, generating diverse images is not a goal; these methods have not presented the capability of generating new images from scratch.", "cite_spans": [{"start": 162, "end": 166, "text": "[53]", "ref_id": "BIBREF52"}, {"start": 208, "end": 212, "text": "[21]", "ref_id": "BIBREF20"}, {"start": 254, "end": 258, "text": "[30]", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Diffusion for Policy Learning. Our work is conceptually related to Diffusion Policy [8] in robotics. In those scenarios, the distribution of taking an action is formulated as a denoising process on the robot observations, which can be pixels or latents [8, 34] . In image generation, we can think of generating a token as an \"action\" to take. Despite this conceptual connection, the diversity of the generated samples in robotics is less of a core consideration than it is for image generation.", "cite_spans": [{"start": 84, "end": 87, "text": "[8]", "ref_id": "BIBREF7"}, {"start": 253, "end": 256, "text": "[8,", "ref_id": "BIBREF7"}, {"start": 257, "end": 260, "text": "34]", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "In a nutshell, our image generation approach is a sequence model operated on a tokenized latent space [6, 13, 40] . But unlike previous methods that are based on vector-quantized tokenizers (e.g., variants of VQ-VAE [51, 13] ), we aim to use continuous-valued tokenizers (e.g., [42] ). We propose Diffusion Loss that makes sequence models compatible with continuous-valued tokens.", "cite_spans": [{"start": 102, "end": 105, "text": "[6,", "ref_id": "BIBREF5"}, {"start": 106, "end": 109, "text": "13,", "ref_id": "BIBREF12"}, {"start": 110, "end": 113, "text": "40]", "ref_id": "BIBREF39"}, {"start": 216, "end": 220, "text": "[51,", "ref_id": "BIBREF50"}, {"start": 221, "end": 224, "text": "13]", "ref_id": "BIBREF12"}, {"start": 278, "end": 282, "text": "[42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3"}, {"text": "To begin with, we revisit the roles of discrete-valued tokens in autoregressive generation models. Denote as x the ground-truth token to be predicted at the next position. With a discrete tokenizer, x can be represented as an integer: 0 ≤ x < K, with a vocabulary size K. The autoregressive model produces a continuous-valued D-dim vector z ∈ R D , which is then projected by a K-way classifier matrix W ∈ R K×D . Conceptually, this formulation models a categorical probability distribution in the form of p(x|z) = softmax(W z).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Rethinking Discrete-Valued Tokens", "sec_num": "3.1"}, {"text": "In the context of generative modeling, this probability distribution must exhibit two essential properties. (i) A loss function that can measure the difference between the estimated and true distributions. In the case of categorical distribution, this can be simply done by the cross-entropy loss. (ii) A sampler that can draw samples from the distribution x ∼ p(x|z) at inference time. In the case of categorical distribution, this is often implemented as drawing a sample from p(x|z) = softmax(W z/τ ), in which τ is a temperature that controls the diversity of the samples. Sampling from a categorical distribution can be approached by the Gumbel-max method [18] or inverse transform sampling. This analysis suggests that discrete-valued tokens are not necessary for autoregressive models. Instead, it is the requirement of modeling a distribution that is essential. A discrete-valued token space implies a categorical distribution, whose loss function and sampler are simple to define. What we actually need are a loss function and its corresponding sampler for distribution modeling.", "cite_spans": [{"start": 661, "end": 665, "text": "[18]", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "Rethinking Discrete-Valued Tokens", "sec_num": "3.1"}, {"text": "Denoising diffusion models [24] offer an effective framework to model arbitrary distributions. But unlike common usages of diffusion models for representing the joint distribution of all pixels or all tokens, in our case, the diffusion model is for representing the distribution for each token.", "cite_spans": [{"start": 27, "end": 31, "text": "[24]", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "Consider a continuous-valued vector x ∈ R d , which denotes the ground-truth token to be predicted at the next position. The autoregressive model produces a vector z ∈ R D at this position. Our goal is to model a probability distribution of x conditioned on z, that is, p(x|z). The loss function and sampler can be defined following the diffusion models [24, 33, 10] , described next.", "cite_spans": [{"start": 354, "end": 358, "text": "[24,", "ref_id": "BIBREF23"}, {"start": 359, "end": 362, "text": "33,", "ref_id": "BIBREF32"}, {"start": 363, "end": 366, "text": "10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "Loss function. Following [24, 33, 10] , the loss function of an underlying probability distribution p(x|z) can be formulated as a denoising criterion:", "cite_spans": [{"start": 25, "end": 29, "text": "[24,", "ref_id": "BIBREF23"}, {"start": 30, "end": 33, "text": "33,", "ref_id": "BIBREF32"}, {"start": 34, "end": 37, "text": "10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L(z, x) = E ε,t ∥ε -ε θ (x t |t, z)∥ 2 .", "eq_num": "(1)"}], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "Here, ε ∈ R d is a noise vector sampled from N (0, I). The noise-corrupted vector x t is x t = √ ᾱt x + √ 1ᾱt ε, where ᾱt defines a noise schedule [24, 33] . t is a time step of the noise schedule. The noise estimator ε θ , parameterized by θ, is a small MLP network (see Sec. 4). The notation ε θ (x t |t, z) means that this network takes x t as the input, and is conditional on both t and z. As per [46, 47] , Eqn. (1) conceptually behaves like a form of score matching: it is related to a loss function concerning the score function of p(x|z), that is, ∇ log x p(x|z). Diffusion Loss is a parameterized loss function, in the same vein as the adversarial loss [15] or perceptual loss [56] .", "cite_spans": [{"start": 147, "end": 151, "text": "[24,", "ref_id": "BIBREF23"}, {"start": 152, "end": 155, "text": "33]", "ref_id": "BIBREF32"}, {"start": 401, "end": 405, "text": "[46,", "ref_id": "BIBREF45"}, {"start": 406, "end": 409, "text": "47]", "ref_id": "BIBREF46"}, {"start": 662, "end": 666, "text": "[15]", "ref_id": "BIBREF14"}, {"start": 686, "end": 690, "text": "[56]", "ref_id": "BIBREF55"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "It is worth noticing that the conditioning vector z is produced by the autoregressive network: z = f (•), as we will discuss later. The gradient of z = f (•) is propagated from the loss function in Eqn. (1) . Conceptually, Eqn. (1) defines a loss function for training the network f (•).", "cite_spans": [{"start": 203, "end": 206, "text": "(1)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "We note that the expectation E ε,t [•] in Eqn. (1) is over t, for any given z. As our denoising network is small, we can sample t multiple times for any given z. This helps improve the utilization of the loss function, without recomputing z. We sample t by 4 times during training for each image.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "Sampler. At inference time, it is required to draw samples from the distribution p(x|z). Sampling is done via a reverse diffusion procedure [24] :", "cite_spans": [{"start": 140, "end": 144, "text": "[24]", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "x t-1 = 1 √ αt x t -1-αt √ 1-ᾱt ε θ (x t |t, z) + σ t δ.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "Here δ is sampled from the Gaussian distribution N (0, I) and σ t is the noise level at time step t. Starting with x T ∼ N (0, I), this procedure produces a sample x 0 such that x 0 ∼ p(x|z) [24] .", "cite_spans": [{"start": 191, "end": 195, "text": "[24]", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "When using categorical distributions (Sec. 3.1), autoregressive models can enjoy the benefit of having a temperature τ for controlling sample diversity. In fact, existing literature, in both languages and images, has shown that temperature plays a critical role in autoregressive generation. It is desired for the diffusion sampler to offer a temperature counterpart. We adopt the temperature sampling presented in [10] . Conceptually, with temperature τ , one may want to sample from the (renormalized) probability of p(x|z)", "cite_spans": [{"start": 415, "end": 419, "text": "[10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "1 τ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": ", whose score function is1 τ ∇ log x p(x|z). In practice, [10] suggests to either divide ε θ by τ , or scale the noise by τ . We adopt the latter option: we scale σ t δ in the sampler by τ . Intuitively, τ controls the sample diversity by adjusting the noise variance.", "cite_spans": [{"start": 58, "end": 62, "text": "[10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "3.2"}, {"text": "Next, we describe the autoregressive model with Diffusion Loss for image generation. Given a sequence of tokens {x 1 , x 2 , ..., x n } where the superscript 1 ≤ i ≤ n specifies an order, autoregressive models [17, 50, 49, 36, 7, 6] formulate the generation problem as \"next token prediction\":", "cite_spans": [{"start": 210, "end": 214, "text": "[17,", "ref_id": "BIBREF16"}, {"start": 215, "end": 218, "text": "50,", "ref_id": "BIBREF49"}, {"start": 219, "end": 222, "text": "49,", "ref_id": "BIBREF48"}, {"start": 223, "end": 226, "text": "36,", "ref_id": "BIBREF35"}, {"start": 227, "end": 229, "text": "7,", "ref_id": "BIBREF6"}, {"start": 230, "end": 232, "text": "6]", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss for Autoregressive Models", "sec_num": "3.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(x 1 , ..., x n ) = n i=1 p(x i | x 1 , ..., x i-1 ).", "eq_num": "(2)"}], "section": "Diffusion Loss for Autoregressive Models", "sec_num": "3.3"}, {"text": "A network is used to represent the conditional probability p(x i | x 1 , ..., x i-1 ). In our case, x i can be continuous-valued. We can rewrite this formulation in two parts. We first produce a conditioning vector z i by a network (e.g., Transformer [52] ) operating on previous tokens:", "cite_spans": [{"start": 251, "end": 255, "text": "[52]", "ref_id": "BIBREF51"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss for Autoregressive Models", "sec_num": "3.3"}, {"text": "z i = f (x 1 , ..., x i-1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss for Autoregressive Models", "sec_num": "3.3"}, {"text": ").", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss for Autoregressive Models", "sec_num": "3.3"}, {"text": "Then, we model the probability of the next token by p(x i |z i ). Diffusion Loss in Eqn. (1) can be applied on p(x i |z i ). The gradient is backpropagated to z i for updating the parameters of f (•).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss for Autoregressive Models", "sec_num": "3.3"}, {"text": "We show that masked generative models, e.g., MaskGIT [4] and MAGE [29] , can be generalized under the broad concept of autoregression, i.e., next token prediction.", "cite_spans": [{"start": 53, "end": 56, "text": "[4]", "ref_id": "BIBREF3"}, {"start": 66, "end": 70, "text": "[29]", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Unifying Autoregressive and Masked Generative Models", "sec_num": "3.4"}, {"text": "Bidirectional attention can perform autoregression. The concept of autoregression is orthogonal to network architectures: autoregression can be done by RNNs [50] , CNNs [49, 7] , and Transformers [38, 36, 6] . When using Transformers, although autoregressive models are popularly implemented by causal attention, we show that they can also be done by bidirectional attention. See Figure 2 . Note that the goal of autoregression is to predict the next token given the previous tokens; it does not constrain how the previous tokens communicate with the next token.", "cite_spans": [{"start": 157, "end": 161, "text": "[50]", "ref_id": "BIBREF49"}, {"start": 169, "end": 173, "text": "[49,", "ref_id": "BIBREF48"}, {"start": 174, "end": 176, "text": "7]", "ref_id": "BIBREF6"}, {"start": 196, "end": 200, "text": "[38,", "ref_id": "BIBREF37"}, {"start": 201, "end": 204, "text": "36,", "ref_id": "BIBREF35"}, {"start": 205, "end": 207, "text": "6]", "ref_id": "BIBREF5"}], "ref_spans": [{"start": 387, "end": 388, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Unifying Autoregressive and Masked Generative Models", "sec_num": "3.4"}, {"text": "We can adopt the bidirectional attention implementation as done in Masked Autoencoder (MAE) [21] . See Figure 2(b) . Specifically, we first apply an MAE-style encoder 1 on the known tokens (with positional embedding [52] ). Then we concatenate the encoded sequence with mask tokens (with positional embedding added again), and map this sequence with an MAE-style decoder. The positional embedding on the mask tokens can let the decoder know at which positions are to be predicted. Unlike causal attention, here the loss is computed only on the unknown tokens [21] .", "cite_spans": [{"start": 92, "end": 96, "text": "[21]", "ref_id": "BIBREF20"}, {"start": 216, "end": 220, "text": "[52]", "ref_id": "BIBREF51"}, {"start": 559, "end": 563, "text": "[21]", "ref_id": "BIBREF20"}], "ref_spans": [{"start": 110, "end": 114, "text": "2(b)", "ref_id": null}], "eq_spans": [], "section": "Unifying Autoregressive and Masked Generative Models", "sec_num": "3.4"}, {"text": "With the MAE-style trick, we allow all known tokens to see each other, and also allow all unknown tokens to see all known tokens. This full attention introduces better communication across tokens than causal attention. At inference time, we can generate tokens (one or more per step) using this bidirectional formulation, which is a form of autoregression. As a compromise, we cannot use the key-value (kv) cache [44] of causal attention to speed up inference. But as we can generate multiple tokens together, we can reduce generation steps to speed up inference. Full attention across tokens can significantly improve the quality and offer a better speed/accuracy trade-off. (a) A standard, raster-order autoregressive model predicts one next token based on the previous tokens. (b) A random-order autoregressive model predicts the next token given a random order. It behaves like randomly masking out tokens and then predicting one. (c) A Masked Autoregressive (MAR) model predicts multiple tokens simultaneously given a random order, which is conceptually analogous to masked generative models [4, 29] . In all cases, the prediction of one step can be done by causal or bidirectional attention (Figure 2 ).", "cite_spans": [{"start": 413, "end": 417, "text": "[44]", "ref_id": "BIBREF43"}, {"start": 1097, "end": 1100, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 1101, "end": 1104, "text": "29]", "ref_id": "BIBREF28"}], "ref_spans": [{"start": 1205, "end": 1206, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Unifying Autoregressive and Masked Generative Models", "sec_num": "3.4"}, {"text": "Autoregressive models in random orders. To connect to masked generative models [4, 29] , we consider an autoregressive variant in random orders. The model is given a randomly permuted sequence. This random permutation is different for each sample. See Figure 3(b) . In this case, the position of the next token to be predicted needs to be accessible to the model. We adopt a strategy similar to MAE [21] : we add positional embedding (that corresponds to the unshuffled positions) to the decoder layers, which can tell what positions to predict. This strategy is applicable for both causal and bidirectional versions.", "cite_spans": [{"start": 79, "end": 82, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 83, "end": 86, "text": "29]", "ref_id": "BIBREF28"}, {"start": 399, "end": 403, "text": "[21]", "ref_id": "BIBREF20"}], "ref_spans": [{"start": 259, "end": 263, "text": "3(b)", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Unifying Autoregressive and Masked Generative Models", "sec_num": "3.4"}, {"text": "As shown in Figure 3 (b)(c), random-order autoregression behaves like a special form of masked generation, in which one token is generated at a time. We elaborate on this as follows.", "cite_spans": [], "ref_spans": [{"start": 19, "end": 20, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Unifying Autoregressive and Masked Generative Models", "sec_num": "3.4"}, {"text": "Masked autoregressive models. In masked generative modeling [4, 29] , the models predict a random subset of tokens based on known/predicted tokens. This can be formulated as permuting the token sequence by a random order, and then predicting multiple tokens based on previous tokens. See Figure 3(c) . Conceptually, this is an autoregressive procedure, which can be written as estimating the conditional distribution: p({x i , x i+1 ..., x j } | x 1 , ..., x i-1 ), where multiple tokens {x i , x i+1 ..., x j } are to be predicted (i ≤ j). We can write this autoregressive model as:", "cite_spans": [{"start": 60, "end": 63, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 64, "end": 67, "text": "29]", "ref_id": "BIBREF28"}], "ref_spans": [{"start": 295, "end": 299, "text": "3(c)", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Unifying Autoregressive and Masked Generative Models", "sec_num": "3.4"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(x 1 , ..., x n ) = p(X 1 , ..., X K ) = K k p(X k | X 1 , ..., X k-1 ).", "eq_num": "(3)"}], "section": "Unifying Autoregressive and Masked Generative Models", "sec_num": "3.4"}, {"text": "Here, X k = {x i , x i+1 ..., x j } is a set of tokens to be predicted at the k-th step, with ∪ k X k = {x 1 , ..., x n }. In this sense, this is essentially \"next set-of-tokens prediction\", and thus is also a general form of autoregression. We refer to this variant as Masked Autoregressive (MAR) models. MAR is a random-order autoregressive model that can predict multiple tokens simultaneously.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unifying Autoregressive and Masked Generative Models", "sec_num": "3.4"}, {"text": "MAR is conceptually related to MAGE [29] . However, MAR samples tokens by a temperature τ applied on the probability distribution of each token (which is the standard practice in generative language models like GPT). In contrast, MAGE (following MaskGIT [4] ) applies a temperature for sampling the locations of the tokens to be predicted: this is not a fully randomized order, which creates a gap between training-time and inference-time behavior.", "cite_spans": [{"start": 36, "end": 40, "text": "[29]", "ref_id": "BIBREF28"}, {"start": 254, "end": 257, "text": "[4]", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Unifying Autoregressive and Masked Generative Models", "sec_num": "3.4"}, {"text": "This section describes our implementation. We note that the concepts introduced in this paper are general and not limited to specific implementations. More detailed specifics are in Appendix B.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation", "sec_num": "4"}, {"text": "Diffusion Process. Our diffusion process follows [33] . Our noise schedule has a cosine shape, with 1000 steps at training time; at inference time, it is resampled with fewer steps (by default, 100) [33] . Our denoising network predicts the noise vector ε [24] . The loss can optionally include the variational lower bound term L vlb [33] . Diffusion Loss naturally supports classifier-free guidance (CFG) [23] (detailed in Appendix B).", "cite_spans": [{"start": 49, "end": 53, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 199, "end": 203, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 256, "end": 260, "text": "[24]", "ref_id": "BIBREF23"}, {"start": 334, "end": 338, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 406, "end": 410, "text": "[23]", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "4.1"}, {"text": "Denoising MLP. We use a small MLP consisting of a few residual blocks [20] for denoising. Each block sequentially applies a LayerNorm (LN) [1] , a linear layer, SiLU [12] , and another linear layer, merging with a residual connection. By default, we use 3 blocks and a width of 1024 channels.", "cite_spans": [{"start": 70, "end": 74, "text": "[20]", "ref_id": "BIBREF19"}, {"start": 139, "end": 142, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 166, "end": 170, "text": "[12]", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "4.1"}, {"text": "The denoising MLP is conditioned on a vector z produced by the AR/MAR model (see Figure 1 ). The vector z is added to the time embedding of the noise schedule time-step t, which serves as the condition of the MLP in the LN layers via AdaLN [37] .", "cite_spans": [{"start": 240, "end": 244, "text": "[37]", "ref_id": "BIBREF36"}], "ref_spans": [{"start": 88, "end": 89, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Diffusion Loss", "sec_num": "4.1"}, {"text": "Tokenizer. We use the publicly available tokenizers provided by LDM [42] . Our experiments will involve their VQ-16 and KL-16 versions [42] . VQ-16 is a VQ-GAN [13] , i.e., VQ-VAE [51] with GAN loss [15] and perceptual loss [56] ; KL-16 is its counterpart regularized by Kullback-Leibler (KL) divergence, without vector quantization. 16 denotes the tokenizer strides.", "cite_spans": [{"start": 68, "end": 72, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 135, "end": 139, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 160, "end": 164, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 180, "end": 184, "text": "[51]", "ref_id": "BIBREF50"}, {"start": 199, "end": 203, "text": "[15]", "ref_id": "BIBREF14"}, {"start": 224, "end": 228, "text": "[56]", "ref_id": "BIBREF55"}], "ref_spans": [], "eq_spans": [], "section": "Autoregressive and Masked Autoregressive Image Generation", "sec_num": "4.2"}, {"text": "Transformer. Our architecture follows the Transformer [52] implementation in ViT [11] . Given a sequence of tokens from a tokenizer, we add positional embedding [52] and append the class tokens [cls]; then we process the sequence by a Transformer. By default, our Transformer has 32 blocks and a width of 1024, which we refer to as the Large size or -L (∼400M parameters).", "cite_spans": [{"start": 54, "end": 58, "text": "[52]", "ref_id": "BIBREF51"}, {"start": 81, "end": 85, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 161, "end": 165, "text": "[52]", "ref_id": "BIBREF51"}], "ref_spans": [], "eq_spans": [], "section": "Autoregressive and Masked Autoregressive Image Generation", "sec_num": "4.2"}, {"text": "Autoregressive baseline. Causal attention is implemented following the common practice of GPT [38] (Figure 2 (a)). The input sequence is shifted by one token (here, [cls]). Triangular masking [52] is applied to the attention matrix. At inference time, temperature (τ ) sampling is applied. We use kv-cache [44] for efficient inference.", "cite_spans": [{"start": 94, "end": 98, "text": "[38]", "ref_id": "BIBREF37"}, {"start": 192, "end": 196, "text": "[52]", "ref_id": "BIBREF51"}, {"start": 306, "end": 310, "text": "[44]", "ref_id": "BIBREF43"}], "ref_spans": [{"start": 107, "end": 108, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Autoregressive and Masked Autoregressive Image Generation", "sec_num": "4.2"}, {"text": "Masked autoregressive models. With bidirectional attention (Figure 2 (b)), we can predict any number of unknown tokens given any number of known tokens. At training time, we randomly sample a masking ratio [21, 4, 29] in [0.7, 1.0]: e.g., 0.7 means 70% tokens are unknown. Because the sampled sequence can be very short, we always pad 64 [cls] tokens at the start of the encoder sequence, which improves the stability and capacity of our encoding. As in Figure 2 , mask tokens [m] are introduced in the decoder, with positional embedding added. For simplicity, unlike [21] , we let the encoder and decoder have the same size: each has half of all blocks (e.g., 16 in MAR-L).", "cite_spans": [{"start": 206, "end": 210, "text": "[21,", "ref_id": "BIBREF20"}, {"start": 211, "end": 213, "text": "4,", "ref_id": "BIBREF3"}, {"start": 214, "end": 217, "text": "29]", "ref_id": "BIBREF28"}, {"start": 568, "end": 572, "text": "[21]", "ref_id": "BIBREF20"}], "ref_spans": [{"start": 67, "end": 68, "text": "2", "ref_id": null}, {"start": 461, "end": 462, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Autoregressive and Masked Autoregressive Image Generation", "sec_num": "4.2"}, {"text": "At inference, MAR performs \"next set-of-tokens prediction\". It progressively reduces the masking ratio from 1.0 to 0 with a cosine schedule [4, 29] . By default, we use 64 steps in this schedule. Temperature (τ ) sampling is applied. Unlike [4, 29] , MAR always uses fully randomized orders.", "cite_spans": [{"start": 140, "end": 143, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 144, "end": 147, "text": "29]", "ref_id": "BIBREF28"}, {"start": 241, "end": 244, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 245, "end": 248, "text": "29]", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Autoregressive and Masked Autoregressive Image Generation", "sec_num": "4.2"}, {"text": "We experiment on ImageNet [9] at a resolution of 256×256. We evaluate FID [22] and IS [43] , and provide Precision and Recall as references following common practice [10] . We follow the evaluation suite provided by [10] .", "cite_spans": [{"start": 26, "end": 29, "text": "[9]", "ref_id": "BIBREF8"}, {"start": 74, "end": 78, "text": "[22]", "ref_id": "BIBREF21"}, {"start": 86, "end": 90, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 166, "end": 170, "text": "[10]", "ref_id": "BIBREF9"}, {"start": 216, "end": 220, "text": "[10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "Diffusion Loss vs. Cross-entropy Loss. We first compare continuous-valued tokens with Diffusion Loss and standard discrete-valued tokens with cross-entropy loss (Table 1 ). For fair comparisons, the tokenizers (\"VQ-16\" and \"KL-16\") are both downloaded from the LDM codebase [42] . These are popularly used tokenizers (e.g., [13, 42, 37] ). The comparisons are in four variants of AR/MAR. As shown in Table 1 , Diffusion Loss consistently outperforms the cross-entropy counterpart in all cases. Specifically, in MAR (e.g., the default), using Diffusion Loss can reduce FID by relatively ∼50%-60%. This is because the continuous-valued KL-16 has smaller compression loss than VQ-16 (discussed next in Table 2 ), and also because a diffusion process models distributions more effectively than categorical ones.", "cite_spans": [{"start": 274, "end": 278, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 324, "end": 328, "text": "[13,", "ref_id": "BIBREF12"}, {"start": 329, "end": 332, "text": "42,", "ref_id": "BIBREF41"}, {"start": 333, "end": 336, "text": "37]", "ref_id": "BIBREF36"}], "ref_spans": [{"start": 168, "end": 169, "text": "1", "ref_id": "TABREF1"}, {"start": 406, "end": 407, "text": "1", "ref_id": "TABREF1"}, {"start": 705, "end": 706, "text": "2", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Properties of Diffusion Loss", "sec_num": "5.1"}, {"text": "In the following ablations, unless specified, we follow the \"default\" MAR setting in Table 1 .", "cite_spans": [], "ref_spans": [{"start": 91, "end": 92, "text": "1", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Properties of Diffusion Loss", "sec_num": "5.1"}, {"text": "Flexibility of Diffusion Loss. One significant advantage of Diffusion Loss is its flexibility with various tokenizers. We compare several publicly available tokenizers in Table 2 .", "cite_spans": [], "ref_spans": [{"start": 177, "end": 178, "text": "2", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Properties of Diffusion Loss", "sec_num": "5.1"}, {"text": "Diffusion Loss can be easily used even given a VQ tokenizer. We simply treat the continuous-valued latent before the VQ layer as the tokens. This variant gives us 7.82 FID (w/o CFG), compared favorably with 8.79 FID (Table 1 ) of cross-entropy loss using the same VQ tokenizer. This suggests the better capability of diffusion for modeling distributions.", "cite_spans": [], "ref_spans": [{"start": 223, "end": 224, "text": "1", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Properties of Diffusion Loss", "sec_num": "5.1"}, {"text": "This variant also enables us to compare the VQ-16 and KL-16 tokenizers using the same loss. As shown in For comprehensiveness, we also train a KL-16 tokenizer on ImageNet using the code of [42] , noting that the original KL-16 in [42] was trained on OpenImages [28] . The comparison is in the last row of Table 2 . We use this tokenizer in the following explorations. Temperature τ has clear influence on both FID (left) and IS (right). Just like the temperature in discrete-valued autoregression, the temperature here also plays a critical role in continuous-valued autoregression.", "cite_spans": [{"start": 189, "end": 193, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 230, "end": 234, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 261, "end": 265, "text": "[28]", "ref_id": "BIBREF27"}], "ref_spans": [{"start": 311, "end": 312, "text": "2", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Properties of Diffusion Loss", "sec_num": "5.1"}, {"text": "Denoising MLP in Diffusion Loss. We investigate the denoising MLP in Table 3 . Even a very small MLP (e.g., 2M) can lead to competitive results. As expected, increasing the MLP width helps improve the generation quality; we have explored increasing the depth and had similar observations. Note that our default MLP size (1024 width, 21M) adds only ∼5% extra parameters to the MAR-L model. During inference, the diffusion sampler has a decent cost of ∼10% overall running time. Increasing the MLP width has negligible extra cost in our implementation (Table 3 ), partially because the main overhead is not about computation but memory communication.", "cite_spans": [], "ref_spans": [{"start": 75, "end": 76, "text": "3", "ref_id": "TABREF5"}, {"start": 557, "end": 558, "text": "3", "ref_id": "TABREF5"}], "eq_spans": [], "section": "Properties of Diffusion Loss", "sec_num": "5.1"}, {"text": "Sampling Steps of Diffusion Loss. Our diffusion process follows the common practice of DDPM [24, 10] : we train with a 1000-step noise schedule but inference with fewer steps. Figure 4 shows that using 100 diffusion steps at inference is sufficient to achieve a strong generation quality.", "cite_spans": [{"start": 92, "end": 96, "text": "[24,", "ref_id": "BIBREF23"}, {"start": 97, "end": 100, "text": "10]", "ref_id": "BIBREF9"}], "ref_spans": [{"start": 183, "end": 184, "text": "4", "ref_id": null}], "eq_spans": [], "section": "Properties of Diffusion Loss", "sec_num": "5.1"}, {"text": "Temperature of Diffusion Loss. In the case of cross-entropy loss, the temperature is of central importance. Diffusion Loss also offers a temperature counterpart for controlling the diversity and fidelity. Figure 5 shows the influence of the temperature τ in the diffusion sampler (see Sec. 3.2) at inference time. The temperature τ plays an important role in our models, similar to the observations on cross-entropy-based counterparts (note that the cross-entropy results in Table 1 are with their optimal temperatures).", "cite_spans": [], "ref_spans": [{"start": 212, "end": 213, "text": "5", "ref_id": null}, {"start": 481, "end": 482, "text": "1", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Properties of Diffusion Loss", "sec_num": "5.1"}, {"text": "From AR to MAR. Table 1 is also a comparison on the AR/MAR variants, which we discuss next. First, replacing the raster order in AR with random order has a significant gain, e.g., reducing FID from 19.23 to 13.07 (w/o CFG). Next, replacing the causal attention with the bidirectional counterpart leads to another massive gain, e.g., reducing FID from 13.07 to 3.43 (w/o CFG).", "cite_spans": [], "ref_spans": [{"start": 22, "end": 23, "text": "1", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Properties of Generalized Autoregressive Models", "sec_num": "5.2"}, {"text": "The random-order, bidirectional AR is essentially a form of MAR that predicts one token at a time.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Properties of Generalized Autoregressive Models", "sec_num": "5.2"}, {"text": "Predicting multiple tokens ('>1') at each step can effectively reduce the number of autoregressive steps. In Table 1 , we show that the MAR variant with 64 steps slightly trades off generation quality.", "cite_spans": [], "ref_spans": [{"start": 115, "end": 116, "text": "1", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Properties of Generalized Autoregressive Models", "sec_num": "5.2"}, {"text": "A more comprehensive trade-off comparison is discussed next.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Properties of Generalized Autoregressive Models", "sec_num": "5.2"}, {"text": "Speed/accuracy Trade-off. Following MaskGIT [4] , our MAR enjoys the flexibility of predicting multiple tokens at a time. This is controlled by the number of autoregressive steps at inference time. Figure 6 plots the speed/accuracy trade-off. MAR has a better trade-off than its AR counterpart, noting that AR is with the efficient kv-cache.", "cite_spans": [{"start": 44, "end": 47, "text": "[4]", "ref_id": "BIBREF3"}], "ref_spans": [{"start": 205, "end": 206, "text": "6", "ref_id": null}], "eq_spans": [], "section": "Properties of Generalized Autoregressive Models", "sec_num": "5.2"}, {"text": "With Diffusion Loss, MAR also shows a favorable trade-off in comparison with the recently popular Diffusion Transformer (DiT) [37] . As a latent diffusion model, DiT models the interdependence 6 : Speed/accuracy trade-off of the generation process. For MAR, a curve is obtained by different autoregressive steps (8 to 128). For DiT, a curve is obtained by different diffusion steps (50, 75, 150, 250) using its official code. We compare our implementation of AR and MAR. AR is with kv-cache for fast inference. AR/MAR model size is L and DiT model size is DiT-XL. The star marker denotes our default MAR setting used in other ablations. We benchmark FID and speed on ImageNet 256×256 using one A100 GPU with a batch size of 256. across all tokens by the diffusion process. The speed/accuracy trade-off of DiT is mainly controlled by its diffusion steps. Unlike our diffusion process on a small MLP, the diffusion process of DiT involves the entire Transformer architecture. Our method is more accurate and faster. Notably, our method can generate at a rate of < 0.3 second per image with a strong FID of < 2.0.", "cite_spans": [{"start": 126, "end": 130, "text": "[37]", "ref_id": "BIBREF36"}], "ref_spans": [{"start": 193, "end": 194, "text": "6", "ref_id": null}], "eq_spans": [], "section": "Properties of Generalized Autoregressive Models", "sec_num": "5.2"}, {"text": "We compare with the leading systems in Table 4 . We explore various model sizes (see Appendix B) and train for 800 epochs. Similar to autoregressive language models [3] , we observe encouraging scaling behavior. Further investigation into scaling could be promising. Regarding metrics, we report 2.35 FID without CFG, largely outperforming other token-based methods. Our best entry has 1.55 FID and compares favorably with leading systems. Figure 7 shows qualitative results.", "cite_spans": [{"start": 165, "end": 168, "text": "[3]", "ref_id": "BIBREF2"}], "ref_spans": [{"start": 45, "end": 46, "text": "4", "ref_id": "TABREF6"}, {"start": 447, "end": 448, "text": "7", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "Benchmarking with Previous Systems", "sec_num": "5.3"}, {"text": "The effectiveness of Diffusion Loss on various autoregressive models suggests new opportunities: modeling the interdependence of tokens by autoregression, jointly with the per-token distribution by diffusion. This is unlike the common usage of diffusion that models the joint distribution of all tokens. Our strong results on image generation suggest that autoregressive models or their extensions are powerful tools beyond language modeling. These models do not need to be constrained by vector-quantized representations. We hope our work will motivate the research community to explore sequence models with continuous-valued representations in other domains. Figure 8 : Failure cases. Similar to existing methods, our system can produce results with noticeable artifacts. For each pair, we show MAR-H and DiT-XL's results of the same class. The leftmost example of DiT is taken from their paper [37] ; the others are obtained from their official code.", "cite_spans": [{"start": 897, "end": 901, "text": "[37]", "ref_id": "BIBREF36"}], "ref_spans": [{"start": 668, "end": 669, "text": "8", "ref_id": null}], "eq_spans": [], "section": "Discussion and Conclusion", "sec_num": "6"}, {"text": "Limitations. Beyond demonstrating the potential of our method for image generation, this paper acknowledges its limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Limitations and Broader Impacts", "sec_num": null}, {"text": "First of all, our image generation system can produce images with noticeable artifacts (Figure 8 ). This limitation is commonly observed in existing methods, especially when trained on controlled, academic data (e.g., ImageNet). Research-driven models trained on ImageNet still have a noticeable gap in visual quality in comparison with commercial models trained on massive data.", "cite_spans": [], "ref_spans": [{"start": 95, "end": 96, "text": "8", "ref_id": null}], "eq_spans": [], "section": "A Limitations and Broader Impacts", "sec_num": null}, {"text": "Second, our image generation system relies on existing pre-trained tokenizers. The quality of our system can be limited by the quality of these tokenizers. Pre-training better tokenizers is beyond the scope of this paper. Nevertheless, we hope our work will make it easier to use continuous-valued tokenizers to be developed in the future.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Limitations and Broader Impacts", "sec_num": null}, {"text": "Last, we note that given the limited computational resources, we have primarily tested our method on the ImageNet benchmark. Further validation is needed to assess the scalability and robustness of our approach in more diverse and real-world scenarios.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Limitations and Broader Impacts", "sec_num": null}, {"text": "Broader Impacts. Our primary aim is to advance the fundamental research on generative models, and we believe it will be beneficial to this field. An immediate application of our method is to extend it to large visual generation models, e.g., text-to-image or text-to-video generation. Our approach has the potential to significantly reduce the training and inference cost of these large models. At the same time, our method may suggest the opportunity to replace traditional loss functions with Diffusion Loss in many applications. On the negative side, our method learns statistics from the training dataset, and as such may reflect the bias in the data; the image generation system may be misused to generate disinformation, which warrants further consideration.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Limitations and Broader Impacts", "sec_num": null}, {"text": "Classifier-free guidance (CFG). To support CFG [23] , at training time, the class condition is replaced with a dummy class token for 10% of the samples [23] . At inference time, the model is run with the given class token and the dummy token, providing two outputs z c and z u . The predicted noise ε is then modified [23] as:", "cite_spans": [{"start": 47, "end": 51, "text": "[23]", "ref_id": "BIBREF22"}, {"start": 152, "end": 156, "text": "[23]", "ref_id": "BIBREF22"}, {"start": 318, "end": 322, "text": "[23]", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "B Additional Implementation Details", "sec_num": null}, {"text": "ε = ε θ (x t |t, z u ) + ω • (ε θ (x t |t, z c ) -ε θ (x t |t, z u )),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Additional Implementation Details", "sec_num": null}, {"text": "where ω is the guidance scale. At inference time, we use a CFG schedule following [5] . We sweep the optimal guidance scale and temperature combination for each model.", "cite_spans": [{"start": 82, "end": 85, "text": "[5]", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "B Additional Implementation Details", "sec_num": null}, {"text": "Training. By default, the models are trained using the AdamW optimizer [31] for 400 epochs.", "cite_spans": [{"start": 71, "end": 75, "text": "[31]", "ref_id": "BIBREF30"}], "ref_spans": [], "eq_spans": [], "section": "B Additional Implementation Details", "sec_num": null}, {"text": "The weight decay and momenta for AdamW are 0.02 and (0.9, 0.95). We use a batch size of 2048 and a learning rate (lr) of 8e-4. Our models with Diffusion Loss are trained with a 100-epoch linear lr warmup [16] , followed by a constant [37] lr schedule. The cross-entropy counterparts are trained with a cosine lr schedule, which works better for them. Following [37, 25] , we maintain the exponential moving average (EMA) of the model parameters with a momentum of 0.9999. Pseudo-code illustrating the concept of Diffusion Loss. Here the conditioning vector z is the output from the AR/MAR model. The gradient is backpropagated to z. For simplicity, here we omit the code for inference rescheduling, temperature and the loss term for variational lower bound [10] , which can be easily incorporated.", "cite_spans": [{"start": 204, "end": 208, "text": "[16]", "ref_id": "BIBREF15"}, {"start": 234, "end": 238, "text": "[37]", "ref_id": "BIBREF36"}, {"start": 361, "end": 365, "text": "[37,", "ref_id": "BIBREF36"}, {"start": 366, "end": 369, "text": "25]", "ref_id": "BIBREF24"}, {"start": 757, "end": 761, "text": "[10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "B Additional Implementation Details", "sec_num": null}, {"text": "Our training is mainly done on 16 servers with 8 V100 GPUs each. Training a 400 epochs MAR-L model takes ∼2.6 days on these GPUs. As a comparison, training a DiT-XL/2 and LDM-4 model for the same number of epochs on this cluster takes 4.6 and 9.5 days, respectively.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Compute Resources.", "sec_num": null}, {"text": "MAR (regardless of the loss used) is conceptually related to MAGE [29] . Besides implementation differences (e.g., architecture specifics, hyper-parameters), a major conceptual difference between MAR and MAGE is in the scanning order at inference time. In MAGE, following MaskGIT [4] , the locations of the next tokens to be predicted are determined on-the-fly by the sample confidence at each location, i.e., the more confident locations are more likely to be selected at each step [4, 29] . In contrast, MAR adopts a fully randomized order, and its temperature sampling is applied to each token. Table 5 compares this difference in controlled settings. The first line is our MAR implementation but using MAGE's on-the-fly ordering strategy, which has similar results as the simpler random order counterpart. Fully randomized ordering can make the training and inference process consistent regarding the distribution of orders; it also allows us to adopt token-wise temperature sampling in a way similar to autoregressive language models (e.g., GPT [38, 39, 3] ). Table 5 : To compare conceptually with MAGE, we run MAR's inference using the MAGE strategy of determining the order on the fly by confidence sampling across the spatial domain. These entries are all based on the tokenizers provided by the LDM codebase [42] . ", "cite_spans": [{"start": 66, "end": 70, "text": "[29]", "ref_id": "BIBREF28"}, {"start": 280, "end": 283, "text": "[4]", "ref_id": "BIBREF3"}, {"start": 483, "end": 486, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 487, "end": 490, "text": "29]", "ref_id": "BIBREF28"}, {"start": 1050, "end": 1054, "text": "[38,", "ref_id": "BIBREF37"}, {"start": 1055, "end": 1058, "text": "39,", "ref_id": "BIBREF38"}, {"start": 1059, "end": 1061, "text": "3]", "ref_id": "BIBREF2"}, {"start": 1318, "end": 1322, "text": "[42]", "ref_id": "BIBREF41"}], "ref_spans": [{"start": 604, "end": 605, "text": "5", "ref_id": null}, {"start": 1071, "end": 1072, "text": "5", "ref_id": null}], "eq_spans": [], "section": "C Comparison between MAR and MAGE", "sec_num": null}, {"text": "Our MAR+DiffLoss approach can also be directly applied to model the RGB pixel space without the need for an image tokenizer. To demonstrate this, we conducted an experiment on ImageNet 64×64, grouping every 4×4 pixels into a single token for the Diffusion Loss to model. A MAR-L+DiffLoss model trained for 400 epochs achieved an FID of 2.93, demonstrating the potential to eliminate the need for tokenizers in autoregressive image generation. However, as commonly observed in the diffusion model literature, directly modeling the pixel space is significantly more computationally expensive than using a tokenizer. For MAR+DiffLoss, directly modeling pixels at higher resolutions might require either a much longer sequence length for the autoregressive transformer or a substantially larger network for the Diffusion Loss to handle larger patches. We leave this exploration for future work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Additional Comparisons D.1 Autoregressive Image Generation in Pixel Space", "sec_num": null}, {"text": "Following previous works, we also report results on ImageNet at a resolution of 512×512, compared with leading systems (Table 6 ). For simplicity, we use the KL-16 tokenizer, which gives a sequence length of 32×32 on a 512×512 image. Other settings follow the MAR-L configuration described in Table 4 . Our method achieves an FID of 2.74 without CFG and 1.73 with CFG. Our results are competitive with those of previous systems. Due to limited resources, we have not trained the larger MAR-H on ImageNet 512×512, which is expected to have better results.", "cite_spans": [], "ref_spans": [{"start": 126, "end": 127, "text": "6", "ref_id": "TABREF9"}, {"start": 299, "end": 300, "text": "4", "ref_id": "TABREF6"}], "eq_spans": [], "section": "D.2 ImageNet 512×512", "sec_num": null}, {"text": "A naïve baseline for continuous-valued tokens is to compute the Mean Squared Error (MSE, i.e., L2) loss directly between the predictions and the target tokens. In the case of a raster-order AR model, using the L2 loss introduces no randomness and thus cannot generate diverse samples. In the case of the MAR models with the L2 loss, the only randomness is the sequence order; the prediction at a location is deterministic for any given order. In our experiment, we have trained an MAR model with the L2 loss, which as expected leads to a disastrous FID score (>100).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3 L2 Loss vs. Diff Loss", "sec_num": null}, {"text": "NeurIPS Paper Checklist Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3 L2 Loss vs. Diff Loss", "sec_num": null}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3 L2 Loss vs. Diff Loss", "sec_num": null}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach. For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3 L2 Loss vs. Diff Loss", "sec_num": null}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Answer: [No] Justification: Following common practice in the generative modeling literature, we do not report error bars in this paper because of the heavy computation overheads.", "cite_spans": [{"start": 8, "end": 12, "text": "[No]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• It should be clear whether the error bar is the standard deviation or the standard error of the mean. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [Yes] Justification: see Appendix A.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Justification: We will require the users to adhere to usage guidelines for our released models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [Yes] Justification: We properly cite the original assets in the paper.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Here the terminology of encoder/decoder is in the sense of a general Autoencoder, following MAE[21]. It is not related to whether the computation is casual/bidirectional in Transformers[52].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "Acknowledgements. <PERSON><PERSON><PERSON> was supported by the Mathworks Fellowship during this project. We thank <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> for helpful discussion. We thank Google TPU Research Cloud (TRC) for granting us access to TPUs, and Google Cloud Platform for supporting GPU resources.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "acknowledgement", "sec_num": null}, {"text": "Justification: This paper does not include theoretical contribution. Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems. • The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorem<PERSON> and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: See section 4 and Appendix B. Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?• If this information is not available online, the authors are encouraged to reach out to the asset's creators. 13 • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [{"start": 301, "end": 303, "text": "13", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Layer normalization", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1607.06450"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Layer normalization. arXiv:1607.06450, 2016.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "All are worth words: a vit backbone for score-based diffusion models", "authors": [{"first": "Fan", "middle": [], "last": "Bao", "suffix": ""}, {"first": "Chongxuan", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "NeurIPS 2022 Workshop on Score-Based Methods", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. All are worth words: a vit backbone for score-based diffusion models. In NeurIPS 2022 Workshop on Score-Based Methods, 2022.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Language models are few-shot learners", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Subbiah", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ariel", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Child", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Winter", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hesse", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Litwin", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Chess", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Language mod- els are few-shot learners. In NeurIPS, 2020.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Masked generative image Transformer", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Han", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "Ce", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Maskgit", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. MaskGIT: Masked generative image Transformer. In CVPR, 2022.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Muse: Textto-image generation via masked generative Transformers", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Han", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuanzhen", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Muse: Text- to-image generation via masked generative Transformers. In ICML, 2023.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Generative pretraining from pixels", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Child", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Jun", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Generative pretraining from pixels. In ICML, 2020.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "PixelSNAIL: An improved autoregressive generative model", "authors": [{"first": "Xi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. PixelSNAIL: An improved autoregres- sive generative model. In ICML, 2018.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Diffusion policy: Visuomotor policy learning via action diffusion", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Siyuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zhenji<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Co<PERSON>ineau", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}], "year": 2023, "venue": "RSS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Diffusion policy: Visuomotor policy learning via action diffusion. In RSS, 2023.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "ImageNet: A large-scale hierarchical image database", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. ImageNet: A large-scale hierar- chical image database. In CVPR, 2009.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Diffusion models beat GANs on image synthesis", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON>. Diffusion models beat GANs on image synthesis. In NeurIPS, 2021.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "Xiaohua", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. An image is worth 16x16 words: Transformers for image recognition at scale. In ICLR, 2021.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Sigmoid-weighted linear units for neural network function approximation in reinforcement learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "Elfwing", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Uchibe", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Neural networks", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Sigmoid-weighted linear units for neural network function approximation in reinforcement learning. Neural networks, 2018.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Taming Transformers for high-resolution image synthesis", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ommer", "suffix": ""}], "year": 2021, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Taming Transformers for high-resolution image syn- thesis. In CVPR, 2021.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Masked diffusion Transformer is a strong image synthesizer", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Pan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ming-Ming", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shuicheng", "middle": [], "last": "Yan", "suffix": ""}], "year": 2023, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> Yan. Masked diffusion Transformer is a strong image synthesizer. In ICCV, 2023.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Generative adversarial nets", "authors": [{"first": "<PERSON>", "middle": ["J"], "last": "Goodfellow", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Pouget-Abadie", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Warde-Farley", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Courville", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Generative adversarial nets. In NeurIPS, 2014.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "<PERSON><PERSON>, and <PERSON><PERSON>, large minibatch SGD: Training ImageNet in 1 hour", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Lukasz", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Aapo", "middle": [], "last": "Kyrola", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1706.02677"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> Accurate, large minibatch SGD: Training ImageNet in 1 hour. arXiv:1706.02677, 2017.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Deep autoregressive networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Dani<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>ni<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Deep autoregressive networks. In ICML, 2014.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Statistical theory of extreme valuse and some practical applications", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Gumbel", "middle": [], "last": "", "suffix": ""}], "year": 1954, "venue": "Nat. Bur. Standards Appl. Math. Ser", "volume": "33", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Statistical theory of extreme valuse and some practical applications. Nat. Bur. Standards Appl. Math. Ser. 33, 1954.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "DiffiT: Diffusion vision Transformers for image generation", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Jiaming", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Vah<PERSON>t", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.02139"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. DiffiT: Diffusion vision Transformers for image generation. arXiv:2312.02139, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Deep residual learning for image recognition", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "Xiangyu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shaoqing", "middle": [], "last": "Ren", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}], "year": 2016, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep residual learning for image recognition. In CVPR, 2016.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Masked autoencoders are scalable vision learners", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Saining", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yang<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Masked autoen- coders are scalable vision learners. In CVPR, 2022.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "GANs trained by a two time-scale update rule converge to a local nash equilibrium", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sepp", "middle": [], "last": "<PERSON><PERSON>reiter", "suffix": ""}], "year": 2017, "venue": "NIP", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. GANs trained by a two time-scale update rule converge to a local nash equilibrium. In NIP, 2017.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Classifier-free diffusion guidance", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2207.12598"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Classifier-free diffusion guidance. arXiv:2207.12598, 2022.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Denoising diffusion probabilistic models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Denoising diffusion probabilistic models. In NeurIPS, 2020.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Analyzing and improving the training dynamics of diffusion models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.02696"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Analyzing and improving the training dynamics of diffusion models. arXiv:2312.02696, 2023.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Understanding diffusion objectives as the ELBO with simple data augmentation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}], "year": 2023, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Understanding diffusion objectives as the ELBO with simple data augmentation. In NeurIPS, 2023.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "UViM: A unified modeling approach for vision with learned guiding codes", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Pi<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xiaohua", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. UViM: A unified modeling approach for vision with learned guiding codes. NeurIPS, 2022.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Openimages: A public dataset for large-scale multi-label and multi-class image classification", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Alldrin", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ferrari", "suffix": ""}, {"first": "Sami", "middle": [], "last": "Abu-El-Haija", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Rom", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Veit", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Belongie", "suffix": ""}, {"first": "Victor", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Gal", "middle": [], "last": "Chechik", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Cai", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Openimages: A public dataset for large-scale multi-label and multi-class image classification. 2017.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "MAGE: Masked generative encoder to unify representation learning and image synthesis", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shlok", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Han", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. MAGE: Masked generative encoder to unify representation learning and image synthesis. In CVPR, 2023.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Denoising autoregressive representation learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>g", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2403.05196"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Denoising autoregressive representation learning. arXiv preprint arXiv:2403.05196, 2024.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Decoupled weight decay regularization", "authors": [{"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Decoupled weight decay regularization. In ICLR, 2019.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Finite scalar quantization: VQ-VAE made simple", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Tschannen", "suffix": ""}], "year": 2024, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Finite scalar quantization: VQ-VAE made simple. In ICLR, 2024.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Improved denoising diffusion probabilistic models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON><PERSON>. Improved denoising diffusion probabilistic models. In ICML, 2021.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Octo: An open-source generalist robot policy", "authors": [{"first": "Octo", "middle": [], "last": "Model Team", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ghosh", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Black", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "You", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sanketi", "suffix": ""}, {"first": "<PERSON>uan", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dorsa", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Chelsea", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "RSS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Octo Model Team, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Octo: An open-source generalist robot policy. In RSS, 2024.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Consistency Decoder", "authors": [{"first": "", "middle": [], "last": "Openai", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "OpenAI. Consistency Decoder, 2024. URL https://github.com/openai/consistencydecoder.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "ICML", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "Lukasz", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ku", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Tran", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Image Transformer. In ICML, 2018.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Scalable diffusion models with Transformers", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Saining", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. Scalable diffusion models with Transformers. In ICCV, 2023.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Improving language understanding by generative pre-training", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Improving language understanding by generative pre-training. 2018.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Language models are unsupervised multitask learners", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Child", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Language models are unsupervised multitask learners. 2019.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Zero-shot text-to-image generation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chelsea", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Zero-shot text-to-image generation. In ICML, 2021.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Generating diverse high-fidelity images with VQ-VAE-2", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. Generating diverse high-fidelity images with VQ- VAE-2. In NeurIPS, 2019.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "High-resolution image synthesis with latent diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ommer", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. High-resolution image synthesis with latent diffusion models. In CVPR, 2022.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Improved techniques for training GANs", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Goodfellow", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Zaremba", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xi", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Improved techniques for training GANs. In NeurIPS, 2016.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Fast Transformer decoding: One write-head is all you need", "authors": [{"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1911.02150"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>. Fast Transformer decoding: One write-head is all you need. arXiv:1911.02150, 2019.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Deep unsupervised learning using nonequilibrium thermodynamics", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Surya", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Deep unsupervised learn- ing using nonequilibrium thermodynamics. In ICML, 2015.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Generative modeling by estimating gradients of the data distribution", "authors": [{"first": "<PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Generative modeling by estimating gradients of the data distribution. In NeurIPS, 2019.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Score-based generative modeling through stochastic differential equations", "authors": [{"first": "<PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Poole", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Score-based generative modeling through stochastic differential equations. In ICLR, 2021.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "GIVT: Generative infinite-vocabulary Transformers", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.02116"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. GIVT: Generative infinite-vocabulary Trans- formers. arXiv:2312.02116, 2023.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Conditional image generation with PixelCNN decoders", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>l", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Conditional image generation with PixelCNN decoders. In NeurIPS, 2016.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Pixel recurrent neural networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>l", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Kavukcuoglu", "suffix": ""}], "year": 2016, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Pixel recurrent neural networks. In ICML, 2016.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Neural discrete representation learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Oriol", "middle": [], "last": "<PERSON>yal<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Kavukcuoglu", "suffix": ""}], "year": 2017, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Neural discrete representation learning. In NeurIPS, 2017.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Attention is all you need", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "Llion", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "Illia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. In NeurIPS, 2017.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Diffusion models as masked autoencoders", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Mangalam", "suffix": ""}, {"first": "Po-Yao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yang<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Fan", "suffix": ""}, {"first": "Hu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hui<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Yuille", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Diffusion models as masked autoencoders. In ICCV, 2023.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "MAGVIT: Masked generative video Transformer", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Han", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuan", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Jiang", "suffix": ""}], "year": 2023, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. MAGVIT: Masked generative video Transformer. In CVPR, 2023.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Language model beats diffusion-tokenizer is key to visual generation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Luca", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Agrim", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "Boqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Jiang", "suffix": ""}], "year": 2024, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Language model beats diffusion-tokenizer is key to visual generation. In ICLR, 2024.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "The unreasonable effectiveness of deep features as a perceptual metric", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Isola", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. The unreasonable effec- tiveness of deep features as a perceptual metric. In CVPR, 2018.", "links": null}}, "ref_entries": {"FIGREF0": {"uris": null, "fig_num": "1", "type_str": "figure", "num": null, "text": "Figure1: Diffusion Loss. Given a continuous-valued token x to be predicted, the autoregressive model produces a vector z, which serves as the condition of a denoising diffusion network (a small MLP). This offers a way to model the probability distribution p(x|z) of this token. This network is trained jointly with the autoregressive model by backpropagation. At inference time, with a predicted z, running the reverse diffusion procedure can sample a token following the distribution: x ∼ p(x|z). This method eliminates the need for discrete-valued tokenizers."}, "FIGREF2": {"uris": null, "fig_num": "3", "type_str": "figure", "num": null, "text": "Figure 3: Generalized Autoregressive Models.(a) A standard, raster-order autoregressive model predicts one next token based on the previous tokens. (b) A random-order autoregressive model predicts the next token given a random order. It behaves like randomly masking out tokens and then predicting one. (c) A Masked Autoregressive (MAR) model predicts multiple tokens simultaneously given a random order, which is conceptually analogous to masked generative models[4,29]. In all cases, the prediction of one step can be done by causal or bidirectional attention (Figure2)."}, "FIGREF3": {"uris": null, "fig_num": "45", "type_str": "figure", "num": null, "text": "Figure 4: Sampling steps of Diffusion Loss. We show the FID (left) and IS (right) w.r.t. the number of diffusive sampling steps. Using 100 steps is sufficient to achieve a strong generation quality."}, "FIGREF5": {"uris": null, "fig_num": null, "type_str": "figure", "num": null, "text": "Figure6: Speed/accuracy trade-off of the generation process. For MAR, a curve is obtained by different autoregressive steps (8 to 128). For DiT, a curve is obtained by different diffusion steps (50, 75, 150, 250) using its official code. We compare our implementation of AR and MAR. AR is with kv-cache for fast inference. AR/MAR model size is L and DiT model size is DiT-XL. The star marker denotes our default MAR setting used in other ablations. We benchmark FID and speed on ImageNet 256×256 using one A100 GPU with a batch size of 256."}, "FIGREF6": {"uris": null, "fig_num": "7", "type_str": "figure", "num": null, "text": "Figure 7: Qualitative Results. We show selected examples of class-conditional generation on Ima-geNet 256×256 using MAR-H with Diffusion Loss."}, "FIGREF7": {"uris": null, "fig_num": "9", "type_str": "figure", "num": null, "text": "Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines? Answer: [Yes] Justification: We follow the NeurIPS Code of Ethics."}, "TABREF0": {"num": null, "html": null, "type_str": "table", "content": "<table><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td>Figure 2:</td></tr><tr><td/><td/><td/><td colspan=\"5\">next token prediction</td><td/><td/><td/></tr><tr><td>[s]</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td>[m]</td></tr><tr><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td/><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td></tr><tr><td colspan=\"6\">loss loss loss loss loss loss</td><td/><td/><td/><td/><td>loss</td></tr><tr><td/><td/><td colspan=\"2\">(a) causal</td><td/><td/><td/><td colspan=\"4\">(b) bidirectional</td></tr></table>", "text": "Bidirectional attention can do autoregression.In contrast to conventional wisdom, the broad concept of \"autoregression\" (next token prediction) can be done by either causal or bidirectional attention. (a) Causal attention restricts each token to attend only to current/previous tokens. With input shifted by one start token [s], it is valid to compute loss on all tokens at training time. (b) Bidirectional attention allows each token to see all tokens in the sequence. Following MAE[21], mask tokens [m] are applied in a middle layer, with positional embedding added. This setup only computes loss on unknown tokens, but it allows for full attention capabilities across the sequence, enabling better communication across tokens. This setup can generate tokens one by one at inference time, which is a form of autoregression. It also allows us to predict multiple tokens simultaneously."}, "TABREF1": {"num": null, "html": null, "type_str": "table", "content": "<table><tr><td>w/o CFG</td><td>w/ CFG</td></tr></table>", "text": "Diffusion Loss vs. Cross-entropy Loss. The tokenizers are VQ-16 (discrete) and KL-16 (continuous), both from the LDM codebase[42] for fair comparisons. Diffusion Loss, with continuous-valued tokens, is better than its cross-entropy counterpart with discrete-valued tokens, consistently observed across all variants of AR and MAR. All entries are implemented by us under the same setting: AR/MAR-L (∼400M parameters), 400 epochs, ImageNet 256×256."}, "TABREF2": {"num": null, "html": null, "type_str": "table", "content": "<table><tr><td>loss</td><td>src</td><td>tokenizer arch</td><td colspan=\"4\">w/o CFG raw seq rFID↓ FID↓ # tokens IS↑</td><td colspan=\"2\">w/ CFG FID↓ IS↑</td></tr><tr><td>Diff Loss</td><td>[42] [42] [42] [35] [42]  †</td><td colspan=\"2\">VQ-16 KL-16 KL-8 Consistency 32 2 16 2 16 2 16 2 16 2 16 2 32 2 16 2 KL-16 16 2 16 2</td><td>5.87 1.43 1.20 1.30 1.22</td><td>7.82 3.50 4.33 5.76 2.85</td><td>151.7 201.4 180.0 170.6 214.0</td><td>3.64 1.98 2.05 3.23 1.97</td><td>258.5 290.3 283.9 271.0 291.2</td></tr></table>", "text": "Flexibility of Diffusion Loss. Diffusion Loss can support different types of tokenizers. (i) VQ tokenizers: we treat the continuous-valued latent before VQ as the tokens. (ii) Tokenizers with a mismatched stride (here, 8): we group 2×2 tokens into a new token for sequence modeling. (iii) Consistency Decoder [35], a non-VQ tokenizer of a different decoder architecture. Here, rFID denotes the reconstruction FID of the tokenizer on the ImageNet training set. Settings in this table for all entries: MAR-L, 400 epochs, ImageNet 256×256."}, "TABREF3": {"num": null, "html": null, "type_str": "table", "content": "<table/>", "text": "VQ-16 has a much worse reconstruction FID (rFID) than KL-16, which consequently leads to a much worse generation FID (e.g., 7.82 vs. 3.50 in Table2).Interestingly, Diffusion Loss also enables us to use tokenizers with mismatched strides. In Table2, we study a KL-8 tokenizer whose stride is 8 and output sequence length is 32×32. Without increasing the sequence length of the generator, we group 2×2 tokens into a new token. Despite the mismatch, we are able to obtain decent results, e.g., KL-8 gives us 2.05 FID, vs. KL-16's 1.98 FID."}, "TABREF5": {"num": null, "html": null, "type_str": "table", "content": "<table/>", "text": "Denoising MLP in Diffusion Loss. The denoising MLP is small and efficient. Here, the inference time involves the entire generation model, and the Transformer's size is 407M. Settings: MAR-L, 400 epochs, Im-ageNet 256×256, 3 MLP blocks."}, "TABREF6": {"num": null, "html": null, "type_str": "table", "content": "<table><tr><td/><td/><td/><td>w/o CFG</td><td>w/ CFG</td><td/></tr><tr><td/><td colspan=\"2\">#params FID↓</td><td colspan=\"4\">IS↑ Pre.↑ Rec.↑ FID↓ IS↑ Pre.↑ Rec.↑</td></tr><tr><td>pixel-based</td><td/><td/><td/><td/><td/></tr><tr><td>ADM [10] VDM++ [26]</td><td colspan=\"6\">554M 10.94 101.0 0.69 0.63 4.59 186.7 0.82 0.52 2B 2.40 225.3 --2.12 267.7 --</td></tr><tr><td>vector-quantized tokens</td><td/><td/><td/><td/><td/></tr><tr><td>Autoreg. w/ VQGAN [13] MaskGIT [4] MAGE [29] MAGVIT-v2 [55]</td><td>1.4B 227M 230M 307M</td><td colspan=\"2\">15.78 6.18 182.1 0.80 0.51 78.3 --6.93 195.8 --3.65 200.5 --</td><td>---1.78 319.4 ---</td><td>----</td><td>----</td></tr><tr><td>continuous-valued tokens</td><td/><td/><td/><td/><td/></tr><tr><td>LDM-4  † [42] U-ViT-H/2-G [2] DiT-XL/2 [37] DiffiT [19] MDTv2-XL/2 [14] GIVT [48] MAR-B, Diff Loss MAR-L, Diff Loss MAR-H, Diff Loss</td><td colspan=\"6\">400M 10.56 103.5 0.71 0.62 3.60 247.7 0.87 0.48 501M ----2.29 263.9 0.82 0.57 675M 9.62 121.5 0.67 0.67 2.27 278.2 0.83 0.57 -----1.73 276.5 0.80 0.62 676M 5.06 155.6 0.72 0.66 1.58 314.7 0.79 0.65 304M 5.67 -0.75 0.59 3.35 -0.84 0.53 208M 3.48 192.4 0.78 0.58 2.31 281.7 0.82 0.57 479M 2.60 221.4 0.79 0.60 1.78 296.0 0.81 0.60 943M 2.35 227.8 0.79 0.62 1.55 303.7 0.81 0.62</td></tr></table>", "text": "System-level comparison on ImageNet 256×256 conditional generation. Diffusion Loss enables Masked Autoregression to achieve leading results in comparison with previous systems. † : LDM operates on continuous-valued tokens, though this result uses a quantized tokenizer."}, "TABREF7": {"num": null, "html": null, "type_str": "table", "content": "<table><tr><td>Algorithm 1 Diffusion Loss: PyTorch-like Pseudo-code</td></tr><tr><td>class DiffusionLoss(nn.Module)</td></tr><tr><td>def __init__(depth, width):</td></tr><tr><td># SimpleMLP takes in x_t, timestep, and condition, and outputs predicted noise.</td></tr><tr><td>self.net = SimpleMLP(depth, width)</td></tr><tr><td># GaussianDiffusion offers forward and backward functions q_sample and p_sample.</td></tr><tr><td>self.diffusion = GaussianDiffusion()</td></tr><tr><td># Given condition z and ground truth token x, compute loss</td></tr><tr><td>def loss(self, z, x):</td></tr><tr><td># sample random noise and timestep</td></tr><tr><td>noise = torch.randn(x.shape)</td></tr><tr><td>timestep = torch.randint(0, self.diffusion.num_timesteps, x.size(0))</td></tr><tr><td># sample x_t from x</td></tr><tr><td>x_t = self.diffusion.q_sample(x, timestep, noise)</td></tr><tr><td># predict noise from x_t</td></tr><tr><td>noise_pred = self.net(x_t, timestep, z)</td></tr><tr><td># L2 loss</td></tr><tr><td>loss = ((noise_pred -noise) ** 2).mean()</td></tr><tr><td># optional: loss += loss_vlb</td></tr><tr><td>return loss</td></tr><tr><td># Given condition and noise, sample x using reverse diffusion process</td></tr><tr><td>def sample(self, z, noise):</td></tr><tr><td>x = noise</td></tr></table>", "text": "To explore our method's scaling behavior, we study three model sizes described as follows. In addition to MAR-L, we explore a smaller model (MAR-B) and"}, "TABREF9": {"num": null, "html": null, "type_str": "table", "content": "<table><tr><td/><td/><td colspan=\"2\">w/o CFG</td><td>w/ CFG</td></tr><tr><td/><td colspan=\"2\">#params FID↓</td><td>IS↑ FID↓ IS↑</td></tr><tr><td>pixel-based</td><td/><td/></tr><tr><td>ADM [10] VDM++ [26]</td><td colspan=\"3\">554M 23.24 2B 2.99 232.2 2.65 278.1 58.1 7.72 172.7</td></tr><tr><td>vector-quantized tokens</td><td/><td/></tr><tr><td>MaskGIT [4] MAGVIT-v2 [55]</td><td>227M 307M</td><td colspan=\"2\">7.32 156.0 3.07 213.1 1.91 324.3 --</td></tr><tr><td>continuous-valued tokens</td><td/><td/></tr><tr><td>U-ViT-H/2-G [2] DiT-XL/2 [37] DiffiT [19] GIVT [48] EDM2-XXL [25] MAR-L, Diff Loss</td><td>501M 675M -304M 1.5B 481M</td><td colspan=\"2\">-12.03 105.3 3.04 240.8 -4.05 263.8 --2.67 252.1 8.35 ---1.91 -1.81 -2.74 205.2 1.73 279.9</td></tr></table>", "text": "System-level comparison on ImageNet 512×512 conditional generation. MAR's CFG scale is set to 4.0; other settings follow the MAR-L configuration described in Table4."}, "TABREF10": {"num": null, "html": null, "type_str": "table", "content": "<table><tr><td>1. Claims</td></tr><tr><td>Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: This paper presents an autoregresive image generation method without vector quantization.</td></tr><tr><td>Guidelines:</td></tr><tr><td>• Answer: [Yes]</td></tr><tr><td>Justification: See Appendix A.</td></tr></table>", "text": "The answer NA means that the abstract and introduction do not include the claims made in the paper.• The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.2. LimitationsQuestion: Does the paper discuss the limitations of the work performed by the authors?"}, "TABREF11": {"num": null, "html": null, "type_str": "table", "content": "<table><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: Guidelines:</td></tr><tr><td>•</td></tr><tr><td>Answer: [NA]</td></tr></table>", "text": "Code and checkpoints are available at https://github.com/LTH14/mar. The answer NA means that paper does not include experiments requiring code. • Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable). • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted."}, "TABREF12": {"num": null, "html": null, "type_str": "table", "content": "<table><tr><td>8. Experiments Compute Resources</td></tr><tr><td>Question: For each experiment, does the paper provide sufficient information on the com-puter resources (type of compute workers, memory, time of execution) needed to reproduce the experiments?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: See Appendix B.</td></tr><tr><td>Guidelines:</td></tr><tr><td>•</td></tr></table>", "text": "• It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper)."}}}}