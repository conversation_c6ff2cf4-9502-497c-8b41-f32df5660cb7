{"paper_id": "InfLLM", "title": "InfLLM: Training-Free Long-Context Extrapolation for LLMs with an Efficient Context Memory", "abstract": "Large language models (LLMs) have emerged as a cornerstone in real-world applications with lengthy streaming inputs (e.g., LLM-driven agents). However, existing LLMs, pre-trained on sequences with a restricted maximum length, cannot process longer sequences due to the out-of-domain and distraction issues. Common solutions often involve continual pre-training on longer sequences, which will introduce expensive computational overhead and uncontrollable change in model capabilities. In this paper, we unveil the intrinsic capacity of LLMs for understanding extremely long sequences without any fine-tuning. To this end, we introduce a training-free memory-based method, InfLLM. Specifically, InfLLM stores distant contexts into additional memory units and employs an efficient mechanism to lookup token-relevant units for attention computation. Thereby, InfLLM allows LLMs to efficiently process long sequences with a limited context window and well capture long-distance dependencies. Without any training, InfLLM enables LLMs that are pre-trained on sequences consisting of a few thousand tokens to achieve comparable performance with competitive baselines that continually train these LLMs on long sequences. Even when the sequence length is scaled to 1, 024K, InfLLM still effectively captures long-distance dependencies. Our code can be found at https://github.com/thunlp/InfLLM.", "pdf_parse": {"paper_id": "InfLLM", "abstract": [{"text": "Large language models (LLMs) have emerged as a cornerstone in real-world applications with lengthy streaming inputs (e.g., LLM-driven agents). However, existing LLMs, pre-trained on sequences with a restricted maximum length, cannot process longer sequences due to the out-of-domain and distraction issues. Common solutions often involve continual pre-training on longer sequences, which will introduce expensive computational overhead and uncontrollable change in model capabilities. In this paper, we unveil the intrinsic capacity of LLMs for understanding extremely long sequences without any fine-tuning. To this end, we introduce a training-free memory-based method, InfLLM. Specifically, InfLLM stores distant contexts into additional memory units and employs an efficient mechanism to lookup token-relevant units for attention computation. Thereby, InfLLM allows LLMs to efficiently process long sequences with a limited context window and well capture long-distance dependencies. Without any training, InfLLM enables LLMs that are pre-trained on sequences consisting of a few thousand tokens to achieve comparable performance with competitive baselines that continually train these LLMs on long sequences. Even when the sequence length is scaled to 1, 024K, InfLLM still effectively captures long-distance dependencies. Our code can be found at https://github.com/thunlp/InfLLM.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Recently, large language models (LLMs) have achieved profound accomplishments in various tasks (<PERSON> et al., 2020; <PERSON><PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2023; Meta, 2024) . Their ability to follow complex instructions shed light on the realization of artificial general intelligence (OpenAI, 2023; <PERSON><PERSON><PERSON> et al., 2022) . With the blooming of LLM-driven applications, such as agent construction (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2024a) and embodied robotics (<PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) , enhancing the capability of LLMs to process streaming long sequences become increasingly crucial. For instance, LLM-driven agents are required to process information continuously received from external environments based on all their historical memories, necessitating a robust capability for handling long streaming sequences.", "section": "Introduction", "sec_num": "1"}, {"text": "Due to limitations caused by unseen lengthy inputs (<PERSON> et al., 2023) and distracting noisy contexts (<PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2023) , most LLMs, pre-trained on sequences consisting of only a few thousand tokens, cannot process longer sequences (<PERSON> et al., 2022; <PERSON> et al., 2023) .", "section": "Introduction", "sec_num": "1"}, {"text": "Common solutions usually involve continually training LLMs on longer sequences but further result in substantial costs and require large-scale high-quality long-sequence datasets (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) . And the continual training process on longer sequences may weaken the performance of LLMs on short contexts (<PERSON><PERSON> et al., 2024) . In view of this, improving the length generalizability of LLMs without further training receives extensive attention, trying to make LLMs trained on short sequences directly applicable to long sequences.", "section": "Introduction", "sec_num": "1"}, {"text": "In this paper, we propose a training-free memory-based approach, named InfLLM, for streamingly processing extremely long sequences with limited computational costs. Specifically, InfLLM incorporate the sliding window attention (<PERSON> et al., 2023; <PERSON> et al., 2023) with an efficient context memory, where each token only attends to local contexts and relevant contexts from the memory. Considering the sparsity of attention score matrices, processing each token typically requires only a small portion of its contexts (<PERSON> et al., 2023b) , and the remaining irrelevant contexts act as noise, leading to attention distraction issues (<PERSON><PERSON> et al., 2023) . We thus construct an external memory containing distant context information. Only relevant information within the memory is selected for each computation step, and other irrelevant noises are ignored. Owing to this, LLMs can understand whole long sequences using a finite-size window and avoid noisy contexts.", "section": "Introduction", "sec_num": "1"}, {"text": "The vast amount of noisy context tokens in long sequences poses significant challenges to effective and efficient memory lookup. To address these challenges, we design a block-level context memory mechanism. Specifically, InfLLM organizes past key-value vectors into blocks, each containing a continuous token sequence. Within each block, the semantically most significant tokens that receive the highest attention scores are selected as the unit representation for subsequent relevance computation in memory lookup. This design offers two primary benefits: (1) Effective Lookup: The coherent semantics of each block can more effectively fulfill the requirements for relevant information retrieval compared to single tokens. The selection of unit representations minimizes the interference of unimportant tokens in relevance computation, enhancing the overall hit rate of memory lookup. (2) Efficient Lookup: The block-level memory unit eliminates the need for per-token relevance computation, significantly reducing computational costs. Moreover, block-level units ensure contiguous memory access, thus minimizing memory loading costs and enhancing computational efficiency. Furthermore, considering the infrequent usage of most units, InfLLM offloads all units on CPU memory and dynamically retains the frequently used units on GPU memory, significantly reducing GPU memory usage. Notably, the block-level memory mechanism in InfLLM does not involve any additional training, and can be directly applied to any LLMs.", "section": "Introduction", "sec_num": "1"}, {"text": "To evaluate the effectiveness of InfLLM, we employ Mistral-7B-inst-v0.2 (<PERSON> et al., 2023) and Llama-3-8B-Instruct (Meta, 2024) as base models, which are pre-trained on the sequences containing no more than 32K and 8K tokens. We use two widely-used benchmarks, ∞-<PERSON><PERSON> (<PERSON> et al., 2023a) and <PERSON><PERSON><PERSON> (<PERSON> et al., 2023) , for evaluation. Especially, the average sequence length in ∞-Bench exceeds 100K tokens, which is challenging for most existing LLMs. Compared to typical methods that continually train LLMs on longer sequences, the experimental results demonstrate that InfLLM enables the LLMs pre-trained on the sequences containing a few thousand tokens to achieve comparable performance without any additional training. Moreover, we examine InfLLM on the sequences containing 1, 024K tokens, and InfLLM can still effectively capture long-distance dependencies, demonstrating the potential of InfLLM in scenarios involving long streaming inputs.", "section": "Introduction", "sec_num": "1"}, {"text": "Enabling LLMs to process long sequences has been extensively studied (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) and can generally be categorized into two main approaches: context length extrapolation and efficient context computation. The former aims to enable LLMs trained on short sequences to process much longer sequences. The latter focuses on enhancing the computational efficiency of attention layers, allowing efficient pre-training LLMs from scratch to process longer sequences. Although the focus of this paper is context length extrapolation, we also detailedly introduce efficient context computation. We also present the relevant works for memory-based models.", "section": "Related Work", "sec_num": "2"}, {"text": "Context Length Extrapolation. Due to the high computational and memory requirements, the training of LLMs is often restricted to short sequences. Directly applying LLMs to long sequences will suffer from out-of-domain and distraction challenges caused by lengthy and noisy inputs (<PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2023) . Consequently, context length extrapolation has garnered attention as a method to improve the sequence length for LLMs without incurring additional training. The earliest approaches involve designing new relative positional encoding mechanisms during pre-training (Press et al., 2022; <PERSON> et al., 2023) . Subsequent studies mainly focus on the widely-used rotary position embedding (RoPE) (<PERSON> et al., 2021) , and propose to achieve length extrapolation by downscaling or reusing the original position indices (<PERSON> et al., 2023b; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023a; <PERSON> et al., 2024; <PERSON> et al., 2024) . These works can alleviate the out-of-domain issue from the unseen length, but can not alleviate the distraction challenge of noisy contexts. To address this, <PERSON> et al. (2023) and <PERSON> et al. (2023) employ the sliding window attention mechanism and directly discard all distant contexts to streamingly read extremely long sequences. However, as these models overlook information from distant tokens, they can not capture the long-distance dependencies for long-text understanding. In this paper, InfLLM utilizes the sliding window attention mechanism, and additionally constructs an efficient context memory to provide LLMs with relevant context information, enabling LLMs to effectively read and understand extremely long sequences.", "section": "Related Work", "sec_num": "2"}, {"text": "Efficient Context Computation. The quadratic computational complexity of the attention layers is a primary factor limiting the lengthy sequence-processing capabilities of LLMs. Thus, numerous scholars have endeavored to design efficient attention mechanisms, including the utilization of sparse attention (<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2019) , approximating attention computations using kernel functions (<PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2020; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020) , and replacing the attention layer with linear-complexity state-space models (<PERSON><PERSON> et al., 2022; <PERSON>u & Dao, 2023) . These approaches necessitate a modification in the model architecture, requiring retraining the models. Simultaneously, many researchers enhance the inference efficiency by evicting useless key-value vectors to reduce computation (<PERSON> et al., 2023b; <PERSON> et al., 2024; <PERSON><PERSON> et al., 2023) . These methods can not extrapolate the context window of LLMs without further training due to out-of-domain issues caused by unseen positions. Recently, some researchers begin to explore the intrinsic sparse attention patterns of long-context LLMs and discard the redundant attention computation for acceleration (<PERSON> et al., 2024) .", "section": "Related Work", "sec_num": "2"}, {"text": "Memory-based Models. Memory networks have been studied for decades, which are proven effective in providing models with additional knowledge and information storage capabilities (<PERSON> et al., 2014; <PERSON> et al., 2015; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2015; <PERSON> et al., 2016) . With the success of pretrained models, memory layers have also been gradually applied in the training processes of recurrent transformer layers, enabling models to process long sequences recursively (<PERSON> et al., 2019; <PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2024) . These works split sequences into segments, encoding each segment individually, and use memory to store context information from preceding segments. While these approaches are similar in concept to InfLLM, they involve modifications to the model architecture and requires further training the whole model. Besides, most existing memory-based methods focus on token-level memory units (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2023) , which require a lot of time to build retrieval indexes for large-scale tokens in each input long sequence. Some methods also adopt block-level memory (<PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2023; <PERSON><PERSON> et al., 2023) , these methods highlight the process of training effective block representations with long sequence data. In contrast, we aim to explore the inherent characteristics of LLMs, and propose a training-free memory module for long-text understanding.", "section": "Related Work", "sec_num": "2"}, {"text": "As shown in Figure 1 , InfLLM builds a training-free context memory to efficiently provide highlyrelevant contexts for each token, endowing the sliding window attention mechanism with the ability to capture long-distance dependencies.", "section": "Methodology", "sec_num": "3"}, {"text": "The main restrictions for improving the length generalizability of LLMs come from the out-of-domain and distraction issues caused by the lengthy and noisy contexts. To address these, following previous works (<PERSON> et al., 2023; <PERSON> et al., 2023) , we adopt the sliding window attention mechanism, which only considers local tokens for each step. Additionally, we construct an extra context memory module to provide relevant context information to capture long-distance dependencies.", "section": "Overall Framework", "sec_num": "3.1"}, {"text": "Figure 1 : The illustration of InfLLM. Here, the current tokens refer to tokens that need to be encoded in the current computation step. The past key-value vectors can be divided into the initial tokens, evicted tokens, and local tokens, arranged the furthest to the nearest relative to the current tokens. For each computation step, the context window consists of the initial tokens, relevant memory units, and local tokens. Specifically, we denote the long input sequence as s = {t i } l i=1 . Due to the limited GPU memory, instead of encoding the whole s at once, we encode the input sequence s chunk-by-chunk and generate the output token-by-token. For each computation step, the inputs consist of past key-value vectors P = {(k j , v j )} l P j=1 and current tokens X = {t i+l P } l X i=1 . For encoding steps, l X equals the chunk size, and for decoding steps, l X equals one.", "section": "Overall Framework", "sec_num": "3.1"}, {"text": "According to the distances from current tokens, we can divide P into three groups: initial tokens, I = P [1:l I ] , evicted tokens, E = P [l I +1:l P -l L ] , and local tokens, L = P [l P -l L +1:l P ] , arranged from the furthest to the nearest relative to the current tokens. Here, l P , l I , l L refer to the length of past key-value vectors, initial tokens, and the local window size. All evicted tokens, E, are stored in the context memory, consisting of multiple memory units. For each step, InfLLM concatenates the initial tokens, relevant memories units from context memory, and local tokens to form the current key-value cache, C = Concat(I, f (X, E), L). f (•) refers to the lookup operation of context memory. The attention output is calculated as:", "section": "Overall Framework", "sec_num": "3.1"}, {"text": "O = Attn [QX, <PERSON><PERSON>(C k , KX), <PERSON><PERSON>(C v , VX)] .", "section": "Overall Framework", "sec_num": "3.1"}, {"text": "Here, Q, K, and V are parameters in attention layers, C k and C v refer to the key and value vectors in C. If f (•) always returns empty sets, InfLLM is degenerated into LM-Infinite (<PERSON> et al., 2023) and Streaming-LLM (<PERSON> et al., 2023) , which directly discards distant contexts.", "section": "Overall Framework", "sec_num": "3.1"}, {"text": "Previous findings indicate that the attention score matrices of LLMs are sparse, and we can generate the same outputs with only a small portion of key-value vectors preserved (<PERSON> et al., 2023b) . Inspired by this, we design a context memory to efficiently look up relevant contexts from large-scale evicted tokens and ignore irrelevant ones to save computational costs. The most intuitive way is to construct a memory consisting of token-level memory units for every past key-value vectors, and every attention head separately, which would result in massive memory units, unacceptable computation, and non-contiguous memory access costs. Thus, considering the local semantic coherence of long sequences, we split the past key-value vectors into blocks, each serving as a memory unit, and conduct memory lookup at the block level to reduce the costs while preserving the performance.", "section": "Context Memory", "sec_num": "3.2"}, {"text": "In this subsection, we will introduce the details of the block-level memory units. Then we present the method to assign positional embeddings for selected relevant memory units and cache management for the context memory.", "section": "Context Memory", "sec_num": "3.2"}, {"text": "Block-Level Memory Units. Block-level memory units can save computation costs compared to token-level ones. It also poses new challenges for unit representations, which are supposed to contain the semantics of the entire unit for effective relevance score computation and be memory-efficient for context length scalability. Traditional methods usually involve training an additional encoder to project a given unit into a low-dimension vector. Inspired by the token redundancy in hidden states (<PERSON><PERSON> et al., 2020; <PERSON> et al., 2020) , we select several representative tokens from the entail blocks as the unit representation. For the m-th token, we define the representative score as:", "section": "Context Memory", "sec_num": "3.2"}, {"text": "r m = 1 l L l L j=1 q m+j • k m ,", "section": "Context Memory", "sec_num": "3.2"}, {"text": "where q m+j is the query vector for (m + j)-th token and k m is the key vector m-th token. Intuitively, r m represents the significance of the m-th token in its corresponding local window, indicating the extent of its influence on other tokens within the local window. The computation of representative scores requires no additional parameters.", "section": "Context Memory", "sec_num": "3.2"}, {"text": "Formally, given the evicted tokens, E, we split it into several memory units, each containing l bs tokens.", "section": "Context Memory", "sec_num": "3.2"}, {"text": "For each unit, the r k tokens with the highest representative scores are selected as representative tokens.", "section": "Context Memory", "sec_num": "3.2"}, {"text": "Generally, r k is a small positive integer. Let us denote a memory unit as", "section": "Context Memory", "sec_num": "3.2"}, {"text": "B = {(k B j , v B j )} l bs j=1", "section": "Context Memory", "sec_num": "3.2"}, {"text": ", and the representative tokens of this unit as", "section": "Context Memory", "sec_num": "3.2"}, {"text": "R(B) = {(k B bj , v B bj )} r k j=1 .", "section": "Context Memory", "sec_num": "3.2"}, {"text": "For the memory lookup phrase, only k m units with the highest relevance scores are loaded for the current attention computation. We calculate the relevance score between B and current tokens X as:", "section": "Context Memory", "sec_num": "3.2"}, {"text": "sim(X, B) = l X i=1 r k j=1 q i+l P • k B bj .", "section": "Context Memory", "sec_num": "3.2"}, {"text": "Notably, the representative tokens selection is a training-free method to obtain the unit representations.", "section": "Context Memory", "sec_num": "3.2"}, {"text": "Here, we can also train an additional encoder to generate more expressive unit representations, which we leave for future work.", "section": "Context Memory", "sec_num": "3.2"}, {"text": "Positional Encoding. Existing LLM training usually employs a finite number of positional encodings, which encounter out-of-domain distribution challenges when directly applied to longer sequence processing (<PERSON> et al., 2023) . Besides, in InfLLM, the current key-value cache is composed of some discontinuous text blocks, and directly assigning continuous positional encodings to them would also lead to mismatch issues and confuse the model. Therefore, inspired by previous works (<PERSON><PERSON> et al., 2020; Su, 2023) , we assign all tokens beyond the local window size with the same positional encodings. Specifically, the distance between tokens in context memory units and current tokens is set as l L .", "section": "Context Memory", "sec_num": "3.2"}, {"text": "Cache Management. To enable LLMs to process extremely long sequence streams while capturing the semantic relevance contained in the long contexts, we need to retain all memory units and look up them at each computation step. Considering the infrequent usage of most units, we employ an offloading mechanism, storing most memory units in CPU memory and only preserving the representative tokens and memory units needed in current steps in GPU memory. Additionally, given the semantic coherence of long sequences, where adjacent tokens often require similar memory units, we allocate a cache space in GPU memory, managed using a least recently used strategy. This approach allows for efficient encoding of extremely long sequences using limited GPU memory.", "section": "Context Memory", "sec_num": "3.2"}, {"text": "From the observation, our offloading mechanism enables InfLLM to process sequences consisting of 100K tokens with only 26G VRAM. Besides, the miss rate of our GPU cache is quite low, which means the offloading mechanism does not introduce significant time overhead in memory loading while saving GPU memory usage. The details can be found in the Appendix.", "section": "Context Memory", "sec_num": "3.2"}, {"text": "Furthermore, for extremely long sequences, the representative tokens of each unit can also be offloaded to the CPU memory, constructing an efficient k-nearest-neighbor index, and thereby further reducing computational complexity.", "section": "Context Memory", "sec_num": "3.2"}, {"text": "Datasets. We adopt representative tasks in a widely-used long document benchmark, ∞-<PERSON><PERSON> (<PERSON> et al., 2023a) for evaluation. We adopt the English datasets for evaluation as the base models are mainly pre-trained on English corpus. The datasets in ∞-Bench cover diverse tasks including question answering, summarization, context retrieval, and mathematic computing. The average length for ∞-Bench is 145.1K. The 95% quantile for sequence lengths is 214K, which is far beyond the maximum length of the base models. Detailed statistics and task descriptions of these datasets are listed in the Appendix. Besides, we also conduct an evaluation on <PERSON><PERSON>en<PERSON> (<PERSON> et al., 2023) . The results for LongBench can be found in the Appendix.", "section": "Settings", "sec_num": "4.1"}, {"text": "Baseline Models. To verify the effectiveness of our proposed method, we compare InfLLM with the following competitive baseline models:", "section": "Settings", "sec_num": "4.1"}, {"text": "(1) Original models: we present the performance of the original LLMs without context length extrapolation.", "section": "Settings", "sec_num": "4.1"}, {"text": "(2) Position downscaling and resuing: NTKaware scaled RoPE (NTK) (LocalLLaMA, 2023) designs a nonlinear interpolation method, which basically changes the rotation base of RoPE. SelfExtend reuse the position ids across neighboring tokens, which makes the extended relative positions in the scope of the training context window.", "section": "Settings", "sec_num": "4.1"}, {"text": "(3) Sliding window: these methods apply the sliding window mechanism to discard distant contexts, including LM-Infinite (Infinite) (<PERSON> et al., 2023) and StreamingLLM (Stream) (<PERSON> et al., 2023) . Therefore, for each attention computation step, the input length does not exceed the context window.", "section": "Settings", "sec_num": "4.1"}, {"text": "(5) Key-value eviction: KV eviction methods aim to discard useless key-value vectors during long sequence processing and thus are usually used to reduce the computation complexity. We present the results of a widely-used key-value eviction method, H2O (<PERSON> et al., 2023b) . The key-value eviction method cannot generalize to longer sequences due to the unseen position embeddings and is expected to achieve unsatisfactory performance.", "section": "Settings", "sec_num": "4.1"}, {"text": "Here, InfLLM and the models with the sliding window mechanism can be used to process extremely long streaming inputs. For NTK and SelfExtend, we extend the context window to 128K, which enables LLMs to process most instances in ∞-Bench.", "section": "Settings", "sec_num": "4.1"}, {"text": "In this paper, we aim to enable LLMs trained with limited sequence length to read and understand extremely long sequences without further training. We adopt Mistral-7B-Instruct-v0.2 (<PERSON> et al., 2023) and Llama-3-8B-Instruct (Meta, 2024) as our base models. The maximum length of Mistral-7B-Instruct-v0.2 and Llama-3-8B-Instruct is 32K and 8K, respectively.", "section": "Implementation Details", "sec_num": "4.2"}, {"text": "For our model, we set the encoding chunk size as 512, and the memory unit size for past key-value vectors, l bs , as 128. The number of representative tokens, r k , is set as 4. For both Mistral-based and Llama-3-based InfLLM, we set the local window size as 4K. For Mistral-based InfLLM, we load 96 relevant memory units for each step, and for Llama-3-based InfLLM, we load 32 relevant memory units. The number of initial tokens is set as 128 for LM-Infinite, StreamingLLM, and InfLLM to cover the system prompts and task descriptions. We adopt FlashAttention (Dao, 2023) to accelerate experiments for all baseline models. Please refer to the Appendix for more details.", "section": "Implementation Details", "sec_num": "4.2"}, {"text": "The results for Mistral-based models and Llama-3-based models are reported in Table 1 . From the results, we can observe that: (1) Compared to models with the sliding window mechanism, which can also read extremely long sequences, our method demonstrates a significant performance improvement. This indicates that the context memory in InfLLM can accurately supplement LLMs with relevant contextual information, enabling efficient and effective understanding and reasoning on long sequences.", "section": "Main Results", "sec_num": "4.3"}, {"text": "(2) The position downscaling and resuing methods, NTK and SelfExtend, tend to compromise model performance while extending the sequence length to 128K. That is because these models cannot address the distraction issue caused by noisy contexts. In contrast, our model can consistently enhance performance for extremely long sequences. We successfully generalize Llama-3 from a 8K length to more than 16 times its length, achieving commendable performance on the ∞-Bench.", "section": "Main Results", "sec_num": "4.3"}, {"text": "(3) The position downscaling and resuing methods can increase the maximum sequence length of LLMs but also raise the computational and memory costs, limiting these methods' application. In contrast, InfLLM utilizes block-level memory and offloading mechanism, enabling efficient processing of long sequences within limited resources.", "section": "Main Results", "sec_num": "4.3"}, {"text": "In this paper, we focus on expanding the context window of LLMs without additional training. In this section, we compare InfLLM with models that undergo continual training on long sequences in terms of both performance and efficiency. Specifically, we select Llama-3-8B-Instruct-Gradient-1048k (Llama-1M)3 , which have been further fine-tuned on long-text data and chat datasets, extending its context window to 1048K. Besides, we also employ InfLLM on the Llama-1M, where we set the local window as 4K and selected memory size as 4K. We present the results on ∞-Bench, the GPU memory usage, and time consumption in Table 2 . From the results, we can observe that: (1) Compared to models that have undergone continual training on long sequences, InfLLM can achieve comparable or even superior results without any additional training. This suggests that LLMs inherently possess the capability to identify key information in long sequences and to understand and reason effectively. Notably, Llama-1M requires 512 GPUs for continual training, which is unaffordable for many researchers. In contrast, InfLLM does not require any training, which indicates the practicability of InfLLM.", "section": "Comparing to Models with Continual Training", "sec_num": "4.4"}, {"text": "(2) In terms of efficiency, InfLLM achieves a 34% decrease in time consumption while using only 34% of the GPU memory compared to the full-attention models. Moreover, at longer sequence lengths of 256K tokens, the full-attention baseline fails due to out-of-memory errors, while InfLLM can efficiently process sequences up to 1024K tokens on a single GPU.", "section": "Comparing to Models with Continual Training", "sec_num": "4.4"}, {"text": "(3) InfLLM can also be directly combined with the model with continual training and achieve comparable or even superior results with only 8K context window. It indicates that InfLLM can also serve as an efficient way to improve the inference speed.", "section": "Comparing to Models with Continual Training", "sec_num": "4.4"}, {"text": "InfLLM leverages the intrinsic capacity of LLMs to construct a context memory for gathering token-relevant information, a concept similar to retrieval augmented generation (RAG) (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2021) . However, compared to using RAG, where historical contexts are treated as a searchable database for long-sequence understanding (<PERSON> et al., 2023) , InfLLM has several advantages: (1) Training-Free: RAG requires additional retrieval data to train a retrieval model, whereas InfLLM is training-free and applicable to any LLMs. Besides, RAG also necessitates finetuning LLMs to adapt to the inputs augmented by the retrieved knowledge.", "section": "Comparing to Retrieval-Augmented Generation", "sec_num": "4.5"}, {"text": "(2) Broader Applicability: RAG models are usually limited by the performance of their retrieval components. Besides, existing retrieval models will suffer from out-of-distribution issues, struggling to perform well on tasks outside their training distribution (<PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) . This limitation adversely affects the overall performance of the RAG system. In contrast, InfLLM has no specific requirements for tasks and can be feasibly used for long sequences. To verify the generalization capabilities of InfLLM, we conduct experiments to comparing RAG and InfLLM on three context retrieval tasks. We utilize E5-mistral-7B-instruct (<PERSON> et al., 2024b) as the retrieval model. The results are shown in Table 3. Our findings demonstrate that even without additional data or training, InfLLM can consistently outperform RAG models, underscoring its superior generalization capabilities. The dependency on an external retrieval model makes RAG less flexible in handling diverse tasks.", "section": "Comparing to Retrieval-Augmented Generation", "sec_num": "4.5"}, {"text": "InfLLM relies on the context memory to look up relevant information. We further explore the impact of core components in the context memory, specifically the representative tokens and memory units.", "section": "The Impact of Memory Settings", "sec_num": "4.6"}, {"text": "The results are shown in Figure 2 .", "section": "The Impact of Memory Settings", "sec_num": "4.6"}, {"text": "Different Number of Representative Tokens. InfLLM splits key-value vectors into memory units and selects several representative tokens from the unit to serve as the unit representations. Consequently, the ability of these representative tokens to semantically represent the entire unit directly impacts the model's performance. We conduct experiments with the number of representative tokens as {1, 2, 4, 8}. The results are shown in Figure 2a . It is observed that as the number of representative tokens increases, there is a trend of improvement in the model performance, which indicates that more representative tokens tend to better represent the semantic content of the memory units. However, it is noted that when the number of representative tokens reaches 8, there is a slight performance decrease. This decline can be attributed to the inclusion of semantically irrelevant tokens as unit representations. More efficient and powerful unit representations will further enhance model performance for future work.", "section": "The Impact of Memory Settings", "sec_num": "4.6"}, {"text": "Different Number of Selected Units. The selected units are utilized to provide relevant context to LLMs. We conduct experiments with the number of units set as {2, 4, 8, 16, 32, 64, 96, 128}. From Figire 2b, we can observe that as the number of selected units increases from 1 to 32, the model performance significantly improves, which is attributed to that more units imply a greater recall rate of relevant content. Larger unit quantity also leads to an increase in the required memory scheduling time and the computational time for attention. Therefore, further enhancing lookup accuracy remains a crucial direction for improving the efficiency of InfLLM.", "section": "The Impact of Memory Settings", "sec_num": "4.6"}, {"text": "Different Memory Unit Size. Each memory unit is supposed to be a coherent semantic unit. Excessively large unit sizes can hinder precise lookup, while a small size will increase the computational overhead of memory lookup. We evaluate InfLLM with the unit size as {32, 64, 128, 256} and keep the total context length as 12K. The results are shown in Figure 2c . It can be observed that the optimal unit size varies for different tasks due to the varying characteristics of input sequences. For example, in Retrieve.KV, a key-value pair constitutes a semantic unit, while in Math.Find, a single number represents a semantic unit. Employing heuristic rules to segment context can easily lead to suboptimal performance. Therefore, exploring how to dynamically segment context is an important direction for future research.", "section": "The Impact of Memory Settings", "sec_num": "4.6"}, {"text": "To further verify the effectiveness of dynamic memory lookup and unit representations, we conduct ablation studies in this section. The results are shown in Table 4 . Unit Representation. We design a block-level memory for efficient context information lookup.", "section": "Ablation Study", "sec_num": "4.7"}, {"text": "We select several representative tokens as the unit representations for relevance computation. We present the results of InfLLM with another training-free representation method (Mean Repr), which computes the representation by averaging the key vectors in a memory unit. From the results, we can observe that InfLLM with average representations can also present competitive performance. It indicates that the original attention vectors in LLMs are effective for relevance score computation, and exploring more efficient unit representations is an important future direction. ", "section": "Ablation Study", "sec_num": "4.7"}, {"text": "Figure 3 : The results on sequences with different lengths.", "section": "LM-Infinite Ours", "sec_num": null}, {"text": "To assess the effectiveness of InfLLM on extremely long sequences, in this subsection, we scale the sequence length to 1024K to evaluate the capacity of InfLLM to capture contextual relevance in long sequences. Specifically, we adopt the Retrieve.PassKey task in ∞-Bench for evaluation. This task prompts LLMs to find a 5-digit sequence among lengthy and noisy contexts, which requires LLMs to locate relevant information among long sequences effectively. We automatically generate inputs with {32, 64, 128, 256, 512, 768, 1024} thousand tokens and for each length, we generate 50 instances for evaluation. We adopt Mistral as the base model.", "section": "LM-Infinite Ours", "sec_num": null}, {"text": "The results are shown in Figure 3 . From the results, we can observe that InfLLM can accurately locate the key information from length noises and achieve 100% accuracy even when the context length scales to 1024 thousand tokens. However, LM-Infinite can only attend to the tokens within the local window, which leads to a rapid decline in its performance as the sequence length increases. It proves that InfLLM can accurately capture the long-distance dependencies for effective long-sequence reasoning.", "section": "LM-Infinite Ours", "sec_num": null}, {"text": "In this paper, we propose a training-free method to improve the length generalizability of LLMs.", "section": "Conclusion", "sec_num": "5"}, {"text": "Based on the sliding window attention mechanism, we construct an additional context memory module, which can help LLMs select relevant information from massive contexts to capture longdistance dependencies. The experiments on two widely-used long-text benchmarks show that InfLLM can effectively improve the ability of LLMs, which are trained on sequences with a few thousand tokens, to process extremely long sequences. In the future, we will explore efficient training of the context memory module to further enhance the model performance. Besides, combining the key-value cache compression methods with InfLLM can further reduce the computational and memory costs.", "section": "Conclusion", "sec_num": "5"}, {"text": "We hope InfLLM can boost the development of streaming applications of LLMs.", "section": "Conclusion", "sec_num": "5"}, {"text": "This paper presents work whose goal is to advance the field of long sequence processing for large language models. There are many potential societal consequences of our work, none of which we feel must be specifically highlighted here.", "section": "Broader Impact", "sec_num": null}, {"text": "In this paper, we propose InfLLM, a method for extending the context window of LLMs without additional training. We verify the effectiveness of our model using a widely-used long-text evaluation benchmark ∞-Bench. However, our method still has the following limitations: (1) We store a large amount of past key-value (KV) cache in the CPU memory, which increases CPU memory usage.", "section": "Limitations", "sec_num": null}, {"text": "In the future, we can reduce CPU memory requirements by integrating techniques like KV cache quantization.", "section": "Limitations", "sec_num": null}, {"text": "(2) While InfLLM reduces the computational overhead for processing long texts in LLMs, there is still room for speed-up. In the future, we can further enhance the inference speed of InfLLM by integrating it with inference frameworks like llama.cpp4 and vllm (<PERSON> et al., 2023) .", "section": "Limitations", "sec_num": null}, {"text": "A Cache Management Strategy Due to the massive amount of memory units for extremely long sequences, we adopt an offloading mechanism to save GPU memory costs. Considering the infrequent usage of memory units, we offload most memory units to CPU memory and only preserve the frequently used memory units and current needed memory units in the GPU memory. To this end, we maintain a cache in GPU memory to effectively utilize GPU memory and reduce the communication between CPU and GPU. The size for our GPU cache is fixed, and therefore we design a least recently used (LRU) strategy for cache management. In this section, we will introduce the management strategy in detail.", "section": "Limitations", "sec_num": null}, {"text": "Loading Memory Units For each computation step, we first compute the relevance scores for each memory unit to determine which units should be used. Then, for each needed memory unit, we first search it in our cache. If there is no hit, then we proceed with the transfer from CPU memory to GPU memory.", "section": "Limitations", "sec_num": null}, {"text": "Offloading Memory Units After the attention computation, we need to offload redundant memory units to keep the GPU cache fixed. To this end, we apply an LRU strategy. Specifically, for each memory unit loaded into our GPU cache, we assign a frequency score s b for it, which will be used to determine whether this unit should be maintained in the GPU cache or offloaded to CPU memory to save GPU memory costs. The frequency scores are updated after the attention computation. Specifically, we update the score as follows:", "section": "Limitations", "sec_num": null}, {"text": "EQUATION", "section": "Limitations", "sec_num": null}, {"text": "where l u represents the number of current tokens involved in this lookup, attention_score(q, k) denotes the attention score between Q with respect to k (ranging from 0 to 1) obtained after performing the attention computation. d is a hyper-parameter, representing the decay coefficient, used to incorporate the influence of previous lookups. After each attention computation, we sort all the memory units in our GPU cache according to their frequency scores s b , and offload the units with the lowest scores back to the CPU memory.", "section": "Limitations", "sec_num": null}, {"text": "To verify the effectiveness of our cache management strategy, we evaluate the cache missing rate of different cache management strategies on a sample of data from the GovReport dataset. Specifically, we compare our LRU strategy with (1) Random: randomly selecting units from the GPU cache to (2) First-in-first-out (FIFO): offload the unit that is first loaded in the GPU cache. The results are illustrated in Figure 4 . It is observable that the LRU strategy we employed exhibits a lower missing rate, which ensures that the offloading mechanism does not introduce significant time overhead. In the experiments described in the main text, we chose a decay value of 0.1. Besides, to validate the effectiveness of our cache, we conducted an ablation study: running InfLLM without the GPU cache. The experimental results demonstrate that for encoding a 100K sequence, the addition of a GPU cache reduces our time costs from 21.5s to 18.8s.", "section": "Limitations", "sec_num": null}, {"text": "In InfLLM, we assign all tokens beyond the local window size with the same positional encoding. Therefore, for the current tokens, we do not explicitly provide positional information for the context. But we think that the unidirectional nature of a decoder-only model allows it to recognize the positional information of the context. For instance, assume a sequence contains three spans S A , S B , and S C in order. When encoding S C , although S A and S B are assigned the same positional encoding, the unidirectional nature of the decoder-only model allows the key-value hidden states of S A and S B We also present the results for the combination of InfLLM and Yi-9B-200K (<PERSON> et al., 2024) in Table 7 . From the results, we can observe that InfLLM can also achieve comparable results with Yi-9B-200K.", "section": "B Positional Encoding", "sec_num": null}, {"text": "NeurIPS Paper Checklist", "section": "B Positional Encoding", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?", "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Answer: [Yes] Justification: We provide detailed experimental results to support our claims.", "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Guidelines:", "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?", "section": "Limitations", "sec_num": "2."}, {"text": "Answer: [Yes] Justification: We provide discussion about the limitation of this paper in the Appendix.", "section": "Limitations", "sec_num": "2."}, {"text": "Guidelines:", "section": "Limitations", "sec_num": "2."}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "section": "Limitations", "sec_num": "2."}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "section": "Limitations", "sec_num": "2."}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Answer Justification: We conform with the NeurIPS Code of Ethics.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Guidelines:", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [Yes] Justification: We discuss the broader impact in the Appendix.", "section": "Broader Impacts", "sec_num": "10."}, {"text": "Guidelines:", "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations.", "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA] Justification: In this paper, we propose a training-free method to extend the context window of existing LLMs.", "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [Yes] Justification: We properly cite the data, and code used in this paper.", "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided.", "section": "Safeguards", "sec_num": "11."}, {"text": "https://huggingface.co/gradientai/Llama-3-8B-Instruct-Gradient-1048k", "section": "", "sec_num": null}, {"text": "https://github.com/ggerganov/llama.cpp", "section": "", "sec_num": null}], "back_matter": [{"text": "This work is supported by the National Key R&D Program of China (No.2022ZD0160501), Quan Cheng Laboratory (Grant No. QCLZD202301) and <PERSON> <PERSON> at Tsinghua University. <PERSON><PERSON> is supported by Tsinghua University Initiative Scientific Research Program (Student Academic Research Advancement Program).", "section": "Acknowledgement", "sec_num": null}, {"text": "inherently embeds their relative positional information: S B can utilize information from S A during its encoding, while S A can only access information from preceding parts of the sequence.To verify the model's capability to capture the relative positional information of the context, we adopt the Retrieve.Passkey task with multiple pass keys for evaluation. In this task, each sequence contains two pass keys, and the model is required to output these two pass keys in order. The data construction approach is consistent with that of ∞-Bench (<PERSON> et al., 2023a) , where the positions of the two pass keys are randomly selected. We created 50 sequences, each 64K in length. The experimental results reveal that in this task, InfLLM can output the values of the two pass keys in the correct order 100% of the time. This indicates that, although our positional encoding disregards the relative positional information of the context, the model can still effectively understand the context in sequence.", "section": "annex", "sec_num": null}, {"text": "The context memory is constructed for all layers in LLMs. We set the size of our GPU cache as 32, which is twice the number of loaded units for each step. We set the frequency score decay coefficient as 0.1. We adopt the half-float precision for all experiments. We use NVIDIA A100 or A800 to conduct our experiments. For the experiment that scales to 1, 024K context, we set the encoding chunk size as 2048, and the number of representative tokens as 1 to speed up experiments.", "section": "C.1 Implementation Details", "sec_num": null}, {"text": "We also employ <PERSON><PERSON><PERSON><PERSON> Bai et al. (2023) as the benchmark to evaluate the effectiveness of InfLLM and baseline models. The evaluation results are shown in Table 5 . The results indicate that: (1) InfLLM outperforms other models capable of processing streaming inputs across various diverse tasks. It proves that the context information provided by the context memory can efficiently enhance the model performance. (2) When applying Llama-3 as the base model, both StreamingLLM and LM-Infinite achieve only comparable or even worse performance than the original Llama-3. This indicates that while sliding window attention can effectively extend the context window size of LLMs, these models discard long-distance contextual information, thereby failing to achieve effective long-sequence understanding. (3) Mistral can handle text lengths up to 32K, covering most instances in LongBench. In contrast, InfLLM, with a window size of only 12K, achieves comparable or even superior performance on average. This further demonstrates InfLLM's ability to filter out noise in long contexts, leading to better long-sequence understanding. In the previous sections, we demonstrated that InfLLM can extend the context windows of Llama-3 (with a maximum length of 8K) and Mistral (with a maximum length of 32K) to several hundred thousand tokens. To further validate the effectiveness of InfLLM, we apply it to the <PERSON><PERSON> Chiang et al. (2023) , which has a maximum length of only 4K. The experimental results are shown in Table 6 . The results show that we effectively extend Vicuna's context length to 128K, achieving significant performance improvements on the Retrieve.Passkey and Retrieve.Number tasks. However, InfLLM can not show performance gains on the Retrieve.KV and Math.Find tasks. This is because the hidden vectors contained in Vicuna have a limited ability to filter out noise in extremely long texts, making it difficult for context memory to effectively locate relevant information in the more complex contexts of the Retrieve.KV and Math.Find tasks. In the future, It deserves further exploration to design more powerful memory mechanism.", "section": "C.2 Performance on LongBench", "sec_num": null}, {"text": "Justification: We propose an empirical method without theoretical analysis and proofs. Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "section": "C.3 Experiments on Vicuna", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?Answer: [Yes] Justification: We provide implementation details in Section 4.2 and Appendix C.1. Besides, we provide the source code used in this paper.Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?• If this information is not available online, the authors are encouraged to reach out to the asset's creators. 13. New Assets Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets? Answer: [Yes] Justification: We provide detailed readme to the released code. Guidelines:• The answer NA means that the paper does not release new assets.• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "section": "Open access to data and code", "sec_num": "5."}], "ref_entries": {"FIGREF0": {"text": "Figure 2: Extra studies about InfLLM. Here, (a), (b), and (c) investigate the impact of the context memory under different numbers of representative tokens, different numbers of selected units, and memory unit sizes, respectively.", "type_str": "figure", "fig_num": "2", "uris": null, "num": null}, "FIGREF2": {"text": "Figure 4: Missing rates of different cache management strategies.", "type_str": "figure", "fig_num": "4", "uris": null, "num": null}, "TABREF0": {"text": "The results of InfLLM and baseline models on ∞-Bench. The 95% quantile for text lengths in ∞-Bench is 214K. The context window size for sliding window models refers to the local window size, and for InfLLM refers to \"local window size + selected memory size\".Window Streaming R.PK R.Num R.KV Choice QA Sum Math.F Avg.", "html": null, "type_str": "table", "content": "<table><tr><td/><td/><td/><td colspan=\"3\">Mistral-based Models (7B)</td><td/><td/><td/><td/><td/></tr><tr><td>Mistral</td><td>32K</td><td>✗</td><td>28.8</td><td>28.8</td><td>14.8</td><td colspan=\"3\">44.5 12.9 25.9</td><td colspan=\"2\">20.6 25.2</td></tr><tr><td>NTK</td><td>128K</td><td>✗</td><td>100.0</td><td>86.8</td><td>19.2</td><td colspan=\"3\">40.2 16.9 20.3</td><td colspan=\"2\">26.9 44.3</td></tr><tr><td>SelfExtend</td><td>128K</td><td>✗</td><td>100.0</td><td>100.0</td><td>15.6</td><td colspan=\"3\">42.8 17.3 18.8</td><td colspan=\"2\">19.1 44.8</td></tr><tr><td>Infinite</td><td>32K</td><td>✓</td><td>28.8</td><td>28.8</td><td>0.4</td><td colspan=\"3\">42.8 11.4 22.5</td><td colspan=\"2\">16.3 21.6</td></tr><tr><td>Streaming</td><td>32K</td><td>✓</td><td>28.8</td><td>28.5</td><td>0.2</td><td colspan=\"3\">42.4 11.5 22.1</td><td colspan=\"2\">16.9 21.5</td></tr><tr><td>H2O</td><td>32K</td><td>✓</td><td>8.6</td><td>4.8</td><td>2.6</td><td colspan=\"3\">48.0 15.6 24.4</td><td colspan=\"2\">26.9 18.7</td></tr><tr><td>InfLLM</td><td>16K</td><td>✓</td><td>100.0</td><td>96.1</td><td>96.8</td><td colspan=\"3\">43.7 15.7 25.8</td><td colspan=\"2\">25.7 57.7</td></tr><tr><td/><td/><td/><td colspan=\"3\">Llama-3-based Models (8B)</td><td/><td/><td/><td/><td/></tr><tr><td>Llama-3</td><td>8K</td><td>✗</td><td>8.5</td><td>7.8</td><td>6.2</td><td colspan=\"3\">44.1 15.5 24.7</td><td colspan=\"2\">21.7 18.4</td></tr><tr><td>NTK</td><td>128K</td><td>✗</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.4</td><td>6.4</td><td>2.6</td><td>1.3</td></tr><tr><td>SelfExtend</td><td>128K</td><td>✗</td><td>100.0</td><td>100.0</td><td>0.2</td><td>19.7</td><td colspan=\"2\">8.6 14.7</td><td colspan=\"2\">22.6 38.0</td></tr><tr><td>Infinite</td><td>8K</td><td>✓</td><td>6.8</td><td>7.6</td><td>0.2</td><td colspan=\"3\">41.5 14.6 20.8</td><td colspan=\"2\">20.6 16.0</td></tr><tr><td>Streaming</td><td>8K</td><td>✓</td><td>8.5</td><td>8.3</td><td>0.4</td><td colspan=\"3\">40.6 14.3 20.4</td><td colspan=\"2\">21.4 16.3</td></tr><tr><td>H2O</td><td>8K</td><td>✓</td><td>2.5</td><td>2.4</td><td>0.0</td><td>0.0</td><td>0.7</td><td>2.8</td><td>6.0</td><td>2.1</td></tr><tr><td>InfLLM</td><td>8K</td><td>✓</td><td>100.0</td><td>99.0</td><td>5.0</td><td colspan=\"3\">43.7 19.5 24.3</td><td colspan=\"2\">23.7 45.0</td></tr></table>", "num": null}, "TABREF1": {"text": "The comparison between InfLLM and models with continual pre-training, Llama-3-8B-Instruct-Gradient-1048k (Llama-1M). InfLLM can achieve comparable performance with Llama-1M with less computation consumption and memory usage.", "html": null, "type_str": "table", "content": "<table><tr><td/><td colspan=\"6\">Train-Free R.PK R.Num R.KV Choice QA Sum Math.F VRAM Time</td></tr><tr><td>Llama-1M</td><td>✗</td><td>100.0 99.8</td><td>23.2</td><td>51.5 13.6 18.5</td><td>18.3</td><td>76.6G 40.4s</td></tr><tr><td>InfLLM</td><td>✓</td><td>100.0 99.0</td><td>5.0</td><td>43.7 19.5 24.3</td><td>23.7</td><td>26.3G 26.7s</td></tr><tr><td>Llama-1M+InfLLM</td><td>✗</td><td>100.0 100.0</td><td>55.8</td><td>39.3 20.3 17.1</td><td>31.4</td><td>26.3G 26.7s</td></tr></table>", "num": null}, "TABREF2": {"text": "The comparison between InfLLM and RAG.", "html": null, "type_str": "table", "content": "<table><tr><td>Task</td><td colspan=\"2\"><PERSON><PERSON>PK R.Num R.KV</td></tr><tr><td colspan=\"2\">RAG-E5 89.2</td><td>65.4 13.2</td></tr><tr><td colspan=\"2\">InfLLM 100.0</td><td>96.1 96.8</td></tr></table>", "num": null}, "TABREF3": {"text": "The results for ablation study.", "html": null, "type_str": "table", "content": "<table><tr><td>Task</td><td colspan=\"2\">R.KV Math.F</td><td>QA</td></tr><tr><td>InfLLM</td><td>96.8</td><td colspan=\"2\">25.7 15.7</td></tr><tr><td>Decoding-Only</td><td>85.2</td><td colspan=\"2\">26.3 12.0</td></tr><tr><td>w/o Lookup</td><td>0.4</td><td colspan=\"2\">16.3 11.4</td></tr><tr><td>Mean Repr</td><td>84.6</td><td colspan=\"2\">25.1 14.9</td></tr></table>", "num": null}, "TABREF5": {"text": "The results of InfLLM and baseline models on LongBench. The 95% quantile for text lengths in LongBench is 31K. The context window size for sliding window models refers to the local window size, and for InfLLM refers to \"local window size + selected memory size\".", "html": null, "type_str": "table", "content": "<table><tr><td/><td>Window</td><td>NQA</td><td><PERSON>asper</td><td>MFQA</td><td>HQA</td><td colspan=\"2\">2WikiMQA Musique</td></tr><tr><td/><td/><td/><td colspan=\"3\">Mistral-based Models (7B)</td><td/><td/></tr><tr><td>Mistral</td><td>32K</td><td>22.06</td><td>29.16</td><td>47.65</td><td>37.53</td><td>21.96</td><td>19.03</td></tr><tr><td>Infinite</td><td>6K</td><td>18.44</td><td>30.02</td><td>39.05</td><td>32.02</td><td>22.27</td><td>15.81</td></tr><tr><td>Streaming</td><td>6K</td><td>17.92</td><td>30.05</td><td>39.09</td><td>32.18</td><td>21.83</td><td>14.71</td></tr><tr><td>InfLLM</td><td>6K</td><td>22.12</td><td>29.33</td><td>47.42</td><td>36.56</td><td>22.31</td><td>17.68</td></tr><tr><td>InfLLM</td><td>12K</td><td>23.03</td><td>29.52</td><td>47.62</td><td>39.53</td><td>23.61</td><td>18.92</td></tr><tr><td/><td/><td/><td colspan=\"3\">Llama-3-based Models (8B)</td><td/><td/></tr><tr><td>Llama-3</td><td>8K</td><td>19.85</td><td>42.36</td><td>41.03</td><td>47.38</td><td>39.20</td><td>22.96</td></tr><tr><td>Infinite</td><td>8K</td><td>19.39</td><td>42.80</td><td>40.44</td><td>43.77</td><td>37.89</td><td>18.33</td></tr><tr><td>Streaming</td><td>8K</td><td>20.05</td><td>42.46</td><td>39.54</td><td>43.69</td><td>37.89</td><td>19.68</td></tr><tr><td>InfLLM</td><td>8K</td><td>22.64</td><td>43.70</td><td>49.03</td><td>49.04</td><td>35.61</td><td>26.06</td></tr><tr><td/><td colspan=\"2\">Window GovReport</td><td>QMSum</td><td>MultiNews</td><td>TREC</td><td>TQA</td><td>SAMSum</td></tr><tr><td/><td/><td/><td colspan=\"3\">Mistral-based Models (7B)</td><td/><td/></tr><tr><td>Mistral</td><td>32K</td><td>31.12</td><td>23.87</td><td>26.62</td><td>71.00</td><td>85.97</td><td>42.29</td></tr><tr><td>Infinite</td><td>6K</td><td>29.74</td><td>21.92</td><td>26.65</td><td>70.00</td><td>85.22</td><td>41.60</td></tr><tr><td>Streaming</td><td>6K</td><td>29.83</td><td>21.94</td><td>26.64</td><td>70.00</td><td>85.57</td><td>41.31</td></tr><tr><td>InfLLM</td><td>6K</td><td>31.03</td><td>23.49</td><td>26.70</td><td>69.00</td><td>86.67</td><td>42.52</td></tr><tr><td>InfLLM</td><td>12K</td><td>31.37</td><td>23.77</td><td>26.66</td><td>71.00</td><td>87.34</td><td>41.80</td></tr><tr><td/><td/><td/><td colspan=\"3\">Llama-3-based Models (8B)</td><td/><td/></tr><tr><td>Llama-3</td><td>8K</td><td>29.94</td><td>21.45</td><td>27.51</td><td>74.00</td><td>90.50</td><td>42.30</td></tr><tr><td>Infinite</td><td>8K</td><td>29.25</td><td>21.41</td><td>27.62</td><td>74.00</td><td>90.08</td><td>41.72</td></tr><tr><td>Streaming</td><td>8K</td><td>29.17</td><td>21.33</td><td>27.56</td><td>73.50</td><td>90.08</td><td>41.55</td></tr><tr><td>InfLLM</td><td>8K</td><td>30.76</td><td>22.70</td><td>27.57</td><td>73.50</td><td>90.91</td><td>42.43</td></tr><tr><td/><td colspan=\"3\">Window PsgCount PsgRetrieval</td><td>LCC</td><td>RepoBench-P</td><td>Avg.</td><td/></tr><tr><td/><td/><td/><td colspan=\"3\">Mistral-based Models (7B)</td><td/><td/></tr><tr><td>Mistral</td><td>32K</td><td>3.95</td><td>86.94</td><td>57.42</td><td>54.14</td><td>43.78</td><td/></tr><tr><td>Infinite</td><td>6K</td><td>2.08</td><td>42.80</td><td>57.12</td><td>53.43</td><td>39.07</td><td/></tr><tr><td>Streaming</td><td>6K</td><td>2.50</td><td>42.17</td><td>55.38</td><td>51.46</td><td>38.67</td><td/></tr><tr><td>InfLLM</td><td>6K</td><td>2.87</td><td>64.00</td><td>56.67</td><td>52.97</td><td>41.90</td><td/></tr><tr><td>InfLLM</td><td>12K</td><td>3.01</td><td>87.42</td><td>56.69</td><td>52.09</td><td>44.02</td><td/></tr><tr><td/><td/><td/><td colspan=\"3\">Llama-3-based Models (8B)</td><td/><td/></tr><tr><td>Llama-3</td><td>8K</td><td>8.50</td><td>62.50</td><td>60.83</td><td>49.14</td><td>44.73</td><td/></tr><tr><td>Infinite</td><td>8K</td><td>4.50</td><td>50.00</td><td>60.12</td><td>48.62</td><td>43.03</td><td/></tr><tr><td>Streaming</td><td>8K</td><td>5.00</td><td>49.00</td><td>60.35</td><td>48.95</td><td>42.99</td><td/></tr><tr><td>InfLLM</td><td>8K</td><td>7.17</td><td>84.00</td><td>59.88</td><td>46.48</td><td>46.95</td><td/></tr><tr><td>offload.</td><td/><td/><td/><td/><td/><td/><td/></tr></table>", "num": null}, "TABREF6": {"text": "The combination of InfLLM and models with continual pre-training, Yi-9B-200K (Yi-200K).", "html": null, "type_str": "table", "content": "<table><tr><td/><td colspan=\"6\">Train-Free R.PK R.Num R.KV Choice QA Sum Math.F</td></tr><tr><td>Yi-200K</td><td>✗</td><td>100.0 98.3</td><td>54.5</td><td colspan=\"2\">63.3 13.0 5.9</td><td>23.4</td></tr><tr><td>Yi-200K+InfLLM</td><td>✗</td><td>100.0 98.3</td><td>47.8</td><td>45.4</td><td>8.2 4.7</td><td>33.1</td></tr><tr><td colspan=\"3\">C.4 Combination of InfLLM with Yi-200K</td><td/><td/><td/><td/></tr></table>", "num": null}, "TABREF7": {"text": "We released our code for further research. The data used in this paper is constructed by previous works, which we haved cited in our papper. Guidelines:• The answer NA means that paper does not include experiments requiring code.• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).• Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.6. Experimental Setting/DetailsQuestion: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors).• It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "html": null, "type_str": "table", "content": "<table><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: results? Answer: [Yes] Justification: We provide the implementation details in Section 4.1 and Appendix C.1. Guidelines: • The answer NA means that the paper does not include experiments. • The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material. 7. Experiment Statistical Significance Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [NA] Justification: The method proposed in this paper does not require any training. Therefore, there is no randomness in the experimental results. Guidelines: • The answer NA means that the paper does not include experiments. • The authors should answer \"Yes\" if the results are accompanied by error bars, confi-dence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. Question: For each experiment, does the paper provide sufficient information on the com-puter resources (type of compute workers, memory, time of execution) needed to reproduce the experiments? Answer: [Yes] Justification: We provide the details in Appendix C.1 and report the computation time consumption in Table 2. Guidelines: • 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines? • 8. Experiments Compute Resources Answer: [Yes]</td></tr><tr><td>: [NA]</td></tr></table>", "num": null}}}}