{"paper_id": "schedule_free", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-24T23:43:50.853461Z"}, "title": "The Road Less Scheduled", "authors": [{"first": "<PERSON>", "middle": [], "last": "De<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Hars<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Cutkosky", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Existing learning rate schedules that do not require specification of the optimization stopping step T are greatly out-performed by learning rate schedules that depend on T . We propose an approach that avoids the need for this stopping time by eschewing the use of schedules entirely, while exhibiting state-of-the-art performance compared to schedules across a wide family of problems ranging from convex problems to large-scale deep learning problems. Our Schedule-Free approach introduces no additional hyper-parameters over standard optimizers with momentum. Our method is a direct consequence of a new theory we develop that unifies scheduling and iterate averaging. An open source implementation of our method is available 1 . Schedule-Free AdamW is the core algorithm behind our winning entry to the MLCommons 2024 AlgoPerf Algorithmic Efficiency Challenge Self-Tuning track.", "pdf_parse": {"paper_id": "schedule_free", "_pdf_hash": "", "abstract": [{"text": "Existing learning rate schedules that do not require specification of the optimization stopping step T are greatly out-performed by learning rate schedules that depend on T . We propose an approach that avoids the need for this stopping time by eschewing the use of schedules entirely, while exhibiting state-of-the-art performance compared to schedules across a wide family of problems ranging from convex problems to large-scale deep learning problems. Our Schedule-Free approach introduces no additional hyper-parameters over standard optimizers with momentum. Our method is a direct consequence of a new theory we develop that unifies scheduling and iterate averaging. An open source implementation of our method is available 1 . Schedule-Free AdamW is the core algorithm behind our winning entry to the MLCommons 2024 AlgoPerf Algorithmic Efficiency Challenge Self-Tuning track.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "The theory of optimization, as applied in machine learning, has been successful at providing precise, prescriptive results for many problems. However, even in the simplest setting of stochastic gradient descent (SGD) applied to convex Lipschitz functions, there are glaring gaps between what our current theory prescribes and the methods used in practice.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Consider the stochastic gradient descent (SGD) step with step size γ > 0, z t+1 = z t -γg t where g t is the stochastic (sub-)gradient at time t, computed at the point z t (formally defined in Section 1.1) of a convex Lipschitz function f . Although standard practice for many classes of problems, classical convergence theory suggests that the expected loss of this z sequence is suboptimal, and that the Polyak-<PERSON><PERSON>pert (PR) average x of the sequence should be returned instead (<PERSON><PERSON><PERSON>, 1990; <PERSON><PERSON><PERSON>, 1988) :", "cite_spans": [{"start": 479, "end": 493, "text": "(Polyak, 1990;", "ref_id": "BIBREF32"}, {"start": 494, "end": 508, "text": "<PERSON><PERSON><PERSON>, 1988)", "ref_id": "BIBREF38"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z t+1 = z t -γg t (1) x t+1 = (1 -c t+1 ) x t + c t+1 z t+1 ,", "eq_num": "(2)"}], "section": "Introduction", "sec_num": "1"}, {"text": "where using c t+1 = 1/(t + 1) results in x t =1 T T t=1 z t . Despite their theoretical optimality, PR averages give much worse results in practice than using the last-iterate of SGD (Figures 2a, 8 ) -a folk-law result in the field of optimization, and a large theory-practice gap that is often attributed to the mismatch between this simplified problem class and the complexity of problems addressed in practice. Recently, <PERSON><PERSON><PERSON> and <PERSON><PERSON> (2023) and <PERSON><PERSON><PERSON> et al. (2023) showed that the exact worst-case optimal rates can be achieved via carefully chosen learning rate sequences (also known as schedules) alone, without the use of averaging. This result suggests that schedules have, in some sense, the same role to play as PR averaging in optimization. However, schedules have a critical disadvantage: they require setting the optimization stopping time T in advance.", "cite_spans": [{"start": 424, "end": 449, "text": "Zamani and Glineur (2023)", "ref_id": "BIBREF52"}, {"start": 454, "end": 475, "text": "<PERSON><PERSON><PERSON> et al. (2023)", "ref_id": null}], "ref_spans": [{"start": 192, "end": 195, "text": "2a,", "ref_id": null}, {"start": 196, "end": 197, "text": "8", "ref_id": null}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Motivated by the theory-practice gap for <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> averaging, we ask the following question: Do there exist iterate averaging approaches that match the empirical performance of learning rate schedules, without sacrificing theoretical guarantees? By developing a new link between averaging and learning rate sequences, we introduce a new approach to averaging that maintains the worst-case convergence rate theory of PR averaging, while matching and often exceeding the performance of schedule-based approaches -firmly answering this question in the affirmative.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• Our approach does not require the stopping time T to be known or set in advance. It closely tracks the Pareto frontier of loss versus training time during a single training run (Figure 1 ), while requiring no additional hyper-parameters over the base SGD (with momentum) or Adam optimizer. • Our approach uses an alternative form of momentum that replaces traditional momentum.", "cite_spans": [], "ref_spans": [{"start": 187, "end": 188, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Summary of Results", "sec_num": null}, {"text": "This form has appealing theoretical properties: it is worst case optimal for any choice of the momentum parameter in the convex Lipschitz setting, a property that does not hold for traditional momentum. • Our key theoretical result is a new online-to-batch conversion theorem, which establishes the optimality of our method while also unifying several existing online-to-batch theorems. • We perform, to our knowledge, one of the largest machine learning optimization algorithm evaluations to date, consisting of 28 problems, ranging from logistic regression to large-scale deep learning problems. This evaluation contains more distinct and diverse largescale machine-learning problems than any other optimizer evaluation we are aware of in the literature. Schedule-Free methods show strong performance, matching or out-performing heavily-tuned cosine schedules. • Schedule-Free AdamW won the MLCommons 2024 AlgoPerf Algorithmic Efficiency Challenge Self-Tuning (Adaptive Algorithm) Track, providing independent verification of its SOTA performance against other optimization algorithms in cases where hyperparametertuning is limited. We provide details of our entry and plots comparing it to the competition baseline.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Summary of Results", "sec_num": null}, {"text": "Consider the stochastic convex minimization min", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Notation", "sec_num": "1.1"}, {"text": "x∈R d f (x) = E ζ [f (x, ζ)],", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Notation", "sec_num": "1.1"}, {"text": "where each f (x, ζ) is Lipschitz and convex in x, and the expectation is taken over the random variable ζ. With a slight abuse of notation, we assume we are given, at time step t and any point y that we choose, an arbitrary sub-gradient ∇f (y, ζ t ) from the sub-differential of f . ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Notation", "sec_num": "1.1"}, {"text": "We propose the following method, which we call Schedule-Free SGD:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "y t = (1 -β)z t + βx t ,", "eq_num": "(3)"}], "section": "Method", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z t+1 = z t -γ∇f (y t , ζ t ), (4) x t+1 = (1 -c t+1 ) x t + c t+1 z t+1 ,", "eq_num": "(5)"}], "section": "Method", "sec_num": "2"}, {"text": "where c t+1 = 1/(t+1) and z 1 = x 1 is the initial point. Note that, with this weighting, the x sequence is just a running average of the z sequence. The y sequence is the gradient location sequence (on which gradients are evaluated at each step). The z sequence is the base sequence, which is where the base optimizer's update is performed (in this case SGD). The x sequence is the evaluation sequence, our best estimate of the weights so far.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "2"}, {"text": "This method has a momentum parameter β that interpolates between <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> averaging (β = 0) and Primal averaging (β = 1). Primal averaging (<PERSON><PERSON><PERSON> and <PERSON>, 2015; <PERSON> et al., 2018; <PERSON><PERSON><PERSON>, 2019; <PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> and <PERSON>, 2021; <PERSON><PERSON><PERSON> and <PERSON><PERSON>, 2022) , is an approach where the gradient is evaluated at the averaged point x, instead of z:", "cite_spans": [{"start": 145, "end": 174, "text": "(<PERSON><PERSON><PERSON> and <PERSON>, 2015;", "ref_id": "BIBREF30"}, {"start": 175, "end": 192, "text": "<PERSON> et al., 2018;", "ref_id": "BIBREF47"}, {"start": 193, "end": 208, "text": "Cutkosky, 2019;", "ref_id": "BIBREF3"}, {"start": 209, "end": 228, "text": "<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF22"}, {"start": 229, "end": 250, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF42"}, {"start": 251, "end": 275, "text": "Defazio and Gower, 2021;", "ref_id": "BIBREF7"}, {"start": 276, "end": 302, "text": "De<PERSON><PERSON> and Jelassi, 2022)", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z t+1 = z t -γ∇f (x t , ζ t ) (6) x t+1 = (1 -c t+1 ) x t + c t+1 z t+1 ,", "eq_num": "(7)"}], "section": "Method", "sec_num": "2"}, {"text": "this approach maintains the worst-case optimality of PR averaging but is generally considered to converge too slowly to be practical (Figures 2a, 8 ). The advantage of our interpolation is that we get the best of both worlds. We can achieve the fast convergence of <PERSON>ya<PERSON>-<PERSON><PERSON><PERSON> averaging (since the z sequence moves much quicker than the x sequence), while still keeping some coupling between the returned sequence x and the gradient-evaluation locations y, which increases stability. Values of β similar to standard momentum values β ≈ 0.9 appear to work well in practice. We will use the notation α = 1 -β when convenient.", "cite_spans": [], "ref_spans": [{"start": 142, "end": 145, "text": "2a,", "ref_id": null}, {"start": 146, "end": 147, "text": "8", "ref_id": null}], "eq_spans": [], "section": "Method", "sec_num": "2"}, {"text": "In this formulation, β = 0.9 gives the practical advantages of momentum, dampening the immediate impact of large gradients, resulting in more stable training. To see this, notice that the immediate effect of the gradient g t at step t is to introduce (1 -β)g t = 0.1g t into the iterate sequence y. This is similar to exponential-moving-average (EMA) momentum, where also (1 -β)g t is added into the iterate sequence on step t. However, here the remainder of g t is very slowly added into y over time, via its place in the average x, whereas with an EMA with β = 0.9, the majority of the gradient is incorporated within the next 10 steps. So from this viewpoint, the Schedule-Free updates can be seen as a version of momentum that has the same immediate effect, but with a greater delay for adding in the remainder of the gradient. This form of momentum (by interpolation) also has a striking advantage: it does not result in any theoretical slowdown; it gives the optimal worst case (<PERSON><PERSON><PERSON>, 2013) convergence for the non-smooth convex setting (including constants), for any choice of momentum β between 0 and 1 inclusive:", "cite_spans": [{"start": 984, "end": 1000, "text": "(<PERSON><PERSON><PERSON>, 2013)", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "2"}, {"text": "Theorem 1. Suppose F is a convex function, and ζ 1 , . . . , ζ T is an i.i.d. sequence of random variables such that F = E[f (x, ζ)] for some function f that is G-Lipschitz in x.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "2"}, {"text": "For any minimizer x ⋆ , define", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "2"}, {"text": "D = ∥x 1 -x ⋆ ∥ and γ = D/(G √ T ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "2"}, {"text": "Then for any β ∈ [0, 1], Schedule-Free SGD ensures:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "2"}, {"text": "E[F (x T ) -F (x ⋆ )] ≤ DG √ T (8)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "2"}, {"text": "In contrast, exponential-moving-average momentum in the non-smooth setting actually hurts the theoretical worst-case convergence rate. The Schedule-Free approach maintains the advantages of momentum (<PERSON><PERSON> et al., 2013) without the potential worst-case slow-down.", "cite_spans": [{"start": 199, "end": 223, "text": "(<PERSON><PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF45"}], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "2"}, {"text": "The method analyzed in Theorem 1 is actually a special-case of a more general result that incorporates arbitrary online optimization algorithms rather than only SGD, as well as arbitrary time-varying sequences of β t . The proof is provided in Appendix A. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General Theory", "sec_num": "2.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x t = t i=1 w i z i t i=1 w i = x t-1 1 - w t t i=1 w i ≜1-ct + w t t i=1 w i ≜ct z t (9) y t = β t x t + (1 -β t )z t (10) g t = ∇f (y t , ζ t ).", "eq_num": "(11)"}], "section": "General Theory", "sec_num": "2.1"}, {"text": "Then we have for all x ⋆ :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General Theory", "sec_num": "2.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E[F (x T ) -F (x ⋆ )] ≤ E[ T t=1 w t ⟨g t , z t -x ⋆ ⟩] T i=1 w i .", "eq_num": "(12)"}], "section": "General Theory", "sec_num": "2.1"}, {"text": "To recover Theorem 1 from the above result, notice that the algorithm analyzed by Theorem 1 is captured by Theorem 2 with w t = 1, β t a constant β and z t+1 = z t -γg t for all t. Next, observe that the sequence z 1 , . . . , z T is performing online gradient descent (<PERSON><PERSON>, 2003) , for which it is well-known that the regret T t=1 ⟨g t , z t -x ⋆ ⟩ (appearing in the numerator of our result) is bounded by DG", "cite_spans": [{"start": 269, "end": 286, "text": "(<PERSON><PERSON><PERSON>, 2003)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "General Theory", "sec_num": "2.1"}, {"text": "T and so the result of Theorem 1 immediately follows.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "√", "sec_num": null}, {"text": "The regret is the principle object of study in online convex optimization (<PERSON><PERSON>, 2022; Orabona, 2019) . Viewed in this light, Theorem 2 provides a way to convert an online convex optimization algorithm into a stochastic optimization algorithm: it is a form of online-to-batch conversion (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2004) . Classical online-to-batch conversions are a standard technique for obtaining convergence bounds for many stochastic optimization algorithms, including stochastic gradient descent (<PERSON><PERSON>, 2003) , <PERSON><PERSON>rad (<PERSON><PERSON> et al., 2011) , AMSGrad (<PERSON><PERSON> et al., 2018) , and <PERSON> (Kingma and Ba, 2014) . All of these algorithms can be analyzed as online convex optimization algorithms: they provide bounds on the regret T t=1 ⟨g t , z t -x ⋆ ⟩ rather than direct convergence guarantees. It is then necessary (although sometimes left unstated) to convert these regret bounds into stochastic convergence guarantees via an online-to-batch conversion. Our result provides a more versatile method for effecting this conversion.", "cite_spans": [{"start": 74, "end": 87, "text": "(Hazan, 2022;", "ref_id": "BIBREF12"}, {"start": 88, "end": 102, "text": "Orabona, 2019)", "ref_id": "BIBREF31"}, {"start": 288, "end": 315, "text": "(<PERSON><PERSON><PERSON> et al., 2004)", "ref_id": null}, {"start": 497, "end": 514, "text": "(<PERSON><PERSON><PERSON>, 2003)", "ref_id": null}, {"start": 525, "end": 545, "text": "(<PERSON><PERSON> et al., 2011)", "ref_id": "BIBREF10"}, {"start": 556, "end": 576, "text": "(<PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF37"}, {"start": 588, "end": 609, "text": "(Kingma and Ba, 2014)", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "√", "sec_num": null}, {"text": "Theorem 2 actually provides a \"grand unification\" of a number of different online-to-batch conversions that have been proposed over the years. Most of these conversion methods were first developed specifically to provide convergence analysis for SGD (or some variant such as dual averaging or mirror descent), and then generalized into techniques that apply to any online convex optimization algorithm. For example, the classical Polyak averaging method can be generalized to form the \"standard\" online-to-batch conversion of <PERSON><PERSON><PERSON> et al. (2004) , and is immediately recovered from Theorem 2 by setting w t = 1 and β t = 0 for all t. More recently <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2015) ; <PERSON> et al. (2018) derived an alternative to Polyak averaging that was later generalized to work with arbitrarily online convex optimization algorithms by <PERSON><PERSON><PERSON> (2019) ; <PERSON><PERSON> et al. (2019) , and then observed to actually be equivalent to the heavy-ball momentum by <PERSON><PERSON><PERSON> (2020) ; <PERSON><PERSON><PERSON> and <PERSON><PERSON> (2021) ; <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2022) . This method is recovered by our Theorem 2 by setting w t = 1 and β t = 1 for all t. Finally, very recently <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2023) discovered that gradient descent with a linear decay stepsize provides a last-iterate convergence guarantee, which was again generalized to an online-to-batch conversion by <PERSON><PERSON><PERSON> et al. (2023) . This final result is also recovered by Theorem 2 by setting w t = 1 and β t = t T (see Appendix B). In Appendix C, we give a further tightening of Theorem 2 -it can be improved to an equality by precisely tracking additional terms that appear on the right-hand-side. This tightened version can be used to show convergence rate results for smooth losses, both with and without strong-convexity. As an example application, we show that schedule-free optimistic-gradient methods (Rakhlin and Sridharan, 2013 ) converge with accelerated rates:", "cite_spans": [{"start": 526, "end": 552, "text": "<PERSON><PERSON><PERSON> et al. (2004)", "ref_id": null}, {"start": 655, "end": 683, "text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2015)", "ref_id": "BIBREF30"}, {"start": 686, "end": 703, "text": "<PERSON> et al. (2018)", "ref_id": "BIBREF47"}, {"start": 840, "end": 855, "text": "<PERSON><PERSON><PERSON> (2019)", "ref_id": "BIBREF3"}, {"start": 858, "end": 877, "text": "<PERSON><PERSON> et al. (2019)", "ref_id": "BIBREF22"}, {"start": 954, "end": 968, "text": "<PERSON><PERSON><PERSON> (2020)", "ref_id": "BIBREF5"}, {"start": 971, "end": 995, "text": "Defazio and Gower (2021)", "ref_id": "BIBREF7"}, {"start": 998, "end": 1024, "text": "<PERSON><PERSON><PERSON> and Jelassi (2022)", "ref_id": "BIBREF8"}, {"start": 1134, "end": 1159, "text": "Zamani and Glineur (2023)", "ref_id": "BIBREF52"}, {"start": 1333, "end": 1354, "text": "<PERSON><PERSON><PERSON> et al. (2023)", "ref_id": null}, {"start": 1833, "end": 1861, "text": "(Rakhlin and Sridharan, 2013", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "√", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E[F (x T ) -F (x ⋆ )] = O D 2 L T 2 + Dσ √ T . (", "eq_num": "13"}], "section": "√", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "√", "sec_num": null}, {"text": "Under classical worst-case convergence theory, the optimal choice of γ for a fixed duration training time", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": "T is γ = D/(G √ T )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": ". This is the rate used in our bounds for Theorem 1 above. For any-time convergence (i.e. when stopping is allowed at any timestep), our proposed method can, in theory, be used with the standard learning rate sequence:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "γ t = D G √ t .", "eq_num": "(14)"}], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": "However, learning rate sequences of this form have poor practical performance (<PERSON><PERSON><PERSON> et al., 2023) . Instead, much larger steps of the form D/G give far better performance across virtually all problems in applications (<PERSON><PERSON><PERSON> and <PERSON><PERSON>, 2023) -another theory-practice mismatch that is virtually undiscussed in the literature. Existing theory suggests that this step-size is too large to give O(1/ √ T ) convergence, however, as we show below, there is an important special case where such large step sizes also give optimal rates up to constant factors. Theorem 3. Consider the online learning setting with bounded gradients g t . Let z t+1 = z t -γg t . Let D = ∥z 1 -z * ∥ for arbitrary reference point z * and define G = max t≤T ∥g t ∥. Suppose that the chosen step-size is γ = D/G, then if it holds that:", "cite_spans": [{"start": 78, "end": 100, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": null}, {"start": 220, "end": 250, "text": "(<PERSON><PERSON><PERSON> and <PERSON><PERSON>, 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T t=1 ⟨g t , z t -z 1 ⟩ ≤ D T t=1 ∥g t ∥ 2 ,", "eq_num": "(15)"}], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": "then:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "1 T T t=1 ⟨g t , z t -z * ⟩ = O   D T T t=1 ∥g t ∥ 2   .", "eq_num": "(16)"}], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": "This regret bound for SGD implies a convergence rate bound for Schedule-Free SGD by application of our online-to-batch conversion. Condition 31 can be checked during a training run (Using reference point z * = x T , and so D = ∥x 1 -x T ∥), and we find that it holds for every problem we consider in our experiments in Section 4. More generally, the full conditions under which large learning rates can be used are not yet fully understood for stochastic problems. In the quadratic case, <PERSON> and <PERSON> (2013) established that large fixed step-sizes give optimal convergence rates, and we conjecture that the success of large learning rates may be attributed to asymptotic quadratic behavior of the learning process.", "cite_spans": [{"start": 488, "end": 512, "text": "<PERSON> and Moulines (2013)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": "Empirically, we find that Schedule-Free momentum enables the use of larger learning rates γ > 0 even in quadratic minimization problems f", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": "(x) = 1 2 x ⊤ Ax -b ⊤ x.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": "We generate 10 different such 20-dimensional problems with eigenvalues drawn log-uniformly in [10 -6 , 1]. We plot the average minimal loss achieved as a function of the two parameters β and γ in Figure 2b . We can see that when the learning rate we use is small, what value of β we choose has little to no effect on the convergence of the algorithm. However, when γ is large, choosing β < 1 becomes crucial to achieving convergence.", "cite_spans": [], "ref_spans": [{"start": 203, "end": 205, "text": "2b", "ref_id": null}], "eq_spans": [], "section": "On Large Learning Rates", "sec_num": "2.2"}, {"text": "The proposed method has a striking resemblance to <PERSON><PERSON><PERSON>'s accelerated method (<PERSON><PERSON><PERSON>, 1983 (<PERSON><PERSON><PERSON>, , 2013) ) for L-smooth functions, which can be written in the AC-SA form (<PERSON>, 2012) :", "cite_spans": [{"start": 80, "end": 95, "text": "(<PERSON><PERSON><PERSON>, 1983", "ref_id": "BIBREF28"}, {"start": 96, "end": 116, "text": "(<PERSON><PERSON><PERSON>, , 2013) )", "ref_id": "BIBREF29"}, {"start": 180, "end": 191, "text": "(<PERSON><PERSON>, 2012)", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "y t = (1 -c t+1 )x t + c t+1 z t (17) z t+1 = z t - k + 1 2L ∇f (y t )", "eq_num": "(18)"}], "section": "Related Work", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x t+1 = (1 -c t+1 ) x t + c t+1 z t+1 ,", "eq_num": "(19)"}], "section": "Related Work", "sec_num": "3"}, {"text": "where c t+1 = 2/(t + 2). The averaging constant, and more generally", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "c t+1 = r + 1 t + r + 1 ,", "eq_num": "(20)"}], "section": "Related Work", "sec_num": "3"}, {"text": "for any real r > -1 is equivalent to the weighted average (<PERSON><PERSON><PERSON> and <PERSON>, 2013; <PERSON><PERSON><PERSON> and <PERSON>, 2021) x t ∝ T t=1 t r z t , where t r represents the rth factorial power of t. Our framework is compatible with factorial power averages without sacrificing theoretical guarantees.", "cite_spans": [{"start": 58, "end": 82, "text": "(<PERSON><PERSON><PERSON> and <PERSON>, 2013;", "ref_id": "BIBREF43"}, {"start": 83, "end": 107, "text": "Defazio and Gower, 2021)", "ref_id": "BIBREF7"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "3"}, {"text": "Our approach differs from conventional accelerated methods by using a different weight for the y t and x t interpolations. We use a constant weight for y t and a decreasing weight for x t . Accelerated methods for strongly-convex problems use a constant weight for both, and those for non-strongly convex use an decreasing weight for both, so our approach doesn't directly correspond to either class of accelerated method. Accelerated methods also use a much larger step size for the z t sequence than our approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "3"}, {"text": "The use of equal-weighted averages is less common than the use of exponential weighting in the practical deep learning optimization literature. Exponential moving averages (EMA) of the iterate sequence are used in the popular Lookahead optimizer (<PERSON> et al., 2019) . In the case of SGD, it performs i = 1 . . . k inner steps:", "cite_spans": [{"start": 246, "end": 266, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF54"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z t,i = z t,i-1 -γ∇f (z t,i-1 )", "eq_num": "(21)"}], "section": "Related Work", "sec_num": "3"}, {"text": "followed by an outer step:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x t = x t-1 + α (z t,k -x t-1 ) . (", "eq_num": "22"}], "section": "Related Work", "sec_num": "3"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "3"}, {"text": "The inner optimizer then starts at z t+1,0 = x t-1 . The Lookahead method can be seen as the EMA version of primal averaging, just as exponential weight averaging is the EMA version of <PERSON>yak<PERSON><PERSON><PERSON>pert averaging.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "3"}, {"text": "Tail averaging, either using an exponential moving average or an equal-weighted average, is a common 'folk-law' technique that often yields a practical improvement. For instance, this kind of averaging is used without citation by the influential work of <PERSON><PERSON><PERSON><PERSON> et al. (2016) : \"Model evaluations are performed using a running average of the parameters computed over time.\", and by <PERSON><PERSON><PERSON><PERSON> et al. (2017) : \"...averaged the last 20 checkpoints\". Tail averages are typically \"Polyak-Ruppert\" style averaging as the average is not used for gradient evaluations during training.", "cite_spans": [{"start": 254, "end": 275, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2016)", "ref_id": "BIBREF46"}, {"start": 382, "end": 403, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2017)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "3"}, {"text": "More sophisticated tail averaging approaches such as Stochastic Weight Averaging (<PERSON><PERSON><PERSON><PERSON> et al., 2018) and LAtest Weight Averaging (<PERSON><PERSON><PERSON>, 2022; <PERSON><PERSON> et al., 2023) combine averaging with large or cyclic learning rates. They are not a replacement for scheduling, instead they aim to improve final test metrics. They generally introduce additional hyper-parameters to tune, and require additional memory. It is possible to use SWA and LAWA on top of our approach, potentially giving further gains. <PERSON><PERSON> et al. (2023) show via a stochastic quadratic analysis framework that averaging and learning rate decreases achieve the same effective learning rate. For instance, and average of two points along the training trajectory can give almost identical results to using a learning rate two times smaller. Stochastic quadratic problems are particularly special, <PERSON> and <PERSON> (2013) have shown that Polyak averaging gives optimal O(1/T ) rates without the use of decreasing time-dependent step size sequences in this setting.", "cite_spans": [{"start": 81, "end": 104, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF17"}, {"start": 133, "end": 148, "text": "(Kaddour, 2022;", "ref_id": "BIBREF21"}, {"start": 149, "end": 169, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF41"}, {"start": 502, "end": 523, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF40"}, {"start": 864, "end": 888, "text": "<PERSON> and Moulines (2013)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "3"}, {"text": "Within optimization theory, tail averages can be used to improve the convergence rate for stochastic non-smooth SGD in the strongly convex setting from O(log(T )/T ) to O(1/T ) (<PERSON><PERSON><PERSON> et al., 2012) , although at the expense of worse constants compared to using weighted averages of the whole sequence (<PERSON><PERSON><PERSON><PERSON> et al., 2012) . <PERSON><PERSON> et al. (2022) use cyclic learning rate schedules with increasing cycle periods to give a method that explores multiple points along the Pareto frontier of training time vs eval performance. Each point at the end of a cycle is an approximation to the model from a tuned schedule ending at that time. Our method gives the entire frontier, rather than just a few points along the path. In addition, our method matches or improves upon best known schedules, whereas the \"... cyclic trade-off curve underestimated the standard trade-off curve by a margin of 0.5% validation accuracy\" (<PERSON><PERSON> et al., 2022) .", "cite_spans": [{"start": 177, "end": 199, "text": "(<PERSON><PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF35"}, {"start": 303, "end": 332, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF24"}, {"start": 335, "end": 355, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF33"}, {"start": 921, "end": 942, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "3"}, {"text": "For our deep learning experiments, we evaluated Schedule-Free learning on a set benchmark tasks that are commonly used in the optimization research literature: CIFAR10 A Wide ResNet (16-8) architecture (<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, 2016) on the CIFAR10 image classification dataset. CIFAR100 A DenseNet (<PERSON> et al., 2017) (<PERSON><PERSON> and <PERSON>, 2016) on the IWSLT14 German-English translation dataset (<PERSON><PERSON><PERSON> et al., 2014) . DLRM The DLRM (<PERSON><PERSON><PERSON> et al., 2019) architecture on the Criteo Kaggle Display Advertising dataset (<PERSON><PERSON><PERSON>, 2014). MRI A stacked U-Net architecture (<PERSON><PERSON> et al., 2020) on the fastMRI dataset (<PERSON><PERSON><PERSON> et al., 2018) . MAE Fine-tuning a pretrained Masked Autoencoder (<PERSON> et al., 2021) ViT (patch16-512d-8b) on the ILSVRC 2012 ImageNet dataset. NanoGPT A 124M parameter GPT-2 (<PERSON> et al., 2019) style decoder-only transformer on the OpenWebText dataset (<PERSON><PERSON><PERSON> and <PERSON>, 2019) . For each problem, both the baseline and the Schedule-Free method were tuned by sweeping both the weight decay and learning rate on a grid. We also swept β over two values, 0.9 and 0.98. Final hyper-parameters are listed in the Appendix. Schedule-Free SGD was used for CIFAR10, CIFAR100, SVHN and ImageNet, and Schedule-Free AdamW (Loshchilov and Hutter, 2019) was used for the remaining tasks. We further include a step-wise schedule as a comparison on problems where step-wise schedules are customary. Further results for Polyak and Primal averaging are in Appendix I.", "cite_spans": [{"start": 202, "end": 233, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, 2016)", "ref_id": "BIBREF51"}, {"start": 299, "end": 319, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF16"}, {"start": 320, "end": 344, "text": "(<PERSON><PERSON> and Rush, 2016)", "ref_id": "BIBREF49"}, {"start": 395, "end": 417, "text": "(<PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF1"}, {"start": 434, "end": 455, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF27"}, {"start": 579, "end": 600, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF44"}, {"start": 624, "end": 646, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF53"}, {"start": 697, "end": 714, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF14"}, {"start": 805, "end": 827, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF34"}, {"start": 886, "end": 912, "text": "(<PERSON><PERSON><PERSON> and <PERSON>, 2019)", "ref_id": "BIBREF11"}, {"start": 1245, "end": 1274, "text": "(<PERSON><PERSON><PERSON><PERSON> and <PERSON>, 2019)", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "Our approach shows very strong performance (Figure 3 ) out-performing existing state-of-the-art cosine schedules on CIFAR-10, CIFAR-100, SVHN, IWSLT-14 (Figure 2a ) and OpenWebText GPT-2 problems, as well as the state-of-the-art Linear Decay schedules on the fastMRI and Criteo DLRM tasks. On the remaining two problems, MAE fine-tuning and ImageNet ResNet-50 training, it ties with the existing best schedules.", "cite_spans": [], "ref_spans": [{"start": 51, "end": 52, "text": "3", "ref_id": "FIGREF2"}, {"start": 160, "end": 162, "text": "2a", "ref_id": null}], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "In general, the optimal learning rates for the Schedule-Free variants were larger than the optimal values for the base optimizers. The ability to use larger learning rates without diverging may be a contributing factor to the faster convergence of Schedule-Free methods. The β parameter works well at the default value of 0.9 for all problems except NanoGPT, where the loss started to increase rapidly when 0.9 was used (similar to the Polyak Averaging results in Appendix I). The larger β = 0.98 value in our sweep was stable.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "The AlgoPerf challenge (<PERSON><PERSON> et al., 2023) is designed to be a large-scale and comprehensive benchmark for deep learning optimization algorithms, covering major data domains and architectures. It includes Transformers, ConvNets and U-Net models across image, language, graph and speech domains, and contains 8 problems total. We evaluated Schedule-Free AdamW following the competition guidelines, comparing against NAdamW, the competition reference Algorithm, running 10 seeds of each. As this is a time-to-target competition, traditional error bars are not appropriate so we instead plot all 10 seeds separately. Note that we excluded one benchmark problem, ResNet-50 training, as neither AdamW nor NAdamW can hit the target accuracy on that task. The self-tuning track restricts participants to provide a single set of hyper-parameters to use for all 8 problems. Given the large number of problems, this gives performance representative of a good default configuration.", "cite_spans": [{"start": 23, "end": 42, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "MLCommons Algorithmic Efficiency benchmark", "sec_num": "4.1"}, {"text": "Schedule-Free AdamW performs well across all considered tasks, out-performing the baseline on the WMT, VIT, FASTMRI and OGBG training, while tying on the Conformer and Criteo workloads, and marginally under-performing on the DeepSpeech workload. We attribute the performance on the Conformer and DeepSpeech tasks to their use of batch-norm -the AlgoPerf setup doesn't easily allow us to update the BN running statistics on the x sequence, which is necessary with our method to get the best performance (See Section 4.3).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MLCommons Algorithmic Efficiency benchmark", "sec_num": "4.1"}, {"text": "We validated the Schedule-Free learning approach on a set of standard logistic regression problems from the LibSVM repository. For each problem, and each method separately, we performed a full learning rate sweep on a power-of-two grid, and plotted mean and standard-error of the final train accuracy from 10 seeds using the best learning rate found.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Convex Problems", "sec_num": "4.2"}, {"text": "Schedule-Free learning out-performs both averaging approaches and the state-of-the-art linear decay (LD) schedule baseline (Figure 7 ). It converges faster on all but 1 of 12 problems, has higher accuracy on 6 of the problems, and ties the baseline on the remaining problems. This demonstrates that the performance advantages of Schedule-Free methods are not limited to non-convex problems.", "cite_spans": [], "ref_spans": [{"start": 131, "end": 132, "text": "7", "ref_id": null}], "eq_spans": [], "section": "Convex Problems", "sec_num": "4.2"}, {"text": "The Schedule-Free variant of a method typically has the same memory requirements as the base method. For instance, Schedule-Free SGD requires no extra memory over standard SGD with momentum. Whereas SGDM tracks the current point x and the momentum buffer m, we can track x and z. The quantity y can be computed directly from the latest values of x and z, and so doesn't need Algorithm 1 Schedule-Free AdamW 1: Input: x 1 , learning rate γ, decay λ, warmup steps T warmup , β 1 , β 2 , ϵ 2: z 1 = x 1 3: v 0 = 0 4: for t = 1 to T do 5:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "y t = (1 -β 1 )z t + β 1 x t ▷ Momentum via interpolation 6: g t ∈ ∂f (y t , ζ t ) ▷ Gradient is evaluated at y 7: v t = β 2 v t-1 + (1 -β 2 )g 2 t 8: γ t = γ 1 -β t 2 min(1, t/T warmup )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "▷ LR includes warmup and <PERSON> bias-correction 9:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "z t+1 = z t -γ t g t /( √ v t + ϵ) -γ t λy t 10: c t+1 = γ 2 t t i=1 γ 2 i 11: x t+1 = (1 -c t+1 ) x t + c t+1 z t+1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "▷ Update weighted iterate average 12: end for 13: Return x T xx to be explicitly stored. It's also possible to instead store z and y, and then compute x when needed. This low memory usage is the case for AdamW also, see Algorithm 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "Our efficient PyTorch implementation actually uses one buffer to always store z and the primary parameter buffer to store either x or y, with the stored quantity flipping between the two for training and test/inference passes.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "Our method requires extra code to handle models where batch norm is used. This is due to the fact that BatchNorm layers maintain a running_mean and running_var to track batch statistics which is calculated at y. For model evaluation, these buffers need to be updated to match the statistics on the x sequence. This can be done by evaluating a small number of training batches using x right before each eval. More sophisticated approaches such as PreciseBN (Wu and Johnson, 2021) can also be used. This calculation is not needed for other normalization layers that do not use batch-statistics.", "cite_spans": [{"start": 456, "end": 478, "text": "(<PERSON> and <PERSON>, 2021)", "ref_id": "BIBREF50"}], "ref_spans": [], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "Learning rate warmup is still necessary for our method. We use a linear warmup for a fixed duration, and fuse the Adam bias-correction term into the learning rate for simplicity (this potentially impacts the effect of weight-decay during early iterations), giving a learning rate LR γ t = γ 1 -β t 2 min(1, t/T warmup ) that approaches γ when the warmup and bias-correction period ends. We found that performance was greatly improved by using a weighted c t sequence when ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "c t+1 = γ 2 t t i=1 γ 2 i . (", "eq_num": "23"}], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "This sequence decreases at a 1/t rate after the learning rate warmup. It is shifted by one from the indexing used in Theorem 2, which is done to simplify the implementation. This sequence is motivated by Theorem 2's weighting sequences, which suggest weights proportional to polynomials of the learning rate. This sequence was used for both SGD and AdamW experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "Weight decay for Schedule-Free methods can be computed at either the y or z sequences. We used decay at y for our experiments, as this matches the interpretation of weight-decay as the use of an additional L2-regularizer term in the loss. We found that computing the regularization at y gives significantly better performance on some problems including ImageNet and NanoGPT training.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "5 Parameter Sensitivity For Schedule-Free learning to be truly schedulefree, it's important that the momentum hyperparameter doesn't implicitly have a dependence on the time-horizon. If tuning this parameter gave different values depending on the training duration, then the problem of setting the horizon has just been shifted to setting the momentum value. In Figure 5 we run ImageNet training with Schedule-Free SGD for a longerthen-standard 200 epochs with a variety of momentum values, with the LR fixed to 1.5. We find that the best choice of momentum (β = 0.9) is the same for all durations of training.", "cite_spans": [], "ref_spans": [{"start": 369, "end": 370, "text": "5", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "Schedule-Free learning has a similar mild time-horizon dependency for the baseline learning rate value as schedule-based approaches. Figure 6 shows that the optimal learning rate stays the same for broad range of values, for both Schedule-Free and Schedule based training. For short duration training (≤ 25 epochs), larger LR values begin to show the best performance. Appendix J shows the sensitivity of the final test accuracy to the baseline learning rate for a selection of our test problems, in comparison to the baseline optimizer with a cosine schedule. We see that the overall sensitivity is similar to the baseline optimizer in each problem.", "cite_spans": [], "ref_spans": [{"start": 140, "end": 141, "text": "6", "ref_id": null}], "eq_spans": [], "section": "Implementation Concerns", "sec_num": "4.3"}, {"text": "Two roads diverged in a wood, and I-<PERSON> took the one less traveled by, And that has made all the difference. -<PERSON>", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "We have presented Schedule-Free learning, an optimization approach that removes the need to specify a learning rate schedule while matching or outperforming schedule-based learning. The primary practical limitation is the need to sweep learning rate and weight decay, as the best values differ from the those used with a schedule. We provide a preliminary theoretical exploration of the method, establishing its worst-case optimal performance for non-smooth Lipschitz convex optimization. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x t = t i=1 w i z i t i=1 w i = x t-1 1 - w t t i=1 w i ≜1-ct + w t t i=1 w i ≜ct z t (", "eq_num": "9"}], "section": "Conclusion", "sec_num": "6"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "y t = β t x t + (1 -β t )z t (10) g t = ∇f (y t , ζ t ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "(11) Then we have for all x ⋆ :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E[F (x T ) -F (x ⋆ )] ≤ E[ T t=1 w t ⟨g t , z t -x ⋆ ⟩] T i=1 w i . (", "eq_num": "12"}], "section": "Conclusion", "sec_num": "6"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Proof. Throughout this proof, we will use the notation w 1:t = t i=1 w i . The result is established by showing the following identity:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "w 1:t F (x t ) -w 1:t-1 F (x t-1 ) -w t F (x ⋆ ) ≤ w t ⟨∇F (y t ), z t -x ⋆ ⟩.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "(24) Where here ∇F (y t ) indicates a subgradient of F at y t with E[g t |z t ] = ∇F (y t ). Given the identity (24), we sum over all t from 1 to T . Then the LHS will telescope to obtain:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "w 1:T (F (x T ) -F (x ⋆ )) ≤ T t=1 w t ⟨∇F (y t ), z t -x ⋆ ⟩,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "from which the conclusion immediately follows since E[g t |z t ] = ∇F (y t ). So, let us establish (24). To do so, it will help to observe the following identities:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "w t z t = w 1:t x t -w 1:t-1 x t-1 w 1:t-1 (x t -x t-1 ) = w t (z t -x t ) (25) z t -y t = β t 1 -β t (y t -x t ). (", "eq_num": "26"}], "section": "Conclusion", "sec_num": "6"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Now, setting ∇F (x t ) to be an arbitrary subgradient of F at x t , we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "w 1:t F (x t ) -w 1:t-1 F (x t-1 ) -w t F (x ⋆ ) = w 1:t-1 (F (x t ) -F (x t-1 )) + w t (F (x t ) -F (x ⋆ )) ≤ w 1:t-1 ⟨∇F (x t ), x t -x t-1 ⟩ + w t (F (x t ) -F (x ⋆ ))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "using ( 25):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "= w t ⟨∇F (x t ), z t -x t ⟩ + w t (F (x t ) -F (x ⋆ )) = w t ⟨∇F (x t ), z t -x t ⟩ + w t (F (x t ) -F (y t )) + w t (F (y t ) -F (x ⋆ )) ≤ w t ⟨∇F (x t ), z t -x t ⟩ + w t ⟨∇F (x t ), x t -y t ⟩ + w t ⟨∇F (y t ), y t -x ⋆ ⟩ = w t ⟨∇F (x t ) -∇F (y t ), z t -y t ⟩ + w t ⟨∇F (y t ), z t -x ⋆ ⟩ using (26): = w t β t 1 -β t ⟨∇F (x t ) -∇F (y t ), y t -x t ⟩ + w t ⟨∇F (y t ), z t -x ⋆ ⟩", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Finally, recall that any convex function satisfies ⟨∇F (b) -∇F (a), a -b⟩ ≤ 0 for all a, b. This classical fact can be established by adding the following two subgradient identities:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "F (a) ≥ F (b) + ⟨∇F (b), a -b⟩, F (b) ≥ F (a) + ⟨∇F (a), b -a⟩.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Then, since β t ∈ [0, 1], we have w t βt 1-βt ⟨∇F (x t ) -∇F (y t ), y t -x t ⟩ ≤ 0, which establishes the desired identity (24).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "The following recursions provide an equivalent update to our main algorithm that casts the update in a more \"momentum-like\" form. Theorem 4. Under the same assumptions and notation as Theorem 2, set:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "∆ t = z t+1 -z t , m t = x t+1 -x t , u t = y t+1 -y t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "Then:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "m t = w t+1 w 1:t-1 w t w 1:t+1 m t-1 + w t+1 w 1:t+1 ∆ t u t = β t + (β t -β t+1 ) w 1:t w t+1 m t + (1 -β t )∆ t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "Here u t is playing the role of the \"update vector\", as the sequence of points y t are where we will be evaluating gradients. The ∆ t value can be interpreted as a \"base update\" value: for the case that the z t sequence is specified by SGD (as in Theorem 1), ∆ t = -ηg t . Thus, the update can be interpreted as a momentum term m t , plus an extra \"push\" in the direction of ∆ t scaled by 1 -β t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "Proof. Let's solve for m t in terms of previous values:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "m t = x t+1 -x t = w t+1 w 1:t+1 (z t+1 -x t ) = w t+1 w 1:t+1 (∆ t + z t -x t ) = w t+1 w 1:t+1 (∆ t + w 1:t-1 w t (x t -x t-1 )) = w t+1 w 1:t-1 w t w 1:t+1 m t-1 + w t+1 w 1:t+1 ∆ t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "Now let's solve for u t :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "u t = β t+1 x t+1 + (1 -β t+1 )z t+1 -β t x t -(1 -β t )z t = β t m t + (1 -β t )∆ t + (β t -β t+1 )(z t+1 -x t+1 ) = β t m t + (1 -β t )∆ t + (β t -β t+1 ) w 1:t w t+1 (x t+1 -x t ) = β t m t + (1 -β t )∆ t + (β t -β t+1 ) w 1:t w t+1 m t = β t + (β t -β t+1 ) w 1:t w t+1 m t + (1 -β t )∆ t", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "In the special case that w t = 1 for all t, the updates simplify to:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "m t = t -1 t + 1 m t-1 + 1 t + 1 ∆ t u t = (β t + t(β t -β t+1 )) m t + (1 -β t )∆ t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "In the special case that β t = β for all t, the update for u t simplifies to:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "u t = βm t + (1 -β)∆ t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "From this, it is clear that if β = 1 and w t = 1, then we recover the standard Polyak momentum with a time-varying momentum factor m t = t-1 t+1 m t-1 + 1 t+1 ∆ t , while if β = 0, then we have ordinary SGD without momentum.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Recovering Prior Conversions, and Connections to Momentum", "sec_num": null}, {"text": "Let's take a look at the update for u t = y t+1 -y t in the special case that w t = 1 for all t:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "u t = (β t + t(β t -β t+1 )) m t + (1 -β t )∆ t . Let us define α t = 1 -β t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "Then we can re-write this update as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "u t = (1 -α t + t(α t+1 -α t )) m t + α t ∆ t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "It looks like we might be able to set α t such that the coefficient of m t vanishes. In this case, α t would play the role of a \"schedule\" as the update would just be u t = α t ∆ t . Solving the recursion we get:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "α t -1 = t(α t+1 -α t ), α t+1 = (t + 1)α t -1 t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "Amazingly, this recursion is satisfied by α t = T -t T , which is the linear decay schedule! Notably, this schedule has α T = 0, which in turn implies that y T = x T , so that the last iterate of our algorithm is x T , for which Theorem 2 provides a convergence guarantee.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "The recursion is also satisfied by α t = 1 for all t (which recovers standard <PERSON><PERSON><PERSON><PERSON><PERSON> averaging). Notably, this recursion shows that α 1 will determine all subsequent α values. The values will decease linearly to zero, and then they will try to go negative, which is not allowed. So the linear decay schedule is the value of α 1 that is \"just barely\" allowed since it hits zero at α T .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "In general with arbitrary w t , the recursion is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "1 -α t + (α t+1 -α t )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "w 1:t w t+1 = 0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "If we insist that α T = 0 (so that y T = x T and we get a \"last iterate\" guarantee), then solving the recursion yields:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "α t = w t+1:T w 1:T ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "which exactly recovers the main result of <PERSON><PERSON><PERSON> et al. (2023) .", "cite_spans": [{"start": 42, "end": 63, "text": "<PERSON><PERSON><PERSON> et al. (2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "B.1 Recovering Linear Decay", "sec_num": null}, {"text": "Here, we provide a generalized version of Theorem 2 in the style of <PERSON><PERSON><PERSON> et al. (2020) . This result employs Bregman divergences to tighten the inequality of Theorem 2 to an equality. Theorem 5. Let F be a convex function. Let ζ 1 , . . . , ζ T be a sequence of i.i.d. random variables, and let g be a function such that E[g(x, ζ t )] ∈ ∂F (x) for all x and t. Let z 1 , . . . , z T be arbitrary vectors and let w 1 , . . . , w T and α 1 , . . . , α T be arbitrary non-negative real numbers with α t ≤ 1 such that z t , w t and α t are independent of ζ t , . . . , ζ T . Define the Bregman divergence of F as", "cite_spans": [{"start": 68, "end": 89, "text": "<PERSON><PERSON><PERSON> et al. (2020)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "B F (a, b) = F (a) -F (b) -⟨∇F (b), a -b⟩ 2 . Set: x t = t i=1 w i z i t i=1 w i = x t-1 1 - w t t i=1 w i + w i t i=1 w i z t y t = (1 -α t )x t + α t z t g t = g(y t , ζ t ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "Define the \"compressed sum\" notation: w 1:t = t i=1 w i , with w 1:0 = 0. Then we have for all x ⋆ :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "E[F (x T ) -F (x ⋆ )] = E T t=1 w t ⟨g t , z t -x ⋆ ⟩ w 1:T -E T t=1 wt αt B F (y t , x t ) + wt(1-αt) αt B F (x t , y t ) w 1:T -E T t=1 w 1:t-1 B F (x t-1 , x t ) + w t B F (x ⋆ , y t ) w 1:T .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "Let's take a minute to unpack this result since it is depressingly complicated. Recall that the Bregman divergence for a convex function must be positive, and so all the subtracted Bregman divergence terms can be dropped to make the bound only looser. This recovers Theorem 2. However, in Section D, we show how to exploit the negative Bregman terms to achieve accelerated rates when F is smooth, and in Section E we show how to exploit the negative Bregman terms to achieve faster rates when F is strongly-convex.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "Proof. The proof is nearly the same as that of Theorem 2. The only difference is that we keep track of all the error terms in the inequalities via Bregman divergences.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "Throughout this proof, we use", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "∇F (x) to indicate E ζ [g(x, ζ)].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "When F is differentiable, this is simply the ordinary gradient at x. When F is non-differentiable, this reprents a specific choice of subgradient at x.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "Recall that any convex function satisfies", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "⟨∇F (b) -∇F (a), a -b⟩ = -B F (a, b) -B F (b, a)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "for all a, b. This classical fact can be established by adding the following two subgradient identities:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "F (a) = F (b) + ⟨∇F (b), a -b⟩ + B F (a, b) F (b) = F (a) + ⟨∇F (a), b -a⟩ + B F (b, a) ⟨∇F (b) -∇F (a), a -b⟩ = -B F (a, b) -B F (b, a). (", "eq_num": "27"}], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "The Theorem is established by showing the following identity:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "w 1:t F (x t ) -w 1:t-1 F (x t-1 ) -w t F (x ⋆ ) = w t ⟨∇F (y t ), z t -x ⋆ ⟩ - w t α t B F (y t , x t ) - w t (1 -α t ) α t B F (x t , y t ) -w 1:t-1 B F (x t-1 , x t ) -w t B F (x ⋆ , y t ).", "eq_num": "(28)"}], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "Given the identity (28), we sum over all t from 1 to T . Then the LHS will telescope to obtain:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "w 1:T (F (x T ) -F (x ⋆ )) = T t=1 w t ⟨∇F (y t ), z t -x ⋆ ⟩ - T t=1 w t α t B F (y t , x t ) - w t (1 -α t ) α t B F (x t , y t ) - T t=1 w 1:t-1 B F (x t-1 , x t ) -w t B F (x ⋆ , y t ),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "from which the conclusion immediately follows since", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "E[g t |g 1 , . . . , g t-1 ] = E[∇F (y t )|g 1 , . . . , g t-1 ].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "So, let us establish (28). To do so, it will help to observe the following identities:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "w t z t = w 1:t x t -w 1:t-1 x t-1 w 1:t-1 (x t -x t-1 ) = w t (z t -x t ) (29) z t -y t = 1 -α t α t (y t -x t ).", "eq_num": "(30)"}], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "So, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "w 1:t F (x t ) -w 1:t-1 F (x t-1 ) -w t F (x ⋆ ) = w 1:t-1 (F (x t ) -F (x t-1 ) + w t (F (x t ) -F (x ⋆ )) = w 1:t-1 ⟨∇F (x t ), x t -x t-1 ⟩ + w t (F (x t ) -F (x ⋆ )) -w 1:t-1 B F (x t-1 , x t )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "using ( 29):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "= w t ⟨∇F (x t ), z t -x t ⟩ + w t (F (x t ) -F (x ⋆ )) -w 1:t-1 B F (x t-1 , x t ) = w t ⟨∇F (x t ), z t -x t ⟩ + w t (F (x t ) -F (y t )) + w t (F (y t ) -F (x ⋆ )) -w 1:t-1 B F (x t-1 , x t ) = w t ⟨∇F (x t ), z t -x t ⟩ + w t ⟨∇F (x t ), x t -y t ⟩ + w t ⟨∇F (y t ), y t -x ⋆ ⟩ -w 1:t-1 B F (x t-1 , x t ) -w t B F (y t , x t ) -w t B F (x ⋆ , y t ) = w t ⟨∇F (x t ) -∇F (y t ), z t -y t ⟩ + w t ⟨∇F (y t ), z t -x ⋆ ⟩ -w 1:t-1 B F (x t-1 , x t ) -w t B F (y t , x t ) -w t B F (x ⋆ , y t )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "using ( 30):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "= w t 1 -α t α t ⟨∇F (x t ) -∇F (y t ), y t -x t ⟩ + w t ⟨∇F (y t ), z t -x ⋆ ⟩ -w 1:t-1 B F (x t-1 , x t ) -w t B F (y t , x t ) -w t B F (x ⋆ , y t )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "using ( 27):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "= w t ⟨∇F (y t ), z t -x ⋆ ⟩ -w t 1 -α t α t (B F (x t , y t ) + B F (y t , x t )) -w 1:t-1 B F (x t-1 , x t ) -w t B F (y t , x t ) -w t B F (x ⋆ , y t ) = w t ⟨∇F (y t ), z t -x ⋆ ⟩ - w t α t B F (y t , x t ) - w t (1 -α t ) α t B F (x t , y t ) -w 1:t-1 B F (x t-1 , x t ) -w t B F (x ⋆ , y t ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Generalizing Theorem 2 via Bregman Divergences", "sec_num": null}, {"text": "In this section, we show that by instantiating our framework with an optimistic online learning algorithm (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, 2013) , we achieve accelerated convergence guarantees. Our results match those available in the prior literature (<PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2020) . Our approach is inspired by <PERSON><PERSON><PERSON> et al. (2020) ,: their method is based upon a version of Theorem 5 for the special case that α t = 0. Our result simply extends their analysis to α t = O(1/t).", "cite_spans": [{"start": 106, "end": 135, "text": "(<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, 2013)", "ref_id": "BIBREF36"}, {"start": 243, "end": 263, "text": "(<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF22"}, {"start": 264, "end": 285, "text": "<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF20"}, {"start": 316, "end": 337, "text": "<PERSON><PERSON><PERSON> et al. (2020)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "D Acceleration", "sec_num": null}, {"text": "First, we establish an important technical Corollary that simplifies Theorem 5 in the case that F is smooth and α t is sufficiently small. Corollary 1. Under the same conditions as Theorem 5, suppose additionally that F is L-smooth and suppose α t ≤ wt 10w1:t for all t. Then we have for all x ⋆ :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Acceleration", "sec_num": null}, {"text": "E[F (x T ) -F (x ⋆ )] ≤ E T t=1 w t ⟨g t , z t -x ⋆ ⟩ w 1:T -E T t=1 w 1:t-1 ∥∇F (y t ) -∇F (y t-1 )∥ 2 6Lw 1:T ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Acceleration", "sec_num": null}, {"text": "where above the value of y 0 is arbitrary (since the coefficient is w 1:0 = 0).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Acceleration", "sec_num": null}, {"text": "Proof. The key thing is to observe that smoothness implies B F (a, b) ≥ 2L∥∇F (a) -∇F (b)∥ 2 . The rest of the argument is straightforward manipulation of the terms in Theorem 5:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Acceleration", "sec_num": null}, {"text": "- w t α t B F (y t , x t ) - w t (1 -α t ) α t B F (x t , y t ) ≤ - w t (2 -α t ) 2Lα t ∥∇F (x t ) -∇F (y t )∥ 2 -w 1:t-1 B F (x t-1 , x t ) -w t B F (x ⋆ , y t ) ≤ - w 1:t-1 2L ∥∇F (x t ) -∇F (x t-1 )∥ 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Acceleration", "sec_num": null}, {"text": "Next, observe that for any vectors a, b, c, for any λ > 0:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Acceleration", "sec_num": null}, {"text": "-∥a + b + c∥ 2 = -∥a∥ 2 -∥b∥ 2 -∥c∥ 2 -2⟨a, b⟩ -2⟨b, c⟩ -2⟨a, c⟩ ≤ -(1 -2/λ)∥a∥ 2 + (2λ -1)(∥b∥ 2 + ∥c∥ 2 ),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Acceleration", "sec_num": null}, {"text": "where we have used <PERSON>'s inequality: |⟨v, w⟩| ≤ ∥v∥ 2 2λ + λ∥w∥ 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Acceleration", "sec_num": null}, {"text": ". Therefore, setting λ t = 3 we obtain:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "-", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "w 1:t-1 B F (x t-1 , x t ) -w t B F (x ⋆ , y t ) ≤ - w 1:t-1 6L ∥∇F (y t ) -∇F (y t-1 )∥ 2 + 5w 1:t-1 2L (∥∇F (x t ) -∇F (y t )∥ 2 + ∥∇F (x t-1 ) -∇F (y t-1 )∥ 2 ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "Now, since α t ≤ wt 10w1:t ≤ 1, we obtain:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "- w t α t B F (y t , x t ) - w t (1 -α t ) α t B F (x t , y t ) -w 1:t-1 B F (x t-1 , x t ) -w t B F (x ⋆ , y t ) ≤ - w 1:t-1 6L ∥∇F (y t ) -∇F (y t-1 )∥ 2 - 5w 1:t 2L ∥∇F (x t ) -∇F (y t )∥ 2 + - 5w 1:t-1 2L ∥∇F (x t-1 ) -∇F (y t-1 )∥ 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "Now summing over t from 1 to T (and dropping one negative term), the sum telescopes to:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "T t=1 - w 1:t-1 6L ∥∇F (y t ) -∇F (y t-1 )∥ 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "The result now follows from <PERSON><PERSON> 5. Now, we consider the case that z t is given by an optimistic mirror descent algorithm: Corollary 2. Suppose F is L-smooth. Define g 0 = 0 and suppose also that for some D satisfying D ≥ ∥y 1 -x ⋆ ∥:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "T t=1 w t ⟨g t , z t -x ⋆ ⟩ ≤ D T t=1 w 2 t ∥g t -g t-1 ∥ 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "Finally, suppose E[∥g t -g t-1 ∥ 2 ] ≤ ∥∇F (y t ) -∇F (y t-1 )∥ 2 + σ 2 t for some constants σ 1 , . . . , σ T (these are just variance bounds on the stochastic gradient oracle). Then with w t = t and α t ≤ 1 5(t-1) , we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "E[F (x T ) -F (x ⋆ )] ≤ 14D 2 L T (T + 1) + 2D T t=1 t 2 σ 2 t T (T + 1) = O D 2 L T 2 + Dσ √ T ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "where σ is uniform upper-bound on σ t . Note that the algorithm does not need to know L or σ.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "Algorithms producing z sequences obtaining the guarantee stated here are called \"optimistic online learning algorithms\".", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "Proof. Applying Corollary 1, we obtain immediately:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "T (T + 1) 2 E[F (x T ) -F (x ⋆ )] ≤ E   D T t=1 t 2 ∥g t -g t-1 ∥ 2 - T t=1 (t -1)t 12L ∥∇F (y t ) -∇F (y t-1 )∥ 2   ≤ D T t=1 t 2 E [∥∇F (y t ) -∇F (y t-1 )∥ 2 ] + t 2 σ 2 t + ∥∇F (y 1 )∥ 2 24L - 1 24L T t=1 t 2 E ∥∇F (y t ) -∇F (y t-1 )∥ 2 ≤ D T t=1 t 2 E [∥∇F (y t ) -∇F (y t-1 )∥ 2 ] + D T t=1 t 2 σ 2 t + ∥∇F (y 1 )∥ 2 24L - 1 24L T t=1 t 2 E ∥∇F (y t ) -∇F (y t-1 )∥ 2 Using the identity A √ C -BC ≤ A 2 4B : ≤ 6D 2 L + L∥y 1 -x ⋆ ∥ 2 24 + D T t=1 t 2 σ 2 t ≤ 7D 2 L + D T t=1 t 2 σ 2 t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "Divide by T (T +1) 2 to conclude the result.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "In this section we provide an algorithm that achieves the optimistic regret bound required for our acceleration result Corollary 2. This algorithm is a mild variation on the established literature (<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, 2013; <PERSON> et al., 2012; <PERSON><PERSON> and <PERSON><PERSON>, 2010; <PERSON><PERSON><PERSON> et al., 2017) to slightly improve a technical dependence on the maximum gradient value.", "cite_spans": [{"start": 197, "end": 226, "text": "(<PERSON>khlin and Sri<PERSON>ran, 2013;", "ref_id": "BIBREF36"}, {"start": 227, "end": 247, "text": "<PERSON> et al., 2012;", "ref_id": "BIBREF2"}, {"start": 248, "end": 269, "text": "Hazan and Kale, 2010;", "ref_id": "BIBREF13"}, {"start": 270, "end": 291, "text": "<PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "Lemma 1. For a sequence of vectors g 1 , . . . , g T , set η t = D √ 2 t i=1 ∥gi-gi-1∥ 2 with g 0 = 0, define m t = max i≤t ∥g i -g i-1 ∥ and define the sequence of vectors z t , z ′ t and gt by the recursions:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "z 1 = z ′ 1 = 0 gt = g t-1 + min (m t-1 , ∥g t -g t-1 ∥) g t -g t-1 ∥g t -g t-1 ∥ η t = D m 2 t + t i=1 ∥g i -g i-1 ∥ 2 z ′ t+1 = Π ∥z ′ t+1 ∥≤D z ′ t -η t gt z t+1 = Π ∥zt+1∥≤D z ′ t+1 -η t g t . Then: T t=1 ⟨g t , z t -x ⋆ ⟩ ≤ 7D 2 T t=1 ∥g t -g t-1 ∥ 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "Proof. For purposes of notation, define g 0 = 0 and z ′ 0 = 0. Further, observe that:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "∥g t -g t-1 ∥ ≤ m t-1 ∥g t -g t-1 ∥ ≤ ∥g t -g t-1 ∥ ∥g t -g t ∥ = m t -m t-1 η t ≤ D t+1 i=1 ∥g i -g i-1 ∥ 2 1 η T ≤ 2 T t=1 ∥g t -g t-1 ∥ 2 D .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "Next, notice that z ′ t+1 = argmin ∥z∥≤D ⟨g t , z⟩ + 1 2ηt ∥z -z ′ t ∥ 2 . Therefore since ∥x ⋆ ∥ ≤ D, by first order optimality conditions:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "gt + z ′ t+1 -z ′ t η t , z ′ t+1 -x ⋆ ≤ 0 ⟨g t , z ′ t+1 -x ⋆ ⟩ ≤ 1 η t ⟨z ′ t -z ′ t+1 , z ′ t+1 -x ⋆ ⟩ = ∥z ′ t -x ⋆ ∥ 2 2η t - ∥z ′ t+1 -x ⋆ ∥ 2 2η t - ∥z ′ t+1 -z ′ t ∥ 2 2η t .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "Similarly, we have z t = argmin ∥z∥≤D ⟨g t-1 , z⟩ + 1 2ηt-1 ∥z -z ′ t ∥ 2 . From this we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "g t-1 + z t -z ′ t η t-1 , z t -z ′ t+1 ≤ 0 ⟨g t-1 , z t -z ′ t+1 ⟩ ≤ ∥z ′ t -z ′ t+1 ∥ 2 2η t-1 - ∥z t -z ′ t+1 ∥ 2 2η t-1 - ∥z t -z ′ t ∥ 2 2η t-1 ⟨g t , z t -z ′ t+1 ⟩ ≤ ∥z ′ t -z ′ t+1 ∥ 2 2η t-1 - ∥z t -z ′ t+1 ∥ 2 2η t-1 - ∥z t -z ′ t ∥ 2 2η t-1 + ⟨g t -g t-1 , z t -z ′ t+1 ⟩ by <PERSON>'s inequality: ≤ ∥z ′ t -z ′ t+1 ∥ 2 2η t-1 - ∥z t -z ′ t+1 ∥ 2 2η t-1 - ∥z t -z ′ t ∥ 2 2η t-1 + η t-1 ∥g t -g t-1 ∥ 2 2 + ∥z t -z ′ t+1 ∥ 2 2η t-1 ≤ ∥z ′ t -z ′ t+1 ∥ 2 2η t-1 + η t-1 ∥g t -g t-1 ∥ 2 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "So, combining these facts (and noticing that η t-1 ≥ η t :)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "⟨g t , z t -x ⋆ ⟩ ≤ ∥z ′ t -x ⋆ ∥ 2 2η t - ∥z ′ t+1 -x ⋆ ∥ 2 2η t + η t-1 ∥g t -g t-1 ∥ 2 2 ⟨g t , z t -x ⋆ ⟩ ≤ ∥z ′ t -x ⋆ ∥ 2 2η t - ∥z ′ t+1 -x ⋆ ∥ 2 2η t + η t-1 ∥g t -g t-1 ∥ 2 2 + ⟨g t -gt , z t -x ⋆ ⟩ ≤ ∥z ′ t -x ⋆ ∥ 2 2η t - ∥z ′ t+1 -x ⋆ ∥ 2 2η t + η t-1 ∥g t -g t-1 ∥ 2 2 + 2D(m t -m t-1 ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "So, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "T t=1 ⟨g t , z t -x ⋆ ⟩ ≤ 2Dm T + ∥z ′ 1 -x ⋆ ∥ 2 2η 1 + T t=2 ∥z ′ t -x ⋆ ∥ 2 2 1 η t - 1 η t-1 + T t=1 η t-1 ∥g t -g t-1 ∥ 2 2 ≤ 2Dm T + 4D 2 /η T + T t=1 η t-1 ∥g t -g t-1 ∥ 2 2 ≤ 6D 2 /η T + T t=1 η t-1 ∥g t -g t-1 ∥ 2 2 ≤ 6D 2 /η T + T t=1 D∥g t -g t-1 ∥ 2 2 t i=1 ∥g i -g i-1 ∥ 2 ≤ 6D 2 /η T + D T t=1 ∥g t -g t-1 ∥ 2 ≤ 7D 2 T t=1 ∥g t -g t-1 ∥ 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 An Optimistic Regret Bound", "sec_num": null}, {"text": "Suppose that the expected loss F is actually known to be µ-strongly convex. Then we'd like to have a convergence guarantee of O(1/µT ). This is achieved in Theorem 6 below. Theorem 6. Under the same assumptions as Theorem 5, define ℓ t (z) = ⟨g t , z⟩ + µ 2 ∥y t -z∥ 2 . Define the \"regret\" of the sequence z t as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "Regret ℓ (x ⋆ ) = T t=1 w t (ℓ t (z t ) -ℓ t (x ⋆ )).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "Then we have for x ⋆ = argmin F :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "E[F (x T ) -F (x ⋆ )] ≤ E Regret ℓ (x ⋆ ) - T t=1 wtµ 2 ∥z t -y t ∥ 2 w 1:T .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "In particular, suppose ∥x ⋆ ∥ ≤ D for some known bound D and ∥g t ∥ ≤ G for all t for some G so long as ∥y t ∥ ≤ D. Then if we define w t = t for all t and set z t by:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "z t+1 = Π ∥z∥≤D z t - 2(g t + µ(z t -y t )) µ(t + 1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": ") .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "then we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E[F (x T ) -F (x ⋆ )] ≤ 2(G + 2µD) 2 µ(T + 1)", "eq_num": "."}], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "Proof. From Theorem 5, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "E[F (x T ) -F (x ⋆ )] ≤ E T t=1 w t ⟨g t , z t -x ⋆ ⟩ w 1:T - T t=1 w t B F (x ⋆ , y t ) w 1:T .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "Now, since F is µ-strongly convex, we have B F (x ⋆ , y t ) ≥ µ 2 ∥y t -x ⋆ ∥ 2 . Further, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "T t=1 w t ⟨g t , z t -x ⋆ ⟩ = T t=1 w t (ℓ t (z t ) -ℓ t (x ⋆ )) - w t µ 2 ∥z t -y t ∥ 2 + w t µ 2 ∥x ⋆ -y t ∥ 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "From this we obtain the desired result:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "E[F (x T ) -F (x ⋆ )] ≤ E Regret ℓ (x ⋆ ) - T t=1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "wtµ 2 ∥z t -y t ∥ 2 w 1:T .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "For the final statement, observe that with w t = t, w t ℓ t (z) = t⟨g t , z⟩ + tµ 2 ∥z -y t ∥ 2 is tµ-strongly convex. Therefore if we use learning rate η t = 1 µw1:t = 2 µt(t+1) , then standard analysis of projected OGD yields:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "T t=1 t(ℓ t (z t ) -ℓ t (x ⋆ )) ≤ T t=1 t⟨∇ℓ t (z t ), z t -x ⋆ ⟩ - tµ 2 ∥z t -x ⋆ ∥ 2 ≤ ∥z 1 -x ⋆ ∥ 2 1 2η 1 - µ 2 ∥z t -x ⋆ ∥ 2 - ∥z T +1 -x ⋆ ∥ 2 2η T + T t=2 ∥z t -x ⋆ ∥ 2 1 2η t - 1 2η t-1 - tµ 2 + T t=1 η t t 2 ∥∇ℓ t (z t )∥ 2 2 ≤ T t=1 η t t 2 ∥∇ℓ t (z t )∥ 2 2 ≤ 1 µ T t=1 ∥∇ℓ t (z t )∥ 2 = 1 µ T t=1 ∥g t + µ(z t -y t )∥ 2 ≤ T (G + 2µD) 2 µ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": "where in the last inequality we have observed that since ∥z t ∥ ≤ D and y t is a linear combination of past z values, ∥y t ∥ ≤ D as well. Finally, observing that w 1:T = T (T +1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Strongly Convex Losses", "sec_num": null}, {"text": ", the result follows.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "2", "sec_num": null}, {"text": "Step size convergence Theorem 7. Consider the online learning setting with bounded gradients g t . Let z t+1 = z t -γg t . Let D = ∥z 1 -z * ∥ for arbitrary reference point z * and define G = max t≤T ∥g t ∥. Suppose that the chosen step-size is γ = D/G, then if it holds that:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Large", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T t=1 ⟨g t , z t -z 1 ⟩ ≤ D T t=1 ∥g t ∥ 2 ,", "eq_num": "(31)"}], "section": "F Large", "sec_num": null}, {"text": "then: Recall from D-Adaptation (<PERSON><PERSON><PERSON> and <PERSON>, 2023) Now suppose that the regret at time T is negative. Then trivially the theorem holds:", "cite_spans": [{"start": 31, "end": 61, "text": "(<PERSON><PERSON><PERSON> and <PERSON><PERSON>, 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "F Large", "sec_num": null}, {"text": "1 T T t=1 ⟨g t , z t -z * ⟩ ≤ 0 = O   D T T t=1 ∥g t ∥ 2   ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Large", "sec_num": null}, {"text": "therefore, without loss of generality we may assume that T t=1 γ ⟨g t , z t -z * ⟩ ≥ 0. Then from combining Equation 34with Equation 33 we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Large", "sec_num": null}, {"text": "0 ≤ - 1 2 ∥s T +1 ∥ 2 + ∥s T +1 ∥ D + 1 2 T t=1 γ 2 ∥g t ∥ 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Large", "sec_num": null}, {"text": "This is a quadratic equation in ∥s T +1 ∥ which we can solve explicitly via the quadratic formula, taking the largest root:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Large", "sec_num": null}, {"text": "∥s T +1 ∥ ≤ -b ± √ b 2 -4ac 2a .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Large", "sec_num": null}, {"text": "Plugging in the values a = - ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Large", "sec_num": null}, {"text": "These experiments follow the same tuning setup as Figure 3 , where the learning rate and momentum is tuned separately for each method. In each case the c weighting sequence used for Schedule-Free training is also used to ensure a fair comparison. The Polyak averaging runs include momentum in the base optimizer as we found this gave the best results. We ran the NanoGPT experiment for a shorter 200,000 steps due to computational budget considerations. The NanoGPT Polyak averaging runs show a divergence in test loss for Polyak averaging. ", "cite_spans": [], "ref_spans": [{"start": 57, "end": 58, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "I Polyak and Primal Averaging Runs", "sec_num": null}, {"text": "https://github.com/facebookresearch/schedule_free 38th Conference on Neural Information Processing Systems (NeurIPS", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "2024).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "if F is not differentiable, then by abuse of notation define ∇F (b) = E[g(b, ζ)], which is a particular choice of subgradient of F .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://github.com/facebookresearch/fairseq", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://github.com/karpathy/nanoGPT", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://github.com/fairinternal/mae", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "-lead 2 Engineering Co-lead 3 Senior Author", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "funding", "sec_num": null}, {"text": "Each dataset is obtained from the LIBSVM repository and used without modifications.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G Experimental Setup G.1 Convex experiments", "sec_num": null}, {"text": "Value GPUs 1×V100 Batch size 16 Epochs 100 Seeds 10 Schedule-Free β 1 0.9Hyper-parameter Value Decay 0.0 Optimizer Adam Baseline β 1 0.9 β 2 0.95", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Hyper-parameter", "sec_num": null}, {"text": "We used custom training code based on the PyTorch tutorial code for this problem. Following standard data-augmentation practises, we appliyed random horizontal flips and random offset cropping down to 32x32, using reflection padding of 4 pixels. Input pixel data was normalized by centering around 0.5. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G.2 CIFAR-10", "sec_num": null}, {"text": "We used the same codebase as for our CIFAR-10 experiments, with the same data augmentation.We normalized each input image using fixed mean and standard error values derived from preprocessing the data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G.3 CIFAR-100", "sec_num": null}, {"text": "Architecture DenseNet [6, 12, 24, 16 ", "cite_spans": [{"start": 22, "end": 25, "text": "[6,", "ref_id": null}, {"start": 26, "end": 29, "text": "12,", "ref_id": null}, {"start": 30, "end": 33, "text": "24,", "ref_id": null}, {"start": 34, "end": 36, "text": "16", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Hyper-parameter Value", "sec_num": null}, {"text": "We used the version of the the fastMRI code base at https://github.com/facebookresearch/ fastMRI/tree/main/banding_removal. Note that we found that training failed using PyTorch 2 or newer, and so we ran these experiments using PyTorch 1.9. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G.10 MRI", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope? Answer: [Yes] Justification: Our paper contains new theory as well as extensive experimental results for our method.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors? Answer: [Yes] Justification: We detail the limitations of our method in the conclusion section.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Answer: [Yes] Justification: All theorems stated in the paper are proven in the Appendix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: Detailed results concerning the experimental setup are provided in the appendix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? Answer: ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Non-strongly-convex smooth stochastic approximation with convergence rate O(1/n)", "authors": [{"first": "F", "middle": [], "last": "Bach", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Bo<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Welling", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2004, "venue": "Advances in Neural Information Processing Systems", "volume": "26", "issue": "", "pages": "2050--2057", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> (2013). Non-strongly-convex smooth stochastic approximation with convergence rate O(1/n). In <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON>, editors, Advances in Neural Information Processing Systems, volume 26. Curran Associates, Inc. Cesa-Bianchi, N., <PERSON>, <PERSON>, and <PERSON>, C. (2004). On the generalization ability of on-line learning algorithms. IEEE Transactions on Information Theory, 50(9):2050-2057.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Report on the 11th IWSLT evaluation campaign", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Bentivogli", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, L<PERSON>, and <PERSON>, M<PERSON> (2014). Report on the 11th IWSLT evaluation campaign. In IWSLT.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Online optimization with gradual variations", "authors": [{"first": "C.-K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C.-J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C.-J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2012, "venue": "JMLR Workshop and Conference Proceedings", "volume": "", "issue": "", "pages": "6--7", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> (2012). Online optimization with gradual variations. In Conference on Learning Theory, pages 6-1. JMLR Workshop and Conference Proceedings.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Anytime online-to-batch, optimism and acceleration", "authors": [{"first": "A", "middle": [], "last": "Cutkosky", "suffix": ""}], "year": 2019, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "1446--1454", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (2019). Anytime online-to-batch, optimism and acceleration. In International conference on machine learning, pages 1446-1454. PMLR.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Benchmarking Neural Network Training Algorithms", "authors": [{"first": "G", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["S"], "last": "Sastry", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Medapati", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Kasimbeg", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Anil", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>der", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["J"], "last": "Maddison", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Badura", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>arg", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C. S., <PERSON>, <PERSON>, <PERSON><PERSON>, S., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Maddison, C. J., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, P. (2023). Benchmarking Neural Network Training Algorithms.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Momentum via primal averaging: Theoretical insights and learning rate schedules for non-convex optimization", "authors": [{"first": "A", "middle": [], "last": "De<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (2020). Momentum via primal averaging: Theoretical insights and learning rate schedules for non-convex optimization.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "When, why and how much? adaptive learning rate scheduling by refinement", "authors": [{"first": "A", "middle": [], "last": "De<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Cutkosky", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, K<PERSON> (2023). When, why and how much? adaptive learning rate scheduling by refinement.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "The power of factorial powers: New parameter settings for (stochastic) optimization", "authors": [{"first": "A", "middle": [], "last": "De<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["M"], "last": "<PERSON>wer", "suffix": ""}], "year": 2021, "venue": "Proceedings of The 13th Asian Conference on Machine Learning", "volume": "157", "issue": "", "pages": "49--64", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> <PERSON> (2021). The power of factorial powers: New parameter settings for (stochastic) optimization. In <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, I<PERSON>, editors, Proceedings of The 13th Asian Conference on Machine Learning, volume 157 of Proceedings of Machine Learning Research, pages 49-64. PMLR.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Adaptivity without compromise: A momentumized, adaptive, dual averaged gradient method for stochastic optimization", "authors": [{"first": "A", "middle": [], "last": "De<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Journal of Machine Learning Research", "volume": "23", "issue": "", "pages": "1--34", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> (2022). Adaptivity without compromise: A momentumized, adaptive, dual averaged gradient method for stochastic optimization. Journal of Machine Learning Research, 23:1-34.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Learning-rate-free learning by D-adaptation", "authors": [{"first": "A", "middle": [], "last": "De<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "The 40th International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> (2023). Learning-rate-free learning by D-adaptation. The 40th International Conference on Machine Learning (ICML 2023).", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Adaptive subgradient methods for online learning and stochastic optimization", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Hazan", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2011, "venue": "Journal of Machine Learning Research", "volume": "", "issue": "61", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON> (2011). Adaptive subgradient methods for online learning and stochastic optimization. Journal of Machine Learning Research, 12(61).", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Openwebtext corpus", "authors": [{"first": "A", "middle": [], "last": "Gokaslan", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> (2019). Openwebtext corpus. http://Skylion007.github.io/ OpenWebTextCorpus.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Introduction to online convex optimization", "authors": [{"first": "E", "middle": [], "last": "Hazan", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> (2022). Introduction to online convex optimization. MIT Press.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Extracting certainty from uncertainty: Regret bounded by variation in costs", "authors": [{"first": "E", "middle": [], "last": "Hazan", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2010, "venue": "Machine learning", "volume": "80", "issue": "", "pages": "165--188", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> (2010). Extracting certainty from uncertainty: Regret bounded by variation in costs. Machine learning, 80:165-188.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Masked autoencoders are scalable vision learners", "authors": [{"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Li", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2111.06377"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (2021). Masked autoencoders are scalable vision learners. arXiv:2111.06377.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Deep residual learning for image recognition", "authors": [{"first": "K", "middle": [], "last": "He", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ren", "suffix": ""}, {"first": "J", "middle": [], "last": "Sun", "suffix": ""}], "year": 2016, "venue": "Proceedings of the IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. (2016). Deep residual learning for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Densely connected convolutional networks", "authors": [{"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": ["Q"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "2261--2269", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> (2017). Densely connected convolu- tional networks. In 2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR), pages 2261-2269.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Averaging weights leads to wider optima and better generalization", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["G"], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Conference on Uncertainty in Artificial Intelligence (UAI)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, A<PERSON> (2018). Averaging weights leads to wider optima and better generalization. In Conference on Uncertainty in Artificial Intelligence (UAI).", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "A modular analysis of adaptive (non-) convex optimization: Optimism, composite objectives, and variational bounds", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "György", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "International Conference on Algorithmic Learning Theory", "volume": "", "issue": "", "pages": "681--720", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> (2017). A modular analysis of adaptive (non-) convex optimization: Optimism, composite objectives, and variational bounds. In International Conference on Algorithmic Learning Theory, pages 681-720. PMLR.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "A simpler approach to accelerated optimization: iterative averaging meets optimism", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Gyorgy", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "4984--4993", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, C<PERSON> (2020). A simpler approach to accelerated optimization: iterative averaging meets optimism. In International conference on machine learning, pages 4984-4993. PMLR.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Stop wasting my time! saving days of ImageNet and BERT training with latest weight averaging", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (2022). Stop wasting my time! saving days of ImageNet and BERT training with latest weight averaging.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "UniXGrad: A universal, adaptive algorithm with optimal guarantees for constrained optimization", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Bach", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> (2019). UniXGrad: A universal, adaptive algorithm with optimal guarantees for constrained optimization. Advances in neural information processing systems, 32.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Adam: a method for stochastic optimization", "authors": [{"first": "D", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ba", "suffix": ""}], "year": 2014, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, <PERSON>. (2014). Adam: a method for stochastic optimization. In International Conference on Learning Representations.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "A simpler approach to obtaining an o(1/t) convergence rate for the projected stochastic subgradient method", "authors": [{"first": "S", "middle": [], "last": "Lacoste-Julien", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Bach", "suffix": ""}], "year": 2012, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> (2012). A simpler approach to obtaining an o(1/t) convergence rate for the projected stochastic subgradient method.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "An optimal method for stochastic composite optimization", "authors": [{"first": "G", "middle": [], "last": "Lan", "suffix": ""}], "year": 2012, "venue": "Mathematical Programming", "volume": "133", "issue": "1", "pages": "365--397", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> (2012). An optimal method for stochastic composite optimization. Mathematical Program- ming, 133(1):365-397.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Decoupled weight decay regularization", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> (2019). Decoupled weight decay regularization. In International Conference on Learning Representations.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Deep learning recommendation model for personalization and recommendation systems", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": ["M"], "last": "Shi", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Park", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "U", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["G"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Cherniavskii", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Smelyanskiy", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, N., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, M<PERSON> (2019). Deep learning recommendation model for personalization and recommendation systems. CoRR.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "A method for solving a convex programming problem with convergence rate", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1983, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (1983). A method for solving a convex programming problem with convergence rate O(1/k 2 ). Soviet Mathematics Do<PERSON><PERSON>.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Lectures on Convex Optimization", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (2013). Lectures on Convex Optimization. Springer Nature.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Quasi-monotone subgradient methods for nonsmooth convex minimization", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Journal of Optimization Theory and Applications", "volume": "165", "issue": "3", "pages": "917--940", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> (2015). Quasi-monotone subgradient methods for nonsmooth convex minimization. Journal of Optimization Theory and Applications, 165(3):917-940.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "A modern introduction to online learning", "authors": [{"first": "F", "middle": [], "last": "Orabona", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1912.13213"]}, "num": null, "urls": [], "raw_text": "Orab<PERSON>, <PERSON><PERSON> (2019). A modern introduction to online learning. arXiv preprint arXiv:1912.13213.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "New stochastic approximation type procedures", "authors": [{"first": "B", "middle": [], "last": "Polyak", "suffix": ""}], "year": 1990, "venue": "Avtomatica i Telemekhanika", "volume": "7", "issue": "", "pages": "98--107", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (1990). New stochastic approximation type procedures. Avtomatica i Telemekhanika, 7:98-107.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Fast benchmarking of accuracy vs. training time with cyclic learning rates", "authors": [{"first": "J", "middle": [], "last": "Portes", "suffix": ""}, {"first": "D", "middle": [], "last": "Blalock", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, J. (2022). Fast benchmarking of accuracy vs. training time with cyclic learning rates.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Language models are unsupervised multitask learners", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Child", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>, I. (2019). Language models are unsupervised multitask learners. Technical report, OpenAI.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Making gradient descent optimal for strongly convex stochastic optimization", "authors": [{"first": "A", "middle": [], "last": "Rakhlin", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "Proceedings of the 29th International Coference on International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON> (2012). Making gradient descent optimal for strongly convex stochastic optimization. In Proceedings of the 29th International Coference on International Conference on Machine Learning.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Online learning with predictable sequences", "authors": [{"first": "A", "middle": [], "last": "Rakhlin", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "Conference on Learning Theory", "volume": "", "issue": "", "pages": "993--1019", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> (2013). Online learning with predictable sequences. In Conference on Learning Theory, pages 993-1019. PMLR.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "On the convergence of Adam and beyond", "authors": [{"first": "S", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> (2018). On the convergence of Adam and beyond. In International Conference on Learning Representations.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Efficient estimations from a slowly convergent Robbins-Monro process", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1988, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (1988). Efficient estimations from a slowly convergent Robbins-Monro process. Technical Report, Cornell University.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "ImageNet Large Scale Visual Recognition Challenge", "authors": [{"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "Su", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["C"], "last": "Berg", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "International Journal of Computer Vision (IJCV)", "volume": "", "issue": "3", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, A. <PERSON>., and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (2015). ImageNet Large Scale Visual Recognition Challenge. International Journal of Computer Vision (IJCV), 115(3).", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Training trajectories, mini-batch losses and the curious role of the learning rate", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> (2023). Training trajectories, mini-batch losses and the curious role of the learning rate.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Early weight averaging meets high learning rates for LLM pre-training", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Neerkaje", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. (2023). Early weight averaging meets high learning rates for LLM pre-training.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "On the (asymptotic) convergence of stochastic gradient descent and stochastic heavy ball", "authors": [{"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["M"], "last": "<PERSON>wer", "suffix": ""}, {"first": "A", "middle": [], "last": "De<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "COLT 2021, Proceedings of Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2021). On the (asymptotic) convergence of stochastic gradient descent and stochastic heavy ball. In Conference on Learning Theory, COLT 2021, Proceedings of Machine Learning Research. PMLR.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Stochastic gradient descent for non-smooth optimization: Convergence results and optimal averaging schemes", "authors": [{"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2013, "venue": "Proceedings of the 30th International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> (2013). Stochastic gradient descent for non-smooth optimization: Conver- gence results and optimal averaging schemes. In Proceedings of the 30th International Conference on Machine Learning.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "End-to-end variational networks for accelerated MRI reconstruction", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Zbontar", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "De<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Knoll", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Medical Image Computing and Computer-Assisted Intervention", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON> (2020). End-to-end variational networks for accelerated MRI reconstruction. In International Conference on Medical Image Computing and Computer-Assisted Intervention. Springer.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "On the importance of initialization and momentum in deep learning", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "Proceedings of the 30th International Conference on International Conference on Machine Learning", "volume": "28", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, G. <PERSON>. (2013). On the importance of initialization and momentum in deep learning. In Proceedings of the 30th International Conference on International Conference on Machine Learning -Volume 28. JMLR.org.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Rethinking the inception architecture for computer vision", "authors": [{"first": "C", "middle": [], "last": "Szegedy", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ioffe", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "2016 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "2818--2826", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> (2016). Rethinking the inception architecture for computer vision. In 2016 IEEE Conference on Computer Vision and Pattern Recognition (CVPR), pages 2818-2826.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Primal averaging: A new gradient evaluation step to attain the optimal individual convergence", "authors": [{"first": "W", "middle": [], "last": "Tao", "suffix": ""}, {"first": "Z", "middle": [], "last": "Pan", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "Tao", "suffix": ""}], "year": 2018, "venue": "IEEE Transactions on Cybernetics", "volume": "", "issue": "", "pages": "1--11", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. (2018). Primal averaging: A new gradient evaluation step to attain the optimal individual convergence. IEEE Transactions on Cybernetics, PP:1-11.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Attention is all you need", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": ["U"], "last": "Kaiser", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "Guyon", "suffix": ""}, {"first": "U", "middle": ["V"], "last": "Luxburg", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in Neural Information Processing Systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, L. <PERSON>., and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (2017). Attention is all you need. In Guyon, I., Luxburg, U. V., Bengio, S., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>, editors, Advances in Neural Information Processing Systems, volume 30. Curran Associates, Inc.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Sequence-to-sequence learning as beam-search optimization", "authors": [{"first": "S", "middle": [], "last": "Wiseman", "suffix": ""}, {"first": "A", "middle": ["M"], "last": "<PERSON>", "suffix": ""}], "year": 2016, "venue": "Proceedings of the 2016 Conference on Empirical Methods in Natural Language Processing", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> <PERSON><PERSON> (2016). Sequence-to-sequence learning as beam-search optimization. In Proceedings of the 2016 Conference on Empirical Methods in Natural Language Processing. Association for Computational Linguistics.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Rethinking \"batch", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, J. (2021). Rethinking \"batch\" in batchnorm.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Wide residual networks", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Proceedings of the British Machine Vision Conference (BMVC)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> (2016). Wide residual networks. In Proceedings of the British Machine Vision Conference (BMVC).", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Exact convergence rate of the last iterate in subgradient methods", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, F<PERSON> (2023). Exact convergence rate of the last iterate in subgradient methods.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "fastMRI: An open dataset and benchmarks for accelerated MRI", "authors": [{"first": "J", "middle": [], "last": "Zbontar", "suffix": ""}, {"first": "F", "middle": [], "last": "Knoll", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "De<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1811.08839"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. (2018). fastMRI: An open dataset and benchmarks for accelerated MRI. arXiv preprint arXiv:1811.08839.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Lookahead optimizer: k steps forward, 1 step back", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ba", "suffix": ""}, {"first": "G", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Advances in Neural Information Processing Systems", "volume": "32", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, G. <PERSON> (2019). Lookahead optimizer: k steps forward, 1 step back. In <PERSON>, H<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>, editors, Advances in Neural Information Processing Systems, volume 32. Curran Associates, Inc.", "links": null}}, "ref_entries": {"FIGREF0": {"num": null, "text": "Figure 1: Schedule-Free methods (black) closely track the Pareto frontier of loss v.s. training time in a single run. Both Schedule-Free SGD (left) and AdamW (right) match or exceed the performance of cosine learning rate schedules of varying lengths (red).", "uris": null, "fig_num": "1", "type_str": "figure"}, "FIGREF1": {"num": null, "text": "Schedule-Free learning converges faster than classical averaging approaches, often out-performing tuned schedules. Incorporating the momentum parameter β allows for convergence despite using larger learning rates γ on quadratic problems.", "uris": null, "fig_num": null, "type_str": "figure"}, "FIGREF2": {"num": null, "text": "Figure 3: Deep Learning Experiments", "uris": null, "fig_num": "3", "type_str": "figure"}, "FIGREF3": {"num": null, "text": "Figure 4: Schedule-Free Adam compared to target-setting baseline on the Algoperf competition self-tuning track.", "uris": null, "fig_num": "4", "type_str": "figure"}, "FIGREF4": {"num": null, "text": "Figure 5: Sensitivity to momentum values", "uris": null, "fig_num": "5", "type_str": "figure"}, "FIGREF5": {"num": null, "text": "Consider SGD with fixed step size γ:z t+1 = z t -γg t . Let s T +1 = T t=1 γg t .", "uris": null, "fig_num": null, "type_str": "figure"}, "FIGREF6": {"num": null, "text": "5.588 SE 0.134) Primal Averaging (3.063 SE 0.004) Schedule-Free Reference @ 200k 2.878", "uris": null, "fig_num": null, "type_str": "figure"}, "FIGREF7": {"num": null, "text": "Figure 8: Polyak and Primal Averaging Experiments", "uris": null, "fig_num": null, "type_str": "figure"}, "TABREF0": {"html": null, "content": "<table/>", "num": null, "text": "Theorem 2. Let F be a convex function. Let ζ 1 , . . . , ζ T be an iid sequence such that F (x) = E ζ [f (x, ζ)].Let z 1 , . . . , z T be arbitrary vectors and let w 1 , . . . , w T and β 1 , . . . , β T be arbitrary numbers in [0, 1] such that z t , w t and β t are independent of ζ t , . . . , ζ T . Set:", "type_str": "table"}, "TABREF1": {"html": null, "content": "<table/>", "num": null, "text": "architecture on the CIFAR-100 (100-class) classification dataset. SVHN A deep ResNet architecture (3-96) on the Street View House Numbers (SVHN) dataset. ImageNet A standard ResNet-50 architecture (<PERSON> et al., 2016) on the ILSVRC 2012 ImageNet (<PERSON><PERSON><PERSON> et al., 2015) classification dataset. IWSLT14 A LSTM architecture", "type_str": "table"}, "TABREF4": {"html": null, "content": "<table><tr><td/><td/><td/><td colspan=\"3\">ILSVRC 2012 ImageNet (ResNet-50) Learning Rate Sweep</td><td/><td/><td/><td colspan=\"3\">ILSVRC 2012 ImageNet (ResNet-50) Learning Rate Sweep</td></tr><tr><td>Test Accuracy (%)</td><td>60 65 70 75</td><td>0</td><td>25</td><td>50</td><td>75 100 125 150 175 200 Epoch Schedule-Free LR 5.0 (76.27% SE 0.03) Schedule-Free LR 3.0 (77.39% SE 0.05) Schedule-Free LR 1.5 (77.83% SE 0.03) Schedule-Free LR 1.0 (77.42% SE 0.02) Schedule-Free LR 0.5 (76.17% SE 0.07)</td><td>Test Accuracy (%)</td><td>60 65 70 75</td><td>0</td><td>25</td><td>50</td><td>Epoch 75 100 125 150 175 200 SGD LR 0.5 (76.42% SE 0.05) SGD LR 0.3 (77.05% SE 0.03) SGD LR 0.15 (77.57% SE 0.02) SGD LR 0.1 (77.68% SE 0.04) SGD LR 0.05 (77.69% SE 0.04)</td></tr></table>", "num": null, "text": "Figure 6: Comparison of the LR sensitivity of Schedule-Free training and cosine schedule training warmup is used, weighted by the square of the γ t used during warmup:", "type_str": "table"}, "TABREF6": {"html": null, "content": "<table><tr><td>Funding Acknowledgments AC is supported by NSF grant number CCF-2211718. <PERSON>, <PERSON> (2003). A Proof of Theorem 2</td></tr><tr><td>Theorem 2.</td></tr></table>", "num": null, "text": "Online convex programming and generalized infinitesimal gradient ascent. In Proceedings of the Twentieth International Conference on International Conference on Machine Learning, pages 928-935.Contributions<PERSON><PERSON><PERSON> discovered the method, led research experimentation and proved initial versions of Theorems 1 and 7, with experimental/theoretical contributions by <PERSON>. <PERSON> led the development of the research codebase. <PERSON><PERSON> proved key results including Theorem 2 and led the theoretical investigation of the method. <PERSON> developed preliminary theory for obtaining accelerated rates which was later supplanted by Theorem 2, and investigated the utility of β with large learning rates for quadratics. Additional derivations by <PERSON> and <PERSON><PERSON><PERSON> are included in appendix sections. Discussions between <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> over the last year contributed to this scientific discovery. Let F be a convex function. Let ζ 1 , . . . , ζ T be an iid sequence such thatF (x) = E ζ [f (x, ζ)].Let z 1 , . . . , z T be arbitrary vectors and let w 1 , . . . , w T and β 1 , . . . , β T be arbitrary numbers in [0, 1] such that z t , w t and β t are independent of ζ t , . . . , ζ T . Set:", "type_str": "table"}, "TABREF7": {"html": null, "content": "<table><tr><td colspan=\"2\">T t=1</td><td>γ ⟨g t , z t -z 1 ⟩ =</td><td>1 2</td><td>T t=1</td><td>γ 2 ∥g t ∥</td><td>2 -</td><td>1 2</td><td>∥s t+1 ∥ 2</td><td>(33)</td></tr><tr><td>and:</td><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>T</td><td colspan=\"5\">γ ⟨g t T</td><td colspan=\"3\">γ ⟨g t , z t -z 1 ⟩ .</td><td>(34)</td></tr><tr><td>t=1</td><td/><td/><td/><td/><td colspan=\"2\">t=1</td><td/><td/></tr></table>", "num": null, "text": "theory that: , z t -z * ⟩ ≤ ∥s T +1 ∥ D +", "type_str": "table"}}}}