{"paper_id": "BAU", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:13:13.524674Z"}, "title": "Generalizable Person Re-identification via Balancing Alignment and Uniformity", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Cho", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["<PERSON>"], "last": "Woo", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>on", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Domain generalizable person re-identification (DG re-ID) aims to learn discriminative representations that are robust to distributional shifts. While data augmentation is a straightforward solution to improve generalization, certain augmentations exhibit a polarized effect in this task, enhancing in-distribution performance while deteriorating out-of-distribution performance. In this paper, we investigate this phenomenon and reveal that it leads to sparse representation spaces with reduced uniformity. To address this issue, we propose a novel framework, Balancing Alignment and Uniformity (BAU), which effectively mitigates this effect by maintaining a balance between alignment and uniformity. Specifically, BAU incorporates alignment and uniformity losses applied to both original and augmented images and integrates a weighting strategy to assess the reliability of augmented samples, further improving the alignment loss. Additionally, we introduce a domain-specific uniformity loss that promotes uniformity within each source domain, thereby enhancing the learning of domain-invariant features. Extensive experimental results demonstrate that BAU effectively exploits the advantages of data augmentation, which previous studies could not fully utilize, and achieves state-of-the-art performance without requiring complex training procedures. The code is available at https://github.com/yoonkicho/BAU.", "pdf_parse": {"paper_id": "BAU", "_pdf_hash": "", "abstract": [{"text": "Domain generalizable person re-identification (DG re-ID) aims to learn discriminative representations that are robust to distributional shifts. While data augmentation is a straightforward solution to improve generalization, certain augmentations exhibit a polarized effect in this task, enhancing in-distribution performance while deteriorating out-of-distribution performance. In this paper, we investigate this phenomenon and reveal that it leads to sparse representation spaces with reduced uniformity. To address this issue, we propose a novel framework, Balancing Alignment and Uniformity (BAU), which effectively mitigates this effect by maintaining a balance between alignment and uniformity. Specifically, BAU incorporates alignment and uniformity losses applied to both original and augmented images and integrates a weighting strategy to assess the reliability of augmented samples, further improving the alignment loss. Additionally, we introduce a domain-specific uniformity loss that promotes uniformity within each source domain, thereby enhancing the learning of domain-invariant features. Extensive experimental results demonstrate that BAU effectively exploits the advantages of data augmentation, which previous studies could not fully utilize, and achieves state-of-the-art performance without requiring complex training procedures. The code is available at https://github.com/yoonkicho/BAU.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Person re-identification (re-ID) aims to match a person with the same identity as a given query across disjoint camera views and different timestamps [88, 96] . Thanks to the discriminative features learned from deep neural networks, significant achievements have been made in this task [1, 44, 45, 72, 73, 78] . However, these learned feature spaces rely on the assumption of independent and identically distributed (i.i.d.) training and testing data, which leads to substantial performance degradation in unseen domains with distributional shifts. To address this issue, domain generalizable person re-ID (DG re-ID) has emerged, focusing on learning representations that are robust to domain shifts [71] .", "cite_spans": [{"start": 150, "end": 154, "text": "[88,", "ref_id": "BIBREF87"}, {"start": 155, "end": 158, "text": "96]", "ref_id": "BIBREF95"}, {"start": 287, "end": 290, "text": "[1,", "ref_id": "BIBREF0"}, {"start": 291, "end": 294, "text": "44,", "ref_id": "BIBREF43"}, {"start": 295, "end": 298, "text": "45,", "ref_id": "BIBREF44"}, {"start": 299, "end": 302, "text": "72,", "ref_id": "BIBREF71"}, {"start": 303, "end": 306, "text": "73,", "ref_id": "BIBREF72"}, {"start": 307, "end": 310, "text": "78]", "ref_id": "BIBREF77"}, {"start": 701, "end": 705, "text": "[71]", "ref_id": "BIBREF70"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Existing DG re-ID methods often leverage advanced network architectures, such as feature normalization modules [34-36, 53, 107] , domain-specific designs [12, 86, 90] , and the integration of transformers [51, 60] . Alternatively, some approaches employ domain adversarial training [6, 20, 91] or meta-learning strategies [10, 40, 71, 94] to learn domain-invariant representations across source domains. Although these studies have shown promising results, they often involve complex training procedures that require significant engineering effort or are prone to training instability [3, 62, 66] .", "cite_spans": [{"start": 111, "end": 127, "text": "[34-36, 53, 107]", "ref_id": null}, {"start": 154, "end": 158, "text": "[12,", "ref_id": "BIBREF11"}, {"start": 159, "end": 162, "text": "86,", "ref_id": "BIBREF85"}, {"start": 163, "end": 166, "text": "90]", "ref_id": "BIBREF89"}, {"start": 205, "end": 209, "text": "[51,", "ref_id": "BIBREF50"}, {"start": 210, "end": 213, "text": "60]", "ref_id": "BIBREF59"}, {"start": 282, "end": 285, "text": "[6,", "ref_id": "BIBREF5"}, {"start": 286, "end": 289, "text": "20,", "ref_id": "BIBREF19"}, {"start": 290, "end": 293, "text": "91]", "ref_id": "BIBREF90"}, {"start": 322, "end": 326, "text": "[10,", "ref_id": "BIBREF9"}, {"start": 327, "end": 330, "text": "40,", "ref_id": "BIBREF39"}, {"start": 331, "end": 334, "text": "71,", "ref_id": "BIBREF70"}, {"start": 335, "end": 338, "text": "94]", "ref_id": "BIBREF93"}, {"start": 585, "end": 588, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 589, "end": 592, "text": "62,", "ref_id": "BIBREF61"}, {"start": 593, "end": 596, "text": "66]", "ref_id": "BIBREF65"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "On the other hand, data augmentation is a straightforward solution to enhance generalization capability by simulating diverse data variations during training. Due to its simplicity and effectiveness, numerous efforts have been made to adopt this approach for various DG tasks [69, 75, 93, 99, 104, 105] . However, in the context of DG re-ID, some data augmentations have been observed to exhibit a polarized effect -improving performance in the source domain while potentially degrading it in the target domain. A notable example is Random Erasing [101] , a technique widely used in person re-ID, which has been shown to deteriorate cross-domain re-ID performance [34, 56, 94] . Despite this observation, the underlying causes and potential solutions for this phenomenon remain underexplored.", "cite_spans": [{"start": 276, "end": 280, "text": "[69,", "ref_id": "BIBREF68"}, {"start": 281, "end": 284, "text": "75,", "ref_id": "BIBREF74"}, {"start": 285, "end": 288, "text": "93,", "ref_id": "BIBREF92"}, {"start": 289, "end": 292, "text": "99,", "ref_id": "BIBREF98"}, {"start": 293, "end": 297, "text": "104,", "ref_id": "BIBREF103"}, {"start": 298, "end": 302, "text": "105]", "ref_id": "BIBREF104"}, {"start": 548, "end": 553, "text": "[101]", "ref_id": "BIBREF100"}, {"start": 664, "end": 668, "text": "[34,", "ref_id": "BIBREF33"}, {"start": 669, "end": 672, "text": "56,", "ref_id": "BIBREF55"}, {"start": 673, "end": 676, "text": "94]", "ref_id": "BIBREF93"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In this paper, we first investigate the polarized effect of data augmentations in DG re-ID. Recent studies have shown that alignment and uniformity in the representation space are closely related to feature generalizability [21, 49, 57, 65, 80, 81] . Building upon this, we reveal that data augmentations can induce sparse representation spaces with less uniformity, which may be detrimental to the open-set nature of the re-ID task, where learning diverse visual information is crucial for generalization [7, 12, 58, 87] . Based on our analysis, we propose a simple yet effective framework, Balancing Alignment and Uniformity (BAU), which alleviates the polarized effect of data augmentations by maintaining a balance between alignment and uniformity. Specifically, it regularizes the representation space by applying alignment and uniformity losses to both original and augmented images. Additionally, we introduce a weighting strategy that considers the reliability of augmented samples to improve the alignment loss. We further propose a domain-specific uniformity loss to promote uniformity within each source domain, enhancing the learning of domain-invariant features. Consequently, BAU effectively exploits the advantages of data augmentation, which previous studies could not fully utilize, and achieves state-of-the-art performance on various benchmarks. In summary, our contributions are as follows:", "cite_spans": [{"start": 224, "end": 228, "text": "[21,", "ref_id": "BIBREF20"}, {"start": 229, "end": 232, "text": "49,", "ref_id": "BIBREF48"}, {"start": 233, "end": 236, "text": "57,", "ref_id": "BIBREF56"}, {"start": 237, "end": 240, "text": "65,", "ref_id": "BIBREF64"}, {"start": 241, "end": 244, "text": "80,", "ref_id": "BIBREF79"}, {"start": 245, "end": 248, "text": "81]", "ref_id": "BIBREF80"}, {"start": 506, "end": 509, "text": "[7,", "ref_id": "BIBREF6"}, {"start": 510, "end": 513, "text": "12,", "ref_id": "BIBREF11"}, {"start": 514, "end": 517, "text": "58,", "ref_id": "BIBREF57"}, {"start": 518, "end": 521, "text": "87]", "ref_id": "BIBREF86"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• We investigate the polarized effect of data augmentations in DG re-ID and reveal that they can lead to sparse representation spaces, which are detrimental to generalization. • We propose a novel BAU framework that mitigates the polarized effect of data augmentations by balancing alignment and uniformity in the representation space. Additionally, we introduce a domain-specific uniformity loss to enhance the learning of domain-invariant representations. • Through extensive experiments on various benchmarks and protocols, we demonstrate that BAU achieves state-of-the-art performance, even without complex training procedures.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Generalizable Person Re-identification. Domain generalizable person re-identification (DG Re-ID) focuses on learning discriminative representations for person retrieval that are robust across unseen domains with distributional shifts. Significant efforts have been made in this task [2, 4, 17, 52, 60, 71, 82] due to its practicality, as it does not require additional model updates for target data, unlike domain adaptation approaches [19, 22, 23, 54, 102] . Given that learned feature statistics can be biased toward the source domain [5, 24, 47, 76] , various feature normalization methods [10, 27, 34, 35, 107] have been proposed to mitigate this domain bias. For instance, SNR [36] eliminates style bias through feature disentanglement, and GDNorm [53] refines feature statistics using a Gaussian process. To achieve domain-invariant representations, several studies [10, 61, 71, 90] leverage domain-adversarial training [20, 41] or meta-learning [18, 40] . DDAN [6] utilizes domain-wise adversarial feature learning to reduce domain discrepancies for domain invariance. M 3 L [94] employs meta-learning to simulate the train-test process of domain generalization with multi-source datasets. There have also been attempts [50, 51] that explore advanced matching strategies between query and gallery images for retrieval to improve interpretability and generalization performance. Recently, Mixture-of-Experts (MoE) based approaches [12, 33] have emerged, where multiple domain-specific experts are trained and applied to the target domain, with META [86] alleviating the model scalability issue by domain-specific batch normalization [5] . In contrast, we effectively utilize the diversity provided by data augmentations to enhance generalization without relying on advanced network architectures or encountering training instability associated with adversarial or meta-learning [3, 62, 66] .", "cite_spans": [{"start": 283, "end": 286, "text": "[2,", "ref_id": "BIBREF1"}, {"start": 287, "end": 289, "text": "4,", "ref_id": "BIBREF3"}, {"start": 290, "end": 293, "text": "17,", "ref_id": "BIBREF16"}, {"start": 294, "end": 297, "text": "52,", "ref_id": "BIBREF51"}, {"start": 298, "end": 301, "text": "60,", "ref_id": "BIBREF59"}, {"start": 302, "end": 305, "text": "71,", "ref_id": "BIBREF70"}, {"start": 306, "end": 309, "text": "82]", "ref_id": "BIBREF81"}, {"start": 436, "end": 440, "text": "[19,", "ref_id": "BIBREF18"}, {"start": 441, "end": 444, "text": "22,", "ref_id": "BIBREF21"}, {"start": 445, "end": 448, "text": "23,", "ref_id": "BIBREF22"}, {"start": 449, "end": 452, "text": "54,", "ref_id": "BIBREF53"}, {"start": 453, "end": 457, "text": "102]", "ref_id": "BIBREF101"}, {"start": 537, "end": 540, "text": "[5,", "ref_id": "BIBREF4"}, {"start": 541, "end": 544, "text": "24,", "ref_id": "BIBREF23"}, {"start": 545, "end": 548, "text": "47,", "ref_id": "BIBREF46"}, {"start": 549, "end": 552, "text": "76]", "ref_id": "BIBREF75"}, {"start": 593, "end": 597, "text": "[10,", "ref_id": "BIBREF9"}, {"start": 598, "end": 601, "text": "27,", "ref_id": "BIBREF26"}, {"start": 602, "end": 605, "text": "34,", "ref_id": "BIBREF33"}, {"start": 606, "end": 609, "text": "35,", "ref_id": "BIBREF34"}, {"start": 610, "end": 614, "text": "107]", "ref_id": "BIBREF106"}, {"start": 682, "end": 686, "text": "[36]", "ref_id": "BIBREF35"}, {"start": 753, "end": 757, "text": "[53]", "ref_id": "BIBREF52"}, {"start": 872, "end": 876, "text": "[10,", "ref_id": "BIBREF9"}, {"start": 877, "end": 880, "text": "61,", "ref_id": "BIBREF60"}, {"start": 881, "end": 884, "text": "71,", "ref_id": "BIBREF70"}, {"start": 885, "end": 888, "text": "90]", "ref_id": "BIBREF89"}, {"start": 926, "end": 930, "text": "[20,", "ref_id": "BIBREF19"}, {"start": 931, "end": 934, "text": "41]", "ref_id": "BIBREF40"}, {"start": 952, "end": 956, "text": "[18,", "ref_id": "BIBREF17"}, {"start": 957, "end": 960, "text": "40]", "ref_id": "BIBREF39"}, {"start": 968, "end": 971, "text": "[6]", "ref_id": "BIBREF5"}, {"start": 1082, "end": 1086, "text": "[94]", "ref_id": "BIBREF93"}, {"start": 1227, "end": 1231, "text": "[50,", "ref_id": "BIBREF49"}, {"start": 1232, "end": 1235, "text": "51]", "ref_id": "BIBREF50"}, {"start": 1437, "end": 1441, "text": "[12,", "ref_id": "BIBREF11"}, {"start": 1442, "end": 1445, "text": "33]", "ref_id": "BIBREF32"}, {"start": 1555, "end": 1559, "text": "[86]", "ref_id": "BIBREF85"}, {"start": 1639, "end": 1642, "text": "[5]", "ref_id": "BIBREF4"}, {"start": 1884, "end": 1887, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 1888, "end": 1891, "text": "62,", "ref_id": "BIBREF61"}, {"start": 1892, "end": 1895, "text": "66]", "ref_id": "BIBREF65"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Alignment and Uniformity. <PERSON> and <PERSON><PERSON> [81] proposed that contrastive learning encompasses two main objectives: alignment, which aims to learn similar representations for positive pairs, and uniformity, which strives to distribute representations uniformly on the unit hypersphere. This framework has significantly influenced representation learning by potentially indicating the feature generalizability [49, 57, 63, 65, 77, 80] . For instance, the concepts of alignment and uniformity have been extensively studied to learn robust representations for improved generalizability across various downstream tasks [21, 57, 77] or domains [65, 80] . This approach has also proven effective when applied to multiple data modalities, including images and text [49, 63] . Despite these advances, the potential of alignment and uniformity in addressing the challenge of DG re-ID remains largely unexplored. In this work, we address this gap by applying alignment and uniformity to person re-ID, balancing feature discriminability and generalizability to learn domain-invariant representations. 3 Method Problem Formulation. Given a set of K source domains,", "cite_spans": [{"start": 41, "end": 45, "text": "[81]", "ref_id": "BIBREF80"}, {"start": 407, "end": 411, "text": "[49,", "ref_id": "BIBREF48"}, {"start": 412, "end": 415, "text": "57,", "ref_id": "BIBREF56"}, {"start": 416, "end": 419, "text": "63,", "ref_id": "BIBREF62"}, {"start": 420, "end": 423, "text": "65,", "ref_id": "BIBREF64"}, {"start": 424, "end": 427, "text": "77,", "ref_id": "BIBREF76"}, {"start": 428, "end": 431, "text": "80]", "ref_id": "BIBREF79"}, {"start": 613, "end": 617, "text": "[21,", "ref_id": "BIBREF20"}, {"start": 618, "end": 621, "text": "57,", "ref_id": "BIBREF56"}, {"start": 622, "end": 625, "text": "77]", "ref_id": "BIBREF76"}, {"start": 637, "end": 641, "text": "[65,", "ref_id": "BIBREF64"}, {"start": 642, "end": 645, "text": "80]", "ref_id": "BIBREF79"}, {"start": 756, "end": 760, "text": "[49,", "ref_id": "BIBREF48"}, {"start": 761, "end": 764, "text": "63]", "ref_id": "BIBREF62"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "D S = {D k } K k=1 , each domain D k = {(x i , y i )} N k", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "i=1 consists of images x i and corresponding identity labels y i , where N k denotes the number of images in a source domain D k . Using these source domains, we train a model f θ , parameterized by θ, to extract person representations f i = f θ (x i ) ∈ R d from the image x i , where d is the dimensionality of the representation space. The trained model is then evaluated on a target domain D T , which is unseen during training. While a general homogeneous DG task has a consistent label space across source and target domains within a closed-set setting, generalizable person re-ID is a heterogeneous DG problem, where each domain has a disjoint label space from the others [79, 103] . Consequently, it is an open-set retrieval task where trained models need to identify unseen classes.", "cite_spans": [{"start": 679, "end": 683, "text": "[79,", "ref_id": "BIBREF78"}, {"start": 684, "end": 688, "text": "103]", "ref_id": "BIBREF102"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Data augmentations, which apply random transformations to input data, are widely used across various tasks to improve training efficiency and model robustness [15, 39, 70, 89] . In the DG re-ID task, however, certain augmentations have shown a polarized effect: they enhance retrieval performance on in-distribution data while potentially degrading it on out-of-distribution data. For instance, Random Erasing [101] , which selectively erases pixels from parts of input images, has been shown to deteriorate cross-domain re-ID performance [56] . As a result, most DG re-ID methods [10, 12, 36, 86, 94] have simply discarded this technique despite its usefulness in standard re-ID settings. Nonetheless, the underlying phenomenon of this polarized effect remains underexplored.", "cite_spans": [{"start": 159, "end": 163, "text": "[15,", "ref_id": "BIBREF14"}, {"start": 164, "end": 167, "text": "39,", "ref_id": "BIBREF38"}, {"start": 168, "end": 171, "text": "70,", "ref_id": "BIBREF69"}, {"start": 172, "end": 175, "text": "89]", "ref_id": "BIBREF88"}, {"start": 410, "end": 415, "text": "[101]", "ref_id": "BIBREF100"}, {"start": 539, "end": 543, "text": "[56]", "ref_id": "BIBREF55"}, {"start": 581, "end": 585, "text": "[10,", "ref_id": "BIBREF9"}, {"start": 586, "end": 589, "text": "12,", "ref_id": "BIBREF11"}, {"start": 590, "end": 593, "text": "36,", "ref_id": "BIBREF35"}, {"start": 594, "end": 597, "text": "86,", "ref_id": "BIBREF85"}, {"start": 598, "end": 601, "text": "94]", "ref_id": "BIBREF93"}], "ref_spans": [], "eq_spans": [], "section": "Polarized Effect of Data Augmentation on In-and Out-of-Distribution", "sec_num": "3.1"}, {"text": "To investigate the polarized effect on re-ID performance between in-distribution (ID) and out-ofdistribution (OOD) scenarios, we conduct experiments1 using varying augmentation probabilities (i.e., the probability of applying data augmentation to input images). For data augmentations, we employ the widely used RandAugment [11] , known for its effectiveness across various vision tasks, which randomly applies transformations sampled from a predefined set, including comprehensive geometric manipulations and color variations. Considering the fine-grained domain characteristics of person re-ID, we exclude transformations that cause severe color distortion, such as Invert and Solarize, from the predefined set, and additionally utilize Random Erasing [101] . We train models with varying augmentation probabilities using a standard training pipeline that employs cross-entropy and batch-hard triplet loss [30, 56] . Following the existing DG re-ID protocol [86] , the training set of MSMT17 (MS) [83] , CUHK03 (C3) [44] , and CUHK-SYSU (CS) [85] are used for model training as source domains, and Market-1501 (M) [95] as the target domain.", "cite_spans": [{"start": 324, "end": 328, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 754, "end": 759, "text": "[101]", "ref_id": "BIBREF100"}, {"start": 908, "end": 912, "text": "[30,", "ref_id": "BIBREF29"}, {"start": 913, "end": 916, "text": "56]", "ref_id": "BIBREF55"}, {"start": 960, "end": 964, "text": "[86]", "ref_id": "BIBREF85"}, {"start": 999, "end": 1003, "text": "[83]", "ref_id": "BIBREF82"}, {"start": 1018, "end": 1022, "text": "[44]", "ref_id": "BIBREF43"}, {"start": 1044, "end": 1048, "text": "[85]", "ref_id": "BIBREF84"}, {"start": 1116, "end": 1120, "text": "[95]", "ref_id": "BIBREF94"}], "ref_spans": [], "eq_spans": [], "section": "Polarized Effect of Data Augmentation on In-and Out-of-Distribution", "sec_num": "3.1"}, {"text": "Fig. 1a compares the performance on the Market-1501 dataset of two models: one trained on the same dataset (ID) and the other trained on MS+CS+C3 (OOD). While data augmentation improves ID performance, OOD performance consistently deteriorates as the augmentation probability increases. This discrepancy highlights the polarized effect of data augmentations in the open-set nature of person re-ID. In closed-set recognition tasks, strong class invariance learned through augmentations can enhance generalization. However, in open-set retrieval tasks, where the model needs to handle unseen classes, learning diverse visual information becomes more crucial for generalization [58, 87] . Although augmentations improve model robustness within training distributions, they can also lead the model to focus on dominant visual information that is easily invariant to augmentations, as shown in Fig. 2 . Consequently, this can result in sparse representation spaces, where the model focuses on learning dominant features while neglecting subtle cues that can be generalized to other domains. To further explore the effect of data augmentations on the representation space, we leverage the concepts of alignment and uniformity [81] , which are key properties of feature distributions on the unit hypersphere.", "cite_spans": [{"start": 675, "end": 679, "text": "[58,", "ref_id": "BIBREF57"}, {"start": 680, "end": 683, "text": "87]", "ref_id": "BIBREF86"}, {"start": 1220, "end": 1224, "text": "[81]", "ref_id": "BIBREF80"}], "ref_spans": [{"start": 5, "end": 7, "text": "1a", "ref_id": "FIGREF0"}, {"start": 894, "end": 895, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Polarized Effect of Data Augmentation on In-and Out-of-Distribution", "sec_num": "3.1"}, {"text": "Alignment is defined as the expected distance between positive pairs:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Polarized Effect of Data Augmentation on In-and Out-of-Distribution", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L align ≜ log E (i,j)∼Ppos [∥f i -f j ∥ 2 2 ],", "eq_num": "(1)"}], "section": "Polarized Effect of Data Augmentation on In-and Out-of-Distribution", "sec_num": "3.1"}, {"text": "where P pos is the distribution of positive pairs. Uniformity, on the other hand, is defined by the logarithm of average pairwise Gaussian potential:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Polarized Effect of Data Augmentation on In-and Out-of-Distribution", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L uniform ≜ log E (i,j)∼Pdata [e -2∥fi-fj ∥ 2 2 ],", "eq_num": "(2)"}], "section": "Polarized Effect of Data Augmentation on In-and Out-of-Distribution", "sec_num": "3.1"}, {"text": "where P data is the distribution of given data. These two properties reflect that positive pairs are close to each other (alignment) while the overall distribution of embeddings is uniformly spread on the hypersphere (uniformity). Several studies have demonstrated that both are essential for generalization, ensuring that the representation space achieves feature discriminability while preserving maximal information from the data [21, 65, 80] .", "cite_spans": [{"start": 433, "end": 437, "text": "[21,", "ref_id": "BIBREF20"}, {"start": 438, "end": 441, "text": "65,", "ref_id": "BIBREF64"}, {"start": 442, "end": 445, "text": "80]", "ref_id": "BIBREF79"}], "ref_spans": [], "eq_spans": [], "section": "Polarized Effect of Data Augmentation on In-and Out-of-Distribution", "sec_num": "3.1"}, {"text": "Fig. 1b illustrates the alignment and uniformity of representations in OOD scenarios (Market-1501 for MS+CS+C3 → Market-1501) for models trained with varying augmentation probabilities. As depicted, the use of augmentations leads to more alignment, thereby enhancing intra-class invariance compared to models trained without augmentation. Conversely, in terms of uniformity, higher augmentation probabilities result in less uniform embeddings, indicating that the model fails to sufficiently preserve the diverse information from the data. However, to achieve generalizability in the open-set re-ID task, learning diverse information (i.e., more uniformity) is crucial [7, 12, 58] . Furthermore, as shown in Fig. 1c , the feature embeddings become increasingly less uniform with higher probabilities, and this becomes more evident as distributional shifts occur, as indicated by the red plot in the same figure. It suggests that simple training with data augmentations causes the model to become dominated by specific in-distribution data and fail to learn diverse visual cues, leading to degraded generalization performance with a sparse representation space with less uniformity.", "cite_spans": [{"start": 669, "end": 672, "text": "[7,", "ref_id": "BIBREF6"}, {"start": 673, "end": 676, "text": "12,", "ref_id": "BIBREF11"}, {"start": 677, "end": 680, "text": "58]", "ref_id": "BIBREF57"}], "ref_spans": [{"start": 5, "end": 7, "text": "1b", "ref_id": "FIGREF0"}, {"start": 713, "end": 715, "text": "1c", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Polarized Effect of Data Augmentation on In-and Out-of-Distribution", "sec_num": "3.1"}, {"text": "Our analysis highlights the polarized effect of data augmentations in person re-ID, showing that while they enhance in-distribution performance, they can deteriorate out-of-distribution performance by leading to a sparse representation space. Nevertheless, data augmentations remain a promising technique to improve generalization by increasing both the diversity and robustness of training data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Polarized Effect of Data Augmentation on In-and Out-of-Distribution", "sec_num": "3.1"}, {"text": "In the following subsection, we present a method to mitigate this effect by incorporating alignment and uniformity, using both original and augmented images to balance feature discriminability and generalizability.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Polarized Effect of Data Augmentation on In-and Out-of-Distribution", "sec_num": "3.1"}, {"text": "We introduce a simple yet effective framework, Balancing Alignment and Uniformity (BAU), which mitigates the polarized effect of data augmentations by maintaining a balance between alignment and uniformity. The overview is illustrated in Fig. 3 . Given an input batch, we generate augmented views of the images, x = t(x), where t ∼ T denotes augmentations sampled from the distribution T . Our model then extracts features from both the original and augmented images, denoted as f i = f θ (x i ) and fi = f θ (x i ), respectively. Since simple training with augmented images can lead to polarized effects (Sec. 3.1), we apply both alignment and uniformity losses to the features of the augmented images to achieve both feature discriminability and generalizability simultaneously. Specifically, the alignment loss enhances feature discriminability by promoting invariance to diverse augmentations, while the uniformity loss encourages generalizability by striving for a uniform distribution of features on the hypersphere, thereby preserving diverse visual information from the data. Alignment Loss. We reformulate the alignment loss to minimize the expected feature distance of positive pairs between original and augmented images, defined as:", "cite_spans": [], "ref_spans": [{"start": 243, "end": 244, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L align = 1 |I pos | (i,j)∈Ipos ∥ fi -f j ∥ 2 2 ,", "eq_num": "(3)"}], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "where I pos = {(i, j) | y i = y j } is the index set for the positive pairs within a mini-batch, and |•| denotes the cardinality of the set. With alignment loss, the model can learn invariance to various transformations introduced by data augmentations, thereby enhancing feature discriminability. However, some augmented samples may suffer from significant corruption due to aggressive augmentations, and learning invariance with these samples can potentially degrade the training process.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "To address this issue, we introduce a weighting strategy for the alignment loss that considers the reliability of augmented samples. Based on studies that handle noisy samples by leveraging the relationship with nearest neighbors [8, 9, 32, 84, 100] , we compute the weight as the <PERSON><PERSON><PERSON> similarity of k-reciprocal nearest neighbors between the augmented sample and the original sample, defined by:", "cite_spans": [{"start": 230, "end": 233, "text": "[8,", "ref_id": "BIBREF7"}, {"start": 234, "end": 236, "text": "9,", "ref_id": "BIBREF8"}, {"start": 237, "end": 240, "text": "32,", "ref_id": "BIBREF31"}, {"start": 241, "end": 244, "text": "84,", "ref_id": "BIBREF83"}, {"start": 245, "end": 249, "text": "100]", "ref_id": "BIBREF99"}], "ref_spans": [], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "w ij = |R k ( fi ) ∩ R k (f j )| |R k ( fi ) ∪ R k (f j )| ∈ [0, 1],", "eq_num": "(4)"}], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "where R k (f i ) is the set of indices for k-reciprocal nearest neighbors within a mini-batch of the feature f i . Intuitively, a low weight implies that the augmented feature fi and the original feature f j are not strongly correlated, indicating that learning invariance between them can provide unreliable information to each other. We reformulate the alignment loss in Eq. ( 3) with weights w, computed as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L align = (i,j)∈Ipos wij ∥ fi -f j ∥ 2 2 ,", "eq_num": "(5)"}], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "where w = w (i,j)∈Ipos wij is the normalized weight. This strategy allows the alignment loss to focus on reliable pairwise relationships between the original and augmented images without noisy samples.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "Uniformity Loss. Following <PERSON> and <PERSON><PERSON> [81] , we compute the uniformity loss to balance feature discriminability and generalizability as:", "cite_spans": [{"start": 42, "end": 46, "text": "[81]", "ref_id": "BIBREF80"}], "ref_spans": [], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L uniform = log   1 |I data | (i,j)∈Idata e -2∥fi-fj ∥ 2 2   + log   1 |I data | (i,j)∈Idata e -2∥ fi-fj ∥ 2 2   ,", "eq_num": "(6)"}], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "where I data = {(i, j) | i ̸ = j} is the index set of all distinct pairs within a mini-batch. By incorporating the uniformity loss, the learned representations can be uniformly distributed on the hypersphere, maintaining the diversity of the feature space. This feature diversity is crucial for the generalization capability, as it prevents the model from overfitting to sparse dominant representations and encourages learning subtle cues that can be generalized across different domains.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "Domain-specific Uniformity Loss. While the uniformity loss within a mini-batch can improve the diversity of the feature space, batched samples may not fully capture the global structure of the entire representation space. Additionally, high uniformity alone does not guarantee domain invariance, since features from the same domain may still cluster together, as illustrated in Fig. 3 (c). To address these issues, we employ a feature memory bank, M = {c 1 , ..., c N } ∈ R N ×d with class prototypes c ∈ R d , where N is the number of classes in the given datasets. Each class prototype represents the feature vector for its respective class and is updated continuously during training using the momentum strategy [23, 28] as:", "cite_spans": [{"start": 715, "end": 719, "text": "[23,", "ref_id": "BIBREF22"}, {"start": 720, "end": 723, "text": "28]", "ref_id": "BIBREF27"}], "ref_spans": [{"start": 383, "end": 384, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "c ← µ • c + (1 -µ) • f , (", "eq_num": "7"}], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "where f is the extracted features of original images with the same class label as c, and µ ∈ [0, 1] is the momentum coefficient. To mitigate the inherent domain bias in simple uniformity, we additionally apply a domain-specific uniformity loss, which enhances the uniformity between the features and prototypes within their corresponding domain as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L domain = log i j∈N (fi) e -2∥fi-cj ∥ 2 2 i N + log i j∈N ( fi) e -2∥ fi-cj ∥ 2 2 i N ,", "eq_num": "(8)"}], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "where N (f ) is the index set of nearest prototypes of f that are from the same source domain but different class, and N is the number of nearest prototypes, which is set to match the size of the minibatch. This loss attempts to uniformly distribute the features of each domain, reducing domain bias for domain-invariant representation space. Furthermore, by using a memory bank to compute uniformity with nearest prototypes, we can efficiently consider the overall structure of the representation space.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "Overall Training Objective. The overall loss function of BAU is then given by:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L BAU = L ce + L tri + λL align + L uniform + L domain ,", "eq_num": "(9)"}], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "where L ce and L tri are the cross-entropy loss and triplet loss, respectively, applied only to the original images. Here, λ is the weighting parameter for the alignment loss. By ensuring alignment and uniformity with both original and augmented images, BAU effectively mitigates the detrimental effect of data augmentations while simultaneously exploiting the diversity they introduce. Moreover, BAU is a simple yet effective framework that directly regularizes the representation space without the need for advanced network architectures or complex training procedures. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Balancing Alignment and Uniformity", "sec_num": "3.2"}, {"text": "M+MS+CS C3 M+CS+C3 MS MS+CS+C3 M Protocol-3 Full-(M+MS+CS) C3 Full-(M+CS+C3) MS Full-(MS+CS+C3) M", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Datasets and Evaluation Protocols", "sec_num": "4.1"}, {"text": "We conduct experiments using the following datasets: Market-1501 [95] , MSMT17 [83] , CUHK02 [43] , CUHK03 [44] , CUHK-SYSU [85] , PRID [31] , GRID [55] , VIPeR [26] , and iLIDs [97] , with dataset statistics shown in Table 1 . For simplicity, we denote Market-1501, MSMT17, CUHK02, CUHK03, and CUHK-SYSU as M, MS, C2, C3, and CS, respectively. Evaluation metrics include mean average precision (mAP) and cumulative matching characteristic (CMC) at Rank-1.", "cite_spans": [{"start": 65, "end": 69, "text": "[95]", "ref_id": "BIBREF94"}, {"start": 79, "end": 83, "text": "[83]", "ref_id": "BIBREF82"}, {"start": 93, "end": 97, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 107, "end": 111, "text": "[44]", "ref_id": "BIBREF43"}, {"start": 124, "end": 128, "text": "[85]", "ref_id": "BIBREF84"}, {"start": 136, "end": 140, "text": "[31]", "ref_id": "BIBREF30"}, {"start": 148, "end": 152, "text": "[55]", "ref_id": "BIBREF54"}, {"start": 161, "end": 165, "text": "[26]", "ref_id": "BIBREF25"}, {"start": 178, "end": 182, "text": "[97]", "ref_id": "BIBREF96"}], "ref_spans": [{"start": 224, "end": 225, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Datasets and Evaluation Protocols", "sec_num": "4.1"}, {"text": "Following previous studies [71, 86, 90, 94] , we evaluate the proposed method across three protocols, as detailed in Table 2 . For Protocol-1, we utilize all images from M, C2, C3, and CS, including both training and testing data, for model training. We then evaluate the model on four small-scale re-ID datasets, specifically PRID, GRID, VIPeR, and iLIDs. The final performance on these small-scale datasets is obtained by averaging the results of 10 repeated random splits of the query and gallery sets. For Protocol-2 and Protocol-3, we follow a leave-one-out evaluation setting with four large-scale datasets: M, MS, C3, and CS. Three datasets are used as the source domain, and the remaining one is used as the target domain. Protocol-2 uses only the training data from the source domains for model training, whereas Protocol-3 utilizes both the training and testing data from the source domains. Since CUHK-SYSU (CS) is a person search dataset with a single camera view, it is only used for training.", "cite_spans": [{"start": 27, "end": 31, "text": "[71,", "ref_id": "BIBREF70"}, {"start": 32, "end": 35, "text": "86,", "ref_id": "BIBREF85"}, {"start": 36, "end": 39, "text": "90,", "ref_id": "BIBREF89"}, {"start": 40, "end": 43, "text": "94]", "ref_id": "BIBREF93"}], "ref_spans": [{"start": 123, "end": 124, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Datasets and Evaluation Protocols", "sec_num": "4.1"}, {"text": "Following previous studies [34, 50, 51, 86, 90] , we use ResNet-50 [29] pre-trained on ImageNet [13] with instance normalization layers as our backbone. All images are resized to 256 × 128. For each iteration, we sample 256 images, consisting of 64 identities with 4 instances for each identity. The total batch size during training is 512, including both original and augmented images. Random flipping, cropping, erasing [101] , RandAugment [11] , and color jitter are used for data augmentation. We train the model for 60 epochs using Adam [38] with a weight decay of 5 × 10 -4 . The initial learning rate is set to 3.5 × 10 -4 and is decreased by a factor of 10 at the 30th and 50th epochs. A warmup strategy is applied during the first 10 epochs. The momentum µ is set to 0.1. We empirically set the weighting parameter λ to 1.5 and k for the weighting strategy to 10. We implement our framework in PyTorch [64] and utilize two RTX-3090 GPUs for training.", "cite_spans": [{"start": 27, "end": 31, "text": "[34,", "ref_id": "BIBREF33"}, {"start": 32, "end": 35, "text": "50,", "ref_id": "BIBREF49"}, {"start": 36, "end": 39, "text": "51,", "ref_id": "BIBREF50"}, {"start": 40, "end": 43, "text": "86,", "ref_id": "BIBREF85"}, {"start": 44, "end": 47, "text": "90]", "ref_id": "BIBREF89"}, {"start": 67, "end": 71, "text": "[29]", "ref_id": "BIBREF28"}, {"start": 96, "end": 100, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 422, "end": 427, "text": "[101]", "ref_id": "BIBREF100"}, {"start": 442, "end": 446, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 542, "end": 546, "text": "[38]", "ref_id": "BIBREF37"}, {"start": 911, "end": 915, "text": "[64]", "ref_id": "BIBREF63"}], "ref_spans": [], "eq_spans": [], "section": "Implementation Details", "sec_num": "4.2"}, {"text": "We compare our method with state-of-the-art DG re-ID methods on Protocol-1, with the results presented in Table 3 . We also report the results of previous studies that use DukeMTMC-reID [98] for training, denoted as D in the table, while we exclude this dataset from training since it has been 4 . The results demonstrate that our method outperforms other methods, confirming the generalization capability of BAU on large-scale datasets. Specifically, we achieve higher average mAP scores across three datasets than the previous state-of-the-art ACL [90] , with improvements of +2.7% and +3.4% on protocol-2 and protocol-3, respectively.", "cite_spans": [{"start": 186, "end": 190, "text": "[98]", "ref_id": "BIBREF97"}, {"start": 550, "end": 554, "text": "[90]", "ref_id": "BIBREF89"}], "ref_spans": [{"start": 112, "end": 113, "text": "3", "ref_id": "TABREF2"}, {"start": 294, "end": 295, "text": "4", "ref_id": "TABREF3"}], "eq_spans": [], "section": "Comparison with State-of-the-Arts", "sec_num": "4.3"}, {"text": "It is also noteworthy that our method achieves state-of-the-art performance without employing advanced feature normalization modules [10, 35, 36, 48, 53] or domain-specific network architectures [12, 86, 90] . Specifically, BAU is a simple yet effective framework that regularizes the representation space by ensuring alignment and uniformity between original and augmented images, without relying on domain-adversarial [6] training or meta-learning [4, 94] strategies.", "cite_spans": [{"start": 133, "end": 137, "text": "[10,", "ref_id": "BIBREF9"}, {"start": 138, "end": 141, "text": "35,", "ref_id": "BIBREF34"}, {"start": 142, "end": 145, "text": "36,", "ref_id": "BIBREF35"}, {"start": 146, "end": 149, "text": "48,", "ref_id": "BIBREF47"}, {"start": 150, "end": 153, "text": "53]", "ref_id": "BIBREF52"}, {"start": 195, "end": 199, "text": "[12,", "ref_id": "BIBREF11"}, {"start": 200, "end": 203, "text": "86,", "ref_id": "BIBREF85"}, {"start": 204, "end": 207, "text": "90]", "ref_id": "BIBREF89"}, {"start": 420, "end": 423, "text": "[6]", "ref_id": "BIBREF5"}, {"start": 450, "end": 453, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 454, "end": 457, "text": "94]", "ref_id": "BIBREF93"}], "ref_spans": [], "eq_spans": [], "section": "Comparison with State-of-the-Arts", "sec_num": "4.3"}, {"text": "To evaluate the effectiveness of each component in BAU and validate our design choices, we conduct extensive ablation studies and analyses on Protocol-3, the most scalable setting in our experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation Study and Analysis", "sec_num": "4.4"}, {"text": "Ablation study of loss functions for augmented images. We first analyze the impact of applying different loss functions to the augmented images, as shown in Table 5 . The baseline model, trained using cross-entropy and triplet losses without any augmented images (when p = 0 of Sec. 3.1), serves as a reference point. Applying only the cross-entropy loss L ce to the augmented images leads to a performance drop, aligning with our analysis in Sec. 3.1 that naive training with augmented images can degrade generalization performance. This result also aligns with studies demonstrating that crossentropy loss can easily overfit to biases and noise in the data [25, 59, 92] . When applying the proposed alignment loss L align , the performance improves, surpassing the baseline. Further incorporating the uniformity loss L uniform significantly boosts the performance, confirming the importance of balancing both alignment and uniformity to achieve better generalization. Finally, integrating the domain-specific uniformity loss L domain achieves the best performance, with notable improvements of +10.2%/+8.0% in average mAP/Rank-1 over the baseline. This validates that mitigating domain bias by uniformly distributing features within each domain is effective. In summary, these results highlight that while simply training with augmentations can be detrimental, carefully regularizing the representation space through alignment and uniformity allows the model to benefit from the augmented data, leading to improved generalization.", "cite_spans": [{"start": 659, "end": 663, "text": "[25,", "ref_id": "BIBREF24"}, {"start": 664, "end": 667, "text": "59,", "ref_id": "BIBREF58"}, {"start": 668, "end": 671, "text": "92]", "ref_id": "BIBREF91"}], "ref_spans": [{"start": 163, "end": 164, "text": "5", "ref_id": "TABREF4"}], "eq_spans": [], "section": "Ablation Study and Analysis", "sec_num": "4.4"}, {"text": "Ablation study of weighting strategy and domain-specific uniformity loss. We conduct an ablation study to validate the effectiveness of the proposed weighting strategy for the alignment loss and the domain-specific uniformity loss, as shown in Table 6 . Without the weighting strategy in Eq. 3, the performance drops compared to the full BAU model, demonstrating the benefit of focusing on reliable pairs between original and augmented images to learn robust features. To investigate the importance of the domain-specific uniformity loss, we apply a simple uniformity loss with a feature memory bank without considering each domain (i.e., modifying Eq. ( 8) with nearest prototypes in the entire domain, not intra-domain). The performance decreases, highlighting the effectiveness of promoting uniformity within each domain for learning domain-invariant features. When both the weighting strategy and domain-specific prototypes are removed, the performance drops further, showing that each component provides complementary benefits, and integrating both in the BAU framework results in better generalization. This analysis validates the effectiveness of the proposed techniques, enabling BAU to achieve strong performance in the DG re-ID task by focusing on reliable pairs in alignment and promoting uniformity within each domain.", "cite_spans": [], "ref_spans": [{"start": 250, "end": 251, "text": "6", "ref_id": "TABREF5"}], "eq_spans": [], "section": "Ablation Study and Analysis", "sec_num": "4.4"}, {"text": "Analysis of alignment and uniformity. To further validate the effect of our proposed method on the learned representation space, we analyze the alignment and uniformity properties of the baseline and BAU. Fig. 4a illustrates these properties for the representations on the testing data of Market-1501 when MS+CS+C3 → M under Protocol-3. When data augmentations are applied, the representations of the baseline become less uniform, resulting in decreased generalization performance, as discussed in Sec. 3.1. In contrast, BAU consistently maintains better alignment and uniformity compared to the baseline across all augmentation probabilities. Notably, even when data augmentation is not applied (i.e., p = 0), BAU still outperforms the baseline. This demonstrates that in addition to preventing the polarizing effect of data augmentation, alignment and uniformity are important factors for generalization, aligning with recent studies [21, 65, 80] . In summary, by explicitly regularizing the representation space with both original and augmented images, BAU achieves better generalization performance by balancing feature discriminability and generalizability, confirming that it effectively leverages the diversity from data augmentations.", "cite_spans": [{"start": 936, "end": 940, "text": "[21,", "ref_id": "BIBREF20"}, {"start": 941, "end": 944, "text": "65,", "ref_id": "BIBREF64"}, {"start": 945, "end": 948, "text": "80]", "ref_id": "BIBREF79"}], "ref_spans": [{"start": 210, "end": 212, "text": "4a", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Ablation Study and Analysis", "sec_num": "4.4"}, {"text": "Analysis of weighting strategy and domain-specific uniformity loss. To explore the effect of the domain-specific uniformity loss, we visualize the t-SNE [74] plot of the training data when MS+CS+C3 → M under Protocol-3, with and without the domain-specific uniformity loss L domain . As shown in Fig. 4b , without L domain , the learned features exhibit domain-specific clusters, indicating a lack of domain invariance. On the other hand, applying L domain results in a more uniform distribution of features across domains, as evidenced by the increased uniformity values for each domain (reported in parentheses). This demonstrates the effectiveness of promoting uniformity within each domain to learn domain-invariant representations.", "cite_spans": [{"start": 153, "end": 157, "text": "[74]", "ref_id": "BIBREF73"}], "ref_spans": [{"start": 301, "end": 303, "text": "4b", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Ablation Study and Analysis", "sec_num": "4.4"}, {"text": "To further analyze the proposed weighting strategy for the alignment loss, we conduct quantitative comparisons across varying augmentation probabilities, with and without the weighting strategy. As shown in Fig. 5a , our weighting strategy consistently improves performance across different augmentation probabilities, with the gap becoming more pronounced at higher probabilities where severe corruption from augmentations is more likely. Specifically, at an augmentation probability of 0.5, the weighting strategy improves the mAP from 78.3% to 79.5%, and at a probability of 1.0, the improvement is even more substantial, from 66.1% to 76.1%. These results demonstrate that BAU effectively enables learning invariance with reliable augmented samples, thanks to the proposed weighting strategy, which focuses on reliable pairs between original and augmented images.", "cite_spans": [], "ref_spans": [{"start": 212, "end": 214, "text": "5a", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Ablation Study and Analysis", "sec_num": "4.4"}, {"text": "For qualitative analysis, Fig. 5b illustrates the weight scores w in Eq. ( 4) for the alignment loss. The top row shows the original images, while the bottom row represents their augmented versions with corresponding weight scores. As w progressively increases from left to right, the augmented images exhibit less distortion, indicating more reliable samples with informative augmentations. The weighting strategy allows the alignment loss to focus on augmented samples that are semantically similar to the original images, facilitating invariance learning from informative augmentations.", "cite_spans": [], "ref_spans": [{"start": 31, "end": 33, "text": "5b", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Ablation Study and Analysis", "sec_num": "4.4"}, {"text": "Although our method has shown promising results, it still has limitations to overcome. As a straightforward approach primarily based on data augmentations for given input data, our method could face challenges under very large domain shifts. While we adopt standard image-level augmentations, we do not incorporate more advanced techniques such as adversarial data augmentations [69, 75, 99, 104] or feature-level augmentations [37, 46, 106] , both of which have shown promise for generalization. Integrating these techniques with the proposed BAU framework could potentially further enhance generalization performance and would be an interesting direction for future research. Despite these limitations, our work is the first to thoroughly investigate and address the polarized effect of data augmentations in DG re-ID. It demonstrates the significant potential of balancing alignment and uniformity to improve generalization in person re-identification. We believe that our findings could inspire further research aimed at advancing generalization capabilities in this field.", "cite_spans": [{"start": 379, "end": 383, "text": "[69,", "ref_id": "BIBREF68"}, {"start": 384, "end": 387, "text": "75,", "ref_id": "BIBREF74"}, {"start": 388, "end": 391, "text": "99,", "ref_id": "BIBREF98"}, {"start": 392, "end": 396, "text": "104]", "ref_id": "BIBREF103"}, {"start": 428, "end": 432, "text": "[37,", "ref_id": "BIBREF36"}, {"start": 433, "end": 436, "text": "46,", "ref_id": "BIBREF45"}, {"start": 437, "end": 441, "text": "106]", "ref_id": "BIBREF105"}], "ref_spans": [], "eq_spans": [], "section": "Discussion", "sec_num": "5"}, {"text": "In this paper, we investigated the polarized effect of data augmentations in domain generalizable person re-identification. Our findings revealed that while augmentations enhance in-distribution performance, they can also lead to sparse representation spaces and deteriorate out-of-distribution performance. To address this issue, we proposed a simple yet effective framework, Balancing Alignment and Uniformity (BAU), which regularizes the representation space by maintaining a balance between alignment and uniformity. Comprehensive experiments on various benchmarks and protocols demonstrated that BAU achieves state-of-the-art performance without requiring advanced network architectures or complex training procedures. Furthermore, our extensive ablation studies and analyses validated the effectiveness of each component in BAU, emphasizing the importance of balancing alignment and uniformity to achieve robust generalization in person re-identification.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "This section provides additional implementation details for the experiments in Sec. 3.1. RandAugment [11] includes comprehensive geometric manipulations and color variations2 : '<PERSON><PERSON><PERSON>ras<PERSON>', 'Equalize', 'Invert', 'Rotate', 'Posterize', 'Solarize', 'Color', 'Contrast', 'Brightness', 'Sharpness', 'ShearX', 'ShearY', 'TranslateX', 'TranslateY', 'Cutout', 'SolarizeAdd'. Since person re-identification is a fine-grained task that requires distinguishing individuals with subtle distinctions, severe color distortion can deteriorate feature discriminability. Therefore, we exclude 'Invert', 'Posterize', 'Solarize', and 'SolarizeAdd' from the predefined set of RandAugment and additionally utilize Random Erasing. Following the conventional training pipeline [56] , we configured the in-distribution model training with a batch size of 64, consisting of 16 identities with 4 instances for each identity. We train the model for 120 epochs using Adam [38] optimizer with weight decay of 5 × 10 -4 . The initial learning rate is set to 3.5 × 10 -4 and is decreased by a factor of 10 at the 40th and 70th epochs. A warmup strategy is also applied during the first 10 epochs. For the out-of-distribution model training, we sample 256 images, consisting of 64 identities with 4 instances for each identity. Other settings are the same as the implementation details in the main paper.", "cite_spans": [{"start": 101, "end": 105, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 758, "end": 762, "text": "[56]", "ref_id": "BIBREF55"}, {"start": 948, "end": 952, "text": "[38]", "ref_id": "BIBREF37"}], "ref_spans": [], "eq_spans": [], "section": "A Additional Implementation Details", "sec_num": null}, {"text": "ViT-B/16 MobileNetV2 ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ResNet-50", "sec_num": null}, {"text": "To further explore the commonality of the polarized effect in data augmentation for DG re-ID, we investigate its presence across different backbones, loss functions, and augmentation types.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Commonality Analysis of Polarized Effect", "sec_num": null}, {"text": "Backbone and loss function. We examine the polarized effect across different backbone architectures, including transformer-based (ViT-B/16 [16] ) and lightweight (MobileNetV2 [67] ) networks. Following the same experimental protocol outlined in Sec. 3.1, we train these models using crossentropy and batch-hard triplet loss [30] , with and without data augmentations (i.e., Random Erasing [101] and RandAugment [11] ). As shown in Fig 6a , the polarized effect is consistently observed across different backbones. Additionally, we investigate this effect across different loss functions on a ResNet-50 backbone, including ArcFace [14] and PCL [23, 42] , which are widely used for re-ID and retrieval tasks. As shown in Fig. 6b , the polarized effect persists across all loss functions, and these results confirm that the polarized effect is not limited to specific architectures and loss functions.", "cite_spans": [{"start": 139, "end": 143, "text": "[16]", "ref_id": "BIBREF15"}, {"start": 175, "end": 179, "text": "[67]", "ref_id": "BIBREF66"}, {"start": 324, "end": 328, "text": "[30]", "ref_id": "BIBREF29"}, {"start": 389, "end": 394, "text": "[101]", "ref_id": "BIBREF100"}, {"start": 411, "end": 415, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 630, "end": 634, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 643, "end": 647, "text": "[23,", "ref_id": "BIBREF22"}, {"start": 648, "end": 651, "text": "42]", "ref_id": "BIBREF41"}], "ref_spans": [{"start": 435, "end": 437, "text": "6a", "ref_id": "FIGREF6"}, {"start": 724, "end": 726, "text": "6b", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "B.1 Commonality Analysis of Polarized Effect", "sec_num": null}, {"text": "Augmentation. We investigate the existence of the polarized effect of individual augmentations used in BAU, specifically Random Erasing, RandAugment, and Color Jitter. Following the experimental setting outlined in Sec. 3.1, we conduct experiments using a ResNet-50 backbone with cross-entropy and batch-hard triplet loss. As shown in Fig. 6c , the results indicate that the performance drop in the unseen domain is primarily driven by the polarized effects observed in Random Erasing and RandAugment, while Color Jitter does not exhibit this behavior, consistent with previous findings in the field. While Random Erasing and RandAugment can introduce significant distortions (e.g., pixel drops) to images, Color Jitter only provides simpler color distortions, which could enhance model robustness to variations in lighting and color conditions in unseen environments. Additionally, as shown in Fig. 7 , increasing the augmentation probability of Random Erasing and RandAugment further degrades performance and reduces uniformity, confirming the polarized effect caused by these augmentations in DG Re-ID.", "cite_spans": [], "ref_spans": [{"start": 340, "end": 342, "text": "6c", "ref_id": "FIGREF6"}, {"start": 900, "end": 901, "text": "7", "ref_id": "FIGREF7"}], "eq_spans": [], "section": "B.1 Commonality Analysis of Polarized Effect", "sec_num": null}, {"text": "In summary, we consistently observe the polarized effect across different backbones, loss functions, and augmentation types, suggesting that this phenomenon is general in DG re-ID. Furthermore, the proposed BAU consistently improves various baselines with different backbones and loss functions (Table . 7) and various augmentation types (Table . 8).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Commonality Analysis of Polarized Effect", "sec_num": null}, {"text": "To further validate the versatility and effectiveness of the proposed BAU framework, we conduct experiments across different backbone architectures and loss functions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 Evaluation of BAU across Backbones and Loss Functions", "sec_num": null}, {"text": "We first apply BAU to two distinct types of backbones: MobileNetV2 [67] , a lightweight network, and ViT-B/16 [16] , a transformer-based architecture. For training, we use cross-entropy and batch-hard triplet loss [30] with Random Erasing [101] and RandAugment [11] for augmentations. As shown in Table . 7, BAU consistently improves the performance of both baseline models across different backbones. For example, with MobileNetV2, BAU enhances the average mAP from 26.1% to 33.2% and Rank-1 from 38.0% to 45.7%, representing significant improvements. These results demonstrate the broad applicability of BAU across different backbones, suggesting that the proposed framework is effective regardless of underlying architecture.", "cite_spans": [{"start": 67, "end": 71, "text": "[67]", "ref_id": "BIBREF66"}, {"start": 110, "end": 114, "text": "[16]", "ref_id": "BIBREF15"}, {"start": 214, "end": 218, "text": "[30]", "ref_id": "BIBREF29"}, {"start": 239, "end": 244, "text": "[101]", "ref_id": "BIBREF100"}, {"start": 261, "end": 265, "text": "[11]", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "B.2 Evaluation of BAU across Backbones and Loss Functions", "sec_num": null}, {"text": "We further evaluate the effectiveness of BAU on models trained with different loss functions, Arc-Face [14] and PCL [23, 42] . For training, we employ a ResNet-50 backbone with Random Erasing and RandAugment for augmentations. As shown in Table . 7, BAU consistently improves the performance of both baselines across different loss functions. For instance, with ArcFace, BAU increases the average mAP from 36.8% to 44.6% and Rank-1 from 50.3% to 59.3%, delivering substantial gains. These results confirm that BAU is not limited to any specific loss function and can be seamlessly integrated into various re-ID loss formulations to enhance generalization capabilities.", "cite_spans": [{"start": 103, "end": 107, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 116, "end": 120, "text": "[23,", "ref_id": "BIBREF22"}, {"start": 121, "end": 124, "text": "42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "B.2 Evaluation of BAU across Backbones and Loss Functions", "sec_num": null}, {"text": "In summary, these results validate the effectiveness and versatility of BAU, showing that it can be applied across diverse architectures and loss functions to improve generalization in DG re-ID. Notably, BAU achieves these improvements as a simple regularization technique, without requiring complex training procedures or additional trainable parameters. This highlights its efficiency and potential for easy integration into various baseline models in the DG re-ID task.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 Evaluation of BAU across Backbones and Loss Functions", "sec_num": null}, {"text": "Table 8 presents an ablation study on the impact of different data augmentations on the generalization performance of our method under Protocol-3. We investigate three augmentations used in the proposed method: Random Erasing [101] , RandAugment [11] , and Color Jitter. As shown in the table, the results demonstrate that each augmentation technique improves the generalization performance, indicating that our method successfully mitigates the polarized effect of data augmentations. Specifically, even though Random Erasing has generally been shown to degrade generalization performance in previous studies, our method can effectively exploit the advantages of this augmentation technique. Furthermore, it is worth noting that even without any augmentations, the proposed method still outperforms the baseline (see Table 5 in the main paper), highlighting the effectiveness of balancing alignment and uniformity in improving generalization. When all three augmentations are applied together, we achieve the best performance, with an average mAP of 52.3% and Rank-1 accuracy of 65.7%. This highlights that the diversity of data augmentations enables the proposed method to learn more diverse information from the data, thereby enhancing feature generalizability.", "cite_spans": [{"start": 226, "end": 231, "text": "[101]", "ref_id": "BIBREF100"}, {"start": 246, "end": 250, "text": "[11]", "ref_id": "BIBREF10"}], "ref_spans": [{"start": 6, "end": 7, "text": "8", "ref_id": "TABREF8"}, {"start": 824, "end": 825, "text": "5", "ref_id": "TABREF4"}], "eq_spans": [], "section": "B.3 Ablation Study of Data Augmentation", "sec_num": null}, {"text": "To further investigate the impact of data augmentation on our method, we conduct experiments by varying the probability p of applying data augmentations during training. As shown in Table 9 , increasing the augmentation probability generally leads to better generalization performance up to a certain point. Setting p to 0.5 yields the best results, so we empirically set this configuration. Further increasing the probability to 0.75 or 1.0 leads to a slight decrease in performance, indicating that excessive overlap of the three augmentations may introduce noise and hinder the learning process.", "cite_spans": [], "ref_spans": [{"start": 188, "end": 189, "text": "9", "ref_id": "TABREF9"}], "eq_spans": [], "section": "B.3 Ablation Study of Data Augmentation", "sec_num": null}, {"text": "In summary, these experimental results validate the effectiveness of data augmentations in enhancing the generalization capability for person re-identification -a potential that previous studies have not fully explored. The proposed BAU framework enables the model to leverage the diversity introduced by augmentations, resulting in more robust and generalizable representations for unseen domains.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Ablation Study of Data Augmentation", "sec_num": null}, {"text": "We conduct a parameter analysis to investigate the impact of the weighting strategy and the alignment loss on the generalization performance of our proposed method. Specifically, we evaluate the effect of varying the number of k-reciprocal nearest neighbors k for the weighting strategy and the weighting parameter λ for the alignment loss on the MS+C3+CS → M setting under Protocol-3. Figure 8a shows the mAP and Rank-1 accuracy of our method with different values of k for the weighting strategy. As k increases, the performance initially improves, reaching the best results at k = 10 with an mAP of 79.5% and a Rank-1 accuracy of 91.1%. However, when k is set to 5, the performance slightly lags behind the baseline (see Table 5 in the main paper). This result implies that the weighting strategy is not effective if unreliable weights are used. Setting k to 5 is too strict for computing reliable weights, as we observed that it causes the scores of too many samples to be close to zero, leading to insufficient learning of alignment. Figure 8b presents the mAP and Rank-1 accuracy of our method with different values of λ for the alignment loss. We observe that setting λ to 1.5 yields the best performance. This demonstrates the importance of balancing the contribution of the alignment loss with other loss terms. When λ is too small, the alignment loss may not sufficiently enforce feature invariance to augmentations. On the other hand, a large λ may overemphasize alignment and hinder uniformity, leading to the learning of less diverse information.", "cite_spans": [], "ref_spans": [{"start": 393, "end": 395, "text": "8a", "ref_id": "FIGREF9"}, {"start": 730, "end": 731, "text": "5", "ref_id": "TABREF4"}, {"start": 1046, "end": 1048, "text": "8b", "ref_id": "FIGREF9"}], "eq_spans": [], "section": "B.4 Parameter Analysis", "sec_num": null}, {"text": "In summary, these results highlight the importance of a reliable weighting strategy and achieving a balance between alignment and uniformity. The optimal values of k = 10 and λ = 1.5 are used in all our experiments reported in the main paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.4 Parameter Analysis", "sec_num": null}, {"text": "The insights behind our method, balancing alignment and uniformity to induce feature diversity, could be valuable for improving generalization in other fine-grained and open-set retrieval tasks. By extending this approach to related domains, such as vehicle re-identification or face recognition, the robustness and real-world applicability of various computer vision systems could be enhanced. Furthermore, by learning more generalizable domain-invariant features, the proposed method could mitigate biases (e.g., race, gender) in person re-identification systems by focusing on domain-agnostic visual cues rather than spurious correlations. Further research specifically evaluating fairness impacts would be valuable to verify and quantify this potential benefit.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Broader Impacts", "sec_num": null}, {"text": "However, person re-identification techniques can potentially have negative impacts, such as the infringement of privacy due to the abuse of surveillance systems. For instance, these methods can raise privacy concerns, as individuals may be tracked without consent as they move through public spaces. Researchers and users of person re-identification technology should be attentive to using it in an appropriate manner while considering ethical issues. Particular care should be taken to avoid using datasets with known ethical concerns for research. The development and deployment of person re-identification systems should be accompanied by appropriate safeguards and regulations to prevent misuse and protect individual privacy rights.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Broader Impacts", "sec_num": null}, {"text": "NeurIPS Paper Checklist", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Broader Impacts", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Answer: [Yes] Justification: The claims are clearly stated in the abstract and the introduction.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Justification: The paper includes the limitations in the appendix Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Answer • If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction). 10. Broader Impacts Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed? Answer: [Yes] Justification: The paper includes broader impacts in the appendix. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA] Justification: The paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Justification: The paper properly mentions existing assets with citations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "38th Conference on Neural Information Processing Systems (NeurIPS 2024).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "More details are provided in the appendix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://github.com/tensorflow/tpu/tree/master/models/official/efficientnet", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "We would like to thank the anonymous reviewers for their constructive comments and suggestions. This work was supported by the Institute of Information & communications Technology Planning & Evaluation (IITP) grant (No. RS-*************, Recognition, Action and Interaction Algorithms for Open-world Robot Service) and the National Research Foundation of Korea (NRF) grant (No. RS-************* (2024)), both funded by the Korea government (MSIT). Prof. <PERSON><PERSON><PERSON> is a corresponding author (e-mail: <EMAIL>).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "Justification: The paper does not include theoretical results. Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorem<PERSON> and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: The paper includes the implementation details in the experiment section and the appendix. Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?• If this information is not available online, the authors are encouraged to reach out to the asset's creators. 13 • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [{"start": 301, "end": 303, "text": "13", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "An improved deep learning architecture for person re-identification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["K"], "last": "Marks", "suffix": ""}], "year": 2015, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. An improved deep learning architecture for person re-identification. In CVPR, 2015.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Dex: Domain embedding expansion for generalized person re-identification", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "BMVC", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Dex: Domain embedding expansion for generalized person re-identification. In BMVC, 2021.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "How to train your maml", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. How to train your maml. In ICLR, 2019.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Person30k: A dual-meta generalization network for person re-identification", "authors": [{"first": "Yan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ce", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xuetao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Person30k: A dual-meta generalization network for person re-identification. In CVPR, 2021.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Domain-specific batch normalization for unsupervised domain adaptation", "authors": [{"first": "<PERSON>oong<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Tack<PERSON><PERSON>", "middle": [], "last": "You", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Kwak", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Han", "suffix": ""}], "year": 2019, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Domain-specific batch normalization for unsupervised domain adaptation. In CVPR, 2019.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Dual distribution alignment network for generalizable person re-identification", "authors": [{"first": "Peixian", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Pingyang", "middle": [], "last": "Dai", "suffix": ""}, {"first": "Jianzhuang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qi", "middle": [], "last": "Tian", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "AAAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and Rongro<PERSON> Ji. Dual distribution alignment network for generalizable person re-identification. In AAAI, 2021.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Abd-net: Attentive but diverse person re-identification", "authors": [{"first": "Tianlong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shaojin", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ye", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "Wuyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ren", "suffix": ""}, {"first": "Zhangyang", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Abd-net: Attentive but diverse person re-identification. In ICCV, 2019.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Instance-dependent label-noise learning with manifold-regularized transition matrix estimation", "authors": [{"first": "De", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Tongliang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>ng", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "Gang", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xin<PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Instance-dependent label-noise learning with manifold-regularized transition matrix estimation. In CVPR, 2022.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Part-based pseudo label refinement for unsupervised person re-identification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Cho", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Woo", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>on", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Part-based pseudo label refinement for unsupervised person re-identification. In CVPR, 2022.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Meta batch-instance normalization for generalizable person re-identification", "authors": [{"first": "Seokeon", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Park", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Meta batch-instance normalization for generalizable person re-identification. In CVPR, 2021.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Randaugment: Practical automated data augmentation with a reduced search space", "authors": [{"first": "Barret", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Zoph", "suffix": ""}, {"first": "Quoc V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Le", "suffix": ""}], "year": 2020, "venue": "CVPR Workshops", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> <PERSON>. Randaugment: Practical automated data augmentation with a reduced search space. In CVPR Workshops, 2020.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Generalizable person reidentification with relevance-aware mixture of experts", "authors": [{"first": "Yongxing", "middle": [], "last": "Dai", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Generalizable person re- identification with relevance-aware mixture of experts. In CVPR, 2021.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Imagenet: A large-scale hierarchical image database", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Imagenet: A large-scale hierarchical image database. In CVPR, 2009.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Arcface: Additive angular margin loss for deep face recognition", "authors": [{"first": "Jiankang", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "CVPR", "volume": "", "issue": "", "pages": "4690--4699", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Arcface: Additive angular margin loss for deep face recognition. In CVPR, pages 4690-4699, 2019.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Improved regularization of convolutional neural networks with cutout", "authors": [{"first": "Terrance", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1708.04552"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Improved regularization of convolutional neural networks with cutout. arXiv preprint arXiv:1708.04552, 2017.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "Xiaohua", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. An image is worth 16x16 words: Transformers for image recognition at scale. In ICLR, 2021.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Identity-seeking self-supervised representation learning for generalizable person re-identification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Shengjin", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Identity-seeking self-supervised represen- tation learning for generalizable person re-identification. In ICCV, 2023.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Model-agnostic meta-learning for fast adaptation of deep networks", "authors": [{"first": "Chelsea", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Model-agnostic meta-learning for fast adaptation of deep networks. In ICML, 2017.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Selfsimilarity grouping: A simple unsupervised cross domain adaptation approach for person re-identification", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Honghui", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Self- similarity grouping: A simple unsupervised cross domain adaptation approach for person re-identification. In ICCV, 2019.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Domain-adversarial training of neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Evgen<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "March", "suffix": ""}, {"first": "Victor", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Journal of Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Domain-adversarial training of neural networks. Journal of Machine Learning Research, 2016.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Simcse: Simple contrastive learning of sentence embeddings", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Xingcheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "EMNLP", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Simcse: Simple contrastive learning of sentence embed- dings. In EMNLP, 2021.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Mutual mean-teaching: Pseudo label refinery for unsupervised domain adaptation on person re-identification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ge", "suffix": ""}, {"first": "<PERSON><PERSON>g", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hongsheng", "middle": [], "last": "Li", "suffix": ""}], "year": 2020, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Mutual mean-teaching: Pseudo label refinery for unsuper- vised domain adaptation on person re-identification. In ICLR, 2020.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Self-paced contrastive learning with hybrid memory for domain adaptive object re-id", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ge", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>g", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. Self-paced contrastive learning with hybrid memory for domain adaptive object re-id. NeurIPS, 2020.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Imagenet-trained cnns are biased towards texture; increasing shape bias improves accuracy and robustness", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bethge", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Brendel", "suffix": ""}], "year": 2019, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Imagenet-trained cnns are biased towards texture; increasing shape bias improves accuracy and robustness. In ICLR, 2019.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Dissecting supervised contrastive learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Dissecting supervised contrastive learning. In ICML, 2021.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Viewpoint invariant pedestrian recognition with an ensemble of localized features", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Tao", "suffix": ""}], "year": 2008, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Viewpoint invariant pedestrian recognition with an ensemble of localized features. In ECCV, 2008.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Generalizable person re-identification via self-supervised batch norm test-time adaption", "authors": [{"first": "<PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "Chenyang", "middle": [], "last": "Si", "suffix": ""}, {"first": "Yan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "AAAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Generalizable person re-identification via self-supervised batch norm test-time adaption. In AAAI, 2022.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Momentum contrast for unsupervised visual representation learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Fan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Saining", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Momentum contrast for unsupervised visual representation learning. In CVPR, 2020.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Deep residual learning for image recognition", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "Xiangyu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shaoqing", "middle": [], "last": "Ren", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}], "year": 2016, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep residual learning for image recognition. In CVPR, 2016.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "defense of the triplet loss for person reidentification", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1703.07737"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. In defense of the triplet loss for person re- identification. arXiv preprint arXiv:1703.07737, 2017.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Person re-identification by descriptive and discriminative classification", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Csaba", "middle": [], "last": "Beleznai", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>ischof", "suffix": ""}], "year": 2011, "venue": "SCIA", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Person re-identification by descriptive and discriminative classification. In SCIA, 2011.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Learning with neighbor consistency for noisy labels", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Iscen", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Valmadre", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Cordelia", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Learning with neighbor consistency for noisy labels. In CVPR, 2022.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Adaptive mixtures of local experts", "authors": [{"first": "<PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["I"], "last": "Jordan", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Nowlan", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1991, "venue": "Neural Computation", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, and <PERSON>. Adaptive mixtures of local experts. Neural Computation, 1991.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Frustratingly easy person re-identification: Generalizing person re-id in practice", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>uan", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "BMVC", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Frustratingly easy person re-identification: Generaliz- ing person re-id in practice. In BMVC, 2019.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Dynamically transformed instance normalization network for generalizable person re-identification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Guosheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shizhou", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yanning", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Dynamically transformed instance normalization network for generalizable person re-identification. In ECCV, 2022.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Style normalization and restitution for generalizable person re-identification", "authors": [{"first": "Xin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Lan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Style normalization and restitution for generalizable person re-identification. In CVPR, 2020.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Cross-class feature augmentation for class incremental learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Park", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Han", "suffix": ""}], "year": 2024, "venue": "AAAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Cross-class feature augmentation for class incremental learning. In AAAI, 2024.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Adam: A method for stochastic optimization", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Ba", "suffix": ""}], "year": 2015, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>: A method for stochastic optimization. In ICLR, 2015.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Imagenet classification with deep convolutional neural networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "NIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Imagenet classification with deep convolutional neural networks. In NIPS, 2012.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Learning to generalize: Meta-learning for domain generalization", "authors": [{"first": "Da", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "AAAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Learning to generalize: Meta-learning for domain generalization. In AAAI, 2018.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Domain generalization with adversarial feature learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Domain generalization with adversarial feature learning. In CVPR, 2018.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Prototypical contrastive learning of unsupervised representations", "authors": [{"first": "Jun<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Pan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Caiming", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hoi", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Prototypical contrastive learning of unsupervised representations. In ICLR, 2021.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Locally aligned feature transforms across views", "authors": [{"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2013, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. Locally aligned feature transforms across views. In CVPR, 2013.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Deepreid: Deep filter pairing neural network for person re-identification", "authors": [{"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Deepreid: Deep filter pairing neural network for person re-identification. In CVPR, 2014.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Harmonious attention network for person re-identification", "authors": [{"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S<PERSON>gang", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Harmonious attention network for person re-identification. In CVPR, 2018.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Uncertainty modeling for out-of-distribution generalization", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Yongxing", "middle": [], "last": "Dai", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ge", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Uncertainty modeling for out-of-distribution generalization. In ICLR, 2022.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Revisiting batch normalization for practical domain adaptation", "authors": [{"first": "Yang<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1603.04779"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Revisiting batch normalization for practical domain adaptation. arXiv preprint arXiv:1603.04779, 2016.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Style-controllable generalized person reidentification", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "ACM MM", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Style-controllable generalized person re- identification. In ACM MM, 2023.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Mind the gap: Understanding the modality gap in multi-modal contrastive representation learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Serena", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Mind the gap: Understanding the modality gap in multi-modal contrastive representation learning. NeurIPS, 2022.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Interpretable and generalizable person re-identification with query-adaptive convolution and temporal lifting", "authors": [{"first": "Shengcai", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON>. Interpretable and generalizable person re-identification with query-adaptive convolution and temporal lifting. In ECCV, 2020.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Transmatcher: Deep image matching through transformers for generalizable person re-identification", "authors": [{"first": "Shengcai", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON>. Transmatcher: Deep image matching through transformers for generalizable person re-identification. In NeurIPS, 2021.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Graph sampling based deep metric learning for generalizable person re-identification", "authors": [{"first": "Shengcai", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON>. Graph sampling based deep metric learning for generalizable person re-identification. In CVPR, 2022.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Debiased batch normalization via gaussian process for generalizable person re-identification", "authors": [{"first": "Jiaw<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Kecheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "AAAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Debiased batch normalization via gaussian process for generalizable person re-identification. In AAAI, 2022.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Adaptive transfer network for cross-domain person re-identification", "authors": [{"first": "Jiaw<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Di", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "<PERSON>g", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Adaptive transfer network for cross-domain person re-identification. In CVPR, 2019.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Multi-camera activity correlation analysis", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "Tao <PERSON>, and S<PERSON><PERSON><PERSON> Gong", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Multi-camera activity correlation analysis. In CVPR, 2009.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "A strong baseline and batch normalization neck for deep person re-identification", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "Fuxu", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "Xingyu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2019, "venue": "IEEE Transactions on Multimedia", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. A strong baseline and batch normalization neck for deep person re-identification. IEEE Transactions on Multimedia, 2019.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Coco-lm: Correcting and contrasting text sequences for language model pretraining", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Payal", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jiaw<PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. Coco-lm: Correcting and contrasting text sequences for language model pretraining. NeurIPS, 2021.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Diverse visual feature aggregation for deep metric learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Homanga", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Samarth", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ommer", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Diva: Diverse visual feature aggregation for deep metric learning. In ECCV, 2020.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Learning from failure: Debiasing classifier from biased classifier", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Nam", "suffix": ""}, {"first": "Hyuntak", "middle": [], "last": "Cha", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ahn", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Learning from failure: De- biasing classifier from biased classifier. NeurIPS, 2020.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Part-aware transformer for generalizable person re-identification", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Heng", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Song", "suffix": ""}], "year": 2023, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Part-aware transformer for generalizable person re-identification. In ICCV, 2023.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Meta distribution alignment for generalizable person re-identification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Meta distribution alignment for generalizable person re-identification. In CVPR, 2022.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "On first-order meta-learning algorithms", "authors": [{"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1803.02999"]}, "num": null, "urls": [], "raw_text": "A Nichol. On first-order meta-learning algorithms. arXiv preprint arXiv:1803.02999, 2018.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Geodesic multi-modal mixup for robust fine-tuning", "authors": [{"first": "Changdae", "middle": [], "last": "Oh", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "So", "suffix": ""}, {"first": "Hoyoon", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Lim", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jong-<PERSON>", "middle": [], "last": "Jeon", "suffix": ""}, {"first": "Kyungwoo", "middle": [], "last": "Song", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Geodesic multi-modal mixup for robust fine-tuning. NeurIPS, 2023.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Pytorch: An imperative style, highperformance deep learning library", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "Gross", "suffix": ""}, {"first": "Francisco", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Natalia", "middle": [], "last": "Gimelshein", "suffix": ""}, {"first": "Luca", "middle": [], "last": "Antiga", "suffix": ""}], "year": 2019, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Pytorch: An imperative style, high- performance deep learning library. In NeurIPS, 2019.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Alignment-uniformity aware representation learning for zero-shot video classification", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Alignment-uniformity aware representation learning for zero-shot video classification. In CVPR, 2022.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "A closer look at smoothness in domain adversarial training", "authors": [{"first": "Hars<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. A closer look at smoothness in domain adversarial training. In ICML, 2022.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "Mobilenetv2: Inverted residuals and linear bottlenecks", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Mobilenetv2: Inverted residuals and linear bottlenecks. In CVPR, 2018.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "Grad-cam: Visual explanations from deep networks via gradient-based localization", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Cogswell", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Vedantam", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Batra", "suffix": ""}], "year": 2017, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Grad-cam: Visual explanations from deep networks via gradient-based localization. In ICCV, 2017.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "Generalizing across domains via cross-gradient training", "authors": [{"first": "Shiv", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Soumen", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Generalizing across domains via cross-gradient training. In ICLR, 2018.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "Very deep convolutional networks for large-scale image recognition", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1409.1556"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Very deep convolutional networks for large-scale image recognition. arXiv preprint arXiv:1409.1556, 2014.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "Generalizable person re-identification by domain-invariant mapping network", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "Tao", "middle": [], "last": "Xiang", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Generalizable person re-identification by domain-invariant mapping network. In CVPR, 2019.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "Pose-driven deep convolutional model for person re-identification", "authors": [{"first": "<PERSON>", "middle": [], "last": "Su", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Xi<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Qi", "middle": [], "last": "Tian", "suffix": ""}], "year": 2017, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Pose-driven deep convolutional model for person re-identification. In ICCV, 2017.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "Beyond part models: Person retrieval with refined part pooling (and a strong convolutional baseline)", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qi", "middle": [], "last": "Tian", "suffix": ""}, {"first": "Shengjin", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Beyond part models: Person retrieval with refined part pooling (and a strong convolutional baseline). In ECCV, 2018.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "Visualizing data using t-sne", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2008, "venue": "Journal of Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Visualizing data using t-sne. Journal of Machine Learning Research, 2008.", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "Generalizing to unseen domains via adversarial data augmentation", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Vol<PERSON>", "suffix": ""}, {"first": "Hongseok", "middle": [], "last": "Namkoong", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Generalizing to unseen domains via adversarial data augmentation. In NeurIPS, 2018.", "links": null}, "BIBREF75": {"ref_id": "b75", "title": "Tent: Fully test-time adaptation by entropy minimization", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Shaoteng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Olshausen", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Tent: Fully test-time adaptation by entropy minimization. In ICLR, 2021.", "links": null}, "BIBREF76": {"ref_id": "b76", "title": "Understanding the behaviour of contrastive loss", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. Understanding the behaviour of contrastive loss. In CVPR, 2021.", "links": null}, "BIBREF77": {"ref_id": "b77", "title": "Learning discriminative features with multiple granularities for person re-identification", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yufeng", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Xi", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "ACM MM", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Learning discriminative features with multiple granularities for person re-identification. In ACM MM, 2018.", "links": null}, "BIBREF78": {"ref_id": "b78", "title": "Generalizing to unseen domains: A survey on domain generalization", "authors": [{"first": "Jindong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Lan", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yidong", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "Tao", "middle": [], "last": "Qin", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2022, "venue": "IEEE Transactions on Knowledge and Data Engineering", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Generalizing to unseen domains: A survey on domain generalization. IEEE Transactions on Knowledge and Data Engineering, 2022.", "links": null}, "BIBREF79": {"ref_id": "b79", "title": "Feature alignment and uniformity for test time adaptation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zipei", "middle": [], "last": "Yan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}], "year": 2023, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Feature alignment and uniformity for test time adaptation. In CVPR, 2023.", "links": null}, "BIBREF80": {"ref_id": "b80", "title": "Understanding contrastive representation learning through alignment and uniformity on the hypersphere", "authors": [{"first": "Tongzhou", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Isola", "suffix": ""}], "year": 2020, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Understanding contrastive representation learning through alignment and uniformity on the hypersphere. In ICML, 2020.", "links": null}, "BIBREF81": {"ref_id": "b81", "title": "Domainmix: Learning generalizable person re-identification without human annotations", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shengcai", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "BMVC", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Domainmix: Learning generalizable person re-identification without human annotations. In BMVC, 2021.", "links": null}, "BIBREF82": {"ref_id": "b82", "title": "Person transfer gan to bridge domain gap for person re-identification", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Qi", "middle": [], "last": "Tian", "suffix": ""}], "year": 2018, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Person transfer gan to bridge domain gap for person re-identification. In CVPR, 2018.", "links": null}, "BIBREF83": {"ref_id": "b83", "title": "A topological filter for learning with label noise", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Metaxas", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. A topological filter for learning with label noise. NeurIPS, 2020.", "links": null}, "BIBREF84": {"ref_id": "b84", "title": "Joint detection and identification feature learning for person search", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Bochao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Joint detection and identification feature learning for person search. In CVPR, 2017.", "links": null}, "BIBREF85": {"ref_id": "b85", "title": "Mimic embedding via adaptive aggregation: learning generalizable person re-identification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}], "year": 2022, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Mimic embedding via adaptive aggregation: learning generalizable person re-identification. In ECCV, 2022.", "links": null}, "BIBREF86": {"ref_id": "b86", "title": "Learning with diversity: Selfexpanded equalization for better generalized deep metric learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Yan", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yanhua", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Heng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Learning with diversity: Self- expanded equalization for better generalized deep metric learning. In ICCV, 2023.", "links": null}, "BIBREF87": {"ref_id": "b87", "title": "Deep learning for person re-identification: A survey and outlook", "authors": [{"first": "<PERSON>g", "middle": [], "last": "Ye", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Tao", "middle": [], "last": "Xiang", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hoi", "suffix": ""}], "year": 2021, "venue": "IEEE Transactions on Pattern Analysis and Machine Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Deep learning for person re-identification: A survey and outlook. IEEE Transactions on Pattern Analysis and Machine Intelligence, 2021.", "links": null}, "BIBREF88": {"ref_id": "b88", "title": "Cutmix: Regularization strategy to train strong classifiers with localizable features", "authors": [{"first": "Sangdoo", "middle": [], "last": "Yun", "suffix": ""}, {"first": "Dongyoon", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Junsuk", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>o", "suffix": ""}], "year": 2019, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Cutmix: Regularization strategy to train strong classifiers with localizable features. In ICCV, 2019.", "links": null}, "BIBREF89": {"ref_id": "b89", "title": "Adaptive cross-domain learning for generalizable person re-identification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xi", "middle": [], "last": "Li", "suffix": ""}], "year": 2022, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Adaptive cross-domain learning for generalizable person re-identification. In ECCV, 2022.", "links": null}, "BIBREF90": {"ref_id": "b90", "title": "Learning domain invariant representations for generalizable person re-identification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Da", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "IEEE Transactions on Image Processing", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Learning domain invariant representations for generalizable person re-identification. IEEE Transactions on Image Processing, 2022.", "links": null}, "BIBREF91": {"ref_id": "b91", "title": "Generalized cross entropy loss for training deep neural networks with noisy labels", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Mert", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>. Generalized cross entropy loss for training deep neural networks with noisy labels. NeurIPS, 2018.", "links": null}, "BIBREF92": {"ref_id": "b92", "title": "Maximum-entropy adversarial data augmentation for improved generalization and robustness", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>g", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xi", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Metaxas", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Maximum-entropy adversarial data augmentation for improved generalization and robustness. In NeruIPS, 2020.", "links": null}, "BIBREF93": {"ref_id": "b93", "title": "Learning to generalize unseen domains via memory-based multi-source meta-learning for person reidentification", "authors": [{"first": "Yuyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zhun", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Fengxiang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yaojin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Learning to generalize unseen domains via memory-based multi-source meta-learning for person re- identification. In CVPR, 2021.", "links": null}, "BIBREF94": {"ref_id": "b94", "title": "Scalable person re-identification: A benchmark", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Tian", "suffix": ""}, {"first": "Shengjin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jingdong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qi", "middle": [], "last": "Tian", "suffix": ""}], "year": 2015, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Scalable person re-identification: A benchmark. In ICCV, 2015.", "links": null}, "BIBREF95": {"ref_id": "b95", "title": "Person re-identification: Past, present and future", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1610.02984"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Person re-identification: Past, present and future. arXiv preprint arXiv:1610.02984, 2016.", "links": null}, "BIBREF96": {"ref_id": "b96", "title": "Associating groups of people", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S<PERSON>gang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Tao", "middle": [], "last": "Xiang", "suffix": ""}], "year": 2009, "venue": "BMVC", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Associating groups of people. In BMVC, 2009.", "links": null}, "BIBREF97": {"ref_id": "b97", "title": "Unlabeled samples generated by gan improve the person re-identification baseline in vitro", "authors": [{"first": "Zhedong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "ICCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Unlabeled samples generated by gan improve the person re-identification baseline in vitro. In ICCV, 2017.", "links": null}, "BIBREF98": {"ref_id": "b98", "title": "Adversarial style augmentation for domain generalized urban-scene segmentation", "authors": [{"first": "Zhun", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yuyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Gim", "middle": [], "last": "", "suffix": ""}, {"first": "Hee", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Adversarial style augmentation for domain generalized urban-scene segmentation. In NeurIPS, 2022.", "links": null}, "BIBREF99": {"ref_id": "b99", "title": "Re-ranking person re-identification with k-reciprocal encoding", "authors": [{"first": "Zhun", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Donglin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}], "year": 2017, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Re-ranking person re-identification with k-reciprocal encoding. In CVPR, 2017.", "links": null}, "BIBREF100": {"ref_id": "b100", "title": "Random erasing data augmentation", "authors": [{"first": "Zhun", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "AAAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Random erasing data augmentation. In AAAI, 2020.", "links": null}, "BIBREF101": {"ref_id": "b101", "title": "Invariance matters: Exemplar memory for domain adaptive person re-identification", "authors": [{"first": "Zhun", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Invariance matters: Exemplar memory for domain adaptive person re-identification. In CVPR, 2019.", "links": null}, "BIBREF102": {"ref_id": "b102", "title": "Domain generalization: A survey", "authors": [{"first": "Kaiyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Tao", "middle": [], "last": "Xiang", "suffix": ""}, {"first": "<PERSON>", "middle": ["Change"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "IEEE Transactions on Pattern Analysis and Machine Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Domain generalization: A survey. IEEE Transactions on Pattern Analysis and Machine Intelligence, 2022.", "links": null}, "BIBREF103": {"ref_id": "b103", "title": "Deep domain-adversarial image generation for domain generalisation", "authors": [{"first": "Kaiyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Tao", "middle": [], "last": "Xiang", "suffix": ""}], "year": 2020, "venue": "AAAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Deep domain-adversarial image generation for domain generalisation. In AAAI, 2020.", "links": null}, "BIBREF104": {"ref_id": "b104", "title": "Learning to generate novel domains for domain generalization", "authors": [{"first": "Kaiyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Tao", "middle": [], "last": "Xiang", "suffix": ""}], "year": 2020, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Learning to generate novel domains for domain generalization. In ECCV, 2020.", "links": null}, "BIBREF105": {"ref_id": "b105", "title": "Domain generalization with mixstyle", "authors": [{"first": "Kaiyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Tao", "middle": [], "last": "Xiang", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Domain generalization with mixstyle. In ICLR, 2021.", "links": null}, "BIBREF106": {"ref_id": "b106", "title": "Rethinking the distribution gap of person re-identification with camera-based batch normalization", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Lingxi", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Haizhou", "middle": [], "last": "Ai", "suffix": ""}, {"first": "Qi", "middle": [], "last": "Tian", "suffix": ""}], "year": 2020, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Haizhou Ai, and <PERSON>. Rethinking the distribution gap of person re-identification with camera-based batch normalization. In ECCV, 2020.", "links": null}}, "ref_entries": {"FIGREF0": {"fig_num": "1", "uris": null, "type_str": "figure", "text": "Figure 1: Analysis on polarized effect of data augmentations on in-distribution (ID) and out-ofdistribution (OOD). (a) mAP (%) on Market-1501 of models trained on the same dataset (ID) and MS+CS+C3 (OOD) with varying augmentation probabilities. (b) Alignment (L align ) and uniformity (L uniform ) of OOD scenarios (MS+CS+C3 → M). Counterintuitively, augmentations lead to more alignment but less uniformity, indicating that the model fails to sufficiently preserve the diverse information from the data distribution. (c) Uniformity (-L uniform ) vs. augmentation probability for the source and target datasets in MS+CS+C3 → M. Higher probabilities result in less uniformity, especially under distribution shifts, indicating an insufficiency in representing OOD data.", "num": null}, "FIGREF1": {"fig_num": "2", "uris": null, "type_str": "figure", "text": "Figure 2: Grad-CAM [68] across different probabilities of data augmentations.", "num": null}, "FIGREF2": {"fig_num": null, "uris": null, "type_str": "figure", "text": "ℒ align & ℒ uniform (c) ℒ domain", "num": null}, "FIGREF3": {"fig_num": "3", "uris": null, "type_str": "figure", "text": "Figure 3: Overview of the proposed framework. In (b) and (c), each color represents a different identity and domain, respectively. (a) With original and augmented images, we apply alignment and uniformity losses to balance feature discriminability and generalization capability. We further introduce a domain-specific uniformity loss to mitigate domain bias. (b) L align pulls positive features closer, while L uniform pushes all features apart to maintain diversity. (c) L domain uniformly distributes each domain's features and prototypes, reducing domain bias and thus enhancing generalization.", "num": null}, "FIGREF4": {"fig_num": "4", "uris": null, "type_str": "figure", "text": "Figure 4: Analysis of alignment and uniformity. (a) Alignment (L align ) and uniformity (L uniform ) on Market-1501 when MS+CS+C3 → M under Protocol-3 with varying augmentation probabilities. (b) T-SNE visualization with and without the domain-specific uniformity loss L domain . The values in parentheses in each legend label indicate the uniformity of the corresponding domain.", "num": null}, "FIGREF5": {"fig_num": "5", "uris": null, "type_str": "figure", "text": "Figure 5: Analysis of the weighting strategy. (a) Quantitative comparison of mAP (%) across varying augmentation probabilities, with and without the weighting strategy, on MS+CS+C3 → M under Protocol-3. The weighting strategy consistently improves performance, especially at higher augmentation probabilities, where the mAP drops significantly without it. (b) Qualitative analysis of the weight score w for different pairs of original and augmented images.", "num": null}, "FIGREF6": {"fig_num": "6", "uris": null, "type_str": "figure", "text": "Figure 6: Analysis on polarized effect across different types of (a) backbones, (b) loss functions, and (c) augmentations. The experimental configurations are the same in Fig. 1 of the main paper.", "num": null}, "FIGREF7": {"fig_num": "7", "uris": null, "type_str": "figure", "text": "Figure 7: Analysis on polarized effect of (a) Random Erasing and (b) RandAugment across augmentation probabilities. The experimental configurations are the same in Fig. 1 of the main paper.", "num": null}, "FIGREF9": {"fig_num": "8", "uris": null, "type_str": "figure", "text": "Figure 8: Parameter analysis of k and λ on MS+C3+CS → M under Protocol-3. (a) mAP/Rank-1 (%) with varying k-reciprocal nearest neighbors for the weighting strategy. (b) mAP/Rank-1 (%) with varying the weighting parameter λ for the alignment loss.", "num": null}, "TABREF0": {"html": null, "content": "<table><tr><td>Dataset</td><td>#ID</td><td colspan=\"2\">#Image #Camera</td></tr><tr><td>Market1501 (M) [95]</td><td>1,501</td><td>32,217</td><td>6</td></tr><tr><td>MSMT17 (MS) [83]</td><td>4,101</td><td>126,441</td><td>15</td></tr><tr><td>CUHK02 (C2) [43]</td><td>1,816</td><td>7,264</td><td>10</td></tr><tr><td>CUHK03 (C3) [44]</td><td>1,467</td><td>14,096</td><td>2</td></tr><tr><td colspan=\"2\">CUHK-SYSU (CS) [85] 11,934</td><td>34,574</td><td>1</td></tr><tr><td>PRID [31]</td><td>200</td><td>1,134</td><td>2</td></tr><tr><td>GRID [55]</td><td>250</td><td>1,275</td><td>8</td></tr><tr><td>VIPeR [26]</td><td>632</td><td>1,264</td><td>2</td></tr><tr><td>iLIDs [97]</td><td>119</td><td>476</td><td>2</td></tr></table>", "type_str": "table", "text": "Statistics of the used datasets.", "num": null}, "TABREF1": {"html": null, "content": "<table><tr><td>Setting</td><td>Training Data</td><td>Testing Data</td></tr><tr><td colspan=\"2\">Protocol-1 Full-(M+C2+C3+CS)</td><td>PRID, GRID, VIPeR, iLIDs</td></tr><tr><td>Protocol-2</td><td/><td/></tr></table>", "type_str": "table", "text": "Evaluation protocols.", "num": null}, "TABREF2": {"html": null, "content": "<table><tr><td colspan=\"2\">Source Method</td><td>mAP</td><td>PRID Rank-1</td><td colspan=\"2\">GRID mAP Rank-1</td><td colspan=\"2\">VIPeR mAP Rank-1</td><td>mAP</td><td>iLIDs Rank-1</td><td colspan=\"2\">Average mAP Rank-1</td></tr><tr><td/><td>DIMN [71]</td><td>52.0</td><td>39.2</td><td>41.1</td><td>29.3</td><td>60.1</td><td>51.2</td><td>78.4</td><td>70.2</td><td>57.9</td><td>47.5</td></tr><tr><td/><td>DualNorm [34]</td><td>64.9</td><td>60.4</td><td>45.7</td><td>41.4</td><td>58.0</td><td>53.9</td><td>78.5</td><td>74.8</td><td>61.8</td><td>57.6</td></tr><tr><td/><td>SNR [36]</td><td>66.5</td><td>52.1</td><td>47.7</td><td>40.2</td><td>61.3</td><td>52.9</td><td>89.9</td><td>84.1</td><td>66.4</td><td>57.3</td></tr><tr><td>M+D</td><td>DDAN [6]</td><td>67.5</td><td>62.9</td><td>50.9</td><td>46.2</td><td>60.8</td><td>56.5</td><td>81.2</td><td>78.0</td><td>65.1</td><td>60.9</td></tr><tr><td>+C2+C3</td><td>RaMoE [12]</td><td>67.3</td><td>57.7</td><td>54.2</td><td>46.8</td><td>64.6</td><td>56.6</td><td>90.2</td><td>85.0</td><td>62.0</td><td>61.5</td></tr><tr><td>+CS</td><td>DMG-Net [4]</td><td>68.4</td><td>60.6</td><td>56.6</td><td>51.0</td><td>60.4</td><td>53.9</td><td>83.9</td><td>79.3</td><td>67.3</td><td>61.2</td></tr><tr><td/><td>GDNorm [53]</td><td>79.9</td><td>72.6</td><td>63.8</td><td>55.4</td><td>74.1</td><td>66.1</td><td>87.2</td><td>81.3</td><td>76.3</td><td>68.9</td></tr><tr><td/><td>DTIN [35]</td><td>79.7</td><td>71.0</td><td>60.6</td><td>51.8</td><td>70.7</td><td>62.9</td><td>87.2</td><td>81.8</td><td>74.6</td><td>66.9</td></tr><tr><td/><td>StyCon [48]</td><td>78.9</td><td>71.0.</td><td>60.4</td><td>50.7</td><td>74.4</td><td>66.8</td><td>86.9</td><td>80.7</td><td>75.2</td><td>67.3</td></tr><tr><td/><td>QAConv 50 [50]</td><td>62.2</td><td>52.3</td><td>57.4</td><td>48.6</td><td>66.3</td><td>57.0</td><td>81.9</td><td>75.0</td><td>67.0</td><td>58.2</td></tr><tr><td/><td>M 3 L [94]</td><td>65.3</td><td>55.0</td><td>50.5</td><td>40.0</td><td>68.2</td><td>60.8</td><td>74.3</td><td>65.0</td><td>64.6</td><td>55.2</td></tr><tr><td>M +C2+C3 +CS</td><td>MetaBIN [10] META [86] ACL [90] StyCon [48]</td><td>70.8 71.7 73.4 78.1</td><td>61.2 61.9 63.0 69.7</td><td>57.9 60.1 65.7 62.1</td><td>50.2 52.4 55.2 53.4</td><td>64.3 68.4 75.1 71.2</td><td>55.9 61.5 66.4 62.8</td><td>82.7 83.5 86.5 84.8</td><td>74.7 79.2 81.8 78.0</td><td>68.9 70.9 75.2 74.1</td><td>60.5 63.8 66.6 66.0</td></tr><tr><td/><td>BAU (Ours)</td><td>77.2</td><td>68.4</td><td>68.1</td><td>59.8</td><td>74.6</td><td>66.1</td><td>88.7</td><td>83.7</td><td>77.2</td><td>69.5</td></tr></table>", "type_str": "table", "text": "Comparison with state-of-the-art methods on Protocol-1. Since DukeMTMC-reID[98], denoted as D in the table, has been withdrawn, it is not utilized for our training.", "num": null}, "TABREF3": {"html": null, "content": "<table><tr><td>Setting</td><td>Method</td><td colspan=\"2\">M+MS+CS → C3 mAP Rank-1</td><td colspan=\"2\">M+CS+C3 → MS mAP Rank-1</td><td colspan=\"2\">MS+CS+C3 → M mAP Rank-1</td><td colspan=\"2\">Average mAP Rank-1</td></tr><tr><td/><td>SNR [36]</td><td>8.9</td><td>8.9</td><td>6.8</td><td>19.9</td><td>34.6</td><td>62.7</td><td>16.8</td><td>30.5</td></tr><tr><td/><td>QAConv 50 [50]</td><td>25.4</td><td>24.8</td><td>16.4</td><td>45.3</td><td>63.1</td><td>83.7</td><td>35.0</td><td>51.3</td></tr><tr><td/><td>M 3 L [94]</td><td>34.2</td><td>34.4</td><td>16.7</td><td>37.5</td><td>61.5</td><td>82.3</td><td>37.5</td><td>51.4</td></tr><tr><td>Protocol-2</td><td>MetaBIN [10]</td><td>28.8</td><td>28.1</td><td>17.8</td><td>40.2</td><td>57.9</td><td>80.1</td><td>34.8</td><td>49.5</td></tr><tr><td/><td>META [86]</td><td>36.3</td><td>35.1</td><td>22.5</td><td>49.9</td><td>67.5</td><td>86.1</td><td>42.1</td><td>57.0</td></tr><tr><td/><td>ACL [90]</td><td>41.2</td><td>41.8</td><td>20.4</td><td>45.9</td><td>74.3</td><td>89.3</td><td>45.3</td><td>59.0</td></tr><tr><td/><td>BAU (Ours)</td><td>42.8</td><td>43.9</td><td>24.3</td><td>50.9</td><td>77.1</td><td>90.4</td><td>48.1</td><td>61.7</td></tr><tr><td/><td>SNR [36]</td><td>17.5</td><td>17.1</td><td>7.7</td><td>22.0</td><td>52.4</td><td>77.8</td><td>25.9</td><td>39.0</td></tr><tr><td/><td>QAConv 50 [50]</td><td>32.9</td><td>33.3</td><td>17.6</td><td>46.6</td><td>66.5</td><td>85.0</td><td>39.0</td><td>55.0</td></tr><tr><td/><td>M 3 L [94]</td><td>35.7</td><td>36.5</td><td>17.4</td><td>38.6</td><td>62.4</td><td>82.7</td><td>38.5</td><td>52.6</td></tr><tr><td>Protocol-3</td><td>MetaBIN [10] META [86]</td><td>43.0 47.1</td><td>43.1 46.2</td><td>18.8 24.4</td><td>41.2 52.1</td><td>67.2 76.5</td><td>84.5 90.5</td><td>43.0 49.3</td><td>56.3 62.9</td></tr><tr><td/><td>ACL [90]</td><td>49.4</td><td>50.1</td><td>21.7</td><td>47.3</td><td>76.8</td><td>90.6</td><td>49.3</td><td>62.7</td></tr><tr><td/><td>BAU (Ours)</td><td>50.6</td><td>51.8</td><td>26.8</td><td>54.3</td><td>79.5</td><td>91.1</td><td>52.3</td><td>65.7</td></tr></table>", "type_str": "table", "text": "Comparison with state-of-the-art methods on Protocol-2 and Protocol-3.", "num": null}, "TABREF4": {"html": null, "content": "<table><tr><td>Method</td><td colspan=\"6\">M+MS+CS → C3 M+CS+C3 → MS MS+CS+C3 → M mAP Rank-1 mAP Rank-1 mAP Rank-1</td><td colspan=\"2\">Average mAP Rank-1</td></tr><tr><td>Baseline (w/o augmented images)</td><td>39.2</td><td>38.9</td><td>18.9</td><td>44.8</td><td>68.3</td><td>86.3</td><td>42.1</td><td>56.7</td></tr><tr><td>Lce</td><td>32.1</td><td>31.9</td><td>18.0</td><td>41.7</td><td>63.1</td><td>83.8</td><td>37.7</td><td>52.5</td></tr><tr><td>Lalign</td><td>41.8</td><td>42.2</td><td>23.1</td><td>47.4</td><td>73.8</td><td>87.4</td><td>46.2</td><td>59.0</td></tr><tr><td>Lalign + Luniform</td><td>46.9</td><td>47.4</td><td>25.3</td><td>51.1</td><td>78.1</td><td>89.5</td><td>50.1</td><td>62.7</td></tr><tr><td>Lalign + Luniform + Ldomain</td><td>50.6</td><td>51.8</td><td>26.8</td><td>54.3</td><td>79.5</td><td>91.1</td><td>52.3</td><td>65.7</td></tr></table>", "type_str": "table", "text": "Ablation study of loss functions for augmented images.", "num": null}, "TABREF5": {"html": null, "content": "<table><tr><td>Weighting</td><td>Domain-specific</td><td colspan=\"6\">M+MS+CS → C3 M+CS+C3 → MS MS+CS+C3 → M</td><td colspan=\"2\">Average</td></tr><tr><td>Strategy of Lalign</td><td>Prototype of Ldomain</td><td>mAP</td><td>Rank-1</td><td>mAP</td><td>Rank-1</td><td>mAP</td><td>Rank-1</td><td colspan=\"2\">mAP Rank-1</td></tr><tr><td/><td/><td>46.2</td><td>46.6</td><td>23.9</td><td>49.3</td><td>76.7</td><td>90.0</td><td>48.9</td><td>62.0</td></tr><tr><td>✓</td><td/><td>47.1</td><td>47.4</td><td>24.7</td><td>50.9</td><td>78.3</td><td>88.9</td><td>50.0</td><td>62.4</td></tr><tr><td/><td>✓</td><td>49.5</td><td>51.0</td><td>25.1</td><td>51.6</td><td>78.6</td><td>90.8</td><td>51.1</td><td>64.5</td></tr><tr><td>✓</td><td>✓</td><td>50.6</td><td>51.8</td><td>26.8</td><td>54.3</td><td>79.5</td><td>91.1</td><td>52.3</td><td>65.7</td></tr></table>", "type_str": "table", "text": "Ablation study of the weighting strategy and the domain-specific uniformity loss. taken down. As shown in the table, although our method utilizes fewer datasets for training, it surpasses prior state-of-the-art methods in average mAP/Rank-1 performance across the four datasets. Comparisons of the proposed method with state-of-the-art methods on Protocol-2 and Protocol-3 are presented in Table", "num": null}, "TABREF7": {"html": null, "content": "<table><tr><td>Method</td><td colspan=\"6\">M+MS+CS → C3 M+CS+C3 → MS MS+CS+C3 → M mAP Rank-1 mAP Rank-1 mAP Rank-1</td><td colspan=\"2\">Average mAP Rank-1</td></tr><tr><td colspan=\"2\">BAU with other backbones</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>MobileNetV2</td><td>21.4</td><td>20.2</td><td>10.1</td><td>24.3</td><td>46.9</td><td>69.4</td><td>26.1</td><td>38.0</td></tr><tr><td>+ BAU (Ours)</td><td>27.5</td><td>27.4</td><td>12.9</td><td>31.3</td><td>59.1</td><td>78.5</td><td>33.2</td><td>45.7</td></tr><tr><td>ViT-B/16</td><td>31.8</td><td>30.9</td><td>14.8</td><td>29.2</td><td>52.9</td><td>74.6</td><td>33.2</td><td>44.9</td></tr><tr><td>+ BAU (Ours)</td><td>37.3</td><td>36.9</td><td>20.0</td><td>40.5</td><td>63.5</td><td>80.6</td><td>40.3</td><td>52.7</td></tr><tr><td colspan=\"3\">BAU with other loss functions</td><td/><td/><td/><td/><td/><td/></tr><tr><td>ArcFace</td><td>33.8</td><td>33.9</td><td>17.6</td><td>37.6</td><td>58.9</td><td>79.5</td><td>36.8</td><td>50.3</td></tr><tr><td>+ BAU (Ours)</td><td>37.8</td><td>38.3</td><td>23.5</td><td>50.8</td><td>72.5</td><td>88.7</td><td>44.6</td><td>59.3</td></tr><tr><td>PCL</td><td>34.6</td><td>35.5</td><td>16.4</td><td>33.7</td><td>63.5</td><td>83.4</td><td>38.2</td><td>50.9</td></tr><tr><td>+ BAU (Ours)</td><td>39.7</td><td>40.9</td><td>21.8</td><td>47.0</td><td>74.3</td><td>88.4</td><td>45.3</td><td>58.8</td></tr></table>", "type_str": "table", "text": "Evaluation of BAU with other backbones and loss functions on Protocol-2.", "num": null}, "TABREF8": {"html": null, "content": "<table><tr><td colspan=\"3\">Random Erasing RandAugment Color Jitter</td><td colspan=\"6\">M+MS+CS → C3 M+CS+C3 → MS MS+CS+C3 → M mAP Rank-1 mAP Rank-1 mAP Rank-1</td><td colspan=\"2\">Average mAP Rank-1</td></tr><tr><td>-</td><td>-</td><td>-</td><td>43.1</td><td>42.8</td><td>23.1</td><td>47.2</td><td>72.0</td><td>87.1</td><td>46.1</td><td>59.0</td></tr><tr><td>✓</td><td>-</td><td>-</td><td>44.3</td><td>45.5</td><td>23.3</td><td>47.4</td><td>74.1</td><td>88.8</td><td>47.2</td><td>60.6</td></tr><tr><td>-</td><td>✓</td><td>-</td><td>47.3</td><td>47.1</td><td>24.4</td><td>51.3</td><td>77.1</td><td>89.9</td><td>49.6</td><td>62.8</td></tr><tr><td>-</td><td>-</td><td>✓</td><td>47.2</td><td>47.6</td><td>25.0</td><td>50.9</td><td>78.0</td><td>90.5</td><td>50.1</td><td>63.0</td></tr><tr><td>✓</td><td>✓</td><td>-</td><td>48.1</td><td>47.7</td><td>25.3</td><td>51.7</td><td>78.7</td><td>90.1</td><td>50.7</td><td>63.2</td></tr><tr><td>✓</td><td>-</td><td>✓</td><td>47.9</td><td>48.5</td><td>25.9</td><td>51.9</td><td>78.4</td><td>90.6</td><td>50.7</td><td>63.7</td></tr><tr><td>-</td><td>✓</td><td>✓</td><td>49.4</td><td>50.2</td><td>26.3</td><td>53.3</td><td>79.0</td><td>90.4</td><td>51.6</td><td>64.6</td></tr><tr><td>✓</td><td>✓</td><td>✓</td><td>50.6</td><td>51.8</td><td>26.8</td><td>54.3</td><td>79.5</td><td>91.1</td><td>52.3</td><td>65.7</td></tr></table>", "type_str": "table", "text": "Ablation study of data augmentations on Protocol-3.", "num": null}, "TABREF9": {"html": null, "content": "<table><tr><td>p</td><td colspan=\"6\">M+MS+CS → C3 M+CS+C3 → MS MS+CS+C3 → M mAP Rank-1 mAP Rank-1 mAP Rank-1</td><td colspan=\"2\">Average mAP Rank-1</td></tr><tr><td>0.0</td><td>43.1</td><td>42.8</td><td>23.1</td><td>47.2</td><td>72.0</td><td>87.1</td><td>46.1</td><td>59.0</td></tr><tr><td>0.25</td><td>48.2</td><td>48.6</td><td>27.0</td><td>55.0</td><td>79.1</td><td>90.9</td><td>51.4</td><td>64.8</td></tr><tr><td>0.5</td><td>50.6</td><td>51.8</td><td>26.8</td><td>54.3</td><td>79.5</td><td>91.1</td><td>52.3</td><td>65.7</td></tr><tr><td>0.75</td><td>49.6</td><td>50.1</td><td>25.1</td><td>51.5</td><td>80.1</td><td>91.8</td><td>51.6</td><td>64.5</td></tr><tr><td>1.0</td><td>47.9</td><td>47.6</td><td>24.0</td><td>49.1</td><td>76.1</td><td>89.1</td><td>49.3</td><td>61.9</td></tr></table>", "type_str": "table", "text": "Ablation study of applying probabilities of data augmentations.", "num": null}, "TABREF11": {"html": null, "content": "<table><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: Code is available at GitHub.</td></tr><tr><td>Guidelines:</td></tr><tr><td>• results?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: The paper include the training and test details in the experiment section and</td></tr><tr><td>the appendix.</td></tr><tr><td>Guidelines:</td></tr><tr><td>• The answer NA means that the paper does not include experiments.</td></tr><tr><td>• The experimental setting should be presented in the core of the paper to a level of detail</td></tr><tr><td>that is necessary to appreciate the results and make sense of them.</td></tr><tr><td>• The full details can be provided either with the code, in appendix, or as supplemental</td></tr><tr><td>material.</td></tr><tr><td>7. Experiment Statistical Significance</td></tr><tr><td>Question: Does the paper report error bars suitably and correctly defined or other appropriate</td></tr><tr><td>information about the statistical significance of the experiments?</td></tr><tr><td>Answer: [No]</td></tr><tr><td>Justification: The paper includes comprehensive experimental results across multiple bench-</td></tr><tr><td>marks and protocols, which require significant training costs. Nonetheless, the paper reports</td></tr><tr><td>average performance across various datasets and random splits for each protocol.</td></tr><tr><td>Guidelines:</td></tr><tr><td>• The answer NA means that the paper does not include experiments.</td></tr><tr><td>• The authors should answer \"Yes\" if the results are accompanied by error bars, confi-</td></tr><tr><td>dence intervals, or statistical significance tests, at least for the experiments that support</td></tr><tr><td>the main claims of the paper.</td></tr><tr><td>•</td></tr></table>", "type_str": "table", "text": "The answer NA means that paper does not include experiments requiring code.• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable). • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted. 6. Experimental Setting/Details Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. 8. Experiments Compute Resources Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments? Answer: [Yes] Justification: The paper includes the information on the computer resources in the experiment section and the appendix. Guidelines: • The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper). 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines? Answer: [Yes] Justification: The paper confirms and agrees the NeurIPS Code of Ethics. The paper includes visualization results with blurred images for anonymization. Guidelines: • The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "num": null}}}}