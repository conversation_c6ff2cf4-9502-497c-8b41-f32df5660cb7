{"paper_id": "SEG-SDXL", "title": "Smoothed Energy Guidance: Guiding Diffusion Models with Reduced Energy Curvature of Attention", "abstract": "Conditional diffusion models have shown remarkable success in visual content generation, producing high-quality samples across various domains, largely due to classifier-free guidance (CFG). Recent attempts to extend guidance to unconditional models have relied on heuristic techniques, resulting in suboptimal generation quality and unintended effects. In this work, we propose Smoothed Energy Guidance (SEG), a novel training-and condition-free approach that leverages the energybased perspective of the self-attention mechanism to enhance image generation. By defining the energy of self-attention, we introduce a method to reduce the curvature of the energy landscape of attention and use the output as the unconditional prediction. Practically, we control the curvature of the energy landscape by adjusting the Gaussian kernel parameter while keeping the guidance scale parameter fixed. Additionally, we present a query blurring method that is equivalent to blurring the entire attention weights without incurring quadratic complexity in the number of tokens. In our experiments, SEG achieves a Pareto improvement in both quality and the reduction of side effects. The code is available at https://github.com/SusungHong/SEG-SDXL.", "pdf_parse": {"paper_id": "SEG-SDXL", "abstract": [{"text": "Conditional diffusion models have shown remarkable success in visual content generation, producing high-quality samples across various domains, largely due to classifier-free guidance (CFG). Recent attempts to extend guidance to unconditional models have relied on heuristic techniques, resulting in suboptimal generation quality and unintended effects. In this work, we propose Smoothed Energy Guidance (SEG), a novel training-and condition-free approach that leverages the energybased perspective of the self-attention mechanism to enhance image generation. By defining the energy of self-attention, we introduce a method to reduce the curvature of the energy landscape of attention and use the output as the unconditional prediction. Practically, we control the curvature of the energy landscape by adjusting the Gaussian kernel parameter while keeping the guidance scale parameter fixed. Additionally, we present a query blurring method that is equivalent to blurring the entire attention weights without incurring quadratic complexity in the number of tokens. In our experiments, SEG achieves a Pareto improvement in both quality and the reduction of side effects. The code is available at https://github.com/SusungHong/SEG-SDXL.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Diffusion models [12, 45, 46] have emerged as a promising tool for visual content generation, producing high-quality and diverse samples across various domains, including image [38, 40, 42, 8, 13, 30, 2, 24, 9, 29, 34, 33, 4, 41, 5, 20, 22] , video [11, 50, 23, 18, 15, 3, 19, 44] , and 3D generation [36, 27, 6, 26, 49, 43, 48, 16] . The success of these models can be largely attributed to the use of classifier-free guidance (CFG) [14] , which enables sampling from a sharper distribution, resulting in improved sample quality. However, CFG is not applicable to unconditional image generation, where no specific conditions are provided, creating a disparity between the capabilities of text-conditioned sampling and sampling without text. This disparity results in a restriction in application, e.g., synthesizing images with ControlNet [51] without a text prompt (see the last two columns of Fig. 1 ).", "section": "Introduction", "sec_num": "1"}, {"text": "Recent literature [17, 1] has attempted to decouple CFG and image quality by extending guidance to general diffusion models, leveraging their inherent representations [25, 32, 17] . Self-attention guidance (SAG) [17] proposes leveraging the intermediate self-attention map of diffusion models to blur the input pixels and provide guidance, while perturbed attention guidance (PAG) [1] perturbs the attention map itself by replacing it with an identity attention map. Despite these efforts, these methods rely on heuristics to make perturbed predictions, resulting in unintended effects such as smoothed-out details, saturation, color shifts, and significant changes in the image structure when given a large guidance scale. Notably, the mathematical underpinnings of these unconditional guidance approaches are not well elucidated. In this work, we approach the objective from an energy-based perspective of the self-attention mechanism, which has been previously explored based on its close connection to the Hopfield energy [39, 31, 7] . Specifically, we start from the definition of the energy of self-attention, where performing a self-attention operation is equivalent to taking a gradient step. In light of this, we propose a tuning-and condition-free method that reduces the curvature of the underlying energy function by directly blurring the attention weights, and then leverages the output as the negative prediction. We call this method Smoothed Energy Guidance (SEG).", "section": "Introduction", "sec_num": "1"}, {"text": "SEG does not merely rely on the guidance scale parameter that cause side effects when its value becomes large. Instead, we can continuously control the original and maximally attenuated curvature of the energy landscape behind the self-attention by simply adjusting the parameter of the Gaussian kernel, with the guidance scale parameter fixed. Additionally, we introduce a novel query blurring technique, which is equivalent to blurring the entire attention weights without incurring quadratic cost in the number of tokens.", "section": "Introduction", "sec_num": "1"}, {"text": "We validate the effectiveness of SEG throughout the various experiments without and with text conditions, and ControlNet [51] trained on canny and depth maps. Based on the attention modulation, SEG results in less structural change from the original prediction compared to previous approaches [17, 1] , while achieving better sample quality.", "section": "Introduction", "sec_num": "1"}, {"text": "Diffusion models [12, 45, 46] are a class of generative models that generate data through an iterative denoising process. The process of adding noise to an image x over time t ∈ [0, T ] is governed by the forward stochastic differential equation (SDE):", "section": "Diffusion models", "sec_num": "2.1"}, {"text": "EQUATION", "section": "Diffusion models", "sec_num": "2.1"}, {"text": "where f and g are predefined functions that determine the manner in which the noise is added, and dw denotes a standard Wiener process.", "section": "Diffusion models", "sec_num": "2.1"}, {"text": "Correspondingly, the denoising process can be described by the reverse SDE:", "section": "Diffusion models", "sec_num": "2.1"}, {"text": "EQUATION", "section": "Diffusion models", "sec_num": "2.1"}, {"text": "where ∇ x log p t (x) represents the score of the noisy data distribution and d w denotes the standard Wiener process for the reversed time.", "section": "Diffusion models", "sec_num": "2.1"}, {"text": "Diffusion models are trained to approximate the score function with s θ (x, t) ≈ ∇ x log p t (x). To generate an image based on a condition c, e.g., a class label or text, one simply needs to train diffusion models to approximate the conditional score function with s θ (x, t, c) ≈ ∇ x log p t (x|c) and replace ∇ x log p t (x) with it in the denoising process. To enhance the quality and faithfulness of the generated samples, classifier-free guidance (CFG) [14] is widely adopted. Accordingly, the reverse process becomes:", "section": "Diffusion models", "sec_num": "2.1"}, {"text": "EQUATION", "section": "Diffusion models", "sec_num": "2.1"}, {"text": "Here, s θ (x, t) is learned by dropping the label by a certain proportion, and γ cfg is a hyperparameter that controls the strength of the guidance. Intuitively, CFG helps us to sample from sharper distribution by conditioning on a class label or text.", "section": "Diffusion models", "sec_num": "2.1"}, {"text": "The attention mechanism [47] , which has been widely adopted in diffusion models [12] , has been interpreted through the lens of energy-based models (EBMs) [31, 39, 7] , especially through its close connection with the Hopfield energy [7, 39] . In the modern (continuous) Hopfield network, the attention operation can be derived based on the concave-convex procedure (CCCP) from the following energy function [39] :", "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "EQUATION", "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "where ξ ∈ R 1×d , X ∈ R N ×d , and lse stands for the log-sum-exp function, defined as lse(v) := log N i=1 e vi . The quadratic term acts as a regularizer to prevent ξ from exploding [39] , while -lse(Xξ ⊤ ) penalizes misalignment between X and ξ.", "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "Mathematically, it turns out that the attention mechanism is equivalent to the update rule of the modern Hopfield network [7, 39] . Specifically, inspired by the Hopfield energy in (4), and noticing that the first term depends on the attention weights, we propose the following energy function for entire self-attention weights in diffusion models: Definition 2.1 (Energy Function for Self-Attention). Let Q ∈ R (HW )×d be a matrix of query vectors and K ∈ R (HW )×d be a matrix of key vectors, where H, W , and d represent the height, width, and dimension, respectively. Let A ∈ R (HW )×(HW ) := QK ⊤ . The energy function with respect to entire self-attention weights in diffusion models is defined as:", "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "EQUATION", "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "Note that to explicitly denote the spatial dimension, we use the subscript (x, y) to represent the index of a row or column of the matrices. Despite using the definition in (5) for the rest of the paper for simplicity, we additionally discuss the dual case, where we use the swapped indexing, in Appendix B.", "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "This view leads us to an important intuition: the attention operation can be seen as a minimization step on the energy landscape, considering that the first derivative represents the softmax operation which also appears in the attention operation. Building upon this intuition, we argue that Gaussian blurring on the attention weights modulates the underlying landscape to have less curvature, and we demonstrate this in the following sections by analyzing the second derivatives.", "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "Our aim is to theoretically derive the effect of Gaussian blur applied on the attention weights, which in the end attenuates the curvature of the underlying energy function. Then, utilizing this fact, we develop attention-based drop-in diffusion guidance that enhances the quality of the generated samples, regardless of whether an explicit condition is given. In Section 3.1, we claim some useful properties of Gaussian blur: that it preserves mean, reduces variance, and thus decreases the lse value. In Section 3.2, we find that the curvature of the energy landscape is attenuated by the attention blur operation, leading naturally to a blunter prediction for guidance. And finally, in Section 3.3, built upon this fact, we define Smoothed Energy Guidance (SEG) and propose the equivalent query blurring method, which can perform attention blurring while avoiding quadratic complexity in the number of tokens.", "section": "Method", "sec_num": "3"}, {"text": "In this section, we derive some important properties of the Gaussian blur with the aim of figuring out the variation of the energy landscape. To this end, we start from some mathematical underpinnings on applying Gaussian blur to attention weights.", "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "A 2D Gaussian filter is a convolution kernel that uses a 2D Gaussian function to assign weights to neighboring pixels. The 2D Gaussian function is defined as:", "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "G(x, y) = 1 2πσ 2 e -(x-µx ) 2 +(y-µy ) 2 2σ 2", "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "where µ x and µ y are the means in the x and y directions, and σ is the standard deviation. The 2D Gaussian filter possesses symmetry, i.e., G(x, y) = G(-x, -y), and normalization, i.e., G(x, y)dxdy = 1. In practice, we use a discretized version of the Gaussian filter with a finite kernel size depending on σ, normalized to sum to 1. Lemma 3.1. Spatially applying a 2D Gaussian blur to the attention weights a := Qk ⊤ preserves the average E i,j [a (i,j) ]. In addition, the variance monotonically decreases every time we apply the Gaussian blur.", "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "Proof sketch. Applying a 2D Gaussian filter to the attention weights a (i,j) yields the blurred values ã(i,j) :", "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "ã(i,j) = k m=-k k n=-k G(m, n) • a (i+m,j+n)", "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "where k is the filter size, G(m, n) is the Gaussian filter value at position (m, n), and a (i+m,j+n) is the attention weight at position (i + m, j + n). Since the Gaussian filter is symmetric and normalized, it can be shown that the mean of the blurred attention weights is equal to the mean of the original attention weights. Similarly, we can show that the variance monotonically decreases when we apply a 2D Gaussian filter. See Appendix A.1 for the complete proof.", "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "Note that this fact also implies that blurring with a Gaussian filter with a larger standard deviation causes a greater decrease in the variance of attention weights. This is because a Gaussian filter with a larger standard deviation can always be represented as a convolution of two filters with smaller standard deviations, due to the associativity of the convolution operation.", "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "Finally, we show that applying a 2D Gaussian blur to attention weights increases the lse value in (5), i.e., increases the energy in (5) . This provides a bit of intuition about the underlying energy landscape, yet it is more prominently utilized in the claims in the following sections. Lemma 3.2. Applying a 2D Gaussian blur to attention weights a := Qk ⊤ increases the lse term when we consider the second-order Taylor series approximation of the exponential function around the mean µ := E i,j [a (i,j) ]. Consequently, the maximum is achieved when the attention is uniform, i.e., a (i,j) = a (k,l) ∀i, j, k, l. This corresponds to the case when we apply the Gaussian blur with σ → ∞.", "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "Proof sketch. Applying the second-order Taylor series approximation around the mean µ, and using Proposition 3.1, we show that the second-order approximation of lse(a) is larger than or equal to that of lse(ã). Subsequently, we introduce Lagrange multipliers to find the maximum, which gives us the result, a (i,j) = a (k,l) ∀i, j, k, l. We leave the full proof in Appendix A.2.", "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "In this section, we demonstrate that applying a 2D Gaussian blur to the attention weights before the softmax operation results in computing the updated value with reduced curvature of the underlying energy function. To this end, we analyze the Gaussian curvature before and after blurring the attention weights. This is closely related to the Hessian of the energy function.", "section": "Analysis of the energy landscape", "sec_num": "3.2"}, {"text": "Theorem 3.1. Let the attention weights be defined as a := Qk ⊤ . Consider the energy function in (5) . Then, applying a Gaussian blur to the attention weights a before the softmax operation results in the attenuation of the Gaussian curvature of the underlying energy function where gradient descent is performed.", "section": "Analysis of the energy landscape", "sec_num": "3.2"}, {"text": "Proof sketch. Let H denote the Hessian of the original energy function, i.e., the derivative of the negative softmax, and H denote the Hessian of the new energy function associated with blurred attention weights. Furthermore, let b ij denote the i-th row, j-th column entry in the Toeplitz matrix B representing the Gaussian blur. Calculating the derivatives, we have the elements of the Hessians, To provide more intuition about what is actually happening and how we utilize this property in the later section, it is intriguing to consider the attenuating effect on the curvature in analogy to classifier-free guidance (CFG). CFG uses the difference between the prediction based on the sharper conditional distribution and the prediction based on the smoother unconditional distribution to guide the sampling process. By analogy, we propose a method to make the landscape of the energy function smoother to guide the sampling process, as opposed to the original (sharper) energy landscape.", "section": "Analysis of the energy landscape", "sec_num": "3.2"}, {"text": "h ij = (ξ(a) i -δ ij )ξ(a) j and hij = (ξ(ã) i -δ ij )ξ(ã) j b ij . Using Lemmas 3.", "section": "Analysis of the energy landscape", "sec_num": "3.2"}, {"text": "From a probabilistic perspective, the energy is associated with the likelihood of the attention weights in terms of the <PERSON><PERSON><PERSON> distribution conditioned on a given configuration, i.e., the feature map. Blurring the attention weights diminishes this likelihood as shown in Lemma 3.2, and also reduces the curvature of the distribution as shown in Theorem 3.1.", "section": "Analysis of the energy landscape", "sec_num": "3.2"}, {"text": "Based on the above observation that the Gaussian blur on attention weights attenuates the curvature of the energy function, we propose Smoothed Energy Guidance (SEG) in this section. For brevity, we redefine the unconditional score prediction as s θ (x, t), and the unconditional score prediction with the energy curvature reduced as sθ (x, t). Specifically, sθ (x, t) is the prediction with the attention weights blurred using a 2D Gaussian filter G with the standard deviation σ. We formulate the process as:", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "EQUATION", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "where * denotes the 2D convolution operator. Then, we replace the original attention weights with (QK ⊤ ) seg and compute the final value as in ordinary self-attention.", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "For practical purposes when the number of tokens is large, we propose an efficient computation of (6) using the property of a linear map, since the convolution operation is linear. Concretely, blurring queries is exactly the same as blurring the entire attention weights, and we propose the following proposition to justify our claim. Proposition 3.1. Let Q and K be the query and key matrices in self-attention, and let G be a 2D Gaussian filter. Blurring the attention weights with G is equivalent to blurring the query matrix Q with G and then computing the attention weights.", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "Proof. Since the convolution operation is linear, we can always find a Toeplitz matrix B such that:", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "EQUATION", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "where * denotes the 2D convolution operation. Using the properties of matrix multiplication, we can rewrite (7) as:", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "EQUATION", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "Finally, SEG is formulated as follows:", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "EQUATION", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "where γ seg denotes the guidance scale of SEG. In a straightforward manner, as SEG does not rely on external conditions, it can be used for conditional sampling strategies such as CFG [14] and ControlNet [51] . For the combinatorial sampling with CFG, following [17] , we simply extend (9) for improved conditional sampling with both SEG and CFG as follows:", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "EQUATION", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "which is an intuitive result, as the update rule moves x towards the conditional prediction while keeping it far from the prediction with blurred attention weights.", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "We are likely to get a result with saturation when using a large guidance scale, such as with classifierfree guidance (CFG) [14] , self-attention guidance (SAG) [17] , and perturbed attention guidance (PAG) [1] . This is a significant caveat since we need to increase the scale to achieve a maximum effect with these methods. Contrary to this, we can fix the scale of SEG as justified in Sec. 5.5 and control its maximum effect through σ of the Gaussian blur, making the choice more flexible. For σ, two extreme cases are recognized. If σ → 0, the blurred attention weights remain the same as the original, while when σ → ∞, the attention weights merely adopt a single mean value across spatial axes. We find that even the latter extreme case results in a high-quality outcome, corroborating that we can control the quality to the limit without saturation.", "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "Classifier-free guidance (CFG) [14] , first proposed as a replacement for classifier guidance (CG) [8] is controlled by a scale parameter. The higher we set classifier-free guidance, the more we get faithful, high-quality images. However, it requires external labels, such as text [30] or class [8] labels, making it impossible to apply to unconditional diffusion models. Also, it requires specific traning procedure with label dropping and it is known that high CFG causes saturation [42] .", "section": "Discussion on related work", "sec_num": "4"}, {"text": "Figure 4 : Conditional generation using ControlNet [51] and SEG. Table 1 : Quantitative comparison of SEG with vanilla SDXL [35] , SAG [17] , and PAG [1] for unconditional generation.", "section": "Discussion on related work", "sec_num": "4"}, {"text": "Vanilla SDXL [35] Tackling the caveats of CFG, unconditional approaches such as self-attention guidance (SAG) [17] and perturbed attention guidance (PAG) [1] have been proposed. SAG selectively blurs images with the mask obtained from the attention map and guides the generation process given the prediction. This indirect approach causes saturation and noisy images when given a large guidance scale, leading to the selection of a guidance scale less than or equal to 1. PAG guides images using prediction with identity attention, where the attention map is an identity matrix. However, the reliance on heuristics to make perturbed predictions results in unintended side effects. As an example of the side effects of replacing the attention map with identity attention, PAG changes the visual structure and color distribution of an image, as evidenced in Figs. 5, 8, and 9.", "section": "Metric", "sec_num": null}, {"text": "Contrary to these, we control the effect of SEG through the standard deviation of the Gaussian filter, σ. Moreover, while being theory-inspired, SEG is relatively free from unintended effects. In the following section, we corroborate our claim with extensive experiments.", "section": "Metric", "sec_num": null}, {"text": "We build upon the current open-source state-of-the-art diffusion model, Stable Diffusion XL (SDXL) [35] , as our baseline, and do not change the configuration. To sample with SEG, we choose the same attention layers (mid-blocks) and guidance scale as PAG [1] . For SEG and PAG sampling, we use the Euler discrete scheduler [21] , while for SAG [17] , we instead use the DDIM scheduler [45] since the current implementation of SAG does not support the Euler discrete sampler.", "section": "Implementation details", "sec_num": "5.1"}, {"text": "For SAG and PAG, we use the same configurations they used in the experiments with the previous version of Stable Diffusion, with guidance scales of 1.0 and 3.0, respectively. We set γ seg to 3.0, except in the ablation study. ", "section": "Implementation details", "sec_num": "5.1"}, {"text": "We use various metrics to evaluate quality (FID [10] and CLIP score [37] , calculated with 30k references from the MS-COCO 2014 validation set [28] ) and to assess the extent of change due to applied guidance (LPIPS vgg, alex [52] ). The latter metric, calculated using the outputs of vanilla SDXL, measures the extent of side effects by comparing guided images to their unguided counterparts.", "section": "Metrics", "sec_num": "5.2"}, {"text": "In this section, our aim is to demonstrate that with SEG, we can sample plausible images using vanilla SDXL [35] under various conditions and even without any conditions, as demonstrated in Fig. 1 . Furthermore, without the risk of saturation, we can control the quality and plausibility of the samples.", "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "For the results, we use σ ∈ {1, 2, 5, 10}. Additionally, as mentioned in Sec. 3.3, we present two extreme cases, σ → 0 (vanilla SDXL) and σ → ∞ (uniform queries).", "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "Unconditional generation In this section, our aim is to demonstrate that with SEG, we can sample plausible images from the unconditional mode of the vanilla SDXL, which was originally trained on a large-scale text-to-image dataset. The results are presented in Fig. 1 , Fig. 2 , and Table 1 . The results show a clear tendency to draw higher quality samples by utilizing the differences between the two energy landscapes with different curvatures derived from self-attention mechanisms.", "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "In Fig. 2 and Fig. 13 , we show the effectiveness of generating more plausible images, while vanilla SDXL is unable to generate high-quality images without any conditions. The results show a clear tendency to draw higher quality samples by utilizing the differences between the two energy landscapes with different curvatures derived from self-attention mechanisms. When σ is larger, the definition and expression of the samples improve, as the difference in curvature becomes more pronounced.", "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "Conditional generation In Figs. 3, 4, 10, 11 , and 14, we display sampling results conditioned on text, Canny, and depth map. Using text (Fig. 3 ), the vanilla SDXL without CFG is unable to generate high-quality images and produces noisy results. Canny and depth map conditioning on SDXL (Fig. 4 , 10, and 11) is achieved through ControlNet [51] , trained on such maps. The results show that SEG enhances the quality and fidelity of the generated images while preserving the textual and structural information provided by the conditioning inputs. Notably, as σ increases, the generated images exhibit improved definition and quality without introducing significant artifacts or deviations from the original condition. The combination with higher CFG scales is shown in Figs. 15 16 17 18 19 .", "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "In Table 2 , we show the quantitative results for text-conditional generation in terms of σ. We observe a clear trade-off between image quality (represented by FID and CLIP score) and the deviation from the original sample (represented by LPIPS). We sample 30k images for each σ to compute the metrics.", "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "Since the results are visually favorable when we use σ = 10 and σ → ∞, and they are the best in terms of CLIP score and FID, respectively, we adopt those configurations for comparison of unconditional guidance methods. The results are presented in Figs. 5, 8, 9, and Table 1 . Notably, our method achieves better image quality in terms of FID, while remaining similar to the original output of vanilla SDXL as measured by LPIPS, implying a Pareto improvement. In this section, we address two parameters, γ seg and σ, and justify that fixing γ seg is a reasonable choice. In Fig. 6 , we present the results from our testing. The results reveal that increasing γ seg does not generally lead to improved sample quality in terms of FID and CLIP score, due to various issues such as saturation. In contrast, increasing σ tends to improve sample quality and plausibility. This supports the claim that image quality should be controlled by σ, instead of the guidance scale parameter. We sample 30k images for each combination to calculate the metrics.", "section": "Comparison with previous methods", "sec_num": "5.4"}, {"text": "We introduce Smoothed Energy Guidance (SEG), a novel training-and condition-free guidance method for image generation with diffusion models. The key advantages of SEG lie in its flexibility and the theoretical foundation, allowing us to significantly enhance sample quality without side effects by adjusting the standard deviation of the Gaussian filter. We hope our method inspires further research on improving generative models, and extending the approach beyond image generation, for example, to video or natural language processing.", "section": "Conclusion, limitations and societal impacts Conclusion", "sec_num": "6"}, {"text": "The paper proposes guidance to enhance quality outcomes. Consequently, the attainable quality of our approach is contingent upon the baseline model employed. Furthermore, the application of SEG to temporal attention mechanisms in video or multi-view diffusion models is not addressed, remaining a promising avenue for future research. It is important to note that the improvements achieved through this method may potentially lead to unintended negative societal consequences by inadvertently amplifying existing stereotypes or harmful biases.", "section": "Limitations and societal impacts", "sec_num": null}, {"text": "A.1 Proof of Lemma 3.1 Let a (i,j) denote the original attention weights and ã(i,j) denote the blurred attention weights, as in the main paper. Assume that the original attention weights are properly padded to maintain consistent statistics. Then, the following shows that the mean of the blurred attention weights remains the same.", "section": "A Full proofs", "sec_num": null}, {"text": "E i,j [ã (i,j) ] = 1 HW H i=1 W j=1 ã(i,j) = 1 HW H i=1 W j=1 k m=-k k n=-k G(m, n) • a (i+m,j+n) = k m=-k k n=-k G(m, n) •   1 HW H i=1 W j=1 a (i+m,j+n)   = k m=-k k n=-k G(m, n) • E i,j [a (i,j) ] = E i,j [a (i,j) ] • k m=-k k n=-k G(m, n) = E i,j [a (i,j) ]", "section": "A Full proofs", "sec_num": null}, {"text": "In addition, the variance of the blurred attention weights is smaller than or equal to the variance of the original attention weights.", "section": "A Full proofs", "sec_num": null}, {"text": "Var i,j [ã (i,j) ] = 1 HW H i=1 W j=1 (ã (i,j) -E i,j [ã (i,j) ]) 2 = 1 HW H i=1 W j=1 k m=-k k n=-k G(m, n) • (a (i+m,j+n) -E i,j [a (i,j) ]) 2 = k m=-k k n=-k k r=-k k s=-k G(m, n) • G(r, s) • Cov[a (i+m,j+n) , a (i+r,j+s) ]", "section": "A Full proofs", "sec_num": null}, {"text": "Using the <PERSON><PERSON><PERSON><PERSON><PERSON> inequality and the normalization property of the 2D Gaussian filter, we can show that the variance monotonically decreases when we apply Gaussian blur.", "section": "A Full proofs", "sec_num": null}, {"text": "Var i,j [ã (i,j) ] ≤ k m=-k k n=-k k r=-k k s=-k G(m, n) • G(r, s) • Var[a (i+m,j+n) ] • Var[a (i+r,j+s) ] = k m=-k k n=-k G(m, n) • Var[a (i+m,j+n) ] 2 ≤ k m=-k k n=-k G(m, n) • k m=-k k n=-k G(m, n) • Var[a (i+m,j+n) ] = k m=-k k n=-k G(m, n) • Var[a (i+m,j+n) ] = Var i,j [a (i,j) ]", "section": "A Full proofs", "sec_num": null}, {"text": "A.2 Proof of Lemma 3.2", "section": "A Full proofs", "sec_num": null}, {"text": "Applying the second-order Taylor series approximation of e x to our function f around the mean µ, we get:", "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Full proofs", "sec_num": null}, {"text": "In the last step, we used the fact that", "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Full proofs", "sec_num": null}, {"text": "Since Var[a] > Var[ã], we have:", "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Full proofs", "sec_num": null}, {"text": "Therefore, the second-order approximation of lse(a) is larger than that of lse(ã).", "section": "A Full proofs", "sec_num": null}, {"text": "Note that this fact also implies blurring with a Gaussian filter with a bigger variance causes more decrease in the variance of attention weights, because Gaussian filter with a larger variance can always be represented as a convolution of two filters with smaller variances, and the convolution operation is associative.", "section": "A Full proofs", "sec_num": null}, {"text": "To find the maximum value subject to the constraint a (1,1) + a (1,2) + . . . + a (H,W ) = c for some constant c, we introduce Lagrange multipliers. Let g(a (1,1) , a (1,2) , . . . , a (H,W ) ) = a (1,1) + a (1,2) + . . . + a (H,W ) . The Lagrangian function is defined as:", "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Full proofs", "sec_num": null}, {"text": "Taking partial derivatives and setting them to zero yields:", "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Full proofs", "sec_num": null}, {"text": "Solving for a (i,j) , we obtain a (i,j) = ln(λ) for all i = 1, 2, . . . , H and j = 1, 2, . . . , W Summing these equations results in:", "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Full proofs", "sec_num": null}, {"text": "Substituting λ back into a (i,j) = ln(λ) gives a (1,1) = a (1,2) = . . . = a (H,W ) = c HW . Therefore, the minimum value of H i=1 W j=1 e a (i,j) is achieved when a (1,1) = a (1,2) = . . . = a (H,W ) .", "section": "A Full proofs", "sec_num": null}, {"text": "Let a = (a 1 , . . . , a n ) denote the attention values before the softmax operation, and let ã = (ã 1 , . . . , ãn ) denote the attention values after applying the 2D Gaussian blur. Let H denote the Hessian of the original energy, i.e., the derivative of the negative softmax, and H denote the Hessian of the underlying energy associated with the blurred weights.", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "The elements in the i-th row and j-th column of the Hessian matrices are given by:", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "respectively, where b ij are the elements of the <PERSON><PERSON>litz matrix corresponding to the Gaussian blur kernel, and δ ij denotes the Kronecker delta.", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "Assuming ξ(ã) i ξ(ã) j ≈ 0 and ξ(a) i ξ(a) j ≈ 0 for all i and j, which is a reasonable assumption when the number of token is large and the softmax values get small, the non-diagonal elements of the Hessians approximate to 0 and the diagonal elements dominate. Therefore, the determinants of the Hessian matrices are approximated as the product of the dominant terms:", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "We have the following inequality: ", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "n i=1 ξ(ã) i b ii < n i=1 ξ(ã) i = e n j=1 ãj ( n j=1 e ãj ) n", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "where the first inequality follows from the property of the Gaussian blur kernel, 0 ≤ b ii < 1, and the second inequality is derived from Lemmas 3.1 and 3.2, which demonstrate the mean-preserving property and the decrease in the lse value when applying a blur. The monotonicity of the logarithm function implies that the denominator involving the blurred attention weights is smaller. Eventually, we obtain the following inequality:", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": ")", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "This implies that the updated value is derived with attenuated curvature of the energy function underlying the blurred softmax operation compared to that of the original softmax operation.", "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "As we previously stated in Section 2.2, we have the dual definition regarding ( 5), where we use swapped indexing. Importantly, the swapped indices can be interpreted as altering the definition of attention weights to A := KQ ⊤ .", "section": "B Dual definition", "sec_num": null}, {"text": "A similar conclusion can be drawn as in the main paper, except that query blurring becomes key blurring with this definition. To see this, Eq. 7 changes slightly with this definition, using the symmetry of the Toeplitz matrix B:", "section": "B Dual definition", "sec_num": null}, {"text": "EQUATION", "section": "B Dual definition", "sec_num": null}, {"text": "where * denotes the 2D convolution operation. Empirically, this altered definition does not introduce a significant difference in the overall image quality, as shown in Fig. 12 .", "section": "B Dual definition", "sec_num": null}, {"text": "In this section, we present further qualitative results to demonstrate the effectiveness and versatility of our Smoothed Energy Guidance (SEG) method across various generation tasks and in comparison with other approaches.", "section": "C Additional qualitative results", "sec_num": null}, {"text": "Comparison with previous methods Figs. 8 and 9 provide a qualitative comparison of SEG against vanilla SDXL [35] , Self-Attention Guidance (SAG) [17] , and Perturbed Attention Guidance (PAG) [1] . These comparisons highlight the superior performance of SEG in terms of image quality, coherence, and adherence to the given prompts. SEG consistently produces sharper details, more realistic textures, and better overall composition compared to the other methods.", "section": "C Additional qualitative results", "sec_num": null}, {"text": "Conditional generation with ControlNet Figs. 10 and 11 showcase the application of SEG in conjunction with ControlNet [51] for conditional image generation. These results illustrate how SEG can enhance the quality and coherence of generated images while maintaining fidelity to the provided control signals. The images demonstrate improved detail, texture, and overall visual appeal compared to standard ControlNet outputs without prompts. Unconditional and text-conditional generation Fig. 13 demonstrates the capability of SEG in unconditional image generation, showcasing its ability to produce high-quality, diverse images without text prompts. Fig. 14 exhibits text-conditional generation results using SEG, illustrating its effectiveness in translating textual descriptions into visually appealing and accurate images.", "section": "C Additional qualitative results", "sec_num": null}, {"text": "Interaction with classifier-free guidance Figs. 15-19 present a series of experiments exploring the combination of SEG with CFG. In these experiments, the SEG guidance scale (γ seg ) is fixed at 3.0, while the CFG scale is varied. The results demonstrate that SEG consistently improves image quality across different CFG scales without causing saturation or significant changes in the general structure of the images.", "section": "C Additional qualitative results", "sec_num": null}, {"text": "Ablation study Fig. 20 displays a visual example of unconditional generation with controlled γ seg and σ. Consistent with results in Sec. 5.5, controlling image quality with σ has fewer side effects than controlling with γ seg .", "section": "C Additional qualitative results", "sec_num": null}, {"text": "The overall pipeline and conceptual framework of SEG are presented in Fig. 7 . Fig. 7 (a) and Fig. 7 (b) depict the original sampling process and the modified sampling process with smoothed energy, respectively. Fig. 7 (c) illustrates the the final prediction (the red arrow) with the guidance scale.", "section": "D Pipeline figure", "sec_num": null}, {"text": "Figure 8 : Qualitative comparison of SEG with vanilla SDXL [35] , SAG [17] , and PAG [1] .", "section": "D Pipeline figure", "sec_num": null}, {"text": "Figure 9 : Qualitative comparison of SEG with vanilla SDXL [35] , SAG [17] , and PAG [1] . Guidelines:", "section": "D Pipeline figure", "sec_num": null}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "section": "D Pipeline figure", "sec_num": null}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors? Answer: [Yes] Justification: The limitations are discussed the paper. Guidelines:", "section": "Limitations", "sec_num": "2."}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "section": "Limitations", "sec_num": "2."}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "section": "Limitations", "sec_num": "2."}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Justification: The paper discuss potential societal impacts. Guidelines:", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)? Answer: [NA] Justification: The paper proposes a guidance method for the current model; therefore, the paper itself poses no such risks. Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort. 12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected? Answer: [Yes] Justification: The creators or original owners of assets are properly credited and the license and terms of use are explicitly mentioned and properly respected. Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators. 13. New Assets", "section": "Safeguards", "sec_num": "11."}], "back_matter": [{"text": "I would like to express my gratitude to <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> for their valuable feedback and insights. Their thoughtful comments and suggestions have been instrumental in improving this work.", "section": "Acknowledgements", "sec_num": null}, {"text": "Justification: The paper provide the full set of assumptions and a complete proof. Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "section": "annex", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: The paper fully disclose all the information needed to reproduce the results. Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets? Answer: [NA] Justification: The paper does not release new assets. Guidelines:• The answer NA means that the paper does not release new assets.• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "section": "Open access to data and code", "sec_num": "5."}], "ref_entries": {"FIGREF0": {"uris": null, "fig_num": "1", "type_str": "figure", "text": "Figure 1: Teaser. (a) Images sampled from vanilla SDXL [35] without any guidance. (b) Images sampled with Smoothed Energy Guidance (Ours). ∅ denotes that there is no condition given. With various input conditions, and even without any, SEG supports the diffusion model in generating plausible and high-quality images without any training.", "num": null}, "FIGREF1": {"uris": null, "fig_num": null, "type_str": "figure", "text": "1 and 3.2 and under reasonable assumptions, we observe that | det(H)| > | det( H)|, which implies that the minimization step is performed on a smoother energy landscape with attenuated Gaussian curvature. The full proof is in Appendix A.3.", "num": null}, "FIGREF2": {"uris": null, "fig_num": "2", "type_str": "figure", "text": "Figure 2: Unconditional generation using SEG.", "num": null}, "FIGREF3": {"uris": null, "fig_num": "3", "type_str": "figure", "text": "Figure 3: Text-conditional generation using SEG.", "num": null}, "FIGREF4": {"uris": null, "fig_num": "5", "type_str": "figure", "text": "Figure 5: Qualitative comparison of SEG with vanilla SDXL[35], SAG[17], and PAG[1].", "num": null}, "FIGREF6": {"uris": null, "fig_num": "6", "type_str": "figure", "text": "Figure 6: Ablation study on γ seg and σ.", "num": null}, "FIGREF8": {"uris": null, "fig_num": "7", "type_str": "figure", "text": "Figure 7: Pipeline of SEG. (a) Original sampling process, self-attention weights, and the corresponding energy landscape. (b) Our modified sampling process with blurred queries where σ ∈ (0, ∞), inducing blurred attention weights and the corresponding smoothed energy landscape. (c) A conceptual figure of γ seg . Note that since the guidance linearly extrapolates predictions from (a) and (b), a high guidance scale causes samples to be out of the manifold.", "num": null}, "FIGREF9": {"uris": null, "fig_num": "10", "type_str": "figure", "text": "Figure 10: Conditional generation using ControlNet [51] and SEG.", "num": null}, "FIGREF10": {"uris": null, "fig_num": "11", "type_str": "figure", "text": "Figure 11: Conditional generation using ControlNet [51] and SEG.", "num": null}, "FIGREF11": {"uris": null, "fig_num": "12", "type_str": "figure", "text": "Figure 12: Comparison between query and key blur across different values of σ.", "num": null}, "FIGREF12": {"uris": null, "fig_num": "13", "type_str": "figure", "text": "Figure 13: Unconditional generation using SEG.", "num": null}, "FIGREF13": {"uris": null, "fig_num": "15", "type_str": "figure", "text": "Figure 15: Experiment on the combination of SEG and CFG. γ seg is fixed to 3.0. The prompt is \"a friendly robot helping an old lady cross the street.\" Without causing saturation or significant changes in the general structure, SEG improves the image quality.", "num": null}, "FIGREF14": {"uris": null, "fig_num": "16", "type_str": "figure", "text": "Figure 16: Experiment on the combination of SEG and CFG. γ seg is fixed to 3.0. The prompt is \"a skateboarding turtle zooming through a mini city made of Legos.\"", "num": null}, "FIGREF15": {"uris": null, "fig_num": "1718", "type_str": "figure", "text": "Figure 17: Experiment on the combination of SEG and CFG. γ seg is fixed to 3.0. The prompt is \"a group of puppies playing soccer with a ball of yarn.\"", "num": null}, "FIGREF16": {"uris": null, "fig_num": "1920", "type_str": "figure", "text": "Figure 19: Experiment on the combination of SEG and CFG. γ seg is fixed to 3.0. The prompt is \"a baby elephant learning to paint with its trunk in an art studio.\"", "num": null}, "TABREF1": {"type_str": "table", "content": "<table><tr><td>Metric</td><td>Vanilla SDXL [35]</td><td>1</td><td>2</td><td>SEG 5</td><td>10</td><td>∞</td></tr><tr><td>FID↓</td><td>53.423</td><td colspan=\"5\">48.284 41.784 33.819 29.325 26.169</td></tr><tr><td>CLIP Score↑</td><td>0.271</td><td>0.273</td><td>0.278</td><td>0.285</td><td>0.290</td><td>0.292</td></tr><tr><td>LPIPSvgg ↓</td><td>-</td><td>0.361</td><td>0.410</td><td>0.449</td><td>0.472</td><td>0.493</td></tr><tr><td>LPIPSalex ↓</td><td>-</td><td>0.295</td><td>0.347</td><td>0.390</td><td>0.416</td><td>0.440</td></tr></table>", "text": "Text-conditional sampling with different σ.", "html": null, "num": null}, "TABREF2": {"type_str": "table", "content": "<table><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: results? Answer: [Yes] Justification: The paper specify all the training and test details. Guidelines: • The answer NA means that the paper does not include experiments. • The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material. 7. Experiment Statistical Significance Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [Yes] Justification: The paper reports statistical information. Guidelines: • The answer NA means that the paper does not include experiments. • The authors should answer \"Yes\" if the results are accompanied by error bars, confi-dence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). Question: For each experiment, does the paper provide sufficient information on the com-puter resources (type of compute workers, memory, time of execution) needed to reproduce the experiments? Answer: [Yes] Justification: The paper provide sufficient information on the computer resources. Guidelines: • Answer: [Yes] Justification: The research conducted in the paper conform with the NeurIPS Code of Ethics. Guidelines: • The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics. • If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consid-eration due to laws or regulations in their jurisdiction). 10. Broader Impacts Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed? • 8. Experiments Compute Resources Answer: [Yes]</td></tr><tr><td>Answer: [Yes]</td></tr></table>", "text": "The paper provide open access to the data and code. Guidelines:• The answer NA means that paper does not include experiments requiring code.• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).• Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.6. Experimental Setting/DetailsQuestion: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper). 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?", "html": null, "num": null}}}}