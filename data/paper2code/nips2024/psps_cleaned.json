{"paper_id": "psps", "title": "Task-Agnostic Machine-Learning-Assisted Inference", "abstract": "Machine learning (ML) is playing an increasingly important role in scientific research. In conjunction with classical statistical approaches, ML-assisted analytical strategies have shown great promise in accelerating research findings. This has also opened a whole field of methodological research focusing on integrative approaches that leverage both ML and statistics to tackle data science challenges. One type of study that has quickly gained popularity employs ML to predict unobserved outcomes in massive samples, and then uses predicted outcomes in downstream statistical inference. However, existing methods designed to ensure the validity of this type of post-prediction inference are limited to very basic tasks such as linear regression analysis. This is because any extension of these approaches to new, more sophisticated statistical tasks requires task-specific algebraic derivations and software implementations, which ignores the massive library of existing software tools already developed for the same scientific problem given observed data. This severely constrains the scope of application for post-prediction inference. To address this challenge, we introduce a novel statistical framework named PSPS for task-agnostic ML-assisted inference. It provides a post-prediction inference solution that can be easily plugged into almost any established data analysis routines. It delivers valid and efficient inference that is robust to arbitrary choice of ML model, allowing nearly all existing statistical frameworks to be incorporated into the analysis of ML-predicted data. Through extensive experiments, we showcase our method's validity, versatility, and superiority compared to existing approaches. Our software is available at https://github.com/qlu-lab/psps.\nHistorically, the field of statistics has faced similar types of challenges. Before the advent of resampling-based methods [19] , it used to require task-specific derivation and implementation to obtain the variance of any new estimator. This old problem mirrors the current state of MLassisted inference, where every new task requires non-trivial effort from researchers. However, with resampling-based inference, the need to manually derive variance is reduced. Instead, resampling methods can be universally applied to many estimation problems and easily obtain variance [17] [18] [19] . Inspired by this, we seek a universal approach that incorporates ML-predicted data into any existing data analysis routines while ensuring valid inference results.\nWe introduce a simple protocol named PoSt-Prediction Summary-statistics-based (PSPS) inference (Fig. 1 ). It employs existing analysis routines to generate summary statistics sufficient for ML-assisted inference, and then produces valid and powerful inference results using these statistics. It has several key features:\n• Assumption-lean and data-adaptive: It inherits the theoretical guarantees of validity and efficiency from state-of-the-art ML-assisted inference methods [4, 20, 35] . These guarantees hold with arbitrary ML predictions. • Task-agnostic and simple: Since our method only requires summary statistics from existing analysis routines, it can be easily adapted for many statistical tasks currently unavailable or difficult to implement in ML-assisted inference. • Federated data analysis: It does not need any individual-level data as input. Sharing of privacy-preserving summary statistics is sufficient for real-world scientific collaboration.\n2 Problem formulations\nWe focus on statistical inference problems for the parameter θ * ≡ θ * (P) ∈ R K defined on the joint distribution of (X, Y ) ∼ P, where Y ∈ Y is a scalar outcome and X ∈ X be a K-dimensional vector representing features. We are interested in estimating θ * using labeled data\n, and a pre-trained ML model f (•) : X → Y. Here, f (•) is a black-box function with unknown operating characteristics and can be mis-specified. We also require an algorithm A that inputs the labeled data L and returns a consistent and asymptotically normally distributed estimator θ for θ * . There are three common ways in the literature to estimate θ * :", "pdf_parse": {"paper_id": "psps", "abstract": [{"text": "Machine learning (ML) is playing an increasingly important role in scientific research. In conjunction with classical statistical approaches, ML-assisted analytical strategies have shown great promise in accelerating research findings. This has also opened a whole field of methodological research focusing on integrative approaches that leverage both ML and statistics to tackle data science challenges. One type of study that has quickly gained popularity employs ML to predict unobserved outcomes in massive samples, and then uses predicted outcomes in downstream statistical inference. However, existing methods designed to ensure the validity of this type of post-prediction inference are limited to very basic tasks such as linear regression analysis. This is because any extension of these approaches to new, more sophisticated statistical tasks requires task-specific algebraic derivations and software implementations, which ignores the massive library of existing software tools already developed for the same scientific problem given observed data. This severely constrains the scope of application for post-prediction inference. To address this challenge, we introduce a novel statistical framework named PSPS for task-agnostic ML-assisted inference. It provides a post-prediction inference solution that can be easily plugged into almost any established data analysis routines. It delivers valid and efficient inference that is robust to arbitrary choice of ML model, allowing nearly all existing statistical frameworks to be incorporated into the analysis of ML-predicted data. Through extensive experiments, we showcase our method's validity, versatility, and superiority compared to existing approaches. Our software is available at https://github.com/qlu-lab/psps.", "section": "Abstract", "sec_num": null}, {"text": "Historically, the field of statistics has faced similar types of challenges. Before the advent of resampling-based methods [19] , it used to require task-specific derivation and implementation to obtain the variance of any new estimator. This old problem mirrors the current state of MLassisted inference, where every new task requires non-trivial effort from researchers. However, with resampling-based inference, the need to manually derive variance is reduced. Instead, resampling methods can be universally applied to many estimation problems and easily obtain variance [17] [18] [19] . Inspired by this, we seek a universal approach that incorporates ML-predicted data into any existing data analysis routines while ensuring valid inference results.", "section": "Abstract", "sec_num": null}, {"text": "We introduce a simple protocol named PoSt-Prediction Summary-statistics-based (PSPS) inference (Fig. 1 ). It employs existing analysis routines to generate summary statistics sufficient for ML-assisted inference, and then produces valid and powerful inference results using these statistics. It has several key features:", "section": "Abstract", "sec_num": null}, {"text": "• Assumption-lean and data-adaptive: It inherits the theoretical guarantees of validity and efficiency from state-of-the-art ML-assisted inference methods [4, 20, 35] . These guarantees hold with arbitrary ML predictions. • Task-agnostic and simple: Since our method only requires summary statistics from existing analysis routines, it can be easily adapted for many statistical tasks currently unavailable or difficult to implement in ML-assisted inference. • Federated data analysis: It does not need any individual-level data as input. Sharing of privacy-preserving summary statistics is sufficient for real-world scientific collaboration.", "section": "Abstract", "sec_num": null}, {"text": "2 Problem formulations", "section": "Abstract", "sec_num": null}, {"text": "We focus on statistical inference problems for the parameter θ * ≡ θ * (P) ∈ R K defined on the joint distribution of (X, Y ) ∼ P, where Y ∈ Y is a scalar outcome and X ∈ X be a K-dimensional vector representing features. We are interested in estimating θ * using labeled data", "section": "Abstract", "sec_num": null}, {"text": ", and a pre-trained ML model f (•) : X → Y. Here, f (•) is a black-box function with unknown operating characteristics and can be mis-specified. We also require an algorithm A that inputs the labeled data L and returns a consistent and asymptotically normally distributed estimator θ for θ * . There are three common ways in the literature to estimate θ * :", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Leveraging machine learning (ML) techniques to enhance and accelerate research has become increasingly popular in many scientific disciplines [44] . For example, sophisticated deep learning models have achieved remarkable success in predicting protein structure and interactions, which has the potential to significantly speed up the research process, save costs, and revolutionize the field of structural biology [1, 2, 25] . However, recent studies have pointed out that statistical inference using ML-predicted outcomes may lead to invalid scientific discoveries due to the lack of consideration of ML prediction uncertainty in traditional statistical approaches. To address this, researchers have introduced methods that couple extensive ML predictions with limited gold-standard data to ensure the validity of ML-assisted statistical inference [3, 35, 46] . Despite these advances, current ML-assisted inference methods can only address very basic statistical tasks, including mean estimation, quantile estimation, and linear and logistic regression [3, 35] . While the same mathematical principle behind existing ML-assisted inference methods can be generalized to a broader class of M-estimation problems, specific algebraic derivations and computational implementations are required for each new statistical task. Moreover, many tasks, such as the Wilcoxon rank-sum test, do not fit into the M-estimation framework. These issues pose significant challenges to the broad application of ML-assisted inference across various scientific domains.", "section": "Introduction", "sec_num": "1"}, {"text": "• Classical statistical methods apply algorithm A to only labeled data L = (X L , Y L ), and returns the estimator and its estimated variance [ θ L , Var( θ L )]. Valid confidence intervals and hypothesis tests can then be constructed using the asymptotic distribution of the estimator. However, it ignores the unlabeled data and ML prediction.", "section": "Introduction", "sec_num": "1"}, {"text": "• Imputation-based methods treat ML prediction f in the unlabeled data as the observed outcome, and apply algorithm A to U = (X U , f U ). We denote the estimator and estimated variance as [ η U , Var( η U )]. This has been shown to give invalid inference results and false scientific findings [3, 35, 36, 46 ]. • ML-assisted inference methods use both L and U as input. These approaches add a debiasing term in the loss function (or estimating equation) for M-estimation problems, thus removing the bias from the imputation-based estimators and producing results that are statistically valid and universally more powerful compared to classical methods [4, 35, 36] .", "section": "Introduction", "sec_num": "1"}, {"text": "Next, we use an example to provide intuition on ML-assisted inference and our protocol.", "section": "Introduction", "sec_num": "1"}, {"text": "We consider the mean estimation problem, where", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "θ * = E[Y i ] ≡ arg min θ E[ 1 2 (Y i -θ) 2 ]", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": ". The classical method only takes the labeled data Y L as input and yields an unbiased and consistent estimator for θ * : θ L = arg min θ", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "1 n n i=1 1 2 (Y i -θ) 2 = 1 n n i=1", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Y i . The imputation-based method only takes the unlabeled data f U as input and returns η U = arg min θ", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "1 N n+N i=n+1 1 2 ( f i -θ) 2 = 1 N n+N i=n+1 f i .", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "It is a biased and inconsistent estimator for E[Y i ] if the ML model f is mis-specified.", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "To address this, ML-assisted estimator takes both labeled data (Y L , f L ) and unlabeled data f U as input and adds a debiasing term to the loss function to rectify the bias caused by ML imputation [3, 20, 35, 36] :", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "θMLA = arg min θ 1 2 { ω0 1 N n+N i=n+1 ( fi -θ) 2 -[ ω0 1 n n i=1 ( fi -θ) 2 - 1 n n i=1 (Yi -θ) 2 ] Debiasing term } = ω0 1 N n+N i=n+1 fi -[ ω0 1 n n i=1 fi - 1 n n i=1 yi]", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Debiasing term", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": ", where the modified loss ensures the consistency of the ML-assisted estimator and the weight", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "ω 0 = Cov l [Y, f ]/n Var l [ f ]/n+ Varu[ f ]/N", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "ensures that ML-assisted estimator is no less efficient than the classical estimator with arbitrary ML predictions:", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Var( θ MLA ) = Var( θ L ) - Cov[Y, f ] n Var[ f ]+n 2 Var[ f ]/N ≤ Var( θ L ).", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Our proposed method is motivated by the observation that the sufficient statistics of the ML-assisted estimator θ MLA and its estimated variance Var( θ MLA ) are the following summary statistics:", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "θ ss = ( 1 n n i=1 y i , 1 n n i=1 f i , 1 N n+N i=n+1 f i ) and Var( θ ss ) =   Var l [Y ]/n Cov l [Y, f ]/n 0 Cov l [Y, f ]/n Var l [ f ]/n 0 0 0 Var u [ f ]/N  ", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Moreover, they can be easily obtained by applying the same algorithm A (mean estimation) to", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "• labeled data with observed outcome", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "A(Y L ) → [ θ L , Var( θ L )] = [ 1 n n i=1 y i , Var l [Y ]/n]", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "• labeled data with predicted outcome", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "A( f L ) → [ η L , Var( η L )] = [ 1 n n i=1 f i , Var l [ f ]/n]", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "• unlabeled data with predicted outcomeA(", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "f U ) → [ η U , Var( η U )] = [ 1 N n+N i=n+1 f i , Var u [ f ]/N ]", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "• bootstrap of labeled data A[(Y L , f L ) q , q = 1, . . . , Q] for estimation of Cov( θ L , η L ) = Cov l [Y, f ]/n. Here, (Y L , f L ) q represents the q-th bootstrap of labeled data.", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Combining these summary statistics for one-step debiasing ω 0 η U -( ω 0 η L -θ L ) recovers θ MLA .", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "To summarize, an algorithm for mean estimation, coupled with resampling, is sufficient for MLassisted mean estimation. This observation inspired us to generalize this protocol for a broad range of tasks. Our protocol illustrated in Fig. 1 only requires three steps: 1) using a pre-trained ML model to predict outcomes for labeled and unlabeled data, 2) applying existing analysis routines to generate summary statistics, and 3) using these statistics in a debiasing procedure to produce statistically valid results in ML-assisted inference.", "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Labeled data", "section": "Outcome", "sec_num": null}, {"text": "One-step debiasing", "section": "Pre-trained ML model", "sec_num": null}, {"text": "Step1: Use pre-trained ML model to predict the gold-standard outcome in both labeled and unlabeled data", "section": "ML-assisted inference", "sec_num": null}, {"text": "Apply the analysis routines to obtain the summary statistics", "section": "Step2:", "sec_num": null}, {"text": "Employ one-step debiasing to the summary statistics ", "section": "Step3:", "sec_num": null}, {"text": "𝑿 𝓛", "section": "Pre-trained ML model", "sec_num": null}, {"text": "Figure 1 : Workflow of PSPS for Task-Agnostic ML-Assisted Inference.", "section": "Summary statistics", "sec_num": null}, {"text": "Our work is closely related to recent methods developed in the literature of ML-assisted inference [3, 4, 20, 35, 37, 46, 56] , and is also related to methods for handling missing data [40, 42] and semi-supervised inference [6, 16, 50, 52] . While current ML-assisted inference methods modify the loss function or the estimating equation, our protocol works directly on the summary statistics. For simple problems such as mean estimation, current methods yield a closed-form solution to the optimization problem. However, for more general statistical tasks, there is no such closed-form solution. Current methods typically require the algebraic form of the loss function, its first-and second-order derivatives, and the variance for the estimator, as well as a newly implemented optimization algorithm to obtain the estimator. We use the logistic regression problem as an example. Here, θ * = arg min θ E[-Y (θX) T -ψ(Xθ)] and", "section": "Related work", "sec_num": "2.3"}, {"text": "ψ(t) = 1/(1 + exp(-t)). The ML-assisted estimator is θ MLA = arg min θ 1 N n+N i=n+1 ω[-f i θ T X T i - ψ(X i θ)]-{ 1 n n i=1 ω[-f i θ T X T i -ψ(X i θ)]-1 n n i=1 [-Y i θ T X T i -ψ(X i θ)]} with estimated asymptotic variance A -1 V(ω) A -1 , where A = 1 N +n ( n i=1 ψ ′′ (X i θ) X T i X i + n+N i=n+1 ψ ′′ (X i θ) X T i X i ), V(ω) = n N [ ω 2 Var n (ψ ′ (X i θ) -f i )X T i + Cov N +n (1 -ω)ψ ′ (X i θ)X T i + ( ω f i -Y i )X T i ,", "section": "Related work", "sec_num": "2.3"}, {"text": "and ω needs to be obtained by optimization to minimize the asymptotic variance. In contrast, our protocol simply applies logistic regression A to", "section": "Related work", "sec_num": "2.3"}, {"text": "• labeled data with observed outcomes (X L , Y L ) to obtain [ θ L , Var( θ L )] • labeled data with predicted outcomes (X L , f L ) to obtain [ η L , Var( η L )] • unlabeled data with predicted outcomes (X U , f U ) to obtain [ η U , Var( η U )] • bootstrap of labeled data (X L , Y L , f L ) q , q = 1, . . . , Q for Cov( θ L , η L ),", "section": "Related work", "sec_num": "2.3"}, {"text": "and returns ω T 0 η U -(ω T 0 η L -θ L ), where ω 0 = ( Var( η L )+ Var( η U )) -1 Cov( θ L , η L ).", "section": "Related work", "sec_num": "2.3"}, {"text": "For each new statistical task, as long as an existing analysis routine can produce an estimator that is asymptotically normally distributed, our protocol can be similarly applied. Additionally, the current mathematical principles guiding ML-assisted inference apply solely to M-estimation [3, 4, 20, 35, 56] . Our protocol extends beyond this limitation, addressing all estimation problems with an asymptotically normally distributed estimator.", "section": "Related work", "sec_num": "2.3"}, {"text": "Inference relying solely on summary statistics is widely used in the statistical genetics literature for practical reasons. Summary statistics-based methods have been developed for tasks such as variance component inference and genetic risk prediction [11, 12, 34, 39, 53] . In contrast to our work, these applications do not leverage ML predictions, but instead focus on inference using summary statistics obtained from observed outcomes. An exception is a previous study for valid genome-wide association studies (GWAS) on ML-predicted outcome [36] . However, it focused only on linear regression modeling with application to GWAS. The PSPS framework introduced in this paper aims to extend ML-assisted inference to general statistical tasks.", "section": "Related work", "sec_num": "2.3"}, {"text": "Our work is also related to semi-supervised learning, resampling-based inference, zero augmentation, and false discovery rate (FDR) control methods. Our protocol is designed for estimation and statistical inference using both labeled and unlabeled data, addressing a different problem from semi-supervised learning [55] , which primarily focuses on prediction. Our protocol is inspired by the core principle of resampling-based inference, which replaces algebraic derivation with computation [19] . The main difference is that we focus on how to use ML to support inference, whereas resamplingbased inference focuses on bias and variance estimation, and type-I error control. The idea of zero augmentation has been used in augmented inverse propensity weighting estimators [38] and in handling unmeasured confounders [48] and missing data for U-statistics [14] . These estimators do not incorporate ML, which is fundamental to our work. We also adapt techniques from the FDR literature [7] [8] [9] [10] . Our unique contribution is to use ML to support FDR control, thereby increasing its statistical power, in contrast to classical methods that rely solely on labeled data.", "section": "Related work", "sec_num": "2.3"}, {"text": "Building on Section 2, we formalized our protocol in Fig. 1 for ML-assisted inference:", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Algorithm 1 PSPS for ML-assisted inference", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Input: A pre-trained ML model f , labeled data L = (X L , Y L ), unlabeled data U = X U 1:", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Use the ML model f to predict the outcome in both labeled and unlabeled data.", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "2: Apply the algorithm A in the analysis routine to • labeled data (X L , Y L ) and obtain [ θ L , Var( θ L )]", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "• labeled data (X L , f L ) and obtain [ η L , Var( η L )]", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "• unlabeled data with (X U , f U ) and obtain [ η U , Var( η U )]", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "• Q bootstrap of labeled data (X L , Y L , f L ) q , q = 1, . . . , Q and obtain Cov( θ L , η L ). 3: Employ one-step debiasing to the summary statistics in step2:", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "θ PSPS = ω T 0 η U -( ω T 0 η L -θ L ), where ω 0 = [ Var( η L ) + Var( η U )] -1 Cov( θ L , η L ) and Var( θ PSPS ) = Var( θ L ) - Cov( θ L , η L ) T [ Var( η L ) + Var( η U )] -1 Cov( θ L , η L ) Output: ML-assisted point estimator θ PSPS , standard error Var( θ PSPS ), α-level confidence interval for the k-th coordinate C PSPS α,k = ( θ PSPS k ± z 1-α/2 Var( θ PSPS ) kk ), and (two-sided) p-value 2(1 -Φ(| θPSPS k √ Var( θPSPS) kk ) |))", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": ", where Φ is the CDF of the standard normal distribution.", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "The only requirements for our protocol are: i) algorithm A, when applied to labeled data (X L , Y L ), returns a consistent and asymptotically normally distributed estimator of θ * ; ii) labeled and unlabeled data are independent and identically distributed. Under these assumptions, the summary statistics have the following asymptotic properties:", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "EQUATION", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "where η ≡ η(P f ) ∈ R K is defined on (X, f ) ∼ P f , V(•) denotes the asymptotic variance and covariance of a estimator, and ρ = n N . The asymptotic approximation gives", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "V( θ L ) ≈ n Var( θ L ), V( θ L , η L ) ≈ n Cov( θ L , η L ), V( η L ) ≈ n Var( η L ) and V( η U ) ≈ N Var( η U ).", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Here, we do not require η L and η U to be consistent for θ * , thus allows arbitrary ML model.", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "With the summary statistics following a multivariate normal distribution asymptotically, the debiased estimator", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "θ PSPS = ω T 0 η U -( ω T 0 η L -θ L )", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "is consistent for θ * and asymptotically normally distributed (Theorem 1). Therefore, by plugging in a consistent estimator for its asymptotic variance V( θ PSPS ) ≈ n Var( θ PSPS ), valid confidence interval and hypothesis testing can be achieved.", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Remark 1. PSPS is more \"task-agnostic\" than existing methods in three aspects:", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "1. For M-estimation tasks, currently, only mean and quantile estimation, as well as linear, logistic, and Poisson regression, have been implemented in software tools and are ready for immediate application. For other M-estimation tasks, task-specific derivation of the MLassisted loss functions and asymptotic variance via the central limit theorem are necessary. After that, researchers still need to develop software packages and optimization algorithms to carry out real applications. In contrast, PSPS only requires already implemented algorithms and software designed for classical inference based on labeled data.", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "2. For problems that do not fall under M-estimation but have asymptotically normally distributed estimators, only PSPS can be applied, and all current methods would fail. The principles behind ML-assisted M-estimation do not extend to these tasks.", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "3. Even for M-estimation tasks that have already been implemented, PSPS offers the additional advantage of relying solely on summary statistics. The \"task-specific derivations\" refer not only to statistical tasks but also to scientific tasks. Real-world data analysis in any scientific discipline often involves conventions and nuisances that require careful consideration. For example, our work is partly motivated by GWAS [43] . Statistically, GWAS is a linear regression that regresses an outcome on many genetic variants. While the regressionbased statistical foundation is simple, conducting a valid GWAS requires accounting for numerous technical issues, such as sample relatedness (i.e., study participants may be genetically related) and population structure (i.e., unrelated individuals of the same ancestry are both genetically and phenotypically similar, creating confounded associations in GWAS). Sophisticated algorithms and software have been developed to address these complex issues [31] . It will be very challenging if all these important features need to be reimplemented in an ML-assisted GWAS framework. With our PSPS protocol, researchers can utilize existing tools that are highly optimized for genetic applications to perform ML-assisted GWAS. This adaptability is not just limited to GWAS, but is a major feature of our approach across scientific domains. PSPS enables researchers to conduct ML-assisted inference using well-established data analysis routines.", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Remark 2. The \"federated data analysis\" feature of PSPS refers to the fact that we only require summary statistics as input for inference, rather than individual-level raw data (features X and label Y ). For example, consider a scenario where labeled data is in one center and unlabeled data is in another, yet researchers cannot access individual-level data from both centers simultaneously. Under such conditions, current ML-assisted inference, which relies on accessing both labeled and unlabeled data to minimize a joint loss function, is not feasible. However, PSPS circumvents this issue by aggregating summary statistics from multiple centers, thereby performing statistical inference while upholding the privacy of individual-level data.", "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "In this section, we examine the theoretical properties of PSPS. In what follows, P → denotes convergence in probability and D → denotes convergence in distribution. All proofs are deferred to the Appendix A.", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "The first result shows that our proposed estimator is consistent, asymptotically normally distributed, and uniformly better in terms of element-wise asymptotic variance compared with the classical estimator based on labeled data only.", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Theorem 1. Assuming equation (1) holds, then θ PSPS P → θ * , and", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "n 1/2 ( θ PSPS -θ * ) D → N 0, V( θ PSPS ) , where V( θ PSPS ) = V( θ L )-V( θ L , η L ) T (V( η L )+ρV( η U )) -1 V( θ L , η L ). Assume the k-th column of V( θ L , η L ) is not a zero vector and at least one of V( η L ) and V( η U ) are positive definite, then V( θ PSPS ) kk ≤ V( θ L ) kk . With V( θ PSPS ) P → V( θ PSPS ), lim n P(θ * k ∈ C PSPS α,k ) = 1 -α.", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "V( θ PSPS ) can be obtained by applying the algebraic form of V( θ PSPS ) using the bootstrap estimators for V( θ L ), V( η L ), V( θ L , η L ), and V( η U ). The regularity conditions for consistent bootstrap variance estimation are outlined in Theorem 3.10 (i) of [41] . We also refer readers to [21] , which showed that bootstrap-based variance provides valid but potentially conservative inference.", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "This result indicates that a greater reduction in variance for the ML-assisted estimator is associated with larger values of V( θ L , η L ) and smaller values of V( η L ), V( η U ), and ρ. The variance reduction term", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "[V( θ L , η L ) T (V( η L ) + ρV( η U )) -1 V( θ L , η L )", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "] kk can also serve as a metric for selecting the optimal ML model in ML-assisted inference.", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Our next result shows that three existing methods, i.e., PPI, PPI++, and PSPA, are asymptotically equivalent to PSPS with different weighting matrices. A broader class for consistent estimator of θ", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "* is θ(ω) = ω T η U -(ω T θ L -η L ), where ω is a K × K matrix. The consistency of θ(ω) for θ * only requires ω T ( η U -η L ) P → 0. Since ( η U -η L )", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "P → 0, assigning arbitrarily fixed weights for will satisfy the condition. However, the choice of weights influences the efficiency of the estimator as illustrated in Proposition 2 later. Proposition 1. Assuming equation (1) and regularity condition for the asymptotic normality of current ML-assisted estimator holds. For any M-estimation problem, we have", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "n 1 2 ( θ (diag(ω ele )C)-θ PSPA ) D → 0, n 1 2 ( θ (diag(ω tr )C)-θ PPI++ ) D → 0, n 1 2 ( θ (diag(1)C)-θ PPI ) D → 0.", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Here, ω ele = [ω ele,1 , . . . , ω ele,K ] T ∈ R K and ω ele,k minimizing the k-th diagonal element of V( θ(ω)), ω tr is a scalar used to minimize the trace of V( θ(ω)), and C is a matrix associated with the second derivatives of the loss function in M-estimation, with further details deferred to Appendix A.", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "This demonstrates that for M-estimation problems, our method is asymptotically equivalent to PSPA, PPI++, and PPI with the respective weights diag(ω ele )C, diag(ω tr )C, and diag(1)C. Therefore, PSPS can be viewed as a generalization of these existing methods.", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Our third result shows that the weights used in the Proposition 1 are not optimal. Instead, our choice of ω 0 represents the optimal smooth combination of ( θ L , η L , η U ) in terms of minimizing the asymptotic variance, while still preserving consistency.", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Proposition 2. Suppose n 1/2 (g( θ L , η L , η U ) -θ * ) D → N (0, Σ g ) and g is a smooth function, then Σ g kk ≥ Σ PSPS kk", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Together with Proposition 1, our results demonstrate that our protocol provides a more efficient estimator compared to existing methods for the M-estimation problems. Furthermore, the applicability of our protocol is not limited to M-estimation and only requires summary statistics as input. It also indicates that in a setting of federated data analysis [24] where individual-level data are not available, PSPS proves to be the optimal approach for combining shared summary statistics. Remark 3. PPI++ [4] employs a power-tuning scalar for variance reduction in ML-assisted inference. This scalar is obtained by minimizing the trace or possibly other scalarization of the estimator's variance-covariance matrix. However, the asymptotic variance of PSPS is always equal to or smaller than that of PPI++, irrespective of the scalarization chosen by researchers. This advantage arises because PSPS utilizes a K × K power tuning matrix, ω, for variance reduction, where K represents the dimensionality of parameters. This matrix facilitates information sharing across different parameter coordinates, thereby enhancing estimation precision. The choice of weighting matrix in PSPS also allows for element-wise variance reduction, reducing each diagonal element of the variance-covariance matrix. In contrast, the single scalar in PPI++ can only target overall trace reduction or variance reduction of a specific element. A detailed example is provided in Appendix B. Only in one-dimensional parameter estimation tasks, such as mean estimation, PPI++ and PSPS exhibit the same asymptotic variance.", "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "We also provide several extensions to ensure the broad applicability of our method.", "section": "Extensions", "sec_num": "3.3"}, {"text": "Here, we relax the assumption that the labeled data and unlabeled data are independent. When they are not independent, this can lead to the non-zero covariance between the η L and η U . Consider a broader class of summary statistics asymptotically satisfying", "section": "Labeled data and unlabeled data are not independent", "sec_num": "3.3.1"}, {"text": "n 1/2   θ L -θ * η L -η η U -η   D → N    0 K 0 K 0 K ,     V( θ L ) V( θ L , η L ) V( η L , η U ) V( θ L , η L ) V( η L ) √ ρV( θ L , η U ) V( η L , η U ) √ ρV( θ L , η U ) ρV( η U )       ", "section": "Labeled data and unlabeled data are not independent", "sec_num": "3.3.1"}, {"text": "We can similarly employ the one-step debiasing", "section": "Labeled data and unlabeled data are not independent", "sec_num": "3.3.1"}, {"text": "θ no-indep PSPS = ω T 0 η U -ω 0 ( η L -θ) where ω 0 = ( V( θ L , η L ) -V( η L , η U )) T ( V( η L ) + V( η U ) -2 V( θ L , η U )) -1 and Var( θ no-indep PSPS ) = Var( θ L ) - ( V( θ L , η L ) -V( η L , η U )) T (V( η L ) + V( η U ) -2 V( θ L , η U )) -1 ( V( θ L , η L ) -V( η L , η U )).", "section": "Labeled data and unlabeled data are not independent", "sec_num": "3.3.1"}, {"text": "The theoretical guarantees of the proposed estimator can be similarly derived by Theorem 1.", "section": "Labeled data and unlabeled data are not independent", "sec_num": "3.3.1"}, {"text": "The other assumption of our approach is that the labeled and unlabeled data are identically distributed so that we can ensure η L -η U P → 0 and validity of PSPS results. To address the potential violation of this assumption, we introduce a sensitivity analysis with hypothesis testing for the null H 0 :", "section": "Sensitivity analysis for distributional shift between labeled and unlabeled data", "sec_num": "3.3.2"}, {"text": "η L,k = η U ,k with test statistics η L,k -η U ,k √ Var( η L,k )+ Var( η U ,k ) D → N (0, 1) to assess if η L,k and η U ,k are significantly different.", "section": "Sensitivity analysis for distributional shift between labeled and unlabeled data", "sec_num": "3.3.2"}, {"text": "Here, the subscript k indicates the k-th coordinate. We recommend using p-value < 0.1 as evidence for heterogeneity and caution the interpretation of results from ML-assisted inference.", "section": "Sensitivity analysis for distributional shift between labeled and unlabeled data", "sec_num": "3.3.2"}, {"text": "The output from PSPS can be used for ML-assisted FDR control, achieving greater statistical power compared to classical FDR control methods that solely rely on labeled data. We refer to our approach as PSPS-BH and PSPS-knockoff. Briefly, PSPS-BH processes the p-value from ML-assisted linear regression through the Benjamini-Ho<PERSON>berg (BH) procedure [9] , while PSPS-knockoff utilizes the ML-assisted debiased <PERSON>so coefficient [23, 51] in the ranking algorithm of knockoff [7] . We present our algorithm in Appendix C and evaluate their performance using experiments in Section 4.", "section": "ML-assisted FDR control", "sec_num": "3.3.3"}, {"text": "We have discussed ML-assisted inference with outcomes predicted by ML models. Here, we note that PSPS can also be applied when either features alone are predicted or both features and outcomes are predicted. The key idea is that the difference between point estimators obtained from applying A to predicted features in both labeled and unlabeled datasets is a consistent estimator for zero. This enables zero augmentation for estimators from observed features and outcomes. To implement this, modify step 2 in Algorithm 1 to apply A to predicted features in both labeled and unlabeled data. A similar approach is applicable when both features and outcomes are predicted.", "section": "ML-assisted inference with predicted features", "sec_num": "3.3.4"}, {"text": "We conduct simulations to assess the finite sample performance of our method. Our objectives are to demonstrate that 1) PSPS achieves narrower confidence intervals when applied to statistical tasks already implemented in existing ML-assisted methods; 2) when applied to statistical tasks that have not been implemented for ML-assisted inference, PSPS provides confidence intervals with narrower width but correct coverage (indicating higher statistical power) compared to classical approaches rely solely on labeled data; 3) PSPS provides well-calibrated FDR control and achieves higher power compared to classical methods only using labeled data.", "section": "Numerical experiments and real data application 4.1 Simulations", "sec_num": "4"}, {"text": "Tasks that have been implemented for ML-assisted inference We compare PSPS with the classical method using only labeled data, the imputation-based method using only unlabeled data, and three ML-assisted inference methods PPI, PPI++, and PSPA [3, 4, 35] on mean estimation and linear and logistic regression. We defer the detailed data-generating process to Appendix D. In short, we generated outcome Y i from feature X 1i and X 2i , and obtained the pre-trained random forest that predict Y i using X 1i and X 2i . We have 500 labeled samples (X 1i , Y i , f i ) , and unlabeled samples (X 1i , f i ) ranged from 1,000 to 10,000. Our goal is to estimate the mean of Y i , as well as the linear and logistic regression coefficient between Y i and X 1i . Fig. 2a-c show confidence interval coverage and Fig. 2d-f show confidence interval width. We find that the imputation-based method fails to obtain correct coverage, while all others including PSPS have the correct coverage. PSPS has narrower confidence intervals compared to the classical method and other approaches for ML-assisted inference. Tasks that have not been implemented for ML-assisted inference Next, we consider several commonly used statistical tasks that currently lack implementations for ML-assisted inference including quantile regression [27] , instrumental variable (IV) regression [5] , negative binomial (NB) regression [22] , debiased <PERSON><PERSON> [51] , and the Wilcoxon rank-sum test [28] . Similar to our previous simulations, we utilize labeled data, unlabeled data, and a pre-trained ML model. Detailed simulation settings are deferred to the Appendix D. Our goal is to estimate the regression coefficient between Y i and X 1i for quantile (at quantile level 0.75), IV, and NB regression, between Y i and high dimensional features X i ∈ R 150 for debiased Lasso, and to perform hypothesis testing on the medians of two independent samples Y i |X 1i = 1 and Y i |X 1i = 0 using the Wilcoxon rank-sum test.", "section": "Numerical experiments and real data application 4.1 Simulations", "sec_num": "4"}, {"text": "Fig. 3a-d show confidence interval coverage and Fig. 3f -i show confidence interval width for parameter estimation. Fig. 3e and Fig. 3j show the type-I error and statistical power for the Wilcoxon rank-sum test. We found that the imputation-based method fails to obtain correct confidence interval coverage and shows inflated type-I error, while PSPS and classical method have the correct coverage and wellcalibrated type-I error control. PSPS has narrower confidence intervals width in all settings, and higher statistical power for the Wilcoxon rank-sum test compared to classical methods. Confidence intervals become narrower as unlabeled sample size increases, indicating higher efficiency gain.", "section": "Numerical experiments and real data application 4.1 Simulations", "sec_num": "4"}, {"text": "We evaluate the finite sample performance of PSPS-BH and PSPS-knockoff in controlling the FDR compared with classical and imputation-based methods as baselines. We consider low-dimensional(K < n) and high-dimensional(K > n) linear regressions for PSPS-BH and PSPS-knockoff, respectively. We simulate the data such that only a proportion of the features are truly associated with the outcome. The data generating process is deferred to Appendix D. Our goal is to select the associated features while maintaining the target FDR level. Imputation-based method failed to control FDR in either low-dimensional or high-dimensional settings. Classical approach, PSPS-BH, and PSPS-knockoff effectively controlled in both lowdimensional and high-dimensional settings. PSPS-BH, and PSPS-knockoff achieve higher statistical power compared to the classical method.", "section": "FDR control", "sec_num": null}, {"text": "These simulations demonstrate that PSPS outperforms existing methods and can be easily adapted for various statistical tasks not yet implemented in current ML-assisted inference methods.", "section": "FDR control", "sec_num": null}, {"text": "We employed our method to carry out ML-assisted quantile regression to identify genetic variants associated with the outcome variability (vQTL) of bone mineral density derived from dual-energy X-ray absorptiometry imaging (DXA-BMD) [33] . DXA-BMD is the primary diagnostic marker for osteoporosis and fracture risk [15, 54] . Identifying vQTL for DXA-BMD can provide insights into the biological mechanisms underlying outcome plasticity and reveal candidate genetic variants involved in potential gene-gene and gene-environment interactions [29, 32, 45, 47, 49] . We focused on total body DXA-BMD, which integrates measurements from multiple skeletal sites. We used data from the UK Biobank [13] , which includes 36,971 labeled and 319,548 unlabeled samples with 9,450,880 genetic variants after quality control. We predicted DXA-BMD values in both labeled and unlabeled samples using SoftImpute [30] with 466 other variables measured in the UK Biobank. Prediction in the labeled sample was implemented through cross-validation to avoid overfitting. The implementation detail is deferred to Appendix D. We used the BH procedure to correct for multiple testing and considered FDR < 0.05 as the significance threshold.", "section": "Identify vQTLs for bone mineral density", "sec_num": "4.2"}, {"text": "No genetic variants reached statistical significance under the classical method with only labeled data. PSPS identified 108 significant variants with FDR < 0.05 spanning 5 independent loci, showcasing the superior statistical power of PSPS (Fig. E.2 and Table E .1). Notably, these significant vQTL cannot be identified by linear regression [36] , indicating the different genetic mechanisms controlling outcome levels and variability for DXA-BMD.", "section": "Identify vQTLs for bone mineral density", "sec_num": "4.2"}, {"text": "We compared the computational efficiency of PSPS with existing methods using a dataset of 500 labeled and 10,000 unlabeled data points. Results are shown in Table E .2. While PSPS is slower due to resampling, its overall runtime is still relatively short.", "section": "Computational efficiency", "sec_num": "4.3"}, {"text": "We introduced a simple, task-agnostic protocol for ML-assisted inference, with applications across a broad range of statistical tasks. We established the consistency and asymptotic normality of the proposed estimator. We further introduced several extensions to expand the scope of our approach.", "section": "Conclusion", "sec_num": "5"}, {"text": "Through extensive experiments, we demonstrated the superior performance and broad applicability of our method across diverse tasks. Our protocol involves initially generating summary statistics using computationally efficient software tools in scientific data analysis, followed by integration of summary statistics to produce ML-assisted inference results, which achieves high computational efficiency while maintaining statistical validity. Future work could focus on developing a fast resampling algorithm to further improve computational efficiency.", "section": "Conclusion", "sec_num": "5"}, {"text": "A.1 Proof the Theorem 1", "section": "A Proofs", "sec_num": null}, {"text": "Proof. Since", "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Proofs", "sec_num": null}, {"text": "θ L P → θ * and η U -η L P → 0. Given weights ω 0 = ( V( η L ) + ρ V( η U )) -1 V( θ L , η L )", "section": "A Proofs", "sec_num": null}, {"text": ", where the variances are consistently estimated, <PERSON><PERSON><PERSON>'s theorem implies that, ω T 0 ( η U -η L )", "section": "A Proofs", "sec_num": null}, {"text": "P → 0.", "section": "A Proofs", "sec_num": null}, {"text": "Also by <PERSON><PERSON><PERSON>'s theorem,", "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Proofs", "sec_num": null}, {"text": "which completes the proof of consistency.", "section": "A Proofs", "sec_num": null}, {"text": "By multivariate delta methods, denoting h([x, y, z] T ) = x + ω T 0 (z -y), we have ∇h([x, y, z] T ) = [1, -ω 0 , ω 0 ], therefore by the consistency of ω 0 ,", "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Proofs", "sec_num": null}, {"text": "which completes the proof of asymptotic normality.", "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "section": "A Proofs", "sec_num": null}, {"text": "Here, by our assumption, (V( η L ) + ρV( η U )) -1 is a positive definite matrix. Therefore, quadratic form V( θ L , η L ) T :,k (V( η L ) + ρV( η U )) -1 V( θ L , η L ) :,k is non-negative and is zero if only all elements of V( θ L , η L ) :,k is zero, which completes the proof of element-wise variance reduction.", "section": "A Proofs", "sec_num": null}, {"text": "Proof. Given", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "the asymptotic variance of θ(ω", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "We first define the M-estimation (Z-estimation) problem. The goal is to estimate a K-dimensional parameter θ * defined through an estimating equation", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "where ψ(•, •; θ) is a user-defined function. By the theory of Z-estimation and a recent paper on MLassisted inference [35] , we have", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "V( θ L ) = A -1 θ * M 1 A -1 θ * , V( θ L , η L ) = A -1 η M 4 A -1 θ * , V( η L ) = A -1 η M 2 A -1 η , and V( η U ) = A -1 η M 3 A -1 η .", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Here,", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "M 1 = Var l [ψ(Y, X; θ * )], M 2 = Var l [ψ( f , X; θ * )], M 3 = Var u [ψ( f , X; θ * )], M 4 = Cov l [ψ(Y, X; θ * ), ψ( f , X; θ * )], A θ * = E[∂ψ(Y, X; θ * )/∂θ], A η = E[∂ψ( f , X; η)/∂η], and ρ = n N .", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Rewritten V( θ(ω)) using the above notation, we have", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Plug in", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "into the equation above, we have", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": ")", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "To connect our protocol with existing methods, we define ", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "By the theory of PSPA, PPI++, and PPI paper [3, 4, 35] , we have", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": ")", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": ")", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Based on the proof of Theorem 1, we have the", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": ")", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": ")", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Denote C = A -1 θ * A η , we have PSPA, PPI++, and PPI is asymptotically equivalent to diag(ω * ele )C, diag(ω * tr )C, and diag(1)C, respectively. This completes the proof.", "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Proof. We apply the first-order Taylor expansion to g( θ L , η L , η U ) around (θ * , η, η):", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "where we used ∇ η,1 and ∇ η,2 to denote the gradient of g(θ * , η, η) w.r.t the first and second η, respectively.", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "This can be written as a linear function of (θ * , η, η):", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "where", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "µ = g(θ * , η, η) -∇ θ * g(θ * , η, η)θ * -2∇ η g(θ * , η, η)η, β 1 = ∇ θ * g(θ * , η, η), β 2 = ∇ η,1 g(θ * , η, η), β 3 = ∇ η,2 g(θ * , η, η).", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "Since we require g( θ L , η L , η U ) P → θ * , we have µ = 0, β 1 = 1, and β 2 + β 3 = 0. This leads to", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "Given", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "we have", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "which is a quadratic function of β 3 and achieves it minimum when", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "β 3 = (V( η L ) + ρV( η U )) -1 V( θ L , η L ) = ω 0 .", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "This completes the proof.", "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "Consider a linear regression with two predictors: Since θPSPS,1 involves two zero-augmentation terms (i.e., -w 1 η1L +w 1 η1U and -ω 12 η2L + ω 12 η2U ), its asymptotic variance should be less than or equal to that of PPI++ with one augmentation term. Therefore, PSPS borrows information from both coordinates, but PPI++ is restricted to information from only the first coordinate. Although the PPI++ can be used under a different scalarization, it still contains one augmentation term.", "section": "B An example for understanding the difference between PSPS and PPI++", "sec_num": null}, {"text": "Y ∼ θ 1 X 1 + θ 2 X 2 .", "section": "B An example for understanding the difference between PSPS and PPI++", "sec_num": null}, {"text": "Algorithm 2 PSPS-BH for linear regression ", "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": "Input: Labeled data L = (X L , Y L , f L ), unlabeled data U = (X U , f U ), FDR level q ∈ (0,", "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": ":= p (k) , where k = max k = 1, . . . , K : p (k) ≤ k K q Output: Discoveries S = k : p k ≤ τ BH q Algorithm 3 PSPS-knockoff with debiased Lasso Input: Labeled data L = (X L , Y L , f L ), unlabeled data U = (X U , f U ), FDR level q ∈ (0, 1).", "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": "1: Obtain the augmented labeled and unlabeled data as L = (X L , XL , Y L , f L ) and Ũ = (X U , XU , f U ) where XL ← knockoff-sample(X L ) and XU ← knockoff-sample(X U ). 2: Calculate the PSPS debiased Lasso coefficient β PSPS-DLasso ← PSPS-DLasso( L, Ũ)", "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": "3: W k for k = 1, • • • , K ← knockoff-statistic( β PSPS-DLasso ) 4: Set the cutoff τ knockoff q = t > 0 : #{k:W k ≤-t} #{k:W k ≥t}∨1 ≤ q Output: Discoveries S = k : M k > τ knockoff q", "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": "Here, we employ second-order multivariate Gaussian knockoff variables for knockoff-sample and use the difference between the absolute values of the k-th feature and its knockoff coefficient as the knockoff-statistic. Alternative choices for these two steps can also be readily integrated into our algorithm [26] .", "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": "Here, we provide the details for our simulation. All our simulation is run in R with version 4.2.1 (2022-06-23) in a MacBook Air with an M1 chip. For all the simulations, the ground truth coefficients are obtained using 5 × 10 4 samples; A pre-trained random forest with 500 trees to grow is obtained from hold-out data. We bootstrap the labeled data for 200 times for covariance estimation. All simulations are repeated 1000 times.", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• Mean estimation, Linear regression, and Quantile regression: We simulate the data from", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "Y i = X 1i β 1 + X 2i β 2 + X 2 1i β 2 + X 2 2i β 2 + ϵ i", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": ", where X i1 and X 2i are independent simulated from N (0, 1),", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "β 1 = √ 0.08, β 2 is set to be the value such that X 2i β 2 + X 2 1i β 2 + X 2 2i", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "β 2 explains 81% of the outcome variance, and ϵ i is simulated from a mean zero normal distribution with variance such that Var(Y i ) = 1. We use X 1i and X 2i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 1000, 2500, 5000, or 10000.", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• Logistic regression: We simulate the data from", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "Y i = 1( Ỹi > median ( Ỹi )), where Ỹi = X 1i β 1 + X 2i β 2 + X 2 1i β 2 + X 2 2i β 2 + ϵ i", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": ", where X i1 and X 2i are independent simulated from N (0, 1), β 1 = √ 0.08, β 2 is set to be the value such that X 2i β 2 + X 2 1i β 2 + X 2 2i β 2 explains 81% of the outcome variance, and ϵ i is simulated from a mean zero normal distribution with variance such that Var( Ỹi ) = 1. We use X 1i and X 2i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 1000, 2500, 5000, or 10000.", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• Instrumental variable (IV) regression: We simulate the data by", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "We use X 1i and X 2i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 1000, 2500, 5000, or 10000.", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "The Z i is used as a instrument for X 1i . • Negative binomial (NB) regression: We simulate the data by", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "Y i = NegativeBinomial (s = k, µ = µ i )", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": ", where s is the dispersion parameter and µ is the rate.", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "We use X 1i and X 2i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 1000, 2500, 5000, or 10000.", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• Debiased <PERSON><PERSON>: We simulate the data by", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "θ 1 , . . . , θ 15 = 0.9 √ 15 ; θ 16 , . . . , θ 200 = 0 (42)", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "We use X 1i , . . . , X 200i as features to predict the Y i in the random forest. We consider labeled data with 100 individuals, and unlabeled data with sample size 1500, 2000, 2500, or 3000.", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• Wilcoxon rank-sum test: We simulate the data by", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "where β 1 = √ 0.01 to power simulation and β 1 = 0 for type-I error simulation, β 2 is set to be the value such that β 2 X 2i + β 2 X 2 2i explains 81% of the outcome variance, and τ ϵ is set to be the value such that Var(Y i ) = 0.We use X 1i and X 2i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 1000, 2500, 5000, or 10000.", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• <PERSON><PERSON><PERSON><PERSON> (BH) procedure: We set K = 150 generate the features independently from N (0, Σ), where Σ is a symmetric Toeplitz matrix that has the structure:", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "The correlation r is set to be 0.25. We then simulate the outcome Y i = 150 k=1 X ki β k + ϵ i , where we randomly sample 15 β k to be 0.15 and let all other remaining β k to be 0. ϵ i is simulated from a mean-zero normal distribution with variance set to the value such that Var(Y i ) = 1. We further generate Z i = 0.9Y i + 150 k=1 X ki θ k + γ i , where θ k = 0.15 for all k = 1, . . . , 150. We use Z i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 5000.", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• knockoff: We used the same setting as described in the BH procedure above to generate the data. The only difference is that we set β k = 0.5 for features associated with the outcome and considered labeled data consisting of 100 individuals, along with unlabeled data comprising a sample size of 1000.", "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "Our prediction pipeline comprises two components: prediction for unlabeled data and prediction for labeled data. To predict bone mineral density in unlabeled data, we first selected predictive features by 1) calculating the correlation of bone mineral density with 466 other variables (sample size > 200,000 from UK Biobank) using labeled data and 2) selecting the top 50 variables with the highest correlations as inputs for the SoftImpute algorithm [30] to predict bone mineral density in the unlabeled data. For the labeled data, we employ a similar approach but incorporate 10-fold cross-validation to prevent overfitting. We select the predictive variables and train the SoftImpute model using 90% of the labeled data. We then perform predictions on the remaining 10% in each fold and repeat this process 10 times across all folds. Justification: We provided extensive theoretical and experimental evidence to support the main claims in our paper.", "section": "D.2 Identify vQTLs for bone mineral density", "sec_num": null}, {"text": "Guidelines:", "section": "D.2 Identify vQTLs for bone mineral density", "sec_num": null}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "section": "D.2 Identify vQTLs for bone mineral density", "sec_num": null}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?", "section": "Limitations", "sec_num": "2."}, {"text": "Answer: [Yes]", "section": "Limitations", "sec_num": "2."}, {"text": "Justification: We have discussed the limitations and future direction of our paper in Conclusion section of the paper.", "section": "Limitations", "sec_num": "2."}, {"text": "Guidelines:", "section": "Limitations", "sec_num": "2."}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "section": "Limitations", "sec_num": "2."}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "section": "Limitations", "sec_num": "2."}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Answer: [Yes]", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Justification: We have provided the code in the Supplementary Material.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Guidelines:", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that paper does not include experiments requiring code. • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the results?", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Answer: [Yes]", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Justification: The detail of the experimental can be found in Appendix D.", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Guidelines:", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "• The answer NA means that the paper does not include experiments.", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments?", "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Answer: [Yes] Justification: We reported the confidence interval width and p-value for statistical significance throughout the paper.", "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Guidelines:", "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The answer NA means that the paper does not include experiments.", "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions).", "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text.", "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments?", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Answer: [Yes]", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Justification: The detail of the experiments compute resources can be found in Appendix D.", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Guidelines:", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "• The answer NA means that the paper does not include experiments.", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "• The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?", "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Answer: [Yes] Justification: We have followed the code Of ethics.", "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Guidelines:", "section": "Code Of Ethics", "sec_num": "9."}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "section": "Code Of Ethics", "sec_num": "9."}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [Yes]", "section": "Broader Impacts", "sec_num": "10."}, {"text": "Justification: We have discussed the positive societal impacts of our method is accelerating scientific in the abstract and introduction.", "section": "Broader Impacts", "sec_num": "10."}, {"text": "Guidelines:", "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact.", "section": "Broader Impacts", "sec_num": "10."}, {"text": "• Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)? Answer: [NA] Justification: Our paper introduced a statistical method and therefore poses no such risks. Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort. 12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected? Answer: [Yes] Justification: We have cited all paper that produced the code package or dataset. Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset.", "section": "Safeguards", "sec_num": "11."}, {"text": "38th Conference on Neural Information Processing Systems (NeurIPS 2024).", "section": "", "sec_num": null}], "back_matter": [{"text": "Acknowledgements: We gratefully acknowledge research support from the National Institutes of Health (NIH; grant U01 HG012039) and support from the University of Wisconsin-Madison Office of the Chancellor and the Vice Chancellor for Research and Graduate Education with funding from the Wisconsin Alumni Research Foundation.", "section": "acknowledgement", "sec_num": null}, {"text": "Answer: [Yes] Justification: The assumptions and a complete (and correct) proof can be found in Section 3 and Appendix A, respectively. Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "section": "annex", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?Answer: [Yes] Justification: The detail of the experimental can be found in Appendix D.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "section": "Guidelines:", "sec_num": null}, {"text": "• For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators. ", "section": "Open access to data and code", "sec_num": "5."}], "ref_entries": {"FIGREF0": {"type_str": "figure", "text": "Figure 2: Simulation for tasks that have been implemented for ML-assisted inference including mean estimation, linear regression, and logistic regression from left to right. Panel a-c present confidence interval coverage and panels d-f present confidence interval width.", "fig_num": "23", "num": null, "uris": null}, "FIGREF1": {"type_str": "figure", "text": "Fig. E.1a-b shows the estimated FDR and Fig. E.1c-d shows the statistical power for different methods.Imputation-based method failed to control FDR in either low-dimensional or high-dimensional settings. Classical approach, PSPS-BH, and PSPS-knockoff effectively controlled in both lowdimensional and high-dimensional settings. PSPS-BH, and PSPS-knockoff achieve higher statistical power compared to the classical method.", "fig_num": null, "num": null, "uris": null}, "FIGREF2": {"type_str": "figure", "text": "ω * tr := arg min ωtr Tr[Σ(ω tr )] where ω tr = [ω tr , . . . , ω tr ] T ∈ R K", "fig_num": null, "num": null, "uris": null}, "FIGREF3": {"type_str": "figure", "text": "Obtain the p-value p k for features k = 1, • • • , K by ML-assisted linear regression PSPS-LR(L, U) 2: Sort the p-values in ascending order p (1) ≤ p (2) ≤ . . . ≤ p (K) 3: Finds the p-value cutoff τ BH", "fig_num": "11", "num": null, "uris": null}, "FIGREF4": {"type_str": "figure", "text": "q", "fig_num": null, "num": null, "uris": null}, "FIGREF5": {"type_str": "figure", "text": "Figure E.1: Simulation for FDR control. Panel a-b shows the estimated FDR level given the expected FDR. Panel c-d shows the power.", "fig_num": "1", "num": null, "uris": null}, "FIGREF6": {"type_str": "figure", "text": "Figure E.2: Manhattan plot of vQTLs for bone mineral density. The X-axis represents chromosomes (CHR), plotted by base pair positions (BP). Each point on the plot indicates a single nucleotide polymorphism (SNP). The Y-axis depicts -log10(p-values).", "fig_num": "2", "num": null, "uris": null}, "TABREF4": {"type_str": "table", "text": "", "num": null, "content": "<table><tr><td colspan=\"4\">Method Linear regression Logistic regression NeurIPS Paper Checklist</td></tr><tr><td>1. Claims</td><td>PSPS PPI</td><td>1.62s 0.024s</td><td>8.27s 0.032s</td></tr><tr><td colspan=\"4\">PPI++ Question: Do the main claims made in the abstract and introduction accurately reflect the 0.031s 0.077s</td></tr><tr><td colspan=\"3\">PSPA paper's contributions and scope? 0.049s</td><td>0.034s</td></tr><tr><td colspan=\"4\">Table E.2: Runtime experiments. Utilizing a dataset with 500 labeled and 10,000 unlabeled data Answer: [Yes]</td></tr><tr><td colspan=\"4\">points, PSPS required 1.62 seconds for linear regression and 8.27 seconds for logistic regression using</td></tr><tr><td colspan=\"4\">200 bootstrap resampling. The computation of one-step debiasing using summary statistics alone</td></tr><tr><td colspan=\"4\">took 0.032 seconds for linear regression and 0.033 seconds for logistic regression. Current methods,</td></tr><tr><td colspan=\"4\">which estimate asymptotic variance via the closed form derived by the Central Limit Theorem instead</td></tr><tr><td colspan=\"4\">of resampling, ranged from 0.024 to 0.049 seconds for linear regression and 0.032 to 0.077 seconds</td></tr><tr><td>for logistic regression.</td><td/><td/><td/></tr><tr><td colspan=\"4\">.1: Significant vQTLs for bone mineral density. Abbreviations: CHR, Chromosome; BP, Base</td></tr><tr><td colspan=\"4\">Pair; SNP, Single Nucleotide Polymorphism; A1, Allele 1 (Effect Allele); A2, Allele 2 (Non-effect</td></tr><tr><td colspan=\"4\">Allele); EAF, Effect Allele Frequency; BETA, Effect Size (Beta Coefficient); SE, Standard Error;</td></tr><tr><td colspan=\"2\">FDR, False Discovery Rate.</td><td/><td/></tr></table>", "html": null}, "TABREF5": {"type_str": "table", "text": "• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).", "num": null, "content": "<table/>", "html": null}}}}