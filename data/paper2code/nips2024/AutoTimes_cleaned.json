{"paper_id": "AutoTimes", "title": "AutoTimes: Autoregressive Time Series Forecasters via Large Language Models", "abstract": "Foundation models of time series have not been fully developed due to the limited availability of time series corpora and the underexploration of scalable pre-training. Based on the similar sequential formulation of time series and natural language, increasing research demonstrates the feasibility of leveraging large language models (LLM) for time series. Nevertheless, the inherent autoregressive property and decoder-only architecture of LLMs have not been fully considered, resulting in insufficient utilization of LLM abilities. To fully revitalize the general-purpose token transition and multi-step generation capability of large language models, we propose AutoTimes to repurpose LLMs as Autoregressive Time series forecasters, which projects time series into the embedding space of language tokens and autoregressively generates future predictions with arbitrary lengths. Compatible with any decoder-only LLMs, the consequent forecaster exhibits the flexibility of the lookback length and scalability with larger LLMs. Further, we formulate time series as prompts, extending the context for prediction beyond the lookback window, termed in-context forecasting. By introducing LLM-embedded textual timestamps, Auto-Times can utilize chronological information to align multivariate time series. Empirically, AutoTimes achieves state-of-the-art with 0.1% trainable parameters and over 5× training/inference speedup compared to advanced LLM-based forecasters.", "pdf_parse": {"paper_id": "AutoTimes", "abstract": [{"text": "Foundation models of time series have not been fully developed due to the limited availability of time series corpora and the underexploration of scalable pre-training. Based on the similar sequential formulation of time series and natural language, increasing research demonstrates the feasibility of leveraging large language models (LLM) for time series. Nevertheless, the inherent autoregressive property and decoder-only architecture of LLMs have not been fully considered, resulting in insufficient utilization of LLM abilities. To fully revitalize the general-purpose token transition and multi-step generation capability of large language models, we propose AutoTimes to repurpose LLMs as Autoregressive Time series forecasters, which projects time series into the embedding space of language tokens and autoregressively generates future predictions with arbitrary lengths. Compatible with any decoder-only LLMs, the consequent forecaster exhibits the flexibility of the lookback length and scalability with larger LLMs. Further, we formulate time series as prompts, extending the context for prediction beyond the lookback window, termed in-context forecasting. By introducing LLM-embedded textual timestamps, Auto-Times can utilize chronological information to align multivariate time series. Empirically, AutoTimes achieves state-of-the-art with 0.1% trainable parameters and over 5× training/inference speedup compared to advanced LLM-based forecasters.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Time series forecasting is of crucial demand in real-world applications, covering various domains including climate, economics, energy, operations, etc. [22, 43] . The growing challenges of generalpurpose forecasting, where one model is versatile to handle variable-length scenarios [24, 41] and the prediction is necessarily instructed by auxiliary information in other modalities [40, 44] , underscore the demand for foundation models [3] of time series, which are aimed to exhibit enhanced capabilities, including multi-step generation, zero-shot generalization [49, 13] , in-context learning and multimodal utilization [15] , thereby expanding the scope of time series forecasting to a wider range of situations. Nevertheless, the development of time series foundation models has been hampered by the limited availability of large-scale pre-training datasets and the technical uncertainty of scalable backbones. In contrast, rapid progress is witnessed in large language models (LLM), facilitated by extensive text corpora [50] , available pre-trained models [36] , and well-established adaptation techniques [14] . Notably, language and time series share basic commonalities in sequence modeling and generation by learned token transitions, presenting opportunities to adopt off-the-shelf LLMs for time series.", "section": "Introduction", "sec_num": "1"}, {"text": "Figure 1: (a) Prevalent LLM4TS methods non-autoregressively generate predictions with the globally flattened representation of lookback series, while large language models inherently predict the next tokens by autoregression [47] . (b) Previous methods adopt language prompts that may lead to the modality disparity, while we find time series can be self-prompted, termed in-context forecasting.", "section": "Introduction", "sec_num": "1"}, {"text": "Despite recent studies on large language models for time series (LLM4TS) achieving performance breakthroughs in current forecasting benchmarks [15] , the mechanism by which LLMs are aligned to the time series modality still remains obscure. The pilot work, FPT [49] leverages LLMs as generic sequential representation extractors for time series, influencing subsequent LLM4TS methodologies. As depicted in Figure 1 (a), the non-autoregressive approach, where time series are segmented into tokens, flattens and projects all lookback tokens for the prediction in a single step. However, it causes inconsistencies in both model structure and generative approach of LLMs: decoder-only models for autoregressive generation are converted to encoder-only and non-autoregressive forecasters.", "section": "Introduction", "sec_num": "1"}, {"text": "Given that prior studies [9, 38] reveal that generalization performance of LLMs is largely derived from the decoder-only structure trained autoregressively, talents of LLMs may not be fully exhibited. It is also supported by the recent rethinking of previous LLM4TS methods [35] , which generally lack the maintenance of autoregression, the essential characteristic of both large language models and statistical forecasters [5, 39] . Therefore, autoregressive LLM4TS methods are underexplored, which can potentially unlock multi-step generation like LLMs, presenting one model for arbitrary lengths.", "section": "Introduction", "sec_num": "1"}, {"text": "Motivated by the reflections, we propose AutoTimes to adapt LLMs as time series forecasters, which retrieves the consistency of autoregression with revitalized LLM capabilities to produce foundation models for time series forecasting. Technically, we independently embed time series segments into the latent space of language models by the consistent training objective: next token prediction [2] . To fully leverage the inherent token transitions of LLMs and reduce the training cost, we freeze the LLM and establish token embedding and projection for time series, which only account for up to 0.1% total parameters. The consequent forecaster adopts autoregressive inference like LLMs, which is no longer constrained to specific lookback/forecast lengths. Going beyond conventional time series forecasting, we propose in-context forecasting as shown in Figure 1 , where time series can be self-prompted by relevant contexts. We further adopt LLM-embedded timestamps as the position embedding to utilize chronological information and align multiple variates. Our contributions are summarized as follows:", "section": "Introduction", "sec_num": "1"}, {"text": "• By refining the inconsistency of non-autoregressive LLM4TS methods, we propose to inherit the autoregressive property of LLMs, which frees our method from training respectively on the lookback length and allows arbitrary-length predictions with chronological awareness.", "section": "Introduction", "sec_num": "1"}, {"text": "• We present AutoTimes, a simple but effective approach to acquire LLM-based forecasters by lightweight adaptation, which utilizes the inherent token transition as the future extrapolation of time series. Further, we propose in-context forecasting, which renovates the conventional paradigm by introducing relevant time series prompts to enhance forecasting.", "section": "Introduction", "sec_num": "1"}, {"text": "• Compared with state-of-the-art methods, our repurposed forecaster achieves superior performance while saving over 80% training and inference time, and further exhibits zero-shot generalizability, in-context forecasting, and scaling behavior empowered by LLMs.", "section": "Introduction", "sec_num": "1"}, {"text": "2 Related Work", "section": "Introduction", "sec_num": "1"}, {"text": "Autoregression is an essential concept of both language modeling and time series forecasting. Despite prevalent deep forecasters [10, 26, 42, 48] adopt a non-autoregressive approach without the requirement of iterative forecasting, autoregression, the absent exploration in deep forecasters, serves as the fundamental principle of statistical methods, which enables variable-length predictions. The most well-known model, ARIMA [4] is developed by incorporating differencing on AR and MA models, which are both autoregressive models with learned time-invariant transition from the past to the future. Incorporated with decomposition and pre-defined transitions, exponential smoothing [39] and state space models (SSM) [12, 23] also take the same autoregressive formulation.", "section": "Autoregressive Models", "sec_num": "2.1"}, {"text": "Autoregressive language models [27, 31] are trained with fine-grained supervision, where the generated token of each position is independently supervised. Consequently, they are not constrained by specific input/output lengths and excel at multi-step generation. Furthermore, existing LLMs are inherently autoregressive models [47] , which demonstrate advanced abilities that are not present in small models, such as the generalization [38] , scalability [6] , and task generality [31, 32] . Therefore, it is imperative to adapt off-the-shelf LLMs as autoregressive forecasters, which keeps the consistency to fully revitalize the model capacity and general-purpose token transitions.", "section": "Autoregressive Models", "sec_num": "2.1"}, {"text": "With the immense advancement of large language model infrastructure, LLM4TS methods have been experiencing significant development in recent years. PromptCast [44] reformulates time series as text pairs and accomplishes forecasting as a sentence-to-sentence task. LLMTime [13] regards time series as numerical tokens, demonstrating the zero-shot generalizability in time series forecasting. FPT [49] fine-tunes parameters of the LLM to adapt it as a general representation extractor serving for multiple time series analysis tasks. UniTime [21] adapts a language model across diverse time series for a unified forecaster of multiple domains. Based on thriving prompting techniques, deft language prompts [15, 21] and soft prompting [7] for time series are further investigated.", "section": "Large Language Models for Time Series", "sec_num": "2.2"}, {"text": "LLM4TS methods have achieved performance breakthroughs in time series forecasting, but the cost of training and inference can sometimes be resource-consuming due to the immensity of LLMs. Recent revisiting of LLM4TS methods has revealed the inefficacy of LLMs adapted in the non-autoregressive approach [35] . By contrast, AutoTimes frozen LLMs, transfers the general-purpose token transition, and introduces minimal parameters to realize autoregressive next token prediction, thereby achieving better model efficiency and consistent utilization of large models. We further provide Table 1 that categorizes prevalent LLM4TS methods by several essential aspects.", "section": "Large Language Models for Time Series", "sec_num": "2.2"}, {"text": "Multimodal models have been well-developed upon LLMs, among which vision language models (VLM) have experienced rapid growth [1, 27] . The booming pre-trained vision backbones [11, 30] , together with the instruction tuning paradigm, has revealed the potential of LLMs for vision tasks, where visual tokens and language tokens are concatenated as the input of the LLM [19, 20] . Inspired by this, previous LLM4TS methods utilize instructive language tokens as prefix-prompts for time series analysis [15, 34, 44] . Unlike previous works, our proposed method regards time series itself as the instructive prompt. It avoids the modality gap caused by concatenating time series and language tokens directly. We incorporate chronological information, the textual timestamp of time series, such that the language model can effectively perceive date and periodicity as the position embedding, and align simultaneous events from different time series [22] for multivariate forecasting. ", "section": "Multimodal Language Models", "sec_num": "2.3"}, {"text": "AutoTimes TimeLLM [15] UniTime [21] FPT [49] LLMTime [13] TEST [34] TEMPO [7] PromptCast [44] Autoregressive", "section": "Method", "sec_num": null}, {"text": "✓ ✗ ✗ ✗ ✓ ✗ ✗ ✗ Freeze LLM ✓ ✓ ✗ ✗ ✓ ✓ ✗ ✓ Multimodal ✓ ✓ ✓ ✗ ✗ ✓ ✓ ✓", "section": "Method", "sec_num": null}, {"text": "The proposed AutoTimes adapts large language models for multivariate time series forecasting. is to predict the future F time steps x L+1:L+F = {x L+1 , . . . , x L+F } ∈ R F ×C . Besides, the textual timestamp a t (e.g. 2016/07/05 00:00:00), as the most common covariate, is adopted for prediction, which is aligned with time points x t ∈ R C at time t. The task is to train an LLM-based forecaster f that is able to predict with the (varying) lookback length L for the (arbitrary) forecast length F as:", "section": "Method", "sec_num": "3"}, {"text": "f : (x 1:L , a 1:L+F ) → xL+1:L+F .", "section": "Token-wise Alignment", "sec_num": null}, {"text": "(1)", "section": "Token-wise Alignment", "sec_num": null}, {"text": "Time series token To empower the forecaster with the capability to predict time series for arbitrary lengths, we repurpose autoregressive LLMs as time series forecasters as depicted in Figure 2 . Prior to this, we define time series token as the consecutive and non-overlapping segment of a single variate. It is regarded as the common token of the LLM-based forecaster, which encompasses series variations and mitigates excessively long autoregression. To focus on modeling temporal variations, our forecaster predicts each variate independently. Beyond Channel Independence [26] that implicitly captures the multivariate correlation [22] by shared parameters, AutoTimes converts timestamps into position embeddings and explicitly aligns simultaneous segment tokens, which is detailed in the next paragraph. Therefore, we simplify x t as the time point of specific variate x t ∈ R. Given a single-variate time series of context length N S, the i-th segment of length S is denoted as:", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "EQUATION", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "Considering the general-purpose token transition, we freeze the parameters of large language models. To realize the token-wise alignment between time series tokens and language tokens, we establish SegmentEmbedding(•) : R S → R D that independently embeds segments into the latent space:", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "EQUATION", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "where D is consistent with the dimension of the LLM.", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "Position embedding Timestamp, an essential covariate indicating the chronological information, is generally utilized as an extra embedding in previous deep forecasters [43, 48] . However, increasing models [10, 26, 45] have discarded the embedding and found the performance will not be greatly affected, implying the improper encoding of timestamps. In contrast, textual timestamps have been demonstrated as an enhancement in LLM4TS methods, which are always formulated into prefixprompts [15, 21] . Nevertheless, it also leads to excessive context length, impeding LLMs from paying sufficient attention to time series tokens and inducing time-consuming feed-forwarding. Inspired by the functionality of position embedding, which incorporates information about the relative or absolute position of the tokens [37] . We adopt LLM-embedded timestamps as position embeddings to utilize temporal information and align simultaneous events (segments) from different varieties.", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "Technically, we formulate the starting and end timestamps of corresponding segments by the template demonstrated in Figure 3 . Experimentally, we observe that the simple template without deft design can consistently boost the forecasting performance in Appendix D.5, aiding the LLM-based forecaster to comprehend the date and align different variates based on Channel Independence. Since all the previous language tokens are visible to the special ending token <EOS> of a sentence, we adopt the embedding of <EOS> as TE i ∈ R D as the position embedding from textual timestamps:", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "EQUATION", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "Notably, TE i is pre-computed by LLMs such that runtime forwarding for language tokens is not required during training. Given that the latent space of the LLM locates both time series tokens and ", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "D C E 1 f F w U = \" > A A A C z 3 i c j V H L S s N A F D 2 N r 1 p f V Z d u g k V w V R I R d V l 0 4 7 I F + 4 B W S p J O 2 6 F 5 k U y U U i p u / Q G 3 + l f i H + h f e G e c g l p E J y Q 5 c + 4 9 Z + b e 6 8 Y + T 4 V l v e a M h c W l 5 Z X 8 a m F t f W N z q 7 i 9 0 0 i j L P F Y 3 Y v 8 K G m 5 T s p 8 H r K 6 4 M J n r T h h T u D 6 r O m O L m S 8 e c O S l E f h l R j H 7 D p w B i H v c 8 8 R R H U 6 g S O G b n + S T r t H 3 W L J K l t q m f P A 1 q A E v a p R 8 Q U d 9 B D B Q 4 Y A D C E E Y R 8 O U n r a s G E h J u 4 a E + I S Q l z F G a Y o k D a j L E Y Z D r E j + g 5 o 1 9 Z s S H v p m S q 1 R 6 f 4 9 C a k N H F A m o j y E s L y N F P F M + U s 2 d + 8 J 8 p T 3 m 1 M f 1 d 7 B c Q K D I n 9 S z f L / K 9 O 1 i L Q x 5 m q g V N N s W J k d Z 5 2 y V R X 5 M 3 N L 1 U J c o i J k 7 h H 8 Y S w p 5 S z P p t K k 6 r a Z W 8 d F X 9 T m Z K V e 0 / n Z n i X t 6 Q B 2 z / H O Q 8 a R 2 X 7 p G z X j k u V c z 3 q P P a w j 0 O a 5 y k q u E Q V d f K O 8 Y", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "I = \" > A A A C 0 H i c j V H L S s N A F D 2 N r 1 p f V Z d u g k V w V R I R d V k U w W W V v q A t J U m n b W h e T i Z i K U X c + g N u", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "> A A A C 0 H i c j V H L S s N A F D 2 N r 1 p f V Z d u g k V w V R I R d V k U w W W V v q A t J Z l O 2 9 C 8 T C Z i K U X c + g N u", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "V E i s x J P Y v 3 S z z v z p V i 0 Q f 5 7 o G n 2 p K N K O q 8 z K X V H d F 3 d z 8 U p U k h 4 Q 4 h X s U 5 4 Q 9 r Z z 1 2 d Q a o W t X v X V 0 / E 1 n K l b t v S w 3 x b u 6 J Q 3 Y / j n O e V A / L t m n J b t 6 U i x f Z K P O Y R 8 H O K J 5 n q G M K 1 R Q I + 8 E j 3 j C s 1 E 1 b o 0 7 4 / 4 z 1 V j I N H v 4 t o y H D 2 j V l E I = < / l a t e x i t > s 1 < l a t e x i t s h a 1 _ b a s e 6 4 = \" o M 0 h A l y + w S 6 f s K / k A O c + O 0 U 3 X x 0 = \" > A A A C 0 H i c j V H L S s N A F D 2 N r 1 p f V Z d u g k V w V R I R d V k U w W V 9 9 A F t K c l 0 2 o b m Z T I R S y n i 1 h 9 w q 1 8 l / o H + h X f G F N Q i O i H J m X P v O T P 3 X j t 0 n V g Y x m t G m 5 m d m 1 / I L u a W l l d W 1 / L r G 9 U 4 S C L G K y x w g 6 h u W z F 3 H Z 9 X h C N c X g 8 j b n m 2 y 2 v 2 4 E T G a z c 8 i p 3 A v x L D k L c 8 q + c 7 X Y d Z g q h W 0 7 N E 3 + 6 O L k / H b b O d L x h F Q y 1 9 G p g p K C B d 5 S D / g i Y 6 C M C Q w A O H D 0 H Y h Y W Y n g Z M G A i J a 2 F E X E T I U X G O M X K k T S i L U 4 Z F 7 I C + P d o 1 U t a n v f S M l Z r R K S 6 9 E S l 1 7 J A m o L y I s D x N V / F E O U v 2 N + + R 8 p R 3 G 9 L f T r 0 8 Y g X 6 x P 6 l m 2 T + V y d r E e j i S N X g U E 2 h Y m R 1 L H V J V F f k z f U v V Q l y C I m T u E P x i D B T y k m f d a W J V e 2 y t 5 a K v 6 l M y c o 9 S 3 M T v M t b 0 o D N n + O c B t W 9 o n l Q N M / 3 C 6 X j d N R Z b G E b u z T P Q 5 R w h j I q 5 H 2 N R z z h W b v Q b r U 7 7 f 4 z V c u k m k 1 8 W 9 r D B + q W l H E = < / l a t e x i t > SE1 < l a t e x i t s h a 1 _ b a s e 6 4 = \" m Q H C W 7 p u i H + N h n B w y k f e d i b + V a A = \" > A A A C 1 3 i c j V H L S s N A F D 2 N r 1 p f t S 7 d B I v g q i Q i 6 r I o g s s K 9 i F t K Z N 0 2 o b m R T I R S y n u x K 0 / 4 F b / S P w D / Q v v j C m o R X R C k j P n 3 n N m 7 r 1 W 6 D q x M I z X j D Y 3 v 7 C 4 l F 3 O r a y u r W / k N w u 1 O E g i m 1 f t w A 2 i h s V i 7 j o + r w p H u L w R R p x 5 l s v r 1 v B U x u v X P I q d w L 8 U o 5 C 3 P d b 3 n Z 5 j M 0 F U J 1 9 o D Z g Y t z w m B l Z v f D a Z d P Y 7 + a J R M t T S Z 4 G Z g i L S V Q n y L 2 i h i w A 2 E n j g 8 C E I u 2 C I 6 W n C h I G Q u D b G x E W E H B X n m C B H 2 o S y O G U w Y o f 0 7 d O u m b I + 7 a V n r N Q 2 n e L S G 5 F S x y 5 p A s q L C M v T d B V P l L N k f / M e K 0 9 5 t x H 9 r d T L I 1 Z g Q O x f u m n m f 3 W y F o E e j l U N D t U U K k Z W Z 6 c u i e q K v L n + p S p B D i F x E n c p H h G 2 l X L a Z 1 1 p Y l W 7 7 C 1 T 8 T e V K V m 5 t 9 P c B O / y l j R g 8 + c 4 Z 0 F t v 2 Q e l s y L g 2 L 5 J B 1 1 F t v Y w R 7 N 8 w h l n K O C K n n f 4 B F P e N a u t F v t T r v / T N U y q W Y L 3 5 b 2 8 A G v 8 5 c T < / l a t e x i t >", "section": "Modality Alignment", "sec_num": "3.1"}, {"text": "< l a t e x i t s h a 1 _ b a s e 6 4 = \" w 7 5 t e P b p w W q y g Q + g j P 4 J N 0 t", "section": "Ê2", "sec_num": null}, {"text": "5 o z M = \" > A A A C 1 3 i c j V H L S s N A F D 2 N r 1 p f t S 7 d B I v g q i Q q 6 r I o g s s K 9 i F t K Z N 0 2 o b m R T I R S y n u x K 0 / 4 F b / S P w D / Q v v j B H U I j o h y Z l z 7 z k z 9 1 4 r d J 1 Y G M Z L R p u Z n Z t f y C 7 m l p Z X V t f y 6 4 V a H C S R z a t 2 4 A Z R w 2 I x d x 2 f V 4 U j X N 4 I I 8 4 8 y + V 1 a 3 g i 4 / U r H s V O 4 F + I U c j b H u v 7 T s + x m S C q k y + 0 B k y M W x 4 T A 6 s 3 P p 1 M O n u d f N E o G W r p 0 8 B M Q R H p q g T 5 Z 7 T Q R Q A b C T x w + B C E X T D E 9 D R h w k B I X B t j 4 i J C j o p z T J A j b U J Z n D I Y s U P 6 9 m n X T F m f 9 t I z V m q b T n H p j U i p Y 5 s 0 A e V F h O V p u o o n y l m y v 3 m P l a e 8 2 4 j + V u r l E S s w I P Y v 3 W f m f 3 W y F o E e j l Q N D t U U K k Z W Z 6 c u i e q K v L n + p S p B D i F x E n c p H h G 2 l f K z z 7 r S x K p 2 2 V u m 4 q 8 q U 7 J y b 6 e 5 C d 7 k L W n A 5 s 9 x T o P a b s k 8 K J n n + 8 X y c T r q L D a x h R 2 a 5 y H K O E M F V f K + x g M e 8 a R d a j f a r X b 3 k a p l U s 0 G v i 3 t / h 2 y U 5 c U < / l a t e x i t > Ê3 < l a t e x i t s h a 1 _ b a s e 6 4 = \" 7 2 0 2 a Q 3 C l F 8 d 5 t F z Q F I I q b c Y k V A = \" > A A A C z 3 i c j V H L S s N A F D 3 G V 6 2 v q k s 3 w S K 4 K o m I d V l 0 4 7 I F + 4 B W S p J O 2 6 F 5 M Z k o p V T c + g N u 9 a / E P 9 C / 8 M 6 Y g l p E J y Q 5 c + 4 9 Z + b e 6 8 Y + T 6 R l v S 4 Y i 0 v L K 6 u 5 t f z 6 x u b W d m F n t 5 F E q f B Y 3 Y v 8 S L R c J 2 E + D 1 l d c u m z V i y Y E 7 g + a 7 q j C x V v 3 j C R 8 C i 8 k u O Y X Q f O I O R 9 7 j m S q E 4 n c O T Q 7 U + S a b f c L R S t k q W X O Q / s D B S R r W p U e E E H P U T w k C I A Q w h J 2 I e D h J 4 2 b F i I i b v G h D h B i O s 4 w x R 5 0 q a U x S j D I X Z E 3 w H t 2 h k b 0 l 5 5 J l r t 0 S k + v Y K U J g 5 J E 1 G e I K x O M 3 U 8 1 c 6 K / c 1 7 o j 3 V 3 c b 0 d z O v g F i J I b F / 6 W a Z / 9 W p W i T 6 O N M 1 c K o p 1 o y q z s t c U t 0 V d X P z S 1 W S H G L i F O 5 R X B D 2 t H L W Z 1 N r E l 2 7 6 q 2 j 4 2 8 6 U 7 F q 7 2 W 5 K d 7 V L W n A 9 s 9 x z o P G c c k + L d m 1 k 2 L l P B t 1 D v s 4 w B H N s 4 w K L l F F n b x j P O I J z 0 b N u D X u j P v P V G M h 0 + z h 2 z I e P g B 3 F Z R I < / l a t e x i t > s 7 < l a t e x i t s h a 1 _ b a s e 6 4 = \" l x 3 i V r f H e C o a t 8 l L f K n k H Z H u h X A = \" > A A A C 0 H i c j V H L S s N A F D 2 N r / q u u n Q T L I K r k o h Y l 0 U R X F b p C 9 p S k u m 0 D c 3 L Z C K W U s S t P + B W v 0 r 8 A / 0 L 7 4 w p q E V 0 Q p I z 5 9 5 z Z u 6 9 d u g 6 s T C M 1 4 w 2 N 7 + w u J R d X l l d W 9 / Y z G 1 t 1 + I g i R i v s s A N o o Z t x d x 1 f F 4 V j n B 5 I 4 y 4 5 d k u r 9 v D M x m v 3 / A o d g K / I k Y h b 3 t W 3 3 d 6 D r M E U e 2 W Z 4 m B 3 R t X z i e d Y i e X N w q G W v o s M F O Q R 7 r K Q e 4 F L X Q R g C G B B w 4 f g r A L C z E 9 T Z g w E B L X x p i 4 i J C j 4 h w T r J A 2 o S x O G R a x Q / r 2 a d d M W Z / 2 0 j N W a k a n u P R G p N S x T 5 q A 8 i L C 8 j R d x R P l L N n f v M f K U 9 5 t R H 8 7 9 f K I F R g Q + 5 d u m v l f n a x F o I c T V Y N D N Y W K k d W x 1 C V R X Z E 3 1 7 9 U J c g h J E 7 i L s U j w k w p p 3 3 W l S Z W t c v e W i r + p j I l K / c s z U 3 w L m 9 J A z Z / j n M W 1 A 4 L 5 n H B v D z K l 0 7 T U W e x i z 0 c 0 D y L K O E C Z V T J + x q P e M K z d q X d a n f a / W e q l k k 1 O / i 2 t I c P + z q U e A = = < / l a t e x i t >", "section": "Ê2", "sec_num": null}, {"text": "TE7 < l a t e x i t s h a 1 _ b a s e 6 4 = \" F U L 8 h e B y B P g language tokens, the position embedding can be integrated with the corresponding time span without increasing the context length. Concretely, the token embedding E i ∈ R D is obtained by:", "section": "Ê2", "sec_num": null}, {"text": "1 A k D U g E M t D 8 6 w v g E = \" > A A A C 0 H i c j V H L S s N A F D 2 N r 1 p f V Z d u g k V w V R I R 6 7 I o g s v 6 6 A N q K c l 0 2 o b m Z T I R S y n i 1 h 9 w q 1 8 l / o H + h X f G F N Q i O i H J m X P v O T P 3 X j t 0 n V g Y x m t G m 5 m d m 1 / I L u a W l l d W 1 / L r G 7 U 4 S C L G q y x w g 6 h h W z F 3 H Z 9 X h S N c 3 g g j b n m 2 y + v 2 4 F j G 6 z c 8 i p 3 A v x T D k L c 8 q + c 7 X Y d Z g q j W l W e J v t 0 d X Z y M 2 6 V 2 v m A U D b X 0 a W C m o I B 0 V Y L 8 C 6 7 Q Q Q C G B B 4 4 f A j C L i z E 9 D R h w k B I X A s j 4 i J C j o p z j J E j b U J Z n D I s Y g f 0 7 d G u m b I + 7 a V n r N S M T n H p j U i p Y 4 c 0 A e V F h O V p u o o n y l m y v 3 m P l K e 8 2 5 D + d u r l E S v Q J / Y v 3 S T z v z p Z i 0 A X h 6 o G h 2 o K F S O r Y 6 l L o r o i b 6 5 / q U q Q Q 0 i c x B 2 K R 4 S Z U k 7 6 r C t N r G q X v b V U / E 1 l S l b u W Z q b 4 F 3 e k g Z s / h z n N K j t F c 2 D o n m 2 X y g f p a P O Y g v b 2 K V 5 l l D G K S q o k v c 1 H v G E Z + 1 c u 9 X u t P v P V C 2 T a j b x b W k P H / j W l H c = < / l a t e x i t > SE7 < l a t e x i t s h a 1 _ b a s e 6 4 = \" s 6 f s G r P Y C m E w S 1 d W E + e b e x k f j g c = \" > A A A C 1 3 i c j V H L S s N A F D 3 G V 6 2 v W p d u g k V w V R I R 7 b I o g s s K 9 i F t K Z N 0 2 o b m R T I R S y n u x K 0 / 4 F b / S P w D / Q v v j C m o R X R C k j P n 3 n N m 7 r 1 W 6 D q x M I z X O W 1 + Y X F p O b O S X V 1 b 3 9 j M b e V r c Z B E N q / a g R t E D Y v F 3 H V 8 X h W O c H k j j D j z L J f X r e G p j N e v e R Q 7 g X 8 p R i F v e 6 z v O z 3 H Z o K o T i 7 f G j A x b n l M D K z e + G w y 6 Z Q 6 u Y J R N N T S Z 4 G Z g g L S V Q l y L 2 i h i w A 2 E n j g 8 C E I u 2 C I 6 W n C h I G Q u D b G x E W E H B X n m C B L 2 o S y O G U w Y o f 0 7 d O u m b I + 7 a V n r N Q 2 n e L S G 5 F S x x 5 p A s q L C M v T d B V P l L N k f / M e K 0 9 5 t x H 9 r d T L I 1 Z g Q O x f u m n m f 3 W y F o E e S q o G h 2 o K F S O r s 1 O X R H V F 3 l z / U p U", "section": "Ê2", "sec_num": null}, {"text": "Q I Q f C S O + F G l F b i m 2 f y L D U R I m k = \" > A A A C 1 3 i c j V H L S s N A F D 3 G V 6 2 v W p d u g k V w V R I R 7 b L o x m V F + 5 C 2 l E k 6 b U P z I p m I p R R 3 4 t Y f c K t / J P 6 B / o V 3 x h T U I j o h y Z l z 7 z k z 9 1 4 r d J 1 Y G M b r n D a / s L i 0 n F n J r q 6 t b 2 z m t v K 1 O E g i m 1 f t w A 2 i h s V i 7 j o + r w p H u L w R R p x 5 l s v r 1 v B U x u v X P I q d w L 8 U o 5 C 3 P d b 3 n Z 5 j M 0 F U J 5 d v D Z g Y t z w m B l Z v f D G Z d E q d X M E o G m r p s 8 B M Q Q H p q g S 5 F 7 T Q R Q A b C T x w + B C E X T D E 9 D R", "section": "Ê2", "sec_num": null}, {"text": "A P J V W D Q z W F i p H V 2 a l L o r o i b 6 5 / q U q Q Q 0 i c x F 2 K R 4 R t p Z z 2 W V", "section": "Ê2", "sec_num": null}, {"text": "EQUATION", "section": "Ê2", "sec_num": null}, {"text": "As shown in Figure 3 , prevalent LLMs [6, 36] are endowed with the capability of predicting the next token s i based on the preceding tokens s <i . We reutilize LLMs in a fully consistent approach and generate prediction of arbitrary lengths iteratively. Given a time series of context length N S, the input series is segmented and embedded into N token embeddings {E 1 , . . . , E N }. The training objective is to independently generate the next tokens {ŝ 2 , . . . , ŝN+1 }. We feed the token embeddings E i into the intermediate layers of the LLM, which inherently parameterize token transitions:", "section": "Next Token Prediction", "sec_num": "3.2"}, {"text": "EQUATION", "section": "Next Token Prediction", "sec_num": "3.2"}, {"text": "We adopt SegmentProjection(•) : R D → R S to independently projects embeddings to segments:", "section": "Next Token Prediction", "sec_num": "3.2"}, {"text": "EQUATION", "section": "Next Token Prediction", "sec_num": "3.2"}, {"text": "Finally, each predicted segment is supervised by the token-wise ground truth to optimize the parameters of embedding and projection layers, which are simply implemented by multi-layer perceptrons:", "section": "Next Token Prediction", "sec_num": "3.2"}, {"text": "EQUATION", "section": "Next Token Prediction", "sec_num": "3.2"}, {"text": "Notably, the context length N S is decided during training, representing the maximum input length during inference. Therefore, one consequent forecaster is suitable for different input lengths like the LLM, validated in Appendix D.4. Moreover, AutoTimes can generate predictions of arbitrary lengths by iterative multi-step forecasting, proven to overcome error accumulation better than state-of-the-art forecasters in Section 4.1, since autoregressive LLMs inherently excel at multi-step generation:", "section": "Next Token Prediction", "sec_num": "3.2"}, {"text": "EQUATION", "section": "Next Token Prediction", "sec_num": "3.2"}, {"text": "Instead of respectively training models on different lookback/forecast lengths, AutoTimes handles all the scenarios by one model. Surprisingly, with the consistency of autoregression, it also inherits notable generalizability and scaling behavior of LLMs, which is demonstrated in Sections 4.2 and 4.4.", "section": "Next Token Prediction", "sec_num": "3.2"}, {"text": "Large language models are capable of generating expected outputs based on provided task demonstrations from downstream datasets without gradient updating, known as the in-context learning ability. The task demonstrations are generally constituted by paired questions and answers [47] . Formally, the context C = {g(x (1) , y (1) ), . . . , g(x (m) , y (m) )} represents a set of demonstrations with m pairs, where g(•) is the template that transforms each question and answer into natural language.", "section": "In-Context Forecasting", "sec_num": "3.3"}, {"text": "In terms of time series forecasting, we propose to constitute the pair by lookback-forecast windows, which are exactly represented as successive time points from earlier historical observations. Hence, we use time series in target datasets as prompts, extending the context for prediction beyond consecutive lookback series. We denote the extended context as C, which contains m time series prompts tsp (j) :", "section": "In-Context Forecasting", "sec_num": "3.3"}, {"text": "EQUATION", "section": "In-Context Forecasting", "sec_num": "3.3"}, {"text": "During training, we first obtain an LLM-based forecaster on a source dataset and select time series prompts from the downstream target dataset based on a unified strategy. During inference, we ensure all the prompts appear before the window to be predicted, such that there is no data leakage from future information. As shown in Figure 4 , we concatenate time series prompts with lookback series and feed them as the context of the forecaster, termed in-context forecasting:", "section": "In-Context Forecasting", "sec_num": "3.3"}, {"text": "EQUATION", "section": "In-Context Forecasting", "sec_num": "3.3"}, {"text": ")", "section": "In-Context Forecasting", "sec_num": "3.3"}, {"text": "We conduct thorough evaluations of the performance of AutoTimes, including time series forecasting, zero-shot forecasting, and the proposed in-context forecasting. Additional analyses are included to evaluate the generality, scaling behavior, and adaptation cost of large language models. Detailed code implementation for reproduction is provided in our public code repository.", "section": "Experiments", "sec_num": "4"}, {"text": "Benchmarks For long-term time series forecasting, we extensively include real-world datasets, including ETTh1, ECL, Traffic, Weather [43] , and Solar-Energy [22] . For short-term forecasting, we adopt the well-acknowledged M4 competition [25] . Detailed descriptions are provided in Appendix A.", "section": "Time Series Forecasting", "sec_num": "4.1"}, {"text": "Baselines We compare AutoTimes with state-of-the-art models, including advanced LLM4TS methods: TimeLLM [15] , UniTime [21] , and FPT [49] ; well-acknowledged deep forecasters: iTransformer [22] , DLinear [45] , PatchTST [26] , and TimesNet [42] . For the challenging short-term forecasting, we further include competitive baselines: Koopa [23] , N-HiTS [8] and N-BEATS [28] . All baselines are officially implemented or reported. We adopt LLaMA-7B [36] as our base LLM. Detailed implementations, error bars, and hyperparameter analysis are provided in Appendix B and C.", "section": "Time Series Forecasting", "sec_num": "4.1"}, {"text": "Setups For short-term forecasting, we follow the well-acknowledged TimesNet [42] , which assesses the fundamental ability of forecasters in modeling temporal variations. For long-term forecasting, we establish a novel one-for-all benchmark: a single forecaster is trained on one dataset and subsequently utilized for all prediction lengths. We highlight that this approach evaluates the basic versatility as foundation models of time series, which aims to break the prevailing practice of extensive training across diverse real-world scenarios. To be specific, we evaluate all methods by rolling forecasting: a model is trained with predetermined input/output lengths, and the predicted values are integrated as part of the input in subsequent iterations until reaching the desired forecast length. Therefore, the key to success in this task lies in mitigating multi-step error accumulation. Still, the conventional one-for-one approach that trains forecasters respectively on each length is also provided in Table 12 .", "section": "Time Series Forecasting", "sec_num": "4.1"}, {"text": "The average results are presented in 10 .", "section": "Results", "sec_num": null}, {"text": "Models AutoTimes TimeLLM [15] UniTime [21] FPT [49] iTrans. [22] DLinear [45] PatchTST [26] TimesNet [42] Metric By diving into the proposed one-for-all and the traditional one-for-one benchmarks in Table 3 and 12, it is notable that prevalent deep forecasters, such as Transformer-based forecasters and DLinear, can achieve competitive and even better results under rolling forecasting. Nevertheless, the performance of non-autoregressive LLM4TS methods can degenerate a lot without respective training. Therefore, it highlights our persistent utilization of autoregression and thorough leveraging of inherent token transitions of LLMs, thereby mitigating error accumulation during multi-step rolling forecasting.", "section": "Results", "sec_num": null}, {"text": "Setups Large language models have exhibited remarkable zero-shot generalization capability [6] .", "section": "Zero-Shot Forecasting", "sec_num": "4.2"}, {"text": "To verify whether our LLM-based forecaster inherits this ability, where no training sample of the target domain is available, we assess the performance of zero-shot forecasting. Concretely, we adhere to the benchmark established by FPT [49] , where the forecaster is initially trained on a source domain and subsequently evaluated on an unseen target domain. We conduct the transfer learning between the M3 and M4 competitions, both of which encompass abundant temporal variation patterns but follow different data distributions. We compare AutoTimes with deep forecasters and FPT as the only LLM4TS method, given that only FPT has exhibited zero-shot generalization in this benchmark. ", "section": "Zero-Shot Forecasting", "sec_num": "4.2"}, {"text": "Setups We conduct in-context forecasting on AutoTimes, which is depicted in Figure 4 . Similar to zero-shot forecasting, the task is to apply a forecaster, trained on a source dataset, to an unseen target dataset. Additionally, several task demonstrations from the target domain, referred to as time series prompts in Equation 10, are available during inference. Specifically, we concatenate these prompts with the lookback window to form the context for prediction.", "section": "In-Context Forecasting", "sec_num": "4.3"}, {"text": "We adopt the aforementioned M4 → M3 scenario. Since the samples of the M3 dataset are univariate time series with different lengths, we always predict the last F time points of each sample during inference. We set the lookback length L = F , and thus the length of time series prompts is 2F . We set the number of prompts m = 1. Therefore, we initially train an LLM-based forecaster on the source M4 dataset with the context length of 3F . We adopt an intuitive strategy to select the prompt: uniformly adopting the first 2F time points of the time series as the corresponding prompt. Supposing the lookback series starts after time t (≥ F ), in-context forecasting is formulated as:", "section": "In-Context Forecasting", "sec_num": "4.3"}, {"text": "EQUATION", "section": "In-Context Forecasting", "sec_num": "4.3"}, {"text": "To prevent data leakage of future information, too short samples are discarded to prevent the overlap between the prompt and the future prediction. The implementation details of in-context forecasting are provided in Appendix D.6. We further investigate different prompt retrieval strategies. Insightful results are provided to reveal the influence of using time series prompts for interactive prediction and take-away instructions of prompt engineering in the time series modality.", "section": "In-Context Forecasting", "sec_num": "4.3"}, {"text": "The quantitative results of in-context forecasting are provided on the right of Figure 4 . The results of zero-shot forecasting, where no downstream demonstration is available, are compared as the baseline. Benefiting from the time series prompts of the target domain, our LLM-based forecaster with the proposed in-context forecasting paradigm achieves consistent promotions on all M3 subsets and the averaged 13.3% SMAPE reduction compared with zero-shot forecasting. In contrast to previous LLM4TS methods that rely on deft language prompts, LLMs adopted by AutoTimes can be instructed by time series itself with our intuitive prompting engineering. From the perspective of the forecasting paradigm, we extend the prediction context beyond the lookback window. To inherit token transitions of language models parameterized by intermediate layers, AutoTimes takes a crucial step by establishing a mapping between time series segments and the latent space of language tokens, which is however absent in non-autoregressive LLM4TS methods. Therefore, ensuring autoregression consistency enhances the effective utilization of LLMs as foundation models.", "section": "Results", "sec_num": null}, {"text": "Generality Previous LLM4TS [15, 49] methods focus on applying their approach to specific LLMs. We demonstrate that AutoTimes is compatible with any decoder-only LLMs. By extensively training LLM-based forecasters by AutoTimes based on prevalent LLMs, including GPT-2 [31] , OPT [46] , and LLaMA [36] , we present the results in Table 5 , highlighting the generality of AutoTimes. Scaling behavior Scalability is an essential characteristic that emerges from small models to large foundation models. By investigating the results presented in Table 5 , we observe that the prediction accuracy of the forecaster generally improves with the increase in LLM parameters. This scaling behavior of LLM-based forecasters introduces a trade-off between performance and adaptation cost.", "section": "Method Analysis", "sec_num": "4.4"}, {"text": "To provide a comprehensive assessment, we evaluate each adapted forecaster from three perspectives: performance, training speed, and parameters, as presented in Figure 5 . We observe that the largest LLaMA-7B consistently delivers optimal forecasting performance. As a relatively small language model, OPT-1.3B exhibits good parameter efficiency as an out-of-the-box forecaster. 5 .", "section": "Method Analysis", "sec_num": "4.4"}, {"text": "Adaptation cost To mitigate the substantial cost of adapting large language models, AutoTimes introduces minimal parameters with all intermediate layers of LLM frozen. Additionally, we seamlessly integrate the language tokens (e.g. textual timestamps) without excessive context length and runtime overhead for training, thereby significantly reducing the adaptation cost. Figure 6 presents a comprehensive efficiency analysis with advanced LLM4TS methods: FPT is applicable on GPT-2 and TimeLLM is applicable on LLaMA-7B. Not only does AutoTime achieve better results in Table 3 , but its training and reasoning time is also greatly reduced, bringing over 5× speedup on average. In terms of parameter efficiency, AutoTimes focuses on establishing the embedding for time series segments, which is simply implemented by the MLP (0.79M ) account for 0.1% parameters of the LLM (7B). Therefore, the results affirm the effectiveness of reutilizing the inherent token transition. Ablation study Recent research has raised doubts about the validity of previous LLM4TS methods [35] , which predominantly adopt non-autoregression, that is, treating the LLM as a BERT-style pre-trained backbone and utilize a globally flattening projector on all lookback tokens. Here, we provide a thorough ablation study to examine our proposed AutoTimes in Table 6 . The results underscore that our method maintains the consistency of the decoder-only architecture and autoregressive inference, effectively leveraging LLMs and addressing the concerns regarding performance improvement and adaptation cost. Further, we provide a comparison by substituting our token-wise segment projection (consistent with LLMs) with the flatten linear head [26] (common in non-autoregressive forecasters). Results of Table 21 in the Appendix reveal that the performance of non-autoregressive generation is consistently inferior to that of our autoregressive AutoTimes approach. LoRA adaptation By incorporating low-rank adaptation technique [14] on the intermediate LLM layers, the token transition of the large language model can be further fine-tuned to align the future extrapolation of time series. Table 7 provides the performance comparing the incorporation of LoRA, which consistently improves the performance of the LLM-based forecaster adapted by AutoTimes. ", "section": "Method Analysis", "sec_num": "4.4"}, {"text": "This paper aims to develop foundation models for time series forecasting. We utilize off-the-shelf LLMs as autoregressive forecasters by transferring the general-purpose and multi-step generation ability. Different from prior methods, we notice prevalent non-autoregressive LLM4TS methods may contradict the decoder-only structure and lead to insufficient utilization of LLMs. Experimentally, the proposed method achieves state-of-the-art performance with remarkable model efficiency. Further analysis reveals that our forecaster effectively inherits advanced capabilities such as zero-shot and in-context forecasting, and is able to utilize both instructive times series and timestamps. In the future, we will further incorporate advanced low-rank adaptation and utilize booming language backbones.", "section": "Conclusion", "sec_num": "5"}, {"text": "We conduct experiments to evaluate the performance of the proposed AutoTimes on seven real-world datasets spanning diverse domains: (1) ETTh1 [48] spans from July 2016 to July 2018 and consists of seven factors related to electricity transformers. (2) Weather [43] encompasses 21 meteorological factors collected every 10 minutes in 2020 from the Weather Station of the Max Planck Biogeochemistry Institute. (3) ECL [43] captures hourly electricity consumption data from 321 clients. ( 4) Traffic [43] gathers hourly road occupancy rates from 862 sensors on San Francisco Bay area freeways, covering the period from January 2015 to December 2016. ( 5) Solar-Energy [18] records solar power production from 137 PV plants in 2006, sampled every 10 minutes. ( 6) M4 is a competition dataset encompassing various time series across different frequencies and domains such as business and economics. (7) M3, albeit smaller than M4, also contains diverse time series from various domains.", "section": "A Dataset Descriptions", "sec_num": null}, {"text": "We follow the same data processing and train-validation-test set split protocol used in TimesNet [43] , where the train, validation, and test datasets are strictly divided according to chronological order to ensure no data leakage. As for long-term forecasting settings, we fix the context length of AutoTimes and the lookback length of other compared methods as 672 in ETT, ECL, Traffic, Weather, and Solar-Energy, and the forecast length varies in {96, 192, 336, 720}. For the short-term forecasting on M4 and M3 datasets, the input length is generally set to twice the output length according to the official implementation of TimesNet. The details are provided in Table 8 . ", "section": "A Dataset Descriptions", "sec_num": null}, {"text": "AutoTimes processes timestamps in textual form rather than numerical encoding, potentially enabling to handle other textual data such as news or logs. We utilize LLM to obtain embedding for the special token <EOS> to capture embedding for the entire sentence. Pseudo-code for this process is depicted in Algorithm 1. It is worth noting that in the context of multivariate time series forecasting, timestamps are shared across variates. Thus, timestamps can implicitly express relationships between variates even with channel independence. Further, assuming there are C variates since the number of timestamps is 1 C of the total time point count, these embeddings can be efficiently pre-computed by large language models. After obtaining embedding for the timestamps, we repurpose LLM for time series forecasting using Algorithm 2. At this stage, only the parameters of SegmentEmbedding and SegmentProjection are updated, while the parameters of LLMs remain entirely frozen. During inference, AutoTimes utilizes the last token generated as its prediction and then employs this output to create subsequent predictions autoregressively. This approach enables AutoTimes to predict sequences of variable lengths with just one model dynamically. Such capability is crucial in real-world application scenarios. The pseudo-code in Algorithm 3-4 illustrates this process.", "section": "B Implementation Details", "sec_num": null}, {"text": "All the experiments are conducted using PyTorch [29] on NVIDIA A100 GPUs. We employ <PERSON> [17] with an initial learning rate in {10 -3 , 5 × 10 -4 , 10 -4 } and MSE loss for model optimization. We adopt Channel Independence [26] for multivariate time series and utilize our position embeddings of timestamps to explicitly align them. The batch size is chosen from {256, 1024, 2048}, and we set the number of training epochs as 10.", "section": "B Implementation Details", "sec_num": null}, {"text": "As for SegmentEmbedding and SegmentProjection, we implement them by either a linear layer or MLP. Results of deep forecaster are based on the benchmark provided by the TimesNet [43] repository, which is fairly built on the same configurations provided by the original paper. LLM4TS methods [15, 21, 49] are implemented by their official and open-source repository. Unless otherwise specified, we use LLaMA-7B [36] as the default base LLM. We also present the standard deviation of AutoTimes forecasting performance with three random seeds in Table 9 , demonstrating that the performance of AutoTimes is stable. ", "section": "B Implementation Details", "sec_num": null}, {"text": "2: for s i = {x (i-1)S+1 , . . . , x iS } ▷ s i ∈ R S 3: for SE i = SegmentEmbedding(s i ) ▷ SE i ∈ R D 4: for E i = SE i + TE i ▷ E i ∈ R D 5: { Ê2 , . . . , ÊN+1 } = LLMLayers({E 1 , . . . , E N }) ▷ Êi ∈ R D", "section": "B Implementation Details", "sec_num": null}, {"text": "6: for i in {2, . . . , N + 1}: ", "section": "B Implementation Details", "sec_num": null}, {"text": "7: for ŝi = SegmentProjection( Êi ) ▷ ŝi ∈ R S 8: s N +1 = {x N S+1 , . . . , x N S+S } 9: L MSE = 1 N S ||s i -ŝi ||", "section": "B Implementation Details", "sec_num": null}, {"text": "2: for s i = {x (i-1)S+1 , . . . , x iS } ▷ s i ∈ R S 3: for SE i = SegmentEmbedding(s i ) ▷ SE i ∈ R D 4: for E i = SE i + TE i ▷ E i ∈ R D 5: { Ê2 , . . . , ÊN1+1 } = LLMLayers({E 1 , . . . , E N1 }) ▷ Êi ∈ R D", "section": "B Implementation Details", "sec_num": null}, {"text": "6: for i in {2, . . . , N + 1}: ", "section": "B Implementation Details", "sec_num": null}, {"text": "SegmentEmbedding and SegmentProjection are uniformly implemented by MLP. The number of layers is fixed as 2 and the hidden dimension is selected from {256, 512, 1024} according to the validation loss. The segment length is set as S = 96 in multivariate datasets and is set as the prediction length S = F in M3 and M4. We verify the robustness of AutoTimes of hyperparameters as follows: the layer number and hidden dimension of SegmentEmbedding and SegmentProjection, context length, and segment length. We observe that AutoTimes is insensitive to the configurations of embedding and projection layers. Besides, performance can be improved by increasing the context length. For long prediction lengths, a larger segment length is favored. FPT [49] iTrans. [22] DLinear [45] PatchTST [26] TimesNet [42] Metric The lookback length is set as L = 672 in others, which are all implemented by their official code.", "section": "C Hyperparameter Sensitivity", "sec_num": null}, {"text": "One-for-all Trained respectively on specific lookback/prediction length AutoTimes TimeLLM [15] UniTime [21] FPT [49] iTrans. [22] DLinear [45] PatchTST [26] TimesNet [42] Metric ", "section": "Method", "sec_num": null}, {"text": "Following the zero-shot forecasting of FPT [15] , each experiment comprises the source and target datasets. We train a model on the source dataset and apply the model on the target dataset for predictions directly.", "section": "D.2 Zero-Shot Forecasting", "sec_num": null}, {"text": "Notably, the zero-shot scenarios are conducted respectively on the subsets (e.g. M4 Monthly → M3 Monthly) and the subsets are divided by the sampling frequency but follow different distributions [25] .", "section": "D.2 Zero-Shot Forecasting", "sec_num": null}, {"text": "For M4 → M3, which means training on M4 and testing on M3, we directly utilize the same model in the short-term forecasting experiments reported in Table 11 . Considering different horizons in subsets, for M3 Yearly, M3 Quarterly, and M3 Monthly, we directly employ models trained on corresponding subsets of M4 for testing. As for M3 Others, we test using the model trained on M4 Quarterly to keep the same horizon.", "section": "D.2 Zero-Shot Forecasting", "sec_num": null}, {"text": "For M3 → M4, similarly, for M4 Yearly, M4 Quarterly, and M4 Monthly, we directly employ models trained on corresponding subsets of M3 for testing. For the remaining subsets, M4 Weekly, M4 Daily, and M4 Hourly, we perform inference using the model trained on M3 Monthly. Table 16 shows the detailed result. ", "section": "D.2 Zero-Shot Forecasting", "sec_num": null}, {"text": "Mainstream LLMs predominantly adopt the decoder-only architecture, and AutoTimes can utilize any decoderonly LLM. We conduct experiments on various types and sizes of LLMs, including GPT-2 [31] , multiple sizes of OPT [46] , and LLaMA [36] . Detailed configurations and results are shown in Table 17 and 18, demonstrating a general trend where performance improves as the model size increases, consistent with the scaling law [16] . ", "section": "D.3 Method Generality", "sec_num": null}, {"text": "In the conventional forecasting paradigm, deep forecasters are trained respectively on lookback/forecast lengths, limiting their applicability to a single lookback length. In contrast, LLMs have the versatility to handle various input lengths. This capability is derived from Rotary Position Embedding [33] and the next toke prediction objective, where LLMs are trained with token-wise supervision in Equation 8, that is, the generated token at each position is supervised. While non-autoregressive LLM4TS methods are typically constrained to a fixed lookback setting, AutoTimes with a consistent training objective has the flexibility to deal with different lookback lengths. We present the results in Figure 8 , where we adapt the LLM by AutoTimes with the context length of C = 672, and evaluate the performance with different lookback lengths, which demonstrates the inherited versatility of LLM-based forecasters. Moreover, the performance is generally improving with increased available lookback observations, leading to an averaged 9.3% promotion from 384 to 672. By contrast, several works have observed that the performance of respectively trained deep forecasters does not necessarily improve with the increasing of lookback length [22, 26, 45] . ", "section": "D.4 Variable Lookback Length", "sec_num": null}, {"text": "We conduct an ablation study on the proposed position embeddings that integrate timestamps, a prevalent textual covariate in real-world applications. As shown in Figure 9 , the forecasting performance is consistently promoted across all multivariate datasets and prediction lengths. The steady improvement can be attributed to the fact that timestamps demote the absolute position of time series segments on the timeline, explicitly aligning different variates in multivariate scenarios. The increasing promotion with longer prediction length also implies that chronological information, such as date and periodicity, yields significant benefits for long-term forecasting.", "section": "D.5 Timestamps as Position Embeddings", "sec_num": null}, {"text": "Figure 9 : Ablation on whether to utilize textual timestamps as the position embedding. Results of different prediction lengths are provided, where the embedding leads to consistent performance promotion across all datasets, and the promotion can increase with a longer prediction length.", "section": "D.5 Timestamps as Position Embeddings", "sec_num": null}, {"text": "For in-context forecasting, similar to zero-shot forecasting in Appendix D.2, we train our model using the source dataset and directly evaluate it on the target dataset. In this task, we first choose M4 as the source dataset and M3 as the target dataset. It is important to note that the structure of the M3 and M4 datasets differs from typical datasets used for long-term forecasting. They consist of multiple univariate time sequences of different lengths.", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "The final part of each sequence serves as the test set, while the preceding part is used for training.", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "Implementation In zero-shot scenarios, we use a sequence of length F preceding and consecutive to the test set as input, referred to as the lookback window, where F is the forecast length of each subset. During in-context forecasting, we concatenate the first 2F time points that belong to the same sequence with the lookback window as input. We aim to enhance prediction performance by incorporating more contextual information. Too short sequences (≤ 4F ) are discarded to prevent overlap between the prompt and the lookback window. For a fair comparison, both zero-shot and in-context forecasting performance are reported on the same remaining sequences. Figure 12 provides showcases of zero-shot and in-context forecasting.", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "Prompt engineering Regarding in-context learning, we further delve into the effect of different strategies to retrieve time series as prompts, which is provided in Table 19 . P.1 and P.2 correspond to the zero-shot and in-context forecasting evaluated in Section 4.3. The prompt of P.3 contains the last 2F time points preceding the beginning of the lookback window. Another retrieval of prompts as P.4, adopts time series that come from another uncorrelated time series (out-of-series). We can obtain the following observations:", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "• P.1 v.s. P.4 indicates that the selected prompt is not suitable, since the prompt does not come from the earlier observations of the same series to be predicted. Although the context window becomes larger, the averaged performance will deteriorate because of irrelevant prompts.", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "• P.2 and P.3 indicates that in most cases, selecting the relevant 2F time series from the same series can provide better contextual information.", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "This highlights the prompt engineering for in-context forecasting. An intuitive suggestion is to utilize consecutive, inter-periodic, and multiple prompts. To verify this idea, we analyze the periodic effect of time series prompts. In previous experiments, we adopt M3 and M4 datasets, which are consistent with the zero-shot experiment of FPT [49] , to present the promotion of our in-context paradigm. To provide more rigorous conclusions, we extend the evaluation to widely recognized datasets. Details of the experiment are as follows: By using a trained model checkpoint on a source domain (Traffic), we conduct forecasting without gradient update on target ETT datasets. We evaluate the pred-96 performance on the last variate (OT). For the zero-shot scenario, the input is length-288 lookback series. For in-context forecasting, the input is (length-384 series prompt + length-288 lookback series).", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "Considering the dataset periodicity, the prompt is uniformly selected as the Ahead-24 (one-day-ahead) series of the original lookback series. To eliminate the performance boost that comes from extending the input length, we also provide the results of length-672 lookback series in the zero-shot scenario. Moreover, we further delve into the effect of different strategies to select time series prompts:", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "• Ahead-Period: The prompt is uniformly selected as the Ahead-24 series of the original lookback series where 24 is one of the periods (daily period) of ETT.", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "• Ahead-Random: The prompt is randomly selected as the previous series of the original series.", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "• Fixed Prompt: The prompt is fixed as the first 384 time points in the variate-OT.", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "• Other Variate: The prompt is uniformly selected as Ahead-24 series, but comes from other variates.", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "Results in Table 20 demonstrate the effectiveness of using suitable time series prompts and highlight the influence of prompt engineering. Using inter-period prompts can outperform simply extending lookback window.", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "The benefit of the proposed in-context forecasting is to extend the input context of time series forecasting beyond a continuous lookback window. Since the essence of prompts is to incorporate useful domain-specific knowledge, here is one use case of in-context forecasting: Considering predicting the weather of one day, one approach is to extend the lookback length from days to weekends. However, it can also introduce noisy information since ", "section": "D.6 In-Context Forecasting", "sec_num": null}, {"text": "In addition to the ablation study of whether LLMs are useful in AutoTimes (Table 6 ), we further delve into the main difference between our method and previous LLM4TS approach and provide a comprehensive ablation study. The results presented in Table 21 demonstrate that the performance of non-autoregression projection is consistently inferior to that of our autoregressive AutoTimes approach. ", "section": "D.7 Ablation Study", "sec_num": null}, {"text": "To facilitate a clear comparison among various models, we present additional prediction showcases for long-term forecasting and short-term forecasting. These examples are provided by the following models: TimeLLM [15] , FPT [49] and PatchTST [26] . Of all the models, AutoTimes delivers the most accurate future series predictions. Additionally, we provide the showcases of zero-shot and in-context forecasting in Figure 12 .", "section": "E Showcases", "sec_num": null}, {"text": "AutoTimes Time-LLM FPT PatchTST Figure 12 : Showcases of zero-shot and in-context forecasting. For in-context forecasting, beyond the lookback window, we uniformly adopt the first 2F time points that belong to the same sequence as the prompt and concatenate them as the prediction context, which achieves a more accurate prediction.", "section": "E Showcases", "sec_num": null}, {"text": "This paper copes with general-purpose time series forecasting, which is faced with increasing challenges such as the versatility to handle variable-length scenarios, good generalizability with scarce samples, utilization of multimodality, and instructive downstream prompts. Since previous studies have demonstrated the feasibility of leveraging large language models for time series, we propose a simple but effective approach as AutoTimes to obtain LLM-based forecasters, which keeps the consistency of autoregression. Our model achieves state-ofthe-art performance on forecasting benchmarks and demonstrates remarkable adaptation speed and parameter efficiency. Besides, advanced capabilities such as multi-step generation and in-context learning are inherited by the repurposed forecaster. Therefore, the proposed method makes it promising to tackle real-world applications, which helps our society prevent risks in advance and make better decisions with limited computational budgets. Our paper mainly focuses on scientific research and has no obvious negative social impact.", "section": "F Broader Impact F.1 Impact on Real-world Applications", "sec_num": null}, {"text": "In this paper, we find prevalent non-autoregressive LLM4TS methods have inconsistencies in the model structure and generative approach with LLMs, leading to insufficient utilization of the inherent multi-step token transition.", "section": "F.2 Impact on Future Research", "sec_num": null}, {"text": "Given that the generalizability and generative ability of LLMs are largely derived from the autoregressive manner, the potentials of LLMs may not be fully exhibited in time series forecasting. Therefore, we propose to adapt LLMs by the consistent training objective, the next token prediction, and accomplish arbitrary-length forecasting by iterative generation. Beyond the conventional forecasting paradigm, we propose in-context forecasting, where the context for prediction is extended, and earlier historical time series can be utilized as advantageous prompts.", "section": "F.2 Impact on Future Research", "sec_num": null}, {"text": "The compatibility with LLMs and insights from autoregression can be instructive for future LLM4TS research and the development of foundation time series models.", "section": "F.2 Impact on Future Research", "sec_num": null}, {"text": "The proposed method has not supported probabilistic forecasting, since AutoTimes only establishes the mapping between time series segments to latent embeddings of the LLM, instead of discrete language tokens. Advanced low-rank adaptation is under exploration in our work, which can further align suitable token transitions as the future extrapolation of time series. More deftly designed embedding and projection layers are underexplored to support more compatible tokenization for time series. Besides, it is fascinating to apply AutoTimes on real-world multimodal time series datasets (such as news-stocks, and logs-measurements), which leaves our future work.", "section": "G Limitation", "sec_num": null}, {"text": "• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "section": "G Limitation", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Answer: [Yes] Justification: Please refer to Section B in the main text and code in our public repository, including the detailed configurations of experiments and the scripts for reproduction.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Guidelines:", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• The answer NA means that the paper does not include experiments.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways. For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility. In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? Answer: [Yes] Justification: Please refer to Section A of Appendix and code in our public repository, including dataset descriptions and how to access them.", "section": "Open access to data and code", "sec_num": "5."}, {"text": "• The answer NA means that paper does not include experiments requiring code.", "section": "Guidelines:", "sec_num": null}, {"text": "• Please see the NeurIPS code and data submission guidelines (https://nips.cc/public/ guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https://nips.cc/public/ guides/CodeSubmissionPolicy) for more details.", "section": "Guidelines:", "sec_num": null}, {"text": "• The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable). • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.", "section": "Guidelines:", "sec_num": null}, {"text": "Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the results?", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Answer: [Yes] Justification: Please refer to Section C and D of Appendix, where we state how hyperparameters are chosen and the detailed description of experiments.", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Guidelines:", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "• The answer NA means that the paper does not include experiments.", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments?", "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Answer: [Yes] Justification: Please refer to Section B and Table 9 of Appendix, where we report standard deviations of results with three random seeds.", "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The answer NA means that the paper does not include experiments. • The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text.", "section": "Guidelines:", "sec_num": null}, {"text": "Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments?", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Answer: [Yes] Justification: Please refer to Section B of Appendix, where we provide sufficient information on the computing resource.", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Guidelines:", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "• The answer NA means that the paper does not include experiments.", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Methods with * means results from the original paper; without * means the reproduction.", "section": "", "sec_num": null}, {"text": "\"-\" indicates that results are not reported in the original paper.", "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by the Ministry of Industry and Information Technology of China.", "section": "Acknowledgments", "sec_num": null}, {"text": "We compare the performance of AutoTimes with state-of-the-art LLM4TS methods and well-acknowledged deep forecasters. Table 11 shows detailed short-term forecast results on M4. Table 10 presents results of the one-for-all forecasting benchmark across ETTh1, ECL, Traffic, Weather, and Solar-Energy datasets. We evaluate all methods by rolling forecasting: each model is trained with input length 672 and output length 96, and the predicted values are integrated as part of the input in the next iteration until reaching the desired forecast horizon.Furthermore, the traditional one-for-one approach, where forecasters are trained individually for each prediction length, is also presented in Table 12 . The results are reproduced using their corresponding official code. For the sake of rigor, we also provide our reproduced results with the officially reported results in Table 13 .Additionally, we evaluate AutoTimes along with other baseline models on recent benchmarks [24] . Results are presented in Table 15 . We also look forward to evaluating on more diverse benchmarks in the future.", "section": "D.1 Time Series Forecasting", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?Answer: [Yes] Justification: Please refer to Section 1 of the main text, where the claims and contributions are included.", "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "section": "Guidelines:", "sec_num": null}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?Answer: [Yes] Justification: Please refer to Section G of Appendix, where we provide several aspects of limitations.Guidelines:• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach. For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?Answer: [NA]Justification: The paper does not include theoretical results.Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and cross-referenced.• All assumptions should be clearly stated or referenced in the statement of any theorems.• The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?Answer: [Yes]Justification: We have reviewed and the reasearch conforms with the NeurIPS Code of Ethics.Guidelines:• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?Answer: [Yes] Justification: Please refer to Section F of Appendix, where we discuss societal impacts and influences on future research.Guidelines:• The answer NA means that there is no societal impact of the work performed.• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?Answer: [NA] Justification: The paper poses no such risks.Guidelines:• The answer NA means that the paper poses no such risks.• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters.• Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?Answer: [Yes]Justification: All creators of datasets are properly credited by citations in Section A.Guidelines:• The answer NA means that the paper does not use existing assets.• The authors should cite the original paper that produced the code package or dataset.• The authors should state which version of the asset is used and, if possible, include a URL.• The name of the license (e.g., CC-BY 4.0) should be included for each asset.• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators.", "section": "Safeguards", "sec_num": "11."}, {"text": "Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets?Answer: [NA] Justification: The paper does not release new assets.Guidelines:• The answer NA means that the paper does not release new assets.• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • At submission time, remember to anonymize your assets (if applicable). You can either create an anonymized URL or include an anonymized zip file.", "section": "New Assets", "sec_num": "13."}, {"text": "Question: For crowdsourcing experiments and research with human subjects, does the paper include the full text of instructions given to participants and screenshots, if applicable, as well as details about compensation (if any)?Answer: [NA]Justification: The paper does not involve crowdsourcing nor research with human subjects.Guidelines:• The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Including this information in the supplemental material is fine, but if the main contribution of the paper involves human subjects, then as much detail as possible should be included in the main paper. • According to the NeurIPS Code of Ethics, workers involved in data collection, curation, or other labor should be paid at least the minimum wage in the country of the data collector.", "section": "Crowdsourcing and Research with Human Subjects", "sec_num": "14."}, {"text": "Question: Does the paper describe potential risks incurred by study participants, whether such risks were disclosed to the subjects, and whether Institutional Review Board (IRB) approvals (or an equivalent approval/review based on the requirements of your country or institution) were obtained?Answer: [NA]Justification: The paper does not involve crowdsourcing nor research with human subjects.Guidelines:• The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "section": "Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects", "sec_num": "15."}], "ref_entries": {"FIGREF0": {"uris": null, "fig_num": "2", "text": "Figure 2: An example to illustrate how AutoTimes adapts language models for time series forecasting.", "type_str": "figure", "num": null}, "FIGREF1": {"uris": null, "fig_num": "2", "text": "g n P B s 1 4 9 a 4 M + 4 / U 4 2 c 1 u z i 2 z I e P g B r N Z R D < / l a t e x i t > s l a t e x i t s h a 1 _ b a s e 6 4 = \"I 3 I u + v S m V b L p m S 9 N u A 9 7 R t a T K g E = \" > A A A C 0 H i c j V H L S s N A F D 2 N r 1 p f V Z d u g k V w V Z I i 6 r I o g s v 6 a C 2 0 p S T T a R u a l 8 l E L K W I W 3 / A r X 6 V + A f 6 F 9 4 Z U 1 C L 6 I Q k Z 8 6 9 5 8 z c e + 3 Q d W J h G K 8 Z b W Z 2 b n 4 h u 5 h b W l 5 Z X c u v b 9 T i I I k Y r 7 L A D a K 6 b c X c d X x e F Y 5 w e T 2 M u O X Z L r + y B 8 c y f n X D o 9 g J / E s x D H n L s 3 q + 0 3 W Y J Y h q N T 1 L 9 O 3 u 6 O J k 3 C 6 1 8 w W j a K i l T w M z B Q W k q x L k X 9 B E B w E Y E n j g 8 C E I u 7 A Q 0 9 O A C Q M h c S 2 M i I s I O S r O M U a O t A l l c c q w i B 3 Q t 0 e 7 R s r 6 t J e e s V I z O s W l N y K l j h 3 S B J Q X E Z a n 6 S q e K G f J / u Y 9 U p 7 y b k P 6 2 6 m X R 6 x A n 9 i / d J P M / + p k L Q J d H K o a H K o p V I y s j q U u i e q K v L n + p S p B D i F x E n c o H h F m Sj n p s 6 4 0 s a p d 9 t Z S 8 T e V K V m 5 Z 2 l u g n d 5 S x q w + X O c 0 6 B W K p r 7 R f N s r 1 A + S k e d x R a 2 s U v z P E A Z p 6 i g S t 7 X e M Q T n r V z 7 V a 7 0 + 4 / U 7 V M q t n E t 6 U 9 f A D s 9 p R y < / l a t e x i t > SE2 < l a t e x i t s h a 1 _ b a s e 6 4 = \" P c b e C C 9 U G X o / A b P e r M D 7 X m 1 O Y 0", "type_str": "figure", "num": null}, "FIGREF2": {"uris": null, "fig_num": null, "text": "9 a v E P 9 C / 8 M 6 Y g l p E J y Q 5 c + 4 9 Z + b e a 0 e e G w v D e M 1 o c / ML i 0 v Z 5 d z K 6 t r 6 R n 5 z q x a H C X d Y 1 Q m 9 k D d s K 2 a e G 7 C q c I X H G h F n l m 9 7 r G 4 P z 2 S 8 f s N 4 7 I Z B R Y w i 1 v a t f u D 2 X M c S R L V b v i U G d m 9 c O Z 9 0 D j r 5 g l E 0 1 N J n g Z m C A t J V D v M v a K G L E A 4 S + G A I I A h 7 s B D T 0 4 Q J A x F x b Y y J 4 4 R c F W e Y I E f a h L I Y Z V j E D u n b p 1 0 z Z Q P a S 8 9 Y q R 0 6 x a O X k 1 L H H m l C y u O E 5 W m 6 i i f K W b K / e Y + Vp 7 z b i P 5 2 6 u U T K z A g 9 i / d N P O / O l m L Q A 8 n q g a X a o o U I 6 t z U p d E d U X e X P 9 S l S C H i D i J u x T n h B 2 l n P Z Z V 5 p Y 1 S 5 7 a 6 n 4 m 8 q U r N w 7 a W 6 C d 3 l L G r D 5 c 5 y z o H Z Q N I + K 5 u V h o X S a j j q L H e x i n + Z 5 j B I u U E a V v K / x i C c 8 a 1 f a r X a n 3 X + m a p l U s 4 1 v S 3 v 4 A O 9 a l H M = < / l a t e x i t > TE2 < l a t e x i t s h a 1 _ b a s e 6 4 = \" c R J 3 N X 8 o o f F x v X z e w w M W v i 5 y e B A = \"", "type_str": "figure", "num": null}, "FIGREF3": {"uris": null, "fig_num": null, "text": "9 a v E P 9 C / 8 M 6 Y g l p E J y Q 5 c + 4 9 Z + b e a 4 e u E w v D e M 1 o c / M L i 0 v Z 5 d z K 6 t r 6 R n 5 z q x Y H S c R 4 l Q V u E D V s K + a u 4 / O q c I T L G 2 H E L c 9 2 e d 0 e n s l 4 / Y Z H s R P 4 F T E K e d u z + r 7 T c 5 g l i G q 3 P E s M 7 N 6 4 c j 7 p m J 1 8 w S g a a u m z w E x B A e k q B / k X t N B F AI Y E H j h 8 C M I u L M T 0 N G H C Q E h c G 2 P i I k K O i n N M k C N t Q l m c M i x i h / T t 0 6 6 Z s j 7 t p W e s 1 I x O c e m N S K l j j z Q B 5 U W E 5 W m 6 i i f K W b K / e Y + V p 7 z b i P 5 2 6 u U R K z A g 9 i / d N P O / O l m L Q A 8 n q g a H a g o V I 6 t j q U u i u i J v r n + p S p B D S J z E X Y p H h J l S T v u s K 0 2 s a p e 9 t V T 8 T W V K V u 5 Z m p v g X d 6 S B m z + H O c s q B 0 U z a O i e X lY K J 2 m o 8 5 i B 7 v Y p 3 k e o 4 Q L l F E l 7 2 s 8 4 g n P 2 p V 2 q 9 1 p 9 5 + p W i b V b O P b 0 h 4 + A O z 6 l H I = < / l a t e x i t > TE1 < l a t e x i t s h a 1 _ b a s e 6 4 = \" l h s z x V p a I V b f h g O m w w l n pf V 9 H R 0 = \" > A A A C 1 3 i c j V H L S s N A F D 2 N r 1 p f t S 7 d B I v g q i Q q 6 r L o x m V F + 5 C 2 l E k 6 b U P z I p m I p R R 3 4 t Y f c K t / J P 6 B / o V 3 x g h q E Z 2 Q 5 My 5 9 5 y Z e 6 8 V u k 4 s D O M l o 8 3 M z s 0 v Z B d z S 8 s r q 2 v 5 9 U I t D p L I 5 l U 7 c I O o Y b G Y u 4 7 P q 8 I R L m + E E W e e 5 f K 6 N T y R 8 f o V j 2 I n 8 C / E K O R t j / V 9 p + f Y T B D V y R d a A y b G L Y + J g d U b n 0 8 m n b 1 O v m i U D L X 0 a W C m o I h 0 V Y L 8 M 1 r o I o C N B B 4 4 f A j C L h h i e p o w Y S A k r o 0 x c R E h R 8 U 5 J s i R N q E s T h m M 2 C F 9 + 7 R r p q x P e + k Z K 7 V N p 7 j 0 R q T U s U 2 a g P I i w v I 0 X c U T 5 S z Z 3 7 z H y l P e b U R / K / X y i B U Y E P u X 7 j P z v z p Z i 0 A P R 6 o G h 2 o K F S O r s 1 O X R H V F 3 l z / U p U g h 5 A 4 i b s U j w j b S v n Z Z 1 1 p Y l W 7 7 C 1 T 8 V e V K V m 5 t 9 P c B G / y l j R g 8 + c 4 p 0 F t t 2 Q e l M y z / W L 5 O B 1 1 F p v Y w g 7 N 8 x B l n K K C K n l f 4 w G P e N I u t R v t V r v 7 S N U y q W Y D 3 5 Z 2 / w 7 T y 5 c i < / l a t e x i t > Ŝ3 < l a t e x i t s h a 1 _ b a s e 6 4 = \" e x e f M a d P 7 O U C 0 W U + F l d 3 5 7 n U s o A = \" > A A A C 1 3 i c j V H L S s N A F D 2 N r 1 p f t S 7 d B I v g q i Q i 6 r L o x m V F + 5 C 2 l E k 6 b U P z I p m I p R R 3 4 t Y f c K t / J P 6 B / o V 3 x h T U I j o h y Z l z 7 z k z 9 1 4 r d J 1 Y G M Z r R p u b X 1 h c y i 7 n V l b X 1 j f y m 4 V a H C S R z a t 2 4 A Z R w 2 I x d x 2 f V 4 U j X N 4 I I 8 4 8 y + V 1 a 3 g q 4 / V r H s V O 4 F + K U c j b H u v 7 T s + x m S C q k y + 0 B k y M W x 4 T A 6 s 3 v p h M O v u d f N E o G W r p s 8 B M Q R H p q g T 5 F 7 T Q R Q A b C T x w + B C E X T D E 9 D R h w k B I X B t j 4 i J C j o p z T J A j b U J Z n D I Y s U P 6 9 m n X T F m f 9 t I z V m q b T n H p j U i p Y 5 c 0 A e V F h O V p u o o n y l m y v 3 m P l a e 8 2 4 j + V u r l E S s w I P Y v 3 T T z v z p Z i 0 A P x 6 o G h 2 o K F S O r s 1 O X R H V F 3 l z / U p U g h 5 A 4 i b s U j w j b S j n t s 6 4 0 s a p d 9 p a p + J v K l K z c 2 2 l u g n d 5 S x q w + X O c s 6 C 2 X z I P S + b 5 Q b F 8 k o 4 6 i 2 3 s Y I / m e Y Q y z l B B l b x v 8 I g n P G t X 2 q 1 2 p 9 1 / p m q Z V L O F b 0 t 7 + A D R a 5 c h < / l a t e x i t > Ŝ2 < l a t e x i t s h a 1 _ b a s e 6 4 = \" b G K J G N g G J H K w j H r L F d 7 2 Q E D T 0 / w = \" > A A A C z 3 i c j V H L S s N A F D 3 G V 6 2 v q k s 3 w S K 4 K o m I u i y 6 c d m C f U B b S p J O 2 9 C 8 m J k o p V T c + g N u 9 a / E P 9 C / 8 M 6 Y g l p E J y Q 5 c + 4 9 Z + b e 6 y a B L 6 R l v S 4 Y i 0 v L K 6 u 5 t f z 6 x u b W d m F n t y 7 i l H u s 5 s V B z J u u I 1 j g R 6 w m f R m w Z s K Z E 7 o B a 7 i j S x V v 3 D A u / D i 6 l u O E d U J n E P l 9 3 3 M k U e 1 2 6 M i h 2 5 + I a d f u F o p W y d L L n A d 2 B o r I V i U u v K C N H m J 4 S B G C I Y I k H M C B o K c F G x Y S 4 j q Y E M c J + T r O M E W e t C l l M c p w i B 3 R d 0 C 7 V s Z G t F e e Q q s 9 O i W g l 5 P S x C F p Y s r j h N V p p o 6 n 2 l m x v 3 l P t K e 6 2 5 j + b u Y", "type_str": "figure", "num": null}, "FIGREF4": {"uris": null, "fig_num": null, "text": "g h 5 A 4 i b s U j w j b S j n t s 6 4 0 s a p d 9 p a p + J v K l K z c 2 2 l u g n d 5 S x q w + X O c s 6 B 2 U D S P i u b F Y a F 8 k o 4 6 g x 3 s Y p / m e Y w y z l F B l b x v 8 I g n P G t X 2 q 1 2 p 9 1 / p m p z q W Y b 3 5 b 2 8 A G + M 5 c Z < / l a t e x i t > Ê8 < l a t e x i t s h a 1 _ b a s e 6 4 = \" h B", "type_str": "figure", "num": null}, "FIGREF5": {"uris": null, "fig_num": null, "text": "h w k B I X B t j 4 i J C j o p z T J A l b U J Z n D I Y s U P 6 9 m n X T F m f 9 t I z V m q b T n H p j U i p Y 4 8 0 A e V F h O V p u o o n y l m y v 3 m P l a e 8 2 4 j + V u r l E S s w I P Y v 3 T T z v z p Z i 0", "type_str": "figure", "num": null}, "FIGREF6": {"uris": null, "fig_num": "3", "text": "Figure 3: Overview of AutoTimes: (1) time series and corresponding timestamps are segmented; (2) textual timestamps are converted into the position embeddings by the LLM; (3) time series segments are embedded and projected by next token prediction, where intermediate layers of LLM are frozen.", "type_str": "figure", "num": null}, "FIGREF7": {"uris": null, "fig_num": "4", "text": "Figure 4: Demonstration of in-context forecasting and results compared with zero-shot. We uniformly select the foremost time points from the target domain as prompts and concatenate them with lookback to obtain the prediction. AutoTimes adapts LLMs on the source domain with a larger context length to place the additional time series prompt. Supplementary showcases are provided in Figure 12.", "type_str": "figure", "num": null}, "FIGREF8": {"uris": null, "fig_num": "5", "text": "Figure 5: Efficiency comparison of alternative LLMs, evaluated by the same configuration of Table5.", "type_str": "figure", "num": null}, "FIGREF9": {"uris": null, "fig_num": "6", "text": "Figure 6: Comparison of AutoTimes and other LLM4TS methods in terms of training/inference time and tunable parameters with the same batch size (224) on the ETTh1 dataset.", "type_str": "figure", "num": null}, "FIGREF10": {"uris": null, "fig_num": "745", "text": "for ŝi = SegmentProjection( Êi ) ▷ ŝi ∈ R S 8: Return ŝN+1 ▷ Return last token ŝN+1 ∈ R S as the prediction Algorithm 4 AutoTimes -Autoregressive Generation Require: Input time series {x 1 , . . . , x N1×S }; textual embeddings {TE 1 , . . . , TE N2 } ▷ Forecast token number N 2 -N 1 1: x = {x 1 , . . . , x N1×S } 2: prediction = {} 3: for i in {1, . . . , N 2 -N 1 }: for ŷ = LLMForecaster(x, TE :N1+i-1 ) ▷ Details in Algorithm 3 for x ← {x, ŷ} ▷ Concatenate for the input for next iteration 6: for prediction ← {prediction, ŷ} ▷ Record prediction results 7: Return prediction ▷ Return result ∈ R (N2-N1)×S", "type_str": "figure", "num": null}, "FIGREF11": {"uris": null, "fig_num": "7", "text": "Figure 7: Hyperparameter sensitivity of AutoTimes. Each curve presents a specific forecast length.", "type_str": "figure", "num": null}, "FIGREF12": {"uris": null, "fig_num": "8", "text": "Figure 8: Performance of LLM-based forecasters on the pred-96 scenario, which are adapted by AutoTimes by the context length C = 672 and directly applied on different lookback lengths.", "type_str": "figure", "num": null}, "FIGREF13": {"uris": null, "fig_num": "1011", "text": "Figure 10: Visualization of input-672-predict-96 results on the Traffic dataset.", "type_str": "figure", "num": null}, "FIGREF14": {"uris": null, "fig_num": null, "text": "contribution. For example (a) If the contribution is primarily a new algorithm, the paper should make it clear how to reproduce that algorithm. (b) If the contribution is primarily a new model architecture, the paper should describe the architecture clearly and fully. (c) If the contribution is a new model (e.g., a large language model), then there should either be a way to access this model for reproducing the results or a way to reproduce the model (e.g.", "type_str": "figure", "num": null}, "TABREF0": {"text": "Comparison of LLM4TS methods: Autoregressive categories LLM-based forecasters by whether to conduct autoregression. Freeze LLM enables quick adaptation, which would otherwise require significant resources for fine-tuning. Multimodal refers to the utilization of information from other modalities. Prior to AutoTimes, none of the LLM4TS methods achieved all three.", "content": "<table/>", "html": null, "type_str": "table", "num": null}, "TABREF3": {"text": "", "content": "<table/>", "html": null, "type_str": "table", "num": null}, "TABREF4": {"text": "Average short-term forecasting results on the M4[25]. Full results are provided in Table11.", "content": "<table><tr><td/><td>Models</td><td colspan=\"2\">AutoTimes TimeLLM</td><td>FPT</td><td>Koopa N-HiTS DLinear PatchTST TimesNet FiLM N-BEATS</td></tr><tr><td>Average</td><td colspan=\"2\">sMAPE 11.831 MASE 1.585 OWA 0.850</td><td colspan=\"2\">11.983 11.991 11.863 11.960 12.418 13.022 11.930 12.489 11.910 1.595 1.600 1.595 1.606 1.656 1.814 1.597 1.690 1.613 0.859 0.861 0.858 0.861 0.891 0.954 0.867 0.902 0.862</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF5": {"text": "Long-term forecasting results of one-for-all: we conduct rolling forecasting with a single model trained on each dataset and accomplish four desired forecast lengths in {96, 192, 336, 720}. AutoTimes adapt LLMs with the context length C = 672. We set the input length L = 672 and output length F = 96 in other methods. All results are averaged. Full results is provided in Table", "content": "<table/>", "html": null, "type_str": "table", "num": null}, "TABREF6": {"text": "MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE", "content": "<table><tr><td>ETTh1 0.389 0.422 0.412 0.437 0.683 0.596 0.429 0.439 0.421 0.445 0.426 0.444 0.409 0.430 0.495 0.491</td></tr><tr><td>ECL 0.159 0.253 0.181 0.288 0.325 0.399 0.184 0.284 0.164 0.258 0.165 0.265 0.169 0.268 0.201 0.303</td></tr><tr><td>Weather 0.235 0.273 0.225 0.266 0.461 0.459 0.228 0.266 0.266 0.291 0.239 0.291 0.226 0.268 0.264 0.293</td></tr><tr><td>Traffic 0.374 0.264 0.410 0.303 0.584 0.367 0.461 0.326 0.384 0.274 0.423 0.298 0.391 0.275 0.602 0.322</td></tr><tr><td>Solar. 0.197 0.242 0.263 0.335 0.392 0.462 0.236 0.303 0.213 0.291 0.222 0.283 0.202 0.269 0.213 0.295</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF7": {"text": "Zero-shot forecasting results in averaged SMAPE. M4 → M3 trains forecasters on the datasets of M4 and evaluates on M3, and vice versa. Detailed results are provided in Appendix D.2ResultsThe comprehensive results of zero-shot forecasting are presented in Table4. AutoTimes demonstrates superior performance compared to deep forecasters and FPT in both M4 → M3 and M3 → M4 scenarios. It is evident that LLM4TS methods generally achieve improved performance in this task due to the enhanced model capacity, leading to a 15% SMAPE reduction compared with the efficient forecaster DLinear. Despite sharing the same Transformer backbone, LLM4TS methods still outperform PatchTST due to the transferable knowledge pre-trained on large corpora of sequences. This underscores the advantage of leveraging LLMs for time series forecasting. Moreover, AutoTimes inherits general-purpose token transitions, surpassing FPT without tuning intermediate LLM layers.", "content": "<table><tr><td colspan=\"2\">Models AutoTimes</td><td>FPT</td><td colspan=\"7\">DLinear PatchTST TimesNet NSFormer FEDFormer Informer Reformer</td></tr><tr><td>M4 → M3</td><td>12.75</td><td colspan=\"2\">13.06 14.03</td><td>13.06</td><td>14.17</td><td>15.29</td><td>13.53</td><td>15.82</td><td>13.37</td></tr><tr><td colspan=\"7\">M3 → M4 13.036 13.125 15.337 13.228 14.553 14.327</td><td colspan=\"3\">15.047 19.047 14.092</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF8": {"text": "Averaged results of alternative language models. Full results are provided in Table18.", "content": "<table><tr><td>LLM</td><td>GPT-2 (124M)</td><td>OPT-350M</td><td>OPT-1.3B</td><td>OPT-2.7B</td><td>OPT-6.7B</td><td>LLaMA-7B</td></tr><tr><td>Metric</td><td colspan=\"6\">MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE</td></tr><tr><td>ECL</td><td colspan=\"6\">0.173 0.266 0.168 0.263 0.164 0.258 0.164 0.258 0.162 0.256 0.159 0.253</td></tr><tr><td>ETTh1</td><td colspan=\"6\">0.397 0.425 0.401 0.429 0.396 0.424 0.394 0.424 0.394 0.424 0.389 0.423</td></tr><tr><td>Traffic</td><td colspan=\"6\">0.406 0.276 0.405 0.277 0.397 0.271 0.394 0.269 0.393 0.270 0.374 0.264</td></tr><tr><td colspan=\"7\">Weather 0.242 0.278 0.240 0.275 0.240 0.276 0.243 0.277 0.247 0.282 0.235 0.273</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF9": {"text": "We follow the protocol of LLM4TS ablation studies[35] to verify whether the LLM is truly useful in our AutoTimes: (1) w/o LLM replaces the language model entirely and passing input tokens directly to the last layer; (2) LLM2Attn replaces the language model with a single multi-head attention layer; (3) LLM2Trsf replaces the language model with a single transformer block.", "content": "<table><tr><td>Dataset</td><td/><td colspan=\"2\">ETTh1</td><td/><td/><td colspan=\"2\">ECL</td><td/></tr><tr><td>Type</td><td>AutoTimes</td><td>w/o LLM</td><td>LLM2Attn</td><td>LLM2Trsf</td><td>AutoTimes</td><td>w/o LLM</td><td>LLM2Attn</td><td>LLM2Trsf</td></tr><tr><td colspan=\"9\">Metric MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE</td></tr><tr><td colspan=\"9\">Pred-96 0.360 0.400 0.365 0.399 0.383 0.404 0.377 0.401 0.129 0.225 0.171 0.263 0.156 0.255 0.162 0.263</td></tr><tr><td colspan=\"9\">Pred-192 0.388 0.419 0.405 0.425 0.414 0.422 0.406 0.420 0.147 0.241 0.192 0.282 0.178 0.276 0.189 0.287</td></tr><tr><td colspan=\"9\">Pred-336 0.401 0.429 0.429 0.441 0.431 0.432 0.421 0.431 0.162 0.258 0.216 0.304 0.198 0.295 0.216 0.309</td></tr><tr><td colspan=\"9\">Pred-720 0.406 0.440 0.450 0.468 0.456 0.454 0.449 0.452 0.199 0.288 0.264 0.342 0.230 0.320 0.258 0.340</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF10": {"text": "Full long-term forecasting results of AutoTimes and AutoTimes equipped with LoRA[14].", "content": "<table><tr><td colspan=\"2\">Dataset</td><td>ETTh1</td><td>ECL</td><td>Weather</td><td>Traffic</td><td>Solar-Energy</td></tr><tr><td colspan=\"2\">Metric</td><td colspan=\"5\">MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE</td></tr><tr><td>Pred-96</td><td colspan=\"6\">AutoTimes 0.360 0.397 0.140 0.236 0.158 0.208 0.369 0.257 0.179 0.220 + LoRA 0.357 0.397 0.130 0.225 0.151 0.201 0.360 0.256 0.176 0.219</td></tr><tr><td>Pred-192</td><td colspan=\"6\">AutoTimes 0.391 0.419 0.159 0.253 0.207 0.254 0.394 0.268 0.198 0.236 + LoRA 0.391 0.420 0.149 0.242 0.197 0.244 0.383 0.267 0.195 0.235</td></tr><tr><td>Pred-336</td><td colspan=\"6\">AutoTimes 0.408 0.432 0.177 0.270 0.262 0.298 0.413 0.278 0.213 0.252 + LoRA 0.409 0.433 0.164 0.259 0.251 0.287 0.401 0.277 0.208 0.249</td></tr><tr><td>Pred-720</td><td colspan=\"6\">AutoTimes 0.429 0.452 0.216 0.303 0.342 0.353 0.449 0.299 0.239 0.277 + LoRA 0.426 0.451 0.202 0.293 0.326 0.339 0.440 0.300 0.225 0.268</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF11": {"text": "Detailed dataset descriptions. Dim denotes the variate number. Dataset Size denotes the total number of time points in (Train, Validation, Test) splits respectively. Forecast Length denotes the future time points to be predicted. Frequency denotes the sampling interval of time points.", "content": "<table><tr><td>Dataset</td><td>Dim</td><td>Forecast Length</td><td>Dataset Size</td><td>Frequency</td><td>Information</td></tr><tr><td>ETTh1</td><td>7</td><td>{96, 192, 336, 720}</td><td>(8545, 2881, 2881)</td><td>Hourly</td><td>Electricity</td></tr><tr><td>Weather</td><td>21</td><td>{96, 192, 336, 720}</td><td>(36792, 5271, 10540)</td><td>10min</td><td>Weather</td></tr><tr><td>ECL</td><td>321</td><td>{96, 192, 336, 720}</td><td>(18317, 2633, 5261)</td><td>Hourly</td><td>Electricity</td></tr><tr><td>Traffic</td><td>862</td><td>{96, 192, 336, 720}</td><td>(12185, 1757, 3509)</td><td>Hourly</td><td>Transportation</td></tr><tr><td>Solar-Energy</td><td>137</td><td>{96, 192, 336, 720}</td><td>(36601, 5161, 10417)</td><td>10min</td><td>Energy</td></tr><tr><td>M4-Yearly</td><td>1</td><td>6</td><td>(23000, 0, 23000)</td><td>Yearly</td><td>Demographic</td></tr><tr><td>M4-Quarterly</td><td>1</td><td>8</td><td>(24000, 0, 24000)</td><td>Quarterly</td><td>Finance</td></tr><tr><td>M4-Monthly</td><td>1</td><td>18</td><td>(48000, 0, 48000)</td><td>Monthly</td><td>Industry</td></tr><tr><td>M4-Weekly</td><td>1</td><td>13</td><td>(359, 0, 359)</td><td>Weekly</td><td>Macro</td></tr><tr><td>M4-Daily</td><td>1</td><td>14</td><td>(4227, 0, 4227)</td><td>Daily</td><td>Micro</td></tr><tr><td>M4-Hourly</td><td>1</td><td>48</td><td>(414, 0, 414)</td><td>Hourly</td><td>Other</td></tr><tr><td>M3-Yearly</td><td>1</td><td>6</td><td>(645, 0, 645)</td><td>Yearly</td><td>Demographic</td></tr><tr><td>M3-Quarterly</td><td>1</td><td>8</td><td>(756, 0, 756)</td><td>Quarterly</td><td>Finance</td></tr><tr><td>M3-Monthly</td><td>1</td><td>18</td><td>(1428, 0, 1428)</td><td>Monthly</td><td>Industry</td></tr><tr><td>M3-Others</td><td>1</td><td>8</td><td>(174, 0, 174)</td><td>Weekly</td><td>Macro</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF12": {"text": "Algorithm 1 AutoTimes -Generate Text Embedding Require: Input time series x (i-1)S+1:iS ▷ i-th token of length S 1: s i = {x (i-1)S+1 , . . . , x iS } ▷ Model dimension of the LLM D", "content": "<table/>", "html": null, "type_str": "table", "num": null}, "TABREF14": {"text": "Performance and standard deviations of AutoTimes. Results come from three random seeds.", "content": "<table><tr><td>Dataset</td><td colspan=\"2\">ETTh1</td><td colspan=\"2\">ECL</td><td colspan=\"2\">Weather</td></tr><tr><td>Horizon</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td></tr><tr><td>96</td><td>0.360±0.002</td><td>0.400±0.002</td><td>0.129±0.001</td><td>0.225±0.001</td><td>0.153±0.000</td><td>0.203±0.001</td></tr><tr><td>192</td><td>0.388±0.002</td><td>0.419±0.001</td><td>0.147±0.002</td><td>0.241±0.002</td><td>0.201±0.001</td><td>0.250±0.001</td></tr><tr><td>336</td><td>0.401±0.003</td><td>0.429±0.002</td><td>0.162±0.002</td><td>0.258±0.003</td><td>0.256±0.002</td><td>0.293±0.001</td></tr><tr><td>720</td><td>0.406±0.004</td><td>0.440±0.002</td><td>0.199±0.004</td><td>0.288±0.002</td><td>0.331±0.002</td><td>0.345±0.001</td></tr><tr><td>Dataset</td><td colspan=\"2\">Traffic</td><td colspan=\"2\">Solar-Energy</td><td/><td/></tr><tr><td>Horizon</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td><td/><td/></tr><tr><td>96</td><td>0.343±0.001</td><td>0.248±0.001</td><td>0.171±0.001</td><td>0.221±0.001</td><td/><td/></tr><tr><td>192</td><td>0.362±0.001</td><td>0.257±0.001</td><td>0.190±0.001</td><td>0.236±0.002</td><td/><td/></tr><tr><td>336</td><td>0.379±0.002</td><td>0.266±0.001</td><td>0.203±0.002</td><td>0.248±0.002</td><td/><td/></tr><tr><td>720</td><td>0.413±0.003</td><td>0.284±0.002</td><td>0.222±0.002</td><td>0.262±0.003</td><td/><td/></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF15": {"text": "Full results of short-term forecasting. We follow the same protocol of TimesNet[42].", "content": "<table><tr><td colspan=\"2\">Method</td><td colspan=\"2\">AutoTimes TimeLLM</td><td>FPT</td><td colspan=\"4\">Koopa N-HiTS N-BEATS PatchTST TimesNet DLinear FiLM</td></tr><tr><td>Yearly</td><td colspan=\"2\">SMAPE 13.319 MASE 2.993 OWA 0.784</td><td colspan=\"6\">13.419 13.531 13.352 13.371 13.866 13.517 13.394 14.012 13.466 3.005 3.015 2.997 3.025 3.006 3.031 3.004 3.071 3.059 0.789 0.793 0.786 0.790 0.802 0.795 0.787 0.815 0.797</td></tr><tr><td>Quarterly</td><td colspan=\"2\">SMAPE 10.101 MASE 1.182 OWA 0.890</td><td colspan=\"6\">10.110 10.177 10.159 10.454 10.689 10.847 10.101 10.758 10.074 1.178 1.194 1.189 1.219 1.294 1.315 1.183 1.306 1.163 0.889 0.897 0.895 0.919 0.957 0.972 0.890 0.905 0.881</td></tr><tr><td>Monthly</td><td colspan=\"2\">SMAPE 12.710 MASE 0.934 OWA 0.880</td><td colspan=\"6\">12.980 12.894 12.730 12.794 13.372 14.584 12.866 13.377 12.801 0.963 0.956 0.953 0.960 1.014 1.169 0.964 1.021 0.955 0.903 0.897 0.901 0.895 0.940 1.055 0.894 0.944 0.893</td></tr><tr><td>Others</td><td>SMAPE MASE OWA</td><td>4.843 3.277 1.026</td><td>4.795 3.178 1.006</td><td colspan=\"2\">4.940 4.861 4.696 3.228 3.124 3.130 1.029 1.004 0.988</td><td>4.894 3.358 1.044</td><td>6.184 4.818 1.140</td><td>4.982 5.259 5.008 3.323 3.608 3.443 1.048 1.122 1.070</td></tr><tr><td>Average</td><td colspan=\"2\">SMAPE 11.831 MASE 1.585 OWA 0.850</td><td colspan=\"6\">11.983 11.991 11.863 11.960 12.418 13.022 11.930 12.489 11.910 1.595 1.600 1.595 1.606 1.656 1.814 1.597 1.690 1.613 0.859 0.861 0.858 0.861 0.891 0.954 0.867 0.902 0.862</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF16": {"text": "Long-term forecasting results of one-for-one: AutoTimes trains one LLM-based forecaster to handle all prediction lengths by autoregression, whereas other models are trained respectively on each prediction length. AutoTimes adapts LLMs with the context length C = 672. The lookback length is set as L = 672 in others. All results are averaged. Full results is provided in Table14.", "content": "<table/>", "html": null, "type_str": "table", "num": null}, "TABREF17": {"text": "MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE Results of LLM4TS methods from the original paper and our reproduction by official code.Models AutoTimes TimeLLM * [15] TimeLLM[15] FPT *[49] FPT[49] UniTime *[21] UniTime[21]", "content": "<table><tr><td colspan=\"7\">ETTh1 0.389 0.422 0.409 0.432 0.438 0.445 0.426 0.438 0.438 0.450 0.423 0.437 0.413 0.431 0.458 0.450</td></tr><tr><td colspan=\"7\">ECL 0.159 0.253 0.170 0.275 0.194 0.287 0.167 0.264 0.161 0.256 0.177 0.274 0.159 0.253 0.192 0.295</td></tr><tr><td colspan=\"7\">Weather 0.235 0.273 0.227 0.266 0.260 0.283 0.231 0.269 0.238 0.272 0.240 0.300 0.226 0.264 0.259 0.287</td></tr><tr><td colspan=\"7\">Traffic 0.374 0.264 0.402 0.294 0.460 0.301 0.416 0.295 0.379 0.272 0.434 0.295 0.391 0.264 0.620 0.336</td></tr><tr><td colspan=\"7\">Solar. 0.197 0.242 0.234 0.293 0.254 0.291 0.229 0.296 0.202 0.269 0.217 0.278 0.189 0.257 0.200 0.268</td></tr><tr><td colspan=\"7\">Metric MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE</td></tr><tr><td colspan=\"7\">ETTh1 0.389 0.422 0.408 0.423 0.409 0.432 0.427 0.426 0.426 0.438 0.442 0.448 0.438 0.445</td></tr><tr><td colspan=\"7\">ECL 0.159 0.253 0.159 0.253 0.170 0.275 0.167 0.263 0.167 0.264 0.216 0.305 0.194 0.287</td></tr><tr><td colspan=\"7\">Weather 0.235 0.273 0.225 0.257 0.227 0.266 0.237 0.270 0.231 0.269 0.253 0.276 0.260 0.283</td></tr><tr><td colspan=\"5\">Traffic 0.374 0.264 0.388 0.264 0.402 0.294 0.414 0.294 0.416 0.295 -</td><td>-</td><td>0.460 0.301</td></tr><tr><td>Solar. 0.197 0.242 -</td><td>-</td><td>0.234 0.293</td><td>-</td><td>-0.229 0.296 -</td><td>-</td><td>0.254 0.291</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF18": {"text": "Full long-term forecasting results of one-for-one: AutoTimes trains one LLM-based forecaster to handle all prediction lengths by autoregression, whereas other models are trained respectively on each prediction length. AutoTimes adapt LLMs with the context length C = 672.", "content": "<table/>", "html": null, "type_str": "table", "num": null}, "TABREF19": {"text": "MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE Forecasting results on additional benchmark datasets[24] (672-pred-96).", "content": "<table><tr><td/><td colspan=\"9\">96 0.360 0.400 0.380 0.412 0.386 0.409 0.377 0.404 0.386 0.405 0.375 0.399 0.370 0.399 0.384 0.402</td></tr><tr><td>ETTh1</td><td colspan=\"9\">192 0.388 0.419 0.405 0.422 0.428 0.436 0.413 0.424 0.422 0.439 0.405 0.416 0.413 0.421 0.557 0.436 336 0.401 0.429 0.422 0.433 0.464 0.456 0.436 0.444 0.444 0.457 0.439 0.443 0.422 0.436 0.491 0.469 720 0.406 0.440 0.430 0.459 0.473 0.479 0.477 0.481 0.500 0.498 0.472 0.490 0.447 0.466 0.521 0.500</td></tr><tr><td/><td colspan=\"9\">Avg 0.389 0.422 0.409 0.432 0.438 0.445 0.426 0.438 0.438 0.450 0.423 0.437 0.413 0.431 0.458 0.450</td></tr><tr><td/><td colspan=\"9\">96 0.129 0.225 0.137 0.244 0.171 0.266 0.137 0.236 0.132 0.227 0.153 0.237 0.129 0.222 0.168 0.272</td></tr><tr><td>ECL</td><td colspan=\"9\">192 0.147 0.241 0.162 0.271 0.178 0.274 0.154 0.251 0.153 0.249 0.152 0.249 0.147 0.240 0.184 0.289 336 0.162 0.258 0.175 0.279 0.194 0.289 0.169 0.267 0.167 0.262 0.169 0.267 0.163 0.259 0.198 0.300</td></tr><tr><td/><td colspan=\"9\">720 0.199 0.288 0.207 0.306 0.232 0.319 0.207 0.300 0.192 0.285 0.233 0.344 0.197 0.290 0.220 0.320</td></tr><tr><td/><td colspan=\"9\">Avg 0.159 0.253 0.170 0.275 0.194 0.287 0.167 0.264 0.161 0.256 0.177 0.274 0.159 0.253 0.192 0.295</td></tr><tr><td/><td colspan=\"9\">96 0.153 0.203 0.149 0.200 0.180 0.223 0.154 0.205 0.163 0.211 0.152 0.237 0.149 0.198 0.172 0.220</td></tr><tr><td>Weather</td><td colspan=\"9\">192 0.201 0.250 0.195 0.243 0.226 0.261 0.196 0.245 0.205 0.250 0.220 0.282 0.194 0.241 0.219 0.261 336 0.256 0.293 0.245 0.282 0.280 0.300 0.254 0.290 0.254 0.289 0.265 0.319 0.245 0.282 0.280 0.306 720 0.331 0.345 0.318 0.338 0.355 0.348 0.321 0.337 0.329 0.340 0.323 0.362 0.314 0.334 0.365 0.359</td></tr><tr><td/><td colspan=\"9\">Avg 0.235 0.273 0.227 0.266 0.260 0.283 0.231 0.269 0.238 0.272 0.240 0.300 0.226 0.264 0.259 0.287</td></tr><tr><td/><td colspan=\"9\">96 0.343 0.248 0.373 0.280 0.438 0.291 0.395 0.283 0.351 0.257 0.410 0.282 0.360 0.249 0.593 0.321</td></tr><tr><td>Traffic</td><td colspan=\"9\">192 0.362 0.257 0.390 0.288 0.446 0.293 0.410 0.290 0.364 0.265 0.423 0.287 0.379 0.256 0.617 0.336 336 0.379 0.266 0.407 0.299 0.461 0.300 0.414 0.295 0.382 0.273 0.436 0.296 0.392 0.264 0.629 0.336 720 0.413 0.284 0.438 0.310 0.494 0.318 0.445 0.311 0.420 0.292 0.466 0.315 0.432 0.286 0.640 0.350</td></tr><tr><td/><td colspan=\"9\">Avg 0.374 0.264 0.402 0.294 0.460 0.301 0.416 0.295 0.379 0.272 0.434 0.295 0.391 0.264 0.620 0.336</td></tr><tr><td>Solar-Energy</td><td colspan=\"9\">96 0.171 0.221 0.224 0.289 0.223 0.274 0.196 0.261 0.187 0.255 0.191 0.256 0.168 0.237 0.178 0.256 192 0.190 0.236 0.244 0.289 0.251 0.290 0.224 0.292 0.200 0.270 0.211 0.273 0.187 0.263 0.200 0.268 336 0.203 0.248 0.225 0.291 0.270 0.301 0.240 0.308 0.209 0.276 0.228 0.287 0.196 0.260 0.212 0.274 720 0.222 0.262 0.243 0.301 0.271 0.298 0.256 0.321 0.213 0.276 0.236 0.295 0.205 0.269 0.211 0.273</td></tr><tr><td/><td colspan=\"9\">Avg 0.197 0.242 0.234 0.293 0.254 0.291 0.229 0.296 0.202 0.269 0.217 0.278 0.189 0.257 0.200 0.268</td></tr><tr><td/><td>Models</td><td colspan=\"2\">AutoTimes</td><td colspan=\"2\">PatchTST</td><td colspan=\"2\">iTransformer</td><td colspan=\"2\">DLinear</td></tr><tr><td/><td>Metric</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td></tr><tr><td colspan=\"2\">Australian Electricity</td><td>0.150</td><td>0.228</td><td>0.163</td><td>0.242</td><td>0.153</td><td>0.233</td><td>0.167</td><td>0.250</td></tr><tr><td/><td>Bdg-2 Panther</td><td>0.537</td><td>0.458</td><td>0.565</td><td>0.476</td><td>0.546</td><td>0.462</td><td>0.581</td><td>0.499</td></tr><tr><td/><td>Oikolab Weather</td><td>0.603</td><td>0.577</td><td>0.635</td><td>0.603</td><td>0.630</td><td>0.591</td><td>0.663</td><td>0.611</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF20": {"text": "Results of zero-shot forecasting. We adopt the same protocol as FPT[49]. M4 → M3 means training forecasters on M4 datasets and evaluating the performance on M3, and vice versa. Results of compared baselines are reported from FPT[49]. Lower SMAPE indicates better performance.", "content": "<table><tr><td/><td colspan=\"9\">Method AutoTimes FPT DLinear PatchTST TimesNet NSformer FEDformer Informer Reformer</td></tr><tr><td>M3 M4 →</td><td>Yearly Quarterly Monthly Others Average</td><td>15.71 9.35 14.06 5.79 12.75</td><td>16.42 17.43 10.13 9.74 14.10 15.65 4.81 6.81 13.06 14.03</td><td>15.99 9.62 14.71 9.44 13.39</td><td>18.75 12.26 14.01 6.88 14.17</td><td>17.05 12.56 16.82 8.13 15.29</td><td>16.00 9.48 15.12 8.94 13.53</td><td>19.70 13.00 15.91 13.03 15.82</td><td>16.03 9.76 14.80 7.53 13.37</td></tr><tr><td>M4 M3 →</td><td colspan=\"4\">Yearly Quarterly 10.742 10.787 18.856 10.929 13.728 13.740 14.193 13.966 Monthly 14.558 14.630 14.765 14.664 Others 6.259 7.081 9.194 7.087 Average 13.036 13.125 15.337 13.228</td><td>15.655 11.877 16.165 6.863 14.553</td><td>14.988 11.686 16.098 6.977 14.327</td><td>13.887 11.513 18.154 7.529 15.047</td><td colspan=\"2\">18.542 15.652 16.907 11.051 23.454 15.604 7.348 7.001 19.047 14.092</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF21": {"text": "Detailed method configurations of AutoTimes for alternative language models.", "content": "<table><tr><td>Base LLM</td><td colspan=\"2\">GPT-2 (124M) OPT-350M</td><td>OPT-1.3B</td><td>OPT-2.7B</td><td colspan=\"2\">OPT-6.7B LLaMA-7B</td></tr><tr><td>Hidden Dim.</td><td>768</td><td>1024</td><td>2048</td><td>2560</td><td>4096</td><td>4096</td></tr><tr><td>Embedding</td><td colspan=\"5\">2-layer MLP 2-layer MLP 2-layer MLP 2-layer MLP 2-layer MLP</td><td>Linear</td></tr><tr><td>Trainable Param. (M)</td><td>0.44</td><td>0.58</td><td>1.10</td><td>1.36</td><td>2.15</td><td>0.79</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF22": {"text": "Effects of different strategies to retrieve time series as prompts for in-context forecasting.", "content": "<table><tr><td>Context for prediction</td><td colspan=\"5\">Yearly Quarterly Monthly Others Averaged Err.</td></tr><tr><td>P.1: Lookback F</td><td>21.52</td><td>12.03</td><td>13.09</td><td>8.46</td><td>13.61</td></tr><tr><td>P.2: Prompt from first 2F + Lookback F</td><td>17.03</td><td>10.29</td><td>12.24</td><td>5.33</td><td>11.80 ↓</td></tr><tr><td>P.3: Prompt from last 2F + Lookback F</td><td>16.30</td><td>9.59</td><td>12.09</td><td>6.24</td><td>11.48 ↓</td></tr><tr><td>P.4: Prompt from 2F of other series + Lookback F</td><td>18.95</td><td>12.18</td><td>14.37</td><td>9.46</td><td>13.98 ↑</td></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF23": {"text": "Strategies to select time series prompts based on periodicity for in-context forecasting. meteorological conditions can change with seasons. Another practical way is to consider how the weather changes on the same day in the last year (or years). Although the input is not continuous, the input context becomes more relevant based on prior knowledge about the periodicity (yearly). Therefore, in-context forecasting makes prior knowledge incorporatable and gets performance promotion.", "content": "<table><tr><td>Context for prediction</td><td colspan=\"5\">ETTh1-OT ETTh2-OT ETTm1-OT ETTm2-OT Average Err.</td></tr><tr><td>P.0: Zero-Shot (Input-288))</td><td>0.0673</td><td>0.1637</td><td>0.0424</td><td>0.1669</td><td>0.1101</td></tr><tr><td>P.1: Zero-Shot (Input-672)</td><td>0.0657</td><td>0.1538</td><td>0.0415</td><td>0.1701</td><td>0.1078</td></tr><tr><td>P.2: Ahead-Period (Input-672)</td><td>0.0645</td><td>0.1513</td><td>0.0399</td><td>0.1629</td><td>0.1047</td></tr><tr><td>P.3: Ahead-Random (Input-672)</td><td>0.0666</td><td>0.1621</td><td>0.0407</td><td>0.1719</td><td>0.1103</td></tr><tr><td>P.4: Fixed Prompt (Input-672)</td><td>0.0769</td><td>0.1859</td><td>0.0512</td><td>0.2104</td><td>0.1311</td></tr><tr><td>P.5: Other-Variates (Input-672)</td><td>0.1263</td><td>0.1780</td><td>0.0852</td><td>0.2297</td><td>0.1548</td></tr><tr><td>non-stationary</td><td/><td/><td/><td/><td/></tr></table>", "html": null, "type_str": "table", "num": null}, "TABREF24": {"text": "Ablation study of the autoregression. FlattenHead replaces the segment-wise projection of AutoTimes by flatten and linear head[26], which is prevalent in non-autoregressive forecasters.", "content": "<table><tr><td>Dataset</td><td>ETTh1</td><td>ECL</td><td>Weather</td><td>Traffic</td></tr><tr><td>Type</td><td/><td/><td/><td/></tr><tr><td colspan=\"5\">Pred-96 0.360 0.400 0.385 0.420 0.129 0.225 0.142 0.247 0.153 0.203 0.155 0.209 0.343 0.248 0.367 0.261</td></tr><tr><td colspan=\"5\">Pred-192 0.388 0.419 0.445 0.463 0.147 0.241 0.157 0.259 0.201 0.250 0.202 0.251 0.362 0.257 0.391 0.282</td></tr><tr><td colspan=\"5\">Pred-336 0.401 0.429 0.463 0.475 0.162 0.258 0.201 0.311 0.256 0.293 0.257 0.286 0.379 0.266 0.404 0.287</td></tr><tr><td colspan=\"5\">Pred-720 0.406 0.440 0.574 0.542 0.199 0.288 0.232 0.331 0.331 0.345 0.333 0.261 0.413 0.284 0.432 0.294</td></tr></table>", "html": null, "type_str": "table", "num": null}}}}