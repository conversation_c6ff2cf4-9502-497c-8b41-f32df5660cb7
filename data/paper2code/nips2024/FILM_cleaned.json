{"paper_id": "FILM", "title": "Make Your LLM Fully Utilize the Context", "abstract": "While many contemporary large language models (LLMs) can process lengthy input, they still struggle to fully utilize information within the long context, known as the lost-in-the-middle challenge. We hypothesize that it stems from insufficient explicit supervision during the long-context training, which fails to emphasize that any position in a long context can hold crucial information. Based on this intuition, our study presents INformation-INtensive ( IN2) training, a purely data-driven solution to overcome lost-in-the-middle. Specifically, IN2 training leverages a synthesized long-context question-answer dataset, where the answer requires (1) fine-grained information awareness on a short segment (∼128 tokens) within a synthesized long context (4K-32K tokens), and (2) the integration and reasoning of information from two or more short segments. Through applying this information-intensive training on Mistral-7B, we present FILM-7B (FILlin-the-Middle). To thoroughly assess the ability of FILM-7B for utilizing long contexts, we design three probing tasks that encompass various context styles (document, code, and structured-data context) and information retrieval patterns (forward, backward, and bi-directional retrieval). The probing results demonstrate that FILM-7B can robustly retrieve information from different positions in its 32K context window. Beyond these probing tasks, FILM-7B significantly improves the performance on real-world long-context tasks (e.g., 23.5→26.9 F1 score on NarrativeQA), while maintaining a comparable performance on short-context tasks (e.g., 59.3→59.2 accuracy on MMLU).", "pdf_parse": {"paper_id": "FILM", "abstract": [{"text": "While many contemporary large language models (LLMs) can process lengthy input, they still struggle to fully utilize information within the long context, known as the lost-in-the-middle challenge. We hypothesize that it stems from insufficient explicit supervision during the long-context training, which fails to emphasize that any position in a long context can hold crucial information. Based on this intuition, our study presents INformation-INtensive ( IN2) training, a purely data-driven solution to overcome lost-in-the-middle. Specifically, IN2 training leverages a synthesized long-context question-answer dataset, where the answer requires (1) fine-grained information awareness on a short segment (∼128 tokens) within a synthesized long context (4K-32K tokens), and (2) the integration and reasoning of information from two or more short segments. Through applying this information-intensive training on Mistral-7B, we present FILM-7B (FILlin-the-Middle). To thoroughly assess the ability of FILM-7B for utilizing long contexts, we design three probing tasks that encompass various context styles (document, code, and structured-data context) and information retrieval patterns (forward, backward, and bi-directional retrieval). The probing results demonstrate that FILM-7B can robustly retrieve information from different positions in its 32K context window. Beyond these probing tasks, FILM-7B significantly improves the performance on real-world long-context tasks (e.g., 23.5→26.9 F1 score on NarrativeQA), while maintaining a comparable performance on short-context tasks (e.g., 59.3→59.2 accuracy on MMLU).", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "To a great mind, nothing is little.", "section": "Introduction", "sec_num": "1"}, {"text": "-<PERSON>", "section": "Introduction", "sec_num": "1"}, {"text": "Long-context large language models (LLMs) have recently received significant attention within the open-source community (<PERSON> et al., 2023; <PERSON> et al., 2022; <PERSON> et al., 2023a; <PERSON> et al., 2023; <PERSON> et al., 2023; Team, 2023; <PERSON> et al., 2023a; <PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023b; <PERSON> et al., 2023b; <PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2024; AI et al., 2024; <PERSON><PERSON> et al., 2024; <PERSON><PERSON><PERSON> & Jaggi, 2024; <PERSON> et al., 2024; <PERSON><PERSON> et al., 2024; <PERSON> et al., 2024; <PERSON><PERSON> et al., 2024) . The training context windows of many contemporary LLMs have been expanded to tens of thousands of Figure 1 : Performance of FILM-7B, Mistral-7B-Instruct-v0.2, and GPT4-Turbo on our three probing tasks. FILM-7B significantly overcomes the problem of information loss in the middle of the context.", "section": "Introduction", "sec_num": "1"}, {"text": "tokens, thereby enabling these models to process extensive context as input. This extended training context window can enhance many real-world downstream tasks such as long-context question answering (<PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2021; <PERSON> et al., 2023) and summarization (<PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2021) .", "section": "Introduction", "sec_num": "1"}, {"text": "However, recent studies have revealed that these long-context LLMs struggle to effectively and robustly utilize all the information provided in the context, known as the lost-in-the-middle challenge (<PERSON> et al., 2024b; <PERSON> et al., 2023) . It implies that while the LLM can comprehend the information at the beginning and end of the long context, it often overlooks the information in the middle. This challenge could significantly hinder the development of long-context LLMs, as they even often fail to pass simple probing tasks such as Needle-in-the-Haystack and passkey retrieval (Mohtashami & Jaggi, 2024) . Consequently, a pressing research question arises: how can we make long-context LLMs fully utilize the information in the long context?", "section": "Introduction", "sec_num": "1"}, {"text": "We hypothesize that the root cause of lost-in-the-middle stems from the unintentional bias hidden in the general training data. In auto-regressive pre-training, the loss on predicting the next token is more likely to be influenced by a few nearby pre-tokens rather than long-distance tokens (<PERSON><PERSON><PERSON> et al., 2018; <PERSON> et al., 2021) . For supervised fine-tuning and alignment, the system message, which strongly influences the generation of the response, is typically presented at the beginning of the context (<PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2024) . As a result, the general training process may inadvertently introduce a position bias, suggesting that important information is always located at the beginning and end of the context.", "section": "Introduction", "sec_num": "1"}, {"text": "Based on this hypothesis, our work introduces training to explicitly teach the model that the crucial information can be intensively present throughout the context, not just at the beginning and end. IN2 training is a purely data-driven solution that utilizes a synthesized long-context question-answer dataset. The long context (ranging from 4K to 32K tokens) is concatenated from many short segments (∼128 tokens), and the question-answer (QA) pairs ask for the information contained in one or more segments which are randomly placed in the long context. Specifically, we generate two types of questions, requiring (1) fine-grained information awareness on exactly one short segment, and (2) the integration and reasoning of information from two or more segments. These QA pairs are generated by prompting GPT-4-Turbo (OpenAI, 2023b) with the designed instructions and the raw segments.", "section": "Introduction", "sec_num": "1"}, {"text": "By applying this information-intensive training on Mistral-7B (<PERSON> et al., 2023) , we present FILM-7B (FILl-in-the-Middle). To thoroughly assess the long-context information awareness of FILM-7B, we design three probing tasks encompassing various context styles (document, code, and structureddata context) and information retrieval patterns (forward, backward, and bi-directional retrieval).", "section": "Introduction", "sec_num": "1"}, {"text": "The probing results (Figure 1 ) demonstrate that IN2 training significantly overcomes the lost-in-themiddle problem for the backbone model. Moreover, it can enhance the open-source model to achieve comparable or even more robust performance compared with proprietary LLMs such as GPT-4-Turbo.", "section": "Introduction", "sec_num": "1"}, {"text": "Beyond these probing tasks, the performance of FILM-7B on real-world long-context tasks also exhibits significant improvements (e.g., 23.5→26.9 F1 score on NarrativeQA (<PERSON><PERSON><PERSON> et al., 2018) ). This demonstrates that the post-training on synthesized long-context data can be generalized to ", "section": "Introduction", "sec_num": "1"}, {"text": "Prompting Multi-Hop QA real-world scenarios. Moreover, FILM-7B maintains a comparable performance on short-context tasks compared with the vanilla backbone model (e.g., 59.3→59.2 accuracy on MMLU (<PERSON><PERSON><PERSON><PERSON> et al., 2020) ). This indicates that the short-context capability of FILM-7B is not compromised during training. Our further analysis explores how the sliding window strategy and the choice of RoPE base θ influence the performance of IN2 training.", "section": "Multiple Segments", "sec_num": null}, {"text": "This section introduces the construction of the dataset for IN2 training and the detailed training process of our model FILM-7B.", "section": "Information-Intensive Training", "sec_num": "2"}, {"text": "Overview. The IN2 training aims to explicitly teach the model that any position in a long context can contain crucial information. To achieve this goal, we construct a long-context question-answer training dataset D = {L i , q i , a i }, where the answer a i to the question q i requires the information contained in some short segments that are randomly placed in the whole long context L i .", "section": "Training Data Construction", "sec_num": "2.1"}, {"text": "Figure 2 illustrates an overview of the data construction process. Specifically, the training data D is constructed based on a general natural language corpus C. Given a raw text C i ∈ C, we first generate a question-answer pair (q i , a i ) using a powerful LLM, then synthesize a long context L i that includes the necessary information from C i and other randomly sampled texts from C. We generate two types of question-answer pairs that require (1) the awareness of fine-grained information in the long context, and (2) the integration and reasoning of information appearing at different positions in the long context. We take the realnewslike subset from the C4 corpus (<PERSON><PERSON> et al., 2020) as C, and take GPT-4-Turbo (OpenAI, 2023b) as the LLM to generate QA pairs.", "section": "Training Data Construction", "sec_num": "2.1"}, {"text": "Fine-grained information awareness. We consider a 128-token segment as the minimum information unit of the context3 . Given a raw text C i , we first randomly extract a 128-token segment s i from it, then generate the q i , a i and L i accordingly, Figure 3 : Three tasks in VAL Probing. The retrieval patterns are determined by the relative positions between the retrieval keywords and the information to be retrieved.", "section": "Training Data Construction", "sec_num": "2.1"}, {"text": "EQUATION", "section": "Training Data Construction", "sec_num": "2.1"}, {"text": "where (q i , a i ) is sampled by prompting the powerful LLM with the segment s i and the instruction I f , ⊕{•} represents the concatenation of the contained segments, and [r j ] are randomly sampled from 128-token segments in C. Note that I f instructs the LLM to make the question-answer pair highly specific to the information provided in s i .", "section": "Training Data Construction", "sec_num": "2.1"}, {"text": "Integration and reasoning of information. Beyond utilizing each single segment, we consider to generate question-answer pairs for information contained in two or more segments. Following the setting of the minimum information unit above, we split a full text C i into a set of 128-token segments [s i ], then generate the q i , a i and L i accordingly,", "section": "Training Data Construction", "sec_num": "2.1"}, {"text": "EQUATION", "section": "Training Data Construction", "sec_num": "2.1"}, {"text": "where I r instructs the LLM to generate a multi-hop question-answer pair that requires the information within at least two segments in learning rate and 3% warm-up steps. The training process is conducted on 16 nodes of 8x80G A100 GPUs with the full sharding strategy and cpu offload strategy implemented by pytorch FSDP (<PERSON> et al., 2023) . One entire training process (for a single FILM-7B model) consumes ∼300 GPU days.", "section": "Training Data Construction", "sec_num": "2.1"}, {"text": "In this section, we first show the preliminary evaluation of FILM-7B on the Needle-in-the-Haystack and discuss about the inadequacies of this probing task. Subsequently, to comprehensively evaluate the long-context information awareness of FILM-7B, we introduce VArious Long-context (VAL) Probing. This includes three tasks that cover various context styles (document, code, and structureddata context) and information retrieval patterns (forward, backward, and bi-directional retrieval). 2 32.1 20.3 59.5 47.5 77.0 47.3 56.2 LongAlign-7B-64K (<PERSON> et al., 2024) 65.3 16.9 39.3 56.0 55.0 36.2 53.2 36.4 LongAlign-13B-64K (<PERSON> et al., 2024) 71.7 13.4 50.8 40.8 82.9 27.0 68.5 27.1 InternLM2-chat-7B (<PERSON><PERSON> et al., 2024) 68.8 18.7 50.2 44.1 61.2 57.1 60.1 40.0 InternLM2-chat-20B (<PERSON><PERSON> et al., 2024) 66.4 27.2 63.4 45.5 74.9 57.2 68.2 43.3 GPT-4-Turbo (OpenAI, 2023b) 81.3 31.7 66.1 46.5 89.6 18.0 79.0 32.1 FILM-7B (ours)", "section": "Long-Context Probing", "sec_num": "3"}, {"text": "85.4 6.1 83.3 18.7 89.0 16.8 85.9 13.9", "section": "Long-Context Probing", "sec_num": "3"}, {"text": "3.1 Near-Perfect Performance on Needle-in-the-Haystack: Are We There Yet?", "section": "Long-Context Probing", "sec_num": "3"}, {"text": "The Needle-in-the-Haystack (<PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2024b) is widely used to assess how robustly a model utilizes information positioned in the long context. It reveals that even some powerful proprietary LLMs, such as GPT-4 and Claude 2.1 (<PERSON><PERSON><PERSON>, 2023) , struggle to fully exploit the information within the long context.", "section": "Long-Context Probing", "sec_num": "3"}, {"text": "We use the Needle-in-the-Haystack task5 to preliminarily evaluate the long-context capability of FILM-7B. Appendix B demonstrates that FILM-7B has achieved near-perfect performance on this task. This result is not surprising as recent open-source LLMs, such as LongAlign (<PERSON> et al., 2024) and InternLM2 (<PERSON><PERSON> et al., 2024) , have also shown near-perfect performance on this task.", "section": "Long-Context Probing", "sec_num": "3"}, {"text": "However, the near-perfect performance on Needle-in-the-Haystack may overestimate the long-context capabilities of LLMs (<PERSON><PERSON> et al., 2024; <PERSON><PERSON><PERSON> et al., 2024) . Specifically, we have the following two concerns:", "section": "Long-Context Probing", "sec_num": "3"}, {"text": "• Needle-in-the-Haystack employs a document-style context, which LLMs could be quite familiar with due to the pre-training on natural language corpora. • The forward retrieval pattern in Needle-in-the-Haystack may simplify the difficulty of information seeking in the long context.", "section": "Long-Context Probing", "sec_num": "3"}, {"text": "The \"forward retrieval\" means that the information being retrieved directly follows the retrieval keyword in a long context. For example, the default question used in Needle-in-the-Haystack is \"What is the best thing to do in San Francisco?\" and the answer is contained in \"The best thing to do in San Francisco is eat a sandwich and sit in Dolores Park on a sunny day.\" The retrieved information \"eat a sandwich and ...\" just follows the retrieval keywords \"best thing to do in San Francisco\".", "section": "Long-Context Probing", "sec_num": "3"}, {"text": "According to the mechanism of induction head (<PERSON><PERSON> et al., 2022) , such a following-up copying is an easily learned pattern for LLMs, thus less challenging for evaluating long context utilization (just like the observation of \"reversal curse\" (<PERSON><PERSON> et al., 2024) ).", "section": "Long-Context Probing", "sec_num": "3"}, {"text": "Given these considerations, we suggest that performances on Needle-in-the-Haystack may not adequately reflect the long-context capabilities of LLMs. Therefore, we propose VAL Probing for a more comprehensive evaluation involving various context styles and retrieval patterns.", "section": "Long-Context Probing", "sec_num": "3"}, {"text": "Our retrieval-based VAL Probing considers three context styles (document, code, and structured-data context) and three retrieval patterns (forward, backward, and bi-directional retrieval). Each context in VAL Probing contains ∼32K tokens, and each task contains ∼3K examples. Figure 3 briefly illustrates the contexts and retrieval instructions in VAL Probing.", "section": "VAL Probing", "sec_num": "3.2"}, {"text": "Document Sentence Retrieval (Bi-Direction). The contexts consist of numerous natural language sentences, and the instruction aims to retrieve a single sentence containing a given piece. The sentences are sampled from the abstracts of papers on arXiv6 . This task follows the bi-directional 5 0  1 0 0  1 5 0  2 0 0  2 5 0  3 0 0  3 5 0  4 0 0  4 5 0  5 0 0  5 5 0  6 0 0  6 5 0  7 0 0  7 5 0  8 0 0 <PERSON>lative Positions in 800 Sentences 5 0  1 0 0  1 5 0  2 0 0  2 5 0  3 0 0  3 5 0  4 0 0  4 5 0  5 0 0  5 5 0  6 0 0  6 5 0  7 0 0  7 5 0  8 0 0 Relative Positions in 800 Functions 5 0  1 0 0  1 5 0  2 0 0  2 5 0  3 0 0  3 5 0  4 0 0  4 5 0  5 0 0  5 5 0  6 0 0  6 5 0  7 0 0  7 5 retrieval pattern, as the expected retrieval results contain words both before and after the given piece in the context. The evaluation metric is the word-level recall score.", "section": "VAL Probing", "sec_num": "3.2"}, {"text": "Code Function Retrieval (Backward). The contexts consist of Python functions, and the instruction aims to retrieve the function name for a given line of code within the function definition. The raw code functions are sampled from the StarCoder (Li et al., 2023c ) dataset7 . We randomly select three lines of definitions for each function. This task follows the backward retrieval pattern, as the function name always precedes the definition. The evaluation metric is the exact-match accuracy.", "section": "VAL Probing", "sec_num": "3.2"}, {"text": "Database Entity Retrieval (Forward). The contexts contain lists of structured entities, each with three fields: ID, label, and description. The query aims to retrieve the label and description for a given ID. The entities are sampled from Wikidata8 . This task follows the forward retrieval pattern, as the label and description follow the ID. We take a relaxed exact-match accuracy as the metric: a 1 score is given if either the label or the description is exactly matched in the response, otherwise a 0 score.", "section": "VAL Probing", "sec_num": "3.2"}, {"text": "We assess the long-context capability of FILM-7B on both probing tasks and real-world long-context tasks. Moreover, we investigate if the performance in short-context scenarios is affected.", "section": "Experiments and Analysis", "sec_num": "4"}, {"text": "Models. We mainly compare FILM-7B with long-context open-source models that have been trained with ≥32K context windows, including the Mistral (<PERSON> et al., 2023), LongChat (Li et al., 2023a) , ChatGLM (Du et al., 2022) , LongAlign (<PERSON> et al., 2024) , <PERSON>Wanjuan (<PERSON><PERSON> et al., 2024) , <PERSON> (AI et al., 2024) and InternLM2 (<PERSON><PERSON> et al., 2024) . We utilize the instruct/chat versions of these models as most of our evaluation tasks are under the zero-shot instruction-following paradigm. We also draw comparisons with popular proprietary LLMs such as GPT-3.5-Turbo (OpenAI, 2023a) and GPT-4-Turbo (OpenAI, 2023b). All models and tasks employ greedy decoding. For probing tasks, we primarily compare FILM-7B with LongAlign and InternLM2 series, as these models have shown near-perfect performances on Needle-in-the-Haystack.", "section": "Experimental Setup", "sec_num": "4.1"}, {"text": "Real-world long-context tasks. We take 9 tasks from the LongBench (<PERSON> et al., 2023) collection to evaluate the long-context capability on real-world scenarios. These tasks encompass long-document question answering (NarrativeQA (<PERSON><PERSON><PERSON><PERSON> et al., 2018) , <PERSON><PERSON><PERSON> (<PERSON><PERSON> et al., 2021) and Multi-FieldQA (MultiFQA) (<PERSON> et al., 2023) , multi-document multi-hop reasoning (HotpotQA (<PERSON> et al., 2018) , 2WikiMultihopQA (2WikiMQA) (<PERSON> et al., 2020) and MuSiQue (<PERSON><PERSON><PERSON> et al., 2022) ), and long-context summarization (GovReport (<PERSON> et al., 2021) , QMSum (<PERSON><PERSON> et al., 2021) and MultiNews (<PERSON><PERSON><PERSON> et al., 2019) ). We employ the middle truncation strategy in LongBench to limit the input within 32K tokens. We report ROUGE-L (<PERSON>, 2004) for summarization tasks and F1 scores for other tasks. The evaluation metrics are computed using the official evaluation scripts9 .", "section": "Experimental Setup", "sec_num": "4.1"}, {"text": "Despite these QA and summarization tasks, we also conduct evaluations on few-shot learning tasks, in which the contexts could also be extremely lengthy if there are many \"few-shot\" examples presented in the context. We take three in-context learning tasks from LongBench, including TREC (Li & Roth, 2002) for few-shot classification, TriviaQA (<PERSON><PERSON> et al., 2017) for few-shot QA, and SAMSum (<PERSON><PERSON><PERSON> et al., 2019) for few-shot summarization.", "section": "Experimental Setup", "sec_num": "4.1"}, {"text": "Short-context tasks. We select 8 short-context tasks commonly used for evaluating the general capabilities of models. These include MMLU (<PERSON><PERSON><PERSON><PERSON> et al., 2020) , BoolQ (<PERSON> et al., 2019) , RACE-High (RACE-H) (<PERSON> et al., 2017) , CommonsenseQA (CSQA) (<PERSON><PERSON><PERSON> et al., 2019) , ARC-Challenge (ARC-C) (<PERSON> et al., 2018) , HellaSwag (Zellers et al., 2019) , GSM8K (<PERSON><PERSON> et al., 2021) , and MATH (<PERSON><PERSON><PERSON><PERSON> et al., 2021) . We use 5-shot for MMLU, 8-shot for GSM8K, 4-shot for MATH, and 0-shot for other tasks. We utilize the lm_eval (<PERSON> et al., 2024) for the evaluations on MMLU, BoolQ, RACE-H, ARC-C and HellaSwag, and use the evaluation scripts from <PERSON> et al. (2024) for other tasks.", "section": "Experimental Setup", "sec_num": "4.1"}, {"text": "FILM-7B significantly mitigates the lost-in-the-middle problem. Figure 4a presents the probing results for both FILM-7B and the backbone model, Mistral-7B-Instruct-v0.2. In all three probing tasks within VAL Probing, the vanilla Mistral model experiences substantial information loss at the middle positions in the long contexts. In contrast, our FILM-7B model consistently exhibits robust FILM-7B achieves performance comparable to, or even outperforming, that of GPT-4-Turbo.", "section": "Main Results and Analysis", "sec_num": "4.2"}, {"text": "Figure 1 illustrates the comparison between FILM-7B and GPT-4-Turbo on our probing tasks.", "section": "Main Results and Analysis", "sec_num": "4.2"}, {"text": "Beyond a qualitative comparison between the performance curves of two models, we quantify the long-context performances on VAL Probing using two metrics:", "section": "Main Results and Analysis", "sec_num": "4.2"}, {"text": "• Average score (Avg). We compute the average performances across the entire context length, reflecting the overall long-context utilization.", "section": "Main Results and Analysis", "sec_num": "4.2"}, {"text": "• Min-max gap (Gap). We calculate the differences between the maximum and minimum performances in Figure 3 . A smaller performance gap signifies greater robustness across different positions. VAL Probing presents a more challenging test suite for long-context models. Figure 4b and 4c show the probing results of LongAlign and InternLM2, two state-of-the-art long-context models.", "section": "Main Results and Analysis", "sec_num": "4.2"}, {"text": "Despite their extended training context windows, these models still encounter the lost-in-the-middle problem. This is particularly noteworthy given their near-perfect performance on the Needle-in-the-Haystack task. This comparison suggests that VAL Probing provides a more challenging evaluation for long-context models.", "section": "Main Results and Analysis", "sec_num": "4.2"}, {"text": "In particular, the results on document and database tasks in VAL Probing demonstrate clear comparisons with Needle-in-the-Haystack. Compared to Needle-in-the-Haystack which uses forward retrieval on natural language context, the document task employs natural language context but with bi-directional retrieval, and the database task uses forward retrieval but with structured-data context. These comparisons highlight that both context styles and retrieval patterns significantly contribute to the hardness of the probing tasks.", "section": "Main Results and Analysis", "sec_num": "4.2"}, {"text": "Training on synthesized long-context data effectively generalizes to real-world scenarios. Table 3 and 4 contain the results on various real-world long-context tasks. It shows that FILM-7B also significantly improves the performance of the backbone model in real-world long-context scenarios. Moreover, it also achieves SOTA-level10 performances on these tasks among ∼7B size open-source models. Notably, the long contexts used in IN2 training are all synthesized from short segments. These improvements suggest that the long-context capabilities learned from the synthesized data can be successfully applied to real-world tasks.", "section": "Main Results and Analysis", "sec_num": "4.2"}, {"text": "FILM-7B maintains the performance on short-context tasks. Figure 6 illustrates the performances of FILM-7B and the vanilla backbone model on short-context tasks. It reveals that the overall performances on short-context tasks are almost comparable with minor variances. These results confirm that FILM-7B does not compromise the short-context capabilities of the backbone model.", "section": "Main Results and Analysis", "sec_num": "4.2"}, {"text": "Analysis on training strategies. We are specifically interested in investigating the impact of the following two training strategies: applying the sliding window and adjusting the position encoding.", "section": "Main Results and Analysis", "sec_num": "4.2"}, {"text": "Due to the page limitation, we provide these further ablations and analysis in Appendix C.", "section": "Main Results and Analysis", "sec_num": "4.2"}, {"text": "Long-context LLMs. Recent research has significantly contributed to the exploration of training large models with extended context windows (<PERSON> et al., 2023; <PERSON> et al., 2022; <PERSON> et al., 2023a; Team et al., 2023; Team, 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2024; AI et al., 2024; <PERSON><PERSON> et al., 2024) . There are primarily two directions in the development of long-context LLMs.", "section": "Related Work", "sec_num": "5"}, {"text": "(1) Data engineering, which emphasizes the construction of long-context data for training the LLMs. This includes data balancing (<PERSON> et al., 2024) , data order arrangement (<PERSON> et al., 2023) , instruction data collection (<PERSON> et al., 2024) , and data quality measurement (<PERSON><PERSON> et al., 2024) . Our IN2 training can be categorized into this field.", "section": "Related Work", "sec_num": "5"}, {"text": "(2) Effective and efficient training, which investigates methods to optimize the training of a long-context model. This encompasses the design of position encoding (<PERSON> et al., 2023a; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023b; <PERSON><PERSON> et al., 2024) , batching strategy (<PERSON> et al., 2024) , parameter-efficient training (<PERSON> et al., 2023b) , and the development of new model architectures (<PERSON><PERSON> et al., 2023a; Gu & Dao, 2023) .", "section": "Related Work", "sec_num": "5"}, {"text": "Long-context evaluations. Existing benchmarks for evaluating long-context models can be divided into two categories. (1) Real-world benchmarks that assess general long-context capabilities (e.g., long-context QA, summarization, and language modeling), such as NarrativeQA (<PERSON><PERSON><PERSON> et al., 2018) , Long<PERSON>ench (<PERSON> et al., 2023) , ZeroSCROLLS (<PERSON><PERSON> et al., 2023) , L-Eval (An et al., 2023) , Loogle (<PERSON> et al., 2023b) , ∞Bench (<PERSON> et al., 2024) , and a series of work on perplexity evaluation (<PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2021; Press et al., 2021; <PERSON> et al., 2023a; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023b; <PERSON> et al., 2023b; <PERSON><PERSON> et al., 2024; <PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2024) . ( 2) Probing tasks that provide a more concise reflection of the long-context utilization across different context lengths and positions. These include Needle-in-the-Haystack, passkey retrieval (<PERSON><PERSON><PERSON><PERSON> & <PERSON>ag<PERSON>, 2024) , synthesized document QA (<PERSON> et al., 2024b) , S3Eval (<PERSON><PERSON> et al., 2024) , Discovery (<PERSON> et al., 2024) , RULER (<PERSON><PERSON><PERSON> et al., 2024) , and the VAL Probing proposed in this study. Among these probing tasks, our VAL Probing is the first to explicitly incorporate a variety of retrieval patterns.", "section": "Related Work", "sec_num": "5"}, {"text": "training on the open-source model, our FILM-7B exhibits significant improvements on probing tasks and real-world long-context tasks while does not compromise the short-context performance. This is the Appendix of the paper: Make Your LLM Fully Utilize the Context.", "section": "This work introduces IN2 training to overcome the lost-in-the-middle problem. By applying IN2", "sec_num": null}, {"text": "To avoid data contamination for the evaluation stage in Section 4, we apply a pre-filtering strategy during sampling the raw texts for constructing the dataset of IN2 training. Specifically, during sampling C i for generating data, if the sampled C i has a 10-gram overlap with any example in all of our evaluation data (including probing tasks, real-world tasks and short-context tasks), it will not be used for neither generating question-answer pairs nor serving as the random segments [r j ].", "section": "A Data Filtering Strategy", "sec_num": null}, {"text": "B Performance on Needle-in-the-Haystack 0 2 0 0 0 4 0 0 0 6 0 0 0 8 0 0 0 1 0 0 0 0 1 2 0 0 0 1 4 0 0 0 1 6 0 0 0 1 8 0 0 0 2 0 0 0 0 2 2 0 0 0 2 4 0 0 0 2 6 0 0 0 2 8 0 0 0 3 0 0 0 0 Token Limit Figure 8 illustrates the performances of models with sliding windows. It shows that in both two settings with sliding windows, the performances drop dramatically when the distance between the 5 0 1 0 0 1 5 0 2 0 0 2 5 0 3 0 0 3 5 0 4 0 0 4 5 0 5 0 0 5 5 0 6 0 0 6 5 0 7 0 0 7 5 0 8 0 0", "section": "A Data Filtering Strategy", "sec_num": null}, {"text": "Relative Positions in 800 Sentences ", "section": "A Data Filtering Strategy", "sec_num": null}, {"text": "Algorithm 1 illustrates how we segment a raw text into ∼128-token segments. We set the ∼128-token segment as the minimum information unit due to the following consideration:", "section": "D Implementation and Reasons for Segmentation", "sec_num": null}, {"text": "• If the segment contains too few tokens (e.g., 16 tokens or 32 tokens), it might not contain enough information for asking a meaningful question.", "section": "D Implementation and Reasons for Segmentation", "sec_num": null}, {"text": "• If we set a large threshold for segmentation (e.g., 1024 tokens or 4096 tokens), most raw texts will just contain one segment11 , thus affecting the construction of QA pairs that require the integration and reasoning of information.", "section": "D Implementation and Reasons for Segmentation", "sec_num": null}, {"text": "• Moreover, we do not just use a full raw text to generate a question with GPT-4, as we are concerned about whether GPT-4 will also more focus on the head and the tail of the text. It could result in a local bias for training: the answers are always placed on the boundaries between two segments containing consecutive information text. Based on the above considerations, we use the ∼128-token segment. Due to the high cost on data generation, we do not conduct further ablations on this design choice. ", "section": "D Implementation and Reasons for Segmentation", "sec_num": null}, {"text": "RULER (<PERSON><PERSON><PERSON> et al., 2024) is a synthetic benchmark that evaluates the effective context length of the long-context LLMs. It revealed that while all existing long-context models with ≤7B sizes claim context size of 32k tokens or greater (except for Llama3), none of them can effectively handle sequence length of 32K by exceeding a qualitative threshold, Llama2-7b performance at 4K (85.6%). 1 0 0 2 0 0 3 0 0 4 0 0 5 0 0 6 0 0 7 0 0 8 0 0 9 0 0 1 0 0 0 1 1 0 0 1 2 0 0 1 3 0 0 1 4 0 0 1 5 0 0 1 6 0 0 1 7 0 0 1 8 0 0", "section": "F Performance on RULER Benchmark", "sec_num": null}, {"text": "Relative Positions in 1800 Sentences Table 7 shows that FILM-7B is the first ≤7B size model that achieves 32K effective context length.", "section": "F Performance on RULER Benchmark", "sec_num": null}, {"text": "Note that for the evaluation on >32K lengths (i.e., 64K and 128K), we use YaRN (<PERSON><PERSON> et al., 2023b) to extend the position embeddings without further fine-tuning.", "section": "F Performance on RULER Benchmark", "sec_num": null}, {"text": "Some existing studies focused on how to extend the context window without further training, such as <PERSON><PERSON><PERSON> (<PERSON> et al., 2023b) . We tried to extend the context window of FILM-7B from 32K to 64K with Yarn and evaluated whether the model can still overcome lost-in-the-middle problem under the extended context length. Figure 9 shows that although FILM-7B outperforms vanilla Mistral-7B on the extended 64K context window, both two models severely suffer the lost-in-the-middle problem. Such an observation indicates that simply extending the position embeddings of the model may not make the model fully utilize the extended context window.", "section": "G Position Extension Suffers Lost-in-the-Middle", "sec_num": null}, {"text": "Example 1: Fine-Grained Information Awareness (1)", "section": "H Training Examples for IN2 Training", "sec_num": null}, {"text": "Segment:", "section": "H Training Examples for IN2 Training", "sec_num": null}, {"text": "Last summer I took a cross-country road trip with a group of people driving different types of electric vehicles.", "section": "H Training Examples for IN2 Training", "sec_num": null}, {"text": "It was an amazing experience, and our trip, our adventures, and our misadventures were portrayed in the documentary Kick Gas. That experience gave me the idea to take my own road trip to experience the freedom of traveling alone on an electric motorcycle. My goals were simple: ride the great motorcycle roads of America, meet other electric motorcycle riders, and visit friends ...", "section": "H Training Examples for IN2 Training", "sec_num": null}, {"text": "What were the primary goals of the individual's solo road trip on an electric motorcycle? Answer:", "section": "Question:", "sec_num": null}, {"text": "The individual's primary goals for the solo road trip on an electric motorcycle were to ride the great motorcycle roads of America, meet other electric motorcycle riders, and visit friends.", "section": "Question:", "sec_num": null}, {"text": "Example 2: Fine-Grained Information Awareness (2)", "section": "Question:", "sec_num": null}, {"text": "Segment:", "section": "Question:", "sec_num": null}, {"text": "Another addition to the kernel is the ar5523 driver, which was started over five years ago for the Atheros USB chipset of the same name. Extensions for supporting more chips and WLAN adapters were added to a number of other drivers; the brcmsmac WLAN driver, for example, now supports the BCM43224 Broadcom chip, while the rt2800usb RaLink driver supports the Sweex LW323 USB WLAN adapter ...", "section": "Question:", "sec_num": null}, {"text": "What new addition to the kernel supports the BCM43224 Broadcom chip? Answer:", "section": "Question:", "sec_num": null}, {"text": "The new addition to the kernel that supports the BCM43224 Broadcom chip is the brcmsmac WLAN driver.", "section": "Question:", "sec_num": null}, {"text": "Example 3: Fine-Grained Information Awareness (3)", "section": "Question:", "sec_num": null}, {"text": "Segment:", "section": "Question:", "sec_num": null}, {"text": "The Arlington County Board plans to vote Saturday afternoon on giving Amazon $23 million and other incentives to build a headquarters campus in Crystal City, but only after hearing scores of northern Virginia residents and advocates testify for or against the project. The five-member board is expected to support the plan, which was announced amid much hoopla on Nov. 13. The proposed county incentives are part of an agreement in which Amazon would occupy significant office space and bring at least 25,000 high-paying jobs to Arlington in coming years. Opponents hope to postpone the vote until after additional public hearings, where they want representatives of the online retail giant to answer questions directly from anyone in the community.", "section": "Question:", "sec_num": null}, {"text": "What is the Arlington County Board's plan regarding Amazon's proposed headquarters in Crystal City? Answer:", "section": "Question:", "sec_num": null}, {"text": "The Arlington County Board is planning to vote on providing Amazon with $23 million and other incentives to construct a headquarters campus in Crystal City. However, this vote will only take place after listening to the opinions of numerous residents and advocates from northern Virginia. The board, which consists of five members, is anticipated to endorse this plan. The proposal includes Amazon occupying a significant amount of office space and introducing at least 25,000 high-paying jobs to Arlington in the forthcoming years.", "section": "Question:", "sec_num": null}, {"text": "Example 4: Integration and Reasoning of Information (1)", "section": "Question:", "sec_num": null}, {"text": "Segment 1: Cloud Cruiser's utilization analytics application enables users to manage and optimize public, private and hybrid cloud usage and spending ...", "section": "Question:", "sec_num": null}, {"text": "This reduces the risk of organizations investing too much or too little in IT, eliminates unused capacity and frees up valuable IT resources for new value-adding projects ... Segment 3: As a Cloud Cruiser customer, we have seen firsthand the value that Cloud Cruiser's technology creates by enabling HPE Flexible Capacity to meter and bill for usage of on-premise IT infrastructure in a pay-as-you-go model ...", "section": "Segment 2:", "sec_num": null}, {"text": "What benefits does the acquisition of Cloud Cruiser bring to HPE and its Flexible Capacity business? Answer:", "section": "Question:", "sec_num": null}, {"text": "The acquisition of Cloud Cruiser enriches HPE's Flexible Capacity business by providing a utilization analytics application that allows users to manage and optimize their cloud usage and spending. Cloud Cruiser's software also facilitates the metering and billing for customers' IT consumption. This enables HPE to offer its customers an on-premise physical IT infrastructure at the cost of cloud services, effectively a rental agreement. Moreover, it reduces the risk of organizations either under or over investing in IT, eliminates unused capacity, and frees up IT resources for new projects ... Filing a false police report and not obeying a legitimate order; to be with her husband in Malta.", "section": "Question:", "sec_num": null}, {"text": "Example 7: Prompt For Equation 1Generate one question and the answer from the given context. The question should be highly specific to the information provided in the context. It should not be a general question that suits any context. Rules to follow when generate the question: 1. The question should be fully answerable from information present in given context. 2. Make sure the question is clear and unambiguous.", "section": "I Prompts For Data Generation and Training", "sec_num": null}, {"text": "3. Phrases like 'based on the provided context', 'according to the context', etc, are not allowed to appear in the question.", "section": "I Prompts For Data Generation and Training", "sec_num": null}, {"text": "Rules to follow when generate the answer:", "section": "I Prompts For Data Generation and Training", "sec_num": null}, {"text": "1. The answer must use the information provided in the context. 2. Do not just copy words from the context. Answer the question in your own words.", "section": "I Prompts For Data Generation and Training", "sec_num": null}, {"text": "### Context ###: si ### Question ###: {completion}", "section": "I Prompts For Data Generation and Training", "sec_num": null}, {"text": "Example 8: Prompt For Equation 2Generate one question and the answer from the given context. The context contains several pieces. Answering the question should require the reader to make multiple logical connections or inferences using **at least two pieces**.", "section": "I Prompts For Data Generation and Training", "sec_num": null}, {"text": "Rules to follow when generate the question: 1. The question should be fully answerable from information present in given context. 2. Make sure the question is clear and unambiguous.", "section": "I Prompts For Data Generation and Training", "sec_num": null}, {"text": "3. Phrases like 'based on the provided context', 'according to the context', etc, are not allowed to appear in the question.", "section": "I Prompts For Data Generation and Training", "sec_num": null}, {"text": "Rules to follow when generate the answer:", "section": "I Prompts For Data Generation and Training", "sec_num": null}, {"text": "1. The answer must use the information provided in the context. Output: ai", "section": "I Prompts For Data Generation and Training", "sec_num": null}, {"text": "The main limitation of this work lies in the insufficient analysis on the choices of training hyperparameters (e.g., learning rate, batch size, training steps and warm-up rate), data construction settings (e.g., data size and data mixture rate) and backbone models (e.g., model sizes and model architectures).", "section": "J Limitations", "sec_num": null}, {"text": "Guidelines:", "section": "J Limitations", "sec_num": null}, {"text": "• The answer NA means that the paper does not include theoretical results.", "section": "J Limitations", "sec_num": null}, {"text": "• All the theorems, formulas, and proofs in the paper should be numbered and cross-referenced.", "section": "J Limitations", "sec_num": null}, {"text": "• All assumptions should be clearly stated or referenced in the statement of any theorems.", "section": "J Limitations", "sec_num": null}, {"text": "• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "section": "J Limitations", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Answer: [Yes] Justification: Section 2, 3 and 4 fully disclose all the information needed to reproduce our experiments.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Guidelines:", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• The answer NA means that the paper does not include experiments.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways. For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. For example (a) If the contribution is primarily a new algorithm, the paper should make it clear how to reproduce that algorithm. (b) If the contribution is primarily a new model architecture, the paper should describe the architecture clearly and fully. (c) If the contribution is a new model (e.g., a large language model), then there should either be a way to access this model for reproducing the results or a way to reproduce the model (e.g., with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility. In the case of closedsource models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? Answer: [No] Justification: The model and evaluation data will be released. Due to internal data release policies, the training data will not be released soon.", "section": "Open access to data and code", "sec_num": "5."}, {"text": "Guidelines:", "section": "Open access to data and code", "sec_num": "5."}, {"text": "• The answer NA means that paper does not include experiments requiring code. • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.", "section": "Open access to data and code", "sec_num": "5."}, {"text": "Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the results?", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Answer: [Yes] Justification: Section 2 and 4 provide all the training and test details.", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Guidelines:", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "• The answer NA means that the paper does not include experiments.", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments?", "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Answer: [Yes] Justification: We show the error bars for the results on probing tasks. For other tasks, there is only one final performance for each task as we use greedy decoding for evaluation.", "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The answer NA means that the paper does not include experiments. • The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors).", "section": "Guidelines:", "sec_num": null}, {"text": "• It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified.", "section": "Guidelines:", "sec_num": null}, {"text": "• The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "section": "Guidelines:", "sec_num": null}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [Yes] Justification: We provide the usage guidelines and disclaimers in our github repo (not released now).", "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [Yes] Justification: The paper and the github repo (not released now) include all necessary citations, URLs, acknowledgements and licenses.", "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators.", "section": "Safeguards", "sec_num": "11."}, {"text": "Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets?", "section": "New Assets", "sec_num": "13."}, {"text": "Answer: [Yes] Justification: The model and probing tasks (to be released) are incorporated with detailed documentations.", "section": "New Assets", "sec_num": "13."}, {"text": "Guidelines:", "section": "New Assets", "sec_num": "13."}, {"text": "• The answer NA means that the paper does not release new assets.", "section": "New Assets", "sec_num": "13."}, {"text": "• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "section": "New Assets", "sec_num": "13."}, {"text": "Appendix D contains the implementation and our considerations for this design choice.", "section": "", "sec_num": null}, {"text": "https://huggingface.co/mistralai/Mistral-7B-Instruct-v0.2.", "section": "", "sec_num": null}, {"text": "https://github.com/gkamradt/LLMTest_NeedleInAHaystack.", "section": "", "sec_num": null}, {"text": "https://info.arxiv.org/help/api/basics.html.", "section": "", "sec_num": null}, {"text": "https://huggingface.co/datasets/bigcode/starcoderdata.", "section": "", "sec_num": null}, {"text": "https://www.wikidata.org/wiki/Wikidata:Data_access.", "section": "", "sec_num": null}, {"text": "https://github.com/THUDM/LongBench.", "section": "", "sec_num": null}, {"text": "The bold numbers in Table3are SOTA-level results among 7B open-source models. Specifically, for each task, we bold the highest result and the results within a margin to the highest one (0.5 for summarization tasks and 2.0 for others).", "section": "", "sec_num": null}, {"text": "The raw texts in realnewslike have an average length of ∼600 tokens with the Mistral tokenizer.", "section": "", "sec_num": null}], "back_matter": [{"text": "We thank all the anonymous reviewers for their valuable comments. <PERSON><PERSON><PERSON> and <PERSON><PERSON> were supported in part by NSFC under grant No. 62088102.We just take the commonly used settings for IN2 training without further searching and analyzing. Intuitively, we believe the change of these settings will not affect the feasibility of IN2 training.", "section": "Acknowledgments", "sec_num": null}, {"text": "This work used pre-trained large language models (i.e., GPT-4 and Mistral-7B) during data construction and training. Therefore, our model may inherit the potential risks of these pre-trained large language models in terms of ethical and safety issues.", "section": "<PERSON>er Impacts", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?Answer: [Yes] Justification: Our main claims made in the abstract and introduction accurately reflect the paper's contributions and scope.Guidelines:• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?Answer: [Yes] Justification: The limitations are discussed in Appendix J.Guidelines:• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach. For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?Answer: [NA] Justification: The paper does not include theoretical results.• For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments?Answer: [Yes] Justification: See Section 2.Guidelines:• The answer NA means that the paper does not include experiments.• The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?Answer: [Yes] Justification: This work conforms with the NeurIPS Code of Ethics.Guidelines:• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?Answer: [Yes] Justification: See Section K.Guidelines:• The answer NA means that there is no societal impact of the work performed.• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster.", "section": "Broader Impacts", "sec_num": "10."}], "ref_entries": {"FIGREF0": {"num": null, "fig_num": "2", "text": "Figure 2: The data construction process for IN2 training, aimed at enhancing the fine-grained information awareness (upper), and the integration and reasoning of information (lower).", "uris": null, "type_str": "figure"}, "FIGREF1": {"num": null, "fig_num": null, "text": "Performance of FILM-7B, InternLM2-chat-7B, and InternLM2-chat-20B.", "uris": null, "type_str": "figure"}, "FIGREF2": {"num": null, "fig_num": "4", "text": "Figure 4: Performance of FILM-7B on VAL Probing and the comparisons with (a) Mistral, (b) LongAlign, and (c) InternLM2. The X-axis is the relative position in the context (∼32K tokens).", "uris": null, "type_str": "figure"}, "FIGREF5": {"num": null, "fig_num": "5", "text": "Figure 5: Compare the performance of IN2 training and general instruction tuning (IT). Both two training process takes the same number of training instances (20% of the full data size, 300K examples). Compare with Mistral + IN2 training, the gains from normal instruction tuning are marginal and unstable.", "uris": null, "type_str": "figure"}, "FIGREF6": {"num": null, "fig_num": "6", "text": "Figure 6: Performances of FILM-7B and the backbone model on short-context tasks.", "uris": null, "type_str": "figure"}, "FIGREF7": {"num": null, "fig_num": "7", "text": "Figure 7: Performances of FILM-7B on Needle-in-the-Haystack.", "uris": null, "type_str": "figure"}, "FIGREF8": {"num": null, "fig_num": "7", "text": "Figure7shows the performance of FILM-7B on Needle-in-the-Haystack. It shows that FILM-7B has achieved near-perfect performance on Needle-in-the-Haystack within its 32K context window.", "uris": null, "type_str": "figure"}, "FIGREF9": {"num": null, "fig_num": "8", "text": "Figure 8: Performance of FILM-7B with a 4K sliding window (SW). PT-IN2: apply the sliding window in both pre-training and IN2 training. IN2: apply the sliding window only in IN2 training.", "uris": null, "type_str": "figure"}, "FIGREF10": {"num": null, "fig_num": "9", "text": "Figure 9: Performance of FILM-7B on 64K context length. The position embeddings are extended through YaRN.", "uris": null, "type_str": "figure"}, "FIGREF11": {"num": null, "fig_num": null, "text": "2. Do not just copy words from the context. Answer the question in your own words. is a context and an instruction. Based on the information provided in the context, write a response for the instruction.", "uris": null, "type_str": "figure"}, "TABREF1": {"num": null, "html": null, "text": "This crucially distinguishes our algorithms from the … Specifically, our modality-missing-aware prompts can … These results demonstrate that there are still a large ... We design better optimizers, a crucial engineering … We present a study of modern architectures applied … This scalability issue is to use of consensus algorithms … Extensive", "content": "<table/>", "type_str": "table"}, "TABREF2": {"num": null, "html": null, "text": "", "content": "<table><tr><td/><td>### Context:</td><td>### Context:</td></tr><tr><td/><td>…</td><td>…</td></tr><tr><td/><td>def get_clause:\\n llen = len(lineup)\\n clause = ''\\n if …</td><td>&lt;id: Q2486402, label: New York State Route 191, … &gt;</td></tr><tr><td/><td>def updateData:\\n if self.train:\\n if self.inplace:\\n self. …</td><td>&lt;id: Q80329096, label: Transverse abdominal incision … &gt;</td></tr><tr><td/><td>def save_comments:\\n for comment in comments:\\n …</td><td>&lt;id: Q70559114, label: Monitoring plasma level of … &gt;</td></tr><tr><td/><td>def plot_patio:\\n ax = plt.subplot(111)\\n passo_x = 1 / …</td><td>&lt;id: Q91568218, label: Progression of the first stage … &gt;</td></tr><tr><td/><td>def encode_label:\\n Label record format:\\n Total: 5 …</td><td>&lt;id: Q84088820, label: Historical perspective of low-… &gt;</td></tr><tr><td/><td>def _parse_array:\\n array = []\\n for child in node. …</td><td>&lt;id: Q63952215, label: Online action-to-perception … &gt;</td></tr><tr><td/><td>def serve_rpc:\\n plugins = [QuarkAsyncPlugin()]\\n rpc =…</td><td>&lt;id: Q40241868, label: Alpha-1-C-octyl-1-deoxynoji-</td></tr><tr><td/><td>def createStrip:\\n story = fetchVign(config)\\n</td><td>rimycin as a pharmacological chaperone for Gaucher</td></tr><tr><td/><td>if specialPlatform == 'android':\\n except Exception as err:</td><td>disease, description: scientific article published on 21</td></tr><tr><td/><td>def breed_childern:\\n self.mutation(first_child)\\n self. …</td><td>August 2006&gt;</td></tr><tr><td>Notably, we achieved the top in highly competitive …</td><td>def get_module_depth:\\n Parameters\\n depth_image: …</td><td>&lt;id: Q5651247, label: Wer, wenn nicht wir, descript … &gt;</td></tr><tr><td>With this, it is shown how approximate FP64x2 GEMM …</td><td>def run_layout:\\n if settings is None:\\n if settings. …</td><td>&lt;id: *********, label: UnZIPping mechanisms of … &gt;</td></tr><tr><td>It is challenging to address widespread and …</td><td>def register:\\n user = None\\n if user_id:\\n if request …</td><td>&lt;id: *********, label: Pursued by genetics: an auto … &gt;</td></tr><tr><td>To verify the effectiveness of the proposed method …</td><td>def test_list_ddl:\\n cursor = con.cursor()\\n result = list( …</td><td>&lt;id: *********, label: Neurological Aspects of … &gt;</td></tr><tr><td>The results show that \\\\emph{GCMiner} significantly …</td><td>def with_laps:\\n with Stopwatch() as sw:\\n for i in …</td><td>&lt;id: *********, label: Unity for Change, description: … &gt;</td></tr><tr><td>Our experimental results on all common benchmark …</td><td>def config_iq_stream:\\n bwActual = c_double(0)\\n …</td><td>&lt;id: *********, label: Hypothetical protein SM_b20 … &gt;</td></tr><tr><td>…</td><td>…</td><td>…</td></tr><tr><td>### Instruction:</td><td>### Instruction:</td><td>### Instruction:</td></tr><tr><td>In above context, which sentence contains the piece</td><td>In above context, which function contains the code snip</td><td>In above context , what is the label and description for</td></tr><tr><td>\"achieving new state-of-the-art performance on all four\"?</td><td>\"if specialPlatform == 'android':\" ?</td><td>the query where the id is Q40241868 ?</td></tr></table>", "type_str": "table"}, "TABREF3": {"num": null, "html": null, "text": "[s i ]. All segments in [s i ] and [r j ] are jointly shuffled, so the required segments may appear far apart in the context.", "content": "<table><tr><td>Context length balance and data mixture. To prevent length bias during IN2 training, we ensure</td></tr><tr><td>the length of the long context L i is evenly distributed from 4K to 32K tokens. Such a length balance</td></tr><tr><td>strategy can be implemented with restricted sampling on [r j ], according to Equation 1 and 2. To</td></tr><tr><td>alleviate catastrophic forgetting on short-context capabilities, we retain ∼10% question-answer pairs</td></tr><tr><td>with the original texts C i instead of converting them into a longer context, and add some general</td></tr><tr><td>instruction-tuning data from the OpenOrca (Lian et al., 2023) dataset.</td></tr><tr><td>Overall, our dataset for IN2 training contains 1.1M long-context data for the fine-grained information</td></tr><tr><td>awareness (∼63%), 300K long-context data for the integration and reasoning of information (∼17%),</td></tr><tr><td>150K short-context question-answer data (∼9%), and 200K general instruction-tuning data (∼11%).</td></tr><tr><td>Appendix I contains the handcraft instructions for data generation. Appendix H illustrates some</td></tr><tr><td>examples of our constructed long-context QA data. Appendix A describes the filtering strategy to</td></tr><tr><td>avoid data contamination for evaluation.</td></tr><tr><td>2.2 Training Details</td></tr><tr><td>Using the training data constructed above, we further fine-tune the Mistral-7B-Instruct-v0.2 4 (Jiang</td></tr><tr><td>et al., 2023) to get our FILM-7B (FILl-in-the-Middle). We perform IN2 training in the instruction-</td></tr><tr><td>tuning paradigm: the long contexts and questions are used as instructions, and the loss on the answer</td></tr><tr><td>parts are used to update the model. Appendix I contains the system template used for formatting</td></tr><tr><td>the training data. For hyper-parameters, we set the global batch size as 128 and conduct one-epoch</td></tr><tr><td>training with ∼14K training steps. We use the cosine learning rate decay with a 1e-6 maximum</td></tr></table>", "type_str": "table"}, "TABREF4": {"num": null, "html": null, "text": "Quantified performances of various models on VAL Probing.", "content": "<table><tr><td>Model</td><td colspan=\"5\">Document Avg Gap↓ Avg Gap↓ Avg Gap↓ Avg Gap↓ Code Database All</td></tr><tr><td colspan=\"2\">Mistral-7B-Instruct-v0.1 (<PERSON> et al., 2023) 44.8 29.9</td><td>6.8</td><td>53.2</td><td>8.8</td><td>74.5 20.1 52.5</td></tr><tr><td colspan=\"2\">Mistral-7B-Instruct-v0.2 (<PERSON> et al., 2023) 74.</td><td/><td/><td/></tr></table>", "type_str": "table"}, "TABREF5": {"num": null, "html": null, "text": "Quantified comparison between IN2 training and normal instruction tuning.", "content": "<table><tr><td>Model</td><td>Document Avg Gap↓ Avg Gap↓ Avg Gap↓ Avg Gap↓ Code Database All</td></tr><tr><td>Mistral-7B-Instruct-v0.2 (<PERSON> et al., 2023)</td><td>74.2 32.1 20.3 59.5 47.5 77.0 47.3 56.2</td></tr><tr><td colspan=\"2\">+ Normal Instruction Tuning (Lian et al., 2023) 69.0 25.9 30.2 76.5 53.4 54.4 50.9 52.3</td></tr><tr><td>+ Information-Intensive Training (ours)</td><td>82.9 11.5 74.5 27.7 83.5 31.6 80.3 23.6</td></tr></table>", "type_str": "table"}, "TABREF6": {"num": null, "html": null, "text": "Performances of various models on real-world long-context tasks. Results of models with * are reported in<PERSON><PERSON> et al. (2023) and<PERSON><PERSON> et al. (2024).", "content": "<table><tr><td>Model</td><td colspan=\"10\">NarrativeQA Qasper MultiFQA HotpotQA 2WikiMQA MuSiQue GovReport QMSum MultiNews Avg</td></tr><tr><td/><td/><td/><td>Close-Source</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>GPT-4-Turbo (OpenAI, 2023b)</td><td>33.0</td><td>50.7</td><td>52.7</td><td>68.5</td><td>64.3</td><td>49.1</td><td>33.9</td><td>25.4</td><td>24.9</td><td>44.7</td></tr><tr><td>GPT-3.5-Turbo  *  (OpenAI, 2023a)</td><td>23.6</td><td>43.3</td><td>52.3</td><td>51.6</td><td>37.7</td><td>26.9</td><td>29.5</td><td>23.4</td><td>26.7</td><td>35.0</td></tr><tr><td/><td/><td/><td>Open-Source</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>LongChat-v1.5-7B-32K  *  (Li et al., 2023a)</td><td>16.9</td><td>27.7</td><td>41.4</td><td>31.5</td><td>20.6</td><td>9.7</td><td>30.8</td><td>22.7</td><td>26.4</td><td>25.3</td></tr><tr><td>ChatGLM2-6B-32K  *  (Du et al., 2022)</td><td>21.1</td><td>31.5</td><td>46.2</td><td>25.3</td><td>20.8</td><td>9.8</td><td>32.4</td><td>24.0</td><td>26.5</td><td>26.4</td></tr><tr><td>LongAlign-7B-64K (Bai et al., 2024)</td><td>18.7</td><td>33.8</td><td>49.1</td><td>28.6</td><td>23.4</td><td>12.5</td><td>30.6</td><td>23.7</td><td>27.5</td><td>27.5</td></tr><tr><td>Mistral-7B-Instruct-v0.1 (Jiang et al., 2023)</td><td>19.6</td><td>33.2</td><td>38.8</td><td>42.9</td><td>31.2</td><td>17.4</td><td>27.5</td><td>22.4</td><td>26.6</td><td>28.9</td></tr><tr><td>Mistral-7B-Instruct-v0.2 (Jiang et al., 2023)</td><td>23.5</td><td>33.8</td><td>45.9</td><td>42.4</td><td>24.3</td><td>20.8</td><td>33.3</td><td>24.8</td><td>26.8</td><td>30.6</td></tr><tr><td>Yi-6B-200K  *  (AI et al., 2024)</td><td>12.4</td><td>26.4</td><td>36.8</td><td>46.6</td><td>40.4</td><td>25.8</td><td>29.3</td><td>20.7</td><td>27.1</td><td>29.5</td></tr><tr><td>ChatGLM3-6B-32K  *  (Du et al., 2022)</td><td>9.2</td><td>43.1</td><td>50.9</td><td>55.3</td><td>43.7</td><td>38.9</td><td>36.0</td><td>24.7</td><td>27.4</td><td>36.6</td></tr><tr><td>InternLM2-chat-7B (Cai et al., 2024)</td><td>24.4</td><td>35.4</td><td>50.2</td><td>52.4</td><td>48.2</td><td>30.5</td><td>33.6</td><td>25.3</td><td>29.0</td><td>36.5</td></tr><tr><td>InternLM2-7B-LongWanjuan  *  (Lv et al., 2024)</td><td>29.9</td><td>39.6</td><td>50.2</td><td>53.7</td><td>42.3</td><td>32.1</td><td>33.0</td><td>25.5</td><td>27.8</td><td>37.1</td></tr><tr><td>FILM-7B (ours)</td><td>26.9</td><td>42.2</td><td>56.0</td><td>62.1</td><td>47.0</td><td>39.0</td><td>33.8</td><td>25.1</td><td>26.9</td><td>39.9</td></tr></table>", "type_str": "table"}, "TABREF7": {"num": null, "html": null, "text": "Model performances on few-shot learning tasks.", "content": "<table><tr><td>Model</td><td colspan=\"4\">TREC TriviaQA SAMSum Average</td></tr><tr><td>GPT4-Turbo (OpenAI, 2023b)</td><td>77.0</td><td>91.7</td><td>39.7</td><td>69.5</td></tr><tr><td colspan=\"2\">Mistral-7B-Instruct-v0.2 (<PERSON> et al., 2023) 71.0</td><td>84.5</td><td>35.8</td><td>63.8</td></tr><tr><td>FILM-7B (ours)</td><td>76.0</td><td>90.0</td><td>39.5</td><td>68.5</td></tr></table>", "type_str": "table"}, "TABREF8": {"num": null, "html": null, "text": "Considering that FILM-7B additionally uses instruction-tuning-style data for post-training, to further demonstrate the effectiveness of IN2 training, here we present more controlled experiments to compare IN2 training and normal instruction tuning under the same training data size. Specifically, for both IN2 training and normal instruction tuning, we take the same backbone model (i.e., Mistral-7B-Instruct-v0.2) and the same number of post-training instances (20% of our full data size, ∼300K examples).The data for normal instruction tuning are randomly sampled from OpenOrca(<PERSON><PERSON> et al., 2023). Comparisons shown in Figure5shows the performance curves on VAL Probing after two training processes, and Table2contains the quantified results. These comparisons clearly demonstrate that IN2 training can effectively alleviate the lost-in-the-middle problem while the normal instruction tuning cannot.", "content": "<table/>", "type_str": "table"}, "TABREF9": {"num": null, "html": null, "text": "Performance of FILM-7B with different RoPE base θ during IN2 training.", "content": "<table><tr><td>Model</td><td>RoPE Base θ</td><td colspan=\"3\">Document Avg Gap↓ Avg Gap↓ Avg Gap↓ Avg Gap↓ Code Database All</td></tr><tr><td/><td colspan=\"4\">1.0 × 10 6 (default) 82.9 11.5 74.5 27.7 83.5 31.6 80.3 23.6</td></tr><tr><td>FILM-7B (20%)</td><td>2.0 × 10 6 1.0 × 10 7</td><td>83.9 83.7</td><td>9.3 7.6</td><td>79.8 27.1 87.7 13.2 83.8 16.5 81.7 18.4 89.4 16.8 84.9 14.3</td></tr><tr><td/><td>1.0 × 10 8</td><td>84.6</td><td>6.6</td><td>81.4 22.3 87.7 13.2 84.6 14.0</td></tr></table>", "type_str": "table"}, "TABREF10": {"num": null, "html": null, "text": "Algorithm 1 Implementation of Raw Text Segmentation Performance of FILM-7B with different training data sizes for IN2 training.", "content": "<table><tr><td colspan=\"2\">Given:</td><td/><td/></tr><tr><td/><td>Ci: The raw text;</td><td/><td/></tr><tr><td/><td colspan=\"4\"><PERSON><PERSON><PERSON><PERSON><PERSON>(•): The tokenizer of the model;</td></tr><tr><td/><td colspan=\"4\">l = 128: The minimal length of each segment;</td></tr><tr><td colspan=\"2\">Return:</td><td/><td/></tr><tr><td/><td colspan=\"3\">Si = [s 1 i , s 2 i , ...]: A set of segments;</td></tr><tr><td colspan=\"2\">1: Si = [ ]</td><td/><td/></tr><tr><td colspan=\"2\">2: Pi = Split(Ci, '\\n')</td><td/><td/></tr><tr><td colspan=\"2\">3: temp_list = [ ]</td><td/><td/></tr><tr><td colspan=\"2\">4: temp_length = 0</td><td/><td/></tr><tr><td colspan=\"5\">5: for p j i ∈ Pi do 6: length = Len(Tokenizer(p j i )) temp_list.append(p j i ) temp_length += length</td></tr><tr><td>7:</td><td colspan=\"2\">if temp_length &gt;= l then</td><td/></tr><tr><td>8:</td><td colspan=\"4\">temp_segment = '\\n'.join(temp_list)</td></tr><tr><td>9:</td><td colspan=\"2\">Si.append(temp_segment)</td><td/></tr><tr><td>10:</td><td>temp_list = [ ]</td><td/><td/></tr><tr><td>11:</td><td colspan=\"2\">temp_length = 0</td><td/></tr><tr><td>12:</td><td>else</td><td/><td/></tr><tr><td>13:</td><td>continue</td><td/><td/></tr><tr><td>14:</td><td>end if</td><td/><td/></tr><tr><td colspan=\"2\">15: end for</td><td/><td/></tr><tr><td colspan=\"3\">16: if temp_list is not empty then</td><td/></tr><tr><td>17:</td><td colspan=\"3\">temp_list = [Si.pop()] + temp_list</td></tr><tr><td>18:</td><td colspan=\"3\">temp_segment = '\\n'.join(temp_list)</td></tr><tr><td>19:</td><td colspan=\"2\">Si.append(temp_segment)</td><td/></tr><tr><td colspan=\"2\">20: end if</td><td/><td/></tr><tr><td colspan=\"2\">21: return Si</td><td/><td/></tr><tr><td/><td>Model</td><td>Data Size</td><td colspan=\"2\">Document Avg Gap↓ Avg Gap↓ Avg Gap↓ Avg Gap↓ Code Database All</td></tr><tr><td/><td/><td>100%</td><td>85.4</td><td>6.1</td><td>83.3 18.7 89.0 16.8 85.9 13.9</td></tr><tr><td/><td/><td>50%</td><td colspan=\"2\">84.2 13.3 80.5 21.5 89.7 15.3 84.8 16.7</td></tr><tr><td/><td>FILM-7B</td><td>20%</td><td colspan=\"2\">82.9 11.5 74.5 27.7 83.5 31.6 80.3 23.6</td></tr><tr><td/><td/><td>10%</td><td colspan=\"2\">84.3 11.3 75.2 31.8 82.3 32.7 80.6 25.3</td></tr><tr><td/><td/><td>1%</td><td colspan=\"2\">76.2 18.8 63.3 48.0 70.9 36.7 70.1 34.5</td></tr></table>", "type_str": "table"}, "TABREF11": {"num": null, "html": null, "text": "Table 6 shows the performance of FILM-7B with different training data sizes for IN2 training. Generally, with the data size increasing, the average performance increases and the performance variance in different positions decreases. Such trends are more significant on code and document probing tasks. Note that the training data for IN2 training are almost from natural language corpus. It indicates that increasing the training data size can better help the generalization of long-context capability on different context styles.", "content": "<table/>", "type_str": "table"}, "TABREF12": {"num": null, "html": null, "text": "Performances (%) of ≤7B models on RULER benchmark. The performance exceeding the threshold (i.e., Llama2-7B on 4K length) is underlined.", "content": "<table><tr><td>Model</td><td colspan=\"2\">Claimed Effective</td><td>4K</td><td colspan=\"6\">8K 16K 32K 64K 128K Avg.</td></tr><tr><td>Llama2-7B (Touvron et al., 2023)</td><td>4K</td><td/><td>85.6</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td></tr><tr><td>LongChat-7B (Li et al., 2023a)</td><td>32K</td><td>&lt;4K</td><td colspan=\"5\">84.7 79.9 70.8 59.3 0.0</td><td>0.0</td><td>49.1</td></tr><tr><td>Together-7B (Together.AI, 2023)</td><td>32K</td><td>4K</td><td colspan=\"5\">88.2 81.1 69.4 63.0 0.0</td><td>0.0</td><td>50.3</td></tr><tr><td>Phi3-3B (Abdin et al., 2024)</td><td>128K</td><td>4K</td><td colspan=\"6\">86.7 78.1 75.6 70.3 58.9 43.3</td><td>68.8</td></tr><tr><td>LWM-7B (Liu et al., 2024a)</td><td>1M</td><td>&lt;4K</td><td colspan=\"6\">82.3 78.4 73.7 69.1 68.1 65.0</td><td>72.8</td></tr><tr><td>ChatGLM3-6B (Du et al., 2022)</td><td>128K</td><td>4K</td><td colspan=\"6\">87.8 83.4 78.6 69.9 56.0 42.0</td><td>69.6</td></tr><tr><td>Mistral-7B (Jiang et al., 2023)</td><td>32K</td><td>16K</td><td colspan=\"6\">93.6 91.2 87.2 75.4 49.0 13.8</td><td>68.4</td></tr><tr><td>FILM-7B (ours)</td><td>32K</td><td>32K</td><td colspan=\"6\">92.8 88.2 88.1 86.9 70.1 27.1</td><td>75.5</td></tr></table>", "type_str": "table"}, "TABREF13": {"num": null, "html": null, "text": "A court presided by Magistrate <PERSON> explained in its judgement of 17 August, 2016 in The Police v <PERSON><PERSON>is <PERSON>, that the charges of a false criminal report and calumnious accusations may be factually similar, however, from a legal point of view they are two separate and distinct charges. The accused, <PERSON><PERSON><PERSON> was charged with having filed a false police report and with not obeying a legitimate order. She admitted these charges ...Segment 2:In passing judgement the court took into consideration that the accused admitted to the charges immediately and also that she did what she did in order to be with her husband in Malta. Magistrate <PERSON><PERSON><PERSON> referred to what <PERSON> said last June, where today 2019s information technology brings suffering of others instantly, but we also become immune to tragedies and sufferings ...", "content": "<table><tr><td>Example 5: Integration and Reasoning of Information (2)</td></tr><tr><td>Segment 1:</td></tr><tr><td><PERSON>, 45, of 38 Roles St., Haines City, was charged with retail theft at Beall's at 5998</td></tr><tr><td>Cypress Gardens Blvd ...</td></tr><tr><td>Segment 2:</td></tr><tr><td><PERSON>, 18, of 108 Hilltop Drive, Winter Haven, was charged with retail theft at Macy's</td></tr><tr><td>at 700 Third St., S.W. ...</td></tr><tr><td>Question:</td></tr><tr><td>Who was charged with retail theft at Beall's and who was charged with the same crime at Macy's?</td></tr><tr><td>Answer:</td></tr><tr><td><PERSON> and <PERSON></td></tr><tr><td>Example 6: Integration and Reasoning of Information (3)</td></tr><tr><td>Segment 1:</td></tr><tr><td>Question:</td></tr><tr><td>What were the two charges Eebis Getu admitted to, and what was her reason for committing these actions</td></tr><tr><td>according to the court's judgement?</td></tr><tr><td>Answer:</td></tr></table>", "type_str": "table"}, "TABREF14": {"num": null, "html": null, "text": "• Please see the NeurIPS code and data submission guidelines (https://nips.cc/public/ guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https://nips.cc/public/ guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).", "content": "<table/>", "type_str": "table"}, "TABREF15": {"num": null, "html": null, "text": "• At submission time, remember to anonymize your assets (if applicable). You can either create an anonymized URL or include an anonymized zip file. 14. Crowdsourcing and Research with Human Subjects Question: For crowdsourcing experiments and research with human subjects, does the paper include the full text of instructions given to participants and screenshots, if applicable, as well as details about compensation (if any)? Answer: [NA] Justification: The paper does not involve crowdsourcing nor research with human subjects. Guidelines: • The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Including this information in the supplemental material is fine, but if the main contribution of the paper involves human subjects, then as much detail as possible should be included in the main paper. • According to the NeurIPS Code of Ethics, workers involved in data collection, curation, or other labor should be paid at least the minimum wage in the country of the data collector. 15. Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects Question: Does the paper describe potential risks incurred by study participants, whether such risks were disclosed to the subjects, and whether Institutional Review Board (IRB) approvals (or an equivalent approval/review based on the requirements of your country or institution) were obtained? Answer: [NA] Justification: The paper does not involve crowdsourcing nor research with human subjects. Guidelines: • The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper.", "content": "<table/>", "type_str": "table"}}}}