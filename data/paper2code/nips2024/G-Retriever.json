{"paper_id": "G-Retriever", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:27:44.123331Z"}, "title": "G-Retriever: Retrieval-Augmented Generation for Textual Graph Understanding and Question Answering", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": "", "affiliation": {"laboratory": "", "institution": "National University of Singapore", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Tian", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Notre Dame", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": "", "affiliation": {"laboratory": "", "institution": "National University of Singapore", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["V"], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Notre Dame", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Loyola Marymount University", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Lecun", "suffix": "", "affiliation": {"laboratory": "", "institution": "New York University", "location": {"addrLine": "5 Meta AI"}}, "email": ""}, {"first": "Xavier", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "National University of Singapore", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "National University of Singapore", "location": {}}, "email": "<EMAIL>"}], "year": "", "venue": null, "identifiers": {}, "abstract": "Given a graph with textual attributes, we enable users to 'chat with their graph': that is, to ask questions about the graph using a conversational interface. In response to a user's questions, our method provides textual replies and highlights the relevant parts of the graph. While existing works integrate large language models (LLMs) and graph neural networks (GNNs) in various ways, they mostly focus on either conventional graph tasks (such as node, edge, and graph classification), or on answering simple graph queries on small or synthetic graphs. In contrast, we develop a flexible question-answering framework targeting real-world textual graphs, applicable to multiple applications including scene graph understanding, common sense reasoning, and knowledge graph reasoning. Toward this goal, we first develop a Graph Question Answering (GraphQA) benchmark with data collected from different tasks. Then, we propose our G-Retriever method, introducing the first retrievalaugmented generation (RAG) approach for general textual graphs, which can be fine-tuned to enhance graph understanding via soft prompting. To resist hallucination and to allow for textual graphs that greatly exceed the LLM's context window size, G-Retriever performs RAG over a graph by formulating this task as a Prize-Collecting Steiner Tree optimization problem. Empirical evaluations show that our method outperforms baselines on textual graph tasks from multiple domains, scales well with larger graph sizes, and mitigates hallucination. Our codes and datasets are available at: https://github.com/XiaoxinHe/G-Retriever.", "pdf_parse": {"paper_id": "G-Retriever", "_pdf_hash": "", "abstract": [{"text": "Given a graph with textual attributes, we enable users to 'chat with their graph': that is, to ask questions about the graph using a conversational interface. In response to a user's questions, our method provides textual replies and highlights the relevant parts of the graph. While existing works integrate large language models (LLMs) and graph neural networks (GNNs) in various ways, they mostly focus on either conventional graph tasks (such as node, edge, and graph classification), or on answering simple graph queries on small or synthetic graphs. In contrast, we develop a flexible question-answering framework targeting real-world textual graphs, applicable to multiple applications including scene graph understanding, common sense reasoning, and knowledge graph reasoning. Toward this goal, we first develop a Graph Question Answering (GraphQA) benchmark with data collected from different tasks. Then, we propose our G-Retriever method, introducing the first retrievalaugmented generation (RAG) approach for general textual graphs, which can be fine-tuned to enhance graph understanding via soft prompting. To resist hallucination and to allow for textual graphs that greatly exceed the LLM's context window size, G-Retriever performs RAG over a graph by formulating this task as a Prize-Collecting Steiner Tree optimization problem. Empirical evaluations show that our method outperforms baselines on textual graph tasks from multiple domains, scales well with larger graph sizes, and mitigates hallucination. Our codes and datasets are available at: https://github.com/XiaoxinHe/G-Retriever.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Graphs and Large Language Models (LLMs). The advent of LLMs has significantly shaped the artificial intelligence landscape. As these models are applied to increasingly diverse tasks, their ability to process complex structured data will be increasingly vital. In particular, in our interconnected world, a significant portion of real-world data inherently possesses a graph structure, such as the Web, e-commerce, recommendation systems, knowledge graphs, and many others. Moreover, many of these involve graphs with textual attributes (i.e., textual graphs), making them well-suited for LLM-centric methods. This has spurred interest in combining graph-based technologies, particularly graph neural networks (GNNs), with LLMs to enhance their reasoning on graphs [44, 15, 24] .", "cite_spans": [{"start": 764, "end": 768, "text": "[44,", "ref_id": "BIBREF43"}, {"start": 769, "end": 772, "text": "15,", "ref_id": "BIBREF14"}, {"start": 773, "end": 776, "text": "24]", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "The Present Work: Enabling 'Chat With Your Graph'. While existing works integrate LLMs and GNNs in various ways, they mostly focus on conventional graph tasks such as node, edge and graph", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "GraphQA New Architecture", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Benchmark", "sec_num": null}, {"text": "Graph Prompt Tuning Graph Textualization", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Retrieval Augmented Generation", "sec_num": null}, {"text": "Sure! Here's an example of a 500-word advertisement for the place based on the scene graph: \"Experience the breathtaking beauty of our place, where the rolling hills meet the sparkling water of the swimming pool. Take a leisurely stroll through the lush green elds, and let the gentle breeze caress your face as you gaze out at the picturesque view. The sheep graze peacefully in the distance, their white coats glistening in the sunlight. The sky is a brilliant blue, with u y white clouds drifting lazily across. Come and bask in the serene ambiance of our place, where the natural beauty will leave you in awe.\" The explanation graph provides a detailed analysis of the reasons why women undergo cosmetic surgery. According to the graph, the main reasons include the desire to change one's appearance, the in uence of societal beauty standards, and the addiction to searching for perfection. These reasons are further supported by the various nodes and edges in the graph, which provide additional insights into the motivations and consequences of cosmetic surgery. Firstly, the node labeled \"human\" highlights the fundamental human desire to feel attractive and con dent in one's appearance... Write a 500-word argument essay based on the information provided in the explanation graph.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Retrieval Augmented Generation", "sec_num": null}, {"text": "Write an 500-word advertisement for this place in the scene graph that would make people want to visit it.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "Great! Based on the provided knowledge graph, here's an educational tour that highlights interesting facts and connections related to <PERSON>'s inventions and other notable achievements:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "1. <PERSON>'s Lightning Rod: Start by exploring <PERSON>'s most famous invention -the lightning rod. Discover how <PERSON>'s invention protected buildings from lightning strikes and how it changed the way people understood electricity... What else did ben franklin invent? Surrounding this question, develop an educational tour through the knowledge graph, highlighting interesting facts and connections in an engaging way. classification [8] , or answering simple questions on small or synthetic graphs [44, 31] . In contrast, we develop a flexible question-answering framework targeting complex and real-world graphs. This framework enables users to 'chat with their graph' via a unified conversational interface, representing a leap towards intuitive interaction with graph data, as demonstrated in Figure 1 .", "cite_spans": [{"start": 439, "end": 442, "text": "[8]", "ref_id": "BIBREF7"}, {"start": 504, "end": 508, "text": "[44,", "ref_id": "BIBREF43"}, {"start": 509, "end": 512, "text": "31]", "ref_id": "BIBREF30"}], "ref_spans": [{"start": 809, "end": 810, "text": "1", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "The Need for a Comprehensive GraphQA Benchmark. Question answering (QA) is a fundamentally important task in natural language processing, serving as a key benchmark for assessing LLMs and providing a unified interface for various capabilities. Despite extensive research in QA, a comprehensive benchmark specifically tailored for the graph modality is lacking. In contrast to existing benchmarks that focus on basic graph-based reasoning tasks such as node degree, edge existence, and shortest path [6, 44] , our benchmark addresses complex and real-world graph applications including common sense reasoning, scene understanding, and knowledge graph reasoning (refer to Figure 2 ). This is vital for measuring progress toward a model capable of answering a wide range of questions about graphs from diverse applications.", "cite_spans": [{"start": 499, "end": 502, "text": "[6,", "ref_id": "BIBREF5"}, {"start": 503, "end": 506, "text": "44]", "ref_id": "BIBREF43"}], "ref_spans": [{"start": 677, "end": 678, "text": "2", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "New Architecture for GraphQA. To enable effective and efficient graph QA, even on large graphs, we propose G-Retriever, a new framework combining the strengths of GNNs, LLMs, and RAG (Figure 3 ). Next, we will discuss the motivation, strengths, and details of our model.", "cite_spans": [], "ref_spans": [{"start": 191, "end": 192, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "Tackling Hallucination in Graph LLMs. LLMs are prone to hallucination, a phenomenon where the generated content is factually inaccurate or nonsensical [12] . We validate the presence of this issue in graph settings. In particular, we employ a baseline method that adapts MiniGPT-4 [57] to graphs, where a frozen LLM interacts with a trainable GNN that encodes graph data as a soft prompt, as in GraphToken [31] . Our findings, shown in Table 1 , indicate that hallucination, an important problem in text-based LLMs, is also prevalent in Graph LLMs. This may be attributed to the baseline's inability to recall the entire graph structure from a single graph embedding, leading to the generation of incorrect nodes or edges during the QA task. In contrast, by employing RAG for direct information retrieval from the actual graph, our G-Retriever mitigates this issue, as substantiated by Table 1 .", "cite_spans": [{"start": 151, "end": 155, "text": "[12]", "ref_id": "BIBREF11"}, {"start": 281, "end": 285, "text": "[57]", "ref_id": "BIBREF56"}, {"start": 406, "end": 410, "text": "[31]", "ref_id": "BIBREF30"}], "ref_spans": [{"start": 442, "end": 443, "text": "1", "ref_id": "TABREF0"}, {"start": 892, "end": 893, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "Enhancing Scalability and Efficiency in Graph LLMs. Recent research endeavors have explored translating graphs into natural language, such as by flattening nodes and edges into a text sequence, enabling their processing by LLMs for graph-based tasks [56, 6] . However, this method faces critical scalability issues. Converting a graph with thousands of nodes and edges into a text sequence results in an excessive number of tokens, surpassing the input capacity of many LLMs. An alternative of truncating the graph text sequence to fit the LLM's input token limit leads to loss of information and response quality. G-Retriever overcomes these issues with its RAG component, which allows for effective scaling to larger graphs by selectively retrieving only relevant parts of the graph.", "cite_spans": [{"start": 250, "end": 254, "text": "[56,", "ref_id": "BIBREF55"}, {"start": 255, "end": 257, "text": "6]", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "Tailoring the RAG Approach to Graphs. Existing RAG methodologies are primarily designed for simpler data types or knowledge graphs, where information is retrieved in a manner isolated from the graph structure [7, 1, 36, 16] . Hence, we introduce a new retrieval approach for general textual graphs. Notably, we formulate subgraph retrieval as a Prize-Collecting Steiner Tree (PCST) Based on the information provided in the graph, the animal in the bushes is an elephant. The nodes that support this answer are: * Node 1: The elephant is mentioned in the first line of the graph. The edges that support this answer are: * The edge connects the elephant to the bushes, indicating that the elephant is in the bushes.", "cite_spans": [{"start": 209, "end": 212, "text": "[7,", "ref_id": "BIBREF6"}, {"start": 213, "end": 215, "text": "1,", "ref_id": "BIBREF0"}, {"start": 216, "end": 219, "text": "36,", "ref_id": "BIBREF35"}, {"start": 220, "end": 223, "text": "16]", "ref_id": "BIBREF15"}], "ref_spans": [], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "Therefore, the answer to the question is: The animal in the bushes is an elephant. optimization problem, which takes the neighborhood information into account during retrieval. This also allows the return of a subgraph most relevant to a query, thereby improving explainability.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "The contributions of this paper are outlined as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "• Pioneering the integration of Graph RAG. We present the first retrieval approach for general textual graph tasks, which greatly enhances scalability and efficiency.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "• Enabling 'Chat with Your Graph'. We develop a flexible question answering framework to handle complex and real-world textual graphs through a unified conversational interface.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "• Introducing A Novel GraphQA Benchmark. We introduce a diverse benchmark targeted at real-world graph question answering, filling a crucial research gap.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "• Empirical Findings. We demonstrate the efficiency and effectiveness of G-Retriever in multiple domains and present the significant finding of hallucination in graph LLMs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Unified Conversational Interface", "sec_num": null}, {"text": "Graphs and Large Language Models. A significant body of research has emerged at the intersection of graph-based techniques and LLMs [30, 24, 15, 44, 54] . This exploration spans diverse aspects, ranging from the design of general graph models [47, 25, 51, 19, 40, 31] , and multi-modal architectures [23, 49] to practical applications. Noteworthy applications include fundamental graph reasoning [52, 3, 56] , node classification [8, 11, 39, 5, 50, 4, 33] , graph classification/regression [32, 55] , and leveraging LLMs for knowledge graph-related tasks [41, 14, 29] .", "cite_spans": [{"start": 132, "end": 136, "text": "[30,", "ref_id": "BIBREF29"}, {"start": 137, "end": 140, "text": "24,", "ref_id": "BIBREF23"}, {"start": 141, "end": 144, "text": "15,", "ref_id": "BIBREF14"}, {"start": 145, "end": 148, "text": "44,", "ref_id": "BIBREF43"}, {"start": 149, "end": 152, "text": "54]", "ref_id": "BIBREF53"}, {"start": 243, "end": 247, "text": "[47,", "ref_id": "BIBREF46"}, {"start": 248, "end": 251, "text": "25,", "ref_id": "BIBREF24"}, {"start": 252, "end": 255, "text": "51,", "ref_id": "BIBREF50"}, {"start": 256, "end": 259, "text": "19,", "ref_id": "BIBREF18"}, {"start": 260, "end": 263, "text": "40,", "ref_id": "BIBREF39"}, {"start": 264, "end": 267, "text": "31]", "ref_id": "BIBREF30"}, {"start": 300, "end": 304, "text": "[23,", "ref_id": "BIBREF22"}, {"start": 305, "end": 308, "text": "49]", "ref_id": "BIBREF48"}, {"start": 396, "end": 400, "text": "[52,", "ref_id": "BIBREF51"}, {"start": 401, "end": 403, "text": "3,", "ref_id": "BIBREF2"}, {"start": 404, "end": 407, "text": "56]", "ref_id": "BIBREF55"}, {"start": 430, "end": 433, "text": "[8,", "ref_id": "BIBREF7"}, {"start": 434, "end": 437, "text": "11,", "ref_id": "BIBREF10"}, {"start": 438, "end": 441, "text": "39,", "ref_id": "BIBREF38"}, {"start": 442, "end": 444, "text": "5,", "ref_id": "BIBREF4"}, {"start": 445, "end": 448, "text": "50,", "ref_id": "BIBREF49"}, {"start": 449, "end": 451, "text": "4,", "ref_id": "BIBREF3"}, {"start": 452, "end": 455, "text": "33]", "ref_id": "BIBREF32"}, {"start": 490, "end": 494, "text": "[32,", "ref_id": "BIBREF31"}, {"start": 495, "end": 498, "text": "55]", "ref_id": "BIBREF54"}, {"start": 555, "end": 559, "text": "[41,", "ref_id": "BIBREF40"}, {"start": 560, "end": 563, "text": "14,", "ref_id": "BIBREF13"}, {"start": 564, "end": 567, "text": "29]", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Retrieval-Augmented Generation (RAG). The concept of Retrieval-Augmented Generation, initially proposed by <PERSON> et al. [21] , has gained increased attention for its ability to mitigate the issue of hallucination within LLMs and enhance trustworthiness and explainability [7] . Despite its success in language-related tasks, the application of retrieval-based approaches to general graph tasks remains largely unexplored. Most existing work focuses primarily on the knowledge graph [38, 1, 36, 16] .", "cite_spans": [{"start": 120, "end": 124, "text": "[21]", "ref_id": "BIBREF20"}, {"start": 272, "end": 275, "text": "[7]", "ref_id": "BIBREF6"}, {"start": 482, "end": 486, "text": "[38,", "ref_id": "BIBREF37"}, {"start": 487, "end": 489, "text": "1,", "ref_id": "BIBREF0"}, {"start": 490, "end": 493, "text": "36,", "ref_id": "BIBREF35"}, {"start": 494, "end": 497, "text": "16]", "ref_id": "BIBREF15"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Our research is the first to apply a retrieval-based approach to general graph tasks, marking a novel advancement in the field and demonstrating the versatility of RAG beyond language processing.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Parameter-Efficient Fine-Tuning (PEFT). The field of LLMs has witnessed significant advancements through various parameter-efficient fine-tuning techniques. These methodologies have played a crucial role in refining LLMs, boosting their performance while minimizing the need for extensive parameter training. Notable among these techniques are prompt tuning, as introduced by <PERSON> et al. [20] , and prefix tuning, proposed by <PERSON> and <PERSON> [22] . Furthermore, methods like LoRA [10] ,", "cite_spans": [{"start": 390, "end": 394, "text": "[20]", "ref_id": "BIBREF19"}, {"start": 441, "end": 445, "text": "[22]", "ref_id": "BIBREF21"}, {"start": 479, "end": 483, "text": "[10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "and the LLaMA-adapter [53] , have been influential. These advancements in PEFT have laid the foundation for the development of sophisticated multimodal models. Prominent examples in this domain include MiniGPT-4 [57] , LLaVA [26] , and NExT-Chat [46] . There are also emerging efforts in applying PEFT to graph LLMs, such as GraphLLM [3] and GraphToken [31] for basic graph reasoing tasks and GNP [41] for multi-option QA on knowledge graphs.", "cite_spans": [{"start": 22, "end": 26, "text": "[53]", "ref_id": "BIBREF52"}, {"start": 212, "end": 216, "text": "[57]", "ref_id": "BIBREF56"}, {"start": 225, "end": 229, "text": "[26]", "ref_id": "BIBREF25"}, {"start": 246, "end": 250, "text": "[46]", "ref_id": "BIBREF45"}, {"start": 334, "end": 337, "text": "[3]", "ref_id": "BIBREF2"}, {"start": 353, "end": 357, "text": "[31]", "ref_id": "BIBREF30"}, {"start": 397, "end": 401, "text": "[41]", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "This section establishes the notation and formalizes key concepts related to textual graphs, language models for text encoding, and large language models and prompt tuning.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Formalization", "sec_num": "3"}, {"text": "Textual Graphs. A textual graph is a graph where nodes and edges possess textual attributes. Formally, it can be defined as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Formalization", "sec_num": "3"}, {"text": "G = (V, E, {x n } n∈V , {x e } e∈E )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Formalization", "sec_num": "3"}, {"text": ", where V and E represent the sets of nodes and edges, respectively. Additionally, x n ∈ D Ln and x e ∈ D Le denote sequential text associate with a node n ∈ V or an edge e ∈ E, where D represents the vocabulary, and L n and L e signify the length of the text associated with the respective node or edge.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Formalization", "sec_num": "3"}, {"text": "Language Models for Text Encoding. In the context of textual graphs, language models (LMs) are essential for encoding the text attributes associated with nodes and edges, thereby learning representations that capture their semantic meaning. For a node n with text attributes x n ∈ D Ln , an LM encodes these attributes as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Formalization", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z n = LM(x n ) ∈ R d ,", "eq_num": "(1)"}], "section": "Formalization", "sec_num": "3"}, {"text": "where z n is the output of the LM, and d is the dimension of the output vector.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Formalization", "sec_num": "3"}, {"text": "Large Language Models and Prompt Tuning. LLMs have introduced a new paradigm for taskadaptation known as \"pre-train, prompt, and predict\", replacing the traditional \"pre-train, fine-tune\" paradigm. In this paradigm, the LLM is first pre-trained on a large corpus of text data to learn general language representations. Then, rather than fine-tuning the model on task-specific labeled data, the model is prompted with a textual prompt that specifies the task and context. Subsequently, the model generates the output directly based on the prompt and the input.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Formalization", "sec_num": "3"}, {"text": "The LLM, parameterized by weights θ, takes a sequence of tokens X, and a prompt P as input, and generates a sequence of tokens Y = {y 1 , y 2 , . . . , y r } as output. Formally, the probability distribution of the output sequence given the concatenated input sequence and prompt, i.e., [P ; X], is defined as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Formalization", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p θ (Y |[P ; X]) = r i=1 p θ (y i |y <i , [P ; X]).", "eq_num": "(2)"}], "section": "Formalization", "sec_num": "3"}, {"text": "Here, y <i represents the prefix of sequence y up to position i -1, and p(y i |y <i , [P ; X]) represents the probability of generating token y i given y <i and [P ; X].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Formalization", "sec_num": "3"}, {"text": "Soft prompt tuning eliminates the need for manual prompt design. Given a series of p tokens X = {x 1 , x 2 , . . . , x p }, after being processed by the text embedder, it forms a matrix X e ∈ R p×d l , where d l is the dimension of the embedding space. Soft prompts can be represented as parameters P e ∈ R q×d l , where q is the length of the prompt. The prompt is then concatenated with the embedded input, forming a single matrix [P e ; X e ] ∈ R (q+p)×d l . This combined matrix is processed by the self-attention layers in LLM as usual. Training involves maximizing the likelihood of Y through backpropagation, with gradient updates applied solely to P e , while θ remains fixed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Formalization", "sec_num": "3"}, {"text": "Our GraphQA represents a comprehensive and diverse benchmark for graph question-answering. It is tailored to assess the capabilities of models in answering a wide range of questions about graphs across diverse domains.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Proposed GraphQA Benchmark", "sec_num": "4"}, {"text": "Each entry in the GraphQA benchmark consists of a textual graph, a question related to the graph, and one or more corresponding answers, as illustrated in Figure 2 . Textual Graphs. The textual graph is converted into a natural language format, resulting in a list of nodes and edges, akin to a CSV file format. It is important to note that while multiple methods exist for textualizing a graph, our focus is not on identifying the optimal solution. Instead, we prioritize a straightforward yet empirically effective approach for representing graphs in natural language, facilitating the benchmark's use in diverse GraphQA scenarios.", "cite_spans": [], "ref_spans": [{"start": 162, "end": 163, "text": "2", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Data Format", "sec_num": "4.1"}, {"text": "Questions and Answers. Questions are designed to explore specific elements or relationships within the graph. Answers, residing within the attributes of nodes or edges, often require multi-hop reasoning for accurate identification.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Data Format", "sec_num": "4.1"}, {"text": "The GraphQA benchmark integrates three existing datasets: ExplaGraphs, SceneGraphs, and WebQSP. Table 2 presents the summary statistics of these datasets. It is important to note that these datasets were not originally developed for this work. However, a significant contribution of our research is the standardization and processing of these diverse datasets into a uniform data format suitable for the GraphQA benchmark. These datasets, previously utilized in different contexts, are reintroduced with a new focus tailored for GraphQA. For a detailed comparison with the original datasets, see the Appendix C.", "cite_spans": [], "ref_spans": [{"start": 102, "end": 103, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Description of Datasets", "sec_num": "4.2"}, {"text": "ExplaGraphs is a dataset for generative commonsense reasoning, focusing on creating explanation graphs for stance prediction in debates. It offers detailed, unambiguous commonsense-augmented graphs to evaluate arguments supporting or refuting a belief. The primary task is to assess whether arguments are supportive or contradictory, using accuracy as the metric. We have converted the triplet-form provided in <PERSON><PERSON> et al. [35] into a standard graph format.", "cite_spans": [{"start": 423, "end": 427, "text": "[35]", "ref_id": "BIBREF34"}], "ref_spans": [], "eq_spans": [], "section": "Description of Datasets", "sec_num": "4.2"}, {"text": "SceneGraphs, a visual question answering dataset, includes 100,000 scene graphs. Each graph details objects, attributes, and relations within an image. This dataset challenges users with tasks requiring spatial understanding and multi-step inference. The task is to answer open-ended questions based on a textual description of a scene graph, evaluated on accuracy. We have sampled from the GQA dataset [13] and constructed standard graphs from the provided JSON files.", "cite_spans": [{"start": 403, "end": 407, "text": "[13]", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Description of Datasets", "sec_num": "4.2"}, {"text": "WebQSP is a large-scale multi-hop knowledge graph QA dataset consisting of 4,737 questions. It was proposed by <PERSON><PERSON> et al. [48] and, following <PERSON><PERSON> et al. [28] , utilizes a subset of Freebase, encompassing facts within 2-hops of entities mentioned in the questions. The task involves answering questions that", "cite_spans": [{"start": 122, "end": 126, "text": "[48]", "ref_id": "BIBREF47"}, {"start": 153, "end": 157, "text": "[28]", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Description of Datasets", "sec_num": "4.2"}, {"text": "Question: What is the name of justin bi<PERSON>er brother?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Graph Encoder", "sec_num": null}, {"text": "Step 1: Indexing", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Graph Encoder", "sec_num": null}, {"text": "Step The most semantically relevant nodes and edges are retrieved, conditioned on the query; 3) Subgraph Construction: A connected subgraph is extracted, covering as many relevant nodes and edges as possible while maintaining a manageable graph size; 4) Generation: An answer is generated using a 'graph prompt', a textualized graph, and the query. require multi-hop reasoning. Given the possibility of multiple answers for the same question, the hit@1 metric is used to assess the precision of the top returned answer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Storage", "sec_num": null}, {"text": "In this section, we introduce G-Retriever, a new architecture tailored for GraphQA, which integrates the strengths of GNNs, LLMs, and RAG. To allow efficient fine-tuning while preserving the LLM's pretrained language capabilities, we freeze the LLM and use a soft prompting approach on the output of the GNN. Our RAG-based design mitigates hallucinations through direct retrieval of the graph, while allowing our approach to scale to graphs exceeding the LLM's context window size. To adapt RAG to graphs, we formulate subgraph retrieval as a PCST optimization problem. This approach also allows us to enhance explainability by returning the retrieved subgraph.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G-Retriever", "sec_num": "5"}, {"text": "G-Retriever comprises four main steps: indexing, retrieval, subgraph construction and generation, as depicted in Figure 3 . The implementation details of each step are elaborated in the following sections.", "cite_spans": [], "ref_spans": [{"start": 120, "end": 121, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "G-Retriever", "sec_num": "5"}, {"text": "We initiate the RAG approach by generating node and graph embeddings using a pre-trained LM. These embeddings are then stored in a nearest neighbor data structure.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Indexing", "sec_num": "5.1"}, {"text": "To elaborate, consider x n ∈ D Ln as the text attributes of node n. Utilizing a pre-trained LM, such as <PERSON><PERSON><PERSON>B<PERSON> [34] , we apply the LM to x n , yielding the representation z n :", "cite_spans": [{"start": 117, "end": 121, "text": "[34]", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Indexing", "sec_num": "5.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z n = LM(x n ) ∈ R d ,", "eq_num": "(3)"}], "section": "Indexing", "sec_num": "5.1"}, {"text": "where d denotes the dimension of the output vector. Similar preprocessing steps are applied to edges. Refer to Figure 3 , Step 1 for an illustrative representation.", "cite_spans": [], "ref_spans": [{"start": 118, "end": 119, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Indexing", "sec_num": "5.1"}, {"text": "For retrieval, we employ the same encoding strategy to the query x q , to ensure consistent treatment of textual information:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Retrieval", "sec_num": "5.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z q = LM(x q ) ∈ R d .", "eq_num": "(4)"}], "section": "Retrieval", "sec_num": "5.2"}, {"text": "Next, to identify the most relevant nodes and edges for the current query, we use a k-nearest neighbors retrieval approach. This method yields a set of 'relevant nodes/edges' based on the similarity between the query and each node or edge. The retrieval operation is defined as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Retrieval", "sec_num": "5.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "V k = argtopk n∈V cos(z q , z n ) E k = argtopk e∈E cos(z q , z e ),", "eq_num": "(5)"}], "section": "Retrieval", "sec_num": "5.2"}, {"text": "where z n and z e are the embeddings of node n and edge e, respectively. We use the cosine similarity function, cos(•, •), to measure the similarity between the query representation and the node/edge embeddings. The argtopk operation retrieves the top-k elements based on this similarity, providing a set of nodes V k and edges E k considered most relevant to the query. See Step 2 of Figure 3 .", "cite_spans": [], "ref_spans": [{"start": 392, "end": 393, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Retrieval", "sec_num": "5.2"}, {"text": "This step aims to construct a subgraph that encompasses as many relevant nodes and edges as possible, while keeping the graph size manageable. This approach offers two key benefits: Firstly, it helps to filter out nodes and edges that are not pertinent to the query. This is crucial because irrelevant information can overshadow the useful data, potentially diverting the focus of the subsequent LLM from the information of interest. Secondly, it enhances efficiency; by keeping the graph size manageable, it becomes feasible to translate the graph into natural language and then input it into the LLM for processing. The Prize-Collecting Steiner Tree algorithm [2] serves as our primary method for identifying such optimally sized and relevant subgraphs. See Step 3 in Figure 3 .", "cite_spans": [{"start": 662, "end": 665, "text": "[2]", "ref_id": "BIBREF1"}], "ref_spans": [{"start": 777, "end": 778, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Subgraph Construction", "sec_num": "5.3"}, {"text": "The PCST problem aims to find a connected subgraph that maximizes the total prize values of its nodes while minimizing the total costs of its edges. Our approach assigns higher prize values to nodes and edges more relevant to the query, as measured by cosine similarity. Specifically, the top k nodes/edges are assigned descending prize values from k down to 1, with the rest assigned zero. The node prize assignment is as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prize-Collecting Steiner Tree (PCST).", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "prize(n) = k -i, if n ∈ V k and n is the top i node, 0, otherwise.", "eq_num": "(6)"}], "section": "Prize-Collecting Steiner Tree (PCST).", "sec_num": null}, {"text": "Edge prizes are assigned similarly. The objective is to identify a subgraph, S * = (V * , E * ), that optimizes the total prize of nodes and edges, minus the costs associated with the size of the subgraph:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prize-Collecting Steiner Tree (PCST).", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "S * = argmax S⊆G, S is connected n∈V S prize(n) + e∈E S prize(e) -cost(S),", "eq_num": "(7)"}], "section": "Prize-Collecting Steiner Tree (PCST).", "sec_num": null}, {"text": "where cost(S)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prize-Collecting Steiner Tree (PCST).", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "= |E S | × C e ,", "eq_num": "(8)"}], "section": "Prize-Collecting Steiner Tree (PCST).", "sec_num": null}, {"text": "and C e denotes a predefined cost per edge, which is adjustable to control the subgraph size.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prize-Collecting Steiner Tree (PCST).", "sec_num": null}, {"text": "The original PCST algorithm is designed for node prizes only. However, given the significance of edge semantics in certain scenarios, we adapt the algorithm to accommodate edge prizes as follows: Consider an edge e with a cost C e and a prize P e . If C e > P e , it can be treated as a reduced edge cost of C e -P e . However, if P e > C e , negative edge costs are not allowed in the original algorithm. Our solution involves replacing edge e with a 'virtual node' v e , connected to both endpoints of e. This virtual node is assigned a prize of P e -C e , and the cost of the two new edges leading to the virtual node is set to zero. This modification effectively mirrors the original problem, as including edge e in the original graph is analogous to including the virtual node in the modified graph. Finally, we optimize the PCST problem using a near-linear time approach [9] .", "cite_spans": [{"start": 877, "end": 880, "text": "[9]", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "Prize-Collecting Steiner Tree (PCST).", "sec_num": null}, {"text": "Graph Encoder. Let S * = (V * , E * ) represent the retrieved subgraph. We use a graph encoder to model the structure of this graph, specifically using a standard Graph Attention Network (GAT) [43] . Our approach for encoding the retrieved subgraph is defined as follows:", "cite_spans": [{"start": 193, "end": 197, "text": "[43]", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "h g = POOL(GNN ϕ1 (S * )) ∈ R dg ,", "eq_num": "(9)"}], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "Here, POOL denotes the mean pooling operation, and d g is the dimension of the graph encoder.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "Projection Layer. We incorporate a multilayer perceptron (MLP) to align the graph token with the vector space of the LLM:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ĥg = MLP ϕ2 (h g ) ∈ R d l ,", "eq_num": "(10)"}], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "where d l is the dimension of the LLM's hidden embedding.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "Text Embedder. To leverage the text-reasoning capabilities of LLMs, we transform the retrieved subgraph S * into a textual format. This transformation involves flattening the textual attributes of the nodes and edges, as illustrated in the green box in Figure 2 . We refer to this operation as textualize(•). Subsequently, we combine the textualized graph with the query to generate a response. Let x q denote the query; we concatenate it with the textualized graph textualize(S * ). We then map the result to an embedding h t using a text embedder, which is the first layer of a pretrained and frozen LLM:", "cite_spans": [], "ref_spans": [{"start": 260, "end": 261, "text": "2", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "h t = TextEmbedder([textualize(S * ); x q ]) ∈ R L×d l ,", "eq_num": "(11)"}], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "where [; ] represents the concatenation operation, and L is the number of tokens.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "LLM Generation with Graph Prompt Tuning. The final stage involves generating the answer Y given the graph token ĥg , acting as a soft prompt, and the text embedder output h t . These inputs are fed through the self-attention layers of a pretrained frozen LLM, with parameter θ. The generation process is represented as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p θ,ϕ1,ϕ2 (Y |S * , x q ) = r i=1 p θ,ϕ1,ϕ2 (y i |y <i , [ ĥg ; h t ]),", "eq_num": "(12)"}], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "where [ ĥg ; h t ] concatenates the graph token ĥg and the text embedder output h t . While θ is frozen, the graph token ĥg receives gradients, enabling the optimization of the parameters of the graph encoder ϕ 1 and the projection layer ϕ 2 through standard backpropagation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "6 Experiments", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer Generation", "sec_num": "5.4"}, {"text": "In the indexing step, we use SentenceBert [34] as the LM to encode all node and edge attributes. In the generation step, we use the open-source Llama2-7b [42] as the LLM and Graph Transformer [37] as the graph encoder. Additional details are provided in Appendix B.1.", "cite_spans": [{"start": 42, "end": 46, "text": "[34]", "ref_id": "BIBREF33"}, {"start": 154, "end": 158, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 192, "end": 196, "text": "[37]", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "Experiment Setup", "sec_num": "6.1"}, {"text": "In our experiments, we consider three model configurations: 1) Inference-only: Using a frozen LLM for direct question answering; 2) Frozen LLM w/ prompt tuning (PT): Keeping the parameters of the LLM frozen and adapting only the prompt; 3) Tuned LLM: Fine-tuning the LLM with LoRA [10] .", "cite_spans": [{"start": 281, "end": 285, "text": "[10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Main Results", "sec_num": "6.2"}, {"text": "We provide more details in Appendix B.2. Table 3 demonstrates the effectiveness of our method across three datasets in various configurations.", "cite_spans": [], "ref_spans": [{"start": 47, "end": 48, "text": "3", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Main Results", "sec_num": "6.2"}, {"text": "In the inference-only setting, G-Retriever surpasses all baselines. Notably, LLM can perform even better when no graph knowledge is provided (i.e., question only), which might be attributed to the complexity and potential noise in the knowledge. For frozen LLM with prompt tuning, G-Retriever outperforms traditional prompt tuning and GraphToken [31] , a graph prompt tuning-based method, with average performance increases of 40.6% and 30.8% respectively. Furthermore, when tuned with LoRA, G-Retriever achieves the best performance. ", "cite_spans": [{"start": 346, "end": 350, "text": "[31]", "ref_id": "BIBREF30"}], "ref_spans": [], "eq_spans": [], "section": "Main Results", "sec_num": "6.2"}, {"text": "The efficiency of our approach is highlighted by the data in ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Efficiency Evaluation", "sec_num": "6.3"}, {"text": "To evaluate hallucination, we instructed the models to answer graph-related questions, specifically by identifying supporting nodes or edges from the graph. We assessed the model's faithfulness using three metrics: the fraction of valid nodes (denoted as Valid Nodes), the fraction of valid edges (denoted as Valid Edges), and the fraction of times the entire set of cited nodes and edges was valid (denoted as Fully Valid Graphs). We manually reviewed 100 responses from both our method and the baseline (i.e.,, LLM with graph prompt tuning). Table 5 shows that G-Retriever significantly reduces hallucinations by 54% compared to the baseline, as our graph retrieval ensures that the data is sourced directly from the actual graph, leading to fewer hallucinations. See Appendix G for details.", "cite_spans": [], "ref_spans": [{"start": 550, "end": 551, "text": "5", "ref_id": "TABREF5"}], "eq_spans": [], "section": "Mitigation of Hallucination", "sec_num": "6.4"}, {"text": "In this ablation study, we assess the individual impact of key components within our pipeline. As shown in Table 6 , there are performance drops when any of these components are removed, with the graph encoder and textualized graph showing declines of 22.51% and 19.19%, respectively. This demonstrates their complementary effects in representing the graph in both textual and embedded formats. Additionally, the retrieval on graphs is also important to the overall performance. Further details are available in Appendix B.3. We also present additional studies on our framework: it is robust to the choice of graph encoders (see Appendix B.4) and benefits from the increased scale of LLMs (see Appendix B.5). ", "cite_spans": [], "ref_spans": [{"start": 113, "end": 114, "text": "6", "ref_id": "TABREF6"}], "eq_spans": [], "section": "Ablation Study", "sec_num": "6.5"}, {"text": "In this work, we introduce a new GraphQA benchmark for real-world graph question answering and present G-Retriever, an architecture adept at complex and creative queries. Experimental results show that G-Retriever surpasses baselines in textual graph tasks across multiple domains, scales effectively with larger graph sizes, and demonstrates resistance to hallucination.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "7"}, {"text": "Limitations and Future Work: Currently, G-Retriever employs a static retrieval component. Future developments could investigate more sophisticated RAG where the retrieval is trainable.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "7"}, {"text": "As LLMs are applied to increasingly diverse tasks, their ability to process complex structured data will be increasingly vital. Our work aims to enhance LLMs' ability to interact with graph-structured data, while resisting hallucination, thus improving model reliability. We also enhance explainability, both by returning the retrieved subgraph, and through the use of conversational interfaces for 'chatting with a graph', which allows for better human-AI interaction and for models to behave in a way that is more well-aligned with human expectations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Impact Statements", "sec_num": null}, {"text": "Experiments are conducted using 2 NVIDIA A100-80G GPUs. Each experiment is replicated four times, utilizing different seeds for each run to ensure robustness and reproducibility.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Experiment B.1 Implementation Settings", "sec_num": null}, {"text": "Graph Encoder. We use Graph Transformer [37] as the GNN backbone. Our configuration employs 4 layers, each with 4 attention heads, and a hidden dimension size of 1024.", "cite_spans": [{"start": 40, "end": 44, "text": "[37]", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "B Experiment B.1 Implementation Settings", "sec_num": null}, {"text": "We use the open-sourced Llama2-7b [42] as the LLM backbone. In fine-tuning the LLM with LoRA [10] , the lora_r parameter (dimension for LoRA update matrices) is set to 8, and lora_alpha (scaling factor) is set to 16. The dropout rate is set to 0.05. In prompt tuning, the LLM is configured with 10 virtual tokens. The number of max text length is 512, the number of max new tokens, i.e., the maximum numbers of tokens to generate, is 32.", "cite_spans": [{"start": 34, "end": 38, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 93, "end": 97, "text": "[10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "LLM.", "sec_num": null}, {"text": "PCST. For retrieval over graphs via PCST, for the SceneGraphs dataset, we select the top k nodes and edges, setting k to 3. Here, the cost of edges, denoted as C e , is set to 1. Regarding the WebQSP dataset, we set k = 3 for nodes and k = 5 for edges, with the edge cost, C e , adjusted to 0.5. For the ExplaGraphs dataset, which is characterized by a small graph size averaging 5.17 nodes and 4.25 edges (as detailed in Table 2 ), the entire graph can fit in the LLM's context window size. Consequently, we aim to retrieve the whole graph by setting k to 0, effectively returning the original graph unaltered.", "cite_spans": [], "ref_spans": [{"start": 428, "end": 429, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "LLM.", "sec_num": null}, {"text": "Optimization. We use the AdamW [27] optimizer. We set the initial learning rate at 1e-5, with a weight decay of 0.05. The learning rate decays with a half-cycle cosine decay after the warm-up period. The batch size is 4, and the number of epochs is 10. To prevent overfitting and ensure training efficiency, an early stopping mechanism is implemented with a patience setting of 2 epochs.", "cite_spans": [{"start": 31, "end": 35, "text": "[27]", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "LLM.", "sec_num": null}, {"text": "In our experiments, we consider three model configurations:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 Details of Model Configurations", "sec_num": null}, {"text": "1) Inference-only: Using a frozen LLM for direct question answering with textual graph and question, see Figure 4 . • Zero-shot. In this approach, the model is given a textual graph description and a task description, and is immediately asked to produce the desired output. No additional examples or demonstrations are provided.", "cite_spans": [], "ref_spans": [{"start": 112, "end": 113, "text": "4", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "B.2 Details of Model Configurations", "sec_num": null}, {"text": "• Zero-CoT. Zero-shot Chain-of-thought (Zero-CoT) prompting [18] is a follow-up to CoT prompting [45] , which introduces an incredibly simple zero shot prompt by appending the words \"Let's think step by step.\" to the end of a question.", "cite_spans": [{"start": 60, "end": 64, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 97, "end": 101, "text": "[45]", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "B.2 Details of Model Configurations", "sec_num": null}, {"text": "• CoT-BAG. Build-a-Graph Prompting (BAG) [44] is a prompting technique that adds \"Let's construct a graph with the nodes and edges first.\" after the textual description of the graph is explicitly given.", "cite_spans": [{"start": 41, "end": 45, "text": "[44]", "ref_id": "BIBREF43"}], "ref_spans": [], "eq_spans": [], "section": "B.2 Details of Model Configurations", "sec_num": null}, {"text": "• KAPING. KAPING [1] is a zero-shot knowledge-augmented prompting method for knowledge graph question answering. It first retrieves triples related to the question from the graph, then prepends them to the input question in the form of a prompt, which is then forwarded to LLMs to generate the answer.", "cite_spans": [{"start": 17, "end": 20, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "B.2 Details of Model Configurations", "sec_num": null}, {"text": "2) Frozen LLM w/ prompt tuning (PT): Keeping the parameters of the LLM frozen and adapting only the prompt. This includes soft prompt tuning (see Figure 5a ), GraphToken [31] , which is a graph prompt tuning method, and our G-Retriever method (see Figure5b). 3) Tuned LLM: Fine-tuning the LLM with LoRA. This includes standard fine-tuning of an LLM for downstream tasks using LoRA (see Figure 6a ) and G-Retriever with LoRA (see Figure 6b ). ", "cite_spans": [{"start": 170, "end": 174, "text": "[31]", "ref_id": "BIBREF30"}], "ref_spans": [{"start": 153, "end": 155, "text": "5a", "ref_id": "FIGREF5"}, {"start": 393, "end": 395, "text": "6a", "ref_id": "FIGREF7"}, {"start": 436, "end": 438, "text": "6b", "ref_id": "FIGREF7"}], "eq_spans": [], "section": "B.2 Details of Model Configurations", "sec_num": null}, {"text": "This section illustrates the modifications made to the original architecture in the ablation study, as presented in Figure 7 .", "cite_spans": [], "ref_spans": [{"start": 123, "end": 124, "text": "7", "ref_id": "FIGREF9"}], "eq_spans": [], "section": "B.3 Details of Ablation Study", "sec_num": null}, {"text": "Without Graph Encoder (w/o GraphEncoder): In this setting, we replaced the graph encoder with trainable soft tokens, setting the number of these virtual tokens to 10.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Details of Ablation Study", "sec_num": null}, {"text": "Without Projection Layer (w/o Projection Layer): Here, we removed the projection layer following the graph encoder. We configured the output dimension of the graph encoder to be 4,096, matching the hidden dimension of Llama2-7b. This allows the output graph token (the yellow token in Figure 7b ) to be concatenated directly with the LLM tokens (blue tokens).", "cite_spans": [], "ref_spans": [{"start": 292, "end": 294, "text": "7b", "ref_id": "FIGREF9"}], "eq_spans": [], "section": "B.3 Details of Ablation Study", "sec_num": null}, {"text": "Without Textualized Graph (w/o Textualized Graph): In this configuration, we modified the textual input to the LLM. Rather than using a combination of the question and the textualized graph, we solely used the question. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Details of Ablation Study", "sec_num": null}, {"text": "In addition to the Graph Transformer [37] , we explore other GNNs as the graph encoder, such as GCN [17] and the GAT [43] . The comparative results of these models on the WebQSP and ExplaGraphs datasets are presented in Table 7 .", "cite_spans": [{"start": 37, "end": 41, "text": "[37]", "ref_id": "BIBREF36"}, {"start": 100, "end": 104, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 117, "end": 121, "text": "[43]", "ref_id": "BIBREF42"}], "ref_spans": [{"start": 226, "end": 227, "text": "7", "ref_id": null}], "eq_spans": [], "section": "B.4 The Choice of Graph Encoder", "sec_num": null}, {"text": "Table 7 : Performance of different graph encoders on the WebQSP and ExplaGraphs datasets.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "7", "ref_id": null}], "eq_spans": [], "section": "B.4 The Choice of Graph Encoder", "sec_num": null}, {"text": "WebQSP ExplaGraphs GCN [17] 70.70 0.8394 GAT [43] 70.27 0.8430 Graph Transformer [37] 70.49 0.8516", "cite_spans": [{"start": 23, "end": 27, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 45, "end": 49, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 81, "end": 85, "text": "[37]", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "Graph Encoder", "sec_num": null}, {"text": "The results demonstrate that our proposed method exhibits consistent robustness across different graph encoders. Notably, all three encoders -GCN, GAT, and GraphTransformer -demonstrate competitive and closely aligned performance on the WebQSP dataset, with Hit@1 scores of 70.70, 70.27, and 70.49, respectively. However, the performance differentiation becomes more pronounced on the ExplaGraphs dataset, where GraphTransformer exhibits a superior Hit@1 score of 0.8516, followed by GAT and GCN with scores of 0.8430 and 0.8394, respectively. This variation in performance across the datasets highlights the importance of encoder selection based on the specific characteristics and requirements of the dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Graph Encoder", "sec_num": null}, {"text": "As for the choice of LLM, we considered both Llama2-7b and Llama2-13b. Our experiments demonstrate that stronger LLMs enhance the effectiveness of our method, as shown in Table 8 , indicating that it benefits from the increased scale of the LLMs. ", "cite_spans": [], "ref_spans": [{"start": 177, "end": 178, "text": "8", "ref_id": "TABREF9"}], "eq_spans": [], "section": "B.5 The Choice of LLM", "sec_num": null}, {"text": "In this section, we detail how our GraphQA benchmark differs from the original datasets, including the specific processing steps we employed. For concrete examples that illustrate the differences between the raw text in the original dataset and in our GraphQA benchmark, please refer to Table 9 .", "cite_spans": [], "ref_spans": [{"start": 293, "end": 294, "text": "9", "ref_id": "TABREF10"}], "eq_spans": [], "section": "C GraphQA Benchmark", "sec_num": null}, {"text": "ExplaGraphs. The original dataset1 [35] represents relationships using triplets. We have standardized this format by converting the triplets into a graph representation. Specifically, each head and tail in a triplet is transformed into a node, and the relation is transformed into an edge. Since the test dataset labels are not available, we have utilized only the training and validation (val) datasets from the original collection. We further divided these into training, val, and test subsets, using a 6:2:2 ratio. SceneGraphs discrimination between capital and lowercase words, we have converted all words to lowercase. We used the same dataset split as in the original dataset.", "cite_spans": [{"start": 35, "end": 39, "text": "[35]", "ref_id": "BIBREF34"}], "ref_spans": [], "eq_spans": [], "section": "C GraphQA Benchmark", "sec_num": null}, {"text": "Contribution of the GraphQA Benchmark. We acknowledge that the GraphQA benchmark involves converting three existing graph datasets into a uniform format. However, we believe this provides significant value to the research community in several ways:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C GraphQA Benchmark", "sec_num": null}, {"text": "• Task Introduction: Unlike existing graph question-answering benchmarks that focus on small or synthetic graphs, our benchmark includes real-world applications and frames them as graph question-answering tasks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C GraphQA Benchmark", "sec_num": null}, {"text": "• Standardization: A key and significant effort of this benchmark is the standardization and processing of diverse datasets into a uniform format suitable for GraphQA tasks. These datasets, previously used in different contexts, are redesigned to focus specifically on GraphQA, ensuring consistent and comparable evaluations across models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C GraphQA Benchmark", "sec_num": null}, {"text": "• Accessibility: We have open-sourced the GraphQA benchmark, providing a unified format that simplifies model application across multiple datasets. This reduces the complexity of handling various data structures and preprocessing pipelines, lowering barriers for new researchers and encouraging broader participation. We have already seen several novel works using our GraphQA benchmark, and we expect rapid adoption within the LLM and GNN communities.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C GraphQA Benchmark", "sec_num": null}, {"text": "• Baseline Comparisons: The benchmark offers baseline performance metrics, helping researchers identify the strengths and weaknesses of new approaches compared to established baselines.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C GraphQA Benchmark", "sec_num": null}, {"text": "Modeling motivation. We formulate subgraph retrieval as a Prize-Collecting Steiner Tree (PCST) optimization problem. This is motivated by the need to find a connected subgraph containing most relevant nodes and edges, a goal that aligns well with the objectives of PCST: maximizing node values while minimizing edge costs. Though not universally acknowledged as optimal, we have empirically validated its effectiveness.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Graph Retrieval-Augmented Generation (GraphRAG) D.1 Elaboration on PCST-Based Retrieval", "sec_num": null}, {"text": "Effectiveness compared to other retrieval baselines. To validate the effectiveness of our PCSTbased retrieval approach, we compared it against several baselines: (1) top-k triples retrieval, i.e., KAPING [1] , which retrieves the top-k triples related to the query and incorporates them into the prompt for the LLM; (2) top-k nodes plus neighbors, which retrieves the top-k nodes and their one-hop neighbors, capturing local context; (3) shortest path retrieval, which retrieves the top-k nodes and computes the shortest paths between them.", "cite_spans": [{"start": 204, "end": 207, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "D Graph Retrieval-Augmented Generation (GraphRAG) D.1 Elaboration on PCST-Based Retrieval", "sec_num": null}, {"text": "For all methods, we set k = 5 and used llama2-7b-chat as the LLM. The results, presented in Table 10 , show that our PCST-based retrieval method achieves the highest accuracy (Hit@1) of 66.17% on the WebQSP dataset, outperforming all baseline methods. In contrast, PCST-based retrieval is guaranteed to return a connected subgraph, capturing the graph context during the retrieval process. This approach retrieves not only high-relevance nodes or edges but also \"bridge\" elements that connect these with contextually significant nodes or edges, which are crucial for generating a comprehensive response.", "cite_spans": [], "ref_spans": [{"start": 98, "end": 100, "text": "10", "ref_id": "TABREF11"}], "eq_spans": [], "section": "D Graph Retrieval-Augmented Generation (GraphRAG) D.1 Elaboration on PCST-Based Retrieval", "sec_num": null}, {"text": "Size Management. Compared to the shortest path method, PCST retrieval provides greater control over the size of the retrieved subgraph. By adjusting the prizes and costs on nodes and edges, users can fine-tune the subgraph's extent. In contrast, the shortest path approach lacks the ability to control the distance between the top-k nodes, which can lead to disconnected subgraphs or the inclusion of unnecessarily long paths.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Graph Retrieval-Augmented Generation (GraphRAG) D.1 Elaboration on PCST-Based Retrieval", "sec_num": null}, {"text": "We identify the most relevant nodes and edges and use a k-nearest neighbors retrieval approach (see Equation 6). Small k values may omit crucial knowledge or information relevant to the query, while large k values could introduce excessive information, distracting the model from the essential details.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3 The Impact of K for Retrieval", "sec_num": null}, {"text": "To evaluate the impact of the number of k, we have conducted additional experiments by varying the choice of k to 3, 5, 10, and 20.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3 The Impact of K for Retrieval", "sec_num": null}, {"text": "Table 11 : The impact of k on the webqsp dataset. k 3 5 10 20", "cite_spans": [], "ref_spans": [{"start": 6, "end": 8, "text": "11", "ref_id": "TABREF0"}], "eq_spans": [], "section": "D.3 The Impact of K for Retrieval", "sec_num": null}, {"text": "Hit@1 0.6977 0.7063 0.7248 0.7039", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3 The Impact of K for Retrieval", "sec_num": null}, {"text": "As shown in Table 11 , the Hit@1 metric initially rises for small k values, peaks at a certain point, and then declines for large k values. Determining the optimal k value can be achieved through techniques like cross-validation using a validation set.", "cite_spans": [], "ref_spans": [{"start": 18, "end": 20, "text": "11", "ref_id": "TABREF0"}], "eq_spans": [], "section": "D.3 The Impact of K for Retrieval", "sec_num": null}, {"text": "The choice of similarity function is also important. In this work, we use cosine similarity, a widely adopted metric for measuring vector similarity in models that process vision and language. For instance, CLIP also employs cosine similarity to assess the similarity between text and image features. Although it might not be the optimal choice, we believe that cosine similarity is a general, representative, and valid choice for facilitating fast retrieval tasks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.4 The Choice of Similarity Function", "sec_num": null}, {"text": "E Discussion on the Complexity E.1 The integration of GNNs, LLMs and GraphRAG G-Retriever is framework integrate the strengths of GNNs, LLMs and GraphRAG. The LLM+X framework, which involves enriching LLMs with multi-modal capabilities by integrating an LLM with an encoder from another modality, is a widely adopted approach. Notable examples include Llava, MiniGPT-4, and Flamingo, among others. They are not complex in terms of understanding or implementation. Regarding the integration of GraphRAG, it does not require training and can be implemented during the preprocessing stage or on the fly. This approach does not significantly increase time complexity or computational complexity. On the contrary, it can substantially reduce the size of the graph (e.g., eliminating 99% of nodes in the WebQSP dataset), which in turn speeds up the overall running time (e.g., reducing it from 18.7 min/epoch to 6.2 min/epoch on the WebQSP dataset).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.4 The Choice of Similarity Function", "sec_num": null}, {"text": "Utilizing two A100 GPUs, each with 80GB of memory, we conducted tests on Llama2-7b and WebQSP datasets. Our experiments had a training batch size of 16 and an evaluation batch size of 32, yielding the following results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2 Computational Resources", "sec_num": null}, {"text": "These results highlight efficiency improvements via graph RAG, which significantly reduces graph size (e.g., eliminating 99% of nodes in the WebQSP dataset) and speeds up running time. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2 Computational Resources", "sec_num": null}, {"text": "In this section, we present quantitative results regarding hallucinations in the SceneGraphs dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G Hallucination in Graph LLMs", "sec_num": null}, {"text": "Baseline. For our baseline, we adapted MiniGPT-4 [57] to graph contexts. This approach involves a frozen LLM interacting with a trainable GNN that encodes graph data as a soft prompt, denoted as LLM+Graph Prompt Tuning. We focus on graph prompt tuning as the baseline, instead of converting the graph into text, since the textual representation of the graph is large and consistently exceeds the input token limits of LLMs.", "cite_spans": [{"start": 49, "end": 53, "text": "[57]", "ref_id": "BIBREF56"}], "ref_spans": [], "eq_spans": [], "section": "G Hallucination in Graph LLMs", "sec_num": null}, {"text": "Experiment Design. We instructed the LLM to answer graph-related questions and to list nodes or edges in the explanation graph that support its answers. Since standard answers for these questions do not exist, allowing the LLM to respond flexibly, it becomes challenging to evaluate its responses.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G Hallucination in Graph LLMs", "sec_num": null}, {"text": "To address this, we manually examined 100 responses generated by our method and the LLM with graph prompt tuning, verifying whether the nodes and edges referenced in the LLM's output actually exist in the graph.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G Hallucination in Graph LLMs", "sec_num": null}, {"text": "Evaluation Metrics. We assessed the model's faithfulness using three metrics: the fraction of valid nodes (denoted as Valid Nodes), the fraction of valid edges (denoted as Valid Edges), and the fraction of times the entire set of nodes and edges cited was valid (denoted as Fully Valid Graphs).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G Hallucination in Graph LLMs", "sec_num": null}, {"text": "Results. The results, as depicted in Table 5 , illustrate the comparative effectiveness of the G-Retriever over the baseline LLM+Graph Prompt Tuning method in reducing hallucinations. The LLM+Graph Prompt Tuning approach demonstrated a significantly lower accuracy in referencing graph elements, with only 31% of nodes and 12% of edges being valid, and the entire set of nodes and edges being valid only 8% of the time. In contrast, G-Retriever showed substantial improvements: 77% validity in nodes, 76% in edges, and 62% in the overall validity of referenced node-edge sets. These results underscore the significant reduction in hallucinations with G-Retriever, particularly in the challenging task of accurately citing both nodes and edges in graph-based contexts. User Write a 500-word argument essay based on the information provided in the explanation graph.", "cite_spans": [], "ref_spans": [{"start": 43, "end": 44, "text": "5", "ref_id": "TABREF5"}], "eq_spans": [], "section": "G Hallucination in Graph LLMs", "sec_num": null}, {"text": "Model The explanation graph provides a detailed analysis of the reasons why women undergo cosmetic surgery. According to the graph, the main reasons include the desire to change one's appearance, the influence of societal beauty standards, and the addiction to searching for perfection. These reasons are further supported by the various nodes and edges in the graph, which provide additional insights into the motivations and consequences of cosmetic surgery. Firstly, the node labeled \"human\" highlights the fundamental human desire to feel attractive and confident in one's appearance. This desire is deeply ingrained in our culture, where beauty is often associated with happiness, success, and social acceptance. As a result, many women feel pressure to conform to societal beauty standards, which can lead to feelings of insecurity and low self-esteem. In response to these feelings, some women may turn to cosmetic surgery as a means of improving their appearance and boosting their self-confidence. Secondly, the node labeled \"women\" highlights the gendered nature of cosmetic surgery. While men also undergo cosmetic surgery, the majority of cosmetic surgery patients are women. This is likely due to the fact that women are more likely to be exposed to societal beauty standards and are more likely to internalize these standards. As a result, women may be more likely to feel insecure about their appearance and more likely to seek cosmetic surgery as a means of improving their looks. Thirdly, the node labeled \"addiction\" highlights the potential negative consequences of cosmetic surgery. While some women may initially feel improved confidence and self-esteem after undergoing cosmetic surgery, the graph suggests that this initial boost may not last. In fact, some women may become addicted to the constant pursuit of perfection, leading to a cycle of surgery and dissatisfaction. This can have serious psychological and emotional consequences, including depression, anxiety, and a loss of self-esteem. Fourthly, the node labeled \"searching for perfection\" highlights the unrealistic expectations that many women have about the results of cosmetic surgery. The graph suggests that women may have unrealistic expectations about the ability of cosmetic surgery to improve their appearance and boost their self-esteem. This can lead to disappointment and dissatisfaction with the results of surgery, as well as a continued pursuit of perfection through additional surgery. Finally, the node labeled \"stop\" highlights the importance of critical thinking and self-reflection when it comes to cosmetic surgery. The graph suggests that women should be aware of the potential negative consequences of cosmetic surgery and should carefully consider the reasons why they are undergoing surgery. They should also be aware of the unrealistic expectations that they may have about the results of surgery and should be prepared to address any negative emotions or feelings of dissatisfaction that may arise.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G Hallucination in Graph LLMs", "sec_num": null}, {"text": "In conclusion, the explanation graph provides a detailed analysis of the reasons why women undergo cosmetic surgery. The graph highlights the fundamental human desire to feel attractive and confident in one's appearance, the gendered nature of cosmetic surgery, the potential negative consequences of cosmetic surgery, and the importance of critical thinking and self-reflection when it comes to surgery. By understanding these reasons and the potential consequences of cosmetic surgery, women can make informed decisions about whether or not to undergo surgery and can work to address any negative emotions or feelings of dissatisfaction that may arise. Model Sure! Here's an example of a 500-word advertisement for the place based on the scene graph: \"Experience the breathtaking beauty of our place, where the rolling hills meet the sparkling water of the swimming pool. Take a leisurely stroll through the lush green fields, and let the gentle breeze caress your face as you gaze out at the picturesque view. The sheep graze peacefully in the distance, their white coats glistening in the sunlight. The sky is a brilliant blue, with fluffy white clouds drifting lazily across. Come and bask in the serene ambiance of our place, where the natural beauty will leave you in awe.\" Legacy and Impact: Reflect on <PERSON>'s lasting legacy and the impact of his inventions on modern society. Discover how <PERSON>'s inventions continue to inspire new generations of inventors and scientists and how they have helped to shape the world we live in today. Connection: <PERSON>'s inventions and ideas have had a lasting impact on many fields, including science, technology, and society as a whole. His legacy continues to inspire and motivate people around the world. By following this educational tour through the knowledge graph, students will gain a deeper understanding of <PERSON>'s inventions and their impact on modern society. They will also learn about <PERSON>'s collaborative approach to invention, his innovative spirit, and his ability to think outside the box.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G Hallucination in Graph LLMs", "sec_num": null}, {"text": "NeurIPS Paper Checklist", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G Hallucination in Graph LLMs", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Answer: [Yes] Justification: As shown in the abstract and introduction Guidelines:", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Answer: [Yes] Justification: We discussed the limitation of this work in Section 7.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Answer Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Justification: As shown in Table 3 , we report the mean scores and standard deviations.", "cite_spans": [], "ref_spans": [{"start": 33, "end": 34, "text": "3", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• It should be clear whether the error bar is the standard deviation or the standard error of the mean. Justification: The research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [Yes] Justification: We discuss the broader impacts in Appendix A.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA] Justification: The paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [Yes] Justification: The creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "https://explagraphs.github.io/", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://huggingface.co/datasets/rmanluo/RoG-webqsp", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "BH is supported by the Ministry of Education, Singapore, under the Academic Research Fund Tier 1 (FY2023) (Grant *********-00-00). XB is supported by NUS Grant ID R-252-000-B97-133. The authors would like to express their gratitude to the reviewers for their feedback, which has improved the clarity and contribution of the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgment", "sec_num": null}, {"text": "We demonstrate the interaction capabilities of G-Retriever with creative questions on different datasets: ExplaGraphs, SceneGraphs, and WebQSP. These examples are showcased in Tables 13, 14 , and 15, respectively. Additionally, the examples are visualized in Figure 1 .Justification: The paper does not include theoretical results. Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [{"start": 176, "end": 186, "text": "Tables 13,", "ref_id": null}, {"start": 187, "end": 189, "text": "14", "ref_id": "BIBREF13"}], "ref_spans": [{"start": 266, "end": 267, "text": "1", "ref_id": null}], "eq_spans": [], "section": "H Demonstrations", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: The paper provides comprehensive details on the experimental setup for reproducibility, as outlined in Section 6 and Appendix B. Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Knowledge-augmented language model prompting for zero-shot knowledge graph question answering", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Alham", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 1st Workshop on Natural Language Reasoning and Structured Explanations (NLRSE)", "volume": "", "issue": "", "pages": "78--106", "other_ids": {"DOI": ["10.18653/v1/2023.nlrse-1.7"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Knowledge-augmented language model prompting for zero-shot knowledge graph question answering. In <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, editors, Proceedings of the 1st Workshop on Natural Language Reasoning and Structured Explanations (NLRSE), pages 78-106, Toronto, Canada, June 2023. Association for Computational Linguistics. doi: 10.18653/v1/2023.nlrse-1.7. URL https://aclanthology.org/2023.nlrse-1.7.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "A note on the prize collecting traveling salesman problem", "authors": [{"first": "<PERSON>", "middle": [], "last": "Bienstock", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1993, "venue": "Mathematical programming", "volume": "59", "issue": "1-3", "pages": "413--420", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, and <PERSON>. A note on the prize collecting traveling salesman problem. Mathematical programming, 59(1-3):413-420, 1993.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Boosting graph reasoning ability of large language model", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "Xiao<PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Graphllm", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.05845"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Graphllm: Boosting graph reasoning ability of large language model. arXiv preprint arXiv:2310.05845, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Exploring the potential of large language models (llms) in learning on graphs", "authors": [{"first": "Zhikai", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hai<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hang", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hongzhi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dawei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Fan", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.03393"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. Exploring the potential of large language models (llms) in learning on graphs. arXiv preprint arXiv:2307.03393, 2023.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Label-free node classification on graphs with large language models (llms)", "authors": [{"first": "Zhikai", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hai<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hongzhi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Haiyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.04668"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Label-free node classification on graphs with large language models (llms). arXiv preprint arXiv:2310.04668, 2023.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Talk like a graph: Encoding graphs for large language models", "authors": [{"first": "Bahare", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.04560"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON>. Talk like a graph: Encoding graphs for large language models. arXiv preprint arXiv:2310.04560, 2023.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Retrieval-augmented generation for large language models: A survey", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Yun", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xinyu", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Kangxia<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Pan", "suffix": ""}, {"first": "Yuxi", "middle": [], "last": "Bi", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Dai", "suffix": ""}, {"first": "Jiaw<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Haofen", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.10997"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Retrieval-augmented generation for large language models: A survey. arXiv preprint arXiv:2312.10997, 2023.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Harnessing explanations: Llm-to-lm interpreter for enhanced text-attributed graph representation learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "Xavier", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Lecun", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.19523"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Harnessing explanations: Llm-to-lm interpreter for enhanced text-attributed graph representation learning. arXiv preprint arXiv:2305.19523, 2023.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "A nearly-linear time framework for graphstructured sparsity", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Indyk", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "928--937", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. A nearly-linear time framework for graph- structured sparsity. In International Conference on Machine Learning, pages 928-937. PMLR, 2015.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Low-rank adaptation of large language models", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuanzhi", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weizhu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2106.09685"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Lora: Low-rank adaptation of large language models. arXiv preprint arXiv:2106.09685, 2021.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Can llms effectively leverage graph structural information: when and why", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.16595"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Can llms effectively leverage graph structural information: when and why. arXiv preprint arXiv:2309.16595, 2023.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. A survey on hallucination in large language models: Principles, taxonomy, challenges, and open questions", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weijiang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Wei<PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weihua", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. A survey on hallucination in large language models: Principles, taxonomy, challenges, and open questions, 2023.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Gqa: A new dataset for real-world visual reasoning and compositional question answering", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "6700--6709", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Gqa: A new dataset for real-world visual reasoning and compositional question answering. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 6700-6709, 2019.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Structgpt: A general framework for large language model to reason over structured data", "authors": [{"first": "Jinhao", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "Kun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zican", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Keming", "middle": [], "last": "Ye", "suffix": ""}, {"first": "<PERSON>", "middle": ["Xin"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.09645"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Structgpt: A general framework for large language model to reason over structured data. arXiv preprint arXiv:2305.09645, 2023.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Large language models on graphs: A comprehensive survey", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Gang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON>g", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Heng", "suffix": ""}, {"first": "Jiaw<PERSON>", "middle": [], "last": "Han", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.02783"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Large language models on graphs: A comprehensive survey. arXiv preprint arXiv:2312.02783, 2023.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Knowledge graphaugmented language models for knowledge-grounded dialogue generation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Kwak", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Knowledge graph- augmented language models for knowledge-grounded dialogue generation, 2023.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Semi-supervised classification with graph convolutional networks", "authors": [{"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Max", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Welling", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1609.02907"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Semi-supervised classification with graph convolutional networks. arXiv preprint arXiv:1609.02907, 2016.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Large language models are zero-shot reasoners", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Kojima", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Shi<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "Yutaka", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>was<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in neural information processing systems", "volume": "35", "issue": "", "pages": "22199--22213", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Large language models are zero-shot reasoners. Advances in neural information processing systems, 35:22199-22213, 2022.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Boosting logical reasoning in large language models through a new framework: The graph of thought", "authors": [{"first": "Bin", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Chunhua", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.08614"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Boosting logical reasoning in large language models through a new framework: The graph of thought. arXiv preprint arXiv:2308.08614, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "The power of scale for parameter-efficient prompt tuning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Al-Rfou", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Constant", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2104.08691"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. The power of scale for parameter-efficient prompt tuning. arXiv preprint arXiv:2104.08691, 2021.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Retrieval-augmented generation for knowledge-intensive nlp tasks", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Piktus", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Vladimir", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Rock<PERSON>ä<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "9459--9474", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, et al. Retrieval-augmented generation for knowledge-intensive nlp tasks. Advances in Neural Information Processing Systems, 33:9459-9474, 2020.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Prefix-tuning: Optimizing continuous prompts for generation", "authors": [{"first": "<PERSON>", "middle": [], "last": "Xiang", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2101.00190"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Prefix-tuning: Optimizing continuous prompts for generation. arXiv preprint arXiv:2101.00190, 2021.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Graphadapter: Tuning vision-language models with dual knowledge graph", "authors": [{"first": "Xin", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>awa<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.13625"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Graphadapter: Tuning vision-language models with dual knowledge graph. arXiv preprint arXiv:2309.13625, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "A survey of graph meets large language model: Progress and future directions", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Peisong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Xiangguo", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Hong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.12399"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. A survey of graph meets large language model: Progress and future directions. arXiv preprint arXiv:2311.12399, 2023.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "One for all: Towards training one graph model for all classification tasks", "authors": [{"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Lecheng", "middle": [], "last": "Kong", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dacheng", "middle": [], "last": "Tao", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.00149"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. One for all: Towards training one graph model for all classification tasks. arXiv preprint arXiv:2310.00149, 2023.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Visual instruction tuning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chunyuan", "middle": [], "last": "Li", "suffix": ""}, {"first": "Qingyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.08485"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Visual instruction tuning. arXiv preprint arXiv:2304.08485, 2023.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Decoupled weight decay regularization", "authors": [{"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1711.05101"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Decoupled weight decay regularization. arXiv preprint arXiv:1711.05101, 2017.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Reasoning on graphs: Faithful and interpretable large language model reasoning", "authors": [{"first": "Linhao", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Pan", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.01061"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Reasoning on graphs: Faithful and interpretable large language model reasoning. arXiv preprint arXiv:2310.01061, 2023.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Reasoning on graphs: Faithful and interpretable large language model reasoning", "authors": [{"first": "Linhao", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Pan", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.01061"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Reasoning on graphs: Faithful and interpretable large language model reasoning. arXiv preprint arXiv:2310.01061, 2023.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Integrating graphs with large language models: Methods and prospects", "authors": [{"first": "Yizhen", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.05499"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Integrating graphs with large language models: Methods and prospects. arXiv preprint arXiv:2310.05499, 2023.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Let your graph do the talking: Encoding structured data for llms", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Bahare", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Tsitsulin", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Al-Rfou", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2402.05862"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Let your graph do the talking: Encoding structured data for llms. arXiv preprint arXiv:2402.05862, 2024.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Can large language models empower molecular property prediction", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.07443"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Can large language models empower molecular property prediction? arXiv preprint arXiv:2307.07443, 2023.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Disentangled representation learning with large language models for text-attributed graphs", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Qin", "suffix": ""}, {"first": "Xin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.18152"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Disentangled representation learning with large language models for text-attributed graphs. arXiv preprint arXiv:2310.18152, 2023.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Sentence-bert: Sentence embeddings using siamese bertnetworks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1908.10084"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>. Sentence-bert: Sentence embeddings using siamese bert- networks. arXiv preprint arXiv:1908.10084, 2019.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Explagraphs: An explanation graph generation task for structured commonsense reasoning", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Prateek", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Bansal", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2104.07644"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Explagraphs: An explanation graph generation task for structured commonsense reasoning. arXiv preprint arXiv:2104.07644, 2021.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Knowledge graph-augmented language models for complex question answering", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Mavadia", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 1st Workshop on Natural Language Reasoning and Structured Explanations (NLRSE)", "volume": "", "issue": "", "pages": "1--8", "other_ids": {"DOI": ["10.18653/v1/2023.nlrse-1.1"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Knowledge graph-augmented language models for complex question answering. In <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, editors, Proceedings of the 1st Workshop on Natural Language Reasoning and Structured Explanations (NLRSE), pages 1-8, Toronto, Canada, June 2023. Association for Computational Linguistics. doi: 10.18653/v1/2023.nlrse-1.1. URL https://aclanthology.org/2023.nlrse-1.1.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Masked label prediction: Unified message passing model for semi-supervised classification", "authors": [{"first": "Yunsheng", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Wenjin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2009.03509"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Masked label prediction: Unified message passing model for semi-supervised classification. arXiv preprint arXiv:2009.03509, 2020.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Think-on-graph: Deep and responsible reasoning of large language model on knowledge graph", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Chengjin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Luming<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Think-on-graph: Deep and responsible reasoning of large language model on knowledge graph. In The Twelfth International Conference on Learning Representations, 2024. URL https://openreview.net/forum?id=nnVO1PvbTv.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Large language models as topological structure enhancers for text-attributed graphs", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Yuxiang", "middle": [], "last": "Ren", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Xuecang", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.14324"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Large language models as topological structure enhancers for text-attributed graphs. arXiv preprint arXiv:2311.14324, 2023.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Graphgpt: Graph instruction tuning for large language models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Shi", "suffix": ""}, {"first": "Lixin", "middle": [], "last": "Su", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dawei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.13023"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Graphgpt: Graph instruction tuning for large language models. arXiv preprint arXiv:2310.13023, 2023.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Graph neural prompting with large language models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Tian", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "Z<PERSON>n", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["V"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Panpan", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.15427"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Graph neural prompting with large language models. arXiv preprint arXiv:2309.15427, 2023.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Llama 2: Open foundation and fine-tuned chat models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Louis", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stone", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Batra", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Shruti", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09288"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Llama 2: Open foundation and fine-tuned chat models. arXiv preprint arXiv:2307.09288, 2023.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Graph attention networks", "authors": [{"first": "Pet<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Guillem", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1710.10903"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Graph attention networks. arXiv preprint arXiv:1710.10903, 2017.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Can language models solve graph problems in natural language", "authors": [{"first": "Heng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "Yulia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.10037"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Can language models solve graph problems in natural language? arXiv preprint arXiv:2305.10037, 2023.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Chain-of-thought prompting elicits reasoning in large language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ma<PERSON>n", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ed", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Le", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in neural information processing systems", "volume": "35", "issue": "", "pages": "24824--24837", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Chain-of-thought prompting elicits reasoning in large language models. Advances in neural information processing systems, 35:24824-24837, 2022.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Next-gpt: Any-to-any multimodal llm", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Leigang", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.05519"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Next-gpt: Any-to-any multimodal llm. arXiv preprint arXiv:2309.05519, 2023.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Natural language is all a graph needs", "authors": [{"first": "Ruosong", "middle": [], "last": "Ye", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shuyuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yongfeng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.07134"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Natural language is all a graph needs. arXiv preprint arXiv:2308.07134, 2023.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "The value of semantic parse labeling for knowledge base question answering", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics", "volume": "2", "issue": "", "pages": "201--206", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. The value of semantic parse labeling for knowledge base question answering. In Proceedings of the 54th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers), pages 201-206, 2016.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Multimodal graph learning for generative tasks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>on", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.07478"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Multimodal graph learning for generative tasks. arXiv preprint arXiv:2310.07478, 2023.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Empower text-attributed graphs learning with large language models (llms)", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuxiang", "middle": [], "last": "Ren", "suffix": ""}, {"first": "Chenghua", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiang", "middle": [], "last": "Li", "suffix": ""}, {"first": "Xuecang", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.09872"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Em- power text-attributed graphs learning with large language models (llms). arXiv preprint arXiv:2310.09872, 2023.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Thought propagation: An analogical approach to complex reasoning with large language models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.03965"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Thought propagation: An analogical approach to complex reasoning with large language models. arXiv preprint arXiv:2310.03965, 2023.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Graph-toolformer: To empower llms with graph reasoning ability via prompt augmented by chatgpt", "authors": [{"first": "Jiaw<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.11116"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>. Graph-toolformer: To empower llms with graph reasoning ability via prompt augmented by chatgpt. arXiv preprint arXiv:2304.11116, 2023.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Llama-adapter: Efficient fine-tuning of language models with zero-init attention", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jiaming", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "Shilin", "middle": [], "last": "Yan", "suffix": ""}, {"first": "Pan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hongsheng", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.16199"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Llama-adapter: Efficient fine-tuning of language models with zero-init attention. arXiv preprint arXiv:2303.16199, 2023.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Graph meets llms: Towards large graph models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Qin", "suffix": ""}, {"first": "Xin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Graph meets llms: Towards large graph models, 2023.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Gimlet: A unified graph-text model for instruction-based molecule zero-shot learning", "authors": [{"first": "Haiteng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Lingpeng", "middle": [], "last": "Kong", "suffix": ""}, {"first": "Qi", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "bioRxiv", "volume": "", "issue": "", "pages": "2023--2025", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>peng <PERSON>, and <PERSON>. Gimlet: A unified graph-text model for instruction-based molecule zero-shot learning. bioRxiv, pages 2023-05, 2023.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Graph reasoning in text space", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Le", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yikang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>g", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Zhaocheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Graphtext", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.01089"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Graphtext: Graph reasoning in text space. arXiv preprint arXiv:2310.01089, 2023.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Minigpt-4: Enhancing vision-language understanding with advanced large language models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiang", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.10592"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Minigpt-4: En- hancing vision-language understanding with advanced large language models. arXiv preprint arXiv:2304.10592, 2023. \"width\": 500, \"objects\": \"681267\": \"name\": \"banana\", \"h\": 34, \"relations\": [\"object\": \"681262\", \"name\": \"to the left of\"], \"w\": 64, \"attributes\": [\"small\", \"yellow\"], \"y\": 55, \"x\": 248, \"681265\": \"name\": \"spots\", \"h\": 16, \"relations\": [], \"w\": 26, \"attributes\": [], \"y\": 92, \"x\": 245, \"681264\": \"name\": \"bananas\", \"h\": 50, \"relations\": [\"object\": \"681259\", \"name\": \"to the left of\"], \"w\": 49, \"attributes\": [\"small\", \"yellow\"], \"y\": 32, \"x\": 268, \"681263\": \"name\": \"picnic\", \"h\": 374, \"relations\": [], \"w\": 499, \"attributes\": [\"delicious\"], \"y\": 0, \"x\": 0, \"681262\": \"name\": \"straw\", \"h\": 95, \"relations\": [\"object\": \"681268\", \"name\": \"to the right of\", \"object\": \"681267\", \"name\": \"to the right of\", \"object\": \"681253\", \"name\": \"to the right of\"], \"w\": 15, \"attributes\": [\"white\", \"plastic\"], \"y\": 55, \"x\": 402, \"681261\": \"name\": \"meat\", \"h\": 27, \"relations\": [\"object\": \"681255\", \"name\": \"on\", \"object\": \"681255\", \"name\": \"inside\"], \"w\": 24, \"attributes\": [\"small\", \"brown\", \"delicious\"], \"y\": 123, \"x\": 68, \"681260\": \"name\": \"rice\", \"h\": 57, \"relations\": [\"object\": \"681255\", \"name\": \"on\", \"object\": \"681258\", \"name\": \"to the left of\"], \"w\": 93, \"attributes\": [\"piled\", \"white\"], \"y\": 162, \"x\": 57, \"681269\": \"name\": \"onions\", \"h\": 16, \"relations\": [], \"w\": 24, \"attributes\": [\"green\"], \"y\": 147, \"x\": 90, \"681268\": \"name\": \"tablecloth\", \"h\": 374, \"relations\": [\"object\": \"681262\", \"name\": \"to the left of\"], \"w\": 396, \"attributes\": [\"white\"], \"y\": 0, \"x\": 0, \"681258\": \"name\": \"bowl\", \"h\": 99, \"relations\": [\"object\": \"681255\", \"name\": \"next to\", \"object\": \"681257\", \"name\": \"of\", \"object\": \"681255\", \"name\": \"near\", \"object\": \"681256\", \"name\": \"to the right of\", \"object\": \"681260\", \"name\": \"to the right of\", \"object\": \"681255\", \"name\": \"to the right of\"], \"w\": 115, \"attributes\": [\"full\"], \"y\": 184, \"x\": 178, \"681259\": \"name\": \"plantains\", \"h\": 70, \"relations\": [\"object\": \"681264\", \"name\": \"to the right of\"], \"w\": 45, \"attributes\": [\"red\"], \"y\": 0, \"x\": 346, \"681256\": \"name\": \"spoon\", \"h\": 65, \"relations\": [\"object\": \"681255\", \"name\": \"on\", \"object\": \"681257\", \"name\": \"to the left of\", \"object\": \"681255\", \"name\": \"in\", \"object\": \"681258\", \"name\": \"to the left of\"], \"w\": 140, \"attributes\": [\"large\", \"metal\", \"silver\"], \"y\": 196, \"x\": 0, \"681257\": \"name\": \"dish\", \"h\": 81, \"relations\": [\"object\": \"681258\", \"name\": \"inside\", \"object\": \"681256\", \"name\": \"to the right of\", \"object\": \"681258\", \"name\": \"in\", \"object\": \"681255\", \"name\": \"to the right of\"], \"w\": 108, \"attributes\": [\"cream colored\"], \"y\": 199, \"x\": 187, \"681254\": \"name\": \"meal\", \"h\": 111, \"relations\": [], \"w\": 130, \"attributes\": [], \"y\": 121, \"x\": 58, \"681255\": \"name\": \"plate\", \"h\": 138, \"relations\": [\"object\": \"681257\", \"name\": \"to the left of\", \"object\": \"681254\", \"name\": \"of\", \"object\": \"681254\", \"name\": \"with\", \"object\": \"681258\", \"name\": \"near\", \"object\": \"681258\", \"name\": \"to the left of\"], \"w\": 176, \"attributes\": [\"white\", \"full\"], \"y\": 111, \"x\": 30, \"681253\": \"name\": \"banana\", \"h\": 30, \"relations\": [\"object\": \"681262\", \"name\": \"to the left of\"], \"w\": 73, \"attributes\": [\"small\", \"yellow\"], \"y\": 87, \"x\": 237, \"height\": 375 node_id,node_attr 0,\"name: banana; attribute: small, yellow; (x,y,w,h): (248, 55, 64, 34)\" 1,\"name: spots; (x,y,w,h): (245, 92, 26, 16)\" 2,\"name: bananas; attribute: small, yellow; (x,y,w,h): (268, 32, 49, 50)\" 3,\"name: picnic; attribute: delicious; (x,y,w,h): (0, 0, 499, 374)\" 4,\"name: straw; attribute: white, plastic; (x,y,w,h): (402, 55, 15, 95)\" 5,\"name: meat; attribute: small, brown, delicious; (x,y,w,h): (68, 123, 24, 27)\" 6,\"name: rice; attribute: piled, white; (x,y,w,h): (57, 162, 93, 57)\" 7,\"name: onions; attribute: green; (x,y,w,h): (90, 147, 24, 16)\" 8,\"name: tablecloth; attribute: white; (x,y,w,h): (0, 0, 396, 374)\" 9,\"name: bowl; attribute: full; (x,y,w,h): (178, 184, 115, 99)\" 10,\"name: plantains; attribute: red; (x,y,w,h): (346, 0, 45, 70)\" 11,\"name: spoon; attribute: large, metal, silver; (x,y,w,h): (0, 196, 140, 65)\" 12,\"name: dish; attribute: cream colored; (x,y,w,h): (187, 199, 108, 81)\" 13,\"name: meal; (x,y,w,h): (58, 121, 130, 111)\" 14,\"name: plate; attribute: white, full; (x,y,w,h): (30, 111, 176, 138)\" 15,\"name: banana; attribute: small, yellow; (x,y,w,h): (237, 87, 73, 30)\" src,edge_attr,dst 0,to the left of,4\\n 2,to the left of,10\\n 4,to the right of,8\\n 4,to the right of,0\\n 4,to the right of,15\\n 5,on,14\\n 5,inside,14\\n 6,on,14\\n 6,to the left of,9\\n 8,to the left of,4\\n 9,next to,14\\n 9,of,12\\n 9,near,14\\n 9,to the right of,11\\n 9,to the right of,6\\n 9,to the right of,14\\n 10,to the right of,2\\n 11,on,14\\n 11,to the left of,12\\n 11,in,14\\n 11,to the left of,9\\n 12,inside,9\\n 12,to the right of,11\\n 12,in,9\\n 12,to the right of,14\\n 14,to the left of,12\\n 14,of,13\\n 14,with,13\\n 14,near,9\\n 14,to the left of,9\\n 15,to the left of,4\\n", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Sports League Award Type', 'freebase.type_hints.included_types', 'Topic'], ['Sports League Award Type', 'type.type.domain', 'Sports'], ['m.0n1v8cy', 'sports.sports_award.award", "authors": [{"first": "<PERSON>", "middle": [], "last": "Webqsp", "suffix": ""}, {"first": "", "middle": [], "last": "Snedeker", "suffix": ""}], "year": 2012, "venue": "FedEx Cup", "volume": "11", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "WebQSP [['FedEx Cup', 'sports.sports_award_type.winners', 'm.0n1v8cy'], ['<PERSON>', 'sports.sports_award_winner.awards', 'm.0n1v8cy'], ['FedEx Cup', 'common.topic.article', 'm.08q5wy'], ['FedEx Cup', 'common.topic.notable_for', 'g.12559n8g_'], ['Sports League Award Type', 'freebase.type_profile.published', 'Published'], ['FedEx Cup', 'common.topic.notable_types', 'Sports League Award Type'], ['m.0n1v8cy', 'sports.sports_award.award_winner', '<PERSON>'], ['Sports League Award Type', 'type.type.expected_by', 'Award'], ['Sports League Award Type', 'common.topic.article', 'm.06zxtxj'], ['2012 PGA Tour', 'sports.sports_league_season.awards', 'm.0n1v8cy'], ['Sports League Award Type', 'freebase.type_hints.included_types', 'Topic'], ['Sports League Award Type', 'type.type.domain', 'Sports'], ['m.0n1v8cy', 'sports.sports_award.award', 'FedEx Cup'], ['Sports League Award Type', 'freebase.type_profile.strict_included_types', 'Topic'], ['Sports League Award Type', 'freebase.type_profile.kind', 'Classification'], ['m.0n1v8cy', 'sports.sports_award.season', '2012 PGA Tour'], ['Sports League Award Type', 'type.type.properties', 'Winners']] node_id,node_attr\\n 0,fedex cup\\n 1,m.0n1v8cy\\n 2,brandt snedeker\\n 3,m.08q5wy\\n 4,g.12559n8g_\\n 5,sports league award type\\n 6,published\\n 7,award\\n 8,m.06zxtxj\\n 9,2012 pga tour\\n 10,topic\\n 11,sports\\n 12,classification\\n 13,winners\\n src,edge_attr,dst 0,sports.sports_award_type.winners,1 2,sports.sports_award_winner.awards,1 0,common.topic.article,3 0,common.topic.notable_for,4 5,freebase.type_profile.published,6 0,common.topic.notable_types,5 1,sports.sports_award.award_winner,2 5,type.type.expected_by,7 5,common.topic.article,8", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "The original GQA dataset is designed for real-world visual reasoning and compositional question answering, aiming to address key shortcomings of previous VQA datasets [13]. It comprises 108k images, each associated with a Scene Graph. In our study, we focus differently on graph question answering; hence, we did not utilize the image counterparts, leveraging only the scene graphs from the original dataset. Additionally, the original dataset describes images using JSON files. We simplified the object IDs to suit our research needs. We randomly sampled 100k samples from the original dataset and divided them into training", "authors": [{"first": "", "middle": [], "last": "Scenegraphs", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "SceneGraphs. The original GQA dataset is designed for real-world visual reasoning and composi- tional question answering, aiming to address key shortcomings of previous VQA datasets [13]. It comprises 108k images, each associated with a Scene Graph. In our study, we focus differently on graph question answering; hence, we did not utilize the image counterparts, leveraging only the scene graphs from the original dataset. Additionally, the original dataset describes images using JSON files. We simplified the object IDs to suit our research needs. We randomly sampled 100k samples from the original dataset and divided them into training, validation, and test subsets, following a 6:2:2 ratio.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "We follow the preprocessing steps from RoG 2 [28]. The original dataset uses a list of triplets format, which we have transformed into our unified graph format. Furthermore, to avoid • For initial submissions, do not include any information that would break anonymity", "authors": [{"first": "", "middle": [], "last": "Webqsp", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "WebQSP. We follow the preprocessing steps from RoG 2 [28]. The original dataset uses a list of triplets format, which we have transformed into our unified graph format. Furthermore, to avoid • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "links": null}}, "ref_entries": {"FIGREF1": {"num": null, "text": "Figure 1: We develop a flexible question-answering framework targeting real-world textual graph applications via a unified conversational interface. Presented here are examples showcasing the model's adeptness in handling generative and creative queries in practical graph-related tasks: common sense reasoning, scene understanding, and knowledge graph reasoning, respectively.", "type_str": "figure", "uris": null, "fig_num": "1"}, "FIGREF2": {"num": null, "text": "Figure 2: Illustrative examples from the GraphQA benchmark datasets.", "type_str": "figure", "uris": null, "fig_num": "2"}, "FIGREF3": {"num": null, "text": "Figure3: Overview of the proposed G-Retriever: 1) Indexing: Graphs are indexed for efficient query processing; 2) Retrieval: The most semantically relevant nodes and edges are retrieved, conditioned on the query; 3) Subgraph Construction: A connected subgraph is extracted, covering as many relevant nodes and edges as possible while maintaining a manageable graph size; 4) Generation: An answer is generated using a 'graph prompt', a textualized graph, and the query.", "type_str": "figure", "uris": null, "fig_num": "3"}, "FIGREF4": {"num": null, "text": "Figure 4: Model configuration 1) Inference-only.", "type_str": "figure", "uris": null, "fig_num": "4"}, "FIGREF5": {"num": null, "text": "Figure 5: Model configuration 2) Frozen LLM w/ prompt tuning.", "type_str": "figure", "uris": null, "fig_num": "5"}, "FIGREF6": {"num": null, "text": "bieber 551, m.0gxnnwp src, edge_attr, dst 294, parents, 356 356, children, 15 551, sibling, 294 551, sibling, 15 Question: What is the name of justin bieber brother? Answer: trainable (b) G-Retriever w/ LoRA", "type_str": "figure", "uris": null, "fig_num": null}, "FIGREF7": {"num": null, "text": "Figure 6: Model configuration 3) Tuned LLM.", "type_str": "figure", "uris": null, "fig_num": "6"}, "FIGREF9": {"num": null, "text": "Figure 7: Ablation study configurations.", "type_str": "figure", "uris": null, "fig_num": "7"}, "FIGREF10": {"num": null, "text": "Graph node_id,node_attr 0,name: head; attribute: black; (x,y,w,h): (386, 185, 8, 4) 1,name: swimming pool; attribute: small, water; (x,y,w,h): (465, 92, 35, 3) 2,name: shore; (x,y,w,h): (49, 85, 436, 108) 3,name: fence; attribute: wired; (x,y,w,h): (11, 158, 167, 46) 4,name: mountains; attribute: covered; (x,y,w,h): (393, 53, 107, 12) 5,name: water; attribute: blue; (x,y,w,h): (0, 50, 499, 154) 6,name: sheep; attribute: walking, grazing; (x,y,w,h): (243, 191, 20, 13) 7,name: sky; attribute: blue, clear, cloudy; (x,y,w,h): (0, 0, 501, 60) 8,name: sheep; attribute: white, fluffy; (x,y,w,h): (409, 176, 13, 10) 9,name: field; attribute: large, green, grassy; (x,y,w,h): (0, 138, 503, 112) 10,name: grass; (x,y,w,h): (0, 84, 500, 166) 11,name: sheep; attribute: white, fluffy; (x,y,w,h): (398, 178, 13, 11) 12,name: sheep; attribute: white, fluffy; (x,y,w,h): (385, 179, 17, 11) 13,name: sheep; attribute: white, fluffy; (x,y,w,h): (364, 181, 14, 10) 14,name: shore; attribute: sandy; (x,y,w,h): (190, 123, 171, 12) src, edge_attr, dst 0,to the left of,11\\n 0,to the right of,13\\n 3,to the left of,6\\n 6,to the right of,3\\n 6,grazing on,10\\n 6,in,9\\n 6,in,10\\n 7,above,5\\n 8,eating,10\\n 8,to the right of,11\\n 8,to the right of,12\\n 11,to the left of,8\\n 11,to the right of,0\\n 11,to the right of,13\\n 11,to the right of,12\\n 11,eating,10\\n 12,eating,10\\n 12,to the left of,8\\n 12,to the right of,13\\n 12,to the left of,11\\n 13,to the left of,0\\n 13,to the left of,11\\n 13,to the left of,12\\n 13,eating,10\\n Image User Write an 500-word advertisement for this place in the scene graph that would make people want to visit it.", "type_str": "figure", "uris": null, "fig_num": null}, "TABREF0": {"num": null, "text": "Observation and mitigation of hallucination in graph LLMs.", "type_str": "table", "html": null, "content": "<table><tr><td>User</td><td>elephants eating</td><td>in</td><td>bushes</td><td>standing by elephant</td></tr><tr><td/><td>grass</td><td colspan=\"2\">on the side of</td><td>river</td></tr><tr><td/><td colspan=\"4\">What kind of animal is in the bushes?</td></tr><tr><td/><td colspan=\"4\">Answer the question and list the nodes or edges from the graph that support your answer.</td></tr><tr><td/><td colspan=\"4\">[Incorrect response with hallucinated nodes and edges]</td></tr><tr><td/><td colspan=\"4\">The animal in the bushes is a deer.</td></tr><tr><td/><td>Nodes:</td><td/><td/><td/></tr><tr><td/><td colspan=\"2\">* Deer (node 1)</td><td/><td/></tr><tr><td>LLM w/ Graph Prompt Tuning</td><td colspan=\"3\">* Bushes (node 2)</td><td/></tr><tr><td/><td>Edges:</td><td/><td/><td/></tr><tr><td/><td colspan=\"4\">* Deer → Bushes (edge 1)</td></tr><tr><td/><td colspan=\"4\">* Deer → Grass (edge 2)</td></tr><tr><td/><td colspan=\"4\">* Bushes → Grass (edge 3)</td></tr><tr><td/><td colspan=\"4\">[Correct response with accurate node and edge references]</td></tr><tr><td>G-Retriever (Ours)</td><td/><td/><td/><td/></tr></table>"}, "TABREF1": {"num": null, "text": "Summary of datasets used in GraphQA benchmark.", "type_str": "table", "html": null, "content": "<table><tr><td>Dataset</td><td/><td/><td>ExplaGraphs</td><td/><td/><td colspan=\"2\">SceneGraphs</td><td/><td/><td/><td>WebQSP</td></tr><tr><td>#Graphs</td><td/><td/><td>2,766</td><td/><td/><td colspan=\"2\">100,000</td><td/><td/><td/><td>4,737</td></tr><tr><td colspan=\"2\">Avg. #Nodes</td><td/><td>5.17</td><td/><td/><td colspan=\"2\">19.13</td><td/><td/><td/><td>1370.89</td></tr><tr><td colspan=\"2\">Avg. #Edges</td><td/><td>4.25</td><td/><td/><td colspan=\"2\">68.44</td><td/><td/><td/><td>4252.37</td></tr><tr><td colspan=\"2\">Node Attribute</td><td colspan=\"3\">Commonsense concepts</td><td colspan=\"3\">Object attributes (e.g., color, shape)</td><td/><td/><td colspan=\"2\">Entities in Freebase</td></tr><tr><td colspan=\"2\">Edge Attribute</td><td colspan=\"3\">Commonsense relations</td><td colspan=\"4\">Relations (e.g., actions, spatial relations)</td><td/><td colspan=\"2\">Relations in Freebase</td></tr><tr><td>Task</td><td/><td colspan=\"3\">Common sense reasoning</td><td colspan=\"3\">Scene graph question answering</td><td colspan=\"4\">Knowledge based question answering</td></tr><tr><td colspan=\"2\">Evaluation Matrix</td><td/><td>Accuracy</td><td/><td/><td colspan=\"2\">Accuracy</td><td/><td/><td/><td>Hit@1</td></tr><tr><td/><td/><td/><td>Explanation Graph</td><td/><td/><td/><td>Scene Graph</td><td/><td/><td/><td>Knowledge Graph</td></tr><tr><td>citizens</td><td>women and men causes is a</td><td>women causes</td><td>desire capable of help the country</td><td/><td>to the right of</td><td>person sitting</td><td>in front of</td><td>sibling_s</td><td colspan=\"2\">justin bieber</td><td>parents</td><td>jeremy children bieber</td></tr><tr><td/><td>have same rights</td><td colspan=\"2\">be in combat</td><td/><td>woman</td><td/><td>computer</td><td>m.0gxnnwp</td><td>sibling</td><td/><td>jaxon bieber</td></tr><tr><td/><td colspan=\"3\">Textualized Graph</td><td/><td/><td>Textualized Graph</td><td/><td/><td/><td colspan=\"2\">Textualized Graph</td></tr><tr><td colspan=\"2\">node_id, node_attr</td><td colspan=\"2\">src, edge_attr, dst</td><td colspan=\"2\">node_id, node_attr</td><td/><td/><td colspan=\"2\">node_id, node_attr</td><td colspan=\"2\">src, edge_attr, dst</td></tr><tr><td colspan=\"2\">0, women and men</td><td>0, is a,1</td><td/><td colspan=\"3\">2, name: computer; (x,y,w,h): (8, 119, 34, 32)</td><td/><td>15, justin bieber</td><td/><td colspan=\"2\">15, people.person.parents, 356</td></tr><tr><td colspan=\"2\">1, citizens</td><td colspan=\"2\">1, causes, 2</td><td colspan=\"4\">3, name: person; attribute: sitting; (x,y,w,h): (169, 75, 49, 40)</td><td colspan=\"2\">294, jaxon bieber</td><td colspan=\"2\">15, people.person.sibling_s, 551</td></tr><tr><td colspan=\"2\">2, have same rights</td><td colspan=\"2\">2, causes, 3</td><td colspan=\"3\">15, name: woman; (x,y,w,h): (255, 18, 235, 292)</td><td/><td colspan=\"2\">356, jeremy bieber</td><td colspan=\"2\">356, people.person.children, 294</td></tr><tr><td colspan=\"2\">3, women</td><td colspan=\"2\">3, capable of, 4</td><td colspan=\"2\">src, edge_attr, dst</td><td/><td/><td colspan=\"2\">551, m.0gxnnwp</td><td colspan=\"2\">551, people.sibling_relationship.sibling, 294</td></tr><tr><td colspan=\"2\">4, help the country</td><td colspan=\"2\">4, desires, 5</td><td colspan=\"2\">15, to the right of, 3</td><td/><td/><td/><td/><td/></tr><tr><td colspan=\"2\">5, be in combat</td><td/><td/><td colspan=\"2\">2, in front of, 3</td><td/><td/><td/><td/><td/></tr><tr><td/><td/><td>Question</td><td/><td/><td/><td>Question</td><td/><td/><td/><td/><td>Question</td></tr><tr><td colspan=\"4\">Arguement 1: Women should not be in combat.</td><td colspan=\"4\">Question: Is there a woman to the right of the person behind the</td><td colspan=\"4\">Question: what is the name of justin bieber brother</td></tr><tr><td colspan=\"4\">Arguement 2: Women and men have the same rights.</td><td colspan=\"2\">computer?</td><td/><td/><td/><td/><td/></tr><tr><td colspan=\"4\">Question: Do argument 1 and argument 2 support or counter each other?</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"4\">Answer in one word in the form of 'support' or 'counter'.</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td/><td/><td>Answer</td><td/><td/><td/><td>Answer</td><td/><td/><td/><td/><td>Answer</td></tr><tr><td>counter</td><td/><td/><td/><td>yes</td><td/><td/><td/><td>jaxon bieber</td><td/><td/></tr></table>"}, "TABREF2": {"num": null, "text": "", "type_str": "table", "html": null, "content": "<table><tr><td>Setting</td><td>Method</td><td>ExplaGraphs</td><td>SceneGraphs</td><td>WebQSP</td></tr><tr><td/><td>Zero-shot</td><td>0.5650</td><td>0.3974</td><td>41.06</td></tr><tr><td>Inference-only</td><td>Zero-CoT [18] CoT-BAG [44]</td><td>0.5704 0.5794</td><td>0.5260 0.5680</td><td>51.30 39.60</td></tr><tr><td/><td>KAPING [1]</td><td>0.6227</td><td>0.4375</td><td>52.64</td></tr><tr><td/><td>Prompt tuning</td><td colspan=\"3\">0.5763 ± 0.0243 0.6341 ± 0.0024 48.34 ± 0.64</td></tr><tr><td>Frozen LLM w/ PT</td><td>GraphToken [31] G-Retriever</td><td colspan=\"3\">0.8508 ± 0.0551 0.4903 ± 0.0105 57.05 ± 0.74 0.8516 ± 0.0092 0.8131 ± 0.0162 70.49 ± 1.21</td></tr><tr><td/><td>∆Prompt tuning</td><td>↑ 47.77%</td><td>↑ 28.23%</td><td>↑ 45.81%</td></tr><tr><td/><td>LoRA</td><td colspan=\"3\">0.8538 ± 0.0353 0.7862 ± 0.0031 66.03 ± 0.47</td></tr><tr><td>Tuned LLM</td><td colspan=\"4\">G-Retriever w/ LoRA 0.8705 ± 0.0329 0.8683 ± 0.0072 73.79 ± 0.70</td></tr><tr><td/><td>∆ LoRA</td><td>↑ 1.95%</td><td>↑ 11.74%</td><td>↑ 10.44%</td></tr></table>"}, "TABREF3": {"num": null, "text": "Retrieval on graphs significantly improves efficiency.", "type_str": "table", "html": null, "content": "<table><tr><td>Dataset</td><td colspan=\"3\">Before Retrieval (Avg.)</td><td colspan=\"3\">After Retrieval (Avg.)</td></tr><tr><td/><td colspan=\"3\"># Tokens # Nodes Min/Epoch</td><td># Tokens</td><td># Nodes</td><td>Min/Epoch</td></tr><tr><td>SceneGraphs</td><td>1,396</td><td>19</td><td>123.1</td><td>235 (↓83%)</td><td>5 (↓74%)</td><td>86.8 (↓29%)</td></tr><tr><td>WebQSP</td><td>100,627</td><td>1,371</td><td>18.7</td><td colspan=\"2\">610 (↓99%) 18 (↓99%)</td><td>6.2(↓67%)</td></tr></table>"}, "TABREF4": {"num": null, "text": "Implementing our graph-based retrieval significantly decreases the number of tokens required to describe the graphs in text, reduces the number of nodes in graphs, and speeds up the training process. Specifically, for the SceneGraphs dataset, tokens decreased by 83%, nodes by 74%, and training time by 29%. For the WebQSP dataset, tokens decreased by 99%, nodes by 99%, and training time by 67%. These substantial reductions demonstrate the method's efficiency and potential in managing large-scale graph data.", "type_str": "table", "html": null, "content": "<table/>"}, "TABREF5": {"num": null, "text": "Hallucination reduction on the SceneGraphs dataset, measured by fractions of valid nodes, valid edges, and fully valid graphs (where all nodes and edges are correct).", "type_str": "table", "html": null, "content": "<table><tr><td/><td colspan=\"2\">Baseline G-Retriever</td></tr><tr><td>Valid Nodes</td><td>31%</td><td>77%</td></tr><tr><td>Valid Edges</td><td>12%</td><td>76%</td></tr><tr><td colspan=\"2\">Fully Valid Graphs 8%</td><td>62%</td></tr></table>"}, "TABREF6": {"num": null, "text": "Ablation study on the WebQSP dataset showing performance drops (Hit@1) when each component is removed.", "type_str": "table", "html": null, "content": "<table><tr><td>Method</td><td>Hit@1</td></tr><tr><td>G-Retriever</td><td>70.49</td></tr><tr><td>w/o Graph Encoder</td><td>54.62 (↓22.51%)</td></tr><tr><td>w/o Projection Layer</td><td>69.70 (↓1.11%)</td></tr><tr><td colspan=\"2\">w/o Textualized Graph 56.96 (↓19.19%)</td></tr><tr><td>w/o Retrieval</td><td>63.84 (↓9.43%)</td></tr></table>"}, "TABREF9": {"num": null, "text": "Performance of different LLMs on the WebQSP dataset.", "type_str": "table", "html": null, "content": "<table><tr><td>LLM</td><td colspan=\"2\">Llama2-7b Llama2-13b</td></tr><tr><td>Hit@1</td><td>70.49</td><td>75.58</td></tr></table>"}, "TABREF10": {"num": null, "text": "Comparison of text formats in original datasets and our GraphQA benchmark.", "type_str": "table", "html": null, "content": "<table><tr><td>Dataset</td><td>Original dataset</td><td>GraphQA Benmark</td></tr><tr><td/><td/><td>node_id,node_attr\\n 0,entrapment\\n 1,being abused\\n 2,police\\n 3,harm\\n 4,people\\n</td></tr><tr><td>ExplaGraphs</td><td>(entrapment; capable of; being abused) (being abused; created by; police) (police; capable of; harm) (harm; used for; people) (people; part of; citizens)</td><td>5,citizens\\n src,edge_attr,dst\\n 0,capable of,1\\n 1,created 2,capable of,3\\n 3,used for,4\\n</td></tr><tr><td/><td/><td>4,part of,5\\n</td></tr></table>"}, "TABREF11": {"num": null, "text": "Comparison of retrieval methods on the WebQSP dataset.", "type_str": "table", "html": null, "content": "<table><tr><td>Method</td><td>Hit@1</td></tr><tr><td>PCST retrieval</td><td>66.17</td></tr><tr><td colspan=\"2\">top-k triples retrieval (KAPING) 52.64</td></tr><tr><td>top-k nodes plus its neighbors</td><td>49.82</td></tr><tr><td>shortest path retrieval</td><td>55.20</td></tr></table>"}, "TABREF12": {"num": null, "text": "Performance and Efficiency of Various Methods on the WebQSP dataset. By returning the most relevant subgraph in response to a query, users can see which parts of the graph are considered important for the answer. This helps users understand the basis of the model's responses. For example, if users want to understand why certain information is present or absent in the LLM's response, they can inspect the subgraph to see whether such information is present or absent in the retrieved subgraph.", "type_str": "table", "html": null, "content": "<table><tr><td>Settting</td><td>Method</td><td colspan=\"2\">Hit@1 Time</td></tr><tr><td>Inference-only</td><td colspan=\"2\">Question only Textual graph and question 41.06 61.16</td><td>31 min 40 min</td></tr><tr><td>Frozen LLM w/ PT</td><td>Prompt Tuning G-Retriever</td><td>48.34 70.49</td><td>18.7 min/epoch 6.2 min/epoch</td></tr><tr><td>Tuned LLM</td><td>LoRA G-Retriever w/ LoRA</td><td>66.03 73.79</td><td>19 min/epoch 6.9 min/epoch</td></tr><tr><td colspan=\"2\">F Discussion on Explainability</td><td/><td/></tr></table>"}, "TABREF13": {"num": null, "text": "Example of creative chat on ExplaGraphs.", "type_str": "table", "html": null, "content": "<table><tr><td>Graph node_id,node_attr</td></tr><tr><td>0,human</td></tr><tr><td>1,cosmetic surgery</td></tr><tr><td>2,women</td></tr><tr><td>3,change appearance</td></tr><tr><td>4,addiction</td></tr><tr><td>5,searching for perfection</td></tr><tr><td>6,stop</td></tr><tr><td>7,positive</td></tr><tr><td>src,edge_attr,dst</td></tr><tr><td>0,receives action,1</td></tr><tr><td>2,part of,0</td></tr><tr><td>1,used for,3</td></tr><tr><td>3,capable of,4</td></tr><tr><td>4,created by,5</td></tr><tr><td>4,not capable of,6</td></tr><tr><td>4,is not a,7</td></tr></table>"}, "TABREF14": {"num": null, "text": "Example of creative chat on SceneGraphs. We utilize GPT-4 to generate an image from the graph, assisting readers in visualizing it", "type_str": "table", "html": null, "content": "<table/>"}, "TABREF15": {"num": null, "text": "Example of creative conversation on the WebQSP dataset. Based on the provided knowledge graph, here's an educational tour that highlights interesting facts and connections related to <PERSON>'s inventions and other notable achievements: 1. <PERSON>'s Lightning Rod: Start by exploring <PERSON>'s most famous invention -the lightning rod. Discover how <PERSON>'s invention protected buildings from lightning strikes and how it changed the way people understood electricity. Connection: <PERSON> worked with <PERSON> on the lightning rod, and their collaboration led to the development of the concept of positive and negative charges. 2. Bifocals: Move on to <PERSON>'s invention of bifocal glasses. Learn how <PERSON>'s vision problems inspired him to create a new type of glasses that allowed people to see both near and far objects clearly. Connection: <PERSON>'s bifocal glasses were a groundbreaking invention that improved the lives of millions of people around the world. 3. <PERSON>: Explore <PERSON>'s design of the <PERSON> stove, a cast-iron stove that provided more heat with less fuel. Discover how <PERSON>'s stove revolutionized heating systems and how it helped to reduce fuel costs for households and businesses. Connection: <PERSON>'s stove was an early example of energy efficiency and sustainability, and it paved the way for modern heating systems. 4. <PERSON><PERSON><PERSON>: Learn about <PERSON>'s connection to <PERSON><PERSON><PERSON>, a Czech inventor who built on <PERSON>'s work and developed the first electric generator. Discover how <PERSON><PERSON><PERSON>'s invention helped to power the Industrial Revolution and how it changed the way people lived and worked. Connection: <PERSON>'s work on the lightning rod inspired <PERSON><PERSON><PERSON> to explore the properties of electricity, and their collaborative efforts helped to advance the field of electrical engineering. 5. Innovator and Original Ideas: Explore <PERSON>'s reputation as an innovator and his ability to come up with original ideas. <PERSON>ver how <PERSON>'s creativity and curiosity led to many of his inventions and how he was able to see connections between seemingly unrelated things. <PERSON>: <PERSON>'s innovative spirit and ability to think outside the box inspired many other inventors and scientists, including <PERSON> <PERSON> and <PERSON> Tesla. 6. Collaboration and Networking: Highlight <PERSON>'s collaborative approach to invention and his ability to network with other inventors and scientists. <PERSON>ver how <PERSON>'s connections with other innovators helped to advance the field of science and technology. Connection: Franklin's collaborations with other inventors and scientists, such as Thomas Jefferson and Prokop Diviš, helped to accelerate the pace of innovation and pave the way for future breakthroughs. 7.", "type_str": "table", "html": null, "content": "<table><tr><td/><td>node_id, node_attr</td></tr><tr><td/><td>8, benjamin franklin</td></tr><tr><td/><td>63, glass harmonica</td></tr><tr><td/><td>98, lightning rod</td></tr><tr><td/><td>99, stealing god's thunder: benjamin franklin's lightning rod and the invention of america</td></tr><tr><td/><td>227, benjamin franklin worked with thoma<PERSON> <PERSON><PERSON><PERSON><PERSON></td></tr><tr><td/><td>442, franklin stove</td></tr><tr><td/><td>525, bifocals</td></tr><tr><td/><td>706, benjamin franklin drawing electricity from the sky</td></tr><tr><td/><td>1044, prokop diviš</td></tr><tr><td/><td>src, edge_attr, dst</td></tr><tr><td>Graph</td><td>706, visual_art.artwork.art_subject, 8 8, base.kwebbase.kwtopic.connections_from, 227</td></tr><tr><td/><td>8, book.book_subject.works, 99</td></tr><tr><td/><td>525, law.invention.inventor, 8</td></tr><tr><td/><td>442, law.invention.inventor, 8</td></tr><tr><td/><td>98, law.invention.inventor, 1044</td></tr><tr><td/><td>8, law.inventor.inventions, 98</td></tr><tr><td/><td>63, law.invention.inventor, 8</td></tr><tr><td/><td>8, base.argumentmaps.innovator.original_ideas, 442</td></tr><tr><td/><td>98, law.invention.inventor, 8</td></tr><tr><td/><td>8, law.inventor.inventions, 525</td></tr><tr><td/><td>442, base.argumentmaps.original_idea.innovator, 8</td></tr><tr><td/><td>8, law.inventor.inventions, 442</td></tr><tr><td/><td>8, law.inventor.inventions, 63</td></tr><tr><td>User</td><td>Question: what else did ben franklin invent? Surrounding this question, develop an educational tour through the knowledge graph, highlighting interesting facts and connections in an engaging way.</td></tr><tr><td/><td>Great!</td></tr><tr><td>Model</td><td/></tr></table>"}, "TABREF16": {"num": null, "text": "While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).• Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.", "type_str": "table", "html": null, "content": "<table><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: Our codes and datasets are available at: https://anonymous.4open.</td></tr><tr><td>science/r/G-Retriever.</td></tr><tr><td>Guidelines:</td></tr><tr><td>• The answer NA means that paper does not include experiments requiring code.</td></tr><tr><td>• Please see the NeurIPS code and data submission guidelines (https://nips.cc/</td></tr><tr><td>public/guides/CodeSubmissionPolicy) for more details.</td></tr><tr><td>•</td></tr><tr><td>: [NA]</td></tr></table>"}, "TABREF17": {"num": null, "text": "• It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "type_str": "table", "html": null, "content": "<table><tr><td>8. Experiments Compute Resources</td></tr><tr><td>Question: For each experiment, does the paper provide sufficient information on the com-</td></tr><tr><td>puter resources (type of compute workers, memory, time of execution) needed to reproduce</td></tr><tr><td>the experiments?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: We reported the required compute resources in the Appendix E.</td></tr><tr><td>Guidelines:</td></tr><tr><td>•</td></tr></table>"}, "TABREF18": {"num": null, "text": "13. New Assets Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets? Answer:[Yes]   Justification: The new dataset introduced in the paper is accompanied by detailed documentation, including data collection methods, preprocessing steps, and usage instructions, as provided in Appendix C. Guidelines:• The answer NA means that the paper does not release new assets. • Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • At submission time, remember to anonymize your assets (if applicable). You can either create an anonymized URL or include an anonymized zip file. 14. Crowdsourcing and Research with Human Subjects Question: For crowdsourcing experiments and research with human subjects, does the paper include the full text of instructions given to participants and screenshots, if applicable, as well as details about compensation (if any)? Answer: [NA] Justification: The paper does not involve crowdsourcing nor research with human subjects. Guidelines: • The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Including this information in the supplemental material is fine, but if the main contribution of the paper involves human subjects, then as much detail as possible should be included in the main paper. • According to the NeurIPS Code of Ethics, workers involved in data collection, curation, or other labor should be paid at least the minimum wage in the country of the data collector. 15. Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects Question: Does the paper describe potential risks incurred by study participants, whether such risks were disclosed to the subjects, and whether Institutional Review Board (IRB) approvals (or an equivalent approval/review based on the requirements of your country or institution) were obtained? Answer: [NA] Justification: The paper does not involve crowdsourcing nor research with human subjects. Guidelines: • The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution.", "type_str": "table", "html": null, "content": "<table/>"}}}}