{"paper_id": "universal_neural_functional", "title": "Universal Neural Functionals", "abstract": "A challenging problem in many modern machine learning tasks is to process weight-space features, i.e., to transform or extract information from the weights and gradients of a neural network. Recent works have developed promising weight-space models that are equivariant to the permutation symmetries of simple feedforward networks. However, they are not applicable to general architectures, since the permutation symmetries of a weight space can be complicated by recurrence or residual connections. This work proposes an algorithm that automatically constructs permutation equivariant models, which we refer to as universal neural functionals (UNFs), for any weight space. Among other applications, we demonstrate how UNFs can be substituted into existing learned optimizer designs, and find promising improvements over prior methods when optimizing small image classifiers and language models. Our results suggest that learned optimizers can benefit from considering the (symmetry) structure of the weight space they optimize. We open-source our library for constructing UNFs at https://github.com/AllanYangZhou/universal_neural_functional.", "pdf_parse": {"paper_id": "universal_neural_functional", "abstract": [{"text": "A challenging problem in many modern machine learning tasks is to process weight-space features, i.e., to transform or extract information from the weights and gradients of a neural network. Recent works have developed promising weight-space models that are equivariant to the permutation symmetries of simple feedforward networks. However, they are not applicable to general architectures, since the permutation symmetries of a weight space can be complicated by recurrence or residual connections. This work proposes an algorithm that automatically constructs permutation equivariant models, which we refer to as universal neural functionals (UNFs), for any weight space. Among other applications, we demonstrate how UNFs can be substituted into existing learned optimizer designs, and find promising improvements over prior methods when optimizing small image classifiers and language models. Our results suggest that learned optimizers can benefit from considering the (symmetry) structure of the weight space they optimize. We open-source our library for constructing UNFs at https://github.com/AllanYangZhou/universal_neural_functional.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Many problems in machine learning require handling weight-space features, such as the weights, gradients, or sparsity masks of neural networks. For example, optimizers iteratively map the current weights and gradient history to updated weights. Taking this perspective, researchers have proposed a variety of data-driven methods that train a neural network to process these weight-space features. Examples applications of these neural functionals [<PERSON> et al., 2023a] include training neural networks to predict classifier generalization from weights [<PERSON><PERSON><PERSON><PERSON> et al., 2020] , to optimize other networks [<PERSON> et al., 2022] , and to classify or edit implicit neural representations (INRs) [<PERSON> et al., 2023] .", "section": "Introduction", "sec_num": "1"}, {"text": "Until recently, researchers lacked a unifying and principled framework for designing neural functionals, and would implement a custom model for their particular weight-space task. A significant recent advance was the development of weight-space models that are permutation equivariant [<PERSON><PERSON> et al., 2023 , <PERSON> et al., 2023a] . Neuron permutation symmetries arise in a neural network's weight space because re-ordering hidden neurons has no effect on the network's function [<PERSON><PERSON><PERSON>, 1990] . A permutation equivariant neural functional can guarantee that under a neuron permutation of its input, its output permutes accordingly. <PERSON><PERSON> et al. [2023] showed that permutation equivariance significantly improves performance on weight-space tasks, but their models only apply to the weight spaces of simple feedforward multilayer perceptrons (MLPs). Permutation equivariant neural functionals [<PERSON> et al., 2023a] added the ability to process weights from simple feedforward convolutional networks (CNNs). However, in practice we may deal with the weight spaces of complex networks that have residual connections, recurrence, normalization layers, and so on. Extending existing approaches to each possible weight space would be tedious and challenging.", "section": "Introduction", "sec_num": "1"}, {"text": "Figure 1 : Illustration of the permutation symmetries in the weight space of a recurrent neural network (Example 2.2). Left: Each layer contains feedforward (ff) weights mapping between different layer's activations, and recurrent (rec) weights transforming activations over time. We can permute the hidden activations as illustrated without changing the final outputs h L t . Right: Permuting the hidden activations induces a permutation on the weights. Here, the rows and columns of the feedforward weights are permuted by (σ ℓ+1 , σ ℓ ), while the recurrent weights are permuted by (σ ℓ , σ ℓ ). Our algorithm automatically constructs permutation equivariant models for any collection of weight tensors given a description of its symmetries (Appendix A).", "section": "Introduction", "sec_num": "1"}, {"text": "We propose an approach that automatically constructs permutation equivariant models for any collection of tensors whose dimensions can permute according to a shared set of permutations. This naturally encompasses the permutation equivariance we might desire for any given weight space. We show that our algorithm constructs the most general linear layer that operates on a given weight space while guaranteeing equivariance to the specified permutation symmetries. Stacking multiple such layers with pointwise nonlinearities produces a deep permutation equivariant model, which we refer to as a universal neural functional.", "section": "Introduction", "sec_num": "1"}, {"text": "To evaluate the empirical effectiveness of UNFs, we apply them to tasks that require processing networks with complex architectures containing recurrence, layer normalization, residual connections, and more. We use UNFs to implement learned optimizers and then optimize small image classifiers, RNNs, and Transformer language models, observing promising improvements over prior methods. In a generalization prediction task, we use UNF to predict the performance of sequence-to-sequence RNN models from their weights. Our experiments show that universal neural functionals are flexible, can be easily applied to different weight spaces, and improve upon prior weight-space methods.", "section": "Introduction", "sec_num": "1"}, {"text": "We largely follow or extend the notation and naming of <PERSON> et al. [2023a] . Given a fixed neural network architecture, there is a weight space W of possible parameters (weights, biases, normalization scalings, etc.) . We refer to all such parameters as \"weights\". A particular set of weights W = W (1) , • • • , W (L) contains multiple \"tensors\", or multidimensional arrays. Depending on the architecture, W contains numerous symmetries [<PERSON><PERSON><PERSON>, 1990 , <PERSON> et al., 2022] , i.e., transformations on the weight space that do not affect the network's behavior. Following prior work [<PERSON><PERSON> et al., 2023 , <PERSON> et al., 2023a] , this work focuses only on the permutation symmetries, which are called neuron permutations.", "section": "Preliminaries", "sec_num": "2"}, {"text": "Neuron permutations correspond to re-arranging the neurons within (hidden) layers, which have no canonical ordering. We make the simplifying assumption that all layers can be re-arranged-this assumption can be later corrected using positional encodings [<PERSON> et al., 2023a] . Assuming there are N independently permutable layers of neurons, the neuron permutation group is the direct product S = S n1 × • • • × S n N , where n i is the number of neurons being permuted in each layer.", "section": "Preliminaries", "sec_num": "2"}, {"text": "In general, each weight is a \"tensor\" (multi-dimensional array) of real numbers. Using M (a, b,", "section": "Preliminaries", "sec_num": "2"}, {"text": "• • • ) to denote arrays R a×b×••• , consider a rank-D ℓ tensor W (ℓ) ∈ M n d ℓ 1 , • • • , n d ℓ D ℓ", "section": "Preliminaries", "sec_num": "2"}, {"text": ". Each dimension", "section": "Preliminaries", "sec_num": "2"}, {"text": "d ℓ i is permuted by σ d ℓ i .", "section": "Preliminaries", "sec_num": "2"}, {"text": "That is, the action of σ on the indices of the weight tensor is:", "section": "Preliminaries", "sec_num": "2"}, {"text": "EQUATION", "section": "Preliminaries", "sec_num": "2"}, {"text": "Defining the multi-index ⃗ i := (i 1 , • • • , i D ℓ ), the action on the weight tensor is to permute the entries: i) , and the action on W is σW := σW (1) , • • • , σW (L) . We now elaborate on the definition of the group and action in several common cases. Example 2.1 (Multilayer perceptron). A multilayer perceptron (MLP) with L + 1 layers has activations h ℓ+1 = s W (ℓ) h ℓ + b (ℓ+1) , with h 1 being the first (input) layer and h L+1 the output. If each h ℓ is a vector of length n ℓ , then the weights are matrices W (ℓ) ∈ M (n ℓ+1 , n ℓ ) and the biases are vectors b (ℓ) ∈ M (n ℓ ). Then we have a neuron permutation group S = S n1 × • • • × S n L+1 , and σ ∈ S can be written σ = (σ ℓ ) L+1 ℓ=1 . The action on the weights and biases is:", "section": "Preliminaries", "sec_num": "2"}, {"text": "σW (ℓ) ⃗ i := W (ℓ) σ -1 ( ⃗", "section": "Preliminaries", "sec_num": "2"}, {"text": "EQUATION", "section": "Preliminaries", "sec_num": "2"}, {"text": "where P (σ ℓ ) is the n ℓ × n ℓ permutation matrix corresponding to σ ℓ . This corresponds exactly to the \"NP\" setting in <PERSON> et al. [2023a] .", "section": "Preliminaries", "sec_num": "2"}, {"text": "Example 2.2 (Recurrent neural network). Consider a deep recurrent neural network (RNN) [<PERSON><PERSON>, 1990] without biases. We follow the presentation of <PERSON> et al. [2023] :", "section": "Preliminaries", "sec_num": "2"}, {"text": "EQUATION", "section": "Preliminaries", "sec_num": "2"}, {"text": "where h 1 t are the inputs and h L+1 t are the outputs at each timestep, with h ℓ 0 initialized to 0. The weight space consists of feedforward (ff) weights W ℓ ff ∈ M (n ℓ+1 , n ℓ ) and recurrent (rec) weights W ℓ rec ∈ M (n ℓ , n ℓ ). We again define the neuron permutation group S := S n1 × • • • × S n L+1 , but the action of the group on the weight space is now different. Here, re-arranging the neurons corresponds to transforming the weights:", "section": "Preliminaries", "sec_num": "2"}, {"text": "W ℓ ff → P (σ ℓ+1 ) W ℓ ff P (σ ℓ ) ⊤ and W ℓ rec → P (σ ℓ ) W ℓ rec P (σ ℓ ) ⊤ .", "section": "Preliminaries", "sec_num": "2"}, {"text": "As illustrated by Figure 1 , the feedforward weights transform just as in the MLP case (Eq. 2), but the recurrent weights' rows and columns must be transformed by the same permutation.", "section": "Preliminaries", "sec_num": "2"}, {"text": "Example 2.3 (Convolutional neural network). Consider a 1D convolutional neural network (CNN) without biases. Using ⋆ to denote cross-correlation, we have activations h ℓ+1 = s W (ℓ) ⋆ h ℓ , where the input is h 1 and the output is h L+1 . If each filter has spatial dimension k ℓ and each h ℓ has n ℓ channels, then we have rank-3 weight tensors W (ℓ) ∈ M (n ℓ+1 , n ℓ , k ℓ ) and neuron permutation group S = L ℓ=1 S n ℓ × S k ℓ . Looking at how each dimension of W (ℓ) permutes, we would have σ n ℓ+1 ∈ S n ℓ+1 permute the first dimension (output channels), σ n ℓ ∈ S n ℓ permute the second dimension (input channels), and σ k ℓ ∈ S k ℓ permute the third dimension (spatial).", "section": "Preliminaries", "sec_num": "2"}, {"text": "We note that permutating the spatial dimensions of a convolution filter would change the CNN's behavior and is not a true symmetry of the weight space. This is a notable difference between how our framework handles convolutional weight spaces compared to NFNs [<PERSON> et al., 2023a] , where the action of the neuron permutation group does not affect the spatial dimensions at all. Assuming that all dimensions of each weight tensor can permute simplifies the development of our framework, and undesired symmetry can be broken (if desired) by positional encodings of the input [<PERSON> et al., 2023a , <PERSON> et al., 2023] .", "section": "Preliminaries", "sec_num": "2"}, {"text": "Equivariance and invariance. We are interested in functions T : W → W that are equivariant, meaning that it doesn't matter whether we apply a neuron permutation to the input or the output. We define L S (W, W) as the space of equivariant linear maps, i.e., those T satisfying:", "section": "Preliminaries", "sec_num": "2"}, {"text": "EQUATION", "section": "Preliminaries", "sec_num": "2"}, {"text": ")", "section": "Preliminaries", "sec_num": "2"}, {"text": "Our goal is to design a layer (i.e., a parameterized space of functions) that is equivalent to L S (W, W).", "section": "Preliminaries", "sec_num": "2"}, {"text": "In some applications, we may instead desire invariance, that is a function P satisfying", "section": "Preliminaries", "sec_num": "2"}, {"text": "EQUATION", "section": "Preliminaries", "sec_num": "2"}, {"text": "Following prior work [<PERSON><PERSON> et al., 2023 , <PERSON> et al., 2023a] , we can build invariant neural functionals by composing several equivariant layers with an invariant pooling layer, e.g., one that sums over every dimension of each weight tensor and concatenates the results.", "section": "Preliminaries", "sec_num": "2"}, {"text": "Since equivariance is preserved under composition, and pointwise non-linearities are already permutation equivariant, we can build deep equivariant models as long as we have an equivariant linear layer. Additionally, composing equivariant layers with an invariant pooling operation produces a deep invariant model. This section introduces a method for producing equivariant weight-space layers for any given weight space, which enables the flexible construction of universal neural functionals.", "section": "Universal neural functionals", "sec_num": "3"}, {"text": "The weight space is a direct sum of individual weight subspaces L) , so the problem of defining an equivariant layer on W can be decomposed into defining equivariant layers between each pair of weight subspaces W (m) and W (ℓ) , for all ℓ and m [<PERSON><PERSON> et al., 2023] .", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "W = W (1) ⊕ • • • ⊕ W (", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "We re-state this result in our own notation. For any ℓ, m pair we define L S W (m) , W (ℓ) as the space of equivariant maps between the two weight subspaces. It contains all T ℓm : W (m) → W (ℓ) satisfying", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "EQUATION", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "noting that the action on the left and right hand sides of the equivariance condition are not, in general, the same.", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "Assume that we already have a basis B sp for L S W (p) , W (s) . A basis function E ∈ B sp can be extended to Ē : W → W by defining:", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "EQUATION", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "where", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "Ē(W ) := Ē1 (W ), • • • , ĒL (W ) .", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "Theorem 3.1 (<PERSON><PERSON> et al. [2023] ). Let { B ℓm } be bases for each L S W (m) , W (ℓ) . Then the union of these bases (extended by Eq. 7) is a basis for linear equivariant maps on W. That is, we have the basis B for L S (W, W) defined:", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "EQUATION", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": ")", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "This result tells us that we can construct an equivariant basis B for L S (W, W) by simply combining the equivariant bases { B ℓm } for each pair of weight subspaces.", "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "Algorithm 1 Basis for equivariant W (m) → W (ℓ) layer ", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Require: W (m) , W (ℓ) 1: Initialize basis B ℓm ← { } 2: I ← { o 1 , • • • , o D ℓ , i 1 , • • • , i", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "E P (X) c[o1],••• ,c o[D ℓ ] := R X c[i1],••• ,c[i Dm ]", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "9:", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "B ℓm ← B ℓm ∪ { E P } 10: end for 11: return B ℓm Since weights are tensors, our decomposed problem involves finding bases for permutation equivariant maps between tensors. Variants of this problem have been studied by numerous prior works-in particular, <PERSON><PERSON> et al. [2018] theoretically characterize a basis for equivariant maps between arbitrary-rank tensors, and provide a concrete implementation of the basis functions in the rank-2 case. Here, we describe a general algorithm that automatically constructs a basis for permutation equivariant maps between arbitrary-rank tensors. Concretely, it implements each basis function in terms of simple array operations that are amenable to efficient computation with modern deep learning frameworks.", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Functions in L S W (m) , W (ℓ) take input tensors indexed by { i 1 , • • • , i Dm } and produces output tensors indexed by { o 1 , • • • , o D ℓ }. We can construct a basis B ℓm for this space where each element is identified by a valid partition P of these indices. Recall that the indices", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "(i 1 , i 2 , • • • ) of W (m) are permuted by σ d m 1 , σ d m 2 , • • • .", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "We say that two indices i 1 and i 2 \"permute simultaneously\" if d m 1 = d m 2 . Definition 1. A valid partition is a partition P of the output and input indices", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "I = { o 1 , • • • , o D ℓ , i 1 , • • • , i Dm } into", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "non-empty subsets, such that each subset only contains indices that are permuted simultaneously.", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Example 3.1 (W (m) = W (ℓ) = R n1×n2 ).", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Here the output and input indices are", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "{ o 1 , o 2 , i 1 , i 2 }. The partition { { o 1 , o 2 } , { i 1 , i 2 } } is not valid because o 1 ,", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "o 2 are permuted by σ 1 , σ 2 , so they do not permute simultaneously. On the other hand,", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "{ { o 1 , i 1 } , { o 2 , i 2 } } is a valid partition. Example 3.2 (W (m) = W (ℓ) = R n1×n1 ). This time, the partition { { o 1 , o 2 } , { i 1 , i 2 } } is valid because o 1 , o 2 are both permuted by σ 1 , as are i 1 , i 2 .", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "To construct the equivariant basis, we enumerate all valid partitions and then map each partition P to a basis function E P . Concretely, we label each subset of P with a distinct character α, β, γ, • • • and then remap each of our original indices", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "{ o 1 , • • • , o D ℓ , i 1 , • • • , i Dm } to", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "a a character based on which subset the index was in. This mapping is best illustrated by continuing our previous example.", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Example 3.3 (W (m) = W (ℓ) = R n1×n2 ).", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Here input and output are both matrices, with combined indices { o 1 , o 2 , i 1 , i 2 }. We have two permutations (σ 1 , σ 2 ) ∈ S n1 × S n2 that can act on the rows and columns of the input and output matrices. There are four valid partitions:", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "EQUATION", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Consider P 2 -we assign a character to each subset:", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "EQUATION", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": ")", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "which tells us to remap the output indices (o 1 , o 2 ) → (α, β) and the input indices", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "(i 1 , i 2 ) → (α, γ), producing the basis function E P2 W (m) αβ := γ W (m)", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "αγ , where summation over γ can be inferred because it only contains an input index.", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Repeating this index-remapping process for each valid partition will generate a total of four basis functions E P1 , • • • , E P4 for L S W (m) , W (ℓ) . Our equivariant W (m) → W (ℓ) layer will be defined as the linear combination m) , which is the layer introduced in Hartford et al. [2018] .", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "T ℓm W (m) ; λ := 4 k=1 λ k • E P k W (", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "To generalize the previous example, for each valid partition of the indices P we label its subsets with characters α, β, γ, • • • and then construct a basis function:", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "EQUATION", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "where c[•] maps each index to the subset of P that contains it. We sum over the characters in R, which is the (possibly empty) subset of characters that only contain input indices (i.e., only appear on the right-hand side). Entries that are not explicitly assigned by the left-hand side are 0. Algorithm 1 gives a formal description of the complete process for generating B ℓm .", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Theorem 3.2. Algorithm 1 produces a basis for the equivariant linear maps from W (m) to W (ℓ) .", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Proof. See Appendix B.1.", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Once Algorithm 1 has generated a basis of equivariant functions B ℓm , we can implement an equivariant layer using a vector λ ℓm ∈ R |B ℓm | of learned coefficients:", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "EQUATION", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": ")", "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Theorem 3.1 now tells us that we may now construct the equivariant weight-space layer by combining the bases { B ℓm } into a basis B of functions on W. The weight-space layer T (•, λ) can then be defined by a linear combination of the basis functions with learned coefficients λ. Explicitly, the full layer is defined:", "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "EQUATION", "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "where m) , λ ℓm . Appendix A provides a concrete description of how we specify the weight space in code and how the algorithm is then used to automatically construct an equivariant weight space layer. Our open-source implementation is compatible with most JAX [<PERSON> et al., 2018] neural network libraries. Theorem 3.3. The weight-space layer (Eq.-13) is S-equivariant, and can express any linear equivariant function on W.", "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "λ ℓ,: = { λ ℓm | ℓ = 1, • • • , L } and T ℓ W, λ ℓ,: = L m=1 T ℓm W (", "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "Proof. Each T ℓm is a linear combination of basis functions in B ℓm . Then, as described by Thm 3.1, Eq. 13 is a linear combination of functions that form a basis for L S (W, W).", "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "For an MLP weight space with neuron permutation group defined as in Example 2.1, this approach will generate the exact same layer as NFN NP [<PERSON> et al., 2023a] . This is because the layers each parameterize all possible linear maps equivariant to the same symmetry group, and hence can express the same set of functions.", "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "In practice, we may be interested in simultaneously processing multiple weight-space features, such as the weights and a history of gradients. These features can be stacked into a \"channel\" dimension analogous to the channels of convolutional networks. In that case, we must consider direct sums of weight spaces of the form W c = ⊕ c k=1 W, with elements that can be written as Extending equivariant layers to the multi-channel setting is quite common in the geometric deep learning literature and simply involves taking linear combinations along the channel dimension [<PERSON> and <PERSON>ing, 2016, <PERSON><PERSON> et al., 2017] . That is, we modify the equivariant layer between subspaces as:", "section": "Multiple feature channels", "sec_num": "3.4"}, {"text": "1 W = (W [1], • • • , W [c]), for W [k] ∈ W. Then the action is σW := (σW [1], • • • , σW [c]) for σ ∈ S,", "section": "Multiple feature channels", "sec_num": "3.4"}, {"text": "EQUATION", "section": "Multiple feature channels", "sec_num": "3.4"}, {"text": "where each λ ℓm b is now a learned c o × c i matrix instead of a scalar.", "section": "Multiple feature channels", "sec_num": "3.4"}, {"text": "The previous sections describes the construction of S-equivariant layers that operate operate on weight-space features in W c . We construct universal neural functionals by stacking multiple such layers (interleaved with pointwise non-linearities) into a deep, permutation equivariant model that can process weights. To construct a permutation invariant model, we can add an invariant pooling layer after the equivariant layers, as in prior work [<PERSON><PERSON> et al., 2023 , <PERSON> et al., 2023a] .", "section": "Deep models", "sec_num": "3.5"}, {"text": "In this section, we refer to weight-space models constructed using our algorithm as universal neural functionals (UNFs). We compare their performance to prior methods on two types of weight-space tasks: predicting the generalization of recurrent sequence-to-sequence models, and training learned optimizers for a variety of architectures and datasets.", "section": "Experiments", "sec_num": "4"}, {"text": "One promising application of neural functionals is in predicting the generalization of neural network models from their weights <PERSON><PERSON><PERSON><PERSON> et al. [2020] . We construct Tiny RNN Zoo2 , a dataset of recurrent neural networks trained to do arithmetic by completing given questions character-by-character. For example, given the input string \"15+20=\" the correct completion would be \"35<EOS>\". To construct the dataset, we train 10 4 sequence-to-sequence [<PERSON><PERSON> et al., 2014] models on example problems with input numbers up to five input digits. Both encoder and decoder RNNs contain a single GRU cell [<PERSON> et al., 2014] The success rate of each RNN model is clearly invariant under permutation symmetries of its weights, so invariance is a natural inductive bias for any generalization predictor. We evaluate STATNN [<PERSON><PERSON><PERSON><PERSON> et al., 2020 ] and a UNF-based predictor (note that NFNs are not applicable to the weights of recurrent networks). STATNN is operates on basic statistical features3 of the weights, and has been shown to be a very strong baseline on previous generalization prediction tasks [<PERSON><PERSON><PERSON><PERSON> et al., 2020] . On the other hand, UNF operates on raw weight inputs and may be able to extract more nuanced signals than STATNN, as was shown (for CNN classifiers) in <PERSON> et al. [2023a] .", "section": "RNN generalization prediction", "sec_num": "4.1"}, {"text": "In particular, STATNN computes the mean, variance, and (0, 25, 50, 75, 100)-percentiles of each weight tensor in the RNN and feeds them into a six-layer MLP with hidden width 600. UNF is a permutation invariant model, implemented using a three-layer equivariant backbone (16 hidden channels) followed by invariant pooling and a three-layer MLP (512 hidden neurons). We train each predictor with binary cross entropy loss (since the target SR is in [0, 1]), using the Adam optimizer with learning rate 0.001, batch size 10, and training for up to 10 epochs. We use the validation data only for early stopping, and assess the performance of each predictor on the test inputs using <PERSON>'s τ , the rank correlation between predicted and actual success rate.", "section": "RNN generalization prediction", "sec_num": "4.1"}, {"text": "Results. Table 1 shows the performance of each predictor on held out weight inputs. Our UNF-based predictor achieves significantly higher rank correlation between predicted and actual success rate, suggesting that the equivariant layers are able to extract more informative features from the raw weights compared to STATNN.", "section": "RNN generalization prediction", "sec_num": "4.1"}, {"text": "Choosing the optimizer is a key step in training any modern neural network. Though most popular optimizers are variants of stochastic descent, the non-convexity of neural network training leaves few rigorous guidelines for ideal optimizer design. This has led some researchers to propose training good optimizers using some form of meta-learning [<PERSON><PERSON> et al., 1990 , 2013 , <PERSON><PERSON><PERSON><PERSON> et al., 2016 , <PERSON><PERSON><PERSON><PERSON> et al., 2017 , <PERSON> et al., 2019] .", "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "Common optimizers today (including the learned ones) are equivariant to any permutation of the weights. This is because permuting the weights also permutes the gradients, so stochastic gradient descent and similar optimizers will produce permuted updates. However, equivariance to any permutation ignores the actual symmetry structure of the optimized neural network. Arguably the more appropriate constraint is to only require equivariance to the neuron permutation group, which enables more expressive optimizers while still respecting the symmetries of the weight space. As we will see, this can be achieved by using UNFs to implement a learned optimizer.", "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "Training learned optimizers that generalize well is extremely compute-intensive [<PERSON> et al., 2022] , so we conduct our experiments in several smaller settings to analyze the impact of architecture Figure 2 : Training loss (negative log-likelihood) curves for different tasks and architectures using meta-learned optimizers. We implement learned optimizers with either universal neural functionals (UNFs), NFNs [<PERSON> et al., 2023a] , or Deep Sets [<PERSON><PERSON><PERSON> et al., 2017] . Deep Sets are the current standard choice for implementing learned optimizers. Note that NFN is identical to UNF in the MLP case, different for CNN case, and not applicable to RNNs or Transformers. All loss curves are smoothed and averaged over 5 random initializations (3 for Transformer), with shaded regions showing standard error. choice on learned optimizer performance. In each setting, an optimizer is meta-trained to optimize an architecture type on a task from random initializations. Following <PERSON> et al. [2022] , our learned optimizers track momentum terms m γ t ← γm t-1 + ∇ t and produce updates of the form:", "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "EQUATION", "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "Here αm γ0 t is a \"nominal term\" that biases the learned optimizer to behave like stochastic gradient descent with momentum coefficient γ 0 . The neural functional f (•) ingests weights W t , gradients ∇ t , momentum terms at several coefficients { m γi t } i , and the iteration t. During meta-training, we optimize network f and scalars α, β, γ 0 to minimize the task training loss after a fixed number of training steps T , the \"inner training horizion.\" To avoid the issue of backpropagating through an optimization process, we estimate meta-gradients using persistent evolutionary strategies [<PERSON><PERSON> et al., 2021] .", "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "Comparisons. The default architecture choice for f (•) in prior work is Deep Sets [<PERSON><PERSON><PERSON> et al., 2017] , which offers equivariance to any permutation symmetry. We study the effect of replacing Deep Sets by UNFs. We also try the NFN NP architecture [<PERSON> et al., 2023a] where applicable, though it cannot be used on the RNN and Transformer experiments. Finally, we consider stochastic gradient descent with momentum (SGDM), which is equivalent to fixing β = 0 in Eq. 15. The SGDM baseline is also meta-trained to tune the learning rate α and momentum decay rate γ 0 . We compare the different learned optimizers in four tasks: MLP on FashionMNIST. Each optimizer trains an MLP classifier on a downsized and flattened version of the FashionMNIST dataset [<PERSON> et al., 2017] . We note that for MLP weight spaces, UNF are identical to NFN NP [<PERSON> et al., 2023a] .", "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "CNN on CIFAR-10. Each optimizer trains a convolutional classifier on a downsized 16 × 16 CIFAR-10. In this setting our algorithm produces a UNF that is different to NFN NP (see Example 2.3).", "section": "Learned optimizers", "sec_num": "4.2"}, {"text": ". Each optimizer trains a character-level RNN-based language model (LM) on the One Billion Word Language Model Benchmark (LM1B) dataset [<PERSON><PERSON><PERSON> et al., 2013] .", "section": "RNN on LM1B", "sec_num": null}, {"text": "Transformer on LM1B. Each optimizer trains a Transformer LM on LM1B, this time predicting tokens instead of characters.", "section": "RNN on LM1B", "sec_num": null}, {"text": "We use an inner training horizon T = 2,000 for the first three tasks and T = 5,000 for the Transformer task, since it takes longer to train. When implementing f (•) for each method, we use a network with four layers, 32 hidden channels, and ReLU nonlinearities. The Deep Set optimizer uses exclusively Deep Set layers [<PERSON><PERSON><PERSON> et al., 2017, Eq. 4] , while the UNF and NFN optimizers uses three Deep Set layers followed by a single UNF or NFN layer. See Appendix C.1-C.2 for full descriptions of the tasks and meta-training.", "section": "RNN on LM1B", "sec_num": null}, {"text": "Results. Figure 2 shows the training curves produced by each of the meta-trained optimizers in each experiment. Learned optimizers with deep architectures (UNF, Deep Set, or NFN) outperform SGDM, even after tuning SGDM's learning rate and momentum decay. UNF typically learns fastest and achieves the lowest training loss across all methods, though Deep Set and NFN can be comparable in some settings. One interesting observation is that UNF outperforms NFN in the CNN experiment. As noted in Example 2.3, UNFs make the stronger assumption that all tensor dimensions-including the spatial dimensions of the convolution filter-are permutable, while NFNs do not. Although the UNF assumption is technically incorrect, the stronger assumption leads to a lower parameter count (see Table 3 in the appendix) which may be easier for meta-optimization.", "section": "RNN on LM1B", "sec_num": null}, {"text": "Overall, our results show the promise of using UNFs to create more expressive learned optimizers that utilize the specific symmetry structure of the weight spaces they optimize. Further work could investigate their capacity for generalization to new tasks and architectures, for example by metatraining on diverse tasks [Metz et al., 2022] . Moreover, as Table 3 in the appendix shows, a necessary trade-off of UNFs being more expressive is that they require more parameters for an equivalent number of layers and hidden channels. Since learned optimizers are still much smaller than the networks they could optimize, this may not be a significant computational constraint in practice. Still, it could be a challenge to meta-optimization, since evolutionary strategies are known to struggle in higher dimensions. Hence, further work on efficient high-dimensional meta-gradient estimators would complement the development of expressive weight-space models like UNF.", "section": "RNN on LM1B", "sec_num": null}, {"text": "There is a long history of neural network architectures that are equivariant to various symmetry groups [<PERSON><PERSON><PERSON> et al., 1995 , <PERSON> and <PERSON>, 2016 , <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2017 , Kondor and <PERSON><PERSON><PERSON>, 2018 , <PERSON> et al., 2018] . Existing frameworks for automatically constructing equivariant models [<PERSON><PERSON> et al., 2021] produce equivariant matrices, which would be intractable for our task. Our work constructs efficient equivariant basis functions for a particular class of permutation symmetries that arise in the weight spaces of neural networks. Permutation equivariant networks have been developed for sets [<PERSON><PERSON><PERSON> et al., 2017] , matrices whose rows and columns permute independently [<PERSON> et al., 2018] , and tensors under higher-order permutation actions [<PERSON><PERSON><PERSON> et al., 2020, Pan and Kondor, 2022] -the latter may also be viewed as equivariant models on graphs or polytopes [<PERSON><PERSON> et al., 2018 , <PERSON><PERSON> et al., 2019] . This work observes that a weight space is a collection of tensors under higher-order permutation symmetries, and develops equivariant models for that setting.", "section": "Related Work", "sec_num": "5"}, {"text": "There has been significant interest in designing architectures that that either optimize or generate neural network weights [<PERSON><PERSON><PERSON><PERSON><PERSON>, 1993 , <PERSON> et al., 2016 , <PERSON><PERSON><PERSON> et al., 2017 , <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, 2021 , <PERSON><PERSON><PERSON> et al., 2022 , <PERSON> et al., 2022] . Some works have identified the importance of respecting the relevant symmetries when implementing black box meta-learners [<PERSON><PERSON> et al., 2022] . However, precise characterizations of equivariant models on neural weight spaces are relatively recent and were initially restricted to simple feedforward models [<PERSON><PERSON> et al., 2023 , <PERSON> et al., 2023a,b] .", "section": "Related Work", "sec_num": "5"}, {"text": "A recent alternative approach has been to leverage message passing neural networks (MPNNs) [<PERSON> et al., 2023] to process weights as edges of a graph. Concurrent to this work, <PERSON><PERSON><PERSON> et al. [2024] demonstrated applications of MPNNs to learned optimization for MLPs and CNNs and <PERSON> et al. [2023] extended MPNNs to process general weight-spaces. MPNN-based approaches benefit from more flexible adaptation to heterogenous inputs, and the computational cost of message passing does not grow as rapidly as our basis-this is because our approach guarantees each linear layer to be maximally expressive while MPNNs do not. We give a more detailed exposition of this trade-off in Appendix B.3", "section": "Related Work", "sec_num": "5"}, {"text": "We introduce a method for constructing permutation-equivariant neural functionals that operate on arbitrary weight spaces, removing a major limitation of previous frameworks that were only applicable to the weight spaces of simple MLPs and CNNs. Our algorithm constructs maximally expressive equivariant linear layers for processing any collection of tensors given a description of their permutation symmetries, and implements these layers in terms of efficient array operations in standard deep learning frameworks. We empirically validate that the resulting universal neural functionals (UNFs) are effective at tasks that involve processing the weights and gradients of convolutional image classifiers, recurrent sequence-to-sequence models, and Transformer language models. In particular, we find that UNFs show promising improvements over existing learned optimizer designs in small scale experiments.", "section": "Conclusion", "sec_num": "6"}, {"text": "Limitations and future work. It remains to be demonstrated how UNFs can be applied to heterogenous weight-space inputs, e.g., to have a single UNF act as a learned optimizer for any input architecture. Moreover, our experimental results only validate the promise of UNF-based learned optimizers in relatively limited settings, and more work would needed to test generalization across arbitrary tasks. Finally, computational tractability may be a significant challenge for more complex architectures as the number of basis terms generated by Alg. 1 would grow rapidly for higher rank tensors with higher-order interactions (see Appendix B.2). Resolving these challenges would further improve the scalability and applicability of neural functionals to weight-space tasks.", "section": "Conclusion", "sec_num": "6"}, {"text": "Here we discuss the concrete specification that precisely describes a weight space and must be provided as input to the algorithm before it can construct equivariant weight-space layers. Our implementation is compatible with most JAX [<PERSON> et al., 2018] neural network libraries.", "section": "A Weight-space specifications", "sec_num": null}, {"text": "Suppose we wish to process an MLP's weights that are stored in a (nested) Python dictionary: params = { \"layer1\": {\"weight\": Array [64, 32] , \"bias\": <PERSON>rray[64]}, \"layer2\": {\"weight\": Array [64, 64] , \"bias\": <PERSON>rray[64]}, } Then a specification should match the nested dictionary structure but provide a string or integer name for each dimension of each array. The name tells the algorithm which permutation affects which dimensions of each array.", "section": "A Weight-space specifications", "sec_num": null}, {"text": "In this example, the specification closely follows the MLP description in Example 2.1, where W (1) ∈ M (n 2 , n 1 ) is permuted as W (1) → P (σ 2 ) W (1) P (σ 1 )", "section": "A Weight-space specifications", "sec_num": null}, {"text": "⊤ . specification = { \"layer1\": {\"weight\": (\"n2\", \"n1\"), \"bias\": (\"n2\",)}, \"layer2\": {\"weight\": (\"n3\", \"n2\"), \"bias\": (\"n3\",)}, } Providing this specification object to our algorithm is sufficient for it to deduce the symmetry group, its action, and construct the corresponding equivariant layer.", "section": "A Weight-space specifications", "sec_num": null}, {"text": "Since most neural networks consist of repeating layers or blocks, the process of constructing the specification can be semi-automated by first defining a function that creates the specification for a single layer or block and then re-using that function for each block. Although we did not find this necessary for our experiments, it may also be possible to automatically deduce the specifications for a network in common deep learning frameworks by analyzing its computation graph.", "section": "A Weight-space specifications", "sec_num": null}, {"text": "B.1 Algorithm 1 generates a basis for L S W (m) , W (ℓ) Here we show that Algorithm 1 produces a basis B ℓm for L S W (m) , W (ℓ) , the space of linear equivariant maps between W (m) and W (ℓ) . Consider instantiating these linear maps as matrices multiplying flattened input vec W (m) . <PERSON><PERSON> et al. [2018] characterize a basis { B µ } µ for these matrices, where the entries of each basis matrix are defined:", "section": "B Further analysis of UNFs", "sec_num": null}, {"text": "EQUATION", "section": "B Further analysis of UNFs", "sec_num": null}, {"text": ")", "section": "B Further analysis of UNFs", "sec_num": null}, {"text": "Here a ∈ I m and b ∈ I ℓ are multi-indexes for the input and output spaces, and µ ∈ I m × I ℓ / ∼ is an equivalence class of the combined input-output index space I m × I ℓ under the equivalence relation ∼ defined by a ∼ a ′ if and only if a i = a j ⇐⇒ a ′ i = a ′ j for all i, j, i.e. the two multi-indexes a, a ′ have the same equality pattern. <PERSON><PERSON> et al. [2018, Eq. 10b] , any equivariant linear map is defined:", "section": "B Further analysis of UNFs", "sec_num": null}, {"text": "EQUATION", "section": "Re-arranging", "sec_num": null}, {"text": "where I{•} is an indicator function for the given condition.", "section": "Re-arranging", "sec_num": null}, {"text": "Notice that each equivalence class µ is represented by what we call a valid partition of", "section": "Re-arranging", "sec_num": null}, {"text": "[D m + D ℓ ] := {1, • • • , D m + D ℓ },", "section": "Re-arranging", "sec_num": null}, {"text": "so this is already a sum over valid partitions as in Eq. 12. We can now observe that each term on the RHS is equivalent to one of our basis functions (Alg 1 Line 8). That is, for a given equivalence class µ represented by valid partition P:", "section": "Re-arranging", "sec_num": null}, {"text": "EQUATION", "section": "Re-arranging", "sec_num": null}, {"text": ")", "section": "Re-arranging", "sec_num": null}, {"text": "This is because for any I := (a, b) yielding a nonzero term on the LHS, if i, j ∈ [D m + D ℓ ] are grouped together by partition P then I i = I j , otherwise they would violate the equality pattern of µ. Therefore, we can replace all indices grouped together in a partition with a single shared symbol, i.e. the characters in Eq. 11.", "section": "Re-arranging", "sec_num": null}, {"text": "Hence, Algorithm 1 produces a basis that spans the same space of equivariant functions defined in <PERSON><PERSON> et al. [2018] , but constructs the basis functions in terms of efficient array operations instead of as matrices. Note that this is similar to the construction in Pan and Kondor [2022] , but generalized to multi-node sets (non-square tensors whose axes can potentially permute independently).", "section": "Re-arranging", "sec_num": null}, {"text": "Suppose we have a neuron permutation symmetry group ℓ) to be the number of indices that σ i ∈ S ni permutes in weight tensors W (ℓ) ∈ W (ℓ) (which could be 0). Finally, denote b(k) to be the k'th Bell number. Then the number of basis functions generated by Algorithm 1 is:", "section": "B.2 Size of basis produced by Algorithm 1", "sec_num": null}, {"text": "S = S n1 × • • • × S n N , i.e., every neuron permutation σ is composed of N distinct permutations (σ 1 , • • • , σ N ). For each i = 1, • • • , N we define c i W (", "section": "B.2 Size of basis produced by Algorithm 1", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Size of basis produced by Algorithm 1", "sec_num": null}, {"text": "Each UNF layer can express any linear equivariant function on a given weight space (Thm 3.3).", "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "Compared to methods based on message-passing neural networks (MPNNs), this means UNFs can have very expressive individual layers, but may also be more computationally challenging due to the growth in the size of the basis (see next section).", "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "As an example, consider a simple \"RNN\" where h t+1 = W h t and h t ∈ R n has exchangeable entries, meaning that W → P W P T is a symmetry. Algorithm 1 would produce an equivariant basis with b(2 + 2) = 15 terms4 .", "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "On the other hand, we could construct a parameter graph [<PERSON> et al., 2023] with n nodes and 2n 2 directed edges between them (allowing a forward and backward edge for each weight, equivalently n 2 undirected edges). Then using a similar construction to <PERSON> et al. [2023, Appendix C.1.2] , we would get a linear GNN that computes:", "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "EQUATION", "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "which is a linear combination of 6 equivariant basis functions, instead of 15. This leads to a potientially interesting trade-off between expressivity vs tractability. However, we also note that in practice MPNNs use non-linear MLPs in their message passing updates, and the comparison between UNF and MPNN-style approaches remains an open empirical question.", "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "Here we describe each of the experimental settings we evaluated the learned optimizers on. Across all experiments, the training loss is negative log-likelihood. [<PERSON> et al., 2017] . The MLP has a hidden size of 32 and ReLU activation function. We use a batch size of 128.", "section": "C Experimental details C.1 Learned optimization tasks", "sec_num": null}, {"text": "CNN on CIFAR-10. Train a convolutional classifier on a downsized 16 × 16 CIFAR-10. The classifier has two convolutional layers (16 and 32 channels), followed by global average pooling and a linear classification head, and is trained with a batch size of 128.", "section": "C Experimental details C.1 Learned optimization tasks", "sec_num": null}, {"text": "Trains a character-level RNN-based language model (LM) on LM1B [<PERSON><PERSON><PERSON> et al., 2013] . The RNN itself has one hidden layer with size 64, and uses identity-initialization [<PERSON> et al., 2015 ]. An embedding layer with dimension 32 maps tokens to embeddings before feeding into the RNN, and an output layer produces token predictions from the RNN output. The LM is trained to predict the next token with teacher forcing at batch size 64, on sequences of length 16.", "section": "RNN on LM1B.", "sec_num": null}, {"text": "Transformer on LM1B. Train a Transformer LM on LM1B, this time predicting tokens instead of characters. The Transformer has two blocks with an embedding dimension of 32, and uses four self-attention heads. We train with a batch size of 8 on length-8 sequences.", "section": "RNN on LM1B.", "sec_num": null}, {"text": "Call DS[c] a single equivariant Deep Set layer [<PERSON><PERSON><PERSON> et al., 2017, Eq 4 ] with c output channels (similarly for UNF[c] and NFN[c]). Then f (•) in our learned optimizers (Eq. 15) is always implemented as a feedforward architecture:", "section": "C.2 Learned optimization meta-training", "sec_num": null}, {"text": "DeepSetOpt = DS[32] -> ReLU -> DS[32] -> ReLU -> DS[32] -> ReLU -> DS[1] UNFOpt = DS[32] -> ReLU -> DS[32] -> ReLU -> DS[32] -> ReLU -> UNF[1] NFNOpt = DS[32] -> ReLU -> DS[32] -> ReLU -> DS[32] -> ReLU -> NFN[1]", "section": "C.2 Learned optimization meta-training", "sec_num": null}, {"text": "For all methods, we initialize α = 0.1 and γ 0 = 0.9 before starting meta-training. For non-SGDM methods, we initialize β = 0.001, and provide six momentum values { m γi t } i with coefficients γ i = 0.1, 0.5, 0.9, 0.99, 0.999, 0.9999. The iteration number t is converted into an 11-dimensional sinusoidal encoding, and all inputs to f (•) are concatenated along the channel dimension. Concretely, this results in an input in W 19 . The output is in W 1 .", "section": "C.2 Learned optimization meta-training", "sec_num": null}, {"text": "We meta-train for 50,000 steps using <PERSON>, estimating meta-gradients over 16 parallel training runs using persistent evolutionary strategies (PES) [<PERSON><PERSON> et al., 2021] with a truncation length of 50 and a noise standard deviation of 0.01. The meta-training objective is training loss at the end of the inner training horizon (T = 5,000 for the Transformer setting, and T = 2,000 otherwise), and we apply a gradient clipping of 1.0.", "section": "C.2 Learned optimization meta-training", "sec_num": null}, {"text": "Size of each learned optimizer f (•). Since Deep Set layers are agnostic to the specific weight space being optimized, the Deep Set learned optimizer uses the same number of parameters in each task. The same is not true of UNF layers, where the number of parameters grows in proportion to the size of the bases generated by Algorithm 1. Table 3 lists the number of parameters in f (•) for each learned optimizer.", "section": "C.2 Learned optimization meta-training", "sec_num": null}, {"text": "Experiments were run on a mix of TPU v3 and v4 accelerators. On a TPU v3-8, training a UNF for our RNN generalization prediction task takes < 3 hours. Also on a TPU v3-8, meta-training a UNF for one of our learned optimizers takes ∼ 4 hours for the MLP task, ∼ 7 hours for the CNN task, and ∼ 20 hours for the RNN task.", "section": "C.3 Compute", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?", "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Answer: [Yes] Justification: The abstract contains exactly the description of the algorithm we developed and experiments we ran.", "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Guidelines:", "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?", "section": "Limitations", "sec_num": "2."}, {"text": "Answer: [Yes] Justification: This is discussed at various points of the paper, including in the Conclusion (final section of the main paper).", "section": "Limitations", "sec_num": "2."}, {"text": "Guidelines:", "section": "Limitations", "sec_num": "2."}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "section": "Limitations", "sec_num": "2."}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "section": "Limitations", "sec_num": "2."}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? Answer: [No] Justification: We provide code for implementing the proposed algorithms, but data and code for some of the experiments could not be released due to proprietary restrictions. However, we do include details for how to implement these experiments in the paper. Guidelines:", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that paper does not include experiments requiring code. • If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction). 10. Broader Impacts Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed? Answer: [NA] Justification: There are no obvious relevant societal impacts. Guidelines:", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA] Justification: No relevant risks.", "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA] Justification: No third-party assets used.", "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided.", "section": "Safeguards", "sec_num": "11."}, {"text": "38th Conference on Neural Information Processing Systems (NeurIPS 2024).", "section": "", "sec_num": null}, {"text": "In the multichannel setting we overload notation and use W to refer to elements of W c , not W.", "section": "", "sec_num": null}, {"text": "Inspired by the Tiny CNN Zoo[<PERSON><PERSON><PERSON><PERSON> et al., 2020].", "section": "", "sec_num": null}, {"text": "Notably, it computes statistics that are invariant to permutations of the weights.", "section": "", "sec_num": null}, {"text": "In this case, the full basis is also given by<PERSON><PERSON><PERSON> et al. [2018, Appendix A].", "section": "", "sec_num": null}], "back_matter": [{"text": "We thank <PERSON><PERSON><PERSON> and <PERSON><PERSON> for insightful general discussions about the project, and <PERSON> for helpful feedback on early drafts. AZ is supported by the NSF Graduate Research Fellowship Program. We are grateful to the TPU Research Cloud (TRC) for providing compute for some of the experiments.", "section": "Acknowledgements", "sec_num": "7"}, {"text": "Answer: [Yes] Justification: We provide proofs for both Thm 3.2 and 3.3.", "section": "annex", "sec_num": null}, {"text": "• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "section": "Guidelines:", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?Answer: [Yes] Justification: We provide code to implement the proposed method as well as details in the paper.Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• If this information is not available online, the authors are encouraged to reach out to the asset's creators.", "section": "Open access to data and code", "sec_num": "5."}, {"text": "Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets? Answer: [NA] Justification: We do not release new assets. Guidelines:• The answer NA means that the paper does not release new assets.• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "section": "New Assets", "sec_num": "13."}], "ref_entries": {"FIGREF0": {"type_str": "figure", "text": "extending the (single channel) definition. The definition of equivariance can then be extended to layers of the form T (•) : W ci → W co , where c i , c o are the number of input and output channels.", "num": null, "fig_num": null, "uris": null}, "TABREF1": {"html": null, "type_str": "table", "text": "with hidden size 128. Each model is trained with a distinct learning rate and batch size, and it's test success rate (SR) is recorded. The learning rate is sampled from a log-uniform distribution over [10 -4 , 10 -2 ], and the batch size is sampled uniformly from { 64, 128, 256 }. With the goal of predicting test SR from weights, we split the Tiny RNN Zoo into 8000/1000/1000 training, validation, and test examples. Rank correlation between predicted and actual success rates of RNNs on an arithmetic task. Predicting with UNF significantly outperforms STATNN[<PERSON><PERSON><PERSON><PERSON> et al., 2020].", "num": null, "content": "<table><tr><td>Method</td><td>Test τ</td></tr><tr><td>Deep Set</td><td>0.8306 ± 0.0006</td></tr><tr><td>STATNN</td><td>0.8839 ± 0.0007</td></tr><tr><td colspan=\"2\">UNF (Ours) 0.8968 ± 0.0006</td></tr></table>"}, "TABREF2": {"html": null, "type_str": "table", "text": "Figure3: Number of parameters used by f (•) in each learned optimizer, for each task. Note that NFN and UNF are identical for the MLP task. This count does not include the other meta-learned scalars in Eq. 15, which are α, γ 0 , β.", "num": null, "content": "<table><tr><td>Task</td><td>UNF</td><td>Deep Set</td><td>NFN</td></tr><tr><td colspan=\"2\">MLP on FashionMNIST 3,783</td><td>2,788</td><td>3,783</td></tr><tr><td>CNN on CIFAR-10</td><td>7,369</td><td>2,788</td><td>41,603</td></tr><tr><td>RNN on LM1B</td><td>8,043</td><td>2,788</td><td>N/A</td></tr><tr><td>Transformer on LM1B</td><td>64,168</td><td>2,788</td><td>N/A</td></tr><tr><td colspan=\"4\">MLP on FashionMNIST. Train a three-layer MLP classifier on a downsized (8 × 8) and flattened</td></tr><tr><td>version of the FashionMNIST dataset</td><td/><td/><td/></tr></table>"}, "TABREF3": {"html": null, "type_str": "table", "text": "• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details.• While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable). • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted. Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. 8. Experiments Compute Resources Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper). 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines? Answer: [Yes] Justification: We have reviewed the Code of Ethics. Guidelines: • The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "num": null, "content": "<table><tr><td>6. Experimental Setting/Details Question: Does the paper specify all the training and test details (e.g., data splits, hyper-parameters, how they were chosen, type of optimizer, etc.) necessary to understand the results? Answer: [Yes] Justification: See Appendix C. Guidelines: • The answer NA means that the paper does not include experiments. • The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material. 7. Experiment Statistical Significance Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [Yes] Justification: Our results report error bars. Guidelines: • The answer NA means that the paper does not include experiments. Answer: [Yes] Justification: See Appendix C.3. Guidelines: • The authors should answer \"the experiments? •</td></tr></table>"}}}}