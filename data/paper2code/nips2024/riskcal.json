{"paper_id": "riskcal", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:14:31.214617Z"}, "title": "Attack-Aware Noise Calibration for Differential Privacy", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Carmel<PERSON>", "middle": [], "last": "Troncoso", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Differential privacy (DP) is a widely used approach for mitigating privacy risks when training machine learning models on sensitive data. DP mechanisms add noise during training to limit the risk of information leakage. The scale of the added noise is critical, as it determines the trade-off between privacy and utility. The standard practice is to select the noise scale to satisfy a given privacy budget ε. This privacy budget is in turn interpreted in terms of operational attack risks, such as accuracy, sensitivity, and specificity of inference attacks aimed to recover information about the training data records. We show that first calibrating the noise scale to a privacy budget ε, and then translating ε to attack risk leads to overly conservative risk assessments and unnecessarily low utility. Instead, we propose methods to directly calibrate the noise scale to a desired attack risk level, bypassing the step of choosing ε. For a given notion of attack risk, our approach significantly decreases noise scale, leading to increased utility at the same level of privacy. We empirically demonstrate that calibrating noise to attack sensitivity/specificity, rather than ε, when training privacy-preserving ML models substantially improves model accuracy for the same risk level. Our work provides a principled and practical way to improve the utility of privacy-preserving ML without compromising on privacy. * Contributed equally.\n38th Conference on Neural Information Processing Systems (NeurIPS 2024).\nStandard Calibration\nAttack Risk → Noise Scale\nFPR) σ Direct calibration of noise to attack risk increases utility compared to the standard calibration at the same level of risk: GPT-2 on SST-2 (text sentiment classification) 55 60 65 70 0.0 0.2 0.4 0.6 0.8 1.0 Attack risk (TPR, 1 -β) α = 0.01 55 60 65 70 Task accuracy α = 0.05 55 60 65 70 α = 0.1 Method Standard calibration Attack risk calibration CNN on CIFAR-10 (image classification) 66 68 70 0.0 0.2 0.4 0.6 0.8 1.0 Attack risk (TPR, 1 -β) 66 68 70 Task accuracy 66 68 70 Method Standard calibration Attack risk calibration", "pdf_parse": {"paper_id": "riskcal", "_pdf_hash": "", "abstract": [{"text": "Differential privacy (DP) is a widely used approach for mitigating privacy risks when training machine learning models on sensitive data. DP mechanisms add noise during training to limit the risk of information leakage. The scale of the added noise is critical, as it determines the trade-off between privacy and utility. The standard practice is to select the noise scale to satisfy a given privacy budget ε. This privacy budget is in turn interpreted in terms of operational attack risks, such as accuracy, sensitivity, and specificity of inference attacks aimed to recover information about the training data records. We show that first calibrating the noise scale to a privacy budget ε, and then translating ε to attack risk leads to overly conservative risk assessments and unnecessarily low utility. Instead, we propose methods to directly calibrate the noise scale to a desired attack risk level, bypassing the step of choosing ε. For a given notion of attack risk, our approach significantly decreases noise scale, leading to increased utility at the same level of privacy. We empirically demonstrate that calibrating noise to attack sensitivity/specificity, rather than ε, when training privacy-preserving ML models substantially improves model accuracy for the same risk level. Our work provides a principled and practical way to improve the utility of privacy-preserving ML without compromising on privacy. * Contributed equally.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "38th Conference on Neural Information Processing Systems (NeurIPS 2024).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "Standard Calibration", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "Attack Risk → Noise Scale", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "FPR) σ Direct calibration of noise to attack risk increases utility compared to the standard calibration at the same level of risk: GPT-2 on SST-2 (text sentiment classification) 55 60 65 70 0.0 0.2 0.4 0.6 0.8 1.0 Attack risk (TPR, 1 -β) α = 0.01 55 60 65 70 Task accuracy α = 0.05 55 60 65 70 α = 0.1 Method Standard calibration Attack risk calibration CNN on CIFAR-10 (image classification) 66 68 70 0.0 0.2 0.4 0.6 0.8 1.0 Attack risk (TPR, 1 -β) 66 68 70 Task accuracy 66 68 70 Method Standard calibration Attack risk calibration", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Machine learning and statistical models can leak information about individuals in their training data, which can be recovered by membership inference, attribute inference, and reconstruction attacks (<PERSON><PERSON><PERSON> et al., 2015; <PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2022) . The most common defenses against these attacks are based on differential privacy (DP) (<PERSON><PERSON> et al., 2014) . Differential privacy introduces noise to either the data, the training algorithm, or the model parameters (<PERSON><PERSON><PERSON><PERSON> et al., 2011) . This noise provably limits the adversary's ability to run successful attacks at the cost of reducing the utility of the model.", "cite_spans": [{"start": 199, "end": 224, "text": "(<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF17"}, {"start": 225, "end": 245, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF52"}, {"start": 246, "end": 264, "text": "<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF62"}, {"start": 265, "end": 284, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF4"}, {"start": 373, "end": 393, "text": "(<PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF14"}, {"start": 502, "end": 526, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2011)", "ref_id": "BIBREF7"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In DP, the parameters ε and δ control the privacy-utility trade-off. These parameters determine the scale (e.g., variance) of the noise added during training: Smaller values of these parameters correspond to larger noise. Larger noise provides stronger privacy guarantees but reduces the utility of the trained model. Typically, δ is set to a small fixed value (usually between 10 -8 and 10 -5 ), leaving ε as the primary tunable parameter. Without additional analyses, the values of parameters (ε, δ) alone do not provide a tangible and intuitive operational notion of privacy risk (<PERSON><PERSON><PERSON><PERSON> et al., 2023) . This begs the question: how should practitioners, regulators, and data subjects decide on acceptable values of ε and δ and calibrate the noise scale to achieve a desired level of protection? The DP noise is calibrated to guarantee at most a certain level of privacy attack sensitivity (y-axis) at three possible attack false-positive rates α ∈ {0.01, 0.05, 0.1}. See Section 4 for details.", "cite_spans": [{"start": 583, "end": 609, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "A standard way of assigning operational meaning to DP parameters is mapping them to attack risks. One common approach is computing attacker's posterior belief (or equivalently, accuracy or advantage) of membership inference attacks, that concrete values of (ε, δ) allow (<PERSON> et al., 2018 ). An alternative is to compute the trade-off between sensitivity and specificity of feasible membership inference attacks (<PERSON><PERSON><PERSON> and <PERSON>, 2010; <PERSON><PERSON><PERSON> et al., 2015; <PERSON> et al., 2022) , which was recently shown to also be directly related to success of record reconstruction attacks (<PERSON> et al., 2024; <PERSON><PERSON><PERSON> et al., 2023a) . Such approaches map (ε, δ) to a quantifiable level of risk for individuals whose data is present in the dataset. Studies have shown that such risk-based measures are the most useful way to interpret the guarantees afforded by DP for practitioners and data subjects (<PERSON> et al., 2021; <PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2023) .", "cite_spans": [{"start": 270, "end": 288, "text": "(<PERSON> et al., 2018", "ref_id": "BIBREF60"}, {"start": 412, "end": 438, "text": "(<PERSON><PERSON><PERSON> and <PERSON>, 2010;", "ref_id": "BIBREF58"}, {"start": 439, "end": 460, "text": "<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF29"}, {"start": 461, "end": 479, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF10"}, {"start": 579, "end": 599, "text": "(<PERSON> et al., 2024;", "ref_id": "BIBREF22"}, {"start": 600, "end": 622, "text": "<PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}, {"start": 890, "end": 913, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF9"}, {"start": 914, "end": 935, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF16"}, {"start": 936, "end": 961, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In this work, we show that directly calibrating the level of noise to satisfy a given level of attack risk, as opposed to satisfying a certain ε, enables a significant increase in utility (see Figure 1 ). We enable this direct calibration to attack risk by working under f -DP (<PERSON> et al., 2022) , a hypothesis testing interpretation of DP. In particular, we extend the tight privacy analysis method by <PERSON><PERSON><PERSON><PERSON> et al. (2022) to directly estimate operational privacy risk notions in f -DP. Then, we use our extended algorithm to directly calibrate the level of noise to satisfy a given level of attack risk. Concretely, our contributions are:", "cite_spans": [{"start": 277, "end": 296, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF10"}, {"start": 404, "end": 428, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF11"}], "ref_spans": [{"start": 200, "end": 201, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "1. We provide efficient methods for calibrating noise to (a) maximum accuracy (equivalently, advantage), (b) sensitivity and specificity of membership inference attacks, in any DP mechanism, including DP-SGD (<PERSON><PERSON><PERSON> et al., 2016) with arbitrarily many steps. 2. We empirically show that our calibration methods reduce the required noise scale for a given level of privacy risk, up to 2× as compared to standard methods for choosing DP parameters.", "cite_spans": [{"start": 208, "end": 228, "text": "(<PERSON><PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In a private language modeling task with GPT-2 (<PERSON><PERSON> et al., 2019) , we demonstrate that the decrease in noise can translate to a 18 p.p. gain in classification accuracy. 3. We demonstrate that relying on membership inference accuracy as an interpretation of privacy risk, as is common practice, can increase attack power in privacy-critical regimes, and that calibration for sensitivity and specificity does not suffer from this drawback.", "cite_spans": [{"start": 47, "end": 69, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF50"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "4. We provide a Python package which implements our algorithms for analyzing DP mechanisms in terms of the interpretable f -DP guarantees, and calibrating to operational risks:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "github.com/<PERSON>-<PERSON>/riskcal Ultimately, we advocate for practitioners to calibrate the noise level in privacy-preserving machine learning algorithms to a sensitivity and specificity constraint under f -DP as outlined in Section 3.2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Related Work. Prior work has studied methods for communicating the privacy guarantees afforded by differential privacy (<PERSON><PERSON><PERSON><PERSON> et al., 2023 (<PERSON><PERSON><PERSON><PERSON> et al., , 2022;; <PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2018) , and introduced various principled methods for choosing the privacy parameters (<PERSON><PERSON><PERSON> and <PERSON>, 2015; <PERSON><PERSON><PERSON> et al., 2014; <PERSON><PERSON> et al., 2014) . Unlike our approach, these works assume that the mechanisms are calibrated to a given ε privacy budget parameter, and do not aim to directly set the privacy guarantees in terms of operational notions of privacy risk. <PERSON><PERSON><PERSON><PERSON> et al. (2024) ; <PERSON><PERSON><PERSON> and <PERSON><PERSON> (2023) ; <PERSON><PERSON> et al. (2024) ; <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2022) use variants of DP that directly limit the advantage of membership inference attacks. We show that calibrating noise to a given level of advantage can increase privacy risk in security-critical regimes and provide methods that mitigate this issue. <PERSON><PERSON> et al. (2024) provide methods for evaluating the success of membership inference attacks under a weaker threat model than in DP. Unlike their work, we preserve the standard strong threat model in differential privacy but set and report the privacy guarantees in terms of an operational notion of risk under f -DP as opposed to the ε parameter.", "cite_spans": [{"start": 119, "end": 144, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023", "ref_id": null}, {"start": 145, "end": 174, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., , 2022;;", "ref_id": "BIBREF41"}, {"start": 175, "end": 196, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF16"}, {"start": 197, "end": 217, "text": "<PERSON><PERSON><PERSON> et al., 2021;", "ref_id": null}, {"start": 218, "end": 236, "text": "<PERSON> et al., 2018)", "ref_id": "BIBREF60"}, {"start": 317, "end": 343, "text": "(<PERSON><PERSON><PERSON> and <PERSON>, 2015;", "ref_id": "BIBREF1"}, {"start": 344, "end": 364, "text": "<PERSON><PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF46"}, {"start": 365, "end": 382, "text": "<PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF23"}, {"start": 602, "end": 624, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2024)", "ref_id": null}, {"start": 627, "end": 648, "text": "<PERSON><PERSON><PERSON> and Is<PERSON> (2023)", "ref_id": null}, {"start": 651, "end": 669, "text": "<PERSON><PERSON> et al. (2024)", "ref_id": "BIBREF25"}, {"start": 672, "end": 697, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF39"}, {"start": 946, "end": 967, "text": "<PERSON><PERSON> et al. (2024)", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "2 Problem Statement", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Setup and notation. Let D n denote the set of all datasets of size n over a space D, and let S ≃ S ′ denote a neighboring relation, e.g. S, S ′ that differ by one datapoint. We study randomized algorithms (mechanisms) M (S) that take as input a dataset S ∈ 2 D , and output the result of a computation, e.g., statistical queries or an ML model. We denote the output domain of the mechanism by Θ. For ease of presentation, we mainly consider randomized mechanisms that are parameterized by a single noise parameter ω ∈ Ω, but our results extend to mechanisms with multiple parameters. For example, in the Gaussian mechanism (<PERSON><PERSON> et al., 2014) , M (S) = q(S) + Z, where Z ∼ N (0, σ 2 ) and q(S) is a non-private statistical algorithm, the parameter is ω = σ with Ω = R ≥0 . We denote a parameterized mechanism by M ω (S). We summarize the notation in Table 1 in the Appendix.", "cite_spans": [{"start": 623, "end": 643, "text": "(<PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF14"}], "ref_spans": [{"start": 857, "end": 858, "text": "1", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Preliminaries", "sec_num": "2.1"}, {"text": "Differential Privacy. For any γ ≥ 0, we define the hockey-stick divergence from distribution P to Q over a domain O by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "D γ (P ∥ Q) ≜ sup E Q(E) -γP (E)", "eq_num": "(1)"}], "section": "Preliminaries", "sec_num": "2.1"}, {"text": "where the supremum is taken over all measurable sets E ⊆ O. We define differential privacy (DP) (<PERSON><PERSON> et al., 2006) as follows:", "cite_spans": [{"start": 96, "end": 116, "text": "(<PERSON><PERSON> et al., 2006)", "ref_id": "BIBREF13"}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2.1"}, {"text": "Definition 2.1. A mechanism M (•) satisfies (ε, δ)-DP iff sup S≃S ′ D e ε (M (S) ∥ M (S ′ )) ≤ δ.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2.1"}, {"text": "Lower values of ε and δ mean more privacy which in turn requires more noise, and vice versa. In the rest of the paper we assume that a larger value of the parameter ω ∈ Ω for Ω ⊆ R, e.g., standard deviation of Gaussian noise ω = σ in the Gaussian mechanism, means that the mechanism M ω (•) is more noisy, which translates into a higher level of privacy (smaller ε, δ), but lower utility.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2.1"}, {"text": "Most DP algorithms satisfy a collection of (ε, δ)-DP guarantees. We define the privacy profile (<PERSON><PERSON> and <PERSON>, 2018) , or privacy curve (<PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2023) of a mechanism as:", "cite_spans": [{"start": 95, "end": 117, "text": "(<PERSON><PERSON> and <PERSON>, 2018)", "ref_id": "BIBREF3"}, {"start": 137, "end": 156, "text": "(<PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF64"}, {"start": 157, "end": 179, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2.1"}, {"text": "Definition 2.2. A parameterized mechanism M ω (•) has a privacy profile ε ω : [0, 1] → R if for every δ ∈ [0, 1], M ω (•) is (ε(δ), δ)-DP.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2.1"}, {"text": "We refer to the function δ ω (ε), defined analogously, also as the privacy profile.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2.1"}, {"text": "A common algorithm for training neural networks with DP guarantees is DP-SGD (<PERSON><PERSON><PERSON> et al., 2016) . The basic building block of DP-SGD is the subsampled Gaussian mechanism, defined as", "cite_spans": [{"start": 77, "end": 97, "text": "(<PERSON><PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "DP-SGD.", "sec_num": null}, {"text": "M (S) = q(PoissonSample p • S) + Z, where Z ∼ N (0, ∆ 2 2 • σ 2 • I d ),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DP-SGD.", "sec_num": null}, {"text": "and PoissonSample p is a procedure which subsamples a dataset S such that every record has the same probability p ∈ (0, 1) to be in the subsample. DP-SGD, parameterized by p, σ, and T ≥ 1, is a repeated application of the subsampled Gaussian mechanism:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DP-SGD.", "sec_num": null}, {"text": "M (1) • M (2) • • • • • M (T ) (S)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DP-SGD.", "sec_num": null}, {"text": ", where q (i) (•) is a single step of gradient descent with per-record gradient clipping to ∆ 2 Euclidean norm. In line with a standard practice (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , we regard all parameters but σ as fixed, thus ω = σ.", "cite_spans": [{"start": 145, "end": 170, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "DP-SGD.", "sec_num": null}, {"text": "Privacy profiles for mechanisms such as DP-SGD are computed via numerical algorithms called accountants (see, e.g., <PERSON><PERSON><PERSON> et al., 2016; <PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2023) . These algorithms compute the achievable privacy profile to accuracy nearly matching the lower bound of a privacy audit where the adversary is free to choose the entire (pathological or realistic) training dataset (<PERSON><PERSON><PERSON> et al., 2021 (<PERSON><PERSON><PERSON> et al., , 2023)) . Given these results, we regard the analyses of these accountants as tight, and use them for calibration to a particular (ε, δ)-DP constraint.", "cite_spans": [{"start": 116, "end": 135, "text": "<PERSON><PERSON><PERSON> et al., 2016;", "ref_id": "BIBREF0"}, {"start": 136, "end": 154, "text": "<PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF64"}, {"start": 155, "end": 179, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF11"}, {"start": 180, "end": 202, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF2"}, {"start": 418, "end": 436, "text": "(<PERSON><PERSON><PERSON> et al., 2021", "ref_id": null}, {"start": 437, "end": 459, "text": "(<PERSON><PERSON><PERSON> et al., , 2023))", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "DP-SGD.", "sec_num": null}, {"text": "Standard Calibration. The procedure of choosing the parameter ω ∈ Ω to satisfy a given level of privacy is called calibration. In standard calibration, one chooses ω given a target DP guarantee ε ⋆ and an accountant that supplies a privacy profile ε ω (δ) for any noise parameter ω ∈ Ω, to ensure that", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DP-SGD.", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "M ω (S) satisfies (ε ⋆ , δ ⋆ )-DP: min ω∈Ω ω s.t. ε ω (δ ⋆ ) ≥ ε ⋆ ,", "eq_num": "(2)"}], "section": "DP-SGD.", "sec_num": null}, {"text": "with δ ⋆ set by convention to δ ⋆ = 1 /c•n, where n is the dataset size, and c > 1 (see, e.g., <PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) . The parameter ε ⋆ is also commonly chosen by convention between 2 and 10 for privacy-persevering ML algorithms with practical utility (<PERSON><PERSON><PERSON><PERSON> et al., 2023) . In Eq. ( 2) and the rest the paper we denote by ⋆ the target value of privacy risk.", "cite_spans": [{"start": 95, "end": 119, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF49"}, {"start": 120, "end": 138, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF45"}, {"start": 275, "end": 300, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "DP-SGD.", "sec_num": null}, {"text": "After calibration, the (ε, δ) parameters are often mapped to some operational notation of privacy attack risk for interpretability. In the next section, we introduce the hypothesis testing framework of DP, f -DP, and the notions of risk that (ε, δ) parameters are often mapped to. In contrast to standard calibration, in Section 2.3, we calibrate ω to directly minimize these privacy risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DP-SGD.", "sec_num": null}, {"text": "We can interpret differential privacy through the lens of membership inference attacks (MIAs) in the so-called strong-adversary model (see, e.g., <PERSON><PERSON><PERSON> et al., 2021) . In this framework, the adversary aims to determine whether a given output θ ∈ Θ came from M (S) or M (S ′ ), where S ′ = S ∪ {z} for some target example z ∈ D. † The adversary has access to the mechanism M (•), the dataset S, and the target example z ∈ D. Such an attack is equivalent to a binary hypothesis test (<PERSON><PERSON><PERSON> and <PERSON>, 2010; <PERSON><PERSON><PERSON> et al., 2015; <PERSON> et al., 2022) :", "cite_spans": [{"start": 146, "end": 164, "text": "<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": null}, {"start": 480, "end": 506, "text": "(<PERSON><PERSON><PERSON> and <PERSON>, 2010;", "ref_id": "BIBREF58"}, {"start": 507, "end": 528, "text": "<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF29"}, {"start": 529, "end": 547, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "H 0 : θ ∼ M (S), H 1 : θ ∼ M (S ′ ),", "eq_num": "(3)"}], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "where the MIA is modelled as a test ϕ : Θ → [0, 1] that maps a given mechanism output θ to the probability of the null hypothesis H 0 being rejected. We can analyze this hypothesis test through the trade-off between the achievable false positive rate (FPR) α ϕ ≜ E M (S) [ϕ] and false negative rate", "cite_spans": [{"start": 271, "end": 274, "text": "[ϕ]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "(FNR) β ϕ ≜ 1 -E M (S ′ ) [ϕ],", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "where the expectations are taken over the coin flips in the mechanism. ‡ <PERSON> et al. (2022) formalize the trade-off function and define f -DP as follows: Definition 2.3. A trade-off function T (M (S), M (S ′ )) : [0, 1] → [0, 1] outputs the FNR of the most powerful attack at any given level α ∈ [0, 1]:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "T (M (S), M (S ′ ))(α) = inf ϕ: Θ→[0,1] {β ϕ | α ϕ ≤ α} (4)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "See Figure 5 in the Appendix for an illustration. Definition 2.4. A mechanism M (•) satisfies f -DP, where f is the trade-off curve for some other mechanism, if for all α ∈ [0, 1], we have inf", "cite_spans": [], "ref_spans": [{"start": 11, "end": 12, "text": "5", "ref_id": null}], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "S≃S ′ T (M (S), M (S ′ ))(α) ≥ f (α).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "Next, we state the equivalence between (ε, δ)-DP guarantees and f -DP guarantees. Proposition 2.1 (<PERSON> et al. (2022) ).", "cite_spans": [{"start": 98, "end": 117, "text": "(<PERSON> et al. (2022)", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "If a mechanism M (•) is (ε, δ)-DP, then it is f -DP with f (α) = max{0, 1 -δ -e ε α, e -ε • (1 -δ -α)}. (5) Moreover, a mechanism M (•) satisfies (ε(δ), δ)-DP for all δ ∈ [0, 1] iff it is f -DP with f (α) = sup δ∈[0,1] max{0, 1 -δ -e ε(δ) α, e -ε(δ) • (1 -δ -α)}.", "eq_num": "(6)"}], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "We overview three particular notions of attack risk: advantage/accuracy of MIAs, FPR/FNR of MIAs, and reconstruction robustness. These risks can be thought of as summary statistics of the f curve.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "Advantage/Accuracy. <PERSON> et al. (2018) proposed § to measure the attack risk as the maximum achievable attack accuracy. To avoid confusion with task accuracy, we use advantage over random guessing, which is the difference between the attack TPR 1 -β ϕ and FNR α ϕ :", "cite_spans": [{"start": 20, "end": 38, "text": "<PERSON> et al. (2018)", "ref_id": "BIBREF60"}], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "η ≜ sup S≃S ′ sup ϕ: Θ→[0,1] 1 -β ϕ -α ϕ .", "eq_num": "(7)"}], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "The advantage η is a linear transformation of the maximum attack accuracy", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "sup 1 /2 • (1 -β ϕ ) + 1 /2 • (1 -α ϕ )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": ", where supremum is over S ≃ S ′ and ϕ : Θ → [0, 1]. Moreover, η can be obtained from a fixed point α * = f (α * ) of the f curve as 1 -2α * , and it is bounded given an (ε, δ)-DP guarantee: Proposition 2.2 (<PERSON> et al. (2015) ). If a mechanism M (•) is (ε, δ)-DP, then we have:", "cite_spans": [{"start": 207, "end": 229, "text": "(<PERSON><PERSON><PERSON> et al. (2015)", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "η ≤ e ε -1 + 2δ e ε + 1 . (", "eq_num": "8"}], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "FPR/FNR Risk. Recent work (<PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> and <PERSON>, 2021) has argued that MIAs are a relevant threat only when the attack true positive rate 1 -β ϕ is high at low enough α ϕ . As a concrete notion of risk, we thus consider minimum level of attack FNR β ⋆ within an FPR region α ∈ [0, α ⋆ ], where α ⋆ is a low value. This approach is similar to the statistically significant p-values often used in the sciences. Following the scientific standards and <PERSON><PERSON> et al. (2022) , we consider α ⋆ ∈ {0.01, 0.05, 0.1}.", "cite_spans": [{"start": 26, "end": 48, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": null}, {"start": 49, "end": 70, "text": "<PERSON><PERSON><PERSON> and <PERSON>, 2021)", "ref_id": "BIBREF51"}, {"start": 464, "end": 485, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Operational Privacy Risks", "sec_num": "2.2"}, {"text": "Another privacy threat is the reconstruction of training data records (see, e.g., <PERSON><PERSON> et al., 2022) . Denoting by R(θ; z) an attack that aims to reconstruct z, its success probability can be formalized as ρ ≜ Pr[ℓ(z, R(θ; z)) ≤ γ] over θ ∼ M (S ∪ {z}), z ∼ π for some loss function ℓ : D 2 → R and prior π. <PERSON><PERSON><PERSON> et al. (2023a) showed that MIA error rates bound reconstruction success as ρ ≤ 1 -f (κ γ ) for an appropriate choice of κ γ . Therefore, the FPR/FNR trade-off curve can also be thought as a notion of robustness to reconstruction attacks.", "cite_spans": [{"start": 82, "end": 101, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF4"}, {"start": 309, "end": 331, "text": "<PERSON><PERSON><PERSON> et al. (2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Reconstruction Robustness.", "sec_num": null}, {"text": "The standard practice in DP is to calibrate the noise scale ω of a mechanism M ω (•) to some target (ε ⋆ , δ ⋆ )-DP guarantee, with ε ⋆ from a recommended range, e.g., ε ⋆ ∈ [2, 10], and δ ⋆ fixed to δ ⋆ < 1 /n, as in Eq. (2). Then, the privacy guarantees provided by the chosen (ε ⋆ , δ ⋆ ) are obtained by mapping these values to bounds on sensitivity and specificity (by Proposition 2.1) or advantage (by Proposition 2.2) of membership inference attacks. In this work, we show that if the goal is to provide an operational and interpretable guarantee such as attack advantage or FPR/FNR, this approach leads to unnecessarily pessimistic noise requirements and a deterioration in utility due to the intermediate step of setting (ε ⋆ , δ ⋆ ). We show it is possible to skip this intermediate step by using the hypothesis-testing interpretation of DP to directly calibrate noise to operational notions of privacy risk. In practice, this means replacing the constraint in Eq. ( 2) with an operational notion of risk:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Our Objective: Attack-Aware Noise Calibration", "sec_num": "2.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "min ω∈Ω ω s.t. risk ω ≤ threshold ⋆ . (", "eq_num": "9"}], "section": "Our Objective: Attack-Aware Noise Calibration", "sec_num": "2.3"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Our Objective: Attack-Aware Noise Calibration", "sec_num": "2.3"}, {"text": "Solving this optimization problem requires two components. First, a way to optimize ω given a method to compute risk ω . As we assume that risk is monotonic in ω, Eq. ( 9) can be solved via binary search (see, e.g., <PERSON><PERSON><PERSON> et al., 2019) using calls to the risk ω function to an arbitrary precision. Second, we need a way to compute risk ω for any value ω. In the next section, we provide efficient methods for doing so for general DP mechanisms, including composed mechanisms such as DP-SGD, by extending the tight privacy analysis from <PERSON><PERSON><PERSON><PERSON> et al. (2022) to computing f -DP. Having these methods, we instantiate Eq. ( 9) for the notions of risks introduced in Section 2.2.", "cite_spans": [{"start": 216, "end": 236, "text": "<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF48"}, {"start": 537, "end": 561, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Our Objective: Attack-Aware Noise Calibration", "sec_num": "2.3"}, {"text": "In this section, we provide methods for calibrating DP mechanisms to the notions of privacy risk in Section 2.2. As a first step, we introduce the core technical building blocks of our calibration method: methods for evaluating advantage η ω and the trade-off curve f ω (α) for a given value of ω.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "Dominating Pairs and PLRVs. We make use of two concepts, originally developed in the context of computing tight privacy profiles under composition: dominating pairs (<PERSON> et al., 2022a) and privacy loss random variables (PLRV) (<PERSON><PERSON> and <PERSON>, 2016) . Definition 3.1. We say that a pair of distributions (P, Q) is a dominating pair for a mechanism M (•) if for every ε ∈ R, we have sup", "cite_spans": [{"start": 165, "end": 184, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 226, "end": 252, "text": "(<PERSON><PERSON> and <PERSON>, 2016)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "S≃S ′ D e ε (M (S) ∥ M (S ′ )) ≤ D e ε (P ∥ Q).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "Importantly, a dominating pair also provides a lower bound on the trade-off curve of a mechanism: Proposition 3.1. If (P, Q) is a dominating pair for a mechanism M , then for α ∈ [0, 1],", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "inf", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "S≃S ′ T (M (S), M (S ′ ))(α) ≥ T (P, Q)(α). (", "eq_num": "10"}], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "The proofs of this and all the following statements are in Appendix E. Proposition 3.1 implies that a mechanism M (•) is f -DP with f = T (P, Q). Next, we introduce privacy loss random variables, which provide a natural parameterization of the curve T (P, Q). Definition 3.2. Suppose that a mechanism M (•) has a discrete-valued dominating pair (P, Q). Then, we define the privacy loss random variables", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "(PLRVs) (X, Y ) as Y ≜ log Q(o) /P (o), with o ∼ Q, and X ≜ log Q(o ′ ) /P (o ′ ) with o ′ ∼ P .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "We can now state the result which serves as a main building block for our calibration algorithms, and forms the main theoretical contribution of our work. Theorem 3.3 (Accounting for advantage and f -DP with PLRVs). Suppose that a mechanism M (•) has a discrete-valued dominating pair (P, Q) with associated PLRVs (X, Y ). The attack advantage η for this mechanism is bounded:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "η ≤ Pr[Y > 0] -Pr[X > 0].", "eq_num": "(11)"}], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "Moreover, for any τ ∈ R ∪ {∞, -∞} and γ ∈ [0, 1], define", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "β * (τ, γ) = Pr[Y ≤ τ ] -γ Pr[Y = τ ].", "eq_num": "(12)"}], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "For any level α ∈ [0, 1], choosing τ = (1 -α)-quantile of X and γ = α-Pr [X>τ ] Pr [X=τ ] guarantees that T (P, Q)(α) = β * (τ, γ).", "cite_spans": [{"start": 83, "end": 89, "text": "[X=τ ]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "To show this, we use the Neyman-<PERSON> lemma to explicitly parameterize the most powerful attack at level α in terms the threshold τ on the Neyman-Pearson test statistic and the probability γ of guessing when the test statistic exactly equals the threshold. See Appendix E.2 for the detailed proof.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "We remark that similar results for the trade-off curve appear in (<PERSON> et al., 2022a) without the γ terms, as <PERSON> et al. assume continuous PLRVs (X, Y ). In our work, we rely on the technique due to <PERSON><PERSON><PERSON><PERSON> et al. (2022) , summarized in Appendix D, which discretizes continuous mechanisms such as the subsampled Gaussian in DP-SGD, and provides a dominating pair that is discrete and finitely supported over an evenly spaced grid. As the dominating pairs are discrete, the γ terms are non-zero, thus are necessary to fully reconstruct the trade-off curve.", "cite_spans": [{"start": 65, "end": 84, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 198, "end": 222, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Numeric Calibration to Attack Risks", "sec_num": "3"}, {"text": "First, we show how to instantiate Eq. ( 9) to calibrate noise to a target advantage η ⋆ ∈ [0, 1]. Let η ω denote the advantage of the mechanism M ω (•) as defined in Eq. ( 7):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Calibration to Advantage", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "min ω∈Ω ω s.t. η ω ≤ η ⋆ . (", "eq_num": "13"}], "section": "Calibration to Advantage", "sec_num": "3.1"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Calibration to Advantage", "sec_num": "3.1"}, {"text": "Given the PLRVs (X ω , Y ω ), we can obtain a substantially tighter bound than converting (ε, δ) guarantees using Proposition 2.2 under standard calibration. Specifically, Theorem 3.3 provides the following way to solve the problem:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Calibration to Advantage", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "min ω∈Ω ω s.t. Pr[Y ω > 0] -Pr[X ω > 0] ≤ η ⋆", "eq_num": "(14)"}], "section": "Calibration to Advantage", "sec_num": "3.1"}, {"text": "We call this approach advantage calibration, and show how to practically implement it in Algorithms 3 and 4 in the Appendix. Given a method for obtaining valid PLRVs X ω , Y ω for any ω, such as the one by <PERSON><PERSON><PERSON><PERSON> et al. (2022) (a) Calibrating noise to attack advantage significantly reduces the required noise scale compared to the standard approach. y axis is logarithmic. Proposition 3.2. Given PLRVs (X ω , Y ω ) of a discrete-valued dominating pair of a mechanism M ω (•), choosing ω * using Eq. ( 14) ensures η ω * ≤ η ⋆ .", "cite_spans": [{"start": 206, "end": 230, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Calibration to Advantage", "sec_num": "3.1"}, {"text": "We demonstrate how calibration for a given level of attack advantage can increase utility. As a mechanism to calibrate, we consider DP-SGD with p = 0.001 subsampling rate, T = 10,000 iterations, and assume that δ ⋆ = 10 -5 . Our goal is to compare the noise scale σ obtained via advantage calibration to the standard approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Utility Benefits.", "sec_num": null}, {"text": "As a baseline, we choose σ using standard calibration in Eq. ( 2), and convert the resulting (ε, δ) guarantees to advantage using Proposition 2.2. We detail this procedure in Algorithm 2 in the Appendix. We consider target values of advantage η ⋆ ∈ [0.01, 0.25]. As we show in Figure 2a , our direct calibration procedure enables to reduce the noise scale by up to 3.5×.", "cite_spans": [], "ref_spans": [{"start": 284, "end": 286, "text": "2a", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Utility Benefits.", "sec_num": null}, {"text": "Calibration to a given level of membership advantage is a compelling idea due to the decrease in noise required to achieve better utility at the same level of risk as with the standard approach. Despite this increase in utility, we caution that this approach comes with a deterioration of privacy guarantees other than maximum advantage compared to standard calibration. Concretely, it allows for increased attack TPR in the privacy-critical regime of low attack FPR (see Section 2.2). The next result quantifies this pitfall: Proposition 3.3 (Cost of advantage calibration). Fix a dataset size n > 1, and a target level of attack advantage η ⋆ ∈ (δ ⋆ , 1), where δ ⋆ = 1 /c•n for some c > 1. For any 0 < α < 1-η ⋆ 2 , there exists a DP mechanism for which the gap in FNR f standard (α) obtained with standard calibration for ε ⋆ that ensures η ≤ η ⋆ , and FNR f adv (α) obtained with advantage calibration is lower bounded:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Pitfalls of Calibrating for Advantage.", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∆β(α) ≜ f standard (α) -f adv (α) ≥ η ⋆ -δ ⋆ + 2α η ⋆ η ⋆ -1 . (", "eq_num": "15"}], "section": "Pitfalls of Calibrating for Advantage.", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Pitfalls of Calibrating for Advantage.", "sec_num": null}, {"text": "For example, if we aim to calibrate a mechanism to at most η ⋆ = 0.5 (or, 75% attack accuracy), we could potentially increase attack sensitivity by ∆β(α) ≈ 30 p.p. at FPR α = 0.1 compared to standard calibration with δ ⋆ = 10 -5 (see the illustration in Figure 2b ). Note that the difference ∆β in Proposition 3.3 is an overestimate in practice: the increase in attack sensitivity can be significantly lower for mechanisms such as the Gaussian mechanism (see Figure 6 in the Appendix).", "cite_spans": [], "ref_spans": [{"start": 261, "end": 263, "text": "2b", "ref_id": "FIGREF2"}, {"start": 466, "end": 467, "text": "6", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Pitfalls of Calibrating for Advantage.", "sec_num": null}, {"text": "In this section, we show how to calibrate the noise in any practical DP mechanism to a given minimum level of attack FNR β ⋆ within an FPR region α ∈ [0, α ⋆ ], which enables to avoid the pitfalls of advantage calibration. We base this notion of risk off the previous work (<PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> and <PERSON>, 2021) which argued that MIAs are a relevant threat only when the achievable TPR 1 -β is high at low FPR α. We instantiate the calibration problem in Eq. ( 9) as follows, assuming", "cite_spans": [{"start": 273, "end": 295, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": null}, {"start": 296, "end": 317, "text": "<PERSON><PERSON><PERSON> and <PERSON>, 2021)", "ref_id": "BIBREF51"}], "ref_spans": [], "eq_spans": [], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "M ω (•) satisfies f ω (α)-DP: min ω∈Ω ω s.t. inf 0≤α≤α ⋆ f ω (α) ≥ β ⋆ .", "eq_num": "(16)"}], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "To solve Eq. ( 16), we begin by showing that such calibration is in fact equivalent to requiring a given level of attack FNR β ⋆ and FPR α ⋆ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "Algorithm 1 Construct the trade-off curve using discrete privacy loss random variables (X, Y )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "Require: PMF Pr[X ω = x i ] over grid {x 1 , x 2 , . . . , x k } with x 1 < x 2 < . . . < x k Require: PMF Pr[Y ω = y j ] over grid {y 1 , y 2 , . . . , y l } with y 1 < y 2 < . . . < y l 1: procedure COMPUTEBETA(ω; α ⋆ ; X ω , Y ω ) 2: t ← min{i ∈ {0, 1, . . . , k} | Pr[X ω > x i ] ≤ α ⋆ }, where x 0 ≜ -∞ 3: γ ← α ⋆ -Pr[Xω>xt]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "Pr [Xω=xt] 4:", "cite_spans": [{"start": 3, "end": 10, "text": "[Xω=xt]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "return f ω (α ⋆ ) = Pr[Y ω ≤ x t ] -γ Pr[Y ω = x t ] Proposition 3.4. For any α ⋆ ≥ 0, β ⋆ ≥ 0 such that α ⋆ + β ⋆ ≤ 1, and any f -DP mechanism M (•): inf 0≤α≤α ⋆ f (α) ≥ β ⋆ iff f (α ⋆ ) ≥ β ⋆ . (", "eq_num": "17"}], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "This follows directly by monotonicity of the trade-off function f (<PERSON> et al., 2022) . The optimization problem becomes:", "cite_spans": [{"start": 66, "end": 85, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "min ω∈Ω ω s.t. f ω (α ⋆ ) ≥ β ⋆ . (", "eq_num": "18"}], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "Unlike advantage calibration to η ⋆ , the approach in Eq. ( 18) limits the adversary's capabilities without increasing the risk in the privacy-critical low-FPR regime, as we can explicitly control the acceptable attack sensitivity for a given low FPR.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "To obtain f ω (α), we use the PLRVs X ω , Y ω along with Theorem 3.3 to compute f = T (P, Q) ¶ (see Algorithm 1), and solve Eq. ( 18) using binary search over ω ∈ Ω. We provide the precise procedure in Algorithm 6 in the Appendix. This approach guarantees the desired level of risk: Proposition 3.5. Given PLRVs (X ω , Y ω ) of a discrete-valued dominating pair of a mechanism M ω (•), choosing ω * using Eq. ( 18) and Algorithm 1 to compute", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "f ω (α) ensures f ω * (α ⋆ ) ≥ β ⋆ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safer Choice: Calibration to FNR within a Given FPR Region", "sec_num": "3.2"}, {"text": "In this section, we first contextualize the proposed method within existing work. Then, we discuss settings in which alternatives to PLRV-based procedures could be more suitable.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Other Approaches to Trade-Off Curve Accounting", "sec_num": "3.3"}, {"text": "Benefits of PLRV-based Trade-Off Curve Accounting. Computational efficiency is important when estimating f ω (α), as the calibration problem requires evaluating this function multiple times for different values of ω as part of binary search. Algorithm 1 computes f ω (α) for a single ω in ≈ 500ms, enabling fast calibration, e.g., in ≈ 1 minute for DP-SGD with T = 10,000 steps on commodity hardware (see Appendix H). Existing methods for estimating f ω (α), on the contrary, either provide weaker guarantees than Proposition 3.5 or are substantially less efficient. In particular, <PERSON> et al.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Other Approaches to Trade-Off Curve Accounting", "sec_num": "3.3"}, {"text": "(2022) introduced µ-GDP, an asymptotic expression for f ω (α) as T → ∞, that overestimates privacy (<PERSON><PERSON> et al., 2021) , and thus leads to mechanisms that do not satisfy the desired level of attack resilience when calibrating to it. <PERSON><PERSON><PERSON> et al. (2023) ; <PERSON> et al. (2020) introduced a discretizationbased approach to approximate f ω (α) (discussed next) that can be orders of magnitude less efficient than the direct estimation in Algorithm 1, e.g., 1-6 minutes (≈ 100-700× slower) for a single evaluation of f ω (α) in the same setting as before, depending on the coarseness of discretization.", "cite_spans": [{"start": 99, "end": 118, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF64"}, {"start": 233, "end": 251, "text": "<PERSON><PERSON><PERSON> et al. (2023)", "ref_id": null}, {"start": 254, "end": 273, "text": "<PERSON> et al. (2020)", "ref_id": "BIBREF65"}], "ref_spans": [], "eq_spans": [], "section": "Other Approaches to Trade-Off Curve Accounting", "sec_num": "3.3"}, {"text": "Calibration using Black-Box Accountants. Most DP mechanisms are accompanied by (ε, δ)-DP accountants, i.e., methods to compute their privacy profile ε ω (δ) or δ ω (ε). Black-box access to these accountants enables to estimate η ω and f ω (α). In particular, Proposition 2.2 tells us that (0, δ)-DP mechanisms bound advantage as η ≤ δ. Thus, advantage calibration can also be performed with any ε ω (δ) accountant by calibrating noise to ensure ε ω (η ⋆ ) = 0. Estimating f ω (α), as mentioned previously, is less straightforward. Existing numeric approaches (<PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2020) are equivalent to approximating Eq. ( 6) on a discrete grid over δ ∈ {δ 1 , . . . , δ u }. This requires u calls to the accountant ε ω (δ), thus quickly becomes inefficient for estimating f ω (α) to high precision.", "cite_spans": [{"start": 559, "end": 578, "text": "(<PERSON><PERSON><PERSON> et al., 2023;", "ref_id": null}, {"start": 579, "end": 598, "text": "<PERSON> et al., 2020)", "ref_id": "BIBREF65"}], "ref_spans": [], "eq_spans": [], "section": "Other Approaches to Trade-Off Curve Accounting", "sec_num": "3.3"}, {"text": "We provide a detailed discussion of such black-box approaches in Appendix A.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Other Approaches to Trade-Off Curve Accounting", "sec_num": "3.3"}, {"text": "Calibration of Mechanisms with Known Trade-Off Curves. An important feature of our calibration methods is that they enable calibration of mechanisms whose privacy profile is unknown Figure 4 : Trade-off curves obtained via our method in Algorithm 1 provide a significantly tighter analysis of the attack risks, compared to the standard method of interpreting the privacy risk for a given (ε, δ) with fixed δ < 1 /n via Eq. ( 5). The trade-off curves are shown for three runs of DP-SGD with different noise multipliers in the language modeling experiment with GPT-2. The dotted line -shows the trade-off curve which corresponds to perfect privacy.", "cite_spans": [], "ref_spans": [{"start": 189, "end": 190, "text": "4", "ref_id": null}], "eq_spans": [], "section": "Other Approaches to Trade-Off Curve Accounting", "sec_num": "3.3"}, {"text": "in the exact form, e.g., DP-SGD for T > 1. Simpler mechanisms, such as the Gaussian mechanism, which are used for simpler statistical analyses, e.g., private mean estimation, admit exact analytical solutions to the calibration problems in Eqs. ( 13) and ( 18). In Appendix G, we provide such solutions for the standard Gaussian mechanism, which enable efficient calibration without needing Algorithm 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Other Approaches to Trade-Off Curve Accounting", "sec_num": "3.3"}, {"text": "In this section, we empirically evaluate the utility improvement of our calibration method over traditional approaches. We do so in simulations as well as in realistic applications of DP-SGD. In Appendix H, we also evaluate the utility gain when performing simpler statistical analyses.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "Simulations. First, we demonstrate the noise reduction when calibrating the DP-SGD algorithm for given error rates using the setup in Section 3.1. We fix three low FPR values: α ⋆ ∈ {0.01, 0.05, 0.1}, and vary maximum attack sensitivity 1 -β ⋆ from 0.1 to 0.5 in each FPR regime. We show the results in Figure 3 . We observe a significant decrease in the noise scale for all values. Although the decrease is smaller than with calibration for advantage (see Figure 2a ), calibrating directly for risk in the low FPR regime avoids the pitfall of advantage calibration: inadvertently increasing risk in this regime.", "cite_spans": [], "ref_spans": [{"start": 310, "end": 311, "text": "3", "ref_id": null}, {"start": 464, "end": 466, "text": "2a", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "Language Modeling and Image Classification. We showed that FPR/FNR calibration enables to significantly reduce the noise scale. Next, we study how much of this reduction in noise translates into actual utility improvement in downstream applications. We evaluate our method for calibrating noise in private deep learning on two tasks: text sentiment classification using the SST-2 dataset (<PERSON><PERSON> et al., 2013) , and image classification using the CIFAR-10 dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2009) .", "cite_spans": [{"start": 388, "end": 409, "text": "(<PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF53"}, {"start": 464, "end": 489, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "For sentiment classification, we fine-tune GPT-2 (small) (<PERSON><PERSON> et al., 2019) using a DP version of LoRA (<PERSON> et al., 2021) . For image classification, we follow the approach of <PERSON><PERSON><PERSON> and <PERSON><PERSON> (2021) of training a convolutional neural network on top of ScatterNet features (Oyallon and Mallat, 2015) with DP-SGD (<PERSON><PERSON><PERSON> et al., 2016) . See additional details in Appendix H. For each setting, by varying the noise scale, we obtain several models at different levels of privacy. For each of the models we compute the guarantees in terms of TPR 1 -β at three fixed levels of FPR α ⋆ ∈ {0.01, 0.05, 0.1} that would be obtained under standard calibration, and using our Algorithm 1.", "cite_spans": [{"start": 57, "end": 79, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF50"}, {"start": 107, "end": 124, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF64"}, {"start": 179, "end": 202, "text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON> (2021)", "ref_id": "BIBREF54"}, {"start": 276, "end": 302, "text": "(Oyallon and Mallat, 2015)", "ref_id": null}, {"start": 315, "end": 335, "text": "(<PERSON><PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "Figure 1 shows that FPR/FNR calibration significantly increases task accuracy (a notion of utility; not to confuse with attack accuracy, a notion of privacy risk) at the same level of 1 -β for all values of α ⋆ . For instance, for GPT-2, we see the accuracy increase of 18.3 p.p. at the same level of privacy risk (top leftmost plot). To illustrate the reasons behind such a large difference between the methods, in Figure 4 , we show the trade-off curves obtained with our Algorithm 1, and with the standard method of deriving the FPR/FNR curve from a single (ε, δ) pair for a fixed δ < 1 /n via Eq. ( 5). We can see that the latter approach drastically overestimates the attack risks, which translates to significantly higher noise and lower task accuracy when calibrating with standard calibration.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "1", "ref_id": "FIGREF0"}, {"start": 423, "end": 424, "text": "4", "ref_id": null}], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "In this work, we proposed novel methods for calibrating noise in differentially private learning targeting a given level of operational privacy risk: advantage and FPR/FNR of membership inference attacks. We introduced an accounting algorithm which directly and tightly estimates privacy guarantees in terms of f -DP, which characterizes these operational risks. Using simulations and end-to-end experiments on common use cases, we showed that our attack-aware noise calibration significantly decreases the required level of noise compared to the standard approach at the same level of operational risk. In the case of calibration for advantage, we also showed that the noise decrease could be harmful as it could allow for increased attack success in the low FPR regime compared to the standard approach, whereas calibration for a given level of FPR/FNR mitigates this issue. Next, we discuss limitations and possible directions for future work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "Choice of Target FPR/FNR. We leave open the question on how to choose the target FPR α ⋆ and FNR β ⋆ , e.g., whether standard significance levels in sciences such as α ⋆ = 0.05 are compatible with data protection regulation and norms. Further work is needed to develop concrete guidance on the choice of target FPR and FNR informed by legal and practical constraints.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "Catastrophic Failures. It is possible to construct pathological DP mechanisms which admit catastrophic failures (see, e.g., <PERSON><PERSON> et al., 2023) , i.e., mechanisms which allow non-trivial attack TPR at FPR α = 0 so that their trade-off curve is such that T (M (S), M (S ′ ))(0) < 1 for some S ≃ S ′ . A classical example in the context of private data release is a mechanism that releases a data record in the clear with probability δ > 0, in which case we have T (M (S), M (S ′ ))(0) = 1 -δ.", "cite_spans": [{"start": 124, "end": 148, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "See the proof of Proposition 3.3 in Appendix E for a concrete construction. In the case that such a pathological mechanism is used in practice, one should use standard calibration to (ε, δ) with δ ≪ 1 /n to directly limit the chance of catastrophic failures. Fortunately, practical mechanisms such as DP-SGD do not admit catastrophic failures, as they ensure T (M (S), M (S ′ ))(0) = 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "Tight Bounds for Privacy Auditing. Multiple prior works on auditing the privacy properties of ML algorithms (<PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON><PERSON><PERSON> and <PERSON>, 2019; <PERSON><PERSON><PERSON><PERSON> et al., 2019) used conversions between (ε, δ) and operational risks like in Proposition 2.1, which we have shown to significantly overestimate the actual risks. Beyond calibrating noise, our methods provide bounds on attack success rates for audits in a more precise and computationally efficient way than a recent similar approach by <PERSON><PERSON><PERSON> et al. (2023) .", "cite_spans": [{"start": 108, "end": 127, "text": "(<PERSON><PERSON><PERSON> et al., 2021;", "ref_id": null}, {"start": 128, "end": 145, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF38"}, {"start": 146, "end": 172, "text": "<PERSON><PERSON><PERSON> and <PERSON>, 2019;", "ref_id": "BIBREF26"}, {"start": 173, "end": 197, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF15"}, {"start": 519, "end": 537, "text": "<PERSON><PERSON><PERSON> et al. (2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "Accounting in the Relaxed Threat Models. Although we have focused on DP, our methods apply to any notion of privacy that is also formalized as a hypothesis test. In particular, our method can be used as is to compute privacy guarantees of DP-SGD in a relaxed threat model (RTM) proposed by <PERSON><PERSON><PERSON> et al. (2023b) . Previously, there was no efficient method for accounting in the RTM.", "cite_spans": [{"start": 290, "end": 312, "text": "<PERSON><PERSON><PERSON> et al. (2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "Applications Beyond Privacy. Our method can be applied to ensure provable generalization guarantees in deep learning. Indeed, prior work has shown that advantage η bounds generalization gaps of ML models (<PERSON><PERSON><PERSON> et al., 2022a,b) . Thus, even though advantage calibration can exacerbate certain risks, it can be a useful tool for ensuring a desired level of generalization in models that usually do not come with non-vacuous generalization guarantees, e.g., deep neural networks. ", "cite_spans": [{"start": 204, "end": 230, "text": "(<PERSON><PERSON><PERSON> et al., 2022a,b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "ω : 2 D → Θ Privacy-preserving mechanism ω ∈ Ω Noise parameter of mechanism M (S) D γ (M (S) ∥ M (S ′ )), γ ≥ 0 Hockeystick divergence Eq. (1) ε ∈ (0, ∞), δ ∈ [0, 1]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "Privacy parameters in differential privacy Def. 2.1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "ε ω : [0, 1] → R Privacy profile curve ε ω (δ) Def. 2.2 δ ω : R → [0, 1] Privacy profile curve δ ω (ε) Def. 2.2 ϕ : Θ → [0, 1] Membership inference hypothesis test α ϕ ∈ [0, 1]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "False positive rate (FPR) of attack ϕ(θ)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "β ϕ ∈ [0, 1] False negative rate (FNR) of attack ϕ(θ) η ∈ [0, 1]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "Maximal advantage across attacks against mechanism M (S) Eq. ( 7) ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "T (M (S), M (S ′ )) : [0, 1] → [0, 1] Trade-off curve", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ω s.t. ε ω (η ⋆ ) = 0 or δ ω (0) = η ⋆", "eq_num": "(19)"}], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "This is a more generic way to perform advantage calibration using an arbitrary black-box accountant.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "It is equivalent to our procedure in Section 3.1 when using <PERSON><PERSON><PERSON><PERSON> et al. (2022) accountant.", "cite_spans": [{"start": 60, "end": 84, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "FPR/FNR Calibration with Grid Search. Given a black-box DP accountant, i.e., a method which computes the privacy profile ε ω (δ) of a mechanism M ω (•), we can approximate f ω (α) by discretizing the range of δ ∈ [0, 1] and solving Eq. ( 6) as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "f ω (α) ≥ sup δ∈{δ1,δ2,...,δu} max{0, 1 -δ -e εω(δ) α, e -εω(δ) • (1 -δ -α)},", "eq_num": "(20)"}], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "where 0 ≤ δ 1 < δ 2 < . . . < δ u ≤ 1. It is possible to perform an analogous discretization using δ ω (ε) and Proposition 2.1, in which case we have to additionally choose a bounded subspace ε ∈ [ε min , ε max ] ⊂ R. Equivalent procedures to Eq. ( 20) have previously appeared in <PERSON><PERSON><PERSON> et al. (2023) ; <PERSON> et al. (2020) .", "cite_spans": [{"start": 281, "end": 299, "text": "<PERSON><PERSON><PERSON> et al. (2023)", "ref_id": null}, {"start": 302, "end": 321, "text": "<PERSON> et al. (2020)", "ref_id": "BIBREF65"}], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "Plugging in Eq. ( 20) into the problem in Eq. ( 18), we can calibrate mechanisms to a given α ⋆ , β ⋆ using binary search (see Section 2.3) in a space [ω min , ω max ] ⊆ Ω to additive error ω err > 0. Denoting by ν:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ν ≜ ω max -ω min ω err ,", "eq_num": "(21)"}], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "the calibration requires u • ⌈log 2 ν⌉ evaluations of ε ω (δ). For instance, a single evaluation of the bound in Eq. ( 20) takes approximately one minute with u = 100, and six minutes with u = 1,000 for DP-SGD with T = 10,000 using <PERSON><PERSON> et al. (2021) accountant as an instantiation of ε ω (δ) on commodity hardware (see Appendix H). In contrast, evaluating f ω (•) using Algorithm 1 in the same settings takes approximately 500ms at the default discretization level ∆ = 10 -4 (see Appendix D).", "cite_spans": [{"start": 232, "end": 250, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF64"}], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "Although this approach is substantially less computationally efficient than our direct procedure in Section 3.2, its strength is that it can be used to calibrate noise in any DP algorithm which provides a way to compute its (ε, δ) guarantees.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Concluding Remarks", "sec_num": "5"}, {"text": "Advantage calibration. The standard advantage calibration first finds ε ⋆ for a given δ ⋆ < 1 /n which provides the desired advantage guarantee via Eq. ( 8), then calibrates noise to the derived (ε ⋆ , δ ⋆ )-DP guarantee using the privacy profile ε ω (δ) function:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Detailed Calibration Algorithms", "sec_num": null}, {"text": "Require: η ⋆ , δ ⋆ , where δ ⋆ < 1 n , privacy profile ε ω (δ). 1: Find ε ⋆ by solving Eq. ( 8) for ε with fixed δ = δ ⋆ and η = η ⋆ 2: Find noise parameter ω * , e.g., using binary search:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "ω * ← argmin ω∈Ω s.t. ε ω (δ ⋆ ) ≥ ε ⋆ 3: return ω *", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "For direct calibration to advantage, we first show how to practically use the expression in Theorem 3.3 to evaluate advantage using PLRVs:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "Algorithm 3 Compute advantage using PLRVs (X, Y ) Require: PMF Pr[X ω = τ ] over grid {x 1 , x 2 , . . . , x k } with x 1 < x 2 < . . . < x k Require: PMF Pr[Y ω = τ ] over grid {y 1 , y 2 , . . . , y l } with y 1 < y 2 < . . . < y l 1: procedure COMPUTEADV(ω; X ω , Y ω ) 2: t X ← min{i ∈ [k] | x i > 0}, t Y ← min{i ∈ [l] | y i > 0} 3: return l i=t Y Pr[Y ω = y i ] - k i=t X Pr[X ω = x i ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "Given Algorithm 3, direct calibration to advantage amounts to, e.g., binary search:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "Algorithm 4 Direct advantage calibration using PLRVs (X, Y )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "Require: η ⋆ , PLRVs X ω , Y ω (see Algorithm 3 for a more detailed specification) 1: Find noise parameter ω * , e.g., using binary search:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "ω * ← argmin ω∈Ω s.t. COMPUTEADV(ω; X ω , Y ω ) ≤ η ⋆ 2: return ω *", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "FPR/FNR Calibration. The standard approach to FPR/FNR calibration proceeds analogously to advantage calibration. First, the algorithm solves Eq. ( 5) to obtain the value of ε ⋆ which ensures that a mechanism satisfies f (α ⋆ ) = β ⋆ . Then, the algorithm calibrates the noise to the obtained (ε ⋆ , δ ⋆ ) pair using the privacy profile function ε ω (δ):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "Algorithm 5 Standard FPR/FNR calibration Require: α ⋆ , β ⋆ , δ ⋆ , where δ ⋆ < 1 n , privacy profile ε ω (δ). 1: Find ε ⋆ by solving Eq. ( 5) for ε with fixed δ = δ ⋆ and f (α ⋆ ) = β ⋆ 2: Find noise parameter ω * , e.g., using binary search:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "ω * ← argmin ω∈Ω s.t. ε ω (δ ⋆ ) ≥ ε ⋆ 3: return ω *", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "Direct calibration to FPR/FNR amounts to, e.g., binary search, using calls to Algorithm 1: Algorithm 6 Direct FPR/FNR calibration using PLRVs (X, Y ) Require: α ⋆ , β ⋆ , PLRVs X ω , Y ω (see Algorithm 1 for a more detailed specification)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "1: Find noise parameter ω * , e.g., using binary search:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "ω * ← argmin ω∈Ω s.t. COMPUTEBETA(ω; α ⋆ ; X ω , Y ω ) ≥ β ⋆ 2: return ω * Attack risk measure Symbol Derived β ⋆ Advantage η ⋆ 1 -α ⋆ -η ⋆ Accuracy acc ⋆ 2(α ⋆ -acc ⋆ ) Positive predictive value / precision ppv ⋆ (α ⋆ -1)(ppv ⋆ -1) ppv ⋆ -1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "Table 2 : Some supported risk measures for calibration with a fixed level of FPR α ⋆ , with the derivation of the corresponding level of FNR β ⋆ . Given α ⋆ and the derived β ⋆ , we can calibrate noise using the procedure in Section 3.2.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Algorithm 2 Standard advantage calibration", "sec_num": null}, {"text": "Noise calibration for a given FPR/FNR level can be seen as a basic building block to calibrate for other operational measures of risk that are functions of FPR α and FNR β.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Calibration to Other Risk Notions", "sec_num": null}, {"text": "For instance, <PERSON><PERSON><PERSON> and <PERSON> (2021) propose to measure the risks of membership inference attacks in terms of accuracy acc and FPR α, where:", "cite_spans": [{"start": 14, "end": 35, "text": "<PERSON><PERSON><PERSON> and <PERSON> (2021)", "ref_id": "BIBREF51"}], "ref_spans": [], "eq_spans": [], "section": "C Calibration to Other Risk Notions", "sec_num": null}, {"text": "acc(α, β) ≜ 1 /2 • ((1 -α) + (1 -β)) .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Calibration to Other Risk Notions", "sec_num": null}, {"text": "We can calibrate for a given level of accuracy acc ⋆ and FPR α ⋆ using the method in Section 3.2 by solving the expression for accuracy for a given β ⋆ . <PERSON><PERSON><PERSON> et al. (2021) propose to measure positive predictive value, or precision, of attacks:", "cite_spans": [{"start": 154, "end": 177, "text": "<PERSON><PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "C Calibration to Other Risk Notions", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ppv(α, β) ≜ 1 -β 1 -β + α . (", "eq_num": "22"}], "section": "C Calibration to Other Risk Notions", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Calibration to Other Risk Notions", "sec_num": null}, {"text": "Although precision alone is not sufficient to determine the level of privacy, like with accuracy, we can calibrate for a given level of precision ppv ⋆ and FPR α ⋆ by deriving the corresponding β ⋆ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Calibration to Other Risk Notions", "sec_num": null}, {"text": "We provide the exact conversions in Table 2 . These enable practitioners to use the calibration method in Section 3.2 while reporting technically equivalent but potentially more interpretable measures, e.g., attack accuracy at a given FPR.", "cite_spans": [], "ref_spans": [{"start": 42, "end": 43, "text": "2", "ref_id": null}], "eq_spans": [], "section": "C Calibration to Other Risk Notions", "sec_num": null}, {"text": "Although throughout the paper we have assumed that the hypotheses H 0 and H 1 both have probability 1 /2, our results and conversions can be easily extended to settings where the hypotheses are not equiprobable, as proposed by <PERSON><PERSON><PERSON> et al. (2021) .", "cite_spans": [{"start": 227, "end": 250, "text": "<PERSON><PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "C Calibration to Other Risk Notions", "sec_num": null}, {"text": "We summarize the technique from <PERSON><PERSON><PERSON><PERSON> et al. (2022) to construct a dominating pair from a composed mechanism", "cite_spans": [{"start": 32, "end": 56, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": "M (S) = M (1) • M (2) • • • • • M (T ) (S)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": ". This models the common use case in privacy-preserving ML where a simple mechanism, such as the subsampled Gaussian in DP-SGD, is applied T times. We assume that each sub-mechanism M (i) , i ∈ [T ], has a known privacy curve δ i (ε). Given an input discretization parameter ∆, a size k, and a starting ε 1 , (<PERSON><PERSON><PERSON><PERSON> et al., 2022) creates a grid {ε 1 , ε 1 + ∆, . . . , ε 1 + k∆}. Then, they compute the privacy curve on this grid {δ i (ε 1 ), δ i (ε 1 + ∆), . . . , δ i (ε 1 + k∆)}, and append the values of δ(-∞) = 0 and δ(∞). The dominating pair for the i th mechanism is constructed using Algorithm 7. Note that Algorithm 7 is identical to Algorithm 1 in <PERSON><PERSON><PERSON><PERSON> et al. (2022) , with the notation modified to be consistent with the notation in this paper.", "cite_spans": [{"start": 309, "end": 334, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF11"}, {"start": 663, "end": 687, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": "This process is repeated for every mechanism. As long as the discretization parameter ∆ is the same for all T mechanisms, the resulting collection of PLRVs can can be composed via the Fast Fourier Transform. The dominating pair for the composed mechanism M is simply the distribution of (X 1 + X 2 . . .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": "+ X T , Y 1 + Y 2 . . . + X T ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": "We remark that the discretization parameter ∆ is user-defined, and the choice for the size k and starting ε for each grid is mechanism-specific. For further implementation details, we point the reader to the code documentation file and the code itself, which can be found in the dp accounting Python library,. In particular, we note that while the PLRVs X, Y have the same support except for atoms at ±∞, the support of the composed PLRV X 1 + X 2 . . . + X T need not be the same as the the support of Y 1 + Y 2 . . . + Y T . This is because in the convolution part of the implementation of <PERSON><PERSON><PERSON><PERSON> et al. (2022) , the code discards any tail probabilities smaller than some truncation Algorithm 7 (<PERSON><PERSON><PERSON><PERSON> et al., 2022) Construct a dominating pair Require: Grid: {-∞, ε 1 , . . . , ε k , ∞}.", "cite_spans": [{"start": 592, "end": 616, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF11"}, {"start": 701, "end": 726, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": "Require: Privacy curve on a grid: {0, δ(ε 1 ), . . . , δ(ε k ), δ(∞)}.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": "1: P (∞) = 0 2: for i = k -1, . . . , 1 do 3:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": "P (ε i ) ← δ(εi-1)-δ(εi) exp(εi)-exp(εi-1) -δ(εi)-δ(εi+1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": "exp(εi+1)-exp(εi)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": "4: P (-∞) ← 1 -j∈[k-1] P (ε j ) 5: Q(-∞) ← 0 6: for i = 1, . . . , k -1 do 7: Q(ε i ) ← exp(ε i )P (ε i ) 8: Q(∞) = δ(∞) 9: return (P, Q)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": "parameter. This is why we allow for X and Y to have different support in Algorithm 1, and why we make no assumptions on the distributions of (P, Q) or of (X, Y ) in the proof for Theorem 3.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 Constructing Discrete Dominating Pairs and their PLRVs", "sec_num": null}, {"text": "In this section, we provide several observations on the trade-off curve of discrete dominating pairs. In particular, these observations hold for the trade-off curve described Theorem 3.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Some Properties of the Trade-Off Curves of Discrete Dominating Pairs", "sec_num": null}, {"text": "Connecting the Dots. From the proof of Theorem 3.3 (see Appendix E.2), we know that when the level α happens to equal a point in the reverse CDF of X, i.e. when α = Pr[X > x i ] for some i, that the corresponding FNR T (P, Q)(α) is simply the CDF of Y evaluated at the same point, i.e.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Some Properties of the Trade-Off Curves of Discrete Dominating Pairs", "sec_num": null}, {"text": "T (P, Q)(α) = Pr[Y ≤ x i ].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Some Properties of the Trade-Off Curves of Discrete Dominating Pairs", "sec_num": null}, {"text": "Since the reverse CDF of X can take on k + 1 values, it follows that there are k + 1 values of α where the trade-off curve is fully characterized by the CDF of the PLRVs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Some Properties of the Trade-Off Curves of Discrete Dominating Pairs", "sec_num": null}, {"text": "Next, we observe a special structure of the trade-off curve on the points outside of these k + 1 values. For fixed τ , Eq. ( 34) implies α * (τ, γ) is increasing linearly in γ and Eq. (37) implies β * (τ, γ) is decreasing linearly in γ. This implies that the trade-off curve \"in between\" the k + 1 points that correspond to the CDFs of the PLRVs is a linear interpolation, where one \"connects the dots\". Hence, the trade-off curve is piece-wise linear, continuous everywhere, and not differentiable at the k + 1 points where α happens to be on the reverse CDF of X.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Some Properties of the Trade-Off Curves of Discrete Dominating Pairs", "sec_num": null}, {"text": "This observation provides an interesting connection to <PERSON><PERSON><PERSON><PERSON> et al. (2022) , who showed that \"connecting the dots\" between finite points on the privacy profile δ(e ε ) || yields a valid pessimistic estimate to the privacy profile. Could \"connecting the dots\" in trade-off curve space also yield a valid pessimistic estimate? The answer is clearly no: \"connecting the dots\" on finite samples from a tradeoff curve corresponds to an optimistic bound on the trade-off curve. Nevertheless, it is interesting to note that the class of discrete and finitely supported privacy loss random variables simultaneously achieve a pessimistic bound in privacy profile space and an optimistic bound in trade-off curve space. Further exploration of this phenomena, specifically in the context of constructing optimal optimistic privacy estimates, is left as future work.", "cite_spans": [{"start": 55, "end": 79, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "D.2 Some Properties of the Trade-Off Curves of Discrete Dominating Pairs", "sec_num": null}, {"text": "Behavior at the Edges. The trade-off curve of discrete dominating (P, Q) in general does not satisfy T (P, Q)(0) = 1. Indeed, the point α = 0 corresponds to τ = x max and γ = 0, in which case", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Some Properties of the Trade-Off Curves of Discrete Dominating Pairs", "sec_num": null}, {"text": "T (P, Q)(0) = Pr[Y ≤ x max ] = 1 -Pr[Y > x max ].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Some Properties of the Trade-Off Curves of Discrete Dominating Pairs", "sec_num": null}, {"text": "Whether or not this equals 1 depends on the details of the PLRV Y , though we note that in our experiments, T (P, Q)(0) is usually 1 to within a margin of 10 -10 . Moreover, we have that", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Some Properties of the Trade-Off Curves of Discrete Dominating Pairs", "sec_num": null}, {"text": "T (P, Q)(α) = 0 for any α ∈ [Pr[X > -∞], 1]. Indeed, for any α ∈ [Pr[X > -∞], 1], we have that τ = -∞, meaning that β * (τ, γ) = Pr[Y ≤ -∞] = 0 for any choice of γ.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Some Properties of the Trade-Off Curves of Discrete Dominating Pairs", "sec_num": null}, {"text": "The observation that T (P, Q)(0) ̸ = 1, that T (P, Q) is piece-wise linear, and that T (P, Q)(α) = 0 for any sufficiently large α, are all consistent with the findings of <PERSON> et al. (2023) , who characterized the trade-off curves of discrete-valued mechanisms. || The linear interpolation must be done in e ε space, as in this grid the privacy profile δ(e ε ) is convex.", "cite_spans": [{"start": 171, "end": 188, "text": "<PERSON> et al. (2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "D.2 Some Properties of the Trade-Off Curves of Discrete Dominating Pairs", "sec_num": null}, {"text": "First, let us define the notion of the convex conjugate that we use in the proofs. For a given function f : [0, 1] → [0, 1], its convex conjugate f * is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "f * (y) = sup 0≤x≤1 yx -f (x),", "eq_num": "(23)"}], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "Next, we can show the omitted proofs. Proposition 3.1. If (P, Q) is a dominating pair for a mechanism M , then for α ∈ [0, 1],", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "inf", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "S≃S ′ T (M (S), M (S ′ ))(α) ≥ T (P, Q)(α). (", "eq_num": "10"}], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "Proof. The proof follows from taking the convex conjugate of both sides of the following result from <PERSON> et al. (2022b) :", "cite_spans": [{"start": 101, "end": 119, "text": "<PERSON> et al. (2022b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "Proposition E.1 (Lemma 20 from <PERSON> et al. (2022b) restated in our notation). If a mechanism is (ε, D e ε (P ∥ Q))-DP, then it is f -DP for f such that the following holds:", "cite_spans": [{"start": 31, "end": 49, "text": "<PERSON> et al. (2022b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "D e ε (P ∥ Q) = 1 + f * (-e ε ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "Taking the convex conjugate of the equation above reveals that f follows exactly the structure of the trade-off curve implied by the Neyman-Pearson optimal test, which is exactly T (P, Q). See Appendix E.2.2 for more details on the Neyman-Pearson lemma.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "Proposition 3.3 (Cost of advantage calibration). Fix dataset size n > 1, and a target level of attack advantage η ⋆ ∈ (δ ⋆ , 1), where δ ⋆ = 1 /c•n for some c > 1. For any 0 < α < 1-η ⋆ 2 , there exists a DP mechanism for which the gap in FNR f standard (α) obtained with standard calibration for ε ⋆ that ensures η ≤ η ⋆ , and FNR f adv (α) obtained with advantage calibration is lower bounded:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∆β(α) ≜ f standard (α) -f adv (α) ≥ η ⋆ -δ ⋆ + 2α η ⋆ η ⋆ -1 . (", "eq_num": "15"}], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "Proof. Let us fix a pair of datasets S ≃ S ′ . Suppose that we have a mechanism M : 2 D → {0, 1, 2, 3} which satisfies (ε, δ)-DP. Further, assume that for the specific fixed pair S, S ′ it is defined as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "P (M (S) = 0) = 0 P (M (S ′ ) = 0) = δ P (M (S) = 1) = (1 -δ) • e ϵ e ϵ +1 P (M (S ′ ) = 1) = (1 -δ) • 1 e ϵ +1 P (M (S) = 2) = (1 -δ) • 1 e ϵ +1 P (M (S ′ ) = 2) = (1 -δ) • e ϵ e ϵ +1 P (M (S) = 3) = δ P (M (S ′ ) = 3) = 0 (24)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "The defining feature of this mechanism is that its trade-off curve T (M (S), M (S ′ )) for S, S ′ exactly matches the f (•) curve for generic (ε, δ)-DP mechanisms in Eq. ( 5) (<PERSON> et al., 2015) . Thus, for this mechanism we can use f and T (M (S), M (S ′ )) interchangeably. In the rest of the proof, we assume that we are calibrating this mechanism.", "cite_spans": [{"start": 175, "end": 197, "text": "(<PERSON><PERSON><PERSON> et al., 2015)", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "We want to derive (1) f standard under standard calibration with δ ⋆ = 1 /c•n and ε ⋆ chosen such that we have η ≤ η ⋆ , (2) f adv under advantage calibration for ensuring η ⋆ , and find their difference.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "For this, we first solve Eq. ( 8) for ε to derive the corresponding ε ⋆ that would satisfy the required level of η ⋆ under standard calibration with δ ⋆ = 1 c•n :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ε ⋆ = log Å 2δ ⋆ -η ⋆ -1 η ⋆ -1 ã (", "eq_num": "25"}], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "As we are interested in the low α regime, let us only consider the following form of the DP trade-off curve from Proposition 2.1:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "f (α) = 1 -δ -e ε α.", "eq_num": "(26)"}], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "It is easy to verify that this this form holds for 0 ≤ α ≤ 1-δ 1+e ε . In the case of (ε ⋆ , δ ⋆ )-DP with ε ⋆ defined by Eq. ( 25), a simple computation shows that this holds for 0 ≤ α ≤ 1-η ⋆ 2 . To get f standard , we plug (ε ⋆ , δ ⋆ ) into the form in Eq. ( 26). Recall that by Eq. ( 8) advantage calibration for generic DP mechanisms is equivalent to calibrating noise to (0, η ⋆ )-DP. Thus, to get f adv (α), we plug into ε = 0, δ = η ⋆ to Eq. ( 26). Subtracting the two, we get:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∆β = η ⋆ -δ ⋆ + 2α η ⋆ -δ ⋆ η ⋆ 1 , (", "eq_num": "27"}], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "from which we get the sought form.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "Proposition 3.5. Given PLRVs (X ω , Y ω ) of a discrete-valued dominating pair of a mechanism M ω (•), choosing ω * using Eq. ( 18) and Algorithm 1 to compute", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "f ω (α) ensures f ω * (α ⋆ ) ≥ β ⋆ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "Proof. Observe that Algorithm 1 computes the intermediate values of τ and γ considered in the four cases of α values in the proof of Theorem 3.3 given in Appendix E.2, and thus computes the valid trade-off curve T (P, Q)(α) as defined in Eq. ( 12). By Proposition 3.1, M ω (•) satisfies f -DP with f = T (P, Q).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "E.2 Proof of Theorem 3.3 Theorem 3.3 (Accounting for advantage and f -DP with PLRVs). Suppose that a mechanism M (•) has a discrete-valued dominating pair (P, Q) with associated PLRVs (X, Y ). The attack advantage η for this mechanism is bounded:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "η ≤ Pr[Y > 0] -Pr[X > 0].", "eq_num": "(11)"}], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "Moreover, for any τ ∈ R ∪ {∞, -∞} and γ ∈ [0, 1], define", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "β * (τ, γ) = Pr[Y ≤ τ ] -γ Pr[Y = τ ].", "eq_num": "(12)"}], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "For any level α ∈ [0, 1], choosing τ = (1 -α)-quantile of X and γ = α-Pr [X>τ ] Pr [X=τ ] guarantees that T (P, Q)(α) = β * (τ, γ).", "cite_spans": [{"start": 83, "end": 89, "text": "[X=τ ]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "Eq. ( 11) is an implication of a result by <PERSON><PERSON> et al. (2021) , which states:", "cite_spans": [{"start": 43, "end": 61, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF64"}], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "δ(ε) = Pr[Y > ε] -e ε Pr[X > ε].", "eq_num": "(28)"}], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "We get Eq. ( 11) by observing that (0, δ)-DP bounds η ≤ δ from Proposition 2.2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "In the remainder of the proof, we show Eq. ( 12) and why choosing the threshold τ and coin flip probability γ in the way specified in the theorem guarantees T (P, Q)(α) = β(τ, γ). In Appendix E.2.1, we establish the notation necessary for the remainder of the proof along with all the assumptions made. In Appendix E.2.2, we introduce the Neyman-Pearson lemma and use it to construct Eq. ( 12). Finally, in Appendix E.2.3, we prove the final statement of the theorem.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Omitted Proofs E.1 Omitted Proofs in Section 3", "sec_num": null}, {"text": "Let the domain of (P, Q) be O, which we assume to be countable. We refer to the probability mass function of P as P (•) and similarly for Q. We allow for multiple atoms o where P (o) > 0 and Q(o) = 0, and also multiple atoms o ′ where Q(o ′ ) > 0 and P (o ′ ) = 0. We make no further assumptions on (P, Q).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.1 Setup, Notation, and Assumptions", "sec_num": null}, {"text": "Since (P, Q) dominate the mechanism M (•), we know from Proposition 3.1 that the hypothesis test:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.1 Setup, Notation, and Assumptions", "sec_num": null}, {"text": "H 0 : o ∼ P, H 1 : o ∼ Q (29)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.1 Setup, Notation, and Assumptions", "sec_num": null}, {"text": "is easier (the trade-off curve is less than or equal to) that the standard DP hypothesis test:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.1 Setup, Notation, and Assumptions", "sec_num": null}, {"text": "H 0 : θ ∼ M (S), H 1 : θ ∼ M (S ′ ) (30)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.1 Setup, Notation, and Assumptions", "sec_num": null}, {"text": "for all S ≃ S ′ . In Appendix E.2.2, we use the Neyman-<PERSON> Lemma to tightly characterize the trade-off curve implied by ( 29). The notion of privacy loss random variables (PLRVs) (X, Y ), which were defined in Def. 3.2 as Y ≜ log Q(o) /P (o) with o ∼ Q, and X ≜ log Q(o ′ ) /P (o ′ ) with o ′ ∼ P , appear naturally and play a central role in the proof.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.1 Setup, Notation, and Assumptions", "sec_num": null}, {"text": "As such, we establish more notation on them. Let T denote the finite values that the PLRVs can take", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.1 Setup, Notation, and Assumptions", "sec_num": null}, {"text": "T = { log Q(o) /P (o) | o ∈ O, P (o) > 0, Q(o) > 0}.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.1 Setup, Notation, and Assumptions", "sec_num": null}, {"text": "We let the support of <PERSON> be", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.1 Setup, Notation, and Assumptions", "sec_num": null}, {"text": "X = ® {-∞} ∪ T if sup T ∈ T {-∞} ∪ T ∪ T } otherwise.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.1 Setup, Notation, and Assumptions", "sec_num": null}, {"text": "and we set Pr[X = sup T ] = 0 if we manually append sup T to X. We do this to make the quantile of X well-defined on all countable domains. Moreover, let x max = sup X = sup T . We will often refer to elements in the support of X via X = {-∞, x 1 , x 2 , . . . , x max }.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.1 Setup, Notation, and Assumptions", "sec_num": null}, {"text": "According to the Neyman-Pearson Lemma (see, e.g., <PERSON><PERSON> and <PERSON>, 2006; <PERSON> et al., 2022) , the most powerful attack at level α for the hypothesis test ( 29) is a threshold test ϕ", "cite_spans": [{"start": 50, "end": 76, "text": "<PERSON><PERSON> and <PERSON>., 2006;", "ref_id": "BIBREF37"}, {"start": 77, "end": 95, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "E.2.2 Applying the Neyman-Pearson Lemma", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "* : O → [0, 1] parameterized by two numbers τ ∈ R ∪ {-∞, ∞}, γ ∈ [0, 1], ϕ * τ,γ (o) =    1 if Q(o) > e τ P (o) γ if Q(o) = e τ P (o) 0 if Q(o) < e τ P (o).", "eq_num": "(31)"}], "section": "E.2.2 Applying the Neyman-Pearson Lemma", "sec_num": null}, {"text": "which we can equivalently write as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.2 Applying the Neyman-Pearson Lemma", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ϕ * τ,γ (o) =      1 if log Q(o) P (o) > τ γ if log Q(o) P (o) = τ 0 if log Q(o) P (o) < τ.", "eq_num": "(32)"}], "section": "E.2.2 Applying the Neyman-Pearson Lemma", "sec_num": null}, {"text": "This threshold test works by flipping a coin and rejecting the null hypothesis (equivalently, guessing that o came from Q) with probability ϕ * τ,γ (o). Here, log Q(o) P (o) is the Neyman-Pearson test statistic, and τ is the threshold for this test statistic. If the test statistic is less (greater) than the threshold, the test always rejects (accepts) the null hypothesis, and if the test statistic equals the threshold, the test flips a coin with probability γ to reject the null hypothesis.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.2 Applying the Neyman-Pearson Lemma", "sec_num": null}, {"text": "The false positive rate of ϕ * τ,γ , which we denote by α, is the probability that the null hypothesis is rejected (ϕ * τ,γ > 0) when the null hypothesis is true (o ∼ P ), and has the following form:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.2 Applying the Neyman-Pearson Lemma", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "α * (τ, γ) ≜ E o∼P [ϕ * τ,γ (o)] (33) = Pr[X > τ ] + γ Pr[X = τ ].", "eq_num": "(34)"}], "section": "E.2.2 Applying the Neyman-Pearson Lemma", "sec_num": null}, {"text": "Similarly, the false negative rate of ϕ * τ,γ , which we denote β, is the probability that the null hypothesis is accepted (1 -ϕ * τ,γ > 0) when the null hypothesis is false (o ∼ Q), and has the following form:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.2 Applying the Neyman-Pearson Lemma", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "β * (τ, γ) ≜ 1 -E θ∼Q [ϕ * τ,γ (θ)] (35) = 1 -(Pr[Y > τ ] + γ Pr[Y = τ ]) (36) = Pr[Y ≤ τ ] -γ Pr[Y = τ ].", "eq_num": "(37)"}], "section": "E.2.2 Applying the Neyman-Pearson Lemma", "sec_num": null}, {"text": "We have thus shown the correctness of the construction of Eq. ( 12). In Appendix E.2.3, we prove the final statement in Theorem 3.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.2 Applying the Neyman-Pearson Lemma", "sec_num": null}, {"text": "The goal of this section is to prove the following statement made in Theorem 3.3:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "For any level α ∈ [0, 1], choosing τ = (1 -α)-quantile of X and γ = α-Pr[X>τ ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "Pr [X=τ ] guarantees that:", "cite_spans": [{"start": 3, "end": 9, "text": "[X=τ ]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "T (P, Q)(α) = β * (τ, γ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "where T (P, Q)(α) outputs the false negative rate of the most powerful attack at level α. From Appendix E.2.2, we know that the most powerful attack takes the form ϕ * τ,γ as defined in Eq. ( 32).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "One should think of the level α as a constraint on the attack ϕ * τ,γ . In particular, the constraint α * (τ, γ) = α (where α * is the false positive rate of ϕ * τ,γ and is defined in Eq. ( 33)) yields a family of possible tests that all achieve the level α. If (P, were continuous distributions, the constraint α * (τ, γ) = α would uniquely determine the optimal test. This does not hold in the discrete case, and hence we must identify the most powerful test within this family.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "Below, we list out 4 different regimes for the value of the level α, identify the family of possible tests in each regime and the most powerful test, and finally give the false negative rate of the respective most powerful test.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "1 Case α = 1: Recall that X has a finite probability of being -∞, meaning that the only way to have α * (τ, γ) = 1 is to set τ = -∞ and γ = 1. The corresponding false negative rate is given by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "β * (-∞, 1) = Pr[Y ≤ -∞] -Pr[Y = -∞] = 0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "2 Case α = 0: If we choose the threshold τ = x max and the coin flip probability γ = 0, then we have that the false positive rate of this test is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "α * (τ = x max , γ = 0) = Pr(X > x max ) + γ Pr[X = x max ] (38) = 0.", "eq_num": "(39)"}], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "Moreover, any test with τ > x max has α * (τ, γ) = 0. However, increasing the threshold above x max can never decrease β * . Moreover, a test with a threshold τ < x max cannot achieve α = 0. It follows that choosing (τ = x max , γ = 0) yields the most powerful test, which has a false negative rate of", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "β * (x max , 0) = Pr[Y ≤ x max ].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "3 Case α = Pr[X > x t ] for some x t ∈ X: If we choose the threshold τ = x t and coin flip probability γ = 0, then we have that the false positive rate of this test is", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "α * (τ = x t , γ = 0) = Pr(X > x t ) + 0 (40) = α.", "eq_num": "(41)"}], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "Moreover, the test ϕ * xt+1,1 and any test with τ ∈ (x t , x t+1 ) has α * (τ, γ) = α. It is straightforward to see that all these tests are equivalent to outputting 1 if log Q(o) P (o) > x t and 0 otherwise, making them all equivalent to ϕ * xt,0 . Note that no other test can achieve the level α, since decreasing the threshold below x t or above x t+1 makes it impossible to achieve level α. For fixed threshold τ = x t (x t+1 ), only a coin flip probability of γ = 0(1) achieves level α. We conclude that all the tests that achieve level α have a false negative rate of", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "β * = Pr[Y ≤ x t ].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "4 Otherwise: If we choose the threshold", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "τ = inf{x ∈ X | α ≥ Pr[X > x]}", "eq_num": "(42)"}], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "and choose the coin flip probability γ to exactly satisfy the constraint that α * (τ, γ) = α, i.e.,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "γ = α -Pr[X > x t ] Pr[X = x t ] ,", "eq_num": "(43)"}], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "then this test achieves a false positive rate of α. It is easy to see that this is the only test that achieves level α, and has a false negative rate of", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "β * = Pr[Y ≤ x t ] -γ Pr[Y = x t ].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "Note that in all regimes, there is one unique test that achieves a level α and is the most powerful test. However, in some regimes of α ∈ [0, 1], namely regime 3, there are many different parameterizations for the same test. In these cases, we are free to choose any parameterization. For each regime, the very first test we list is the parameterization we choose. To summarize, we have the following most powerful tests:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "1 when α = 1, choose τ = -∞, γ = 1 2 when α = 0, choose τ = x max , γ = 0 3 when α = Pr[X > x t ], choose τ = x t , γ = 0", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "4 else, choose τ via Eq. ( 42), and γ = α-Pr [X>τ ] Pr [X=τ ] .", "cite_spans": [{"start": 55, "end": 61, "text": "[X=τ ]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "It is clear from the list above that for distributions with finite support, the most powerful test can be concisely written as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "τ = inf{x ∈ X | α ≥ Pr[X > x]} (44) γ = α -Pr[X > τ ] Pr[X = τ ] .", "eq_num": "(45)"}], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "where we recognize τ as the (1 -α)-quantile of X.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "that for distributions with countably infinite support, Eq. ( 45) does not capture Case 2, since Pr[X = x max ] = 0. So, we define γ = 0 whenever α = 0, and γ = Eq. ( 45) otherwise. Since this work focuses on using PLRVs from <PERSON><PERSON><PERSON><PERSON> et al. (2022) , which are always finitely supported, we report Eq. ( 44) and Eq. ( 45) without this edge case in the main body.", "cite_spans": [{"start": 226, "end": 250, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "We remark that similar results regarding the trade-off curve between two discrete mechanisms can be found in <PERSON> et al. (2023) . We differ from this work by parameterizing the trade-off curve using PLRVs, in contrast to <PERSON> et al., who parameterized the trade-off curve in terms of the discrete distributions P and Q. Our parameterization lends itself more naturally to composition, as the PLRVs sum under composition.", "cite_spans": [{"start": 109, "end": 126, "text": "<PERSON> et al. (2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E.2.3 Construction of the Trade-Off Curve of a Dominating Pair", "sec_num": null}, {"text": "The algorithm of <PERSON><PERSON><PERSON><PERSON> et al. (2022) , which is implemented in the dp accounting Python library, ** handles Poisson subsampling under composition (i.e. accounting for DP-SGD) by analyzing the removal and add relations separately. This approach, to the authors knowledge, was first advocated for by <PERSON> et al. (2022b) (see the discussion in their Appendix).", "cite_spans": [{"start": 17, "end": 41, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF11"}, {"start": 303, "end": 321, "text": "<PERSON> et al. (2022b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "F Practical Considerations", "sec_num": null}, {"text": "In particular, instead of the algorithm outputting a dominating pair (P, Q) that dominates for the symmetric add/remove relation under composition, it outputs one dominating pair for the asymmetric remove relation (P remove , Q remove ) and one for the asymmetric add relation (P add , Q add ). This means that naively applying Theorem 3.3 to, for example, (P add , Q add ), will return a trade-off curve that is only valid for DP-SGD under the asymmetric add relation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Practical Considerations", "sec_num": null}, {"text": "To handle the case when Theorem 3.3 is applied to a dominating pair (P, Q) (equivalently, the PLRVS (X, Y )) that only dominate a mechanism under an asymmetric neighboring relation, a more sophisticated technique is needed to map T (P, Q) to the target symmetric neighboring relation. In particular, a result from (<PERSON> et al., 2022) explains how to handle this case: Proposition F.1 (Proposition F.2 from <PERSON> et al. (2022) ). Let f : [0, 1] → [0, 1] be a convex, continuous, non-increasing function with f (x) ≤ 1 -x for x ∈ [0, 1]. Suppose a mechanism M is (ε, 1 + f * (-e ε ))-DP for all ε ≥ 0, then it is Symm(f )-DP with the symmetrization operator Symm(f ) defined as:", "cite_spans": [{"start": 314, "end": 333, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF10"}, {"start": 406, "end": 424, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "F Practical Considerations", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Symm(f )(x) = ® {f, f -1 } * * , if x ≤ f (x), max{f, f -1 }, if x > f (x),", "eq_num": "(46)"}], "section": "F Practical Considerations", "sec_num": null}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Practical Considerations", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x = inf{x ∈ [0, 1] | : -1 ∈ ∂f (x)},", "eq_num": "and"}], "section": "F Practical Considerations", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "{f, f -1 } * * (x) =    f (x), if x ≤ x, f (x), if x < x ≤ f (x), f -1 (x), if x > f (x). (", "eq_num": "47"}], "section": "F Practical Considerations", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Practical Considerations", "sec_num": null}, {"text": "Though not explicitly stated, the proposition does assume the mechanism M (•) has a symmetric neighboring relation. By letting f be unspecified however, the proposition allows for the input function f to correspond to an asymmetric neighboring relation. In this case, the proposition returns a trade-off curve that holds for the symmetric neighboring relation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Practical Considerations", "sec_num": null}, {"text": "We can hence apply this proposition to the problem at hand by recalling that given a dominating pair (P, Q), we have that the mechanism is (ε, D e ε (P ∥ Q))-DP. Moreover, Theorem 3.3 outputs the tradeoff function f = T (P, Q), which is exactly the function f such that D e ε (P ∥ Q) = 1 + f * (-e ε ). We can thus restate Proposition F.1 in more familiar form as: ** https://github.com/google/differential-privacy/tree/main/python/dp accounting/dp accounting/pld Corollary G.3 (FPR/FNR calibration for GM). For a Gaussian mechanism M σ (S), and target α ⋆ ≥ 0, β ⋆ ≥ 0 such that α ⋆ + β ⋆ ≤ 1, choosing σ as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Practical Considerations", "sec_num": null}, {"text": "σ = ∆ 2 Φ -1 (1 α ⋆ ) -Φ -1 (β ⋆ ) (52)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Practical Considerations", "sec_num": null}, {"text": "ensures that adversary's FNR and FPR rates are lower bounded by α ⋆ and β ⋆ , respectively.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Practical Considerations", "sec_num": null}, {"text": "Note that using the exact expressions above to calibrate Gaussian mechanism offer only computational advantages compared the method in the main body. In terms of resulting noise scale σ, the results are the same as with generic PLRV-based calibration up to a numerical approximation error.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Practical Considerations", "sec_num": null}, {"text": "H Additional Experiments, Details, and Figures", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Practical Considerations", "sec_num": null}, {"text": "We use a commodity machine with AMD Ryzen 5 2600 six-core CPU, 16GB of RAM, and an Nvidia GeForce RTX 4070 GPU with 16GB of VRAM to run our experiments. All experiments with deep learning take up to four hours to finish.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "H.1 Computing Resources", "sec_num": null}, {"text": "In all our experimental results, the neighborhood relation S ≃ S ′ is the add-remove relation, i.e., S ≃ S ′ iff |S ∆ S ′ | = 1, which is the standard relation used by modern DP-SGD accountants. See more on implementation details related to the neighborhood relation in Appendix F.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "H.2 Experimental Setup", "sec_num": null}, {"text": "Text Sentiment Classification. We follow <PERSON> et al. (2021) to finetune a GPT-2 (small) (<PERSON><PERSON> et al., 2019) using LoRA (<PERSON> et al., 2021) with DP-SGD on the SST-2 sentiment classification task (<PERSON><PERSON> et al., 2013) from the GLUE benchmark (<PERSON> et al., 2018) . We use the Poisson subsampling probability p ≈ 0.004 corresponding to expected batch size of 256, gradient clipping norm of ∆ 2 = 1.0, and finetune for three epochs with LoRA of dimension 4 and scaling factor of 32. We vary the noise multiplier σ ∈ {0.5715, 0.6072, 0.6366, 0.6945, 0.7498} approximately corresponding to ε ∈ {3.95, 3.2, 2.7, 1.9, 1.45}, respectively, at δ = 10 -5 . We use the default training split of the SST-2 dataset containing 67,348 examples for finetuning, and the default validation split containing 872 examples as a test set.", "cite_spans": [{"start": 41, "end": 57, "text": "<PERSON> et al. (2021)", "ref_id": "BIBREF64"}, {"start": 86, "end": 108, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF50"}, {"start": 115, "end": 137, "text": "LoRA (Hu et al., 2021)", "ref_id": null}, {"start": 193, "end": 214, "text": "(<PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF53"}, {"start": 239, "end": 258, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF56"}], "ref_spans": [], "eq_spans": [], "section": "H.2 Experimental Setup", "sec_num": null}, {"text": "Image Classification. We follow <PERSON><PERSON><PERSON> and <PERSON><PERSON> (2021) to train a convolutional neural network (<PERSON><PERSON><PERSON> and <PERSON><PERSON>, 2021, Table 9, Appendix) over the ScatterNet features (Oyallon and Mallat, 2015) on the CIFAR-10 ( <PERSON><PERSON><PERSON><PERSON> et al., 2009) image classification dataset. We use the Poisson subsampling probability of p ≈ 0.16 corresponding to expected batch size of 8192, learning rate of 4, Nesterov momentum of 0.9, and gradient clipping norm of ∆ 2 = 0.1. We train for up to 100 epochs. We vary the gradient noise multiplier σ /∆2 ∈ {4, 5, 6, 8, 10}, corresponding to ε ∈ {5, 3.86, 3.15, 2.31, 1.63}, respectively, at δ = 10 -5 . We use the default 50K/10K train/test split of CIFAR-10.", "cite_spans": [{"start": 32, "end": 55, "text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON> (2021)", "ref_id": "BIBREF54"}, {"start": 96, "end": 139, "text": "(<PERSON><PERSON><PERSON> and <PERSON><PERSON>, 2021, Table 9, Appendix)", "ref_id": null}, {"start": 169, "end": 195, "text": "(Oyallon and Mallat, 2015)", "ref_id": null}, {"start": 214, "end": 238, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "H.2 Experimental Setup", "sec_num": null}, {"text": "Histogram release is a simple but common usage of DP, appearing as a building block, e.g., in private query interfaces (<PERSON><PERSON><PERSON><PERSON> et al., 2020) . To evaluate attack-aware noise calibration for histogram release, we use the well-known ADULT dataset (<PERSON> and <PERSON>, 1996) comprising a small set of US Census data. We simulate the release of the histogram of the 'Education' attribute (with 16 distinct values, e.g., \"High school\", \"Bachelor's\", etc.) using the standard Gaussian mechanism with post-processing to ensure that the counts are positive integers. To measure utility, we use the L 1 distance (error) between the original histogram and the released private histogram.", "cite_spans": [{"start": 119, "end": 142, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF18"}, {"start": 247, "end": 272, "text": "(<PERSON> and <PERSON>, 1996)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "H.3 Additional Experiments with Histogram Release", "sec_num": null}, {"text": "Figure 7 shows the increase in utility if we calibrate the noise of the mechanism using the direct calibration algorithm to a given level of FPR α ⋆ and FNR β ⋆ vs. standard calibration over 100 simulated releases with different random seeds. In certain cases, e.g., for α ⋆ = 0.1 and β ⋆ = 0.75, our approach decreases the error by approx. 3× from three erroneous counts on average to one.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "7", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "H.3 Additional Experiments with Histogram Release", "sec_num": null}, {"text": "We use the following key open-source software:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "H.4 Software", "sec_num": null}, {"text": "• <PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> et al., 2019) for implementing neural networks.", "cite_spans": [{"start": 10, "end": 31, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "H.4 Software", "sec_num": null}, {"text": "• huggingface (<PERSON> et al., 2019) suite of packages for training language models.", "cite_spans": [{"start": 14, "end": 33, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF59"}], "ref_spans": [], "eq_spans": [], "section": "H.4 Software", "sec_num": null}, {"text": "• opacus (<PERSON><PERSON><PERSON><PERSON> et al., 2021) for training PyTorch neural networks with • dp-transformers (<PERSON><PERSON><PERSON><PERSON> et al., 2022) for differentially private finetuning of language models. • numpy (<PERSON> et al., 2020) , scipy (<PERSON><PERSON><PERSON><PERSON> et al., 2020 (<PERSON><PERSON><PERSON><PERSON> et al., ), pandas (pandas development team, 2020)) , and jupyter (<PERSON><PERSON><PERSON><PERSON> et al., 2016) for numeric analyses. • seaborn (Waskom, 2021) for visualizations.", "cite_spans": [{"start": 9, "end": 34, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF63"}, {"start": 95, "end": 119, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF61"}, {"start": 186, "end": 207, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF21"}, {"start": 216, "end": 238, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020", "ref_id": "BIBREF55"}, {"start": 239, "end": 299, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., ), pandas (pandas development team, 2020))", "ref_id": null}, {"start": 314, "end": 336, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "H.4 Software", "sec_num": null}, {"text": "0 0.5 1 0 0.5 1 1 -δ 1-δ 1+e ε 1 -δ 1-δ 1+e ε Attack FPR, α Attack FNR, β", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "H.4 Software", "sec_num": null}, {"text": "Feasible region by DP Exact feasible region Figure 5: Trade-off curves of a Gaussian mechanism that satisfies (ε, δ)-DP. Each curve shows a boundary of the feasible region (greyed out) of possible membership inference attack FPR (α) and FNR (β) pairs. The solid curve shows the limit of the feasible region guaranteed by DP via Eq. ( 5), which is a conservative overestimate of attack success rates compared to the exact trade-off curve (dotted). The maximum advantage η is achieved with FPR and FNR at the point closest to the origin. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "H.4 Software", "sec_num": null}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "H.4 Software", "sec_num": null}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "H.4 Software", "sec_num": null}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "H.4 Software", "sec_num": null}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• It should be clear whether the error bar is the standard deviation or the standard error of the mean. Justification: Neither the research process itself nor the outcomes of the research carry significant potential for harm.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [Yes] Justification: The topic of our paper is concerned with a social issue of privacy in machine learning and statistical analyses, and our work aims to improve the state of the art in the area. Although our work is mostly technical, we take a broader look in Sections 1 and 5.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)? Answer: [NA] Justification: [NA] Guidelines:", "cite_spans": [{"start": 251, "end": 255, "text": "[NA]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort. 12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected? Answer: [Yes] Justification: We cite the dataset sources as well the sources for the key pieces of software used for the experimental evaluations and analyses in the main body and Appendix H. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}, {"text": "Answer: [NA]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}, {"text": "Justification: [NA] Guidelines:", "cite_spans": [{"start": 15, "end": 19, "text": "[NA]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}, {"text": "• The answer NA means that the paper does not release new assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}, {"text": "• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • At submission time, remember to anonymize your assets (if applicable). You can either create an anonymized URL or include an anonymized zip file.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}, {"text": "Question: For crowdsourcing experiments and research with human subjects, does the paper include the full text of instructions given to participants and screenshots, if applicable, as well as details about compensation (if any)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Crowdsourcing and Research with Human Subjects", "sec_num": "14."}, {"text": "Answer: [NA]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Crowdsourcing and Research with Human Subjects", "sec_num": "14."}, {"text": "Justification: [NA] Guidelines:", "cite_spans": [{"start": 15, "end": 19, "text": "[NA]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Crowdsourcing and Research with Human Subjects", "sec_num": "14."}, {"text": "• The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Including this information in the supplemental material is fine, but if the main contribution of the paper involves human subjects, then as much detail as possible should be included in the main paper. • According to the NeurIPS Code of Ethics, workers involved in data collection, curation, or other labor should be paid at least the minimum wage in the country of the data collector.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Crowdsourcing and Research with Human Subjects", "sec_num": "14."}, {"text": "Question: Does the paper describe potential risks incurred by study participants, whether such risks were disclosed to the subjects, and whether Institutional Review Board (IRB) approvals (or an equivalent approval/review based on the requirements of your country or institution) were obtained?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects", "sec_num": "15."}, {"text": "Answer: [NA]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects", "sec_num": "15."}, {"text": "Justification: [NA] Guidelines:", "cite_spans": [{"start": 15, "end": 19, "text": "[NA]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects", "sec_num": "15."}, {"text": "• The answer NA means that the paper does not involve crowdsourcing nor research with human subjects.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects", "sec_num": "15."}, {"text": "• Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects", "sec_num": "15."}, {"text": "† We use add relation in this exposition, i.e., S ≃ S ′ iff S ′ = S ∪ {z}, but our results hold for any relation. ‡ Note that sensitivity (TPR) is 1 -β and specificity (TNR) is 1 -α.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "§ <PERSON> et al. (2018) used posterior belief, which is equivalent to accuracy under uniform prior.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "¶ In practice, we need to additionally symmetrize the trade-off curve due to the implementation details of the add/remove neighborhood relation in <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2022) accountant. See Appendix F.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "The authors would like to thank <PERSON><PERSON><PERSON> for the helpful suggestions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "Proposition F.2 (Proposition F.2 from <PERSON> et al. (2022) restated) . Suppose that (P, Q) is a dominating pair for a mechanism M (•) under either the add or remove relation. Then, the mechanism is Symm(T (P, Q))-DP with respect to the add/remove relation.Proposition F.2 allows us to, for example, use a dominating pair for the asymmetric add to obtain a trade-off curve for the symmetric add/remove relation. Moreover, the operator <PERSON>ymm(T (P, Q)) turns out to be straightforward to implement in practice.Appendix E.2.3 details how to explicitly construct T (P, Q). It is well known that T (Q, P )(α) = T (P, Q) -1 (α), hence the order of (P, Q) can be easily swapped in Appendix E.2.3 to get the inverse function T (P, Q) -1 . The only obstacle remaining is in determining x = inf{x ∈ [0, 1] | : -1 ∈ ∂f (x)}. Due to the structure of T (P, Q), namely that it is a piece-wise linear function parameterized by Eq. ( 34) and Eq. ( 37), it turns out that the subdifferential ∂f (x) are of the form {e τ }, where τ are the allowable thresholds of the Neyman-<PERSON> lemma at level x identified in each of the 4 cases of the proof laid out in Appendix E.2.3. As an example, a unique threshold of -∞ at α = 1 implies that the derivative of T (P, Q) at α = 1 is 0, meaning the trade-off curve is flat there.", "cite_spans": [{"start": 38, "end": 66, "text": "<PERSON> et al. (2022) restated)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "This gives us all the information needed to implement the Symm operator.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "It follows that the constraint", "sec_num": null}, {"text": "In the case where the trade-off curve of the mechanism has a closed form, we can solve the calibration problems in Eqs. ( 13) and ( 18) exactly without resorting to the numerical procedures in Sections 3.1 and 3.2. Definition G.1. For a given non-private algorithm q : 2 D → R d , a Gaussian mechanism (GM) is defined as M (S) = q(S) + ξ, where ξ ∼ N (0, ∆ 2 • σ 2 • I d ) and ∆ 2 ≜ sup S≃S ′ ∥q(S) -q(S ′ )∥ 2 is the sensitivity of q(S).For the Gaussian mechanism, we can exactly compute the relevant adversary's error rates: Proposition G.1 (<PERSON> and <PERSON> (2018) ; <PERSON> et al. (2022) ). Suppose that M σ (S) is GM with sensitivity ∆ 2 and noise variance σ 2 . Denote by µ = ∆2 /σ and by Φ(t) the CDF of the standard Gaussian distribution N (0, 1). Then,• The mechanism satisfies (ε, δ)-DP if the following holds:• It satisfies f -DP with:With these closed-form expressions, we can solve the calibration problems exactly: Corollary G.2 (Advantage calibration for GM). For a GM M σ (S) and target η ⋆ > 0, choosing σ as:ensures that adversary's advantage is upper bounded by η ⋆ .Proof of Corollary G.2. It is sufficient to ensure (0, η ⋆ )-DP. Plugging in ε = 0 and δ = η ⋆ into Eq. ( 48), we have:from which we can derive µ2 By solving Eq. ( 49) for α, we also have an exact expression for calibrating to a given level of α ⋆ , β ⋆ : Justification: The theoretical results are within the standard setup of differential privacy detailed in Section 2.1.Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [{"start": 543, "end": 565, "text": "(<PERSON><PERSON> and <PERSON> (2018)", "ref_id": "BIBREF3"}, {"start": 568, "end": 586, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "G Calibrating Gaussian Mechanism", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?Answer: [Yes] Justification: We provide the detailed information on reproducing the experimental results in Appendix H. Moreover, we link the code along with the instructions for reproducing.Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [{"start": 271, "end": 276, "text": "[Yes]", "ref_id": null}, {"start": 1834, "end": 1837, "text": "(d)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? Answer: [Yes] Justification: We use common openly available benchmark datasets. We have published the code on the Github platform. Guidelines:• The answer NA means that paper does not include experiments requiring code.• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Justification: We provide the information on the machine learning details in the main body as well as in Appendix H. Guidelines:• The answer NA means that the paper does not include experiments.• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [No] Justification: In our setting, we can directly approximate the theoretical quantities of interest (i.e., the level of privacy) without the need for empirical statistical methods and the corresponding uncertainty estimation. For the empirically evaluated model accuracy values, we only use one seed in the main suite of experiments for computational reasons. In the additional experiments in Appendix H, we provide 95% confidence bands.Guidelines:• The answer NA means that the paper does not include experiments.• The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper.", "cite_spans": [{"start": 170, "end": 174, "text": "[No]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Deep learning with differential privacy", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Goodfellow", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Talwar", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2016, "venue": "Proceedings of the 2016 ACM SIGSAC conference on computer and communications security", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Deep learning with differential privacy. In Proceedings of the 2016 ACM SIGSAC conference on computer and communications security, 2016.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Revisiting the economics of privacy: Population statistics and confidentiality protection as public goods", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "Abowd", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Economics", "volume": "", "issue": "1/20", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Revisiting the economics of privacy: Population statistics and confidentiality protection as public goods. Economics, (1/20), 2015.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "The saddle-point method in differential privacy", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Flavio", "middle": [], "last": "Calmon", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "508--528", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. The saddle-point method in differential privacy. In International Conference on Machine Learning, pages 508-528. PMLR, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Improving the gaussian mechanism for differential privacy: Analytical calibration and optimal denoising", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yu-<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. Improving the gaussian mechanism for differential privacy: Analytical calibration and optimal denoising. In International Conference on Machine Learning. PMLR, 2018.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Reconstructing training data with informed adversaries", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "2022 IEEE Symposium on Security and Privacy (SP)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Reconstructing training data with informed adversaries. In 2022 IEEE Symposium on Security and Privacy (SP). IEEE, 2022.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Adult. UCI Machine Learning Repository", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1996, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"DOI": ["10.24432/C5XW20"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. Adult. UCI Machine Learning Repository, 1996. DOI: https://doi.org/10.24432/C5XW20.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Membership inference attacks from first principles", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Nasr", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "2022 IEEE Symposium on Security and Privacy (SP)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Membership inference attacks from first principles. In 2022 IEEE Symposium on Security and Privacy (SP), 2022.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Differentially private empirical risk minimization", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "Sarwate", "suffix": ""}], "year": 2011, "venue": "Journal of Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON>. Differentially private empirical risk minimization. Journal of Machine Learning Research, 2011.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Closed-form bounds for DP-SGD against record-level inference", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Shruti", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Santiago", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "33rd USENIX Security Symposium", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Closed-form bounds for DP-SGD against record-level inference. In 33rd USENIX Security Symposium (USENIX Security 2024), 2024.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "I need a better description\"': An investigation into user expectations for differential privacy", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["M"], "last": "Redmiles", "suffix": ""}], "year": 2021, "venue": "Proceedings of the 2021 ACM SIGSAC Conference on Computer and Communications Security", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. \"I need a better description\"': An investigation into user expectations for differential privacy. In Proceedings of the 2021 ACM SIGSAC Conference on Computer and Communications Security, 2021.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Gaussian differential privacy", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON> J", "middle": [], "last": "Su", "suffix": ""}], "year": 2022, "venue": "Journal of the Royal Statistical Society Series B: Statistical Methodology", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> differential privacy. Journal of the Royal Statistical Society Series B: Statistical Methodology, 2022.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Connect the dots: Tighter discrete approximations of privacy loss distributions", "authors": [{"first": "Vadym", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings on Privacy Enhancing Technologies", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Connect the dots: Tighter discrete approximations of privacy loss distributions. Proceedings on Privacy Enhancing Technologies, 2022.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Calibrating noise to sensitivity in private data analysis", "authors": [{"first": "<PERSON>", "middle": [], "last": "Dwork", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2006, "venue": "Proceedings of the Theory of Cryptography Conference", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Calibrating noise to sensitivity in private data analysis. In Proceedings of the Theory of Cryptography Conference, 2006.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "The algorithmic foundations of differential privacy", "authors": [{"first": "<PERSON>", "middle": [], "last": "Dwork", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "Foundations and Trends in Theoretical Computer Science", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, et al. The algorithmic foundations of differential privacy. Foundations and Trends in Theoretical Computer Science, 2014.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "That which we call private", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Song", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1908.03566"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. That which we call private. arXiv preprint arXiv:1908.03566, 2019.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Am i private and if so, how many? communicating privacy guarantees of differential privacy with risk communication formats", "authors": [{"first": "Saskia", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Voigt", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sörries", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Tschorsch", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the 2022 ACM SIGSAC Conference on Computer and Communications Security", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Am i private and if so, how many? communicating privacy guarantees of differential privacy with risk communication formats. In Proceedings of the 2022 ACM SIGSAC Conference on Computer and Communications Security, 2022.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Model inversion attacks that exploit confidence information and basic countermeasures", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>ha", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "R<PERSON>enpart", "suffix": ""}], "year": 2015, "venue": "Proceedings of the ACM SIGSAC conference on computer and communications security", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Model inversion attacks that exploit confidence information and basic countermeasures. In Proceedings of the ACM SIGSAC conference on computer and communications security, 2015.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "A programming framework for OpenDP", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hay", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. A programming framework for OpenDP. Manuscript, May, 2020.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Total variation with differential privacy: Tighter composition and asymptotic bounds", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "IEEE International Symposium on Information Theory (ISIT)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Total variation with differential privacy: Tighter composition and asymptotic bounds. In IEEE International Symposium on Information Theory (ISIT), 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Numerical composition of differential privacy", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Numerical composition of differential privacy. Advances in Neural Information Processing Systems (NeurIPS), 2021.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Array programming with NumPy", "authors": [{"first": "<PERSON>", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": ["<PERSON><PERSON><PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gommers", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Berg", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Picus", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>be", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Nature", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Array programming with NumPy. Nature, 2020.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Bounding training data reconstruction in DP-SGD", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Bounding training data reconstruction in DP-SGD. Advances in Neural Information Processing Systems, 2024.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Differential privacy: An economic method for choosing epsilon", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "2014 IEEE 27th Computer Security Foundations Symposium", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Differential privacy: An economic method for choosing epsilon. In 2014 IEEE 27th Computer Security Foundations Symposium, 2014.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Lora: Low-rank adaptation of large language models", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuanzhi", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weizhu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, et al. Lora: Low-rank adaptation of large language models. In International Conference on Learning Representations, 2021.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Provable membership inference privacy", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Jinsung", "middle": [], "last": "<PERSON>on", "suffix": ""}, {"first": "Sercan", "middle": ["O"], "last": "Arik", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Provable membership inference privacy. Transactions on Machine Learning Research, 2024.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Evaluating differentially private machine learning in practice", "authors": [{"first": "Bargav", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "28th USENIX Security Symposium (USENIX Security 19)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. Evaluating differentially private machine learning in practice. In 28th USENIX Security Symposium (USENIX Security 19), 2019.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Revisiting membership inference under realistic assumptions", "authors": [{"first": "Bargav", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings on Privacy Enhancing Technologies", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Re- visiting membership inference under realistic assumptions. Proceedings on Privacy Enhancing Technologies, 2021.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Breaking the communication-privacy-accuracy tradeoff with f-differential privacy", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zhonggen", "middle": [], "last": "Su", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Zhaoyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Quek", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Dai", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Breaking the communication-privacy-accuracy tradeoff with f-differential privacy. Advances in Neural Information Processing Systems (NeurIPS), 2023.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "The composition theorem for differential privacy", "authors": [{"first": "<PERSON>", "middle": [], "last": "Kai<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Oh", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. The composition theorem for differential privacy. In International Conference on Machine Learning. PMLR, 2015.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Bounding data reconstruction attacks with the hypothesis testing interpretation of differential privacy", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ziller", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.03928"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Bounding data recon- struction attacks with the hypothesis testing interpretation of differential privacy. arXiv preprint arXiv:2307.03928, 2023a.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Optimal privacy guarantees for a relaxed threat model: Addressing sub-optimal adversaries in differentially private machine learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ziller", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Optimal privacy guarantees for a relaxed threat model: Addressing sub-optimal adversaries in differentially private machine learning. Advances in Neural Information Processing Systems (NeurIPS), 2023b.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "<PERSON>, and <PERSON><PERSON><PERSON> development team. Jupyter notebooks -a publishing format for reproducible computational workflows", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Grout", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Damián", "middle": [], "last": "A<PERSON>", "suffix": ""}, {"first": "Safia", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Positioning and Power in Academic Publishing: Players, Agents and Agendas", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> development team. Jupyter notebooks -a publishing format for reproducible computational workflows. In Positioning and Power in Academic Publishing: Players, Agents and Agendas. IOS Press, 2016.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Learning multiple layers of features from tiny images", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, et al. Learning multiple layers of features from tiny images. Technical report, 2009.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Disparate vulnerability to membership inference attacks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Carmel<PERSON>", "middle": [], "last": "Troncoso", "suffix": ""}], "year": 2022, "venue": "Proceedings on Privacy Enhancing Technologies", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Disparate vulnerability to membership inference attacks. Proceedings on Privacy Enhancing Technologies, 2022a.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "What you see is what you get: Principled deep learning via distributional generalization", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Yao-Yuan", "suffix": ""}, {"first": "Yaodong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jaroslaw", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Preetum", "middle": [], "last": "Blasiok", "suffix": ""}, {"first": "", "middle": [], "last": "Nakkiran", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Blas<PERSON>k, and <PERSON><PERSON><PERSON> Nakkiran. What you see is what you get: Principled deep learning via distributional generalization. Advances in Neural Information Processing Systems (NeurIPS), 2022b.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Gaussian membership inference privacy", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Gaussian membership inference privacy. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Testing statistical hypotheses", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Romano", "suffix": ""}], "year": 2006, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Testing statistical hypotheses. Springer Science & Business Media, 2006.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Generalization techniques empirically outperform differential privacy against membership inference", "authors": [{"first": "Jiaxiang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2110.05524"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Generalization techniques empirically outperform differential privacy against membership inference. arXiv preprint arXiv:2110.05524, 2021.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Optimal membership inference bounds for adaptive composition of sampled gaussian mechanisms", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Cormode", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>ha", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2204.06106"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Optimal membership inference bounds for adaptive composition of sampled gaussian mechanisms. arXiv preprint arXiv:2204.06106, 2022.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Towards explaining epsilon: A worst-case study of differential privacy risks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Saskia", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Voigt", "suffix": ""}, {"first": "", "middle": [], "last": "Tschorsch", "suffix": ""}], "year": null, "venue": "2021 IEEE European Symposium on Security and Privacy Workshops (EuroS&PW)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Towards explaining epsilon: A worst-case study of differential privacy risks. In 2021 IEEE European Symposium on Security and Privacy Workshops (EuroS&PW), 2021.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Visualizing privacy-utility trade-offs in differentially private data releases", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xi", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings on Privacy Enhancing Technologies", "volume": "2", "issue": "", "pages": "601--618", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Visualizing privacy-utility trade-offs in differentially private data releases. Proceedings on Privacy Enhancing Technologies, 2:601-618, 2022.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "What are the chances? explaining the epsilon parameter in differential privacy", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "Smart", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["M"], "last": "Redmiles", "suffix": ""}], "year": null, "venue": "32nd USENIX Security Symposium", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. What are the chances? explaining the epsilon parameter in differential privacy. In 32nd USENIX Security Symposium (USENIX Security 23), 2023.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Adversary instantiation: Lower bounds for differentially private machine learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Nasr", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Papernot", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "IEEE Symposium on security and privacy (SP)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Adversary instantiation: Lower bounds for differentially private machine learning. In IEEE Symposium on security and privacy (SP), 2021.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Tight auditing of differentially private machine learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Nasr", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "32nd USENIX Security Symposium", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Tight auditing of differentially private machine learning. In 32nd USENIX Security Symposium (USENIX Security 23), 2023.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Guidelines for evaluating differential privacy guarantees", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Howart<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Guidelines for evaluating differential privacy guarantees. National Institute of Standards and Technology, Tech. Rep, 2023.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Redrawing the boundaries on purchasing data from privacy-sensitive individuals", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "Proceedings of the conference on Innovations in theoretical computer science", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Redrawing the boundaries on purchasing data from privacy-sensitive individuals. In Proceedings of the conference on Innovations in theoretical computer science, 2014.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Deep roto-translation scattering for object classification", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Oyallon", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Mallat", "suffix": ""}], "year": 2020, "venue": "Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, 2015. The pandas development team. pandas-dev/pandas: Pandas", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>. Deep roto-translation scattering for object classification. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, 2015. The pandas development team. pandas-dev/pandas: Pandas, 2020.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Pytorch: An imperative style, high-performance deep learning library", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "Gross", "suffix": ""}, {"first": "Francisco", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Natalia", "middle": [], "last": "Gimelshein", "suffix": ""}, {"first": "Luca", "middle": [], "last": "Antiga", "suffix": ""}], "year": 2019, "venue": "Advances in Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Pytorch: An imperative style, high-performance deep learning library. In Advances in Neural Information Processing Systems (NeurIPS), 2019.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "How to DP-fy ML: A practical guide to machine learning with differential privacy", "authors": [{"first": "Natalia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": ["<PERSON>"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Vassilvitskii", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2023, "venue": "Journal of Artificial Intelligence Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. How to DP-fy ML: A practical guide to machine learning with differential privacy. Journal of Artificial Intelligence Research, 2023.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Language models are unsupervised multitask learners", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Child", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "OpenAI blog", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Language models are unsupervised multitask learners. OpenAI blog, 2019.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "On the difficulty of membership inference attacks", "authors": [{"first": "Shahbaz", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Xin", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>. On the difficulty of membership inference attacks. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, 2021.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Membership inference attacks against machine learning models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Congzheng", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "IEEE symposium on security and privacy", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Membership inference attacks against machine learning models. In IEEE symposium on security and privacy (SP). IEEE, 2017.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Recursive deep models for semantic compositionality over a sentiment treebank", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Potts", "suffix": ""}], "year": 2013, "venue": "Proceedings of the Conference on Empirical Methods in Natural Language Processing", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Recursive deep models for semantic compositionality over a sentiment treebank. In Proceedings of the Conference on Empirical Methods in Natural Language Processing, 2013.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Differentially private learning needs better features (or much more data)", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bone<PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. Differentially private learning needs better features (or much more data). In International Conference on Learning Representations, 2021.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "<PERSON>, <PERSON>, and SciPy 1.0 Contributors. SciPy 1.0: Fundamental Algorithms for Scientific Computing in Python", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gommers", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Haberland", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "İlhan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Polat", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>p<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Ribeiro", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and SciPy 1.0 Contributors. SciPy 1.0: Fundamental Algorithms for Scientific Computing in Python. Nature Methods, 2020.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Glue: A multi-task benchmark and analysis platform for natural language understanding", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hill", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the 2018 EMNLP Workshop BlackboxNLP: Analyzing and Interpreting Neural Networks for NLP", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Glue: A multi-task benchmark and analysis platform for natural language understanding. In Proceedings of the 2018 EMNLP Workshop BlackboxNLP: Analyzing and Interpreting Neural Networks for NLP, 2018.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "seaborn: statistical data visualization", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Waskom", "suffix": ""}], "year": null, "venue": "Journal of Open Source Software", "volume": "6", "issue": "60", "pages": "", "other_ids": {"DOI": ["10.21105/joss.03021"]}, "num": null, "urls": [], "raw_text": "<PERSON>. seaborn: statistical data visualization. Journal of Open Source Software, 6(60), 2021. doi: 10.21105/joss.03021. URL https://doi.org/10.21105/joss.03021.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "A statistical framework for differential privacy", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2010, "venue": "Journal of the American Statistical Association", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. A statistical framework for differential privacy. Journal of the American Statistical Association, 2010.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Huggingface's transformers: State-of-the-art natural language processing", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Debut", "suffix": ""}, {"first": "Victor", "middle": [], "last": "San<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Delangue", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Cistac", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1910.03771"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Huggingface's transformers: State-of-the-art natural language processing. arXiv preprint arXiv:1910.03771, 2019.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Differential privacy: A primer for a non-technical audience", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bembenek", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>un", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Vand. J. Ent. & Tech. L", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Differential privacy: A primer for a non-technical audience. Vand. J. Ent. & Tech. L., 2018.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "dp-transformers: Training transformer models with differential privacy", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>, and <PERSON>. dp-transformers: Training transformer models with differential privacy. https://www.microsoft.com/en-us/research/project/ dp-transformers, August 2022.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Privacy risk in machine learning: Analyzing the connection to overfitting", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>ha", "suffix": ""}], "year": 2018, "venue": "2018 IEEE 31st Computer Security Foundations Symposium (CSF)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Privacy risk in machine learning: Analyzing the connection to overfitting. In 2018 IEEE 31st Computer Security Foundations Symposium (CSF). IEEE, 2018.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Opacus: User-friendly differential privacy library in PyTorch", "authors": [{"first": "Ashkan", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Testuggine", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Malek", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ghosh", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Cormode", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2109.12298"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Opacus: User-friendly differential privacy library in PyTorch. arXiv preprint arXiv:2109.12298, 2021.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Differentially private fine-tuning of language models", "authors": [{"first": "Da", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Arturs", "middle": [], "last": "Backurs", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, et al. Differentially private fine-tuning of language models. In International Conference on Learning Representations, 2021.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "Sharp composition bounds for gaussian differential privacy via <PERSON><PERSON> expansion", "authors": [{"first": "Qinqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Su", "suffix": ""}], "year": 2020, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Sharp composition bounds for gaussian differential privacy via Edgeworth expansion. In International Conference on Machine Learning. PMLR, 2020.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "Optimal accounting of differential privacy via characteristic function", "authors": [{"first": "Yuqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yu-<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of The 25th International Conference on Artificial Intelligence and Statistics", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Optimal accounting of differential privacy via char- acteristic function. In Proceedings of The 25th International Conference on Artificial Intelligence and Statistics, 2022a.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "Optimal accounting of differential privacy via characteristic function", "authors": [{"first": "Yuqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yu-<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Artificial Intelligence and Statistics", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Optimal accounting of differential privacy via characteristic function. In International Conference on Artificial Intelligence and Statistics. PMLR, 2022b.", "links": null}}, "ref_entries": {"FIGREF0": {"fig_num": "1", "uris": null, "num": null, "text": "Figure 1: Test accuracy (x-axis) of a privately finetuned GPT-2 on SST-2 text sentiment classification dataset (top) and a convolutional neural network on CIFAR-10 image classification dataset (bottom).The DP noise is calibrated to guarantee at most a certain level of privacy attack sensitivity (y-axis) at three possible attack false-positive rates α ∈ {0.01, 0.05, 0.1}. See Section 4 for details.", "type_str": "figure"}, "FIGREF1": {"fig_num": null, "uris": null, "num": null, "text": "Optimal calibration for advantage comes with a pitfall: it allows for ∆β higher attack power in the low FPR regime compared to standard calibration.", "type_str": "figure"}, "FIGREF2": {"fig_num": "2", "uris": null, "num": null, "text": "Figure 2: Benefits and pitfalls of advantage calibration.", "type_str": "figure"}, "FIGREF4": {"fig_num": "6", "uris": null, "num": null, "text": "Figure6: The increase in attack sensitivity due to calibration for advantage is less drastic for Gaussian mechanism than for a generic (ε, δ)-DP mechanism.", "type_str": "figure"}, "FIGREF5": {"fig_num": "7", "uris": null, "num": null, "text": "Figure 7: Direct calibration to attack FNR/FPR reduces average L 1 error in histogram release with Gaussian mechanism. The confidence bands are 95% CI over 100 simulated releases.", "type_str": "figure"}, "TABREF1": {"html": null, "num": null, "text": "Calibration to attack TPR (i.e., 1-FNR) significantly reduces the noise scale in low FPR regimes. Unlike calibration for attack advantage, this approach does not come with a deterioration of privacy for low FPR, as it directly targets this regime.", "content": "<table><tr><td/><td/><td colspan=\"3\">Attack FPR, α = 0.01</td><td/><td/><td colspan=\"3\">Attack FPR, α = 0.05</td><td/><td/><td colspan=\"3\">Attack FPR, α = 0.1</td></tr><tr><td/><td>1.0</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>Noise scale, σ</td><td>0.6 0.8</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td>Method Standard calibration TPR/FPR calibration</td></tr><tr><td/><td>0.4</td><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td/><td>0.1</td><td>0.2</td><td>0.3</td><td>0.4</td><td>0.5</td><td>0.1</td><td>0.2</td><td>0.3</td><td>0.4</td><td>0.5</td><td>0.1</td><td>0.2</td><td>0.3</td><td>0.4</td><td>0.5</td></tr><tr><td/><td/><td colspan=\"3\">Attack TPR, 1 -β</td><td/><td/><td colspan=\"3\">Attack TPR, 1 -β</td><td/><td/><td colspan=\"3\">Attack TPR, 1 -β</td></tr><tr><td colspan=\"5\">0.2 Task accuracy = 0.57 0.4 0.6 0.8 Figure 3: 0.0 0.0 0.2 0.8 1.0 0.4 0.6 Attack FNR, β</td><td>1.0</td><td>0.0</td><td colspan=\"3\">0.2 Task accuracy = 0.66 0.4 0.6 0.8</td><td>1.0</td><td>0.0</td><td colspan=\"4\">0.2 Task accuracy = 0.7 0.4 0.6 0.8</td><td>1.0</td><td>Method (ε, δ)-DP Ours (Algorithm 1)</td></tr><tr><td/><td/><td colspan=\"3\">Attack FPR, α</td><td/><td/><td colspan=\"3\">Attack FPR, α</td><td/><td/><td/><td colspan=\"2\">Attack FPR, α</td></tr></table>", "type_str": "table"}, "TABREF2": {"html": null, "num": null, "text": "Notation summary", "content": "<table><tr><td>Symbol</td><td>Description</td><td>Reference</td></tr><tr><td>z ∈ D</td><td>Data record</td><td/></tr><tr><td>S ∈ 2 D</td><td>Dataset of records</td><td/></tr><tr><td>S ≃ S ′</td><td>Adjacency relation of neighboring datasets</td><td/></tr><tr><td>M</td><td/><td/></tr></table>", "type_str": "table"}, "TABREF4": {"html": null, "num": null, "text": "The answer NA means that the abstract and introduction do not include the claims made in the paper.• The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.2. LimitationsQuestion: Does the paper discuss the limitations of the work performed by the authors? Answer: [Yes] Justification: Section 3.1 discusses in detail the limitations of advantage calibration. Section 5 discusses limitations and future work for the whole paper.", "content": "<table><tr><td>NeurIPS Paper Checklist</td></tr><tr><td>1. Claims</td></tr><tr><td>Question: Do the main claims made in the abstract and introduction accurately reflect the</td></tr><tr><td>paper's contributions and scope?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: Claims in the abstract/intro succinctly represent the claims in the main body.</td></tr><tr><td>Guidelines:</td></tr><tr><td>•</td></tr></table>", "type_str": "table"}, "TABREF5": {"html": null, "num": null, "text": "• It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified.• For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "content": "<table><tr><td>8. Experiments Compute Resources</td></tr><tr><td>Question: For each experiment, does the paper provide sufficient information on the com-</td></tr><tr><td>puter resources (type of compute workers, memory, time of execution) needed to reproduce</td></tr><tr><td>the experiments?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: Our experiments only require commodity hardware. We detail the requirements</td></tr><tr><td>in Appendix H.</td></tr><tr><td>Guidelines:</td></tr><tr><td>•</td></tr></table>", "type_str": "table"}}}}