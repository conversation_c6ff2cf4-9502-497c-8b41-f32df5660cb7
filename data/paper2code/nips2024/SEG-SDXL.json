{"paper_id": "SEG-SDXL", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:29:19.799172Z"}, "title": "Smoothed Energy Guidance: Guiding Diffusion Models with Reduced Energy Curvature of Attention", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Washington", "location": {}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Conditional diffusion models have shown remarkable success in visual content generation, producing high-quality samples across various domains, largely due to classifier-free guidance (CFG). Recent attempts to extend guidance to unconditional models have relied on heuristic techniques, resulting in suboptimal generation quality and unintended effects. In this work, we propose Smoothed Energy Guidance (SEG), a novel training-and condition-free approach that leverages the energybased perspective of the self-attention mechanism to enhance image generation. By defining the energy of self-attention, we introduce a method to reduce the curvature of the energy landscape of attention and use the output as the unconditional prediction. Practically, we control the curvature of the energy landscape by adjusting the Gaussian kernel parameter while keeping the guidance scale parameter fixed. Additionally, we present a query blurring method that is equivalent to blurring the entire attention weights without incurring quadratic complexity in the number of tokens. In our experiments, SEG achieves a Pareto improvement in both quality and the reduction of side effects. The code is available at https://github.com/SusungHong/SEG-SDXL.", "pdf_parse": {"paper_id": "SEG-SDXL", "_pdf_hash": "", "abstract": [{"text": "Conditional diffusion models have shown remarkable success in visual content generation, producing high-quality samples across various domains, largely due to classifier-free guidance (CFG). Recent attempts to extend guidance to unconditional models have relied on heuristic techniques, resulting in suboptimal generation quality and unintended effects. In this work, we propose Smoothed Energy Guidance (SEG), a novel training-and condition-free approach that leverages the energybased perspective of the self-attention mechanism to enhance image generation. By defining the energy of self-attention, we introduce a method to reduce the curvature of the energy landscape of attention and use the output as the unconditional prediction. Practically, we control the curvature of the energy landscape by adjusting the Gaussian kernel parameter while keeping the guidance scale parameter fixed. Additionally, we present a query blurring method that is equivalent to blurring the entire attention weights without incurring quadratic complexity in the number of tokens. In our experiments, SEG achieves a Pareto improvement in both quality and the reduction of side effects. The code is available at https://github.com/SusungHong/SEG-SDXL.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Diffusion models [12, 45, 46] have emerged as a promising tool for visual content generation, producing high-quality and diverse samples across various domains, including image [38, 40, 42, 8, 13, 30, 2, 24, 9, 29, 34, 33, 4, 41, 5, 20, 22] , video [11, 50, 23, 18, 15, 3, 19, 44] , and 3D generation [36, 27, 6, 26, 49, 43, 48, 16] . The success of these models can be largely attributed to the use of classifier-free guidance (CFG) [14] , which enables sampling from a sharper distribution, resulting in improved sample quality. However, CFG is not applicable to unconditional image generation, where no specific conditions are provided, creating a disparity between the capabilities of text-conditioned sampling and sampling without text. This disparity results in a restriction in application, e.g., synthesizing images with ControlNet [51] without a text prompt (see the last two columns of Fig. 1 ).", "cite_spans": [{"start": 17, "end": 21, "text": "[12,", "ref_id": "BIBREF11"}, {"start": 22, "end": 25, "text": "45,", "ref_id": "BIBREF44"}, {"start": 26, "end": 29, "text": "46]", "ref_id": "BIBREF45"}, {"start": 177, "end": 181, "text": "[38,", "ref_id": "BIBREF37"}, {"start": 182, "end": 185, "text": "40,", "ref_id": "BIBREF39"}, {"start": 186, "end": 189, "text": "42,", "ref_id": "BIBREF41"}, {"start": 190, "end": 192, "text": "8,", "ref_id": "BIBREF7"}, {"start": 193, "end": 196, "text": "13,", "ref_id": "BIBREF12"}, {"start": 197, "end": 200, "text": "30,", "ref_id": "BIBREF29"}, {"start": 201, "end": 203, "text": "2,", "ref_id": "BIBREF1"}, {"start": 204, "end": 207, "text": "24,", "ref_id": "BIBREF23"}, {"start": 208, "end": 210, "text": "9,", "ref_id": "BIBREF8"}, {"start": 211, "end": 214, "text": "29,", "ref_id": "BIBREF28"}, {"start": 215, "end": 218, "text": "34,", "ref_id": "BIBREF33"}, {"start": 219, "end": 222, "text": "33,", "ref_id": "BIBREF32"}, {"start": 223, "end": 225, "text": "4,", "ref_id": "BIBREF3"}, {"start": 226, "end": 229, "text": "41,", "ref_id": "BIBREF40"}, {"start": 230, "end": 232, "text": "5,", "ref_id": "BIBREF4"}, {"start": 233, "end": 236, "text": "20,", "ref_id": "BIBREF19"}, {"start": 237, "end": 240, "text": "22]", "ref_id": "BIBREF21"}, {"start": 249, "end": 253, "text": "[11,", "ref_id": "BIBREF10"}, {"start": 254, "end": 257, "text": "50,", "ref_id": "BIBREF49"}, {"start": 258, "end": 261, "text": "23,", "ref_id": "BIBREF22"}, {"start": 262, "end": 265, "text": "18,", "ref_id": "BIBREF17"}, {"start": 266, "end": 269, "text": "15,", "ref_id": "BIBREF14"}, {"start": 270, "end": 272, "text": "3,", "ref_id": "BIBREF2"}, {"start": 273, "end": 276, "text": "19,", "ref_id": "BIBREF18"}, {"start": 277, "end": 280, "text": "44]", "ref_id": "BIBREF43"}, {"start": 301, "end": 305, "text": "[36,", "ref_id": "BIBREF35"}, {"start": 306, "end": 309, "text": "27,", "ref_id": "BIBREF26"}, {"start": 310, "end": 312, "text": "6,", "ref_id": "BIBREF5"}, {"start": 313, "end": 316, "text": "26,", "ref_id": "BIBREF25"}, {"start": 317, "end": 320, "text": "49,", "ref_id": "BIBREF48"}, {"start": 321, "end": 324, "text": "43,", "ref_id": "BIBREF42"}, {"start": 325, "end": 328, "text": "48,", "ref_id": "BIBREF47"}, {"start": 329, "end": 332, "text": "16]", "ref_id": "BIBREF15"}, {"start": 434, "end": 438, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 840, "end": 844, "text": "[51]", "ref_id": "BIBREF50"}], "ref_spans": [{"start": 901, "end": 902, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Recent literature [17, 1] has attempted to decouple CFG and image quality by extending guidance to general diffusion models, leveraging their inherent representations [25, 32, 17] . Self-attention guidance (SAG) [17] proposes leveraging the intermediate self-attention map of diffusion models to blur the input pixels and provide guidance, while perturbed attention guidance (PAG) [1] perturbs the attention map itself by replacing it with an identity attention map. Despite these efforts, these methods rely on heuristics to make perturbed predictions, resulting in unintended effects such as smoothed-out details, saturation, color shifts, and significant changes in the image structure when given a large guidance scale. Notably, the mathematical underpinnings of these unconditional guidance approaches are not well elucidated. In this work, we approach the objective from an energy-based perspective of the self-attention mechanism, which has been previously explored based on its close connection to the Hopfield energy [39, 31, 7] . Specifically, we start from the definition of the energy of self-attention, where performing a self-attention operation is equivalent to taking a gradient step. In light of this, we propose a tuning-and condition-free method that reduces the curvature of the underlying energy function by directly blurring the attention weights, and then leverages the output as the negative prediction. We call this method Smoothed Energy Guidance (SEG).", "cite_spans": [{"start": 18, "end": 22, "text": "[17,", "ref_id": "BIBREF16"}, {"start": 23, "end": 25, "text": "1]", "ref_id": "BIBREF0"}, {"start": 167, "end": 171, "text": "[25,", "ref_id": "BIBREF24"}, {"start": 172, "end": 175, "text": "32,", "ref_id": "BIBREF31"}, {"start": 176, "end": 179, "text": "17]", "ref_id": "BIBREF16"}, {"start": 212, "end": 216, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 381, "end": 384, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 1026, "end": 1030, "text": "[39,", "ref_id": "BIBREF38"}, {"start": 1031, "end": 1034, "text": "31,", "ref_id": "BIBREF30"}, {"start": 1035, "end": 1037, "text": "7]", "ref_id": "BIBREF6"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "SEG does not merely rely on the guidance scale parameter that cause side effects when its value becomes large. Instead, we can continuously control the original and maximally attenuated curvature of the energy landscape behind the self-attention by simply adjusting the parameter of the Gaussian kernel, with the guidance scale parameter fixed. Additionally, we introduce a novel query blurring technique, which is equivalent to blurring the entire attention weights without incurring quadratic cost in the number of tokens.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "We validate the effectiveness of SEG throughout the various experiments without and with text conditions, and ControlNet [51] trained on canny and depth maps. Based on the attention modulation, SEG results in less structural change from the original prediction compared to previous approaches [17, 1] , while achieving better sample quality.", "cite_spans": [{"start": 121, "end": 125, "text": "[51]", "ref_id": "BIBREF50"}, {"start": 293, "end": 297, "text": "[17,", "ref_id": "BIBREF16"}, {"start": 298, "end": 300, "text": "1]", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Diffusion models [12, 45, 46] are a class of generative models that generate data through an iterative denoising process. The process of adding noise to an image x over time t ∈ [0, T ] is governed by the forward stochastic differential equation (SDE):", "cite_spans": [{"start": 17, "end": 21, "text": "[12,", "ref_id": "BIBREF11"}, {"start": 22, "end": 25, "text": "45,", "ref_id": "BIBREF44"}, {"start": 26, "end": 29, "text": "46]", "ref_id": "BIBREF45"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion models", "sec_num": "2.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx = f (x, t)dt + g(t)dw,", "eq_num": "(1)"}], "section": "Diffusion models", "sec_num": "2.1"}, {"text": "where f and g are predefined functions that determine the manner in which the noise is added, and dw denotes a standard Wiener process.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Diffusion models", "sec_num": "2.1"}, {"text": "Correspondingly, the denoising process can be described by the reverse SDE:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Diffusion models", "sec_num": "2.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx = [f (x, t) -g(t) 2 ∇ x log p t (x)]dt + g(t)d w,", "eq_num": "(2)"}], "section": "Diffusion models", "sec_num": "2.1"}, {"text": "where ∇ x log p t (x) represents the score of the noisy data distribution and d w denotes the standard Wiener process for the reversed time.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Diffusion models", "sec_num": "2.1"}, {"text": "Diffusion models are trained to approximate the score function with s θ (x, t) ≈ ∇ x log p t (x). To generate an image based on a condition c, e.g., a class label or text, one simply needs to train diffusion models to approximate the conditional score function with s θ (x, t, c) ≈ ∇ x log p t (x|c) and replace ∇ x log p t (x) with it in the denoising process. To enhance the quality and faithfulness of the generated samples, classifier-free guidance (CFG) [14] is widely adopted. Accordingly, the reverse process becomes:", "cite_spans": [{"start": 459, "end": 463, "text": "[14]", "ref_id": "BIBREF13"}], "ref_spans": [], "eq_spans": [], "section": "Diffusion models", "sec_num": "2.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx = [f (x, t) -g(t) 2 (γ cfg s θ (x, t, c) -(γ cfg -1)s θ (x, t))]dt + g(t)d w.", "eq_num": "(3)"}], "section": "Diffusion models", "sec_num": "2.1"}, {"text": "Here, s θ (x, t) is learned by dropping the label by a certain proportion, and γ cfg is a hyperparameter that controls the strength of the guidance. Intuitively, CFG helps us to sample from sharper distribution by conditioning on a class label or text.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Diffusion models", "sec_num": "2.1"}, {"text": "The attention mechanism [47] , which has been widely adopted in diffusion models [12] , has been interpreted through the lens of energy-based models (EBMs) [31, 39, 7] , especially through its close connection with the Hopfield energy [7, 39] . In the modern (continuous) Hopfield network, the attention operation can be derived based on the concave-convex procedure (CCCP) from the following energy function [39] :", "cite_spans": [{"start": 24, "end": 28, "text": "[47]", "ref_id": "BIBREF46"}, {"start": 81, "end": 85, "text": "[12]", "ref_id": "BIBREF11"}, {"start": 156, "end": 160, "text": "[31,", "ref_id": "BIBREF30"}, {"start": 161, "end": 164, "text": "39,", "ref_id": "BIBREF38"}, {"start": 165, "end": 167, "text": "7]", "ref_id": "BIBREF6"}, {"start": 235, "end": 238, "text": "[7,", "ref_id": "BIBREF6"}, {"start": 239, "end": 242, "text": "39]", "ref_id": "BIBREF38"}, {"start": 409, "end": 413, "text": "[39]", "ref_id": "BIBREF38"}], "ref_spans": [], "eq_spans": [], "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E(ξ) = -lse(Xξ ⊤ ) + 1 2 ξξ ⊤ ,", "eq_num": "(4)"}], "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "where ξ ∈ R 1×d , X ∈ R N ×d , and lse stands for the log-sum-exp function, defined as lse(v) := log N i=1 e vi . The quadratic term acts as a regularizer to prevent ξ from exploding [39] , while -lse(Xξ ⊤ ) penalizes misalignment between X and ξ.", "cite_spans": [{"start": 183, "end": 187, "text": "[39]", "ref_id": "BIBREF38"}], "ref_spans": [], "eq_spans": [], "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "Mathematically, it turns out that the attention mechanism is equivalent to the update rule of the modern Hopfield network [7, 39] . Specifically, inspired by the Hopfield energy in (4), and noticing that the first term depends on the attention weights, we propose the following energy function for entire self-attention weights in diffusion models: Definition 2.1 (Energy Function for Self-Attention). Let Q ∈ R (HW )×d be a matrix of query vectors and K ∈ R (HW )×d be a matrix of key vectors, where H, W , and d represent the height, width, and dimension, respectively. Let A ∈ R (HW )×(HW ) := QK ⊤ . The energy function with respect to entire self-attention weights in diffusion models is defined as:", "cite_spans": [{"start": 122, "end": 125, "text": "[7,", "ref_id": "BIBREF6"}, {"start": 126, "end": 129, "text": "39]", "ref_id": "BIBREF38"}], "ref_spans": [], "eq_spans": [], "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E(A) := H i=1 W j=1 E ′ (a :(i,j) ), E ′ (a) := -lse (a) = -log H k=1 W l=1 e a (k,l) .", "eq_num": "(5)"}], "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "Note that to explicitly denote the spatial dimension, we use the subscript (x, y) to represent the index of a row or column of the matrices. Despite using the definition in (5) for the rest of the paper for simplicity, we additionally discuss the dual case, where we use the swapped indexing, in Appendix B.", "cite_spans": [{"start": 173, "end": 176, "text": "(5)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "This view leads us to an important intuition: the attention operation can be seen as a minimization step on the energy landscape, considering that the first derivative represents the softmax operation which also appears in the attention operation. Building upon this intuition, we argue that Gaussian blurring on the attention weights modulates the underlying landscape to have less curvature, and we demonstrate this in the following sections by analyzing the second derivatives.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Energy-based view of attention mechanism", "sec_num": "2.2"}, {"text": "Our aim is to theoretically derive the effect of Gaussian blur applied on the attention weights, which in the end attenuates the curvature of the underlying energy function. Then, utilizing this fact, we develop attention-based drop-in diffusion guidance that enhances the quality of the generated samples, regardless of whether an explicit condition is given. In Section 3.1, we claim some useful properties of Gaussian blur: that it preserves mean, reduces variance, and thus decreases the lse value. In Section 3.2, we find that the curvature of the energy landscape is attenuated by the attention blur operation, leading naturally to a blunter prediction for guidance. And finally, in Section 3.3, built upon this fact, we define Smoothed Energy Guidance (SEG) and propose the equivalent query blurring method, which can perform attention blurring while avoiding quadratic complexity in the number of tokens.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Method", "sec_num": "3"}, {"text": "In this section, we derive some important properties of the Gaussian blur with the aim of figuring out the variation of the energy landscape. To this end, we start from some mathematical underpinnings on applying Gaussian blur to attention weights.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "A 2D Gaussian filter is a convolution kernel that uses a 2D Gaussian function to assign weights to neighboring pixels. The 2D Gaussian function is defined as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "G(x, y) = 1 2πσ 2 e -(x-µx ) 2 +(y-µy ) 2 2σ 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "where µ x and µ y are the means in the x and y directions, and σ is the standard deviation. The 2D Gaussian filter possesses symmetry, i.e., G(x, y) = G(-x, -y), and normalization, i.e., G(x, y)dxdy = 1. In practice, we use a discretized version of the Gaussian filter with a finite kernel size depending on σ, normalized to sum to 1. Lemma 3.1. Spatially applying a 2D Gaussian blur to the attention weights a := Qk ⊤ preserves the average E i,j [a (i,j) ]. In addition, the variance monotonically decreases every time we apply the Gaussian blur.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "Proof sketch. Applying a 2D Gaussian filter to the attention weights a (i,j) yields the blurred values ã(i,j) :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "ã(i,j) = k m=-k k n=-k G(m, n) • a (i+m,j+n)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "where k is the filter size, G(m, n) is the Gaussian filter value at position (m, n), and a (i+m,j+n) is the attention weight at position (i + m, j + n). Since the Gaussian filter is symmetric and normalized, it can be shown that the mean of the blurred attention weights is equal to the mean of the original attention weights. Similarly, we can show that the variance monotonically decreases when we apply a 2D Gaussian filter. See Appendix A.1 for the complete proof.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "Note that this fact also implies that blurring with a Gaussian filter with a larger standard deviation causes a greater decrease in the variance of attention weights. This is because a Gaussian filter with a larger standard deviation can always be represented as a convolution of two filters with smaller standard deviations, due to the associativity of the convolution operation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "Finally, we show that applying a 2D Gaussian blur to attention weights increases the lse value in (5), i.e., increases the energy in (5) . This provides a bit of intuition about the underlying energy landscape, yet it is more prominently utilized in the claims in the following sections. Lemma 3.2. Applying a 2D Gaussian blur to attention weights a := Qk ⊤ increases the lse term when we consider the second-order Taylor series approximation of the exponential function around the mean µ := E i,j [a (i,j) ]. Consequently, the maximum is achieved when the attention is uniform, i.e., a (i,j) = a (k,l) ∀i, j, k, l. This corresponds to the case when we apply the Gaussian blur with σ → ∞.", "cite_spans": [{"start": 133, "end": 136, "text": "(5)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "Proof sketch. Applying the second-order Taylor series approximation around the mean µ, and using Proposition 3.1, we show that the second-order approximation of lse(a) is larger than or equal to that of lse(ã). Subsequently, we introduce Lagrange multipliers to find the maximum, which gives us the result, a (i,j) = a (k,l) ∀i, j, k, l. We leave the full proof in Appendix A.2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Gaussian blur to attention weights", "sec_num": "3.1"}, {"text": "In this section, we demonstrate that applying a 2D Gaussian blur to the attention weights before the softmax operation results in computing the updated value with reduced curvature of the underlying energy function. To this end, we analyze the Gaussian curvature before and after blurring the attention weights. This is closely related to the Hessian of the energy function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Analysis of the energy landscape", "sec_num": "3.2"}, {"text": "Theorem 3.1. Let the attention weights be defined as a := Qk ⊤ . Consider the energy function in (5) . Then, applying a Gaussian blur to the attention weights a before the softmax operation results in the attenuation of the Gaussian curvature of the underlying energy function where gradient descent is performed.", "cite_spans": [{"start": 97, "end": 100, "text": "(5)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "Analysis of the energy landscape", "sec_num": "3.2"}, {"text": "Proof sketch. Let H denote the Hessian of the original energy function, i.e., the derivative of the negative softmax, and H denote the Hessian of the new energy function associated with blurred attention weights. Furthermore, let b ij denote the i-th row, j-th column entry in the Toeplitz matrix B representing the Gaussian blur. Calculating the derivatives, we have the elements of the Hessians, To provide more intuition about what is actually happening and how we utilize this property in the later section, it is intriguing to consider the attenuating effect on the curvature in analogy to classifier-free guidance (CFG). CFG uses the difference between the prediction based on the sharper conditional distribution and the prediction based on the smoother unconditional distribution to guide the sampling process. By analogy, we propose a method to make the landscape of the energy function smoother to guide the sampling process, as opposed to the original (sharper) energy landscape.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Analysis of the energy landscape", "sec_num": "3.2"}, {"text": "h ij = (ξ(a) i -δ ij )ξ(a) j and hij = (ξ(ã) i -δ ij )ξ(ã) j b ij . Using Lemmas 3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Analysis of the energy landscape", "sec_num": "3.2"}, {"text": "From a probabilistic perspective, the energy is associated with the likelihood of the attention weights in terms of the <PERSON><PERSON><PERSON> distribution conditioned on a given configuration, i.e., the feature map. Blurring the attention weights diminishes this likelihood as shown in Lemma 3.2, and also reduces the curvature of the distribution as shown in Theorem 3.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Analysis of the energy landscape", "sec_num": "3.2"}, {"text": "Based on the above observation that the Gaussian blur on attention weights attenuates the curvature of the energy function, we propose Smoothed Energy Guidance (SEG) in this section. For brevity, we redefine the unconditional score prediction as s θ (x, t), and the unconditional score prediction with the energy curvature reduced as sθ (x, t). Specifically, sθ (x, t) is the prediction with the attention weights blurred using a 2D Gaussian filter G with the standard deviation σ. We formulate the process as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(QK ⊤ ) seg = G * (QK ⊤ ),", "eq_num": "(6)"}], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "where * denotes the 2D convolution operator. Then, we replace the original attention weights with (QK ⊤ ) seg and compute the final value as in ordinary self-attention.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "For practical purposes when the number of tokens is large, we propose an efficient computation of (6) using the property of a linear map, since the convolution operation is linear. Concretely, blurring queries is exactly the same as blurring the entire attention weights, and we propose the following proposition to justify our claim. Proposition 3.1. Let Q and K be the query and key matrices in self-attention, and let G be a 2D Gaussian filter. Blurring the attention weights with G is equivalent to blurring the query matrix Q with G and then computing the attention weights.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "Proof. Since the convolution operation is linear, we can always find a Toeplitz matrix B such that:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "G * (QK ⊤ ) = B(QK ⊤ ),", "eq_num": "(7)"}], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "where * denotes the 2D convolution operation. Using the properties of matrix multiplication, we can rewrite (7) as:", "cite_spans": [{"start": 108, "end": 111, "text": "(7)", "ref_id": "BIBREF6"}], "ref_spans": [], "eq_spans": [], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "B(QK ⊤ ) = (BQ)K ⊤ = (G * Q)K ⊤ .", "eq_num": "(8)"}], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "Finally, SEG is formulated as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx = [f (x, t) -g(t) 2 (γ seg s θ (x, t) -(γ seg -1)s θ (x, t))]dt + g(t)d w,", "eq_num": "(9)"}], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "where γ seg denotes the guidance scale of SEG. In a straightforward manner, as SEG does not rely on external conditions, it can be used for conditional sampling strategies such as CFG [14] and ControlNet [51] . For the combinatorial sampling with CFG, following [17] , we simply extend (9) for improved conditional sampling with both SEG and CFG as follows:", "cite_spans": [{"start": 184, "end": 188, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 204, "end": 208, "text": "[51]", "ref_id": "BIBREF50"}, {"start": 262, "end": 266, "text": "[17]", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "dx = [f (x, t) -g(t) 2 ((1 -γ cfg + γ seg )s θ (x, t) + γ cfg s θ (x, t, c) -γ seg sθ (x, t))]dt + g(t)d w,", "eq_num": "(10)"}], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "which is an intuitive result, as the update rule moves x towards the conditional prediction while keeping it far from the prediction with blurred attention weights.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "We are likely to get a result with saturation when using a large guidance scale, such as with classifierfree guidance (CFG) [14] , self-attention guidance (SAG) [17] , and perturbed attention guidance (PAG) [1] . This is a significant caveat since we need to increase the scale to achieve a maximum effect with these methods. Contrary to this, we can fix the scale of SEG as justified in Sec. 5.5 and control its maximum effect through σ of the Gaussian blur, making the choice more flexible. For σ, two extreme cases are recognized. If σ → 0, the blurred attention weights remain the same as the original, while when σ → ∞, the attention weights merely adopt a single mean value across spatial axes. We find that even the latter extreme case results in a high-quality outcome, corroborating that we can control the quality to the limit without saturation.", "cite_spans": [{"start": 124, "end": 128, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 161, "end": 165, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 207, "end": 210, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Smoothed energy guidance for diffusion models", "sec_num": "3.3"}, {"text": "Classifier-free guidance (CFG) [14] , first proposed as a replacement for classifier guidance (CG) [8] is controlled by a scale parameter. The higher we set classifier-free guidance, the more we get faithful, high-quality images. However, it requires external labels, such as text [30] or class [8] labels, making it impossible to apply to unconditional diffusion models. Also, it requires specific traning procedure with label dropping and it is known that high CFG causes saturation [42] .", "cite_spans": [{"start": 31, "end": 35, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 99, "end": 102, "text": "[8]", "ref_id": "BIBREF7"}, {"start": 281, "end": 285, "text": "[30]", "ref_id": "BIBREF29"}, {"start": 295, "end": 298, "text": "[8]", "ref_id": "BIBREF7"}, {"start": 485, "end": 489, "text": "[42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Discussion on related work", "sec_num": "4"}, {"text": "Figure 4 : Conditional generation using ControlNet [51] and SEG. Table 1 : Quantitative comparison of SEG with vanilla SDXL [35] , SAG [17] , and PAG [1] for unconditional generation.", "cite_spans": [{"start": 51, "end": 55, "text": "[51]", "ref_id": "BIBREF50"}, {"start": 124, "end": 128, "text": "[35]", "ref_id": "BIBREF34"}, {"start": 135, "end": 139, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 150, "end": 153, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [{"start": 7, "end": 8, "text": "4", "ref_id": null}, {"start": 71, "end": 72, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Discussion on related work", "sec_num": "4"}, {"text": "Vanilla SDXL [35] Tackling the caveats of CFG, unconditional approaches such as self-attention guidance (SAG) [17] and perturbed attention guidance (PAG) [1] have been proposed. SAG selectively blurs images with the mask obtained from the attention map and guides the generation process given the prediction. This indirect approach causes saturation and noisy images when given a large guidance scale, leading to the selection of a guidance scale less than or equal to 1. PAG guides images using prediction with identity attention, where the attention map is an identity matrix. However, the reliance on heuristics to make perturbed predictions results in unintended side effects. As an example of the side effects of replacing the attention map with identity attention, PAG changes the visual structure and color distribution of an image, as evidenced in Figs. 5, 8, and 9.", "cite_spans": [{"start": 13, "end": 17, "text": "[35]", "ref_id": "BIBREF34"}, {"start": 110, "end": 114, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 154, "end": 157, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Metric", "sec_num": null}, {"text": "Contrary to these, we control the effect of SEG through the standard deviation of the Gaussian filter, σ. Moreover, while being theory-inspired, SEG is relatively free from unintended effects. In the following section, we corroborate our claim with extensive experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Metric", "sec_num": null}, {"text": "We build upon the current open-source state-of-the-art diffusion model, Stable Diffusion XL (SDXL) [35] , as our baseline, and do not change the configuration. To sample with SEG, we choose the same attention layers (mid-blocks) and guidance scale as PAG [1] . For SEG and PAG sampling, we use the Euler discrete scheduler [21] , while for SAG [17] , we instead use the DDIM scheduler [45] since the current implementation of SAG does not support the Euler discrete sampler.", "cite_spans": [{"start": 99, "end": 103, "text": "[35]", "ref_id": "BIBREF34"}, {"start": 255, "end": 258, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 323, "end": 327, "text": "[21]", "ref_id": "BIBREF20"}, {"start": 344, "end": 348, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 385, "end": 389, "text": "[45]", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "Implementation details", "sec_num": "5.1"}, {"text": "For SAG and PAG, we use the same configurations they used in the experiments with the previous version of Stable Diffusion, with guidance scales of 1.0 and 3.0, respectively. We set γ seg to 3.0, except in the ablation study. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Implementation details", "sec_num": "5.1"}, {"text": "We use various metrics to evaluate quality (FID [10] and CLIP score [37] , calculated with 30k references from the MS-COCO 2014 validation set [28] ) and to assess the extent of change due to applied guidance (LPIPS vgg, alex [52] ). The latter metric, calculated using the outputs of vanilla SDXL, measures the extent of side effects by comparing guided images to their unguided counterparts.", "cite_spans": [{"start": 48, "end": 52, "text": "[10]", "ref_id": "BIBREF9"}, {"start": 68, "end": 72, "text": "[37]", "ref_id": "BIBREF36"}, {"start": 143, "end": 147, "text": "[28]", "ref_id": "BIBREF27"}, {"start": 226, "end": 230, "text": "[52]", "ref_id": "BIBREF51"}], "ref_spans": [], "eq_spans": [], "section": "Metrics", "sec_num": "5.2"}, {"text": "In this section, our aim is to demonstrate that with SEG, we can sample plausible images using vanilla SDXL [35] under various conditions and even without any conditions, as demonstrated in Fig. 1 . Furthermore, without the risk of saturation, we can control the quality and plausibility of the samples.", "cite_spans": [{"start": 108, "end": 112, "text": "[35]", "ref_id": "BIBREF34"}], "ref_spans": [{"start": 195, "end": 196, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "For the results, we use σ ∈ {1, 2, 5, 10}. Additionally, as mentioned in Sec. 3.3, we present two extreme cases, σ → 0 (vanilla SDXL) and σ → ∞ (uniform queries).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "Unconditional generation In this section, our aim is to demonstrate that with SEG, we can sample plausible images from the unconditional mode of the vanilla SDXL, which was originally trained on a large-scale text-to-image dataset. The results are presented in Fig. 1 , Fig. 2 , and Table 1 . The results show a clear tendency to draw higher quality samples by utilizing the differences between the two energy landscapes with different curvatures derived from self-attention mechanisms.", "cite_spans": [], "ref_spans": [{"start": 266, "end": 267, "text": "1", "ref_id": "FIGREF0"}, {"start": 275, "end": 276, "text": "2", "ref_id": "FIGREF2"}, {"start": 289, "end": 290, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "In Fig. 2 and Fig. 13 , we show the effectiveness of generating more plausible images, while vanilla SDXL is unable to generate high-quality images without any conditions. The results show a clear tendency to draw higher quality samples by utilizing the differences between the two energy landscapes with different curvatures derived from self-attention mechanisms. When σ is larger, the definition and expression of the samples improve, as the difference in curvature becomes more pronounced.", "cite_spans": [], "ref_spans": [{"start": 8, "end": 9, "text": "2", "ref_id": "FIGREF2"}, {"start": 19, "end": 21, "text": "13", "ref_id": "FIGREF12"}], "eq_spans": [], "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "Conditional generation In Figs. 3, 4, 10, 11 , and 14, we display sampling results conditioned on text, Canny, and depth map. Using text (Fig. 3 ), the vanilla SDXL without CFG is unable to generate high-quality images and produces noisy results. Canny and depth map conditioning on SDXL (Fig. 4 , 10, and 11) is achieved through ControlNet [51] , trained on such maps. The results show that SEG enhances the quality and fidelity of the generated images while preserving the textual and structural information provided by the conditioning inputs. Notably, as σ increases, the generated images exhibit improved definition and quality without introducing significant artifacts or deviations from the original condition. The combination with higher CFG scales is shown in Figs. 15 16 17 18 19 .", "cite_spans": [{"start": 32, "end": 34, "text": "3,", "ref_id": "BIBREF2"}, {"start": 35, "end": 37, "text": "4,", "ref_id": "BIBREF3"}, {"start": 38, "end": 41, "text": "10,", "ref_id": "BIBREF9"}, {"start": 42, "end": 44, "text": "11", "ref_id": "BIBREF10"}, {"start": 341, "end": 345, "text": "[51]", "ref_id": "BIBREF50"}], "ref_spans": [{"start": 143, "end": 144, "text": "3", "ref_id": "FIGREF3"}, {"start": 294, "end": 295, "text": "4", "ref_id": null}, {"start": 775, "end": 777, "text": "15", "ref_id": "FIGREF13"}, {"start": 778, "end": 780, "text": "16", "ref_id": "FIGREF14"}, {"start": 781, "end": 783, "text": "17", "ref_id": "FIGREF8"}, {"start": 784, "end": 786, "text": "18", "ref_id": "FIGREF0"}, {"start": 787, "end": 789, "text": "19", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "In Table 2 , we show the quantitative results for text-conditional generation in terms of σ. We observe a clear trade-off between image quality (represented by FID and CLIP score) and the deviation from the original sample (represented by LPIPS). We sample 30k images for each σ to compute the metrics.", "cite_spans": [], "ref_spans": [{"start": 9, "end": 10, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Controlling image generation with the standard deviation", "sec_num": "5.3"}, {"text": "Since the results are visually favorable when we use σ = 10 and σ → ∞, and they are the best in terms of CLIP score and FID, respectively, we adopt those configurations for comparison of unconditional guidance methods. The results are presented in Figs. 5, 8, 9, and Table 1 . Notably, our method achieves better image quality in terms of FID, while remaining similar to the original output of vanilla SDXL as measured by LPIPS, implying a Pareto improvement. In this section, we address two parameters, γ seg and σ, and justify that fixing γ seg is a reasonable choice. In Fig. 6 , we present the results from our testing. The results reveal that increasing γ seg does not generally lead to improved sample quality in terms of FID and CLIP score, due to various issues such as saturation. In contrast, increasing σ tends to improve sample quality and plausibility. This supports the claim that image quality should be controlled by σ, instead of the guidance scale parameter. We sample 30k images for each combination to calculate the metrics.", "cite_spans": [], "ref_spans": [{"start": 273, "end": 274, "text": "1", "ref_id": null}, {"start": 579, "end": 580, "text": "6", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "Comparison with previous methods", "sec_num": "5.4"}, {"text": "We introduce Smoothed Energy Guidance (SEG), a novel training-and condition-free guidance method for image generation with diffusion models. The key advantages of SEG lie in its flexibility and the theoretical foundation, allowing us to significantly enhance sample quality without side effects by adjusting the standard deviation of the Gaussian filter. We hope our method inspires further research on improving generative models, and extending the approach beyond image generation, for example, to video or natural language processing.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion, limitations and societal impacts Conclusion", "sec_num": "6"}, {"text": "The paper proposes guidance to enhance quality outcomes. Consequently, the attainable quality of our approach is contingent upon the baseline model employed. Furthermore, the application of SEG to temporal attention mechanisms in video or multi-view diffusion models is not addressed, remaining a promising avenue for future research. It is important to note that the improvements achieved through this method may potentially lead to unintended negative societal consequences by inadvertently amplifying existing stereotypes or harmful biases.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations and societal impacts", "sec_num": null}, {"text": "A.1 Proof of Lemma 3.1 Let a (i,j) denote the original attention weights and ã(i,j) denote the blurred attention weights, as in the main paper. Assume that the original attention weights are properly padded to maintain consistent statistics. Then, the following shows that the mean of the blurred attention weights remains the same.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "E i,j [ã (i,j) ] = 1 HW H i=1 W j=1 ã(i,j) = 1 HW H i=1 W j=1 k m=-k k n=-k G(m, n) • a (i+m,j+n) = k m=-k k n=-k G(m, n) •   1 HW H i=1 W j=1 a (i+m,j+n)   = k m=-k k n=-k G(m, n) • E i,j [a (i,j) ] = E i,j [a (i,j) ] • k m=-k k n=-k G(m, n) = E i,j [a (i,j) ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "In addition, the variance of the blurred attention weights is smaller than or equal to the variance of the original attention weights.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "Var i,j [ã (i,j) ] = 1 HW H i=1 W j=1 (ã (i,j) -E i,j [ã (i,j) ]) 2 = 1 HW H i=1 W j=1 k m=-k k n=-k G(m, n) • (a (i+m,j+n) -E i,j [a (i,j) ]) 2 = k m=-k k n=-k k r=-k k s=-k G(m, n) • G(r, s) • Cov[a (i+m,j+n) , a (i+r,j+s) ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "Using the <PERSON><PERSON><PERSON><PERSON><PERSON> inequality and the normalization property of the 2D Gaussian filter, we can show that the variance monotonically decreases when we apply Gaussian blur.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "Var i,j [ã (i,j) ] ≤ k m=-k k n=-k k r=-k k s=-k G(m, n) • G(r, s) • Var[a (i+m,j+n) ] • Var[a (i+r,j+s) ] = k m=-k k n=-k G(m, n) • Var[a (i+m,j+n) ] 2 ≤ k m=-k k n=-k G(m, n) • k m=-k k n=-k G(m, n) • Var[a (i+m,j+n) ] = k m=-k k n=-k G(m, n) • Var[a (i+m,j+n) ] = Var i,j [a (i,j) ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "A.2 Proof of Lemma 3.2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "Applying the second-order Taylor series approximation of e x to our function f around the mean µ, we get:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "H i=1 W j=1 e a (i,j) ≈ H i=1 W j=1 e µ + e µ (a (i,j) -µ) + 1 2 e µ (a (i,j) -µ) 2", "eq_num": "(11)"}], "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "= HW • e µ + 1 2 e µ H i=1 W j=1 (a (i,j) -µ) 2", "eq_num": "(12)"}], "section": "A Full proofs", "sec_num": null}, {"text": "In the last step, we used the fact that", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "H i=1 W j=1 (a (i,j) -µ) = 0 because µ is the mean. Similarly, H i=1 W j=1 e ã(i,j) ≈ HW • e µ + 1 2 e µ H i=1 W j=1 (ã (i,j) -µ) 2", "eq_num": "(13)"}], "section": "A Full proofs", "sec_num": null}, {"text": "Since Var[a] > Var[ã], we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "H i=1 W j=1 (a (i,j) -µ) 2 ≥ H i=1 W j=1 (ã (i,j) -µ) 2", "eq_num": "(14)"}], "section": "A Full proofs", "sec_num": null}, {"text": "Therefore, the second-order approximation of lse(a) is larger than that of lse(ã).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "Note that this fact also implies blurring with a Gaussian filter with a bigger variance causes more decrease in the variance of attention weights, because Gaussian filter with a larger variance can always be represented as a convolution of two filters with smaller variances, and the convolution operation is associative.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "To find the maximum value subject to the constraint a (1,1) + a (1,2) + . . . + a (H,W ) = c for some constant c, we introduce Lagrange multipliers. Let g(a (1,1) , a (1,2) , . . . , a (H,W ) ) = a (1,1) + a (1,2) + . . . + a (H,W ) . The Lagrangian function is defined as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L(a (1,1) , a (1,2) , . . . , a (H,W ) , λ) = e a (1,1) +e a (1,2) +. . .+e a (H,W ) -λ(a (1,1) +a (1,2) +. . .+a (H,W ) -c)", "eq_num": "(15)"}], "section": "A Full proofs", "sec_num": null}, {"text": "Taking partial derivatives and setting them to zero yields:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂a (i,j) = e a (i,j) -λ = 0", "eq_num": "(16)"}], "section": "A Full proofs", "sec_num": null}, {"text": "Solving for a (i,j) , we obtain a (i,j) = ln(λ) for all i = 1, 2, . . . , H and j = 1, 2, . . . , W Summing these equations results in:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "λ = e c HW", "eq_num": "(17)"}], "section": "A Full proofs", "sec_num": null}, {"text": "Substituting λ back into a (i,j) = ln(λ) gives a (1,1) = a (1,2) = . . . = a (H,W ) = c HW . Therefore, the minimum value of H i=1 W j=1 e a (i,j) is achieved when a (1,1) = a (1,2) = . . . = a (H,W ) .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Full proofs", "sec_num": null}, {"text": "Let a = (a 1 , . . . , a n ) denote the attention values before the softmax operation, and let ã = (ã 1 , . . . , ãn ) denote the attention values after applying the 2D Gaussian blur. Let H denote the Hessian of the original energy, i.e., the derivative of the negative softmax, and H denote the Hessian of the underlying energy associated with the blurred weights.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "The elements in the i-th row and j-th column of the Hessian matrices are given by:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "h ij = (ξ(a) i -δ ij )ξ(a) j , (", "eq_num": "18"}], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": ") hij = (ξ(ã) i -δ ij )ξ(ã) j b ij ,", "eq_num": "(19)"}], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "respectively, where b ij are the elements of the <PERSON><PERSON>litz matrix corresponding to the Gaussian blur kernel, and δ ij denotes the Kronecker delta.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "Assuming ξ(ã) i ξ(ã) j ≈ 0 and ξ(a) i ξ(a) j ≈ 0 for all i and j, which is a reasonable assumption when the number of token is large and the softmax values get small, the non-diagonal elements of the Hessians approximate to 0 and the diagonal elements dominate. Therefore, the determinants of the Hessian matrices are approximated as the product of the dominant terms:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "| det(H)| ≈ n i=1 ξ(a) i , | det( H)| ≈ n i=1 ξ(ã) i b ii", "eq_num": "(20)"}], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "We have the following inequality: ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "n i=1 ξ(ã) i b ii < n i=1 ξ(ã) i = e n j=1 ãj ( n j=1 e ãj ) n", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "where the first inequality follows from the property of the Gaussian blur kernel, 0 ≤ b ii < 1, and the second inequality is derived from Lemmas 3.1 and 3.2, which demonstrate the mean-preserving property and the decrease in the lse value when applying a blur. The monotonicity of the logarithm function implies that the denominator involving the blurred attention weights is smaller. Eventually, we obtain the following inequality:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "| det( H)| < | det(H)|. (", "eq_num": "23"}], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "This implies that the updated value is derived with attenuated curvature of the energy function underlying the blurred softmax operation compared to that of the original softmax operation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Theorem 3.1", "sec_num": null}, {"text": "As we previously stated in Section 2.2, we have the dual definition regarding ( 5), where we use swapped indexing. Importantly, the swapped indices can be interpreted as altering the definition of attention weights to A := KQ ⊤ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Dual definition", "sec_num": null}, {"text": "A similar conclusion can be drawn as in the main paper, except that query blurring becomes key blurring with this definition. To see this, Eq. 7 changes slightly with this definition, using the symmetry of the Toeplitz matrix B:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Dual definition", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "G * (KQ ⊤ ) = B(KQ ⊤ ) (24) = ((KQ ⊤ ) ⊤ B ⊤ ) ⊤ (25) = (QK ⊤ B ⊤ ) ⊤ (26) = (Q(BK) ⊤ ) ⊤ (27) = (Q(G * K) ⊤ ) ⊤ (28) = (G * K)Q ⊤ ,", "eq_num": "(29)"}], "section": "B Dual definition", "sec_num": null}, {"text": "where * denotes the 2D convolution operation. Empirically, this altered definition does not introduce a significant difference in the overall image quality, as shown in Fig. 12 .", "cite_spans": [], "ref_spans": [{"start": 174, "end": 176, "text": "12", "ref_id": "FIGREF11"}], "eq_spans": [], "section": "B Dual definition", "sec_num": null}, {"text": "In this section, we present further qualitative results to demonstrate the effectiveness and versatility of our Smoothed Energy Guidance (SEG) method across various generation tasks and in comparison with other approaches.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Additional qualitative results", "sec_num": null}, {"text": "Comparison with previous methods Figs. 8 and 9 provide a qualitative comparison of SEG against vanilla SDXL [35] , Self-Attention Guidance (SAG) [17] , and Perturbed Attention Guidance (PAG) [1] . These comparisons highlight the superior performance of SEG in terms of image quality, coherence, and adherence to the given prompts. SEG consistently produces sharper details, more realistic textures, and better overall composition compared to the other methods.", "cite_spans": [{"start": 39, "end": 40, "text": "8", "ref_id": "BIBREF7"}, {"start": 108, "end": 112, "text": "[35]", "ref_id": "BIBREF34"}, {"start": 145, "end": 149, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 191, "end": 194, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "C Additional qualitative results", "sec_num": null}, {"text": "Conditional generation with ControlNet Figs. 10 and 11 showcase the application of SEG in conjunction with ControlNet [51] for conditional image generation. These results illustrate how SEG can enhance the quality and coherence of generated images while maintaining fidelity to the provided control signals. The images demonstrate improved detail, texture, and overall visual appeal compared to standard ControlNet outputs without prompts. Unconditional and text-conditional generation Fig. 13 demonstrates the capability of SEG in unconditional image generation, showcasing its ability to produce high-quality, diverse images without text prompts. Fig. 14 exhibits text-conditional generation results using SEG, illustrating its effectiveness in translating textual descriptions into visually appealing and accurate images.", "cite_spans": [{"start": 118, "end": 122, "text": "[51]", "ref_id": "BIBREF50"}], "ref_spans": [{"start": 491, "end": 493, "text": "13", "ref_id": "FIGREF12"}, {"start": 654, "end": 656, "text": "14", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "C Additional qualitative results", "sec_num": null}, {"text": "Interaction with classifier-free guidance Figs. 15-19 present a series of experiments exploring the combination of SEG with CFG. In these experiments, the SEG guidance scale (γ seg ) is fixed at 3.0, while the CFG scale is varied. The results demonstrate that SEG consistently improves image quality across different CFG scales without causing saturation or significant changes in the general structure of the images.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Additional qualitative results", "sec_num": null}, {"text": "Ablation study Fig. 20 displays a visual example of unconditional generation with controlled γ seg and σ. Consistent with results in Sec. 5.5, controlling image quality with σ has fewer side effects than controlling with γ seg .", "cite_spans": [], "ref_spans": [{"start": 20, "end": 22, "text": "20", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "C Additional qualitative results", "sec_num": null}, {"text": "The overall pipeline and conceptual framework of SEG are presented in Fig. 7 . Fig. 7 (a) and Fig. 7 (b) depict the original sampling process and the modified sampling process with smoothed energy, respectively. Fig. 7 (c) illustrates the the final prediction (the red arrow) with the guidance scale.", "cite_spans": [], "ref_spans": [{"start": 75, "end": 76, "text": "7", "ref_id": "FIGREF8"}, {"start": 84, "end": 85, "text": "7", "ref_id": "FIGREF8"}, {"start": 99, "end": 100, "text": "7", "ref_id": "FIGREF8"}, {"start": 217, "end": 218, "text": "7", "ref_id": "FIGREF8"}], "eq_spans": [], "section": "D Pipeline figure", "sec_num": null}, {"text": "Figure 8 : Qualitative comparison of SEG with vanilla SDXL [35] , SAG [17] , and PAG [1] .", "cite_spans": [{"start": 59, "end": 63, "text": "[35]", "ref_id": "BIBREF34"}, {"start": 70, "end": 74, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 85, "end": 88, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [{"start": 7, "end": 8, "text": "8", "ref_id": null}], "eq_spans": [], "section": "D Pipeline figure", "sec_num": null}, {"text": "Figure 9 : Qualitative comparison of SEG with vanilla SDXL [35] , SAG [17] , and PAG [1] . Guidelines:", "cite_spans": [{"start": 59, "end": 63, "text": "[35]", "ref_id": "BIBREF34"}, {"start": 70, "end": 74, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 85, "end": 88, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [{"start": 7, "end": 8, "text": "9", "ref_id": null}], "eq_spans": [], "section": "D Pipeline figure", "sec_num": null}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Pipeline figure", "sec_num": null}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors? Answer: [Yes] Justification: The limitations are discussed the paper. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Justification: The paper discuss potential societal impacts. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)? Answer: [NA] Justification: The paper proposes a guidance method for the current model; therefore, the paper itself poses no such risks. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort. 12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected? Answer: [Yes] Justification: The creators or original owners of assets are properly credited and the license and terms of use are explicitly mentioned and properly respected. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators. 13. New Assets", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}], "back_matter": [{"text": "I would like to express my gratitude to <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> for their valuable feedback and insights. Their thoughtful comments and suggestions have been instrumental in improving this work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "Justification: The paper provide the full set of assumptions and a complete proof. Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: The paper fully disclose all the information needed to reproduce the results. Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets? Answer: [NA] Justification: The paper does not release new assets. Guidelines:• The answer NA means that the paper does not release new assets.• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Self-rectifying diffusion sampling with perturbed-attention guidance", "authors": [{"first": "Dong<PERSON>on", "middle": [], "last": "Ahn", "suffix": ""}, {"first": "Hyoungwon", "middle": [], "last": "Cho", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Min", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Seonhwa", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["Hee"], "last": "Park", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2403.17377"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Self-rectifying diffusion sampling with perturbed-attention guidance. arXiv preprint arXiv:2403.17377, 2024.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Conditional image generation with score-based diffusion models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Carola<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2111.13606"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Conditional image generation with score-based diffusion models. arXiv preprint arXiv:2111.13606, 2021.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Align your latents: High-resolution video synthesis with latent diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["Wook"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Fidler", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Kreis", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "22563--22575", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Align your latents: High-resolution video synthesis with latent diffusion models. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 22563- 22575, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Retrieval-augmented diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Oktay", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ommer", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "15309--15324", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Retrieval-augmented diffusion models. Advances in Neural Information Processing Systems, 35:15309-15324, 2022.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Ledits++: Limitless image editing using text-to-image models", "authors": [{"first": "<PERSON>", "middle": [], "last": "Brack", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Apolinário", "middle": [], "last": "Passos", "suffix": ""}], "year": 2024, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "8861--8870", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Ledits++: Limitless image editing using text-to-image models. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 8861-8870, 2024.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Fantasia3d: Disentangling geometry and appearance for high-quality text-to-3d content creation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yongwei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "22246--22256", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Fantasia3d: Disentangling geometry and appearance for high-quality text-to-3d content creation. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 22246-22256, 2023.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "On a model of associative memory with huge storage capacity", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Löwe", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Upgang", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Vermet", "suffix": ""}], "year": 2017, "venue": "Journal of Statistical Physics", "volume": "168", "issue": "", "pages": "288--299", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. On a model of associative memory with huge storage capacity. Journal of Statistical Physics, 168:288-299, 2017.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Diffusion models beat gans on image synthesis", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in neural information processing systems", "volume": "34", "issue": "", "pages": "8780--8794", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON>. Diffusion models beat gans on image synthesis. Advances in neural information processing systems, 34:8780-8794, 2021.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Diffusion self-guidance for controllable image generation", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Poole", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Aleksander", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "16222--16239", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Diffusion self-guidance for controllable image generation. Advances in Neural Information Processing Systems, 36:16222-16239, 2023.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Gans trained by a two time-scale update rule converge to a local nash equilibrium", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sepp", "middle": [], "last": "<PERSON><PERSON>reiter", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Gans trained by a two time-scale update rule converge to a local nash equilibrium. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Imagen video: High definition video generation with diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>t<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Poole", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Fleet", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.02303"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Imagen video: High definition video generation with diffusion models. arXiv preprint arXiv:2210.02303, 2022.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Denoising diffusion probabilistic models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "6840--6851", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Denoising diffusion probabilistic models. Advances in neural information processing systems, 33:6840-6851, 2020.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Cascaded diffusion models for high fidelity image generation", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>t<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Fleet", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Journal of Machine Learning Research", "volume": "23", "issue": "47", "pages": "1--33", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Cascaded diffusion models for high fidelity image generation. Journal of Machine Learning Research, 23(47):1-33, 2022.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Classifier-free diffusion guidance", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2207.12598"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Classifier-free diffusion guidance. arXiv preprint arXiv:2207.12598, 2022.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Video diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Fleet", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "8633--8646", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Video diffusion models. Advances in Neural Information Processing Systems, 35:8633-8646, 2022.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Debiasing scores and prompts of 2d diffusion for view-consistent text-to-3d generation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "Dong<PERSON>on", "middle": [], "last": "Ahn", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Debiasing scores and prompts of 2d diffusion for view-consistent text-to-3d generation. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Improving sample quality of diffusion models using self-attention guidance", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "7462--7471", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Improving sample quality of diffusion models using self-attention guidance. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 7462-7471, 2023.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Direct2v: Large language models are frame-level directors for zero-shot text-to-video generation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.14330"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Direct2v: Large language models are frame-level directors for zero-shot text-to-video generation. arXiv preprint arXiv:2305.14330, 2023.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Free-bloom: Zero-shot text-to-video generator with llm director and ldm animator", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Shi", "suffix": ""}, {"first": "Lan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Sibei", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Free-bloom: Zero-shot text-to-video generator with llm director and ldm animator. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Collaborative diffusion for multi-modal face generation and editing", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "6080--6090", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Collaborative diffusion for multi-modal face generation and editing. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 6080-6090, 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Elucidating the design space of diffusion-based generative models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "26565--26577", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Elucidating the design space of diffusion-based generative models. Advances in Neural Information Processing Systems, 35:26565-26577, 2022.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Imagic: Text-based real image editing with diffusion models", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Zada", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>v", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Inbar", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Irani", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "6007--6017", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Imagic: Text-based real image editing with diffusion models. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 6007-6017, 2023.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Text2video-zero: Text-to-image diffusion models are zero-shot video generators", "authors": [{"first": "Levon", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Andranik", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Zhangyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Shi", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "15954--15964", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Text2video-zero: Text-to-image diffusion models are zero-shot video generators. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 15954-15964, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Diffusionclip: Text-guided diffusion models for robust image manipulation", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ye", "middle": [], "last": "", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "2426--2435", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Diffusionclip: Text-guided diffusion models for robust image manipulation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 2426-2435, 2022.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Diffusion models already have a semantic latent space", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Uh", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.10960"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Diffusion models already have a semantic latent space. arXiv preprint arXiv:2210.10960, 2022.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Sweetdreamer: Aligning geometric priors in 2d diffusion for consistent text", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.02596"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Sweetdreamer: Aligning geometric priors in 2d diffusion for consistent text-to-3d. arXiv preprint arXiv:2310.02596, 2023.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Magic3d: High-resolution text-to-3d content creation", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jun", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Towa<PERSON>", "middle": [], "last": "Takikawa", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Kreis", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Fidler", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Tsung-Yi", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "300--309", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Magic3d: High-resolution text-to-3d content creation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 300-309, 2023.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Microsoft coco: Common objects in context", "authors": [{"first": "Tsung-Yi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Belongie", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Perona", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2014, "venue": "Computer Vision-ECCV 2014: 13th European Conference", "volume": "", "issue": "", "pages": "740--755", "other_ids": {}, "num": null, "urls": [], "raw_text": "Tsung<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> <PERSON>. Microsoft coco: Common objects in context. In Computer Vision-ECCV 2014: 13th European Conference, Zurich, Switzerland, September 6-12, 2014, Proceedings, Part V 13, pages 740-755. Springer, 2014.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Compositional visual generation with composable diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Antonio", "middle": [], "last": "Torralba", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "European Conference on Computer Vision", "volume": "", "issue": "", "pages": "423--439", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Compositional visual generation with composable diffusion models. In European Conference on Computer Vision, pages 423-439. Springer, 2022.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Glide: Towards photorealistic image generation and editing with text-guided diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2112.10741"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Glide: Towards photorealistic image generation and editing with text-guided diffusion models. arXiv preprint arXiv:2112.10741, 2021.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "<PERSON>, and <PERSON>. Energy-based cross attention for bayesian context update in text-to-image diffusion models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Park", "suffix": ""}, {"first": "Jeongsol", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Energy-based cross attention for bayesian context update in text-to-image diffusion models. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Understanding the latent space of diffusion models through the lens of riemannian geometry", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Park", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Jaewoong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Uh", "suffix": ""}], "year": 2023, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "24129--24142", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Understanding the latent space of diffusion models through the lens of riemannian geometry. Advances in Neural Information Processing Systems, 36:24129-24142, 2023.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Scalable diffusion models with transformers", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Saining", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "4195--4205", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. Scalable diffusion models with transformers. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 4195-4205, 2023.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Wavelet diffusion models are fast and scalable image generators", "authors": [{"first": "<PERSON>o", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>uan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Tran", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "10199--10208", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Wavelet diffusion models are fast and scalable image generators. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 10199- 10208, 2023.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Sdxl: Improving latent diffusion models for high-resolution image synthesis", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Zion", "middle": [], "last": "English", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.01952"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Sdxl: Improving latent diffusion models for high-resolution image synthesis. arXiv preprint arXiv:2307.01952, 2023.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Dreamfusion: Text-to-3d using 2d diffusion", "authors": [{"first": "<PERSON>", "middle": [], "last": "Poole", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Mildenhall", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2209.14988"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Dreamfusion: Text-to-3d using 2d diffusion. arXiv preprint arXiv:2209.14988, 2022.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Learning transferable visual models from natural language supervision", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Wook"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hall<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "8748--8763", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Learning transferable visual models from natural language supervision. In International conference on machine learning, pages 8748-8763. PMLR, 2021.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Hierarchical text-conditional image generation with clip latents", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "1", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2204.06125"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Hierarchical text-conditional image generation with clip latents. arXiv preprint arXiv:2204.06125, 1(2):3, 2022.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Hopfield networks is all you need", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Schäfl", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Adler", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2008.02217"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Hopfield networks is all you need. arXiv preprint arXiv:2008.02217, 2020.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "High-resolution image synthesis with latent diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ommer", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "10684--10695", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. High-resolution image synthesis with latent diffusion models. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 10684-10695, 2022.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Palette: Image-to-image diffusion models", "authors": [{"first": "<PERSON>t<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Fleet", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "ACM SIGGRAPH 2022 conference proceedings", "volume": "", "issue": "", "pages": "1--10", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Palette: Image-to-image diffusion models. In ACM SIGGRAPH 2022 conference proceedings, pages 1-10, 2022.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Photorealistic text-toimage diffusion models with deep language understanding", "authors": [{"first": "<PERSON>t<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON><PERSON><PERSON>"], "last": "Lopes", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in neural information processing systems", "volume": "35", "issue": "", "pages": "36479--36494", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Photorealistic text-to- image diffusion models with deep language understanding. Advances in neural information processing systems, 35:36479-36494, 2022.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Retrieval-augmented score distillation for text-to-3d generation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Minseop", "middle": [], "last": "Kwak", "suffix": ""}, {"first": "Doyup", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2402.02972"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>. Retrieval-augmented score distillation for text-to-3d generation. arXiv preprint arXiv:2402.02972, 2024.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Make-a-video: Text-to-video generation without text-video data", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Polyak", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "An", "suffix": ""}, {"first": "Songyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qiyuan", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Oron", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2209.14792"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Make-a-video: Text-to-video generation without text-video data. arXiv preprint arXiv:2209.14792, 2022.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Denoising diffusion implicit models", "authors": [{"first": "Jiaming", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.02502"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Denoising diffusion implicit models. arXiv preprint arXiv:2010.02502, 2020.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Score-based generative modeling through stochastic differential equations", "authors": [{"first": "<PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Poole", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2011.13456"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Score-based generative modeling through stochastic differential equations. arXiv preprint arXiv:2011.13456, 2020.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Attention is all you need", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "Llion", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "Illia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Score jacobian chaining: Lifting pretrained 2d diffusion models for 3d generation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "Yeh", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "12619--12629", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Score jacobian chaining: Lifting pretrained 2d diffusion models for 3d generation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 12619-12629, 2023.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Prolificdreamer: High-fidelity and diverse text-to-3d generation with variational score distillation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yikai", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Fan", "middle": [], "last": "Bao", "suffix": ""}, {"first": "Chongxuan", "middle": [], "last": "Li", "suffix": ""}, {"first": "Hang", "middle": [], "last": "Su", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Prolificdreamer: High-fidelity and diverse text-to-3d generation with variational score distillation. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Video probabilistic diffusion models in projected latent space", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "18456--18466", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Video probabilistic diffusion models in projected latent space. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 18456-18466, 2023.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Adding conditional control to text-to-image diffusion models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Agrawala", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "3836--3847", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Adding conditional control to text-to-image diffusion models. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 3836-3847, 2023.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "The unreasonable effectiveness of deep features as a perceptual metric", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Isola", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "586--595", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. The unreasonable effectiveness of deep features as a perceptual metric. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 586-595, 2018.", "links": null}}, "ref_entries": {"FIGREF0": {"uris": null, "fig_num": "1", "type_str": "figure", "text": "Figure 1: Teaser. (a) Images sampled from vanilla SDXL [35] without any guidance. (b) Images sampled with Smoothed Energy Guidance (Ours). ∅ denotes that there is no condition given. With various input conditions, and even without any, SEG supports the diffusion model in generating plausible and high-quality images without any training.", "num": null}, "FIGREF1": {"uris": null, "fig_num": null, "type_str": "figure", "text": "1 and 3.2 and under reasonable assumptions, we observe that | det(H)| > | det( H)|, which implies that the minimization step is performed on a smoother energy landscape with attenuated Gaussian curvature. The full proof is in Appendix A.3.", "num": null}, "FIGREF2": {"uris": null, "fig_num": "2", "type_str": "figure", "text": "Figure 2: Unconditional generation using SEG.", "num": null}, "FIGREF3": {"uris": null, "fig_num": "3", "type_str": "figure", "text": "Figure 3: Text-conditional generation using SEG.", "num": null}, "FIGREF4": {"uris": null, "fig_num": "5", "type_str": "figure", "text": "Figure 5: Qualitative comparison of SEG with vanilla SDXL[35], SAG[17], and PAG[1].", "num": null}, "FIGREF6": {"uris": null, "fig_num": "6", "type_str": "figure", "text": "Figure 6: Ablation study on γ seg and σ.", "num": null}, "FIGREF8": {"uris": null, "fig_num": "7", "type_str": "figure", "text": "Figure 7: Pipeline of SEG. (a) Original sampling process, self-attention weights, and the corresponding energy landscape. (b) Our modified sampling process with blurred queries where σ ∈ (0, ∞), inducing blurred attention weights and the corresponding smoothed energy landscape. (c) A conceptual figure of γ seg . Note that since the guidance linearly extrapolates predictions from (a) and (b), a high guidance scale causes samples to be out of the manifold.", "num": null}, "FIGREF9": {"uris": null, "fig_num": "10", "type_str": "figure", "text": "Figure 10: Conditional generation using ControlNet [51] and SEG.", "num": null}, "FIGREF10": {"uris": null, "fig_num": "11", "type_str": "figure", "text": "Figure 11: Conditional generation using ControlNet [51] and SEG.", "num": null}, "FIGREF11": {"uris": null, "fig_num": "12", "type_str": "figure", "text": "Figure 12: Comparison between query and key blur across different values of σ.", "num": null}, "FIGREF12": {"uris": null, "fig_num": "13", "type_str": "figure", "text": "Figure 13: Unconditional generation using SEG.", "num": null}, "FIGREF13": {"uris": null, "fig_num": "15", "type_str": "figure", "text": "Figure 15: Experiment on the combination of SEG and CFG. γ seg is fixed to 3.0. The prompt is \"a friendly robot helping an old lady cross the street.\" Without causing saturation or significant changes in the general structure, SEG improves the image quality.", "num": null}, "FIGREF14": {"uris": null, "fig_num": "16", "type_str": "figure", "text": "Figure 16: Experiment on the combination of SEG and CFG. γ seg is fixed to 3.0. The prompt is \"a skateboarding turtle zooming through a mini city made of Legos.\"", "num": null}, "FIGREF15": {"uris": null, "fig_num": "1718", "type_str": "figure", "text": "Figure 17: Experiment on the combination of SEG and CFG. γ seg is fixed to 3.0. The prompt is \"a group of puppies playing soccer with a ball of yarn.\"", "num": null}, "FIGREF16": {"uris": null, "fig_num": "1920", "type_str": "figure", "text": "Figure 19: Experiment on the combination of SEG and CFG. γ seg is fixed to 3.0. The prompt is \"a baby elephant learning to paint with its trunk in an art studio.\"", "num": null}, "TABREF1": {"type_str": "table", "content": "<table><tr><td>Metric</td><td>Vanilla SDXL [35]</td><td>1</td><td>2</td><td>SEG 5</td><td>10</td><td>∞</td></tr><tr><td>FID↓</td><td>53.423</td><td colspan=\"5\">48.284 41.784 33.819 29.325 26.169</td></tr><tr><td>CLIP Score↑</td><td>0.271</td><td>0.273</td><td>0.278</td><td>0.285</td><td>0.290</td><td>0.292</td></tr><tr><td>LPIPSvgg ↓</td><td>-</td><td>0.361</td><td>0.410</td><td>0.449</td><td>0.472</td><td>0.493</td></tr><tr><td>LPIPSalex ↓</td><td>-</td><td>0.295</td><td>0.347</td><td>0.390</td><td>0.416</td><td>0.440</td></tr></table>", "text": "Text-conditional sampling with different σ.", "html": null, "num": null}, "TABREF2": {"type_str": "table", "content": "<table><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: results? Answer: [Yes] Justification: The paper specify all the training and test details. Guidelines: • The answer NA means that the paper does not include experiments. • The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material. 7. Experiment Statistical Significance Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [Yes] Justification: The paper reports statistical information. Guidelines: • The answer NA means that the paper does not include experiments. • The authors should answer \"Yes\" if the results are accompanied by error bars, confi-dence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). Question: For each experiment, does the paper provide sufficient information on the com-puter resources (type of compute workers, memory, time of execution) needed to reproduce the experiments? Answer: [Yes] Justification: The paper provide sufficient information on the computer resources. Guidelines: • Answer: [Yes] Justification: The research conducted in the paper conform with the NeurIPS Code of Ethics. Guidelines: • The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics. • If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consid-eration due to laws or regulations in their jurisdiction). 10. Broader Impacts Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed? • 8. Experiments Compute Resources Answer: [Yes]</td></tr><tr><td>Answer: [Yes]</td></tr></table>", "text": "The paper provide open access to the data and code. Guidelines:• The answer NA means that paper does not include experiments requiring code.• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).• Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.6. Experimental Setting/DetailsQuestion: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper). 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?", "html": null, "num": null}}}}