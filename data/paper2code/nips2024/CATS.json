{"paper_id": "CATS", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:13:32.602394Z"}, "title": "Are Self-Attentions Effective for Time Series Forecasting?", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Seoul National University", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON>se<PERSON>", "middle": [], "last": "Park", "suffix": "", "affiliation": {"laboratory": "", "institution": "Seoul National University", "location": {}}, "email": "<EMAIL>"}, {"first": "Jaewook", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Seoul National University", "location": {}}, "email": "<EMAIL>"}, {"first": "Hoki", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Chung-Ang University", "location": {}}, "email": "<EMAIL>"}], "year": "", "venue": null, "identifiers": {}, "abstract": "Time series forecasting is crucial for applications across multiple domains and various scenarios. Although Transformers have dramatically advanced the landscape of forecasting, their effectiveness remains debated. Recent findings have indicated that simpler linear models might outperform complex Transformer-based approaches, highlighting the potential for more streamlined architectures. In this paper, we shift the focus from evaluating the overall Transformer architecture to specifically examining the effectiveness of self-attention for time series forecasting. To this end, we introduce a new architecture, Cross-Attention-only Time Series transformer (CATS), that rethinks the traditional transformer framework by eliminating self-attention and leveraging cross-attention mechanisms instead. By establishing future horizon-dependent parameters as queries and enhanced parameter sharing, our model not only improves long-term forecasting accuracy but also reduces the number of parameters and memory usage. Extensive experiment across various datasets demonstrates that our model achieves superior performance with the lowest mean squared error and uses fewer parameters compared to existing models. The implementation of our model is available at: https://github.com/dongbeank/CATS.", "pdf_parse": {"paper_id": "CATS", "_pdf_hash": "", "abstract": [{"text": "Time series forecasting is crucial for applications across multiple domains and various scenarios. Although Transformers have dramatically advanced the landscape of forecasting, their effectiveness remains debated. Recent findings have indicated that simpler linear models might outperform complex Transformer-based approaches, highlighting the potential for more streamlined architectures. In this paper, we shift the focus from evaluating the overall Transformer architecture to specifically examining the effectiveness of self-attention for time series forecasting. To this end, we introduce a new architecture, Cross-Attention-only Time Series transformer (CATS), that rethinks the traditional transformer framework by eliminating self-attention and leveraging cross-attention mechanisms instead. By establishing future horizon-dependent parameters as queries and enhanced parameter sharing, our model not only improves long-term forecasting accuracy but also reduces the number of parameters and memory usage. Extensive experiment across various datasets demonstrates that our model achieves superior performance with the lowest mean squared error and uses fewer parameters compared to existing models. The implementation of our model is available at: https://github.com/dongbeank/CATS.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Time series forecasting plays a critical role within the machine learning society, given its applications ranging from financial forecasting to medical diagnostics. To improve the accuracy of predictions, researchers have extensively explored and developed various models. These range from traditional statistical methods to modern deep learning techniques. Most notably, Transformer [19] has brought about a paradigm shift in time series forecasting, resulting in numerous high-performance models, such as Informer [29] , Autoformer [23] , Pyraformer [11] , FEDformer [31] , and Crossformer [28] . This line of work establishes new benchmarks for high performance in time series forecasting. However, <PERSON><PERSON> et al. [26] have raised questions about the effectiveness of Transformer-based time series forecasting models especially for long term time series forecasting. Specifically, their experiments demonstrated that simple linear models could outperform these Transformer-based approaches, thereby opening new avenues for research into simpler architectural frameworks. Indeed, the following studies [12, 6] have further validated that these linear models can be enhanced by incorporating additional features. Despite these developments, the effectiveness of each components in Transformer architecture in time series forecasting remains a subject of debate. <PERSON><PERSON> et al. [14] introduced an encoder-only Transformer that utilizes patching rather than point-wise input tokens, which exhibited improved performance compared to linear models. <PERSON><PERSON> et al. [26] also highlighted potential shortcomings in simpler linear Figure 1 : Experimental results illustrating the mean squared error (MSE) and the number of parameters with varying input sequence lengths on ETTm1. Each bubble represents a different model, with the bubble size indicating the number of parameters in millions-larger bubbles denote models with more parameters. Our model consistently shows the lowest MSE (i.e., best performance) with fewer parameters even for longer input sequences. The detailed results can be found in Table 5 .", "cite_spans": [{"start": 384, "end": 388, "text": "[19]", "ref_id": "BIBREF19"}, {"start": 516, "end": 520, "text": "[29]", "ref_id": "BIBREF29"}, {"start": 534, "end": 538, "text": "[23]", "ref_id": "BIBREF23"}, {"start": 552, "end": 556, "text": "[11]", "ref_id": "BIBREF11"}, {"start": 569, "end": 573, "text": "[31]", "ref_id": "BIBREF31"}, {"start": 592, "end": 596, "text": "[28]", "ref_id": "BIBREF28"}, {"start": 714, "end": 718, "text": "[26]", "ref_id": "BIBREF26"}, {"start": 1101, "end": 1105, "text": "[12,", "ref_id": "BIBREF12"}, {"start": 1106, "end": 1108, "text": "6]", "ref_id": "BIBREF6"}, {"start": 1371, "end": 1375, "text": "[14]", "ref_id": "BIBREF14"}, {"start": 1551, "end": 1555, "text": "[26]", "ref_id": "BIBREF26"}], "ref_spans": [{"start": 1621, "end": 1622, "text": "1", "ref_id": null}, {"start": 2092, "end": 2093, "text": "5", "ref_id": "TABREF7"}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "networks, such as their inability to capture temporal dynamics at change points [18] compared to Transformers. Consequently, while a streamlined architecture may be beneficial, it is imperative to critically evaluate which elements of the Transformer are necessary and which are not for time series modeling.", "cite_spans": [{"start": 80, "end": 84, "text": "[18]", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In light of these considerations, our study shifts focus from the overall architecture of the Transformer to a more specific question: Are self-attentions effective for time series forecasting? While this question is also noted in [26] , their analysis was limited to substituting attention layers with linear layers, leaving substantial room for potential model design when focusing on Transformers. Furthermore, the issue of temporal information loss (i.e., permutation-invariant and anti-order characteristics of self-attention) is predominantly caused by the use of self-attention rather than the Transformer architecture itself. Therefore, we aim to resolve the issues of self-attention and therefore propose a new forecasting architecture that achieves higher performance with a more efficient structure.", "cite_spans": [{"start": 231, "end": 235, "text": "[26]", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In this paper, we introduce a novel forecasting architecture named Cross-Attention-only Time Series transformer (CATS) that simplifies the original Transformer architecture by eliminating all selfattentions and focusing on the potential of cross-attentions. Specifically, our model establishes future horizon-dependent parameters as queries and treats past time series data as key and value pairs. This allows us to enhance parameter sharing and improve long-term forecasting performance. As shown in Figure 1 , our model shows the lowest mean squared error (i.e., better forecasting performance) even for longer input sequences and with fewer parameters than existing models. Moreover, we demonstrate that this simplified architecture can provide a clearer understanding of how future predictions are derived from past data with individual attention maps for the specific forecasting horizon. Finally, through extensive experiments, we show that our proposed model not only achieves state-of-the-art performance but also requires fewer parameters and less memory consumption compared to previous Transformer models across various time series datasets.", "cite_spans": [], "ref_spans": [{"start": 508, "end": 509, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Time Series Transformers Transformer models [19] have shown effective in various domains [5, 4, 15] , with a novel encoder-decoder structure with self-attention, masked self-attention, and cross-attention. The self-attention mechanism is a key component for extracting semantic correlations between paired elements, even with identical input elements; however, autoregressive inference with self-attention requires quadratic time and memory complexity. Therefore, Informer [29] proposed directly predicting multi-steps, and a line of work, such as Autoformer [23] , FEDformer [31] , and Pyraformer [11] , investigated the complexity issue in time series transformers. Simultaneously, unique properties of time series, such as stationarity [12] , decomposition [23] , frequency features [31] , or cross-dimensional properties [28] were employed to modify the attention layer for forecasting tasks.", "cite_spans": [{"start": 44, "end": 48, "text": "[19]", "ref_id": "BIBREF19"}, {"start": 89, "end": 92, "text": "[5,", "ref_id": "BIBREF5"}, {"start": 93, "end": 95, "text": "4,", "ref_id": "BIBREF4"}, {"start": 96, "end": 99, "text": "15]", "ref_id": "BIBREF15"}, {"start": 473, "end": 477, "text": "[29]", "ref_id": "BIBREF29"}, {"start": 559, "end": 563, "text": "[23]", "ref_id": "BIBREF23"}, {"start": 576, "end": 580, "text": "[31]", "ref_id": "BIBREF31"}, {"start": 598, "end": 602, "text": "[11]", "ref_id": "BIBREF11"}, {"start": 739, "end": 743, "text": "[12]", "ref_id": "BIBREF12"}, {"start": 760, "end": 764, "text": "[23]", "ref_id": "BIBREF23"}, {"start": 786, "end": 790, "text": "[31]", "ref_id": "BIBREF31"}, {"start": 825, "end": 829, "text": "[28]", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Recently, researchers have investigated the essential architecture in Transformers to capture long-term dependencies. PatchTST [14] became a de-facto standard Transformer model by patching the time series input in a channel-independence manner, which is widely used in following Transformerbased forecasting models [13, 7] . On the other hand, <PERSON> et al. [3] emphasized the importance of decoder-only forecasting models, while they focused on zero-shot using pre-trained language models. However, none of them have investigated the importance of cross-attention for time series forecasting.", "cite_spans": [{"start": 127, "end": 131, "text": "[14]", "ref_id": "BIBREF14"}, {"start": 315, "end": 319, "text": "[13,", "ref_id": "BIBREF13"}, {"start": 320, "end": 322, "text": "7]", "ref_id": "BIBREF7"}, {"start": 355, "end": 358, "text": "[3]", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "Temporal Information Encoding Fixed temporal order in time series is the distinct property of time series, in contrast to the language domain where semantic information does not heavily depend on the word ordering [4] . Thus, some researchers have used learnable positional encoding in Transformers to embed time-dependent properties [9, 23] . However, <PERSON><PERSON> et al. [26] first argued that self-attention is not suitable for time series due to its permutation invariant and anti-order properties. While they focus on building complex representations, they are inefficient in maintaining the original context of historical and future values. They rather proposed linear models without any embedding layer and demonstrated that it can achieve better performance than Transformer models, particularly showing robust performance to long input sequences. Recent linear time series models outperformed previous Transformer models with simple architectures by focusing on pre-processing and frequencybased properties [10, 2, 21] . On the other hand, <PERSON><PERSON> et al. [22] investigated the new line of works of time-index models, which try to model the underlying dynamics with given time stamps. These related works imply that preserving the order of time series sequences plays a crucial role in time series forecasting.", "cite_spans": [{"start": 214, "end": 217, "text": "[4]", "ref_id": "BIBREF4"}, {"start": 334, "end": 337, "text": "[9,", "ref_id": "BIBREF9"}, {"start": 338, "end": 341, "text": "23]", "ref_id": "BIBREF23"}, {"start": 365, "end": 369, "text": "[26]", "ref_id": "BIBREF26"}, {"start": 1008, "end": 1012, "text": "[10,", "ref_id": "BIBREF10"}, {"start": 1013, "end": 1015, "text": "2,", "ref_id": "BIBREF2"}, {"start": 1016, "end": 1019, "text": "21]", "ref_id": "BIBREF21"}, {"start": 1052, "end": 1056, "text": "[22]", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "3 Revisiting Self-Attention in Time Series Forecasting Motivation of Self-Attention Removal Following the concerns about the effectiveness of selfattention on temporal information preservation [26] , we conduct an experiment using PatchTST [14] . We consider three variations of the PatchTST model: the original PatchTST with overlapping patches with length 16 and stride 8 (Fig. 2a ); a modified PatchTST with non-overlapping patches with length 24 (Fig. 2b ); and a version where self-attention is replaced by a linear embedding layer, using non-overlapping patches with length 24 (Fig. 2c ). This setup allows us to isolate the effects of self-attention on temporal information preservation, while controlling for the impact of patch overlap. Compared to the original PatchTST (Fig. 2a ), both non-overlapping versions (Fig. 2b and Fig. 2c ) show more vivid patterns. The version with linear embedding (Fig. 2c ) demonstrates the clearest capture of temporal information, suggesting that the self-attention mechanism itself may not be necessary for capturing temporal information.", "cite_spans": [{"start": 193, "end": 197, "text": "[26]", "ref_id": "BIBREF26"}, {"start": 240, "end": 244, "text": "[14]", "ref_id": "BIBREF14"}], "ref_spans": [{"start": 380, "end": 382, "text": "2a", "ref_id": "FIGREF2"}, {"start": 456, "end": 458, "text": "2b", "ref_id": "FIGREF2"}, {"start": 589, "end": 591, "text": "2c", "ref_id": "FIGREF2"}, {"start": 786, "end": 788, "text": "2a", "ref_id": "FIGREF2"}, {"start": 828, "end": 830, "text": "2b", "ref_id": "FIGREF2"}, {"start": 840, "end": 842, "text": "2c", "ref_id": "FIGREF2"}, {"start": 911, "end": 913, "text": "2c", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Related Work", "sec_num": "2"}, {"text": "In Table 1 , we summarize the forecasting performance of the original PatchTST (Fig. 2a ) and PatchTST without self-attention (Fig. 2c ). PatchTST without self-attention consistently improves or maintains performance across all forecasting horizons. Specifically, the original version with self-attention shows lower performance for longer forecast horizons. This result suggests that selfattention may not only be unnecessary for effective time series forecasting but could even hinder Our findings offer new insights into the role of self-attention in time series forecasting. As shown in Fig. 2 and Table 1 , replacing self-attention with a linear layer not only captures clear temporal patterns but also results in significant performance improvements, particularly for longer forecast horizons. These results highlight potential areas for enhancing the handling of temporal information, beyond addressing the well-known concerns regarding computational complexity.", "cite_spans": [], "ref_spans": [{"start": 9, "end": 10, "text": "1", "ref_id": "TABREF0"}, {"start": 85, "end": 87, "text": "2a", "ref_id": "FIGREF2"}, {"start": 132, "end": 134, "text": "2c", "ref_id": "FIGREF2"}, {"start": 596, "end": 597, "text": "2", "ref_id": "FIGREF1"}, {"start": 608, "end": 609, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Output features Input features", "sec_num": null}, {"text": "Output Data Rethinking Transformer Design Given the challenges associated with self-attention in time series forecasting, we propose a fundamental rethinking of the Transformer architecture for this task. Fig. 3 illustrates the differences between existing architectures and our proposed approach. Traditional Transformer architectures (Fig. 3a ) and encoder-only models (Fig. 3b ) rely heavily on self-attention mechanisms, which may lead to temporal information loss. In contrast, <PERSON><PERSON> et al. [26] proposed a simplified linear model, DLinear (Fig. 3c ), which removes all Transformer-based components. While this approach reduces computational load and potentially avoids some temporal information loss, it may struggle to capture complex temporal dependencies.", "cite_spans": [{"start": 495, "end": 499, "text": "[26]", "ref_id": "BIBREF26"}], "ref_spans": [{"start": 210, "end": 211, "text": "3", "ref_id": "FIGREF3"}, {"start": 342, "end": 344, "text": "3a", "ref_id": "FIGREF3"}, {"start": 377, "end": 379, "text": "3b", "ref_id": "FIGREF3"}, {"start": 550, "end": 552, "text": "3c", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Input Data", "sec_num": null}, {"text": "Learnable Queries", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Cross -Attention", "sec_num": null}, {"text": "To address these challenges while preserving the advantages of Transformer architectures, we propose the Cross-Attention-only Time Series transformer (CATS), depicted in Fig. 3d . Our approach removes all self-attention layers and focuses solely on cross-attention, aiming to better capture temporal dependencies while maintaining the structural advantages of the transformer architecture.", "cite_spans": [], "ref_spans": [{"start": 175, "end": 177, "text": "3d", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Cross -Attention", "sec_num": null}, {"text": "In the following section, we will introduce our CATS model in detail, explaining our key innovations including a novel use of cross-attention, efficient parameter sharing, and adaptive masking techniques.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Cross -Attention", "sec_num": null}, {"text": "4 Proposed Methodology By focusing solely on cross-attention, our approach allows us to maintain the periodic properties of time series, which self-attention, with its permutation-invariant and anti-order characteristics, struggles to capture. Furthermore, we leverage advanced architectural designs of time series transformers, such as patching [14] , which linear models cannot utilize. The following subsections provide detailed descriptions of each component of our CATS model, explaining how these elements work together to address the challenges of time series forecasting.", "cite_spans": [{"start": 346, "end": 350, "text": "[14]", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "Cross -Attention", "sec_num": null}, {"text": "Cross-Attention via Future as Query Similar to self-attention, the cross-attention mechanism employs three elements: key, query, and value. The distinctive feature of cross-attention is that the query originates from a different source than the key or value. Generally, the query component aims to identify the most relevant information among the keys and uses it to extract crucial data from the values [1, 27]. In the realm of time series forecasting, where predictions are often made for a specific target horizon-such as forecasting 10 steps ahead. Therefore, within this concept of forecasting, we argue that each future horizon should be regarded as a question, i.e., an independent query.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Cross -Attention", "sec_num": null}, {"text": "To implement this, we establish horizon-dependent parameters as learnable queries. As shown in Fig. 4 , we begin by creating parameters for the specified forecasting horizon. For each of these virtualized parameters, we assign a fixed number of parameters to represent the corresponding horizon as learnable queries q. For example, q i is a horizon-dependent query at L + i. When patching is applied, these queries are then processed independently; each learnable query q ∈ R P is first fed into the embedding layer, and then fed into the multi-head attention with the embedded input time series patches as the key and value.", "cite_spans": [], "ref_spans": [{"start": 100, "end": 101, "text": "4", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Cross -Attention", "sec_num": null}, {"text": "Based on these new query parameters, we can utilize a cross-attention-only structure in the decoder, resulting in an advantage in efficiency. In Pyraformer. However, since these two models have an encoder-decoder and a relatively huge amount of parameters, they require 10x and 4x computational times than ours, respectively.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Cross -Attention", "sec_num": null}, {"text": "Parameter Sharing across Horizons One of the strongest benefits of cross-attention via future horizon as a query q is that each cross-attention is only calculated on the values from a single forecasting horizon and the input time series. Mathematically, for a prediction of future value xL+i can be expressed as a function solely dependent on the past samples X = [x 1 , ..., x L ] and q i , independent of q j for all i ̸ = j or i and j are not in the same patch.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Cross -Attention", "sec_num": null}, {"text": "This independent forecasting mechanism offers a notable advantage; a higher level of parameter sharing. As demonstrated in [14] , significant reductions in the required number of parameters can be achieved in time series forecasting through parameter sharing between inputs or patches, enhancing computational efficiency. Regarding this, we propose parameter sharing across all possible layers -the embedding layer, multi-head attention, and projection layer -for every horizon-dependent query q. In other words, all horizon queries q 1 , . . . , q T or q 1 , . . . , q N T share the same embedding layer used for the input time series x 1 , . . . , x L or patches p 1 , . . . , p N L before proceeding to the cross-attention layer, respectively. Furthermore, to maximize the parameter sharing, we also propose cross-dimension sharing that uses the same query parameters for all dimensions.", "cite_spans": [{"start": 123, "end": 127, "text": "[14]", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "Cross -Attention", "sec_num": null}, {"text": "For the multi-head attention and projection layers, we apply the same algorithm across horizons. Notably, unlike the approach in PatchTST [14] , we also share the projection layer for each prediction. Specifically, PatchTST, being an encoder-only model, employs a fully connected layer as the projection layer for the encoder outputs P ∈ R D×N L , resulting in (D × N L ) × T parameters. In contrast, our model first processes raw queries q = [q 1 , . . . , q N T ] ∈ R P ×N T . These queries are then embedded through the cross-attention mechanism, resulting in Q = [q 1 , . . . , q N T ] ∈ R D×N T . The final projection uses shared parameters W ∈ R P ×D , producing an output W Q ∈ R P ×N T . Thus, our number of parameters for this projection becomes P × D, which is not proportionally increasing to T . This approach significantly reduces time complexity during both the training and inference phases. In Table 3 , we outline the impact of parameter sharing across different forecasting horizons. In contrast to the model without parameter sharing, which exhibits a rapid increase in parameters as the forecasting horizon extends, our model, which shares all layers including the projection layer, maintains a nearly consistent number of parameters.", "cite_spans": [{"start": 138, "end": 142, "text": "[14]", "ref_id": "BIBREF14"}], "ref_spans": [{"start": 916, "end": 917, "text": "3", "ref_id": "TABREF4"}], "eq_spans": [], "section": "Cross -Attention", "sec_num": null}, {"text": "Additionally, all operations, including embedding and multi-head attention, are performed independently for each learnable query. This implies that the forecast for a specific horizon does not depend on other horizons. Such an approach allows us to generate distinct attention maps for each forecasting horizon, providing a clear understanding of how each prediction is derived. Please refer to Section 5.5.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Cross -Attention", "sec_num": null}, {"text": "Parameter sharing across horizons enhances the efficiency of our proposed architecture and simplifies the model. However, we observed that a high degree of parameter sharing could lead to overfitting to the keys and values (i.e., past time series data), rather than the queries (i.e., forecasting horizon). Specifically, the model may converge to generate similar or identical predictions, xL+i and xL+j , despite receiving different horizon queries, q i and q j (i.e., the target horizons differ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Query-Adaptive Masking", "sec_num": null}, {"text": "Therefore, to ensure the model focuses on each horizon-dependent query q, we introduce a new technique that masks the attention outputs. As illustrated in the right-bottom figure of Fig. 4 , for each horizon, we apply a mask to the direct connection from Multi-Head Attention to LayerNorm with a probability p. This mask prevents access to the input time series information, resulting in only the query to influence future value predictions. This selective disconnection, rather than the application of dropout in the residual connections, helps the layers to concentrate more effectively on the forecasting queries. We note that this approach can be related to stochastic depth in residual networks [8] . The stochastic depth technique has proven effective across various tasks, such as vision tasks [17, 25] . To the best of our knowledge, this is the first application of stochastic depth in Transformers for time series forecasting. A detailed analysis of query-adaptive masking can be found in Appendix.", "cite_spans": [{"start": 700, "end": 703, "text": "[8]", "ref_id": "BIBREF8"}, {"start": 801, "end": 805, "text": "[17,", "ref_id": "BIBREF17"}, {"start": 806, "end": 809, "text": "25]", "ref_id": "BIBREF25"}], "ref_spans": [{"start": 187, "end": 188, "text": "4", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Query-Adaptive Masking", "sec_num": null}, {"text": "In summary, the framework described in this section, including cross-attention via future as query, parameter sharing across horizons, and query-adaptive masking, is named the Cross-Attention-only Time Series transformer (CATS).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Query-Adaptive Masking", "sec_num": null}, {"text": "In this section, we provide extensive experiments to provide the benefits of our proposed framework, CATS, including forecasting performance and computational efficiency. To this end, we use 7 different real-world datasets and 9 baseline models. For datasets, we use Electricity, ETT (ETTh1, ETTh2, ETTm1, and ETTm2), Weather, Traffic, and M4. These datasets are provided in [23] and [24] for time series forecasting benchmark, detailed in Appendix.", "cite_spans": [{"start": 375, "end": 379, "text": "[23]", "ref_id": "BIBREF23"}, {"start": 384, "end": 388, "text": "[24]", "ref_id": "BIBREF24"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "For baselines, we utilize a wide range of various baselines, including the state-of-the-art long-term time series forecasting model TimeMixer [21] , PatchTST [14] , Timesnet [24] , Crossformer [28] , MICN [20] , FiLM [30] , DLinear [26] , Autoformer [23] , and Informer [29] . For both long-term and short-term time series forecasting results, we report performance of our model alongside the results of other models as presented in TimeMixer [21] , ensuring a consistent comparison across all baselines. We used 4 NVIDIA RTX 4090 24GB GPUs with 2 Intel(R) Xeon(R) Gold 5218R CPUs @ 2.10GHz for all experiments.", "cite_spans": [{"start": 142, "end": 146, "text": "[21]", "ref_id": "BIBREF21"}, {"start": 158, "end": 162, "text": "[14]", "ref_id": "BIBREF14"}, {"start": 174, "end": 178, "text": "[24]", "ref_id": "BIBREF24"}, {"start": 193, "end": 197, "text": "[28]", "ref_id": "BIBREF28"}, {"start": 205, "end": 209, "text": "[20]", "ref_id": "BIBREF20"}, {"start": 217, "end": 221, "text": "[30]", "ref_id": "BIBREF30"}, {"start": 232, "end": 236, "text": "[26]", "ref_id": "BIBREF26"}, {"start": 250, "end": 254, "text": "[23]", "ref_id": "BIBREF23"}, {"start": 270, "end": 274, "text": "[29]", "ref_id": "BIBREF29"}, {"start": 443, "end": 447, "text": "[21]", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "To ease comparison, we follow the settings of [21] for long-term forecasting, using various forecast horizons with a fixed 96 input sequence length. Detailed settings are provided in the Appendix. <PERSON><PERSON> et al. [26] observed that many models experience a decline in performance when using long input sequences for time series forecasting. To address this, some approaches have been developed to capture long-term dependencies. For instance, TimeMixer [21] employs linear models with mixed scale, and PatchTST [14] utilizes an encoder network to encode long-term information. However, these models still have computational issues, particularly in terms of escalating memory and parameter requirements. Thus, in this subsection, we provide a comparison between previous models and ours in terms of efficient and robust forecasting for long input sequences.", "cite_spans": [{"start": 46, "end": 50, "text": "[21]", "ref_id": "BIBREF21"}, {"start": 209, "end": 213, "text": "[26]", "ref_id": "BIBREF26"}, {"start": 449, "end": 453, "text": "[21]", "ref_id": "BIBREF21"}, {"start": 507, "end": 511, "text": "[14]", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "Long-term Time Series Forecasting Results", "sec_num": "5.1"}, {"text": "First of all, to provide a fair comparison, we summarize the number of parameters, GPU memory consumption, and forecasting performance of comparison models with varying input lengths. As summarized in Table 5 , existing complex models, such as PatchTST and TimeMixer, suffer from increased parameters and computational burdens when performing forecasting with long input lengths.", "cite_spans": [], "ref_spans": [{"start": 207, "end": 208, "text": "5", "ref_id": "TABREF7"}], "eq_spans": [], "section": "Long-term Time Series Forecasting Results", "sec_num": "5.1"}, {"text": "Although DLinear uses fewer parameters and less GPU memory, its performance is limited due to its linear structure in capturing non-linearity patterns. Considering both performance and efficiency, the proposed model demonstrates robust performance improvement even with longer input lengths. In Appendix, we provide additional experimental results supporting these findings. Furthermore, we conduct a deeper comparison between Transformer-based models. Especially, TimeMixer [21] argues that their model outperforms PatchTST [14] in the setting of long input sequences. Regarding this setting, we also conduct an experiment on L = 512. We summarize the results in Fig. 5 . Among these Transformer-based models, our model achieves the lowest MSE for most forecasting horizons. Moreover, our model requires even a lower number of parameters, GPU memory, and running time. Especially, for parameter efficiency, CATS shows significant differences even on a log scale due to its efficient parameter-sharing. Fig. 5c highlights GPU memory usage across different forecasting horizons. While PatchTST and TimeMixer consume significantly more memory, CATS maintains a low and stable memory consumption, demonstrating superior memory efficiency. In Fig. 5d CATS also consistently achieves lower running times compared to PatchTST and TimeMixer.", "cite_spans": [{"start": 475, "end": 479, "text": "[21]", "ref_id": "BIBREF21"}, {"start": 525, "end": 529, "text": "[14]", "ref_id": "BIBREF14"}], "ref_spans": [{"start": 669, "end": 670, "text": "5", "ref_id": "FIGREF5"}, {"start": 1008, "end": 1010, "text": "5c", "ref_id": "FIGREF5"}, {"start": 1244, "end": 1246, "text": "5d", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Long-term Time Series Forecasting Results", "sec_num": "5.1"}, {"text": "Additionally, we also compare the same factors when we use a longer input length L = 2880. As more input length is used, the forecasting performance of our model outperforms all other models. Most importantly, while the computational complexity increases as input length increases, our model achieves a faster running time, compared to other models with a 512 input sequence length. Overall, these results emphasize the efficiency and performance advantages of our model, particularly in terms of parameter count, memory usage, and running time.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Long-term Time Series Forecasting Results", "sec_num": "5.1"}, {"text": "Table 6 summarizes the averaged results for the M4 dataset, comparing the performance of various models. Our CATS model consistently achieved the best results across all metrics. Notably, CATS reduced MASE by 19.94% compared to PatchTST, a self-attention-only model that suffers from temporal information loss as mentioned in Section 3. CATS, with its cross-attention-only structure, effectively mitigated this issue and captured temporal dependencies more efficiently than previous models. Although TimeMixer, a state-of-the-art linear model, performed well, CATS surpassed it across all metrics. This demonstrates that CATS excels at capturing short-term temporal dependencies, providing superior performance in short-term forecasting tasks. In our propose structure, we mainly use cross-attention rather than self-attention due to the forecastingunfriendly properties of self-attention. To verify the effectiveness of cross-attention in the proposed structure, we replace the cross-attention layers with self-attention layers while maintaining other structures. In Table 7 , we gradually replace the cross-attention with self-attention (SA) among a total of three cross-attention layers. To maintain the original Transformer structure, we set the maximum replacement as two. As shown in this table, we confirm the effectiveness of the cross-attention mechanism compared to using self-attention layers. Specifically, the zero SA, which is our model, shows better performance than using SA for almost all cases except only one case. ", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "6", "ref_id": "TABREF8"}, {"start": 1074, "end": 1075, "text": "7", "ref_id": "TABREF9"}], "eq_spans": [], "section": "Short-term Time Series Forecasting Results", "sec_num": "5.3"}, {"text": "As noted in Section 4.2, in our proposed model, all operations including embedding and multi-head attention are performed independently for each learnable query. In other words, the forecast for a specific horizon does not depend on other horizons. This approach helps us better understand how each prediction is derived. Therefore, in this subsection, we visualize how the proposed model understands the periodic properties.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Explaining Periodic Patterns through Cross-Attention", "sec_num": "5.5"}, {"text": "To provide an easy understanding, we here consider a simple time series forecasting task with data that consists of two independent signals as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Explaining Periodic Patterns through Cross-Attention", "sec_num": "5.5"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x(t) = {x (t mod τ ) } ∞ t=1 , x i ∼ N (0, 1) (i = 0, 1, . . . , τ -1),", "eq_num": "(1)"}], "section": "Explaining Periodic Patterns through Cross-Attention", "sec_num": "5.5"}, {"text": "y(t) = +k if t ≡ 0 (mod S) -k if t ≡ 1 2 S (mod S).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Explaining Periodic Patterns through Cross-Attention", "sec_num": "5.5"}, {"text": "(", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Explaining Periodic Patterns through Cross-Attention", "sec_num": "5.5"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": ")", "eq_num": "2"}], "section": "Explaining Periodic Patterns through Cross-Attention", "sec_num": "5.5"}, {"text": "For prediction, we use an input sequence length L = 48 and a forecasting horizon T = 72 with signals x(t) and y(t) are defined with τ = 24, S = 8, and k = 5. The patch length is set to 4 without overlapping to elucidate the distinct periodic components with 2 attention heads. In Fig. 6 , we illustrate a score map (12×18) of the cross-attention from the trained CATS. Since both patch length and stride are set to 4, each patch will contain exactly one shock value. We observe that the cross-attentions capture the shocks within the signal and the periodicity of the signal in Fig. 6a and Fig. 6b , respectively. Fig. 6a shows that patches an even number of steps before the current patch contain the shocks of the same direction, resulting in higher attention scores, while odd-numbered steps have lower scores. Moreover, the correlation over 24 steps is clearly demonstrated in patches spaced by multiples of 6 steps, as shown in Fig. 6b . This periodic pattern ensures that the attention mechanism effectively captures the periodicity in x(t), reflecting the model's ability to leverage this periodic information for more accurate predictions. In Appendix, we provide a detailed explanation.  and (c, d ) the two pairs with the highest attention scores. We predict 96 steps with input sequence length 96 on ETTm1. The input patches consist of four patches of 24 lengths and one padding patch.", "cite_spans": [], "ref_spans": [{"start": 285, "end": 286, "text": "6", "ref_id": "FIGREF6"}, {"start": 583, "end": 585, "text": "6a", "ref_id": "FIGREF6"}, {"start": 595, "end": 597, "text": "6b", "ref_id": "FIGREF6"}, {"start": 619, "end": 621, "text": "6a", "ref_id": "FIGREF6"}, {"start": 938, "end": 940, "text": "6b", "ref_id": "FIGREF6"}, {"start": 1196, "end": 1196, "text": "", "ref_id": null}, {"start": 1201, "end": 1206, "text": "(c, d", "ref_id": null}], "eq_spans": [], "section": "Explaining Periodic Patterns through Cross-Attention", "sec_num": "5.5"}, {"text": "As shown in Fig. 7c and 7d , the patches with high attention scores exhibit similar temporal patterns, demonstrating the ability of CATS to detect sequential and periodic patterns.", "cite_spans": [], "ref_spans": [{"start": 17, "end": 19, "text": "7c", "ref_id": "FIGREF8"}, {"start": 24, "end": 26, "text": "7d", "ref_id": "FIGREF8"}], "eq_spans": [], "section": "Explaining Periodic Patterns through Cross-Attention", "sec_num": "5.5"}, {"text": "Based on our study, we exploit the advantages of Transformer models in time series forecasting by removing self-attentions and developing a new cross-attention-based architecture. We believe that our model establishes a strong baseline for such forecasting tasks and offers further insights into the complexities of long-term forecasting problems. Our findings provide a reevaluation of self-attentions in this domain, and we hope that future research can critically assess the efficacy and efficiency across various time series analysis tasks. As a limitation, our proposed methods assume channel independence between variables based on the recent work [14] . As the time series data in the real-world are highly correlated, we hope future research can address cross-variate dependency with reduced computation complexity based on the proposed architecture.", "cite_spans": [{"start": 654, "end": 658, "text": "[14]", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "We evaluated the performance using seven datasets commonly used in long-term time series forecasting, including Weather, Traffic, Electricity, ETT (ETTh1, ETTh2, ETTm1, and ETTm2), and M4. These datasets capture a range of periodic characteristics and scenarios that are difficult to predict in the real world, making them highly suitable for tasks, such as long-term time series forecasting, generation, and imputation. Details of these datasets are described in Table 8 . The M4 dataset are provided <PERSON> et al. [24] , while the remaining datasets are provided in <PERSON> et al. [23] . ", "cite_spans": [{"start": 512, "end": 516, "text": "[24]", "ref_id": "BIBREF24"}, {"start": 574, "end": 578, "text": "[23]", "ref_id": "BIBREF23"}], "ref_spans": [{"start": 470, "end": 471, "text": "8", "ref_id": "TABREF10"}], "eq_spans": [], "section": "A Experimental settings A.1 Datasets", "sec_num": null}, {"text": "In every experiment in our paper, following [14] , we fixed the random seed of 2021 to enhance the reproducibility of our experiments. Additionally, following numerous studies in the field of time series forecasting [14] , we fixed the input sequence length L = 96. For the forecasting horizon T , we also used the widely accepted values, i.e., [96, 192, 336, 720 ]. For our model, in all configurations, we adopt the GeGLU activation function [16] between the two linear layers in the feed-forward network for our model. Additionally, we use learnable positional embedding parameters for the input data and omit positional embeddings for learnable queries to avoid redundant parameter learning.", "cite_spans": [{"start": 44, "end": 48, "text": "[14]", "ref_id": "BIBREF14"}, {"start": 216, "end": 220, "text": "[14]", "ref_id": "BIBREF14"}, {"start": 345, "end": 349, "text": "[96,", "ref_id": null}, {"start": 350, "end": 354, "text": "192,", "ref_id": null}, {"start": 355, "end": 359, "text": "336,", "ref_id": null}, {"start": 360, "end": 363, "text": "720", "ref_id": null}, {"start": 444, "end": 448, "text": "[16]", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "A.2 Hyperparameter Settings", "sec_num": null}, {"text": "For the experiments summarized in Table 4 and Table 11 , our model uses three cross-attention layers with embedding size D = 256, number of attention heads H = 32. Specifically, to avoid overfitting on small datasets [14] , we use patch length 48 on the ETTh1 and ETTh2 datasets. Further details on the hyperparameter settings for these experiments are provided in Table 9 . ", "cite_spans": [{"start": 217, "end": 221, "text": "[14]", "ref_id": "BIBREF14"}], "ref_spans": [{"start": 40, "end": 41, "text": "4", "ref_id": "TABREF6"}, {"start": 52, "end": 54, "text": "11", "ref_id": "TABREF5"}, {"start": 371, "end": 372, "text": "9", "ref_id": "TABREF11"}], "eq_spans": [], "section": "A.2 Hyperparameter Settings", "sec_num": null}, {"text": "In Table 5 , we compared models with the number of parameters, GPU memory consumption, and MSE across different input lengths on ETTm1 with varying input sequence lengths. In this section, we provide comprehensive results on longer input sequence lengths L = 512. The detailed parameters can be found in Table 13 and the corresponding experimental results are summarized in Table 14 .", "cite_spans": [], "ref_spans": [{"start": 9, "end": 10, "text": "5", "ref_id": "TABREF7"}, {"start": 310, "end": 312, "text": "13", "ref_id": "TABREF15"}, {"start": 380, "end": 382, "text": "14", "ref_id": "TABREF16"}], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "As with unified hyperparameter settings, we follow the settings of the most recent work [21] to ease comparison. Overall, the experimental results clearly illustrate the superiority of CATS over recent forecasting models across multiple datasets and prediction horizons. CATS consistently shows the lowest Mean Squared Error (MSE) and Mean Absolute Error (MAE) across a variety of datasets and forecast horizons. For instance, on Electricity, at the 96 forecast horizon, CATS achieves the best MSE score of 0.144 and the best MAE score of 0.189, underscoring its accuracy in predicting electrical demand. Regarding GPU memory consumption, we observe that both PatchTST and TimeMixer require significantly more GPU memory as the input length increases. For example, PatchTST's GPU memory usage scales drastically, making it less feasible for long input sequences. TimeMixer also shows an increase in GPU memory consumption, although it is less severe than PatchTST. In contrast, DLinear maintains a relatively constant GPU memory usage, demonstrating its efficiency in terms of computational resources. However, CATS stands out by offering a balanced approach, with moderate GPU memory usage that scales more favorably compared to PatchTST and TimeMixer. This balance between memory efficiency and performance is crucial for practical applications requiring long-term time series forecasting.", "cite_spans": [{"start": 88, "end": 92, "text": "[21]", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "Furthermore, when analyzing the MSE across different input lengths, CATS consistently shows the best performance. It maintains lower MSE compared to other models across all input lengths. This robustness in performance, combined with its efficient parameter and memory usage, highlights the superiority of CATS in long-term time series forecasting tasks. Overall, these results show the advantages of CATS in terms of parameter efficiency, GPU memory consumption, and forecasting accuracy. These findings support the proposed model's potential for practical and scalable time series forecasting solutions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "Table 17 presents the full results on the Traffic dataset. Here, we use the Traffic dataset with a batch size of 8. All GPU memory consumption was measured in a setting using four multi-GPUs. As shown in Table 17 , CATS with a 2880 input sequence length consistently outperforms models with a 512 input sequence length, including PatchTST and TimeMixer. Specifically, CATS demonstrates fewer parameters, lower GPU memory consumption, and faster running speeds. These results highlight the efficiency of CATS with large input sizes. The Traffic dataset, characterized by high-dimensional data, shows a significant reduction in MSE when using longer input sequences.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 8, "text": "17", "ref_id": "TABREF17"}, {"start": 210, "end": 212, "text": "17", "ref_id": "TABREF17"}], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "Table 18 provides the full results on the Electricity dataset. Similar to the Traffic dataset, CATS shows superior efficiency in training, particularly with an input size of 2880, across all cases. Here, we use the Electricity dataset with a batch size of 32. All GPU memory consumption was measured in a setting using four multi-GPUs. In this experiment, CATS with a 512 input sequence length did not use parameter sharing for queries, while CATS with a 2880 input sequence length did. This demonstrates the effectiveness of query parameter sharing when utilizing large amounts of data for training. The results confirm that query sharing among dimensions leads to greater efficiency and improved performance. In this section, we demonstrate the effectiveness of query-adaptive masking compared to dropout, which is a widely adopted technique in Transformer-based forecasting models. We consider four different setups: using only dropout, using query-adaptive masking with fixed probabilities, queryadaptive masking with linearly increasing probabilities, and using both methods simultaneously. As shown in Fig. 8 , the query-adaptive masking shows better forecasting performance and faster converge speed compared to dropout. Applying a gradually increasing masking probability based on the horizon predicted by the query shows slight performance improvements over using a fixed probability or combining with dropout. In contrast, using dropout alone shows noticeable differences in both convergence speed and overall performance. This demonstrates that when multiple inputs with different forecasting horizons share a single model, probabilistic masking is more beneficial for model training than dropout. C Detailed Explanation of Section 5.5", "cite_spans": [], "ref_spans": [{"start": 6, "end": 8, "text": "18", "ref_id": "TABREF18"}, {"start": 1113, "end": 1114, "text": "8", "ref_id": null}], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "In this section, we provide the detailed results of experiments in Section 5.5. We first restate the formulation of two independent signals used in Section 5.5 as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "x(t) = {x (t mod τ ) } ∞ t=1 , x i ∼ N (0, 1) (i = 0, 1, . . . , τ -1), y(t) = +k if t ≡ 0 (mod S) -k if t ≡ 1 2 S (mod S)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": ",", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "We use the model parameters as follows: the patch length is 4 without overlapping, the decoder has 1 layer, and there are 2 attention heads. The signals x(t) and y(t) are defined with τ = 24, S = 8, and k = 5. The visualization of synthetic data is shown in Fig. 9 . We utilize an input sequence length L = 48 and a forecasting horizon T = 72. This setup allows us to generate time series data with distinct periodic components.", "cite_spans": [], "ref_spans": [{"start": 263, "end": 264, "text": "9", "ref_id": "FIGREF11"}], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "In the main paper, Fig. 6 displays a cross-attention score map between the input patch and the output patch derived from this experiment. The left figure presents the attention score of the first attention head, illustrating the model's ability to detect shocks within the signal. The right figure more clearly demonstrates the periodicity of the signal. Given that the patch length and stride are both set to 4, each patch will contain exactly one shock value, either -5 or +5. This is because the shocks occur every 4 steps, alternating between positive and negative shocks. Consequently, the patch immediately preceding the current patch will contain a different shock, leading to lower attention scores due to the differing shock values. In contrast, patches that are an even number of steps before the current patch will contain the same type of shock, resulting in higher attention scores. These points are well illustrated in Fig. 6a , where the varying attention scores correspond to the presence of alternating shocks. This pattern helps to highlight the alternating shock signal within the data.", "cite_spans": [], "ref_spans": [{"start": 24, "end": 25, "text": "6", "ref_id": "FIGREF6"}, {"start": 938, "end": 940, "text": "6a", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "Additionally, if there is a correlation with the series preceding 24 steps, the patches that are 6 steps or multiples of 6 steps before the current patch will exhibit high attention scores due to the periodic nature of the signal x(t). The diagonal formation of the attention scores, which accurately follows a period of 24, is clearly depicted in Fig. 6b , highlighting the model's capability to utilize fixed-period input patches to predict future outcomes. This periodic pattern ensures that the attention mechanism effectively captures the 24-step periodicity in x(t), reflecting the model's ability to leverage this periodic information for more accurate predictions.", "cite_spans": [], "ref_spans": [{"start": 353, "end": 355, "text": "6b", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "This experimental configuration provides a robust framework to evaluate how well our proposed model captures and interprets the underlying patterns in the data, specifically focusing on the alternating shock signal and the periodic nature of the normal signal. This dual emphasis on both the shock signal and the periodicity of the normal signal enhances the interpretability and predictive performance of the model, distinctly demonstrating how the model leverages periodic information to enhance prediction accuracy.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "To push further, we reproduce the experiment of Fig. 7 with other datasets used in forecasting tasks. We illustrate the results of the Weather, Traffic, Electricity, ETTm2, ETTh1, and ETTh2 in Figures 10, 11, 12, 13, 14 , and 15, respectively. For each figure, (a) represents the forecasting results, (b) shows the cross-attention score map, and (c) and (d) illustrate the two pairs with the highest attention scores. For all figures, our attention-based explanation successfully discovers similar periodic patterns. Therefore, we believe that our model has the potential to provide a clearer understanding of the mechanisms underlying forecasting predictions. We hope that future research will continue to explore and expand upon this foundation. • If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.", "cite_spans": [{"start": 193, "end": 204, "text": "Figures 10,", "ref_id": null}, {"start": 205, "end": 208, "text": "11,", "ref_id": "BIBREF11"}, {"start": 209, "end": 212, "text": "12,", "ref_id": "BIBREF12"}, {"start": 213, "end": 216, "text": "13,", "ref_id": "BIBREF13"}, {"start": 217, "end": 219, "text": "14", "ref_id": "BIBREF14"}], "ref_spans": [{"start": 53, "end": 54, "text": "7", "ref_id": "FIGREF7"}], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Performance with Longer Input Sequences", "sec_num": null}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "Answer: [Yes] Justification: We uploaded the source code of our model and experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "• The answer NA means that paper does not include experiments requiring code. Justification: In the main paper and the Appendix, we provide sufficient information on the used computer resources. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "• The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Justification: We confirm that the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [NA] Justification: We confirm that there is no societal impact of the work performed.", "cite_spans": [{"start": 8, "end": 12, "text": "[NA]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Question: For crowdsourcing experiments and research with human subjects, does the paper include the full text of instructions given to participants and screenshots, if applicable, as well as details about compensation (if any)? Answer: [NA] Justification: We confirm that the paper does not involve crowdsourcing nor research with human subjects. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Including this information in the supplemental material is fine, but if the main contribution of the paper involves human subjects, then as much detail as possible should be included in the main paper. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}], "back_matter": [{"text": "7 Acknowledgements This work was partly supported by the Institute of Information & communications Technology Planning & Evaluation (IITP) grant funded by the Korea government (MSIT) (No. RS-2022-II220984, Development of Artificial Intelligence Technology for Personalized Plug-and-Play Explanation and Verification of Explanation) and the National Research Foundation of Korea (NRF) grant funded by the Korean government (MSIT) (No. RS-*************). This work was also supported by the MSIT(Ministry of Science and ICT), Korea, under the ITRC(Information Technology Research Center) support program (IITP-2024-RS-*************) supervised by the IITP.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "acknowledgement", "sec_num": null}, {"text": "The checklist is designed to encourage best practices for responsible machine learning research, addressing issues of reproducibility, transparency, research ethics, and societal impact. Do not remove the checklist: The papers not including the checklist will be desk rejected. The checklist should follow the references and follow the (optional) supplemental material. The checklist does NOT count towards the page limit.Please read the checklist guidelines carefully for information on how to answer these questions. For each question in the checklist:• You should answer [Yes] , [No] , or [NA] .• [NA] means either that the question is Not Applicable for that particular paper or the relevant information is Not Available.• Please provide a short (1-2 sentence) justification right after your answer (even for NA).The checklist answers are an integral part of your paper submission. They are visible to the reviewers, area chairs, senior area chairs, and ethics reviewers. You will be asked to also include it (after eventual revisions) with the final version of your paper, and its final version will be published with the paper.The reviewers of your paper will be asked to use the checklist as one of the factors in their evaluation.While \"[Yes] \" is generally preferable to \"[No] \", it is perfectly acceptable to answer \"[No] \" provided a proper justification is given (e.g., \"error bars are not reported because it would be too computationally expensive\" or \"we were unable to find the license for the dataset we used\"). In general, answering \"[No] \" or \"[NA] \" is not grounds for rejection. While the questions are phrased in a binary way, we acknowledge that the true answer is often more nuanced, so please just use your best judgment and write a justification to elaborate. All supporting evidence can appear either in the main paper or the supplemental material, provided in Appendix. If you answer [Yes] to a question, in the justification please point to the section(s) where related material for the question can be found.IMPORTANT, please:• Delete this instruction block, but keep the section heading \"NeurIPS paper checklist\",• Keep the checklist subsection headings, questions/answers and guidelines below.• Do not modify the questions and only use the provided macros for your answers.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "NeurIPS Paper Checklist", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?Answer: [Yes] Justification: We made the main claims in the abstract and introduction accurately reflect the paper's contributions and scope.Guidelines:• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "cite_spans": [{"start": 130, "end": 135, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?Answer: [Yes] Justification: We discuss the limitations of the work in the Conclusion.Guidelines:• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?Answer: [NA]Justification: The paper does not include theoretical results.Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in Appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?Answer: [Yes] Justification: We uploaded the source code of our model and experiments.", "cite_spans": [{"start": 271, "end": 276, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "Justification: We confirm that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer: [NA]", "sec_num": null}, {"text": "• The answer NA means that the paper poses no such risks.• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?Answer: [Yes]Justification: We confirm that we have cited the original paper that produced the code package or dataset.Guidelines:• The answer NA means that the paper does not use existing assets.• The authors should cite the original paper that produced the code package or dataset.• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets?Answer: [NA]Justification: We confirm that that the paper does not release new assets.Guidelines:• The answer NA means that the paper does not release new assets.• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • At submission time, remember to anonymize your assets (if applicable). You can either create an anonymized URL or include an anonymized zip file.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}], "bib_entries": {"BIBREF1": {"ref_id": "b1", "title": "Crossvit: Cross-attention multiscale vision transformer for image classification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF international conference on computer vision", "volume": "", "issue": "", "pages": "357--366", "other_ids": {}, "num": null, "urls": [], "raw_text": "References [1] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Crossvit: Cross-attention multi- scale vision transformer for image classification. In Proceedings of the IEEE/CVF international conference on computer vision, pages 357-366, 2021.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Tsmixer: An all-mlp architecture for time series forecast-ing", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Sercan", "middle": ["O"], "last": "Arik", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["<PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Tsmixer: An all-mlp architecture for time series forecast-ing. Transactions on Machine Learning Research, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "A decoder-only foundation model for time-series forecasting", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Wei<PERSON>", "middle": [], "last": "Kong", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. A decoder-only foundation model for time-series forecasting. In International Conference on Machine Learning. PMLR, 2024.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Pre-training of deep bidirectional transformers for language understanding", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of NAACL-HLT", "volume": "", "issue": "", "pages": "4171--4186", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Bert: Pre-training of deep bidirectional transformers for language understanding. In Proceedings of NAACL-HLT, pages 4171-4186, 2019.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "Xiaohua", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. An image is worth 16x16 words: Transformers for image recognition at scale. In International Conference on Learning Representations, 2021. URL https://openreview.net/forum?id=YicbFdNTTy.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Tsmixer: Lightweight mlp-mixer model for multivariate time series forecasting", "authors": [{"first": "<PERSON>", "middle": [], "last": "Eka<PERSON>ram", "suffix": ""}, {"first": "Arinda<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Nam", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining", "volume": "", "issue": "", "pages": "459--469", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Tsmixer: Lightweight mlp-mixer model for multivariate time series forecasting. In Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, pages 459-469, 2023.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Moment: A family of open time-series foundation models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Cai", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Moment: A family of open time-series foundation models. In International Conference on Machine Learning, 2024.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Deep networks with stochastic depth", "authors": [{"first": "Gao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["Q"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Computer Vision-ECCV 2016: 14th European Conference", "volume": "", "issue": "", "pages": "646--661", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Deep networks with stochastic depth. In Computer Vision-ECCV 2016: 14th European Conference, Amsterdam, The Netherlands, October 11-14, 2016, Proceedings, Part IV 14, pages 646-661. Springer, 2016.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Enhancing the locality and breaking the memory bottleneck of transformer on time series forecasting", "authors": [{"first": "Shiyang", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yu-<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xifeng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Yan", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> Yan. Enhancing the locality and breaking the memory bottleneck of transformer on time series forecasting. Advances in neural information processing systems, 32, 2019.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Revisiting long-term time series forecasting: An investigation on linear mapping", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Qi", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>n", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.10721"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Revisiting long-term time series forecasting: An investigation on linear mapping. arXiv preprint arXiv:2305.10721, 2023.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Pyraformer: Low-complexity pyramidal attention for long-range time series modeling and forecasting", "authors": [{"first": "Shizhan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>g", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["X"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Dustdar", "suffix": ""}], "year": 2021, "venue": "International conference on learning representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Pyraformer: Low-complexity pyramidal attention for long-range time series modeling and forecasting. In International conference on learning representations, 2021.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Non-stationary transformers: Exploring the stationarity in time series forecasting", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Haixu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "9881--9893", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Non-stationary transformers: Exploring the stationarity in time series forecasting. Advances in Neural Information Processing Systems, 35:9881-9893, 2022.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "itransformer: Inverted transformers are effective for time series forecasting", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Haixu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Lintao", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. itransformer: Inverted transformers are effective for time series forecasting. In The Twelfth International Conference on Learning Representations, 2024. URL https://openreview. net/forum?id=JePfAI8fah.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "A time series is worth 64 words: Long-term forecasting with transformers", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Nam", "middle": ["H"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. A time series is worth 64 words: Long-term forecasting with transformers. In The Eleventh International Conference on Learning Representations, 2023. URL https://openreview.net/forum? id=Jbdc0vTOcol.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Improving language understanding by generative pre-training", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, et al. Improving language understanding by generative pre-training. 2018.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Glu variants improve transformer", "authors": [{"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2002.05202"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>. Glu variants improve transformer. arXiv preprint arXiv:2002.05202, 2020.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Transformer for semantic segmentation", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Cordelia", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Segmenter", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF international conference on computer vision", "volume": "", "issue": "", "pages": "7262--7272", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Segmenter: Transformer for semantic segmentation. In Proceedings of the IEEE/CVF international conference on computer vision, pages 7262-7272, 2021.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "An evaluation of change point detection algorithms", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Van Den B<PERSON>", "suffix": ""}, {"first": "K", "middle": ["I"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2003.06222"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. An evaluation of change point detection algorithms. arXiv preprint arXiv:2003.06222, 2020.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Attention is all you need", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "Llion", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "Illia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Micn: Multi-scale local and global context modeling for long-term series forecasting", "authors": [{"first": "Hui<PERSON>ang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Micn: Multi-scale local and global context modeling for long-term series forecasting. In The Eleventh International Conference on Learning Representations, 2022.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Decomposable multiscale mixing for time series forecasting", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Haixu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Lintao", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Timemixer", "suffix": ""}], "year": 2024, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> ZHO<PERSON>. Timemixer: Decomposable multiscale mixing for time series forecasting. In The Twelfth International Conference on Learning Representations, 2024. URL https: //openreview.net/forum?id=7oLshfEIC2.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Learning deep timeindex models for time series forecasting", "authors": [{"first": "<PERSON>", "middle": [], "last": "Woo", "suffix": ""}, {"first": "Cheng<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Doyen", "middle": [], "last": "Sahoo", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hoi", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "37217--37237", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Learning deep time- index models for time series forecasting. In International Conference on Machine Learning, pages 37217-37237. PMLR, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Autoformer: Decomposition transformers with auto-correlation for long-term series forecasting", "authors": [{"first": "Haixu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in neural information processing systems", "volume": "34", "issue": "", "pages": "22419--22430", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Autoformer: Decomposition trans- formers with auto-correlation for long-term series forecasting. Advances in neural information processing systems, 34:22419-22430, 2021.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Timesnet: Temporal 2d-variation modeling for general time series analysis", "authors": [{"first": "Haixu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "The eleventh international conference on learning representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Timesnet: Temporal 2d-variation modeling for general time series analysis. In The eleventh international conference on learning representations, 2022.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Lite vision transformer with enhanced self-attention", "authors": [{"first": "Cheng<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yilin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "He", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Yuille", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "11998--12008", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Lite vision transformer with enhanced self-attention. In Proceedings of the IEEE/CVF Confer- ence on Computer Vision and Pattern Recognition, pages 11998-12008, 2022.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Are transformers effective for time series forecasting", "authors": [{"first": "<PERSON>ling", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Muxi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "37", "issue": "", "pages": "11121--11128", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Are transformers effective for time series forecasting? In Proceedings of the AAAI conference on artificial intelligence, volume 37, pages 11121-11128, 2023.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Fcaformer: Forward cross attention in hybrid vision transformer", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "6060--6069", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Fcaformer: Forward cross attention in hybrid vision transformer. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 6060-6069, 2023.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Crossformer: Transformer utilizing cross-dimension dependency for multivariate time series forecasting", "authors": [{"first": "Yunhao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Yan", "suffix": ""}], "year": 2022, "venue": "The eleventh international conference on learning representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON>. Crossformer: Transformer utilizing cross-dimension dependency for multivariate time series forecasting. In The eleventh international conference on learning representations, 2022.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Informer: Beyond efficient transformer for long sequence time-series forecasting", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shan<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Wancai", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "35", "issue": "", "pages": "11106--11115", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Informer: Beyond efficient transformer for long sequence time-series forecasting. In Proceedings of the AAAI conference on artificial intelligence, volume 35, pages 11106-11115, 2021.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Film: Frequency improved legendre memory model for long-term time series forecasting", "authors": [{"first": "Tian", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Qingsong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Tao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "12677--12690", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. Film: Frequency improved legendre memory model for long-term time series forecasting. Advances in Neural Information Processing Systems, 35:12677-12690, 2022.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Fedformer: Frequency enhanced decomposed transformer for long-term series forecasting", "authors": [{"first": "Tian", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Qingsong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "27268--27286", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Fedformer: Frequency enhanced decomposed transformer for long-term series forecasting. In International conference on machine learning, pages 27268-27286. PMLR, 2022.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Additional Results for Section", "authors": [{"first": "B", "middle": [], "last": "", "suffix": ""}], "year": null, "venue": "", "volume": "5", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "B.2 Additional Results for Section 5.2", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "We provide additional experimental results to support the findings discussed in Section 5.2. Tables 15 and 16 summarize detailed comparisons of the number of parameters", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "We provide additional experimental results to support the findings discussed in Section 5.2. Tables 15 and 16 summarize detailed comparisons of the number of parameters, GPU memory consumption,", "links": null}}, "ref_entries": {"FIGREF1": {"text": "Figure 2: Absolute values of weights in the final linear layer for different PatchTST variations. The distinct patterns reveal how each model captures temporal information.", "num": null, "fig_num": "2", "type_str": "figure", "uris": null}, "FIGREF2": {"text": "Fig.2illustrates the absolute values of the weights in the final linear layer for these model variations. Compared to the original PatchTST (Fig.2a), both non-overlapping versions (Fig.2band Fig.2c) show more vivid patterns. The version with linear embedding (Fig.2c) demonstrates the clearest capture of temporal information, suggesting that the self-attention mechanism itself may not be necessary for capturing temporal information.", "num": null, "fig_num": "2", "type_str": "figure", "uris": null}, "FIGREF3": {"text": "Figure 3: Illustration of existing time series forecasting architectures and the proposed architecture.", "num": null, "fig_num": "3", "type_str": "figure", "uris": null}, "FIGREF4": {"text": "Figure 4: Illustration on the proposed model architecture. Our model removes all self-attentions from the original Transformer structure and focuses on cross-attentions. To fully utilize the cross-attention, we conceptualize the future horizon as queries and use the input time series (i.e., past time series) as keys and values (Fig. A). This simplified structure enables us to enhance the parameter sharing across forecasting horizons (Fig. B) and make use of query-adaptive masking (Fig. C) for performance.", "num": null, "fig_num": "4", "type_str": "figure", "uris": null}, "FIGREF5": {"text": "Figure5: Efficiency and performance analysis for time series forecasting models. We summarize the forecasting performance, number of parameters, GPU memory consumption, and running time with varying forecasting horizon lengths on Traffic. The running time is averaged from 300 iterations.", "num": null, "fig_num": "5", "type_str": "figure", "uris": null}, "FIGREF6": {"text": "Figure 6: Score map of cross-attentions between input and output patches.", "num": null, "fig_num": "6", "type_str": "figure", "uris": null}, "FIGREF7": {"text": "Figure 7: Illustration of (a) forecasting result, (b) averaged cross-attention score, and (c,d) patches with the highest score on ETTm1. The score map is averaged from all the heads across layers.", "num": null, "fig_num": "7", "type_str": "figure", "uris": null}, "FIGREF8": {"text": "Fig. 7 illustrates (a) forecasting results, (b) a cross-attention score map (5×4) on the ETTm1 dataset,and (c, d) the two pairs with the highest attention scores. We predict 96 steps with input sequence length 96 on ETTm1. The input patches consist of four patches of 24 lengths and one padding patch. As shown in Fig.7c and 7d, the patches with high attention scores exhibit similar temporal patterns, demonstrating the ability of CATS to detect sequential and periodic patterns.", "num": null, "fig_num": "7", "type_str": "figure", "uris": null}, "FIGREF9": {"text": "Figure Comparison of performance with query-adaptive masking with two different probabilities, dropout, and using both query-adaptive masking and dropout. The results of p = 0.1 to 0.7 indicate a probability masking that is linearly increased proportionally to the horizon predicted by the query.", "num": null, "fig_num": null, "type_str": "figure", "uris": null}, "FIGREF10": {"text": "Sum of two input signals", "num": null, "fig_num": null, "type_str": "figure", "uris": null}, "FIGREF11": {"text": "Figure 9: Visualization of input signals for toy experiment.", "num": null, "fig_num": "9", "type_str": "figure", "uris": null}, "FIGREF12": {"text": "Figure 10: Illustration of (a) forecasting result, (b) averaged cross-attention score,and (c,d) patches with the highest score on Weather. The score map is averaged from all the heads across layers.", "num": null, "fig_num": "10", "type_str": "figure", "uris": null}, "FIGREF13": {"text": "Figure 11: Illustration of (a) forecasting result, (b) averaged cross-attention score, and (c,d) patches with the highest score on Traffic. The score map is averaged from all the heads across layers.", "num": null, "fig_num": "111215", "type_str": "figure", "uris": null}, "FIGREF14": {"text": "For example (a) If the contribution is primarily a new algorithm, the paper should make it clear how to reproduce that algorithm. (b) If the contribution is primarily a new model architecture, the paper should describe the architecture clearly and fully. (c) If the contribution is a new model (e.g., a large language model), then there should either be a way to access this model for reproducing the results or a way to reproduce the model (e.g.", "num": null, "fig_num": null, "type_str": "figure", "uris": null}, "TABREF0": {"text": "Effect of self-attention inPatchTST on forecasting performance (MSE) on ETTm1.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td colspan=\"3\">Horizon original w/o self-attn</td></tr><tr><td>96</td><td>0.290</td><td>0.290</td></tr><tr><td>192</td><td>0.332</td><td>0.328</td></tr><tr><td>336</td><td>0.366</td><td>0.359</td></tr><tr><td>720</td><td>0.416</td><td>0.414</td></tr></table>"}, "TABREF1": {"text": "×T with the prediction X = {x L+1 , . . . , xL+T } ∈ R M ×T based on past datasets X = {x 1 , . . . , x L } ∈ R M ×L . Here, T represents the forecasting horizon, L denotes the input sequence length, and M represents the dimension of time series data.In traditional time series transformers, we feed the historical multivariate time series X to embedding layers, resulting in the historical embedding H ∈ R D×L . Here, D is the embedding size. Note that, in channel-independence manners, the multivariate input is considered to separate univariate time series x ∈ R 1×L . With patching[14], univariate time series x transforms into patches p = Patch(x) ∈ R P ×N L where P is the size of each patch and N L is the number of input patches. Similar to non-patching situations, patches are fed to embedding layers P = Embedding(p) ∈ R D×N L .", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td>4.1 Problem Definition and Notations A multivariate time series forecasting task aims to predict future values X = {x L+1 , . . . , x L+T } ∈ R M Masking p 1-p p 1-p</td></tr></table>"}, "TABREF2": {"text": "we summarize the time complexity of recent Transformer models and ours. The results indicate that our method only requires the time complexity of O(LT /P 2 ), where most of the Transformer-based models require O(L 2 ) except FEDformer and", "num": null, "type_str": "table", "html": null, "content": "<table/>"}, "TABREF3": {"text": "Time complexity of Transformer-based models to calculate attention outputs. Time refers to the inference time obtained by averaging 10 runs under L = 96 and T = 720 on Electricity.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method</td><td>Encoder</td><td>Decoder</td><td>Time</td><td>Method</td><td>Encoder</td><td>Decoder</td><td>Time</td></tr><tr><td>Transformer [13]</td><td>O(L 2 )</td><td>O(T (T + L))</td><td colspan=\"2\">10.4ms Informer [29]</td><td>O(L log L)</td><td>O(T (T + log L))</td><td>13.5ms</td></tr><tr><td>Autoformer [23]</td><td colspan=\"4\">O(L log L) O ((L/2 + H) log (L/2 + T )) 24.1ms Pyraformer [11]</td><td>O(L)</td><td>O(T (T + L))</td><td>11.2ms</td></tr><tr><td>FEDformer [31]</td><td>O(L)</td><td>O (L/2 + H)</td><td colspan=\"3\">69.3ms Crossformer [28] O M L 2 /P 2</td><td>O M T (T + L)/P 2</td><td>30.6ms</td></tr><tr><td>PatchTST [14]</td><td>O L 2 /P 2</td><td>-</td><td colspan=\"2\">7.6ms CATS (Ours)</td><td>-</td><td>O LT /P 2</td><td>7.0ms</td></tr></table>"}, "TABREF4": {"text": "Effect of parameter sharing across horizons on the number of parameters for different forecasting horizons on ETTh1.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td colspan=\"3\">Horizon w/ sharing w/o sharing</td></tr><tr><td>96</td><td>355,320</td><td>404,672</td></tr><tr><td>192</td><td>355,416</td><td>552,320</td></tr><tr><td>336</td><td>355,560</td><td>958,112</td></tr><tr><td>720</td><td>355,944</td><td>3,121,568</td></tr></table>"}, "TABREF5": {"text": "", "num": null, "type_str": "table", "html": null, "content": "<table/>"}, "TABREF6": {"text": "Multivariate long-term forecasting results with recent forecasting models and ours for unified hyperparameter settings. The best results are in bold and the second best are underlined. Full results are provided in Appendix.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Models</td><td>CATS</td><td>TimeMixer PatchTST</td><td>Timesnet Crossformer</td><td>MICN</td><td>FiLM</td><td>DLinear</td><td>Autoformer</td><td>Informer</td></tr><tr><td colspan=\"9\">Metric MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE</td></tr></table>"}, "TABREF7": {"text": "Comparison of models with the number of parameters, GPU memory consumption, and MSE across different input sequence lengths on ETTm1. Full results with more diverse input lengths are provided in Appendix.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td/><td/><td/><td>Parameters</td><td/><td/><td/><td>GPU Memory</td><td/><td>MSE</td></tr><tr><td colspan=\"2\">Input Length 336</td><td>720</td><td>1440</td><td>2880</td><td>336</td><td>720</td><td>1440</td><td>2880</td><td>336 720 1440 2880</td></tr><tr><td>PatchTST</td><td colspan=\"9\">4.3M 8.7M (2.0x) 17.0M (4.0x) 33.6M (7.9x) 3.5GB 7.4GB (2.1x) 22.0GB (6.3x) 58.6GB (16.9x) 0.418 0.418 0.420 0.412</td></tr><tr><td>TimeMixer</td><td colspan=\"9\">1.1M 4.1M (3.6x) 14.2M (12.6x) 52.9M (46.8x) 2.9GB 3.9GB (1.3x) 5.9GB (2.0x) 10.3GB (3.6x) 0.428 0.425 0.414 0.472</td></tr><tr><td>DLinear</td><td colspan=\"2\">0.5M 1.0M (2.1x)</td><td>2.1M (4.2x)</td><td colspan=\"4\">4.2M (8.5x) 1.1GB 1.1GB (1.0x) 1.2GB (1.0x)</td><td colspan=\"2\">1.2GB (1.1x) 0.426 0.422 0.401 0.408</td></tr><tr><td>CATS</td><td colspan=\"2\">0.4M 0.4M (1.0x)</td><td>0.4M (1.0x)</td><td colspan=\"4\">0.4M (1.1x) 1.9GB 2.1GB (1.1x) 2.7GB (1.4x)</td><td colspan=\"2\">3.8GB (2.0x) 0.407 0.402 0.399 0.395</td></tr><tr><td colspan=\"8\">5.2 Efficient and Robust Forecasting for Long Input Sequences</td><td/></tr></table>"}, "TABREF8": {"text": "Averaged univariate short-term forecasting results in the M4 dataset. The best results are in bold and the second best are underlined. Full results are presented in Appendix.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td/><td>Models</td><td colspan=\"5\">CATS TimeMixer Timesnet PatchTST MICN FiLM DLinear Autoformer Informer</td></tr><tr><td>Average</td><td colspan=\"2\">SMAPE 11.701 11.723 MASE 1.557 1.559 OWA 0.838 0.840</td><td>11.829 1.585 0.851</td><td>13.152 19.638 14.863 13.639 1.945 5.947 2.207 2.095 0.998 2.279 1.125 1.051</td><td>12.909 1.771 0.939</td><td>14.086 2.718 1.230</td></tr><tr><td colspan=\"5\">5.4 Replacement of Cross-Attention with Self-Attention</td><td/></tr></table>"}, "TABREF9": {"text": "Performance comparison on models with three attention layers. We replace one or more cross-attentions (CA) with self-attentions (SA) in our model. In total, there are three cross-attentions in all settngs and 'Zero SA' stands for our model. The best results are in bold.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Dataset</td><td/><td>Electricity</td><td/><td/><td>ETTm1</td><td/></tr><tr><td>Case</td><td>Zero SA</td><td>One SA</td><td>Two SA</td><td>Zero SA</td><td>One SA</td><td>Two SA</td></tr><tr><td>Metric</td><td colspan=\"6\">MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE</td></tr><tr><td>96</td><td colspan=\"6\">0.126 0.218 0.128 0.220 0.133 0.225 0.283 0.340 0.284 0.338 0.284 0.340</td></tr><tr><td>192</td><td colspan=\"6\">0.144 0.235 0.150 0.238 0.153 0.245 0.319 0.363 0.331 0.373 0.324 0.368</td></tr><tr><td>336</td><td colspan=\"6\">0.159 0.252 0.167 0.257 0.169 0.263 0.351 0.385 0.376 0.401 0.369 0.400</td></tr><tr><td>720</td><td colspan=\"6\">0.194 0.283 0.205 0.293 0.210 0.300 0.400 0.414 0.429 0.437 0.442 0.445</td></tr></table>"}, "TABREF10": {"text": "Details of 13 real-world datasets.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td/><td colspan=\"3\">Dimension Frequency Timesteps</td><td>Information</td><td>Forecasting Horizon</td></tr><tr><td>Weather</td><td>21</td><td>10-min</td><td>52,696</td><td>Weather</td><td>(96, 192, 336, 720)</td></tr><tr><td>Electricity</td><td>321</td><td>Hourly</td><td>17,544</td><td>Electricity</td><td>(96, 192, 336, 720)</td></tr><tr><td>Traffic</td><td>862</td><td>Hourly</td><td>26,304</td><td>Transportation</td><td>(96, 192, 336, 720)</td></tr><tr><td>ETTh1</td><td>7</td><td>Hourly</td><td>17,420</td><td>Temperature</td><td>(96, 192, 336, 720)</td></tr><tr><td>ETTh2</td><td>7</td><td>Hourly</td><td>17,420</td><td>Temperature</td><td>(96, 192, 336, 720)</td></tr><tr><td>ETTm1</td><td>7</td><td>15-min</td><td>69,680</td><td>Temperature</td><td>(96, 192, 336, 720)</td></tr><tr><td>ETTm2</td><td>7</td><td>15-min</td><td>69,680</td><td>Temperature</td><td>(96, 192, 336, 720)</td></tr><tr><td>M4-Quartely</td><td>1</td><td>Quartely</td><td>48000</td><td>Finance</td><td>8</td></tr><tr><td>M4-Monthly</td><td>1</td><td>Monthly</td><td>96000</td><td>Industry</td><td>18</td></tr><tr><td>M4-Yearly</td><td>1</td><td>Yearly</td><td>46000</td><td>Demographic</td><td>6</td></tr><tr><td>M4-Weekly</td><td>1</td><td>Weekly</td><td>718</td><td>Macro</td><td>13</td></tr><tr><td>M4-Daily</td><td>1</td><td>Daily</td><td>8454</td><td>Micro</td><td>14</td></tr><tr><td>M4-Hourly</td><td>1</td><td>Hourly</td><td>828</td><td>Other</td><td>48</td></tr></table>"}, "TABREF11": {"text": "Experimental settings used in Table4and Table11. The complete results for these short-term experiments are presented in Table12, while the full results for the long-term forecasting experiments with a fixed 96 input sequence length are provided in the Appendix inTable 11.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td colspan=\"8\">Metric Layers Embedding Size Query Sharing Input Sequence Length Batch Size Epoch Learning Rate</td></tr><tr><td>Weather</td><td>3</td><td>256</td><td>False</td><td>96</td><td>64</td><td>30</td><td>10 -3</td></tr><tr><td>Electricity</td><td>3</td><td>256</td><td>False</td><td>96</td><td>32</td><td>30</td><td>10 -3</td></tr><tr><td>Traffic</td><td>3</td><td>256</td><td>True</td><td>96</td><td>32</td><td>100</td><td>10 -3</td></tr><tr><td>ETTh1</td><td>3</td><td>256</td><td>False</td><td>96</td><td>256</td><td>10</td><td>10 -3</td></tr><tr><td>ETTh2</td><td>3</td><td>256</td><td>True</td><td>96</td><td>256</td><td>10</td><td>10 -3</td></tr><tr><td>ETTm1</td><td>3</td><td>256</td><td>False</td><td>96</td><td>128</td><td>30</td><td>10 -3</td></tr><tr><td>ETTm2</td><td>3</td><td>256</td><td>True</td><td>96</td><td>128</td><td>30</td><td>10 -3</td></tr></table>"}, "TABREF12": {"text": "Experimental settings of the M4 dataset.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Dataset</td><td colspan=\"7\">Layers Embedding Size Input Sequence Length Batch Size Epoch Patience Learning Rate</td></tr><tr><td>M4-Quartely</td><td>3</td><td>64</td><td>16</td><td>32</td><td>30</td><td>10</td><td>10 -3</td></tr><tr><td>M4-Monthly</td><td>3</td><td>64</td><td>36</td><td>32</td><td>30</td><td>10</td><td>10 -3</td></tr><tr><td>M4-Yearly</td><td>3</td><td>64</td><td>12</td><td>32</td><td>30</td><td>10</td><td>10 -3</td></tr><tr><td>M4-Weekly</td><td>3</td><td>64</td><td>26</td><td>32</td><td>30</td><td>10</td><td>10 -3</td></tr><tr><td>M4-Daily</td><td>3</td><td>64</td><td>28</td><td>32</td><td>30</td><td>10</td><td>10 -3</td></tr><tr><td>M4-Hourly</td><td>3</td><td>128</td><td>96</td><td>32</td><td>30</td><td>10</td><td>10 -3</td></tr></table>"}, "TABREF13": {"text": "Multivariate long-term forecasting results with recent forecasting models and ours for unified hyperparameter settings. The best results are in bold and the second best are underlined.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Models</td><td>CATS</td><td>TimeMixer PatchTST</td><td>Timesnet Crossformer</td><td>MICN</td><td>FiLM</td><td>DLinear</td><td>Autoformer</td><td>Informer</td></tr><tr><td colspan=\"9\">Metric MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE</td></tr></table>"}, "TABREF14": {"text": "Full results of univariate short-term forecasting in the M4 dataset. All forecasting horizons are in[6, 48]. The best results are in bold and the second best are underlined.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td colspan=\"2\">Models</td><td colspan=\"6\">CATS TimeMixer Timesnet PatchTST MICN FiLM DLinear Autoformer Informer</td></tr><tr><td>Quarterly</td><td colspan=\"2\">SMAPE 9.979 MASE 1.164 OWA 0.878</td><td>9.996 1.166 0.825</td><td>10.100 1.182 0.890</td><td>10.644 15.214 12.925 12.145 1.278 1.963 1.664 1.520 0.949 1.407 1.193 1.106</td><td>11.338 1.365 1.012</td><td>11.360 1.401 1.027</td></tr><tr><td>Monthly</td><td colspan=\"3\">SMAPE 12.557 12.605 MASE 0.916 0.919 OWA 0.866 0.869</td><td>12.670 0.933 0.878</td><td>13.399 16.943 15.407 13.514 1.031 1.442 1.298 1.037 0.949 1.265 1.144 0.956</td><td>13.958 1.103 1.002</td><td>14.062 1.141 1.024</td></tr><tr><td>Yearly</td><td colspan=\"3\">SMAPE 13.263 13.206 MASE 2.967 2.916 OWA 0.779 0.776</td><td>13.387 2.996 0.786</td><td>16.463 25.022 17.431 16.965 3.967 7.162 4.043 4.283 1.003 1.667 1.042 1.058</td><td>13.974 3.134 0.822</td><td>14.727 3.418 0.881</td></tr><tr><td>Others</td><td colspan=\"2\">SMAPE 4.560 MASE 3.107 OWA 0.970</td><td>4.564 3.115 0.982</td><td>4.891 3.302 1.035</td><td>6.558 41.985 7.134 6.709 4.511 62.734 5.09 4.953 1.401 14.313 1.553 1.487</td><td>5.485 3.865 1.187</td><td>24.460 20.960 5.879</td></tr><tr><td>Average</td><td colspan=\"3\">SMAPE 11.701 11.723 MASE 1.557 1.559 OWA 0.838 0.840</td><td>11.829 1.585 0.851</td><td>13.152 19.638 14.863 13.639 1.945 5.947 2.207 2.095 0.998 2.279 1.125 1.051</td><td>12.909 1.771 0.939</td><td>14.086 2.718 1.230</td></tr></table>"}, "TABREF15": {"text": "Experimental settings with an input sequence length of 512.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td colspan=\"8\">Metric Layers Embedding Size Query Sharing Input Sequence Length Batch Size Epoch Learning Rate</td></tr><tr><td>Weather</td><td>3</td><td>128</td><td>False</td><td>512</td><td>128</td><td>30</td><td>10 -3</td></tr><tr><td>Electricity</td><td>3</td><td>128</td><td>False</td><td>512</td><td>32</td><td>30</td><td>10 -3</td></tr><tr><td>Traffic</td><td>3</td><td>128</td><td>True</td><td>512</td><td>32</td><td>100</td><td>10 -3</td></tr><tr><td>ETTh1</td><td>3</td><td>256</td><td>False</td><td>512</td><td>128</td><td>10</td><td>10 -3</td></tr><tr><td>ETTh2</td><td>3</td><td>256</td><td>True</td><td>512</td><td>256</td><td>10</td><td>10 -3</td></tr><tr><td>ETTm1</td><td>3</td><td>128</td><td>False</td><td>512</td><td>128</td><td>30</td><td>10 -3</td></tr><tr><td>ETTm2</td><td>3</td><td>256</td><td>True</td><td>512</td><td>128</td><td>30</td><td>10 -3</td></tr></table>"}, "TABREF16": {"text": "Multivariate long-term forecasting results with an input sequence length of 512. The best results are in bold and the second best are underlined. MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAEat the end, the parameters scale linearly with the input length. This highlights a limitation of the Encoder's architecture. On the other hand, TimeMixer's parameters grow almost quadratically as the input length doubles. Similarly, DLinear's parameters increase linearly with the input length. Our proposed model, CATS demonstrates significant efficiency through parameter sharing, where the parameters hardly increase with longer inputs. Notably, from an input length of 336, CATS has fewer parameters than DLinear, showcasing the deep learning model's advantage in detecting inherent patterns in the data.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Models</td><td>CATS</td><td>TimeMixer PatchTST</td><td>Timesnet Crossformer</td><td>MICN</td><td>FiLM</td><td>DLinear</td><td>Autoformer</td><td>Informer</td></tr><tr><td colspan=\"2\">Metric MSE MAE</td><td/><td/><td/><td/><td/><td/><td/></tr></table>"}, "TABREF17": {"text": "Comparison of models with the number of parameters, GPU memory consumption, running speed, and MSE across different forecasting horizon sizes on Traffic. Full results of Fig.5.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td colspan=\"2\">Horizon Models</td><td colspan=\"3\">Paramters Gpu Memory Running Time MSE</td></tr><tr><td/><td>PatchTST</td><td>1,186,272</td><td>28.54GB</td><td>0.1390s/iter 0.360</td></tr><tr><td>96</td><td>TimeMixer CATS (L = 512)</td><td>2,442,961 357,496</td><td>38.12GB 5.81GB</td><td>0.2548s/iter 0.360 0.0533s/iter 0.352</td></tr><tr><td/><td>CATS (L = 2880)</td><td>370,168</td><td>9.79GB</td><td>0.1158s/iter 0.339</td></tr><tr><td/><td>PatchTST</td><td>1,972,800</td><td>28.34GB</td><td>0.1412s/iter 0.379</td></tr><tr><td>192</td><td>TimeMixer CATS (L = 512)</td><td>2,535,505 357,592</td><td>38.13GB 6.73GB</td><td>0.2596s/iter 0.375 0.0571s/iter 0.373</td></tr><tr><td/><td>CATS (L = 2880)</td><td>370,264</td><td>11.10GB</td><td>0.1209s/iter 0.362</td></tr><tr><td/><td>PatchTST</td><td>3,152,592</td><td>28.91GB</td><td>0.1487s/iter 0.392</td></tr><tr><td>336</td><td>TimeMixer CATS (L = 512)</td><td>2,674,321 357,736</td><td>38.69GB 7.46GB</td><td>0.2647s/iter 0.385 0.0584s/iter 0.387</td></tr><tr><td/><td>CATS (L = 2880)</td><td>370,408</td><td>12.72GB</td><td>0.1266s/iter 0.379</td></tr><tr><td/><td>PatchTST</td><td>6,298,704</td><td>29.15GB</td><td>0.1628s/iter 0.432</td></tr><tr><td>720</td><td>TimeMixer CATS (L = 512)</td><td>3,044,497 358,120</td><td>41.17GB 10.10GB</td><td>0.2777s/iter 0.430 0.0734s/iter 0.423</td></tr><tr><td/><td>CATS (L = 2880)</td><td>370,792</td><td>18.40GB</td><td>0.1556s/iter 0.420</td></tr></table>"}, "TABREF18": {"text": "Comparison of models with the number of parameters, GPU memory consumption, running speed, and MSE across different forecasting horizon sizes on Electricity.", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td colspan=\"2\">Horizon Models</td><td colspan=\"3\">Paramters Gpu Memory Running Time MSE</td></tr><tr><td/><td>PatchTST</td><td>1,186,272</td><td>40.36GB</td><td>0.2021s/iter 0.129</td></tr><tr><td>96</td><td>TimeMixer CATS (L = 512)</td><td>2,429,049 388,216</td><td>33.80GB 6.89GB</td><td>0.2118s/iter 0.129 0.0587s/iter 0.126</td></tr><tr><td/><td>CATS (L = 2880)</td><td>370,168</td><td>12.82GB</td><td>0.1653s/iter 0.126</td></tr><tr><td/><td>PatchTST</td><td>1,972,800</td><td>40.39GB</td><td>0.2048s/iter 0.147</td></tr><tr><td>192</td><td>TimeMixer CATS (L = 512)</td><td>2,521,593 419,032</td><td>33.81GB 8.07GB</td><td>0.2212s/iter 0.140 0.0636s/iter 0.144</td></tr><tr><td/><td>CATS (L = 2880)</td><td>370,264</td><td>14.70GB</td><td>0.1725s/iter 0.139</td></tr><tr><td/><td>PatchTST</td><td>3,152,592</td><td>40.42GB</td><td>0.2070s/iter 0.163</td></tr><tr><td>336</td><td>TimeMixer CATS (L = 512)</td><td>2,660,409 465,256</td><td>34.24GB 9.15GB</td><td>0.2314s/iter 0.161 0.0690s/iter 0.159</td></tr><tr><td/><td>CATS (L = 2880)</td><td>370,408</td><td>17.38GB</td><td>0.1839s/iter 0.153</td></tr><tr><td/><td>PatchTST</td><td>6,298,704</td><td>41.40GB</td><td>0.2313s/iter 0.197</td></tr><tr><td>720</td><td>TimeMixer CATS (L = 512)</td><td>3,030,585 588,520</td><td>36.13GB 12.77GB</td><td>0.2478s/iter 0.194 0.0964s/iter 0.194</td></tr><tr><td/><td>CATS (L = 2880)</td><td>370,792</td><td>25.86GB</td><td>0.2262s/iter 0.183</td></tr><tr><td colspan=\"3\">B.3 Ablation Study on Query-adaptive Masking</td><td/><td/></tr></table>"}, "TABREF19": {"text": "• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details.• While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).• Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.6. Experimental Setting/DetailsQuestion: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the The answer NA means that the paper does not include experiments.• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in Appendix, or as supplemental material.7. Experiment Statistical SignificanceQuestion: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [No] Justification: Instead of error bars, following the numerous studies in the field of time series forecasting[14,26], we fixed the random seed and provided the comparable experimental results in the main paper with generally accepted datasets and settings[21].Guidelines:• The answer NA means that the paper does not include experiments.• The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text.8. Experiments Compute ResourcesQuestion: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments?", "num": null, "type_str": "table", "html": null, "content": "<table><tr><td>results?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: We specify all the training and test details (e.g., data splits, hyperparameters,</td></tr><tr><td>how they were chosen, type of optimizer, etc.) necessary to understand the results in</td></tr><tr><td>Appendix.</td></tr><tr><td>Guidelines:</td></tr><tr><td>•</td></tr></table>"}, "TABREF20": {"text": "• According to the NeurIPS Code of Ethics, workers involved in data collection, curation, or other labor should be paid at least the minimum wage in the country of the data collector. 15. Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects Question: Does the paper describe potential risks incurred by study participants, whether such risks were disclosed to the subjects, and whether Institutional Review Board (IRB) approvals (or an equivalent approval/review based on the requirements of your country or institution) were obtained? Answer: [NA] Justification: We confirm that the paper does not involve crowdsourcing nor research with human subjects. Guidelines: • The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper.", "num": null, "type_str": "table", "html": null, "content": "<table/>"}}}}