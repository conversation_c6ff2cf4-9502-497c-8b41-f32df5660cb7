{"paper_id": "xMIL", "title": "xMIL: Insightful Explanations for Multiple Instance Learning in Histopathology", "abstract": "Multiple instance learning (MIL) is an effective and widely used approach for weakly supervised machine learning. In histopathology, MIL models have achieved remarkable success in tasks like tumor detection, biomarker prediction, and outcome prognostication. However, MIL explanation methods are still lagging behind, as they are limited to small bag sizes or disregard instance interactions. We revisit MIL through the lens of explainable AI (XAI) and introduce xMIL, a refined framework with more general assumptions. We demonstrate how to obtain improved MIL explanations using layer-wise relevance propagation (LRP) and conduct extensive evaluation experiments on three toy settings and four real-world histopathology datasets. Our approach consistently outperforms previous explanation attempts with particularly improved faithfulness scores on challenging biomarker prediction tasks. Finally, we showcase how xMIL explanations enable pathologists to extract insights from MIL models, representing a significant advance for knowledge discovery and model debugging in digital histopathology. Codes are available at: https://github.com/bifold-pathomics/xMIL.", "pdf_parse": {"paper_id": "xMIL", "abstract": [{"text": "Multiple instance learning (MIL) is an effective and widely used approach for weakly supervised machine learning. In histopathology, MIL models have achieved remarkable success in tasks like tumor detection, biomarker prediction, and outcome prognostication. However, MIL explanation methods are still lagging behind, as they are limited to small bag sizes or disregard instance interactions. We revisit MIL through the lens of explainable AI (XAI) and introduce xMIL, a refined framework with more general assumptions. We demonstrate how to obtain improved MIL explanations using layer-wise relevance propagation (LRP) and conduct extensive evaluation experiments on three toy settings and four real-world histopathology datasets. Our approach consistently outperforms previous explanation attempts with particularly improved faithfulness scores on challenging biomarker prediction tasks. Finally, we showcase how xMIL explanations enable pathologists to extract insights from MIL models, representing a significant advance for knowledge discovery and model debugging in digital histopathology. Codes are available at: https://github.com/bifold-pathomics/xMIL.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Multiple instance learning (MIL) [1, 2] is a learning paradigm in which a single label is predicted from a bag of instances. Various MIL methods have been proposed, differing in how they aggregate instances into bag information [3, 4, 5, 6, 7, 8, 9, 10, 11, 12] . MIL has become particularly popular in histopathology, where gigapixel microscopy slides are cut into patches representing small tissue regions. From these patches, MIL models can learn to detect tumor [13] or classify disease subtypes [6] , aiming to support pathologists in their routine diagnostic workflows. They have further demonstrated remarkable success at tasks that even pathologists cannot perform reliably due to a lack of known histopathological patterns associated with the target, e.g., predicting clinically relevant biomarkers [14, 15, 16] or outcomes like survival [17, 18] directly from whole slide images. The MIL model has been trained to predict HPV status. The xMIL-LRP heatmap shows that the model identified evidence in favor of an HPV infection at the tumor border (red area) and evidence against an HPV infection inside the tumor (blue area, lower half of the tissue). The dominant blue region explains why the model mispredicted the slide as HPV-negative. Investigation of the tumor border by a pathologist revealed a higher lymphocyte density, which is one of the known recurrent but not always defining visual features of HPV infection in head and neck tumors. xMIL-LRP allows pathologists to extract fine-grained insights about the model strategy. In contrast, the \"attention\" and \"single\" methods neither explain the negative prediction nor distinguish the relevant areas.", "section": "Introduction", "sec_num": "1"}, {"text": "Explaining which visual features a MIL model uses for its prediction is highly relevant in this context. It allows experts to sanity-check the model strategy [19] , e.g., whether a model focuses on the disease area for making a diagnosis. This is particularly important in histopathology, where models operating in high-stake environments are prone to learning confounding factors like artifacts or staining differences instead of actual signal [20, 21, 22] . On top of that, MIL explanations can enable pathologists to discover novel connections between visual features and prediction targets. For example, the explanations could reveal a previously unknown association of a histopathological pattern with poor survival, leading to the identification of a targetable disease mechanism. Previous works have shown the potential of scientific knowledge discovery from explainable AI (XAI) [22, 23, 24, 25, 26] .", "section": "Introduction", "sec_num": "1"}, {"text": "Most studies have used attention scores as MIL explanations [3, 4, 6, 27, 28, 29] . However, it has been shown that attention heatmaps are limited in faithfully reflecting model predictions [30, 31, 32, 33] . Further MIL explanation methods have been proposed, including perturbation schemes passing modified bags through the model [34] and architectural changes towards fully additive MIL models [33] . Nevertheless, these methods do not account for the complexities inherent to many histopathological prediction tasks, as they are limited to small bag sizes or disregard instance interactions.", "section": "Introduction", "sec_num": "1"}, {"text": "We revisit MIL through the lens of XAI and introduce xMIL, a more general and realistic multiple instance learning framework including requirements for good explanations. We then present xMIL-LRP, an adaptation of layer-wise relevance propagation (LRP) [35, 36] to MIL. xMIL-LRP distinguishes between positive and negative evidence, disentangles instance interactions, and scales to large bag sizes. It applies to various MIL models without requiring architecture modifications, including Attention MIL [3] and TransMIL [4] . We assess the performance of multiple explanation techniques via three toy experiments, which can serve as a novel benchmarking tool for MIL explanations in complex tasks with instance interactions and context-sensitive targets. We further perform faithfulness experiments on four real-world histopathology datasets covering tumor detection, disease subtyping, and biomarker prediction. xMIL-LRP consistently outperforms previous attempts across all tasks and model architectures, with the biggest advantages observed for Transformer-based biomarker prediction.", "section": "Introduction", "sec_num": "1"}, {"text": "Figure 1 showcases the importance of understanding positive and negative evidence for a prediction. Only xMIL-LRP uncovers that the model found evidence for the presence of the biomarker, but stronger evidence against it. This explains why it predicted the biomarker to be absent and enables pathologists to extract insights about the visual features that support or reject the presence of the biomarker according to the model. The example illustrates the strength of our approach, suggesting that xMIL-LRP represents a significant advance for model debugging and knowledge discovery in histopathology.", "section": "Introduction", "sec_num": "1"}, {"text": "The paper is structured as follows: In Section 2, we review MIL assumptions, models, and explanation methods related to this work. In Section 3, we introduce xMIL as a general form of MIL, and xMIL-LRP as a solution for it. In Section 4, we experimentally show the improved explanation quality of our approach. We demonstrate how to extract insights from example heatmaps in Section 5. Our contributions are summarized as follows:", "section": "Introduction", "sec_num": "1"}, {"text": "• Methodical: Despite attempts to apply XAI to MIL models in histopathology (e.g. [6, 27, 28, 29, 33, 34, 37, 38, 39, 40] ), there exists no formalism guiding the interpretation of the heatmaps and defining their desired properties. xMIL is a novel framework addressing this gap. Within xMIL, heatmaps estimate the instances' impact on the bag label, which makes their interpretation straightforward and insightful. • Empirical: Our extensive empirical evaluation of XAI methods for MIL on synthetic and realworld histopathology datasets is the first of its kind. It reveals that the widely used MIL explanation methods regularly yield misleading results. In contrast, xMIL-LRP sets a new state-of-the-art for explainability in AttnMIL and TransMIL models in histopathology. • Insight generation: Previous studies [33, 34] conducted qualitative assessments of heatmaps on easy-to-learn datasets like CAMELYON or TCGA NSCLC. The insights gained in these settings are limited to model debugging, i.e., \"Does the model focus on the disease area?\" To our knowledge, we are the first to present a method generating heatmaps that enable pathologists to extract fine-grained insights about the model in a difficult biomarker prediction task.", "section": "Introduction", "sec_num": "1"}, {"text": "MIL formulations. In MIL, a sample is represented by a bag of instances X = {x 1 , • • • , x K } with a bag label y, where x k ∈ R D is the k-th instance. The number of instances per bag K may vary across samples. In its standard formulation [1, 2, 3] , the instances of a bag exhibit neither dependency nor ordering among each other. It is further assumed that binary instance labels y k ∈ {0, 1} exist but are not necessarily known. The binary bag label is 1 if and only if at least one instance label is 1, i.e., y = max k {y k }. Various extensions have been proposed [41, 42] , each making different assumptions about the relationships between instances and bag labels.", "section": "Multiple instance learning (MIL)", "sec_num": "2.1"}, {"text": "MIL models. MIL architectures typically consist of three components as illustrated in Figure 2 : a backbone extracting instance representations, an aggregation function fusing the instance representations into a bag representation, and a prediction head inferring the final bag prediction. As recent foundation models for histopathology have become powerful feature extractors suitable for a wide range of tasks [29, 43, 44, 45, 46] , the weights of the backbone are often frozen, allowing for a more efficient training. For aggregation, earlier works used parameter-free mean or max pooling approaches [47, 48, 49] . Recently, attention mechanisms could improve performance, flexibly extracting relevant instance-level information using non-linear weighting [3, 6, 50] and self-attention [4, 51] . Attention MIL (AttnMIL) [3] computes a weighted average of the instances' feature vectors via a single attention head. TransMIL [4] uses a custom two-layer Transformer architecture, viewing instance representations as tokens. The bag representation is extracted from the class token at the final layer. TransMIL allows for computing arbitrary pairwise interactions between all instances relevant to the prediction task. While various extensions of AttnMIL and TransMIL have been proposed (e.g., [5, 6, 7, 8, 9, 52, 53, 10, 11, 12] ), these two methods are arguably prototypical and among the most commonly used in the digital histopathology community.", "section": "Multiple instance learning (MIL)", "sec_num": "2.1"}, {"text": "From the few studies investigating MIL interpretability, most of them use attention heatmaps [3, 4, 6, 27, 28, 29] . Moreover, basic gradient-and propagation-based methods have been explored for specific architectures and applications [54, 55] . <PERSON><PERSON><PERSON> et al. [55] applied LRP to generate pixel-level attributions for single-cell images in a blood cancer diagnosis task, but did not consider its potential for instance-level explanations. Perturbation-based methods, building on model-agnostic approaches like SHAP [56] , perturb bag instances and compute importance scores from the resulting change in the model prediction; <PERSON> et al. [34] proposed passing bags of single instances through the model (\"single\"), dropping single instances from bags (\"one-removed\"), and sampling coalitions of instances to be removed (\"MILLI\"). <PERSON><PERSON><PERSON> et al. [33] introduced \"additive MIL\", providing directly interpretable instance scores while constraining the model's ability to capture instance interactions.", "section": "MIL explanation methods.", "sec_num": null}, {"text": "Histopathological datasets and prediction tasks are diverse and come with various inherent challenges. We highlight the following three features.", "section": "Limitations of MIL in histopathology", "sec_num": "2.2"}, {"text": "• Instance ambiguity. Instances are small high-resolution patches from large images. Their individual information content may be limited, as they can be subject to noise or only be interpretable as part of a larger structure. For example, it is not always possible to distinguish a benign high-grade adenoma from a malignant adenocarcinoma on a patch level due to their similar morphology. • Positive, negative, and class-wise evidence. A single bag may contain evidence for multiple classes that a MIL model needs to weigh for correct decision-making. In survival prediction, for example, a strong immune response may support longer survival, while an aggressive tumor pattern speaks for shorter survival. • Instance interactions. In many prediction tasks, it may be necessary to consider interactions between instances. A gene mutation may generate morphological alterations in the tumor area, the tumor microenvironment, and the healthy tissue, all of which may need to be considered together to reliably predict the biomarker.", "section": "Limitations of MIL in histopathology", "sec_num": "2.2"}, {"text": "Existing MIL formulations make explicit assumptions about the relationship between instances and bag labels [42] , limiting their ability to capture the full complexity of a histopathological prediction task. The standard MIL formulation, in particular, does not consider any of the aforementioned aspects, rendering it an unsuitable framework for most histopathological settings.", "section": "Limitations of MIL in histopathology", "sec_num": "2.2"}, {"text": "Similarly, previous MIL explanation methods suffer from various shortcomings that limit their applicability in real-world histopathology datasets. The direct interpretability of attention scores is insufficient to faithfully reflect the model predictions [30, 31, 32] . Moreover, they cannot distinguish between positive, negative, or class-wise evidence [33] . Purely gradient-based explanations may suffer from shattered gradients, resulting in unreliable explanations [57] . Perturbation-based approaches come with high computational complexity. While the linear \"single\" and \"one removed\" methods require K forward passes per bag, MILLI scales quadratically with the number of instances [34] .", "section": "Limitations of MIL in histopathology", "sec_num": "2.2"}, {"text": "In histopathology, where bags typically contain more than 1,000 and frequently more than 10,000 instances, quadratic runtime is practically infeasible. Additive MIL and linear perturbation-based methods do not consider higher-order instance interactions. In prediction tasks depending on interactions, linear perturbation-based explanations may fail to provide faithful explanations, while additive models may not achieve competitive performances.", "section": "Limitations of MIL in histopathology", "sec_num": "2.2"}, {"text": "Notation. We denote vectors with boldface lowercase letters (e.g., x), scalars with lowercase letters (e.g., x), and sets with uppercase letters (e.g., X).", "section": "Methods", "sec_num": "3"}, {"text": "We address the limitations discussed in Section 2.2 and introduce a more general formulation of MIL: explainable multiple instance learning (xMIL). At its core, we propose moving away from the notion of instance labels towards context-aware evidence scores, which better reflect the intricacies of histopathology while laying the foundation for developing and evaluating MIL explanation methods. Definition 3.1 (Explainable multiple instance learning). Let X = {x 1 , . . . , x K } be a bag of instances with a bag label y ∈ R.", "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "(1) There exists an aggregation function A that maps the bag to its label, i.e., A(X) = y. We make no assumptions about the relationship among the instances or between the instances and the label y.", "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "(2) There exists an evidence function E assigning an evidence score E(X, y, x k ) = ϵ k ∈ R to any instance x k in the bag, quantifying the impact the instance has on the bag label y. The aim of xMIL is to estimate (i) the aggregation function A and (ii) the evidence function E. Definition 3.2 (Properties of the evidence function). Let x k , x k ′ be instances from a bag X. We assume that E has the following properties.", "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "(1) Context sensitivity. The evidence score ϵ k of instance x k may depend on other instances from X.", "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "(2) Positive and negative evidence. If ϵ k > 0, the instance x k has a positive impact on the bag label y. If ϵ k < 0, then x k has a negative impact on y. If ϵ k = 0, then x k is irrelevant to y.", "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "(3) Ordering. If ϵ k > ϵ k ′ ≥ 0, then instance x k has a higher positive impact on y than x k ′ . If 0 ≥ ϵ k ′ > ϵ k , then instance x k has a higher negative impact on y than x k ′ . Similar to our definition, previous works described context sensitivity and accounting for positive and negative evidence as desirable properties of MIL explanation methods [33, 34] . However, xMIL integrates these principles directly into the formalization of the MIL problem.", "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "In contrast to previous MIL formulations, xMIL addresses the potential complexities within histopathological prediction tasks by refraining from posing strict assumptions on A. Via the evidence function E, we suggest that instances may vary in their ability to support or refute a class and that their influence may depend on the context within the bag. In practice, the evidence function is often unknown, as the notion of an \"impact\" on the bag label is hard to quantify. For the standard MIL setting, however, the binary instance labels fulfill the criteria of the evidence function. Therefore, xMIL is a more general and realistic formulation of multiple instance learning for histopathology.", "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "We can learn the aggregation function A via training a MIL model. To gain deeper insights into the prediction task by estimating the evidence function E, we design an explanation method for the learned aggregation function with characteristics suitable to the properties of the evidence function.", "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "We introduce xMIL-LRP as an efficient solution to xMIL, bringing layer-wise relevance propagation (LRP) to MIL. LRP is a well-established XAI method [35, 58] with a large body of literature supporting its performance in explaining various types of architectures in different tasks [32, 36, 59, 60, 61, 62] . Starting from the prediction score of a selected class, the LRP attribution of neuron i in layer l receives incoming messages from neurons j from subsequent layer l + 1, resulting in relevance scores r", "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "(l) i = j qij i ′ q i ′ j • r (l+1) j", "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": ", with q ij being the contribution of neuron i of layer l to relevance r (l+1) j1 . A variety of so-called \"propagation rules\" have been proposed [36] to specify the contribution q ij in specific model layers. For the attention mechanism, as a core component of many MIL architectures, we employ the AH-rule introduced by <PERSON> et al. [32] . In a general attention mechanism, let z k = [z kd ] d be the embedding vector of the k-th token and p kj the attention score between tokens k and j. The output vector of the attention module is y j = k p kj z k . The AH-rule of LRP treats attention scores as a constant weighting matrix during the backpropagation pass of LRP. If R(y jd ) is the relevance of the d-th dimension of y j = [y jd ] d , the AH-rule computes the relevance of the d-th feature of z k as:", "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "EQUATION", "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "This formulation can be directly applied to AttnMIL, and also adapted to a QKV attention block in a transformer, where z k is the embedding associated with the value representation.", "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "We illustrate the effect of this rule in AttnMIL in Figure 2-B . The relevance flow separates the instances weighted by the attention mechanism into positive, negative, and neutral instances, resulting in more descriptive heatmaps that better show the relevant tissue regions compared to attention scores.", "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "We further implement the LRP-ϵ rule for linear layers followed by ReLU activation function [58] , as well as the LN-rule to address the break of conservation in layer norm [32] , with details presented in Appendix A.2.", "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "At the instance-level, xMIL-LRP assigns each instance", "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "x k = [x kd ] d ∈ R D a relevance vector r k = [r kd ] d with r kd = R(x kd ) = r (0)", "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "kd being the relevance score of the d-th feature of x k . We define the instance-wise relevance score as an estimate for the evidence score of the instance as εk = d r kd .", "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "The properties of xMIL-LRP are particularly suitable for estimating the evidence function:", "section": "Properties of xMIL-LRP and other explanation methods", "sec_num": "3.3"}, {"text": "Context sensitivity: xMIL-LRP disentangles instance interactions and contextual information as it jointly considers the relevance flow across the whole bag. LRP and Gradient × Input (G×I) are rooted in a deep Taylor decomposition of the model prediction [63] and consequently capture dependencies between features by tracing relevance flow through the components of the MIL model. While attention is context-aware, it is limited to considering dependencies of features at a specific layer. The \"single\" method is unaware of context. \"One-removed\" and additive MIL can only capture the impact of individual instances on the prediction.", "section": "Properties of xMIL-LRP and other explanation methods", "sec_num": "3.3"}, {"text": "Positive and negative evidence: xMIL-LRP relevance scores are real-valued and can identify whether an instance supports or refutes the model prediction. Features irrelevant to the prediction will receive an explanation score close to zero. Therefore, the range of explanation scores matches the range of the assumed evidence function. The same holds for additive MIL, MILLI, and \"one-removed\". Attention and \"single\" do not distinguish between positive and negative evidence.", "section": "Properties of xMIL-LRP and other explanation methods", "sec_num": "3.3"}, {"text": "Conservation: Following the conservation principle of LRP, xMIL-LRP provides an instance-wise decomposition of the model output, i.e., k εk = k,d r kd = y. This instance-level conservation also holds for additive MIL, but not for the other discussed methods. The local conservation principle of LRP [36] further allows us to analyze attribution scores at the instance feature vector level without requiring propagation through the foundation model-the instance-wise attribution scores are the same at any layer of the model.", "section": "Properties of xMIL-LRP and other explanation methods", "sec_num": "3.3"}, {"text": "Baseline methods. We compared several explanation methods to our xMIL-LRP (see Appendix A.1 for details). For AttnMIL and TransMIL, we selected Gradient × Input (G×I) [64, 65] and Integrated gradients (IG) [66] as gradient-based baselines. We further included the \"single\" perturbation method (single) [34] , which involves using predictions for individual instances as explanation scores. Single is the only computationally feasible perturbation-based approach for the bag sizes considered here (up to 24,000). We evaluated raw attention scores for AttnMIL and attention rollout [67] for TransMIL (attn). In the random baseline (rand), instance scores were randomly sampled from a standard normal distribution. For additive attention MIL (AddMIL) [33] , we assessed raw attention scores (attn) and the model-intrinsic instance-wise predictions (logits).", "section": "Experiments and results", "sec_num": "4"}, {"text": "We designed novel toy experiments to assess and compare the characteristics of xMIL-LRP and the baseline methods for AttnMIL, TransMIL, and AddMIL in controlled settings. We focused on evaluating to what extent the explanations account for context sensitivity and positive and negative evidence, i.e., the first two characteristics of the evidence function according to Definition 3.2, which we consider crucial aspects for explaining real-world histopathology prediction tasks.", "section": "Toy experiments", "sec_num": "4.1"}, {"text": "Inspired by previous works [3, 34] , we sampled bags of MNIST images [68] , with each instance representing a number between 0 and 9. We defined three MIL tasks for these bags:", "section": "Toy experiments", "sec_num": "4.1"}, {"text": "• 4-Bags: The bag label is class 1 if 8 is in the bag, class 2 if 9 is in the bag, class 3 if 8 and 9 are in the bag, and class 0 otherwise. The dataset was proposed by <PERSON> et al. [34] . In this setting, the model needs to learn basic instance interactions. • Pos-Neg: We define 4, 6, 8 as positive and 5, 7, 9 as negative numbers. The bag label is class 1 if the amount of unique positive numbers is strictly greater than that of unique negative numbers, and class 0 otherwise. The model needs to adequately weigh positive and negative evidence to make correct predictions. • Adjacent <PERSON>irs: The bag label is class 1 if it contains any pair of consecutive numbers between 0 and 4, i.e., (0,1), (1,2), (2,3) or (3, 4) , and class 0 otherwise. In this case, the impact of an instance is contextual, as it depends on the presence or absence of adjacent numbers.", "section": "Toy experiments", "sec_num": "4.1"}, {"text": "To assess the explanation quality, we first defined valid evidence scores as ground truths according to Definition 3.2. For each dataset, we require one evidence function per predicted class c, denoted by", "section": "Toy experiments", "sec_num": "4.1"}, {"text": "EQUATION", "section": "Toy experiments", "sec_num": "4.1"}, {"text": "k = -1 if the instance refutes class c, and ϵ (c) k = 0 if it is irrelevant. We aimed to measure whether an explanation method correctly distinguishes instances with positive, neutral, and negative evidence scores. Therefore, we computed a two-class averaged area under the precision-recall curve (AUPRC-2), measuring if the positive instances received the highest and the negative instances the lowest explanation scores. We assessed AttnMIL and TransMIL models and repeated each experiment 30 times. The details of the ground truth, the evaluation metric, and the experimental setup are provided in Appendix A.3.", "section": "Toy experiments", "sec_num": "4.1"}, {"text": "Table 1 displays the test AUROC scores of the three models across datasets, demonstrating that the models solve the tasks to varying degrees, alongside the performances of the explanation methods. We find that xMIL-LRP outperformed the other explanation approaches across MIL models and datasets in all but one setting. It reached particularly high AUPRC-2 scores in the 4-Bags and Pos-Neg datasets while being most robust in the more difficult Adjacent Pairs setting. Attention severely suffered from the presence of positive and negative evidence, which it cannot distinguish by design. While IG performed comparably to xMIL-LRP for AttnMIL models, it was inferior for TransMIL. Notably, the test AUROC of AddMIL was worse in all settings, resulting in explanations that are not competitive with the post-hoc explanation methods on AttnMIL and TransMIL. This supports our point that AddMIL may not perform competitively in difficult prediction tasks. The single perturbation method provided good explanations in the Pos-Neg setting, where numbers have a fixed evidence score irrespective of the other instances in the bag. However, in 4-Bags and Adjacent Pairs, the method's performance decreased, as it always assigns the same score to the same instance regardless of the bag context. In contrast, xMIL-LRP is both context-sensitive and identifies positive and negative instances. Since we expect that these aspects are common features of many Table 1 : Results of the toy experiments. We report AUPRC-2 scores of MIL explanation methods on three toy datasets measuring how well a method identified instances with positive and negative evidence scores (mean ± std. over 30 repetitions). The highest mean scores are bold and the second highest are underlined. We also display the model performances (\"Test AUROC\", mean ± std.). real-world histopathological datasets, we conclude that our method is the only suitable approach for such complex settings.", "section": "Toy experiments", "sec_num": "4.1"}, {"text": "Datasets and model training. To evaluate the performance of explanations on real-world histopathology prediction tasks, we considered four diverse datasets of increasing task difficulty covering tumor detection, disease subtyping, and biomarker prediction. These datasets had previously been used for benchmarking in multiple studies [12, 33, 46, 69] .", "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "• CAMELYON16 [70] consists of 400 sentinel lymph node slides, of which 160 carry to-berecognized metastatic lesions of different sizes. It is a well-established tumor detection dataset. • The TCGA NSCLC dataset (abbreviated as NSCLC) contains 529 slides with lung adenocarcinoma (LUAD) and 512 with lung squamous cell carcinoma (LUSC). The prediction task is to distinguish these two non-small cell lung cancer (NSCLC) subtypes. • The TCGA HNSC HPV dataset [12] (abbreviated as HNSC HPV) has 433 slides of head and neck squamous cell carcinoma (HNSC). 43 of them were affected by a human papillomavirus (HPV) infection diagnosed via additional testing [71] . HPV infection is an essential biomarker guiding prognosis and treatment [12] . The task is to identify the HPV status directly from the slides. Label imbalances and the complexity of the predictive signature are key challenges in this task. • The TCGA LUAD TP53 (abbreviated as LUAD TP53) dataset contains 529 lung adenocarcinoma (LUAD) slides, 263 of which exhibit a mutation of the TP53 gene, which is one of the most common mutations across cancers. In lung cancer, it is associated with poorer prognosis and resistance to chemotherapy and radiation [72] . Previous works showed that TP53 mutation can be predicted from LUAD slides [69, 73] .", "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "We generated patches at 20x magnification and obtained 10,454 ± 6,236 patches per slide across all datasets (mean ± std.). Features were extracted using the pre-trained CTransPath [43] foundation model and aggregated using AttnMIL or TransMIL. 2 Additional details regarding the datasets and training procedure are described in Appendix A.4.", "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "We report the mean and standard deviation of the test set AUROC over 5 repetitions in Table 2 . In all but one case, TransMIL outperformed AttnMIL, with the largest margin observed in the difficult TP53 dataset. Our results generally align with performances reported in previous works [12, 46, 69] .", "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "Faithfulness evaluation. As the evidence functions E of our histopathology datasets are unknown, we resorted to assessing faithfulness, i.e., how accurately explanation scores reflect the model prediction [74, 75] . The primary goal of the faithfulness experiments is to evaluate the ordering of relevance scores (Property 3 of the evidence function in Definition 3.2). Faithfulness can be quantified by progressively excluding instances from the most relevant first (MORF) to the least relevant last and measuring the change in prediction score. The area under the resulting perturbation curve (AUPC) indicates how faithfully the identified ordering of the instances affects the model prediction. The lower the AUPC score, the more faithful the method. We calculated AUPC for correctly classified slides. Further methodological details are provided in Appendix A.5.", "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "In Figure 3 , we show the perturbation curves and AUPC boxplots for the patch-dropping experiment for TransMIL in our four datasets (Figure 4 shows the results for AttnMIL). Additionally, we summarize our results in Table 2 . To test the difference in the AUPC values among the baseline explanation methods, we performed paired t-tests between the random baseline vs. all methods and xMIL-LRP vs. all other baselines. The p-values were corrected using the Bonferroni method for multiple comparison correction. All tests resulted in significant differences except for random baseline vs. G×I for CAMELYON16 and attention for HNSC HPV.", "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "xMIL-LRP significantly achieved the lowest average AUPC compared to the baselines, providing the most faithful explanations across all tasks and model architectures. Especially evident with the TransMIL model, xMIL-LRP accurately decomposed the mixing of patch information via selfattention. Notably, the largest margin of xMIL-LRP to other methods could be observed in the more challenging biomarker prediction tasks of the HNSC HPV and LUAD TP53 datasets.", "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "The results also reflect whether the explanation scores contain meaningful positive/negative evidence for the target class (Property 2 of the evidence function in Definition 3.2): if so, we expect the model's prediction to flip when all patches supporting the target class are excluded. In Figure 3 , the model decision always flips when patches are excluded based on xMIL-LRP scores, whereas other methods show inconsistent results.", "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "Attention scores, as the most widely used explanation approach for MIL in histopathology, did not provide faithful explanations outside the simple tumor detection setting in the CAMELYON16 dataset. This remarkably highlights their limited usefulness as model explanations and confirms previously reported results in other domains [30, 31, 32] . Passing single instances through the model (\"single\") achieved good faithfulness scores for simpler tasks and AttnMIL, but performed worse for Transformer-based biomarker prediction.", "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "The identification of predictive features for HPV infection in head and neck carcinoma from histopathological slides is a challenging task for pathologists. In this task, there are partially known morphological patterns associated with the class label. We provide a brief overview of the known histological features differentiating HPV-negative and HPV-positive HNSC in Appendix A.7 and Figure 5 . In the following, we demonstrate how faithful xMIL-LRP explanations can support pathologists in gaining insights about the model strategy and inferring task-relevant features.", "section": "Extracting insights from xMIL-LRP heatmaps", "sec_num": "5"}, {"text": "We extracted explanation scores for the best-performing TransMIL models. To increase the readability of resulting heatmaps, we clipped the scores per slide at the whiskers of their boxplots, which extended 1.5 times the interquartile range from the first and third quartiles. We then translated them into a zero-centered red-blue color map, with red indicating positive and blue negative scores. Notice that the explanation methods operate on different scales. For xMIL-LRP, a positive relevance score indicates support for the explained label, while a negative score contradicts it. The first row depicts the perturbation curves, where the solid lines are the average perturbation curve and the shaded area is the standard error of the mean at each perturbation step. Each boxplot on the second row shows the distribution of AUPC values for all test set slides per explanation methods. In each boxplot, the red line marks the median and the red dot marks the mean. Lower perturbation curves and AUPCs represent higher faithfulness.", "section": "Extracting insights from xMIL-LRP heatmaps", "sec_num": "5"}, {"text": "We revisit the example of the HNSC tumor with a false-positive prediction of an HPV infection in Figure 1 . As previously noted, only xMIL-LRP indicates that the model recognizes evidence of HPV infection in the tumor border, but not the remaining tumor. Despite a prediction score close to 0, all relevance scores from the single method were between 0.95-0.97, suggesting that context-free single-instance bags may not be informative in this task. We observed this phenomenon across various slides.", "section": "Extracting insights from xMIL-LRP heatmaps", "sec_num": "5"}, {"text": "Heatmaps of additional examples are provided in Appendix A.7. In Figure 6 , xMIL-LRP accurately delineates and distinguishes HPV-positive tumor islands from the surrounding stroma. In this simple case, attention also provides a reasonable explanation. Figure 7 presents another correctly classified HPV-positive sample. Here, xMIL-LRP outlines spatially consistent slide regions with clear positive evidence, distinct from regions of negative or mixed evidence (top row). Most notably, the subepithelial mucous glands (bottom row), which are not associated with HPV, are correctly highlighted in blue, unlike in the attention map. In Figure 8 , we display a false positive slide. In this case, xMIL-LRP allowed us to identify that the evidence of HPV-positivity can be attributed to an unusual morphology of an HPV-negative tumor that shares some morphological features usually associated with HPV infection (e.g., smaller tumor cells with hyperchromatic nuclei, dense lymphocyte infiltrates).", "section": "Extracting insights from xMIL-LRP heatmaps", "sec_num": "5"}, {"text": "We introduced xMIL, a more general and realistic MIL framework for histopathology, formalizing requirements for MIL explanations via the evidence function. We adapted LRP to MIL as xMIL-LRP, experimentally demonstrated its advantages over previous explanation approaches, and showed how access to faithful explanations can enable pathologists to extract insights from a biomarker prediction model. Thus, xMIL is a step toward increasing the reliability of clinical ML systems and driving medical knowledge discovery, particularly in histopathology. Despite being motivated by the challenges in histopathology, our approach presented here can be directly transferred to other problem settings that require explaining complex MIL models, e.g., in video, audio, or text domains. Furthermore, a detailed analysis of potentially complex dependencies between instances, especially in the context of multi-modal inputs, represents a promising direction for future research.", "section": "Conclusion", "sec_num": "6"}, {"text": "A.1 Baseline MIL explanation methods Attention maps Attention scores have commonly been used as an explanation of the model by considering attention heatmaps, assuming that they reflect the importance of input features [76] .", "section": "A Appendix", "sec_num": null}, {"text": "In AttnMIL, a bag representation is computed as an attention-weighted average of instance-level representations, i.e.,", "section": "A Appendix", "sec_num": null}, {"text": "EQUATION", "section": "A Appendix", "sec_num": null}, {"text": "where f (x k ) is an instance and g(X) the bag representation. The attention scores a k assign to each patch an attribution with 0 ≤ a k ≤ 1, and have been used as instance-wise explanation scores [3] .", "section": "A Appendix", "sec_num": null}, {"text": "In TransMIL, the attention heads deliver self-attention vectors A l h ∈ R (K+1)×(K+1) for each head h and Transformer layer l, recalling that the first token is the class token. Mean pooling is often used for fusing the self-attention matrices of different heads, i.e., A l = A l h h . The attention scores from the class token to the instance tokens can be used as attribution scores, i.e., A l", "section": "A Appendix", "sec_num": null}, {"text": "(1,2:) . Alternatively, attention rollout has been proposed to summarize the self-attention matrices over layers [67] . For a model with L Transformer layers, attention rollout combines {A l } L l=1 as Ã =", "section": "A Appendix", "sec_num": null}, {"text": "Ǎl where Ǎl = 0.5A l + 0.5I, with I being the identity matrix. Then, similar to the layer-wise attention scores, the heatmap is defined as the attention rollout of the class token to the instances, i.e., Ã(1,2:) .", "section": "L l=1", "sec_num": null}, {"text": "Gradient-based methods Gradient-based methods utilize gradient information to derive feature relevance scores. <PERSON><PERSON><PERSON> et el. [54] combined raw gradients to identify the most relevant features and derive tiles that activate these features the most.", "section": "L l=1", "sec_num": null}, {"text": "Various other gradient-based methods have been proposed in the XAI literature, including saliency maps and Gradient × Input (G×I) [64, 65] and Integrated Gradients (IG) [66] . These methods can easily be adapted to compute explanations in MIL. We obtain the gradient of a MIL model prediction ŷ with respect to a patch ∇ŷ(x k ).", "section": "L l=1", "sec_num": null}, {"text": "For G×I, we can then define the relevance score of the k-th instance as d [∇ŷ(x k )] d x kd , with x kd being the d-th feature of x k .", "section": "L l=1", "sec_num": null}, {"text": "Integrated gradients (IG) [66] computes the gradients of the model's output with respect to the input, integrated over a path from a baseline to the actual input. The baseline is typically set to zero, and so we do. The explanation score of the k-th instance is computed as d IG(x kd ), where the relevance score of the d-th feature of the k-th instance IG(x kd ) is computed as", "section": "L l=1", "sec_num": null}, {"text": "EQUATION", "section": "L l=1", "sec_num": null}, {"text": "where f is the model and X is the K × D feature matrix of the bag (with K being the number of instances and D being the number of features for each instance). We used the implementation of IG available in Captum [77] with the internal batch size set to the number of instances in a bag.", "section": "L l=1", "sec_num": null}, {"text": "The idea of perturbation-based explanation methods is to perturb selected instances of a bag and derive importance scores from the resulting change in the model prediction. It builds on model-agnostic post-hoc local interpretability methods like LIME [78] and SHAP [56] .", "section": "Perturbation-based methods", "sec_num": null}, {"text": "<PERSON> et al. [34] proposed and evaluated multiple perturbation-based methods of different complexity.", "section": "Perturbation-based methods", "sec_num": null}, {"text": "The \"single\" method passes bags of single patches X k = {x k } for k = 1, . . . , K through the model and uses the outcome f (X k ) as explanation score. \"One removed\" drops single patches, i.e. constructs bags Xk = X\\X k for k = 1, . . . , K and defines the difference to the original prediction score f (X) -f ( Xk ) as explanation. The \"combined\" approach takes the mean of these two scores. As these methods cannot account for patch interactions, <PERSON> et al. also propose an algorithm to sample coalitions of patches to be perturbed, called MILLI. They show that MILLI outperforms the baselines on toy datasets when instance interactions need to be considered. The complexity of MILLI is O(nK 2 ), where n is the number of coalitions and K is the bag size.", "section": "Perturbation-based methods", "sec_num": null}, {"text": "Additive MIL The idea of additive MIL [33] is to make the MIL model inherently interpretable by designing the bag-level prediction to be a sum of individual instance predictions. Let function f be a feature extractor and ψ m , ψ p MLPs. In many cases, particularly for Attention MIL [3] , a MIL model p can be written as", "section": "Perturbation-based methods", "sec_num": null}, {"text": "EQUATION", "section": "Perturbation-based methods", "sec_num": null}, {"text": "where a k is the attention score of instance k. For Attention MIL, ψ m is defined as the inner part of the softmax function of Equation 2, and ψ p as prediction head outputting class logits. To obtain an additive model, the authors suggest to instead compute", "section": "Perturbation-based methods", "sec_num": null}, {"text": "EQUATION", "section": "Perturbation-based methods", "sec_num": null}, {"text": ")", "section": "Perturbation-based methods", "sec_num": null}, {"text": "This way, the bag prediction becomes the sum of the individual instance predictions ψ p (a k f (x k )), which can be used as instance explanation scores. These instance logits are proportional to the Shapley values of the instances [33] . In our experiments, we consider the proposed additive variant of Attention MIL (AddMIL).", "section": "Perturbation-based methods", "sec_num": null}, {"text": "LRP is a method for explaining neural network predictions by redistributing the output's relevance back through the network to the input features. The redistribution follows a relevance conservation principle, where the total relevance of each layer is preserved as it propagates backward. If r (l) j denotes the relevance of neuron j in layer l, conservation means that j r", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "(l1) j = i r (l2) i", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "holds for any two layers l 1 and l 2 . As a general principle, LRP posits", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "with q ij being the contribution of neuron i of layer l relevance r (l+1) j", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": ". There are \"propagation rules\" for various layer types [36, 58] that specify q ij for different setups.", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "Feed forward neural network. The following generic rule holds for propagating relevance through linear layers followed by ReLU [36] :", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "where a j is the activation of neuron j in layer l, w ij the weight from neuron i of layer l to neuron j of layer l + 1, ϵ a stabilizing term to prevent numerical instabilities, and ρ(w ij ) a modification of the weights of the linear layer. For example, if ρ(w ij ) = w ij + γmax(w ij , 0), then Equation 7 is called LRP-γ rule. For γ = 0, this equation is called LRP-ϵ rule.", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "LayerNorm. Assume z k is the embedding of the k-th token and y k = LayerNorm(z k ) as:", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "where E{z} and std{z} are the expected values and standard deviation of the tokens.", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "For propagating relevance through LayerNorm, <PERSON> et al. [32] suggested the LN-rule as the following:", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "EQUATION", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "where δ kj = 1, if k = j 0, otherwise and z kd is the d-th dimension of z k and R(z kd ) is the relevance assigned to it. In practice, LN-rule is implemented by detaching std{z} and handling it as a constant.", "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "Evidence functions. We define the evidence functions for the three datasets as follows. We write x k ∼ n to indicate that instance k represents MNIST number n.", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "• In the 4-Bags dataset, 8 supports classes 1 and 3 but refutes classes 0 and 2, while 9 supports classes 2 and 3 but refutes classes 0 and 1. Hence, for x k ∼ 8, we define ϵ", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "(c) k = 1 for c ∈ {1, 3} and ϵ (c) k = -1 for c ∈ {0, 2}. For x k ∼ 9, we set ϵ (c) k = 1 for c ∈ {2, 3} and ϵ (c) k = -1 for c ∈ {0, 1}. In all other cases, ϵ (c) k = 0.", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "• In Pos-Neg, 4, 6, and 8 instances support class 1 and refute class 0, and vice versa for 5, 7, 9.", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "Hence, we set ϵ", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "k = 1 and ϵ", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "k = -1 and ϵ ", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "(0) k = 1 if x k ∼ {5, 7,", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "k = 0 otherwise. The evidence scores for the other numbers are defined accordingly.", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "Evaluation metric. We aim to measure whether an explanation method correctly distinguishes instances with positive, neutral, and negative evidence scores. We separate this into two steps: quantify the separation between positive and non-positive instances, and quantify the separation between negative and non-negative instances. Let e (c) = [ϵ", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "K ] be the evidence scores for some bag X = {x 1 , . . . , x K } and class c, and", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "K ] be the class-wise explanation scores from some explanation method. We define e (c) pos = min(e (c) , 0) and e (c) neg = min(-e (c) , 0) as the binarized positive and negative evidence, and compute", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "EQUATION", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "We utilize the area under the precision-recall curve (AUPRC) to account for potential imbalances.", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "Our AUPRC-2 metric can be interpreted as the one-vs-all AUPRC score for detecting positive and negative instances. It becomes 1 if all instances with positive / negative evidence have been assigned the highest / lowest evidence scores. For each dataset and explanation method, we computed the AUPRC-2 across all classes and test bags and report the average score.", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "Experimental details. Instead of training end-to-end MIL models, we obtained feature vectors with 512 dimensions for each MNIST image via a ResNet18 model pre-trained on Imagenet from the TorchVision library [79] . For each bag, we first sampled a subset of numbers, where each number was selected with a probability of 0.5, and then randomly drew 30 MNIST feature vectors from this subset. We used 2,000 bags for training, 500 for validation, and 1,000 for testing. We trained AttnMIL and TransMIL models with a learning rate of 0.0001 for a maximum of 1000 and 200 epochs for AttnMIL and TransMIL, respectively. We finally selected the model with the lowest validation loss. We repeated each model training 30 times and report means and standard deviations across repetitions. Each experiment with its repetitions was run on single CPUs in less than 24 hours, respectively. We used the same setting for training AddMIL, but with an Adam optimizer as in the original paper [33] .", "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "Dataset details. We downloaded TCGA HNSC, LUAD, and LUSC datasets from TCGA website. The HPV status of HNSC dataset and the TP53 mutations of LUAD dataset were downloaded from cBioPortal [80, 81, 82] . We applied the following splits.", "section": "A.4 Histopathology experiments: Data and training details", "sec_num": null}, {"text": "• CAMELYON16: We used the pre-defined test set of 130 slides, and randomly split the remaining slides into 230 for training and 40 for validation. • NSCLC: As in previous works [4, 33] , we randomly split the slides into 60% training, 15% validation, and 25% test data. • HNSC HPV: Due to the low number of HPV-positive samples, we uniformly split the dataset into three cross-validation folds like in previous work [12] . • LUAD TP53: We randomly split the slides into 60% training, 15% validation, and 25% test data. From TCGA-BA-A6DB", "section": "A.4 Histopathology experiments: Data and training details", "sec_num": null}, {"text": "From TCGA-BA-5559 NeurIPS Paper Checklist Guidelines:", "section": "HPV-negative", "sec_num": null}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "section": "HPV-positive", "sec_num": null}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "section": "HPV-positive", "sec_num": null}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "section": "HPV-positive", "sec_num": null}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Justification: (1) our data are from public datasets that have followed the guidelines of their corresponding ethics committee. ( 2) we declare no known potential harmful consequence of our submitted work regarding the mentioned points in the code of ethics. (3) if applicable, we implemented all the impact mitigation measures mentioned in the code of ethics, such as disclosing essential elements for reproducibility. Guidelines:", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction). 10. Broader Impacts Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed? Answer: [NA] Justification: We anticipate no negative societal impact for our work.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Guidelines:", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)? Answer: [NA] Justification: We are aware of no potential risk for any misuse of our work. Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort. 12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected? Answer: [Yes] Justification: (1) We cited the open datasets properly in Section 4.2. (2) if some parts of other repositories have been used in our codes, we have cited the repository properly. These repositories have MIT license or GPLv3 license for non-commercial academic use. Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "section": "Safeguards", "sec_num": "11."}, {"text": "In some XAI research papers, uppercase letters, e.g., R (l) j , are used for denoting relevance values.", "section": "", "sec_num": null}, {"text": "We did not include AddMIL in the real-world experiments, as it is difficult to compare heatmaps from different models without having a ground truth like in the toy experiments (Section 4.1). Also notice that faithfulness evaluations are not applicable, since AddMIL explanations are faithful by design[33].", "section": "", "sec_num": null}], "back_matter": [{"text": "The results shown here are in whole or part based upon data generated by the TCGA Research Network: https://www.cancer.gov/tcga. This work was in part supported by the German Ministry for Education and Research (BMBF) under Grants 01IS14013A-E, 01GQ1115, 01GQ0850, 01IS18025A, 031L0207D, 01IS18037A, and BIFOLD24B and by the Institute of Information & Communications Technology Planning & Evaluation (IITP) grants funded by the Korea government (MSIT) (No. 2019-0-00079, Artificial Intelligence Graduate School Program, Korea University and No. 2022-0-00984, Development of Artificial Intelligence Technology for Personalized Plug-and-Play Explanation and Verification of Explanation).", "section": "Acknowledgements", "sec_num": null}, {"text": "Preprocessing details. We extracted patches from the slides of 256 × 256 pixels without overlap at 20x magnification (0.5 microns per pixel). We identified and excluded background patches via Otsu's method [83] on slide thumbnails and applied a patch-level minimum standard deviation of 8.Training details. For training, we sampled bags of 2048 patches per slide and passed their feature representations through the MIL model. For validation and testing, we simultaneously exposed all patches of a slide to the model to avoid sampling biases and statistical instabilities. Due to the computational complexity of TransMIL, we excluded slides with more than 24,000 patches (≈ 6% of all slides). We did this for all methods to ensure fair comparisons. AttnMIL models were trained for up to 1,000 epochs with batch size 32, and the TransMIL models for up to 200 epochs with batch size 5. We selected the checkpoint with the highest validation AUC. In the HNSC HPV dataset, we used one fold as validation and test fold and two folds as training folds and repeated this procedure for all possible assignments of folds. We applied a grid search over learning rates and dropout schemes and selected the hyper-parameter settings with the highest mean validation AUCs over 5 repetitions. For AttnMIL, we found that the best configuration was always a learning rate of 0.002 and no dropout. For TransMIL, we ended up with a learning rate of 0.0002 and high dropout (0.2 after the feature extractor, 0.5 after the self-attention blocks and before the final classification layer) for CAMELYON16 and NSCLC, and a learning rate of 0.002 without dropout for HNSC HPV and LUAD TP53. The training was done on an A100 80GB GPU.", "section": "annex", "sec_num": null}, {"text": "Given a slide X = {x k } K k=1 and a heatmaping function H producing the explanation scores of the instances in X, i.e., H(x k ) = εk , we binned the patches of slide X into 100 ordered groups, where E i is the set of all patches whose attribution scores are between the (100 -i)th and (100 -i + 1)-th percentiles of the explanation scores of the instances of X, for example, E 1 is the set of the most relevant 1% patches of X and E 100 is the least relevant 1% of the patches.Patch dropping. Following the region perturbation strategy introduced in [74] , we progressively excluded the most relevant regions from slide X, i.e. in the n-th iteration, the most relevant n% of patches were excluded. Formally, the perturbation procedure can be formulated as the following:where P(X, n) is a perturbation function that excludes the most relevant n% of patches from slide X. Note that at step n = 100 all the patches are excluded and therefore, X (100) morf = ∅, where ∅ is an empty set, for which we pass an array of zeros to the model.Comparing heatmaps. We define the quantity of interest for the comparison of different heatmaps as the area under the perturbation curve (AUPC) as the following:where f is the model's function.That is, the lower AUPC, the more faithful the explanation method.We ran all the AttnMIL experiments on an A100 40GB GPU and the TransMIL experiments on a single CPU.", "section": "A.5 Faithfulness evaluation: Patch flipping", "sec_num": null}, {"text": "We present the patch dropping results for AttnMIL in Figure 4 .", "section": "A.6 Faithfulness evaluation: Additional results", "sec_num": null}, {"text": "Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "section": "Answer: [NA] Justification: [NA]", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: All codes are publicly accessible on GitHub. The experiment pipelines are clearly described in Sections 4.1 and 4.2 of the manuscript. Further description of data processing pipelines is given in the main paper in Section 4.2 and in the supplemental material in Section A. 4 . Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.• The name of the license (e.g., CC-BY 4.0) should be included for each asset.• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators. ", "section": "Experimental Result Reproducibility", "sec_num": "4."}], "ref_entries": {"FIGREF0": {"type_str": "figure", "text": "Conference on Neural Information Processing Systems (NeurIPS 2024).", "num": null, "fig_num": null, "uris": null}, "FIGREF1": {"type_str": "figure", "text": "Figure 1: In digital pathology, heatmaps guide the identification of tissue slide areas most important for a model prediction. The figure displays heatmaps from different MIL explanation methods (columns) for a head and neck tumor slide (top row) with a selected zoomed-in region (bottom row).The MIL model has been trained to predict HPV status. The xMIL-LRP heatmap shows that the model identified evidence in favor of an HPV infection at the tumor border (red area) and evidence against an HPV infection inside the tumor (blue area, lower half of the tissue). The dominant blue region explains why the model mispredicted the slide as HPV-negative. Investigation of the tumor border by a pathologist revealed a higher lymphocyte density, which is one of the known recurrent but not always defining visual features of HPV infection in head and neck tumors. xMIL-LRP allows pathologists to extract fine-grained insights about the model strategy. In contrast, the \"attention\" and \"single\" methods neither explain the negative prediction nor distinguish the relevant areas.", "num": null, "fig_num": "1", "uris": null}, "FIGREF3": {"type_str": "figure", "text": "Figure 2: The two steps of xMIL: estimating the aggregation function (A) and the evidence function (B). Panel A shows a block diagram of a MIL model applied to a histopathology slide. The feature extraction module is typically a combination of a frozen foundation model followed by a shallow MLP. In most of the recent MIL models, the aggregation module uses attention mechanisms for combining the instance feature vectors into a single feature representation per bag. The prediction head is a linear layer or an MLP. Panel B schematically shows xMIL-LRP for explaining AttnMIL. In xMIL-LRP, the model output is backpropagated to the input instances. The colored lines represent the relevance flow. Red and blue colors encode the positive and negative values. The attention module is handled via the AH-rule as described in Section 3.2. As discussed in Section 3.3, the instance explanation scores can be computed at the output of the foundation model or at the input level.", "num": null, "fig_num": "2", "uris": null}, "FIGREF4": {"type_str": "figure", "text": "xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG", "num": null, "fig_num": null, "uris": null}, "FIGREF5": {"type_str": "figure", "text": "Figure3: Patch dropping results for TransMIL. The first row depicts the perturbation curves, where the solid lines are the average perturbation curve and the shaded area is the standard error of the mean at each perturbation step. Each boxplot on the second row shows the distribution of AUPC values for all test set slides per explanation methods. In each boxplot, the red line marks the median and the red dot marks the mean. Lower perturbation curves and AUPCs represent higher faithfulness.", "num": null, "fig_num": "3", "uris": null}, "FIGREF6": {"type_str": "figure", "text": "otherwise.• In Adjacent Pairs, 4 supports class 1 and refutes class 0 if 3 is also present, but is irrelevant otherwise. That is, for x k ∼ 4, we set ϵ if 3 is also in in the bag, and ϵ", "num": null, "fig_num": "1", "uris": null}, "FIGREF7": {"type_str": "figure", "text": "xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG", "num": null, "fig_num": null, "uris": null}, "FIGREF8": {"type_str": "figure", "text": "Figure 4: Patch dropping results for AttnMIL. The first row depicts the perturbation curves, where the solid lines are the average perturbation curve and the shaded area is the standard error of the mean at each perturbation step. Each boxplot on the second row shows the distribution of AUPC values for all test set slides per explanation methods. In each boxplot, the red line marks the median and the red dot marks the mean. Lower perturbation curves and AUPCs represent higher faithfulness.", "num": null, "fig_num": "4", "uris": null}, "FIGREF9": {"type_str": "figure", "text": "Figure 5: Exemplary histological features of HPV-negative and -positive HNSC. We display further exemplary heatmaps of TransMIL model predictions in the HNSC HPV dataset in Figures 6, 7, and 8.", "num": null, "fig_num": "56", "uris": null}, "FIGREF10": {"type_str": "figure", "text": "Figure 7: Heatmaps from different explanation methods for a TransMIL model predicting HPV-status. The model correctly predicted the slide HPV-positive (prediction score: 0.9048). For xMIL-LRP, red indicates evidence for and blue against the HPV-positive class.", "num": null, "fig_num": "7", "uris": null}, "FIGREF11": {"type_str": "figure", "text": "Figure 8: Heatmaps from different explanation methods for a TransMIL model predicting HPV-status. The slide is HPV-negative, but the model predicted HPV-positive (prediction score: 0.9997). For xMIL-LRP, red indicates evidence for and blue against the HPV-negative class.", "num": null, "fig_num": "8", "uris": null}, "TABREF1": {"html": null, "type_str": "table", "text": "Results of the faithfulness experiments. AUPC values per dataset, MIL model, and explanation method (mean ± std. over all slides). Lower scores indicate higher faithfulness. The best performance per setting (significant minimum based on the paired t-tests) is highlighted in bold. We also display the model performances (\"Test AUROC\", mean ± std. over 5 repetitions).", "num": null, "content": "<table><tr><td/><td/><td colspan=\"2\">AttnMIL</td><td/><td/><td colspan=\"2\">TransMIL</td></tr><tr><td/><td>CAMELYON16</td><td>NSCLC</td><td colspan=\"3\">HNSC HPV LUAD TP53 CAMELYON16</td><td>NSCLC</td><td>HNSC HPV LUAD TP53</td></tr><tr><td>Test AUROC</td><td>0.93 ± 0.00</td><td colspan=\"2\">0.95 ± 0.00 0.88 ± 0.06</td><td>0.71 ± 0.01</td><td>0.95 ± 0.01</td><td colspan=\"2\">0.96 ± 0.00 0.88 ± 0.05</td><td>0.75 ± 0.01</td></tr><tr><td>Rand</td><td>0.94 ± 0.13</td><td colspan=\"2\">0.98 ± 0.04 0.97 ± 0.07</td><td>0.84 ± 0.14</td><td>0.95 ± 0.11</td><td colspan=\"2\">0.98 ± 0.08 1.00 ± 0.01</td><td>0.94 ± 0.17</td></tr><tr><td>Attn</td><td>0.65 ± 0.46</td><td colspan=\"2\">0.70 ± 0.27 0.94 ± 0.18</td><td>0.65 ± 0.14</td><td>0.63 ± 0.45</td><td colspan=\"2\">0.91 ± 0.22 0.95 ± 0.15</td><td>0.64 ± 0.38</td></tr><tr><td>Single</td><td>0.61 ± 0.43</td><td colspan=\"2\">0.42 ± 0.26 0.78 ± 0.23</td><td>0.34 ± 0.16</td><td>0.42 ± 0.35</td><td colspan=\"2\">0.53 ± 0.26 0.92 ± 0.13</td><td>0.73 ± 0.33</td></tr><tr><td>G×I</td><td>0.92 ± 0.19</td><td colspan=\"2\">0.81 ± 0.35 0.81 ± 0.25</td><td>0.44 ± 0.23</td><td>0.82 ± 0.36</td><td colspan=\"2\">0.79 ± 0.30 0.87 ± 0.20</td><td>0.66 ± 0.40</td></tr><tr><td>IG</td><td>0.62 ± 0.44</td><td colspan=\"2\">0.75 ± 0.38 0.78 ± 0.25</td><td>0.38 ± 0.20</td><td>0.88 ± 0.23</td><td colspan=\"2\">0.99 ± 0.01 1.00 ± 0.00</td><td>0.99 ± 0.01</td></tr><tr><td>xMIL-LRP</td><td>0.51 ± 0.38</td><td colspan=\"2\">0.25 ± 0.22 0.71 ± 0.24</td><td>0.31 ± 0.16</td><td>0.29 ± 0.30</td><td colspan=\"2\">0.45 ± 0.26 0.75 ± 0.23</td><td>0.24 ± 0.28</td></tr></table>"}, "TABREF2": {"html": null, "type_str": "table", "text": "The answer NA means that the abstract and introduction do not include the claims made in the paper.• The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.2. LimitationsQuestion: Does the paper discuss the limitations of the work performed by the authors? Answer: [Yes] Justification: The limitations of the framework and previous works are extensively discussed in the paper, especially in Section 2.2. Additionally, we list further limitations in the discussions.", "num": null, "content": "<table><tr><td>1. Claims</td></tr><tr><td>Question: Do the main claims made in the abstract and introduction accurately reflect the</td></tr><tr><td>paper's contributions and scope?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: The contributions, claims, and the scope of the paper are made clear in the</td></tr><tr><td>abstract and introduction (cf. Section 1).</td></tr><tr><td>Guidelines:</td></tr><tr><td>•</td></tr></table>"}, "TABREF3": {"html": null, "type_str": "table", "text": "Open access to data and code Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? Answer:[No]   Justification: Our code for reproducing our results and implementation of the used methods is made publicly accessible. Datasets used in this work are public datasets properly described and cited in Sections 4.2 and A.4. Guidelines:• The answer NA means that paper does not include experiments requiring code. • Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable). • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted. The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. 8. Experiments Compute Resources Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments? Answer: [Yes] Justification: The compute resources are mentioned in Sections A.3 and A.4. Guidelines: • The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper). 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines? Answer: [Yes]", "num": null, "content": "<table><tr><td>6. Experimental Setting/Details</td></tr><tr><td>Question: Does the paper specify all the training and test details (e.g., data splits, hyper-</td></tr><tr><td>parameters, how they were chosen, type of optimizer, etc.) necessary to understand the</td></tr><tr><td>results?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: All the details in this regard are provided in Sections 4.2 and A.4.</td></tr><tr><td>Guidelines:</td></tr><tr><td>• The answer NA means that the paper does not include experiments.</td></tr><tr><td>• The experimental setting should be presented in the core of the paper to a level of detail</td></tr><tr><td>that is necessary to appreciate the results and make sense of them.</td></tr><tr><td>• The full details can be provided either with the code, in appendix, or as supplemental</td></tr><tr><td>material.</td></tr><tr><td>7. Experiment Statistical Significance</td></tr><tr><td>Question: Does the paper report error bars suitably and correctly defined or other appropriate</td></tr><tr><td>information about the statistical significance of the experiments?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: In our experimental results in Section 4.2 we used paired t-tests with multiple</td></tr><tr><td>comparison corrections for comparing the methods, as well as the standard error of the mean</td></tr><tr><td>to plot the error bars on the perturbation curves of Figures 3 and 4.</td></tr><tr><td>Guidelines:</td></tr></table>"}}}}