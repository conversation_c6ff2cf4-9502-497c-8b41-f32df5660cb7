{"paper_id": "xMIL", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:20:09.370329Z"}, "title": "xMIL: Insightful Explanations for Multiple Instance Learning in Histopathology", "authors": [{"first": "<PERSON>", "middle": [], "last": "Hense", "suffix": "", "affiliation": {"laboratory": "", "institution": "Berlin Institute for the Foundations of Learning and Data", "location": {"settlement": "Berlin", "country": "Germany"}}, "email": ""}, {"first": "Mina", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Berlin Institute for the Foundations of Learning and Data", "location": {"settlement": "Berlin", "country": "Germany"}}, "email": "<EMAIL>"}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Berlin Institute for the Foundations of Learning and Data", "location": {"settlement": "Berlin", "country": "Germany"}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Berlin Institute for the Foundations of Learning and Data", "location": {"settlement": "Berlin", "country": "Germany"}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Berlin Institute for the Foundations of Learning and Data", "location": {"settlement": "Berlin", "country": "Germany"}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ciernik", "suffix": "", "affiliation": {"laboratory": "", "institution": "Berlin Institute for the Foundations of Learning and Data", "location": {"settlement": "Berlin", "country": "Germany"}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "Buchstab", "suffix": "", "affiliation": {"laboratory": "", "institution": "Ludwig Maximilian University", "location": {"settlement": "Munich", "country": "Germany"}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Ludwig Maximilian University", "location": {"settlement": "Munich", "country": "Germany"}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Berlin Institute for the Foundations of Learning and Data", "location": {"settlement": "Berlin", "country": "Germany"}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Berlin Institute for the Foundations of Learning and Data", "location": {"settlement": "Berlin", "country": "Germany"}}, "email": "<EMAIL>"}], "year": "", "venue": null, "identifiers": {}, "abstract": "Multiple instance learning (MIL) is an effective and widely used approach for weakly supervised machine learning. In histopathology, MIL models have achieved remarkable success in tasks like tumor detection, biomarker prediction, and outcome prognostication. However, MIL explanation methods are still lagging behind, as they are limited to small bag sizes or disregard instance interactions. We revisit MIL through the lens of explainable AI (XAI) and introduce xMIL, a refined framework with more general assumptions. We demonstrate how to obtain improved MIL explanations using layer-wise relevance propagation (LRP) and conduct extensive evaluation experiments on three toy settings and four real-world histopathology datasets. Our approach consistently outperforms previous explanation attempts with particularly improved faithfulness scores on challenging biomarker prediction tasks. Finally, we showcase how xMIL explanations enable pathologists to extract insights from MIL models, representing a significant advance for knowledge discovery and model debugging in digital histopathology. Codes are available at: https://github.com/bifold-pathomics/xMIL.", "pdf_parse": {"paper_id": "xMIL", "_pdf_hash": "", "abstract": [{"text": "Multiple instance learning (MIL) is an effective and widely used approach for weakly supervised machine learning. In histopathology, MIL models have achieved remarkable success in tasks like tumor detection, biomarker prediction, and outcome prognostication. However, MIL explanation methods are still lagging behind, as they are limited to small bag sizes or disregard instance interactions. We revisit MIL through the lens of explainable AI (XAI) and introduce xMIL, a refined framework with more general assumptions. We demonstrate how to obtain improved MIL explanations using layer-wise relevance propagation (LRP) and conduct extensive evaluation experiments on three toy settings and four real-world histopathology datasets. Our approach consistently outperforms previous explanation attempts with particularly improved faithfulness scores on challenging biomarker prediction tasks. Finally, we showcase how xMIL explanations enable pathologists to extract insights from MIL models, representing a significant advance for knowledge discovery and model debugging in digital histopathology. Codes are available at: https://github.com/bifold-pathomics/xMIL.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Multiple instance learning (MIL) [1, 2] is a learning paradigm in which a single label is predicted from a bag of instances. Various MIL methods have been proposed, differing in how they aggregate instances into bag information [3, 4, 5, 6, 7, 8, 9, 10, 11, 12] . MIL has become particularly popular in histopathology, where gigapixel microscopy slides are cut into patches representing small tissue regions. From these patches, MIL models can learn to detect tumor [13] or classify disease subtypes [6] , aiming to support pathologists in their routine diagnostic workflows. They have further demonstrated remarkable success at tasks that even pathologists cannot perform reliably due to a lack of known histopathological patterns associated with the target, e.g., predicting clinically relevant biomarkers [14, 15, 16] or outcomes like survival [17, 18] directly from whole slide images. The MIL model has been trained to predict HPV status. The xMIL-LRP heatmap shows that the model identified evidence in favor of an HPV infection at the tumor border (red area) and evidence against an HPV infection inside the tumor (blue area, lower half of the tissue). The dominant blue region explains why the model mispredicted the slide as HPV-negative. Investigation of the tumor border by a pathologist revealed a higher lymphocyte density, which is one of the known recurrent but not always defining visual features of HPV infection in head and neck tumors. xMIL-LRP allows pathologists to extract fine-grained insights about the model strategy. In contrast, the \"attention\" and \"single\" methods neither explain the negative prediction nor distinguish the relevant areas.", "cite_spans": [{"start": 33, "end": 36, "text": "[1,", "ref_id": "BIBREF0"}, {"start": 37, "end": 39, "text": "2]", "ref_id": "BIBREF1"}, {"start": 228, "end": 231, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 232, "end": 234, "text": "4,", "ref_id": "BIBREF3"}, {"start": 235, "end": 237, "text": "5,", "ref_id": "BIBREF4"}, {"start": 238, "end": 240, "text": "6,", "ref_id": "BIBREF5"}, {"start": 241, "end": 243, "text": "7,", "ref_id": "BIBREF6"}, {"start": 244, "end": 246, "text": "8,", "ref_id": "BIBREF7"}, {"start": 247, "end": 249, "text": "9,", "ref_id": "BIBREF8"}, {"start": 250, "end": 253, "text": "10,", "ref_id": "BIBREF9"}, {"start": 254, "end": 257, "text": "11,", "ref_id": "BIBREF10"}, {"start": 258, "end": 261, "text": "12]", "ref_id": "BIBREF11"}, {"start": 466, "end": 470, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 500, "end": 503, "text": "[6]", "ref_id": "BIBREF5"}, {"start": 808, "end": 812, "text": "[14,", "ref_id": "BIBREF13"}, {"start": 813, "end": 816, "text": "15,", "ref_id": "BIBREF14"}, {"start": 817, "end": 820, "text": "16]", "ref_id": "BIBREF15"}, {"start": 847, "end": 851, "text": "[17,", "ref_id": "BIBREF16"}, {"start": 852, "end": 855, "text": "18]", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Explaining which visual features a MIL model uses for its prediction is highly relevant in this context. It allows experts to sanity-check the model strategy [19] , e.g., whether a model focuses on the disease area for making a diagnosis. This is particularly important in histopathology, where models operating in high-stake environments are prone to learning confounding factors like artifacts or staining differences instead of actual signal [20, 21, 22] . On top of that, MIL explanations can enable pathologists to discover novel connections between visual features and prediction targets. For example, the explanations could reveal a previously unknown association of a histopathological pattern with poor survival, leading to the identification of a targetable disease mechanism. Previous works have shown the potential of scientific knowledge discovery from explainable AI (XAI) [22, 23, 24, 25, 26] .", "cite_spans": [{"start": 158, "end": 162, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 445, "end": 449, "text": "[20,", "ref_id": "BIBREF19"}, {"start": 450, "end": 453, "text": "21,", "ref_id": "BIBREF20"}, {"start": 454, "end": 457, "text": "22]", "ref_id": "BIBREF21"}, {"start": 887, "end": 891, "text": "[22,", "ref_id": "BIBREF21"}, {"start": 892, "end": 895, "text": "23,", "ref_id": "BIBREF22"}, {"start": 896, "end": 899, "text": "24,", "ref_id": "BIBREF23"}, {"start": 900, "end": 903, "text": "25,", "ref_id": "BIBREF24"}, {"start": 904, "end": 907, "text": "26]", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Most studies have used attention scores as MIL explanations [3, 4, 6, 27, 28, 29] . However, it has been shown that attention heatmaps are limited in faithfully reflecting model predictions [30, 31, 32, 33] . Further MIL explanation methods have been proposed, including perturbation schemes passing modified bags through the model [34] and architectural changes towards fully additive MIL models [33] . Nevertheless, these methods do not account for the complexities inherent to many histopathological prediction tasks, as they are limited to small bag sizes or disregard instance interactions.", "cite_spans": [{"start": 60, "end": 63, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 64, "end": 66, "text": "4,", "ref_id": "BIBREF3"}, {"start": 67, "end": 69, "text": "6,", "ref_id": "BIBREF5"}, {"start": 70, "end": 73, "text": "27,", "ref_id": "BIBREF26"}, {"start": 74, "end": 77, "text": "28,", "ref_id": "BIBREF27"}, {"start": 78, "end": 81, "text": "29]", "ref_id": "BIBREF28"}, {"start": 190, "end": 194, "text": "[30,", "ref_id": "BIBREF29"}, {"start": 195, "end": 198, "text": "31,", "ref_id": "BIBREF30"}, {"start": 199, "end": 202, "text": "32,", "ref_id": "BIBREF31"}, {"start": 203, "end": 206, "text": "33]", "ref_id": "BIBREF32"}, {"start": 332, "end": 336, "text": "[34]", "ref_id": "BIBREF33"}, {"start": 397, "end": 401, "text": "[33]", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "We revisit MIL through the lens of XAI and introduce xMIL, a more general and realistic multiple instance learning framework including requirements for good explanations. We then present xMIL-LRP, an adaptation of layer-wise relevance propagation (LRP) [35, 36] to MIL. xMIL-LRP distinguishes between positive and negative evidence, disentangles instance interactions, and scales to large bag sizes. It applies to various MIL models without requiring architecture modifications, including Attention MIL [3] and TransMIL [4] . We assess the performance of multiple explanation techniques via three toy experiments, which can serve as a novel benchmarking tool for MIL explanations in complex tasks with instance interactions and context-sensitive targets. We further perform faithfulness experiments on four real-world histopathology datasets covering tumor detection, disease subtyping, and biomarker prediction. xMIL-LRP consistently outperforms previous attempts across all tasks and model architectures, with the biggest advantages observed for Transformer-based biomarker prediction.", "cite_spans": [{"start": 253, "end": 257, "text": "[35,", "ref_id": "BIBREF34"}, {"start": 258, "end": 261, "text": "36]", "ref_id": "BIBREF35"}, {"start": 503, "end": 506, "text": "[3]", "ref_id": "BIBREF2"}, {"start": 520, "end": 523, "text": "[4]", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Figure 1 showcases the importance of understanding positive and negative evidence for a prediction. Only xMIL-LRP uncovers that the model found evidence for the presence of the biomarker, but stronger evidence against it. This explains why it predicted the biomarker to be absent and enables pathologists to extract insights about the visual features that support or reject the presence of the biomarker according to the model. The example illustrates the strength of our approach, suggesting that xMIL-LRP represents a significant advance for model debugging and knowledge discovery in histopathology.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "1", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "The paper is structured as follows: In Section 2, we review MIL assumptions, models, and explanation methods related to this work. In Section 3, we introduce xMIL as a general form of MIL, and xMIL-LRP as a solution for it. In Section 4, we experimentally show the improved explanation quality of our approach. We demonstrate how to extract insights from example heatmaps in Section 5. Our contributions are summarized as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• Methodical: Despite attempts to apply XAI to MIL models in histopathology (e.g. [6, 27, 28, 29, 33, 34, 37, 38, 39, 40] ), there exists no formalism guiding the interpretation of the heatmaps and defining their desired properties. xMIL is a novel framework addressing this gap. Within xMIL, heatmaps estimate the instances' impact on the bag label, which makes their interpretation straightforward and insightful. • Empirical: Our extensive empirical evaluation of XAI methods for MIL on synthetic and realworld histopathology datasets is the first of its kind. It reveals that the widely used MIL explanation methods regularly yield misleading results. In contrast, xMIL-LRP sets a new state-of-the-art for explainability in AttnMIL and TransMIL models in histopathology. • Insight generation: Previous studies [33, 34] conducted qualitative assessments of heatmaps on easy-to-learn datasets like CAMELYON or TCGA NSCLC. The insights gained in these settings are limited to model debugging, i.e., \"Does the model focus on the disease area?\" To our knowledge, we are the first to present a method generating heatmaps that enable pathologists to extract fine-grained insights about the model in a difficult biomarker prediction task.", "cite_spans": [{"start": 82, "end": 85, "text": "[6,", "ref_id": "BIBREF5"}, {"start": 86, "end": 89, "text": "27,", "ref_id": "BIBREF26"}, {"start": 90, "end": 93, "text": "28,", "ref_id": "BIBREF27"}, {"start": 94, "end": 97, "text": "29,", "ref_id": "BIBREF28"}, {"start": 98, "end": 101, "text": "33,", "ref_id": "BIBREF32"}, {"start": 102, "end": 105, "text": "34,", "ref_id": "BIBREF33"}, {"start": 106, "end": 109, "text": "37,", "ref_id": "BIBREF36"}, {"start": 110, "end": 113, "text": "38,", "ref_id": "BIBREF37"}, {"start": 114, "end": 117, "text": "39,", "ref_id": "BIBREF38"}, {"start": 118, "end": 121, "text": "40]", "ref_id": "BIBREF39"}, {"start": 814, "end": 818, "text": "[33,", "ref_id": "BIBREF32"}, {"start": 819, "end": 822, "text": "34]", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "MIL formulations. In MIL, a sample is represented by a bag of instances X = {x 1 , • • • , x K } with a bag label y, where x k ∈ R D is the k-th instance. The number of instances per bag K may vary across samples. In its standard formulation [1, 2, 3] , the instances of a bag exhibit neither dependency nor ordering among each other. It is further assumed that binary instance labels y k ∈ {0, 1} exist but are not necessarily known. The binary bag label is 1 if and only if at least one instance label is 1, i.e., y = max k {y k }. Various extensions have been proposed [41, 42] , each making different assumptions about the relationships between instances and bag labels.", "cite_spans": [{"start": 242, "end": 245, "text": "[1,", "ref_id": "BIBREF0"}, {"start": 246, "end": 248, "text": "2,", "ref_id": "BIBREF1"}, {"start": 249, "end": 251, "text": "3]", "ref_id": "BIBREF2"}, {"start": 572, "end": 576, "text": "[41,", "ref_id": "BIBREF40"}, {"start": 577, "end": 580, "text": "42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Multiple instance learning (MIL)", "sec_num": "2.1"}, {"text": "MIL models. MIL architectures typically consist of three components as illustrated in Figure 2 : a backbone extracting instance representations, an aggregation function fusing the instance representations into a bag representation, and a prediction head inferring the final bag prediction. As recent foundation models for histopathology have become powerful feature extractors suitable for a wide range of tasks [29, 43, 44, 45, 46] , the weights of the backbone are often frozen, allowing for a more efficient training. For aggregation, earlier works used parameter-free mean or max pooling approaches [47, 48, 49] . Recently, attention mechanisms could improve performance, flexibly extracting relevant instance-level information using non-linear weighting [3, 6, 50] and self-attention [4, 51] . Attention MIL (AttnMIL) [3] computes a weighted average of the instances' feature vectors via a single attention head. TransMIL [4] uses a custom two-layer Transformer architecture, viewing instance representations as tokens. The bag representation is extracted from the class token at the final layer. TransMIL allows for computing arbitrary pairwise interactions between all instances relevant to the prediction task. While various extensions of AttnMIL and TransMIL have been proposed (e.g., [5, 6, 7, 8, 9, 52, 53, 10, 11, 12] ), these two methods are arguably prototypical and among the most commonly used in the digital histopathology community.", "cite_spans": [{"start": 412, "end": 416, "text": "[29,", "ref_id": "BIBREF28"}, {"start": 417, "end": 420, "text": "43,", "ref_id": "BIBREF42"}, {"start": 421, "end": 424, "text": "44,", "ref_id": "BIBREF43"}, {"start": 425, "end": 428, "text": "45,", "ref_id": "BIBREF44"}, {"start": 429, "end": 432, "text": "46]", "ref_id": "BIBREF45"}, {"start": 603, "end": 607, "text": "[47,", "ref_id": "BIBREF46"}, {"start": 608, "end": 611, "text": "48,", "ref_id": "BIBREF47"}, {"start": 612, "end": 615, "text": "49]", "ref_id": "BIBREF48"}, {"start": 759, "end": 762, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 763, "end": 765, "text": "6,", "ref_id": "BIBREF5"}, {"start": 766, "end": 769, "text": "50]", "ref_id": "BIBREF49"}, {"start": 789, "end": 792, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 793, "end": 796, "text": "51]", "ref_id": "BIBREF50"}, {"start": 823, "end": 826, "text": "[3]", "ref_id": "BIBREF2"}, {"start": 927, "end": 930, "text": "[4]", "ref_id": "BIBREF3"}, {"start": 1294, "end": 1297, "text": "[5,", "ref_id": "BIBREF4"}, {"start": 1298, "end": 1300, "text": "6,", "ref_id": "BIBREF5"}, {"start": 1301, "end": 1303, "text": "7,", "ref_id": "BIBREF6"}, {"start": 1304, "end": 1306, "text": "8,", "ref_id": "BIBREF7"}, {"start": 1307, "end": 1309, "text": "9,", "ref_id": "BIBREF8"}, {"start": 1310, "end": 1313, "text": "52,", "ref_id": "BIBREF51"}, {"start": 1314, "end": 1317, "text": "53,", "ref_id": "BIBREF52"}, {"start": 1318, "end": 1321, "text": "10,", "ref_id": "BIBREF9"}, {"start": 1322, "end": 1325, "text": "11,", "ref_id": "BIBREF10"}, {"start": 1326, "end": 1329, "text": "12]", "ref_id": "BIBREF11"}], "ref_spans": [{"start": 93, "end": 94, "text": "2", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Multiple instance learning (MIL)", "sec_num": "2.1"}, {"text": "From the few studies investigating MIL interpretability, most of them use attention heatmaps [3, 4, 6, 27, 28, 29] . Moreover, basic gradient-and propagation-based methods have been explored for specific architectures and applications [54, 55] . <PERSON><PERSON><PERSON> et al. [55] applied LRP to generate pixel-level attributions for single-cell images in a blood cancer diagnosis task, but did not consider its potential for instance-level explanations. Perturbation-based methods, building on model-agnostic approaches like SHAP [56] , perturb bag instances and compute importance scores from the resulting change in the model prediction; <PERSON> et al. [34] proposed passing bags of single instances through the model (\"single\"), dropping single instances from bags (\"one-removed\"), and sampling coalitions of instances to be removed (\"MILLI\"). <PERSON><PERSON><PERSON> et al. [33] introduced \"additive MIL\", providing directly interpretable instance scores while constraining the model's ability to capture instance interactions.", "cite_spans": [{"start": 93, "end": 96, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 97, "end": 99, "text": "4,", "ref_id": "BIBREF3"}, {"start": 100, "end": 102, "text": "6,", "ref_id": "BIBREF5"}, {"start": 103, "end": 106, "text": "27,", "ref_id": "BIBREF26"}, {"start": 107, "end": 110, "text": "28,", "ref_id": "BIBREF27"}, {"start": 111, "end": 114, "text": "29]", "ref_id": "BIBREF28"}, {"start": 235, "end": 239, "text": "[54,", "ref_id": "BIBREF53"}, {"start": 240, "end": 243, "text": "55]", "ref_id": "BIBREF54"}, {"start": 260, "end": 264, "text": "[55]", "ref_id": "BIBREF54"}, {"start": 515, "end": 519, "text": "[56]", "ref_id": "BIBREF55"}, {"start": 638, "end": 642, "text": "[34]", "ref_id": "BIBREF33"}, {"start": 843, "end": 847, "text": "[33]", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "MIL explanation methods.", "sec_num": null}, {"text": "Histopathological datasets and prediction tasks are diverse and come with various inherent challenges. We highlight the following three features.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations of MIL in histopathology", "sec_num": "2.2"}, {"text": "• Instance ambiguity. Instances are small high-resolution patches from large images. Their individual information content may be limited, as they can be subject to noise or only be interpretable as part of a larger structure. For example, it is not always possible to distinguish a benign high-grade adenoma from a malignant adenocarcinoma on a patch level due to their similar morphology. • Positive, negative, and class-wise evidence. A single bag may contain evidence for multiple classes that a MIL model needs to weigh for correct decision-making. In survival prediction, for example, a strong immune response may support longer survival, while an aggressive tumor pattern speaks for shorter survival. • Instance interactions. In many prediction tasks, it may be necessary to consider interactions between instances. A gene mutation may generate morphological alterations in the tumor area, the tumor microenvironment, and the healthy tissue, all of which may need to be considered together to reliably predict the biomarker.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations of MIL in histopathology", "sec_num": "2.2"}, {"text": "Existing MIL formulations make explicit assumptions about the relationship between instances and bag labels [42] , limiting their ability to capture the full complexity of a histopathological prediction task. The standard MIL formulation, in particular, does not consider any of the aforementioned aspects, rendering it an unsuitable framework for most histopathological settings.", "cite_spans": [{"start": 108, "end": 112, "text": "[42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Limitations of MIL in histopathology", "sec_num": "2.2"}, {"text": "Similarly, previous MIL explanation methods suffer from various shortcomings that limit their applicability in real-world histopathology datasets. The direct interpretability of attention scores is insufficient to faithfully reflect the model predictions [30, 31, 32] . Moreover, they cannot distinguish between positive, negative, or class-wise evidence [33] . Purely gradient-based explanations may suffer from shattered gradients, resulting in unreliable explanations [57] . Perturbation-based approaches come with high computational complexity. While the linear \"single\" and \"one removed\" methods require K forward passes per bag, MILLI scales quadratically with the number of instances [34] .", "cite_spans": [{"start": 255, "end": 259, "text": "[30,", "ref_id": "BIBREF29"}, {"start": 260, "end": 263, "text": "31,", "ref_id": "BIBREF30"}, {"start": 264, "end": 267, "text": "32]", "ref_id": "BIBREF31"}, {"start": 355, "end": 359, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 471, "end": 475, "text": "[57]", "ref_id": "BIBREF56"}, {"start": 691, "end": 695, "text": "[34]", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Limitations of MIL in histopathology", "sec_num": "2.2"}, {"text": "In histopathology, where bags typically contain more than 1,000 and frequently more than 10,000 instances, quadratic runtime is practically infeasible. Additive MIL and linear perturbation-based methods do not consider higher-order instance interactions. In prediction tasks depending on interactions, linear perturbation-based explanations may fail to provide faithful explanations, while additive models may not achieve competitive performances.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations of MIL in histopathology", "sec_num": "2.2"}, {"text": "Notation. We denote vectors with boldface lowercase letters (e.g., x), scalars with lowercase letters (e.g., x), and sets with uppercase letters (e.g., X).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Methods", "sec_num": "3"}, {"text": "We address the limitations discussed in Section 2.2 and introduce a more general formulation of MIL: explainable multiple instance learning (xMIL). At its core, we propose moving away from the notion of instance labels towards context-aware evidence scores, which better reflect the intricacies of histopathology while laying the foundation for developing and evaluating MIL explanation methods. Definition 3.1 (Explainable multiple instance learning). Let X = {x 1 , . . . , x K } be a bag of instances with a bag label y ∈ R.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "(1) There exists an aggregation function A that maps the bag to its label, i.e., A(X) = y. We make no assumptions about the relationship among the instances or between the instances and the label y.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "(2) There exists an evidence function E assigning an evidence score E(X, y, x k ) = ϵ k ∈ R to any instance x k in the bag, quantifying the impact the instance has on the bag label y. The aim of xMIL is to estimate (i) the aggregation function A and (ii) the evidence function E. Definition 3.2 (Properties of the evidence function). Let x k , x k ′ be instances from a bag X. We assume that E has the following properties.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "(1) Context sensitivity. The evidence score ϵ k of instance x k may depend on other instances from X.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "(2) Positive and negative evidence. If ϵ k > 0, the instance x k has a positive impact on the bag label y. If ϵ k < 0, then x k has a negative impact on y. If ϵ k = 0, then x k is irrelevant to y.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "(3) Ordering. If ϵ k > ϵ k ′ ≥ 0, then instance x k has a higher positive impact on y than x k ′ . If 0 ≥ ϵ k ′ > ϵ k , then instance x k has a higher negative impact on y than x k ′ . Similar to our definition, previous works described context sensitivity and accounting for positive and negative evidence as desirable properties of MIL explanation methods [33, 34] . However, xMIL integrates these principles directly into the formalization of the MIL problem.", "cite_spans": [{"start": 358, "end": 362, "text": "[33,", "ref_id": "BIBREF32"}, {"start": 363, "end": 366, "text": "34]", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "In contrast to previous MIL formulations, xMIL addresses the potential complexities within histopathological prediction tasks by refraining from posing strict assumptions on A. Via the evidence function E, we suggest that instances may vary in their ability to support or refute a class and that their influence may depend on the context within the bag. In practice, the evidence function is often unknown, as the notion of an \"impact\" on the bag label is hard to quantify. For the standard MIL setting, however, the binary instance labels fulfill the criteria of the evidence function. Therefore, xMIL is a more general and realistic formulation of multiple instance learning for histopathology.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "We can learn the aggregation function A via training a MIL model. To gain deeper insights into the prediction task by estimating the evidence function E, we design an explanation method for the learned aggregation function with characteristics suitable to the properties of the evidence function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL: An XAI-based framework for multiple instance learning", "sec_num": "3.1"}, {"text": "We introduce xMIL-LRP as an efficient solution to xMIL, bringing layer-wise relevance propagation (LRP) to MIL. LRP is a well-established XAI method [35, 58] with a large body of literature supporting its performance in explaining various types of architectures in different tasks [32, 36, 59, 60, 61, 62] . Starting from the prediction score of a selected class, the LRP attribution of neuron i in layer l receives incoming messages from neurons j from subsequent layer l + 1, resulting in relevance scores r", "cite_spans": [{"start": 149, "end": 153, "text": "[35,", "ref_id": "BIBREF34"}, {"start": 154, "end": 157, "text": "58]", "ref_id": "BIBREF57"}, {"start": 281, "end": 285, "text": "[32,", "ref_id": "BIBREF31"}, {"start": 286, "end": 289, "text": "36,", "ref_id": "BIBREF35"}, {"start": 290, "end": 293, "text": "59,", "ref_id": "BIBREF58"}, {"start": 294, "end": 297, "text": "60,", "ref_id": "BIBREF59"}, {"start": 298, "end": 301, "text": "61,", "ref_id": "BIBREF60"}, {"start": 302, "end": 305, "text": "62]", "ref_id": "BIBREF61"}], "ref_spans": [], "eq_spans": [], "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "(l) i = j qij i ′ q i ′ j • r (l+1) j", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": ", with q ij being the contribution of neuron i of layer l to relevance r (l+1) j1 . A variety of so-called \"propagation rules\" have been proposed [36] to specify the contribution q ij in specific model layers. For the attention mechanism, as a core component of many MIL architectures, we employ the AH-rule introduced by <PERSON> et al. [32] . In a general attention mechanism, let z k = [z kd ] d be the embedding vector of the k-th token and p kj the attention score between tokens k and j. The output vector of the attention module is y j = k p kj z k . The AH-rule of LRP treats attention scores as a constant weighting matrix during the backpropagation pass of LRP. If R(y jd ) is the relevance of the d-th dimension of y j = [y jd ] d , the AH-rule computes the relevance of the d-th feature of z k as:", "cite_spans": [{"start": 146, "end": 150, "text": "[36]", "ref_id": "BIBREF35"}, {"start": 333, "end": 337, "text": "[32]", "ref_id": "BIBREF31"}], "ref_spans": [], "eq_spans": [], "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "R(z kd ) = j z kd p kj i z id p ij R(y jd ).", "eq_num": "(1)"}], "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "This formulation can be directly applied to AttnMIL, and also adapted to a QKV attention block in a transformer, where z k is the embedding associated with the value representation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "We illustrate the effect of this rule in AttnMIL in Figure 2-B . The relevance flow separates the instances weighted by the attention mechanism into positive, negative, and neutral instances, resulting in more descriptive heatmaps that better show the relevant tissue regions compared to attention scores.", "cite_spans": [], "ref_spans": [{"start": 59, "end": 62, "text": "2-B", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "We further implement the LRP-ϵ rule for linear layers followed by ReLU activation function [58] , as well as the LN-rule to address the break of conservation in layer norm [32] , with details presented in Appendix A.2.", "cite_spans": [{"start": 91, "end": 95, "text": "[58]", "ref_id": "BIBREF57"}, {"start": 172, "end": 176, "text": "[32]", "ref_id": "BIBREF31"}], "ref_spans": [], "eq_spans": [], "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "At the instance-level, xMIL-LRP assigns each instance", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "x k = [x kd ] d ∈ R D a relevance vector r k = [r kd ] d with r kd = R(x kd ) = r (0)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "kd being the relevance score of the d-th feature of x k . We define the instance-wise relevance score as an estimate for the evidence score of the instance as εk = d r kd .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "xMIL-LRP: Estimating the evidence function", "sec_num": "3.2"}, {"text": "The properties of xMIL-LRP are particularly suitable for estimating the evidence function:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Properties of xMIL-LRP and other explanation methods", "sec_num": "3.3"}, {"text": "Context sensitivity: xMIL-LRP disentangles instance interactions and contextual information as it jointly considers the relevance flow across the whole bag. LRP and Gradient × Input (G×I) are rooted in a deep Taylor decomposition of the model prediction [63] and consequently capture dependencies between features by tracing relevance flow through the components of the MIL model. While attention is context-aware, it is limited to considering dependencies of features at a specific layer. The \"single\" method is unaware of context. \"One-removed\" and additive MIL can only capture the impact of individual instances on the prediction.", "cite_spans": [{"start": 254, "end": 258, "text": "[63]", "ref_id": "BIBREF62"}], "ref_spans": [], "eq_spans": [], "section": "Properties of xMIL-LRP and other explanation methods", "sec_num": "3.3"}, {"text": "Positive and negative evidence: xMIL-LRP relevance scores are real-valued and can identify whether an instance supports or refutes the model prediction. Features irrelevant to the prediction will receive an explanation score close to zero. Therefore, the range of explanation scores matches the range of the assumed evidence function. The same holds for additive MIL, MILLI, and \"one-removed\". Attention and \"single\" do not distinguish between positive and negative evidence.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Properties of xMIL-LRP and other explanation methods", "sec_num": "3.3"}, {"text": "Conservation: Following the conservation principle of LRP, xMIL-LRP provides an instance-wise decomposition of the model output, i.e., k εk = k,d r kd = y. This instance-level conservation also holds for additive MIL, but not for the other discussed methods. The local conservation principle of LRP [36] further allows us to analyze attribution scores at the instance feature vector level without requiring propagation through the foundation model-the instance-wise attribution scores are the same at any layer of the model.", "cite_spans": [{"start": 299, "end": 303, "text": "[36]", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Properties of xMIL-LRP and other explanation methods", "sec_num": "3.3"}, {"text": "Baseline methods. We compared several explanation methods to our xMIL-LRP (see Appendix A.1 for details). For AttnMIL and TransMIL, we selected Gradient × Input (G×I) [64, 65] and Integrated gradients (IG) [66] as gradient-based baselines. We further included the \"single\" perturbation method (single) [34] , which involves using predictions for individual instances as explanation scores. Single is the only computationally feasible perturbation-based approach for the bag sizes considered here (up to 24,000). We evaluated raw attention scores for AttnMIL and attention rollout [67] for TransMIL (attn). In the random baseline (rand), instance scores were randomly sampled from a standard normal distribution. For additive attention MIL (AddMIL) [33] , we assessed raw attention scores (attn) and the model-intrinsic instance-wise predictions (logits).", "cite_spans": [{"start": 167, "end": 171, "text": "[64,", "ref_id": "BIBREF63"}, {"start": 172, "end": 175, "text": "65]", "ref_id": "BIBREF64"}, {"start": 206, "end": 210, "text": "[66]", "ref_id": "BIBREF65"}, {"start": 302, "end": 306, "text": "[34]", "ref_id": "BIBREF33"}, {"start": 580, "end": 584, "text": "[67]", "ref_id": "BIBREF66"}, {"start": 748, "end": 752, "text": "[33]", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Experiments and results", "sec_num": "4"}, {"text": "We designed novel toy experiments to assess and compare the characteristics of xMIL-LRP and the baseline methods for AttnMIL, TransMIL, and AddMIL in controlled settings. We focused on evaluating to what extent the explanations account for context sensitivity and positive and negative evidence, i.e., the first two characteristics of the evidence function according to Definition 3.2, which we consider crucial aspects for explaining real-world histopathology prediction tasks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Toy experiments", "sec_num": "4.1"}, {"text": "Inspired by previous works [3, 34] , we sampled bags of MNIST images [68] , with each instance representing a number between 0 and 9. We defined three MIL tasks for these bags:", "cite_spans": [{"start": 27, "end": 30, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 31, "end": 34, "text": "34]", "ref_id": "BIBREF33"}, {"start": 69, "end": 73, "text": "[68]", "ref_id": "BIBREF67"}], "ref_spans": [], "eq_spans": [], "section": "Toy experiments", "sec_num": "4.1"}, {"text": "• 4-Bags: The bag label is class 1 if 8 is in the bag, class 2 if 9 is in the bag, class 3 if 8 and 9 are in the bag, and class 0 otherwise. The dataset was proposed by <PERSON> et al. [34] . In this setting, the model needs to learn basic instance interactions. • Pos-Neg: We define 4, 6, 8 as positive and 5, 7, 9 as negative numbers. The bag label is class 1 if the amount of unique positive numbers is strictly greater than that of unique negative numbers, and class 0 otherwise. The model needs to adequately weigh positive and negative evidence to make correct predictions. • Adjacent <PERSON>irs: The bag label is class 1 if it contains any pair of consecutive numbers between 0 and 4, i.e., (0,1), (1,2), (2,3) or (3, 4) , and class 0 otherwise. In this case, the impact of an instance is contextual, as it depends on the presence or absence of adjacent numbers.", "cite_spans": [{"start": 182, "end": 186, "text": "[34]", "ref_id": "BIBREF33"}, {"start": 713, "end": 716, "text": "(3,", "ref_id": "BIBREF2"}, {"start": 717, "end": 719, "text": "4)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Toy experiments", "sec_num": "4.1"}, {"text": "To assess the explanation quality, we first defined valid evidence scores as ground truths according to Definition 3.2. For each dataset, we require one evidence function per predicted class c, denoted by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Toy experiments", "sec_num": "4.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E (c) (X, y, x k ) = ϵ (c) k . We assigned ϵ (c) k = 1 if x k supports class c, ϵ", "eq_num": "(c)"}], "section": "Toy experiments", "sec_num": "4.1"}, {"text": "k = -1 if the instance refutes class c, and ϵ (c) k = 0 if it is irrelevant. We aimed to measure whether an explanation method correctly distinguishes instances with positive, neutral, and negative evidence scores. Therefore, we computed a two-class averaged area under the precision-recall curve (AUPRC-2), measuring if the positive instances received the highest and the negative instances the lowest explanation scores. We assessed AttnMIL and TransMIL models and repeated each experiment 30 times. The details of the ground truth, the evaluation metric, and the experimental setup are provided in Appendix A.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Toy experiments", "sec_num": "4.1"}, {"text": "Table 1 displays the test AUROC scores of the three models across datasets, demonstrating that the models solve the tasks to varying degrees, alongside the performances of the explanation methods. We find that xMIL-LRP outperformed the other explanation approaches across MIL models and datasets in all but one setting. It reached particularly high AUPRC-2 scores in the 4-Bags and Pos-Neg datasets while being most robust in the more difficult Adjacent Pairs setting. Attention severely suffered from the presence of positive and negative evidence, which it cannot distinguish by design. While IG performed comparably to xMIL-LRP for AttnMIL models, it was inferior for TransMIL. Notably, the test AUROC of AddMIL was worse in all settings, resulting in explanations that are not competitive with the post-hoc explanation methods on AttnMIL and TransMIL. This supports our point that AddMIL may not perform competitively in difficult prediction tasks. The single perturbation method provided good explanations in the Pos-Neg setting, where numbers have a fixed evidence score irrespective of the other instances in the bag. However, in 4-Bags and Adjacent Pairs, the method's performance decreased, as it always assigns the same score to the same instance regardless of the bag context. In contrast, xMIL-LRP is both context-sensitive and identifies positive and negative instances. Since we expect that these aspects are common features of many Table 1 : Results of the toy experiments. We report AUPRC-2 scores of MIL explanation methods on three toy datasets measuring how well a method identified instances with positive and negative evidence scores (mean ± std. over 30 repetitions). The highest mean scores are bold and the second highest are underlined. We also display the model performances (\"Test AUROC\", mean ± std.). real-world histopathological datasets, we conclude that our method is the only suitable approach for such complex settings.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "1", "ref_id": null}, {"start": 1453, "end": 1454, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Toy experiments", "sec_num": "4.1"}, {"text": "Datasets and model training. To evaluate the performance of explanations on real-world histopathology prediction tasks, we considered four diverse datasets of increasing task difficulty covering tumor detection, disease subtyping, and biomarker prediction. These datasets had previously been used for benchmarking in multiple studies [12, 33, 46, 69] .", "cite_spans": [{"start": 334, "end": 338, "text": "[12,", "ref_id": "BIBREF11"}, {"start": 339, "end": 342, "text": "33,", "ref_id": "BIBREF32"}, {"start": 343, "end": 346, "text": "46,", "ref_id": "BIBREF45"}, {"start": 347, "end": 350, "text": "69]", "ref_id": "BIBREF68"}], "ref_spans": [], "eq_spans": [], "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "• CAMELYON16 [70] consists of 400 sentinel lymph node slides, of which 160 carry to-berecognized metastatic lesions of different sizes. It is a well-established tumor detection dataset. • The TCGA NSCLC dataset (abbreviated as NSCLC) contains 529 slides with lung adenocarcinoma (LUAD) and 512 with lung squamous cell carcinoma (LUSC). The prediction task is to distinguish these two non-small cell lung cancer (NSCLC) subtypes. • The TCGA HNSC HPV dataset [12] (abbreviated as HNSC HPV) has 433 slides of head and neck squamous cell carcinoma (HNSC). 43 of them were affected by a human papillomavirus (HPV) infection diagnosed via additional testing [71] . HPV infection is an essential biomarker guiding prognosis and treatment [12] . The task is to identify the HPV status directly from the slides. Label imbalances and the complexity of the predictive signature are key challenges in this task. • The TCGA LUAD TP53 (abbreviated as LUAD TP53) dataset contains 529 lung adenocarcinoma (LUAD) slides, 263 of which exhibit a mutation of the TP53 gene, which is one of the most common mutations across cancers. In lung cancer, it is associated with poorer prognosis and resistance to chemotherapy and radiation [72] . Previous works showed that TP53 mutation can be predicted from LUAD slides [69, 73] .", "cite_spans": [{"start": 13, "end": 17, "text": "[70]", "ref_id": "BIBREF69"}, {"start": 457, "end": 461, "text": "[12]", "ref_id": "BIBREF11"}, {"start": 652, "end": 656, "text": "[71]", "ref_id": "BIBREF70"}, {"start": 731, "end": 735, "text": "[12]", "ref_id": "BIBREF11"}, {"start": 1212, "end": 1216, "text": "[72]", "ref_id": "BIBREF71"}, {"start": 1294, "end": 1298, "text": "[69,", "ref_id": "BIBREF68"}, {"start": 1299, "end": 1302, "text": "73]", "ref_id": "BIBREF72"}], "ref_spans": [], "eq_spans": [], "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "We generated patches at 20x magnification and obtained 10,454 ± 6,236 patches per slide across all datasets (mean ± std.). Features were extracted using the pre-trained CTransPath [43] foundation model and aggregated using AttnMIL or TransMIL. 2 Additional details regarding the datasets and training procedure are described in Appendix A.4.", "cite_spans": [{"start": 180, "end": 184, "text": "[43]", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "We report the mean and standard deviation of the test set AUROC over 5 repetitions in Table 2 . In all but one case, TransMIL outperformed AttnMIL, with the largest margin observed in the difficult TP53 dataset. Our results generally align with performances reported in previous works [12, 46, 69] .", "cite_spans": [{"start": 285, "end": 289, "text": "[12,", "ref_id": "BIBREF11"}, {"start": 290, "end": 293, "text": "46,", "ref_id": "BIBREF45"}, {"start": 294, "end": 297, "text": "69]", "ref_id": "BIBREF68"}], "ref_spans": [{"start": 92, "end": 93, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "Faithfulness evaluation. As the evidence functions E of our histopathology datasets are unknown, we resorted to assessing faithfulness, i.e., how accurately explanation scores reflect the model prediction [74, 75] . The primary goal of the faithfulness experiments is to evaluate the ordering of relevance scores (Property 3 of the evidence function in Definition 3.2). Faithfulness can be quantified by progressively excluding instances from the most relevant first (MORF) to the least relevant last and measuring the change in prediction score. The area under the resulting perturbation curve (AUPC) indicates how faithfully the identified ordering of the instances affects the model prediction. The lower the AUPC score, the more faithful the method. We calculated AUPC for correctly classified slides. Further methodological details are provided in Appendix A.5.", "cite_spans": [{"start": 205, "end": 209, "text": "[74,", "ref_id": "BIBREF73"}, {"start": 210, "end": 213, "text": "75]", "ref_id": "BIBREF74"}], "ref_spans": [], "eq_spans": [], "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "In Figure 3 , we show the perturbation curves and AUPC boxplots for the patch-dropping experiment for TransMIL in our four datasets (Figure 4 shows the results for AttnMIL). Additionally, we summarize our results in Table 2 . To test the difference in the AUPC values among the baseline explanation methods, we performed paired t-tests between the random baseline vs. all methods and xMIL-LRP vs. all other baselines. The p-values were corrected using the Bonferroni method for multiple comparison correction. All tests resulted in significant differences except for random baseline vs. G×I for CAMELYON16 and attention for HNSC HPV.", "cite_spans": [], "ref_spans": [{"start": 10, "end": 11, "text": "3", "ref_id": "FIGREF5"}, {"start": 140, "end": 141, "text": "4", "ref_id": "FIGREF8"}, {"start": 222, "end": 223, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "xMIL-LRP significantly achieved the lowest average AUPC compared to the baselines, providing the most faithful explanations across all tasks and model architectures. Especially evident with the TransMIL model, xMIL-LRP accurately decomposed the mixing of patch information via selfattention. Notably, the largest margin of xMIL-LRP to other methods could be observed in the more challenging biomarker prediction tasks of the HNSC HPV and LUAD TP53 datasets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "The results also reflect whether the explanation scores contain meaningful positive/negative evidence for the target class (Property 2 of the evidence function in Definition 3.2): if so, we expect the model's prediction to flip when all patches supporting the target class are excluded. In Figure 3 , the model decision always flips when patches are excluded based on xMIL-LRP scores, whereas other methods show inconsistent results.", "cite_spans": [], "ref_spans": [{"start": 297, "end": 298, "text": "3", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "Attention scores, as the most widely used explanation approach for MIL in histopathology, did not provide faithful explanations outside the simple tumor detection setting in the CAMELYON16 dataset. This remarkably highlights their limited usefulness as model explanations and confirms previously reported results in other domains [30, 31, 32] . Passing single instances through the model (\"single\") achieved good faithfulness scores for simpler tasks and AttnMIL, but performed worse for Transformer-based biomarker prediction.", "cite_spans": [{"start": 330, "end": 334, "text": "[30,", "ref_id": "BIBREF29"}, {"start": 335, "end": 338, "text": "31,", "ref_id": "BIBREF30"}, {"start": 339, "end": 342, "text": "32]", "ref_id": "BIBREF31"}], "ref_spans": [], "eq_spans": [], "section": "Histopathology experiments", "sec_num": "4.2"}, {"text": "The identification of predictive features for HPV infection in head and neck carcinoma from histopathological slides is a challenging task for pathologists. In this task, there are partially known morphological patterns associated with the class label. We provide a brief overview of the known histological features differentiating HPV-negative and HPV-positive HNSC in Appendix A.7 and Figure 5 . In the following, we demonstrate how faithful xMIL-LRP explanations can support pathologists in gaining insights about the model strategy and inferring task-relevant features.", "cite_spans": [], "ref_spans": [{"start": 394, "end": 395, "text": "5", "ref_id": null}], "eq_spans": [], "section": "Extracting insights from xMIL-LRP heatmaps", "sec_num": "5"}, {"text": "We extracted explanation scores for the best-performing TransMIL models. To increase the readability of resulting heatmaps, we clipped the scores per slide at the whiskers of their boxplots, which extended 1.5 times the interquartile range from the first and third quartiles. We then translated them into a zero-centered red-blue color map, with red indicating positive and blue negative scores. Notice that the explanation methods operate on different scales. For xMIL-LRP, a positive relevance score indicates support for the explained label, while a negative score contradicts it. The first row depicts the perturbation curves, where the solid lines are the average perturbation curve and the shaded area is the standard error of the mean at each perturbation step. Each boxplot on the second row shows the distribution of AUPC values for all test set slides per explanation methods. In each boxplot, the red line marks the median and the red dot marks the mean. Lower perturbation curves and AUPCs represent higher faithfulness.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Extracting insights from xMIL-LRP heatmaps", "sec_num": "5"}, {"text": "We revisit the example of the HNSC tumor with a false-positive prediction of an HPV infection in Figure 1 . As previously noted, only xMIL-LRP indicates that the model recognizes evidence of HPV infection in the tumor border, but not the remaining tumor. Despite a prediction score close to 0, all relevance scores from the single method were between 0.95-0.97, suggesting that context-free single-instance bags may not be informative in this task. We observed this phenomenon across various slides.", "cite_spans": [], "ref_spans": [{"start": 104, "end": 105, "text": "1", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Extracting insights from xMIL-LRP heatmaps", "sec_num": "5"}, {"text": "Heatmaps of additional examples are provided in Appendix A.7. In Figure 6 , xMIL-LRP accurately delineates and distinguishes HPV-positive tumor islands from the surrounding stroma. In this simple case, attention also provides a reasonable explanation. Figure 7 presents another correctly classified HPV-positive sample. Here, xMIL-LRP outlines spatially consistent slide regions with clear positive evidence, distinct from regions of negative or mixed evidence (top row). Most notably, the subepithelial mucous glands (bottom row), which are not associated with HPV, are correctly highlighted in blue, unlike in the attention map. In Figure 8 , we display a false positive slide. In this case, xMIL-LRP allowed us to identify that the evidence of HPV-positivity can be attributed to an unusual morphology of an HPV-negative tumor that shares some morphological features usually associated with HPV infection (e.g., smaller tumor cells with hyperchromatic nuclei, dense lymphocyte infiltrates).", "cite_spans": [], "ref_spans": [{"start": 72, "end": 73, "text": "6", "ref_id": null}, {"start": 259, "end": 260, "text": "7", "ref_id": "FIGREF10"}, {"start": 641, "end": 642, "text": "8", "ref_id": "FIGREF11"}], "eq_spans": [], "section": "Extracting insights from xMIL-LRP heatmaps", "sec_num": "5"}, {"text": "We introduced xMIL, a more general and realistic MIL framework for histopathology, formalizing requirements for MIL explanations via the evidence function. We adapted LRP to MIL as xMIL-LRP, experimentally demonstrated its advantages over previous explanation approaches, and showed how access to faithful explanations can enable pathologists to extract insights from a biomarker prediction model. Thus, xMIL is a step toward increasing the reliability of clinical ML systems and driving medical knowledge discovery, particularly in histopathology. Despite being motivated by the challenges in histopathology, our approach presented here can be directly transferred to other problem settings that require explaining complex MIL models, e.g., in video, audio, or text domains. Furthermore, a detailed analysis of potentially complex dependencies between instances, especially in the context of multi-modal inputs, represents a promising direction for future research.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "A.1 Baseline MIL explanation methods Attention maps Attention scores have commonly been used as an explanation of the model by considering attention heatmaps, assuming that they reflect the importance of input features [76] .", "cite_spans": [{"start": 219, "end": 223, "text": "[76]", "ref_id": "BIBREF75"}], "ref_spans": [], "eq_spans": [], "section": "A Appendix", "sec_num": null}, {"text": "In AttnMIL, a bag representation is computed as an attention-weighted average of instance-level representations, i.e.,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Appendix", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "g(X) = K k=1 a k f (x k ), a k = softmax w T tanh(V f (x k ) T ) ⊙ sigm(U f (x k ) T )", "eq_num": "(2)"}], "section": "A Appendix", "sec_num": null}, {"text": "where f (x k ) is an instance and g(X) the bag representation. The attention scores a k assign to each patch an attribution with 0 ≤ a k ≤ 1, and have been used as instance-wise explanation scores [3] .", "cite_spans": [{"start": 197, "end": 200, "text": "[3]", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "A Appendix", "sec_num": null}, {"text": "In TransMIL, the attention heads deliver self-attention vectors A l h ∈ R (K+1)×(K+1) for each head h and Transformer layer l, recalling that the first token is the class token. Mean pooling is often used for fusing the self-attention matrices of different heads, i.e., A l = A l h h . The attention scores from the class token to the instance tokens can be used as attribution scores, i.e., A l", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Appendix", "sec_num": null}, {"text": "(1,2:) . Alternatively, attention rollout has been proposed to summarize the self-attention matrices over layers [67] . For a model with L Transformer layers, attention rollout combines {A l } L l=1 as Ã =", "cite_spans": [{"start": 113, "end": 117, "text": "[67]", "ref_id": "BIBREF66"}], "ref_spans": [], "eq_spans": [], "section": "A Appendix", "sec_num": null}, {"text": "Ǎl where Ǎl = 0.5A l + 0.5I, with I being the identity matrix. Then, similar to the layer-wise attention scores, the heatmap is defined as the attention rollout of the class token to the instances, i.e., Ã(1,2:) .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "L l=1", "sec_num": null}, {"text": "Gradient-based methods Gradient-based methods utilize gradient information to derive feature relevance scores. <PERSON><PERSON><PERSON> et el. [54] combined raw gradients to identify the most relevant features and derive tiles that activate these features the most.", "cite_spans": [{"start": 127, "end": 131, "text": "[54]", "ref_id": "BIBREF53"}], "ref_spans": [], "eq_spans": [], "section": "L l=1", "sec_num": null}, {"text": "Various other gradient-based methods have been proposed in the XAI literature, including saliency maps and Gradient × Input (G×I) [64, 65] and Integrated Gradients (IG) [66] . These methods can easily be adapted to compute explanations in MIL. We obtain the gradient of a MIL model prediction ŷ with respect to a patch ∇ŷ(x k ).", "cite_spans": [{"start": 130, "end": 134, "text": "[64,", "ref_id": "BIBREF63"}, {"start": 135, "end": 138, "text": "65]", "ref_id": "BIBREF64"}, {"start": 169, "end": 173, "text": "[66]", "ref_id": "BIBREF65"}], "ref_spans": [], "eq_spans": [], "section": "L l=1", "sec_num": null}, {"text": "For G×I, we can then define the relevance score of the k-th instance as d [∇ŷ(x k )] d x kd , with x kd being the d-th feature of x k .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "L l=1", "sec_num": null}, {"text": "Integrated gradients (IG) [66] computes the gradients of the model's output with respect to the input, integrated over a path from a baseline to the actual input. The baseline is typically set to zero, and so we do. The explanation score of the k-th instance is computed as d IG(x kd ), where the relevance score of the d-th feature of the k-th instance IG(x kd ) is computed as", "cite_spans": [{"start": 26, "end": 30, "text": "[66]", "ref_id": "BIBREF65"}], "ref_spans": [], "eq_spans": [], "section": "L l=1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "IG(x kd ) = x kd • 1 α=0 f (αX) ∂x kd dα,", "eq_num": "(3)"}], "section": "L l=1", "sec_num": null}, {"text": "where f is the model and X is the K × D feature matrix of the bag (with K being the number of instances and D being the number of features for each instance). We used the implementation of IG available in Captum [77] with the internal batch size set to the number of instances in a bag.", "cite_spans": [{"start": 212, "end": 216, "text": "[77]", "ref_id": "BIBREF76"}], "ref_spans": [], "eq_spans": [], "section": "L l=1", "sec_num": null}, {"text": "The idea of perturbation-based explanation methods is to perturb selected instances of a bag and derive importance scores from the resulting change in the model prediction. It builds on model-agnostic post-hoc local interpretability methods like LIME [78] and SHAP [56] .", "cite_spans": [{"start": 251, "end": 255, "text": "[78]", "ref_id": "BIBREF77"}, {"start": 265, "end": 269, "text": "[56]", "ref_id": "BIBREF55"}], "ref_spans": [], "eq_spans": [], "section": "Perturbation-based methods", "sec_num": null}, {"text": "<PERSON> et al. [34] proposed and evaluated multiple perturbation-based methods of different complexity.", "cite_spans": [{"start": 13, "end": 17, "text": "[34]", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Perturbation-based methods", "sec_num": null}, {"text": "The \"single\" method passes bags of single patches X k = {x k } for k = 1, . . . , K through the model and uses the outcome f (X k ) as explanation score. \"One removed\" drops single patches, i.e. constructs bags Xk = X\\X k for k = 1, . . . , K and defines the difference to the original prediction score f (X) -f ( Xk ) as explanation. The \"combined\" approach takes the mean of these two scores. As these methods cannot account for patch interactions, <PERSON> et al. also propose an algorithm to sample coalitions of patches to be perturbed, called MILLI. They show that MILLI outperforms the baselines on toy datasets when instance interactions need to be considered. The complexity of MILLI is O(nK 2 ), where n is the number of coalitions and K is the bag size.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Perturbation-based methods", "sec_num": null}, {"text": "Additive MIL The idea of additive MIL [33] is to make the MIL model inherently interpretable by designing the bag-level prediction to be a sum of individual instance predictions. Let function f be a feature extractor and ψ m , ψ p MLPs. In many cases, particularly for Attention MIL [3] , a MIL model p can be written as", "cite_spans": [{"start": 38, "end": 42, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 283, "end": 286, "text": "[3]", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "Perturbation-based methods", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(X) = ψ p K k=1 a k f (x k ) with a k = softmax k (ψ m (X)),", "eq_num": "(4)"}], "section": "Perturbation-based methods", "sec_num": null}, {"text": "where a k is the attention score of instance k. For Attention MIL, ψ m is defined as the inner part of the softmax function of Equation 2, and ψ p as prediction head outputting class logits. To obtain an additive model, the authors suggest to instead compute", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Perturbation-based methods", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p(X) = K k=1 ψ p (a k f (x k )) . (", "eq_num": "5"}], "section": "Perturbation-based methods", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Perturbation-based methods", "sec_num": null}, {"text": "This way, the bag prediction becomes the sum of the individual instance predictions ψ p (a k f (x k )), which can be used as instance explanation scores. These instance logits are proportional to the Shapley values of the instances [33] . In our experiments, we consider the proposed additive variant of Attention MIL (AddMIL).", "cite_spans": [{"start": 232, "end": 236, "text": "[33]", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Perturbation-based methods", "sec_num": null}, {"text": "LRP is a method for explaining neural network predictions by redistributing the output's relevance back through the network to the input features. The redistribution follows a relevance conservation principle, where the total relevance of each layer is preserved as it propagates backward. If r (l) j denotes the relevance of neuron j in layer l, conservation means that j r", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "(l1) j = i r (l2) i", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "holds for any two layers l 1 and l 2 . As a general principle, LRP posits", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "r (l) i = j q ij i ′ q i ′ j • r (l+1) j ,", "eq_num": "(6)"}], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "with q ij being the contribution of neuron i of layer l relevance r (l+1) j", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": ". There are \"propagation rules\" for various layer types [36, 58] that specify q ij for different setups.", "cite_spans": [{"start": 56, "end": 60, "text": "[36,", "ref_id": "BIBREF35"}, {"start": 61, "end": 64, "text": "58]", "ref_id": "BIBREF57"}], "ref_spans": [], "eq_spans": [], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "Feed forward neural network. The following generic rule holds for propagating relevance through linear layers followed by ReLU [36] :", "cite_spans": [{"start": 127, "end": 131, "text": "[36]", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "r (l) i = j a j ρ(w ij ) ϵ + i ′ a i ′ ρ(w i ′ j ) • r (l+1) j ,", "eq_num": "(7)"}], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "where a j is the activation of neuron j in layer l, w ij the weight from neuron i of layer l to neuron j of layer l + 1, ϵ a stabilizing term to prevent numerical instabilities, and ρ(w ij ) a modification of the weights of the linear layer. For example, if ρ(w ij ) = w ij + γmax(w ij , 0), then Equation 7 is called LRP-γ rule. For γ = 0, this equation is called LRP-ϵ rule.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "LayerNorm. Assume z k is the embedding of the k-th token and y k = LayerNorm(z k ) as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "y k = z k -E{z} std{z} + ϵ ,", "eq_num": "(8)"}], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "where E{z} and std{z} are the expected values and standard deviation of the tokens.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "For propagating relevance through LayerNorm, <PERSON> et al. [32] suggested the LN-rule as the following:", "cite_spans": [{"start": 56, "end": 60, "text": "[32]", "ref_id": "BIBREF31"}], "ref_spans": [], "eq_spans": [], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "R(z kd ) = j z kd (δ kj -1 N ) i z id (δ ij -1 N ) R(y jd ),", "eq_num": "(9)"}], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "where δ kj = 1, if k = j 0, otherwise and z kd is the d-th dimension of z k and R(z kd ) is the relevance assigned to it. In practice, LN-rule is implemented by detaching std{z} and handling it as a constant.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Layer-wise Relevance Propagation (LRP)", "sec_num": null}, {"text": "Evidence functions. We define the evidence functions for the three datasets as follows. We write x k ∼ n to indicate that instance k represents MNIST number n.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "• In the 4-Bags dataset, 8 supports classes 1 and 3 but refutes classes 0 and 2, while 9 supports classes 2 and 3 but refutes classes 0 and 1. Hence, for x k ∼ 8, we define ϵ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "(c) k = 1 for c ∈ {1, 3} and ϵ (c) k = -1 for c ∈ {0, 2}. For x k ∼ 9, we set ϵ (c) k = 1 for c ∈ {2, 3} and ϵ (c) k = -1 for c ∈ {0, 1}. In all other cases, ϵ (c) k = 0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "• In Pos-Neg, 4, 6, and 8 instances support class 1 and refute class 0, and vice versa for 5, 7, 9.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "Hence, we set ϵ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "k = 1 and ϵ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(0) k = -1 if x k ∼ {4, 6, 8}, ϵ", "eq_num": "(1)"}], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "k = -1 and ϵ ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "(0) k = 1 if x k ∼ {5, 7,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "k = 0 otherwise. The evidence scores for the other numbers are defined accordingly.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "Evaluation metric. We aim to measure whether an explanation method correctly distinguishes instances with positive, neutral, and negative evidence scores. We separate this into two steps: quantify the separation between positive and non-positive instances, and quantify the separation between negative and non-negative instances. Let e (c) = [ϵ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(c) 1 , . . . , ϵ", "eq_num": "(c)"}], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "K ] be the evidence scores for some bag X = {x 1 , . . . , x K } and class c, and", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "s (c) = [ε (c) 1 , . . . , ε", "eq_num": "(c)"}], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "K ] be the class-wise explanation scores from some explanation method. We define e (c) pos = min(e (c) , 0) and e (c) neg = min(-e (c) , 0) as the binarized positive and negative evidence, and compute", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "AUPRC-2 = 1 2 • AUPRC(e (c) pos , s (c) ) + AUPRC(e (c) neg , -s (c) ) .", "eq_num": "(10)"}], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "We utilize the area under the precision-recall curve (AUPRC) to account for potential imbalances.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "Our AUPRC-2 metric can be interpreted as the one-vs-all AUPRC score for detecting positive and negative instances. It becomes 1 if all instances with positive / negative evidence have been assigned the highest / lowest evidence scores. For each dataset and explanation method, we computed the AUPRC-2 across all classes and test bags and report the average score.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "Experimental details. Instead of training end-to-end MIL models, we obtained feature vectors with 512 dimensions for each MNIST image via a ResNet18 model pre-trained on Imagenet from the TorchVision library [79] . For each bag, we first sampled a subset of numbers, where each number was selected with a probability of 0.5, and then randomly drew 30 MNIST feature vectors from this subset. We used 2,000 bags for training, 500 for validation, and 1,000 for testing. We trained AttnMIL and TransMIL models with a learning rate of 0.0001 for a maximum of 1000 and 200 epochs for AttnMIL and TransMIL, respectively. We finally selected the model with the lowest validation loss. We repeated each model training 30 times and report means and standard deviations across repetitions. Each experiment with its repetitions was run on single CPUs in less than 24 hours, respectively. We used the same setting for training AddMIL, but with an Adam optimizer as in the original paper [33] .", "cite_spans": [{"start": 208, "end": 212, "text": "[79]", "ref_id": "BIBREF78"}, {"start": 974, "end": 978, "text": "[33]", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "A.3 Toy experiments: Training and evaluation details", "sec_num": null}, {"text": "Dataset details. We downloaded TCGA HNSC, LUAD, and LUSC datasets from TCGA website. The HPV status of HNSC dataset and the TP53 mutations of LUAD dataset were downloaded from cBioPortal [80, 81, 82] . We applied the following splits.", "cite_spans": [{"start": 187, "end": 191, "text": "[80,", "ref_id": "BIBREF79"}, {"start": 192, "end": 195, "text": "81,", "ref_id": "BIBREF80"}, {"start": 196, "end": 199, "text": "82]", "ref_id": "BIBREF81"}], "ref_spans": [], "eq_spans": [], "section": "A.4 Histopathology experiments: Data and training details", "sec_num": null}, {"text": "• CAMELYON16: We used the pre-defined test set of 130 slides, and randomly split the remaining slides into 230 for training and 40 for validation. • NSCLC: As in previous works [4, 33] , we randomly split the slides into 60% training, 15% validation, and 25% test data. • HNSC HPV: Due to the low number of HPV-positive samples, we uniformly split the dataset into three cross-validation folds like in previous work [12] . • LUAD TP53: We randomly split the slides into 60% training, 15% validation, and 25% test data. From TCGA-BA-A6DB", "cite_spans": [{"start": 177, "end": 180, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 181, "end": 184, "text": "33]", "ref_id": "BIBREF32"}, {"start": 416, "end": 420, "text": "[12]", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "A.4 Histopathology experiments: Data and training details", "sec_num": null}, {"text": "From TCGA-BA-5559 NeurIPS Paper Checklist Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HPV-negative", "sec_num": null}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HPV-positive", "sec_num": null}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HPV-positive", "sec_num": null}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HPV-positive", "sec_num": null}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Justification: (1) our data are from public datasets that have followed the guidelines of their corresponding ethics committee. ( 2) we declare no known potential harmful consequence of our submitted work regarding the mentioned points in the code of ethics. (3) if applicable, we implemented all the impact mitigation measures mentioned in the code of ethics, such as disclosing essential elements for reproducibility. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction). 10. Broader Impacts Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed? Answer: [NA] Justification: We anticipate no negative societal impact for our work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)? Answer: [NA] Justification: We are aware of no potential risk for any misuse of our work. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort. 12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected? Answer: [Yes] Justification: (1) We cited the open datasets properly in Section 4.2. (2) if some parts of other repositories have been used in our codes, we have cited the repository properly. These repositories have MIT license or GPLv3 license for non-commercial academic use. Guidelines:", "cite_spans": [{"start": 878, "end": 881, "text": "(1)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "In some XAI research papers, uppercase letters, e.g., R (l) j , are used for denoting relevance values.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "We did not include AddMIL in the real-world experiments, as it is difficult to compare heatmaps from different models without having a ground truth like in the toy experiments (Section 4.1). Also notice that faithfulness evaluations are not applicable, since AddMIL explanations are faithful by design[33].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "The results shown here are in whole or part based upon data generated by the TCGA Research Network: https://www.cancer.gov/tcga. This work was in part supported by the German Ministry for Education and Research (BMBF) under Grants 01IS14013A-E, 01GQ1115, 01GQ0850, 01IS18025A, 031L0207D, 01IS18037A, and BIFOLD24B and by the Institute of Information & Communications Technology Planning & Evaluation (IITP) grants funded by the Korea government (MSIT) (No. 2019-0-00079, Artificial Intelligence Graduate School Program, Korea University and No. 2022-0-00984, Development of Artificial Intelligence Technology for Personalized Plug-and-Play Explanation and Verification of Explanation).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "Preprocessing details. We extracted patches from the slides of 256 × 256 pixels without overlap at 20x magnification (0.5 microns per pixel). We identified and excluded background patches via Otsu's method [83] on slide thumbnails and applied a patch-level minimum standard deviation of 8.Training details. For training, we sampled bags of 2048 patches per slide and passed their feature representations through the MIL model. For validation and testing, we simultaneously exposed all patches of a slide to the model to avoid sampling biases and statistical instabilities. Due to the computational complexity of TransMIL, we excluded slides with more than 24,000 patches (≈ 6% of all slides). We did this for all methods to ensure fair comparisons. AttnMIL models were trained for up to 1,000 epochs with batch size 32, and the TransMIL models for up to 200 epochs with batch size 5. We selected the checkpoint with the highest validation AUC. In the HNSC HPV dataset, we used one fold as validation and test fold and two folds as training folds and repeated this procedure for all possible assignments of folds. We applied a grid search over learning rates and dropout schemes and selected the hyper-parameter settings with the highest mean validation AUCs over 5 repetitions. For AttnMIL, we found that the best configuration was always a learning rate of 0.002 and no dropout. For TransMIL, we ended up with a learning rate of 0.0002 and high dropout (0.2 after the feature extractor, 0.5 after the self-attention blocks and before the final classification layer) for CAMELYON16 and NSCLC, and a learning rate of 0.002 without dropout for HNSC HPV and LUAD TP53. The training was done on an A100 80GB GPU.", "cite_spans": [{"start": 206, "end": 210, "text": "[83]", "ref_id": "BIBREF82"}], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "Given a slide X = {x k } K k=1 and a heatmaping function H producing the explanation scores of the instances in X, i.e., H(x k ) = εk , we binned the patches of slide X into 100 ordered groups, where E i is the set of all patches whose attribution scores are between the (100 -i)th and (100 -i + 1)-th percentiles of the explanation scores of the instances of X, for example, E 1 is the set of the most relevant 1% patches of X and E 100 is the least relevant 1% of the patches.Patch dropping. Following the region perturbation strategy introduced in [74] , we progressively excluded the most relevant regions from slide X, i.e. in the n-th iteration, the most relevant n% of patches were excluded. Formally, the perturbation procedure can be formulated as the following:where P(X, n) is a perturbation function that excludes the most relevant n% of patches from slide X. Note that at step n = 100 all the patches are excluded and therefore, X (100) morf = ∅, where ∅ is an empty set, for which we pass an array of zeros to the model.Comparing heatmaps. We define the quantity of interest for the comparison of different heatmaps as the area under the perturbation curve (AUPC) as the following:where f is the model's function.That is, the lower AUPC, the more faithful the explanation method.We ran all the AttnMIL experiments on an A100 40GB GPU and the TransMIL experiments on a single CPU.", "cite_spans": [{"start": 551, "end": 555, "text": "[74]", "ref_id": "BIBREF73"}], "ref_spans": [], "eq_spans": [], "section": "A.5 Faithfulness evaluation: Patch flipping", "sec_num": null}, {"text": "We present the patch dropping results for AttnMIL in Figure 4 .", "cite_spans": [], "ref_spans": [{"start": 60, "end": 61, "text": "4", "ref_id": null}], "eq_spans": [], "section": "A.6 Faithfulness evaluation: Additional results", "sec_num": null}, {"text": "Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer: [NA] Justification: [NA]", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: All codes are publicly accessible on GitHub. The experiment pipelines are clearly described in Sections 4.1 and 4.2 of the manuscript. Further description of data processing pipelines is given in the main paper in Section 4.2 and in the supplemental material in Section A. 4 . Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.• The name of the license (e.g., CC-BY 4.0) should be included for each asset.• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators. ", "cite_spans": [{"start": 566, "end": 567, "text": "4", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Solving the multiple instance problem with axis-parallel rectangles", "authors": [{"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["H"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Tom<PERSON>", "middle": [], "last": "Lathrop", "suffix": ""}, {"first": "", "middle": [], "last": "Lozano-<PERSON>", "suffix": ""}], "year": 1997, "venue": "Artificial Intelligence", "volume": "89", "issue": "1", "pages": "31--71", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. Solving the multiple instance problem with axis-parallel rectangles. Artificial Intelligence, 89(1):31-71, 1997.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "A framework for multiple-instance learning", "authors": [{"first": "Oded", "middle": [], "last": "Mar<PERSON>", "suffix": ""}, {"first": "Tom<PERSON>", "middle": [], "last": "Lozano-<PERSON>", "suffix": ""}], "year": 1997, "venue": "Advances in Neural Information Processing Systems", "volume": "10", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON>. A framework for multiple-instance learning. Advances in Neural Information Processing Systems, 10, 1997.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Attention-based deep multiple instance learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "Ilse", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Max", "middle": [], "last": "Welling", "suffix": ""}], "year": 2018, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "2127--2136", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Attention-based deep multiple instance learning. In International Conference on Machine Learning, pages 2127-2136. PMLR, 2018.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Transmil: Transformer based correlated multiple instance learning for whole slide image classification", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "Bian", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yifeng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiangyang", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "2136--2147", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Transmil: Transformer based correlated multiple instance learning for whole slide image classification. Advances in Neural Information Processing Systems, 34:2136-2147, 2021.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Dual-stream multiple instance learning network for whole slide image classification with self-supervised contrastive learning", "authors": [{"first": "Bin", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "14318--14328", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Dual-stream multiple instance learning network for whole slide image classification with self-supervised contrastive learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 14318-14328, 2021.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Data-efficient and weakly supervised computational pathology on whole-slide images", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "Drew <PERSON> Williamson", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature Biomedical Engineering", "volume": "5", "issue": "6", "pages": "555--570", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Data-efficient and weakly supervised computational pathology on whole-slide images. Nature Biomedical Engineering, 5(6):555-570, 2021.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Cluster-to-conquer: A framework for end-to-end multi-instance learning for whole slide image classification", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Lubaina", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "Moskaluk", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Medical Imaging with Deep Learning", "volume": "", "issue": "", "pages": "682--698", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Cluster-to-conquer: A framework for end-to-end multi-instance learning for whole slide image classification. In Medical Imaging with Deep Learning, pages 682-698. PMLR, 2021.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Remix: A general and efficient framework for multiple instance learning based whole slide image classification", "authors": [{"first": "Jiaw<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hanbo", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Fan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "Jianhua", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Medical Image Computing and Computer-Assisted Intervention", "volume": "", "issue": "", "pages": "35--45", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Remix: A general and efficient framework for multiple instance learning based whole slide image classification. In International Conference on Medical Image Computing and Computer-Assisted Intervention, pages 35-45. Springer, 2022.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Dtfd-mil: Double-tier feature distillation multiple instance learning for histopathology whole slide image classification", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yan<PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "Coupland", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "18802--18812", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Dtfd-mil: Double-tier feature distillation multiple instance learning for histopathology whole slide image classification. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 18802-18812, 2022.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Structured state space models for multiple instance learning in digital pathology", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Cournède", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Medical Image Computing and Computer-Assisted Intervention", "volume": "", "issue": "", "pages": "594--604", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Structured state space models for multiple instance learning in digital pathology. In International Conference on Medical Image Computing and Computer-Assisted Intervention, pages 594-604. Springer, 2023.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Camil: Context-aware multiple instance learning for cancer detection and subtyping in whole slide images", "authors": [{"first": "<PERSON>", "middle": [], "last": "Fourkioti", "suffix": ""}, {"first": "<PERSON>", "middle": ["De"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bakal", "suffix": ""}], "year": 2024, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Camil: Context-aware multiple instance learning for cancer detection and subtyping in whole slide images. In The Twelfth International Conference on Learning Representations, 2024.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "An aggregation of aggregation methods in computational pathology", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Bilal", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Jewsbury", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Medical Image Analysis", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. An aggregation of aggregation methods in computational pathology. Medical Image Analysis, 2023.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Clinical-grade computational pathology using weakly supervised deep learning on whole slide images", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Geneslaw", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Miraflor", "suffix": ""}, {"first": "Vitor", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>i", "middle": [], "last": "Busam", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Victor", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Klimstra", "suffix": ""}, {"first": "", "middle": [], "last": "Fuchs", "suffix": ""}], "year": 2019, "venue": "Nature Medicine", "volume": "25", "issue": "8", "pages": "1301--1309", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Clinical-grade computational pathology using weakly supervised deep learning on whole slide images. Nature Medicine, 25(8):1301-1309, 2019.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Pan-cancer image-based detection of clinically actionable genetic alterations", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["I"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Jan", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sommer", "suffix": ""}, {"first": "", "middle": [], "last": "Bankhead", "suffix": ""}], "year": 2020, "venue": "Nature Cancer", "volume": "1", "issue": "8", "pages": "789--799", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Pan-cancer image-based detection of clinically actionable genetic alterations. Nature Cancer, 1(8):789-799, 2020.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Deep learning in cancer pathology: a new generation of clinical biomarkers", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Brinker", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON><PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "British Journal of Cancer", "volume": "124", "issue": "4", "pages": "686--696", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Deep learning in cancer pathology: a new generation of clinical biomarkers. British Journal of Cancer, 124(4):686-696, 2021.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "A systematic pan-cancer study on deep learning-based prediction of multi-omic biomarkers from routine pathology images", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Cher", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Debap<PERSON>", "middle": [], "last": "Bass", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Me<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hense", "suffix": ""}, {"first": "Pandu", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Communications Medicine", "volume": "4", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. A systematic pan-cancer study on deep learning-based prediction of multi-omic biomarkers from routine pathology images. Communications Medicine, 4(1):48, 2024.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Predicting survival after hepatocellular carcinoma resection using deep learning on histological slides", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Oumeima", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Zaslavskiy", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Hepatology", "volume": "72", "issue": "6", "pages": "2000--2013", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. Predict- ing survival after hepatocellular carcinoma resection using deep learning on histological slides. Hepatology, 72(6):2000-2013, 2020.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Deep learning for prediction of colorectal cancer outcome: a discovery and validation study", "authors": [{"first": "Ole<PERSON>Johan", "middle": [], "last": "Skrede", "suffix": ""}, {"first": "Sepp", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Maddison", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Nesheim", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "The Lancet", "volume": "395", "issue": "10221", "pages": "350--360", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Deep learning for prediction of colorectal cancer outcome: a discovery and validation study. The Lancet, 395(10221):350-360, 2020.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Unmasking clever hans predictors and assessing what machines really learn", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Binder", "suffix": ""}, {"first": "Grégoire", "middle": [], "last": "Montavon", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Same<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Nature Communications", "volume": "10", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Unmasking clever hans predictors and assessing what machines really learn. Nature Communications, 10(1):1096, 2019.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "The impact of site-specific digital histology signatures on deep learning model accuracy and bias", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "Olufunmilayo I Olopade", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature Communications", "volume": "12", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, et al. The impact of site-specific digital histology signatures on deep learning model accuracy and bias. Nature Communications, 12(1):4423, 2021.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Resolving challenges in deep learning-based analyses of histopathological images using explanation methods", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bockmayr", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Same<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Binder", "suffix": ""}], "year": 2020, "venue": "Scientific Reports", "volume": "10", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Resolving challenges in deep learning-based analyses of histopathological images using explanation methods. Scientific Reports, 10(1):6423, 2020.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Toward explainable artificial intelligence for precision pathology", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bockmayr", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Buchstab", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Grégoire", "middle": [], "last": "Montavon", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Annual Review of Pathology: Mechanisms of Disease", "volume": "19", "issue": "", "pages": "541--570", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Toward explainable artificial intelligence for precision pathology. Annual Review of Pathology: Mechanisms of Disease, 19:541--570, 2024.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Morphological and molecular breast cancer profiling through explainable machine learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "Binder", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bockmayr", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hellweg", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Den<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature Machine Intelligence", "volume": "3", "issue": "", "pages": "355--366", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, and <PERSON>. Morphological and molecular breast cancer profiling through explainable machine learning. Nature Machine Intelligence, 3:355 -366, 2021.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Analysing cerebrospinal fluid with explainable deep learning: From diagnostics to insights", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Schweizer", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Osterloh", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Neuropathology and Applied Neurobiology", "volume": "49", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Analysing cerebrospinal fluid with explainable deep learning: From diagnostics to insights. Neuropathol- ogy and Applied Neurobiology, 49(1):e12866, 2023.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Quantum-chemical insights from deep tensor neural networks", "authors": [{"first": "Farhad", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Nature Communications", "volume": "8", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Quantum-chemical insights from deep tensor neural networks. Nature Communi- cations, 8(1):13890, 2017.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Combining machine learning and computational chemistry for predictive insights into chemical systems", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "V<PERSON>ilev-<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Bingqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Chemical Reviews", "volume": "121", "issue": "16", "pages": "9816--9872", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Combining machine learning and computa- tional chemistry for predictive insights into chemical systems. Chemical Reviews, 121(16):9816- 9872, 2021.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Deep learning-enabled assessment of cardiac allograft rejection from endomyocardial biopsies", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Shady", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Nature Medicine", "volume": "28", "issue": "3", "pages": "575--582", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, et al. Deep learning-enabled assessment of cardiac allograft rejection from endomyocardial biopsies. Nature Medicine, 28(3):575-582, 2022.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Ai-based pathology predicts origins for cancers of unknown primary", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Drew <PERSON> Williamson", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Shady", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature", "volume": "594", "issue": "", "pages": "106--110", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Ai-based pathology predicts origins for cancers of unknown primary. Nature, 594(7861):106-110, 2021.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Transformer-based biomarker prediction from colorectal cancer histology: A large-scale multicentric study", "authors": [{"first": "Sophia", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["P"], "last": "West", "suffix": ""}, {"first": "Jan", "middle": ["<PERSON><PERSON>"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["I"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Cancer Cell", "volume": "41", "issue": "9", "pages": "1650--1661", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Transformer-based biomarker prediction from colorectal cancer histology: A large-scale multicentric study. Cancer Cell, 41(9):1650-1661, 2023.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Attention is not not explanation", "authors": [{"first": "<PERSON>", "middle": [], "last": "Wiegreffe", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP-IJCNLP)", "volume": "", "issue": "", "pages": "11--20", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. Attention is not not explanation. In Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP-IJCNLP), pages 11-20, Hong Kong, China, 2019. Association for Computational Linguistics.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Attention is not Explanation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies", "volume": "", "issue": "", "pages": "3543--3556", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. Attention is not Explanation. In Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguis- tics: Human Language Technologies, pages 3543-3556, Minneapolis, Minnesota, June 2019. Association for Computational Linguistics.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Xai for transformers: Better explanations through conservative propagation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Grégoire", "middle": [], "last": "Montavon", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "435--451", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON> Xai for transformers: Better explanations through conservative propagation. In International Conference on Machine Learning, pages 435-451. PMLR, 2022.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Additive mil: Intrinsically interpretable multiple instance learning for pathology", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>in", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "20689--20702", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>. Additive mil: Intrinsically interpretable multiple instance learning for pathology. Advances in Neural Information Processing Systems, 35:20689-20702, 2022.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Model agnostic interpretability for multiple instance learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "Early", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ram<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Model agnostic interpretability for multiple instance learning. In International Conference on Learning Representations, 2022.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "On pixel-wise explanations for non-linear classifier decisions by layer-wise relevance propagation", "authors": [{"first": "<PERSON>", "middle": [], "last": "Bach", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Binder", "suffix": ""}, {"first": "Grégoire", "middle": [], "last": "Montavon", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Same<PERSON>", "suffix": ""}], "year": null, "venue": "PLoS ONE", "volume": "10", "issue": "7", "pages": "7--2015", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. On pixel-wise explanations for non-linear classifier decisions by layer-wise relevance propagation. PLoS ONE, 10(7), 07 2015.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Layer-wise relevance propagation: an overview. Explainable AI: interpreting, explaining and visualizing deep learning", "authors": [{"first": "Grégoire", "middle": [], "last": "Montavon", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Binder", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Same<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "193--209", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> <PERSON>. Layer-wise relevance propagation: an overview. Explainable AI: interpreting, explaining and visualizing deep learning, pages 193-209, 2019.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Pan-cancer integrative histology-genomic analysis via multimodal deep learning", "authors": [{"first": "<PERSON>", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "Drew <PERSON> Williamson", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Shady", "suffix": ""}, {"first": "Bumjin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Cancer Cell", "volume": "40", "issue": "8", "pages": "865--878", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Pan-cancer integrative histology-genomic analysis via multimodal deep learning. Cancer Cell, 40(8):865-878, 2022.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Deep learning-based phenotyping reclassifies combined hepatocellular-cholangiocarcinoma", "authors": [{"first": "<PERSON>", "middle": [], "last": "Calderaro", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Loetitia", "middle": [], "last": "<PERSON>avre", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Bazille", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Uguen", "suffix": ""}], "year": 2023, "venue": "Nature Communications", "volume": "14", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, et al. Deep learning-based phenotyping reclassifies combined hepatocellular-cholangiocarcinoma. Nature Communications, 14(1):8290, 2023.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Analysis of 3d pathology samples using weakly supervised ai", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Sl"], "last": "Drew <PERSON> Williamson", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Gan", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Cell", "volume": "187", "issue": "10", "pages": "2502--2520", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Analysis of 3d pathology samples using weakly supervised ai. Cell, 187(10):2502-2520, 2024.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Regression-based deep-learning predicts molecular biomarkers from pathology slides", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["I"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Carrero", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "Mara", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Calderaro", "suffix": ""}], "year": 2024, "venue": "Nature Communications", "volume": "15", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, et al. Regression-based deep-learning predicts molecular biomarkers from pathology slides. Nature Communications, 15(1):1253, 2024.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Multiple instance learning: A survey of problem characteristics and applications", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Carbonneau", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Cheplygin<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Pattern Recognition", "volume": "77", "issue": "", "pages": "329--353", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Multiple instance learning: A survey of problem characteristics and applications. Pattern Recognition, 77:329-353, 2018.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "A review of multi-instance learning assumptions", "authors": [{"first": "<PERSON>", "middle": [], "last": "Foulds", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2010, "venue": "The Knowledge Engineering Review", "volume": "25", "issue": "1", "pages": "1--25", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. A review of multi-instance learning assumptions. The Knowledge Engineering Review, 25(1):1-25, 2010.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Transformer-based unsupervised contrastive learning for histopathological image classification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Junzhou", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Han", "suffix": ""}], "year": 2022, "venue": "Medical Image Analysis", "volume": "81", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Transformer-based unsupervised contrastive learning for histopathological image classification. Medical Image Analysis, 81, 2022.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "A foundation model by pathologists for pathologists", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sc<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Dernbach", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Kunft", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Tietz", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2401.04079"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, et al. Rudolfv: A foundation model by pathologists for pathologists. arXiv preprint arXiv:2401.04079, 2024.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "A foundation model for clinical-grade computational pathology and rare cancers detection", "authors": [{"first": "Eugene", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Alican", "middle": [], "last": "Bozkurt", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hall", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Nature Medicine", "volume": "", "issue": "", "pages": "1--12", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, et al. A foundation model for clinical-grade computational pathology and rare cancers detection. Nature Medicine, pages 1-12, 2024.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Towards a generalpurpose foundation model for computational pathology", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "<PERSON>g", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Drew <PERSON> Williamson", "suffix": ""}, {"first": "<PERSON>", "middle": ["H"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Nature Medicine", "volume": "30", "issue": "3", "pages": "850--862", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Towards a general- purpose foundation model for computational pathology. Nature Medicine, 30(3):850-862, 2024.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "From image-level to pixel-level labeling with convolutional networks", "authors": [{"first": "<PERSON>", "middle": ["O"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "2015 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "1713--1721", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. From image-level to pixel-level labeling with con- volutional networks. In 2015 IEEE Conference on Computer Vision and Pattern Recognition (CVPR), pages 1713-1721, 2015.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Deep miml network", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "Proceedings of the Thirty-First AAAI Conference on Artificial Intelligence, AAAI'17", "volume": "", "issue": "", "pages": "1884--1890", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>. Deep miml network. In Proceedings of the Thirty-First AAAI Conference on Artificial Intelligence, AAAI'17, page 1884-1890. AAAI Press, 2017.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Deep multi-instance networks with sparse label assignment for whole mammogram classification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["<PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Medical Image Computing and Computer Assisted Intervention-MICCAI 2017: 20th International Conference", "volume": "20", "issue": "", "pages": "603--611", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep multi-instance networks with sparse label assignment for whole mammogram classification. In Medical Image Computing and Computer Assisted Intervention-MICCAI 2017: 20th International Conference, Quebec City, QC, Canada, September 11-13, 2017, Proceedings, Part III 20, pages 603-611. Springer, 2017.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Explicit document modeling through weighted multiple-instance learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Journal of Artificial Intelligence Research", "volume": "58", "issue": "", "pages": "591--626", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Explicit document modeling through weighted multiple-instance learning. Journal of Artificial Intelligence Research, 58:591-626, 2017.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "<PERSON>el self-attention for weakly-supervised image classification using deep multiple instance learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Borowa", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Tabor", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "2021 IEEE Winter Conference on Applications of Computer Vision (WACV)", "volume": "", "issue": "", "pages": "1720--1729", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. <PERSON><PERSON> self-attention for weakly-supervised image classification using deep multiple instance learning. In 2021 IEEE Winter Conference on Applications of Computer Vision (WACV), pages 1720-1729, 2021.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Protomil: Multiple instance learning with prototypical parts for whole-slide image classification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ka<PERSON><PERSON>ń<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Joint European Conference on Machine Learning and Knowledge Discovery in Databases", "volume": "", "issue": "", "pages": "421--436", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Protomil: Multiple instance learning with prototypical parts for whole-slide image classification. In Joint European Conference on Machine Learning and Knowledge Discovery in Databases, pages 421-436. Springer, 2022.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Promil: Probabilistic multiple instance learning for medical imaging", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Lewicki", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Tabor", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ECAI 2023", "volume": "", "issue": "", "pages": "2210--2217", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Promil: Probabilistic multiple instance learning for medical imaging. In ECAI 2023, pages 2210-2217. IOS Press, 2023.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Improving interpretability for computer-aided diagnosis tools on whole slide imaging with multiple instance learning and gradient-based explanations", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Hippolyte", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Berlemont", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "La<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bloch", "suffix": ""}], "year": 2020, "venue": "Interpretable and Annotation-Efficient Learning for Medical Image Computing: Third International Workshop, iMIMIC 2020", "volume": "3", "issue": "", "pages": "43--53", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Improving interpretability for computer-aided diagnosis tools on whole slide imaging with multiple instance learning and gradient-based explanations. In Interpretable and Annotation- Efficient Learning for Medical Image Computing: Third International Workshop, iMIMIC 2020, Second International Workshop, MIL3ID 2020, and 5th International Workshop, LABELS 2020, Held in Conjunction with MICCAI 2020, Lima, Peru, October 4-8, 2020, Proceedings 3, pages 43-53. Springer, 2020.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Pixel-level explanation of multiple instance learning models in biomedical single cell images", "authors": [{"first": "Ario", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Adonkina", "suffix": ""}, {"first": "Ashkan", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Marr", "suffix": ""}], "year": 2023, "venue": "International Conference on Information Processing in Medical Imaging", "volume": "", "issue": "", "pages": "170--182", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Pixel-level explanation of multiple instance learning models in biomedical single cell images. In International Conference on Information Processing in Medical Imaging, pages 170-182. Springer, 2023.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "A unified approach to interpreting model predictions", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Su-In", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in Neural Information Processing Systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. A unified approach to interpreting model predictions. Advances in Neural Information Processing Systems, 30, 2017.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "The shattered gradients problem: If resnets are the answer, then what is the question?", "authors": [{"first": "<PERSON>", "middle": [], "last": "Balduzzi", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["P"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "ICML", "volume": "70", "issue": "", "pages": "342--350", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, and <PERSON>. The shattered gradients problem: If resnets are the answer, then what is the question? In ICML, volume 70 of Proceedings of Machine Learning Research, pages 342-350. PMLR, 2017.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Methods for interpreting and understanding deep neural networks", "authors": [{"first": "Grégoire", "middle": [], "last": "Montavon", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Same<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Digital Signal Processing", "volume": "73", "issue": "", "pages": "1--15", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Methods for interpreting and understanding deep neural networks. Digital Signal Processing, 73:1-15, 2018.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Explaining and interpreting lstms", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Grégoire", "middle": [], "last": "Montavon", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Sepp", "middle": [], "last": "<PERSON><PERSON>reiter", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Same<PERSON>", "suffix": ""}], "year": 2019, "venue": "Explainable AI: Interpreting, Explaining and Visualizing Deep Learning", "volume": "", "issue": "", "pages": "211--238", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Explaining and interpreting lstms. Explainable AI: Interpreting, Explaining and Visualizing Deep Learning, pages 211-238, 2019.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Building and interpreting deep similarity models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Grégoire", "middle": [], "last": "Montavon", "suffix": ""}], "year": 2020, "venue": "IEEE Transactions on Pattern Analysis and Machine Intelligence", "volume": "44", "issue": "3", "pages": "1149--1161", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Building and interpreting deep similarity models. IEEE Transactions on Pattern Analysis and Machine Intelligence, 44(3):1149-1161, 2020.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Higher-order explanations of graph neural networks via relevant walks", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Shin<PERSON>", "middle": [], "last": "Nakajima", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Grégoire", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Montavon", "suffix": ""}], "year": 2021, "venue": "IEEE Transactions on Pattern Analysis and Machine Intelligence", "volume": "44", "issue": "11", "pages": "7581--7596", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Higher-order explanations of graph neural networks via relevant walks. IEEE Transactions on Pattern Analysis and Machine Intelligence, 44(11):7581- 7596, 2021.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Toward explainable artificial intelligence for regression models: A methodological perspective", "authors": [{"first": "<PERSON>", "middle": [], "last": "Letzgus", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Same<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Grégoire", "middle": [], "last": "Montavon", "suffix": ""}], "year": 2022, "venue": "IEEE Signal Processing Magazine", "volume": "39", "issue": "4", "pages": "40--58", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Toward explainable artificial intelligence for regression models: A method- ological perspective. IEEE Signal Processing Magazine, 39(4):40-58, 2022.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Explaining nonlinear classification decisions with deep taylor decomposition", "authors": [{"first": "Grégoire", "middle": [], "last": "Montavon", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bach", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Binder", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Same<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "Pattern Recognition", "volume": "65", "issue": "", "pages": "211--222", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Explaining nonlinear classification decisions with deep taylor decomposition. Pattern Recognition, 65:211-222, 2017.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "How to explain individual classification decisions", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2010, "venue": "The Journal of Machine Learning Research", "volume": "11", "issue": "", "pages": "1803--1831", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. How to explain individual classification decisions. The Journal of Machine Learning Research, 11:1803-1831, 2010.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Learning important features through propagating activation differences", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Greenside", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Proceedings of the 34th International Conference on Machine Learning", "volume": "70", "issue": "", "pages": "3145--3153", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Learning important features through propagating activation differences. In Proceedings of the 34th International Conference on Machine Learning -Volume 70, ICML'17, page 3145-3153, 2017.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "Axiomatic attribution for deep networks", "authors": [{"first": "Mu<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Yan", "suffix": ""}], "year": 2017, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "3319--3328", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Axiomatic attribution for deep networks. In International Conference on Machine Learning, pages 3319-3328. PMLR, 2017.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "Quantifying attention flow in transformers", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics", "volume": "", "issue": "", "pages": "4190--4197", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Quantifying attention flow in transformers. In <PERSON>, <PERSON>, <PERSON>, and <PERSON>, editors, Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics, pages 4190-4197, Online, July 2020. Association for Computational Linguistics.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "The mnist database of handwritten digit images for machine learning research", "authors": [{"first": "Li", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "IEEE Signal Processing Magazine", "volume": "29", "issue": "6", "pages": "141--142", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. The mnist database of handwritten digit images for machine learning research [best of the web]. IEEE Signal Processing Magazine, 29(6):141-142, 2012.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "Heal: an automated deep learning framework for cancer histopathology image analysis", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Yun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Changyuan", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Se<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["I"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Bioinformatics", "volume": "37", "issue": "22", "pages": "4291--4295", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Heal: an automated deep learning framework for cancer histopathology image analysis. Bioinformatics, 37(22):4291- 4295, 2021.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "Diagnostic assessment of deep learning algorithms for detection of lymph node metastases in women with breast cancer", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Babak", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "Veta", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Nico", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Litjens", "suffix": ""}, {"first": "Awm", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Maschenka", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "JAMA", "volume": "318", "issue": "22", "pages": "2199--2210", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Diagnostic assessment of deep learning algorithms for detection of lymph node metastases in women with breast cancer. JAMA, 318(22):2199-2210, 2017.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "Genomic, pathway network, and immunologic features distinguishing squamous carcinomas", "authors": [{"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hui<PERSON>", "middle": [], "last": "Fan", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Akbani", "suffix": ""}], "year": 2018, "venue": "Cell Reports", "volume": "23", "issue": "1", "pages": "194--212", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Genomic, pathway network, and immunologic features distinguishing squamous carcinomas. Cell Reports, 23(1):194-212, 2018.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "Tp53 mutations in nonsmall cell lung cancer", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2011, "venue": "BioMed Research International", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, et al. Tp53 mutations in nonsmall cell lung cancer. BioMed Research International, 2011, 2011.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "Classification and mutation prediction from non-small cell lung cancer histopathology images using deep learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Santiago"], "last": "Ocampo", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Fenyö", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Razavian", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Nature Medicine", "volume": "24", "issue": "10", "pages": "1559--1567", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Classifica- tion and mutation prediction from non-small cell lung cancer histopathology images using deep learning. Nature Medicine, 24(10):1559-1567, 2018.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "Evaluating the visualization of what a deep neural network has learned", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Same<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Binder", "suffix": ""}, {"first": "Grégoire", "middle": [], "last": "Montavon", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2016, "venue": "IEEE Transactions on Neural Networks and Learning Systems", "volume": "28", "issue": "11", "pages": "2660--2673", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> <PERSON>. Evaluating the visualization of what a deep neural network has learned. IEEE Transactions on Neural Networks and Learning Systems, 28(11):2660-2673, 2016.", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "Decoupling pixel flipping and occlusion strategy for consistent XAI benchmarks", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. Decoupling pixel flipping and occlusion strategy for consistent XAI benchmarks. Transactions on Machine Learning Research, 2024.", "links": null}, "BIBREF75": {"ref_id": "b75", "title": "Attention is all you need", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "Llion", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "Ł Ukasz", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "Illia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in Neural Information Processing Systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. In <PERSON><PERSON>, U. V<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural Information Processing Systems, volume 30. Curran Associates, Inc., 2017.", "links": null}, "BIBREF76": {"ref_id": "b76", "title": "Captum: A unified and generic model interpretability library for pytorch", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Bilal", "middle": [], "last": "Alsallakh", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Natalia", "middle": [], "last": "Kliushkin<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>ya", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Yan", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2009.07896"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, et al. Captum: A unified and generic model interpretability library for pytorch. arXiv preprint arXiv:2009.07896, 2020.", "links": null}, "BIBREF77": {"ref_id": "b77", "title": "Model-agnostic interpretability of machine learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1606.05386"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Model-agnostic interpretability of machine learning. arXiv preprint arXiv:1606.05386, 2016.", "links": null}, "BIBREF78": {"ref_id": "b78", "title": "TorchVision maintainers and contributors. Torchvision: Pytorch's computer vision library", "authors": [], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "TorchVision maintainers and contributors. Torchvision: Pytorch's computer vision library. https://github.com/pytorch/vision, 2016.", "links": null}, "BIBREF79": {"ref_id": "b79", "title": "The cbio cancer genomics portal: an open platform for exploring multidimensional cancer genomics data", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>rusoz", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "Gross", "suffix": ""}, {"first": "Se<PERSON><PERSON>k", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Bülent", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "Cancer Discovery", "volume": "2", "issue": "5", "pages": "401--404", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. The cbio cancer genomics portal: an open platform for exploring multidimensional cancer genomics data. Cancer Discovery, 2(5):401-404, 2012.", "links": null}, "BIBREF80": {"ref_id": "b80", "title": "Integrative analysis of complex cancer genomics and clinical profiles using the cbioportal", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Bülent", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>rusoz", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Gross", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "Science Signaling", "volume": "6", "issue": "269", "pages": "1--1", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Integrative analysis of complex cancer genomics and clinical profiles using the cbioportal. Science Signaling, 6(269):pl1-pl1, 2013.", "links": null}, "BIBREF81": {"ref_id": "b81", "title": "Analysis and visualization of longitudinal genomic and clinical data from the aacr project genie biopharma collaborative in cbioportal", "authors": [{"first": "Ritika", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Mastrogiacomo", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Tran", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Xiang", "middle": [], "last": "Mazor", "suffix": ""}, {"first": "Angelica", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Cancer Research", "volume": "83", "issue": "23", "pages": "3861--3867", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Analysis and visualization of longitudinal genomic and clinical data from the aacr project genie biopharma collaborative in cbioportal. Cancer Research, 83(23):3861-3867, 2023.", "links": null}, "BIBREF82": {"ref_id": "b82", "title": "A threshold selection method from gray-level histograms", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Otsu", "suffix": ""}], "year": 1975, "venue": "Automatica", "volume": "11", "issue": "", "pages": "23--27", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> et al. A threshold selection method from gray-level histograms. Automatica, 11(285-296):23-27, 1975.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "text": "Conference on Neural Information Processing Systems (NeurIPS 2024).", "num": null, "fig_num": null, "uris": null}, "FIGREF1": {"type_str": "figure", "text": "Figure 1: In digital pathology, heatmaps guide the identification of tissue slide areas most important for a model prediction. The figure displays heatmaps from different MIL explanation methods (columns) for a head and neck tumor slide (top row) with a selected zoomed-in region (bottom row).The MIL model has been trained to predict HPV status. The xMIL-LRP heatmap shows that the model identified evidence in favor of an HPV infection at the tumor border (red area) and evidence against an HPV infection inside the tumor (blue area, lower half of the tissue). The dominant blue region explains why the model mispredicted the slide as HPV-negative. Investigation of the tumor border by a pathologist revealed a higher lymphocyte density, which is one of the known recurrent but not always defining visual features of HPV infection in head and neck tumors. xMIL-LRP allows pathologists to extract fine-grained insights about the model strategy. In contrast, the \"attention\" and \"single\" methods neither explain the negative prediction nor distinguish the relevant areas.", "num": null, "fig_num": "1", "uris": null}, "FIGREF3": {"type_str": "figure", "text": "Figure 2: The two steps of xMIL: estimating the aggregation function (A) and the evidence function (B). Panel A shows a block diagram of a MIL model applied to a histopathology slide. The feature extraction module is typically a combination of a frozen foundation model followed by a shallow MLP. In most of the recent MIL models, the aggregation module uses attention mechanisms for combining the instance feature vectors into a single feature representation per bag. The prediction head is a linear layer or an MLP. Panel B schematically shows xMIL-LRP for explaining AttnMIL. In xMIL-LRP, the model output is backpropagated to the input instances. The colored lines represent the relevance flow. Red and blue colors encode the positive and negative values. The attention module is handled via the AH-rule as described in Section 3.2. As discussed in Section 3.3, the instance explanation scores can be computed at the output of the foundation model or at the input level.", "num": null, "fig_num": "2", "uris": null}, "FIGREF4": {"type_str": "figure", "text": "xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG", "num": null, "fig_num": null, "uris": null}, "FIGREF5": {"type_str": "figure", "text": "Figure3: Patch dropping results for TransMIL. The first row depicts the perturbation curves, where the solid lines are the average perturbation curve and the shaded area is the standard error of the mean at each perturbation step. Each boxplot on the second row shows the distribution of AUPC values for all test set slides per explanation methods. In each boxplot, the red line marks the median and the red dot marks the mean. Lower perturbation curves and AUPCs represent higher faithfulness.", "num": null, "fig_num": "3", "uris": null}, "FIGREF6": {"type_str": "figure", "text": "otherwise.• In Adjacent Pairs, 4 supports class 1 and refutes class 0 if 3 is also present, but is irrelevant otherwise. That is, for x k ∼ 4, we set ϵ if 3 is also in in the bag, and ϵ", "num": null, "fig_num": "1", "uris": null}, "FIGREF7": {"type_str": "figure", "text": "xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG LR P G xI S in gl e A tt n R a n d IG", "num": null, "fig_num": null, "uris": null}, "FIGREF8": {"type_str": "figure", "text": "Figure 4: Patch dropping results for AttnMIL. The first row depicts the perturbation curves, where the solid lines are the average perturbation curve and the shaded area is the standard error of the mean at each perturbation step. Each boxplot on the second row shows the distribution of AUPC values for all test set slides per explanation methods. In each boxplot, the red line marks the median and the red dot marks the mean. Lower perturbation curves and AUPCs represent higher faithfulness.", "num": null, "fig_num": "4", "uris": null}, "FIGREF9": {"type_str": "figure", "text": "Figure 5: Exemplary histological features of HPV-negative and -positive HNSC. We display further exemplary heatmaps of TransMIL model predictions in the HNSC HPV dataset in Figures 6, 7, and 8.", "num": null, "fig_num": "56", "uris": null}, "FIGREF10": {"type_str": "figure", "text": "Figure 7: Heatmaps from different explanation methods for a TransMIL model predicting HPV-status. The model correctly predicted the slide HPV-positive (prediction score: 0.9048). For xMIL-LRP, red indicates evidence for and blue against the HPV-positive class.", "num": null, "fig_num": "7", "uris": null}, "FIGREF11": {"type_str": "figure", "text": "Figure 8: Heatmaps from different explanation methods for a TransMIL model predicting HPV-status. The slide is HPV-negative, but the model predicted HPV-positive (prediction score: 0.9997). For xMIL-LRP, red indicates evidence for and blue against the HPV-negative class.", "num": null, "fig_num": "8", "uris": null}, "TABREF1": {"html": null, "type_str": "table", "text": "Results of the faithfulness experiments. AUPC values per dataset, MIL model, and explanation method (mean ± std. over all slides). Lower scores indicate higher faithfulness. The best performance per setting (significant minimum based on the paired t-tests) is highlighted in bold. We also display the model performances (\"Test AUROC\", mean ± std. over 5 repetitions).", "num": null, "content": "<table><tr><td/><td/><td colspan=\"2\">AttnMIL</td><td/><td/><td colspan=\"2\">TransMIL</td></tr><tr><td/><td>CAMELYON16</td><td>NSCLC</td><td colspan=\"3\">HNSC HPV LUAD TP53 CAMELYON16</td><td>NSCLC</td><td>HNSC HPV LUAD TP53</td></tr><tr><td>Test AUROC</td><td>0.93 ± 0.00</td><td colspan=\"2\">0.95 ± 0.00 0.88 ± 0.06</td><td>0.71 ± 0.01</td><td>0.95 ± 0.01</td><td colspan=\"2\">0.96 ± 0.00 0.88 ± 0.05</td><td>0.75 ± 0.01</td></tr><tr><td>Rand</td><td>0.94 ± 0.13</td><td colspan=\"2\">0.98 ± 0.04 0.97 ± 0.07</td><td>0.84 ± 0.14</td><td>0.95 ± 0.11</td><td colspan=\"2\">0.98 ± 0.08 1.00 ± 0.01</td><td>0.94 ± 0.17</td></tr><tr><td>Attn</td><td>0.65 ± 0.46</td><td colspan=\"2\">0.70 ± 0.27 0.94 ± 0.18</td><td>0.65 ± 0.14</td><td>0.63 ± 0.45</td><td colspan=\"2\">0.91 ± 0.22 0.95 ± 0.15</td><td>0.64 ± 0.38</td></tr><tr><td>Single</td><td>0.61 ± 0.43</td><td colspan=\"2\">0.42 ± 0.26 0.78 ± 0.23</td><td>0.34 ± 0.16</td><td>0.42 ± 0.35</td><td colspan=\"2\">0.53 ± 0.26 0.92 ± 0.13</td><td>0.73 ± 0.33</td></tr><tr><td>G×I</td><td>0.92 ± 0.19</td><td colspan=\"2\">0.81 ± 0.35 0.81 ± 0.25</td><td>0.44 ± 0.23</td><td>0.82 ± 0.36</td><td colspan=\"2\">0.79 ± 0.30 0.87 ± 0.20</td><td>0.66 ± 0.40</td></tr><tr><td>IG</td><td>0.62 ± 0.44</td><td colspan=\"2\">0.75 ± 0.38 0.78 ± 0.25</td><td>0.38 ± 0.20</td><td>0.88 ± 0.23</td><td colspan=\"2\">0.99 ± 0.01 1.00 ± 0.00</td><td>0.99 ± 0.01</td></tr><tr><td>xMIL-LRP</td><td>0.51 ± 0.38</td><td colspan=\"2\">0.25 ± 0.22 0.71 ± 0.24</td><td>0.31 ± 0.16</td><td>0.29 ± 0.30</td><td colspan=\"2\">0.45 ± 0.26 0.75 ± 0.23</td><td>0.24 ± 0.28</td></tr></table>"}, "TABREF2": {"html": null, "type_str": "table", "text": "The answer NA means that the abstract and introduction do not include the claims made in the paper.• The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.2. LimitationsQuestion: Does the paper discuss the limitations of the work performed by the authors? Answer: [Yes] Justification: The limitations of the framework and previous works are extensively discussed in the paper, especially in Section 2.2. Additionally, we list further limitations in the discussions.", "num": null, "content": "<table><tr><td>1. Claims</td></tr><tr><td>Question: Do the main claims made in the abstract and introduction accurately reflect the</td></tr><tr><td>paper's contributions and scope?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: The contributions, claims, and the scope of the paper are made clear in the</td></tr><tr><td>abstract and introduction (cf. Section 1).</td></tr><tr><td>Guidelines:</td></tr><tr><td>•</td></tr></table>"}, "TABREF3": {"html": null, "type_str": "table", "text": "Open access to data and code Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? Answer:[No]   Justification: Our code for reproducing our results and implementation of the used methods is made publicly accessible. Datasets used in this work are public datasets properly described and cited in Sections 4.2 and A.4. Guidelines:• The answer NA means that paper does not include experiments requiring code. • Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable). • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted. The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. 8. Experiments Compute Resources Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments? Answer: [Yes] Justification: The compute resources are mentioned in Sections A.3 and A.4. Guidelines: • The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper). 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines? Answer: [Yes]", "num": null, "content": "<table><tr><td>6. Experimental Setting/Details</td></tr><tr><td>Question: Does the paper specify all the training and test details (e.g., data splits, hyper-</td></tr><tr><td>parameters, how they were chosen, type of optimizer, etc.) necessary to understand the</td></tr><tr><td>results?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: All the details in this regard are provided in Sections 4.2 and A.4.</td></tr><tr><td>Guidelines:</td></tr><tr><td>• The answer NA means that the paper does not include experiments.</td></tr><tr><td>• The experimental setting should be presented in the core of the paper to a level of detail</td></tr><tr><td>that is necessary to appreciate the results and make sense of them.</td></tr><tr><td>• The full details can be provided either with the code, in appendix, or as supplemental</td></tr><tr><td>material.</td></tr><tr><td>7. Experiment Statistical Significance</td></tr><tr><td>Question: Does the paper report error bars suitably and correctly defined or other appropriate</td></tr><tr><td>information about the statistical significance of the experiments?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: In our experimental results in Section 4.2 we used paired t-tests with multiple</td></tr><tr><td>comparison corrections for comparing the methods, as well as the standard error of the mean</td></tr><tr><td>to plot the error bars on the perturbation curves of Figures 3 and 4.</td></tr><tr><td>Guidelines:</td></tr></table>"}}}}