{"paper_id": "universal_neural_functional", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:21:29.724849Z"}, "title": "Universal Neural Functionals", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Chelsea", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": "jameshar<PERSON><EMAIL>"}, {"first": "Google", "middle": [], "last": "Deepmind", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "A challenging problem in many modern machine learning tasks is to process weight-space features, i.e., to transform or extract information from the weights and gradients of a neural network. Recent works have developed promising weight-space models that are equivariant to the permutation symmetries of simple feedforward networks. However, they are not applicable to general architectures, since the permutation symmetries of a weight space can be complicated by recurrence or residual connections. This work proposes an algorithm that automatically constructs permutation equivariant models, which we refer to as universal neural functionals (UNFs), for any weight space. Among other applications, we demonstrate how UNFs can be substituted into existing learned optimizer designs, and find promising improvements over prior methods when optimizing small image classifiers and language models. Our results suggest that learned optimizers can benefit from considering the (symmetry) structure of the weight space they optimize. We open-source our library for constructing UNFs at https://github.com/AllanYangZhou/universal_neural_functional.", "pdf_parse": {"paper_id": "universal_neural_functional", "_pdf_hash": "", "abstract": [{"text": "A challenging problem in many modern machine learning tasks is to process weight-space features, i.e., to transform or extract information from the weights and gradients of a neural network. Recent works have developed promising weight-space models that are equivariant to the permutation symmetries of simple feedforward networks. However, they are not applicable to general architectures, since the permutation symmetries of a weight space can be complicated by recurrence or residual connections. This work proposes an algorithm that automatically constructs permutation equivariant models, which we refer to as universal neural functionals (UNFs), for any weight space. Among other applications, we demonstrate how UNFs can be substituted into existing learned optimizer designs, and find promising improvements over prior methods when optimizing small image classifiers and language models. Our results suggest that learned optimizers can benefit from considering the (symmetry) structure of the weight space they optimize. We open-source our library for constructing UNFs at https://github.com/AllanYangZhou/universal_neural_functional.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Many problems in machine learning require handling weight-space features, such as the weights, gradients, or sparsity masks of neural networks. For example, optimizers iteratively map the current weights and gradient history to updated weights. Taking this perspective, researchers have proposed a variety of data-driven methods that train a neural network to process these weight-space features. Examples applications of these neural functionals [<PERSON> et al., 2023a] include training neural networks to predict classifier generalization from weights [<PERSON><PERSON><PERSON><PERSON> et al., 2020] , to optimize other networks [<PERSON> et al., 2022] , and to classify or edit implicit neural representations (INRs) [<PERSON> et al., 2023] .", "cite_spans": [{"start": 447, "end": 467, "text": "[<PERSON> et al., 2023a]", "ref_id": null}, {"start": 551, "end": 575, "text": "[<PERSON><PERSON><PERSON><PERSON> et al., 2020]", "ref_id": "BIBREF10"}, {"start": 605, "end": 624, "text": "[Metz et al., 2022]", "ref_id": "BIBREF28"}, {"start": 690, "end": 713, "text": "[<PERSON> et al., 2023]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Until recently, researchers lacked a unifying and principled framework for designing neural functionals, and would implement a custom model for their particular weight-space task. A significant recent advance was the development of weight-space models that are permutation equivariant [<PERSON><PERSON> et al., 2023 , <PERSON> et al., 2023a] . Neuron permutation symmetries arise in a neural network's weight space because re-ordering hidden neurons has no effect on the network's function [<PERSON><PERSON><PERSON>, 1990] . A permutation equivariant neural functional can guarantee that under a neuron permutation of its input, its output permutes accordingly. <PERSON><PERSON> et al. [2023] showed that permutation equivariance significantly improves performance on weight-space tasks, but their models only apply to the weight spaces of simple feedforward multilayer perceptrons (MLPs). Permutation equivariant neural functionals [<PERSON> et al., 2023a] added the ability to process weights from simple feedforward convolutional networks (CNNs). However, in practice we may deal with the weight spaces of complex networks that have residual connections, recurrence, normalization layers, and so on. Extending existing approaches to each possible weight space would be tedious and challenging.", "cite_spans": [{"start": 285, "end": 304, "text": "[<PERSON><PERSON> et al., 2023", "ref_id": "BIBREF29"}, {"start": 305, "end": 326, "text": ", <PERSON> et al., 2023a]", "ref_id": null}, {"start": 475, "end": 496, "text": "[<PERSON><PERSON><PERSON><PERSON>, 1990]", "ref_id": "BIBREF17"}, {"start": 636, "end": 655, "text": "<PERSON><PERSON> et al. [2023]", "ref_id": "BIBREF29"}, {"start": 896, "end": 916, "text": "[<PERSON> et al., 2023a]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Figure 1 : Illustration of the permutation symmetries in the weight space of a recurrent neural network (Example 2.2). Left: Each layer contains feedforward (ff) weights mapping between different layer's activations, and recurrent (rec) weights transforming activations over time. We can permute the hidden activations as illustrated without changing the final outputs h L t . Right: Permuting the hidden activations induces a permutation on the weights. Here, the rows and columns of the feedforward weights are permuted by (σ ℓ+1 , σ ℓ ), while the recurrent weights are permuted by (σ ℓ , σ ℓ ). Our algorithm automatically constructs permutation equivariant models for any collection of weight tensors given a description of its symmetries (Appendix A).", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "We propose an approach that automatically constructs permutation equivariant models for any collection of tensors whose dimensions can permute according to a shared set of permutations. This naturally encompasses the permutation equivariance we might desire for any given weight space. We show that our algorithm constructs the most general linear layer that operates on a given weight space while guaranteeing equivariance to the specified permutation symmetries. Stacking multiple such layers with pointwise nonlinearities produces a deep permutation equivariant model, which we refer to as a universal neural functional.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "To evaluate the empirical effectiveness of UNFs, we apply them to tasks that require processing networks with complex architectures containing recurrence, layer normalization, residual connections, and more. We use UNFs to implement learned optimizers and then optimize small image classifiers, RNNs, and Transformer language models, observing promising improvements over prior methods. In a generalization prediction task, we use UNF to predict the performance of sequence-to-sequence RNN models from their weights. Our experiments show that universal neural functionals are flexible, can be easily applied to different weight spaces, and improve upon prior weight-space methods.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "We largely follow or extend the notation and naming of <PERSON> et al. [2023a] . Given a fixed neural network architecture, there is a weight space W of possible parameters (weights, biases, normalization scalings, etc.) . We refer to all such parameters as \"weights\". A particular set of weights W = W (1) , • • • , W (L) contains multiple \"tensors\", or multidimensional arrays. Depending on the architecture, W contains numerous symmetries [<PERSON><PERSON><PERSON>, 1990 , <PERSON> et al., 2022] , i.e., transformations on the weight space that do not affect the network's behavior. Following prior work [<PERSON><PERSON> et al., 2023 , <PERSON> et al., 2023a] , this work focuses only on the permutation symmetries, which are called neuron permutations.", "cite_spans": [{"start": 55, "end": 74, "text": "<PERSON> et al. [2023a]", "ref_id": null}, {"start": 169, "end": 216, "text": "(weights, biases, normalization scalings, etc.)", "ref_id": null}, {"start": 438, "end": 458, "text": "[<PERSON><PERSON><PERSON><PERSON>, 1990", "ref_id": "BIBREF17"}, {"start": 459, "end": 482, "text": ", <PERSON> et al., 2022]", "ref_id": "BIBREF13"}, {"start": 591, "end": 610, "text": "[<PERSON><PERSON> et al., 2023", "ref_id": "BIBREF29"}, {"start": 611, "end": 632, "text": ", <PERSON> et al., 2023a]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "Neuron permutations correspond to re-arranging the neurons within (hidden) layers, which have no canonical ordering. We make the simplifying assumption that all layers can be re-arranged-this assumption can be later corrected using positional encodings [<PERSON> et al., 2023a] . Assuming there are N independently permutable layers of neurons, the neuron permutation group is the direct product S = S n1 × • • • × S n N , where n i is the number of neurons being permuted in each layer.", "cite_spans": [{"start": 253, "end": 273, "text": "[<PERSON> et al., 2023a]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "In general, each weight is a \"tensor\" (multi-dimensional array) of real numbers. Using M (a, b,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "• • • ) to denote arrays R a×b×••• , consider a rank-D ℓ tensor W (ℓ) ∈ M n d ℓ 1 , • • • , n d ℓ D ℓ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": ". Each dimension", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "d ℓ i is permuted by σ d ℓ i .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "That is, the action of σ on the indices of the weight tensor is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "σ (i 1 , • • • , i D ℓ ) := σ d ℓ 1 (i 1 ), • • • , σ d ℓ D ℓ (i D ℓ ) .", "eq_num": "(1)"}], "section": "Preliminaries", "sec_num": "2"}, {"text": "Defining the multi-index ⃗ i := (i 1 , • • • , i D ℓ ), the action on the weight tensor is to permute the entries: i) , and the action on W is σW := σW (1) , • • • , σW (L) . We now elaborate on the definition of the group and action in several common cases. Example 2.1 (Multilayer perceptron). A multilayer perceptron (MLP) with L + 1 layers has activations h ℓ+1 = s W (ℓ) h ℓ + b (ℓ+1) , with h 1 being the first (input) layer and h L+1 the output. If each h ℓ is a vector of length n ℓ , then the weights are matrices W (ℓ) ∈ M (n ℓ+1 , n ℓ ) and the biases are vectors b (ℓ) ∈ M (n ℓ ). Then we have a neuron permutation group S = S n1 × • • • × S n L+1 , and σ ∈ S can be written σ = (σ ℓ ) L+1 ℓ=1 . The action on the weights and biases is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "σW (ℓ) ⃗ i := W (ℓ) σ -1 ( ⃗", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "W (ℓ) → P (σ ℓ+1 ) W (ℓ) P (σ ℓ ) ⊤ and b (ℓ) → P (σ ℓ ) b (ℓ) ,", "eq_num": "(2)"}], "section": "Preliminaries", "sec_num": "2"}, {"text": "where P (σ ℓ ) is the n ℓ × n ℓ permutation matrix corresponding to σ ℓ . This corresponds exactly to the \"NP\" setting in <PERSON> et al. [2023a] .", "cite_spans": [{"start": 122, "end": 141, "text": "<PERSON> et al. [2023a]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "Example 2.2 (Recurrent neural network). Consider a deep recurrent neural network (RNN) [<PERSON><PERSON>, 1990] without biases. We follow the presentation of <PERSON> et al. [2023] :", "cite_spans": [{"start": 87, "end": 100, "text": "[<PERSON><PERSON>, 1990]", "ref_id": "BIBREF11"}, {"start": 147, "end": 165, "text": "<PERSON> et al. [2023]", "ref_id": "BIBREF38"}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "h ℓ+1 t = s W ℓ+1 rec h ℓ+1 t-1 + W ℓ ff h ℓ t ,", "eq_num": "(3)"}], "section": "Preliminaries", "sec_num": "2"}, {"text": "where h 1 t are the inputs and h L+1 t are the outputs at each timestep, with h ℓ 0 initialized to 0. The weight space consists of feedforward (ff) weights W ℓ ff ∈ M (n ℓ+1 , n ℓ ) and recurrent (rec) weights W ℓ rec ∈ M (n ℓ , n ℓ ). We again define the neuron permutation group S := S n1 × • • • × S n L+1 , but the action of the group on the weight space is now different. Here, re-arranging the neurons corresponds to transforming the weights:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "W ℓ ff → P (σ ℓ+1 ) W ℓ ff P (σ ℓ ) ⊤ and W ℓ rec → P (σ ℓ ) W ℓ rec P (σ ℓ ) ⊤ .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "As illustrated by Figure 1 , the feedforward weights transform just as in the MLP case (Eq. 2), but the recurrent weights' rows and columns must be transformed by the same permutation.", "cite_spans": [], "ref_spans": [{"start": 25, "end": 26, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "Example 2.3 (Convolutional neural network). Consider a 1D convolutional neural network (CNN) without biases. Using ⋆ to denote cross-correlation, we have activations h ℓ+1 = s W (ℓ) ⋆ h ℓ , where the input is h 1 and the output is h L+1 . If each filter has spatial dimension k ℓ and each h ℓ has n ℓ channels, then we have rank-3 weight tensors W (ℓ) ∈ M (n ℓ+1 , n ℓ , k ℓ ) and neuron permutation group S = L ℓ=1 S n ℓ × S k ℓ . Looking at how each dimension of W (ℓ) permutes, we would have σ n ℓ+1 ∈ S n ℓ+1 permute the first dimension (output channels), σ n ℓ ∈ S n ℓ permute the second dimension (input channels), and σ k ℓ ∈ S k ℓ permute the third dimension (spatial).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "We note that permutating the spatial dimensions of a convolution filter would change the CNN's behavior and is not a true symmetry of the weight space. This is a notable difference between how our framework handles convolutional weight spaces compared to NFNs [<PERSON> et al., 2023a] , where the action of the neuron permutation group does not affect the spatial dimensions at all. Assuming that all dimensions of each weight tensor can permute simplifies the development of our framework, and undesired symmetry can be broken (if desired) by positional encodings of the input [<PERSON> et al., 2023a , <PERSON> et al., 2023] .", "cite_spans": [{"start": 260, "end": 280, "text": "[<PERSON> et al., 2023a]", "ref_id": null}, {"start": 574, "end": 593, "text": "[<PERSON> et al., 2023a", "ref_id": null}, {"start": 594, "end": 613, "text": ", <PERSON> et al., 2023]", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "Equivariance and invariance. We are interested in functions T : W → W that are equivariant, meaning that it doesn't matter whether we apply a neuron permutation to the input or the output. We define L S (W, W) as the space of equivariant linear maps, i.e., those T satisfying:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T (σW ) = σT (W ) , ∀σ ∈ S, W ∈ W. (", "eq_num": "4"}], "section": "Preliminaries", "sec_num": "2"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "Our goal is to design a layer (i.e., a parameterized space of functions) that is equivalent to L S (W, W).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "In some applications, we may instead desire invariance, that is a function P satisfying", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "P (σW ) = P (W ) , ∀σ ∈ S, W ∈ W.", "eq_num": "(5)"}], "section": "Preliminaries", "sec_num": "2"}, {"text": "Following prior work [<PERSON><PERSON> et al., 2023 , <PERSON> et al., 2023a] , we can build invariant neural functionals by composing several equivariant layers with an invariant pooling layer, e.g., one that sums over every dimension of each weight tensor and concatenates the results.", "cite_spans": [{"start": 21, "end": 40, "text": "[<PERSON><PERSON> et al., 2023", "ref_id": "BIBREF29"}, {"start": 41, "end": 62, "text": ", <PERSON> et al., 2023a]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Preliminaries", "sec_num": "2"}, {"text": "Since equivariance is preserved under composition, and pointwise non-linearities are already permutation equivariant, we can build deep equivariant models as long as we have an equivariant linear layer. Additionally, composing equivariant layers with an invariant pooling operation produces a deep invariant model. This section introduces a method for producing equivariant weight-space layers for any given weight space, which enables the flexible construction of universal neural functionals.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Universal neural functionals", "sec_num": "3"}, {"text": "The weight space is a direct sum of individual weight subspaces L) , so the problem of defining an equivariant layer on W can be decomposed into defining equivariant layers between each pair of weight subspaces W (m) and W (ℓ) , for all ℓ and m [<PERSON><PERSON> et al., 2023] .", "cite_spans": [{"start": 245, "end": 265, "text": "[<PERSON><PERSON> et al., 2023]", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "W = W (1) ⊕ • • • ⊕ W (", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "We re-state this result in our own notation. For any ℓ, m pair we define L S W (m) , W (ℓ) as the space of equivariant maps between the two weight subspaces. It contains all T ℓm : W (m) → W (ℓ) satisfying", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T ℓm σW (m) = σT ℓm W (m) ∀σ, W (m) ,", "eq_num": "(6)"}], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "noting that the action on the left and right hand sides of the equivariance condition are not, in general, the same.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "Assume that we already have a basis B sp for L S W (p) , W (s) . A basis function E ∈ B sp can be extended to Ē : W → W by defining:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Ē(W ) ℓ := E W (p) ℓ = s 0 otherwise ,", "eq_num": "(7)"}], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "Ē(W ) := Ē1 (W ), • • • , ĒL (W ) .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "Theorem 3.1 (<PERSON><PERSON> et al. [2023] ). Let { B ℓm } be bases for each L S W (m) , W (ℓ) . Then the union of these bases (extended by Eq. 7) is a basis for linear equivariant maps on W. That is, we have the basis B for L S (W, W) defined:", "cite_spans": [{"start": 12, "end": 32, "text": "(<PERSON><PERSON> et al. [2023]", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "B = ℓ,m∈ L 2 Ē E ∈ B ℓm . (", "eq_num": "8"}], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "This result tells us that we can construct an equivariant basis B for L S (W, W) by simply combining the equivariant bases { B ℓm } for each pair of weight subspaces.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Decomposing equivariant weight-space maps", "sec_num": "3.1"}, {"text": "Algorithm 1 Basis for equivariant W (m) → W (ℓ) layer ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Require: W (m) , W (ℓ) 1: Initialize basis B ℓm ← { } 2: I ← { o 1 , • • • , o D ℓ , i 1 , • • • , i", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "E P (X) c[o1],••• ,c o[D ℓ ] := R X c[i1],••• ,c[i Dm ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "9:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "B ℓm ← B ℓm ∪ { E P } 10: end for 11: return B ℓm Since weights are tensors, our decomposed problem involves finding bases for permutation equivariant maps between tensors. Variants of this problem have been studied by numerous prior works-in particular, <PERSON><PERSON> et al. [2018] theoretically characterize a basis for equivariant maps between arbitrary-rank tensors, and provide a concrete implementation of the basis functions in the rank-2 case. Here, we describe a general algorithm that automatically constructs a basis for permutation equivariant maps between arbitrary-rank tensors. Concretely, it implements each basis function in terms of simple array operations that are amenable to efficient computation with modern deep learning frameworks.", "cite_spans": [{"start": 255, "end": 274, "text": "<PERSON><PERSON> et al. [2018]", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Functions in L S W (m) , W (ℓ) take input tensors indexed by { i 1 , • • • , i Dm } and produces output tensors indexed by { o 1 , • • • , o D ℓ }. We can construct a basis B ℓm for this space where each element is identified by a valid partition P of these indices. Recall that the indices", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "(i 1 , i 2 , • • • ) of W (m) are permuted by σ d m 1 , σ d m 2 , • • • .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "We say that two indices i 1 and i 2 \"permute simultaneously\" if d m 1 = d m 2 . Definition 1. A valid partition is a partition P of the output and input indices", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "I = { o 1 , • • • , o D ℓ , i 1 , • • • , i Dm } into", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "non-empty subsets, such that each subset only contains indices that are permuted simultaneously.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Example 3.1 (W (m) = W (ℓ) = R n1×n2 ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Here the output and input indices are", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "{ o 1 , o 2 , i 1 , i 2 }. The partition { { o 1 , o 2 } , { i 1 , i 2 } } is not valid because o 1 ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "o 2 are permuted by σ 1 , σ 2 , so they do not permute simultaneously. On the other hand,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "{ { o 1 , i 1 } , { o 2 , i 2 } } is a valid partition. Example 3.2 (W (m) = W (ℓ) = R n1×n1 ). This time, the partition { { o 1 , o 2 } , { i 1 , i 2 } } is valid because o 1 , o 2 are both permuted by σ 1 , as are i 1 , i 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "To construct the equivariant basis, we enumerate all valid partitions and then map each partition P to a basis function E P . Concretely, we label each subset of P with a distinct character α, β, γ, • • • and then remap each of our original indices", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "{ o 1 , • • • , o D ℓ , i 1 , • • • , i Dm } to", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "a a character based on which subset the index was in. This mapping is best illustrated by continuing our previous example.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Example 3.3 (W (m) = W (ℓ) = R n1×n2 ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Here input and output are both matrices, with combined indices { o 1 , o 2 , i 1 , i 2 }. We have two permutations (σ 1 , σ 2 ) ∈ S n1 × S n2 that can act on the rows and columns of the input and output matrices. There are four valid partitions:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "P 1 = { { o 1 , i 1 } , { o 2 , i 2 } } , P 2 = { { o 1 , i 1 } , { o 2 } , { i 2 } } , P 3 = { { o 1 } , { i 1 } , { o 2 , i 2 } } , P 4 = { { o 1 } , { o 2 } , { i 1 } , { i 2 } } .", "eq_num": "(9)"}], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Consider P 2 -we assign a character to each subset:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "P 2 = { { o 1 , i 1 } α , { o 2 } β , { i 2 } γ } . (", "eq_num": "10"}], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "which tells us to remap the output indices (o 1 , o 2 ) → (α, β) and the input indices", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "(i 1 , i 2 ) → (α, γ), producing the basis function E P2 W (m) αβ := γ W (m)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "αγ , where summation over γ can be inferred because it only contains an input index.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Repeating this index-remapping process for each valid partition will generate a total of four basis functions E P1 , • • • , E P4 for L S W (m) , W (ℓ) . Our equivariant W (m) → W (ℓ) layer will be defined as the linear combination m) , which is the layer introduced in Hartford et al. [2018] .", "cite_spans": [{"start": 270, "end": 292, "text": "<PERSON> et al. [2018]", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "T ℓm W (m) ; λ := 4 k=1 λ k • E P k W (", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "To generalize the previous example, for each valid partition of the indices P we label its subsets with characters α, β, γ, • • • and then construct a basis function:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E(W (m) ) c[o1],••• ,c[o D ℓ ] = R W (m) c[i1],••• ,c[i Dm ] ,", "eq_num": "(11)"}], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "where c[•] maps each index to the subset of P that contains it. We sum over the characters in R, which is the (possibly empty) subset of characters that only contain input indices (i.e., only appear on the right-hand side). Entries that are not explicitly assigned by the left-hand side are 0. Algorithm 1 gives a formal description of the complete process for generating B ℓm .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Theorem 3.2. Algorithm 1 produces a basis for the equivariant linear maps from W (m) to W (ℓ) .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Proof. See Appendix B.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Once Algorithm 1 has generated a basis of equivariant functions B ℓm , we can implement an equivariant layer using a vector λ ℓm ∈ R |B ℓm | of learned coefficients:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T ℓm W (m) ; λ ℓm := |B ℓm | b=1 λ ℓm b • E P b W (m) . (", "eq_num": "12"}], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers between tensors", "sec_num": "3.2"}, {"text": "Theorem 3.1 now tells us that we may now construct the equivariant weight-space layer by combining the bases { B ℓm } into a basis B of functions on W. The weight-space layer T (•, λ) can then be defined by a linear combination of the basis functions with learned coefficients λ. Explicitly, the full layer is defined:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T (W, λ) = T 1 W, λ 1,: , • • • , T L W, λ L,: ,", "eq_num": "(13)"}], "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "where m) , λ ℓm . Appendix A provides a concrete description of how we specify the weight space in code and how the algorithm is then used to automatically construct an equivariant weight space layer. Our open-source implementation is compatible with most JAX [<PERSON> et al., 2018] neural network libraries. Theorem 3.3. The weight-space layer (Eq.-13) is S-equivariant, and can express any linear equivariant function on W.", "cite_spans": [{"start": 260, "end": 283, "text": "[<PERSON><PERSON> et al., 2018]", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "λ ℓ,: = { λ ℓm | ℓ = 1, • • • , L } and T ℓ W, λ ℓ,: = L m=1 T ℓm W (", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "Proof. Each T ℓm is a linear combination of basis functions in B ℓm . Then, as described by Thm 3.1, Eq. 13 is a linear combination of functions that form a basis for L S (W, W).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "For an MLP weight space with neuron permutation group defined as in Example 2.1, this approach will generate the exact same layer as NFN NP [<PERSON> et al., 2023a] . This is because the layers each parameterize all possible linear maps equivariant to the same symmetry group, and hence can express the same set of functions.", "cite_spans": [{"start": 140, "end": 160, "text": "[<PERSON> et al., 2023a]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Equivariant layers on weight spaces", "sec_num": "3.3"}, {"text": "In practice, we may be interested in simultaneously processing multiple weight-space features, such as the weights and a history of gradients. These features can be stacked into a \"channel\" dimension analogous to the channels of convolutional networks. In that case, we must consider direct sums of weight spaces of the form W c = ⊕ c k=1 W, with elements that can be written as Extending equivariant layers to the multi-channel setting is quite common in the geometric deep learning literature and simply involves taking linear combinations along the channel dimension [<PERSON> and <PERSON>ing, 2016, <PERSON><PERSON> et al., 2017] . That is, we modify the equivariant layer between subspaces as:", "cite_spans": [{"start": 570, "end": 580, "text": "[<PERSON> and", "ref_id": null}, {"start": 581, "end": 621, "text": "<PERSON><PERSON>, 2016, <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2017]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Multiple feature channels", "sec_num": "3.4"}, {"text": "1 W = (W [1], • • • , W [c]), for W [k] ∈ W. Then the action is σW := (σW [1], • • • , σW [c]) for σ ∈ S,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multiple feature channels", "sec_num": "3.4"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "T ℓm W (m) ; λ ℓm [k ′ ] := |B ℓm | b=1 ci k=1 λ ℓm b [k ′ , k] • E P b W (m) [k],", "eq_num": "(14)"}], "section": "Multiple feature channels", "sec_num": "3.4"}, {"text": "where each λ ℓm b is now a learned c o × c i matrix instead of a scalar.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Multiple feature channels", "sec_num": "3.4"}, {"text": "The previous sections describes the construction of S-equivariant layers that operate operate on weight-space features in W c . We construct universal neural functionals by stacking multiple such layers (interleaved with pointwise non-linearities) into a deep, permutation equivariant model that can process weights. To construct a permutation invariant model, we can add an invariant pooling layer after the equivariant layers, as in prior work [<PERSON><PERSON> et al., 2023 , <PERSON> et al., 2023a] .", "cite_spans": [{"start": 446, "end": 465, "text": "[<PERSON><PERSON> et al., 2023", "ref_id": "BIBREF29"}, {"start": 466, "end": 487, "text": ", <PERSON> et al., 2023a]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Deep models", "sec_num": "3.5"}, {"text": "In this section, we refer to weight-space models constructed using our algorithm as universal neural functionals (UNFs). We compare their performance to prior methods on two types of weight-space tasks: predicting the generalization of recurrent sequence-to-sequence models, and training learned optimizers for a variety of architectures and datasets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "One promising application of neural functionals is in predicting the generalization of neural network models from their weights <PERSON><PERSON><PERSON><PERSON> et al. [2020] . We construct Tiny RNN Zoo2 , a dataset of recurrent neural networks trained to do arithmetic by completing given questions character-by-character. For example, given the input string \"15+20=\" the correct completion would be \"35<EOS>\". To construct the dataset, we train 10 4 sequence-to-sequence [<PERSON><PERSON> et al., 2014] models on example problems with input numbers up to five input digits. Both encoder and decoder RNNs contain a single GRU cell [<PERSON> et al., 2014] The success rate of each RNN model is clearly invariant under permutation symmetries of its weights, so invariance is a natural inductive bias for any generalization predictor. We evaluate STATNN [<PERSON><PERSON><PERSON><PERSON> et al., 2020 ] and a UNF-based predictor (note that NFNs are not applicable to the weights of recurrent networks). STATNN is operates on basic statistical features3 of the weights, and has been shown to be a very strong baseline on previous generalization prediction tasks [<PERSON><PERSON><PERSON><PERSON> et al., 2020] . On the other hand, UNF operates on raw weight inputs and may be able to extract more nuanced signals than STATNN, as was shown (for CNN classifiers) in <PERSON> et al. [2023a] .", "cite_spans": [{"start": 128, "end": 151, "text": "<PERSON><PERSON><PERSON><PERSON> et al. [2020]", "ref_id": "BIBREF10"}, {"start": 450, "end": 474, "text": "[<PERSON><PERSON><PERSON> et al., 2014]", "ref_id": "BIBREF34"}, {"start": 602, "end": 622, "text": "[<PERSON> et al., 2014]", "ref_id": "BIBREF6"}, {"start": 819, "end": 844, "text": "[<PERSON><PERSON><PERSON><PERSON> et al., 2020", "ref_id": "BIBREF36"}, {"start": 1105, "end": 1131, "text": "[<PERSON><PERSON><PERSON><PERSON> et al., 2020]", "ref_id": "BIBREF36"}, {"start": 1286, "end": 1305, "text": "<PERSON> et al. [2023a]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "RNN generalization prediction", "sec_num": "4.1"}, {"text": "In particular, STATNN computes the mean, variance, and (0, 25, 50, 75, 100)-percentiles of each weight tensor in the RNN and feeds them into a six-layer MLP with hidden width 600. UNF is a permutation invariant model, implemented using a three-layer equivariant backbone (16 hidden channels) followed by invariant pooling and a three-layer MLP (512 hidden neurons). We train each predictor with binary cross entropy loss (since the target SR is in [0, 1]), using the Adam optimizer with learning rate 0.001, batch size 10, and training for up to 10 epochs. We use the validation data only for early stopping, and assess the performance of each predictor on the test inputs using <PERSON>'s τ , the rank correlation between predicted and actual success rate.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RNN generalization prediction", "sec_num": "4.1"}, {"text": "Results. Table 1 shows the performance of each predictor on held out weight inputs. Our UNF-based predictor achieves significantly higher rank correlation between predicted and actual success rate, suggesting that the equivariant layers are able to extract more informative features from the raw weights compared to STATNN.", "cite_spans": [], "ref_spans": [{"start": 15, "end": 16, "text": "1", "ref_id": "TABREF1"}], "eq_spans": [], "section": "RNN generalization prediction", "sec_num": "4.1"}, {"text": "Choosing the optimizer is a key step in training any modern neural network. Though most popular optimizers are variants of stochastic descent, the non-convexity of neural network training leaves few rigorous guidelines for ideal optimizer design. This has led some researchers to propose training good optimizers using some form of meta-learning [<PERSON><PERSON> et al., 1990 , 2013 , <PERSON><PERSON><PERSON><PERSON> et al., 2016 , <PERSON><PERSON><PERSON><PERSON> et al., 2017 , <PERSON> et al., 2019] .", "cite_spans": [{"start": 346, "end": 366, "text": "[<PERSON><PERSON> et al., 1990", "ref_id": "BIBREF3"}, {"start": 367, "end": 373, "text": ", 2013", "ref_id": null}, {"start": 374, "end": 401, "text": ", <PERSON><PERSON><PERSON><PERSON> et al., 2016", "ref_id": "BIBREF1"}, {"start": 402, "end": 427, "text": ", <PERSON><PERSON><PERSON><PERSON> et al., 2017", "ref_id": "BIBREF39"}, {"start": 428, "end": 448, "text": ", <PERSON> et al., 2019]", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "Common optimizers today (including the learned ones) are equivariant to any permutation of the weights. This is because permuting the weights also permutes the gradients, so stochastic gradient descent and similar optimizers will produce permuted updates. However, equivariance to any permutation ignores the actual symmetry structure of the optimized neural network. Arguably the more appropriate constraint is to only require equivariance to the neuron permutation group, which enables more expressive optimizers while still respecting the symmetries of the weight space. As we will see, this can be achieved by using UNFs to implement a learned optimizer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "Training learned optimizers that generalize well is extremely compute-intensive [<PERSON> et al., 2022] , so we conduct our experiments in several smaller settings to analyze the impact of architecture Figure 2 : Training loss (negative log-likelihood) curves for different tasks and architectures using meta-learned optimizers. We implement learned optimizers with either universal neural functionals (UNFs), NFNs [<PERSON> et al., 2023a] , or Deep Sets [<PERSON><PERSON><PERSON> et al., 2017] . Deep Sets are the current standard choice for implementing learned optimizers. Note that NFN is identical to UNF in the MLP case, different for CNN case, and not applicable to RNNs or Transformers. All loss curves are smoothed and averaged over 5 random initializations (3 for Transformer), with shaded regions showing standard error. choice on learned optimizer performance. In each setting, an optimizer is meta-trained to optimize an architecture type on a task from random initializations. Following <PERSON> et al. [2022] , our learned optimizers track momentum terms m γ t ← γm t-1 + ∇ t and produce updates of the form:", "cite_spans": [{"start": 80, "end": 99, "text": "[Metz et al., 2022]", "ref_id": "BIBREF28"}, {"start": 411, "end": 431, "text": "[<PERSON> et al., 2023a]", "ref_id": null}, {"start": 447, "end": 468, "text": "[<PERSON><PERSON><PERSON> et al., 2017]", "ref_id": "BIBREF41"}, {"start": 975, "end": 997, "text": "<PERSON> et al. [2022]", "ref_id": "BIBREF15"}], "ref_spans": [{"start": 205, "end": 206, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "W t+1 ← W t -α (m γ0 t + βf (W t , ∇ t , { m γi t } i , t)) .", "eq_num": "(15)"}], "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "Here αm γ0 t is a \"nominal term\" that biases the learned optimizer to behave like stochastic gradient descent with momentum coefficient γ 0 . The neural functional f (•) ingests weights W t , gradients ∇ t , momentum terms at several coefficients { m γi t } i , and the iteration t. During meta-training, we optimize network f and scalars α, β, γ 0 to minimize the task training loss after a fixed number of training steps T , the \"inner training horizion.\" To avoid the issue of backpropagating through an optimization process, we estimate meta-gradients using persistent evolutionary strategies [<PERSON><PERSON> et al., 2021] .", "cite_spans": [{"start": 597, "end": 617, "text": "[<PERSON><PERSON> et al., 2021]", "ref_id": "BIBREF37"}], "ref_spans": [], "eq_spans": [], "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "Comparisons. The default architecture choice for f (•) in prior work is Deep Sets [<PERSON><PERSON><PERSON> et al., 2017] , which offers equivariance to any permutation symmetry. We study the effect of replacing Deep Sets by UNFs. We also try the NFN NP architecture [<PERSON> et al., 2023a] where applicable, though it cannot be used on the RNN and Transformer experiments. Finally, we consider stochastic gradient descent with momentum (SGDM), which is equivalent to fixing β = 0 in Eq. 15. The SGDM baseline is also meta-trained to tune the learning rate α and momentum decay rate γ 0 . We compare the different learned optimizers in four tasks: MLP on FashionMNIST. Each optimizer trains an MLP classifier on a downsized and flattened version of the FashionMNIST dataset [<PERSON> et al., 2017] . We note that for MLP weight spaces, UNF are identical to NFN NP [<PERSON> et al., 2023a] .", "cite_spans": [{"start": 82, "end": 103, "text": "[<PERSON><PERSON><PERSON> et al., 2017]", "ref_id": "BIBREF41"}, {"start": 249, "end": 269, "text": "[<PERSON> et al., 2023a]", "ref_id": null}, {"start": 753, "end": 772, "text": "[<PERSON> et al., 2017]", "ref_id": "BIBREF40"}, {"start": 839, "end": 859, "text": "[<PERSON> et al., 2023a]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Learned optimizers", "sec_num": "4.2"}, {"text": "CNN on CIFAR-10. Each optimizer trains a convolutional classifier on a downsized 16 × 16 CIFAR-10. In this setting our algorithm produces a UNF that is different to NFN NP (see Example 2.3).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Learned optimizers", "sec_num": "4.2"}, {"text": ". Each optimizer trains a character-level RNN-based language model (LM) on the One Billion Word Language Model Benchmark (LM1B) dataset [<PERSON><PERSON><PERSON> et al., 2013] .", "cite_spans": [{"start": 136, "end": 157, "text": "[<PERSON><PERSON><PERSON> et al., 2013]", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "RNN on LM1B", "sec_num": null}, {"text": "Transformer on LM1B. Each optimizer trains a Transformer LM on LM1B, this time predicting tokens instead of characters.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RNN on LM1B", "sec_num": null}, {"text": "We use an inner training horizon T = 2,000 for the first three tasks and T = 5,000 for the Transformer task, since it takes longer to train. When implementing f (•) for each method, we use a network with four layers, 32 hidden channels, and ReLU nonlinearities. The Deep Set optimizer uses exclusively Deep Set layers [<PERSON><PERSON><PERSON> et al., 2017, Eq. 4] , while the UNF and NFN optimizers uses three Deep Set layers followed by a single UNF or NFN layer. See Appendix C.1-C.2 for full descriptions of the tasks and meta-training.", "cite_spans": [{"start": 318, "end": 346, "text": "[<PERSON><PERSON><PERSON> et al., 2017, Eq. 4]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "RNN on LM1B", "sec_num": null}, {"text": "Results. Figure 2 shows the training curves produced by each of the meta-trained optimizers in each experiment. Learned optimizers with deep architectures (UNF, Deep Set, or NFN) outperform SGDM, even after tuning SGDM's learning rate and momentum decay. UNF typically learns fastest and achieves the lowest training loss across all methods, though Deep Set and NFN can be comparable in some settings. One interesting observation is that UNF outperforms NFN in the CNN experiment. As noted in Example 2.3, UNFs make the stronger assumption that all tensor dimensions-including the spatial dimensions of the convolution filter-are permutable, while NFNs do not. Although the UNF assumption is technically incorrect, the stronger assumption leads to a lower parameter count (see Table 3 in the appendix) which may be easier for meta-optimization.", "cite_spans": [], "ref_spans": [{"start": 16, "end": 17, "text": "2", "ref_id": null}, {"start": 783, "end": 784, "text": "3", "ref_id": null}], "eq_spans": [], "section": "RNN on LM1B", "sec_num": null}, {"text": "Overall, our results show the promise of using UNFs to create more expressive learned optimizers that utilize the specific symmetry structure of the weight spaces they optimize. Further work could investigate their capacity for generalization to new tasks and architectures, for example by metatraining on diverse tasks [Metz et al., 2022] . Moreover, as Table 3 in the appendix shows, a necessary trade-off of UNFs being more expressive is that they require more parameters for an equivalent number of layers and hidden channels. Since learned optimizers are still much smaller than the networks they could optimize, this may not be a significant computational constraint in practice. Still, it could be a challenge to meta-optimization, since evolutionary strategies are known to struggle in higher dimensions. Hence, further work on efficient high-dimensional meta-gradient estimators would complement the development of expressive weight-space models like UNF.", "cite_spans": [{"start": 320, "end": 339, "text": "[Metz et al., 2022]", "ref_id": "BIBREF28"}], "ref_spans": [{"start": 361, "end": 362, "text": "3", "ref_id": null}], "eq_spans": [], "section": "RNN on LM1B", "sec_num": null}, {"text": "There is a long history of neural network architectures that are equivariant to various symmetry groups [<PERSON><PERSON><PERSON> et al., 1995 , <PERSON> and <PERSON>, 2016 , <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2017 , Kondor and <PERSON><PERSON><PERSON>, 2018 , <PERSON> et al., 2018] . Existing frameworks for automatically constructing equivariant models [<PERSON><PERSON> et al., 2021] produce equivariant matrices, which would be intractable for our task. Our work constructs efficient equivariant basis functions for a particular class of permutation symmetries that arise in the weight spaces of neural networks. Permutation equivariant networks have been developed for sets [<PERSON><PERSON><PERSON> et al., 2017] , matrices whose rows and columns permute independently [<PERSON> et al., 2018] , and tensors under higher-order permutation actions [<PERSON><PERSON><PERSON> et al., 2020, Pan and Kondor, 2022] -the latter may also be viewed as equivariant models on graphs or polytopes [<PERSON><PERSON> et al., 2018 , <PERSON><PERSON> et al., 2019] . This work observes that a weight space is a collection of tensors under higher-order permutation symmetries, and develops equivariant models for that setting.", "cite_spans": [{"start": 104, "end": 123, "text": "[<PERSON><PERSON><PERSON> et al., 1995", "ref_id": "BIBREF24"}, {"start": 124, "end": 149, "text": ", Cohen and Welling, 2016", "ref_id": "BIBREF7"}, {"start": 150, "end": 176, "text": ", <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2017", "ref_id": null}, {"start": 177, "end": 203, "text": ", Kondor and Trivedi, 2018", "ref_id": "BIBREF21"}, {"start": 204, "end": 225, "text": ", <PERSON> et al., 2018]", "ref_id": "BIBREF8"}, {"start": 298, "end": 318, "text": "[<PERSON><PERSON> et al., 2021]", "ref_id": "BIBREF12"}, {"start": 611, "end": 632, "text": "[<PERSON><PERSON><PERSON> et al., 2017]", "ref_id": "BIBREF41"}, {"start": 689, "end": 712, "text": "[Hartford et al., 2018]", "ref_id": "BIBREF16"}, {"start": 766, "end": 795, "text": "[<PERSON><PERSON><PERSON> et al., 2020, Pan and", "ref_id": null}, {"start": 796, "end": 809, "text": "Kondor, 2022]", "ref_id": "BIBREF30"}, {"start": 886, "end": 905, "text": "[<PERSON><PERSON> et al., 2018", "ref_id": "BIBREF26"}, {"start": 906, "end": 930, "text": ", <PERSON><PERSON><PERSON> et al., 2019]", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "5"}, {"text": "There has been significant interest in designing architectures that that either optimize or generate neural network weights [<PERSON><PERSON><PERSON><PERSON><PERSON>, 1993 , <PERSON> et al., 2016 , <PERSON><PERSON><PERSON> et al., 2017 , <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, 2021 , <PERSON><PERSON><PERSON> et al., 2022 , <PERSON> et al., 2022] . Some works have identified the importance of respecting the relevant symmetries when implementing black box meta-learners [<PERSON><PERSON> et al., 2022] . However, precise characterizations of equivariant models on neural weight spaces are relatively recent and were initially restricted to simple feedforward models [<PERSON><PERSON> et al., 2023 , <PERSON> et al., 2023a,b] .", "cite_spans": [{"start": 124, "end": 142, "text": "[<PERSON><PERSON><PERSON><PERSON><PERSON>, 1993", "ref_id": "BIBREF33"}, {"start": 143, "end": 160, "text": ", <PERSON> et al., 2016", "ref_id": null}, {"start": 161, "end": 183, "text": ", <PERSON><PERSON><PERSON> et al., 2017", "ref_id": null}, {"start": 184, "end": 214, "text": ", Kirsch and Sc<PERSON>id<PERSON>ber, 2021", "ref_id": "BIBREF18"}, {"start": 215, "end": 237, "text": ", <PERSON><PERSON><PERSON> et al., 2022", "ref_id": "BIBREF31"}, {"start": 238, "end": 258, "text": ", Metz et al., 2022]", "ref_id": null}, {"start": 383, "end": 404, "text": "[<PERSON><PERSON> et al., 2022]", "ref_id": "BIBREF19"}, {"start": 569, "end": 588, "text": "[<PERSON><PERSON> et al., 2023", "ref_id": "BIBREF29"}, {"start": 589, "end": 612, "text": ", <PERSON> et al., 2023a,b]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "5"}, {"text": "A recent alternative approach has been to leverage message passing neural networks (MPNNs) [<PERSON> et al., 2023] to process weights as edges of a graph. Concurrent to this work, <PERSON><PERSON><PERSON> et al. [2024] demonstrated applications of MPNNs to learned optimization for MLPs and CNNs and <PERSON> et al. [2023] extended MPNNs to process general weight-spaces. MPNN-based approaches benefit from more flexible adaptation to heterogenous inputs, and the computational cost of message passing does not grow as rapidly as our basis-this is because our approach guarantees each linear layer to be maximally expressive while MPNNs do not. We give a more detailed exposition of this trade-off in Appendix B.3", "cite_spans": [{"start": 91, "end": 111, "text": "[<PERSON> et al., 2023]", "ref_id": "BIBREF42"}, {"start": 177, "end": 198, "text": "<PERSON><PERSON><PERSON> et al. [2024]", "ref_id": null}, {"start": 280, "end": 297, "text": "<PERSON> et al. [2023]", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "5"}, {"text": "We introduce a method for constructing permutation-equivariant neural functionals that operate on arbitrary weight spaces, removing a major limitation of previous frameworks that were only applicable to the weight spaces of simple MLPs and CNNs. Our algorithm constructs maximally expressive equivariant linear layers for processing any collection of tensors given a description of their permutation symmetries, and implements these layers in terms of efficient array operations in standard deep learning frameworks. We empirically validate that the resulting universal neural functionals (UNFs) are effective at tasks that involve processing the weights and gradients of convolutional image classifiers, recurrent sequence-to-sequence models, and Transformer language models. In particular, we find that UNFs show promising improvements over existing learned optimizer designs in small scale experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Limitations and future work. It remains to be demonstrated how UNFs can be applied to heterogenous weight-space inputs, e.g., to have a single UNF act as a learned optimizer for any input architecture. Moreover, our experimental results only validate the promise of UNF-based learned optimizers in relatively limited settings, and more work would needed to test generalization across arbitrary tasks. Finally, computational tractability may be a significant challenge for more complex architectures as the number of basis terms generated by Alg. 1 would grow rapidly for higher rank tensors with higher-order interactions (see Appendix B.2). Resolving these challenges would further improve the scalability and applicability of neural functionals to weight-space tasks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Here we discuss the concrete specification that precisely describes a weight space and must be provided as input to the algorithm before it can construct equivariant weight-space layers. Our implementation is compatible with most JAX [<PERSON> et al., 2018] neural network libraries.", "cite_spans": [{"start": 234, "end": 257, "text": "[<PERSON><PERSON> et al., 2018]", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "A Weight-space specifications", "sec_num": null}, {"text": "Suppose we wish to process an MLP's weights that are stored in a (nested) Python dictionary: params = { \"layer1\": {\"weight\": Array [64, 32] , \"bias\": <PERSON>rray[64]}, \"layer2\": {\"weight\": Array [64, 64] , \"bias\": <PERSON>rray[64]}, } Then a specification should match the nested dictionary structure but provide a string or integer name for each dimension of each array. The name tells the algorithm which permutation affects which dimensions of each array.", "cite_spans": [{"start": 131, "end": 135, "text": "[64,", "ref_id": null}, {"start": 136, "end": 139, "text": "32]", "ref_id": null}, {"start": 189, "end": 193, "text": "[64,", "ref_id": null}, {"start": 194, "end": 197, "text": "64]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "A Weight-space specifications", "sec_num": null}, {"text": "In this example, the specification closely follows the MLP description in Example 2.1, where W (1) ∈ M (n 2 , n 1 ) is permuted as W (1) → P (σ 2 ) W (1) P (σ 1 )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Weight-space specifications", "sec_num": null}, {"text": "⊤ . specification = { \"layer1\": {\"weight\": (\"n2\", \"n1\"), \"bias\": (\"n2\",)}, \"layer2\": {\"weight\": (\"n3\", \"n2\"), \"bias\": (\"n3\",)}, } Providing this specification object to our algorithm is sufficient for it to deduce the symmetry group, its action, and construct the corresponding equivariant layer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Weight-space specifications", "sec_num": null}, {"text": "Since most neural networks consist of repeating layers or blocks, the process of constructing the specification can be semi-automated by first defining a function that creates the specification for a single layer or block and then re-using that function for each block. Although we did not find this necessary for our experiments, it may also be possible to automatically deduce the specifications for a network in common deep learning frameworks by analyzing its computation graph.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Weight-space specifications", "sec_num": null}, {"text": "B.1 Algorithm 1 generates a basis for L S W (m) , W (ℓ) Here we show that Algorithm 1 produces a basis B ℓm for L S W (m) , W (ℓ) , the space of linear equivariant maps between W (m) and W (ℓ) . Consider instantiating these linear maps as matrices multiplying flattened input vec W (m) . <PERSON><PERSON> et al. [2018] characterize a basis { B µ } µ for these matrices, where the entries of each basis matrix are defined:", "cite_spans": [{"start": 288, "end": 307, "text": "<PERSON><PERSON> et al. [2018]", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "B Further analysis of UNFs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "B µ a,b = 1 (a, b) ∈ µ 0 otherwise . (", "eq_num": "16"}], "section": "B Further analysis of UNFs", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Further analysis of UNFs", "sec_num": null}, {"text": "Here a ∈ I m and b ∈ I ℓ are multi-indexes for the input and output spaces, and µ ∈ I m × I ℓ / ∼ is an equivalence class of the combined input-output index space I m × I ℓ under the equivalence relation ∼ defined by a ∼ a ′ if and only if a i = a j ⇐⇒ a ′ i = a ′ j for all i, j, i.e. the two multi-indexes a, a ′ have the same equality pattern. <PERSON><PERSON> et al. [2018, Eq. 10b] , any equivariant linear map is defined:", "cite_spans": [{"start": 347, "end": 375, "text": "<PERSON><PERSON> et al. [2018, Eq. 10b]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "B Further analysis of UNFs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L(W (m) ) b = a∈Im µ∈Im×I ℓ /∼ w µ B µ a,b W (m) a = µ∈Im×I ℓ /∼ w µ a∈Im I{(a, b) ∈ µ}W (m) a ,", "eq_num": "(17)"}], "section": "Re-arranging", "sec_num": null}, {"text": "where I{•} is an indicator function for the given condition.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Re-arranging", "sec_num": null}, {"text": "Notice that each equivalence class µ is represented by what we call a valid partition of", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Re-arranging", "sec_num": null}, {"text": "[D m + D ℓ ] := {1, • • • , D m + D ℓ },", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Re-arranging", "sec_num": null}, {"text": "so this is already a sum over valid partitions as in Eq. 12. We can now observe that each term on the RHS is equivalent to one of our basis functions (Alg 1 Line 8). That is, for a given equivalence class µ represented by valid partition P:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Re-arranging", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "a I{(a, b) ∈ µ}W (m) a = E P (W (m) ). (", "eq_num": "18"}], "section": "Re-arranging", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Re-arranging", "sec_num": null}, {"text": "This is because for any I := (a, b) yielding a nonzero term on the LHS, if i, j ∈ [D m + D ℓ ] are grouped together by partition P then I i = I j , otherwise they would violate the equality pattern of µ. Therefore, we can replace all indices grouped together in a partition with a single shared symbol, i.e. the characters in Eq. 11.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Re-arranging", "sec_num": null}, {"text": "Hence, Algorithm 1 produces a basis that spans the same space of equivariant functions defined in <PERSON><PERSON> et al. [2018] , but constructs the basis functions in terms of efficient array operations instead of as matrices. Note that this is similar to the construction in Pan and Kondor [2022] , but generalized to multi-node sets (non-square tensors whose axes can potentially permute independently).", "cite_spans": [{"start": 98, "end": 117, "text": "<PERSON><PERSON> et al. [2018]", "ref_id": "BIBREF26"}, {"start": 267, "end": 288, "text": "Pan and Kondor [2022]", "ref_id": "BIBREF30"}], "ref_spans": [], "eq_spans": [], "section": "Re-arranging", "sec_num": null}, {"text": "Suppose we have a neuron permutation symmetry group ℓ) to be the number of indices that σ i ∈ S ni permutes in weight tensors W (ℓ) ∈ W (ℓ) (which could be 0). Finally, denote b(k) to be the k'th Bell number. Then the number of basis functions generated by Algorithm 1 is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 Size of basis produced by Algorithm 1", "sec_num": null}, {"text": "S = S n1 × • • • × S n N , i.e., every neuron permutation σ is composed of N distinct permutations (σ 1 , • • • , σ N ). For each i = 1, • • • , N we define c i W (", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 Size of basis produced by Algorithm 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "|B ℓm | = ℓ,m N i=1 b c i W (ℓ) + c i W (m) .", "eq_num": "(19)"}], "section": "B.2 Size of basis produced by Algorithm 1", "sec_num": null}, {"text": "Each UNF layer can express any linear equivariant function on a given weight space (Thm 3.3).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "Compared to methods based on message-passing neural networks (MPNNs), this means UNFs can have very expressive individual layers, but may also be more computationally challenging due to the growth in the size of the basis (see next section).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "As an example, consider a simple \"RNN\" where h t+1 = W h t and h t ∈ R n has exchangeable entries, meaning that W → P W P T is a symmetry. Algorithm 1 would produce an equivariant basis with b(2 + 2) = 15 terms4 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "On the other hand, we could construct a parameter graph [<PERSON> et al., 2023] with n nodes and 2n 2 directed edges between them (allowing a forward and backward edge for each weight, equivalently n 2 undirected edges). Then using a similar construction to <PERSON> et al. [2023, Appendix C.1.2] , we would get a linear GNN that computes:", "cite_spans": [{"start": 56, "end": 74, "text": "[<PERSON> et al., 2023]", "ref_id": "BIBREF25"}, {"start": 253, "end": 286, "text": "<PERSON> et al. [2023, Appendix C.1.2]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "f (W ) = aW ⋆,⋆ + bW j,⋆ + cW ⋆,k + dW k,⋆ + eW ⋆,j + f W jk ,", "eq_num": "(20)"}], "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "which is a linear combination of 6 equivariant basis functions, instead of 15. This leads to a potientially interesting trade-off between expressivity vs tractability. However, we also note that in practice MPNNs use non-linear MLPs in their message passing updates, and the comparison between UNF and MPNN-style approaches remains an open empirical question.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Comparison to MPNN-based approaches", "sec_num": null}, {"text": "Here we describe each of the experimental settings we evaluated the learned optimizers on. Across all experiments, the training loss is negative log-likelihood. [<PERSON> et al., 2017] . The MLP has a hidden size of 32 and ReLU activation function. We use a batch size of 128.", "cite_spans": [{"start": 161, "end": 180, "text": "[<PERSON> et al., 2017]", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "C Experimental details C.1 Learned optimization tasks", "sec_num": null}, {"text": "CNN on CIFAR-10. Train a convolutional classifier on a downsized 16 × 16 CIFAR-10. The classifier has two convolutional layers (16 and 32 channels), followed by global average pooling and a linear classification head, and is trained with a batch size of 128.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Experimental details C.1 Learned optimization tasks", "sec_num": null}, {"text": "Trains a character-level RNN-based language model (LM) on LM1B [<PERSON><PERSON><PERSON> et al., 2013] . The RNN itself has one hidden layer with size 64, and uses identity-initialization [<PERSON> et al., 2015 ]. An embedding layer with dimension 32 maps tokens to embeddings before feeding into the RNN, and an output layer produces token predictions from the RNN output. The LM is trained to predict the next token with teacher forcing at batch size 64, on sequences of length 16.", "cite_spans": [{"start": 63, "end": 84, "text": "[<PERSON><PERSON><PERSON> et al., 2013]", "ref_id": "BIBREF5"}, {"start": 170, "end": 186, "text": "[<PERSON> et al., 2015", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "RNN on LM1B.", "sec_num": null}, {"text": "Transformer on LM1B. Train a Transformer LM on LM1B, this time predicting tokens instead of characters. The Transformer has two blocks with an embedding dimension of 32, and uses four self-attention heads. We train with a batch size of 8 on length-8 sequences.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RNN on LM1B.", "sec_num": null}, {"text": "Call DS[c] a single equivariant Deep Set layer [<PERSON><PERSON><PERSON> et al., 2017, Eq 4 ] with c output channels (similarly for UNF[c] and NFN[c]). Then f (•) in our learned optimizers (Eq. 15) is always implemented as a feedforward architecture:", "cite_spans": [{"start": 47, "end": 73, "text": "[<PERSON><PERSON><PERSON> et al., 2017, Eq 4", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "C.2 Learned optimization meta-training", "sec_num": null}, {"text": "DeepSetOpt = DS[32] -> ReLU -> DS[32] -> ReLU -> DS[32] -> ReLU -> DS[1] UNFOpt = DS[32] -> ReLU -> DS[32] -> ReLU -> DS[32] -> ReLU -> UNF[1] NFNOpt = DS[32] -> ReLU -> DS[32] -> ReLU -> DS[32] -> ReLU -> NFN[1]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Learned optimization meta-training", "sec_num": null}, {"text": "For all methods, we initialize α = 0.1 and γ 0 = 0.9 before starting meta-training. For non-SGDM methods, we initialize β = 0.001, and provide six momentum values { m γi t } i with coefficients γ i = 0.1, 0.5, 0.9, 0.99, 0.999, 0.9999. The iteration number t is converted into an 11-dimensional sinusoidal encoding, and all inputs to f (•) are concatenated along the channel dimension. Concretely, this results in an input in W 19 . The output is in W 1 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Learned optimization meta-training", "sec_num": null}, {"text": "We meta-train for 50,000 steps using <PERSON>, estimating meta-gradients over 16 parallel training runs using persistent evolutionary strategies (PES) [<PERSON><PERSON> et al., 2021] with a truncation length of 50 and a noise standard deviation of 0.01. The meta-training objective is training loss at the end of the inner training horizon (T = 5,000 for the Transformer setting, and T = 2,000 otherwise), and we apply a gradient clipping of 1.0.", "cite_spans": [{"start": 147, "end": 167, "text": "[<PERSON><PERSON> et al., 2021]", "ref_id": "BIBREF37"}], "ref_spans": [], "eq_spans": [], "section": "C.2 Learned optimization meta-training", "sec_num": null}, {"text": "Size of each learned optimizer f (•). Since Deep Set layers are agnostic to the specific weight space being optimized, the Deep Set learned optimizer uses the same number of parameters in each task. The same is not true of UNF layers, where the number of parameters grows in proportion to the size of the bases generated by Algorithm 1. Table 3 lists the number of parameters in f (•) for each learned optimizer.", "cite_spans": [], "ref_spans": [{"start": 343, "end": 344, "text": "3", "ref_id": null}], "eq_spans": [], "section": "C.2 Learned optimization meta-training", "sec_num": null}, {"text": "Experiments were run on a mix of TPU v3 and v4 accelerators. On a TPU v3-8, training a UNF for our RNN generalization prediction task takes < 3 hours. Also on a TPU v3-8, meta-training a UNF for one of our learned optimizers takes ∼ 4 hours for the MLP task, ∼ 7 hours for the CNN task, and ∼ 20 hours for the RNN task.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.3 Compute", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Answer: [Yes] Justification: The abstract contains exactly the description of the algorithm we developed and experiments we ran.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Answer: [Yes] Justification: This is discussed at various points of the paper, including in the Conclusion (final section of the main paper).", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? Answer: [No] Justification: We provide code for implementing the proposed algorithms, but data and code for some of the experiments could not be released due to proprietary restrictions. However, we do include details for how to implement these experiments in the paper. Guidelines:", "cite_spans": [{"start": 198, "end": 202, "text": "[No]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that paper does not include experiments requiring code. • If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction). 10. Broader Impacts Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed? Answer: [NA] Justification: There are no obvious relevant societal impacts. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA] Justification: No relevant risks.", "cite_spans": [{"start": 8, "end": 12, "text": "[NA]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA] Justification: No third-party assets used.", "cite_spans": [{"start": 8, "end": 12, "text": "[NA]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "38th Conference on Neural Information Processing Systems (NeurIPS 2024).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "In the multichannel setting we overload notation and use W to refer to elements of W c , not W.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Inspired by the Tiny CNN Zoo[<PERSON><PERSON><PERSON><PERSON> et al., 2020].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Notably, it computes statistics that are invariant to permutations of the weights.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "In this case, the full basis is also given by<PERSON><PERSON><PERSON> et al. [2018, Appendix A].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "We thank <PERSON><PERSON><PERSON> and <PERSON><PERSON> for insightful general discussions about the project, and <PERSON> for helpful feedback on early drafts. AZ is supported by the NSF Graduate Research Fellowship Program. We are grateful to the TPU Research Cloud (TRC) for providing compute for some of the experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": "7"}, {"text": "Answer: [Yes] Justification: We provide proofs for both Thm 3.2 and 3.3.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?Answer: [Yes] Justification: We provide code to implement the proposed method as well as details in the paper.Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [{"start": 271, "end": 276, "text": "[Yes]", "ref_id": null}, {"start": 1753, "end": 1756, "text": "(d)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• If this information is not available online, the authors are encouraged to reach out to the asset's creators.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets? Answer: [NA] Justification: We do not release new assets. Guidelines:• The answer NA means that the paper does not release new assets.• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Incidence networks for geometric deep learning", "authors": [{"first": "M", "middle": [], "last": "Albooyeh", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1905.11460"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Incidence networks for geometric deep learning. arXiv preprint arXiv:1905.11460, 2019.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Learning to learn by gradient descent by gradient descent", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Pfau", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>llingford", "suffix": ""}, {"first": "N", "middle": ["De"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Advances in neural information processing systems", "volume": "29", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Learning to learn by gradient descent by gradient descent. Advances in neural information processing systems, 29, 2016.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "On the optimization of a synaptic learning rule", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Gescei", "suffix": ""}], "year": 2013, "venue": "Optimality in Biological and Artificial Networks?", "volume": "", "issue": "", "pages": "281--303", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. On the optimization of a synaptic learning rule. In Optimality in Biological and Artificial Networks?, pages 281-303. Routledge, 2013.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Learning a synaptic learning rule. Université de Montréal, Département d'informatique et de recherche", "authors": [{"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1990, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Learning a synaptic learning rule. Université de Montréal, Département d'informatique et de recherche . . . , 1990.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "JAX: composable transformations of Python+NumPy programs", "authors": [{"first": "J", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>p<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. JAX: composable transformations of Python+NumPy programs, 2018. URL http://github.com/google/jax.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "One billion word benchmark for measuring progress in statistical language modeling", "authors": [{"first": "C", "middle": [], "last": "Chelba", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Q", "middle": [], "last": "Ge", "suffix": ""}, {"first": "T", "middle": [], "last": "Brants", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2013, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1312.3005"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. One bil- lion word benchmark for measuring progress in statistical language modeling. arXiv preprint arXiv:1312.3005, 2013.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Empirical evaluation of gated recurrent neural networks on sequence modeling", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Cho", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1412.3555"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Empirical evaluation of gated recurrent neural networks on sequence modeling. arXiv preprint arXiv:1412.3555, 2014.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Group equivariant convolutional networks", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Welling", "suffix": ""}], "year": 2016, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "2990--2999", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON>. Group equivariant convolutional networks. In International conference on machine learning, pages 2990-2999. PMLR, 2016.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Spherical CNNs", "authors": [{"first": "T", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Welling", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1801.10130"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Spherical CNNs. arXiv preprint arXiv:1801.10130, 2018.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Deep learning on implicit neural representations of shapes", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Spezialetti", "suffix": ""}, {"first": "P", "middle": ["Zama"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": ["Di"], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Deep learning on implicit neural representations of shapes. In International Conference on Learning Representations (ICLR), 2023.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Classifying the classifier: dissecting the weight space of neural networks", "authors": [{"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Unger", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2002.05688"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Classifying the classifier: dissecting the weight space of neural networks. arXiv preprint arXiv:2002.05688, 2020.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Finding structure in time", "authors": [{"first": "J", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1990, "venue": "Cognitive science", "volume": "14", "issue": "2", "pages": "179--211", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> <PERSON><PERSON>. Finding structure in time. Cognitive science, 14(2):179-211, 1990.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "A practical method for constructing equivariant multilayer perceptrons for arbitrary matrix groups", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Welling", "suffix": ""}, {"first": "A", "middle": ["G"], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "3318--3328", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>. A practical method for constructing equivariant multilayer perceptrons for arbitrary matrix groups. In International Conference on Machine Learning, pages 3318-3328. PMLR, 2021.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "On the symmetries of deep learning models and their internal representations", "authors": [{"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "11893--11905", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. On the symmetries of deep learning models and their internal representations. Advances in Neural Information Processing Systems, 35:11893- 11905, 2022.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "A closer look at learned optimization: Stability, robustness, and inductive biases", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Metz", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "3758--3773", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. A closer look at learned optimization: Stability, robustness, and inductive biases. Advances in Neural Information Processing Systems, 35:3758-3773, 2022.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Deep models of interactions across sets", "authors": [{"first": "J", "middle": [], "last": "Hartford", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Leyton-Brown", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "1909--1918", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Deep models of interactions across sets. In International Conference on Machine Learning, pages 1909-1918. PMLR, 2018.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "On the algebraic structure of feedforward network weight spaces", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1990, "venue": "Advanced Neural Computers", "volume": "", "issue": "", "pages": "129--135", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>. On the algebraic structure of feedforward network weight spaces. In Advanced Neural Computers, pages 129-135. Elsevier, 1990.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Meta learning backpropagation and improving it", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "14122--14134", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON>. Meta learning backpropagation and improving it. Advances in Neural Information Processing Systems, 34:14122-14134, 2021.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Introducing symmetries to black box meta reinforcement learning", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Flennerhag", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Oh", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "36", "issue": "", "pages": "7202--7210", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Introducing symmetries to black box meta reinforcement learning. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 36, pages 7202-7210, 2022.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Graph Neural Networks for Learning Equivariant Representations of Neural Networks", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Knyazev", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": ["J"], "last": "Burghouts", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["G"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["W"], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "12th International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>. Graph Neural Networks for Learning Equivariant Representations of Neural Networks. In 12th International Conference on Learning Representations (ICLR), 2024.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "On the generalization of equivariance and convolution in neural networks to the action of compact groups", "authors": [{"first": "R", "middle": [], "last": "Kondor", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "2747--2755", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON>. On the generalization of equivariance and convolution in neural networks to the action of compact groups. In International Conference on Machine Learning, pages 2747-2755. PMLR, 2018.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "A simple way to initialize recurrent networks of rectified linear units", "authors": [{"first": "Q", "middle": ["V"], "last": "Le", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1504.00941"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>. A simple way to initialize recurrent networks of rectified linear units. arXiv preprint arXiv:1504.00941, 2015.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Convolutional networks for images, speech, and time series. The handbook of brain theory and neural networks", "authors": [{"first": "Y", "middle": [], "last": "Lecun", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1995, "venue": "", "volume": "3361", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, et al. Convolutional networks for images, speech, and time series. The handbook of brain theory and neural networks, 3361(10):1995, 1995.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Graph metanetworks for processing diverse neural architectures", "authors": [{"first": "D", "middle": [], "last": "Lim", "suffix": ""}, {"first": "H", "middle": [], "last": "Mar<PERSON>", "suffix": ""}, {"first": "M", "middle": ["T"], "last": "Law", "suffix": ""}, {"first": "J", "middle": [], "last": "Lorraine", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.04501"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Graph metanetworks for processing diverse neural architectures. arXiv preprint arXiv:2312.04501, 2023.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Invariant and equivariant graph networks", "authors": [{"first": "H", "middle": [], "last": "Mar<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1812.09902"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Invariant and equivariant graph networks. arXiv preprint arXiv:1812.09902, 2018.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Understanding and correcting pathologies in the training of learned optimizers", "authors": [{"first": "L", "middle": [], "last": "Metz", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "4556--4565", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Understanding and correcting pathologies in the training of learned optimizers. In International Conference on Machine Learning, pages 4556-4565. PMLR, 2019.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Training versatile learned optimizers by scaling up", "authors": [{"first": "L", "middle": [], "last": "Metz", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Merchant", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Poole", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.09760"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, B<PERSON> Poole, <PERSON><PERSON>, <PERSON><PERSON>, et al. Velo: Training versatile learned optimizers by scaling up. arXiv preprint arXiv:2211.09760, 2022.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Equivariant architectures for learning in deep weight spaces", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Shamsian", "suffix": ""}, {"first": "I", "middle": [], "last": "Achituve", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Chechik", "suffix": ""}, {"first": "H", "middle": [], "last": "Mar<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2301.12780"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Equivariant architectures for learning in deep weight spaces. arXiv preprint arXiv:2301.12780, 2023.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Permutation equivariant layers for higher order interactions", "authors": [{"first": "H", "middle": [], "last": "Pan", "suffix": ""}, {"first": "R", "middle": [], "last": "Kondor", "suffix": ""}], "year": 2022, "venue": "International Conference on Artificial Intelligence and Statistics", "volume": "", "issue": "", "pages": "5987--6001", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON>. Permutation equivariant layers for higher order interactions. In International Conference on Artificial Intelligence and Statistics, pages 5987-6001. PMLR, 2022.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Learning to learn with generative models of neural network checkpoints", "authors": [{"first": "W", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2209.12892"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, and <PERSON><PERSON>. Learning to learn with generative models of neural network checkpoints. arXiv preprint arXiv:2209.12892, 2022.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Equivariance through parameter-sharing", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "2892--2901", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Equivariance through parameter-sharing. In Interna- tional conference on machine learning, pages 2892-2901. PMLR, 2017.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "A 'self-referential'weight matrix", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1993, "venue": "ICANN'93: Proceedings of the International Conference on Artificial Neural Networks Amsterdam, The Netherlands 13", "volume": "", "issue": "", "pages": "446--450", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>. A 'self-referential'weight matrix. In ICANN'93: Proceedings of the International Conference on Artificial Neural Networks Amsterdam, The Netherlands 13-16 September 1993 3, pages 446-450. Springer, 1993.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Sequence to sequence learning with neural networks", "authors": [{"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON>yal<PERSON>", "suffix": ""}, {"first": "Q", "middle": ["V"], "last": "Le", "suffix": ""}], "year": 2014, "venue": "Advances in neural information processing systems", "volume": "27", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON> to sequence learning with neural networks. Advances in neural information processing systems, 27, 2014.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "The general theory of permutation equivarant neural networks and higher order graph variational encoders", "authors": [{"first": "E", "middle": ["H"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": ["S"], "last": "Hy", "suffix": ""}, {"first": "R", "middle": [], "last": "Kondor", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2004.03990"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, and <PERSON><PERSON>. The general theory of permutation equivarant neural networks and higher order graph variational encoders. arXiv preprint arXiv:2004.03990, 2020.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Predicting neural network accuracy from weights", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2002.11448"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Predicting neural network accuracy from weights. arXiv preprint arXiv:2002.11448, 2020.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Unbiased gradient estimation in unrolled computation graphs with persistent evolution strategies", "authors": [{"first": "P", "middle": [], "last": "Vicol", "suffix": ""}, {"first": "L", "middle": [], "last": "Metz", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "10553--10563", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Unbiased gradient estimation in unrolled computation graphs with persistent evolution strategies. In International Conference on Machine Learning, pages 10553-10563. PMLR, 2021.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Fleet policy learning via weight merging and an application to robotic tool-use", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.01362"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Fleet policy learning via weight merging and an application to robotic tool-use. arXiv preprint arXiv:2310.01362, 2023.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Learned optimizers that scale and generalize", "authors": [{"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": ["G"], "last": "Colmenarejo", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "3751--3760", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Learned optimizers that scale and generalize. In International conference on machine learning, pages 3751-3760. PMLR, 2017.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms, 2017.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Deep sets", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ko<PERSON>ur", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Deep sets. doi: 10.48550. arXiv preprint ARXIV.1703.06114, 2017.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Neural networks are graphs! graph neural networks for equivariant processing of neural networks", "authors": [{"first": "D", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": ["J"], "last": "Burghouts", "suffix": ""}, {"first": "C", "middle": ["G"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, and <PERSON><PERSON> <PERSON><PERSON>. Neural networks are graphs! graph neural networks for equivariant processing of neural networks. 2023.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Permutation equivariant neural functionals", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "S", "middle": [], "last": "Sokota", "suffix": ""}, {"first": "J", "middle": ["Z"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.14040"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, and <PERSON><PERSON>. Permutation equivariant neural functionals. arXiv preprint arXiv:2302.14040, 2023a.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Neural functional transformers", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Sokota", "suffix": ""}, {"first": "J", "middle": ["Z"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.13546"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, and <PERSON><PERSON>. Neural functional transformers. arXiv preprint arXiv:2305.13546, 2023b.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "text": "extending the (single channel) definition. The definition of equivariance can then be extended to layers of the form T (•) : W ci → W co , where c i , c o are the number of input and output channels.", "num": null, "fig_num": null, "uris": null}, "TABREF1": {"html": null, "type_str": "table", "text": "with hidden size 128. Each model is trained with a distinct learning rate and batch size, and it's test success rate (SR) is recorded. The learning rate is sampled from a log-uniform distribution over [10 -4 , 10 -2 ], and the batch size is sampled uniformly from { 64, 128, 256 }. With the goal of predicting test SR from weights, we split the Tiny RNN Zoo into 8000/1000/1000 training, validation, and test examples. Rank correlation between predicted and actual success rates of RNNs on an arithmetic task. Predicting with UNF significantly outperforms STATNN[<PERSON><PERSON><PERSON><PERSON> et al., 2020].", "num": null, "content": "<table><tr><td>Method</td><td>Test τ</td></tr><tr><td>Deep Set</td><td>0.8306 ± 0.0006</td></tr><tr><td>STATNN</td><td>0.8839 ± 0.0007</td></tr><tr><td colspan=\"2\">UNF (Ours) 0.8968 ± 0.0006</td></tr></table>"}, "TABREF2": {"html": null, "type_str": "table", "text": "Figure3: Number of parameters used by f (•) in each learned optimizer, for each task. Note that NFN and UNF are identical for the MLP task. This count does not include the other meta-learned scalars in Eq. 15, which are α, γ 0 , β.", "num": null, "content": "<table><tr><td>Task</td><td>UNF</td><td>Deep Set</td><td>NFN</td></tr><tr><td colspan=\"2\">MLP on FashionMNIST 3,783</td><td>2,788</td><td>3,783</td></tr><tr><td>CNN on CIFAR-10</td><td>7,369</td><td>2,788</td><td>41,603</td></tr><tr><td>RNN on LM1B</td><td>8,043</td><td>2,788</td><td>N/A</td></tr><tr><td>Transformer on LM1B</td><td>64,168</td><td>2,788</td><td>N/A</td></tr><tr><td colspan=\"4\">MLP on FashionMNIST. Train a three-layer MLP classifier on a downsized (8 × 8) and flattened</td></tr><tr><td>version of the FashionMNIST dataset</td><td/><td/><td/></tr></table>"}, "TABREF3": {"html": null, "type_str": "table", "text": "• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details.• While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable). • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted. Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. 8. Experiments Compute Resources Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper). 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines? Answer: [Yes] Justification: We have reviewed the Code of Ethics. Guidelines: • The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "num": null, "content": "<table><tr><td>6. Experimental Setting/Details Question: Does the paper specify all the training and test details (e.g., data splits, hyper-parameters, how they were chosen, type of optimizer, etc.) necessary to understand the results? Answer: [Yes] Justification: See Appendix C. Guidelines: • The answer NA means that the paper does not include experiments. • The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material. 7. Experiment Statistical Significance Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [Yes] Justification: Our results report error bars. Guidelines: • The answer NA means that the paper does not include experiments. Answer: [Yes] Justification: See Appendix C.3. Guidelines: • The authors should answer \"the experiments? •</td></tr></table>"}}}}