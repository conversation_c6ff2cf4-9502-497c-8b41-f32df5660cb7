{"paper_id": "Learning_Vibrating_Plates", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:13:52.390086Z"}, "title": "Learning to Predict Structural Vibrations", "authors": [{"first": "Jan", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Göttingen", "location": {}}, "email": "jan.van<PERSON><PERSON>@uni-goettingen.de"}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Technische Universität Braunschweig", "location": {}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Technische Universität Braunschweig", "location": {}}, "email": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Technische Universität Braunschweig", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Göttingen", "location": {}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "In mechanical structures like airplanes, cars and houses, noise is generated and transmitted through vibrations. To take measures to reduce this noise, vibrations need to be simulated with expensive numerical computations. Deep learning surrogate models present a promising alternative to classical numerical simulations as they can be evaluated magnitudes faster, while trading-off accuracy. To quantify such trade-offs systematically and foster the development of methods, we present a benchmark on the task of predicting the vibration of harmonically excited plates. The benchmark features a total of 12,000 plate geometries with varying forms of beadings, material, boundary conditions, load position and sizes with associated numerical solutions. To address the benchmark task, we propose a new network architecture, named Frequency-Query Operator, which predicts vibration patterns of plate geometries given a specific excitation frequency. Applying principles from operator learning and implicit models for shape encoding, our approach effectively addresses the prediction of highly variable frequency response functions occurring in dynamic systems. To quantify the prediction quality, we introduce a set of evaluation metrics and evaluate the method on our vibrating-plates benchmark. Our method outperforms Deep-ONets, Fourier Neural Operators and more traditional neural network architectures and can be used for design optimization. Code, dataset and visualizations: https://github.com/ecker-lab/Learning_Vibrating_Plates", "pdf_parse": {"paper_id": "Learning_Vibrating_Plates", "_pdf_hash": "", "abstract": [{"text": "In mechanical structures like airplanes, cars and houses, noise is generated and transmitted through vibrations. To take measures to reduce this noise, vibrations need to be simulated with expensive numerical computations. Deep learning surrogate models present a promising alternative to classical numerical simulations as they can be evaluated magnitudes faster, while trading-off accuracy. To quantify such trade-offs systematically and foster the development of methods, we present a benchmark on the task of predicting the vibration of harmonically excited plates. The benchmark features a total of 12,000 plate geometries with varying forms of beadings, material, boundary conditions, load position and sizes with associated numerical solutions. To address the benchmark task, we propose a new network architecture, named Frequency-Query Operator, which predicts vibration patterns of plate geometries given a specific excitation frequency. Applying principles from operator learning and implicit models for shape encoding, our approach effectively addresses the prediction of highly variable frequency response functions occurring in dynamic systems. To quantify the prediction quality, we introduce a set of evaluation metrics and evaluate the method on our vibrating-plates benchmark. Our method outperforms Deep-ONets, Fourier Neural Operators and more traditional neural network architectures and can be used for design optimization. Code, dataset and visualizations: https://github.com/ecker-lab/Learning_Vibrating_Plates", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Humans are exposed to noise in everyday life, which is unpleasant and unhealthy in the long term [1] . Therefore, designers and engineers work on reducing noise that occurs, for example, in cars, airplanes, and houses. In this work, we specifically consider vibrations in mechanical structures as a source of sound. Vibrating structures radiate sound into the surrounding air. For example in a car, the engine causes the chassis to vibrate, which then radiates sound into the interior of the car. By reducing the vibration energy of the chassis, the noise can be reduced.", "cite_spans": [{"start": 97, "end": 100, "text": "[1]", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Vibrations of mechanical structures depend on the frequency of the excitation force (e.g. by the engine). A special case occurs when the excitation frequency matches an eigenfrequency of a given structure. In this case, the external force adds energy in phase with the structure's natural vibration and amplifies the motion with each cycle. This continues until the energy added equals the energy lost due to damping, resulting in large vibration amplitudes. This effect is called resonance and leads to characteristic resonance peaks in the dynamic response of the system. At resonance frequencies, due to the higher vibration amplitudes, more noise is emitted. A second distinctive feature of structural vibrations is the vibration pattern, i.e. the spatial field of vibration velocity amplitudes. With increasing frequency, these vibration patterns become more complex and exhibit more local maxima and minima (Figure 1 , left) [2] .", "cite_spans": [{"start": 931, "end": 934, "text": "[2]", "ref_id": "BIBREF1"}], "ref_spans": [{"start": 921, "end": 922, "text": "1", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "38th Conference on Neural Information Processing Systems (NeurIPS 2024). To reduce noise, the vibration patterns of a mechanical structure can be influenced through modifications to its design. One method is the placement of damping elements, that absorb vibrational energy and thereby reduce sound emission, but this adds weight and requires space. Another approach is introducing beadings, which are indentations in plate-like structures (Figure 1 , right). Beadings increase the local stiffness of a structure, resulting in a shift in the structure's eigenfrequencies and subsequent resonance peaks. When they are well-placed, beadings can reduce the vibration energy for a range of excitation frequencies by shifting the resonance peaks out of the range. Reducing the vibration energy for a specific range of frequencies is a goal in many applications, e.g. in automotive design, where a motor excites vibrations in a range of frequencies [3] .", "cite_spans": [{"start": 943, "end": 946, "text": "[3]", "ref_id": "BIBREF2"}], "ref_spans": [{"start": 448, "end": 449, "text": "1", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In this work, we focus on a crucial prerequisite for targeted modifications to a design: Computing its vibrational behavior. The finite element method (FEM) is an established approach for numerically solving partial differential equations. The geometry of a design is discretized into small elements and the solution of the PDE is approximated by simple functions, e.g. polynomial functions, defined on these elements. [4, 5] . This method enables the numerical simulation of vibration patterns, but is computationally expensive. With increasing frequency and decreasing wavelength, finer meshes are required to accurately resolve the vibrations. This leads to a high increase in computational load and limits the number of designs and value of the frequencies that can be evaluated. Deep learning surrogate models could accelerate the evaluation of design candidates by several magnitudes. Related work on predicting the solution of partial differential equations with deep learning has mostly focused on time-domain problems [e.g. 6, 7, 8] . In contrast, for our problem the change over time is not of interest. Instead, we predict steady-state vibration patterns in the frequency domain. Steady-state refers to the fact that the system vibrates harmonically and the amplitude and frequency remain constant over time since the system is in a dynamic equilibrium. Despite being practically relevant in acoustics and structural dynamics in general this problem is so far under-explored by machine learning research.", "cite_spans": [{"start": 419, "end": 422, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 423, "end": 425, "text": "5]", "ref_id": "BIBREF4"}, {"start": 1033, "end": 1035, "text": "6,", "ref_id": "BIBREF5"}, {"start": 1036, "end": 1038, "text": "7,", "ref_id": "BIBREF6"}, {"start": 1039, "end": 1041, "text": "8]", "ref_id": "BIBREF7"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Contributions. To explore the potential of vibration prediction with deep learning methods, we (1) introduce a benchmark and define evaluation metrics on it, (2) evaluate a range of machine learning methods on the benchmark and (3) introduce our own method.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Our novel benchmark dataset consists of 12,000 instances of an exemplary structural mechanical system, a plate excited by a harmonic force, and their numerically computed vibrations given a range of excitation frequencies. Given a plate instance, the task is to predict the vibration patterns and frequency response. We vary material properties and the boundary conditions of the plate as well as the geometry by adding beadings. Plates with beadings are abundant in technical systems (Figure 1 , right). Plates are also often a component of more complex mechanical systems and their vibrational behavior on their own is similar to more complex systems [9, 10] , making them a wellposed and scalable initial benchmark problem for deep learning methods.", "cite_spans": [{"start": 653, "end": 656, "text": "[9,", "ref_id": "BIBREF8"}, {"start": 657, "end": 660, "text": "10]", "ref_id": "BIBREF9"}], "ref_spans": [{"start": 493, "end": 494, "text": "1", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "To address the benchmark task, we propose a novel network architecture named Frequency-Query Operator (FQO). This model is trained to predict the resulting vibration pattern from plate geometries together with an excitation frequency query. This approach is inspired by work on operator learning for predicting the solution to partial differential equations [11] and implicit models for shape representation [e.g. 12, 13, 14], both techniques enable evaluating any point in the domain instead of a fixed grid. In our case, this enables predictions for any excitation frequency, including those not seen during training. On our vibrating-plates benchmark, the proposed FQO can accurately predict the highly variable resonances occurring in vibration patterns and outperforms DeepONet [11] , Fourier Neural Operators [15] and other baselines.", "cite_spans": [{"start": 358, "end": 362, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 783, "end": 787, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 815, "end": 819, "text": "[15]", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "We introduce a dataset consisting of instances of aluminum plate geometries and their vibration patterns. The plates are simply supported, i.e. the edges cannot move up and down. Depending on the dataset setting, the rotational stiffness at the boundary is varied, which corresponds to free rotation or clamped edges. The plate is excited by a harmonic point force at varying positions with the excitation frequency varied between 1 and 300 Hz. While the specific setting in other mechanical engineering design tasks may differ, this setup functions as an exemplary engineering design problem. Analogous problems are the design of an air-conditioning enclosure [16] , a washing machine [17] or parts of a car chassis [3] . Compared to these problems, our plate setup has two differences that allow for a comparatively easy experimental real world validation of the computed vibration patterns and do not change typical vibrational characteristics: First, exciting the plate with a point force is a common experimental setup, where a plate is excited via a shaker. Second, the condition of no rotational stiffness at the edges in comparison to clamped edges does not introduce additional uncertainty and parameters into the measurement and mirrors e.g. a bonnet of a car that rests on the chassis. Other typical types of fixation include screws or welding. In the following, we describe the specific quantity of interest of the vibration patterns, how the vibration patterns of the plate are obtained via numerical simulation and how the plate geometry and parameters are varied.", "cite_spans": [{"start": 661, "end": 665, "text": "[16]", "ref_id": "BIBREF15"}, {"start": 686, "end": 690, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 717, "end": 720, "text": "[3]", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "Dataset and Benchmark Construction 2.1 Vibrating Plates Dataset", "sec_num": "2"}, {"text": "Vibration patterns and frequency response function. Our benchmark is designed to address a vibroacoustic engineering design problem. Therefore, the goal is to predict a quantity that best reflects the noise emitted by a mechanical structure. For a plate, a natural choice is the maximum velocity field v z (x, y|f ) for a specific frequency f . Here, v z (x, y|f ) represents the component of the velocity field orthogonal to the plate surface (in the following V(f ) denotes the velocity field on the discrete grid). This component closely relates to how much sound is radiated, but specific details about where the velocity on the plate is highest are superfluous. Therefore, we use the mean of the squared velocity as a more compact representation and express it in a frequency response function F, which is a function of the excitation frequency:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset and Benchmark Construction 2.1 Vibrating Plates Dataset", "sec_num": "2"}, {"text": "F(f ) = 10 log 10 r A A v z (x, y|f ) 2 dA (1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset and Benchmark Construction 2.1 Vibrating Plates Dataset", "sec_num": "2"}, {"text": "The square velocity is proportional to the kinetic energy and is therefore closely related to how strongly the vibration couples into a surrounding fluid and can then be perceived as airborne sound.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset and Benchmark Construction 2.1 Vibrating Plates Dataset", "sec_num": "2"}, {"text": "In the above expression, A is the plate area over which the velocity is averaged. The result is scaled by a reference value r and converted to a decibel scale.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset and Benchmark Construction 2.1 Vibrating Plates Dataset", "sec_num": "2"}, {"text": "Numerical simulation. Historically, plate structures have been the subject of intense research regarding their vibrational behavior [e.g. 18, 19] . A common approach in plate modeling is to reduce the model to a two-dimensional problem with the goal to accurately describe the vibrational behavior while being computationally efficient [20] . To model the vibrational behavior of plates in this work, we use a shell formulation based on <PERSON><PERSON>'s plate theory [18] . This theory is applicable for moderately thin plates and represents the plate using a mid-plane with constant thickness. <PERSON><PERSON> plate theory is a standard choice in many engineering applications and has been experimentally validated [21, 22] .", "cite_spans": [{"start": 138, "end": 141, "text": "18,", "ref_id": "BIBREF17"}, {"start": 142, "end": 145, "text": "19]", "ref_id": "BIBREF18"}, {"start": 336, "end": 340, "text": "[20]", "ref_id": "BIBREF19"}, {"start": 460, "end": 464, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 702, "end": 706, "text": "[21,", "ref_id": "BIBREF20"}, {"start": 707, "end": 710, "text": "22]", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "Dataset and Benchmark Construction 2.1 Vibrating Plates Dataset", "sec_num": "2"}, {"text": "Figure 2 : Process of the finite element solution in frequency domain in order to compute the velocity field at each frequency query.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Dataset and Benchmark Construction 2.1 Vibrating Plates Dataset", "sec_num": "2"}, {"text": "We apply the finite element method to solve the shell formulation and simulate the vibrational behavior of the plate [4] (Figure 2 ). This involves partitioning the plate geometry into discrete elements and approximating the solution on these elements by simple ansatzfunctions. By choosing a sufficiently large number of elements, the solution converges to the exact solution of the model [23] . We discretize the plate with a regular grid and use triangular elements in the domain to allow a flexible representation of beadings. The discretization is sufficient to resolve wave lengths in the plate structure, but limits the detail that can be represented with the beading patterns. After discretizing the plate, the PDE is integrated over the elements and a linear system of equations is derived. This linear system describes the dynamics of the discretized structure and is solved with a direct solver. We perform the computations with a specialized FEM software for acoustics [24] . Further details on the setup and mechanical model are given in Appendix A.1.", "cite_spans": [{"start": 117, "end": 120, "text": "[4]", "ref_id": "BIBREF3"}, {"start": 390, "end": 394, "text": "[23]", "ref_id": "BIBREF22"}, {"start": 981, "end": 985, "text": "[24]", "ref_id": "BIBREF23"}], "ref_spans": [{"start": 129, "end": 130, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Dataset and Benchmark Construction 2.1 Vibrating Plates Dataset", "sec_num": "2"}, {"text": "Dataset variations. The plate instances are varied in two settings: For the V-5000 setting, we generate random beading patterns consisting of 1 -3 lines and 0 -2 ellipses. Also, the width of the beading-elements is randomly varied. The size of the plates as well as material, boundary and loading parameters are fixed. For the G-5000 setting, we apply the same beading pattern variation and additionally vary the plate geometry (length, width and thickness) as well as the damping loss factor, rotational stiffness at the boundary and forcing position. For each setting, 5000 instances for training and validation are generated. 1000 further instances are generated as a test set and are not used during training or to select a model. Further details are given in Appendix A.2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Dataset and Benchmark Construction 2.1 Vibrating Plates Dataset", "sec_num": "2"}, {"text": "Dataset analysis. The mean plate design shows a close to uniform distribution, with a margin at the plate's edge (see Figure 3b ). With a greater proportion of beaded area in a given plate, the number of peaks tends to decrease (see Figure 3a ). This is due to additional beadings stiffening the plates, and it represents an interesting trait specific to our problem. The density of peaks is related to the frequency. As the frequency increases, so does the peak density. Starting from around 120 Hz the peak density plateaus (see Figure 3d ). The average number of peaks in the G-5000 setting is smaller than in the V-5000 setting. This is influenced by the on average smaller plates being stiffer and therefore having less peaks in the frequency range (see Figure 3c ).", "cite_spans": [], "ref_spans": [{"start": 125, "end": 127, "text": "3b", "ref_id": "FIGREF2"}, {"start": 240, "end": 242, "text": "3a", "ref_id": "FIGREF2"}, {"start": 538, "end": 540, "text": "3d", "ref_id": "FIGREF2"}, {"start": 766, "end": 768, "text": "3c", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Dataset and Benchmark Construction 2.1 Vibrating Plates Dataset", "sec_num": "2"}, {"text": "Before computing our metrics, we perform the following preprocessing steps to address numerical issues as well as facilitate an easier interpretation of the evaluation metrics. We normalize the fre-quency response and the velocity fields. To do this, we first take the log of the velocity fields, to align it with the dB-scale of the frequency response. Then, we subtract the mean per frequency over all samples (depicted in Figure 3b for frequency response) and then divide by the overall standard deviation across all frequencies and samples. Small changes in the beading pattern can cause frequency shifts, potentially pushing peaks out of the considered frequency band. To reduce the effect of such edge cases, we predict frequency responses between 1 and 300 Hz but evaluate on the frequency band between 1 and 250 Hz.", "cite_spans": [], "ref_spans": [{"start": 432, "end": 434, "text": "3b", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Evaluation", "sec_num": "2.2"}, {"text": "We propose three complementary metrics to measure the quality of the frequency response predictions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "2.2"}, {"text": "Mean squared error. The mean squared error (MSE) is a well-known regression error measure:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "2.2"}, {"text": "For the global deviation we compare the predicted F(f ) and numerically computed frequency response F(f ) by the MSE error", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "2.2"}, {"text": "E MSE = i ( F(f i ) -F(f i )) 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "2.2"}, {"text": "Earth mover distance. The earth mover distance [25, 26] expresses the work needed to transmute a distribution P into another distribution Q. As a first step, the optimal flow γ is identified. Based on γ the earth mover distance is expressed as follows:", "cite_spans": [{"start": 47, "end": 51, "text": "[25,", "ref_id": "BIBREF24"}, {"start": 52, "end": 55, "text": "26]", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "2.2"}, {"text": "E EMD (P, Q) = i,j γij • d ij i,j γij with γ = min γ i,j γ ij • d ij", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "2.2"}, {"text": "where d ij is the distance between bins i and j in P and Q. Correspondingly, γ ij is the flow between bins i and j. We calculate the E EMD based on the original amplitudes in m/s that have not been transformed to the log-scale (dB) and normalize these amplitudes with the sum over all frequencies. As a consequence and unlike the MSE, E EMD is invariant to the mean amplitude and only considers the shape of the frequency response. In this form, our metric is equivalent to the W 1 Wasserstein metric [27, 28] .", "cite_spans": [{"start": 501, "end": 505, "text": "[27,", "ref_id": "BIBREF26"}, {"start": 506, "end": 509, "text": "28]", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "2.2"}, {"text": "Peak frequency error. To specifically address the prediction of resonance peaks, which are particularly relevant for noise emission, we introduce a third metric called peak frequency error. The metric answers two questions: (1) Does the predicted frequency response contain the same number of resonance peaks as the true response? (2) How far are corresponding ground truth and prediction peaks shifted against each other? To this end, we set up an algorithm that starts by detecting a set of peaks K in the ground truth and a set of peaks K in the prediction using the find peaks function in scipy [29] (examples in Appendix B). Then, we match these peaks pairwise using the Hungarian algorithm [30] based on the distance between the frequencies of the peaks E F . This allows us to determine the ratio between predicted and actual peaks | K| |K| and |K| | K| . To equally penalize predicting too many and too few peaks we consider the minimum of both ratios:", "cite_spans": [{"start": 599, "end": 603, "text": "[29]", "ref_id": "BIBREF28"}, {"start": 696, "end": 700, "text": "[30]", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "2.2"}, {"text": "E PEAKS = 1 -min{ | K| |K| , |K| | K| }.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Evaluation", "sec_num": "2.2"}, {"text": "We propose a method to predict the frequency response, F g,m (f ), for plates characterized by their geometry g (influenced by beading patterns) and scalar parameters m (height, width, thickness, damping loss factor, rotational stiffness at boundary, loading position). This process involves two steps: (1) First, the input g and m are encoded by an encoder Φ. Because g is defined on a regular grid, standard image processing architectures are suitable. (2) Frequency response predictions are generated for specific excitation frequencies f by a decoder Ψ (Figure 4 ). The computation can then be expressed as:", "cite_spans": [], "ref_spans": [{"start": 565, "end": 566, "text": "4", "ref_id": null}], "eq_spans": [], "section": "Predicting Vibrations with Neural Networks", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Ψ(Φ(g, m), f ) = Fg,m (f )", "eq_num": "(2)"}], "section": "Predicting Vibrations with Neural Networks", "sec_num": "3"}, {"text": "This problem formulation, training a neural network to predict a function and evaluating this function, given some input values, is a common paradigm in operator learning [11] . It allows for the evaluation of any frequency query f , even if it has not been part of the training data. In contrast, predicting frequencies on a fixed grid only allows for the evaluation of those frequencies. This formulation shares similarities with implicit models, for instance by [13] in the context of 3d shape prediction. Based on this, we investigate the following central aspects of our architecture:", "cite_spans": [{"start": 171, "end": 175, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 465, "end": 469, "text": "[13]", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Predicting Vibrations with Neural Networks", "sec_num": "3"}, {"text": "Figure 4 : Frequency-Query Operator method. The geometry encoder takes the mesh geometry and the scalar properties as input. The resulting feature volume along with a frequency query is passed to the query decoder, that either predicts a velocity field or directly a frequency response. The velocity field is aggregated to arrive at the frequency response at the query frequency f .", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "4", "ref_id": null}], "eq_spans": [], "section": "Predicting Vibrations with Neural Networks", "sec_num": "3"}, {"text": "Q1 -Frequency-query approach: Vibrations are dominated by resonance peaks at specific frequencies. The resonance frequencies vary strongly across instances. An implicit or operator learning approach has been shown to be able to deal with high variation better in other contexts. In the context of vibration prediction, a frequency-query approach could be employed to generate predictions for one specific frequency.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Predicting Vibrations with Neural Networks", "sec_num": "3"}, {"text": "Q2 -ViT encoder: Image processing architectures based on convolutions encode local features. In contrast, vision transformers have a global receptive field size from early layers. As vibrations are determined by the full geometry, we expect vision transformers to perform better.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Predicting Vibrations with Neural Networks", "sec_num": "3"}, {"text": "We can train networks to either directly predict the aggregate frequency response F or to predict the velocity field V and compute F from V via Equation 1. For predicting the velocity field, much richer training data is available, since it describes a field over the plate instead of the scalar frequency response. Most of this information is not represented in the frequency response.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q3 -Velocity field prediction:", "sec_num": null}, {"text": "In the following, we describe architectural variations explored for these aspects.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Q3 -Velocity field prediction:", "sec_num": null}, {"text": "To parse the plate geometry into a feature vector, we employ three variants: ResNet18 [31, RN18], a vision transformer [32, 33, ViT] and the encoder part of a UNet [34] . For the RN18, we replace batch normalization with layer normalization [35] , as we found this to work substantially better. Compared to the CNN-based RN18, the ViT architecture supports interactions across different image regions in early layers. For both, the RN18 and the ViT encoder, we obtain a feature vector x by average pooling the last feature map. Since the UNet generates velocity fields, no pooling is applied.", "cite_spans": [{"start": 119, "end": 123, "text": "[32,", "ref_id": "BIBREF31"}, {"start": 124, "end": 127, "text": "33,", "ref_id": "BIBREF32"}, {"start": 128, "end": 132, "text": "ViT]", "ref_id": null}, {"start": 164, "end": 168, "text": "[34]", "ref_id": "BIBREF33"}, {"start": 241, "end": 245, "text": "[35]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Geometry Encoder Φ", "sec_num": "3.1"}, {"text": "FiLM conditioning. For including the scalar parameters m, we introduce a film layer [36] . The film layer first encodes the scalar parameters with a linear layer. The resulting encoding is then multiplied element-wise with the feature of the encoder and a bias is added. This operation is applied before the last layer of the geometry encoder (UNet) or after it (RN18, ViT).", "cite_spans": [{"start": 84, "end": 88, "text": "[36]", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Geometry Encoder Φ", "sec_num": "3.1"}, {"text": "FQO-RN18 and FQO-ViT: Predicting F(f ) directly. Having obtained an encoding of the plate geometry and properties x, a decoder now takes this as well as a frequency query as input and maps them towards a prediction. For the RN18 and ViT geometry encoders, the decoder is implemented by an MLP taking both x and a scalar frequency value f as input to predict the response for that specific query frequency, i.e. Ψ(x, f ) ∈ R. The frequency query is merged to x by a film layer [36] . By querying the decoder with all frequencies individually, we obtain results for the frequency band between 1 and 300 Hz. The MLP has six hidden layers with 512 dimensions each and ReLU activations.", "cite_spans": [{"start": 476, "end": 480, "text": "[36]", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Decode<PERSON>", "sec_num": "3.2"}, {"text": "FQO-UNet: Predicting F(f ) through the velocity field V(f ). To incorporate physics-based contraints and take advantage of the larger amount of available data, we employ a UNet to predict the velocity fields, V(f ). From V(f ), we derive the frequency response F(f ) (analogous to Equation 1). A frequency query, introduced via a FiLM layer after the encoder, enables frequencyspecific predictions. To reduce the memory and computation demands per geometry during training, we select a random subset of k frequency queries per geometry in a batch, with k < 300. If not otherwise specified, k is set to 50. Grid-Unet and Grid-RN18: Predicting F for a fixed grid of frequencies. To ablate the frequency-query approach, we employ two variations of the FQO-RN18 and FQO-UNet architectures, that do not employ frequency queries. They instead generate predictions for 1-300 Hz at once. This is done by setting the output size of the respective last layer to 300.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Decode<PERSON>", "sec_num": "3.2"}, {"text": "We further report baseline results on the following alternative methods: A k-Nearest Neighbors regressor, that finds the nearest neighbors in the latent space of an autoencoder. DeepONet [11] , with a RN18 as backbone and a MLP to encode the query frequencies as a branch net. Two architectures based on Fourier Neural Operators [15] . One employing an FNO as a replacement for the querybased decoder based on RN18 features. The second directly takes the input geometry and is trained to map it to the velocity fields.", "cite_spans": [{"start": 187, "end": 191, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 329, "end": 333, "text": "[15]", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "Baseline Methods", "sec_num": "3.3"}, {"text": "All methods are trained in a data-driven fashion for 500 epochs on the training dataset of 5000 samples. 500 samples from the training dataset are excluded and employed for validation. We report evaluation results on the previously unseen test set consisting of 1000 additional samples.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training", "sec_num": "3.4"}, {"text": "For methods that predict V(f ), i.e. UNet based methods and the FNO variation, the training loss is set to L V where L V represents the MSE on the log-transformed, normalized squared velocity field (Ablation on loss function in Appendix D). For methods that directly predict F, the loss is set to L F , the MSE on the normalized frequency response. Choosing the log-transformed quantities enables the loss to be sensitive to errors outside of resonance frequencies. Otherwise, such errors would have little influence on the total loss, as their magnitude is much lower. See Appendix C for further details on the architectures and training procedure.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training", "sec_num": "3.4"}, {"text": "We train the architecture variations and baseline methods on the Vibrating Plates dataset (see Table 1 ). To assess which architecture aspects described in Section 3 are beneficial, we perform the following comparisons. Regarding Q1 (frequency-query approach), the Frequency-Query Operator variations consistently yield better predictions than equivalent grid-based methods, where responses for all frequencies are predicted at once: The E MSE and the E EMD are lower, more peaks are reproduced, and the peak positions are more precise. Regarding Q3 (velocity field prediction), predicting the velocity fields and then transforming them to the frequency response leads to better results than directly predicting the frequency response. Specifically, the UNet based architectures strongly outperform all alternatives, which we attribute to the richer training data of velocity fields. Regarding Q2, the ViT encoder leads to worse results than the CNN-based encoders. All evaluated baseline methods achieve comparatively worse results than our proposed methods. Despite using the same RN18 geometry encoder as FQO-RN18, DeepONet [11] performs worse. We assume that this is due to incorporating frequency information through a single weighted summation, which limits the model's expressivity [37] . In contrast, FQO-RN18 introduces the queried frequency earlier into the model. Two Fourier Neural Operator [15] baseline methods are evaluated: the first, RN18 + FNO, which substitutes the query-based decoder with an FNO decoder, underperforms compared to FQO-RN18 on both datasets. The second FNO baseline, trained directly to predict velocity fields, yields poorer results.", "cite_spans": [{"start": 1127, "end": 1131, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 1289, "end": 1293, "text": "[37]", "ref_id": "BIBREF36"}, {"start": 1403, "end": 1407, "text": "[15]", "ref_id": "BIBREF14"}], "ref_spans": [{"start": 101, "end": 102, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "Results for the G-5000 setting are slightly worse than for the V-5000 setting. The difference is surprising small considering the seven additional varied parameters in the G-5000 setting. One reason might be the average number of peaks in the frequency response: the plates in G-5000 are on average smaller and because of this stiffer, leading to fewer peaks (on average 3.9 in G-5000 and 5.9 in V-5000). This interpretation is supported by the fact that the average error becomes higher with increasing frequency and thus increasing peak density (Figure 3d ). Looking at a prediction example (Figure 5a-d ) for our best model, FQO-UNet, the predicted velocity field has subtle differences to the ground truth. The prediction captures the two modes and their shape quite well, but the shape is slightly less regular than in the reference. Despite that, the resulting frequency response prediction at f = 131 is close to the FEM reference. In comparison to the grid-based prediction, where peaks tend to be blurry, the frequency response peaks generated by FQO-UNet are more pronounced. Additional visualizations are provided in Appendix E.3 and in the code repository. For the best architecture in our experiments, FQO-UNet, we report mean and standard deviation results for multiple runs in Appendix E.2 and provide an ablation of model size for the FQO-UNet and Grid-UNet architectures in Appendix D.", "cite_spans": [], "ref_spans": [{"start": 555, "end": 557, "text": "3d", "ref_id": "FIGREF2"}, {"start": 601, "end": 605, "text": "5a-d", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "Transfer learning. To quantify to which degree features learned on a subset of the design space transfer to a different subset, the V-5000 setting is split into two equally-sized parts based on the number of mesh elements that are part of a beading. The \"more beadings\" set contains only 5.1 peaks on average because the plates are stiffened by the beadings, compared to 6.7 peaks on average for the \"less beadings\" set. The training on plates with less beadings leads to a smaller drop in prediction quality (see Table 2 ). This indicates that training on data with more complex frequency responses might be more efficient. In addition, we train a single model on both G-5000 and V-5000. Performance increases, indicating that training can benefit from training with data based on similar mechanical models (Table 3 ).", "cite_spans": [], "ref_spans": [{"start": 520, "end": 521, "text": "2", "ref_id": "TABREF1"}, {"start": 815, "end": 816, "text": "3", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "Sample efficiency. We train the FQO-UNet and the FQO-RN18 with reduced numbers of samples (see Figure 5e ). It is notable, that the FQO-UNet with a quarter of the training data achieves nearly the same prediction quality as the FQO-RN18 with full training data. This highlights the benefit of including the velocity fields into the training process. Quantitative results are given in Appendix E.1 for both dataset settings. We further investigate the optimal ratio of numbers of frequencies per geometry and total number of geometries, by generating an additional dataset in the V-5000 setting consisting of 50,000 plate geometries but with only 15 frequency evaluations per geometry. These frequencies are uniformly spaced with a random starting frequency. Reducing the frequencies per geometry drastically increases the data efficiency of our method. With a tenth of data points compared to our original dataset, the MSE metric approaches the original value (Figure 5f , quantitative results in Appendix E.1).", "cite_spans": [], "ref_spans": [{"start": 102, "end": 104, "text": "5e", "ref_id": "FIGREF4"}, {"start": 968, "end": 970, "text": "5f", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "Design optimization. We investigate the potential of our FQO-UNet to be used for optimizing a beading pattern for reduced vibrations in a specified frequency range. Following the approach described in [38] , to generate plates with reduced vibrations, a diffusion model trained to generate novel beading patterns is combined with gradient information from our FQO-UNet as follows: A gradient on the pixels of the input beading pattern is obtained by passing a beading pattern through the network, computing the sum of the predicted frequency response as a loss and then performing backpropagation to the input beading pattern. This gradient is then used to guide the diffusion model to generate beading patterns with reduced vibrations. We optimize beading patterns to reduce vibrations between 100 and 200 Hz using the FQO-UNet trained on the V-5000 dataset (Figure 6 ).", "cite_spans": [{"start": 201, "end": 205, "text": "[38]", "ref_id": "BIBREF37"}], "ref_spans": [{"start": 867, "end": 868, "text": "6", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "Resulting plates have a lower mean frequency response in the targeted range than any plate in the training dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "Acoustics. While research on surrogate models for the spatio-temporal evolution of vector fields is fairly common [39, 40, 41] , directly predicting frequency responses through neural networks is an understudied problem. A general CNN architecture is applied in [42] to calibrate the parameters of an analytical model for a composite column on a shake table. The data includes spectrograms representing the structural response in time-frequency domain. The frequency-domain response of acoustic metamaterials is considered in a material design task by conditional generative adversarial networks or reinforcement learning [43, 44, 45] . The frequency response of a multi-mass oscillator is predicted with transformer-based methods [46] . Within the context of aeroacoustics, the propagation of a two-dimensional acoustic wave while considering sound-scattering obstacles is predicted in time-domain by a CNN [47, 48] . A review of machine learning in acoustics is given by [49] . Several acoustic benchmarks for numerical methods are available [50] , however, these benchmarks do not systematically vary input geometries, making them not directly applicable to data-driven models.", "cite_spans": [{"start": 114, "end": 118, "text": "[39,", "ref_id": "BIBREF38"}, {"start": 119, "end": 122, "text": "40,", "ref_id": "BIBREF39"}, {"start": 123, "end": 126, "text": "41]", "ref_id": "BIBREF40"}, {"start": 262, "end": 266, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 622, "end": 626, "text": "[43,", "ref_id": "BIBREF42"}, {"start": 627, "end": 630, "text": "44,", "ref_id": "BIBREF43"}, {"start": 631, "end": 634, "text": "45]", "ref_id": "BIBREF44"}, {"start": 731, "end": 735, "text": "[46]", "ref_id": "BIBREF45"}, {"start": 908, "end": 912, "text": "[47,", "ref_id": "BIBREF46"}, {"start": 913, "end": 916, "text": "48]", "ref_id": "BIBREF47"}, {"start": 973, "end": 977, "text": "[49]", "ref_id": "BIBREF48"}, {"start": 1044, "end": 1048, "text": "[50]", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "5"}, {"text": "Scientific machine learning. Data-driven machine learning techniques were successfully applied in many different disciplines within engineering and applied science; for example for alloy discovery [51] , crystal structure prediction [52] , climate modeling [53] and protein folding [54] . A popular use case for data-driven methods is to accelerate fluid dynamics, governed by the Navier-Stokes equations [39, 40, 55, 56, 57] .", "cite_spans": [{"start": 197, "end": 201, "text": "[51]", "ref_id": "BIBREF50"}, {"start": 233, "end": 237, "text": "[52]", "ref_id": "BIBREF51"}, {"start": 257, "end": 261, "text": "[53]", "ref_id": "BIBREF52"}, {"start": 282, "end": 286, "text": "[54]", "ref_id": "BIBREF53"}, {"start": 405, "end": 409, "text": "[39,", "ref_id": "BIBREF38"}, {"start": 410, "end": 413, "text": "40,", "ref_id": "BIBREF39"}, {"start": 414, "end": 417, "text": "55,", "ref_id": "BIBREF54"}, {"start": 418, "end": 421, "text": "56,", "ref_id": "BIBREF55"}, {"start": 422, "end": 425, "text": "57]", "ref_id": "BIBREF56"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "5"}, {"text": "The question of how to structure and train neural networks for predicting the solution of partial differential equations (PDE) has been the topic of intense research. Many methods investigate the inclusion of physics informed loss terms [58, 59, 60, 56, 61] . Some methods directly solve PDEs with neural networks as a surrogate model [62, 63] . Graph neural networks are often employed, e.g. for interaction of rigid and deformable objects as well as fluids [64, 65] .", "cite_spans": [{"start": 237, "end": 241, "text": "[58,", "ref_id": "BIBREF57"}, {"start": 242, "end": 245, "text": "59,", "ref_id": "BIBREF58"}, {"start": 246, "end": 249, "text": "60,", "ref_id": "BIBREF59"}, {"start": 250, "end": 253, "text": "56,", "ref_id": "BIBREF55"}, {"start": 254, "end": 257, "text": "61]", "ref_id": "BIBREF60"}, {"start": 335, "end": 339, "text": "[62,", "ref_id": "BIBREF61"}, {"start": 340, "end": 343, "text": "63]", "ref_id": "BIBREF62"}, {"start": 459, "end": 463, "text": "[64,", "ref_id": "BIBREF63"}, {"start": 464, "end": 467, "text": "65]", "ref_id": "BIBREF64"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "5"}, {"text": "Operator learning and implicit models. A promising avenue of research for incorporating inductive biases for physical models has been operator learning [11, 15, 66, 37, 41] . Operator learning structures neural networks such that they implement a function that can be evaluated at real values instead of a fixed discrete grid. DeepONet [11] implements operator learning by taking the value at which it is evaluated as an input and processes this value in a separate branch. Fourier Neural Operators [15] use a point-wise mapping to a latent space which is processed through a sequence of individual layers in Fourier space before being projected to the output space. Implicit models (or coordinate-based representation) are models where location is utilized as an input to obtain a location-specific prediction, instead of predicting the entire grid at once and thus fit in the operator learning paradigm. Such models were used to represent shapes [12, 67, 68, 13] , later their representations were improved [69, 70] and adapted for representing neural radiance fields (NeRFs) [71, 14] . Our method applies techniques from these implicit models to operator learning.", "cite_spans": [{"start": 152, "end": 156, "text": "[11,", "ref_id": "BIBREF10"}, {"start": 157, "end": 160, "text": "15,", "ref_id": "BIBREF14"}, {"start": 161, "end": 164, "text": "66,", "ref_id": "BIBREF65"}, {"start": 165, "end": 168, "text": "37,", "ref_id": "BIBREF36"}, {"start": 169, "end": 172, "text": "41]", "ref_id": "BIBREF40"}, {"start": 336, "end": 340, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 499, "end": 503, "text": "[15]", "ref_id": "BIBREF14"}, {"start": 948, "end": 952, "text": "[12,", "ref_id": "BIBREF11"}, {"start": 953, "end": 956, "text": "67,", "ref_id": "BIBREF66"}, {"start": 957, "end": 960, "text": "68,", "ref_id": "BIBREF67"}, {"start": 961, "end": 964, "text": "13]", "ref_id": "BIBREF12"}, {"start": 1009, "end": 1013, "text": "[69,", "ref_id": "BIBREF68"}, {"start": 1014, "end": 1017, "text": "70]", "ref_id": "BIBREF69"}, {"start": 1078, "end": 1082, "text": "[71,", "ref_id": "BIBREF70"}, {"start": 1083, "end": 1086, "text": "14]", "ref_id": "BIBREF13"}], "ref_spans": [], "eq_spans": [], "section": "Related Work", "sec_num": "5"}, {"text": "We introduced the problem of predicting structural vibrations and associated frequency response functions of mechanical systems. Unlike other benchmarks for deep learning surrogate models, this task necessitates predicting a steady-state solution that remains constant over time, but varies across different excitation frequencies. To this end, we created the Vibrating Plates dataset and benchmark and provide reference scores for several methods. Our Frequency-Query Operator method addresses the benchmark and achieves better results than the DeepONet and FNO baselines. We find that query-based approaches and the indirect prediction of a mean frequency response through predicted field quantities lead to better results. Surrogate models as shown in this work can greatly accelerate the prediction of physical quantities over the finite element method: Our models achieved a speed-up of around 4 to 6 orders of magnitude (see Appendix C), which makes tasks such as design optimization feasible. This efficiency, however, depends on the availability of enough pre-generated training data and requires model training. We further investigated effects of changing the composition of the training dataset and found that using less frequencies per plate and more different plates positively impacts prediction accuracy.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Limitations and future work. Our dataset and method serve as an initial step in the development of surrogate models for vibration prediction. The dataset focuses on plates, a common geometric primitive used in a great number of applications. However, many structures beyond plates exist, involving curved shells, multi-component geometries and complex material parameters. While some results from our study might transfer to these cases, more flexible architectures, able to deal with 3D data, would be needed. Different mechanical models, might also produce more complex frequency responses with e.g. more closely spaced modes, making the prediction task more challenging. As more complex geometries incur higher computational costs of FEM simulations, key questions are how to enhance sample-efficiency further, for example through transfer learning. A further limitation is the manufacturability of the considered beading patterns. The plate beadings could in principle be manufactured by deep drawing of sheet metal, but would require specifically designed stamps.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "2353: Daring More Intelligence -Design Assistants in Mechanics and Dynamics'. The authors gratefully acknowledge the computing time made available to them on the high-performance computers HLRN-IV at GWDG at the NHR Centers NHR@Göttingen. These centers are jointly supported by the German Federal Ministry of Education and Research and the German state governments participating in the NHR (www.nhr-verein.de/unsere-partner).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "In the following, we give a technical description of the mechanical model that is applied to generate the datasets. For moderately thin plates, the plate theory by <PERSON><PERSON> is a valid differential equation [18] :", "cite_spans": [{"start": 205, "end": 209, "text": "[18]", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "A.1 The Mechanical Model", "sec_num": null}, {"text": "B ∇ 4 u z -ω 2 ρ s h u z + ω 2 Bρ s G + ρ s I ∇ 2 u z + ω 4 I ρ 2 s G u z = p l", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1 The Mechanical Model", "sec_num": null}, {"text": "This equation is combined with a disc formulation for in-plane loads in order to receive a shell formulation for the mechanical description of arbitrarily formed moderately thin structures considering in-plane and transverse loads. The plate part is the dominating and important part for resolving bending waves. In the equation, u z denotes the normal displacement of the plate structure as degree of freedom of interest. B represents the bending stiffness, ρ s the density, h the thickness, G the shear modulus and I the moment of inertia. The angular frequency ω is defined as ω = 2πf . The right hand-side excitation p l describes an applied pressure load, which is converted to point forces through integration. As boundary conditions we apply homogeneous dirichlet boundary conditions, i.e. u z (x) = 0 on the boundary and include a rotational stiffness at the boundary to model different boundary conditions, ranging from free rotating to clamped plates. The equation is transformed into a weak integral formulation by weighted residuals, discretized using finite elements and integrated numerically. In particular, we use triangular shell elements with 3 nodes and linear ansatz functions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1 The Mechanical Model", "sec_num": null}, {"text": "The integration delivers the sparse linear system of equations. This linear system is solved using the direct solver MUMPS [72] with a specialized FEM implementation for acoustics [24] . The discretization is chosen, such that the bending waves are resolved with a minimum of 10 nodes. The bending wave length λ B of a plate can be calculated by", "cite_spans": [{"start": 123, "end": 127, "text": "[72]", "ref_id": "BIBREF71"}, {"start": 180, "end": 184, "text": "[24]", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "A.1 The Mechanical Model", "sec_num": null}, {"text": "λ B = 2π f 4 Et 2 12(1 -ν 2 )ρ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1 The Mechanical Model", "sec_num": null}, {"text": ",", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1 The Mechanical Model", "sec_num": null}, {"text": "where E is the Young's modulus, t the thickness, ν the Poisson ratio and ρ the density of the plate. The final discretization is set to 181 × 121 for G-5000 and 121 × 81 for V-5000, which is sufficient for convergence.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1 The Mechanical Model", "sec_num": null}, {"text": "The exact physical setting and variation of our mechanical model to form the V-5000 and G-5000 datasets are given in Table 4 , Table 6 and Table 5 . Both dataset settings contain 6000 samples, each consisting of a plate geometry with associated physical and material parameters and the computed velocity fields V(f ) and frequency response F(f ) for frequencies f 1 to 300 Hz. 1000 samples from the 6000 samples are selected as a test dataset and not considered during neural network training or validation. Computing a single sample out of the 6000 samples takes 2 minutes and 19 seconds on a machine with a 2 Ghz CPU (20 physical cores). For the G-5000 dataset, the geometry, material, boundary condition and loading parameters are varied. The effect of two of the material parameters is visualized in Figure 7 . Increasing the damping reduces amplitudes at resonance peaks but does not shift the overall form of the frequency response.", "cite_spans": [], "ref_spans": [{"start": 123, "end": 124, "text": "4", "ref_id": "TABREF4"}, {"start": 133, "end": 134, "text": "6", "ref_id": "TABREF6"}, {"start": 145, "end": 146, "text": "5", "ref_id": "TABREF5"}, {"start": 811, "end": 812, "text": "7", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "A.2 Datasets", "sec_num": null}, {"text": "Increasing the thickness increases the overall stiffness and shifts resonance peaks towards higher frequencies in a less regular manner. For boundary condition variation, we include one rotational stiffness parameter, which models the rotational stiffness along the x-axis at the lower and upper edge and along the y-axis at the left and right edge. The rotational stiffness is added at the respective rotational degree of freedom at the boundaries and varied as given in Table 6 . Increasing the damping reduces the amplitudes at the resonance peaks. Increasing the plate thickness increases the stiffness of the plate and thus shifts the resonance peaks towards higher frequencies", "cite_spans": [], "ref_spans": [{"start": 478, "end": 479, "text": "6", "ref_id": "TABREF6"}], "eq_spans": [], "section": "A.2 Datasets", "sec_num": null}, {"text": "We provide examples of the find peak operation which serves as the basis for the peak frequency error on ground truth (Fig. 8 ) and predictions (Fig. 9 , using a kNN baseline) and visualize the matched peaks for calculating the peak frequency error (Fig. 10 ). Note that find peaks is run with the prominence threshold set to 0.5 meaning that the peak must be at least 0.5 units higher than their surroundings. ", "cite_spans": [], "ref_spans": [{"start": 124, "end": 125, "text": "8", "ref_id": null}, {"start": 150, "end": 151, "text": "9", "ref_id": null}, {"start": 255, "end": 257, "text": "10", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "B Metrics -Peak Frequency Error", "sec_num": null}, {"text": "In the following, our neural network architectures and the training procedure are described. The training and neural networks can be reproduced based on the publicly available code repository. To give an overall impression of the employed models, Table 7 gives an overview of the number of parameters and the speed for a forward pass prediction. Predicting F(f ) directly: FQO-RN18 and FQO-ViT. To directly predict the frequency response instead of predicting the velocity fields and then transforming it to the frequency response, we use a ResNet [31] and a vision transformer (ViT) [32] as geometry encoders. For the ResNet, we opt for the ResNet18 backbone. We replace batch normalization with layer normalization [35] , as we found this to work substantially better. In addition, we employ the vision transformer (ViT) architecture [32] . The ViT supports interactions across different image regions in early layers. We use a variation of the ViT-Base configuration with a reduced token size of 192, an intermediate size of 768 and three attention heads. For both the RN18 and the ViT encoder, we obtain the d-dimensional global feature x through average pooling from the last feature map or the encoded tokens. Scalar parameters are introduced the encoding by a film layer. As a decoder, we employ an MLP. The MLP r takes both x and a scalar frequency value f as input to predict the response for that specific query frequency, i.e. r(x, f ) ∈ R. The frequency query is introduced by a film layer to x. By querying the decoder with all frequencies individually, we obtain results for the frequency band between 1 and 300 Hz. The MLP has six hidden layers with 512 dimensions each and ReLU activation functions.", "cite_spans": [{"start": 548, "end": 552, "text": "[31]", "ref_id": "BIBREF30"}, {"start": 584, "end": 588, "text": "[32]", "ref_id": "BIBREF31"}, {"start": 717, "end": 721, "text": "[35]", "ref_id": null}, {"start": 836, "end": 840, "text": "[32]", "ref_id": "BIBREF31"}], "ref_spans": [{"start": 253, "end": 254, "text": "7", "ref_id": "TABREF7"}], "eq_spans": [], "section": "C Architectures", "sec_num": null}, {"text": "Predicting F(f ) through the velocity field V(f ): FQO-UNet. To predict V(f ) instead of directly predicting F(f ), we employ a UNet. The plate geometry is encoded by the UNet encoder and the scalar material parameters are introduced before the last contraction block of the UNet. A frequency query is introduced after the encoder again by a film layer and the decoder then produces predictions of size 40 × 60, which is sufficient to capture the structure and modes of the velocity fields. Since the decoder has to be evaluated for each frequency query individually, we opt to map to predictions for five velocity fields per query. The UNet consists of three contraction blocks, two spatial-shape-preserving blocks and two expansion blocks. Additionally, two self-attention layers are included in the encoder and one self attention layer in the decoder. To ensure global features are included in the full feature volume after the encoder, adaptive average pooling is applied to the feature volume and the result is concatenated to the feature volume.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Architectures", "sec_num": null}, {"text": "To provide a direct comparison to the query-based approach, two methods that predict all frequency responses at once are tested.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Grid-based Methods", "sec_num": null}, {"text": "Grid-RN18. The same RN18 is used to generate a global feature x as in the FQO-RN18. Given x, an MLP r predicts the frequency response on a fixed 1 Hz interval grid, with r(x) ∈ R 300 . We employ six hidden layers with 512 dimensions each and ReLU activations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Grid-based Methods", "sec_num": null}, {"text": "Grid-based U-Net. For the grid-based U-Net we also employ the same architecture as for the query-variation but double the number of channels to account for the larger number of predictions that the network has to produce at once. The U-Net is trained to predict all 300 velocity fields at once.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Grid-based Methods", "sec_num": null}, {"text": "RN18 + Fourier Neural Operator (FNO). A 1d FNO as constructed by [15] takes as input x processed by a linear layer to size 300, the number of frequencies to be predicted. We keep 32 modes and use eight FNO blocks with 128 hidden channels.", "cite_spans": [{"start": 65, "end": 69, "text": "[15]", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "C.3 Baseline Methods", "sec_num": null}, {"text": "DeepONet. We further test a DeepONet with the RN18 as branch network and as trunk network, a four layer MLP of width 128 and 512 as output width to match the size of x. The trunk network processes the frequency queries and is then combined with x to produce the prediction [11, 66] . Note, the RN18 branch network is the same as the encoder of the FQO-RN18.", "cite_spans": [{"start": 273, "end": 277, "text": "[11,", "ref_id": "BIBREF10"}, {"start": 278, "end": 281, "text": "66]", "ref_id": "BIBREF65"}], "ref_spans": [], "eq_spans": [], "section": "C.3 Baseline Methods", "sec_num": null}, {"text": "The FNO takes as input the geometry interpolated to the resolution 40×60. The 2d FNO then consists of eight FNO blocks with 128 hidden channels and finally 300 output channels to map to the 300 velocity fields in this resolution. In the FNO blocks, 32 modes are preserved after the Fourier transform. Scalar parameters are introduced by a film layer after the first FNO block.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FNO (velocity fields).", "sec_num": null}, {"text": "We further test a k-Nearest Neighbors algorithm as a baseline, which predicts the frequency response of a plate as the mean frequency response of the k closest plates in the training set. To determine the distance between different plate designs, we use the cosine distance on the 96-dimensional latent space of a convolutional autoencoder [73] trained on the beading pattern geometries. The normalized scalar properties are appended to the latent space to include them. To obtain a prediction, the frequency responses of the k neighbors are averaged and the optimal k in the range [1, 25] is empirically determined to minimize the MSE. Determining the nearest neighbors directly in the geometry space was tried out, but yielded worse results.", "cite_spans": [{"start": 340, "end": 344, "text": "[73]", "ref_id": "BIBREF72"}, {"start": 582, "end": 585, "text": "[1,", "ref_id": "BIBREF0"}, {"start": 586, "end": 589, "text": "25]", "ref_id": "BIBREF24"}], "ref_spans": [], "eq_spans": [], "section": "C.4 k-Nearest Neighbors (k-NN)", "sec_num": null}, {"text": "The networks are trained using the AdamW optimizer [74] , with β = [0.9, 0.99] and weight decay set to 0.00005. We further choose a cosine learning rate schedule with a warm-up period [75] of 50 epochs. The maximum learning rate is set to 0.001, except for the UNet and ViT architectures, for which it is set to 0.0005. In total, the networks are trained for 500 epochs. As a validation set, 500 samples from the training dataset are set and excluded from the training and the checkpoint with the lowest MSE on these samples is selected. We report evaluation results on the previously unseen test set.", "cite_spans": [{"start": 51, "end": 55, "text": "[74]", "ref_id": "BIBREF73"}, {"start": 184, "end": 188, "text": "[75]", "ref_id": "BIBREF74"}], "ref_spans": [], "eq_spans": [], "section": "C.5 Training", "sec_num": null}, {"text": "Compute resources. All trainings reported in this work were computed on a cluster with single A100 GPUs. The most resource intense training run took roughly 1d and 16h on a single A100 GPU and was the ablation of the number of channels with the highest scaling factor for the FQO-UNet method detailed in Section D. All other training runs with the FQO-UNet were completed in less than 24h. The trainings for the other methods were substantially shorter with i.e. the Grid-UNet finishing training in roughly 2h -3h and likewise the FNO (velocity field) method. The roughly 20 to 30 training runs for the FQO-UNet dominate the required compute resources. We estimate that it took in total 1 A100 for 30 days. In addition, preliminary and failed experiments required further computational resources.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.5 Training", "sec_num": null}, {"text": "We provide ablation results for the loss function for training methods that predict the velocity field. We consider the loss function L total = αL V + (1 -α)L F and provide results in Table 8 . This ablation was performed with training batches consisting of 300 frequencies per geometry, instead of a subset. We find that the loss on the velocity field prediction L V is more important than the loss on the frequency response. We provide ablation results on the number of channels in the FQO-UNet and Grid-UNet architectures in Table 9 . For the FQO-UNet, this ablation was performed with training batches consisting of 300 frequencies per geometry, instead of a subset. We find that increasing the number of channels did not lead to a perfomance improvement for the Grid-UNet and leads to marginal further improvements for the FQO-UNet architecture. ", "cite_spans": [], "ref_spans": [{"start": 190, "end": 191, "text": "8", "ref_id": "TABREF8"}, {"start": 534, "end": 535, "text": "9", "ref_id": "TABREF9"}], "eq_spans": [], "section": "D Ablations", "sec_num": null}, {"text": "To provide full baseline results for the training with a reduced amount of samples, we refer to Table 10 .", "cite_spans": [], "ref_spans": [{"start": 102, "end": 104, "text": "10", "ref_id": "TABREF0"}], "eq_spans": [], "section": "E.1 Sample Efficiency", "sec_num": null}, {"text": "Table 10 : Test results for different training dataset sizes for both settings, V-5000 and G-5000.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 8, "text": "10", "ref_id": "TABREF0"}], "eq_spans": [], "section": "E.1 Sample Efficiency", "sec_num": null}, {"text": "V-5000 G-5000 Full results on training with different ratios of frequencies and geometries are provided in Table 11 . ", "cite_spans": [], "ref_spans": [{"start": 113, "end": 115, "text": "11", "ref_id": "TABREF11"}], "eq_spans": [], "section": "E.1 Sample Efficiency", "sec_num": null}, {"text": "EMSE", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.1 Sample Efficiency", "sec_num": null}, {"text": "To provide a notion of the variability of the results for different training runs, we performed four trainings for the FQO-UNet with different initial seeds and different random splits in training and validation set. These trainings were performed with training batches consisting of all 300 frequencies per geometry, instead of a subset. In Table 12 we report evaluation results on the respective validation sets and on the unseen test set and observe only modest variation. Answer: [NA] Justification: This paper does not include theoretical results, as it mainly deals with applicational issues.", "cite_spans": [{"start": 484, "end": 488, "text": "[NA]", "ref_id": null}], "ref_spans": [{"start": 348, "end": 350, "text": "12", "ref_id": "TABREF12"}], "eq_spans": [], "section": "E.2 Multiple Trainings with Random Splits", "sec_num": null}, {"text": "• The answer NA means that the paper does not include theoretical results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems. • The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Answer: [Yes] Justification: Experimental reproducibility is guaranteed by providing code and data. Further, we included several sections in the appendix, giving concrete architectural and training details and describing the simulation setup to obtain our dataset. Evaluating design optimization results requires proprietary Finite-Element simulation software.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. For example (a) If the contribution is primarily a new algorithm, the paper should make it clear how to reproduce that algorithm. (b) If the contribution is primarily a new model architecture, the paper should describe the architecture clearly and fully. (c) If the contribution is a new model (e.g., a large language model), then there should either be a way to access this model for reproducing the results or a way to reproduce the model (e.g., with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "Answer: [Yes] Justification: The link to code and data is included directly in the abstract. It is https: //github.com/ecker-lab/Learning_Vibrating_Plates Guidelines:", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "• The answer NA means that paper does not include experiments requiring code. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Answer: [Yes] Justification: In Appendix E.2 mean and standard deviation results for four training runs with different seeds and training and validation splits are reported, giving a notion of variability.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Answer: [Yes] Justification: Information on compute resources for deep learning is provided in Section C.5. Information on compute resources for the dataset generation is provided in Section A.2.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "• The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Justification: Our research conforms with the Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer: [Yes]", "sec_num": null}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer: [Yes]", "sec_num": null}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer: [Yes]", "sec_num": null}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer: [Yes]", "sec_num": null}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [NA] Justification: Including deep learning in the engineering pipeline for actual applications is at a very preliminary stage.", "cite_spans": [{"start": 8, "end": 12, "text": "[NA]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA] Justification: Our data and models do not have a high risk for misuse.", "cite_spans": [{"start": 8, "end": 12, "text": "[NA]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA] Justification: This work does not use existing assets, save for publically available codebases like pytorch.", "cite_spans": [{"start": 8, "end": 12, "text": "[NA]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}], "back_matter": [{"text": "Acknowledgements. This research is funded by the Deutsche Forschungsgemeinschaft (DFG, German Research Foundation), project number 501927736, within the DFG Priority Programme", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "acknowledgement", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope? Answer: [Yes] Justification: This paper introduces a dataset and method for predicting vibrations of plates. This is communicated in the abstract and introduction. Guidelines:• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "cite_spans": [{"start": 131, "end": 136, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors? Answer: [Yes] Justification: We included a limitations paragraph, detailing the limitation in scope of our data as well as our method. Computational efficiency is also discussed and numerical values are given in the Appendix. Guidelines:• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach. For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Auditory and non-auditory effects of noise on health", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Babisch", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Brink", "suffix": ""}, {"first": "Charlotte", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stansfeld", "suffix": ""}], "year": 2014, "venue": "The Lancet", "volume": "383", "issue": "9925", "pages": "1325--1332", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Auditory and non-auditory effects of noise on health. The Lancet, 383(9925):1325-1332, 2014.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Principles of vibration and sound", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2012, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Principles of vibration and sound. Springer Science & Business Media, 2012.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Design and placement of passive acoustic measures in early design phases", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Schriften des Instituts für Akustik. Shaker", "volume": "2", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Design and placement of passive acoustic measures in early design phases, volume 2 of Schriften des Instituts für Akustik. Shaker, Düren, Aug 2022. Dissertation, Tech- nische Universität Braunschweig, 2022.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "The finite element method: its basis and fundamentals", "authors": [{"first": "<PERSON>", "middle": [], "last": "Olek <PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["Z"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2005, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. The finite element method: its basis and fundamentals. Elsevier, 2005.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Finite element method", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2007, "venue": "", "volume": "", "issue": "", "pages": "1--12", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>. Finite element method. Wiley Encyclopedia of Computer Science and Engineering, pages 1-12, 2007.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "PDEBench: An extensive benchmark for scientific machine learning", "authors": [{"first": "Makoto", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Praditia", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "1596--1611", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. PDEBench: An extensive benchmark for scientific machine learning. Advances in Neural Information Processing Systems, 35:1596-1611, 2022.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "An Extensible Benchmark Suite for Learning to Simulate Physical Systems", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Teseo", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2108.07799"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. An Extensible Benchmark Suite for Learning to Simulate Physical Systems. arXiv preprint arXiv:2108.07799, 2021.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "AirfRANS: High Fidelity Computational Fluid Dynamics Dataset for Approximating Reynolds-Averaged Navier-Stokes Solutions", "authors": [{"first": "Florent", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "23463--23478", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>lore<PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. AirfRANS: High Fi- delity Computational Fluid Dynamics Dataset for Approximating Reynolds-Averaged Navier- Stokes Solutions. Advances in Neural Information Processing Systems, 35:23463-23478, 2022.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "An adaptive sparse grid rational Arnoldi method for uncertainty quantification of dynamical systems in the frequency domain", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Journal for Numerical Methods in Engineering", "volume": "122", "issue": "20", "pages": "5487--5511", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. An adaptive sparse grid rational Arnoldi method for uncertainty quantifi- cation of dynamical systems in the frequency domain. International Journal for Numerical Methods in Engineering, 122(20):5487-5511, 2021.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Wave-resolving numerical prediction of passenger cabin noise under realistic loading", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["K"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ewert", "suffix": ""}, {"first": "Jan", "middle": ["W"], "last": "Delfs", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Fundamentals of High Lift for Future Civil Aircraft: Contributions to the Final Symposium of the Collaborative Research Center", "volume": "880", "issue": "", "pages": "231--246", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Wave-resolving numerical prediction of passenger cabin noise under realistic loading. In Fun- damentals of High Lift for Future Civil Aircraft: Contributions to the Final Symposium of the Collaborative Research Center 880, December 17-18, 2019, Braunschweig, Germany, pages 231-246, 2021.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Deeponet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Pengzhan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Em"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1910.03193"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Deeponet: Learning nonlinear operators for identifying differential equations based on the universal approximation theorem of operators. arXiv preprint arXiv:1910.03193, 2019.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Occupancy Networks: Learning 3D Reconstruction in Function Space", "authors": [{"first": "<PERSON>", "middle": ["M"], "last": "Mescheder", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "4455--4465", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> <PERSON><PERSON><PERSON>. Occupancy Networks: Learning 3D Reconstruction in Function Space. 2019 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 4455- 4465, 2018.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "PIFu: Pixel-Aligned Implicit Function for High-Resolution Clothed Human Digitization", "authors": [{"first": "Shunsuke", "middle": [], "last": "Sai<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Natsume", "suffix": ""}, {"first": "Shigeo", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kanazawa", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "Li", "suffix": ""}], "year": 2019, "venue": "IEEE/CVF International Conference on Computer Vision (ICCV)", "volume": "", "issue": "", "pages": "2304--2314", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. PIFu: Pixel-Aligned Implicit Function for High-Resolution Clothed Human Digiti- zation. 2019 IEEE/CVF International Conference on Computer Vision (ICCV), pages 2304- 2314, 2019.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Neural Radiance Fields from One or Few Images. 2021 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ye", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kanazawa", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "4576--4585", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. pixelNeRF: Neural Radiance Fields from One or Few Images. 2021 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 4576-4585, 2020.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Fourier Neural Operator for Parametric Partial Differential Equations", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Nikola", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Burigede", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.08895"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Fourier Neural Operator for Parametric Partial Dif- ferential Equations. arXiv preprint arXiv:2010.08895, 2020.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Topography optimization of an enclosure panel for low-frequency noise and vibration reduction using the equivalent radiated power approach", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Can", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Materials & Design", "volume": "183", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Topography optimization of an enclosure panel for low-frequency noise and vibration reduction using the equivalent radiated power approach. Materials & Design, 183:108125, 2019.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Cabinet design for vibration reduction of a drum type washing machine", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Hee", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Journal of the Korean Society for Precision Engineering", "volume": "33", "issue": "9", "pages": "731--737", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Cabinet design for vibration reduction of a drum type washing machine. Journal of the Korean Society for Precision Engineering, 33(9):731-737, 2016.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Influence of Rotatory Inertia and Shear on Flexural Motions of Isotropic, Elastic Plates", "authors": [{"first": "R", "middle": ["D"], "last": "Mindlin", "suffix": ""}], "year": 1951, "venue": "Journal of Applied Mechanics", "volume": "18", "issue": "1", "pages": "31--38", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> <PERSON><PERSON>. Influence of Rotatory Inertia and <PERSON><PERSON> on Flexural Motions of Isotropic, Elastic Plates. Journal of Applied Mechanics, 18(1):31-38, 1951.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "An efficient standard plate theory", "authors": [{"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1991, "venue": "International journal of engineering science", "volume": "29", "issue": "8", "pages": "901--916", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. An efficient standard plate theory. International journal of engineering science, 29(8):901-916, 1991.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Thin plates and shells: theory, analysis, and applications", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Carrera", "suffix": ""}], "year": 2002, "venue": "Appl. Mech. Rev", "volume": "55", "issue": "4", "pages": "72--B73", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Thin plates and shells: theory, analysis, and applications. Appl. Mech. Rev., 55(4):B72-B73, 2002.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Analysis of Shells, Plates and Beams", "authors": [{"first": "Natalia", "middle": [], "last": "Holm Altenbach", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["H"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Analysis of Shells, Plates and Beams. Springer, 2020.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Formulation and validation of dynamical models for narrow plate motion", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "White", "suffix": ""}], "year": 1993, "venue": "Applied mathematics and computation", "volume": "58", "issue": "2-3", "pages": "103--141", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Formulation and validation of dynamical models for narrow plate motion. Applied mathematics and computation, 58(2-3):103-141, 1993.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Finite element and boundary methods in structural acoustics and vibration", "authors": [{"first": "Noureddine", "middle": [], "last": "<PERSON>all<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sgard", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Finite element and boundary methods in structural acoustics and vibration. CRC Press, 2015.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "elPaSo Core -Elementary parallel solver core module for high performance vibroacoustic simulations", "authors": [{"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>. elPaSo Core -Elementary parallel solver core module for high performance vibroacoustic simulations, 2023.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Fast and robust earth mover's distances", "authors": [{"first": "Ofir", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "2009 IEEE 12th International Conference on Computer Vision", "volume": "", "issue": "", "pages": "460--467", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Fast and robust earth mover's distances. In 2009 IEEE 12th International Conference on Computer Vision, pages 460-467, September 2009.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "The earth mover's distance as a metric for image retrieval", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Carlo", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Leonidas", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2000, "venue": "International Journal of Computer Vision", "volume": "40", "issue": "2", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. The earth mover's distance as a metric for image retrieval. International Journal of Computer Vision, 40(2):99, 2000.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "<PERSON><PERSON> processes over denumerable products of spaces, describing large systems of automata", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 1969, "venue": "Problemy <PERSON>chi Informatsii", "volume": "5", "issue": "3", "pages": "64--72", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>. <PERSON><PERSON> processes over denumerable products of spaces, de- scribing large systems of automata. Problemy Peredachi Informatsii, 5(3):64-72, 1969.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Sinkhorn distances: Lightspeed computation of optimal transport", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "Advances in Neural Information Processing Systems", "volume": "26", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Sinkhorn distances: Lightspeed computation of optimal transport. Advances in Neural Information Processing Systems, 26, 2013.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "SciPy 1.0: fundamental algorithms for scientific computing in Python", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gommers", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Haberland", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Nature Methods", "volume": "17", "issue": "3", "pages": "261--272", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. SciPy 1.0: fundamental algorithms for scientific computing in Python. Nature Methods, 17(3):261-272, 2020.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "The Hungarian method for the assignment problem", "authors": [{"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1955, "venue": "Naval Research Logistics Quarterly", "volume": "2", "issue": "1-2", "pages": "83--97", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. The Hungarian method for the assignment problem. Naval Research Logistics Quarterly, 2(1-2):83-97, 1955.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Deep residual learning for image recognition", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "Xiangyu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shaoqing", "middle": [], "last": "Ren", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}], "year": 2016, "venue": "Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "770--778", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep residual learning for image recognition. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recog- nition, pages 770-778, 2016.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "Xiaohua", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.11929"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint arXiv:2010.11929, 2020.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Attention is all you need", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "Llion", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "Illia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in Neural Information Processing Systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. Advances in Neural Information Processing Systems, 30, 2017.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "U-net: Convolutional networks for biomedical image segmentation", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Brox", "suffix": ""}], "year": 2015, "venue": "International Conference on Medical Image Computing and Computer-Assisted Intervention", "volume": "", "issue": "", "pages": "234--241", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. U-net: Convolutional networks for biomedical image segmentation. In International Conference on Medical Image Computing and Computer-Assisted Intervention, pages 234-241, 2015.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Film: Visual reasoning with a general conditioning layer", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Strub", "suffix": ""}, {"first": "<PERSON>rm", "middle": ["De"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Courville", "suffix": ""}], "year": 2018, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "32", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Film: Visual reasoning with a general conditioning layer. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 32, 2018.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "NOMAD: Nonlinear Manifold Decoders for Operator Learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Paris", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2206.03551"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. NOMAD: Nonlin- ear Manifold Decoders for Operator Learning. arXiv preprint arXiv:2206.03551, 2022.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Minimizing structural vibrations via guided diffusion design optimization", "authors": [{"first": "Jan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "ICLR 2024 Workshop on AI4DifferentialEquations In Science", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Min- imizing structural vibrations via guided diffusion design optimization. In ICLR 2024 Workshop on AI4DifferentialEquations In Science, 2024.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Machine learning for fluid mechanics", "authors": [{"first": "<PERSON><PERSON>", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Noack", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Annual Review of Fluid Mechanics", "volume": "52", "issue": "", "pages": "477--508", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Machine learning for fluid mechanics. Annual Review of Fluid Mechanics, 52:477-508, 2020.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Machine learning-accelerated computational fluid dynamics", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Qing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["P"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the National Academy of Sciences of the United States of America", "volume": "118", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Machine learning-accelerated computational fluid dynamics. Proceedings of the Na- tional Academy of Sciences of the United States of America, 118, 2021.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Neural Operator: Learning Maps Between Function Spaces With Applications to PDEs", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Burigede", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Journal of Machine Learning Research", "volume": "24", "issue": "89", "pages": "1--97", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Neural Operator: Learning Maps Be- tween Function Spaces With Applications to PDEs. Journal of Machine Learning Research, 24(89):1-97, 2023.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Applicability of Convolutional Neural Networks for Calibration of Nonlinear Dynamic Models of Structures", "authors": [{"first": "<PERSON>", "middle": [], "last": "Lanning", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["E"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Tao", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Frontiers in Built Environment", "volume": "8", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Applicability of Convolutional Neural Net- works for Calibration of Nonlinear Dynamic Models of Structures. Frontiers in Built Environ- ment, 8, 2022.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Generative adversarial networks for the design of acoustic metamaterials", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Gurbuz", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Kronow<PERSON>r", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Steffen", "middle": [], "last": "Marburg", "suffix": ""}], "year": 2021, "venue": "", "volume": "149", "issue": "", "pages": "1162--1174", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Generative adversarial networks for the design of acoustic metamaterials. The Jour- nal of the Acoustical Society of America, 149(2):1162-1174, 2021.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Reinforcement learning applied to metamaterial design", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Linwei", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "De", "suffix": ""}, {"first": "La", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gerstoft", "suffix": ""}], "year": 2021, "venue": "The Journal of the Acoustical Society of America", "volume": "150", "issue": "1", "pages": "321--338", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Reinforcement learning applied to metamaterial design. The Journal of the Acoustical Society of America, 150(1):321-338, 2021.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Conditional Wasserstein generative adversarial networks applied to acoustic metamaterial design", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gerstoft", "suffix": ""}], "year": 2021, "venue": "The Journal of the Acoustical Society of America", "volume": "150", "issue": "6", "pages": "4362--4374", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Conditional Wasserstein generative adver- sarial networks applied to acoustic metamaterial design. The Journal of the Acoustical Society of America, 150(6):4362-4374, 2021.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Deep learning for frequency response prediction of a multimass oscillator", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "PAMM", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Deep learning for frequency response prediction of a multimass oscillator. PAMM, page e202300091, 2023.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Predicting the propagation of acoustic waves using deep convolutional neural networks", "authors": [{"first": "Antonio", "middle": [], "last": "Alguacil", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Bauerheim", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Journal of Sound and Vibration", "volume": "512", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Predicting the propagation of acoustic waves using deep convolutional neural networks. Journal of Sound and Vibration, 512:116285, 2021.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Deep Learning Surrogate for the Temporal Propagation and Scattering of Acoustic Waves", "authors": [{"first": "Antonio", "middle": [], "last": "Alguacil", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bauerheim", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "AIAA Journal", "volume": "60", "issue": "10", "pages": "5890--5906", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Deep Learning Surrogate for the Temporal Propagation and Scattering of Acoustic Waves. AIAA Journal, 60(10):5890-5906, 2022.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Machine learning in acoustics: Theory and applications", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bianco", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gerstoft", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "R<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "The Journal of the Acoustical Society of America", "volume": "146", "issue": "5", "pages": "3590--3628", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Machine learning in acoustics: Theory and applications. The Journal of the Acoustical Society of America, 146(5):3590-3628, 2019.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "A platform for benchmark cases in computational acoustics", "authors": [{"first": "Ma<PERSON>n", "middle": [], "last": "Hornikx", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Steffen", "middle": [], "last": "Marburg", "suffix": ""}], "year": 2015, "venue": "Acta Acustica united with Acustica", "volume": "101", "issue": "4", "pages": "811--820", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. A platform for benchmark cases in computational acoustics. Acta Acustica united with Acustica, 101(4):811-820, 2015.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Machine learning-enabled high-entropy alloy discovery", "authors": [{"first": "Ziyuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Po-Yen", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ye", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hongbin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ferrari", "suffix": ""}, {"first": "T", "middle": ["P C"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gutfleisch", "suffix": ""}, {"first": "Dierk", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Science", "volume": "378", "issue": "", "pages": "78--85", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Machine learning-enabled high-entropy alloy discovery. Science, 378:78-85, 2022.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Crystal Structure Prediction via Deep Learning", "authors": [{"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Shatruk", "suffix": ""}], "year": 2018, "venue": "Journal of the American Chemical Society", "volume": "140", "issue": "", "pages": "10158--10168", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Crystal Structure Prediction via Deep Learning. Journal of the American Chemical Society, 140 32:10158-10168, 2018.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Deep learning to represent subgrid processes in climate models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the National Academy of Sciences of the United States of America", "volume": "115", "issue": "", "pages": "9684--9689", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Deep learning to represent subgrid processes in climate models. Proceedings of the National Academy of Sciences of the United States of America, 115:9684-9689, 2018.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Highly accurate protein structure prediction with AlphaFold", "authors": [{"first": "<PERSON>", "middle": ["M"], "last": "Jumper", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Green", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Tunyasuvunakool", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Augustin", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bridgland", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Romera-Paredes", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Adler", "suffix": ""}, {"first": "Stig", "middle": [], "last": "Back", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Pacholska", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Berghammer", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bodenstein", "suffix": ""}, {"first": "Oriol", "middle": [], "last": "Silver", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON>yal<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Senior", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Kavukcuoglu", "suffix": ""}, {"first": "De<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature", "volume": "596", "issue": "", "pages": "583--589", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Highly accurate protein structure prediction with AlphaFold. Nature, 596:583-589, 2021.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "CFDNet: a deep learning-based accelerator for fluid simulations", "authors": [{"first": "<PERSON>avi", "middle": [], "last": "Obiols-Sales", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Malaya", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the 34th ACM International Conference on Supercomputing", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>-<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. CFDNet: a deep learning-based accelerator for fluid simulations. Proceedings of the 34th ACM International Conference on Supercomputing, 2020.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Towards Physics-informed Deep Learning for Turbulent Flow Prediction", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Towards Physics-informed Deep Learning for Turbulent Flow Prediction. Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining, 2019.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Accelerating Eulerian Fluid Simulation With Convolutional Networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Schlachter", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "3424--3433", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Accelerating Eu- lerian Fluid Simulation With Convolutional Networks. International Conference on Machine Learning, pages 3424-3433, 2017.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Physics-informed neural networks: A deep learning framework for solving forward and inverse problems involving nonlinear partial differential equations", "authors": [{"first": "Maziar", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Paris", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Journal of Computational Physics", "volume": "378", "issue": "", "pages": "686--707", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Physics-informed neural networks: A deep learning framework for solving forward and inverse problems involving nonlinear par- tial differential equations. Journal of Computational Physics, 378:686-707, 2019.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "A physicsinformed deep learning framework for inversion and surrogate modeling in solid mechanics", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Maziar", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Computer Methods in Applied Mechanics and Engineering", "volume": "379", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. A physics- informed deep learning framework for inversion and surrogate modeling in solid mechanics. Computer Methods in Applied Mechanics and Engineering, 379:113741, 2021.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Characterizing possible failure modes in physics-informed neural networks", "authors": [{"first": "Aditi", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Shandian", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "26548--26560", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Characterizing possible failure modes in physics-informed neural networks. In <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural Information Processing Systems, volume 34, pages 26548-26560. Curran Associates, Inc., 2021.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "torchPhysics GitHub repository", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON>nk<PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON>. torchPhysics GitHub repository. https://github.com/ boschresearch/torchphysics, 2023.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "The deep Ritz method: a deep learning-based numerical algorithm for solving variational problems", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Communications in Mathematics and Statistics", "volume": "6", "issue": "1", "pages": "1--12", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> et al. The deep Ritz method: a deep learning-based numerical algorithm for solving variational problems. Communications in Mathematics and Statistics, 6(1):1-12, 2018.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Quadratic residual networks: A new class of neural networks for solving forward and inverse problems in physics involving pdes", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Bu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the 2021 SIAM International Conference on Data Mining (SDM)", "volume": "", "issue": "", "pages": "675--683", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>. Quadratic residual networks: A new class of neural networks for solving forward and inverse problems in physics involving pdes. In Proceedings of the 2021 SIAM International Conference on Data Mining (SDM), pages 675-683, 2021.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Interaction networks for learning about objects, relations and physics", "authors": [{"first": "<PERSON>", "middle": [], "last": "Battaglia", "suffix": ""}, {"first": "Razvan", "middle": [], "last": "Pa<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Advances in Neural Information Processing Systems", "volume": "29", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, et al. Interaction networks for learning about objects, relations and physics. Advances in Neural Information Processing Systems, 29, 2016.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Learning to simulate complex physics with graph networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Pfaff", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Battaglia", "suffix": ""}], "year": 2020, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "8459--8468", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Learning to simulate complex physics with graph networks. International Confer- ence on Machine Learning, pages 8459-8468, 2020.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "A comprehensive and fair comparison of two neural operators (with practical extensions) based on FAIR data", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Cai", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Zhongqiang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Em"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Computer Methods in Applied Mechanics and Engineering", "volume": "393", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. A comprehensive and fair comparison of two neural operators (with practical extensions) based on FAIR data. Computer Methods in Applied Mechanics and Engineering, 393:114778, 2022.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "Learning Implicit Fields for Generative Shape Modeling", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "5932--5941", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>. Learning Implicit Fields for Generative Shape Modeling. 2019 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 5932- 5941, 2018.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "DeepSDF: Learning Continuous Signed Distance Functions for Shape Representation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Park", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "Florence", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "Newcombe", "suffix": ""}, {"first": "S", "middle": [], "last": "Lovegrove", "suffix": ""}], "year": 2019, "venue": "IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "165--174", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. DeepSDF: Learning Continuous Signed Distance Functions for Shape Representation. 2019 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 165-174, 2019.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "Implicit neural representations with periodic activation functions", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Wetzstein", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "7462--7473", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Implicit neural representations with periodic activation functions. Advances in Neural Infor- mation Processing Systems, 33:7462-7473, 2020.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "Fourier Features Let Networks Learn High Frequency Functions in Low Dimensional Domains", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Mildenhall", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["T"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ren", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Fourier Features Let Networks Learn High Frequency Functions in Low Dimensional Domains. Advances in Neural Information Processing Systems, 2020.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "Representing Scenes as <PERSON><PERSON><PERSON><PERSON> for View Synthesis", "authors": [{"first": "<PERSON>", "middle": [], "last": "Mildenhall", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["T"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ren", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Nerf", "suffix": ""}], "year": 2021, "venue": "", "volume": "65", "issue": "", "pages": "99--106", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. NeRF: Representing Scenes as Neural Radiance Fields for View Synthesis. Communications of the ACM, 65(1):99-106, 2021.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "Mumps: a general purpose distributed memory sparse solver", "authors": [{"first": "<PERSON>", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Excellent", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2000, "venue": "International Workshop on Applied Parallel Computing", "volume": "", "issue": "", "pages": "121--130", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Mumps: a general purpose distributed memory sparse solver. In International Workshop on Applied Parallel Computing, pages 121-130. Springer, 2000.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "Reducing the dimensionality of data with neural networks", "authors": [{"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["R"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2006, "venue": "science", "volume": "313", "issue": "5786", "pages": "504--507", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON> <PERSON>. Reducing the dimensionality of data with neural networks. science, 313(5786):504-507, 2006.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "Decoupled weight decay regularization", "authors": [{"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1711.05101"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Decoupled weight decay regularization. arXiv preprint arXiv:1711.05101, 2017.", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "Stochastic gradient descent with warm restarts", "authors": [{"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Sgdr", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1608.03983"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Sgdr: Stochastic gradient descent with warm restarts. arXiv preprint arXiv:1608.03983, 2016.", "links": null}}, "ref_entries": {"FIGREF0": {"uris": null, "text": "CC-BY-SA 4, <PERSON>-<PERSON>Y 2, <PERSON>", "type_str": "figure", "num": null, "fig_num": null}, "FIGREF1": {"uris": null, "text": "Figure 1: Left: We introduce the Vibrating Plates dataset of 12,000 samples for predicting vibration patterns based on plate geometries. A harmonic force excites the plates, causing them to vibrate. The vibration patterns of the plates are obtained through numerical simulation. Diverse architectures are evaluated on the dataset. Right: Beadings are indentations and used in many vibrating technical systems. Here, on an oil filter, a washing machine and a disk drive. They increase the structural stiffness and alter the vibration.", "type_str": "figure", "num": null, "fig_num": "1"}, "FIGREF2": {"uris": null, "text": "Figure 3: Dataset analysis. (a) shows two discretized plate geometries with their corresponding frequency response, the red crosses mark the detected peaks. (b) shows the mean plate design and frequency response. (c) shows number of peaks in different dataset settings. (d) shows the distribution of the peaks over the frequencies.", "type_str": "figure", "num": null, "fig_num": "3"}, "FIGREF3": {"uris": null, "text": "(a) Plate Geometry (b) Frequency Response Prediction (c) Ground Truth Velocity Field at f (d) Predicted Velocity Field at f (e) Reducing the Dataset Size (f) Using less Freqs. per Plate", "type_str": "figure", "num": null, "fig_num": null}, "FIGREF4": {"uris": null, "text": "Figure 5: Results. (b) to (d) show the velocity field at one frequency and prediction for the plate geometry in (a) from FQO-UNet. (e) shows the test MSE for training two methods with reduced numbers of samples from V-5000. (f) shows effects of different data generation strategies. The blue line is an isoconture for a fixed compute budget of 150,000 data points, with varying number of frequencies per plate geometry. The green star represents using a larger dataset at 15 frequencies per plate (half of V-5000). The red cross represents a model trained on V-5000. Training with fewer frequencies per plate is more efficient.", "type_str": "figure", "num": null, "fig_num": "5"}, "FIGREF5": {"uris": null, "text": "Figure 6: Design optimization. Exemplary generation result with lowest mean response between 100 Hz and 200 Hz out of 32 generations (left, mean response below). Plate with lowest response out of all 5000 training examples from V-5000 (middle left). Comparison of responses from left plates (middle right). Responses from 16 generated plates (right).", "type_str": "figure", "num": null, "fig_num": "6"}, "FIGREF6": {"uris": null, "text": "Figure7: One-at-a-time parameter variation of the thickness parameter and the damping loss factor. Increasing the damping reduces the amplitudes at the resonance peaks. Increasing the plate thickness increases the stiffness of the plate and thus shifts the resonance peaks towards higher frequencies", "type_str": "figure", "num": null, "fig_num": "7"}, "FIGREF7": {"uris": null, "text": "Figure 8: Find peak results on random ground truth samples.", "type_str": "figure", "num": null, "fig_num": "8910"}, "FIGREF8": {"uris": null, "text": "velocity field for frequency 82 Predicted velocity field for frequency 107 Predicted velocity field for frequency 235 Geometry Actual velocity field for frequency 54 Actual velocity field for frequency 89 Actual velocity field for frequency velocity field for frequency 54 Predicted velocity field for frequency 89 Predicted velocity field for frequency 225 Geometry Actual velocity field for frequency 80 Actual velocity field for frequency 152 Actual velocity field for frequency velocity field for frequency 82 Predicted velocity field for frequency 151 Predicted velocity field for frequency 199 Geometry Actual velocity field for frequency 87 Actual velocity field for frequency 113 Actual velocity field for frequency velocity field for frequency 88 Predicted velocity field for frequency 112 Predicted velocity field for frequency 179", "type_str": "figure", "num": null, "fig_num": null}, "FIGREF9": {"uris": null, "text": "Figure 11: V-5000 example predictions. The velocity fields at the three peaks with the highest amplitude are shown. The plots are scaled with respect to the maximum velocity in the prediction and reference velocity field to make the differences in magnitude visible.", "type_str": "figure", "num": null, "fig_num": "11"}, "FIGREF10": {"uris": null, "text": "Figure 12: G-5000 example predictions. The velocity fields at the three peaks with the highest amplitude are shown. Empty axes indicates less than three peaks in the response. The plots are scaled with respect to the maximum velocity in the prediction and reference velocity field to make the differences in magnitude visible.", "type_str": "figure", "num": null, "fig_num": "12"}, "TABREF0": {"text": "Test results for frequency response prediction. Column VF indicates if F is indirectly predicted through the velocity field (Q3), column FQ indicates if frequency queries (Q1) are used. Q1 to Q3 refer to the model components described in Section 3.", "content": "<table><tr><td/><td/><td/><td colspan=\"2\">V-5000</td><td colspan=\"2\">G-5000</td><td/></tr><tr><td/><td colspan=\"6\">FQ VF EMSE EEMD EPEAKS EF EMSE EEMD EPEAKS</td><td>EF</td></tr><tr><td/><td/><td/><td>Baselines</td><td/><td/><td/><td/></tr><tr><td>k-NN</td><td>-</td><td>-</td><td>0.63 21.50</td><td>0.45</td><td>8.7 0.88 32.48</td><td>0.68</td><td>21.0</td></tr><tr><td>RN18 + FNO</td><td>-</td><td>-</td><td>0.42 10.76</td><td>0.34</td><td>5.6 0.28 14.12</td><td>0.21</td><td>6.1</td></tr><tr><td>DeepONet</td><td>✓</td><td>-</td><td>0.49 16.91</td><td>0.48</td><td>5.4 0.44 23.05</td><td>0.57</td><td>9.9</td></tr><tr><td>FNO (velocity field)</td><td>-</td><td>✓</td><td>0.47 13.10</td><td>0.36</td><td>6.3 0.49 21.16</td><td>0.39</td><td>10.7</td></tr><tr><td>Grid-RN18</td><td>-</td><td>-</td><td>0.44 13.29</td><td>0.36</td><td>5.4 0.30 14.95</td><td>0.26</td><td>6.5</td></tr><tr><td>FQO-RN18 (Q1)</td><td>✓</td><td>-</td><td>0.32 10.70</td><td>0.17</td><td>5.3 0.24 13.51</td><td>0.13</td><td>5.1</td></tr><tr><td>FQO-ViT (Q2)</td><td>✓</td><td>-</td><td>0.68 20.96</td><td>0.54</td><td>7.1 0.52 24.34</td><td>0.49</td><td>11.5</td></tr><tr><td>Grid-UNet</td><td>-</td><td>✓</td><td>0.19 7.57</td><td>0.24</td><td>2.7 0.17 9.41</td><td>0.14</td><td>4.6</td></tr><tr><td>FQO-UNet</td><td>✓</td><td>✓</td><td>0.08 4.24</td><td>0.07</td><td>1.7 0.11 7.47</td><td>0.08</td><td>3.1</td></tr></table>", "type_str": "table", "num": null, "html": null}, "TABREF1": {"text": "Transfer learning performance: We split V-5000 into two halves based on amount of beadings and evaluate transfer learning performance across these splits: training subset → test subset. The gray rows denote test results on the original subset that has been used for training.", "content": "<table><tr><td/><td>EMSE EEMD EPEAKS</td><td>EF</td><td>EMSE EEMD EPEAKS</td><td>EF</td></tr><tr><td colspan=\"2\">FQO-RN18 0.61 16.19 0.20</td><td>9.3</td><td>0.82 15.79 0.36</td><td>8.3</td></tr><tr><td>(origin)</td><td>0.33 10.48 0.18</td><td>5.0</td><td>0.42 12.00 0.29</td><td>5.8</td></tr><tr><td colspan=\"2\">FQO-UNet 0.39 11.17 0.21</td><td>5.6</td><td>0.54 12.02 0.25</td><td>5.5</td></tr><tr><td>(origin)</td><td>0.18 8.68 0.19</td><td>2.6</td><td>0.17 7.83 0.13</td><td>3.0</td></tr></table>", "type_str": "table", "num": null, "html": null}, "TABREF2": {"text": "A FQO-UNet is trained in parallel on batches from V-5000 and G-5000 and evaluated on the G-5000 test set. Performance increases in all metrics.", "content": "<table><tr><td/><td>EMSE</td><td colspan=\"2\">EEMD EPEAKS</td><td>EF</td></tr><tr><td>G-5000</td><td colspan=\"2\">0.111 7.47</td><td>0.079</td><td>3.1</td></tr><tr><td colspan=\"3\">G-5000 + V-5000 0.093 6.97</td><td>0.071</td><td>2.9</td></tr></table>", "type_str": "table", "num": null, "html": null}, "TABREF4": {"text": "Dataset settings. Width is the width of lines and ellipses in mm. Properties. (prop.) involves plate size, thickness, material, boundary and loading properties.", "content": "<table><tr><td/><td/><td colspan=\"2\">Sample space</td><td/><td colspan=\"2\">Sample number</td></tr><tr><td>Setting</td><td colspan=\"3\">Prop. Lines Ellipses</td><td>Width</td><td>Train</td><td>Test</td></tr><tr><td>V-5000</td><td>fix</td><td>1 -3</td><td>0 -2</td><td colspan=\"2\">30 -70 5000</td><td>1000</td></tr><tr><td>G-5000</td><td>vary</td><td>1 -3</td><td>0 -2</td><td colspan=\"2\">40 -60 5000</td><td>1000</td></tr></table>", "type_str": "table", "num": null, "html": null}, "TABREF5": {"text": "Geometry and material parameters for V-5000 and G-5000 datasets.", "content": "<table><tr><td/><td/><td>Geometry</td><td/><td/><td colspan=\"2\">Material (Aluminum)</td><td/></tr><tr><td/><td>length</td><td>width</td><td>thickness</td><td>density</td><td>Young's mod.</td><td>Poisson ratio</td><td>loss factor</td></tr><tr><td>V-5000</td><td>0.9 m</td><td>0.6 m</td><td>0.003 m</td><td>2700 kg/m 3</td><td>7e10 N/m 2</td><td>0.3</td><td>0.02</td></tr><tr><td>G-5000</td><td>0.6 -0.9 m</td><td>0.4 -0.6 m</td><td>0.002 -0.004 m</td><td>2700 kg/m 3</td><td>7e10 N/m 2</td><td>0.3</td><td>0.01 -0.03</td></tr></table>", "type_str": "table", "num": null, "html": null}, "TABREF6": {"text": "Loading and boundary condition parameters for V-5000 and G-5000 datasets.", "content": "<table><tr><td/><td colspan=\"2\">Loading (Point force)</td><td>Boundary condition (rot. stiffness)</td></tr><tr><td/><td>x-position</td><td>y-position</td><td>cry/crx</td></tr><tr><td>V-5000</td><td>0.36 m</td><td>0.225 m</td><td>0.0 Nm</td></tr><tr><td>G-5000</td><td colspan=\"2\">0.18 -0.72 m 0.12 -0.48 m</td><td>0.0 -100 Nm</td></tr></table>", "type_str": "table", "num": null, "html": null}, "TABREF7": {"text": "Model size and speed comparison for a forward pass of a batch of 16 plate geometries on an A100 GPU. In comparison, solving one geometry via FEM takes 2 minutes and 19 seconds. The slowest deep learning method is then around 6000 times faster.", "content": "<table><tr><td/><td># weights in Mio</td><td>Time (s)</td></tr><tr><td>RN18 + FNO</td><td>11.8</td><td>0.014</td></tr><tr><td>DeepONet</td><td>11.3</td><td>0.005</td></tr><tr><td>FNO (velocity field)</td><td>134</td><td>0.008</td></tr><tr><td>Grid-RN18</td><td>12.9</td><td>0.005</td></tr><tr><td>FQO-RN18</td><td>12.7</td><td>0.005</td></tr><tr><td>FQO-ViT</td><td>9.28</td><td>0.013</td></tr><tr><td>Grid-UNet</td><td>27.9</td><td>0.010</td></tr><tr><td>FQO-UNet</td><td>7.1</td><td>0.338</td></tr><tr><td>FEM (20 CPU cores)</td><td>-</td><td>∼ 2224.000</td></tr></table>", "type_str": "table", "num": null, "html": null}, "TABREF8": {"text": "Ablation of α value for weighing loss on the predicted velocity field vs. predicted frequency response. Higher α value indicates more weight on velocity field. 1 indicates only velocity field loss. The selected α parameter is printed in bold.", "content": "<table><tr><td/><td/><td colspan=\"2\">V-5000</td><td/></tr><tr><td>α</td><td colspan=\"3\">EMSE EEMD EPEAKS</td><td>EF</td></tr><tr><td>0</td><td>0.25</td><td>8.02</td><td>0.15</td><td>4.4</td></tr><tr><td colspan=\"2\">0.5 0.15</td><td>5.52</td><td>0.11</td><td>2.9</td></tr><tr><td colspan=\"2\">0.9 0.09</td><td>3.90</td><td>0.08</td><td>1.8</td></tr><tr><td>1</td><td>0.09</td><td>4.00</td><td>0.07</td><td>1.8</td></tr></table>", "type_str": "table", "num": null, "html": null}, "TABREF9": {"text": "Ablation of number of channels of FQO-UNet and Grid-UNet. The number of channels is multiplied by a constant factor over the depth, named width. The selected width parameter is printed in bold.", "content": "<table><tr><td/><td/><td colspan=\"2\">V-5000</td><td/></tr><tr><td>width</td><td colspan=\"3\">EMSE EEMD EPEAKS</td><td>EF</td></tr><tr><td>FQO-UNet</td><td/><td/><td/><td/></tr><tr><td>16</td><td>0.12</td><td>5.00</td><td>0.09</td><td>2.2</td></tr><tr><td>32</td><td>0.09</td><td>3.90</td><td>0.08</td><td>1.8</td></tr><tr><td>64</td><td>0.08</td><td>3.82</td><td>0.07</td><td>1.8</td></tr><tr><td>Grid-UNet</td><td/><td/><td/><td/></tr><tr><td>16</td><td colspan=\"2\">0.31 11.44</td><td>0.31</td><td>3.9</td></tr><tr><td>32</td><td>0.24</td><td>8.56</td><td>0.25</td><td>3.3</td></tr><tr><td>64</td><td>0.19</td><td>7.57</td><td>0.24</td><td>2.7</td></tr><tr><td>128</td><td>0.24</td><td>8.17</td><td>0.21</td><td>3.7</td></tr></table>", "type_str": "table", "num": null, "html": null}, "TABREF11": {"text": "Data generation experiment.", "content": "<table><tr><td>V-5000</td></tr></table>", "type_str": "table", "num": null, "html": null}, "TABREF12": {"text": "4 models were trained on random splits in training and validation sets (4500 and 500 samples respectively). The results are denoted as mean [standard deviation].", "content": "<table><tr><td/><td/><td colspan=\"2\">V-5000</td><td/></tr><tr><td>Evaluation set</td><td>EMSE</td><td>EEMD</td><td>EPEAKS</td><td>EF</td></tr><tr><td>Validation set</td><td colspan=\"3\">0.096 [0.0023] 4.1 [0.072] 0.081 [0.0071]</td><td>2 [0.061]</td></tr><tr><td>Test set</td><td colspan=\"2\">0.094 [0.0031] 4.1 [0.091]</td><td>0.08 [0.003]</td><td>1.9 [0.096]</td></tr><tr><td>E.3 Visualizations</td><td/><td/><td/><td/></tr></table>", "type_str": "table", "num": null, "html": null}, "TABREF13": {"text": "• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).• Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.", "content": "<table><tr><td>6. Experimental Setting/Details</td></tr><tr><td>Question: Does the paper specify all the training and test details (e.g., data splits, hyper-</td></tr><tr><td>parameters, how they were chosen, type of optimizer, etc.) necessary to understand the</td></tr><tr><td>results?</td></tr><tr><td>Answer: [Yes]</td></tr></table>", "type_str": "table", "num": null, "html": null}}}}