{"paper_id": "gfn-diffusion", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:06:56.284403Z"}, "title": "Improved off-policy training of diffusion samplers", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Sendera", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "We study the problem of training diffusion models to sample from a distribution with a given unnormalized density or energy function. We benchmark several diffusion-structured inference methods, including simulation-based variational approaches and off-policy methods (continuous generative flow networks). Our results shed light on the relative advantages of existing algorithms while bringing into question some claims from past work. We also propose a novel exploration strategy for off-policy methods, based on local search in the target space with the use of a replay buffer, and show that it improves the quality of samples on a variety of target distributions. Our code for the sampling methods and benchmarks studied is made public at (link) as a base for future work on diffusion models for amortized inference.", "pdf_parse": {"paper_id": "gfn-diffusion", "_pdf_hash": "", "abstract": [{"text": "We study the problem of training diffusion models to sample from a distribution with a given unnormalized density or energy function. We benchmark several diffusion-structured inference methods, including simulation-based variational approaches and off-policy methods (continuous generative flow networks). Our results shed light on the relative advantages of existing algorithms while bringing into question some claims from past work. We also propose a novel exploration strategy for off-policy methods, based on local search in the target space with the use of a replay buffer, and show that it improves the quality of samples on a variety of target distributions. Our code for the sampling methods and benchmarks studied is made public at (link) as a base for future work on diffusion models for amortized inference.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Approximating and sampling from complex multivariate distributions is a fundamental problem in probabilistic deep learning [e.g., 27, 35, 26, 48, 57] and in scientific applications [3, 52, 38, 1, 32] . The problem of drawing samples from a distribution given only an unnormalized probability density or energy is particularly challenging in high-dimensional spaces and when the distribution of interest has many separated modes [5] . Sampling methods based on Markov chain Monte Carlo (MCMC)such as Metropolis-adjusted Langevin [MALA; 24, 65, 64] and Hamiltonian MC [HMC; 20, 31] may be slow to mix between modes and have a high cost per sample. While variants such as sequential MC [SMC; 25, 13, 16] and nested sampling [69, 10, 43] have better mode coverage, their cost may grow prohibitively with the dimensionality of the problem. This motivates the use of amortized variational inference, i.e., fitting parametric models that sample the target distribution. Diffusion models, continuous-time stochastic processes that gradually evolve a simple distribution to a complex target, are powerful density estimators with proven mode-mixing properties [15] ; as such, they have been widely used in the setting of generative models learned from data [70, 72, 28, 50, 66] . However, the problem of training diffusion models to sample from a distribution with a given blackbox density or energy function has attracted less attention. Recent work has drawn connections between diffusion (learning the denoising process) and stochastic control (learning the Föllmer drift [21] ), leading to approaches such as the path integral sampler [PIS; 88], denoising diffusion sampler [DDS; 78], and time-reversed diffusion sampler [DIS; 8]; such approaches were recently unified by [63] and [79] . Another line of work [42, 86] is based on continuous generative flow networks (GFlowNets), which are deep reinforcement learning algorithms adapted to variational inference that offer stable off-policy training and thus flexible exploration [46] . Despite the advances in sampling methods and attempts to unify them theoretically [63, 79] , the field suffers from some failures in benchmarking and reproducibility, with the works differing in the choice of model architectures, using unstated hyperparameters, and even disagreeing in their definitions of the same target densities (see §B.1). The first main contribution of this paper is a unified library for diffusion-structured samplers. The library has a focus on off-policy methods (continuous GFlowNets) but also includes simulation-based variational objectives such as PIS. Using this codebase, we are able to benchmark methods from past work under comparable conditions and confirm claims about exploration strategies and desirable inductive biases, while calling into question other claims on robustness and sample efficiency. Our library also includes several new modeling and training techniques, and we provide preliminary evidence of their utility in possible future work ( §5.3).", "cite_spans": [{"start": 130, "end": 133, "text": "27,", "ref_id": "BIBREF26"}, {"start": 134, "end": 137, "text": "35,", "ref_id": "BIBREF34"}, {"start": 138, "end": 141, "text": "26,", "ref_id": "BIBREF25"}, {"start": 142, "end": 145, "text": "48,", "ref_id": "BIBREF47"}, {"start": 146, "end": 149, "text": "57]", "ref_id": "BIBREF56"}, {"start": 181, "end": 184, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 185, "end": 188, "text": "52,", "ref_id": "BIBREF51"}, {"start": 189, "end": 192, "text": "38,", "ref_id": "BIBREF37"}, {"start": 193, "end": 195, "text": "1,", "ref_id": "BIBREF0"}, {"start": 196, "end": 199, "text": "32]", "ref_id": "BIBREF31"}, {"start": 428, "end": 431, "text": "[5]", "ref_id": "BIBREF4"}, {"start": 535, "end": 538, "text": "24,", "ref_id": "BIBREF23"}, {"start": 539, "end": 542, "text": "65,", "ref_id": "BIBREF64"}, {"start": 543, "end": 546, "text": "64]", "ref_id": "BIBREF63"}, {"start": 572, "end": 575, "text": "20,", "ref_id": null}, {"start": 576, "end": 579, "text": "31]", "ref_id": "BIBREF30"}, {"start": 689, "end": 692, "text": "25,", "ref_id": "BIBREF24"}, {"start": 693, "end": 696, "text": "13,", "ref_id": "BIBREF12"}, {"start": 697, "end": 700, "text": "16]", "ref_id": "BIBREF15"}, {"start": 721, "end": 725, "text": "[69,", "ref_id": "BIBREF68"}, {"start": 726, "end": 729, "text": "10,", "ref_id": "BIBREF9"}, {"start": 730, "end": 733, "text": "43]", "ref_id": "BIBREF42"}, {"start": 1150, "end": 1154, "text": "[15]", "ref_id": "BIBREF14"}, {"start": 1247, "end": 1251, "text": "[70,", "ref_id": "BIBREF69"}, {"start": 1252, "end": 1255, "text": "72,", "ref_id": "BIBREF71"}, {"start": 1256, "end": 1259, "text": "28,", "ref_id": "BIBREF27"}, {"start": 1260, "end": 1263, "text": "50,", "ref_id": "BIBREF49"}, {"start": 1264, "end": 1267, "text": "66]", "ref_id": "BIBREF65"}, {"start": 1565, "end": 1569, "text": "[21]", "ref_id": "BIBREF20"}, {"start": 1766, "end": 1770, "text": "[63]", "ref_id": "BIBREF62"}, {"start": 1775, "end": 1779, "text": "[79]", "ref_id": "BIBREF78"}, {"start": 1803, "end": 1807, "text": "[42,", "ref_id": "BIBREF41"}, {"start": 1808, "end": 1811, "text": "86]", "ref_id": "BIBREF85"}, {"start": 2023, "end": 2027, "text": "[46]", "ref_id": "BIBREF45"}, {"start": 2112, "end": 2116, "text": "[63,", "ref_id": "BIBREF62"}, {"start": 2117, "end": 2120, "text": "79]", "ref_id": "BIBREF78"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Our second contribution is a study of methods for improving exploration and credit assignmentthe propagation of learning signals from the target density to the parameters of earlier sampling steps -in diffusion-structured samplers ( §4). First, our results ( §5.2) suggest that the technique of utilizing partial trajectory information [44, 55] , as done in the diffusion setting by [86] , offers little benefit, and a higher training cost, over on-policy [88] or off-policy [42] trajectory-based optimization. Second, we examine the utility of a gradient-based variant which parametrizes the denoising distribution as a correction to a Langevin process [88] . We show that this inductive bias is also beneficial in the offpolicy (GFlowNet) setting despite higher computational cost. Finally, motivated by recent approaches in discrete sampling, we propose an efficient exploration technique based on local search in the target space with the use of a replay buffer, which improves sample quality across various target distributions.", "cite_spans": [{"start": 336, "end": 340, "text": "[44,", "ref_id": "BIBREF43"}, {"start": 341, "end": 344, "text": "55]", "ref_id": "BIBREF54"}, {"start": 383, "end": 387, "text": "[86]", "ref_id": "BIBREF85"}, {"start": 456, "end": 460, "text": "[88]", "ref_id": "BIBREF87"}, {"start": 475, "end": 479, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 654, "end": 658, "text": "[88]", "ref_id": "BIBREF87"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Amortized variational inference approaches use a parametric model 𝑞 𝜃 to approximate a given target density 𝑝 target , typically through stochastic optimization [30, 58, 2] . Notably, explicit density models like autoregressive models and normalizing flows have been extensively utilized in density estimation [60, 19, 81, 22, 51] . However, these models impose structural constraints, thereby limiting their expressive power [14, 23, 87] . The adoption of diffusion processes in generative models has stimulated a renewed interest in hierarchical models as density estimators [80, 28, 76] . Approaches like PIS [88] leverage stochastic optimal control for sampling from unnormalized densities, albeit still struggling with scalability in high-dimensional spaces.", "cite_spans": [{"start": 161, "end": 165, "text": "[30,", "ref_id": "BIBREF29"}, {"start": 166, "end": 169, "text": "58,", "ref_id": "BIBREF57"}, {"start": 170, "end": 172, "text": "2]", "ref_id": "BIBREF1"}, {"start": 310, "end": 314, "text": "[60,", "ref_id": "BIBREF59"}, {"start": 315, "end": 318, "text": "19,", "ref_id": "BIBREF18"}, {"start": 319, "end": 322, "text": "81,", "ref_id": "BIBREF80"}, {"start": 323, "end": 326, "text": "22,", "ref_id": "BIBREF21"}, {"start": 327, "end": 330, "text": "51]", "ref_id": "BIBREF50"}, {"start": 426, "end": 430, "text": "[14,", "ref_id": "BIBREF13"}, {"start": 431, "end": 434, "text": "23,", "ref_id": "BIBREF22"}, {"start": 435, "end": 438, "text": "87]", "ref_id": "BIBREF86"}, {"start": 577, "end": 581, "text": "[80,", "ref_id": "BIBREF79"}, {"start": 582, "end": 585, "text": "28,", "ref_id": "BIBREF27"}, {"start": 586, "end": 589, "text": "76]", "ref_id": "BIBREF75"}, {"start": 612, "end": 616, "text": "[88]", "ref_id": "BIBREF87"}], "ref_spans": [], "eq_spans": [], "section": "Prior work", "sec_num": "2"}, {"text": "Generative flow networks, originally defined in the discrete case by [6, 7] , view hierarchical sampling (i.e., stepwise generation) as a sequential decision-making process and represent a synthesis of reinforcement learning and variational inference approaches [46, 90, 73, 18] , expanding from specific scientific domains [e.g., 36, 4, 89] to amortized inference over a broader array of latent structures [e.g., 77, 34] . Their ability to efficiently navigate trajectory spaces via off-policy exploration has been crucial, yet they encounter challenges in training dynamics, such as credit assignment and exploration efficiency [45, 44, 55, 59, 68, 39, 37] . These challenges have repercussions in the scalability of these methods in more complex scenarios, which this paper addresses in the continuous case.", "cite_spans": [{"start": 69, "end": 72, "text": "[6,", "ref_id": "BIBREF5"}, {"start": 73, "end": 75, "text": "7]", "ref_id": null}, {"start": 262, "end": 266, "text": "[46,", "ref_id": "BIBREF45"}, {"start": 267, "end": 270, "text": "90,", "ref_id": "BIBREF89"}, {"start": 271, "end": 274, "text": "73,", "ref_id": "BIBREF72"}, {"start": 275, "end": 278, "text": "18]", "ref_id": "BIBREF17"}, {"start": 331, "end": 334, "text": "36,", "ref_id": "BIBREF35"}, {"start": 335, "end": 337, "text": "4,", "ref_id": "BIBREF3"}, {"start": 338, "end": 341, "text": "89]", "ref_id": "BIBREF88"}, {"start": 414, "end": 417, "text": "77,", "ref_id": "BIBREF76"}, {"start": 418, "end": 421, "text": "34]", "ref_id": "BIBREF33"}, {"start": 630, "end": 634, "text": "[45,", "ref_id": "BIBREF44"}, {"start": 635, "end": 638, "text": "44,", "ref_id": "BIBREF43"}, {"start": 639, "end": 642, "text": "55,", "ref_id": "BIBREF54"}, {"start": 643, "end": 646, "text": "59,", "ref_id": "BIBREF58"}, {"start": 647, "end": 650, "text": "68,", "ref_id": "BIBREF67"}, {"start": 651, "end": 654, "text": "39,", "ref_id": "BIBREF38"}, {"start": 655, "end": 658, "text": "37]", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "Prior work", "sec_num": "2"}, {"text": "3 Setting: Diffusion-structured sampling Let E : R 𝑑 → R be a differentiable energy function and define 𝑅(x) = exp(-E (x)), the reward or unnormalized target density. Assuming the integral 𝑍 := ∫ R 𝑑 𝑅(x) 𝑑x exists, E defines a Boltzmann density 𝑝 target (x) = 𝑅(x)/𝑍 on R 𝑑 . We are interested in the problems of sampling from 𝑝 target and approximating the partition function 𝑍 given access only to E and possibly to its gradient ∇E.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prior work", "sec_num": "2"}, {"text": "We describe two closely related perspectives on this problem: via neural SDEs and stochastic control ( §3.1) and via continuous generative flow networks ( §3.2).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prior work", "sec_num": "2"}, {"text": "Generative modeling with SDEs. Diffusion models assume a continuous-time generative process given by a neural stochastic differential equation [SDE; 75, 54, 67] :", "cite_spans": [{"start": 149, "end": 152, "text": "75,", "ref_id": "BIBREF74"}, {"start": 153, "end": 156, "text": "54,", "ref_id": "BIBREF53"}, {"start": 157, "end": 160, "text": "67]", "ref_id": "BIBREF66"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "𝑑x 𝑡 = 𝑢(x 𝑡 , 𝑡; 𝜃) 𝑑𝑡 + 𝑔(x 𝑡 , 𝑡; 𝜃) 𝑑w 𝑡 ,", "eq_num": "(1)"}], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "where x 0 follows a fixed tractable distribution 𝜇 0 (such as a Gaussian or a point mass). The initial distribution 𝜇 0 and the stochastic dynamics specified by (1) induce marginal densities 𝑝 𝑡 on R 𝑑 for each 𝑡 > 0. The functions 𝑢 and 𝑔 have learnable parameters that we wish to optimize, using some objective, so as to make the terminal density 𝑝 1 close to 𝑝 target . Samples can be drawn from 𝑝 1 by sampling x 0 ∼ 𝜇 0 and simulating the SDE (1) to time 𝑡 = 1.", "cite_spans": [{"start": 161, "end": 164, "text": "(1)", "ref_id": "BIBREF0"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "The SDE driving 𝜇 0 to 𝑝 target is not unique. However, if one fixes a reverse-time SDE, or noising process, that pushes 𝑝 target at 𝑡 = 1 to 𝜇 0 at 𝑡 = 0, then its reverse, the forward SDE (1), is uniquely determined under mild conditions and is called the denoising process. For usual choices of the noising process, there are stochastic regression objectives for learning the drift 𝑢 of the denoising process given samples from 𝑝 target , and the diffusion rate 𝑔 is available in closed form [28, 72] .", "cite_spans": [{"start": 495, "end": 499, "text": "[28,", "ref_id": "BIBREF27"}, {"start": 500, "end": 503, "text": "72]", "ref_id": "BIBREF71"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "Time discretization. In practice, the integration of the SDE ( 1) is approximated by a discrete-time scheme, the simplest of which is Euler-Maruyama integration. The process ( 1) is replaced by a discrete-time Markov chain", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "x 0 → x Δ𝑡 → x 2Δ𝑡 → • • • → x 1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": ", where Δ𝑡 =1 𝑇 is the time increment and and 𝑇 is the number of steps:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x 0 ∼ 𝜇 0 , x 𝑡+Δ𝑡 = x 𝑡 + 𝑢(x 𝑡 , 𝑡; 𝜃)Δ𝑡 + 𝑔(x 𝑡 , 𝑡; 𝜃) √ Δ𝑡 z 𝑡 z 𝑡 ∼ N (0, I 𝑑 ).", "eq_num": "(2)"}], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "The density of the transition kernel from x 𝑡 to x 𝑡+Δ𝑡 can explicitly be written as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "𝑝 𝐹 (x 𝑡+Δ𝑡 | x 𝑡 ) = N (x 𝑡+Δ𝑡 ; x 𝑡 + 𝑢(x 𝑡 , 𝑡; 𝜃)Δ𝑡, 𝑔(x 𝑡 , 𝑡; 𝜃) 2 Δ𝑡I 𝑑 ),", "eq_num": "(3)"}], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "where 𝑝 𝐹 denotes the transition density of the discretized forward SDE. This density defines a joint distribution over trajectories starting at x 0 :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "𝑝 𝐹 (x Δ𝑡 , . . . , x 1 | x 0 ) = 𝑇 -1 𝑖=0 𝑝 𝐹 (x (𝑖+1)Δ𝑡 | x 𝑖Δ𝑡 ).", "eq_num": "(4)"}], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "Similarly, a discrete-time reverse process", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x 1 → x 1-Δ𝑡 → x 1-2Δ𝑡 → • • • → x 0 with transition densities 𝑝 𝐵 (x 𝑡 -Δ𝑡 | x 𝑡 ) defines a joint distribution 1 via 𝑝 𝐵 (x 0 , . . . , x 1-Δ𝑡 | x 1 ) = 𝑇 𝑡=1 𝑝 𝐵 (x (𝑖-1)Δ𝑡 | x 𝑖Δ𝑡 ).", "eq_num": "(5)"}], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "If the forward and backward processes (starting from 𝜇 0 and 𝑝 target , respectively) are reverses of each other, then they define the same distribution over trajectories, i.e., for all", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x 0 → x Δ𝑡 → • • • → x 1 , 𝜇 0 (x 0 ) 𝑝 𝐹 (x Δ𝑡 , . . . , x 1 | x 0 ) = 𝑝 target (x 1 ) 𝑝 𝐵 (x 0 , . . . , x 1-Δ𝑡 | x 1 ).", "eq_num": "(6)"}], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "In particular, the marginal densities of x 1 under the forward and backward processes are then equal to 𝑝 target , and the forward process can be used to sample the target distribution.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "Because the reverse of a process with Gaussian increments is, in general, not itself Gaussian, (6) can be enforced only approximately, but the discrepancy vanishes as Δ𝑡 → 0 (i.e., increments are infinitesimally Gaussian), an application of the central limit theorem that is key to stochastic calculus [54] .", "cite_spans": [{"start": 302, "end": 306, "text": "[54]", "ref_id": "BIBREF53"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "SDE learning as hierarchical variational inference. The problem of learning the parameters 𝜃 of the forward process so as to enforce ( 6) is one of hierarchical variational inference. The backward process transforms x 1 into x 0 via a sequence of latent variables x 1-Δ𝑡 , . . . , x 0 , and the forward process aims to match the posterior distribution over these variables and thus to approximately enforce (6) .", "cite_spans": [{"start": 407, "end": 410, "text": "(6)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "In the setting of diffusion models learned from data, where one has samples from 𝑝 target , one can optimize the forward process by minimizing the KL divergence 𝐷 KL ( 𝑝 target • 𝑝 𝐵 ∥𝜇 0 • 𝑝 𝐹 ) between the distribution over trajectories given by the reverse process and that given by the forward process. This is equivalent to the typical training of diffusion models, which optimizes a variational bound on the data log-likelihood (see [71] ). However, in the setting of an intractable density 𝑝 target , unbiased estimators of this divergence are not available. Instead, one can optimize the reverse KL:2 ", "cite_spans": [{"start": 439, "end": 443, "text": "[71]", "ref_id": "BIBREF70"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "𝐷 KL (𝜇 0 • 𝑝 𝐹 ∥ 𝑝 target • 𝑝 𝐵 ) = ∫ log 𝜇 0 (x 0 ) 𝑝 𝐹 (x Δ𝑡 , . . . , x 1 | x 0 ) 𝑝 target (x 1 ) 𝑝 𝐵 (x 0 , . . . , x 1-Δ𝑡 | x 1 ) 𝑑𝜇 0 (x 0 ) 𝑝 𝐹 (x Δ𝑡 , . . . , x 1 | x 0 ) 𝑑x Δ𝑡 . . . 𝑑x 1 .", "eq_num": "(7)"}], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "Various estimators of this objective are available. For instance, the path integral sampler objective [PIS; 88] uses the reparametrization trick to express (7) as an expectation over noise variables z 𝑡 that participate in the hierarchical sampling of x Δ𝑡 , . . . , x 1 , yielding an unbiased gradient estimator, but one that requires backpropagation into the simulation of the forward process. The related denoising diffusion sampler [DDS; 78] applies the same principle in a different integration scheme.", "cite_spans": [{"start": 156, "end": 159, "text": "(7)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama hierarchical samplers", "sec_num": "3.1"}, {"text": "Continuous generative flow networks (GFlowNets) [42] express the problem of enforcing (6) as a reinforcement learning task. In this section, we summarize this interpretation, its connection to neural SDEs, the associated learning objectives, and their relative advantages and disadvantages.", "cite_spans": [{"start": 48, "end": 52, "text": "[42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "The connection between generative flow networks and diffusion models or SDEs was first made informally by [46] in the distribution-matching setting and by [84] in the maximum-likelihood setting, while the theoretical foundations for continuous GFlowNets were later laid down by [42] .", "cite_spans": [{"start": 106, "end": 110, "text": "[46]", "ref_id": "BIBREF45"}, {"start": 155, "end": 159, "text": "[84]", "ref_id": "BIBREF83"}, {"start": 278, "end": 282, "text": "[42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "State and action space. To formulate sampling as a sequential decision-making problem, one must define the spaces of states and actions. In the case of sampling by 𝑇-step <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> integration, assuming 𝜇 0 is a point mass at 0, the state space is", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "S = {(0, 0) ∪ (x, 𝑡) : x ∈ R 𝑑 , 𝑡 ∈ {Δ𝑡, 2Δ𝑡, . . . , 1} ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "with the point (x, 𝑡) representing that the sampling agent is at position x at time 𝑡.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "Sampling begins with the initial state x 0 := (0, 0), proceeds through a sequence of states (x Δ𝑡 , Δ𝑡), (x 2Δ𝑡 , 2Δ𝑡), . . . , and ends at a state (x 1 , 1); states (x, 𝑡) with 𝑡 = 1 are called terminal states and their collection is denoted X. From now on, we will often write x 𝑡 in place of the state (x 𝑡 , 𝑡) when the time 𝑡 is clear from context. The sequence of states", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "x 0 → x Δ𝑡 → • • • → x 1 is called a complete trajectory.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "The actions from a nonterminal state (x 𝑡 , 𝑡) correspond to the possible next states (x 𝑡+Δ𝑡 , 𝑡 + Δ𝑡) that can be reached from (x 𝑡 , 𝑡) by a single step of the Euler-Maruyama integrator. 3Forward policy and learning problem. A (forward) policy is a collection of continuous distributions over the successor states -states reachable by a single action -of every nonterminal state (x, 𝑡). In our context, this amounts to a collection of conditional probability densities 𝑝 𝐹 (x 𝑡+Δ𝑡 | x 𝑡 ; 𝜃), representing the density of the transition kernel from x 𝑡 to x 𝑡+Δ𝑡 . GFlowNet training optimizes the parameters 𝜃, which may be the weights of a neural network specifying a density over x 𝑡+Δ𝑡 conditioned on x Δ𝑡 . A policy 𝑝 𝐹 induces a distribution over complete trajectories", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "𝜏 = (x 0 → x Δ𝑡 → • • • → x 1 ) via 𝑝 𝐹 (𝜏; 𝜃) = 𝑇 -1 𝑖=0 𝑝 𝐹 (x (𝑖+1)Δ𝑡 | x 𝑖Δ𝑡 ; 𝜃).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "In particular, we get a marginal density over terminal states:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "𝑝 ⊤ 𝐹 (x 1 ; 𝜃)= ∫ 𝑝 𝐹 (x 0 → x Δ𝑡 → • • • → x 1 ; 𝜃) 𝑑x Δ𝑡 . . . 𝑑x 1-Δ𝑡 .", "eq_num": "(8)"}], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "The learning problem solved by GFlowNets is to find the parameters 𝜃 of a policy 𝑝 𝐹 whose terminating density 𝑝 ⊤ 𝐹 is equal to 𝑝 target , i.e.,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "𝑝 ⊤ 𝐹 (x 1 ; 𝜃) = 𝑅(x 1 ) 𝑍 ∀x 1 ∈ R 𝑑 .", "eq_num": "(9)"}], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "However, because the integral ( 8) is intractable and 𝑍 is unknown, auxiliary objects must be introduced into optimization objectives to enforce (9) , as discussed below.", "cite_spans": [{"start": 145, "end": 148, "text": "(9)", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "Notably, if the policy is a Gaussian with mean and variance given by neural networks taking x 𝑡 and 𝑡 as input, then learning the policy amounts to learning the drift 𝑢(x 𝑡 , 𝑡; 𝜃) and diffusion 𝑔(x 𝑡 , 𝑡; 𝜃) of a SDE (1), i.e., fitting a neural SDE. The SDE learning problem in §3.1 is thus the same as that of fitting a GFlowNet with Gaussian policies.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "Backward policy and trajectory balance. A backward policy is a collection of conditional probability densities 𝑝 𝐵 (x 𝑡 -Δ𝑡 | x 𝑡 ; 𝜓), representing a probability density of transitioning from x 𝑡 to an ancestor state x 𝑡 -Δ𝑡 . The backward policy induces a distribution over complete trajectories 𝜏 conditioned on their terminal state (cf. ( 5)):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "𝑝 𝐵 (𝜏 | x 1 ; 𝜓) = 𝑇 𝑖=1 𝑝 𝐵 (x (𝑖-1)Δ𝑡 | x 𝑖Δ𝑡 ; 𝜓),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "where exceptionally 𝑝 𝐵 (x 0 | x Δ𝑡 ) = 1 as 𝜇 0 is a point mass.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "Generalizing a result in the discrete-space setting [45] , [42] show that 𝑝 𝐹 samples from the target distribution (i.e., satisfies ( 9)) if and only if there exists a backward policy 𝑝 𝐵 and a scalar 𝑍 𝜃 such that the trajectory balance conditions are fulfilled for every complete trajectory 𝜏 = (x 0 → x Δ𝑡 → • • • → x 1 ):", "cite_spans": [{"start": 52, "end": 56, "text": "[45]", "ref_id": "BIBREF44"}, {"start": 59, "end": 63, "text": "[42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "𝑍 𝜃 𝑝 𝐹 (𝜏; 𝜃) = 𝑅(x 1 ) 𝑝 𝐵 (𝜏 | x 1 ; 𝜓).", "eq_num": "(10"}], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": ") If these conditions hold, then 𝑍 𝜃 equals the true partition function 𝑍 = ∫ x 𝑅(x) 𝑑x. The trajectory balance objective for a trajectory 𝜏 is the squared log-ratio of the two sides of (10) , that is:", "cite_spans": [{"start": 186, "end": 190, "text": "(10)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L TB (𝜏; 𝜃, 𝜓) = log 𝑍 𝜃 𝑝 𝐹 (𝜏; 𝜃) 𝑅(x 1 ) 𝑝 𝐵 (𝜏 | x 1 ; 𝜓) 2 .", "eq_num": "(11)"}], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "One can thus achieve (9) by minimizing to zero the loss L TB (𝜏; 𝜃, 𝜓) with respect to the parameters 𝜃 and 𝜓, where the trajectories 𝜏 used for training are sampled from some training policy 𝜋(𝜏).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "While it is possible to optimize (11) with respect to the parameters of both the forward and backward policies, in some learning problems, one fixes the backward policy and only optimizes the parameters of 𝑝 𝐹 and the estimate of the partition function 𝑍 𝜃 . For example, for most experiments in §5, we fix the backward policy to a discretized Brownian bridge, following past work.", "cite_spans": [{"start": 33, "end": 37, "text": "(11)", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "Off-policy optimization. Unlike the KL objective (7) , whose gradient involves an expectation over the distribution of trajectories under the current forward process, (11) can be optimized off-policy, i.e., using trajectories sampled from an arbitrary distribution 𝜋. Because minimizing L TB (𝜏; 𝜃, 𝜓) to 0 for all 𝜏 in the support of 𝜋 will achieve (9) , 𝜋 can be taken be any distribution with full support, so as to promote discovery of modes of the target distribution. Various choices motivated by reinforcement learning techniques have been proposed, including noisy exploration or tempering [6] , replay buffers [17] , <PERSON> sampling [59] , and backward traces from terminal states obtained by MCMC [43] .", "cite_spans": [{"start": 49, "end": 52, "text": "(7)", "ref_id": null}, {"start": 167, "end": 171, "text": "(11)", "ref_id": "BIBREF10"}, {"start": 350, "end": 353, "text": "(9)", "ref_id": "BIBREF8"}, {"start": 598, "end": 601, "text": "[6]", "ref_id": "BIBREF5"}, {"start": 619, "end": 623, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 644, "end": 648, "text": "[59]", "ref_id": "BIBREF58"}, {"start": 709, "end": 713, "text": "[43]", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "In the continuous case, [46, 42] proposed to simply add a small constant to the policy variance when sampling trajectories for training. Off-policy optimization is a key advantage of GFlowNets over variational methods such as PIS, which require on-policy optimization [46] .", "cite_spans": [{"start": 24, "end": 28, "text": "[46,", "ref_id": "BIBREF45"}, {"start": 29, "end": 32, "text": "42]", "ref_id": "BIBREF41"}, {"start": 268, "end": 272, "text": "[46]", "ref_id": "BIBREF45"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "However, when L TB happens to be optimized on-policy, i.e., using trajectories sampled from the policy 𝑝 𝐹 itself, we get an unbiased estimator of the gradient of the KL divergence (7) with respect to 𝑝 𝐹 's parameters up to a constant [62, 46, 90] , that is:", "cite_spans": [{"start": 181, "end": 184, "text": "(7)", "ref_id": null}, {"start": 236, "end": 240, "text": "[62,", "ref_id": "BIBREF61"}, {"start": 241, "end": 244, "text": "46,", "ref_id": "BIBREF45"}, {"start": 245, "end": 248, "text": "90]", "ref_id": "BIBREF89"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "E 𝜏∼ 𝑝 𝐹 ( 𝜏 ) [∇ 𝜃 ′ L TB (𝜏; 𝜃, 𝜓)] = 2∇ 𝜃 ′ 𝐷 KL ( 𝑝 𝐹 (𝜏; 𝜃) ∥ 𝑝 target (x 1 ) 𝑝 𝐵 (𝜏 | x 1 ; 𝜓))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": ", where ∇ 𝜃 ′ denotes the gradient with respect to the parameters of 𝑝 𝐹 , but not 𝑍 𝜃 . This unbiased estimator tends to have higher variance than the reparametrization-based estimator used by PIS. On the other hand, it does not require backpropagation through the simulation of the forward process and can be used to optimize the parameters of both the forward and backward policies.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "Other objectives. The trajectory balance objective (11) is not the only possible objective that can be used to enforce (9) . A notable generalization is subtrajectory balance [SubTB; 44], which involves modeling a scalar state flow 𝑓 (x 𝑡 ; 𝜃) associated with each state x 𝑡 -intended to model the marginal density of the forward process at x 𝑡 -and enforcing subtrajectory balance conditions for all partial trajectories", "cite_spans": [{"start": 51, "end": 55, "text": "(11)", "ref_id": "BIBREF10"}, {"start": 119, "end": 122, "text": "(9)", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x 𝑚Δ𝑡 → x (𝑚+1)Δ𝑡 → • • • → x 𝑛Δ𝑡 : 𝑓 (x 𝑚Δ𝑡 ; 𝜃) 𝑛-1 𝑖=𝑚 𝑝 𝐹 (x (𝑖+1)Δ𝑡 | x 𝑖Δ𝑡 ; 𝜃) = 𝑓 (x 𝑛Δ𝑡 ; 𝜃) 𝑛 𝑖=𝑚+1 𝑝 𝐵 (x (𝑖-1)Δ𝑡 | x 𝑖Δ𝑡 ; 𝜓),", "eq_num": "(12)"}], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "where for terminal states 𝑓 (x 1 ) = 𝑅(x 1 ). This approach has some computational overhead associated with training the state flow, but has been shown to be effective in discrete-space settings, especially when combined with the forward-looking reward shaping scheme proposed by [55] . It has also been tested in the continuous case, but our experimental results suggest that it offers little benefit over the TB objective in the diffusion setting (see §4.1 and §B.1).", "cite_spans": [{"start": 280, "end": 284, "text": "[55]", "ref_id": "BIBREF54"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "It is also worth noting the off-policy VarGrad estimator [53, 62] , rediscovered for GFlowNets by [85] .", "cite_spans": [{"start": 57, "end": 61, "text": "[53,", "ref_id": "BIBREF52"}, {"start": 62, "end": 65, "text": "62]", "ref_id": "BIBREF61"}, {"start": 98, "end": 102, "text": "[85]", "ref_id": "BIBREF84"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "Like TB, VarGrad can be optimized over trajectories drawn off-policy. Rather than enforcing (10) for every trajectory, VarGrad optimizes the empirical variance (over a minibatch) of the log-ratio of the two sides of (10) . As noted by [46] , this is equivalent to minimizing L TB first with respect to log 𝑍 𝜃 to optimality over the batch, then with respect to the parameters of 𝑝 𝐹 .", "cite_spans": [{"start": 92, "end": 96, "text": "(10)", "ref_id": "BIBREF9"}, {"start": 216, "end": 220, "text": "(10)", "ref_id": "BIBREF9"}, {"start": 235, "end": 239, "text": "[46]", "ref_id": "BIBREF45"}], "ref_spans": [], "eq_spans": [], "section": "Euler-Maruyama samplers as GFlowNets", "sec_num": "3.2"}, {"text": "The main challenges in training off-policy sampling models are exploration efficiency (discovery of high-reward states) and credit assignment (propagation of reward signals to the actions that led to them). We describe several new and existing methods for addressing these challenges in the context of diffusion-structured GFlowNets. These techniques will be empirically studied and compared in §5.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Exploration and credit assignment in continuous GFlowNets", "sec_num": "4"}, {"text": "Partial energies and subtrajectory-based learning. [86] studied the diffusion sampler learning problem introduced by [42] , but replaced the TB learning objective with the SubTB objective. 4 In addition, an inductive bias resembling the geometric interpolation in [47] was used for the state flow function:", "cite_spans": [{"start": 51, "end": 55, "text": "[86]", "ref_id": "BIBREF85"}, {"start": 117, "end": 121, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 264, "end": 268, "text": "[47]", "ref_id": "BIBREF46"}], "ref_spans": [], "eq_spans": [], "section": "Credit assignment methods", "sec_num": "4.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "log 𝑓 (x 𝑡 ; 𝜃) = (1 -𝑡) log 𝑝 ref 𝑡 (x 𝑡 ) + 𝑡 log 𝑅(x 𝑡 ) + NN(x 𝑡 , 𝑡; 𝜃),", "eq_num": "(13)"}], "section": "Credit assignment methods", "sec_num": "4.1"}, {"text": "where NN is a neural network and", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Credit assignment methods", "sec_num": "4.1"}, {"text": "𝑝 ref 𝑡 (x 𝑡 ) = N (x 𝑡 ; 0, 𝜎 2 𝑡𝐼 𝑑 )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Credit assignment methods", "sec_num": "4.1"}, {"text": "is the marginal density of a Brownian motion with rate 𝜎 at x 𝑡 . The use of the target density log 𝑅(x 𝑡 ) = -E (x 𝑡 ) in the state flow function was hypothesized to provide an effective signal driving the sampler to high-density states at early steps in the trajectory. Such an inductive bias on the state flow was called forward-looking (FL) by [55] , and we will refer to this method as FL-SubTB in §5.", "cite_spans": [{"start": 348, "end": 352, "text": "[55]", "ref_id": "BIBREF54"}], "ref_spans": [], "eq_spans": [], "section": "Credit assignment methods", "sec_num": "4.1"}, {"text": "Langevin dynamics inductive bias. [88] proposed an inductive bias on the architecture of the drift of the neural SDE 𝑢(x 𝑡 , 𝑡; 𝜃) (in GFlowNet terms, the mean of the Gaussian density 𝑝 𝐹 (x 𝑡+Δ𝑡 | x 𝑡 ; 𝜃)) that resembles a Langevin process on the target distribution. One writes", "cite_spans": [{"start": 34, "end": 38, "text": "[88]", "ref_id": "BIBREF87"}], "ref_spans": [], "eq_spans": [], "section": "Credit assignment methods", "sec_num": "4.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "𝑢(x 𝑡 , 𝑡; 𝜃) = NN 1 (x 𝑡 , 𝑡; 𝜃) + NN 2 (𝑡; 𝜃)∇E (x 𝑡 ),", "eq_num": "(14)"}], "section": "Credit assignment methods", "sec_num": "4.1"}, {"text": "where NN 1 and NN 2 are neural networks outputting a vector and a scalar, respectively. The second term in ( 14) is a scaled gradient of the target energy -the drift of a Langevin SDE -and the first term is a learned correction. This inductive bias, which we name the Langevin parametrization (LP), was shown to improve the efficiency of PIS. We will study its effect on continuous GFlowNets in §5.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Credit assignment methods", "sec_num": "4.1"}, {"text": "The inductive bias ( 14) placed on policies represents a different way of incorporating the reward signal at intermediate steps in the trajectory and can steer the sampler towards low-energy regions. It contrasts with (13) in that it provides the gradient of the energy directly to the policy, rather than just using the energy to provide a learning signal to policies via the parametrization of the log-state flow (13) .", "cite_spans": [{"start": 218, "end": 222, "text": "(13)", "ref_id": "BIBREF12"}, {"start": 415, "end": 419, "text": "(13)", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Credit assignment methods", "sec_num": "4.1"}, {"text": "Considerations of the continuous-time limit lead us to conjecture that the Langevin parametrization ( 14) with NN 1 independent of x 𝑡 is equivalent to the forward-looking flow (13) in the limit of small time increments Δ𝑡 → 0, i.e., they induce the same asymptotics of the discrepancy in the SubTB constraints (12) over short partial trajectories. Such theoretical analysis can be the subject of future work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Credit assignment methods", "sec_num": "4.1"}, {"text": "Local search with parallel MALA. The FL and LP inductive biases both induce computational overhead: either in the evaluation and optimization of a state flow or in the need to evaluate the energy gradient at every step of sampling (see §C.3). We present an alternative technique that does not induce additional computation cost per training trajectory. LGCP (𝑑 = 1600) In detail, we initially sample 𝑀 candidates from the sampler: {x (1) , . . . , x ( 𝑀 ) } ∼ 𝑝 ⊤ 𝐹 (•). Subsequently, we run parallel MALA across 𝑀 chains over 𝐾 transitions , with the initial states of the Markov chain being {x (1) , . . . , x ( 𝑀 ) }. After the 𝐾 burn-in burn-in transitions, the accepted samples are stored in a local search buffer D LS . We occasionally update the buffer using MALA steps and replay samples from it to minimize the computational demands of iterative local search. MALA steps are far more parallelizable than sampler training and need to be made only rarely (as the buffer is much larger than the training batch size), so the overhead of local search is small.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A new method for off-policy exploration with local search and replay buffer", "sec_num": "4.2"}, {"text": "Algorithm ↓ Metric → Δ log 𝑍 Δ log 𝑍 RW Δ log 𝑍 Δ log 𝑍 RW Δ log 𝑍 Δ log", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A new method for off-policy exploration with local search and replay buffer", "sec_num": "4.2"}, {"text": "Training with local search and replay buffer. To train samplers with the aid of the buffer, we draw a sample x from D LS (uniformly or using a prioritization scheme, §E), sample a trajectory 𝜏 leading to x from the backward process, and make a gradient update on the objective (e.g., TB) associated with 𝜏.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A new method for off-policy exploration with local search and replay buffer", "sec_num": "4.2"}, {"text": "When training with local search guidance, we alternate two steps, inspired by [43] , who alternate training on forward trajectories and backward trajectories initialized at a fixed set of MCMC samples.", "cite_spans": [{"start": 78, "end": 82, "text": "[43]", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "A new method for off-policy exploration with local search and replay buffer", "sec_num": "4.2"}, {"text": "Step A involves training with on-policy or exploratory forward sampling while Step B uses samples drawn from the local search buffer described above. This allows the sampler to explore both diversified samples (Step A) and low-energy samples (Step B). See §E for detailed pseudocode of adaptive-step parallel MALA and local search-guided GFlowNet training.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A new method for off-policy exploration with local search and replay buffer", "sec_num": "4.2"}, {"text": "We conduct comprehensive benchmarks of various diffusion-structured samplers, encompassing both GFlowNet samplers and methods such as PIS. For the GFlowNet samplers, we investigate a range of techniques, including different exploration strategies and loss functions. Additionally, we examine the efficacy of the Langevin parametrization and the newly proposed local search with buffer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "We explore two types of tasks, with more details provided in §B: sampling from energy distributions -a 2-dimensional mixture of Gaussians with 25 modes (25GMM), the 10-dimensional Funnel, the 32-dimensional Manywell distribution, and the 1600-dimensional Log-Gaussian Cox processand conditional sampling from the latent posterior of a variational autoencoder (VAE; [41, 61] ). This allows us to investigate both unconditional and conditional generative modeling techniques.", "cite_spans": [{"start": 365, "end": 369, "text": "[41,", "ref_id": "BIBREF40"}, {"start": 370, "end": 373, "text": "61]", "ref_id": "BIBREF60"}], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "We evaluate three algorithm categories:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "(1) Traditional sampling methods: We consider a standard Sequential Monte Carlo (SMC) implementation and a state-of-the-art nested sampling method (GGNS, [43] ). ( 2) Simulation-driven variational approaches: DIS [8] , DDS [78] , and PIS [88] .", "cite_spans": [{"start": 154, "end": 158, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 213, "end": 216, "text": "[8]", "ref_id": "BIBREF7"}, {"start": 223, "end": 227, "text": "[78]", "ref_id": "BIBREF77"}, {"start": 238, "end": 242, "text": "[88]", "ref_id": "BIBREF87"}], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "(3) Diffusion-based GFlowNet samplers: Our evaluation focuses on TB-based training and the enhancements described in §4: the VarGrad estimator (VarGrad), off-policy exploration (Expl.), Langevin parametrization (LP), and local search (LS). Additionally, we assess the FL-SubTBbased continuous GFlowNet as studied by [86] for a comprehensive comparison.", "cite_spans": [{"start": 316, "end": 320, "text": "[86]", "ref_id": "BIBREF85"}], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "For ( 2) and ( 3), we employ a consistent neural architecture across methods (details in §D).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "Learning problem and fixed backward process. In our main experiments, we borrow the modeling setting from [88] . We aim to learn a Gaussian forward policy 𝑝 𝐹 that samples from the target distribution in 𝑇 = 100 steps (Δ𝑡 = 0.01). Just as in past work [88, 42, 86] , the backward process is fixed to a discretized Brownian bridge with a noise rate 𝜎 that depends on the domain; explicitly,", "cite_spans": [{"start": 106, "end": 110, "text": "[88]", "ref_id": "BIBREF87"}, {"start": 252, "end": 256, "text": "[88,", "ref_id": "BIBREF87"}, {"start": 257, "end": 260, "text": "42,", "ref_id": "BIBREF41"}, {"start": 261, "end": 264, "text": "86]", "ref_id": "BIBREF85"}], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "𝑝 𝐵 (x 𝑡 -Δ𝑡 | x 𝑡 ) = N x 𝑡 -Δ𝑡 ; 𝑡 -Δ𝑡 𝑡 x 𝑡 , 𝑡 -Δ𝑡 𝑡 𝜎 2 Δ𝑡I 𝑑 ,", "eq_num": "(15)"}], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "understood to be a point mass at 0 when 𝑡 = Δ𝑡. To keep the learning problem consistent with past work, we fix the variance of the forward policy 𝑝 𝐹 to 𝜎 2 . This simplification is justified in continuous time, when the forward and reverse SDEs have the same diffusion rate. However, in §5.3, we will provide evidence that learning the forward policy's variance is quite beneficial for shorter trajectories.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "Benchmarking metrics. To evaluate diffusion-based samplers, we use two metrics from past work [88, 42] , which we restate in our notation. Given any forward policy 𝑝 𝐹 , we have a variational lower bound on the log-partition function log", "cite_spans": [{"start": 94, "end": 98, "text": "[88,", "ref_id": "BIBREF87"}, {"start": 99, "end": 102, "text": "42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "𝑍 = ∫ R 𝑑 𝑅(x) 𝑑x: log ∫ R 𝑑 𝑅(x) 𝑑x = log E 𝜏=(•••→x 1 )∼ 𝑝 𝐹 ( 𝜏 ) 𝑅(x 1 ) 𝑝 𝐵 (𝜏 | x 1 ) 𝑝 𝐹 (𝜏) ≥ E 𝜏=(•••→x 1 )∼ 𝑝 𝐹 ( 𝜏 ) log 𝑅(x 1 ) 𝑝 𝐵 (𝜏 | x 1 ) 𝑝 𝐹 (𝜏)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": ".", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "We use a 𝐾-sample (𝐾 = 2000) <PERSON> estimate of this expectation, log Ẑ, as a metric, which equals the true log 𝑍 if 𝑝 𝐹 and 𝑝 𝐵 jointly satisfy (10) and thus 𝑝 𝐹 samples from the target distribution. We also employ an importance-weighted variant, which emphasizes mode coverage over accurate local modeling:", "cite_spans": [{"start": 150, "end": 154, "text": "(10)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "log ẐRW := log 𝐾 ∑︁ 𝑖=1 𝑅(x (𝑖) 1 ) 𝑝 𝐵 (𝜏 (𝑖) | x (𝑖) 1 ) 𝑝 𝐹 (𝜏 (𝑖) )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": ", where 𝜏 (1) , . . . , 𝜏 (𝐾 ) are trajectories sampled from 𝑝 𝐹 and leading to terminal states x (1) 1 , . . . , x (𝐾 ) 1 . The estimator log ẐRW is also a lower bound on log 𝑍 and approaches it as 𝐾 → ∞ [11] . In the unconditional modeling benchmarks, we compare both estimators to the true log-partition function, which is known analytically for all tasks except LGCP (leading to discrepancies in past work; see §B.1).", "cite_spans": [{"start": 205, "end": 209, "text": "[11]", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "In addition, we include a sample-based metric (2-<PERSON><PERSON><PERSON> distance); see §C.1. Unconditional sampling. We report the metrics for all algorithms and energies in Table 1 .", "cite_spans": [], "ref_spans": [{"start": 169, "end": 170, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Tasks and baselines", "sec_num": "5.1"}, {"text": "We observe that TB's performance is generally modest without additional exploration and credit assignment mechanisms, except on the Funnel task, where variations in performance across methods are negligible. This confirms hypotheses from past work about the importance of offpolicy exploration [46, 42] and the importance of improved credit assignment [86] . On the other hand, our results do not show a consistent and significant improvement of the FL-SubTB objective used by [86] over TB. Replacing TB with the VarGrad objective yields similar results.", "cite_spans": [{"start": 294, "end": 298, "text": "[46,", "ref_id": "BIBREF45"}, {"start": 299, "end": 302, "text": "42]", "ref_id": "BIBREF41"}, {"start": 352, "end": 356, "text": "[86]", "ref_id": "BIBREF85"}, {"start": 477, "end": 481, "text": "[86]", "ref_id": "BIBREF85"}], "ref_spans": [], "eq_spans": [], "section": "Results", "sec_num": "5.2"}, {"text": "The simple off-policy exploration method of adding variance to the policy notably enhances performance on the 25GMM task. We investigate this phenomenon in more detail in Fig. 2 , finding that exploration that slowly decreases over the course of training is the best strategy.", "cite_spans": [], "ref_spans": [{"start": 176, "end": 177, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Results", "sec_num": "5.2"}, {"text": "On the other hand, our local search-guided exploration with a replay buffer (LS) leads to a substantial improvement in performance, surpassing or competing with GFlowNet baselines, non-GFlowNet baselines, and non-amortized sampling methods in most tasks and metrics. This advantage is attributed to efficient exploration and the ability to replay past low-energy regions, thus preventing mode collapse during training (Fig. 1 ). Further details on LS enhancements are discussed in §E with ablation studies in §E.2. Incorporating Langevin parametrization (LP) into TB or FL-SubTB results in notable performance improvements (despite being 2-3× slower per iteration), indicating that previous observations [88] transfer to off-policy algorithms. Compared to FL-SubTB, which aims for enhanced credit assignment through partial energy, LP achieves superior credit assignment leveraging gradient information, akin to partial energy in continuous time. LP is either superior or competitive across most tasks and metrics.", "cite_spans": [{"start": 704, "end": 708, "text": "[88]", "ref_id": "BIBREF87"}], "ref_spans": [{"start": 424, "end": 425, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Results", "sec_num": "5.2"}, {"text": "In §C.3, we study the scaling of the algorithms with dimension, showing efficiency of the proposed LS.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Results", "sec_num": "5.2"}, {"text": "Conditional sampling. For the VAE task, we observe that the performance of the baseline GFlowNet-based samplers is generally worse than that of the simulation-based PIS (Table 2 ). While LP and LS improve the performance of TB, they do not close the gap in likelihood estimation; however, with the VarGrad objective, the performance is competitive with or superior to PIS. We hypothesize that this discrepancy is due to the difficulty of fitting the conditional log-partition function estimator, which is required for the TB objective but not for VarGrad, which only learns the policy. (In Fig. D.1 we show decoded samples encoded using the best-performing diffusion encoder.)", "cite_spans": [], "ref_spans": [{"start": 176, "end": 177, "text": "2", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Results", "sec_num": "5.2"}, {"text": "Our implementation of diffusion-structured generative flow networks includes several additional options that diverge from the modeling assumptions made in most past work in the field. Notably, it features the ability to:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Extensions to general SDE learning problems", "sec_num": "5.3"}, {"text": "• optimize the backward (noising) process -not only the denoising process -as was done for related learning problems in [12, 63, 79] ; • learn the forward process's diffusion rate 𝑔(x 𝑡 , 𝑡; 𝜃), not only the mean 𝑢(x 𝑡 , 𝑡; 𝜃); • assume a varying noise schedule for the backward process, making it possible to train models with standard noising SDEs used for diffusion models for images.", "cite_spans": [{"start": 120, "end": 124, "text": "[12,", "ref_id": "BIBREF11"}, {"start": 125, "end": 128, "text": "63,", "ref_id": "BIBREF62"}, {"start": 129, "end": 132, "text": "79]", "ref_id": "BIBREF78"}], "ref_spans": [], "eq_spans": [], "section": "Extensions to general SDE learning problems", "sec_num": "5.3"}, {"text": "These extensions will allow others to build on our implementation and apply it to problems such as finetuning diffusion models trained on images with a GFlowNet objective.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Extensions to general SDE learning problems", "sec_num": "5.3"}, {"text": "As noted in §5.1, in the main experiments we fixed the diffusion rate of the learned forward process, an assumption inherited from all past work and justified in the continuous-time limit. However, we perform an experiment to show the importance of extensions such as learning the forward variance in discrete time. Fig. 3 shows the samples of models on the 25GMM energy following the experimental setup of [43] . We see that when the forward policy's variance is learned, the model can better capture the details of the target distributions, choosing a low variance in the vicinity of the peaks to avoid 'blurring' them through the noise added in the last step of sampling.", "cite_spans": [{"start": 407, "end": 411, "text": "[43]", "ref_id": "BIBREF42"}], "ref_spans": [{"start": 321, "end": 322, "text": "3", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Extensions to general SDE learning problems", "sec_num": "5.3"}, {"text": "In §C.2, we include preliminary results using a variance-preserving backward process, as commonly used in diffusion models, in place of the reversed Brownian motion used in the main experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Extensions to general SDE learning problems", "sec_num": "5.3"}, {"text": "The ability to model distributions accurately in fewer steps is important for computational efficiency. Future work can consider ways to improve performance in coarse time discretizations, such as non-Gaussian transitions, whose utility in diffusion models trained from data has been demonstrated [82] .", "cite_spans": [{"start": 297, "end": 301, "text": "[82]", "ref_id": "BIBREF81"}], "ref_spans": [], "eq_spans": [], "section": "Extensions to general SDE learning problems", "sec_num": "5.3"}, {"text": "We have presented a study of diffusion-structured samplers for amortized inference over continuous variables. Our results suggest promising techniques for improving the mode coverage and efficiency of these models. Future work on applications can consider inference of high-dimensional parameters of dynamical systems and inverse problems. In probabilistic machine learning, extensions of this work should study integration of our amortized sequential samplers as variational posteriors in an expectation-maximization loop for training latent variable models, as was recently done for discrete compositional latents by [33] , and for sampling Bayesian posteriors over high-dimensional model parameters. The most important direction of theoretical work is understanding the continuous-time limit (𝑇 → ∞) of all the algorithms we have studied.", "cite_spans": [{"start": 619, "end": 623, "text": "[33]", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "In a paper that appeared subsequently to the publication of this work, <PERSON><PERSON> et al. [9] have shown connections among the families of diffusion sampling algorithms considered here and analyzed their continuous-time limits.", "cite_spans": [{"start": 85, "end": 88, "text": "[9]", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "Note added in final version:", "sec_num": null}, {"text": "Code is available at https://github.com/GFNOrg/gfn-diffusion and will continue to be maintained and extended.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Code and hyperparameters", "sec_num": null}, {"text": "Below are commands to reproduce some of the results on Manywell and VAE with PIS and GFlowNet models as an example, showing the hyperparameters: PIS:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Code and hyperparameters", "sec_num": null}, {"text": "--mode_fwd pis --lr_policy 1e-3", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Code and hyperparameters", "sec_num": null}, {"text": "--mode_fwd pis --lr_policy 1e-3 --langevin ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PIS + <PERSON><PERSON>:", "sec_num": null}, {"text": "Gaussian Mixture Model with 25 modes (25GMM). The model, termed as 25GMM, consists of a two-dimensional Gaussian mixture model with 25 distinct modes. Each mode exhibits an identical variance of 0.3. The centers of these modes are strategically positioned on a grid formed by the Cartesian product {-10, -5, 0, 5, 10} × {-10, -5, 0, 5, 10}, effectively distributing them across the coordinate space.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Target densities", "sec_num": null}, {"text": "Funnel [29] . The funnel represents a classical benchmark in sampling techniques, characterized by a ten-dimensional distribution defined as follows: The first dimension, 𝑥 0 , follows a normal distribution with mean 0 and variance 9, denoted as 𝑥 0 ∼ N (0, 9). Conditional on 𝑥 0 , the remaining dimensions, 𝑥 1:9 , are distributed according to a multivariate normal distribution with mean vector 0 and a covariance matrix exp(𝑥 0 )I, where I is the identity matrix. This is succinctly represented as 𝑥 1:9 | 𝑥 0 ∼ N (0, exp (𝑥 0 ) I).", "cite_spans": [{"start": 7, "end": 11, "text": "[29]", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "B Target densities", "sec_num": null}, {"text": "<PERSON><PERSON> [52] . The manywell is characterized by a 32-dimensional distribution, which is constructed as the product of 16 identical two-dimensional double well distributions. Each of these two-dimensional components is defined by a potential function, 𝜇(𝑥 1 , 𝑥 2 ), expressed as", "cite_spans": [{"start": 9, "end": 13, "text": "[52]", "ref_id": "BIBREF51"}], "ref_spans": [], "eq_spans": [], "section": "B Target densities", "sec_num": null}, {"text": "𝜇(𝑥 1 , 𝑥 2 ) = exp -𝑥 4 1 + 6𝑥 2 1 + 0.5𝑥 1 -0.5𝑥 2 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Target densities", "sec_num": null}, {"text": ". VAE [41] . This task involves sampling from a 20-dimensional latent posterior 𝑝(𝑧|𝑥) ∝ 𝑝(𝑧) 𝑝(𝑥|𝑧), where 𝑝(𝑧) is a fixed prior and 𝑝(𝑥|𝑧) is a pretrained VAE decoder, using a conditional sampler 𝑞(𝑧|𝑥) dependent on input data (image) 𝑥.", "cite_spans": [{"start": 6, "end": 10, "text": "[41]", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "B Target densities", "sec_num": null}, {"text": "LGCP [49] . This density over a 1600-dimensional variable is a Log-Gaussian Cox process fit to a distribution of pine saplings in Finland.", "cite_spans": [{"start": 5, "end": 9, "text": "[49]", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "B Target densities", "sec_num": null}, {"text": "Wrong definitions of the Funnel density. As already noted by [78] , [88] uses a different variance of the first component in the Funnel density, 1 instead of 9. This apparent bug in the task definition has been propagated to subsequent work, including [42] .", "cite_spans": [{"start": 61, "end": 65, "text": "[78]", "ref_id": "BIBREF77"}, {"start": 68, "end": 72, "text": "[88]", "ref_id": "BIBREF87"}, {"start": 252, "end": 256, "text": "[42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "B.1 Discrepancies in past work", "sec_num": null}, {"text": "LGCP. The LGCP benchmark suffers from the lack of a consistent ground truth log 𝑍 to compare against. Previous work has compared the value of the partition function log 𝑍 against a \"long run of Sequential Monte Carlo\" [88] . We note that this approach produces noisy estimates of the partition function, especially in high-dimensional problems (indeed, SMC has rarely been used in problems with over a thousand dimensions); therefore, it is unclear how long the SMC needs to be run to produce an accurate estimate. We found that two different values are being used in the literature: log 𝑍 = 512.6 in one repository and log 𝑍 = 501.8 in another.", "cite_spans": [{"start": 218, "end": 222, "text": "[88]", "ref_id": "BIBREF87"}], "ref_spans": [], "eq_spans": [], "section": "Evaluation on", "sec_num": null}, {"text": "On FL-SubTB as used in [86] . We make two observations calling into question the main results of [86] .", "cite_spans": [{"start": 23, "end": 27, "text": "[86]", "ref_id": "BIBREF85"}, {"start": 97, "end": 101, "text": "[86]", "ref_id": "BIBREF85"}], "ref_spans": [], "eq_spans": [], "section": "Evaluation on", "sec_num": null}, {"text": "First, the only substantial difference between the algorithm used by [86] and the one from the past work [42] -which first proposed the use of GFlowNet objectives to train diffusion samplers -is the substitution of the FL-SubTB objective [55, 44] for TB [45] . However, [86] elects to compare FL-SubTB with the Langevin parameterization to TB without the Langevin parameterization. Our results in Table 1 show that while the Langevin parameterization is crucial for the performance of all objectives; FL-SubTB does not provide any consistent benefit over TB or VarGrad.", "cite_spans": [{"start": 69, "end": 73, "text": "[86]", "ref_id": "BIBREF85"}, {"start": 105, "end": 109, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 238, "end": 242, "text": "[55,", "ref_id": "BIBREF54"}, {"start": 243, "end": 246, "text": "44]", "ref_id": "BIBREF43"}, {"start": 254, "end": 258, "text": "[45]", "ref_id": "BIBREF44"}, {"start": 270, "end": 274, "text": "[86]", "ref_id": "BIBREF85"}], "ref_spans": [{"start": 403, "end": 404, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Evaluation on", "sec_num": null}, {"text": "Second, the results are not reproducible, neither with the published code from [86] run 'out of the box', nor with our reimplementation. In particular, on the LGCP density, the training did not converge within the allotted training time. We have contacted the authors of [86] , who confirmed that running their published code does not reproduce the results in the paper but could not provide any further explanation or a working implementation. ", "cite_spans": [{"start": 79, "end": 83, "text": "[86]", "ref_id": "BIBREF85"}, {"start": 271, "end": 275, "text": "[86]", "ref_id": "BIBREF85"}], "ref_spans": [], "eq_spans": [], "section": "Evaluation on", "sec_num": null}, {"text": "Following the recent results by [8, 63, 78] , we perform an additional set of experiments with a different successful noise schedule. We replace the Brownian motion by the variance-preserving SDEs from <PERSON> et al. [72] , given by an <PERSON>nstein-<PERSON> process:", "cite_spans": [{"start": 32, "end": 35, "text": "[8,", "ref_id": "BIBREF7"}, {"start": 36, "end": 39, "text": "63,", "ref_id": "BIBREF62"}, {"start": 40, "end": 43, "text": "78]", "ref_id": "BIBREF77"}, {"start": 214, "end": 218, "text": "[72]", "ref_id": "BIBREF71"}], "ref_spans": [], "eq_spans": [], "section": "C.2 Variance-preserving noising process", "sec_num": null}, {"text": "𝜎(𝑡) := 𝜈 √︁ 2𝛽(𝑡)I and 𝜇(𝑥, 𝑡) := -𝛽(𝑡)𝑥 (16) with 𝜈 ∈ (0, ∞).", "cite_spans": [{"start": 42, "end": 46, "text": "(16)", "ref_id": "BIBREF15"}], "ref_spans": [], "eq_spans": [], "section": "C.2 Variance-preserving noising process", "sec_num": null}, {"text": "In particular, we follow the common procedure -use 𝜈 := 1 and 𝛽(𝑡) := (1 -𝑡) 𝛽 𝑚𝑖𝑛 + 𝑡 𝛽 𝑚𝑎𝑥 , 𝑡 ∈ [0, 1], with 𝛽 𝑚𝑖𝑛 = 0.01 and 𝛽 𝑚𝑎𝑥 = 4.0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Variance-preserving noising process", "sec_num": null}, {"text": "We evaluate three representative methods using this variance-preserving backward process. The results, in Table C .2, are similar to those using the Brownian bridge process. We expect that the choice of noising process gains importance in challenging high-dimensional problems.", "cite_spans": [], "ref_spans": [{"start": 112, "end": 113, "text": "C", "ref_id": "TABREF5"}], "eq_spans": [], "section": "C.2 Variance-preserving noising process", "sec_num": null}, {"text": "The Manywell energy ( §B) is defined in any even number of dimensions and thus allows to study the scaling of the methods with dimension. We evaluate several representative methods in dimension 8, 128, and 512 (in addition to the 32 studied in the main text). All experimental settings are kept the same as as for 𝑑 = 32. Due to the large runtime, some runs in dimensions 128 and 512 had to be limited at 12 hours, while in dimensions 8 and 32 all run in under 3 hours on a RTX8000 GPU.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.3 Scalability study", "sec_num": null}, {"text": "These results are shown in Table C .3. We observe:", "cite_spans": [], "ref_spans": [{"start": 33, "end": 34, "text": "C", "ref_id": "TABREF5"}], "eq_spans": [], "section": "C.3 Scalability study", "sec_num": null}, {"text": "• The overhead of the Langevin parametrization grows with dimension, but is critical to performance. • The even higher overhead of FL-SubTB as used by [86] .", "cite_spans": [{"start": 151, "end": 155, "text": "[86]", "ref_id": "BIBREF85"}], "ref_spans": [], "eq_spans": [], "section": "C.3 Scalability study", "sec_num": null}, {"text": "• The relatively high efficiency and low overhead of our newly proposed local search. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.3 Scalability study", "sec_num": null}, {"text": "Sampling energies. In this section, we detail the hyperparameters used for our experiments. An important parameter is the diffusion coefficient of the forward policy, which is denoted by 𝜎 and also used in the definition of the fixed backward process. The base diffusion rate 𝜎 2 (parameter t_scale) is set to 5 for 25GMM and 1 for Funnel and Manywell, consistent with past work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "For LGCP, we found that using too small diffusion rate 𝜎 2 (e.g., 𝜎 2 = 1) prevents the methods from achieving reasonable results. We tested different values of 𝜎 2 = {1, 3, 5}, and selected 𝜎 2 = 5, which gives the best results, which follows the findings in <PERSON> [88] .", "cite_spans": [{"start": 273, "end": 277, "text": "[88]", "ref_id": "BIBREF87"}], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "For all our experiments, we used a learning rate of 10 -3 . Additionally, we used a higher learning rate for learning the flow parameterization, which is set as 10 -1 when using the TB loss and 10 -2 with the SubTB loss. These settings were found to be consistently stable (unlike those with higher learning rates) and converge within the allotted number of steps (unlike those with lower learning rates).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "For the SubTB loss, we experimented with the settings of 10× lower learning rates for both flow and policy models communicated by the authors of [86] , but found the results to be inferior both using their published code (and other unstated hyperparameters communicated by the authors) and using our reimplementation.", "cite_spans": [{"start": 145, "end": 149, "text": "[86]", "ref_id": "BIBREF85"}], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "For models with exploration, we use an exploration factor of 0.2 (that is, noise with a variance of 0.2 is added to the policy when sampling trajectories for training), which decays linearly over the first half of training, consistent with [42] .", "cite_spans": [{"start": 240, "end": 244, "text": "[42]", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "We train all our models for 25, 000 iterations except those using Langevin dynamics, which are trained for 10, 000 iterations. This results in approximately equal computation time owing to the overhead from computation of the score at each sampling step.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "We use the same neural network architecture for the GFlowNet as one of our baselines [88] . Similar to [88] , we also use an initialization scheme with last-layer weights set to 0 at the start of training. Since the SubTB requires the flow function to be conditioned on the current state x 𝑡 and time 𝑡, we follow [86] and parametrize the flow model with the same architecture as the Langevin scaling model NN 2 in [88] . Additionally, we perform clipping on the output of the network as well as the score obtained from the energy function, typically setting the clipping parameter of Langevin scaling model to 10 2 and policy network to 10 4 , similarly to [78] :", "cite_spans": [{"start": 85, "end": 89, "text": "[88]", "ref_id": "BIBREF87"}, {"start": 103, "end": 107, "text": "[88]", "ref_id": "BIBREF87"}, {"start": 314, "end": 318, "text": "[86]", "ref_id": "BIBREF85"}, {"start": 415, "end": 419, "text": "[88]", "ref_id": "BIBREF87"}, {"start": 658, "end": 662, "text": "[78]", "ref_id": "BIBREF77"}], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "𝑓 𝜃 (𝑘, 𝑥) = clip NN 1 (𝑘, 𝑥; 𝜃) + NN 2 (𝑘; 𝜃) ⊙ clip ∇ ln 𝜋(𝑥), -10 2 , 10 2 , -10 4 , 10 4 . (", "eq_num": "17"}], "section": "D Experiment details", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "All models were trained with a batch size of 300. In each experiment, we train models on a single NVIDIA A100-Large GPU, if not stated explicitly otherwise.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "VAE experiment. In the VAE experiment, we used a standard VAE model pretrained for 100 epochs on the MNIST dataset. The encoder 𝑞(𝑧|𝑥) contains an input linear layer (784 neurons) followed by hidden linear layer (400 neurons), ReLU activation function, and two linear heads (20 neurons each) whose outputs were reparametrized to be means and scales of multivariate Normal distribution. The decoder consists of 20-dimensional input, one hidden layer (400 neurons), followed by the ReLU activation, and 784-dimensional output. The output is processed by the sigmoid function to be scaled properly into [0, 1].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "The goal is to sample conditionally on 𝑥 the latent 𝑧 from the unnormalized density 𝑝(𝑧, 𝑥) = 𝑝(𝑧) 𝑝(𝑥 | 𝑧) (where 𝑝(𝑧) is the prior and 𝑝(𝑥|𝑧) is the likelihood computed from the decoder), which is proportional to the posterior 𝑝(𝑧 | 𝑥). We reuse the model architectures from the unconditional sampling experiments, but also provide 𝑥 as an input to the first layer of the models expressing the policy drift (as well as the flow, for FL-SubTB) and add one hidden layer to process high-dimensional conditions. For models trained with TB, log 𝑍 𝜃 also becomes a MLP taking 𝑥 as input.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "The VarGrad and LS techniques require adaptations in the conditional setting. For LS, buffers (D buffer and D LS ) must store the associated conditions 𝑥 together with the samples 𝑧 and the corresponding unnormalized density 𝑅(𝑧; 𝑥), i.e., a tuple of (𝑥, 𝑧, 𝑅(𝑧; 𝑥)). For VarGrad, because the partition function depends on the conditioning information 𝑥, it is necessary to compute variance over many trajectories sharing the same condition. We choose to sample 10 trajectories for each condition occurring in a minibatch and compute the VarGrad loss for each such set of 10 trajectories. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Experiment details", "sec_num": null}, {"text": "Prioritized sampling scheme. We can use uniform or prioritized sampling to draw samples from the buffer for training. We found prioritized sampling to work slightly better in our experiments (see ablation study in §E.2), although the choice should be investigated more thoroughly in future work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Local search-guided GFlowNet", "sec_num": null}, {"text": "We use rank-based prioritization [74] , which follows a probabilistic approach defined as:", "cite_spans": [{"start": 33, "end": 37, "text": "[74]", "ref_id": "BIBREF73"}], "ref_spans": [], "eq_spans": [], "section": "E Local search-guided GFlowNet", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "𝑝(x; D buffer ) ∝ 𝑘 |D buffer | + rank D buffer (x) -1 ,", "eq_num": "(18)"}], "section": "E Local search-guided GFlowNet", "sec_num": null}, {"text": "where rank D buffer (x) represents the relative rank of a sample 𝑥 based on a ranking function 𝑅(x) (in our case, the unnormalized target density at sample x). The parameter 𝑘 is a hyperparameter for prioritization, where a lower value of 𝑘 assigns a higher probability to samples with higher ranks, thereby introducing a more greedy selection approach. We set 𝑘 = 0.01 for every task. Given that the sampling is proportional to the size of D buffer , we impose a constraint on the maximum size of the buffer: We use the number of total iterations 𝐼 = 25, 000 for every task as default. Note as local search is performed to update D LS occasionally that per 100 iterations, the number of local search updates is done 25, 000/100 = 250.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Local search-guided GFlowNet", "sec_num": null}, {"text": "|D buffer | =", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Local search-guided GFlowNet", "sec_num": null}, {"text": "Increasing capacity of buffer. The capacity of the replay buffer influences the duration for which it retains past experiences, enabling it to replay these experiences to the policy. This mechanism helps in preventing mode collapse during training. Table E .1 demonstrates that enhancing the buffer's capacity leads to improved sampling quality. Furthermore, Figure 1 illustrates that increasing the buffer's capacity-thereby encouraging the model to recall past low-energy experiences-enhances its mode-seeking capability. Guidelines:", "cite_spans": [], "ref_spans": [{"start": 255, "end": 256, "text": "E", "ref_id": null}, {"start": 366, "end": 367, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "E.2 Ablation study for local search-guided GFlowNets", "sec_num": null}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2 Ablation study for local search-guided GFlowNets", "sec_num": null}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2 Ablation study for local search-guided GFlowNets", "sec_num": null}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.2 Ablation study for local search-guided GFlowNets", "sec_num": null}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Answer: [NA]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Answer: [Yes] Justification: See experiment sections and references to appendix material.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "• The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Answer: [Yes] Justification: We believe there are no violations of the CoE.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [NA] Justification: The paper studies a ML problem with no immediate societal impacts.", "cite_spans": [{"start": 8, "end": 12, "text": "[NA]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Justification: The paper studies a ML problem with no immediate application to generation of new image or text content, nor other functions that have the potential for misuse, to the best of our knowledge.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [Yes] Justification: We cite the works introducing all datasets we study.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "38th Conference on Neural Information Processing Systems (NeurIPS 2024).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "In the case that 𝜇 0 is a point mass, we assume the distribution x 0 | x Δ𝑡 to also be a point mass, which has density 𝑝 𝐵 (x 0 | x Δ𝑡 ) = 1 with respect to the measure 𝜇 0 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "To be precise, the fraction in(7) should be understood as a Radon-<PERSON> derivative, which makes sense whether 𝜇 0 is a point mass or a continuous distribution and generalizes to continuous time[8,63].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Formally, the foundations in[42] require assuming reference measures with respect to which the reward and kernel densities are defined. As we deal with Euclidean spaces and assume the Le<PERSON>gue measure, readers need not burden themselves with measure theory. We note, however, that this flexibility allows easy generalization to sampling on other spaces, such as any Riemannian manifolds, where other methods do not directly apply.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Despite the claimed benefits of FL-SubTB for diffusion samplers, we discovered that[86] modifies critical experimental variables in comparisons and reports irreproducible results; see §B.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "We thank <PERSON><PERSON><PERSON><PERSON> for assistance with methods from prior work, as well as <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> for helpful discussions and suggestions.The authors acknowledge funding from UNIQUE, CIFAR, NSERC, Intel, Recursion Pharmaceuticals, and Samsung. The research was enabled in part by computational resources provided by the Digital Research Alliance of Canada (https://alliancecan.ca), Mila (https://mila.quebec), and NVIDIA. The research of M.S. was in part funded by National Science Centre, Poland, 2022/45/N/ST6/03374.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgments", "sec_num": null}, {"text": "This section describes a detailed algorithm for local search, which provides an updated buffer D LS , which contains low-energy samples.Dynamic adjustment of step size 𝜂. To enhance local search using parallel MALA, we dynamically select the Langevin step size (𝜂), which governs the MH acceptance rate. Our objective is to attain an average acceptance rate of 0.574, which is theoretically optimal for high-dimensional MALA's efficiency [56] . While the user can customize the target acceptance rate, the adaptive approach eliminates the need for manual tuning.Computational cost of local search. The computational cost of local search is not significant. Local search for iteration of 𝐾 = 200 requires 6.04 seconds (averaged with five trials in Manywell), where we only occasionally (every 100 iterations) update D LS with MALA. The speed is evaluated using the computational resources of the Intel Xeon Scalable Gold 6338 CPU (2.00GHz) and the NVIDIA RTX 4090 GPU. 𝑀 }, current buffer D LS , total steps 𝐾, burn in steps 𝐾 burn-in , initial step size 𝜂 0 , amplifying factor 𝑓 increase , damping factor 𝑓 decrease , unnormalized target density 𝑅 output Updated buffer D LS Initialize acceptance counterCompute acceptance ratio 𝑟 ← min 1,With probability 𝑟, accept the proposal: 𝑥 We adopt default parameters: 𝑓 increase = 1.1, 𝑓 decrease = 0.9, 𝜂 0 = 0.01, 𝐾 = 200, 𝐾 burn-in = 100, and 𝛼 target = 0.574 for three unconditional tasks. For conditional tasks of VAE, we give more iterations of local search: 𝐾 = 500, 𝐾 burn-in = 200.It is noteworthy that by adjusting the inverse temperature 𝛽 into 𝑅 𝛽 during the computation of the Metropolis-Hastings acceptance ratio 𝑟, we can facilitate a greedier local search strategy aimed at exploring samples with lower energy (i.e., higher density 𝑝 target ). This approach proves advantageous for navigating high-dimensional and steep landscapes, which are typically challenging for locating low-energy samples. For unconditional tasks, we set 𝛽 = 1.In the context of the VAE task (Table 2 ), we utilize two GFlowNet loss functions: TB and VarGrad. For local search within TB, we set 𝛽 = 1, while for VarGrad, we employ 𝛽 = 5. As illustrated in Table 2 , employing a local search with 𝛽 = 1 fails to enhance the performance of the TB method. Conversely, a local search with 𝛽 = 5 results in improvements at the log ẐRW metric over the VarGrad + Expl. + LP, even though the performance of VarGrad + Expl. + LP surpasses that of TB substantially. This underscores the importance of selecting an appropriate 𝛽 value, which is critical for optimizing the exploration-exploitation balance depending on the target objectives.Justification: No new theoretical results. For exposition of the mathematical basis for our algorithms, we state the assumptions. Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [{"start": 438, "end": 442, "text": "[56]", "ref_id": "BIBREF55"}], "ref_spans": [{"start": 2033, "end": 2034, "text": "2", "ref_id": null}, {"start": 2196, "end": 2197, "text": "2", "ref_id": null}], "eq_spans": [], "section": "E.1 Local search algorithm", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: See experiment sections and references to appendix material. Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?Answer: [Yes]Justification: We provide code to reproduce nearly all of our experimental results.Guidelines:• The answer NA means that paper does not include experiments requiring code.• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e. • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the results?Answer: [Yes]Justification: See the experiment sections and references to appendix material.Guidelines:• The answer NA means that the paper does not include experiments.• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments?Answer: [Yes]Justification: All results tables and plots show standard deviation and indicate significance of the best metric.Guidelines:• The answer NA means that the paper does not include experiments.• The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors).• For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Posterior samples of source galaxies in strong gravitational lenses with score-based priors", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Hezaveh", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.03812"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Y., and <PERSON><PERSON>, Y. <PERSON> samples of source galaxies in strong gravitational lenses with score-based priors. arXiv preprint arXiv:2211.03812, 2022.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Amortized variational inference for simple hierarchical models", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>tized variational inference for simple hierarchical models. Neural Information Processing Systems (NeurIPS), 2021.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Flow-based generative models for Markov chain Monte Carlo in lattice field theory", "authors": [{"first": "M", "middle": ["S"], "last": "Albergo", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Physical Review D", "volume": "100", "issue": "3", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, P. E. Flow-based generative models for Markov chain Monte Carlo in lattice field theory. Physical Review D, 100(3):034515, 2019.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Towards bayesian inference of gene regulatory networks with GFlowNets", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Hartford", "suffix": ""}, {"first": "", "middle": [], "last": "Dyngfn", "suffix": ""}], "year": null, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Bengio, Y., and Hartford, J. DynGFN: Towards bayesian inference of gene regulatory networks with GFlowNets. Neural Information Processing Systems (NeurIPS), 2023.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "On free energy barriers in Gaussian priors and failure of cold start MCMC for high-dimensional unimodal distributions", "authors": [{"first": "A", "middle": ["S"], "last": "Bandeira", "suffix": ""}, {"first": "A", "middle": [], "last": "Maillard", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Philosophical transactions. Series A, Mathematical, physical, and engineering sciences", "volume": "381", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and Wang, S. On free energy barriers in Gaussian priors and failure of cold start MCMC for high-dimensional unimodal distributions. Philosophical transactions. Series A, Mathematical, physical, and engineering sciences, 381, 2022.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Flow network based generative models for non-iterative diverse candidate generation", "authors": [{"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Precup", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, D<PERSON>, and <PERSON><PERSON>, Y. Flow network based generative models for non-iterative diverse candidate generation. Neural Information Processing Systems (NeurIPS), 2021.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "An optimal control perspective on diffusion-based generative modeling", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.01364"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. An optimal control perspective on diffusion-based generative modeling. arXiv preprint arXiv:2211.01364, 2022.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "From discrete-time policies to continuous-time diffusion samplers: Asymptotic equivalences and faster training", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Sendera", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2025, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2501.06148"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON>. From discrete-time policies to continuous-time diffusion samplers: Asymptotic equivalences and faster training. arXiv preprint arXiv:2501.06148, 2025.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Nested sampling methods", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2101.09675"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> sampling methods. arXiv preprint arXiv:2101.09675, 2021.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Importance weighted autoencoders", "authors": [{"first": "Y", "middle": [], "last": "Burda", "suffix": ""}, {"first": "R", "middle": ["B"], "last": "Grosse", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Importance weighted autoencoders. Interna- tional Conference on Learning Representations (ICLR), 2016.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Likelihood training of Schrödinger bridge using forward-backward SDEs theory", "authors": [{"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G.-H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, E<PERSON> A. Likelihood training of Schrödinger bridge using forward-backward SDEs theory. International Conference on Learning Representations (ICLR), 2022.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "A sequential particle filter method for static models", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2002, "venue": "Biometrika", "volume": "89", "issue": "3", "pages": "539--552", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>. A sequential particle filter method for static models. Biometrika, 89(3):539-552, 2002.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Relaxing bijectivity constraints with continuously indexed normalising flows", "authors": [{"first": "R", "middle": [], "last": "Cornish", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Relaxing bijectivity constraints with continuously indexed normalising flows. International Conference on Machine Learning (ICML), 2020.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Convergence of denoising diffusion models under the manifold hypothesis", "authors": [{"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> Convergence of denoising diffusion models under the manifold hypothesis. Transactions on Machine Learning Research (TMLR), 2022.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Sequential Monte Carlo samplers", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2006, "venue": "Journal of the Royal Statistical Society Series B: Statistical Methodology", "volume": "68", "issue": "3", "pages": "411--436", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> samplers. Journal of the Royal Statistical Society Series B: Statistical Methodology, 68(3):411-436, 2006.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Bayesian structure learning with generative flow networks", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>ez<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Rankawat", "suffix": ""}, {"first": "S", "middle": [], "last": "Lacoste-Julien", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Uncertainty in Artificial Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, Y. Bay<PERSON>an structure learning with generative flow networks. Uncertainty in Artificial Intelligence (UAI), 2022.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Discrete probabilistic inference as control in multi-path environments", "authors": [{"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Precup", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Uncertainty in Artificial Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, D<PERSON>, and <PERSON><PERSON>, <PERSON>. <PERSON>rete probabilistic inference as control in multi-path environments. Uncertainty in Artificial Intelligence (UAI), 2024.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Density estimation using Real NVP", "authors": [{"first": "L", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>, S. Density estimation using Real NVP. International Conference on Learning Representations (ICLR), 2017.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "An entropy approach to the time reversal of diffusion processes", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1985, "venue": "", "volume": "", "issue": "", "pages": "156--163", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> An entropy approach to the time reversal of diffusion processes. pp. 156-163, 1985.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "High-dimensional integration and sampling with normalizing flows", "authors": [{"first": "C", "middle": [], "last": "Gao", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Machine Learning: Science and Technology", "volume": "1", "issue": "4", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, C. i-flow: High-dimensional integration and sampling with normalizing flows. Machine Learning: Science and Technology, 1(4):045023, 2020.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "FFJORD: Freeform continuous dynamics for scalable reversible generative models", "authors": [{"first": "W", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Bettencourt", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, J<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, D. FFJORD: Free- form continuous dynamics for scalable reversible generative models. International Conference on Learning Representations (ICLR), 2019.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Representations of knowledge in complex systems", "authors": [{"first": "U", "middle": [], "last": "Grenander", "suffix": ""}, {"first": "M", "middle": ["I"], "last": "<PERSON>", "suffix": ""}], "year": 1994, "venue": "Journal of the Royal Statistical Society: Series B (Methodological)", "volume": "56", "issue": "4", "pages": "549--581", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> <PERSON>. Representations of knowledge in complex systems. Journal of the Royal Statistical Society: Series B (Methodological), 56(4):549-581, 1994.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Sequential Monte Carlo", "authors": [{"first": "J", "middle": ["H"], "last": "Halton", "suffix": ""}], "year": 1962, "venue": "Mathematical Proceedings of the Cambridge Philosophical Society", "volume": "58", "issue": "", "pages": "57--78", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>. In Mathematical Proceedings of the Cambridge Philosophical Society, volume 58, pp. 57-78. Cambridge University Press, 1962.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Variational Bayesian last layers", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, J. Variational Bayesian last layers. International Conference on Learning Representations (ICLR), 2024.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Probabilistic backpropagation for scalable learning of Bayesian neural networks", "authors": [{"first": "J", "middle": ["M"], "last": "Hernández<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON> and <PERSON>, <PERSON><PERSON>babilistic backpropagation for scalable learning of Bayesian neural networks. International Conference on Machine Learning (ICML), 2015.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Denoising diffusion probabilistic models", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Denoising diffusion probabilistic models. Neural Information Processing Systems (NeurIPS), 2020.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "NeuTralizing bad geometry in Hamiltonian Monte Carlo using neural transport", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["V"], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Tran", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1903.03704"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, S. NeuTra- lizing bad geometry in Hamiltonian Monte Carlo using neural transport. arXiv preprint arXiv:1903.03704, 2019.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Stochastic variational inference", "authors": [{"first": "M", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["W"], "last": "Paisley", "suffix": ""}], "year": 2013, "venue": "Journal of Machine Learning Research (JMLR)", "volume": "14", "issue": "", "pages": "1303--1347", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> <PERSON>. Stochastic variational inference. Journal of Machine Learning Research (JMLR), 14:1303-1347, 2013.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "The No-U-Turn sampler: adaptively setting path lengths in Hamiltonian Monte Carlo", "authors": [{"first": "M", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "Journal of Machine Learning Research (JMLR)", "volume": "15", "issue": "1", "pages": "1593--1623", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, et al. The No-U-Turn sampler: adaptively setting path lengths in Hamiltonian Monte Carlo. Journal of Machine Learning Research (JMLR), 15(1):1593-1623, 2014.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Stochastic optimal control for collective variable free sampling of molecular transition paths", "authors": [{"first": "L", "middle": [], "last": "Holdijk", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Hooft", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Ensing", "suffix": ""}, {"first": "M", "middle": [], "last": "Welling", "suffix": ""}], "year": null, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>ing, M. <PERSON>astic optimal control for collective variable free sampling of molecular transition paths. Neural Information Processing Systems (NeurIPS), 2023.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "GFlowNet-EM for learning compositional latent variable models", "authors": [{"first": "E", "middle": ["J"], "last": "Hu", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "G<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, A., and <PERSON>, Y. GFlowNet-EM for learning compositional latent variable models. International Conference on Machine Learning (ICML), 2023.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Amortizing intractable inference in large language models. International Conference on Learning Representations (ICLR)", "authors": [{"first": "E", "middle": ["J"], "last": "Hu", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Y., and <PERSON>, <PERSON><PERSON>- tizing intractable inference in large language models. International Conference on Learning Representations (ICLR), 2024.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "What are Bayesian neural network posteriors really like? International Conference on Machine Learning (ICML)", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["G"], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>. What are Bayesian neural network posteriors really like? International Conference on Machine Learning (ICML), 2021.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Biological sequence design with gflownets", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": ["F"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": ["A"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, B<PERSON>, <PERSON><PERSON>, C. A., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Biological sequence design with gflownets. International Conference on Machine Learning (ICML), 2022.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Learning energy decompositions for partial inference of GFlowNets", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ahn", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, S. Learning energy decompositions for partial inference of GFlowNets. International Conference on Learning Representations (ICLR), 2024.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Torsional diffusion for molecular conformer generation", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "Corso", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Jaakkola", "suffix": ""}], "year": null, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>, T. Torsional diffusion for molecular conformer generation. Neural Information Processing Systems (NeurIPS), 2022.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Learning to scale logits for temperature-conditional GFlowNets", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Ko", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Pan", "suffix": ""}, {"first": "T", "middle": [], "last": "Yun", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Park", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.02823"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, J., and Bengio, Y. Learning to scale logits for temperature-conditional GFlowNets. arXiv preprint arXiv:2310.02823, 2023.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Local search GFlowNets. International Conference on Learning Representations (ICLR)", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Yun", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Ahn", "suffix": ""}, {"first": "J", "middle": [], "last": "Park", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Bengio, Y., Ahn, S., and Park, J. Local search GFlowNets. International Conference on Learning Representations (ICLR), 2024.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Auto-encoding variational Bayes", "authors": [{"first": "D", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Welling", "suffix": ""}], "year": 2014, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, D. P<PERSON> and Welling, M. Auto-encoding variational Bayes. International Conference on Learning Representations (ICLR), 2014.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "A theory of continuous generative flow networks", "authors": [{"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Lemos", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Volokhova", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": ["N"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, N. A theory of continuous generative flow networks. International Conference on Machine Learning (ICML), 2023.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Improving gradient-guided nested sampling for posterior inference", "authors": [{"first": "P", "middle": [], "last": "Lemos", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "Hezaveh", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.03911"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Improving gradient-guided nested sampling for posterior inference. arXiv preprint arXiv:2312.03911, 2023.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Learning GFlowNets from partial episodes for improved convergence and stability", "authors": [{"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Nica", "suffix": ""}, {"first": "T", "middle": [], "last": "Bosc", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, A<PERSON>, <PERSON>, T., <PERSON>, Y., and <PERSON><PERSON>, N. Learning GFlowNets from partial episodes for improved convergence and stability. International Conference on Machine Learning (ICML), 2022.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Trajectory balance: Improved credit assignment in gflownets. Neural Information Processing Systems (NeurIPS)", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C., and <PERSON><PERSON>, Y. Trajectory balance: Improved credit assignment in gflownets. Neural Information Processing Systems (NeurIPS), 2022.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "GFlowNets and variational inference. International Conference on Learning Representations (ICLR)", "authors": [{"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "X", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Hu", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, Y. G<PERSON>lowNets and variational inference. International Conference on Learning Representations (ICLR), 2023.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Learning interpolations between <PERSON>ltzmann densities", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Learning interpolations between Boltzmann densities. Transactions on Machine Learning Research (TMLR), 2023.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Exploring exchangeable dataset amortization for bayesian posterior inference", "authors": [{"first": "S", "middle": [], "last": "Mittal", "suffix": ""}, {"first": "N", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICML 2023 Workshop on Structured Probabilistic Inference {\\&} Generative Modeling", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, M. A. Exploring exchangeable dataset amortization for bayesian posterior inference. In ICML 2023 Workshop on Structured Probabilistic Inference {\\&} Generative Modeling, 2023.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Log Gaussian Cox processes", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Syversveen", "suffix": ""}, {"first": "R", "middle": [], "last": "Waagepetersen", "suffix": ""}], "year": 1998, "venue": "Scandinavian Journal of Statistics", "volume": "25", "issue": "3", "pages": "451--482", "other_ids": {"ISSN": ["0303-6898"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> processes. Scandinavian Journal of Statistics, 25(3):451-482, 1998. ISSN 0303-6898.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Improved denoising diffusion probabili1stic models", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON>mproved denoising diffusion probabili1stic models. International Conference on Machine Learning (ICML), 2021.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Asymptotically unbiased estimation of physical observables with neural samplers", "authors": [{"first": "K", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Nakajima", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "Same<PERSON>", "suffix": ""}, {"first": "K.-R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "Kessel", "suffix": ""}], "year": 2020, "venue": "Physical Review E", "volume": "101", "issue": "2", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON>. Asymptot- ically unbiased estimation of physical observables with neural samplers. Physical Review E, 101(2):023304, 2020.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Boltzmann generators: Sampling equilibrium states of many-body systems with deep learning", "authors": [{"first": "F", "middle": [], "last": "Noé", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Science", "volume": "365", "issue": "6457", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> generators: Sampling equilibrium states of many-body systems with deep learning. Science, 365(6457):eaaw1147, 2019.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Solving high-dimensional Hamilton-Jacobi-Bellman PDEs using neural networks: perspectives from the theory of controlled diffusions and measures on path space", "authors": [{"first": "N", "middle": [], "last": "Nüsken", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Partial Differential Equations and Applications", "volume": "2", "issue": "4", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> high-dimensional Hamilton-Jacobi-Bellman PDEs using neural networks: perspectives from the theory of controlled diffusions and measures on path space. Partial Differential Equations and Applications, 2(4):48, 2021.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Stochastic Differential Equations: An Introduction with Applications", "authors": [{"first": "B", "middle": [], "last": "Øksendal", "suffix": ""}], "year": 2003, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Stochastic Differential Equations: An Introduction with Applications. Springer, 2003.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Better training of GFlowNets with local credit and incomplete trajectories", "authors": [{"first": "L", "middle": [], "last": "Pan", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, Y. Better training of GFlowNets with local credit and incomplete trajectories. International Conference on Machine Learning (ICML), 2023.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Optimal scaling and diffusion limits for the langevin algorithm in high dimensions", "authors": [{"first": "N", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": ["H"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "The Annals of Applied Probability", "volume": "22", "issue": "6", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, A. H. Optimal scaling and diffusion limits for the langevin algorithm in high dimensions. The Annals of Applied Probability, 22(6), December 2012.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Learning complex stochastic models with invertible neural networks", "authors": [{"first": "S", "middle": ["T"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "U", "middle": ["K"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "U", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Bayesflow", "suffix": ""}], "year": 2020, "venue": "IEEE transactions on neural networks and learning systems", "volume": "33", "issue": "", "pages": "1452--1466", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, U. <PERSON>: Learning complex stochastic models with invertible neural networks. IEEE transactions on neural networks and learning systems, 33(4):1452-1466, 2020.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Black box variational inference", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "Artificial Intelligence and Statistics (AISTATS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> box variational inference. Artificial Intelligence and Statistics (AISTATS), 2014.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Thompson sampling for improved exploration in GFlowNets", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C.-H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.17693"]}, "num": null, "urls": [], "raw_text": "Rector-<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S., <PERSON>, N., and <PERSON><PERSON>, <PERSON><PERSON> sampling for improved exploration in GFlowNets. arXiv preprint arXiv:2306.17693, 2023.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Variational inference with normalizing flows", "authors": [{"first": "D", "middle": [], "last": "Rezende", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, S. Variational inference with normalizing flows. International Conference on Machine Learning (ICML), 2015.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Stochastic backpropagation and approximate inference in deep generative models", "authors": [{"first": "D", "middle": ["J"], "last": "Rezende", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, D. Stochastic backpropagation and approximate inference in deep generative models. International Conference on Machine Learning (ICML), 2014.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "VarGrad: A low-variance gradient estimator for variational inference", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Boustati", "suffix": ""}, {"first": "N", "middle": [], "last": "Nüsken", "suffix": ""}, {"first": "F", "middle": ["J R"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Akyildiz", "middle": [], "last": "", "suffix": ""}], "year": 2020, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. VarGrad: A low-variance gradient estimator for variational inference. Neural Information Processing Systems (NeurIPS), 2020.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Improved sampling via learned diffusions", "authors": [{"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "G.-H", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> Improved sampling via learned diffusions. International Conference on Learning Representations (ICLR), 2023.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Optimal scaling of discrete approximations to langevin diffusions", "authors": [{"first": "G", "middle": ["O"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["S"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1998, "venue": "Journal of the Royal Statistical Society: Series B (Statistical Methodology)", "volume": "60", "issue": "1", "pages": "255--268", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, J. <PERSON>. Optimal scaling of discrete approximations to langevin diffusions. Journal of the Royal Statistical Society: Series B (Statistical Methodology), 60(1): 255-268, 1998.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Exponential convergence of Langevin distributions and their discrete approximations", "authors": [{"first": "G", "middle": ["O"], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1996, "venue": "<PERSON><PERSON><PERSON>", "volume": "", "issue": "", "pages": "341--363", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, R. L. Exponential convergence of Langevin distributions and their discrete approximations. Bernoulli, pp. 341-363, 1996.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "High-resolution image synthesis with latent diffusion models", "authors": [{"first": "R", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Ommer", "suffix": ""}], "year": null, "venue": "Conference on Computer Vision and Pattern Recognition (CVPR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, B. High-resolution image synthesis with latent diffusion models. Conference on Computer Vision and Pattern Recognition (CVPR), 2021.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "Applied stochastic differential equations", "authors": [{"first": "S", "middle": [], "last": "Särkkä", "suffix": ""}, {"first": "A", "middle": [], "last": "Solin", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>. Applied stochastic differential equations. Cambridge University Press, 2019.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "Towards understanding and improving GFlowNet training. International Conference on Machine Learning (ICML)", "authors": [{"first": "M", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Cho", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, T. To- wards understanding and improving GFlowNet training. International Conference on Machine Learning (ICML), 2023.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "Nested sampling for general Bayesian computation", "authors": [{"first": "J", "middle": [], "last": "Skilling", "suffix": ""}], "year": 2006, "venue": "Bayesian Analysis", "volume": "1", "issue": "4", "pages": "833--859", "other_ids": {"DOI": ["10.1214/06-BA127"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> sampling for general Bayesian computation. Bayesian Analysis, 1(4):833 - 859, 2006. doi: 10.1214/06-BA127. URL https://doi.org/10.1214/06-BA127.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "Deep unsupervised learning using nonequilibrium thermodynamics", "authors": [{"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, S. Deep unsupervised learning using nonequilibrium thermodynamics. International Conference on Machine Learning (ICML), 2015.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "Maximum likelihood training of score-based diffusion models", "authors": [{"first": "Y", "middle": [], "last": "Song", "suffix": ""}, {"first": "C", "middle": [], "last": "Durkan", "suffix": ""}, {"first": "I", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, S. Maximum likelihood training of score-based diffusion models. Neural Information Processing Systems (NeurIPS), 2021.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "Score-based generative modeling through stochastic differential equations", "authors": [{"first": "Y", "middle": [], "last": "Song", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "Poole", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S., and <PERSON>, B. Score-based generative modeling through stochastic differential equations. International Conference on Learning Representations (ICLR), 2021.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "Generative flow networks as entropyregularized RL", "authors": [{"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Morozov", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.12934"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, D. Generative flow networks as entropy- regularized RL. arXiv preprint arXiv:2310.12934, 2023.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "Sample-efficient optimization in the latent space of deep generative models via weighted retraining", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["M"], "last": "Hernández<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> <PERSON>. <PERSON>-efficient optimization in the latent space of deep generative models via weighted retraining. Neural Information Processing Systems (NeurIPS), 2020.", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "Neural stochastic differential equations: Deep latent Gaussian models in the diffusion limit", "authors": [{"first": "B", "middle": [], "last": "Tzen", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1905.09883"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> stochastic differential equations: Deep latent Gaussian models in the diffusion limit. arXiv preprint arXiv:1905.09883, 2019.", "links": null}, "BIBREF75": {"ref_id": "b75", "title": "Theoretical guarantees for sampling and inference in generative models with latent diffusions", "authors": [{"first": "B", "middle": [], "last": "Tzen", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Conference on Learning Theory (CoLT)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>retical guarantees for sampling and inference in generative models with latent diffusions. Conference on Learning Theory (CoLT), 2019.", "links": null}, "BIBREF76": {"ref_id": "b76", "title": "A scalable approximate method for probabilistic neurosymbolic inference. Neural Information Processing Systems (NeurIPS)", "authors": [{"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "Thanapalasingam", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, A. A-NeSI: A scalable approximate method for probabilistic neurosymbolic inference. Neural Information Processing Systems (NeurIPS), 2023.", "links": null}, "BIBREF77": {"ref_id": "b77", "title": "Denoising diffusion samplers", "authors": [{"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "W", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Denoising diffusion samplers. International Conference on Learning Representations (ICLR), 2023.", "links": null}, "BIBREF78": {"ref_id": "b78", "title": "Transport meets variational inference: Controlled Monte Carlo diffusions", "authors": [{"first": "F", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "Pa<PERSON><PERSON>", "suffix": ""}, {"first": "D", "middle": [], "last": "Blessing", "suffix": ""}, {"first": "N", "middle": [], "last": "Nüsken", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, N. Transport meets variational inference: Controlled Monte Carlo diffusions. International Conference on Learning Representations (ICLR), 2024.", "links": null}, "BIBREF79": {"ref_id": "b79", "title": "A connection between score matching and denoising autoencoders", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2011, "venue": "Neural computation", "volume": "23", "issue": "7", "pages": "1661--1674", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> connection between score matching and denoising autoencoders. Neural compu- tation, 23(7):1661-1674, 2011.", "links": null}, "BIBREF80": {"ref_id": "b80", "title": "Stochastic normalizing flows. Neural Information Processing Systems (NeurIPS)", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "Noé", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>astic normalizing flows. Neural Information Processing Systems (NeurIPS), 2020.", "links": null}, "BIBREF81": {"ref_id": "b81", "title": "Tackling the generative learning trilemma with denoising diffusion GANs", "authors": [{"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "Kreis", "suffix": ""}, {"first": "A", "middle": [], "last": "Vah<PERSON>t", "suffix": ""}], "year": null, "venue": "International Conference on Leraning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> Tackling the generative learning trilemma with denoising diffusion GANs. International Conference on Leraning Representations (ICLR), 2022.", "links": null}, "BIBREF82": {"ref_id": "b82", "title": "Generative flow networks for discrete probabilistic modeling", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Volokhova", "suffix": ""}, {"first": "A", "middle": [], "last": "Courville", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Machine Learning (ICML)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, A., Courville, A., and Bengio, Y. Generative flow networks for discrete probabilistic modeling. International Conference on Machine Learning (ICML), 2022.", "links": null}, "BIBREF83": {"ref_id": "b83", "title": "Unifying generative models with GFlowNets and beyond", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": ["T Q"], "last": "<PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2209.02606"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, Y. Unifying generative models with GFlowNets and beyond. arXiv preprint arXiv:2209.02606, 2023.", "links": null}, "BIBREF84": {"ref_id": "b84", "title": "Robust scheduling with GFlowNets", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON> scheduling with GFlowNets. International Conference on Learning Representations (ICLR), 2023.", "links": null}, "BIBREF85": {"ref_id": "b85", "title": "Diffusion generative flow samplers: Improving learning signals through partial trajectory optimization", "authors": [{"first": "D", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": ["T Q"], "last": "<PERSON>", "suffix": ""}, {"first": "C.-H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Courville", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Courville, A., and Bengio, Y. Diffusion generative flow samplers: Improving learning signals through partial trajectory optimization. International Conference on Learning Representations (ICLR), 2024.", "links": null}, "BIBREF86": {"ref_id": "b86", "title": "Diffusion normalizing flow. Neural Information Processing Systems (NeurIPS)", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> normalizing flow. Neural Information Processing Systems (NeurIPS), 2021.", "links": null}, "BIBREF87": {"ref_id": "b87", "title": "Path integral sampler: a stochastic control approach for sampling", "authors": [{"first": "Q", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations (ICLR)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> integral sampler: a stochastic control approach for sampling. International Conference on Learning Representations (ICLR), 2022.", "links": null}, "BIBREF88": {"ref_id": "b88", "title": "Sample-efficient multiobjective molecular optimization with GFlowNets", "authors": [{"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "Hu", "suffix": ""}, {"first": "J", "middle": [], "last": "Yan", "suffix": ""}, {"first": "C.-Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "T", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Neural Information Processing Systems (NeurIPS)", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>-efficient multi- objective molecular optimization with GFlowNets. Neural Information Processing Systems (NeurIPS), 2023.", "links": null}, "BIBREF89": {"ref_id": "b89", "title": "A variational perspective on generative flow networks", "authors": [{"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "J.-W", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Transactions on Machine Learning Research", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>, C. A. A variational perspective on generative flow networks. Transactions on Machine Learning Research (TMLR), 2023.", "links": null}}, "ref_entries": {"FIGREF0": {"uris": null, "num": null, "type_str": "figure", "text": "Figure 1: Two-dimensional projections of Manywell samples from models trained by different algorithms. Our proposed replay buffer with local search is capable of preventing mode collapse.To enhance the quality of samples during training, we incorporate local search into the exploration process, motivated by the success of local exploration[83,33,40] and replay buffer [e.g., 17] methods for GFlowNets in discrete spaces. Unlike these methods, which define MCMC kernels via the GFlowNet policies, our method leverages parallel Metropolis-adjusted Langevin (MALA) directly in the target space.", "fig_num": "1"}, "FIGREF1": {"uris": null, "num": null, "type_str": "figure", "text": "Figure2: Effect of exploration variance on models trained with TB on the 25GMM energy. Exploration promotes mode discovery, but should be decayed over time to optimally allocate the modeling power to high-likelihood trajectories.", "fig_num": "2"}, "FIGREF3": {"uris": null, "num": null, "type_str": "figure", "text": "Figure 3: Left: Distribution of x 0 , x 0.1 , . . . , x 1 learned by 10-step samplers with fixed (top) and learned (middle) forward policy variance on the 25GMM energy. The last step of sampling the fixed-variance model adds Gaussian noise of a variance close to that of the components of the target distribution, preventing the the sampler from sharply capturing the modes. The last row shows the policy variance learned as a function of x 𝑡 at various time steps 𝑡 (white is high variance, blue is low), showing that less noise is added around the peaks near 𝑡 = 1. The two models' log-partition function estimates are -1.67 and -0.62, respectively. Right: For varying number of steps 𝑇, we plot the log Ẑ obtained by models with fixed and learned variance. Learning policy variances gives similar samplers with fewer steps.", "fig_num": "3"}, "FIGREF4": {"uris": null, "num": null, "type_str": "figure", "text": "python train.py --t_scale 1. --energy many_well --pis_architectures --zero_init --clipping --mode_fwd tb --lr_policy 1e-3 --lr_flow 1e-1 GFlowNet TB + Expl.: python train.py --t_scale 1. --energy many_well --pis_architectures --zero_init --clipping --mode_fwd tb --lr_policy 1e-3 --lr_flow 1e-1 --exploratory --exploration_wd --exploration_factor 0.2 GFlowNet VarGrad + Expl.: python train.py --t_scale 1. --energy many_well --pis_architectures --zero_init --clipping --mode_fwd tb-avg --lr_policy 1e-3 --lr_flow 1e-1 --exploratory --exploration_wd --exploration_factor 0.2 GFlowNet FL-SubTB: python train.py --t_scale 1. --energy many_well --pis_architectures --zero_init --clipping --mode_fwd subtb --lr_policy 1e-3 --lr_flow 1e-2 --partial_energy --conditional_flow_model GFlowNet FL-SubTB + LP: python train.py --t_scale 1. --energy many_well --pis_architectures --zero_init --clipping --mode_fwd subtb --lr_policy 1e-3 --lr_flow 1e-2 --partial_energy --conditional_flow_model --langevin --epochs 10000 GFlowNet TB + Expl. + LS: python train.py --t_scale 1. --energy many_well --pis_architectures --zero_init --clipping --mode_fwd tb --lr_policy 1e-3 --lr_back 1e-3 --lr_flow 1e-1 --exploratory --exploration_wd --exploration_factor 0.1 --both_ways --local_search --buffer_size 600000 --prioritized rank --rank_weight 0.01 --ld_step 0.1 --ld_schedule --target_acceptance_rate 0.574 GFlowNet TB + Expl. + LP: python train.py --t_scale 1. --energy many_well --pis_architectures --zero_init --clipping --mode_fwd tb --lr_policy 1e-3 --lr_flow 1e-1 --exploratory --exploration_wd --exploration_factor 0.2 --langevin --epochs 10000 GFlowNet TB + Expl. + LS (VAE):python train.py --energy vae --pis_architectures --zero_init --clipping --mode_fwd cond-tb-avg --mode_bwd cond-tb-avg --repeats 5 --lr_policy 1e-3 --lr_flow 1e-1 --lr_back 1e-3 --exploratory --exploration_wd --exploration_factor 0.1 --both_ways --local_search --max_iter_ls 500 --burn_in 200 --buffer_size 90000 --prioritized rank --rank_weight 0.01 --ld_step 0.001 --ld_schedule --target_acceptance_rate 0.574 GFlowNet TB + Expl. + LP + LS (VAE): python train.py --energy vae --pis_architectures --zero_init --clipping --mode_fwd cond-tb-avg --mode_bwd cond-tb-avg --repeats 5 --lr_policy 1e-3 --lr_flow 1e-1 --lgv_clip 1e2 --gfn_clip 1e4 --epochs 10000 --exploratory --exploration_wd --exploration_factor 0.1 --both_ways --local_search --lr_back 1e-3 --max_iter_ls 500 --burn_in 200 --buffer_size 90000 --prioritized rank --rank_weight 0.01 --langevin --ld_step 0.001 --ld_schedule --target_acceptance_rate 0.574", "fig_num": null}, "FIGREF5": {"uris": null, "num": null, "type_str": "figure", "text": "Figure D.1: Our sampler (VarGrad + Expl. + LP) is conditioned by a subset of never-seen data coming from the ground truth distribution (left). The conditional samples were then decoded by the the fixed VAE (middle). For the comparison, we show the reconstruction of the real data by VAE (right). We observed that the decoded samples are visually very similar to the reconstructions making these two pictures almost indistinguishable. Both, decoded samples and reconstruction, are more blurry than the ground truth data, which is caused by a limited capacity of the VAE's latent space.", "fig_num": null}, "FIGREF6": {"uris": null, "num": null, "type_str": "figure", "text": "Figure E.1: Illustration of each sampler trained with varying capacities of replay buffers, depicting 2,000 samples. As the capacity of the buffer increases, the number of modes captured by the sampler also increases. Benefit of prioritization. Rank-prioritized sampling gives faster convergence compared with no prioritization (uniform sampling), as shown in Fig. E.2a. Dynamic adjustment of 𝜂 vs. fixed 𝜂 = 0.01. As shown in Fig. E.2b, dynamic adjustment to target acceptance rate 𝛼 target = 0.574 gives better performances than fixed Langevin step size of 𝜂 showcasing the effectiveness of the dynamic adjustment.", "fig_num": "1"}, "FIGREF7": {"uris": null, "num": null, "type_str": "figure", "text": "Benefit of scheduling 𝜂 dynamically.", "fig_num": null}, "FIGREF8": {"uris": null, "num": null, "type_str": "figure", "text": "Figure E.2: Ablation study for prioritized replay buffer and step size 𝜂 scheduling of local search. Mean and standard deviation are plotted based on five independent runs.", "fig_num": "2"}, "TABREF0": {"content": "<table/>", "num": null, "type_str": "table", "text": "Log-partition function estimation errors for unconditional modeling tasks (mean and standard deviation over 5 runs). The four groups of models are: MCMC-based samplers, simulation-driven variational methods, baseline GFlowNet methods with different learning objectives, and methods augmented with Langevin parametrization and local search. See §C.1 for additional metrics.", "html": null}, "TABREF2": {"content": "<table><tr><td>Algorithm ↓ Metric →</td><td>log Ẑ</td><td>log ẐRW</td></tr><tr><td>GGNS [43]</td><td colspan=\"2\">-82.406±0.882</td></tr><tr><td>PIS [88]</td><td>-102.54±0.437</td><td>-47.753±2.821</td></tr><tr><td>+ LP [88]</td><td>-99.890±0.373</td><td>-47.326±0.777</td></tr><tr><td>TB [42]</td><td>-162.73±35.55</td><td>-61.407±17.83</td></tr><tr><td>VarGrad</td><td>-102.54±0.934</td><td>-46.502±1.018</td></tr><tr><td>TB + Expl. [42]</td><td>-148.04±4.046</td><td>-49.967±5.683</td></tr><tr><td>FL-SubTB</td><td>-147.992±22.671</td><td>-54.196±3.996</td></tr><tr><td>+ LP [86]</td><td>-111.536±1.027</td><td>-47.640±1.313</td></tr><tr><td>TB + Expl. + LS (ours)</td><td>-245.78±13.80</td><td>-55.378±9.125</td></tr><tr><td>TB + Expl. + LP (ours)</td><td>-112.45±0.671</td><td>-48.827±1.787</td></tr><tr><td>TB + Expl. + LP + LS (ours)</td><td>-117.26±2.502</td><td>-49.157±2.051</td></tr><tr><td>VarGrad + Expl. (ours)</td><td>-103.39±0.691</td><td>-47.318±1.981</td></tr><tr><td>VarGrad + Expl. + LS (ours)</td><td>-105.40±0.882</td><td>-48.235±0.891</td></tr><tr><td>VarGrad + Expl. + LP (ours)</td><td>-99.472±0.259</td><td>-46.574±0.736</td></tr><tr><td>VarGrad + Expl. + LP + LS (ours)</td><td>-99.783±0.312</td><td>-46.245±0.543</td></tr></table>", "num": null, "type_str": "table", "text": "Log-likelihood estimates on a test set for a pretrained VAE decoder on MNIST. The latent being sampled is 20-dimensional. The VAE's training ELBO (Gaussian encoder) was ≈ -101.", "html": null}, "TABREF3": {"content": "<table/>", "num": null, "type_str": "table", "text": "", "html": null}, "TABREF4": {"content": "<table><tr><td>Energy →</td><td/><td>25GMM (𝑑 = 2)</td><td/><td/><td>Funnel (𝑑 = 10)</td><td/><td colspan=\"3\">Manywell (𝑑 = 32)</td></tr><tr><td>Algorithm ↓ Metric →</td><td>Δ log 𝑍</td><td>Δ log 𝑍 RW</td><td>W 2 2</td><td>Δ log 𝑍</td><td>Δ log 𝑍 RW</td><td>W 2 2</td><td>Δ log 𝑍</td><td>Δ log 𝑍 RW</td><td>W 2 2</td></tr><tr><td>SMC</td><td colspan=\"2\">0.569±0.010</td><td>0.86±0.10</td><td colspan=\"2\">0.561±0.801</td><td>50.3±18.9</td><td colspan=\"2\">14.99±1.078</td><td>8.28±0.32</td></tr><tr><td>GGNS [43]</td><td colspan=\"2\">0.016±0.042</td><td>1.19±0.17</td><td colspan=\"2\">0.033±0.173</td><td>25.6±4.75</td><td colspan=\"2\">0.292±0.454</td><td>6.51±0.32</td></tr><tr><td>DIS [8]</td><td>1.125±0.056</td><td>0.986±0.011</td><td>4.71±0.06</td><td>0.839±0.169</td><td>0.093±0.038</td><td>20.7±2.1</td><td>10.52±1.02</td><td>3.05±0.46</td><td>5.98±0.46</td></tr><tr><td>DDS [78]</td><td>1.760±0.08</td><td>0.746±0.389</td><td>7.18±0.044</td><td>0.424±0.049</td><td>0.206±0.033</td><td>29.3±9.5</td><td>7.36±2.43</td><td>0.23±0.05</td><td>5.71±0.16</td></tr><tr><td>PIS [88]</td><td>1.769±0.104</td><td>1.274±0.218</td><td>6.37±0.65</td><td>0.534±0.008</td><td>0.262±0.008</td><td>22.0±4.0</td><td>3.85±0.03</td><td>2.69±0.04</td><td>6.15±0.02</td></tr><tr><td>+ LP [88]</td><td>1.799±0.051</td><td>0.225±0.583</td><td>7.16±0.11</td><td>0.587±0.012</td><td>0.285±0.044</td><td>22.1±4.0</td><td>13.19±0.82</td><td>0.07±0.85</td><td>6.55±0.34</td></tr><tr><td>TB [42]</td><td>1.176±0.109</td><td>1.071±0.112</td><td>4.83±0.45</td><td>0.690±0.018</td><td>0.239±0.192</td><td>22.4±4.0</td><td>4.01±0.04</td><td>2.67±0.02</td><td>6.14±0.02</td></tr><tr><td>TB + Expl. [42]</td><td>0.560±0.302</td><td>0.422±0.320</td><td>3.61±1.41</td><td>0.749±0.015</td><td>0.226±0.138</td><td>21.3±4.0</td><td>4.01±0.05</td><td>2.68±0.06</td><td>6.15±0.02</td></tr><tr><td>VarGrad + Expl.</td><td>0.615±0.241</td><td>0.487±0.250</td><td>3.89±0.85</td><td>0.642±0.010</td><td>0.250±0.112</td><td>22.1±4.0</td><td>4.01±0.05</td><td>2.69±0.06</td><td>6.15±0.02</td></tr><tr><td>FL-SubTB</td><td>1.127±0.010</td><td>1.020±0.010</td><td>4.64±0.09</td><td>0.527±0.011</td><td>0.182±0.142</td><td>22.1±4.0</td><td>3.98±0.07</td><td>2.72±0.05</td><td>6.15 ±0.01</td></tr><tr><td>+ LP [86]</td><td>0.209±0.025</td><td>0.011±0.024</td><td>1.45±0.29</td><td>0.563±0.021</td><td>0.155±0.317</td><td>22.2±4.0</td><td>4.23±0.12</td><td>2.66±0.22</td><td>6.10±0.02</td></tr><tr><td>TB + Expl. + LS (ours)</td><td>0.171±0.013</td><td>0.004±0.011</td><td>1.25±0.18</td><td>0.653±0.025</td><td>0.285±0.099</td><td>21.9±4.0</td><td>4.57±2.13</td><td>0.19±0.29</td><td>5.66±0.05</td></tr><tr><td>TB + Expl. + LP (ours)</td><td>0.206±0.018</td><td>0.011±0.010</td><td>1.29±0.07</td><td>0.666±0.615</td><td>0.051±0.616</td><td>22.3±3.9</td><td>7.46±1.74</td><td>1.06±1.11</td><td>5.73±0.31</td></tr><tr><td>TB + Expl. + LP + LS (ours)</td><td>0.190±0.013</td><td>0.007±0.011</td><td>1.31±0.07</td><td>0.768±0.052</td><td>0.264±0.063</td><td>21.8±3.9</td><td>4.68±0.49</td><td>0.07±0.17</td><td>5.33±0.03</td></tr><tr><td colspan=\"2\">VarGrad + Expl. + LP + LS (ours) 0.207±0.016</td><td>0.015±0.015</td><td>1.13±0.13</td><td>0.920±0.118</td><td>0.256±0.037</td><td>21.2±4.0</td><td>4.11±0.45</td><td>0.02±0.21</td><td>5.30±0.02</td></tr></table>", "num": null, "type_str": "table", "text": "1,  showing <PERSON><PERSON><PERSON> distances between sets of 𝐾 samples from the true distribution and generated by a trained sampler. (Note that ground truth for LGCP is not available.) TableC.1: Log-partition function estimation errors and 2-Was<PERSON><PERSON> distances for unconditional modeling tasks (mean and standard deviation over 5 runs). The four groups of models are: MCMCbased samplers, simulation-driven variational methods, baseline GFlowNet methods with different learning objectives, and methods augmented with Langevin parametrization and local search. : mean indistinguishable from minimum in column with 𝑝 < 0.05 under one-sided Welch unpaired 𝑡-test.", "html": null}, "TABREF5": {"content": "<table><tr><td>Backward process →</td><td/><td>Brownian</td><td/><td/><td>VP</td><td/></tr><tr><td>Objective ↓ Metric →</td><td>Δ log 𝑍</td><td>Δ log 𝑍 RW</td><td>W 2 2</td><td>Δ log 𝑍</td><td>Δ log 𝑍 RW</td><td>W 2 2</td></tr><tr><td>TB + Expl. + LP</td><td colspan=\"6\">7.46±1.74 1.06±1.11 5.73±0.31 7.55±2.85 1.49±1.30 5.68±0.42</td></tr><tr><td colspan=\"7\">TB + Expl. + LP + LS 4.68±0.49 0.07±0.17 5.33±0.03 4.52±0.21 1.23±0.07 5.75±0.01</td></tr><tr><td>VarGrad + Expl.</td><td colspan=\"6\">4.01±0.05 2.69±0.06 6.15±0.02 4.04±0.05 2.65±0.08 6.17±0.02</td></tr></table>", "num": null, "type_str": "table", "text": "2: Log-partition function estimation errors and empirical 2-Was<PERSON><PERSON> distances on the 32-dimensional Manywell with Brownian and variance-preserving noising processes.", "html": null}, "TABREF6": {"content": "<table><tr><td>Dimension → TB [42] Objective ↓ PIS + LP [88]</td><td>0.86 0.95</td><td>𝑑 = 8</td><td>0.14 0.70</td><td>13.19 4.01</td><td>𝑑 = 32 0.07 2.68</td><td colspan=\"2\">𝑑 = 128 23.7 205.6 58.0 119.8</td><td>251 1223</td><td>𝑑 = 512 169 957</td><td>s / iter</td><td>10 1 10 2</td><td>PIS + LP TB FL-SubTB + LP TB + LP TB + LS TB + LP + LS</td></tr><tr><td>FL-SubTB + LP [86] TB + LP</td><td>0.57 0.25</td><td/><td>0.67 0.04</td><td>4.23 7.46</td><td>2.66 1.06</td><td>48.9 46.4</td><td>21.7 14.0</td><td>198 259</td><td>107 169</td><td/><td>10 0</td></tr><tr><td>TB + LS</td><td>0.44</td><td/><td>0.00</td><td>4.57</td><td>0.19</td><td>458.7</td><td>139.3</td><td>1626</td><td>1077</td><td/><td/></tr><tr><td>TB + LP + LS</td><td>0.25</td><td/><td>0.02</td><td>4.68</td><td>0.07</td><td>66.6</td><td>14.9</td><td>326</td><td>209</td><td/><td/></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td/><td>10 1</td><td>8</td><td>32</td><td>128 Dimension</td><td>512</td></tr></table>", "num": null, "type_str": "table", "text": "3: Scaling with dimension on Manywell: log-partition function estimation errors and time per training iteration on a RTX8000 GPU. Metric → Δ log 𝑍 Δ log 𝑍 RW Δ log 𝑍 Δ log 𝑍 RW Δ log 𝑍 Δ log 𝑍 RW Δ log 𝑍 Δ log 𝑍 RW", "html": null}, "TABREF7": {"content": "<table><tr><td colspan=\"2\">7: else</td></tr><tr><td>8:</td><td>if 𝑖%100 == 0 then</td></tr><tr><td colspan=\"2\">9: Sample {𝑥 1 11: end if 12: Sample {𝑥 ′ 1 , . . . , 𝑥 ′ 𝑀 } ∼ 𝑝 buffer (• • • ; D LS ) 13: Sample {𝜏 ′ 1 , . . . , 𝜏 ′ 𝑀 } ∼ 𝑃 𝐵 (• • • |𝑥 ′ ) 14: Minimize 𝐿 (𝜏 ′ ; 𝜃) using {𝜏 ′ 𝑀 } to update 𝑃 𝐹 1 , . . . , 𝜏 ′ 15: end if</td></tr><tr><td colspan=\"2\">16: end for</td></tr></table>", "num": null, "type_str": "table", "text": "600, 000 with first-in first out (FIFO) data structure for every task, except we use |D buffer | = 90, 000 for VAE task. See the algorithm below for a detailed pseudocode. Algorithm 1 GFlowNet Training with Local search 1: Initialize policy parameters 𝜃 for 𝑃 𝐹 , and empty buffers D buffer , D LS 2: for 𝑖 = 1, 2, . . . , 𝐼 do 3: if 𝑖%2 == 0 then 4: Sample 𝑀 trajectories {𝜏 1 , . . . , 𝜏 𝑀 } ∼ 𝑃 𝐹 (•|𝜖-greedy) 5: Update D buffer ← D buffer ∪ {𝑥|𝜏 → 𝑥} 6: Minimize 𝐿 (𝜏; 𝜃) using {𝜏 1 , . . . , 𝜏 𝑀 } to update 𝑃 𝐹 , . . . , 𝑥 𝑀 } ∼ D buffer 10: D LS ← Local Search({𝑥 1 , . . . , 𝑥 𝑀 }; D LS )", "html": null}, "TABREF8": {"content": "<table><tr><td>Buffer Capacity ↓ Metric →</td><td>Δ log 𝑍</td><td>Δ log 𝑍 RW</td><td>W 2 2</td></tr><tr><td>30, 000</td><td colspan=\"3\">4.41±0.10 2.73±0.15 6.17±0.02</td></tr><tr><td>60, 000</td><td colspan=\"3\">4.06±0.05 2.38±0.38 6.14±0.04</td></tr><tr><td>600, 000</td><td colspan=\"3\">4.57±2.13 0.19±0.29 5.66±0.05</td></tr></table>", "num": null, "type_str": "table", "text": "Comparison of the sampling quality of each sampler trained with varying replay buffer capacities in Manywell. Five independent runs have been conducted, with both the mean and standard deviation reported.", "html": null}, "TABREF9": {"content": "<table><tr><td>NeurIPS Paper Checklist</td></tr><tr><td>1. Claims</td></tr><tr><td>Question: Do the main claims made in the abstract and introduction accurately reflect the</td></tr><tr><td>paper's contributions and scope?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: See theory and experiment sections.</td></tr><tr><td>Guidelines:</td></tr><tr><td>•</td></tr></table>", "num": null, "type_str": "table", "text": "The answer NA means that the abstract and introduction do not include the claims made in the paper.• The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.2. LimitationsQuestion: Does the paper discuss the limitations of the work performed by the authors? Answer: [Yes] Justification: Yes, see section 5.3 and conclusion, as well as references to appendix material where relevant.", "html": null}}}}