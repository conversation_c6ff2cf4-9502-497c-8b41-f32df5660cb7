{"paper_id": "PACE", "title": "PACE: Marrying generalization in PArameter-efficient fine-tuning with Consistency rEgularization", "abstract": "Parameter-Efficient Fine-Tuning (PEFT) effectively adapts pre-trained transformers to downstream tasks. However, the optimization of tasks performance often comes at the cost of generalizability in fine-tuned models. To address this issue, we theoretically connect smaller weight gradient norms during training and larger datasets to the improvements in model generalization. Motivated by this connection, we propose reducing gradient norms for enhanced generalization and aligning finetuned model with the pre-trained counterpart to retain knowledge from large-scale pre-training data. Yet, naive alignment does not guarantee gradient reduction and can potentially cause gradient explosion, complicating efforts to manage gradients. To address such an issue, we propose PACE, marrying generalization of PArameterefficient fine-tuning with Consistency rEgularization. We perturb features learned from the adapter with the multiplicative noise and ensure the fine-tuned model remains consistent for same sample under different perturbations. Theoretical analysis shows that PACE not only implicitly regularizes gradients for enhanced generalization, but also implicitly aligns the fine-tuned and pre-trained models to retain knowledge. Experimental evidence supports our theories. PACE surpasses existing PEFT methods in visual adaptation tasks (VTAB-1k, FGVC, few-shot learning, domain adaptation) showcasing its potential for resource-efficient finetuning. It also improves LoRA in text classification (GLUE) and mathematical reasoning (GSM-8K). The code is available at github.com/MaxwellYaoNi/PACE.", "pdf_parse": {"paper_id": "PACE", "abstract": [{"text": "Parameter-Efficient Fine-Tuning (PEFT) effectively adapts pre-trained transformers to downstream tasks. However, the optimization of tasks performance often comes at the cost of generalizability in fine-tuned models. To address this issue, we theoretically connect smaller weight gradient norms during training and larger datasets to the improvements in model generalization. Motivated by this connection, we propose reducing gradient norms for enhanced generalization and aligning finetuned model with the pre-trained counterpart to retain knowledge from large-scale pre-training data. Yet, naive alignment does not guarantee gradient reduction and can potentially cause gradient explosion, complicating efforts to manage gradients. To address such an issue, we propose PACE, marrying generalization of PArameterefficient fine-tuning with Consistency rEgularization. We perturb features learned from the adapter with the multiplicative noise and ensure the fine-tuned model remains consistent for same sample under different perturbations. Theoretical analysis shows that PACE not only implicitly regularizes gradients for enhanced generalization, but also implicitly aligns the fine-tuned and pre-trained models to retain knowledge. Experimental evidence supports our theories. PACE surpasses existing PEFT methods in visual adaptation tasks (VTAB-1k, FGVC, few-shot learning, domain adaptation) showcasing its potential for resource-efficient finetuning. It also improves LoRA in text classification (GLUE) and mathematical reasoning (GSM-8K). The code is available at github.com/MaxwellYaoNi/PACE.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Transformers [68] , with the self-attention mechanism [3] capturing long-range dependencies in data, succeed in various deep learning tasks, including image classification (ViT [16] ), multimodal learning (CLIP [55] ), image synthesis (StableDiffusion [57] ), semantic segmentation (SAM [33] ) and text generation (LLaMA [65] ). The success of transformers can be largely attributed to the availability of abundant data, such as ImageNet [11] and Laion5B [60] , which empower researchers to scale up these models by training them under an enormous number of parameters.", "section": "Introduction", "sec_num": "1"}, {"text": "Such huge models, with knowledge from large-scale pre-training [63] , constitute on foundation models that can be easily adapted to various downstream tasks through full fine-tuning or linear probing [20] , eliminating the need for task-specific model design [8] . However, full fine-tuning is storage-intensive and infeasible for maintaining separate model weights as the number of tasks grows, while linear probing, which only trains the last head layer, yields inferior adaptation performance.", "section": "Introduction", "sec_num": "1"}, {"text": "To overcome these limitations, Parameter-Efficient Fine-Tuning (PEFT) [24] fine-tunes only a small subset of parameters, thereby reducing storage requirements while surpassing the performance of full fine-tuning and linear probing. These advantages have popularized PEFT and inspired the development of various PEFT methods for deep learning tasks, which can be categorized into two groups: those increasing inference cost and cost-efficient ones. The first group introduces additional learning branches, such as non-linear adapters [25, 8] , or concatenates learnable parameters with input tokens, e.g., visual prompts [28, 82, 52] , increasing inference cost. The second group, focuses on cost-efficiency by lower-rank adaptation in linear layers [7, 26] , or affine transformations such as SSF [41] and RepAdapters [45] , which can be reparameterized during inference for efficiency.", "section": "Introduction", "sec_num": "1"}, {"text": "Despite the superiority and efficiency of PEFT, prioritizing optimization for downstream tasks compromises the generalizability of fine-tuned models, yielding suboptimal performance. Although some analyses have been conducted on PEFT [63, 27, 18, 72, 39] , they fail to fully explain the generalization of PEFT, leading to ineffective strategies for improving generalization.", "section": "Introduction", "sec_num": "1"}, {"text": "To address this gap in understanding generalization in PEFT, we establish a theoretical connection from generalization theory: smaller weight gradient norms and larger data volumes contribute to better generalization. Motivated by this, we propose reducing weight gradient norms and aligning output space of the fine-tuned model with the pre-trained one to retain knowledge captured from large pre-training data. Yet, theoretical analyses reveal this naive alignment does not guarantee gradient regularization and can even cause gradient explosion, complicating efforts for gradient management. To address this issue, we propose perturbing features learned from the adapter with multiplicative noise and constraining the network output to be consistent across different perturbations.", "section": "Introduction", "sec_num": "1"}, {"text": "Our method, called PACE, marries generalization of PArameter-efficient fine-tuning with Consistency rEgularization. Its name, PACE, reflects our goal of keeping the output behavior of the fine-tuned model in pace with the pre-trained one. Despite its simplicity, theoretical analysis confirms that PACE not only implicitly regularizes weight gradients for better generalization but also implicitly aligns the fine-tuned model with the pre-trained counterpart to retain knowledge from large-scale pre-training data. Experimental evidence supports our theories. PACE improves existing PEFT methods, achieving superior results across six adaptation benchmarks. Our key contributions are: i. We establish a theory connecting smaller weight gradient norms and larger datasets with enhanced generalization, motivating gradient reduction and model alignment for fine-tuning. ii. We propose PACE, a simple yet effective method perturbing features from adapters with multiplicative noise and constraining output of fine-tuned model to be consistent across perturbations. iii. Our theoretical and empirical evidence confirms that PACE implicitly regularizes gradients and aligns the fine-tuned model with the pre-trained one. PACE excels on 4 visual adaptation tasks. iv. We provide novel theoretical explanations of how gradient penalization and consistency regularization benefit generalization, offering fundamental insights applicable across deep learning.", "section": "Introduction", "sec_num": "1"}, {"text": "Parameter-Efficient Fine-Tuning (PEFT). LoRA [26] uses low-rank decomposition to reduce parameters and treats adapters as side paths. SSF [41] proposes affine transformations on latent features. FacT [30] decomposes and reassembles parameter matrices in ViT. Surgical fine-tuning [36] of different network parts improves adaptation to distribution shifts. FLoRA [74] performs a batched low-rank adaptation. GLoRA [7] unifies cost-efficient PEFT methods. NOAH [82] uses parameter search on neural prompts. ARC [14] leverages cross-layer ViT similarity, parameter-sharing adapter and scaling factors for lower fine-tuning cost. RLRR [15] incorporates a residual term for flexibility while preserving pre-trained representation. RepAdapter [45] reparameterizes adapters for efficient inference. Res-tuning [29] unbinds tuners from the backbone for memory efficiency. <PERSON> et al. [84] show impressive fine-tuning results by tuning layernorm in attention. OFT [54] and BOFT [42] propose orthogonal fine-tuning to preserve hypersphere energy between neurons.", "section": "Related work", "sec_num": "2"}, {"text": "Consistency Regularization. Fixmatch [61] applies consistency regularization over augmented images for semi-supervised learning. Openmatch [59] utilizes it on outlier predictions for open-set semi-supervised learning. R-Drop [76] applies it to transformers [68] with dropout for NLP tasks. CR [79] applies it over augmented real and fake images for GAN training. CAGAN [50] enforces consistency on discriminators with dropout for GAN training. Despite the empirical success of consistency regularization demonstrated by previous works, theoretical analysis is lacking. While NICE [47] demonstrates that consistency regularization lowers latent feature gradients for stable GAN training, it fails to reveal reduced weight gradient for enhanced generalization. Our study goes beyond prior works by providing a theoretical link between smaller weight gradients and improved generalization, effectively marrying generalization of PEFT with consistency regularization.", "section": "Related work", "sec_num": "2"}, {"text": "Generalization of Fine-Tuning. <PERSON> et al. [38] constrain the fine-tuned model's closeness to the pretrained model in weight space. <PERSON> et al. [18] induce sparsity on PEFT for better generalization. <PERSON> et al. [72] studies generalization of PEFT fine-tuning graph neural network. <PERSON> et al. [83] employ rank-1 gradient boosting (GB) updates supported by the GB theoretical framework. VioLET [73] , PromptSRC [31] and CoPrompt [58] naively align the fine-tuned model with the pre-trained one for enhanced generalization or avoiding forgetting. Additionally, L2SP [77] , DELTA [40] , and FTP [64] aim to retain pre-trained knowledge by aligning fine-tuned models with pre-trained ones, reducing distance in weight space, feature space and using projected gradient descent, respectively. However, they fail to provide a theoretical analysis for this alignment. Our study goes beyond understanding generalization of PEFT by discovering the benefits of gradient regularization and model alignment.", "section": "Related work", "sec_num": "2"}, {"text": "We propose PACE to match both requirements, paving a comprehensive understanding for PEFT.", "section": "Related work", "sec_num": "2"}, {"text": "Gradient regularization. Previous studies have empirically shown that gradient regularization improves performance [67, 85, 48, 49] and adversarially robust accuracy [13] . However, they lack theoretical connection between smaller gradient norms and better generalization [17, 81, 6] . We bridge this gap by establishing a fundamental theory between reduced gradient norms and improved generalization, providing a solid foundation for future research on enhancing generalization.", "section": "Related work", "sec_num": "2"}, {"text": "We begin with a unified perspective on cost-efficient PEFT based on GLoRA [7] , linking generalization with gradients and large-scale data, and motivating the alignment of the fine-tuned model with the pre-trained model to leverage its knowledge. We identify limitations of naive alignment in gradient regularization and introduce PACE, which implicitly enhances gradient regularization and model alignment. We conclude with theoretical justification and efficient implementations.", "section": "Approach", "sec_num": "3"}, {"text": "The transformer architectures [68, 16] have excelled in natural language processing and computer vision tasks through their powerful sequential modeling capabilities. This success stems from their ability to process text/image tokens through L transformer blocks, where each block contains selfattention and MLP modules primarily composed of linear layers. These linear layers enable the self-attention mechanism to capture long-range dependencies, allowing transformers to achieve superior performance when scaled to a huge number of parameters and trained on extensive datasets.", "section": "A unified perspective on cost-efficient PEFT methods", "sec_num": "3.1"}, {"text": "With massive parameters, pre-trained on large-scale data, transformers serve as foundation models that can be fine-tuned for downstream tasks using limited data. However, fully fine-tuning all parameters for various downstream tasks requires substantial memory and can lead the forgetting of pre-trained knowledge. To alleviate this without increasing inference cost, adapters with lightweight parameters are often preferred for fine-tuning. Let h0 (•) be a transformation within the pre-trained transformer. Current adapters can be unified as introducing a residual branch ∆ h to form a new transformation h:", "section": "A unified perspective on cost-efficient PEFT methods", "sec_num": "3.1"}, {"text": "EQUATION", "section": "A unified perspective on cost-efficient PEFT methods", "sec_num": "3.1"}, {"text": "Here, a is the input and h0 (•) can represent MLP modules, as in Adapter [25] and AdaptFormer [8] , or linear layers in self-attention and MLP modules, as in [26, 7, 12, 34] . In SSF [41] , h0 (•) is the identity mapping and ∆ h(a) = a ⊙ (γ -1) + β with γ and β as affine transformation parameters.", "section": "A unified perspective on cost-efficient PEFT methods", "sec_num": "3.1"}, {"text": "Given that linear layers are key components in transformer, tuning them offers a flexible and effective way to adapt models to downstream tasks. This work focuses on methods that tune the linear layer without increasing inference cost. Let (W 0 , b 0 ), (∆W , ∆b), and (W , b) be the parameters of pre-trained model, adapter and fine-tuned model, respectively, where W 0 , ∆W , W ∈ R dout×din and b 0 , ∆b, b ∈ R dout . Fine-tuning a linear layer in self-attention or MLP module can be formed as:", "section": "A unified perspective on cost-efficient PEFT methods", "sec_num": "3.1"}, {"text": "EQUATION", "section": "A unified perspective on cost-efficient PEFT methods", "sec_num": "3.1"}, {"text": "Based on GLoRA [7] , cost-efficient PEFT methods for linear layers vary in the form of ∆W , ∆b:", "section": "A unified perspective on cost-efficient PEFT methods", "sec_num": "3.1"}, {"text": "LoRA add : ∆W = W d W u , ∆b = b lora where W d ∈ R dout×r , W u ∈ R r×din ,", "section": "A unified perspective on cost-efficient PEFT methods", "sec_num": "3.1"}, {"text": "and r is the rank.", "section": "A unified perspective on cost-efficient PEFT methods", "sec_num": "3.1"}, {"text": "LoRA mul : ∆W = W 0 ⊙(W d W u ), ∆b = b 0 ⊙b lora , including RepAdapter [45] via reparameterization.", "section": "A unified perspective on cost-efficient PEFT methods", "sec_num": "3.1"}, {"text": "VPT add : ∆W is zero, ∆b = W 0 P , with learnable P ∈ R din×1 as layer-wise visual prompt. We use VPT add to differentiate from VPT [28] , which concatenates P with tokens, increasing inference cost.", "section": "A unified perspective on cost-efficient PEFT methods", "sec_num": "3.1"}, {"text": "Having established a unified perspective on cost-efficient PEFT, we now motivate our method from a perspective on improving generalization of neural networks to enhance performance on unseen data. Consider a network f := ϕ(g(x)) with l layers, where g is feature extractor and ϕ is the classification head. Let θ := {(W (i) , b (i) )} l i=1 be the parameter set with dimension d and", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "D n := {(x i , y i )} n i=1", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "be the training set of size n drawn i.i.d. from distribution D, which contains infinite data. The following lemma from [17] explains the relationship between the empirical and population loss.", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "Lemma 1 (Theorem 1 from [17] ) Let L D n (θ) be the empirical loss function over f on training set D n and L D (θ) be the population loss. For any ρ > 0, with high probability over D n ∼ D, we have", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "EQUATION", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "where R : (R + , R + ) → R + is an increasing function (under conditions on L D (θ) and n as in §B.5).", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "Lemma 1 bounds the population loss by the empirical loss with perturbed weights, indicating that a minimal empirical loss increase from small weight perturbations implies low population loss.", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "By observing that the maximum of", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "L D n is achieved at ϵ = ρ∇ θ ∥∇ θ ∥2", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": ", where ∇ θ is the gradient of L D n at θ, and performing a Taylor expansion of L D n around θ, we formulate the following theorem.", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "Theorem 1 Denote ∇ θ as the gradient and λ H max as the largest eigenvalue of the Hessian matrix H θ of L D n at θ. For any ρ > 0, with high probability over training set D n ∼ D, we have", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "EQUATION", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "Here, higher-order terms from the <PERSON> expansion are incorporated into R ∥θ∥ 2 2 ρ 2 , 1 n , which is related to weights norm and inversely related to the training data size n.", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "Theorem 1 (proof in §B.1) outlines strategies for enhancing generalization. They involve regularizing weight norms and the largest Hessian eigenvalues, and crucially, increasing data size n and reducing the weight gradient norms (illustrated in Figure 1 ). However, excessive reduction should be avoided as it could impair network's representation capacity, yielding higher empirical and population loss.", "section": "Generalization of deep neural networks", "sec_num": "3.2"}, {"text": "Theorem 1 emphasizes that large-scale data and smaller gradient magnitudes are essential for better generalization in neural network training. Therefore, aligning the fine-tuned model with the pretrained one is crucial, as it ensures retention of knowledge obtained from large-scale data, preserving generalization. PEFT methods, often outperforming full fine-tuning, achieve this alignment by limiting the number of trainable parameters, restricting the model's capacity to deviate from the pretrained one. However, the training objective prioritizes downstream task performance, compromising alignment with pre-trained knowledge. While sparsity regularization [18] and weight decay on adapter weights help, they do not ensure alignment, as even small weight changes can lead to significant divergence in output space [75, 21, 17] . Therefore, we propose to achieve the alignment by reducing the FP-distance (output distance between fine-tuned and pre-trained models on training samples):", "section": "Motivation and limitation of aligning the fine-tuned model with the pre-trained model", "sec_num": "3.3"}, {"text": "EQUATION", "section": "Motivation and limitation of aligning the fine-tuned model with the pre-trained model", "sec_num": "3.3"}, {"text": "where θ, θ 0 , ∆θ ∈ R d are parameters for the fine-tuned model, pre-trained model and the adapter.", "section": "Motivation and limitation of aligning the fine-tuned model with the pre-trained model", "sec_num": "3.3"}, {"text": "While reducing FP-distance keeps the fine-tuned model close to the pre-trained model, thus preserving its knowledge, it does not ensure reduced gradient magnitudes, leading to suboptimal generalization. ", "section": "Motivation and limitation of aligning the fine-tuned model with the pre-trained model", "sec_num": "3.3"}, {"text": "I) also reduces FP-distance (between fine-tuned f (θ 0 +∆θ) and pre-trained f (θ 0 )), especially when z 1 =1, z 2 = 0 or vice versa.", "section": "Motivation and limitation of aligning the fine-tuned model with the pre-trained model", "sec_num": "3.3"}, {"text": "To understand the gradient-related limitations in this alignment, we assume ∆θ is small enough for a Taylor expansion approximation. Following standard practices [17, 80, 2] , we perform the expansion up to the second-order terms. Given the independence between elements in squared L 2 distances ( §B.4) and to simplify our theories, we analyze a one-dimensional output for a single i.i.d. sample, which leads us to the following proposition.", "section": "Motivation and limitation of aligning the fine-tuned model with the pre-trained model", "sec_num": "3.3"}, {"text": "Proposition 1 Assuming ∆θ is small, denote f (θ) ∈ R as the one-dimensional output for x, with ∇ and H as its gradient and Hessian at θ. FP-distance over x can be decomposed as follows:", "section": "Motivation and limitation of aligning the fine-tuned model with the pre-trained model", "sec_num": "3.3"}, {"text": "EQUATION", "section": "Motivation and limitation of aligning the fine-tuned model with the pre-trained model", "sec_num": "3.3"}, {"text": "Prop. 1 establishes the relationship between weight gradients, adapter weights, and FP-distance. However, it remains unclear if it regulates gradients. Our experiments show that minimizing FPdistance can sometimes increase gradient magnitude, complicating efforts for managing gradient.", "section": "Motivation and limitation of aligning the fine-tuned model with the pre-trained model", "sec_num": "3.3"}, {"text": "To achieve better generalization by both regularizing gradients and aligning the fine-tuned model with the pre-trined model, we propose a consistency regularization loss for f , encouraging invariance of f to the same input under varying multiplicative noise perturbations on the adapter weights, as follows:", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": "EQUATION", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": "where z 1 , z 2 ∼ N (1, σ 2 I) is the multiplicative noise applied on adapter weight. To understand the generalization benefits in this consistency regularization, we simplify the analysis by focusing on one-dimensional output for a single sample, resulting in the following theorem.", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": "Theorem 2 Using notations from Prop. 1, let f (θ 0 + z ⊙ ∆θ) ∈ R be the one-dimensional output for x. Define ∆θ j as j-th element in ∆θ, ∇ j as the j-th element in ∇ and H jk as the (j, k)-entry in", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": "H. With z 1 , z 2 ∼ N (1, σ 2 I", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": "), the consistency loss over x can be approximated as:", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": "EQUATION", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": ")", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": "Theorem 2 (proof in §B.2) shows that the consistency regularization essentially penalizes the first-and second-order gradients of f at θ (illustrated in Figure 1 ), with the regularization strength controlled by the noise variance σ 2 and adaptively influenced by the magnitude of elements in adapter weight ∆θ. Thus, minimizing the consistency loss implicitly regularizes the gradients, improving generalization.", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": "With the FP-distance in Prop. 1 and consistency loss in Theorem 2, we establish their relationship as:", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": "Theorem 3 With d as the dimension of θ, Eq. 6 can be upper-bounded as: Theorem 3 (proof in B.3) establishes the relationship between Eq. 6 and Eq. 8, showing Eq. 6 is upperbounded by terms involving ∥∆θ⊙∇∥ 2 2 and ∥(∆θ∆θ T )⊙H∥ 2 F which appear in Eq. 8. Reducing these terms results in a decrease in Eq. 6. Thus minimizing the consistency loss implicitly aligns the fine-tuned and pre-trained models (illustrated in Figure 1 ), preserving pre-trained knowledge.", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": "EQUATION", "section": "Consistency regularization", "sec_num": "3.4"}, {"text": "Providing different weight perturbations for each input in a mini-batch increases memory and computational demands. To avoid this, we perturb feature outputs from the adapter ∆h(•), effectively simulating perturbation that shares noise across each row in the weight matrix ∆W . Our simple pipeline is shown in Figure 2 . Consider X ∈ R B×T ×din as a batch of data where B and T are the batch and token sizes. The calculation for the linear layer of the fine-tuned model, which utilizes pre-trained weights W 0 , b 0 and adapter weights ∆W , ∆b, processes an output size of d out as:", "section": "Efficient implementation of PACE", "sec_num": "3.5"}, {"text": "EQUATION", "section": "Efficient implementation of PACE", "sec_num": "3.5"}, {"text": "EQUATION", "section": "Efficient implementation of PACE", "sec_num": "3.5"}, {"text": ")", "section": "Efficient implementation of PACE", "sec_num": "3.5"}, {"text": "Operator ⊙ is the element-wise multiplication after expanding the left matrix Z ∈ R B×dout ∼ N (1, σ 2 I) into B × T × d out where tokens within the same example share the same noise. Motivated by [37] , the σ decreases linearly as block depth increases. Let f 1 (•) and f 2 (•) be two networks share same weights but do not share the noise patterns. The loss function for PACE is:", "section": "Efficient implementation of PACE", "sec_num": "3.5"}, {"text": "EQUATION", "section": "Efficient implementation of PACE", "sec_num": "3.5"}, {"text": ")", "section": "Efficient implementation of PACE", "sec_num": "3.5"}, {"text": "where ℓ is the classification loss and λ is a hyperparameter controlling regularization strength. During inference, noise and regularization are ommitted, ∆W , ∆b are integrated with W 0 , b 0 for efficiency:", "section": "Efficient implementation of PACE", "sec_num": "3.5"}, {"text": "EQUATION", "section": "Efficient implementation of PACE", "sec_num": "3.5"}, {"text": "Efficient PACE variants. In §C, we present two variants that match the computational/memory costs of the baseline while achieving superior performance with substantially reduced resources.", "section": "Efficient implementation of PACE", "sec_num": "3.5"}, {"text": "We combine LoRA mul and VPT add to form a strong baseline LoRA mul +VPT add , outperforming other combinations in most cases. We evaluate our method across four visual classification adaptation tasks: VTAB-1K [78] , few-shot learning [30] , FGVC [28] and domain adaptation [82] . We demonstrate PACE improves LoRA on GLUE [70] for text classification and GSM-8K [9] for text generation.", "section": "Experiments", "sec_num": "4"}, {"text": "Datasets and evluations. VTAB-1K comprises 19 datasets organized into (i) Natural images, (ii) Specialized datasets (remote sensing, medical) and (iii) Structured datasets (scene structure) domains. Each dataset has 1K training examples. Following [78, 28] , we use the provided 800-200 train split for hyperparameter selection, evaluate using the full training set and report average accuracy across three trails. Few-shot learning involves 5 fine-grained datasets: FGVC-Aircraft [46] , Food101 [4] , OxfordFlowers102 [51] , OxfordPets [53] and StanfordCars [35] . Following [30] , we evaluate 1, 2, 4, 8 and 16 shots, train on the provided training set, tune hyperparameters using validation and report average test accuracy over three random seeds. FGVC includes 5 fine-grained datasets: CUB-200-2011 [69] , NABirds [66] , OxfordFlowers [51] , StanfordDogs [10] and StanfordCars [35] . We follow [28] to use validation set for hyperparameter and report test results. For domain adaptation, following [82, 7] , we train on ImageNet [11] with a 16-shot setting, use the validation split by [82] for hyperparameter selection and report the results on the official validation set and 4 out-of-domain datasets: ImageNet-Sketch [71] , ImageNet-V2 [56] , ImageNet-A [23] and ImageNet-R [22] . We evaluate on GLUE [70] for text classification and GSM-8K [9] for mathematical reasoning.", "section": "Experiments", "sec_num": "4"}, {"text": "Pre-trained backbones. We experiment with two vision transformers, Vision Transforms (ViT-B/16) [16] and Swin Transformer (Swin-B) [44] . These two are pre-trained on ImageNet-21K [11] . We test a ViT-B-Laion-IN12K model, pre-trained on Laion-2B [60] and fine-tuned on ImageNet-12K [11] . We use RoBERTa base [43] and Phi-3-mini-4k-instruct [1] for text classification and generation.", "section": "Experiments", "sec_num": "4"}, {"text": "Implementation details. We follow [28] for image processing: 224 × 224 resizing for VTAB-1K; random flips and crops to 224 × 224 for FGVC and few-shot learning; stronger augmentation for domain adaptation task, following [16, 82, 41] . We use the Adam optimizer [32] with cosine learning rate decay and linear warm-up (first 10 epochs). Models are fine-tuned for 300 epochs on VTAB-1K and 100 epochs on other vision adaptation tasks, with batch size 64. For text classification we follow [26] . See §G for mathematical reasoning details. All experiments used an NVIDIA H100 GPU.", "section": "Experiments", "sec_num": "4"}, {"text": "Baseline. For each dataset, we identified the better method (LoRA mul +VPT add or LoRA add ) and tuned the rank, learning rate, and weight decay to form a strong baseline. The detailed baseline settings for each task and the number of trainable parameters are provided in §F, where LoRA mul +VPT add generally outperformed other variants. Building on the strong LoRA mul +VPT add , we use the grid search for our λ and σ, following strategies from previous studies [28, 41, 26] . Beyond LoRA mul +VPT add , PACE also enhances PEFT methods such as AdaptFormer, GLoRA, COFT, and BOFT ( §D.4). 4 . Results on FGVC. Table 3 shows that PACE improves the strong LoRA mul +VPT add by 0.7%, outperforming SSF [41] , ARC [14] and RLRR [15] that use strongly pre-trained ViT with augmentations. In §D.2, PACE achieves larger improvements on smaller datasets.", "section": "Experiments", "sec_num": "4"}, {"text": "Method ViT-B (ImageNet-21K) ViT-B (Laion2B-ImageNet-12K) Swin-B (ImageNet-21K) CIFAR ImageNet-1K CIFAR ImageNet-1K CIFAR ImageNet-1K -100 Src. -S -V -A -R -100 Src. -S -V -A -R -100 Src. -S -V -A -R", "section": "Experiments", "sec_num": "4"}, {"text": "Results on domain adaptation. Results on text classification and mathematical reasoning. Table 5 shows that PACE outperforms LoRA by 1% on GLUE text classification and by 3.11% on GSM-8K mathematical reasoning.", "section": "Experiments", "sec_num": "4"}, {"text": "Generalization on other backbones. We evaluate PACE on CIFAR-100 (VTAB-1K) and domain adaptation using Swin-B [44] pre-trained on ImageNet-21K and ViT-B (pre-trained on Laion 2B, then fine-tuned on ImageNet-12K). Table 7 shows PACE outperforms LoRA mul +VPT add and other PEFT methods across all backbones, demonstrating its strong generalizability. Further experiments in §D.3 show PACE works effectively with self-supervised models such as MAE [19] and DINO [5] .", "section": "Experiments", "sec_num": "4"}, {"text": "To verify our theories, we conduct experiments on CIFAR-100 (VTAB-1K) using ViT-B/ 16 To clarify why naive alignment is problematic, we vary the regularization strength λ over a wide range (1e-3 to 5e4) for both Fine-tuned Pre-trained model Alignment (FPA) by minimizing D fp in Eq. 5 and PACE. Figure 5 shows the averaged gradient norm over training (see also Figures 8 & 9 for more visualizations). PACE robustly lowers gradient norms with larger λ, while FPA exhibits unpredictable behavior, even causing gradient explosion. This verifies Prop. 1 that minimizing D fp is problematic for gradient regularization, complicating gradient management. ", "section": "Analyses", "sec_num": "4.2"}, {"text": "We ablate PACE based on the baseline LoRA mul +VPT add on CIFAR-100 (VTAB-1K) and ImageNet-1K in domain adaption as shown in Table 8 . The ablations include Noise (baseline w/ noise perturbing adapter), PACE add (replacing the multiplicative noise with the additive noise), PACE h (perturbing h(•) instead of ∆h(•) in Eq. 11), PACE drop (replacing the Gaussian noise with the dropout noise), PACE σ= (all transformer blocks share the same σ), PACE σ↑ (σ increases linearly with depth), FPA (fine-tuned and pre-trined alignment by minimizing Eq. 5), SAM (sharpness-aware minimization [17] ), GP (gradient penalization), ℓ 1 (sparsity regularization), and transfer learning methods L2SP [77] , DELTA [40] and FTP [64] . We grid-search hyperparameters and report the best results. Table 8 presents the results for all variants. PACE improves over Noise, which itself is better than baseline, justifying our adapter perturbation and consistency regularization. PACE add performs worse than PACE, showing the superiority of the multiplicative noise. Although PACE h can implicitly regularize gradients, it performs worse than PACE, verifying the advantages of perturbing adapter to implicitly align models. PACE drop is worse than PACE, indicating the dropout noise is suboptimal. PACE σ= and PACE σ↑ perform worse, justifying our design of linearly decreasing σ. FPA, SAM and GP, which either only align models or only regularize gradients, are outperformed by PACE. Despite combining FPA+GP, it still performs worse than ours, suggesting ineffective combination. ℓ 1 , L2SP, DELTA, and FTP obtain worse results than PACE, showing their limitations in improving generalization. PACE regularizes gradients for better generalization and aligns models to retain knowledge, surpassing all other variants. We further evaluate applying PACE across multiple M networks during training or applying it lazily with half-batch size at every N steps (PACE half lazy in §C). Figure 6 presents the results, showing that applying PACE among two networks at every training step performs best. However, lazy regularization applied every few steps can still provide reasonable results while saving computational/memory costs.", "section": "Ablation studies", "sec_num": "4.3"}, {"text": "We test the sensitivity of hyperparameters λ and σ introduced in our PACE on OxfordPets for few-shot learning across 1, 2, 4, 8 shots. The results presented in Figure 7 demonstrate that with less data, larger λ and σ are favored, verifying the effectiveness of PACE in improving generalization.", "section": "Ablation studies", "sec_num": "4.3"}, {"text": "We have introduced PACE, a novel and effective method that combines generalization of PArameterefficient fine-tuning with Consistency rEgularization. Through rigorous theoretical analyses, we have shown PACE reduces weight gradient for improved generalization and it aligns the fine-tuned model with the pre-trained model for retaining pre-training knowledge. Our experimental results support the theoretical analyses, justifying the generalization advantages of PACE over other PEFT methods. With its dual advantages, PACE consistently outperforms other variants across different backbones, firmly establishing PACE as a powerful solution for enhancing generalization for PEFT methods. Limitations and border impacts are discussed in §A.", "section": "Conclusions", "sec_num": "5"}, {"text": "<PERSON> † <PERSON> ‡, † <PERSON><PERSON><PERSON> * , §, † † The Australian National University § Data61 CSIRO ‡ Australian Institute for Machine Learning, The University of Adelaide † <EMAIL> ‡ <EMAIL> § <EMAIL>", "section": "PACE: Marrying generalization of PArameter-efficient fine-tuning with Consistency rEgularization (Supplementary Material)", "sec_num": null}, {"text": "Our work provides a powerful solution for improving generalization in Parameter Efficient Fine-Tuning (PEFT), allowing for effective fine-tuning of pre-trained models while reducing the heavily reliance on pre-training from scratch using large scale data. Our advancements in PEFT, supported by Theorems 1, 2 and 3, offer novel insights into gradient regularization and model alignment. These insights extend beyond PEFT and can be applied to other areas such as continual learning and transfer learning, potentially enhancing the performance and efficiency of models in various domains. By leveraging our findings, practitioners can develop more robust and adaptable models that generalize well to new tasks and environments, leading to more intelligent and versatile AI systems. In terms of negative impacts, the robustness of our fine-tuning method could potentially be misused to create more convincing deepfakes, raising concerns about the spread of misinformation, manipulation of public opinion, and malicious activities such as fraud, blackmail, or harassment. However, potential misuse is a downside with any improvements that have universal nature.", "section": "A Broader impacts and limitations A.1 Broader impacts", "sec_num": null}, {"text": "While our work effectively improves generalization ability, it introduces additional computational costs by requiring input samples to be passed through the network twice for regularization. However, this can be mitigated by using two efficient variants, PACE fast and PACE half lazy , proposed in §C, where we demonstrate the potential for resource-efficient fine-tuning. Additionally, our method introduces extra hyperparameters λ and σ, which require caution during hyperparameter search. Nonetheless, Figure 7 suggests that fewer training data requires larger λ and σ values, providing insight for hyperparameter tuning.", "section": "A.2 Limitations", "sec_num": null}, {"text": "Settting ϵ = ρ∇ θ ∥∇ θ ∥2 , we perform a second-order Taylor expansion of L D n around θ. By incorporating the higher-order terms from the Taylor expansion into R", "section": "B Proofs B.1 Proof of Theorem 1", "sec_num": null}, {"text": "∥θ∥ 2 2 ρ 2 , 1", "section": "B Proofs B.1 Proof of Theorem 1", "sec_num": null}, {"text": "n , we derive:", "section": "B Proofs B.1 Proof of Theorem 1", "sec_num": null}, {"text": "EQUATION", "section": "B Proofs B.1 Proof of Theorem 1", "sec_num": null}, {"text": ")", "section": "B Proofs B.1 Proof of Theorem 1", "sec_num": null}, {"text": "Assuming that the approximation does not alter the inequality relationship, i.e., it preserves the ≤ relation on both sides and considering the largest eigenvalue of", "section": "B Proofs B.1 Proof of Theorem 1", "sec_num": null}, {"text": "H θ as λ H max , implying v T H θ v ≤ λ H max ∥v∥ 2 2", "section": "B Proofs B.1 Proof of Theorem 1", "sec_num": null}, {"text": "for any v, we further bound Eq. 14 as follows and arrive at:", "section": "B Proofs B.1 Proof of Theorem 1", "sec_num": null}, {"text": "L D (θ) ≤ L D n (θ) + ρ∥∇ θ ∥ 2 + ρ 2 2 λ H max + R ∥θ∥ 2 2 ρ 2 , 1 n .", "section": "B Proofs B.1 Proof of Theorem 1", "sec_num": null}, {"text": "The proof is motivated by <PERSON> and <PERSON><PERSON><PERSON> [47] . We include the proof process for completeness.", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "<PERSON><PERSON>", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": ")", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Defining v := m 1 ⊙ ∆θ and u := m 2 ⊙ ∆θ, where v, u ∼ N (0, σ 2 diag(∆θ ⊙ ∆θ)), we can rewrite Eq. 15 as follows:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": ")", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Next, we derive the four terms, Eq. 16, 17, 18, and 19, respectively as follows:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Eq. 16 .", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Using E z1,z2 [(z 1 -z 2 ) 2 ] = 2σ 2 for z 1 , z 2 ∼ N (0, σ 2 )", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": ", we can simplify (Eq. 16) as follows, noting that terms related to different dimensions are canceled due to zero-mean independent Gaussian noise:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": ")", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Eq. 17. Utilizing E[z 3 ] = µ 3 + 3µσ 2 for z ∼ N (µ, σ 2 ), and noting that E[z 3 ] = 0 for µ = 0, Eq. 17 is derived as:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Eq. 18. We first decompose Eq. 18, then discuss each case and obtain the final result:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": ")", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Given the independence of elements in v, only terms with an element repeated two or four times contribute non-zero results, leading to four distinct, non-overlapping cases. Using E[z 2 ] = σ 2 + µ 2 and E[z 4 ] = µ 4 + 6µ 2 σ 2 + 3σ 4 for z ∼ N (µ, σ 2 ), and simplifying to E[z 2 ] = σ 2 and E[z 4 ] = 3σ 4 when µ = 0, we have:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Case 1: j = k ̸ = p = q,", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "given the independence of v j and v p , we have:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": ")", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Case 2: For j = p ̸ = k = q, the independence of v j and v k simplifies our calculation, leading to:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": ")", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Case 3: For j = q ̸ = k = p, utilizing the independence of v j and v k as well as the symmetry H jk = H kj , we obtain:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": ")", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Case 4: For j = q = k = p, using E[z 4 ] = 3σ 4 where z ∼ N (0, σ 2 ), we have:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Combining above four cases together, we have the result for Eq. 18:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": ")", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "Eq. 19:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "-", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": ")", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "With results of Eq. 20, 21, 27, 28, we have the final results:", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "EQUATION", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": ")", "section": "B.2 Proof of Theorem 2", "sec_num": null}, {"text": "The <PERSON><PERSON><PERSON><PERSON><PERSON> inequality states that for u, v ∈ R d , we have", "section": "B.3 Proof of Theorem 3", "sec_num": null}, {"text": "( j u j v j ) 2 ≤ ( j u 2 j )( j v 2 j ). Let u = 1, it follows that ( j v j ) 2 ≤ d∥v∥ 2 2 .", "section": "B.3 Proof of Theorem 3", "sec_num": null}, {"text": "Using this inequality, we then prove the following:", "section": "B.3 Proof of Theorem 3", "sec_num": null}, {"text": "EQUATION", "section": "B.3 Proof of Theorem 3", "sec_num": null}, {"text": ") [∆θ T H∆θ] 2 = j,k ∆θ j ∆θ k H jk 2 ≤ d 2 (∆θ∆θ T ) ⊙ H 2 F (31)", "section": "B.3 Proof of Theorem 3", "sec_num": null}, {"text": "Here, the inequality is obtained by treating ∆θ j ∆θ k H jk as an element of a vector with size of d 2 . This leads to the final results.", "section": "B.3 Proof of Theorem 3", "sec_num": null}, {"text": "We use the squared L 2 distance for multi-dimensional outputs for D fp and D pace , which allows our one-dimensional analysis to naturally generalize to multiple dimensions. For example, for a vector-valued function in the naive alignment,", "section": "B.4 Rationale for one-dimensional output analysis", "sec_num": null}, {"text": "f (θ) = [f 1 (θ), ..., f m (θ)],", "section": "B.4 Rationale for one-dimensional output analysis", "sec_num": null}, {"text": "where m is the output dimension, we have:", "section": "B.4 Rationale for one-dimensional output analysis", "sec_num": null}, {"text": "∥f (θ 0 ) -f (θ 0 + ∆θ)∥ 2 2 = m i=1 [f i (θ 0 ) -f i (θ 0 + ∆θ)] 2 .", "section": "B.4 Rationale for one-dimensional output analysis", "sec_num": null}, {"text": "This equality shows that the squared L 2 distance in multiple dimensions is simply the sum of nonnegative squared differences in each dimension. Consequently, this additive nature enables our one-dimensional analysis to extend seamlessly to multiple dimensions in practice, aligning with our empirical observations.", "section": "B.4 Rationale for one-dimensional output analysis", "sec_num": null}, {"text": "According to [17] , the function R", "section": "B.5 R increases with 1 n", "sec_num": null}, {"text": "∥θ∥ 2 2 ρ 2 , 1 n in Eq. 3 is defined as: R ∥θ∥ 2 2 ρ 2 , 1 n = k log 1 + ∥θ∥ 2 2 ρ 2 1 + log n k 2 + 4 log n δ + 8 log(6n + 3k) n -1 .", "section": "B.5 R increases with 1 n", "sec_num": null}, {"text": "Here k is the number of parameters, n is the number of training samples, δ ∈ (0, 1] is the confidence level and ρ is the max norm of the Gaussian perturbation noise.", "section": "B.5 R increases with 1 n", "sec_num": null}, {"text": "To ensure R is valid, we require n > 1. To analyze how R changes with n, we fix ∥θ∥ 2 2 ρ 2 and break the expression under the square root of R into three terms:", "section": "B.5 R increases with 1 n", "sec_num": null}, {"text": "R 1 = k log 1 + ∥θ∥ 2 2 ρ 2 1 + log n k 2 n -1 , R 2 = 4 log n -4 log δ n -1 , R 3 = 8 log(6n + 3k) n -1", "section": "B.5 R increases with 1 n", "sec_num": null}, {"text": "We analyze each term separately to determine whether it decreases with increasing n.", "section": "B.5 R increases with 1 n", "sec_num": null}, {"text": "Analysis for R 1 : The derivative for R 1 w.r.t. n is:", "section": "B.5 R increases with 1 n", "sec_num": null}, {"text": "R ′ 1 = k 1+ ∥θ∥ 2 2 ρ 2 1+ √ log n k 2 • 2 ∥θ∥ 2 2 ρ 2 (1+ log n k ) • 1 2 √ log n k • 1 kn • (n-1)-k log 1+ ∥θ∥ 2 2 ρ 2 1+ log n k 2 (n -1) 2 . = ∥θ∥ 2 2 ρ 2 1+ √ log n k 1+ ∥θ∥ 2 2 ρ 2 1+ √ log n k 2 • 1 √ log n k • n-1 n -k log 1 + ∥θ∥ 2 2 ρ 2 1 + log n k 2 (n -1) 2 < ∥θ∥ 2 2 ρ 2 1+ √ log n k ∥θ∥ 2 2 ρ 2 1+ √ log n k 2 • 1 √ log n k -k log ∥θ∥ 2 2 ρ 2 1 + log n k 2 (n -1) 2 < 1 1+ √ log n k • 1 √ log n k -k log ∥θ∥ 2 2 ρ 2 + log 1 + log n k 2 (n -1) 2 < 1 √ log n k • 1 √ log n k -k log ∥θ∥ 2 2 ρ 2 -k log 1 + log n k 2 (n -1) 2 = k (n -1) 2 • 1 log n -log ∥θ∥ 2 2 ρ 2 -log 1 + log n k 2 . Since ∥θ∥ 2 2 ρ 2 is generally large, the smallest n is 2 and log 1 + log n k 2 > 0. Therefore, for n > 1, R ′ 1 < 0, meaning R 1 decreases as n increase. Analysis of R 2 : The derivative for R 2 w.r.t. n is R ′ 2 = 4 (n -1) 2 (1 - 1 n -log n + log δ). Since δ ≤ 1, for n > 1, R ′ 2 < 0, indicating that R 2 decreases with increasing n. Analysis of R 3 : The derivative for R 3 w.r.t. n is R ′ 3 = 8 6(n-1) 6n+3k -log(6n + 3k) (n -1) 2 < 8 1 -log(6n + 3k) (n -1) 2 .", "section": "B.5 R increases with 1 n", "sec_num": null}, {"text": "For n > 1, log(6n + 3k) > 1, implying that R ′ 3 < 0 and R 3 decrease as n increases.", "section": "B.5 R increases with 1 n", "sec_num": null}, {"text": "For n > 1, all terms R 1 , R 2 and R 3 decreases as n increases. Thus R(", "section": "Conclusion.", "sec_num": null}, {"text": "∥θ∥ 2 2 ρ 2 , 1 n ) is a decreasing function of n.", "section": "Conclusion.", "sec_num": null}, {"text": "Building upon strong theoretical foundation of PACE for generalization, we demonstrate that simple modifications can reduce memory and training time requirements of PACE. In this section, we explore two efficient variants, PACE fast and PACE half lazy , both maintaining similar computational and memory requirements as the baseline while improving performance. We then provide empirical results which show that PACE fast slightly outperforms PACE half lazy while requiring no additional hyperparameters and using fewer computational resources. Given its superior efficiency, we further explore the potential of PACE fast for resource-efficient fine-tuning. By simply reducing the batch size and epochs, PACE fast outperforms the baseline while using significantly less GPU memory and training time.", "section": "C Efficient PACE variants", "sec_num": null}, {"text": "PACE fast : Building on the observation that only small datasets are typically available for fine-tuning, we assume that the model behavior changes gradually across epochs. Under this assumption, we store the model outputs from the previous epoch (f e-1 (x)), which contain inherent noise due to the adapter perturbation, and compute the consistency regularization loss between these stored outputs and the current epoch's noised outputs:", "section": "C Efficient PACE variants", "sec_num": null}, {"text": "d pace fast (x) = ∥f (x) -o e-1 ∥ 2 2 ;", "section": "C Efficient PACE variants", "sec_num": null}, {"text": "where o e-1 = f e-1 (x).", "section": "C Efficient PACE variants", "sec_num": null}, {"text": "Here the output vector o ∈ R C , where C is the number of classes. Since f (•) applies noise perturbation to the adapter and changes gradually between epochs, f e-1 (x) and f (x) can be seen as applying different i.i.d. noises to similar model states. This approach preserves the theoretical foundation of PACE while incurring minimal storage and computation costs. With typically few classes C and a limited number of samples in fine-tuning, storing o e-1 within GPU or CPU memory is manageable.", "section": "C Efficient PACE variants", "sec_num": null}, {"text": "PACE half lazy : During training, the network always applies noise perturbations. Every N -th iteration uses a half batch size and consistency regularization, while all other iterations use the full batch size.", "section": "C Efficient PACE variants", "sec_num": null}, {"text": "Memory and computational efficiency of two variants. Both variants maintain similar computational and memory requirements as the baseline. To demonstrate this, we conduct experiments on CIFAR-100 (VTAB-1K) using ViT-B/16, Camelyon (VTAB-1K) with Swin-B, and ImageNet (domain adaptation) with ViT-B/16. Table 9 compares maximum GPU memory usage, total training time, and accuracy for each task, showing that PACE fast and PACE half lazy significantly improve upon the baseline while maintaining similar computational demands.", "section": "C Efficient PACE variants", "sec_num": null}, {"text": "We find that PACE fast slightly outperforms PACE half lazy without requiring additional hyperparameters, yet it needs to store outputs from the previous epoch. We therefore analyze its memory requirements. Memory efficiency of PACE fast . We compare the additional memory requirement of PACE fast with the baseline GPU memory consumption. Table 10 shows that the memory overhead of PACE fast is negligible compared to the baseline GPU memory requirements and can be easily stored in GPU. Moreover, even in the rare scenario of fine-tuning on the full ImageNet 1K dataset (1.2 million samples), PACE fast requires only 4.8GB of additional memory for storing the output of the model's classification head. This is significantly smaller than the dataset itself (>100GB) and can be easily accommodated in the CPU/GPU memory. Resource-Efficient training with PACE fast . Given the superior performance, minimal memory overhead, and no need for additional hyperparameters of PACE fast , we explore its potential for resource-efficient training by maintaining the same number of updates with reduced batch size and proportionally reduced epochs. Table 11 shows that even with 1/8 batch size and epochs, PACE fast still outperforms the baseline by 1.7% while only using ∼1/3 GPU memory and ∼1/4 training time. This demonstrates the robustness and generalization benefits that PACE fast brings to models, enabling them to excel under constrained training configurations. Such an efficiency is particularly valuable for fine-tuning large foundation models, where resource constraints necessitate small batch sizes and typically lead to sharp loss landscapes, yet the theoretical guarantee of PACE for smooth loss landscapes provides a promising solution for these challenges. ", "section": "C Efficient PACE variants", "sec_num": null}, {"text": "In this section, we provide additional experiments of PACE on VTAB-1K with different epochs, varying training data sizes on FGVC benchmarks, self-supervised pre-trained backbones and combinations with other PEFT methods.", "section": "D Additional Experiments", "sec_num": null}, {"text": "In Table 1 , We use 300 epochs for VTAB-1K tasks as we observed slight improvements over 100 epochs. However, this does not mean PACE requires longer training to converge. Since the optimizer uses the cosine learning rate decay, reducing the number of training epochs to 100 has a minimal impact on performance, as shown in Table 12 .", "section": "D.1 Experiments of VTAB-1K with different epochs", "sec_num": null}, {"text": "To ensure fair memory and computational budgets, we also tested PACE with half the batch size and 50 epochs. Table 12 shows that under these conditions, PACE still improves baseline accuracy by 2.10%, and outperforms the previous SOTA GLoRA, which uses 500 epochs for training and 30 for parameter search. These results demonstrate PACE's efficiency and effectiveness across various training configurations.", "section": "D.1 Experiments of VTAB-1K with different epochs", "sec_num": null}, {"text": "To validate generalization benefits of PACE on limited data settings, we conduct experiments on FGVC using 50%, 20%, and 10% of the original training samples. Table 13 shows that PACE achieves larger improvements with smaller data sizes, aligning with our theoretical analyses. ", "section": "D.2 Experiments on FGVC with limited training data", "sec_num": null}, {"text": "To further verify the effectiveness of PACE on a self-supervised pre-trained backbone, we conduct VTAB-1K experiments on SVHN, Camelyon, and Clevr-Count using MAE [19] and DINO [19] , with ViT-B/16 pre-trained on ImageNet-1K [11] . Table 14 shows that PACE improves the baseline on these self-supervised backbones, confirming its applicability to fine-tuning self-supervised models. We conducted experiments combining PACE with several PEFT methods, including AdaptFormer [8] , GLoRA [7] , COFT [54] , and BOFT [42] , on CIFAR-100 (VTAB-1K) and ImageNet (domain adaptation) using ViT-B/16. Table 15 shows that integrating PACE improves the baseline performance. ", "section": "D.3 Experiments on self-supervised pre-trained backbones", "sec_num": null}, {"text": "For each dataset, we follow strategies from previous works [41, 28, 7, 45] to apply grid search on the rank, learning rate and weight decay to establish strong baselines. ", "section": "F Hyperparameter settings", "sec_num": null}, {"text": "We conduct experiments on text generation tasks by fine-tuning Phi-3-mini-4k-instruct [1] on the GSM-8K [9] dataset using causal language modeling. We use learning rate of 2e-6, batch size of 4, LoRA rank of 16, prompt \"Answer below question. First think step-by-step and then answer the final number:\\n\\n<Question>\" as instruction and fine-tune models on the training set and evaluated the performance on the test set.", "section": "G Experiment details for GSM-8K", "sec_num": null}, {"text": "NeurIPS Paper Checklist • The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "section": "G Experiment details for GSM-8K", "sec_num": null}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "section": "G Experiment details for GSM-8K", "sec_num": null}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "section": "G Experiment details for GSM-8K", "sec_num": null}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Answer: [Yes]", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pre-trained language models, image generators, or scraped datasets)?", "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA] .", "section": "Safeguards", "sec_num": "11."}, {"text": "Justification: Our work poses no such risks.", "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [Yes]", "section": "Safeguards", "sec_num": "11."}, {"text": "Justification: All publicly available assets (models, code, and data) used in this work have been properly credited, and their respective licenses and terms of use have been explicitly mentioned and adhered to.", "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset.", "section": "Safeguards", "sec_num": "11."}, {"text": "* The corresponding author.", "section": "", "sec_num": null}], "back_matter": [{"text": "Acknowledgments. We thank <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> for their valuable discussions and support. PK is funded by CSIRO's Science Digital.", "section": "acknowledgement", "sec_num": null}, {"text": "Justification: Complete proofs for each theorem are provided in §B. Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "section": "annex", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: Training details and hyperparameter selection are presented in Sec. 4 and §F, respectively. Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. For example (a) If the contribution is primarily a new algorithm, the paper should make it clear how to reproduce that algorithm. (b) If the contribution is primarily a new model architecture, the paper should describe the architecture clearly and fully. (c) If the contribution is a new model (e.g., a large language model), then there should either be a way to access this model for reproducing the results or a way to reproduce the model (e.g., with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?Answer: [Yes] Justification: We will release our code. Guidelines:• The answer NA means that paper does not include experiments requiring code.• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e. • The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "section": "Open access to data and code", "sec_num": "5."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [Yes] Justification: All reported results are averaged over three random seeds. Guidelines:• The answer NA means that the paper does not include experiments.• The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean.• It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text.", "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments?Answer: [Yes]Justification: All experiments were conducted on a single NVIDIA H100 GPU with 96 GB memory, with each experiment completing within 8 hours.Guidelines:• The answer NA means that the paper does not include experiments.• The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?Answer: [Yes] ,Justification: We have carefully reviewed and adhered to the code of ethics throughout our research and writing process.Guidelines:• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?Answer: [Yes]Justification: Potential impacts are discussed in §A.Guidelines:• The answer NA means that there is no societal impact of the work performed.• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations.• For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "section": "Broader Impacts", "sec_num": "10."}], "ref_entries": {"FIGREF0": {"type_str": "figure", "uris": null, "fig_num": "2", "text": "Figure 2: Our pipeline. Adapter ∆h(•) and h 0 (•) from pre-trained model form the linear layer h of Multi-Head Attention and MLP in fine-tuned model. We perturb ∆h(•) with multiplicative noise and ensure the network remains consistent to same inputs under varying perturbations.", "num": null}, "FIGREF1": {"type_str": "figure", "uris": null, "fig_num": null, "text": "Gradient Norm. (b) FP-Distance (c) Train and validation accuracy.", "num": null}, "FIGREF2": {"type_str": "figure", "uris": null, "fig_num": "3", "text": "Figure 3: Analysis for PACE. (a) gradient norm, (b) FP-Distance and (c) train & val. accuracy are evaluated on validation set of CIFAR-100 (VTAB-1K) with baseline LoRA mul +VPT add on ViT-B/16.", "num": null}, "FIGREF3": {"type_str": "figure", "uris": null, "fig_num": "4", "text": "Figure 4: Analysis for PACE. (a) gradient norm, (b) FP-Distance and (c) train & val. accuracy are evaluated on the validation set of Camelyon (VTAB-1K) with baseline LoRA mul +VPT add on Swin-B.", "num": null}, "FIGREF4": {"type_str": "figure", "uris": null, "fig_num": "56", "text": "Figure 5: Gradient norms of models across wide range of regularization strengths λ on CIFAR-100 (VTAB-1K) w/ ViT-B/16. Line and shadow represent mean and std across training epochs.", "num": null}, "FIGREF5": {"type_str": "figure", "uris": null, "fig_num": "7", "text": "Figure 7: Results for varied λ and σ as well as shot on OxfordPets in few-shot learning.", "num": null}, "FIGREF6": {"type_str": "figure", "uris": null, "fig_num": "8", "text": "Figure 8: Gradient norms of (a) FPA and (b) PACE with different regularization strengths λ during training on CIFAR-100 (VTAB-1K) w/ ViT-B/16. Figure 5 illustrates the average gradient norm over training epochs.", "num": null}, "FIGREF7": {"type_str": "figure", "uris": null, "fig_num": null, "text": "-3 1e-3 1e-3 1e-3 1e-2 1e-3 5e-3 5e-3 5e-3 5e-4 5e-4 1e-4 5e-3 5e-3 5e-3 5e-3 1e-2 2e-4 WD 1e-4 1e-4 1e-3 1e-2 1e-3 1e-3 1e-2 1e-2 1e-2 1e-2 1e-4 1e-3 1e-4 1e-3 1e-3 1e-4 1e-2 1e-2 1e-2", "num": null}, "TABREF1": {"content": "<table><tr><td>Method</td><td>Cifar100</td><td>Caltech101</td><td>DTD</td><td>Natural Flowers102</td><td>Pets</td><td>SVHN</td><td>Sun397</td><td><PERSON><PERSON><PERSON></td><td>Specialized EuroSAT Resisc45</td><td>Retinopathy</td><td>Clevr-Count</td><td>Clevr-Dist</td><td>DMLab</td><td>Structured KITTI-Dist dSpr-Loc</td><td>dSpr-Ori</td><td>sNORB-Azim</td><td>NsORB-Ele</td><td>Mean Acc.</td></tr><tr><td>Full</td><td colspan=\"18\">68.9 87.7 64.3 97.3 86.9 87.4 38.8 79.7 95.7 84.2 73.9 56.3 58.6 41.7 65.5 57.5 46.7 25.7 29.1 68.9</td></tr><tr><td>Linear</td><td colspan=\"18\">64.4 85.0 63.2 97.0 86.3 36.6 51.0 78.5 87.5 68.5 74.0 34.3 30.6 33.2 55.4 12.5 20.0 9.6 19.2 57.6</td></tr><tr><td>VPT-Deep</td><td colspan=\"18\">78.8 901</td></tr><tr><td>RLRR</td><td colspan=\"18\">75.6 92.4 72.9 99.3 91.5 89.8 57.0 86.8 95.2 85.3 75.9 79.7 64.2 53.9 82.1 83.9 53.7 33.4 43.6 76.7</td></tr><tr><td>GLoRA</td><td colspan=\"18\">76.4 92.9 74.6 99.6 92.5 91.5 57.8 87.3 96.8 88.0 76.0 83.1 67.3 54.5 86.2 83.8 52.9 37.0 41.4 78.0</td></tr><tr><td>Baseline</td><td colspan=\"18\">74.9 93.3 72.0 99.4 91.0 91.5 54.8 83.2 95.7 86.9 74.2 83.0 70.5 51.9 81.4 77.9 51.7 33.6 44.4 76.4</td></tr><tr><td>+PACE</td><td colspan=\"18\">79.0 94.2 73.6 99.4 92.4 93.7 58.0 87.4 96.4 89.3 77.1 84.9 70.9 54.9 84.3 84.7 57.3 39.3 44.8 79.0</td></tr></table>", "type_str": "table", "html": null, "text": "Results on VTAB-1K with ViT-B/16. Mean Acc. is the average of group mean values. .8 65.8 98.0 88.3 78.1 49.6 81.8 96.1 83.4 68.4 68.5 60.0 46.5 72.8 73.6 47.9 32.9 37.8 72.0 Ada<PERSON>er 69.2 90.1 68.0 98.8 89.9 82.8 54.3 84.0 94.9 81.9 75.5 80.9 65.3 48.6 78.3 74.8 48.5 29.9 41.6 73.9 <PERSON><PERSON><PERSON><PERSON><PERSON> 70.8 91.2 70.5 99.1 90.9 86.6 54.8 83.0 95.8 84.4 76.3 81.9 64.3 49.3 80.3 76.3 45.7 31.7 41.1 74.7 LoRA 67.1 91.4 69.4 98.8 90.4 85.3 54.0 84.9 95.3 84.4 73.6 82.9 69.2 49.8 78.5 75.7 47.1 31.0 44.0 74.5 NOAH 69.6 92.7 70.2 99.1 90.4 86.1 53.7 84.4 95.4 83.9 75.8 82.8 68.9 49.9 81.7 81.8 48.3 32.8 44.2 74.2 RepAdapter 69.0 92.6 75.1 99.4 91.8 90.2 52.9 87.4 95.9 87.4 75.5 75.9 62.3 53.3 80.6 77.3 54.9 29.5 37.9 76.", "num": null}, "TABREF2": {"content": "<table><tr><td>Shot</td><td/><td colspan=\"3\">FGVCAircraft</td><td/><td/><td/><td>Food101</td><td/><td/><td/><td/><td>Flowers102</td><td/><td/></tr><tr><td>Method</td><td>1</td><td>2</td><td>4</td><td>8</td><td>16</td><td>1</td><td>2</td><td>4</td><td>8</td><td>16</td><td>1</td><td>2</td><td>4</td><td>8</td><td>16</td></tr><tr><td>Lo<PERSON> add</td><td colspan=\"15\">10.4 15.2 27.2 41.7 59.2 33.9 51.9 59.3 66.0 71.3 93.3 96.4 98.0 98.6 98.7</td></tr><tr><td>+PACE</td><td colspan=\"15\">10.7 16.3 28.2 42.1 61.0 40.6 55.9 63.8 70.3 75.2 95.0 98.0 98.9 99.5 99.6</td></tr><tr><td>VPT add</td><td colspan=\"15\">11.2 15.1 23.7 36.3 51.5 34.3 56.6 64.8 71.7 75.4 94.3 97.6 98.2 99.3 99.6</td></tr><tr><td>+PACE</td><td colspan=\"5\">11.6 16.2 24.0 37.0 52.4 OxfordPets</td><td/><td colspan=\"3\">StanfordCars</td><td/><td/><td/><td>Average</td><td/><td/></tr><tr><td>LoRA add</td><td colspan=\"15\">73.2 83.1 87.5 89.2 91.1 8.7 15.3 30.2 55.3 74.5 43.9 52.3 60.4 70.1 78.9</td></tr><tr><td>+PACE</td><td colspan=\"15\">75.3 85.0 90.7 90.8 92.4 9.4 16.0 30.9 56.1 75.9 46.2 54.2 62.5 71.7 80.8</td></tr><tr><td>VPT add</td><td colspan=\"15\">75.9 85.6 90.3 90.6 92.3 9.3 15.0 27.8 46.6 65.1 45.0 53.9 60.9 68.9 76.7</td></tr><tr><td>+PACE</td><td colspan=\"15\">78.2 87.4 90.3 91.1 92.3 9.9 15.4 27.9 47.0 65.9 46.9 54.8 61.5 69.3 77.2</td></tr><tr><td colspan=\"16\">LoRA mul +VPT add 69.9 84.1 89.1 91.3 91.9 9.0 16.3 32.7 59.0 76.4 43.0 53.5 62.6 73.2 81.2</td></tr><tr><td>+PACE</td><td colspan=\"15\">76.5 88.0 90.3 91.4 92.4 9.7 16.4 33.7 59.8 77.3 46.2 55.3 63.9 73.9 81.9</td></tr></table>", "type_str": "table", "html": null, "text": "Classification accuracy on Few-shot learning with ViT-B/16 pre-trained on ImageNet-21K. 39.9 57.2 66.7 72.4 76.1 95.3 97.8 98.6 99.4 99.6 LoRA mul +VPT add 10.5 15.6 28.4 44.8 61.8 35.4 54.3 64.8 72.1 76.4 90.4 97.3 98.4 99.4 99.5 +PACE 12.3 16.8 29.9 45.7 62.5 39.3 57.2 66.7 73.4 77.8 93.4 98.1 99.1 99.5 99.7", "num": null}, "TABREF3": {"content": "<table><tr><td>Method</td><td>CUB NA-Oxford Stan. Stan. Mean -2011 Birds Flowers Dogs Cars Acc.</td></tr><tr><td>Full</td><td>87.3 82.7 98.8 89.4 84.5 85.9</td></tr><tr><td>Linear</td><td>85.3 75.9 97.9 86.2 51.3 79.3</td></tr><tr><td>VPT</td><td>88.5 84.2 99.0 90.2 83.6 89.1</td></tr><tr><td>LoRA</td><td>88.3 85.6 99.2 91.0 83.2 89.5</td></tr><tr><td>SSF*</td><td>89.5 85.7 99.6 89.6 89.2 90.7</td></tr><tr><td>ARC*</td><td>89.3 85.7 99.7 89.1 89.5 90.7</td></tr><tr><td>RLRR*</td><td>89.8 85.3 99.6 90.0 90.4 91.0</td></tr><tr><td colspan=\"2\">LoRA mul +VPT add 88.9 87.1 99.4 91.2 87.5 90.8</td></tr><tr><td>+PACE</td><td>89.8 87.3 99.5 92.2 88.8 91.5</td></tr></table>", "type_str": "table", "html": null, "text": "Results on FGVC with ViT-B/16. * denotes using augmented ViT by AugReg[62].", "num": null}, "TABREF4": {"content": "<table><tr><td>Method</td><td colspan=\"2\">Source ImageNet -Sketch -V2 -A -R Acc. Target Mean</td></tr><tr><td>Full</td><td>63.9</td><td>18.5 52.5 3.2 21.2 31.8</td></tr><tr><td>Linear</td><td>67.9</td><td>14.4 60.8 9.4 25.6 35.6</td></tr><tr><td>Adapter</td><td>70.5</td><td>16.4 59.1 5.5 22.1 34.7</td></tr><tr><td>VPT</td><td>70.5</td><td>18.3 58.0 4.6 23.2 34.7</td></tr><tr><td>LoRA</td><td>70.8</td><td>20.0 59.3 6.9 23.3 36.0</td></tr><tr><td>NOAH</td><td>71.5</td><td>24.8 66.1 11.9 28.5 40.5</td></tr><tr><td>GLoRA</td><td>78.3</td><td>30.6 67.5 13.3 31.0 44.1</td></tr><tr><td>LoRA mul +VPT add</td><td>78.3</td><td>30.6 68.5 14.1 32.5 44.8</td></tr><tr><td>+PACE</td><td>79.0</td><td>31.8 69.4 16.3 35.2 46.3</td></tr></table>", "type_str": "table", "html": null, "text": "Results on domain adaptation with ViT-B/16 pre-trained on ImageNet-21K.", "num": null}, "TABREF5": {"content": "<table><tr><td>Method</td><td>COLA</td><td>STSB</td><td>MRPC</td><td>RTE</td><td>QNLI</td><td>SST2</td><td>Avg.</td></tr><tr><td>Full</td><td>63.6</td><td>91.2</td><td>90.2</td><td>78.7</td><td>92.8</td><td>94.8</td><td>85.2</td></tr><tr><td>BitFit</td><td>62.0</td><td>90.8</td><td>92.7</td><td>81.5</td><td>91.8</td><td>93.7</td><td>85.4</td></tr><tr><td>Adapt</td><td>62.6</td><td>90.3</td><td>88.4</td><td>75.9</td><td>93.0</td><td>94.7</td><td>84.2</td></tr><tr><td>VeRA</td><td>65.6</td><td>90.7</td><td>89.5</td><td>78.7</td><td>91.8</td><td>94.6</td><td>85.2</td></tr><tr><td>LoRA</td><td>63.4</td><td>91.5</td><td>89.7</td><td>86.6</td><td>93.3</td><td>95.1</td><td>86.6</td></tr><tr><td>+PACE</td><td>66.2</td><td>92.0</td><td>91.4</td><td>86.9</td><td>93.6</td><td>95.6</td><td>87.6</td></tr></table>", "type_str": "table", "html": null, "text": "Results for GLUE w/ RoBERTa base . <PERSON>'s correlation for COLA, Pearson correlation for STSB, and accuracy for others.", "num": null}, "TABREF6": {"content": "<table><tr><td>Method</td><td>Accuracy</td></tr><tr><td>Pre-trained</td><td>62.01</td></tr><tr><td>Full</td><td>73.16</td></tr><tr><td>LoRA</td><td>75.66</td></tr><tr><td>+PACE</td><td>78.77</td></tr></table>", "type_str": "table", "html": null, "text": "Results for GSM-8K using Phi-3-mini-4k-instruct.", "num": null}, "TABREF7": {"content": "<table/>", "type_str": "table", "html": null, "text": "Classification results on domain adaptation and CIFAR-100 in VTAB-1K based different pre-trained models. Src. is short for 'source' in Table", "num": null}, "TABREF8": {"content": "<table/>", "type_str": "table", "html": null, "text": "Full 51.6 63.9 18.5 52.5 3.2 21.2 51.2 66.0 29.0 56.1 8.1 27.9 65.6 71.7 27.0 61.1 10.8 24.4 Linear 63.4 67.9 14.4 60.8 9.4 25.6 61.9 79.2 43.2 69.5 23.4 40.9 65.0 78.8 36.7 68.8 23.2 35.9 LoRA add 71.2 73.8 27.1 64.8 13.6 25.0 71.3 77.5 39.8 67.8 20.4 35.6 74.3 76.3 30.7 65.7 16.8 28.9 VPT add 73.6 74.3 27.1 65.9 11.5 26.7 71.8 78.4 40.4 68.7 22.4 38.4 72.7 76.2 30.6 66.2 17.6 29.1 LoRA mul 73.4 78.1 31.2 68.3 13.4 32.7 73.2 78.6 41.9 68.8 22.6 37.8 73.9 76.1 30.8 65.7 18.1 28.9 LoRA add +VPT add 70.3 76.8 28.7 66.6 13.7 29.9 71.8 78.0 41.4 68.3 20.6 36.9 74.5 76.3 30.7 65.7 16.8 28.9 <PERSON><PERSON> mul +VPT add 74.9 78.3 30.6 68.5 14.1 32.5 73.8 78.3 41.5 68.6 21.6 38.2 74.6 76.6 31.2 66.5 18.5 29.4 +PACE 79.0 79.0 31.8 69.4 16.3 35.2 78.0 80.1 45.8 71.2 24.6 43.6 78.9 79.6 39.2 70.1 25.2 38.0 4.1 Comparison with the State of the Arts Results on VTAB-1K. Table 1 presents the results comparing PACE with recent state-of-the-art PEFT methods. PACE improves the strong baseline by 2.6% accuracy, surpassing the previous SOTA GLoRA [7] by 1%, which uses two stages for parameter search. In §D.1, we show that reducing training epochs to 50 or 100 has minimal impact on PACE performance. Results on Few-shot Learning. Table 2 compares performance w/ and w/o our PACE. PACE improves LoRA add , VPT add , LoRA mul +VPT add , with LoRA mul +VPT add +PACE performing best in most cases. PACE yields notable improvement, especially when the number of shot is small.", "num": null}, "TABREF9": {"content": "<table/>", "type_str": "table", "html": null, "text": "Table 4 compares PACE with others. LoRA mul +VPT add outperforms GLoRA [7] which relies on parameter search. Meanwhile, PACE improves LoRA mul +VPT add by 1.5%, outperforming other PEFT methods, demonstrating superior performance on domain adaptation.", "num": null}, "TABREF10": {"content": "<table/>", "type_str": "table", "html": null, "text": "and Came<PERSON><PERSON> (VTAB-1K) on Swin-B.Figures 3 & 4  show the gradient norm (summed across all layers) and FP-distance (Eq. 5) and the train & validation accuracy during training for baseline LoRA mul +VPT add and PACE on validation set. Figures3a & 4ashow that PACE has a smaller gradient norm than baseline, verifying Theorem 2 that PACE can implicitly lower the weight gradient norm for better generalization. Figures3b & 4bdemonstrate that PACE maintains a lower FP-distance than the baseline, verifying Theorem 3 that PACE can implicitly align the fine-tuned model with pre-trained model, retaining knowledge from large-scale pre-training. Owing to the advantages of the gradient regularization and model alignment, PACE shortens the performance gap between seen and unseen data, yielding higher accuracy on the unseen validation set, as shown in Figures3c & 4c.", "num": null}, "TABREF11": {"content": "<table><tr><td>Method</td><td colspan=\"2\">CIFAR -100 Source -Sketch -V2 -A -R ImageNet-1K</td></tr><tr><td colspan=\"2\">LoRA mul +VPT add 74.9 78.3</td><td>30.6 68.5 14.1 32.5</td></tr><tr><td>+Noise</td><td>77.4 78.3</td><td>31.3 68.6 14.3 33.0</td></tr><tr><td>+PACE</td><td>79.0 79.0</td><td>31.8 69.4 16.3 35.2</td></tr><tr><td>+PACE add</td><td>75.7 78.3</td><td>31.2 68.7 13.7 32.7</td></tr><tr><td colspan=\"3\">+PACE 6</td></tr><tr><td>+ℓ 1</td><td>75.2 78.2</td><td>30.6 68.6 13.7 32.8</td></tr><tr><td>+L2SP [77]</td><td>75.9 78.5</td><td>30.4 68.7 14.9 33.5</td></tr><tr><td>+DELTA [40]</td><td>76.4 78.4</td><td>30.8 68.7 14.6 33.7</td></tr><tr><td>+FTP [64]</td><td>76.2 78.6</td><td>30.8 68.6 15.8 33.6</td></tr></table>", "type_str": "table", "html": null, "text": "", "num": null}, "TABREF12": {"content": "<table><tr><td>Method</td><td colspan=\"9\">CIFAR-100 (ViT/16-B) GPU Memory Time Accuracy GPU Memory Time Accuracy GPU Memory Time Mean Acc. Camelyon (Swin-B) ImageNet (ViT/16-B)</td></tr><tr><td>LoRA mul +VPT add</td><td>8.9GB</td><td>29m</td><td>74.6</td><td>15.7GB</td><td>33m</td><td>86.7</td><td>8.9GB</td><td>161m</td><td>44.8</td></tr><tr><td>+PACE</td><td>17.7GB</td><td>53m</td><td>79.0</td><td>29.4GB</td><td>60m</td><td>89.3</td><td>17.7GB</td><td>278m</td><td>46.3</td></tr><tr><td>+PACE fast</td><td>9.0GB</td><td>29m</td><td>78.3</td><td>15.7GB</td><td>34m</td><td>88.8</td><td>9.0GB</td><td>162m</td><td>46.1</td></tr><tr><td>+PACE half lazy (N = 2)</td><td>9.3GB</td><td>29m</td><td>78.7</td><td>15.7GB</td><td>36m</td><td>89.2</td><td>9.0GB</td><td>165m</td><td>46.0</td></tr><tr><td>+PACE half lazy (N = 4)</td><td>9.3GB</td><td>29m</td><td>78.4</td><td>15.7GB</td><td>35m</td><td>88.9</td><td>9.0GB</td><td>163m</td><td>45.6</td></tr><tr><td>+PACE half lazy (N = 6)</td><td>9.3GB</td><td>29m</td><td>78.4</td><td>15.7GB</td><td>35m</td><td>89.0</td><td>9.0GB</td><td>163m</td><td>45.7</td></tr><tr><td>+PACE half lazy (N = 10)</td><td>9.3GB</td><td>29m</td><td>78.2</td><td>15.7GB</td><td>35m</td><td>88.9</td><td>9.0GB</td><td>162m</td><td>45.6</td></tr></table>", "type_str": "table", "html": null, "text": "GPU memory usage, training time, and accuracy for PACE fast and PACE half lazy . here, 'm' denotes minutes, Both variants outperform the baseline while maintaining similar computational demands.", "num": null}, "TABREF13": {"content": "<table><tr><td>Dataset</td><td>Memory of PACE fast</td><td>Baseline GPU Memory</td><td>Ratio</td></tr><tr><td>CIFAR-100 (VTAB-1K w/ ViT/16-B)</td><td>390KB</td><td>8.9GB</td><td>0.0042%</td></tr><tr><td>Camelyon (VTAB-1K w/ Swin-B)</td><td>7.81KB</td><td>15.7GB</td><td>0.000047%</td></tr><tr><td>ImageNet (Domain adaptation w/ ViT/16-B)</td><td>61MB</td><td>8.9GB</td><td>0.67%</td></tr></table>", "type_str": "table", "html": null, "text": "Comparison of PACE fast memory overhead and the baseline GPU memory requirements.", "num": null}, "TABREF14": {"content": "<table><tr><td>CIFAR-100 Mem. LoRA mul +VPT add Method 8.9GB 29m 74.6 15.7GB 33m 86.7 8.9GB 161m Camelyon ImageNet</td><td>44.8</td><td>Average 11.1GB 74m 68.7</td></tr><tr><td>+PACE fast ( 1 2 batch size, 1 2 epochs) 5.4GB 17m 78.1 8.6GB 21m 88.9 5.4GB 85m</td><td>45.8</td><td>6.5GB 41m 70.9</td></tr><tr><td>+PACE fast ( 1 4 batch size, 1 4 epochs) 3.5GB 10m 77.8 6.0GB 14m 88.7 3.5GB 50m</td><td>45.6</td><td>4.3GB 25m 70.7</td></tr><tr><td>+PACE fast ( 1 8 batch size, 1 8 epochs) 2.9GB 6m 77.2 5.2GB 10m 88.6 2.9GB 32m</td><td>45.5</td><td>3.7GB 16m 70.4</td></tr></table>", "type_str": "table", "html": null, "text": "Results of PACE fast with a reduced batch size and epochs on CIFAR-100 (VTAB-1K w/ ViT-B/16), Camelyon (VTAB-1K w/ Swin-B), ImageNet (Domain adaptaion w/ ViT-B/16). PACE fast outperforms baseline while using less GPU memory and training time. Time Acc. Mem. Time Acc. Mem. Time MeanAcc. Mem. Time Acc.", "num": null}, "TABREF15": {"content": "<table><tr><td>#Epoch</td><td>Method</td><td>Natural</td><td>Specialized</td><td>Structured</td><td>Avg.</td></tr><tr><td>530</td><td>GLoRA</td><td>83.61</td><td>87.02</td><td>63.27</td><td>77.97</td></tr><tr><td>100</td><td>Baseline</td><td>81.94</td><td>85.40</td><td>61.40</td><td>76.24</td></tr><tr><td>100</td><td>+PACE</td><td>83.94</td><td>87.44</td><td>64.62</td><td>78.67</td></tr><tr><td>50</td><td>+PACE (half batch size)</td><td>83.77</td><td>87.32</td><td>63.92</td><td>78.34</td></tr><tr><td>200</td><td>Baseline</td><td>82.28</td><td>85.30</td><td>61.64</td><td>76.40</td></tr><tr><td>200</td><td>+PACE</td><td>84.13</td><td>87.57</td><td>64.85</td><td>78.85</td></tr><tr><td>300</td><td>Baseline</td><td>82.41</td><td>85.00</td><td>61.80</td><td>76.40</td></tr><tr><td>300</td><td>+PACE</td><td>84.32</td><td>87.55</td><td>65.13</td><td>79.00</td></tr></table>", "type_str": "table", "html": null, "text": "Classification results for different methods on VTAB-1K with different training epochs.", "num": null}, "TABREF16": {"content": "<table><tr><td>Method</td><td>CUB 50% 20% 10% 50% 20% 10% 50% 20% 10% 50% 20% 10% 50% 20% 10% NAB Flowers Stanford Dogs Stanford Cars</td></tr><tr><td colspan=\"2\">baseline 87.1 83.9 79.1 80.7 75.0 70.2 98.5 96.5 93.1 90.6 88.7 86.9 78.7 54.9 30.1</td></tr><tr><td colspan=\"2\">+PACE 88.4 85.5 81.4 82.9 77.5 73.8 99.2 97.9 96.1 91.8 90.9 89.8 80.5 57.3 33.2</td></tr></table>", "type_str": "table", "html": null, "text": "Classification results on FGVC using varying percentages of data based on ViT-B/16.", "num": null}, "TABREF17": {"content": "<table><tr><td>Method</td><td>SVHN</td><td>MAE Camelyon</td><td>Clevr-Count</td><td>SVHN</td><td>DINO Camelyon</td><td>Clevr-Count</td></tr><tr><td>Full</td><td>90.1</td><td>74.6</td><td>52.5</td><td>89.7</td><td>73.1</td><td>34.5</td></tr><tr><td>Linear</td><td>44.5</td><td>79.9</td><td>57.1</td><td>50.7</td><td>82.5</td><td>44.2</td></tr><tr><td>LoRA mul +VPT add</td><td>89.3</td><td>82.7</td><td>82.1</td><td>90.0</td><td>85.4</td><td>55.7</td></tr><tr><td>+PACE</td><td>93.5</td><td>85.8</td><td>86.4</td><td>91.7</td><td>88.1</td><td>61.0</td></tr><tr><td colspan=\"4\">D.4 Experiments of Combining PACE with Other PEFT</td><td/><td/><td/></tr></table>", "type_str": "table", "html": null, "text": "Classification results on VTAB-1K using self-supervised DINO and MAE, with ViT-B/16 pre-trained on the ImageNet-1K dataset.", "num": null}, "TABREF18": {"content": "<table><tr><td>Method</td><td colspan=\"3\">CIFAR-100 (VTAB-1K)</td><td>Source</td><td colspan=\"3\">ImageNet (Domain Adaptation) -Sketch -V2 -A</td><td>-R</td><td>Avg.</td></tr><tr><td>AdaptFormer</td><td/><td>70.6</td><td/><td>77.4</td><td>26.5</td><td>67.4</td><td>12.4</td><td>28.7</td><td>42.4</td></tr><tr><td>+PACE</td><td/><td>74.8</td><td/><td>78.2</td><td>27.4</td><td>67.9</td><td>13.9</td><td>31.7</td><td>43.8</td></tr><tr><td>GLoRA</td><td/><td>75.9</td><td/><td>78.2</td><td>30.3</td><td>68.1</td><td>13.5</td><td>31.6</td><td>44.3</td></tr><tr><td>+PACE</td><td/><td>78.6</td><td/><td>78.8</td><td>31.7</td><td>69.0</td><td>15.9</td><td>34.4</td><td>45.9</td></tr><tr><td>COFT</td><td/><td>71.8</td><td/><td>76.9</td><td>26.4</td><td>66.7</td><td>13.1</td><td>30.7</td><td>42.7</td></tr><tr><td>+PACE</td><td/><td>75.3</td><td/><td>77.8</td><td>27.9</td><td>68.2</td><td>14.9</td><td>32.9</td><td>44.3</td></tr><tr><td>BOFT</td><td/><td>72.3</td><td/><td>77.1</td><td>27.0</td><td>66.8</td><td>12.8</td><td>31.1</td><td>42.9</td></tr><tr><td>+PACE</td><td/><td>75.7</td><td/><td>77.9</td><td>28.3</td><td>68.2</td><td>14.7</td><td>33.4</td><td>44.5</td></tr><tr><td colspan=\"2\">E Additional Plots</td><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"9\">Figures 8 and 9 show the gradient issues in FPA and the gradient regularization effects of PACE.</td></tr><tr><td colspan=\"2\">Baseline</td><td>0.001</td><td/><td>0.005</td><td>0.01</td><td>0.05</td><td>0.1</td><td>0.5</td></tr><tr><td>3e4</td><td/><td/><td/><td/><td>3e4</td><td/><td/></tr><tr><td>1e4 2e4 ∥∂f /∂Θ∥ 2</td><td/><td/><td/><td/><td>1e4 2e4</td><td/><td/></tr><tr><td colspan=\"2\">epoch=</td><td>100</td><td>200</td><td>300</td><td>epoch=</td><td>100</td><td>200</td><td>300</td></tr></table>", "type_str": "table", "html": null, "text": "Classification results of different PEFT methods based on ViT-B/16.", "num": null}, "TABREF19": {"content": "<table><tr><td>∥</td><td>∂f ∂θ</td><td colspan=\"2\">∥ 2</td><td>Baseline</td><td>+FPA</td><td>+PACE</td></tr><tr><td/><td colspan=\"2\">6e3</td><td/><td/><td/></tr><tr><td/><td colspan=\"2\">3e3</td><td/><td/><td/></tr><tr><td/><td colspan=\"2\">λ=</td><td colspan=\"4\">1e-3 5e-3 0.01 0.05 0.1 0.5 1 5 10 50 100 500 1e3 5e3 1e4 5e4</td></tr><tr><td>Figure 9:</td><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "html": null, "text": "Gradient norms of models across wide range of regularization strengths λ on Camelyon (VTAB-1K) w/ Swin-B. Line and shadow represent mean and std over training epochs. While gradient explosion is less frequent for FPA in this setting, it exhibits unpredictable gradient norm with varied regularization strengths. In contrast, PACE reliably lowers gradient norms as regularization strength λ increases, demonstrating its robustness for effective gradient control.", "num": null}, "TABREF20": {"content": "<table><tr><td/><td/><td/><td/><td>Natural</td><td/><td/><td/><td/><td colspan=\"2\">Specialized</td><td/><td/><td/><td/><td colspan=\"2\">Structured</td><td/><td/><td/><td/></tr><tr><td>Hyperparameter</td><td>Cifar100</td><td>Caltech101</td><td>DTD</td><td>Flowers102</td><td>Pets</td><td>SVHN</td><td>Sun397</td><td>Camelyon</td><td>EuroSAT</td><td>Resisc45</td><td>Retinopathy</td><td>Clevr-Count</td><td>Clevr-Dist</td><td>DMLab</td><td>KITTI-Dist</td><td>dSpr-Loc</td><td>dSpr-Ori</td><td>sNORB-Azim</td><td>NsORB-Ele</td><td>Average parameter (M)</td></tr><tr><td colspan=\"2\">Method A</td><td>A</td><td>A</td><td>A</td><td>A</td><td>A</td><td>A</td><td>A</td><td>A</td><td>A</td><td>B</td><td>B</td><td>B</td><td>A</td><td>A</td><td>A</td><td>A</td><td>A</td><td>B</td><td/></tr><tr><td>Rank</td><td colspan=\"7\">10 14 12 18 18 14 10</td><td>8</td><td>8</td><td>10</td><td>2</td><td>2</td><td>8</td><td>18</td><td>4</td><td colspan=\"3\">10 10 22</td><td/><td>1.81</td></tr></table>", "type_str": "table", "html": null, "text": "Hyperparameters for baseline on VTAB-1K with ViT-B/16. A: LoRA mul +VPT add , B: LoRA add . lr: learning rate. WD: weight decay.", "num": null}, "TABREF21": {"content": "<table><tr><td colspan=\"6\">learning rate FGVCAircraft Food101 Flowers102 OxfordPets StanfordCars</td><td>Mean</td></tr><tr><td>Baseline</td><td>5e-3</td><td>5e-3</td><td>5e-3</td><td>2e-3</td><td>2e-3</td><td>Parameter (M)</td></tr><tr><td>LoRA add</td><td>4</td><td>4</td><td>4</td><td>4</td><td>10</td><td>0.93</td></tr><tr><td>VPT add</td><td>1</td><td>1</td><td>1</td><td>1</td><td>1</td><td>0.14</td></tr><tr><td>LoRA mul +VPT add</td><td>14</td><td>10</td><td>18</td><td>18</td><td>24</td><td>2.70</td></tr></table>", "type_str": "table", "html": null, "text": "Ranks for baselines in Few-shot learning. Weight decay is fixed at 1e-4.", "num": null}, "TABREF22": {"content": "<table><tr><td colspan=\"7\">Hyperparameter CUB-200-2011 NABirds OxfordFlowers StanfordDogs StanfordCars Mean Parameter (M)</td></tr><tr><td>learning rate</td><td>5e-3</td><td>5e-4</td><td>5e-3</td><td>5e-3</td><td>2e-4</td><td/></tr><tr><td>weight decay</td><td>1e-2</td><td>1e-3</td><td>1e-3</td><td>1e-2</td><td>1e-3</td><td>2.80</td></tr><tr><td>rank</td><td>14</td><td>18</td><td>18</td><td>24</td><td>14</td><td/></tr></table>", "type_str": "table", "html": null, "text": "Hyperparameters for the baseline LoRA mul +VPT add in FGVC.", "num": null}, "TABREF23": {"content": "<table><tr><td>Baseline</td><td>rank</td><td>learning rate</td><td>weight decay</td><td>Parameter (M)</td></tr><tr><td>LoRA mul +VPT add</td><td>10</td><td>5e-4</td><td>1e-2</td><td>2.39</td></tr></table>", "type_str": "table", "html": null, "text": "Hyperparameters for baseline LoRA mul +VPT add in domain adaptation.", "num": null}, "TABREF24": {"content": "<table><tr><td>1. Claims</td></tr><tr><td>Question: Do the main claims made in the abstract and introduction accurately reflect the</td></tr><tr><td>paper's contributions and scope?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: We theoretically and empirically verify the claims and contributions made in</td></tr><tr><td>the abstract and introduction.</td></tr><tr><td>Guidelines:</td></tr><tr><td>•</td></tr></table>", "type_str": "table", "html": null, "text": "The answer NA means that the abstract and introduction do not include the claims made in the paper.• The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.2. LimitationsQuestion: Does the paper discuss the limitations of the work performed by the authors? Answer: [Yes] Justification: The limitations of our work are discussed in §A Guidelines:", "num": null}}}}