{"paper_id": "AsyncDiff", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:37:31.233173Z"}, "title": "AsyncDiff: Parallelizing Diffusion Models by Asynchronous Denoising", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "National University of Singapore", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": "", "affiliation": {"laboratory": "", "institution": "National University of Singapore", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "National University of Singapore", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "National University of Singapore", "location": {}}, "email": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "National University of Singapore", "location": {}}, "email": "<EMAIL>"}], "year": "", "venue": null, "identifiers": {}, "abstract": "We introduce a new distributed acceleration paradigm that attains a 2.8x speed-up on Stable Diffusion XL while maintaining pixel-level consistency, using four NVIDIA A5000 GPUs.", "pdf_parse": {"paper_id": "AsyncDiff", "_pdf_hash": "", "abstract": [{"text": "We introduce a new distributed acceleration paradigm that attains a 2.8x speed-up on Stable Diffusion XL while maintaining pixel-level consistency, using four NVIDIA A5000 GPUs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Diffusion models [13] stand out in generative modeling and have significantly advanced various fields including text-to-image [43, 41, 45, 46, 72, 78 ] and text-to-video generation [64, 9, 61, 21, 2] , Figure 2 : By preparing each component's input beforehand, we enable parallel computation of the denoising model, which substantially reduces latency while minimally affecting quality.", "cite_spans": [{"start": 17, "end": 21, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 126, "end": 130, "text": "[43,", "ref_id": "BIBREF42"}, {"start": 131, "end": 134, "text": "41,", "ref_id": "BIBREF40"}, {"start": 135, "end": 138, "text": "45,", "ref_id": "BIBREF44"}, {"start": 139, "end": 142, "text": "46,", "ref_id": "BIBREF45"}, {"start": 143, "end": 146, "text": "72,", "ref_id": "BIBREF71"}, {"start": 147, "end": 149, "text": "78", "ref_id": "BIBREF77"}, {"start": 181, "end": 185, "text": "[64,", "ref_id": "BIBREF63"}, {"start": 186, "end": 188, "text": "9,", "ref_id": "BIBREF8"}, {"start": 189, "end": 192, "text": "61,", "ref_id": "BIBREF60"}, {"start": 193, "end": 196, "text": "21,", "ref_id": "BIBREF20"}, {"start": 197, "end": 199, "text": "2]", "ref_id": "BIBREF1"}], "ref_spans": [{"start": 209, "end": 210, "text": "2", "ref_id": null}], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "image translation [49, 56, 23] , audio generation [22, 14, 44] , style transfer [62, 4, 17] , low-level vision tasks [47, 60, 40, 26, 8, 70, 3] , image editing [19, 66, 51, 77] , and 3D model generation [42, 18, 37] , among others. However, their widespread application is hindered by the high latency inherent in their multi-step sequential denoising process. This issue becomes more pronounced as the complexity and size of the models increase to enhance generative quality.", "cite_spans": [{"start": 18, "end": 22, "text": "[49,", "ref_id": "BIBREF48"}, {"start": 23, "end": 26, "text": "56,", "ref_id": "BIBREF55"}, {"start": 27, "end": 30, "text": "23]", "ref_id": "BIBREF22"}, {"start": 50, "end": 54, "text": "[22,", "ref_id": "BIBREF21"}, {"start": 55, "end": 58, "text": "14,", "ref_id": "BIBREF13"}, {"start": 59, "end": 62, "text": "44]", "ref_id": "BIBREF43"}, {"start": 80, "end": 84, "text": "[62,", "ref_id": "BIBREF61"}, {"start": 85, "end": 87, "text": "4,", "ref_id": "BIBREF3"}, {"start": 88, "end": 91, "text": "17]", "ref_id": "BIBREF16"}, {"start": 117, "end": 121, "text": "[47,", "ref_id": "BIBREF46"}, {"start": 122, "end": 125, "text": "60,", "ref_id": "BIBREF59"}, {"start": 126, "end": 129, "text": "40,", "ref_id": "BIBREF39"}, {"start": 130, "end": 133, "text": "26,", "ref_id": "BIBREF25"}, {"start": 134, "end": 136, "text": "8,", "ref_id": "BIBREF7"}, {"start": 137, "end": 140, "text": "70,", "ref_id": "BIBREF69"}, {"start": 141, "end": 143, "text": "3]", "ref_id": "BIBREF2"}, {"start": 160, "end": 164, "text": "[19,", "ref_id": "BIBREF18"}, {"start": 165, "end": 168, "text": "66,", "ref_id": "BIBREF65"}, {"start": 169, "end": 172, "text": "51,", "ref_id": "BIBREF50"}, {"start": 173, "end": 176, "text": "77]", "ref_id": "BIBREF76"}, {"start": 203, "end": 207, "text": "[42,", "ref_id": "BIBREF41"}, {"start": 208, "end": 211, "text": "18,", "ref_id": "BIBREF17"}, {"start": 212, "end": 215, "text": "37]", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In response to these challenges, significant research efforts are directed toward enhancing the efficiency of diffusion models. Notably, training-free acceleration methods have garnered increasing popularity due to their low cost and convenience. Numerous studies [35, 63, 76, 67, 53, 25, 33, 57, 34] improve inference speed by skipping redundant calculations in the denoising process. As computational resources grow rapidly, distributing computations across multiple devices has become a more promising approach. Recent advances [52, 24, 58] demonstrate that using distributed computing to parallelize inference effectively increases the acceleration ratio for diffusion models while maintaining acceptable generative quality. Though these methods succeed in parallelizing the diffusion models, they require iterative refining [52] or displaced patch parallelism [24] , resulting in a larger number of model evaluations or low GPU utilization correspondingly. Thus, we wish to propose a new parallel paradigm for diffusion, akin to the model parallelism in distributed computing [15, 38, 28, 16, 39, 65] , which divides the denoising model into several components to be distributed on different GPUs. The primary challenge lies in the inherent sequential denoising process of diffusion models. Each step in this process depends on the completion of its predecessor, forming a dependency chain that impedes parallelization and significantly increases inference latency. Our approach seeks to disrupt this chain, allowing for the parallel execution of the denoising model while closely approximating the results of the sequential process.", "cite_spans": [{"start": 264, "end": 268, "text": "[35,", "ref_id": "BIBREF34"}, {"start": 269, "end": 272, "text": "63,", "ref_id": "BIBREF62"}, {"start": 273, "end": 276, "text": "76,", "ref_id": "BIBREF75"}, {"start": 277, "end": 280, "text": "67,", "ref_id": "BIBREF66"}, {"start": 281, "end": 284, "text": "53,", "ref_id": "BIBREF52"}, {"start": 285, "end": 288, "text": "25,", "ref_id": "BIBREF24"}, {"start": 289, "end": 292, "text": "33,", "ref_id": "BIBREF32"}, {"start": 293, "end": 296, "text": "57,", "ref_id": "BIBREF56"}, {"start": 297, "end": 300, "text": "34]", "ref_id": "BIBREF33"}, {"start": 531, "end": 535, "text": "[52,", "ref_id": "BIBREF51"}, {"start": 536, "end": 539, "text": "24,", "ref_id": "BIBREF23"}, {"start": 540, "end": 543, "text": "58]", "ref_id": "BIBREF57"}, {"start": 829, "end": 833, "text": "[52]", "ref_id": "BIBREF51"}, {"start": 865, "end": 869, "text": "[24]", "ref_id": "BIBREF23"}, {"start": 1081, "end": 1085, "text": "[15,", "ref_id": "BIBREF14"}, {"start": 1086, "end": 1089, "text": "38,", "ref_id": "BIBREF37"}, {"start": 1090, "end": 1093, "text": "28,", "ref_id": "BIBREF27"}, {"start": 1094, "end": 1097, "text": "16,", "ref_id": "BIBREF15"}, {"start": 1098, "end": 1101, "text": "39,", "ref_id": "BIBREF38"}, {"start": 1102, "end": 1105, "text": "65]", "ref_id": "BIBREF64"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In this paper, we introduce AsyncDiff, a universal, distributed acceleration paradigm that innovatively explores model parallelism in diffusion models. As shown in Fig 2,  our method sequentially partitions the heavyweight denoising model ϵ θ into multiple components {ϵ n θ } N n=1 based on computational load, assigning each to a separate device. Our core idea lies in decoupling the dependencies between these cascaded components by leveraging the high similarity in hidden states across consecutive diffusion steps. After the initial warm-up steps, each component takes the output from the previous component's prior step as the approximation of its original input. This transforms the traditional sequential denoising into an asynchronous process, allowing components to predict noise for different time steps in parallel. Additionally, we incorporate stride denoising to skip redundant calculations and reduce the frequency of communication between devices, further enhancing efficiency.", "cite_spans": [{"start": 164, "end": 170, "text": "Fig 2,", "ref_id": null}, {"start": 171, "end": 171, "text": "", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Through extensive testing across multiple base models, our method effectively distributes the computational burden across various devices, substantially boosting inference speed while maintaining quality. Specifically, with the text-to-image model Stable Diffusion v2.1 [43] , our method achieves a 1.8x speedup with only a marginal 0.01 drop in CLIP Score [11] , and a 4.0x speedup with a slight 0.38 reduction in CLIP Score on two and four NVIDIA A5000 GPUs, respectively. For video diffusion models, AnimateDiff [9] and Stable Video Diffusion [2] , our approach significantly reduces latency by tens of seconds, effectively preserving video quality.", "cite_spans": [{"start": 270, "end": 274, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 357, "end": 361, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 515, "end": 518, "text": "[9]", "ref_id": "BIBREF8"}, {"start": 546, "end": 549, "text": "[2]", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In summary, we present a novel distributed acceleration method for diffusion models that significantly reduces inference latency with minimal impact on generation quality. This is achieved by replacing the sequential denoising process with an asynchronous process, allowing each component of the denoising model to run independently across different devices. Extensive experiments on both image and video diffusion models strongly demonstrate the effectiveness and versatility of our method.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "2 Related Works Diffusion Models. Diffusion models have attracted significant attention due to their powerful generative capabilities across various tasks. <PERSON><PERSON><PERSON><PERSON> et al. [54] first proposed diffusion probabilistic models. <PERSON> et al. [13] with the introduction of Denoising Diffusion Probabilistic Models (DDPM), enhancing training efficiency and generation quality. <PERSON><PERSON><PERSON> et al. [43] advanced these models by incorporating latent spaces, enabling high-resolution image generation. Despite these advancements, the high latency of the iterative denoising process remains a limitation.", "cite_spans": [{"start": 178, "end": 182, "text": "[54]", "ref_id": "BIBREF53"}, {"start": 240, "end": 244, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 388, "end": 392, "text": "[43]", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Inference Acceleration. Training-based acceleration methods focus on reducing sampling steps [48, 71, 32, 50, 69] or optimizing model architectures [27, 80, 7, 73, 68, 6] . However, these methods incur high training costs and complexity. Training-free methods are gaining popularity due to their ease of use. Some approaches develop fast solvers for SDE or ODE to improve sampling efficiency [31, 1, 30, 74, 81] . Other works [35, 63, 76, 67, 53, 25, 33, 79] observed special characteristics of diffusion models and skipped the redundant computation within the denoising process.", "cite_spans": [{"start": 93, "end": 97, "text": "[48,", "ref_id": "BIBREF47"}, {"start": 98, "end": 101, "text": "71,", "ref_id": "BIBREF70"}, {"start": 102, "end": 105, "text": "32,", "ref_id": "BIBREF31"}, {"start": 106, "end": 109, "text": "50,", "ref_id": null}, {"start": 110, "end": 113, "text": "69]", "ref_id": "BIBREF68"}, {"start": 148, "end": 152, "text": "[27,", "ref_id": "BIBREF26"}, {"start": 153, "end": 156, "text": "80,", "ref_id": "BIBREF79"}, {"start": 157, "end": 159, "text": "7,", "ref_id": "BIBREF6"}, {"start": 160, "end": 163, "text": "73,", "ref_id": "BIBREF72"}, {"start": 164, "end": 167, "text": "68,", "ref_id": "BIBREF67"}, {"start": 168, "end": 170, "text": "6]", "ref_id": "BIBREF5"}, {"start": 392, "end": 396, "text": "[31,", "ref_id": "BIBREF30"}, {"start": 397, "end": 399, "text": "1,", "ref_id": "BIBREF0"}, {"start": 400, "end": 403, "text": "30,", "ref_id": "BIBREF29"}, {"start": 404, "end": 407, "text": "74,", "ref_id": "BIBREF73"}, {"start": 408, "end": 411, "text": "81]", "ref_id": "BIBREF80"}, {"start": 426, "end": 430, "text": "[35,", "ref_id": "BIBREF34"}, {"start": 431, "end": 434, "text": "63,", "ref_id": "BIBREF62"}, {"start": 435, "end": 438, "text": "76,", "ref_id": "BIBREF75"}, {"start": 439, "end": 442, "text": "67,", "ref_id": "BIBREF66"}, {"start": 443, "end": 446, "text": "53,", "ref_id": "BIBREF52"}, {"start": 447, "end": 450, "text": "25,", "ref_id": "BIBREF24"}, {"start": 451, "end": 454, "text": "33,", "ref_id": "BIBREF32"}, {"start": 455, "end": 458, "text": "79]", "ref_id": "BIBREF78"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Parallelism. The parallelism strategy presents a promising yet underexplored approach to accelerating diffusion models. ParaDiGMS [52] implements Picard iterations for parallel sampling, yet its practical speed-up ratio is modest, and it struggles to maintain consistency with original outputs. Faster Diffusion [25] introduces encoder propagation but significantly compromises quality, and its parallelization remains theoretical. Distrifusion [24] adopts patch parallelism, dividing highresolution images into sub-patches to facilitate parallel inference on each patch by reusing stale activation maps from each layer. However, this approach lacks flexibility across different data types or tasks, often encountering low resource utilization. Furthermore, its reliance on reusing per-layer activation maps greatly increases GPU memory demands thus introducing additional challenges for realistic applications. In contrast, our method uniquely implements model parallelism through asynchronous denoising, achieving substantial acceleration while maintaining a stable resource usage ratio and minimal impact on quality.", "cite_spans": [{"start": 130, "end": 134, "text": "[52]", "ref_id": "BIBREF51"}, {"start": 312, "end": 316, "text": "[25]", "ref_id": "BIBREF24"}, {"start": 445, "end": 449, "text": "[24]", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Diffusion models [13] are a dominant class of generative models that transform Gaussian noise into complex data distributions via a Markov process. The forward process is defined by:", "cite_spans": [{"start": 17, "end": 21, "text": "[13]", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "Preliminary", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "q(x t |x t-1 ) = N (x t ; 1 -β t x t-1 , β t I),", "eq_num": "(1)"}], "section": "Preliminary", "sec_num": "3.1"}, {"text": "where {β t } progressively increases noise until the data becomes indistinguishable from noise. The reverse process, essential for data reconstruction, involves iterative denoising:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminary", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p θ (x t-1 |x t ) = N (x t-1 ; µ θ (x t , t), σ 2 t I),", "eq_num": "(2)"}], "section": "Preliminary", "sec_num": "3.1"}, {"text": "where µ θ (x t , t) is the predicted mean and σ 2 t is the variance. For DDIMs [55] , the reverse update is deterministic:", "cite_spans": [{"start": 79, "end": 83, "text": "[55]", "ref_id": "BIBREF54"}], "ref_spans": [], "eq_spans": [], "section": "Preliminary", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x t-1 = α t-1 α t x t + 1 -α t-1 1 - 1 -α t α t-1 ϵ θ (x t , t),", "eq_num": "(3)"}], "section": "Preliminary", "sec_num": "3.1"}, {"text": "where α t is the cumulative product of (1 -β t ). These processes are computationally intensive, influencing the quality of generated samples and necessitating efficient inference methods for practical applications.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Preliminary", "sec_num": "3.1"}, {"text": "Traditional diffusion models employ a sequential and synchronous denoising process. At each time step t, the noise-prediction model ϵ θ estimates the noise ϵ t based on the noisy image x t and the time", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Asynchronous Diffusion Model", "sec_num": "3.2"}, {"text": "� � � � GPU 3 (� � 4 ) T � �-1 T T-1 T-1 � � T � �-1 T-1 � � T � �-1 T-1 � �-1 T-2 T-2 T-2 T-2 � �-2 T-3 T-3 T-3 T-3 � �-3 T-4 T-4 T-4 T-4 � �-4 � �-5 Warm-up Parallel Parallel Parallel Parallel GPU 2 (� � 3 ) GPU 1 (� � 2 ) GPU 0 (� � 1 )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Asynchronous Diffusion Model", "sec_num": "3.2"}, {"text": "Communication within device Communication across devices T: Time embedding embedding t. The image for the next step, x t-1 , is then generated using a sampler function S(x t , ϵ t , t). This process is iterative, where the generation of ϵ t at each step is dependent on the completion of the previous denoising step, making the process slow, particularly when ϵ θ is computationally intensive.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Asynchronous Diffusion Model", "sec_num": "3.2"}, {"text": "To address the limitations of high latency in diffusion models, leveraging multiple GPUs for distributed inference is a promising solution. Existing studies primarily focus on patch parallelism [24] , where the input image is divided into patches, each processed on a different GPU. While this strategy efficiently distributes computational loads, it still retains the bottleneck of sequential denoising, as each patch must undergo the complete denoising process iteratively. In contrast, our asynchronous diffusion model innovatively introduces a model parallelism strategy. By approximating the sequential denoising as an asynchronous process, this approach enables parallel inference of the noise prediction model, effectively reducing latency and breaking the constraints of sequential execution. Asynchronous Denoising. Figure 3 illustrates our approach to the asynchronous denoising. For a denoising process consisting of T steps, the initial w steps are designated as a warm-up phase, where w is significantly smaller than T . During this phase, the denoising model ϵ θ operates using standard sequential inference. After warm-up steps, rather than splitting the input image, we partition the denoising model ϵ θ into N sequential components, expressed as ϵ θ = {ϵ 1 θ , ϵ 2 θ , ..., ϵ N θ }. Each component is divided to handle a comparable computational load and assigned to a distinct device. This equitable division aims to equalize the time cost of each component to approximately l(ϵ θ )/N , thus minimizing the overall maximum latency. In this setup, original noise prediction for x t can be represented as a cascading operation through these sub-models, defined mathematically as:", "cite_spans": [{"start": 194, "end": 198, "text": "[24]", "ref_id": "BIBREF23"}], "ref_spans": [{"start": 832, "end": 833, "text": "3", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Asynchronous Diffusion Model", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ϵ t = ϵ θ (x t , t) = ϵ N θ (ϵ N -1 θ (. . . ϵ 2 θ (ϵ 1 θ (x t , t), t) . . . , t), t).", "eq_num": "(4)"}], "section": "Asynchronous Diffusion Model", "sec_num": "3.2"}, {"text": "Although each device can independently compute its assigned component, the dependency chain persists because the input for each component ϵ θ,n is derived from the output from its preceding component ϵ θ,n-1 . Therefore, despite the distribution of model components across multiple devices, full parallelization is constrained by these sequential dependencies.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Asynchronous Diffusion Model", "sec_num": "3.2"}, {"text": "Our principal innovation is to break the dependency between cascaded components by utilizing hidden features from previous steps. Observations indicate that the hidden states of each block in the denoising model always exhibit substantial similarity across adjacent time steps. Leveraging this, each component at time step t can take the output from the preceding component at time step t -1 as the approximation of its original input. Specifically, the n-th component ϵ n θ (, t) receives the output of θ and ϵ 2 θ are skipped at time step t. A single parallel batch results in the completion of denoising for two steps, producing x t-1 and x t-2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Asynchronous Diffusion Model", "sec_num": "3.2"}, {"text": "GPU 3(� � 3 ) � �-2 � �-1 � � t-", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Asynchronous Diffusion Model", "sec_num": "3.2"}, {"text": "ϵ n-1 θ (•, t -1). This alteration allows the noise prediction for x t to be represented as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "t-1 t-1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ϵ t = ϵ N θ (ϵ N -1 θ (. . . ϵ 2 θ (ϵ 1 θ (x t+N -1 , t + N -1), t + N -2) . . . , t + 1), t).", "eq_num": "(5)"}], "section": "t-1 t-1", "sec_num": null}, {"text": "In this new framework, noise prediction ϵ t is derived from components executed across N previous time steps. This transforms the denoising process from sequential to asynchronous, as the prediction of noise ϵ t already begins before denoising at step t + 1 is completed. At each time step, the N components are running as parts of the noise prediction model for the next N steps. Specifically, the n-th component ϵ n θ , computed in parallel at time t, contributes to the noise prediction for the future time step t -N + n. Figure 3 depicts this asynchronous process using a U-net model with N set to 4. The strong resemblance of hidden states between consecutive diffusion steps enables the asynchronous process to closely mimic the denoising results of the original sequential process.", "cite_spans": [], "ref_spans": [{"start": 532, "end": 533, "text": "3", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "t-1 t-1", "sec_num": null}, {"text": "Model Parallelism. By transitioning to an asynchronous denoising strategy, the dependencies among components within the same time step are eliminated. This adjustment allows each component's input for time step t to be prepared in advance, enabling the N split components to be processed concurrently across multiple devices. Once computed, the outputs from each component must be stored and then broadcasted to other devices to facilitate parallel processing for subsequent time steps. In contrast, in the traditional sequential denoising process, the time cost for each step accumulates as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "t-1 t-1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "C seq (t) = C(ϵ 1 θ ) + C(ϵ 2 θ ) + . . . + C(ϵ N θ ).", "eq_num": "(6"}], "section": "t-1 t-1", "sec_num": null}, {"text": ") By adopting asynchronous denoising to enable parallel computation of each component, the cost for each time step is now given by:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "t-1 t-1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "C asy (t) = max(C(ϵ 1 θ ), C(ϵ 2 θ ), ..., C(ϵ N θ )) + C(comm.),", "eq_num": "(7)"}], "section": "t-1 t-1", "sec_num": null}, {"text": "where max() represents taking the maximum value, and C(comm.) indicates the communication cost across multiple GPUs. As the model components are equally divided by computational load, their time costs are similar, allowing us to approximate the overall cost of each time step as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "t-1 t-1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "C asy (t) ≈ C seq (t) N + C(comm.).", "eq_num": "(8)"}], "section": "t-1 t-1", "sec_num": null}, {"text": "Since the communication overhead C(comm.) is generally much lower than the model's execution time, it leads to significant overall cost reductions. Moreover, increasing N further reduces time costs but complicates the accurate approximation of the original denoising process.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "t-1 t-1", "sec_num": null}, {"text": "Stride Denoising. While asynchronous denoising reduces latency by parallelizing the denoising model, it completes only one denoising step at a time. To enhance efficiency, we introduce stride denoising, which completes multiple denoising steps simultaneously through a single parallel computation. The diagram is illustrated in Figure 4 , where we set the stride to 2 for clarity. Unlike the continuous broadcasting of hidden states at each time step, stride denoising broadcasts them every two steps. As depicted, at time step t, we conduct denoising alone, and at time step t -1, we compute and broadcast the hidden states for the next parallel computation round. Consequently, the hidden states from time step t are not required, allowing us to skip the calculations for ϵ 1 θ and ϵ 2 θ at this step. In this stride, only ϵ 3 θ (•, t), ϵ previously broadcast hidden states, enabling their parallel processing. Both ϵ 3 θ (•, t) and ϵ 3 θ (•, t -1) share the same feature from ϵ 2 θ (•, t + 1), so the stride should be kept small to maintain quality. Stride denoising effectively reduces both computational load and communication demands by decreasing the parallel computing rounds needed to complete the process. Compared to the significant improvements it brings in efficiency, the quality sacrifice is minimal and can be entirely compensated for by slightly increasing the warm-up steps. We also illustrate the full schematic of it in Appendix Figure 7 .", "cite_spans": [], "ref_spans": [{"start": 335, "end": 336, "text": "4", "ref_id": "FIGREF1"}, {"start": 1456, "end": 1457, "text": "7", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "t-1 t-1", "sec_num": null}, {"text": "Multi-Device Communication. Parallel inference of the model necessitates efficient communication between devices, as each component ϵ n θ must access the cached hidden state from the preceding component ϵ n-1 θ , which resides on a different device. Post each parallel computation batch, each device stores the current hidden state needed for the next parallel batch. These states, encompassing all component outputs, are then broadcast to all participating devices before the next parallel computation batch. Although each component ϵ n θ primarily uses the cached output of ϵ n-1 θ for its input, it may require residual features [10] from other components. Therefore, it's crucial to broadcast the stored states from every component across all devices before each round of parallel computation.", "cite_spans": [{"start": 632, "end": 636, "text": "[10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "t-1 t-1", "sec_num": null}, {"text": "Base models. We validated the broad applicability of AsyncDiff through extensive testing on several diffusion models. For text-to-image tasks, we experimented with three versions of Stable Diffusion: SD 1.5, SD 2.1 [43] , and Stable Diffusion XL (SDXL) [41] . Additionally, we explored the effectiveness of AsyncDiff on video diffusion models using Stable Video Diffusion (SVD) [2] and AnimateDiff [9] . All models were evaluated using 50 DDIM steps. We facilitated communication across multiple GPUs using the broadcast operation from torch.distributed, powered by the NVIDIA Collective Communication Library (NCCL) backend.", "cite_spans": [{"start": 215, "end": 219, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 253, "end": 257, "text": "[41]", "ref_id": "BIBREF40"}, {"start": 378, "end": 381, "text": "[2]", "ref_id": "BIBREF1"}, {"start": 398, "end": 401, "text": "[9]", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "Implementation Details", "sec_num": "4.1"}, {"text": "Table 1 : Quantitative evaluations of AsyncDiff on three text-to-image diffusion models, showcasing various configurations. 'N' indicates the number of components into which the model is divided, and 'S' represents the denoising stride. MACs quantifies the computational load per device for generating a single image throughout the denoising process. Dataset and Evaluation Metrics. We assess the zero-shot generation capability using the MS-COCO 2017 [29] validation set, which comprises 5,000 images and captions. For image generation, quality is measured by the CLIP Score (on ViT-g/14) [11] and Fréchet Inception Distance (FID) [12] , with LPIPS [75] used to check consistency with original outputs. In video generation, quality is evaluated by averaging the CLIP Score across all frames of a video. We also report MACs per device and latency to gauge efficiency comprehensively. All latency measurements were conducted on NVIDIA A5000 GPUs equipped with NVLINK Bridge.", "cite_spans": [{"start": 452, "end": 456, "text": "[29]", "ref_id": "BIBREF28"}, {"start": 590, "end": 594, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 632, "end": 636, "text": "[12]", "ref_id": "BIBREF11"}], "ref_spans": [{"start": 6, "end": 7, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Implementation Details", "sec_num": "4.1"}, {"text": "Improvements on Base Models. Table 1 displays our acceleration outcomes for three fundamental image diffusion models under various configurations. In this context, 'N' represents the number of segments into which the denoising model is divided, and 'S' denotes the stride of denoising for each parallel computation batch. Our approach, AsyncDiff, not only significantly accelerates processing but also minimally impacts generative quality. The speedup ratio is almost proportional to the number of devices used, demonstrating efficient resource utilization. Visualization results in Figure 5 (a) illustrate the high generative quality achieved even with substantially reduced latency. Although achieving pixel-level consistency with the original output is challenging at high acceleration ratios, the generated image still effectively conveys the semantic information in the prompt, which is crucial for generative results.", "cite_spans": [], "ref_spans": [{"start": 35, "end": 36, "text": "1", "ref_id": null}, {"start": 590, "end": 591, "text": "5", "ref_id": null}], "eq_spans": [], "section": "Experimental Results on Image Diffusion Models", "sec_num": "4.2"}, {"text": "Pixel-level Consistency by Warm-up. In Table 2 , we explore the balance between pixel-level consistency and processing speed by adjusting the warm-up steps in the diffusion models. As the initial steps of these models play a crucial role in reconstructing the global structure based on text prompts [76] , a modest increase in warm-up steps can significantly enhance consistency with the Comparison with Acceleration Baselines. We evaluated our AsyncDiff method on SD 2.1 against two other parallel acceleration methods: Faster Diffusion [25] and Distrifusion [24] . Faster Diffusion employs encoder propagation but compromises significantly on generative quality. As its parallelism maintains theoretical and lacks a multi-device implementation, we cannot measure its realistic latency with more than one GPU. Its ideal speed-up on 2 devices is about 1.9x. Distrifusion, on the other hand, uses patch parallelism for distributed acceleration but faces potential issues with low resource utilization and high GPU memory demands.", "cite_spans": [{"start": 299, "end": 303, "text": "[76]", "ref_id": "BIBREF75"}, {"start": 538, "end": 542, "text": "[25]", "ref_id": "BIBREF24"}, {"start": 560, "end": 564, "text": "[24]", "ref_id": "BIBREF23"}], "ref_spans": [{"start": 45, "end": 46, "text": "2", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Experimental Results on Image Diffusion Models", "sec_num": "4.2"}, {"text": "According to Table 3 , our method achieves the same operational speed using only 4 GPUs and 3 GPUs as Distrifusion does with 8 GPUs and 4 GPUs, respectively. Additionally, our method requires almost the same amount of memory as the original setup, whereas Distrifusion significantly increases memory requirements, posing extra challenges for practical applications. In terms of generative quality, AsyncDiff and Distrifusion both mirror the original diffusion model's performance at a 1.6x acceleration ratio. However, at higher speedup ratios of 2.3x and 2.7x, our method demonstrates significantly superior generative quality. Qualitative comparisons in Fig 6 further show that AsyncDiff maintains better pixel-level consistency with the original input compared to Distrifusion.", "cite_spans": [], "ref_spans": [{"start": 19, "end": 20, "text": "3", "ref_id": "TABREF3"}, {"start": 662, "end": 669, "text": "further", "ref_id": null}], "eq_spans": [], "section": "Experimental Results on Image Diffusion Models", "sec_num": "4.2"}, {"text": "As presented in of our method. Video generation, often constrained by exceptionally high latency and substantial computation load, greatly benefits from our approach. For a 50-step video diffusion model, AsyncDiff significantly reduces latency-by tens or even hundreds of seconds-while preserving the quality of generated content. Qualitative results shown in the Appendix. D further corroborate the effectiveness of our method. AsyncDiff achieves an impressive acceleration ratio of over three times while still producing videos that closely match the prompt descriptions, ensuring the rationality of actions and details. These findings highlight the substantial potential of AsyncDiff in accelerating the inference process of video diffusion models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Results on Video Diffusion Models", "sec_num": "4.3"}, {"text": "We introduce stride denoising to further enhance the efficiency of the asynchronous denoising process. Stride denoising completes multiple steps simultaneously through a single parallel computation, reducing the number of parallel rounds and communication frequency across devices. For a diffusion process with T steps and warm-up step W , the number of broadcasts decreases from T -W to (T -W )//2 with a stride of 2. This strategy also reduces the computational load on each device by skipping unnecessary calculations. Table 5 shows the effects of stride denoising in our parallel framework with 3 and 4 devices. Stride denoising significantly lowers overall latency and the proportion of communication time, especially as the number of devices used increases. While stride denoising slightly impacts generation quality, this effect is minimal and can be mitigated by a modest increase in warm-up steps, preserving efficiency and maintaining quality.", "cite_spans": [], "ref_spans": [{"start": 528, "end": 529, "text": "5", "ref_id": "TABREF6"}], "eq_spans": [], "section": "Effect of Stride Denoising", "sec_num": "4.4"}, {"text": "With the recent rise of advanced sampling algorithms for diffusion models, a key concern is whether the acceleration method can adapt to various samplers. AsyncDiff is a universal method that can be combined with different samplers, such as the DDIM sampler [55] and DPM-Solver [31] . In Table 7 , we present the quantitative evaluation of AsyncDiff on SD 2.1 using the DDIM sampler.", "cite_spans": [{"start": 258, "end": 262, "text": "[55]", "ref_id": "BIBREF54"}, {"start": 278, "end": 282, "text": "[31]", "ref_id": "BIBREF30"}], "ref_spans": [{"start": 294, "end": 295, "text": "7", "ref_id": "TABREF8"}], "eq_spans": [], "section": "Compatibility with Various Samplers", "sec_num": "4.5"}, {"text": "Compared to using fewer DDIM steps, our method achieves significantly better generation quality at similar speeds, with the improvement becoming more pronounced as speedup increases. Table 6 presents the quantitative evaluation of AsyncDiff on SD 2.1 with the DPM-Solver sampler. At the same speedup ratio, AsyncDiff significantly enhances generation quality compared to the baseline. Qualitative results are also provided in the Appendix figures, demonstrating that our method achieves considerable acceleration while maintaining high consistency with the original output. ", "cite_spans": [], "ref_spans": [{"start": 189, "end": 190, "text": "6", "ref_id": "TABREF7"}], "eq_spans": [], "section": "Compatibility with Various Samplers", "sec_num": "4.5"}, {"text": "As a hardware-friendly and versatile method, our acceleration technique delivers strong performance on a wide range of GPUs. We tested inference speeds on the professional-grade NVIDIA RTX A5000, as well as the consumer-grade NVIDIA RTX 2080 Ti and NVIDIA RTX 3090 GPUs. As shown in Table 8 , our method achieved a high acceleration ratio across all three GPUs. Furthermore, our method can be applied as long as the devices have basic communication capabilities.", "cite_spans": [], "ref_spans": [{"start": 289, "end": 290, "text": "8", "ref_id": "TABREF9"}], "eq_spans": [], "section": "Efficiency Analysis on Different Devices", "sec_num": "5"}, {"text": "In this paper, we propose a new parallel paradigm, AsyncDiff, to accelerate diffusion models by leveraging model parallelism across multiple devices. We split the denoising model into several components, each assigned to a different device. We transform the conventional sequential denoising into an asynchronous process by exploiting the high similarity of hidden states between consecutive time steps, enabling each component to compute in parallel. Our method has been comprehensively validated on three image diffusion models (SD 2.1, SD 1.5, SDXL) and two video diffusion models (SVD, AnimateDiff). Extensive experiments demonstrate that our approach significantly accelerates inference with only a marginal impact on generative quality. This work investigates the practical application of model parallelism in diffusion models, establishing a new baseline for future research in distributed diffusion models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "❈ In this document, we provide materials that extend beyond the scope of the main manuscript, constrained by space limitations. Model Segmentation. In our method, we partition the cumbersome denoising model into multiple components, each assigned to a different device. After successfully parallelizing the computation of each component, the time cost for each time step now corresponds to the maximum latency among these components. To optimize parallel processing efficiency, we partition the model into segments that each carry a roughly equal computational load. This arrangement allows all modules to finish their computations nearly simultaneously, making full use of available computational resources. The segmentation strategy is sequential except for SDXL [41] . For the denoising U-net within the SDXL module, we group its first and last blocks into a single segment and apply sequential splitting to the remaining blocks. This is because SDXL has specific needs for high-frequency details, and res connections typically contain abundant high-frequency information. Time Shifting. We introduce a technique called time shifting. Following the warm-up steps, the time embedding for each step is shifted back by one step. For instance, in a 50-step asynchronous denoising process with a warm-up of 2 steps, the original sequence of time embeddings is {50, 49, 48, 47, ..., 3, 2, 1}. With time shifting, this sequence is adjusted to {50, 49, 49, 48, ..., 3, 2}. In certain extreme cases, asynchronous denoising might leave residual noise in the output. Time shifting addresses this by adjusting the time embeddings backward, enhancing the denoising effect. It's important to note that time shifting is not a standard component of our method but is employed optionally. The quantitative results presented in this paper are achieved without the use of time shifting.", "cite_spans": [{"start": 765, "end": 769, "text": "[41]", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Stride Denoising. To further enhance efficiency, we introduce stride denoising, which completes multiple denoising steps simultaneously through a single parallel computation. Figure 7 illustrates the full schematic of applying stride denoising to AsyncDiff. In this depiction, the denoising model ϵ θ is divided into three components ϵ n θ and ϵ 2 θ at these steps. Stride denoising effectively reduces both computational load and communication demands by decreasing the parallel computing rounds needed to complete the process. Compared to the significant improvements it brings in efficiency, the quality sacrifice is minimal and can be entirely compensated for by slightly increasing the warm-up steps.", "cite_spans": [], "ref_spans": [{"start": 182, "end": 183, "text": "7", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Conclusion", "sec_num": "6"}, {"text": "Time cost. In Table 9 , we present the time costs associated with model running and inter-device communication when using AsyncDiff on SD 2.1. Generally, communication expenses constitute only a minor fraction of the total time cost, demonstrating that AsyncDiff is an effective distributed acceleration technique suitable for practical application. It is important to note that as the number of devices increases, the time needed for data broadcasting between devices also rises, thereby increasing the proportion of communication costs. However, employing stride denoising can substantially reduce these costs by decreasing the number of parallel rounds needed to complete the denoising process. Speedup Ratio. We also evaluate the acceleration ratio on SD 2.1 with varying numbers of denoising steps. As indicated in Table 10 , AsyncDiff significantly enhances processing speed, even with a denoising procedure consisting of only 25 steps. When the number of steps extends to 100, our approach achieves a speedup of up to 4.3x, surpassing the ratio of devices employed. C More Quantitative Results.", "cite_spans": [], "ref_spans": [{"start": 20, "end": 21, "text": "9", "ref_id": "TABREF10"}, {"start": 826, "end": 828, "text": "10", "ref_id": "TABREF11"}], "eq_spans": [], "section": "B More Analysis.", "sec_num": null}, {"text": "To thoroughly assess the quality of images produced following acceleration, we provide quantitative analyses on three base models (SD 2.1 [43] , SD 1.5 [43] , SDXL [41] ) using four additional metrics: the full reference metric, DISTS [5] , and no-reference metrics including MUSIQ [20] , CLIP-IQA [59] , and NIQE [36] . The experimental results in Table 11 demonstrate that our method significantly reduces inference latency while maintaining a high level of quality in diffusion model-generated images. On SD 1.5, our approach not only accelerates the inference process but also brings the image quality closer to the natural distribution.", "cite_spans": [{"start": 138, "end": 142, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 152, "end": 156, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 164, "end": 168, "text": "[41]", "ref_id": "BIBREF40"}, {"start": 235, "end": 238, "text": "[5]", "ref_id": "BIBREF4"}, {"start": 282, "end": 286, "text": "[20]", "ref_id": "BIBREF19"}, {"start": 298, "end": 302, "text": "[59]", "ref_id": "BIBREF58"}, {"start": 314, "end": 318, "text": "[36]", "ref_id": "BIBREF35"}], "ref_spans": [{"start": 355, "end": 357, "text": "11", "ref_id": null}], "eq_spans": [], "section": "B More Analysis.", "sec_num": null}, {"text": "Table 11 : Quantitative evaluations of AsyncDiff three text-to-image diffusion models using more metrics including DISTS [5] , MUSIQ [20] , CLIP-IQA [59] , and NIQE [36] . ", "cite_spans": [{"start": 121, "end": 124, "text": "[5]", "ref_id": "BIBREF4"}, {"start": 133, "end": 137, "text": "[20]", "ref_id": "BIBREF19"}, {"start": 149, "end": 153, "text": "[59]", "ref_id": "BIBREF58"}, {"start": 165, "end": 169, "text": "[36]", "ref_id": "BIBREF35"}], "ref_spans": [{"start": 6, "end": 8, "text": "11", "ref_id": null}], "eq_spans": [], "section": "B More Analysis.", "sec_num": null}, {"text": "Qualitative Results on Image Diffusion Models. As depicted in Figure 8 , we present further qualitative results for SD 2.1 and SDXL under various configurations. The speedup achieved is nearly proportional to the number of devices utilized, indicating efficient resource usage by our method. Moreover, the images generated by our approach closely match the text descriptions and are of high quality.", "cite_spans": [], "ref_spans": [{"start": 69, "end": 70, "text": "8", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "D More Qualitative Results", "sec_num": null}, {"text": "Qualitative Results on Video Diffusion Models. We present qualitative evaluations of AsyncDiff applied to the video diffusion models. Figures 9, 10, and 11 illustrate the generated results using our method on the text-to-video model AnimateDiff [9] . Figure 12 displays results from applying our method to the image-to-video model SVD [2] . For a 50-step video diffusion model, AsyncDiff markedly decreases latency-saving tens or even hundreds of seconds-while maintaining the integrity and quality of the generated videos.", "cite_spans": [{"start": 245, "end": 248, "text": "[9]", "ref_id": "BIBREF8"}, {"start": 335, "end": 338, "text": "[2]", "ref_id": "BIBREF1"}], "ref_spans": [{"start": 258, "end": 260, "text": "12", "ref_id": null}], "eq_spans": [], "section": "D More Qualitative Results", "sec_num": null}, {"text": "As a distributed acceleration framework, AsyncDiff necessitates frequent communication between devices throughout the denoising process. Consequently, if the devices lack the capability to communicate effectively or have subpar communication infrastructure, our method may not perform optimally. Additionally, AsyncDiff operates as a plug-and-play acceleration solution that depends on pre-trained diffusion models. Therefore, if the baseline quality of the original diffusion models is unsatisfactory, achieving high-quality results with our method could be challenging.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E Limitations", "sec_num": null}, {"text": "In this paper, we introduce a universal distributed acceleration approach for diffusion models. This method substantially speeds up the inference phase of diverse diffusion models by fully leveraging computational resources. It holds significant potential for practical applications, particularly in computationally intensive generation tasks like video and speech generation. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Societal impacts", "sec_num": null}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Societal impacts", "sec_num": null}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Societal impacts", "sec_num": null}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F Societal impacts", "sec_num": null}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Answer: [NA]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Justification: The paper does not include theoretical results", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that the paper does not include theoretical results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Answer: [Yes] Justification: We provide a detailed description of our method along with extensive experimental results.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• The answer NA means the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material? Answer: [Yes] Justification: We offer the full code along with relevant instructions. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "• The answer NA means that paper does not include experiments requiring code. • The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)? Answer: [NA] Justification: The paper poses no such risks. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [Yes] Justification: The creators or original owners of assets (e.g., code, data, models) used in the paper are properly credited, and the license and terms of use are explicitly mentioned and properly adhered to.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}, {"text": "Answer: [Yes] Justification: New assets introduced in the paper are well documented, and the documentation is provided alongside the assets.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}, {"text": "• The answer NA means that the paper does not release new assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}, {"text": "• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • At submission time, remember to anonymize your assets (if applicable). You can either create an anonymized URL or include an anonymized zip file.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}, {"text": "Question: For crowdsourcing experiments and research with human subjects, does the paper include the full text of instructions given to participants and screenshots, if applicable, as well as details about compensation (if any)?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Crowdsourcing and Research with Human Subjects", "sec_num": "14."}, {"text": "Answer: [NA] Justification: The paper not involve crowdsourcing nor research with human subjects. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Crowdsourcing and Research with Human Subjects", "sec_num": "14."}, {"text": "• The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Including this information in the supplemental material is fine, but if the main contribution of the paper involves human subjects, then as much detail as possible should be included in the main paper. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Crowdsourcing and Research with Human Subjects", "sec_num": "14."}], "back_matter": [{"text": "This project is supported by the Ministry of Education, Singapore, under its Academic Research Fund Tier 2 (Award Number: MOE-T2EP20122-0006).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgement", "sec_num": null}, {"text": "The checklist designed to encourage best practices for responsible machine learning research, addressing issues of reproducibility, transparency, research ethics, and societal impact. Do not remove the checklist: The papers not including the checklist will be desk rejected. The checklist should follow the references and follow the (optional) supplemental material. The checklist does NOT count towards the page limit.Please read the checklist guidelines carefully for information on how to answer these questions. For each question in the checklist:• You should answer [Yes] , [No] , or [NA] .• [NA] means either that the question is Not Applicable for that particular paper or the relevant information is Not Available.• Please provide a short (1-2 sentence) justification right after your answer (even for NA).The checklist answers are an integral part of your paper submission. They are visible to the reviewers, area chairs, senior area chairs, and ethics reviewers. You will be asked to also include it (after eventual revisions) with the final version of your paper, and its final version will be published with the paper.The reviewers of your paper will be asked to use the checklist as one of the factors in their evaluation.While \"[Yes] \" is generally preferable to \"[No] \", it is perfectly acceptable to answer \"[No] \" provided a proper justification is given (e.g., \"error bars are not reported because it would be too computationally expensive\" or \"we were unable to find the license for the dataset we used\"). In general, answering \"[No] \" or \"[NA] \" is not grounds for rejection. While the questions are phrased in a binary way, we acknowledge that the true answer is often more nuanced, so please just use your best judgment and write a justification to elaborate. All supporting evidence can appear either in the main paper or the supplemental material, provided in appendix. If you answer [Yes] to a question, in the justification please point to the section(s) where related material for the question can be found.IMPORTANT, please:• Delete this instruction block, but keep the section heading \"NeurIPS paper checklist\",• Keep the checklist subsection headings, questions/answers and guidelines below.• Do not modify the questions and only use the provided macros for your answers.", "cite_spans": [{"start": 579, "end": 583, "text": "[No]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "NeurIPS Paper Checklist", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?Answer: [Yes]Justification: The main claims made in our abstract and introduction accurately reflect the paper's contributions and scope.Guidelines:• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?Answer: [Yes] ", "cite_spans": [{"start": 94, "end": 99, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Analytic-dpm: an analytic estimate of the optimal reverse variance in diffusion probabilistic models", "authors": [{"first": "Fan", "middle": [], "last": "Bao", "suffix": ""}, {"first": "Chongxuan", "middle": [], "last": "Li", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2201.06503"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Analytic-dpm: an analytic estimate of the optimal reverse variance in diffusion probabilistic models. arXiv preprint arXiv:2201.06503, 2022.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Stable video diffusion: Scaling latent video diffusion models to large datasets", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Yam", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zion", "middle": [], "last": "English", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Voleti", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Letts", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.15127"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Stable video diffusion: Scaling latent video diffusion models to large datasets. arXiv preprint arXiv:2311.15127, 2023.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Metaisp: Efficient raw-to-srgb mappings with merely 1m parameters", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuan", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bi", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Proceedings of the Thirty-Third International Joint Conference on Artificial Intelligence, IJCAI-24", "volume": "8", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Metaisp: Efficient raw-to-srgb mappings with merely 1m parameters. <PERSON> <PERSON>, editor, Proceedings of the Thirty-Third International Joint Conference on Artificial Intelligence, IJCAI-24, pages 686-694. International Joint Conferences on Artificial Intelligence Organization, 8 2024. Main Track.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Style injection in diffusion: A training-free approach for adapting large-scale diffusion models for style transfer", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>ae<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>o", "suffix": ""}], "year": 2024, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "8795--8805", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Style injection in diffusion: A training-free approach for adapting large-scale diffusion models for style transfer. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 8795-8805, 2024.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Image quality assessment: Unifying structure and texture similarity", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["P"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "IEEE transactions on pattern analysis and machine intelligence", "volume": "44", "issue": "5", "pages": "2567--2581", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Image quality assessment: Unifying structure and texture similarity. IEEE transactions on pattern analysis and machine intelligence, 44(5):2567-2581, 2020.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Depgraph: Towards any structural pruning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bi", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "16091--16101", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Depgraph: Towards any structural pruning. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 16091-16101, 2023.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Structural pruning for diffusion models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in neural information processing systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Structural pruning for diffusion models. Advances in neural information processing systems, 36, 2024.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Shadowdiffusion: When degradation prior meets diffusion model for shadow removal", "authors": [{"first": "Lanqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "14049--14058", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Shadowdiffusion: When degradation prior meets diffusion model for shadow removal. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 14049-14058, 2023.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Animatediff: Animate your personalized text-to-image diffusion models without specific tuning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ceyuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Dahua", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Dai", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.04725"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Animated- iff: Animate your personalized text-to-image diffusion models without specific tuning. arXiv preprint arXiv:2307.04725, 2023.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Deep residual learning for image recognition", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "Xiangyu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shaoqing", "middle": [], "last": "Ren", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}], "year": 2016, "venue": "Proceedings of the IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "770--778", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Deep residual learning for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 770-778, 2016.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Clipscore: A reference-free evaluation metric for image captioning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ari", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Forbes", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Le Bras", "suffix": ""}, {"first": "Yejin", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2104.08718"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Clipscore: A reference-free evaluation metric for image captioning. arXiv preprint arXiv:2104.08718, 2021.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Gans trained by a two time-scale update rule converge to a local nash equilibrium", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sepp", "middle": [], "last": "<PERSON><PERSON>reiter", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Gans trained by a two time-scale update rule converge to a local nash equilibrium. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Denoising diffusion probabilistic models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "6840--6851", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Denoising diffusion probabilistic models. Advances in neural information processing systems, 33:6840-6851, 2020.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Make-an-audio: Text-to-audio generation with prompt-enhanced diffusion models", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jiaw<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ren", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ye", "suffix": ""}, {"first": "Jinglin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "13916--13932", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Make-an-audio: Text-to-audio generation with prompt-enhanced diffusion models. In International Conference on Machine Learning, pages 13916-13932. PMLR, 2023.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Gpipe: Efficient training of giant neural networks using pipeline parallelism", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Bapna", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Yonghui", "middle": [], "last": "Le", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. Gpipe: Efficient training of giant neural networks using pipeline parallelism. Advances in neural information processing systems, 32, 2019.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Beyond data and model parallelism for deep neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Zaharia", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Aiken", "suffix": ""}], "year": 2019, "venue": "Proceedings of Machine Learning and Systems", "volume": "1", "issue": "", "pages": "1--13", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Beyond data and model parallelism for deep neural networks. Proceedings of Machine Learning and Systems, 1:1-13, 2019.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Learning graph neural networks for image style transfer", "authors": [{"first": "Yongcheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dacheng", "middle": [], "last": "Tao", "suffix": ""}], "year": 2022, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Learning graph neural networks for image style transfer. In ECCV, 2022.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Holodiffusion: Training a 3d diffusion model using 2d images", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Novotny", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "18423--18433", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Holodiffusion: Training a 3d diffusion model using 2d images. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 18423-18433, 2023.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Imagic: Text-based real image editing with diffusion models", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Zada", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>v", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Inbar", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Irani", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "6007--6017", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Imagic: Text-based real image editing with diffusion models. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 6007-6017, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Musiq: Multi-scale image quality transformer", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yilin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Milanfar", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF international conference on computer vision", "volume": "", "issue": "", "pages": "5148--5157", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Musiq: Multi-scale image quality transformer. In Proceedings of the IEEE/CVF international conference on computer vision, pages 5148-5157, 2021.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Text2video-zero: Text-to-image diffusion models are zero-shot video generators", "authors": [{"first": "Levon", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Andranik", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Zhangyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Shi", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "15954--15964", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Text2video-zero: Text-to-image diffusion models are zero-shot video generators. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 15954-15964, 2023.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Diffwave: A versatile diffusion model for audio synthesis", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kong", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Kexin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Cat<PERSON>ro", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2009.09761"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Diffwave: A versatile diffusion model for audio synthesis. arXiv preprint arXiv:2009.09761, 2020.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Image-to-image translation with brownian bridge diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Kaitao", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Bin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Bbdm", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern Recognition", "volume": "", "issue": "", "pages": "1952--1961", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Bbdm: Image-to-image translation with brownian bridge diffusion models. In Proceedings of the IEEE/CVF conference on computer vision and pattern Recognition, pages 1952-1961, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Distrifusion: Distributed parallel inference for high-resolution diffusion models", "authors": [{"first": "Muyang", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Cai", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qinsheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Han", "middle": [], "last": "Cai", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yangqing", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Song", "middle": [], "last": "Han", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2402.19481"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Distrifusion: Distributed parallel inference for high-resolution diffusion models. arXiv preprint arXiv:2402.19481, 2024.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Faster diffusion: Rethinking the role of unet encoder in diffusion models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Tai<PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Shahbaz Khan", "suffix": ""}, {"first": "Lin<PERSON>uan", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ming-Ming", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.09608"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Faster diffusion: Rethinking the role of unet encoder in diffusion models. arXiv preprint arXiv:2312.09608, 2023.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Diffusion models for image restoration and enhancement-a comprehensive survey", "authors": [{"first": "Xin", "middle": [], "last": "Li", "suffix": ""}, {"first": "Yulin", "middle": [], "last": "Ren", "suffix": ""}, {"first": "Xin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Lan", "suffix": ""}, {"first": "Xing<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.09388"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Diffusion models for image restoration and enhancement-a comprehensive survey. arXiv preprint arXiv:2308.09388, 2023.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Snapfusion: Text-to-image diffusion model on mobile devices within two seconds", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Yun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yanzhi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ren", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Snapfusion: Text-to-image diffusion model on mobile devices within two seconds. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "{AlpaServe}: Statistical multiplexing with model parallelism for deep learning serving", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "17th USENIX Symposium on Operating Systems Design and Implementation", "volume": "23", "issue": "", "pages": "663--679", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. {AlpaServe}: Statistical multiplexing with model parallelism for deep learning serving. In 17th USENIX Symposium on Operating Systems Design and Implementation (OSDI 23), pages 663-679, 2023.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Microsoft coco: Common objects in context", "authors": [{"first": "Tsung-Yi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Belongie", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Perona", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2014, "venue": "Computer Vision-ECCV 2014: 13th European Conference", "volume": "", "issue": "", "pages": "740--755", "other_ids": {}, "num": null, "urls": [], "raw_text": "Tsung<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> <PERSON>. Microsoft coco: Common objects in context. In Computer Vision-ECCV 2014: 13th European Conference, Zurich, Switzerland, September 6-12, 2014, Proceedings, Part V 13, pages 740-755. Springer, 2014.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Pseudo numerical methods for diffusion models on manifolds", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ren", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2202.09778"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Pseudo numerical methods for diffusion models on manifolds. arXiv preprint arXiv:2202.09778, 2022.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Dpm-solver: A fast ode solver for diffusion probabilistic model sampling in around 10 steps", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Fan", "middle": [], "last": "Bao", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chongxuan", "middle": [], "last": "Li", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "5775--5787", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Dpm-solver: A fast ode solver for diffusion probabilistic model sampling in around 10 steps. Advances in Neural Information Processing Systems, 35:5775-5787, 2022.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Latent consistency models: Synthesizing high-resolution images with few-step inference", "authors": [{"first": "Simian", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Hang", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.04378"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Latent consistency models: Synthesizing high-resolution images with few-step inference. arXiv preprint arXiv:2310.04378, 2023.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Accelerating diffusion models via early stop of the diffusion process", "authors": [{"first": "Zhaoyang", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xudong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ceyuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dahua", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Dai", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2205.12524"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Accelerating diffusion models via early stop of the diffusion process. arXiv preprint arXiv:2205.12524, 2022.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Learning-to-cache: Accelerating diffusion transformer via layer caching", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bi", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2406.01733"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Learning-to-cache: Accelerating diffusion transformer via layer caching. arXiv preprint arXiv:2406.01733, 2024.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Accelerating diffusion models for free", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Deepcache", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.00858"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Deepcache: Accelerating diffusion models for free. arXiv preprint arXiv:2312.00858, 2023.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Making a \"completely blind\" image quality analyzer", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Mittal", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "Bovik", "suffix": ""}], "year": 2012, "venue": "IEEE Signal processing letters", "volume": "20", "issue": "3", "pages": "209--212", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Making a \"completely blind\" image quality analyzer. IEEE Signal processing letters, 20(3):209-212, 2012.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Diffrf: Rendering-guided 3d radiance field diffusion", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Sid<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Rota"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Kontschieder", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "4328--4338", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Diffrf: Rendering-guided 3d radiance field diffusion. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 4328-4338, 2023.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Pipedream: generalized pipeline parallelism for dnn training", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Harlap", "suffix": ""}, {"first": "Amar", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON>er", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Zaharia", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 27th ACM symposium on operating systems principles", "volume": "", "issue": "", "pages": "1--15", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Pipedream: generalized pipeline parallelism for dnn training. In Proceedings of the 27th ACM symposium on operating systems principles, pages 1-15, 2019.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Efficient large-scale language model training on gpu clusters using megatron-lm", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Legresley", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Cat<PERSON>ro", "suffix": ""}], "year": 2021, "venue": "Proceedings of the International Conference for High Performance Computing, Networking, Storage and Analysis", "volume": "", "issue": "", "pages": "1--15", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Efficient large-scale language model training on gpu clusters using megatron-lm. In Proceedings of the International Conference for High Performance Computing, Networking, Storage and Analysis, pages 1-15, 2021.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Restoring vision in adverse weather conditions with patch-based denoising diffusion models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Özdenizci", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Legenstein", "suffix": ""}], "year": 2023, "venue": "IEEE Transactions on Pattern Analysis and Machine Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Restoring vision in adverse weather conditions with patch-based denoising diffusion models. IEEE Transactions on Pattern Analysis and Machine Intelligence, 2023.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Sdxl: Improving latent diffusion models for high-resolution image synthesis", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Zion", "middle": [], "last": "English", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.01952"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Sdxl: Improving latent diffusion models for high-resolution image synthesis. arXiv preprint arXiv:2307.01952, 2023.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Dreamfusion: Text-to-3d using 2d diffusion", "authors": [{"first": "<PERSON>", "middle": [], "last": "Poole", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Mildenhall", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2209.14988"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Dreamfusion: Text-to-3d using 2d diffusion. arXiv preprint arXiv:2209.14988, 2022.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "High-resolution image synthesis with latent diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ommer", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "10684--10695", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. High-resolution image synthesis with latent diffusion models. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 10684-10695, 2022.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Mm-diffusion: Learning multi-modal diffusion models for joint audio and video generation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>uan", "suffix": ""}, {"first": "Yiyang", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Huigu<PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "Bei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Jing <PERSON>", "suffix": ""}, {"first": "Qin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Baining", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "10219--10228", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Mm-diffusion: Learning multi-modal diffusion models for joint audio and video generation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 10219-10228, 2023.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Dreambooth: Fine tuning text-to-image diffusion models for subject-driven generation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuanzhen", "middle": [], "last": "Li", "suffix": ""}, {"first": "Varun", "middle": [], "last": "Jampani", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "22500--22510", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Dream- booth: Fine tuning text-to-image diffusion models for subject-driven generation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 22500-22510, 2023.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Photorealistic text-toimage diffusion models with deep language understanding", "authors": [{"first": "<PERSON>t<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON><PERSON><PERSON>"], "last": "Lopes", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in neural information processing systems", "volume": "35", "issue": "", "pages": "36479--36494", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Photorealistic text-to- image diffusion models with deep language understanding. Advances in neural information processing systems, 35:36479-36494, 2022.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Image super-resolution via iterative refinement", "authors": [{"first": "<PERSON>t<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Fleet", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "IEEE transactions on pattern analysis and machine intelligence", "volume": "45", "issue": "4", "pages": "4713--4726", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Image super-resolution via iterative refinement. IEEE transactions on pattern analysis and machine intelligence, 45(4):4713-4726, 2022.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Progressive distillation for fast sampling of diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2202.00512"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Progressive distillation for fast sampling of diffusion models. arXiv preprint arXiv:2202.00512, 2022.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Unit-ddpm: Unpaired image translation with denoising diffusion probabilistic models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["P"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2104.05358"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Unit-ddpm: Unpaired image translation with denoising diffusion probabilistic models. arXiv preprint arXiv:2104.05358, 2021.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Dragdiffusion: Harnessing diffusion models for interactive point-based image editing", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Pan", "suffix": ""}, {"first": "Wenqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Song", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.14435"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Dragdiffusion: Harnessing diffusion models for interactive point-based image editing. arXiv preprint arXiv:2306.14435, 2023.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Parallel sampling of diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Belkhale", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Dorsa", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Parallel sampling of diffusion models. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Frdiff: Feature reuse for exquisite zero-shot acceleration of diffusion models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "So", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Park", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.03517"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Frdiff: Feature reuse for exquisite zero-shot acceleration of diffusion models. arXiv preprint arXiv:2312.03517, 2023.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Deep unsupervised learning using nonequilibrium thermodynamics", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Surya", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "2256--2265", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Deep unsupervised learning using nonequilibrium thermodynamics. In International conference on machine learning, pages 2256-2265. PMLR, 2015.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Denoising diffusion implicit models", "authors": [{"first": "Jiaming", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.02502"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Denoising diffusion implicit models. arXiv preprint arXiv:2010.02502, 2020.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Dual diffusion implicit bridges for image-toimage translation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Su", "suffix": ""}, {"first": "Jiaming", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2203.08382"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Dual diffusion implicit bridges for image-to- image translation. arXiv preprint arXiv:2203.08382, 2022.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Litefocus: Accelerated diffusion inference for long audio synthesis", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2407.10468"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Litefocus: Accelerated diffusion inference for long audio synthesis. arXiv preprint arXiv:2407.10468, 2024.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Video-infinity: Distributed long video generation", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xingyi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Songhua", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2406.16260"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Video-infinity: Distributed long video generation. arXiv preprint arXiv:2406.16260, 2024.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Exploring clip for assessing the look and feel of images", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Change"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the AAAI Conference on Artificial Intelligence", "volume": "37", "issue": "", "pages": "2555--2563", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Exploring clip for assessing the look and feel of images. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 37, pages 2555-2563, 2023.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Exploiting diffusion prior for real-world image super-resolution", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zongsheng", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Shangchen", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Change"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.07015"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Exploiting diffusion prior for real-world image super-resolution. arXiv preprint arXiv:2305.07015, 2023.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Modelscope text-to-video technical report", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.06571"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Modelscope text-to-video technical report. arXiv preprint arXiv:2308.06571, 2023.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Stylediffusion: Controllable disentangled style transfer via diffusion models", "authors": [{"first": "Zhizhong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Xi<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "7677--7689", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Stylediffusion: Controllable disentangled style transfer via diffusion models. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 7677-7689, 2023.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Cache me if you can: Accelerating diffusion models through block caching", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Bichen", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Dai", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "Artsiom", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Peizhao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "Tsai", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.03209"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Cache me if you can: Accelerating diffusion models through block caching. arXiv preprint arXiv:2312.03209, 2023.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Tune-a-video: One-shot tuning of image diffusion models for text-to-video generation", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ge", "suffix": ""}, {"first": "Xintao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Weixia<PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "7623--7633", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Tune-a-video: One-shot tuning of image diffusion models for text-to-video generation. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 7623-7633, 2023.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Gspmd: general and scalable parallelization for ml computation graphs", "authors": [{"first": "Yuanzhong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Maxim", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Dmitry", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ly", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Maggioni", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2105.04663"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, et al. Gspmd: general and scalable parallelization for ml computation graphs. arXiv preprint arXiv:2105.04663, 2021.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "Paint by example: Exemplar-based image editing with diffusion models", "authors": [{"first": "Binxin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shuyang", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>g", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "18381--18391", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Paint by example: Exemplar-based image editing with diffusion models. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 18381-18391, 2023.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "Hash3d: Training-free acceleration for 3d generation", "authors": [{"first": "Xingyi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2404.06091"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Hash3d: Training-free acceleration for 3d generation. arXiv preprint arXiv:2404.06091, 2024.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "Diffusion probabilistic model made slim", "authors": [{"first": "Xingyi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xinchao", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "22552--22562", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Diffusion probabilistic model made slim. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 22552- 22562, 2023.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "One-step diffusion with distribution matching distillation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "G<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Park", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.18828"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. One-step diffusion with distribution matching distillation. arXiv preprint arXiv:2311.18828, 2023.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "Scaling up to excellence: Practicing model scaling for photo-realistic image restoration in the wild", "authors": [{"first": "Fanghua", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jinjin", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "Xiangtao", "middle": [], "last": "Kong", "suffix": ""}, {"first": "Xintao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2401.13627"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Scaling up to excellence: Practicing model scaling for photo-realistic image restoration in the wild. arXiv preprint arXiv:2401.13627, 2024.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "Resshift: Efficient diffusion model for image super-resolution by residual shifting", "authors": [{"first": "Zongsheng", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Change"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Resshift: Efficient diffusion model for image super-resolution by residual shifting. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "Text-to-image diffusion model in generative ai: A survey", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "In", "middle": [], "last": "<PERSON> <PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.07909"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Text-to-image diffusion model in generative ai: A survey. arXiv preprint arXiv:2303.07909, 2023.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "Laptop-diff: Layer pruning and normalized distillation for compressing diffusion models", "authors": [{"first": "<PERSON>g<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qingsong", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2404.11098"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Laptop-diff: Layer pruning and normalized distillation for compressing diffusion models. arXiv preprint arXiv:2404.11098, 2024.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "Fast sampling of diffusion models with exponential integrator", "authors": [{"first": "Qinsheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2204.13902"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>. Fast sampling of diffusion models with exponential integrator. arXiv preprint arXiv:2204.13902, 2022.", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "The unreasonable effectiveness of deep features as a perceptual metric", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Isola", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the IEEE conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "586--595", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. The unreasonable effectiveness of deep features as a perceptual metric. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 586-595, 2018.", "links": null}, "BIBREF75": {"ref_id": "b75", "title": "Cross-attention makes inference cumbersome in text-to-image diffusion models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>g", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2404.02747"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Cross-attention makes inference cumbersome in text-to-image diffusion models. arXiv preprint arXiv:2404.02747, 2024.", "links": null}, "BIBREF76": {"ref_id": "b76", "title": "Sine: Single image editing with text-to-image diffusion models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ligong", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ghosh", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["N"], "last": "Metaxas", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ren", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "6027--6037", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. <PERSON>: Single image editing with text-to-image diffusion models. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 6027-6037, 2023.", "links": null}, "BIBREF77": {"ref_id": "b77", "title": "Uni-controlnet: All-in-one control to text-to-image diffusion models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dongdong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Bao", "suffix": ""}, {"first": "Shaozhe", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "Kwan-<PERSON><PERSON> K", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> <PERSON><PERSON>. Uni-controlnet: All-in-one control to text-to-image diffusion models. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF78": {"ref_id": "b78", "title": "Real-time video generation with pyramid attention broadcast", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "You", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2408.12588"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Real-time video generation with pyramid attention broadcast. arXiv preprint arXiv:2408.12588, 2024.", "links": null}, "BIBREF79": {"ref_id": "b79", "title": "Mobilediffusion: Subsecond text-to-image generation on mobile devices", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Tingbo", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.16567"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Mobilediffusion: Subsecond text-to-image generation on mobile devices. arXiv preprint arXiv:2311.16567, 2023.", "links": null}, "BIBREF80": {"ref_id": "b80", "title": "Dpm-solver-v3: Improved diffusion ode solver with empirical model statistics", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Dpm-solver-v3: Improved diffusion ode solver with empirical model statistics. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}}, "ref_entries": {"FIGREF0": {"text": "Figure 3: Overview of the asynchronous denoising process. The denoising model ϵ θ is divided into four components {ϵ n θ } 4 n=1 for clarity. Following the warm-up stage, each component's input is prepared in advance, breaking the dependency chain and facilitating parallel processing.", "type_str": "figure", "num": null, "fig_num": "3", "uris": null}, "FIGREF1": {"text": "Figure 4: Illustration of stride denoising. The model ϵ θ is divided into three components {ϵ n θ } 3 n=1 , with a stride S of 2 for clarity. Components ϵ 1 θ and ϵ 2 θ are skipped at time step t. A single parallel batch results in the completion of denoising for two steps, producing x t-1 and x t-2 .", "type_str": "figure", "num": null, "fig_num": "4", "uris": null}, "FIGREF2": {"text": "Figure 5: Qualitative Results. (a) Our method significantly accelerates the denoising process with minimal impact on generative quality. (b) Increasing warm-up steps achieves pixel-level consistency with the original output while maintaining a high speed-up ratio.", "type_str": "figure", "num": null, "fig_num": "35", "uris": null}, "FIGREF3": {"text": "Figure 6: Qualitative Comparison with Distrifusion on SD2.1. At the same acceleration ratio, AsyncDiff outperforms in generating higher quality and more consistent images with the original.", "type_str": "figure", "num": null, "fig_num": "6", "uris": null}, "FIGREF4": {"text": "Figure 7: Schematic of the asynchronous diffusion model with stride denoising. The model ϵ θ is divided into three components {ϵ n θ } 3 n=1 , with a stride S of 2 for clarity. A single parallel batch results in the completion of denoising for two steps", "type_str": "figure", "num": null, "fig_num": "7", "uris": null}, "FIGREF5": {"text": ", and for clarity, the stride S is set to 2. Unlike the continuous broadcasting of hidden states at each time step, stride denoising broadcasts them every two steps. As depicted, at time step {T -1, T -3, T -5, T -7}, we conduct denoising alone, and at time step {T -2, T -4, T -6, T -8}, we compute and broadcast the hidden states for the next parallel computation round. Consequently, the hidden states from time step {T -1, T -3, T -5, T -7} not required, allowing us to skip the calculations for ϵ 1", "type_str": "figure", "num": null, "fig_num": "3", "uris": null}, "FIGREF6": {"text": "Figure 8: Qualitative results on SD 2.1 and SDXL with different configurations.Our method maintains excellent generation quality even when achieving speedups of up to four times.", "type_str": "figure", "num": null, "fig_num": "8", "uris": null}, "FIGREF7": {"text": "fireworks on the town, Van <PERSON> style, digital artwork, illustrative, painterly, matte painting, highly detailed, cinematic", "type_str": "figure", "num": null, "fig_num": null, "uris": null}, "FIGREF8": {"text": "Figure 9: Qualitative results on AnimateDiff (1)", "type_str": "figure", "num": null, "fig_num": "9101112", "uris": null}, "FIGREF9": {"text": "For example (a) If the contribution is primarily a new algorithm, the paper should make it clear how to reproduce that algorithm. (b) If the contribution is primarily a new model architecture, the paper should describe the architecture clearly and fully. (c) If the contribution is a new model (e.g., a large language model), then there should either be a way to access this model for reproducing the results or a way to reproduce the model (e.g.", "type_str": "figure", "num": null, "fig_num": null, "uris": null}, "TABREF0": {"text": "", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>GPU 0(� � 1 )</td><td>GPU 1(� � 2 )</td><td>GPU 2(� � 3 )</td></tr><tr><td>Communication within device</td><td>Communication across devices</td><td>t: Time embedding</td></tr></table>"}, "TABREF2": {"text": "Quantitative evaluations of the effect of increasing warm-up steps. More warm-up steps can achieve pixel-level consistency with the original output while slightly reducing processing speed.", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>Base Model</td><td colspan=\"2\">Configuration</td><td colspan=\"2\">Devices MACs↓</td><td colspan=\"2\">latency↓</td><td colspan=\"2\">Speed up↑</td><td colspan=\"2\">CLIP Score↑</td><td>FID↓</td><td>LPIPS↓</td></tr><tr><td/><td colspan=\"2\">Original Model</td><td>1</td><td>76T</td><td>5.51s</td><td/><td/><td>1.0x</td><td/><td>31.60</td><td>27.89</td><td>-</td></tr><tr><td/><td colspan=\"2\">+ Ours (N=2 S=1)</td><td>2</td><td>38T</td><td>3.03s</td><td/><td/><td>1.8x</td><td/><td>31.59</td><td>27.79</td><td>0.2121</td></tr><tr><td>SD 2.1</td><td colspan=\"2\">+ Ours (N=3 S=1)</td><td>3</td><td>25T</td><td>2.41s</td><td/><td/><td>2.3x</td><td/><td>31.56</td><td>28.00</td><td>0.2755</td></tr><tr><td>(Text-to-Image)</td><td colspan=\"2\">+ Ours (N=4 S=1)</td><td>4</td><td>19T</td><td>2.10s</td><td/><td/><td>2.6x</td><td/><td>31.40</td><td>28.28</td><td>0.3132</td></tr><tr><td/><td colspan=\"2\">+ Ours (N=2 S=2)</td><td>3</td><td>19T</td><td>1.82s</td><td/><td/><td>3.0x</td><td/><td>31.43</td><td>28.55</td><td>0.3458</td></tr><tr><td/><td colspan=\"2\">+ Ours (N=3 S=2)</td><td>4</td><td>13T</td><td>1.35s</td><td/><td/><td>4.0x</td><td/><td>31.22</td><td>29.41</td><td>0.3778</td></tr><tr><td/><td colspan=\"2\">Original Model</td><td>1</td><td>34T</td><td>2.70s</td><td/><td/><td>1.0x</td><td/><td>30.63</td><td>29.96</td><td>-</td></tr><tr><td/><td colspan=\"2\">+ Ours (N=2 S=1)</td><td>2</td><td>17T</td><td>1.52s</td><td/><td/><td>1.8x</td><td/><td>30.62</td><td>29.94</td><td>0.1988</td></tr><tr><td>SD 1.5</td><td colspan=\"2\">+ Ours (N=3 S=1)</td><td>3</td><td>11T</td><td>1.23s</td><td/><td/><td>2.2x</td><td/><td>30.58</td><td>29.87</td><td>0.2645</td></tr><tr><td>(Text-to-Image)</td><td colspan=\"2\">+ Ours (N=4 S=1)</td><td>4</td><td>9T</td><td>1.01</td><td/><td/><td>2.6x</td><td/><td>30.52</td><td>30.10</td><td>0.3073</td></tr><tr><td/><td colspan=\"2\">+ Ours (N=2 S=2)</td><td>3</td><td>9T</td><td>0.94s</td><td/><td/><td>2.9x</td><td/><td>30.46</td><td>30.98</td><td>0.3232</td></tr><tr><td/><td colspan=\"2\">+ Ours (N=3 S=2)</td><td>4</td><td>6T</td><td>0.72s</td><td/><td/><td>3.7x</td><td/><td>30.17</td><td>30.89</td><td>0.3811</td></tr><tr><td/><td colspan=\"2\">Original Model</td><td>1</td><td>299T</td><td>13.81s</td><td/><td/><td>1.0x</td><td/><td>32.33</td><td>27.43</td><td>-</td></tr><tr><td/><td colspan=\"2\">+ Ours (N=2 S=1)</td><td>2</td><td>150T</td><td>8.00s</td><td/><td/><td>1.7x</td><td/><td>32.21</td><td>27.79</td><td>0.2509</td></tr><tr><td>SDXL</td><td colspan=\"2\">+ Ours (N=3 S=1)</td><td>3</td><td>100T</td><td>5.84s</td><td/><td/><td>2.4x</td><td/><td>32.05</td><td>28.03</td><td>0.2940</td></tr><tr><td>(Text-to-Image)</td><td colspan=\"2\">+ Ours (N=4 S=1)</td><td>4</td><td>75T</td><td>5.12s</td><td/><td/><td>2.7x</td><td/><td>31.90</td><td>29.12</td><td>0.3157</td></tr><tr><td/><td colspan=\"2\">+ Ours (N=2 S=2)</td><td>3</td><td>75T</td><td>4.91s</td><td/><td/><td>2.8x</td><td/><td>31.70</td><td>28.99</td><td>0.3209</td></tr><tr><td/><td colspan=\"2\">+ Ours (N=3 S=2)</td><td>4</td><td>49T</td><td>3.65s</td><td/><td/><td>3.8x</td><td/><td>31.40</td><td>30.27</td><td>0.3556</td></tr><tr><td>Configuration</td><td/><td>SD 2.1</td><td/><td/><td/><td colspan=\"2\">SD 1.5</td><td/><td/><td>SDXL</td></tr><tr><td/><td>Speedup↑</td><td>CLIP↑</td><td>LPIPS↓</td><td colspan=\"2\">Speedup↑</td><td colspan=\"2\">CLIP↑</td><td colspan=\"2\">LPIPS↓</td><td>Speedup↑</td><td>CLIP↑</td><td>LPIPS↓</td></tr><tr><td>Original Model</td><td>1.0x</td><td>31.60</td><td>-</td><td colspan=\"2\">1.0x</td><td colspan=\"2\">30.63</td><td>-</td><td/><td>1.0x</td><td>32.33</td><td>-</td></tr><tr><td>Warm-up = 3</td><td>3.5x</td><td>31.26</td><td>0.3289</td><td colspan=\"2\">3.3x</td><td colspan=\"2\">30.16</td><td>0.3676</td><td/><td>3.8x</td><td>31.40</td><td>0.3556</td></tr><tr><td>Warm-up = 5</td><td>3.1x</td><td>31.27</td><td>0.2769</td><td colspan=\"2\">3.0x</td><td colspan=\"2\">30.14</td><td>0.3304</td><td/><td>3.4x</td><td>31.60</td><td>0.2993</td></tr><tr><td>Warm-up = 7</td><td>2.9x</td><td>31.32</td><td>0.2309</td><td colspan=\"2\">2.7x</td><td colspan=\"2\">30.10</td><td>0.2839</td><td/><td>3.0x</td><td>31.77</td><td>0.2521</td></tr><tr><td>Warm-up = 9</td><td>2.7x</td><td>31.40</td><td>0.1940</td><td colspan=\"2\">2.5x</td><td colspan=\"2\">30.17</td><td>0.2354</td><td/><td>2.8x</td><td>31.92</td><td>0.2095</td></tr><tr><td>Warm-up = 11</td><td>2.4x</td><td>31.45</td><td>0.1628</td><td colspan=\"2\">2.4x</td><td colspan=\"2\">30.22</td><td>0.1927</td><td/><td>2.5x</td><td>32.01</td><td>0.1740</td></tr></table>"}, "TABREF3": {"text": "Quantitative comparison with other parallel acceleration methods. To ensure a fair comparison with Distrifusion, we increased the warm-up steps in our method to match the speedup ratio of Distrifusion, allowing us to fairly compare generation quality and resource costs.", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>Method</td><td>Speed up↑</td><td>Devices</td><td>MACs↓</td><td colspan=\"2\">Memory↓</td><td colspan=\"2\">CLIP Score↑</td><td>FID↓</td><td>LPIPS↓</td></tr><tr><td>Original Model</td><td>1.0x</td><td>1</td><td>76T</td><td colspan=\"2\">5240MB</td><td>31.60</td><td/><td>27.87</td><td>-</td></tr><tr><td>Faster Diffusion</td><td>1.6x</td><td>1</td><td>57T</td><td colspan=\"2\">9692MB</td><td>30.84</td><td/><td>29.95</td><td>0.3477</td></tr><tr><td>Distrifusion</td><td>1.6x</td><td>2</td><td>38T</td><td colspan=\"2\">6538MB</td><td>31.59</td><td/><td>27.89</td><td>0.0178</td></tr><tr><td>Ours (N=2 S=1)</td><td>1.6x</td><td>2</td><td>44T</td><td colspan=\"2\">5450MB</td><td>31.59</td><td/><td>27.79</td><td>0.0944</td></tr><tr><td>Distrifusion</td><td>2.3x</td><td>4</td><td>19T</td><td colspan=\"2\">7086MB</td><td>31.43</td><td/><td>27.97</td><td>0.2710</td></tr><tr><td>Ours (N=2 S=2)</td><td>2.3x</td><td>3</td><td>20T</td><td colspan=\"2\">5516MB</td><td>31.49</td><td/><td>27.71</td><td>0.2117</td></tr><tr><td>Distrifusion</td><td>2.7x</td><td>8</td><td>10T</td><td colspan=\"2\">7280MB</td><td>31.31</td><td/><td>28.12</td><td>0.2934</td></tr><tr><td>Ours (N=3 S=2)</td><td>2.7x</td><td>4</td><td>14T</td><td colspan=\"2\">5580MB</td><td>31.40</td><td/><td>28.03</td><td>0.1940</td></tr><tr><td>Oiginal</td><td>Ours 1.6x Speedup</td><td>Distrifusion 1.6x Speedup</td><td colspan=\"2\">Ours 2.3x Speedup</td><td colspan=\"2\">Distrifusion 2.3x Speedup</td><td colspan=\"2\">Ours 2.7x Speedup</td><td>Distrifusion 2.7x Speedup</td></tr><tr><td>1 Device</td><td>2 Devices</td><td>2 Devices</td><td>3 Devices</td><td/><td/><td>4 Devices</td><td/><td>4 Devices</td><td>8 Devices</td></tr></table>"}, "TABREF4": {"text": "", "type_str": "table", "num": null, "html": null, "content": "<table/>"}, "TABREF5": {"text": "Quantitative evaluations of AsyncDiff on text-to-video and image-to-video diffusion models. We present the results with various configurations.", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>Base Model</td><td>Configuration</td><td colspan=\"2\">Devices MACs↓</td><td>latency↓</td><td>Speed up↑</td><td>CLIP Score↑</td></tr><tr><td/><td>Original Model</td><td>1</td><td>786T</td><td>43.5s</td><td>1.0x</td><td>30.65</td></tr><tr><td>AnimateDiff</td><td>+ Ours (N=2 S=1)</td><td>2</td><td>393T</td><td>24.5s</td><td>1.8x</td><td>30.65</td></tr><tr><td>(Text-to-Video)</td><td>+ Ours (N=3 S=1)</td><td>3</td><td>262T</td><td>19.1s</td><td>2.3x</td><td>30.54</td></tr><tr><td/><td>+ Ours (N=2 S=2)</td><td>3</td><td>197T</td><td>14.2s</td><td>3.0x</td><td>30.32</td></tr><tr><td/><td>+ Ours (N=3 S=2)</td><td>4</td><td>131T</td><td>11.5s</td><td>3.8x</td><td>30.20</td></tr><tr><td/><td>Original Model</td><td>1</td><td>3221T</td><td>184s</td><td>1.0x</td><td>26.88</td></tr><tr><td>SVD (Image-to-Video)</td><td>+ Ours (N=2 S=1) + Ours (N=3 S=1) + Ours (N=4 S=1)</td><td>2 3 4</td><td>1611T 1074T 805T</td><td>101s 80s 68s</td><td>1.8x 2.3x 2.7x</td><td>26.66 26.56 26.19</td></tr></table>"}, "TABREF6": {"text": "Effect of stride denoising on SD 2.1. Stride denoising significantly lowers overall latency and the communication cost while only slightly compromising the generative quality", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>Configuration</td><td colspan=\"3\">MACs↓ Latency↓ Speedup↑</td><td colspan=\"2\">Communication</td><td>CLIP Score↑</td></tr><tr><td/><td/><td/><td/><td>Nums↓</td><td>Latency↓</td><td/></tr><tr><td>AsyncDiff (3 devices) w/o stride denoising</td><td>25T</td><td>2.41s</td><td>2.3x Faster</td><td>49 times</td><td>0.23s(9.5%)</td><td>31.56</td></tr><tr><td>AsyncDiff (3 devices) w/ stride denoising</td><td>19T</td><td>1.82s</td><td>3.0x Faster</td><td>25 times</td><td>0.12s(6.6%)</td><td>31.43</td></tr><tr><td>AsyncDiff (4 devices) w/o stride denoising</td><td>19T</td><td>2.10s</td><td>2.6x Faster</td><td>49 times</td><td>0.40s(19.0%)</td><td>31.40</td></tr><tr><td>AsyncDiff (4 devices) w/ stride denoising</td><td>13T</td><td>1.35s</td><td>4.0x Faster</td><td>25 times</td><td>0.10s(7.4%)</td><td>31.22</td></tr></table>"}, "TABREF7": {"text": "Quantitative evaluations of AsyncDiff using DPM-Solver sampler on SD 2.1", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>Method</td><td>Speed up ↑</td><td>MACs ↓</td><td>CLIP Score ↑</td><td>FID ↓</td></tr><tr><td>DPM-Solver 25steps</td><td>1.0x</td><td>76T</td><td>31.57</td><td>28.37</td></tr><tr><td>DPM-Solver 15steps</td><td>1.6x</td><td>46T</td><td>31.52</td><td>28.89</td></tr><tr><td>Ours (N=2 S=1)</td><td>1.6x</td><td>38T</td><td>31.58</td><td>27.71</td></tr><tr><td>DPM-Solver 10steps</td><td>2.2x</td><td>30T</td><td>31.29</td><td>29.28</td></tr><tr><td>Ours (N=3 S=1)</td><td>2.2x</td><td>25T</td><td>31.36</td><td>28.20</td></tr></table>"}, "TABREF8": {"text": "Quantitative evaluations of AsyncDiff using DDIM sampler on SD 2.1", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>Method</td><td>Speed up ↑</td><td>MACs ↓</td><td>CLIP Score ↑</td><td>FID ↓</td></tr><tr><td>Original</td><td>1.0x</td><td>76T</td><td>31.60</td><td>27.89</td></tr><tr><td>DDIM 27steps</td><td>1.8x</td><td>41T</td><td>31.53</td><td>28.43</td></tr><tr><td>Our AsyncDiff (N=2 S=1)</td><td>1.8x</td><td>38T</td><td>31.59</td><td>27.79</td></tr><tr><td>DDIM 21steps</td><td>2.3x</td><td>32T</td><td>31.46</td><td>29.09</td></tr><tr><td>Our AsyncDiff (N=3 S=1)</td><td>2.3x</td><td>25T</td><td>31.56</td><td>28.00</td></tr><tr><td>DDIM 15steps</td><td>3.0x</td><td>23T</td><td>31.26</td><td>30.12</td></tr><tr><td>Our AsyncDiff (N=2 S=2)</td><td>3.0x</td><td>19T</td><td>31.43</td><td>28.55</td></tr><tr><td>DDIM 11steps</td><td>4.0x</td><td>17T</td><td>30.99</td><td>32.25</td></tr><tr><td>Our AsyncDiff (N=3 S=2)</td><td>4.0x</td><td>13T</td><td>31.22</td><td>29.41</td></tr></table>"}, "TABREF9": {"text": "Acceleration Ratio and Latency on Different GPUs", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>GPU</td><td>FP16 Compute</td><td>Original</td><td>N=2 S=1</td><td>N=3 S=1</td><td>N=2 S=2</td><td>N=3 S=2</td></tr><tr><td>NVIDIA RTX A5000</td><td>117 TFLOPS</td><td>1.0x(5.51s)</td><td>1.8x(3.03s)</td><td>2.3x(2.41s)</td><td>3.0x(1.82s)</td><td>4.0x(1.35s)</td></tr><tr><td>NVIDIA RTX 3090</td><td>71 TFLOPS</td><td>1.0x(5.61s)</td><td>1.8x(3.20s)</td><td>2.1x(2.65s)</td><td>2.9x(1.91s)</td><td>3.5x(1.60s)</td></tr><tr><td>NVIDIA RTX 2080Ti</td><td>54 TFLOPS</td><td>1.0x(8.20s)</td><td>1.7x(4.91s)</td><td>2.0x(4.08s)</td><td>2.8x(2.94s)</td><td>3.5x(2.35s)</td></tr></table>"}, "TABREF10": {"text": "Time cost comparisons on SD 2.1. 'Ratio' in this table represents the proportion of communication cost to overall latency. All measurements were conducted on NVIDIA A5000 GPUs equipped with NVLINK Bridge", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>Config</td><td/><td colspan=\"2\">Time Cost</td><td/></tr><tr><td/><td>Overall</td><td>Running</td><td>Comm.</td><td>Ratio</td></tr><tr><td>N=2 S=1</td><td>3.03s</td><td>2.90s</td><td>0.13s</td><td>4.30%</td></tr><tr><td>N=3 S=1</td><td>2.41s</td><td>2.18s</td><td>0.23s</td><td>9.54%</td></tr><tr><td>N=4 S=1</td><td>2.10s</td><td>1.80s</td><td>0.30s</td><td>14.29%</td></tr><tr><td>N=2 S=2</td><td>1.82s</td><td>1.70s</td><td>0.12s</td><td>6.59%</td></tr><tr><td>N=3 S=2</td><td>1.35s</td><td>1.25s</td><td>0.10s</td><td>7.40%</td></tr></table>"}, "TABREF11": {"text": "Acceleration ratio on SD 2.1 under different num of denoising steps", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>Config</td><td/><td>Speedup↑</td><td/></tr><tr><td/><td>25steps</td><td>50steps</td><td>100steps</td></tr><tr><td>Origin</td><td>1.0x (2.89s)</td><td>1.0x (5.51s)</td><td>1.0x (10.96s)</td></tr><tr><td>N=2 S=1</td><td>1.7x (1.70s)</td><td>1.8x (3.03s)</td><td>1.8x (6.04s)</td></tr><tr><td>N=3 S=1</td><td>2.1x (1.35s)</td><td>2.3x (2.41s)</td><td>2.3x (4.71s)</td></tr><tr><td>N=4 S=1</td><td>2.4x (1.21s)</td><td>2.6x (2.10s)</td><td>2.7x (4.01s)</td></tr><tr><td>N=2 S=2</td><td>2.7x (1.05s)</td><td>3.0x (1.82s)</td><td>3.2x (3.39s)</td></tr><tr><td>N=3 S=2</td><td>3.4x (0.86s)</td><td>4.0x (1.35s)</td><td>4.3x (2.52s)</td></tr></table>"}, "TABREF13": {"text": "• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details.• While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).• as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.6. Experimental Setting/DetailsQuestion: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the results?Answer: [Yes] Justification: We provide all the details about the experiment in our paper. Guidelines:• The answer NA means that the paper does not include experiments.• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material. 7. Experiment Statistical Significance Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [Yes] Justification: We provide the details about initialization and dataset split. Guidelines: • The answer NA means that the paper does not include experiments. • The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. The paper should provide amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper). 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines? Answer: [Yes] Justification: We strictly adhere to the NeurIPS Code of Ethics. Guidelines: • The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics. • If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction). 10. Broader Impacts Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>8. Experiments Compute Resources</td></tr><tr><td>Question: For each experiment, does the paper provide sufficient information on the com-</td></tr><tr><td>puter resources (type of compute workers, memory, time of execution) needed to reproduce</td></tr><tr><td>the experiments?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: We provide the details about the computation resources we used in the experi-</td></tr><tr><td>ments.</td></tr><tr><td>Guidelines:</td></tr></table>"}, "TABREF14": {"text": "• According to the NeurIPS Code of Ethics, workers involved in data collection, curation, or other labor should be paid at least the minimum wage in the country of the data collector. 15. Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects Question: Does the paper describe potential risks incurred by study participants, whether such risks were disclosed to the subjects, and whether Institutional Review Board (IRB) approvals (or an equivalent approval/review based on the requirements of your country or institution) were obtained? Answer: [NA] Justification: The paper does not involve crowdsourcing nor research with human subjects. Guidelines: • The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper.", "type_str": "table", "num": null, "html": null, "content": "<table/>"}}}}