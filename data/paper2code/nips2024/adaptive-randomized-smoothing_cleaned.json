{"paper_id": "adaptive-randomized-smoothing", "title": "Adaptive Randomized Smoothing: Certified Adversarial Robustness for Multi-Step Defences", "abstract": "We propose Adaptive Randomized Smoothing (ARS) to certify the predictions of our test-time adaptive models against adversarial examples. ARS extends the analysis of randomized smoothing using f -Differential Privacy to certify the adaptive composition of multiple steps. For the first time, our theory covers the sound adaptive composition of general and high-dimensional functions of noisy inputs. We instantiate ARS on deep image classification to certify predictions against adversarial examples of bounded L ∞ norm. In the L ∞ threat model, ARS enables flexible adaptation through high-dimensional inputdependent masking. We design adaptivity benchmarks, based on CIFAR-10 and CelebA, and show that ARS improves standard test accuracy by 1 to 15% points. On ImageNet, ARS improves certified test accuracy by up to 1.6% points over standard RS without adaptivity. Our code is available at https: //github.com/ubc-systopia/adaptive-randomized-smoothing.", "pdf_parse": {"paper_id": "adaptive-randomized-smoothing", "abstract": [{"text": "We propose Adaptive Randomized Smoothing (ARS) to certify the predictions of our test-time adaptive models against adversarial examples. ARS extends the analysis of randomized smoothing using f -Differential Privacy to certify the adaptive composition of multiple steps. For the first time, our theory covers the sound adaptive composition of general and high-dimensional functions of noisy inputs. We instantiate ARS on deep image classification to certify predictions against adversarial examples of bounded L ∞ norm. In the L ∞ threat model, ARS enables flexible adaptation through high-dimensional inputdependent masking. We design adaptivity benchmarks, based on CIFAR-10 and CelebA, and show that ARS improves standard test accuracy by 1 to 15% points. On ImageNet, ARS improves certified test accuracy by up to 1.6% points over standard RS without adaptivity. Our code is available at https: //github.com/ubc-systopia/adaptive-randomized-smoothing.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Despite impressive accuracy, deep learning models still show a worrying susceptibility to adversarial attacks. Such attacks have been shown for a large number of tasks and models (<PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2018) , including areas where security and safety are critical such as fraud detection (<PERSON><PERSON><PERSON><PERSON> and <PERSON>, 2018) or autonomous driving (<PERSON> et al., 2021) .", "section": "Introduction", "sec_num": "1"}, {"text": "Several rigorous defences have been proposed to certify robustness. Randomized Smoothing (RS) (<PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2019) does so by averaging predictions over noisy versions of the input at test time, and as such can scale to large deep learning models. However, RS has its limitations: it is inflexible and either degrades accuracy or only certifies against small attacks.", "section": "Introduction", "sec_num": "1"}, {"text": "To address these shortcomings and improve robustness, there has been a recent push to develop defences that adapt to inputs at test time <PERSON><PERSON><PERSON> et al. (2022) , including for RS (<PERSON><PERSON> et al., 2022a; <PERSON><PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2022) . Most such adaptive defences are heuristic, unproven, and subject to improved attacks (<PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022a; <PERSON> et al., 2022) , running the risk of reverting to a hopeless cat and mouse game with attackers (<PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2020) , or only provide limited adaptivity (<PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2022) and gain ( §5).", "section": "Introduction", "sec_num": "1"}, {"text": "We (re)connect RS to Differential Privacy (DP), after its abandonment for a tighter analysis via hypothesis testing (<PERSON> et al., 2019) , and introduce Adaptive Randomized Smoothing (ARS) to provide test-time adaptivity while preserving rigorous bounds. Specifically, we analyze RS through the lens of f -Differential Privacy (f -DP), and use this connection to leverage a key strength of DP: the end-to-end analysis of multi-step adaptive computation using composition results ( §2).", "section": "Introduction", "sec_num": "1"}, {"text": "We use ARS to design two-step defence against L ∞ adversaries on image classification (Figure 1 ), which is a challenging setting for RS Blum et al. (2020) . The first step computes an input mask that Step M2 takes masked input w(m1) ⊙ X and adds noise to get m2. Base classifier g post-processes a weighted average of m1, m2 to output a label. RS reduces to σ2 = σ and w(.) = 1 (no M1).", "section": "Introduction", "sec_num": "1"}, {"text": "focuses on task-relevant information. This reduces the dimension of the input, which is then passed to the second step for prediction. Thanks to this adaptive dimension reduction, the second step makes its prediction on a less noisy image, improving the performance and certification radius ( §3).", "section": "Introduction", "sec_num": "1"}, {"text": "We evaluate our adaptive randomized smoothing method in three settings ( §4). For image classification, we first design a challenging benchmark based on CIFAR-10, and we show that ARS can improve accuracy by up to 15%. For spatially-localized face attribute classification on the CelebA dataset, we show that ARS improves accuracy by up to 7.7%. For large-scale image classification on ImageNet, ARS maintains accuracy and improves certified accuracy by up to 1.6%.", "section": "Introduction", "sec_num": "1"}, {"text": "After introducing the necessary background and known results on RS and DP ( §2.1), we reconnect RS to its DP roots by showing that the tight analysis of <PERSON> et al. (2019) can be seen as PixelDP (<PERSON><PERSON><PERSON><PERSON> et al., 2019) using f -Differential Privacy (<PERSON> et al., 2019; 2022) , a hypothesis testing formulation of DP ( §2.2). This connection lets us leverage composition results for f -DP to analyze multi-step approaches for provable robustness to adversarial examples, which we name Adaptive Randomized Smoothing (ARS) ( §2.3). We leverage ARS to design and analyze a two-step defence against L ∞ -bounded adversaries ( §2.4), which we then instantiate as a deep network ( §3).", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "2.1 Related Work: Adversarial Robustness, Randomized Smoothing, and Differential Privacy Adversarial Examples (<PERSON><PERSON><PERSON><PERSON> et al., 2014) : Consider a classifier g : X → Y, and input X. An adversarial example of radius r in the L p threat model, for model g on input X, is an input X + e such that g(X + e) ̸ = g(X), where e ∈ B p (r), where B p (r) ≜ {x ∈ R d : ∥x∥ p ≤ r} is the L p ball of radius r. These inputs or attacks are made against classifiers at test time. For more on the active topics of attack and defence, we refer to surveys (<PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2018) on current attacks and provable defences. In general, provable defences do not focus on the largest-scale highest-accuracy classifiers, with the notable exception of randomized smoothing.", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "Randomized Smoothing (RS) (<PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2019) is a scalable approach to certifying model predictions against L 2 -norm adversaries. Specifically, it certifies robustness to any attack ∈ B 2 (r X ). The algorithm randomizes a base model g by adding spherical Gaussian noise to its input, and produces a smoothed classifier that returns the class with highest expectation over the noise: y + ≜ arg max y∈Y P z∼N (0,σ 2 I d ) g(X + z) = y . The tightest analysis from <PERSON> et al. (2019) uses hypothesis testing theory to show that, with p + , p -∈ [0, 1] such that P(g(X + z) = y + ) ≥ p + ≥ p -≥ max y-̸ =y+ P(g(X + z) = y -), the certificate size r X for prediction y + is:", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "EQUATION", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "where Φ -1 is the inverse standard Gaussian CDF, p + lower-bounds the probability of g(X + z) = y + (the most probable class), and p -upper-bounds the probability of other classes.", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "While sound, RS is static during testing even though attacks may adapt. Recent work aims to make RS adapt at test time (<PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON> et al., 2022a; <PERSON> et al., 2022) . While pioneering, these works are restricted in either their soundness or their degree of adaptation and resulting improvement. <PERSON><PERSON><PERSON><PERSON> et al. (2021) soundly adapt the variance for RS by the distance between test and train inputs. However, this only provides minimal adaptivity, with only minor improvement to certification. <PERSON><PERSON> et al. (2022a) adapt the variance for RS to each test input, but the analysis is not end-to-end, and hence not sound (<PERSON><PERSON><PERSON> et al., 2021) (except for their memory-based edition, but this requires storage of all examples). UniCR (<PERSON> et al., 2022) adapts the noise distribution for RS, primarily to the data distribution during training, and optionally to input during testing. The train-time adaptation is sound, but the test-time adaptation is not due to the same issue raised by <PERSON><PERSON><PERSON> et al. (2021) . We propose ARS to advance certified test-time adaptivity: our approach is sound and high-dimensional to flexibly adapt the computation of later steps conditioned on earlier steps by differential privacy theory.", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "Differential Privacy (DP) is a rigorous notion of privacy. A randomized mechanism M is (ϵ, δ)-DP if, for any neighbouring inputs X and X ′ , and any subset of possible outputs Y ⊂ Range(M), <PERSON><PERSON><PERSON> et al. (2019) , we define neighbouring based on L p norms: X and", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "P(M(X) ∈ Y) ≤ e ϵ P(M(X ′ ) ∈ Y) + δ. Following", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "X ′ in R d are L p neighbours at radius r if X -X ′ ∈ B p (r).", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "RS was initially analyzed using (ϵ, δ)-Differential Privacy (<PERSON><PERSON><PERSON><PERSON> et al., 2019) . Intuitively, the randomized classifier M(X) ≜ g(X + z), z ∼ N (0, σ 2 I d ) acts as a privacy preserving mechanism (the Gaussian mechanism) that provably \"hides\" small variations in X. This privacy guarantee yields a robustness certificate for the expected predictions.", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "f -DP (<PERSON> et al., 2019) is a notion of privacy that extends (ϵ, δ)-DP, and defines privacy as a bound on the power of hypothesis tests. Appendix A provides more details on f -DP. The main result we leverage is Theorem 2.7 of <PERSON> et al. (2022) that for a Gaussian mechanism", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "EQUATION", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "We leverage two key properties of f -DP. First, f -DP is resilient to post-processing. That is, if mechanism M is f -DP, proc • M is also f -DP. Second, f -DP is closed under adaptive composition. We refer to §3 in <PERSON> et al. (2022) for the precise definition and use their Corollary 3.3: the adaptive composition of two Gaussian mechanisms G µ1 -DP and G µ2 -DP is itself G µ -DP with", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "EQUATION", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "f -DP is distinct from the f -divergence from information theory. <PERSON><PERSON><PERSON><PERSON> et al. ( 2020) use fdivergence bounds between the noise distribution centred on the original input and centred on any perturbed input. This improves RS by broadening the noise distributions and norm-bounds on the adversary that RS can support. In contrast we focus on f -DP, which captures enough information to reconstruct any divergence by post-processing (Proposition B.1. in <PERSON> et al. (2019) ). Our main objective is different: we leverage f -DP composition properties to enable multi-step deep learning architectures that adapt to the input at test time with robustness guarantees.", "section": "Theory for Adaptivity via Differential Privacy", "sec_num": "2"}, {"text": "We reconnect RS with DP, using f -DP to yield results as strong as that of Equation (1). We start with a general robustness result on f -DP classifiers, which we later build on for our main result. Proposition 2.1 (f -DP Robustness). Let M : X → Y be f -DP for B p (r) neighbourhoods, and let M S : X → arg max y∈Y P(M(X) = y) be the associated smoothed classifier. Let y + ≜ M S (X) be the prediction on input X, and let p + , p -∈ [0, 1] be such that P(M(X) = y + ) ≥ p + ≥ p -≥ max y-̸ =y+ P(M(X) = y -). Then:", "section": "Randomized Smoothing from f -DP", "sec_num": "2.2"}, {"text": "f (1 -p + ) ≥ 1 -f (p -) ⇒ ∀e ∈ B p (r), M S (X + e) = y + Proof. See Appendix B1.", "section": "Randomized Smoothing from f -DP", "sec_num": "2.2"}, {"text": "Let us now instantiate Proposition 2.1 for Gaussian RS (see §2.1):", "section": "Randomized Smoothing from f -DP", "sec_num": "2.2"}, {"text": "Corollary 2.2 (RS from f -DP). Let M : X → g(X + z), z ∼ N (0, σ 2 I d ), and M S : X → arg max y∈Y P(M(X) = y) be the associated smooth model. Let y + ≜ M S (X) be the prediction on input X, and let p + , p -∈ [0, 1] be such that P(M(X) = y + ) ≥ p + ≥ p -≥ max y-̸ =y+ P(M(X) = y -). Then ∀e ∈ B 2 (r x ), M S (X + e) = y + , with:", "section": "Randomized Smoothing from f -DP", "sec_num": "2.2"}, {"text": "r X = σ 2 Φ -1 (p + ) -Φ -1 (p -) .", "section": "Randomized Smoothing from f -DP", "sec_num": "2.2"}, {"text": "Proof. See Appendix B2. Sketch: M is a Gaussian mechanism, and is G r σ -DP for any r (B 2 (r) neighbourhood). We apply Proposition 2.1 and maximize r such that G r σ (1-p + ) ≥ 1-G r σ (p -).", "section": "Randomized Smoothing from f -DP", "sec_num": "2.2"}, {"text": "While Proposition 2.1 is new, so far we have only used it to reprove the known result of Corollary 2.2. So why is this connection between f -DP and robustness useful? Our key insight is that we can leverage adaptive composition results at the core of DP algorithms to certify multi-step methods that adapt to their inputs at test time. Such adaptive defences have seen recent empirical interest, but either lack formal guarantees, or provide only limited adaptivity in practice ( §5). For the first time we derive a sound and high-dimensional adaptive method for certification.", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "We formalize adaptive multi-step certification as follows. Consider k randomized Gaussian mechanisms M 1 , . . . , M k (our adaptive steps), such that m i ∼ M i (X|m <i ), and for all r ≥ 0 we have that M i is G r/σi -DP for the B 2 (r) neighbouring definition. Note that the computation M i can depend on previous results, as long as it is G r/σi -DP. Further consider a (potentially randomized) post-processing classifier g(m 1 , . . . , m k ) = y ∈ Y.", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "Theorem 2.3 (Main result: Adaptive RS). Using definitions above, let M :", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "X → g(m 1 , . . . , m k ) ∈ Y, (m 1 , . . . , m k ) ∼ (M 1 (X), . . . , M k (X|m <k ))", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": ", and the associated smoothed model be M S : X → arg max m∈Y P(M(X) = y). Let y + ≜ M S (X) be the prediction on input X, and let p + , p -∈ [0, 1] be such that P(M(X) = y + ) ≥ p + ≥ p -≥ max y-̸ =y+ P(M(X) = y -). Then ∀e ∈ B 2 (r x ), M S (X + e) = y + , with:", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "r X = 1 2 k i=1 1 σ 2 i Φ -1 (p + ) -Φ -1 (p -) .", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "Proof. By adaptive composition of Gaussian DP mechanisms (Equation ( 3)", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "), M is G µ -DP with µ = k i=1 r 2 σ 2 i = r k i=1 1 σ 2 i", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": ". We can then apply Corollary 2.2 with σ = 1/", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "k i=1 1 σ 2 i .", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "We focus on Gaussian RS, but a similar argument applies to general f -DP mechanisms for which we can compute f i at any r, and the composition f i ⊗ • • • ⊗ f k , potentially using numerical techniques such as that of <PERSON><PERSON> et al. (2021) . For Gaussian noise, Theorem 2.3 leverages strong results from DP to provide a perhaps surprising result: there is no cost to adaptivity, in the sense that k independent measurements of input X with Gaussian noise (without adaptivity) of respective variance σ 2 i can be averaged to one measurement of variance", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "σ 2 = 1/ k i=1 σ -2 i .", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "To show this, we can use a weighted average to minimize variance (see e.g., Equation 4 in <PERSON><PERSON> (2015) ), with", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "c j = σ -2 j / k i=1 σ -2 i yielding σ 2 = k j=1 c 2 j σ 2 j = k j=1 σ -2 j / k i=1 σ -2 i 2 = 1/ k i=1 σ -2 i .", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "The ARS r X from Theorem 2.3 is identical to that of one step RS from Corollary 2.2 using this variance: adaptivity over multi-step computation comes with no decrease in certified radius.", "section": "Adaptive Randomized Smoothing", "sec_num": "2.3"}, {"text": "How can we leverage the multi-step adaptivity from Theorem 2.3 to increase certified accuracy? We focus on two-step certified defence against L ∞ -bounded attacks to increase accuracy by adaptivity. Previous work already notes that RS applies to L ∞ -bounded attackers (<PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2019) , using the fact that", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "∀X ∈ R d , ∥X∥ 2 ≤ √ d∥X∥ ∞ , and hence that X -X ′ ∈ B ∞ (r ∞ ) ⇒ X -X ′ ∈ B 2 ( √ d • r ∞ ).", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "Using Corollary 2.2, this yields:", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "EQUATION", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "While L ∞ -specific RS theory exists (<PERSON> et al., 2020) , further work by <PERSON><PERSON> et al. (2020) has found that Gaussian RS performs advantageously in practice. However, <PERSON><PERSON> et al. (2020) ; <PERSON> et al. (2020) ; <PERSON> et al. (2021) show that the √ d dependency cannot be avoided for a large family of distributions, leading the authors to speculate that RS might be inherently limited for L ∞ certification of predictions on high dimensional images. To side-step this issue, we use two-steps adaptivity to first select subsets of the image important to the classification task (thereby reducing dimension), and then make the prediction based on the selected subset. Formally: Proposition 2.4 (Adaptive RS for L ∞ ). Define the following pair of (adaptive) mechanisms:", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "M 1 : X → X + z 1 ≜ m 1 , z 1 ∼ N (0, σ 2 1 I d ) (5)", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "Then, with any function w : R d → [0, 1] d (interpreted as a mask):", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "EQUATION", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": ")", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "where ⊙ is the element-wise product; and the final prediction function g : m 1 , m 2 → Y.", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "Consider the mechanism M that samples m 1 ∼ M 1 , then m 2 ∼ M 2 , and finally outputs g(m 1 , m 2 ); and the associated smoothed classifier M S : X → arg max y∈Y P(M(X) = y). Let y + ≜ M S (X) be the prediction on input X, and let p + , p -∈ [0, 1] be such that", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "EQUATION", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "Proof. Consider any X,", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "X ′ s.t. X -X ′ ∈ B ∞ (r ∞ ). We analyze M 1 and M 2 in turn. ∥X -X ′ ∥ 2 ≤ √ d∥X -X ′ ∥ ∞ , so X -X ′ ∈ B 2 ( √ dr ∞ ), and M 1 is G µ1 -DP with µ 1 = r ∞ √ d σ1 . ∥w(m 1 ) ⊙ X -w(m 1 ) ⊙ X ′ ∥ 2 = ∥w(y 1 ) ⊙ X -X ′ ∥ 2 ≤ ∥w(y 1 )∥ 2 ∥X -X ′ ∥ ∞ so X -X ′ ∈ B 2 (∥w(y 1 )∥ 2 r ∞ ) and M 2 is G µ2 -DP with µ 2 = ∥w(y1)∥2r ∞ ∥w(y1)∥2σ2/ √ d = r ∞ √ d σ2 . Applying Theorem 2.3 with (r ∞ ) 2 d σ 2 1 + (r ∞ ) 2 d σ 2 2 = r ∞ d 1 σ 2 1 + 1 σ 2 2", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "concludes the proof.", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "Important remarks. 1. w(.) is a masking function, adaptively reducing (if w i (m 1 ) ≪ 1) the value of X i and thereby the attack surface of an L ∞ attacker. This reduces the effective dimension of the input to M 2 . 2. Reducing the dimension enables a reduction in the noise variance in M 2 , at fixed privacy guarantee G µ2 . The variance reduction is enabled for all dimensions in the input, even those that are not masked (w i (m 1 ) ≈ 1). As a result, the variance of the noise in M 2 scales as ∥w(m 1 )∥ 2 2 ≤ d. The more masking, the lower the variance. It may help to consider the change of variables σ ← σ/ √ d in Equation (4), and σ 1,2 ← σ 1,2 / √ d in Proposition 2.4, to remove d from r ∞ X and scale the noise variance with d. For RS (Equation (4)), the noise variance scales as d. For ARS, only Equation (5) (the first step) suffers from variance scaled by d, while the second step's variance (Equation ( 6)) scales as ||w(m 1 )|| 2 2 , which can be much smaller than d when a large part of the image is masked. Reduced variance translates into higher accuracy, as well as p + and p -being further apart, for a larger r ∞ X . 3. The variance reduction due to masking applies in the translation from the B ∞ (r ∞ ) bound on the attack to the B 2 (r) sensitivity used in ARS. This variance reduction would not apply to an L 2 -bounded adversary (an attack that only changes pixels with mask values of one yields no sensitivity improvement). Hence, our two-steps ARS architecture for L ∞ -bounded adversaries does not reduce to bounding L ∞ with L 2 as the traditional RS application does, and our gains come explicitly from variance reduction enabled by adaptive masking against an L ∞ attack.", "section": "ARS against L ∞ -Bounded Adversaries", "sec_num": "2.4"}, {"text": "Figure 1 shows our deep learning architecture based on Proposition 2.4. The first step, M 1 , adds noise to input X and post-processes the result into a mask w(m 1 ). The second step, M 2 , takes masked input w(m 1 ) ⊙ X and adds noise. Finally, the base classifier g post-processes a weighted average of m 1 , m 2 to output a label. The whole model is trained end-to-end on the classification task. In RS, only the path going through M 2 is present. This is equivalent to setting σ 2 = σ and w(.) = 1, with no M 1 . In both cases, the final predictions are averaged over the noise to create the smoothed classifier. The ARS architecture introduces several new components, which we next describe.", "section": "Two-Step ARS for L ∞ Certification of Image Classification", "sec_num": "3"}, {"text": "Budget Splitting: the noise budget σ (Figure 1 ; red) is split to assign noise levels to the two steps M 1 and M 2 . We parameterize ARS with the same σ as standard RS then split it by the f -DP composition formula from Equation (3). In practice, we assign σ 1 ≥ σ to M 1 , and then", "section": "Two-Step ARS for L ∞ Certification of Image Classification", "sec_num": "3"}, {"text": "σ 2 = 1/ 1 σ 2 -1 σ 2 1", "section": "Two-Step ARS for L ∞ Certification of Image Classification", "sec_num": "3"}, {"text": ". We set σ 1 by either fixing it to a constant or learning it end-to-end.", "section": "Two-Step ARS for L ∞ Certification of Image Classification", "sec_num": "3"}, {"text": "Masking: the mask model w(•) takes the noisy image from M 1 and predicts a weighting (one value in [0, 1] per input pixel) that is multiplied with the input element-wise (denoted ⊙ in Proposition 2.4).", "section": "Two-Step ARS for L ∞ Certification of Image Classification", "sec_num": "3"}, {"text": "The model is a U-Net architecture, which makes pixel-wise predictions, and acts as a post-processing of M 1 in the f -DP analysis. Our masking enables test-time adaptivity to reduce the noise variance for M 2 , via the mask's dependence on the input through m 1 .", "section": "Two-Step ARS for L ∞ Certification of Image Classification", "sec_num": "3"}, {"text": "Mechanism output averaging: to fully leverage both steps' information, we take a weighted average of the outputs m 1 and m 2 before passing the result to the base classifier g. For a particular input pixel i, denote X i the value of pixel, w i ∈ [0, 1] its mask weight (we omit the explicit dependency on m 1 in w for compactness), and m 1,i , m 2,i the respective values output by M 1 and M 2 . Then, the final value of pixel i in the averaged input will be Xi", "section": "Two-Step ARS for L ∞ Certification of Image Classification", "sec_num": "3"}, {"text": "≜ c 1,i m 1,i + c 2,i m 2,i . We set c 1,i , c 2,i such that Xi is the unbiased estimate of X i with smallest variance. First, we set c 1,i + w i c 2,i = 1, such that E[ Xi ] = c 1,i X i + c 2,i w i X i = X i . Second, we minimize the variance. Notice that V[ Xi ] = c 2 1,i σ 2 1 + c 2 2,i ∥w∥ 2 2 σ 2 2 = (1 -w i c 2,i ) 2 σ 2 1 + c 2 2,i ∥w∥ 2 2 σ 2 2 :", "section": "Two-Step ARS for L ∞ Certification of Image Classification", "sec_num": "3"}, {"text": "this is a convex function in c 2,i minimized when its gradient in c 2,i is zero. Plugging back into the constraint to get c 1,i , we obtain the following weights:", "section": "Two-Step ARS for L ∞ Certification of Image Classification", "sec_num": "3"}, {"text": "c 1,i = ∥w∥ 2 2 σ 2 2 σ 2 1 w 2 i +∥w∥ 2 2 σ 2 2 , and c 2,i = σ 2 1 wi σ 2 1 w 2 i +∥w∥ 2 2 σ 2 2 .", "section": "Two-Step ARS for L ∞ Certification of Image Classification", "sec_num": "3"}, {"text": "The averaged noisy input X is finally fed to the base classifier g for prediction. The smoothed classifier M S averages predictions (over noise draws) over the entire pipeline. The parameters of w and g (and σ 1 if not fixed) are learned during training and are fixed at inference/certification time.", "section": "Two-Step ARS for L ∞ Certification of Image Classification", "sec_num": "3"}, {"text": "We evaluate on standard and L ∞ -certified test accuracy. Certified accuracy at radius r ∞ is the percentage of test samples that are correctly classified and have an", "section": "Experiments", "sec_num": "4"}, {"text": "L ∞ certificate radius r ∞ X ≥ r ∞ . Standard accuracy is obtained for r ∞ = 0.", "section": "Experiments", "sec_num": "4"}, {"text": "Datasets We evaluate on CIFAR-10 (<PERSON><PERSON><PERSON><PERSON>, 2009) in §4.1, CelebA (<PERSON> et al., 2015) (specifically the unaligned HD-CelebA-Cropper edition) in §4.2, and ImageNet (<PERSON><PERSON> et al., 2009) in §4.3. We measure adaptivity on CIFAR-10 and CelebA by designing challenging benchmarks requiring adaptivity, and measure scalability on ImageNet.", "section": "Experiments", "sec_num": "4"}, {"text": "Models We choose the standard ResNet (<PERSON> et al., 2016) models as base classifiers g with ResNet-110 for CIFAR-10 and ResNet-50 for CelebA and ImageNet. For ARS, our mask model w is a simplified U-Net (<PERSON><PERSON> et al., 2015) (see Appendix C.1 for details). For the noise budget, we find that a fixed budget split performs reliably, and so in all experiments we split by", "section": "Experiments", "sec_num": "4"}, {"text": "σ 1 = σ 2 = √ 2σ.", "section": "Experiments", "sec_num": "4"}, {"text": "Methods We compare to standard and strong static methods, design a baseline specifically for our masking approach, and evaluate the only sound input-dependent methods prior to ARS. <PERSON> et al. is the standard approach to RS (<PERSON> et al., 2019) . UniCR (<PERSON> et al., 2022) learns the noise distribution for RS during training but is static during testing (while they propose an input-adaptive variant, it is not sound so we restrict our comparison to the training variant). We tune hyper-parameters, and perform a grid search for β (the parameter of the noise distribution) to maximize certified accuracy. We find that β = 2 (Gaussian), or β = 2.25 (close to a Gaussian, σ = 1.0, k = 48  42.5 (2.1)  45.1 (1.2)  44.3 (0.2)  34.1 (1.0)  47.6 (2.0)  σ = 0.25, k = 64  71.6 (0.9)  73.1 (3.2)  67.8 (0.5)  64.1 (0.8)  77 (1.8)  σ = 0.5, k = 64  63 (1.6)  62.5 (1.7)  58.7 (0.2)  45.1 (1.1)  69.9 (1.2)  σ = 1.0, k = 64  41.3 (1 ", "section": "Experiments", "sec_num": "4"}, {"text": "Input dimension is a key challenge in L ∞ certification using RS (see §2.4). We design our 20kBG benchmark to vary this parameter without affecting the task: we superimpose CIFAR-10 images onto a larger distractor background from the 20k background images dataset <PERSON> et al. (2022a) . The backgrounds are split into train and test sets, and resized to k × k pixels (where k ≥ 32). The CIFAR-10 image (of dimensions 32 × 32 × 3) is then placed at random along the edges of the background image to maximize spatial variation. The spurious background increases the dimension (= k × k × 3) of the input when k > 32, making L ∞ certification with RS more challenging, but is uninformative for the task of CIFAR-10 by construction. Our mask model (M 1 ) needs to learn to ignore the background to reduce the effective dimension of the input. For computational reasons, we run all certification results on a 200 sample subset of the CIFAR-10 test set. Appendix D shows extended results, details about hyperparameter tuning (C.3), and results on larger test-sets (D.1). We set the failure probability of the certification procedure to 0.05, use 100 samples to select the most probable class, and 50,000 samples for the Monte Carlo estimate of p + .", "section": "CIFAR-10 Benchmark: Classification with Distractor Backgrounds", "sec_num": "4.1"}, {"text": "Table 1 summarizes the standard accuracy (r = 0) of each approach on the different settings. We vary σ in {0.25, 0.5, 1.0} and k in {32, 48, 64, 96} to show the effect of noise levels and dimensionality on the accuracy of different approaches (k = 32 corresponds to original CIFAR-10 images). Figure 2 shows the certified test accuracy at different radii r ∞ for ARS and all baselines we consider.", "section": "CIFAR-10 Benchmark: Classification with Distractor Backgrounds", "sec_num": "4.1"}, {"text": "We make three observations. First, ARS outperforms all the baselines under distractor backgrounds (k > 32). Static mask is slightly better at k = 32, probably because CIFAR-10 images lack enough \"irrelevant\" information for ARS to discard. This is precisely why we introduce this benchmark, where we explicitly add such irrelevant information to input images. Indeed, at k > 32, we observe that the standard accuracy of ARS improves, translating to an improved certified accuracy at all certification levels, since more accurate and confident predictions give a larger certified radius r ∞ .", "section": "CIFAR-10 Benchmark: Classification with Distractor Backgrounds", "sec_num": "4.1"}, {"text": "Second, as we grow the input dimension k, the accuracy of ARS remains either stable or increases, whereas that of other baselines goes down, resulting in an increasing gap. For instance, at σ = 1.0, the gap between ARS and the best baseline is 1.3% points for k = 32, 2.5% points for k = 48, 8.2% points for k = 64 and 15% points for k = 96. As k grows, the amount of relevant information (a 32 × 32 × 3 CIFAR-10 image) remains the same, whereas the amount of spurious background information increases. ARS' mask is able to rule out spurious pixels, reducing the noise in the second step (Figure 3 ). Thanks to this masking, ARS is much less sensitive to increases in dimensionality.", "section": "CIFAR-10 Benchmark: Classification with Distractor Backgrounds", "sec_num": "4.1"}, {"text": "Third, we observe that except k = 32, ARS improves over all the baselines in low (σ = 0.25) to high (σ = 1.0) noise regimes. In fact, this trend continues to persist in higher noise regimes (Appendix D). Similar to previous observations, we notice that as we increase σ, other baselines's accuracy drops significantly whereas ARS accuracy drops much less, displaying higher noise tolerance.", "section": "CIFAR-10 Benchmark: Classification with Distractor Backgrounds", "sec_num": "4.1"}, {"text": "ARS training and inference requires additional computation. To certify a single input (k = 32), <PERSON> et al. (2019) takes ∼12 seconds while ARS takes ∼26 seconds (as measured on an NVIDIA A100 80Gb GPU). This 2× overhead does however yield improved certified accuracy.", "section": "CIFAR-10 Benchmark: Classification with Distractor Backgrounds", "sec_num": "4.1"}, {"text": "To evaluate ARS on a more realistic task with natural spatial variation, we use the CelebA face dataset in its unaligned version. We focus on the \"mouth slightly open\" (label 21) binary classification task because mouth location and shape vary. The input part relevant to this task is likely well-localized, which affords an opportunity for the mask model to reduce the effective input dimension. The dataset consists of images with varied resolution, and meta-data about the position of different features, including the mouth. To create a challenging benchmark, we randomly crop all images to 160 × 160 pixels, which creates spatial variation in the mouth's position. The only crop constraint is that the mouth is ≥ 10 pixels from the edge to ensure sufficient input to solve the task. Figure 5 shows example images from the test set, their respective masks from ARS, and the baseline static mask. Figure 4 shows the certified accuracy for ARS, <PERSON> et al. (2019) , and static mask, for three levels of the noise σ. First, both baselines perform very similarly. We can see from Figure 5 that the static mask is approximately identity (notice the ≥ 0.99 scale), with only very slight dimming on the edges. This is because the mouth is not centred in our benchmark, so there is no one-size-fits-all mask. Second, ARS is able to predict a sparse mask that focuses on areas likely to have the mouth. The mask adapts to each input at test time, which is what enables the sparsity without performance degradation. Third, this sparse mask leads to a large noise reduction, enabling ARS to drastically improve both standard and certified accuracy. For instance, with σ = 0.5, ARS improves the standard accuracy from 91.0% to 94.3% (a 3.3 point improvement), while the certified accuracy at r ∞ = 0.004 jumps from 45.0% to 79.3% (a more than 30 point improvement!). At lower noise (σ = 0.25) there is still an increase in standard accuracy from 94.3% to 97.0%, and an increase in certified accuracy from 68.3% to 84.7% at r ∞ = 0.002). At larger noise (σ = 1.0), ARS sees significant increases (7.7% points in standard accuracy, and from 21.3% to 49.3% in certified accuracy at r ∞ = 0.008).", "section": "CelebA Benchmark: Classification Without Spatial Alignment", "sec_num": "4.2"}, {"text": "To evaluate the scalability of ARS we experiment on ImageNet (without any modification) with σ = 0.25, 0.5, 1.0. For each noise level, we compare with <PERSON> et al. (2019) , which we reproduce for this large-scale setting. We evaluate two versions of ARS: our regular setting (End-To-End); and a version that fixes the base classifier to the model trained as in <PERSON> et al. (2019) , and only trains our mask model for 10 epochs (Pretrain). The certified accuracy is plotted in Figure 6 and the standard accuracy is reported in Table 3 .", "section": "ImageNet Benchmark: Classification on the Standard Large-Scale Dataset", "sec_num": "4.3"}, {"text": "When only training the mask model, certified accuracy remains close to that of <PERSON> et al. (2019) at all radii and noise levels. ARS trained end-to-end improves both standard and certified accuracy.", "section": "ImageNet Benchmark: Classification on the Standard Large-Scale Dataset", "sec_num": "4.3"}, {"text": "Setting/Approach The standard accuracy (at r = 0) increases from 57.2% to 57.4% and from 43.6% to 44.5% when σ = 0.5 and σ = 1, respectively. For σ = 0.25, standard accuracy for ARS is close but slightly lower than <PERSON> et al. (2019) , while the pretrained ARS outperforms <PERSON> et al. (2019) from 66.5% to 67.4%. Appendix F discusses other ARS improvements without certification.", "section": "ImageNet Benchmark: Classification on the Standard Large-Scale Dataset", "sec_num": "4.3"}, {"text": "Certified accuracy increases at larger σ. For instance at σ = 0.5, ARS improves certified accuracy at r ∞ = 0.001 from 48.9% to 50.5%. At larger noise σ = 1.0, ARS improves certified accuracy at r ∞ = 0.005 from 21.7% to 23.1%. This shows that ARS' adaptivity generalizes outside of the specialized benchmarks we designed, and can scale to large datasets and complex classification tasks. 2021)), or leverage DP-SGD (<PERSON><PERSON><PERSON> et al., 2016) to analyze defences that update by test-time optimization (<PERSON><PERSON> et al., 2022b; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2021) . Going further, one could leverage the vast DP literature to extend ARS, enabling fully-adaptive variance defences inspired by <PERSON><PERSON> et al. (2022a) by leveraging privacy odometers (<PERSON> et al., 2016; Lécuyer, 2021; <PERSON><PERSON> et al., 2023) .", "section": "ImageNet Benchmark: Classification on the Standard Large-Scale Dataset", "sec_num": "4.3"}, {"text": "To conclude: we introduced Adaptive Randomized Smoothing (ARS) to reconnect RS with DP theory, to propose a new two-step defence for deep image classification, and to rigorously analyze such adaptive defences that condition on inputs at test time. This framework opens promising avenues for designing models that are adaptively and soundly robust with provable guarantees about their updates on natural and adversarial inputs.", "section": "ImageNet Benchmark: Classification on the Standard Large-Scale Dataset", "sec_num": "4.3"}, {"text": "Proposition 2.1 (f -DP Robustness). Let M : X → Y be f -DP for B p (r) neighbourhoods, and let M S : X → arg max y∈Y P(M(X) = y) be the associated smooth classifier. Let y + ≜ M S (X) be the prediction on input X, and let p + , p -∈ [0, 1] be such that P(M(X) = y + ) ≥ p + ≥ p -≥ max y-̸ =y+ P(M(X) = y -). Then:", "section": "B Proofs", "sec_num": null}, {"text": "f (1 -p + ) ≥ 1 -f (p -) ⇒ ∀e ∈ B p (r), M S (X + e) = y +", "section": "B Proofs", "sec_num": null}, {"text": "Proof. Let us first consider any runner-up class y -. Calling <PERSON> the random variable for <PERSON>'s prediction, consider the rejection rule ϕ = 1{M = y -}, where 1 is the indicator function. Denoting α ≜ E M(X) (ϕ), and using the fact that M is f -DP for B p (r) neighbourhoods, we have that ∀e ∈ B p (r):", "section": "B Proofs", "sec_num": null}, {"text": "EQUATION", "section": "B Proofs", "sec_num": null}, {"text": "where the last inequality is because α", "section": "B Proofs", "sec_num": null}, {"text": "= E M(X) (ϕ) = P(M(X) = y -) ≤ p -, and f is non- increasing so f (α) ≥ f (p -) and hence 1 -f (α) ≤ 1 -f (p -).", "section": "B Proofs", "sec_num": null}, {"text": "Let us now consider the predicted class y + . Keeping the same notations, and defining the rule", "section": "B Proofs", "sec_num": null}, {"text": "EQUATION", "section": "B Proofs", "sec_num": null}, {"text": "Putting Equations ( 8) and ( 9) together, we have that", "section": "B Proofs", "sec_num": null}, {"text": "P(M(X +e) = y + ) ≥ f (1-p + ) ≥ 1-f (p -) ≥ P(M(X + e) = y -)", "section": "B Proofs", "sec_num": null}, {"text": "and thus m S (X + e) = y + .", "section": "B Proofs", "sec_num": null}, {"text": "Note that we do not have to chose a rule ϕ ∈ {0, 1}, but could instead return any number in [0, 1], such as the logits of the base classification model, yielding the following definition for the smoothed classifier M S : X → arg max y∈Y E(M(X) y ).", "section": "B Proofs", "sec_num": null}, {"text": "Proposition 2.2 (RS from f -DP). Let M : X → g(X + z), z ∼ N (0, σ 2 I d ), and M S : X → arg max y∈Y P(M(X) = y) be the associated smooth model. Let y + ≜ M S (X) be the prediction on input X, and let p + , p -∈ [0, 1] be such that P(M(X) = y + ) ≥ p + ≥ p -≥ max y-̸ =y+ P(M(X) = y -). Then ∀e ∈ B 2 (r x ), M S (X + e) = y + , with:", "section": "B Proofs", "sec_num": null}, {"text": "r X = σ 2 Φ -1 (p + ) -Φ -1 (p -) . Proof. X :→ X + z, z ∼ N (0, σ 2 ) is a Gaussian mechanism. By Equation (2), for the B r (r) neighbouring definition, it is G r σ -DP. By post-processing M is also G r σ -DP. Applying Proposition 2.1, we have that G r σ (1 -p + ) ≥ 1 -G r σ (p -) ⇒ ∀e ∈ B 2 (r), m S (X + e) = y + . Let us find r X = sup {r : G r σ (1 -p + ) ≥ 1 -G r σ (p -)}. Since G r σ (.) as a function of r is monotonously decreasing this will happen at G r X σ (1 -p + ) = 1 -G r X σ (p -), that is: Φ Φ -1 (p + ) - r X σ = 1 -Φ Φ -1 (1 -p -) - r X σ ⇒ Φ -1 (p + ) - r X σ = -Φ -1 (1 -p -) + r X σ ⇒ Φ -1 (p + ) - r X σ = Φ -1 (p -) + r X σ ⇒ r X = σ 2 Φ -1 (p + ) -Φ -1 (p -) ,", "section": "B Proofs", "sec_num": null}, {"text": "where the first implication holds because by symmetry of the standard normal 1 -Φ(x) = Φ(-x), and because Φ is strictly monotonous ; the second because similarly,", "section": "B Proofs", "sec_num": null}, {"text": "Φ -1 (1 -p) = -Φ -1 (p).", "section": "B Proofs", "sec_num": null}, {"text": "the rest of hyperparameters same as for k = 32, 48, 64. Note that for k = 96, we could not get the standard accuracy to improve upon random baseline for σ = {0.5, 0.75, 1.0, 1.5}, despite extensively tuning the learning rate.", "section": "B Proofs", "sec_num": null}, {"text": "k σ = 0.12 σ = 0.25 σ = 0.5 σ = 0.75 σ = 1.0 σ = 1. For UniCR, we tune β (the parameter of the generalized normal distribution for the noise) using σ = 0.75 and k = 48 as the testbed. We perform grid search on β and find that β = 2.0 or β = 2.25 (Gaussian and close to a Gaussian, but with a wider more and shorted tails) perform best. For each k, σ setting, we train 3 models with β = 2.25 and 2.0 and choose the β giving highest mean standard accuracy. The chosen β for each setting is given in Table 5 . For each setting we also tune optimizer hyper-parameters. At k = 32, we use SGD optimizer. We use a learning rate of 0.01, momentum of 0.9 and weight decay of 0.0005 with a step learning rate scheduler (30 step and γ = 0.1). At k = 48, 64, 96 we use training batch size 256, 100 epochs, and AdamW optimizer. We use a learning rate of 0.001 with step learning rate scheduler (30 step size and γ = 0.5). ", "section": "B Proofs", "sec_num": null}, {"text": "We show here full sweep of results for CIFAR-10 Bg20k benchmark for k = 32, 48, 64, 96 and σ = 0.12, 0.25, 0.5, 0.75, 1.0, 1.5 for all our baselines and ARS. Similar to results presented in Table 1 , we report the mean accuracy and standard deviation over three seeds. ", "section": "D Additional Results on CIFAR10 BG20k Benchmark", "sec_num": null}, {"text": "The baseline results in Table 6 are lower than those reported by <PERSON> et al. (2019) (Figure 6 ). This is a result of our certifying over a smaller subset of the test set (the released code from <PERSON> et al. ( 2019)1 uses a subset of size 500, while the paper says the certification was on the full test set). In Table 7 we report the standard accuracy (r = 0) on the same 500 samples subset of the CIFAR-10 test set as used in the code released by <PERSON> et al. (2019) . We show results for k = 32, which is the plain CIFAR-10 task, using both the hyper-parameters from <PERSON> et al. (2019) , our own optimized hyper-parameters (the most notable change is that we use AdamW), and ARS. We make three observations. First, as it turns out, our 200 samples subset used for results in Table 6 Table 7 : k = 32 standard accuracy (r = 0) on CIFAR-10 (20kBG). We report the mean accuracy and standard deviation over three seeds. We compare three approaches: <PERSON> et al. (2019) trained with the hyper-parameters they report in their GitHub repository, <PERSON> et al. (2019) trained with the hyper-parameters we report in 4, and ARS. We train models with each approach at σ = 0.12, 0.25, 0.5, 1.0. The corresponding models are certified on 200, 500, and 10,000 samples.", "section": "D.1 Impact of Certification Test-set Size", "sec_num": null}, {"text": "is more challenging, and accuracy values are systematically lower (by about 2% points) than over 500 samples and the full test set (10k samples). Using the same 500 samples subset yields accuracy values very close to those reported by <PERSON> et al. (2019) on their hyper-parameters. Second, over all samples sizes 200, 500, and 10k, our hyper-parameters significantly improve the accuracy of RS (by about 3% points consistently), confirming that we are making a fair comparison between the best RS models we could find and ARS. Third, at k = 32 ARS provides modest improvements over tuned RS, as CIFAR-10 images are well cropped and low-dimensional, providing less opportunity for dimension reduction through masking, and hence lower ARS improvements.", "section": "D.1 Impact of Certification Test-set Size", "sec_num": null}, {"text": "Figure 12 , Figure 13 , Figure 14 and Figure 15 shows figures of different stages in our ARS architecture (Figure 1 ). In all of these figures, 1 st row corresponds to input images X, 2 nd row corresponds to images right after M 1 , 3 rd row corresponds to ARS masks, 4 th row corresponds to element-wise Table 8 : Standard Accuracy (r = 0) on CelebA (unaligned and cropped). We report the mean accuracy and standard deviation over three seeds.", "section": "D.2 CIFAR-10 BG20k figures", "sec_num": null}, {"text": "We show here a full set of results for the CelebA benchmark over all σ = 0.12, 0.25, 0.5, 0.75, 1.0, 1.5 for <PERSON> et al. ( 2019), static masking, and ARS. Similarly to the results in Table 2 , we report the mean standard accuracy and standard deviation over three seeds in Table 8 . The certified accuracies are plotted in Figure 17 .", "section": "D.2 CIFAR-10 BG20k figures", "sec_num": null}, {"text": "We see that at all σ, ARS has the highest standard accuracy. An interesting observation is that on ImageNet, ARS yields larger improvements to the test accuracy without certification. We follow the certification procedure of <PERSON> et al. (2019) , which first determines the predicted class, and then certifies it if and only if the probability of this predicted class is such that p + ≥ 0.5 (that is, it groups all other classes into one non-predicted class). If this is not the case, the prediction will count as not certified at r = 0, even if the predicted class is still correct. We keep this procedure for consistency with prior work, but on a task with a large number of classes like in ImageNet, the accuracy at r = 0 can be much lower as the non-certified accuracy.", "section": "D.2 CIFAR-10 BG20k figures", "sec_num": null}, {"text": "We noticed that ARS significantly improves this non certified accuracy, while not improving the accuracy at r = 0 as much (see Section 4.3). In effect ARS leads to more correct predictions, but this (correct) predicted class has p + < 0.5, so the accuracy at r = 0 does not increase. Table 9 shows this effect by comparing the test accuracy (no certification at all) across all methods. Under ARS, the test accuracy increases from 68.5% to 70.1% when σ = 0.25, from 60.7% to 64.2% when σ = 0.5, and from 47.9%to 53.8% for σ = 1.0. In summary, ARS achieves best test accuracy (without certification) for all the noise levels. This suggests that ARS helps more than shown by the default certification approach, and that a finer analysis that accounts for the probability of all classes could yield further improvements in certified accuracy.", "section": "F Additional Results on ImageNet", "sec_num": null}, {"text": "1. Claims Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope? Answer: [Yes] Justification: The abstract and introduction identify the purpose of our paper and our theoretical, technical, and empirical contributions. We provide theoretical results in Section 2 with detailed proofs, and comprehensive experimental results in Section 4 to support our technical claims and contributions for test-time adaptive certification for robustness. Our choice of theoretical framework, task scope, and evaluation datasets are all present in the abstract.", "section": "NeurIPS Paper Checklist", "sec_num": null}, {"text": "Guidelines:", "section": "NeurIPS Paper Checklist", "sec_num": null}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "section": "NeurIPS Paper Checklist", "sec_num": null}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors? Answer: [Yes] Justification: We identify limitations in Section 5 and in particular call out computational overhead and the need to assess the combination of ARS with other improvements to RS (which are compatible in principle, but require experiment to measure and verify).", "section": "Limitations", "sec_num": "2."}, {"text": "Guidelines:", "section": "Limitations", "sec_num": "2."}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "section": "Limitations", "sec_num": "2."}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "section": "Limitations", "sec_num": "2."}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Answer: [Yes] Justification: We present the theoretical results in Section 2, where we cover the main results, and provide Appendix B with further detailed assumptions and proofs. Guidelines:", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that the paper does not include theoretical results.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Answer: [Yes] Justification: The method is described in the text, and to ensure sufficient detail, we additionally provide appendices and supplementary material that includes the anonymized code for our method. The experiments identify their settings and hyperparameters, such as noise levels and input image dimensions, and these settings can be verified by inspection of the results for our method and the baselines like <PERSON> et al. (2019) . By relying on standard base models, such as ResNet-50, our work is made more reproducible by following the conventions of existing papers. For modeling specific to our contribution, such as a the mask model, we have taken care to provide more detail in the appendix. Our adaptivity benchmarks, while our own design, are simple to implement (through padding and cropping) and our choices of how to transform the base public datasets are described in the text. In addition, we have incorporated reviewers feedback in the camera ready version. Guidelines:", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• The answer NA means that the paper does not include experiments.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. For example (a) If the contribution is primarily a new algorithm, the paper should make it clear how to reproduce that algorithm.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. Justification: We have reviewed the NeurIPS Code of Ethics and can confirm that we conform to them in every respect when we conducted research in the paper.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Guidelines:", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [NA] Justification: This paper improves the robustness of existing machine learning models to adversarial examples. Adversarial examples have been proposed for a limited number of beneficial use cases, such as censorship or surveillance evasion, but overall more robust and trustworthy ML models can benefit the increasingly broad deployment of machine learning, including in security and safety-critical applications. As a paper with a theoretical emphasis and empirical evaluation on well-established benchmarks, this work does not have additional societal impact beyond the norm.", "section": "Broader Impacts", "sec_num": "10."}, {"text": "Guidelines:", "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?", "section": "Safeguards", "sec_num": "11."}, {"text": "Answer: [NA] Justification: Our work does not have a high risk for misuse, given its theoretical portion on proofs for robustness, and its empirical portion on the robustness evaluation of standard image classifiers. As such no special safeguards are needed. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "section": "Safeguards", "sec_num": "11."}, {"text": "https://github.com/locuslab/smoothing/blob/78a4d949e4f627d000a78908e001f8ca66c92943/ experiments.MD", "section": "", "sec_num": null}], "back_matter": [{"text": "We thank <PERSON><PERSON><PERSON><PERSON> and <PERSON> for reviewing and providing feedback during the Google DeepMind publication process. We thank <PERSON><PERSON><PERSON> for discussing variations on input-dependent variance for randomized smoothing. We are grateful for the support of the Natural Sciences and Engineering Research Council of Canada (NSERC) [reference number RGPIN-2022-04469], as well as a Google Research Scholar award. This research was enabled by computational support provided by the Digital Research Alliance of Canada (alliancecan.ca).", "section": "Acknowledgments and Disclosure of Funding", "sec_num": null}, {"text": "For this background we use the DP mechanism terminology. A mechanism M(.) is a randomized computation taking an input and returning one sample from the distribution of outputs for this input: m ∼ M(x) with input x and output m. In ARS, each model step corresponds to an f -DP mechanism.Definitions. <PERSON> et al. (2019; 2022) formalize privacy as a bound on the power of hypothesis tests. Consider any two neighbouring inputs: in the most common DP applications, X, X ′ are two databases differing in one element; in the case of ARS against an L p adversary, X, X ′ ∈ R d are any two inputs such that X -X ′ ∈ B p (r). Intuitively a randomized mechanism M is private if, for any such neighbouring inputs, the distributions M(X) and M(X ′ ) are hard to distinguish. That is, by looking at a sample output from mechanism M, it is hard to guess whether M ran on X or on X ′ .In f -DP (<PERSON> et al., 2019; 2022) \"hard to distinguish\" is defined by a hypothesis testing problem: H 0 : the input was X vs.H 1 : the input was X ′ .The output m ∼ M serves as input to a rejection rule ϕ(.) ∈ [0, 1] (note: to preserve typical notations, lower-case ϕ is the rejection rule, and upper-case Φ is the standard normal CDF). The rejection rule rejects H 0 with probability ϕ(m), so ϕ(m) = 0 predicts that X was the input, and ϕ(m) = 1 that X ′ was.Given a rejection rule ϕ, we define its Type I error α ϕ and type II error (or one minus the power of the rule) β ϕ as:Intuitively, α ϕ is the expected amount of rejection of H 0 when the hypothesis is correct (X was in input, but we think X ′ was), also called the level of the rejection rule. On the flip side, β ϕ is the expected amount of non-rejection under H 1 (X ′ was in input, but we think X was). 1 -β ϕ is called the power of the rejection rule.For any two distributions M(X) and M(X ′ ), we define the trade-off function T M(X), M(X ′ ) : [0, 1] → [0, 1] that quantifies the minimum amount of type II error achievable at each value of type I error by any (measurable) rule; or equivalently the maximum power of any rule at each level:Now we define f -DP: for any trade-off function f , a mechanism M is f -DP if, for any neighbouring inputs X, X ′ , T M(X), M(X ′ ) ≥ fThese definitions are the main technical tools we need to prove Proposition 2.1. Corollary 2.2 only adds the formula for f for the Gaussian mechanism, given in Section 2.1.Composition. All other results rely on the above plus the adaptive composition of f -DP mechanisms. Such composition is key to all DP theory and algorithm design. Consider a sequence of N mechanisms M i , such that each mechanism is f i -DP with regards to X, X ′ , and depends on the neighbouring input as well as the output of all previous mechanisms. More formally, under H 0 we have m i ∼ M i (X, m <i ), and under H 1 we have m i ∼ M i (X, m <i ), where m <i ≜ (m 1 , . . . , m i-1 ).Concretely, each M i is f i -DP with regards to X, X ′ for f i known in advance, but the actual computation made by M i can depend on m <i (as long as it is f i -DP). We leverage this adaptivity to lower the noise variance in our method's second step while keeping f 2 fixed (see §3).We need two more results to define the composition of a sequence of mechanisms. First, Proposition 2.2 in Dong et al. (2019; 2022) shows that for any trade-off function f , there exist two distributionsCall any such pair of distributions a representative pair of f . Second, we define the composition operatorThat is, the composition operator between two trade-off functions is the trade-off function between the product distributions on their representative pair. Then Theorem 3.2 in Dong et al. (2019; 2022) shows that:Concretely, the mechanism that returns the sequence of results for all compute adaptive ", "section": "A f -DP Background", "sec_num": null}, {"text": "C.1 Mask ArchitectureFigure 7 shows the architecture of our Mask model w (M 1 ). We adapt a UNet architecture to preserve dimensions, and use a Sigmoid layer at the end of the model to output values between 0 and 1 for mask weights. We set up our UNet hyperparameters as : in_channels=3, out_channels=1 (to out put a mask), base_channel=32, channel_mult={1,2,4,8}. ", "section": "C Experiment Details", "sec_num": null}, {"text": "Table 4 provides the details of our ARS models' hyper-parameters. On the CelebA dataset, we tune the hyper-parameters in the ARS, <PERSON> et al. (2019) , and static mask settings at σ = 0.75. In all settings, we settle on SGD with learning rate 0.05 and a step learning rate scheduler (step size of 3 and γ = 0.8) for the base classifier. In the static mask setting, we use SGD with learning rate 0.01.", "section": "C.2 Hyperparameter tuning for CelebA", "sec_num": null}, {"text": "For the base classifier g in <PERSON> et al. (2019) , Static Mask and ARS experiments, we tune the optimizer and its hyperparameters using k = 64, σ = 1.5 as the testbed. Based on these tuning experiments, we chose <PERSON><PERSON> as the optimizer with an initial learning rate of 0.01 and weight decay to 0.0001. We scale the learning rate by 0.1 every 30 th epoch, with a batch size of 256. For the mask model w in ARS experiments, we again used the AdamW optimizer with an initial learning rate of 0.001, weight decay of 0.0001, whilst scaling the learning rate by 0.5 every 40 th epoch. We used the same hyperparameters for all k's and σ's for these setups.For <PERSON><PERSON><PERSON> et al. (2021) , for k = 32, 48, 64, we tune the hyperparameters using k = 64 as the testbed. We kept the optimizer same as used in the author's code (SGD), setting the initial learning rate to 0.01, momentum to 0.9 and weight decay to 0. We scaled the learning rate by 0.1 every 30 th epoch. We tune k = 96's parameters separately. We start with an initial learning rate 0.1, keeping , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "section": "C.3 Hyperparameter tuning for CIFAR-10 BG20k", "sec_num": null}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?Answer: [Yes] Justification: We submit our codes and necessary command in a zip file. For the camera ready version, we have open sourced our code at https://github.com/ubc-systopia/ adaptive-randomized-smoothing/tree/main. The hyperparameter settings are listed in Table 4 . The datasets we used are cited and has open access to the public. .", "section": "Open access to data and code", "sec_num": "5."}, {"text": "• The answer NA means that paper does not include experiments requiring code.• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable). • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.", "section": "Guidelines:", "sec_num": null}, {"text": "Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the results?Answer: [Yes] Justification: We detail our experimental setting at a high-level in Section 4 and then elaborate in each subsection. We list our experimental setting in Section 4 and the hyperparameters in Table 4 . For baselines, we rely on the reference hyperparameter settings from the papers for <PERSON> et al. (2019) , rely on code and hyper-parameters shared by the authors for <PERSON>ú<PERSON> et al. ( 2021) (the code is not public), and tune hyper-parameters and β for Hong et al. (2022) (details in §C).Guidelines:• The answer NA means that the paper does not include experiments.Guidelines:• The answer NA means that the paper poses no such risks.• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort. 12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected? Answer: [Yes] Justification: All papers, code, and assets, etc. have been properly cited in the paper. We make use of the the reference code provided by the cited papers by their authors. We make use of standard and existing datasets, and therefore do not collect or introduce additional assets.Guidelines:• The answer NA means that the paper does not use existing assets.• The authors should cite the original paper that produced the code package or dataset.• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators. 13. New Assets Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets? Answer: [NA] Justification: We do not collect or include new assets. To the extent that our own method code is an asset, it is the creation of the authors, and it is included anonymously and confidentially in our supplementary materials. If accepted, the code will be released under an open license such as BSD-2. Guidelines:• The answer NA means that the paper does not release new assets.• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • At submission time, remember to anonymize your assets (if applicable). You can either create an anonymized URL or include an anonymized zip file.", "section": "Experimental Setting/Details", "sec_num": "6."}], "ref_entries": {"FIGREF0": {"text": "Figure 1: Two-step ARS for L∞-bounded attacks. Step M1 adds noise to input X and post-processes the result into a mask w(m1).Step M2 takes masked input w(m1) ⊙ X and adds noise to get m2. Base classifier g post-processes a weighted average of m1, m2 to output a label. RS reduces to σ2 = σ and w(.) = 1 (no M1).", "fig_num": "1", "type_str": "figure", "uris": null, "num": null}, "FIGREF1": {"text": "Figure 2: Certified Test Accuracy on CIFAR-10 (20kBG). (a)-(c) show the effect of dimensionality for (a) no background / k = 32, (b) k = 48, and (c) k = 96 for constant σ = 0.5. (d)-(f) show the effect of noise for (d) σ = 0.25, (e) σ = 0.5 and (f) σ = 1.0 with dimensionality fixed to k = 64. Each line is the mean and the shaded interval covers +/-one standard deviation across seeds.", "fig_num": "23", "type_str": "figure", "uris": null, "num": null}, "FIGREF2": {"text": "Figure 4: Certified test accuracy on CelebA (unaligned and cropped). We evaluate static methods and ARS to measure the value of adaptivity. Each line is the mean and the shading covers ±1 standard deviation across three seeds. Adaptivity helps at all noise levels.", "fig_num": "4", "type_str": "figure", "uris": null, "num": null}, "FIGREF3": {"text": "Figure 5: ARS masks are localized and input specific.", "fig_num": "5", "type_str": "figure", "uris": null, "num": null}, "FIGREF4": {"text": "Figure 6: Certified test accuracy on ImageNet for σ = 0.25, 0.5, 1. We plot the mean and the shading covers ±1 standard deviation for three seeds. ARS is equal or better than non-adaptive RS (<PERSON> et al.) at large scale.", "fig_num": "6", "type_str": "figure", "uris": null, "num": null}, "FIGREF5": {"text": "Figure 8: k = 32 certified test accuracy results for CIFAR-10 (20kBG) (a)-(f) show the effect of increasing σ. These results are in our 20kBG setting where= a CIFAR-10 image is placed randomly along the edges of a background image. Each line is the mean and the shaded interval covers +/-one standard deviation across seeds.", "fig_num": "8", "type_str": "figure", "uris": null, "num": null}, "FIGREF6": {"text": "Figure 9: k = 48 certified test accuracy results for CIFAR-10 (20kBG) (a)-(f) show the effect of increasing σ. These results are in our 20kBG setting where a CIFAR-10 image is placed randomly along the edges of a background image. Each line is the mean and the shaded interval covers +/-one standard deviation across seeds.", "fig_num": "9", "type_str": "figure", "uris": null, "num": null}, "FIGREF7": {"text": "Figure 11: k = 96 certified test accuracy results for CIFAR-10 (20kBG) (a)-(f) show the effect of increasing σ. These results are in our 20kBG setting where a CIFAR-10 image is placed randomly along the edges of a background image. Each line is the mean and the shaded interval covers +/-one standard deviation across seeds.", "fig_num": null, "type_str": "figure", "uris": null, "num": null}, "FIGREF8": {"text": "(a) σ = 0.25 (b) σ = 1.0 Figure 12: Figures at different stages in our ARS architecture for CIFAR-10 k = 32 input images. Check (a) σ = 0.25 (b) σ = 1.0 Figure 14: Figures at different stages in our ARS architecture for CIFAR-10 BG20k k = 64 input images. Check Appendix D.2 for detailed information about each row (a) σ = 0.25 (b) σ = 1.0 Figure 15: Figures at different stages in our ARS architecture for CIFAR-10 BG20k k = 96 input images. Check Appendix D.2 for detailed information about each row E Additional Results on CelebA", "fig_num": null, "type_str": "figure", "uris": null, "num": null}, "FIGREF9": {"text": "Figure 16: The localized ARS masks produce un-noised mouth regions after averaging.", "fig_num": "16", "type_str": "figure", "uris": null, "num": null}, "FIGREF10": {"text": "Figure 16 shows how adaptive masking reduces the noise around areas that are important to classification. The images follow our architecture visualized Figure 1. The mask model is provided the first query noised images as input. The learned masks, presented in the bottom left, are sparse and highly concentrated around the area of interest-the mouth area. The second query noised images (after weighted average) use the mask to clearly reduce the noise around the mouth. This large noise reduction enables ARS to outperform static masking and <PERSON> et al. (2019), as shown on Figure 4.", "fig_num": "16", "type_str": "figure", "uris": null, "num": null}, "FIGREF11": {"text": "Figure 17: Certified test accuracy on CelebA (unaligned and cropped). Each line is the mean and the shading covers ±1 standard deviation across three seeds. Adaptivity helps at all noise levels.", "fig_num": "17", "type_str": "figure", "uris": null, "num": null}, "FIGREF12": {"text": "Figure 18: The localized ARS masks produce un-noised object regions after averaging. For σ = 1.", "fig_num": "18", "type_str": "figure", "uris": null, "num": null}, "FIGREF13": {"text": "Figure 19: For σ = 0.5.", "fig_num": "19", "type_str": "figure", "uris": null, "num": null}, "FIGREF14": {"text": "Figure 20: For σ = 0.25. Figures 18 to 20 show how adaptive masking reduces the noise around areas that are important to classification for ImageNet. The images follow our architecture visualized Figure 1. The mask model is provided the first query noised images as input. The learned masks, presented in the bottom left, are sparse and concentrated around the area of interest (the bird, or any labelled object). The second query noised images after weighted average use the mask to clearly reduce the around the bird. Setting/Approach <PERSON> et al. ARS (Pretrain) ARS (End-To-End) ImageNet, σ = 0.25 68.5 (0.1) 69.5 (0.0) 70.1 (0.2) ImageNet, σ = 0.5 60.7 (0.5) 63.2 (0.1) 64.2 (0.2) ImageNet, σ = 1.0 47.9 (0.1) 52.1 (0.0) 53.8 (0.3)", "fig_num": "20", "type_str": "figure", "uris": null, "num": null}, "TABREF1": {"text": "Standard Accuracy (r = 0) on CIFAR-10 (20kBG). Our 20kBG benchmark places CIFAR-10 images on larger background images. We report the mean accuracy and standard deviation over three seeds. ARS achieves higher accuracy across noise σ and input dimension k ( ∆ indicates adaptivity). We provide results with more σ levels in Appendix D.", "type_str": "table", "content": "<table><tr><td/><td>.8)</td><td>40.0 (0.5)</td><td>42.2 (0.6)</td><td>26.5 (0.7)</td><td>50.4 (2.5)</td></tr><tr><td>σ = 0.25, k = 96</td><td>65.3 (1.6)</td><td>71.8 (1.3)</td><td>68.8 (1.8)</td><td>45.5 (0.9)</td><td>78.3 (2.2)</td></tr><tr><td>σ = 0.5, k = 96</td><td>56.6 (2.4)</td><td>59.5 (1.4)</td><td>59.7 (1.3)</td><td>10.8 (2.3)</td><td>69.8 (1.2)</td></tr><tr><td>σ = 1.0, k = 96</td><td>33.8 (3.8)</td><td>36.9 (0.5)</td><td>41.3 (2.4)</td><td>10.4 (0.3)</td><td>56.3 (2.3)</td></tr></table>", "num": null, "html": null}, "TABREF2": {"text": "Standard test accuracy (r = 0) on ImageNet. ARS maintains standard accuracy.", "type_str": "table", "content": "<table><tr><td/><td colspan=\"3\"><PERSON> et al. ARS (Pretrain) ARS (End-To-End)</td></tr><tr><td colspan=\"2\">ImageNet, σ = 0.25 66.5 (0.009)</td><td>67.4 (0.002)</td><td>65.7 (0.006)</td></tr><tr><td>ImageNet, σ = 0.5</td><td>57.2 (0.009)</td><td>56.0 (0.003)</td><td>57.4 (0.010)</td></tr><tr><td>ImageNet, σ = 1.0</td><td>43.6 (0.005)</td><td>43.8 (0.002)</td><td>44.5 (0.010)</td></tr></table>", "num": null, "html": null}, "TABREF3": {"text": "UniCR β chosen for each k, σ setting.", "type_str": "table", "content": "<table><tr><td>5</td></tr></table>", "num": null, "html": null}, "TABREF5": {"text": "Standard", "type_str": "table", "content": "<table/>", "num": null, "html": null}, "TABREF6": {"text": "<PERSON> et al. w/ AdamW ARS", "type_str": "table", "content": "<table><tr><td/><td>200</td><td>77.2 (2.7)</td><td>79.0 (0.7)</td><td>78.5 (0.7)</td></tr><tr><td>0.12</td><td>500</td><td>79.6 (2.1)</td><td>82.1 (0.4)</td><td>82.3 (1.1)</td></tr><tr><td/><td>10k</td><td>80.3 (0.6)</td><td>82.7 (0.6)</td><td>83.4 (0.5)</td></tr><tr><td/><td>200</td><td>70.7 (3.7)</td><td>70.6 (1.0)</td><td>72.6 (0.9)</td></tr><tr><td>0.25</td><td>500</td><td>72.4 (2.6)</td><td>75.6 (0.9)</td><td>75.9 (0.9)</td></tr><tr><td/><td>10k</td><td>73.7 (2.6)</td><td>77.2 (0.2)</td><td>77.7 (0.1)</td></tr><tr><td/><td>200</td><td>62.0 (0.4)</td><td>63.6 (2.0)</td><td>64.0 (1.4)</td></tr><tr><td>0.5</td><td>500</td><td>63.1 (0.7)</td><td>65.2 (0.9)</td><td>65.9 (0.7)</td></tr><tr><td/><td>10k</td><td>64.3 (0.5)</td><td>66.1 (0.3)</td><td>67.9 (0.3)</td></tr><tr><td/><td>200</td><td>45.0 (1.5)</td><td>48.0 (0.7)</td><td>49.3 (0.6)</td></tr><tr><td>1.0</td><td>500</td><td>45.9 (1.6)</td><td>49.1 (0.4)</td><td>50.5 (0.8)</td></tr><tr><td/><td>10k</td><td>46.8 (1.7)</td><td>50.0 (0.5)</td><td>51.5 (0.4)</td></tr></table>", "num": null, "html": null}, "TABREF7": {"text": "Test accuracy without certification on ImageNet.", "type_str": "table", "content": "<table/>", "num": null, "html": null}, "TABREF8": {"text": "• The full details can be provided either with the code, in appendix, or as supplemental material. 7. Experiment Statistical Significance Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [Yes] Justification: We ran all experiments end-to-end over three different seeds, and show both mean and standard deviation on plots and tables (except on ImageNet, for resources reasons). The factors of variability are those that are standard and natural to deep learning and robustness by randomized smoothing: initialization of parameters, train-time sampling of the data, train-time sampling of augmentations, and test-time sampling of noise for certification. Guidelines: • The answer NA means that the paper does not include experiments. • The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. 8. Experiments Compute Resources Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments? Answer: [Yes] Justification: The computing resources are included in Table 4. The resources are single GPUs, which while required are sufficiently standard for this field as to be understandable and accessible. Guidelines: • The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper). 9. Code Of Ethics Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?", "type_str": "table", "content": "<table><tr><td>Answer: [Yes]</td></tr></table>", "num": null, "html": null}, "TABREF9": {"text": "14. Crowdsourcing and Research with Human Subjects Question: For crowdsourcing experiments and research with human subjects, does the paper include the full text of instructions given to participants and screenshots, if applicable, as well as details about compensation (if any)? Answer: [NA] Justification: No crowdsourcing or human subjects. Guidelines: • The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Including this information in the supplemental material is fine, but if the main contribution of the paper involves human subjects, then as much detail as possible should be included in the main paper. • According to the NeurIPS Code of Ethics, workers involved in data collection, curation, or other labor should be paid at least the minimum wage in the country of the data collector. 15. Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects Question: Does the paper describe potential risks incurred by study participants, whether such risks were disclosed to the subjects, and whether Institutional Review Board (IRB) approvals (or an equivalent approval/review based on the requirements of your country or institution) were obtained? Answer: [NA] Justification: No crowdsourcing or human subjects. Guidelines: • The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper.", "type_str": "table", "content": "<table/>", "num": null, "html": null}}}}