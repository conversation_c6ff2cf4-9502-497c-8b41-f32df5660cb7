{"paper_id": "MutaPLM", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:05:07.932450Z"}, "title": "MutaPLM: Protein Language Modeling for Mutation Explanation and Engineering", "authors": [{"first": "Yizhen", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "Suyuan", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": "<PERSON><PERSON><PERSON>@air.tsinghua.edu.cn"}, {"first": "Zaiqing", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": "<EMAIL>"}, {"first": "Delta", "middle": [], "last": "Decoder", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Studying protein mutations within amino acid sequences holds tremendous significance in life sciences. Protein language models (PLMs) have demonstrated strong capabilities in broad biological applications. However, due to architectural design and lack of supervision, PLMs model mutations implicitly with evolutionary plausibility, which is not satisfactory to serve as explainable and engineerable tools in real-world studies. To address these issues, we present MutaPLM, a unified framework for interpreting and navigating protein mutations with protein language models. MutaPLM introduces a protein delta network that captures explicit protein mutation representations within a unified feature space, and a transfer learning pipeline with a chain-of-thought (CoT) strategy to harvest protein mutation knowledge from biomedical texts. We also construct MutaDescribe, the first large-scale protein mutation dataset with rich textual annotations, which provides cross-modal supervision signals. Through comprehensive experiments, we demonstrate that MutaPLM excels at providing human-understandable explanations for mutational effects and prioritizing novel mutations with desirable properties. Our code, model, and data are open-sourced at https://github.com/PharMolix/MutaPLM.", "pdf_parse": {"paper_id": "MutaPLM", "_pdf_hash": "", "abstract": [{"text": "Studying protein mutations within amino acid sequences holds tremendous significance in life sciences. Protein language models (PLMs) have demonstrated strong capabilities in broad biological applications. However, due to architectural design and lack of supervision, PLMs model mutations implicitly with evolutionary plausibility, which is not satisfactory to serve as explainable and engineerable tools in real-world studies. To address these issues, we present MutaPLM, a unified framework for interpreting and navigating protein mutations with protein language models. MutaPLM introduces a protein delta network that captures explicit protein mutation representations within a unified feature space, and a transfer learning pipeline with a chain-of-thought (CoT) strategy to harvest protein mutation knowledge from biomedical texts. We also construct MutaDescribe, the first large-scale protein mutation dataset with rich textual annotations, which provides cross-modal supervision signals. Through comprehensive experiments, we demonstrate that MutaPLM excels at providing human-understandable explanations for mutational effects and prioritizing novel mutations with desirable properties. Our code, model, and data are open-sourced at https://github.com/PharMolix/MutaPLM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Studying protein evolution through mutations within amino acid sequences is a central research topic in life sciences [1] [2] [3] . Despite immense research efforts, a large number of protein mutations with biological significance remain under-explored, highlighting the demand for in-silico tools to model these mutations. Practically, the tool should meet two requirements. First, it should be explainable, providing insightful and human-understandable interpretations for mutational effects. This is crucial for broad biological applications ranging from identifying immune-escape pathogens [4, 5] to interpreting the mechanisms of human diseases [6, 7] . Additionally, the tool should be engineerable, proposing protein mutations that satisfy desirable properties such as catalytic activity and thermostability. This process is known as directed evolution [8, 9] , the most prevailing approach for protein design in the laboratory, which offers substantial benefits across various application fields, including industry [10] , biotechnology [11] , and therapeutics [12] .", "cite_spans": [{"start": 118, "end": 121, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 122, "end": 125, "text": "[2]", "ref_id": "BIBREF1"}, {"start": 126, "end": 129, "text": "[3]", "ref_id": "BIBREF2"}, {"start": 594, "end": 597, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 598, "end": 600, "text": "5]", "ref_id": "BIBREF4"}, {"start": 650, "end": 653, "text": "[6,", "ref_id": "BIBREF5"}, {"start": 654, "end": 656, "text": "7]", "ref_id": "BIBREF6"}, {"start": 860, "end": 863, "text": "[8,", "ref_id": "BIBREF7"}, {"start": 864, "end": 866, "text": "9]", "ref_id": "BIBREF8"}, {"start": 1024, "end": 1028, "text": "[10]", "ref_id": "BIBREF9"}, {"start": 1045, "end": 1049, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 1069, "end": 1073, "text": "[12]", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "To achieve these goals, deep learning models [13] [14] [15] have emerged to capture evolutionary information from protein sequences. Recently, the development of protein language models (PLMs) [16] [17] [18] [19] [20] has brought a paradigm shift in computational biology. By self-supervised learning [21] on evolutionary-scale databases [22, 23] , PLMs have achieved great success in various biological applications, including structure prediction [19, 24] and protein design [18, 25] . Additionally, PLMs have demonstrated zero-shot capabilities in predicting and optimizing evolutionary plausibility [26] [27] [28] , a continuous value indicating whether a mutation is favored by natural selection.", "cite_spans": [{"start": 45, "end": 49, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 50, "end": 54, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 55, "end": 59, "text": "[15]", "ref_id": "BIBREF14"}, {"start": 193, "end": 197, "text": "[16]", "ref_id": "BIBREF15"}, {"start": 198, "end": 202, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 203, "end": 207, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 208, "end": 212, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 213, "end": 217, "text": "[20]", "ref_id": "BIBREF19"}, {"start": 301, "end": 305, "text": "[21]", "ref_id": "BIBREF20"}, {"start": 338, "end": 342, "text": "[22,", "ref_id": "BIBREF21"}, {"start": 343, "end": 346, "text": "23]", "ref_id": "BIBREF22"}, {"start": 449, "end": 453, "text": "[19,", "ref_id": "BIBREF18"}, {"start": 454, "end": 457, "text": "24]", "ref_id": "BIBREF23"}, {"start": 477, "end": 481, "text": "[18,", "ref_id": "BIBREF17"}, {"start": 482, "end": 485, "text": "25]", "ref_id": "BIBREF24"}, {"start": 603, "end": 607, "text": "[26]", "ref_id": "BIBREF25"}, {"start": 608, "end": 612, "text": "[27]", "ref_id": "BIBREF26"}, {"start": 613, "end": 617, "text": "[28]", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Despite their promising advancements, we argue that existing PLMs are not yet satisfactory as explainable and engineerable tools for handling protein mutations. Regarding mutation explanation, PLMs' implicit interpretation with evolutionary plausibility is overly vague, lacking detailed information for mutational effects such as specific alterations in protein functions and impacts on organisms. Regarding mutation engineering, PLMs can only propose evolutionary-plausible mutations, which may be misaligned with human preferences in real-world practices of directed evolution. For example, enhancing the catalytic activity of an enzyme from a bacterium could be detrimental to its survival due to increased energy costs but beneficial for industrial applications. In such scenarios, the utility of PLMs in assisting protein engineering is significantly compromised.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In this paper, we aim to develop explainable and engineerable PLMs by explicitly modeling protein mutations. However, conventional PLMs based on the Transformers [29] architecture provide contextaware representations for each amino acid, which are inadequate for capturing the discrepancies between the wild-type and its mutant within a unified feature space. Besides, there is a lack of supervision signals necessary for comprehending the intricate impacts of protein mutations, which require extensive background knowledge, including protein structures, protein functions, and mechanisms of biological processes.", "cite_spans": [{"start": 162, "end": 166, "text": "[29]", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "To address these issues, we envision that (1) mutation representations could be captured from the variations of PLM representations between the wild-type and its mutant with appropriate architecture, and (2) expert-written texts from protein databases and biomedical publications provide rich crossmodal supervision for learning protein mutations. Specifically, we propose MutaPLM, a unified framework for interpreting and navigating Mutations with Protein Language Models. We introduce a protein delta network that translates between mutations and protein delta features, formulating a unified feature space aligned with textual semantics. We develop a transfer learning pipeline with a chain-of-thought (CoT) strategy [30] to harvest protein mutation knowledge from biomedical texts. Additionally, we construct MutaDescribe, the first large-scale dataset containing diverse protein mutations and rich textual annotations of their effects. Using natural language as a friendly interface, the dataset facilitates the training and evaluation of mutation explanation and engineering.", "cite_spans": [{"start": 720, "end": 724, "text": "[30]", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Through comprehensive experiments, we demonstrate that MutaPLM is a versatile, explainable, and engineerable tool for assisting protein mutation studies. In mutation explanation, MutaPLM outperforms the strongest baseline model by 6.5% in ROUGE-L, and 19.4% of the predicted mutational effects are regarded as accurate and insightful by human experts. In mutation engineering, our model achieves an average of 0.409 recall scores on top-50 mutation proposals navigated by free-text instructions, improving ESM-2 [19] by 1.6-fold.", "cite_spans": [{"start": 512, "end": 516, "text": "[19]", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Our contributions are summarized as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• We propose MutaPLM, a unified framework that enables protein language models to capture mutations explicitly using a protein delta network and cross-modal supervision.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• We build MutaDescribe, the first dataset with detailed textual annotations for protein mutations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• We validate the effectiveness of MutaPLM in explaining and engineering protein mutations through comprehensive experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "2 Related Work", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In analogy to large language models (LLMs) [31] [32] [33] [34] in natural language processing (NLP), protein language models (PLMs) such as ProteinBERT [35] , ProtTrans [17] , ProtGPT2 [18] , and ESM series [36, 19, 37] have surged in modeling protein sequences. Pre-trained by masked language modeling [38] or auto-regressive language modeling [39] on evolutionary-scale protein databases, PLMs have demonstrated outstanding predictive power on protein secondary and tertiary structures [24] , protein functions [40] and protein-protein interactions [41] . More recently, explorations on PLMs unifying protein sequences and natural language [42] [43] [44] [45] have attracted rising research interest, as texts provide unstructured knowledge and a friendly user interface for studying proteins. Notably, a contemporary work [46] proposes to perform text-based protein editing by directly generating the mutated protein sequence. Unfortunately, none of the existing PLMs qualifies as an explainable and engineerable tool in modeling protein mutations, mainly owing to architectural design and lack of supervision.", "cite_spans": [{"start": 43, "end": 47, "text": "[31]", "ref_id": "BIBREF30"}, {"start": 48, "end": 52, "text": "[32]", "ref_id": "BIBREF31"}, {"start": 53, "end": 57, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 58, "end": 62, "text": "[34]", "ref_id": "BIBREF33"}, {"start": 152, "end": 156, "text": "[35]", "ref_id": "BIBREF34"}, {"start": 169, "end": 173, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 185, "end": 189, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 207, "end": 211, "text": "[36,", "ref_id": "BIBREF35"}, {"start": 212, "end": 215, "text": "19,", "ref_id": "BIBREF18"}, {"start": 216, "end": 219, "text": "37]", "ref_id": "BIBREF36"}, {"start": 303, "end": 307, "text": "[38]", "ref_id": "BIBREF37"}, {"start": 345, "end": 349, "text": "[39]", "ref_id": "BIBREF38"}, {"start": 488, "end": 492, "text": "[24]", "ref_id": "BIBREF23"}, {"start": 513, "end": 517, "text": "[40]", "ref_id": "BIBREF39"}, {"start": 551, "end": 555, "text": "[41]", "ref_id": "BIBREF40"}, {"start": 642, "end": 646, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 647, "end": 651, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 652, "end": 656, "text": "[44]", "ref_id": "BIBREF43"}, {"start": 657, "end": 661, "text": "[45]", "ref_id": "BIBREF44"}, {"start": 825, "end": 829, "text": "[46]", "ref_id": "BIBREF45"}], "ref_spans": [], "eq_spans": [], "section": "Protein Language Models", "sec_num": "2.1"}, {"text": "Previous works formulate mutation explanation as learning the 'local fitness landscape', a mapping from protein sequences to specific functional activity scores [47] . Models for protein fitness prediction could be categorized as (1) alignment-based models [48, 49] trained on multiple sequence alignments (MSAs) [50] , (2) PLM models [18, 19] trained on large-scale unaligned sequences, (3) inverse-folding models [27, 51] that learn protein fitness through structure-conditioned sequence distributions, and (4) hybrid models [52, 53] that combine both PLMs and MSAs. The evaluations are performed as per wild-type protein on deep mutation scanning (DMS) [54] or clinical variant [55] benchmarks. In this work, we formulate mutation explanation as a more challenging task that aims at providing textual descriptions of mutational effects for arbitrary wild-type protein and mutation.", "cite_spans": [{"start": 161, "end": 165, "text": "[47]", "ref_id": "BIBREF46"}, {"start": 257, "end": 261, "text": "[48,", "ref_id": "BIBREF47"}, {"start": 262, "end": 265, "text": "49]", "ref_id": "BIBREF48"}, {"start": 313, "end": 317, "text": "[50]", "ref_id": "BIBREF49"}, {"start": 335, "end": 339, "text": "[18,", "ref_id": "BIBREF17"}, {"start": 340, "end": 343, "text": "19]", "ref_id": "BIBREF18"}, {"start": 415, "end": 419, "text": "[27,", "ref_id": "BIBREF26"}, {"start": 420, "end": 423, "text": "51]", "ref_id": "BIBREF50"}, {"start": 527, "end": 531, "text": "[52,", "ref_id": "BIBREF51"}, {"start": 532, "end": 535, "text": "53]", "ref_id": "BIBREF52"}, {"start": 656, "end": 660, "text": "[54]", "ref_id": "BIBREF53"}, {"start": 681, "end": 685, "text": "[55]", "ref_id": "BIBREF54"}], "ref_spans": [], "eq_spans": [], "section": "Protein Mutation Modeling", "sec_num": "2.2"}, {"text": "The traditional mutation engineering [8, 9] task aims at generating protein mutants with high fitness scores. One line of work leverages generative models including variational auto-encoders (VAEs) [56] , generative language models [57] and diffusion models [58] to directly generate the protein sequence conditioned on fitness scores. Another line attempts to propose mutations iteratively by greedy sampling [59] , reinforcement learning [60] , or proximal gradients [61] on the learned fitness landscape. Differing from prior studies, MutaPLM incorporates textual instructions instead of fitness scores as navigation and proposes mutations satisfying human preferences.", "cite_spans": [{"start": 37, "end": 40, "text": "[8,", "ref_id": "BIBREF7"}, {"start": 41, "end": 43, "text": "9]", "ref_id": "BIBREF8"}, {"start": 198, "end": 202, "text": "[56]", "ref_id": "BIBREF55"}, {"start": 232, "end": 236, "text": "[57]", "ref_id": "BIBREF56"}, {"start": 258, "end": 262, "text": "[58]", "ref_id": "BIBREF57"}, {"start": 410, "end": 414, "text": "[59]", "ref_id": "BIBREF58"}, {"start": 440, "end": 444, "text": "[60]", "ref_id": "BIBREF59"}, {"start": 469, "end": 473, "text": "[61]", "ref_id": "BIBREF60"}], "ref_spans": [], "eq_spans": [], "section": "Protein Mutation Modeling", "sec_num": "2.2"}, {"text": "The main goal of our work is to develop explainable and engineerable PLMs by explicitly modeling protein mutations. To achieve this goal, we elaborate on the proposed MutaPLM framework, highlighting three design components: (1) a protein delta network that translates between mutations and protein delta features z ∆ (Sec. 3.1, detailed in Appendix A.1), (2) a transfer learning pipeline with a chain-of-thought strategy that harvests protein mutation knowledge from cross-modal supervision (Sec. 3.2, detailed in Appendix A.3), and (3) a specifically constructed dataset with diverse proteins and rich textual annotations of mutation effects (Sec. 3.3, detailed in Appendix B.2).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Methods", "sec_num": "3"}, {"text": "The protein delta network follows an encoder-decoder architecture, utilizing textual semantics as the latent feature space for protein mutations. As illustrated in Fig. 1 , the protein delta network is composed of a protein language model (PLM), a large language model (LLM), a wild-type encoder, a delta encoder, a delta decoder, and two mutation prediction heads. We leverage ESM-2 (650M) [19] , a powerful PLM pre-trained on evolutionary-scale databases, to encode protein sequences. We initialize the LLM with BioMedGPT-LM [62] , a scientific language model built on LLaMA2-7B [31] through continual pre-training [63] on large-scale biomedical corpora.", "cite_spans": [{"start": 391, "end": 395, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 527, "end": 531, "text": "[62]", "ref_id": "BIBREF61"}, {"start": 581, "end": 585, "text": "[31]", "ref_id": "BIBREF30"}, {"start": 617, "end": 621, "text": "[63]", "ref_id": "BIBREF62"}], "ref_spans": [{"start": 169, "end": 170, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "Protein Delta Network for Explicit Mutation Modeling", "sec_num": "3.1"}, {"text": "Formulation of protein delta features. We speculate that the subtraction of PLM representations between the mutant and wild-type, denoted as h ∆ , contains rich mutation information, making it suitable for extracting protein delta features z ∆ . Specifically:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Protein Delta Network for Explicit Mutation Modeling", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "h ∆ = h mt -h wt = f PLM (x mt ) -f PLM (x wt ),", "eq_num": "(1)"}], "section": "Protein Delta Network for Explicit Mutation Modeling", "sec_num": "3.1"}, {"text": "where x mt and x wt are the amino acid sequences of the mutant and wild-type protein, h mt and h wt are their sequence representations, and f PLM is the protein language model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Protein Delta Network for Explicit Mutation Modeling", "sec_num": "3.1"}, {"text": "The delta encoder f enc and delta decoder f dec facilitates bi-directional transformations between h ∆ and z ∆ as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Protein Delta Network for Explicit Mutation Modeling", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z ∆ = f enc (h ∆ ), h ∆ = f dec (z ∆ ).", "eq_num": "(2)"}], "section": "Protein Delta Network for Explicit Mutation Modeling", "sec_num": "3.1"}, {"text": "Encoding protein delta features. Given h ∆ , the delta encoder is expected to extract informationpreserving protein delta features z ∆ within a unified feature space. However, protein sequences vary in length, ranging from several tens to thousands of amino acids. To address this issue, we adopt a cross-attention module [29] to transform the sequential representations into a fixed number of latent features. The module, partly inspired by BLIP series [64, 65] , maintains K trainable features that serve as queries and takes the sequence representations as keys and values to generate outputs. We employ two parallel modules for encoding the wild-type features h wt and mutational features h ∆ .", "cite_spans": [{"start": 322, "end": 326, "text": "[29]", "ref_id": "BIBREF28"}, {"start": 454, "end": 458, "text": "[64,", "ref_id": "BIBREF63"}, {"start": 459, "end": 462, "text": "65]", "ref_id": "BIBREF64"}], "ref_spans": [], "eq_spans": [], "section": "Protein Delta Network for Explicit Mutation Modeling", "sec_num": "3.1"}, {"text": "Decoding protein delta features. Drawing inspirations from LM-DESIGN [66] , we introduce a cross-attention module that takes a symmetrical form of the delta encoder. Specifically, it treats the wild-type protein representations h wt as queries and protein delta features z ∆ as keys and values.", "cite_spans": [{"start": 69, "end": 73, "text": "[66]", "ref_id": "BIBREF65"}], "ref_spans": [], "eq_spans": [], "section": "Protein Delta Network for Explicit Mutation Modeling", "sec_num": "3.1"}, {"text": "The outputs are then processed by a two-layer feed-forward network (FFN) to reconstruct h ∆ . The mutant representations h mt are obtained by combining h ∆ with h wt , and fed into a position head and a language modeling head to predict the mutation. The position head is a fully-connected layer that predicts whether the amino acid should be substituted. The language modeling head is initialized from the PLM and predicts the type of the mutated amino acid. To facilitate text-based protein engineering, we maintain K trainable soft tokens, which are appended to the input token embeddings of the LLM to summarize textual semantics. The output representations of the soft tokens are processed by the delta decoder to generate mutations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Protein Delta Network for Explicit Mutation Modeling", "sec_num": "3.1"}, {"text": "Compared with previous works that connect protein sequences with LLMs [67, 44, 45] , the proposed protein delta network exhibits the following advantages:", "cite_spans": [{"start": 70, "end": 74, "text": "[67,", "ref_id": "BIBREF66"}, {"start": 75, "end": 78, "text": "44,", "ref_id": "BIBREF43"}, {"start": 79, "end": 82, "text": "45]", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "Protein Delta Network for Explicit Mutation Modeling", "sec_num": "3.1"}, {"text": "• Explicit modeling of protein mutations. Prior models are designed for static protein sequences, while MutaPLM models the alterations introduced by mutations with protein delta features z ∆ . • Encoder-decoder architecture. Prior works adopt either an encoder or a decoder architecture for protein sequences, while MutaPLM incorporates both encoding and decoding components.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Protein Delta Network for Explicit Mutation Modeling", "sec_num": "3.1"}, {"text": "Biomedical texts contain rich expert-annotated information on protein properties and mutational effects. As depicted in Fig. 2 , MutaPLM harvests these cross-modal supervision signals through a transfer learning pipeline, which we detail as follows:", "cite_spans": [], "ref_spans": [{"start": 125, "end": 126, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Transfer Learning with Cross-modal Supervision", "sec_num": "3.2"}, {"text": "Pre-training on protein literature. In this stage, we aim to incorporate general protein knowledge from scientific publications with language modeling objectives, as shown in Fig. 2(a) . ( 1) For the encoding workflow, we take the output representations of the wild-type encoder as LLM inputs and calculate the next-token prediction objective [39] for generating descriptive texts. (2) For the decoding workflow, we employ the conditional masked language modeling (CMLM) objective [68] on the protein sequence. Specifically, we mask 15% amino acids and task the PLM to recover the masks based on the remaining amino acid sequence and the LLM-summarized textual representations.", "cite_spans": [{"start": 343, "end": 347, "text": "[39]", "ref_id": "BIBREF38"}, {"start": 481, "end": 485, "text": "[68]", "ref_id": "BIBREF67"}], "ref_spans": [{"start": 180, "end": 184, "text": "2(a)", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Transfer Learning with Cross-modal Supervision", "sec_num": "3.2"}, {"text": "It is worth noting that in this stage, the delta decoder acts as a modality translator, generating bias terms that help reconstruct the original sequence instead of capturing protein mutation information. Overall, we optimize the summation of these two language modeling objectives.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Transfer Learning with Cross-modal Supervision", "sec_num": "3.2"}, {"text": "Fine-tuning on protein mutations with chain-of-thought (CoT). As depicted in Fig. 2 (b), we fine-tune MutaPLM on textual annotations of mutational effects to facilitate mutation explanation and engineering. Since mutational effects typically involve the enhancement or attenuation of protein functions, we adopt a chain-of-thought (CoT) strategy [30] that seamlessly connects protein functions and mutational effects within a two-round dialogue. In the first round, we prompt the LLM to describe the functions of the wild-type protein using the encoding workflow. In the second round, we introduce two tasks, namely describing the mutational effects with the encoding workflow, and predicting the mutation based on textual instructions with the decoding workflow. Both tasks utilize the latent wild-type representations and the predicted functions from the first round dialogue as additional inputs. Formally, the overall objective of fine-tuning is the summation of three parts: (1) next token prediction on protein function descriptions, (2) next token prediction on mutational effects, and (3) weighted cross-entropy between the predicted mutation and the ground-truth mutation. We build MutaDescribe, a large-scale dataset comprising 20.9K wild-type proteins and 171.1K single-site mutations, to facilitate fine-tuning and evaluation. We provide an overview of our dataset in Tab. 1. The construction process involves the following steps:", "cite_spans": [{"start": 346, "end": 350, "text": "[30]", "ref_id": "BIBREF29"}], "ref_spans": [{"start": 82, "end": 83, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Transfer Learning with Cross-modal Supervision", "sec_num": "3.2"}, {"text": "Raw data collection. The primary source of MutaDescribe is UniProtKB/SwissProt [69] , a widely adopted protein database that contains 106.6K single-site substitutions. We collect expert-reviewed descriptions of mutational effects from the Phenotypes & Variants entry and retrieve the abstract of the corresponding publications on PubMed [70] based on available reference information.", "cite_spans": [{"start": 79, "end": 83, "text": "[69]", "ref_id": "BIBREF68"}, {"start": 337, "end": 341, "text": "[70]", "ref_id": "BIBREF69"}], "ref_spans": [], "eq_spans": [], "section": "Transfer Learning with Cross-modal Supervision", "sec_num": "3.2"}, {"text": "Quality control. We prompt GPT-3.5-turbo [33] to filter out low-quality descriptions such as those that only mention the originating species. This step helps ensure that the dataset contains high-quality and informative annotations. Data enrichment. Given that the descriptions in UniProtKB are generally short and homogeneous, we utilize GPT-3.5-turbo to enrich the textual annotations by retrieving relevant descriptions from the original PubMed abstract. Additionally, we balance the number of benign and malignant mutations by constructing reversed samples. Specifically, for each mutation, we attempt to exchange the wild-type and the mutant and prompt GPT-3.5-turbo to write a description opposite to the original mutational effect. For example, if the mutational effect of an A89H mutation is \"Increased catalytic activity\", we will create a reversed sample with an H89A mutation and \"Decreased catalytic activity\".", "cite_spans": [{"start": 41, "end": 45, "text": "[33]", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "Transfer Learning with Cross-modal Supervision", "sec_num": "3.2"}, {"text": "Data splitting. We first randomly split our dataset into training, validation, and test sets. To evaluate models' generalization capabilities on novel proteins, we further partition the test set into three subsets based on the wild-type sequence homology with training sequences. We adopt MMSeqs2 [71] , a widely-adopted tool to calculate sequence homology. The Easy, Medium and Hard test subsets comprise samples whose sequence homology are between [0.95, 1], [0.5, 0.95), and [0, 0.5) respectively. We also implement a temporal split based on the publication date of the mutation, and we defer readers to Appendix B for details and Appendix D.1 for evaluation results.", "cite_spans": [{"start": 297, "end": 301, "text": "[71]", "ref_id": "BIBREF70"}], "ref_spans": [], "eq_spans": [], "section": "Transfer Learning with Cross-modal Supervision", "sec_num": "3.2"}, {"text": "Compared with prior mutation benchmarks [55, 72, 73] , MutaDescribe is the first to incorporate textual annotations for facilitating mutation explanation and engineering. Besides, MutaDescribe contains a wider variety of wild-type proteins, surpassing ProteinGym [73] by 6 times in quantity.", "cite_spans": [{"start": 40, "end": 44, "text": "[55,", "ref_id": "BIBREF54"}, {"start": 45, "end": 48, "text": "72,", "ref_id": "BIBREF71"}, {"start": 49, "end": 52, "text": "73]", "ref_id": "BIBREF72"}, {"start": 263, "end": 267, "text": "[73]", "ref_id": "BIBREF72"}], "ref_spans": [], "eq_spans": [], "section": "Transfer Learning with Cross-modal Supervision", "sec_num": "3.2"}, {"text": "In this section, we demonstrate that MutaPLM is adept at interpreting and engineering mutations through comprehensive experiments. We start with a brief introduction of our training setups (Sec. 4.1), followed by detailed evaluations on two core tasks: mutation explanation (Sec. 4.2) and mutation engineering (Sec. 4.3). We also present an in-depth analysis of our design components (Sec. 4.4), including pre-training and the CoT strategy. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "4"}, {"text": "To alleviate catastrophic forgetting [76] and save computational costs, we train MutaPLM in a parameter-efficient way. We apply low-rank adaptation (LoRA) [77] on the LLM with a rank of 16.", "cite_spans": [{"start": 37, "end": 41, "text": "[76]", "ref_id": "BIBREF75"}, {"start": 155, "end": 159, "text": "[77]", "ref_id": "BIBREF76"}], "ref_spans": [], "eq_spans": [], "section": "Training Setup", "sec_num": "4.1"}, {"text": "The number of query embeds and soft tokens is set as K = 32. We optimize the LoRA modules, the wild-type encoder, the delta encoder, the delta decoder, the soft tokens, the position head, and the language modeling (LM) head, which comprises a total of 75.0M parameters. The remaining 7.4B parameters are kept frozen.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Training Setup", "sec_num": "4.1"}, {"text": "We pre-train MutaPLM for 200K steps with a batch size of 32 on 1.1M protein-text data collected from biomedical publications (detailed in Appendix B.1) and fine-tune it for 70K steps with a batch size of 24 on MutaDescribe. For both stages, we use the AdamW optimizer [78] with a learning rate that is linearly warmed up to 10 -4 for the first 1K steps and decreases to 10 -5 following a cosine annealing strategy. The overall training process takes 10 days on 4 NVIDIA A100 GPUs.", "cite_spans": [{"start": 268, "end": 272, "text": "[78]", "ref_id": "BIBREF77"}], "ref_spans": [], "eq_spans": [], "section": "Training Setup", "sec_num": "4.1"}, {"text": "Differing from existing studies that interpret mutational effects with protein fitness [26, 28] , we formulate mutation explanation as providing detailed textual descriptions for protein mutations.", "cite_spans": [{"start": 87, "end": 91, "text": "[26,", "ref_id": "BIBREF25"}, {"start": 92, "end": 95, "text": "28]", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Performance Evaluation on Mutation Explanation", "sec_num": "4.2"}, {"text": "Baselines. While no prior work is specifically designed for this task, we perform zero-shot analysis on popular LLMs with various zero-shot or few-shot prompts and implement supervised models for comparison. Our baselines include (1) Text-based LLMs. We perform in-context learning [79] by providing 1-shot and 5-shot demonstrations to GPT-4 [33] , the most advanced model in NLP. Additionally, we implement a k-nearest neighbor (kNN) strategy [80] that selects the top-k homologous proteins from the training set as few-shot examples. (2) LLM-assisted PLMs, including ESM-2 [19] and OntoProtein [75] . In addition to kNN-based 5-shot samples for GPT-4, we leverage PLMs to provide additional information by predicting the evolutionary plausibility of the mutation.", "cite_spans": [{"start": 282, "end": 286, "text": "[79]", "ref_id": "BIBREF78"}, {"start": 342, "end": 346, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 444, "end": 448, "text": "[80]", "ref_id": "BIBREF79"}, {"start": 575, "end": 579, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 596, "end": 600, "text": "[75]", "ref_id": "BIBREF74"}], "ref_spans": [], "eq_spans": [], "section": "Performance Evaluation on Mutation Explanation", "sec_num": "4.2"}, {"text": "(3) LLMs trained on protein sequences, including Galactica-6.7B [74] , Mol-Instructions [67] , and ProtLLM [44] . We feed the wild-type and mutated protein sequences into these models and instruct them to provide mutation explanations. (4) Fine-tuned LLMs. We fine-tune BioMedGPT-LM by feeding the ESM-2 representations of the wild-type and mutant (Fine-tuned ESM-2) or the wild-type sequence and evolutionary plausibility (AugmentedESM [27] ) into the LLM and performing casual generation. Notably, for all ESM-2 models used in our baselines, we adopt the model with 650M parameters for fair comparison. We defer readers to Appendix C.1 for more implementation details.", "cite_spans": [{"start": 64, "end": 68, "text": "[74]", "ref_id": "BIBREF73"}, {"start": 88, "end": 92, "text": "[67]", "ref_id": "BIBREF66"}, {"start": 107, "end": 111, "text": "[44]", "ref_id": "BIBREF43"}, {"start": 437, "end": 441, "text": "[27]", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "Performance Evaluation on Mutation Explanation", "sec_num": "4.2"}, {"text": "Evaluation. We adopt <PERSON><PERSON><PERSON> [81] and R<PERSON><PERSON><PERSON> [82] scores to assess the quality of the generations by comparing them with ground-truth annotations. To further investigate whether the predictions are truly insightful and helpful in studying protein mutations, we perform a human-AI collaborative evaluation. Specifically, we first utilize GPT-4 as a proxy of human experts to categorize the predictions into Accurate, Relevant, Opposite, and Irrelevant, based on the relevance between the predictions and ground truth. Then, we recruit a postgraduate from a top university who majors in biology to assess and rectify GPT-4 evaluation results on mutation explanations following the same categorization protocol. The prompt and detailed evaluation results are displayed in Appendix C.3.", "cite_spans": [{"start": 26, "end": 30, "text": "[81]", "ref_id": "BIBREF80"}, {"start": 41, "end": 45, "text": "[82]", "ref_id": "BIBREF81"}], "ref_spans": [], "eq_spans": [], "section": "Performance Evaluation on Mutation Explanation", "sec_num": "4.2"}, {"text": "Results and analysis. We present performance comparisons on the test sets of MutaDescribe in Tab. 2 and Fig. 3 . We observe that: ( 4) Supervised baselines underperform few-shot GPT-4 models, especially on Medium and Hard sets and BLEU-2 scores. We observe that supervised models tend to randomly combine short textual segments from the training set, indicating overfitting problems. ( 5) LLMs trained on protein sequences perform poorly, as they are solely instruction-tuned on single protein sequences. Hence, we emphasize the significance of knowledge transfer from protein functions to mutational effects and their basic properties.", "cite_spans": [], "ref_spans": [{"start": 109, "end": 110, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Performance Evaluation on Mutation Explanation", "sec_num": "4.2"}, {"text": "Case study. Additionally, we present a case study in Fig. 4 for a mutation from m7GpppX diphosphatase. Our model accurately identifies the increased decapping activity and provides novel insights beyond the ground truth. In contrast, the GPT-4 model mistakenly identifies the mutational effects as decreases in enzymic activity. More cases are available in Appendix D.3.", "cite_spans": [], "ref_spans": [{"start": 58, "end": 59, "text": "4", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Performance Evaluation on Mutation Explanation", "sec_num": "4.2"}, {"text": "Differing from prior works [59] [60] [61] that perform mutation engineering with an active learning paradigm [84] , we challenge models to directly propose protein mutations based on the wild-type sequence and textual instructions. As we primarily focus on single-site mutations, we formulate this as a retrieval task from 19 × L possible mutants for a protein sequence of length L.", "cite_spans": [{"start": 27, "end": 31, "text": "[59]", "ref_id": "BIBREF58"}, {"start": 32, "end": 36, "text": "[60]", "ref_id": "BIBREF59"}, {"start": 37, "end": 41, "text": "[61]", "ref_id": "BIBREF60"}, {"start": 109, "end": 113, "text": "[84]", "ref_id": "BIBREF83"}], "ref_spans": [], "eq_spans": [], "section": "Performance Evaluation on Mutation Engineering", "sec_num": "4.3"}, {"text": "Baselines. We adopt four groups of baselines including: (1) Few-shot LLMs. Similar to mutation explanation, we prompt GPT-4 to suggest single-site mutations through in-context few-shot learning.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Performance Evaluation on Mutation Engineering", "sec_num": "4.3"}, {"text": "(2) Zero-shot PLMs including ESM-2 [19] and OntoProtein [75] . We calculate the evolutionary plausibility scores following [26] for each amino acid and derive the best mutant. (3) A retrieval-based model, namely ProtST (ESM-2) [42] . We calculate the cosine similarity between PLM and textual representations of mutational effects to score and rank mutations. (4) Fine-tuned models. We fine-tune BioMedGPT [62] to directly propose a mutation based on the protein sequence and instruction. We also fine-tune ESM-2 by combining its wild-type sequence representations with BioMedBERT [83] encodings of textual instructions by a cross-attention layer. Please refer to Appendix C.1 for details of our baselines.", "cite_spans": [{"start": 35, "end": 39, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 56, "end": 60, "text": "[75]", "ref_id": "BIBREF74"}, {"start": 123, "end": 127, "text": "[26]", "ref_id": "BIBREF25"}, {"start": 227, "end": 231, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 406, "end": 410, "text": "[62]", "ref_id": "BIBREF61"}, {"start": 581, "end": 585, "text": "[83]", "ref_id": "BIBREF82"}], "ref_spans": [], "eq_spans": [], "section": "Performance Evaluation on Mutation Engineering", "sec_num": "4.3"}, {"text": "Evaluation. We report the average accuracy of the mutated amino acid on the ground-truth mutational position. We also report top-50 recall scores on all possible mutations. Visualization of protein fitness on multi-round optimization. In addition to single-site mutations, we employ a beam-search algorithm [85] to obtain multi-point substitutions iteratively. We manually write the optimization objective for 6 representative benchmarks, set the number of beams as 20, perform 20 independent runs, and visualize the fitness scores predicted by ESM landscape models [86] . We compare MutaPLM with EvoProtGrad [87] , a gradient-based strategy that leverages PLMs for multi-round optimization, as well as with random sampling. More details are presented in Appendix C.4. As shown in Fig. 5 , our model consistently yields higher-fitness mutants across 6 proteins with varying objectives, especially in the initial rounds of optimization. These results highlight MutaPLM's potential in assisting real-world mutagenesis applications.", "cite_spans": [{"start": 307, "end": 311, "text": "[85]", "ref_id": "BIBREF84"}, {"start": 566, "end": 570, "text": "[86]", "ref_id": "BIBREF85"}, {"start": 609, "end": 613, "text": "[87]", "ref_id": "BIBREF86"}], "ref_spans": [{"start": 786, "end": 787, "text": "5", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "Performance Evaluation on Mutation Engineering", "sec_num": "4.3"}, {"text": "Impacts of transfer learning. We show the impacts of pre-training and fine-tuning in Fig. 6 . As the fine-tuning proceeds, the performance of MutaPLM continues to improve on the Easy set but deteriorates on the Medium and Hard sets, indicating overfitting problems on out-of-domain samples. Besides, without pre-training, MutaPLM achieves higher performance for the initial steps, which we attribute to the adaptation cost from pre-training texts to fine-tuning texts. However, the overall ROUGE-L scores decline by 1.56% for mutation explanation and 1.18% for mutation engineering as the fine-tuning finalizes. Overall, these results validate our transfer learning design.", "cite_spans": [], "ref_spans": [{"start": 90, "end": 91, "text": "6", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "In-depth Analysis", "sec_num": "4.4"}, {"text": "To investigate the impacts of the chain-of-thought strategy, we perform ablation studies by (1) replacing the predicted function with the ground truth description, (2) replacing the predicted function with 'Unknown function', (3) removing the delta features for mutation explanation, and (4) removing the mutational effects for mutation engineering. As shown in Tab. 4, removing protein functions leads to a performance drop of 2.80% for mutation explanation and 1.13% for mutation engineering. Conversely, using the ground-truth function results in notable improvements, particularly for mutation explanation. Besides, the delta features and mutational effects within the second-round dialog play more significant roles in MutaPLM. These findings highlight the significance of jointly incorporating protein function and mutation information in explaining and navigating protein mutations. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Impacts of chain-of-thought (CoT).", "sec_num": null}, {"text": "MutaPLM pioneers as the first attempt in the explicit modeling of protein mutations with natural language, and we expect future endeavors on (1) expanding the scale and diversity of the MutaDescribe dataset by integrating multi-point mutations and indels [73] , (2) analyzing the alterations of protein 3D structures [88] to deepen the understanding of mutations, and (3) developing active learning [84] pipelines to harness feedbacks from wet-lab experiments in real-world mutagenesis studies.", "cite_spans": [{"start": 255, "end": 259, "text": "[73]", "ref_id": "BIBREF72"}, {"start": 317, "end": 321, "text": "[88]", "ref_id": "BIBREF87"}, {"start": 399, "end": 403, "text": "[84]", "ref_id": "BIBREF83"}], "ref_spans": [], "eq_spans": [], "section": "Limitations and Broader Impacts", "sec_num": "5"}, {"text": "While MutaPLM bears promise in mutation explanation and engineering, we emphasize safety concerns that it can be misused to generate pathogenic mutations and harmful bio-agents. Hence, we declare that MutaPLM, upon public release, should be restricted to research purposes, and any further applications should undergo comprehensive experiments and human inspections.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations and Broader Impacts", "sec_num": "5"}, {"text": "In this work, we present MutaPLM, a unified framework harvesting protein language models for mutation explanation and engineering. We propose a protein delta network to model mutations explicitly with protein delta features and develop a transfer learning pipeline with a chain-of-thought strategy to integrate protein mutation knowledge from biomedical texts. Additionally, we construct MutaDescribe, the first large-scale dataset containing diverse proteins and detailed textual annotations for mutations. Our experiments demonstrate that MutaPLM offers insightful explanations for mutational effects and proposes desirable mutants based on textual instructions. We anticipate that the proposed MutaPLM framework and our publicly released dataset will pave the way for novel research avenues and applications in studying proteins. where the cross attention is calculated following Equ. A4.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusions", "sec_num": "6"}, {"text": "Mutation prediction heads. After reconstructing the mutant representation by h mt = h wt + h ∆ , we develop a position prediction head f pos and a language modeling head f LM to predict the mutation. Specifically:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusions", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "P x (mt) i ̸ = x (wt) i = f pos h (mt) i , P x (mt) i = f LM h (mt) i , (", "eq_num": "A7"}], "section": "Conclusions", "sec_num": "6"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusions", "sec_num": "6"}, {"text": "where P x", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusions", "sec_num": "6"}, {"text": "(mt) i ̸ = x (wt) i", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusions", "sec_num": "6"}, {"text": "denotes the probability of i-th amino acid to be mutated, and P x", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusions", "sec_num": "6"}, {"text": "(mt) i", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusions", "sec_num": "6"}, {"text": "denotes the probability distribution of the i-th amino acid. The parameters of the position prediction head are initialized from scratch, and those of the language modeling head are derived from the PLM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusions", "sec_num": "6"}, {"text": "To model mutations explicitly, we leverage the subtraction of the wild-type and mutant representations as the mutational features h ∆ , which is subsequently processed by the delta encoder. One of the essential considerations is that the PLM is overly smooth, making h ∆ too small and less informative. However, we argue that due to the non-smooth nature of the protein fitness landscape [61] , the output representations of PLMs are also non-smooth. Moreover, after training, the delta encoder learns to capture the orientation of h ∆ , yielding a z ∆ with an appropriate norm. We also present empirical justification by calculating the average l 2 -norm of h wt , h ∆ , and z ∆ on MutaDescribe, which are displayed in Tab. A1.", "cite_spans": [{"start": 388, "end": 392, "text": "[61]", "ref_id": "BIBREF60"}], "ref_spans": [], "eq_spans": [], "section": "A.2 Justifications for Mutational Features", "sec_num": null}, {"text": "MutaPLM performs pre-training on large-scale protein-relevant literature. Given the protein sequence x wt and its semantically related text t, we optimize the following objectives:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "Protein-to-text generation. We first concatenate the latent wild-type features z wt in Equ. A4 and the text embeddings e in Equ. A3. We perform conditional auto-regressive language modeling that aims to generate t based on the protein representations and previous tokens. The objective is calculated as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z = [z 1 , z 2 , • • • , z K protein , z K+1 , • • • , z K+N text ] = g transformers ([z wt ; e]) , P (t i |t <i , z wt ) = g LM (z K+i ), L p2t = 1 N N i=1 H [t i , P (t i |t <i , z wt )] ,", "eq_num": "(A8)"}], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "where H(•, •) denotes cross-entropy.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "Text-to-protein generation. We first append K trainable soft tokens s = [s 1 , s 2 , • • • , s K ] to the input token embeddings to summarize textual semantics. Then, we derive z ∆ as the last hidden state of s as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z = [z 1 , z2 , • • • , zN text , zN+1 , • • • , zN+K z∆ ] = g transformers ([e; s]),", "eq_num": "(A9)"}], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "where s denotes the soft tokens. We pass z ∆ into the delta decoder to obtain h ∆ as in Equ. A6. It is worth noting that in this stage, we are aimed at aligning the feature space of PLMs and LLMs, and z ∆ and h ∆ are NOT related to protein mutations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "Then, we randomly mask 15% amino acids in the protein sequence. We adopt the conditional masked language modeling objective to reconstruct the masked tokens as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "first-round dialog and protein delta features z ∆ . The objective is calculated as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "L exp = 1 T T i=1 H t (∆) i , P t (∆) i t (∆) <i , y func , z ∆ , z wt . (A14)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "Second-round dialog for mutation engineering. We apply the same soft tokens s as in pre-training to the input prompt to calculate the delta features based on the first-round dialog and descriptions of mutational effects:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ẑ = [ẑ 1 , ẑ2 , • • • , ẑN prompt , ẑN+1 , • • • , ẑN+K z∆ ] = g transformers ([t prompt ; s]), (", "eq_num": "A15"}], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "where t prompt is the input embeddings of the prompt involving the first-round dialog and the mutational effects.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "Then, reconstructing h mt = h wt + h ∆ with the delta decoder, we calculate the weighted cross-entropy loss for the mutation position and the mutated amino acid with the prediction heads:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L eng = - 1 L L i=1 1 x (mt) i = x (wt) i log(1 -f pos (h mt i )) + λ • 1 x (mt) i ̸ = x (wt) i log f pos (h mt i ) -L • 1 x (mt) i ̸ = x (wt) i H x (mt) i , f LM (h (mt) i ) ,", "eq_num": "(A16)"}], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "where 1{•} is the boolean indicator function, and λ is a hyper-parameter controlling label weight. In our experiments, we set λ = 50.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "The overall objective is calculated as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L 2 = E (xwt,xmt,tfunc,t∆)∼D2 (L func + L exp + L eng ),", "eq_num": "(A17)"}], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "where D 2 is our fine-tuning dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "The prompt templates for fine-tuning are displayed in Tab. A2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Pre-training Objectives", "sec_num": null}, {"text": "Our pre-training data involves 1.1M protein-text pairs collected from the UniProtKB/SwissProt [69] database. We download 467.8K proteins with the Publications entry and retrieve 257.2K PubMed [70] abstracts based on the reference information.", "cite_spans": [{"start": 94, "end": 98, "text": "[69]", "ref_id": "BIBREF68"}, {"start": 192, "end": 196, "text": "[70]", "ref_id": "BIBREF69"}], "ref_spans": [], "eq_spans": [], "section": "B Training data B.1 Pre-training Data", "sec_num": null}, {"text": "To create a natural language annotated dataset for protein mutations, we first collect 164K samples from the Phenotypes & Variants entry of UniProtKB/SwissProt. After deduplication and removing sites without valid text annotations, we obtain 107K mutants for 21K proteins as our raw data, comprising 33K natural variants and 74K mutagenesis sequences.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 Fine-tuning and Testing Data: MutaDescribe", "sec_num": null}, {"text": "Unfortunately, the collected raw data is not suitable for protein mutation modeling, mainly owing to the following problems: (1) As shown in Tab. A3, the expert-revised annotations within UniProtKB contain an average of 9.4 words, containing limited information. (2) Through analyzing the polarity of the mutational effects, we observe that the number of malignant and benign mutations are imbalanced (∼ 9:1), which may mislead model predictions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 Fine-tuning and Testing Data: MutaDescribe", "sec_num": null}, {"text": "To address these issues, (1) we perform data enrichment by collecting the abstracts of the biological literature in which the mutation is mentioned. We retrieve 50K publications based on the reference information of the mutation available in UniProtKB and prompt GPT-3.5-turbo to extract relevant information from the abstracts. The prompt template is visualized in Tab. A5. After ChatGPT enrichment, the textual annotations are expanded with an average of 28.3 words. (2) We generate 64.5K additional reverse samples. Specifically, for each malignant and benign mutation, we exchange the wild-type and mutant and prompt GPT-3.5-turbo to flip the polarity of the textual descriptions for mutational effects. We empirically find that the quality of mutation descriptions using GPT-3.5-turbo and GPT-4 is similar, and therefore we opt for GPT-3.5-turbo to save API costs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 Fine-tuning and Testing Data: MutaDescribe", "sec_num": null}, {"text": "We implement two splitting strategies for our dataset. For structural split, we first partition our dataset into training, validation, and test sets. Then, for each wild-type sequence in the test set, we calculate the maximum sequence homology with the wild-type sequences in the training set by MMseqs2 [71] . Based on the homology, we divide the test set into three subsets. The Easy subset comprises 460 mutants with homology between 0.95 and 1, the Medium subset comprises 384 mutants with homology between 0.5 and 0.95, and the Hard subset comprises 404 mutants with homology between 0 and 0.5. For temporal split, we extract the publication date of the literature reporting each mutation. Mutations studied before 2022 are used as training and validation sets, while those studied in 2022 and 2023 comprise the test set. The train/valid/test set comprises 156K, 8K, and 1.6K samples, respectively. The detailed statistics of temporal split are shown in Tab. A4.", "cite_spans": [{"start": 304, "end": 308, "text": "[71]", "ref_id": "BIBREF70"}], "ref_spans": [], "eq_spans": [], "section": "B.2 Fine-tuning and Testing Data: MutaDescribe", "sec_num": null}, {"text": "We present a closer look at our MutaDescribe dataset in Fig. A1 , displaying the length of protein sequences, the number of words in textual annotations, the number of mutation samples per protein, the distribution of the originating species, the distribution of the cellular localization and the distribution of the mutated amino acid. We show in our illustrations that MutaDescribe is a large-scale, diverse, and detailed annotated dataset for studying protein mutations.", "cite_spans": [], "ref_spans": [{"start": 61, "end": 63, "text": "A1", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "B.2 Fine-tuning and Testing Data: MutaDescribe", "sec_num": null}, {"text": "For mutation explanation, we implement the following baselines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "Galactica-6.7B [74] . This baseline is a unified large-language model pre-trained on scientific papers and protein knowledge bases. We prompt the model to investigate if it could explain mutational effects in a zero-shot manner.", "cite_spans": [{"start": 15, "end": 19, "text": "[74]", "ref_id": "BIBREF73"}], "ref_spans": [], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "ProLLaMA [45] . This baseline is developed on LLaMA2-7B by further pre-training the model on protein sequences from UniRef50 [22] . Similarly, we perform zero-shot mutation explanation by prompting.", "cite_spans": [{"start": 9, "end": 13, "text": "[45]", "ref_id": "BIBREF44"}, {"start": 125, "end": 129, "text": "[22]", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "Mol-Instructions [67] . We implement the protein-oriented model of Mol-Instructions that is instruction-tuned from LLaMA2-7B [31] . We perform zero-shot prompting that provides the model with the name and amino acid sequence of the protein sequence and task definitions.", "cite_spans": [{"start": 17, "end": 21, "text": "[67]", "ref_id": "BIBREF66"}, {"start": 125, "end": 129, "text": "[31]", "ref_id": "BIBREF30"}], "ref_spans": [], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "GPT-4 [33] with in-context learning. We adopt the 0613 version of GPT-4, the most advanced LLM in natural language processing. In addition to the protein name, wild-type sequence, and mutation Table A5 : Prompt template for data enrichment. We prompt GPT-3.5-turbo to extract relevant information from the abstracts of the biological literature in which the mutation is mentioned.", "cite_spans": [{"start": 6, "end": 10, "text": "[33]", "ref_id": "BIBREF32"}], "ref_spans": [{"start": 199, "end": 201, "text": "A5", "ref_id": null}], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "[System prompt]You will be provided with a document and some relevant mutation sites (for example, site A21D indicates a mutation from A to D at position 21). First, determine whether these sites are mentioned in the document. If so, extract the text from the document that describes the functional changes caused by these sites. Otherwise, you must extract any functional changes mentioned in the document. For each site, please try to extract the corresponding protein name or gene name. You must be accurate and clear. Return a series of JSON documents, with each JSON formatted as follows: {\"Mutation Site\": <provided mutation site>, \"Mentioned\": <whether this site is mentioned in the document> \"protein_name\": <protein name corresponding to the site>, \"gene_name\": <gene name corresponding to the site>, \"Functional_changes\": <functional information>} [User prompt] document: <document> sites: <list of mutation sites> information, we provide few-shot demonstrations to facilitate in-context learning. For the 1-shot and 5-shot baseline, we randomly sample 1 and 5 samples from the training set of MutaDescribe. For the kNN-based 5-shot baseline, we follow [80] to search for relevant samples based on the sequence homology calculated by MMseqs2 [71] . We select 5 samples from the training set with the highest homology as few-shot demonstrations for each test sample.", "cite_spans": [{"start": 1163, "end": 1167, "text": "[80]", "ref_id": "BIBREF79"}, {"start": 1252, "end": 1256, "text": "[71]", "ref_id": "BIBREF70"}], "ref_spans": [], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "GPT-4 + ESM-2 [19] . ESM-2 is a popular protein language model pre-trained on evolutionary-scale databases. Given a mutation, we mask the mutated position and utilize ESM-2 (650M) to predict the logits for the mutated amino acid. Following [26] , we adopt the subtraction between the mutant and wild-type logits as the evolutionary plausibility scores. We follow the 5-shot kNN setting on GPT-4 and provide the scores as additional information.", "cite_spans": [{"start": 14, "end": 18, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 240, "end": 244, "text": "[26]", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "GPT-4 + OntoProtein [75] . OntoProtein is a text-augmented PLM that aligns protein sequences with gene ontology definitions. We follow the GPT-4 + ESM-2 baseline to predict mutational effects based on evolutionary plausibility and kNN few-shot demonstrations.", "cite_spans": [{"start": 20, "end": 24, "text": "[75]", "ref_id": "BIBREF74"}], "ref_spans": [], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "AugmentedESM [27] . In the original paper, the model is designed to solve fitness regression tasks by linearly combining the adaptive fitness score calculated following [26] and the amino acid sequence.", "cite_spans": [{"start": 13, "end": 17, "text": "[27]", "ref_id": "BIBREF26"}, {"start": 169, "end": 173, "text": "[26]", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "We slightly adapt the model to perform mutation explanation by feeding the fitness score and the raw protein sequence into BioMedGPT-LM. We fine-tune the LLM with the casual auto-regressive language modeling objective on mutation effects. The hyperparameters for fine-tuning are the same as MutaPLM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "Finetuned ESM-2. Similar to MiniGPT-4 [89] , we translate each residue representation of ESM-2 (650M) [19] into LLM input embeddings using a linear projection layer. We fine-tune BioMedGPT-LM with the casual auto-regressive language modeling objective on mutation effects based on the translated features of the wild-type and mutant. The hyperparameters for fine-tuning are also the same as MutaPLM.", "cite_spans": [{"start": 38, "end": 42, "text": "[89]", "ref_id": "BIBREF88"}, {"start": 102, "end": 106, "text": "[19]", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "The prompts for our baselines are displayed in Tab. A6.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.1 Baselines for Mutation Explanation", "sec_num": null}, {"text": "For mutation engineering, we implement the following baselines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "Random. As the name suggests, the proposed mutations are randomly sampled from every possible single-site substitution with equal probability.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "GPT-4 [33] with in-context learning. We provide few-shot examples for GPT-4 to suggest protein mutations, and the sampling strategy is the same as in mutation explanation. We evaluate accuracy and top-50 recall with a two-round dialog. In the first-round dialog, we directly prompt GPT-4 to provide 50 mutations on arbitrary positions. In the second-round dialog, we provide the model with the ground-truth position and ask ESM-2 [19] . We feed the whole sequence into the PLM to calculate the output logits for each amino acid. We rank mutations by the subtraction of the mutant and wild-type logits.", "cite_spans": [{"start": 6, "end": 10, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 430, "end": 434, "text": "[19]", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "OntoProtein [75] . This baseline follows the same implementation as ESM2-650M.", "cite_spans": [{"start": 12, "end": 16, "text": "[75]", "ref_id": "BIBREF74"}], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "ProtST (ESM-2) [42] . ProtST trains a series of PLMs by contrastive learning [90] between protein sequences and biomedical texts. Hence, we implement a cross-modal retrieval strategy, using the cosine similarity between the mutated sequence and the textual description of mutational effects to score mutations. We opt not to report top-50 recall scores due to: (1) unaffordable computational costs, as each possible mutation requires an individual forward pass, and (2) poor performance, as the baseline merely outperforms random guesses.", "cite_spans": [{"start": 15, "end": 19, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 77, "end": 81, "text": "[90]", "ref_id": "BIBREF89"}], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "Fine-tuned BioMedGPT. We provide the LLM with the wild-type sequence and textual instructions of desired mutational effects, and fine-tune the model to propose mutations. To evaluate accuracy, we additionally provide the mutated position and prompt the model to generate the mutated amino acid. To evaluate top-50 recall, we prompt the model to generate a single mutation, since our dataset only comprises one ground-truth mutation. The evaluations are performed within two independent sessions, and we combine the causal auto-regressive language modeling objective of both sessions during fine-tuning.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "Table A8 : Prompt template for GPT-4 evaluation. We leverage GPT-4 to categorize predictions into Accurate, Relevant, Opposite, and Irrelevant, based on the relevance between the predicted functional alterations and ground-truth explanations.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 8, "text": "A8", "ref_id": null}], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "[System prompt] You are an expert in biology and protein sciences. You want to figure out the effects of protein mutations by alterations of protein functions. Now we provide you with two descriptions of protein mutational effects in a JSON format, where the \"label\" denotes the ground truth description of the mutational effects, and the \"prediction\" denotes the prediction of a model. You should be precise and faithful in evaluating if the predicted mutation effects are semantically related to the ground truth. You should answer with one of the following categories:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "(1) Accurate. The prediction and the label describe the same functions that are altered, and the extent of functional changes is mostly the same (For example, \"strongly decrease\" and \"abolish\").", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "(2) Relevant. The prediction and the label describe the same functions that are altered, and the extent of functional changes are in the same direction (For example, \"strongly increase\" and \"slightly increase\").", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "(3) Opposite. The prediction and the label describe the same functions that are altered, but the functional changes are opposite (For example, \"increase\" and \"decrease\"). ( 4) Irrelevant. The prediction and the label describe different alterations of functions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "Note that you should be careful about the altered functions before analyzing the extent. Answer with one word only from \"Accurate\", \"Relevant\", \"Opposite\" and \"Irrelevant\" to summarize your evaluation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "[User prompt]{\"label\": {ground_truth}, \"prediction\": {model_output}}", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "• Accurate. The predicted alterations in protein functions and estimations of extent are mostly the same as the ground truth. • Relevant. The prediction identifies the protein function that is altered by the mutation. While it accurately predicts the attenuation or the degradation, the estimation of the extent is not correct. • Opposite. The prediction identifies the protein function that is altered by the mutation. However, it mistakenly predicts attenuation as degradation or vice versa. • Irrelevant. The prediction and the ground truth are about completely different functional alterations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "Then, we recruit a postgraduate from a top university who majors in biology to further assess the results. Specifically, we collect samples that are marked as Accurate, Relevant, and Opposite by GPT-4, and include Irrelevant samples for strong baselines (5-shot GPT-4 models and fine-tuned models) and MutaPLM. We present the mutation explanations, ground-truth results, GPT-4 evaluation, and categorization protocol, and ask the expert to rectify the evaluation result if necessary. In total, 12.0% of the GPT-4 evaluations are modified, and the confusion matrix is displayed in Fig. A2 . We observe that GPT-4 evaluation is consistent with human experts in most cases, showcasing its reliability as a proxy of expert evaluators in saving evaluation costs. However, it occasionally misclassifies Accurate predictions into Relevant, and Relevant or Opposite predictions into Irrelevant, which we attribute to the fact that GPT-4 tends to favor more fluent answers instead of more informative ones. We leave more realistic and labor-saving evaluation strategies for future exploration.", "cite_spans": [], "ref_spans": [{"start": 585, "end": 587, "text": "A2", "ref_id": "FIGREF7"}], "eq_spans": [], "section": "C.2 Baselines for Mutation Engineering", "sec_num": null}, {"text": "We incorporate the following datasets from [86] for multi-round fitness optimization:", "cite_spans": [{"start": 43, "end": 47, "text": "[86]", "ref_id": "BIBREF85"}], "ref_spans": [], "eq_spans": [], "section": "C.4 Multi-round Optimization", "sec_num": null}, {"text": "• Adeno-associated Viruses (AAV) [91] . The dataset involves a 28-amino acid segment of the caspid protein VP1 from Adeno-associated virus. The optimization objective is to improve its capability as a gene delivery vector. • Aliphatic Amide Hydrolase (AMIE) [92] . The dataset aims to improve the enzymic activity of Aliphatic amidase from Pseudomonas aeruginosa in catalyzing the hydrolysis of short-chain aliphatic amides. LGK Increased enzyme activity. UBE2I Increased growth rescue rate at high temperature in a yeast strain.", "cite_spans": [{"start": 33, "end": 37, "text": "[91]", "ref_id": "BIBREF90"}, {"start": 258, "end": 262, "text": "[92]", "ref_id": "BIBREF91"}], "ref_spans": [], "eq_spans": [], "section": "C.4 Multi-round Optimization", "sec_num": null}, {"text": "• Green Fluorescent Proteins (avGFP) [93] . The dataset aims to enhance the fluorescent intensity of the Green Fluorescent Protein from Aequorea victoria. The protein is widely adopted as a biosensor for detecting gene expressions and protein locations. • Ubiquitination Factor Ube4b (E4B) [94] . The dataset aims to improve the enzymic activity of Ubiquitin conjugation factor E4B in Homo sapiens, which plays a role in proteasomal degradation by interacting with other proteins. • Levoglucosan Kinase (LGK) [95] . The dataset focuses on Levoglucosan kinase in Lipomyces starkeyi. The optimization objective is to enhance its catalytic activity in canonical kinase phosphotransfer reaction. • SUMO E2 conjugase (UBE2I) [96] . The dataset studies SUMO-conjugating enzyme UBC9 in Homo sapiens which is relevant to several human diseases. The optimization objective is to improve the growth rescue rate at high temperatures in a yeast strain.", "cite_spans": [{"start": 37, "end": 41, "text": "[93]", "ref_id": "BIBREF92"}, {"start": 290, "end": 294, "text": "[94]", "ref_id": "BIBREF93"}, {"start": 509, "end": 513, "text": "[95]", "ref_id": "BIBREF94"}, {"start": 720, "end": 724, "text": "[96]", "ref_id": "BIBREF95"}], "ref_spans": [], "eq_spans": [], "section": "C.4 Multi-round Optimization", "sec_num": null}, {"text": "We manually write prompts in Tab. A9 to navigate the optimization process by a beam search process. Specifically, we initialize the candidate set with the wild-type sequence. Then, for each round of optimization, we feed each candidate sequence and the textual instruction into the decoding workflow of MutaPLM. Then we sample K mutations, the probability of which is proportional to the logits of the position head and the logits of the LM head. The optimization process is further detailed in Algorithm 1. The baselines are implemented by the EvoProtGrad [87] package. We perform experiments for 20 times, each comprising 10 optimization rounds.", "cite_spans": [{"start": 557, "end": 561, "text": "[87]", "ref_id": "BIBREF86"}], "ref_spans": [], "eq_spans": [], "section": "C.4 Multi-round Optimization", "sec_num": null}, {"text": "The experimental results for mutation explanation and engineering are shown in Tab. A10 and Tab. A11 respectively. We observe that: (1) MutaPLM achieves promising performance on the temporal split and outperforms strong baselines, showcasing its robustness in handling novel mutations. (2) For mutation explanation, the experiment results are similar to those on the Hard set of the structural split, and we observe similar over-fitting issues as in structural split that more training steps lead ▷ Calculate the logits two prediction heads Score(x, i, j) ← Score pos i + Score aa i,j , ∀i ̸ = j ▷ The score mutating i-th amino acid to j end for P (x, i, j) ← GlobalSoftMax[Score(x, i, j)] ▷ Probality distribution of sampling mutations C ← Mutate(x, i, j), (x, i, j) ∼ SampleK(P ) ▷ Sampling without replacement end for return C to improved validation loss but performance drops on the test set. This further underscores the significance of improving the generalization capability of mutation explanation models to assist real-world applications. (3) For mutation engineering, the results show little difference with those on the structural split. As discussed in Sec. 4.3, the PLM may have witnessed the protein sequence during pre-training, which mitigates the overfitting problem.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.1 Experiment Results on Temporal Split", "sec_num": null}, {"text": "While MutaPLM is not specifically designed for numeric tasks, we investigate if the learned Delta features could benefit fitness regression. We perform experiments on two protein fitness benchmarks, namely Spike-ACE2 [97] and avGFP [93] . Spike-ACE2 is a deep mutational scanning dataset that aims to predict the binding strengths between SARS-Cov-2 variants and its receptor ACE2, which is critical for identifying potentially dangerous strains of the virus. The avGFP benchmark aims to predict the fluorescence intensity of GFP variants, which is beneficial for developing biomarkers. [19] 0.331±0.041 0.554±0.013 Augmented ESM [27] 0.363±0.021 0.497±0.096 Augmented EVmutation [48] 0.354±0.044 0.512±0.034 ConFit [28] 0.412±0.033 0.564±0.035 Tranception_L [99] 0.488±0.040 0.594±0.019 MutaPLM 0.481±0.028 0.596±0.032", "cite_spans": [{"start": 217, "end": 221, "text": "[97]", "ref_id": "BIBREF96"}, {"start": 232, "end": 236, "text": "[93]", "ref_id": "BIBREF92"}, {"start": 587, "end": 591, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 630, "end": 634, "text": "[27]", "ref_id": "BIBREF26"}, {"start": 680, "end": 684, "text": "[48]", "ref_id": "BIBREF47"}, {"start": 716, "end": 720, "text": "[28]", "ref_id": "BIBREF27"}, {"start": 759, "end": 763, "text": "[99]", "ref_id": "BIBREF98"}], "ref_spans": [], "eq_spans": [], "section": "D.2 Low-N Fitness Regression", "sec_num": null}, {"text": "Following prior works [98, 28] , we adopt the low-N setting with 192 randomly sampled training samples and 48 validation samples. We calculate the adaptive fitness by our PLM following [26] and concatenate it with the delta features z ∆ . The result is fed into a trainable 2-layer MLP to predict the fitness scores, and the remaining parameters are kept frozen. We also implement baselines including Ridge Regression, ESM-2 [19] , AugmentedESM [27] , Augmented EVmutation [48] , ConFit [28] ,", "cite_spans": [{"start": 22, "end": 26, "text": "[98,", "ref_id": "BIBREF97"}, {"start": 27, "end": 30, "text": "28]", "ref_id": "BIBREF27"}, {"start": 185, "end": 189, "text": "[26]", "ref_id": "BIBREF25"}, {"start": 425, "end": 429, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 445, "end": 449, "text": "[27]", "ref_id": "BIBREF26"}, {"start": 473, "end": 477, "text": "[48]", "ref_id": "BIBREF47"}, {"start": 487, "end": 491, "text": "[28]", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "D.2 Low-N Fitness Regression", "sec_num": null}, {"text": "and Tranception_L [99] . All the models are trained for 50 epochs with a batch size of 16 and a learning rate of 0.001 using the MSE loss. We sample different low-N datasets with 5 random seeds and report the results in Tab. A12.", "cite_spans": [{"start": 18, "end": 22, "text": "[99]", "ref_id": "BIBREF98"}], "ref_spans": [], "eq_spans": [], "section": "D.2 Low-N Fitness Regression", "sec_num": null}, {"text": "We observe that MutaPLM significantly outperforms baseline models that adopt ESM-2 as the PLM, indicating that the delta features have captured mutational knowledge from natural language supervision that benefits fitness regression tasks. While MutaPLM achieves comparable results with Tranception_L on both benchmarks, it is worth noting that the model adopts a different network architecture specifically designed for fitness regression. Therefore, we speculate that adopting a mutation-oriented PLM instead of ESM-2 may further improve the performance. While fitness regression is not the main focus of our work, we expect future endeavors that jointly harvest discrete textual descriptions and continuous fitness scores.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Low-N Fitness Regression", "sec_num": null}, {"text": "We present more case studies of mutation explanation in Fig. A3 .", "cite_spans": [], "ref_spans": [{"start": 61, "end": 63, "text": "A3", "ref_id": "FIGREF8"}], "eq_spans": [], "section": "D.3 Additional Case Studies", "sec_num": null}, {"text": "Answer Justification: N/A.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3 Additional Case Studies", "sec_num": null}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3 Additional Case Studies", "sec_num": null}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3 Additional Case Studies", "sec_num": null}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.3 Additional Case Studies", "sec_num": null}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [Yes] Justification: We discuss broader impacts in Section 5.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)? Answer: [Yes] Justification: We clarify safeguards in Section 5.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort. 12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected? Answer: [Yes] Justification: The original papers of assets are cited. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}], "back_matter": [{"text": "This research is supported by the National Key R&D Program of China (No. 2022YFF1203002) and PharMolix Inc.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgments and Disclosure of Funding", "sec_num": null}, {"text": "A.1 Model Architecture Our protein delta network consists of a protein language model (PLM), a large language model (LLM), a wild-type encoder, a delta encoder, a delta decoder, and two prediction heads for mutation. We introduce these components as follows:Protein language model. We formulate the wild-type protein as an amino acid sequence x wt = x of length L. We focus on single-site substitution mutants, denoted by its sequencesatisfying H(x wt , x mt ) = 1, where H(•, •) is the Hamming distance. We adopt ESM-2 (650M) [19] as our protein language model f PLM , which transforms the protein sequences into dense feature vectors as follows:Then, we introduce the mutational representation, h ∆ , calculated as follows:Large language model. Similarly, we formulate biomedical texts as a sequence of tokens t = [t 1 , t 2 , • • • , t N ]. We initialize our LLM with BioMedGPT-LM [62] , which is obtained by continually pre-training LLaMA2-7B [31] on biomedical corpus. The large language model f LLM takes the following steps to transform t into latent features and output distributions of the next token:where g emb is the word embedding layer, z t is the textual representation calculated by transformer blocks g transformers , g LM is the language modeling head, and P (t i |t <i ) is the probability distribution of i-th token based on preceding tokens.Wild-type encoder. The wild-type encoder comprises K trainable query vectors q wt = [q 1 , q 2 , • • • , q K ] and a cross attention module. It transforms the wild-type representations h wt into a fixed number of features as follows:where W Q , W K , W V are trainable parameters, and d k is the feature dimension.Delta encoder. The delta encoder follows the same architecture as the wild-type encoder. It encodes the protein delta features as follows:where q ∆ are the K trainable queries, and the cross attention is calculated following Equ. A4. Notably, the wild-type encoder and delta encoder comprise independent parameters.Delta decoder. The delta decoder transforms the protein delta features z ∆ back to the original mutation representations h ∆ . It comprises a cross-attention layer and a two-layer feed-forward network with ReLU activation. Specifically: where x mask is the masked sequence of the wild-type x wt , and M denotes the masked positions.Overall objective. The overall objective for pre-training is calculated by:where E denotes expectation, and D 1 denotes our pre-training dataset.", "cite_spans": [{"start": 527, "end": 531, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 884, "end": 888, "text": "[62]", "ref_id": "BIBREF61"}, {"start": 947, "end": 951, "text": "[31]", "ref_id": "BIBREF30"}], "ref_spans": [], "eq_spans": [], "section": "Appendix A Details of MutaPLM", "sec_num": null}, {"text": "We employ a chain-of-thought (CoT) strategy to reason over protein functions and mutational effects in a two-round dialog. Given the wild type sequence x wt , the mutant sequence x mt , the description of protein functions t func and the description of mutation effects t ∆ , we calculate the following objectives:First-round dialog. We first prompt the LLM to generate function descriptionsM based on the wild-type protein. We perform conditional auto-regressive language modeling as follows:The predictions of protein functions y func = y (func)N is derived by:Second-round dialog for mutation explanation. We prompt the LLM to generate textual descriptions for mutation effectsbased on the function information in the Evaluating Rec@50 on GPT-4[System prompt] You are an expert in bioinformatics. You will be provided with a protein and the functional change resulting from a single-site mutation. Please predict the 50 most probable mutation sites where Each entry starts with the amino acid before the mutation, followed by the position of the mutation, and ends with the amino acid after the mutation. For example, D65A indicates that the amino acid at position 65 changes from D to A. Your response should only contain the 50 sites in a list format separated by commas, without additional words.[ Evaluating Rec@50 on fine-tuned BioMedGPT You are an expert assistant in biology and protein engineering. Now you are given a protein sequence and an instruction describing a mutation effect.Protein: {protein sequence} Instruction: {mutational effects} User: Please design a mutation that best fits the instruction. Assistant:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.4 Fine-tuning Objectives", "sec_num": null}, {"text": "Accuracy on fine-tuned BioMedGPT You are an expert assistant in biology and protein engineering. Now you are given a protein sequence and an instruction describing a mutation effect. Protein: {protein sequence} Instruction: {mutational effects} User: Given mutation at position {mutation position}, please choose an amino acid that best fits the instruction. Assistant:Fine-tuned ESM-2. We leverage BioMedBERT [83] to encode the textual instructions. We employ a cross-attention layer that takes the ESM-2 representations of the wild-type sequence as queries and the BioMedBERT representations as keys and values. The outputs are fed into a position prediction head and a language modeling head to predict mutations, which is the same as MutaPLM.The prompt templates for GPT-4 and fine-tuned BioMedGPT are presented in Tab. A7.", "cite_spans": [{"start": 410, "end": 414, "text": "[83]", "ref_id": "BIBREF82"}], "ref_spans": [], "eq_spans": [], "section": "Evaluating", "sec_num": null}, {"text": "Due to the complexity of biomedical texts, we develop a human-AI collaborative evaluation pipeline to comment on the accuracy and helpfulness of predicted mutational effects. Specifically, we query GPT-4 to compare model predictions with ground-truth annotations as in Tab. A8 and categorize them as follows.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.3 Human-AI Collaborative Evaluation for Mutation Explanation", "sec_num": null}, {"text": "Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope? Answer: [Yes] Justification: The claims are validated by our experiments in Section 4. Guidelines:• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<PERSON><PERSON><PERSON>", "sec_num": "1."}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors? Answer: [Yes] Justification: We discuss our limitations in Section 5. Guidelines:• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Answer: [NA] Justification: The paper does not include theoretical results.Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: We provide the dataset construction process in Section 3.3 and implementation details in Section 4.1. Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "An integrated view of protein evolution", "authors": [{"first": "Csaba", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Balázs", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2006, "venue": "Nature reviews genetics", "volume": "7", "issue": "5", "pages": "337--348", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>. An integrated view of protein evolution. Nature reviews genetics, 7(5):337-348, 2006.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Mutational effects and the evolution of new protein functions", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "Tawfik", "suffix": ""}], "year": 2010, "venue": "Nature Reviews Genetics", "volume": "11", "issue": "8", "pages": "572--582", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Mutational effects and the evolution of new protein functions. Nature Reviews Genetics, 11(8):572-582, 2010.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Predicting the functional impact of protein mutations: application to cancer genomics", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2011, "venue": "Nucleic acids research", "volume": "39", "issue": "17", "pages": "118--e118", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Predicting the functional impact of protein mutations: application to cancer genomics. Nucleic acids research, 39(17):e118-e118, 2011.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Sars-cov-2 variants, spike mutations and immune escape", "authors": [{"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["M"], "last": "Thomson", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Rambaut", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature Reviews Microbiology", "volume": "19", "issue": "7", "pages": "409--424", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Sars-cov-2 variants, spike mutations and immune escape. Nature Reviews Microbiology, 19(7):409-424, 2021.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Increased immune escape of the new sars-cov-2 variant of concern omicron", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xiao<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Cellular & Molecular Immunology", "volume": "19", "issue": "2", "pages": "293--295", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Increased immune escape of the new sars-cov-2 variant of concern omicron. Cellular & Molecular Immunology, 19(2):293-295, 2022.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Defective protein folding as a basis of human disease", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ba<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "<PERSON>u", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1995, "venue": "Trends in biochemical sciences", "volume": "20", "issue": "11", "pages": "456--459", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Defective protein folding as a basis of human disease. Trends in biochemical sciences, 20(11):456-459, 1995.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "The structural basis of protein folding and its links with human disease", "authors": [{"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Philosophical Transactions of the Royal Society of London. Series B: Biological Sciences", "volume": "356", "issue": "", "pages": "133--145", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. The structural basis of protein folding and its links with human disease. Philosophical Transactions of the Royal Society of London. Series B: Biological Sciences, 356 (1406):133-145, 2001.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Directed evolution drives the next generation of biocatalysts", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2009, "venue": "Nature chemical biology", "volume": "5", "issue": "8", "pages": "567--573", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Directed evolution drives the next generation of biocatalysts. Nature chemical biology, 5(8):567-573, 2009.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Directed evolution: bringing new chemistry to life", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "<PERSON><PERSON><PERSON><PERSON> Chemie (International Ed. in English)", "volume": "57", "issue": "16", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Directed evolution: bringing new chemistry to life. <PERSON><PERSON><PERSON><PERSON> Chemie (International Ed. in English), 57(16):4143, 2018.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Directed evolution of biocatalysts", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1999, "venue": "Current opinion in chemical biology", "volume": "3", "issue": "1", "pages": "54--59", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Directed evolution of biocatalysts. Current opinion in chemical biology, 3(1):54-59, 1999.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Directed evolution of green fluorescent protein by a new versatile pcr strategy for site-directed and semi-random mutagenesis", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2000, "venue": "Nucleic acids research", "volume": "28", "issue": "16", "pages": "78--e78", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Directed evolution of green fluorescent protein by a new versatile pcr strategy for site-directed and semi-random mutagenesis. Nucleic acids research, 28(16):e78-e78, 2000.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Directed evolution of antibody fragments with monovalent femtomolar antigen-binding affinity", "authors": [{"first": "<PERSON>", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Katarina", "middle": ["S"], "last": "Midelfort", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2000, "venue": "Proceedings of the National Academy of Sciences", "volume": "97", "issue": "20", "pages": "10701--10705", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON> <PERSON>. Directed evolution of antibody fragments with monovalent femtomolar antigen-binding affinity. Proceedings of the National Academy of Sciences, 97(20):10701-10705, 2000.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Sequence-based prediction of protein protein interaction using a deep-learning algorithm", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Luhua", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ji<PERSON><PERSON>", "middle": [], "last": "Pei", "suffix": ""}], "year": 2017, "venue": "BMC bioinformatics", "volume": "18", "issue": "", "pages": "1--8", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Sequence-based prediction of protein protein interaction using a deep-learning algorithm. BMC bioinformatics, 18:1-8, 2017.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Deep learning in protein structural modeling and design", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sulam", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Patterns", "volume": "1", "issue": "9", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Deep learning in protein structural modeling and design. Patterns, 1(9), 2020.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Learning the protein language: Evolution, structure, and function", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Cell systems", "volume": "12", "issue": "6", "pages": "654--669", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Learning the protein language: Evolution, structure, and function. Cell systems, 12(6):654-669, 2021.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Progen: Language modeling for protein generation", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Po-Ssu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2004.03497"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Progen: Language modeling for protein generation. arXiv preprint arXiv:2004.03497, 2020.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Prottrans: Toward understanding the language of life through self-supervised learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Dallag<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Llion", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "IEEE transactions on pattern analysis and machine intelligence", "volume": "44", "issue": "10", "pages": "7112--7127", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Prottrans: Toward understanding the language of life through self-supervised learning. IEEE transactions on pattern analysis and machine intelligence, 44(10):7112-7127, 2021.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Protgpt2 is a deep unsupervised language model for protein design", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Ferruz", "suffix": ""}, {"first": "Steffen", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Nature communications", "volume": "13", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Protgpt2 is a deep unsupervised language model for protein design. Nature communications, 13(1):4348, 2022.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Evolutionary-scale prediction of atomic-level protein structure with a language model", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Halil", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hie", "suffix": ""}, {"first": "Zhongkai", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Smetanin", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Verkuil", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Science", "volume": "379", "issue": "6637", "pages": "1123--1130", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Evolutionary-scale prediction of atomic-level protein structure with a language model. Science, 379(6637):1123-1130, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Poet: A generative model of protein families as sequences-of-sequences", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Poet: A generative model of protein families as sequences-of-sequences. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Self-supervised learning: Generative or contrastive", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Fanjin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Li", "middle": [], "last": "Mian", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "IEEE transactions on knowledge and data engineering", "volume": "35", "issue": "1", "pages": "857--876", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Self-supervised learning: Generative or contrastive. IEEE transactions on knowledge and data engineering, 35(1):857-876, 2021.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Uniref clusters: a comprehensive and scalable alternative for improving sequence similarity searches", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Baris E Suzek", "suffix": ""}, {"first": "Hongzhan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["H"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Uniprot", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Consortium", "suffix": ""}], "year": 2015, "venue": "Bioinformatics", "volume": "31", "issue": "6", "pages": "926--932", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and UniProt Consortium. Uniref clusters: a comprehensive and scalable alternative for improving sequence similarity searches. Bioinformatics, 31(6):926-932, 2015.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Clustering huge protein sequence sets in linear time", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Nature communications", "volume": "9", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Clustering huge protein sequence sets in linear time. Nature communications, 9(1):2542, 2018.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Protein language-model embeddings for fast, accurate, and alignment-free protein structure prediction", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Rost", "suffix": ""}], "year": 2022, "venue": "Structure", "volume": "30", "issue": "8", "pages": "1169--1177", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Protein language-model embeddings for fast, accurate, and alignment-free protein structure prediction. Structure, 30(8): 1169-1177, 2022.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Controllable protein design with language models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Ferruz", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Nature Machine Intelligence", "volume": "4", "issue": "6", "pages": "521--532", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>. Controllable protein design with language models. Nature Machine Intelligence, 4(6):521-532, 2022.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Language models enable zero-shot prediction of the effects of mutations on protein function", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Verkuil", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Rives", "suffix": ""}], "year": 2021, "venue": "Advances in neural information processing systems", "volume": "34", "issue": "", "pages": "29287--29303", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Language models enable zero-shot prediction of the effects of mutations on protein function. Advances in neural information processing systems, 34:29287-29303, 2021.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Learning protein fitness models from evolutionary and assay-labeled data", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Fannjiang", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Listgarten", "suffix": ""}], "year": 2022, "venue": "Nature biotechnology", "volume": "40", "issue": "7", "pages": "1114--1122", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, and <PERSON>. Learning protein fitness models from evolutionary and assay-labeled data. Nature biotechnology, 40(7):1114-1122, 2022.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Contrastive fitness learning: Reprogramming protein language models for low-n learning of protein fitness landscape", "authors": [{"first": "Junming", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "International Conference on Research in Computational Molecular Biology", "volume": "", "issue": "", "pages": "470--474", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Contrastive fitness learning: Reprogramming protein language models for low-n learning of protein fitness landscape. In International Conference on Research in Computational Molecular Biology, pages 470-474. Springer, 2024.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Attention is all you need", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "Llion", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "Illia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Chain-of-thought prompting elicits reasoning in large language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ma<PERSON>n", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ed", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Le", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in neural information processing systems", "volume": "35", "issue": "", "pages": "24824--24837", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Chain-of-thought prompting elicits reasoning in large language models. Advances in neural information processing systems, 35:24824-24837, 2022.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Llama 2: Open foundation and fine-tuned chat models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Louis", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stone", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Batra", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Shruti", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09288"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Llama 2: Open foundation and fine-tuned chat models. arXiv preprint arXiv:2307.09288, 2023.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Mistral 7b", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Bamford", "suffix": ""}, {"first": "Diego", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "De Las Casas", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Lucile", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.06825"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, et al. Mistral 7b. arXiv preprint arXiv:2310.06825, 2023.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "<PERSON><PERSON><PERSON>, et al. Gpt-4 technical report", "authors": [{"first": "<PERSON>", "middle": [], "last": "Achiam", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Adler", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ilge", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Florencia", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Almeida", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Altenschmidt", "suffix": ""}, {"first": "Sam", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.08774"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, et al. Gpt-4 technical report. arXiv preprint arXiv:2303.08774, 2023.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Deepseek llm: Scaling open-source language models with longtermism", "authors": [{"first": "<PERSON>", "middle": [], "last": "Bi", "suffix": ""}, {"first": "Deli", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Guanting", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shanhuang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Damai", "middle": [], "last": "Dai", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Honghui", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2401.02954"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. Deepseek llm: Scaling open-source language models with longtermism. arXiv preprint arXiv:2401.02954, 2024.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Proteinbert: a universal deep-learning model of protein sequence and function", "authors": [{"first": "Nadav", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ofer", "suffix": ""}, {"first": "Yam", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Nadav", "middle": [], "last": "Rappoport", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Bioinformatics", "volume": "38", "issue": "8", "pages": "2102--2110", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Proteinbert: a universal deep-learning model of protein sequence and function. Bioinformatics, 38(8): 2102-2110, 2022.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Biological structure and function emerge from scaling unsupervised learning to 250 million protein sequences", "authors": [{"first": "<PERSON>", "middle": [], "last": "Rives", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ma", "suffix": ""}], "year": 2021, "venue": "Proceedings of the National Academy of Sciences", "volume": "118", "issue": "15", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> <PERSON>, <PERSON>, et al. Biological structure and function emerge from scaling unsupervised learning to 250 million protein sequences. Proceedings of the National Academy of Sciences, 118(15):e2016239118, 2021.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Simulating 500 million years of evolution with a language model", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Halil", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Oktay", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Verkuil", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "bioRxiv", "volume": "", "issue": "", "pages": "2024--2027", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Simulating 500 million years of evolution with a language model. bioRxiv, pages 2024-07, 2024.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Bert: Pre-training of deep bidirectional transformers for language understanding", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 2019 Conference of the North American Chapter", "volume": "1", "issue": "", "pages": "4171--4186", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. <PERSON>: Pre-training of deep bidirectional transformers for language understanding. In Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long and Short Papers), pages 4171-4186, 2019.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Improving language understanding by generative pre-training", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, et al. Improving language understanding by generative pre-training. 2018.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Learning functional properties of proteins with language models", "authors": [{"first": "Serbulent", "middle": [], "last": "Unsal", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Atas", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Albayrak", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Aybar C Acar", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Nature Machine Intelligence", "volume": "4", "issue": "3", "pages": "227--245", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Learning functional properties of proteins with language models. Nature Machine Intelligence, 4(3):227-245, 2022.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Improving protein-protein interaction prediction using protein language model and protein network features", "authors": [{"first": "Jun", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Analytical Biochemistry", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Improving protein-protein interaction prediction using protein language model and protein network features. Analytical Biochemistry, page 115550, 2024.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Protst: Multi-modality learning of protein sequences and biomedical texts", "authors": [{"first": "Ming<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xinyu", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "Santiago", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "38749--38767", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Protst: Multi-modality learning of protein sequences and biomedical texts. In International Conference on Machine Learning, pages 38749-38767. PMLR, 2023.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Toward unified ai drug discovery with multimodal knowledge", "authors": [{"first": "Yizhen", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Xi<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zaiqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Health Data Science", "volume": "4", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Toward unified ai drug discovery with multimodal knowledge. Health Data Science, 4:0113, 2024.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Protllm: An interleaved protein-language llm with protein-as-word pre-training", "authors": [{"first": "Le", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ming<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2403.07920"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Protllm: An interleaved protein-language llm with protein-as-word pre-training. arXiv preprint arXiv:2403.07920, 2024.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Prollama: A protein large language model for multi-task protein language processing", "authors": [{"first": "Liuzhen<PERSON>ao", "middle": [], "last": "Lv", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "Li", "suffix": ""}, {"first": "Yuyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jiaxi", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Li", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "Yong<PERSON>", "middle": [], "last": "Tian", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2402.16445"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Prollama: A protein large language model for multi-task protein language processing. arXiv preprint arXiv:2402.16445, 2024.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Multi-modal clip-informed protein editing", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hanjing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yi<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hongxia", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Jintai", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "bioRxiv", "volume": "", "issue": "", "pages": "2024--2027", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. Multi-modal clip-informed protein editing. bioRxiv, pages 2024-07, 2024.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Exploring protein fitness landscapes by directed evolution", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2009, "venue": "Nature reviews Molecular cell biology", "volume": "10", "issue": "12", "pages": "866--876", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Exploring protein fitness landscapes by directed evolution. Nature reviews Molecular cell biology, 10(12):866-876, 2009.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Mutation effects predicted from sequence co-variation", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["Pi"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Schärfe", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Springer", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["S"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Marks", "suffix": ""}], "year": 2017, "venue": "Nature biotechnology", "volume": "35", "issue": "2", "pages": "128--135", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Mutation effects predicted from sequence co-variation. Nature biotechnology, 35(2):128-135, 2017.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Gemme: a simple and fast global epistatic model predicting mutational effects", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Carbone", "suffix": ""}], "year": 2019, "venue": "Molecular biology and evolution", "volume": "36", "issue": "11", "pages": "2604--2619", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Gemme: a simple and fast global epistatic model predicting mutational effects. Molecular biology and evolution, 36(11):2604- 2619, 2019.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Multiple sequence alignment with clustal x", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}], "year": 1998, "venue": "Trends in biochemical sciences", "volume": "23", "issue": "10", "pages": "403--405", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Multiple sequence alignment with clustal x. Trends in biochemical sciences, 23(10): 403-405, 1998.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Robust deep learning-based protein sequence design using proteinmpnn", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Ragotte", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["F"], "last": "Milles", "suffix": ""}, {"first": "I", "middle": ["M"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>y", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Bethel", "suffix": ""}], "year": 2022, "venue": "Science", "volume": "378", "issue": "6615", "pages": "49--56", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Robust deep learning-based protein sequence design using proteinmpnn. Science, 378(6615):49-56, 2022.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Msa transformer", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Verkuil", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Rives", "suffix": ""}], "year": 2021, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "8844--8856", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Msa transformer. In International Conference on Machine Learning, pages 8844-8856. PMLR, 2021.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Trancepteve: Combining family-specific and family-agnostic models of protein sequences for improved fitness prediction", "authors": [{"first": "<PERSON>", "middle": [], "last": "Notin", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gal", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["<PERSON>"], "last": "Marks", "suffix": ""}], "year": 2022, "venue": "NeurIPS 2022 Workshop on Learning Meaningful Representations of Life", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Trancepteve: Combining family-specific and family-agnostic models of protein sequences for improved fitness prediction. In NeurIPS 2022 Workshop on Learning Meaningful Representations of Life, 2022.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Deep mutational scanning: a new style of protein science", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Fields", "suffix": ""}], "year": 2014, "venue": "Nature methods", "volume": "11", "issue": "8", "pages": "801--807", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Deep mutational scanning: a new style of protein science. Nature methods, 11(8):801-807, 2014.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "<PERSON><PERSON><PERSON> at five years: Delivering on the promise", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["L"], "last": "Landrum", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Human mutation", "volume": "39", "issue": "11", "pages": "1623--1630", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. <PERSON><PERSON><PERSON> at five years: Delivering on the promise. Human mutation, 39(11):1623-1630, 2018.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Conditioning by adaptive sampling for robust design", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Hahnbeom", "middle": [], "last": "Park", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Listgarten", "suffix": ""}], "year": 2019, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "773--782", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Conditioning by adaptive sampling for robust design. In International conference on machine learning, pages 773-782. PMLR, 2019.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Accelerating bayesian optimization for biological sequence design with denoising autoencoders", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Nate", "middle": [], "last": "Gruver", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Greenside", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "20459--20478", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Accelerating bayesian optimization for biological se- quence design with denoising autoencoders. In International Conference on Machine Learning, pages 20459-20478. PMLR, 2022.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Protein design with guided discrete diffusion", "authors": [{"first": "Nate", "middle": [], "last": "Gruver", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Gj"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Lafrance-<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Cho", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Protein design with guided discrete diffusion. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "Adalead: A simple and robust adaptive greedy search algorithm for sequence design", "authors": [{"first": "Sam", "middle": [], "last": "Sinai", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Locane", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.02141"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Adalead: A simple and robust adaptive greedy search algorithm for sequence design. arXiv preprint arXiv:2010.02141, 2020.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "Model-based reinforcement learning for biological sequence design", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "International conference on learning representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Model-based reinforcement learning for biological sequence design. In International conference on learning representations, 2019.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Optimizing protein fitness using gibbs sampling with graph-based smoothing", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["S"], "last": "Jaakkola", "suffix": ""}, {"first": "Regina", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Il<PERSON>", "middle": ["R"], "last": "Fiete", "suffix": ""}], "year": 2023, "venue": "ICML 2023 Workshop: Sampling and Optimization in Discrete Space", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Optimizing protein fitness using gibbs sampling with graph-based smoothing. In ICML 2023 Workshop: Sampling and Optimization in Discrete Space, 2023.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Biomedgpt: Open multimodal generative pre-trained transformer for biomedicine", "authors": [{"first": "Yizhen", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Fan", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Mu", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Zaiqing", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.09442"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Biomedgpt: Open multimodal generative pre-trained transformer for biomedicine. arXiv preprint arXiv:2308.09442, 2023.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Continual pre-training of language models", "authors": [{"first": "Zixuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Gyuhak", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Continual pre-training of language models. In The Eleventh International Conference on Learning Representations, 2022.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Blip: Bootstrapping languageimage pre-training for unified vision-language understanding and generation", "authors": [{"first": "Jun<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Dongxu", "middle": [], "last": "Li", "suffix": ""}, {"first": "Caiming", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hoi", "suffix": ""}], "year": 2022, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "12888--12900", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Blip: Bootstrapping language- image pre-training for unified vision-language understanding and generation. In International conference on machine learning, pages 12888-12900. PMLR, 2022.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "Blip-2: Bootstrapping language-image pre-training with frozen image encoders and large language models", "authors": [{"first": "Jun<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Dongxu", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hoi", "suffix": ""}], "year": 2023, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "19730--19742", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Blip-2: Bootstrapping language-image pre-training with frozen image encoders and large language models. In International conference on machine learning, pages 19730-19742. PMLR, 2023.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "Structureinformed language models are protein designers", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ye", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>u", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "42317--42338", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Structure- informed language models are protein designers. In International Conference on Machine Learning, pages 42317-42338. PMLR, 2023.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "Mol-instructions-a large-scale biomolecular instruction dataset for large language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiaozhuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>ng<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Kangwei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Fan", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Mol-instructions-a large-scale biomolecular instruction dataset for large language models. In The Twelfth International Conference on Learning Representations, 2023.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "Mask-predict: Parallel decoding of conditional masked language models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Ghaz<PERSON>inejad", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1904.09324"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Mask-predict: Parallel decoding of conditional masked language models. arXiv preprint arXiv:1904.09324, 2019.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "Uniprotkb/swiss-prot, the manually annotated section of the uniprot knowledgebase: how to use the entry view", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Bansal", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Bridge", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Bougueleret", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Xenarios", "suffix": ""}], "year": 2016, "venue": "Plant bioinformatics: methods and protocols", "volume": "", "issue": "", "pages": "23--54", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Uniprotkb/swiss-prot, the manually annotated section of the uniprot knowledgebase: how to use the entry view. Plant bioinformatics: methods and protocols, pages 23-54, 2016.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "Pubmed: the bibliographic database", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "The NCBI handbook", "volume": "2", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Pubmed: the bibliographic database. The NCBI handbook, 2(1), 2013.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "Mmseqs2 enables sensitive protein sequence searching for the analysis of massive data sets", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Nature biotechnology", "volume": "35", "issue": "11", "pages": "1026--1028", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Mmseqs2 enables sensitive protein sequence searching for the analysis of massive data sets. Nature biotechnology, 35(11):1026-1028, 2017.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "Deep generative models of genetic variation capture the effects of mutations", "authors": [{"first": "<PERSON>", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Marks", "suffix": ""}], "year": 2018, "venue": "Nature methods", "volume": "15", "issue": "10", "pages": "816--822", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Deep generative models of genetic variation capture the effects of mutations. Nature methods, 15(10):816-822, 2018.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "Proteingym: largescale benchmarks for protein fitness prediction and design", "authors": [{"first": "<PERSON>", "middle": [], "last": "Notin", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Han", "middle": [], "last": "Spinner", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ada", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Orenbuch", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, et al. Proteingym: large- scale benchmarks for protein fitness prediction and design. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "Galactica: A large language model for science", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Guillem", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sc<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Saravia", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.09085"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Galactica: A large language model for science. arXiv preprint arXiv:2211.09085, 2022.", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "Ontoprotein: Protein pretraining with gene ontology embedding", "authors": [{"first": "<PERSON>ng<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Bi", "suffix": ""}, {"first": "Xiaozhuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Siyuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jiaz<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Ontoprotein: Protein pretraining with gene ontology embedding. In International Conference on Learning Representations, 2021.", "links": null}, "BIBREF75": {"ref_id": "b75", "title": "An empirical study of catastrophic forgetting in large language models during continual fine-tuning", "authors": [{"first": "Yun", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Fandong", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.08747"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. An empirical study of catastrophic forgetting in large language models during continual fine-tuning. arXiv preprint arXiv:2308.08747, 2023.", "links": null}, "BIBREF76": {"ref_id": "b76", "title": "Lora: Low-rank adaptation of large language models", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuanzhi", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weizhu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, et al. Lora: Low-rank adaptation of large language models. In International Conference on Learning Representations, 2021.", "links": null}, "BIBREF77": {"ref_id": "b77", "title": "Decoupled weight decay regularization", "authors": [{"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Decoupled weight decay regularization. In International Conference on Learning Representations, 2018.", "links": null}, "BIBREF78": {"ref_id": "b78", "title": "A survey on in-context learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Damai", "middle": [], "last": "Dai", "suffix": ""}, {"first": "Ce", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Baobao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Jingjing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2301.00234"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. A survey on in-context learning. arXiv preprint arXiv:2301.00234, 2022.", "links": null}, "BIBREF79": {"ref_id": "b79", "title": "Microsoft Research AI4Science and Microsoft Azure Quantum. The impact of large language models on scientific discovery: a preliminary study using gpt-4", "authors": [], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.07361"]}, "num": null, "urls": [], "raw_text": "Microsoft Research AI4Science and Microsoft Azure Quantum. The impact of large lan- guage models on scientific discovery: a preliminary study using gpt-4. arXiv preprint arXiv:2311.07361, 2023.", "links": null}, "BIBREF80": {"ref_id": "b80", "title": "Bleu: a method for automatic evaluation of machine translation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ward", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2002, "venue": "Proceedings of the 40th annual meeting of the Association for Computational Linguistics", "volume": "", "issue": "", "pages": "311--318", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Bleu: a method for automatic evaluation of machine translation. In Proceedings of the 40th annual meeting of the Association for Computational Linguistics, pages 311-318, 2002.", "links": null}, "BIBREF81": {"ref_id": "b81", "title": "Rouge: A package for automatic evaluation of summaries", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2004, "venue": "Text summarization branches out", "volume": "", "issue": "", "pages": "74--81", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>. Rouge: A package for automatic evaluation of summaries. In Text summarization branches out, pages 74-81, 2004.", "links": null}, "BIBREF82": {"ref_id": "b82", "title": "Domain-specific language model pretraining for biomedical natural language processing", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Xiaodong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ji<PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Hoifung", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ACM Transactions on Computing for Healthcare (HEALTH)", "volume": "3", "issue": "1", "pages": "1--23", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Domain-specific language model pretraining for biomedical natural language processing. ACM Transactions on Computing for Healthcare (HEALTH), 3(1):1-23, 2021.", "links": null}, "BIBREF83": {"ref_id": "b83", "title": "A survey of deep active learning", "authors": [{"first": "Pengzhen", "middle": [], "last": "Ren", "suffix": ""}, {"first": "Yun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Po-Yao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Xiaojiang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "ACM computing surveys (CSUR)", "volume": "54", "issue": "9", "pages": "1--40", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. A survey of deep active learning. ACM computing surveys (CSUR), 54 (9):1-40, 2021.", "links": null}, "BIBREF84": {"ref_id": "b84", "title": "Beam search strategies for neural machine translation", "authors": [{"first": "<PERSON>", "middle": [], "last": "Freitag", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Al-Onaizan", "suffix": ""}], "year": 2017, "venue": "Proceedings of the First Workshop on Neural Machine Translation", "volume": "", "issue": "", "pages": "56--60", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. Beam search strategies for neural machine translation. In Proceedings of the First Workshop on Neural Machine Translation, pages 56-60, 2017.", "links": null}, "BIBREF85": {"ref_id": "b85", "title": "Proximal exploration for model-guided protein sequence design", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Zhizhou Ren", "suffix": ""}, {"first": "Fan", "middle": [], "last": "Li", "suffix": ""}, {"first": "Yuan", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "18520--18536", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Proximal exploration for model-guided protein sequence design. In International Conference on Machine Learning, pages 18520-18536. PMLR, 2022.", "links": null}, "BIBREF86": {"ref_id": "b86", "title": "Plug & play directed evolution of proteins with gradient-based discrete mcmc", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Law", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Biagioni", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "St", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2023, "venue": "Machine Learning: Science and Technology", "volume": "4", "issue": "2", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Plug & play directed evolution of proteins with gradient-based discrete mcmc. Machine Learning: Science and Technology, 4(2):025014, 2023.", "links": null}, "BIBREF87": {"ref_id": "b87", "title": "Accurate structure prediction of biomolecular interactions with alphafold 3", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Adler", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Green", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>more", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Nature", "volume": "", "issue": "", "pages": "1--3", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Accurate structure prediction of biomolecular interactions with alphafold 3. Nature, pages 1-3, 2024.", "links": null}, "BIBREF88": {"ref_id": "b88", "title": "Minigpt-4: Enhancing vision-language understanding with advanced large language models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiang", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.10592"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Minigpt-4: En- hancing vision-language understanding with advanced large language models. arXiv preprint arXiv:2304.10592, 2023.", "links": null}, "BIBREF89": {"ref_id": "b89", "title": "Learning transferable visual models from natural language supervision", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Wook"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hall<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "8748--8763", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Learning transferable visual models from natural language supervision. In International conference on machine learning, pages 8748-8763. PMLR, 2021.", "links": null}, "BIBREF90": {"ref_id": "b90", "title": "Deep diversification of an aav capsid protein by machine learning", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "Bashir", "suffix": ""}, {"first": "<PERSON>", "middle": ["K"], "last": "Sinai", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["F"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Church", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature Biotechnology", "volume": "39", "issue": "6", "pages": "691--696", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Deep diversification of an aav capsid protein by machine learning. Nature Biotechnology, 39(6):691-696, 2021.", "links": null}, "BIBREF91": {"ref_id": "b91", "title": "Single-mutation fitness landscapes for an enzyme on multiple substrates reveal specificity is globally encoded", "authors": [{"first": "<PERSON>", "middle": ["E"], "last": "Wrenbeck", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "Azouz", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "Nature communications", "volume": "8", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Single-mutation fitness landscapes for an enzyme on multiple substrates reveal specificity is globally encoded. Nature communications, 8(1):15695, 2017.", "links": null}, "BIBREF92": {"ref_id": "b92", "title": "Local fitness landscape of the green fluorescent protein", "authors": [{"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["V"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["V"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Dmitry", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Soylemez", "suffix": ""}], "year": 2016, "venue": "Nature", "volume": "533", "issue": "7603", "pages": "397--401", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, et al. Local fitness landscape of the green fluorescent protein. Nature, 533 (7603):397-401, 2016.", "links": null}, "BIBREF93": {"ref_id": "b93", "title": "Activity-enhancing mutations in an e3 ubiquitin ligase identified by high-throughput mutagenesis", "authors": [{"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "Lo", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Shendure", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "Fields", "suffix": ""}, {"first": "", "middle": [], "last": "K<PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "Proceedings of the National Academy of Sciences", "volume": "110", "issue": "14", "pages": "1263--E1272", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Activity-enhancing mutations in an e3 ubiquitin ligase identified by high-throughput mutagenesis. Proceedings of the National Academy of Sciences, 110(14):E1263-E1272, 2013.", "links": null}, "BIBREF94": {"ref_id": "b94", "title": "Comprehensive sequence-flux mapping of a levoglucosan utilization pathway in e. coli", "authors": [{"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Bacik", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}], "year": 2015, "venue": "ACS synthetic biology", "volume": "4", "issue": "11", "pages": "1235--1243", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Compre- hensive sequence-flux mapping of a levoglucosan utilization pathway in e. coli. ACS synthetic biology, 4(11):1235-1243, 2015.", "links": null}, "BIBREF95": {"ref_id": "b95", "title": "A framework for exhaustively mapping functional missense variants", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Song", "middle": [], "last": "Sun", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Cote", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "Ver<PERSON>", "suffix": ""}, {"first": "Yingzhou", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Pons", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "Molecular systems biology", "volume": "13", "issue": "12", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, et al. A framework for exhaustively mapping functional missense variants. Molecular systems biology, 13(12):957, 2017.", "links": null}, "BIBREF96": {"ref_id": "b96", "title": "Shifting mutational constraints in the sars-cov-2 receptor-binding domain during viral evolution", "authors": [{"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "Hannon", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "Hauser", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["Ghez"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Bernadeta", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Dadonaite", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Science", "volume": "377", "issue": "6604", "pages": "420--424", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Shifting mutational constraints in the sars-cov-2 receptor-binding domain during viral evolution. Science, 377(6604):420-424, 2022.", "links": null}, "BIBREF97": {"ref_id": "b97", "title": "Low-n protein engineering with data-efficient deep learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "Alley", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "Esvelt", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "Church", "suffix": ""}], "year": 2021, "venue": "Nature methods", "volume": "18", "issue": "4", "pages": "389--396", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Low-n protein engineering with data-efficient deep learning. Nature methods, 18(4):389-396, 2021.", "links": null}, "BIBREF98": {"ref_id": "b98", "title": "Tranception: protein fitness prediction with autoregressive transformers and inference-time retrieval", "authors": [{"first": "<PERSON>", "middle": [], "last": "Notin", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Marchena-Hurtado", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Marks", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gal", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "16990--17017", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Tranception: protein fitness prediction with autoregressive transformers and inference-time retrieval. In International Conference on Machine Learning, pages 16990-17017. PMLR, 2022.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "fig_num": "1", "num": null, "text": "Figure 1: Model architecture of MutaPLM. (a) The encoding branch of the protein delta network. The delta encoder takes the subtraction of the PLM representations of the mutant and wild-type as inputs to generate z∆. (b) The decoding branch of the protein delta network. The key components involve a delta decoder that reconstructs mutant features and two prediction heads deciding the position and amino acid of the mutation.", "uris": null}, "FIGREF1": {"type_str": "figure", "fig_num": "2", "num": null, "text": "Figure 2: Training pipeline of MutaPLM. (a) Workflow of pre-training on protein-related literature.We perform next token prediction for the encoding workflow and conditional masked language modeling for the decoding workflow. (b) Workflow of fine-tuning with chain-of-thought (CoT). We employ a two-round dialog that involves describing the functions of a wild-type protein, explaining the effects of its mutation, and predicting the mutation based on the mutational effects.", "uris": null}, "FIGREF2": {"type_str": "figure", "fig_num": "3", "num": null, "text": "Figure 3: Human-AI collaborative evaluation results for mutation explanation on the test sets of MutaDescribe. We show the number of accurate, relevant, opposite, and irrelevant predictions.", "uris": null}, "FIGREF3": {"type_str": "figure", "fig_num": "4", "num": null, "text": "Figure 4: Case study for a mutation from A (Alanine) to D (Aspartic) at the 205-th position of m7GpppX diphosphatase. MutaPLM provides accurate explanations and insights, while GPT-4 generates irrelevant results.", "uris": null}, "FIGREF4": {"type_str": "figure", "fig_num": "5", "num": null, "text": "Figure 5: Visualization of fitness scores for multi-round protein optimization. The curves indicate the average results, and the shaded regions indicate the standard deviation.", "uris": null}, "FIGREF5": {"type_str": "figure", "fig_num": "6", "num": null, "text": "Performance analysis for mutation explanation (blue) and engineering (orange) on pre-training and finetuning. w/o pt: without pre-training. w/ pt: with pre-training.", "uris": null}, "FIGREF6": {"type_str": "figure", "fig_num": null, "num": null, "text": "Figure A1: Detailed statistics of the MutaDescribe dataset. We show (a) the length of protein sequences, (b) the number of words in textual annotations, (c) the number of mutation samples per protein, (d) the distribution of the originating species, (e) the distribution of the cellular localization and (f) the distribution of the mutated amino acid.", "uris": null}, "FIGREF7": {"type_str": "figure", "fig_num": null, "num": null, "text": "Figure A2: Confusion matrix between GPT-4 and manual evaluation.", "uris": null}, "FIGREF8": {"type_str": "figure", "fig_num": null, "num": null, "text": "Figure A3: More case studies at mutation explanation. We report the outputs of MutaPLM and GPT-4 (5-shot, kNN).", "uris": null}, "TABREF0": {"type_str": "table", "num": null, "text": "Statistics of the MutaDescribe dataset. We report the number of proteins and samples, the average protein sequence length, and the average number of words for mutational effects.", "html": null, "content": "<table><tr><td>Split</td><td colspan=\"4\"># Proteins # Samples Avg. sequence length Avg. words</td></tr><tr><td>Train</td><td>20,553</td><td>165,236</td><td>516.1</td><td>28.3</td></tr><tr><td>Valid</td><td>2,207</td><td>4,663</td><td>524.8</td><td>28.3</td></tr><tr><td>Test (Easy)</td><td>429</td><td>460</td><td>518.1</td><td>27.3</td></tr><tr><td>Test (Medium)</td><td>68</td><td>384</td><td>669.6</td><td>31.6</td></tr><tr><td>Test (Hard)</td><td>81</td><td>404</td><td>530.0</td><td>31.8</td></tr><tr><td colspan=\"5\">3.3 MutaDescribe: A Diverse Protein Mutation Dataset with Textual Annotations</td></tr></table>"}, "TABREF1": {"type_str": "table", "num": null, "text": "Performance evaluation for mutation explanation on the test sets of MutaDescribe. R-L: ROUGE-L. OntoProtein[75] 11.84 10.93 12.69 11.22 12.81 8.17 12.42 10.13 AugmentedESM [27] 11.60 8.33 11.40 7.46 10.73 6.95 11.26 7.62 Fine-tuned ESM-2 [19] 20.49 9.37 11.87 5.95 11.34 3.32 14.88 6.36 MutaPLM 25.80 18.77 21.07 12.59 16.51 8.69 21.34 13.61", "html": null, "content": "<table><tr><td>BL-2: BLEU-2.</td><td/></tr><tr><td>Model</td><td>Easy R-L BL-2 R-L BL-2 R-L BL-2 R-L BL-2 Medium Hard Average</td></tr><tr><td>ProLLaMA [45]</td><td>1.02 0.64 1.00 0.91 1.03 0.70 1.02 0.74</td></tr><tr><td>Mol-Instructions [67]</td><td>5.10 0.65 5.19 0.65 5.56 0.90 5.27 0.73</td></tr><tr><td>Galactica-6.7B [74]</td><td>6.53 3.52 7.64 3.58 7.33 2.88 7.13 3.33</td></tr><tr><td>GPT-4-0613 (1-shot) [33]</td><td>8.04 2.93 9.96 3.42 9.62 2.69 9.14 3.00</td></tr><tr><td>GPT-4-0613 (5-shot) [33]</td><td>10.46 2.51 10.31 2.81 10.79 1.88 10.52 2.40</td></tr><tr><td colspan=\"2\">GPT-4-0613 (5-shot, kNN) [33] 11.63 9.63 12.98 10.88 12.46 8.63 12.31 9.69</td></tr><tr><td>GPT-4 + ESM-2 [19]</td><td>11.69 11.09 13.02 11.50 12.77 8.48 12.45 10.37</td></tr><tr><td>GPT-4 +</td><td/></tr></table>"}, "TABREF3": {"type_str": "table", "num": null, "text": "Performance evaluation for mutation engineering on the test sets of MutaDescribe. Acc: prediction accuracy of the amino acid given the position of the mutation. Rec@50: top 50 recall of the desired mutant. -: not reported due to unaffordable computation costs (requires ∼ 1M forward passes).", "html": null, "content": "<table><tr><td>Model</td><td colspan=\"8\">Easy Acc Rec@50 Acc Rec@50 Acc Rec@50 Acc Rec@50 Medium Hard Average</td></tr><tr><td>Random</td><td>5.23</td><td>0.83</td><td>4.94</td><td>0.52</td><td>5.20</td><td>1.24</td><td>5.13</td><td>0.87</td></tr><tr><td>ProtST (ESM-2) [42]</td><td>5.86</td><td>-</td><td>6.51</td><td>-</td><td>7.18</td><td>-</td><td>6.49</td><td>-</td></tr><tr><td>GPT-4-0613 (1-shot) [33]</td><td colspan=\"8\">10.83 5.00 10.77 6.92 12.09 8.79 11.21 6.81</td></tr><tr><td>GPT-4-0613 (5-shot) [33]</td><td colspan=\"2\">14.84 4.68</td><td>9.32</td><td colspan=\"5\">6.78 13.33 5.62 12.65 5.63</td></tr><tr><td colspan=\"9\">GPT-4-0613 (5-shot, kNN) [33] 15.97 7.56 14.29 7.14 14.77 7.95 15.06 7.56</td></tr><tr><td>ESM-2 [19]</td><td colspan=\"8\">35.21 23.91 34.63 22.91 37.87 28.71 35.84 25.15</td></tr><tr><td>OntoProtein [75]</td><td colspan=\"8\">39.78 28.91 36.45 26.04 38.61 29.20 38.37 28.12</td></tr><tr><td>Fine-tuned BioMedGPT [62]</td><td colspan=\"8\">35.21 7.82 32.29 5.72 39.60 12.62 35.73 8.72</td></tr><tr><td>Fine-tuned ESM-2 [19, 83]</td><td colspan=\"8\">52.17 35.65 52.08 30.60 50.00 34.65 51.43 33.77</td></tr><tr><td>MutaPLM</td><td colspan=\"8\">56.08 43.47 48.69 34.89 55.19 43.81 53.51 40.94</td></tr></table>"}, "TABREF5": {"type_str": "table", "num": null, "text": "Ablation studies. w/o: without. w/: with. We report average ROUGE-L for mutation explanation and average Recall@50 for mutation engineering.", "html": null, "content": "<table><tr><td>Model</td><td colspan=\"2\">Explain Engineer</td></tr><tr><td>MutaPLM</td><td>21.34</td><td>40.94</td></tr><tr><td>w/ golden function</td><td>23.80</td><td>41.26</td></tr><tr><td>w/o function</td><td>18.54</td><td>39.81</td></tr><tr><td>w/o delta features</td><td>17.36</td><td>-</td></tr><tr><td>w/o mutational effects</td><td>-</td><td>35.10</td></tr></table>"}, "TABREF6": {"type_str": "table", "num": null, "text": "Average l 2 -norm of MutaPLM's intermediate representations on MutaDescribe.", "html": null, "content": "<table><tr><td colspan=\"2\">Representation h wt</td><td>h ∆</td><td>z ∆</td></tr><tr><td>Avg. l 2 -norm</td><td colspan=\"3\">9.90 0.35 1.04</td></tr></table>"}, "TABREF7": {"type_str": "table", "num": null, "text": "An Overview of MutaDescribe.", "html": null, "content": "<table><tr><td># All</td><td># Raw</td><td># Enriched</td><td># Reversed</td></tr><tr><td>171,147</td><td>106,645</td><td>57,147</td><td>64,502</td></tr><tr><td colspan=\"2\">Avg. words (UniProtKB)</td><td colspan=\"2\">Avg. words (Enriched)</td></tr><tr><td>9.44</td><td/><td>28.33</td><td/></tr><tr><td colspan=\"4\"># Malignant # Benign # Not significant # Unknown</td></tr><tr><td>72,198</td><td>8,000</td><td>26,447</td><td>4</td></tr></table>"}, "TABREF8": {"type_str": "table", "num": null, "text": "Statistics of the temporal split. We report the number of proteins and samples, the average protein sequence length, and the average number of words for mutational effects.", "html": null, "content": "<table><tr><td colspan=\"5\">Split # Proteins # Samples Avg. sequence length Avg. words</td></tr><tr><td>Train</td><td>20,295</td><td>156,300</td><td>518.00</td><td>28.48</td></tr><tr><td>Valid</td><td>5,436</td><td>8,000</td><td>514.30</td><td>28.73</td></tr><tr><td>Test</td><td>310</td><td>1,611</td><td>536.67</td><td>26.37</td></tr></table>"}, "TABREF9": {"type_str": "table", "num": null, "text": "Prompts for navigating mutation engineering.", "html": null, "content": "<table/>"}, "TABREF10": {"type_str": "table", "num": null, "text": "Algorithm 1 Multi-round Optimization with Beam Search Require: Wild-type Sequence x wt , Instruction t, Number of Rounds N , Number of Candidates K C ← {x wt } for Round = 1, 2, • • • , N do for x ∈ C do h ← f PLM (x) h ← h + Decoder(h, T ) ▷ Add mutational features Score pos , Score aa ← f pos (h), f LM (h)", "html": null, "content": "<table/>"}, "TABREF11": {"type_str": "table", "num": null, "text": "Performance evaluation for mutation explanation on temporal split.", "html": null, "content": "<table><tr><td>Model</td><td colspan=\"6\">BLEU-2 BLEU-4 METEOR ROUGE-1 ROUGE-2 ROUGE-L</td></tr><tr><td>ProLLaMA [45]</td><td>0.69</td><td>0.21</td><td>3.33</td><td>0.83</td><td>0.04</td><td>0.80</td></tr><tr><td>Galactica-6.7B [74]</td><td>3.50</td><td>1.31</td><td>5.61</td><td>7.44</td><td>0.85</td><td>6.17</td></tr><tr><td>Mol-Instructions [67]</td><td>0.58</td><td>0.08</td><td>4.90</td><td>5.41</td><td>0.13</td><td>4.55</td></tr><tr><td colspan=\"2\">GPT-4-0613 (5-shot, kNN) [33] 9.30</td><td>4.25</td><td>15.08</td><td>13.92</td><td>2.29</td><td>11.84</td></tr><tr><td>AugmentedESM [27]</td><td>7.00</td><td>3.12</td><td>11.29</td><td>12.03</td><td>2.84</td><td>10.12</td></tr><tr><td>Fine-tuned ESM-2 [19]</td><td>6.90</td><td>3.83</td><td>13.86</td><td>14.21</td><td>4.63</td><td>12.62</td></tr><tr><td>MutaPLM</td><td>10.83</td><td>6.15</td><td>17.84</td><td>18.99</td><td>6.92</td><td>16.51</td></tr></table>"}, "TABREF12": {"type_str": "table", "num": null, "text": "Performance evaluation for mutation engineering on temporal split.", "html": null, "content": "<table><tr><td>Model</td><td colspan=\"2\">Accuracy (%) Recall@50 (%)</td></tr><tr><td>Random</td><td>4.40</td><td>0.81</td></tr><tr><td>ProtST (ESM-2) [42]</td><td>5.11</td><td>-</td></tr><tr><td>GPT-4-0613 (5-shot, kNN) [33]</td><td>12.13</td><td>6.28</td></tr><tr><td>ESM-2 [19]</td><td>34.76</td><td>24.02</td></tr><tr><td>OntoProtein [75]</td><td>37.74</td><td>28.49</td></tr><tr><td>Fine-tuned BioMedGPT [62]</td><td>34.57</td><td>4.09</td></tr><tr><td>Fine-tuned ESM-2 [19, 83]</td><td>55.78</td><td>44.04</td></tr><tr><td>MutaPLM</td><td>58.50</td><td>46.05</td></tr></table>"}, "TABREF13": {"type_str": "table", "num": null, "text": "Performance evaluation on protein fitness regression benchmarks. We perform experiments 5 times with different random seeds and report the <PERSON><PERSON><PERSON> correlation coefficient. The best and second-best results are marked in bold and underlined.", "html": null, "content": "<table><tr><td>Model</td><td>Spike-ACE2</td><td>avGFP</td></tr><tr><td>Ridge Regression</td><td colspan=\"2\">0.335±0.052 0.298±0.071</td></tr><tr><td>ESM-2</td><td/><td/></tr></table>"}, "TABREF14": {"type_str": "table", "num": null, "text": ": [Yes] Justification: We provide our data and code at https://github.com/PharMolix/ MutaPLM. Guidelines: • The answer NA means that paper does not include experiments requiring code. • Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).• Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted. The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors).• It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text. The answer NA means that the paper does not include experiments. • The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "html": null, "content": "<table><tr><td>understand the • The experimental setting should be presented in the core of the paper to a level of detail results? Answer: [Yes] Justification: We present implementation details in Section 4.1. Guidelines: • The answer NA means that the paper does not include experiments. that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material. 7. Experiment Statistical Significance Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments? Answer: [No] Justification: While we report error bars for protein fitness optimization, the majority of experiments do not include error bars because it would be too computationally expensive. Guidelines: • The answer NA means that the paper does not include experiments. • The authors should answer \"Yes\" if the results are accompanied by error bars, confi-dence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. Question: For each experiment, does the paper provide sufficient information on the com-puter resources (type of compute workers, memory, time of execution) needed to reproduce the experiments? Answer: [Yes] Justification: The information is provided in Section 4.1. Guidelines: • 8. Experiments Compute Resources •</td></tr></table>"}, "TABREF15": {"type_str": "table", "num": null, "text": "13. New Assets Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets? Answer: [Yes] Justification: The assets and documentation are at https://github.com/PharMolix/ MutaPLM. Guidelines: • The answer NA means that the paper does not release new assets. • Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • At submission time, remember to anonymize your assets (if applicable). You can either create an anonymized URL or include an anonymized zip file. 14. Crowdsourcing and Research with Human Subjects Question: For crowdsourcing experiments and research with human subjects, does the paper include the full text of instructions given to participants and screenshots, if applicable, as well as details about compensation (if any)? Answer: [Yes] Justification: The instructions to human participants are displayed in Appendix C.3. Guidelines: • The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Including this information in the supplemental material is fine, but if the main contribution of the paper involves human subjects, then as much detail as possible should be included in the main paper. • According to the NeurIPS Code of Ethics, workers involved in data collection, curation, or other labor should be paid at least the minimum wage in the country of the data collector. 15. Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects Question: Does the paper describe potential risks incurred by study participants, whether such risks were disclosed to the subjects, and whether Institutional Review Board (IRB) approvals (or an equivalent approval/review based on the requirements of your country or institution) were obtained? Answer: [NA] Justification: The paper does not involve crowdsourcing nor research with human subjects. Guidelines: • The answer NA means that the paper does not involve crowdsourcing nor research with human subjects. • Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper.", "html": null, "content": "<table/>"}}}}