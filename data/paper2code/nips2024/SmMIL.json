{"paper_id": "SmMIL", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:13:29.570061Z"}, "title": "Sm: enhanced localization in Multiple Instance Learning for medical imaging classification", "authors": [{"first": "Francisco", "middle": ["M"], "last": "Castro-<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Granada", "location": {}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "UGR University of Granada", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Northwestern University", "location": {}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Granada", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["K"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Northwestern University", "location": {}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Multiple Instance Learning (MIL) is widely used in medical imaging classification to reduce the labeling effort. While only bag labels are available for training, one typically seeks predictions at both bag and instance levels (classification and localization tasks, respectively). Early MIL methods treated the instances in a bag independently. Recent methods account for global and local dependencies among instances. Although they have yielded excellent results in classification, their performance in terms of localization is comparatively limited. We argue that these models have been designed to target the classification task, while implications at the instance level have not been deeply investigated. Motivated by a simple observation -that neighboring instances are likely to have the same label -we propose a novel, principled, and flexible mechanism to model local dependencies. It can be used alone or combined with any mechanism to model global dependencies (e.g., transformers). A thorough empirical validation shows that our module leads to state-of-the-art performance in localization while being competitive or superior in classification. Our code is at https://github.com/Franblueee/SmMIL.", "pdf_parse": {"paper_id": "SmMIL", "_pdf_hash": "", "abstract": [{"text": "Multiple Instance Learning (MIL) is widely used in medical imaging classification to reduce the labeling effort. While only bag labels are available for training, one typically seeks predictions at both bag and instance levels (classification and localization tasks, respectively). Early MIL methods treated the instances in a bag independently. Recent methods account for global and local dependencies among instances. Although they have yielded excellent results in classification, their performance in terms of localization is comparatively limited. We argue that these models have been designed to target the classification task, while implications at the instance level have not been deeply investigated. Motivated by a simple observation -that neighboring instances are likely to have the same label -we propose a novel, principled, and flexible mechanism to model local dependencies. It can be used alone or combined with any mechanism to model global dependencies (e.g., transformers). A thorough empirical validation shows that our module leads to state-of-the-art performance in localization while being competitive or superior in classification. Our code is at https://github.com/Franblueee/SmMIL.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Over the last decades, medical imaging classification has benefited from advances in deep learning [35, 44] . However, the performance of these methods drops when the number of labeled samples is low, which is common in real-world medical scenarios [1] . To overcome this, Multiple Instance Learning (MIL) has emerged as a popular weakly supervised approach [14, 8, 12] .", "cite_spans": [{"start": 99, "end": 103, "text": "[35,", "ref_id": "BIBREF34"}, {"start": 104, "end": 107, "text": "44]", "ref_id": "BIBREF43"}, {"start": 249, "end": 252, "text": "[1]", "ref_id": "BIBREF0"}, {"start": 358, "end": 362, "text": "[14,", "ref_id": "BIBREF13"}, {"start": 363, "end": 365, "text": "8,", "ref_id": "BIBREF7"}, {"start": 366, "end": 369, "text": "12]", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In MIL, instances are arranged in bags. At train time, a label is available for the entire bag, while the instance labels remain unknown. The goal is to train a method that, given a test bag, can predict both at bag and instance levels (classification and localization tasks, respectively). This paradigm is well suited to the medical imaging domain [28] . In cancer detection from Whole Slide Images (WSIs), the WSI represents the bag, and the patches are the instances. In intracranial hemorrhage detection from Computerized Tomographic (CT) scans, the full scan represents the bag, and the slices at different heights are the instances. In these scenarios, making accurate predictions of instance labels is extremely important from a clinical viewpoint, as it translates into pinpointing the location of the lesion [7] .", "cite_spans": [{"start": 350, "end": 354, "text": "[28]", "ref_id": "BIBREF27"}, {"start": 818, "end": 821, "text": "[7]", "ref_id": "BIBREF6"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Most successful approaches in MIL build on the attention-based pooling [17] , a permutation-invariant operator that assigns an attention value to each instance independently. This method has been extended in different ways while maintaining the permutation-invariant property [21, 25, 39] . The aforementioned works pose a problem: the dependencies between the instances, which are important when making a diagnosis, are ignored. To account for this, TransMIL [32] proposed to model global dependencies using a transformer encoder. The idea is to use the self-attention mechanism to introduce interactions between each pair of instances. Based on it, other transformer-based approaches have emerged, also focusing on global dependencies [9, 22, 37] . More recently, several works have also incorporated natural local interactions, which are those between neighboring instances [14, 40, 41] .", "cite_spans": [{"start": 71, "end": 75, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 276, "end": 280, "text": "[21,", "ref_id": "BIBREF20"}, {"start": 281, "end": 284, "text": "25,", "ref_id": "BIBREF24"}, {"start": 285, "end": 288, "text": "39]", "ref_id": "BIBREF38"}, {"start": 460, "end": 464, "text": "[32]", "ref_id": "BIBREF31"}, {"start": 737, "end": 740, "text": "[9,", "ref_id": "BIBREF8"}, {"start": 741, "end": 744, "text": "22,", "ref_id": "BIBREF21"}, {"start": 745, "end": 748, "text": "37]", "ref_id": "BIBREF36"}, {"start": 877, "end": 881, "text": "[14,", "ref_id": "BIBREF13"}, {"start": 882, "end": 885, "text": "40,", "ref_id": "BIBREF39"}, {"start": 886, "end": 889, "text": "41]", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Although these methods accounting for dependencies have resulted in excellent performance at the bag level, the evaluation at the instance level has received less attention and the results are not comparatively good so far, see the very recent [14] . In this work, we argue that recent MIL methods have been designed with the classification task in mind, and we propose a new model that focuses on both the classification and localization tasks. Specifically, we propose a novel and theoretically grounded mechanism to introduce local dependencies, hereafter referred to as the smooth operator Sm. This is a flexible module that can be used alone on top of classical MIL approaches, or in combination with transformers to also account for global dependencies. In both cases, we show that the proposed operator achieves state-of-the-art localization results while being competitive in classification. We compare against a total amount of eight methods, including very recent ones [14, 40] . We utilize three different datasets of different nature and size, covering two different medical imaging problems (cancer detection in WSI images and hemorrhage detection in CT scans).", "cite_spans": [{"start": 244, "end": 248, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 979, "end": 983, "text": "[14,", "ref_id": "BIBREF13"}, {"start": 984, "end": 987, "text": "40]", "ref_id": "BIBREF39"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Our main contributions are: (i) we provide a unified view of current deep MIL approaches; (ii) we propose a principled mechanism to introduce local interactions, which is a modular component that can be combined or not with global interactions; and (iii) we evaluate up to eight state-of-the-art MIL methods on three real-world MIL datasets in both classification and localization tasks, showing that the proposed method stands out in localization while being competitive or superior in classification.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "In this work, we tackle the localization task in deep MIL methods using existing concepts and techniques from deep MIL and Graph Neural Networks (GNNs) theory.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2"}, {"text": "Deep Multiple Instance Learning. As explained by <PERSON> et al. [34] , deep MIL methods can be divided into two broad categories, namely instance-based or embedding-based, depending on the level at which a specific aggregation operator is applied. In this paper, we focus on the embedding-based category, and in particular attention-based ones.", "cite_spans": [{"start": 61, "end": 65, "text": "[34]", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2"}, {"text": "<PERSON><PERSON> et al. [17] proposed attention-based pooling to weigh each instance in the bag. To improve it, different modifications were proposed, including the use of clustering layers [25] , grouping the instances in pseudo-bags [39] , and using similarity measures to critical instances to compute the attention values [21] . However, these methods ignore the existing dependencies between the instances in a bag. To address this, <PERSON><PERSON> et al. [32] proposed to use a transformer-based architecture and the PPEG position encoding module. This has been extended with different transformer variations, including the deformable transformer architecture [22] , hierarchical attention [37] , and regional sampling [9] . Recently, these methods have been improved to include spatial information in different ways, including the use of a Graph Convolutional Network (GCN) before the transformer [41] , a neighbor-constrained attention module [14] , and a spatial-encoding transformer architecture [40] .", "cite_spans": [{"start": 12, "end": 16, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 178, "end": 182, "text": "[25]", "ref_id": "BIBREF24"}, {"start": 223, "end": 227, "text": "[39]", "ref_id": "BIBREF38"}, {"start": 314, "end": 318, "text": "[21]", "ref_id": "BIBREF20"}, {"start": 438, "end": 442, "text": "[32]", "ref_id": "BIBREF31"}, {"start": 643, "end": 647, "text": "[22]", "ref_id": "BIBREF21"}, {"start": 673, "end": 677, "text": "[37]", "ref_id": "BIBREF36"}, {"start": 702, "end": 705, "text": "[9]", "ref_id": "BIBREF8"}, {"start": 881, "end": 885, "text": "[41]", "ref_id": "BIBREF40"}, {"start": 928, "end": 932, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 983, "end": 987, "text": "[40]", "ref_id": "BIBREF39"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2"}, {"text": "In the studies mentioned above, the objective is to obtain increasingly better bag-level results, while the evaluation at the instance level is usually performed qualitatively. In contrast, our work addresses both the instance localization task and the bag classification task, as both are of great importance for making a diagnosis. Moreover, our work is not limited to WSI classification; it is also valid for other medical imaging modalities.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2"}, {"text": "Graph Neural Networks. Our motivation -that neighboring instances are likely to have the same label -is a well-established assumption within the machine learning community, often referred to as the cluster assumption [10, 31] . Since leveraged in 1984 by <PERSON><PERSON><PERSON> [30] in the context of spatial statistics, it has been extensively used in spectral clustering [27] , semi-supervised learning on graphs [3] , and recently in GNNs [20] . Our work builds upon seminal works in these areas.", "cite_spans": [{"start": 217, "end": 221, "text": "[10,", "ref_id": "BIBREF9"}, {"start": 222, "end": 225, "text": "31]", "ref_id": "BIBREF30"}, {"start": 262, "end": 266, "text": "[30]", "ref_id": "BIBREF29"}, {"start": 357, "end": 361, "text": "[27]", "ref_id": "BIBREF26"}, {"start": 399, "end": 402, "text": "[3]", "ref_id": "BIBREF2"}, {"start": 426, "end": 430, "text": "[20]", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2"}, {"text": "The proposed smooth operator is derived considering a Dirichlet energy minimization problem, similar to the work by <PERSON> and <PERSON> [42] and <PERSON> et al. [43] . This approach has been employed in recent years to obtain new GNN models, including the p-Laplacian layer [15] , and PPNP layer [16] . Moreover, the Dirichlet energy has been studied in the context of GNNs to analyze the over-smoothing phenomenon [6, 23] . In this regard, our bound on the decrease of the Dirichlet energy is analogous to the result derived by <PERSON> et al. [23] to study over-smoothing for GCNs. Our result, however, holds for the proposed mechanism, of which the graph convolutional layer is a special type.", "cite_spans": [{"start": 135, "end": 139, "text": "[42]", "ref_id": "BIBREF41"}, {"start": 156, "end": 160, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 269, "end": 273, "text": "[15]", "ref_id": "BIBREF14"}, {"start": 291, "end": 295, "text": "[16]", "ref_id": "BIBREF15"}, {"start": 410, "end": 413, "text": "[6,", "ref_id": "BIBREF5"}, {"start": 414, "end": 417, "text": "23]", "ref_id": "BIBREF22"}, {"start": 534, "end": 538, "text": "[23]", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2"}, {"text": "We first describe the binary MIL problem tackled in this paper. Then, we provide a unified view of the most popular deep MIL methods. As explained in Sec. 2, we focus on embedding-based approaches.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Background: A unified view of deep MIL approaches", "sec_num": "3"}, {"text": "In MIL, the training set consists of pairs of the form (X, Y ), where X ∈ R N ×P is a bag of instances and Y ∈ {0, 1} is the bag label. We write X = [x 1 , . . . , x N ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Background: A unified view of deep MIL approaches", "sec_num": "3"}, {"text": "T ∈ R N ×P , where x n ∈ R P are the instances. Each instance x n is associated to a label y n ∈ {0, 1}, not available during training. It is assumed that Y = max {y 1 , . . . , y N }, i.e., a bag X is considered positive if and only if there is at least one positive instance in the bag.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Background: A unified view of deep MIL approaches", "sec_num": "3"}, {"text": "Given a previously unseen bag (e.g., a medical image), the goal at test time is to: i) predict the bag label (classification task) and ii) obtain predictions or estimates for the instance labels (localization task). In general, deep MIL models output a bag-level prediction Ŷ , as well as instance-level scalars f n that are used for instance-level prediction. This general process is depicted in Fig. 1a . In many approaches, these f n are the so-called attention values (e.g., ABMIL [17] , TransMIL [32] , CAMIL [14] ), but they can be obtained in different ways (e.g., through GraphCAM in GTP [41] ). Within the general process in Fig. 1a , deep MIL models can be categorized into three families, depending on how instances interact with each other, see Fig. 1b , Fig. 1c , and Fig. 1d .", "cite_spans": [{"start": 485, "end": 489, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 501, "end": 505, "text": "[32]", "ref_id": "BIBREF31"}, {"start": 514, "end": 518, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 596, "end": 600, "text": "[41]", "ref_id": "BIBREF40"}], "ref_spans": [{"start": 402, "end": 404, "text": "1a", "ref_id": "FIGREF1"}, {"start": 639, "end": 641, "text": "1a", "ref_id": "FIGREF1"}, {"start": 762, "end": 764, "text": "1b", "ref_id": "FIGREF1"}, {"start": 772, "end": 774, "text": "1c", "ref_id": "FIGREF1"}, {"start": 786, "end": 788, "text": "1d", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Background: A unified view of deep MIL approaches", "sec_num": "3"}, {"text": "In the first family, shown in Fig. 1b , the instances are encoded independently and then aggregated. The well-known ABMIL [17] fits in this paradigm. Subsequent works introduce slight modifications to ABMIL, while still encoding each instance independently [21, 25, 39] . ABMIL, on which we will rely to introduce our model, is depicted in Fig. 3a . First, a bag of embeddings H = [h 1 , . . . , h N ] ∈ R N ×D is obtained by applying a neural network independently to each instance. Then, the attention-based pooling computes the attention values f and the bag embedding z according to", "cite_spans": [{"start": 122, "end": 126, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 257, "end": 261, "text": "[21,", "ref_id": "BIBREF20"}, {"start": 262, "end": 265, "text": "25,", "ref_id": "BIBREF24"}, {"start": 266, "end": 269, "text": "39]", "ref_id": "BIBREF38"}], "ref_spans": [{"start": 35, "end": 37, "text": "1b", "ref_id": "FIGREF1"}, {"start": 345, "end": 347, "text": "3a", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Background: A unified view of deep MIL approaches", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "F = tanh HW ⊤ , f = Fw,", "eq_num": "(1)"}], "section": "Background: A unified view of deep MIL approaches", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z = AttPool (H) = H ⊤ Softmax (f ) ,", "eq_num": "(2)"}], "section": "Background: A unified view of deep MIL approaches", "sec_num": "3"}, {"text": "where W ∈ R L×D , w ∈ R L are trainable weights. Last, Ŷ is obtained by applying a linear classifier on z.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Background: A unified view of deep MIL approaches", "sec_num": "3"}, {"text": "The second family accounts for global interactions between instances, possibly long-range ones, see Fig. 1c . These works treat instances as tokens that interact through the self-attention mechanism. This way, global interactions between instances are learned. One of the most popular approaches in this family is TransMIL [32] , which was later extended in different directions [9, 22] . The third family complements the previous one with local interactions defined by a fixed neighborhood, see Fig. 1d . They differ in how local interactions are represented, e.g., as a graph in CAMIL [14] and GTP [41] , or using a position-encoded feature map in SETMIL [40] .", "cite_spans": [{"start": 323, "end": 327, "text": "[32]", "ref_id": "BIBREF31"}, {"start": 379, "end": 382, "text": "[9,", "ref_id": "BIBREF8"}, {"start": 383, "end": 386, "text": "22]", "ref_id": "BIBREF21"}, {"start": 587, "end": 591, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 600, "end": 604, "text": "[41]", "ref_id": "BIBREF40"}, {"start": 657, "end": 661, "text": "[40]", "ref_id": "BIBREF39"}], "ref_spans": [{"start": 105, "end": 107, "text": "1c", "ref_id": "FIGREF1"}, {"start": 501, "end": 503, "text": "1d", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Background: A unified view of deep MIL approaches", "sec_num": "3"}, {"text": "In most of these works, the localization task is assessed qualitatively, e.g., by visually comparing the attention maps. This contrasts with the classification task, which is always evaluated quantitatively. As evidenced by <PERSON><PERSON><PERSON><PERSON> et al. [14] , this has translated into comparatively poor performance in terms of localization. We notice that current models have been designed to target the classification task, and they excel at that. However, their model design is not as careful about the instance-level implications. For example, CAMIL [14] does not leverage any local information to obtain the instance-level attention values. Indeed, from their Eq. ( 8) one deduces that the a i values are obtained from the tile representations t i , which have not undergone any local interaction. Observe that local interactions take place in Eq. ( 4) and Eq. ( 5) in their paper, but these only affect the bag-level predictions, not the instance-level ones. Similarly, GTP [41] introduces local interactions through an off-the-shelf graph convolutional layer, the effect of which is not investigated at the instance level. In the following section, we propose a principled approach to account for meaningful local interactions based on the Dirichlet energy. The idea is motivated by a natural property often observed in the instance-level labels of medical images: the similarity between neighboring instances.", "cite_spans": [{"start": 241, "end": 245, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 542, "end": 546, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 968, "end": 972, "text": "[41]", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "Background: A unified view of deep MIL approaches", "sec_num": "3"}, {"text": "In medical imaging, instance labels are a priori expected to exhibit local dependencies with their neighboring instances: an instance is likely to be surrounded by instances with the same label, see Fig. 2 . Recall that attention values are commonly used as a proxy to estimate these labels, so they should inherit this property. Based on these observations, our intuitive idea is to favor a smoothness property on the attention values. To this end, Sec. 4.1 formalizes the notion of smoothness through the Dirichlet energy. Sec. 4.2 presents the proposed smoothing operator Sm, which encourages smoothness as well as fidelity to the original signal. Sec. 4.3 proposes how to leverage Sm in the context of MIL, both in combination with global interactions (via transformers), and without them. We will build on top of the well-known and simple ABMIL to isolate the effect of Sm and avoid over-sophisticated models.", "cite_spans": [], "ref_spans": [{"start": 204, "end": 205, "text": "2", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "Method: Introducing smoothness in the attention values", "sec_num": "4"}, {"text": "We represent each bag as a graph, where the nodes are the instances and the edges represent the spatial connectivity between instances. Formally, we suppose that each bag X ∈ R N ×D has been assigned an adjacency matrix A = [A ij ] ∈ R N ×N , defined by A ij > 0 if instances x i and x j are neighbors, and A ij = 0 otherwise. We assume that the adjacency matrix is symmetric, i.e. A ij = A ji .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Modelling the smoothness", "sec_num": "4.1"}, {"text": "The Dirichlet energy is a well-known functional that measures the variability of a function defined on a graph [42, 43] . In our case, we think of this function as the attention values f ∈ R N , recall Fig. 1a . As we shall see below, it will be necessary to define the Dirichlet energy for multivariate graph functions. Given a multivariate graph function U = [u 1 , . . . , u N ] ⊤ ∈ R N ×D defined on the bag graph, the Dirichlet energy of U is given by", "cite_spans": [{"start": 111, "end": 115, "text": "[42,", "ref_id": "BIBREF41"}, {"start": 116, "end": 119, "text": "43]", "ref_id": "BIBREF42"}], "ref_spans": [{"start": 207, "end": 209, "text": "1a", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Modelling the smoothness", "sec_num": "4.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E D (U) = 1 2 N i=1 N j=1 A ij ∥u i -u j ∥ 2 2 = Trace U ⊤ LU ,", "eq_num": "(3)"}], "section": "Modelling the smoothness", "sec_num": "4.1"}, {"text": "where ∥•∥ 2 denotes the Euclidean norm, L is the graph Laplacian matrix L = D -A, D ∈ R N ×N is the degree matrix, D = Diag (D 1 , . . . , D N ), D n = i A ni . When D = 1 we obtain the definition for univariate graph functions, such as the attention values f .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Modelling the smoothness", "sec_num": "4.1"}, {"text": "Bounding E D on the attention values. In most deep MIL approaches, the attention values f are obtained by applying a neural network to instance-level features. One example is ABMIL [17] , which uses a two-layer perceptron defined by Eq. 1. Noting that tanh is a Lipschitz function with Lipschitz constant equal to 1, we arrive at the following chain of inequalities", "cite_spans": [{"start": 181, "end": 185, "text": "[17]", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "Modelling the smoothness", "sec_num": "4.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E D (f ) ≤ ∥w∥ 2 2 E D (F) ≤ ∥w∥ 2 2 ∥W∥ 2 2 E D (H) ,", "eq_num": "(4)"}], "section": "Modelling the smoothness", "sec_num": "4.1"}, {"text": "where ∥•∥ 2 denotes the spectral norm. A more general result holds in the general case of an arbitrary multi-layer perceptron, see Appendix A for a proof. The above chain of inequalities tells us that if we want E D (f ) to be low, we can act on f itself or on previous layers (e.g., on F or on H), constraining the norm of the trainable weights to remain constant. This constraint can be achieved using spectral normalization [26] , and we study its influence in Sec. B.3. In the next subsection, we propose an operator that can be used on any of these levels (f , F, H) to reduce the Dirichlet energy of its output.", "cite_spans": [{"start": 427, "end": 431, "text": "[26]", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "Modelling the smoothness", "sec_num": "4.1"}, {"text": "Our goal now turns into finding an operator Sm : R N ×D → R N ×D that, given a bag graph multivariate function U ∈ R N ×D , returns another bag graph multivariate function Sm (U) ∈ R N ×D such that its Dirichlet energy is lower without losing the information present in the original U. Following seminal works [42, 43] , we cast this as an optimization problem,", "cite_spans": [{"start": 310, "end": 314, "text": "[42,", "ref_id": "BIBREF41"}, {"start": 315, "end": 318, "text": "43]", "ref_id": "BIBREF42"}], "ref_spans": [], "eq_spans": [], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Sm (U) = arg min G E (G) ,", "eq_num": "(5)"}], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E (G) = αE D (G) + (1 -α) ∥U -G∥ 2 F ,", "eq_num": "(6)"}], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "where α ∈ [0, 1) accounts for the trade off between both terms, and ∥•∥ F denotes the Frobenius norm. The first term in the above equation penalizes functions with too much variability, while the second term penalizes functions that differ too much from the original U. Note that this can be interpreted as a maximum a posteriori formulation, where the first term corresponds to the prior distribution and the second to the observation model, see [30] . The objective function E is strictly convex, and therefore admits a unique solution, given by", "cite_spans": [{"start": 447, "end": 451, "text": "[30]", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Sm (U) = (I + γL) -1 U,", "eq_num": "(7)"}], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "where γ = α/(1α). Unfortunately, the expression in Eq. ( 7), although elegant, incurs prohibitive computational and memory costs, especially when the number of instances in the bag is large (which is the case of WSIs). Instead, we can take an iterative approach, defining Sm (U) = G(T ), with", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "G(0) = U; G(t) = α (I -L) G(t -1) + (1 -α) U, t ∈ {1, . . . , T } .", "eq_num": "(8)"}], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "As demonstrated by <PERSON> et al. [43] , the sequence {G(t)} converges to the optimal solution in Eq. 7. As studied by <PERSON><PERSON><PERSON> et al. [16] , it is enough to use a small number of iterations T to approximate the exact solution. Therefore, in this work, we will adopt the iterative approach described by Eq. 8. Based on previous work [16] , we will use T = 10, and α will be set as a trainable parameter initialized at α = 0.5. See Sec. B.3 for a study on the effects of these hyperparameters and Fig. 12 for a visual comparison of the effect that α has on the attention maps.", "cite_spans": [{"start": 31, "end": 35, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 133, "end": 137, "text": "[16]", "ref_id": "BIBREF15"}, {"start": 331, "end": 335, "text": "[16]", "ref_id": "BIBREF15"}], "ref_spans": [{"start": 499, "end": 501, "text": "12", "ref_id": "FIGREF16"}], "eq_spans": [], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "Theoretical guarantees via the normalized Laplacian. We present a result that informs us about the rate at which the Dirichlet energy decreases when applying Sm. Let us define", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "λ * γ = max (1 + γλ n ) -2 : λ n ∈ Λ \\ {0}", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The smooth operator", "sec_num": "4.2"}, {"text": ", where Λ = {λ 1 , . . . , λ N } are the eigenvalues of the bag graph Laplacian matrix. Then, we have the following inequality,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E D (Sm (U)) ≤ λ * γ E D (U) . (", "eq_num": "9"}], "section": "The smooth operator", "sec_num": "4.2"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "The proof is inspired by <PERSON><PERSON> and <PERSON> [6] , see Appendix A. If λ * γ < 1, then the smooth operator effectively decreases the Dirichlet energy. If we replace the Laplacian matrix by the normalized Laplacian matrix, L = D -1/2 LD -1/2 , it is known that its eigenvalues lie in the interval [0, 2), and then λ * γ < 1 holds. This motivates the use of the normalized Laplacian in our experiments. The smooth operator Sm only introduces one parameter to be estimated, α. Also, it is differentiable with respect to its input. Therefore, it can be integrated into simple attention-based MIL models, such as ABMIL, to account for local dependencies. ", "cite_spans": [{"start": 38, "end": 41, "text": "[6]", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "The smooth operator", "sec_num": "4.2"}, {"text": "Here we propose how to leverage the operator Sm in the context of MIL. We build on top of the well-known ABMIL. First, we introduce SmAP, which integrates ABMIL with Sm and only accounts for local interactions. Second, we introduce SmTAP, which equips SmAP with a transformer encoder to account for global dependencies. The proposed models are depicted in Fig. 3b and Fig. 3c . The details about the architecture we have used can be found in Sec. B.2.", "cite_spans": [], "ref_spans": [{"start": 361, "end": 363, "text": "3b", "ref_id": "FIGREF5"}, {"start": 373, "end": 375, "text": "3c", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "The proposed model", "sec_num": "4.3"}, {"text": "SmAP: Smooth Attention Pooling. This is represented in Fig. 3b . First, the bag of embeddings H is obtained as in ABMIL [17] , i.e. treating the instances independently. Then, the operator Sm is integrated within the attention pooling. Based on Eq. 4, this can be done on the attention values themselves or on previous representations. We consider three different variants: SmAP-late, SmAP-mid, SmAP-early. They act, respectively, on f (the attention values themselves), on F (i.e. before entering the last layer), and on H (i.e. before entering the attention-based pooling). Formally,", "cite_spans": [{"start": 120, "end": 124, "text": "[17]", "ref_id": "BIBREF16"}], "ref_spans": [{"start": 60, "end": 62, "text": "3b", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "The proposed model", "sec_num": "4.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "late: f = Sm tanh HW ⊤ w ,", "eq_num": "(10) mid"}], "section": "The proposed model", "sec_num": "4.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": ": f = tanh Sm HW ⊤ w,", "eq_num": "(11) early"}], "section": "The proposed model", "sec_num": "4.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": ": z = AttPool (Sm (H)) ,", "eq_num": "(12)"}], "section": "The proposed model", "sec_num": "4.3"}, {"text": "While SmAP-late and SmAP-mid act on the computation of the attention values, SmAP-early acts on the embedding that is passed to the attention-based pooling, see Fig. 8 in Appendix C. We use SmAP-early by default. Sec. 5.3 shows that results do not differ much among configurations.", "cite_spans": [], "ref_spans": [{"start": 166, "end": 167, "text": "8", "ref_id": null}], "eq_spans": [], "section": "The proposed model", "sec_num": "4.3"}, {"text": "SmTAP: Smooth Transformer Attention Pooling. This is represented in Fig. 3c . The only difference with SmAP is that the neural network acting independently on the instance embeddings is replaced by a transformer encoder to account for global dependencies. Based on the idea that smoothness can be imposed at previous locations, recall Eq. 4, we propose to also apply Sm to the transformer output:", "cite_spans": [], "ref_spans": [{"start": 73, "end": 75, "text": "3c", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "The proposed model", "sec_num": "4.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "H = Sm Softmax q (X) k (X) ⊤ v (X) ,", "eq_num": "(13)"}], "section": "The proposed model", "sec_num": "4.3"}, {"text": "where q, k, and v are the standard queries, keys, and values in the dot product self-attention [4] .", "cite_spans": [{"start": 95, "end": 98, "text": "[4]", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "The proposed model", "sec_num": "4.3"}, {"text": "Notice that SmTAP uses Sm in two places: the first after the transformer encoder and the second in the aggregator. Naturally, one could think of other variants that use Sm in only one place or the other. In Sec. 5.3 we ablate these different configurations, leading to similar results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The proposed model", "sec_num": "4.3"}, {"text": "We validate the proposed Sm in three medical MIL datasets: RSNA [13] , PANDA [5] , and CAME-LYON16 [2] . We evaluate the effectiveness of our approach by a quantitative and qualitative analysis. All experiments have been conducted under fair and reproducible conditions. Details on the datasets and experimental setup can be found in Appendix B. The code is available at https://github.com/Franblueee/SmMIL.", "cite_spans": [{"start": 64, "end": 68, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 77, "end": 80, "text": "[5]", "ref_id": "BIBREF4"}, {"start": 99, "end": 102, "text": "[2]", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "We compare our approaches with state-of-the-art deep MIL methods. We consider two groups of methods, depending on the presence/absence of a transformer block to model global dependencies. In the first group, we include those models that do not use this block: the proposed SmAP, ABMIL [17] , CLAM [25] , DSMIL [21] , and DFTD-MIL [39] . The second group consists of models that do use the transformer encoder: the proposed SmTAP, TransMIL [32] , SETMIL [40] , GTP [41] , and CAMIL [14] . These groups ensure a fair comparison in terms of model capabilities and complexity. In Sec. B.3 we report the results of three more methods: DeepGraphSurv [24] , PathGCN [11] , and IIBMIL [29] . Note that the performance obtained by these methods does not affect the conclusions we will obtain in this section.", "cite_spans": [{"start": 285, "end": 289, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 297, "end": 301, "text": "[25]", "ref_id": "BIBREF24"}, {"start": 310, "end": 314, "text": "[21]", "ref_id": "BIBREF20"}, {"start": 330, "end": 334, "text": "[39]", "ref_id": "BIBREF38"}, {"start": 439, "end": 443, "text": "[32]", "ref_id": "BIBREF31"}, {"start": 453, "end": 457, "text": "[40]", "ref_id": "BIBREF39"}, {"start": 464, "end": 468, "text": "[41]", "ref_id": "BIBREF40"}, {"start": 481, "end": 485, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 644, "end": 648, "text": "[24]", "ref_id": "BIBREF23"}, {"start": 659, "end": 663, "text": "[11]", "ref_id": "BIBREF10"}, {"start": 677, "end": 681, "text": "[29]", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "In Sec. 5.1 we consider the localization task. In Sec. 5.2 we turn to the classification task. Sec. 5.3 shows an ablation study on how different uses of the smooth operator affect the proposed model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments", "sec_num": "5"}, {"text": "In this subsection, we analyze the ability of each model to predict the label of the instances inside a bag. As explained in Sec. 3, deep MIL models assign a scalar value f n to each instance x n , see Fig. 1a . Although these can be obtained in different ways, for simplicity we will refer to them as attention values. Thus, we compare the attention values with the ground truth instance labels, which are available for the test set only for evaluation purposes.", "cite_spans": [], "ref_spans": [{"start": 207, "end": 209, "text": "1a", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "Localization: instance level results", "sec_num": "5.1"}, {"text": "Quantitative analysis. We analyze the performance of each method using the area under the ROC curve (AUROC) and the F1 score. Note that a critical hyperparameter for the latter is the threshold used on f n to determine the label of each instance. To ensure a fair comparison, we compute the optimal threshold for each method using the validation set. As a general summary, we also report the average rank achieved by each model across metrics and datasets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Localization: instance level results", "sec_num": "5.1"}, {"text": "The results are shown in Table 1 . We find that using Sm provides the best performance overall, placing as the best or second-best within each group. Only in RSNA the proposed SmAP is outperformed by ABMIL. We attribute this to the fact that the bag graphs in CT scans are not as complex as in WSIs, and therefore the local interactions are not as meaningful. Note that the performance gain is particularly significant on CAMELYON16, where the bags have a larger number of instances, the graphs are much denser and the imbalance between positive and negative instances is more severe. Notably, SmTAP significantly outperforms SETMIL, GTP, and CAMIL, which also model local dependencies. Contrary to our method, their design is focused on bag-level performance and it does not translate into meaningful instance-level properties.", "cite_spans": [], "ref_spans": [{"start": 31, "end": 32, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Localization: instance level results", "sec_num": "5.1"}, {"text": "Attention histograms. We examine the attention histograms produced by each model on the CAME-LYON16 dataset. The corresponding figures for RSNA and PANDA can be found in Appendix C. In Fig. 4 , we represent the frequency with which attention values are assigned to positive and negative instances, separately. An ideal instance classifier would place all the positive instances on the right and all the negative instances on the left. This illustrates why SmTAP and SmAP achieve such a good performance: they concentrate the negative instances to the left of the histogram while succeeding in grouping a large part of the positive instances to the right. TransMIL and GTP assign low attention values to both positive and negative instances. CAMIL is able to identify positive instances, but negative instances are assigned from intermediate to high attention values. CLAM and DSMIL assign low attention values to negative instances, but the distribution of the positive instances resembles a uniform and a normal distribution, respectively.", "cite_spans": [], "ref_spans": [{"start": 190, "end": 191, "text": "4", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "Localization: instance level results", "sec_num": "5.1"}, {"text": "Attention maps. To visualize the localization differences, we show the attention maps generated by four of the transformer-based methods in a WSI from CAMELYON16, see Fig. 5 . SmTAP attention The results shown in this subsection validate the utility of the smooth operator at the instance level. They suggest that having smooth attention maps is a powerful inductive bias that improves the instance-level performance. In the following, we analyze its impact at the bag level.", "cite_spans": [], "ref_spans": [{"start": 172, "end": 173, "text": "5", "ref_id": "FIGREF7"}], "eq_spans": [], "section": "Localization: instance level results", "sec_num": "5.1"}, {"text": "In this subsection, we show that the use of the smooth operator does not deteriorate the bag classification results. On the contrary, in some cases, it improves them. Again, we focus on the AUROC and F1 scores, measured by comparing the true bag labels with the methods' bag label predictions. The threshold for the F1 score is 0.5. We also report the mean rank achieved by each model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Classification: bag level results.", "sec_num": "5.2"}, {"text": "Table 2 shows the results. The proposed models achieve the best performance overall. As in the localization task, ABMIL performs better than SmAP in RSNA. Again, we believe it to be a consequence of the CT scan's low-complexity structure. DFTD-MIL obtains the best result in CAMELYON16, but ranks second or third in the other two datasets. GTP and SETMIL outperform the proposed SmTAP in PANDA, but their performance significantly decreases in CAMELYON16, obtaining the worst results. Overall, our methods provide the most consistent performance, achieving an aggregated mean rank of 1.833.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "2", "ref_id": "TABREF1"}], "eq_spans": [], "section": "Classification: bag level results.", "sec_num": "5.2"}, {"text": "The proposed Sm comes with different design choices and hyperparameters: the placement of Sm, the trade-off parameter α, the number of approximation steps T , and the use of spectral normalization. We analyze them in the following, showing that Sm leads to enhanced results almost under any choice. This supports that our hypothesis -that neighboring instances are likely to have the same label -is a powerful inductive bias worth exploring. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Ablation study", "sec_num": "5.3"}, {"text": "Recall that SmAP leverages by default the early variation, but we also described SmAP-mid and SmAP-late. Likewise, we discussed different variants for SmTAP. Table 3 summarizes the impact of these choices on the final performance.", "cite_spans": [], "ref_spans": [{"start": 164, "end": 165, "text": "3", "ref_id": "TABREF2"}], "eq_spans": [], "section": "Placement of Sm", "sec_num": "5.3.1"}, {"text": "Sm without the transformer encoder (SmAP). These variants differ in the place where Sm is located inside the attention pool, recall Eq. 10-Eq. 12. We include ABMIL since we build our model on top of it. We see that using SmAP improves the performance at both instance and bag levels. This improvement is more noticeable in PANDA and CAMELYON. We attribute it to the bag graph structure being more complex in WSIs than in CT scans. Also, the Dirichlet energy is lower when the smooth operator is used, as theoretically expected. We observe that the proposed method is robust to different placement configurations, which is consistent with the theoretical guarantees presented in Sec. 4.1. However, none of the variants consistently outperforms the others.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Placement of Sm", "sec_num": "5.3.1"}, {"text": "Sm with the transformer encoder (SmTAP). Recall that SmTAP leverages Sm both after the transformer encoder and inside the attention pooling. Here we will refer to it as SmT+SmAP, and will compare it against T+SmAP and SmT+AP (using Sm only in one of the components) and against T+AP (not using Sm). We observe that Sm has no significant effect on bag-level performance. At instance-level we do observe differences: the baseline T+AP is outperformed as long as Sm is used within the attention pooling.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Placement of Sm", "sec_num": "5.3.1"}, {"text": "In the following we study the influence of the trade-off parameter α and of the spectral normalization. Due to space limitations, the analysis for the number of approximation steps T is in Sec. B.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sm hyperparameters", "sec_num": "5.3.2"}, {"text": "The trade-off parameter α. From Eq. 6 we see that α ∈ [0, 1) controls the amount of smoothness enforced by Sm. Note that α = 0 in Eq. 7 produces no smoothness, turning Sm into the identity operator. In Fig. 6a we show the performance obtained for different values of α in CAMELYON16. Each choice of this hyperparameter improves upon the baseline ABMIL (α = 0). We see that better localization results are obtained when α is lower, while better classification results are obtained when Setting α > 0 improves upon the baseline ABMIL (α = 0) and is a trade-off between better localization results (lower α) or better classification results (higher α). Likewise, Sm without spectral normalization already improves the results upon the baseline (ABMIL), but the best performance is obtained when they are used together.", "cite_spans": [], "ref_spans": [{"start": 207, "end": 209, "text": "6a", "ref_id": "FIGREF9"}], "eq_spans": [], "section": "Sm hyperparameters", "sec_num": "5.3.2"}, {"text": "α is higher. Fixing α = 0.5 is a compromise between the two, and produces very similar results as setting it as a trainable parameter initialized at α = 0.5. Fig. 12 provides a visual comparison of the effect that α has on the attention maps.", "cite_spans": [], "ref_spans": [{"start": 163, "end": 165, "text": "12", "ref_id": "FIGREF16"}], "eq_spans": [], "section": "Sm hyperparameters", "sec_num": "5.3.2"}, {"text": "The effect of spectral normalization. Spectral normalization forces the norm of the multi-layer perceptron weights to remain constant. In this work, this is a key design choice that helps Sm to obtain attention maps with lower Dirichlet energy. In our experiments, we have used spectral normalization in the layers immediately after Sm. Note that the late variant does not require spectral normalization, since it applies Sm directly to the attention values. In Fig. 6b we show the results obtained with and without spectral normalization in CAMELYON16. We observe that, even without spectral normalization, Sm improves upon the baseline. The improvement is more significant when Sm is paired with spectral normalization, especially at the instance level.", "cite_spans": [], "ref_spans": [{"start": 467, "end": 469, "text": "6b", "ref_id": "FIGREF9"}], "eq_spans": [], "section": "Sm hyperparameters", "sec_num": "5.3.2"}, {"text": "The main goal of this paper is to draw attention to the study of MIL methods at the instance level.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion and conclusion", "sec_num": "6"}, {"text": "To that end, we revised current deep MIL methods and provided a unified perspective on them. We proposed the smooth operator Sm to introduce local interactions in a principled way. By design, it produces smooth attention maps that resemble the ground truth instance labels. We conducted an exhaustive experimental validation with three real-world MIL datasets and up to eight state-of-the-art methods in both classification and localization tasks. This study showed that our method provides the best performance in localization while being highly competitive (best or second best) at classification.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion and conclusion", "sec_num": "6"}, {"text": "Despite its advantages, our method has some limitations. The first is that, as with every other operator in GNNs, the computational costs of the smooth operator scale with the size of the bag. Fortunately, it can be paired with existing subgraph sampling techniques to mitigate this problem. The second limitation is that we do not have a definite answer for where it is better to use the proposed operator.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion and conclusion", "sec_num": "6"}, {"text": "We have shown that it leads to improvements in almost every place, but the optimal location may be problem-dependent and has to be tailored by the practitioner.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion and conclusion", "sec_num": "6"}, {"text": "Finally, we hope that our work will draw more attention to the localization problem, which is very important for the deployment of computer-aided systems in the real world. In this sense, safely deploying the proposed methods in clinical practice requires evaluating them in a wider range of medical problems and quantifying their uncertainty. For the latter, we believe that the smooth operator could also benefit from a probabilistic formulation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Discussion and conclusion", "sec_num": "6"}, {"text": "A.1 Proof of Eq. 4", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "We present a general result for an arbitrary multilayer perceptron with Lipschitz activation functions. Note that assuming Lipschitzness is not a restriction, since most of the currently used activation functions meet this property [6] . The desired Eq. 4 is a particular case of this result. Let L ∈ N. Consider a L-layer perceptron that, given Y ∈ R N ×D0 , outputs Y L ∈ R N ×D L defined by the following rule", "cite_spans": [{"start": 232, "end": 235, "text": "[6]", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Y 0 = Y,", "eq_num": "(14)"}], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Y ℓ+1 = φ ℓ Y ℓ W ℓ + B ℓ , ℓ ∈ {0, . . . , L -1} ,", "eq_num": "(15)"}], "section": "A Proofs", "sec_num": null}, {"text": "where W ℓ ∈ R D ℓ ×D ℓ+1 , and", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "B ℓ = [b ℓ , . . . , b ℓ ] ⊤ ∈ R N ×D ℓ+1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "where b ℓ ∈ R D ℓ+1 are trainable weights, and φ ℓ : R → R are activation functions applied element-wise. We suppose that each activation function φ ℓ is K ℓ -Lipschitz. Then, we obtain the following inequality,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E D Y ℓ+1 ≤ K 2 ℓ ∥W ℓ ∥ 2 2 E D Y ℓ . (", "eq_num": "16"}], "section": "A Proofs", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "Before verifying it, we note that by applying this inequality to every layer, we arrive at", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E D Y L ≤ • • • ≤ K 2 L-1-ℓ:0 ∥W L-1-ℓ:0 ∥ 2 E D Y ℓ ≤ • • • ≤ K 2 L-1:0 ∥W L-1:0 ∥ 2 E D (Y) ,", "eq_num": "(17)"}], "section": "A Proofs", "sec_num": null}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "∥W ℓ:0 ∥ 2 = ℓ j=0 ∥W j ∥ 2 and K ℓ:0 = ℓ j=0 K ℓ . Taking L = 2, D 0 = D, D 1 = L, D 2 = 1, b 0 = b 1 = 0, φ 0 =", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "tanh, and φ 1 = Id, we recover Eq. 4.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "To verify Eq. 16, we write Y ℓ+1 = y ℓ+1 1 , . . . , y ℓ+1 N ⊤", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "and Y ℓ = y ℓ 1 , . . . , y ℓ N ⊤ . We have,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E D Y ℓ+1 = 1 2 N i=1 N j=1 A ij y ℓ+1 i -y ℓ+1 j 2 2 = (18) = 1 2 N i=1 N j=1 A ij φ ℓ W ⊤ ℓ y ℓ i + B ℓ -φ ℓ W ⊤ ℓ y ℓ j + B ℓ 2 2 ≤ (19) ≤ K 2 ℓ 1 2 N i=1 N j=1 A ij W ⊤ ℓ y ℓ i -y ℓ j 2 2 ≤ (20) ≤ K 2 ℓ ∥W ℓ ∥ 2 2 1 2 N i=1 N j=1 A ij y ℓ i -y ℓ j 2 2 = K 2 ℓ ∥W ℓ ∥ 2 2 E D Y ℓ ,", "eq_num": "(21)"}], "section": "A Proofs", "sec_num": null}, {"text": "where from Eq. 19 to Eq. 20 we have used the definition of <PERSON>ps<PERSON><PERSON> function and from Eq. 20 to Eq. 21 we have used the consistency between the spectral and Euclidean norms.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "A.2 Proof of Eq. 9", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "In this section, we adapt the proof presented in [6] for a similar result. Let U ∈ R N ×D . Our goal is to show that", "cite_spans": [{"start": 49, "end": 52, "text": "[6]", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E D (I + γL) -1 U ≤ λ * γ E D (U) ,", "eq_num": "(22)"}], "section": "A Proofs", "sec_num": null}, {"text": "where γ > 0 and ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "λ * γ = max (1 + γλ n ) -2 : λ n ∈ Λ \\ {0} , being Λ =", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E D (I + γL) -1 u ≤ λ * γ E D (u) .", "eq_num": "(23)"}], "section": "A Proofs", "sec_num": null}, {"text": "Next, it is useful to note that if λ n is an eigenvalue of L with associated eigenvector v n , then (1 + γλ n ) -1 is an eigenvalue of (I + γL) -1 with the same associated eigenvector. Finally, let {v 1 , . . . , v N } be a orthonormal eigenbasis of L, being each v n associated to the eigenvalue λ n . This basis always exists since L is a symmetric matrix. Writing u = N n=1 c n v n , with c n ∈ R, we have", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(I + γL) -1 u = N n=1 c n (1 + γλ n ) -1 v n .", "eq_num": "(24)"}], "section": "A Proofs", "sec_num": null}, {"text": "Using that the eigenvectors are orthogonal to each other, we arrive at", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E D (I + γL) -1 u = N n=1 c 2 n λ n (1 + γλ n ) -2 ≤ λ * γ N n=1 c 2 n λ n = λ * γ E D (u) .", "eq_num": "(25)"}], "section": "A Proofs", "sec_num": null}, {"text": "In this section, we provide the details of the datasets, architectures, and configurations used for each experiment. The code is available at https://github.com/Franblueee/SmMIL.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B Experiments: details and further results", "sec_num": null}, {"text": "We provide insights into the datasets we have used: a description of the problem, the train/test splits, and preprocessing (instance selection and feature extraction). For all datasets, we obtain an initial train/test partition. Then, we split the initial train partition into five different train/validation splits. Every model is trained on each of these splits and then evaluated on the test set. We report the average performance on this test set.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 Datasets", "sec_num": null}, {"text": "RSNA. It was published by the Radiological Society of North America (RSNA) to detect acute intracranial hemorrhage and its subtypes [13] . It is available in Kaggle 1 . We use the official train-test split. It includes a total of 1150 scans. There are a total amount of 39750 slices and the number in each scan varies from 24 to 57. Each slice is preprocessed following [36] .", "cite_spans": [{"start": 132, "end": 136, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 370, "end": 374, "text": "[36]", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "B.1 Datasets", "sec_num": null}, {"text": "PANDA. It is a public dataset for the classification of the severity of prostate cancer from microscopy scans of prostate biopsy samples [5] . It is available in Kaggle2 . Since the official test set is not publicly available, we use the train/test split proposed in [33] . To extract the patches from each WSI, we follow the procedure described in [33] , obtaining patches of size 512 × 512 at 10× magnification. This results in a total amount of 10503 WSIs and 1107931 patches.", "cite_spans": [{"start": 137, "end": 140, "text": "[5]", "ref_id": "BIBREF4"}, {"start": 267, "end": 271, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 349, "end": 353, "text": "[33]", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "B.1 Datasets", "sec_num": null}, {"text": "CAMELYON16. It is a public dataset for the detection of breast cancer metastasis [2] . It is available at the Registry of Open Data of AWS 3 . The official repository contains 400 WSIs in total, including 270 for training and 130 for testing. From each WSI, we extract patches of size 512 × 512 at 20× magnification using the method proposed by <PERSON> et al. [25] .", "cite_spans": [{"start": 81, "end": 84, "text": "[2]", "ref_id": "BIBREF1"}, {"start": 355, "end": 359, "text": "[25]", "ref_id": "BIBREF24"}], "ref_spans": [], "eq_spans": [], "section": "B.1 Datasets", "sec_num": null}, {"text": "We provide details about how we have implemented the proposed methods and how we have conducted the experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 Model and training configuration", "sec_num": null}, {"text": "Feature extractor. Due to the limited memory of the GPU, it is necessary to extract features from each instance. Otherwise, the bags will not fit in memory. In this work, we consider three options for the feature extractor, all of which are pre-trained in Imagenet: ResNet18 (P = 512), ResNet50 (P = 2048), and ViT-B-32 (P = 768). In addition, for CAMELYON16 we also consider ResNet50-BT 4 (P = 2048), which is a ResNet50 model pre-trained using the Barlow Twins Self-Supervised Learning method on a huge dataset of WSIs patches [38, 18] . The results reported in the main text correspond to ResNet18 for RSNA and PANDA, and to ResNet50-BT for CAMELYON16. We study how the choice of the feature extractor affects the results in Sec. B.3. Model architecture. We describe the architecture we have used for the proposed methods (SmAP and SmTAP). For the rest of the methods considered, we adopt their original implementations and default configurations, publicly available on their GitHub repositories. For the independent instance encoding part (see Fig. 3a and Fig. 3b ), the instance embeddings h n are obtained using one fully connected layer with 512 units (D = 512). For the attention-based pooling described by Eq. 1 and Eq. 2, we fix D = 512 and L = 100. The transformer encoder in Fig. 3c is implemented using two transformer layers. These layers use the standard multi-head attention mechanism equipped with skip connections and layer normalization [4] . We fix the key, query, and value dimensions to 128 and the number of heads to 8. We used the Pytorch's implementation of dot product attention 5 . Finally, the bag-embedding classifier was implemented using one fully connected layer.", "cite_spans": [{"start": 529, "end": 533, "text": "[38,", "ref_id": "BIBREF37"}, {"start": 534, "end": 537, "text": "18]", "ref_id": "BIBREF17"}, {"start": 1456, "end": 1459, "text": "[4]", "ref_id": "BIBREF3"}], "ref_spans": [{"start": 1053, "end": 1055, "text": "3a", "ref_id": "FIGREF5"}, {"start": 1065, "end": 1067, "text": "3b", "ref_id": "FIGREF5"}, {"start": 1292, "end": 1294, "text": "3c", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "B.2 Model and training configuration", "sec_num": null}, {"text": "Training setup and hyperparameters. To ensure fair and reproducible conditions, we trained every method under the same setup. The number of epochs was set to 50. We adopt the Adam optimizer [19] with the default Pytorch configuration. For the base learning rate, we considered two different values, 10 -4 and 10 -5 , since we noticed that models that do not use transformers obtained better results when the learning rate was higher. We report the best results for each model. We adopted a slow start using Pytorch's LinearLR scheduler with start_factor=0.1 and total_iters=5.", "cite_spans": [{"start": 190, "end": 194, "text": "[19]", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "B.2 Model and training configuration", "sec_num": null}, {"text": "During training, we monitored the bag AUROC and cross-entropy loss in the validation set and kept the weights that obtained the best results. The batch size was set to 32 in RSNA and PANDA. In CAMELYON16, it was set to 4 for no-transformer methods, and to 1 for transformer-based methods. However, for SETMIL, we had to set it to 1 in PANDA and CAMELYON16 due to its high GPU memory requirements. In RSNA we weighted the loss function to account for the imbalance between positive and negative bags since we observed it to improve the results. All the experiments were performed on one NVIDIA GeForce RTX 3090.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 Model and training configuration", "sec_num": null}, {"text": "We complete the ablation study presented in the main paper, Sec. 5.3, by looking at the rest of the design choices or hyperparameters associated with our Sm.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Further ablation studies.", "sec_num": null}, {"text": "Smooth operator approximation. The exact form of Sm given by Eq. 7 becomes computationally infeasible for large bag sizes. The quality of the approximation, given by Eq. 8, is controlled by the number of steps T . Fig. 7 shows the results for different values of this hyperparameter in RSNA and CAMELYON16. In RSNA, since the bags are smaller, we can compute the closed-form solution, which we represent as T = ∞. Almost any choice of T > 0 improves upon ABMIL. This improvement is particularly noticeable in CAMELYON16. Moreover, in most cases, the performance stabilizes at T = 10, which is the value we used in our experiments.", "cite_spans": [], "ref_spans": [{"start": 219, "end": 220, "text": "7", "ref_id": "FIGREF13"}], "eq_spans": [], "section": "B.3 Further ablation studies.", "sec_num": null}, {"text": "Sm on top of other models. We have proposed two new models (SmAP and SmTAP) by applying Sm on top of two baselines (ABMIL and Transformer+ABMIL, respectively). Instead, the Sm can be applied on top of other existing approaches. In Table 4 we explore how other approaches behave when combined with the proposed Sm in the CAMELYON16 dataset. Instance-level performance is enhanced (greatly in some cases, e.g. an increase from 0.76 to 0.96 in AUROC for DSMIL), whereas bag-level results are competitive. The decrease in bag-level results for DFTD-MIL is explained by the fact that this method randomly splits each bag into different chunks. This may lead to the loss of local interactions exploited by Sm (e.g. if two adjacent instances end in different chunks).", "cite_spans": [], "ref_spans": [{"start": 237, "end": 238, "text": "4", "ref_id": "TABREF3"}], "eq_spans": [], "section": "B.3 Further ablation studies.", "sec_num": null}, {"text": "An alternative smoothing strategy. Introducing a penalty term in the loss function to favor smoothness is a natural alternative to the proposed operator. However, there is an important difference: the use of a penalty term does not modify the model architecture. The penalty term favors that the learned weights encode such a property, but it is not explicitly encoded in the model. For instance, note that the penalty term is not used at inference time. We compare the penalty-based approach and the proposed Sm in Table 5 . Although differences are not large, Sm obtains superior performance.", "cite_spans": [], "ref_spans": [{"start": 522, "end": 523, "text": "5", "ref_id": "TABREF4"}], "eq_spans": [], "section": "B.3 Further ablation studies.", "sec_num": null}, {"text": "Feature extractor. We investigate whether the choice of the feature extractor influences the results and conclusions presented in the main text. We have evaluated each of the considered methods in each dataset using the feature extractors mentioned above (ResNet18, ResNet50, ViT-B-32, and ResNet50-BT). The results are shown in Tables 7 8 9 10 . We summarize them in Table 6 , where we collect the average instance and bag rank of each method for each feature extractor. We observe that the proposed smooth operator <PERSON><PERSON> obtains in almost all cases the highest rank. This supports the idea that the improvement introduced by Sm does not depend on the used features. NeurIPS Paper Checklist Justification: We discuss the limitations of the proposed method in Sec. 6. Guidelines:", "cite_spans": [], "ref_spans": [{"start": 336, "end": 337, "text": "7", "ref_id": "TABREF6"}, {"start": 338, "end": 339, "text": "8", "ref_id": "TABREF7"}, {"start": 340, "end": 341, "text": "9", "ref_id": "TABREF8"}, {"start": 342, "end": 344, "text": "10", "ref_id": "TABREF9"}, {"start": 374, "end": 375, "text": "6", "ref_id": "TABREF5"}], "eq_spans": [], "section": "B.3 Further ablation studies.", "sec_num": null}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Further ablation studies.", "sec_num": null}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Further ablation studies.", "sec_num": null}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 Further ablation studies.", "sec_num": null}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof? Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions). • The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Answer: [Yes] Justification: This information can be found along with the rest of the experimental configuration information, see Appendix B.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "• The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Justification: Our research completely conforms with the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer: [Yes]", "sec_num": null}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer: [Yes]", "sec_num": null}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer: [Yes]", "sec_num": null}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Answer: [Yes]", "sec_num": null}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [Yes] Justification: As pointed out in Sec. 1 and in Sec. 6, one of the main goals of this work is to draw attention to the localization task in MIL, which can contribute favorably to the deployment of computer-aided systems in the real world. However, as we also indicate in Sec. 6, for this to happen safely (e.g., avoiding misdiagnosis), further investigation is needed about equipping them with uncertainty estimation mechanisms. Guidelines:", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact. • Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)? Answer: [NA] Justification: The paper poses no such risks. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort. 12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected? Answer: [Yes] Justification: We cite the original works where the methods were proposed. We used the code provided by the corresponding authors. Each paper includes the URL to its GitHub repository, and each implementation is released under (possibly) a different license, specified in the mentioned repository.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "https://www.kaggle.com/c/rsna-intracranial-hemorrhage-detection", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://www.kaggle.com/c/prostate-cancer-grade-assessment/data", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://registry.opendata.aws/camelyon/", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Weights available at https://github.com/lunit-io/benchmark-ssl-pathology.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "https://pytorch.org/docs/stable/generated/torch.nn.functional.scaled_dot_ product_attention.html", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by project PID2022-140189OB-C22 funded by MCIN / AEI / 10.13039 / 501100011033. <PERSON> acknowledges FPU contract FPU21/01874 funded by Ministerio de Universidades. <PERSON> acknowledges grant C-EXP-153-UGR23 funded by Consejería de Universidad, Investigación e Innovación and by the European Union (EU) ERDF Andalusia Program 2021-2027.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Acknowledgements", "sec_num": null}, {"text": "Justification: The full set of assumptions is included along with the proof of each result, as well as in the main text, see Appendix A and Sec. 4 . Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [{"start": 145, "end": 146, "text": "4", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)? Answer: [Yes] Justification: We describe the proposed method in Sec. 4.3. We provide the details about the datasets and experimental configuration we used in Appendix B. The code associated with this paper has been uploaded as supplementary material to OpenReview, and will be made public on GitHub upon the acceptance of the paper. Also, the datasets are available publicly on Kaggle (RSNA and PANDA) and the Registry of Open Data on AWS (CAMELYON16). Guidelines:• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?Answer: [Yes] Justification: The code associated with this paper has been uploaded as supplementary material to OpenReview, and will be made public on GitHub upon the acceptance of the paper. Also, the datasets are available publicly on Kaggle (RSNA and PANDA) and the Registry of Open Data on AWS (CAMELYON16).Guidelines:• The answer NA means that paper does not include experiments requiring code. • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.", "cite_spans": [{"start": 197, "end": 202, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}, {"text": "Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the results?Answer: [Yes] Justification: We provide the details about the training configuration we have used in Appendix B.Guidelines:• The answer NA means that the paper does not include experiments.• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments?Answer: [Yes] Justification: Our results are presented as the mean and standard deviation obtained in five independent runs, see Sec. 5 and Sec. B.3. Due to space reasons, for Table 3 we only provide the mean values.Guidelines:• The answer NA means that the paper does not include experiments.Guidelines:• The answer NA means that the paper does not use existing assets.• The authors should cite the original paper that produced the code package or dataset.• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset. • For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators.", "cite_spans": [], "ref_spans": [{"start": 343, "end": 344, "text": "3", "ref_id": null}], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets? Answer: [Yes] Justification: Our code has been uploaded as supplementary material, along with instructions to preprocess each dataset and replicate the results of the experiments. Guidelines:• The answer NA means that the paper does not release new assets.• Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc. • The paper should discuss whether and how consent was obtained from people whose asset is used. • We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution. • For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "New Assets", "sec_num": "13."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Diagnostic accuracy of deep learning in medical imaging: a systematic review and meta-analysis", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Sounderajah", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "S", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Hutan", "middle": [], "last": "King", "suffix": ""}, {"first": "Ara", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "NPJ digital medicine", "volume": "4", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Diagnostic accuracy of deep learning in medical imaging: a systematic review and meta-analysis. NPJ digital medicine, 4(1):65, 2021.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Diagnostic assessment of deep learning algorithms for detection of lymph node metastases in women with breast cancer", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Babak", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "Veta", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Nico", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Litjens", "suffix": ""}, {"first": "Awm", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Maschenka", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "<PERSON><PERSON>", "volume": "318", "issue": "22", "pages": "2199--2210", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Diagnostic assessment of deep learning algorithms for detection of lymph node metastases in women with breast cancer. Jama, 318(22):2199-2210, 2017.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Semi-supervised learning on manifolds", "authors": [{"first": "<PERSON>", "middle": [], "last": "Belkin", "suffix": ""}, {"first": "Partha", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2002, "venue": "Machine Learning Journal", "volume": "1", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. Semi-supervised learning on manifolds. Machine Learning Journal, 1, 2002.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Deep Learning -Foundations and Concepts", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Deep Learning -Foundations and Concepts. 2023.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Artificial intelligence for diagnosis and gleason grading of prostate cancer: the panda challenge", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Kartasalo", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ström", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Pinc<PERSON><PERSON>", "suffix": ""}, {"first": "Yuannan", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["F"], "last": "Cai", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Vink", "suffix": ""}], "year": 2022, "venue": "Nature medicine", "volume": "28", "issue": "1", "pages": "154--163", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, et al. Artificial intelligence for diagnosis and gleason grading of prostate cancer: the panda challenge. Nature medicine, 28(1):154-163, 2022.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "A note on over-smoothing for graph neural networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "Cai", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2006.13318"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. A note on over-smoothing for graph neural networks. arXiv preprint arXiv:2006.13318, 2020.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Clinical-grade computational pathology using weakly supervised deep learning on whole slide images", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Geneslaw", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Miraflor", "suffix": ""}, {"first": "Vitor", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>i", "middle": [], "last": "Busam", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Victor", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Klimstra", "suffix": ""}, {"first": "", "middle": [], "last": "Fuchs", "suffix": ""}], "year": 2019, "venue": "Nature medicine", "volume": "25", "issue": "8", "pages": "1301--1309", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Clinical-grade computational pathology using weakly supervised deep learning on whole slide images. Nature medicine, 25(8):1301-1309, 2019.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Multiple instance learning: A survey of problem characteristics and applications", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Carbonneau", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Cheplygin<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Pattern Recognition", "volume": "77", "issue": "", "pages": "329--353", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Multiple instance learning: A survey of problem characteristics and applications. Pattern Recognition, 77:329-353, 2018.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Towards hierarchical regional transformer-based multiple instance learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "3952--3960", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Towards hierarchical regional transformer-based multiple instance learning. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 3952-3960, 2023.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Cluster kernels for semi-supervised learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>le", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Weston", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2002, "venue": "Advances in neural information processing systems", "volume": "15", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Cluster kernels for semi-supervised learning. Advances in neural information processing systems, 15, 2002.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Whole slide images are 2d point clouds: Context-aware survival prediction using patch-based graph convolutional networks", "authors": [{"first": "<PERSON>", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Medical Image Computing and Computer Assisted Intervention-MICCAI 2021: 24th International Conference", "volume": "", "issue": "", "pages": "339--349", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Whole slide images are 2d point clouds: Context-aware sur- vival prediction using patch-based graph convolutional networks. In Medical Image Computing and Computer Assisted Intervention-MICCAI 2021: 24th International Conference, Strasbourg, France, September 27-October 1, 2021, Proceedings, Part VIII 24, pages 339-349. Springer, 2021.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Solving the multiple instance problem with axis-parallel rectangles", "authors": [{"first": "<PERSON>", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "Tom<PERSON>", "middle": [], "last": "Lathrop", "suffix": ""}, {"first": "", "middle": [], "last": "Lozano-<PERSON>", "suffix": ""}], "year": 1997, "venue": "Artificial intelligence", "volume": "89", "issue": "1-2", "pages": "31--71", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. Solving the multiple instance problem with axis-parallel rectangles. Artificial intelligence, 89(1-2):31-71, 1997.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Construction of a machine learning dataset through collaboration: the rsna 2019 brain ct hemorrhage challenge", "authors": [{"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Radiology: Artificial Intelligence", "volume": "2", "issue": "3", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Construction of a machine learning dataset through collaboration: the rsna 2019 brain ct hemorrhage challenge. Radiology: Artificial Intelligence, 2(3):e190211, 2020.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "CAMIL: Context-aware multiple instance learning for cancer detection and subtyping in whole slide images", "authors": [{"first": "<PERSON>", "middle": [], "last": "Fourkioti", "suffix": ""}, {"first": "<PERSON>", "middle": ["De"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bakal", "suffix": ""}], "year": 2024, "venue": "The Twelfth International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. CAMIL: Context-aware multiple instance learning for cancer detection and subtyping in whole slide images. In The Twelfth International Conference on Learning Representations, 2024. URL https://openreview.net/forum? id=rzBskAEmoc.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "p-laplacian based graph neural networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Bian", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "6878--6917", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. p-laplacian based graph neural networks. In International Conference on Machine Learning, pages 6878-6917. PMLR, 2022.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Predict then propagate: Graph neural networks meet personalized pagerank", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Aleksandar", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1810.05997"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Predict then propagate: Graph neural networks meet personalized pagerank. arXiv preprint arXiv:1810.05997, 2018.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Attention-based deep multiple instance learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "Ilse", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Max", "middle": [], "last": "Welling", "suffix": ""}], "year": 2018, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "2127--2136", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Attention-based deep multiple instance learning. In International conference on machine learning, pages 2127-2136. PMLR, 2018.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Benchmarking self-supervised learning on diverse pathology datasets", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "Seonwook", "middle": [], "last": "Park", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "3344--3354", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Benchmarking self-supervised learning on diverse pathology datasets. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 3344-3354, 2023.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Adam: A method for stochastic optimization", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Ba", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1412.6980"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>: A method for stochastic optimization. arXiv preprint arXiv:1412.6980, 2014.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Semi-supervised classification with graph convolutional networks", "authors": [{"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Max", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Welling", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1609.02907"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Semi-supervised classification with graph convolutional networks. arXiv preprint arXiv:1609.02907, 2016.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Dual-stream multiple instance learning network for whole slide image classification with self-supervised contrastive learning", "authors": [{"first": "Bin", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "14318--14328", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Dual-stream multiple instance learning network for whole slide image classification with self-supervised contrastive learning. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pages 14318-14328, 2021.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Dt-mil: deformable transformer for multi-instance learning on histopathological image", "authors": [{"first": "Hang", "middle": [], "last": "Li", "suffix": ""}, {"first": "Fan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Xi<PERSON>", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ming<PERSON>uan", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Junzhou", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Liansheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jianhua", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Medical Image Computing and Computer Assisted Intervention-MICCAI 2021: 24th International Conference", "volume": "", "issue": "", "pages": "206--216", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Dt-mil: deformable transformer for multi-instance learning on histopathological image. In Medical Image Computing and Computer Assisted Intervention- MICCAI 2021: 24th International Conference, Strasbourg, France, September 27-October 1, 2021, Proceedings, Part VIII 24, pages 206-216. Springer, 2021.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Deeper insights into graph convolutional networks for semi-supervised learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the AAAI conference on artificial intelligence", "volume": "32", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Deeper insights into graph convolutional networks for semi-supervised learning. In Proceedings of the AAAI conference on artificial intelligence, volume 32, 2018.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Graph cnn for survival analysis on whole slide pathological images", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yeqing", "middle": [], "last": "Li", "suffix": ""}, {"first": "Junzhou", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "International Conference on Medical Image Computing and Computer-Assisted Intervention", "volume": "", "issue": "", "pages": "174--182", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Graph cnn for survival analysis on whole slide pathological images. In International Conference on Medical Image Computing and Computer-Assisted Intervention, pages 174-182. Springer, 2018.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Data-efficient and weakly supervised computational pathology on whole-slide images", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "Drew <PERSON> Williamson", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature biomedical engineering", "volume": "5", "issue": "6", "pages": "555--570", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Data-efficient and weakly supervised computational pathology on whole-slide images. Nature biomedical engineering, 5(6):555-570, 2021.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Spectral normalization for generative adversarial networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kataoka", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1802.05957"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Spectral normalization for generative adversarial networks. arXiv preprint arXiv:1802.05957, 2018.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "On spectral clustering: Analysis and an algorithm", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Jordan", "suffix": ""}, {"first": "Yair", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2001, "venue": "Advances in neural information processing systems", "volume": "14", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. On spectral clustering: Analysis and an algorithm. Advances in neural information processing systems, 14, 2001.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Multiple-instance learning for medical image and video analysis", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Cazuguel", "suffix": ""}, {"first": "Béatrice", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "IEEE reviews in biomedical engineering", "volume": "10", "issue": "", "pages": "213--234", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Multiple-instance learning for medical image and video analysis. IEEE reviews in biomedical engineering, 10: 213-234, 2017.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Iib-mil: Integrated instance-level and bag-level multiple instances learning with label disambiguation for pathological image analysis", "authors": [{"first": "Qin", "middle": [], "last": "Ren", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Fan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yong<PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "Junzhou", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jianhua", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Medical Image Computing and Computer-Assisted Intervention", "volume": "", "issue": "", "pages": "560--569", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Iib-mil: Integrated instance-level and bag-level multiple instances learning with label disambiguation for pathological image analysis. In International Conference on Medical Image Computing and Computer-Assisted Intervention, pages 560-569. Springer, 2023.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Spatial statistics", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1981, "venue": "Wiley Series in Probability and Statistics", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Spatial statistics. Wiley Series in Probability and Statistics, 1981.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Learning with labeled and unlabeled data", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2000, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Learning with labeled and unlabeled data. 2000.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Transmil: Transformer based correlated multiple instance learning for whole slide image classification", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "Bian", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yifeng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiangyang", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in neural information processing systems", "volume": "34", "issue": "", "pages": "2136--2147", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Transmil: Transformer based correlated multiple instance learning for whole slide image classification. Advances in neural information processing systems, 34:2136-2147, 2021.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Self-learning for weakly supervised gleason grading of local patterns", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Colomer", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Dolz", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "IEEE journal of biomedical and health informatics", "volume": "25", "issue": "8", "pages": "3094--3104", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Self-learning for weakly supervised gleason grading of local patterns. IEEE journal of biomedical and health informatics, 25(8):3094-3104, 2021.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Artificial intelligence for digital and computational pathology", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "Drew <PERSON> Williamson", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Nature Reviews Bioengineering", "volume": "1", "issue": "12", "pages": "930--949", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Artificial intelligence for digital and computational pathology. Nature Reviews Bioengineering, 1(12):930-949, 2023.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Analysis of 3D pathology samples using weakly supervised AI", "authors": [{"first": "<PERSON>", "middle": ["H"], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "F", "middle": ["K"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S L"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Gan", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xavier", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Farré", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Anil", "middle": ["V"], "last": "True", "suffix": ""}, {"first": "<PERSON>", "middle": ["T C"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Cell", "volume": "187", "issue": "10", "pages": "2502--2520", "other_ids": {"DOI": ["10.1016/j.cell.2024.03.035"], "ISSN": ["1097-4172"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Analysis of 3D pathology samples using weakly supervised AI. Cell, 187(10):2502-2520.e17, May 2024. ISSN 1097-4172. doi: 10.1016/j.cell.2024.03.035.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Combining attention-based multiple instance learning and gaussian processes for ct hemorrhage detection", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["K"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Medical Image Computing and Computer-Assisted Intervention", "volume": "", "issue": "", "pages": "582--591", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Combining attention-based multiple instance learning and gaussian processes for ct hemorrhage detection. In International Conference on Medical Image Computing and Computer-Assisted Intervention, pages 582-591. Springer, 2021.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Diagnose like a pathologist: Transformer-enabled hierarchical attention-guided multiple instance learning for whole slide image classification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "King", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2301.08125"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Diagnose like a pathologist: Transformer-enabled hierarchical attention-guided multiple instance learning for whole slide image classification. arXiv preprint arXiv:2301.08125, 2023.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Barlow twins: Selfsupervised learning via redundancy reduction", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Zbontar", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Lecun", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "12310--12320", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. <PERSON> twins: Self- supervised learning via redundancy reduction. In International conference on machine learning, pages 12310-12320. PMLR, 2021.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Dtfd-mil: Double-tier feature distillation multiple instance learning for histopathology whole slide image classification", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yan<PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "Coupland", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "18802--18812", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Dtfd-mil: Double-tier feature distillation multiple instance learning for histopathology whole slide image classification. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 18802-18812, 2022.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Setmil: spatial encoding transformer-based multiple instance learning for pathological image analysis", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Junzhou", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Liansheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jianhua", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Medical Image Computing and Computer-Assisted Intervention", "volume": "", "issue": "", "pages": "66--76", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Setmil: spatial encoding transformer-based multiple instance learning for pathological image analysis. In International Conference on Medical Image Computing and Computer- Assisted Intervention, pages 66-76. Springer, 2022.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "A graph-transformer for whole slide image classification", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Green", "suffix": ""}, {"first": "Marg<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "IEEE transactions on medical imaging", "volume": "41", "issue": "11", "pages": "3003--3015", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. A graph-transformer for whole slide image classification. IEEE transactions on medical imaging, 41(11):3003-3015, 2022.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Regularization on discrete spaces", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2005, "venue": "Joint Pattern Recognition Symposium", "volume": "", "issue": "", "pages": "361--368", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. Regularization on discrete spaces. In Joint Pattern Recognition Symposium, pages 361-368. Springer, 2005.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Learning with local and global consistency", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Weston", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2003, "venue": "Advances in neural information processing systems", "volume": "16", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Learning with local and global consistency. Advances in neural information processing systems, 16, 2003.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "A review of deep learning in medical imaging: Imaging traits, technology trends, case studies with progress highlights, and future promises", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Greenspan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Davat<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Anant", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Prince", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE", "volume": "109", "issue": "5", "pages": "820--838", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. A review of deep learning in medical imaging: Imaging traits, technology trends, case studies with progress highlights, and future promises. Proceedings of the IEEE, 109(5):820-838, 2021.", "links": null}}, "ref_entries": {"FIGREF0": {"text": "Global and local interactions.", "uris": null, "fig_num": null, "num": null, "type_str": "figure"}, "FIGREF1": {"text": "Figure 1: (a) Unified view of deep MIL models. Depending on how instances interact with each other in (a), we devise three different families of methods: (b), (c), (d).", "uris": null, "fig_num": "1", "num": null, "type_str": "figure"}, "FIGREF2": {"text": "(a) Labelled patches in a WSI. (b) Labelled slices in a CT scan.", "uris": null, "fig_num": null, "num": null, "type_str": "figure"}, "FIGREF3": {"text": "Figure 2: WSIs are divided into patches. CT scans are provided as slices. They often show spatial dependencies: in a WSI, a patch is usually surrounded by patches with the same label, while in a CT scan, a slice is usually surrounded by slices with the same label. The red color indicates malignant/hemorrhage patches/slices.", "uris": null, "fig_num": "2", "num": null, "type_str": "figure"}, "FIGREF5": {"text": "Figure 3: Smooth Attention Multiple Instance Learning. (a) The well-known model in [17], which we build upon. (b): only local interactions are considered by applying the proposed smooth operator Sm in the aggregation part. (c): both global and local interactions are considered by applying Sm both in the transformer and in the aggregation parts.", "uris": null, "fig_num": "3", "num": null, "type_str": "figure"}, "FIGREF6": {"text": "Figure 4: Attention histograms on CAMELYON16. First/second rows show models without/with global interactions. SmAP and SmTAP stand out at separating positive and negative instances.", "uris": null, "fig_num": "4", "num": null, "type_str": "figure"}, "FIGREF7": {"text": "Figure 5: Attention maps on CAMELYON16. The novel SmTAP produces the most accurate one. map resembles the most to the ground truth. As noted in Fig. 4, CAMIL assigns high attention values to both positive and negative instances. TransMIL and GTP pinpoint the regions of interest, but the attention is relatively low in those areas, which produces unclear boundaries, especially in the case of TransMIL. The attention maps for the rest of the methods and datasets are in Appendix C.", "uris": null, "fig_num": "5", "num": null, "type_str": "figure"}, "FIGREF9": {"text": "Figure6: Influence of the trade-off parameter α (left) and of spectral normalization (right) in CAMELYON16. Setting α > 0 improves upon the baseline ABMIL (α = 0) and is a trade-off between better localization results (lower α) or better classification results (higher α). Likewise, Sm without spectral normalization already improves the results upon the baseline (ABMIL), but the best performance is obtained when they are used together.", "uris": null, "fig_num": "6", "num": null, "type_str": "figure"}, "FIGREF10": {"text": "{λ 1 , . . . , λ N } the eigenvalues of the symmetric graph Laplacian matrix L. First, we reduce the proof to univariate graph functions by looking at the rows of U as univariate graph functions. Denoting them as {u 1 , . . . , u D }, where each u d ∈ R N , we have E D (I + γL) -1 U = D d=1 E D (I + γL) -1 u d . Therefore, it will be sufficient to show that, for any u ∈ R N ,", "uris": null, "fig_num": null, "num": null, "type_str": "figure"}, "FIGREF13": {"text": "Figure 7: Influence of the number of steps T used to approximate Sm in RSNA and CAMELYON16. ABMIL corresponds to T = 0. Using T = 10 is enough to closely match the performance of the exact form (T = ∞).", "uris": null, "fig_num": "7", "num": null, "type_str": "figure"}, "FIGREF15": {"text": "Figure 8: Graphical representation of the different variants SmAP-late, SmAP-mid, SmAP-early. The well-known ABMIL, which we build upon, is shown in (a).", "uris": null, "fig_num": "810", "num": null, "type_str": "figure"}, "FIGREF16": {"text": "Figure 11: CAMELYON16 attention maps.", "uris": null, "fig_num": "12", "num": null, "type_str": "figure"}, "FIGREF17": {"text": "Figure 13: RSNA attention histograms.", "uris": null, "fig_num": "14", "num": null, "type_str": "figure"}, "TABREF0": {"text": "Localization results (mean and standard deviation from five independent runs). The best is in bold and the second-best is underlined. (↓)/(↑) means lower/higher is better. The proposed operator improves the localization results in all three datasets and both with and without global interactions. It ranks first in eight out of twelve dataset-score pairs.", "content": "<table><tr><td/><td/><td colspan=\"2\">RSNA</td><td colspan=\"2\">PANDA</td><td colspan=\"2\">CAMELYON16</td><td/></tr><tr><td/><td/><td>AUROC (↑)</td><td>F1 (↑)</td><td>AUROC (↑)</td><td>F1 (↑)</td><td>AUROC (↑)</td><td>F1 (↑)</td><td>Rank (↓)</td></tr><tr><td>Without global interactions</td><td>SmAP ABMIL CLAM DSMIL</td><td colspan=\"2\">0.798 0.033 0.806 0.012 0.486 0.033 0.477 0.014 0.523 0.069 0.076 0.154 0.554 0.004 0.180 0.000</td><td>0.799 0.005 0.768 0.002 0.727 0.046 0.765 0.008</td><td>0.635 0.006 0.602 0.004 0.568 0.038 0.598 0.006</td><td colspan=\"3\">0.960 0.007 0.840 0.053 1.500 0.548 0.819 0.074 0.766 0.060 2.500 1.225 0.849 0.044 4.167 1.329 0.821 0.046 0.760 0.070 0.654 0.183 4.333 0.516</td></tr><tr><td/><td>DFTD-MIL</td><td>0.747 0.070</td><td>0.453 0.194</td><td>0.795 0.004</td><td>0.637 0.006</td><td>0.884 0.002</td><td>0.742 0.040</td><td>2.500 1.049</td></tr><tr><td/><td>SmTAP</td><td colspan=\"3\">0.767 0.046 0.474 0.023 0.790 0.007</td><td>0.622 0.01</td><td colspan=\"3\">0.789 0.008 0.600 0.067 1.500 1.225</td></tr><tr><td>With global</td><td>TransMIL SETMIL</td><td>0.732 0.013 0.726 0.025</td><td>0.471 0.014 0.438 0.027</td><td>0.751 0.011 0.774 0.007</td><td>0.636 0.008 0.631 0.010</td><td>0.781 0.024 0.615 0.231</td><td>0.127 0.078 0.134 0.267</td><td>3.083 1.429 3.667 0.816</td></tr><tr><td>interactions</td><td>GTP CAMIL</td><td>0.736 0.017 0.760 0.036</td><td>0.425 0.018 0.456 0.013</td><td>0.768 0.022 0.785 0.011</td><td>0.636 0.011 0.621 0.013</td><td>0.442 0.091 0.742 0.028</td><td>0.037 0.036 0.479 0.175</td><td>3.917 1.429 2.833 1.169</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF1": {"text": "Classification results (mean and standard deviation from five independent runs). The best is in bold and the second-best is underlined. (↓)/(↑) means lower/higher is better. The models with the proposed operator achieve the best performance overall, ranking first or second in nine out of twelve dataset-score pairs.", "content": "<table><tr><td/><td/><td colspan=\"2\">RSNA</td><td colspan=\"2\">PANDA</td><td colspan=\"2\">CAMELYON16</td><td/></tr><tr><td/><td/><td>AUROC (↑)</td><td>F1 (↑)</td><td>AUROC (↑)</td><td>F1 (↑)</td><td>AUROC (↑)</td><td>F1 (↑)</td><td>Rank (↓)</td></tr><tr><td>Without global</td><td>SmAP ABMIL CLAM</td><td>0.888 0.005 0.889 0.005 0.674 0.157</td><td>0.787 0.026 0.796 0.011 0.161 0.291</td><td colspan=\"2\">0.943 0.001 0.915 0.002 0.933 0.002 0.909 0.001 0.893 0.026 0.868 0.034</td><td>0.976 0.007 0.956 0.011 0.960 0.029</td><td colspan=\"2\">0.916 0.016 1.833 0.753 0.914 0.021 2.500 1.049 0.897 0.012 4.500 0.837</td></tr><tr><td>interactions</td><td>DSMIL</td><td>0.689 0.063</td><td>0.240 0.012</td><td>0.921 0.008</td><td>0.904 0.008</td><td>0.947 0.076</td><td>0.866 0.123</td><td>4.167 0.753</td></tr><tr><td/><td colspan=\"2\">DFTD-MIL 0.890 0.045</td><td>0.775 0.282</td><td>0.940 0.001</td><td>0.903 0.002</td><td>0.983 0.01</td><td colspan=\"2\">0.937 0.013 2.000 1.265</td></tr><tr><td>With</td><td>SmTAP TransMIL</td><td colspan=\"2\">0.906 0.007 0.825 0.026 0.883 0.008 0.716 0.031</td><td>0.946 0.003 0.933 0.010</td><td>0.917 0.002 0.895 0.029</td><td>0.976 0.014 0.973 0.018</td><td colspan=\"2\">0.948 0.02 1.833 0.983 0.911 0.028 4.083 0.917</td></tr><tr><td>global</td><td>SETMIL</td><td>0.869 0.011</td><td>0.716 0.036</td><td colspan=\"2\">0.974 0.003 0.946 0.003</td><td>0.715 0.155</td><td>0.471 0.341</td><td>3.583 2.010</td></tr><tr><td>interactions</td><td>GTP CAMIL</td><td>0.901 0.008 0.889 0.019</td><td>0.805 0.017 0.805 0.028</td><td>0.949 0.004 0.938 0.003</td><td>0.920 0.003 0.911 0.004</td><td>0.748 0.118 0.984 0.007</td><td>0.727 0.143 0.918 0.018</td><td>2.750 0.987 2.750 1.173</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF2": {"text": "Ablation study on different configurations of our models. AUROC (at both instance and bag levels), and normalized Dirichlet energy of attention values are reported. Almost all configurations improve the results in both tasks against the baseline (not using Sm).", "content": "<table><tr><td/><td/><td>RSNA</td><td/><td/><td>PANDA</td><td/><td/><td>CAMELYON16</td><td/></tr><tr><td>SmAP-early</td><td>0.798</td><td>0.888</td><td>0.009</td><td>0.799</td><td>0.943</td><td>0.106</td><td>0.960</td><td>0.976</td><td>0.395</td></tr><tr><td>SmAP-mid</td><td>0.806</td><td>0.888</td><td>0.012</td><td>0.792</td><td>0.940</td><td>0.135</td><td>0.922</td><td>0.964</td><td>0.384</td></tr><tr><td>SmAP-late</td><td>0.811</td><td>0.891</td><td>0.011</td><td>0.802</td><td>0.944</td><td>0.082</td><td>0.819</td><td>0.964</td><td>0.321</td></tr><tr><td>ABMIL</td><td>0.806</td><td>0.889</td><td>0.023</td><td>0.768</td><td>0.933</td><td>0.141</td><td>0.819</td><td>0.956</td><td>0.419</td></tr><tr><td>SmT+SmAP</td><td>0.791</td><td>0.910</td><td>0.010</td><td>0.813</td><td>0.944</td><td>0.306</td><td>0.841</td><td>0.986</td><td>0.313</td></tr><tr><td>SmT+AP</td><td>0.791</td><td>0.910</td><td>0.010</td><td>0.754</td><td>0.940</td><td>0.356</td><td>0.754</td><td>0.984</td><td>0.320</td></tr><tr><td>T+SmAP</td><td>0.792</td><td>0.910</td><td>0.010</td><td>0.787</td><td>0.944</td><td>0.332</td><td>0.915</td><td>0.986</td><td>0.343</td></tr><tr><td>T+AP</td><td>0.792</td><td>0.910</td><td>0.020</td><td>0.760</td><td>0.942</td><td>0.391</td><td>0.781</td><td>0.984</td><td>0.433</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF3": {"text": "Using Sm on top of other models (CAMELYON16 with ResNet50-BT features). Improvements are highlighted in green. Using the proposed Sm increases the instance-level performance, while the bag-level performance remains competitive.", "content": "<table><tr><td/><td colspan=\"2\">Instance</td><td>Bag</td><td/></tr><tr><td/><td>AUROC (↑)</td><td>F1 (↑)</td><td>AUROC (↑)</td><td>F1 (↑)</td></tr><tr><td>CLAM</td><td>0.849 0.044</td><td>0.821 0.046</td><td>0.96 0.029</td><td>0.897 0.012</td></tr><tr><td>SmCLAM</td><td>0.928 0.028</td><td>0.873 0.018</td><td>0.966 0.007</td><td>0.889 0.017</td></tr><tr><td>DSMIL</td><td>0.76 0.078</td><td>0.654 0.203</td><td>0.947 0.085</td><td>0.866 0.136</td></tr><tr><td>SmDSMIL</td><td>0.960 0.013</td><td>0.776 0.088</td><td>0.967 0.011</td><td>0.919 0.018</td></tr><tr><td>DFTD-MIL</td><td>0.984 0.002</td><td>0.742 0.040</td><td>0.983 0.010</td><td>0.937 0.013</td></tr><tr><td>SmDFTD-MIL</td><td>0.984 0.183</td><td>0.836 0.222</td><td>0.978 0.158</td><td>0.903 0.183</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF4": {"text": "Instance and bag AUROC (higher is better) in CAMELYON16 using ResNet50-BT features for the proposed methods and the penalty-based approach. The best in each column is highlighted in bold. Sm obtains superior performance, although the differences are not large. SmAP 0.798 0.033 0.888 0.005 0.799 0.005 0.943 0.001 0.961 0.007 0.965 0.007 ABMIL+PENALTY 0.782 0.050 0.889 0.043 0.780 0.003 0.935 0.001 0.979 0.013 0.963 0.012", "content": "<table><tr><td/><td/><td colspan=\"2\">RSNA</td><td>PANDA</td><td/><td>CAMELYON16</td></tr><tr><td/><td/><td>Inst.</td><td>Bag</td><td>Inst.</td><td>Bag</td><td>Inst.</td><td>Bag</td></tr><tr><td>W/o</td><td/><td/><td/><td/><td/></tr><tr><td>global int.</td><td/><td/><td/><td/><td/></tr><tr><td>W/</td><td>SmTAP</td><td colspan=\"5\">0.767 0.046 0.906 0.007 0.790 0.007 0.946 0.003 0.789 0.008 0.976 0.014</td></tr><tr><td>global int.</td><td>T+PENALTY</td><td>0.737 0.045</td><td>0.905 0.005</td><td colspan=\"3\">0.772 0.011 0.947 0.001 0.769 0.099 0.988 0.004</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF5": {"text": "Instance and bag average ranks (lower is better) obtained by each method for different choices of the feature extractor. The best result within each group is bolded, and the second-best is underlined. SmAP and SmTAP obtain in almost all cases the highest rank. SmTAP 2.167 1.835 1.667 1.033 2.375 1.847 1.875 0.835 1.833 0.983 2.500 0.837 1.500 0.707 1.500 0.707", "content": "<table><tr><td/><td/><td colspan=\"2\">ResNet18</td><td colspan=\"2\">ResNet50</td><td colspan=\"2\">ViT-B-32</td><td colspan=\"2\">ResNet50-BT</td></tr><tr><td/><td/><td>Inst.</td><td>Bag</td><td>Inst.</td><td>Bag</td><td>Inst.</td><td>Bag</td><td>Inst.</td><td>Bag</td></tr><tr><td/><td>SmAP ABMIL</td><td colspan=\"8\">2.000 0.632 2.000 1.095 1.625 0.744 1.750 0.707 1.667 0.816 1.500 1.225 1.000 0.000 2.000 0.000 2.667 1.366 1.667 0.816 3.750 1.581 3.250 0.707 4.333 2.160 3.000 0.894 4.500 0.707 3.500 0.707</td></tr><tr><td>Without global</td><td colspan=\"2\">DeepGraphSurv 4.000 2.000 CLAM 6.167 0.983</td><td>5.500 1.225 5.500 1.225</td><td>2.500 1.195 4.750 2.053</td><td>5.625 0.916 4.500 2.070</td><td>3.333 1.211 6.333 0.816</td><td>5.000 0.000 5.000 2.449</td><td>2.500 0.707 3.000 1.414</td><td>6.000 0.000 3.500 0.707</td></tr><tr><td>interactions</td><td>DSMIL</td><td>5.167 0.983</td><td>5.333 1.506</td><td>6.375 0.518</td><td>6.000 0.926</td><td>5.667 1.033</td><td>6.667 0.516</td><td>6.000 0.000</td><td>5.000 0.000</td></tr><tr><td/><td>PathGCN</td><td>5.833 1.472</td><td>5.167 1.722</td><td>5.625 1.302</td><td>4.625 2.134</td><td>4.500 1.643</td><td>4.000 1.673</td><td>7.000 0.000</td><td>7.000 0.000</td></tr><tr><td/><td>DFTD-MIL</td><td>2.167 0.983</td><td>2.833 1.169</td><td>3.375 1.302</td><td>2.250 1.488</td><td>2.167 0.983</td><td>2.833 0.983</td><td colspan=\"2\">4.000 1.414 1.000 0.000</td></tr><tr><td>With global interactions</td><td>TransMIL SETMIL GTP IIBMIL CAMIL</td><td>3.167 1.329 3.667 0.816 4.167 0.983 5.167 2.041 2.667 1.751</td><td>3.833 1.169 3.500 1.975 3.333 1.751 5.667 0.816 3.000 0.894</td><td>3.500 1.069 3.625 1.768 4.500 1.069 4.500 2.138 2.500 1.309</td><td>3.625 1.061 4.125 2.031 3.375 1.598 5.125 1.642 2.875 1.356</td><td>4.167 1.329 3.667 1.506 5.000 1.265 4.167 1.722 2.167 1.472</td><td>4.500 1.975 3.333 1.862 4.833 1.329 3.167 2.041 2.667 1.211</td><td>4.000 1.414 4.500 0.707 6.000 0.000 2.000 1.414 3.000 1.414</td><td>4.000 0.000 6.000 0.000 5.000 0.000 2.500 0.707 2.000 1.414</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF6": {"text": "Instance AUROC (higher is better) for different choices of the feature extractor.", "content": "<table><tr><td>ResNet50+BT</td><td>CAMELYON16 CAMELYON16</td><td>0.773 0.062 0.961 0.007</td><td>0.755 0.143 0.816 0.055</td><td colspan=\"2\">0.756 0.104 0.959 0.033 0.463 0.034 0.849 0.044</td><td>0.661 0.115 0.760 0.078</td><td colspan=\"2\">0.813 0.073 0.952 0.011 0.443 0.138 0.884 0.002</td><td colspan=\"2\">0.819 0.162 0.789 0.008 0.779 0.062 0.781 0.024</td><td colspan=\"2\">0.569 0.009 0.615 0.231</td><td colspan=\"2\">0.477 0.095 0.442 0.091</td><td>0.863 0.038 0.939 0.007 0.873 0.138 0.742 0.028</td></tr><tr><td>ViT-B-32</td><td>PANDA</td><td>0.810 0.007</td><td>0.773 0.004</td><td colspan=\"2\">0.809 0.008 0.777 0.004</td><td>0.779 0.002</td><td>0.769 0.032</td><td>0.785 0.008</td><td>0.822 0.010</td><td>0.749 0.040</td><td colspan=\"2\">0.789 0.089</td><td colspan=\"2\">0.720 0.039</td><td>0.740 0.031</td><td>0.806 0.015</td></tr><tr><td/><td>RSNA</td><td colspan=\"2\">0.804 0.017 0.797 0.023</td><td>0.755 0.063</td><td>0.500 0.000</td><td>0.702 0.029</td><td>0.749 0.046</td><td>0.807 0.030</td><td>0.795 0.027</td><td>0.749 0.019</td><td colspan=\"2\">0.755 0.001</td><td colspan=\"2\">0.760 0.013</td><td>0.690 0.017</td><td>0.783 0.007</td></tr><tr><td/><td>CAMELYON16</td><td colspan=\"2\">0.854 0.116 0.806 0.130</td><td>0.814 0.027</td><td>0.559 0.056</td><td>0.670 0.111</td><td>0.851 0.219</td><td>0.952 0.013</td><td>0.716 0.105</td><td colspan=\"3\">0.844 0.023 0.787 0.005</td><td colspan=\"2\">0.528 0.149</td><td>0.500 0.000</td><td>0.964 0.014</td></tr><tr><td>ResNet50</td><td>PANDA</td><td colspan=\"2\">0.786 0.005 0.774 0.009</td><td>0.806 0.002</td><td>0.785 0.004</td><td>0.747 0.006</td><td>0.772 0.011</td><td>0.784 0.007</td><td>0.756 0.012</td><td>0.743 0.021</td><td colspan=\"2\">0.774 0.071</td><td colspan=\"2\">0.754 0.019</td><td>0.729 0.031</td><td>0.766 0.027</td></tr><tr><td/><td>RSNA</td><td>0.783 0.026</td><td>0.796 0.027</td><td>0.768 0.013</td><td>0.497 0.005</td><td>0.568 0.015</td><td>0.692 0.047</td><td>0.795 0.018</td><td>0.802 0.016</td><td>0.707 0.023</td><td colspan=\"2\">0.678 0.004</td><td colspan=\"2\">0.736 0.024</td><td>0.672 0.024</td><td>0.796 0.013</td></tr><tr><td/><td>CAMELYON16</td><td>0.798 0.037</td><td>0.679 0.082</td><td colspan=\"2\">0.868 0.094 0.516 0.102</td><td>0.628 0.181</td><td>0.618 0.214</td><td>0.920 0.074</td><td>0.750 0.134</td><td colspan=\"3\">0.820 0.038 0.792 0.032</td><td colspan=\"2\">0.594 0.228</td><td>0.500 0.000</td><td>0.917 0.043</td></tr><tr><td>ResNet18</td><td>PANDA</td><td>0.799 0.005</td><td>0.768 0.002</td><td>0.720 0.011</td><td>0.727 0.046</td><td>0.765 0.008</td><td>0.664 0.019</td><td>0.795 0.004</td><td>0.790 0.007</td><td>0.751 0.011</td><td colspan=\"2\">0.774 0.007</td><td colspan=\"2\">0.768 0.022</td><td>0.740 0.020</td><td>0.785 0.011</td></tr><tr><td/><td>RSNA</td><td colspan=\"2\">0.798 0.033 0.806 0.012</td><td>0.681 0.054</td><td>0.523 0.069</td><td>0.554 0.004</td><td>0.711 0.049</td><td>0.747 0.070</td><td>0.767 0.046</td><td>0.732 0.013</td><td colspan=\"2\">0.726 0.025</td><td colspan=\"2\">0.736 0.017</td><td>0.675 0.017</td><td>0.760 0.036</td></tr><tr><td/><td/><td>SmAP</td><td>ABMIL</td><td>DeepGraphSurv</td><td>CLAM</td><td>DSMIL</td><td>PathGCN</td><td>DFTD-MIL</td><td>SmTAP</td><td>TransMIL</td><td colspan=\"2\">SETMIL</td><td colspan=\"2\">GTP</td><td>IIBMIL</td><td>CAMIL</td></tr><tr><td/><td/><td/><td/><td>Without</td><td>global</td><td>interactions</td><td/><td/><td/><td colspan=\"2\">With</td><td colspan=\"2\">global</td><td colspan=\"2\">interactions</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF7": {"text": "Instance F1 (higher is better) for different choices of the feature extractor.", "content": "<table><tr><td>ResNet50+BT</td><td>CAMELYON16 CAMELYON16</td><td>0.580 0.053 0.839 0.053</td><td>0.419 0.029 0.767 0.039</td><td>0.465 0.059 0.771 0.070</td><td colspan=\"2\">0.373 0.054 0.821 0.046 0.255 0.134 0.654 0.203</td><td>0.414 0.119 0.077 0.114</td><td>0.552 0.055 0.742 0.040</td><td>0.552 0.138 0.600 0.067</td><td>0.194 0.090 0.127 0.078</td><td colspan=\"2\">0.159 0.039 0.134 0.267</td><td colspan=\"2\">0.084 0.048 0.037 0.036</td><td>0.295 0.015 0.352 0.100</td><td>0.426 0.055 0.479 0.175</td></tr><tr><td>ViT-B-32</td><td>PANDA</td><td>0.645 0.009</td><td>0.605 0.006</td><td colspan=\"2\">0.642 0.006 0.610 0.005</td><td>0.610 0.004</td><td>0.610 0.023</td><td>0.616 0.013</td><td colspan=\"2\">0.658 0.013 0.630 0.041</td><td colspan=\"2\">0.822 0.012</td><td colspan=\"2\">0.641 0.017</td><td>0.655 0.006</td><td>0.641 0.014</td></tr><tr><td/><td>RSNA</td><td colspan=\"2\">0.494 0.019 0.498 0.021</td><td>0.479 0.043</td><td>0.000 0.000</td><td>0.399 0.031</td><td>0.481 0.039</td><td>0.489 0.033</td><td>0.475 0.034</td><td colspan=\"3\">0.480 0.046 0.467 0.008</td><td colspan=\"2\">0.447 0.021</td><td>0.443 0.010</td><td>0.504 0.025</td></tr><tr><td/><td>CAMELYON16</td><td>0.675 0.077</td><td>0.654 0.067</td><td colspan=\"2\">0.663 0.038 0.584 0.087</td><td>0.290 0.174</td><td>0.371 0.211</td><td>0.591 0.098</td><td>0.630 0.070</td><td>0.196 0.115</td><td colspan=\"2\">0.036 0.021</td><td colspan=\"2\">0.150 0.122</td><td>0.000 0.000</td><td>0.563 0.153</td></tr><tr><td>ResNet50</td><td>PANDA</td><td colspan=\"2\">0.630 0.009 0.611 0.007</td><td>0.641 0.002</td><td>0.621 0.007</td><td>0.592 0.005</td><td>0.608 0.010</td><td>0.617 0.011</td><td>0.606 0.015</td><td>0.622 0.023</td><td colspan=\"2\">0.821 0.022</td><td colspan=\"2\">0.621 0.014</td><td>0.641 0.019 0.615 0.014</td></tr><tr><td/><td>RSNA</td><td>0.473 0.015</td><td colspan=\"2\">0.470 0.031 0.464 0.022</td><td>0.000 0.000</td><td>0.271 0.019</td><td>0.431 0.020</td><td>0.447 0.026</td><td>0.517 0.020</td><td>0.442 0.024</td><td colspan=\"2\">0.405 0.021</td><td colspan=\"2\">0.431 0.013</td><td>0.403 0.014</td><td>0.483 0.024</td></tr><tr><td/><td>CAMELYON16</td><td colspan=\"2\">0.591 0.059 0.428 0.049</td><td>0.595 0.129</td><td>0.406 0.238</td><td>0.155 0.180</td><td>0.150 0.211</td><td>0.563 0.132</td><td>0.581 0.061</td><td>0.174 0.080</td><td colspan=\"2\">0.237 0.058</td><td colspan=\"2\">0.168 0.132</td><td>0.000 0.000</td><td>0.403 0.157</td></tr><tr><td>ResNet18</td><td>PANDA</td><td colspan=\"2\">0.635 0.006 0.602 0.004</td><td>0.581 0.026</td><td>0.568 0.038</td><td>0.598 0.006</td><td>0.526 0.019</td><td>0.637 0.006</td><td>0.622 0.010</td><td colspan=\"3\">0.636 0.008 0.631 0.010</td><td colspan=\"2\">0.636 0.011</td><td>0.645 0.007</td><td>0.621 0.013</td></tr><tr><td/><td>RSNA</td><td colspan=\"2\">0.477 0.014 0.486 0.033</td><td>0.293 0.168</td><td>0.076 0.154</td><td>0.180 0.000</td><td>0.447 0.014</td><td>0.453 0.194</td><td>0.474 0.023</td><td colspan=\"3\">0.471 0.014 0.438 0.027</td><td colspan=\"2\">0.425 0.018</td><td>0.420 0.016</td><td>0.456 0.013</td></tr><tr><td/><td/><td>SmAP</td><td>ABMIL</td><td>DeepGraphSurv</td><td>CLAM</td><td>DSMIL</td><td>PathGCN</td><td>DFTD-MIL</td><td>SmTAP</td><td>TransMIL</td><td colspan=\"2\">SETMIL</td><td colspan=\"2\">GTP</td><td>IIBMIL</td><td>CAMIL</td></tr><tr><td/><td/><td/><td/><td>Without</td><td>global</td><td>interactions</td><td/><td/><td/><td colspan=\"2\">With</td><td colspan=\"2\">global</td><td>interactions</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF8": {"text": "Bag AUROC (higher is better) for different choices of the feature extractor.", "content": "<table><tr><td>ResNet50+BT</td><td>CAMELYON16 CAMELYON16</td><td colspan=\"2\">0.775 0.023 0.976 0.007 0.792 0.022 0.956 0.011</td><td>0.747 0.039 0.870 0.070</td><td>0.832 0.030 0.960 0.029</td><td>0.628 0.063 0.947 0.085</td><td>0.741 0.121 0.575 0.206</td><td>0.801 0.015 0.983 0.010</td><td colspan=\"2\">0.754 0.032 0.976 0.014 0.655 0.086 0.973 0.018</td><td colspan=\"2\">0.469 0.105 0.715 0.155</td><td colspan=\"2\">0.456 0.110 0.748 0.118</td><td>0.791 0.049 0.974 0.002</td><td>0.772 0.034 0.984 0.007</td></tr><tr><td>ViT-B-32</td><td>PANDA</td><td>0.947 0.002</td><td>0.943 0.002</td><td>0.938 0.002</td><td>0.927 0.001</td><td>0.925 0.004</td><td colspan=\"2\">0.945 0.006 0.945 0.001</td><td>0.946 0.004</td><td>0.939 0.003</td><td colspan=\"2\">0.970 0.005</td><td colspan=\"2\">0.945 0.003</td><td>0.939 0.002</td><td>0.947 0.004</td></tr><tr><td/><td>RSNA</td><td>0.897 0.005</td><td colspan=\"2\">0.893 0.007 0.870 0.010</td><td>0.735 0.047</td><td>0.792 0.041</td><td>0.880 0.023</td><td>0.870 0.020</td><td>0.896 0.009</td><td>0.900 0.013</td><td colspan=\"2\">0.895 0.012</td><td colspan=\"2\">0.890 0.015</td><td>0.897 0.006 0.892 0.008</td></tr><tr><td/><td>CAMELYON16</td><td>0.777 0.046</td><td>0.752 0.023</td><td>0.695 0.007</td><td colspan=\"2\">0.775 0.041 0.693 0.036</td><td>0.708 0.064</td><td>0.720 0.031</td><td>0.805 0.057</td><td colspan=\"3\">0.791 0.027 0.657 0.030</td><td colspan=\"2\">0.459 0.056</td><td>0.455 0.042</td><td>0.738 0.039</td></tr><tr><td>ResNet50</td><td>PANDA</td><td colspan=\"2\">0.944 0.001 0.942 0.003</td><td>0.925 0.002</td><td>0.930 0.002</td><td>0.926 0.002</td><td>0.943 0.006</td><td>0.945 0.002</td><td>0.944 0.002</td><td>0.942 0.002</td><td colspan=\"2\">0.977 0.005</td><td colspan=\"2\">0.952 0.002 0.939 0.004</td><td>0.941 0.002</td></tr><tr><td/><td>RSNA</td><td>0.890 0.007</td><td>0.886 0.013</td><td>0.877 0.003</td><td>0.802 0.054</td><td>0.761 0.026</td><td colspan=\"2\">0.890 0.017 0.886 0.009</td><td colspan=\"2\">0.893 0.009 0.885 0.008</td><td colspan=\"2\">0.870 0.008</td><td colspan=\"2\">0.896 0.016</td><td>0.861 0.006</td><td>0.892 0.010</td></tr><tr><td/><td>CAMELYON16</td><td colspan=\"2\">0.729 0.037 0.731 0.030</td><td>0.673 0.017</td><td>0.683 0.082</td><td>0.672 0.110</td><td>0.585 0.180</td><td>0.706 0.022</td><td>0.783 0.056</td><td colspan=\"3\">0.771 0.050 0.628 0.039</td><td colspan=\"2\">0.577 0.075</td><td>0.641 0.012</td><td>0.746 0.041</td></tr><tr><td>ResNet18</td><td>PANDA</td><td>0.943 0.001</td><td>0.933 0.002</td><td>0.837 0.020</td><td>0.893 0.026</td><td>0.921 0.008</td><td>0.848 0.005</td><td>0.940 0.001</td><td>0.946 0.003</td><td>0.933 0.010</td><td colspan=\"2\">0.974 0.003</td><td colspan=\"2\">0.949 0.004 0.931 0.004</td><td>0.938 0.003</td></tr><tr><td/><td>RSNA</td><td>0.888 0.005</td><td colspan=\"2\">0.889 0.005 0.848 0.017</td><td>0.674 0.157</td><td>0.689 0.063</td><td>0.888 0.007</td><td>0.890 0.045</td><td>0.906 0.007</td><td>0.883 0.008</td><td colspan=\"2\">0.869 0.011</td><td colspan=\"2\">0.901 0.008 0.868 0.013</td><td>0.889 0.019</td></tr><tr><td/><td/><td>SmAP</td><td>ABMIL</td><td>DeepGraphSurv</td><td>CLAM</td><td>DSMIL</td><td>PathGCN</td><td>DFTD-MIL</td><td>SmTAP</td><td>TransMIL</td><td colspan=\"2\">SETMIL</td><td colspan=\"2\">GTP</td><td>IIBMIL</td><td>CAMIL</td></tr><tr><td/><td/><td/><td/><td>Without</td><td>global</td><td>interactions</td><td/><td/><td/><td colspan=\"2\">With</td><td colspan=\"2\">global</td><td>interactions</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF9": {"text": "Bag F1 (higher is better) for different choices of the feature extractor.", "content": "<table><tr><td>ResNet50+BT</td><td>CAMELYON16 CAMELYON16</td><td colspan=\"3\">0.701 0.023 0.916 0.016 0.673 0.038 0.606 0.067 0.912 0.027 0.772 0.056</td><td>0.671 0.033 0.897 0.012</td><td>0.308 0.080 0.866 0.136</td><td>0.606 0.082 0.345 0.352</td><td>0.668 0.017 0.937 0.013</td><td colspan=\"2\">0.679 0.044 0.453 0.098 0.948 0.020 0.911 0.028</td><td colspan=\"2\">0.451 0.085 0.471 0.341</td><td colspan=\"2\">0.384 0.105 0.727 0.143</td><td>0.686 0.016 0.922 0.010 0.639 0.039 0.918 0.018</td></tr><tr><td>ViT-B-32</td><td>PANDA</td><td>0.918 0.002</td><td>0.912 0.001</td><td>0.908 0.004</td><td>0.904 0.002</td><td>0.902 0.004</td><td colspan=\"2\">0.914 0.006 0.914 0.002</td><td>0.914 0.004</td><td>0.892 0.024</td><td colspan=\"2\">0.953 0.004</td><td colspan=\"2\">0.912 0.003</td><td>0.893 0.008</td><td>0.917 0.002</td></tr><tr><td/><td>RSNA</td><td>0.805 0.012</td><td>0.788 0.023</td><td>0.776 0.019</td><td>0.000 0.000</td><td>0.683 0.036</td><td>0.776 0.012</td><td>0.798 0.024</td><td>0.807 0.032</td><td>0.719 0.027</td><td colspan=\"2\">0.730 0.014</td><td colspan=\"2\">0.773 0.015</td><td>0.723 0.061</td><td>0.792 0.013</td></tr><tr><td/><td>CAMELYON16</td><td>0.713 0.044</td><td>0.661 0.019</td><td>0.590 0.014</td><td colspan=\"2\">0.676 0.041 0.212 0.118</td><td>0.507 0.177</td><td>0.599 0.043</td><td>0.707 0.020</td><td colspan=\"3\">0.635 0.075 0.013 0.072</td><td colspan=\"2\">0.382 0.076</td><td>0.000 0.000</td><td>0.619 0.039</td></tr><tr><td>ResNet50</td><td>PANDA</td><td>0.918 0.005</td><td>0.912 0.007</td><td>0.905 0.003</td><td>0.904 0.005</td><td>0.907 0.002</td><td>0.915 0.004</td><td>0.917 0.002</td><td>0.914 0.003</td><td>0.905 0.013</td><td colspan=\"2\">0.951 0.011</td><td colspan=\"2\">0.923 0.003 0.889 0.011</td><td>0.913 0.003</td></tr><tr><td/><td>RSNA</td><td>0.788 0.031</td><td colspan=\"2\">0.800 0.024 0.770 0.013</td><td>0.016 0.024</td><td>0.374 0.064</td><td>0.757 0.089</td><td>0.806 0.009</td><td colspan=\"2\">0.809 0.016 0.758 0.045</td><td colspan=\"2\">0.734 0.027</td><td colspan=\"2\">0.807 0.019</td><td>0.667 0.011</td><td>0.811 0.014</td></tr><tr><td/><td>CAMELYON16</td><td colspan=\"2\">0.661 0.056 0.667 0.022</td><td>0.560 0.021</td><td>0.485 0.272</td><td>0.252 0.239</td><td>0.287 0.324</td><td>0.576 0.105</td><td>0.677 0.062</td><td>0.636 0.019</td><td colspan=\"2\">0.540 0.024</td><td colspan=\"2\">0.458 0.082</td><td>0.000 0.000</td><td>0.649 0.054</td></tr><tr><td>ResNet18</td><td>PANDA</td><td>0.915 0.002</td><td colspan=\"2\">0.909 0.001 0.823 0.024</td><td>0.868 0.034</td><td>0.904 0.008</td><td>0.857 0.003</td><td>0.903 0.002</td><td>0.917 0.002</td><td>0.895 0.029</td><td colspan=\"2\">0.946 0.003</td><td colspan=\"2\">0.920 0.003 0.881 0.012</td><td>0.911 0.004</td></tr><tr><td/><td>RSNA</td><td colspan=\"2\">0.787 0.026 0.796 0.011</td><td>0.719 0.036</td><td>0.161 0.291</td><td>0.240 0.012</td><td>0.782 0.064</td><td>0.775 0.282</td><td>0.825 0.026</td><td>0.716 0.031</td><td colspan=\"2\">0.716 0.036</td><td colspan=\"2\">0.805 0.017 0.621 0.050</td><td>0.805 0.028</td></tr><tr><td/><td/><td>SmAP</td><td>ABMIL</td><td>DeepGraphSurv</td><td>CLAM</td><td>DSMIL</td><td>PathGCN</td><td>DFTD-MIL</td><td>SmTAP</td><td>TransMIL</td><td colspan=\"2\">SETMIL</td><td colspan=\"2\">GTP</td><td>IIBMIL</td><td>CAMIL</td></tr><tr><td/><td/><td/><td/><td>Without</td><td>global</td><td>interactions</td><td/><td/><td/><td colspan=\"2\">With</td><td colspan=\"2\">global</td><td>interactions</td></tr></table>", "html": null, "num": null, "type_str": "table"}, "TABREF10": {"text": "The answer NA means that the abstract and introduction do not include the claims made in the paper.• The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.2. LimitationsQuestion: Does the paper discuss the limitations of the work performed by the authors? Answer:[Yes]", "content": "<table><tr><td>1. Claims</td></tr><tr><td>Question: Do the main claims made in the abstract and introduction accurately reflect the</td></tr><tr><td>paper's contributions and scope?</td></tr><tr><td>Answer: [Yes]</td></tr><tr><td>Justification: Our claims are supported by Sec. 3 (unified view of deep MIL methods), by</td></tr><tr><td>Sec. 4 (derivation of the proposed smooth operator Sm), and by Sec. 5 (experimental results).</td></tr><tr><td>Guidelines:</td></tr><tr><td>•</td></tr></table>", "html": null, "num": null, "type_str": "table"}}}}