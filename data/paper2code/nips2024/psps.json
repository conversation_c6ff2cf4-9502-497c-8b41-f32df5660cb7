{"paper_id": "psps", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:28:53.393288Z"}, "title": "Task-Agnostic Machine-Learning-Assisted Inference", "authors": [{"first": "Jiacheng", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Wisconsin-Madison", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Wisconsin-Madison", "location": {}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Machine learning (ML) is playing an increasingly important role in scientific research. In conjunction with classical statistical approaches, ML-assisted analytical strategies have shown great promise in accelerating research findings. This has also opened a whole field of methodological research focusing on integrative approaches that leverage both ML and statistics to tackle data science challenges. One type of study that has quickly gained popularity employs ML to predict unobserved outcomes in massive samples, and then uses predicted outcomes in downstream statistical inference. However, existing methods designed to ensure the validity of this type of post-prediction inference are limited to very basic tasks such as linear regression analysis. This is because any extension of these approaches to new, more sophisticated statistical tasks requires task-specific algebraic derivations and software implementations, which ignores the massive library of existing software tools already developed for the same scientific problem given observed data. This severely constrains the scope of application for post-prediction inference. To address this challenge, we introduce a novel statistical framework named PSPS for task-agnostic ML-assisted inference. It provides a post-prediction inference solution that can be easily plugged into almost any established data analysis routines. It delivers valid and efficient inference that is robust to arbitrary choice of ML model, allowing nearly all existing statistical frameworks to be incorporated into the analysis of ML-predicted data. Through extensive experiments, we showcase our method's validity, versatility, and superiority compared to existing approaches. Our software is available at https://github.com/qlu-lab/psps.\nHistorically, the field of statistics has faced similar types of challenges. Before the advent of resampling-based methods [19] , it used to require task-specific derivation and implementation to obtain the variance of any new estimator. This old problem mirrors the current state of MLassisted inference, where every new task requires non-trivial effort from researchers. However, with resampling-based inference, the need to manually derive variance is reduced. Instead, resampling methods can be universally applied to many estimation problems and easily obtain variance [17] [18] [19] . Inspired by this, we seek a universal approach that incorporates ML-predicted data into any existing data analysis routines while ensuring valid inference results.\nWe introduce a simple protocol named PoSt-Prediction Summary-statistics-based (PSPS) inference (Fig. 1 ). It employs existing analysis routines to generate summary statistics sufficient for ML-assisted inference, and then produces valid and powerful inference results using these statistics. It has several key features:\n• Assumption-lean and data-adaptive: It inherits the theoretical guarantees of validity and efficiency from state-of-the-art ML-assisted inference methods [4, 20, 35] . These guarantees hold with arbitrary ML predictions. • Task-agnostic and simple: Since our method only requires summary statistics from existing analysis routines, it can be easily adapted for many statistical tasks currently unavailable or difficult to implement in ML-assisted inference. • Federated data analysis: It does not need any individual-level data as input. Sharing of privacy-preserving summary statistics is sufficient for real-world scientific collaboration.\n2 Problem formulations\nWe focus on statistical inference problems for the parameter θ * ≡ θ * (P) ∈ R K defined on the joint distribution of (X, Y ) ∼ P, where Y ∈ Y is a scalar outcome and X ∈ X be a K-dimensional vector representing features. We are interested in estimating θ * using labeled data\n, and a pre-trained ML model f (•) : X → Y. Here, f (•) is a black-box function with unknown operating characteristics and can be mis-specified. We also require an algorithm A that inputs the labeled data L and returns a consistent and asymptotically normally distributed estimator θ for θ * . There are three common ways in the literature to estimate θ * :", "pdf_parse": {"paper_id": "psps", "_pdf_hash": "", "abstract": [{"text": "Machine learning (ML) is playing an increasingly important role in scientific research. In conjunction with classical statistical approaches, ML-assisted analytical strategies have shown great promise in accelerating research findings. This has also opened a whole field of methodological research focusing on integrative approaches that leverage both ML and statistics to tackle data science challenges. One type of study that has quickly gained popularity employs ML to predict unobserved outcomes in massive samples, and then uses predicted outcomes in downstream statistical inference. However, existing methods designed to ensure the validity of this type of post-prediction inference are limited to very basic tasks such as linear regression analysis. This is because any extension of these approaches to new, more sophisticated statistical tasks requires task-specific algebraic derivations and software implementations, which ignores the massive library of existing software tools already developed for the same scientific problem given observed data. This severely constrains the scope of application for post-prediction inference. To address this challenge, we introduce a novel statistical framework named PSPS for task-agnostic ML-assisted inference. It provides a post-prediction inference solution that can be easily plugged into almost any established data analysis routines. It delivers valid and efficient inference that is robust to arbitrary choice of ML model, allowing nearly all existing statistical frameworks to be incorporated into the analysis of ML-predicted data. Through extensive experiments, we showcase our method's validity, versatility, and superiority compared to existing approaches. Our software is available at https://github.com/qlu-lab/psps.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "Historically, the field of statistics has faced similar types of challenges. Before the advent of resampling-based methods [19] , it used to require task-specific derivation and implementation to obtain the variance of any new estimator. This old problem mirrors the current state of MLassisted inference, where every new task requires non-trivial effort from researchers. However, with resampling-based inference, the need to manually derive variance is reduced. Instead, resampling methods can be universally applied to many estimation problems and easily obtain variance [17] [18] [19] . Inspired by this, we seek a universal approach that incorporates ML-predicted data into any existing data analysis routines while ensuring valid inference results.", "cite_spans": [{"start": 123, "end": 127, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 574, "end": 578, "text": "[17]", "ref_id": "BIBREF16"}, {"start": 579, "end": 583, "text": "[18]", "ref_id": "BIBREF17"}, {"start": 584, "end": 588, "text": "[19]", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "We introduce a simple protocol named PoSt-Prediction Summary-statistics-based (PSPS) inference (Fig. 1 ). It employs existing analysis routines to generate summary statistics sufficient for ML-assisted inference, and then produces valid and powerful inference results using these statistics. It has several key features:", "cite_spans": [], "ref_spans": [{"start": 101, "end": 102, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "• Assumption-lean and data-adaptive: It inherits the theoretical guarantees of validity and efficiency from state-of-the-art ML-assisted inference methods [4, 20, 35] . These guarantees hold with arbitrary ML predictions. • Task-agnostic and simple: Since our method only requires summary statistics from existing analysis routines, it can be easily adapted for many statistical tasks currently unavailable or difficult to implement in ML-assisted inference. • Federated data analysis: It does not need any individual-level data as input. Sharing of privacy-preserving summary statistics is sufficient for real-world scientific collaboration.", "cite_spans": [{"start": 155, "end": 158, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 159, "end": 162, "text": "20,", "ref_id": "BIBREF19"}, {"start": 163, "end": 166, "text": "35]", "ref_id": "BIBREF34"}], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "2 Problem formulations", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "We focus on statistical inference problems for the parameter θ * ≡ θ * (P) ∈ R K defined on the joint distribution of (X, Y ) ∼ P, where Y ∈ Y is a scalar outcome and X ∈ X be a K-dimensional vector representing features. We are interested in estimating θ * using labeled data", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": ", and a pre-trained ML model f (•) : X → Y. Here, f (•) is a black-box function with unknown operating characteristics and can be mis-specified. We also require an algorithm A that inputs the labeled data L and returns a consistent and asymptotically normally distributed estimator θ for θ * . There are three common ways in the literature to estimate θ * :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Leveraging machine learning (ML) techniques to enhance and accelerate research has become increasingly popular in many scientific disciplines [44] . For example, sophisticated deep learning models have achieved remarkable success in predicting protein structure and interactions, which has the potential to significantly speed up the research process, save costs, and revolutionize the field of structural biology [1, 2, 25] . However, recent studies have pointed out that statistical inference using ML-predicted outcomes may lead to invalid scientific discoveries due to the lack of consideration of ML prediction uncertainty in traditional statistical approaches. To address this, researchers have introduced methods that couple extensive ML predictions with limited gold-standard data to ensure the validity of ML-assisted statistical inference [3, 35, 46] . Despite these advances, current ML-assisted inference methods can only address very basic statistical tasks, including mean estimation, quantile estimation, and linear and logistic regression [3, 35] . While the same mathematical principle behind existing ML-assisted inference methods can be generalized to a broader class of M-estimation problems, specific algebraic derivations and computational implementations are required for each new statistical task. Moreover, many tasks, such as the Wilcoxon rank-sum test, do not fit into the M-estimation framework. These issues pose significant challenges to the broad application of ML-assisted inference across various scientific domains.", "cite_spans": [{"start": 142, "end": 146, "text": "[44]", "ref_id": "BIBREF43"}, {"start": 414, "end": 417, "text": "[1,", "ref_id": "BIBREF0"}, {"start": 418, "end": 420, "text": "2,", "ref_id": "BIBREF1"}, {"start": 421, "end": 424, "text": "25]", "ref_id": "BIBREF24"}, {"start": 849, "end": 852, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 853, "end": 856, "text": "35,", "ref_id": "BIBREF34"}, {"start": 857, "end": 860, "text": "46]", "ref_id": "BIBREF45"}, {"start": 1055, "end": 1058, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 1059, "end": 1062, "text": "35]", "ref_id": "BIBREF34"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• Classical statistical methods apply algorithm A to only labeled data L = (X L , Y L ), and returns the estimator and its estimated variance [ θ L , Var( θ L )]. Valid confidence intervals and hypothesis tests can then be constructed using the asymptotic distribution of the estimator. However, it ignores the unlabeled data and ML prediction.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "• Imputation-based methods treat ML prediction f in the unlabeled data as the observed outcome, and apply algorithm A to U = (X U , f U ). We denote the estimator and estimated variance as [ η U , Var( η U )]. This has been shown to give invalid inference results and false scientific findings [3, 35, 36, 46 ]. • ML-assisted inference methods use both L and U as input. These approaches add a debiasing term in the loss function (or estimating equation) for M-estimation problems, thus removing the bias from the imputation-based estimators and producing results that are statistically valid and universally more powerful compared to classical methods [4, 35, 36] .", "cite_spans": [{"start": 294, "end": 297, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 298, "end": 301, "text": "35,", "ref_id": "BIBREF34"}, {"start": 302, "end": 305, "text": "36,", "ref_id": "BIBREF35"}, {"start": 306, "end": 308, "text": "46", "ref_id": "BIBREF45"}, {"start": 653, "end": 656, "text": "[4,", "ref_id": "BIBREF3"}, {"start": 657, "end": 660, "text": "35,", "ref_id": "BIBREF34"}, {"start": 661, "end": 664, "text": "36]", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "Next, we use an example to provide intuition on ML-assisted inference and our protocol.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Introduction", "sec_num": "1"}, {"text": "We consider the mean estimation problem, where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "θ * = E[Y i ] ≡ arg min θ E[ 1 2 (Y i -θ) 2 ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": ". The classical method only takes the labeled data Y L as input and yields an unbiased and consistent estimator for θ * : θ L = arg min θ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "1 n n i=1 1 2 (Y i -θ) 2 = 1 n n i=1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Y i . The imputation-based method only takes the unlabeled data f U as input and returns η U = arg min θ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "1 N n+N i=n+1 1 2 ( f i -θ) 2 = 1 N n+N i=n+1 f i .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "It is a biased and inconsistent estimator for E[Y i ] if the ML model f is mis-specified.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "To address this, ML-assisted estimator takes both labeled data (Y L , f L ) and unlabeled data f U as input and adds a debiasing term to the loss function to rectify the bias caused by ML imputation [3, 20, 35, 36] :", "cite_spans": [{"start": 199, "end": 202, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 203, "end": 206, "text": "20,", "ref_id": "BIBREF19"}, {"start": 207, "end": 210, "text": "35,", "ref_id": "BIBREF34"}, {"start": 211, "end": 214, "text": "36]", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "θMLA = arg min θ 1 2 { ω0 1 N n+N i=n+1 ( fi -θ) 2 -[ ω0 1 n n i=1 ( fi -θ) 2 - 1 n n i=1 (Yi -θ) 2 ] Debiasing term } = ω0 1 N n+N i=n+1 fi -[ ω0 1 n n i=1 fi - 1 n n i=1 yi]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Debiasing term", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": ", where the modified loss ensures the consistency of the ML-assisted estimator and the weight", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "ω 0 = Cov l [Y, f ]/n Var l [ f ]/n+ Varu[ f ]/N", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "ensures that ML-assisted estimator is no less efficient than the classical estimator with arbitrary ML predictions:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Var( θ MLA ) = Var( θ L ) - Cov[Y, f ] n Var[ f ]+n 2 Var[ f ]/N ≤ Var( θ L ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Our proposed method is motivated by the observation that the sufficient statistics of the ML-assisted estimator θ MLA and its estimated variance Var( θ MLA ) are the following summary statistics:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "θ ss = ( 1 n n i=1 y i , 1 n n i=1 f i , 1 N n+N i=n+1 f i ) and Var( θ ss ) =   Var l [Y ]/n Cov l [Y, f ]/n 0 Cov l [Y, f ]/n Var l [ f ]/n 0 0 0 Var u [ f ]/N  ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Moreover, they can be easily obtained by applying the same algorithm A (mean estimation) to", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "• labeled data with observed outcome", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "A(Y L ) → [ θ L , Var( θ L )] = [ 1 n n i=1 y i , Var l [Y ]/n]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "• labeled data with predicted outcome", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "A( f L ) → [ η L , Var( η L )] = [ 1 n n i=1 f i , Var l [ f ]/n]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "• unlabeled data with predicted outcomeA(", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "f U ) → [ η U , Var( η U )] = [ 1 N n+N i=n+1 f i , Var u [ f ]/N ]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "• bootstrap of labeled data A[(Y L , f L ) q , q = 1, . . . , Q] for estimation of Cov( θ L , η L ) = Cov l [Y, f ]/n. Here, (Y L , f L ) q represents the q-th bootstrap of labeled data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Combining these summary statistics for one-step debiasing ω 0 η U -( ω 0 η L -θ L ) recovers θ MLA .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "To summarize, an algorithm for mean estimation, coupled with resampling, is sufficient for MLassisted mean estimation. This observation inspired us to generalize this protocol for a broad range of tasks. Our protocol illustrated in Fig. 1 only requires three steps: 1) using a pre-trained ML model to predict outcomes for labeled and unlabeled data, 2) applying existing analysis routines to generate summary statistics, and 3) using these statistics in a debiasing procedure to produce statistically valid results in ML-assisted inference.", "cite_spans": [], "ref_spans": [{"start": 237, "end": 238, "text": "1", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Building the intuition with mean estimation", "sec_num": "2.2"}, {"text": "Labeled data", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Outcome", "sec_num": null}, {"text": "One-step debiasing", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Pre-trained ML model", "sec_num": null}, {"text": "Step1: Use pre-trained ML model to predict the gold-standard outcome in both labeled and unlabeled data", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ML-assisted inference", "sec_num": null}, {"text": "Apply the analysis routines to obtain the summary statistics", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Step2:", "sec_num": null}, {"text": "Employ one-step debiasing to the summary statistics ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Step3:", "sec_num": null}, {"text": "𝑿 𝓛", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Pre-trained ML model", "sec_num": null}, {"text": "Figure 1 : Workflow of PSPS for Task-Agnostic ML-Assisted Inference.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "1", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "Summary statistics", "sec_num": null}, {"text": "Our work is closely related to recent methods developed in the literature of ML-assisted inference [3, 4, 20, 35, 37, 46, 56] , and is also related to methods for handling missing data [40, 42] and semi-supervised inference [6, 16, 50, 52] . While current ML-assisted inference methods modify the loss function or the estimating equation, our protocol works directly on the summary statistics. For simple problems such as mean estimation, current methods yield a closed-form solution to the optimization problem. However, for more general statistical tasks, there is no such closed-form solution. Current methods typically require the algebraic form of the loss function, its first-and second-order derivatives, and the variance for the estimator, as well as a newly implemented optimization algorithm to obtain the estimator. We use the logistic regression problem as an example. Here, θ * = arg min θ E[-Y (θX) T -ψ(Xθ)] and", "cite_spans": [{"start": 99, "end": 102, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 103, "end": 105, "text": "4,", "ref_id": "BIBREF3"}, {"start": 106, "end": 109, "text": "20,", "ref_id": "BIBREF19"}, {"start": 110, "end": 113, "text": "35,", "ref_id": "BIBREF34"}, {"start": 114, "end": 117, "text": "37,", "ref_id": "BIBREF36"}, {"start": 118, "end": 121, "text": "46,", "ref_id": "BIBREF45"}, {"start": 122, "end": 125, "text": "56]", "ref_id": "BIBREF55"}, {"start": 185, "end": 189, "text": "[40,", "ref_id": "BIBREF39"}, {"start": 190, "end": 193, "text": "42]", "ref_id": "BIBREF41"}, {"start": 224, "end": 227, "text": "[6,", "ref_id": "BIBREF5"}, {"start": 228, "end": 231, "text": "16,", "ref_id": "BIBREF15"}, {"start": 232, "end": 235, "text": "50,", "ref_id": "BIBREF49"}, {"start": 236, "end": 239, "text": "52]", "ref_id": "BIBREF51"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2.3"}, {"text": "ψ(t) = 1/(1 + exp(-t)). The ML-assisted estimator is θ MLA = arg min θ 1 N n+N i=n+1 ω[-f i θ T X T i - ψ(X i θ)]-{ 1 n n i=1 ω[-f i θ T X T i -ψ(X i θ)]-1 n n i=1 [-Y i θ T X T i -ψ(X i θ)]} with estimated asymptotic variance A -1 V(ω) A -1 , where A = 1 N +n ( n i=1 ψ ′′ (X i θ) X T i X i + n+N i=n+1 ψ ′′ (X i θ) X T i X i ), V(ω) = n N [ ω 2 Var n (ψ ′ (X i θ) -f i )X T i + Cov N +n (1 -ω)ψ ′ (X i θ)X T i + ( ω f i -Y i )X T i ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2.3"}, {"text": "and ω needs to be obtained by optimization to minimize the asymptotic variance. In contrast, our protocol simply applies logistic regression A to", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2.3"}, {"text": "• labeled data with observed outcomes (X L , Y L ) to obtain [ θ L , Var( θ L )] • labeled data with predicted outcomes (X L , f L ) to obtain [ η L , Var( η L )] • unlabeled data with predicted outcomes (X U , f U ) to obtain [ η U , Var( η U )] • bootstrap of labeled data (X L , Y L , f L ) q , q = 1, . . . , Q for Cov( θ L , η L ),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2.3"}, {"text": "and returns ω T 0 η U -(ω T 0 η L -θ L ), where ω 0 = ( Var( η L )+ Var( η U )) -1 Cov( θ L , η L ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2.3"}, {"text": "For each new statistical task, as long as an existing analysis routine can produce an estimator that is asymptotically normally distributed, our protocol can be similarly applied. Additionally, the current mathematical principles guiding ML-assisted inference apply solely to M-estimation [3, 4, 20, 35, 56] . Our protocol extends beyond this limitation, addressing all estimation problems with an asymptotically normally distributed estimator.", "cite_spans": [{"start": 289, "end": 292, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 293, "end": 295, "text": "4,", "ref_id": "BIBREF3"}, {"start": 296, "end": 299, "text": "20,", "ref_id": "BIBREF19"}, {"start": 300, "end": 303, "text": "35,", "ref_id": "BIBREF34"}, {"start": 304, "end": 307, "text": "56]", "ref_id": "BIBREF55"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2.3"}, {"text": "Inference relying solely on summary statistics is widely used in the statistical genetics literature for practical reasons. Summary statistics-based methods have been developed for tasks such as variance component inference and genetic risk prediction [11, 12, 34, 39, 53] . In contrast to our work, these applications do not leverage ML predictions, but instead focus on inference using summary statistics obtained from observed outcomes. An exception is a previous study for valid genome-wide association studies (GWAS) on ML-predicted outcome [36] . However, it focused only on linear regression modeling with application to GWAS. The PSPS framework introduced in this paper aims to extend ML-assisted inference to general statistical tasks.", "cite_spans": [{"start": 252, "end": 256, "text": "[11,", "ref_id": "BIBREF10"}, {"start": 257, "end": 260, "text": "12,", "ref_id": "BIBREF11"}, {"start": 261, "end": 264, "text": "34,", "ref_id": "BIBREF33"}, {"start": 265, "end": 268, "text": "39,", "ref_id": "BIBREF38"}, {"start": 269, "end": 272, "text": "53]", "ref_id": "BIBREF52"}, {"start": 546, "end": 550, "text": "[36]", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2.3"}, {"text": "Our work is also related to semi-supervised learning, resampling-based inference, zero augmentation, and false discovery rate (FDR) control methods. Our protocol is designed for estimation and statistical inference using both labeled and unlabeled data, addressing a different problem from semi-supervised learning [55] , which primarily focuses on prediction. Our protocol is inspired by the core principle of resampling-based inference, which replaces algebraic derivation with computation [19] . The main difference is that we focus on how to use ML to support inference, whereas resamplingbased inference focuses on bias and variance estimation, and type-I error control. The idea of zero augmentation has been used in augmented inverse propensity weighting estimators [38] and in handling unmeasured confounders [48] and missing data for U-statistics [14] . These estimators do not incorporate ML, which is fundamental to our work. We also adapt techniques from the FDR literature [7] [8] [9] [10] . Our unique contribution is to use ML to support FDR control, thereby increasing its statistical power, in contrast to classical methods that rely solely on labeled data.", "cite_spans": [{"start": 315, "end": 319, "text": "[55]", "ref_id": "BIBREF54"}, {"start": 492, "end": 496, "text": "[19]", "ref_id": "BIBREF18"}, {"start": 773, "end": 777, "text": "[38]", "ref_id": "BIBREF37"}, {"start": 817, "end": 821, "text": "[48]", "ref_id": "BIBREF47"}, {"start": 856, "end": 860, "text": "[14]", "ref_id": "BIBREF13"}, {"start": 986, "end": 989, "text": "[7]", "ref_id": "BIBREF6"}, {"start": 990, "end": 993, "text": "[8]", "ref_id": "BIBREF7"}, {"start": 994, "end": 997, "text": "[9]", "ref_id": "BIBREF8"}, {"start": 998, "end": 1002, "text": "[10]", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "Related work", "sec_num": "2.3"}, {"text": "Building on Section 2, we formalized our protocol in Fig. 1 for ML-assisted inference:", "cite_spans": [], "ref_spans": [{"start": 58, "end": 59, "text": "1", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Algorithm 1 PSPS for ML-assisted inference", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Input: A pre-trained ML model f , labeled data L = (X L , Y L ), unlabeled data U = X U 1:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Use the ML model f to predict the outcome in both labeled and unlabeled data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "2: Apply the algorithm A in the analysis routine to • labeled data (X L , Y L ) and obtain [ θ L , Var( θ L )]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "• labeled data (X L , f L ) and obtain [ η L , Var( η L )]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "• unlabeled data with (X U , f U ) and obtain [ η U , Var( η U )]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "• Q bootstrap of labeled data (X L , Y L , f L ) q , q = 1, . . . , Q and obtain Cov( θ L , η L ). 3: Employ one-step debiasing to the summary statistics in step2:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "θ PSPS = ω T 0 η U -( ω T 0 η L -θ L ), where ω 0 = [ Var( η L ) + Var( η U )] -1 Cov( θ L , η L ) and Var( θ PSPS ) = Var( θ L ) - Cov( θ L , η L ) T [ Var( η L ) + Var( η U )] -1 Cov( θ L , η L ) Output: ML-assisted point estimator θ PSPS , standard error Var( θ PSPS ), α-level confidence interval for the k-th coordinate C PSPS α,k = ( θ PSPS k ± z 1-α/2 Var( θ PSPS ) kk ), and (two-sided) p-value 2(1 -Φ(| θPSPS k √ Var( θPSPS) kk ) |))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": ", where Φ is the CDF of the standard normal distribution.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "The only requirements for our protocol are: i) algorithm A, when applied to labeled data (X L , Y L ), returns a consistent and asymptotically normally distributed estimator of θ * ; ii) labeled and unlabeled data are independent and identically distributed. Under these assumptions, the summary statistics have the following asymptotic properties:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n 1/2   θ L -θ * η L -η η U -η   D → N    0 K 0 K 0 K ,   V( θ L ) V( θ L , η L ) 0 V( θ L , η L ) V( η L ) 0 0 0 ρV( η U )      ,", "eq_num": "(1)"}], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "where η ≡ η(P f ) ∈ R K is defined on (X, f ) ∼ P f , V(•) denotes the asymptotic variance and covariance of a estimator, and ρ = n N . The asymptotic approximation gives", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "V( θ L ) ≈ n Var( θ L ), V( θ L , η L ) ≈ n Cov( θ L , η L ), V( η L ) ≈ n Var( η L ) and V( η U ) ≈ N Var( η U ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Here, we do not require η L and η U to be consistent for θ * , thus allows arbitrary ML model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "With the summary statistics following a multivariate normal distribution asymptotically, the debiased estimator", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "θ PSPS = ω T 0 η U -( ω T 0 η L -θ L )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "is consistent for θ * and asymptotically normally distributed (Theorem 1). Therefore, by plugging in a consistent estimator for its asymptotic variance V( θ PSPS ) ≈ n Var( θ PSPS ), valid confidence interval and hypothesis testing can be achieved.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Remark 1. PSPS is more \"task-agnostic\" than existing methods in three aspects:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "1. For M-estimation tasks, currently, only mean and quantile estimation, as well as linear, logistic, and Poisson regression, have been implemented in software tools and are ready for immediate application. For other M-estimation tasks, task-specific derivation of the MLassisted loss functions and asymptotic variance via the central limit theorem are necessary. After that, researchers still need to develop software packages and optimization algorithms to carry out real applications. In contrast, PSPS only requires already implemented algorithms and software designed for classical inference based on labeled data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "2. For problems that do not fall under M-estimation but have asymptotically normally distributed estimators, only PSPS can be applied, and all current methods would fail. The principles behind ML-assisted M-estimation do not extend to these tasks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "3. Even for M-estimation tasks that have already been implemented, PSPS offers the additional advantage of relying solely on summary statistics. The \"task-specific derivations\" refer not only to statistical tasks but also to scientific tasks. Real-world data analysis in any scientific discipline often involves conventions and nuisances that require careful consideration. For example, our work is partly motivated by GWAS [43] . Statistically, GWAS is a linear regression that regresses an outcome on many genetic variants. While the regressionbased statistical foundation is simple, conducting a valid GWAS requires accounting for numerous technical issues, such as sample relatedness (i.e., study participants may be genetically related) and population structure (i.e., unrelated individuals of the same ancestry are both genetically and phenotypically similar, creating confounded associations in GWAS). Sophisticated algorithms and software have been developed to address these complex issues [31] . It will be very challenging if all these important features need to be reimplemented in an ML-assisted GWAS framework. With our PSPS protocol, researchers can utilize existing tools that are highly optimized for genetic applications to perform ML-assisted GWAS. This adaptability is not just limited to GWAS, but is a major feature of our approach across scientific domains. PSPS enables researchers to conduct ML-assisted inference using well-established data analysis routines.", "cite_spans": [{"start": 424, "end": 428, "text": "[43]", "ref_id": "BIBREF42"}, {"start": 999, "end": 1003, "text": "[31]", "ref_id": "BIBREF30"}], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "Remark 2. The \"federated data analysis\" feature of PSPS refers to the fact that we only require summary statistics as input for inference, rather than individual-level raw data (features X and label Y ). For example, consider a scenario where labeled data is in one center and unlabeled data is in another, yet researchers cannot access individual-level data from both centers simultaneously. Under such conditions, current ML-assisted inference, which relies on accessing both labeled and unlabeled data to minimize a joint loss function, is not feasible. However, PSPS circumvents this issue by aggregating summary statistics from multiple centers, thereby performing statistical inference while upholding the privacy of individual-level data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "General protocol for PSPS", "sec_num": "3.1"}, {"text": "In this section, we examine the theoretical properties of PSPS. In what follows, P → denotes convergence in probability and D → denotes convergence in distribution. All proofs are deferred to the Appendix A.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "The first result shows that our proposed estimator is consistent, asymptotically normally distributed, and uniformly better in terms of element-wise asymptotic variance compared with the classical estimator based on labeled data only.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Theorem 1. Assuming equation (1) holds, then θ PSPS P → θ * , and", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "n 1/2 ( θ PSPS -θ * ) D → N 0, V( θ PSPS ) , where V( θ PSPS ) = V( θ L )-V( θ L , η L ) T (V( η L )+ρV( η U )) -1 V( θ L , η L ). Assume the k-th column of V( θ L , η L ) is not a zero vector and at least one of V( η L ) and V( η U ) are positive definite, then V( θ PSPS ) kk ≤ V( θ L ) kk . With V( θ PSPS ) P → V( θ PSPS ), lim n P(θ * k ∈ C PSPS α,k ) = 1 -α.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "V( θ PSPS ) can be obtained by applying the algebraic form of V( θ PSPS ) using the bootstrap estimators for V( θ L ), V( η L ), V( θ L , η L ), and V( η U ). The regularity conditions for consistent bootstrap variance estimation are outlined in Theorem 3.10 (i) of [41] . We also refer readers to [21] , which showed that bootstrap-based variance provides valid but potentially conservative inference.", "cite_spans": [{"start": 266, "end": 270, "text": "[41]", "ref_id": "BIBREF40"}, {"start": 298, "end": 302, "text": "[21]", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "This result indicates that a greater reduction in variance for the ML-assisted estimator is associated with larger values of V( θ L , η L ) and smaller values of V( η L ), V( η U ), and ρ. The variance reduction term", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "[V( θ L , η L ) T (V( η L ) + ρV( η U )) -1 V( θ L , η L )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "] kk can also serve as a metric for selecting the optimal ML model in ML-assisted inference.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Our next result shows that three existing methods, i.e., PPI, PPI++, and PSPA, are asymptotically equivalent to PSPS with different weighting matrices. A broader class for consistent estimator of θ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "* is θ(ω) = ω T η U -(ω T θ L -η L ), where ω is a K × K matrix. The consistency of θ(ω) for θ * only requires ω T ( η U -η L ) P → 0. Since ( η U -η L )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "P → 0, assigning arbitrarily fixed weights for will satisfy the condition. However, the choice of weights influences the efficiency of the estimator as illustrated in Proposition 2 later. Proposition 1. Assuming equation (1) and regularity condition for the asymptotic normality of current ML-assisted estimator holds. For any M-estimation problem, we have", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "n 1 2 ( θ (diag(ω ele )C)-θ PSPA ) D → 0, n 1 2 ( θ (diag(ω tr )C)-θ PPI++ ) D → 0, n 1 2 ( θ (diag(1)C)-θ PPI ) D → 0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Here, ω ele = [ω ele,1 , . . . , ω ele,K ] T ∈ R K and ω ele,k minimizing the k-th diagonal element of V( θ(ω)), ω tr is a scalar used to minimize the trace of V( θ(ω)), and C is a matrix associated with the second derivatives of the loss function in M-estimation, with further details deferred to Appendix A.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "This demonstrates that for M-estimation problems, our method is asymptotically equivalent to PSPA, PPI++, and PPI with the respective weights diag(ω ele )C, diag(ω tr )C, and diag(1)C. Therefore, PSPS can be viewed as a generalization of these existing methods.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Our third result shows that the weights used in the Proposition 1 are not optimal. Instead, our choice of ω 0 represents the optimal smooth combination of ( θ L , η L , η U ) in terms of minimizing the asymptotic variance, while still preserving consistency.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Proposition 2. Suppose n 1/2 (g( θ L , η L , η U ) -θ * ) D → N (0, Σ g ) and g is a smooth function, then Σ g kk ≥ Σ PSPS kk", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "Together with Proposition 1, our results demonstrate that our protocol provides a more efficient estimator compared to existing methods for the M-estimation problems. Furthermore, the applicability of our protocol is not limited to M-estimation and only requires summary statistics as input. It also indicates that in a setting of federated data analysis [24] where individual-level data are not available, PSPS proves to be the optimal approach for combining shared summary statistics. Remark 3. PPI++ [4] employs a power-tuning scalar for variance reduction in ML-assisted inference. This scalar is obtained by minimizing the trace or possibly other scalarization of the estimator's variance-covariance matrix. However, the asymptotic variance of PSPS is always equal to or smaller than that of PPI++, irrespective of the scalarization chosen by researchers. This advantage arises because PSPS utilizes a K × K power tuning matrix, ω, for variance reduction, where K represents the dimensionality of parameters. This matrix facilitates information sharing across different parameter coordinates, thereby enhancing estimation precision. The choice of weighting matrix in PSPS also allows for element-wise variance reduction, reducing each diagonal element of the variance-covariance matrix. In contrast, the single scalar in PPI++ can only target overall trace reduction or variance reduction of a specific element. A detailed example is provided in Appendix B. Only in one-dimensional parameter estimation tasks, such as mean estimation, PPI++ and PSPS exhibit the same asymptotic variance.", "cite_spans": [{"start": 355, "end": 359, "text": "[24]", "ref_id": "BIBREF23"}, {"start": 503, "end": 506, "text": "[4]", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "Theoretical guarantees", "sec_num": "3.2"}, {"text": "We also provide several extensions to ensure the broad applicability of our method.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Extensions", "sec_num": "3.3"}, {"text": "Here, we relax the assumption that the labeled data and unlabeled data are independent. When they are not independent, this can lead to the non-zero covariance between the η L and η U . Consider a broader class of summary statistics asymptotically satisfying", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Labeled data and unlabeled data are not independent", "sec_num": "3.3.1"}, {"text": "n 1/2   θ L -θ * η L -η η U -η   D → N    0 K 0 K 0 K ,     V( θ L ) V( θ L , η L ) V( η L , η U ) V( θ L , η L ) V( η L ) √ ρV( θ L , η U ) V( η L , η U ) √ ρV( θ L , η U ) ρV( η U )       ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Labeled data and unlabeled data are not independent", "sec_num": "3.3.1"}, {"text": "We can similarly employ the one-step debiasing", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Labeled data and unlabeled data are not independent", "sec_num": "3.3.1"}, {"text": "θ no-indep PSPS = ω T 0 η U -ω 0 ( η L -θ) where ω 0 = ( V( θ L , η L ) -V( η L , η U )) T ( V( η L ) + V( η U ) -2 V( θ L , η U )) -1 and Var( θ no-indep PSPS ) = Var( θ L ) - ( V( θ L , η L ) -V( η L , η U )) T (V( η L ) + V( η U ) -2 V( θ L , η U )) -1 ( V( θ L , η L ) -V( η L , η U )).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Labeled data and unlabeled data are not independent", "sec_num": "3.3.1"}, {"text": "The theoretical guarantees of the proposed estimator can be similarly derived by Theorem 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Labeled data and unlabeled data are not independent", "sec_num": "3.3.1"}, {"text": "The other assumption of our approach is that the labeled and unlabeled data are identically distributed so that we can ensure η L -η U P → 0 and validity of PSPS results. To address the potential violation of this assumption, we introduce a sensitivity analysis with hypothesis testing for the null H 0 :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sensitivity analysis for distributional shift between labeled and unlabeled data", "sec_num": "3.3.2"}, {"text": "η L,k = η U ,k with test statistics η L,k -η U ,k √ Var( η L,k )+ Var( η U ,k ) D → N (0, 1) to assess if η L,k and η U ,k are significantly different.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sensitivity analysis for distributional shift between labeled and unlabeled data", "sec_num": "3.3.2"}, {"text": "Here, the subscript k indicates the k-th coordinate. We recommend using p-value < 0.1 as evidence for heterogeneity and caution the interpretation of results from ML-assisted inference.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sensitivity analysis for distributional shift between labeled and unlabeled data", "sec_num": "3.3.2"}, {"text": "The output from PSPS can be used for ML-assisted FDR control, achieving greater statistical power compared to classical FDR control methods that solely rely on labeled data. We refer to our approach as PSPS-BH and PSPS-knockoff. Briefly, PSPS-BH processes the p-value from ML-assisted linear regression through the Benjamini-Ho<PERSON>berg (BH) procedure [9] , while PSPS-knockoff utilizes the ML-assisted debiased <PERSON>so coefficient [23, 51] in the ranking algorithm of knockoff [7] . We present our algorithm in Appendix C and evaluate their performance using experiments in Section 4.", "cite_spans": [{"start": 349, "end": 352, "text": "[9]", "ref_id": "BIBREF8"}, {"start": 427, "end": 431, "text": "[23,", "ref_id": "BIBREF22"}, {"start": 432, "end": 435, "text": "51]", "ref_id": "BIBREF50"}, {"start": 473, "end": 476, "text": "[7]", "ref_id": "BIBREF6"}], "ref_spans": [], "eq_spans": [], "section": "ML-assisted FDR control", "sec_num": "3.3.3"}, {"text": "We have discussed ML-assisted inference with outcomes predicted by ML models. Here, we note that PSPS can also be applied when either features alone are predicted or both features and outcomes are predicted. The key idea is that the difference between point estimators obtained from applying A to predicted features in both labeled and unlabeled datasets is a consistent estimator for zero. This enables zero augmentation for estimators from observed features and outcomes. To implement this, modify step 2 in Algorithm 1 to apply A to predicted features in both labeled and unlabeled data. A similar approach is applicable when both features and outcomes are predicted.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ML-assisted inference with predicted features", "sec_num": "3.3.4"}, {"text": "We conduct simulations to assess the finite sample performance of our method. Our objectives are to demonstrate that 1) PSPS achieves narrower confidence intervals when applied to statistical tasks already implemented in existing ML-assisted methods; 2) when applied to statistical tasks that have not been implemented for ML-assisted inference, PSPS provides confidence intervals with narrower width but correct coverage (indicating higher statistical power) compared to classical approaches rely solely on labeled data; 3) PSPS provides well-calibrated FDR control and achieves higher power compared to classical methods only using labeled data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Numerical experiments and real data application 4.1 Simulations", "sec_num": "4"}, {"text": "Tasks that have been implemented for ML-assisted inference We compare PSPS with the classical method using only labeled data, the imputation-based method using only unlabeled data, and three ML-assisted inference methods PPI, PPI++, and PSPA [3, 4, 35] on mean estimation and linear and logistic regression. We defer the detailed data-generating process to Appendix D. In short, we generated outcome Y i from feature X 1i and X 2i , and obtained the pre-trained random forest that predict Y i using X 1i and X 2i . We have 500 labeled samples (X 1i , Y i , f i ) , and unlabeled samples (X 1i , f i ) ranged from 1,000 to 10,000. Our goal is to estimate the mean of Y i , as well as the linear and logistic regression coefficient between Y i and X 1i . Fig. 2a-c show confidence interval coverage and Fig. 2d-f show confidence interval width. We find that the imputation-based method fails to obtain correct coverage, while all others including PSPS have the correct coverage. PSPS has narrower confidence intervals compared to the classical method and other approaches for ML-assisted inference. Tasks that have not been implemented for ML-assisted inference Next, we consider several commonly used statistical tasks that currently lack implementations for ML-assisted inference including quantile regression [27] , instrumental variable (IV) regression [5] , negative binomial (NB) regression [22] , debiased <PERSON><PERSON> [51] , and the Wilcoxon rank-sum test [28] . Similar to our previous simulations, we utilize labeled data, unlabeled data, and a pre-trained ML model. Detailed simulation settings are deferred to the Appendix D. Our goal is to estimate the regression coefficient between Y i and X 1i for quantile (at quantile level 0.75), IV, and NB regression, between Y i and high dimensional features X i ∈ R 150 for debiased Lasso, and to perform hypothesis testing on the medians of two independent samples Y i |X 1i = 1 and Y i |X 1i = 0 using the Wilcoxon rank-sum test.", "cite_spans": [{"start": 242, "end": 245, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 246, "end": 248, "text": "4,", "ref_id": "BIBREF3"}, {"start": 249, "end": 252, "text": "35]", "ref_id": "BIBREF34"}, {"start": 1310, "end": 1314, "text": "[27]", "ref_id": "BIBREF26"}, {"start": 1355, "end": 1358, "text": "[5]", "ref_id": "BIBREF4"}, {"start": 1395, "end": 1399, "text": "[22]", "ref_id": "BIBREF21"}, {"start": 1417, "end": 1421, "text": "[51]", "ref_id": "BIBREF50"}, {"start": 1455, "end": 1459, "text": "[28]", "ref_id": "BIBREF27"}], "ref_spans": [{"start": 758, "end": 762, "text": "2a-c", "ref_id": "FIGREF6"}, {"start": 806, "end": 810, "text": "2d-f", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "Numerical experiments and real data application 4.1 Simulations", "sec_num": "4"}, {"text": "Fig. 3a-d show confidence interval coverage and Fig. 3f -i show confidence interval width for parameter estimation. Fig. 3e and Fig. 3j show the type-I error and statistical power for the Wilcoxon rank-sum test. We found that the imputation-based method fails to obtain correct confidence interval coverage and shows inflated type-I error, while PSPS and classical method have the correct coverage and wellcalibrated type-I error control. PSPS has narrower confidence intervals width in all settings, and higher statistical power for the Wilcoxon rank-sum test compared to classical methods. Confidence intervals become narrower as unlabeled sample size increases, indicating higher efficiency gain.", "cite_spans": [], "ref_spans": [{"start": 5, "end": 9, "text": "3a-d", "ref_id": null}, {"start": 53, "end": 55, "text": "3f", "ref_id": null}, {"start": 121, "end": 123, "text": "3e", "ref_id": null}, {"start": 133, "end": 135, "text": "3j", "ref_id": null}], "eq_spans": [], "section": "Numerical experiments and real data application 4.1 Simulations", "sec_num": "4"}, {"text": "We evaluate the finite sample performance of PSPS-BH and PSPS-knockoff in controlling the FDR compared with classical and imputation-based methods as baselines. We consider low-dimensional(K < n) and high-dimensional(K > n) linear regressions for PSPS-BH and PSPS-knockoff, respectively. We simulate the data such that only a proportion of the features are truly associated with the outcome. The data generating process is deferred to Appendix D. Our goal is to select the associated features while maintaining the target FDR level. Imputation-based method failed to control FDR in either low-dimensional or high-dimensional settings. Classical approach, PSPS-BH, and PSPS-knockoff effectively controlled in both lowdimensional and high-dimensional settings. PSPS-BH, and PSPS-knockoff achieve higher statistical power compared to the classical method.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FDR control", "sec_num": null}, {"text": "These simulations demonstrate that PSPS outperforms existing methods and can be easily adapted for various statistical tasks not yet implemented in current ML-assisted inference methods.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FDR control", "sec_num": null}, {"text": "We employed our method to carry out ML-assisted quantile regression to identify genetic variants associated with the outcome variability (vQTL) of bone mineral density derived from dual-energy X-ray absorptiometry imaging (DXA-BMD) [33] . DXA-BMD is the primary diagnostic marker for osteoporosis and fracture risk [15, 54] . Identifying vQTL for DXA-BMD can provide insights into the biological mechanisms underlying outcome plasticity and reveal candidate genetic variants involved in potential gene-gene and gene-environment interactions [29, 32, 45, 47, 49] . We focused on total body DXA-BMD, which integrates measurements from multiple skeletal sites. We used data from the UK Biobank [13] , which includes 36,971 labeled and 319,548 unlabeled samples with 9,450,880 genetic variants after quality control. We predicted DXA-BMD values in both labeled and unlabeled samples using SoftImpute [30] with 466 other variables measured in the UK Biobank. Prediction in the labeled sample was implemented through cross-validation to avoid overfitting. The implementation detail is deferred to Appendix D. We used the BH procedure to correct for multiple testing and considered FDR < 0.05 as the significance threshold.", "cite_spans": [{"start": 232, "end": 236, "text": "[33]", "ref_id": "BIBREF32"}, {"start": 315, "end": 319, "text": "[15,", "ref_id": "BIBREF14"}, {"start": 320, "end": 323, "text": "54]", "ref_id": "BIBREF53"}, {"start": 541, "end": 545, "text": "[29,", "ref_id": "BIBREF28"}, {"start": 546, "end": 549, "text": "32,", "ref_id": "BIBREF31"}, {"start": 550, "end": 553, "text": "45,", "ref_id": "BIBREF44"}, {"start": 554, "end": 557, "text": "47,", "ref_id": "BIBREF46"}, {"start": 558, "end": 561, "text": "49]", "ref_id": "BIBREF48"}, {"start": 691, "end": 695, "text": "[13]", "ref_id": "BIBREF12"}, {"start": 896, "end": 900, "text": "[30]", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "Identify vQTLs for bone mineral density", "sec_num": "4.2"}, {"text": "No genetic variants reached statistical significance under the classical method with only labeled data. PSPS identified 108 significant variants with FDR < 0.05 spanning 5 independent loci, showcasing the superior statistical power of PSPS (Fig. E.2 and Table E .1). Notably, these significant vQTL cannot be identified by linear regression [36] , indicating the different genetic mechanisms controlling outcome levels and variability for DXA-BMD.", "cite_spans": [{"start": 341, "end": 345, "text": "[36]", "ref_id": "BIBREF35"}], "ref_spans": [{"start": 260, "end": 261, "text": "E", "ref_id": "TABREF4"}], "eq_spans": [], "section": "Identify vQTLs for bone mineral density", "sec_num": "4.2"}, {"text": "We compared the computational efficiency of PSPS with existing methods using a dataset of 500 labeled and 10,000 unlabeled data points. Results are shown in Table E .2. While PSPS is slower due to resampling, its overall runtime is still relatively short.", "cite_spans": [], "ref_spans": [{"start": 163, "end": 164, "text": "E", "ref_id": "TABREF4"}], "eq_spans": [], "section": "Computational efficiency", "sec_num": "4.3"}, {"text": "We introduced a simple, task-agnostic protocol for ML-assisted inference, with applications across a broad range of statistical tasks. We established the consistency and asymptotic normality of the proposed estimator. We further introduced several extensions to expand the scope of our approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "5"}, {"text": "Through extensive experiments, we demonstrated the superior performance and broad applicability of our method across diverse tasks. Our protocol involves initially generating summary statistics using computationally efficient software tools in scientific data analysis, followed by integration of summary statistics to produce ML-assisted inference results, which achieves high computational efficiency while maintaining statistical validity. Future work could focus on developing a fast resampling algorithm to further improve computational efficiency.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Conclusion", "sec_num": "5"}, {"text": "A.1 Proof the Theorem 1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "Proof. Since", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n 1/2   θ L -θ * η L -η η U -η   D → N    0 K 0 K 0 K ,   V( θ L ) V( θ L , η L ) 0 V( θ L , η L ) V( η L ) 0 0 0 ρV( η U )      ,", "eq_num": "(2)"}], "section": "A Proofs", "sec_num": null}, {"text": "θ L P → θ * and η U -η L P → 0. Given weights ω 0 = ( V( η L ) + ρ V( η U )) -1 V( θ L , η L )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": ", where the variances are consistently estimated, <PERSON><PERSON><PERSON>'s theorem implies that, ω T 0 ( η U -η L )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "P → 0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "Also by <PERSON><PERSON><PERSON>'s theorem,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "θ PSPS = θ L + ω T 0 ( η U -η L ) P → θ * ,", "eq_num": "(3)"}], "section": "A Proofs", "sec_num": null}, {"text": "which completes the proof of consistency.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "By multivariate delta methods, denoting h([x, y, z] T ) = x + ω T 0 (z -y), we have ∇h([x, y, z] T ) = [1, -ω 0 , ω 0 ], therefore by the consistency of ω 0 ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n 1/2 h[   θ L -θ * η L -η η U -η   ] = n 1/2 [ θ L + ω T 0 ( η U -η L )]", "eq_num": "(4)"}], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "D → N (θ * , V( θ L ) -V( θ L , η L ) T (V( η L ) + ρV( η U )) -1 V( θ L , η L )),", "eq_num": "(5)"}], "section": "A Proofs", "sec_num": null}, {"text": "which completes the proof of asymptotic normality.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Denote V( θ L , η L ) :,k as the k-th column of V( θ L , η L ), the k-th diagonal element of V( θ L , η L ) T (V( η L ) + ρV( η U )) -1 V( θ L , η L ) is a quadratic form V( θ L , η L ) T :,k (V( η L ) + ρV( η U )) -1 V( θ L , η L ) :,k .", "eq_num": "(6)"}], "section": "A Proofs", "sec_num": null}, {"text": "Here, by our assumption, (V( η L ) + ρV( η U )) -1 is a positive definite matrix. Therefore, quadratic form V( θ L , η L ) T :,k (V( η L ) + ρV( η U )) -1 V( θ L , η L ) :,k is non-negative and is zero if only all elements of V( θ L , η L ) :,k is zero, which completes the proof of element-wise variance reduction.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A Proofs", "sec_num": null}, {"text": "Proof. Given", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n 1/2   θ L -θ * η L -η η U -η   D → N    0 K 0 K 0 K ,   V( θ L ) V( θ L , η L ) 0 V( θ L , η L ) V( η L ) 0 0 0 ρV( η U )      ,", "eq_num": "(7)"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "the asymptotic variance of θ(ω", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": ") = θ(ω) = θ L + ω T ( η U -η L ) is V( θ(ω)) = V( θ L ) + ω T V( η L )ω + ω T ρV( η U )ω -2ω T V( θ L , η L )", "eq_num": "(8)"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "We first define the M-estimation (Z-estimation) problem. The goal is to estimate a K-dimensional parameter θ * defined through an estimating equation", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E{ψ(Y, X; θ)} = 0,", "eq_num": "(9)"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "where ψ(•, •; θ) is a user-defined function. By the theory of Z-estimation and a recent paper on MLassisted inference [35] , we have", "cite_spans": [{"start": 118, "end": 122, "text": "[35]", "ref_id": "BIBREF34"}], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "V( θ L ) = A -1 θ * M 1 A -1 θ * , V( θ L , η L ) = A -1 η M 4 A -1 θ * , V( η L ) = A -1 η M 2 A -1 η , and V( η U ) = A -1 η M 3 A -1 η .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Here,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "M 1 = Var l [ψ(Y, X; θ * )], M 2 = Var l [ψ( f , X; θ * )], M 3 = Var u [ψ( f , X; θ * )], M 4 = Cov l [ψ(Y, X; θ * ), ψ( f , X; θ * )], A θ * = E[∂ψ(Y, X; θ * )/∂θ], A η = E[∂ψ( f , X; η)/∂η], and ρ = n N .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Rewritten V( θ(ω)) using the above notation, we have", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "V( θ(ω)) = A -1 θ * M 1 A -1 θ * + ω T A -1 η M 2 A -1 η ω + ρω T A -1 η M 3 A -1 η ω -2ω T A -1 η M 4 A -1 θ * (10) = A -1 θ * M 1 A -1 θ * + ω T A -1 η (M 2 + ρM 3 )A -1 η ω -2ω T A -1 η M 4 A -1 θ *", "eq_num": "(11)"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Plug in", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ω = ω 0 = (Var( η L ) + Var( η U )) -1 Cov( θ L , η L ) (12) = (V( η L ) + ρV( η U )) -1 V( θ L , η L ) (13) = (A -1 η M 2 A -1 η + ρA -1 η M 3 A -1 η ) -1 A -1 η M 4 A -1 θ * (14) = A η (M 2 + ρM 3 ) -1 M 4 A -1 θ *", "eq_num": "(15)"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "into the equation above, we have", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "V( θ(ω 0 )) = A -1 θ * M 1 A -1 θ * -A -1 θ * M T 4 A -1 η [A η (M 2 + ρM 3 ) -1 A η ]A -1 η M 4 A -1 θ * (16) = A -1 θ * M 1 A -1 θ * -A -1 θ * M T 4 (M 2 + ρM 3 ) -1 M 4 A -1 θ * (17) = A -1 θ * M 1 -M T 4 (M 2 + ρM 3 ) -1 M 4 A -1 θ * . (", "eq_num": "18"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "To connect our protocol with existing methods, we define ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Σ(ω) = A -1 θ * M 1 A -1 θ * + ω T A -1 θ * M 2 A -1 θ * ω T + ω T ρA -1 θ * M 3 A θ * ω -2ω T A -1 θ * M 4 A -1 θ *", "eq_num": "(19)"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ω * ele := [ω * ele,1 , . . . , ω * ele,K ] ∈ R K where ω * ele,k = arg min ωele,k Σ(ω ele ) kk", "eq_num": "(20)"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "By the theory of PSPA, PPI++, and PPI paper [3, 4, 35] , we have", "cite_spans": [{"start": 44, "end": 47, "text": "[3,", "ref_id": "BIBREF2"}, {"start": 48, "end": 50, "text": "4,", "ref_id": "BIBREF3"}, {"start": 51, "end": 54, "text": "35]", "ref_id": "BIBREF34"}], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n 1/2 ( θ PSPA -θ * ) D → N (0, Σ(diag(ω * ele ))) (", "eq_num": "22"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n 1/2 ( θ PPI++ -θ * ) D → N (0, Σ(diag(ω * tr ))) (", "eq_num": "23"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n 1/2 ( θ PPI -θ * ) D → N (0, Σ(diag(1)))", "eq_num": "(24)"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Based on the proof of Theorem 1, we have the", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n 1/2 ( θ(ω) -θ * ) D → N 0, A -1 θ * M 1 A -1 θ * + ω T A -1 η M 2 A -1 η ω + ρω T A -1 η M 3 A η ω -2ω T A -1 η M 4 A -1 θ * . (25) Plug in ω with diag(ω * ele )A -1 θ * A η , diag(ω * tr )A -1 θ * A η , and diag(1)A -1 θ * A η , we have n 1/2 ( θ(diag(ω * ele )A -1 θ * A η ) -θ * ) D → N (0, Σ(diag(ω * ele ))) (", "eq_num": "26"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n 1/2 ( θ(diag(ω * tr )A -1 θ * A η ) -θ * ) D → N (0, Σ(diag(ω * tr ))) (", "eq_num": "27"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n 1/2 ( θ(diag(1)A -1 θ * A η ) -θ * ) D → N (0, Σ(diag(1)))", "eq_num": "(28)"}], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Denote C = A -1 θ * A η , we have PSPA, PPI++, and PPI is asymptotically equivalent to diag(ω * ele )C, diag(ω * tr )C, and diag(1)C, respectively. This completes the proof.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 Proof the Proposition 1", "sec_num": null}, {"text": "Proof. We apply the first-order Taylor expansion to g( θ L , η L , η U ) around (θ * , η, η):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "g( θ L , η L , η U ) ≈ g(θ * , η, η) + ∇ θ * g(θ * , η, η)( θ L -θ * ) + ∇ η,1 g(θ * , η, η)( η L -η) + ∇ η,2 g(θ * , η, η)( η U -η),", "eq_num": "(29)"}], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "where we used ∇ η,1 and ∇ η,2 to denote the gradient of g(θ * , η, η) w.r.t the first and second η, respectively.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "This can be written as a linear function of (θ * , η, η):", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "g( θ L , η L , η U ) = µ + β T 1 θ L + β T 2 η L + β T 3 η U ,", "eq_num": "(30)"}], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "µ = g(θ * , η, η) -∇ θ * g(θ * , η, η)θ * -2∇ η g(θ * , η, η)η, β 1 = ∇ θ * g(θ * , η, η), β 2 = ∇ η,1 g(θ * , η, η), β 3 = ∇ η,2 g(θ * , η, η).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "Since we require g( θ L , η L , η U ) P → θ * , we have µ = 0, β 1 = 1, and β 2 + β 3 = 0. This leads to", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "g( θ L , η L , η U ) = θ L -β T 3 ( η U -η L ),", "eq_num": "(31)"}], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "Given", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "n 1/2   θ L -θ * η L -η η U -η   D → N    0 K 0 K 0 K ,   V( θ L ) V( θ L , η L ) 0 V( θ L , η L ) V( η L ) 0 0 0 ρV( η U )      ,", "eq_num": "(32)"}], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "we have", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "V(g( θ L , η L , η U )) = V( θ L ) + β T 3 V( η L )β 3 + β T 3 ρV( η U )β 3 -2β T 3 V( θ L , η L ),", "eq_num": "(33)"}], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "which is a quadratic function of β 3 and achieves it minimum when", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "β 3 = (V( η L ) + ρV( η U )) -1 V( θ L , η L ) = ω 0 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "This completes the proof.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.3 Proof of Proposition 2", "sec_num": null}, {"text": "Consider a linear regression with two predictors: Since θPSPS,1 involves two zero-augmentation terms (i.e., -w 1 η1L +w 1 η1U and -ω 12 η2L + ω 12 η2U ), its asymptotic variance should be less than or equal to that of PPI++ with one augmentation term. Therefore, PSPS borrows information from both coordinates, but PPI++ is restricted to information from only the first coordinate. Although the PPI++ can be used under a different scalarization, it still contains one augmentation term.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B An example for understanding the difference between PSPS and PPI++", "sec_num": null}, {"text": "Y ∼ θ 1 X 1 + θ 2 X 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B An example for understanding the difference between PSPS and PPI++", "sec_num": null}, {"text": "Algorithm 2 PSPS-BH for linear regression ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": "Input: Labeled data L = (X L , Y L , f L ), unlabeled data U = (X U , f U ), FDR level q ∈ (0,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": ":= p (k) , where k = max k = 1, . . . , K : p (k) ≤ k K q Output: Discoveries S = k : p k ≤ τ BH q Algorithm 3 PSPS-knockoff with debiased Lasso Input: Labeled data L = (X L , Y L , f L ), unlabeled data U = (X U , f U ), FDR level q ∈ (0, 1).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": "1: Obtain the augmented labeled and unlabeled data as L = (X L , XL , Y L , f L ) and Ũ = (X U , XU , f U ) where XL ← knockoff-sample(X L ) and XU ← knockoff-sample(X U ). 2: Calculate the PSPS debiased Lasso coefficient β PSPS-DLasso ← PSPS-DLasso( L, Ũ)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": "3: W k for k = 1, • • • , K ← knockoff-statistic( β PSPS-DLasso ) 4: Set the cutoff τ knockoff q = t > 0 : #{k:W k ≤-t} #{k:W k ≥t}∨1 ≤ q Output: Discoveries S = k : M k > τ knockoff q", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": "Here, we employ second-order multivariate Gaussian knockoff variables for knockoff-sample and use the difference between the absolute values of the k-th feature and its knockoff coefficient as the knockoff-statistic. Alternative choices for these two steps can also be readily integrated into our algorithm [26] .", "cite_spans": [{"start": 307, "end": 311, "text": "[26]", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "C Algorithms for ML-assisted FDR control", "sec_num": null}, {"text": "Here, we provide the details for our simulation. All our simulation is run in R with version 4.2.1 (2022-06-23) in a MacBook Air with an M1 chip. For all the simulations, the ground truth coefficients are obtained using 5 × 10 4 samples; A pre-trained random forest with 500 trees to grow is obtained from hold-out data. We bootstrap the labeled data for 200 times for covariance estimation. All simulations are repeated 1000 times.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• Mean estimation, Linear regression, and Quantile regression: We simulate the data from", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "Y i = X 1i β 1 + X 2i β 2 + X 2 1i β 2 + X 2 2i β 2 + ϵ i", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": ", where X i1 and X 2i are independent simulated from N (0, 1),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "β 1 = √ 0.08, β 2 is set to be the value such that X 2i β 2 + X 2 1i β 2 + X 2 2i", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "β 2 explains 81% of the outcome variance, and ϵ i is simulated from a mean zero normal distribution with variance such that Var(Y i ) = 1. We use X 1i and X 2i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 1000, 2500, 5000, or 10000.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• Logistic regression: We simulate the data from", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "Y i = 1( Ỹi > median ( Ỹi )), where Ỹi = X 1i β 1 + X 2i β 2 + X 2 1i β 2 + X 2 2i β 2 + ϵ i", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": ", where X i1 and X 2i are independent simulated from N (0, 1), β 1 = √ 0.08, β 2 is set to be the value such that X 2i β 2 + X 2 1i β 2 + X 2 2i β 2 explains 81% of the outcome variance, and ϵ i is simulated from a mean zero normal distribution with variance such that Var( Ỹi ) = 1. We use X 1i and X 2i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 1000, 2500, 5000, or 10000.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• Instrumental variable (IV) regression: We simulate the data by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Z i ∼ N (0, 1),", "eq_num": "(34)"}], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "X 1i = 0.4Z i + δ i , δ i ∼ N (0, 0.84),", "eq_num": "(35)"}], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "X 2i = 0.3Z i + 0.8Y i + γ i , where γ i ∼ N (0, τ γ ), such that Var(X 2i ) = 1,", "eq_num": "(36)"}], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Y i = √ 0.08X 1i + ϵ i , where ϵ i ∼ N (0, τ ϵ ), such that Var(Y i ) = 1", "eq_num": "(37)"}], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "We use X 1i and X 2i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 1000, 2500, 5000, or 10000.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "The Z i is used as a instrument for X 1i . • Negative binomial (NB) regression: We simulate the data by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "X 1i ∼ N (0, 1), X 2i ∼ N (0, 1),", "eq_num": "(38)"}], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "µ i = exp( √ 0.3X 1i + 0.8X 2i )", "eq_num": "(39)"}], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "Y i = NegativeBinomial (s = k, µ = µ i )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": ", where s is the dispersion parameter and µ is the rate.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "We use X 1i and X 2i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 1000, 2500, 5000, or 10000.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• Debiased <PERSON><PERSON>: We simulate the data by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "X 1i , . . . , X 200i ∼ N (0, 1)", "eq_num": "(41)"}], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "θ 1 , . . . , θ 15 = 0.9 √ 15 ; θ 16 , . . . , θ 200 = 0 (42)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Y i = 150 k=1 X ki θ k + ϵ i , ϵ i ∼ N (0, τ ϵ ) such that Var(Y i ) = 1.", "eq_num": "(43)"}], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "We use X 1i , . . . , X 200i as features to predict the Y i in the random forest. We consider labeled data with 100 individuals, and unlabeled data with sample size 1500, 2000, 2500, or 3000.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• Wilcoxon rank-sum test: We simulate the data by", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "X 1i ∼ <PERSON><PERSON>(0.5), X 2i ∼ N (0, 1)", "eq_num": "(44)"}], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Y i = β 1 X 1i + β 2 X 2i + β 2 X 2 2i + ϵ i , ϵ i ∼ N (0, τ ϵ ),", "eq_num": "(45)"}], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "where β 1 = √ 0.01 to power simulation and β 1 = 0 for type-I error simulation, β 2 is set to be the value such that β 2 X 2i + β 2 X 2 2i explains 81% of the outcome variance, and τ ϵ is set to be the value such that Var(Y i ) = 0.We use X 1i and X 2i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 1000, 2500, 5000, or 10000.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• <PERSON><PERSON><PERSON><PERSON> (BH) procedure: We set K = 150 generate the features independently from N (0, Σ), where Σ is a symmetric Toeplitz matrix that has the structure:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Σ =       r 0 r 1 . . . r p-1 r 1 . . . . . . r p-2 . . . . . . . . . . . . r p-1 r p-2 . . . r 0      ", "eq_num": "(46)"}], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "The correlation r is set to be 0.25. We then simulate the outcome Y i = 150 k=1 X ki β k + ϵ i , where we randomly sample 15 β k to be 0.15 and let all other remaining β k to be 0. ϵ i is simulated from a mean-zero normal distribution with variance set to the value such that Var(Y i ) = 1. We further generate Z i = 0.9Y i + 150 k=1 X ki θ k + γ i , where θ k = 0.15 for all k = 1, . . . , 150. We use Z i as features to predict the Y i in the random forest. We consider labeled data with 500 individuals, and unlabeled data with sample size 5000.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "• knockoff: We used the same setting as described in the BH procedure above to generate the data. The only difference is that we set β k = 0.5 for features associated with the outcome and considered labeled data consisting of 100 individuals, along with unlabeled data comprising a sample size of 1000.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D Implementation details D.1 Simulation", "sec_num": null}, {"text": "Our prediction pipeline comprises two components: prediction for unlabeled data and prediction for labeled data. To predict bone mineral density in unlabeled data, we first selected predictive features by 1) calculating the correlation of bone mineral density with 466 other variables (sample size > 200,000 from UK Biobank) using labeled data and 2) selecting the top 50 variables with the highest correlations as inputs for the SoftImpute algorithm [30] to predict bone mineral density in the unlabeled data. For the labeled data, we employ a similar approach but incorporate 10-fold cross-validation to prevent overfitting. We select the predictive variables and train the SoftImpute model using 90% of the labeled data. We then perform predictions on the remaining 10% in each fold and repeat this process 10 times across all folds. Justification: We provided extensive theoretical and experimental evidence to support the main claims in our paper.", "cite_spans": [{"start": 451, "end": 455, "text": "[30]", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "D.2 Identify vQTLs for bone mineral density", "sec_num": null}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Identify vQTLs for bone mineral density", "sec_num": null}, {"text": "• The answer NA means that the abstract and introduction do not include the claims made in the paper. • The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers. • The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings. • It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D.2 Identify vQTLs for bone mineral density", "sec_num": null}, {"text": "Question: Does the paper discuss the limitations of the work performed by the authors?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Justification: We have discussed the limitations and future direction of our paper in Conclusion section of the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper. • The authors are encouraged to create a separate \"Limitations\" section in their paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "• The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be. • The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated. • The authors should reflect on the factors that influence the performance of the approach.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon. • The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size. • If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness. • While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Limitations", "sec_num": "2."}, {"text": "Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Justification: We have provided the code in the Supplementary Material.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "• The answer NA means that paper does not include experiments requiring code. • Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Theory Assumptions and Proofs", "sec_num": "3."}, {"text": "Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the results?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Justification: The detail of the experimental can be found in Appendix D.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "• The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them. • The full details can be provided either with the code, in appendix, or as supplemental material.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experimental Setting/Details", "sec_num": "6."}, {"text": "Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Answer: [Yes] Justification: We reported the confidence interval width and p-value for statistical significance throughout the paper.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The authors should answer \"Yes\" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper. • The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "• The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.) • The assumptions made should be given (e.g., Normally distributed errors). • It should be clear whether the error bar is the standard deviation or the standard error of the mean. • It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified. • For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates). • If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiment Statistical Significance", "sec_num": "7."}, {"text": "Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Justification: The detail of the experiments compute resources can be found in Appendix D.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "• The answer NA means that the paper does not include experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "• The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage. • The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute. • The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Experiments Compute Resources", "sec_num": "8."}, {"text": "Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Answer: [Yes] Justification: We have followed the code Of ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "• The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "• If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics. • The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Code Of Ethics", "sec_num": "9."}, {"text": "Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Answer: [Yes]", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Justification: We have discussed the positive societal impacts of our method is accelerating scientific in the abstract and introduction.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• The answer NA means that there is no societal impact of the work performed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "• Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations. • The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster. • The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology. • If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Broader Impacts", "sec_num": "10."}, {"text": "Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)? Answer: [NA] Justification: Our paper introduced a statistical method and therefore poses no such risks. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper poses no such risks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters. • Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images. • We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort. 12. Licenses for existing assets Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected? Answer: [Yes] Justification: We have cited all paper that produced the code package or dataset. Guidelines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The answer NA means that the paper does not use existing assets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should cite the original paper that produced the code package or dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• The authors should state which version of the asset is used and, if possible, include a URL. • The name of the license (e.g., CC-BY 4.0) should be included for each asset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "• For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided. • If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, paperswithcode.com/datasets has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Safeguards", "sec_num": "11."}, {"text": "38th Conference on Neural Information Processing Systems (NeurIPS 2024).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "Acknowledgements: We gratefully acknowledge research support from the National Institutes of Health (NIH; grant U01 HG012039) and support from the University of Wisconsin-Madison Office of the Chancellor and the Vice Chancellor for Research and Graduate Education with funding from the Wisconsin Alumni Research Foundation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "acknowledgement", "sec_num": null}, {"text": "Answer: [Yes] Justification: The assumptions and a complete (and correct) proof can be found in Section 3 and Appendix A, respectively. Guidelines:• The answer NA means that the paper does not include theoretical results.• All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced. • All assumptions should be clearly stated or referenced in the statement of any theorems.• The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition. • Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material. • Theorems and Lemmas that the proof relies upon should be properly referenced.", "cite_spans": [{"start": 8, "end": 13, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?Answer: [Yes] Justification: The detail of the experimental can be found in Appendix D.", "cite_spans": [{"start": 271, "end": 276, "text": "[Yes]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Experimental Result Reproducibility", "sec_num": "4."}, {"text": "• The answer NA means that the paper does not include experiments.• If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not. • If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable. • Depending on the contribution, reproducibility can be accomplished in various ways.For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed. • While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. , with an open-source dataset or instructions for how to construct the dataset). (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility.In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Guidelines:", "sec_num": null}, {"text": "• For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided. • If this information is not available online, the authors are encouraged to reach out to the asset's creators. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Open access to data and code", "sec_num": "5."}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Accurate structure prediction of biomolecular interactions with alphafold 3", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Adler", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Green", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>more", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Nature", "volume": "", "issue": "", "pages": "1--3", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Accurate structure prediction of biomolecular interactions with alphafold 3. Nature, pages 1-3, 2024.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Openfold: Retraining alphafold2 yields new insights into its learning mechanisms and capacity for generalization", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Nazim", "middle": [], "last": "Bouatta", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sachin", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Qinghui", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Berenberg", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Fisk", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Nature Methods", "volume": "", "issue": "", "pages": "1--11", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. Open- fold: Retraining alphafold2 yields new insights into its learning mechanisms and capacity for generalization. Nature Methods, pages 1-11, 2024.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Prediction-powered inference", "authors": [{"first": "<PERSON>", "middle": [], "last": "Ana<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Fannjiang", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Science", "volume": "382", "issue": "6671", "pages": "669--674", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Prediction-powered inference. Science, 382(6671):669-674, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Ppi++: Efficient predictionpowered inference", "authors": [{"first": "<PERSON>", "middle": ["C"], "last": "Ana<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.01453"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Ppi++: Efficient prediction- powered inference. arXiv preprint arXiv:2311.01453, 2023.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Identification and estimation of local average treatment effects", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Imbens", "suffix": ""}], "year": 1994, "venue": "Econometrica", "volume": "62", "issue": "", "pages": "467--475", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Identification and estimation of local average treatment effects. Econometrica, 62:467-475, 1994.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Semi-supervised linear regression", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sklar", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Berk", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Journal of the American Statistical Association", "volume": "117", "issue": "540", "pages": "2238--2251", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Semi-supervised linear regression. Journal of the American Statistical Association, 117(540): 2238-2251, 2022.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Controlling the false discovery rate via knockoffs", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Candès", "suffix": ""}], "year": 2015, "venue": "The Annals of Statistics", "volume": "43", "issue": "5", "pages": "2055--2085", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Controlling the false discovery rate via knockoffs. The Annals of Statistics, 43(5):2055-2085, 2015.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "A knockoff filter for high-dimensional selective inference", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Candès", "suffix": ""}], "year": 2019, "venue": "The Annals of Statistics", "volume": "47", "issue": "5", "pages": "2504--2537", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. A knockoff filter for high-dimensional selective inference. The Annals of Statistics, 47(5):2504-2537, 2019.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Controlling the false discovery rate: a practical and powerful approach to multiple testing", "authors": [{"first": "Yoav", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hochberg", "suffix": ""}], "year": 1995, "venue": "Journal of the Royal statistical society: series B (Methodological)", "volume": "57", "issue": "1", "pages": "289--300", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON>. Controlling the false discovery rate: a practical and powerful approach to multiple testing. Journal of the Royal statistical society: series B (Methodological), 57(1):289-300, 1995.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "The control of the false discovery rate in multiple testing under dependency", "authors": [{"first": "Yoav", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2001, "venue": "The Annals of Statistics", "volume": "", "issue": "", "pages": "1165--1188", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. The control of the false discovery rate in multiple testing under dependency. The Annals of Statistics, pages 1165-1188, 2001.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Psychiatric Genomics Consortium, Genetic Consortium for Anorexia Nervosa of the Wellcome Trust Case Control Consortium 3, <PERSON><PERSON>, et al. An atlas of genetic correlations across human diseases and traits", "authors": [{"first": "<PERSON>", "middle": [], "last": "Bulik<PERSON>Sullivan", "suffix": ""}, {"first": "<PERSON>", "middle": ["K"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Po-Ru", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Reprogen", "middle": [], "last": "Loh", "suffix": ""}, {"first": "", "middle": [], "last": "Consortium", "suffix": ""}], "year": 2015, "venue": "Nature genetics", "volume": "47", "issue": "11", "pages": "1236--1241", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, ReproGen Consortium, Psychiatric Genomics Consortium, Genetic Consortium for Anorexia Nervosa of the Wellcome Trust Case Control Consortium 3, <PERSON><PERSON>, et al. An atlas of genetic correlations across human diseases and traits. Nature genetics, 47(11): 1236-1241, 2015.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Ld score regression distinguishes confounding from polygenicity in genome-wide association studies", "authors": [{"first": "<PERSON>", "middle": ["K"], "last": "Bulik<PERSON>Sullivan", "suffix": ""}, {"first": "Po-Ru", "middle": [], "last": "Loh", "suffix": ""}, {"first": "<PERSON>", "middle": ["K"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON> ; <PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "Alkes L", "middle": [], "last": "Price", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Nature genetics", "volume": "47", "issue": "3", "pages": "291--295", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Schizophrenia Working Group of the Psychiatric Genomics Consortium, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Ld score regression distinguishes confounding from polygenicity in genome-wide association studies. Nature genetics, 47(3):291-295, 2015.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "The uk biobank resource with deep phenotyping and genomic data", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Band", "suffix": ""}, {"first": "<PERSON>", "middle": ["T"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Delaneau", "suffix": ""}, {"first": "O'", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Nature", "volume": "562", "issue": "7726", "pages": "203--209", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. The uk biobank resource with deep phenotyping and genomic data. Nature, 562(7726):203-209, 2018.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "The correlation-assisted missing data estimator", "authors": [{"first": "I", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Cannings", "suffix": ""}, {"first": "", "middle": [], "last": "Fan", "suffix": ""}], "year": 2022, "venue": "Journal of Machine Learning Research", "volume": "23", "issue": "41", "pages": "1--49", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. The correlation-assisted missing data estimator. Journal of Machine Learning Research, 23(41):1-49, 2022.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Utility of dxa for monitoring, technical aspects of dxa bmd measurement and precision testing", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["F"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "Bone", "volume": "104", "issue": "", "pages": "44--53", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Utility of dxa for monitoring, technical aspects of dxa bmd measurement and precision testing. Bone, 104:44-53, 2017.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Optimal and safe estimation for highdimensional semi-supervised learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>ng", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Journal of the American Statistical Association", "volume": "", "issue": "", "pages": "1--12", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Optimal and safe estimation for high- dimensional semi-supervised learning. Journal of the American Statistical Association, pages 1-12, 2023.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "The jackknife, the bootstrap and other resampling plans", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1982, "venue": "SIAM", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. The jackknife, the bootstrap and other resampling plans. SIAM, 1982.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Bootstrap methods: another look at the jackknife", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1992, "venue": "Breakthroughs in statistics: Methodology and distribution", "volume": "", "issue": "", "pages": "569--593", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Bootstrap methods: another look at the jackknife. In Breakthroughs in statistics: Methodology and distribution, pages 569-593. Springer, 1992.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "An introduction to the bootstrap", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1994, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. An introduction to the bootstrap. Chapman and Hall/CRC, 1994.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Prediction de-correlated inference", "authors": [{"first": "<PERSON>", "middle": [], "last": "Gan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2312.06478"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. Prediction de-correlated inference. arXiv preprint arXiv:2312.06478, 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Bootstrap standard error estimates and inference", "authors": [{"first": "<PERSON><PERSON>g", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1963, "venue": "Econometrica", "volume": "89", "issue": "4", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Bootstrap standard error estimates and inference. Economet- rica, 89(4):1963-1977, 2021.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Negative binomial regression", "authors": [{"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2011, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Negative binomial regression. Cambridge University Press, 2011.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Confidence intervals and hypothesis testing for highdimensional regression", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Javanmard", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "The Journal of Machine Learning Research", "volume": "15", "issue": "1", "pages": "2869--2909", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Confidence intervals and hypothesis testing for high- dimensional regression. The Journal of Machine Learning Research, 15(1):2869-2909, 2014.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Communication-efficient distributed statistical inference", "authors": [{"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "Yun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Journal of the American Statistical Association", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Communication-efficient distributed statistical inference. Journal of the American Statistical Association, 2018.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Highly accurate protein structure prediction with alphafold", "authors": [{"first": "<PERSON>", "middle": [], "last": "Jumper", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Green", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Tunyasuvunakool", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Augustin", "middle": [], "last": "V<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature", "volume": "596", "issue": "7873", "pages": "583--589", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, et al. Highly accurate protein structure prediction with alphafold. Nature, 596(7873):583-589, 2021.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Power of knockoff: The impact of ranking algorithm, augmented design, and symmetric statistic", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jun", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "Yucong", "middle": [], "last": "Ma", "suffix": ""}], "year": 2024, "venue": "Journal of Machine Learning Research", "volume": "25", "issue": "3", "pages": "1--67", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Power of knockoff: The impact of ranking algorithm, augmented design, and symmetric statistic. Journal of Machine Learning Research, 25(3):1-67, 2024.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Quantile regression", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2005, "venue": "", "volume": "38", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Quantile regression, volume 38. Cambridge university press, 2005.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "On a test of whether one of two random variables is stochastically larger than the other. The annals of mathematical statistics", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1947, "venue": "", "volume": "", "issue": "", "pages": "50--60", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. On a test of whether one of two random variables is stochastically larger than the other. The annals of mathematical statistics, pages 50-60, 1947.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Leveraging phenotypic variability to identify genetic interactions in human phenotypes", "authors": [{"first": "<PERSON>", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["V"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "Elemento", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "The American Journal of Human Genetics", "volume": "108", "issue": "1", "pages": "49--67", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> <PERSON>, <PERSON>, and <PERSON>. Leveraging phenotypic variability to identify genetic interactions in human phenotypes. The American Journal of Human Genetics, 108(1):49-67, 2021.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Spectral regularization algorithms for learning large incomplete matrices", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2010, "venue": "The Journal of Machine Learning Research", "volume": "11", "issue": "", "pages": "2287--2322", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON>. Spectral regularization algorithms for learning large incomplete matrices. The Journal of Machine Learning Research, 11:2287-2322, 2010.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Computationally efficient whole-genome regression for quantitative and binary traits", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Leland", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Backman", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature genetics", "volume": "53", "issue": "7", "pages": "1097--1103", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. Computationally efficient whole-genome regression for quantitative and binary traits. Nature genetics, 53(7):1097-1103, 2021.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Identifying genetic loci associated with complex trait variability", "authors": [{"first": "Jiacheng", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Handbook of Statistical Bioinformatics", "volume": "", "issue": "", "pages": "257--270", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Identifying genetic loci associated with complex trait variability. In Handbook of Statistical Bioinformatics, pages 257-270. Springer, 2022.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "A quantile integral linear model to quantify genetic effects on phenotypic variability", "authors": [{"first": "Jiacheng", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yupei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuchang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the National Academy of Sciences", "volume": "119", "issue": "39", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. A quantile integral linear model to quantify genetic effects on phenotypic variability. Proceedings of the National Academy of Sciences, 119(39):e2212959119, 2022.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Quantifying portable genetic effects and improving cross-ancestry genetic prediction with gwas summary statistics", "authors": [{"first": "Jiacheng", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Gefei", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Nature Communications", "volume": "14", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Quantifying portable genetic effects and improving cross-ancestry genetic prediction with gwas summary statistics. Nature Communications, 14(1):832, 2023.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Assumption-lean and data-adaptive post-prediction inference", "authors": [{"first": "Jiacheng", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xinran", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yi<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.14220"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Assumption-lean and data-adaptive post-prediction inference. arXiv preprint arXiv:2311.14220, 2023.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Valid inference for machine learning-assisted genome-wide association studies", "authors": [{"first": "Jiacheng", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yi<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zhong<PERSON>uan", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Xinran", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Tianyuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Nature Genetics", "volume": "", "issue": "", "pages": "1--9", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Valid inference for machine learning-assisted genome-wide association studies. Nature Genetics, pages 1-9, 2024.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Revisiting inference after prediction", "authors": [{"first": "Kesha<PERSON>", "middle": [], "last": "Mo<PERSON>wan<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Journal of Machine Learning Research", "volume": "24", "issue": "394", "pages": "1--18", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>. Revisiting inference after prediction. Journal of Machine Learning Research, 24(394):1-18, 2023.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Estimation of regression coefficients when some regressors are not always observed", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["<PERSON>"], "last": "Rotnitzky", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1994, "venue": "Journal of the American statistical Association", "volume": "89", "issue": "427", "pages": "846--866", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. Estimation of regression coefficients when some regressors are not always observed. Journal of the American statistical Association, 89(427):846-866, 1994.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Improving polygenic prediction in ancestrally diverse populations", "authors": [{"first": "Yunfeng", "middle": [], "last": "<PERSON>uan", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chia-<PERSON>n", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Max", "middle": [], "last": "Lam", "suffix": ""}, {"first": "Zheng<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Nature genetics", "volume": "54", "issue": "5", "pages": "573--580", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Improving polygenic prediction in ancestrally diverse populations. Nature genetics, 54(5):573-580, 2022.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Multiple imputation after 18+ years", "authors": [{"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1996, "venue": "Journal of the American statistical Association", "volume": "91", "issue": "434", "pages": "473--489", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Multiple imputation after 18+ years. Journal of the American statistical Association, 91(434):473-489, 1996.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "The jackknife and bootstrap", "authors": [{"first": "Jun", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Dongsheng", "middle": [], "last": "Tu", "suffix": ""}], "year": 2012, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON> Tu. The jackknife and bootstrap. Springer Science & Business Media, 2012.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Semiparametric theory and missing data", "authors": [{"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2006, "venue": "", "volume": "4", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Anastasios A Tsiatis. Semiparametric theory and missing data, volume 4. Springer, 2006.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Genome-wide association studies", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Qin", "middle": [], "last": "Qin", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["De"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "Okada", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature Reviews Methods Primers", "volume": "1", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Genome-wide association studies. Nature Reviews Methods Primers, 1(1):59, 2021.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Scientific discovery in the age of artificial intelligence", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Kexin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zim<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Payal", "middle": [], "last": "Chandak", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Nature", "volume": "620", "issue": "7972", "pages": "47--60", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, et al. Scientific discovery in the age of artificial intelligence. Nature, 620(7972):47-60, 2023.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Genotype-by-environment interactions inferred from genetic effects on phenotypic variability in the uk biobank", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Fu<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Min", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Science advances", "volume": "5", "issue": "8", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Genotype-by-environment interactions inferred from genetic effects on phenotypic variability in the uk biobank. Science advances, 5(8):eaaw3538, 2019.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Methods for correcting inference based on outcomes predicted by machine learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["H"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["T"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the National Academy of Sciences", "volume": "117", "issue": "48", "pages": "30266--30275", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON>. Methods for correcting inference based on outcomes predicted by machine learning. Proceedings of the National Academy of Sciences, 117(48):30266-30275, 2020.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Variance-quantitative trait loci enable systematic discovery of gene-environment interactions for cardiometabolic serum biomarkers", "authors": [{"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "Franco", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Jenkai", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Han", "middle": [], "last": "Florez", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["K"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Nature Communications", "volume": "13", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, et al. Variance-quantitative trait loci enable systematic discovery of gene-environment interactions for cardiometabolic serum biomarkers. Nature Communications, 13(1):3993, 2022.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Combining multiple observational data sources to estimate causal effects", "authors": [{"first": "Shu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}], "year": 2020, "venue": "Journal of the American Statistical Association", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. Combining multiple observational data sources to estimate causal effects. Journal of the American Statistical Association, 2020.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Identifying loci affecting trait variability and detecting interactions in genome-wide association studies", "authors": [{"first": "<PERSON>", "middle": ["L"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Nature genetics", "volume": "50", "issue": "11", "pages": "1608--1614", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Identifying loci affecting trait variability and detecting interactions in genome-wide association studies. Nature genetics, 50 (11):1608-1614, 2018.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Semi-supervised inference: General theory and estimation of means", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Cai", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON> <PERSON>. Semi-supervised inference: General theory and estimation of means. 2019.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Confidence intervals for low dimensional parameters in high dimensional linear models", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON>", "suffix": ""}], "year": 2014, "venue": "Journal of the Royal Statistical Society Series B: Statistical Methodology", "volume": "76", "issue": "1", "pages": "217--242", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON>. Confidence intervals for low dimensional parameters in high dimensional linear models. Journal of the Royal Statistical Society Series B: Statistical Methodology, 76(1):217-242, 2014.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "High-dimensional semi-supervised learning: in search of optimal inference of the mean", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Biometrika", "volume": "109", "issue": "2", "pages": "387--403", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>. High-dimensional semi-supervised learning: in search of optimal inference of the mean. Biometrika, 109(2):387-403, 2022.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Optimizing and benchmarking polygenic risk scores with gwas summary statistics", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gruenloh", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Yan", "suffix": ""}, {"first": "Yi<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zhong<PERSON>uan", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Jiacheng", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yuchang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2024, "venue": "Genome Biology", "volume": "25", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Optimizing and benchmarking polygenic risk scores with gwas summary statistics. Genome Biology, 25(1):260, 2024.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Whole-genome sequencing identifies en1 as a determinant of bone density and fracture", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Estrada", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Rosello-Diez", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "Chitra", "middle": ["L"], "last": "Dahia", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Park-Min", "suffix": ""}, {"first": "<PERSON>", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Nature", "volume": "526", "issue": "7571", "pages": "112--117", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Whole-genome sequencing identifies en1 as a determinant of bone density and fracture. Nature, 526(7571):112-117, 2015.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Semi-supervised learning literature survey", "authors": [{"first": "<PERSON>", "middle": [], "last": "Xiaojin", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2005, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Xiaojin <PERSON>. Semi-supervised learning literature survey. 2005.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Cross-prediction-powered inference", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Candès", "suffix": ""}], "year": 2024, "venue": "Proceedings of the National Academy of Sciences", "volume": "121", "issue": "15", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. Cross-prediction-powered inference. Proceedings of the National Academy of Sciences, 121(15):e2322083121, 2024.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "For initial submissions, do not include any information that would break anonymity (if applicable)", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "• For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "text": "Figure 2: Simulation for tasks that have been implemented for ML-assisted inference including mean estimation, linear regression, and logistic regression from left to right. Panel a-c present confidence interval coverage and panels d-f present confidence interval width.", "fig_num": "23", "num": null, "uris": null}, "FIGREF1": {"type_str": "figure", "text": "Fig. E.1a-b shows the estimated FDR and Fig. E.1c-d shows the statistical power for different methods.Imputation-based method failed to control FDR in either low-dimensional or high-dimensional settings. Classical approach, PSPS-BH, and PSPS-knockoff effectively controlled in both lowdimensional and high-dimensional settings. PSPS-BH, and PSPS-knockoff achieve higher statistical power compared to the classical method.", "fig_num": null, "num": null, "uris": null}, "FIGREF2": {"type_str": "figure", "text": "ω * tr := arg min ωtr Tr[Σ(ω tr )] where ω tr = [ω tr , . . . , ω tr ] T ∈ R K", "fig_num": null, "num": null, "uris": null}, "FIGREF3": {"type_str": "figure", "text": "Obtain the p-value p k for features k = 1, • • • , K by ML-assisted linear regression PSPS-LR(L, U) 2: Sort the p-values in ascending order p (1) ≤ p (2) ≤ . . . ≤ p (K) 3: Finds the p-value cutoff τ BH", "fig_num": "11", "num": null, "uris": null}, "FIGREF4": {"type_str": "figure", "text": "q", "fig_num": null, "num": null, "uris": null}, "FIGREF5": {"type_str": "figure", "text": "Figure E.1: Simulation for FDR control. Panel a-b shows the estimated FDR level given the expected FDR. Panel c-d shows the power.", "fig_num": "1", "num": null, "uris": null}, "FIGREF6": {"type_str": "figure", "text": "Figure E.2: Manhattan plot of vQTLs for bone mineral density. The X-axis represents chromosomes (CHR), plotted by base pair positions (BP). Each point on the plot indicates a single nucleotide polymorphism (SNP). The Y-axis depicts -log10(p-values).", "fig_num": "2", "num": null, "uris": null}, "TABREF4": {"type_str": "table", "text": "", "num": null, "content": "<table><tr><td colspan=\"4\">Method Linear regression Logistic regression NeurIPS Paper Checklist</td></tr><tr><td>1. Claims</td><td>PSPS PPI</td><td>1.62s 0.024s</td><td>8.27s 0.032s</td></tr><tr><td colspan=\"4\">PPI++ Question: Do the main claims made in the abstract and introduction accurately reflect the 0.031s 0.077s</td></tr><tr><td colspan=\"3\">PSPA paper's contributions and scope? 0.049s</td><td>0.034s</td></tr><tr><td colspan=\"4\">Table E.2: Runtime experiments. Utilizing a dataset with 500 labeled and 10,000 unlabeled data Answer: [Yes]</td></tr><tr><td colspan=\"4\">points, PSPS required 1.62 seconds for linear regression and 8.27 seconds for logistic regression using</td></tr><tr><td colspan=\"4\">200 bootstrap resampling. The computation of one-step debiasing using summary statistics alone</td></tr><tr><td colspan=\"4\">took 0.032 seconds for linear regression and 0.033 seconds for logistic regression. Current methods,</td></tr><tr><td colspan=\"4\">which estimate asymptotic variance via the closed form derived by the Central Limit Theorem instead</td></tr><tr><td colspan=\"4\">of resampling, ranged from 0.024 to 0.049 seconds for linear regression and 0.032 to 0.077 seconds</td></tr><tr><td>for logistic regression.</td><td/><td/><td/></tr><tr><td colspan=\"4\">.1: Significant vQTLs for bone mineral density. Abbreviations: CHR, Chromosome; BP, Base</td></tr><tr><td colspan=\"4\">Pair; SNP, Single Nucleotide Polymorphism; A1, Allele 1 (Effect Allele); A2, Allele 2 (Non-effect</td></tr><tr><td colspan=\"4\">Allele); EAF, Effect Allele Frequency; BETA, Effect Size (Beta Coefficient); SE, Standard Error;</td></tr><tr><td colspan=\"2\">FDR, False Discovery Rate.</td><td/><td/></tr></table>", "html": null}, "TABREF5": {"type_str": "table", "text": "• Please see the NeurIPS code and data submission guidelines (https://nips.cc/ public/guides/CodeSubmissionPolicy) for more details. • While we encourage the release of code and data, we understand that this might not be possible, so \"No\" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark). • The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines (https: //nips.cc/public/guides/CodeSubmissionPolicy) for more details. • The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc. • The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why. • At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).", "num": null, "content": "<table/>", "html": null}}}}