{"iclr2024": [{"source": "poster", "paper": "Generative Judge for Evaluating Alignment", "repo_name": "auto-j", "repo_url": "https://github.com/GAIR-NLP/auto-j"}, {"source": "poster", "paper": "Distributional Preference Learning: Understanding and Accounting for Hidden Context in RLHF", "repo_name": "hidden-context", "repo_url": "https://github.com/cassidylaidlaw/hidden-context"}, {"source": "oral", "paper": "Inherently Interpretable Time Series Classification via Multiple Instance Learning", "repo_name": "MILTimeSeriesClassification", "repo_url": "https://github.com/JAEarly/MILTimeSeriesClassification"}, {"source": "oral", "paper": "iTransformer: Inverted Transformers Are Effective for Time Series Forecasting", "repo_name": "iTransformer", "repo_url": "https://github.com/thuml/iTransformer"}, {"source": "poster", "paper": "Tell Your Model Where to Attend: Post-hoc Attention Steering for LLMs", "repo_name": "PASTA", "repo_url": "https://github.com/QingruZhang/PASTA"}, {"source": "poster", "paper": "Knowledge Distillation Based on Transformed Teacher Matching", "repo_name": "TTM", "repo_url": "https://github.com/zkxufo/TTM"}, {"source": "poster", "paper": "Meaning Representations from Trajectories in Autoregressive Models", "repo_name": "meaning-as-trajectories", "repo_url": "https://github.com/tianyu139/meaning-as-trajectories"}, {"source": "poster", "paper": "A Simple Interpretable Transformer for Fine-Grained Image Classification and Analysis", "repo_name": "INTR", "repo_url": "https://github.com/Imageomics/INTR"}, {"source": "poster", "paper": "VDC: Versatile Data Cleanser based on Visual-Linguistic Inconsistency by Multimodal Large Language Models", "repo_name": "vdc", "repo_url": "https://github.com/zihao-ai/vdc"}, {"source": "poster", "paper": "Vocos: Closing the gap between time-domain and Fourier-based neural vocoders for high-quality audio synthesis", "repo_name": "vocos", "repo_url": "https://github.com/gemelo-ai/vocos"}, {"source": "poster", "paper": "SliceGPT: Compress Large Language Models by Deleting Rows and Columns", "repo_name": "TransformerCompression", "repo_url": "https://github.com/microsoft/TransformerCompression"}, {"source": "poster", "paper": "Beyond Accuracy: Evaluating Self-Consistency of Code Large Language Models with IdentityChain", "repo_name": "IdentityChain", "repo_url": "https://github.com/marcusm117/IdentityChain"}, {"source": "poster", "paper": "Guiding Masked Representation Learning to Capture Spatio-Temporal Relationship of Electrocardiogram", "repo_name": "ST-MEM", "repo_url": "https://github.com/bakqui/ST-MEM"}, {"source": "oral", "paper": "Social Reward: Evaluating and Enhancing Generative AI through Million-User Feedback from an Online Creative Community", "repo_name": "Social-Reward", "repo_url": "https://github.com/Picsart-AI-Research/Social-Reward"}, {"source": "poster", "paper": "Language Model Detectors Are Easily Optimized Against", "repo_name": "llm-detector-evasion", "repo_url": "https://github.com/charlottttee/llm-detector-evasion"}, {"source": "poster", "paper": "Improving protein optimization with smoothed fitness landscapes", "repo_name": "GGS", "repo_url": "https://github.com/kirjner/GGS"}, {"source": "poster", "paper": "SparseFormer: Sparse Visual Recognition via Limited Latent Tokens", "repo_name": "sparseformer", "repo_url": "https://github.com/showlab/sparseformer"}, {"source": "poster", "paper": "AutoVP: An Automated Visual Prompting Framework and Benchmark", "repo_name": "AutoVP", "repo_url": "https://github.com/IBM/AutoVP"}, {"source": "poster", "paper": "Hierarchical Context Merging: Better Long Context Understanding for Pre-trained LLMs", "repo_name": "HOMER", "repo_url": "https://github.com/alinlab/HOMER"}, {"source": "poster", "paper": "SEABO: A Simple Search-Based Method for Offline Imitation Learning", "repo_name": "SEABO", "repo_url": "https://github.com/dmksjfl/SEABO"}, {"source": "poster", "paper": "OpenChat: Advancing Open-source Language Models with Mixed-Quality Data", "repo_name": "openchat", "repo_url": "https://github.com/imoneoi/openchat"}, {"source": "poster", "paper": "Rethinking The Uniformity Metric in Self-Supervised Learning", "repo_name": "WassersteinSSL", "repo_url": "https://github.com/statsle/WassersteinSSL"}, {"source": "poster", "paper": "VONet: Unsupervised Video Object Learning With Parallel U-Net Attention and Object-wise Sequential VAE", "repo_name": "<PERSON><PERSON>", "repo_url": "https://github.com/hnyu/vonet"}, {"source": "poster", "paper": "Efficient Backpropagation with Variance-Controlled Adaptive Sampling", "repo_name": "VCAS", "repo_url": "https://github.com/thu-ml/VCAS"}, {"source": "poster", "paper": "Structuring Representation Geometry with Rotationally Equivariant Contrastive Learning", "repo_name": "CARE", "repo_url": "https://github.com/Sharut/CARE"}, {"source": "poster", "paper": "ControlVideo: Training-free Controllable Text-to-Video Generation", "repo_name": "ControlVideo", "repo_url": "https://github.com/YBYBZhang/ControlVideo"}, {"source": "poster", "paper": "Context-Aware Meta-Learning", "repo_name": "CAML", "repo_url": "https://github.com/cfifty/CAML"}, {"source": "poster", "paper": "RECOMBINER: Robust and Enhanced Compression with Bayesian Implicit Neural Representations", "repo_name": "RECOMBINER", "repo_url": "https://github.com/cambridge-mlg/RECOMBINER/"}, {"source": "poster", "paper": "Peering Through Preferences: Unraveling Feedback Acquisition for Aligning Large Language Models", "repo_name": "sparse_feedback", "repo_url": "https://github.com/Hritikbansal/sparse_feedback"}, {"source": "poster", "paper": "Modulate Your Spectrum in Self-Supervised Learning", "repo_name": "INTL", "repo_url": "https://github.com/winci-ai/INTL"}], "nips2024": [{"source": "oral", "paper": "PACE: marrying generalization in PArameter-efficient fine-tuning with Consistency rEgularization", "repo_name": "PACE", "repo_url": "https://github.com/MaxwellYaoNi/PACE"}, {"source": "oral", "paper": "The Road Less Scheduled", "repo_name": "schedule_free", "repo_url": "https://github.com/facebookresearch/schedule_free"}, {"source": "poster", "paper": "G-Retriever: Retrieval-Augmented Generation for Textual Graph Understanding and Question Answering", "repo_name": "G-Retriever", "repo_url": "https://github.com/XiaoxinHe/G-Retriever"}, {"source": "poster", "paper": "Binarized Diffusion Model for Image Super-Resolution", "repo_name": "BI-DiffSR", "repo_url": "https://github.com/zhengchen1999/BI-DiffSR"}, {"source": "poster", "paper": "Learning to Predict Structural Vibrations", "repo_name": "Learning_Vibrating_Plates", "repo_url": "https://github.com/ecker-lab/Learning_Vibrating_Plates"}, {"source": "poster", "paper": "Attack-Aware Noise Calibration for Differential Privacy", "repo_name": "riskcal", "repo_url": "https://github.com/<PERSON>-<PERSON>/riskcal"}, {"source": "poster", "paper": "Make Your LLM Fully Utilize the Context", "repo_name": "FILM", "repo_url": "https://github.com/microsoft/FILM"}, {"source": "poster", "paper": "Smoothed Energy Guidance: Guiding Diffusion Models with Reduced Energy Curvature of Attention", "repo_name": "SEG-SDXL", "repo_url": "https://github.com/SusungHong/SEG-SDXL"}, {"source": "poster", "paper": "Sm: enhanced localization in Multiple Instance Learning for medical imaging classification", "repo_name": "SmMIL", "repo_url": "https://github.com/Franblueee/SmMIL"}, {"source": "poster", "paper": "AutoTimes: Autoregressive Time Series Forecasters via Large Language Models", "repo_name": "AutoTimes", "repo_url": "https://github.com/thuml/AutoTimes"}, {"source": "poster", "paper": "End-to-End Ontology Learning with Large Language Models", "repo_name": "ollm", "repo_url": "https://github.com/andylolu2/ollm"}, {"source": "poster", "paper": "Scaling transformer neural networks for skillful and reliable medium-range weather forecasting", "repo_name": "stormer", "repo_url": "https://github.com/tung-nd/stormer"}, {"source": "oral", "paper": "Autoregressive Image Generation without Vector Quantization", "repo_name": "mar", "repo_url": "https://github.com/LTH14/mar"}, {"source": "oral", "paper": "Adaptive Randomized Smoothing: Certified Adversarial Robustness for Multi-Step Defences", "repo_name": "adaptive-randomized-smoothing", "repo_url": "https://github.com/ubc-systopia/adaptive-randomized-smoothing"}, {"source": "poster", "paper": "Generalizable Person Re-identification via Balancing Alignment and Uniformity", "repo_name": "BAU", "repo_url": "https://github.com/yoonkicho/BAU"}, {"source": "poster", "paper": "Universal Neural Functionals", "repo_name": "universal_neural_functional", "repo_url": "https://github.com/AllanYang<PERSON>/universal_neural_functional"}, {"source": "poster", "paper": "Are Self-Attentions Effective for Time Series Forecasting?", "repo_name": "CATS", "repo_url": "https://github.com/dongbeank/CATS"}, {"source": "poster", "paper": "xMIL: Insightful Explanations for Multiple Instance Learning in Histopathology", "repo_name": "xMIL", "repo_url": "https://github.com/bifold-pathomics/xMIL"}, {"source": "poster", "paper": "Leveraging Environment Interaction for Automated PDDL Translation and Planning with Large Language Models", "repo_name": "llm-pddl-planning", "repo_url": "https://github.com/BorealisAI/llm-pddl-planning"}, {"source": "poster", "paper": "Task-Agnostic Machine Learning-Assisted Inference", "repo_name": "psps", "repo_url": "https://github.com/qlu-lab/psps"}, {"source": "poster", "paper": "Make Continual Learning Stronger via C-Flat", "repo_name": "C-Flat", "repo_url": "https://github.com/WanNaa/C-Flat"}, {"source": "poster", "paper": "DARG: Dynamic Evaluation of Large Language Models via Adaptive Reasoning Graph", "repo_name": "DARG", "repo_url": "https://github.com/SALT-NLP/DARG"}, {"source": "poster", "paper": "AsyncDiff: Parallelizing Diffusion Models by Asynchronous Denoising", "repo_name": "AsyncDiff", "repo_url": "https://github.com/czg1225/AsyncDiff"}, {"source": "poster", "paper": "You Only Look Around: Learning Illumination Invariant Feature for Low-light Object Detection", "repo_name": "YOLA", "repo_url": "https://github.com/MingboHong/YOLA"}, {"source": "poster", "paper": "MutaPLM: Protein Language Modeling for Mutation Explanation and Engineering", "repo_name": "MutaPLM", "repo_url": "https://github.com/PharMolix/MutaPLM"}, {"source": "poster", "paper": "Advancing Training Efficiency of Deep Spiking Neural Networks through Rate-based Backpropagation", "repo_name": "rate-based-backpropagation", "repo_url": "https://github.com/Tab-ct/rate-based-backpropagation"}, {"source": "poster", "paper": "Improved off-policy training of diffusion samplers", "repo_name": "gfn-diffusion", "repo_url": "https://github.com/GFNOrg/gfn-diffusion"}, {"source": "poster", "paper": "Navigating the Effect of Parametrization for Dimensionality Reduction", "repo_name": "ParamRepulsor", "repo_url": "https://github.com/hyhuang00/ParamRepulsor"}, {"source": "poster", "paper": "Long-Range Feedback Spiking Network Captures Dynamic and Static Representations of the Visual Cortex under Movie Stimuli", "repo_name": "SNN-Neural-Similarity-Movie", "repo_url": "https://github.com/Grasshlw/SNN-Neural-Similarity-Movie"}, {"source": "poster", "paper": "InfLLM: Training-Free Long-Context Extrapolation for LLMs with an Efficient Context Memory", "repo_name": "InfLLM", "repo_url": "https://github.com/thunlp/InfLLM"}], "icml2024": [{"source": "oral", "paper": "SAMformer: Unlocking the Potential of Transformers in Time Series Forecasting with Sharpness-Aware Minimization and Channel-Wise Attention", "repo_name": "sam<PERSON>", "repo_url": "https://github.com/romilbert/samformer"}, {"source": "poster", "paper": "Autoformalizing Euclidean Geometry", "repo_name": "LeanEuclid", "repo_url": "https://github.com/loganrjmurphy/LeanEuclid"}, {"source": "poster", "paper": "Recurrent Distance Filtering for Graph Representation Learning", "repo_name": "GRED", "repo_url": "https://github.com/skeletondyh/GRED"}, {"source": "poster", "paper": "CosPGD: an efficient white-box adversarial attack for pixel-wise prediction tasks", "repo_name": "cospgd", "repo_url": "https://github.com/shashankskagnihotri/cospgd"}, {"source": "poster", "paper": "Token-level Direct Preference Optimization", "repo_name": "Token-level-Direct-Preference-Optimization", "repo_url": "https://github.com/Vance0124/Token-level-Direct-Preference-Optimization"}, {"source": "oral", "paper": "BayOTIDE: Bayesian Online Multivariate Time Series Imputation with Functional Decomposition", "repo_name": "BayOTIDE", "repo_url": "https://github.com/xuangu-fang/BayOTIDE"}, {"source": "poster", "paper": "CurBench: Curriculum Learning Benchmark", "repo_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "repo_url": "https://github.com/THUMNLab/CurBench"}, {"source": "poster", "paper": "Exploring the Low-Pass Filtering Behavior in Image Super-Resolution", "repo_name": "LPFInISR", "repo_url": "https://github.com/RisingEntropy/LPFInISR"}, {"source": "poster", "paper": "Towards Efficient Exact Optimization of Language Model Alignment", "repo_name": "exact-optimization", "repo_url": "https://github.com/haozheji/exact-optimization"}, {"source": "poster", "paper": "On the Effectiveness of Supervision in Asymmetric Non-Contrastive Learning", "repo_name": "Sup-ANCL", "repo_url": "https://github.com/JH-Oh-23/Sup-ANCL"}, {"source": "poster", "paper": "Drug Discovery with Dynamic Goal-aware Fragments", "repo_name": "GEAM", "repo_url": "https://github.com/SeulLee05/GEAM"}, {"source": "poster", "paper": "Fool Your (Vision and) Language Model With Embarrassingly Simple Permutations", "repo_name": "FoolyourVLLMs", "repo_url": "https://github.com/ys-zong/FoolyourVLLMs"}, {"source": "poster", "paper": "Image Restoration Through Generalized Ornstein-Uhlenbeck Bridge", "repo_name": "GOUB", "repo_url": "https://github.com/Hammour-steak/GOUB"}, {"source": "poster", "paper": "Timer: Generative Pre-trained Transformers Are Large Time Series Models", "repo_name": "Large-Time-Series-Model", "repo_url": "https://github.com/thuml/Large-Time-Series-Model"}, {"source": "poster", "paper": "Mitigating Oversmoothing Through Reverse Process of GNNs for Heterophilic Graphs", "repo_name": "reverse-gnn", "repo_url": "https://github.com/ml-postech/reverse-gnn"}, {"source": "poster", "paper": "Scribble-Supervised Semantic Segmentation with Prototype-based Feature Augmentation", "repo_name": "PFA", "repo_url": "https://github.com/TranquilChan/PFA"}, {"source": "poster", "paper": "ConvNet vs Transformer, Supervised vs CLIP: Beyond ImageNet Accuracy", "repo_name": "Beyond-INet", "repo_url": "https://github.com/kirill-vish/Beyond-INet"}, {"source": "oral", "paper": "CLIF: Complementary Leaky Integrate-and-Fire Neuron for Spiking Neural Networks", "repo_name": "Complementary-LIF", "repo_url": "https://github.com/HuuYuLong/Complementary-LIF"}, {"source": "oral", "paper": "FiT: Flexible Vision Transformer for Diffusion Model", "repo_name": "FiT", "repo_url": "https://github.com/whlzy/FiT"}, {"source": "oral", "paper": "Decomposing Uncertainty for Large Language Models through Input Clarification Ensembling", "repo_name": "llm_uncertainty", "repo_url": "https://github.com/UCSB-NLP-Chang/llm_uncertainty"}, {"source": "oral", "paper": "SparseTSF: Modeling Long-term Time Series Forecasting with *1k* Parameters", "repo_name": "SparseTSF", "repo_url": "https://github.com/lss-1138/SparseTSF"}, {"source": "oral", "paper": "Sample-specific Masks for Visual Reprogramming-based Prompting", "repo_name": "SMM", "repo_url": "https://github.com/tmlr-group/SMM"}, {"source": "poster", "paper": "Boundary Exploration for Bayesian Optimization With Unknown Physical Constraints", "repo_name": "BE-CBO", "repo_url": "https://github.com/yunshengtian/BE-CBO"}, {"source": "poster", "paper": "Listwise Reward Estimation for Offline Preference-based Reinforcement Learning", "repo_name": "LiRE", "repo_url": "https://github.com/chwoong/LiRE"}, {"source": "poster", "paper": "Graph Distillation with Eigenbasis Matching", "repo_name": "GDEM", "repo_url": "https://github.com/liuyang-tian/GDEM"}, {"source": "poster", "paper": "Temporal Spiking Neural Networks with Synaptic Delay for Graph Reasoning", "repo_name": "GRSNN", "repo_url": "https://github.com/pkuxmq/GRSNN"}, {"source": "poster", "paper": "Position: <PERSON><PERSON>, Unsupervised Time Series Anomaly Detection?", "repo_name": "QuoVadisTAD", "repo_url": "https://github.com/ssarfraz/QuoVadisTAD"}, {"source": "poster", "paper": "Neural SPH: Improved Neural Modeling of Lagrangian Fluid Dynamics", "repo_name": "neuralsph", "repo_url": "https://github.com/tumaer/neuralsph"}, {"source": "poster", "paper": "Self-Play Fine-Tuning Converts Weak Language Models to Strong Language Models", "repo_name": "SPIN", "repo_url": "https://github.com/uclaml/SPIN"}, {"source": "poster", "paper": "Unveiling and Harnessing Hidden Attention Sinks: Enhancing Large Language Models without Training through Attention Calibration", "repo_name": "ACT", "repo_url": "https://github.com/GATECH-EIC/ACT"}]}