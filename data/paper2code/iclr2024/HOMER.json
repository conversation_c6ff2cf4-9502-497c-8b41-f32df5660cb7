{"paper_id": "HOMER", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:48:40.916119Z"}, "title": "HIERARCHICAL CONTEXT MERGING: BETTER LONG CONTEXT UNDERSTANDING FOR PRE-TRAINED LLMS", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Song", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Oh", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Sang<PERSON><PERSON>", "middle": [], "last": "Mo", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of Michigan", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Carnegie Mellon University", "location": {}}, "email": ""}, {"first": "Sukmin", "middle": [], "last": "Yun", "suffix": "", "affiliation": {"laboratory": "", "institution": "Hanyang University ERICA", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ha", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Large language models (LLMs) have shown remarkable performance in various natural language processing tasks. However, a primary constraint they face is the context limit, i.e., the maximum number of tokens they can process. Previous works have explored architectural changes and modifications in positional encoding to relax the constraint, but they often require expensive training or do not address the computational demands of self-attention. In this paper, we present Hierarchical cOntext MERging (HOMER), a new training-free scheme designed to overcome the limitations. HOMER uses a divide-and-conquer algorithm, dividing long inputs into manageable chunks. Each chunk is then processed collectively, employing a hierarchical strategy that merges adjacent chunks at progressive transformer layers. A token reduction technique precedes each merging, ensuring memory usage efficiency. We also propose an optimized computational order reducing the memory requirement to logarithmically scale with respect to input length, making it especially favorable for environments with tight memory restrictions. Our experiments demonstrate the proposed method's superior performance and memory efficiency, enabling the broader use of LLMs in contexts requiring extended context. Code is available at https://github.com/alinlab/HOMER.", "pdf_parse": {"paper_id": "HOMER", "_pdf_hash": "", "abstract": [{"text": "Large language models (LLMs) have shown remarkable performance in various natural language processing tasks. However, a primary constraint they face is the context limit, i.e., the maximum number of tokens they can process. Previous works have explored architectural changes and modifications in positional encoding to relax the constraint, but they often require expensive training or do not address the computational demands of self-attention. In this paper, we present Hierarchical cOntext MERging (HOMER), a new training-free scheme designed to overcome the limitations. HOMER uses a divide-and-conquer algorithm, dividing long inputs into manageable chunks. Each chunk is then processed collectively, employing a hierarchical strategy that merges adjacent chunks at progressive transformer layers. A token reduction technique precedes each merging, ensuring memory usage efficiency. We also propose an optimized computational order reducing the memory requirement to logarithmically scale with respect to input length, making it especially favorable for environments with tight memory restrictions. Our experiments demonstrate the proposed method's superior performance and memory efficiency, enabling the broader use of LLMs in contexts requiring extended context. Code is available at https://github.com/alinlab/HOMER.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "In recent years, large language models (LLMs) have performed exceptionally in various natural language processing tasks (OpenAI, 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2023) . Using this capability, multiple emerging applications are using LLMs as a central component. However, LLMs have a fundamental constraint in their context limit, which means the maximum number of input tokens they can process. The ability to handle long contexts is important for real-world applications: chatbots might need to interpret extensive chat histories, while the user could task code comprehension models to process extensive codebases.", "cite_spans": [{"start": 120, "end": 134, "text": "(OpenAI, 2023;", "ref_id": null}, {"start": 135, "end": 156, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "A significant challenge in overcoming the context limit is addressing the quadratic computational burden of the self-attention mechanism. Prior works have attempted to reduce the computational cost by altering the model architecture, such as introducing sparse attention (<PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2020) or linearized attention (<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020) . Yet, such methods are often not scalable (<PERSON><PERSON> et al., 2022) , and more importantly, they often require extensive model training, making them difficult to use for large-scale models that are prevalent today.", "cite_spans": [{"start": 271, "end": 291, "text": "(<PERSON> et al., 2019;", "ref_id": "BIBREF5"}, {"start": 292, "end": 313, "text": "<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF0"}, {"start": 338, "end": 359, "text": "(<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF15"}, {"start": 360, "end": 387, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF14"}, {"start": 431, "end": 449, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "To overcome this issue, recent works have focused on strategies to extend the context limit of pretrained state-of-the-art LLMs. However, their major focus has been modifying the positional encoding (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) , which does not address the quadratic computational cost of self-attention, leaving the efficiency concern unaddressed. Reducing the complexity of pre-trained LLMs remains an important yet underexplored research question. HOMER maintains reasonable performance for context lengths up to 32K tokens. Detailed comparisons with more baselines are provided in Table 1 . (b) The memory requirement for processing long inputs. (c) Average inference time required for generating 100 tokens conditioned on various context lengths. All efficiency measurements are done with a single A100 GPU. The baselines include plain Llama, PI, NTK, and YaRN. Peak memory usage of the baselines at 64k is an estimated value, as they do not fit in a single A100 GPU. Detailed results are provided in Table 5 and Appendix E.", "cite_spans": [{"start": 199, "end": 218, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF4"}, {"start": 219, "end": 237, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF21"}], "ref_spans": [{"start": 601, "end": 602, "text": "1", "ref_id": "TABREF2"}, {"start": 1022, "end": 1023, "text": "5", "ref_id": "TABREF6"}], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In this paper, we introduce HOMER (Hierarchical cOntext MERging), a novel technique designed to extend the context limit while ensuring computational efficiency. HOMER employs a divide-andconquer approach, dividing the long input into manageable chunks. Unlike previous methodologies (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) , HOMER does not process these chunks independently. Instead, it employs a hierarchical merging strategy, progressively merging adjacent chunks as they are processed along the transformer layers (see Figure 2 for its illustration). To ensure computational efficiency, apply token reduction before each merging stage.", "cite_spans": [{"start": 284, "end": 303, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF30"}, {"start": 304, "end": 325, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF1"}], "ref_spans": [{"start": 533, "end": 534, "text": "2", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Furthermore, HOMER can be applied to pre-trained LLMs without any further finetuning. This can be beneficial for practical use scenarios where model finetuning is infeasible, such as in environments with limited computing resources. Also, data preparation may present another challenge for finetuning due to the scarcity of coherent texts with tens of thousands of tokens. For instance, specialized text data should be prepared to finetune an instruction-finetuned or chat-finetuned model without severely losing its desired properties.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Through extensive evaluation on downstream tasks and perplexity measurements, we demonstrate that HOMER can effectively extend pre-trained LLMs to handle long inputs beyond their context limits. We first verify the effectiveness of our method on various downstream tasks, including passkey retrieval and question answering. We further demonstrate the fluency of HOMER by measuring perplexity on long documents. Finally, we highlight the computational efficiency of HOMER as presented in Figure 1b and Figure 1c . In all experiments, we illustrate that HOMER can be used with conventional positional encoding scaling techniques (<PERSON> et al., 2023; bloc97, 2023; <PERSON><PERSON> et al., 2023) , and shows improved performance when used on top of these approaches.", "cite_spans": [{"start": 627, "end": 646, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF4"}, {"start": 647, "end": 660, "text": "bloc97, 2023;", "ref_id": null}, {"start": 661, "end": 679, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF21"}], "ref_spans": [{"start": 494, "end": 496, "text": "1b", "ref_id": "FIGREF2"}, {"start": 508, "end": 510, "text": "1c", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In summary, our contributions are as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• We present hierarchical context merging: a memory-efficient context limit extension technique, that can be used with pre-trained LLMs without additional training.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• We assess the effectiveness of HOMER through experiments on long inputs. In passkey retrieval experiments, HOMER shows 80.4% retrieval accuracy for 32k inputs, whereas even the best-performing baseline shows only 22.4% accuracy. HOMER also improves the prediction accuracy on question answering by 3% (32.7% → 35.7%), presenting its capability to perform complex reasoning about the content in the extended context length. In language modeling experiments, HOMER is the only method showing low perplexity on inputs up to 64k tokens, while the baselines exhibit severe performance degradation for inputs over 32k tokens.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• We demonstrate the efficiency of our approach and analyze the source of computational savings. Utilizing an optimized computation order, memory requirement scales logarithmically with respect to the input sequence length, reducing the memory requirement by over 70%. • We show that our method is compatible with the conventional RoPE-scaling methods in a plug-in manner, and using them together achieves an additional performance gain.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Long-range transformers. Classical methods for long-range transformers primarily focus on reducing the quadratic computational cost of self-attention, such as sparse attention (<PERSON> et al., 2019; <PERSON> et al., 2019; <PERSON> et al., 2019; <PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2020) , or linearized attention (<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2020; <PERSON><PERSON><PERSON><PERSON> et al., 2021) . However, these approaches fundamentally change the underlying architecture, and it has not been proven to be scalable for large models (<PERSON><PERSON> et al., 2022) .", "cite_spans": [{"start": 176, "end": 194, "text": "(<PERSON> et al., 2019;", "ref_id": "BIBREF7"}, {"start": 195, "end": 214, "text": "<PERSON> et al., 2019;", "ref_id": "BIBREF5"}, {"start": 215, "end": 232, "text": "<PERSON> et al., 2019;", "ref_id": "BIBREF23"}, {"start": 233, "end": 250, "text": "<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF22"}, {"start": 251, "end": 272, "text": "<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF0"}, {"start": 273, "end": 293, "text": "<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF32"}, {"start": 320, "end": 341, "text": "(<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF15"}, {"start": 342, "end": 369, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF14"}, {"start": 370, "end": 388, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF29"}, {"start": 389, "end": 414, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF6"}, {"start": 552, "end": 570, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "Extension of LLM context lengths. As the context limit of LLMs has become a critical problem, a line of concurrent works emerged, focusing on efficiently extending the context length of LLMs, with most works focusing on Llama (<PERSON><PERSON><PERSON><PERSON> et al., 2023) . Most works focus on scaling the Rotary Position Embedding (RoPE) (<PERSON> et al., 2021) . <PERSON> et al. (2023) and <PERSON><PERSON><PERSON><PERSON><PERSON> (2023) concurrently discovered the Position Interpolation method (PI), which involves linearly interpolating the position ids. bloc97 ( 2023) suggested an NTK-aware scaling method (NTK) which further alters the base of RoPE. <PERSON><PERSON> et al. (2023) further extended NTK-aware scaling, suggesting another RoPE scaling method, YaRN. Several works additionally alter the attention mechanism by either applying a mask (<PERSON> et al., 2023) or setting an upper bound on the distance between tokens (Su, 2023) .", "cite_spans": [{"start": 226, "end": 248, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF28"}, {"start": 316, "end": 333, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF26"}, {"start": 336, "end": 354, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF4"}, {"start": 595, "end": 613, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF21"}, {"start": 779, "end": 797, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF10"}, {"start": 855, "end": 865, "text": "(Su, 2023)", "ref_id": "BIBREF25"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "While all methods are known to work without further training, we consider PI, NTK, and YaRN as our main baselines as they are directly compatible with Flash Attention 2 (Dao, 2023) , easily enabling memory-efficient inference on long inputs. We also emphasize that our work is orthogonal to these work, and can be further applied on top of these methods to further improve performance.", "cite_spans": [{"start": 169, "end": 180, "text": "(Dao, 2023)", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "Divide-and-conquer approaches. Approaches to overcome the quadratic computation problem in long context modeling while using the same quadratic self-attention mechanism are to divide the long input into multiple chunks, and most methods process the chunks independently. Inspired by Fusion-in-Decoder (Izacard & Grave, 2020) , SLED (<PERSON><PERSON><PERSON> et al., 2023) independently encodes multiple chunks and feeds all of them to the decoder. Similarly, <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON> et al., 2023) introduces a k-NN search on the encoder outputs, reducing the number of visible tokens at inference time. Retrieval-augmented LLMs including Memorizing transformers (<PERSON> et al., 2022) and LongMem (<PERSON> et al., 2023 ) take a similar approach of individually forwarding each chunk, and retrieve the cached hidden states for further use. Most of these methods, except for <PERSON><PERSON>iform<PERSON>, require method-specific finetuning.", "cite_spans": [{"start": 301, "end": 324, "text": "(Izacard & Grave, 2020)", "ref_id": "BIBREF13"}, {"start": 332, "end": 351, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF12"}, {"start": 452, "end": 474, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF1"}, {"start": 640, "end": 657, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 670, "end": 688, "text": "(<PERSON> et al., 2023", "ref_id": "BIBREF30"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "Token reduction. Token reduction methods have been widely studied in the field of efficient vision transformers. The key idea of these methods is to progressively reduce the number of tokens in order to reduce computation, resulting in more efficient training and inference. Two main approaches in this direction are either pruning the redundant tokens (<PERSON> et al., 2022) or merging them (<PERSON><PERSON> et al., 2022) . To the best of our knowledge, this is the first work to apply token reduction to extend the context limit of large language models.", "cite_spans": [{"start": 353, "end": 373, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF16"}, {"start": 390, "end": 410, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "In this section, we illustrate the detailed procedure of our proposed method, Hierarchical cOntext MERging (HOMER); a novel and efficient method for extending the context limit of large language models (LLMs). As visualized in Figure 2 , HOMER consists of two steps: (i) hierarchical merging of the intermediate hidden states, which we call context embeddings, and (ii) further refinement of the lower-layer embeddings by propagative refinement to produce a compact, fixed-length embedding for each layer, which can be seamlessly integrated as a typical kv-cache (Chen, 2022) . We first introduce the key idea of hierarchical merging in Section 3.1. Then, we explain propagative refinement in Section 3.2. Finally in Section 3.3, we introduce an optimized computation order to further reduce the memory requirement to scale logarithmically with the input length. In the intermediate layers, we merge multiple chunks by concatenation, forming a new, merged chunk. To keep the chunk length bounded, we apply token reduction on the original chunks to make them shorter, prior to merging. This process is repeated until all chunks are merged into a single chunk. Finally, we further refine the lower-layer embeddings to get a compact fixed-length, layerwise embedding. The embedding can then be used like a standard kv-cache (Chen, 2022) .", "cite_spans": [{"start": 563, "end": 575, "text": "(Chen, 2022)", "ref_id": null}, {"start": 1321, "end": 1333, "text": "(Chen, 2022)", "ref_id": null}], "ref_spans": [{"start": 234, "end": 235, "text": "2", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "HIERARCHICAL CONTEXT MERGING", "sec_num": "3"}, {"text": "We propose a divide-and-conquer approach to handle the quadratic computation of self-attention more efficiently. We divide the long input into multiple chunks and process the local chunks with the usual self-attention. Although some previous studies have adopted a similar approach (<PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) , they independently handle each chunk, possibly restricting the richness of the intermediate embeddings as they only have access to local information. In contrast, we progressively merge adjacent chunks as they move through the transformer layers, enabling the chunks to see each other. However, naïvely concatenating the adjacent chunks lengthens the resulting chunk and adds a significant computational burden. Thus we propose to use a token reduction technique to shorten each chunk before merging.", "cite_spans": [{"start": 282, "end": 301, "text": "(<PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF12"}, {"start": 302, "end": 323, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "HIERARCHICAL MERGING OF CONTEXT EMBEDDINGS", "sec_num": "3.1"}, {"text": "By hierarchically reducing and merging the context embeddings, our method bypasses the quadratic computations required by the self-attention mechanism. This approach not only aims at computational efficiency but also preserves the richness of the context. The detailed process of hierarchical context merging is carried out as follows.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIERARCHICAL MERGING OF CONTEXT EMBEDDINGS", "sec_num": "3.1"}, {"text": "Division of long context into multiple chunks. The first step of our method is to divide the long context into uniform chunks. However, simply slicing the input into chunks encounters issues in the network's initial layers where each chunk cannot see each other. This approach restricts most tokens from accessing the starting instructions, harming the resulting embeddings' quality. Moreover, the tokens at the end miss the global context, which is essential for generating subsequent tokens. We address this by attaching the initial and concluding parts of the prompt to every segment (i.e., treating them as shared prefixes and suffixes), ensuring each chunk contains the instruction and the ending tokens.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIERARCHICAL MERGING OF CONTEXT EMBEDDINGS", "sec_num": "3.1"}, {"text": "Token reduction on individual chunks. To keep the resulting chunk's length short after merging, we adopt token reduction techniques, which have been widely studied in the field of efficient vision transformers. For vision transformers (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) , dropping the tokens that receive minimal attention from the [CLS] token (i.e. the classification token) is known to be a simple and effective token pruning method (<PERSON><PERSON><PERSON> et al., 2023) . Inspired by this, we propose to prune the tokens receiving minimal attention from the final token in each chunk. If the chunks contain affixes, we do not prune the tokens corresponding to the affixes.", "cite_spans": [{"start": 235, "end": 261, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF9"}, {"start": 427, "end": 448, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "HIERARCHICAL MERGING OF CONTEXT EMBEDDINGS", "sec_num": "3.1"}, {"text": "In practice, we identified a position bias in simple attention-based pruning where tokens near the end often receive higher attention weights. To rectify this, We incorporate a calibration technique (<PERSON> et al., 2021) . By averaging the attention weights of the last token with respect to the tokens at each position ahead, we derive the bias logits. These bias logits are subtracted from the attention logits during the token reduction to refine token pruning. In summary, the final token pruning is performed by pruning a fixed number of tokens according to the significance score s sig defined as follows:", "cite_spans": [{"start": 199, "end": 218, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "HIERARCHICAL MERGING OF CONTEXT EMBEDDINGS", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "s i sig := l i att -l dist(i) bias ,", "eq_num": "(1)"}], "section": "HIERARCHICAL MERGING OF CONTEXT EMBEDDINGS", "sec_num": "3.1"}, {"text": "where s i sig denotes the significance score of a token at position i, l i att denotes the token's attention logit, and l dist(i) bias denotes the bias logit corresponding to the token's distance from the final token, dist(i).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIERARCHICAL MERGING OF CONTEXT EMBEDDINGS", "sec_num": "3.1"}, {"text": "Merging chunk embeddings. After shortening, adjacent chunks are concatenated to form a unified chunk. This iterative process of reduction and merging across layers ensures individual chunks converge into a single chunk at the final layers. If the chunks include affixes, direct concatenation might lead to redundancy; we address this by simply averaging the duplicates.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIERARCHICAL MERGING OF CONTEXT EMBEDDINGS", "sec_num": "3.1"}, {"text": "Handling position ids. Management of position ids is an important design choice for our approach. While dynamically scaling the position ids through conventional methods like PI, NTK, and YaRN is viable, these techniques tend to underperform with increased scale factors, being less effective for extended contexts. To circumvent this issue, we reuse the same position ids across different chunks. For affixes, we ensure that corresponding tokens in different chunks are assigned the same ids for consistency across the chunks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIERARCHICAL MERGING OF CONTEXT EMBEDDINGS", "sec_num": "3.1"}, {"text": "As depicted in Figure 2 , the hierarchical context merging produces embeddings characterized by a trapezoidal shape. The higher-layer embeddings are concise, while the lower-layer ones remain extended. To further reduce the computational burden for lower layers, we introduce an additional refinement step after token reduction, called propagative refinement.", "cite_spans": [], "ref_spans": [{"start": 22, "end": 23, "text": "2", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "PROPAGATIVE REFINEMENT OF LOWER-LAYER EMBEDDINGS", "sec_num": "3.2"}, {"text": "The process is straightforward: when a token is pruned in the upper layers, the corresponding tokens are also pruned in the lower-layer embeddings. Therefore, the pruning decision of the upper layers propagates back to the lower layers. The synchronized pruning across layers results in shorter, uniform embeddings for each layer. For better understanding, we have added a detailed illustration in Appendix D demonstrating the process step-by-step. The rationale behind this is an intuition that the upper layers have a better ability to identify the important tokens. Thus, we apply pruning in the upper layers and reflect them in the lower layers. After performing hierarchical merging and propagative refinement, we end up with standardized, fixed-length embeddings for every layer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PROPAGATIVE REFINEMENT OF LOWER-LAYER EMBEDDINGS", "sec_num": "3.2"}, {"text": "Using the refined embeddings for further generation. Conventional implementation of autoregressive language models often cache the key and value embeddings in order to avoid redundant computation. This technique is commonly known as kv-caching (Chen, 2022) . As the refined embeddings have the same length for every layer, they can easily be integrated with the kv-cache implementation by simply replacing it for the generation process.", "cite_spans": [{"start": 244, "end": 256, "text": "(Chen, 2022)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "PROPAGATIVE REFINEMENT OF LOWER-LAYER EMBEDDINGS", "sec_num": "3.2"}, {"text": "In typical Transformer models, all tokens at a given layer are computed in parallel. Following this paradigm, a direct implementation of HOMER would also process multiple chunks concurrently. While such implementation of HOMER inherently requires linear memory with respect to the input length, we propose a more optimized computation order that allows the memory requirement to scale logarithmically. This efficiency is achieved by strategically reordering the processing steps during the hierarchical merging.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "COMPUTATION ORDER OPTIMIZATION FOR MEMORY-LIMITED ENVIRONMENTS", "sec_num": "3.3"}, {"text": "While representing each chunk as a node, the hierarchical context merging process can be conceptualized as a traversal on the binary tree from leaves to the root. By adopting the depth-first search (DFS) algorithm to the computation sequence while executing the propagative refinement, we can achieve a computation cost of a logarithmic scale with respect to the length of the input sequence. For clarity, a pseudo-code representation is provided in Algorithm 1 and Figure 3 . A comprehensive proof of the memory requirement can be found in Appendix A. Through this approach, extensive inputs can be processed even in resource-constrained setups.", "cite_spans": [], "ref_spans": [{"start": 473, "end": 474, "text": "3", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "COMPUTATION ORDER OPTIMIZATION FOR MEMORY-LIMITED ENVIRONMENTS", "sec_num": "3.3"}, {"text": "In this section, we demonstrate the effectiveness of the proposed method, HOMER through extensive experiments. Section 4.1 contains the passkey retrieval experiments, originally suggested by <PERSON><PERSON><PERSON><PERSON> & <PERSON> (2023) . This shows our method's ability to utilize the long context to handle downstream tasks. Section 4.2 contains experiments on question answering. This shows the model's capability to handle more complex and challenging tasks. Section 4.3 demonstrates that HOMER remains fluent, even when conditioned on very long contexts. This is done by measuring perplexity on long documents from PG-19 dataset (<PERSON> et al., 2019) . Section 4.4 contains ablation study on the key components that make HOMER effective. Finally in Section 4.5, we analyze the memory efficiency of our method.", "cite_spans": [{"start": 191, "end": 216, "text": "Mohtashami & Jaggi (2023)", "ref_id": "BIBREF18"}, {"start": 614, "end": 632, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "Common setup and baselines. We select Llama-2 as our base model, as it is the most widely used and the strongest open-source large language model. We use the pretrained models for language modeling experiments, and the chat model for evaluation on downstream tasks, which include passkey retrieval and question answering.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "Recent works on positional encoding interpolation have shown their ability to extend Llama's context limit without training. We set Position Interpolation (PI) (kaiokendev, 2023), NTK-aware scaling (bloc97, 2023) , and YaRN (<PERSON><PERSON> et al., 2023) as our main baselines. As these models scale the positional encoding by a constant factor, we define their context limit as the original context limit (4k tokens for Llama-2) multiplied by the scaling factor. In practice, NTK and YaRN are known to be able to process slightly shorter context than the defined context limit (<PERSON><PERSON> et al., 2023) .", "cite_spans": [{"start": 198, "end": 212, "text": "(bloc97, 2023)", "ref_id": null}, {"start": 224, "end": 243, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF21"}, {"start": 567, "end": 586, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "For each task, we report the performance of HOMER applied on plain Llama. To further emphasize that our method is orthogonal to the positional encoding scaling methods, and can be applied on top of them, we additionally show the performance of HOMER combined with the best-performing baseline for each task.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "In this section, we investigate if HOMER can effectively leverage the long context to handle downstream tasks. We evaluate this on the passkey retrieval task, originally proposed by <PERSON><PERSON><PERSON><PERSON> & <PERSON> (2023) . In this task, the model is asked to retrieve a random number (called passkey) hidden inside distracting texts. The task is widely used to evaluate the maximum context length that the model can effectively handle.", "cite_spans": [{"start": 182, "end": 207, "text": "Mohtashami & Jaggi (2023)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "PASSKEY RETRIEVAL", "sec_num": "4.1"}, {"text": "To evaluate the performance at different input lengths, we evaluate the models with inputs of lengths 4k, 8k, 16k, and 32k tokens. We report the retrieval accuracy in Table 1 . The result demonstrates that HOMER successfully maintains a high accuracy of around 80% for context length up to 32k tokens which is 8 times longer than the pre-trained context length, while significantly outperforming every baseline. Furthermore, it is also evident that the performance can be further improved by applying HOMER on top of YaRN, the best-performing baseline. For baselines with limited context length, the input documents are clipped to fit in the context limit.", "cite_spans": [], "ref_spans": [{"start": 173, "end": 174, "text": "1", "ref_id": "TABREF2"}], "eq_spans": [], "section": "PASSKEY RETRIEVAL", "sec_num": "4.1"}, {"text": "For NTK and YaRN, we further clip the documents to be 3/4 of their context limit, as they are only capable of handling inputs slightly shorter than the claimed context limit. This observation can also be found in Section 4.1, as NTK and YaRN models could not handle inputs that are as long as their context limit. For HOMER experiments, we feed the full context into the model as HOMER has no hard limit on the maximum context length.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PASSKEY RETRIEVAL", "sec_num": "4.1"}, {"text": "We report the prediction accuracy in Table 2 . As evident from the table, HOMER effectively extends the context limit, enjoying over 3% of accuracy gain compared to plain Llama. The performance is further improved when applied on top of the best-performing positional encoding scaling method (NTK), achieving 38.8% accuracy. This demonstrates that language models extended with HOMER could potentially perform more sophisticated reasoning based on the extended context.", "cite_spans": [], "ref_spans": [{"start": 43, "end": 44, "text": "2", "ref_id": "TABREF3"}], "eq_spans": [], "section": "PASSKEY RETRIEVAL", "sec_num": "4.1"}, {"text": "In this section, we investigate the language modeling fluency of HOMER using the perplexity metric.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LANGUAGE MODELING", "sec_num": "4.3"}, {"text": "To this end, we sample 25 long documents from the PG-19 dataset (<PERSON> et al., 2019) and measure the perplexity on documents truncated to specified evaluation lengths.", "cite_spans": [{"start": 64, "end": 82, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "LANGUAGE MODELING", "sec_num": "4.3"}, {"text": "The core of our methodology is the compression of long context into short embeddings. Aligned with this premise, perplexity is measured iteratively: preceding contexts are condensed with HOMER, and the perplexity of the subsequent segment is deduced based on these compressed contexts. Throughout this procedure, the initial 4k tokens were evaluated using unmodified models, with subsequent tokens assessed in 2k token increments. In every experiment, the last 100 tokens of the input are treated as a suffix.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LANGUAGE MODELING", "sec_num": "4.3"}, {"text": "As illustrated in Table 3 , HOMER maintains minimal perplexity values across long documents spanning up to 64k tokens. A more fine-grained perplexity plot is provided in Appendix F. While all other methods either suffer from significant degradation beyond certain thresholds (attributed to In this section, we demonstrate the effectiveness of the design choices made for our method. Specifically, we focus on (1) the proposed token pruning criteria and (2) the method for refining the lowerlayer embeddings after applying hierarchical merging. Following the settings of Section 4.1, we compared the retrieval accuracy of each candidate.", "cite_spans": [], "ref_spans": [{"start": 24, "end": 25, "text": "3", "ref_id": "TABREF4"}], "eq_spans": [], "section": "LANGUAGE MODELING", "sec_num": "4.3"}, {"text": "Effectiveness of our token pruning method. To reduce redundant tokens in the intermediate Transformer layers, we define a calibrated significance score based on the attention weights each token receives from the last token in the chunk. In the pruning procedure, K tokens with the lowest significance scores are dropped. We demonstrate the effectiveness of this criteria by comparing it to a simple baseline, which randomly selects which token to drop. We additionally report the performance with uncalibrated pruning criteria to further emphasize the effectiveness of significance weight calibration. As illustrated in Table 4a , the use of attention-based significance scores and calibration provide an effective proxy for determining the importance of given tokens.", "cite_spans": [], "ref_spans": [{"start": 626, "end": 628, "text": "4a", "ref_id": "TABREF5"}], "eq_spans": [], "section": "LANGUAGE MODELING", "sec_num": "4.3"}, {"text": "Effectiveness of propagative refinement. Another key component of our method is the refinement of the lower-layer embeddings, described as propagative refinement. To evaluate its effectiveness, we compare its performance with three alternative approaches (i) not refining the lower-layer embeddings, (ii) gathering random tokens, and (iii) gathering tokens according to their significance score at each layer. As illustrated in Table 4b , propagative refinement achieves the best performance. We credit this to the ability of upper transformer layers to understand high-level information, with their attention weights successfully representing token significance. By selectively providing more significant tokens, propagative pruning reduces computation while improving performance. ", "cite_spans": [], "ref_spans": [{"start": 434, "end": 436, "text": "4b", "ref_id": "TABREF5"}], "eq_spans": [], "section": "LANGUAGE MODELING", "sec_num": "4.3"}, {"text": "In this section, we discuss the computational efficiency offered by our methodology, with a primary focus on memory efficiency. We first demonstrate the computational efficiency of our method by measuring the peak GPU memory usage while processing long inputs. In the following part of the section, we discuss the four key mechanisms that bring efficiency gains.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "COMPUTATIONAL EFFICIENCY", "sec_num": "4.5"}, {"text": "The peak memory usage for HOMER and baselines is illustrated in Table 5 . Note that we report a single number for all baselines (Plain Llama, PI, NTK, YaRN) because the baselines only modify the positional making no difference in the peak GPU memory usage. For a fair comparison, all methods are tested with Flash Attention 2 (Dao, 2023) enabled. As shown in the table, HOMER significantly reduces memory requirements, reducing the peak memory usage by over 70% when running inference on 64k inputs.", "cite_spans": [{"start": 326, "end": 337, "text": "(Dao, 2023)", "ref_id": "BIBREF8"}], "ref_spans": [{"start": 70, "end": 71, "text": "5", "ref_id": "TABREF6"}], "eq_spans": [], "section": "COMPUTATIONAL EFFICIENCY", "sec_num": "4.5"}, {"text": "The first source of our efficiency gains is the chunking mechanism. We circumvent the quadratic computation associated with self-attention by processing each chunk separately at the earlier layers. Token reduction is our second source of computation reduction. As our algorithm progressively reduces the number of tokens, fewer tokens have to be processed in the upper layers, reducing the computational overhead. The third source of computation saving is that HOMER outputs concise embeddings, optimizing the subsequent self-attention computation during the generation phase. Compared to naïve forwarding of the complete input, our compact embeddings significantly minimize the size of kv-cache, thus optimizing the computation process. Finally, the memory requirement is further reduced from linear to logarithmic with respect to the input length, thanks to the optimized computation ordering described in Section 3.3. Additional discussion on the inference speed is provided in Appendix E.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "COMPUTATIONAL EFFICIENCY", "sec_num": "4.5"}, {"text": "In this paper, we introduced Hierarchical cOntext MERging (HOMER), a novel method that efficiently addresses the context limit issue inherent in large language models (LLMs). By employing a strategic divide-and-conquer technique, HOMER prunes redundant tokens, creating compact embeddings while maintaining the richness of information. This approach, validated by our experiments, has proven to be memory-efficient and effective, enabling the handling of extended contexts up to 64k tokens with significantly reduced memory requirements.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "5"}, {"text": "Limitations and future work. Although our work focuses on training-free extension of context limit, there is no fundamental limit on our method making finetuning impossible. We believe that further improving our method with small-data finetuning can additionally boost performance, and the resulting model would enjoy both the extended context limit and reduced memory requirements.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "5"}, {"text": "While large language models (LLMs) have become a new paradigm of AI academia and business industry by showing remarkable contributions, their critical limitations still remain such as hallucination, biased content generation, and unintended toxicity. The proposed research on long context windows does not address this limitation directly. The influences of longer context limits on the LLM limitations should be more explored, which is one of significant future research directions.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ETHICS STATEMENT", "sec_num": null}, {"text": "We outline the implementation details (including detailed algorithm, prompt design, and hyperparameters) and experiment setups (including tasks, datasets, and metrics) in Section 4 and Appendix B. We also release the source codes. Note that propagative refinement must be applied after processing each node to enjoy the optimized memory usage.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "A.1 PRELIMINARIES Problem setup. We conceptualize the hierarchical context merging process as a binary tree. For example, Figure 3 illustrates a merging process with 4 input chunks.", "cite_spans": [], "ref_spans": [{"start": 129, "end": 130, "text": "3", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "Constants. L i refers to the number of layers used for processing a chunk at binary tree height i. L := L i is the total number of network layers. C is the maximum chunk size. M is the memory required for storing a key-value pair for a single token in a single layer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "Remarks. As the chunk size is bounded, the memory required for forwarding a single chunk through a single layer can be treated as constant. Therefore, it suffices to consider the memory required for storing the key-value pairs at each layer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "Let FinalMem(h) be the memory occupied after processing a binary tree of height h. As propagative refinement reduces the intermediate hidden states to be C/2 tokens long, FinalMem(h) is bounded as follows.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "FinalMem(h) = C 2 × h i=0 L i × M ≤ 1 2 LCM A.2 PROOF", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "Proposition. Let PeakMem(h) be the peak memory usage for processing a binary tree of height h. Then,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "PeakMem(h) ≤ 1 2 h + 1 LCM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "We prove the proposition using induction. First, consider the leaf node where h = 0. As C tokens are passed through L 0 layers, the peak memory usage is given as follows, proving the base case.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "PeakMem(0) = L 0 CM ≤ LCM", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "Now consider a non-leaf node with h > 0. Processing of a non-leaf node consists of three steps: (1) sequentially processing two child nodes, (2) obtaining a merged chunk and forwarding it through L h layers, (3) applying propagative refinement on the h i=1 L i lower-layer hidden states. As step", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "(3) is a memory reduction step, PeakMem(h) is the maximum of peak memories of steps (1) and (2).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "In step (1) the two child nodes are sequentially processed, resulting in the peak memory of FinalMem(h -1) + PeakMem(h -1).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "In step (2) hidden states of length C must be held for h i=0 L i layers, so the peak memory is", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "C × h i=0 L i × M.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "By applying the induction hypothesis, we get", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "PeakMem(h) = max FinalMem(h -1) + PeakMem(h -1), C × h i=0 L i × M ≤ max 1 2 LCM + 1 2 (h -1) + 1 LCM, LCM = max 1 2 h + 1 LCM, LCM = 1 2 h + 1 LCM.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "As the peak memory grows linearly with the tree height, it grows logarithmic with the input sequence length.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "Context merging schedule. Following the formulation in Figure 3 , we detail how many layers are assigned to each level of the binary tree. The basic principle is to assign an equal number of layers to each node. In practice, we noticed that additionally assigning more layers to the leaf nodes helps improve the overall performance. We assign 12 additional layers for 7b models and 20 layers for 13b models.", "cite_spans": [], "ref_spans": [{"start": 62, "end": 63, "text": "3", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "B IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "Calibration. For all models (HOMER and HOMER+baselines), calibration is performed using 100 text corpora segments from the validation set and the test set of WikiText-103 (<PERSON><PERSON> et al., 2016) .", "cite_spans": [{"start": 171, "end": 192, "text": "(<PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "B IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "In all experiments involving HOMER, the maximum chunk length was set to be half of the context limit.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Maximum chunk length.", "sec_num": null}, {"text": "In this section, we provide a comprehensive explanation of propagative refinement suggested in Section 3.2. Figure 4 illustrates the process, where 3 out of 6 tokens are pruned at layer N. Initially, as shown in part (a), the three least significant tokens (2, 3, and 5) are marked for pruning in layer N. Subsequently, in part (b), the corresponding tokens in the lower-layer embeddings are also marked for pruning. Finally, part (c) demonstrates the outcome after pruning, where all marked tokens are eliminated across every layer, resulting in a uniform, compressed embedding structure composed of just three tokens.", "cite_spans": [], "ref_spans": [{"start": 115, "end": 116, "text": "4", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "D ILLUSTRATION OF PROPAGATIVE REFINEMENT", "sec_num": null}, {"text": "In this section, we discuss the inference speed of HOMER. Besides reducing memory requirements, HOMER also provides a significant speedup due to the extensive reduction in computation. Table 8 illustrates the average inference time for HOMER and other baselines. Specifically, we compare the time required to generate 20, 50, and 100 tokens, conditioned on 8k, 16k, and 32k contexts. The main source of performance gain in HOMER, as described in Section 4.5, is the computation reduction. The following points highlight these improvements:", "cite_spans": [], "ref_spans": [{"start": 191, "end": 192, "text": "8", "ref_id": "TABREF7"}], "eq_spans": [], "section": "E INFERENCE SPEED ANALYSIS", "sec_num": null}, {"text": "• The divide-and-conquer approach circumvents the quadratic computation associated with selfattention. • Token pruning significantly reduces the number of tokens to process, especially in the upper layers. • HOMER compresses long contexts into short embeddings, substantially reducing the size of the kv-cache. This step lowers the computational demand during the decoding stage.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E INFERENCE SPEED ANALYSIS", "sec_num": null}, {"text": "As evident from the results, HOMER provides a significant speedup (up to 162.6%) compared to the baseline methods. It's important to note that our method is even more beneficial when generating longer outputs conditioned on longer inputs, underscoring its effectiveness in handling long contexts.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E INFERENCE SPEED ANALYSIS", "sec_num": null}, {"text": "We also emphasize that the additional computation introduced by HOMER is minimal. The hierarchical context merging process involves relatively cheap operations, including matrix subtraction (calibration), gathering by index (calibration, token pruning, and propagative refinement), top-k selection (token pruning), and tensor concatenation (merging). Conversely, it reduces more costly operations such as matrix multiplication for computing self-attention.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E INFERENCE SPEED ANALYSIS", "sec_num": null}, {"text": "In this section, we provide a fine-grained perplexity plot for the fluency experiment in Section 4.3. Figure 6 : Perplexity plot on 100 long samples from passkey retrieval, measured with Llama-2-7b-chat. HOMER achieves lower perplexity when conditioned on longer inputs, demonstrating its ability to effectively handle the long inputs. Perplexity values on landmark lengths are provided in Table 9 . In this section, we provide additional perplexity experiments on a more challenging benchmark where accessing previous long contexts is essential. To achieve this, we reformulated the passkey retrieval task in Section 4.1 and measured the perplexity of ground-truth answer phrases (e.g., 'The passkey is 12321.'). The results are demonstrated in Figure 6 and Table 9 .", "cite_spans": [], "ref_spans": [{"start": 109, "end": 110, "text": "6", "ref_id": null}, {"start": 396, "end": 397, "text": "9", "ref_id": "TABREF8"}, {"start": 753, "end": 754, "text": "6", "ref_id": null}, {"start": 765, "end": 766, "text": "9", "ref_id": "TABREF8"}], "eq_spans": [], "section": "F PERPLEXITY PLOT FOR LANGUAGE MODELING EXPERIMENT", "sec_num": null}, {"text": "As the results show, HOMER exhibits lower perplexity when conditioned on longer contexts, achieving its best performance with 64k inputs. Furthermore, HOMER outperforms the long-context competitors with context lengths of 16k and beyond. These experiments emphasize the efficacy of HOMER in utilizing long contexts, particularly in scenarios where accessing such context is necessary.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F PERPLEXITY PLOT FOR LANGUAGE MODELING EXPERIMENT", "sec_num": null}], "back_matter": [{"text": "This work was supported by Institute of Information & communications Technology Planning & Evaluation (IITP) grant funded by the Korea government (MSIT) (No.2019-0-00075, Artificial Intelligence Graduate School Program (KAIST); No.2021-0-02068, Artificial Intelligence Innovation Hub; No.2022-0-00959, Few-shot Learning of Casual Inference in Vision and Language for Decision Making).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACKNOWLEDGEMENTS AND D<PERSON><PERSON>OSURE OF FUNDING", "sec_num": null}, {"text": "The detailed prompt format for each downstream task are provided in Table 6 and Table 7. Table 6 : Prompt for passkey retrieval task. Slight modifications are made from the original prompt to turn it into a chat prompt (<PERSON><PERSON><PERSON><PERSON> et al., 2023) .", "cite_spans": [{"start": 219, "end": 241, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF28"}], "ref_spans": [{"start": 74, "end": 75, "text": "6", "ref_id": null}, {"start": 80, "end": 88, "text": "Table 7.", "ref_id": null}, {"start": 95, "end": 96, "text": "6", "ref_id": null}], "eq_spans": [], "section": "C PROMPTS FOR DOWNSTREA<PERSON> TASKS", "sec_num": null}, {"text": "There is an important info hidden inside a lot of irrelevant text. Find it and memorize them. I will quiz you about the important information there.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prefix [INST] <<SYS>>", "sec_num": null}, {"text": "Context The grass is green. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "<</SYS>>", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "The long-document transformer", "authors": [{"first": "Iz", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Long<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2004.05150"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Longformer: The long-document transformer. arXiv preprint arXiv:2004.05150, 2020.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Unlimiformer: Long-range transformers with unlimited length input", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Alon", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Neubig", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.01625"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Unlimiformer: Long-range transformers with unlimited length input. arXiv preprint arXiv:2305.01625, 2023.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Ntk-aware scaled rope allows llama models to have extended (8k+) context size without any fine-tuning and minimal perplexity degradation", "authors": [], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "Ntk-aware scaled rope allows llama models to have extended (8k+) context size with- out any fine-tuning and minimal perplexity degradation. https://www.reddit.com/ r/LocalLLaMA/comments/14lz7j5/ntkaware_%20scaled_rope_allows_ llama_models_to_have/, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Token merging: Your vit but faster", "authors": [{"first": "<PERSON>", "middle": [], "last": "Bolya", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Dai", "suffix": ""}, {"first": "Peizhao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.09461"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Token merging: Your vit but faster. arXiv preprint arXiv:2210.09461, 2022. <PERSON>. Transformer inference arithmetic. https://kipp.ly/blog/ transformer-inference-arithmetic/, 2022.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Extending context window of large language models via positional interpolation", "authors": [{"first": "S<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuandong", "middle": [], "last": "Tian", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.15595"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Extending context window of large language models via positional interpolation. arXiv preprint arXiv:2306.15595, 2023.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Generating long sequences with sparse transformers", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Child", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1904.10509"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Generating long sequences with sparse transformers. arXiv preprint arXiv:1904.10509, 2019.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Rethinking attention with performers", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Likhosherstov", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xingyou", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Sarl<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Afroz", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Lukasz", "middle": [], "last": "Kaiser", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2009.14794"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Rethinking attention with performers. arXiv preprint arXiv:2009.14794, 2021.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Transformer-xl: Attentive language models beyond a fixed-length context", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Dai", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>uo<PERSON> <PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1901.02860"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>- nov. Transformer-xl: Attentive language models beyond a fixed-length context. arXiv preprint arXiv:1901.02860, 2019.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Flashattention-2: Faster attention with better parallelism and work partitioning", "authors": [{"first": "Tri", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.08691"]}, "num": null, "urls": [], "raw_text": "Tri Dao. Flashattention-2: Faster attention with better parallelism and work partitioning. arXiv preprint arXiv:2307.08691, 2023.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "Xiaohua", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. An image is worth 16x16 words: Transformers for image recogni- tion at scale. In International Conference on Learning Representations, 2021. URL https: //openreview.net/forum?id=YicbFdNTTy.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Lm-infinite: Simple on-the-fly length generalization for large language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Heng", "suffix": ""}, {"first": "Sinong", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.16137"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Lm-infinite: Simple on-the-fly length generalization for large language models. arXiv preprint arXiv:2308.16137, 2023.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Which tokens to use? investigating token reduction in vision transformers", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sergio", "middle": [], "last": "Escalera", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2308.04657"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Which tokens to use? investigating token reduction in vision transformers. arXiv preprint arXiv:2308.04657, 2023.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Efficient long-text understanding with short-text models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "Transactions of the Association for Computational Linguistics", "volume": "11", "issue": "", "pages": "284--299", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Efficient long-text understanding with short-text models. Transactions of the Association for Computational Linguistics, 11:284-299, 2023.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Leveraging passage retrieval with generative models for open domain question answering", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "I<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Grave", "suffix": ""}], "year": 2020, "venue": "Things i'm learning while training superhot", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2007.01282"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>. Leveraging passage retrieval with generative models for open domain question answering. arXiv preprint arXiv:2007.01282, 2020. kaiokendev. Things i'm learning while training superhot. https://kaiokendev.github. io/til#extending-context-to-8k./, 2023.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "<PERSON><PERSON>, and <PERSON><PERSON><PERSON> ¸ois <PERSON>t. Transformers are rnns: Fast autoregressive transformers with linear attention", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Vyas", "suffix": ""}], "year": 2020, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> <PERSON>. Transformers are rnns: Fast autoregressive transformers with linear attention. In International Conference on Ma- chine Learning, 2020.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Reformer: The efficient transformer", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Lev<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2001.04451"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Reformer: The efficient transformer. arXiv preprint arXiv:2001.04451, 2020.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Not all patches are what you need: Expediting vision transformers via token reorganizations", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ge", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Pengtao", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2202.07800"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Not all patches are what you need: Expediting vision transformers via token reorganizations. arXiv preprint arXiv:2202.07800, 2022.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Pointer sentinel mixture models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Caiming", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1609.07843"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Pointer sentinel mixture models. arXiv preprint arXiv:1609.07843, 2016.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Landmark attention: Random-access infinite context length for transformers", "authors": [{"first": "Amirkeivan", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.16300"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON>. Landmark attention: Random-access infinite context length for transformers. arXiv preprint arXiv:2305.16300, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "OpenAI. Gpt-4 technical report", "authors": [], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.08774"]}, "num": null, "urls": [], "raw_text": "OpenAI. Gpt-4 technical report. arXiv preprint arXiv:2303.08774, 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "He <PERSON>, et al. Quality: Question answering with long input texts, yes! arXiv preprint", "authors": [{"first": "<PERSON>", "middle": [], "last": "Yuanzhe", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Nangia", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Angelica", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Vishakh", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2112.08608"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Quality: Question answering with long input texts, yes! arXiv preprint arXiv:2112.08608, 2021.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Yarn: Efficient context window extension of large language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Quesnelle", "suffix": ""}, {"first": "Honglu", "middle": [], "last": "Fan", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Shippole", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.00071"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Yarn: Efficient context window extension of large language models. arXiv preprint arXiv:2309.00071, 2023.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Blockwise self-attention for long document understanding", "authors": [{"first": "<PERSON>ez<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sinong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1911.02972"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Blockwise self-attention for long document understanding. arXiv preprint arXiv:1911.02972, 2019.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Compressive transformers for long-range sequence modelling", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["P"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Lillicrap", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1911.05507"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Compressive transformers for long-range sequence modelling. arXiv preprint arXiv:1911.05507, 2019.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Zeroscrolls: A zero-shot benchmark for long text understanding", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Avia", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.14196"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Zeroscrolls: A zero-shot benchmark for long text understanding. arXiv preprint arXiv:2305.14196, 2023.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Rectified rotary position embeddings", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Su", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>. Rectified rotary position embeddings. https://github.com/bojone/rerope, 2023.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Enhanced transformer with rotary position embedding", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Su", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shengfeng", "middle": [], "last": "Pan", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yunfeng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2104.09864"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Roformer: En- hanced transformer with rotary position embedding. arXiv preprint arXiv:2104.09864, 2021.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Scaling laws vs model architectures: How does inductive bias influence scaling? arXiv preprint", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["Won"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Jinfeng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Vinh Q Tran", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2207.10551"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Scaling laws vs model architectures: How does inductive bias influence scaling? arXiv preprint arXiv:2207.10551, 2022.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Llama 2: Open foundation and fine-tuned chat models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Louis", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stone", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Batra", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Shruti", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09288"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Llama 2: Open founda- tion and fine-tuned chat models. arXiv preprint arXiv:2307.09288, 2023.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Linformer: Self-attention with linear complexity", "authors": [{"first": "Sinong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Z"], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Han", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "Ma", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2006.04768"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. <PERSON><PERSON>: Self-attention with linear complexity. arXiv preprint arXiv:2006.04768, 2020.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Augmenting language models with long-term memory", "authors": [{"first": "Weizhi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiaodong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xifeng", "middle": [], "last": "Yan", "suffix": ""}, {"first": "Ji<PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.07174"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Aug- menting language models with long-term memory. arXiv preprint arXiv:2306.07174, 2023.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Big bird: Transformers for longer sequences", "authors": [{"first": "Man<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ainslie", "suffix": ""}, {"first": "Santiago", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ontanon", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. Big bird: Transformers for longer sequences. Neural Information Processing Systems, 2020.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Calibrate before use: Improving few-shot performance of language models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "12697--12706", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Calibrate before use: Improving few-shot performance of language models. In International Conference on Machine Learning, pp. 12697-12706. PMLR, 2021.", "links": null}}, "ref_entries": {"FIGREF2": {"uris": null, "text": "Figure 1: (a) Passkey retrieval accuracy on various context lengths, measured with Llama-2-7b-chat.HOMER maintains reasonable performance for context lengths up to 32K tokens. Detailed comparisons with more baselines are provided in Table1. (b) The memory requirement for processing long inputs. (c) Average inference time required for generating 100 tokens conditioned on various context lengths. All efficiency measurements are done with a single A100 GPU. The baselines include plain Llama, PI, NTK, and YaRN. Peak memory usage of the baselines at 64k is an estimated value, as they do not fit in a single A100 GPU. Detailed results are provided in Table5and Appendix E.", "type_str": "figure", "num": null, "fig_num": "1"}, "FIGREF3": {"uris": null, "text": "Figure2: An overview of the proposed hierarchical context merging. We first divide a long context into multiple chunks and independently forward them through the early transformer layers. In the intermediate layers, we merge multiple chunks by concatenation, forming a new, merged chunk. To keep the chunk length bounded, we apply token reduction on the original chunks to make them shorter, prior to merging. This process is repeated until all chunks are merged into a single chunk. Finally, we further refine the lower-layer embeddings to get a compact fixed-length, layerwise embedding. The embedding can then be used like a standard kv-cache(Chen, 2022).", "type_str": "figure", "num": null, "fig_num": "2"}, "FIGREF4": {"uris": null, "text": "Figure3: Hierarchical context merging process conceptualized as a binary tree. The top-left numbers of each node denote the memory-efficient computation order. Note that propagative refinement must be applied after processing each node to enjoy the optimized memory usage.", "type_str": "figure", "num": null, "fig_num": "3"}, "FIGREF5": {"uris": null, "text": "Figure 4: Illustration of the propagative refinement process.", "type_str": "figure", "num": null, "fig_num": "4"}, "FIGREF6": {"uris": null, "text": "Figure 5: Perplexity plot on 25 long documents from PG-19 dataset (<PERSON> et al., 2019), measured with Llama-2-7b. HOMER consistently achieves low perplexity across long documents up to 64K tokens, demonstrating its ability to remain fluent while conditioned on very long inputs. Detailed comparison with more baselines are provided in Table3.", "type_str": "figure", "num": null, "fig_num": "5"}, "TABREF2": {"text": "Retrieval accuracy on passkey retrieval. Average accuracy on 500 samples are reported. The best values are in bold, and the second-best values are underlined. Empty values indicate NaN.", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td/><td>Context</td><td/><td colspan=\"2\">Llama-2-7b-chat</td><td/><td/><td colspan=\"2\">Llama-2-13b-chat</td><td/></tr><tr><td>Method</td><td>limit</td><td>4K</td><td>8K</td><td>16K</td><td>32K</td><td>4K</td><td>8K</td><td>16K</td><td>32K</td></tr><tr><td>Plain</td><td>4k</td><td colspan=\"2\">1.000 0.000</td><td>-</td><td>-</td><td colspan=\"4\">1.000 0.000 0.000 0.000</td></tr><tr><td>Plain + HOMER</td><td>None</td><td colspan=\"8\">0.990 0.924 0.890 0.776 1.000 0.944 0.882 0.804</td></tr><tr><td>PI</td><td>8k</td><td colspan=\"3\">0.432 0.356 0.000</td><td>-</td><td colspan=\"4\">0.600 0.544 0.000 0.000</td></tr><tr><td/><td>16k</td><td colspan=\"8\">0.006 0.006 0.006 0.000 0.022 0.028 0.018 0.000</td></tr><tr><td>NTK</td><td>8k</td><td colspan=\"8\">0.812 0.000 0.000 0.000 0.866 0.000 0.000 0.000</td></tr><tr><td/><td>16k</td><td colspan=\"8\">0.516 0.652 0.000 0.000 0.626 0.692 0.000 0.000</td></tr><tr><td/><td>32k</td><td colspan=\"8\">0.106 0.194 0.162 0.000 0.286 0.570 0.442 0.000</td></tr><tr><td>YaRN</td><td>8k</td><td colspan=\"3\">0.996 0.002 0.000</td><td>-</td><td colspan=\"4\">1.000 0.464 0.000 0.000</td></tr><tr><td/><td>16k</td><td colspan=\"8\">0.844 0.756 0.000 0.000 0.980 0.952 0.214 0.000</td></tr><tr><td/><td>32k</td><td colspan=\"8\">0.702 0.654 0.696 0.002 0.926 0.888 0.836 0.026</td></tr><tr><td/><td>64k</td><td colspan=\"8\">0.678 0.358 0.148 0.026 0.902 0.826 0.364 0.224</td></tr><tr><td>YaRN + HOMER</td><td>None</td><td colspan=\"8\">0.996 0.984 0.876 0.802 1.000 1.000 0.974 0.860</td></tr><tr><td colspan=\"2\">4.2 QUESTION ANSWERING</td><td/><td/><td/><td/><td/><td/><td/><td/></tr></table>"}, "TABREF3": {"text": "Accuracy in question answering, as evaluated on the QuALITY validation set. The best results are highlighted in bold.", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">Context limit Accuracy</td></tr><tr><td>Plain</td><td>4k</td><td>0.327</td></tr><tr><td>Plain + HOMER</td><td>None</td><td>0.358</td></tr><tr><td>PI</td><td>8k</td><td>0.366</td></tr><tr><td>NTK</td><td>8k</td><td>0.379</td></tr><tr><td>YaRN</td><td>8k</td><td>0.310</td></tr><tr><td>NTK + HOMER</td><td>None</td><td>0.388</td></tr></table>"}, "TABREF4": {"text": "Perplexity of 25 long documents from PG-19 truncated to the evaluation length. The best values are in bold, and the second-best values are underlined. Empty values indicate NaN. ) or show heightened perplexity even within shorter contexts, HOMER steadily maintains minimal perplexity across extended contexts. This suggests that HOMER is the only method that maintains reasonable fluency even when conditioned on very long contexts. Moreover, HOMER can be seamlessly integrated with conventional positional encoding scaling techniques to further improve performance. As evident from Table3, applying HOMER on top of YaRN yields lower perplexity.", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td/><td>Context</td><td/><td/><td>Llama-2-7b</td><td/><td/><td/><td colspan=\"2\">Llama-2-13b</td><td/><td/></tr><tr><td>Method</td><td>limit</td><td>4K</td><td>8K</td><td>16K</td><td>32K</td><td>64K</td><td>4K</td><td>8K</td><td>16K</td><td>32K</td><td>64K</td></tr><tr><td>Plain</td><td>4k</td><td>6.72</td><td>-</td><td>-</td><td>-</td><td>-</td><td>6.14</td><td colspan=\"4\">&gt; 10 2 &gt; 10 3 &gt; 10 3 &gt; 10 3</td></tr><tr><td>Plain + HOMER</td><td>None</td><td>6.72</td><td>7.29</td><td>7.78</td><td>8.43</td><td>9.64</td><td>6.13</td><td>6.60</td><td>6.87</td><td>7.13</td><td>7.59</td></tr><tr><td>PI</td><td>8k</td><td>7.91</td><td>8.19</td><td>-</td><td>-</td><td>-</td><td>6.96</td><td>7.19</td><td colspan=\"3\">&gt; 10 2 &gt; 10 3 &gt; 10 3</td></tr><tr><td/><td>16k</td><td>&gt; 10</td><td>&gt; 10</td><td>&gt; 10</td><td>-</td><td>-</td><td>&gt; 10</td><td>&gt; 10</td><td>&gt; 10</td><td colspan=\"2\">&gt; 10 2 &gt; 10 3</td></tr><tr><td>NTK</td><td>8k</td><td>6.97</td><td>&gt; 10</td><td>&gt; 10 2</td><td>-</td><td>-</td><td>6.26</td><td>9.62</td><td colspan=\"3\">&gt; 10 2 &gt; 10 3 &gt; 10 3</td></tr><tr><td/><td>16k</td><td>7.59</td><td>7.95</td><td>&gt; 10</td><td colspan=\"2\">&gt; 10 2 &gt; 10 3</td><td>6.76</td><td>7.05</td><td>&gt; 10</td><td colspan=\"2\">&gt; 10 3 &gt; 10 3</td></tr><tr><td/><td>32k</td><td>8.42</td><td>8.97</td><td>9.76</td><td>&gt; 10</td><td>&gt; 10 2</td><td>7.42</td><td>7.90</td><td>8.45</td><td>&gt; 10</td><td>&gt; 10 3</td></tr><tr><td>YaRN</td><td>8k</td><td>6.79</td><td>7.40</td><td>-</td><td>-</td><td>-</td><td>6.19</td><td>6.59</td><td colspan=\"3\">&gt; 10 2 &gt; 10 3 &gt; 10 3</td></tr><tr><td/><td>16k</td><td>7.00</td><td>7.32</td><td>8.98</td><td>-</td><td>-</td><td>6.36</td><td>6.65</td><td>7.83</td><td colspan=\"2\">&gt; 10 2 &gt; 10 3</td></tr><tr><td/><td>32k</td><td>7.50</td><td>8.05</td><td>8.78</td><td>&gt; 10</td><td>-</td><td>6.65</td><td>7.05</td><td>7.40</td><td>8.85</td><td>&gt; 10 2</td></tr><tr><td/><td>64k</td><td>8.49</td><td>&gt; 10</td><td>&gt; 10</td><td>&gt; 10</td><td>&gt; 10</td><td>7.17</td><td>8.32</td><td>&gt; 10</td><td>&gt; 10</td><td>&gt; 10</td></tr><tr><td>YaRN + HOMER</td><td>None</td><td>6.79</td><td>7.09</td><td>7.52</td><td>7.95</td><td>8.83</td><td>6.19</td><td>6.51</td><td>6.78</td><td>7.02</td><td>7.44</td></tr><tr><td colspan=\"3\">lower scaling factors4.4 ABLATION STUDIES</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr></table>"}, "TABREF5": {"text": "Ablation on different components. We report the passkey retrieval accuracy for 500 samples, evaluated on 16k contexts. The best values are highlighted in bold.", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td colspan=\"2\">(a) Token pruning criteria.</td><td colspan=\"2\">(b) Lower-layer embedding refinement.</td></tr><tr><td>Method</td><td>Accuracy</td><td>Method</td><td>Accuracy</td></tr><tr><td>Random</td><td>0.006</td><td>No refinement</td><td>0.002</td></tr><tr><td>Attention-based top-K</td><td>0.056</td><td>Random</td><td>0.116</td></tr><tr><td>+ calibration</td><td>0.890</td><td>Layer-wise top-K</td><td>0.040</td></tr><tr><td/><td/><td>Propagative refinement</td><td>0.890</td></tr></table>"}, "TABREF6": {"text": "Peak memory usage for long inputs. All measurements are taken on a single A100 GPU, with Flash Attention 2(Dao, 2023) applied. The baselines include plain Llama, PI, NTK, and YaRN. We report a single value for all baselines as they share the same memory requirement.", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td>Peak GPU memory (GB)</td></tr></table>"}, "TABREF7": {"text": "Inference time for long inputs. All measurements are taken on a single A100 GPU, with Flash Attention 2(<PERSON><PERSON>, 2023) applied. We also report the percentage of the speedup. We report a single value for all baselines * following the setup in Section 4.5. The baselines include plain Llama, PI, NTK, and YaRN. The inference time is averaged over 25 runs.", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td/><td/><td>Average run time (seconds)</td><td/></tr><tr><td>Setup</td><td>8k</td><td>16k</td><td>32k</td></tr><tr><td/><td/><td>20 tokens</td><td/></tr><tr><td>Baselines  *</td><td>1.879</td><td>3.224</td><td>6.546</td></tr><tr><td>HOMER</td><td colspan=\"3\">1.673 (12.3% speedup) 2.270 (42.0% speedup) 3.513 (86.3% speedup)</td></tr><tr><td/><td/><td>50 tokens</td><td/></tr><tr><td>Baselines  *</td><td>3.842</td><td>6.028</td><td>11.143</td></tr><tr><td>HOMER</td><td colspan=\"3\">3.026 (27.0% speedup) 3.639 (65.6% speedup) 4.873 (128.7% speedup)</td></tr><tr><td/><td/><td>100 tokens</td><td/></tr><tr><td>Baselines  *</td><td>7.149</td><td>10.733</td><td>18.828</td></tr><tr><td>HOMER</td><td colspan=\"3\">5.355 (33.5% speedup) 5.930 (81.0% speedup) 7.169 (162.6% speedup)</td></tr></table>"}, "TABREF8": {"text": "Perplexity values for passkey retrieval. Empty values indicate NaN.", "type_str": "table", "num": null, "html": null, "content": "<table><tr><td/><td>Context Limit</td><td>2k</td><td>4k</td><td>8k</td><td>16k</td><td>32k</td><td>64k</td></tr><tr><td>Plain</td><td>4k</td><td colspan=\"3\">23.12 13.013 &gt; 10 3</td><td>-</td><td>-</td><td>-</td></tr><tr><td>Plain + HOMER</td><td>None</td><td colspan=\"5\">23.22 29.882 23.932 20.897 7.119</td><td>4.085</td></tr><tr><td>YaRN</td><td>8k</td><td colspan=\"4\">23.414 17.338 23.394 &gt; 10 4</td><td>-</td><td>-</td></tr><tr><td/><td>16k</td><td colspan=\"5\">28.183 19.07 15.366 &gt; 10 2 &gt; 10 3</td><td>-</td></tr><tr><td/><td>32k</td><td colspan=\"6\">19.248 20.491 28.034 17.187 31.017 &gt; 10 3</td></tr><tr><td/><td>64k</td><td colspan=\"6\">22.031 16.963 15.481 36.760 38.786 &gt; 10 2</td></tr><tr><td>YaRN + HOMER</td><td>None</td><td colspan=\"5\">23.414 17.232 22.263 11.317 7.618</td><td>2.412</td></tr></table>"}}}}