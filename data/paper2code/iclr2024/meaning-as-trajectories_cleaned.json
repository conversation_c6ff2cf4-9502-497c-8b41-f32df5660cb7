{"paper_id": "meaning-as-trajectories", "title": "MEANING REPRESENTATIONS FROM TRAJECTORIES IN AUTOREGRESSIVE MODELS", "abstract": "We propose to extract meaning representations from autoregressive language models by considering the distribution of all possible trajectories extending an input text. This strategy is prompt-free, does not require fine-tuning, and is applicable to any pre-trained autoregressive model. Moreover, unlike vectorbased representations, distribution-based representations can also model asymmetric relations (e.g., direction of logical entailment, hypernym/hyponym relations) by using algebraic operations between likelihood functions. These ideas are grounded in distributional perspectives on semantics and are connected to standard constructions in automata theory, but to our knowledge they have not been applied to modern language models. We empirically show that the representations obtained from large models align well with human annotations, outperform other zero-shot and prompt-free methods on semantic similarity tasks, and can be used to solve more complex entailment and containment tasks that standard embeddings cannot handle. Finally, we extend our method to represent data from different modalities (e.g., image and text) using multimodal autoregressive models. Our code is available at: https://github.com/tianyu139/ meaning-as-trajectories † Equal contribution * Work done during an internship at AWS AI Labs.", "pdf_parse": {"paper_id": "meaning-as-trajectories", "abstract": [{"text": "We propose to extract meaning representations from autoregressive language models by considering the distribution of all possible trajectories extending an input text. This strategy is prompt-free, does not require fine-tuning, and is applicable to any pre-trained autoregressive model. Moreover, unlike vectorbased representations, distribution-based representations can also model asymmetric relations (e.g., direction of logical entailment, hypernym/hyponym relations) by using algebraic operations between likelihood functions. These ideas are grounded in distributional perspectives on semantics and are connected to standard constructions in automata theory, but to our knowledge they have not been applied to modern language models. We empirically show that the representations obtained from large models align well with human annotations, outperform other zero-shot and prompt-free methods on semantic similarity tasks, and can be used to solve more complex entailment and containment tasks that standard embeddings cannot handle. Finally, we extend our method to represent data from different modalities (e.g., image and text) using multimodal autoregressive models. Our code is available at: https://github.com/tianyu139/ meaning-as-trajectories † Equal contribution * Work done during an internship at AWS AI Labs.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Generative Large Scale Models today are capable of generating remarkably coherent text by iteratively predicting individual tokens. Contrary to encoder-only or encoder-decoder models, however, autoregressive models do not construct explicit representations of sentences: the model's representation of a given input is distributed across layers and attention heads, making it difficult to analyze how the model \"understands\" and contextualizes language. This lack of transparency and interpretability is a challenge for the responsible deployment of these models.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In this paper, we propose a simple way to explore how autoregressive models manipulate text. Whereas standard methods represent sentences by embedding them in a vector space, we propose to represent sentences, or parts of sentences, as the distribution of their possible continuations, or trajectories. This can be seen as a practical embodiment of classical distributional approaches to semantics (<PERSON><PERSON><PERSON>, 2020; <PERSON>, 2008) , according to which the meaning of linguistic items is tied to the distribution of their usage. It is also related to standard constructions in formal language and automata theory which associate the \"behavior\" of a prefix with the set of its possible future continuations (<PERSON> et al., 2007) .", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Prior work has mostly focused on representing sentences via pooling output tokens. For example, BERT-based models (<PERSON> et al., 2018) include a [CLS] token that is meant to capture semantic information. Sentence encoder models such as ST5 (<PERSON> et al., 2021) represent sentences by pooling encoder or decoder outputs. These strategies are most effective after fine-tuning using a contrastive learning objective (<PERSON> et al., 2021) , but this requires additional data and modifies the weights of the original model. In particular, these methods do not faithfully reflect the original model's internal representation of the input sentence, and may be skewed by biases present in the data. Moreover, at the time of writing, the most powerful large language models are based on autoregressive architectures (<PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2020; OpenAI, 2023) . For such architectures, similar strategies based on averages of output tokens significantly underperform (Table 1 ). Instead, prompt engineering is usually regarded as the de facto standard to solve semantic tasks without further finetuning. For example, <PERSON> et al. (2023) craft careful prompts to elicit better token representations from autoregressive models. However, such approaches are problematic, since: 1) they can be highly susceptible to the (language dependent) choice of prompt; 2) the answer may not faithfully capture how the model actually interprets the sentence -a model may reply that two sentences are similar, but not necessarily represent them internally in the same way; 3) they do not provide any structured (topological/metric/compositional) semantic space in which sentences are embedded.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Representing the meaning of a sentence as a distribution of trajectories provides a straightforward way of bypassing these limitations. First, the method is general and can be applied to any autoregressive model without assumptions on architecture -or even on the language that it was trained on -and does not require fine-tuning or carefully crafted prompts. Since modern pre-trained large models are very capable of processing text, they should provide strong meaning representations outof-the-box. Second, using representations based on trajectories allows not only measuring semantic distances, but also defining compositional operations on meanings. In particular, we can define Boolean-like operations between meanings, and use them for example to infer the direction of logical entailment between sentences as perceived by the model, or determine hypernym/hyponym relation between words (Figure 2b ). Third, our method can be applied without any modification to multimodal autoregressive models that encode images as sequences of tokens, and used to compare the meaning representation of data from different modalities. The main technical challenge of our definition is that the space of all possible continuations of a sentence is too large to be explored directly. We show however that, with an appropriate sampling strategy, 10-20 sampled trajectories for each prompt are sufficient to approximate pairwise distances in semantic space (see Figure 1 ). The focus of our work is to propose a general way to extract \"canonical\" and interpretable meaning representations from pre-trained autoregressive models. Nonetheless, we empirically show that our method achieves competitive results on prompt-free, zero-shot semantic textual similarity (STS) tasks and that the representation we obtain applying our method to the LLaVA (<PERSON> et al., 2023) vision-language model outperforms CLIP embeddings (Radford et al., 2021) on semantic imageimage and image-text similarity tasks on the Crisscrossed Captions (Parekh et al., 2020) dataset. We show that the representation we obtain, although based solely on distributions of token sequences, largely agrees with human annotations both on semantic similarity, logical entailment and containment relations. These results support the idea that autoregressive models can represent sentences in a semantically meaningful way even if the representations are not explicit in their activations.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In summary, our main contributions are as follows:", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "1. We propose a canonical meaning representation for autoregressive models as a distribution over trajectories extending a sentence. Unlike vector space representations, this definition can directly capture asymmetric relations like logical entailments and hypernym/hyponym relations. 2. We show that the representations obtained from modern Large Language Models (LLMs) align well with conventional linguistic meanings: our method achieves competitive performance on Semantic Textual Similarity (STS) benchmarks, outperforming comparable zero-shot and promptfree baselines using the same architectures. 3. Our method can be extended without any modification to Vision Language Models (VLMs) for the purpose of quantifying semantic image-image and image-text similarity, outperforming even CLIP embeddings (<PERSON><PERSON> et al., 2021) when applied to LLaVA (<PERSON> et al., 2023) .", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Our work unifies two lines of work which are highly synergistic yet largely disjoint up until nowthe investigation of \"meaning\" within the internal representations of pre-trained LLMs and the computation of sequence embeddings for semantic comparison tasks. The close relationship between the statistical distribution of linguistic items and their meaning is the basis of the Distributional Hypothesis (<PERSON>, 1954) . This perspective also draws insight from <PERSON><PERSON><PERSON><PERSON>'s use theory of meaning (<PERSON><PERSON><PERSON><PERSON>, 1953) , commonly sloganized as \"meaning is use.\" In the field of natural language processing (NLP), semantic representations are frequently constructed based on statistical co-occurrences. However, conventional computational approaches such as word2vec (<PERSON><PERSON> et al., 2013) , typically involve computing statistics from a text corpus and then constructing vector representations for words or sentences. In contrast, in this work we propose to directly leverage the distribution itself as a fundamental representation of meaning. This is possible since LLMs offer a way to efficiently sample from such distributions, thereby providing an intrinsic notion of meaning from the perspective of the model.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Recently, several authors have argued that models trained on language alone, or more generally on \"form,\" are necessarily incapable of learning and representing conventional meaning. In particular, <PERSON><PERSON> & Ko<PERSON> (2020) propose a definition of meaning as a relation between language expressions and \"communicative intents\" which are, by definition, external to the language. Therefore, they conclude that LLMs trained on language expressions cannot in principle learn meanings. This leads to characterizing LLMs as \"stochastic parrots\" (<PERSON><PERSON> et al., 2021) capable of modeling the statistical form of the language (syntax) but intrinsically incapable of representing meaning (semantics). <PERSON> et al. (2021) investigate the role of assertions in both code and language, suggesting that ungrounded language models cannot fully emulate semantic representations.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "However, semantic structures can be constructed from syntactic ones: For instance, <PERSON> et al. (2023) show that models trained on synthetic languages with \"strong transparency\" (defined as those where expressions have context-independent denotations) can emulate semantic representations. They suggest, however, that the context-dependency of natural language limits language models from learning the semantic representations within. Using the language of category theory, <PERSON> et al. (2022) describe a functor between a syntactic category of text probabilities and a semantic category of meanings. While purely theoretical, their construction is closely related to our distribution-based meaning representation, thus providing further support for our proposed method. <PERSON><PERSON><PERSON> et al. (2023) define meanings in LLMs as equivalence classes of sentences induced by the trained model. This definition generalizes that of <PERSON><PERSON> & Koller (2020) , since \"communicative intent\" can be latent in the expressions used for training the LLM, which induces partitions the set of complete sentences. But while this characterization is suitable for analyzing the controllability of the model in the corresponding metric space, the resulting meaning representation does not exhibit any obvious compositional structure. Our definition is more general, and provides us with means to compose meaning directly in representation space, unlike all other works. While we do not wish to focus on the high-level aspects of the debate on \"meaning\" and \"understanding\" (or lack thereof) in LLMs, our results provide evidence that autoregressive models actually have rich latent semantic representations within their internal structure.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Encoder-based architectures have traditionally been the main tool for embedding sequences in a common vector space, in which they can be easily compared. Apart from BERT (<PERSON> et al., 2018) and ST5 (<PERSON> et al., 2021) , Sentence-BERT (Reimers & Gurevych, 2019) fine-tunes a modified BERT architecture to improve sentence embeddings. <PERSON><PERSON> & Frank (2022) improves the interpretability of Sentence-BERT embeddings while preserving their effectiveness. <PERSON> et al. (2020) and <PERSON> et al. (2021) propose contrastive fine-tuning objectives to obtain more effective embeddings. In contrast, our method does not require any fine-tuning, hence can faithfully reflect the original model's internal representation of an input string. Prompting is also commonly used to extract embeddings. <PERSON> et al. (2022) search over prompts to improve the embeddings obtained from BERT. <PERSON> et al. (2023) propose PromptEOL to summarize sentences as a single word for comparisons. Similar to fine-tuning, prompting alters/biases the meaning of the original string, and further requires sufficient command over the language being used to engineer an effective prompt. The latter also fails to scale with model sizes, generally performing worse on semantic similarity tasks as model size increases.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Most related to our work, <PERSON><PERSON><PERSON><PERSON><PERSON> (2022) applies decoder-only models for semantic search by computing pairwise conditional likelihood scores between a query and each document in the search database. Our experiments show that this conditional likelihood is insufficient to fully capture relative semantic meaning. Our method is prompt-free, and scales well with model size and human perception of model performances. Unlike prompt-based methods, the meaning space resulting from our method can also be composed to compute more complex relations between strings.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Preliminaries. We use A to denote a finite vocabulary of tokens and A * to indicate the set of all variable-length finite sequences of tokens in A. We view a language model as a map M (•|•) : ", "section": "METHOD", "sec_num": "3"}, {"text": "A * × A * → [0, 1] associating a \"prompt\" sequence s ∈ A *", "section": "METHOD", "sec_num": "3"}, {"text": "where P M is the probability over the next token defined by the model. When s = ϵ is the empty string, we write M (t) instead of M (t|ϵ).", "section": "METHOD", "sec_num": "3"}, {"text": "Meaning representation for prompts. We define the syntactic meaning representation of a prompt string s for the model M as the function M s := M (-|s) : A * → [0, 1]. This definition fully captures the way in which the model interprets the string s. For example, if M s = M t , then the prompts s and t are indistinguishable based on their continuations for the model. Note that the function M s (t) that represents the string s is an infinite dimensional object, since its domain are all finite sequences t ∈ A * . One of the challenges we will handle later is how to effectively use this representation through sampling.", "section": "METHOD", "sec_num": "3"}, {"text": "We remark that we can consider a particular case of eq. ( 1) where the domain of M s is restricted only to strings t ∈ A 1 comprising single tokens, instead of arbitrary length strings. This represents a sentence using the score distribution over the immediate next token, and is a common baseline used to embed sentences with autoregressive models. However, it is easy to see that this is a very incomplete semantic representation: for example, common tokens such as \"The\" are often the most likely next token regardless of the actual meaning of the prompt. This limitation will be evident in our experimental results.", "section": "METHOD", "sec_num": "3"}, {"text": "Sets of continuations. The function M s : A * → [0, 1] essentially represents the meaning of a string as the distribution of trajectories that extend that string. To guide intuitions, it is often useful to consider the more restricted setting where scores are binary M s : A * → {0, 1}. This can be interpreted as the characteristic function of the set of strings t that are feasible continuations of s according to the model. One advantage of this interpretation is that it makes explicit that meaning representations in our framework are not simple vectors, but rich objects that can be naturally manipulated though set-theoretic operations such as intersections -a fact that we will use later. This simpler setting also allows a direct connection with automata theory: For any language L ⊂ A * , the sets of feasible continuations s -1 L := {t : st ∈ L} of prefixes s can seen as the set of states of a canonical \"minimal automaton\" accepting the language (<PERSON> et al., 2007) . In a similar fashion, the sets M s of strings accepted by the model prompted with s can be interpreted as a canonical \"model of behaviors\" for the LLM. We refer to Appendix G for a discussion on these topics.", "section": "METHOD", "sec_num": "3"}, {"text": "Semantic similarity. Given two prompts u and v, we define their semantic distance as the distance d(M u , M v ) between their representation M u and M v , where d denotes a distance function that can be picked arbitrarily. For our experiments, we use:", "section": "METHOD", "sec_num": "3"}, {"text": "d(M u , M v ) = E t∼ 1 2 (Mu+Mv) |log M u (t) -log M v (t)| (2) = E t∼ 1 2 (Mu+Mv) 1 m m i=1 log p(a i |u, a <i ) p(a i |v, a <i ) .", "section": "METHOD", "sec_num": "3"}, {"text": "This amounts to comparing the expected difference in log-likelihood between the two models on continuations t ∼ 1 2 (M u + M v ) sampled with equal probability from either prompts. We ablate on other natural choices of distances in Appendix A.2. As noted above, explicitly integrating eq. ( 2) over all possible trajectories t is not feasible. Rather, we approximate the expectation through <PERSON> sampling. More precisely, we sample n trajectories T u = {t u i } n i=1 for the prompt u, where t u i ∼ M u , and n trajectories T v for the prompt v, each of length up to a fixed hyperparameter m.", "section": "METHOD", "sec_num": "3"}, {"text": "We then approximate eq. ( 2) as:", "section": "METHOD", "sec_num": "3"}, {"text": "d(M u , M v ) ≈ 1 2n t∈Tu⊔Tv |log M u (t) -log M v (t)|", "section": "METHOD", "sec_num": "3"}, {"text": "The steps we follow are detailed in Algorithm 1. More sophisticated approaches for approximating the distance could be explored in future work.", "section": "METHOD", "sec_num": "3"}, {"text": "A related baseline for comparing the similarity of two sentences u and v is the likelihood of their concatenation, M (uv) or M (v|u). However, perplexity-based measures are known to be unreliable when directly used to compare different sentences, even when the sentences have the same length (<PERSON> et al., 2022; Mei<PERSON> & Cotterell, 2021) . Moreover, the fact that v is a likely continuation of u does not necessarily imply that u and v have the same meaning. Our method circumvents these problems: rather than computing M (v|u), we compare the values of M u (t) = M (t|u) and M v (t) = M (t|v) on a common set of continuations t ∈ T u ⊔ T v . This strategy is arguably more natural and also, as our experiments will demonstrate, much more effective. While our notions of semantic similarity are defined from the perspective of language models, our experiments in Section 4 suggest that they increasingly align with that of human annotators as model size and training data increases, and vastly outperform that of next-token/likelihood baselines.", "section": "METHOD", "sec_num": "3"}, {"text": "Require: Model M , Strings u and v, num. trajectories n, max trajectory length m, distance d T u ← Sample n trajectories from u up to [EOS] or length m, whichever occurs sooner", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "T v ← Sample n trajectories from v up to [EOS] or length m, whichever occurs sooner Initialize M u = M v = ∅ for t = a 1 . . . a mt ∈ T u ⊔ T v do ▷ Compute trajectory likelihood M u [t] ← mt i=1 P M (a i |u a 1 . . . a i-1 ) 1/mt M v [t] ← mt i=1 P M (a i |v a 1 . . . a i-1 ) 1/mt end for return d(M u , M v )", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "▷ Return similarity score", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "Containments of semantic representations. Our representations M u belong to the space of functions [0, 1] A * , which we can view as the \"meaning space\" for the vocabulary A. Note that this space has a natural partial order: given M, N ∈ [0, 1] A * , we say that M < N if t ∈ A * we have M (t) < N (t), which intuitively means that any feasible sentence for M is also feasible for N . More generally, we can define operations of meet and join as M ∧ N := min(M, N ) and M ∨ N := max(M, N ), respectively. These Boolean-like operations on meanings can be used to investigate more complex (even asymmetric) meanings relationships, in addition to similarity. These definitions require using unnormalized scores, which is why we consider [0, 1] A * instead of the set of probabilities over A * as our meaning space. Note that, in contrast, traditional vector-space embeddings are ill-suited for representing such relationships.", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "In our experiments, we explore how this sort of (syntactic) meaning containment is related to entailment (⇒) in the conventional sense. As we discuss in Appendix F, given two sentences u and v such that u ⇒ v, the relation M v < M u is \"more true\" than M u < M v . Note that, for our particular score representation in eq. ( 1), neither M v < M u nor M v > M u can hold exactly; however we can quantify how far they are from being true. Based on this, we define the Entailment Test:", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "If d(M u ∧ M v , M v ) < d(M u ∧ M v , M u ), then u ⇒ v; otherwise, v ⇒ u.", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "Semantic representation for substrings. The meaning representation M s for a string considered until now assumes that s is used as a prompt, i.e., as a prefix within a longer string. We can also modify our definition to account for strings in any position, and in particular to words. Specifically, for any string u, we consider a meaning representation M u :", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "A * × A * → [0, 1] defined by: M u (s, t) := M (s u t).", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "Intuitively, the meaning of a word/string is the likelihood function of it appearing in between all \"contexts\" (s, t) -a very natural idea in distributional semantics, resembling for example the skipgram model used in word2vec (<PERSON><PERSON> et al., 2013) .", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "Using this representation, we can define partial ordering of meanings in the same way considered above for prompts. However, unlike the previous setting, sampling the support of M u or M v (contexts that contain u and v) is not trivial, since LLMs can only sample \"forward\" trajectories. In practice, we circumvent this issue by using a text corpus, WikiText (<PERSON><PERSON> et al., 2016) , to retrieve, rather than sample, paragraphs containing the given word to use as context. In Section 4, our experiments show that the partial ordering in semantic space aligns quite well with \"meaning containment\" in natural language, i.e., with hyponym/hypernym relations defined in WordNet (<PERSON>, 1995) . Specifically, if v is a hyponym of u, then it is natural to expect that M v < M u (see Appendix F for a justification). Thus, given two words (u, v) between which a hyponym relation exists, we define the Hyponym Test:", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "If d(M u ∧ M v , M v ) < d(M u ∧ M v , M u ), then v is a hyponym of u;", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "otherwise, u is a hyponym of v. We refer to Algorithm 2 in the Appendix for full details.", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "Semantic similarity for different modalities. The meaning representations we consider are applicable to any model that assigns likelihoods to sequences of tokens. In particular, they can be applied without modification to multimodal autoregressive models which accept both image and text prompts. In Section 4, we show how meaning representations obtained from the multimodal model LLa<PERSON> (<PERSON> et al., 2023) can effectively compute semantic image-text and image-image distances.", "section": "Algorithm 1 Similarity in Meaning Space", "sec_num": null}, {"text": "Implementation details. Apart from adding a full stop (\".\") at the end of each sequence that does not already end with a punctuation to complete the sentence, we evaluate each dataset verbatim (in Table 6 in the Appendix, we show results obtained without this step). For our baseline methods, we report the best result with or without adding a full stop, to ensure fair comparison. For experiments on LLaVA (<PERSON> et al., 2023) , we use the default query format to structure the input data. We do not apply any additional prompts/formatting for all other models unless otherwise mentioned. We use eq. (2) as our distance function. We report results using other metrics/divergences in the Appendix. We use multinomial sampling for all experiments on our method with sampling temperature λ = 1.0. We set n = 20 and m = 20 for sampling trajectories, based on ablations in Appendix A. Distance metric and hyperparameter choices for semantic similarity are based on a search using the validation set of the STS-B dataset, and are then fixed when evaluating on all test datasets. Evaluation procedure. We evaluate our method on the following tasks:", "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "Semantic Textual Similarity (STS) (<PERSON><PERSON><PERSON> et al., 2012; 2013; 2014; 2015; 2016; <PERSON><PERSON> et al., 2017) :", "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "The STS dataset scores how similar two pieces of texts are. We use the S<PERSON><PERSON> coefficient (scaled by 100×) to evaluate correlation with the human-annotated similarity scores.", "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "Stanford Natural Language Inference (SNLI) (<PERSON> et al., 2015) : SNLI labels pairs of strings based on the categories {entailment, neutral, contradiction}. The latter two are symmetric and can 2021) respectively. Our method outperforms all baselines, and even encoder-based methods like ST5-Enc-mean (11B). As model size scales, our method approaches the paragon of contrastive-trained models, even though the models we use have been trained only on unsupervised next-token prediction. be quantified via similarity. To evaluate our method's ability to compute asymmetric relationships, we restrict SNLI to only pairs of sentences labelled with the \"entailment\" relation. We express this as a binary classification task to determine the direction of entailment, i.e., given pair (u, v), we wish to determine if u ⇒ v, or v ⇒ u. We term this resultant task SNLI-Entailment.", "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "WordNet (<PERSON>, 1995) : WordNet establishes a hierarchy among English words through semanticsbased hypernym/hyponym relations. We sample branches from the WordNet hierarchy (see Appendix C.1), and recover their pairwise relations using operations in syntactic meaning space.", "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "Crisscrossed Captions (CxC) (<PERSON><PERSON><PERSON> et al., 2020) : CxC extends MS-COCO (<PERSON> et al., 2014) with human-labelled semantic similarity scores ranging from 0-5 for image-image, caption-caption, and image-caption pairs. Since most scores are close to 5 (e.g. original image-caption pairs from COCO) for which ranking comparisons would be vacuous, we subsample a balanced subset of 1000 pairs each from the image-image (CxC-SIS) and image-caption (CxC-SITS) dataset for our experiments.", "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "Semantic similarity. Our main baselines for comparison are methods which are 1) zero-shot, and 2) prompt-free. As such, we compare our method as presented in Algorithm 1 against encoder-based models, and the following baselines for autoregressive models given a pair of strings (u, v):", "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "1. Conditional Likelihood / Cross-Encoder (<PERSON><PERSON><PERSON><PERSON><PERSON>, 2022) : computes M (u|v). 3. (Last/Mean) Token: we represent u and v using the model's output distribution for the next token immediately following the sentence (last) or the average next-token predictions over the input sentence (mean), and produce a similarity score via cosine similarity. Figure 2 : (a) Accuracy in inferring the entailment direction. On SNLI-Entailment, our method outperforms existing baselines applied to the best model, Falcon-7B, showing that our notion of meaning containment aligns with that of natural language when quantifying \"entailment\" relationships between statements. (b) Accuracy in inferring hypernym/hyponym direction. Our method performs significantly better than chance on WordNet hyponym/hypernym prediction. (c) Visualization of word hierarchy recovered by our method on a subset of words using Falcon-7B (red indicates predictions that differ from the WordNet ground-truth).", "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "On the Semantic Textual Similarity benchmark, Table 1 shows that our method uniformly outperforms all baselines on one of the best autoregressive models, Falcon-7B, by a minimum relative improvement of 38.3%. Even when applied to GPT-2, a much smaller model, our method improves over Falcon-7B baselines by 3.8%. While our method is expectedly outperformed by models which are explicitly fine-tuned on contrastive-learning objectives, such as SimCSE (Gao et al., 2021) , it performs comparably to CLIP (<PERSON><PERSON> et al., 2021) , and when applied to Falcon-7B and LLaMA-33B respectively, significantly outperforms the best zero-shot encoder-based model (ST5-Enc-mean 11B) by a relative margin of 13.3% and 14.8%. We achieve this without any fine-tuning or prompting. We further highlight that our results do not rely on any human-annotated data or contrastive pairs, since the models we use have been trained only on unsupervised next-token prediction.", "section": "Joint", "sec_num": "2."}, {"text": "Lastly, our method shows an improvement in performance that correlates with model size, suggesting that further performance gains could be obtained as larger/better autoregressive models are used.", "section": "Joint", "sec_num": "2."}, {"text": "Our results also suggest that the proposed method can be used to evaluate pre-trained models in a zero-shot manner without requiring instruction-tuning or RLHF, since their alignment with human labelers seems to correlate with human perception of how good a model is.", "section": "Joint", "sec_num": "2."}, {"text": "Entailment via meaning containment. We show accuracies obtained on SNLI-Entailment in Figure 2a when applying the Entailment Test described in Section 3. We compare against Cond. Likelihood (u", "section": "Joint", "sec_num": "2."}, {"text": "⇒ v if M (v|u) > M (u|v), else v ⇒ u) and Joint Likelihood (u ⇒ v if M (uv) > M (vu))", "section": "Joint", "sec_num": "2."}, {"text": "on the best performing model, Falcon-7B. Our results show that the trajectories sampled from all LLMs that we tested align with the assumptions of the Entailment Test with significantly high probability, outperforming both random and likelihood baselines by 15.7% and 9.3% respectively.", "section": "Joint", "sec_num": "2."}, {"text": "Meaning containment of individual words. We apply the above-defined Hyponym Test to recover hypernym/hyponym relations from WordNet. Our results in Figure 2b and Figure 2c show that the Hyponym Test is mostly able to recover semantic containment relations between words, with an absolute improvement of 12.7% to 18.1% over the random baseline, depending on the model. Note that our computation of the hierarchy is based entirely on pairwise comparisons and does not explicitly enforce the transitivity of containments; however, transitivity is almost always already satisfied by the predictions of our method (i.e., the recovered hierarchy is an acyclic graph). We present more qualitative examples in Appendix D.2.", "section": "Joint", "sec_num": "2."}, {"text": "Vision-language experiments. Our method can be applied without any modification to models that accept multimodal token sequences. In Table 2 , we apply our method to CxC (<PERSON><PERSON><PERSON> et al., 2020) using the vision-language model LLaVA (<PERSON> et al., 2023) to show that we can measure semantic distances between not only text, but also between image-image (CxC-SIS) and image-text (CxC-SITS) pairs that align with that of human annotators. Our method outperforms all decoder-only baselines on both SIS and SITS. On SIS, our method even outperforms CLIP (<PERSON><PERSON> et al., 2021) which is trained explicitly on a contrastive image-text objective. We highlight that while the \"Cond. Likelihood\" baseline on SITS should most directly capture M (caption|image), our experiments show that it fares poorly compared to our method. We hypothesize that this results from the limitations of perplexity. For instance, likelihood scores are directly compared across captions of various lengths, for which length normalization does not sufficiently mitigate the bias towards shorter sentences (<PERSON> et al., 2022) . Our method avoids this issue entirely by construction, since we compare distributions across the same set of trajectories. We can optionally make use of \"alignment prompts\" to ensure that the trajectories from image and text modalities are more similar. This improves the resulting performance on the CxC-SITS task, outperforming the CLIP paragon by 13.3% (Table 2 ). We discuss this in Appendix E.2.", "section": "Joint", "sec_num": "2."}, {"text": "We proposed a strategy to investigate how autoregressive language models interpret text. By identifying \"meaning\" -from the perspective of the model -with score distributions over text continuations, we can compare the meaning of arbitrary strings. This notion of meaning correlates with that of human annotators, outperforming comparable zero-shot and prompt-free baselines on semantic textual similarity tasks using the same architectures. We further defined composition operators on meanings and showed how autoregressive language models can be used to quantify entailment between sentence pairs and hyponym relations between individual words. Our method can further be applied without modification to autoregressive vision-language architectures to define and compute meaning representations of images, outperforming even CLIP on semantic image similarity tasks.", "section": "CONCLUSIONS", "sec_num": "5"}, {"text": "A key limitation of our approach is its computational cost compared to embedding methods that require only a single forward pass. However, our ablations in Appendix A show that that using 10-20 trajectories of 10-20 tokens each is sufficient to achieve most of the performance gain compared to using more trajectories or tokens. We also note that both the sampling and score evaluation processes can be easily parallelized. Our approach in its current form is also not computationally efficient for semantic search, since the computation of pairwise similarities between queries and database elements is performed using a different set of trajectories for each new query. We explore ways to mitigate this in Appendix A.4 and the potential performance trade-offs that they incur.", "section": "CONCLUSIONS", "sec_num": "5"}, {"text": "Our method is intentionally prompt-free, as our goal in this work was to define the most canonical meaning representation of a string for a given model. Nevertheless, our experiments in Appendix E strongly suggest that designing appropriate \"alignment\" prompts could further significantly improve quantitative results on semantic similarity tasks. Our method can also be generalized to compare semantic distances between autoregressive models from the same family of architectures sharing a common vocabulary, since their meaning representations belong to the same space. We leave these directions for future work. According to our definitions, the meaning representations of complete and incomplete sentences differ, since the distributions over their trajectories are likely to be very different. To see this, consider the following pair of semantically similar sentences that differ by the last punctuation: \"The dog ate the bone\" and \"The dog ate the bone.\". The continuations of the latter are likely to start with a capital letter, but this does not hold for the former. Hence, our method is likely to attribute larger distances between these two sentences than humans. In Table 4 , we show that by ensuring all sentences we compare are complete, by appending a full stop when necessary, the similarity scores computed between sentences align better with that of human annotators. We also show that completing the sentence can occasionally improve results for certain baselines as well.", "section": "CONCLUSIONS", "sec_num": "5"}, {"text": "We note that Algorithm 1 is computationally expensive for semantic search, where we wish to retrieve the most similar sample in a search database D given a query q, since it requires multiple sampling and forward pass operations for each pairwise comparison d(q, s) for all s ∈ D. As such, an inference cost of O(|D|) is incurred each time a new query is received. This holds true even for previously proposed methods for semantic search using decoder-only models, e.g., (<PERSON><PERSON><PERSON><PERSON>, 2022) . Instead, if there exists a fixed set of trajectories T D for the search database D that can be used instead of A * in eq. ( 1), then M s for each item s ∈ D can be pre-computed beforehand, incurring a one-time cost of O(|D|). Hence, for each subsequent query, we only need to incur an O(1) inference cost to compute M q on T D . This can be compared against the pre-computed embeddings in D using standard distance functions such as L1. We present a proof-of-concept experiment on the STS-B validation dataset to observe the trade-off in performance that this incurs in Table 5 , where we obtain T D by naively selecting n examples from the dataset uniformly at random, from each of which we generate a single trajectory. Nevertheless, our preliminary results demonstrate that it is indeed possible to achieve satisfactory performance using fixed sets of trajectories. We leave investigating more sophisticated methods to construct T D for future work.", "section": "A.4 EXTENSION TO SEMANTIC SEARCH", "sec_num": null}, {"text": "We provided baseline comparisons in Table 1 of the main body of the paper against one of the best model tested, Falcon-7B. In Table 6 , we provide additional baseline results for several other autoregressive architectures used.", "section": "B FURTHER BASELINES", "sec_num": null}, {"text": "We use the base GPT-2, GPT-2-XL (<PERSON><PERSON> et al., 2019) , LLaMA-13B (<PERSON><PERSON><PERSON><PERSON> et al., 2023) and Falcon-7B (<PERSON><PERSON><PERSON><PERSON> et al., 2023) for experiments on models trained with unsupervised pre-training objectives. We use Vicuna-13B (<PERSON> et al., 2023) and StableVicuna-13B1 as the instruction-tuned version and the RLHF-trained (reinforcement learning with human feedback) ver- 53.29 sion of LLaMA-13B respectively. We use LLaVA1 (<PERSON> et al., 2023) for our multimodal experiments, which is trained to accept both image and text inputs.", "section": "C ADDITIONAL IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "Technically, computing distances with Equation (2) when compositional terms are involved would require sampling trajectories from the composed distributions. In particular, evaluating d(M u ∧ M v , M u ) in the Entailment test would require sampling trajectories T u∧v from the composed distribution M u ∧ M v , then approximating Equation ( 2) with the set of trajectories T u∧v ⊔ T u . For the sake of simplicity and computational efficiency, we instead compute M u ∧ M v over the set of trajectories T u ⊔ T v sampled from u and v, which we empirically found to be similarly effective when applied to downstream tasks. We show in Figure 4 that the alignment of our method on semantic textual similarity with human annotators scales with model size.", "section": "C ADDITIONAL IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "T u = {(s i , t i )} n i=1 ← Sample n paragraphs (s i u t i ) containing u from D corpus T v = {(s i , t i )} 2n i=n+1 ← Sample n paragraphs (s i v t i ) containing v from D corpus T ← T u ⊔ T v Initialize M u = M v = M u ∧ M v = ∅ for t = (a 1 . . . a mt , b 1 . . . b m ′ t ) ∈ T u ⊔ T v do M u [t] ← mt i=1 P M (a i |a 1 . . . a i-1 ) • P M (u|a 1 . . . a mt )• m ′ t i=1 P M (b i |a 1 . . . a mt u b 1 . . . b i-1 ) 1/(mt+m ′ t +1) M v [t] ← mt i=1 P M (a i |a 1 . . . a i-1 ) • P M (v|a 1 . . . a mt )• m ′ t i=1 P M (b i |a 1 . . . a mt v b 1 . . . b i-1 ) 1/(mt+m ′ t +1) M u [i] ∧ M v [t] ← min(M u [t], M v [t]) end for if d(M u , M u ∧ M v ) < d(M v , M u ∧ M v ) then return u hyponym of v else return v hyponym of u end if D ADDITIONAL VISUALIZATIONS D.1 PERFORMANCE SCALES WITH MODEL SIZE", "section": "C ADDITIONAL IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "In Figure 5 , we show further visualizations of the hyponym/hypernym hierarchies established by our method on WordNet using Falcon-7B. Our definition of meaning in the context of large language is prompt-free, and hence not subject to the drawbacks and variabilities that arise from prompt-engineering. However, prompts can naturally be used to improve performances on downstream tasks by conditioning the trajectories obtained from input strings. 2023) for LLaMA-13B, and show that prompt-based methods are brittle and generalize poorly to other architectures apart from that which they were tuned on. Nevertheless, we show that prompting, while detracting from retaining our \"pure\" notion of meaning, can be used to further improve our results on downstream semantic textual similarity tasks. We also compare against other prompt-based methods here. For Ours-Prompt-1, we simply prepend \"The meaning of this sentence is: \" to each input string. For Ours-Prompt-2, we append \"This sentence implies \" to the end of each input string to condition the set of trajectories towards logical implications, achieving superior results across zero-shot, prompt-based methods. † indicates results taken from <PERSON> et al. (2023) By implementing an existing prompt-based method (<PERSON> et al., 2023) on LLaMA-13B, we show in Table 7 that prompt-based methods are brittle and model-specific, hence require careful tuning for each specific architecture. In contrast, our original method is prompt-free and robust against such variances arising from prompt-engineering.", "section": "D.2 WORDNET HYPONYM/HYPERNYM RELATIONS", "sec_num": null}, {"text": "Nevertheless, we present some preliminary investigations for augmenting our method with prompts in Table 7 for the STS task. We also compare against existing zero-shot prompt-based methods. We show that prompting can also significantly improve performance over the prompt-free approach for the STS task, by appending \"The meaning of this sentence is: \" to each input string when generating trajectories. We note that we did not carefully search over prompts, and simply tried the first ones (above) that came to mind. It is likely that there exist others which work better.", "section": "D.2 WORDNET HYPONYM/HYPERNYM RELATIONS", "sec_num": null}, {"text": "In the main paper, we presented prompt-free approaches for extracting similarity scores from multimodal inputs. However, we note that by our definitions, LLaVA (<PERSON> et al., 2023) does not technically attribute the same meaning to images and captions. We visualize this in Figure 6 , where we show that image and caption inputs are continued very differently by the model. For instance, given an image, LLaVA generally attempts to generate a caption. On the other hand, when given a caption, LLaVA simply continues it, often in an unpredictable manner. In spite of this misalignment, there exists sufficient overlap in likelihood scores to outperform all baselines as observed in Table 2 of the main paper.", "section": "E.2 <PERSON>IGN<PERSON><PERSON> PROMPTS FOR VISION-LANGUAGE MODELS", "sec_num": null}, {"text": "We demonstrate that a prompt can optionally be used to align the meaning representations for vision and text modalities for the purposes of semantic comparison. We achieve this by conditioning the caption continuations to match the continuations of images. In particular, we append \"This is a caption for an image. Describe this image. This image shows\" to the text inputs, and \"Describe this image. This image shows\" to image inputs. Figure 6 (Right) shows that this successfully aligns the trajectories of both modalities. Indeed Table 2 of the main paper shows that this improves over the prompt-free version of our method on the CxC-SITS task by 18.4%, and outperforms the CLIP paragon by 13.3%.", "section": "E.2 <PERSON>IGN<PERSON><PERSON> PROMPTS FOR VISION-LANGUAGE MODELS", "sec_num": null}, {"text": "A young man holding an umbrella next to a herd of cattle.", "section": "E.2 <PERSON>IGN<PERSON><PERSON> PROMPTS FOR VISION-LANGUAGE MODELS", "sec_num": null}, {"text": "Image Trajectories a young child, likely a boy, standing amongst a young man, presumably a farmer, a small child, possibly a young boy, standing a young child standing in a bamboo a small child, likely a girl, standing among … Text Trajectories a young man holding an umbrella next to a young man standing next to a herd of a young man standing next to a herd of a young man standing beside a herd of cattle a young man standing next to a herd of … ", "section": "E.2 <PERSON>IGN<PERSON><PERSON> PROMPTS FOR VISION-LANGUAGE MODELS", "sec_num": null}, {"text": "In this section, we discuss how the partial ordering defined on our meaning representations is related to entailment (⇒) between statements and to hyponym/hypernym relations between words.", "section": "F MEANING CONTAINMENTS", "sec_num": null}, {"text": "In the main body of the paper, we claimed that if u ⇒ v then M v ≤ M u is more natural than M u ≤ M v . Our empirical experiments indeed show that our Entailment Test succeeds significantly more often than chance. Intuitively, this means that if u ⇒ v then more continuations for v are feasible continuations for u, instead of the other way around.", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "To understand why this is the case, we consider the sets C u , C v of consequents of u and v, that is, the set of sentences t such that u ⇒ t or v ⇒ t respectively. If u ⇒ v, then by transitivity of entailment we have", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "C v ⊂ C u (since v ⇒ t implies u ⇒ t).", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "Thinking M u and M v as sets, then if M u = C u and M v = C v were true (i.e., if the set of feasible continuations coincided with the set consequents), then this would justify our claim. In practice, continuations and consequents do not coincide, especially because some continuations are not consequents. However, it is generally true that consequents are valid continuations-so approximately C u ⊂ M u , C v ⊂ M v -and overall consequents seem sufficiently frequent as continuations to dictate the containment relation among general continuations.", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "To make this argument more concrete, consider the sentences u =\"<PERSON>, the neighbor's dog, is barking.\" and v =\"A dog is barking.\", so that u ⇒ v. If t is a continuation of v, then t could in general be", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "• a consequent of v (v ⇒ t) for example t =\"Therefore, I can't sleep.\"", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "• an antecedent of v (t ⇒ v) for example t =\"Indeed, there is a cat.\".", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "• non-comparable with v (neither v ⇒ t nor t ⇒ v hold) for example, \"The dog is brown.\"", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "or \"The dog's name is <PERSON>\".", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "From these examples, we see that: 1) consequent continuations for v are also valid continuations for u; 2) antecedent continuations are somewhat unnatural and likely uncommon; 3) non-comparable continuations of v may or may not be valid continuations of u. Overall, if we think of consequent continuations as the default, then we expect the containment direction M v ≤ M u to hold more than", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "M u ≤ M v F.2 HYPONYM TEST In the paper, we claimed that if v is a hyponym of u then M v ≤ M u is a more natural relation than M v ≤ M u .", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "Our experiments indeed suggest that M v ≤ M u occurs significantly more often than chance. Intuitively, this means that if v is a hyponym of u then v can be substituted with u more often than the other way around.", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "To investigate why this is the case, we distinguish between two types of usages of a common noun v (e.g., \"dog\"):", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "• Definite reference: when v refers to a specific instance or set of instances of the noun, for example \"The dog is barking.\"", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "• Generic reference: when v refers to all instances of the noun, for example \"Any dog is an animal\". Now, if s is a sentence that uses a v with definite reference, then it is possible to replace v with a hypernym in s (\"The dog is barking.\"→\"The animal is barking.\"). In contrast, if s uses v with generic reference, then we can replace v with a hyponym (\"Any dog is an animal.\"→\"Any German Shepherd is an animal.\"). Thus, whether hyponym relations correspond to M v ≤ M u or M u ≤ M v depends on which type of reference is more common. Our empirical results suggest that definite reference is more common-as one might probably expect, particularly for singular nouns.", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "We note that in practice, M u ≤ M v rarely happens. As an alternative, we can express this relation as d(M u ∧M v , M u ) = 0, where ∧ represents the meet operation given by M u ∧M v := min(M u , M v ).", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "In other words, M u ≤ M v if M u is contained within their intersection. This alternative formulation offers a crucial advantage, as it provides a soft measure of containment that quantifies the strength of this relation.", "section": "F.1 ENTAILMENT TEST", "sec_num": null}, {"text": "In this section, we present a more theoretical discussion that motivates our notion of meaning representation. We also introduce some definitions and perspectives on LLMs that were not required for describing the methods proposed in the paper but that may be of independent interest. We recall that in the main body of the paper, we identified a language model with a map M :", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "A * × A * → [0, 1].", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Here we take a step back and start from a more primitive notion of autoregressive token generator. Definition 1. An autoregressive token generator is a map G : A * × A → [0, 1] associating any string s with a score G(a|s) for identifying the next token a in A. Given any u ∈ A * , we use G u to denote the prompted token generator, that is, a token generator defined by G u (a|s) := G(a|us). We write G(A) for the set of all autoregressive token generators with tokens from A.", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Starting with an initial prompt u 0 , a greedy text generation process using the generator G returns a sequence of strings u i+1 = u i a, or a trajectory, where a is a token recovered from G(u i ) according to some sampling scheme.", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Given a candidate trajectory u = a 1 . . . a n , the token generator provides a sequence of token-level scores G(a 1 . . . a i-1 )(a i ) in [0, 1]. These scores can be aggregated, for example by simply taking their product. In practice, it is more common to normalize by sequence length. Thus, we consider the sequence level score as given by", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "EQUATION", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "This choice allows us to use a generator evaluate candidate trajectories, obtaining a map A * → [0, 1]. We think of such a map as a (soft) \"predicate\" characterizing strings that the model considers \"feasible.\"", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Definition 2. A linguistic predicate is a map L : A * → [0, 1]. We write L(A) for the set of all linguistic predicates with tokens from A.", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Any autoregressive language generator G can thus be uniquely associated with a linguistic predicate L(G) ∈ L(A) using eq. ( 4). Conversely, any linguistic predicate L determines an associated token generator G(L) ∈ G(L) by setting", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "G(L)(a|u) = min L(u a) |u|+1 L(u) |u| , 1 ,", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "where this minimum is taken to be 1 whenever the denominator of the left term is zero. Thus, we obtain two \"dual\" maps L : G(A) → L(A) and G : L(A) → G(A). These maps are not full inverses but satisfy G • L = Id G .", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "A generator G also uniquely determines a language model M G : A * × A * → [0, 1], as defined in the main body of the paper, by simply setting M B (s|u) := L(G u )(s). Using these definitions, the meaning representation of the prompt u for the token generator G (or the model M G ) is the linguistic predicate L(G u ) associated with the prompted generator.", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "As mentioned in the paper, these ideas are closely connected to the theory of automata and formal languages. To see this, consider a \"crisp\" token generator G whose values are always in {0, 1}. In this setting, we say that a string u = a 1 . . . a n (or trajectory) is \"feasible\" if G(a i |a 1 . . . a i-1 ) = 1 for all i. The associated predicate L(G) also takes values in {0, 1} and can be seen as the formal language consisting of all feasible strings (we identify {0, 1}-valued functions with subsets of the domain). 1 We now remark that, if a string u is acceptable for G, then", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "L(G u ) = u -1 L(G),", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "where u -1 L := {v ∈ A * : uv ∈ L} is the \"left quotient\" of L by u (sometimes also known as the <PERSON><PERSON><PERSON><PERSON> derivative). The set u -1 L is a class of the equivalence relation on A * given by u 1 ∼ L u 2 ⇔ u 1 s ∈ L iff u 2 s ∈ L.", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Thus, u 1 ∼ L u 2 if u 1 and u 2 have no \"distinguishing continuations.\" This equivalence relation features in the construction of the minimal automaton that accepts a given language Pin (2022). More precisely, for any language L, a minimal automaton for L has states identified with left quotients {u -1 L : s ∈ A * }, accepting states corresponding F = {s -1 L : s ∈ L} (classes of strings in L), and actions for each token a ∈ A described by (s -1 L) • a = (sa) -1 L.", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Thus, the meaning representations L(G u ) = u -1 L(G) for acceptable strings u correspond exactly to the states of a minimal automaton accepting the language L(G). We also remark that a different equivalence relation L, sometimes known as the syntactic congruence, is given by", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "u 1 ≈ L u 2 ⇔ su 1 t ∈ L iff su 2 t ∈ L,", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "and has the property that u 1 , u 2 ∈ A * induce the same action on states of the minimal automaton if and only if u 1 ≈ L u 2 ; in other words, the monoid of transformations on states is given by A * / ∼ synt (Pin, 2022) . The equivalence classes for this relation correspond to the meaning representation for substrings that we consider in the paper, a refinement of the meaning representation for prefixes.", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Remark 3. If a string u is not feasible for a crisp generator G, then we have u -1 L(G) = ∅, since the product in eq. ( 4) is zero when at least one token is not acceptable. On the other hand, according to Definition 1, the language L(G u ) depends only on tokens following u, and thus may a priori be arbitrary and unrelated to the language L(G). Practically, this means that infeasible prompts may lead to completely unpredicatable continuations. This intuition may also be useful for general (non-crisp) language models, by thinking of infeasible prompts as strings with very low likelihood for the model.", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "We conclude by revisiting these ideas in the actual [0, 1]-valued setting considered in the paper. To do so, we take a \"coalgebraic\" perspective on automata, as described in (<PERSON>, 2012) . We view a deterministic automaton with [0, 1]-valued outputs and action set A as a triple (S, δ, λ) where S is a set and δ, λ are maps δ : S × A → S, λ : S → [0, 1].", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Here δ describes state transitions and λ describes (soft) acceptance of states (note that we do not model the initial state; for this reason we sometimes also use the term \"process\" instead of automaton). An autoregressive token generator G can be seen as an automaton in which S = A * , δ is string concatenation, and λ is the predicate L(G).", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Given two automata (S, δ, λ) and (T, δ ′ , λ ′ ), a morphism between the two is defined by a map between states f : S → T such that f (δ(s, a)) = δ ′ (f (s), a) and λ(s) = λ ′ (f (s)), ∀s ∈ S, a ∈ A.", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "We write such a morphism as f : (S, δ, λ) → (T, δ ′ , λ ′ ).", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Intuitively, a \"semantic interpretation\" of an automaton (S, δ, v) is given by a morphism m : (S, δ, λ) → (U, γ, µ), where U is some sort of \"meaning space\" and γ, µ correspond transitions and observations of S within U .1 Moreover, a desirable property would be that the meaning process (U, γ, µ) is also universal: this would mean that any automaton (S, δ, λ) can be interpreted in (U, γ, µ) and in a unique way. The following standard result shows such a process actually exists and its states correspond to predicates.", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "Proposition 4 (Proposition 2.3.5 <PERSON> ( 2012)). Let (U, γ, µ) be the process given by U = [0, 1] A * and for any L ∈ U : The association on the right is analogous to the left-quotient set u -1 L(G) considered for crisp models and motivates the semantic representation considered in this work.", "section": "G LANGUAGES MEANINGS", "sec_num": null}, {"text": "https://huggingface.co/CarperAI/stable-vicuna-13b-delta", "section": "", "sec_num": null}, {"text": "https://huggingface.co/liuhaotian/llava-v1-0719-336px-lora-merge-vicuna-13b-v1.3", "section": "", "sec_num": null}, {"text": "According to our definitions, the language L(G) associated with a generator is always a prefix-closed set. If the vocabulary A has a \"end of sentence\" [EOS] token, one could also consider the language of all strings that are feasible and also \"complete,\" i.e., such that G([EOS]|u) = 1.", "section": "", "sec_num": null}, {"text": "This kind of interpretation assumes that the set A also acts on meanings. When modeling natural language, it is probably more natural to think of A as some collection of \"meaningful sentences\" (as opposed to tokens) so that the state space S = A * of a language generator is the set of concatenations of such sentences. This would not significantly change our discussion nor the construction of our representations.", "section": "", "sec_num": null}], "back_matter": [{"text": "In this section, we present ablation studies on how trajectories are sampled in Appendix A.1, choice of distance function in Appendix A.2, completing \"incomplete\" sentences with a single full stop in Appendix A.3, and discuss extensions to perform computationally efficient semantic search in Appendix A.4.", "section": "A ABLATION STUDIES", "sec_num": null}, {"text": "We present ablations on Algorithm 1 to investigate the impact of (1) number of trajectories (2) length of trajectories and (3) sampling temperature. All experiments are done on the validation set of STS-B instead of test set to avoid over-fitting results to the test set.Figure 3 (a) shows that performance on evaluating semantic similarity increases with both number and length of trajectories sampled, at the cost of computational time. We use n = m = 20 for all of our main experiments, which is sufficient to yield most of the performance. We also ablate of sampling temperature λ in Figure 3(b) , where we show that sampling trajectories that are either too diverse or lack diversity (as measured by λ) tends to harm performance. Instead, the standard temperature value λ = 1.0 yields the best results. Figure 3 : Ablation over maximum length (M), number (N) of trajectories, and sampling temperature (λ) on STS-B validation dataset using the Falcon-7B model. While only a small number of short trajectories is sufficient to yield good results, performance on semantic similarity generally increases with both number and length of trajectories. Too much diversity and lack of diversity in the sampled trajectories both harm performance, as shown by higher and lower values of λ respectively.", "section": "A.1 ABLATION ON TRAJECTORIES", "sec_num": null}, {"text": "We further ablate over the choice of distance function in Table 3 . For distance functions on probability spaces (<PERSON><PERSON>, Total Variation, Symmetric KL-Divergence), we normalize the scores M u usingto convert them into a probability distribution summing to 1. We use τ = 0.5 which we experimentally determined to perform best. We compare against our choice of distance function as defined in eq. ( 2), and a modified version that uses L2 instead of L1 loss, which we refer to as Log-L1 and Log-L2 respectively. We show that most choices of distance functions (Symmetric KL Divergence, Hellinger distance, Log-L2, Log-L1) work reasonably well for computing the semantic distance between strings. We chose Log-L1 in our main experiments, which performs best.", "section": "A.2 CHOICE OF DISTANCE FUNCTION", "sec_num": null}], "ref_entries": {"FIGREF0": {"text": "Figure 1: Sentences with similar meanings produce similar score distributions over their continuations (top), while sentences with different meanings produce different score distributions over their continuations (bottom).", "fig_num": "1", "type_str": "figure", "uris": null, "num": null}, "FIGREF1": {"text": "and a possible continuation sequence t ∈ A * with a score M (t|s) ∈ [0, 1]. Intuitively, this score represents the likelihood of the model sampling t as a continuation of s. For our experiments, we use as score the inverse perplexity: M (t = (a 1 . . . a m )|s) := m i=1 P M (a i |s a 1 . . . a i-1 ) 1/m ,", "fig_num": null, "type_str": "figure", "uris": null, "num": null}, "FIGREF2": {"text": "Likelihood: measures the likelihood of the concatenation of u and v, M (uv) = M (uv|ϵ) where ϵ is begin-of-sentence token [BOS], normalized by number of tokens. If [BOS] is not supported by the model, we use the M (u v n . . . v 2 |v 1 ) instead where v = (v n . . . v 1 ).", "fig_num": null, "type_str": "figure", "uris": null, "num": null}, "FIGREF4": {"text": "Figure4: Plot of performance on semantic textual tasks vs number of model parameters, as measured using GPT-2, GPT-2-XL, Falcon-7B, LLaMA-13B, and LLaMA-33B.", "fig_num": "4", "type_str": "figure", "uris": null, "num": null}, "FIGREF5": {"text": "Figure 5: WordNet Hyponym/Hypernym Relation predictions using Falcon-7B", "fig_num": "5", "type_str": "figure", "uris": null, "num": null}, "FIGREF6": {"text": "Figure6: A prompt can be used to align the meaning representations (i.e., distribution over trajectories) for vision and text modalities to measure image-caption similarities. We obtain trajectories on the right by appending \"Describe this image. This image shows\" to image inputs, and appending \"This is a caption for an image. Describe this image. This image shows\" to caption text inputs.", "fig_num": null, "type_str": "figure", "uris": null, "num": null}, "FIGREF7": {"text": "γ(L)(a) = L a , where L a (s) := L(as), µ(L) = L(ϵ),where ϵ is the empty string. Then, for any automaton (S, δ, λ), there exists a unique morphism m : (S, δ, λ) → (U, γ, µ). Moreover, (U, γ, µ) is determined (up to isomorphism) by this property.The unique map m : (S, δ, λ) → (U, γ, µ) from this Proposition can be described asS → U = [0, 1] A * , s → (v → λ(δ * (s, v))), s ∈ S, v ∈ A * ,where δ * : S × A * → S is the interated transition function. In particular, if we consider the process (A * , •, L(G)) associated with a token generator G, this unique map is u → (v → L(G)(uv)), u, v ∈ A * .", "fig_num": null, "type_str": "figure", "uris": null, "num": null}, "TABREF1": {"text": "Comparison with other prompt-free and zero-shot methods on Semantic Textual Similarity benchmarks. * ; † indicate results taken from <PERSON> et al. (2021); <PERSON> et al. (", "html": null, "type_str": "table", "content": "<table/>", "num": null}, "TABREF3": {"text": "Image-Image Similarity and Image-Text (Caption) Similarity on balanced subsets of CxC-SIS and CxC-SITS respectively. Even without any prompts, our method outperforms all zero-shot baselines on both modalities. The performance on the image-text similarity can be further boosted with an alignment prompt, allowing our method to outperform even CLIP which is explicitly trained with a contrastive objective to output aligned image-text embeddings. For CLIP (Vision), we use image embeddings prior to projection onto text embedding space.", "html": null, "type_str": "table", "content": "<table><tr><td>Architecture</td><td>Method</td><td colspan=\"3\">CxC-SIS CxC-SITS Average</td></tr><tr><td/><td>CLIP-ViTL/14</td><td>66.33</td><td>64.25</td><td>65.29</td></tr><tr><td>CLIP (Radford et al., 2021)</td><td>CLIP-ViTB/16 CLIP-ViTL/14 (Vision)</td><td>66.95 71.45</td><td>64.60 -</td><td>65.78 -</td></tr><tr><td/><td>CLIP-ViTB/16 (Vision)</td><td>72.08</td><td>-</td><td>-</td></tr><tr><td/><td>Cond. Likelihood</td><td>-</td><td>29.46</td><td>-</td></tr><tr><td><PERSON><PERSON><PERSON> (Liu et al., 2023)</td><td>Mean Token Last Token</td><td>32.76 26.91</td><td>-0.52 2.43</td><td>16.12 14.67</td></tr><tr><td/><td>Ours</td><td>81.47</td><td>57.14</td><td>69.31</td></tr><tr><td>LLaVA (Liu et al., 2023) w/ Alignment Prompt</td><td>Mean Token (Prompt) Last Token (Prompt) Ours (Prompt)</td><td>32.76 26.91 81.47</td><td>-0.07 6.21 67.63</td><td>16.35 16.56 74.55</td></tr></table>", "num": null}, "TABREF4": {"text": "Ablation over the choice of distance function on LLaMA-13B and LLaVA. For Hellinger distance, Total Variation (TV), and Symmetric KL-Divergence, we set τ = 0.5 as in eq. (3). Here we use the prompt-aligned version of our method for SITS.", "html": null, "type_str": "table", "content": "<table><tr><td>Metric</td><td colspan=\"7\">STS-B STS-12 STS-13 STS-14 STS-15 STS-16 SNLI SIS SITS</td></tr><tr><td>Hellinger</td><td>69.7</td><td>53.0</td><td>65.5</td><td>50.7</td><td>65.6</td><td>71.2</td><td>65.8 81.3 67.6</td></tr><tr><td>TV</td><td>51.2</td><td>40.9</td><td>47.8</td><td>32.8</td><td>42.4</td><td>50.9</td><td>64.1 78.5 62.4</td></tr><tr><td>Sym-KL</td><td>69.7</td><td>52.9</td><td>65.4</td><td>50.6</td><td>65.5</td><td>71.1</td><td>65.9 80.9 67.6</td></tr><tr><td>Log-L1</td><td>70.6</td><td>52.5</td><td>65.9</td><td>53.2</td><td>67.8</td><td>74.1</td><td>63.6 81.0 67.6</td></tr><tr><td>Log-L2</td><td>69.2</td><td>51.4</td><td>64.1</td><td>48.2</td><td>65.7</td><td>71.6</td><td>65.4 80.8 67.0</td></tr><tr><td colspan=\"5\">A.3 COMPLETING THE SENTENCE WITH FULL STOP</td><td/><td/><td/></tr></table>", "num": null}, "TABREF5": {"text": "Ablation over adding a full stop (FS) to incomplete sentences. Based on our definitions, meaning representations of complete and incomplete sentences differ. We show that the meaning similarities of complete sentences align better with that of human annotators.", "html": null, "type_str": "table", "content": "<table><tr><td>Method</td><td colspan=\"8\">FS? STS-B STS12 STS13 STS14 STS15 STS16 SICK-R Avg</td></tr><tr><td>Baselines (Falcon-7B)</td><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>Cond. Likelihod</td><td>✗</td><td>46.0</td><td>22.3</td><td>52.5</td><td>41.7</td><td>46.9</td><td>51.8</td><td>54.3 45.1</td></tr><tr><td>Joint Likelihood</td><td>✗</td><td>38.3</td><td>4.5</td><td>38.3</td><td>32.4</td><td>28.3</td><td>34.4</td><td>43.3 31.4</td></tr><tr><td>Last token</td><td>✗</td><td>24.9</td><td>18.9</td><td>13.6</td><td>4.2</td><td>4.7</td><td>18.5</td><td>34.1 17.0</td></tr><tr><td>Mean token</td><td>✗</td><td>18.8</td><td>18.0</td><td>25.9</td><td>18.5</td><td>25.8</td><td>27.5</td><td>37.3 24.5</td></tr><tr><td>Cond. Likelihood</td><td>✓</td><td>46.7</td><td>25.1</td><td>53.9</td><td>41.9</td><td>53.7</td><td>54.2</td><td>57.2 47.5</td></tr><tr><td>Joint Likelihood</td><td>✓</td><td>38.1</td><td>6.0</td><td>40.8</td><td>32.7</td><td>33.7</td><td>35.7</td><td>47.6 33.5</td></tr><tr><td>Last token</td><td>✓</td><td>23.1</td><td>27.0</td><td>20.1</td><td>8.5</td><td>18.7</td><td>18.3</td><td>40.8 22.4</td></tr><tr><td>Mean token</td><td>✓</td><td>18.1</td><td>21.7</td><td>25.4</td><td>16.9</td><td>26.3</td><td>26.6</td><td>33.9 24.1</td></tr><tr><td>Baselines (LLaMA-13B)</td><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>Cond. Likelihood</td><td>✗</td><td>41.9</td><td>19.8</td><td>54.6</td><td>40.1</td><td>54.6</td><td>52.2</td><td>55.0 45.5</td></tr><tr><td>Joint Likelihood</td><td>✗</td><td>36.6</td><td>-0.6</td><td>34.8</td><td>27.8</td><td>28.2</td><td>32.6</td><td>43.2 28.9</td></tr><tr><td>Last token</td><td>✗</td><td>18.8</td><td>15.9</td><td>18.2</td><td>5.6</td><td>2.3</td><td>9.9</td><td>35.6 15.2</td></tr><tr><td>Mean token</td><td>✗</td><td>28.0</td><td>22.0</td><td>27.5</td><td>19.6</td><td>30.8</td><td>35.8</td><td>43.7 29.6</td></tr><tr><td>Cond. Likelihood</td><td>✓</td><td>44.3</td><td>20.8</td><td>51.8</td><td>38.6</td><td>56.0</td><td>50.9</td><td>56.7 45.6</td></tr><tr><td>Joint Likelihood</td><td>✓</td><td>36.7</td><td>1.1</td><td>35.0</td><td>27.7</td><td>33.0</td><td>32.4</td><td>48.0 30.6</td></tr><tr><td>Last Token</td><td>✓</td><td>18.2</td><td>24.2</td><td>29.0</td><td>16.8</td><td>21.9</td><td>10.2</td><td>40.8 23.0</td></tr><tr><td>Mean Token</td><td>✓</td><td>28.8</td><td>25.2</td><td>30.2</td><td>20.2</td><td>31.5</td><td>35.1</td><td>45.0 30.9</td></tr><tr><td>Ours</td><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>Ours (GPT-2)</td><td>✗</td><td>48.3</td><td>28.7</td><td>39.7</td><td>23.8</td><td>35.7</td><td>60.0</td><td>56.6 41.8</td></tr><tr><td>Ours (GPT-2-XL)</td><td>✗</td><td>56.8</td><td>32.5</td><td>49.5</td><td>29.1</td><td>45.2</td><td>66.0</td><td>63.1 48.9</td></tr><tr><td>Ours (Falcon-7B)</td><td>✗</td><td>67.2</td><td>44.0</td><td>62.1</td><td>44.7</td><td>57.5</td><td>76.1</td><td>69.3 60.1</td></tr><tr><td>Ours (LLaMA-13B)</td><td>✗</td><td>66.9</td><td>39.9</td><td>61.2</td><td>45.0</td><td>56.4</td><td>74.4</td><td>68.7 58.9</td></tr><tr><td>Ours (GPT-2)</td><td>✓</td><td>55.2</td><td>39.9</td><td>42.6</td><td>30.5</td><td>52.4</td><td>62.7</td><td>62.0 49.3</td></tr><tr><td>Ours (GPT-2-XL)</td><td>✓</td><td>62.1</td><td>43.6</td><td>54.8</td><td>37.7</td><td>61.3</td><td>68.2</td><td>68.4 56.6</td></tr><tr><td>Ours (Falcon-7B)</td><td>✓</td><td>67.7</td><td>56.3</td><td>66.5</td><td>53.0</td><td>67.4</td><td>75.5</td><td>73.5 65.7</td></tr><tr><td>Ours (LLaMA-13B)</td><td>✓</td><td>70.6</td><td>52.5</td><td>65.9</td><td>53.2</td><td>67.8</td><td>74.1</td><td>73.0 65.3</td></tr></table>", "num": null}, "TABREF6": {"text": "Trade-off in performance on STS-B validation set from using a fixed set of trajectories (m = 20) for all pairwise distance comparisons.", "html": null, "type_str": "table", "content": "<table><tr><td>Method</td><td><PERSON><PERSON><PERSON> (x100)</td></tr><tr><td>Ours (Falcon-7B)</td><td>74.74</td></tr><tr><td>-Fixed Traj (n = 20)</td><td>49.41</td></tr><tr><td>-Fixed Traj (n = 40)</td><td/></tr></table>", "num": null}, "TABREF7": {"text": "Comparison of our method against baselines (best among with/without fullstop) for each model architecture on STS tasks. Require: Model M , Words u and v, number of trajectories n, distance d, Text Corpus D corpus", "html": null, "type_str": "table", "content": "<table><tr><td>Model</td><td>Method</td><td colspan=\"7\">STS-B STS12 STS13 STS14 STS15 STS16 SICK-R Avg</td></tr><tr><td/><td>Cond. Likelihood</td><td>37.9</td><td>28.6</td><td>39.1</td><td>34.3</td><td>50.5</td><td>47.1</td><td>53.0 41.5</td></tr><tr><td/><td>Joint Likelihood</td><td>27.9</td><td>18.4</td><td>22.0</td><td>23.1</td><td>32.8</td><td>27.2</td><td>44.0 27.9</td></tr><tr><td>GPT-2</td><td>Last token</td><td>27.7</td><td>8.4</td><td>23.0</td><td>10.5</td><td>31.0</td><td>26.6</td><td>41.9 24.1</td></tr><tr><td/><td>Mean token</td><td>20.4</td><td>17.0</td><td>22.0</td><td>17.6</td><td>36.2</td><td>31.5</td><td>38.4 26.2</td></tr><tr><td/><td>Ours</td><td>55.2</td><td>39.9</td><td>42.6</td><td>30.5</td><td>52.4</td><td>62.7</td><td>62.0 49.3</td></tr><tr><td/><td>Cond. Likelihood</td><td>40.3</td><td>23.8</td><td>43.2</td><td>33.6</td><td>51.3</td><td>48.7</td><td>55.0 42.3</td></tr><tr><td/><td>Joint Likelihood</td><td>31.4</td><td>13.3</td><td>29.3</td><td>23.8</td><td>35.2</td><td>28.6</td><td>46.0 29.7</td></tr><tr><td>GPT-2-XL</td><td>Last token</td><td>24.1</td><td>-5.9</td><td>20.9</td><td>5.0</td><td>25.6</td><td>21.4</td><td>40.8 18.8</td></tr><tr><td/><td>Mean token</td><td>21.1</td><td>13.0</td><td>28.0</td><td>16.7</td><td>34.9</td><td>33.0</td><td>37.8 26.4</td></tr><tr><td/><td>Ours</td><td>62.1</td><td>43.6</td><td>54.8</td><td>37.7</td><td>61.3</td><td>68.2</td><td>68.4 56.6</td></tr><tr><td/><td>Cond. Likelihood</td><td>46.7</td><td>25.1</td><td>53.9</td><td>41.9</td><td>53.7</td><td>54.2</td><td>57.2 47.5</td></tr><tr><td/><td>Joint Likelihood</td><td>38.1</td><td>6.0</td><td>40.8</td><td>32.7</td><td>33.7</td><td>35.7</td><td>47.6 33.5</td></tr><tr><td>Falcon-7B</td><td>Last token</td><td>23.1</td><td>27.0</td><td>20.1</td><td>8.5</td><td>18.7</td><td>18.3</td><td>40.8 22.4</td></tr><tr><td/><td>Mean token</td><td>18.8</td><td>18.0</td><td>25.9</td><td>18.5</td><td>25.8</td><td>27.5</td><td>37.3 24.5</td></tr><tr><td/><td>Ours</td><td>67.7</td><td>56.3</td><td>66.5</td><td>53.0</td><td>67.4</td><td>75.5</td><td>73.5 65.7</td></tr><tr><td/><td>Cond. Likelihood</td><td>44.3</td><td>20.8</td><td>51.8</td><td>38.6</td><td>56.0</td><td>50.9</td><td>56.7 45.6</td></tr><tr><td/><td>Joint Likelihood</td><td>36.7</td><td>1.1</td><td>35.0</td><td>27.7</td><td>33.0</td><td>32.4</td><td>48.0 30.6</td></tr><tr><td>LLaMA-13B</td><td>Last token</td><td>18.2</td><td>24.2</td><td>29.0</td><td>16.8</td><td>21.9</td><td>10.2</td><td>40.8 23.0</td></tr><tr><td/><td>Mean token</td><td>28.8</td><td>25.2</td><td>30.2</td><td>20.2</td><td>31.5</td><td>35.1</td><td>45.0 30.9</td></tr><tr><td/><td>Ours</td><td>70.6</td><td>52.5</td><td>65.9</td><td>53.2</td><td>67.8</td><td>74.1</td><td>73.0 65.3</td></tr><tr><td/><td>Cond. Likelihood</td><td>31.4</td><td>21.5</td><td>41.5</td><td>35.3</td><td>38.8</td><td>38.3</td><td>56.3 37.6</td></tr><tr><td/><td>Joint Likelihood</td><td>36.2</td><td>4.9</td><td>35.6</td><td>27.7</td><td>30.3</td><td>32.3</td><td>47.8 30.7</td></tr><tr><td>LLaMA-33B</td><td>Last token</td><td>21.8</td><td>20.1</td><td>13.2</td><td>9.4</td><td>22.5</td><td>11.5</td><td>40.8 19.9</td></tr><tr><td/><td>Mean token</td><td>27.9</td><td>24.0</td><td>29.6</td><td>21.7</td><td>35.4</td><td>34.6</td><td>43.5 31.0</td></tr><tr><td/><td>Ours</td><td>71.5</td><td>52.5</td><td>70.6</td><td>54.6</td><td>69.1</td><td>75.2</td><td>73.0 66.6</td></tr><tr><td>Vicuna-13B</td><td>Ours</td><td>70.2</td><td>53.4</td><td>62.4</td><td>52.0</td><td>68.5</td><td>73.9</td><td>75.3 65.1</td></tr><tr><td colspan=\"2\">StableVicuna-13B Ours</td><td>70.5</td><td>56.2</td><td>63.9</td><td>52.5</td><td>67.9</td><td>74.8</td><td>75.3 65.9</td></tr></table>", "num": null}, "TABREF8": {"text": "<PERSON> <PERSON> et al. (", "html": null, "type_str": "table", "content": "<table/>", "num": null}, "TABREF9": {"text": ".", "html": null, "type_str": "table", "content": "<table><tr><td>Method</td><td colspan=\"7\">STS-B STS12 STS13 STS14 STS15 STS16 SICK-R Avg</td></tr><tr><td>Contrastive-Trained Models</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>PromptBERT (Jiang et al., 2022)</td><td>81.6</td><td>71.6</td><td>84.6</td><td>77.0</td><td>84.5</td><td>80.6</td><td>69.9 78.5</td></tr><tr><td>PromptRoBERT (Jiang et al., 2022)</td><td>81.9</td><td>73.9</td><td>84.7</td><td>77.3</td><td>85.0</td><td>81.7</td><td>69.5 79.2</td></tr><tr><td>Autoregressive Models</td><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>PromptEOL (OPT-1.3B)  †</td><td>73.2</td><td>64.6</td><td>79.1</td><td>68.5</td><td>78.9</td><td>78.6</td><td>69.4 73.2</td></tr><tr><td>PromptEOL (OPT-13B)  †</td><td>70.7</td><td>60.2</td><td>81.4</td><td>67.0</td><td>75.5</td><td>79.6</td><td>66.0 71.9</td></tr><tr><td>PromptEOL (OPT-66B)  †</td><td>71.7</td><td>55.7</td><td>74.6</td><td>64.9</td><td>72.3</td><td>75.2</td><td>67.4 68.8</td></tr><tr><td>PromptEOL (LLaMA-13B)</td><td>63.4</td><td>52.3</td><td>75.3</td><td>64.0</td><td>70.5</td><td>73.2</td><td>60.5 65.6</td></tr><tr><td>Ours (LLaMA-13B)</td><td>70.6</td><td>52.5</td><td>65.9</td><td>53.2</td><td>67.8</td><td>74.1</td><td>73.0 65.3</td></tr><tr><td>Ours-Prompt-1 (LLaMA-13B)</td><td>72.2</td><td>61.6</td><td>68.4</td><td>66.9</td><td>72.7</td><td>75.6</td><td>76.3 70.5</td></tr><tr><td>Ours-Prompt-2 (LLaMA-13B)</td><td>81.5</td><td>67.9</td><td>79.9</td><td>75.3</td><td>82.9</td><td>82.3</td><td>74.6 77.8</td></tr><tr><td colspan=\"4\">E.1 PROMPTING FOR SEMANTIC TEXTUAL SIMILARITY</td><td/><td/><td/><td/></tr></table>", "num": null}}}}