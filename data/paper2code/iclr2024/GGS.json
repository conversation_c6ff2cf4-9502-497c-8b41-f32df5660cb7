{"paper_id": "GGS", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:49:21.182446Z"}, "title": "IMPROVING PROTEIN OPTIMIZATION WITH SMOOTHED FITNESS LANDSCAPES", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": "<EMAIL>"}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": "raman.sa<PERSON>@uochb.cas.cz"}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": "sha<PERSON><PERSON><PERSON>@mit.edu"}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Jaakkola", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Regina", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": "<EMAIL>"}, {"first": "Il<PERSON>", "middle": [], "last": "Fiete", "suffix": "", "affiliation": {}, "email": "<EMAIL>"}], "year": "", "venue": null, "identifiers": {}, "abstract": "The ability to engineer novel proteins with higher fitness for a desired property would be revolutionary for biotechnology and medicine. Modeling the combinatorially large space of sequences is infeasible; prior methods often constrain optimization to a small mutational radius, but this drastically limits the design space. Instead of heuristics, we propose smoothing the fitness landscape to facilitate protein optimization. First, we formulate protein fitness as a graph signal then use T<PERSON><PERSON><PERSON> regularization to smooth the fitness landscape. We find optimizing in this smoothed landscape leads to improved performance across multiple methods in the GFP and AAV benchmarks. Second, we achieve state-of-the-art results utilizing discrete energy-based models and MCMC in the smoothed landscape. Our method, called Gibbs sampling with Graph-based Smoothing (GGS), demonstrates a unique ability to achieve 2.5 fold fitness improvement (with in-silico evaluation) over its training set. GGS demonstrates potential to optimize proteins in the limited data regime. Code: https://github.com/kirjner/GGS", "pdf_parse": {"paper_id": "GGS", "_pdf_hash": "", "abstract": [{"text": "The ability to engineer novel proteins with higher fitness for a desired property would be revolutionary for biotechnology and medicine. Modeling the combinatorially large space of sequences is infeasible; prior methods often constrain optimization to a small mutational radius, but this drastically limits the design space. Instead of heuristics, we propose smoothing the fitness landscape to facilitate protein optimization. First, we formulate protein fitness as a graph signal then use T<PERSON><PERSON><PERSON> regularization to smooth the fitness landscape. We find optimizing in this smoothed landscape leads to improved performance across multiple methods in the GFP and AAV benchmarks. Second, we achieve state-of-the-art results utilizing discrete energy-based models and MCMC in the smoothed landscape. Our method, called Gibbs sampling with Graph-based Smoothing (GGS), demonstrates a unique ability to achieve 2.5 fold fitness improvement (with in-silico evaluation) over its training set. GGS demonstrates potential to optimize proteins in the limited data regime. Code: https://github.com/kirjner/GGS", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "In protein engineering, fitness can be defined as performance on a desired property or function. Examples of fitness include catalytic activity for enzymes (<PERSON> et al., 2021) and fluorescence for biomarkers (<PERSON><PERSON>, 2011) . Protein optimization seeks to improve protein fitness by altering the underlying sequences of amino acids. However, the number of possible proteins increases exponentially with sequence length, rendering it infeasible to perform brute-force search to engineer novel functions, which often require multiple mutations from the starting sequence (i.e. at least 3 (<PERSON><PERSON> & <PERSON>, 2019) ). Directed evolution (<PERSON>, 1998) has been successful in improving protein fitness, but it requires substantial labor and time. We aim to computationally generate high-fitness proteins by optimizing a learned model of the fitness landscape, but face several challenges. Proteins can be notorious for highly non-smooth fitness landscapes1 : fitness can change dramatically with single mutations, fitness measurements contain experimental noise, and most protein sequences have zero fitness (<PERSON><PERSON> et al., 2022) . Furthermore, protein fitness datasets are scarce and difficult to generate due to their high costs (<PERSON><PERSON><PERSON> et al., 2021) . As a result, machine learning (ML) methods are susceptible to predicting false positives and getting stuck in local optima (<PERSON> et al., 2019) . The 3D protein structure, if available, can provide information in navigating the noisy fitness landscape such as identifying hot spot residues (<PERSON><PERSON><PERSON> et al., 2012) , but high quality structures are not available in many cases.", "cite_spans": [{"start": 156, "end": 179, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF0"}, {"start": 212, "end": 229, "text": "(Remington, 2011)", "ref_id": "BIBREF24"}, {"start": 592, "end": 618, "text": "(<PERSON><PERSON><PERSON> & Weissman, 2019)", "ref_id": "BIBREF10"}, {"start": 641, "end": 655, "text": "(<PERSON>, 1998)", "ref_id": null}, {"start": 1111, "end": 1133, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF4"}, {"start": 1235, "end": 1257, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF7"}, {"start": 1383, "end": 1405, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF3"}, {"start": 1552, "end": 1572, "text": "(<PERSON><PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF35"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "One way to deal with noisy and limited data is to regularize the fitness landscape model2 . Our work considers a smoothing regularizer in which similar sequences (based on a distance measure) are predicted to have similar predicted fitness. While actual fitness lanscapes are not smooth, smoothing can be an important tool in the context of optimization, allowing gradient-based methods to reach higher peaks by avoiding local optima, especially in discrete optimization (<PERSON><PERSON>, 2020) . A few works have studied properties of protein fitness landscapes (Section 2), but none have directly applied smoothing with a graph framework during optimization.", "cite_spans": [{"start": 471, "end": 486, "text": "(<PERSON><PERSON>, 2020)", "ref_id": "BIBREF34"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We propose a novel method for applying smoothing to protein sequence and fitness data together with an optimization technique that takes advantage of the smoothing. First, we formulate sequences as a graph with fitness values as node attributes and apply <PERSON><PERSON><PERSON><PERSON> regularization to smooth the topological signal measured by the graph Laplacian. The smoothed data is then fitted with a neural network to be used as a model for discrete optimization (Figure 1 top). Second, we sample over the energy function for high fitness sequences by using the model's gradients in a Gibbs With Gradients (GWG) procedure (<PERSON><PERSON><PERSON> et al., 2021) . In GWG, a discrete distribution is constructed based on the model's gradients where mutations with improved fitness will correlate with higher probability. The process of taking gradients and sampling mutations is performed in an iterative fashion where subsequent mutations will guide towards higher fitness (Figure 1 ", "cite_spans": [{"start": 606, "end": 630, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF11"}], "ref_spans": [{"start": 455, "end": 456, "text": "1", "ref_id": "FIGREF0"}, {"start": 950, "end": 951, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Figure 1 shows an overview of the method. We refer to the procedure of smoothing then sampling as Gibbs sampling with Graph-based Smoothing (GGS). To evaluate our method, we introduce a set of tasks using the well studied Green Fluorescent Proteins (GFP) (<PERSON><PERSON><PERSON><PERSON> et al., 2016) and Adeno-Associated Virus (AAV) (<PERSON> et al., 2021) proteins. We chose GFP and AAV because of their real-world importance and availability of large mutational data. We design a set of tasks that emulate starting with noisy and limited data and evaluate with a trained model (as done in most prior works). We evaluate GGS and prior works on our proposed benchmarks to show that GGS is state-of-the-art in GFP and AAV fitness optimization. Our contributions are summarized as follows:", "cite_spans": [{"start": 255, "end": 279, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2016)", "ref_id": "BIBREF26"}, {"start": 313, "end": 334, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF5"}], "ref_spans": [{"start": 7, "end": 8, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "bottom).", "sec_num": null}, {"text": "• We develop a novel sequence-based protein optimization algorithm, GGS, which uses graph-based smoothing to train a smoothed fitness model. The model is used as a discrete energy function to progressively sample mutations towards higher-fitness sequences with GWG (Section 3). • We develop a set of tasks that measure a method's ability to extrapolate towards higher fitness.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "bottom).", "sec_num": null}, {"text": "We use publicly available GFP and AAV datasets to emulate difficult optimization scenarios of starting with limited and noisy data (Section 4.1). • Our benchmark shows prior methods fail to extrapolate towards higher fitness. However, we show graph-based smoothing can drastically improve their performance; in one baseline, the fitness jumps from 18% to 39% in GFP and 4% to 44% in AAV after smoothing (Section 4.2). • Our method GGS directly exploits smoothness to achieve state-of-the-art results with 5 times higher fitness in GFP and 2 times higher in AAV compared to the next best method (Section 4.2).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "bottom).", "sec_num": null}, {"text": "Protein fitness regularization. The NK model was an early attempt to model smoothness of protein fitness through a statistical model of epistasis (<PERSON><PERSON> & Weinberger, 1989) . <PERSON><PERSON> et al. (2022) proposed a framework to approximate the sparsity of protein fitness using a generalized NK model (<PERSON><PERSON><PERSON> & <PERSON>, 2013) . Concurrently, dWJS (<PERSON> et al., 2023) is most related to our work by utilizing Gaussian noise to regularize the discrete energy function during Langevin MCMC. dWJS trains by denoising to smooth a energy-based model whereas we apply discrete regularization using graph-based smoothing techniques.", "cite_spans": [{"start": 146, "end": 175, "text": "(<PERSON><PERSON><PERSON> & Weinberger, 1989)", "ref_id": "BIBREF15"}, {"start": 178, "end": 199, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF4"}, {"start": 297, "end": 319, "text": "(<PERSON><PERSON><PERSON> & Dinitz, 2013)", "ref_id": "BIBREF6"}, {"start": 341, "end": 360, "text": "(<PERSON> et al., 2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "Finally, we distinguish our smoothing method from traditional regularizers applied during training such as dropout (<PERSON><PERSON><PERSON><PERSON> et al., 2014) . Our goal is to smooth the fitness landscape in a way that is amenable for iterative optimization. We enforce similar sequences to have similar fitness which is not guaranteed with dropout or similar regularizers applied in minibatch training. Evaluating multiple smoothing strategies is not the focus of our work, but rather to demonstrate their importance.", "cite_spans": [{"start": 115, "end": 140, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2014)", "ref_id": "BIBREF29"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "The following describes our method. Section 3.1 details the problem formulation. Next section 3.2 describes the procedure for training a smoothed model. Lastly, section 3.3 provides background on Gibbs With Gradients (GWG) which is adapted for protein optimization. The full algorithm, Gibbs sampling with Graph-based Smoothing (GGS), is presented in Algorithm 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "METHOD", "sec_num": "3"}, {"text": "We denote the starting set of N proteins as D = (X, Y ) where X = {x 1 , . . . , x N } ⊂ V M are the sequences and Y = {y 1 , . . . , y N } are corresponding real-valued scalar fitness measurements. Each sequence x i ∈ V M is composed of M residues from a vocabulary V of 20 amino acids. Subscripts refer to different sequences. Note our method can be extended to other modalities, e.g. nucleic acids.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PROBLEM FORMULATION", "sec_num": "3.1"}, {"text": "For in-silico evaluation, we denote the set of all known sequences and fitness measurements as D * = (X * , Y * ). We assume there exists a unknown black-box function g : V M → R such that g(x * ) = y * . In practice, g needs to be approximated by a evaluator model, g ϕ , trained with weights ϕ to minimize prediction error on D * . g ϕ poses a limitation to evaluation since the true fitness needs to be verified with biological experiments. Nevertheless, an in-silico approximation provides a accessible way for evaluation and is done in all prior works. The starting dataset is a strict subset of the known dataset D ⊂ D * to simulate fitness optimization scenarios. Given D, our task is to generate a set of sequences with higher fitness than the starting set.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PROBLEM FORMULATION", "sec_num": "3.1"}, {"text": "Our goal is to develop a model of the sequence-to-fitness mapping that can be utilized when sampling higher fitness sequences. Unfortunately, the high-dimensional sequence space coupled with few data points and noisy labels can result in a noisy model that is prone to sampling false positives or getting stuck in local optima. To address this, we use smoothing techniques from graph signal processing.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GRAPH-BASED SMOOTHING ON PROTEINS", "sec_num": "3.2"}, {"text": "The smoothing process is depicted in Figure 2 . First, we train a noisy fitness model f θ : V M → R with weights θ on the initial dataset D using Mean-Squared Error (MSE). D is usually very small in real-world scenarios. We augment the dataset by using f θ to infer the fitness of neighboring sequences which we do not have labels for -known as transductive inference. Neighboring sequences are generated by randomly applying point mutations to each sequence in X. The augmented and original sequences become nodes, V , in our graph while their fitness labels are node attributes. Edges, E, are constructed with a k-nearest neighbor (kNN) graph around each node based on the Levenshtein distance3 . The graph construction algorithm can be found in Algorithm 4.", "cite_spans": [], "ref_spans": [{"start": 44, "end": 45, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "GRAPH-BASED SMOOTHING ON PROTEINS", "sec_num": "3.2"}, {"text": "The following borrows techniques from <PERSON><PERSON><PERSON> et al. (2022) . The smoothness of the fitness variability in our protein graph is defined as the sum over the square of all local variability,", "cite_spans": [{"start": 38, "end": 57, "text": "<PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF13"}], "ref_spans": [], "eq_spans": [], "section": "GRAPH-BASED SMOOTHING ON PROTEINS", "sec_num": "3.2"}, {"text": "TV 2 (Y ) = 1 2 i∈V (∆y i ) 2 , ∆y i = (i,j)∈E (y i -y j ) 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GRAPH-BASED SMOOTHING ON PROTEINS", "sec_num": "3.2"}, {"text": "TV refers to Total Variation and ∆y i is the local variability of node i that measures local changes in fitness. Using TV 2 as a regularizer, we solve the following optimization problem, known as <PERSON><PERSON><PERSON><PERSON> regularization (<PERSON> & <PERSON>, 2004) , for a new set of smoothed fitness labels, arg min", "cite_spans": [{"start": 220, "end": 244, "text": "(<PERSON> & <PERSON>, 2004)", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "GRAPH-BASED SMOOTHING ON PROTEINS", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Ŷ ∈R |V | ∥Y -Ŷ ∥ 2 2 + γ TV 2 ( Ŷ ).", "eq_num": "(1)"}], "section": "GRAPH-BASED SMOOTHING ON PROTEINS", "sec_num": "3.2"}, {"text": "With abuse of notation, we represent Y as a vector with each node's fitness. γ is a hyperparameter set to control the smoothness; too high can lead to underfitting. We experiment with different γ's in Section 4. Since eq. ( 1) is a quadratic convex problem, it has a closed form solution, Ŷ = (I+γL) -1 Y where L is the graph Laplacian and I is the identity matrix. The final step is to retrain the model on the sequences in the graph and their smoothed fitness labels. The result will be a model f θ with lower TV 2 than before and thus improved smoothness. The smoothing algorithm is in Algorithm 2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GRAPH-BASED SMOOTHING ON PROTEINS", "sec_num": "3.2"}, {"text": "Equipped with model f θ from section 3.2, we apply it in a procedure to sample mutations that improve the starting sequences' fitness. f θ can also be viewed as an energy-based model (EBM) that defines a Boltzmann distribution log p(x) = f θ (x) -log Z where Z is the normalization constant. Higher fitness sequences will be more likely under this distribution, while sampling will induce diversity and novelty. To sample from p(x), we use Gibbs With Gradients (GWG) <PERSON> et al. (2021) which has attracted significant interest due to its simplicity and state-of-the-art performance in discrete optimization. In this section, we describe the GWG procedure for protein sequences. GWG uses Gibbs sampling with approximations of locally informed proposals (<PERSON><PERSON>, 2020):", "cite_spans": [{"start": 467, "end": 490, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "q(x ′ |x) ∝ exp 1 2 i (x ′ i ) ⊤ d θ (x) i 1(x ′ ∈ H(x)), d θ (x) i = [∇ x f θ (x)] i -x i ⊙ [∇f θ (x)] i .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "(2) With slight abuse of notation, we use the one-hot sequence representation x ∈ {0, 1} M ×|V| where x i ∈ {0, 1} |V| represents the ith index of the sequence with 1 at its amino acid index and 0 elsewhere. ⊙ is the element wise product. H(x) = {y ∈ V M : d Hamming (x, y) ≤ 1} is the 1-ball around x using Hamming distance. The core idea of GWG is to use d θ (x) i as the first order approximation of a continuous gradient of the change in likelihood from mutating the ith index of x to a different amino acid. The quality of the proposals in eq. ( 2) rely on the smoothness of the energy f θ (Theorem 1 in <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2021) ). If the gradients, ∇f θ , are noisy, then the proposal distributions are ineffective in sampling better sequences. Hence, smoothing f θ is desirable (see section 4).", "cite_spans": [{"start": 609, "end": 632, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "The choice of H(•) as the 1-Hamming ball limits x ′ to point mutations from x and only requires O (M × |V|) compute to construct. Let the point mutation where x and x ′ differ be defined by the residue location, i loc ∈ {1, . . . , M }, and amino acid substitution, j sub ∈ {1, . . . , |V|}. By limiting x ′ to point mutants (i loc , j sub ), sampling q(x ′ |x) is equivalent to sampling the following,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "(i loc , j sub ) ∼ q(•|x) = Cat Softmax d θ (x) i,j τ M,|V| i=1,j=1", "eq_num": "(3)"}], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "where τ is the sampling temperature and d θ (x) i,j is the logits of mutating to (i, j). The proposal sequence x ′ is constructed by setting its i loc residue to j sub and equal to x elsewhere. Each proposed sequence is accepted or rejected using Metropolis-Hasting (MH),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "min exp(f θ (x ′ ) -f θ (x)) q(x|x ′ ) q(x ′ |x) , 1 .", "eq_num": "(4)"}], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "We provide the GWG algorithm in Algorithm 3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "Clustered sampling. GWG requires a starting sequence to start mutating. A reasonable starting set are the sequences X used to train the model. On each round r, we use eq. ( 3) to propose N prop mutations for each sequence. If accepted via eq. ( 4), then the mutated sequence will be added to the next round. However, this procedure can lead to an intractable number of sequences to consider.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "To control compute bandwidth, we perform hierarchical clustering (<PERSON><PERSON><PERSON><PERSON>, 2011 ) on all the sequences in a round and take the sequence of each cluster with the highest predicted fitness using f θ .", "cite_spans": [{"start": 65, "end": 79, "text": "(<PERSON><PERSON><PERSON><PERSON>, 2011", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "Let C be the number of clusters which we set based on amount of available compute. This procedure, known as Reduce, is,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Reduce(X; θ) = C c=1 {arg max x∈X c f θ (x)} where {X c } C c=1 = Cluster(X; C).", "eq_num": "(5)"}], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "Each round r reduces the sequences from the previous round and performs GWG sampling. Xr = Reduce(X r ; θ), X r+1 = GWG( Xr ; θ)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "To summarize, we adapted GWG for protein optimization by developing a smoothed model to satisfy GWG's smoothness assumptions and use clustering during sampling to reduce redundancy and compute. An illustration of clustered sampling is provided in Figure 5 .", "cite_spans": [], "ref_spans": [{"start": 254, "end": 255, "text": "5", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "The full algorithm for smoothing and clustered sampling is provided in Algorithm 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "Algorithm 1 GGS: Gibbs sampling with Graph-based Smoothing", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "Require:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "Starting dataset: D = (X, Y ) 1: θ ← arg max θ E (x,y)∼D (y -f θ (x)) 2 ▷ Initial training 2: θ ← Smooth(D; θ)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "▷ GS algorithm 2 3: for r = 0, . . . , R -1 do 4:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "Xr ← Reduce(X r ; θ) 5:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "X r+1 ← GWG( Xr ; θ)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "▷ GWG algorithm 3 6: end for 7: Return TopK(X R )", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "▷ Return Top-K best sequences based on predicted fitness f θ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SAMPLING IMPROVED FITNESS WITH GIBBS", "sec_num": "3.3"}, {"text": "Our experiments demonstrate the benefits of smoothing in protein optimization. Section 4.1 presents a set of challenging tasks based on the GFP and AAV proteins that emulate starting with experimental noise and a sparsely sampled fitness landscape. Section 4.2 evaluates the performance of baselines and our method, GGS, on our benchmark. In addition, we find applying smoothing improves performance for two baselines. Section 4.3 provides sweeps over hyperparameters and analysis of GGS.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "Baselines. We choose a representative set of prior works that evaluated on GFP and AAV: GFlowNets (GFN-AL) (<PERSON> et al., 2022) , model-based adaptive sampling (CbAS) (<PERSON><PERSON> et al., 2019) , greedy search (AdaLead) (Sinai et al., 2020) , bayesian optimization (BO-qei) (<PERSON> et al., 2017) , conservative model-based optimization (CoMs) (<PERSON><PERSON><PERSON><PERSON> et al., 2021) , and proximal exploration (PEX) (<PERSON> et al., 2022) . NOS (<PERSON><PERSON><PERSON> et al., 2023) performs protein optimization with diffusion models. However, their framework is tailored to antibody optimization and requires non-trivial modifications for general proteins. We were unable to evaluate Song & Li (2023) due to unrunnable public code.", "cite_spans": [{"start": 107, "end": 126, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF14"}, {"start": 166, "end": 188, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF3"}, {"start": 215, "end": 235, "text": "(Sinai et al., 2020)", "ref_id": "BIBREF27"}, {"start": 269, "end": 290, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF33"}, {"start": 338, "end": 361, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF31"}, {"start": 395, "end": 413, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF25"}, {"start": 420, "end": 441, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF12"}, {"start": 645, "end": 661, "text": "<PERSON> & Li (2023)", "ref_id": "BIBREF28"}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "GGS implementation. We use a 1D CNN (see Appendix B.1 for architecture and training) for model f θ . To ensure a fair comparison, we use the same model architecture in baselines when possible. In graph-based smoothing (GS), we augment the graph until it has N nodes = 250, 000 nodes. We found larger graphs to not give improvements. Similarly, we use τ = 0.1, R = 15 rounds and N prop = 100 proposals per round during GWG at which sequences would converge and more sampling did not give improvements. We choose the smoothing weight γ = 1.0 through grid search. We study sensitivity to hyperparameters, especially γ, in Section 4.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "We develop a set of tasks based on two well-studied protein systems: Green Fluoresent Protein (GFP) and Adeno-Associated Virus (AAV) (<PERSON><PERSON><PERSON><PERSON> et al., 2016; <PERSON> et al., 2021) . These were chosen due to their relatively large amount of measurements, 56,806 and 44,156 respectively, with sequence variability of up to 15 mutations from the wild-type. Other datasets are either too small or do not have enough sequence variability. GFP's fitness is its fluorescence properties as a biomarker while for AAV's is the ability to package a DNA payload, i.e. for gene delivery. We found GFP and AAV to suffice in demonstrating how prior methods fail to extrapolate.", "cite_spans": [{"start": 133, "end": 157, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2016;", "ref_id": "BIBREF26"}, {"start": 158, "end": 178, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "BENCHMARK", "sec_num": "4.1"}, {"text": "One measure of difficulty is the number of mutations required to achieve the highest known fitness; this assesses a method's exploration capability. We designate the set of optimal proteins, X 99th , as any sequence in the 99th fitness percentile in the entire dataset4 . Quantitatively, we compute the minimum number of mutations required from the training set to achieve the optimal fitness: Gap(X 0 ; X 99th ) = min({dist(x, x) :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BENCHMARK", "sec_num": "4.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "x ∈ X, x ∈ X 99th }).", "eq_num": "(6)"}], "section": "BENCHMARK", "sec_num": "4.1"}, {"text": "A high mutational gap would require the method discovering many mutations in a high dimensional space. A second measure of difficulty is the fitness range of the starting set of sequences. Starting with a low range of fitness requires the method to learn from barely functional proteins and exploit limited knowledge to find mutations that confer higher fitness. Appendix A shows Gap and starting rate are necessary as we found the previous GFP benchmark (<PERSON><PERSON><PERSON><PERSON> et al., 2022) as too \"easy\" by only requiring one mutation to achieve the optimal fitness.", "cite_spans": [{"start": 455, "end": 478, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "BENCHMARK", "sec_num": "4.1"}, {"text": "Recall the protein optimization task is to use the starting set D to propose a set of sequences with higher fitness. We design two difficulties, medium and hard, for GFP and AAV based on the properties of D. We restricted the range and the mutational gap to modulate task difficulty. We found Gap= 7 and Range < 30% to suffice in finding where our baseline methods fail to discover better proteins.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BENCHMARK", "sec_num": "4.1"}, {"text": "We use this setting as the hard difficulty and sought to develop GGS to solve it. In-silico evaluation. We follow prior works in using a trained evaluator model as a proxy for realworld experimental validation. A popular model choice is the TAPE transformer (<PERSON> et al., 2019) . However, we noticed a poor performance of the transformer compared to a simpler CNN that matches the findings of <PERSON><PERSON><PERSON> et al. (2021) . We use CNN architecture for the evaluator due to its superior performance. Following <PERSON> et al. (2022) , each method generates 128 samples X = {x i } 128 i=1 whose approximated fitness is predicted with the evaluator. We additionally report Diversity and Novelty that are also used in <PERSON> et al. (2022) . Descriptions of these metrics can be found in Appendix B.2 We emphasize that higher diversity and novelty are not equivalent to better performance, but provide insight into the exploration and exploitation trade-offs of different methods. For instance, a random algorithm would achieve maximum diversity and novelty.", "cite_spans": [{"start": 258, "end": 276, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF23"}, {"start": 392, "end": 413, "text": "<PERSON><PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF7"}, {"start": 501, "end": 519, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF14"}, {"start": 702, "end": 720, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF14"}], "ref_spans": [], "eq_spans": [], "section": "BENCHMARK", "sec_num": "4.1"}, {"text": "We run 5 seeds and report the average metric across all seeds including the standard deviation in parentheses. We evaluate GGS and previously described baselines. To ensure a fair comparison, we use the same CNN architecture as the model across all methods -all our baselines (and GGS) perform model-based optimization. Since graph-based smoothing (GS) is a general technique, we sought to evaluate its effectiveness in each of our baselines. To incorporate GS, we used the smoothed predictor as a replacement in each baseline which will be denoted with \"+ GS\". Table 3 summarizes GFP results while table 4 summarizes AAV.", "cite_spans": [], "ref_spans": [{"start": 568, "end": 569, "text": "3", "ref_id": "TABREF1"}], "eq_spans": [], "section": "RESULTS", "sec_num": "4.2"}, {"text": "GGS substantially outperforms all unsmoothed baselines, consistently achieving a improvement in fitness from the starting range of fitness in each difficulty. However, the smoothed baselines (lines with + GS) demonstrated a up to three fold improvement for CbAS, AdaLead. We find larger improvements in GFP where the sequence space is far larger than AAV -suggesting the GFP fitness landscape is harder to optimize over.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RESULTS", "sec_num": "4.2"}, {"text": "The most difficult task is clearly hard difficulty on GFP where all the baselines without smoothing cannot achieve fitness higher than the training set. With smoothing, GGS achieves the best fitness since the sampling procedure uses gradient-based proposals that benefit from a smooth model. Appendix C.2.1 presents results on additional difficulties to analyze GGS beyond hard.. We observe GGS is able to achieve the highest fitness while exhibiting respectable diversity and novelty. Notably, GGS's novelty falls within the range of the mutational gap in each difficulty, suggesting it is extrapolating an appropriate amount for each task. Our sampling procedure, GWG, fails to perform without smoothing which agrees with its theoretical requirements of requiring a smooth model for good performance. We conclude smoothing is a beneficial technique not only for GGS but also for some baselines. GGS is able to achieve state-of-the-art results in our benchmark. (0.1) 16.3 (1.6) 213 (2.7) 0.16 (0.2) 22.2 (0.8) 215 (4.6)  CbAS 0.14 (0.0) 9.7 (1.1) 7.2 (0.4) 0.18 (0.0) 9.6 (1.3) 7.8 (0.4) CbAS + GS 0.66 (0.1) 3.8 (0.4) 5.0 (0.0) 0.57 (0.0) 4.2 (0.17) 6.3 (0.6) AdaLead 0.56 (0.0) 3.5 (0.1) 2.0 (0.0) 0.18 (0.0) 5.6 (0.5) 2.8 (0.4) AdaLead + GS 0.59 (0.0) 5.5 (0. 25) 128 ( 84) 0.0 (0.1) 114 ( 36) 187 (5.7) PEX 0.47 (0.0) 3.0 (0.0) 1.4 (0.2) 0.0 (0.0) 3.0 (0.0 1.3 (0.3) PEX + GS 0.45 (0.0) 2.9 (0.0) 1.2 (0.3) 0.0 (0.0) 2.9 (0.0) 1.2 (0.3) GWG 0.1 (0.0) 33.0 (0.8) 12.8 (0.4) 0.0 (0.0) 4.2 (7.0) 7.6 (1.1) GGS (ours) 0.76 (0.0) 3.7 (0.2) 5.0 (0.0) 0.74 (0.0) 3.6 (0.1) 8.0 (0.0) 9.0 (1.1) 20.6 (0.5) 0.1 (0.1) 9.5 (2.5) 19.4 (1.1) CbAS 0.43 (0.0) 12.7 (0.7) 7.2 (0.4) 0.36 (0.0) 14.4 (0.7) 8.6 (0.5) CbAS + GS 0.47 (0.1) 8.8 (0.9) 5.3 (0.6) 0.4 (0.0) 12.5 (0.4) 7.0 (0.0) AdaLead 0.46 (0.0) 8.5 (0.8) 2.8 (0.4) 0.4 (0.0) 8.53 (0.1) 3.4 (0.5) AdaLead + GS 0.43 (0.0) 3.77 (0.2) 2.0 (0.0) 0.44 (0.0) 2.9 (0.1) 2.0 (0.0) BOqei 0.38 (0.0) 15.22 (0.8) 0.0 (0.0) 0.32 (0.0) 17.9 (0.3) 0.0 (0.0) BOqei + GS 0.34 (0.0) 12.2 (0.3) 0.0 (0.0) 0.32 (0.0) 17.2 (0.7) 0.0 (0.0) CoMS 0.37 (0.1) 10.1 (5.9) 8.2 (3.5) 0.26 (0.0) 10.7 (3.5) 10.0 (2.8) CoMS + GS 0.37 (0.1) 9.0 (3.6) 8.6 (3.7) 0.22 (0.1) 13.2 (1.9) 12.6 (2.4) PEX 0.4 (0.0) 2.8 (0.0)", "cite_spans": [], "ref_spans": [{"start": 963, "end": 1027, "text": "(0.1) 16.3 (1.6) 213 (2.7) 0.16 (0.2) 22.2 (0.8) 215 (4.6)  CbAS", "ref_id": "TABREF2"}], "eq_spans": [], "section": "RESULTS", "sec_num": "4.2"}, {"text": "1.4 (0.2) 0.3 (0.0) 2.8 (0.0) 1.3 (0.3) PEX + GS 0.4 (0.0) 2.8 (0.0) 1.4 (0.2) 0.3 (0.0) 2.8 (0.0) 1.1 (0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RESULTS", "sec_num": "4.2"}, {"text": "2) GWG 0.43 (0.1) 6.6 (6.3) 7.7 (0.8) 0.33 (0.0) 12.0 (0.4) 12.2 (0.4) GGS (ours) 0.51 (0.0) 4.0 (0.2) 5.4 (0.5) 0.60 (0.0) 4.5 (0.5) 7.0 (0.0)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RESULTS", "sec_num": "4.2"}, {"text": "We analyze the effect of varying the following hyperparameters: number of nodes N nodes in the protein graph, smoothness weight γ in eq. ( 1), and number of sampling rounds R during GWG sampling. For space, we leave the analysis of the sampling temperature τ in appendix C.1. Figure 3 presents the results of running GGS with different hyperparameters on the hard difficulty of GFP and AAV. Along the X-axis, we plot the median performance of the sequences during each round of GWG where r = 0 is initialization and r = 15 are the sequences and the end of GWG. The Y-axis shows the predicted fitness of the smoothed model in blue while the fitness scored with our is shown in red. Interestingly, we find in the majority of cases the smoothed model's predictions are highly correlated with the evaluator along the sampling trajectory. This is despite the model being trained on 4% of the data with the hard filtering. Appendix C.2.2 shows the prediction error where we find smoothing greatly improves in predicting the fitness of unseen sequences despite having higher train error.", "cite_spans": [], "ref_spans": [{"start": 283, "end": 284, "text": "3", "ref_id": null}], "eq_spans": [], "section": "ANALYSIS", "sec_num": "4.3"}, {"text": "Graph size. We find N nodes = 250, 000 nodes to have the best performance over a smaller graph with 100,000 nodes. Larger graphs allow for better approximation of the fitness landscape. However, larger graphs require more compute. A future direction could be to determine optimal graph size with different node augmentations strategies than random mutations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ANALYSIS", "sec_num": "4.3"}, {"text": "Smoothing. Too much smoothing γ = 10.0 can lead to worse performance in AAV while GFP is not sensitive. This suggests the optimal γ is dependent on the particular fitness landscape. Since real proteins landscapes are unknown, the biggest limitation of our method is determining the optimal γ. An important extension of GGS is to theoretically characterize landscapes (<PERSON><PERSON><PERSON> & <PERSON>, 2013) and provide guidelines of selecting γ.", "cite_spans": [{"start": 367, "end": 389, "text": "(<PERSON><PERSON><PERSON> & Dinitz, 2013)", "ref_id": "BIBREF6"}], "ref_spans": [], "eq_spans": [], "section": "ANALYSIS", "sec_num": "4.3"}, {"text": "Sampling convergence. We find a set number of rounds are required for GWG sampling to converge when the landscape is smooth enough (middle and right column). We find additional rounds are unnecessary; in practice, more rounds can be ran to ensure convergence. Results on sweeping the temperature are in Appendix C.1 where we see 0.1 clearly performs the best for GFP and AAV.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ANALYSIS", "sec_num": "4.3"}, {"text": "Figure 3 : GGS hyperparameter analysis on GFP and AAV hard difficulty. See Section 4.3.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "3", "ref_id": null}], "eq_spans": [], "section": "ANALYSIS", "sec_num": "4.3"}, {"text": "We present Gibbs sampling with Graph-based Smoothing (GGS) for protein optimization with a smoothed fitness landscape. Our main contribution and insight is a novel application of graph signal processing to protein optimization. We show smoothing is not only beneficial to our method but also to our baselines. To evaluate, we designed a suite of tasks around two measure of difficulty: number of edits to achieve the 99th percentile (mutational gap) and starting range of fitness. All baselines struggled to achieve good performance on our tasks. However, some baselines showed a three fold improvement with smoothing. GGS performed the best by combining <PERSON> with gradients with a smoothed model -demonstrating the synergy of gradient-based sampling with a smooth discrete energy-based model. Our results highlight the benefits of optimizing over a smooth landscape that may not be reflective of the true fitness landscape. We believe it's important to investigate how regularization can be used to transform protein fitness data to be compatible with modern optimization algorithms. Our goal is to not learn the excess biological noise, but find the signal in the data to discover the best protein. We conclude with limitations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISCUSSION", "sec_num": "5"}, {"text": "Evaluation limitations. The results demonstrate strong evidence of using smoothing given its improvement in multiple methods. Despite this, our evaluations follow prior works by utilizing an trained model for evaluation. This can be unreliable compared to testing out sequences with wet-lab validation. Unfortunately, wet-lab validation can be cost and time intensive. The ultimate test would be to use GGS in an active learning or experimental pipeline with wet-lab validation in the loop.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISCUSSION", "sec_num": "5"}, {"text": "Method limitations. Our method utilizes several hyperparameters such as the graph size and smoothing parameter γ. We demonstrated the effects of each hyperparameter in Section 4.3. Given the success of smoothing, it is desirable to find systematic ways to determine optimal hyperparameters based on an approximation of the underlying fitness landscape. We demonstrated our hyperparameter choices are not specific to either AAV or GFP, but this does not guarantee optimality for new landscapes. We believe the connections between spectral graph theory and protein optimization has more to give in advancing the important problem of protein optimization.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISCUSSION", "sec_num": "5"}, {"text": "A ADDITIONAL GFP ANALYSIS Design-bench difficulty. Prior works have used the GFP task introduced by design-bench (DB), a suite of model-based reinforcement learning tasks (<PERSON><PERSON><PERSON><PERSON> et al., 2022) , which samples a starting set of 5,000 sequences from the 50-60th percentile fitness range. However, we found this task to be too easy in the sense only one mutation was required from sequences in the training set to achieve the 99th percentile. We quantify this difficulty using the mutational gap described in eq. ( 6). Our proposed medium and hard difficulties (Section 4.1) require many more mutations to reach the top fitness percentile, see Figure 4 . Similar issues may be present in other benchmarks. B ADDITIONAL METHODS", "cite_spans": [{"start": 171, "end": 194, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF32"}], "ref_spans": [{"start": 650, "end": 651, "text": "4", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "DISCUSSION", "sec_num": "5"}, {"text": "We utilize a 1D convolutional neural network (CNN) architecture in our model and oracle. The CNN takes in a one-hot encoded sequence as input then applies a 1D convolution with kernel width 5 followed by max-pooling and a dense layer to a single node that outputs a scalar value. It uses 256 channels throughout for a total of 157,000 parameters. Despite its simplicity, we find the CNN to outperform Transformers. Indeed, this corroborates the results in <PERSON><PERSON><PERSON> et al. (2021) that a simple CNN can be effective in low data regimes.", "cite_spans": [{"start": 456, "end": 477, "text": "<PERSON><PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF7"}], "ref_spans": [], "eq_spans": [], "section": "B.1 CNN ARCHITECTURE", "sec_num": null}, {"text": "Training is performed with batch size 1024, ADAM optimizer (Kingma & Ba, 2014) (with β 1 = 0.9, β 2 = 0.999), learning rate 0.0001, and 50 epochs, using a single A6000 Nvidia GPU.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 CNN ARCHITECTURE", "sec_num": null}, {"text": "We provide mathematical definitions of each metric. Note g ϕ is the evaluator trained to predict the approximate fitness as a proxy for experimental validation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 METRICS", "sec_num": null}, {"text": "• x ′ ← x 5:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 METRICS", "sec_num": null}, {"text": "(i loc , j sub ) ∼ q(•|x) ▷ Sample index and token eq. (3) 6:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 METRICS", "sec_num": null}, {"text": "x ′ i loc ← V j sub ▷ Apply mutation 7:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 METRICS", "sec_num": null}, {"text": "if accept using eq. ( 4) then 8:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 METRICS", "sec_num": null}, {"text": "X ′ ← X ′ ∪ {x ′ } 9:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 METRICS", "sec_num": null}, {"text": "end if 10:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 METRICS", "sec_num": null}, {"text": "end for 11: end for 12: Return X ′ ▷ Return accepted sequences.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.2 METRICS", "sec_num": null}, {"text": "Require: Sequences: X 1: V ← X ▷ Construct nodes. 2: while |V | ≤ N nodes do 3:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 4 CreateGraph", "sec_num": null}, {"text": "x ∼ U(V ) 4:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 4 CreateGraph", "sec_num": null}, {"text": "x ′ ← PointMutation(x)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 4 CreateGraph", "sec_num": null}, {"text": "▷ Sample a point mutation uniformly at random. 5: end while 6: E ← x∈V kNN(x; V ) ▷ Construct edges (Algorithm 5). ) is ran to generate many sample sequences, V r+1 . To control computation, we hierarchically cluster all sampled sequences based on <PERSON><PERSON>htein distance and take the top fitness sequence in each cluster, using our trained fitness prediction model f θ to score each sequence -we refer to this subroutine as Reduce (eq. ( 5)). The top sequences, Ṽr+1 are used for the next round.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 4 CreateGraph", "sec_num": null}, {"text": "We determine the effect of different tmperatures γ when running GGS on the hard difficulty for GFP and AAV. All other hyperparameters follow those used in the main results, see Section 4.2. Table 5 shows the results where clearly γ = 0.1 performs the best for both AAV and GFP.", "cite_spans": [], "ref_spans": [{"start": 196, "end": 197, "text": "5", "ref_id": null}], "eq_spans": [], "section": "C.1 SAMPLING TEMPERATURE SWEEP", "sec_num": null}, {"text": "Finally, we note that in every case except two, smoothing dramatically increases acceptance rate of the GWG sampling procedure, which aligns with the inversely proportional relationship between smoothness of the energy function and sampling efficiency. In the case of the hardest GFP task, even the the smoothed model had overfit to the training set. As for the GFP medium task, we suspect that this particular section of the experimental dataset allowed the unsmoothed model to learn a smooth landscape initially.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.1 SAMPLING TEMPERATURE SWEEP", "sec_num": null}, {"text": "Landscape refers to the sequence to fitness mapping.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "In the sequel, we will use \"model\" when referring to the fitness landscape model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Defined as the minimum number of mutations between two sequences.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "This may differ from the true optimal protein found in nature. Unfortunately, we must work with existing datasets since every possible protein cannot be experimentally measured.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "The authors thank <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> for helpful discussion and feedback. JY was supported in part by an NSF-GRFP. JY, RB, and TJ acknowledge support from NSF Expeditions grant (award 1918839: Collaborative Research: Understanding the World Through Code), Machine Learning for Pharmaceutical Discovery and Synthesis (MLPDS) consortium, the Abdul Latif Jameel Clinic for Machine Learning in Health, the DTRA Discovery of Medical Countermeasures Against New and Emerging (DOMANE) threats program, the DARPA Accelerated Molecular Discovery program and the Sanofi Computational Antibody Design grant. IF is supported by the Office of Naval Research, the Howard Hughes Medical Institute (HHMI), and NIH (NIMH-MH129046). RS was partly supported by the European Regional Development Fund under the project IMPACT (reg. no. CZ.02.1.01/0.0/0.0/15 003/0000468), the Ministry of Education, Youth and Sports of the Czech Republic through the e-INFRA CZ (ID:90254), and the MISTI Global Seed Funds under the MIT-Czech Republic Seed Fund.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACKNOWLEDGMENTS", "sec_num": null}, {"text": ".4 (0.5) 0.45 (0.0) 15.2 (1.1) 9.0 (0.0) 0.1 0.74 (0.0) 3.6 (0.1) 8.0 (0.0) 0.6 (0.0) 4.5 (0.2) 7.0 (0.0) 1.0 0.0 (0.1) 28.2 (0.8) 11.4 (0.5) 0.45 (0.0) 11.9 (0.5) 8.0 (0.0) 2.0 0.0 (0.1) 36.1 (1.0) 13.0 (0.0) 0.33 (0.0) 16.7 (0.9) 8.5 (0.5)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "In this section, we provide further analyses into the effect of smoothing on performance of GGS, extrapolation to unseen data, and acceptance rate of the GWG sampling procedure. Throughout, we use the same parameters τ = 0.1, γ = 1, r = 15, N nodes = 250, 000 as in the main text.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 SMOOTHING ANALYSIS", "sec_num": null}, {"text": "We first define additional benchmarks, one easier, and three harder, for each protein dataset. We note that the \"easy\" GFP task is equivalent to the design-bench baseline that is sometimes used as a benchmark in protein engineering tasks. Due to experimental noise, protein variants are assayed multiple times, and can be assigned multiple fitness values, which means the fitness values of one sequence may occupy a large percentile range. In the case of this task, multiple measurements of the wildtype GFP fitness are found in the 50th-60th percentile range. Because WT GFP is also a \"top sequence,\" this task necessarily has a mutational gap of 0. Due to this leakage, we develop our own benchmarks in the main text, and extend those to AAV.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2.1 ADDITIONAL BENCHMARKS", "sec_num": null}, {"text": "The following two tables show how a smoothed model outperforms its unsmoothed counterpart according to our evaluator across all GFP/AAV benchmarks except AAV Harder2 (see ( * )), and GFP Harder3, where the smoothing was not sufficient to induce successful GWG sampling (see Table 10 ). For the GFP task, our model fails (achieves 0 median fitness) when we restrict the data to the 10th percentile and mutation gap 8 for GFP where |D| = 397. For AAV, we find the model is able to still find signal and achieve 0.384 evaluated fitness despite the data being limited to the 10th percentile and mutation gap of 13 where |D| = 476. It is notable, though, that the performance improvements gained from smoothing are smaller than in the case of GFP. Presumably, this is due to the vastly reduced dimension of the AAV sequence space in comparison to that of GFP, which may result in a neural network to learn a smoother landscape without any regularization.", "cite_spans": [], "ref_spans": [{"start": 280, "end": 282, "text": "10", "ref_id": null}], "eq_spans": [], "section": "C.2.2 HOW SMOOTHING AFFECTS PERFORMANCE", "sec_num": null}, {"text": "The following tables show the benefits of smoothing on extrapolation to held out ground truth experimental data, up to a certain difficulty benchmark, as well as how smoothing vastly improves the acceptance rate for the GWG sampling procedure. For each benchmark category, we evaluated the impact of smoothing on extrapolation abilities by analyzing the Mean Absolute Error (MAE) of the models on that benchmark's training and holdout datasets from the experimental ground truth. The effectiveness of smoothing was indicated by reduced MAE values on the holdout set. We also find that the MAE on the training set is lower for the unsmoothed models, as expected. In line with the results of the previous section, the effect of smoothing is reduced for AAV. As task difficulty increases, for both proteins, the effectiveness of smoothing on extrapolation decreases, which we expect as any signal leading from the training set to the fitter sequences gets obscured as training set size decreases.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2.3 HOW SMOOTHING AFFECTS EXTRAPOLATION + SAMPLING", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "The adaptive landscape of a metallo-enzyme is shaped by environment-dependent epistasis", "authors": [{"first": "<PERSON>", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Gloria", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature Communications", "volume": "12", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. The adaptive landscape of a metallo-enzyme is shaped by environment-dependent epistasis. Nature Communications, 12(1): 3867, 2021.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Model-based reinforcement learning for biological sequence design", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Model-based reinforcement learning for biological sequence design. 2020.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Conditioning by adaptive sampling for robust design", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Hahnbeom", "middle": [], "last": "Park", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Listgarten", "suffix": ""}], "year": 2019, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "773--782", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Conditioning by adaptive sampling for robust design. In International conference on machine learning, pp. 773-782. PMLR, 2019.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "On the sparsity of fitness functions and implications for learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>azade<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Listgarten", "suffix": ""}], "year": 2022, "venue": "Proceedings of the National Academy of Sciences", "volume": "119", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. On the sparsity of fitness functions and implications for learning. Proceedings of the National Academy of Sciences, 119(1):e2109649118, 2022.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Deep diversification of an aav capsid protein by machine learning", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "Bashir", "suffix": ""}, {"first": "<PERSON>", "middle": ["K"], "last": "Sinai", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["F"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Church", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Nature Biotechnology", "volume": "39", "issue": "6", "pages": "691--696", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Deep diversification of an aav capsid protein by machine learning. Nature Biotechnology, 39(6):691-696, 2021.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "An analysis of nk landscapes: Interaction structure, statistical properties, and expected number of local optima", "authors": [{"first": "<PERSON>", "middle": [], "last": "Buzas", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "IEEE Transactions on Evolutionary Computation", "volume": "18", "issue": "6", "pages": "807--818", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. An analysis of nk landscapes: Interaction structure, statistical properties, and expected number of local optima. IEEE Transactions on Evolutionary Computation, 18(6):807-818, 2013.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Flip: Benchmark tasks in fitness landscape inference for proteins", "authors": [{"first": "<PERSON>", "middle": [], "last": "Dallag<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Kadina", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["K"], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "bioRxiv", "volume": "", "issue": "", "pages": "2021--2032", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Flip: Benchmark tasks in fitness landscape inference for proteins. bioRxiv, pp. 2021-11, 2021.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Plug & play directed evolution of proteins with gradient-based discrete mcmc", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Law", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Biagioni", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "St", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2023, "venue": "Machine Learning: Science and Technology", "volume": "4", "issue": "2", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Plug & play directed evolution of proteins with gradient-based discrete mcmc. Machine Learning: Science and Technology, 4(2):025014, 2023.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Protein discovery with discrete walk-jump sampling", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Berenberg", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Zadorozhny", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Klein<PERSON>z", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Lafrance-<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Yan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ra", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Cho", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.12360"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. Protein discovery with discrete walk-jump sampling. arXiv preprint arXiv:2306.12360, 2023.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "The expected time to cross extended fitness plateaus. Theoretical Population Biology", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "129", "issue": "", "pages": "54--67", "other_ids": {"DOI": ["10.1016/j.tpb"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. The expected time to cross extended fitness plateaus. Theo- retical Population Biology, 129:54-67, 2019. ISSN 0040-5809. doi: https://doi.org/10.1016/j.tpb. 2019.03.008. URL https://www.sciencedirect.com/science/article/pii/ S0040580918301011. Special issue in honor of <PERSON>'s 75th birthday.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Oops i took a gradient: Scalable sampling for discrete distributions", "authors": [{"first": "Will", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Swersky", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Maddison", "suffix": ""}], "year": 2021, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "3831--3841", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Oops i took a gradient: Scalable sampling for discrete distributions. In International Conference on Machine Learning, pp. 3831-3841. PMLR, 2021.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Protein design with guided discrete diffusion", "authors": [{"first": "Nate", "middle": [], "last": "Gruver", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Gj"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Lafrance-<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Cho", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.20009"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Protein design with guided discrete diffusion. arXiv preprint arXiv:2305.20009, 2023.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Graph filters for signal processing and machine learning on graphs", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gama", "suffix": ""}, {"first": "<PERSON>", "middle": ["I"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Santiago", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.08854"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Graph filters for signal processing and machine learning on graphs. arXiv preprint arXiv:2211.08854, 2022.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Biological sequence design with GFlowNets", "authors": [{"first": "Moksh", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "F", "middle": ["P"], "last": "Bonaventure", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dinghuai", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Payel", "middle": [], "last": "Simine", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the 39th International Conference on Machine Learning", "volume": "162", "issue": "", "pages": "17--23", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Biological sequence design with GFlowNets. In <PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> (eds.), Proceedings of the 39th International Conference on Machine Learning, volume 162 of Proceedings of Machine Learning Research, pp. 9786-9801. PMLR, 17-23 Jul 2022. URL https://proceedings.mlr.press/v162/jain22a.html.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "The nk model of rugged fitness landscapes and its application to maturation of the immune response", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1989, "venue": "Journal of theoretical biology", "volume": "141", "issue": "2", "pages": "211--245", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. The nk model of rugged fitness landscapes and its application to maturation of the immune response. Journal of theoretical biology, 141(2):211-245, 1989.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Adam: A method for stochastic optimization", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Ba", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1412.6980"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>: A method for stochastic optimization. arXiv preprint arXiv:1412.6980, 2014.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Protein sequence design in a latent space via model-based reinforcement learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Hyunkyu", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ro", "suffix": ""}, {"first": "", "middle": [], "last": "Cha", "suffix": ""}, {"first": "Min", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Protein sequence design in a latent space via model-based reinforcement learning.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Local latent space bayesian optimization over structured inputs", "authors": [{"first": "Natalie", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Haydn", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "34505--34518", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Local latent space bayesian optimization over structured inputs. Advances in Neural Information Processing Systems, 35:34505-34518, 2022.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Language models enable zero-shot prediction of the effects of mutations on protein function", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Verkuil", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Rives", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "29287--29303", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Language models enable zero-shot prediction of the effects of mutations on protein function. Advances in Neural Information Processing Systems, 34:29287-29303, 2021.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Modern hierarchical, agglomerative clustering algorithms", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2011, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1109.2378"]}, "num": null, "urls": [], "raw_text": "<PERSON>. Modern hierarchical, agglomerative clustering algorithms. arXiv preprint arXiv:1109.2378, 2011.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Tranception: protein fitness prediction with autoregressive transformers and inference-time retrieval", "authors": [{"first": "<PERSON>", "middle": [], "last": "Notin", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON><PERSON>"], "last": "Hurtado", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Marks", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gal", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "16990--17017", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Tranception: protein fitness prediction with autoregressive transformers and inference-time retrieval. In International Conference on Machine Learning, pp. 16990-17017. PMLR, 2022.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Extrapolative controlled sequence generation via iterative refinement", "authors": [{"first": "Vishakh", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Yuanzhe"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "He", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.04562"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Extrapolative controlled sequence generation via iterative refinement. arXiv preprint arXiv:2303.04562, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Evaluating protein transfer learning with tape", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Yun", "middle": ["S"], "last": "Song", "suffix": ""}], "year": 2019, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Evaluating protein transfer learning with tape. In Advances in Neural Information Processing Systems, 2019.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Green fluorescent protein: a perspective", "authors": [{"first": "Remington", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2011, "venue": "Protein Science", "volume": "20", "issue": "9", "pages": "1509--1519", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> <PERSON>. Green fluorescent protein: a perspective. Protein Science, 20(9):1509-1519, 2011.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Proximal exploration for model-guided protein sequence design", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Zhizhou Ren", "suffix": ""}, {"first": "Fan", "middle": [], "last": "Li", "suffix": ""}, {"first": "Yuan", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the 39th International Conference on Machine Learning", "volume": "162", "issue": "", "pages": "17--23", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Proximal exploration for model-guided protein sequence design. In <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON> (eds.), Proceedings of the 39th International Conference on Machine Learning, volume 162 of Proceedings of Machine Learning Research, pp. 18520-18536. PMLR, 17-23 Jul 2022. URL https://proceedings.mlr.press/v162/ ren22a.html.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Local fitness landscape of the green fluorescent protein", "authors": [{"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["V"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["V"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "Dmitry", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Soylemez", "suffix": ""}], "year": 2016, "venue": "Nature", "volume": "533", "issue": "7603", "pages": "397--401", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, et al. Local fitness landscape of the green fluorescent protein. Nature, 533(7603):397-401, 2016.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Adalead: A simple and robust adaptive greedy search algorithm for sequence design", "authors": [{"first": "Sam", "middle": [], "last": "Sinai", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Locane", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.02141"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Adalead: A simple and robust adaptive greedy search algorithm for sequence design. arXiv preprint arXiv:2010.02141, 2020.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Importance weighted expectation-maximization for protein sequence design", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.00386"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>. Importance weighted expectation-maximization for protein sequence design. arXiv preprint arXiv:2305.00386, 2023.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Dropout: a simple way to prevent neural networks from overfitting", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "The journal of machine learning research", "volume": "15", "issue": "1", "pages": "1929--1958", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Dropout: a simple way to prevent neural networks from overfitting. The journal of machine learning research, 15(1):1929-1958, 2014.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Accelerating bayesian optimization for biological sequence design with denoising autoencoders", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Nate", "middle": [], "last": "Gruver", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Greenside", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2203.12742"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Accelerating bayesian optimization for biological sequence design with denoising autoencoders. arXiv preprint arXiv:2203.12742, 2022.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Conservative objective models for effective offline model-based optimization", "authors": [{"first": "<PERSON>", "middle": [], "last": "Trabucco", "suffix": ""}, {"first": "Aviral", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xinyang", "middle": [], "last": "Geng", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the 38th International Conference on Machine Learning", "volume": "139", "issue": "", "pages": "18--24", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Conservative objective models for effective offline model-based optimization. In <PERSON> and <PERSON> (eds.), Proceedings of the 38th International Conference on Machine Learning, volume 139 of Proceedings of Machine Learning Research, pp. 10358-10368. PMLR, 18-24 Jul 2021. URL https://proceedings. mlr.press/v139/trabucco21a.html.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Design-bench: Benchmarks for data-driven offline model-based optimization", "authors": [{"first": "<PERSON>", "middle": [], "last": "Trabucco", "suffix": ""}, {"first": "Xinyang", "middle": [], "last": "Geng", "suffix": ""}, {"first": "Aviral", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Design-bench: Benchmarks for data-driven offline model-based optimization. CoRR, abs/2202.08450, 2022. URL https: //arxiv.org/abs/2202.08450.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "The reparameterization trick for acquisition functions", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1712.00424"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. The reparameterization trick for acquisition functions. arXiv preprint arXiv:1712.00424, 2017.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Informed proposals for local mcmc in discrete spaces", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Journal of the American Statistical Association", "volume": "115", "issue": "530", "pages": "852--865", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Informed proposals for local mcmc in discrete spaces. Journal of the American Statistical Association, 115(530):852-865, 2020.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Relationship between hot spot residues and ligand binding hot spots in protein-protein interfaces", "authors": [{"first": "<PERSON>", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hall", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Vajda", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "Journal of chemical information and modeling", "volume": "52", "issue": "8", "pages": "2236--2244", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Relationship between hot spot residues and ligand binding hot spots in protein-protein interfaces. Journal of chemical information and modeling, 52(8):2236-2244, 2012.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "A regularization framework for learning from graph data", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2004, "venue": "ICML 2004 Workshop on Statistical Relational Learning and Its Connections to Other Fields (SRL 2004)", "volume": "", "issue": "", "pages": "132--137", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. A regularization framework for learning from graph data. In ICML 2004 Workshop on Statistical Relational Learning and Its Connections to Other Fields (SRL 2004), pp. 132-137, 2004.", "links": null}}, "ref_entries": {"FIGREF0": {"uris": null, "fig_num": "1", "text": "Figure 1: Overview. (A) Protein optimization is challenging due to a noisy fitness landscape where the starting dataset (unblurred) is a fraction of the landscape with the highest fitness sequences hidden (blurred). (B) We develop Graph-based Smoothing (GS) to estimate a smoothed fitness landscape from the starting data. (C) A model is trained on the smoothed fitness landscape to infer the rest of the landscape. (D) Gradients from the model are used in Gibbs With Gradients (GWG) where on each step a new mutation is proposed. (E) The goal of sampling is for each trajectory to gradually head towards higher fitness.", "num": null, "type_str": "figure"}, "FIGREF1": {"uris": null, "fig_num": "2", "text": "Figure 2: Steps in graph-based smoothing on proteins illustrated with a fictitious data of length 2 sequences with vocabulary {A, B}. Above each node are corresponding fitness values. Solid nodes are those in our training set while dashed nodes are augmented via point mutations to increase the smoothing effectiveness. See section 3.2 for description of each step.", "num": null, "type_str": "figure"}, "FIGREF3": {"uris": null, "fig_num": "4", "text": "Figure 4: Easy is taken from design-bench where sequences between the 50-60th percentile are used in training regardless of edit distance to sequences in the 99th percentile. Data leakage is present due to multiple measurements that allows the wild-type and other top sequences to be included during training. Medium filters the training dataset to have sequences in the 20-40th percentile and be 6 or more mutations away from anything in the top 99th percentile. Hard similarly filters for sequences in at most the 30th percentile and 7 or more mutations away.", "num": null, "type_str": "figure"}, "FIGREF4": {"uris": null, "fig_num": null, "text": "Normalized) Fitness = median({ξ(x i ; Y * )} Nsamples i=1 ) where ξ(x; Y * ) = g ϕ (xi)-min(Y * ) max(Y * )-min(Y * ) is the min-max normalized fitness based on the lowest and highest known fitness in Y * .• Diversity = median({dist(x, x) : x, x ∈ X, x ̸ = x}) is the average sample similarity.• Novelty = median({η(x i ; X)} Nsamples i=1 ) where η(x; X) = min({dist(x, x) : x ∈ X * , x ̸ = x})is the minimum distance of sample x to any of the starting sequences X.Algorithm 2 Smooth: Graph-based Smoothing Require: Sequences: X Require: Noisy model weights:θ 1: V, E ← CreateGraph(X) ▷ Construct graph (Algorithm 4). 2: L ← GraphLaplacian(V, E) ▷ Compute graph Laplacian. 3: Y ← [f θ (x 1 ), . . . , f θ (x Nnodes )] ⊤ 4: Ŷ ← (I + γL) -1 Y ▷ Compute smoothed fitness labels. 5: θ ← arg max θ E (x,ŷ)∼(V, Ŷ ) (ŷ -f θ (x)) 2▷ Train on smoothed dataset. 6: Return θ Algorithm 3 GWG: Gibbs With Gradients Require: Parent sequences: X Require: Model weights: θ 1: X ′ ← ∅ 2: for x ∈ X do 3:for i = 1, . . . , N prop do ▷ Number of proposals per sequence.4:", "num": null, "type_str": "figure"}, "FIGREF5": {"uris": null, "fig_num": "5", "text": "Figure5: Illustration of clustered sampling. Ṽr is the starting set of sequences for sampling in round r. GWG (Algorithm 3) is ran to generate many sample sequences, V r+1 . To control computation, we hierarchically cluster all sampled sequences based on Levenshtein distance and take the top fitness sequence in each cluster, using our trained fitness prediction model f θ to score each sequence -we refer to this subroutine as Reduce (eq. (5)). The top sequences, Ṽr+1 are used for the next round.", "num": null, "type_str": "figure"}, "TABREF0": {"content": "<table><tr><td/><td colspan=\"2\">: GFP tasks</td><td/><td/><td colspan=\"2\">Table 2: AAV tasks</td><td/></tr><tr><td colspan=\"3\">Difficulty Range (%) Gap</td><td>|D|</td><td colspan=\"3\">Difficulty Range (%) Gap</td><td>|D|</td></tr><tr><td>Medium</td><td>20th-40th</td><td>6</td><td>2828</td><td>Medium</td><td>20th-40th</td><td>6</td><td>2139</td></tr><tr><td>Hard</td><td>&lt; 30th</td><td>7</td><td>2426</td><td>Hard</td><td>&lt; 30th</td><td>7</td><td>3448</td></tr></table>", "text": "", "num": null, "html": null, "type_str": "table"}, "TABREF1": {"content": "<table><tr><td/><td/><td colspan=\"2\">Medium difficulty</td><td/><td>Hard difficulty</td><td/></tr><tr><td>Method</td><td>Fitness</td><td>Diversity</td><td>Novelty</td><td>Fitness</td><td>Diversity</td><td>Novelty</td></tr><tr><td>GFN-AL</td><td colspan=\"3\">0.09 (0.1) 25.1 (0.5) 213 (2.2)</td><td colspan=\"3\">0.1 (0.2) 23.6 (1.0) 214 (4.2)</td></tr><tr><td colspan=\"2\">GFN-AL + GS 0.15</td><td/><td/><td/><td/><td/></tr></table>", "text": "GFP optimization results. Bold indicates improvement with smoothing.", "num": null, "html": null, "type_str": "table"}, "TABREF2": {"content": "<table><tr><td/><td colspan=\"3\">Medium difficulty</td><td/><td>Hard difficulty</td><td/></tr><tr><td>Method</td><td>Fitness</td><td>Diversity</td><td>Novelty</td><td>Fitness</td><td>Diversity</td><td>Novelty</td></tr><tr><td>GFN-AL</td><td>0.2 (0.1)</td><td>9.6 (1.2)</td><td colspan=\"4\">19.4 (1.1) 0.1 (0.1) 11.6 (1.4) 19.6 (1.1)</td></tr><tr><td colspan=\"2\">GFN-AL + GS 0.18 (0.1)</td><td/><td/><td/><td/><td/></tr></table>", "text": "AAV optimization results. Bold indicates improvement with smoothing.", "num": null, "html": null, "type_str": "table"}}}}