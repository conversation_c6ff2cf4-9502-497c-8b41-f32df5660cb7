{"paper_id": "SEABO", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:57:15.361430Z"}, "title": "SEABO: A SIMPLE SEARCH-BASED METHOD FOR OFFLINE IMITATION LEARNING", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "Ma", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "", "middle": [], "last": "Le Wan", "suffix": "", "affiliation": {"laboratory": "IEG", "institution": "", "location": {"settlement": "Tencent"}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": "<EMAIL>"}, {"first": "Zongqing", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Peking University", "location": {}}, "email": "<EMAIL>"}], "year": "", "venue": null, "identifiers": {}, "abstract": "Offline reinforcement learning (RL) has attracted much attention due to its ability in learning from static offline datasets and eliminating the need of interacting with the environment. Nevertheless, the success of offline RL relies heavily on the offline transitions annotated with reward labels. In practice, we often need to hand-craft the reward function, which is sometimes difficult, labor-intensive, or inefficient. To tackle this challenge, we set our focus on the offline imitation learning (IL) setting, and aim at getting a reward function based on the expert data and unlabeled data. To that end, we propose a simple yet effective search-based offline IL method, tagged SEABO. SEABO allocates a larger reward to the transition that is close to its closest neighbor in the expert demonstration, and a smaller reward otherwise, all in an unsupervised learning manner. Experimental results on a variety of D4RL datasets indicate that SEABO can achieve competitive performance to offline RL algorithms with ground-truth rewards, given only a single expert trajectory, and can outperform prior reward learning and offline IL methods across many tasks. Moreover, we demonstrate that SEABO also works well if the expert demonstrations contain only observations. Our code is publicly available at https://github.com/dmksjfl/SEABO.", "pdf_parse": {"paper_id": "SEABO", "_pdf_hash": "", "abstract": [{"text": "Offline reinforcement learning (RL) has attracted much attention due to its ability in learning from static offline datasets and eliminating the need of interacting with the environment. Nevertheless, the success of offline RL relies heavily on the offline transitions annotated with reward labels. In practice, we often need to hand-craft the reward function, which is sometimes difficult, labor-intensive, or inefficient. To tackle this challenge, we set our focus on the offline imitation learning (IL) setting, and aim at getting a reward function based on the expert data and unlabeled data. To that end, we propose a simple yet effective search-based offline IL method, tagged SEABO. SEABO allocates a larger reward to the transition that is close to its closest neighbor in the expert demonstration, and a smaller reward otherwise, all in an unsupervised learning manner. Experimental results on a variety of D4RL datasets indicate that SEABO can achieve competitive performance to offline RL algorithms with ground-truth rewards, given only a single expert trajectory, and can outperform prior reward learning and offline IL methods across many tasks. Moreover, we demonstrate that SEABO also works well if the expert demonstrations contain only observations. Our code is publicly available at https://github.com/dmksjfl/SEABO.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "In recent years, reinforcement learning (RL) (<PERSON> & Bart<PERSON>, 2018) has made prominent achievements in fields like video games (<PERSON><PERSON><PERSON> et al., 2015; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020) , robotics (<PERSON><PERSON> et al., 2013) , nuclear fusion control (<PERSON><PERSON> et al., 2022) , etc. It is known that RL is a reward-oriented learning paradigm. Online RL algorithms typically require an interactive environment for data collection and improve the policy through trial-and-error. However, continual online interactions are usually expensive, time-consuming, or even dangerous in many practical applications. Offline RL (<PERSON> et al., 2012; <PERSON> et al., 2020) , instead, resorts to learning optimal policies from previously gathered datasets, which are composed of trajectories containing observations, actions, and rewards.", "cite_spans": [{"start": 45, "end": 67, "text": "(Sutton & Barto, 2018)", "ref_id": "BIBREF69"}, {"start": 127, "end": 146, "text": "(<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF55"}, {"start": 147, "end": 174, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF67"}, {"start": 186, "end": 206, "text": "(<PERSON><PERSON> et al., 2013)", "ref_id": "BIBREF34"}, {"start": 232, "end": 254, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF10"}, {"start": 595, "end": 615, "text": "(<PERSON> et al., 2012;", "ref_id": "BIBREF41"}, {"start": 616, "end": 636, "text": "<PERSON> et al., 2020)", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "A bare fact is that reward engineering is often difficult, expensive, and labor-intensive. It is also hard to specify or abstract a good reward function given some rules. To overcome this challenge in the offline setting, there are generally two methods. First, one can train the policy via the behavior cloning (BC) algorithm (<PERSON><PERSON><PERSON><PERSON>, 1988) , but its performance is heavily determined by the performance of the data-collecting policy (a.k.a., the behavior policy). Second, one can learn a reward function from some expert demonstrations and assign rewards to the unlabeled data in the dataset. Then, the policy can be optimized by leveraging the reward. This is also known as offline imitation learning (offline IL). Note that in many real-world tasks, acquiring a few expert demonstrations is easy (e.g., ask a human expert to operate the system) and affordable.", "cite_spans": [{"start": 327, "end": 344, "text": "(<PERSON><PERSON><PERSON><PERSON>, 1988)", "ref_id": "BIBREF62"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "However, it turns out that, similar to offline RL, offline IL also suffers from distribution shift issue (<PERSON> et al., 2022b; <PERSON><PERSON> et al., 2023) , where the learned policy deviates from the data-collecting policy, leading to poor performance during evaluation. Prior works concerning on distribution cor- The key idea behind SEABO. We assign larger rewards to transitions that are closer to the expert demonstration, and smaller rewards otherwise. The dotted lines connect the query samples with their nearest neighbors along the demonstration. Right: Illustration of the SEABO framework. Given an expert demonstration, we first construct a KD-tree and then feed the unlabeled samples into the tree to query their nearest neighbors. We use the resulting distance to calculate the reward label. Then one can adopt any existing offline RL algorithm to train on the labeled dataset.", "cite_spans": [{"start": 105, "end": 124, "text": "(<PERSON> et al., 2022b;", "ref_id": null}, {"start": 125, "end": 145, "text": "<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "rection estimation (DICE family) address this by enforcing the learned policy to be close to the behavior policy via a distribution divergence measure (e.g., f -divergence (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2019) ). However, such distribution matching schemes can incur training instability (<PERSON> et al., 2022) and over-conservatism (<PERSON> et al., 2023) , and they often involve training task-specific discriminators. On the other hand, some works seek to decouple the processes of reward annotation and policy optimization (<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2023) . However, they involve solving complex optimal transport problems or contrasting expert states and unlabeled trajectory states.", "cite_spans": [{"start": 172, "end": 198, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF19"}, {"start": 199, "end": 215, "text": "<PERSON> et al., 2019)", "ref_id": "BIBREF28"}, {"start": 294, "end": 311, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF53"}, {"start": 334, "end": 351, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF63"}, {"start": 522, "end": 542, "text": "(<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF87"}, {"start": 543, "end": 560, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In this paper, we propose a simple yet effective alternative, SEArch-Based method for Offline imitation learning, namely SEABO, that leverages search algorithms to acquire reward signals in an unsupervised learning manner. As illustrated in Figure 1 (left), we hypothesize that the transition is near-optimal if it lies close to the expert trajectory, hence larger reward ought to be assigned to it, and vice versa. To that end, we propose to determine whether the sample approaches the expert trajectory via measuring the distance between the query sample and its nearest neighbor in the expert trajectory. In practice, as depicted in Figure 1 (right), SEABO first builds a KD-tree upon expert demonstrations. Then for each unlabeled sample in the dataset, we query the tree to find its nearest neighbor, and measure their distance. If the distance is small (i.e., close to expert trajectory), a large reward will be annotated, while if the distance is large (i.e., stray away from the expert trajectory), the assigned reward is low. SEABO is efficient and easy to implement. It can be combined with any existing offline RL algorithm to acquire a meaningful policy from the static offline dataset.", "cite_spans": [], "ref_spans": [{"start": 248, "end": 249, "text": "1", "ref_id": "FIGREF0"}, {"start": 643, "end": 644, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Empirical results on the D4RL (<PERSON> et al., 2020) datasets show that SEABO can enable the offline RL algorithm to achieve competitive or even better performance against its performance under groundtruth rewards with only one expert trajectory. SEABO also beats recent strong reward annotation methods and imitation learning baselines on many datasets. Furthermore, we also demonstrate that SEABO can learn effectively when the expert demonstrations are composed of pure observations.", "cite_spans": [{"start": 30, "end": 47, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We formulate the interaction between the environment and policy as a Markov Decision Process (MDP) specified by the tuple ⟨S, A, p, r, γ, p 0 ⟩, where S is the state space, A is the action space, p : S × A → S is the transition dynamics, r : S × A → R is the scalar reward signal, γ ∈ [0, 1] is the discount factor, p 0 is the initial state distribution. A policy π(a|s) outputs the action based on the state s. We assume that the underlying MDP has a finite horizon. The goal of RL is to maximize the discounted future reward:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "2"}, {"text": "J(π) = E s0∼p0 E a∼π,st+1∼p(•|st,at) [", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "2"}, {"text": "In this paper, we focus on the offline IL setting. We assume that we have access to the dataset of expert demonstrations D e = {τ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "2"}, {"text": "(i) e } M i=1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "2"}, {"text": ", and a dataset of unlabeled data D u = {τ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "2"}, {"text": "(i) u } N i=1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "2"}, {"text": ", where M and N are the sizes of the expert dataset and unlabeled dataset, respectively. The unlabeled trajectories are gathered by some unknown behavior policy µ. Note that we allow the expert demonstrations to either contain actions or do not contain actions. We aim at attaining the reward function by extracting information from the expert trajectories and unlabeled trajectories, and assigning rewards to the unlabeled datasets, without any interactions with the environment. Then we can train the policy using any offline RL algorithm.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "2"}, {"text": "Offline Reinforcement Learning. In offline RL (<PERSON> et al., 2012; <PERSON> et al., 2020) , the agent is not permitted to interact with the environment, and can only learn policies from previously gathered dataset", "cite_spans": [{"start": 46, "end": 66, "text": "(<PERSON> et al., 2012;", "ref_id": "BIBREF41"}, {"start": 67, "end": 87, "text": "<PERSON> et al., 2020)", "ref_id": "BIBREF44"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "3"}, {"text": "D = {(s i , a i , r i , s i+1 )} N i=1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "3"}, {"text": ", where N is the dataset size. Existing work on offline RL can be generally categorized into model-based (<PERSON> et al., 2020; 2021; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2022b; <PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2022a; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2021; Uehara & Sun, 2022; <PERSON> et al., 2023) and model-free approaches (<PERSON><PERSON> et al., 2019; Fujimoto & Gu, 2021; <PERSON> et al., 2020; <PERSON><PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022c; a; <PERSON> et al., 2022; <PERSON> et al., 2020; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2022; <PERSON> et al., 2024) . The success of these methods rely heavily on the requirement that the datasets must contain annotated reward signals.", "cite_spans": [{"start": 105, "end": 122, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF79"}, {"start": 123, "end": 128, "text": "2021;", "ref_id": null}, {"start": 129, "end": 150, "text": "<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF29"}, {"start": 151, "end": 169, "text": "<PERSON><PERSON> et al., 2022b;", "ref_id": null}, {"start": 170, "end": 190, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF65"}, {"start": 191, "end": 208, "text": "<PERSON> et al., 2022a;", "ref_id": null}, {"start": 209, "end": 227, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF5"}, {"start": 228, "end": 248, "text": "<PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF25"}, {"start": 249, "end": 268, "text": "Uehara & Sun, 2022;", "ref_id": "BIBREF73"}, {"start": 269, "end": 288, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF83"}, {"start": 315, "end": 338, "text": "(<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF18"}, {"start": 339, "end": 359, "text": "Fujimoto & Gu, 2021;", "ref_id": "BIBREF17"}, {"start": 360, "end": 379, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF40"}, {"start": 380, "end": 403, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF39"}, {"start": 404, "end": 422, "text": "<PERSON><PERSON> et al., 2022c;", "ref_id": null}, {"start": 423, "end": 425, "text": "a;", "ref_id": null}, {"start": 426, "end": 445, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF6"}, {"start": 446, "end": 464, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF84"}, {"start": 465, "end": 482, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF63"}, {"start": 483, "end": 500, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF1"}, {"start": 501, "end": 519, "text": "<PERSON> et al., 2024)", "ref_id": "BIBREF77"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "3"}, {"text": "Imitation Learning. Imitation Learning (IL) considers optimizing the behavior of the agent given some expert demonstrations, and no reward is needed. The primary goal of IL is to mimic the behavior of the expert demonstrator. Behavior cloning (BC) (<PERSON><PERSON><PERSON><PERSON>, 1988) directly performs supervised regression or maximum-likelihood on expert demonstrations. Yet, BC can suffer from compounding error and may result in performance collapse upon unseen states (<PERSON> et al., 2011) . Another line of work, inverse reinforcement learning (IRL) (Arora & Doshi, 2021) , first learns a reward function using expert demonstrations, and then utilizes this reward function to train policies with RL algorithms. Typical IRL algorithms include adversarial methods (Ho & Ermon, 2016; <PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON> et al., 2017) , maximum-entropy approaches (<PERSON><PERSON><PERSON><PERSON> et al., 2008; <PERSON><PERSON><PERSON> et al., 2011) , normalizing flows (<PERSON><PERSON><PERSON> et al., 2023) , etc. However, these methods often require abundant online transitions to train a good policy. Imitation learning without online interactions, which is the focus of our work, is hence attractive and remains an active area. There are many advances in offline IL, such as applying online IRL algorithms in the offline setting (<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2023) , using energy-based methods (<PERSON> et al., 2020) , weighting the BC loss with the output of the trained discriminator (<PERSON> et al., 2022) , etc. Among them, DICE (<PERSON><PERSON><PERSON> et al., 2019) family receives much attention. Methods like ValueDICE (Kostrikov et al., 2020) , DemoDICE (<PERSON> et al., 2022b) , and LobsDICE (Kim et al., 2022a) can consistently drub BC in the offline setting. Notably, a recent work, OTR (Luo et al., 2023) , acquires the reward function in the offline setting via optimal transport. OTR decouples the processes of reward learning and policy optimization. Still, OTR needs to solve complex optimal transport problems. We, instead, explore to get the reward function via a search-based method.", "cite_spans": [{"start": 248, "end": 265, "text": "(<PERSON><PERSON><PERSON><PERSON>, 1988)", "ref_id": "BIBREF62"}, {"start": 454, "end": 473, "text": "(<PERSON> et al., 2011)", "ref_id": "BIBREF66"}, {"start": 535, "end": 556, "text": "(Arora & Doshi, 2021)", "ref_id": null}, {"start": 747, "end": 765, "text": "(<PERSON> <PERSON> E<PERSON>, 2016;", "ref_id": "BIBREF23"}, {"start": 766, "end": 784, "text": "<PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF27"}, {"start": 785, "end": 808, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF37"}, {"start": 809, "end": 828, "text": "<PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF2"}, {"start": 858, "end": 880, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2008;", "ref_id": "BIBREF86"}, {"start": 881, "end": 904, "text": "<PERSON><PERSON><PERSON> et al., 2011)", "ref_id": "BIBREF4"}, {"start": 925, "end": 946, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF15"}, {"start": 1272, "end": 1292, "text": "(<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF87"}, {"start": 1293, "end": 1310, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF82"}, {"start": 1340, "end": 1362, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF26"}, {"start": 1432, "end": 1449, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF76"}, {"start": 1474, "end": 1495, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF56"}, {"start": 1551, "end": 1575, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF38"}, {"start": 1587, "end": 1606, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 1622, "end": 1641, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 1719, "end": 1737, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "3"}, {"text": "Search Algorithms. Search algorithms (<PERSON><PERSON>, 1999) are critical components in artificial intelligence. Typical search algorithms include brute-force search algorithms (<PERSON><PERSON><PERSON>, 1959; <PERSON><PERSON> & Tyson, 1985; <PERSON><PERSON>, 1985; <PERSON> & <PERSON>, 1993 ), heuristic search approaches (<PERSON><PERSON> & <PERSON>, 1966; <PERSON> et al., 1968; <PERSON><PERSON>, 1970; Edelkamp & <PERSON>ödl, 2011) , etc. In this paper, we resort to the simple search approach, KD-tree (<PERSON>, 1975) , for capturing the nearest neighbors of the unlabeled data in the expert demonstrations.", "cite_spans": [{"start": 37, "end": 49, "text": "(<PERSON><PERSON>, 1999)", "ref_id": "BIBREF36"}, {"start": 166, "end": 182, "text": "(<PERSON><PERSON><PERSON>, 1959;", "ref_id": "BIBREF12"}, {"start": 183, "end": 205, "text": "Stickel & Tyson, 1985;", "ref_id": "BIBREF68"}, {"start": 206, "end": 217, "text": "<PERSON><PERSON>, 1985;", "ref_id": "BIBREF35"}, {"start": 218, "end": 237, "text": "<PERSON> & Korf, 1993", "ref_id": "BIBREF70"}, {"start": 269, "end": 291, "text": "(Doran & Michie, 1966;", "ref_id": "BIBREF13"}, {"start": 292, "end": 310, "text": "<PERSON> et al., 1968;", "ref_id": "BIBREF21"}, {"start": 311, "end": 322, "text": "<PERSON><PERSON>, 1970;", "ref_id": "BIBREF61"}, {"start": 323, "end": 348, "text": "Edelkamp & Schrödl, 2011)", "ref_id": "BIBREF14"}, {"start": 420, "end": 435, "text": "(<PERSON>, 1975)", "ref_id": "BIBREF3"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "3"}, {"text": "In this section, we formally present our novel approach for offline imitation learning, SEArch-Based Offline imitation learning (SEABO). We begin by analyzing the common formulation adopted in distribution matching IL methods (Ho <PERSON> E<PERSON>, 2016; <PERSON> et al., 2020; <PERSON><PERSON><PERSON><PERSON> et al., 2020) , which attempt to match the state-action distribution of the agent p π and the expert p e , often by means of minimizing some f -divergence measurement D f : arg min π D f (p π ∥p e ). Though these methods have promising results, they usually require training task-specific discriminators and suffer from training instability (<PERSON> et al., 2020; <PERSON> et al., 2022) . A natural question arises, can we get the reward signals without training neural networks? Instead of measuring the distribution of states or state-action pairs, we want to determine the optimality of a single transition. Our idea is quite straightforward, the closer the transition is to the expert trajectory, the more optimal this transition is. The agent ought to pay more attention to those optimal transitions. This motivates us to measure how close the unlabeled transition is to the expert trajectories. We propose to achieve this by finding the nearest neighbor of the query transition in the expert demonstrations, and then measuring their distance (e.g., Euclidean distance). If the distance is large, then the transition is away from the expert demonstration. While if the distance is small, it indicates that the transition is near-optimal, or even is exact expert data if the distance approaches 0. Intuitively, this distance can be interpreted as a reward signal.", "cite_spans": [{"start": 226, "end": 244, "text": "(<PERSON> <PERSON> E<PERSON>, 2016;", "ref_id": "BIBREF23"}, {"start": 245, "end": 262, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF32"}, {"start": 263, "end": 286, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF38"}, {"start": 614, "end": 633, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF75"}, {"start": 634, "end": 650, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF53"}], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "To that end, we construct a function dubbed NearestNeighbor(demo,query sample) that returns the nearest neighbor of the query sample in the expert demonstrations. Suppose the expert trajectories are made up of state-action pairs, then for the query sample (s, a, s ′ ), we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "(s e , ãe , s′ e ) = NearestNeighbor(D e , (s, a, s ′ )).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "(1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "Then we measure their deviation using some distance measurement D: d = D((s e , ãe , s′ e ), (s, a, s ′ )).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "(2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "Afterward, following prior work (<PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2021; <PERSON><PERSON> et al., 2023) , we get the rewards via a squashing function: r = α exp(-β × d), where α and β are hyperparameters that control the scale of the reward and the impact of the distance, respectively. Find its nearest neighbor, (s e , ãe , s′ e ) = NearestNeighbor(D e , (s, a, s ′ ))", "cite_spans": [{"start": 32, "end": 52, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF8"}, {"start": 53, "end": 73, "text": "<PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF15"}, {"start": 74, "end": 95, "text": "<PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF9"}, {"start": 96, "end": 113, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "5:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "Measure the distance: d = D((s e , ãe , s′ e ), (s, a, s ′ ))", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "6:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "Get the reward signal via Equation 37:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "D label ← D label ∪ (s, a, r, s ′ ) 8: end for We name the resulting method SEABO, and list its pseudo-code in Algorithm 1. For practical implementation of SEABO, we leverage KD-tree (<PERSON>, 1975) for searching the nearest neighbors, and adopt Euclidean distance (<PERSON><PERSON> et al., 2019) as the distance measurement for simplicity (i.e., the default setting of KD-tree). We also slightly modify the aforementioned formula of the reward function to make it better adapt to different tasks with one set of hyperparameters, which gives:", "cite_spans": [{"start": 175, "end": 198, "text": "KD-tree (Bentley, 1975)", "ref_id": null}, {"start": 265, "end": 286, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF72"}], "ref_spans": [], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "r = α exp - β × d |A| ,", "eq_num": "(3)"}], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "where |A| is the dimension of the action space. Note that this technique is also adopted in <PERSON><PERSON> et al. (2021) . We choose to use (s, a, s ′ ) to query since the magnitude of states and actions may be different. One possible solution is to query the demonstrations via (ξ × s, a), ξ ∈ R + , but it introduces an additional hyperparameter that may need to be tuned per dataset. We empirically find that involving s ′ in the query sample can ensure good performance across many tasks. The above procedure (as specified in Figure 1 (right)) also applies when the expert demonstrations contain only observations, because it is feasible that we find the nearest neighbors using only observations. SEABO enjoys many advantages over prior reward learning methods or offline imitation learning algorithms. First, SEABO does not require any additional processing upon the offline dataset1 .", "cite_spans": [{"start": 92, "end": 113, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF9"}], "ref_spans": [{"start": 530, "end": 531, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "The unlabeled dataset can have different trajectory lengths, and the unlabeled trajectories can be fragmented, or even scattered, since SEABO computes the rewards only using the single transition instead of the entire trajectory. Second, SEABO does not require training reward models or discriminators, hence getting rid of the issues of training instability and hyperparameter tuning of the neural networks. Third, SEABO is easy to implement and can be combined with any offline RL algorithm. To show the effectiveness of our method, we plot the distribution of ground-truth rewards (oracle) and rewards given by SEABO. We choose two datasets, halfcheetah-medium-expert-v2 and hopper-medium-v2 from D4RL (Fu et al., 2020) as examples, and use α = 1, β = 0.5, which is the same as our hyperparameter setup in Section 5. The results are summarized in Figure 2 . We find that the reward distributions of SEABO resemble those of oracle. Notably, SEABO successfully gives two peaks in halfcheetah-medium-expert, indicating that it can distinguish samples of different qualities. These reveal that SEABO can serve as a good reward labeler, which validates its combination with off-the-shelf offline RL algorithms.", "cite_spans": [{"start": 705, "end": 722, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF16"}], "ref_spans": [{"start": 857, "end": 858, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "OFFLINE IMITATION LEARNING VIA SEARCH-BASED METHOD", "sec_num": "4"}, {"text": "In this section, we empirically evaluate SEABO on D4RL datasets. We are targeted at examining, given only one single expert demonstration, whether SEABO can make different base offline RL algorithms recover or beat their performance with ground-truth rewards across varied tasks. We are also interested in exploring how SEABO competes against prior reward learning and offline imitation learning methods. We further investigate whether SEABO can work well if the expert demonstrations are composed of pure observations. Moreover, we check how different choices of search algorithms affect the performance of SEABO.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTS", "sec_num": "5"}, {"text": "We discard reward signals in the D4RL datasets to form unlabeled datasets. For expert demonstrations, we follow <PERSON><PERSON> et al. (2023) and utilize the trajectory with the highest return in the raw dataset for ease of evaluation. One can also use a separate expert trajectory. All of the experiments in this paper are run for 1M gradient steps over five different random seeds, and the results are averaged at the final gradient step. We report the mean performance in conjunction with the corresponding standard deviation. We adopt the same squashing function for tasks under the same domain. Unless specified, we use the number of expert demonstrations K = 1 for evaluation. It is worth noting that SEABO is computationally efficient since there is only a single expert trajectory, and the time complexity of KD-tree gives O(d f log |D e |), where d f is the feature dimension size. It takes SEABO about 1 minute to annotate 1 million unlabeled transitions using merely CPUs. Hence, we believe the overall computation overhead from SEABO is minor and tolerable. We defer the experimental details and hyperparameter setup for all of our experiments to Appendix A.", "cite_spans": [{"start": 112, "end": 129, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTS", "sec_num": "5"}, {"text": "SEABO upon different base algorithms. We first explore whether SEABO can aid different offline RL algorithms. We verify this by incorporating SEABO with two popular offline RL algorithms, TD3 BC (Fujimoto & Gu, 2021) and IQL (<PERSON><PERSON><PERSON><PERSON> et al., 2022) . We conduct experiments on 9 medium-level (medium, medium-replay, medium-expert) D4RL MuJoCo locomotion \"-v2\" datasets (halfcheetah, hopper, walker2d) and summarize the results in Table 1 .", "cite_spans": [{"start": 195, "end": 216, "text": "(Fujimoto & Gu, 2021)", "ref_id": "BIBREF17"}, {"start": 225, "end": 249, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF39"}], "ref_spans": [{"start": 437, "end": 438, "text": "1", "ref_id": "TABREF1"}], "eq_spans": [], "section": "MAIN RESULTS", "sec_num": "5.1"}, {"text": "One can see that IQL+SEABO beats IQL with ground-truth rewards on 6 out of 9 datasets, and TD3 BC+SEABO outperforms TD3 BC with raw rewards on 5 out of 9 datasets. On other datasets, SEABO can achieve competitive performance against the oracle performance. The overall scores of SEABO with IQL and TD3 BC exceed those of ground-truth rewards. This evidence indicates that SEABO can generate high-quality rewards and benefit different offline RL algorithms.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MAIN RESULTS", "sec_num": "5.1"}, {"text": "SEABO competes against baselines. To better illustrate the effectiveness of SEABO, we compare IQL+SEABO against the following strong reward learning and offline IL baselines: ORIL (<PERSON><PERSON><PERSON> et al., 2020) , which learns the reward function by contrasting the expert demonstrations with the unlabeled trajectories; UDS (<PERSON> et al., 2022) , which keeps the rewards in the expert demonstrations and simply assigns minimum rewards to the unlabeled data; OTR (<PERSON><PERSON> et al., 2023) , which learns a reward function via using the optimal transport to get the optimal alignment between the expert demonstrations and unlabeled trajectories. For a fair comparison, all these methods adopt IQL as the base algorithm. We additionally compare against BC, and 10%BC (<PERSON> et al., 2021) . We take the results of IQL+ORIL and IQL+UDS directly from the OTR paper. As OTR computes rewards using pure observations (and SEABO uses (s, a, s ′ ) to query the reward), we modify its way of solving optimal coupling by involving actions, and run IQL+OTR on these datasets with its official codebase. We summarize the comparison results in Table 2 . It can be found that, though methods like ORIL and OTR can lead to competitive or better performance on some of the datasets than IQL trained with raw rewards, SEABO beats them on numerous tasks. Meanwhile, SEABO is the only method that can surpass IQL with ground-truth rewards in terms of the total score.", "cite_spans": [{"start": 180, "end": 200, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF87"}, {"start": 314, "end": 331, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF81"}, {"start": 449, "end": 467, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF49"}, {"start": 744, "end": 763, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF5"}], "ref_spans": [{"start": 1113, "end": 1114, "text": "2", "ref_id": "TABREF2"}], "eq_spans": [], "section": "MAIN RESULTS", "sec_num": "5.1"}, {"text": "SEABO evaluation on wider datasets. We further evaluate IQL+SEABO on two challenging domains from D4RL, AntMaze and Adroit. We run IQL with ground-truth rewards to obtain the IQL performance. We take the results of IQL+OTR from its paper directly. Table 3 demonstrates the detailed comparison results. We find that IQL+SEABO beats IQL and IQL+OTR on 5 out of ", "cite_spans": [], "ref_spans": [{"start": 254, "end": 255, "text": "3", "ref_id": "TABREF3"}], "eq_spans": [], "section": "MAIN RESULTS", "sec_num": "5.1"}, {"text": "To further show the advantages of SEABO, we additionally compare it against recent strong offline imitation learning approaches, including DemoDICE (<PERSON> et al., 2022b) and SMODICE (<PERSON> et al., 2022) . We also convert two online IL algorithms into the offline setting, SQIL (<PERSON> et al., 2020) and PWIL (<PERSON><PERSON> et al., 2021) , where we replace the base algorithm in SQIL with TD3 BC and utilize IQL as the base algorithm for PWIL. All algorithms are run using their official implementations under the identical experimental setting as SEABO (i.e., one single expert demonstration).", "cite_spans": [{"start": 148, "end": 167, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 172, "end": 197, "text": "SMODICE (<PERSON> et al., 2022)", "ref_id": null}, {"start": 272, "end": 292, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF64"}, {"start": 302, "end": 324, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "COMPARISON AGAINST OFFLINE IL ALGORITHMS", "sec_num": "5.2"}, {"text": "For a fair comparison, we involve actions when training discriminators in SMODICE and measuring the distance in PWIL. We use IQL as the base algorithm for SEABO. The empirical results in Table 4 show that IQL+SEABO achieves the best performance on 6 out of 9 datasets, and has the highest total score (surpassing the second highest one by 13.9%). Though SEABO underperforms PWIL on some datasets, it significantly beats PWIL on tasks like hopper-medium-v2. Note that SMODICE behaves poorly on many tasks, which is also observed in <PERSON> et al. (2023) .", "cite_spans": [{"start": 531, "end": 547, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF45"}], "ref_spans": [{"start": 193, "end": 194, "text": "4", "ref_id": "TABREF4"}], "eq_spans": [], "section": "COMPARISON AGAINST OFFLINE IL ALGORITHMS", "sec_num": "5.2"}, {"text": "We now examine how SEABO behaves when the expert demonstrations consist of only observations, i.e., D e = {τ i e } M i=1 , where M is the size of the demonstration and τ = {s 0 , s 1 , . . . , s T }. In principle, SEABO can also calculate rewards by querying the KD-tree with only states, (s e , s′ e ) = NearestNeighbor(D e , (s, s ′ )). The distance can then be calculated with some distance metric D, d = D((s e , s′ e ), (s, s ′ )), and the rewards can be computed accordingly, via Equation 3. For baselines, since DemoDICE and ValueDICE are inapplicable to state-only regimes (<PERSON> et al., 2021) , we compare against LobsDICE (<PERSON> et al., 2022a) , which is a state-of-the-art offline IL algorithm that learns from expert observations. We also involve SMODICE, PWIL, and OTR for comparison, and train them using only expert observations. All baselines are run with their official implementa- We allow the single expert demonstration to involve actions (i.e., query with (s, a, s ′ )), and run all of the variants of SEABO using the same set of hyperparameters for a fair comparison. Empirical results on 9 D4RL locomotion datasets are shown in Table 6 . It is interesting to see that SEABO with Ball-tree is competitive with SEABO with KD-tree (their performance differences are minor), while SEABO with HNSW exhibits poor performance on many datasets. This means that the choice of the search algorithm counts in SEABO, and simply employing KD-tree can already guarantee good performance. Please see more discussions in Appendix C.", "cite_spans": [{"start": 581, "end": 599, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF85"}, {"start": 630, "end": 649, "text": "(<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [{"start": 1153, "end": 1154, "text": "6", "ref_id": "TABREF6"}], "eq_spans": [], "section": "STATE-ONLY REGIMES", "sec_num": "5.3"}, {"text": "It is vital to examine how sensitive SEABO is to the introduced hyperparameters. Due to the space limit, we can only report part of the experiments here and defer more experiments to Appendix B.3. Reward scale α. α controls the scale of the resulting rewards. To check its influence, we conduct experiments on three datasets from D4RL locomotion tasks and sweep α across {1, 5, 10}. Results in Figure 3 demonstrate that the best α may depend on the dataset while a smaller α is preferred.", "cite_spans": [], "ref_spans": [{"start": 401, "end": 402, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "PARAMETER STUDY", "sec_num": "5.5"}, {"text": "Weighting coefficient β. β is probably the most critical hyperparameter which decides the scale of the distance. In Figure 4 (a), we vary β across {0.1, 0.5, 1, 5}, and find that the performance drops with too small or too large β. It seems that β = 0.5 or β = 1 can achieve a good trade-off.", "cite_spans": [], "ref_spans": [{"start": 123, "end": 124, "text": "4", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "PARAMETER STUDY", "sec_num": "5.5"}, {"text": "Number of neighbors N . To see whether the number of neighbors N matters, we run IQL+SEABO with N ∈ {1, 5, 10}. Results in Figure 4 (b) show that SEABO is robust to this hyperparameter.", "cite_spans": [], "ref_spans": [{"start": 130, "end": 131, "text": "4", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "PARAMETER STUDY", "sec_num": "5.5"}, {"text": "Number of expert demonstrations K. We investigate whether increasing the number of expert demonstrations can further boost the performance of SEABO and baselines by running experiments of these methods on 9 MuJoCo locomotion tasks. We report the aggregate performance (i.e., total score) in Table 7 . One can see that all methods enjoy performance improvement when K = 10, while none of them can outperform SEABO (there still exists a large performance gap). ", "cite_spans": [], "ref_spans": [{"start": 297, "end": 298, "text": "7", "ref_id": "TABREF7"}], "eq_spans": [], "section": "PARAMETER STUDY", "sec_num": "5.5"}, {"text": "In this paper, we propose a novel search-based offline imitation learning method, dubbed SEABO, that annotates the unlabeled offline trajectories in an unsupervised learning manner. SEABO builds a KD-tree using the expert demonstration(s), and searches the nearest neighbors of the query sample.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "6"}, {"text": "We then measure their distance and output the reward signal via a squashing function. SEABO is easy to implement and can be incorporated with any offline RL algorithm. Experiments on D4RL datasets show that SEABO can incur competitive or even better offline policies than pre-defined reward functions. SEABO can also function well if the expert demonstrations are made up of only observations. For future work, it is interesting to apply SEABO in visual offline RL datasets (e.g., <PERSON> et al. (2022b) ), or adapt SEABO to cross-domain offline imitation learning tasks.", "cite_spans": [{"start": 481, "end": 498, "text": "<PERSON> et al. (2022b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "6"}, {"text": "In this section, we detail the hyperparameter setup utilized in our experiments. We conduct experiments on 9 MuJoCo locomotion \"-v2\" medium-level datasets, 6 AntMaze \"-v0\" datasets, and 8 Adroit \"-v0\" datasets, yielding a total of 23 tasks. We list the hyperparameter setup for IQL and TD3 BC on MuJoCo locomotion tasks in Table 8 . We keep the hyperparameter setup of the base offline RL algorithms unchanged for both IQL and TD3 BC. For IQL, we do not rescale the rewards in the datasets by 1000 /max return-min return, as we have an additional hyperparameter α to control the reward scale. In practice, we find minor performance differences if we rescale the rewards. We generally utilize the same formula of squashing function for most of the datasets, except that we set β = 1 in hopper-medium-replay-v2, and α = 10, β = 0.1 in hopper-medium-expert-v2 for better performance. Note that using α = 1, β = 0.5 on these tasks can also produce a good performance (e.g., setting α = 1, β = 0.5 on hopper-medium-replay-v2 leads to an average performance of 87.2, still outperforming strong baselines like OTR), while slightly modifying the hyperparameter setup can result in better performance. We divide the scaled distance by the action dimension of the task to strike a balance between different tasks (as we use one set of hyperparameters). This is also adopted in PWIL paper (Dad<PERSON> et al., 2021) . For TD3 BC, we use the same type of squashing function as IQL on the locomotion tasks, with α = 1, β = 0.5, except that we use α = 10 for walker2d-medium-v2 and walker2d-medium-replay-v2 for slightly better performance. We use the official implementation of TD3 BC (https://github.com/sfujim/TD3 BC) and adopt the PyTorch (Paszke et al. (2019) ) version of IQL for evaluation. We summarize the hyperparameter setup of SEABO (using IQL as the underlying algorithm) on the AntMaze domain and Adroit domain in Table 9 and Table 10 , respectively. We only list the different hyperparameters in these tables and the other hyperparameters follow those presented in Table 8 . Note that we filter the highest return trajectory as the expert demonstration in the Adroit domain, while selecting the goal-reached trajectory as the expert demonstration in the AntMaze domain, which is also adopted in OTR paper (Luo et al., 2023) . We adopt a comparatively large β = 5 on AntMaze tasks. We also follow the IQL paper (Kostrikov et al., 2022) to subtract 1 from the rewards, which we find can result in better performance. For Adroit tasks, we remove the action dimension in the squashing function, since these tasks have large action space dimensions. If one insists on involving |A|, a much larger β than 0.5 is then necessary to mitigate its influence. We find that simply removing |A| can ensure quite good performance on all of the evaluated Adroit datasets. Note that OTR (Luo et al., 2023) also adopts different forms of squashing functions for different domains. We query with (s, s ′ ) for Adroit tasks and (s, a, s ′ ) for other domains.", "cite_spans": [{"start": 1378, "end": 1400, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF9"}, {"start": 1725, "end": 1746, "text": "(<PERSON><PERSON><PERSON> et al. (2019)", "ref_id": "BIBREF59"}, {"start": 2302, "end": 2320, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF49"}, {"start": 2407, "end": 2431, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF39"}, {"start": 2867, "end": 2885, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF49"}], "ref_spans": [{"start": 329, "end": 330, "text": "8", "ref_id": "TABREF8"}, {"start": 1916, "end": 1917, "text": "9", "ref_id": "TABREF9"}, {"start": 1922, "end": 1930, "text": "Table 10", "ref_id": "TABREF10"}, {"start": 2068, "end": 2069, "text": "8", "ref_id": "TABREF8"}], "eq_spans": [], "section": "A HYPERPARAMETER SETUP", "sec_num": null}, {"text": "To acquire expert demonstrations, we use the trajectory with the highest return as expert demonstrations on MuJoCo locomotion tasks and Adroit tasks, and filter the goal-reached trajectory in AntMaze tasks. For all of the baseline reward learning and offline imitation learning algorithms, we follow this setting and run them with their official codebases2 over five different random seeds. We use the PWIL implementation from Acme (<PERSON> et al., 2020) 3 .", "cite_spans": [{"start": 432, "end": 454, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF24"}], "ref_spans": [], "eq_spans": [], "section": "A HYPERPARAMETER SETUP", "sec_num": null}, {"text": "In SEABO, we use the KD-tree implementation from the scipy library (<PERSON><PERSON><PERSON><PERSON> et al., 2020) , i.e., scipy.spatial.KDTree. We set the number of nearest neighbors N = 1, and keep other default hyperparameters in KD-tree. Note that we can directly get the desired distance by querying the KD-tree. For Ball-tree, we use its implementation in the scikit-learn package (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2011) , i.e., sklearn.neighbors.BallTree. We also keep its original hyperparameters unchanged. For HNSW, we use its implementation in hnswlib4 . We use the suggested hyperparameter setting in its GitHub page and set ef construction=200 (which defines a construction time/accuracy trade-off) and M=16 (which defines the maximum number of outgoing connections in the graph). All these search algorithms adopt the Euclidean distance as the distance measurement.", "cite_spans": [{"start": 67, "end": 90, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF74"}, {"start": 363, "end": 387, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2011)", "ref_id": "BIBREF60"}], "ref_spans": [], "eq_spans": [], "section": "A HYPERPARAMETER SETUP", "sec_num": null}, {"text": "In our experiments, we use MuJoCo 2.0 (<PERSON><PERSON><PERSON> et al., 2012) with Gym version 0.18.3, <PERSON>y<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> et al., 2019) version 1.8. We use the normalized score metric recommended in the D4RL paper (<PERSON> et al., 2020) , where 0 corresponds to a random policy, and 100 corresponds to an expert policy. Formally, suppose we get the average return J by deploying the learned policy in the test environment, the normalized score gives:", "cite_spans": [{"start": 38, "end": 60, "text": "(<PERSON><PERSON><PERSON> et al., 2012)", "ref_id": "BIBREF71"}, {"start": 94, "end": 115, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF59"}, {"start": 194, "end": 211, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "A HYPERPARAMETER SETUP", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Normalized score = J -J r J e -J r × 100,", "eq_num": "(4)"}], "section": "A HYPERPARAMETER SETUP", "sec_num": null}, {"text": "where J r is the return of a random policy, and J e is the return of an expert policy.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A HYPERPARAMETER SETUP", "sec_num": null}, {"text": "In this section, we present the missing experimental results from the main text due to the space limit.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B MISSING EXPERIMENTAL RESULTS", "sec_num": null}, {"text": "Table 11 : Comparison of SEABO against baseline algorithms under 10 expert demonstrations.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 8, "text": "11", "ref_id": "TABREF1"}], "eq_spans": [], "section": "B MISSING EXPERIMENTAL RESULTS", "sec_num": null}, {"text": "We use IQL as the base algorithm for SEABO, PWIL, and OTR. We report the mean performance at the final 10 evaluations for each algorithm, and ± captures the standard deviation. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B MISSING EXPERIMENTAL RESULTS", "sec_num": null}, {"text": "In Section 5.5, we present the comparison results of SEABO and baseline reward learning and offline IL algorithms under different numbers of expert demonstrations K ∈ {1, 10}. However, we only report the aggregate performance (i.e., the total score) on the 9 MuJoCo locomotion mediumlevel tasks (medium, medium-replay, medium-expert) in Table 7 . To make the comparison clearer, we present the detailed normalized scores of these methods under K = 10 on different datasets in Table 11 , where we mainly compare SEABO against different variants of PWIL and OTR. SEABO computes the rewards with actions involved in the single expert demonstration here.", "cite_spans": [], "ref_spans": [{"start": 343, "end": 344, "text": "7", "ref_id": "TABREF7"}, {"start": 482, "end": 484, "text": "11", "ref_id": "TABREF1"}], "eq_spans": [], "section": "B.1 NUMERICAL COMPARISON UNDER TEN EXPERT DEMONSTRATIONS", "sec_num": null}, {"text": "The results reveal that SEABO outperforms baseline methods on 5 out of 9 datasets and is competitive with baselines on the rest of the datasets. SEABO achieves a total score of 716.1, surpassing the second best method (OTR-state) by 3.2%. Though we observe that PWIL-action beats SEABO on datasets like halfcheetah-medium-v2, it can perform poorly on datasets like hopper-medium-expert-v2. We also note that the performance of PWIL deteriorates in the state-only regimes, i.e., learning from pure expert observations. This phenomenon is also reported in <PERSON><PERSON> et al. (2021) ; <PERSON><PERSON> et al. (2023) . SEABO, instead, is flexible and can be applied regardless of whether the expert demonstrations contain actions.", "cite_spans": [{"start": 554, "end": 575, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF9"}, {"start": 578, "end": 595, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "B.1 NUMERICAL COMPARISON UNDER TEN EXPERT DEMONSTRATIONS", "sec_num": null}, {"text": "Furthermore, we show in Table 12 the results of IQL+SEABO on AntMaze and Adroit datasets when 10 expert demonstrations are provided. We compare IQL+SEABO against IQL+OTR and IQL with raw rewards (denoted as IQL). The results demonstrate that SEABO can recover the performance of the offline RL algorithm with ground-truth rewards and sometimes yield better performance. This advantage is agnostic to the number of expert demonstrations K.", "cite_spans": [], "ref_spans": [{"start": 30, "end": 32, "text": "12", "ref_id": "TABREF12"}], "eq_spans": [], "section": "B.1 NUMERICAL COMPARISON UNDER TEN EXPERT DEMONSTRATIONS", "sec_num": null}, {"text": "IQL+SEABO matches the performance of IQL+OTR on many AntMaze tasks and outperforms IQL+OTR on 6 out of 8 datasets from the Adroit domain. On both the AntMaze domain and Adroit domain, OTR underperforms SEABO in terms of the total score. One may notice that the performance of IQL+SEABO decreases with more expert demonstrations, mainly on the Adroit datasets. This is caused by the performance drop on pen-human-v0, which dominate the total score (the magnitude of its score is much larger than those of other datasets). One can also observe that the performance of IQL+OTR declines on many Adroit tasks, given 10 expert demonstrations (see Table 7 in <PERSON><PERSON> et al. (2023) ). Still, IQL+SEABO exhibits strong performance across numerous datasets.", "cite_spans": [{"start": 652, "end": 669, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF49"}], "ref_spans": [{"start": 647, "end": 648, "text": "7", "ref_id": "TABREF7"}], "eq_spans": [], "section": "B.1 NUMERICAL COMPARISON UNDER TEN EXPERT DEMONSTRATIONS", "sec_num": null}, {"text": "Since the majority of the experiments in the main text are conducted using IQL as the base offline RL algorithm, it is interesting to see how SEABO competes against baseline methods with another offline RL algorithm as the base method. To that end, we choose TD3 BC and incorporate it with the strong baseline method, OTR. We follow the experimental setting utilized in the main text, filter a single expert demonstration with the highest return in the offline dataset, and deem it as the expert demonstration. We run TD3 BC+SEABO and TD3 BC+OTR on 9 D4RL MuJoCo locomotion datasets. We follow our experimental setup specified in Appendix A, and use the default hyperpa- Table 13 : Comparison of SEABO against OTR using TD3 BC as the base algorithm. We report the average normalized scores and their standard deviations. We bold and highlight the mean score cell except for TD3 BC. We adopt one single expert demonstration for OTR and SEABO. rameter setup of OTR suggested by the authors. We summarize the comparison results in Table 13 . It turns out that TD3 BC+SEABO outperforms TD3 BC+OTR on 8 out of 9 datasets, often by a large margin, surpassing it by 10.1% in terms of the total score. TD3 BC+SEABO is the only algorithm that even beats TD3 BC learned with raw rewards in total score. We observe that the standard deviation of TD3 BC+OTR is large on datasets like halfcheetah-medium-expert-v2, while the standard deviation of TD3 BC+SEABO is much smaller. This evidence indicates that SEABO is superior to OTR when acting as the reward labeler, and can consistently aid different base offline RL algorithms recover its performance under ground-truth rewards or achieve better performance.", "cite_spans": [], "ref_spans": [{"start": 677, "end": 679, "text": "13", "ref_id": "TABREF3"}, {"start": 1034, "end": 1036, "text": "13", "ref_id": "TABREF3"}], "eq_spans": [], "section": "B.2 COMPARISON OF TD3 BC+OTR AND TD3 BC+SEABO", "sec_num": null}, {"text": "In Section 5.5, we are only able to attach the results on a small proportion of datasets from D4RL, e.g., halfcheetah-medium-replay-v2 due to the space limit. In this part, we include wider experimental results in terms of the reward scale α, weighting coefficient β, and number of neighbors N . Again, we use IQL as the base offline RL algorithm for SEABO. The expert demonstrations utilized here contain actions. We follow the hyperparameter setup specified in Section A.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 HYPERPARAMETER SENSITIVITY", "sec_num": null}, {"text": "Reward scale α. The reward scale α controls the magnitude of the computed rewards. In Figure 3 of the main text, we find that a smaller α seems to be better (especially on hopper-medium-v2). We further conduct experiments on three additional tasks, halfcheetah-medium-expert-v2, hopper-medium-replay-v2, and walker2d-medium-v2 by varying α ∈ {1, 5, 10}. The results are shown in Figure 5 , where we actually do not find much performance difference of α on these three tasks. That indicates that IQL+SEABO is robust to α on most of the datasets. In practice, one can simply set α = 1, which we find can already yield very good performance on MuJoCo tasks, AntMaze tasks, and Adroit tasks.", "cite_spans": [], "ref_spans": [{"start": 93, "end": 94, "text": "3", "ref_id": "FIGREF2"}, {"start": 386, "end": 387, "text": "5", "ref_id": null}], "eq_spans": [], "section": "B.3 HYPERPARAMETER SENSITIVITY", "sec_num": null}, {"text": "Weighting coefficient β. As commented in the main text, the weighting coefficient β is perhaps the most important hyperparameter in SEABO, since it controls the weights of the measured distance and this may have a significant influence on the final rewards. For a specific domain, we mostly adopt a fixed β as we do not want to bother tuning this hyperparameter. However, we believe it is vital to examine how β influences the performance of SEABO in wider experiments. We additionally conduct several experiments on halfcheetah-medium-expert-v2, hopper-medium-replay-v2, walker2d-medium-replay-v2 from D4RL locomotion tasks. We sweep β across {0.1, 0.5, 1, 5}, and summarize the results in Figure 6 . It can be clearly seen that a large β results in poor performance on halfcheetah-medium-expert-v2 and walker2d-medium-replay-v2, while setting β = 5 results in the best performance on hopper-medium-replay-v2. In the hyperparameter setup part, we state that we set β = 1 on hopper-medium-replay-v2 due to the fact that SEABO is comparatively stable with β{0.5, 1}. We do not doubt that the best β is task-dependent, and one can get higher performance by carefully tuning this hyperparameter. However, we empirically show that using a fixed β is also feasible, and we believe this is appealing since the users can get rid of the work of tedious hyperparameter search.", "cite_spans": [], "ref_spans": [{"start": 698, "end": 699, "text": "6", "ref_id": null}], "eq_spans": [], "section": "B.3 HYPERPARAMETER SENSITIVITY", "sec_num": null}, {"text": "Number of neighbors N . The number of neighbors N is a hyperparameter introduced in the nearest neighbor algorithms. For all of our main experiments, we simply adopt N = 1, i.e., searching for the nearest neighbor. In Figure 4 (b), we see that SEABO is robust to this hyperparameter. To examine whether this conclusion applies to a wider range of datasets, we conduct experiments on three additional datasets, halfcheetah-medium-v2, halfcheetah-medium-expert-v2, and walker2d-medium-replay-v2. The results are summarized in Figure 7 , where we also observe that SEABO is robust to this hyperparameter, indicating the effectiveness and generality of SEABO.", "cite_spans": [], "ref_spans": [{"start": 225, "end": 226, "text": "4", "ref_id": "FIGREF3"}, {"start": 531, "end": 532, "text": "7", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "B.3 HYPERPARAMETER SENSITIVITY", "sec_num": null}, {"text": "In this part, we investigate how SEABO behaves under long-horizon manipulation tasks. To that end, we evaluate SEABO in Kitchen datasets (<PERSON> et al., 2020) . The kitchen environment (<PERSON> et al., 2019) consists of a 9 DoF Franka robot interacting with a kitchen scene that includes an openable microwave, four turnable oven burners, an oven light switch, a freely movable kettle, two hinged cabinets, and a sliding cabinet door. In kitchen, the robot may need to manipulate different components, e.g., it may need to open the microwave, move the kettle, turn on the light, and slide open the cabinet (precision is required). We run IQL+SEABO on three kitchen datasets using the author-recommended hyperparameters of IQL on the kitchen environment. We set reward scale α = 1, coefficient β = 0.5 for SEABO. We compare IQL+SEABO against some baselines taken from the IQL paper and summarize the results in Table 14 . We find that SEABO exhibits superior performance, surpassing IQL with raw rewards by 21.0%. We believe these results show that SEABO can aid some long-horizon manipulation tasks.", "cite_spans": [{"start": 137, "end": 154, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF16"}, {"start": 181, "end": 201, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF20"}], "ref_spans": [{"start": 910, "end": 912, "text": "14", "ref_id": "TABREF14"}], "eq_spans": [], "section": "B.4 PER<PERSON><PERSON><PERSON><PERSON><PERSON> OF SEABO UNDER LONG-HORIZON MANIPULATION TASKS", "sec_num": null}, {"text": "However, we experimentally find that SEABO does not exhibit strong performance for some tasks that require precision, e.g., the IKEA Furniture assembly benchmark (<PERSON> et al., 2019; 2021; <PERSON><PERSON> et al., 2023) . We leave the open problem of how to enable SEABO to successfully address such benchmarks a future work.", "cite_spans": [{"start": 162, "end": 180, "text": "(<PERSON> et al., 2019;", "ref_id": "BIBREF42"}, {"start": 181, "end": 186, "text": "2021;", "ref_id": null}, {"start": 187, "end": 204, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "B.4 PER<PERSON><PERSON><PERSON><PERSON><PERSON> OF SEABO UNDER LONG-HORIZON MANIPULATION TASKS", "sec_num": null}, {"text": "In this section, we provide the detailed training curves of IQL+SEABO on the locomotion tasks, AntMaze tasks, and Adroit tasks. We also provide learning curves of TD3 BC+SEABO on locomotion tasks. We summarize the results of IQL+SEABO on D4RL MuJoCo locomotion tasks in Figure 8 , the performance of IQL+SEABO on AntMaze tasks in Figure 9 , and the curves of IQL+SEABO on Adroit tasks in Figure 10 . The results of TD3 BC+SEABO are depicted in Figure 11 .", "cite_spans": [], "ref_spans": [{"start": 277, "end": 278, "text": "8", "ref_id": null}, {"start": 337, "end": 338, "text": "9", "ref_id": null}, {"start": 395, "end": 397, "text": "10", "ref_id": "FIGREF0"}, {"start": 451, "end": 453, "text": "11", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "B.5 LEARNING CURVES", "sec_num": null}, {"text": "From all these results, we find that both IQL+SEABO and TD3 BC+SEABO have stable and strong performance on the evaluated tasks, indicating the advantages of our method.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.5 LEARNING CURVES", "sec_num": null}, {"text": "The success of SEABO can be largely attributed to the adopted search algorithm (i.e., KD-tree). In Section 5.4 of the main text, we compare different design choices for the underlying search algorithm. It is not surprising to find that Ball-tree results in a similar performance as KD-tree, as Ball-tree shares many similarities with KD-tree. However, we find that HNSW incurs quite poor performance on many datasets using its default hyperparameter setup (see Appendix A). HNSW builds a multi-layer structure made up of a hierarchical set of proximity graphs for nested subsets of the stored elements while employing a heuristic for selecting proximity graph neighbors. HNSW is a graph-based search algorithm. Based on the empirical results in Table 6 in the main text, we find that HNSW leads to quite poor performance for the base offline RL algorithm, only achieving competitive performance against KD-tree on halfcheetah-medium-v2 and walker2d-medium-expert-v2. ", "cite_spans": [], "ref_spans": [{"start": 751, "end": 752, "text": "6", "ref_id": "TABREF6"}], "eq_spans": [], "section": "C DISCUSSIONS ON D<PERSON><PERSON><PERSON>ENT SEARCH ALGORITHMS", "sec_num": null}, {"text": "Methods like OTR(<PERSON><PERSON> et al.,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "2023) need zero padding of the unlabeled trajectories to ensure that they have identical length as the expert trajectories.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "datasets on AntMaze, and outperforms baselines on 6 out of 8 datasets on Adroit, often by a large margin. IQL+SEABO incurs a performance improvement of 6.0% and 32.0% beyond IQL with vanilla rewards on AntMaze and Adroit tasks, respectively. These indicate that SEABO with one single expert trajectory can handle datasets with diverse behavior, and work as a good and promising proxy to the hand-crafted rewards.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "OTR official codebase: https://github.com/ethanluoyc/optimal transport reward/. SMODICE official codebase: https://github.com/JasonMa2016/SMODICE. DemoDICE and LobsDICE official codebase: https://github.com/geon-hyeong/imitation-dice.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "3 https://github.com/deepmind/acme/tree/master/acme/agents/jax/", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "pwil 4 https://github.com/nmslib/hnswlib", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by the STI 2030-Major Projects under Grant 2021ZD0201404 and NSFC under Grant 62250068. The authors would like to thank the anonymous reviewers for their valuable comments and advice.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACKNOWLEDGEMENTS", "sec_num": null}, {"text": "In this subsection, we try to understand why HNSW fails through some empirical evidence. We choose some subsets, halfcheetah-medium-v2, halfcheetah-medium-expert-v2, hopper-medium-replay-v2, hopper-medium-expert-v2, walker2d-medium-v2, and walker2d-medium-replay-v2 , from D4RL MuJoCo datasets and plot the reward density of ground-truth rewards, rewards computed using KD-tree, and rewards acquired via HNSW. We summarize the results in Figure 12 . It is clear that SEABO with KD-tree can produce a similar reward structure as the ground-truth reward distribution, while SEABO with HNSW tends to assign large rewards to only a small proportion of samples and small rewards to the majority of transitions. We believe this explains the unsatisfying performance of IQL+SEABO with HNSW as the base search algorithm, indicating that a graph-based search mechanism may not be suitable for D4RL datasets. Another possible explanation is that the hyperparameters of HNSW need to be tuned to adapt to different tasks. We do not doubt that a careful tuning of hyperparameters (e.g., the maximum number of outgoing connections in the graph, the number of neighbors, etc.) has the potential of making SEABO with HNSW work in D4RL datasets. However, we do not think it is necessary to do that considering the fact that adopting KD-tree with its default hyperparameters can already result in quite good performance across different datasets. Hence, it is recommended that one uses KD-tree (or Ball-tree) as the base search algorithm.", "cite_spans": [{"start": 113, "end": 265, "text": "halfcheetah-medium-v2, halfcheetah-medium-expert-v2, hopper-medium-replay-v2, hopper-medium-expert-v2, walker2d-medium-v2, and walker2d-medium-replay-v2", "ref_id": null}], "ref_spans": [{"start": 445, "end": 447, "text": "12", "ref_id": null}], "eq_spans": [], "section": "Published as a conference paper at ICLR 2024", "sec_num": null}, {"text": "In Table 15 , we list the compute infrastructure that we use to run all of the algorithms. ", "cite_spans": [], "ref_spans": [{"start": 9, "end": 11, "text": "15", "ref_id": null}], "eq_spans": [], "section": "D COMPUTE INFRASTRUCTURE", "sec_num": null}, {"text": "AMD EPYC 7452 RTX3090×8 288GB", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CPU GPU Memory", "sec_num": null}, {"text": "Despite the simplicity and effectiveness of our proposed algorithm, SEABO, we have to admit honestly that there may exist some potential limitations. First, SEABO is slightly sensitive to the weighting coefficient β on some datasets (not all datasets), and one may need to manually tune it so as to find the best-suited hyperparameter setup for a specific task. While based on our empirical results, one can find the best β ∈ {0.5, 1, 5} using grid search, It is not difficult to conduct experiments since SEABO is computationally efficient (and can be applied with only CPUs). Second, it may take more time for SEABO to annotate the unlabeled trajectories with visual input, as images are hard to process. Whereas, we can preprocess the visual images using some pre-trained image encoder (e.g., ImageNet pretrain models) to obtain low-dimensional representations of the high-dimensional image. Note that we build KD-tree upon expert demonstrations which usually contain a small amount of transitions. Thus, it should not be time-consuming to annotate the visual trajectories.We hope this work can provide some new insights to the community and inspire future work on offline imitation learning.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E LIMITATIONS", "sec_num": null}, {"text": "In this section, we provide reward distribution plots of the ground truth rewards, rewards obtained by SEABO, and rewards output from HNSW on some Adroit-v0 and AntMaze-v0 tasks, hammer-human-v0, hammer-cloned-v0, door-human-v0, door-cloned-v0, antmaze-uamze-v0, and antmaze-medium-diverse-v0. Note that we provide the histogram plot of rewards in antmaze-medium-diverse-v0 as most of the samples in this datasets have quite similar reward signals, making it difficult to draw the density plot. We summarize the results in Figure 13 . It can be seen that with KD-tree, SEABO outputs similar reward density as vanilla rewards (e.g., SEABO successfully gives three peaks in hammer-human-v0 and door-human-v0).", "cite_spans": [], "ref_spans": [{"start": 530, "end": 532, "text": "13", "ref_id": null}], "eq_spans": [], "section": "F ADDITIONAL R<PERSON>WARD PLOTS ON ADROIT AND <PERSON><PERSON><PERSON><PERSON><PERSON> TASKS", "sec_num": null}, {"text": "There are some previous studies that use nearest neighbor-based methods for imitation learning, e.g., <PERSON><PERSON> et al. (2021) . Among them, the most relevant to our work is <PERSON><PERSON><PERSON> (2022) . In this section, we discuss the connections and differences between our method and prior work, ILR (Ciosek, 2022) , which can be summarized below:• The motivations are varied. The practical reward formula in ILR is given by r = 1 -min (s ′ ,a ′ )∈D d l2 ((s, a), (s ′ , a ′ )) 2 , which is a relaxation of its theoretical version.There exists a gap between the theory and the resulting reward formula. The authors claim that the relaxation is an upper bound on the scaled theoretical reward and interpret L = min (s ′ ,a ′ )∈D d l2 ((s, a), (s ′ , a ′ )) 2 as the l 2 -diameter of the state-action space. The primary goal of doing so is to reduce imitation learning to RL with a stationary reward for deterministic experts. However, the motivation of SEABO is that we would like to determine the optimality of the single transition (instead of examining whether the transition comes from the expert trajectory or performing relaxation to the rewards). We assume that the transition is near-optimal if it lies close to the expert trajectory. Hence, we assign a larger reward to the transition if it is close to the expert trajectory and a smaller reward otherwise. Meanwhile, SEABO does not require that the expert is deterministic (and also does not require that the environment is deterministic). We aim to adopt SEABO to annotate unlabeled samples in the dataset and train off-the-shelf offline RL algorithms.• The methods are different but connected. The reward formula adopted in ILR is a special case of SEABO with Euclidean distance. SEABO does not interpret L as the diameter of the state-action space. SEABO can adopt N nearest neighbors and use their average distance to compute the reward (ILR simply finds the smallest Euclidean distance between sample (s, a) and the expert trajectory). Meanwhile, SEABO is not restricted to Euclidean distance. Our procedure is, that we first find the nearest neighbor of the query sample, and then utilize some distance measurements (different distance measurements can be used here) to decide the distance between the query sample and its nearest neighbor, and finally get the reward by adopting a squashing function. Furthermore, SEABO strongly relies on the nearest neighbor methods (e.g., KD-Tree), and one can use different types of nearest neighbor algorithms in SEABO, while ILR does not emphasize search algorithms. Note that different search algorithms with different hyperparameter setups can result in different final rewards. For example, in scipy.spatial.KDTree.query, setting eps larger than 0 enables approximate nearest neighbors search and ensures that the k-th returned value is no further than (1 + eps) times the distance to the real k-th nearest neighbor. This may incur different results from ILR even under Euclidean distance. Moreover, SEABO can also work in stateonly regimes, which is both a more general and challenging setting, while ILR strongly relies on the assumption that state-action pairs are present in the expert trajectory in its theory and practical implementation. Finally, one can query with (s, a, s ′ ), (s, a) or (s, s ′ ) in SEABO (ILR is limited to (s, a)), and SEABO adopts a different choice of squashing function.• The settings are varied. SEABO is targeted at the offline imitation learning setting while ILR addresses the online setting. It also turns out that the experiment setup (e.g., number of expert trajectories) is different between SEABO and ILR.", "cite_spans": [{"start": 102, "end": 120, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF58"}, {"start": 168, "end": 181, "text": "Ciosek (2022)", "ref_id": "BIBREF7"}, {"start": 283, "end": 297, "text": "(Ciosek, 2022)", "ref_id": "BIBREF7"}], "ref_spans": [], "eq_spans": [], "section": "G DISCUSSIONS ON SEABO AND ILR", "sec_num": null}], "bib_entries": {"BIBREF1": {"ref_id": "b1", "title": "Pessimistic Bootstrapping for Uncertainty-Driven Offline Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>arg", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON>. Pessimistic Bootstrapping for Uncertainty-Driven Offline Reinforcement Learning. In International Conference on Learning Representations, 2022. URL https://openreview. net/forum?id=Y4cs1Z3HnqL.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "End-to-End Differentiable Adversarial Imitation Learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Oron", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Itai", "middle": [], "last": "Caspi", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. End-to-End Differentiable Adversarial Imitation Learning. In International Conference on Machine Learning, 2017.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Multidimensional binary search trees used for associative searching", "authors": [{"first": "<PERSON>", "middle": [], "last": "Louis", "suffix": ""}, {"first": "Bentley", "middle": [], "last": "", "suffix": ""}], "year": 1975, "venue": "Communications of the ACM", "volume": "18", "issue": "9", "pages": "509--517", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Multidimensional binary search trees used for associative searching. Communi- cations of the ACM, 18(9):509-517, 1975.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Relative Entropy Inverse Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2011, "venue": "International Conference on Artificial Intelligence and Statistics", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Relative Entropy Inverse Reinforcement Learning. In International Conference on Artificial Intelligence and Statistics, 2011.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Decision Transformer: Reinforcement Learning via Sequence Modeling", "authors": [{"first": "<PERSON>i", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Decision Transformer: Reinforcement Learning via Sequence Modeling. ArXiv, abs/2106.01345, 2021.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Adversarially Trained Actor Critic for Offline Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Tengyang", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON>ek<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Proceedings of the 39th International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Adversarially Trained Actor Critic for Offline Reinforcement Learning. In Proceedings of the 39th International Conference on Ma- chine Learning, 2022. URL https://proceedings.mlr.press/v162/cheng22b. html.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Imitation Learning by Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>ek. Imitation Learning by Reinforcement Learning. In International Confer- ence on Learning Representations, 2022. URL https://openreview.net/forum?id= 1zwleytEpYx.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Imitation Learning from Pixel Observations for Continuous Control", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Eugene", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ya<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Imitation Learning from Pixel Observations for Continuous Control, 2022. URL https: //openreview.net/forum?id=JLbXkHkLCG6.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Primal Wasserstein Imitation Learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Primal Wasserstein Imitation Learning. In International Conference on Learning Representations, 2021. URL https://openreview.net/forum?id=TtYSU29zgR.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Magnetic control of tokamak plasmas through deep reinforcement learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Carpanese", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ewalds", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Diego", "middle": [], "last": "De Las", "suffix": ""}, {"first": "", "middle": [], "last": "Casas", "suffix": ""}], "year": 2022, "venue": "Nature", "volume": "602", "issue": "7897", "pages": "414--419", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Mag- netic control of tokamak plasmas through deep reinforcement learning. Nature, 602(7897):414- 419, 2022.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "DITTO: Offline Imitation Learning with World Models", "authors": [{"first": "Branton", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. DITTO: Offline Imitation Learning with World Models. ArXiv, abs/2302.03086, 2023.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "A note on two problems in connexion with graphs", "authors": [{"first": "W", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>jk<PERSON>", "suffix": ""}], "year": 1959, "venue": "Numerische Mathematik", "volume": "1", "issue": "", "pages": "269--271", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>. A note on two problems in connexion with graphs. Numerische Mathematik, 1: 269-271, 1959.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Experiments with the graph traverser program", "authors": [{"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "Proceedings of the Royal Society of London. Series A. Mathematical and Physical Sciences", "volume": "294", "issue": "", "pages": "235--259", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Experiments with the graph traverser program. Proceedings of the Royal Society of London. Series A. Mathematical and Physical Sciences, 294(1437):235-259, 1966.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Heuristic search: theory and applications", "authors": [{"first": "<PERSON>", "middle": [], "last": "Edelkamp", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2011, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Heuristic search: theory and applications. Elsevier, 2011.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "A Coupled Flow Approach to Imitation Learning", "authors": [{"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. A Coupled Flow Approach to Imitation Learning. In International Conference on Machine Learning, 2023.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "D4RL: Datasets for Deep Data-Driven Reinforcement Learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Aviral", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ofir", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. D4RL: Datasets for Deep Data-Driven Reinforcement Learning. ArXiv, abs/2004.07219, 2020.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "A Minimalist Approach to Offline Reinforcement Learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Shi<PERSON><PERSON>", "middle": ["<PERSON>"], "last": "<PERSON>u", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. A Minimalist Approach to Offline Reinforcement Learning. In Advances in Neural Information Processing Systems, 2021.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Off-Policy Deep Reinforcement Learning without Exploration", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Precup", "suffix": ""}], "year": 2019, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. Off-Policy Deep Reinforcement Learning without Exploration. In International Conference on Machine Learning, 2019.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "A Divergence Minimization Perspective on Imitation Learning Methods", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Shi<PERSON><PERSON>", "middle": ["<PERSON>"], "last": "<PERSON>u", "suffix": ""}], "year": 2019, "venue": "Conference on Robot Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. A Divergence Minimization Perspective on Imitation Learning Methods. In Conference on Robot Learning, 2019.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Relay Policy Learning: Solving Long-Horizon Tasks via Imitation and Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. <PERSON><PERSON> Pol- icy Learning: Solving Long-Horizon Tasks via Imitation and Reinforcement Learning. ArXiv, abs/1910.11956, 2019.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "A formal basis for the heuristic determination of minimum cost paths", "authors": [{"first": "<PERSON><PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1968, "venue": "IEEE transactions on Systems Science and Cybernetics", "volume": "4", "issue": "2", "pages": "100--107", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. A formal basis for the heuristic determination of minimum cost paths. IEEE transactions on Systems Science and Cybernetics, 4(2):100-107, 1968.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "FurnitureBench: Reproducible Real-World Benchmark for Long-Horizon Complex Manipulation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Lim", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> FurnitureBench: Reproducible Real-World Benchmark for Long-Horizon Complex Manipulation. ArXiv, abs/2305.12821, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Generative Adversarial Imitation Learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Generative Adversarial Imitation Learning. In Advances in Neural Information Processing Systems, 2016.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "A Research Framework for Distributed Reinforcement Learning", "authors": [{"first": "<PERSON>", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bart<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": ["P"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Tamara", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Fan", "middle": [], "last": "Cass<PERSON>r", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Sergio", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Serkan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Le"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Bilal", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Acme: A Re- search Framework for Distributed Reinforcement Learning. ArXiv, abs/2006.00979, 2020. URL https://api.semanticscholar.org/CorpusID:219176679.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Offline Reinforcement Learning as One Big Sequence Modeling Problem", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Qiyang", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Offline Reinforcement Learning as One Big Se- quence Modeling Problem. In Advances in Neural Information Processing Systems, 2021.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Strictly Batch Imitation Learning by Energybased Distribution Matching", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Bica", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Strictly Batch Imitation Learning by Energy- based Distribution Matching. In Advances in neural information processing systems, 2020.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "A Bayesian Approach to Generative Adversarial Imitation Learning", "authors": [{"first": "<PERSON><PERSON>ok", "middle": [], "last": "Jeon", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. A Bayesian Approach to Generative Adversarial Imitation Learning. In Neural Information Processing Systems, 2018.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Imitation Learning as f-Divergence Minimization", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Gilwoo", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Sanjiban", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["S"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Workshop on the Algorithmic Foundations of Robotics", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Imitation Learning as f-Divergence Minimization. In Workshop on the Algorithmic Foundations of Robotics, 2019.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "MOReL: Model-Based Offline Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Praneeth", "middle": [], "last": "Netrapalli", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. MOReL: Model- Based Offline Reinforcement Learning. In Advances in Neural Information Processing Systems, 2020.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Lobs-DICE: Offline Learning from Observation via Stationary Distribution Correction Estimation", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hongseok", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems, 2022a", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Lobs- DICE: Offline Learning from Observation via Stationary Distribution Correction Estimation. In Advances in Neural Information Processing Systems, 2022a. URL https://openreview. net/forum?id=8U5J6zK_MtV.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "DemoDICE: Offline Imitation Learning with Supplementary Imperfect Demonstrations", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>ok", "middle": [], "last": "Jeon", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Hongseok", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations, 2022b", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. DemoDICE: Offline Imitation Learning with Supplementary Imper- fect Demonstrations. In International Conference on Learning Representations, 2022b. URL https://openreview.net/forum?id=BrPdX1bDZkQ.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Imitation with Neural Density Models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>dal", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "Jiaming", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Imitation with Neural Density Models. In Neural Information Processing Systems, 2020.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Adam: A Method for Stochastic Optimization", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Ba", "suffix": ""}], "year": 2015, "venue": "International Conference on Learning Representation", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>: A Method for Stochastic Optimization. In International Conference on Learning Representation, 2015.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Reinforcement learning in robotics: A survey", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2013, "venue": "The International Journal of Robotics Research", "volume": "32", "issue": "11", "pages": "1238--1274", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON>. Reinforcement learning in robotics: A survey. The International Journal of Robotics Research, 32(11):1238-1274, 2013.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Depth-first iterative-deepening: An optimal admissible tree search", "authors": [{"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1985, "venue": "Artificial intelligence", "volume": "27", "issue": "1", "pages": "97--109", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Depth-first iterative-deepening: An optimal admissible tree search. Artificial intel- ligence, 27(1):97-109, 1985.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Artificial Intelligence Search Algorithms", "authors": [{"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1999, "venue": "Algorithms and Theory of Computation Handbook", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Artificial Intelligence Search Algorithms. In Algorithms and Theory of Computa- tion Handbook, 1999.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Discriminator-Actor-Critic: Addressing Sample Inefficiency and <PERSON><PERSON> in Adversarial Imitation Learning", "authors": [{"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Debidatta", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Discriminator-Actor-Critic: Addressing Sample Inefficiency and Reward <PERSON> in Adversar- ial Imitation Learning. In International Conference on Learning Representations, 2019. URL https://openreview.net/forum?id=Hk4fpoA5Km.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Imitation Learning via Off-Policy Distribution Matching", "authors": [{"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ofir", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Imitation Learning via Off-Policy Distribution Matching. In International Conference on Learning Representations, 2020. URL https:// openreview.net/forum?id=Hyg-JC4FDr.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Offline Reinforcement Learning with Implicit Q-Learning", "authors": [{"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Offline Reinforcement Learning with Implicit Q-Learning. In International Conference on Learning Representations, 2022. URL https: //openreview.net/forum?id=68n2s9ZJWF8.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Conservative Q-Learning for Offline Reinforcement Learning", "authors": [{"first": "Aviral", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Conservative Q-Learning for Offline Reinforcement Learning. In Advances in Neural Information Processing Systems, 2020.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Batch Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "Reinforcement Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Batch Reinforcement Learning. In Rein- forcement Learning, 2012.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "IKEA Furniture Assembly Environment for Long-Horizon Complex Manipulation Tasks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Lim", "suffix": ""}], "year": 2019, "venue": "2021 IEEE International Conference on Robotics and Automation (ICRA)", "volume": "", "issue": "", "pages": "6343--6349", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. IKEA Furniture Assembly Environment for Long-Horizon Complex Manipulation Tasks. 2021 IEEE Interna- tional Conference on Robotics and Automation (ICRA), pp. 6343-6349, 2019.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Adversarial Skill Chaining for Long-Horizon Robot Manipulation via Terminal State Regularization", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "Lim", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Conference on Robot Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Adversarial Skill Chaining for Long-Horizon Robot Manipulation via Terminal State Regularization. In Conference on Robot Learning, 2021.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Offline Reinforcement Learning: Tutorial, Review, and Perspectives on Open Problems", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Offline Reinforcement Learning: Tutorial, Review, and Perspectives on Open Problems. ArXiv, abs/2005.01643, 2020.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "MAHALO: Unifying Offline Reinforcement Learning and Imitation Learning from Observations", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Boots", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. MAHALO: Unifying Offline Reinforcement Learning and Imitation Learning from Observations. In International Conference on Machine Learning, 2023.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "New Algorithms for Efficient High-Dimensional Nonparametric Classification", "authors": [{"first": "<PERSON>g", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2006, "venue": "Journal of Machine Learning Research", "volume": "7", "issue": "6", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. New Algorithms for Efficient High-Dimensional Nonparametric Classification. Journal of Machine Learning Research, 7(6), 2006.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Revisiting Design Choices in Offline Model Based Reinforcement Learning", "authors": [{"first": "<PERSON>g", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>-<PERSON>er", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "International Conference on Learning Representations, 2022a", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Revisit- ing Design Choices in Offline Model Based Reinforcement Learning. In International Confer- ence on Learning Representations, 2022a. URL https://openreview.net/forum?id= zz9hXVhf40.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Challenges and Opportunities in Offline Reinforcement Learning from Visual Observations", "authors": [{"first": "<PERSON>g", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Gj"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>-<PERSON>er", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "Yee", "middle": ["<PERSON>e"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2206.04779"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Challenges and Opportunities in Offline Reinforcement Learning from Visual Observations. arXiv preprint arXiv:2206.04779, 2022b.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Optimal Transport for Offline Imitation Learning", "authors": [{"first": "Yicheng", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Grefenstette", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2023, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Optimal Transport for Offline Imitation Learning. In The Eleventh International Confer- ence on Learning Representations, 2023. URL https://openreview.net/forum?id= MhuFzFsrfvH.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "State Advantage Weighting for Offline RL", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Le", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zongqing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Li", "suffix": ""}], "year": null, "venue": "3rd Offline RL Workshop: Offline RL as a \"Launchpad", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. State Advantage Weighting for Offline RL. In 3rd Offline RL Workshop: Offline RL as a \"Launchpad\", 2022a. URL https: //openreview.net/forum?id=2rOD_UQfvl.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Double Check Your State Before Trusting It: Confidence-Aware Bidirectional Offline Model-Based Imagination", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Zongqing", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems, 2022b", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Double Check Your State Before Trusting It: Confidence- Aware Bidirectional Offline Model-Based Imagination. In Advances in Neural Information Pro- cessing Systems, 2022b. URL https://openreview.net/forum?id=3e3IQMLDSLP.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Mildly Conservative Q-Learning for Offline Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Zongqing", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "Advances in Neural Information Processing Systems, 2022c", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Mildly Conservative Q-Learning for Offline Reinforcement Learning. In Advances in Neural Information Processing Systems, 2022c. URL https://openreview.net/forum?id=VYYf6S67pQc.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Versatile Offline Imitation from Observations and Examples via Regularized State-Occupancy Matching", "authors": [{"first": "<PERSON>", "middle": [], "last": "Yecheng", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Ba<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Versatile Offline Imita- tion from Observations and Examples via Regularized State-Occupancy Matching. In Interna- tional Conference on Machine Learning, 2022.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Efficient and robust approximate nearest neighbor search using hierarchical navigable small world graphs", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dmitry", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "IEEE transactions on pattern analysis and machine intelligence", "volume": "42", "issue": "4", "pages": "824--836", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Efficient and robust approximate nearest neighbor search using hierarchical navigable small world graphs. IEEE transactions on pattern analysis and machine intelligence, 42(4):824-836, 2018.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Human-level Control through Deep Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>ni<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Kavukcuoglu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Silver", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON><PERSON><PERSON>"], "last": "Fidjeland", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ostrovski", "suffix": ""}, {"first": "Stig", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "King", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "De<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Nature", "volume": "518", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Human-level Control through Deep Reinforcement Learning. Nature, 518:529-533, 2015. URL https://api.semanticscholar.org/ CorpusID:205242740.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "DualDICE: Behavior-Agnostic Estimation of Discounted Stationary Distribution Corrections", "authors": [{"first": "Ofir", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Yinlam", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Dai", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}], "year": 2019, "venue": "Advances in neural information processing systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. DualDICE: Behavior-Agnostic Estimation of Discounted Stationary Distribution Corrections. In Advances in neural information processing systems, 2019.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Five balltree construction algorithms", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1989, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Five balltree construction algorithms. Technical report, International Com- puter Science Institute, 1989.", "links": null}, "BIBREF58": {"ref_id": "b58", "title": "The surprising effectiveness of representation learning for visual imitation", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "(", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": ")", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Pi<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (<PERSON><PERSON>) <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. The surprising effectiveness of representation learning for visual imitation. ArXiv, abs/2112.01511, 2021.", "links": null}, "BIBREF59": {"ref_id": "b59", "title": "PyTorch: An Imperative Style, High-Performance Deep Learning Library", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "Gross", "suffix": ""}, {"first": "Francisco", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Natalia", "middle": [], "last": "Gimelshein", "suffix": ""}, {"first": "Luca", "middle": [], "last": "Antiga", "suffix": ""}, {"first": "Alban", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>son", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Soumith", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. PyTorch: An Imperative Style, High-Performance Deep Learning Library. In Neural Information Processing Systems, 2019.", "links": null}, "BIBREF60": {"ref_id": "b60", "title": "Scikit-learn: Machine learning in Python", "authors": [{"first": "F", "middle": [], "last": "Pedregosa", "suffix": ""}, {"first": "G", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Gramfort", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "B", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "O", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "Blondel", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "Dubourg", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON>p<PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Passos", "suffix": ""}, {"first": "D", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": [], "last": "Duchesnay", "suffix": ""}], "year": 2011, "venue": "Journal of Machine Learning Research", "volume": "12", "issue": "", "pages": "2825--2830", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Scikit-learn: Machine learning in Python. Journal of Machine Learning Research, 12:2825-2830, 2011.", "links": null}, "BIBREF61": {"ref_id": "b61", "title": "Heuristic search viewed as path finding in a graph", "authors": [{"first": "Ira", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1970, "venue": "Artificial intelligence", "volume": "1", "issue": "3-4", "pages": "193--204", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Heuristic search viewed as path finding in a graph. Artificial intelligence, 1(3-4):193-204, 1970.", "links": null}, "BIBREF62": {"ref_id": "b62", "title": "Alvinn: An autonomous land vehicle in a neural network", "authors": [{"first": "A", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Pomerleau", "suffix": ""}], "year": 1988, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Alvinn: An autonomous land vehicle in a neural network. In Advances in Neural Information Processing Systems, 1988.", "links": null}, "BIBREF63": {"ref_id": "b63", "title": "Policy Regularization with Dataset Constraint for Offline Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zongzhang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Policy Regularization with Dataset Constraint for Offline Reinforcement Learning. In International Conference on Machine Learning, 2023.", "links": null}, "BIBREF64": {"ref_id": "b64", "title": "{SQIL}: Imitation Learning via Reinforcement Learning with Sparse Rewards", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Anca", "middle": ["D"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. {SQIL}: Imitation Learning via Reinforce- ment Learning with Sparse Rewards. In International Conference on Learning Representations, 2020. URL https://openreview.net/forum?id=S1xKd24twB.", "links": null}, "BIBREF65": {"ref_id": "b65", "title": "RAMBO-RL: Robust Adversarial Model-Based Offline Reinforcement Learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. RAMBO-RL: Robust Adversarial Model-Based Offline Reinforcement Learning. In Advances in Neural Information Processing Systems, 2022. URL https://openreview.net/forum?id=nrksGSRT7kX.", "links": null}, "BIBREF66": {"ref_id": "b66", "title": "A Reduction of Imitation Learning and Structured Prediction to No-Regret Online Learning", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": ["<PERSON>"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2011, "venue": "International Conference on Artificial Intelligence and Statistics", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. A Reduction of Imitation Learning and Structured Prediction to No-Regret Online Learning. In International Conference on Artificial Intelligence and Statistics, 2011.", "links": null}, "BIBREF67": {"ref_id": "b67", "title": "Mastering atari, go, chess and shogi by planning with a learned model", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>fre", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "De<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Graepel", "suffix": ""}], "year": 2020, "venue": "Nature", "volume": "588", "issue": "7839", "pages": "604--609", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Mastering atari, go, chess and shogi by planning with a learned model. Nature, 588(7839):604-609, 2020.", "links": null}, "BIBREF68": {"ref_id": "b68", "title": "An analysis of consecutively bounded depth-first search with applications in automated deduction", "authors": [{"first": "E", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1985, "venue": "IJCAI", "volume": "", "issue": "", "pages": "1073--1075", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. An analysis of consecutively bounded depth-first search with applications in automated deduction. In IJCAI, pp. 1073-1075, 1985.", "links": null}, "BIBREF69": {"ref_id": "b69", "title": "Reinforcement learning: An introduction", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Reinforcement learning: An introduction. MIT press, 2018.", "links": null}, "BIBREF70": {"ref_id": "b70", "title": "Pruning Duplicate Nodes in Depth-First Search", "authors": [{"first": "<PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1993, "venue": "AAAI Conference on Artificial Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Pruning Duplicate Nodes in Depth-First Search. In AAAI Conference on Artificial Intelligence, 1993.", "links": null}, "BIBREF71": {"ref_id": "b71", "title": "MuJoCo: A Physics Engine for Model-based Control", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Tassa", "suffix": ""}], "year": 2012, "venue": "IEEE/RSJ International Conference on Intelligent Robots and Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. MuJoCo: A Physics Engine for Model-based Con- trol. IEEE/RSJ International Conference on Intelligent Robots and Systems, 2012.", "links": null}, "BIBREF72": {"ref_id": "b72", "title": "Recent Advances in Imitation Learning from Observation", "authors": [{"first": "Faraz", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stone", "suffix": ""}], "year": 2019, "venue": "International Joint Conference on Artificial Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON>. Recent Advances in Imitation Learning from Obser- vation. In International Joint Conference on Artificial Intelligence, 2019.", "links": null}, "BIBREF73": {"ref_id": "b73", "title": "Pessimistic Model-based Offline Reinforcement Learning under Partial Coverage", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}], "year": 2022, "venue": "International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. Pessimistic Model-based Offline Reinforcement Learning under Partial Coverage. In International Conference on Learning Representations, 2022. URL https: //openreview.net/forum?id=tyrJsbKAe6.", "links": null}, "BIBREF74": {"ref_id": "b74", "title": "<PERSON>, <PERSON>, and SciPy 1.0 Contributors. SciPy 1.0: Fundamental Algorithms for Scientific Computing in Python", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gommers", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Haberland", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "İlhan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Polat", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>p<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "E", "middle": ["A"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["H"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Ribeiro", "suffix": ""}], "year": 2020, "venue": "", "volume": "17", "issue": "", "pages": "261--272", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and SciPy 1.0 Contributors. SciPy 1.0: Fundamental Algorithms for Scientific Computing in Python. Nature Methods, 17:261-272, 2020.", "links": null}, "BIBREF75": {"ref_id": "b75", "title": "Support-weighted Adversarial Imitation Learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Carlo", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Support-weighted Adver- sarial Imitation Learning. ArXiv, abs/2002.08803, 2020.", "links": null}, "BIBREF76": {"ref_id": "b76", "title": "Discriminator-Weighted Offline Imitation Learning from Suboptimal Demonstrations", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xianyuan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Huiling", "middle": [], "last": "Qin", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Discriminator-Weighted Offline Imita- tion Learning from Suboptimal Demonstrations. In International Conference on Machine Learn- ing, 2022.", "links": null}, "BIBREF77": {"ref_id": "b77", "title": "Exploration and anti-exploration with distributional random network distillation", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Tao", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}], "year": 2024, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Exploration and anti-exploration with distributional random network distillation. ArXiv, abs/2401.09750, 2024.", "links": null}, "BIBREF78": {"ref_id": "b78", "title": "Offline Imitation Learning with Suboptimal Demonstrations via Relaxed Distribution Matching", "authors": [{"first": "Lantao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jiaming", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "AAAI Conference on Artificial Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Offline Imitation Learning with Suboptimal Demonstrations via Relaxed Distribution Matching. In AAAI Confer- ence on Artificial Intelligence, 2023.", "links": null}, "BIBREF79": {"ref_id": "b79", "title": "MOPO: Model-based Offline Policy Optimization", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Lantao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Y"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chelsea", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Tengyu", "middle": [], "last": "Ma", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. MOPO: Model-based Offline Policy Optimization. In Advances in Neural Information Processing Systems, 2020.", "links": null}, "BIBREF80": {"ref_id": "b80", "title": "COMBO: Conservative Offline Model-Based Policy Optimization", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Aviral", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chelsea", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. COMBO: Conservative Offline Model-Based Policy Optimization. In Advances in Neural Infor- mation Processing Systems, 2021.", "links": null}, "BIBREF81": {"ref_id": "b81", "title": "How to Leverage Unlabeled Data in Offline Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Aviral", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Chebotar", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Chelsea", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. How to Leverage Unlabeled Data in Offline Reinforcement Learning. In International Conference on Machine Learning, 2022.", "links": null}, "BIBREF82": {"ref_id": "b82", "title": "CLARE: Conservative Model-Based Reward Learning for Offline Inverse Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Guanbo", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ren", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. CLARE: Conservative Model-Based Reward Learning for Offline Inverse Reinforcement Learn- ing. In The Eleventh International Conference on Learning Representations, 2023. URL https://openreview.net/forum?id=5aT4ganOd98.", "links": null}, "BIBREF83": {"ref_id": "b83", "title": "Uncertainty-driven Trajectory Truncation for Model-based Offline Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Jiangpeng", "middle": [], "last": "Yan", "suffix": ""}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Le", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Uncertainty-driven Trajectory Truncation for Model-based Offline Reinforcement Learning. ArXiv, abs/2304.04660, 2023.", "links": null}, "BIBREF84": {"ref_id": "b84", "title": "PLAS: Latent Action Space for Offline Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Bajracharya", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Held", "suffix": ""}], "year": 2020, "venue": "Conference on Robot Learning", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. PLAS: Latent Action Space for Offline Rein- forcement Learning. In Conference on Robot Learning, 2020.", "links": null}, "BIBREF85": {"ref_id": "b85", "title": "Off-Policy Imitation Learning from Observations", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Dai", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Off-Policy Imitation Learning from Obser- vations. In Neural Information Processing Systems, 2021.", "links": null}, "BIBREF86": {"ref_id": "b86", "title": "Maximum Entropy Inverse Reinforcement Learning", "authors": [{"first": "<PERSON>", "middle": ["D"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "J", "middle": ["<PERSON>"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["K"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2008, "venue": "AAAI Conference on Artificial Intelligence", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Maximum Entropy Inverse Reinforcement Learning. In AAAI Conference on Artificial Intelligence, 2008.", "links": null}, "BIBREF87": {"ref_id": "b87", "title": "Offline Learning from Demonstrations and Unlabeled Experience", "authors": [{"first": "<PERSON>", "middle": [], "last": "Zolna", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ksen<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Offline Learning from Demonstrations and Unlabeled Experience. ArXiv, abs/2011.13885, 2020.", "links": null}}, "ref_entries": {"FIGREF0": {"uris": null, "fig_num": "1", "text": "Figure1: Left: The key idea behind SEABO. We assign larger rewards to transitions that are closer to the expert demonstration, and smaller rewards otherwise. The dotted lines connect the query samples with their nearest neighbors along the demonstration. Right: Illustration of the SEABO framework. Given an expert demonstration, we first construct a KD-tree and then feed the unlabeled samples into the tree to query their nearest neighbors. We use the resulting distance to calculate the reward label. Then one can adopt any existing offline RL algorithm to train on the labeled dataset.", "type_str": "figure", "num": null}, "FIGREF1": {"uris": null, "fig_num": "2", "text": "Figure 2: Density plots of ground-truth rewards and rewards acquired by SEABO. Note that oracle indicates the ground-truth rewards are plotted.", "type_str": "figure", "num": null}, "FIGREF2": {"uris": null, "fig_num": "3", "text": "Figure 3: Parameter study on the reward scale. The shaded region denotes the standard deviation.", "type_str": "figure", "num": null}, "FIGREF3": {"uris": null, "fig_num": "4", "text": "Figure 4: Parameter study of (a) weighting coefficient β, (b) number of neighbors N . The shaded region captures the standard deviation.", "type_str": "figure", "num": null}, "FIGREF4": {"uris": null, "fig_num": "56", "text": "Figure 5: Additional experiments on the influence of α. The shaded region captures the standard deviation. All other hyperparameters are kept unchanged except α.", "type_str": "figure", "num": null}, "FIGREF5": {"uris": null, "fig_num": "7", "text": "Figure 7: Additional experiments on examining the influence of the number of neighbors in KD-tree. The shaded region represents the standard deviation.", "type_str": "figure", "num": null}, "FIGREF6": {"uris": null, "fig_num": "891011", "text": "Figure 8: Full learning curves of IQL+SEABO on D4RL MuJoCo datasets. We plot the average performance and the shaded region captures the standard deviation.", "type_str": "figure", "num": null}, "FIGREF7": {"uris": null, "fig_num": "13", "text": "Figure 13: Density plot comparison of ground-truth rewards and rewards acquired by different search algorithms. The results are on selected datasets from Adroit and AntMaze tasks.", "type_str": "figure", "num": null}, "TABREF1": {"text": "Results of SEABO upon different base algorithms. µ max denotes the normalized return of the highest return trajectory in the specific dataset, IQL and TD3 BC indicate that they are trained upon the ground-truth reward labels, while +SEABO indicates the algorithm is trained on the reward signals provided by SEABO. The normalized average scores at the final 10 episodes of evaluations are reported, along with standard deviations. We bold the mean score and highlight the cell if SEABO outperforms algorithms trained on ground-truth rewards.", "html": null, "content": "<table><tr><td>Task Name</td><td>µmax</td><td>IQL</td><td colspan=\"2\">IQL+SEABO TD3 BC</td><td>TD3 BC+SEABO</td></tr><tr><td>halfcheetah-medium</td><td>45.0</td><td>47.4±0.2</td><td>44.8±0.3</td><td>48.0±0.7</td><td>45.9±0.3</td></tr><tr><td>hopper-medium</td><td>99.5</td><td>66.2±5.7</td><td>80.9±3.2</td><td>60.7±12.5</td><td>76.1±4.2</td></tr><tr><td>walker2d-medium</td><td>92.0</td><td>78.3±8.7</td><td>80.9±0.6</td><td>83.7±5.3</td><td>76.6±0.4</td></tr><tr><td colspan=\"2\">halfcheetah-medium-replay 42.4</td><td>44.2±1.2</td><td>42.3±0.1</td><td>44.4±0.8</td><td>43.0±0.4</td></tr><tr><td>hopper-medium-replay</td><td>98.6</td><td>94.7±8.6</td><td>92.7±2.9</td><td>64.8±25.5</td><td>96.3±3.0</td></tr><tr><td>walker2d-medium-replay</td><td>89.9</td><td>73.8±7.1</td><td>74.0±2.7</td><td>87.4±8.4</td><td>73.1±2.2</td></tr><tr><td colspan=\"2\">halfcheetah-medium-expert 92.8</td><td>86.7±5.3</td><td>89.3±2.5</td><td>93.5±2.0</td><td>95.7±0.4</td></tr><tr><td>hopper-medium-expert</td><td colspan=\"3\">116.0 91.5±14.3 97.5±5.8</td><td colspan=\"2\">100.2±20.0 107.1±3.3</td></tr><tr><td>walker2d-medium-expert</td><td colspan=\"3\">109.0 109.6±1.0 110.9±0.2</td><td>109.5±0.5</td><td>109.7±0.2</td></tr><tr><td>Total Score</td><td colspan=\"2\">785.2 692.4</td><td>713.3</td><td>692.3</td><td>723.5</td></tr></table>", "type_str": "table", "num": null}, "TABREF2": {"text": "Comparison of SEABO against some recent baselines. We report the mean normalized scores and the corresponding standard deviations. We bold and highlight the mean score cell if it is close to or beats IQL trained on the raw rewards.", "html": null, "content": "<table><tr><td>Task Name</td><td>BC</td><td colspan=\"2\">10%BC IQL</td><td colspan=\"4\">IQL+ORIL IQL+UDS IQL+OTR IQL+SEABO</td></tr><tr><td>halfcheetah-medium</td><td>42.6</td><td>42.5</td><td>47.4±0.2</td><td>49.0±0.2</td><td>42.4±0.3</td><td>43.2±0.2</td><td>44.8±0.3</td></tr><tr><td>hopper-medium</td><td>52.9</td><td>56.9</td><td>66.2±5.7</td><td>47.0±4.0</td><td>54.5±3.0</td><td>74.2±5.1</td><td>80.9±3.2</td></tr><tr><td>walker2d-medium</td><td>75.3</td><td>75.0</td><td>78.3±8.7</td><td>61.9±6.6</td><td>68.9±6.2</td><td>78.7±2.2</td><td>80.9±0.6</td></tr><tr><td colspan=\"2\">halfcheetah-medium-replay 36.6</td><td>40.6</td><td>44.2±1.2</td><td>44.1±0.6</td><td>37.9±2.4</td><td>41.8±0.3</td><td>42.3±0.1</td></tr><tr><td>hopper-medium-replay</td><td>18.1</td><td>75.9</td><td>94.7±8.6</td><td>82.4±1.7</td><td colspan=\"2\">49.3±22.7 85.4±0.8</td><td>92.7±2.9</td></tr><tr><td>walker2d-medium-replay</td><td>26.0</td><td>62.5</td><td>73.8±7.1</td><td>76.3±4.9</td><td>17.7±9.6</td><td>67.2±6.0</td><td>74.0±2.7</td></tr><tr><td colspan=\"2\">halfcheetah-medium-expert 55.2</td><td>92.9</td><td>86.7±5.3</td><td>87.5±3.9</td><td>63.0±5.7</td><td>87.4±4.4</td><td>89.3±2.5</td></tr><tr><td>hopper-medium-expert</td><td>52.5</td><td>110.9</td><td colspan=\"2\">91.5±14.3 29.7±22.2</td><td>53.9±2.5</td><td colspan=\"2\">88.4±12.6 97.5±5.8</td></tr><tr><td>walker2d-medium-expert</td><td colspan=\"2\">107.5 109.0</td><td colspan=\"2\">109.6±1.0 110.6±0.6</td><td colspan=\"3\">107.5±1.7 109.5±0.3 110.9±0.2</td></tr><tr><td>Total Score</td><td colspan=\"2\">466.7 666.2</td><td>692.4</td><td>588.5</td><td>495.1</td><td>675.8</td><td>713.3</td></tr></table>", "type_str": "table", "num": null}, "TABREF3": {"text": "Experimental results on the AntMaze-v0 and Adroit-v0 domains. SEABO and OTR use IQL as the base algorithm. IQL denotes that IQL uses the ground-truth reward for policy learning. We report the mean normalized scores and the corresponding standard deviations. We bold and highlight the best mean score cell.", "html": null, "content": "<table><tr><td/><td/><td/><td/><td>Task Name</td><td>IQL</td><td>IQL+OTR</td><td>IQL+SEABO</td></tr><tr><td>Task Name</td><td>IQL</td><td>IQL+OTR</td><td>IQL+SEABO</td><td>pen-human</td><td>70.7±8.6</td><td>66.8±21.2</td><td>94.3±12.0</td></tr><tr><td>umaze</td><td>87.5±2.6</td><td>83.4±3.3</td><td>90.0±1.8</td><td>pen-cloned</td><td>37.2±7.3</td><td>46.9±20.9</td><td>48.7±15.3</td></tr><tr><td>umaze-diverse</td><td>62.2±13.8</td><td>68.9±13.6</td><td>66.2±7.2</td><td>door-human</td><td>3.3±1.3</td><td>5.9±2.7</td><td>5.1±2.0</td></tr><tr><td>medium-diverse</td><td>70.0±10.9</td><td>70.4±4.8</td><td>72.2±4.1</td><td>door-cloned</td><td>1.6±0.5</td><td>0.0±0.0</td><td>0.4±0.8</td></tr><tr><td>medium-play</td><td>71.2±7.3</td><td>70.5±6.6</td><td>71.6±5.4</td><td>relocate-human</td><td>0.1±0.0</td><td>0.1±0.1</td><td>0.4±0.5</td></tr><tr><td>large-diverse</td><td>47.5±9.5</td><td>45.5±6.2</td><td>50.0±6.8</td><td>relocate-cloned</td><td>-0.2±0.0</td><td>-0.2±0.0</td><td>-0.2±0.0</td></tr><tr><td>large-play</td><td>39.6±5.8</td><td>45.3±6.9</td><td>50.8±8.7</td><td>hammer-human</td><td>1.6±0.6</td><td>1.8±1.4</td><td>2.7±1.8</td></tr><tr><td>Total Score</td><td>378.0</td><td>384.0</td><td>400.8</td><td>hammer-cloned</td><td>2.1±1.0</td><td>0.9±0.3</td><td>2.2±0.8</td></tr><tr><td/><td/><td/><td/><td>Total Score</td><td>116.4</td><td>122.2</td><td>153.6</td></tr></table>", "type_str": "table", "num": null}, "TABREF4": {"text": "Comparison of SEABO against imitation learning algorithms. We use IQL as the base algorithm for SEABO and PWIL. PWIL-action means that we concatenate state and action to compute rewards in PWIL. We report the mean performance at the final 10 episodes of evaluation for each algorithm, ± captures the standard deviation. We highlight the best mean score cell.", "html": null, "content": "<table><tr><td>Task Name</td><td>SQIL</td><td colspan=\"4\">DemoDICE SMODICE PWIL-action SEABO</td></tr><tr><td>halfcheetah-medium</td><td>31.3±1.8</td><td>42.5±1.7</td><td>41.7±1.0</td><td>44.4±0.2</td><td>44.8±0.3</td></tr><tr><td>hopper-medium</td><td colspan=\"2\">44.7±20.1 55.1±3.3</td><td>56.3±2.3</td><td>60.4±1.8</td><td>80.9±3.2</td></tr><tr><td>walker2d-medium</td><td>59.6±7.5</td><td>73.4±2.6</td><td>13.3±9.2</td><td>72.6±6.3</td><td>80.9±0.6</td></tr><tr><td colspan=\"2\">halfcheetah-medium-replay 29.3±2.2</td><td>38.1±2.7</td><td>38.7±2.4</td><td>42.6±0.5</td><td>42.3±0.1</td></tr><tr><td>hopper-medium-replay</td><td colspan=\"2\">45.2±23.1 39.0±15.4</td><td>44.3±19.7</td><td>94.0±7.0</td><td>92.7±2.9</td></tr><tr><td>walker2d-medium-replay</td><td colspan=\"2\">36.3±13.2 52.2±13.1</td><td>44.6±23.4</td><td>41.9±6.0</td><td>74.0±2.7</td></tr><tr><td colspan=\"2\">halfcheetah-medium-expert 40.1±6.4</td><td>85.8±5.7</td><td>87.9±5.8</td><td>89.5±3.6</td><td>89.3±2.5</td></tr><tr><td>hopper-medium-expert</td><td>49.8±5.8</td><td>92.3±14.2</td><td>76.0±8.6</td><td>70.9±35.1</td><td>97.5±5.8</td></tr><tr><td>walker2d-medium-expert</td><td colspan=\"2\">35.9±22.2 106.9±1.9</td><td>47.8±31.1</td><td>109.8±0.2</td><td>110.9±0.2</td></tr><tr><td>Total Score</td><td>372.2</td><td>585.3</td><td>450.6</td><td>626.1</td><td>713.3</td></tr></table>", "type_str": "table", "num": null}, "TABREF5": {"text": "Experimental results on the state-only regime. SEABO, PWIL, and OTR utilize IQL as the base offline RL algorithm. PWIL-state denotes that PWIL only uses observations to compute rewards. The results are averaged over the final 10 evaluations, and ± captures the standard deviation. We highlight the cell with the best mean performance.", "html": null, "content": "<table><tr><td>Task Name</td><td colspan=\"4\">SMODICE LobsDICE PWIL-state OTR</td><td>SEABO</td></tr><tr><td>halfcheetah-medium</td><td>41.1±2.1</td><td>41.5±1.8</td><td>0.1±0.6</td><td>43.3±0.2</td><td>45.0±0.2</td></tr><tr><td>hopper-medium</td><td>56.5±1.8</td><td>56.9±1.4</td><td>1.4±0.5</td><td>78.7±5.5</td><td>74.7±5.2</td></tr><tr><td>walker2d-medium</td><td>15.5±18.6</td><td>69.3±5.4</td><td>0.2±0.2</td><td>79.4±1.4</td><td>81.3±1.3</td></tr><tr><td colspan=\"2\">halfcheetah-medium-replay 39.2±3.1</td><td>39.9±3.1</td><td>-2.4±0.2</td><td>41.3±0.6</td><td>42.4±0.6</td></tr><tr><td>hopper-medium-replay</td><td>55.3±21.4</td><td colspan=\"2\">41.6±16.8 0.7±0.2</td><td>84.8±2.6</td><td>88.0±0.7</td></tr><tr><td>walker2d-medium-replay</td><td>37.8±10.2</td><td>33.2±7.0</td><td>-0.2±0.2</td><td>66.0±6.7</td><td>76.4±3.0</td></tr><tr><td colspan=\"2\">halfcheetah-medium-expert 88.0±4.0</td><td>89.4±3.2</td><td>0.0±1.0</td><td>89.6±3.0</td><td>91.8±1.5</td></tr><tr><td>hopper-medium-expert</td><td>75.1±11.7</td><td>53.4±3.2</td><td>2.7±2.1</td><td colspan=\"2\">93.2±20.6 97.5±6.4</td></tr><tr><td>walker2d-medium-expert</td><td>32.3±14.7</td><td colspan=\"2\">106.6±2.7 0.2±0.3</td><td colspan=\"2\">109.3±0.8 110.5±0.3</td></tr><tr><td>Total Score</td><td>440.8</td><td>531.8</td><td>2.7</td><td>685.6</td><td>707.6</td></tr></table>", "type_str": "table", "num": null}, "TABREF6": {"text": "Comparison of different choices of search algorithms in SEABO. We report the mean normalized scores with standard deviations. We highlight the best mean score cell except for IQL. The results in Table5suggest that SEABO outperforms other methods on 8 out of 9 tasks, achieving a total score of 707.6, while LobsDICE and OTR only have a total score of 531.8 and 685.6, respectively. It indicates that SEABO can work quite well regardless of whether the expert demonstrations contain actions, further demonstrating the advantages of SEABO. Note that the failure of PWIL in state-only regimes is also reported in<PERSON><PERSON> et al. (2023).", "html": null, "content": "<table><tr><td>Task Name</td><td>IQL</td><td colspan=\"3\">SEABO (KD-tree) SEABO (Ball-tree) SEABO (HNSW)</td></tr><tr><td>halfcheetah-medium</td><td>47.4±0.2</td><td>44.8±0.3</td><td>44.9±0.3</td><td>42.1±0.6</td></tr><tr><td>hopper-medium</td><td>66.2±5.7</td><td>80.9±3.2</td><td>80.7±3.7</td><td>47.2±2.9</td></tr><tr><td>walker2d-medium</td><td>78.3±8.7</td><td>80.9±0.6</td><td>80.8±0.6</td><td>30.7±19.9</td></tr><tr><td colspan=\"2\">halfcheetah-medium-replay 44.2±1.2</td><td>42.3±0.1</td><td>42.5±0.3</td><td>26.9±4.2</td></tr><tr><td>hopper-medium-replay</td><td>94.7±8.6</td><td>92.7±2.9</td><td>92.1±2.3</td><td>25.8±7.5</td></tr><tr><td>walker2d-medium-replay</td><td>73.8±7.1</td><td>74.0±2.7</td><td>74.3±2.0</td><td>29.1±10.1</td></tr><tr><td colspan=\"2\">halfcheetah-medium-expert 86.7±5.3</td><td>89.3±2.5</td><td>89.2±2.4</td><td>34.5±2.2</td></tr><tr><td>hopper-medium-expert</td><td colspan=\"2\">91.5±14.3 97.5±5.8</td><td>96.7±6.2</td><td>41.5±7.7</td></tr><tr><td>walker2d-medium-expert</td><td colspan=\"2\">109.6±1.0 110.9±0.2</td><td>110.9±0.1</td><td>108.6±0.8</td></tr><tr><td>Total Score</td><td>692.4</td><td>713.3</td><td>712.1</td><td>386.4</td></tr><tr><td colspan=\"3\">tions and single expert demonstration. 5.4 COMPARISON OF DIFFERENT SEARCH ALGORITHMS</td><td/><td/></tr><tr><td colspan=\"5\">The most critical component in SEABO is the nearest neighbor search algorithm. It is interesting to</td></tr><tr><td colspan=\"5\">check how SEABO performs under different search algorithms. To that end, we build SEABO on top</td></tr><tr><td colspan=\"5\">of Ball-tree (Omohundro, 1989; Liu et al., 2006), and HNSW (Hierarchical Navigable Small World</td></tr><tr><td colspan=\"5\">graphs, Malkov &amp; Yashunin (2018)). These are widely applied nearest neighbor algorithms, where</td></tr><tr><td colspan=\"5\">Ball-tree partitions regions via hyper-spheres and HNSW is a fully graph-based search structure.</td></tr></table>", "type_str": "table", "num": null}, "TABREF7": {"text": "Comparison of SEABO against baseline algorithms under different amounts of expert demonstrations. We report the aggregate performances and bold the best one.", "html": null, "content": "<table><tr><td># demo</td><td colspan=\"6\">DemoDICE IQL+ORIL IQL+UDS IQL+OTR IQL+PWIL IQL+SEABO</td></tr><tr><td>K = 1</td><td>585.3</td><td>588.5</td><td>495.1</td><td>685.6</td><td>626.1</td><td>713.3</td></tr><tr><td colspan=\"2\">K = 10 589.3</td><td>618.3</td><td>575.8</td><td>694.2</td><td>638.0</td><td>716.1</td></tr></table>", "type_str": "table", "num": null}, "TABREF8": {"text": "Hyperparameter setup of SEABO on locomotion tasks, with IQL and TD3 BC as the base offline RL algorithms.", "html": null, "content": "<table><tr><td/><td>Hyperparameter</td><td>Value</td></tr><tr><td colspan=\"2\">Shared Configurations Hidden layers</td><td>(256, 256)</td></tr><tr><td/><td>Discount factor</td><td>0.99</td></tr><tr><td/><td>Actor learning rate</td><td>3 × 10 -4</td></tr><tr><td/><td>Critic learning rate</td><td>3 × 10 -4</td></tr><tr><td/><td>Batch size</td><td>256</td></tr><tr><td/><td>Optimizer</td><td>Adam (Kingma &amp; Ba, 2015)</td></tr><tr><td/><td>Target update rate</td><td>5 × 10 -3</td></tr><tr><td/><td>Activation function</td><td>ReLU</td></tr><tr><td>IQL</td><td>Value learning rate</td><td>3 × 10 -4</td></tr><tr><td/><td>Temperature</td><td>3.0</td></tr><tr><td/><td>Expectile</td><td>0.7</td></tr><tr><td>TD3 BC</td><td>Policy noise</td><td>0.2</td></tr><tr><td/><td>Policy noise clipping</td><td>(-0.5, 0.5)</td></tr><tr><td/><td>Policy update frequency</td><td>2</td></tr><tr><td/><td>Normalization weight</td><td>2.5</td></tr><tr><td>SEABO</td><td>Squashing function</td><td>r = exp(-0.5×d |A| )</td></tr><tr><td/><td>Distance measurement</td><td>Euclidean distance</td></tr><tr><td/><td>Number of neighbors</td><td>1</td></tr><tr><td/><td colspan=\"2\">Number of expert demonstrations 1</td></tr></table>", "type_str": "table", "num": null}, "TABREF9": {"text": "Hyperparameter setup of SEABO on AntMaze tasks, with IQL as the base offline RL algorithm.", "html": null, "content": "<table><tr><td/><td>Hyperparameter</td><td>Value</td></tr><tr><td>IQL</td><td>Temperature</td><td>10.0</td></tr><tr><td/><td>Expectile</td><td>0.9</td></tr><tr><td colspan=\"3\">SEABO Squashing function r = exp(-5×d |A| ) -1</td></tr></table>", "type_str": "table", "num": null}, "TABREF10": {"text": "Hyperparameter setup of SEABO on Adroit tasks, with IQL as the base offline RL algorithm.", "html": null, "content": "<table><tr><td/><td>Hyperparameter</td><td>Value</td></tr><tr><td>IQL</td><td>Temperature</td><td>0.5</td></tr><tr><td/><td>Expectile</td><td>0.7</td></tr><tr><td/><td>Actor dropout rate</td><td>0.1</td></tr><tr><td colspan=\"3\">SEABO Squashing function r = exp(-0.5 × d)</td></tr></table>", "type_str": "table", "num": null}, "TABREF12": {"text": "Experimental results of SEABO on the AntMaze-v0 and Adroit-v0 domains with 10 expert demonstrations. SEABO and OTR use IQL as the base algorithm. The average normalized scores along with the corresponding standard deviations are reported. We bold and highlight the best mean score cell.", "html": null, "content": "<table><tr><td/><td/><td/><td/><td>Task Name</td><td>IQL</td><td>IQL+OTR</td><td>IQL+SEABO</td></tr><tr><td>Task Name</td><td>IQL</td><td>IQL+OTR</td><td>IQL+SEABO</td><td>pen-human</td><td>70.7±8.6</td><td>69.4±21.5</td><td>85.8±16.1</td></tr><tr><td>umaze</td><td>87.5±2.6</td><td>88.7±3.5</td><td>87.6±2.0</td><td>pen-cloned</td><td>37.2±7.3</td><td>42.7±25.0</td><td>49.2±12.2</td></tr><tr><td>umaze-diverse</td><td>62.2±13.8</td><td>64.4±18.2</td><td>70.0±9.5</td><td>door-human</td><td>3.3±1.3</td><td>4.2±2.1</td><td>6.8±5.6</td></tr><tr><td>medium-diverse</td><td>70.0±10.9</td><td>70.5±6.9</td><td>70.2±5.4</td><td>door-cloned</td><td>1.6±0.5</td><td>0.0±0.0</td><td>0.1±0.1</td></tr><tr><td>medium-play</td><td>71.2±7.3</td><td>72.7±6.2</td><td>72.8±1.6</td><td>relocate-human</td><td>0.1±0.0</td><td>0.1±0.1</td><td>0.1±0.1</td></tr><tr><td>large-diverse</td><td>47.5±9.5</td><td>50.7±6.9</td><td>50.0±7.9</td><td>relocate-cloned</td><td>-0.2±0.0</td><td>-0.2±0.0</td><td>-0.2±0.0</td></tr><tr><td>large-play</td><td>39.6±5.8</td><td>51.2±7.1</td><td>48.6±9.8</td><td>hammer-human</td><td>1.6±0.6</td><td>1.4±0.2</td><td>1.7±0.3</td></tr><tr><td>Total Score</td><td>378.0</td><td>398.2</td><td>399.2</td><td>hammer-cloned</td><td>2.1±1.0</td><td>1.3±0.7</td><td>1.7±0.5</td></tr><tr><td/><td/><td/><td/><td>Total Score</td><td>116.4</td><td>118.9</td><td>145.2</td></tr></table>", "type_str": "table", "num": null}, "TABREF14": {"text": "Comparison of SEABO against baselines in the Kitchen tasks. We report the average normalized scores and the corresponding standard deviations. We bold and highlight the best mean score cell.", "html": null, "content": "<table><tr><td>Task Name</td><td>BC</td><td>CQL IQL IQL+SEABO</td></tr><tr><td colspan=\"3\">kitchen-complete-v0 65.0 43.8 62.5 67.5±4.2</td></tr><tr><td>kitchen-partial-v0</td><td colspan=\"2\">38.0 49.8 46.3 71.0±4.1</td></tr><tr><td>kitchen-mixed-v0</td><td colspan=\"2\">51.5 51.0 51.0 55.0±3.5</td></tr><tr><td>Average Score</td><td colspan=\"2\">51.5 48.2 53.3 64.5</td></tr></table>", "type_str": "table", "num": null}}}}