{"paper_id": "vdc", "title": "VDC: VERSA<PERSON>LE DATA CLEANSER BASED ON VISUAL-<PERSON>INGUIST<PERSON> INCONSISTENCY BY MULTI-MODAL LARGE LANGUAGE MODELS", "abstract": "The role of data in building AI systems has recently been emphasized by the emerging concept of data-centric AI. Unfortunately, in the real-world, datasets may contain dirty samples, such as poisoned samples from backdoor attack, noisy labels in crowdsourcing, and even hybrids of them. The presence of such dirty samples makes the DNNs vunerable and unreliable. Hence, it is critical to detect dirty samples to improve the quality and realiability of dataset. Existing detectors only focus on detecting poisoned samples or noisy labels, that are often prone to weak generalization when dealing with dirty samples from other fields. In this paper, we find a commonality of various dirty samples is visual-linguistic inconsistency between images and associated labels. To capture the semantic inconsistency between modalities, we propose versatile data cleanser (VDC) leveraging the surpassing capabilities of multimodal large language models (MLLM) in cross-modal alignment and reasoning. It consists of three consecutive modules: the visual question generation module to generate insightful questions about the image; the visual question answering module to acquire the semantics of the visual content by answering the questions with MLLM; followed by the visual answer evaluation module to evaluate the inconsistency. Extensive experiments demonstrate its superior performance and generalization to various categories and types of dirty samples. The code is available at https://github.com/zihao-ai/vdc.", "pdf_parse": {"paper_id": "vdc", "abstract": [{"text": "The role of data in building AI systems has recently been emphasized by the emerging concept of data-centric AI. Unfortunately, in the real-world, datasets may contain dirty samples, such as poisoned samples from backdoor attack, noisy labels in crowdsourcing, and even hybrids of them. The presence of such dirty samples makes the DNNs vunerable and unreliable. Hence, it is critical to detect dirty samples to improve the quality and realiability of dataset. Existing detectors only focus on detecting poisoned samples or noisy labels, that are often prone to weak generalization when dealing with dirty samples from other fields. In this paper, we find a commonality of various dirty samples is visual-linguistic inconsistency between images and associated labels. To capture the semantic inconsistency between modalities, we propose versatile data cleanser (VDC) leveraging the surpassing capabilities of multimodal large language models (MLLM) in cross-modal alignment and reasoning. It consists of three consecutive modules: the visual question generation module to generate insightful questions about the image; the visual question answering module to acquire the semantics of the visual content by answering the questions with MLLM; followed by the visual answer evaluation module to evaluate the inconsistency. Extensive experiments demonstrate its superior performance and generalization to various categories and types of dirty samples. The code is available at https://github.com/zihao-ai/vdc.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "The emerging concept of data-centric AI (DCAI) highlights the pivotal role of data in constructing advanced AI systems (<PERSON><PERSON> et al., 2023) . The quality and reliability of data are crucial factors influencing model performance. Nevertheless, in the real world, dataset can be susceptible to undesirable flaws (<PERSON><PERSON> et al., 2023) .", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "For instance, dirty samples may be introduced into the datasets intentionally or unintentionally. In this paper, we comprehensively examine three categories of dirty samples as follows: Category I: Poisoned Samples. In the context of backdoor attack, malicious attackers intentionally manipulate partical clean samples by embedding triggers and changing the ground-truth labels to target labels, thereby generating poisoned samples. Deep neural networks (DNNs) trained on the dataset with such poisoned samples will be injected with backdoor, i.e., predict any poisoned sample as the target label during the inference stage, while maintain accuracy on the clean samples. Category II: Noisy Labels. In scenarios of crowdsourcing or web crawling, human annotators or automatic annotation robots may make mistakes accidentally, resulting in the presence of dirty samples with corrupted labels. Training DNNs using the dataset with such noisy labels will significantly degrade the overall performance. Category III: Hybrid Dirty Samples. An even more critical concern arises when the attackers poison datasets that initially contain noisy labels. In this case, the datasets comprise both poisoned samples and noisy labels. Models trained on such datasets will encounter both malicious backdoor attack and performance degradation simultaneously.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "The presence of above dirty samples makes the DNNs vulnerable and unreliable. To enhance the robustness and performance of DNNs, the detection of dirty samples is crucial in the lifecycle of DCAI. Recent research have been proposed on the noisy label detection (<PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON> et al., 2023) or poisoned sample detection (<PERSON><PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON> et al., 2023) respectively. However, they frequently exhibit limitations in terms of generalization: 1). Inconsistent generalization across different categories of dirty samples. We empirically find that detectors designed for detecting poisoned samples are ineffective when applied to datasets with noisy labels, and vice versa. Moreover, both types of detectors prove inadequate for hybrid dirty samples. (See Table 5 in Sec 5.2.3). 2). Inconsistent generalization across different types of dirty samples in the same category. For noisy label detection, research has shown that symmetric noisy labels are more readily detectable than asymmetric ones (<PERSON> et al., 2021) . Likewise, for poisoned sample detection, sensitivity to various triggers has been demonstrated in <PERSON> et al. (2022) . Therefore, developing a universal framework capable of detecting multiple types of dirty samples concurrently, including noisy labels and poisoned samples, is an urgent challenge for DCAI.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We find a notable commonality of noisy labels and poisoned samples lies in visual-linguistic inconsistency between visual contents and associated labels, i.e., the semantics of visual modality and that of language modality of label do not match, even when the poisoned samples are embedded with triggers. Given the exceptional capabilities of multimodal large language models (MLLM) in cross-modal alignment and reasoning, we resort to MLLM to measure this semantic inconsistency between modalities. To this end, we propose a universal detection framework called Versatile Data Cleanser (VDC). It consists of three consecutive modules: the visual question generation (VQG) module to generate insightful visual questions about the image based on the associated label; the visual question answering (VQA) module to obtain the semantic information of the image by answering the generated questions with MLLM; followed by the visual answer evaluation (VAE) module to measure the inconsistency by evaluating the matching score between the semantics of the image and labels. Since VDC does not involve the training process with specific dirty samples, it is endowed with the universal capacity to detect various categories and types of dirty samples.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We summarize our main contributions: 1). We identify the commonality of various dirty samples is visual-linguistic inconsistency between visual contents and associated labels. 2). To quantify this inconsistency, we propose a versatile data cleanser that leverages the impressive capabilities of multimodal large language models. 3). Experiments show that VDC consistently exhibits superior performance for detecting poisoned samples, noisy labels, and hybrids of them.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Poisoned Sample Detection. The rise of backdoor attacks in machine learning has posed a significant security threat, including embedding malicious triggers into clean training samples (<PERSON> et al., 2023) . Several recent studies have explored detecting and mitigating the presence of poisoned samples in datasets. <PERSON> et al. (2018) proposes to use K-means to separate the clean and poison clusters in the latent space. <PERSON><PERSON> et al. (2018) and <PERSON><PERSON> et al. (2021) utilize robust statistics to detects poisoned samples based on spectral signature. <PERSON> et al. (2019) observes the randomness of predicted classes for perturbed inputs. <PERSON><PERSON> et al. (2021) proposes to detect artifacts of poison samples in the frequency domain. <PERSON> et al. (2022) focuses on sensitivity metrics for distinguishing poisoned samples from clean ones. <PERSON> et al. (2023) proposes confusion training to decouple benign correlations while exposing backdoor patterns to detection. Most of these approaches require training on the poisoned dataset or external clean subset, which depends on the types of poisoned samples, while our proposed method is more robust and generalizable to various types of poisoned samples.", "section": "RELATED WORKS", "sec_num": "2"}, {"text": "Noisy Label Detection. Human-annotated labels are often prone to noise, and the presence of such noisy labels will degrade the performance of the DNNs. Several approaches have been proposed to detect noisy labels (<PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2021) . <PERSON><PERSON><PERSON> et al. (2021) proposes to exploit confident learning to estimate the uncertainty of dataset labels. CORES (<PERSON> et al., 2021) progressively sieves out corrupted examples via a proposed confidence regularizer. <PERSON> et al. (2022) proposes a data-centric solution based on neighborhood information to detect noisy labels. BHN (<PERSON> et al., 2023) leverages clean data by framing the problem of noisy label detection with clean data as a multiple hypothesis testing problem.", "section": "RELATED WORKS", "sec_num": "2"}, {"text": "Existing poisoned sample detection and noisy label detection methods are limited to performing well in their respective domain. Instead, our paper proposes a universal detection framework capable of detecting various types of dirty samples simultaneously.", "section": "RELATED WORKS", "sec_num": "2"}, {"text": "In this section, we first define the setup of dirty sample detection task, including poisoned samples and noisy labels, and then clarify the goals of this paper.", "section": "PRELIMINARIES: DIRTY SAMPLE DETECTION", "sec_num": "3"}, {"text": "Setup. We consider a standard classification problem given the dataset D = {(x i , y i )} N i=1 that contains N samples i.i.d sampled from X × Y, where x i ∈ X denotes the input feature, y i ∈ Y = {1, . . . , K} is the label of x i . The classification task aims to learn a classifier f θ : X → Y. In the real-world, however, when collecting a dataset, some samples may be corrupted due to human mistakes or malicious goals, thereby generating dirty samples with corrupted labels in the dataset. Therefore, in the real-world, D is the fusion of dirty dataset", "section": "PRELIMINARIES: DIRTY SAMPLE DETECTION", "sec_num": "3"}, {"text": "D = {( xi , ỹi )} M i=1 and clean dataset D = {( xi , ŷi )} N -M i=1 , i.e., D ′ = D ∪ D, where ( xi , ỹi", "section": "PRELIMINARIES: DIRTY SAMPLE DETECTION", "sec_num": "3"}, {"text": ") is a dirty sample and M is the number of dirty samples, ( xi , ŷi ) is a clean sample. We formulate two types of dirty sample in the following:", "section": "PRELIMINARIES: DIRTY SAMPLE DETECTION", "sec_num": "3"}, {"text": "• Poisoned Sample: Poisoned sample denotes the one that its visual feature is maliciously manipulated by the attacker, i.e., xi := g(x i ) ̸ = xi , where g(•) is the generation function, such as blending (<PERSON> et al., 2017) and wrapping-based transformation (<PERSON><PERSON><PERSON> & <PERSON>ran, 2021) . Meanwhile, the label is changed to the target label by the attacker, i.e., ỹi = y t ̸ = ŷi .", "section": "PRELIMINARIES: DIRTY SAMPLE DETECTION", "sec_num": "3"}, {"text": "• Noisy Label: Noisy label represents the sample that its label is annotated incorrectly, while its visual feature remains unchanged, i.e., xi = xi , ỹi ∈ Ỹ ̸ = ŷi , where Ỹ represents noisy version of Y. Following <PERSON> et al. (2023) ; <PERSON> et al. (2022) , we focus on the closed-set label noise that Y and Ỹ are assumed to be in the same label space. This situation is common when human annotators are asked to select the most appropriate label from a preset label set.", "section": "PRELIMINARIES: DIRTY SAMPLE DETECTION", "sec_num": "3"}, {"text": "Goal. Unlike most existing works that can only detect noisy labels or poisoned samples, our goal is to design a universal detection framework that can be applied to various categories of dirty samples.", "section": "PRELIMINARIES: DIRTY SAMPLE DETECTION", "sec_num": "3"}, {"text": "We find that what poisoned samples and noisy labels have in common is that the visual features of the poisoned samples are inconsistent with their given labels. For example, an image containing 'cat' is wrongly labeled as a 'dog', which can be detected by comparing the semantics of the visual content of the image and that of the given label. For the poisoned sample, although the trigger is embedded into the image, its underlying semantics has not been modified. We refer this commonality as \"visual-linguistic inconsistency\". Thanks to the surpassing abilities of multimodal understanding and reasoning of MLLM, we propose Versatile Data Cleanser, called VDC, to capture the visuallinguistic inconsistency based on MLLM. To the best of our knowledge, VDC is the first universal framework that is capable of detecting both noisy labels and poisoned samples simultaneously. As shown in Figure 1 , it consists of the following consecutive modules: ", "section": "METHODOLOGY: VERSATILE DATA CLEANSER", "sec_num": "4"}, {"text": "We propose to obtain semantic information of the visual content by asking MLLM visual questions. Therefore, the first step is how to design insightful questions based on the given label y i , which is formulated as follows:", "section": "VISUAL QUESTION GENERATION", "sec_num": "4.1"}, {"text": "EQUATION", "section": "VISUAL QUESTION GENERATION", "sec_num": "4.1"}, {"text": "where y i might be corrupted label ỹi or ground-truth label ŷi , Q j i denotes the j-th question and A j i denotes expected answer, and N q denotes the number of questions. In order to comprehensively and fully understand the semantics of images, two different types of questions are considered in VDC, including coarse-grained general questions and fine-grained label-specific questions.", "section": "VISUAL QUESTION GENERATION", "sec_num": "4.1"}, {"text": "General Questions. General questions can serve as a means to acquire holistic semantic understanding of an image from a global perspective, such as \"Please describe the image briefly.\". The expected answers to these general questions align with the given label. Since the general questions remain consistent across various labels, they are generated by random selection from a set of predefined templates, as outlined in Table 10 in Appendix E.", "section": "VISUAL QUESTION GENERATION", "sec_num": "4.1"}, {"text": "Label-specific Questions. Besides, the label-specific questions related to the given labels aim to extract more localized semantics from the image, encompassing aspects of common sense features, attributions, functions, geography, history, culture, and etc . For example, given the label \"airplane\", an apt question is \"Is the object in the image designed for flying in the air?\". Designing most labelspecific questions necessitates a level of expertise about the label that may exceed the capacity of a human annotator. When dealing with a multitude of labels, such as ImageNet with 1,000 classes, manually designing for each label becomes impractical. Hence, we utilize LLM like ChatGPT (Ope-nAI) to automatically generate these questions, depending on its expansive open-world knowledge.", "section": "VISUAL QUESTION GENERATION", "sec_num": "4.1"}, {"text": "The well-designed prompts and generated questions are detailed in Appendix D and E.", "section": "VISUAL QUESTION GENERATION", "sec_num": "4.1"}, {"text": "The next step involves responding to the generated questions in Sec 4.1 based on the input image x i to acquire the semantics of the visual content. This process is often referred to as the visual question answering (VQA) task, which can be formulated as follows:", "section": "VISUAL QUESTION ANSWERING", "sec_num": "4.2"}, {"text": "EQUATION", "section": "VISUAL QUESTION ANSWERING", "sec_num": "4.2"}, {"text": "where R j i indicates the response of VQA model for the question Q j i . Answering these questions necessitates the capabilities of natural language generation and external knowledge beyond the visible content of image. Therefore, we resort to MLLM as our VQA model owing to its remarkable capabilities of visual and language understanding and reasoning, which has been demonstrated in a wide range of visual-language tasks.", "section": "VISUAL QUESTION ANSWERING", "sec_num": "4.2"}, {"text": "Afterward, for a suspicious input sample (x i , y i ), we obtain a set of questions, expected answers, and responses, i.e., {Q j i , A j i , R j i } Nq j=1 . The subsequent step is to assess visual-linguistic consistency by evaluating the matching score between the semantics of the image and label. We first judge the correctness of the response of MLLM, i.e., whether it aligns with the expected answer, which can be formulated as follows:", "section": "VISUAL ANSWER EVALUATION", "sec_num": "4.3"}, {"text": "EQUATION", "section": "VISUAL ANSWER EVALUATION", "sec_num": "4.3"}, {"text": ") where e i denotes the correctness, i.e., true or false. For label-specific questions with deterministic expected answers, we use string matching to evaluate the response. If the word \"yes\" is present in the response, the result should be true, otherwise if the response contains the word \"no\", the result should be false. Nevertheless, for general questions, string matching is insufficient to determine correctness. In such cases, we employ ChatGPT as a specialized evaluator through meticulously designed prompts, which is a commonly adopted approach in the evaluation of <PERSON><PERSON> et al. (2023) .", "section": "VISUAL ANSWER EVALUATION", "sec_num": "4.3"}, {"text": "Vote-based Ensemble. Then the matching score s i of sample (x i , y i ) is computed as the proportion of questions answered correctly, which are formulated as follows:", "section": "VISUAL ANSWER EVALUATION", "sec_num": "4.3"}, {"text": "s i = Nq j=1 1(e i = true) N q (4)", "section": "VISUAL ANSWER EVALUATION", "sec_num": "4.3"}, {"text": "where 1(•) denotes identity function. If the score is less than the threshold α, sample (x i , y i ) is detected as a dirty sample and then removed from the dataset.", "section": "VISUAL ANSWER EVALUATION", "sec_num": "4.3"}, {"text": "Datasets. We evaluate ASRon three benchmark datasets, CIFAR-10 ( <PERSON><PERSON><PERSON><PERSON> et al., 2009) and two ImageNet (<PERSON><PERSON><PERSON> et al., 2015) subsets: (1) For ImageNet-100, we randomly choose 100 classes from ImageNet, in which 500 images per class for training and 100 images per class for testing.", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "(2) For ImageNet-Dog, to evaluate the effect of similarity of classes, we randomly choose 10 classes of dogs from ImageNet, in which 800 images per class for training and 200 images per class for testing.", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "Dirty Samples Generation. Denote the ratio of dirty samples in the whole dataset by η. Two types of dirty samples are considered in the evaluation, which are illstrated as follows:", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "• Poisoned Samples. We consider six representative backdoor attacks to generate poisoned samples: (1) Visible triggers: BadNets (<PERSON><PERSON> et al., 2019) , Blended (<PERSON> et al., 2017) , TrojanNN (<PERSON> et al., 2018) . ( 2) Invisible triggers: SIG (<PERSON><PERSON> et al., 2019) , <PERSON><PERSON> Li et al. (2021 ), WaNet Nguyen & Tran (2021) . For all attacks, we randomly choose the same number of images from all classes except target class to add trigger, and then change the labels as target label. The example and settings of each attack are detailed in Appendix C.2. • Noisy Labels. We experiment with two popular synthetic noisy model models: the symmetric and asymmetric noise: (1) Symmetric noisy label is generated by uniform flipping, i.e., randomly flipping a ground-truth label to all other possible classes (<PERSON> et al., 2019) . ( 2) Asymmetric noisy label is generated by flipping the ground-truth label to the next class, i.e., (i mod K) + 1, where K denotes the number of classes.", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "Evaluation Metrics. We report the detection results with two key metrics: true positive rate (TPR) and false positive rate (FPR) following <PERSON> et al. (2023) . TPR means the recall of detected dirty samples, representing the capacity to successfully detect dirty samples within the dataset. FPR denotes the ratio of clean samples erroneously identified as dirty samples, highlighting the susceptibility to produce false alarms. An ideal detection method should exhibit a higher TPR and lower FPR.", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "Let v i = 1 indicate that the i-th sample is detected as dirty sample. Moreover, when retraining on the purified dataset, we report the attack success rate (ASR) and the clean accuracy (ACC) of the retrained model.", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "Implemented Details. We adopt ChatGPT based on GPT-3.5-turbo (OpenAI) as LLM and Instruct-BLIP (<PERSON> et al., 2023) as MLLM in VDC. For all datasets, we generate two general questions. The number of label-specific questions is six for ImageNet-100 and four for CIFAR-10 and ImageNet-Dog. The threshold α is set as 0.5 across all experiments. The noisy ratio η for noisy labels is set as 0.4. We poison 50 and 500 samples per class for CIFAR-10, 5 and 50 per class for ImageNet-100, and 80 per class for ImageNet-Dog. We retrain on the purified dataset with ResNet-18 (<PERSON> et al., 2016) . Additional details can be found in Appendix C.1.", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "Compared Baselines. For poisoned sample detection, we compare with 7 baselines, in which STRIP (<PERSON> et al., 2019) , SS (<PERSON><PERSON> et al., 2018) , SCAn (<PERSON> et al., 2021) , Frequency (<PERSON><PERSON> et al., 2021) and CT (<PERSON> et al., 2023) require external clean subset to execute, while SPECTRE (<PERSON><PERSON> et al., 2021) and D-BR (<PERSON> et al., 2022) do not require any clean subset. For noisy label detection, we compare with 5 baselines, including BHN (<PERSON> et al., 2023) , CL (Northcutt et al., 2021) , CORES <PERSON> et al. (2021) , SimiFeat-V and SimiFeat-R (<PERSON> et al., 2022) , in which BHN relies on a clean subset to perform. The detailed settings of each baseline can be found in Appendix C.3,C.4.", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "In this section, we first conduct a comprehensive evaluation on the poisoned samples detection.", "section": "RESULTS ON DETECTING POISONED SAMPLES", "sec_num": "5.2.1"}, {"text": "The results on CIFAR-10, ImageNet-100 and ImageNet-Dog with different poisoning ratios are presented in Tables 1, 2 ,13,14 (Refer Tables 13,14 in Appendix F.2). For a fair comparison, all baselines requiring clean data utilize 4% clean subset. The results demonstrate the effectiveness of our proposed method from the following aspects:", "section": "RESULTS ON DETECTING POISONED SAMPLES", "sec_num": "5.2.1"}, {"text": "Consistent Effectiveness Against Various Types of Poisoned Samples. From the results on CIFAR-10 in Table 1 , we find that VDC consistently exhibits superior performance under various types of poisoned samples without relying on any clean subset, demonstrating the generalization of VDC. In contrast, other detectors are sensitive to different types of triggers. For example, VDC achieves average TPR of 99.91% against all backdoor attacks, while SPECTER experiences a sig- 1 , when facing a larger dataset with more labels, VDC maintains performance with the average TPR still reaching 99.94%. On the contrary, other baselines are unstable on different datasets, such as SPECTRE decreases from 77.20% to 45.58%. To explore the effect of the similarity of classes, we evaluate on a fine-grained dataset ImageNet-Dog. From the results in Table 3 in Appendix F.1, VDC shows evident improvement compared to other baselines.", "section": "RESULTS ON DETECTING POISONED SAMPLES", "sec_num": "5.2.1"}, {"text": "Consistent Effectiveness Across Poisoning Ratios. We also evaluate with lower poisoning ratios on CIFAR-10 and ImageNet-100 to study the effect of poisoning ratios. Compare Table 1 with η = 0.09 and Table 13 with η = 0.009 on CIFAR-10, we find that the performance of VDC has almost no fluctuation, while other methods are greatly affected by the poisoning ratio. A similar phenomenon on ImageNet-100 can be found in Table 2 and 14. 5.2.2 RESULTS ON DETECTING NOISY LABELS.", "section": "RESULTS ON DETECTING POISONED SAMPLES", "sec_num": "5.2.1"}, {"text": "In this section, we evaluate VDC on noisy label detection, another common type of dirty samples. The results on CIFAR-10, ImageNet-100 and ImageNet-Dog are shown in Table 4 , verifying that VDC also performs well on detecting noisy labels from the following points:", "section": "RESULTS ON DETECTING POISONED SAMPLES", "sec_num": "5.2.1"}, {"text": "Consistent Effectiveness Against Various Types of Noisy Labels. By comparing the performance on the symmetric and the asymmetric noisy labels, we note that asymmetric is a more challenging setting. Even though some baselines behave well on detecting symmetric noisy labels, such as SimiFeat-V and SimiFeat-R, they may reach low TPR on the symmetric noisy labels. However, VDC consistently works well on the asymmetric noisy label. For example, VDC achieves 99.60% TPR on detecting asymmetric noisy labels on CIFAR-10, while SimiFeat-V only has 59.67% TPR.", "section": "RESULTS ON DETECTING POISONED SAMPLES", "sec_num": "5.2.1"}, {"text": "Consistent Effectiveness Across Datasets. From the results on the three datasets in Table 4 , we note that VDC performs consistently well on different datasets, while other methods perform worse on ImageNet-100 and Imagenet-Dog, which indicates the robustness of our proposed method. ", "section": "RESULTS ON DETECTING POISONED SAMPLES", "sec_num": "5.2.1"}, {"text": "In the real world, when an attacker poisons a realistic dataset, the dataset may already contain noisy labels. Therefore, in this section, we further evaluate the effectiveness of detectors when the dataset contains both poisoned samples and noisy samples, in which poisoning ratio is 0.09 and noisy ratio is 0.1 The results on CIFAR-10 are shown in Table 5 . The following insightful points can be found from the results:", "section": "RESULTS ON DETECTING HYBRID DIRTY SAMPLES", "sec_num": "5.2.3"}, {"text": "Consistent Effectiveness Against Hybrids of Poisoned Samples and Noisy Labels. In this more challenging scenario, VDC still shows leading advantages compared with other methods, with average TPR reaching 99.41%. However, methods designed only for poisoned sample detection perform poorly when detecting a mixture of various dirty samples, such as SCAn decreasing from 95.46% to 46.29%. In the meantime, methods designed only to detect noisy samples also underperform in this case, such as CL decreasing from 85.05% to 36.33%, which further illustrates the effectiveness and robustness of our proposed method.", "section": "RESULTS ON DETECTING HYBRID DIRTY SAMPLES", "sec_num": "5.2.3"}, {"text": "After detecting and removing dirty samples from the origin dataset, we normally train DNNs on the purified datasets to verify the detection effect. The results on the purified datasets initially contain poisoned samples, noisy labels, and hybrid dirty samples are shown in Tables 6, 15 ,16,17. By accurately detecting dirty samples, VDC indeed prevents the trained model from being interfered by dirty samples, i.e., maintaining low ASR and high ACC compared with other detectors.", "section": "TRAINING ON THE PURIFIED DATASETS", "sec_num": "5.2.4"}, {"text": "In this section, we provide further analysis and ablation studies of VDC and show some limitations.", "section": "A CLOSER LOOK AT VDC", "sec_num": "6"}, {"text": "Effect of the Type of Visual Questions. Figure 2a illustrates the influence of visual question types generated in VDC. We conducted experiments separately only using general questions or label- specific questions while keeping all other settings constant. We observe that using only one type of question makes the model perform worse. In addition, label-specific questions are slightly more important than general questions.", "section": "A CLOSER LOOK AT VDC", "sec_num": "6"}, {"text": "Effect of the Number of Visual Questions. We investigate the effect of the number of visual questions generated in VDC. Figure 2b shows the detection results w.r.t. various number of questions. We find that VDC's performance improves as the number of questions increases. But more questions also lead to more inference time. Therefore, it becomes crucial to strike a balance between these two factors.", "section": "A CLOSER LOOK AT VDC", "sec_num": "6"}, {"text": "Effect of the Multimodal Large Language Model. In Figure 2c , we substitute the multimodal large language model in the VDC with <PERSON><PERSON> (<PERSON> et al., 2023) , another recently open-sourced MLLM, to investigate the impact of MLLM. Although the performance differs from those obtained with InstructBLIP, it still outperforms the majority of baselines. with the TPR for all poisoned samples consistently exceeding 96%, which further verifies the effectiveness of VDC.", "section": "A CLOSER LOOK AT VDC", "sec_num": "6"}, {"text": "Computational Complexity. Unlike other baselines that require training, VDC requires only inference of LLM and MLLM. Let K represent the number of classes, N qg and N qs denote the number of general questions and label-specific questions respectively, T and T ′ denote the time of one inference of LLM and MLLM. The overall time complexity can be expressed as O(T KN qs ) + O(T ′ (N qg + N qs )N ) + O(T N qg N ), in which three terms correspond to the complexities of VQG, VQA, and VQE respectively. With the development of lightweight LLM, such as quantization (<PERSON> et al., 2023) , the inference speed of LLM will increase, leading to a further reduction in the computational cost of VDC.", "section": "A CLOSER LOOK AT VDC", "sec_num": "6"}, {"text": "Limitations. 1) VDC hinges on the inconsistency between visual content and labels, making it inappropriate for detecting samples without corrupted labels, such as clean-label backdoor attack.", "section": "A CLOSER LOOK AT VDC", "sec_num": "6"}, {"text": "2) Although ensembling technique has been employed in our framework to mitigate the risk of abnormal questions and answers, LLM and MLLM may still yield incorrect replies. However, the performance of VDC will also improve in the future as LLM progresses.", "section": "A CLOSER LOOK AT VDC", "sec_num": "6"}, {"text": "In this paper, we propose to detect dirty samples with corrupted labels by exploiting semantic inconsistency between visual content and associated labels. To this end, we design versatile data cleanser (VDC), a universal detection framework harnessing the surpassing capabilities of large language models and multimodal large language models, which is capable of detecting various categories and types of dirty samples. Experimental results validate the consistent superior performance of VDC in poisoned sample detection and noisy label detection. In addtion, VDC still maintains effectiveness even when the dataset contains the hybrid dirty samples. Furthermore, we anticipate that as large language models continue to evolve at a rapid pace, VDC will demonstrate further enhanced performance in the future.", "section": "CONCLUSION", "sec_num": "7"}, {"text": "The overall structure of the Appendix is listed as follows:", "section": "A APPENDIX OVERVIEW", "sec_num": null}, {"text": "• • Appendix E: Examples of generated questions.", "section": "A APPENDIX OVERVIEW", "sec_num": null}, {"text": "-Appendix E.1: Examples of general questions.", "section": "A APPENDIX OVERVIEW", "sec_num": null}, {"text": "-Appendix E.2: Examples of label-specific questions.", "section": "A APPENDIX OVERVIEW", "sec_num": null}, {"text": "• Appendix F: Additional experimental results.", "section": "A APPENDIX OVERVIEW", "sec_num": null}, {"text": "-Appendix F.1: More results of poisoned sample detection.", "section": "A APPENDIX OVERVIEW", "sec_num": null}, {"text": "-Appendix F.2: More results of training on the purified datasets.", "section": "A APPENDIX OVERVIEW", "sec_num": null}, {"text": "As we identified in the manuscript, how to measure the visual-linguistic inconsistency between the visual content and associated labels is the key to detect dirty samples. A naive approach to quantify such semantic inconsistency is directly using CLIP (<PERSON><PERSON> et al., 2021) . We first encode the input image using image encoder in the CLIP, and get the image representation I. The associated label is transformed into sentences, \"a photo of {label}\". Then the text representation T is extracted from the sentence via text encoder in the CLIP. Cosine similarity between I and T is treated as the matching score. If the matching score is less than a certain threshold, the input sample can be considered as dirty sample. In the implementation, we choose ViT-B/32 as the image encoder and the threshold is set as 0.2. The results are shown in Table 7 , where VDC-CLIP represents the naive approach with CLIP. We find that the TPR using only CLIP is far from our proposed VDC, indicating the need for more advanced detection frameworks instead of only using CLIP. After successfully detecting dirty samples in the dataset, we need to normally training on the purified dataset t further verify the effectiveness of detectors. In our experiments, we choose ResNet-18 as the target model. For all datasets, the training epochs is set as 100 and adpot SGD optimizer. For Published as a conference paper at ICLR 2024 CIFAR-10, we set the batch size of 128 and the inital learning rate of 0.1 and decreases it by the factor of 10 after 50, 75 epochs. For ImageNet-100 and ImageNet-Dog, the batch size is 64, the inital learning rate is 0.1 and decreases by the factor of 10 after 30, 60 epochs.", "section": "B A NAIVE APPROACH WITH CLIP", "sec_num": null}, {"text": "In this section, we present the settings for generating poisoned samples in backdoor attacks that are evaluated in the main manuscript. For all backdoor attacks, we choose class 0 as the target label.", "section": "C.2 DETAILS OF POISONED SAMPLE GENERATION", "sec_num": null}, {"text": "BadNets BadNets (<PERSON><PERSON> et al., 2019) stands as a seminal work in the realm of backdoor attacks, which introduces the concept of substituting specific pixels within a clean image with a welldesigned trigger, thus yielding a poisoned image. In our experiments, for a 32 × 32 image in CIFAR-10, we select a 3 × 3 white square patch located in the lower-right corner of the image to serve as the trigger. In the case of images with dimensions 224 × 224 from both ImageNet-100 and ImageNet-Dog datasets, we utilize a white square patch with dimensions 21 × 21 as the trigger.", "section": "C.2 DETAILS OF POISONED SAMPLE GENERATION", "sec_num": null}, {"text": "Blen<PERSON> Blen<PERSON> Chen et al. ( 2017) firstly adopted the blended injection strategy to generate poisoned samples by blending a benign input instance with the key pattern. The choice of the key pattern can be an arbitrary image. In our experiments, we use a \"Hello Kitty\" cartoon image (see Figure 3 ) as a trigger, and the blending ratio is set as 0.1. SIG SIG (<PERSON><PERSON> et al., 2019) proposes a horizontal sinusoidal signal designed by v(i, j) = ∆ sin(2πjf /m), 1 ≤ j ≤ m, 1 ≤ i ≤ l, for a certain frequency f , on the clean image, where m is the number of columns of the image and l the number of rows. In the evaluation, we set ∆ = 20, f = 6 for all datasets. The overlay backdooor signal is applied on all the channels. In this case, the backdoor is almost, though not perfectly, invisible.", "section": "C.2 DETAILS OF POISONED SAMPLE GENERATION", "sec_num": null}, {"text": "TrojanNN TrojanNN attack <PERSON> et al. (2018) starts by choosing a trigger mask, which is a subset of the input variables that are used to inject the trigger. Then it searches for value assignment of the input variables in the trigger mask so that the selected neuron(s) of the target model can achieve the maximum values. The identified input values are essentially the trigger. In our evaluation, as shown in Figure 4 , we choose to use the Apple logo as the trigger mask and ResNet-18 as target model.", "section": "C.2 DETAILS OF POISONED SAMPLE GENERATION", "sec_num": null}, {"text": "SSBA SSBA (<PERSON> et al., 2021) generates sample-specific invisible additive noises as backdoor triggers by encoding an attacker-specified string into clean images through an encoder-decoder network. Following the settings in (<PERSON> et al., 2021) , we use a U-Net (<PERSON> et al., 2015) style DNN as the encoder, a spatial transformer network (<PERSON><PERSON><PERSON> et al., 2015) as the decoder. The encoderdecoder is trained for 140,000 iterations and batch size is set as 16.", "section": "C.2 DETAILS OF POISONED SAMPLE GENERATION", "sec_num": null}, {"text": "WaNet WaNet (Nguyen & Tran, 2021) uses a small and smooth warping field in generating poisoned images, making the modification unnoticeable. In our experiments, we adopt elastic image warping proposed in (Nguyen & Tran, 2021) . ", "section": "C.2 DETAILS OF POISONED SAMPLE GENERATION", "sec_num": null}, {"text": "In this section, we present the settings of 7 poisoned sample detection baselines compared in our experiments.", "section": "C.3 DETAILS OF <PERSON><PERSON><PERSON><PERSON><PERSON> POISONED SAMPLE DETECTORS", "sec_num": null}, {"text": "STRIP STRIP (<PERSON> et al., 2019) detects a poisoned sample by checking whether superimposing the input image over a set of randomly selected images makes those new image's class label harder to predict. If so, the input is considered to be normal and otherwise. In our evaluation, the FRR is preset to be 0.1 SS SS (<PERSON><PERSON> et al., 2018) identifies spectral signatures of all known backdoor attacks to utilize tools from robust statistics to thwart the attacks. The upper bound on number of poisoned training set examples ε is set as 0.1.", "section": "C.3 DETAILS OF <PERSON><PERSON><PERSON><PERSON><PERSON> POISONED SAMPLE DETECTORS", "sec_num": null}, {"text": "SCAn SCAn (<PERSON> et al., 2021) utilizes several statistical methods to estimate the most likely parameters for the decomposition and untangling models and then detect an infected label through a likelihood ratio test. The threshold user for split clean samples in each classes is set as <PERSON><PERSON><PERSON>'s number e.", "section": "C.3 DETAILS OF <PERSON><PERSON><PERSON><PERSON><PERSON> POISONED SAMPLE DETECTORS", "sec_num": null}, {"text": "Frequency Frequency-based detection (<PERSON><PERSON> et al., 2021) trains a binary classifier based on a training set that contains DCT transformations of clean samples and samples with digital manipulations. For CIFAR-10, We directly use their provided pretrained detection model. For ImageNet-100 and ImageNet-Dog, we train a 6 layer CNN with the same settings as CIFAR-10.", "section": "C.3 DETAILS OF <PERSON><PERSON><PERSON><PERSON><PERSON> POISONED SAMPLE DETECTORS", "sec_num": null}, {"text": "CT CT (<PERSON> et al., 2023) proposes confusion training that applies an additional poisoning attack to the already poisoned dataset, actively decoupling benign correlation while exposing backdoor patterns to detection. In our experiments, we set confusion factor λ = 20, the number of confusion iterations m = 6000, the number of confusion training rounds K = 6.", "section": "C.3 DETAILS OF <PERSON><PERSON><PERSON><PERSON><PERSON> POISONED SAMPLE DETECTORS", "sec_num": null}, {"text": "We only use the sample-distinguishment (SD) module in D-BR. SD module splits the whole training set into clean, poisoned and uncertain samples, according to the FCT metric. In our evaluation, we set α c = 0.2, α p is set as the true poisoning ratio.", "section": "D-BR", "sec_num": null}, {"text": "SPECTRE SPECTRE (<PERSON><PERSON> et al., 2021) uses robust covariance estimation to amplify the spectral signature of corrupted data. In our experiments, α is set as 4, poison fraction ε is set as 0.1.", "section": "D-BR", "sec_num": null}, {"text": "In this section, we present the settings of 5 noisy label detection baselines compared in our experiments.", "section": "C.4 DETAILS OF BA<PERSON>L<PERSON>E NOISY LABEL DETECTORS", "sec_num": null}, {"text": "BHN BHN (<PERSON> et al., 2023) SimiFeat-V and SimiFeat-R SimiFeat-V (<PERSON> et al., 2022) uses \"local voting\" via checking the noisy label consensuses of nearby features to determine if the example is corrupted. SimiFeat-R (<PERSON> et al., 2022) scores and ranks each instance based on the neighborhood information and filters out a guaranteed number of instances that are likely to be corrupted. In the evaluation, the KNN paprameter k is set as 10 and epochs is set as 21.", "section": "C.4 DETAILS OF BA<PERSON>L<PERSON>E NOISY LABEL DETECTORS", "sec_num": null}, {"text": "In this section, we present the prompts that we used to query ChatGPT in our paper. Table 8 shows the prompts used for the generation of label-specific visual questions for different datasets. Table 9 shows the prompts used for the evaluation of the response of MLLM.", "section": "D PROMPTS USED IN CHATGPT", "sec_num": null}, {"text": "In this section, we show some examples of generated visual questions in the visual question generation module of VDC.", "section": "E EXAMPLES OF GENERATED QUESTIONS", "sec_num": null}, {"text": "Table 10 shows the general questions used for acquiring holistic descriptions of the image, with some prompts sourced from (<PERSON> et al., 2023) . • Describe the image in detail.", "section": "E.1 EXAMPLES OF GENERAL QUESTIONS", "sec_num": null}, {"text": "• Describe the image briefly.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• How would you summarize the content of the image in a few words?", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Provide a detailed description of the given image.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Describe the image concisely.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Provide a brief description of the given image.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Offer a succinct explanation of the picture presented.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Summarize the visual content of the image.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Give a short and clear explanation of the given image.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Share a concise interpretation of the image provided.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Present a compact description of the photo's key features.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Relay a brief, clear account of the picture shown.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Render a clear and concise summary of the photo.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Write a terse but informative summary of the picture.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Create a compact narrative representing the image presented.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "• Please generate a detailed description of the dog in the image, including the breed of the dog, its specific attributes, unique features that can distinguish it from other breeds.", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "(for ImageNet-Dog)", "section": "E.2 EXAMPLES OF LABEL-SPECIFIC QUESTIONS", "sec_num": null}, {"text": "In this section, we provide more experimental results that mentioned in the manuscript.", "section": "F ADDITIONAL EXPERIMENTAL RESULTS", "sec_num": null}, {"text": "• Table 13 shows the detection results on CIFAR-10 with poisoning ratio η = 0.009, i.e., 50 poisoned samples per class.", "section": "F.1 MORE POISONED SAMPLE DETECTION RESULTS", "sec_num": null}, {"text": "• Table 14 shows the detection results on ImageNet-100 with poisoning ratio η = 0.0099, i.e., 5 poisoned samples per class.", "section": "F.1 MORE POISONED SAMPLE DETECTION RESULTS", "sec_num": null}, {"text": "The results show the consistent effectiveness of VDC across different datasets and poisoning ratios.", "section": "F.1 MORE POISONED SAMPLE DETECTION RESULTS", "sec_num": null}, {"text": "F.2 RESULTS OF TRAINING ON THE PURIFIED DATASETS.", "section": "F.1 MORE POISONED SAMPLE DETECTION RESULTS", "sec_num": null}, {"text": "• Table 15 shows the normally training results on the purified CIFAR-10 with poisoning ratio η = 0.009, i.e., 50 poisoned samples per class.", "section": "F.1 MORE POISONED SAMPLE DETECTION RESULTS", "sec_num": null}, {"text": "• Table 17 shows the normally training results on the purified CIFAR-10 with poisoning ratio η = 0.09 noisy ratio η 2 = 0.1.", "section": "F.1 MORE POISONED SAMPLE DETECTION RESULTS", "sec_num": null}, {"text": "The results show that our proposed VDC can indeed improve the reliability and usability of DNNs trained with dirty samples. ", "section": "F.1 MORE POISONED SAMPLE DETECTION RESULTS", "sec_num": null}, {"text": "https://github.com/cleanlab/cleanlab", "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by the National Natural Science Foundation of China under grant No. 62076213, Shenzhen Science and Technology Program under grants No. RCYX20210609103057050, Outstanding Youth Program of Guangdong Natural Science Foundation, and the Guangdong Provincial Key Laboratory of Big Data Computing, the Chinese University of Hong Kong, Shenzhen.", "section": "ACKNOWLEDGMENTS", "sec_num": null}, {"text": "Published as a conference paper at ICLR 2024 Table 11 : Examples of label-specific questions on ImageNet-100.", "section": "annex", "sec_num": null}, {"text": "cock Questions: Is the object in the image belong to the type of cock?Is the object in the image a type of poultry? Is the object in the image commonly found on farms or in rural areas? Does the object in the image have a comb on top of its head? Is the object in the image known for its distinctive crowing sound? Does the object in the image have sharp spurs on its legs?Label: goldfinch Questions: Is there a type of bird in the image?Is the object in the image a type of finch? Does the image feature a small bird known for its vibrant yellow and black coloration?Is the object in the image a type of finch with bright plumage? Is the bird in the image a small passerine species? Is the bird in the image belong to carduelis?Label: scorpion Questions: Does the object in the image have a venomous stinger?Is there a scorpion in the image? Is the object in the image a type of arachnid? Is the creature in the image venomous? Is the creature in the image commonly found in desert regions? Does the image show a scorpion?Label: koala Questions: Is there a koala in the image? Does the object in the image have a round face with large, fluffy ears? Is the object in the image known for its ability to climb trees? Does the animal in the image primarily feed on eucalyptus leaves? Is the object in the image typically gray in color? Is the object in the image commonly found in Australia?Label: flamingo Questions: Is the image showing a flamingo?Is the object in the image commonly found in wetland habitats? Does the object in the image have a long, curved neck? Is the bird in the image tall with long legs? Is the bird in the image known for its vibrant pink plumage Is the bird in the image known for standing on one leg?Label: gorilla Questions: Is the creature in the image a type of gorilla?Is the animal in the image known for its intelligence? Is the animal in the image known for its strength? Is the animal in the image a primate? Is the object in the image commonly found in forests or jungles? Does the object in the image have a large and robust body?Label: dumbbell Questions: Does the image show a dumbbell?Is there a dumbbell in the image? Is the object in the image often used to build muscle strength? Is the object in the image associated with fitness training? Is the object in the image commonly hold with hands? Is the object in the image a type of exercise equipment?Table 12 : Examples of label-specific questions on ImageNet-100.Label: hatchet Questions: Is there a hatchet in the image? Does the image show a hatchet? Is the object in the image a type of cutting tool? Is the object in the image typically used for chopping? Is the object in the image often used for splitting wood? Is the object in the image typically held with one hand?Label: stethoscope Questions: Is the object in the image a stethoscope? Does the object in the image have a distinct Y-shaped design? Is the medical instrument in the image a stethoscope? Is the device primarily used by doctors? Is the tool in the image commonly used to listen to heartbeats? Is the device in the image associated with medical examinations?Label: broccoli Questions: Is there broccoli in the image?Is the object in the image a vegetable with a thick, edible stalk? Is the vegetable in the image broccoli? Is the vegetable in the image green? Is the object in the image commonly used in salads? Is the object in the image often cooked or consumed for its health benefits?Label: space shuttle Questions: Does the image show a space shuttle?Is the object in the image capable of launching vertically into space? Is the object in the image equipped with powerful rocket engines for propulsion? Is this a spacecraft used to transport astronauts and cargo? Is the object known for its missions to the International Space Station? Is this a vehicle that was used by NASA for space exploration?Label: pomegranate Questions: Is the fruit in the image a pomegranate?Is the fruit in the image typically red or reddish in color? Does the fruit in the image have a tough outer rind? Is the fruit in the image typically used to make juices and other beverages? Does the object in the image have a crown-like structure at the top? Does the object in the image have a segmented interior filled with clusters of juicy, ruby-red seeds?Label: radio telescope Questions: Is the object in the image a type of radio telescope? Does the object in the image have a large dish or antenna-like structure? Does the object in the image have a parabolic or spherical reflector to focus radio waves? Is the device in the image used for radio astronomy? Is the equipment in the image designed to receive radio waves from the universe? Does the image depict a device that contributes to radio astronomy research?", "section": "Label:", "sec_num": null}], "ref_entries": {"FIGREF0": {"num": null, "fig_num": "1", "text": "Figure 1: The framework of Versatile Data Cleanser. Given the image and label, the visual question generation module first generates general and label-specific questions respectively. Then the visual question answering module answers the generated questions based on the image. Last, the visual question evaluation module evaluates the correctness of answers and makes the final judge based on the vote-based ensemble.", "type_str": "figure", "uris": null}, "FIGREF1": {"num": null, "fig_num": null, "text": "(a) Effect of question types. (b) Effect of question numbers. (c) Effect of MLLM.", "type_str": "figure", "uris": null}, "FIGREF2": {"num": null, "fig_num": "2", "text": "Figure 2: Ablation results on the different aspects of VDC. (a) shows average results on CIFAR-10 with η = 0.09 under different types of visual questions, where G denotes general questions and S denotes labelspecific questions. (b) shows average results on ImageNet-100 with η = 0.099 under different numbers of visual questions. (c) shows results of various poisoned samples on CIFAR-10 with η = 0.09 under different multimodal large language models.", "type_str": "figure", "uris": null}, "FIGREF3": {"num": null, "fig_num": null, "text": "Appendix B: A Naive Approach with CLIP • Appendix C: More implementation details. -Appendix C.1: Details of training on the purified datasets. -Appendix C.2: Details of poisoned sample generation. -Appendix C.3: Details of baseline Poisoned sample detectors. -Appendix C.4: Details of baseline Noisy label detectors • Appendix D: Prompts used in ChatGPT.", "type_str": "figure", "uris": null}, "FIGREF4": {"num": null, "fig_num": "3", "text": "Figure 3: The Hello Kitty pattern used in Blended.", "type_str": "figure", "uris": null}, "FIGREF5": {"num": null, "fig_num": "4", "text": "Figure 4: The trigger mask used in TrojanNN.", "type_str": "figure", "uris": null}, "FIGREF6": {"num": null, "fig_num": null, "text": "As shown in Figure5, we choose one image from ImageNet-100 and visualize the examples of various poisoned samples mentioned above.", "type_str": "figure", "uris": null}, "FIGREF8": {"num": null, "fig_num": "5", "text": "Figure 5: Examples of various types of poisoned samples.", "type_str": "figure", "uris": null}, "TABREF1": {"num": null, "content": "<table><tr><td>Method</td><td>Clean Data</td><td colspan=\"14\">BadNets TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ Blended SIG TrojanNN SSBA WaNet Average</td></tr><tr><td>STRIP</td><td>4%</td><td colspan=\"6\">94.22 10.99 32.82 11.12 100.00 10.98</td><td colspan=\"3\">99.73 10.05 81.87</td><td>9.33</td><td>3.82</td><td colspan=\"3\">10.45 68.74 10.49</td></tr><tr><td>SS</td><td>4%</td><td colspan=\"4\">61.62 48.85 61.40 48.87</td><td colspan=\"2\">60.89 48.92</td><td colspan=\"8\">59.53 49.06 58.02 49.21 57.22 49.29 59.78 49.03</td></tr><tr><td>SCAn</td><td>4%</td><td>96.49</td><td>2.82</td><td>93.49</td><td>2.80</td><td>99.47</td><td>2.59</td><td>99.90</td><td>2.85</td><td>92.49</td><td>2.83</td><td>90.93</td><td>2.99</td><td>95.46</td><td>2.81</td></tr><tr><td>Frequency</td><td>4%</td><td colspan=\"4\">88.98 18.71 82.80 18.70</td><td colspan=\"10\">48.07 20.79 100.00 11.40 85.84 19.81 40.02 20.61 74.29 18.34</td></tr><tr><td>CT</td><td>4%</td><td>97.24</td><td>0.18</td><td>97.78</td><td>1.02</td><td>99.16</td><td>0.74</td><td colspan=\"2\">100.00 0.13</td><td>98.31</td><td>0.10</td><td>95.16</td><td>0.70</td><td>97.94</td><td>0.48</td></tr><tr><td>D-BR</td><td>0%</td><td>87.13</td><td>3.36</td><td>23.93</td><td>7.60</td><td>94.40</td><td>2.56</td><td colspan=\"3\">80.85 10.28 10.07</td><td>8.93</td><td>10.18</td><td>8.87</td><td>51.09</td><td>6.93</td></tr><tr><td>SPECTRE</td><td>0%</td><td colspan=\"4\">94.00 20.62 95.31 20.49</td><td>8.16</td><td>29.11</td><td colspan=\"8\">80.07 22.00 97.44 20.28 88.24 21.19 77.20 22.29</td></tr><tr><td>VDC (Ours)</td><td>0%</td><td>99.93</td><td>2.75</td><td>99.87</td><td>2.75</td><td>99.84</td><td>2.75</td><td>99.93</td><td>2.75</td><td>99.91</td><td>2.75</td><td>99.96</td><td>2.75</td><td>99.91</td><td>2.75</td></tr></table>", "text": "Comparison of TPR (%) and FPR (%) for poisoned sample detection on CIFAR-10. η = 0.09, i.e., 500 poisoned samples per class. Average is the mean of results of different triggers. Top 2 are bold.", "type_str": "table", "html": null}, "TABREF2": {"num": null, "content": "<table><tr><td/><td/><td/><td colspan=\"9\">Dataset: ImageNet-100 η = 0.099 (50 poisoned samples per class)</td><td/><td/><td/><td/></tr><tr><td>Method</td><td>Clean Data</td><td colspan=\"14\">BadNets TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ Blended SIG TrojanNN SSBA WaNet Average</td></tr><tr><td>STRIP</td><td>4%</td><td colspan=\"10\">92.20 12.56 99.19 11.79 100.00 10.95 100.00 13.14 99.74 11.26</td><td>3.11</td><td colspan=\"3\">11.32 82.37 11.84</td></tr><tr><td>SS</td><td>4%</td><td colspan=\"4\">48.12 50.21 44.95 50.55</td><td colspan=\"2\">44.95 50.55</td><td colspan=\"8\">44.95 50.55 44.95 50.55 45.13 50.53 45.51 50.49</td></tr><tr><td>SCAn</td><td>4%</td><td>97.01</td><td>1.66</td><td>97.58</td><td>2.46</td><td>99.21</td><td>1.21</td><td>99.92</td><td>1.77</td><td>87.39</td><td>1.36</td><td>58.97</td><td>2.54</td><td>90.01</td><td>1.83</td></tr><tr><td>Frequency</td><td>4%</td><td>1.52</td><td>1.59</td><td>1.31</td><td>1.59</td><td>1.72</td><td>1.59</td><td>95.05</td><td>1.59</td><td>3.41</td><td>1.59</td><td>0.04</td><td>1.59</td><td>17.18</td><td>1.59</td></tr><tr><td>CT</td><td>4%</td><td>94.16</td><td>0.37</td><td>99.21</td><td>0.37</td><td>99.35</td><td>0.06</td><td>99.84</td><td>0.58</td><td>91.47</td><td>0.39</td><td>0.00</td><td>0.69</td><td>80.67</td><td>0.41</td></tr><tr><td>D-BR</td><td>0%</td><td colspan=\"2\">86.43 23.56</td><td>9.56</td><td>10.03</td><td colspan=\"2\">76.09 15.07</td><td>16.53</td><td>9.06</td><td colspan=\"3\">11.09 10.15 10.08</td><td>9.87</td><td colspan=\"2\">34.96 12.96</td></tr><tr><td>SPECTRE</td><td>0%</td><td colspan=\"4\">48.57 50.16 44.95 50.55</td><td colspan=\"2\">44.95 50.55</td><td colspan=\"8\">44.97 50.55 44.95 50.55 45.09 50.54 45.58 50.48</td></tr><tr><td>VDC (Ours)</td><td>0%</td><td>99.92</td><td>1.55</td><td>99.94</td><td>1.55</td><td>99.90</td><td>1.55</td><td>99.96</td><td>1.55</td><td>99.98</td><td>1.55</td><td>99.94</td><td>1.55</td><td>99.94</td><td>1.55</td></tr></table>", "text": "Comparison of TPR (%) and FPR (%) for poisoned sample detection on ImageNet-100. η = 0.099, i.e., 50 poisoned samples per class. Average is the mean of results of different triggers. Top 2 are bold.", "type_str": "table", "html": null}, "TABREF3": {"num": null, "content": "<table><tr><td>Method</td><td>Clean Data</td><td colspan=\"14\">BadNets TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ Blended SIG TrojanNN SSBA WaNet Average</td></tr><tr><td>Strip</td><td>4%</td><td colspan=\"10\">93.19 10.88 82.78 11.18 97.08 11.95 98.61 11.32 95.42 10.62</td><td>3.47</td><td>9.42</td><td colspan=\"2\">78.43 10.90</td></tr><tr><td>SS</td><td>4%</td><td colspan=\"14\">19.86 21.66 17.64 21.88 21.94 21.46 23.89 21.26 23.75 21.28 12.50 22.39 19.93 21.66</td></tr><tr><td>SCAn</td><td>4%</td><td>98.06</td><td>0.01</td><td>72.36</td><td>9.15</td><td>78.61</td><td>0.03</td><td>62.22</td><td>0.23</td><td>84.44</td><td>0.15</td><td>12.54</td><td>2.12</td><td>68.04</td><td>1.95</td></tr><tr><td>Frequency</td><td>4%</td><td colspan=\"14\">83.89 45.48 50.00 45.45 44.03 45.48 95.97 44.64 61.94 45.45 36.53 45.65 62.06 45.36</td></tr><tr><td>CT</td><td>4%</td><td>92.50</td><td>0.99</td><td>84.31</td><td>0.58</td><td>15.41</td><td>0.99</td><td>98.06</td><td>0.44</td><td>88.89</td><td>0.29</td><td>0.00</td><td>0.92</td><td>63.20</td><td>0.70</td></tr><tr><td>D-BR</td><td>0%</td><td>8.61</td><td>8.85</td><td>9.31</td><td>8.85</td><td>10.83</td><td>9.22</td><td>9.72</td><td>9.01</td><td>8.75</td><td>9.15</td><td>8.47</td><td>9.12</td><td>9.28</td><td>9.03</td></tr><tr><td>SPECTRE</td><td>0%</td><td colspan=\"14\">99.44 45.11 77.64 47.27 99.86 45.07 97.50 45.30 96.94 45.36 53.19 49.68 87.43 46.30</td></tr><tr><td>VDC (Ours)</td><td>0%</td><td>98.89</td><td>4.12</td><td>97.50</td><td>4.12</td><td>98.61</td><td>4.12</td><td>99.31</td><td>4.12</td><td>98.89</td><td>4.12</td><td>98.89</td><td>4.12</td><td>98.68</td><td>4.12</td></tr></table>", "text": "Comparison of TPR (%) and FPR (%) for poisoned sample detection on ImageNet-Dog. η = 0.09, i.e., 80 poisoned samples per class. Average is the mean of results of different triggers. Top 2 are bold.", "type_str": "table", "html": null}, "TABREF4": {"num": null, "content": "<table><tr><td>Method</td><td>Clean Data</td><td colspan=\"3\">CIFAR-10 η = 0.4 Symmetric Asymmetric</td><td colspan=\"3\">ImageNet-100 η = 0.4 Symmetric Asymmetric</td><td colspan=\"2\">ImageNet-Dog η = 0.4 Symmetric Asymmetric</td></tr><tr><td/><td/><td colspan=\"8\">TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓</td></tr><tr><td>BHN</td><td colspan=\"2\">20% 80.88 2.98</td><td colspan=\"2\">83.13 3.24</td><td>57.04 1.94</td><td colspan=\"2\">16.24 0.96</td><td>14.36 0.23</td><td>22.54 0.75</td></tr><tr><td>CORES</td><td>0%</td><td>92.11 4.85</td><td>5.36</td><td>4.47</td><td>77.22 2.06</td><td>0.05</td><td>0.07</td><td colspan=\"2\">84.44 23.87 44.04 24.47</td></tr><tr><td>CL</td><td>0%</td><td>85.05 8.75</td><td colspan=\"2\">82.49 4.50</td><td colspan=\"5\">67.32 19.07 43.62 17.82 90.78 71.37 61.97 46.86</td></tr><tr><td>SimiFeat-V</td><td>0%</td><td>98.80 4.13</td><td colspan=\"2\">59.67 7.43</td><td>98.31 5.52</td><td colspan=\"4\">55.67 17.65 89.59 11.73 51.85 22.10</td></tr><tr><td>SimiFeat-R</td><td>0%</td><td>99.16 5.11</td><td colspan=\"3\">79.46 15.18 99.27 8.22</td><td colspan=\"4\">69.59 27.25 95.86 17.90 66.39 35.07</td></tr><tr><td>VDC (Ours)</td><td>0%</td><td>98.81 2.61</td><td colspan=\"2\">99.60 2.62</td><td>94.79 1.55</td><td colspan=\"2\">92.34 1.55</td><td>97.30 7.90</td><td>91.97 7.90</td></tr></table>", "text": "Comparison of TPR (%) and FPR (%) for noisy label detection on the three datasets under different types of noisy labels, where noisy ratio η = 0.4. Top 2 are bold.", "type_str": "table", "html": null}, "TABREF5": {"num": null, "content": "<table><tr><td>Method</td><td>Clean Data</td><td colspan=\"14\">BadNets TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ Blended SIG TrojanNN SSBA WaNet Average</td></tr><tr><td>STRIP</td><td>4%</td><td colspan=\"9\">50.59 12.41 27.40 11.74 50.83 12.09 50.31 12.72 43.54</td><td>9.39</td><td>4.98</td><td colspan=\"3\">11.64 37.94 11.67</td></tr><tr><td>SS</td><td>4%</td><td colspan=\"14\">51.63 49.61 52.77 49.34 53.80 49.10 50.91 49.78 51.45 49.65 50.57 49.86 51.86 49.56</td></tr><tr><td>SCAn</td><td>4%</td><td>45.38</td><td>0.00</td><td>45.64</td><td>3.80</td><td>50.71</td><td>1.77</td><td>47.46</td><td>0.00</td><td>44.67</td><td>0.01</td><td>43.89</td><td>4.07</td><td>46.29</td><td>1.61</td></tr><tr><td>Frequency</td><td>4%</td><td colspan=\"14\">52.22 18.65 49.21 18.66 34.08 20.71 53.18 11.44 51.44 19.73 30.14 20.53 45.05 18.29</td></tr><tr><td>CT</td><td>4%</td><td>45.22</td><td>0.32</td><td>47.88</td><td>0.39</td><td>48.37</td><td>0.22</td><td>49.41</td><td>1.61</td><td>46.67</td><td>0.38</td><td>46.00</td><td>0.69</td><td>47.26</td><td>0.60</td></tr><tr><td>D-BR</td><td>0%</td><td>31.92</td><td>0.00</td><td>12.09</td><td>1.07</td><td>1.20</td><td>1.42</td><td colspan=\"2\">16.13 17.73</td><td>0.01</td><td>0.02</td><td>3.08</td><td>3.20</td><td>10.74</td><td>3.91</td></tr><tr><td>SPECTRE</td><td>0%</td><td colspan=\"14\">23.42 22.07 22.49 22.29 26.09 27.51 22.41 22.32 21.67 22.49 34.22 23.96 25.05 23.44</td></tr><tr><td>BHN</td><td>20%</td><td>68.40</td><td>1.27</td><td>69.19</td><td>1.34</td><td>70.35</td><td>1.35</td><td>72.61</td><td>1.12</td><td>67.81</td><td>1.26</td><td>69.43</td><td>1.34</td><td>69.63</td><td>1.28</td></tr><tr><td>CL</td><td>0%</td><td>49.69</td><td>0.80</td><td>33.11</td><td>0.70</td><td>34.32</td><td>0.53</td><td>33.21</td><td>0.51</td><td>33.85</td><td>0.67</td><td>33.77</td><td>0.74</td><td>36.33</td><td>0.66</td></tr><tr><td>CORES</td><td>0%</td><td>66.73</td><td>2.29</td><td>47.59</td><td>2.46</td><td colspan=\"3\">30.41 15.94 47.27</td><td>2.18</td><td>48.03</td><td>2.55</td><td>49.76</td><td>2.92</td><td>48.30</td><td>4.72</td></tr><tr><td>SimiFeat-V</td><td>0%</td><td>80.02</td><td>4.71</td><td>77.94</td><td>5.06</td><td>66.72</td><td>5.31</td><td>52.60</td><td>4.89</td><td>85.73</td><td>4.72</td><td>87.48</td><td>4.80</td><td>75.08</td><td>4.92</td></tr><tr><td>SimiFeat-R</td><td>0%</td><td>81.36</td><td>4.57</td><td>79.12</td><td>5.48</td><td>66.23</td><td>6.17</td><td>52.42</td><td>4.93</td><td>80.85</td><td>5.24</td><td>89.19</td><td>4.93</td><td>74.86</td><td>5.22</td></tr><tr><td>VDC (Ours)</td><td>0%</td><td>99.42</td><td>2.79</td><td>99.40</td><td>2.79</td><td>99.39</td><td>2.79</td><td>99.42</td><td>2.79</td><td>99.41</td><td>2.79</td><td>99.43</td><td>2.79</td><td>99.41</td><td>2.79</td></tr></table>", "text": "Comparison of TPR (%) and FPR (%) for detecting the mixture of poisoned sampels and noisy labels on CIFAR-10, where poisoning ratio η1 and noisy ratio η2 are set as 0.1. Top 2 are bold.Dataset: CIFAR-10 poisoning ratio η 1 = 0.09 noisy ratio η 2 = 0.1", "type_str": "table", "html": null}, "TABREF6": {"num": null, "content": "<table><tr><td>Method</td><td colspan=\"14\">BadNets ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ Blended SIG TrojanNN SSBA WaNet Average</td></tr><tr><td colspan=\"15\">No detection 96.31 92.33 98.16 93.30 99.99 93.51 100.00 93.43 98.16 92.66 95.38 92.89 98.00 93.02</td></tr><tr><td>Strip</td><td>1.82</td><td colspan=\"2\">92.38 98.18</td><td>92.9</td><td>0.23</td><td>93.15</td><td>81.38</td><td colspan=\"7\">93.61 79.69 92.26 94.68 92.95 59.33 92.88</td></tr><tr><td>SS</td><td colspan=\"6\">89.84 86.43 94.49 87.59 99.97 86.09</td><td>99.79</td><td colspan=\"2\">88.17 96.79</td><td>87.2</td><td colspan=\"4\">80.77 85.19 93.61 86.78</td></tr><tr><td>SCAn</td><td>0.97</td><td colspan=\"4\">93.39 32.21 93.53 20.11</td><td>92.5</td><td>7.51</td><td colspan=\"3\">93.34 17.67 92.55</td><td>5.17</td><td colspan=\"3\">93.04 13.94 93.06</td></tr><tr><td>Frequency</td><td colspan=\"2\">75.71 92.05</td><td>87.3</td><td>91.9</td><td colspan=\"2\">99.89 91.95</td><td>1.27</td><td colspan=\"2\">93.12 66.58</td><td>90.2</td><td>91.88</td><td>91.6</td><td colspan=\"2\">70.44 91.80</td></tr><tr><td>CT</td><td>0.82</td><td>92.83</td><td>1.93</td><td>93.4</td><td>1.26</td><td>92.91</td><td>2.31</td><td>93.2</td><td>3.19</td><td>93.09</td><td>2.36</td><td>93.09</td><td>1.98</td><td>93.09</td></tr><tr><td>D-BR</td><td colspan=\"6\">88.14 93.23 95.78 91.44 99.97 93.82</td><td>100</td><td colspan=\"7\">93.21 97.42 92.54 95.54 92.33 96.14 92.76</td></tr><tr><td>SPECTRE</td><td colspan=\"3\">71.89 87.92 45.57</td><td>87.6</td><td>99.9</td><td>86.76</td><td>97.6</td><td>87.76</td><td>4.77</td><td colspan=\"5\">88.31 18.53 88.19 56.38 87.76</td></tr><tr><td>VDC (Ours)</td><td>0.86</td><td>93.32</td><td>1.23</td><td>92.24</td><td>1.24</td><td>93.15</td><td>4.41</td><td>93.88</td><td>1.12</td><td>93.11</td><td>0.94</td><td>93.57</td><td>1.63</td><td>93.21</td></tr></table>", "text": "Comparison of ASR (%) and ACC (%) for training on the purified CIFAR-10 with poisoning ratio η = 0.09. Top 2 are bold.", "type_str": "table", "html": null}, "TABREF7": {"num": null, "content": "<table><tr><td>Dataset</td><td>Method</td><td>BadNets TPR FPR</td><td>Blended TPR FPR TPR FPR SIG</td><td>TrojanNN TPR FPR</td><td>SSBA TPR FPR</td><td>WaNet TPR</td><td>FPR</td></tr><tr><td>CIFAR-10 η = 0.09</td><td colspan=\"7\">VDC-CLIP VDC (Ours) 99.93 2.75 99.87 2.75 99.84 2.75 99.93 2.75 99.91 2.75 99.96 2.75 41.89 1.98 48.02 1.98 28.37 1.98 38.07 1.98 51.95 1.98 34.60 1.98</td></tr><tr><td>CIFAR-10 η = 0.009</td><td colspan=\"7\">VDC-CLIP VDC (Ours) 100.00 2.72 99.56 2.72 99.78 2.72 100.00 2.72 99.78 2.72 100.00 2.72 41.55 2.83 49.33 2.83 29.33 2.83 39.56 2.83 52.00 2.83 36.78 2.83</td></tr><tr><td>ImageNet-100 η = 0.099</td><td colspan=\"7\">VDC-CLIP VDC (Ours) 99.92 1.55 99.94 1.55 99.90 1.55 99.96 1.55 99.98 1.55 99.94 1.55 80.81 1.84 77.52 1.84 82.34 1.84 70.14 1.84 77.90 1.84 82.20 1.84</td></tr><tr><td>ImageNet-100 η = 0.0099</td><td colspan=\"7\">VDC-CLIP VDC (Ours) 99.80 1.55 100.00 1.55 99.80 1.55 100.00 1.55 100.00 1.55 99.80 1.55 81.40 1.75 77.00 1.75 82.60 1.75 71.11 1.75 77.78 1.75 83.23 1.75</td></tr><tr><td>ImageNet-Dog η = 0.09</td><td colspan=\"7\">VDC-CLIP VDC (Ours) 98.89 4.12 97.50 4.12 98.61 4.12 99.31 4.12 98.89 4.12 98.89 4.12 12.50 3.23 4.44 3.23 7.22 3.23 11.94 3.23 4.58 3.23 9.58 3.23</td></tr><tr><td colspan=\"4\">C MORE IMPLEMENTATION DETAILS</td><td/><td/><td/><td/></tr><tr><td colspan=\"4\">C.1 DETAILS OF TRAINING ON THE PURIFIED DATASETS</td><td/><td/><td/><td/></tr></table>", "text": "Comparsion of TPR (%) and FPR (%) on the poisoned sample detection. VDC-CLIP denotes the naive approach with CLIP.", "type_str": "table", "html": null}, "TABREF8": {"num": null, "content": "<table/>", "text": "defines the p-values based on the neural network with the clean data. The p-values are then applied to the multiple hypothesis testing to detect corrupted examples. In our evaluation, we set leave ratio as 0.4. We use ResNet-18 for all datasets, and training epochs is set to be 200. CORES CORES (<PERSON> et al., 2021) trains ResNet-34 on the noisy dataset and uses its proposed sample sieve to filter out the corrupted examples. In our experiments, we adopt its default setting during training and calculate the F1 of the sieved out corrupted examples. The training epochs is set as 40.CL CL(<PERSON><PERSON><PERSON> et al., 2021) detects corrupted labels by firstly estimating probabilistic thresholds to characterize the label noise, ranking examples based on model predictions, then filtering out corrupted examples based on ranking and thresholds.In our experiments, we train ResNet-18 on the noisy dataset and call the functions of Cleanlab 1 to detect noisy labels.", "type_str": "table", "html": null}, "TABREF9": {"num": null, "content": "<table/>", "text": "and 12 show the examples of label-specific visual questions on ImageNet-100.", "type_str": "table", "html": null}, "TABREF10": {"num": null, "content": "<table><tr><td>Dataset: CIFAR-10, ImageNet-100</td></tr></table>", "text": "The prompts used for the generation of label-specific visual questions for different datasets with ChatGPT. {label i } represents the label name of class i, {n} denotes the number questions of each lable.Prompt: Please generate some visual questions to ask a multimodal large language model to identify if the label of an image is correct. These questions will help determine if the object in the image corresponds to the given label.Remember that the goal is to ask questions that would lead to a 'yes' answer if the label is correct.The labels are ['{label 1 }',• • • ,'{label k }'],generate {n} the most insightful questions for each label. For example, if the label is 'airplane', the possible questions could be: Can the object in the image be used to fly in the air?Dataset: ImageNet-DogPrompt: Please generate some visual questions to ask a multimodal large language model to identify if the label of an image is correct. These questions will help determine if the breed of the dog in the image corresponds to the given label. Remember that the goal is to ask visual questions that would lead to a 'yes' answer if the label is correct.The labels are ['{label 1 }',• • • ,'{label k }'], generate {n} different questions for each label, such as the breed, attributes. The questions of each label should be used to judge different breeds. For example, if the label is 'Chihuahua', the possible questions could be: Does the dog in the image have any distinct features of a Chihuahua?", "type_str": "table", "html": null}, "TABREF11": {"num": null, "content": "<table><tr><td>Prompt: Assume you are a helpful and precise assistant for evaluation. Please judge</td></tr><tr><td>whether the 'Caption' of an image and one of the 'Label' refer to the same</td></tr><tr><td>object. Answer with yes or no.</td></tr><tr><td>-Caption: '{response}'</td></tr><tr><td>-Label: '{label}'</td></tr></table>", "text": "The prompts used for the evaluation of the response of MLLM with ChatGPT. {label} represents the label name, {response} represents the response of MLLM in visual qunestion answering module.", "type_str": "table", "html": null}, "TABREF12": {"num": null, "content": "<table/>", "text": "The list of general questions for image description.", "type_str": "table", "html": null}, "TABREF13": {"num": null, "content": "<table><tr><td/><td/><td/><td colspan=\"9\">Dataset: CIFAR-10 η = 0.009 (50 poisoned samples per class)</td><td/><td/><td/><td/></tr><tr><td>Method</td><td>Clean Data</td><td colspan=\"14\">BadNets TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ Blended SIG TrojanNN SSBA WaNet Average</td></tr><tr><td>STRIP</td><td>4%</td><td colspan=\"2\">86.22 11.67</td><td>4.22</td><td>10.86</td><td colspan=\"2\">99.56 11.27</td><td>99.78</td><td>9.84</td><td>65.11</td><td>9.87</td><td>2.44</td><td>9.98</td><td colspan=\"2\">59.56 10.58</td></tr><tr><td>SS</td><td>4%</td><td colspan=\"6\">97.56 12.72 99.78 12.70 100.00 12.69</td><td>3.33</td><td>13.57</td><td colspan=\"2\">99.33 12.70</td><td colspan=\"4\">92.00 12.77 82.00 12.86</td></tr><tr><td>SCAn</td><td>4%</td><td>92.22</td><td>2.28</td><td>87.78</td><td>1.92</td><td>99.78</td><td>2.83</td><td>99.78</td><td>2.81</td><td>88.00</td><td>2.28</td><td>34.54</td><td>2.65</td><td>83.68</td><td>2.46</td></tr><tr><td>Frequency</td><td>4%</td><td colspan=\"4\">89.11 21.51 84.22 21.55</td><td colspan=\"4\">48.67 21.76 100.00 19.32</td><td colspan=\"2\">85.56 21.66</td><td colspan=\"4\">39.78 21.72 74.56 21.25</td></tr><tr><td>CT</td><td>4%</td><td>97.56</td><td>1.32</td><td>99.50</td><td>1.66</td><td colspan=\"2\">100.00 1.01</td><td colspan=\"2\">100.00 3.92</td><td colspan=\"2\">100.00 1.82</td><td>76.00</td><td>2.58</td><td>95.51</td><td>2.05</td></tr><tr><td>D-BR</td><td>0%</td><td>0.44</td><td>0.91</td><td>0.00</td><td>0.90</td><td>0.00</td><td>0.90</td><td>11.11</td><td>0.78</td><td>1.11</td><td>0.91</td><td>1.33</td><td>0.89</td><td>2.33</td><td>0.88</td></tr><tr><td>SPECTRE</td><td>0%</td><td>98.00</td><td>5.91</td><td>99.78</td><td>5.90</td><td colspan=\"2\">100.00 5.89</td><td colspan=\"2\">100.00 5.89</td><td>99.33</td><td>5.90</td><td>91.56</td><td>5.97</td><td>98.11</td><td>5.91</td></tr><tr><td>VDC (Ours)</td><td>0%</td><td colspan=\"2\">100.00 2.72</td><td>99.56</td><td>2.72</td><td>99.78</td><td>2.72</td><td colspan=\"2\">100.00 2.72</td><td>99.78</td><td>2.72</td><td colspan=\"2\">100.00 2.72</td><td>99.85</td><td>2.72</td></tr></table>", "text": "Comparison of TPR (%) and FPR (%) for poisoned sample detection on CIFAR-10. η = 0.009, i.e., 50 poisoned samples per class. Average is the mean of results of different triggers.", "type_str": "table", "html": null}, "TABREF14": {"num": null, "content": "<table><tr><td>Method</td><td>Clean Data</td><td colspan=\"14\">BadNets TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ TPR↑ FPR↓ Blended SIG TrojanNN SSBA WaNet Average</td></tr><tr><td>STRIP</td><td>4%</td><td colspan=\"2\">89.70 11.94</td><td colspan=\"4\">79.39 11.36 100.00 11.03</td><td colspan=\"2\">98.99 11.09</td><td colspan=\"2\">99.60 11.61</td><td>1.62</td><td colspan=\"3\">12.50 78.22 11.59</td></tr><tr><td>SS</td><td>4%</td><td colspan=\"2\">48.08 49.92</td><td colspan=\"2\">54.14 49.86</td><td colspan=\"2\">47.47 49.92</td><td colspan=\"2\">48.08 49.92</td><td colspan=\"6\">49.49 49.90 50.91 49.89 49.70 49.90</td></tr><tr><td>SCAn</td><td>4%</td><td>96.16</td><td>2.49</td><td>87.47</td><td>1.95</td><td>88.89</td><td>2.83</td><td>98.99</td><td>1.81</td><td>86.46</td><td>2.12</td><td>97.37</td><td>2.91</td><td>92.56</td><td>2.35</td></tr><tr><td>Frequency</td><td>4%</td><td>1.62</td><td>1.57</td><td>1.21</td><td>1.57</td><td>1.62</td><td>1.57</td><td>94.75</td><td>1.57</td><td>3.03</td><td>1.57</td><td>0.00</td><td>1.57</td><td>17.04</td><td>1.57</td></tr><tr><td>CT</td><td>4%</td><td>96.77</td><td>0.01</td><td>80.20</td><td>0.46</td><td>0.00</td><td>0.94</td><td colspan=\"2\">100.00 0.26</td><td>90.30</td><td>1.19</td><td>0.00</td><td>0.06</td><td>61.21</td><td>0.49</td></tr><tr><td>D-BR</td><td>0%</td><td>1.01</td><td>1.99</td><td>1.41</td><td>1.65</td><td>0.00</td><td>1.98</td><td>0.81</td><td>1.81</td><td>1.21</td><td>1.77</td><td>1.21</td><td>1.82</td><td>0.94</td><td>1.84</td></tr><tr><td>SPECTRE</td><td>0%</td><td colspan=\"2\">60.20 49.80</td><td colspan=\"2\">74.95 49.65</td><td colspan=\"2\">92.12 49.48</td><td colspan=\"2\">61.62 49.78</td><td colspan=\"6\">71.11 49.69 63.23 49.77 70.54 49.70</td></tr><tr><td>VDC (Ours)</td><td>0%</td><td>99.80</td><td>1.55</td><td colspan=\"2\">100.00 1.55</td><td>99.80</td><td>1.55</td><td colspan=\"2\">100.00 1.55</td><td colspan=\"2\">100.00 1.55</td><td>99.80</td><td>1.55</td><td>99.90</td><td>1.55</td></tr></table>", "text": "Comparison of TPR (%) and FPR (%) for poisoned sample detection on ImageNet-100. η = 0.0099, i.e., 5 poisoned samples per class. Average is the mean of results of different triggers.", "type_str": "table", "html": null}, "TABREF15": {"num": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">BadNets</td><td colspan=\"2\">Blended</td><td colspan=\"2\">SIG</td><td colspan=\"2\">TrojanNN</td><td colspan=\"2\">SSBA</td><td colspan=\"2\">WaNet</td><td colspan=\"2\">Average</td></tr><tr><td/><td colspan=\"14\">ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑</td></tr><tr><td colspan=\"15\">No detection 91.83 93.67 74.00 93.63 99.64 93.50 99.99 93.56 72.86 93.70 13.18 93.37 75.25 93.57</td></tr><tr><td>Strip</td><td>0.73</td><td colspan=\"3\">93.27 69.97 93.19</td><td>0.27</td><td>93.15</td><td>2.7</td><td>92.02</td><td>4.53</td><td>93.7</td><td colspan=\"4\">10.79 93.03 14.83 93.06</td></tr><tr><td>SS</td><td>0.97</td><td>92.62</td><td>0.98</td><td>92.9</td><td>0.41</td><td colspan=\"3\">92.77 99.94 92.74</td><td>1.21</td><td>92.76</td><td>0.89</td><td colspan=\"3\">93.15 17.40 92.82</td></tr><tr><td>SCAn</td><td>0.6</td><td>93.38</td><td>1.59</td><td>93.09</td><td>0.23</td><td>93.19</td><td>3.92</td><td>92.89</td><td>1.52</td><td colspan=\"3\">93.62 21.44 93.73</td><td>4.88</td><td>93.32</td></tr><tr><td>Frequency</td><td>0.86</td><td>92.54</td><td>5.83</td><td colspan=\"2\">93.15 98.44</td><td>92.4</td><td>2.17</td><td>92.69</td><td>2.33</td><td>93.01</td><td>6.32</td><td colspan=\"3\">91.62 19.33 92.57</td></tr><tr><td>CT</td><td>0.79</td><td>93.24</td><td>0.71</td><td>93.94</td><td>0.12</td><td>93.7</td><td>3.96</td><td>93.17</td><td>0.57</td><td>93.76</td><td>1.32</td><td>93.55</td><td>1.25</td><td>93.56</td></tr><tr><td>D-BR</td><td>90.97</td><td>93.4</td><td colspan=\"6\">73.98 93.62 99.58 94.21 99.98 93.86</td><td>68</td><td colspan=\"5\">93.06 18.82 93.73 75.22 93.65</td></tr><tr><td>SPECTRE</td><td>0.87</td><td>92.89</td><td>1.26</td><td>92.94</td><td>0.21</td><td>92.99</td><td>4.1</td><td>92.96</td><td>1.06</td><td>92.92</td><td>1.07</td><td>92.9</td><td>1.43</td><td>92.93</td></tr><tr><td>VDC (Ours)</td><td>0.61</td><td>93.29</td><td>0.69</td><td>93.73</td><td>0.31</td><td>93.14</td><td>3.10</td><td>93.47</td><td>1.02</td><td>93.72</td><td>0.76</td><td>93.74</td><td>1.08</td><td>93.52</td></tr></table>", "text": "Comparison of ASR (%) and ACC (%) for training on the purified CIFAR-10 with poisoning ratio η = 0.009.", "type_str": "table", "html": null}, "TABREF16": {"num": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">CIFAR-10 η = 0.4</td><td colspan=\"2\">ImageNe-100 η = 0.4</td><td colspan=\"2\">ImageNet-Dog η = 0.4</td></tr><tr><td/><td colspan=\"6\">Symmetric Asymmetric Symmetric Asymmetric Symmetric Asymmetric</td></tr><tr><td>No detection</td><td>61.84</td><td>56.09</td><td>31.21</td><td>32.65</td><td>28.45</td><td>31.35</td></tr><tr><td>BHN</td><td>88.71</td><td>89.21</td><td>40.12</td><td>44.25</td><td>31.65</td><td>38.90</td></tr><tr><td>CORES</td><td>84.68</td><td>84.70</td><td>38.41</td><td>41.87</td><td>18.70</td><td>37.05</td></tr><tr><td>CL</td><td>87.82</td><td>57.94</td><td>39.19</td><td>46.98</td><td>18.25</td><td>30.00</td></tr><tr><td>SimiFeat-V</td><td>89.39</td><td>74.70</td><td>37.81</td><td>41.68</td><td>33.70</td><td>34.90</td></tr><tr><td>SimiFeat-R</td><td>90.48</td><td>80.54</td><td>38.31</td><td>39.70</td><td>31.86</td><td>28.80</td></tr><tr><td>VDC (Ours)</td><td>90.75</td><td>90.89</td><td>66.84</td><td>69.32</td><td>46.54</td><td>48.80</td></tr></table>", "text": "Comparison of ACC (%) for training on the purified datasets with noisy labels.", "type_str": "table", "html": null}, "TABREF17": {"num": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">BadNets</td><td colspan=\"2\">Blended</td><td/><td>SIG</td><td colspan=\"2\">TrojanNN</td><td colspan=\"2\">SSBA</td><td colspan=\"2\">WaNet</td><td colspan=\"2\">Average</td></tr><tr><td colspan=\"5\">ASR↓ ACC↑ ASR↓ Strip 1.64 85.84 96.13 85.24</td><td>0.98</td><td>85.97</td><td>1.82</td><td colspan=\"7\">84.35 57.91 84.66 93.89 85.25 42.06 85.22</td></tr><tr><td>SS</td><td colspan=\"6\">94.38 78.33 95.31 81.33 99.96 81.58</td><td>99.97</td><td colspan=\"7\">80.56 92.81 78.45 69.07 77.34 91.92 79.60</td></tr><tr><td>SCAn</td><td>2.18</td><td>86.9</td><td>4.87</td><td>85.75</td><td>4.91</td><td>85.17</td><td>4.2</td><td>86.99</td><td>3.97</td><td>86.12</td><td>5.44</td><td>83.41</td><td>4.26</td><td>85.72</td></tr><tr><td>Frequency</td><td colspan=\"6\">75.73 85.04 76.38 83.84 99.77 84.85</td><td>3.01</td><td>85.4</td><td colspan=\"6\">72.79 82.05 89.23 84.08 69.49 84.21</td></tr><tr><td>CT</td><td>2.46</td><td>85.41</td><td>1.53</td><td>86.49</td><td>0.97</td><td>85.26</td><td>55.96</td><td>86.1</td><td>9.18</td><td>84.49</td><td>5.39</td><td colspan=\"3\">86.46 12.58 85.70</td></tr><tr><td>D-BR</td><td colspan=\"2\">90.72 86.09</td><td>96.3</td><td colspan=\"3\">86.59 99.86 86.04</td><td>100</td><td colspan=\"7\">85.46 96.59 86.16 94.93 85.11 96.40 85.91</td></tr><tr><td>SPECTRE</td><td colspan=\"6\">96.71 82.51 96.89 84.62 99.91 80.34</td><td>100</td><td colspan=\"7\">84.46 97.29 83.69 10.02 84.76 83.47 83.40</td></tr><tr><td colspan=\"7\">BHN 73.04 91SimiFeat-V 1.21 92.38 71.01 92.21 99.96 84.76</td><td>99.99</td><td colspan=\"7\">85.42 95.74 84.07 92.97 84.35 76.81 87.20</td></tr><tr><td>SimiFeat-R</td><td>1.04</td><td colspan=\"5\">92.79 67.91 92.34 99.97 85.23</td><td>99.99</td><td colspan=\"7\">85.05 95.46 84.47 88.73 82.90 75.52 87.13</td></tr><tr><td>VDC (Ours)</td><td>1.01</td><td>92.58</td><td>1.13</td><td>91.73</td><td>3.07</td><td>91.67</td><td>4.59</td><td>92.79</td><td>1.39</td><td>92.06</td><td>0.99</td><td>92.63</td><td>2.03</td><td>92.24</td></tr></table>", "text": "Comparison of ASR (%) and ACC (%) for training on the purified CIFAR-10 with poisoning ratio η 1 = 0.09, noisy ratio η 1 = 0.1. : CIFAR-10 poisoning ratio η 1 = 0.09 noisy ratio η 2 = 0.1 ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ ASR↓ ACC↑ No detection 96.17 85.18 97.37 86.54 99.95 86.45 100.00 86.70 96.53 85.90 94.78 86.35 97.47 86.19 .22 50.73 91.68 99.98 85.61 100.00 85.13 95.77 84.54 88.11 83.40 84.61 86.93 CL 95.86 88.92 98.10 90.29 99.97 86.01 99.99 85.58 96.38 85.12 91.19 84.19 96.92 86.69 CORES 95.88 81.94 96.53 84.98 99.96 85.40 100.00 86.11 95.18 84.42 93.90 84.22 96.91 84.51", "type_str": "table", "html": null}}}}