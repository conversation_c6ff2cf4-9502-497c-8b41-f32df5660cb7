{"paper_id": "CAML", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:51:19.789918Z"}, "title": "CONTEXT-AWARE META-LEARNING", "authors": [{"first": "<PERSON>", "middle": [], "last": "Fifty", "suffix": "", "affiliation": {"laboratory": "", "institution": "Stanford University", "location": {}}, "email": "<EMAIL>"}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Stanford University", "location": {}}, "email": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Stanford University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Stanford University", "location": {}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Stanford University", "location": {}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Stanford University", "location": {}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Large Language Models like ChatGPT demonstrate a remarkable capacity to learn new concepts during inference without any fine-tuning. However, visual models trained to detect new objects during inference have been unable to replicate this ability, and instead either perform poorly or require meta-training and/or finetuning on similar objects. In this work, we propose a meta-learning algorithm that emulates Large Language Models by learning new visual concepts during inference without fine-tuning. Our approach leverages a frozen pre-trained feature extractor, and analogous to in-context learning, recasts visual meta-learning as sequence modeling over datapoints with known labels and a test datapoint with an unknown label. On 8 out of 11 few-shot image classification benchmarks, our approach-without meta-training or fine-tuning-exceeds or matches the state-ofthe-art algorithm, P>M>F, which is meta-trained on these benchmarks. Our code is available at https://github.com/cfifty/CAML.\nMeta-learning refers to a capacity to learn new concepts from a small number of demonstrations (<PERSON> et al., 2015) . In a decade of remarkable advances to machine intelligence, it remains an area where human performance continues to surpass that of machines (<PERSON> et al., 2020) . To match human capabilities, and towards developing machines that can learn and think like humans, we must develop machine intelligence capable of learning novel concepts from only a few examples (<PERSON> et al., 2017) .\nMany applications of deep learning apply a learning algorithm to a large set of training data; however, learning from a very small number of training examples poses a challenge (<PERSON> et al., 2017; <PERSON><PERSON><PERSON><PERSON> et al., 2018) . This challenge led to two predominant evaluation settings: in-domain and cross-domain. The in-domain setting evaluates a meta-learner's ability to quickly adapt to new tasks after training on similar tasks within a specific domain. Models designed for this setting are often extremely fast but exhibit poor generalization to tasks outside the target domain (Chen et al., 2019) . Meanwhile, the cross-domain setting evaluates a meta-learner's ability to adapt to tasks in previously unseen domains. Methods designed for this setting are highly adaptable but slow during inference as they require fine-tuning on the support set (Guo et al., 2020; Oh et al., 2022; Hu et al., 2022) . Critically, meta-learners in both settings differ from a human's capacity to quickly generalize to new tasks.\nThe problem of simultaneously fast and general meta-learning has recently been addressed in Natural Language by Large Language Models (LLMs). LLMs like ChatGPT can quickly generalize to new tasks through an ability termed in-context learning (Brown et al., 2020) . However, it remains an open problem in Computer Vision. Even the best visual meta-learning algorithms cannot be deployed to a ChatGPT-like system because such systems require models that can (1) generalize to a broad set of tasks unknown at training time and (2) do so in real-time, without the time allowance for finetuning the model. LLMs have shown a remarkable ability to do both; however, current visual meta-learners may only satisfy one requirement or the other (Hu et al., 2022) .\nTo measure progress towards this goal of fast and general visual meta-learners, we develop an evaluation paradigm that we call universal meta-learning. Universal meta-learning measures a model's capacity to quickly learn new image classes. It evaluates models across a diverse set of meta-learning benchmarks spanning many different image classification tasks without meta-training on any of the benchmarks' training sets or fine-tuning on the support set during inference. We focus on", "pdf_parse": {"paper_id": "CAML", "_pdf_hash": "", "abstract": [{"text": "Large Language Models like ChatGPT demonstrate a remarkable capacity to learn new concepts during inference without any fine-tuning. However, visual models trained to detect new objects during inference have been unable to replicate this ability, and instead either perform poorly or require meta-training and/or finetuning on similar objects. In this work, we propose a meta-learning algorithm that emulates Large Language Models by learning new visual concepts during inference without fine-tuning. Our approach leverages a frozen pre-trained feature extractor, and analogous to in-context learning, recasts visual meta-learning as sequence modeling over datapoints with known labels and a test datapoint with an unknown label. On 8 out of 11 few-shot image classification benchmarks, our approach-without meta-training or fine-tuning-exceeds or matches the state-ofthe-art algorithm, P>M>F, which is meta-trained on these benchmarks. Our code is available at https://github.com/cfifty/CAML.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "Meta-learning refers to a capacity to learn new concepts from a small number of demonstrations (<PERSON> et al., 2015) . In a decade of remarkable advances to machine intelligence, it remains an area where human performance continues to surpass that of machines (<PERSON> et al., 2020) . To match human capabilities, and towards developing machines that can learn and think like humans, we must develop machine intelligence capable of learning novel concepts from only a few examples (<PERSON> et al., 2017) .", "cite_spans": [{"start": 95, "end": 114, "text": "(<PERSON> et al., 2015)", "ref_id": "BIBREF26"}, {"start": 258, "end": 278, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF3"}, {"start": 477, "end": 496, "text": "(<PERSON> et al., 2017)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "Many applications of deep learning apply a learning algorithm to a large set of training data; however, learning from a very small number of training examples poses a challenge (<PERSON> et al., 2017; <PERSON><PERSON><PERSON><PERSON> et al., 2018) . This challenge led to two predominant evaluation settings: in-domain and cross-domain. The in-domain setting evaluates a meta-learner's ability to quickly adapt to new tasks after training on similar tasks within a specific domain. Models designed for this setting are often extremely fast but exhibit poor generalization to tasks outside the target domain (<PERSON> et al., 2019) . Meanwhile, the cross-domain setting evaluates a meta-learner's ability to adapt to tasks in previously unseen domains. Methods designed for this setting are highly adaptable but slow during inference as they require fine-tuning on the support set (<PERSON> et al., 2020; <PERSON> et al., 2022; <PERSON> et al., 2022) . Critically, meta-learners in both settings differ from a human's capacity to quickly generalize to new tasks.", "cite_spans": [{"start": 177, "end": 196, "text": "(<PERSON> et al., 2017;", "ref_id": "BIBREF27"}, {"start": 197, "end": 218, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF14"}, {"start": 578, "end": 597, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF6"}, {"start": 847, "end": 865, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF16"}, {"start": 866, "end": 882, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF33"}, {"start": 883, "end": 899, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "The problem of simultaneously fast and general meta-learning has recently been addressed in Natural Language by Large Language Models (LLMs). LLMs like ChatGPT can quickly generalize to new tasks through an ability termed in-context learning (<PERSON> et al., 2020) . However, it remains an open problem in Computer Vision. Even the best visual meta-learning algorithms cannot be deployed to a ChatGPT-like system because such systems require models that can (1) generalize to a broad set of tasks unknown at training time and (2) do so in real-time, without the time allowance for finetuning the model. LLMs have shown a remarkable ability to do both; however, current visual meta-learners may only satisfy one requirement or the other (<PERSON> et al., 2022) .", "cite_spans": [{"start": 242, "end": 262, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF3"}, {"start": 734, "end": 751, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}, {"text": "To measure progress towards this goal of fast and general visual meta-learners, we develop an evaluation paradigm that we call universal meta-learning. Universal meta-learning measures a model's capacity to quickly learn new image classes. It evaluates models across a diverse set of meta-learning benchmarks spanning many different image classification tasks without meta-training on any of the benchmarks' training sets or fine-tuning on the support set during inference. We focus on", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "the application of few-shot image classification-as opposed to dense prediction tasks like in-painting or segmentation-as the universal setting has already been explored for these applications (<PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) .", "cite_spans": [{"start": 193, "end": 211, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF1"}, {"start": 212, "end": 231, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF55"}, {"start": 232, "end": 250, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF48"}, {"start": 251, "end": 268, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF23"}, {"start": 269, "end": 288, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Beyond benchmarking methods in the universal setting, we present a meta-learner that achieves strong universal performance. Drawing inspiration from in-context learning in LLMs, we reformulate n-way-k-shot image classification as non-causal sequence modeling over the support set and an unknown query image. Specifically, given n-way classification with k-examples from each class, we train a non-causal model over {(x i , y i )} nk i=1 (image, label) support set pairs, and an unlabeled query image x nk+1 , to predict the label of the query image. This formulation causes the meta-learner to extrapolate to new classes in its parameter space, enabling it to learn new visual concepts during inference without fine-tuning. Due to its capacity to learn visual information \"in-context\", we term our approach Context-Aware Meta-Learning (CAML).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "In summary, our contribution is two-fold. First, we develop a meta-learning evaluation paradigm that approximates the performance of visual meta-learners in a ChatGPT-like application. Second, we design a meta-learning algorithm that works well in this setting. Our empirical findings show that CAML outperforms other meta-learners in the universal setting. Remarkably, CAML's performance in the universal setting often matches-and even exceeds-the in-domain performance of the state-ofthe-art meta-learning algorithm, P>M>F (<PERSON> et al., 2022) , that is directly trained on each down-stream benchmark.", "cite_spans": [{"start": 525, "end": 542, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Meta-Learning as Causal Sequence Modeling. Several of the earliest meta-learning algorithms were formulated as causal sequence modeling problems. <PERSON><PERSON><PERSON><PERSON> et al. (2001) leverage a LSTM (<PERSON><PERSON><PERSON><PERSON> & Schmidhuber, 1997) to model extensions to semi-linear and quadratic functions, and two decades later, <PERSON> et al. (2014) ; <PERSON><PERSON> et al. (2016) ; <PERSON> et al. (2017) build upon this approach by integrating a form of external memory that the LSTM can read to and write from memory to develop Neural Turing Machines. With the advent of self-attention (<PERSON><PERSON><PERSON><PERSON> et al., 2017) , <PERSON><PERSON><PERSON> et al. (2017) predict the labels of query images by first composing a sequence of (image, label) pairs and then feeding it through a stack of interleaved causal self-attention and temporal convolution layers. <PERSON><PERSON> et al. (2022) replaces the stack of interleaved causal self-attention and temporal convolution layers with a Transformer encoder; however, their approach is also causal in the input sequence by composing a sequence of (image, label of previous image) pairs. Both <PERSON><PERSON><PERSON> et al. (2017) and <PERSON><PERSON> et al. (2022) are conceptually similar to our work; however, the causal property of both approaches breaks an important symmetry in meta-learning, namely invariance to permutations of the support set (<PERSON><PERSON><PERSON><PERSON> et al., 2018; <PERSON> et al., 2021) . In Section 5.2, we observe a performance gap between both approaches and CAML and hypothesize the causal approach actually forces a subtly more difficult modeling problem by imposing a causality inductive bias on a fundamentally non-causal prediction task.", "cite_spans": [{"start": 146, "end": 170, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2001)", "ref_id": "BIBREF18"}, {"start": 187, "end": 219, "text": "(Hochreiter & Schmidhuber, 1997)", "ref_id": "BIBREF17"}, {"start": 303, "end": 323, "text": "<PERSON> et al. (2014)", "ref_id": "BIBREF15"}, {"start": 326, "end": 347, "text": "<PERSON><PERSON> et al. (2016)", "ref_id": "BIBREF39"}, {"start": 350, "end": 370, "text": "<PERSON> et al. (2017)", "ref_id": "BIBREF22"}, {"start": 554, "end": 576, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF45"}, {"start": 579, "end": 599, "text": "<PERSON><PERSON><PERSON> et al. (2017)", "ref_id": "BIBREF31"}, {"start": 795, "end": 815, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF24"}, {"start": 1065, "end": 1085, "text": "<PERSON><PERSON><PERSON> et al. (2017)", "ref_id": "BIBREF31"}, {"start": 1090, "end": 1110, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF24"}, {"start": 1297, "end": 1319, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF14"}, {"start": 1320, "end": 1340, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "Cross-Domain Meta-Learning. Cross-domain meta-learning refers to a challenging evaluation paradigm where the meta-training and inference-time data distributions are significantly different (<PERSON> et al., 2019) . Recent work finds that leveraging self-supervised pre-training-or foundational model feature extractors-can significantly improve cross-domain performance (<PERSON> et al., 2022; <PERSON> et al., 2021) . Moreover, fine-tuning with respect to the support set almost always outperforms meta-learning without fine-tuning in this setting (<PERSON> et al., 2020; <PERSON> et al., 2022; Phoo & Hari<PERSON>n, 2020; <PERSON> et al., 2021) . While effective, fine-tuning is prohibitive to deploying visual meta-learning models in a manner similar to LLMs like ChatGPT as the latency and memory cost to fine-tune a model's parameters on each user query is untenable. Accordingly, we propose the universal setting to measure a meta-learner's ability to learn to classify any task seen during inference without fine-tuning.", "cite_spans": [{"start": 189, "end": 208, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF6"}, {"start": 366, "end": 383, "text": "(<PERSON> et al., 2022;", "ref_id": "BIBREF19"}, {"start": 384, "end": 403, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF54"}, {"start": 536, "end": 554, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF16"}, {"start": 555, "end": 571, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF33"}, {"start": 572, "end": 595, "text": "Ph<PERSON> & Hariharan, 2020;", "ref_id": "BIBREF35"}, {"start": 596, "end": 615, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "In-Context Learning for Dense Prediction Tasks. Many recent works have explored in-context learning for other applications of computer vision. <PERSON> et al. (2022) casts in-context learning as image in-painting by first concatenating demonstration images with a query image and then using a vision model to fill-in-the-blank within this concatenated image. Building on this work, <PERSON> et al. (2023) explores what demonstrations lead to strong in-painting performance and <PERSON> et al. (2023) generalizes the approach by formulating other visual applications like segmentation, depth and then concatenated with their corresponding ELMES label embeddings. We feed the resulting sequence of concatenated vectors into a non-casual sequence model and extract the query vector from the output sequence to predict its class.", "cite_spans": [{"start": 143, "end": 160, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF1"}, {"start": 377, "end": 396, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF55"}, {"start": 469, "end": 487, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "estimation, etc. as in-painting. Other approaches explore in-context learning for applications like scene understanding (<PERSON><PERSON><PERSON><PERSON> et al., 2024) , medical image segmentation (<PERSON><PERSON> et al., 2023) , and more generally dense prediction tasks (<PERSON> et al., 2023) . Like these approaches, we study visual in-context learning; however, this work focuses on few-shot image classification rather than dense prediction tasks.", "cite_spans": [{"start": 120, "end": 144, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2024)", "ref_id": "BIBREF0"}, {"start": 174, "end": 194, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF4"}, {"start": 239, "end": 257, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "We adapt the ideas underpinning in-context learning in LLMs-namely learning to classify a query from a context of support set demonstrations in a single forward pass-to image classification. However, dissimilar from in-context learning, visual meta-learners should be non-causal: placing one example before another in the support set does not entail a causal relationship (<PERSON><PERSON><PERSON><PERSON> et al., 2018; <PERSON> et al., 2021) .", "cite_spans": [{"start": 372, "end": 394, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF14"}, {"start": 395, "end": 415, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "APPROACH", "sec_num": "3"}, {"text": "Architecture. An overview of CAML is shown in Figure 1 . It consists of three different components:", "cite_spans": [], "ref_spans": [{"start": 53, "end": 54, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "APPROACH", "sec_num": "3"}, {"text": "(1) a frozen pre-trained image encoder, (2) a fixed Equal Length and Maximally Equiangular Set (ELMES) class encoder, and (3) a non-causal sequence model. While pre-trained image encoders and non-causal sequence models are well-known, to encode label information we introduce an ELMES encoder. An ELMES encoder is a bijective mapping between the labels and a set of vectors that are equal length and maximally equiangular. Historically, labels have been encoded with one-hot vectors; however in Section 4, we prove that an ELMES encoding of mutually exclusive classes allows the sequence model to maximally identify classes within the support set.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "APPROACH", "sec_num": "3"}, {"text": "As visualized in Figure 1 , CAML first encodes query and support set images using a frozen pre-trained feature extractor. Crucially, the pre-trained image encoder's embedding space distills images into low-dimensional representations so that images with similar content and visual characteristics have similar embeddings. Classes of the support set are encoded with an ELMES class encoder; however as the class of the query is unknown, we use a special learnable \"unknown token\" embedding that is learned during large-scale pre-training. CAML then concatenates each image embedding with its corresponding query embedding to form an input sequence.", "cite_spans": [], "ref_spans": [{"start": 24, "end": 25, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "APPROACH", "sec_num": "3"}, {"text": "Progressing through Figure 1 , this sequence is fed into a non-causal sequence model, i.e. a Transformer encoder, to condition the output representations on the full context of query and support set points. This enables dynamic and real-time classification; visual characteristics from query and support set images can be compared with each other to determine the specific visual features-such as content, textures, etc.-used to classify the query. From the output sequence of the non-causal sequence model, we select the element at the same position as the query in the input sequence, and pass this vector through a shallow MLP to predict the label of the query.", "cite_spans": [], "ref_spans": [{"start": 27, "end": 28, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "APPROACH", "sec_num": "3"}, {"text": "Large-Scale Pre-Training. As our focus is universal meta-learning-and CAML may encounter any new visual concept during inference-we pre-train CAML's non-causal sequence model on few-shot image classification tasks from ImageNet-1k (<PERSON><PERSON> et al., 2009) , Fungi (Schroeder & Cui, 2018) , MSCOCO (<PERSON> et al., 2014) , and WikiArt (Saleh & Elgammal, 2015) . We chose these datasets because they span generic object recognition (ImageNet-1k, MSCOCO), fine-grained image classification (Fungi), and unnatural image classification (WikiArt). To avoid distorting the pre-trained image encoder's embedding space, we freeze this module and only update the sequence model's parameters during pretraining. Similarly, since an ELMES minimizes the entropy of detecting classes within the support set, the label encoder is also frozen. In the context of pre-training, meta-training, and fine-tuning, CAML only requires pre-training and avoids meta-training on the train/validation splits of meta-learning benchmarks or fine-tuning on the support set during inference.", "cite_spans": [{"start": 231, "end": 250, "text": "(<PERSON><PERSON> et al., 2009)", "ref_id": "BIBREF9"}, {"start": 259, "end": 282, "text": "(Schroeder & Cui, 2018)", "ref_id": "BIBREF40"}, {"start": 292, "end": 310, "text": "(<PERSON> et al., 2014)", "ref_id": "BIBREF29"}, {"start": 325, "end": 349, "text": "(Saleh & Elgammal, 2015)", "ref_id": "BIBREF38"}], "ref_spans": [], "eq_spans": [], "section": "APPROACH", "sec_num": "3"}, {"text": "In this section, we motivate our choice of the ELMES Class Encoder by considering the symmetries desirable in meta-learning algorithms. Two important symmetries are (1) invariance to the assignment of support set classes to numeric labels and (2) invariance to permutations in the ordering of the input sequence. The first invariance implies the class embeddings must be equiangular and equal norm, with an ELMES configuration minimizing the entropy of learnable model parameters detecting any given class. Later, we show an ELMES also satisfies the second symmetry. Due to space constraints, all proofs and many definitions, properties, lemmas, and theorems are allocated to Appendix A.1. We begin with a formal definition of an ELMES.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "THEORETICAL ANALYSIS", "sec_num": "4"}, {"text": "Definition 1. An Equal Length and Maximally Equiangular Set (ELMES) is a set of non-zero vectors {ϕ j } d j=1 , ϕ j ∈ R d+k for some k ≥ 0 and d > 1, such that ∀j ̸ = j ′ , ∥ϕ j ∥ = ∥ϕ j ′ ∥ and ⟨ϕ j , ϕ j ′ ⟩ = -1 d-1 . Simply, all vectors in this set are equal length and maximally equiangular.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EQUAL LENGTH AND <PERSON>X<PERSON><PERSON>LY EQUIAN<PERSON><PERSON><PERSON>R SET OF VECTORS", "sec_num": "4.1"}, {"text": "An Equal Angle and Maximally Equiangular Set (ELMES) of vectors has connections to both Equiangular Tight Frames in representation theory (<PERSON>, 1974; <PERSON><PERSON><PERSON> et al., 2018) as well as the Simplex Equiangular Tight Frames highlighted in recent neural collapse works exploring softmaxlayer geometry at the terminal phase of training (<PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2022) . We offer additional discussion comparing these structures in Appendix A.1 as well as provide an intuitive view of an ELMES as a regular d-simplex immersed in R d+k .", "cite_spans": [{"start": 138, "end": 151, "text": "(Welch, 1974;", "ref_id": "BIBREF50"}, {"start": 152, "end": 172, "text": "<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF12"}, {"start": 331, "end": 352, "text": "(<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF34"}, {"start": 353, "end": 371, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF53"}], "ref_spans": [], "eq_spans": [], "section": "EQUAL LENGTH AND <PERSON>X<PERSON><PERSON>LY EQUIAN<PERSON><PERSON><PERSON>R SET OF VECTORS", "sec_num": "4.1"}, {"text": "Symmetry in the assignment of support classes to numeric labels is an important property of metalearning algorithms. For example, if we have the support set classes {tower, bear, tree}, the mapping of {bear -> 1, tower -> 2, tree -> 3} should produce the same prediction for a query point as a different mapping {bear -> 2, tower -> 3, tree -> 1}. To explore this symmetry, we examine how class embeddings may be used by the model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LABEL SYMMETRY", "sec_num": "4.2"}, {"text": "From our formulation in Section 3, we represent a demonstration vector as a concatenation of an image embedding ρ and a label embedding ϕ: [ρ ϕ]. This vector is directly fed into the self-attention mechanism, where we matrix multiply with key, query, and value self-attention heads. Taking only one of these matrices for simplicity with head-dimension k:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LABEL SYMMETRY", "sec_num": "4.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "[ρ ϕ] Γ 1 ... Γ k ψ 1 ... ψ k = [⟨ρ , Γ 1 ⟩ ... ⟨ρ , Γ k ⟩] + [⟨ϕ , ψ 1 ⟩ ... ⟨ϕ , ψ k ⟩]", "eq_num": "(1)"}], "section": "LABEL SYMMETRY", "sec_num": "4.2"}, {"text": "The output of this transformation will be the sum of two vectors: one composed of the inner products between the image embedding ρ and the learnable {Γ i } k i=1 and the other composed of the class embedding ϕ and the learnable {ψ i } k i=1 . Note that Equation (1) implies that CAML is not invariant to the assignment of labels to support set classes due to the addition between ⟨ρ , Γ i ⟩ and ⟨ϕ , ψ i ⟩; however, we can constrain the geometry of the class embeddings {ϕ} d j=1 to in principle respect label symmetry. Specifically for i ̸ = j ̸ = k, ⟨ϕ i , ϕ j ⟩ = ⟨ϕ i , ϕ k ⟩ and ∥ϕ i ∥ = ∥ϕ j ∥.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LABEL SYMMETRY", "sec_num": "4.2"}, {"text": "Similar to a convolutional filter learning to match a pattern within an image, our analysis assumes the learnable [ψ 1 ... ψ k ] will converge to vectors that maximize the inner product with a single class embedding subject to certain constraints. Under this assumption, we ask what geometry of the d-class embeddings {ϕ} d j=1 allows a learnable ψ i vector to most unambiguously detect a single class embedding. To answer this question, we define a probability mass function for each ψ i over the set of d-classes so that maximizing the probability of the j th class aligns with maximizing ⟨ϕ j , ψ i ⟩ and equally minimizing ⟨ϕ k , ψ i ⟩ for k ̸ = j. Definition 2. Let X be a discrete Random Variable taking on values in {1, 2, ..., d}. For learnable vector ψ i , define probability mass function p ψi (X = j) as the softmax over [⟨ϕ 1 , ψ i ⟩ ... ⟨ϕ d , ψ i ⟩] so that: cos(θi,j ) d k=1 e ∥ψi∥∥ϕj ∥ cos(θ i,k ) where θ i,j is the angle between ϕ j and ψ i .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LABEL SYMMETRY", "sec_num": "4.2"}, {"text": "p ψi (X = j) = e ∥ψi∥∥ϕj ∥", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LABEL SYMMETRY", "sec_num": "4.2"}, {"text": "We say ψ i learns to detect class j when p ψi (X = j) > p ψi (X = k) for 1 ≤ k ≤ d with k ̸ = j. By symmetry in the assignment of class embeddings to support classes, we can assume that the number of ψ i learned to detect class i is similar to the number of ψ j learned to detect class j for all pairs (i, j). We also leverage symmetry in the assignment of labels to support set classes to make the following assumptions. A justification for each assumption is located in Appendix A.1. Assumption 1. Suppose {ψ i } k i=1 are learnable class detectors of unit norm with at least one", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LABEL SYMMETRY", "sec_num": "4.2"}, {"text": "ψ i detecting each class 1 ≤ i ≤ d. The probability p ψj (X = j) = p ψi (X = i) for 1 ≤ i, j ≤ d. Assumption 2. Define p ψi (X = i)\\{ϕ l } d l=(m+1) as the probability of ψ i detecting ϕ i from the set of vectors {ϕ j } m j=1 , m < d. Then the probability p ψj (X = j)\\{ϕ l } d l=(m+1) = p ψi (X = i)\\{ϕ l } d l=(m+1) for 1 ≤ i, j ≤ m and m ≥ 2. Assumption 3. When ψ i = ϕi ∥ϕi∥ , p ψi (X = i) is maximized.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LABEL SYMMETRY", "sec_num": "4.2"}, {"text": "When Assumption 1, Assumption 2, and Assumption 3 hold, the set of class embeddings that maximize the probability of a learnable ψ i detecting class i is necessarily an ELMES. Theorem 1. The set of class embeddings {ϕ j } d j=1 ∀j, 1 ≤ j ≤ d that maximizes p ψj (X = j) is necessarily an ELMES.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LABEL SYMMETRY", "sec_num": "4.2"}, {"text": "Alternatively when viewed through the lens of information theory, we can interpret an ELMES as the class embedding that minimizes the entropy of ψ i detecting class i. Informally, ELMES causes ψ i to have the least uncertainty when detecting class i. Proposition 1. Let H ψi (X) be the entropy of p ψi (X). An ELMES minimizes H ψi (X).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "LABEL SYMMETRY", "sec_num": "4.2"}, {"text": "In addition to label symmetry, it is also desirable for the output prediction of CAML to not depend on the order of demonstrations in the sequence. For example, if we have the support set classes {tower, bear, tree}, the sequence {(bear -> 1), (tower -> 2), (tree -> 3)} should produce the same output as the permuted sequence {(tree -> 3), (bear -> 1), (tower -> 2)}. Building on the prior work of <PERSON><PERSON> et al. (2021) ; <PERSON> et al. (2023) , it suffices to show to show that the ELMES label encoder is equivariant to permutations in the input sequence to show that CAML is invariant to permutations. Proposition 2. Consider an n-sequence of one-hot labels stacked into a matrix S ∈ R n×w , and an ELMES label encoder denoted by W ∈ R w×d with w denoting \"way\" and d the dimension of the label embedding. The label embedding SW is equivariant to permutations. 2015) .", "cite_spans": [{"start": 399, "end": 419, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF25"}, {"start": 422, "end": 441, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF13"}, {"start": 861, "end": 866, "text": "2015)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "PERMUTATION INVARIANCE.", "sec_num": "4.3"}, {"text": "Generic object recognition, fine-grained image classification, and unnatural image classification are standard benchmarking tasks in meta-learning literature (<PERSON> et al., 2020; <PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2020) . Beyond this, we compose a challenging new inter-domain category by combining Pascal VOC with Paintings so that each class is composed of both natural images and paintings. This allows us to evaluate the ability of meta-learning algorithms to generalize across domains within the same class. For example, the support image for the class \"tower\" may be <PERSON>'s The Starry Night, while the query may be a picture of the Eiffel Tower. Humans have the ability to generalize visual concepts between such domains; however, meta-learning algorithms struggle with this formulation (<PERSON> & <PERSON>, 2011) .", "cite_spans": [{"start": 158, "end": 177, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF7"}, {"start": 178, "end": 194, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF19"}, {"start": 195, "end": 219, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF52"}, {"start": 220, "end": 237, "text": "<PERSON> et al., 2020)", "ref_id": "BIBREF16"}, {"start": 815, "end": 847, "text": "(<PERSON><PERSON> & Gr <PERSON>, 2011)", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "PERMUTATION INVARIANCE.", "sec_num": "4.3"}, {"text": "We evaluate the performance of CAML, Prototypical Networks (ProtoNet) (<PERSON><PERSON> et al., 2017) , MetaOpt (<PERSON> et al., 2019) , MetaQDA (<PERSON> et al., 2021) , SNAIL (<PERSON><PERSON><PERSON> et al., 2017) , and GPICL (<PERSON><PERSON> et al., 2022) and WikiArt, and during evaluation on the set of 11 meta-learning benchmarks, models are not meta-trained or fine-tuned. We compare with ProtoNet, MetaOpt, and MetaQDA as they achieve state-of-the-art results when paired with a pre-trained feature extractor (<PERSON> et al., 2022) . As sequence modeling underpins CAML, we also compare with SNAIL and GPICL to evaluate the performance of past formulations of causal sequence-based meta-learning algorithms in the universal setting.", "cite_spans": [{"start": 70, "end": 90, "text": "(<PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF42"}, {"start": 101, "end": 119, "text": "(<PERSON> et al., 2019)", "ref_id": null}, {"start": 130, "end": 150, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF54"}, {"start": 159, "end": 180, "text": "(<PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF31"}, {"start": 193, "end": 214, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF24"}, {"start": 473, "end": 490, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "BASELINES", "sec_num": "5.1"}, {"text": "To assess the gap between universal and in-domain meta-learning performance, we benchmark the current state-of-the-art meta-learning algorithm P>M>F (<PERSON> et al., 2022) . Similar to the universal setting, P>M>F uses a ViT-base feature extractor initialized with weights from DINO (<PERSON><PERSON> et al., 2021) ; however, it meta-trains on the training set of each benchmark before evaluating on that benchmark's test set.", "cite_spans": [{"start": 149, "end": 166, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF19"}, {"start": 278, "end": 298, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "BASELINES", "sec_num": "5.1"}, {"text": "When pre-training all models in the universal setting, we set the learning rate to a fixed 1 × 10 -5 and do not perform any hyperparameter tuning in order to match the practices used by P>M>F. We use early stopping with a window size of 10 epochs during pre-training and the code release of <PERSON> et al.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BASELINES", "sec_num": "5.1"}, {"text": "(2022) to benchmark P>M>F with the training settings and hyperparameters described in their work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BASELINES", "sec_num": "5.1"}, {"text": "Our findings are summarized in This result suggests that the amount of new visual information learned during inference through visual in-context learning can be comparable to the amount learned when directly meta-training on in-domain data. This capacity may unlock new applications in the visual space, just as the emergence of in-context learning in LLMs has enabled many new applications in natural language.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RESULTS", "sec_num": "5.2"}, {"text": "Benchmarks Where CAML Underperforms. The 3 datasets where P>M>F outperforms CAML are CIFAR-fs, Aircraft, and ChestX. CIFAR-fs is a generic object recognition benchmark containing CIFAR images downsampled to 32x32 resolution. As CAML and CLIP pre-train on 224x224 resolution images, downsampling by a factor of 49 likely induces a distribution shift that was not learned by CAML during large-scale pre-training. In the cases of Aircraft and ChestX, we postulate that the CLIP embedding space-structured so images with similar captions have similar embeddingsstruggles to effectively differentiate between the fine-grained, specialized classes in these tasks. For example, while a Boeing 737 and Airbus A380 have different labels in the Aircraft dataset, the scraped CLIP captions for those images may not reach that level of granularity. This corroborates the findings from <PERSON><PERSON> et al. (2021) , which found that in a zero-shot setting, CLIP underperforms in specialized or complex tasks.", "cite_spans": [{"start": 873, "end": 894, "text": "<PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "RESULTS", "sec_num": "5.2"}, {"text": "Our ablation study into non-CLIP pre-trained feature extractors in Tables 5 to 8 of Appendix C shows CAML's performance on Aircraft can drastically improve. Specifically, 5w-1s performance increases from 63.3 to 81.8 and 5w-5s performance increases from 79.1 to 92.1 when a ViT-Huge pre-trained on Laion-2b (<PERSON><PERSON> et al., 2022) initializes the weights of the image encoder rather than CLIP.", "cite_spans": [{"start": 307, "end": 331, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF41"}], "ref_spans": [], "eq_spans": [], "section": "RESULTS", "sec_num": "5.2"}, {"text": "Fine-tuning CLIP Backbone. Our findings in Tables 1 to 4 indicate that updating the CLIP image encoder during pre-training hurts the performance of ProtoNet and MetaOpt. We observe that these methods tend to overfit during pre-training, and our empirical results show a similar pattern: pretraining with these methods often helps the performance of benchmarks similar to ImageNet (i.e. Pascal, MiniImageNet, tiered-ImageNet), but it significantly hurts the performance of out-of-domain tasks (i.e. Aircraft, CUB, Paintings) as shown in Tables 1 to 4 . We believe that further training the CLIP backbone distorts the structure of its embedding space, leading to catastrophic forgetting on out-of-domain tasks. Conversely, CAML, MetaQDA, SNAIL, and GPICL-all of which freeze the parameters of the CLIP feature extractor-benefit greatly from large-scale episodic pre-training on ImageNet-1k, Fungi, MSCOCO, and WikiArt.", "cite_spans": [], "ref_spans": [{"start": 50, "end": 56, "text": "1 to 4", "ref_id": "TABREF4"}, {"start": 543, "end": 549, "text": "1 to 4", "ref_id": "TABREF4"}], "eq_spans": [], "section": "RESULTS", "sec_num": "5.2"}, {"text": "To better understand how CAML learns during inference, we analyze its ability to dynamically update its representations. Due to casting meta-learning as non-causal sequence modeling, CAML considers the full context of query and support set to predict the label of the query. Specifically, the query dynamically influences the representation of support set points, and the support set points dynamically influence the representation of the query as this sequence is passed through the layers of a non-causal sequence model. This property enables universal meta-learning by allowing the model to update the support and query representations based on the context of the task, not only the contents of the images, within the parameter space of the sequence model.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ANALYSIS", "sec_num": "6"}, {"text": "An example where the query dynamically influences the support set is visualized in Figure 2 . Given only the 5 support examples, the prediction task is ambiguous. However, the nature of the query determines the prediction task. The query image of a tower in Figure 2a reduces the task to generic object recognition: classify the query based on the object portrayed in the image. On the other hand, and as visualized in Figure 2b , the query image of embroidery reduces the prediction task to texture identification: classify the query based on artistic medium.", "cite_spans": [], "ref_spans": [{"start": 90, "end": 91, "text": "2", "ref_id": "FIGREF2"}, {"start": 265, "end": 267, "text": "2a", "ref_id": "FIGREF2"}, {"start": 426, "end": 428, "text": "2b", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "ANALYSIS", "sec_num": "6"}, {"text": "To analyze how dynamic representations affect CAML, we examine the representations of the support set and query vectors at the input to and output of the non-causal sequence model. For both examples visualized in Figure 2a and Figure 2b , the non-causal sequence model learns to separate support set vectors by class identity and group the query representation with the correct support set example.", "cite_spans": [], "ref_spans": [{"start": 220, "end": 222, "text": "2a", "ref_id": "FIGREF2"}, {"start": 234, "end": 236, "text": "2b", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "ANALYSIS", "sec_num": "6"}, {"text": "We find the frozen CLIP image embeddings are actually antagonistic for the classification-by-texture task visualized in Figure 2b : the query image embedding is closest to the support set example for the second class, \"oil painting\". Unsurprisingly, the baseline methods that rely on frozen CLIP embeddings-specificially MetaQDA, ProtoNet † , and MetaOpt † -group the query with \"oil painting\" and therefore misclassify this example. On the other hand, as CAML considers the full context of the query and support set, it develops representations of the query in the context of the support set-and the support set in the context of the query-to group the query with the \"embroidery\" support set image as they share the same texture, thereby correctly classifying this example.", "cite_spans": [], "ref_spans": [{"start": 127, "end": 129, "text": "2b", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "ANALYSIS", "sec_num": "6"}, {"text": "In this work, we develop universal meta-learning to approximate the performance of visual metalearners deployed to a ChatGPT-like application and present CAML: a meta-learning algorithm that emulates in-context learning in LLMs by learning new visual concepts during inference without fine-tuning. Our empirical findings show that CAML-without meta-training or fine-tuning-exceeds or matches the performance of the current state-of-the-art meta-learning algorithm on 8 out of 11 benchmarks. This result indicates visual meta-learning models are ready for deployment in a manner similar to LLMs, and we hope this work recalibrates our sense of limitations for the universal meta-learning paradigm.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "7"}, {"text": "Nevertheless, there are areas where CAML struggles. Specifically, the performance of CAML on highly out-of-distribution images-e.g. chest x-rays-and varying image resolutions-e.g. rescaled CIFAR images-lags behind that of the best in-domain approaches. Developing methods for the universal setting that are robust to these cases is a promising direction for future work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "7"}, {"text": "We offer additional insight into the theoretical analysis presented in Section 4 and provide the omitted remarks, properties, lemmas, and proofs. where I d ∈ R d×d is the identity matrix and 1 ∈ R d×1 is the ones vector. Somewhat contradictory, a Simplex Equiangular Tight Frame is not an Equiangular Tight Frame (Welch, 1974) ", "cite_spans": [{"start": 313, "end": 326, "text": "(<PERSON>, 1974)", "ref_id": "BIBREF50"}], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "max j̸ =j ′ |⟨ϕ j , ϕ j ′ ⟩| ∥ϕ j ∥∥ϕ j ′ ∥ = n -d d(n -1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "It is well-known that a set of non-zero equal-norm vectors satisfies the <PERSON> lower bound if and only if that set of vectors is equiangular and also a tight frame for R d (<PERSON><PERSON><PERSON> et al., 2018) . Definition 5. A set of non-zero, equal norm vectors {ϕ j } n j=1 is equiangular if ∀j ̸ = j ′ , |⟨ϕ j , ϕ j ′ ⟩| = c for some c ∈ R, c > 0.", "cite_spans": [{"start": 172, "end": 193, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "Definition 6. {ϕ j } n j=1 is a tight frame for R d if, ∀v ∈ R d , ∃A > 0 such that A∥v∥ 2 = n j=1 |⟨ϕ j , v⟩| 2 . Remark 1. A Simplex Equiangular Tight Frame is not a tight frame.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "Proof. Observe that for any finite d, for {ϕ j } d j=1 equal to the columns of", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "d d-1 (I d -1 d 11 T ), it is the case that d-1 j=1 ϕ j = -1 * ϕ d . So {ϕ j } n j=1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "do not span R d , and therefore, cannot be a tight frame.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "Similarly, a Simplex ETF is not a d-simplex. Remark 2. A Simplex Equiangular Tight Frame is not a simplex.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "Proof. A simplex in R n requires n + 1 points.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "To align terminology with properties, we generalize a Simplex ETF to an ELMES in Definition 1: a set of d vectors in a (d + k)-dimensional ambient space with k ≥ 0. Observe that a regular simplex is a special type of ETF in which the number of vectors in the set is one more than the dimension of the space that they span (<PERSON><PERSON><PERSON> et al., 2018) . Building on this observation, an intuitive view of ELMES is a regular d-simplex immersed in R d+k .", "cite_spans": [{"start": 322, "end": 343, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "Remark 3. Consider a centered d-dimensional regular simplex with vertices {ϕ j } d+1 j=1 , ϕ j ∈ R d+1 . Let ı can be the canonical inclusion map:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "R d → R d+1 , ı can (x 1 , x 2 , ..., x d ) = (x 1 , x 2 , ..., x d , 0 d+1 ), then {ı can (ϕ j )} d+1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "j=1 is an ELMES. Proof. The two criteria of an ELMES are maximally equiangular and equal length. As all vertices of a centered regular d-simplex are equal length from the origin, {ϕ j } d+1 j=1 are equal length and therefore {ı can (ϕ j )} d+1 j=1 must also have equal length. Similarly, from Lemma 10 of <PERSON><PERSON><PERSON> et al. ( 2020), we know the cosine of the angle between any two vectors in a (d + 1)-dimensional ELMES is -1 d . It is known that for a d-dimensional regular simplex in R d centered at the origin, the angle subtended by any two verticies through the origin is cos(θ) = -1 d . Immersing {ϕ j } d+1 j=1 , ϕ j ∈ R d , into R d+1 via the canonical inclusion operator ı can does not change the pairwise angle between vectors in this set: ⟨ϕ j , ϕ j ′ ⟩ = ⟨ı can (ϕ j ) , ı can (ϕ j ′ )⟩.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "As {ı can (ϕ j )} d+1 j=1 are equal length and maximally equiangular, it forms an ELMES.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "We now show that an ELMES immersed in a higher dimension remains an ELMES. Taken with Remark 3, we can view a high-dimensional ELMES in R d composed of n + 1 vectors {ϕ j } n+1 j=1 , d >> n + 1, as simply a n-simplex immersed in R d via the canonical inclusion operator.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "Lemma 1. Let ı can : R d → R d+k . If {ϕ j } n j=1 is an ELMES , then {ı can (ϕ j )} d j=1 is an ELMES.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "Proof. This reduces to proving that the maximum angle between a set of d equiangular points in R d is the maximum angle between a set of d equiangular points in R d+k . Let {ϕ j } d j=1 be an ELMES such that ϕ j ∈ R d and {ψ j } d j=1 be an ELMES such that ψ j ∈ R d+k . Then {ψ j } d j=1 lie in a d-dimensional subspace of R d+k : ∃γ 1 , ..., γ d and basis vectors e 1 , ..., e d such that ∀ψ j ∈ {ψ j } d j=1 ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "ψ j = d i=1 γ i e i . Therefore, ∀j ̸ = j ′ , ⟨ψ j , ψ j ′ ⟩ ≤ ⟨ϕ j , ϕ j ′ ⟩ as {ϕ j } d j=1 are an ELMES for R d .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A APPENDIX A.1 SUPPLEMENTARY THEORETICAL ANALYSIS", "sec_num": null}, {"text": "There are infinitely many ELMES by rotating one such set of vectors about the origin.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.2 ELMES ROTATIONAL SYMMETRY", "sec_num": null}, {"text": "Remark 4. Let {ϕ j } d j=1 be an ELMES in R d+k for some k ≥ 0. Let o : R d+k → R d+k be an operator from the special orthogonal group SO(d + k). Then {o(ϕ j )} d j=1 is also an ELMES .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.2 ELMES ROTATIONAL SYMMETRY", "sec_num": null}, {"text": "Proof. Length is preserved as operations in SO(d + k) have determinant 1 and angles are similarly preserved as operations in SO(d + k) are unitary (i.e. preserving inner product).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.2 ELMES ROTATIONAL SYMMETRY", "sec_num": null}, {"text": "A final remark relates to the common misconception that a set of orthonormal basis vectors {ψ j } d j=1 is an ELMES. While {ψ j } d j=1 is an ETF in R d since this set realizes the Welch lower-bound in Definition 4, these vectors are not maximally equiangular:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "⟨ψ j , ψ j ′ ⟩ = 0 > -1 d-1 . A.2 ELMES MAXIMIZES p ψj (X = j)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "Justification of Assumption 1. This property is implied by symmetry in the assignment of class embeddings to support classes. As the assignment is arbitrary, all learnable ψ i class detectors should have equal probability of detecting their respective class. For simplicity of notation, we say ψ i learns to detect class embedding ϕ i rather another class embedding ϕ k , k ̸ = i.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "Justification of Assumption 2. Informally, this property states that, for any m-subset of classes {ϕ j } m j=1 , the probability of ψ j detecting class j is equal to the probability of ψ i detecting class i. This is again implied by symmetry in the assignment of class embeddings to support classes as meta-learning algorithms may predict among a subset of m classes in the support set rather than the maximum number of classes d.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "Justification of Assumption 3. Recall in R d , ⟨ψ , ϕ⟩ = ∥ψ∥∥ϕ∥ cos(θ) where θ is the angle between ψ i and ϕ i . Then this assumption constrains our set {ϕ j } d j=1 so that relative norm of ϕ i with respect to ϕ j is lower bounded by cos(θ i,j ): ∥ϕi∥ ∥ϕj ∥ > cos(θ i,j ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "Informally, the {ϕ j } d j=1 are sufficiently spread out in the ambient space so that the learnable ψ i that maximizes p ψi (X = i) is ϕ i itself: ψ i = ϕi ∥ϕi∥ . This constraint helps us avoid degenerative cases like {ϕ j } d j=1 all equal. For example, ϕ j = αϕ i , i ̸ = j with α > 0 is one such degenerative case where one class embedding vector is stacked on a different class embedding, but with higher norm.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "Proof of Theorem 1. Taken with Assumption 1, Assumption 2, and Assumption 3, it suffices to show Theorem 2 and Lemma 4 to prove Theorem 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "Theorem 2. p ψ1 (X = 1) = p ψ2 (X = 2) = ... = p ψ d (X = d) ⇐⇒ {ϕ j } d", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "j=1 are equiangular and equal norm.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "To show the forward (⇒) direction, it suffices to first show", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "p ψ1 (X = 1) = p ψ2 (X = 2) = ... = p ψ d (X = d) ⇒ {ϕ j } d j=1 are equal norm and then show p ψ1 (X = 1) = p ψ2 (X = 2) = ... = p ψ d (X = d) ⇒ {ϕ j } d j=1 are equiangular. Lemma 2. p ψ1 (X = 1 ) = p ψ2 (X = 2 ) = ... = p ψ d (X = d ) ⇒ {ϕ j } d j =1 are equal norm.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "Proof. This implication holds when d = 2: p ψ1 (X = 1) = e ∥ϕ1∥ e ∥ϕ1∥ + e ∥ϕ2∥ cos(θ1,2) = e ∥ϕ2∥ e ∥ϕ2∥ + e ∥ϕ1∥ cos(θ1,2) = p ψ2 (X = 2) e ∥ϕ1∥ (e ∥ϕ2∥ + e ∥ϕ1∥ cos(θ1,2) ) = e ∥ϕ2∥ (e ∥ϕ1∥ + e ∥ϕ2∥ cos(θ1,2) ) e ∥ϕ1∥+∥ϕ1∥ cos(θ1,2) = e ∥ϕ2∥+∥ϕ2∥ cos(θ1,2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "∥ϕ 1 ∥(1 + cos(θ 1,2 )) = ∥ϕ 2 ∥(1 + cos(θ 1,2 )) ∥ϕ 1 ∥ = ∥ϕ 2 ∥ Suppose d > 2 and p ψ1 (X = 1) = ... = p ψ d (X = d).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "By Assumption 2, all m-combinations d m of {p ψ1 (X = 1), ..., p ψ d (X = d)} are equal. This implies all 2-combinations are equal:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "p ψi (X = i) = p ψj (X = j) ⇒ ∥ϕ i ∥ = ∥ϕ j ∥. Therefore, ∥ϕ 1 ∥ = ... = ∥ϕ d ∥.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.1.3 A SET OF ORTHONORMAL BASIS VECTORS IS NOT AN ELMES", "sec_num": null}, {"text": "Proof of Proposition 2. This follows from row-wise equivariance to permutations in matrix multiplication. For any permutation π : [1, . . . , n] → [1, . . . , n] applied to the rows of S n , we have π(S)W = π(SW ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2.2 AN ELMES MAINTAINS PERMUTATION INVARIANCE", "sec_num": null}, {"text": "In this section, we describe our experimental settings, and further, we direct readers interested in reproducing or using any of the methods we benchmark in this work to our released code. Unless stated otherwise, all universal meta-learning baselines use a CLIP feature extractor to encode images. GPICL Implementation. We adapt the GPICL algorithm presented by <PERSON><PERSON> et al. (2022) for episodic meta-training with an ELMES label encoder. Specifically, we represent image feature vectors as CLIP embeddings and the label embeddings with an ELMES. Following <PERSON><PERSON> et al. (2022) , we form a sequence by concatening the current CLIP image embedding with the previous example's ELMES label embedding and add learnable positional embeddings so the model can use positional information of elements in the sequence to classify the query point in a causal-like fashion. We set the General-Purpose In-Context Learning Transformer model to a ViT-Large (<PERSON><PERSON><PERSON> et al., 2020) with leranable positional embeddings.", "cite_spans": [{"start": 363, "end": 383, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF24"}, {"start": 558, "end": 578, "text": "<PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF24"}, {"start": 944, "end": 970, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "B EXPERIMENTAL SETTINGS", "sec_num": null}, {"text": "CAML Implementation. The image encoder is set to CLIP and the label encoder is an ELMES. For the non-causal sequence model, we use a ViT-Large as described in Table 1 of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2020) . This size is chosen as it has a hidden dimension of 1,024 and the CLIP output embedding vectors have hidden dimension of size 768. Choosing a non-causal sequence model with a large hidden dimension allows us to concatenate the label embedding to the CLIP embedding; in this case, the label embedding is a 256 dimensional ELMES. In total, the implementation of CAML used for empirical evaluation has 302 million trainable parameters.", "cite_spans": [{"start": 170, "end": 195, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2020)", "ref_id": "BIBREF10"}], "ref_spans": [{"start": 165, "end": 166, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Large", "sec_num": null}, {"text": "Optimization Settings. Following the recommendation of training Vision Transformers (<PERSON> et al., 2021) as well as standard practices, all universal meta-learning approaches use a cosine learning rate schedule with 9,600 warmup steps increasing linearly from 0 to 1e-5 followed by cosine decay to 1e-6 over the subsequent 360,000 steps. Given the size of our pre-training datasets, we do not apply dropout, attention dropout, or weight decay regularization. We select a batch size of 525 so the 5-way-1-shot episodes contain 520 query predictions and the 5-way-5-shot episodes contain 500 query predictions. Given the scale of the pre-training datasets-and the computation to train a single model-we do not conduct any hyperparameter tuning.", "cite_spans": [{"start": 84, "end": 106, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF43"}], "ref_spans": [], "eq_spans": [], "section": "Large", "sec_num": null}, {"text": "P>M>F Meta-Training. We follow the settings used by <PERSON> et al. (2022) to evaluate P>M>F. Specifically, P>M>F uses a DINO (<PERSON><PERSON> et al., 2021) feature extractor rather than a CLIP feature extractor as the authors of P>M>F found a DINO feature extractor to be preferrable. We refer readers <PERSON> et al. (2022) for this comparison. For meta-training, we use the code released by <PERSON> et al. (2022) and simply switch out the datasets to evaluate the In-Domain setting. Both the in-domain and universal meta-learning settings use the same test-set data; the difference is that P>M>F meta-trains on each training dataset before evaluating on the testing dataset of each benchmark.", "cite_spans": [{"start": 52, "end": 68, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF19"}, {"start": 120, "end": 140, "text": "(<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF5"}, {"start": 287, "end": 303, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF19"}, {"start": 372, "end": 388, "text": "<PERSON> et al. (2022)", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "Large", "sec_num": null}, {"text": "ELMES Ablation. To supplement our theoretical analysis in Section 4, we train a version of CAML with learnable class embedding vectors in place of the fixed ELMES encoder. Given our analysis in Section 4, it is perhaps unsurprising we find that-without any constraints or limitations-the class embeddings converge to an ELMES. The average pair-wise angle between embedding vectors is 1.77 ± 0.02 radians whereas the expected pairwise angle from an ELMES is 1.82. Similarly, the average norm of the learnable class embeddings converges to 1.34 ± 0.02 whereas the learned norm of the ELMES model is 1.32.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C SUPPLEMENTARY ANALYSIS", "sec_num": null}, {"text": "An evaluation comparing CAML with learnable class embeddings to the approach with a fixed ELMES encoder is presented in Table 5, Table 6, Table 7 , and Table 8 of the Appendix. In summary, the performance is approximately the same on each benchmark with the exception of Aircraft. In this case, the learnable embedding model significantly outperforms the ELMES model, and moreover, surpasses all other universal meta-learning baselines on the 5-way-1-shot split with an accuracy of 66.3 ± .2. Nevertheless, given the similarity between both approaches on the remaining 10 datasets, Image Encoder Ablation. To evaluate how the performance of CAML is affected by the pre-trained image encoder, we evaluate CAML with a ResNet-34 image encoder pre-trained on ImageNet-1k, a ViT-base image encoder pre-trained with CLIP, and a ViT-huge image encoder that is pre-trained on Laion-2b (<PERSON> et al., 2022) . We use the open source models released by Hugging Face in our evaluation.", "cite_spans": [{"start": 877, "end": 901, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF41"}], "ref_spans": [{"start": 126, "end": 128, "text": "5,", "ref_id": "TABREF6"}, {"start": 129, "end": 137, "text": "Table 6,", "ref_id": "TABREF7"}, {"start": 138, "end": 145, "text": "Table 7", "ref_id": "TABREF8"}, {"start": 158, "end": 159, "text": "8", "ref_id": "TABREF9"}], "eq_spans": [], "section": "C SUPPLEMENTARY ANALYSIS", "sec_num": null}, {"text": "As indicated in Table 9 , Table 10 , Table 11 , and Table 12 , the performance of CAML scales with the strength of the feature extractor. Specifically, the performance with a ResNet-34 feature extractor is significantly worse than the performance with a CLIP ViT-base feature extractor, and in turn, the performance with a CLIP ViT-base is significantly worse than the performance with a Laion-2b ViT-huge feature extractor. However, its unclear what facet of the improved feature extractor is relevant for CAML , especially on out-of-distribution tasks like Aircraft where the most benefit is seen. Moreover, it is unclear why there is no improvement on another out-of-distribution dataset, ChestX. the 5-way-1-shot task. In Figure 5 (right), we visualize the average standard deviation of all 120 permutations the 5-way-1-shot task for samples from mini-ImageNet. The mean of this statistic is 0.004 ± 0.0004. Taken together, this indicates CAML is empirically robust to permutations in the assignment of labels to support set classes.", "cite_spans": [], "ref_spans": [{"start": 22, "end": 23, "text": "9", "ref_id": "TABREF10"}, {"start": 32, "end": 34, "text": "10", "ref_id": "TABREF11"}, {"start": 43, "end": 45, "text": "11", "ref_id": "TABREF12"}, {"start": 58, "end": 60, "text": "12", "ref_id": "TABREF13"}, {"start": 733, "end": 734, "text": "5", "ref_id": null}], "eq_spans": [], "section": "C SUPPLEMENTARY ANALYSIS", "sec_num": null}, {"text": "Weaknesses of CAML. Despite its strong empirical performance, CAML presents several weaknesses. First, the maximum number of classes present in the support set at any point during inference must be known at pre-training to instantiate a d-way ELMES. Further, at least one dataset during pre-training must use a d-way classification setting so the ψ i class detectors referenced in Section 4 are trained within the Transformer encoder's attention layers.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D DISCUSSION", "sec_num": null}, {"text": "Why does CAML not fine-tune the image encoder during pre-training? We do not fine-tune the image encoder because it is not advantageous for universal meta-learning.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D DISCUSSION", "sec_num": null}, {"text": "Our goal is to develop a meta-learning algorithm that may function in a ChatGPT-like application; it should be able to run in-context learning on any set of images. Foundational image models are trained for exactly this purpose: they are pre-trained on billions of images to form a well-structured image embedding space that is robust to augmentations, occlusions, etc. Moreover, valuable characteristics such as the presence of objects, textures, etc. of an image are encoded into the structure of the embedding space so that the axes of variability among the embeddings encode variation in specific visual attributes.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "D DISCUSSION", "sec_num": null}, {"text": "Fine-tuning the image encoder can corrupt this embedding space; especially since the datasets we use for pre-training are orders of magnitude smaller than the ones used to train the Foundational model. This hypothesis is supported by our experiments with ProtoNet and MetaOpt in Tables 1 to 4 . Specifically, we find fine-tuning the backbone during pre-training leads to performance degradation on many of our benchmarks when evaluated in the universal meta-learning setting.", "cite_spans": [], "ref_spans": [{"start": 286, "end": 292, "text": "1 to 4", "ref_id": "TABREF4"}], "eq_spans": [], "section": "D DISCUSSION", "sec_num": null}, {"text": "EXPERIMENTSTo quantify universal image classification performance, we evaluate a diverse set of 11 meta-learning benchmarks divided across 4 different categories:1. Generic Object Recognition: mini-ImageNet(<PERSON><PERSON><PERSON> et al., 2016), tiered-ImageNet(<PERSON> et al., 2018), CIFAR-fs(<PERSON><PERSON><PERSON> et al., 2018), and Pascal <PERSON>(<PERSON><PERSON> et al.)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "We thank <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> for their invaluable feedback and help during revisions of this work. We also thank <PERSON> for helping us improve the related work, <PERSON> for constructive dialogue over Twitter, and the Hazy Group at Stanford as a whole their support throughout the research process. We gratefully acknowledge the support of NIH under No. U54EB020405 (Mobilize), DARPA under Nos. N660011924033 (MCS), NSF under Nos. OAC-1835598 (CINES), CCF-1918940 (Expeditions), DMS-2327709 (IHBEM), Nos. CCF2247015 (Hardware-Aware), CCF1763315 (Beyond Sparsity), CCF1563078 (Volume to Velocity), and 1937301 (RTML); US DEVCOM ARL under Nos. W911NF-23-2-0184 (Long-context) and W911NF-21-2-0251 (Interactive Human-AI Teaming); ONR under Nos. N000142312633 (Deep Signal Processing); Stanford HAI under No. 247183; NXP, Xilinx, LETI-CEA, Intel, IBM, Microsoft, NEC, Toshiba, TSMC, ARM, Hitachi, BASF, Accenture, Ericsson, Qualcomm, Analog Devices, Google Cloud, Salesforce, Total, Wu Tsai Neurosciences Institute, Chan Zuckerberg Initiative, Amazon, Genentech, GSK, Juniper Networks, KDDI, UCB, the HAI-GCP Cloud Credits for Research program, the Stanford Data Applications Initiative, and the Stanford Data Science Initiative (SDSI).The U.S. Government is authorized to reproduce and distribute reprints for Governmental purposes notwithstanding any copyright notation thereon. Any opinions, findings, and conclusions or recommendations expressed in this material are those of the authors and do not necessarily reflect the views, policies, or endorsements, either expressed or implied, of NIH, ONR, or the U.S. Government.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACKNOWLEDGMENTS", "sec_num": null}, {"text": "Lemma 3. p ψ1 (X = 1) = p ψ2 (X = 2) = ... = p ψ d (X = d) ⇒ {ϕ j } d j=1 are equiangular.Proof. This implication is trivially true when d = 2 (see the proof of Lemma 2), and we show it is similarly true when d = 3. Following the steps in the proof of Lemma 2, we arrive at the following 3 pairs of equalities:(1) e ∥ϕ1∥(1+cos(θ1,2)) + e ∥ϕ1∥+∥ϕ3∥ cos(θ2,3) = e ∥ϕ2∥(1+cos(θ1,2)) + e ∥ϕ2∥+∥ϕ3∥ cos(θ1,3)(2) e ∥ϕ1∥(1+cos(θ1,3)) + e ∥ϕ1∥+∥ϕ2∥ cos(θ2,3) = e ∥ϕ3∥(1+cos(θ1,3)) + e ∥ϕ3∥+∥ϕ2∥ cos(θ1,3)(3) e ∥ϕ2∥(1+cos(θ2,3)) + e ∥ϕ2∥+∥ϕ1∥ cos(θ1,3) = e ∥ϕ3∥(1+cos(θ2,3)) + e ∥ϕ3∥+∥ϕ1∥ cos(θ1,2)From Lemma 2, p ψ1 (X = 1) = p ψ2 (X = 2) = p ψ3 (X = 3) ⇒ ∥ϕ 1 ∥ = ∥ϕ 2 ∥ = ∥ϕ 3 ∥, so the above pairs of equalities reduce to:and when d = 3, {ϕ j } 3 j=1 are equiangular. Suppose d > 3 and p ψ1 (X = 1) = ... = p ψ d (X = d). By Assumption 2, all m-combinations d m of {p ψ1 (X = 1), ..., p ψ d (X = d)} are equal. This implies all 3-combinations are equal:By Lemma 2 and Lemma 3,j=1 are equiangular and equal norm.(⇐) Suppose {ϕ j } d j=1 are equiangular and equal norm. Let ∥ϕ∥ be the norm of any vector in our set and cos(θ) be the pairwise angle between any two vectors. Thene ∥ϕ∥ e ∥ϕ∥ + (d -1)e ∥ϕ∥ cos(θ) = p ψj (X = j)for any 1 ≤ i, j ≤ d.Lemma 4. For a set of equiangular and equal norm vectors, maximum equiangularity maximizes j p ψj (X = j).Proof. The maximum pairwise angle between two vectors in R d is π, and from Theorem 2e ∥ϕ∥ e ∥ϕ∥ + (d -1)e ∥ϕ∥ cos (θ) for all 1 ≤ i, j ≤ d. Increasing the angle θ decreases cos(θ). Decreasing cos(θ) only decreases the denominator, which in turn, increases p ψi (X = i). Therefore, maximizing the pairwise angle between all vectors maximizes p ψi (X = i) for all 1 ≤ i ≤ d. e ∥ϕ∥ 2 +(d-1)e ∥ϕ∥ 2 cos(θ) . Clearly, among such probability vectors, the minimum entropy is achieved at the boundary where cos(θ) is minimized, i.e., when the {ϕ j } d j=1 are maximally equiangular. To investigate this dimension, we the image embeddings of both Aircraft and ChestX using t-sne (Van der Maaten & Hinton, 2008) dimensionality reduction. Figure 4 visualizes these embeddings colored by class identity. We find the ViT-huge model pre-trained on Laion-2b better separates the Aircraft dataset than the ViT-base model pre-trained using the CLIP objective; however, both models do not reasonably separate ChestX. We postulate that an image encoder that can capture the axes of variability among image embeddings is crucial for strong CAML performance, and the reason we observe significantly improved results on Aircraft but not ChestX when using a Laion-2b ViT-h image encoder.", "cite_spans": [], "ref_spans": [{"start": 2080, "end": 2081, "text": "4", "ref_id": null}], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "Taken together, these results indicate CAML is modular: as foundational model feature extractors continue to improve, CAML will be able to capture these advances to improve its own performance.Assignment of Labels to Support Set Classes Analysis. Symmetry to the assignment of labels to support set classes is a desirable property of few-shot learning algorithms. For instance, the predictions for [(bear, 1), (tower, 2), (tree, 3)] should be the same if the labels are permuted to [(bear, 3), (tower 1), (tree, 2)]. CAML is not invariant to permutations in the assignment of classes to support set examples as implied by eq. ( 1) in Section 4.2; however, we empirically find it is robust to them. Label symmetry is distinct from the permutation invariance property of CAML that is discussed in Section 4.3. Tangibly for the sequence [(bear, 1), (tower, 2), (tree, 3)], permutation invariance ensures the predictions are the same as if the order of demonstrations is permuted to [(tower, 2), (tree, 3), (bear, 1)].In Figure 5 (left), we visualize the histogram of the correct class probability for the example presented in Figure 2a after permuting the assignment of labels to support-set images for all 120 permutations", "cite_spans": [], "ref_spans": [{"start": 1024, "end": 1025, "text": "5", "ref_id": null}, {"start": 1130, "end": 1132, "text": "2a", "ref_id": null}], "eq_spans": [], "section": "A.2.1 AN ELMES MINIMIZES", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Towards in-context scene understanding", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2024, "venue": "Advances in Neural Information Processing Systems", "volume": "36", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Towards in-context scene understanding. Advances in Neural Information Processing Systems, 36, 2024.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Visual prompting via image inpainting", "authors": [{"first": "<PERSON>", "middle": [], "last": "Bar", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "25005--25017", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Visual prompting via image inpainting. Advances in Neural Information Processing Systems, 35:25005-25017, 2022.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Meta-learning with differentiable closed-form solvers", "authors": [{"first": "Luca", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["F"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON> <PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1805.08136"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Meta-learning with differen- tiable closed-form solvers. arXiv preprint arXiv:1805.08136, 2018.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Language models are few-shot learners", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Subbiah", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "1877--1901", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Language models are few-shot learners. Advances in neural information processing systems, 33:1877-1901, 2020.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Universeg: Universal medical image segmentation", "authors": [{"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Butoi", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "R", "middle": [], "last": "Mert", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["V"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Dalca", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "21438--21451", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Universeg: Universal medical image segmentation. pp. 21438-21451, 2023.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Emerging properties in self-supervised vision transformers", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF international conference on computer vision", "volume": "", "issue": "", "pages": "9650--9660", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Emerging properties in self-supervised vision transformers. In Proceedings of the IEEE/CVF international conference on computer vision, pp. 9650-9660, 2021.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "A closer look at few-shot classification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zsolt", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1904.04232"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. A closer look at few-shot classification. arXiv preprint arXiv:1904.04232, 2019.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "A closer look at few-shot classification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zsolt", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. A closer look at few-shot classification, 2020.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "In search of art", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "Computer Vision-ECCV 2014 Workshops", "volume": "", "issue": "", "pages": "54--70", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. In search of art. In Computer Vision-ECCV 2014 Workshops: Zurich, Switzerland, September 6-7 and 12, 2014, Proceedings, Part I 13, pp. 54-70. Springer, 2015.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Imagenet: A large-scale hierarchical image database", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2009, "venue": "2009 IEEE Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "248--255", "other_ids": {"DOI": ["10.1109/CVPR.2009.5206848"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Imagenet: A large-scale hier- archical image database. In 2009 IEEE Conference on Computer Vision and Pattern Recognition, pp. 248-255, 2009. doi: 10.1109/CVPR.2009.5206848.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "Xiaohua", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.11929"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint arXiv:2010.11929, 2020.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "The PASCAL Visual Object Classes Challenge", "authors": [{"first": "M", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["K I"], "last": "<PERSON>", "suffix": ""}, {"first": "J", "middle": [], "last": "Winn", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. The PASCAL Visual Object Classes Challenge 2012 (VOC2012) Results. http://www.pascal- network.org/challenges/VOC/voc2012/workshop/index.html.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Equiangular tight frames that contain regular simplices", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "King", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Linear Algebra and its applications", "volume": "555", "issue": "", "pages": "98--138", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, and <PERSON>. Equiangular tight frames that contain regular simplices. Linear Algebra and its applications, 555:98-138, 2018.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "In-context learning for few-shot molecular property prediction", "authors": [{"first": "<PERSON>", "middle": [], "last": "Fifty", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. In-context learning for few-shot molecular property prediction, 2023.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Conditional neural processes", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Maddison", "suffix": ""}, {"first": "Tiago", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yee", "middle": ["<PERSON>e"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Rezende", "suffix": ""}, {"first": "E<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "1704--1713", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Conditional neural processes. In International conference on machine learning, pp. 1704-1713. PMLR, 2018.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Neural turing machines", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Dani<PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1410.5401"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. Neural turing machines. arXiv preprint arXiv:1410.5401, 2014.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "A broader study of cross-domain few-shot learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["V"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Computer Vision-ECCV 2020: 16th European Conference", "volume": "16", "issue": "", "pages": "124--141", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. A broader study of cross-domain few-shot learning. In Computer Vision-ECCV 2020: 16th European Conference, Glasgow, UK, August 23-28, 2020, Proceedings, Part XXVII 16, pp. 124-141. Springer, 2020.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Long short-term memory", "authors": [{"first": "Sepp", "middle": [], "last": "<PERSON><PERSON>reiter", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1997, "venue": "Neural computation", "volume": "9", "issue": "8", "pages": "1735--1780", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>. Long short-term memory. Neural computation, 9(8): 1735-1780, 1997.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Learning to learn using gradient descent", "authors": [{"first": "Sepp", "middle": [], "last": "<PERSON><PERSON>reiter", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Younger", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2001, "venue": "Artificial Neural Networks-ICANN 2001: International Conference", "volume": "11", "issue": "", "pages": "87--94", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON>. Learning to learn using gradient descent. In Artificial Neural Networks-ICANN 2001: International Conference Vienna, Austria, August 21-25, 2001 Proceedings 11, pp. 87-94. Springer, 2001.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Pushing the limits of simple pipelines for few-shot learning: External data and fine-tuning make a difference", "authors": [{"first": "Shell", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Da", "middle": [], "last": "Li", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>ung", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Pushing the limits of simple pipelines for few-shot learning: External data and fine-tuning make a difference, 2022.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Dynamic distillation network for cross-domain few-shot recognition with unlabeled data", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Islam", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Panda", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "3584--3595", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Dynamic distillation network for cross-domain few-shot recognition with unlabeled data. Advances in Neural Information Processing Systems, 34:3584-3595, 2021.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Universal meta-learning architecture and algorithms. Meta-learning in computational intelligence", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Gr Ąbczewski", "suffix": ""}], "year": 2011, "venue": "", "volume": "", "issue": "", "pages": "1--76", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. Universal meta-learning architecture and algorithms. Meta-learning in computational intelligence, pp. 1-76, 2011.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Learning to remember rare events", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "Ofir", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Aurko", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1703.03129"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Learning to remember rare events. arXiv preprint arXiv:1703.03129, 2017.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Universal few-shot learning of dense prediction tasks with visual token matching", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>wo<PERSON>", "middle": [], "last": "Cho", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.14969"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Univer- sal few-shot learning of dense prediction tasks with visual token matching. arXiv preprint arXiv:2303.14969, 2023.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "General-purpose in-context learning by meta-learning transformers", "authors": [{"first": "Louis", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Metz", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2212.04458"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. General-purpose in-context learning by meta-learning transformers. arXiv preprint arXiv:2212.04458, 2022.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Selfattention between datapoints: Going beyond individual input-output pairs in deep learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Kossen", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Band", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Rainforth", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gal", "suffix": ""}], "year": 2021, "venue": "Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "28742--28756", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Self- attention between datapoints: Going beyond individual input-output pairs in deep learning. Ad- vances in Neural Information Processing Systems, 34:28742-28756, 2021.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Human-level concept learning through probabilistic program induction", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Brenden M Lake", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Science", "volume": "350", "issue": "6266", "pages": "1332--1338", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Human-level concept learning through probabilistic program induction. Science, 350(6266):1332-1338, 2015.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Building machines that learn and think like people", "authors": [{"first": "", "middle": [], "last": "Brenden M Lake", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Behavioral and brain sciences", "volume": "40", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Building machines that learn and think like people. Behavioral and brain sciences, 40:e253, 2017.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Microsoft coco: Common objects in context", "authors": [{"first": "Tsung-Yi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Belongie", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Perona", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "C", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2014, "venue": "Computer Vision-ECCV 2014: 13th European Conference", "volume": "", "issue": "", "pages": "740--755", "other_ids": {}, "num": null, "urls": [], "raw_text": "Tsung<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> <PERSON>. Microsoft coco: Common objects in context. In Computer Vision- ECCV 2014: 13th European Conference, Zurich, Switzerland, September 6-12, 2014, Proceedings, Part V 13, pp. 740-755. Springer, 2014.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Fine-grained visual classification of aircraft", "authors": [{"first": "Subhransu", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Esa", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2013, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1306.5151"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Fine-grained visual classification of aircraft. arXiv preprint arXiv:1306.5151, 2013.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "A simple neural attentive metalearner", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Xi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1707.03141"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. A simple neural attentive meta- learner. arXiv preprint arXiv:1707.03141, 2017.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Transformers can do bayesian inference", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Pineda <PERSON>ngo", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2112.10510"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Transformers can do bayesian inference. arXiv preprint arXiv:2112.10510, 2021.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Understanding cross-domain few-shot learning based on domain similarity and few-shot difficulty", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Oh", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Namgyu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Yun", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "2622--2636", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Understanding cross-domain few-shot learning based on domain similarity and few-shot difficulty. Advances in Neural Information Processing Systems, 35:2622-2636, 2022.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Prevalence of neural collapse during the terminal phase of deep learning training", "authors": [{"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "Han", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the National Academy of Sciences", "volume": "117", "issue": "40", "pages": "24652--24663", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Prevalence of neural collapse during the terminal phase of deep learning training. Proceedings of the National Academy of Sciences, 117(40): 24652-24663, 2020.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Self-training for few-shot transfer across extreme task differences", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.07734"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. Self-training for few-shot transfer across extreme task differences. arXiv preprint arXiv:2010.07734, 2020.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Learning transferable visual models from natural language supervision", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Wook"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hall<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "8748--8763", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Learning transferable visual models from natural language supervision. In International conference on machine learning, pp. 8748-8763. PMLR, 2021.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Meta-learning for semi-supervised few-shot classification", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ren", "suffix": ""}, {"first": "Eleni", "middle": [], "last": "Triantafillou", "suffix": ""}, {"first": "Sachin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Swersky", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1803.00676"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Meta-learning for semi-supervised few-shot classification. arXiv preprint arXiv:1803.00676, 2018.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Large-scale classification of fine-art paintings: Learning the right metric on the right feature", "authors": [{"first": "Babak", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Elgam<PERSON>", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1505.00855"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Large-scale classification of fine-art paintings: Learning the right metric on the right feature. arXiv preprint arXiv:1505.00855, 2015.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Metalearning with memory-augmented neural networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Lillicrap", "suffix": ""}], "year": 2016, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "1842--1850", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Meta- learning with memory-augmented neural networks. In International conference on machine learning, pp. 1842-1850. PMLR, 2016.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "FGVCx fungi classification challenge", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. FGVCx fungi classification challenge 2018. github.com/ visipedia/fgvcx_fungi_comp, 2018.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Laion-5b: An open large-scale dataset for training next generation image-text models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Beaumont", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "25278--25294", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Laion-5b: An open large-scale dataset for training next generation image-text models. Advances in Neural Information Processing Systems, 35:25278-25294, 2022.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Prototypical networks for few-shot learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Swersky", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Prototypical networks for few-shot learning, 2017.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "How to train your vit? data, augmentation, and regularization in vision transformers", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Xiaohua", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2106.10270"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. How to train your vit? data, augmentation, and regularization in vision transformers. arXiv preprint arXiv:2106.10270, 2021.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Visualizing data using t-sne", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2008, "venue": "Journal of machine learning research", "volume": "9", "issue": "11", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Visualizing data using t-sne. Journal of machine learning research, 9(11), 2008.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Attention is all you need", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "Llion", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "Illia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in neural information processing systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. Advances in neural information processing systems, 30, 2017.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Matching networks for one shot learning", "authors": [{"first": "Oriol", "middle": [], "last": "<PERSON>yal<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Lillicrap", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Advances in neural information processing systems", "volume": "29", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, et al. Matching networks for one shot learning. Advances in neural information processing systems, 29, 2016.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "The caltech-ucsd birds-200", "authors": [{"first": "<PERSON>", "middle": [], "last": "Wah", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Perona", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Belongie", "suffix": ""}], "year": 2011, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. The caltech-ucsd birds-200-2011 dataset. 2011.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Images speak in images: A generalist painter for in-context visual learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chunhua", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition", "volume": "", "issue": "", "pages": "6830--6839", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Images speak in images: A generalist painter for in-context visual learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 6830-6839, 2023.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "Randomized online pca algorithms with regret bounds that are logarithmic in the dimension", "authors": [{"first": "K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Kuz<PERSON>", "suffix": ""}], "year": 2008, "venue": "Journal of Machine Learning Research", "volume": "9", "issue": "", "pages": "2287--2320", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. Randomized online pca algorithms with regret bounds that are logarithmic in the dimension. Journal of Machine Learning Research, 9(Oct):2287-2320, 2008.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Lower bounds on the maximum cross correlation of signals (corresp.)", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1974, "venue": "IEEE Transactions on Information theory", "volume": "20", "issue": "3", "pages": "397--399", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Lower bounds on the maximum cross correlation of signals (corresp.). IEEE Transac- tions on Information theory, 20(3):397-399, 1974.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Few-shot learning with localization in realistic settings", "authors": [{"first": "<PERSON>", "middle": [], "last": "Wertheimer", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of the IEEE/CVF conference on computer vision and pattern recognition", "volume": "", "issue": "", "pages": "6558--6567", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. Few-shot learning with localization in realistic settings. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 6558-6567, 2019.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Few-shot classification with feature map reconstruction networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "Wertheimer", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Few-shot classification with feature map reconstruction networks, 2020.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Do we really need a learnable classifier at the end of deep neural network? arXiv e-prints", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Shi<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiangtai", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dacheng", "middle": [], "last": "Tao", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Do we really need a learnable classifier at the end of deep neural network? arXiv e-prints, pp. arXiv-2203, 2022.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Shallow bayesian meta learning for real-world few-shot recognition", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Shallow bayesian meta learning for real-world few-shot recognition, 2021.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "What makes good examples for visual in-context learning?", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Kaiyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2301.13670"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. What makes good examples for visual in-context learning? arXiv preprint arXiv:2301.13670, 2023.", "links": null}}, "ref_entries": {"FIGREF0": {"num": null, "type_str": "figure", "uris": null, "text": "Figure 1: Overview of CAML. Query and support set images are encoded with a pre-trained feature extractor", "fig_num": "1"}, "FIGREF1": {"num": null, "type_str": "figure", "uris": null, "text": "Left: An example task-classify images by the objects depicted. Center: image embeddings output from the Image Encoder (CLIP) in CAML . Right: joint image-label representations output by the non-causal sequence model in CAML for the same task. Left: An example task-classify images by the artistic medium used. Center: CLIP image embeddings output from the Image Encoder (CLIP) in CAML . Right: joint image-label representations output by the non-causal sequence model in CAML for the same task.", "fig_num": null}, "FIGREF2": {"num": null, "type_str": "figure", "uris": null, "text": "Figure 2: Two sample tasks over the same support images but utilizing different criteria to define classes. The nature of the query image informs the task being presented, e.g. classification by object (top) vs. classification by texture (bottom). For both tasks, the output of the non-causal sequence model provides better separation among class representations than CLIP embeddings and groups the query representation with the proper task, even when projected into 2D space by PCA.", "fig_num": "2"}, "FIGREF3": {"num": null, "type_str": "figure", "uris": null, "text": ".1 EQUIANGULAR TIGHT FRAME<PERSON> et al. (2020) coin the term Simplex Equianguar Tight Frame to describe a set of vectors {ϕ j } d j=1 such that the minimum angle between any two pairs of vectors is maximized and all vectors have equal norm. Formally, Definition 3. Let R d be a d-dimensional inner product space over R with the Euclidean inner product. A Simplex ETF is a set of d vectors {ϕ j } d j=1 , ϕ j ∈ R d , specified by the columns of d d-1 (I d -1 d 11 T )", "fig_num": "1"}, "FIGREF4": {"num": null, "type_str": "figure", "uris": null, "text": "Figure 3: A visualization of a d = 4 ELMES in R 3 . Observe the endpoints of the vectors of an ELMES lie on the vertices of a centered regular tetrahedron.", "fig_num": "3"}, "FIGREF5": {"num": null, "type_str": "figure", "uris": null, "text": "-Scale Pre-Training. All methods evaluated in the universal meta-learning setting adhere to the same pre-training paradigm. For each large-scale image classification dataset, we reformulate the objective from typical supervised image classification to both a 5-way-1-shot and a 5-way-5-shot episodic prediction tasks. Within a dataset, examples from different classes are randomly sampled to compose a batch of episodes, and after exhausting iterating through every training example, this process is repeated with the next dataset. Iterating through each dataset in our set of ImageNet-1k, Fungi, MSCOCO, and WikiArt then constitutes a single epoch of training. ProtoNet and MetaOpt Implementations. For the ProtoNet and MetaOpt algorithms, we evaluate two settings. The first freezes the CLIP backbone and then applies the metric-learning objectivecosine distance for ProtoNet and SVM for MetaOpt-to classify the query image from the unmodified CLIP embeddings. The second emulates P>M>F Hu et al. (2022) by fine-tuning the CLIP backbone during large-scale pre-training with the metric-learning objective function. During inference, the metric-learning objective is applied to the fine-tuned CLIP embeddings to classify query images. MetaQDA Implementation. We follow the MetaQDA algorithm presented in <PERSON> et al. (2021). Specifically, we freeze the CLIP feature extractor backbone and train the MetaQDA classifier during large-scale episodic pre-training. SNAIL Implementation. We use the architecture presented in <PERSON><PERSON><PERSON> et al. (2017) but with the hidden dimension of the Attention and Temporal Convolution Blocks adapted to CLIP embeddings rather than the ResNet embeddings used in the original implementation. As in this Mishra et al. (2017), we freeze the CLIP feature extractor and train the SNAIL model parameters during large-scale pre-training.", "fig_num": null}, "FIGREF6": {"num": null, "type_str": "figure", "uris": null, "text": "Figure 5: (Left) histogram of the correct class probability for the example presented in Figure 2a after the assignment of labels to support-set images for all 120 permutations of the 5-way-1-shot task. (Right) histogram of the average standard deviation of all 120 permutations of the 5-way-1-shot task for 1,000 samples from mini-ImageNet.", "fig_num": null}, "TABREF0": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method (Backbone)</td><td colspan=\"2\">CIFAR-fs</td><td colspan=\"2\">MiniImageNet</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td>In-Domain [Meta-Training]</td><td/><td/><td/><td/></tr><tr><td>P&gt;M&gt;F Hu et al. (2022)</td><td>84.3</td><td>92.2</td><td>95.3</td><td>98.4</td></tr><tr><td>Universal Meta-Learning;</td><td/><td/><td/><td/></tr><tr><td>No Meta-Training or Finetuning</td><td/><td/><td/><td/></tr><tr><td>ProtoNet (Snell et al., 2017)</td><td colspan=\"4\">62.9±.2 79.7±.2 92.1±.1 97.1±.0</td></tr><tr><td>ProtoNet  †</td><td colspan=\"4\">57.7±.2 81.0±.2 85.3±.2 96.0±.1</td></tr><tr><td>MetaOpt (Lee et al., 2019)</td><td colspan=\"4\">53.1±.3 73.1±.2 78.5±.2 91.6±.1</td></tr><tr><td>MetaOpt  †</td><td colspan=\"4\">61.7±.2 83.1±.1 86.9±.2 96.5±.1</td></tr><tr><td>MetaQDA (Zhang et al., 2021)</td><td colspan=\"4\">60.4±.2 83.2±.1 88.2±.2 97.4±.0</td></tr><tr><td>GPICL (Kirsch et al., 2022)</td><td colspan=\"4\">41.5±.4 78.3±.2 95.6±.1 98.2±.1</td></tr><tr><td>SNAIL (Mishra et al., 2017)</td><td colspan=\"4\">62.1±.3 71.1±.3 93.6±.1 98.1±.0</td></tr><tr><td>CAML</td><td colspan=\"4\">70.8±.2 85.5±.1 96.2±.1 98.6±.0</td></tr></table>", "text": "MiniImageNet & CIFAR-fs mean accuracy and standard error across 10,000 test epochs. † indicates the pre-trained image encoder backbone was frozen during training."}, "TABREF1": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method (Backbone)</td><td colspan=\"2\">Pascal + Paintings</td><td colspan=\"2\">Paintings</td><td colspan=\"2\">Pascal</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td>In-Domain [Meta-Training]</td><td/><td/><td/><td/><td/><td/></tr><tr><td>P&gt;M&gt;F</td><td>60.7</td><td>74.4</td><td>53.2</td><td>65.8</td><td>72.2</td><td>84.4</td></tr><tr><td>Universal Meta-Learning</td><td/><td/><td/><td/><td/><td/></tr><tr><td>ProtoNet</td><td colspan=\"6\">49.6±.2 63.5±.1 38.3±.2 48.2±.1 77.9±.2 87.3±.2</td></tr><tr><td>ProtoNet  †</td><td colspan=\"6\">52.2±.2 70.6±.1 48.3±.2 64.1±.1 72.2±.2 84.3±.2</td></tr><tr><td>MetaOpt</td><td colspan=\"6\">38.2±.2 58.2±.1 31.6±.2 48.0±.1 63.7±.2 81.7±.2</td></tr><tr><td>MetaOpt  †</td><td colspan=\"6\">53.2±.2 74.8±.1 49.3±.2 65.9±.1 72.8±.2 84.4±.2</td></tr><tr><td>MetaQDA</td><td colspan=\"6\">53.8±.2 74.1±.1 49.4±.2 66.6±.1 73.5±.2 85.2±.2</td></tr><tr><td>GPICL</td><td colspan=\"6\">62.6±.2 74.6±.1 51.6±.2 61.0±.1 81.7±.2 88.2±.2</td></tr><tr><td>SNAIL</td><td colspan=\"6\">62.5±.2 77.6±.1 51.9±.2 65.8±.1 79.7±.2 88.0±.2</td></tr><tr><td>CAML</td><td colspan=\"6\">63.8±.2 78.3±.1 51.1±.2 65.2±.1 82.6±.2 89.7±.1</td></tr><tr><td colspan=\"7\">2. Fine-Grained Image Classification: CUB (Wah et al., 2011), Aircraft (Maji et al., 2013), meta-</td></tr><tr><td colspan=\"7\">iNat (Wertheimer &amp; Hariharan, 2019), and tiered meta-iNat (Wertheimer &amp; Hariharan, 2019)</td></tr><tr><td colspan=\"7\">3. Unnatural Image Classification: ChestX (Guo et al., 2020) and Paintings (Crowley &amp; Zisserman,</td></tr><tr><td>2015)</td><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"7\">4. Inter-Domain Image Classification: Pascal+Paintings (Everingham et al.; Crowley &amp; Zisserman,</td></tr></table>", "text": "Pascal & Paintings mean accuracy and standard error across 10,000 test epochs. † indicates the the pre-trained image encoder backbone was frozen during training."}, "TABREF2": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method (Backbone)</td><td colspan=\"2\">meta-iNat</td><td colspan=\"2\">tiered meta-iNat</td><td colspan=\"2\">ChestX</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td>In-Domain [Meta-Training]</td><td/><td/><td/><td/><td/><td/></tr><tr><td>P&gt;M&gt;F</td><td>91.2</td><td>96.1</td><td>74.8</td><td>89.9</td><td>27.0</td><td>32.1</td></tr><tr><td>Universal Meta-Learning</td><td/><td/><td/><td/><td/><td/></tr><tr><td>ProtoNet</td><td colspan=\"6\">78.4±.2 89.4±.1 66.3±.2 82.2±.2 22.4±.1 25.3±.1</td></tr><tr><td>ProtoNet  †</td><td colspan=\"6\">84.5±.2 94.8±.1 73.8±.2 89.5±.1 22.7±.1 25.8±.1</td></tr><tr><td>MetaOpt</td><td colspan=\"6\">53.0±.2 77.7±.2 37.3±.2 63.0±.2 20.8±.1 23.0±.1</td></tr><tr><td>MetaOpt  †</td><td colspan=\"6\">85.5±.2 95.5±.1 75.1±.2 91.9±.1 23.0±.1 27.4±.1</td></tr><tr><td>MetaQDA</td><td colspan=\"6\">86.3±.2 95.9±.1 76.0±.2 92.4±.1 22.6±.1 27.0±.1</td></tr><tr><td>GPICL</td><td colspan=\"6\">90.0±.2 95.1±.1 60.8±.5 87.6±.2 20.1±.1 20.9±.1</td></tr><tr><td>SNAIL</td><td colspan=\"6\">89.1±.2 94.8±.1 77.3±.2 86.5±.2 20.2±.0 20.0±.0</td></tr><tr><td>CAML</td><td colspan=\"6\">91.2±.2 96.3±.1 81.9±.2 91.6±.1 21.5±.1 22.2±.1</td></tr></table>", "text": "meta-iNat & tiered meta-iNat & ChestX mean accuracy and standard error across 10,000 test epochs. † indicates the the pre-trained image encoder backbone was frozen during training."}, "TABREF3": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method (Backbone)</td><td>CUB</td><td/><td colspan=\"2\">tiered-ImageNet</td><td colspan=\"2\">Aircraft</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td>In-Domain [Meta-Training]</td><td/><td/><td/><td/><td/><td/></tr><tr><td>P&gt;M&gt;F</td><td>92.3</td><td>97.0</td><td>93.5</td><td>97.3</td><td>79.8</td><td>89.3</td></tr><tr><td>Universal Meta-Learning</td><td/><td/><td/><td/><td/><td/></tr><tr><td>ProtoNet</td><td colspan=\"4\">59.4±.2 77.3±.2 93.5±.1 97.4±.1</td><td>37.9±.2</td><td>52.5±.2</td></tr><tr><td>ProtoNet  †</td><td colspan=\"4\">87.0±.2 97.1±.1 87.3±.2 96.1±.1</td><td>62.4±.3</td><td>82.0±.2</td></tr><tr><td>MetaOpt</td><td colspan=\"4\">71.5±.2 41.2±.2 76.6±.2 89.6±.1</td><td>41.6±.2</td><td>26.7±.1</td></tr><tr><td>MetaOpt  †</td><td colspan=\"4\">87.9±.2 97.2±.1 88.2±.2 96.5±.1</td><td>64.8±.2</td><td>82.6±.2</td></tr><tr><td>MetaQDA</td><td colspan=\"4\">88.3±.2 97.4±.1 89.4±.2 97.0±.1</td><td>63.6±.3</td><td>83.0±.2</td></tr><tr><td>GPICL</td><td colspan=\"4\">75.1±.5 94.5±.1 94.6±.1 97.2±.1</td><td>19.8±.2</td><td>61.8±.3</td></tr><tr><td>SNAIL</td><td colspan=\"6\">87.5±.2 92.8±.2 93.1±.1 97.3±.1 48.9 ± .3 35.8±.3</td></tr><tr><td>CAML</td><td colspan=\"4\">91.8±.2 97.1±.1 95.4±.1 98.1±.1</td><td>63.3±.3</td><td>79.1±.2</td></tr></table>", "text": "CUB & tiered-ImageNet & Aircraft mean accuracy and standard error across 10,000 test epochs. † indicates the the pre-trained image encoder backbone was frozen during training."}, "TABREF4": {"num": null, "type_str": "table", "html": null, "content": "<table/>", "text": "Table 2, Table 3, and Table 4 and indicate that CAML sets a new state-of-the-art for universal meta-learning by significantly outperforming other baselines on 14 of 22 evaluation settings. For 5 of the other 8 evaluation settings, CAML matches-or nearly matches-the best performing baseline. Remarkably, CAML also performs competitively with P>M>F on 8 out of 11 meta-learning benchmarks, even though P>M>F meta-trains on the training set of each benchmark."}, "TABREF5": {"num": null, "type_str": "table", "html": null, "content": "<table/>", "text": "as this set of vectors does not form a tight frame in R d . Definition 4. Let R be a d-dimensional space over R with the Euclidean inner product. An Equiangular Tight Frame (ETF) is a set of non-zero, equal norm vectors {ϕ j } n j=1 , n ≥ d, that achieves the Welch lower bound:"}, "TABREF6": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">CIFAR-fs</td><td colspan=\"2\">MiniImageNet</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td>CAML [ELMES Class Embedding]</td><td colspan=\"4\">70.8±.2 85.5±.1 96.2±.1 98.6±.0</td></tr><tr><td colspan=\"5\">CAML [Learnable Class Embedding] 71.1±.2 85.9±.1 96.1±.1 98.7±.0</td></tr></table>", "text": "MiniImageNet & CIFAR-fs mean accuracy and standard error across 10,000 test epochs."}, "TABREF7": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method</td><td>CUB</td><td/><td colspan=\"2\">tiered-ImageNet</td><td colspan=\"2\">Aircraft</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td>CAML [ELMES Class Embedding]</td><td colspan=\"6\">91.8±.2 97.1±.1 95.4±.1 98.1±.1 63.3±.3 79.1±.2</td></tr><tr><td colspan=\"7\">CAML [Learnable Class Embedding] 91.8±.2 97.1±.1 95.3±.1 98.3±.1 66.3±.2 80.6±.2</td></tr></table>", "text": "CUB & tiered-ImageNet & Aircraft mean accuracy and standard error across 10,000 test epochs."}, "TABREF8": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">Pascal + Paintings</td><td colspan=\"2\">Paintings</td><td colspan=\"2\">Pascal</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td>CAML [ELMES Class Embedding]</td><td colspan=\"6\">63.8±.2 78.3±.1 51.1±.2 65.2±.1 82.6±.2 89.7±.1</td></tr><tr><td colspan=\"7\">CAML [Learnable Class Embedding] 63.1±.2 78.0±.1 51.3±.2 65.0±.1 82.1±.2 89.7±.1</td></tr></table>", "text": "Pascal & Paintings mean accuracy and standard error across 10,000 test epochs."}, "TABREF9": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">meta-iNat</td><td colspan=\"2\">tiered meta-iNat</td><td colspan=\"2\">ChestX</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td>CAML [ELMES Class Embedding]</td><td colspan=\"6\">91.2±.2 96.3±.1 81.9±.2 91.6±.1 21.5±.1 22.2±.1</td></tr><tr><td colspan=\"7\">CAML [Learnable Class Embedding] 91.4±.2 96.4±.1 82.1±.2 91.8±.1 21.5±.1 22.6±.1</td></tr></table>", "text": "meta-iNat & tiered meta-iNat & ChestX mean accuracy and standard error across 10,000 test epochs."}, "TABREF10": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">CIFAR-fs</td><td colspan=\"2\">MiniImageNet</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td>CAML (ResNet34)</td><td colspan=\"4\">61.8 ± .2 79.4 ± .2 94.7 ± .1 98.1 ± .0</td></tr><tr><td>CAML (ViT-base)</td><td>70.8±.2</td><td>85.5±.1</td><td>96.2±.1</td><td>98.6±.0</td></tr><tr><td>CAML (ViT-huge) •</td><td>83.3±.4</td><td>93.5±.2</td><td>98.6±.1</td><td>99.6±.0</td></tr></table>", "text": "MiniImageNet & CIFAR-fs mean accuracy and standard error across 10,000 test epochs. • indicates mean and standard error across 2,500 test epochs."}, "TABREF11": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">CUB</td><td colspan=\"2\">tiered-ImageNet</td><td colspan=\"2\">Aircraft</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td>CAML (ResNet34)</td><td colspan=\"6\">75.4 ± .2 88.3 ± .1 96.1 ± .1 98.5 ± .0 45.1 ± .2 58.7 ± .2</td></tr><tr><td>CAML (ViT-base)</td><td>91.8±.2</td><td>97.1±.1</td><td>95.4±.1</td><td>98.1±.1</td><td>63.3±.3</td><td>79.1±.2</td></tr><tr><td>CAML (ViT-huge) •</td><td>95.8±.2</td><td>98.7±.1</td><td>96.8±.2</td><td>98.8±.1</td><td>81.8±.4</td><td>92.1±.3</td></tr></table>", "text": "CUB & tiered-ImageNet & Aircraft mean accuracy and standard error across 10,000 test epochs. • indicates mean and standard error across 2,500 test epochs."}, "TABREF12": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">Pascal + Paintings</td><td colspan=\"2\">Paintings</td><td colspan=\"2\">Pascal</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td>CAML (ResNet34)</td><td colspan=\"6\">57.5 ± .2 71.0 ± .1 46.1 ± .2 57.3 ± .1 77.4 ± .2 86.8 ± .1</td></tr><tr><td>CAML (ViT-base)</td><td>63.8±.2</td><td>78.3±.1</td><td>51.1±.2</td><td>65.2±.1</td><td>82.6±.2</td><td>89.7±.1</td></tr><tr><td>CAML (ViT-huge) •</td><td>66.4±.4</td><td>81.0±.2</td><td>54.7±.3</td><td>69.9±.2</td><td>83.4±.4</td><td>90.1±.3</td></tr></table>", "text": "Pascal & Paintings mean accuracy and standard error across 10,000 test epochs. • indicates mean and standard error across 2,500 test epochs."}, "TABREF13": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">meta-iNat</td><td colspan=\"2\">tiered meta-iNat</td><td colspan=\"2\">ChestX</td></tr><tr><td/><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td><td>5w-1s</td><td>5w-5s</td></tr><tr><td colspan=\"2\">CAML (ResNet34) 82.4 CAML (ViT-base) 91.2±.2</td><td>96.3±.1</td><td>81.9±.2</td><td>91.6±.1</td><td>21.5±.1</td><td>22.2±.1</td></tr><tr><td>CAML (ViT-huge) •</td><td>94.6±.3</td><td>97.9±.1</td><td>89.3±.4</td><td>95.6±.2</td><td>21.6±.2</td><td>22.0±.2</td></tr></table>", "text": "meta-iNat & tiered meta-iNat & ChestX mean accuracy and standard error across 10,000 test epochs. • indicates mean and standard error across 2,500 test epochs. ± .2 91.4 ± .1 72.3 ± .2 84.6 ± .2 21.8 ± .1 23.6 ± .1 and the learnable class embeddings actually forming an ELMES, we attribute the difference in Aircraft performance to stochasticity in training the model, suggesting that the fixed ELMES encoder is indeed optimal."}}}}