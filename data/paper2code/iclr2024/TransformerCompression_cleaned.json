{"paper_id": "TransformerCompression", "title": "SLICEGPT: COMPRESS LARGE LANGUAGE MODELS BY DELETING ROWS AND COLUMNS", "abstract": "Large language models have become the cornerstone of natural language processing, but their use comes with substantial costs in terms of compute and memory resources. Sparsification provides a solution to alleviate these resource constraints, and recent works have shown that trained models can be sparsified post-hoc. Existing sparsification techniques face challenges as they need additional data structures and offer constrained speedup with current hardware. In this paper we present SliceGPT, a new post-training sparsification scheme which replaces each weight matrix with a smaller (dense) matrix, reducing the embedding dimension of the network. Through extensive experimentation we show that SliceGPT can remove up to 25% of the model parameters (including embeddings) for LLAMA-2 70B, OPT 66B and Phi-2 models while maintaining 99%, 99% and 90% zero-shot task performance of the dense model respectively. Our sliced models run on fewer GPUs and run faster without any additional code optimization: on 24GB consumer GPUs we reduce the total compute for inference on LLAMA-2 70B to 64% of that of the dense model; on 40GB A100 GPUs we reduce it to 66%. We offer a new insight, computational invariance in transformer networks, which enables SliceGPT and we hope it will inspire and enable future avenues to reduce memory and computation demands for pre-trained models. Code is available at: https://github.com/microsoft/TransformerCompression . * Equal contribution † Work completed as an intern at Microsoft.", "pdf_parse": {"paper_id": "TransformerCompression", "abstract": [{"text": "Large language models have become the cornerstone of natural language processing, but their use comes with substantial costs in terms of compute and memory resources. Sparsification provides a solution to alleviate these resource constraints, and recent works have shown that trained models can be sparsified post-hoc. Existing sparsification techniques face challenges as they need additional data structures and offer constrained speedup with current hardware. In this paper we present SliceGPT, a new post-training sparsification scheme which replaces each weight matrix with a smaller (dense) matrix, reducing the embedding dimension of the network. Through extensive experimentation we show that SliceGPT can remove up to 25% of the model parameters (including embeddings) for LLAMA-2 70B, OPT 66B and Phi-2 models while maintaining 99%, 99% and 90% zero-shot task performance of the dense model respectively. Our sliced models run on fewer GPUs and run faster without any additional code optimization: on 24GB consumer GPUs we reduce the total compute for inference on LLAMA-2 70B to 64% of that of the dense model; on 40GB A100 GPUs we reduce it to 66%. We offer a new insight, computational invariance in transformer networks, which enables SliceGPT and we hope it will inspire and enable future avenues to reduce memory and computation demands for pre-trained models. Code is available at: https://github.com/microsoft/TransformerCompression . * Equal contribution † Work completed as an intern at Microsoft.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Large language models (LLMs) are neural networks with billions of parameters, trained on trillions of tokens (<PERSON> et al., 2023) . The cost of training an LLM has caused a shift to re-using pre-trained models for multiple tasks, the foundation model paradigm. The size of LLMs makes deploying a pre-trained model an expensive undertaking. Many models require multiple GPUs to be able to compute a prediction, and because the models are autoregressive, multiple forward passes of the neural network are needed to generate text responses. It is therefore of widespread interest to reduce the computational requirements of these models, usually performed via post-training techniques referred to as model compression.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "A majority of model compression techniques fall into one of four categories: distillation, tensor decomposition (which includes low-rank factorization), pruning and quantization (<PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2023; <PERSON> & <PERSON>, 2021) . In this work we focus on pruning, though we hope that our methodology may influence future work on other areas. Whilst pruning methods have been around for some time, many approaches require recovery fine-tuning (RFT) after pruning to maintain performance, making the overall process an expensive and hard-to-scale task. With SliceGPT we compress large models using a single GPU in just a few hours and maintain competitive performance on generation and downstream tasks even without RFT.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Unstructured sparsity", "section": "X W", "sec_num": null}, {"text": "2:4 Structured sparsity XQ Q ⊤ W Slicing (ours)", "section": "X W", "sec_num": null}, {"text": "Figure 1 : Matrix multiplication of the signal X and a weight matrix W under different types of sparsity. Left: unstructured sparsity, where some elements of W are zero, and X is dense. Middle: 2:4 structured sparsity, where each block of four weight matrix entries contains two zeros, and X is dense. Right: SliceGPT, where after introducing transformation Q, all the sparsity is arranged to the bottom rows of W and the corresponding columns of X are removed.", "section": "X W", "sec_num": null}, {"text": "Pruning methods work by setting some elements of the weight matrices in an LLM to zero, and (optionally) updating the surrounding elements of the matrix to compensate. The result is a sparse pattern which means that some floating point operations can be skipped in the matrix multiplications required in the forward pass of the neural network. The relative speedup of the operations depends on the level of sparsity and the sparsity pattern: more structured sparsity is associated with more computational gain. In contrast to other pruning methods, SliceGPT prunes away (slices off!) entire rows or columns of the weight matrices. Before slicing, we perform a single transformation of the network which leaves the predictions invariant, but allows the slicing to have only a small effect.", "section": "X W", "sec_num": null}, {"text": "The result is that weight matrices are smaller, and the signals passed between blocks of the neural network are smaller too: we reduce the embedding dimension of the neural network.", "section": "X W", "sec_num": null}, {"text": "Figure 1 compares our approach with existing sparsity methods. Our contributions are as follows:", "section": "X W", "sec_num": null}, {"text": "1. We introduce the idea of computational invariance: we show that we can apply orthogonalmatrix transformations to each weight matrix in a transformer without changing the model.", "section": "X W", "sec_num": null}, {"text": "2. We use this to edit each block in a transformer architecture, such that we are projecting the signal matrix1 between blocks onto its own principal components. We remove columns or rows of the transformed weight matrices to reduce the model size. We call the transformation and removal of weights SliceGPT.", "section": "X W", "sec_num": null}, {"text": "3. We conduct multiple experiments on OPT (<PERSON> et al., 2022) and LLAMA-2 (<PERSON><PERSON><PERSON><PERSON> et al., 2023) LLMs, demonstrating that SliceGPT is able to compress these models by up to 30% with superior perplexity to the state of the art 2:4 scheme. On downstream tasks we additionally experiment with Phi-2 and show that all models can be sliced by up to 30% while maintaining >90% of the dense performance.", "section": "X W", "sec_num": null}, {"text": "In this section, we first describe some necessary background on transformer architectures, which allows us to introduce notation which we will use to prove our main results. Then we describe related work on sparsification for compressing such architectures.", "section": "BACKGROUND", "sec_num": "2"}, {"text": "Transformer networks (<PERSON><PERSON><PERSON><PERSON> et al., 2017) are a class of neural networks that have been shown to be effective at a wide range of tasks including language modeling. The transformer architecture is composed of a series of layers, each of which is composed of a multi-head self-attention block followed by a feed-forward network block. Between each block, there is a LayerNorm (<PERSON> et al., 2016) (or RMSNorm (<PERSON> & <PERSON>, 2019 )) block. Figure 2 illustrates part of a transformer network: an attention block connected to a Feed Forward Network (FFN) block through a LayerNorm block, with residual connections. The following describes the operations of each component (ignoring dropout, which is not applied post-training).", "section": "TRANSFORMER NETWORKS", "sec_num": "2.1"}, {"text": "Embeddings Let D be the embedding dimension of our transformer, N be the sequence length. The transformer model takes as input a sequence of token IDs and position IDs, and uses them to index the embedding matrices, producing the initial signal X with shape N × D. In what follows we consider, without loss of generality, a single embedding matrix W embd indexed by input sequence s.", "section": "TRANSFORMER NETWORKS", "sec_num": "2.1"}, {"text": "LayerNorm After embeddings, the signal matrix is passed through a LayerNorm operation, which subtracts the mean from each row of the matrix, divides the row by its standard deviation, rescales (columnwise), and adds an offset. We write the LayerNorm block as LayerNorm(X) = RMSNorm(XM)diag(α)", "section": "TRANSFORMER NETWORKS", "sec_num": "2.1"}, {"text": "√ D + 1 N β ⊤ (1)", "section": "TRANSFORMER NETWORKS", "sec_num": "2.1"}, {"text": "where RMSNorm(X) applies2 x ← x/∥x∥ to each row of X. The vector parameter α and offset (vector) parameter β are learned independently at each LayerNorm instance. The constant matrix M = I -1 D 11 ⊤ is a D × D matrix which subtracts the mean from each row of X. 2 ). We can consider the concatenation of these matrices to be a single linear layer, which we denote W in . We also refer to the output matrix as W out . We treat the attention block as σ(XW in + b in )W out + b out3 , where σ represents the multi-head attention operation.", "section": "TRANSFORMER NETWORKS", "sec_num": "2.1"}, {"text": "The other type of block that appears in transformer architectures is a Feed Forward Network (FFN) block. In many cases, this is a Multi-layer Perceptron (MLP), which consists of a linear layer W 1 , followed by an elementwise operation σ, followed by a second linear layer: σ(XW 1 + b 1 )W 2 + b 2 . Some architectures have adopted the gated format, where an additional matrix is used, and the operation is σ(XW 1 + b 1 ) • (XW 2 ) W 3 , where • is an element-wise product. Much like the first three linear layers in the attention module, we can consider the concatenation of W 1 and W 2 to be a single linear operation, and denote it W in . We can therefore denote the operation of MLP or gated FFN layers as σ(XW in )W out , where σ takes a different meaning to that in an attention.", "section": "FFN Blocks", "sec_num": null}, {"text": "Language Modelling (LM) Head All of the transformer networks to which we apply SliceGPT in this paper have a decoder-only structure following (<PERSON><PERSON> et al., 2018) : after multiple layers applying alternating attention and FFN blocks, a head block computes logits which are used to compute the loss during training and token prediction on deployment. The head operation is XW head + b head , where X is the output of the last transformer block.", "section": "FFN Blocks", "sec_num": null}, {"text": "Forward pass Once the model is trained and all of the parameters are set, the computations required in a transformer network to produce predictions involve passing signal matrices from one block to the next until the head node is reached. Since we are able to define both FFN and attention blocks in the form σ(XW in + b in )W out + b out , where we understand that σ represents either a point-wise or multi-head-attention nonlinearity, we are able to describe the forward pass using Algorithm 1.", "section": "FFN Blocks", "sec_num": null}, {"text": "Algorithm 1 The forward pass of a transformer network In the simplest setting, one can employ magnitude-based sparsification, which involves setting the smallest weights in the model to zero (<PERSON> et al., 2016; <PERSON>, 2017; <PERSON> et al., 2019) .", "section": "FFN Blocks", "sec_num": null}, {"text": "Require: {W ℓ in , b ℓ in , W ℓ out b ℓ out } L ℓ=1 //", "section": "FFN Blocks", "sec_num": null}, {"text": "Although magnitude sparsification is scalable, its application to LLMs gives too strong a degradation in performance (Frantar & Alistarh, 2023) . Optimal Brain Surgeon (OBS) (<PERSON><PERSON><PERSON> et al., 1993; <PERSON><PERSON><PERSON> et al., 1989) , a more sophisticated method, systematically removes weights that have the least impact on the loss function. The method compensates for the error introduced by weight removal by updating the un-pruned weights using the inverse of the Hessian matrix. Unfortunately, OBS is impractical for models with a few million parameters due to the need to calculate and store the inverse of the Hessian matrix. To address the computational limitation posed by OBS, recent research has explored two approaches: approximating the inverse of the Hessian matrix such as <PERSON><PERSON><PERSON><PERSON> (Singh & Alistarh, 2020) or applying it separately to each layer such as in Optimal Brain Compression (OBC, Frantar & Alistarh, 2022) , known as layer-wise pruning. While these techniques have proven effective for medium-sized networks, they are not practical for large language models, where individual layer weight matrices typically contain more than 10 8 parameters.", "section": "FFN Blocks", "sec_num": null}, {"text": "GPTQ (<PERSON><PERSON><PERSON> et al., 2022) has solved this issue by quantizing (representing the parameter using lower precision) the weight matrix of LLMs using a column-by-column scheme and updating all not-yet-quantized weights in the next columns. SparseGPT (Frantar & Alistarh, 2023) applied the same idea for pruning and sparsifies the LLMs using unstructured and semi-structured pruning, and Sun et al. (2023) simplified the idea by using only the diagonal of the Hessian. Since achieving endto-end speed improvements through unstructured pruning is a demanding task, they also attempted a similar technique to induce sparsity with semi-structured patterns like 2:4 and 4:8 (<PERSON><PERSON><PERSON> et al., 2021) . However, implementing such structures does not maintain the accuracy of the model.", "section": "FFN Blocks", "sec_num": null}, {"text": "Another approach to compression is low-rank approximation, where each weight matrix is replaced with the product of two matrices with a smaller inner dimension, usually followed by a fine-tuning step (<PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON> & Goldberg, 2020; <PERSON><PERSON> et al., 2020) . To achieve compression, the inner dimension must be smaller than half of the original dimension. In contrast, our method replaces each weight matrix with a single smaller one, reducing the embedding dimension without the need for fine-tuning.", "section": "FFN Blocks", "sec_num": null}, {"text": "We propose to delete rows and columns of weight matrices, which is similar to pruning of filters and channels in the convnet literature. There, sparsity-inducing regularization is added to batch-norm factors (<PERSON> et al., 2017) or network structures (<PERSON> & <PERSON>, 2018) , and the network is trained or fine-tuned, resulting in the pruning of channels or parts of the network. Perhaps the most analogous methods to ours are ThiNet (<PERSON><PERSON> et al., 2017; <PERSON> et al., 2017) , which apply linear operations between layers (as will we), interleaved with more fine-tuning with regularization. In this literature, the model sizes are typically several orders of magnitude smaller than in LLMs, for example the VGG16 network has 138M parameters, comparable with the very smallest OPT model that we consider. The huge size of LLMs makes methods that involve extensive fine-tuning unappealing, especially when outer-loops are needed to select regularization parameters.", "section": "FFN Blocks", "sec_num": null}, {"text": "Recently, some works have been proposed that apply structured pruning to LLMs, followed by continued training (or fine-tuning) to recover the performance that is lost. For example <PERSON><PERSON><PERSON><PERSON>run<PERSON> (<PERSON> et al., 2023a) removes connected structures from an LLM before further training. Contemporarily with our work, <PERSON><PERSON>geon (<PERSON> et al., 2023) interweaves recovery fine-tuning with pruning. We provide results for SliceGPT as a single-shot method and with post-slicing recovery fine-tuning.", "section": "FFN Blocks", "sec_num": null}, {"text": "Our SliceGPT method relies on a computational invariance that is inherent in the transformer architecture. By this, we mean that it is possible to apply an orthogonal transformation to the output of one component, so long as it is undone in the next. Our key insight is that the RMSNorm operation which is performed between blocks of the network does not affect the transformation: the operations commute. In this section, we first describe how the invariance occurs in RMSNorm-connected transformer networks, then we note how networks trained with LayerNorm connections can be converted to RMSNorm. Next, we describe our method to compute transformations at each layer using Principal Component Analysis (PCA), such that the signal between blocks is projected onto its principal components. Finally, we describe how deleting the minor principal components corresponds to slicing away rows or columns of the modified network.", "section": "SLICEGPT", "sec_num": "3"}, {"text": "Let Q denote an orthogonal matrix: we have", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "Q ⊤ Q = QQ ⊤ = I.", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "Note that multiplying a vector x by Q does not change the norm of the vector, since ∥Qx∥", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "= x ⊤ Q ⊤ Qx = √ x ⊤ x = ∥x∥.", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "In this work, the dimensions of Q will always match the embedding dimension of the transformer D.", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "Suppose that X ℓ is the output of one block of the transformer, which is then processed by RMSNorm, and then inputted to the subsequent block as RMSNorm(X ℓ ). If we insert linear layers with the orthogonal matrix Q before RMSNorm and Q ⊤ after RMSNorm, the network remains unchanged, since each row of the signal matrix is multiplied by Q, normalized and multiplied by Q ⊤ . We have", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "RMSNorm(X ℓ Q)Q ⊤ = RMSNorm(X ℓ ) .", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "(2)", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "A proof of this relation appears in Appendix A.1. Now, since each attention or FFN block of the network has a linear operation on both the input and output, we can absorb the additional operations Q into the linear layers of the blocks. Since the network contains residual connections, we must also apply Q to the output of all previous layers (all the way back to the embedding) and to all subsequent layers (all the way up to the LM Head).", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "An invariant function is one for which a transformation to the input does not result in a change to the output. In our case, we can apply any orthogonal transformation Q to the weights of the transformer without changing the result, so the computation can be performed in any transformed state. We refer to this as a computational invariance, and define it in the following theorem. Theorem 1. Let W ℓ in and W ℓ out be the weight matrices of the linear layers of the ℓ-th block of an RMSNorm-connected transformer network, and b ℓ in , b ℓ out be the corresponding biases, if any, and let W embd and W head be the embedding and head matrices. Let Q be an orthogonal matrix of dimension D. Then the following network is equivalent to the original transformer network:", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "EQUATION", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "EQUATION", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": ")", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "The input and head biases are copied: bℓ in = b ℓ in , bhead = b head .", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "Proof. We can show that the transformed network computes the same results as the original by stepping through Algorithm 1. Suppose that on line 1, the original network has computed X, then the modified network has computed X = XQ, using Equation 3. Applying RMSNorm on line 2, we see that the operation of the two networks matches: by Equation 2 we have RMSNorm( X) = RMSNorm(XQ) = RMSNorm(X)Q. Applying the nonlinearity on line 4, we see that X Wℓ in = XW ℓ in , using Equation 4 and it follows that Z = ZQ. On line 5 the residual connection means we have ( X + Z) = (X + Z)Q, and applying RMSNorm results in assignment of X = XQ. This follows through to the end of the loop. Finally, on line 7, the transformations are undone as XW head = X Whead using Equation 7. We use (α) for brevity. The mean-subtraction matrix M is applied to each matrix W out . Layernorm becomes RMSNorm, up to a constant √ D (not shown). Here, the scaling (α ′ ) comes from the previous block.", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "The computational invariance of the transformer network applies only to RMSNorm-connected networks. Before working on those with Layer-Norm, we convert the network to RMSNorm by absorbing the linear blocks of LayerNorm into the adjacent blocks. Figure 3 shows such a transformation on the transformer network (see Figure 2 ) . In each block, we multiply the output matrix W out by the mean-subtraction matrix M, which accounts for the mean subtraction that would happen in the subsequent LayerNorm. The input matrices W in are pre-multiplied by the scales of the preceding LayerNorm blocks. The embedding matrix W embd must be mean-subtracted, and W head must be re-scaled by the last Layer-Norm scales. This is a straightforward change in the order of operations and does not affect the network output.", "section": "COMPUTATIONAL INVARIANCE IN TRANSFORMER NETWORKS", "sec_num": "3.1"}, {"text": "Now that every LayerNorm in the transformer has been converted to RMSNorm, we can select any Q to modify the model. Our initial plan was to collect signals from the model, construct an orthogonal matrix using those signals and to delete parts of the network. We quickly saw that the signals at different blocks of the network were not aligned, and that we would need to apply a different orthogonal matrix at each block, Q ℓ .", "section": "A TRANSFORMATION PER BLOCK", "sec_num": "3.3"}, {"text": "Allowing the orthogonal matrix used in each block to differ can be shown to leave the model unchanged using the same proof as Theorem 1, with the exception of line 5 of Algorithm 1. Here we see that the residual connection and the output of the block must have the same rotation. To fix this, we modify the residual connection by applying the linear transformation Q ⊤ ℓ-1 Q ℓ to the residual. Figure 4 shows how different rotations can be applied to different blocks with the additional linear operation in the residual connection. Unlike the modifications to the weight matrices, these additional operations cannot be pre-computed and add a small (D × D) overhead to the model. Nonetheless, they are needed to allow slicing the model (Section 3.4) and we see real speedup overall (Section 4).", "section": "A TRANSFORMATION PER BLOCK", "sec_num": "3.3"}, {"text": "To compute the matrices Q ℓ , we use PCA. We select a calibration dataset from the training set, run it through the model (after converting LayerNorm operations into RMSNorm), and extract the orthogonal matrix of the layer. We use the output of the transformed network to calculate the orthogonal matrices of the next layers. More precisely, if X ℓ,i is the output of the ℓ th RMSNorm block for the i th sequence in the calibration dataset, we compute", "section": "A TRANSFORMATION PER BLOCK", "sec_num": "3.3"}, {"text": "EQUATION", "section": "A TRANSFORMATION PER BLOCK", "sec_num": "3.3"}, {"text": "and set Q ℓ to the be the eigenvectors of C ℓ , sorted by decreasing eigenvalues.", "section": "A TRANSFORMATION PER BLOCK", "sec_num": "3.3"}, {"text": "Attention RMSNorm FFN Q ⊤ 1 (α ′ )Wk Q ⊤ 1 (α ′ )Wq Q ⊤ 1 (α ′ )Wv WoMQ2 Multi-Head Attention", "section": "A TRANSFORMATION PER BLOCK", "sec_num": "3.3"}, {"text": "Inputs multiplied by Q1 and truncated", "section": "A TRANSFORMATION PER BLOCK", "sec_num": "3.3"}, {"text": "+ x ∥x∥ Q ⊤ 2 (α)W1 Activation Function W2MQ3 + Q ⊤ 1 Q2 Q ⊤ 2 Q3", "section": "A TRANSFORMATION PER BLOCK", "sec_num": "3.3"}, {"text": "Figure 4 : With the network converted to RMSNorm (see Figure 3 ), we apply the computational-invariance idea. The input weight matrices diag(α)W in are pre-multiplied by Q ⊤ . The output matrices W out M are post-multiplied by Q. In the skip-connection, a new linear layer is added Q ⊤ ℓ Q ℓ+1 . After these modifications, the matrices can be sliced (hatched areas).", "section": "A TRANSFORMATION PER BLOCK", "sec_num": "3.3"}, {"text": "The goal of Principal Component Analysis is usually to take a data matrix X and compute a lower dimensional representation Z, and an approximate reconstruction X:", "section": "SLICING", "sec_num": "3.4"}, {"text": "EQUATION", "section": "SLICING", "sec_num": "3.4"}, {"text": ")", "section": "SLICING", "sec_num": "3.4"}, {"text": "where Q is the eigenvectors of X ⊤ X, and D is a D × D small deletion matrix (containing D small columns of the D × D identity matrix), which removes some of the columns of the matrix to the left. The reconstruction is L 2 optimal, in the sense that QD is a linear mapping that minimizes ∥X -X∥ 2 .", "section": "SLICING", "sec_num": "3.4"}, {"text": "When we apply PCA to the signal matrix X between blocks, we never materialize the N × D signal matrix, but we apply the deletion matrix D to the operations preceding and succeeding the construction of that matrix, which have already been multiplied by Q in the above. We delete rows of W in and columns of W out and W embd . We also delete both rows and columns of the matrix Q ⊤ ℓ-1 Q ℓ that we have inserted into the residual connection (see Figure 4 ).", "section": "SLICING", "sec_num": "3.4"}, {"text": "Setup We use Hugging Face Transformers (<PERSON> et al., 2019) to implement our code with PyTorch (<PERSON><PERSON><PERSON> et al., 2019) . The computation of Q is performed on a single H100 GPU with 80GB of memory, taking approximately 3.5 hours to complete for the LLAMA-2 70B model. We use double precision for the PCA calculation because using single precision for eigenvector calculations in PyTorch leads to a discrepancy in the final accuracy, as detailed in Appendix A.2.", "section": "EXPERIMENTAL VALIDATION", "sec_num": "4"}, {"text": "We experiment with two different calibration sets: the WikiText-2 training dataset (<PERSON><PERSON> et al., 2016) and the Alpaca training dataset (<PERSON><PERSON> et al., 2023 ). An ablation study on the calibration set size and sequence length is presented in Appendix A.3. We apply a small amount of RFT to sliced LLAMA-2 and Phi-2 models using LoRA (<PERSON> et al., 2021) , following the idea from <PERSON> et al. (2023a) . For models sliced with WikiText-2 we use approximately 1k sequences, for those sliced with the Alpaca dataset we use 5k. We use LoRA with r = 32, α = 10 and sequence length 1024, and defaults for all other hyperparameters in PEFT (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2022) .", "section": "EXPERIMENTAL VALIDATION", "sec_num": "4"}, {"text": "We evaluate all our experiments on OPT (<PERSON> et al., 2022) , LLAMA-2 (<PERSON><PERSON><PERSON><PERSON> et al., 2023) model families, and additionally evaluate Phi-2 (in our zero-shot task) experiments. We exclude OPT 175B, as it is outperformed by smaller LLAMA-2 models. Nonetheless, we anticipate that this larger model will yield improved results, as larger models typically offer more promising opportunities for compression (see Section 4.1). We evaluate our scheme on both language generation as well as popular zero-shot tasks. To demonstrate the comprehensive speedup achieved by SliceGPT we use: Quadro RTX6000 GPUs with 24GB of memory as a representative example of consumer-level GPUs; 40GB A100s and 80GB H100s to provide datacenter-level benchmarks.", "section": "Models, Tasks, and GPUs", "sec_num": null}, {"text": "Baseline Setup We initially planned to compare our results against a scheme that pruned columns (or rows) with the smallest norm but found that this baseline was very poor, with the WikiText-2 perplexity of the model soaring into the 1000s after pruning just a few columns. Instead, we compare SliceGPT against SparseGPT (Frantar & Alistarh, 2023) employing a 2:4 sparsity ratio, as this is the only sparsity scheme which achieves speedup (<PERSON><PERSON><PERSON> et al., 2021) .", "section": "Models, Tasks, and GPUs", "sec_num": null}, {"text": "Generation Task We begin by showcasing our findings using the WikiText-2 dataset. In this context, we evaluate the performance of both the OPT and LLAMA-2 model families across different sizes when using this dataset for slicing. Table 1 shows the perplexity obtained by various slicing levels. SliceGPT exhibits superior performance when applied to OPT models compared to LLAMA-2 models which matches our intuition from the spectrum analysis of those models (see Appendix A.4 for our discussion). The performance of SliceGPT improves as the model size increases. Comparing SliceGPT with SparseGPT, we see that that SparseGPT 2:4 performs worse than SliceGPT with 25% slicing in all LLAMA-2 models. For OPT, we see that 30% sliced models beat 2:4 sparsity for all model sizes except 2.7B. Zero-shot Tasks We assess SliceGPT across five well-known zero-shot tasks: PIQA (<PERSON>isk et al., 2020) ; WinoGrande (<PERSON><PERSON><PERSON> et al., 2021) ; HellaSwag (Z<PERSON>s et al., 2019) ; ARC-e and ARCc (<PERSON> et al., 2018) using the LM Evaluation Harness (<PERSON> et al., 2021) . Figure 5 shows the results. We see a marked difference between the datasets, with the Alpaca dataset giving much higher performing models. We attribute this difference to the similarity between Alpaca and the benchmark tasks. For LLAMA-2 70B sliced at 30%, with RFT on Alpaca we are able to achieve an average accuracy of 74.3%, compared to 76.6% on the dense model. The sliced model has approximately 51.6B parameters and considerably improved throughput as we demonstrate later. Results for OPT and for all models post-pruning without RFT are shown in Appendix A.5.", "section": "RESULTS", "sec_num": "4.1"}, {"text": "We see that Phi-2 is not able to recover the drop in accuracy from slicing using only the WikiText-2 dataset, but using Alpaca we are able to recover several percentage points. The average accuracy of Phi-2 with 25% slicing and RFT is 65.2%, compared to 72.2% with the dense model. The sliced model has approximately 2.2B parameters and retains 90.3% of the accuracy of the 2.8B model. This shows that even small LMs can benefit from post-training pruning. Tables of accuracies across each task are provided in Appendix A.6. Benchmarking Throughput Unlike conventional sparsity methods, which introduce sparsity in W in and W out , SliceGPT also introduces (structured) sparsity in X: entire columns of X are sliced off, reducing the embedding dimension. This enhances both the computational complexity (in flops) and data movement within our compressed model.", "section": "RESULTS", "sec_num": "4.1"}, {"text": "The token throughput of models sliced at 25% and 50% are compared to the dense model on 80GB H100 GPUs. We set the sequence length to 128 and find the maximum throughput by doubling the batch size until the GPUs run out of memory or the throughput drops off. The 25% sliced models achieve up to 1.55× throughput improvement over the dense model. At 50% slicing the largest models require only one GPU instead of two, with large increases in throughput: 3.13× and 1.87×. This means that for a fixed number of GPUs, these models achieve 6.26× and 3.75× throughput of a dense model. We note that the WikiText2 perplexity of SliceGPT at 50% is worse than SparseGPT 2:4, but the throughput is much higher than could be achieved with a sparse method that does not slice X. For full details see Appendix A.7.", "section": "RESULTS", "sec_num": "4.1"}, {"text": "Inference Time Table 2 compares the time of generating a single token in OPT 66B and LLAMA-2 70B models on Quadro RTX6000 and A100 GPUs. We observe 16-17% speedup on RTX6000 GPUs when employing 25% slicing, and 11-13% on A100s. We reduce the number of GPUs used in both cases, providing energy and cost savings relative to deployment of the dense model. For LLAMA-2 70B, the compute required using RTX6000 GPUs is reduced to 64%, from 1764 GPUms to 1075 GPUms4 . We attribute this improvement to our approach of substituting weight matrices with smaller ones in our compressed models, which is infeasible with other pruning schemes.", "section": "RESULTS", "sec_num": "4.1"}, {"text": "Table 2 : Average per-token inference time of SliceGPT when generating sequences of length 128 (with batch size of 1). In each case, we show the time taken in ms, the number of GPUs required and the total compute in GPUms. End-to-end performance gains are not feasible with the SparseGPT baseline at the time of writing. Instead, we compare SliceGPT with SparseGPT by comparing the relative timing of each operation involved in a transformer layer. We find that SliceGPT (25%) is competitive with SparseGPT (2:4) in terms of speedup and perplexity for large models. For further details see Appendix A.8.", "section": "RESULTS", "sec_num": "4.1"}, {"text": "We've introduced SliceGPT which allows for structured pruning for large language models. We reduce the cost of inference of LLAMA-2 70B on 40GB A100 GPUs to 66% of that of the dense model without any additional code optimization, requiring fewer GPUs (from 4 to 3) while maintaining better held-out perplexity than SparseGPT 2:4. On 24GB RTX6000 GPUs, the cost of inference is reduced to 64%, requiring 2 fewer GPUs (from 7 to 5). On zero-shot downstream tasks, slicing OPT 66B, LLAMA-2 70B and Phi-2 at 25% maintains 99%, 96% and 87% of the dense performance. With recovery fine-tuning 25%-sliced LLAMA-2 70B and Phi-2 increase to 99% and 90% respectively.", "section": "CONCLUSION AND FUTURE WORK", "sec_num": "5"}, {"text": "Opportunities remain to build on our method. Smaller but dense LMs perform better than LMs with 13B parameters or less pruned to similar sizes, though we do not expect this to remain the case for long. Our pruned models have more parameters than those pruned with SparseGPT but fit larger batches in GPU memory with no overhead for sparsity structure: perhaps a combined method could obtain the best of both. Other methods of computing Q could improve the results. To further decrease the inference time and GPU count, complementary methods including quantization (<PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2022) , and structural pruning (e.g. <PERSON> et al., 2023b ) could be used.", "section": "CONCLUSION AND FUTURE WORK", "sec_num": "5"}, {"text": "A.1 PROOF OF EQUATION 2", "section": "A APPENDIX", "sec_num": null}, {"text": "An orthogonal matrix Q is a square matrix that satisfies the relation Q ⊤ Q = QQ ⊤ = I. The norm of a vector is the square-root of the sum of squares of the elements:", "section": "A APPENDIX", "sec_num": null}, {"text": "∥x∥ = i x 2 i = √ x ⊤ x. Multiplying a vector by Q does not change the norm since ∥Qx∥ = x ⊤ Q ⊤ Qx = ∥x∥.", "section": "A APPENDIX", "sec_num": null}, {"text": "The RMSNorm operation divides each row of the input matrix X by its norm. By the basic rules of linear algebra, if x is a row of X, then Q ⊤ x is the corresponding row of XQ. Applying RMSNorm to XQ, said row will now be equal to 1 ∥x∥ Q ⊤ x. After RMSnorm, we can multiply by Q ⊤ , our row is now equal to 1 ∥x∥ QQ ⊤ x = 1 ∥x∥ x. Thus we have the relation", "section": "A APPENDIX", "sec_num": null}, {"text": "EQUATION", "section": "A APPENDIX", "sec_num": null}, {"text": "A.2 SINGLE PRECISION EIGENVALUE CALCULATION As previously noted in Section 4, we employ double precision when performing the PCA algorithm. This choice is made in order to mitigate potential numerical errors that may arise during the computation of the orthogonal matrix in SliceGPT. Nevertheless, it is intriguing to investigate the impact of employing lower precision for PCA calculations on the ultimate accuracy.", "section": "A APPENDIX", "sec_num": null}, {"text": "Table 3 shows the perplexity of all our models when we apply FP32 PCA in our scheme. It shows that the accuracy of larger models could be affected by numerical errors during the PCA calculation phase. It should be noted that we use PyTorch (torch.linalg) for calculating the eigenvectors and eigenvalues. 20% 34.12 16.51 13.87 11.64 10.73 9.94 9.80 7.30 6.07 5.82 SliceGPT 25% 38.25 17.67 14.78 12.14 11.08 10.15 9.81 8.52 6.65 7.01 SliceGPT 30% 44.17 19.33 16.20 12.82 11.53 10.43 9.99 10.41 7.49 8.75 ", "section": "A APPENDIX", "sec_num": null}, {"text": "We present an ablation study to examine the role of the WikiText-2 calibration set. We focus on the generation task with 25% sparsity using OPT 6.7B and LLAMA-2 7B models. Next we explore the effect of using different sequence lengths N in the calibration set. Given a fixed number of B samples, the PCA input matrix is computed using N B embedding vectors, and understanding the tradeoff between having a larger B or a larger N is interesting. Figure 6 (right) shows the results of varying the sequence length in the calibration set from 128 to 4096: we conclude that having a larger sequence length can result in better perplexity.", "section": "A.3 SENSITIVITY TO THE CALIBRATION SET SIZE AND SEQUENCE LENGTH", "sec_num": null}, {"text": "Using these insights, we use a calibration set size of 1024 and sequence length 2048 in our main experiments (Table 1 ). In Table 4 below we evaluate the perplexity of OPT and LLAMA-2 models on WikiText-2 with a smaller calibration set size, which confirms the trend that decreasing this degrades the perplexity across all models and sizes. The figure below shows the eigenvalue distribution for the OPT 6.7B and LLAMA-2 7B models. Although both models have a comparable parameter count, the LLAMA-2 model has a more tightly compressed distribution in its embeddings spectrum. This observation shows that there are no dominant principal components with significantly more information, making the pruning of these components a more challenging task. OPT (6.7B) 0 2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 Layer Num.", "section": "A.3 SENSITIVITY TO THE CALIBRATION SET SIZE AND SEQUENCE LENGTH", "sec_num": null}, {"text": "LLAMA-2 (7B) Except for the first layer in the LLAMA-2 model, the eigenvalue distributions for both models show faster decay in early layers compared to later ones. This suggests that a greater amount of slicing could be applied after the orthogonal transformation in these early layers.", "section": "A.3 SENSITIVITY TO THE CALIBRATION SET SIZE AND SEQUENCE LENGTH", "sec_num": null}, {"text": "We can use the above insights to slice different layers by different amounts. Instead of specifying the slicing level upfront, we set the fraction of the total variance to discard during each PCA calculation, which sets the number of rows and columns to slice off from each matrix. For each model, we run three experiments with varying target variances to obtain a total reduction on the network close to 25%.", "section": "A.3 SENSITIVITY TO THE CALIBRATION SET SIZE AND SEQUENCE LENGTH", "sec_num": null}, {"text": "The results are shown in Table 5 below. Varying the slicing level by layer improves the WikiText-2 perplexity in OPT models, but has the opposite effect in LLAMA-2 models. A.5 ZERO-SHOT ACCURACY ABLATION OVER CALIBRATION DATASET Figure 8 shows the average scores achieved by the sliced models across the zero-shot tasks. The top row of the plot shows the mean accuracy when WikiText-2 is used for calibration, and the bottom row shows the accuracy when Alpaca is used for calibration. We observe a similar pattern to the generation task in the results: the OPT models are more amenable to compression than the LLAMA-2 models, and the reduction in accuracy is less pronounced in the larger models. Here we also include the Phi-2 model: we see that sliced versions of the Phi-2 model are comparable with sliced versions of the LLAMA-2 7B model. The largest OPT and LLAMA-2 models can be compressed very effectively, with just a few percentage points loss when removing 30% of the 66B OPT model. Recovery fine-tuning (RFT) can be applied LLAMA-2 and Phi-2 models to improve their performance further (Figure 5 in main text). Despite an extensive search, we were not able to find RFT parameters that enabled improved performance in the OPT models.", "section": "A.3 SENSITIVITY TO THE CALIBRATION SET SIZE AND SEQUENCE LENGTH", "sec_num": null}, {"text": "Table 12 : Results of timing the matrix multiplications involved in each layer of OPT models. For larger models, SliceGPT (25%) gives slightly better speedup than SparseGPT 2:4, and with better WikiText-2 perplexity. For smaller models SparseGPT 2:4 provides better speedup albeit at worse perplexity. Slicing at 50% trades off perplexity for even greater speedups. A.9 RECOVERY FINE-TUNING COST All LLAMA-2 , OPT and Phi-2 models can be sliced on a single GPU in 1 to 3 hours. With recovery fine-tuning we compress all LMs in 1 to 5 hours total, as shown in Table 13 .", "section": "A.3 SENSITIVITY TO THE CALIBRATION SET SIZE AND SEQUENCE LENGTH", "sec_num": null}, {"text": "Table 13 : Compute cost of slicing 30% with SliceGPT and performing recovery fine-tuning using the Alpaca dataset. Here we use a calibration set size of 1024 for LLAMA-2 models and 2048 for Phi-2 , and calibration sequence length 2048 in all cases.", "section": "A.3 SENSITIVITY TO THE CALIBRATION SET SIZE AND SEQUENCE LENGTH", "sec_num": null}, {"text": "Model SliceGPT 30% Recovery fine-tuning Total Time GPUs Time GPUs LLAMA-2 7B 0h44m 1xH100 80GB 0h23m 1xH100 80GB 1h07m LLAMA-2 13B 1h08m 1xH100 80GB 0h44m 1xH100 80GB 1h52m LLAMA-2 70B 3h31m 1xH100 80GB 1h35m 4xH100 80GB 5h06m", "section": "A.3 SENSITIVITY TO THE CALIBRATION SET SIZE AND SEQUENCE LENGTH", "sec_num": null}, {"text": "Phi-2 0h49m 1xV100 32GB 1h59m 1xV100 32GB 2h48m", "section": "A.3 SENSITIVITY TO THE CALIBRATION SET SIZE AND SEQUENCE LENGTH", "sec_num": null}, {"text": "The signal matrix is sometimes referred as activation matrix.", "section": "", "sec_num": null}, {"text": "In some implementations an RMSNorm block may contain scale parameters. We consider these to be special instances of LayerNorm and handle them accordingly.", "section": "", "sec_num": null}, {"text": "For ease of notation here and throughout this paper, we abuse notation slightly and omit the broadcasting of the bias terms across the sequence length dimension. The complete notation for the operation of an attention block is σ(XWin + 1N b ⊤ in )Wout + 1N b ⊤ out .", "section": "", "sec_num": null}, {"text": "Our Hugging Face-based testing does not enjoy continuous batching or model sharding. This means that in terms of inference time, the dense-model could be improved more than our sliced model in terms of GPUms. Nonetheless, our measurements do reflect the energy-usage per token in such a deployment.", "section": "", "sec_num": null}], "back_matter": [{"text": "We thank <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON> for their invaluable contributions to the source code. We additionally thank <PERSON><PERSON><PERSON> for her helpful feedback when reviewing early versions of the paper.", "section": "ACKNOWLEDGEMENTS", "sec_num": null}, {"text": "In this section, we provide the detailed results of the zero-shot tasks we presented in the paper. We use the CuSparseLT 0.5 library to run sparse matrix multiplications on an 80 GB A100 GPU, replicating the size of the matrix-matrix multiplications in three different-sized LLAMA-2 models. We used PyTorch to run similar matrix multiplications for the dense equivalent, and for SliceGPT (which is also straightforward dense matmul, but smaller). We chose a sequence length of 2048, and took the matrix sizes from the HuggingFace config files. We took the median runtime over 10 3 attempts.Each LLAMA-2 layer requires a gated FFN with one up projection, one down projection, and a gated projection. In attention, the architecture of the model means that the query matrix multiplication is a different size to the key and value matrix multiplications. The following table shows the time taken in ms to run each matrix multiplication in the model, plus a \"total\" time and a relative speedup. We also benchmarked the OPT architecture in the same way. In this case, the matrix multiplications associated with Key, Value, Query and Out are all the same size, and there are just two matrix multiplications in the MLP section (FC1 and FC2).", "section": "A.6 DETAILED ZERO-SHOT RESULTS", "sec_num": null}], "ref_entries": {"FIGREF0": {"uris": null, "fig_num": "3", "type_str": "figure", "text": "Figure 3: Converting a transformer network from LayerNorm to RMSNorm: the scale matrix diag(α) is absorbed into the subsequent matrix W in . Figure shows the block in combined colors.We use (α) for brevity. The mean-subtraction matrix M is applied to each matrix W out . Layernorm becomes RMSNorm, up to a constant", "num": null}, "FIGREF1": {"uris": null, "fig_num": "5", "type_str": "figure", "text": "Figure 5: Mean zero-shot accuracy on LLAMA-2 and Phi-2 across multiple tasks after slicing and recovery fine-tuning (RFT). Left: WikiText-2 used for calibration and RFT. Right: Alpaca used for calibration and RFT.", "num": null}, "FIGREF2": {"uris": null, "fig_num": null, "type_str": "figure", "text": "Quadro RTX6000Dense 237ms on 6 GPUs 1422 GPUms 252ms on 7 GPUs 1764 GPUms (24GB) 25% 204ms on 5 GPUs 1020 GPUms 215ms on 5 GPUs 1075 GPUms", "num": null}, "FIGREF3": {"uris": null, "fig_num": "6", "type_str": "figure", "text": "Figure 6: The effect of the calibration set size and sequence length on perplexity of WikiText2.", "num": null}, "FIGREF4": {"uris": null, "fig_num": "6", "type_str": "figure", "text": "Figure 6 (left) shows the result of varying the size of the calibration set on the perplexity. It shows that sample sizes of at least 128 provide sensible choices for our calibration set.", "num": null}, "FIGREF5": {"uris": null, "fig_num": null, "type_str": "figure", "text": "the MLP input.", "num": null}, "FIGREF6": {"uris": null, "fig_num": "7", "type_str": "figure", "text": "Figure 7: Normalized (by maximum) spectrum of the MLP inputs (log scale) using 64 samples.Except for the first layer in the LLAMA-2 model, the eigenvalue distributions for both models show faster decay in early layers compared to later ones. This suggests that a greater amount of slicing could be applied after the orthogonal transformation in these early layers.", "num": null}, "FIGREF7": {"uris": null, "fig_num": "8", "type_str": "figure", "text": "Figure 8: Mean zero-shot accuracy on OPT, LLAMA-2 and Phi-2 across multiple tasks after slicing with the WikiText-2 (top) and Alpaca (bottom) datasets for calibration.", "num": null}, "TABREF0": {"content": "<table><tr><td>W1</td><td/><td>Activation Function</td><td>W2</td><td>FFN</td></tr><tr><td/><td>M</td><td>x ∥x∥</td><td>diag(α)</td><td>LayerNorm</td></tr><tr><td/><td/><td>+</td><td/><td/></tr><tr><td/><td/><td/><td/><td>Attention</td></tr><tr><td>Wk</td><td>Wq</td><td>Wv</td><td>Multi-Head Attention</td><td>Wo</td></tr><tr><td/><td/><td>Inputs</td><td/><td/></tr></table>", "type_str": "table", "html": null, "text": "Attention BlocksThe attention block has four matrices: W k , W q , W v and W o , each of dimension D × D. The input signal arriving into the block is projected into the Key (XW k ), Query (XW q ), and Value (XW v ) matrices, which are then split into multiple heads. A nonlinear operation is applied at each head before the signals are combined and multiplied by the output weight matrix W o . Since the first three weight matrices are applied separately to the inputs, we can concatenate them and perform a single matrix multiplication (denoted by the white box around these matrices in Figure", "num": null}, "TABREF2": {"content": "<table><tr><td>Method</td><td>OPT 125M 1.3B 2.7B 6.7B</td><td>13B</td><td>30B</td><td>66B</td><td>LLAMA-2 7B 13B 70B</td></tr><tr><td>Dense</td><td colspan=\"3\">27.64 14.61 12.46 10.85 10.12 9.56</td><td colspan=\"2\">9.33 5.47 4.88 3.32</td></tr><tr><td>SparseGPT 2:4</td><td colspan=\"5\">45.07 29.61 14.90 13.00 11.80 10.53 10.22 8.69 7.07 4.98</td></tr><tr><td colspan=\"4\">SliceGPT (10%) 29.34 15.10 12.75 10.92 10.27 9.65</td><td colspan=\"2\">9.43 5.89 5.21 3.69</td></tr><tr><td colspan=\"4\">SliceGPT (20%) 34.26 16.43 13.73 11.48 10.66 9.87</td><td colspan=\"2\">9.57 6.64 5.81 4.25</td></tr><tr><td colspan=\"6\">SliceGPT (25%) 37.74 17.46 14.56 11.90 10.94 10.04 9.68 7.24 6.30 4.60</td></tr><tr><td colspan=\"6\">SliceGPT (30%) 43.98 19.09 15.83 12.51 11.33 10.27 9.85 8.12 6.99 5.05</td></tr></table>", "type_str": "table", "html": null, "text": "OPT and LLAMA-2 perplexity results on WikiText2. The calibration set size and sequence length are 1024 and 2048, respectively.", "num": null}, "TABREF3": {"content": "<table><tr><td>Method</td><td>OPT 125M 1.3B 2.7B 6.7B</td><td>13B</td><td>30B</td><td>66B</td><td>7B</td><td>LLAMA-2 13B 70B</td></tr><tr><td>Dense</td><td colspan=\"3\">27.64 14.61 12.46 10.85 10.12 9.56</td><td>9.33</td><td colspan=\"2\">5.47 4.88 3.32</td></tr><tr><td colspan=\"7\">SparseGPT 2:4 45.07 29.61 14.90 13.00 11.80 10.53 10.22 8.69 7.07 4.98</td></tr><tr><td colspan=\"4\">SliceGPT 10% 29.48 15.15 12.83 11.05 10.28 9.68</td><td>9.45</td><td colspan=\"2\">6.51 5.64 4.20</td></tr><tr><td>SliceGPT</td><td/><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "html": null, "text": "OPT and LLAMA-2 perplexity results on WikiText2 using FP32 PCA calculation. The calibration set size and sequence length are 128 and 2048, respectively.", "num": null}, "TABREF4": {"content": "<table><tr><td>Method</td><td>OPT 125M 1.3B 2.7B 6.7B</td><td>13B</td><td>30B</td><td>66B</td><td>LLAMA-2 7B 13B 70B</td></tr><tr><td>Dense</td><td colspan=\"3\">27.64 14.61 12.46 10.85 10.12 9.56</td><td colspan=\"2\">9.33 5.47 4.88 3.32</td></tr><tr><td>SparseGPT 2:4</td><td colspan=\"5\">45.07 29.61 14.90 13.00 11.80 10.53 10.22 8.69 7.07 4.98</td></tr><tr><td colspan=\"4\">SliceGPT (10%) 29.33 15.15 12.82 11.00 10.30 9.66</td><td colspan=\"2\">9.43 5.96 5.29 3.78</td></tr><tr><td colspan=\"4\">SliceGPT (20%) 34.53 16.58 13.89 11.62 10.75 9.91</td><td colspan=\"2\">9.61 6.86 6.04 4.46</td></tr><tr><td colspan=\"6\">SliceGPT (25%) 38.13 17.78 14.84 12.12 11.08 10.10 9.76 7.56 6.61 4.89</td></tr><tr><td colspan=\"6\">SliceGPT (30%) 44.61 19.61 16.30 12.81 11.55 10.32 9.95 8.64 7.44 5.42</td></tr><tr><td colspan=\"4\">A.4 SPECTRUM ANALYSIS OF LLAMA-2 AND OPT MODELS</td><td/><td/></tr></table>", "type_str": "table", "html": null, "text": "OPT and LLAMA-2 perplexity results on WikiText2. The calibration set size and sequence length are 128 and 2048, respectively.", "num": null}, "TABREF5": {"content": "<table><tr><td>Model</td><td colspan=\"2\">WikiText-2 PPL (25% constant slicing) (varying slicing by layer) WikiText-2 PPL</td><td>Improvement</td></tr><tr><td>OPT 6.7B</td><td>12.10</td><td>11.94, 24.7% total slicing</td><td>0.16</td></tr><tr><td>OPT 13B</td><td>11.04</td><td>10.76, 24.2% total slicing</td><td>0.28</td></tr><tr><td>OPT 30B</td><td>10.13</td><td>9.95, 24.8% total slicing</td><td>0.18</td></tr><tr><td>OPT 66B</td><td>9.75</td><td>9.63, 24.1% total slicing</td><td>0.12</td></tr><tr><td>LLAMA-2 7B</td><td>6.84</td><td>7.63, 24.1% total slicing</td><td>-0.79</td></tr><tr><td>LLAMA-2 13B</td><td>6.00</td><td>6.17, 23.3% total slicing</td><td>-0.17</td></tr><tr><td>LLAMA-2 70B</td><td>4.44</td><td>4.63, 25.5% total slicing</td><td>-0.19</td></tr></table>", "type_str": "table", "html": null, "text": "Evaluating the effects of varying slicing level by layer. The calibration set size is 128 and the sequence length is the maximum for each model.", "num": null}}}}