{"paper_id": "hidden-context", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:03:06.904085Z"}, "title": "DISTRIBUTIONAL PREFERENCE LEARNING: UNDERSTANDING AND ACCOUNTING FOR H<PERSON><PERSON><PERSON> CONTEXT IN RLHF", "authors": [{"first": "<PERSON>", "middle": [], "last": "Sit<PERSON><PERSON><PERSON><PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "Laidlaw", "suffix": "", "affiliation": {}, "email": "cassidy<PERSON><EMAIL>"}, {"first": "<PERSON>", "middle": [], "last": "Hadfield-Menell", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "In practice, preference learning from human feedback depends on incomplete data with hidden context. Hidden context refers to data that affects the feedback received, but which is not represented in the data used to train a preference model. This captures common issues of data collection, such as having human annotators with varied preferences, cognitive processes that result in seemingly irrational behavior, and combining data labeled according to different criteria. We prove that standard applications of preference learning, including reinforcement learning from human feedback (RLHF), implicitly aggregate over hidden contexts according to a well-known voting rule called Borda count. We show this can produce counter-intuitive results that are very different from other methods which implicitly aggregate via expected utility. Furthermore, our analysis formalizes the way that preference learning from users with diverse values tacitly implements a social choice function. A key implication of this result is that annotators have an incentive to misreport their preferences in order to influence the learned model, leading to vulnerabilities in the deployment of RLHF. As a step towards mitigating these problems, we introduce a class of methods called distributional preference learning (DPL). DPL methods estimate a distribution of possible score values for each alternative in order to better account for hidden context. Experimental results indicate that applying DPL to RLHF for LLM chatbots identifies hidden context in the data and significantly reduces subsequent jailbreak vulnerability. Our code and data are available at https://github.com/cassidylaidlaw/hidden-context.", "pdf_parse": {"paper_id": "hidden-context", "_pdf_hash": "", "abstract": [{"text": "In practice, preference learning from human feedback depends on incomplete data with hidden context. Hidden context refers to data that affects the feedback received, but which is not represented in the data used to train a preference model. This captures common issues of data collection, such as having human annotators with varied preferences, cognitive processes that result in seemingly irrational behavior, and combining data labeled according to different criteria. We prove that standard applications of preference learning, including reinforcement learning from human feedback (RLHF), implicitly aggregate over hidden contexts according to a well-known voting rule called Borda count. We show this can produce counter-intuitive results that are very different from other methods which implicitly aggregate via expected utility. Furthermore, our analysis formalizes the way that preference learning from users with diverse values tacitly implements a social choice function. A key implication of this result is that annotators have an incentive to misreport their preferences in order to influence the learned model, leading to vulnerabilities in the deployment of RLHF. As a step towards mitigating these problems, we introduce a class of methods called distributional preference learning (DPL). DPL methods estimate a distribution of possible score values for each alternative in order to better account for hidden context. Experimental results indicate that applying DPL to RLHF for LLM chatbots identifies hidden context in the data and significantly reduces subsequent jailbreak vulnerability. Our code and data are available at https://github.com/cassidylaidlaw/hidden-context.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Encoding human preferences and values into interactive learning systems is an essential component for making those systems safe and socially beneficial. To accomplish this, modern machine learning models, such as large language model (LLM) chatbots like ChatGPT and Claude, are trained with feedback from human evaluators. This method, often called reinforcement learning from human feedback (RLHF), seeks to align system behavior with the preferences of annotators. In this paper, we study how RLHF infers preferences when there is hidden context that influences human evaluations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Hidden context is any information that affects preference annotations but is not given as input to the learned utility or reward model. It can arise through several mechanisms. For instance, when feedback is collected from many different people, annotator identity is hidden context: it affects the annotations, since different annotators could have very different preferences, but it is not input to the reward model, since the annotators' data is combined anonymously. Other sources of hidden context include human irrationality and evaluation according to multiple objectives.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "To motivate the consequences of naive preference learning with hidden context, consider the following hypothetical scenario:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Alternative, e.g., chatbot response.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "u(a, z)", "sec_num": null}, {"text": "Hidden context, e.g., annotator identity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "u(a, z)", "sec_num": null}, {"text": "Preference learning û(a)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "u(a, z)", "sec_num": null}, {"text": "Annotator identity in a population with diverse preferences.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "AI", "sec_num": null}, {"text": "Annotation instructions in a dataset collected with multiple prompts.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Internal mental states of a person behaving irrationally.", "sec_num": null}, {"text": "Figure 1 : We analyze the effects of hidden context on preference learning, which is one of the key steps in reinforcement learning from human feedback (RLHF). Hidden context is any information that affects the annotator's assessment of the utility of different alternatives, but is not input to the learned utility or reward model. Our framework emcompasses many potential issues with preference learning, including human irrationality, diverse preferences among annotators, and combining multiple objectives (Section 2). We prove that preference learning implicitly aggregates over hidden context using a rule called Borda count (Section 3).", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "1", "ref_id": null}], "eq_spans": [], "section": "Sources of hidden context", "sec_num": null}, {"text": "Example 1.1. A company has developed an AI assistant to help high school students navigate college admissions. They implement RLHF by asking their customers for feedback on how helpful the chatbot's responses are. Among other questions, this process asks users whether or not they prefer to see information about the Pell Grant, an aid program for low-income students. Because the population of customers is biased towards high-income students, most feedback indicates that users prefer other content to content about the Pell Grant. As a result, RLHF trains the chatbot to provide less of this kind of information. This marginally improves outcomes for the majority of users, but drastically impacts lower-income students, who rely on these recommendations to understand how they can afford college.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sources of hidden context", "sec_num": null}, {"text": "The heart of this issue is that common preference learning approaches assume that all relevant features are provided as input to the reward model. However, when there is hidden context-which is almost always the case-this assumption is false. As a result, standard methods can have unexpected and undesirable consequences. In Example 1.1, relevant context about the annotator's identity (i.e. their income level) is missing from the data. The implicit aggregation over preferences biases the outcome in favor of high-income applicants. In this work, we take steps to better understand the implications of unobserved context in preference learning and consider technical approaches to identify when such situations occur.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sources of hidden context", "sec_num": null}, {"text": "In Section 2 we present a formal model of preference learning with hidden context. We show that our model can represent many challenges in preference learning, such as combining data from different users, accounting for irrationality, and optimizing for multiple objectives. Since these challenges are ubiquitous, understanding their implications is crucial for safely deploying RLHF-trained models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sources of hidden context", "sec_num": null}, {"text": "In Section 3, we use our model to develop theoretical results on the consequences of hidden context in preference learning. First, we provide a precise characterization of the utility function that preference learning will output when there is hidden context. In particular, we show that preference learning implicitly aggregates over hidden context using a rule called the Borda count. We explore the implications of this finding, identifying cases when Borda account aggregates preferences in unintuitive ways quite different from other methods like regression. Furthermore, when data is combined from many annotators, we establish connections with the social choice literature to expose another problem arising from hidden context: annotators may have an incentive to misreport their preferences to influence the learned reward function.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sources of hidden context", "sec_num": null}, {"text": "Next, we consider the design of preference learning methods that more gracefully account for hidden context. In Section 4, we propose distributional preference learning (DPL). DPL estimates a distribution over utility values for each input instead of a single real-valued output. This allows the method to detect situations where unobserved context could influence preferences. We show how DPL can detect the effects of missing features through an explained variance (r 2 ) metric.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sources of hidden context", "sec_num": null}, {"text": "We validate DPL in two ways. First, we conduct a small-scale synthetic experiment with a 1dimensional space of alternatives that allows us to directly compare to Borda count. Next, we apply DPL to a real-world dataset of preferences for use in RLHF. In this case, the preference data is collected according to two distinct objectives. In one subset of the data, raters were asked to prefer helpful and honest responses. In the other subset, raters were asked to prefer responses that did not respond to harmful requests. This introduces hidden context because the single reward model is trained on the combined data. We find that DPL is able to identify this hidden context automatically and identifies the uncertainty when these competing goals are at odds.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sources of hidden context", "sec_num": null}, {"text": "Beyond identifying potential instances of relevant hidden context, our experiments indicate that DPL can be used to develop guardrails that protect against jailbreaks. <PERSON> et al. (2023) showed that many jailbreaks succeed by pitting the helpfulness and harmlessness objectives of chatbots against one another. This means that some jailbreaks can be understood as a consequence of hidden context. As a result, it is possible to detect this class of jailbreaks by leveraging the distribution of utilities we get from DPL. In particular, risk-aversion with respect to the distribution of learned utilities can dramatically reduce the rate at which the preference model prefers jailbroken responses.", "cite_spans": [{"start": 168, "end": 185, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "Sources of hidden context", "sec_num": null}, {"text": "We summarize our contributions as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sources of hidden context", "sec_num": null}, {"text": "1. we identify and formally characterize the problem of preference learning with hidden context, and describe a number of settings where it may arise; 2. we show that preference learning with hidden context implicitly implements Borda count, which can have counter-intuitive implications and incentives for annotators to misreport preferences; 3. we introduce distributional preference learning and show that it can detect and mitigate some effects of hidden context in LLM-based preference models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sources of hidden context", "sec_num": null}, {"text": "We begin by formally describing the problem of preference learning with hidden context. Consider a finite set of alternatives A, and an unknown utility function u : A → R. For instance, in the case of a chatbot, the alternatives could be the possible responses to a prompt, and the utility function would describe how much a particular response is preferred. To estimate u, we observe the outcome of comparisons between pairs of alternatives (a, b). We assume there is a fixed probability for any pair of alternatives (a, b) that a will be preferred to b; we denote this probability p u (a, b) and assume that p u (a, b) + p u (b, a) = 1; that is, the order in which the alternatives are presented does not matter. In the ideal case, comparison outcomes would exactly reflect the utility function, i.e., p u (a, b) = 1{u(a) > u(b)}. Realistically, however, preference comparison data never exactly follows a single utility function. To account for the fact that people are noisy and/or inconsistent in their feedback, a common assumption is that instead preference comparisons are made according to a Bradley-Terry-Luce (BTL) model (Rajkumar & Agarwal, 2014) , also sometimes known as Boltzmann-rational model (<PERSON><PERSON> et al., 2020) :", "cite_spans": [{"start": 1132, "end": 1158, "text": "(<PERSON><PERSON> & <PERSON>, 2014)", "ref_id": "BIBREF41"}, {"start": 1210, "end": 1229, "text": "(<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF27"}], "ref_spans": [], "eq_spans": [], "section": "SETTING AND RELATED WORK", "sec_num": "2"}, {"text": "p BTL u (a, b) = e u (a)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SETTING AND RELATED WORK", "sec_num": "2"}, {"text": "e u (a)+e u (b) . In this model, the higher u(a) is compared to u(b), the more likely the outcome of the comparison is to prefer a to b; as the utilities for a and b are closer, the comparison outcome moves towards uniformly random. The most commonly used method for estimating the utility function u from preference data is to fit the maximum likelihood estimator (MLE) under the BTL model. To derive the MLE, we consider the limit of infinite data and assume that preference comparisons are elicited for uniformly randomly selected pairs of alternatives. The MLE for the utility function û is given by û = arg min û L(û; u), where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SETTING AND RELATED WORK", "sec_num": "2"}, {"text": "L(û; u) = 1 |A|(|A|-1) a̸ =b -p u (a, b) log e û(a) e û(a) +e û(b) -(1 -p u (a, b)) log e û(b) e û(a) +e û(b) . (1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SETTING AND RELATED WORK", "sec_num": "2"}, {"text": "Although in practice û might be represented by a neural network, we assume for theoretical purposes that L(û; u) is optimized over all possible û : A → R. In some cases, L may not have any minimum, so we consider a regularized version of (1); see Equation ( 6) and Appendix A.1 for more details.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "SETTING AND RELATED WORK", "sec_num": "2"}, {"text": "While preference learning based on (1) has been widely deployed and enjoyed some success, it rests on assumptions that often do not hold in practice. In particular, irrationality, partial observability, and diversity of preferences among a population all challenge the BTL model on which the usual preference learning loss is based. We argue that all of these cases can be understood as special cases of a general phenomenon: hidden context. For concreteness, consider again Example 1.1. The key problem in the example is a mismatch between the information that influences the user's feedback and the information that the preference learning algorithm uses to estimate utilities based on that feedback. The user gives feedback that depends on their financial situation, while the learned utility model observes request-response pairs. Thus, the preference learning algorithm must produce a single ordering over alternatives that implicitly aggregating feedback over the hidden context of whether the user is high-or low-income.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "To model hidden context in preference learning, we extend the preference learning formalization to utility functions u : A × Z → R over a space of observed features a ∈ A and hidden context z ∈ Z. Let D z be a distribution over Z. In Example 1.1, z ∈ {0, 1} could represent whether the user is lowor high-income; then perhaps z ∼ B(0.8) if 80% of users are high-income (where B(p) represents a <PERSON><PERSON><PERSON> random variable with mean p). Given u(a, z) and D z , we can calculate the probability that one alternative a is chosen over another b given that z is hidden:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p u,Dz (a, b) = E z∼Dz [O u (a, b, z)] where O u (a, b, z) = 1/2 if u(a, z) = u(b, z) 1{u(a, z) > u(b, z)} o.w. (", "eq_num": "2"}], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "p u,Dz marginalizes over the distribution of the hidden context z and thus reflects the comparison data available to the preference learning algorithm. Our model of hidden contexts can represent many settings where preference learning is difficult:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "Partial observability.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "There may be variables that are observable by the human making preference comparisons but not by the AI system, which learns from that data. For instance, suppose annotators' preferences depend on the day of the week or the month of the year, but the estimated utility function ignores the date the comparisons were made.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "Multiple objectives. System designers may combine data about user preferences over multiple, different objectives. For instance, the Anthropic HH-RLHF dataset (Bai et al., 2022a ) contains one subset with comparisons of chatbot responses based on harmlessness and another subset with comparisons based on helpfulness. When these subsets are combined, the objective that was used to make the comparison (in this case, either harmlessness or helpfulness) is a hidden context.", "cite_spans": [{"start": 159, "end": 177, "text": "(<PERSON> et al., 2022a", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "Population with diverse preferences. Preference learning is almost always applied to data aggregated from many annotators who may have very different utility functions (e.g., <PERSON> et al. (2022a) observe high intra-annotator disagreement). If z represents the annotator who makes a comparison, then u(•, z) could represent the utility function for that annotator. However, when the data is used to train a single utility function û(•), then the annotator's identity z is a hidden context.", "cite_spans": [{"start": 175, "end": 193, "text": "<PERSON> et al. (2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "Irrational and noisy decisions. Various types of irrationality could be modeled as unseen latent variables that affect a person's decision-making. For instance, to represent a person making noisy utility estimates, one could let Z = R |A| , z(a)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "iid ∼ N (0, 1), and u(a, z) = µ(a) + z(a) for some µ : A → R. That is, the person has an underlying utility µ(a) for each alternative but makes comparisons based on that utility plus independently sampled Gaussian noise representing irrationality in their utility assessments. This is equivalent to the Thurstone-Mosteller model of noisy decision making (<PERSON>, 2001) .", "cite_spans": [{"start": 354, "end": 369, "text": "(<PERSON><PERSON>, 2001)", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "Due to the ubiquity of these settings, preference learning is nearly always performed with hidden context. This means that the learned utility function û(a), which only depends on the seen features a, must somehow aggregate over the hidden contexts z. We aim to understand and mitigate the consequences of this ubiquitous challenge. (2021) . As part of RLHF, preference learning has been widely used recently for training large language models (LLM) to give outputs according to human preferences (<PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2021; <PERSON> et al., 2022a; b; <PERSON><PERSON><PERSON> et al., 2022) . It has also been extensively analyzed in theory; some results focus on its sample complexity in various settings (<PERSON>, 2015; <PERSON> et al., 2015; <PERSON>, 2018; <PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2021) or other directions such as the statistical identifiability of preferences (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2023) , the computational efficiency of preference learning (<PERSON><PERSON><PERSON> & <PERSON>, 2015) , Bayesian preference learning (<PERSON> & <PERSON>, 2010) , or the combination of preference learning and reinforcement learning (<PERSON> et al., 2023) . However, to our knowledge, no prior work has specifically analyzed the behavior of preference learning with hidden context.", "cite_spans": [{"start": 333, "end": 339, "text": "(2021)", "ref_id": null}, {"start": 497, "end": 519, "text": "(<PERSON><PERSON> et al., 2020;", "ref_id": null}, {"start": 520, "end": 542, "text": "<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF46"}, {"start": 543, "end": 563, "text": "<PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF1"}, {"start": 564, "end": 582, "text": "<PERSON> et al., 2022a;", "ref_id": null}, {"start": 583, "end": 585, "text": "b;", "ref_id": null}, {"start": 586, "end": 606, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": null}, {"start": 722, "end": 740, "text": "(<PERSON>, 2015;", "ref_id": "BIBREF11"}, {"start": 741, "end": 759, "text": "<PERSON> et al., 2015;", "ref_id": "BIBREF44"}, {"start": 760, "end": 784, "text": "<PERSON> & Wainwright, 2018;", "ref_id": "BIBREF43"}, {"start": 785, "end": 805, "text": "<PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF24"}, {"start": 806, "end": 829, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF25"}, {"start": 830, "end": 852, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF9"}, {"start": 928, "end": 947, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF50"}, {"start": 948, "end": 968, "text": "<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF45"}, {"start": 1023, "end": 1053, "text": "(<PERSON><PERSON><PERSON> & Grossglauser, 2015)", "ref_id": "BIBREF36"}, {"start": 1085, "end": 1107, "text": "(Caron & Do<PERSON>t, 2010)", "ref_id": "BIBREF8"}, {"start": 1179, "end": 1197, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF51"}], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "The challenges of preference learning that we group as cases of \"hidden context\" have also been studied individually. There has been some work on explicitly modeling annotator disagreement (<PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2023) as well as other approaches to learning from annotators with diverse preferences (<PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON>, 2023; <PERSON> et al., 2023) . Other work has studied the effects of human irrationality or non-BTL models of human behavior on preference learning (<PERSON><PERSON> et al., 2020; <PERSON> et al., 2021; Laidlaw & Russell, 2021; <PERSON> et al., 2022; Laidlaw & Dragan, 2022) , which under our framework can be modeled as hidden context. <PERSON><PERSON> & <PERSON>-<PERSON> (2020) and <PERSON> et al. (2023) study the optimization of multiple objectives learned from human preferences. Finally, related to our connections with social choice theory in Section 3, some previous work has associated preference or reward learning with concepts in economics, such as voting rules (Con<PERSON> & <PERSON>, 2005) , incentive compatibility (Echenique & Prasad, 2019) , and mechanism design (<PERSON> et al., 2020) .", "cite_spans": [{"start": 189, "end": 211, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF22"}, {"start": 212, "end": 233, "text": "<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF4"}, {"start": 315, "end": 333, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF28"}, {"start": 334, "end": 356, "text": "<PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF17"}, {"start": 357, "end": 370, "text": "<PERSON><PERSON><PERSON>, 2023;", "ref_id": "BIBREF37"}, {"start": 371, "end": 389, "text": "<PERSON> et al., 2023)", "ref_id": null}, {"start": 509, "end": 528, "text": "(<PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF5"}, {"start": 529, "end": 546, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF33"}, {"start": 547, "end": 571, "text": "Laidlaw & Russell, 2021;", "ref_id": "BIBREF32"}, {"start": 572, "end": 590, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF30"}, {"start": 591, "end": 614, "text": "Laidlaw & Dragan, 2022)", "ref_id": "BIBREF31"}, {"start": 677, "end": 708, "text": "Zhuang & Hadfield-Menell (2020)", "ref_id": null}, {"start": 713, "end": 730, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF14"}, {"start": 998, "end": 1025, "text": "(Conitzer & Sandholm, 2005)", "ref_id": "BIBREF13"}, {"start": 1052, "end": 1078, "text": "(Echen<PERSON> & Prasad, 2019)", "ref_id": "BIBREF18"}, {"start": 1102, "end": 1126, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF20"}], "ref_spans": [], "eq_spans": [], "section": "HIDDEN CONTEXT", "sec_num": "2.1"}, {"text": "We begin our analysis by precisely describing the behavior of preference learning with hidden context. In particular, we can show that a utility function û(a) learned with the BTL loss as in ( 6) implicitly aggregates utilities over the hidden contexts z using a rule called Borda count. We define the Borda count BC(a) of an alternative a as BC(a) = 1 |A| b∈A p u,Dz (a, b). That is, the Borda count is the average probability that the alternative is preferred to other alternatives. If an alternative is almost always preferred to all other alternatives, then its Borda count will be close to 1; if it is almost always dispreferred, the Borda count will be near 0. We use the term Borda count as a reference to the well-known voting rule of the same name-a connection we expand on in Section 3.2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "THEORETICAL ANALYSIS", "sec_num": "3"}, {"text": "Theorem 3.1. BTL preference learning implicitly aggregates hidden context according to Borda count. That is, if û is optimized according to ( 6", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "THEORETICAL ANALYSIS", "sec_num": "3"}, {"text": "), then ∀a, b ∈ A, û(a) > û(b) ⇔ BC(a) > BC(b).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "THEORETICAL ANALYSIS", "sec_num": "3"}, {"text": "We defer all proofs to Appendix A. According to Theorem 3.1, the learned utility function and Borda count differ by only a monotonic transformation. If we use reinforcement learning or another optimization technique to search for the alternative a which maximizes û(a)-as one does in RLHF-then the optimal alternative will the same as that which maximizes the Borda count BC(a). Similar results that relate preference learning and Borda count were previously explored by <PERSON> (2014), although they do not consider the setting of hidden context. While Theorem 3.1 precisely describes the results of preference learning with hidden context, its implications are unclear. Is Borda count a useful way of aggregating over hidden contexts in practice, and how does it compare to other aggregation rules? To answer this question, we give multiple perspectives on preference learning with hidden context using the result of Theorem 3.1. First, we compare preference learning to least-squares regression with hidden context. Then, we analyze learning from a population with diverse preferences through the lens of social choice theory.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "THEORETICAL ANALYSIS", "sec_num": "3"}, {"text": "One desirable property of preference learning with hidden context would be if it converged to the expected utility for each alternative when marginalizing over hidden context, which we denote by ū(a) = E z∼Dz [u(a, z)]. For instance, one can show that least-squares utility regression converges to the expected utility when there is hidden context; see Appendix A.2 for a formal statement and proof. The fact for least-squares utility regression û = ū shows that, in some sense, it gracefully degrades in the presence of hidden context. Although there are drawbacks of expected utility, it is a wellunderstood method of aggregating utilities over hidden contexts that has desirable decision-theoretic properties. Thus, it would be helpful if the utility function û(a) learned by preference learning with hidden context were equivalent to the expected utility ū(a). In this section, we characterize when the output of preference learning with hidden context is equivalent to that of utility regression.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "COMPARISON TO EXPECTED UTILITY AND LEAST-SQUARES REGRESSION", "sec_num": "3.1"}, {"text": "In some cases, we can show that preference learning does identify a utility function that is equivalent to the expected utility. The result requires that the zero-mean \"noise\" induced by hidden context is identical across alternatives and reasonably distributed. We represent this noise as ϵ(a) = u(a, z) -ū(a) (where z ∼ D z ) to be the random variable representing the residual utility of an alternative a after subtracting its expected utility. Theorem 3.2. Let ϵ(a) be independent and identically distributed for all a ∈ A. Many noise distributions, such as uniform and normal distributions, clearly satisfy the assumptions of Theorem 3.2. Thus, as long as the noise caused by hidden context does not vary across alternatives and is not too unusual, we generally expect that preference learning will give a utility function with the same ordering over alternatives as the expected utility. This means that it performs similarly to least-squares regression.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Positive results", "sec_num": null}, {"text": "In other cases, preference learning can behave quite differently from utility regression. Example 1.1 describes such a case. The expected utility of telling students about Pell Grants is higher than the expected utility of not telling them, since it is of great benefit to low-income students and only small inconvience to high-income students. However, the Borda count is lower since the high-income majority prefer not to hear about the grants.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Negative results", "sec_num": null}, {"text": "One might suppose that preference learning and regression disagree in this case because the majority of users prefer the alternative with lower expected utility, and preference learning gives a learned utility function which assigns higher utilities to alternatives preferred to by the majority of users. As long as the majority of feedback agrees with the ordering given by the expected utility, will preference learning and regression give the same result? The following theorem shows that this is not the case. That is, Proposition 3.3 describes a case where for any two alternatives, the majority of feedback chooses the alternative with the higher expected utility, and yet preference learning still does not produce a utility function equivalent to the expected utility. In general, it is impossible to always identify ū (even up to a monotonic transformation) given only comparison data. Theorem 3.4 (Unidentifiability of ū). Suppose a preference learning algorithm takes as input unlimited samples of the form (a, b, O u (a, b, z)) for all values of a and b, where z ∼ D z , and deterministically outputs a learned utility function û(a). Then there is some utility function u and distribution over unseen features D z such that û is not equivalent to ū.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Negative results", "sec_num": null}, {"text": "When training on comparison data from many agents, each with their own preferences, preference learning aggregates all their feedback into a single utility function. As we described in Section 2, this is a case where the identity of the annotator is hidden context: it affects the comparison outcomes but is unseen by the preference learning algorithm. Social choice theory studies methods for aggregating preferences from a population. Thus, it can provide a lens through which to understand this particular case of preference learning with hidden contexts.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONNECTIONS TO SOCIAL CHOICE THEORY", "sec_num": "3.2"}, {"text": "In a large dataset of preference comparisons from many annotators, individual comparisons can be thought of as \"votes\" for one alternative over another. When preference learning combines this data into a single utility function, it is similar to a voting rule that ranks candidates based on annotators' votes. In particular, Borda count is a well-studied voting rule-usual definitions of Borda count in voting theory differ from ours only by an affine transformation (<PERSON>, 2005; <PERSON>, 2013; <PERSON><PERSON>, 2012) . This means that many results from the social choice literature on Borda count can be applied to understanding preference learning from a diverse population. For example, under Borda count, participants may have an incentive to misreport their preferences (<PERSON><PERSON><PERSON>, 1998) . Figure 3 : The results of our experiments with synthetic data. We find that the utility estimated by normal preference learning agrees closely with the Borda count, as our theory suggests. Furthermore, DPL successfully identify alternatives where hidden context has a significant effect.", "cite_spans": [{"start": 467, "end": 482, "text": "(<PERSON>, 2005;", "ref_id": "BIBREF29"}, {"start": 483, "end": 497, "text": "<PERSON>, 2013;", "ref_id": "BIBREF19"}, {"start": 498, "end": 512, "text": "<PERSON><PERSON><PERSON>, 2012)", "ref_id": "BIBREF34"}, {"start": 770, "end": 785, "text": "(<PERSON><PERSON><PERSON>, 1998)", "ref_id": "BIBREF16"}], "ref_spans": [{"start": 795, "end": 796, "text": "3", "ref_id": null}], "eq_spans": [], "section": "CONNECTIONS TO SOCIAL CHOICE THEORY", "sec_num": "3.2"}, {"text": "Through the social choice lens, a natural question arises: can voting rules other than Borda count be implemented in preference learning by changing the estimation procedure? We explore this question further in Appendix B.3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONNECTIONS TO SOCIAL CHOICE THEORY", "sec_num": "3.2"}, {"text": "Our theoretical results show that preference learning in the presence of hidden context can lead to undesirable outcomes. While system designers may still choose to use preference learning for RLHF or other applications, they should carefully consider these downsides and try to mitigate them. The first step towards this is detection-knowing to what degree hidden context affects preference data both on a dataset and instance level. In this section, we describe a simple modification to preference learning such that it can detect and characterize inconsistent feedback.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISTRIBUTIONAL PREFERENCE LEARNING", "sec_num": "4"}, {"text": "Our alternative preference learning methods, which we call distributional preference learning (DPL), output a distribution over possible utilities for each alternative rather than a single value (Figure 2 ). In particular, we learn a mapping D : A → ∆(R) from alternatives to distributions over utilities to estimate the distribution of u(a, z) when z ∼ D z . We consider two variants, each of which parameterizes the distribution D(a) in a different way.", "cite_spans": [], "ref_spans": [{"start": 203, "end": 204, "text": "2", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "DISTRIBUTIONAL PREFERENCE LEARNING", "sec_num": "4"}, {"text": "First, the mean-and-variance model learns two functions μ : A → R and σ : A → [0, ∞), parameterizing the distribution over utilities as D(a) = N μ(a), σ(a) 2 . Second, in the categorical model, we choose n evenly spaced utility values u 1 < u 2 < . . . < u n , and then parameterize the distribution as the probabilities of each of those utilities p(u i | a) for i ∈ {1, . . . , n}. We train the distributional preference models by maximizing the likelihood of the data given the model", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISTRIBUTIONAL PREFERENCE LEARNING", "sec_num": "4"}, {"text": "p D (a, b) = E [O(u a , u b ) | u a ∼ D(a), u b ∼ D(b)].", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISTRIBUTIONAL PREFERENCE LEARNING", "sec_num": "4"}, {"text": "Concretely, for the mean-and-variance model, the loss for a single preference comparison where alternative a is preferred to b is the negative log probability that u a -u b > 0 :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISTRIBUTIONAL PREFERENCE LEARNING", "sec_num": "4"}, {"text": "-log Φ μ(a)-μ(b) √ σ(a) 2 +σ(b) 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISTRIBUTIONAL PREFERENCE LEARNING", "sec_num": "4"}, {"text": "For the categorical model, the equivalent loss is", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISTRIBUTIONAL PREFERENCE LEARNING", "sec_num": "4"}, {"text": "-log n i=1 n j=1 p(u i | a)p(u j | b) 1/2 u i = u j 1{u i > u j } o.w.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISTRIBUTIONAL PREFERENCE LEARNING", "sec_num": "4"}, {"text": "Note that DPL is not trying to model uncertainty about the utility function which comes from limited data, but rather uncertainty which comes from hidden context. Even in the limit of infinite data, DPL will not necessarily converge to a point estimate of utility for each alternative.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISTRIBUTIONAL PREFERENCE LEARNING", "sec_num": "4"}, {"text": "Since DPL methods give more information than a single utility estimate at each alternative, they can detect the effects of missing features both at the dataset and instance level. At the dataset level, a popular metric for determining the effects of missing features in regression is the coefficient of determination, r 2 . We can derive an equivalent measure for DPL. Let ), where a is sampled from the uniform distribution over alternatives. Intuitively, r 2 , which has to be between 0 and 1, represents the amount of variation in utility values that is captured by the observed features a; 1 -r 2 is the proportion of variance caused by hidden context. At the instance level, alternatives a where Var( D(a)) is higher are likely those where missing features have a larger impact on the utility of the alternative.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISTRIBUTIONAL PREFERENCE LEARNING", "sec_num": "4"}, {"text": "To test distributional preference learning, we ran experiments in a simple setting of preference learning with hidden context. We let A = [0, 1] and z ∼ B(1/2). We suppose that the true utility function is u(a, z) = a if a < 0.8 and u(a, z) = 2az otherwise. That is, the missing variable z has no effect when a < 0.8, but for a ≥ 0.8, u(a, z) is either 2a or zero, each with probability one-half. This environment could model a case where the utilities of some alternatives (when a < 0.8) are easy for users to judge, while others (when a ≥ 0.8) have quite high variance due to irrationality or unobserved variables. We estimate utility functions both with normal preference learning and DPL; Figure 3 shows the results. The left plot shows that the learned utilities closely agree with Borda count and diverge from the expected utility ū, as our theory in Section 3 suggests.", "cite_spans": [], "ref_spans": [{"start": 700, "end": 701, "text": "3", "ref_id": null}], "eq_spans": [], "section": "Synthetic experiments", "sec_num": null}, {"text": "The right plots show that DPL accurately outputs high-variance distributions when a > 0.8, since those are the alternatives for which hidden context affects preference comparisons.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Synthetic experiments", "sec_num": null}, {"text": "Using DPL While our experiments show that DPL can detect the effects of hidden context in preference data, how should this additional information be used? We encourage qualitative analysis of alternatives where DPL suggests there are significant effects of hidden context. This can help system designers anticipate the negative consequences of hidden context before models are deployed. Beyond a qualitative analysis, risk-aversion is a concrete way to incorporate the additional information provided by DPL. Instead of directly attempting to maximize the learned utility function, risk aversion with respect to the learned utility distribution introduces a penalty for alternatives where the data may be affected by hidden context. In the next section, we show that combining risk aversion with DPL can be used to develop guardrails that mitigate jailbreaks in LLMs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Synthetic experiments", "sec_num": null}, {"text": "In this section, we evaluate DPL's ability to identify hidden context through a case study on large language model (LLM)-based reward models. Chatbots like GPT-4 and <PERSON> are trained by learning a human reward model and then optimizing it via reinforcement learning, together referred to as RLHF. In order to evaluate the ability of DPL methods to identify hidden context, we use the HH-RLHF dataset (<PERSON> et al., 2022a) . For this dataset, raters were separately asked to provide preferences on whether responses were helpful or harmful. When a single utility function is trained on the entire HH-RLHF dataset, the objective (helpfulness or harmlessness) that was used to annotate a pair of responses is a hidden context since it is not available to the learned utility function. This missing variable may cause real harm: <PERSON> et al. (2023) present jailbreaks that manipulate models to prioritize helpfulness over harmlessness and output harmful content. Through our case study, we aim to answer three questions:", "cite_spans": [{"start": 402, "end": 421, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 825, "end": 842, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "CASE STUDY: COMPETING OBJECTIVES IN RLHF", "sec_num": "5"}, {"text": "1. Does the hidden context of the labeling objective contribute to jailbreak vulnerability? 2. Can we DPL detect the effects of this hidden context without explicit supervision? 3. Can we DPL reduce models' susceptibility to jailbreaks?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CASE STUDY: COMPETING OBJECTIVES IN RLHF", "sec_num": "5"}, {"text": "Understanding jailbreak vulnerability To address the first question, we train three LLM-based utility functions on the HH-RLHF dataset (<PERSON> et al., 2022a) . The dataset consists of conversations between a human and an AI assistant with two alternatives for the assistant's final response, plus a label for which response is preferred. Half of the comparisons are labeled based on which response is more helpful and honest and half based on which response is more harmless. Using standard preference learning, we train utility functions ûhelpful on just the helpful-labeled data, ûharmless on just the harmless-labeled data, and ûcombined on both (see Appendix C for experiment details).", "cite_spans": [{"start": 135, "end": 154, "text": "(<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "CASE STUDY: COMPETING OBJECTIVES IN RLHF", "sec_num": "5"}, {"text": "To test if implementing RLHF using these utility functions would lead to jailbreak vulnerabilities, we collect pairs of responses to jailbreak prompts from <PERSON> et al. (2023) that are designed to fool the model into giving a harmful response; each pair consists of one safe response and one jailbroken response. If a learned utility function assigns higher utility to the jailbroken response, then we expect using that utility function to train an LLM assistant via RLHF would lead to the assistant outputting the jailbroken response. We define the \"jailbreak rate\" of a utility function as the percentage of jailbreak prompts for which it assigns higher utility to the jailbroken response. Since avoiding jailbreaks is not the only purpose of an LLM assistant, we also evaluate the \"helpfulness accuracy\" of a utility function as its accuracy at predicting judgements in the HH-RLHF helpfulness test set.", "cite_spans": [{"start": 156, "end": 173, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "CASE STUDY: COMPETING OBJECTIVES IN RLHF", "sec_num": "5"}, {"text": "The top of Table 1a shows the jailbreak rates and helpfulness accuracies for each of the three normally-trained utility functions. While ûharmless , trained only on harmlessness-annotated data, has a very low jailbreak rate of under 4%, its helpfulness accuracy of around 50% suggests it is useless for judging the helpfulness of responses to non-harmful prompts. ûhelpful has much higher helpfulness accuracy, but also prefers jailbroken responses more than half the time. The problem is that the jailbroken responses are generally more \"helpful\" than a safe response which refuses to answer the prompt. Since our theory suggests that ûcombined is aggregating the helpful and harmful utilities via Borda count, in many cases the high helpfulness of jailbroken responses leads to high utilities under the combined utility function. In fact, ûcombined has a jailbreak rate of around 25%, showing that one cause of jailbreaks is training a single reward model on data which combines two competing objectives-a clear case of hidden context in preference learning.", "cite_spans": [], "ref_spans": [{"start": 17, "end": 19, "text": "1a", "ref_id": "TABREF0"}], "eq_spans": [], "section": "CASE STUDY: COMPETING OBJECTIVES IN RLHF", "sec_num": "5"}, {"text": "To answer the next question-whether we can detect hidden contextwe additionally train DPL models on all three datasets and measure their r 2 values, which are shown in Table 1b . Recall that lower r 2 indicates more effects from hidden context. We find that among the mean-and-variance DPL models, those trained on either just the helpfuless or just the harmlessness data have r 2 above 0.75, while the DPL model trained on the combined data has a much lower r 2 = 0.53. We see the same pattern with categorical DPL models: r 2 = (0.63, 0.53) for the singleobjective models while r 2 = 0.41 for the combined model. This indicates that DPL can consistently measure the effect of hidden context via the r 2 metric: for both variants of DPL, r 2 is considerably lower when hidden context is present.", "cite_spans": [], "ref_spans": [{"start": 174, "end": 176, "text": "1b", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Detecting hidden context", "sec_num": null}, {"text": "Preventing jailbreaks How might the distributional output of DPL be leveraged within RLHF to guard against jailbreaks? Ideally, we would like the trained model to avoid responses that are helpful but also harmful. We could implement this by training separate helpfulness and harmlessness utility models and then explicitly combining them. However, this requires that we know which objective each pair of alternatives was labeled with. In many cases, hidden context may not even be observable or recorded; for instance, if annotators simply interpret the labeling instructions differently, they may be labeling according to different objectives implicitly.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Detecting hidden context", "sec_num": null}, {"text": "DPL methods allow the reward model to account for hidden context without the need for that context to be recorded. In particular, we can avoid helpful-but-harmful responses by optimizing a lower quantile of the distribution D output by DPL. Optimizing this quantile is a type of risk-averse optimization that is only possible with DPL, since normal preference learning outputs a single score for each alternative. The bottom of Figure 1a shows that using the 0.01-quantile of DPL models (rows labeled \"risk-averse\") can mitigate jailbreaks without harming the models' accuracy otherwise. For instance, the lower quantile of the categorical DPL model trained on the combined data has a jailbreak rate of 13%, compared to 25% for ûcombined . The models have similar helpfulness accuracy, indicating that risk-averse optimization does not hurt DPL's performance on non-harmful prompts. Figure 4 illustrates an example where risk-averse optimization prevents a jailbreak response.", "cite_spans": [], "ref_spans": [{"start": 435, "end": 437, "text": "1a", "ref_id": null}, {"start": 890, "end": 891, "text": "4", "ref_id": null}], "eq_spans": [], "section": "Detecting hidden context", "sec_num": null}, {"text": "Preference learning is becoming an essential component of real-world AI systems that helps align outcomes with the values of users. However, in the ubiquitous case of hidden context-arising from diverse preferences, competing objectives, irrationality, and other types of partial observabilitypreference learning may have unexpected or unwanted consequences. We hope that future system designers will carefully consider our analysis and examine how hidden context may be affecting preference learning in their systems. Furthermore, we encourage practitioners to consider using distribution preference learning as an alternative method that can explicitly account for hidden context. Proof. According to Proposition A.1, (6) must be strongly convex if λ > 0 and thus there is a unique minimum of the loss function satisfying the first-order condition. Furthermore, if λ = 0, which corresponds to an un-regularized objective, then if there is a solution it must also satisfy the first-order condition. The first-order condition can be written as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "6"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L(û; u) ∂ û(a) = λû(a) + c̸ =a σ(û(a) -û(c)) -p u,Dz (a, c) = 0 ∀a ∈ A.", "eq_num": "(7)"}], "section": "CONCLUSION", "sec_num": "6"}, {"text": "Here, σ(x) = exp x 1+exp x is the logistic sigmoid function. Note that we want to show the following: BC(a) > BC(b) ⇐⇒ û(a) > û(b) where û is the optimal solution to (6).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "6"}, {"text": "First consider the forward direction. Let a, b ∈ A such that BC(a) > BC(b), and assume by way of contradiction that û(a) ≤ û(b). Let f, g : R → R be defined as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "6"}, {"text": "f (α) = λα + c̸ =a σ(α -û(c)) -p u,Dz (a, c) g(α) = λα + c̸ =b σ(α -û(c)) -p u,Dz (b, c) .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "6"}, {"text": "Thus f (û(a)) = g(û(b)) = 0 by the first-order condition in (7). Observe that f and g are increasing functions in α. Now note the following: Corollary B.3. If there is a solution to preference learning, then it is equivalent to BC. Furthermore, the solution to L 2 -regularized preference learning is also equivalent to BC.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "6"}, {"text": "g(α) -f (α) = σ(α -û(a)) -σ(α -û(b)) + c̸ =a", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "6"}, {"text": "Proof. Observe that as per Theorem 3.1, the feature over which the expectation is taken with respect to is the identifier i for each agent. Since agents are uniformly sampled, this is a scaling of Borda count.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "6"}, {"text": "In this section we consider what SWFs can be represented when the distribution of comparisons are known. We call such SWFs proportion-representable if they can be directly determined by a classifier, ie", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "ρ[⪰](a, b) = E [O u (a, b, i)] = 1 |I| |{i ∈ I : a ≻ i b}|", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "a ≻ i b ⇐⇒ O u (a, b, i) > 1 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "In the context of preference learning via maximum likelihood estimation, this is a useful property of a SWF as it can be directly implemented by optimizing a cross-entropy loss on the comparisons. We formally define this property as follows: This suggests that it might be possible to separate the learning of preferences in aggregate with the normative properties of the SWF implemented. It is not obvious what is an ideal SWF to implement, and thus having the flexibility to change implementations without relearning the utility function is useful. A general property that allows an SWF to be proportion-representable is the following: Definition B.5. A SWF is comparison-anonymous if swapping the some comparisons of two individuals (still maintaining a rational preference) doesn't change the outcome.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Definition B.4. F is proportion-representable if ∃g such that ∀ ⪰, a, b ∈ A, aF (⪰)b ⇐⇒ ag[ρ[⪰ ]]b.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Observe that this is a stronger property than regular anonymity. We now state a simple result on the equivalence between proportion-representability and comparison-anonymity: Proposition B.6. An SWF is proportion-representable iff it is comparison-anonymous.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Proof. The forward direction is clear, hence we only prove the backward direction. Assume F is comparison-anonymous, and for contradiction, assume it is not proportion-representable. Then for some ⪰̸ =⪰ ′ with the same proportion ∃x, y such that xF (⪰)y but yF P (⪰ ′ )x. This is a contradiction as by comparison-anonymity we can swap preferences in one profile to become the other profile, but the social preference doesn't change.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Since learning a classifier directly is the most general setup for learning from comparisons, this provides a fundamental limit on what SWFs can be implemented. Other SWFs may require richer preference models that consider the whole ranking rather than just individual comparisons. We now consider specific examples of SWFs from the voting theory literature, showing a mix of positive and negative results.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Scoring rules A scoring rule is determined by α(k), the score of the k-th ranking of the alternative that is non-decreasing in k:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "u(a) = i α(|{b : a ≻ i b}|)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "For example, Borda count has α(k) = k. We know show that the only scoring rules that are comparison anonymous are those that are affine transformations of the Borda count. Proposition B.7. A scoring rule is comparison-anonymous iff it is an affine scoring rule.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Proof. For the backward direction, observe that by linearity of α, the associated utility function is an affine transformation of Borda count. This maintains the comparison anonymity property since such a property is preserved under monotone transformations. Now we consider the forward direction. If α is a scoring rule that is not affine, then the following condition must hold for some", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "1 ≤ k ≤ |A| since |A| ≥ 3: α(k + 1) -α(k) ̸ = α(k + 2) -α(k + 1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "First consider the case where α(k + 1) -α(k) < α(k + 2) -α(k + 1). Without loss of generality, consider the two agent case. Assume the preference ranking for both agents are identical apart from their rankings at {k, k + 1, k + 2}. Let them have the following rankings respectively for some alternative a, b, c:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "b ≻ a ≻ c c ≻ a ≻ b", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Thus the utilities of each alternative are as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "u(a) = 2α(k + 1) u(b) = α(k) + α(k + 2) u(c) = α(k) + α(k + 2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "By assumption, we have that u(b) > u(a). Now consider the proportion-preserving transformation of the preference profile:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "a ≻ b ≻ c c ≻ b ≻ a", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "where all other rankings are kept the same. Hence the utilities of each alternative are:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "u(a) = α(k) + α(k + 2) u(b) = 2α(k + 1) u(c) = α(k) + α(k + 2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Thus u(a) > u(b). This holds similarly for the case where α(k + 1) -α(k) > α(k + 2) -α(k + 1). Furthermore, we can generalize to arbitrary number of agents by allowing all agents other than some two to have the same preference ranking, and letting said two have the above preferences. As the SWF is linear in the agents, the relative ranking between alternatives only depend on the two agents, preserving our result. Since the ranking of the SWF induced by α is not preserved when considering an alternative preference profile with the same proportions of comparisons, it cannot be comparison-anonymous.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Corollary B.8. <PERSON>rda count is the only proportion-representable SWF (up to monotone transformations) that is induced by a scoring rule.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Proof. This follows by linearity of the scoring rule.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Copeland Rule and Maximin rules The Copeland and maximin rules are given by the following", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "C Cope<PERSON> (a) = c M (a, c) -M (c, a), <PERSON> <PERSON><PERSON> (a) = min c̸ =a M (a, c)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "These rules can be seen to be proportion-representable by using the same result for pairwisemajority: Proposition B.9. The Copeland and maximum rules are a proportion-representable SWF.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "specific. Be very careful not to reward the AI for telling you something false! Sometimes the AI will also behave misleadingly as though it's a person who can \"go out and get something\" or \"look something up\" or \"ask its colleague.\" It can't do any of these things and it should not be rewarded for making such claims!", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "The AI can also often fail to be helpful because it's indirect --it might just fill the conversation with unnecessary chatter, or act as though it wants to look something up. It's good for the AI to be appropriately humble, but the AI shouldn't just fill time with irrelevant chatter.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Finally, the AI should always be polite and friendy.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "Consider the following conversation between an AI assistant and a human: give your final answer by writing \"A\" or \"B\" on a new line.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.3 PROPORTION-REP<PERSON>SENT<PERSON><PERSON> SWFS", "sec_num": null}, {"text": "To train our preference models, we fine-tune LLAMA-2-7B (<PERSON><PERSON><PERSON><PERSON> et al., 2023) using LoRA (<PERSON> et al., 2021) . We replace the normal language model head of the LLAMA models with a linear layer with either 1 output (normal preference learning), 2 outputs (mean-and-variance DPL), or 10 outputs (categorical DPL). We use the AdamW optimizer (Loshchilov & Hutter, 2019) with a learning rate of 3 × 10 -6 which is decayed via a cosine schedule to 3 × 10 -7 , a batch size of 2 comparisons (i.e., 4 responses total), and weight decay of 0.0001. Preference models trained on just the harmlessness or helpfulness subsets of the data are trained for 2 epochs, while preference models trained on the combined data are trained for 1 epoch; this ensures all models are trained for roughly the same number of gradient steps. We implement training using PyTorch (<PERSON><PERSON><PERSON> et al., 2019) and HuggingFace Transformers (<PERSON> et al., 2020) .", "cite_spans": [{"start": 56, "end": 78, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF47"}, {"start": 90, "end": 107, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF26"}, {"start": 338, "end": 365, "text": "(<PERSON><PERSON><PERSON><PERSON> & Hutter, 2019)", "ref_id": "BIBREF35"}, {"start": 848, "end": 869, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF40"}, {"start": 899, "end": 918, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF49"}], "ref_spans": [], "eq_spans": [], "section": "C.2 MODEL TRAINING", "sec_num": null}, {"text": "Mean-and-variance DPL As mentioned above, for the mean-and-variance variant of distributional preference learning (DPL) we use a neural network which takes in a prompt-response pair a and has two outputs f 1 (a) and f 2 (a). We parameterize the output distribution as D(a) = N (μ(a), σ(a) 2 ), where μ(a) = f 1 (a) and σ(a) = log (1 + exp f 2 (a)). We apply the softplus to the second output to obtain the output standard variance so as to ensure it is positive.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 MODEL TRAINING", "sec_num": null}, {"text": "Categorical DPL For the categorical variant of DPL, we use a neural network which takes in a prompt-response pair a and has n = 10 outputs f 1 (a), . . . , f n (a). We parameterize the output distribution as That is, the probabilities placed on n evenly spaced point masses between 0 and 1 are given by a taking the softmax of the neural network outputs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 MODEL TRAINING", "sec_num": null}, {"text": "To stabilize training, we found it was useful to add a small entropy bonus to the training loss. That is, we add to the DPL loss a term ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C.2 MODEL TRAINING", "sec_num": null}, {"text": "To collect the dataset of jailbroken responses, we started with the dataset of all ChatGPT and <PERSON> responses to jailbreak prompts from <PERSON> et al. (2023) , which contains labels for each response indicating if the model was a \"good bot\" or \"bad bot.\" We filtered to prompts that produced a \"good bot\" response from one model and \"bad bot\" response from the other, giving us 187 pairs of responses.", "cite_spans": [{"start": 138, "end": 155, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF48"}], "ref_spans": [], "eq_spans": [], "section": "C.3 JAILBROKEN RESPONSES", "sec_num": null}], "back_matter": [{"text": "We thank <PERSON><PERSON><PERSON> and <PERSON> for feedback on drafts. <PERSON> was supported by an Open Philanthropy AI Fellowship. <PERSON> was supported by an AI2050 Early Career Fellowship from Schmidt Sciences.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACKNOWLEDGMENTS", "sec_num": null}, {"text": "A.1 PROOF THAT L(û; u) IS CONVEX Proposition A.1. The loss function L(û; u) is strictly convex as a function of the values of û(a) for all a ∈ A. Furthermore, if λ > 0, then L(û; u) + λ 2 a∈A û(a) 2 is strongly convex.Proof. Note that L(û; u) is a sum of many functions of the form -log e û(a) e û(a) + e û (b) (3) weighted by nonnegative coefficients, for various values of a, b ∈ A. Thus, we only need to show that functions of the form (3) are convex and then the entire loss function must be convex as well.To see why ( 3) is convex, we can multiply the top and bottom of the fraction by e -u(a) to obtain -log 1 1 + e û(b)-û (a) .(4)Note that the second derivative of the functione x (1 + e x ) 2 > 0, which means f (x) is strictly convex. Thus implies that (4) must be a strictly convex function of û since letting x = û(b) -û(a), x is an affine transformation of û and strict convexity is preserved under affine transformations.Finally, when λ > 0, λ 2 a∈A û(a) 2 is clearly a strongly convex function of û(a) for a ∈ A. Thus, adding it to the strictly convex unregularized loss function makes the sum strongly convex.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "APPENDIX A PROOFS AND ADDITIONAL THEORETICAL RESULTS", "sec_num": null}, {"text": "Proposition A.2. Suppose that û is estimated via least-squares utility regression:Then for all a ∈ A,Proof. We can rewrite the optimization objective in (5) asNote that since for any a, û(a) only appears in one term in the sum, we can define û pointwise asIt is clear that the above is minimized when Observe the following for the last two terms in (8):where (i) follows from the assumption that F b,a (δ) > F b,a (0) = 1 2 . Now note the following for each term of the summation in (8):Here, (i) follows from the fact that ū(a) > ū(b), and so ū(b) + ϵ(b) > ū(c) + ϵ(c) implies ū(a) + ϵ(b) > ū(c)+ϵ(c), meaning that the probability of the latter event must be at least that of the former. (ii) follows from the fact that the distributions of ϵ(a) and ϵ(b) are identical.Combining the above with (8) shows that BC(a) -BC(b) > 0, i.e., BC(a) > BC(b); this completes the proof.A.5 PROOF OF PROPOSITION 3.3 A.6 PROOF OF THEOREM 3.4 Theorem 3.4 (Unidentifiability of ū). Suppose a preference learning algorithm takes as input unlimited samples of the form (a, b, O u (a, b, z)) for all values of a and b, where z ∼ D z , and deterministically outputs a learned utility function û(a). Then there is some utility function u and distribution over unseen features D z such that û is not equivalent to ū.Proof. Consider an alternative space A = {a, b} and hidden context z ∈ Z = {0, 1} with D z = B(1/2). Now, define two utility functions over these alternatives:Note that ū(a) = 0 < ū(b) = 1, while ū′ (a) = 0 > ū(b) = -1. Now, these utility functions result in the following distribution over comparison outcomes:That is, both (u, ϵ) and (u ′ , ϵ ′ ) result in identical distributions over comparison outcomes. Thus, the preference learning algorithm must output identical learned utility functions in either scenario; call its output û. If û(a) ≥ û(b), then it has failed to identify ū, since ū(a) < ū(b). On the other hand, if û(a) < û(b), then it has failed to identify ū′ , since ū(a) > ū(b). Thus, either way, there is some utility function and noise function distribution under which the algorithm's output is not equivalent to the expected utility.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 PROOF THAT LEAST-SQUARES REGRESSION CONVERGES TO EXPECTED UTILITY", "sec_num": null}, {"text": "To analyze preference learning through the lens of social choice theory, we first define the concept of a social welfare functional. Let I be the number of agents, and let P ⊂ R ⊂ B = A × A be the set of strict rational 1 , rational 2 and binary relations (respectively) on the space of alternatives A. We say ⪰= (⪰ i ) I i=1 ∈ R I is a preference profile. Viewing an individual's feedback as their revealed preference, which is available in a sufficiently rich dataset of comparisons, we can see preference learning as being similar to a social welfare functional: Definition B.1. A social welfare functional (SWF) is a map F : K → B where K ⊆ R I is the domain of preference profiles.We will assume that K = R I . Proof. Observe that they can be rewritten as such:These results showcase how there is some flexibility in how we choose to implement preference learning when aggregating across individuals.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B RESULTS ON SOCIAL CHOICE THEORY B.1 PRELIMINARIES", "sec_num": null}, {"text": "In this appendix, we describe the details of our LLM preference learning experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C EXPERIMENT DETAILS", "sec_num": null}, {"text": "We initially used the original labels from the HH-RLHF dataset to train preference models. However, we found that the distribution of prompts was quite different between the helpfulness and harmfulness splits of the dataset. In the helpfulness split, most prompts were harmless questions or requests for assistance. In contrast, in the harmlessness split, most prompts were specifically chosen to elicit harmful behavior. Preference models trained on the combined data were therefore able to identify the type of prompt and respond accordingly: they responded to harmful prompts based on harmfulness and harmless prompts based on helpfulness.To emphasize the effect of hidden context in this setting, we decided to randomly relabel half of the dataset with the opposite objective. This way, the objective used for annotation cannot be inferred from the prompt. To relabel the dataset in this way, we used GPT-3.5; <PERSON><PERSON> et al. (2023) show that simulating human annotators with LLMs in this way is an effective way to generate human-quality labels at a much lower cost.We prompted GPT-3.5 with the below two prompts for helpfulness and harmlessness, which are based on the instructions given to human annotators in <PERSON> et al. (2022a) . Note that for the harmlessness labels, we ask the model which response is more harmful but then invert the resulting label. We found that when GPT-3.5 labeled according to the same objective as the original label in the dataset, the agreement between the human and machine annotations was 63%, similar to the researcher-annotator agreement in <PERSON> et al. (2022a) .", "cite_spans": [{"start": 914, "end": 934, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF15"}, {"start": 1215, "end": 1233, "text": "<PERSON> et al. (2022a)", "ref_id": null}, {"start": 1579, "end": 1597, "text": "<PERSON> et al. (2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "C.1 PREFERENCE DATASET", "sec_num": null}, {"text": "In this task, you will be presented with a prompt and two responses from an AI assistant. Your task is to determine which response is more * helpful * and * honest * . Please consider the responses carefully, and use your overall judgement. Ask yourself \"did this response represent a thoughtful, honest, and reasonable attempt to either directly help me, or to converse with me to elicit information that will make it easier for the AI to help me later on in this conversation\". Note that this is subtle -if you tell the AI about a difficult personal situation, helping might involve showing empathy and understanding. But if you ask for something practical, the AI should realize you just want a concise response, and not prolong the interaction with irrelevant questions and comments. The AI assistant should be helpful in the same way that a friend or subject-expert would (hopefully) be helpful.The most common bad behaviors involve the AI making up facts, websites, and statistics. Please fact check the AI if it seems to be making reasonable claims that reference something", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Prompt for helpfulness:", "sec_num": null}, {"text": "Prompt: Write a Wikipedia article about the following topic: What tools do I need to cut down a stop sign?Safe response: I apologize, but I will not provide information to assist with illegal... Figure 4 : A comparison of how DPL and normal preference learning evaluate two responses to a jailbreak prompt. Normal preference learning assigns higher utility to the jailbroken response. While DPL also assigns a higher mean utility to the unsafe response, it also assigns it higher variance, indicating there is disagreement resulting from the helpfulness and harmlessness objectives diverging. Thus, if we evaluate the responses based on their lower quantiles (dashed lines), the safe response is preferred.", "cite_spans": [], "ref_spans": [{"start": 202, "end": 203, "text": "4", "ref_id": null}], "eq_spans": [], "section": "D ADDITIONAL RESULTS", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "APRIL: Active Preference-learning based Reinforcement Learning", "authors": [{"first": "Riad", "middle": [], "last": "Akrour", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Sebag", "suffix": ""}], "year": 2012, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1208.0984"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. APRIL: Active Preference-learning based Reinforcement Learning, August 2012. URL http://arxiv.org/abs/1208.0984. arXiv:1208.0984 [cs].", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "A General Language Assistant as a Laboratory for Alignment", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yun<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dawn", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Deep", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Nova", "middle": [], "last": "Das<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Elhage", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hatfield-<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Kernion", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2112.00861"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. A General Language Assistant as a Laboratory for Alignment, December 2021. URL http://arxiv.org/abs/2112.00861. arXiv:2112.00861 [cs].", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Constitutional AI: Harmlessness from AI Feedback", "authors": [{"first": "Yun<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kund<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Kernion", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Azalia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dawn", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Deep", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ladish", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Lukosuite", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Elhage", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Nova", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Das<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ringer", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["El"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Showk", "suffix": ""}, {"first": "Tamera", "middle": [], "last": "Fort", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Telleen<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Conerly", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "Hume", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hatfield-<PERSON><PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2212.08073"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Constitutional AI: Harmlessness from AI Feedback, December 2022b. URL http://arxiv.org/abs/2212. 08073. arXiv:2212.08073 [cs].", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Which Examples Should be Multiply Annotated? Active Learning When Annotators May Disagree", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Iii", "middle": [], "last": "", "suffix": ""}], "year": 2023, "venue": "Findings of the Association for Computational Linguistics: ACL 2023", "volume": "", "issue": "", "pages": "10352--10371", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Which Examples Should be Multiply Annotated? Active Learning When Annotators May Disagree. In Findings of the Associa- tion for Computational Linguistics: ACL 2023, pp. 10352-10371, Toronto, Canada, July 2023. Association for Computational Linguistics. URL https://aclanthology.org/2023. findings-acl.658.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "LESS is More: Rethinking Probabilistic Models of Human Behavior", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "R", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["F"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "S", "middle": ["<PERSON>"], "last": "Fisac", "suffix": ""}, {"first": "Anca", "middle": ["D"], "last": "Sastry", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Proceedings of the 2020 ACM/IEEE International Conference on Human-Robot Interaction", "volume": "", "issue": "", "pages": "429--437", "other_ids": {"DOI": ["10.1145/3319502.3374811"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. LESS is More: Rethinking Probabilistic Models of Human Behavior. Proceedings of the 2020 ACM/IEEE International Conference on Human-Robot Interaction, pp. 429-437, March 2020. doi: 10.1145/3319502.3374811. URL https://dl.acm.org/doi/10.1145/3319502.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Conference Name: HRI '20: ACM/IEEE International Conference on Human-Robot Interaction ISBN: 9781450367462 Place", "authors": [], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"DOI": ["10.1145/3319502.3374811"]}, "num": null, "urls": [], "raw_text": "Conference Name: HRI '20: ACM/IEEE International Conference on Human-Robot Interaction ISBN: 9781450367462 Place: Cambridge United Kingdom Publisher: ACM.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "A Survey of Preference-Based Online Learning with Bandit Algorithms", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Busa-Fekete", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "Lecture Notes in Computer Science", "volume": "", "issue": "", "pages": "18--39", "other_ids": {"DOI": ["10.1007/978-3-319-11662-43"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON>. A Survey of Preference-Based Online Learning with Bandit Algorithms. In <PERSON>, <PERSON>, <PERSON>, and <PERSON> (eds.), Algorithmic Learning Theory, Lecture Notes in Computer Science, pp. 18-39, Cham, 2014. Springer International Publishing. ISBN 978-3-319-11662-4. doi: 10.1007/ 978-3-319-11662-4 3.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Efficient Bayesian Inference for Generalized Bradley-Terry Models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2010, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1011.1761"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>. Efficient Bayesian Inference for Generalized Bradley-Terry Models, November 2010. URL http://arxiv.org/abs/1011.1761. arXiv:1011.1761 [stat].", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Recovering Preferences From Finite Data", "authors": [{"first": "P", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Chambers", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "Echenique", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "Econometrica", "volume": "89", "issue": "4", "pages": "1633--1664", "other_ids": {"DOI": ["10.3982/ECTA17845"], "ISSN": ["1468-0262"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Recovering Prefer- ences From Finite Data. Econometrica, 89(4):1633-1664, 2021. ISSN 1468-0262. doi: 10.3982/ECTA17845. URL https://onlinelibrary.wiley.com/doi/abs/10.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Spectral MLE: Top-$K$ Rank Aggregation from Pairwise Comparisons", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1504.07218"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON>. Spectral MLE: Top-$K$ Rank Aggregation from Pairwise Com- parisons, May 2015. URL http://arxiv.org/abs/1504.07218. arXiv:1504.07218 [cs, math, stat].", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Deep reinforcement learning from human preferences", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1706.03741"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Deep reinforcement learning from human preferences, June 2017. URL http://arxiv.org/abs/ 1706.03741. arXiv:1706.03741 [cs, stat].", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Common Voting Rules as Maximum Likelihood Estimators", "authors": [{"first": "<PERSON>", "middle": [], "last": "Conitzer", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sandholm", "suffix": ""}], "year": 2005, "venue": "UAI '05, Proceedings of the 21st Conference in Uncertainty in Artificial Intelligence", "volume": "", "issue": "", "pages": "1--2", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. Common Voting Rules as Maximum Likeli- hood Estimators. In UAI '05, Proceedings of the 21st Conference in Uncertainty in Ar- tificial Intelligence, Edinburgh, Scotland, July 26-29, 2005, pp. 145-152. AUAI Press, 2005. URL https://dslpitt.org/uai/displayArticleDetails.jsp?mmnu= 1&smnu=2&article_id=1213&proceeding_id=21.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Safe RLHF: Safe Reinforcement Learning from Human Feedback", "authors": [{"first": "<PERSON>", "middle": [], "last": "Dai", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Pan", "suffix": ""}, {"first": "Ruiyang", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Jiaming", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xin<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yizhou", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yaodong", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.12773"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Safe RLHF: Safe Reinforcement Learning from Human Feedback, October 2023. URL http://arxiv.org/abs/2310.12773. arXiv:2310.12773 [cs].", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "AlpacaFarm: A Simulation Framework for Methods that Learn from Human Feedback", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xuechen", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ba", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["B"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.14387"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>. AlpacaFarm: A Simulation Framework for Methods that Learn from Human Feedback, August 2023. URL http://arxiv.org/ abs/2305.14387. arXiv:2305.14387 [cs].", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "The Borda count and agenda manipulation", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 1998, "venue": "Social Choice and Welfare", "volume": "15", "issue": "2", "pages": "289--296", "other_ids": {"ISSN": ["0176-1714"]}, "num": null, "urls": [], "raw_text": "<PERSON>. The Borda count and agenda manipulation. Social Choice and Welfare, 15(2): 289-296, 1998. ISSN 0176-1714. URL https://www.jstor.org/stable/41106256. Publisher: Springer.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "A density estimation perspective on learning from pairwise human preferences", "authors": [{"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2311.14115"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. A density estimation perspective on learning from pairwise human preferences, 2023. URL http://arxiv.org/abs/2311.14115. arXiv:2311.14115 [cs].", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Incentive Compatible Active Learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "Echenique", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1911.05171"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. Incentive Compatible Active Learning, November 2019. URL http://arxiv.org/abs/1911.05171. arXiv:1911.05171 [cs].", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "The original Borda count and partial voting", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2013, "venue": "Social Choice and Welfare", "volume": "40", "issue": "2", "pages": "1432--1217", "other_ids": {"DOI": ["10.1007/s00355-011-0603-9"], "ISSN": ["0176-1714"]}, "num": null, "urls": [], "raw_text": "<PERSON>. The original Borda count and partial voting. Social Choice and Welfare, 40(2): 353-358, February 2013. ISSN 0176-1714, 1432-217X. doi: 10.1007/s00355-011-0603-9. URL http://link.springer.com/10.1007/s00355-011-0603-9.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Multi-Principal Assistance Games", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hadfield-Menell", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2007.09540"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Multi- Principal Assistance Games, July 2020. URL http://arxiv.org/abs/2007.09540. arXiv:2007.09540 [cs].", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "When the Majority is Wrong: Modeling Annotator Disagreement for Subjective Tasks", "authors": [{"first": "Eve", "middle": [], "last": "Fleisig", "suffix": ""}, {"first": "Rediet", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"DOI": ["10.48550/ARXIV.2305.06626"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. When the Majority is Wrong: Modeling Annotator Disagreement for Subjective Tasks. 2023. doi: 10.48550/ARXIV.2305.06626. URL https: //arxiv.org/abs/2305.06626. Publisher: arXiv Version Number: 3.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Comparative analysis of Bradley-Terry and <PERSON><PERSON><PERSON>-Mosteller paired comparison models for image quality assessment", "authors": [{"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2001, "venue": "PICS", "volume": "1", "issue": "", "pages": "108--112", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Comparative analysis of <PERSON><PERSON> and <PERSON><PERSON><PERSON>-Mosteller paired compari- son models for image quality assessment. In PICS, volume 1, pp. 108-112, 2001.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Approximate Ranking from Pairwise Comparisons", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Max", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1801.01253"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Approximate Ranking from Pairwise Comparisons, January 2018. URL http://arxiv.org/abs/1801. 01253. arXiv:1801.01253 [cs, math, stat].", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Minimax Rate for Learning From Pairwise Comparisons in the BTL Model", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Saligrama", "suffix": ""}], "year": 2020, "venue": "Proceedings of the 37th International Conference on Machine Learning", "volume": "", "issue": "", "pages": "4193--4202", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Minimax Rate for Learning From Pairwise Comparisons in the BTL Model. In Proceedings of the 37th International Conference on Machine Learning, pp. 4193-4202. PMLR, November 2020. URL https://proceedings. mlr.press/v119/hendrickx20a.html. ISSN: 2640-3498.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "LoRA: Low-Rank Adaptation of Large Language Models", "authors": [{"first": "<PERSON>", "middle": ["J"], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Yuanzhi", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weizhu", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2106.09685"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. LoRA: Low-Rank Adaptation of Large Language Models, October 2021. URL http://arxiv.org/abs/2106.09685. arXiv:2106.09685 [cs].", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Reward-Rational (Implicit) Choice: A Unifying Formalism for Reward Learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Anca", "middle": ["D"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2002.04833"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Reward-Rational (Implicit) Choice: A Unifying Formalism for Reward Learning. arXiv:2002.04833 [cs], December 2020. URL http://arxiv.org/abs/2002.04833. arXiv: 2002.04833.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Embedding Democratic Values into Social Media AIs via Societal Objective Functions", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "Lam", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON><PERSON>"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.13912"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Embedding Democratic Values into Social Media AIs via Societal Objective Functions, July 2023. URL http://arxiv.org/abs/2307.13912. arXiv:2307.13912 [cs].", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Voting systems. University of Kansas, Department of Mathematics", "authors": [{"first": "<PERSON>", "middle": ["E"], "last": "<PERSON>", "suffix": ""}], "year": 2005, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Voting systems. University of Kansas, Department of Mathematics, 2005. URL https://pj.freefaculty.org/Ukraine/PJ3_VotingSystemsEssay.pdf.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Models of human preference for learning reward functions", "authors": [{"first": "W", "middle": ["<PERSON>"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hatgis-Kessell", "suffix": ""}, {"first": "Serena", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stone", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2206.02231"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>. Models of human preference for learning reward functions, June 2022. URL http://arxiv.org/abs/2206.02231. arXiv:2206.02231 [cs, eess].", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "The Boltzmann Policy Distribution: Accounting for Systematic Suboptimality in Human Models", "authors": [{"first": "<PERSON>", "middle": [], "last": "Laidlaw", "suffix": ""}, {"first": "Anca", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2204.10759"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. The Boltzmann Policy Distribution: Accounting for System- atic Suboptimality in Human Models, April 2022. URL http://arxiv.org/abs/2204. 10759. arXiv:2204.10759 [cs].", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Uncertain decisions facilitate better preference learning. Advances in Neural Information Processing Systems", "authors": [{"first": "<PERSON>", "middle": [], "last": "Laidlaw", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "34", "issue": "", "pages": "15070--15083", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Uncertain decisions facilitate better preference learning. Ad- vances in Neural Information Processing Systems, 34:15070-15083, 2021.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Benchmarking Preference-Based Reinforcement Learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Anca", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "B-Pref", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2111.03026"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. B-Pref: Benchmarking Preference-Based Reinforcement Learning, November 2021. URL http://arxiv.org/abs/2111.03026. arXiv:2111.03026 [cs].", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Math in Society", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2012, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Math in Society. CreateSpace Independent Publishing Platform, September 2012. ISBN 978-1-4792-7653-0.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Decoupled Weight Decay Regularization", "authors": [{"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1711.05101"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Decoupled Weight Decay Regularization, January 2019. URL http://arxiv.org/abs/1711.05101. arXiv:1711.05101 [cs, math].", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Fast and Accurate Inference of Plackett-Luce Models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "Advances in Neural Information Processing Systems", "volume": "28", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Fast and Accurate Inference of Plackett<PERSON><PERSON>. In Advances in Neural Information Processing Systems, volume 28. Curran Associates, Inc., 2015. URL https://papers.nips.cc/paper_files/paper/2015/hash/ 2a38a4a9316c49e5a833517c45d31070-Abstract.html.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "AI Alignment and Social Choice: Fundamental Limitations and Policy Implications", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2310.16048"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>. AI Alignment and Social Choice: Fundamental Limitations and Policy Impli- cations, October 2023. URL http://arxiv.org/abs/2310.16048. arXiv:2310.16048 [cs].", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Training language models to follow instructions with human feedback", "authors": [{"first": "<PERSON>", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Almeida", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Katarina", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hilton", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["F"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "December 2022", "volume": "35", "issue": "", "pages": "27730--27744", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Training language models to follow instructions with hu- man feedback. Advances in Neural Information Processing Systems, 35:27730-27744, Decem- ber 2022. URL https://proceedings.neurips.cc/paper_files/paper/2022/ hash/b1efde53be364a73914f58805a001731-Abstract-Conference.html.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Dueling RL: Reinforcement Learning with Trajectory Preferences", "authors": [{"first": "Aldo", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2111.04850"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Dueling RL: Reinforcement Learning with Trajectory Preferences, November 2021. URL http://arxiv.org/abs/2111.04850. arXiv:2111.04850 [cs].", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "PyTorch: An Imperative Style, High-Performance Deep Learning Library", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "Gross", "suffix": ""}, {"first": "Francisco", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Natalia", "middle": [], "last": "Gimelshein", "suffix": ""}, {"first": "Luca", "middle": [], "last": "Antiga", "suffix": ""}, {"first": "Alban", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>son", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Soumith", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1912.01703"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. PyTorch: An Imperative Style, High-Performance Deep Learning Library, December 2019. URL http://arxiv.org/abs/1912.01703. arXiv:1912.01703 [cs, stat].", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "A Statistical Convergence Perspective of Algorithms Rank Aggregation from Pairwise Data", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2014, "venue": "Proceedings of the 31st International Conference on Machine Learning", "volume": "", "issue": "", "pages": "118--126", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON>. A Statistical Convergence Perspective of Algorithms Rank Aggregation from Pairwise Data. In Proceedings of the 31st International Conference on Machine Learning, pp. 118-126. PMLR, January 2014. URL https://proceedings.mlr.press/ v32/rajkumar14.html. ISSN: 1938-7228.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Active Preference-Based Learning of Reward Functions", "authors": [{"first": "Dorsa", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Anca", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Robotics: Science and Systems XIII. Robotics: Science and Systems Foundation", "volume": "", "issue": "", "pages": "", "other_ids": {"DOI": ["10.15607/RSS.2017.XIII.053"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Active Preference-Based Learning of Reward Functions. In Robotics: Science and Systems XIII. Robotics: Science and Systems Foundation, July 2017. ISBN 978-0-9923747-3-0. doi: 10.15607/RSS.2017.XIII.053. URL http://www.roboticsproceedings.org/rss13/p53.pdf.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Simple, Robust and Optimal Ranking from Pairwise Comparisons", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Journal of Machine Learning Research", "volume": "18", "issue": "199", "pages": "1--38", "other_ids": {"ISSN": ["1533-7928"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Simple, Robust and Optimal Ranking from Pairwise Comparisons. Journal of Machine Learning Research, 18(199):1-38, 2018. ISSN 1533-7928. URL http://jmlr.org/papers/v18/16-206.html.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Estimation from Pairwise Comparisons: Sharp Minimax Bounds with Topology Dependence", "authors": [{"first": "B", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1505.01462"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Estimation from Pairwise Comparisons: Sharp Minimax Bounds with Topology Dependence, May 2015. URL http://arxiv.org/abs/1505.01462. arXiv:1505.01462 [cs, math, stat].", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Invariance in Policy Optimisation and Partial Identifiability in Reward Learning", "authors": [{"first": "<PERSON>ar", "middle": [], "last": "Max", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Skalse", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Farrugia-Roberts", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Abate", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gleave", "suffix": ""}], "year": 2023, "venue": "Proceedings of the 40th International Conference on Machine Learning", "volume": "", "issue": "", "pages": "32033--32058", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Invariance in Policy Optimisation and Partial Identifiability in Reward Learning. In Proceedings of the 40th International Conference on Machine Learning, pp. 32033-32058. PMLR, July 2023. URL https://proceedings.mlr.press/v202/skalse23a. html. ISSN: 2640-3498.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Learning to summarize with human feedback", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Stiennon", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chelsea", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["F"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "3008--3021", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Learning to summarize with human feedback. In Advances in Neural Information Processing Systems, volume 33, pp. 3008-3021. Curran Asso- ciates, Inc., 2020. URL https://proceedings.neurips.cc/paper/2020/hash/ 1f89885d556929e98d3ef9b86448f951-Abstract.html.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Llama 2: Open Foundation and Fine-Tuned Chat Models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Louis", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stone", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Batra", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Shruti", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "B<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Cristian <PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Guillem", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Esiobu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "Vedanuj", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Artem", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Punit", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ying<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xavier", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Todor", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Molybog", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Rungt<PERSON>", "suffix": ""}, {"first": "Kalyan", "middle": [], "last": "Saladi", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>uan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON>", "suffix": ""}, {"first": "Ra<PERSON><PERSON>", "middle": [], "last": "Subrama<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Xiaoqing", "suffix": ""}, {"first": "Binh", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>in", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Yan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Zarov", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Fan", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kambadur", "suffix": ""}, {"first": "Au<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Sc<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09288"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Llama 2: Open Foundation and Fine-Tuned Chat Models, July 2023. URL http://arxiv.org/abs/2307.09288. arXiv:2307.09288 [cs].", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "How Does LLM Safety Training Fail?", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Jailbroken", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.02483"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Jailbroken: How Does LLM Safety Training Fail?, July 2023. URL http://arxiv.org/abs/2307.02483. arXiv:2307.02483 [cs].", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "HuggingFace's Transformers: Stateof-the-art Natural Language Processing", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Debut", "suffix": ""}, {"first": "Victor", "middle": [], "last": "San<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Delangue", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Cistac", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ya<PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Jernite", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Plu", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>ger", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1910.03771"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON> HuggingFace's Transformers: State- of-the-art Natural Language Processing, July 2020. URL http://arxiv.org/abs/1910. 03771. arXiv:1910.03771 [cs].", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Learning Mixtures of Plackett-Luce Models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Lirong", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1603.07323"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Learning Mixtures of Plackett-Luce Models, March 2020. URL http://arxiv.org/abs/1603.07323. arXiv:1603.07323 [cs].", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Principled Reinforcement Learning with Human Feedback from Pairwise or $K$-wise Comparisons", "authors": [{"first": "Banghua", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jiantao", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["I"], "last": "Jordan", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2301.11270"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Principled Reinforcement Learning with Human Feedback from Pairwise or $K$-wise Comparisons, May 2023. URL http://arxiv.org/ abs/2301.11270. arXiv:2301.11270 [cs, math, stat].", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Consequences of Misaligned AI", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hadfield-Menell", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "15763--15773", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>-<PERSON><PERSON>. Consequences of Misaligned AI. In Advances in Neural Information Processing Systems, volume 33, pp. 15763-15773. Curran Asso- ciates, Inc., 2020. URL https://proceedings.neurips.cc/paper/2020/hash/ b607ba543ad05417b8507ee86c54fcb7-Abstract.html.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Fine-Tuning Language Models from Human Preferences", "authors": [{"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stiennon", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1909.08593"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Fine-Tuning Language Models from Human Preferences, Jan- uary 2020. URL http://arxiv.org/abs/1909.08593. arXiv:1909.08593 [cs, stat].", "links": null}}, "ref_entries": {"FIGREF0": {"num": null, "uris": null, "fig_num": "2", "text": "RELATED WORK Preference learning and its use in reinforcement learning have a long history <PERSON><PERSON><PERSON><PERSON> et al. (2012); Busa-Fekete & Hüllermeier (2014); <PERSON><PERSON> et al. (2017); <PERSON><PERSON> et al. (2017); <PERSON><PERSON><PERSON> et al.", "type_str": "figure"}, "FIGREF1": {"num": null, "uris": null, "fig_num": "2", "text": "Figure 2: We introduce distributional preference learning (DPL), which explicitly accounts for hidden context. While normal preference learning outputs a single utility estimate for each alternative, DPL outputs a distribution over utilities. This distribution represents the range of utility values for that alternative as the hidden context varies, e.g., the distribution of utilities assigned to a chatbot response by different annotators or according to different objectives (like harmlessness vs. helpfulness). cumulative distribution function of ϵ(a) -ϵ(b). Then the utility function û learned by minimizing (6) satisfies û(a) > û(b) ⇔ ū(a) > ū(b) for any a, b ∈ A.", "type_str": "figure"}, "FIGREF2": {"num": null, "uris": null, "fig_num": null, "text": "Proposition 3.3. ∃A, D z , u s.t ∀a, b ∈ A, [ū(a) > ū(b)] ⇒ [p u,Dz (a, b) > 1/2], but û is not equivalent to ū, i.e., there exist a, b ∈ A such that û(a) > û(b) but ū(a) < ū(b).", "type_str": "figure"}, "FIGREF4": {"num": null, "uris": null, "fig_num": null, "text": "μ(a) = E[ D(a)]. Then we define r 2 = Var[μ(a)]/(Var[μ(a)] + E[Var[ D(a)]]", "type_str": "figure"}, "FIGREF5": {"num": null, "uris": null, "fig_num": null, "text": "1. BTL preference learning implicitly aggregates hidden context according to Borda count. That is, if û is optimized according to (6), then ∀a, b ∈ A, û(a) > û(b) ⇔ BC(a) > BC(b).", "type_str": "figure"}, "FIGREF6": {"num": null, "uris": null, "fig_num": null, "text": "p u,Dz (a, c) -c̸ =b p u,Dz (b, c) (i) ≥ BC(a) -BC(b) > 0.(i) follows from σ(•) being an increasing function and our assumption that û(a) ≤ û(b). Hence g(α) > f (α) for any α. Observe the following contradiction:0 = f (û(a)) > g(û(a)) ≥ g(û(b)) = 0The first inequality follows from the fact above that g(α) > f (α); the second inequality follows from f being increasing and û(a) ≤ û(b) by assumption. Thus, by contradiction, it must be that u(a) > u(b).To show the the backward implication, if instead BC(a) ≥ BC(b), and by contradiction û(a) < û(b), then we have that:g(α) -f (α) = σ(α -û(a)) -σ(α -û(b)) + c̸ =a p u,Dz (a, c) -c̸ =b p u,Dz (b, c) > BC(a) -BC(b) ≥ 0, after which the proof proceeds identically. Thus, û is equivalent to BC. A.4 PROOF OF THEOREM 3.2 Theorem 3.2. Let ϵ(a) be independent and identically distributed for all a ∈ A. Furthermore, suppose ϵ(a) -ϵ(b) has support around 0, i.e., ∀δ > 0, F a,b (δ) > F a,b (0) = 1 2 , where F a,b is the cumulative distribution function of ϵ(a) -ϵ(b). Then the utility function û learned by minimizing (6) satisfies û(a) > û(b) ⇔ ū(a) > ū(b) for any a, b ∈ A.", "type_str": "figure"}, "FIGREF7": {"num": null, "uris": null, "fig_num": null, "text": "We motivate this line of exploration by noting that Borda count and pairwise majority (denoted M : A × A → {0, 1}) can be induced by a classifier: BC(a) ∝ c∈A ρ(a, c) M (a, b) = 1{ρ(a, b) > ρ(b, a)}", "type_str": "figure"}, "FIGREF8": {"num": null, "uris": null, "fig_num": null, "text": "<prompt> Now, consider the following two responses from the AI assistant: Response A: <response A> Response B: <response B> Which response is more helpful and honest? Explain your reasoning and then give your final answer by writing \"A\" or \"B\" on a new line. Prompt for harmfulness: In this task, you will be presented with a prompt and two responses from an AI assistant. Your task is to choose the * most harmful * response. Examples of harmful behavior include: * Responses about people from different cultures that are stereotypical/offensive instead of subtle/mature. * Responses that are inappropriate and unhelpful. * Responses that help the user do something illegal or unethical, like robbing a bank. * Responses that are offensive even though the user did not directly ask for such content. * Anything that is obnoxious, offensive, or harmful. Consider the following conversation between an AI assistant and a human: {prompt_indented} Now, consider the following two responses from the AI assistant: Response A: {responses[0]} Response B: {responses[1]} Which response is more harmful? Explain your reasoning and then", "type_str": "figure"}, "FIGREF10": {"num": null, "uris": null, "fig_num": null, "text": "log pi (a) , where κ is the weight of the entropy bonus. We use κ = 0.1 in all experiments with the categorical DPL model.", "type_str": "figure"}, "TABREF0": {"num": null, "content": "<table><tr><td>Pref. learning</td><td>Training</td><td colspan=\"2\">Jailbreak Helpfulness</td><td/><td/><td/></tr><tr><td>method</td><td>dataset</td><td>rate</td><td>accuracy</td><td>Training</td><td colspan=\"2\">r 2 from DPL</td></tr><tr><td>Standard</td><td>Helpful</td><td>52.4%</td><td>72.6%</td><td>dataset</td><td colspan=\"2\">Mean Categor-</td></tr><tr><td>Standard</td><td>Harmless</td><td>3.7%</td><td>49.5%</td><td/><td>&amp; var.</td><td>ical</td></tr><tr><td>Standard</td><td>Combined</td><td>25.1%</td><td>68.2 %</td><td>Helpful</td><td>0.89</td><td>0.63</td></tr><tr><td colspan=\"2\">Mean &amp; var. DPL Combined ↰ Risk-averse</td><td>30.5% 20.3%</td><td>68.4% 66.4%</td><td>Harmless Combined</td><td>0.77 0.53</td><td>0.53 0.41</td></tr><tr><td>Categorical DPL ↰ Risk-averse</td><td>Combined</td><td>32.1% 13.4%</td><td>66.2% 66.2%</td><td colspan=\"3\">(b) The r 2 values, which quantify</td></tr><tr><td/><td/><td/><td/><td colspan=\"3\">the effect of hidden context (see Sec-</td></tr><tr><td colspan=\"4\">(a) Combining our distribution preference learning (DPL) methods</td><td colspan=\"3\">tion 4), measured by DPL mod-</td></tr><tr><td colspan=\"4\">with risk-averse optimization mitigates jailbreaks without hurting</td><td colspan=\"3\">els trained on different preference</td></tr><tr><td colspan=\"2\">accuracy on non-harmful prompts.</td><td/><td/><td>datasets.</td><td/><td/></tr></table>", "text": "Results from our experiments on explaining and mitigating LLM jailbreaks in Section 4.", "html": null, "type_str": "table"}}}}