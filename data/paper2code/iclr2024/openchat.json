{"paper_id": "openchat", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:00:36.472074Z"}, "title": "OPENCHAT: ADVANCING O<PERSON><PERSON>-SOURCE LANGUAGE MODELS WITH MIXED-QUALITY DATA", "authors": [{"first": "Guan", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "Xianyuan", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "Xiangang", "middle": [], "last": "Li", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "Song", "suffix": "", "affiliation": {"laboratory": "Laboratory of Brain and Intelligence", "institution": "Tsinghua University", "location": {}}, "email": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Tsinghua University", "location": {}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Nowadays, open-source large language models like LLaMA have emerged. Recent developments have incorporated supervised fine-tuning (SFT) and reinforcement learning fine-tuning (RLFT) to align these models with human goals. However, SFT methods treat all training data with mixed quality equally, while RLFT methods require high-quality pairwise or ranking-based preference data. In this study, we present a novel framework, named OpenChat, to advance open-source language models with mixed-quality data. Specifically, we consider the general SFT training data, consisting of a small amount of expert data mixed with a large proportion of sub-optimal data, without any preference labels. We propose the C(onditioned)-RLFT, which regards different data sources as coarse-grained reward labels and learns a class-conditioned policy to leverage complementary data quality information. Interestingly, the optimal policy in C-RLFT can be easily solved through single-stage, RL-free supervised learning, which is lightweight and avoids costly human preference labeling. Through extensive experiments on three standard benchmarks, our openchat-13b fine-tuned with C-RLFT achieves the highest average performance among all 13b open-source language models. Moreover, we use AGIEval to validate the model generalization performance, in which only openchat-13b surpasses the base model. Finally, we conduct a series of analyses to shed light on the effectiveness and robustness of OpenChat. Our code, data, and models are publicly available at https://github.com/imoneoi/openchat and https://huggingface.co/openchat.", "pdf_parse": {"paper_id": "openchat", "_pdf_hash": "", "abstract": [{"text": "Nowadays, open-source large language models like LLaMA have emerged. Recent developments have incorporated supervised fine-tuning (SFT) and reinforcement learning fine-tuning (RLFT) to align these models with human goals. However, SFT methods treat all training data with mixed quality equally, while RLFT methods require high-quality pairwise or ranking-based preference data. In this study, we present a novel framework, named OpenChat, to advance open-source language models with mixed-quality data. Specifically, we consider the general SFT training data, consisting of a small amount of expert data mixed with a large proportion of sub-optimal data, without any preference labels. We propose the C(onditioned)-RLFT, which regards different data sources as coarse-grained reward labels and learns a class-conditioned policy to leverage complementary data quality information. Interestingly, the optimal policy in C-RLFT can be easily solved through single-stage, RL-free supervised learning, which is lightweight and avoids costly human preference labeling. Through extensive experiments on three standard benchmarks, our openchat-13b fine-tuned with C-RLFT achieves the highest average performance among all 13b open-source language models. Moreover, we use AGIEval to validate the model generalization performance, in which only openchat-13b surpasses the base model. Finally, we conduct a series of analyses to shed light on the effectiveness and robustness of OpenChat. Our code, data, and models are publicly available at https://github.com/imoneoi/openchat and https://huggingface.co/openchat.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Recently, there have been notable advancements in Large Language Models (LLMs), such as GPT-4 (OpenAI, 2023) and Chinchilla (<PERSON> et al., 2022) , demonstrating impressive performance in various downstream natural language processing (NLP) tasks (<PERSON> et al., 2023) . Despite the remarkable success of GPT-4, the specific techniques employed in its development remain shrouded in mystery. To gain a deeper understanding of the underlying technical aspects and to promote the widespread adoption of LLMs, a series of open-source base language models have emerged, especially LLaMA (<PERSON><PERSON><PERSON><PERSON> et al., 2023a) and Llama 2 (<PERSON><PERSON><PERSON><PERSON> et al., 2023b) . Building upon the released base language models, there are typically two methods to align these base models to specific abilities, including supervised fine-tuning (SFT) and reinforcement learning fine-tuning (RLFT).", "cite_spans": [{"start": 88, "end": 108, "text": "GPT-4 (OpenAI, 2023)", "ref_id": null}, {"start": 124, "end": 147, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF13"}, {"start": 249, "end": 268, "text": "(<PERSON> et al., 2023)", "ref_id": null}, {"start": 583, "end": 606, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}, {"start": 619, "end": 642, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "The first line of methods (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) use SFT to enhance instruction following abilities. Most existing methods primarily focus on designing SFT datasets. Some studies (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) collect user-shared conversations as well as human feedback datasets from the public web, while others (<PERSON> et al., 2023a; <PERSON><PERSON> et al., 2023) develop frameworks for automatically gathering extensive open-domain instructions spanning various difficulty levels. However, these constructed SFT datasets are generally mixed with limited expert data Figure 1 : Our proposed framework OpenChat with Conditioned-RLFT to advance the open-source language model fine-tuning with mixed-quality data, comparing to previous supervised fine-tuning (SFT) method and reinforcement learning fine-tuning (RLFT) method. MLE and RL denote maximum likelihood estimates and reinforcement learning, respectively. and a large proportion of sub-optimal data due to the high cost of human labor and API requests. Naturally, it is not advisable to indiscriminately feed all these mixed conversations to the base model, as the low-quality data are likely to negatively impact learning (<PERSON> et al., 2023; <PERSON> et al., 2022) . However, this is largely neglected in previous methods, which often treat all training data equally.", "cite_spans": [{"start": 26, "end": 47, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF5"}, {"start": 48, "end": 67, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF27"}, {"start": 198, "end": 219, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF5"}, {"start": 220, "end": 238, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF11"}, {"start": 342, "end": 360, "text": "(<PERSON> et al., 2023a;", "ref_id": null}, {"start": 361, "end": 379, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF9"}, {"start": 1195, "end": 1214, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF40"}, {"start": 1215, "end": 1231, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF35"}], "ref_spans": [{"start": 590, "end": 591, "text": "1", "ref_id": null}], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "To allow LLMs to go beyond modeling the training data distribution, recent LLMs (OpenAI, 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2023b) adopt RLFT to align better with the human desired behaviors, especially API-based models. The well-known reinforcement learning from human feedback (RLHF) method (<PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2017; <PERSON> et al., 2022b) first collects plenty of highquality preference feedback from human annotators to fit one or multiple reward models (typically also trained based on smaller LLMs), and then uses RL to maximize the estimated reward. The involvement of learning and optimizing with extra reward models using RL brings considerable computational and stability issues. Some recent studies (<PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) partly address this problem by avoiding fitting the reward model and fusing preference modeling and LLM fine-tuning into a single-stage training process. However, all existing RLHF methods require highquality pairwise or ranking-based preference data for preference modeling, which inevitably require expensive human expert annotations (<PERSON> et al., 2023) .", "cite_spans": [{"start": 80, "end": 94, "text": "(OpenAI, 2023;", "ref_id": null}, {"start": 95, "end": 117, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}, {"start": 280, "end": 301, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF21"}, {"start": 302, "end": 326, "text": "<PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF7"}, {"start": 327, "end": 345, "text": "<PERSON> et al., 2022b)", "ref_id": null}, {"start": 714, "end": 737, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF26"}, {"start": 738, "end": 756, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF36"}, {"start": 1093, "end": 1114, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF2"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "To address the aforementioned limitations, we propose a new framework, named OpenChat, to advance the open-source language model fine-tuning with mixed-quality data as shown in Fig. 1 . Here, we consider the general non-pairwise (nor ranking-based) SFT training data, consisting of a small amount of expert data and a large proportion of easily accessible sub-optimal data, without any preference labels. Specifically, we propose the Conditioned-RLFT (C-RLFT), which enables leveraging mixed-quality training data with very coarse-grained reward labels. The reward label can be as simple as a relative value differentiating different classes of data, i.e., expert and sub-optimal. We derive C-RLFT based on the KL-regularized RL framework (<PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2022) , which maximizes the reward while penalizing the difference between the fine-tuned policy and a reference policy. However, to remedy the imperfect reward signal, we learn the fine-tuned LLM itself as a class-conditioned policy (i.e., conditioning data source classes with distinct prompt tokens), and regularize it with a better and more informative class-conditioned reference policy instead of the original pre-trained LLM. The optimal policy for this RL problem can be shown as equivalent to a class-conditioned reward-weighted regression problem, which can be easily solved through single-stage supervised learning. C-RLFT provides several particularly desirable features for open-source LLM fine-tuning. First, it allows for simple and RL-free training, largely removing the complexities and instabilities in typical RLHF fine-tuning. Second, it has extremely low requirements for the quality of the reward and does not need costly human feedback collection.", "cite_spans": [{"start": 739, "end": 760, "text": "(<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF14"}, {"start": 761, "end": 781, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF15"}], "ref_spans": [{"start": 182, "end": 183, "text": "1", "ref_id": null}], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Despite being simple and lightweight, our proposed OpenChat with C-RLFT achieves great performance in a series of benchmark evaluations. Specifically, we leverage the ShareGPT conversations dataset1 following <PERSON><PERSON> (<PERSON> et al., 2023) and use llama-2-13b as the base model. It is worth noting that our proposed method can be applied to any mixed-quality datasets and arbitrary base language models. We conduct extensive experiments on three standard benchmarks to assess instruction following ability, including Alpaca-Eval (<PERSON> et al., 2023) , MT-bench (<PERSON> et al., 2023) and Vicuna-bench (<PERSON> et al., 2023) . The results demonstrate that openchat-13b significantly surpasses previous 13b open-source language models and can even outperform gpt-3.5-turbo in all three benchmarks. Furthermore, we also use AGIEval (<PERSON><PERSON> et al., 2023) to prove the generalization, where openchat-13b also achieves the top-1 average accuracy among all 13b opensource language models. Finally, we design a series of ablation studies and analyses to validate the contribution of different components, and performance consistency, providing insights into the effectiveness and robustness of OpenChat.", "cite_spans": [{"start": 216, "end": 237, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF5"}, {"start": 527, "end": 544, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF17"}, {"start": 556, "end": 576, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF36"}, {"start": 594, "end": 615, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF5"}, {"start": 821, "end": 841, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF39"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Given a conversation dataset D = {(x i , y i )}, where x i indicates the instruction, y i is its corresponding response, the pre-trained language model π 0 (y|x) can be regarded as a probability distribution mapping from instructions to responses. There are two lines of research to adapt the pre-trained language model π 0 (y|x) to a fine-tuned language model π θ (y|x) with desirable features, including supervised fine-tuning and reinforcement learning fine-tuning.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARIES", "sec_num": "2"}, {"text": "Supervised Fine-tuning (SFT). This line of methods (<PERSON> et al., 2023a; b; <PERSON><PERSON> et al., 2023) directly uses a high-quality conversation dataset D to fine-tune the pre-trained language model π 0 (y|x) using supervised learning, i.e., maximum likelihood estimates (MLE):", "cite_spans": [{"start": 51, "end": 69, "text": "(<PERSON> et al., 2023a;", "ref_id": null}, {"start": 70, "end": 72, "text": "b;", "ref_id": null}, {"start": 73, "end": 91, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARIES", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "J SFT (θ) = E (x,y)∼D [log π θ (y|x)]", "eq_num": "(1)"}], "section": "PRELIMINARIES", "sec_num": "2"}, {"text": "where π θ is initialized from π 0 . To ensure the fine-tuning performance, SFT requires the conversation dataset D to have very high quality, because SFT treats all training data uniformly (<PERSON> et al., 2023; <PERSON> et al., 2023) . However, the collection of high-quality SFT datasets can be very expensive. Most existing open-source LLMs (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023a; <PERSON> et al., 2023) fine-tune their models using conversation datasets that likely contain a large proportion of sub-optimal data due to high costs of human labor or API requests, inevitably leading to a certain level of performance degeneration.", "cite_spans": [{"start": 189, "end": 208, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF40"}, {"start": 209, "end": 227, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF3"}, {"start": 337, "end": 358, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF5"}, {"start": 359, "end": 377, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF11"}, {"start": 378, "end": 395, "text": "<PERSON> et al., 2023a;", "ref_id": null}, {"start": 396, "end": 414, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARIES", "sec_num": "2"}, {"text": "Reinforcement Learning Fine-tuning (RLFT). Another intuitive approach to align LLMs is through RL, which models rewards according to human preference feedbacks (<PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2022a; <PERSON><PERSON><PERSON><PERSON> et al., 2023) or pre-defined classifiers (<PERSON> et al., 2023) , and fine-tune LLMs to maximize the reward. The reward r(x, y), either modeled explicitly or implicitly, assigns high values on desirable responses and low values on bad ones to guide the alignment of the finetuned LLM. A popular RL framework for fine-tuning LLMs is the KL-regularized RL (<PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2023) , which adds an additional KL penalty to constrain the fine-tuned LLM π θ (y|x) to stay close to the base pre-trained LLM π 0 (y|x). This has been shown beneficial to avoid distribution collapse as compared to naïvely maximize reward using RL (<PERSON><PERSON><PERSON> et al., 2022) . The RL objective of this series of RLFT models can be typically formulated as follows:", "cite_spans": [{"start": 160, "end": 181, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF21"}, {"start": 182, "end": 200, "text": "<PERSON> et al., 2022a;", "ref_id": null}, {"start": 201, "end": 223, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF26"}, {"start": 251, "end": 268, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF31"}, {"start": 559, "end": 580, "text": "(<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF14"}, {"start": 581, "end": 601, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF15"}, {"start": 602, "end": 624, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF26"}, {"start": 868, "end": 889, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF15"}], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARIES", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "J RLFT (θ) = E y∼π θ [r(x, y)] -βD KL (π θ , π 0 )", "eq_num": "(2)"}], "section": "PRELIMINARIES", "sec_num": "2"}, {"text": "In existing RLFT methods, high-quality reward signals play a crucial role in ensuring improved LLM fine-tuning performance. This, however, requires collecting considerable amounts of costly pairwise (or ranking-based) human preference feedback, which poses a major challenge in the development of many open-source language models.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARIES", "sec_num": "2"}, {"text": "In this section, we introduce the OpenChat framework, which provides a new possibility to fine-tune open-source LLMs using easily collectable and mixed-quality training data without any preference labels. More specifically, we consider the setting where we are given a pre-trained LLM π 0 , a small set of high-quality/expert conversation data D exp , and a larger medium-quality or sub-optimal conversation dataset D sub , we aim to fine-tune an LLM policy π θ based on π 0 using only data from D exp D sub . Taking the most popular SFT dataset ShareGPT used in Vicuna (Chiang et al., 2023) as an example, the distinct data sources from GPT-4 and GPT-3.5 can be regarded as D exp and D sub , as the overall quality of GPT-3.5 conversations generally falls short when compared to that of GPT-4 conversations (OpenAI, 2023; <PERSON> et al., 2023) , where detailed comparison can be found in Sec. 5.1.", "cite_spans": [{"start": 570, "end": 591, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF5"}, {"start": 788, "end": 822, "text": "GPT-4 conversations (OpenAI, 2023;", "ref_id": null}, {"start": 823, "end": 839, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "OPENCHAT", "sec_num": "3"}, {"text": "Obviously, it is not possible to derive accurate and fine-grained reward signals solely based on D exp and D sub . However, it should be noted that the quality difference between D exp and D sub itself can serve as implicit or weak reward signals. To make use of this coarse-grained reward information, we provide a new insight that by regularizing π θ with a better and more informative class-conditioned reference policy π c instead of the original base pre-trained LLM π 0 , we are likely to compensate for the potential deficiencies in the rewards and achieve good fine-tuning performance. In the following, we describe the details of OpenChat and its core algorithm -C-RLFT.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "OPENCHAT", "sec_num": "3"}, {"text": "Given the SFT conversation datasets D exp D sub with different quality levels, we can replenish them with distinct sources as class labels (e.g., c i ∈ {GPT-4, GPT-3.5}) and construct a classconditioned dataset D c = {(x i , y i , c i )}. We use π c (y|x, c) to denote class-conditioned distribution over instructions x and responses y in the class-conditioned dataset D c , which can be perceived similarly as the behavior policy of a dataset in offline RL literature (<PERSON> et al., 2020) , with the difference that π c is now a class-conditioned policy.", "cite_spans": [{"start": 469, "end": 490, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "CLASS-<PERSON><PERSON><PERSON><PERSON><PERSON> DATASET AND REWARDS", "sec_num": "3.1"}, {"text": "According to the different overall quality with respect to class labels, we can naturally encode coarse-grained rewards r c (x, y) in D c as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CLASS-<PERSON><PERSON><PERSON><PERSON><PERSON> DATASET AND REWARDS", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "r c (x i , y i ) = 1, if (x i , y i ) ∈ D exp (e.g., c i = GPT-4), α, if (x i , y i ) ∈ D sub (e.g., c i = GPT-3.5) (α < 1). (", "eq_num": "3"}], "section": "CLASS-<PERSON><PERSON><PERSON><PERSON><PERSON> DATASET AND REWARDS", "sec_num": "3.1"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CLASS-<PERSON><PERSON><PERSON><PERSON><PERSON> DATASET AND REWARDS", "sec_num": "3.1"}, {"text": "where we regard GPT-4 conversations as expert data D expert , and GPT-3.5 conversations as suboptimal data D sub . Meanwhile, we set α < 1 to guide the fine-tuned model to favor more of the high-quality responses.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CLASS-<PERSON><PERSON><PERSON><PERSON><PERSON> DATASET AND REWARDS", "sec_num": "3.1"}, {"text": "As the rewards r c (x, y) in Eq. ( 3) are very coarse-grained, to reliably use them in RLFT, we need to provide additional sources of information to remedy their deficiencies. Here, we introduce C-RLFT, which is inspired by the insight from the goal-conditioned supervised learning in offline RL, that by conditioning on proper information in a supervised goal/outcome-conditioned policy, it is possible to recover optimized performance (<PERSON> et al., 2021; <PERSON><PERSON> et al., 2021) . C-RLFT contains two key ingredients: 1) fine-tuning the LLM as a class-conditioned policy π θ (y|x, c), and 2) regularizing π θ with respect to the class information augmented reference policy π c rather than the original base reference policy π 0 in the KL-regularized RL framework.", "cite_spans": [{"start": 437, "end": 456, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF4"}, {"start": 457, "end": 477, "text": "<PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF10"}], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "Class-conditioned policy. Instead of directly fine-tuning an LLM from the pre-trained model π 0 as in existing methods, we model the LLM to be fine-tuned as a class-conditioned policy π θ (y|x, c). This can be easily implemented by conditioning each example from different data sources using distinct conversation templates as shown below.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "[GPT-4 Template] GPT4 User: Question<|end of turn|>GPT4 Assistant:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "[GPT-3.5 Template] GPT3 User: Question<|end of turn|>GPT3 Assistant:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "To differentiate speakers, we introduce a new <|end of turn|> special token at the end of each utterance, following <PERSON> et al. (2023) . The <|end of turn|> token functions similarly to the EOS token for stopping generation while preventing confusion with the learned meaning of EOS during pretraining. We further discuss the effect of conversation template choice in App. A.", "cite_spans": [{"start": 116, "end": 134, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF40"}], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "Policy optimization. To compensate for the coarse-grained reward information r c (x, y), we modify the original KL-regularized RL objective Eq. ( 2) and instead optimize the following problem:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "J C-RLFT (θ) = E y∼π θ [r c (x, y)] -βD KL (π θ , π c ) (4)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "The idea is to use the higher-quality and more informative class-conditioned behavior policy π c of D c for regularization, rather than the pre-trained model π 0 . We adopt this design for the following reasons. First, for most existing open-source pre-trained LLMs, their performance in many cases is still inferior to the behavior policy that generates the sub-optimal data (e.g. . This means that even the sub-optimal data D sub is likely to have higher quality than π 0 . Second, π c contains additional data source information, which can help differentiating the quality of data.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "Following prior works (<PERSON>, 2007; <PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2023) , it can be shown that the optimal solution to the above KL-regularized reward maximization objective takes the following form (see App. B for detailed derivation):", "cite_spans": [{"start": 22, "end": 45, "text": "(Peters & Schaal, 2007;", "ref_id": "BIBREF25"}, {"start": 46, "end": 64, "text": "<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF24"}, {"start": 65, "end": 85, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF15"}, {"start": 86, "end": 108, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "π * (y|x, c) ∝ π c (y|x, c) exp 1 β r c (x, y)", "eq_num": "(5)"}], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "We can thus extract the optimized policy π θ by minimizing the KL divergence between π * under the class-conditioned dataset D c (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2022) :", "cite_spans": [{"start": 129, "end": 148, "text": "(<PERSON> et al., 2020;", "ref_id": "BIBREF19"}, {"start": 149, "end": 169, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF15"}], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "π θ =arg min θ E (x,c)∼Dc [D KL (π * (•|x, c)∥π θ (•|x, c))] =arg min θ E (x,c)∼Dc [E y∼π * [-log π θ (y|x, c)]] =arg max θ E (x,y,c)∼Dc exp 1 β r c (x, y) log π θ (y|x, c)", "eq_num": "(6)"}], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "The last step is obtained by plugging the closed form π * in Eq. ( 5) and using the fact that π c is exactly the class-conditioned behavior distribution of D c . This suggests that the fine-tuned policy π θ can be learned through a simple reward-weighted regression objective with the class-conditioned dataset D c . This learning objective provides a remarkably simple scheme to fine-tune open-source LLMs. It does not require accurate reward labels, but uses conditioning to differentiate good and inferior model behaviors. Moreover, after initializing π θ with π 0 , we no longer need to load π 0 during training, while most RLHF methods using PPO for policy optimization (<PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2022a) still need to maintain π 0 to compute the KL penalty during fine-tuning. This enables C-RLFT to save a considerable amount of computation resources during training.", "cite_spans": [{"start": 675, "end": 696, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF21"}, {"start": 697, "end": 715, "text": "<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "Model inference. During the inference phase, we assume that our C-RLFT method has learned to distinguish expert and sub-optimal data distributions. Considering that we aim to exclusively generate high-quality responses for our fine-tuned class-conditioned π θ , we use the same specific conversation template employed in GPT-4 conversations during the training phase as below:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "[Inference template] GPT4 User: Question<|end of turn|>GPT4 Assistant:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-TUNING VIA C(ONDITIONED)-RLFT", "sec_num": "3.2"}, {"text": "Mixed-quality Data. Following <PERSON><PERSON> (<PERSON> et al., 2023) , we adopt a widely-used SFT dataset, the ShareGPT dataset. The ShareGPT dataset consists of approximately 70k user-shared conversations, including around 6k expert conversations generated by GPT-4 and the remaining sub-optimal conversations from GPT-3.5. We perform experiments to assess their varying quality in Sec. 5.1.", "cite_spans": [{"start": 37, "end": 58, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUPS", "sec_num": "4.1"}, {"text": "Benchmarks. To evaluate the instruction-following ability, we employ the three most widely recognized benchmarks, including AlpacaEval (<PERSON> et al., 2023) , MT-bench (<PERSON> et al., 2023) and Vicuna-bench (<PERSON> et al., 2023) . Additionally, to verify generalization, we perform all English tasks in AGIEval (<PERSON><PERSON> et al., 2023) Automatic Evaluators. To mitigate the cost of human annotations, we follow the official evaluators according to each benchmark. Specifically, AlpacaEval employs alpaca eval gpt, while MT-bench and Vicuna-bench use gpt-4. It is worth noting that these benchmarks have already computed the human agreements to ensure reliability. Additionally, we further introduce gpt-3.5 and claude-2 to eliminate the potential self-enhancement bias in App. E.", "cite_spans": [{"start": 135, "end": 152, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF17"}, {"start": 164, "end": 184, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF36"}, {"start": 202, "end": 223, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF5"}, {"start": 306, "end": 326, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF39"}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUPS", "sec_num": "4.1"}, {"text": "Metrics. We employ three metrics following the official implementations: (1) Win rate: This metric is employed for pairwise comparisons. Given a question and two answers generated by the tested model and the target model, the LLM evaluator needs to compare these two models. The tested model receives 1 point for a win, 0.5 points for a tie, and 0 points for a loss. (2) Score: This metric is applied for single-answer grading in MT-bench, where the LLM evaluator directly judges the Implementation Details. The openchat-13b is based on the llama-2-13b (<PERSON><PERSON><PERSON><PERSON> et al., 2023b) . We fine-tune the model for 5 epochs on the ShareGPT dataset using the AdamW optimizer with a sequence length of 4,096 tokens and an effective batch size of 200k tokens. Given that the reward weight term in Eq. ( 6) (exp(r c /β)) remains constant within a class, we simplify the process by assigning a unit weight to D exp and the weight of 0.1 to D sub . The AdamW optimizer's hyperparameters are set as follows: β 1 = 0.9, β 2 = 0.95, ϵ = 10 -5 , and weight decay of 0.1. We employ a cosine learning rate schedule with a maximum learning rate of 6.7×10 -5 , which decays to 10% of the maximum value. The hyperparameters remain consistent with the base model pretraining settings following <PERSON><PERSON><PERSON><PERSON> et al. (2023b) . However, we scale the learning rate proportionally to the square root of the batch size, following the theoretical analysis provided by <PERSON><PERSON>l et al. (2020) .", "cite_spans": [{"start": 553, "end": 576, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}, {"start": 1269, "end": 1291, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2023b)", "ref_id": null}, {"start": 1430, "end": 1452, "text": "<PERSON><PERSON><PERSON> et al. (2020)", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUPS", "sec_num": "4.1"}, {"text": "In the first set of results, we compare the win-rate (%) performance of openchat-13b and other popular LLMs on three standard benchmarks to assess the instruction-following ability. The results are presented in Table 1 . Among the API-based LLMs, the win rate of gpt-4 significantly outperforms all other models, demonstrating that gpt-4 maintains obvious advantages.", "cite_spans": [], "ref_spans": [{"start": 217, "end": 218, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "MAIN RESULTS", "sec_num": "4.2"}, {"text": "The open-source language model llama-2-chat-70b, which employs both SFT and RLHF, is another powerful instruction-following model which surpasses claude and gpt-3.5-turbo. However, guanaco-65b and guanaco-33b lag behind other models larger than 13b. Regarding the series of 13b models, the open-source language models based on llama-13b, including vicuna-v1.1-13b, wizardlm-v1.0-13b and ultralm-13b, achieve approximately 50% average win rates across the three benchmarks. Meanwhile, the open-source language models based on llama-2-13b generally exhibit higher average win rates. Notably, wizardlm-v1.2-13b and llama-2-chat-13b achieve average win rate scores of 74.3 and 74.4, respectively, which is close to the 76.6 score of claude. Our proposed language model openchat-13b attains the highest win rate scores in both AlpacaEval and MT-bench benchmarks, and the second-highest win rate scores in Vicuna-bench. It is worth noting that the average win rate score of openchat-13b even surpasses that of claude.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MAIN RESULTS", "sec_num": "4.2"}, {"text": "In the second set of results, we present the MT-bench scores of openchat-13b and other baseline models in Fig. 2 (a). The overall trends are align closely with the win rate performance. For the APIbased language models, gpt-4 continues to lead by a significant margin. Notably, all API-based language models perform better than open-source language models. Among the open-source language models, the series based on llama-2-13b generally surpasses those based on llama-13b. Meanwhile, our openchat-13b achieves the highest MT-bench score, even exceeding opensource language models with much larger parameters, such as llama-2-chat-70b. Although prior works (OpenAI, 2023; <PERSON> et al., 2023) have widely confirmed that GPT-4 demonstrates superior capabilities on a broad range of tasks compared to GPT-3.5, we further analyze the quality of collected GPT-3.5 and GPT-4 conversations in the ShareGPT dataset to validate our assumption of mixed-quality data. Specifically, we randomly sample 128 conversations from each data source. Then gpt-4 serves as the automatic evaluator, scoring the responses following <PERSON> et al. (2023) . As illustrated in Figure 3 , GPT-4 conversations contain more high-quality conversations and exhibit a higher overall score. The detailed scoring settings can be found in App. G.", "cite_spans": [{"start": 657, "end": 671, "text": "(OpenAI, 2023;", "ref_id": null}, {"start": 672, "end": 690, "text": "<PERSON> et al., 2023)", "ref_id": null}, {"start": 1108, "end": 1127, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF36"}], "ref_spans": [{"start": 111, "end": 112, "text": "2", "ref_id": "FIGREF1"}, {"start": 1155, "end": 1156, "text": "3", "ref_id": null}], "eq_spans": [], "section": "MAIN RESULTS", "sec_num": "4.2"}, {"text": "We conduct an ablation study on the two key components of the openchat-13b model to ascertain their individual contributions to the overall performance, including the coarse-grained rewards and the class-conditioned policy. Additionally, we introduce two important baselines. One series is only SFT which uses the same ShareGPT data as OpenChat with three filtering strategies (i.e., no-filtering, only GPT-3, and only GPT-3.5). The other is vicuna-v1.5-13b which SFT on about 125k ShareGPT data as a baseline. The results of ablation studies are detailed in Table 2 . Without coarse-grained rewards, the training phase treats different data sources equally, leading to performance decline. Similarly, without a class-conditioned policy, language models lack explicit signals to discern between expert and sub-optimal data, significantly reducing performance. We also conduct only SFT on different sources of ShareGPT data. It is worth noting that only SFT with GPT-4 data performs much better than with the entire ShareGPT data, indicating that the quality of data is much more important than quantity. However, openchat-13b still obviously outperforms all the only SFT methods, demonstrating that our proposed framework can better exploit the mixed-quality data. This indicates the significant contribution of both main components to model performance. Notably, expanding the SFT dataset from 70k to 125k has less impact on performance improvement than our proposed components, particularly the class-conditioned policy. Table 2 : Ablation studies of coarse-grained rewards (reward) and class-conditioned policy (condition) to openchat-13b.", "cite_spans": [], "ref_spans": [{"start": 565, "end": 566, "text": "2", "ref_id": null}, {"start": 1529, "end": 1530, "text": "2", "ref_id": null}], "eq_spans": [], "section": "ABLATION STUDIES", "sec_num": "5.2"}, {"text": "Firstly, we visualize the representations of openchat-13b, and its ablation version, only SFT, to distinguish between our proposed C-RLFT and SFT. We randomly sample 2,000 GPT-4 and GPT-3.5 conversations. To obtain the representations of conversations, we compute the embeddings through mean pooling of all tokens in the last Transformer layer output following <PERSON> (2018) . These embeddings, depicted in Fig. 4 Secondly, given the significant impact of the classconditioned policy, we further explore its effects on model performance during the inference phases by examining the influence of class-conditioned prompt tokens. In the inference phase, we use the GPT-4 prompt to induce openchat-13b to generate high-quality responses. Here we further verify the impacts of different inference prompts by replacing the GPT-4 prompt with the GPT-3.5 prompt. The comparison results, illustrated in Fig. 5 , reveal a substantial performance decline when using the GPT-3.5 prompt instead of the GPT-4 prompt. This suggests that our openchat-13b model can distinguish the quality of different data sources based on our class-conditioned policy, and it further indicates that the representation space of GPT-4 is superior to that of GPT-3.5.", "cite_spans": [{"start": 361, "end": 372, "text": "<PERSON> (2018)", "ref_id": "BIBREF32"}], "ref_spans": [{"start": 410, "end": 411, "text": "4", "ref_id": null}, {"start": 898, "end": 899, "text": "5", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "REVEALING SECRETS OF C-RLFT", "sec_num": "5.3"}, {"text": "In this section, we investigate the impact of varying data sizes on model performance. Specifically, we sub-sample one class in GPT-3.5 or GPT-4 with the ratio varying from 60% to 100% in 10% increments, while keeping the other class unchanged. It is worth noting that the total number of GPT-3.5 data is more than ten times larger than that of GPT-4. The results are shown in Fig. 6 . Firstly, we observe that the overall decline in both average performances is relatively modest, indicating that our openchat-13b is robust to variation in data size. Secondly, although the number of GPT-4 data points changes much less than GPT-3.5, the effect of varying GPT-4 data size is even more pronounced. This phenomenon demonstrates that expert data, while limited in quantity, is extremely important.", "cite_spans": [], "ref_spans": [{"start": 382, "end": 383, "text": "6", "ref_id": "FIGREF4"}], "eq_spans": [], "section": "EFFECTS OF DATA SIZE", "sec_num": "5.4"}, {"text": "Large Language Models. Recent years have witnessed significant advancements in LLMs, with models such as GPT-4 (OpenAI, 2023), PaLM (<PERSON><PERSON><PERSON> et al., 2022) , and others comprising hundreds of billions or more parameters. This surge in LLMs extends beyond API-based models, as a suite of open-source language models like LLaMA (<PERSON><PERSON>vron et al., 2023a) , LLaMA-2 (<PERSON><PERSON><PERSON><PERSON> et al., 2023b) , and Falcon (Penedo et al., 2023) have emerged. This paper primarily focuses on the LLaMA base models, which are among the most popular open-source language models. Supervised Fine-tuning for LLMs. A considerable body of work has been dedicated to enhancing large base language models through SFT. For instance, Alpaca (<PERSON><PERSON> et al., 2023) uses self-instruct (<PERSON> et al., 2022) to generate 52k instruction-following demonstrations via text-davinci-003. This instruction data has been extensively applied in subsequent studies, such as Koala (<PERSON><PERSON> et al., 2023) . <PERSON><PERSON> et al. (2023) follow Alpaca's setup but replace GPT-4 as the distillation teacher. WizardLM (<PERSON> et al., 2023a) introduces Evol-Instruct, a technique that rewrites Alpaca's initial instruction data into more complex instructions, thereby enhancing the model's instruction-following capabilities. Other studies, such as UltraChat (<PERSON><PERSON> et al., 2023) and <PERSON><PERSON> (<PERSON> et al., 2023b) , have designed frameworks to obtain large-scale datasets of instructional conversations. <PERSON>una (<PERSON> et al., 2023) , another popular variant, is the first to adopt ShareGPT with 70k user-shared ChatGPT conversations. Unlike previous SFT studies that treat all training data uniformly, we strive to maximize the use of mixed-quality data.", "cite_spans": [{"start": 132, "end": 156, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF6"}, {"start": 327, "end": 350, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023a)", "ref_id": null}, {"start": 361, "end": 384, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}, {"start": 398, "end": 419, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 705, "end": 725, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF27"}, {"start": 745, "end": 764, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF30"}, {"start": 928, "end": 947, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF11"}, {"start": 950, "end": 968, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF23"}, {"start": 1047, "end": 1065, "text": "(<PERSON> et al., 2023a)", "ref_id": null}, {"start": 1283, "end": 1302, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF9"}, {"start": 1313, "end": 1331, "text": "(<PERSON> et al., 2023b)", "ref_id": null}, {"start": 1429, "end": 1450, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORKS", "sec_num": "6"}, {"text": "Reinforcement Learning Fine-tuning for LLMs. To better align with preferences beyond SFT, RLFT methods have been proposed. The most well-known method is RLHF (<PERSON><PERSON><PERSON> et al., 2022) , which involves collecting preference feedback from humans to train reward models. Subsequently, Proximal Policy Optimization (PPO) is used to train the target LLM to maximize the reward given. Most API-based LLMs, such as GPT-4, ChatGPT (OpenAI, 2023) , and open-source models like Llama-2-chat series (<PERSON><PERSON>v<PERSON> et al., 2023b) , utilize RLHF techniques. However, RLHF is a complex and unstable process that involves training a reward model and the LLM with an RL objective. As a result, simpler alternatives like DPO (<PERSON><PERSON><PERSON><PERSON> et al., 2023) , RRHF (<PERSON> et al., 2023) have been proposed. DPO trains the LLM to predict and maximize reward simultaneously in a one-staged manner, while RRHF uses a ranking loss to encourage preferred answer output. Considering that the preference data is costly to collect, our method uses easily collectible and mixed-quality training data without any preference labels to finetune LLMs.", "cite_spans": [{"start": 158, "end": 179, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF21"}, {"start": 404, "end": 433, "text": "GPT-4, ChatGPT (OpenAI, 2023)", "ref_id": null}, {"start": 484, "end": 507, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023b)", "ref_id": null}, {"start": 698, "end": 721, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF26"}, {"start": 729, "end": 748, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORKS", "sec_num": "6"}, {"text": "In this paper, we present OpenChat, an innovative framework featuring the Conditioned-RLFT method, tailored to advance open-source language models with mixed-quality data. Our model, openchat-13b, delivers the highest average performance on extensive benchmarks among all 13b open-source language models, demonstrating notable advantages such as simplicity, RL-free training, and minimal reward quality requirements. Despite these encouraging results, we acknowledge potential research areas for further improvement. Firstly, our assumption of different quality according to data sources may be overly simplistic, and the assigned coarse-grained rewards could be more finely tuned to reflect the actual quality of each data point. Secondly, while our model primarily focuses on enhancing instruction-following capabilities, exploring the application of OpenChat towards improving the reasoning abilities of LLMs offers a promising avenue for future work.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION AND FUTURE WORK", "sec_num": "7"}, {"text": "This study aims to advance open-source language models with mixed-quality data. Firstly, the proliferation of these open-source language models democratizes AI research by broadening its accessibility. These models serve as inclusive, transparent platforms that stimulate innovation and research. They cultivate a dynamic community of researchers from various backgrounds, thereby facilitating more comprehensive discussions and expedited collaborations. Secondly, refining the capability of these models to follow instructions can lead to more satisfying and safer responses, including the reduction of human bias and the promotion of fairness. Lastly, the dataset employed in this study consists of publicly available, user-shared instances, which are conveniently collected. This approach to data collection helps minimize potential data leakage and privacy concerns.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ETHICS STATEMENT", "sec_num": null}, {"text": "In line with our dedication to enhancing reproducibility, we have outlined our foundational model and training hyperparameters in 4.1. Besides, the training code, data and model weights for openchat-13b is publicly available at https://github.com/imoneoi/openchat and https://huggingface.co/openchat. We further detect the effects of class-conditioned prompt tokens on model performance during the training phase. Our designed class-conditioned prompt tokens in different positions are shown in Table 3 . we attempt three distinct initial prompt tokens in different positions: before speaker, before assistant, and beginning. The results are shown in Fig. 7 . We observe that putting the conditioned prompt tokens in every turn (either before the speaker or before the assistant) performs similarly, but adding the condition only once at the beginning of the conversation performs much worse. This may be due to LLMs tend to forget the prompt at the beginning when the context is long or during subsequent turns. <PERSON><PERSON><PERSON><PERSON> et al. (2023b) also observe the gradual loss of multi-turn consistency when the system prompt is put at the beginning. Therefore, we repeat the condition prompt every turn, to improve the effect of class-conditioned policy.", "cite_spans": [{"start": 1013, "end": 1035, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2023b)", "ref_id": null}], "ref_spans": [{"start": 501, "end": 502, "text": "3", "ref_id": "TABREF2"}, {"start": 656, "end": 657, "text": "7", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}, {"text": "Conditioned Prompts", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Sources Types", "sec_num": null}, {"text": "Before speaker GPT4 User: Question<|end of turn|>GPT4 Assistant:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GPT-4", "sec_num": null}, {"text": "Before assistant User: Question<|end of turn|>GPT4 Assistant:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "GPT-4", "sec_num": null}, {"text": "Assistant is GPT4<|end of turn|>User: Question<|end of turn|>Assistant:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Beginning", "sec_num": null}, {"text": "GPT-3.5", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Beginning", "sec_num": null}, {"text": "Before speaker GPT3 User: Question<|end of turn|>GPT3 Assistant:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Beginning", "sec_num": null}, {"text": "Before assistant User: Question<|end of turn|>GPT3 Assistant:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Beginning", "sec_num": null}, {"text": "Assistant is GPT3<|end of turn|>User: Question<|end of turn|>Assistant: ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Beginning", "sec_num": null}, {"text": "The goal of C-RLFT is to find the optimal KL-regularized conditional policy. This optimization problem can be formulated as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B DERIVATION OF THE OPTIMAL POLICY IN C-RLFT", "sec_num": null}, {"text": "max π E y∼π [r c (x, y)] -βD KL (π, π c ) (7)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B DERIVATION OF THE OPTIMAL POLICY IN C-RLFT", "sec_num": null}, {"text": "To ensure π is a valid probability distribution, we add the normalization constraint and the optimization problem becomes:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B DERIVATION OF THE OPTIMAL POLICY IN C-RLFT", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "max π E y∼π [r c (x, y)] -βD KL (π, π c ) (8) s.t. y π(y|x, c)dy = 1", "eq_num": "(9)"}], "section": "B DERIVATION OF THE OPTIMAL POLICY IN C-RLFT", "sec_num": null}, {"text": "We can obtain the optimal solution of this constrained optimization problem by solving its <PERSON><PERSON><PERSON> (KKT) conditions. The Lagrangian of this problem is:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B DERIVATION OF THE OPTIMAL POLICY IN C-RLFT", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L(π, λ) = E y∼π [r c (x, y)] -βD KL (π, π c ) + λ(1 - y π(y|x, c)dy)", "eq_num": "(10)"}], "section": "B DERIVATION OF THE OPTIMAL POLICY IN C-RLFT", "sec_num": null}, {"text": "Following the KKT conditions, we take derivatives of L with respect to π and λ, and set them to zero:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B DERIVATION OF THE OPTIMAL POLICY IN C-RLFT", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∂L ∂π = r c (x, y) + β log π(y|x, c) -β log π c (y|x, c) + β -λ = 0 (11) ∂L ∂λ = 1 - y π(y|x, c)dy = 0", "eq_num": "(12)"}], "section": "B DERIVATION OF THE OPTIMAL POLICY IN C-RLFT", "sec_num": null}, {"text": "Solving these equations gives us the optimal policy π * :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B DERIVATION OF THE OPTIMAL POLICY IN C-RLFT", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "π * (y|x, c) = 1 Z(x, c) π c (y|x, c) exp 1 β r c (x, y) (13) Z(x, c) = y π c (y|x, c) exp 1 β r c (x, y) dy", "eq_num": "(14)"}], "section": "B DERIVATION OF THE OPTIMAL POLICY IN C-RLFT", "sec_num": null}, {"text": "where Z(x, c) is a normalization term ensuring that π * (y|x, c) is a valid probability distribution.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B DERIVATION OF THE OPTIMAL POLICY IN C-RLFT", "sec_num": null}, {"text": "This section provides the specifics of the benchmarks employed in our study:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C DETAILS OF BENCHMARKS", "sec_num": null}, {"text": "• AlpacaEval (<PERSON> et al., 2023) : This benchmark primarily assesses the model's ability to comprehend and execute user instructions. It incorporates a test set of 805 user instructions, collected from a diverse array of sources, and corresponding reference responses from text-davinci-003. • MT-bench (<PERSON> et al., 2023) : MT-bench presents a rigorous multi-turn benchmark designed to test both conversational and instruction-following capabilities. It includes 80 high-quality multiturn questions that span eight distinct topics: writing, roleplay, extraction, reasoning, mathematics, coding, knowledge I (STEM), and knowledge II (humanities/social science).", "cite_spans": [{"start": 13, "end": 30, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF17"}, {"start": 300, "end": 320, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF36"}], "ref_spans": [], "eq_spans": [], "section": "C DETAILS OF BENCHMARKS", "sec_num": null}, {"text": "• Vicuna-bench (<PERSON> et al., 2023) : This benchmark evaluates the proficiency of large language models across eight question categories: generic, knowledge, roleplay, commonsense, Fermi problems, counterfactual scenarios, coding, mathematics, and writing.", "cite_spans": [{"start": 15, "end": 36, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "C DETAILS OF BENCHMARKS", "sec_num": null}, {"text": "• AGIEval (<PERSON><PERSON> et al., 2023) : AGIEval is a collection of human-centric standardized tests aimed at gauging the problem-solving abilities of language models. We include all English multiplechoice tasks in our evaluation, which encompass general college admission tests (SAT, AQuA-RAT), law school admission tests (LSAT), and civil service exams (LogiQA).", "cite_spans": [{"start": 10, "end": 30, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF39"}], "ref_spans": [], "eq_spans": [], "section": "C DETAILS OF BENCHMARKS", "sec_num": null}, {"text": "Table 4 presents the detailed specifications of the models used in our study, including the base models, context length, finetuning methods, and datasets employed.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "4", "ref_id": null}], "eq_spans": [], "section": "D MODEL INFORMATION", "sec_num": null}, {"text": "Larger than 13b", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model Base Model Context Finetuning Data", "sec_num": null}, {"text": "gpt-4 - 8k SFT + RLFT - claude - 9k SFT + RLFT - gpt-3.5-turbo - 4k SFT + RLFT - llama-2-chat-70b llama-2-70b 4k SFT + RLFT ∼27k high-quality SFT data + ∼2.9M preference guanaco-65b llama-65b 2k SFT ∼9k OASST1 guanaco-33b llama-33b 2k SFT ∼9k OASST1 Equal to 13b vicuna-v1.1-13b llama-13b 2k SFT ∼70k ShareGPT wizardlm-v1.0-13b llama-13b 2k SFT ∼70k gpt-3.5-turbo ultralm-13b llama-13b 2k SFT ∼1.5M UltraChat llama-2-chat-13b llama-2-13b 4k SFT + RLFT ∼27k high-quality SFT data + ∼2.9M preference vicuna-v1.5-13b llama-2-13b 4k SFT ∼125k ShareGPT wizardlm-v1.2-13b llama-2-13b 4k SFT ∼250k gpt-3.5-turbo openchat-13b (ours) llama-2-13b 4k C-RLFT ∼70k ShareGPT", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model Base Model Context Finetuning Data", "sec_num": null}, {"text": "Table 4 : The details of the proposed OpenChat series models and other popular language models. The RLFT, SFT, and C-RLFT indicate reinforcement learning fine-tuning, supervised fine-tuning, and conditioned-RLFT proposed in our paper, respectively.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "4", "ref_id": null}], "eq_spans": [], "section": "Model Base Model Context Finetuning Data", "sec_num": null}, {"text": "Figure 8 : The consistency between GPT-3.5 and Claude-2 in AlpacaEval benchmark.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "8", "ref_id": null}], "eq_spans": [], "section": "E EVALUATORS CONSISTENCY", "sec_num": null}, {"text": "While all benchmarks evaluate the agreement between humans and automatic evaluators, we also consider the self-enhancement bias as discussed by <PERSON> et al. (2023) , where self-enhancement bias indicates that automatic evaluators may favor their own generated answers. To address this, we employ two additional automatic evaluators, gpt-3.5 and claude-2, alongside the official evaluator gpt-4, to verify the consistency of evaluators on AlpacaEval. The results between gpt-3.5 and claude-2 are shown in Fig. 9 , while the correlations between gpt-4 and others (r = 0. ", "cite_spans": [{"start": 144, "end": 163, "text": "<PERSON> et al. (2023)", "ref_id": "BIBREF36"}], "ref_spans": [{"start": 509, "end": 510, "text": "9", "ref_id": null}], "eq_spans": [], "section": "E EVALUATORS CONSISTENCY", "sec_num": null}, {"text": "Table 5 presents the comprehensive results of AGIEval performance. All models are assessed using the official AGIEval zero-shot prompt and answer matching as described in <PERSON><PERSON> et al. (2023) .", "cite_spans": [{"start": 171, "end": 190, "text": "<PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF39"}], "ref_spans": [{"start": 6, "end": 7, "text": "5", "ref_id": "TABREF3"}], "eq_spans": [], "section": "F AGIEVAL RESULTS", "sec_num": null}, {"text": "For conversation models (excluding llama-2-13b), we utilize the corresponding conversation templates and set the zero-shot prompt as the user's question. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F AGIEVAL RESULTS", "sec_num": null}, {"text": "We employ gpt-4 to automatically score the quality of conversations in the ShareGPT dataset on a scale of 1-10. For multi-turn conversations, each assistant's response is scored independently, taking into account the context of previous turns. The conversation's score is the average of all turn scores. The scoring prompt template is shown in Table 6 .", "cite_spans": [], "ref_spans": [{"start": 350, "end": 351, "text": "6", "ref_id": "TABREF5"}], "eq_spans": [], "section": "G CONVERSATION QUALITY SCORING", "sec_num": null}, {"text": "Please act as an impartial judge and evaluate the quality of the response provided by an AI assistant to the conversation displayed below. Your evaluation should consider factors such as the helpfulness, relevance, accuracy, depth, creativity, and level of detail of the response. Begin your evaluation by providing a short explanation. Be as objective as possible. After providing your explanation, you must rate the response on a scale of 1 to 10 by strictly following this format: \"[[rating]]\", for example: \"Rating: [[5] ]\".", "cite_spans": [{"start": 520, "end": 524, "text": "[[5]", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Scoring prompt template [Instruction]", "sec_num": null}, {"text": "[ ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Scoring prompt template [Instruction]", "sec_num": null}, {"text": "The correlations between arbitrary two models among gpt-4, gpt-3.5 and claude-2 are shown in Fig. 9 .", "cite_spans": [], "ref_spans": [{"start": 98, "end": 99, "text": "9", "ref_id": null}], "eq_spans": [], "section": "H EVALUATORS CONSISTENCY", "sec_num": null}, {"text": "Figure 9 : The consistency of three evaluators, GPT-4, GPT-3.5, Claude-2 in AlpacaEval benchmark.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "9", "ref_id": null}], "eq_spans": [], "section": "H EVALUATORS CONSISTENCY", "sec_num": null}, {"text": "vicuna-13b-v1.5 [Turn #1] Craft an intriguing opening paragraph for a fictional short story. The story should involve a character who wakes up one morning to find that they can time travel.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "I CASE STUDY", "sec_num": null}, {"text": "As the sun began to rise, <PERSON> stirred in her bed, the sound of birds chirping outside her window. But as she opened her eyes, she was not greeted by the familiar sight of her bedroom. Instead, she found herself standing in the middle of a bustling city street, the sound of cars and people rushing past her. Confused and disoriented, she looked around, trying to make sense of where she was and how she got there. And then she saw it -a shimmering aura around her, and a faint buzzing in her ears. It was then that she realized she had time traveled.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "I CASE STUDY", "sec_num": null}, {"text": "[Turn #2] Summarize the story with three bullet points using only nouns and adjectives, without verbs. * <PERSON> wakes up in a bustling city street * She discovers she has time traveled * She is disoriented and confused ultralm-13b [Turn #1] Craft an intriguing opening paragraph for a fictional short story. The story should involve a character who wakes up one morning to find that they can time travel.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "I CASE STUDY", "sec_num": null}, {"text": "It was a typical morning when the alarm screeched through the room, jolting the sleeper awake. As they rubbed their eyes and reached for their glasses, they noticed something peculiar. The clock on the bedside table had stopped ticking. Yet, the room was bathed in a soft, golden light, and the air felt different. Suddenly, the sleeper felt a strange sensation in their chest, and before they knew it, they were hurtling through time.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "I CASE STUDY", "sec_num": null}, {"text": "[Turn #2] Summarize the story with three bullet points using only nouns and adjectives, without verbs. As I lay in bed, I felt a strange sensation wash over me, like the whisper of a breeze on a summer day. I opened my eyes to find myself in a world that was both familiar and yet, utterly foreign. The clock on my nightstand read 6:45 AM, but something felt off. The light pouring through the window seemed warmer, more golden than it should be. And then, I felt it -the sudden jolt of time travel. I blinked, and when I opened my eyes again, I was somewhere else entirely. The world outside my window was not the one I had left just moments before. I sat up, rubbing my eyes, trying to make sense of the strange sensation that had washed over me. How had I traveled through time? And where was I now? The possibilities were endless, and I couldn't wait to explore this new world and all its wonders.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "I CASE STUDY", "sec_num": null}, {"text": "[Turn #2] Summarize the story with three bullet points using only nouns and adjectives, without verbs. Sure, here are three bullet points summarizing the story using only nouns and adjectives, without verbs: * Character wakes up with strange sensation * Golden light pours in through window * Unfamiliar world outside with endless possibilities openchat-13b [Turn #1] Craft an intriguing opening paragraph for a fictional short story. The story should involve a character who wakes up one morning to find that they can time travel.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "I CASE STUDY", "sec_num": null}, {"text": "As the first rays of sunlight peeked through the window, <PERSON> closed her eyes, relishing the warmth that caressed her face. She never could resist the allure of a beautiful morning, but this particular day was about to become anything but ordinary. With a start, she sat up in bed, her heart pounding, as she realized she was somewhere else, somewhere she had never been before. Confused and exhilarated, <PERSON> discovered that she had the extraordinary ability to time travel, her very first morning blessed -or perhaps cursed -with the power to traverse the fabric of time.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "I CASE STUDY", "sec_num": null}, {"text": "[Turn #2] Summarize the story with three bullet points using only nouns and adjectives, without verbs. *<PERSON>, a time traveler *Mysterious morning adventure *Discovery of extraordinary ability Question How long will it take to walk around the world, assuming that would be possible?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "I CASE STUDY", "sec_num": null}, {"text": "vicuna-13b-v1.5 It is not possible to walk around the world, as the distance around the globe is much greater than the distance across it. The circumference of the Earth at the equator is approximately 40,075 kilometers (24,901 miles), while the diameter is about 12,742 kilometers (7,918 miles).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "I CASE STUDY", "sec_num": null}, {"text": "Assuming you could walk continuously without rest, it would take approximately 1,900 years to walk around the world at an average walking speed of 5 kilometers (3.1 miles) per hour. However, this is just an estimate and does not take into account factors such as the terrain, altitude, and other conditions that would affect walking speed.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "I CASE STUDY", "sec_num": null}, {"text": "Walking around the world is not possible as the Earth is not a perfect sphere and has different land masses making it impossible to walk continuously without gaps. However, if we assume that there were no land masses and the Earth was a perfect sphere, it would take approximately 43,288.89 miles (69,617.05 kilometers) to walk around the equator. If we were to walk around the circumference of the Earth, it would take approximately 24,901.26 miles (40,074.91 kilometers). However, if we were to walk around the world in a straight line, it would take approximately 24,859.82 miles (40,008.06 kilometers) as the Earth is not a perfect sphere and has an elliptical shape. These distances are based on the equator circumference and the circumference of the Earth, respectively. The time it would take to walk around the world would depend on the individual's walking speed and other factors such as stops for rest and food.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ultralm-13b", "sec_num": null}, {"text": "wizardlm-13b-v1.2 As an AI, I'm assuming you're asking about walking around the Earth's equator. The distance around the Earth's equator at the equator itself is about 40,075 kilometers (24,901 miles). However, the actual distance would be much greater if you were to walk along a route that follows coastlines, navigates through mountains, and crosses other obstacles. Now, let's consider that a person walks at a moderate pace of about 5 kilometers per hour. Walking around the world would then take approximately 8,015 hours or approximately 333,600 minutes.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ultralm-13b", "sec_num": null}, {"text": "To convert this into days, you would divide the total number of minutes by 24 (hours in a day): 333,600 minutes / 24 = 14,000 days.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ultralm-13b", "sec_num": null}, {"text": "So, if you were to walk around the world, it would take you approximately 14,000 days or approximately 38.89 years, assuming you walk continuously without rest and no obstacles. This is an estimate based on the distance around the Earth's equator and does not take into account real-world challenges like mountains, oceans, and other obstacles.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ultralm-13b", "sec_num": null}, {"text": "Thank you for your question! While it is not possible to walk around the entire world, as there are many areas that are not accessible by foot, such as oceans and other bodies of water, it's important to note that even if it were possible, it would be a very long and challenging journey.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "llama-2-chat-13b", "sec_num": null}, {"text": "The circumference of the Earth at the equator is approximately 40,075 kilometers (24,901 miles). However, it's important to consider that walking is not a viable mode of transportation for long distances, as it would be extremely time-consuming and would require a tremendous amount of physical endurance.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "llama-2-chat-13b", "sec_num": null}, {"text": "Furthermore, it's important to note that walking through certain areas of the world, such as deserts or other remote regions, could be dangerous due to harsh weather conditions, lack of resources, and other hazards.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "llama-2-chat-13b", "sec_num": null}, {"text": "Therefore, it's not possible to provide an accurate estimate of how long it would take to walk around the world, as it is not a feasible or safe journey. It's important to prioritize safe and accessible forms of transportation when traveling, and to plan your journeys with careful consideration of your physical limitations and the resources available to you. Is there anything else I can help with?", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "llama-2-chat-13b", "sec_num": null}, {"text": "It is possible to walk around the world, though it would be a continuous, grueling journey that would take several years, if not a decade or more, depending on the route chosen, individual physical capabilities, and other factors. The distance around the world varies depending on the route, but a commonly used measure is the equatorial circumference of the Earth, which is approximately 24,901.48 miles (40,075.01 kilometers).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "openchat-13b", "sec_num": null}, {"text": "Walking at a moderate pace of around 3 miles (4.8 kilometers) per hour, it would take an average person nearly 3 years and 3 months to walk around the world (assuming no breaks or rest days). However, this is just an estimate, and the actual time required would depend on various factors, such as:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "openchat-13b", "sec_num": null}, {"text": "1. Route: The distance around the world varies depending on the route taken. For example, the shortest distance is along the Equator, while the longest distance is along the meridians of longitude. 2. Terrain: Walking through mountainous or heavily forested areas may be slower and more challenging than walking on flat terrain. 3. Weather: Adverse weather conditions, such as extreme heat or cold, high winds, or heavy rainfall, can make walking more difficult and slow down progress. 4. Fitness and endurance: An individual's physical fitness, endurance, and ability to withstand the rigors of constant walking will affect their pace and overall journey time. 5. Nutrition and rest: Proper nutrition and adequate rest are essential for maintaining physical and mental stamina. Taking regular breaks and rest days will prolong the journey.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "openchat-13b", "sec_num": null}, {"text": "Keep in mind that walking around the world is an extraordinary feat that would require extensive preparation, both physically and logistically. It's essential to consider the many challenges and potential hazards involved before embarking on such an adventure.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "openchat-13b", "sec_num": null}, {"text": "Table 8 : Comparison of models on AlpacaEval benchmark. Specifically, the models ultralm-13b and llama-2-chat-13b fail to provide a direct answer, while vicuna-13b-v1.5 and wizardlm-13b-v1.2 offer estimates with significant discrepancies. openchat-13b provides a reasonable estimate, demonstrating the varying capabilities of models in addressing complex hypothetical questions.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "8", "ref_id": null}], "eq_spans": [], "section": "openchat-13b", "sec_num": null}, {"text": "The ShareGPT dataset is collected from https://sharegpt.com/.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "The work is supported by the National Key R&D Program of China (2022ZD0160502) and the National Natural Science Foundation of China (No.61925601). Furthermore, we would like to thank <PERSON><PERSON> in GPT Desk Pte. Ltd., <PERSON><PERSON> at Tsinghua University, Baochang Ma, and <PERSON><PERSON> Wan in 01.AI company for their resource support. We would like to express our gratitude to <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> at Tsinghua University for their valuable discussion. Finally, we are also grateful to the developers of the following projects, which have contributed significantly to our research: Llama, self-instruct, FastChat (Vicuna), Alpaca, and StarCoder.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACKNOWLEDGMENTS", "sec_num": null}, {"text": "Given that f (x) = 5x 3 -2x + 3, find the value of f(2).vicuna-13b-v1.5To find the value of f(2), we simply substitute 2 for x in the expression for f(x):Simplifying this expression, we get:Therefore, the value of f(2) is 24.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Question", "sec_num": null}, {"text": "To find the value of f(2), we substitute 2 for x in the function f(x):Therefore, the value of f( 2) is 25.The problem is asking us to find the value of the function f(2). The function is given as f (x) = 5x 3 -2x+3.Step 1: Substitute the value of x into the function We are told to substitute the value of x with 2. So, we replace every x in the function with 2. This gives us:Step 2: Simplify the expression Next, we simplify the expression. The expression 5(2) 3 simplifies to 5*8, which equals 40. The expression 2(2) simplifies to 4. And the number 3 remains the same. So, we have:Step 3: Perform the subtraction and addition Now, we perform the subtraction and addition in the correct order. First, we subtract 4 from 40, which gives us 36. Then, we add 3 to 36, which gives us 39. So, we have:So, the value of f(2) is 39.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ultralm-13b", "sec_num": null}, {"text": "Sure! To find the value of f(2), we can plug 2 into the function f (x) = 5x 3 -2x + 3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "llama-2-chat-13b", "sec_num": null}, {"text": "To find the value of f(2), we need to evaluate the function whenTable 9 : Comparison of models on Vicuna-bench.Only openchat-13b and wizardlm-13b-v1.2 provide the correct answer to this math problem.", "cite_spans": [], "ref_spans": [{"start": 70, "end": 71, "text": "9", "ref_id": null}], "eq_spans": [], "section": "openchat-13b", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Training a helpful and harmless assistant with reinforcement learning from human feedback", "authors": [{"first": "Yun<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Nova", "middle": [], "last": "Das<PERSON><PERSON>", "suffix": ""}, {"first": "Dawn", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Fort", "suffix": ""}, {"first": "Deep", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2204.05862"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Training a helpful and harmless assistant with reinforcement learning from human feedback. arXiv preprint arXiv:2204.05862, 2022a.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Constitutional ai: Harmlessness from ai feedback", "authors": [{"first": "Yun<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kund<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Kernion", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Azalia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2212.08073"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Constitutional ai: Harm- lessness from ai feedback. arXiv preprint arXiv:2212.08073, 2022b.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Open problems and fundamental limitations of reinforcement learning from human feedback", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>ander", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON><PERSON><PERSON>"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Scheurer", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>dman", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.15217"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Open problems and fundamental limitations of reinforcement learning from human feedback. arXiv preprint arXiv:2307.15217, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Training a better alpaca with fewer data", "authors": [{"first": "Lichang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shiyang", "middle": [], "last": "Li", "suffix": ""}, {"first": "Jun", "middle": [], "last": "Yan", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Heng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.08701"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. Alpagasus: Training a better alpaca with fewer data. arXiv preprint arXiv:2307.08701, 2023.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Decision transformer: Reinforcement learning via sequence modeling", "authors": [{"first": "<PERSON>i", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "Advances in neural information processing systems", "volume": "34", "issue": "", "pages": "15084--15097", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Decision transformer: Reinforcement learning via sequence modeling. Advances in neural information processing systems, 34:15084-15097, 2021.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Vicuna: An open-source chatbot impressing gpt-4 with 90%* chatgpt quality", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Zhang<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Siyuan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Yonghao", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stoica", "suffix": ""}, {"first": "<PERSON>", "middle": ["P"], "last": "Xi<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Vicuna: An open-source chatbot impressing gpt-4 with 90%* chatgpt quality, March 2023. URL https: //lmsys.org/blog/2023-03-30-vicuna/.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Scaling language modeling with pathways", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Chowdhery", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ma<PERSON>n", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["Won"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2204.02311"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Palm: Scaling language modeling with pathways. arXiv preprint arXiv:2204.02311, 2022.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Deep reinforcement learning from human preferences. Advances neural information processing systems", "authors": [{"first": "Jan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Deep reinforcement learning from human preferences. Advances neural information processing sys- tems, 30, 2017.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Efficient finetuning of quantized llms", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ari", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Qlora", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.14314"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Qlora: Efficient finetuning of quantized llms. arXiv preprint arXiv:2305.14314, 2023.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Enhancing chat language models by scaling high-quality instructional conversations", "authors": [{"first": "<PERSON>ng", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "Yulin", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Bokai", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Qin", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "Zhiyuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Maosong", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.14233"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Enhancing chat language models by scaling high-quality instructional conversations. arXiv preprint arXiv:2305.14233, 2023.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Rvs: What is essential for offline rl via supervised learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Eysenbach", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2112.10751"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Rvs: What is essential for offline rl via supervised learning? arXiv preprint arXiv:2112.10751, 2021.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Koala: A dialogue model for academic research", "authors": [{"first": "Xinyang", "middle": [], "last": "Geng", "suffix": ""}, {"first": "Arnav", "middle": [], "last": "Gudibande", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dawn", "middle": [], "last": "Song", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Koala: A dialogue model for academic research. Blog post, April 2023. URL https: //bair.berkeley.edu/blog/2023/04/03/koala/.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Learning rates as a function of batch size: A random matrix theory approach to neural network training", "authors": [{"first": "Diego", "middle": [], "last": "Granziol", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "J. <PERSON>. Learn. Res", "volume": "23", "issue": "", "pages": "1--173", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Learning rates as a function of batch size: A random matrix theory approach to neural network training. J. Mach. Learn. Res., 23:173:1- 173:65, 2020. URL https://api.semanticscholar.org/CorpusID:226281826.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Training compute-optimal large language models", "authors": [{"first": "Jordan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Buchatskaya", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Cai", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Diego", "middle": [], "last": "De Las", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "Casas", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2203.15556"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Train- ing compute-optimal large language models. arXiv preprint arXiv:2203.15556, 2022.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Way off-policy batch deep reinforcement learning of implicit human preferences in dialog", "authors": [{"first": "<PERSON>", "middle": [], "last": "Jaques", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON><PERSON>"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Agata", "middle": [], "last": "Lapedriza", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Shi<PERSON><PERSON>", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Picard", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1907.00456"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Way off-policy batch deep reinforcement learning of implicit human preferences in dialog. arXiv preprint arXiv:1907.00456, 2019.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "RL with KL penalties is better viewed as Bayesian inference", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Findings of the Association for Computational Linguistics: EMNLP 2022", "volume": "", "issue": "", "pages": "1083--1091", "other_ids": {"DOI": ["10.18653/v1/2022.findings-emnlp.77"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON> with KL penalties is better viewed as Bayesian inference. In Findings of the Association for Computational Linguistics: EMNLP 2022, pp. 1083-1091, Abu Dhabi, United Arab Emirates, December 2022. Association for Computational Linguistics. doi: 10.18653/v1/2022.findings-emnlp.77. URL https:// aclanthology.org/2022.findings-emnlp.77.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Offline reinforcement learning: Tutorial, review, and perspectives on open problems", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Aviral", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2005.01643"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Offline reinforcement learning: Tuto- rial, review, and perspectives on open problems. arXiv preprint arXiv:2005.01643, 2020.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Alpacaeval: An automatic evaluator of instruction-following models", "authors": [{"first": "Xuechen", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["B"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>. Alpacaeval: An automatic evaluator of instruction-following models. https://github.com/tatsu-lab/alpaca_eval, 2023.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Umap: Uniform manifold approximation and projection", "authors": [{"first": "Leland", "middle": [], "last": "Mcinnes", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "The Journal of Open Source Software", "volume": "3", "issue": "29", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Umap: Uniform manifold approximation and projection. The Journal of Open Source Software, 3(29):861, 2018.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Awac: Accelerating online reinforcement learning with offline datasets", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2006.09359"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Awac: Accelerating online rein- forcement learning with offline datasets. arXiv preprint arXiv:2006.09359, 2020.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Training language models to follow instructions with human feedback", "authors": [{"first": "<PERSON>", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Almeida", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Katarina", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hilton", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Training language models to follow instructions with human feedback, 2022.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "The refinedweb dataset for falcon llm: outperforming curated corpora with web data, and web data only", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Penedo", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Malartic", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ham<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Almaz<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Launay", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.01116"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. The refinedweb dataset for falcon llm: outperforming curated corpora with web data, and web data only. arXiv preprint arXiv:2306.01116, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Instruction tuning with gpt-4", "authors": [{"first": "Baolin", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Chunyuan", "middle": [], "last": "Li", "suffix": ""}, {"first": "Pengcheng", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Galley", "suffix": ""}, {"first": "Ji<PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.03277"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Instruction tuning with gpt-4. arXiv preprint arXiv:2304.03277, 2023.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Advantage-weighted regression: Simple and scalable off-policy reinforcement learning", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Aviral", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1910.00177"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Advantage-weighted regression: Simple and scalable off-policy reinforcement learning. arXiv preprint arXiv:1910.00177, 2019.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Reinforcement learning by reward-weighted regression for operational space control", "authors": [{"first": "Jan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2007, "venue": "Proceedings of the 24th international conference on Machine learning", "volume": "", "issue": "", "pages": "745--750", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Reinforcement learning by reward-weighted regression for operational space control. In Proceedings of the 24th international conference on Machine learning, pp. 745- 750, 2007.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Direct preference optimization: Your language model is secretly a reward model", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Archit", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "Chelsea", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.18290"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Direct preference optimization: Your language model is secretly a reward model. arXiv preprint arXiv:2305.18290, 2023.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Stanford alpaca: An instruction-following llama model", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xuechen", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["B"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>. Stanford alpaca: An instruction-following llama model. https://github.com/tatsu-lab/stanford_alpaca, 2023.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Llama: Open and efficient foundation language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "I<PERSON><PERSON>", "suffix": ""}, {"first": "Xavier", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Timothée", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>yal", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hambro", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Azhar", "suffix": ""}, {"first": "Au<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Grave", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Llama: Open and efficient foundation language models, 2023a.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Llama 2: Open foundation and fine-tuned chat models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Louis", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "Stone", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Batra", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Shruti", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "B<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Cantón", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Guillem", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Esiobu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Vedanuj", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["S"], "last": "<PERSON>yal", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": ["V"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Punit", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ying<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xavier", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Todor", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Molybog", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Kalyan", "middle": [], "last": "Rungt<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Saladi", "suffix": ""}, {"first": "<PERSON>uan", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Subrama<PERSON>", "suffix": ""}, {"first": "Binh", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>in", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Yan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Zarov", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Fan", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kambadur", "suffix": ""}, {"first": "Au<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Sc<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Llama 2: Open foundation and fine-tuned chat models. ArXiv, abs/2307.09288, 2023b. URL https://api.semantic<PERSON>olar.org/ CorpusID:259950998.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Self-instruct: Aligning language model with self generated instructions", "authors": [{"first": "Yizhong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2212.10560"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Self-instruct: Aligning language model with self generated instructions. arXiv preprint arXiv:2212.10560, 2022.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Fine-grained human feedback gives better rewards for language model training", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ammanabrolu", "suffix": ""}, {"first": "<PERSON>", "middle": ["A"], "last": "<PERSON>", "suffix": ""}, {"first": "Mari", "middle": [], "last": "Ostendorf", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.01693"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Fine-grained human feedback gives better rewards for language model training. arXiv preprint arXiv:2306.01693, 2023.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "bert-as-service", "authors": [{"first": "Han", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. bert-as-service. https://github.com/hanxiao/bert-as-service, 2018.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Empowering large language models to follow complex instructions", "authors": [{"first": "Can", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qingfeng", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xi<PERSON><PERSON>", "middle": [], "last": "Geng", "suffix": ""}, {"first": "P<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chongyang", "middle": [], "last": "Tao", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.12244"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Wizardlm: Empowering large language models to follow complex instructions. arXiv preprint arXiv:2304.12244, 2023a.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Baize: An open-source chat model with parameter-efficient tuning on self-chat data", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.01196"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Baize: An open-source chat model with parameter-efficient tuning on self-chat data. arXiv preprint arXiv:2304.01196, 2023b.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Discriminator-weighted offline imitation learning from suboptimal demonstrations", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xianyuan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Huiling", "middle": [], "last": "Qin", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "24725--24742", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Discriminator-weighted offline imitation learning from suboptimal demonstrations. In International Conference on Machine Learning, pp. 24725-24742. PMLR, 2022.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Rrhf: Rank responses to align language models with human feedback without tears", "authors": [{"first": "<PERSON>", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "Hongyi", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>fang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Rrhf: Rank responses to align language models with human feedback without tears. ArXiv, abs/2304.05302, 2023. URL https://api.semanticscholar.org/CorpusID: 258059818.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "A survey of large language models", "authors": [{"first": "Kun", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yupeng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Beichen", "middle": [], "last": "Min", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zican", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.18223"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. A survey of large language models. arXiv preprint arXiv:2303.18223, 2023.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Judging llm-as-a-judge with mt-bench and chatbot arena", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Siyuan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Zhang<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yonghao", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Dacheng", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Xi<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2306.05685"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. Judging llm-as-a-judge with mt-bench and chatbot arena. arXiv preprint arXiv:2306.05685, 2023.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Agieval: A human-centric benchmark for evaluating foundation models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Weizhu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.06364"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Agieval: A human-centric benchmark for evaluating foundation models. arXiv preprint arXiv:2304.06364, 2023.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Less is more for alignment", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>in", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Avia", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>i", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.11206"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, et al. Lima: Less is more for alignment. arXiv preprint arXiv:2305.11206, 2023.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "num": null, "fig_num": null, "uris": null, "text": "using zero-shot settings, which presents a collection of humancentric standardized exams. More details of the benchmarks are listed in App. C.Baselines. We evaluate the most popular API-based and open-source LLMs: (1) gpt-4 (Ope-nAI, 2023) and gpt-3.5-turbo(<PERSON><PERSON><PERSON> et al., 2022) are highly advanced LLMs developed by OpenAI; (2) c<PERSON><PERSON>(<PERSON> et al., 2022a)  is helpful and harmless assistants by <PERSON>throp<PERSON>; (3) llama-2-chat(<PERSON><PERSON><PERSON><PERSON> et al., 2023b)  series models are the most frequently used open-source models with SFT and RLHF; (4) wizardlm(<PERSON> et al., 2023a), guanaco(Dettmers et al., 2023), ultralm(<PERSON><PERSON> et al., 2023) and vicuna(<PERSON> et al., 2023) are among the well-known opensource LLMs with SFT. More details are shown in App. D."}, "FIGREF1": {"type_str": "figure", "num": null, "fig_num": "2", "uris": null, "text": "Figure 2: The MT-bench score (a) and AGIEval Accuracy (b) of the proposed openchat-13b and other popular open-source language models, where the detailed performance of AGIEval in App. F."}, "FIGREF2": {"type_str": "figure", "num": null, "fig_num": null, "uris": null, "text": ", are consequently mapped to 2-D space using UMAP(<PERSON><PERSON><PERSON><PERSON> et al., 2018). Multiple clusters are observed in both only SFT and openchat models, likely due to the diverse conversation domains in the ShareGPT dataset. More importantly, the GPT-4 and GPT-3.5 conversation representations in only SFT were intermingled. In contrast, openchat-13b clearly distinguished these representations according to their data sources, demonstrating the efficacy of our proposed C-RLFT in enriching input information. (a) only SFT (b) openchat-13b Figure 4: Visualization of GPT-4 and GPT-3.5 conversations' representations in only SFT and openchat-13b."}, "FIGREF3": {"type_str": "figure", "num": null, "fig_num": "5", "uris": null, "text": "Figure 5: Effects of class-conditioned prompt tokens during inference phase."}, "FIGREF4": {"type_str": "figure", "num": null, "fig_num": "6", "uris": null, "text": "Figure 6: Effects of subsampling a specific class of data."}, "FIGREF5": {"type_str": "figure", "num": null, "fig_num": "7", "uris": null, "text": "Figure 7: Effects of class-conditioned prompt tokens during training phase."}, "FIGREF6": {"type_str": "figure", "num": null, "fig_num": null, "uris": null, "text": "79 and 0.73) are detailed in App. H. The model performances evaluated by both evaluators show a strong Pearson correlation of r = 0.91. Most importantly, regardless of the automatic evaluator used, our openchat-13b outperforms all other 13b open-source language models, ranking within the top three among all API-based and open-source language models."}, "TABREF0": {"type_str": "table", "html": null, "num": null, "content": "<table><tr><td>Models</td><td>Base Models</td><td>Method</td><td colspan=\"4\">AlpacaEval MT-bench Vicuna-bench Average</td></tr><tr><td/><td/><td colspan=\"2\">Larger than 13b</td><td/><td/><td/></tr><tr><td>gpt-4</td><td>-</td><td>SFT + RLFT</td><td>95.3</td><td>82.5</td><td>90.0</td><td>89.3</td></tr><tr><td>llama-2-chat-70b</td><td colspan=\"2\">llama-2-70b SFT + RLFT</td><td>92.7</td><td>60.0</td><td>87.5</td><td>80.1</td></tr><tr><td>claude</td><td>-</td><td>SFT + RLFT</td><td>88.4</td><td>65.0</td><td>76.3</td><td>76.6</td></tr><tr><td>gpt-3.5-turbo</td><td>-</td><td>SFT + RLFT</td><td>86.1</td><td>50.0</td><td>50.0</td><td>62.0</td></tr><tr><td>guanaco-65b</td><td>llama-65b</td><td>SFT</td><td>71.8</td><td>40.6</td><td>49.4</td><td>53.9</td></tr><tr><td>guanaco-33b</td><td>llama-33b</td><td>SFT</td><td>66.0</td><td>40.6</td><td>54.4</td><td>53.7</td></tr><tr><td/><td/><td colspan=\"2\">Equal to 13b</td><td/><td/><td/></tr><tr><td>vicuna-v1.1-13b</td><td>llama-13b</td><td>SFT</td><td>70.4</td><td>29.4</td><td>45.0</td><td>48.3</td></tr><tr><td colspan=\"2\">wizardlm-v1.0-13b llama-13b</td><td>SFT</td><td>75.3</td><td>33.1</td><td>44.4</td><td>50.9</td></tr><tr><td>vicuna-v1.5-13b</td><td colspan=\"2\">llama-2-13b SFT</td><td>78.8</td><td>37.2</td><td>47.1</td><td>54.4</td></tr><tr><td>ultralm-13b</td><td>llama-13b</td><td>SFT</td><td>80.6</td><td>37.2</td><td>50.0</td><td>55.9</td></tr><tr><td colspan=\"3\">wizardlm-v1.2-13b llama-2-13b SFT</td><td>89.2</td><td>53.1</td><td>80.6</td><td>74.3</td></tr><tr><td>llama-2-chat-13b</td><td colspan=\"2\">llama-2-13b SFT + RLFT</td><td>81.1</td><td>55.3</td><td>86.9</td><td>74.4</td></tr><tr><td>openchat-13b</td><td colspan=\"2\">llama-2-13b C-RLFT</td><td>89.5</td><td>57.5</td><td>85.0</td><td>77.3</td></tr></table>", "text": "The win-rate (%) performance of the proposed openchat-13b and other popular open-source language models. The competitors are text-davinci-003 in AlpacaEval, and gpt-3.5-turbo in both MT-bench and Vicuna-bench. The bold scores denote the best performance, and the underline scores indicate the second-best performance."}, "TABREF2": {"type_str": "table", "html": null, "num": null, "content": "<table/>", "text": "The attempted conditioned prompts during the training phase."}, "TABREF3": {"type_str": "table", "html": null, "num": null, "content": "<table><tr><td>Task</td><td colspan=\"5\">openchat-13b llama-2-13b wizardlm-v1.2-13b llama-2-chat-13b vicuna-v1.5-13b</td></tr><tr><td>AQuA-RAT</td><td>19.3</td><td>20.1</td><td>28.3</td><td>22.4</td><td>21.7</td></tr><tr><td>LogiQA</td><td>34.9</td><td>32.6</td><td>27.2</td><td>27.3</td><td>19.0</td></tr><tr><td>LSAT-AR</td><td>19.1</td><td>21.3</td><td>21.7</td><td>19.6</td><td>20.0</td></tr><tr><td>LSAT-LR</td><td>37.5</td><td>33.3</td><td>32.4</td><td>27.6</td><td>16.7</td></tr><tr><td>LSAT-RC</td><td>45.0</td><td>46.1</td><td>43.5</td><td>25.3</td><td>19.0</td></tr><tr><td>SAT-English (w/o Psg.)</td><td>44.7</td><td>41.3</td><td>33.5</td><td>25.2</td><td>26.7</td></tr><tr><td>SAT-English</td><td>66.0</td><td>65.5</td><td>65.5</td><td>25.7</td><td>28.2</td></tr><tr><td>SAT-Math</td><td>30.9</td><td>29.1</td><td>22.7</td><td>28.6</td><td>24.5</td></tr><tr><td>Average</td><td>36.4</td><td>35.0</td><td>32.8</td><td>25.8</td><td>20.7</td></tr></table>", "text": "Zero-shot performance on AGIEval benchmark."}, "TABREF5": {"type_str": "table", "html": null, "num": null, "content": "<table/>", "text": "Conversation evaluation prompts."}, "TABREF6": {"type_str": "table", "html": null, "num": null, "content": "<table><tr><td/><td>Time Traveler:</td></tr><tr><td/><td>-Unexpectedly travels through time</td></tr><tr><td/><td>-Struggles to control powers</td></tr><tr><td/><td>-Encounters historical figures and events</td></tr><tr><td colspan=\"2\">wizardlm-13b-v1.2 [Turn #1] [Turn #2] Summarize the story with three bullet points using only nouns and adjectives, without</td></tr><tr><td/><td>verbs.</td></tr><tr><td/><td>* Character: John</td></tr><tr><td/><td>* Time Travel</td></tr><tr><td/><td>* Doctor Who</td></tr><tr><td/><td>* Sunlight</td></tr><tr><td/><td>* Room</td></tr><tr><td/><td>* Day</td></tr><tr><td>llama-2-chat-13b</td><td>[Turn #1] Craft an intriguing opening paragraph for a fictional short story. The story should involve</td></tr><tr><td/><td>a character who wakes up one morning to find that they can time travel.</td></tr></table>", "text": "Craft an intriguing opening paragraph for a fictional short story. The story should involve a character who wakes up one morning to find that they can time travel. It was just another ordinary day, a day like any other, or so it seemed. But for <PERSON>, it was about to become the most extraordinary day of his life. As he groggily opened his eyes, he was hit with a wave of dizziness, a feeling he had never experienced before. Confused and disoriented, he rubbed his eyes, hoping it was just a side effect of his late night binge-watching of Doctor Who. But as he looked around, he realized that something was very wrong. His room seemed different, and the sunlight streaming in through the window was at an angle he had never seen before. It was then that he realized he had somehow managed to time travel."}, "TABREF7": {"type_str": "table", "html": null, "num": null, "content": "<table/>", "text": ""}}}}