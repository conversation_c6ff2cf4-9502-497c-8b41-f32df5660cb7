{"paper_id": "PASTA", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:47:46.049816Z"}, "title": "TELL YOUR MODEL WHERE TO ATTEND: POST-HOC ATTENTION STEERING FOR LLMS", "authors": [{"first": "Qingru", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of California", "location": {"settlement": "Berkeley"}}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": "<EMAIL>"}, {"first": "Liyuan", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": "<EMAIL>"}, {"first": "Xiaodong", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Ji<PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": "", "affiliation": {}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "University of California", "location": {"settlement": "Berkeley"}}, "email": "<EMAIL>"}], "year": "", "venue": null, "identifiers": {}, "abstract": "In human-written articles, we often leverage the subtleties of text style, such as bold and italics, to guide the attention of readers. These textual emphases are vital for the readers to grasp the conveyed information. When interacting with large language models (LLMs), we have a similar need -steering the model to pay closer attention to user-specified information, e.g., an instruction. Existing methods, however, are constrained to process plain text and do not support such a mechanism. This motivates us to introduce PASTA -Post-hoc Attention STeering Approach, a method that allows LLMs to read text with user-specified emphasis marks. To this end, PASTA identifies a small subset of attention heads and applies precise attention reweighting on them, directing the model attention to user-specified parts. Like prompting, PASTA is applied at inference time and does not require changing any model parameters. Experiments demonstrate that PASTA can substantially enhance an LLM's ability to follow user instructions or integrate new knowledge from user inputs, leading to a significant performance improvement on a variety of tasks, e.g., an average accuracy improvement of 22% for LLAMA-7B. Our code is publicly available at https://github.com/QingruZhang/PASTA. * Work is done during <PERSON><PERSON>'s internship at Microsoft Research. 1 We use prompts to refer to all LLM text inputs, including user instructions, and the other background information (which we refer to as context).", "pdf_parse": {"paper_id": "PASTA", "_pdf_hash": "", "abstract": [{"text": "In human-written articles, we often leverage the subtleties of text style, such as bold and italics, to guide the attention of readers. These textual emphases are vital for the readers to grasp the conveyed information. When interacting with large language models (LLMs), we have a similar need -steering the model to pay closer attention to user-specified information, e.g., an instruction. Existing methods, however, are constrained to process plain text and do not support such a mechanism. This motivates us to introduce PASTA -Post-hoc Attention STeering Approach, a method that allows LLMs to read text with user-specified emphasis marks. To this end, PASTA identifies a small subset of attention heads and applies precise attention reweighting on them, directing the model attention to user-specified parts. Like prompting, PASTA is applied at inference time and does not require changing any model parameters. Experiments demonstrate that PASTA can substantially enhance an LLM's ability to follow user instructions or integrate new knowledge from user inputs, leading to a significant performance improvement on a variety of tasks, e.g., an average accuracy improvement of 22% for LLAMA-7B. Our code is publicly available at https://github.com/QingruZhang/PASTA. * Work is done during <PERSON><PERSON>'s internship at Microsoft Research. 1 We use prompts to refer to all LLM text inputs, including user instructions, and the other background information (which we refer to as context).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "The advent of large language models (LLMs) has marked a significant milestone in natural language processing (NLP) and artificial intelligence (AI), showcasing exceptional performance across a wide range of tasks (<PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON> et al., 2020a; OpenAI, 2023) . Efforts to further refine these models have been relentless, aiming to enable them to process and respond to natural and programming languages with human-like expertise (<PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2023) .", "cite_spans": [{"start": 213, "end": 235, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017;", "ref_id": "BIBREF47"}, {"start": 236, "end": 256, "text": "<PERSON> et al., 2020a;", "ref_id": null}, {"start": 257, "end": 270, "text": "OpenAI, 2023)", "ref_id": null}, {"start": 442, "end": 465, "text": "(<PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF43"}, {"start": 466, "end": 483, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF55"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Despite their remarkable achievements, LLMs often encounter challenges in understanding their contextual inputs during interactions with users (<PERSON> et al., 2023; <PERSON> et al., 2021) . This difficulty becomes particular evident when they are presented prompts 1 containing extensive background contexts or complex user instructions. Lengthy contexts can overwhelm LLMs, as their attention modules, learned from data, are unable to fully capture crucial details (<PERSON> et al., 2023) . Complex instructions can further inhibit the model from focusing on the user's intentions, resulting in undesired outputs (<PERSON> et al., 2022) . Additionally, for time-sensitive data, such as news articles, there can exist factual knowledge within contexts, which contradicts with model prior beliefs induced from outdated pre-training. As a result, a model may generate outputs conditioned on its pre-existing belief instead of attending to new facts within the contexts (<PERSON><PERSON> et al., 2022a; b; <PERSON> et al., 2022; <PERSON> et al., 2023) . All of these challenges contribute to LLMs struggling to comprehend user intentions.", "cite_spans": [{"start": 143, "end": 162, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF37"}, {"start": 163, "end": 179, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF24"}, {"start": 458, "end": 476, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 601, "end": 619, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF52"}, {"start": 949, "end": 969, "text": "(<PERSON><PERSON> et al., 2022a;", "ref_id": null}, {"start": 970, "end": 972, "text": "b;", "ref_id": "BIBREF57"}, {"start": 973, "end": 995, "text": "<PERSON> et al., 2022;", "ref_id": null}, {"start": 996, "end": 1019, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF13"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Compared to LLMs, human readers rarely struggle to understand the emphases of articles and intentions of writers. Writers often leverage a variety of text styles, such as bold and italics, to emphasize specific contents. This mechanism enables writers to direct and maintain the attention of human readers, ensuring that the intended information is accurately captured. In interactions between users and LLMs, it is users also need to highlight specific information for the model. Consequently, model generation can be effectively biased in accordance with user guidance, thus addressing the challenges mentioned earlier. This feature is particularly essential when designing user-AI interfaces, and can be frequently applied in extensive conversations between users and models. Existing methods, however, do not support such a mechanism. LLMs are inherently limited to processing plain texts, devoid of any stylistic cues or emphasis markers (<PERSON> et al., 2020b; <PERSON> et al., 2021; <PERSON> et al., 2022) . Even when emphasis markers are added to prompts, state-of-the-art LLMs often struggle to discern weak signals from a couple of marker tokens (See evidence in Section 5.1).", "cite_spans": [{"start": 943, "end": 964, "text": "(<PERSON> et al., 2020b;", "ref_id": null}, {"start": 965, "end": 982, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF23"}, {"start": 983, "end": 1000, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF52"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Motivated by the need to convey user emphasis, we introduce PASTA (Post-hoc Attention STeering Approach), a post-hoc method2 that enables users to highlight specific information, e.g., an instruction as in Figure 1 , and steer models to interpret emphasized texts like human readers. Specifically, PASTA selects a small subset of attention heads and applies precise attention reweighting on them. As illustrated in Figure 1 , PASTA upweights the attention scores of the user-specified tokens while downweighting the other tokens at specific attention heads. Our method is inspired by the observation that attention modules exhibit various token-attending patterns across different heads (<PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2019) . These attention patterns can be interpreted as encoding diverse semantic or syntactic information, and altering them can substantially influence model behaviors (<PERSON> et al., 2023a; <PERSON> et al., 2021b) . Through steering attention modules, PASTA directs the model to pay close attention to the user-specified parts and hence generate the desired output aligning with the highlighted contents. Notably, PASTA is applied after training and does not require changing any model parameters; PASTA only requires access to the attention scores of specific heads of an LLM.", "cite_spans": [{"start": 687, "end": 708, "text": "(<PERSON> et al., 2019;", "ref_id": null}, {"start": 709, "end": 728, "text": "<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF48"}, {"start": 729, "end": 748, "text": "<PERSON> et al., 2019)", "ref_id": "BIBREF5"}, {"start": 912, "end": 931, "text": "(<PERSON> et al., 2023a;", "ref_id": null}, {"start": 932, "end": 949, "text": "<PERSON> et al., 2021b)", "ref_id": null}], "ref_spans": [{"start": 213, "end": 214, "text": "1", "ref_id": "FIGREF0"}, {"start": 422, "end": 423, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Since attention heads can serve different functions (<PERSON><PERSON> et al., 2019; <PERSON><PERSON> et al., 2023) , we introduce an efficient model profiling algorithm to identify which heads are effective for steering. Specifically, we subsample small training sets from multiple tasks and evaluate the performance of attention steering for each individual head across these tasks. PASTA selects the attention heads that, when steered, generally improve the multi-task performance. We empirically observe that steering these heads not only benefits the existing tasks but also enhances the performance on unseen tasks. Notably, the model profiling is performed only once for an LLM. The selected attention heads can be regarded as a model-level profile, effective for steering the LLM on unseen tasks.", "cite_spans": [{"start": 52, "end": 73, "text": "(<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF45"}, {"start": 74, "end": 91, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We conduct experiments on diverse tasks to demonstrate the effectiveness of PASTA. Specifically, we evaluate PASTA using GPT-J-6B (Wang & Komatsuzaki, 2021) and LLAMA-7B (<PERSON><PERSON><PERSON><PERSON> et al., 2023) on tasks that span complex instructions, lengthy contexts, and knowledge conflicts within contexts. The results demonstrate that PASTA consistently provides a significant performance improvement over baseline prompting strategies. For example, PASTA achieve an average accuracy improvement of 22% over few-shot prompting for LLAMA-7B across 4 challenging tasks.", "cite_spans": [{"start": 130, "end": 156, "text": "(Wang & Komatsuzaki, 2021)", "ref_id": "BIBREF49"}, {"start": 170, "end": 192, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF46"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Problem description In standard LLM prompting, we are given a pre-trained LLM and a text prompt x. In our setting, we additionally require (i) access to attention scores produced by attention modules in the LLM3 and (ii) we are provided a user-specified subset of the prompt x g ⊂ x to be emphasized.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BACKGROUND", "sec_num": "2"}, {"text": "As in the example in Figure 1 , x can be a string that ends in an instruction, such as <PERSON> is a doctor but used to be a nurse...Return her occupation in json format. If a user emphasizes the instruction, x g can simply be the final instruction Return her occupation in json format. In evaluation datasets, we assume that the user-specified part of each example is already provided by enclosing at its both ends in some emphasis markers, like ' * ' marker in Markdown. Generating these well-structured data often incurs little overhead. For example, in the dataset tailored for evaluting model ability to follow user instruction, we can simply mark the final instruction for every example, which are fixed and shared across examples. When it comes to user-LLM interface, users can specify x g by enclosing it with the same emphasis markers. x g can be specified flexibly. Namely, it need not be a continuous span, and can be used to emphasize diverse information.", "cite_spans": [], "ref_spans": [{"start": 28, "end": 29, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "BACKGROUND", "sec_num": "2"}, {"text": "Multi-Head Attention. A typical transformer model consists of L stacked layers, where each layer contains two submodules: a multi-head attention (MHA) and a fully connected feed-forward network (FFN). Given the input X ∈ R n×d , MHA of the layer l performs the attention function in parallel H heads: MHA (l) (X) = Concat(H (l,1) , ..., H (l,H) )W o where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BACKGROUND", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "H (l,h) = A (l,h) V = Softmax QK ⊤ / d h V", "eq_num": "(1)"}], "section": "BACKGROUND", "sec_num": "2"}, {"text": "where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BACKGROUND", "sec_num": "2"}, {"text": "Q = XW q h , K = XW k h , V = XW v h and W q h , W k h , W v h ∈ R d×d h are learnable", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BACKGROUND", "sec_num": "2"}, {"text": "projection matrices of head h. d h is typically set to d/H. Specifically, denote the attention scores at the head h of the l-th layer as A (l,h) .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BACKGROUND", "sec_num": "2"}, {"text": "PASTA (Algorithm 1) consists of two components: (i) post-hoc attention steering, which emphasizes the user-specified parts of the input during inference, see Section 3.1 and (ii) multi-task model profiling, which selects the effective attention heads for steering, see Section 3.2.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "METHOD", "sec_num": "3"}, {"text": "Multi-task model profiling (Section 3.2) 1: Input: small training sets {D (i) } m i=1 , the hyperparameters α, k; 2: for 1 ≤ i ≤ m do 3:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 PASTA: Post-hoc Attention Steering Approach", "sec_num": null}, {"text": "for 1 ≤ l ≤ L, 1 ≤ h ≤ H do 4:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 PASTA: Post-hoc Attention Steering Approach", "sec_num": null}, {"text": "Evaluate the model performance on D (i) when steering the head (l, h) by (2); 5:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 PASTA: Post-hoc Attention Steering Approach", "sec_num": null}, {"text": "Return the evaluation result of steering (l, h) on D (i) ; 6: end for 7:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 PASTA: Post-hoc Attention Steering Approach", "sec_num": null}, {"text": "Collect the steering results of all heads and return the task profiling R (i) ; 8: end for 9: Output: The attention head set", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 PASTA: Post-hoc Attention Steering Approach", "sec_num": null}, {"text": "H = ∩ m i=1 R (i)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 PASTA: Post-hoc Attention Steering Approach", "sec_num": null}, {"text": "1:k . Inference-time steering (Section 3.1) 1: Input: text inputs x, user-underlined segments G, coefficient α; 2: Output: the model generations while steering every head (l, h) in H by (2).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 PASTA: Post-hoc Attention Steering Approach", "sec_num": null}, {"text": "PASTA emphasizes the user-specified input subset by downweighting the attention scores of tokens that are not specified by the user. Specifically, given the index set of highlighted input spans as G, PASTA emphasizes these user-specified tokens by an attention projection T :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "POST-HOC ATTENTION STEERING", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "H (l,h) = T (A (l,h) )V , where [T (A)] ij = αA ij /C i if j ∈ G - A ij /C i otherwise. (", "eq_num": "2"}], "section": "POST-HOC ATTENTION STEERING", "sec_num": "3.1"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "POST-HOC ATTENTION STEERING", "sec_num": "3.1"}, {"text": "where 0 ≤ α < 1 is a scaling coefficient and G -= [n] -G is the index set of tokens that are not in G. The term C i = j∈G A ij + j∈G -αA ij normalizes the scores so that they sum to one. The attention steering (2) is conducted during the inference time and does not require any training.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "POST-HOC ATTENTION STEERING", "sec_num": "3.1"}, {"text": "(2) steers the model attention by scaling down the scores of tokens that are not highlighted by the user.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "POST-HOC ATTENTION STEERING", "sec_num": "3.1"}, {"text": "When the coefficient α is set very small, user-specified segments are highlighted given their increased attention scores after renormalization. Consequently, we can direct the model to concentrate more on the user-specified tokens, biasing the generation to align with the specified contents.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "POST-HOC ATTENTION STEERING", "sec_num": "3.1"}, {"text": "PASTA scales down the attention scores of non-specified tokens by α. As renormalization is followed, it is equivalent to scaling up the attention scores of user-specified tokens by 1/α. The reason of selecting ( 2) is that it can be more numerically stable compared to scaling up scores. Alternatively, one can also scale the attention scores by adding a positive constant to the underlined tokens G.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "POST-HOC ATTENTION STEERING", "sec_num": "3.1"}, {"text": "The reason of we select multiplication in (2) instead of addition is that it preserves the difference on attention magnitude among the highlighted tokens. As such, the steering operation only adjusts overall attention scales of two groups of tokens. In contrast, addition by a large constant to the highlighted tokens results in their attention scores almost uniformly distributed, leading to unnecessary information loss and performance degeneration.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "POST-HOC ATTENTION STEERING", "sec_num": "3.1"}, {"text": "Empirically, we find that applying attention steering in (2) to all attention heads performs worse than applying it only to specific heads (see Section 5.3). It is important to specify the correct attention heads, given that different heads serve distinctive roles in encoding semantic/syntactic information.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MULTI-TASK MODEL PROFILING", "sec_num": "3.2"}, {"text": "To this end, we propose a multi-task model profiling algorithm to identify the effective attention heads for steering. Specifically, given m tasks involving user emphases, we subsample a small training set D (i) (e.g., |D (i) | = 1000) from each task i. Then, we evaluate the performance of steering every individual attention head (l, h)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MULTI-TASK MODEL PROFILING", "sec_num": "3.2"}, {"text": "(1 ≤ l ≤ L, 1 ≤ h ≤ H) on each small subset D (i) (1 ≤ i ≤ m).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MULTI-TASK MODEL PROFILING", "sec_num": "3.2"}, {"text": "For every task i, we rank all of heads according to their steering performance on D (i) and regard the ranking R (i) = [(l 1 , h 1 ), (l 2 , h 2 ), . . . ] as the profiling of task i. We then set the attention head set H for steering as the intersection of top-k performing heads, H = ∩ m i=1 R (i)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MULTI-TASK MODEL PROFILING", "sec_num": "3.2"}, {"text": "1:k (see Section 5.3 for alternative choices). Intuitively, we expect performance to improve as the number of tasks m increases.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MULTI-TASK MODEL PROFILING", "sec_num": "3.2"}, {"text": "Like attention steering, model profiling requires only access to attention scores, in addition to its inputs and outputs (model weights and gradients are not required). Importantly, this process needs to be performed only once for a LLM, similar to finetuning. However, unlike finetuning, model steering does not modify model weights and, more importantly, generalizes to new tasks. The resulting head set H can be regarded as a model-level profile. Once it is determined, we can apply the attention steering on H to both existing tasks and unseen tasks to enhance model contextual understanding and benefit downstream performance.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MULTI-TASK MODEL PROFILING", "sec_num": "3.2"}, {"text": "Evaluation tasks and metrics.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "We implement PASTA for two pre-trained models: GPT-J (6 billion parameters, (Wang & <PERSON>, 2021) ) and LLaMA-7B (7 billion parameters, (<PERSON><PERSON><PERSON><PERSON> et al., 2023) ). We evaluate the effectiveness of PASTA at (i) handling complex user instructions, (ii) interpreting lengthy contexts, and (iii) resolving in-context knowledge conflicts. For (i), we introduce two new tasks: JSON formatting and Pronouns changing. For (ii) and (iii), we study Bias in Bios (De-Arteaga et al., 2019) and CounterFact (<PERSON><PERSON> et al., 2022a) . For each task, we provide a description, describing which part of the input we emphasize, and what metrics we use for evaluation (see Appendix A for full dataset details).", "cite_spans": [{"start": 76, "end": 102, "text": "(Wang & Komatsuzaki, 2021)", "ref_id": "BIBREF49"}, {"start": 141, "end": 163, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF46"}, {"start": 455, "end": 480, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF8"}, {"start": 497, "end": 517, "text": "(<PERSON><PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "• JSON Formatting is a new task that evaluates an LLM's ability to produce outputs in a userdesired format (JSON). This is an important usecase for LLMs when their output is being used in a downstream process. This task utilizes the biographical data from BiasBios (described below) but appends a different instruction to the end of contexts: answer the occupation of {person} and generate the answer as JSON format. The instruction prompts models to generate outputs in JSON format.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "We emphasize the final instruction Metrics: (a) Format accuracy (F. Acc.) measures the accuracy at generating valid JSON. (b) Prediction accuracy (P. Acc.) measures the accuracy at generating the correct target in JSON values after loading the JSON-formatted generations.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "• Pronouns changing is a new task that evaluates an LLM's ability to follow a difficult user instruction. It again uses the biographical contexts from BiasBios but instead instructs models to: substitute 'she' and 'he' with 'they' and generate the occupation of {person} after changing pronouns.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "We emphasize the final instruction.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "Metrics: (a) Accuracy evaluates the ratio that 'she/he' are successfully changed to 'they' in model generations. (b) All-changed accuracy (A. Acc.) is the ratio that models replace all corresponding pronouns, i.e., changing she/he/her/him/hers/his to they/them/their/theirs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "• CounterFact measures an LLM's ability to generate text consistent with a new fact. Each example consists of (subject, relation, old target, new target), e.g., (<PERSON>, is a professional, basketball player, baseball player). We present the model both old and new facts following the prompt: Previously, {old fact}, but currently, {new fact}. {question}. This change in facts over time often confuses LLMs, resulting in random guesses on two of them when answering the {question}.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "We emphasize the input span containing the new fact.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "Metrics: we evaluate metrics following (<PERSON><PERSON> et al., 2022a ): (a) Efficacy score (ES) is the portion of cases for which the model has P LLM (new target) > P LLM (old target); (b) Paraphrase score (PS) is the same as ES but changes the {question} with a set of rephrased questions to assess the generalization", "cite_spans": [{"start": 39, "end": 58, "text": "(<PERSON><PERSON> et al., 2022a", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "• BiasBios consists of professional biographies of non-famous people, originally introduced to investigate gender bias in occupations. Each example includes biographical context and a label of target occupation. The first sentence mentions the person's occupation, and subsequent sentences describe the individual's career history but may not be directly related to the prediction, potentially distracting the model attention. At the end of the context, we append the question: {person} has the occupation of . We emphasize the first sentence, as it carries the most information about the occupation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "Metrics: following (<PERSON> et al., 2023) , we compute Accuracy by checking whether the probability assigned to the target occupation is the highest among the 28 candidate occupations.", "cite_spans": [{"start": 19, "end": 43, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF13"}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "For Pronouns changing, CounterFact, and BiasBios, we additionally measure Fluency as the average bi-gram and tri-gram entropy of generations, designed to be low for degenerated or repetitive texts (<PERSON><PERSON> et al., 2022a) . We filter out any results receiving a fluency below 3.0 (see full results including fluency in Appendix B.1).", "cite_spans": [{"start": 197, "end": 217, "text": "(<PERSON><PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "Baselines. We compare PASTA to the following baselines:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "• Zero-shot prompting is the most common approach to interact with LLMs, in which a user feeds models a prompt containing background context and a user instruction or question.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "• Marked prompting alters the prompts used in zero-shot prompting by surrounding user-specified input spans with emphasis markers, e.g. asterisks, as is done in markdown files for emphasis, or quotes, as is done in natural languages.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "• Few-shot prompting includes demonstrations (example inputs and target outputs) at the beginning of the prompt fed to the LLM. Few-shot prompting often improves performance in new tasks, but increases the computational cost of inference due to the increased prompt length, particularly when demonstrations are lengthy (<PERSON> et al., 2023 ); here we use 3 demonstrations in context.", "cite_spans": [{"start": 319, "end": 337, "text": "(<PERSON> et al., 2023", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTAL SETUP", "sec_num": "4"}, {"text": "We study PASTA in 2 settings: multi-task and task-agnostic. In the multi-task setting, the evaluation task j is included for profiling, whereas in the task-agnostic setting, the evaluation task is excluded (instead, we profile on the 3 datasets besides j). The multi-task setting improves performance but requires labeled training samples for the task which is evaluated, which can be difficult to obtain in practice.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PASTA settings", "sec_num": null}, {"text": "Empirically, we find that PASTA is not sensitive to the scaling coefficient α (see Section 5.3) and fix it to 0.01 in our experiments. We select 1000 training samples from each of the 4 tasks above for model profiling. After model profiling, we select k from {300, 400, 500} for LLAMA-7B ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PASTA settings", "sec_num": null}, {"text": "Tables 1 and 2 present the main results for PASTA applied to LLAMA-7B and GPT-J respectively. Few-shot prompting is the strongest baseline, and task-agnostic PASTA outperforms it on the main metric for each task for all settings except JSON Formatting with GPT-J. Multi-task PASTA outperforms all baselines across all settings.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MAIN RESULT: PASTA IMPROVES MODEL GENERATION", "sec_num": "5.1"}, {"text": "PASTA can improve LLM instruction following. The results from JSON Formatting and Pronouns Changing tasks indicate that, by highlighting the user instruction at the end of inputs, PASTA effectively steers models to focus on user intentions, thereby biasing their generation to fulfill specific requirements or formats. For example, while GPT-J only achieves 39.9% of its zero-shot generations complying the user requirement on the Pronouns Changing task, PASTA yields a remarkable 53% accuracy improvement by emphasizing the instruction. Moreover, PASTA achieves an impressive 96.64% format accuracy and 85.09% prediction accuracy when applied to LLAMA-7B on the JSON Formatting task. This performance exceeds that of few-shot prompting by 11%, even though few-shot prompting explicitly provides the model with correct JSON examples through additional demonstrations. Table 3 presents a few examples generated by LLAMA-7B when applying PASTA.", "cite_spans": [], "ref_spans": [{"start": 874, "end": 875, "text": "3", "ref_id": "TABREF2"}], "eq_spans": [], "section": "MAIN RESULT: PASTA IMPROVES MODEL GENERATION", "sec_num": "5.1"}, {"text": "PASTA can help models capture crucial contextual information. In the case of BiasBios and CounterFact tasks, we apply PASTA to emphasize specific context spans for LLMs. Consequently, the models are guided to pay close attention to the specific contextual information or new facts within contexts. The results from these two tasks illustrate that PASTA can direct the models to interpret the crucial information or resolve the knowledge conflicts within contexts, yielding significant improvement in prediction performance of both tasks. For example, PASTA achieves a prediction accuracy of 94.96% for GPT-J on the BiasBios task, which is 16.32% higher than the best baseline.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MAIN RESULT: PASTA IMPROVES MODEL GENERATION", "sec_num": "5.1"}, {"text": "Tables 1 and 2 also suggest that marked prompting, a baseline that highlights specific texts akin to human writers, struggles to effectively convey emphasis to LLMs. One possible reason is that these emphasis markers rarely appear in the massive pre-training data. In contrast, few-shot prompting sometimes leads to improvements in model performance. However, a drawback of few-shot prompting is its instability, i.e. its performance exhibits high variance across different samples in the demonstration (See Appendix B). It is well-known that the the performance of LLMs can be sensitive to minor changes in prompts, such as rephrasing and reformatting, even when these prompts convey the same meaning (<PERSON> & <PERSON>, 2021; <PERSON> et al., 2021) . We find that PASTA can alleviate the sensitivity of model performance to varying prompts. Specifically, Table 4 evaluates the performance of LLAMA-7B and GPT-J on JSON Formatting and Pronouns Changing task given different instructions in the prompt template, all of which convey the same meaning (see precise prompts in Appendix A.1). The results show that zero-shot performance is sensitive to different prompts and can significantly deteriorate with poorly crafted templates. In contrast, PASTA consistently improves model performance over zero-shot prompting for all prompts, effectively mitigating sensitivity to variations in the prompts.", "cite_spans": [{"start": 702, "end": 729, "text": "(Reynolds & McDonell, 2021;", "ref_id": null}, {"start": 730, "end": 747, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF23"}], "ref_spans": [{"start": 860, "end": 861, "text": "4", "ref_id": "TABREF3"}], "eq_spans": [], "section": "MAIN RESULT: PASTA IMPROVES MODEL GENERATION", "sec_num": "5.1"}, {"text": "In this section, we investigate different hyperparameter choices and modeling decisions that affect the performance of PASTA. The performance of LLAMA-7B on the JSON Formatting task when we steer (i) all heads (green); (ii) an entire layer (yellow); and (iii) an individual head within a layer (blue violin plot). The performance varies dramatically across layers and across heads of a layer. Appendix B.3 for comparisons on the remaining tasks). Selecting heads via model profiling in PASTA (red line) significantly outperforms other approaches. Steering all heads (dashed green line) degrades performance compared to the baseline zero-shot performance (dashed black line). This is likely because steering all heads over-amplifies the user-specified information at the expense of other essential information required for effective generation and prediction. Interestingly, we find that the performance varies significantly when steering different layers (yellow) or heads (blue violin plot). As mentioned in Section 1, attention heads play distinct roles in encoding diverse semantic and syntactic information (<PERSON><PERSON> et al., 2019) . When steering heads, which are appropriately involved in encoding of user-specified information, the model can be guided to capture and reinforce these specific signals. Conversely, modifying the attention of unrelated heads not only fails to emphasize the desired information but also interferes with their original functions, resulting in performance deterioration. Therefore, it is important to identify the effective heads through model profiling prior to applying the steering.", "cite_spans": [{"start": 1111, "end": 1132, "text": "(<PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF45"}], "ref_spans": [], "eq_spans": [], "section": "ANALYSIS AND ABLATIONS", "sec_num": "5.3"}, {"text": "Varying strategies for selecting heads during profiling. As described in Sec. 5.3, our model profiling selects the Intersection of the top-k performing heads to steer across multiple tasks. Alternatively, when evaluating on task j, we can select heads for steering with different strategies: (i) Task-specificsteer the top-k 2 performing heads of only the task j, i.e., R (j) 1:k2 ; or (ii) Union -the union of these heads across multiple tasks, i.e., ∪ m i=1 R", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model profiling", "sec_num": null}, {"text": "(i)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model profiling", "sec_num": null}, {"text": "1:k2 . Table 5 compares their performance. Using task-specific heads rather than intersection-selected heads sometimes yields improved performance, but requires selecting a different set of heads for each new task. Varying the number of heads to be steered. Figures 3a and 3b illustrate the performance of PASTA when steering different number of heads on two tasks. The results suggest that as more heads are included for steering, the model follows the user even more closely, achieving higher efficacy (JSON Format Acc. and Pron. Change Acc.). However, at some point, this it results in a decrease in the metrics reflecting the generation quality (JSON Pred. Acc and Fluency). Thus, there is a trade-off between emphasizing efficacy and generation quality. Overemphasizing can lead the model to focus solely on satisfying the user requirements and ignore the other parts. Therefore, we recommend applying PASTA to a moderate number of heads (typically 50 to 150), striking a balance between the efficacy and generation quality.", "cite_spans": [], "ref_spans": [{"start": 13, "end": 14, "text": "5", "ref_id": "TABREF5"}, {"start": 266, "end": 268, "text": "3a", "ref_id": "FIGREF2"}, {"start": 273, "end": 275, "text": "3b", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "Model profiling", "sec_num": null}, {"text": "Varying the scaling coefficient α. hyperparameter; in practice, we fix it as 0.01. Notice that setting α to zero should be avoided, as this leads to the complete removal of other crucial contexts at the steered heads, resulting in performance degeneration.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Model profiling", "sec_num": null}, {"text": "The primary method for controlling LLMs has been through prompting, often yielding impressive improvements in performance (<PERSON> et al., 2020b; <PERSON> et al., 2021; <PERSON> et al., 2022) and spurring a line of work aiming to make prompting easier, e.g. (<PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2020; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2023b) . However, LLMs remain extremely sensitive to nuances in prompts (Webson & Pavlick, 2021; <PERSON> et al., 2021) ; PASTA complements these approaches by making it easier for a user to specify a prompt in difficult scenarios.", "cite_spans": [{"start": 122, "end": 143, "text": "(<PERSON> et al., 2020b;", "ref_id": null}, {"start": 144, "end": 161, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF23"}, {"start": 162, "end": 179, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF52"}, {"start": 246, "end": 269, "text": "(<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF44"}, {"start": 270, "end": 288, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF0"}, {"start": 289, "end": 307, "text": "<PERSON> et al., 2020;", "ref_id": "BIBREF40"}, {"start": 308, "end": 326, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF10"}, {"start": 327, "end": 347, "text": "<PERSON> et al., 2023b)", "ref_id": null}, {"start": 413, "end": 437, "text": "(Webson & Pavlick, 2021;", "ref_id": "BIBREF50"}, {"start": 438, "end": 454, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF24"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "6"}, {"text": "Another line of work aims to make LLMs more amenable to prompting by modifying them during training. Most prominent among these approaches are instruction finetuning (<PERSON> et al., 2021; <PERSON> et al., 2022) , Reinforcement Learning from Human Feedback (<PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2022) , and other related methods, e.g. (<PERSON> et al., 2023) . There are also a few methods for directly specifying which parts on an input are important during training, e.g. (<PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2023) . PASTA can be used in addition to these approaches to improve some aspects of model steerability (e.g. instruction following).", "cite_spans": [{"start": 166, "end": 184, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF51"}, {"start": 185, "end": 204, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF4"}, {"start": 250, "end": 272, "text": "(<PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF57"}, {"start": 273, "end": 293, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF31"}, {"start": 328, "end": 346, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF20"}, {"start": 462, "end": 481, "text": "(<PERSON> et al., 2017;", "ref_id": "BIBREF34"}, {"start": 482, "end": 502, "text": "<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF33"}, {"start": 503, "end": 528, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2020;", "ref_id": "BIBREF35"}, {"start": 529, "end": 550, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF19"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "6"}, {"text": "PASTA is related to variety of methods for adapting to new tasks, including LoRA (<PERSON> et al., 2021a) , AdaLoRA (<PERSON> et al., 2023) , QLoRA (<PERSON><PERSON><PERSON> et al., 2023) , and TOAST (<PERSON> et al., 2023b) . PASTA is also related to a variety of research on model editing, e.g. ROME (<PERSON><PERSON> et al., 2022a) , MEMIT (<PERSON><PERSON> et al., 2022b) , MEND (<PERSON> et al., 2022) , and REMEDI (<PERSON> et al., 2023) . Unlike these works, PASTA preserves an LLMs ability to transfer to new tasks using prompts and human-selected info, rather than using new labeled examples.", "cite_spans": [{"start": 81, "end": 99, "text": "(<PERSON> et al., 2021a)", "ref_id": null}, {"start": 110, "end": 130, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF56"}, {"start": 139, "end": 162, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF11"}, {"start": 175, "end": 194, "text": "(<PERSON> et al., 2023b)", "ref_id": null}, {"start": 272, "end": 292, "text": "(<PERSON><PERSON> et al., 2022a)", "ref_id": null}, {"start": 301, "end": 321, "text": "(<PERSON><PERSON> et al., 2022b)", "ref_id": null}, {"start": 329, "end": 352, "text": "(<PERSON> et al., 2022)", "ref_id": null}, {"start": 366, "end": 390, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF13"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "6"}, {"text": "Finally, PASTA is also motivated by works which have aimed to mechanistically understand attention scores (<PERSON><PERSON> et al., 2023) , e.g. by studying them through feature importance (<PERSON> <PERSON>, 2019; <PERSON>ieg<PERSON><PERSON> & Pinter, 2019; <PERSON><PERSON> et al., 2023) , probing (<PERSON><PERSON><PERSON> et al., 2018; <PERSON>, 2019) , visualization (<PERSON><PERSON><PERSON> et al., 2015; <PERSON><PERSON> et al., 2017) , localizing knowledge (<PERSON><PERSON> et al., 2022a; <PERSON> et al., 2021) , categorizing directions in representation space (<PERSON> et al., 2017; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) , or natural-language explanations (<PERSON> et al., 2023; <PERSON> et al., 2023a) .", "cite_spans": [{"start": 106, "end": 124, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": null}, {"start": 176, "end": 198, "text": "(Jain & Wallace, 2019;", "ref_id": "BIBREF16"}, {"start": 199, "end": 224, "text": "Wiegreffe & Pinter, 2019;", "ref_id": "BIBREF53"}, {"start": 225, "end": 242, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF9"}, {"start": 253, "end": 275, "text": "(<PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF6"}, {"start": 276, "end": 293, "text": "<PERSON>, 2019)", "ref_id": "BIBREF21"}, {"start": 310, "end": 333, "text": "(<PERSON><PERSON><PERSON> et al., 2015;", "ref_id": "BIBREF17"}, {"start": 334, "end": 352, "text": "<PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF30"}, {"start": 376, "end": 396, "text": "(<PERSON><PERSON> et al., 2022a;", "ref_id": null}, {"start": 397, "end": 414, "text": "<PERSON> et al., 2021)", "ref_id": "BIBREF7"}, {"start": 465, "end": 483, "text": "(<PERSON> et al., 2017;", "ref_id": "BIBREF18"}, {"start": 484, "end": 509, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF36"}, {"start": 545, "end": 565, "text": "(Bills et al., 2023;", "ref_id": null}, {"start": 566, "end": 586, "text": "<PERSON> et al., 2023a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "6"}, {"text": "In this study, we propose PASTA, a novel approach aimed at enabling LLMs to move beyond the limitations of plain text and effectively perceive user guidance embodied as highlighted parts of prompts. By making precise adjustments to attention scores in selected heads, PASTA directs the model's focus to the relevant context, mirroring the way humans benefit from textual cues. Unlike traditional fine-tuning methods, PASTA is applied at inference time and requires neither parameter updates nor gradient computation; PASTA requires only selecting which attention heads to apply the re-weighting to, a one-time profiling operation for a LLM. Experimental results show that PASTA can significantly improve model performance on a variety of tasks. In the future, we plan to integrate PASTA with various other methods, such as few-shot in-context learning, aiming to highlight effective examples to enhance its stability.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "7"}, {"text": "In this section, we include extended results, including fluency metrics. Fluency score is the average bigram and tri-gram entropy of generations, designed to be low for degenerated or repetitive texts (<PERSON><PERSON> et al., 2022a) . This metric can be regarded as the reference metric of generation quality. Typically, the generations of language models are reliable as long as their fluency score is not too low. Here, we filter out any results receiving a fluency score below 3.0. Table 8 , 9 and 10 include all results and fluency evaluation. Few-shot prompting sometimes leads to improvements in model performance. as explicitly providing the examples in additional demonstrations. However, a drawback of few-shot prompting is its insta-bility, meaning its performance exhibits high variance across different samples in the demonstratio.", "cite_spans": [{"start": 201, "end": 221, "text": "(<PERSON><PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [{"start": 480, "end": 481, "text": "8", "ref_id": "TABREF6"}], "eq_spans": [], "section": "B.1 EXTENDED RESULTS WITH FLUENCY", "sec_num": null}, {"text": "In this section, we present the results to show that the performance of few-shot prompting displays high variance in terms of sampling different few-shot demonstrations. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B.1 EXTENDED RESULTS WITH FLUENCY", "sec_num": null}, {"text": "<PERSON><PERSON> is an American photographer living in New York. Specialized in fashion photography and portrait, she applies her talent on both humans and animals. In this series of pictures, It's all about personification these dogs in the spotlight. The whole thing gives a great result. More pictures & infos on her portfolio. {instruction} {\"name\": \"<PERSON><PERSON>\", \"occupation\": \"photographer\" }\\n{\"name\": \"<PERSON><PERSON>\", \"occupation\": \"photographer\" }. . . <PERSON> is a software engineer on the Core Processing team at comScore, an online audience measurement and analytics company. He prefers to do most of his coding in Pig. Before working at comScore he wrote software to optimize supply chains and to allocate fixed income securities. {instruction} {\"name\":", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PASTA Generation", "sec_num": null}, {"text": "\"<PERSON>\", \"occupation\": \"Software Engineer\"}\\n{\"name\": \"<PERSON>\", \"occupation\": \"Software Engineer\"}. . . <PERSON><PERSON> is an associate professor at the Faculty of Information, University of Toronto. He is a co-author of Readers Advisory Service in North American Public Libraries, 1870 -2005 (<PERSON><PERSON><PERSON><PERSON>, 2007) ; The Evolution of Library and Museum Partnerships: Historical Antecedents, Contemporary Manifestations, and Future Directions (Libraries Unlimited, 2004); and Reading and the Reference Librarian: The Importance to Library Service of Staff Reading Habits (McFarland, 2004) .{instruction} {\"name\": \"<PERSON><PERSON>\", \"occupation\": \"Associate Professor\"} \\n{\"name\": \"<PERSON><PERSON>\", \"occupation\": \"Associate Professor\"}. . . <PERSON> is an educational psychologist whose major concern is with how people learn, ie skills and knowledge development, especially in educational settings. His emphasis is on a strong research (statistical) foundation crossing areas such as social learning theory, information processing, and a cognitive approach to emotional factors. {instruction} {\"name\": \"<PERSON>\", \"occupation\": \"Educational Psychologist\"}\\n{\"name\": \"<PERSON>\", \"occupation\": \"Educational Psychologist\"}. . . <PERSON><PERSON> is a Solutions Architect with Amazon Web Services. He provides technical guidance, design advice and thought leadership to some of the largest and successful AWS customers and partners on the planet. His deepest expertise spans application architecture, containers, devops, security, machine learning and SaaS business applications. Over the last 12 years, he's brought an intense customer focus to challenging and deeply technical roles in multiple industries. He has a number of patents and has successfully led product development, architecture and customer engagements. {instruction} {\"name\":", "cite_spans": [{"start": 240, "end": 271, "text": "American Public Libraries, 1870", "ref_id": null}, {"start": 272, "end": 277, "text": "-2005", "ref_id": null}, {"start": 278, "end": 295, "text": "(<PERSON><PERSON><PERSON><PERSON>, 2007)", "ref_id": null}, {"start": 551, "end": 568, "text": "(<PERSON><PERSON><PERSON><PERSON>, 2004)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "PASTA Generation", "sec_num": null}, {"text": "\"Asif\", \"occupation\": \"Solutions Architect\"}\\n{\"name\": \"<PERSON><PERSON>\", \"occupation\": \"Solutions Architect\"}. . . <PERSON> is an Associate Professor in Construction Management at the University of Newcastle, Australia, where he was first appointed at the end of 1999. He is currently Deputy Head of the School of Architecture and Built Environment, and was previously Head of Discipline, Building. During this time he has undertaken various administrative roles at School, Faculty, and University level, and is currently a member of Academic Senate. {instruction} {\"name\": \"<PERSON>\", \"occupation\": \"Associate Professor in Construction Management\"}\\n{\"name\": \"<PERSON>\", \"occupation\": \"Associate Professor in Construction Management\"}. . . <PERSON> is an immigration attorney with at least three links to the Mexican government. In addition to heading the Center for Human Rights and Constitutional Law, he also operates vocesunidas.org, which until recently was called the \"Mexico Project\". It's since undergone an interesting change. {instruction} {\"name\": \"<PERSON>\", \"occupation\": \"Immigration Attorney\"}\\n The answer is a json object. {\"name\": \"<PERSON>\", \"occupation\": \"Immigration Attorney\"}\\n{\"name\": \"<PERSON>\", \"occupation\": \"Immigration Attorney\"}. . . <PERSON><PERSON> is a freelance journalist. He was also the Turtle Mountain Band of Chippewa's Oil and Gas Specialist. He is a frequent contributor to the tribe's newspaper, The Turtle Mountain Times, as well as indianz.com and Indian Country Today Media Network, both national news outlets for Indian Country. {instruction} {\"name\": \"<PERSON><PERSON>\", \"occupation\": \"Freelance Journalist\"}\\n {\"name\": \"Delvin\", \"occupation\": \"Freelance Journalist\", \"age\": 25}\\n {\"name\": \"Delvin\", \"occupation\": \"Freelance Journalist\", \"age\": 25, \"gender\": \"male\"}\\n {\"name\": \"Delvin\", \"occupation\": \"Freelance Journalist\", \"age\": 25, \"gender\": \"male\", \"hair\": \"black\", \"eyes\": \"brown\"}. . . Robert is a rock photographer based in Los Angeles. He started his career as a staff photographer for the legendary Creem Magazine. He photographed the Stooges at some of their earliest shows and has maintained a close working relationship with the group. His work has appeared in Playboy, The New York Times, Los Angeles Times, Rolling Stone, and Mojo, as well as on over 200 albums, including the Stooges' most recent album, The Weirdness. He oversaw the 2001 return of CREEM magazine, as well as the 2007 hardcover anthology CREEM: America 2019s Only Rock 2018n 2019 Roll Magazine. {instruction} {\"name\": \"Robert Matheu\", \"occupation\": \"Rock Photographer\"}\\n{\"name\": \"Robert Matheu\", \"occupation\": \"Rock Photographer\"}. . .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PASTA Generation", "sec_num": null}, {"text": "We understand the importance of preserving generative fluency and quality while enhancing taskspecific performance with PASTA. To ensure this, we employ two metrics to evaluate the quality of PASTA generations across three natural language generation tasks (Prons. Changing, BiasBios, and CounterFact).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E ADDITIONAL EVALUATION METRICS", "sec_num": null}, {"text": "• Fluency Evaluation (<PERSON><PERSON> et al., 2022a) : As mentioned in Section 5, we assess the fluency of all generations (the average bigram and trigram entropy of generations), and exclude results with a fluency score below 3.0. This step effectively eliminates degenerated or repetitive generations from consideration.", "cite_spans": [{"start": 21, "end": 41, "text": "(<PERSON><PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "E ADDITIONAL EVALUATION METRICS", "sec_num": null}, {"text": "• Consistency Metric: We employ an additional consistency metric (introduced by <PERSON> et al. ( 2023)), which measures the average tf-idf similarity between the generated text and reference texts of full dataset. This metric helps us measure how well the generated text aligns with overall contextual inputs in terms of content and style (higher is better).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E ADDITIONAL EVALUATION METRICS", "sec_num": null}, {"text": "Table 16 presents examples of LLAMA-7B generation with PASTA and their fluency and consistency scores on the Pronouns changing task. We can see that repetitive or meaningless generations receive low fluency (below 3.0) and consistency (below 8.0). The generations with high fluency (around 4.5) and consistency (above 13) are meaningful and readable. The following table presents the average fluency and consistency evaluation across the mentioned tasks: The results show that PASTA achieves comparable consistency and fluency scores to zero-shot prompting. This indicates that PASTA effectively maintains the generative quality and fluency while significantly improving the task efficacy.", "cite_spans": [], "ref_spans": [{"start": 6, "end": 8, "text": "16", "ref_id": "TABREF0"}], "eq_spans": [], "section": "E ADDITIONAL EVALUATION METRICS", "sec_num": null}, {"text": "Post-hoc means that our method does not update the model weights.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "We do not need access model weights nor intermediate outputs from the other modules like FFNs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "We implement all algorithms using <PERSON>yTor<PERSON> (<PERSON><PERSON><PERSON> et al., 2019) and <PERSON><PERSON><PERSON> (<PERSON> et al., 2019) and run experiments on NVIDIA V100 GPUs and NVIDIA A6000 GPUs.Table 6 provides detailed statistics of datasets in our experiments. For each task, the prompt templates in our results are as follows:• JSON Formatting:-(Original) {context}. Answer the occupation of {person} and generate the answer as json format. Here is an example: {\"name\": , \"occupation\": ,}. Now generate the answer. -(Shortened one in Section 5.2) {context}. Answer the occupation of {person} and generate the answer as json format. -(Rephrased one in Section 5.2) Answer the occupation of {person} and generate the answer as json format. Here is an example: {\"name\": , \"occupation\": ,}. {context}.Now generate the answer.• Pronouns Changing:-(Original): {context}. For the aforementioned text, substitute 'she' and 'he' with 'they' and generate the occupation of {person} after changing pronouns. -(Shortened one in Section 5.2): {context}. Change 'she' and 'he' with 'they' and answer the occupation of {person} after replacing the pronouns -(Rephrased one in Section 5.2): {context}. For the aforementioned descriptions, replace 'she' and 'he' with 'they' in the aformentioned text and generate the new text after replacing the pronouns.• BiasBios: {context}. {person} has the occupation of.• CounterFact: Previously, {old fact}. Currently, {new fact}. {question}", "cite_spans": [{"start": 42, "end": 63, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF32"}, {"start": 80, "end": 99, "text": "(<PERSON> et al., 2019)", "ref_id": "BIBREF54"}], "ref_spans": [{"start": 168, "end": 169, "text": "6", "ref_id": null}], "eq_spans": [], "section": "APPENDIX A EXPERIMENTAL DETAILS", "sec_num": null}, {"text": "Table 7 presents the number of heads to be steered by PASTA for LLAMA-7B and GPT-J-6B on every task. We further test PASTA's applicability to Vicuna-7B-v1.3, which is instruction-tuned from LLAMA-7B. We apply PASTA using attention heads selected from LLAMA-7B profiling (including multi-task and task-specific heads). In this way, we evaluate if the heads selected from the base models are transferable to an instruction-tuned model, thereby avoiding the re-profiling. The table below presents the performance of <PERSON><PERSON> across all tasks. The results demonstrate that the attention heads selected for LLAMA-7B effectively steer Vicuna-7B, indicating that re-profiling is not necessary for instruction-tuned models. Notably, when steering taskspecific heads selected from LLAMA profiling, PASTA significantly enhances Vicuna's performance across all tasks. This evidence shows that PASTA can complement instruction tuning without necessitating re-profiling. <PERSON> is a software engineer on the Core Processing team at comScore, an online audience measurement and analytics company. He prefers to do most of his coding in Pig. Before working at comScore he wrote software to optimize supply chains and to allocate fixed income securities. {instruction}<PERSON> is a software engineer on the Core Processing team at comScore, an online audience measurement and analytics company. They prefer to do most of their coding in Pig. Before working at comScore they wrote software to optimize supply chains and to allocate fixed income securities.\\nThe above text is a good example of a gender-neutral sentence.\\nThe following text is a good example of a gender-neutral sentence:\\nJon is a software engineer on the Core Processing team at comScore, an online audience measurement and analytics company. They prefer to do most of their coding in Pig. ", "cite_spans": [], "ref_spans": [{"start": 6, "end": 7, "text": "7", "ref_id": null}], "eq_spans": [], "section": "A.2 THE EVALUATION DETAILS OF PASTA", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Promptsource: An integrated development environment and repository for natural language prompts", "authors": [{"first": "H", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Victor", "middle": [], "last": "Bach", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "San<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>son", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Nayak", "suffix": ""}, {"first": "Taewoon", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "M", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>ful Bari", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2202.01279"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. Promptsource: An integrated development environment and repository for natural language prompts. arXiv preprint arXiv:2202.01279, 2022.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Language models can explain neurons in language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "Bills", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Language models can explain neurons in language models. URL https://openaipublic. blob. core. windows. net/neuron-explainer/paper/index. html.(Date accessed: 14.05. 2023), 2023.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Language models are few-shot learners", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Subbiah", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ariel", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Child", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Winter", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Hesse", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Litwin", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Chess", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "1877--1901", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Language models are few-shot learners. In <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> (eds.), Advances in Neural Information Processing Systems, volume 33, pp. 1877-1901. Curran Associates, Inc., 2020a. URL https://proceedings.neurips.cc/paper_files/paper/2020/file/ 1457c0d6bfcb4967418bfb8ac142f64a-Paper.pdf.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Language models are few-shot learners", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Subbiah", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Advances in neural information processing systems", "volume": "33", "issue": "", "pages": "1877--1901", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Language models are few-shot learners. Advances in neural information processing systems, 33:1877-1901, 2020b.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Scaling instruction-finetuned language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON> Won", "suffix": ""}, {"first": "Le", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Longpre", "suffix": ""}, {"first": "Barret", "middle": [], "last": "Zoph", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.11416"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. Scaling instruction-finetuned language models. arXiv preprint arXiv:2210.11416, 2022.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "What does BERT look at? an analysis of BERT's attention", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 2019 ACL Workshop BlackboxNLP: Analyzing and Interpreting Neural Networks for NLP", "volume": "", "issue": "", "pages": "276--286", "other_ids": {"DOI": ["10.18653/v1/W19-4828"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. What does BERT look at? an analysis of BERT's attention. In Proceedings of the 2019 ACL Workshop BlackboxNLP: Analyzing and Inter- preting Neural Networks for NLP, pp. 276-286, Florence, Italy, August 2019. Association for Computational Linguistics. doi: 10.18653/v1/W19-4828. URL https://aclanthology.org/W19-4828.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "What you can cram into a single vector: Probing sentence embeddings for linguistic properties", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "German", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1805.01070"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. What you can cram into a single vector: Probing sentence embeddings for linguistic properties. arXiv preprint arXiv:1805.01070, 2018.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Knowledge neurons in pretrained transformers", "authors": [{"first": "Damai", "middle": [], "last": "Dai", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>o", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Baobao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2104.08696"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Knowledge neurons in pretrained transformers. arXiv preprint arXiv:2104.08696, 2021.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Bias in bios: A case study of semantic representation bias in a high-stakes setting", "authors": [{"first": "<PERSON>", "middle": [], "last": "De-Arteaga", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2019, "venue": "proceedings of the Conference on Fairness, Accountability, and Transparency", "volume": "", "issue": "", "pages": "120--128", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Bias in bios: A case study of semantic representation bias in a high-stakes setting. In proceedings of the Conference on Fairness, Accountability, and Transparency, pp. 120-128, 2019.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "At<PERSON>: Understanding transformer predictions through memory efficient attention manipulation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Deb", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Deiseroth", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Weinbach", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2301.08110"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Atman: Understanding transformer predictions through memory efficient attention manipulation. arXiv preprint arXiv:2301.08110, 2023.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Optimizing discrete text prompts with reinforcement learning", "authors": [{"first": "Mingkai", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Han", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Shu", "suffix": ""}, {"first": "<PERSON>g", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON>", "middle": ["P"], "last": "Xi<PERSON>", "suffix": ""}, {"first": "Z<PERSON>ing", "middle": [], "last": "Hu", "suffix": ""}, {"first": "", "middle": [], "last": "Rlprompt", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2205.12548"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Rlprompt: Optimizing discrete text prompts with reinforcement learning. arXiv preprint arXiv:2205.12548, 2022.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Qlora: Efficient finetuning of quantized llms", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ari", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Qlora: Efficient finetuning of quantized llms, 2023.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "A survey on in-context learning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Damai", "middle": [], "last": "Dai", "suffix": ""}, {"first": "Ce", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Baobao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "Jingjing", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. A survey on in-context learning, 2023.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Inspecting and editing knowledge representations in language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Z"], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Inspecting and editing knowledge representations in language models, 2023.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Low-rank adaptation of large language models", "authors": [{"first": "J", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuanzhi", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weizhu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2106.09685"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Lora: Low-rank adaptation of large language models. arXiv preprint arXiv:2106.09685, 2021a.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Lora: Low-rank adaptation of large language models", "authors": [{"first": "J", "middle": ["<PERSON>"], "last": "Hu", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Yuanzhi", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weizhu", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Lora: Low-rank adaptation of large language models. arXiv preprint abs:2106.09685, 2021b.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Attention is not explanation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1902.10186"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>. Attention is not explanation. arXiv preprint arXiv:1902.10186, 2019.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Visualizing and understanding recurrent networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1506.02078"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON>. Visualizing and understanding recurrent networks. arXiv preprint arXiv:1506.02078, 2015.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Interpretability beyond feature attribution: Quantitative testing with concept activation vectors (tcav)", "authors": [{"first": "Been", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Cai", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Viegas", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1711.11279"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Interpretability beyond feature attribution: Quantitative testing with concept activation vectors (tcav). arXiv preprint arXiv:1711.11279, 2017.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Post hoc explanations of language models can improve language models", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>ck", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Himabindu", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.11426"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Post hoc explanations of language models can improve language models. arXiv preprint arXiv:2305.11426, 2023.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Rlaif: Scaling reinforcement learning from human feedback with ai feedback", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Phatale", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Mans<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Victor", "middle": [], "last": "Carbune", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.00267"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Rlaif: Scaling reinforcement learning from human feedback with ai feedback. arXiv preprint arXiv:2309.00267, 2023.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Incorporating priors with feature attribution on text classification", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Be<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1906.08286"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON>. Incorporating priors with feature attribution on text classification. arXiv preprint arXiv:1906.08286, 2019.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "Lost in the middle: How language models use long contexts", "authors": [{"first": "<PERSON>", "middle": ["F"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Paranjape", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Lost in the middle: How language models use long contexts, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Pre-train, prompt, and predict: A systematic survey of prompting methods in natural language processing", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weizhe", "middle": [], "last": "Yuan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zhengbao", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Neubig", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2107.13586"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Pre-train, prompt, and predict: A systematic survey of prompting methods in natural language processing. arXiv preprint arXiv:2107.13586, 2021.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Fantastically ordered prompts and where to find them: Overcoming few-shot prompt order sensitivity", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Max", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Stenetorp", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2104.08786"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Fantastically ordered prompts and where to find them: Overcoming few-shot prompt order sensitivity. arXiv preprint arXiv:2104.08786, 2021.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Locating and editing factual associations in gpt", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bau", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Andonian", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "17359--17372", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Locating and editing factual associations in gpt. Advances in Neural Information Processing Systems, 35:17359-17372, 2022a.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Mass-editing memory in a transformer", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Andonian", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Bau", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.07229"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Mass-editing memory in a transformer. arXiv preprint arXiv:2210.07229, 2022b.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Are sixteen heads really better than one?", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Neubig", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Are sixteen heads really better than one?", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Advances in Neural Information Processing Systems", "authors": [{"first": "In", "middle": ["H"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "A", "middle": [], "last": "Beygelzimer", "suffix": ""}, {"first": "F", "middle": [], "last": "", "suffix": ""}], "year": 2019, "venue": "", "volume": "32", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "In <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (eds.), Advances in Neural Information Processing Systems, volume 32. Curran Associates, Inc., 2019. URL https://proceedings.neurips.cc/paper_files/paper/2019/file/ 2c601ad9d2ff9bc8b282670cdd54f69f-Paper.pdf.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Feature visualization", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "OpenAI. Gpt-4 technical report", "volume": "2", "issue": "11", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Feature visualization. Distill, 2(11):e7, 2017. OpenAI. Gpt-4 technical report, 2023.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Training language models to follow instructions with human feedback", "authors": [{"first": "<PERSON>", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Almeida", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Katarina", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "27730--27744", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. Training language models to follow instructions with human feedback. Advances in Neural Information Processing Systems, 35:27730-27744, 2022.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Pytorch: An imperative style, high-performance deep learning library", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "Gross", "suffix": ""}, {"first": "Francisco", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Natalia", "middle": [], "last": "Gimelshein", "suffix": ""}, {"first": "Luca", "middle": [], "last": "Antiga", "suffix": ""}, {"first": "Alban", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>son", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Soumith", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Advances in Neural Information Processing Systems 32: Annual Conference on Neural Information Processing Systems", "volume": "", "issue": "", "pages": "8024--8035", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Pytorch: An imperative style, high-performance deep learning library. <PERSON> <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON> (eds.), Advances in Neural Information Processing Systems 32: Annual Conference on Neural Information Processing Systems 2019, NeurIPS 2019, December 8-14, 2019, Vancouver, BC, Canada, pp. 8024-8035, 2019. <PERSON><PERSON> and <PERSON>. Prompt programming for large language models: Beyond the few-shot paradigm, 2021.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Interpretations are useful: penalizing explanations to align neural networks with prior knowledge", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Bin", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1909.13584"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Interpretations are useful: penalizing explanations to align neural networks with prior knowledge. arXiv preprint arXiv:1909.13584, 2019.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Right for the right reasons: Training differentiable models by constraining their explanations", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON>", "suffix": ""}, {"first": "Finale", "middle": [], "last": "Doshi-Velez", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1703.03717"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON><PERSON>. Right for the right reasons: Training differentiable models by constraining their explanations. arXiv preprint arXiv:1703.03717, 2017.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Making deep neural networks right for the right scientific reasons by interacting with their explanations", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>mer", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Nature Machine Intelligence", "volume": "2", "issue": "8", "pages": "476--486", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Making deep neural networks right for the right scientific reasons by interacting with their explanations. Nature Machine Intelligence, 2(8):476-486, 2020.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Toward a visual concept vocabulary for gan latent space", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bau", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Antonio", "middle": [], "last": "Torralba", "suffix": ""}], "year": 2021, "venue": "Proceedings of the IEEE/CVF International Conference on Computer Vision", "volume": "", "issue": "", "pages": "6804--6812", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Toward a visual concept vocabulary for gan latent space. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 6804-6812, 2021.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Large language model alignment: A survey", "authors": [{"first": "Tianhao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xi<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Large language model alignment: A survey, 2023.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Toast: Transfer learning via attention steering", "authors": [{"first": "Baifeng", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gai", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xin", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Toast: Transfer learning via attention steering. arXiv preprint abs:2305.15542, 2023a.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Refocusing is key to transfer learning", "authors": [{"first": "Baifeng", "middle": [], "last": "Shi", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Gai", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xin", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.15542"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Refocusing is key to transfer learning. arXiv preprint arXiv:2305.15542, 2023b.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Autoprompt: Eliciting knowledge from language models with automatically generated prompts", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["L"], "last": "<PERSON>", "suffix": ""}, {"first": "I", "middle": ["V"], "last": "", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.15980"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Autoprompt: Eliciting knowledge from language models with automatically generated prompts. arXiv preprint arXiv:2010.15980, 2020.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Explaining black box text modules in natural language with language models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "R", "middle": [], "last": "Aliyah", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON>", "suffix": ""}, {"first": "Bin", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ji<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Gao", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.09863"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Ex- plaining black box text modules in natural language with language models. arXiv preprint arXiv:2305.09863, 2023a.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Explaining patterns in data with language models via interpretable autoprompting", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["X"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "Ji<PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Explaining patterns in data with language models via interpretable autoprompting, 2023b.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Learning to summarize from human feedback", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Stiennon", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON>", "suffix": ""}, {"first": "Chelsea", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Learning to summarize from human feedback. arXiv preprint abs:2009.01325, 2020.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Interactive and visual prompt engineering for ad-hoc task adaptation with large language models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>son", "suffix": ""}, {"first": "Victor", "middle": [], "last": "San<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Interactive and visual prompt engineering for ad-hoc task adaptation with large language models, 2022.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "BERT rediscovers the classical NLP pipeline", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics", "volume": "", "issue": "", "pages": "4593--4601", "other_ids": {"DOI": ["10.18653/v1/P19-1452"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. BERT rediscovers the classical NLP pipeline. In Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics, pp. 4593-4601, Florence, Italy, July 2019. Association for Computational Linguistics. doi: 10.18653/v1/P19-1452. URL https: //aclanthology.org/P19-1452.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Llama 2: Open foundation and fine-tuned chat models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Louis", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stone", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Batra", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Shruti", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.09288"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Llama 2: Open foundation and fine-tuned chat models. arXiv preprint arXiv:2307.09288, 2023.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Attention is all you need", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "Llion", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "Ł Ukasz", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "Illia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in Neural Information Processing Systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. In <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> (eds.), Advances in Neural Information Processing Systems, volume 30. Curran Associates, Inc., 2017. URL https://proceedings.neurips.cc/paper_ files/paper/2017/file/3f5ee243547dee91fbd053c1c4a845aa-Paper.pdf.", "links": null}, "BIBREF48": {"ref_id": "b48", "title": "Analyzing multi-head self-attention: Specialized heads do the heavy lifting, the rest can be pruned", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Rico", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Analyzing multi-head self-attention: Specialized heads do the heavy lifting, the rest can be pruned, July 2019. URL https://aclanthology. org/P19-1580.", "links": null}, "BIBREF49": {"ref_id": "b49", "title": "GPT-J-6B: A 6 Billion Parameter Autoregressive Language Model", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>zaki", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. GPT-J-6B: A 6 Billion Parameter Autoregressive Language Model. https: //github.com/kingoflolz/mesh-transformer-jax, May 2021.", "links": null}, "BIBREF50": {"ref_id": "b50", "title": "Do prompt-based models really understand the meaning of their prompts", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>son", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2109.01247"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Do prompt-based models really understand the meaning of their prompts? arXiv preprint arXiv:2109.01247, 2021.", "links": null}, "BIBREF51": {"ref_id": "b51", "title": "Finetuned language models are zero-shot learners", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ma<PERSON>n", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Y", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["M"], "last": "<PERSON>", "suffix": ""}, {"first": "Quoc V", "middle": [], "last": "Dai", "suffix": ""}, {"first": "", "middle": [], "last": "Le", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2109.01652"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Finetuned language models are zero-shot learners. arXiv preprint arXiv:2109.01652, 2021.", "links": null}, "BIBREF52": {"ref_id": "b52", "title": "Chain-of-thought prompting elicits reasoning in large language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ma<PERSON>n", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Ed", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "V", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Le", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "Advances in Neural Information Processing Systems", "volume": "35", "issue": "", "pages": "24824--24837", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Chain-of-thought prompting elicits reasoning in large language models. Advances in Neural Information Processing Systems, 35:24824-24837, 2022.", "links": null}, "BIBREF53": {"ref_id": "b53", "title": "Attention is not not explanation", "authors": [{"first": "<PERSON>", "middle": [], "last": "Wiegreffe", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1908.04626"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON>. Attention is not not explanation. arXiv preprint arXiv:1908.04626, 2019.", "links": null}, "BIBREF54": {"ref_id": "b54", "title": "Huggingface's transformers: State-of-the-art natural language processing", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Debut", "suffix": ""}, {"first": "Victor", "middle": [], "last": "San<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Delangue", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Cistac", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1910.03771"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Huggingface's transformers: State-of-the-art natural language processing. arXiv preprint arXiv:1910.03771, 2019.", "links": null}, "BIBREF55": {"ref_id": "b55", "title": "Deepspeed-chat: Easy, fast and affordable rlhf training of chatgpt-like models at all scales", "authors": [{"first": "Zhewei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Yazdani <PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Xiao<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ammar", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zhongzhu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L A", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Qin", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Che", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Deepspeed-chat: Easy, fast and affordable rlhf training of chatgpt-like models at all scales. arXiv preprint abs:2308.01320, 2023.", "links": null}, "BIBREF56": {"ref_id": "b56", "title": "Adaptive budget allocation for parameter-efficient fine-tuning", "authors": [{"first": "Qingru", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Pengcheng", "middle": [], "last": "He", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weizhu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "The Eleventh International Conference on Learning Representations", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Adaptive budget allocation for parameter-efficient fine-tuning. In The Eleventh International Conference on Learning Representations, 2023. URL https://openreview.net/forum?id=lq62uWRJjiY.", "links": null}, "BIBREF57": {"ref_id": "b57", "title": "Fine-tuning language models from human preferences", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stiennon", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1909.08593"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Fine-tuning language models from human preferences. arXiv preprint arXiv:1909.08593, 2019.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "fig_num": "1", "uris": null, "text": "Figure 1: PASTA uses a user-specified part of the input to steer the model generation aligning with user intentions. PASTA modifies the attention scores generated during inference, by emphasizing the scores generated at token positions corresponding to the user-specified part of the context.", "num": null}, "FIGREF1": {"type_str": "figure", "fig_num": "2", "uris": null, "text": "Figure2: The performance of LLAMA-7B on the JSON Formatting task when we steer (i) all heads (green); (ii) an entire layer (yellow); and (iii) an individual head within a layer (blue violin plot). The performance varies dramatically across layers and across heads of a layer.", "num": null}, "FIGREF2": {"type_str": "figure", "fig_num": "3", "uris": null, "text": "Figure 3: The performance of applying PASTA to LLAMA-7B on JSON Formating and Pronouns Changing tasks when varying the number of steered heads |H| (3a,3b); and changing the scaling coefficient α (3c).", "num": null}, "FIGREF3": {"type_str": "figure", "fig_num": "6", "uris": null, "text": "Figure6: The performance of LLAMA-7B on CounterFact task when we steer (i) all heads (green); (ii) entrie layer (yellow); and (iii) individual head with a layer (blue violin plot). The performance varies dramatically across layers and across heads of a layer.", "num": null}, "FIGREF4": {"type_str": "figure", "fig_num": null, "uris": null, "text": "Assistant Professor in the Department of Pathology. She received her MD from the University of Colorado School of Medicine where she also completed her AP/CP residency training in pathology and a fellowship in Cytogenetics. She completed her fellowship in Molecular Genetic Pathology in the Department of Pathology in 2009. She is a Medical Director in Cytogenetics, Molecular Cytogenetics, and Molecular Oncology at ARUP Laboratories. {instruction} {\"name\": \"<PERSON>\", \"occupation\": \"Assistant Professor in the Department of Pathology\", \"answer\": \"<PERSON> is an Assistant Professor in the Department of Pathology. She received her MD from the University of Colorado School of Medicine where she also completed her AP/CP residency training in pathology and a fellowship in Cytogenetics.\",", "num": null}, "TABREF0": {"type_str": "table", "content": "<table><tr><td/><td>Method</td><td colspan=\"4\">JSON Format Prons. Changing BiasBios CounterFact F. Acc / P. Acc Acc / A.Acc Acc ES / PS</td><td>All Ave.</td></tr><tr><td/><td>Zero-shot</td><td>60.00 / 54.94</td><td>71.84 / 66.28</td><td>87.36</td><td colspan=\"2\">58.50 / 52.03 67.29</td></tr><tr><td>Prompting</td><td>* -marked \"\"-marked</td><td>18.55 / 12.71 4.56 / 4.20</td><td>39.14 / 35.17 20.55 / 18.19</td><td>90.62 89.82</td><td colspan=\"2\">57.74 / 50.52 49.38 58.14 / 51.70 42.15</td></tr><tr><td/><td>Few-shot</td><td>84.85 / 73.58</td><td>59.06 / 55.27</td><td>88.79</td><td colspan=\"2\">87.45 / 49.82 73.45</td></tr><tr><td>PASTA</td><td>Task-agnostic Multi-task</td><td>88.16 / 49.08 96.64 / 85.09</td><td>83.65 / 81.31 96.42 / 95.84</td><td>93.54 95.28</td><td colspan=\"2\">98.82 / 99.03 85.89 99.60 / 99.57 95.46</td></tr></table>", "text": "Main results of LLAMA-7B to demonstrate that PASTA can improve the model ability to (i) follow user instruction (JSON Format and Prons. Changing); (ii) interpret contextual information (BiasBios); (iii) resolving knowledge conflicts (CounterFact). For all scores, higher is better. The best results are in bold.", "num": null, "html": null}, "TABREF1": {"type_str": "table", "content": "<table><tr><td/><td>Method</td><td colspan=\"4\">JSON Format Prons. Changing BiasBios CounterFact F. Acc / P. Acc Acc / A.Acc Acc ES / PS</td><td>All Ave.</td></tr><tr><td/><td>Zero-shot</td><td>28.83 / 25.09</td><td>39.88 / 36.19</td><td>72.76</td><td colspan=\"2\">42.14 / 42.02 44.96</td></tr><tr><td>Prompting</td><td>* -marked \"\"-marked</td><td>4.44 / 4.10 8.81 / 5.62</td><td>41.25 / 37.57 6.12 / 5.72</td><td>74.14 78.64</td><td colspan=\"2\">44.50 / 45.09 40.63 45.54 / 41.84 33.87</td></tr><tr><td/><td>Few-shot</td><td>84.15 / 72.65</td><td>35.77 / 32.08</td><td>72.98</td><td colspan=\"2\">68.34 / 38.23 59.65</td></tr><tr><td>PASTA</td><td>Task-agnostic Multi-task</td><td>46.68 / 34.71 91.50 / 18.63</td><td>91.62 / 88.60 92.96 / 91.34</td><td>80.84 94.96</td><td colspan=\"2\">99.54 / 99.57 77.80 98.62 / 98.79 85.22</td></tr></table>", "text": "Main results of GPT-J to demonstrate that PASTA can improve the model ability to (i) follow user instruction (JSON Format and Prons. Changing); (ii) interpret contextual information (BiasBios); (iii) resolving knowledge conflicts (CounterFact). For all scores, higher is better. The best results are in bold.", "num": null, "html": null}, "TABREF2": {"type_str": "table", "content": "<table><tr><td>Task</td><td>Prompt</td><td>Zero-shot Generation</td><td>PASTA Generation</td></tr><tr><td>JSON Format</td><td><PERSON><PERSON> is an American photographer living in New York. Specialized in fash-ion photography and portrait, she ap-plies her talent on both humans and ani-mals . . . {instruction}</td><td>✗ <PERSON><PERSON> is a fashion and portrait pho-tographer living in New York, special-and animals. who applies her talent on both humans ized in fashion photography and portrait,</td><td>✓ {\"name\": \"<PERSON><PERSON>\", \"occupation\": \"photographer\" }</td></tr><tr><td>Prons. Change</td><td><PERSON> is an Associate Professor in the Department of Curriculum Instruction at St. John University; she holds a doc-torate in Reading/Writing/Literacy from the University of Pennsylvania. She re-cently published an article in \"Urban Education\" with . . . {instruction}</td><td>✗ <PERSON> is a professor in the Depart-ment of Curriculum Instruction at St. John University; she holds a doctor-ate in Reading/Writing/Literacy from the University of Pennsylvania. She re-cently published an article in \"Urban Education\" with . . .</td><td>✓ <PERSON> is an associate profes-sor in the department of curriculum and instruction at St. John's Univer-sity; they hold a doctorate in read-ing/writing/literacy from the University of Pennsylvania. They recently pub-lished an article in . . .</td></tr></table>", "text": "Generation examples of LLAMA-7B on JSON Formatting and Pronouns Changing tasks.", "num": null, "html": null}, "TABREF3": {"type_str": "table", "content": "<table><tr><td/><td/><td colspan=\"2\">LLAMA-7B</td><td colspan=\"2\">GPT-J</td><td/></tr><tr><td colspan=\"2\">Instruction Method</td><td>JSON Format F. Acc / P. Acc</td><td>Prons. Changing Acc / A. Acc</td><td>JSON Format F. Acc / P. Acc</td><td>Prons. Changing Acc / A. Acc</td><td>Average</td></tr><tr><td>Original</td><td>Zero-shot PASTA</td><td>60.0 / 54.9 96.6 / 85.1</td><td>71.8 / 66.3 96.4 / 95.8</td><td>28.8 / 25.1 91.5 / 18.6</td><td>39.9 / 36.2 93.0 / 91.3</td><td>47.9 83.5</td></tr><tr><td>Shortened</td><td>Zero-shot PASTA</td><td>36.0 / 32.4 87.4 / 65.9</td><td>49.2 / 42.6 89.0 / 86.9</td><td>25.4 / 17.1 54.1 / 37.0</td><td>56.5 / 54.8 94.0 / 93.7</td><td>39.3 76.0</td></tr><tr><td>Rephrased</td><td>Zero-shot PASTA</td><td>57.9 / 54.2 97.1 / 87.1</td><td>82.3 / 79.6 89.6 / 89.0</td><td>63.3 / 50.3 77.5 / 68.1</td><td>76.0 / 72.8 94.8 / 92.3</td><td>67.1 86.9</td></tr></table>", "text": "Results about sensitivity of model performance to prompt rephrasing on the JSON Formatting task.", "num": null, "html": null}, "TABREF5": {"type_str": "table", "content": "<table><tr><td/><td>PASTA</td><td colspan=\"4\">JSON Format Prons. Changing BiasBios CounterFact F. Acc / P. Acc Acc / A.Acc Acc ES / PS</td><td>All Avg.</td></tr><tr><td/><td>Task-specific</td><td>95.56 / 86.83</td><td>98.52 / 98.02</td><td>97.62</td><td colspan=\"2\">99.18 / 99.24 96.57</td></tr><tr><td>LLAMA</td><td>Union</td><td>88.42 / 74.49</td><td>92.12 / 91.44</td><td>96.36</td><td colspan=\"2\">99.24 / 99.35 92.22</td></tr><tr><td/><td>Intersection</td><td>96.64 / 85.09</td><td>96.42 / 95.84</td><td>95.28</td><td colspan=\"2\">99.60 / 99.57 95.46</td></tr><tr><td/><td>Task-specific</td><td>85.71 / 79.39</td><td>94.74 / 92.54</td><td>97.64</td><td colspan=\"2\">99.26 / 99.34 93.29</td></tr><tr><td>GPT-J</td><td>Union</td><td>72.61 / 64.89</td><td>89.68 / 87.76</td><td>95.56</td><td colspan=\"2\">99.82 / 99.83 88.21</td></tr><tr><td/><td>Intersection</td><td>91.50 / 18.63</td><td>92.96 / 91.34</td><td>94.96</td><td colspan=\"2\">98.62 / 98.79 85.22</td></tr></table>", "text": "Varying head selection strategies between top task-specific heads, union across multiple tasks, and intersection (the default used in PASTA).", "num": null, "html": null}, "TABREF6": {"type_str": "table", "content": "<table><tr><td/><td>Method</td><td>JSON Format Prons. Changing F. Acc / P. Acc Acc / A.Acc / Flue. Acc / Flue. BiasBios</td><td>CounterFact ES / PS /Flue.</td></tr><tr><td/><td>Zero-shot</td><td colspan=\"2\">60.00 / 54.94 71.84 / 66.28 / 6.10 87.36 / 3.98 58.50 / 52.03 / 4.96</td></tr><tr><td>Prompting</td><td>* -marked \"\"-marked</td><td colspan=\"2\">18.55 / 12.71 39.14 / 35.17 / 6.03 90.62 / 3.89 57.74 / 50.52 / 5.12 4.56 / 4.20 20.55 / 18.19 / 5.13 89.82 / 3.97 58.14 / 51.70 / 5.13</td></tr><tr><td/><td>Few-shot</td><td colspan=\"2\">84.85 / 73.58 59.06 / 55.27 / 5.95 88.79 / 4.19 87.45 / 49.82 / 5.68</td></tr><tr><td>PASTA</td><td colspan=\"3\">Task-agnostic 88.16 / 49.08 83.65 / 81.31 / 4.62 93.54 / 3.03 98.82 / 99.03 / 4.78 Multi-task 96.64 /</td></tr></table>", "text": "Main results of LLAMA-7B to demonstrate that PASTA can improve the model ability to (i) follow user instruction (JSON Format and Prons. Changing); (ii) interpret contextual information (BiasBios); (iii) resolving knowledge conflicts (CounterFact). For all scores, higher is better. The best results are in bold. 85.09 96.42 / 95.84 / 5.43 95.28 / 4.05 99.60 / 99.57 / 4.89", "num": null, "html": null}, "TABREF7": {"type_str": "table", "content": "<table><tr><td/><td>Method</td><td colspan=\"2\">JSON Format Prons. Changing F. Acc / P. Acc Acc / A.Acc / Flue. Acc / Flue. BiasBios</td><td>CounterFact ES / PS /Flue.</td></tr><tr><td/><td>Zero-shot</td><td colspan=\"3\">28.83 / 25.09 39.88 / 36.19 / 5.91 72.76 / 5.06 42.14 / 42.02 / 5.01</td></tr><tr><td>Prompting</td><td>* -marked \"\"-marked</td><td>4.44 / 4.10 8.81 / 5.62</td><td colspan=\"2\">41.25 / 37.57 / 4.76 74.14 / 5.01 44.50 / 45.09 / 5.22 6.12 / 5.72 / 5.43 78.64 / 4.96 45.54 / 41.84 / 5.16</td></tr><tr><td/><td>Few-shot</td><td colspan=\"3\">84.15 / 72.65 35.77 / 32.08 / 6.46 72.98 / 4.82 68.34 / 38.23 / 5.67</td></tr><tr><td>PASTA</td><td colspan=\"4\">Task-agnostic 46.68 / 34.71 91.62 / 88.60 / 3.00 80.84 / 4.92 99.54 / 99.57 / 5.11 Multi-task 91.50 / 18.63 92.96 / 91.34 / 4.91 94.96 / 4.87 98.62 / 98.79 / 5.11</td></tr></table>", "text": "Main results of GPT-J to demonstrate that PASTA can improve the model ability to (i) follow user instruction (JSON Format and Prons. Changing); (ii) interpret contextual information (BiasBios); (iii) resolving knowledge conflicts (CounterFact). For all scores, higher is better. The best results are in bold.", "num": null, "html": null}, "TABREF8": {"type_str": "table", "content": "<table><tr><td/><td>PASTA</td><td>JSON Format Prons. Changing F. Acc / P. Acc Acc / A.Acc / Flue. Acc / Flue. BiasBios</td><td>CounterFact ES / PS /Flue.</td></tr><tr><td>LLAMA</td><td colspan=\"3\">Task-specific 95.56 / 86.83 98.52 / 98.02 / 5.92 97.62 / 4.18 99.18 / 99.24 / 4.93 union 88.42 / 74.49 92.12 / 91.44 / 4.88 96.36 / 4.13 99.24 / 99.35 / 4.53 intersection 96.64 / 85.09 96.42 / 95.84 / 5.43 95.28 / 4.05 99.60 / 99.57 / 4.89</td></tr><tr><td>GPT-J</td><td colspan=\"3\">Task-specific 85.71 / 79.39 94.74 / 92.54 / 5.07 97.64 / 5.06 99.26 / 99.34 / 4.94 Union 72.61 / 64.89 89.68 / 87.76 / 3.92 95.56 / 5.02 99.82 / 99.83 / 5.03 Intersection 91.50 / 18.63 92.96 / 91.34 / 4.91 94.96 / 4.87 98.62 / 98.79 / 5.11</td></tr><tr><td colspan=\"3\">B.2 THE VARIANCE OF FEW-SHOT PERFORMANCE</td></tr></table>", "text": "Varying head selection strategies between top top task-specific heads, union across multiple tasks, and intersection (the default used in PASTA).", "num": null, "html": null}, "TABREF9": {"type_str": "table", "content": "<table><tr><td/><td/><td/><td/><td>Few-shot examples</td><td/><td>LLAMA-7B</td><td/><td>GPT-J-6B</td><td/><td/></tr><tr><td/><td/><td/><td/><td>Demonstration 1 Demonstration 2 Demonstration 3 Demonstration 4 Demonstration 5</td><td colspan=\"4\">84.87 / 90.09 / 4.74 43.82 / 40.36 / 6.43 57.24 / 53.98 / 6.22 40.68 / 37.86 / 6.44 57.08 / 53.22 / 6.02 33.13 / 29.21 / 6.48 52.26 / 48.30 / 6.42 25.47 / 20.89 / 6.44 43.86 / 40.78 / 6.43 11.90 / 8.63 / 6.51</td><td/><td/></tr><tr><td colspan=\"5\">B.3 MODEL PROFILING RESULTS</td><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"11\">In this Section, we provide more results of the performance of LLAMA-7B on all of tasks when steering: (i) all heads; (ii) entire layer; (iii) a individual head of a layer.</td></tr><tr><td colspan=\"2\">100</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td/><td>80</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>Acc</td><td>20 40 60</td><td>1</td><td>4</td><td>8</td><td>12</td><td>16 Layer</td><td>20</td><td>24</td><td>28</td><td>32 Steer entire layer Zero-shot PASTA Steer all heads Steer single head</td></tr><tr><td colspan=\"11\">4: The performance of LLAMA-7B on Pronouns Changing task when we steer (i) all heads (green); (ii) entrie layer (yellow); and (iii) individual head with a layer (blue violin plot). The performance varies dramatically across layers and across heads of a layer.</td></tr><tr><td colspan=\"2\">90</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td colspan=\"2\">60 70 80 Acc</td><td>1</td><td>4</td><td>8</td><td>12</td><td>16 Layer</td><td>20</td><td>24</td><td>28</td><td>32 Steer entire layer Zero-shot PASTA Steer all heads Steer single head</td></tr><tr><td colspan=\"11\">5: The performance of LLAMA-7B on BiasBios task when we steer (i) all heads (green); (ii) entrie layer (yellow); and (iii) individual head with a layer (blue violin plot). The performance varies dramatically across layers and across heads of a layer.</td></tr><tr><td colspan=\"2\">100</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td/><td>80</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>ES</td><td>40 60</td><td>1</td><td>4</td><td>8</td><td>12</td><td>16 Layer</td><td>20</td><td>24</td><td>28</td><td>32 Steer entire layer Zero-shot PASTA Steer all heads Steer single head</td></tr></table>", "text": "The few-shot performance (Acc. / A. Acc. / Fluency) on the Pronouns Changing task.", "num": null, "html": null}, "TABREF10": {"type_str": "table", "content": "<table/>", "text": "Generation examples of LLAMA-7B on JSON Formatting.", "num": null, "html": null}, "TABREF11": {"type_str": "table", "content": "<table><tr><td>Method</td><td>Prons. Changing Acc /</td><td>BiasBios</td><td>CounterFact</td></tr></table>", "text": "Results of fluency and consistency evaluation on LLAMA-7B. Cons. / Flue. Acc / Cons. / Flue. ES / PS / Cons. / Flue. Zero-shot 71.84 / 22.29 / 6.10 87.36 / 13.02 / 3.98 58.50 / 52.03 / 11.64 / 4.96 PASTA 92.30 / 22.37 / 6.07 95.28 / 14.25 / 4.05 99.60 / 99.57 / 19.29 / 4.89", "num": null, "html": null}}}}