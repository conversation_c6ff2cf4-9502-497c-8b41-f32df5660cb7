{"paper_id": "IdentityChain", "title": "BEYOND ACCURACY: EVALUATING SELF-CONSISTENCY OF CODE LARGE LANGUAGE MODELS WITH IDENTITYCHAIN", "abstract": "Code Large Language Models (Code LLMs) are being increasingly employed in real-life applications, so evaluating them is critical. While the conventional accuracy evaluates the performance of Code LLMs on a set of individual tasks, their self-consistency across different tasks is overlooked. Intuitively, a trustworthy model should be self-consistent when generating natural language specifications for its own code and generating code for its own specifications. Failure to preserve self-consistency reveals a lack of understanding of the shared semantics underlying natural language and programming language, and therefore undermines the trustworthiness of a model. In this paper, we first formally define the selfconsistency of Code LLMs and then design a framework, IdentityChain, which effectively and efficiently evaluates the self-consistency and conventional accuracy of a model at the same time. We study eleven Code LLMs and show that they fail to preserve self-consistency, which is indeed a distinct aspect from conventional accuracy. Furthermore, we show that IdentityChain can be used as a model debugging tool to expose weaknesses of Code LLMs by demonstrating three major weaknesses that we identify in current models using IdentityChain. Our code is available at https://github.com/marcusm117/IdentityChain.", "pdf_parse": {"paper_id": "IdentityChain", "abstract": [{"text": "Code Large Language Models (Code LLMs) are being increasingly employed in real-life applications, so evaluating them is critical. While the conventional accuracy evaluates the performance of Code LLMs on a set of individual tasks, their self-consistency across different tasks is overlooked. Intuitively, a trustworthy model should be self-consistent when generating natural language specifications for its own code and generating code for its own specifications. Failure to preserve self-consistency reveals a lack of understanding of the shared semantics underlying natural language and programming language, and therefore undermines the trustworthiness of a model. In this paper, we first formally define the selfconsistency of Code LLMs and then design a framework, IdentityChain, which effectively and efficiently evaluates the self-consistency and conventional accuracy of a model at the same time. We study eleven Code LLMs and show that they fail to preserve self-consistency, which is indeed a distinct aspect from conventional accuracy. Furthermore, we show that IdentityChain can be used as a model debugging tool to expose weaknesses of Code LLMs by demonstrating three major weaknesses that we identify in current models using IdentityChain. Our code is available at https://github.com/marcusm117/IdentityChain.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Code Large Language Models (Code LLMs) are being increasingly employed in real-life applications (GitHub, 2023; OpenAI, 2023) . Hence, evaluating them rigorously is a crucial problem. Conventional evaluations of Code LLMs focus on the models' accuracy on a wide range of individual tasks (<PERSON> et al., 2021; <PERSON> et al., 2022) , primarily the following two: 1) Code Generation i.e. Natural Language to Programming Language (NL-to-PL) Generation: Given a natural language specification, the model is tasked to generate a corresponding program.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "2) Code Summarization i.e. Programming Language to Natural Language (PL-to-NL) Generation: Given a program, the model is tasked to generate a corresponding natural language specification. However, evaluating these two tasks in isolation overlooks their symmetric nature. NL-to-PL and PL-to-NL Generation can be thought of as semantic-preserving translation and back-translation between the PL space and the NL space. Therefore, a trustworthy model should be able to correctly perform PL-to-NL Generation given programs generated by itself from previous NL-to-PL tasks. Similarly, it should correctly perform NL-to-PL Generation given natural language specifications generated by itself from previous PL-to-NL tasks. We call such a property \"self-consistency\".", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Consider a real example shown in Figure 1 . is first instructed to generate a program pl 0 according to a specification nl 0 written in a docstring, and then instructed to summarize its own code pl 0 into a new docstring nl 1 . If we evaluate NL-to-PL and PL-to-NL Generation in isolation, GPT-3.5 is more than capable as it achieves 100% accuracy on both tasks. However, from the selfconsistency perspective, even though the model is self-consistent when generating nl 1 from pl 0 , Figure 1 : The IdentityChain Framework. Starting from a docstring nl 0 , instruct the model to generate a program pl 0 , summarize pl 0 into a new docstring nl 1 , and generate a new program pl 1 . If the test outputs of pl 1 do not match the ones of pl 0 , then the model is not self-consistent. This chain can be extended to length n ∈ N and we compute whether, for all i < n, the test outputs of pl i match the ones of pl i+1 , returning a binary result that indicates if the model is self-consistent regarding nl 0 . it surprisingly fails to preserve self-consistency when generating pl 1 from its own docstring nl 1 . Note that self-consistency is different from consistency: nl 1 here is generated by the model itself instead of arbitrarily crafted by humans or synthesized by other algorithms. This example reveals that GPT-3.5 doesn't understand the underlying semantics of the programs and natural language specifications, which raises a significant trustworthiness concern.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Unfortunately, current NL-to-PL evaluations (<PERSON> et al., 2021; <PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2023) typically assess if the model-generated programs pass a set of test cases, and current PL-to-NL evaluations (<PERSON> et al., 2021; <PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2023) commonly employ token-based metrics like BLEU (<PERSON><PERSON><PERSON> et al., 2002) , which both fail to take self-consistency into account. Although similar self-consistency properties of LLMs have been probed through some natural language tasks (<PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) , their evaluations rely on Closed-domain QA tasks and cannot be generalized to open-ended generation (Section 2). Therefore, in this paper:", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "1) We formalize the definition of self-consistency and its evaluation (Section 3).", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "2) We design a novel framework, IdentityChain (Section 4), which effectively and efficiently evaluates a Code LLM's self-consistency by employing a new metric, Test Output Match (TOM) score, and leveraging greedy decoding during inference. Through experiments, we exhibit the effectiveness of the TOM score (Section 6.2) and the efficiency of greedy decoding (Section 6.3).", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "3) We evaluate eleven current Code LLMs including GPT-4, showing that they are not always selfconsistent. Furthermore, we find that more accurate models are not necessarily more self-consistent, highlighting that self-consistency is a different aspect from conventional accuracy (Section 6.1). 4) We show through experiments that TOM score is also an effective metric to evaluate PL-to-NL Generation (Section 6.2), thus completing IdentityChain as a holistic framework that evaluates the NL-to-PL accuracy, PL-to-NL accuracy, and self-consistency of Code LLMs at the same time. We further discuss three major weaknesses of current models that we identify using IdentityChain, demonstrating the potential of IdentityChain as a debugging tool that helps model developers by exposing weaknesses of models and inspiring potential improvements (Section 6.4).", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "words, Abstract Syntax Tree, and Data-Flow Match into account, and CodeBERTScore (<PERSON> et al., 2023) computes a similarity score of code embeddings extracted by pre-trained Code LLMs. However, static code similarity doesn't reflect the dynamic semantics of programs, which gave rise to execution-based metrics like Pass@K (<PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2022) . Nonetheless, all existing NL-to-PL metrics focus only on the one-time accuracy while overlooking whether they are self-consistent regarding a model's own output. For PL-to-NL evaluation, BLEU (<PERSON><PERSON><PERSON> et al., 2002) score has been the automated metric adopted by most models (<PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON> et al., 2023) . Metrics like ROGUE (<PERSON>, 2004) , chrF (<PERSON>, 2015) , and BERTScore (<PERSON> et al., 2020) are also reasonable choices. However, these static metrics fail to capture semantics separately from syntax and require ground truth references for comparison. In this paper, we proposed a dynamic metric TOM score for self-consistency evaluation, showing that it is not only effective but also compatible with all existing evaluation benchmarks with test cases. We also show that TOM score effectively evaluates PL-to-NL Generation regardless of ground-truth references, outperforming all aforementioned PL-to-NL metrics.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Evaluating Self-Consistency of Large Language Models. Previous studies (<PERSON><PERSON><PERSON> & Riedel, 2018; <PERSON> et al., 2019; <PERSON><PERSON> & Ha<PERSON>i, 2020) show that LLMs behave inconsistently when given two semantically-bonded inputs.1 However, measuring those inconsistencies is different from evaluating a model's self-consistency since these inputs, either hand-crafted or algorithm-synthesized are not generated by the model itself. As LLMs become better at multitasking (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2022) , their self-consistency across tasks evolves into an important issue. <PERSON> et al. (2023) asks LLMs to generate the answer for an arithmetic reasoning problem, replace a variable in the original problem with an unknown x, and then instruct the same model to solve for x given the answer it previously generated. <PERSON><PERSON> et al. (2023) asks LLMs to translate a question from English to another language and instruct the same model to answer the questions in both languages. However, both evaluation settings above rely on tasks with fixed ground truths and cannot be generalized to open-ended generation tasks where there can be multiple ground truth answers with arbitrary lengths. In this paper, we evaluate Code LLMs on two major open-ended generation tasks: NL-to-PL and PL-to-NL Generation.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Given a model M that is capable of performing both NL-to-PL and PL-to-NL Generation, let n2p and p2n denote two instructions that respectively set M to perform NL-to-PL Generation or PL-to-NL Generation. In practice, the instructions n2p and p2n are usually prompts. Therefore, a model instructed to perform one of the two tasks can be defined as two functions:", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "M n2p : N L → PL M p2n : PL → N L where PL denotes the space of all valid programs in a specific programming language and N L denotes the space of all semantically valid and unambiguous2 program specifications in a specific natural language. For example, PL can be the space of all valid Python programs and N L can be the space of all valid and unambiguous corresponding English specifications of these programs, which is the setting for all experiments later in this paper.", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "Let nl 0 ∈ N L be a valid and unambiguous natural language specification, and pl 0 = M n2p (nl 0 ) be the program generated by the model M for nl 0 . If the model is accurate, then pl 0 and nl 0 should have the same underlying semantics. 3 If we further instruct the model to generate a specification nl 1 = M p2n (pl 0 ) given pl 0 , then the semantics of pl 1 , nl 1 , pl 0 should be all identical. We call such a property \"self-consistency\". Generally, a self-consistent model should be able to perform such translations between N L and PL infinitely many times without changing underlying semantics.", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "Note that self-consistency is a different property from accuracy. While accuracy assesses a model's ability to uni-directionally translate from N L to PL or the converse in a single step, self-consistency assesses the model's ability to bidirectionally translate between the two spaces in infinitely many steps. Therefore, a model can remain self-consistent even when it's inaccurate, as long as it consistently preserves the same error. Similarly, low self-consistency but high accuracy can also happen.", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "We can now formalize the above intuitions about the self-consistency of Code LLMs. Assume that given N L and PL, there exists a semantics space D (we don't assume any specific definition of D) s.t. an interpretation function sem is well-defined as the following:", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "sem : N L ∪ PL → D", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "which means that for all pl ∈ PL or nl ∈ N L, the interpretation function sem maps it uniquely to an element in D. We define the self-consistency property as the following:", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "Definition 1: Self-Consistency. Given a valid and unambiguous specification nl", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "0 ∈ N L, a model M is self-consistent w.r.t. nl 0 if and only if ∀i ∈ N, sem(pl i ) = sem(nl i+1 ) = sem(pl i+1 )", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "where", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "pl 0 = M n2p (nl 0 ), nl i+1 = M p2n (pl i ), pl i+1 = M n2p (nl i+1 )", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "Aligning with the informal intuitions, our definition doesn't consider the initial generation pl 0 to be semantically the same as nl 0 . As long as, for all i ∈ N, the three-tuple pl i , nl i+1 , and pl i+1 are semantically identical, we can say that M is self-consistent w.r.t. nl 0 . If pl 0 is semantically identical to nl 0 and the model is self-consistent w.r.t. nl 0 , we can say the model is \"strong self-consistent\", since if a model is always accurate, it must be self-consistent. We formally define it as:", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "Definition 2: Strong Self-Consistency. Given nl 0 ∈ N L, a model M is strong self-consistent w.r.t. nl 0 if and only if M is self-consistent w.r.t. nl 0 and sem(nl 0 ) = sem(pl 0 ), where pl 0 = M n2p (nl 0 ).", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "Similar to the above definitions, we can further define self-consistency and strong self-consistency w.r.t. an arbitrary pl 0 ∈ PL. Note that these two classes of definitions are not equivalent,4 but for simplicity, we adopt the self-consistency and strong self-consistency w.r.t. nl 0 ∈ N L definitions.", "section": "SELF-CONSISTENCY DEFINITION", "sec_num": "3.1"}, {"text": "Chain of Identity Transformations. Let a model M be self-consistent w.r.t. nl 0 . Instruct the model to generate pl 0 = M n2p (nl 0 ) and iteratively apply the PL-to-NL function to get nl i+1 = M p2n (pl i ) and the NL-to-PL function to get pl i+1 = M n2p (nl i+1 ). From the semantics perspective, alternatively applying the PL-to-NL and NL-to-PL functions on pl 0 for n ∈ N + times is equivalent to applying the identity transformation I in the semantics space D on sem(pl 0 ) for 2n times:", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "sem((M n2p • M p2n ) n (pl 0 )) = I 2n (sem(pl 0 ))", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "The chain of transformations on pl 0 between the language spaces N L and PL corresponds to a chain of identity transformations on sem(pl 0 ) within the semantics space D. In the equation above, the superscript n denotes the length of such an \"identity chain\".", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "Self-Consistency Scores. To evaluate the self-consistency of a model M , it's impossible to extend the identity chain infinitely long or exhaust all N L, so we approximate by picking a fixed chain length n ∈ N + and a reasonably large subset of N L with m ∈ N + elements as an evaluation set.", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "We index the inputs in the evaluation set by j ∈ N + , 1 ≤ j ≤ m. For an input nl 0,j in the evaluation set, we check its corresponding semantic equalities sem(pl i ) = sem(nl i+1 ) = sem(pl i+1 ) for all i ∈ N, 0 ≤ i < n. We use a binary output sc n,j ∈ {0, 1} to indicate whether all semantic equalities are true at the same time i.e. whether M is self-consistent w.r.t. nl 0.j within n steps. Similarly, we use ssc n,j ∈ {0, 1} to denote if M is strong self-consistent w.r.t. nl 0.j within n steps. Finally, by aggregating sc n,j and ssc n,j over all j, we can evaluate self-consistency and strong self-consistency of M within n steps by reporting two scores SC n and SSC n defined as the following:", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "SC n = m j=1 sc n,j m SSC n = m j=1 ssc n,j m 4 THE IDENTITYCHAIN FRAMEWORK 4.1 EFFECTIVE SELF-CONSISTENCY EVALUATION", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "Determining the truth value of the semantic equalities sem(pl i ) = sem(nl i+1 ) = sem(pl i+1 ) can be performed by humans. However, it's not feasible to employ human judgment when the evaluation set scales up. Consequently, we need automated metrics as approximations.", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "Inapplicability of Existing Automated Metrics. Ideal automated PL-to-NL and NL-to-PL metrics should map a program and a natural language specification to the semantic space, and directly compute their semantic distance. Given such ideal metrics, we can approximate or even determine the truth values of sem(pl i ) = sem(nl i+1 ) and sem(nl i+1 ) = sem(pl i+1 ). However, all existing metrics gauge the semantic equalities indirectly by computing a distance between the model-generated candidate and a predefined ground truth reference. Specifically, all existing NL-to-PL metrics compute a distance between two programs in the same programming language and all existing PL-to-NL metrics compute a distance between two specifications in the same natural language. Unfortunately, we do not have any predefined ground truth reference for either nl i+1 or pl i+1 .5 ", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "Relaxation of the Semantic Equalities. Recall that our goal is to approximate the truth value of the semantic equalities sem(pl i ) = sem(nl i+1 ) = sem(pl i+1 ). Although there are no existing metrics to approximate the truth values of sem(pl i ) = sem(nl i+1 ) or sem(nl i+1 ) = sem(pl i+1 ), the third equality sem(pl i ) = sem(pl i+1 ) is feasible to gauge. We can use existing NL-to-PL metrics to approximate this equality as they directly compute a distance between two programs in the same programming language. In addition, if the model summarizes pl i wrongly into a semantically different nl i+1 , then program pl i+1 , which is supposed to be semantically identical to nl i+1 , is highly unlikely to have the exact same semantics as pl i and vice versa. Therefore, any effective NLto-PL metric, which approximates the truth value of sem(pl i ) = sem(pl i+1 ), can be also considered as an effective approximation to that of sem(pl i ) = sem(nl i+1 ). In Table 2 , we empirically show that there is a positive correlation between them.", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "Design of the Test Output Match (TOM) Score. While all NL-to-PL metrics have the potential to be self-consistency evaluation metrics, we want to pick one that best approximates the semantic equality sem(pl i ) = sem(pl i+1 ). As reviewed in Section 2, execution-based dynamic metrics like Pass/Fail can directly, though not complete, gauge the code semantics, and are therefore more preferred than static metrics like CodeBLEU. In Table 2 , we empirically verify this conclusion.", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "The most widely used dynamic metric, Pass@K, is not directly applicable to self-consistency evaluation. Whether pl i passes or fails the test cases does not imply whether it is semantically identical to pl i+1 and vice versa, so naturally, we come up with a new metric, the Pass/Fail Match (P/FM) score, which checks if pl i and pl i+1 both pass or both fail at the same time. If both of them pass all test cases, they must be semantically identical. If one passes while the other fails, they must be semantically different. However, P/FM doesn't handle the Fail-Fail situation well since pl i and pl i+1 can fail the same test case due to completely different reasons.", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "We, therefore, propose another new metric, the Test Output Match (TOM) score, which compares the exact output of pl i and pl i+1 for each test case, records 1 if the outputs match and 0 if the outputs differ, and finally computes the percentage of matches among all test cases.", "section": "SELF-CONSISTENCY EVALUATION", "sec_num": "3.2"}, {"text": "For syntax errors and runtime errors like ValueError or IndexError, the TOM score is calculated by comparing the full error message instead of just the error type. By capturing more fine-granular semantic information, TOM score better approximates the truth value of sem(pl i ) = sem(pl i+1 ) than the simple P/FM score. In Table 2 , we show that TOM indeed better correlates to the humanjudged truth value, and therefore is an effective metric for self-consistency evaluation.", "section": "TOM = Number of Matched Outputs Total Number of Test Cases", "sec_num": null}, {"text": "Efficient Evaluation by Greed<PERSON> Decoding. To evaluate self-consistency up to a certain chain length n, we use greedy decoding for both NL-to-PL and PL-to-NL Generation. Given a starting point nl 0 , if at some step i in the chain, pl i+1 is an exact match of pl i , or nl i+1 is an exact match of nl i , then by the deterministic nature of greedy decoding, we know that the model will always generate the same program and specification repeatedly. In such cases, we can assert that the model is selfconsistent w.r.t. pl i or nl i (not necessarily nl 0 ). Therefore, our IdentityChain framework adopts greedy decoding and stops the chain early when exact matches are found. We show in Figure 2 that, with greedy decoding and early stopping, self-consistent cases can be quickly determined.", "section": "EFFICIENT SELF-CONSISTENCY EVALUATION", "sec_num": "4.2"}, {"text": "The IdentityChain framework not only effectively and efficiently evaluates the self-consistency of a Code LLM, but also holistically evaluates multiple aspects of a model at the same time.", "section": "HOLISTIC EVALUATION OF CODE LLMS", "sec_num": "4.3"}, {"text": "NL-2-PL Accuracy. The bootstrapping step from nl 0 to pl 0 is exactly the canonical NL-to-PL evaluation setting, where we can compute the Pass@1 score to evaluate the model's NL-to-PL accuracy.", "section": "HOLISTIC EVALUATION OF CODE LLMS", "sec_num": "4.3"}, {"text": "PL-2-NL Accuracy. Unlike NL-to-PL metrics, existing PL-to-NL metrics are all static and therefore struggle to capture underlying semantics. As discussed in Section 4.1, by back-translating a model-generated natural language specification into another program, we can approximate the semantic equality between the original program and the specification. Therefore, the SC 1 score i.e. the averaged TOM score between all pl 0 and pl 1 , can be an effective metric for the model's PL-to-NL accuracy. In Table 2 , we empirically show that TOM outperforms all existing PL-2-NL metrics.", "section": "HOLISTIC EVALUATION OF CODE LLMS", "sec_num": "4.3"}, {"text": "Strong Self-Consistency. An ideal model should be both accurate and self-consistent. An accurate but not self-consistent model is not trustworthy, while a self-consistent but not accurate model is useless. The strong self-consistency score SSC n takes both accuracy and self-consistency into account, which serves as a comprehensive evaluation of the model's overall performance.", "section": "HOLISTIC EVALUATION OF CODE LLMS", "sec_num": "4.3"}, {"text": "Model developers can first check the SSC n score as a performance summary and then examine the SC n , Pass@1, and SC 1 scores to determine whether the model is lacking more accuracy or selfconsistency. More importantly, with IdentityChain, it's easy to pinpoint cases where a model is not self-consistent to reveal subtle weaknesses of the model, as we will show in Section 6.4.", "section": "HOLISTIC EVALUATION OF CODE LLMS", "sec_num": "4.3"}, {"text": "Benchmarks. We evaluate the self-consistency of Code LLMs on two widely adopted benchmarks: HumanEval and MBPP. HumanEval (<PERSON> et al., 2021) 2021). In both datasets, all problems have predefined meaningful function names, for example, \"has close elements\". If the model generates an incorrect function body at the initial step, there can be a conflict between the semantics of the function body and the name, which weakens the soundness of self-consistency evaluation. Therefore, we replace meaningful function names with a generic \"func\" at all steps except the initial one, so that the model solely relies on the semantics of the function body or docstring instead of taking shortcuts using the function name. See Appendix C for a concrete example.", "section": "EXPERIMENTS", "sec_num": "5"}, {"text": "Models. We evaluate two types of Code LLMs: foundation models and instruction-tuned models.", "section": "EXPERIMENTS", "sec_num": "5"}, {"text": "For foundation models, we evaluate two open-source model families, StarCoderBase (<PERSON> et al., 2023) and Code Llama (<PERSON><PERSON><PERSON> et al., 2023) . For instruction-tuned models, we evaluate the instructiontuned versions of Code Llama and StarCoderBase, Google's Gemini-1.0-Pro-0016 (Team, 2023) , and three most capable OpenAI models: GPT-3.5-Turbo-0613, GPT-4-0613, and GPT-4-0125-Preview (the latest GPT-4-Turbo snapshot). For models from Google and OpenAI, we choose the parameter-frozen snapshots of them so that the results can be reproduced.", "section": "EXPERIMENTS", "sec_num": "5"}, {"text": "Prompts. We use one-shot prompting for all the models on both benchmarks to better guide the model to generate the expected format.7 For instruction-tuned models, we formulate the prompt as chats (<PERSON><PERSON><PERSON> et al., 2022) , where the \"system\" role provides general instructions, the \"user\" role provides the input of the one-shot example, and the \"assistant\" role provides the output of the one-shot example. For foundation models, the prompt is only the one-shot example. To maximize the capacity of all Code LLMs, we carefully customize the prompt template for each model. See the \"examples\" folder in our code repository for details of the prompt templates. See Appendix B for detailed hardware and software configurations of all experiments.", "section": "EXPERIMENTS", "sec_num": "5"}, {"text": "Code LLMs Fail to Preserve Self-Consistency. We observe in Table 1 that all models' selfconsistency and strong self-consistency decreases as the number of iteration steps increases. For example, all models' SSC 5 scores, which assess strong self-consistency within five steps, evidently decline up to 78.0% compared to the initial Pass@1.8 Regardless of the accuracy of the initial generation, all models' SC 5 scores, which assess self-consistency within five steps, also decline up to 43.8% compared to SC 1 . Such a performance drop indicates that while the models might be initially (strong) self-consistent, they are not able to preserve it. In Section 6.4, we delve deeper into the some of root-cause errors that trigger violations of (strong) self-consistency. Table 1 : Performance of Code LLMs evaluated by IdentityChain. Pass@1 indicates the NL-to-PL accuracy. SC 1 representing self-consistency within 1 step indicates PL-to-NL accuracy. SC 5 represents self-consistency within 5 steps and SSC 5 represents strong self-consistency within 5 steps.", "section": "SELF-CONSISTENCY OF CODE LLMS", "sec_num": "6.1"}, {"text": "Self-Consistency is Different from Conventional Accuracy. Existing evaluations of Code LLMs refer to conventional accuracy (e.g. Pass@K) as the model's overall capacity, which is confirmed by our results in Table 1 : larger models in the same model families indeed have higher Pass@1 scores. However, results in Table 1 show that stacking more parameters does not necessarily guarantee improvement of self-consistency. For example, the Pass@1 score of StarChat-Beta (15B), which indicates accuracy, is higher than Code Llama-Instruct-7B for both benchmarks, but the SC 5 score of the former, which indicates self-consistency, is lower than the latter for both benchmarks. For another example, while StarCoderBase-7B performs worse than StarCoderBase-15B in Pass@1 for both benchmarks, it outperforms the double-sized version of itself in SSC 5 , which indicates strong self-consistency, for both benchmarks.", "section": "SELF-CONSISTENCY OF CODE LLMS", "sec_num": "6.1"}, {"text": "Moreover, conventional accuracy can underestimate the capability difference between models, and self-consistency complements the drawback. For example, GPT-4, which is recognized to be significantly more capable than GPT-3.5, reports a Pass@1 score of 74.8 on HumanEvalPlus, which is only a 4.2% relative improvement compared to GPT-3.5. However, GPT-4 is significantly more self-consistent. It achieves an SC 5 score of 76.1, which is 51.2% higher than GPT-3.5, highlighting that there is a non-trivial capability gap between GPT-4 and GPT-3.5.", "section": "SELF-CONSISTENCY OF CODE LLMS", "sec_num": "6.1"}, {"text": "To show the effectiveness of TOM, we excerpt a 1-step chain (nl 0 , pl 0 , nl 1 , pl 1 ) from an Identity-Chain experiment of GPT-3.5, and gathered human-judged ground truth of whether nl 1 is semantically identical to pl 0 i.e. sem(pl 0 ) = sem(nl 1 ).9 Table 2 : <PERSON> (r), <PERSON><PERSON><PERSON> (ρ), and <PERSON><PERSON><PERSON> (τ ) correlations with human-judged ground truth of whether pl 0 is semantically identical to nl 1 , the model-generated docstring for pl 0 .", "section": "EFFECTIVENESS OF TOM SCORE", "sec_num": "6.2"}, {"text": "TOM is Effective for Self-Consistency Evaluation. We compared TOM to two static PL space metrics: Exact Match (EM) and CodeBLEU and the naive dynamic metric Pass/Faill Match (P/FM) using <PERSON> (r), <PERSON><PERSON><PERSON> (ρ), and <PERSON><PERSON><PERSON> (τ ) correlations with human judgment in Table 2. Recall our conjectures in Section 4.1: PL space metrics can approximate the truth value of sem(pl 0 ) = sem(nl 1 ), dynamic metrics are better than static ones, and the fine-grained TOM score is better than naive P/FM. All three conjectures are verified in this experiment.", "section": "Metric", "sec_num": null}, {"text": "TOM is Effective for PL-to-NL Evaluation. Within the same experiment setting, we compare TOM with four NL space metrics: BLEU, ROUGE-L, chrF, and BERTScore in Table 2 . Note that for this comparison, the correlations are only computed on 117 out of the total 163 problems where pl 0 passes all test cases. Otherwise, if pl 0 is not semantically the same as nl 0 , we can't use nl 0 as a ground truth reference for nl 1 to calculate those NL space metrics. We show that TOM outperforms all NL space metrics given that their ground truth references exist, not to mention that TOM works well for the remaining 46 problems, for which the ground truth references are absent. Greedy Decoding Efficiently Evaluates Self-Consistency. We find that using greedy decoding, IdentityChain efficiently reveals most not self-consistent cases within the initial three steps. Figure 2 shows an evident decline of both SC i and SSC i scores within the first three steps. After that, all models stabilize or show only minimal decreases in their (strong) self-consistency scores, which underscores the efficiency of IdentityChain as an evaluation tool.", "section": "Metric", "sec_num": null}, {"text": "Although we set the chain length to five in our experiments, for mode developers and researchers with tighter time limits or computing resources, it's reasonable to choose a shorter chain length when using greedy decoding. Greedy Decoding Results are Generalizable to Different Temperatures. To show the generalizability of greedy decoding results, we additionally evaluate the SC 5 score at four different temperatures. As illustrated in Figure 3 , while the SC 5 scores of all models decrease as the temperature increases, their relative rankings mostly remain i.e. more self-consistent models are always more self-consistent regardless of temperature, which shows that the greedy decoding results are indeed generalizable. Moreover, it is reasonable that the absolute self-consistency of all models drops as the temperature increases.", "section": "EFFICIENCY OF GREEDY DECODING", "sec_num": "6.3"}, {"text": "There is always a balance between exploration, which introduces novel solutions but weakens selfconsistency, and exploitation, which ensures self-consistency but may overlook novel solutions.", "section": "EFFICIENCY OF GREEDY DECODING", "sec_num": "6.3"}, {"text": "From our observations, greedy decoding is both more efficient and more appropriate for selfconsistency evaluation. See Appendix D for the SSC 5 scores evaluated at different temperatures.", "section": "EFFICIENCY OF GREEDY DECODING", "sec_num": "6.3"}, {"text": "Evaluating Code LLMs with IdentityChain, we can easily pinpoint the cases where the model is not self-consistent. Studying these non-self-consistent cases, we identify three major weaknesses of current models in code understanding, which are not captured by accuracy-oriented evaluations.", "section": "IDENTITYCHAIN AS A MODEL DEBUGGING TOOL", "sec_num": "6.4"}, {"text": "Code LLMs Have Weak Sense of Data Types. We observe that Code LLMs are not sensitive to data types. In all programming languages, data type is a fundamental element that specifies how variables should be stored, manipulated, and interacted with. However, we find that current models tend to overlook data type information. We show such an example in Appendix Figure 6 . Inaccurate interpretations of data types will inevitably result in erroneous code usage. In real software development scenarios, it can lead to intricate issues like memory management, performance bottlenecks, or unexpected behaviors during code execution (<PERSON> et al., 2022; <PERSON> & <PERSON>, 2023) .", "section": "IDENTITYCHAIN AS A MODEL DEBUGGING TOOL", "sec_num": "6.4"}, {"text": "Code LLMs Have Weak Sense of Implicit Code Semantics. We observe that Code LLMs cannot accurately capture the implicit code semantics, which is a major root cause of non-self-consistency. Current models tend to only capture the shallow semantics that are explicitly presented in the program while overlooking the implicit logic. For example, they tend to only summarize the explicit if-checks while ignoring the implicit else-branch. We show two concrete examples in Appendix Figure 7 . Ignoring implicit code semantics during PL-to-NL Generation will unavoidably result in misleading or ambiguous documentation in real development scenarios.", "section": "IDENTITYCHAIN AS A MODEL DEBUGGING TOOL", "sec_num": "6.4"}, {"text": "Code LLMs Have Weak Sense of Code Execution. We also observe that Code LLMs cannot accurately predict the execution outcomes <PERSON> et al. (2021) ; <PERSON><PERSON> et al. (2023a) . Specifically, when instructed to summarize programs, the models often generate correct natural language specifications but incorrect input-output examples. We show two concrete examples in Appendix Figure 8 . This weakness is particularly concerning if we want to generate test cases to guide the entire software development process (Test-Driven Development), which underscores the importance of aligning the models' PL-to-NL Generation ability with their understanding of code execution.", "section": "IDENTITYCHAIN AS A MODEL DEBUGGING TOOL", "sec_num": "6.4"}, {"text": "In conclusion, we reveal that different from accuracy, self-consistency is indeed a crucial missing link in current evaluations of Code LLMs, and IdentityChain effectively and efficiently bridges the gap. More importantly, IdentityChain can be used not only as a holistic evaluation tool but also as a model debugging tool that helps model developers study weaknesses in their models and thus potentially inspire future improvements. See Appendix A for future directions that potentially extend the scope of self-consistency evaluation or improve current Code LLMs using IdentityChain.", "section": "CONCLUSION", "sec_num": "7"}, {"text": "Introducing PL-to-PL and NL-to-NL Generation. ", "section": "A FUTURE WORK", "sec_num": null}, {"text": "For all models, we use greedy decoding for our main experiment in Section 6.1. The closed-source OpenAI models GPT-3.5 and GPT-4 are non-deterministic and there is no way to set them to perform greedy decoding using APIs. Therefore, we set the temperature to 0 to minimize the randomness.", "section": "B EXPERIMENT CONFIGURATIONS", "sec_num": null}, {"text": "For open-source models, all model checkpoints are downloaded using the Python library \"transformers\" from Hugging Face. Note that we downloaded Code Llama and Code Llama-Instruct, from https://huggingface.co/codellama instead of the link provided by Meta AI. We run open-source model experiments on NVIDIA RTX A6000 GPUs with CUDA 11.3, cuDNN8devel, PyTorch 1.12.1, and Python 3.10.9. For efficiency, we set the max prompt length to be 1,024 tokens, the max generation length to be 512 tokens, and the inference precision to be FP16. In this case, we will falsely conclude that GPT-3.5 is not self-consistent. However, when summarizing the program along with a generic name \"func\" in replacement, GPT-3.5 correctly captures the code semantics and thus is self-consistent w.r.t. the original docstring. Therefore, when generating nl i and pl i for i ≥ 1, we replace the original meaningful function name with the generic \"func\".", "section": "B EXPERIMENT CONFIGURATIONS", "sec_num": null}, {"text": "D GENERALIZABILITY OF GREEDY DECODING Figure 7 : Code LLMs Have Weak Sense of Implicit Code Semantics. In the left example, the implementation has an implicit \"else\" branch that returns -1 when no even number is found. In the right example, the implementation also has an implicit \"else\" branch that returns -1 when no larger satisfying number is found. However, both summarizations fail to capture that implicit logic. ", "section": "B EXPERIMENT CONFIGURATIONS", "sec_num": null}, {"text": "One input entails, contradicts, or is identical to the other.", "section": "", "sec_num": null}, {"text": "Nonsensical and ambiguous text is important in natural languages, but for NL-PL tasks, it makes more sense to only consider a subset of the natural language that validly and unambiguously specifies programs.", "section": "", "sec_num": null}, {"text": "Aside from program semantics i.e. input-output behavior, nl0 and pl0 should be also aligned regarding pragmatic aspects like complexity, security, and human readability. In this paper, our scope is just the semantics.", "section": "", "sec_num": null}, {"text": "Self-consistency w.r.t. all nl0 ∈ N L doesn't imply self-consistency w.r.t. all pl0 ∈ PL. The converse is also not true. The NL-to-PL function Mn2p can simply map all nl0 to the exact same pl0, where M is strong self-consistent w.r.t. pl0. No claim can be made about <PERSON> 's self-consistency w.r.t. the entire PL space.", "section": "", "sec_num": null}, {"text": "Taking nli or pli as the ground truth reference for nli+1 or pli+1 is not generally applicable. For example, if pl0 fails some test cases, then nl1 = Mp2n(pl0), which is supposed to be semantically identical to pl0, must be semantically different from nl0. Therefore, nl0 cannot be seen as the ground truth for nl1.", "section": "", "sec_num": null}, {"text": "We set the temperature to 0.2 for Gemini since the API sometimes returns no response due to its recitation or safety filtering mechanism. To compare with other models using temperature 0.2, see Figure3and 5.", "section": "", "sec_num": null}, {"text": "For MBPP, we use task 2 in the prompt split as the one-shot example. For HumanEvalPlus, since there's no dedicated prompt split, we use HumanEval/0 as the one-shot example and exclude it from experiments.", "section": "", "sec_num": null}, {"text": "For Code Llama-Instruct and Code Llama 7B, the Pass@1 we measured are noticeably different from those reported by<PERSON><PERSON><PERSON> et al. (2023). We conjecture that it might be caused by the models' sensitivity to prompts.", "section": "", "sec_num": null}, {"text": "Different from the grading policy used in <PERSON> et al. (2021), which ignores incorrect input-output examples in the generated docstrings, we consider a docstring with correct description but wrong examples still wrong.", "section": "", "sec_num": null}], "back_matter": [{"text": "We would like to thank <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON> for valuable discussions. This work was supported in part by an IBM Ph.D. Fellowship, DARPA/NIWC-Pacific N66001-21-C-4018, NSF CNS-1845995, CNS-2247370, CCF-2221943, CCF-2313055, CCF-1845893, and CCF-2107405. Any opinions, findings, conclusions, or recommendations expressed herein are those of the authors and do not necessarily reflect those of IBM, DARPA, or NSF.", "section": "ACKNOWLEDGEMENT", "sec_num": null}], "ref_entries": {"FIGREF0": {"uris": null, "fig_num": "2", "num": null, "text": "Figure 2: SSC i and SC i at Computed Each Step i.", "type_str": "figure"}, "FIGREF1": {"uris": null, "fig_num": "3", "num": null, "text": "Figure 3: SC 5 Evaluated at Different Temperatures.", "type_str": "figure"}, "FIGREF2": {"uris": null, "fig_num": "4", "num": null, "text": "Figure4: Replacing Meaningful Function Names with A Generic \"func\". Given the docstring with the original function name, GPT-3.5 generates an incorrect program that conflicts with the function name. When further summarizing that program along with the original function name, GPT-3.5 completely ignores the code and generates a new docstring based on the function name. In this case, we will falsely conclude that GPT-3.5 is not self-consistent. However, when summarizing the program along with a generic name \"func\" in replacement, GPT-3.5 correctly captures the code semantics and thus is self-consistent w.r.t. the original docstring. Therefore, when generating nl i and pl i for i ≥ 1, we replace the original meaningful function name with the generic \"func\".", "type_str": "figure"}, "FIGREF3": {"uris": null, "fig_num": "5", "num": null, "text": "Figure5: SSC 5 Evaluated at Different Temperatures. Similar to the SC 5 results in Section 6.3, for the strong self-consistency score SSC 5 , the relative rankings of models mostly remain regardless of temperature i.e. more strong self-consistent models are always more strong self-consistent no matter the temperature, which confirms that greedy results are generalizable to different temperatures.", "type_str": "figure"}, "FIGREF4": {"uris": null, "fig_num": "6", "num": null, "text": "Figure 6: Code LLMs Have Weak Sense of Data Types. The implementation checks whether all three inputs are type int at the same time, but the summarization only mentions that the inputs are three \"numbers\" failing to capture the data type information.", "type_str": "figure"}, "FIGREF5": {"uris": null, "fig_num": "8", "num": null, "text": "Figure 8: Code LLMs Have Weak Sense of Code Execution. In both examples, some input-output pairs in the summarization are wrong, which means that the model fails to predict execution.", "type_str": "figure"}, "TABREF0": {"num": null, "content": "<table/>", "text": "contains 164 hand-crafted Python problems.<PERSON> et al. (2023) proposes HumanEvalPlus to augment HumanEval with more test coverage. Specifically, we use HumanEvalPlus-Mini-v0.1.6 where each problem has 16.5 test cases on average.MBPP Austin et al. (2021)  includes 974 crowd-sourced Python problems with 3.0 test cases for each problem on average. For more precise evaluations, we use the test split of the sanitized version of MBPP, which contains 257 problems manually verified by<PERSON><PERSON><PERSON> et al. (", "html": null, "type_str": "table"}, "TABREF3": {"num": null, "content": "<table/>", "text": "It is natural to extend the self-consistency definitions on a large set of multiple programming and natural languages by introducing PL-to-PL Generation i.e. Code Translation and NL-to-NL Generation i.e. Machine Translation. In practice, IdentityChain can be improved to support more programming languages and natural languages.Studying Weaknesses of Code LLMs. Following the three examples in Section 6.4, future work can further identify and categorize more subtle weaknesses in Code LLMs. More importantly, we encourage future work to investigate the relationship between those weaknesses and the training data. It is possible that the weaknesses are barely addressed within current training paradigms.Fine-tuning Code LLMs for Better Self-Consistency. It is not yet clear how we can improve the self-consistency of Code LLMs. For a model with imbalanced NL-to-PL and PL-to-NL accuracy, fine-tuning the task that the model performs worse can possibly work. For a model with balanced accuracy, we might need to customize a fine-tuning dataset that contains input-output pairs generated by the model itself. Many fair hypotheses can be made following this line of reasoning. We encourage future work to raise more and test them accordingly.", "html": null, "type_str": "table"}}}}