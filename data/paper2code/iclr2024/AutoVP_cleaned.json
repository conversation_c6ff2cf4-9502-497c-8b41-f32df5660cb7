{"paper_id": "AutoVP", "title": "AUTOVP: AN AUTOMATED VISUAL PROMPTING <PERSON>RA<PERSON><PERSON>OR<PERSON> AND <PERSON><PERSON><PERSON>MARK", "abstract": "Visual prompting (VP) is an emerging parameter-efficient fine-tuning approach to adapting pre-trained vision models to solve various downstream imageclassification tasks. However, there has hitherto been little systematic study of the design space of VP and no clear benchmark for evaluating its performance. To bridge this gap, we propose AutoVP, an end-to-end expandable framework for automating VP design choices, along with 12 downstream image-classification tasks that can serve as a holistic VP-performance benchmark. Our design space covers 1) the joint optimization of the prompts; 2) the selection of pre-trained models, including image classifiers and text-image encoders; and 3) model output mapping strategies, including nonparametric and trainable label mapping. Our extensive experimental results show that AutoVP outperforms the best-known current VP methods by a substantial margin, having up to 6.7% improvement in accuracy; and attains a maximum performance increase of 27.5% compared to linear-probing (LP) baseline. AutoVP thus makes a two-fold contribution: serving both as an efficient tool for hyperparameter tuning on VP design choices, and as a comprehensive benchmark that can reasonably be expected to accelerate VP's development. The source code is available at https://github.com/IBM/AutoVP.", "pdf_parse": {"paper_id": "AutoVP", "abstract": [{"text": "Visual prompting (VP) is an emerging parameter-efficient fine-tuning approach to adapting pre-trained vision models to solve various downstream imageclassification tasks. However, there has hitherto been little systematic study of the design space of VP and no clear benchmark for evaluating its performance. To bridge this gap, we propose AutoVP, an end-to-end expandable framework for automating VP design choices, along with 12 downstream image-classification tasks that can serve as a holistic VP-performance benchmark. Our design space covers 1) the joint optimization of the prompts; 2) the selection of pre-trained models, including image classifiers and text-image encoders; and 3) model output mapping strategies, including nonparametric and trainable label mapping. Our extensive experimental results show that AutoVP outperforms the best-known current VP methods by a substantial margin, having up to 6.7% improvement in accuracy; and attains a maximum performance increase of 27.5% compared to linear-probing (LP) baseline. AutoVP thus makes a two-fold contribution: serving both as an efficient tool for hyperparameter tuning on VP design choices, and as a comprehensive benchmark that can reasonably be expected to accelerate VP's development. The source code is available at https://github.com/IBM/AutoVP.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Originating in the domain of natural language processing, prompting (<PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON> et al., 2023) has gained considerable popularity as a parameter-efficient fine-tuning approach for adapting pre-trained models to downstream tasks. Prompting's methodology has recently been extended to the field of computer vision, where it is termed visual prompting (VP) (<PERSON><PERSON> et al., 2022) . In its simplest form, VP can be perceived as an in-domain model-reprogramming technique (<PERSON>, 2022) . More specifically, it adjusts the inputs and outputs of a pre-trained vision model to address downstream image-classification tasks, without having to make any changes to the weights or architecture of the source model's pre-trained backbone. As such, it stands in contrast to conventional transfer-learning methods that involve complete fine-tuning, LP (i.e., involving modifications of the trainable linear layer in the penultimate layer's output), or zero-shot learning (<PERSON><PERSON> et al., 2021) . For instance, as illustrated in Figure 1 , VP adds a universal trainable data frame to the target samples at the model-input stage, and then employs a mapping function -which can be either explicitly defined or implicitly learned -to associate the source and target labels at the output stage.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "While VP exhibits tremendous potential, there are two critical challenges that limit its research and development. The first is absence of a systematic VP framework. That is, VP design choices, such as prompts' sizes and shapes, source models, and label-mapping (LM) strategies, have thus far only been studied separately, generally for the purpose of delineating their distinct roles in enhancing downstream task accuracy. Ideally, such a systematic framework would automatically search for the best configurations for performance optimization. For example, <PERSON><PERSON> et al. (2022) have demonstrated that changing the padding size of visual prompts can yield around 15% variation in final accuracy. It has also been observed that VP is better at augmenting large text-image models, such as CLIP (<PERSON><PERSON> et al., 2021) , than pure vision models like ResNet50 (<PERSON> et al., 2016) . In a study by <PERSON> et al. (2023b) , iterative label mapping (ILM) during training achieved accuracy up to < l a t e x i t s h a 1 _ b a s e 6 4 = \" a J 8 i r y t / J j 2 j L y G I s 2023b)) on all 12 different downstream image-classification tasks.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "X o V k m F J H R 0 = \" > A A A B 6 3 i c b V B N S w M x E J 2 t X 7 V + V T 1 6 C Z a C p 7 I r V X s R C l 4 8 V r A f 0 C 4 l m 2 b b 0 C S 7 J F m h L P 0 L X j w o 4 t U / 5 M 1 / Y 7 b d g 7 Y + G H i 8 N 8 P M v C D m T B v X / X Y K G 5 t b 2 z v F 3 d L e / s H h U f n 4 p K O j R B H a J h G P V C / A m n I m a d s w w 2 k v V h S L g N N u M L 3 L / O 4 T V Z p F 8 t H M Y u o L P J Y s Z A S b T I p v 6 4 1 h u e L W 3 A X Q O v F y U o E c r W H 5 a z C K S C K o N I R j r f u e G x s / x c o w w u m 8 N E g 0 j T G Z 4 j H t W y q x o N p P F 7 f O U d U q I x R G y p Y 0 a K H + n k i x 0 H o m A t s p s J n o V S 8 T / / P 6 i Q k b f s p k n B g q y X J R m H B k I p Q 9 j k Z M U W L 4 z B J M F L O 3 I j L B C h N j 4 y n Z E L z V l 9 d J 5 7 L m X d e u H u q V Z j W P o w h n c A 4 X 4 M E N N O E e W t A G A h N 4 h l d 4 c 4 T z 4 r w 7 H 8 v W g p P P n M I f O J 8 / R Z K N q A = = < / l a t e x i t > p = 48 < l a t e x i t s h a 1 _ b a s e 6 4 = \" c M / r u m S p C u + g 4 Q Z X 3 Y 4 y 3 H E u C R k = \" > A A A B / H i c j V D L S s N A F J 3 4 r P U V L b h x M 1 g E V y U R X 8 u C G 5 c V 7 A P a U C b T S T p 0 M h N m b p Q Q 6 q + 4 c a G I W z / E n X / j 9 L F Q U f D A h c", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "K N G V N q o T S n Z A Y J r h k T e A g W C f V j C S h Y O 1 w d D n x 2 7 d M G 6 7 k D e Q p C x I S S x 5 x S s B K f b f S E 0 r G g k W g e T w E o r W 6 6 7 t V v + Z N g f 8 m V T R H o + + + 9 w a K Z g m T Q A U x p u t 7 K Q Q F 0 c C p Y O N y L z M s J X R E Y t a 1 V J K E m a C Y h h / j Q 6 s M c K S 0 H Q l 4 q n 6 9 K E h i T J 6 E d j M h M D Q / v Y n 4 m 9 f N I L o I C i 7 T D J i k s 0 d R J j A o P G k C D 7 h m F E R u C a G a 2 6 y Y D o k m F G x f 5 f + V 0 D q u + W e 1 0 + u T a n 1 v X k c J 7 a M D d I R 8 d I 7 q 6 A o 1 U B N R l K M H 9 I S e n X v n 0 X l x X m e r C 8 7 8 p o K + w X n 7 B K P U l U o = < / l a t e x i t > ! < l a t e x i t s h a 1 _ b a s e 6 4 = \" H X e C B y O b s d J g R R h C j P 7 C V d 3 f j v k = \" > A A A B 6 H i c j V D L S g N B E O y N r x h f U Y 9 6 G A y C p 7 A r v o 4 B L x 4 T M A 9 I l j A 7 6 U 3 G z M 4 u M 7 N C W P I F X j w o 4 t V P 8 u b f O N n k o K J g Q U N R 1 U 1 3 V 5 A I r o 3 r f j i F p e W V 1 b X i e m l j c 2 t 7 p 7 y 7 1 9 J x q h g 2 W S x i 1 Q m o R s E l N g 0 3 A j u J Q h o F A t v B + H r m t + 9 R a R 7 L W z N J 0 I / o U P K Q M 2 q s 1 E j 6 5 Y p X d X O Q v 0 k F F q j 3 y + + 9 Q c z S C K V h g m r d 9 d z E + B l V h j O B 0 1 I v 1 Z h Q N q Z D 7 F o q a Y T a z / J D p + T Y K g M S x s q W N C R X v 0 5 k N N J 6 E g W 2 M 6 J m p H 9 6 M / E 3 r 5 u a 8 M r P u E x S g 5 L N F 4 W p I C Y m s 6 / J g C t k R k w s o U x x e y t h I 6 o o M z a b 0 v 9 C a J 1 W v Y v q e e O s U j t c x F G E A z i C E / D g E m p w A 3 V o A g O E B 3 i C Z + f O e X R", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "13.7% better than fixed label mapping strategies. The second critical challenge is lack of a unified performance benchmark: the existing literature evaluates the performance of proposed VP methods in an ad hoc manner, by reporting on arbitrarily selected downstream datasets, making comparisons across different methods difficult at best.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "To bridge this gap, this paper proposes AutoVP, a solution addressing both these challenges via 1) its automated, extendable framework for joint optimization of a) input-image scaling (i.e., prompt size), b) visual prompts, c) source model selection, and d) output label-mapping strategies; and 2) its provision of a unified benchmark consisting of 12 diverse image-classification tasks with quantifiable content-similarity relative to the dataset (e.g., ImageNet) used for source model pre-training.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "As shown in Figure 1 , the first component (i.e., input scaling) of AutoVP determines the optimal ratio between the sizes of prompts and the original images. The second, visual prompts, serve as trainable parameters, and undergo iterative updates during the backpropagation phase. The pre-trained model extracts pertinent features and renders predictions within the source domain; and finally, output label mapping establishes a connection between the label spaces of the source and target domains, facilitating accurate predictions in the downstream domain. The modularity of AutoVP allows for the seamless integration and easy extension of various designs for these four components.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Table 1 compares AutoVP against prior VP proposals and the other two baselines proposed to date: LP and text-prompt (TP)-based zero-shot inference. As the table shows, AutoVP is the only such framework that considers the broad range of settings that can affect VP performance. Moreover, thanks to such settings' collective optimization, AutoVP's configuration amounts to a breakthrough in average accuracy across 12 distinct downstream tasks. For instance, with CLIP as the pre-trained model (see Table 2 ), AutoVP's average accuracy is 4.6% higher than CLIP-VP's (<PERSON><PERSON> et al., 2022) and 2.1% higher than ILM-VP's (<PERSON> et al., 2023b) . AutoVP also surpasses LP's accuracy by 0.7% on average, suggesting that it is a competitive alternative to LP in terms of transfer learning.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We summarize the main contributions as follows:", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• AutoVP is the first end-to-end VP framework that simultaneously takes account of the design of input scale, visual prompts, pre-trained model selection, and output LM strategies. This modular approach to automating VP gives its users flexibility for VP engineering, as well as a straightforward, comprehensive performance benchmark based on 12 downstream image-classification datasets. 2021)), CLIP-VP (<PERSON><PERSON> et al., 2022) , and ILM-VP (<PERSON> et al., 2023b) . The average accuracy is evaluated over 12 downstream tasks (see Section 4). For detailed information about the setting configurations, please refer to Section 3. • The proposed hyper-parameter tuning process is capable of identifying optimal configurations tailored to individual downstream datasets. In addition, its novel components -e.g., automated input scaling (Section 3) and weight initialization (Section 4.2) -augment VP's overall efficacy significantly, as compared to state-of-the-art VP methods, LP, and zero-shot baselines (see Table 1 ). • This paper represents the first step in a comprehensive exploration of optimal configurations across varied conditions (e.g., fixing a source model or an output-mapping strategy), and presents an analysis of domain similarity's impact on VP performance for each downstream dataset. • This paper highlights AutoVP's superior performance over LP in data-limited settings (Figure 2 ) and its better out-of-distribution robustness than LP (Figure 4 ).", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Background of Visual Prompts. Traditionally, to derive task-specific machine-learning models, researchers have to train or update all model parameters. But, amid the advancement of powerful foundation models, model fine-tuning and training from scratch have both become time-consuming and parameter-inefficient approaches, usually requiring large amounts of training data and storage space.", "section": "BACKGROUND AND RELATED WORK", "sec_num": "2"}, {"text": "To this end, VP, also known as in-domain model reprogramming, has emerged as an effective means of obtaining machine-learning models for various domain-specific tasks (<PERSON>, 2022) . A well-developed pre-trained model from a source domain can be directly used for performing tasks in the target domain with little transformation of the target data. For example, we can use an ImageNet pre-trained model to classify medical images without modifying any of its parameters (<PERSON><PERSON> et al., 2020) . On the other hand, VP, along with temperature scaling, can also be used as a post-processing calibration method to align model confidence and accuracy (<PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2023) . As compared to traditional approaches such as transfer learning, model fine-tuning, and training from scratch, VP is a low-complexity and model-agnostic strategy; and it is especially suitable for low-data domains.", "section": "BACKGROUND AND RELATED WORK", "sec_num": "2"}, {"text": "The Design of Visual Prompts. A VP framework can generally be divided into two trainable modules, one for input transformation and the other for output transformation. These are respectively placed at the input and output ends of a pre-trained model. In the case of input transformation, previous literature has proposed various ways to generate and place visual prompts. One of the most popular such approaches is to pad a frame around the target task image and then fill it with trainable additive-input perturbation (prompts) (<PERSON><PERSON> et al., 2020; <PERSON> et al., 2023b; <PERSON><PERSON> et al., 2019; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2023) . Next, since the output logits of the source pre-trained model are still in the source domain, further output transformation (e.g., LM) is required to obtain the target-domain logits. One naive way of achieving this is randomly mapping (RandMap) m source labels onto the target labels. <PERSON><PERSON> et al. (2020) found that frequency-based LM (FreqMap), which constructs its LM from the source-label prediction distribution of the target-domain data, can further improve the accuracy of downstream tasks. However, <PERSON> et al. (2023b) argued that FreqMap lacks interpretability and that its interaction with VP is difficult to measure. To address that problem, the authors proposed iterative LM (IterMap), a transformation of FreqMap that enables it to concurrently design LM and visual prompts. <PERSON> et al. (2023) , meanwhile, proposed a semanticsbased LM approach that aligns source and target classes that have similar semantic meanings. And Liao et al. (2023) utilized a prototypical verbalizer to map a mask token to downstream labels, thus providing a different perspective on LM. In this paper, we follow a similar design to Bahng et al. (2022) , in which visual prompts are placed around images for input transformations, and there are four mapping methods for output transformations. Further details will be presented in Section 3.", "section": "BACKGROUND AND RELATED WORK", "sec_num": "2"}, {"text": "Non-universal Visual Prompts. Instead of utilizing universal input prompts, some researchers have focused on designing input-aware prompting models (<PERSON> et al., 2022a; b) Although input prompting is commonly applied directly to the target image, researchers have also developed other prompting methods, such as convolutional visual prompt (<PERSON><PERSON> et al., 2023) , which learns prompting parameters in a small convolutional structure through self-supervision tasks without knowledge of ground truths, and visual prompt tuning (<PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2023) , which learns prompting parameters at intermediate layers of a source model. In this paper, we focus on a pixel-level VP approach using a task-specific prompting model for each image-classification task.", "section": "BACKGROUND AND RELATED WORK", "sec_num": "2"}, {"text": "As such, our approach closely resembles real-world scenarios in which a pre-trained source model remains unmodified, and external variations are not introduced internally.", "section": "BACKGROUND AND RELATED WORK", "sec_num": "2"}, {"text": "Following the system overview of AutoVP in Figure 1 , we present its four main components (Input Scaling, Visual Prompt, Pre-trained Classifier, and Output Label Mapping) and its hyper-parameter tuning feature, which enables the joint optimization of these components. Our framework can be extended to support user-defined configurations.", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "Input Scaling. In our implementation of AutoVP, we choose frame-shape prompts as the default prompting template. Hence, the visual prompt sizes p represent the width of the frame, and its actual number of parameters is 2cp(h + w -2p), where c, w, and h are color channels, width and height respectively. Although the input image size is determined by the source model, when fine-tuning to a downstream dataset from a source model, there is design freedom to resize the target images and use the remaining space for visual prompts. For instance, if the source model takes images with size 224 × 224 as input, one can scale the target image size to 128 × 128, resulting in the final visual prompt of size p = (224 -128)/2 = 48. It was shown in <PERSON><PERSON> et al. (2022) and <PERSON> et al. (2022) that the prompt size (p) plays a key role in VP performance. To automate the process of optimizing image resizing scale, we design a gradient-based optimization algorithm to implement the input scaling module, which is implemented using kornia.geometry.transform() from the Kornia library (Riba et al., 2020) . The transform() function integrates a range of geometric transformations for 2D images into deep learning, including differentiable image scaling. In addition to image resizing, the prompt size p will also scale up or down to fill the remaining space. Furthermore, to facilitate the optimization of image resizing and avoid bad local minima, we set the default image size to 128 along with three initial scales: 0.5, 1.0, and 1.5 to optimize, and the corresponding prompt sizes p are 80, 48, and 16 respectively. Consequently, the input scaling module allows AutoVP to obtain the optimal image resizing scale and prompt size (p).", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "Visual Prompt. For the visual prompt module, AutoVP adds universal pixel-level prompts around all (resized) input images. Let x t ∈ R Nt denote the target (flattened) input image (of N t -dimension), xt ∈ R Ns be the prompted image, which fits the input dimension (N s ) of the pre-trained source model f θs (θ s denotes its weights), δ ∈ R Ns be a trainable universal perturbation, and M p ∈ {0, 1} Ns be a binary mask of prompt size p, indicating the prompting area. Hence, the prompted image xt can be formulated as:", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "xt = P(x t ) = InputScaling p (x t ) + M p ⊙ σ(δ)", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "Prompts .", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "(1)", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "The prompts are initialized as 0 and formally defined as M p ⊙ σ(δ), where σ is the Sigmoid function that maps the input to a value between 0 and 1 (the scaled input pixel value range), ensuring it has the same numerical range as the input image. We then update δ using gradient descent.", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "Pre-trained Classifier. After applying the prompts to the resized image through the preceding stages, the prompted image is subsequently fed into the pre-trained model, which serves as a feature extractor to generate predictions in the source domain. We include four representative pre-trained models in our AutoVP framework: ResNet18 (<PERSON> et al., 2016) , ResNeXt101-IG (<PERSON><PERSON><PERSON> et al., 2018) , Swin-T (<PERSON> et al., 2021) , and a vision-language multi-modal model, CLIP (<PERSON><PERSON> et al., 2021) with the ViT-B/32 vision encoder backbone. Note that in AutoVP, the weights of the pre-trained classifiers are frozen and kept unchanged. The details of the models are provided in Appendix A.1.", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "Output Label Mapping. The pre-trained models predict target data to source labels, while the last mile for VP is to map predictions on the source labels to target classes. As illustrated in Figure 1 , AutoVP provides four types of output mapping, and they can be generally categorized into two groups. (i) nonparametric label mapping: frequency mapping (FreqMap) and semantic mapping (SemanticMap), which are defined during the initialization of VP training and remain unchanged throughout the training process; and (ii) trainable label mapping: iterative label mapping (IterMap) and fully connected layer mapping (FullyMap). These two methods dynamically adjust the mapping based on the prompted images. In the following, we provide the overview of our four output mapping approaches, please refer to Appendix A.2 for more details.", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "• Frequency Mapping (FreqMap) is proposed by <PERSON><PERSON> et al. (2020) . It utilizes the source-label prediction distribution of the target-domain data to map each target class to the top-m most frequent source classes. Let", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "Y s = {0, • • • , K s -1} and Y t = {0, • • • , K t -1}", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "be the set of source and target labels, where K s /K t are the numbers of source/target classes. Consider Xt collects all prompted images of label y t in target domain D t , i.e. Xt = {x ti = P(x ti )|(y ti = y t ), (x ti , y ti ) ∈ D t }, then when m = 1, the mapping of y t can be defined as:", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "y t ← y * s = arg max ys∈Ys ( xt∈ Xt P red(f θs (x t ), y s )), P red(f θs (x t ), y s ) = 1, if y s = arg max f θs (x t ) 0, otherwise .", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "(2)", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "The objective of FreqMap is to map the target label y t to the source label y * s , which is the most frequent label that f θs classified Xt as. If a source class is selected as the most frequently predicted class for multiple target classes, it will be assigned to the target class that has the highest count of predictions. The general many-to-one frequency mapping algorithm is provided in Algorithm 1 in the Appendix A.2. Moreover, random label mapping (RandMap) can be viewed as a special case of FreqMap by randomly assigning a subset of source labels to a target label.", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "• Iterative Mapping (IterMap, or ILM) is proposed by <PERSON> et al. (2023b) , which is an iterative approach for updating FreqMap. IterMap performs the frequency mapping at the beginning of each training epoch to obtain a new mapping distribution that aligns with the updated prompts. • Semantic Mapping (SemanticMap) follows the works from <PERSON> et al. (2023) and <PERSON><PERSON> et al. (2021) . We utilize the text encoder of CLIP to generate the embeddings of the names of the source and target classes. Subsequently, we map the source-target pairs based on the highest cosine similarity score between their respective embeddings. Hence, SemanticMap can be utilized in any of the three vision pre-trained models (ResNet18, Swin-T, and ResNeXt101-IG) by establishing mappings between the target classes and semantically similar classes from ImageNet-1K. However, SemanticMap is not applicable for CLIP, as it does not have an explicit set of source domain classes. • Fully Connected Layer Mapping (FullyMap) uses a linear layer to map the source output logits to target classes (<PERSON><PERSON> et al., 2023) . FullyMap can be represented as L t = w • L s + b, where L s is the output logits from the source pre-trained model, w and b are the weight and bias vector of the linear layer, and L t is the output of the linear layer which also serves as the final output logits of the VP model.", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "End-to-end Hyper-parameter Tuning. AutoVP's overall tuning procedure is depicted in Appendix A.3. Given its flexibility and modularity, its users must consider numerous settings (n = 222), including how big the initial input image should be, whether to use a trainable resizing module, which pre-trained classifiers to adopt, what output-mapping method to implement, and the number of source labels to map for each target label. To speed up the tuning operation and save computational resources, we use <PERSON> (<PERSON> et al., 2018) along with an early-stop strategy for terminating poor trails. In our experiments, we employed grid searches to test all configurations. An ASHA scheduler (<PERSON> et al., 2018) was used to retain the top-n tasks, and we continued training them while stopping the remaining tasks early. We established experimentally that n = 2 top tasks were enough to obtain the optimal setting. When the few-epoch tuning process (training 2-5 epochs with each setting) is complete, we select the setting having the highest testing accuracy and conduct complete training using that setting. By using hyper-parameter tuning, AutoVP can efficiently find the best configuration of VP and lead to significant accuracy improvement in downstream tasks.", "section": "AUTOVP FRAMEWORK", "sec_num": "3"}, {"text": "Experimental Setup. We evaluated the performance of AutoVP on 12 downstream datasets (CI-FAR10, CIFAR100, ISIC, SVHN, GTSRB, Flowers102, DTD, Food101, EuroSAT, OxfordIIITPet, UCF101, and FMoW), which are common datasets when measuring transfer learning generalization. Detailed descriptions of these datasets are given in Appendix B.1. We repeated each AutoVP experiment in triplicate, utilizing a learning rate of 40 with the SGD optimizer for CLIP, and a learning rate of 0.001 with the <PERSON> optimizer for the other pre-trained models. The results of the baselines (CLIP-VP (<PERSON><PERSON> et al., 2022) and ILM-VP (<PERSON> et al., 2023b) ) were extracted from the reported accuracies in their respective papers (please refer to Appendix B.2 for more details). Our experiments were performed on NVIDIA GeForce RTX 3090 and are implemented with PyTorch.", "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "Comparison of AutoVP and Prior Work. To ensure that our comparison of AutoVP against previously proposed VP approaches was fair, we fixed its source model but relaxed its other hyperparameter tunings. The results of using CLIP as the source model are presented in Table 2 , along with the optimal settings arrived at. We compared AutoVP against LP and two state-of-the-art VP baselines, CLIP-VP and ILM-VP, whose configurations can also be found in Table 1 . With the optimal configuration chosen via the tuning process, AutoVP outperformed these other approaches by up to 6.7% on nine of the 12 target datasets. Additionally, AutoVP surpassed the LP baseline on half those datasets, by a maximum of 27.5% in the case of SVHN. AutoVP also obtained the best average accuracy.", "section": "EXPERIMENTAL RESULTS", "sec_num": "4.1"}, {"text": "We observed that AutoVP employed FullyMap as the output transformation on most datasets. We speculate that this might have been because the linear layer has more parameters and thus allows the achievement of better results. Also, when AutoVP selected initial image scales, it had a tendency to scale up those images with relatively small prompt sizes. This allowed the VP model to allocate more attention to the image itself, leading to improved overall performance. As shown in Figure 1 , when ResNet18 was used as the source model, AutoVP outperformed ILM-VP by 24.8% on average. More experimental results under this setting are provided in Appendix C.1.", "section": "EXPERIMENTAL RESULTS", "sec_num": "4.1"}, {"text": "AutoVP with Source Model Selection. We also allowed AutoVP to search the optimal source model for downstream tasks. The optimal settings selected by AutoVP, and a comparison of experimental results can be found in Appendix C.1. Our experimental results show that Swin-T was the pre-trained model most frequently chosen by AutoVP as most suitable, i.e., in the cases of eight of the 12 datasets. On average, this choice resulted in 0.43% better accuracy than when CLIP was utilized as the fixed pre-trained backbone. On the DTD and Flowers102 datasets, however, Swin-T's performance was better than CLIP's by much more: i.e., 6.80% and 3.08%, respectively. These findings highlight how multiple pre-trained models can be leveraged to enhance performance across a diverse range of datasets.", "section": "EXPERIMENTAL RESULTS", "sec_num": "4.1"}, {"text": "Data Scalability. To understand how AutoVP would perform in a data-limited regime, we gradually and uniformly reduced the amount of training data to 50%, then 25%, then 10%, and finally 1% of each training dataset's original size. The experimental results in Figure 2 indicate that AutoVP consistently outperformed LP across all 12 datasets, and that its relative performance was especially high in the two scenarios with the lowest data volumes, i.e., 10% and 1% data usage. The dataset-specific results can be found in Figure C .1 (within Appendix C.2).", "section": "EXPERIMENTAL RESULTS", "sec_num": "4.1"}, {"text": "We designed a range of model architectures as testbeds for examining the performance of AutoVP's various components. Our comparisons of these VP architectures included 1) the utilization of a weight-initialization strategy with FullyMap, 2) the inclusion vs. exclusion of the CLIP text encoder, 3) the presence vs. absence of visual prompts, and 4) the frequency analysis of the learned VP.", "section": "ABLATION STUDIES OF AUTOVP", "sec_num": "4.2"}, {"text": "Weight Initialization of FullyMap with CLIP. When CLIP was used as the pre-trained model, the FullyMap output transformation exhibited significantly inferior performance to FreqMap and IterMap (Figure 3 ). This is because FreqMap and IterMap can leverage CLIP's zero-shot property with the semantic meanings of the labels, whereas the fully connected layer needs to learn the mapping from a random state. As a result, FullyMap tends to perform poorly in the hyper-parameter tuning process, yet may achieve higher accuracy after completing 200 epochs of training. In Figure 3 , for example, AutoVP suggests that the optimal output transformation for Flowers102 with CLIP is IterMap; but in reality, FullyMap achieves better performance after training for 200 epochs (87.3%, as against 78.8% for IterMap). Impact of the Non-inclusion of Text Encoder in CLIP. When replicating the experimental setting as shown in Table 2 and establishing a direct connection between the fully connected output mapping layer and the CLIP image encoder without incorporating the text encoder, there was a substantial decrease in average accuracy: to 69.0% (see Figure E .1, Appendix E.1). Dataset-specific accuracy drops were particularly prominent in Flowers102, Food101, and OxfordIIITPet. These outcomes suggest that label semantics play a crucial role in those datasets.", "section": "ABLATION STUDIES OF AUTOVP", "sec_num": "4.2"}, {"text": "The Impact of Visual Prompts. We also investigated the effects on the overall performance of removing the module of visual prompts from the AutoVP pipeline while retaining the pre-trained model and output-mapping modules. When the ResNet18 model was used, leaving the visual prompts in yielded better performance than omitting them in just three out of 12 cases: i.e., the SVHN, GTSRB, and ISIC datasets (Figure C.3 (b) , Appendix C.3). For the remaining datasets, the inclusion of visual prompts actually led to a decline in performance. This suggests that when a relatively small source model is used for VP, a significant improvement in accuracy of the sort reported in Table C .1 can primarily be attributed to fully connected layer mapping, and visual prompts may be perceived as noise. On the other hand, when the CLIP model was used, most of the datasets had positive gain values (Figure 5 (b)), indicating improved performance, when visual prompts were included. This suggests that CLIP is more suitable for VP tasks than ResNet18 is.", "section": "ABLATION STUDIES OF AUTOVP", "sec_num": "4.2"}, {"text": "Frequency Analysis of the Learned Visual Prompts. In Appendix D.1, we also conducted an analysis from a frequency perspective (<PERSON>, 1988) to study the generalization of visual prompt patterns. The results highlighted the effectiveness of prompting with CLIP, harnessing low-frequency features that generalize to the target domain.", "section": "ABLATION STUDIES OF AUTOVP", "sec_num": "4.2"}, {"text": "Tuning Selection. AutoVP provides joint optimization of its multiple configurations and selects different parameters according to its target tasks. In terms of output label mapping, FullyMap exhibits superior performance in vision models, but IterMap or FreqMap appear to enhance the performance of text-image models like CLIP. In this context, weight initialization with FullyMap plays an important role in CLIP, making this option one of the more frequently chosen output-mapping strategies (Table 2 ). We also observed that novel designs exploiting larger image scales and mapping a larger number of source classes tended to yield enhanced performance. More information on selection preferences during hyperparameter tuning can be found in Appendix D.3.", "section": "DISCUSSIONS", "sec_num": "5"}, {"text": "AutoVP Robustness. We trained AutoVP on CIFAR10 and evaluated its robustness on the corrupted dataset CIFAR10-C, which consists of 18 types of filters or noise. As shown in Figure 4 , AutoVP maintained greater robustness than ILM-VP, CLIP-VP, and LP. Its loss of accuracy was relatively small: suggesting that AutoVP exhibits a lower degree of overfitting to the training data and possesses a higher ability to resist the impact of noise than the other baselines do.", "section": "DISCUSSIONS", "sec_num": "5"}, {"text": "Performance Evaluation on ID/OOD Downstream Tasks. We evaluate the out-of-distribution (OOD) extent of each dataset relative to the pre-trained CLIP by considering the average confidence score (<PERSON> et al., 2017) and the CLIP zero-shot inference. The accuracy gains achieved through VP (Figure 5 ) were computed as the difference in accuracy between AutoVP and LP or non-VP approaches (i.e. visual prompts were removed and output mapping retained). We observed that the datasets that were more in-distribution (ID), with higher confidence scores and higher zero-shot accuracy, exhibited smaller accuracy gains from VP. Conversely, datasets that were more OOD, characterized by lower confidence scores and lower zero-shot accuracy, had their accuracy improved more through AutoVP.", "section": "DISCUSSIONS", "sec_num": "5"}, {"text": "We also evaluated accuracy gains with ResNet18 pre-trained on ImageNet-1K (<PERSON><PERSON><PERSON> et al., 2015) (Figure C.3, Appendix C.3 ) and, to assess domain differences between ImageNet-1K and other downstream datasets, we calculated the FID score (<PERSON><PERSON><PERSON> et al., 2017) . The results were consistent with the cases using CLIP. In conclusion, AutoVP is suitable for datasets that exhibit more OOD characteristics than the source domain dataset.", "section": "DISCUSSIONS", "sec_num": "5"}, {"text": "This work is subject to some limitations. First, our optimization process did not include certain hyperparameters, notably learning rate and weight decay. This omission stemmed from our primary focus on identifying the best configurations for VP training, and because including such hyperparameters would have greatly increased execution workload. In addition, when it comes to choosing the best pre-trained model to fine-tune on the target dataset, <PERSON> et al. (2021) also argued that, in general, the sophisticated fine-tuning techniques (e.g., regularization) would not change the ranking of pre-trained models in downstream tasks. Nonetheless, we conducted supplementary tuning experiments encompassing various learning rates and weight decays, the results of which can be found in Table E .3 (within Appendix E.3). In practice, we suggest enabling the tuning of learning rates, weight decay, and other model-specific parameters after the initial hyperparameter tuning phase of AutoVP. The tuning of fundamental hyperparameters could potentially be accelerated with recent advancements in utilizing generalization metrics to identify optimal hyperparameter configurations (<PERSON> et al., 2023a; b) , a subject to be explored in future research.", "section": "LIMITATIONS", "sec_num": "6"}, {"text": "Another limitation pertains to the scope of AutoVP, which is oriented toward classification tasks. However, we have extended its application to segmentation and detection tasks, as detailed in Appendix E.2. Furthermore, recent studies have demonstrated the success of visual prompts in generative tasks (<PERSON><PERSON> et al., 2021; 2022; <PERSON> & <PERSON>, 2022; <PERSON> et al., 2022; <PERSON> et al., 2023) . Nevertheless, expanding support for generative tasks will require accommodation of their distinctive requirements, e.g., via the integration of a suitable pre-trained generative model, such as GANs (<PERSON><PERSON><PERSON> et al., 2014) , VAEs (Kingma & Welling, 2013), or diffusion models (<PERSON> et al., 2020) , along with tailored prompt design. Certainly, our results imply that there are many avenues for VP research that merit further exploration.", "section": "LIMITATIONS", "sec_num": "6"}, {"text": "This paper has introduced AutoVP, an end-to-end framework that automatically selects the optimal VP configuration for a downstream dataset. AutoVP demonstrates superior performance over other state-of-the-art VP methods and transfers learning baselines in both standard and sample-reduced fine-tuning settings. This research has also yielded important insights into optimal VP configurations, the effects of downstream data characteristics on VP, and how robustness against image corruption might be improved. In short, we believe AutoVP is an efficient and expandable tool, and perhaps more importantly, a useful benchmark that will accelerate the development of VP research and technology.", "section": "CONCLUSION", "sec_num": "7"}, {"text": "A.1 PRE-TRAINED CLASSIFIER DETAILS Our AutoVP framework includes four pre-trained models: ResNet18 (<PERSON> et al., 2016) , ResNeXt101-IG (<PERSON><PERSON><PERSON> et al., 2018) , Swin-T (<PERSON> et al., 2021) , and CLIP (<PERSON><PERSON> et al., 2021) . Both ResNet18 and Swin-T models were trained on the ImageNet-1K (<PERSON><PERSON><PERSON> et al., 2015) dataset, while ResNeXt101-IG was pre-trained on a large collection of Instagram photos. Additionally, CLIP was trained on a dataset consisting of 400 million pairs of images and corresponding text from the internet.", "section": "A IMPLEMENTATION DETAILS OF AUTOVP", "sec_num": null}, {"text": "In the structural differences, the first three models are single-modality vision models. ResNet18 is a typical and relatively small convolutional neural network with residual blocks. ResNeXt101-IG is a deeper residual network that incorporates cardinality, which refers to the size of the set of transformations (<PERSON><PERSON> et al., 2017) . Swin-T is a vision transformer that operates by dividing the input image into patches and processing them using the transformer architecture. The last model, CLIP, is a vision-language multi-modal model that calculates the cosine similarity between image embeddings and label text embeddings. The prediction is to select the class with the highest similarity score to the embedding of the input image. The prediction flow is illustrated in Eq. 3, where the input image is denoted as x, and K t represents the size of the predictable class set. The token vector of the i-th class label text, obtained from a tokenizer, is denoted as ClsT k i . CLIP utilizes the Image-Encoder() and Text-Encoder() components to extract features from images and text. The resulting image and text embeddings are represented as x emb and t embi , respectively. The cosine similarity between any pair of (image, text) embeddings can be computed, and the class with the highest cosine similarity is considered as the predicted class y pred .", "section": "A IMPLEMENTATION DETAILS OF AUTOVP", "sec_num": null}, {"text": "x emb = Image-Encoder(x)", "section": "A IMPLEMENTATION DETAILS OF AUTOVP", "sec_num": null}, {"text": "EQUATION", "section": "A IMPLEMENTATION DETAILS OF AUTOVP", "sec_num": null}, {"text": "By training with pairs of image and text annotations, CLIP is able to learn correlations between visual and textual information, achieving state-of-the-art zero-shot accuracy.", "section": "A IMPLEMENTATION DETAILS OF AUTOVP", "sec_num": null}, {"text": "As mentioned in Section 3, AutoVP incorporates four output label mappings: frequency mapping (FreqMap), iterative mapping (IterMap), semantic mapping (SemanticMap), and fully connected layer mapping (FullyMap). In the following, we provide more details of each mapping algorithm. ", "section": "A.2 OUTPUT LABEL MAPPINGS OF AUTOVP", "sec_num": null}, {"text": "In FreqMap, the mapping is established between each target class and the most frequently mapped source class. We demonstrate the general many-to-one mapping (multiple source labels mapped to one target label) in Algorithm 1. In lines 6-10, we traverse all the training data pairs (x t , y t ). First, we pad the image x t with the current visual prompts to obtain a prompted image xt . Then, we could obtain the prediction f θs (x t ) = y s in the source domain. This gives us a mapping relation from y s to y t , and we increase the count accordingly. In line 11, we obtain the list of source-target id pairs (S id , T id ), which is sorted by the count matrix (count) in descending order. Accordingly, we start defining the mapping of FreqMap from the most frequently source-target pair. If the source class has not been mapped yet and the mapping count for the target class has not reached the limit m, we establish the mapping relationship M[S id ][T id ] = 1 (line 14). We continue this process until all target classes have been mapped to m source classes. Once this condition is met, the mapping assignment is completed, and we return the mapping matrix M. ", "section": "A.2.1 FREQMAP (FREQUENCY MAPPING)", "sec_num": null}, {"text": "In IterMap, at the beginning of each training epoch, the mapping relationship is established using the FreqMap with the current prompted image. This means that the visual prompts updated via the VP model are padded onto the image and participate in the mapping process. Hence, this mapping relationship changes as the visual prompts are trained, which is known as bi-level optimization (<PERSON> et al., 2023b) .", "section": "A.2.2 ITERMAP (ITERATIVE MAPPING)", "sec_num": null}, {"text": "In SemanticMap, source and target classes with semantically similar class names are mapped together. This mapping is accomplished using CLIP's tokenizer Tokenizer() and text encoder Text-Encoder(). We demonstrate the mapping process using the following equation:", "section": "A.2.3 SEMANTICMAP (SEMANTIC MAPPING)", "sec_num": null}, {"text": "ClsT", "section": "A.2.3 SEMANTICMAP (SEMANTIC MAPPING)", "sec_num": null}, {"text": "EQUATION", "section": "A.2.3 SEMANTICMAP (SEMANTIC MAPPING)", "sec_num": null}, {"text": "In Eq. 4, let", "section": "A.2.3 SEMANTICMAP (SEMANTIC MAPPING)", "sec_num": null}, {"text": "Y s = {0, • • • , K s -1} and Y t = {0, • • • , K t -1}", "section": "A.2.3 SEMANTICMAP (SEMANTIC MAPPING)", "sec_num": null}, {"text": "be the set of source and target labels, where K s /K t are the numbers of source/target classes. For the source label y s ∈ Y s with the classname N ys and the target label y t ∈ Y t with the classname N yt , we first utilize the tokenizer to obtain token vectors (ClsT k ys and ClsT k yt ) corresponding to N ys and N yt . Then, the text encoder is used to obtain their embeddings (Emb ys and Emb yt ). Pair-wise cosine similarity is calculated between each source and target embeddings, and each target label is mapped to the source label with the highest similarity.", "section": "A.2.3 SEMANTICMAP (SEMANTIC MAPPING)", "sec_num": null}, {"text": "FullyMap utilizes a linear layer L t = w • L s + b with weights w and bias b to learn the mapping, enabling the transformation of the output source logits L s to target logits L t . As for weight initialization (WI), it is employed in the case of CLIP with FullyMap. This technique involves setting the weights between the target labels and their default templates as 1, while setting the rest to 0. With WI, the linear layer can achieve a favorable initial mapping state, thereby expediting the process of obtaining a good mapping relation.", "section": "A.2.4 FULLYMAP (FULLY CONNECTED LAYER MAPPING)", "sec_num": null}, {"text": "In Figure A.2, there are three stages involved in the tuning process, while the Visual Prompt component depicted in Figure 1 is not involved in the tuning process, as it does not contain any hyper-parameters. During the Input Scaling stage, the initial scale of the input image is determined, and users can choose whether to learn the resize scale during training. In the Pre-trained Classifier stage, users have the option to select from four pre-trained models to serve as the feature extractor. The Output Label Mapping stage offers four mapping methods to choose from. For FreqMap, IterMap, and SemanticMap, users can specify the number of source classes that are mapped to a single target class. ", "section": "A.3 AUTOVP TUNING PROCESS", "sec_num": null}, {"text": "To assess the efficacy of the proposed AutoVP, we selected the following twelve datasets for our experiments. The detailed information is shown in B.1.", "section": "B DATASETS AND <PERSON><PERSON>LINES B.1 THE TWELVE DOWNSTREAM DATASETS", "sec_num": null}, {"text": "• CIFAR10 & CIFAR100 (<PERSON><PERSON><PERSON> & <PERSON>, 2009) : The datasets consist of labeled subsets of the 80 million tiny images dataset, which are composed of 32×32 color images. • ISIC (<PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON><PERSON> et al., 2018) : The International Skin Imaging Collaboration (ISIC) developed an international repository of dermoscopic images known as the ISIC Archive. The images of the datasets were acquired using different devices at several medical centers worldwide. • SVHN (<PERSON><PERSON> et al., 2011) : A real-world image dataset of street view house numbers.", "section": "B DATASETS AND <PERSON><PERSON>LINES B.1 THE TWELVE DOWNSTREAM DATASETS", "sec_num": null}, {"text": "• GTSRB (<PERSON><PERSON> et al., 2013) 2022)) in Section 4 is obtained from the results documented in the respective papers. For some datasets (such as ISIC, FMoW, and GTSRB), the authors did not include them in their paper; in this regard, we follow the corresponding experimental settings to obtain baseline accuracy.", "section": "B DATASETS AND <PERSON><PERSON>LINES B.1 THE TWELVE DOWNSTREAM DATASETS", "sec_num": null}, {"text": "We present additional experimental results to highlight the effectiveness of AutoVP. In addition to the comparisons with CLIP and VP baselines discussed in Section 4.1, we further evaluate the performance using ResNet18 as the pre-trained model and explore a scenario without any pre-trained model restrictions. The results using ResNet18 are presented in Table C .1, while the results for the unrestricted scenario are provided in Table C .2. These comparisons consistently demonstrate that AutoVP outperforms previous approaches, including LP and the state-of-the-art VP methods. To further understand how the downstream dataset distribution influences the performance of visual prompting. We conduct experiments to observe the relation between accuracy gain and dataset characteristics. When using CLIP as the pre-trained model, we define confidence score, obtained by averaging the maximum softmax probability of predictions across the entire training set, as an indicator of dataset characteristics. For other vision pre-trained models, the FID score is used to measure the dissimilarity between the downstream dataset and the ImageNet-1K dataset, i.e. the degree of out-of-distribution (OOD). We present the Confidence/FID scores of each dataset in Figure C .2, providing insights into their OOD characteristics. Furthermore, Figure C.3 demonstrates the accuracy gain for each dataset when using ResNet18 as the pre-trained model. The experimental results show that when using AutoVP, datasets with a higher degree of OOD tend to benefit from more accuracy gains. Table 2 has encompassed the prevalent 12 datasets in VP research. Furthermore, to enable a more comprehensive comparison within a wider spectrum of VP research, we have included additional datasets-SUN397 (<PERSON> et al., 2010) , RESISC (Cheng et al., 2017) , CLEVR (Johnson et al., 2017) , and StanfordCar (Krause et al., 2013) -with their respective results available in Table C .4. Our findings consistently demonstrate AutoVP's superior performance compared to ILM and CLIP-VP across the most of datasets. Especially notable is the substantial 15% increase in accuracy observed in the out-of-distribution (OOD) dataset, CLEVR, when compared to linear probing (LP). However, the in-distribution (ID) datasets, SUN397 and StanfordCar, showcased lower performance than LP, aligning with the trend of accuracy gain illustrated in Figure 5 in Section 5. We have analyzed the learned prompts using the best setting selected from AutoVP and have represented them in the frequency domain through Fast Fourier transformation (Brigham, 1988) . In ", "section": "C ADDITIONAL EXPERIMENTS WITH AUTOVP C.1 FIXED PRE-TRAINED MODEL vs. AUTO PRE-TRAINED MODEL SELECTION", "sec_num": null}, {"text": "V H N E u r o S A T F l o w e r s 1 0 2 C I F A R 1 0 0 U C F 1 0 1 D T D F M o W G T S R B C I F A R 1 0 F o o d", "section": "S", "sec_num": null}, {"text": "In AutoVP, we set the learning rate (LR) for CLIP to be 40 and employed the SGD optimizer with a momentum of 0.9 and a weight decay (WD) of 0. In this section, we have undertaken supplementary tuning options within the AutoVP framework. Specifically, we introduce additional choices for the LR, selecting from 35, 40, and 45 (for CLIP), as well as WD, choosing from 0, 10 -5 , and 10 -10 . The outcomes presented in Table E .3 showcase a 0.6% enhancement in accuracy with the additional tuning options. It is important to note that the execution workload dramatically increases, as it will involve exploring 9 times more additional combinations in the tuning process. In order to establish an equitable comparison with AutoVP, we undertake a comprehensive hyperparameter tuning process for ILM-VP. We investigate tuning options that span 1, 2, 5, and 10 source classes for output mapping, as well as prompt sizes of 10, 20, 30, 40, and 50. For ILM-VP without tuning, default settings are maintained, with the mapping number set to 1 and the prompt size set to 30.", "section": "E.3 EXPLORING ADDITIONAL TUNING AXES", "sec_num": null}, {"text": "The tuning procedure is applied to several datasets, and the results are shown in Table E .4. Although hyper-parameter tuning in ILM-VP leads to an improvement in accuracy, it remains unable to surpass the performance exhibited by AutoVP. Some visual prompting research has delved into a black-box setting (<PERSON><PERSON> et al., 2020; <PERSON> et al., 2023) , where the internal architecture of the pre-trained model remains unattainable during the training process. For instance, BlackVIP (<PERSON> et al., 2023) employs an input-dependent prompt designer to generate visual prompts. These prompts are then fed into a black-box model, and gradient approximation strategies are utilized to update the prompt designer. In Table E .5, we present a comparison between AutoVP and BlackVIP. Since BlackVIP uses CLIP as the pre-trained backbone, we report our results using the same pre-trained model. AutoVP demonstrates a 16% performance increase on average compared to BlackVIP. This notable difference might be attributed to the variance in update strategies: BlackVIP utilizes SPSA-GC for black-box models, while AutoVP relies on classic gradient descent. However, due to the differing objectives of these two studies, direct comparisons may introduce certain unfairness.", "section": "E.3 EXPLORING ADDITIONAL TUNING AXES", "sec_num": null}, {"text": "F.1 COMPARISON OF AUTOVP, LINEAR PROBING, AND FULL FINE-TUNING When comparing the performance of AutoVP to that of linear probing (LP) and full fine-tuning (FF), it's crucial to acknowledge the significant discrepancy in terms of trainable parameter size.", "section": "F PERFORMANCE AND RESOURCE UTILIZATION", "sec_num": null}, {"text": "As FF involves a parameter size that is roughly 100 to 1000 times larger (as shown in Table F .4). Nevertheless, it's worth noting that the differences in performance between VP and FF are relatively minor in certain datasets. For instance, in the case of EuroSAT, both AutoVP and FF achieve a 96% accuracy (as demonstrated in Table F .1). This observation suggests that VP retains its advantages even when faced with such substantial variations in parameter size. ", "section": "F PERFORMANCE AND RESOURCE UTILIZATION", "sec_num": null}], "back_matter": [{"text": "The research described in this paper was conducted in the JC STEM Lab of Intelligent Design Automation, which is funded by The Hong Kong Jockey Club Charities Trust.", "section": "ACKNOWLEDGMENTS", "sec_num": null}, {"text": "Although AutoVP primarily focuses on classification tasks, we aimed to delve deeper into various vision tasks by incorporating models designed for different purposes. For segmentation tasks, we employ DeepLabV3 (<PERSON> et al., 2017) as the pre-trained backbone, and for detection tasks, we utilize OWL-ViT (<PERSON> et al., 2022) . To evaluate the capability of both in-distribution (ID) and out-of-distribution (OOD) datasets, we chose ISIC as the OOD dataset for both tasks. For ID datasets, we use Pets in the segmentation task and VOC (<PERSON><PERSON> et al., 2015) in the detection task.Our VP segmentation framework, depicted in Figure E.2 (a), integrates a FullyMap after the pretrained model to facilitate pixel-wise classification using a custom class number. In contrast, the linear probing approach modifies the final 2D convolutional layer, and the results are depicted in Table E .1. We evaluated segmentation performance using two metrics: IoU (Intersection over Union) score and pixel-wise classification accuracy. AutoVP exhibited superior performance on both metrics on the ISIC dataset. Additionally, segmentation examples highlighted that predictions align more accurately with the ground truth mask when the prompt space is larger (see Figure E .3). However, in the ID dataset (Pets), VP performance was inferior to LP. This aligns with our findings in the classification task, where OOD datasets derived greater benefits from visual prompts. ", "section": "annex", "sec_num": null}], "ref_entries": {"FIGREF0": {"num": null, "fig_num": null, "text": "t e x i t s h a 1 _ b a s e 6 4 = \" D z O E a f y X + 2 U 1 1 o t l 9 6 x 8 w n / + x E Y = \" > A A A B 6 3 i c b V B N S w M x E J 2 t X 7 V + V T 1 6 C Z a C p 7 I r t n o R C l 4 8 V r A f 0 C 4 l m 2 b b 0 C S 7 J F m h L P 0 L X j w o 4 t U / 5 M 1 / Y 7 b d g 7 Y + G H i 8 N 8 P M v C D m T B v X / X Y K G 5 t b 2 z v F 3 d L e / s H h U f n 4 pK O j R B H a J h G P V C / A m n I m a d s w w 2 k v V h S L g N N u M L 3 L / O 4 T V Z p F 8 t H M Y u o L P J Y s Z A S b T I p v v c a w X H F r 7 g J o n X g 5 q U C O 1 r D 8 N R h F J B F U G s K x 1 n 3 P j Y 2 f Y m U Y 4 X R e G i S a x p h M 8 Z j 2 L Z V Y U O 2 n i 1 v n q G q V E Q o j Z U s a t F B / T 6 R Y a D 0 T g e 0 U 2 E z 0 q p e J / 3 n 9 x I Q 3 f s p k n B g q y X J R m H B k I p Q 9 j k Z M U W L 4 z B J M F L O 3 I j L B C h N j 4 y n Z E L z V l 9 d J 5 7 L m N W r 1 h 6 t K s 5 r H U Y Q z O I c L8 O A a m n A P L W g D g Q k 8 w y u 8 O c J 5 c d 6 d j 2 V r w c l n T u E P n M 8 f P f u N o w = = < / l a t e x i t > p = 16 ×0.5 < l a t e x i t s h a 1 _ b a s e 6 4 = \"C U W Z l f A m s S m u A U x B W y C B J V Z L 4 l M = \" > A A A B 6 3 i c b V B N S w M x E J 2 t X 7 V + V T 1 6 C Z a C p 7 I r V n s R C l 4 8 V r A f 0 C 4 l m 2 b b 0 C S 7 J F m h L P 0 L X j w o 4 t U / 5 M 1 / Y 7 b d g 7 Y + G H i 8 N 8 P M v C D m T B v X / X Y K G 5 t b 2 z v F 3 d L e / s H h U f n 4 p K O j R B H a J h G P V C / A m n I m a d s w w 2 k v V h S L g N N u M L 3 L / O 4 T V Z p F 8 t H M Y u o L P J Y s Z A S b T I p v G + 6 w X H F r 7 g J o n X g 5 q U C O 1 r D 8 N R h F J B F U G s K x 1 n 3 P j Y 2 f Y m U Y 4 X R e G i S a x p h M 8 Z j 2 L Z V Y U O 2 n i 1 v n q G q V E Q o j Z Us a t F B / T 6 R Y a D 0 T g e 0 U 2 E z 0 q p e J / 3 n 9 x I Q N P 2 U y T g y V Z L k o T D g y E c o e R y O m K D F 8 Z g k m i t l b E Z l g h Y m x 8 Z R s C N 7 q y + u k c 1 n z r m v 1 h 6 t K s 5 r H U Y Q z O I c L 8 O A G m n A P L W g D g Q k 8 w y u 8 O c J 5 c d 6 d j 2 V r w c l n T u E P n M 8 f P 4 a N p A = = < / l a t e x i t > p = 80 < l a t e x i t s h a 1 _ b a s e 6 4 = \" 4 u j G + / D r e / z 5 Q j 1 y C w K z w G b K O I k = \" > A A A B / H i c j V D L S s N A F J 3 U V 6 2 v a J d u h h b B V U n E 1 7 L g x m U F 2 w p t K J P p J B 0 6 m Q k z N 0 o I 9 V f c u F D E r R / i z r 9 x + l i o K H j g w u G c e 7 m H E 6 a C G / C 8 D 6 e 0 t L y y u l Z e r 2 x s b m 3v u L t 7 H a M y T V m b K q H 0 T U g M E 1 y y N n A Q 7 C b V j C S h Y N 1 w f D H 1 u 7 d M G 6 7 k N e Q p C x I S S x 5 x S s B K A 7 f a F 0 r G g k W g e T w C o r W 6 G 7 h 1 v + H N g P 8 m d b R A a + C + 9 4 e K Z g m T Q A U x p u d 7 K Q Q F 0 c C p Y J N K P z M s J X R M Y t a z V J K E m a C Y h Z / g A 6 s M c a S 0 H Q l 4 p n 6 9 K E h i T J 6 E d j M h M D I / v a n 4 m 9 f L I D o P C i 7 T D J i k 8 0 d R J j A o P G 0 C D 7 l m F E R u C a G a 2 6 y Y j o g m F G x f l f + V 0 D l q + K e N k 6 v j e r O 2 q K O M 9 l E N H S I f n a E m u k Q t 1 E Y U 5 e g B P a F n 5 9 5 5 d F 6 c 1 / l q y V n c V N E 3 O G + f p 3 C V V g = = </ l a t e x i t > ! < l a t e x i t s h a 1 _ b a s e 6 4 = \" h w 4 U 7 D z y 6 O 6 A P w s J C H 8 0 w K q 9 N + U = \" > A A A B 6 H i c j V D L S g N B E O y N r x h f U Y 9 e h g T B U 9 g V X 8 e A F 4 8 J m A c k S 5 i d 9 C Z j Z m e X m V k h L P k C L x 4 U 8 e o n e f N v n E 1 y U F G w o K G o 6 q a 7 K 0 g E 1 8 Z 1 P 5 z C y u r a + k Z x s 7 S 1 v b O 7 V 9 4 / a O s 4 V Q x b L B a x 6 g Z U o + A S W 4 Y b g d 1 E I Y 0 C g Z 1 g c p 3 7 n X t U m s f y 1 k w T 9 C M 6 k j z k j B o r N Z N B u e r V 3 D n I 3 6 Q K S z Q G 5 f f + M G Z p h N I w Q b X u e W 5 i / I w q w 5 n A W a m f a k w o m 9 A R 9 i y V N E L t Z / N D Z + T Y K k M S x s q W N G S u f p 3 I a K T 1 N A p s Z 0 T N W P / 0 c v E 3 r 5 e a 8 M r P u E x S g 5 I t F o W p I C Y m + d d k y B U y I 6 a W U K a 4 v Z W w M V W U G Z t N 6 X 8 h t E 9 r 3 k X t v H l W r V e W c R T h C C p w A h 5 c Q h 1 u o A E t Y I D w A E / w 7 N w 5 j 8 6 L 8 7 p o L T j L m U P 4 B u f t E 9 g e j O E = < / l a t e x i t > p ×1.0", "uris": null, "type_str": "figure"}, "FIGREF1": {"num": null, "fig_num": null, "text": "M 5 9 3 I P J 0 w F N + B 5 H 8 7 C 4 t L y y m p p r b y + s b m 1 7 e 7 s t o z", "uris": null, "type_str": "figure"}, "FIGREF2": {"num": null, "fig_num": null, "text": "e n N d 5 a 8 F Z z O z D N z h v n 9 Z Q j N s = < / l a t e x i t > labels Trainable LM Nonparametric LM E u ro S A T C IF A R 10 F lo w er s1 02 S V H N P et s G T S R B IS IC C IF A R 10 0 U C F 10 1 D T D F o o d 10 1 F M o W Accuracy (%)", "uris": null, "type_str": "figure"}, "FIGREF3": {"num": null, "fig_num": "45", "text": "Figure 4: Models Robustness with CLIP. The comparison of accuracy drop on the CIFAR10-C dataset across AutoVP, ILM-VP, CLIP-VP and LP.", "uris": null, "type_str": "figure"}, "FIGREF4": {"num": null, "fig_num": null, "text": "Figure A.1: Output Label Mapping. Illustration of four output mapping methods.", "uris": null, "type_str": "figure"}, "FIGREF5": {"num": null, "fig_num": "1", "text": "FreqMap (δ, f θs , D t , m) Input: visual prompts δ, source classifier f θs , target dataset D t , and the specified number of source classes mapped to each target class m Output: mapping matrix M # Initialization 1 K s , K t ← number of source and target classes 2 M ← 0 Ks×Kt # mapping matrix 3 count ← 0 Ks×Kt # a zero matrix, records the number of images of each target class being predicted (by f θs ) as each source class 4 done_s ← 0 Ks # a boolean vector, records whether the source class has been assigned 5 done_t ← 0 Kt # a boolean vector, records whether the number of source classes per target class is equal to m # Calculate the Frequency 6 foreach (x t , y t ) ∈ D t do 7 xt ← P(x t ) # generates xt from x t and δ using Eq. 1 8 y s ← f θs (x t ) # the predicted source domain class 9 count[y s ][y t ] ← count[y s ][y t ] + 1 10 end 11 index_list ← ArgSort(count) # get the list of source-target id pairs (S id , T id ) sorted by count[S id ][T id ] in descending order # Define the FreqMap 12 for (S id , T id ) in index_list do 13 if not done_t[T id ] and not done_s[S id ] then14 M[S id ][T id ] ← 1 # assign the mapping from S id (source label) to T id (target label) 15 done_s[S id ] ← True 16 end 17 if Sum(M[:][T id ]) == m then 18 done_t[T id ] ← True # if the target class T id has been mapped to m source classes 19 end 20 if Sum(done_t) == K t then 21 break # early stop if all the target classes has been mapped to m source classes", "uris": null, "type_str": "figure"}, "FIGREF6": {"num": null, "fig_num": "2", "text": "Figure A.2: Hyper-Parameter Tuning Selection. Illustration of the end-to-end hyper-parameter tuning process in AutoVP with a total of 222 possible configurations.", "uris": null, "type_str": "figure"}, "FIGREF7": {"num": null, "fig_num": null, "text": ": The German Traffic Sign Benchmark (GTSB) is a multi-class, single-image classification challenge that was conducted at the International Joint Conference on Neural Networks (IJCNN) in 2011. • Flowers102 (<PERSON><PERSON><PERSON> & <PERSON>rman, 2008): The 102 categories flowers dataset consists of commonly occurring flowers in the United Kingdom. • DTD (<PERSON><PERSON><PERSON><PERSON> et al., 2014): The Describable Textures Dataset (DTD) is a collection of textural images that have been annotated with a series of human-centric attributes. • Food101 (<PERSON><PERSON> et al., 2014): The food dataset consists of 101 different classes, with a total of 101,000 images. • EuroSAT (<PERSON><PERSON><PERSON> et al., 2019): The Sentinel-2 satellite images dataset for land use and land cover classification. • OxfordIIITPet (Pets) (<PERSON><PERSON> et al., 2012): The dataset includes diverse breeds of cats and dogs, with images exhibiting variations in scale, pose, and lighting conditions. • UCF101 (<PERSON><PERSON><PERSON> et al., 2012): The action recognition dataset consists of realistic action videos that have been collected from YouTube. • FMoW (<PERSON> et al., 2018): The dataset contains satellite images that are used for sites and land use classification.", "uris": null, "type_str": "figure"}, "FIGREF8": {"num": null, "fig_num": null, "text": "Figure C.1: Data Scalability. The charts present the accuracy of AutoVP and LP with varying percentages of data usage: 100%, 50%, 25%, 10%, and 1%.", "uris": null, "type_str": "figure"}, "FIGREF9": {"num": null, "fig_num": null, "text": "Figure C.2: Confidence Score and FID Score. We sort the datasets by the degree of out-ofdistribution (OOD), where the one closer to the left indicates higher similarity (in-distribution, ID) to the training data of the pre-trained model, while the one closer to the right indicates greater dissimilarity (OOD).", "uris": null, "type_str": "figure"}, "FIGREF10": {"num": null, "fig_num": "3", "text": "Figure C.3: Accuracy Gains with Resnet18. The gains are calculated by taking the difference between the performance of AutoVP and LP or Non-VP scenario.", "uris": null, "type_str": "figure"}, "FIGREF11": {"num": null, "fig_num": null, "text": "Figure D.1(a), the prompting result of SVHN dataset with CLIP is notably distinct and achieves the highest testing accuracy among all the pre-trained models. In the frequency analysis (Figure D.1(b)), the prompts are concentrated in the low-frequency domain (at the center of the plot), with CLIP displaying the most distinct structure and significantly larger magnitudes compared to the others. These results validate the efficient learning of prompts with CLIP, harnessing low-frequency features that generalize to the target domain.", "uris": null, "type_str": "figure"}, "FIGREF12": {"num": null, "fig_num": "1", "text": "Figure D.1: Prompting Analysis of SVHN Using Various Pre-trained Models. (a) Frame-shape prompts learned with the best settings selected from AutoVP for a given pre-trained model. (b) Prompts in the frequency domain by Fast Fourier transformation.", "uris": null, "type_str": "figure"}, "FIGREF13": {"num": null, "fig_num": null, "text": "Figure D.2 illustrates that FullyMap can be interpreted as a weighted combination of multiple source labels, where some human-readable features may exhibit similarity. For instance, in Figure D.2(a), bumpy shows similarities with custard apple, disk brake, and pineapple, while in Figure D.2(b), scaly shares similar features with boa constrictor, coho, and common iguana.", "uris": null, "type_str": "figure"}, "FIGREF14": {"num": null, "fig_num": "34", "text": "Figure D.3: The Correspondence Between Output Mapping Labels in DTD. Each column represents the mapping between the target label and its respective top-1 source label in FullyMap. (a) FullyMap (top-1 class having the largest weight) with Swin-T (second row). (b) IterMap-1 with Swin-T (third row).", "uris": null, "type_str": "figure"}, "FIGREF15": {"num": null, "fig_num": "4", "text": "Figure D.4 illustrates the average tuning results for all datasets across different settings, including image scale, mapping methods, pre-trained models, and so on (see Figure A.2). In Figure D.4(a), among the vision models (ResNet18, ResNeXt-IG, Swin-T), the FullyMap demonstrates superior performance compared to the other methods. However, for CLIP, a vision-language model, the FullyMap only shows a slight advantage over the others. Furthermore, Figure D.4(b)indicates that larger initial scales, such as 1.0 or 1.5 (yielding square images with a width of 128 × 1.0 or 128 × 1.5), generally lead to better results when using FreqMap, IterMap, or FullyMap. Last, since both FreqMap and IterMap can configure the number (m) of source labels mapped to each target class, we found that increasing this count generally improves accuracy for the three vision models (seeFigure D.4(c)). However, for CLIP, mapping five source labels appears to be the optimal choice based on the average tuning results.", "uris": null, "type_str": "figure"}, "FIGREF16": {"num": null, "fig_num": "1", "text": "Figure E.1: The Accuracy Difference of AutoVP Variations. The chart shows the difference in accuracy between AutoVP variations with and without the CLIP text encoder. The numbers above the bars indicate the accuracy difference.", "uris": null, "type_str": "figure"}, "FIGREF17": {"num": null, "fig_num": null, "text": "", "uris": null, "type_str": "figure"}, "TABREF1": {"html": null, "num": null, "content": "<table/>", "text": "Comparison of AutoVP with other baselines, including Linear Probing, CLIP zero-shot inference with text prompts (i.e. CLIP-TP in<PERSON><PERSON><PERSON> et al. (", "type_str": "table"}, "TABREF4": {"html": null, "num": null, "content": "<table><tr><td>Dataset</td><td>AutoVP Setting</td><td>AutoVP</td><td colspan=\"3\">ILM-VP CLIP-VP LP</td></tr><tr><td>SVHN (<PERSON><PERSON> et al., 2011) CIFAR10 (<PERSON><PERSON><PERSON><PERSON> &amp; <PERSON>, 2009) Flowers102 (<PERSON><PERSON><PERSON> &amp; <PERSON>, 2008) Food101 (<PERSON><PERSON> et al., 2014) UCF101 (<PERSON><PERSON><PERSON> et al., 2012) OxfordIIITPet (<PERSON> et al., 2012) CIFAR100 (<PERSON><PERSON><PERSON><PERSON> &amp; <PERSON>, 2009) EuroSAT (<PERSON><PERSON><PERSON> et al., 2019) DTD (<PERSON><PERSON><PERSON><PERSON> et al., 2014) ISIC (<PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON><PERSON> et al., 2018) FMoW (<PERSON> et al., 2018) GTSRB (<PERSON><PERSON> et al., 2013) Average Accuracy</td><td colspan=\"2\">FullyMap, p = 51 IterMap-1, p = 23 FullyMap, p = 16 FreqMap-1, p = 16 82.3 ± 0.1 92.9 ± 0.2 95.2 ± 0.0 90.4 ± 0.6 FullyMap, p = 16 73.1 ± 0.6 FreqMap-10, p = 16 88.2 ± 0.2 FullyMap, p = 31 77.9 ± 0.6 FullyMap, p = 16 96.8 ± 0.2 FullyMap, p = 17 62.5 ± 0.3 IterMap-1, p = 16 74.0 ± 1.0 FullyMap, p = 16 40.8 ± 0.8 FullyMap, p = 80 93.1 ± 0.2 80.6</td><td>91.2 94.4 83.7 79.1 70.6 85.0 73.9 96.9 63.9 73.3 36.8 92.6 78.5</td><td>88.4 94.2 70.3 78.9 66.1 85.0 75.3 96.4 57.1 75.1 32.9 92.4 76.0</td><td>65.4 95.0 96.9 84.6 83.3 89.2 80.0 95.3 74.6 71.9 36.3 85.8 79.9</td></tr></table>", "text": "Comparison of VP testing accuracy (%) using CLIP as a pre-trained model on 12 datasets; the optimal tuning settings of AutoVP and the final prompts sizes p are also provided. In the AutoVP setting field, the notation \"Mapping-m\" represents mapping m source classes to each target class.", "type_str": "table"}, "TABREF6": {"html": null, "num": null, "content": "<table><tr><td/><td/><td>.1: Dataset Setting</td><td/><td/></tr><tr><td>Dataset</td><td colspan=\"4\">Class Number Training Set Size Testing Set Size Batch Size</td></tr><tr><td>SVHN</td><td>10</td><td>73,257</td><td>26,032</td><td>128</td></tr><tr><td>EuroSAT</td><td>10</td><td>13,500</td><td>8,100</td><td>128</td></tr><tr><td>Flowers102</td><td>102</td><td>4,093</td><td>2,463</td><td>64</td></tr><tr><td>CIFAR100</td><td>100</td><td>50,000</td><td>10,000</td><td>128</td></tr><tr><td>UCF101</td><td>101</td><td>7,639</td><td>3,783</td><td>128</td></tr><tr><td>DTD</td><td>47</td><td>2,820</td><td>1,692</td><td>32</td></tr><tr><td>FMoW</td><td>62</td><td>76,863</td><td>22,108</td><td>128</td></tr><tr><td>GTSRB</td><td>43</td><td>26,640</td><td>12,630</td><td>128</td></tr><tr><td>CIFAR10</td><td>10</td><td>50,000</td><td>10,000</td><td>128</td></tr><tr><td>Food101</td><td>101</td><td>75,750</td><td>25,250</td><td>128</td></tr><tr><td>OxfordIIITPet</td><td>37</td><td>3,680</td><td>3,669</td><td>128</td></tr><tr><td>ISIC</td><td>7</td><td>4,990</td><td>555</td><td>128</td></tr><tr><td colspan=\"2\">B.2 BASELINES DETAILS</td><td/><td/><td/></tr></table>", "text": "", "type_str": "table"}, "TABREF7": {"html": null, "num": null, "content": "<table><tr><td colspan=\"2\">Published as a conference paper at ICLR 2024</td><td/><td/></tr><tr><td>C.2 DATA SCALABILITY</td><td/><td/><td/></tr><tr><td>Dataset</td><td>AutoVP Setting</td><td>AutoVP</td><td colspan=\"2\">ILM-VP LP</td></tr><tr><td colspan=\"3\">SVHN CIFAR10 Flowers102 Food101 UCF101 OxfordIIITPet FullyMap, p = 16 82.65 ± 0.84 FullyMap, p = 48 83.74 ± 0.45 FullyMap, p = 48 87.81 ± 0.17 FullyMap, p = 16 85.40 ± 1.89 FullyMap, p = 16 54.15 ± 3.53 FullyMap, p = 16 55.86 ± 1.81 CIFAR100 FullyMap, p = 16 63.67 ± 3.48 EuroSAT FullyMap, p = 48 93.01 ± 0.15 DTD FullyMap, p = 16 54.82 ± 1.14 ISIC FullyMap, p = 16 67.44 ± 1.22 FMoW FullyMap, p = 16 30.17 ± 0.06 GTSRB FullyMap, p = 16 81.52 ± 1.21</td><td>75.2 65.5 27.9 14.8 23.9 65.4 24.8 85.2 35.3 57.5 14.8 52.0</td><td>65.0 85.9 88.0 50.6 63.2 87.2 63.3 93.8 60.0 68.6 28.4 77.4</td></tr><tr><td colspan=\"2\">Average Accuracy</td><td>70.02</td><td>45.2</td><td>69.3</td></tr><tr><td>Table C.2: Dataset</td><td>AutoVP Setting</td><td/><td>AutoVP</td><td>LP</td></tr><tr><td>SVHN CIFAR10 Flowers102 Food101 UCF101 OxfordIIITPet CIFAR100 EuroSAT DTD ISIC FMoW GTSRB</td><td colspan=\"4\">CLIP, FullyMap, p = 51 ResNeXt101-IG, FullyMap, p = 48 95.89 ± 0.07 93.89 92.86 ± 0.18 65.40 Swin-T, FullyMap, p = 16 93.48 ± 0.52 95.75 CLIP, FreqMap-1, p = 16 82.28 ± 0.09 84.60 Swin-T, FullyMap, p = 16 72.96 ± 0.26 75.96 Swin-T, FullyMap, p = 16 90.20 ± 0.55 93.04 ResNeXt101-IG, FullyMap, p = 48 79.76 ± 0.47 76.09 Swin-T, FullyMap, p = 16 95.98 ± 0.02 95.50 Swin-T, FullyMap, p = 16 69.25 ± 0.58 71.49 Swin-T, FullyMap, p = 16 71.66 ± 1.45 72.22 Swin-T, FullyMap, p = 48 39.79 ± 0.83 32.73 Swin-T, FullyMap, p = 55 88.10 ± 2.11 74.97</td></tr><tr><td/><td>Average Accuracy</td><td/><td>81.02</td><td>77.64</td></tr></table>", "text": "1: AutoVP with ResNet18. Comparison of VP test accuracy (%) using ResNet18 as the pre-trained model on 12 datasets. AutoVP with source model selection. This table displays the best tuning setting without any restriction on the choice of pre-trained model, and shows the test accuracy (%) of <PERSON><PERSON> and the LP baseline of the chosen model across 12 datasets.In the data scalability experiments, FigureC.1 illustrates the performance of AutoVP and <PERSON> on each dataset under various data usage proportions. The corresponding settings can be found in TableC.3. When the chosen pre-trained model is fixed to CLIP, AutoVP outperformed LP in scenarios with limited data on most of the datasets. In some datasets, such as SVHN, GTSRB, and EuroSAT, AutoVP performs better than LP for all proportions of data. Besides, in OxfordIIITPet, CIFAR100, DTD, Food101, UCF101, and Flowers102, <PERSON><PERSON> also obtains promising performance compared to <PERSON> on a small amount of data. This suggests that AutoVP will be a more suitable and effective solution than LP, especially when training data is limited.", "type_str": "table"}, "TABREF8": {"html": null, "num": null, "content": "<table><tr><td colspan=\"5\">C.3 DOWNSTREAM DATASET ANALYSIS (ID/OOD vs. ACCURACY GAIN)</td><td/></tr><tr><td>Dataset</td><td>100%</td><td>50%</td><td>25%</td><td>10%</td><td>1%</td></tr><tr><td>SVHN</td><td>FullyMap, p = 53</td><td>FullyMap, p = 51</td><td>FullyMap, p = 49</td><td colspan=\"2\">FullyMap, p = 80 FullyMap, p = 80</td></tr><tr><td>CIFAR10</td><td colspan=\"3\">IterMap-1, p = 22 FreqMap-1, p = 16 FreqMap-5, p = 16</td><td colspan=\"2\">IterMap-5, p = 16 IterMap-1, p = 16</td></tr><tr><td>Flowers102</td><td colspan=\"3\">FullyMap, p = 16 FreqMap-1, p = 16 FreqMap-1, p = 16</td><td colspan=\"2\">IterMap-1, p = 16 FullyMap, p = 16</td></tr><tr><td>Food101</td><td>FreqMap-1, p = 16</td><td>IterMap-5, p = 16</td><td>IterMap-1, p = 16</td><td colspan=\"2\">IterMap-1, p = 16 FullyMap, p = 16</td></tr><tr><td>UCF101</td><td>FullyMap, p = 17</td><td>FullyMap, p = 16</td><td>IterMap-1, p = 16</td><td colspan=\"2\">FullyMap, p = 16 FullyMap, p = 16</td></tr><tr><td colspan=\"4\">OxfordIIITPet FreqMap-10, p = 16 FreqMap-5, p = 16 FreqMap-5, p = 16</td><td colspan=\"2\">FullyMap, p = 16 FullyMap, p = 16</td></tr><tr><td>CIFAR100</td><td>FullyMap, p = 30</td><td>FullyMap, p = 26</td><td>IterMap-1, p = 16</td><td colspan=\"2\">FreqMap-1, p = 16 FullyMap, p = 16</td></tr><tr><td>EuroSAT</td><td>FullyMap, p = 16</td><td>FullyMap, p = 16</td><td>FullyMap, p = 16</td><td colspan=\"2\">FullyMap, p = 16 FullyMap, p = 16</td></tr><tr><td>DTD</td><td colspan=\"2\">FullyMap, p = 18 IterMap-10, p = 16</td><td>IterMap-5, p = 16</td><td colspan=\"2\">FullyMap, p = 16 FullyMap, p = 16</td></tr><tr><td>ISIC</td><td colspan=\"2\">IterMap-1, p = 16 FreqMap-1, p = 16</td><td>FullyMap, p = 80</td><td colspan=\"2\">FreqMap-5, p = 48 FullyMap, p = 80</td></tr><tr><td>FMoW</td><td>FullyMap, p = 16</td><td>FullyMap, p = 16</td><td colspan=\"3\">FullyMap, p = 16 FreqMap-10, p = 16 FullyMap, p = 17</td></tr><tr><td>GTSRB</td><td>FullyMap, p = 80</td><td>FullyMap, p = 16</td><td>FullyMap, p = 79</td><td colspan=\"2\">FullyMap, p = 48 FullyMap, p = 80</td></tr></table>", "text": "3: Data Scalability Settings. The table shows the settings for data scalability experiments with data usage ranging from 100% to 1%. The source pre-trained model is fixed to CLIP. The notation \"Mapping-m\" represents mapping m source classes to each target class.", "type_str": "table"}, "TABREF9": {"html": null, "num": null, "content": "<table><tr><td>Dataset</td><td>AutoVP Setting</td><td colspan=\"3\">AutoVP LP ILM CLIP-VP</td></tr><tr><td>SUN397</td><td>FullyMap, p = 16</td><td>65.4</td><td>70.9 61.2</td><td>60.5</td></tr><tr><td colspan=\"2\">StanfordCar FullyMap, p = 16</td><td>61.8</td><td>77.6 57.6</td><td>56.2</td></tr><tr><td>RESISC</td><td>FullyMap, p = 17</td><td>88.5</td><td>91.7 86.6</td><td>84.5</td></tr><tr><td>CLEVR</td><td>FullyMap, p = 16</td><td>83.0</td><td>68.0 83.1</td><td>81.4</td></tr><tr><td colspan=\"4\">D ANALYSIS OF AUTOVP RESULTS</td><td/></tr></table>", "text": "Performance on Additional Datasets. The testing accuracy (%) for four additional datasets commonly found in VP research. The figure on the right side shows the accuracy gains.", "type_str": "table"}, "TABREF11": {"html": null, "num": null, "content": "<table><tr><td>Setting</td><td>Tuning Selection Testing Accuracy (%)</td></tr><tr><td>AutoVP</td><td/></tr><tr><td>w/o LR &amp; WD tuning</td><td/></tr></table>", "text": "3: Hyper-Parameter Tuning for Learning Rate (LR) and Weight Decay (WD). The table displays the optimal tuning configurations and corresponding testing accuracy for both scenarios with and without additional LR and WD tuning options for AutoVP on Flowers102. The pre-trained model used is CLIP. The highest accuracy is marked in bold.", "type_str": "table"}, "TABREF12": {"html": null, "num": null, "content": "<table><tr><td>AutoVP</td><td>88.2 61.8</td><td>90.4</td><td>82.3 65.4 62.5</td><td>92.9</td><td>96.8</td><td>88.5</td><td>82.8</td><td>73.1 80.4</td></tr><tr><td colspan=\"2\">BlackVIP 89.7 65.6</td><td>70.6</td><td>86.6 64.7 45.2</td><td>44.3</td><td>73.1</td><td>64.5</td><td>36.8</td><td>69.1 64.6</td></tr><tr><td colspan=\"2\">Datasets</td><td colspan=\"2\">AutoVP</td><td colspan=\"2\">ILM-VP</td><td/><td colspan=\"2\">ILM-VP</td></tr><tr><td/><td/><td/><td/><td colspan=\"2\">w/ Tuning</td><td/><td colspan=\"2\">w/o Tuning</td></tr><tr><td colspan=\"2\">UCF101</td><td colspan=\"2\">FullyMap, p = 16</td><td/><td/><td/><td/><td/></tr></table>", "text": "4: ILM-VP with Hyper-Parameter Tuning. The chosen tuning configurations and the corresponding testing accuracy (%) in ILM-VP. The highest accuracy is marked in bold. Published as a conference paper at ICLR 2024 E.5 COMPARISON OF AUTOVP AND BLACKVIP Table E.5: AutoVP vs. BlackVIP. The testing accuracy (%) of AutoVP and BlackVIP. Pets Cars Flowers Food SUN DTD SVHN EuroSAT RESISC CLEVR UCF Avg.", "type_str": "table"}, "TABREF13": {"html": null, "num": null, "content": "<table><tr><td colspan=\"2\">F.2 COMPUTING RESOURCES</td><td/><td/></tr><tr><td/><td colspan=\"4\">Hyper-Parameter Tuning 200-Epoch Training Total Execution Time</td></tr><tr><td>Time</td><td>146 mins</td><td colspan=\"2\">31 mins</td><td>177 mins</td></tr><tr><td/><td>Experimental Info.</td><td/><td>AutoVP</td><td>LP</td><td>FF</td></tr><tr><td/><td>Accuracy (%)</td><td/><td>96.84</td><td>94.62 96.78</td></tr><tr><td/><td>Execution Time (second)</td><td/><td>2448</td><td>2370</td><td>3081</td></tr><tr><td/><td colspan=\"2\">Trainable Parameter Size (Million)</td><td>0.15</td><td>0.005 151.28</td></tr></table>", "text": "1: Comparison of AutoVP, Linear Probing (LP), and Full Fine-Tuning (FF). With the EuroSAT dataset and using CLIP as the pre-trained model, the table displays the testing accuracy, execution time, and trainable parameter size associated with each respective method.In this section, we provide the execution time (measured on NVIDIA GeForce RTX 3090) and the comparison of trainable parameters of AutoVP. For ease of comparison, we use the Flower102 dataset for illustration. In TableF.2, we provide the end-to-end execution time (hyper-parameter tuning + 200-epoch training) of AutoVP. When we measure only the 200-epoch training, AutoVP demonstrates its competitiveness in terms of similar or even lower training time (see TableF.3), compared to the state-of-the-art VP baselines (ILM-VP and CLIP-VP The comparison of trainable parameters can also be found in TableF.4. TableF.2: Flowers102 End-to-End Execution Time. For the Flowers102 dataset, the pre-trained model selected by AutoVP is Swin-T, the output mapping is FullyMap, and the prompt size is 16.", "type_str": "table"}, "TABREF14": {"html": null, "num": null, "content": "<table><tr><td colspan=\"5\">Pre-trained model AutoVP ILM-VP CLIP-VP Linear Probing</td></tr><tr><td>Swin-T</td><td>31</td><td>43</td><td>-</td><td>28</td></tr><tr><td>CLIP</td><td>39</td><td>76</td><td>38</td><td>40</td></tr></table>", "text": "3: Execution Time Comparison. The 200-epoch training time (in minutes) on the Flow-ers102 dataset varies depending on the chosen pre-trained model: Swin-T or CLIP. In both cases, AutoVP utilizes the FullyMap as the output mapping method with the prompt size 16.", "type_str": "table"}, "TABREF15": {"html": null, "num": null, "content": "<table><tr><td/><td/><td>AutoVP</td><td/><td/><td colspan=\"2\">Linear Probing Full Finetune</td></tr><tr><td/><td colspan=\"4\">SemanticMap FreqMap IterMap FullyMap</td><td/><td/></tr><tr><td>ResNet18</td><td>0.15</td><td>0.15</td><td>0.15</td><td>0.20</td><td>0.03</td><td>11.20</td></tr><tr><td>ResNeXt101-IG</td><td>0.15</td><td>0.15</td><td>0.15</td><td>0.20</td><td>0.11</td><td>86.85</td></tr><tr><td>Swin-T</td><td>0.15</td><td>0.15</td><td>0.15</td><td>0.20</td><td>0.04</td><td>27.56</td></tr><tr><td>CLIP</td><td>0.15</td><td>0.15</td><td>0.15</td><td>0.49</td><td>0.03</td><td>151.23</td></tr></table>", "text": "4: Trainable Parameter Size. The average trainable parameter sizes (million) are calculated across the 12 datasets for different pre-trained models, mapping methods, and baselines.", "type_str": "table"}}}}