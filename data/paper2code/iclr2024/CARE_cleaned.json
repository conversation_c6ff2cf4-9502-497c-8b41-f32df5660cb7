{"paper_id": "CARE", "title": "STRUCTURING REPRESENTATION GEOMETRY WITH RO-TATIONALLY EQUIVARIANT CONTRASTIVE LEARNING", "abstract": "Self-supervised learning converts raw perceptual data such as images to a compact space where simple Euclidean distances measure meaningful variations in data. In this paper, we extend this formulation by adding additional geometric structure to the embedding space by enforcing transformations of input space to correspond to simple (i.e., linear) transformations of embedding space. Specifically, in the contrastive learning setting, we introduce an equivariance objective and theoretically prove that its minima forces augmentations on input space to correspond to rotations on the spherical embedding space. We show that merely combining our equivariant loss with a non-collapse term results in non-trivial representations, without requiring invariance to data augmentations. Optimal performance is achieved by also encouraging approximate invariance, where input augmentations correspond to small rotations. Our method, CARE: Contrastive Augmentationinduced Rotational Equivariance, leads to improved performance on downstream tasks, and ensures sensitivity in embedding space to important variations in data (e.g., color) that standard contrastive methods do not achieve. Code is available at https://github.com/Sharut/CARE.", "pdf_parse": {"paper_id": "CARE", "abstract": [{"text": "Self-supervised learning converts raw perceptual data such as images to a compact space where simple Euclidean distances measure meaningful variations in data. In this paper, we extend this formulation by adding additional geometric structure to the embedding space by enforcing transformations of input space to correspond to simple (i.e., linear) transformations of embedding space. Specifically, in the contrastive learning setting, we introduce an equivariance objective and theoretically prove that its minima forces augmentations on input space to correspond to rotations on the spherical embedding space. We show that merely combining our equivariant loss with a non-collapse term results in non-trivial representations, without requiring invariance to data augmentations. Optimal performance is achieved by also encouraging approximate invariance, where input augmentations correspond to small rotations. Our method, CARE: Contrastive Augmentationinduced Rotational Equivariance, leads to improved performance on downstream tasks, and ensures sensitivity in embedding space to important variations in data (e.g., color) that standard contrastive methods do not achieve. Code is available at https://github.com/Sharut/CARE.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "It is only partially understood what structure neural network representation spaces should possess in order to enable intelligent behavior to emerge efficiently (<PERSON> et al., 2022) . One known key ingredient is to learn low-dimensional spaces in which simple Euclidean distances effectively measure the similarity between data, as demonstrated by powerful self-supervised methods for web-scale learning (<PERSON> et al., 2020; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2021) . However, many use cases require the use of richer structural relationships that similarities between data cannot capture. One example that has enjoyed considerable success is the encoding of relations between objects (X is a parent of Y, A is a treatment for B) as simple transformations of embeddings (e.g., translations), which has driven learning with knowledge graphs (<PERSON><PERSON> et al., 2013; <PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2022) . But similar capabilities have been notably absent from existing self-supervised learning recipes.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Recent contrastive self-supervised learning approaches have explored ways to close this gap by ensuring representation spaces are sensitive to certain transformations of input data, such as variations in color (<PERSON><PERSON><PERSON><PERSON> et al., 2022; Devillers & Lefort, 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2023) that earlier contrastive methods lacked. Encouraging sensitivity is especially important in contrastive learning, as it is known to learn shortcuts that forget features that are not needed to solve the pretraining task (<PERSON> et al., 2021) . This line of work formalizes sensitivity in terms of equivariance: transformations of input data correspond to predictable transformations in representation space. Equivariance requires specifying a family of transformations a ∈ A in the input space, a corresponding transformation T a in representation space, and training f so that Figure 1 : CARE is an equivariant contrastive learning approach that trains augmentations (cropping, blurring, etc.) of input data to correspond to orthogonal transformations of embedding space.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "f (a(x)) ≈ T a f (x). Prior works have considered a learnable feed-forward network for T a , (Devillers & Lefort, 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2023) . However, we find that this approach suffers from geometric pathologies, such as inconsistency under compositions: T a2•a1 f (x) ̸ = T a2 T a1 f (x). Furthermore, encoding the relation between the embeddings of x and a(x) in a non-linear way conflicts with the aim of learning a representation space where linear transformations relate different instances.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "To address these concerns we propose CARE, an equivariant contrastive learning framework that learns to translate augmentations in the input space (such as cropping, blurring, and jittering) into simple linear transformations in feature space. Here, we use the sphere as our feature space (the standard contrastive learning space) and hence consider the isometries of the sphere: rotations and reflections, i.e., orthogonal transformations. As orthogonal transformations are less expressive (by design), our learning problem is more constrained, meaning that prior approaches for learning non-linear transforms cannot be used (see Section 3). Instead, CARE trains f to preserve angles, i.e., f (a(x)) ⊤ f (a(x ′ )) ≈ f (x) ⊤ f (x ′ ), a property that must hold if f is orthogonally equivariant. We show that achieving low error on this seemingly weaker property also implies approximate equivariance and enjoys consistency under compositions. Critically, we can easily integrate CARE into contrastive learning workflows since both operate by comparing pairs of data.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "The key contributions of this work include:", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• Introducing CARE, a novel equivariant contrastive learning framework that trains transformations (cropping, jittering, blurring, etc.) in input space to approximately correspond to local orthogonal transformations in representation space.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• Theoretically proving and empirically demonstrating that CARE places an orthogonally equivariant structure on the embedding space.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• Showing that CARE increases sensitivity to features (e.g., color) compared to invariancebased contrastive methods, and also improves performance on image recognition tasks.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Given access only to samples from a marginal distribution p(x) on some input space X such as images, the goal of representation learning is commonly to train a feature extracting model f : X → S d-1 mapping to the unit sphere S d-1 = {z ∈ R d : ∥z∥ 2 = 1}. A common strategy to automatically generate supervision from the data is to additionally introduce a space of augmentations A, containing maps a : X → X which slightly perturb inputs x (blurring, cropping, jittering, etc.). Siamese selfsupervised methods learn representation spaces that reflect the relationship between the embeddings of x = a(x) and x + = a + (x), commonly by training f to be invariant or equivariant to the augmentations in the input space (<PERSON> & <PERSON>, 2021) .", "section": "LEARNING", "sec_num": null}, {"text": "Invariance to augmentation. One approach is to train f to embed x and x + nearby-i.e., so that f (x) = f (x + ) is invariant to augmentations. The InfoNCE loss (<PERSON> et al., 2018; <PERSON><PERSON><PERSON> & H<PERSON>n, 2010) used in contrastive learning achieves precisely this:", "section": "LEARNING", "sec_num": null}, {"text": "EQUATION", "section": "LEARNING", "sec_num": null}, {"text": "where τ > 0 is a temperature hyperparameter, and x - i ∼ p are negative samples from the marginal distribution on X . As noted by <PERSON> (2020) , the contrastive training mechanism balances invariance to augmentations with a competing objective: uniformly distributing embeddings over the sphere, which rules out trivial solutions such as constant functions.", "section": "LEARNING", "sec_num": null}, {"text": "Whilst contrastive learning has produced considerable advances in large-scale learning (<PERSON><PERSON> et al., 2021) , several lines of work have begun to probe the fundamental role of invariance in contrastive learning. Two key conclusions of recent investigations include: 1) invariance limits the expressive power of features learned by f , as it removes information about features or transformations that may be relevant in fine-grained tasks (<PERSON> et al., 2021; <PERSON><PERSON> et al., 2022) , and 2) contrastive learning actually benefits from not having exact invariance. For instance, a critical role of the projection head is to expand the feature space so that f is not fully invariant (<PERSON> et al., 2022) , suggesting that it is preferable for the embeddings of x and x + to be close, but not identical.", "section": "LEARNING", "sec_num": null}, {"text": "Equivariance to augmentation. To address the limitations of invariance, recent work has additionally proposed to control equivariance (i.e., sensitivity) of f to data transformations (<PERSON><PERSON><PERSON><PERSON> et al., 2022; Devillers & Lefort, 2023; <PERSON><PERSON><PERSON><PERSON> et al., 2023) . Prior works can broadly be viewed as training a set of features f (sometimes alongside the usual invariant features) so that f (a(x)) ≈ T a f (x) for samples x ∼ p from the data distribution where T a is some transformation of the embedding space. A common choice is to take T a f (x) = MLP(f (x), a), a learnable feed-forward network, and optimize a loss ∥MLP(f (x), a) -f (a(x))∥ 2 . Whilst a learnable MLP ensures that information about a is encoded into the embedding of a(x), it permits complex non-linear relations between embeddings and hence does not necessarily encode relations in a linearly separable way. Furthermore, it does not enjoy the beneficial properties of equivariance in the formal group-theoretic sense, such as consistency under compositions in general:", "section": "LEARNING", "sec_num": null}, {"text": "T a2•a1 f (x) ̸ = T a2 T a1 f (x).", "section": "LEARNING", "sec_num": null}, {"text": "Instead, this work introduces CARE, an equivariant contrastive learning approach respecting two key design principles: Principle 1. The map T a satisfying f (a(x)) = T a f (x) should be linear. Principle 2. Equivariance should be learned from pairs of data, as in invariant contrastive learning.", "section": "LEARNING", "sec_num": null}, {"text": "The first principle asks that f converts complex perturbations a of input data into much simpler (i.e., linear) transformations in embedding space. Specifically, we constrain the complexity of T a by considering isometries of the sphere, O(d) = {Q ∈ R d×d : QQ T = Q T Q = I}, containing all rotations and reflections. Throughout this paper we define f (a(x)) = T a f (x) for T a ∈ O(d) to be orthogonal equivariance. This approach draws heavily from ideas in linear representation theory (<PERSON> & <PERSON>, 1966; <PERSON><PERSON> et al., 1977) , which studies how to convert abstract group structures into matrix spaces equipped with standard matrix multiplication as the group operation.", "section": "LEARNING", "sec_num": null}, {"text": "The second principle stipulates how we want to learn orthogonal equivariance. Naively following previous non-linear approaches is challenging as our learning problem is more constrained, requiring learning a mapping a → R a to orthogonal matrices. Furthermore, for a single (a, x) pair, the orthogonal matrix R a such that f (a(x)) = R a f (x) is not unique, making it hard to directly learn R a . We sidestep these challenges by, instead of explicitly learning R a , training f so that an augmentation a applied to two different inputs x, x + produces the same change in embedding space.", "section": "LEARNING", "sec_num": null}, {"text": "Our method, CARE, encodes data augmentations (cropping, blurring, jittering, etc.) as O(d) transformations of embeddings using an equivariance-promoting objective function. CARE can be viewed as an instance of symmetry regularization, a term introduced by <PERSON><PERSON><PERSON> et al. (2022) .", "section": "LEARNING", "sec_num": null}, {"text": "This section introduces a simple and practical approach for training a model f : X → S d-1 so that f is orthogonally equivariant: i.e., a data augmentation a ∼ A (cropping, blurring, jittering, etc.) applied to any input x ∈ X causes the embedding f (x) to be transformed by the same R a ∈ O(d) for all x ∈ X : f (a(x)) = R a f (x).", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "To achieve this, we consider the following loss:", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "EQUATION", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "Since inner products describe angles on the sphere, this objective enforces the angles between the embeddings of independent samples x and x ′ to be the same as those between their transformed counterparts a(x) and a(x ′ ). This is necessarily true if f is orthogonally equivariant or, more generally, R a ∈ O(d) exists. But the converse-that L equi = 0 implies orthogonal equivarianceis non-obvious. In Section 3.1 we theoretically analyze L equi , demonstrating that it does indeed enforce mapping input augmentations to orthogonal transformations of embeddings. In practice, we replace the f (x", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": ") ⊤ f (x ′ ) term with f (a ′ (x)) ⊤ f (a ′ (x ′", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": ")) for a freshly sampled a ′ ∼ A, noting that minimizing this variant also minimizes L equi , if we assume a ′ can be the identity function with non-zero probability. A trivial but undesirable solution that minimizes L equi is to collapse the embeddings of all points to be the same (see Figure 10 ). One natural approach to avoiding trivial solutions is to combine the equivariance loss with a non-collapse term such as the uniformity & Isola, 2020) whose optima f distribute points uniformly over the sphere:", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "L unif (f ) = log E x,x ′ ∼X exp f (x) ⊤ f (x ′ ) (Wang", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "EQUATION", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "This is directly comparable to the InfoNCE loss, which can similarly be decomposed into two terms:", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "EQUATION", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "where", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "L inv (f ) = E a,a ′ ∼A ∥f (a(x)) -f (a ′ (x))∥ is minimized when f is invariant to A-i.e., f (a(x)) = f (x).", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "Figure 10 in the Appendix shows that training using L equi + L unif yields nontrivial representations. However, the performance is below that of invariance-based contrastive learning approaches. We hypothesize that this is because data augmentations-which make small perceptual changes to data-should correspond to small perturbations of embeddings, which L equi does not enforce.", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "To rule out this possibility, we introduce CARE: Contrastive Augmentation-induced Rotational Equivariance. CARE additionally enforces the orthogonal transformations in embedding space to be localized by reintroducing an invariance loss term L inv to encourage f to be approximately invariant. Doing so breaks the indifference of L equi between large and small rotations, biasing towards small. Specifically, we propose the following objective that combines our equivariant loss with InfoNCE:", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "EQUATION", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "where λ weights the equivariant loss. We note that many variations of this approach are possible. For instance, the equivariant loss and InfoNCE loss could use different augmentations, resulting in invariance to specific transformations while maintaining rotational equivariance to others, similar to <PERSON><PERSON><PERSON><PERSON> et al. (2022) . The InfoNCE loss can also be replaced by other Siamese self-supervised losses. We leave further exploration of these possibilities to future work. In all, CARE consists of three components: (i) a term to induce orthogonal equivariance; (ii) a non-collapse term; and (iii) an invariance term to enforce localized transformations on the embedding space.", "section": "CARE: CONTRASTIVE AUGMENTATION-INDUCED ROTATIONAL EQUIVARIANCE", "sec_num": "3"}, {"text": "In this section, we establish that matching angles via L equi leads to a seemingly stronger property. Specifically, L equi = 0 implies the existence of an orthogonal matrix R a ∈ O(d) for any augmentation a, such that f (a(x)) = R a f (x) holds for all x. The converse also holds and is easy to see. Indeed, suppose such an", "section": "THEORETICAL PROPERTIES OF ORTHOGONALLY EQUIVARIANT LOSS", "sec_num": "3.1"}, {"text": "R a ∈ O(d) exists. Then, f (a(x ′ )) ⊤ f (a(x)) = f (x ′ ) ⊤ R ⊤ a R a f (x) = f (x) ⊤ f (x ′", "section": "THEORETICAL PROPERTIES OF ORTHOGONALLY EQUIVARIANT LOSS", "sec_num": "3.1"}, {"text": "), which implies L equi (f ) = 0. We formulate the first direction as a proposition. Proposition 1. Suppose L equi (f ) = 0. Then for almost every a ∈ A, there is an orthogonal matrix R a ∈ O(d) such that f (a(x)) = R a f (x) for almost all x ∈ X .", "section": "THEORETICAL PROPERTIES OF ORTHOGONALLY EQUIVARIANT LOSS", "sec_num": "3.1"}, {"text": "Figure 1 illustrates this result. Crucially R a is independent of x, without which the Proposition 1 would be trivial. That is, a single orthogonal transformation R a captures the impact of applying a across the entire input space X . Consequently, exact minimization of L equi loss converts \"unstructured\" augmentations in input space to have a structured geometric interpretation as rotations in the embedding space. In Appendix B.1, we extend this result to the case where the loss is low but not exactly minimized, in which case we have a guarantee of approximate equivariance. This result can be expressed as the existence of a mapping ρ : A → O(d) that encodes the space of augmentations within O(d). This raises a natural question: how much of the structure of A does this encoding preserve? For instance, assuming A is a semi-group (i.e., closed under compositions a ′ • a ∈ A), does this transformation respect compositions: f (a ′ (a(x)) = R a ′ R a f (x)? This property does not hold for non-linear actions (Devillers & Lefort, 2023), but does for orthogonal equivariance:", "section": "THEORETICAL PROPERTIES OF ORTHOGONALLY EQUIVARIANT LOSS", "sec_num": "3.1"}, {"text": "Corollary 1. If L equi (f ) = 0 and {f (x) : x ∈ X } spans R d , then ρ : A → O(d) given by ρ(a) = R a satisfies ρ(a ′ • a) = ρ(a ′ )ρ(a)", "section": "THEORETICAL PROPERTIES OF ORTHOGONALLY EQUIVARIANT LOSS", "sec_num": "3.1"}, {"text": "for almost all a, a ′ . That is, ρ defines a group action on S d-1 up to a set of measure zero.", "section": "THEORETICAL PROPERTIES OF ORTHOGONALLY EQUIVARIANT LOSS", "sec_num": "3.1"}, {"text": "Formally, this result states that if A is a semi-group, then ρ : A → O(d) defines a group homomorphism (or linear representation of A in the sense of representation theory (<PERSON>, 1966; <PERSON><PERSON> et al., 1977) , a branch of mathematics that studies the encoding of abstract groups as spaces of linear maps). To exactly attain L equi (f ) = 0, the space of augmentations A needs to have a certain structure, but this becomes less restrictive if d is large. Assuming for simplicity that A is a group, the first isomorphism theorem for groups states that ρ(A) ≃ A/ ker(ρ). For instance, if ker(ρ) is trivial, the equivariant loss can be exactly zero when the group of augmentations is a subgroup of the orthogonal group. Examples include orthogonal transformations or rotations that fix a subspace-i.e., O(d ′ ) or SO(d ′ ) with d ′ ≤ d-or subgroups of the permutation group on d elements. Furthermore, the <PERSON> theorem implies that any compact Lie group can be realized as a closed subgroup of O(d) for some d (<PERSON>, 1927) . As with Proposition 1, we also extend this result to the case of low loss that is not exactly zero, where we show that compositionality is approximately preserved.", "section": "THEORETICAL PROPERTIES OF ORTHOGONALLY EQUIVARIANT LOSS", "sec_num": "3.1"}, {"text": "R a f (x + 1 ) f (x) f f a f f (x + 2 ) R a′", "section": "THEORETICAL PROPERTIES OF ORTHOGONALLY EQUIVARIANT LOSS", "sec_num": "3.1"}, {"text": "Proposition 1 states that perfectly optimizing L equi = 0 produces an f that is equivariant, encoding augmentations in the input space as orthogonal transformation in the embedding space. Notably, since the computation of L equi solely relies on pairwise data instances x, x ′ ∈ X , it naturally aligns with the contrastive learning paradigm that already works with pairs of data.", "section": "EXTENSIONS TO OTHER GROUPS", "sec_num": "3.2"}, {"text": "In fact, it is possible to extend the idea of CARE and its benefits to some other group actions. Mathematically, invariants of the action of", "section": "EXTENSIONS TO OTHER GROUPS", "sec_num": "3.2"}, {"text": "O(d) on n points-seen in (R d ) n as Q (x 1 , . . . x n ) = (Q x 1 , . . . , Q x n )-can be expressed as a function of pairs of objects (x ⊤ i x j ) i,j=1...n", "section": "EXTENSIONS TO OTHER GROUPS", "sec_num": "3.2"}, {"text": ". This is because the orthogonal group is defined as the stabilizer of a bilinear form. In other words, letting B(x, x ′ ) = x ⊤ x ′ denote the standard inner product, we have", "section": "EXTENSIONS TO OTHER GROUPS", "sec_num": "3.2"}, {"text": "O(d) = {A ∈ GL(d) : B(Ax, Ax ′ ) = B(x, x ′ ) for all x, x ′ ∈ R d }.", "section": "EXTENSIONS TO OTHER GROUPS", "sec_num": "3.2"}, {"text": "(7) This argument applies more generally to other groups that are defined as stabilizers of bilinear forms. For instance, the <PERSON><PERSON><PERSON> group, which has applications in the context of special relativity, can be defined as the stabilizer of the Minkowski inner product. Additionally, the symplectic group, which is used to characterize Hamiltonian dynamical systems, can be defined in a similar manner.", "section": "EXTENSIONS TO OTHER GROUPS", "sec_num": "3.2"}, {"text": "Such extensions to other groups allow to use CARE for different embedding space geometries. For instance, several recent works have used a hyperbolic space as an embedding space for self-supervised learners (<PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) . If we constrain our embedding to a hyperboloid model of hyperbolic space, then linear isometries of this space are precisely the <PERSON><PERSON><PERSON> group. Further discussions on extensions to other groups are given in Appendix D. ", "section": "EXTENSIONS TO OTHER GROUPS", "sec_num": "3.2"}, {"text": "To probe the geometric properties of CARE, we consider two efficiently computable metrics for empirically measuring the orthogonal equivariance in the embedding space.", "section": "MEASURING ORTHOGONAL ACTION ON EMBEDDING SPACE", "sec_num": "4"}, {"text": "<PERSON><PERSON><PERSON>'s problem. A natural way to assess the equivariance of f is to sample a batch of data {x i } n i=1 and an augmentation a and test to what extent applying a transforms the embeddings of each x i the same way. To measure this we compute a single rotation that approximates the map from f (x i ) to f (a(x i )) for all i. Let F and F a ∈ R d×n have ith columns f (x i ) and f (a(x i )) respectively, then we compute the error W f = min R∈SO(d) ∥RF -F a ∥ Fro , where ∥ • ∥ Fro denotes the Frobenius norm.", "section": "MEASURING ORTHOGONAL ACTION ON EMBEDDING SPACE", "sec_num": "4"}, {"text": "If W f = 0, then f (a(x i )) = R a f (x i ) for all i. This is a well-studied problem known as <PERSON><PERSON><PERSON>'s problem. The analytic solution to <PERSON><PERSON><PERSON>'s problem is computed easily. It is nearly R * = U V ⊤ where U ΣV ⊤ is a singular value decomposition of F a F ⊤ . However, a slight modification is required as this R * could have determinant ±1, and therefore may not belong to SO(d). The only modification needed is to re-scale so that the determinant is one:", "section": "MEASURING ORTHOGONAL ACTION ON EMBEDDING SPACE", "sec_num": "4"}, {"text": "R * = U • diag 1 (n-1) , det(U )det(V ) • V ⊤", "section": "MEASURING ORTHOGONAL ACTION ON EMBEDDING SPACE", "sec_num": "4"}, {"text": "where 1 n denotes the vector in R n of all ones.", "section": "MEASURING ORTHOGONAL ACTION ON EMBEDDING SPACE", "sec_num": "4"}, {"text": "Relative rotational equivariance. Optimizing for the CARE objective may potentially result in learning invariance since for input image x, f (a(x)) = f (x) for a ∈ A is a trivial optimal solution of arg min f L equi (f ). To check that our model is learning non-trivial equivariance, we consider a metric similar to one proposed by <PERSON><PERSON><PERSON> et al. ( 2023)", "section": "MEASURING ORTHOGONAL ACTION ON EMBEDDING SPACE", "sec_num": "4"}, {"text": "EQUATION", "section": "MEASURING ORTHOGONAL ACTION ON EMBEDDING SPACE", "sec_num": "4"}, {"text": ")", "section": "MEASURING ORTHOGONAL ACTION ON EMBEDDING SPACE", "sec_num": "4"}, {"text": "Here, the denominator measures the invariance of the representation, with smaller values corresponding to greater invariance to the augmentations. The numerator, on the other hand, measures equivariance and can be simplified to", "section": "MEASURING ORTHOGONAL ACTION ON EMBEDDING SPACE", "sec_num": "4"}, {"text": "[f (a(x ′ )) ⊤ f (a(x)) -f (x) ⊤ f (x ′ ) 2 (i.e., L equi (f )) up to a", "section": "MEASURING ORTHOGONAL ACTION ON EMBEDDING SPACE", "sec_num": "4"}, {"text": "constant, because f maps to the unit sphere. The ratio γ f of these two terms measures the non-trivial equivariance, with a lower value implying greater non-trivial orthogonal equivariance.", "section": "MEASURING ORTHOGONAL ACTION ON EMBEDDING SPACE", "sec_num": "4"}, {"text": "We examine the representations learned by CARE, as well as those obtained from purely invariancebased contrastive approaches. We describe our experiment configurations in Appendix F.", "section": "EXPERIMENTS", "sec_num": "5"}, {"text": "We consider the problem of learning representations of proteins from the Protein Data Bank (<PERSON><PERSON> et al., 2021) . Each protein is described by a point cloud X ∈ R n×3 . To respect the permutation invariance of each point cloud, we take f to be a DeepSet (<PERSON><PERSON><PERSON> et al., 2017) , producing embeddings in R 16 , and train CARE using random rotations of 3D space as the augmentations-i.e., X and XR are a positive pair for R ∈ SO(3). We evaluate our models on the task of predicting the first principal component of the point cloud, an important structural property of the input. This task is rotation equivariant, so we expect that CARE should outperform the invariance-based methods such as SimCLR, as is verified in Figure 4 . We test equivariance to rotations by randomly sampling a new protein X, and a sequence of rotations {R i } 100 i=1 along each of the three orthogonal axes, evenly spaced, tracing a full 360 • rotation of the point cloud. We then compute z i = f (XR i ) for each i, and project them into a 2D space. Each row of Figure 3 shows the trajectory of 3 different proteins, and three rotation trajectories, for a given training method. We find that CARE exhibits a much more regular geometry than models trained with SimCLR, L unif , or L inv . Learning the SO(3) manifold is challenging, and previous works assume access to the corresponding group action (<PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2021) However, CARE learns it by merely using x and a(x), without relying on the group action a.", "section": "LEARNING REPRESENTATIONS OF PROTEIN POINT CLOUDS", "sec_num": "5.1"}, {"text": "<PERSON><PERSON><PERSON>'s Problem We compare ResNet-18 models pretrained with CARE and with SimCLR on CIFAR10. For each model, we compute the optimal value W f of <PERSON><PERSON><PERSON>'s problem, as introduced in Section 4, over repeated trials. In each trial, we sample a single augmentation a ∼ A at random and compute W f for f = f CARE and f = f SimCLR over the test data. We repeat this process 20 times and plot the results in Figure 5 , where the colors of dots indicate the sampled augmentation. Results show that CARE has a lower average error and worst-case error. Further, comparing point-wise for each augmentation, CARE achieves lower error in nearly all cases. Analyzing structure on a 2D manifold. To further study L equi , we train an encoder f that projects the input onto a unit circle S 1 , where orthogonal transformations are defined by angles. We measure the cosine of the angle between pairs f (x) and f (a(x)) for all x in the test set, for 20 distinct sampled augmentations a ∼ A. As shown in Figure 6 , Both CARE and SimCLR exhibit high density close to 1, demonstrating approximate invariance. However, unlike CARE, SimCLR exhibits non-zero density in the region -0.5 to -1.0, indicating that the application of augmentations significantly displaces the embeddings. Further, CARE consistently exhibits lower variance of the cosine between f (x) and f (a(x)) for a fixed augmentation, showing that it transforms all embeddings in the same way.", "section": "QUANTITATIVE MEASURES FOR ORTHOGONAL EQUIVARIANCE", "sec_num": "5.2"}, {"text": "Ablation of loss terms. The CARE loss L CARE is a weighted sum of the InfoNCE loss L InfoNCE and the orthogonal equivariance loss L equi . Figure 7 evaluates the performance of ResNet-50 models trained on CIFAR10 using L InfoNCE +λL equi for varying λ, finding optimal λ in the range 0.01 ≤ λ ≤ 0.1. We additionally split the InfoNCE loss into constituent parts L inv , L unif , and test different combinations of the three losses, including L equi . We find that using all three jointly is optimal. See Figure 10 in Appendix G.1 for detailed results.", "section": "QUANTITATIVE MEASURES FOR ORTHOGONAL EQUIVARIANCE", "sec_num": "5.2"}, {"text": "Relative rotational equivariance. We measure the relative rotational equivariance for both CARE and SimCLR over the course of pretraining by following the approach outlined in Section 4. Specifically, we compare ResNet-18 models trained using CARE and SimCLR on CIFAR10. From Figure 8 , we observe that both the models produce embeddings with comparable non-zero invariance loss L inv , indicating approximate invariance. However, they differ in their sensitivity to augmentations, with CARE attaining a much lower relative equivariance error γ f . Importantly, this shows that CARE is not achieving lower equivariance error L equi by collapsing to invariance, a trivial form of equivariance.", "section": "QUANTITATIVE MEASURES FOR ORTHOGONAL EQUIVARIANCE", "sec_num": "5.2"}, {"text": "A key property promised by equivariant contrastive models is sensitivity to specific augmentations.", "section": "QUALITATIVE ASSESSMENT OF EQUIVARIANCE", "sec_num": "5.3"}, {"text": "To qualitatively evaluate the sensitivity, or equivariance, of our models, we consider an image retrieval task on the Flowers-102 dataset (<PERSON><PERSON><PERSON> & Zisserman, 2008) , as considered by <PERSON><PERSON><PERSON><PERSON> et al. (2023) . Specifically, when presented with an input image x, we extract the top 5 nearest neighbors based on the Euclidean distance of f (x) and f (a(x)), where a ∈ A. ", "section": "QUALITATIVE ASSESSMENT OF EQUIVARIANCE", "sec_num": "5.3"}, {"text": "Next, we examine the quality of features learned by CARE for solving image classification tasks. We train ResNet-50 models on four datasets: CIFAR10, CIFAR100, STL10, and ImageNet100 using SimCLR and MoCo-v2 (see Appendix F for details). We refer to the model trained using CARE with SimCLR or MoCo-v2 backbone as CARE SimCLR and CARE MoCo-v2 respectively. For each method and dataset, we evaluate the quality of the learned features by training a linear classifier (i.e., probe (<PERSON>, 2017) ) on the frozen features of f and report the performance on the test set in Table 1 . We find consistent improvements in performance using CARE, showing the benefits of our structured embedding approach for image recognition tasks. ", "section": "LINEAR PROBE FOR IMAGE CLASSIFICATION", "sec_num": "5.4"}, {"text": "Converting transformations that are complex in input space into simple transformations in embedding space has many potential uses. For instance, modifying data (e.g., in order to reason about counterfactuals) can be viewed as transforming one embedding to another. If the sought after transformation was simple and predictable, it may be easier to find. Similarly, generalizing out-of-distribution is easier when extrapolating linearly (<PERSON> et al., 2021) , suggesting that linear transformations of embedding space may facilitate more reliable generalization. This work considers several design principles that may be broadly relevant: 1) learned equivariance preserves the expressivity of backbone architectures, and in some cases may be easier for model design than hard-coded equivariance, 2) linear group actions are desirable, but require carefully designed objectives (similar in spirit to the principle of parsimony (<PERSON> et al., 2022) , also advocated for by <PERSON><PERSON><PERSON> et al. (2022) ), and 3) orthogonal (and related) symmetries are a promising structure for Siamese network training as they can be efficiently learned using pair-wise data comparisons.", "section": "DISCUSSION", "sec_num": "6"}, {"text": "Geometry of representation space. Equivariance is a key tool for encoding geometric structuree.g., symmetries-into neural network representations (Cohen & Welling, 2016; <PERSON><PERSON><PERSON> et al., 2021) . Whilst hard-coding equivariance into model architectures is very successful, approximate learned equivariance (<PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022) , has certain advantages: 1) it applies when the symmetry is provided only by data, with no closed-form expression, 2) it can still be used when it is unclear how to hard code equivariance into the architecture, and 3) it can exploit standard high capacity architectures (<PERSON> et al., 2016; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) , benefiting from considerable engineering efforts to optimize their performance. <PERSON><PERSON><PERSON> et al. (2022) also consider learning orthogonal equivariance but consider problems where both input and embedding space are acted on by O(d). Our setting differs from this in two key ways: 1) we consider a very different set of transforms of input space-jitter, crops, etc.-and 2) CARE can be naturally integrated into contrastive learning, and 3) we theoretically study the minima of the angle-preserving loss. A related line of work, mechanistic interpretability, hypothesizes that algorithmic structure-possibly including group symmetries-emerges naturally within network connections during training (<PERSON><PERSON><PERSON> et al., 2023) . Our approach is very different from this as we directly train models to have the desired structure without relying on implicit processes.", "section": "A RELATED WORK", "sec_num": null}, {"text": "Self-supervised learning. Prior equivariant contrastive learning approaches extend the usual setup of learning invariance by learning sensitivity to certain features known to be important for downstream tasks. For instance, <PERSON><PERSON><PERSON><PERSON> et al. (2022) learns to predict the augmentation applied but only considers a discrete group of 4-fold rotations. <PERSON> et al. (2021) learns the difference of augmentation parameters and <PERSON> et al. ( 2021) constructs separate embedding sub-spaces that capture invariances to all but one augmentation. However, these approaches do not offer a meaningful structure to the embedding space. Others attempt to control how this sensitivity occurs. Specifically, Devillers & Lefort (2023); <PERSON><PERSON><PERSON><PERSON> et al. (2023) ; <PERSON><PERSON><PERSON><PERSON> et al. (2023) learn a mapping from one latent representation to another, predicting how data augmentation affects the embedding. But this does not constrain the group action on embeddings, resulting in complex non-linear augmentation maps. Finally, the recent work <PERSON><PERSON> et al. (2023) implements approximate equivariance using 2D representations.", "section": "A RELATED WORK", "sec_num": null}, {"text": "The aim of this section is to detail the proofs of the theoretical results presented in the main manuscript.", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "The key theoretical tools driving our analysis are prepared separately in Section C.", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "Throughout our analysis, we assume that all spaces (e.g., A and X ) are subspaces of Euclidean space and therefore admit a Lebesgue measure. We also assume that all distributions (e.g., a ∼ A and x ∼ X ) admit a density with respect to the <PERSON><PERSON>gue measure. With these conditions in mind, we recall the loss function that is the main object of study:", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "L equi (f ) = E a∼A E x,x ′ ∼X f (a(x ′ )) ⊤ f (a(x)) -f (x) ⊤ f (x ′ ) 2 (9)", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "Next, we re-state and prove Proposition 1, our first key result. Proposition 1. Suppose L equi (f ) = 0. Then for almost every a ∈ A, there is an orthogonal matrix", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "R a ∈ O(d) such that f (a(x)) = R a f (x) for almost all x ∈ X . Proof. Suppose that L equi (f ) = 0. This means that f (a(x ′ )) ⊤ f (a(x)) = f (x) ⊤ f (x ′ ) for almost all a ∈ G, and x, x ′ ∈ X . Setting g a (x) = f (a(x)), we have that g a (x ′ ) ⊤ g a (x) = f (x) ⊤ f (x ′ ).", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "The continuous version of the First Fundamental Theorem of invariant theory for the orthogonal group (see Proposition 5) implies that there is an", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "R a ∈ O(d) such that f (a(x)) = g a (x) = R a f (x).", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "As discussed in greater detail in the main manuscript, these results show that minimizing L equi produces a model where an augmentation a corresponds to a single orthogonal transformation of embeddings R a , independent of the input. This result is continuous in flavor as it studies the loss over the full data distribution p(x). There exists a corresponding result for the finite sample loss", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "L equi,n (f ) = E a∼A n i,j=1 f (a(x j )) ⊤ f (a(x i )) -f (x i ) ⊤ f (x j ) 2 .", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "Proposition 2. Suppose L equi,n (f ) = 0. Then for almost every a ∈ A, there is an orthogonal matrix", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "R a ∈ O(d) such that f (a(x i )) = R a f (x i ) for all i = 1, . . . , n.", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "As for the population counterpart, the proof of this result directly follows from the application of the First Fundamental Theorem of invariant theory for the orthogonal group.", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "Proof of Proposition 2. Suppose that L equi (f ) = 0. This means that for almost every a ∈ G, and every i, j = 1, . . . , n we have", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "f (a(x j )) ⊤ f (a(x i )) = f (x i ) ⊤ f (x j ).", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "In other words AA T = BB T where A, B ∈ R n×d are matrices whose ith rows are", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "A i = f (a(x i )) ⊤ and B i = f (x i ) ⊤ respectively.", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "This implies, by the First Fundamental Theorem of invariant theory for the orthogonal group (see Corollary 3), that there is an R a ∈ O(d) such that A = BR a . Considering only the ith rows of A and B leads us to conclude that f (a", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "(x i )) = R a f (x i ).", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "A corollary of Proposition 1 is that compositions of augmentations correspond to compositions of rotations.", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "Corollary 1. If L equi (f ) = 0 and {f (x) : x ∈ X } spans R d , then ρ : A → O(d) given by ρ(a) = R a satisfies ρ(a ′ • a) = ρ(a ′ )ρ(a)", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "for almost all a, a ′ . That is, ρ defines a group action on S d-1 up to a set of measure zero.", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "Proof. Applying Proposition 1 on a ′ • a as the sampled augmentation, we have that", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "f (a ′ • a(x i )) = R a ′ •a f (x i ) = ρ(a ′ • a)f (x i ).", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "However, taking x = a(x i ) and applying Proposition 1 twice we also know that f (a", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "′ • a(x i )) = f (a ′ (x)) = R a f (x) = R a ′ f (a(x i )) = R a ′ R a f (x) = ρ(a ′ )ρ(a)f (x i ). That is, ρ(a ′ • a)f (x i ) = f (a ′ • a(x i )) = ρ(a ′ )ρ(a)f (x i ).", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "Since this holds for all i and the set of", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "f (x i ) spans R d , we have that ρ(a ′ • a) = ρ(a ′ )ρ(a).", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "This corollary requires us to assume that A is a semi-group. That is, A is closed under compositions, but group elements do not necessarily have inverses and it does not need to include an identity element.", "section": "B PROOFS OF THEORETICAL RESULTS", "sec_num": null}, {"text": "For Proposition 1, we show that when our equivariance loss is zero, the learned function is exactly equivariant. In practice, the equivariance loss may not exactly be minimized. Here, we give an approximate equivariance guarantee when the loss is small. The proof is based on Theorem 1 of <PERSON><PERSON> et al. ( 2020) (see Section C.1). For that, define", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "L equi (f, a) = E x,x ′ ∼X f (a(x ′ )) ⊤ f (a(x)) -f (x) ⊤ f (x ′ ) 2 (10)", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "Proposition 3. Suppose we have a finite training set X = {x 1 , . . . , x n }. For an augmentation a ∈ A, suppose that", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "EQUATION", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": ")", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "and that the matrix A = [f (x 1 ), . . . , f (x n )] ⊤ satisfies the conditions of Proposition 6. Let A ‡ denote the pseudoinverse of A. Then there is an", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "EQUATION", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": ")", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "and hence for each", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "EQUATION", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": ")", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "Proof. The first inequality is a direct application of Proposition 6, where A = [f (x 1 ), . . . , f (x n )] ⊤ and B = [f (a(x 1 )), . . . , f (a(x n ))] ⊤ . The second inequality follows from the fact that ∥x∥ ∞ ≤ ∥x∥ 2 for vectors x ∈ R n .", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "Corollary 2. If L equi (f, a) ≤ ϵ 4 for all a ∈ A and A = [f (x 1 ), . . . , f (x n )] ⊤ satisfies the conditions in Proposition 6 then ρ :", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "A → O(d) given by ρ(a) = R a satisfies ∥ρ(a ′ • a) -ρ(a ′ )ρ(a)∥ ≤ o(ϵ 2 )", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "for any a, a ′ ∈ A.", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "Proof. Applying Proposition 3 on a ′ • a and using the triangle inequality, we have", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "∥R a ′ •a f (x) -R a ′ R a f (x)∥ ≤ ∥R a ′ •a f (x) -f (a ′ • a(x)) + f (a ′ • a(x)) -R a ′ R a f (x)∥ ≤ ∥R a ′ •a f (x) -f (a ′ • a(x))∥ + ∥f (a ′ • a(x)) -R a ′ R a f (x)∥ ≤ ∥f (a ′ • a(x)) -R a ′ R a f (x)∥ + (1 + √ 2) A ‡ ϵ 2", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "Now, using triangle inequality again, we have", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "∥R a ′ •a f (x) -f (a ′ • a(x))∥ = ∥f (a ′ • a(x)) -R a ′ f (a(x)) + R a ′ f (a(x)) -R a ′ R a f (x)∥ ≤ ∥f (a ′ • a(x)) -R a ′ f (a(x))∥ + ∥R a ′ f (a(x)) -R a ′ R a f (x)∥", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "Taking x = a(x), using the submultiplicativity property of matrix norms and applying Proposition 3 twice we have,", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "∥f (a ′ • a(x)) -R a ′ f (a(x))∥ + ∥R a ′ f (a(x)) -R a ′ R a f (x)∥ = ∥f (a ′ (x)) -R a ′ f (x)∥ + ∥R a ′ (f (a(x)) -R a f (x))∥ ≤ ∥f (a ′ (x)) -R a ′ f (x)∥ + ∥R a ′ ∥ ∥(f (a(x)) -R a f (x))∥ ≤ (1 + √ 2) Ā ‡ ϵ 2 + (1 + √ 2) A ‡ ϵ 2", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "Hence,", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "∥R a ′ •a f (x) -R a ′ R a f (x)∥ ≤ (1 + √ 2) Ā ‡ ϵ 2 + (2 + 2 √ 2) A ‡ ϵ 2 where A = [f (x 1 ), . . . , f (x n )] ⊤ and Ā = [f (a(x 1 )), . . . , f (a(x n ))] ⊤", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "By our assumption, we can choose a basis f", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "(x i1 ), f (x i2 ), ...f (x i d ) of R d , where i 1 , i 2 , ...i n ∈ [n].", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "We can then write any unit norm x in terms of this basis:", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "x = d j=1 c j f (x ij ). Letting B = [f (x i1 ), . . . , f (x i d )] ⊤ , we have B -1 x = c", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": ". Thus, we can bound the 1-norm of c as", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "EQUATION", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "Then using the triangle inequality, we have", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "∥R a ′ •a x -R a ′ R a x∥ = n j=1 c j R a ′ •a f (x ij ) -R a ′ R a f (x ij ) ≤ n j=1 |c j | R a ′ •a f (x ij ) -R a ′ R a f (x ij ) ≤ ∥c∥ 1 (1 + √ 2) A ‡ ϵ 2 ≤ √ d(1 + √ 2) A ‡ B -1 ϵ 2 .", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "Taking the supremum over all unit norm x finishes the proof.", "section": "B.1 APPROXIMATE EQUIVARIANCE FOR <PERSON><PERSON><PERSON><PERSON>O LOSS", "sec_num": null}, {"text": "This section recalls some classical theory on orthogonal groups and an extension that we use for proving results over continuous data distributions.", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "A function f : (R d ) n → R is said to be O(d)-invariant if f (Rv 1 , . . . , Rv n ) = f (v 1 , . . . , v n ) for all R ∈ O(d).", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "Throughout this section, we are especially interested in determining easily computed statistics that characterize an O(d) invariant function f . In other words, we would like to write f as a function of these statistics. The following theorem was first proved by <PERSON> using <PERSON>'s identity (<PERSON>, 1946) and shows that the inner products v ⊤ i v j suffice. Theorem 4 (First fundamental theorem of invariant theory for the orthogonal group). Suppose that", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "f : (R d ) n → R is O(d)-invariant.", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "Then there exists a function g : R n×n → R for which", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "f (v 1 , . . . , v n ) = g [v ⊤ i v j ] n i,j=1 .", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "In other words, to compute f at a given input, it is not necessary to know all of v 1 , . . . , v n . Computing the value of f at a point can be done using only the inner products v ⊤ i v j , which are invariant to O(d). Letting V be the n × d matrix whose ith row is v ⊤ i , we may also write", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "f (v 1 , . . . , n n ) = g(V V ⊤ ). The map V → V V ⊤ is known as the orthogonal projection of V .", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "A corollary of this result has recently been used to develop O(d) equivariant architectures in machine learning (<PERSON><PERSON> et al., 2021) . Corollary 3. Suppose that A, B are n × d matrices and AA ⊤ = BB ⊤ . Then A = BR for some R ∈ O(d). <PERSON><PERSON> et al. (2021) use this characterization of orthogonally equivariant functions to parameterize function classes of neural networks that have the same equivariance. This result is also useful in our context; However, we put it to use for a very different purpose: studying L equi .", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "Intuitively this result says the following: given two point clouds A, B of unit length vectors with some fixed correspondence (bijection) between each point in A and a point in B, if the angles between the ith and jth points in cloud A always equal the angle between the ith and jth point in cloud B, then A and B are the same up to an orthogonal transformation. This is the main tool we use to prove the finite sample version of the main result for our equivariant loss (Proposition 2). However, to analyze the population sample loss L equi (Proposition 1), we require an extended version of this result to the continuous limit as n → ∞. To this end, we develop a simple but novel extension to Theorem 4 to the case of continuous data distributions. This result may be useful in other contexts independent of our setting. Proposition 5. Let X be any set and f, h :", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "X → R d be functions on X . If f (x) ⊤ f (y) = h(x) ⊤ h(y) for all x, y ∈ X , then there exists R ∈ O(d) such that Rf (x) = h(x) for all x ∈ X .", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "The proof of this result directly builds on the finite sample version. The key idea of the proof is that since the embedding space R d is finite-dimensional we may select a set of points {f (x i )} i whose span has maximal rank in the linear space spanned by the outputs of f . This means that any arbitrary point f (x) can be written as a linear combination of the f (x i ). This observation allows us to apply the finite sample result on each f (x i ) term in the sum to conclude that f (x) is also a rotation of a sum of h(x i ) terms. Next, we give the formal proof.", "section": "C <PERSON><PERSON><PERSON><PERSON>OUND ON INVARIANCE THEORY FOR THE ORTHOGONAL GROUP", "sec_num": null}, {"text": "5. Choose x 1 , . . . , x n ∈ X such that F = [f (x 1 ) | . . . | f (x n )] ⊤ ∈ R n×d and h = [h(x 1 ) | . . . | h(x n )] ⊤ ∈ R n×d", "section": "Proof of Proposition", "sec_num": null}, {"text": "have maximal rank. Note we use \"|\" to denote the column-wise concatenation of vectors. Note that such x i can always be chosen. Since we have F F ⊤ = HH ⊤ , we know by Corollary 3 that F = HR for some R ∈ O(d).", "section": "Proof of Proposition", "sec_num": null}, {"text": "F = [F | f (x)] ⊤ and H = [H | h(x)] ⊤ ,", "section": "Now consider an arbitrary x ∈ X and define", "sec_num": null}, {"text": "both of which belong to R (n+1)×d . Note that again we have F F ⊤ = H H⊤ so also know that F = H R for some R ∈ O(d). Since x i were chosen so that F and H are of maximal rank, we know that h(x) = n i=1 c i h(x i ) for some coefficients c i ∈ R, since if this were not the case then we would have rank( H) = rank(H) + 1.", "section": "Now consider an arbitrary x ∈ X and define", "sec_num": null}, {"text": "From this, we know that", "section": "Now consider an arbitrary x ∈ X and define", "sec_num": null}, {"text": "R ⊤ h(x) = n i=1 c i R ⊤ h(x i ) = n i=1 c i f (x i ) = n i=1 c i R⊤ h(x i ) = R⊤ n i=1 c i h(x i ) = R⊤ h(x) = f (x).", "section": "Now consider an arbitrary x ∈ X and define", "sec_num": null}, {"text": "So we have that Rf (x) = RR ⊤ h(x) = h(x) for all x ∈ X .", "section": "Now consider an arbitrary x ∈ X and define", "sec_num": null}, {"text": "Here, we consider the setting when our equivariance loss is not exactly minimized. This corresponds to when the pairwise dot products between representations do not exactly match. We ", "section": "C.1 APPROXIMATE EQUIVARIANCE RESULTS", "sec_num": null}, {"text": "EQUATION", "section": "C.1 APPROXIMATE EQUIVARIANCE RESULTS", "sec_num": null}, {"text": ")", "section": "C.1 APPROXIMATE EQUIVARIANCE RESULTS", "sec_num": null}, {"text": "where A ‡ is the pseudoinverse of A, ∥•∥ F is the Frobenius norm, and ∥•∥ is the maximum singular value norm.", "section": "C.1 APPROXIMATE EQUIVARIANCE RESULTS", "sec_num": null}, {"text": "In other words, for a fixed and full rank matrix A, we have that min R∈O(d) ∥A -BR∥ F = o(ϵ), where o(ϵ) → 0 as the error ϵ → 0.", "section": "C.1 APPROXIMATE EQUIVARIANCE RESULTS", "sec_num": null}, {"text": "In Section 3.2, we explore the possibility of formulating an equivariant loss L equi for pairs of points that fully captures equivariance by requiring the group to be the stabilizer of a bilinear form. In this context, the invariants are generated by polynomials of degree two in two variables, and the equivariant functions can be obtained by computing gradients of these invariants (<PERSON><PERSON><PERSON> & <PERSON>, 2022) . Section 3.2 notes that this holds true not only for the orthogonal group, which is the primary focus of our research but also for the <PERSON><PERSON>z group and the symplectic group, suggesting natural extensions of our approach.", "section": "D EXTENSIONS TO OTHER GROUPS: FURTHER DISCUSSION", "sec_num": null}, {"text": "It is worth noting that the group of rotations SO(d) does not fall into this framework. It can be defined as the set of transformations that preserve both inner products (a 2-form) and determinants (a d-form). Consequently, some of its generators have degree 2 while others have degree d (see (<PERSON><PERSON>, 1946) , Section II.A.9).", "section": "D EXTENSIONS TO OTHER GROUPS: FURTHER DISCUSSION", "sec_num": null}, {"text": "<PERSON><PERSON>'s theorem states that if a group acts on n copies of a vector space (in our case, (R d ) n for consistency with the rest of the paper), its action can be characterized by examining how it acts on k copies (i.e., (R d ) k ) when the maximum degree of its irreducible components is k (refer to Section 6 of (<PERSON><PERSON><PERSON>, 2006) for a precise statement of the theorem). Since our interest lies in understanding equivariance in terms of pairs of objects, we desire invariants that act on pairs of points. One way to guarantee this is to restrict ourselves to groups that act through representations where the irreducible components have degrees of at most two (though this is not necessary in all cases, such as the orthogonal group O(d) that we consider in the main paper). An example of such groups is the product of finite subgroups of the unitary group U (2), which holds relevance in particle physics. According to <PERSON><PERSON>'s theorem, the corresponding invariants can be expressed as polarizations of degree-2 polynomials on two variables. Polarizations represent an algebraic construction that enables the expression of homogeneous polynomials in multiple variables by introducing additional variables to polynomials with fewer variables. In our case, the base polynomials consist of degree-2 polynomials in two variables, while the polarizations incorporate additional variables. Notably, an interesting open problem lies in leveraging this formulation for contrastive learning.", "section": "D EXTENSIONS TO OTHER GROUPS: FURTHER DISCUSSION", "sec_num": null}, {"text": "Algorithm 1 presents pytorch-based pseudocode for implementing CARE. This implementation introduces the idea of using a smaller batch size for the equivariance loss compared to the InfoNCE loss. Specifically, by definition, the equivariance loss is defined as a double expectation, one over data pairs and the other over augmentations. Empirical observations reveal that sampling one augmentation per batch leads to unstable yet superior performance when compared to standard invariant-based baselines such as SimCLR. Since these invariant-based contrastive benchmarks generally perform well with large batch sizes, we adopt the approach of splitting a batch into multiple chunks to efficiently sample multiple augmentations per batch for the equivariance loss. Each chunk of the batch is associated with a new pair of augmentations, ensuring a large batch size for the InfoNCE loss and a smaller batch size for the equivariance loss.", "section": "E IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "Algorithm 1 PyTorch based pseudocode for CARE ", "section": "E IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "(f ) = infonce_loss(z inv 1 , z inv 2 ) 16: Lequiv(f ) = orthogonal_equivariance_loss(z equiv 1 , z equiv 2 , n_split) 17: LCARE(f ) = LInfoNCE(f ) + λ • Lequiv(f ) 18: /*", "section": "E IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "We first outline the training protocol adopted for training our proposed approach on a variety of datasets, namely CIFAR10, CIFAR100, STL10, and ImageNet100.", "section": "F.3 EXPERIMENTAL PROTOCOLS", "sec_num": null}, {"text": "CIFAR10, CIFAR100 and STL10 All encoders have ResNet-50 backbones and are trained for 400 epochs with temperature τ = 0.5 for SimCLR and τ = 0.1 for MoCo-v2 * . The encoded features have a dimension of 2048 and are further processed by a two-layer MLP projection head, producing an output dimension of 128. A batch size of 256 was used for all datasets. For CIFAR10 and CIFAR100, we employed the Adam optimizer with a learning rate of 1e -3 and weight decay of 1e -6 . For STL10, we employed the SGD optimizer with a learning rate of 0.06, utilizing cosine annealing and a weight decay of 5e -4 , with 10 warmup steps. We use the same set of augmentations as in SimCLR (<PERSON> et al., 2020) . To train the encoder using L CARE-SimCLR , we use the same hyper-parameters for InfoNCE loss. Additionally, we use 4, 8 and 16 batch splits for CIFAR100, STL10 and CIFAR10, respectively. This allows us to sample multiple augmentations per batch, effectively reducing the batch size of equivariance loss whilst retaining the same for InfoNCE loss. Furthermore, for the equivariant term, we find it optimal to use a weight of λ = 0.01, 0.001, and 0.01 for CIFAR10, CIFAR100, and STL10, respectively.", "section": "F.3 EXPERIMENTAL PROTOCOLS", "sec_num": null}, {"text": "ImageNet100 We use ResNet-50 as the encoder architecture and pretrain the model for 200 epochs.", "section": "F.3 EXPERIMENTAL PROTOCOLS", "sec_num": null}, {"text": "A base learning rate of 0.8 is used in combination with cosine annealing scheduling and a batch size of 512. For MoCo-v2, we use 0.99 as the momentum and τ = 0.2 as the temperature. All remaining hyperparameters were maintained at their respective official defaults as in the official MoCo-v2 code.", "section": "F.3 EXPERIMENTAL PROTOCOLS", "sec_num": null}, {"text": "While training with L CARE-SimCLR and L CARE-MoCo , we find it optimal to use splits of 4 and 8 and weight of λ = 0.005 and 0.01 respectively on the equivariant term.", "section": "F.3 EXPERIMENTAL PROTOCOLS", "sec_num": null}, {"text": "We train a linear classifier on frozen features for 100 epochs with a batch size of 512 for CIFAR10, CIFAR100, and STL10 datasets. To optimize the classifier, we employ the Adam optimizer with a learning rate of 1e -3 and a weight decay of 1e -6 . In the case of ImageNet100, we train the linear classifier for 60 epochs using a batch size of 128. We initialize the learning rate to 30.0 and apply a step scheduler with an annealing rate of 0.1 at epochs 30, 40, and 50. The remaining hyper-parameters are retained from the official code.", "section": "Linear evaluation", "sec_num": null}, {"text": "G ADDITIONAL EXPERIMENTS G.1 ABLATING LOSS TERMS G.2 HISTOGRAM FOR LOSS ABLATION.", "section": "Linear evaluation", "sec_num": null}, {"text": "To accompany Figure 10 , this section plots the cosine similarity between positive pairs. We provide two plots for each experiment: the first plots the histogram of similarities of positive pairs drawn from the test set; the second plots the average positive cosine similarity throughout training. The results are reported in Figures 11, 12, 13, 14, 15, 16 .", "section": "Linear evaluation", "sec_num": null}, {"text": "In the protein cloud experiment in Section 5.1, we use the Protein Data Bank (PDB)-the single global repository of experimentally determined 3D structures of biological macromolecules and their complexes, containing approximately 130,000 samples. The core objective is to assess whether our method can effectively encode the SO(3) manifold, a highly challenging and critical aspect in drug discovery. Each input point cloud is transformed through action of the SO(3) group. Consequently, the desirect 2D trajectory is a circle corresponding to rotation along each of three orthogonal axes. This is consistent with the structure of the SO(3) manifold. safety and alignment considerations. However, beyond this, CARE, offers insights into algorithmic approaches for controlling and moderating model behavior. Specifically, CARE identifies a simple rotation of embedding space that corresponds to a change in the attribute of the data. In principle, this transformation could be used to \"canonicalize\" data, preventing the model from relying on certain attributes in decision-making. Additionally, controlled transformations of embeddings could be used to debias model responses and achieve desired variations in output. It is important to note that while our focus is on the core methodology, we do not explore these possibilities in this particular work.", "section": "G.3 ADDITIONAL PROTEIN TRAJECTORIES", "sec_num": null}, {"text": "* https://github.com/facebookresearch/moco", "section": "", "sec_num": null}], "back_matter": [{"text": "This research was supported by NSF award CCF-2112665. <PERSON> is supported by National Science Foundation Graduate Research Fellowship. <PERSON><PERSON><PERSON>r is partially funded by the NSF-Simons Research Collaboration on the Mathematical and Scientific Foundations of Deep Learning (MoDL) (NSF DMS 2031985), NSF CISE 2212457, ONR N00014-22-1-2126 and an Amazon AI2AI Faculty Research Award.We acknowledge MIT SuperCloud and Lincoln Laboratory Supercomputing Center for providing HPC resources that have contributed to this work. We wish to thank <PERSON> for insightful discussions on extensions of our method to biology.", "section": "ACKNOWLEDGEMENTS", "sec_num": "7"}, {"text": "nearest neighbors with significantly different shades (e.g., red and orange) compared to those learned by CARE, which are closer in color to the query images. ", "section": "annex", "sec_num": null}, {"text": "Limitations. While our method, CARE, learns embedding spaces with many advantages over prior contrastive learning embedding spaces, there are certain limitations that we acknowledge here. First, we do not provide a means to directly identify the rotation corresponding to a specific transformation. Instead, our approach allows the recovery of the rotation by solving <PERSON><PERSON><PERSON>'s problem. However, this requires solving an instance of <PERSON><PERSON><PERSON>'s for each augmentation of interest. Future improvements that develop techniques for quickly and easily (i.e., without needing to solve an optimization problem) identifying specific rotations would be a valuable improvement, enhancing the steerability of our models. Second, it is worth noting that equivariant contrastive methods, including CARE, only achieve approximate equivariance. This is a fundamental challenge shared by all such methods, as it is unclear how to precisely encode exact equivariance. The question remains open as to a) whether this approximate equivariance should be considered damaging in the first place, and if so, b) whether scaling techniques can sufficiently produce reliable approximate equivariance to enable the diverse applications that equivariance promises. Addressing this challenge is a crucial area for future research and exploration in the field. Each of these limitations points to valuable directions for future work.Broader impact. Through our self-supervised learning method CARE we explore foundational questions regarding the structure and nature of neural network representation spaces. Currently, our approaches are exploratory and not ready for integration into deployed systems. However, this line of work studies self-supervised learning and therefore has the potential to scale and eventually contribute to systems that do interact with humans. In such cases, it is crucial to consider the usual", "section": "H ADDITIONAL DISCUSSION", "sec_num": null}], "ref_entries": {"FIGREF1": {"fig_num": "2", "uris": null, "num": null, "text": "Figure 2: When L equi = 0, compositions of augmentations correspond to compositions of rotations.", "type_str": "figure"}, "FIGREF2": {"fig_num": "3", "uris": null, "num": null, "text": "Figure 3: Trajectories through embedding space of three randomly sampled protein point clouds, rotated from 0 to 2π in three orthogonal axes. Rows correspond to different training methods.", "type_str": "figure"}, "FIGREF3": {"fig_num": "4", "uris": null, "num": null, "text": "Figure 4: CARE achieves the lowest error on the task of predicting the first principal component of a protein", "type_str": "figure"}, "FIGREF4": {"fig_num": "5", "uris": null, "num": null, "text": "Figure 5: Measuring equivariance using <PERSON><PERSON><PERSON>'s problem. Lower score is more equivariant.", "type_str": "figure"}, "FIGREF5": {"fig_num": "67", "uris": null, "num": null, "text": "Figure 6: Histogram of cosine angles between data pairs. CARE has much lower variance.", "type_str": "figure"}, "FIGREF6": {"fig_num": "8", "uris": null, "num": null, "text": "Figure 8: Relative rotational equivariance (Lower is more equivariant). Both CARE and invariancebased contrastive methods (e.g., SimCLR) produce approximately invariant embeddings. However, CARE learns a considerably more rotationally structured embedding space.", "type_str": "figure"}, "FIGREF7": {"fig_num": "9", "uris": null, "num": null, "text": "Figure9: CARE exhibits sensitivity to features that invariance-based contrastive methods (e.g., SimCLR) do not. For each query input, we retrieve top 5 nearest neighbors in the embedding space.", "type_str": "figure"}, "FIGREF8": {"fig_num": null, "uris": null, "num": null, "text": "use a generalization of Corollary 3 to this case (<PERSON><PERSON><PERSON><PERSON> et al., 2020) Instead of the dot products AA ⊤ and BB ⊤ exactly matching, they match only up to some error. As a result, we can guarantee A is close to BR for some orthogonal matrix R ∈ O(d) up to some error. Proposition 6. (<PERSON><PERSON><PERSON><PERSON> et al., 2020) Let A, B ∈ R n×d with n ≥ d, where A is full rank. Suppose AA ⊤ -BB ⊤ F ≤ ϵ 2 and A ‡ ϵ ≤ 1 Then there exists an orthogonal R ∈ O(d) such that ∥A -", "type_str": "figure"}, "FIGREF9": {"fig_num": "111213141516", "uris": null, "num": null, "text": "Figure 11: (left) Histogram of positive cosine similarity values at the end of pre-training using the invariance loss; (right) Evolution of positive cosine similarity values over pre-training epochs using the invariance loss", "type_str": "figure"}, "TABREF0": {"html": null, "num": null, "text": "Top-1 and Top-5 linear probe accuracy (%) on CIFAR10, CIFAR100, STL10 and Ima-geNet100 datasets. We report the mean performance from 3 different random initializations for the linear classifer. * denote numbers from Devillers & Lefort (2023), and ** from<PERSON><PERSON><PERSON> et al. (2023)", "content": "<table><tr><td>Method</td><td>CIFAR10</td><td>CIFAR100</td><td>STL10</td><td>ImageNet100</td></tr><tr><td colspan=\"2\">Invariant prediction approaches</td><td/><td/><td/></tr><tr><td>SimCLR</td><td>90.98 ±0.10</td><td>66.77 ±0.34</td><td>84.19 ±0.13</td><td>72.79 ±0.08</td></tr><tr><td>MoCo-v2</td><td>91.95 ±0.05</td><td>69.88 ±0.23</td><td>-</td><td>73.50 ±0.19</td></tr><tr><td>BYOL</td><td>90.44*</td><td>67.41**</td><td>-</td><td>-</td></tr><tr><td><PERSON></td><td>84.54 ±0.02</td><td>55.54 ±0.05</td><td>90.62 ±0.02</td><td/></tr><tr><td colspan=\"2\">Equivariant prediction approaches</td><td/><td/><td/></tr><tr><td colspan=\"2\">EquiMod SimCLR 91.28</td><td>67.59</td><td>83.67</td><td>-</td></tr><tr><td>EquiMod BYOL</td><td>91.57*</td><td>-</td><td>-</td><td>-</td></tr><tr><td>CARE SimCLR</td><td colspan=\"4\">91.92 ±0.12 (↑ 0.94) 68.05 ±0.28 (↑ 1.28) 84.64 ±0.29 (↑ 0.45) 76.69 ±0.08 (↑ 3.90)</td></tr><tr><td>CARE MoCo-v2</td><td colspan=\"3\">92.19 ±0.01 (↑ 0.24) 70.56 ±0.15 (↑ 0.68) 88.97 ±0.48</td><td>74.30 ±0.07 (↑ 0.80)</td></tr><tr><td colspan=\"5\">CARE Barlow Twins 85.65 ±0.05 (↑ 1.11) 56.76 ±0.02 (↑ 1.22) 90.92 ±0.01 (↑ 0.30) -</td></tr></table>", "type_str": "table"}, "TABREF2": {"html": null, "num": null, "text": "All experiments were performed on an HPC computing cluster using 4 NVIDIA Tesla V100 GPUs with 32GB accelerator RAM for a single training run. The CPUs used were Intel Xeon Gold 6248 processors with 40 cores and 384GB RAM. All experiments use the PyTorch deep learning framework(<PERSON><PERSON><PERSON> et al., 2019).", "content": "<table><tr><td/><td>Optimization step */</td></tr><tr><td>19:</td><td>LCARE(f ).backward()</td></tr><tr><td>20:</td><td>optimizer.step()</td></tr><tr><td colspan=\"2\">F SUPPLEMENTARY EXPERIMENTAL DETAILS AND ASSETS DISCLOSURE</td></tr><tr><td colspan=\"2\">F.1 ASSETS</td></tr><tr><td colspan=\"2\">We do not introduce new data in the course of this work. Instead, we use publicly available widely</td></tr><tr><td colspan=\"2\">used image datasets for the purposes of benchmarking and comparison.</td></tr><tr><td colspan=\"2\">F.2 HARDWARE AND SETUP</td></tr></table>", "type_str": "table"}, "TABREF3": {"html": null, "num": null, "text": "Figures 17, 18, 19 and 20 illustrate   Figure10: Ablating different loss terms. Combining L equi with a uniformity promoting non-collapse term suffices to learn non-trivial features. However, optimal performance is achieved when encouraging smaller rotations, as in CARE. ResNet-50 models pretrained on CIFAR10 and evaluated with linear probes.", "content": "<table><tr><td/><td/><td/><td/><td/><td/><td/><td/><td>90.98</td><td>91.93</td></tr><tr><td/><td/><td>80</td><td/><td/><td/><td/><td/></tr><tr><td/><td>Top-1 Accuracy</td><td>20 40 60</td><td>10.0</td><td>10.0</td><td/><td>20.46</td><td/><td>22.94</td><td>46.88</td></tr><tr><td/><td/><td>0</td><td>Invariance (I)</td><td>Equiv. (E)</td><td colspan=\"2\">Uniform (U)</td><td colspan=\"2\">Random Init.</td><td>Uniform + Equiv.</td><td>SimCLR (I+U)</td><td>(I+E+U) CARE</td></tr><tr><td/><td>200</td><td/><td/><td/><td/><td/><td/><td>1.000</td></tr><tr><td>Frequency</td><td>0 50 100 150</td><td/><td colspan=\"3\">0.996 Positive cosine similarity 0.998</td><td colspan=\"2\">1.000</td><td>Positive cosine similarity</td><td>0.995 0.996 0.997 0.998 0.999</td><td>0</td><td>50 Pretraining epochs 100 150 Invariance Loss (I) 200</td></tr></table>", "type_str": "table"}}}}