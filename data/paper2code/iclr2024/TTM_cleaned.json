{"paper_id": "TTM", "title": "KNOWLEDGE DISTILLATION BASED ON TRANS-FORMED TEACHER MATCHING", "abstract": "As a technique to bridge logit matching and probability distribution matching, temperature scaling plays a pivotal role in knowledge distillation (KD). Conventionally, temperature scaling is applied to both teacher's logits and student's logits in KD. Motivated by some recent works, in this paper, we drop instead temperature scaling on the student side, and systematically study the resulting variant of KD, dubbed transformed teacher matching (TTM). By reinterpreting temperature scaling as a power transform of probability distribution, we show that in comparison with the original KD, TTM has an inherent Rényi entropy term in its objective function, which serves as an extra regularization term. Extensive experiment results demonstrate that thanks to this inherent regularization, TTM leads to trained students with better generalization than the original KD. To further enhance student's capability to match teacher's power transformed probability distribution, we introduce a sample-adaptive weighting coefficient into TTM, yielding a novel distillation approach dubbed weighted TTM (WTTM). It is shown, by comprehensive experiments, that although WTTM is simple, it is effective, improves upon TTM, and achieves state-of-the-art accuracy performance. Our source code is available at https://github.com/zkxufo/TTM.", "pdf_parse": {"paper_id": "TTM", "abstract": [{"text": "As a technique to bridge logit matching and probability distribution matching, temperature scaling plays a pivotal role in knowledge distillation (KD). Conventionally, temperature scaling is applied to both teacher's logits and student's logits in KD. Motivated by some recent works, in this paper, we drop instead temperature scaling on the student side, and systematically study the resulting variant of KD, dubbed transformed teacher matching (TTM). By reinterpreting temperature scaling as a power transform of probability distribution, we show that in comparison with the original KD, TTM has an inherent Rényi entropy term in its objective function, which serves as an extra regularization term. Extensive experiment results demonstrate that thanks to this inherent regularization, TTM leads to trained students with better generalization than the original KD. To further enhance student's capability to match teacher's power transformed probability distribution, we introduce a sample-adaptive weighting coefficient into TTM, yielding a novel distillation approach dubbed weighted TTM (WTTM). It is shown, by comprehensive experiments, that although WTTM is simple, it is effective, improves upon TTM, and achieves state-of-the-art accuracy performance. Our source code is available at https://github.com/zkxufo/TTM.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Knowledge distillation (KD) has achieved a great success and drawn a lot of attention ever since it was proposed. The original form of KD was proposed by <PERSON><PERSON><PERSON><PERSON><PERSON> et al. (2006) , where a small model (student) was trained to match the logits of a large model (teacher). Later, a generalized version now known as KD was proposed by <PERSON><PERSON> et al. (2015) , where the small student model was trained to match the class probability distribution of the large teacher model. Compared to the student model trained with standard empirical risk minimization (ERM), the student model trained via KD has better performance in terms of accuracy, to the extent that this light-weight KD-trained student model is able to take the place of some larger and more complex models with little performance degradation, achieving the goal of model compression.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In the literature, KD is generally formulated as minimizing the following loss", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "EQUATION", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "where L CE = H(y, q) is the cross entropy loss between the one-hot probability distribution corresponding to label y and the student output probability distribution q, which is the canonical loss of ERM, D(p t T ||q T ) is the Kullback-Leibler divergence between the temperature scaled output probability distribution p t T of the teacher and the temperature scaled output probability distribution q T of the student, T is the temperature of distillation, and λ is a balancing weight. Note that p t T = σ(v/T ) and q T = σ(z/T ), given logits v of the teacher and logits z of the student, where σ denotes the softmax function.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "The use of the temperature T above is a pivotal characteristic of KD. On one hand, it provides a way to build a bridge between class probability distribution matching and logits matching. Indeed, it was shown in <PERSON><PERSON> et al. (2015) that as T goes to ∞, KD is equivalent to its logits-matching predecessor. On the other hand, it also distinguishes KD from the logits-matching approach, since in practice, empirically optimal values of the temperature T are often quite modest. Beyond these, there is little understanding about the role of the temperature T and in general why KD in its formulation (1) helps the student learns better. In particular, the following questions naturally arise: Q1 Why does the temperature T have to be applied to both the teacher and student? Q2 Would it be better off to apply the temperature T to the teacher only, but not to the student? So far, answers to the above questions remain elusive at the best.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "The purpose of this paper is to address the above questions. First, we demonstrate both theoretically and experimentally that the answer to the question Q2 above is affirmative, and it is better off to drop the temperature T entirely on the student side-the resulting variant of KD is referred to as transformed teacher matching (TTM) and formulated as minimizing the following objective:", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "LT T M = H(y, q) + βD(p t T ||q) (2)", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "where β is a balancing weight. Specifically, we show that (1) temperature scaling of logits is equivalent to a power transform of probability distribution, and (2) in comparison with KD, TTM has an inherent Rényi entropy term in its objective function (2). It is this inherent Rényi entropy that serves as an extra regularization term and hence improves upon KD. This theoretic analysis is further confirmed by extensive experiment results. It is shown by extensive experiments that thanks to this inherent regularization, TTM leads to trained students with better generalization than KD. Second, to further enhance student's capability to match teacher's power transformed probability distribution, we introduce a sample-adaptive weighting coefficient into TTM, yielding a novel distillation approach dubbed weighted TTM (WTTM). WTTM is simple and has almost the same computational complexity as KD. And yet it is very effective; it is shown, by comprehensive experiments, that it is significantly better than KD in terms of accuracy, improves upon TTM, and achieves state-of-the-art accuracy performance. For example, WTTM can reach 72.19% classification accuracy on ImageNet for ResNet-18 distilled from ResNet-34, outperforming most highly complex feature-based distillation methods.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "With the temperature T dropped entirely on the student side, TTM and WTTM, along with the statistical perspective of KD (<PERSON> et al., 2021) and the newly established upper bound on error rate in term of the cross entropy H(p * x , q) between the true, but often unknown conditional probability distribution p *", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "x of label y given an input sample x and the output probability distribution q of a model in response to the input x, <PERSON> et al. (2023a) offer a new explanation of why KD helps. First, the purpose of the teacher in KD is to provide a proper estimate for the unknown true conditional probability distribution p *", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "x , which is a linear combination of the one-hot vector corresponding to the label y and the power transformed teacher's probability distribution p t T . Second, the role of the temperature T on the teacher side is to improve this estimate. Third, replacing p *", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "x by its estimate from the transformed teacher, the learning process in KD is to simply minimize the cross entropy upper bound on error rate, which improves upon the standard deep learning process where p *", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "x in the cross entropy upper bound is rudimentarily approximated by the one-hot vector corresponding to the label y.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In a multi-class classification setting, an output of a neural network in response to an input sample is a probability vector or distribution q with K entries, where K is the number of all possible classes, and the class with the highest probability is the prediction made by the neural network for this particular sample. Conventionally, a prediction is said to be confident if the corresponding q concentrates most of its probability mass on the predicted class. <PERSON><PERSON><PERSON><PERSON> et al. (2016) points out that if a model is too confident about its predictions, then it tends to suffer from overfitting. To avoid overfitting and improve generalization, <PERSON><PERSON><PERSON> et al. (2017) proposed to penalize confident predictions. Since a confident prediction generally corresponds to q with low entropy, they enforced confidence penalty (CP) by introducing a negative entropy regularizer into the objective function of the learning process, which is formulated as", "section": "CONFIDENCE PENALTY", "sec_num": "2.1"}, {"text": "LCP = H(y, q) -ηH(q) (3)", "section": "CONFIDENCE PENALTY", "sec_num": "2.1"}, {"text": "where η controls the strength of the confidence penalty. Thanks to the entropy regularization, the learned model is encouraged to output smoother distributions with larger entropy, leading to less confident predictions, and most importantly, better generalization.", "section": "CONFIDENCE PENALTY", "sec_num": "2.1"}, {"text": "<PERSON><PERSON>yi entropy (<PERSON><PERSON><PERSON>, 1961) is a generalized version of Shannon entropy, which has been successfully applied in many machine learning topics, such as differential privacy (<PERSON><PERSON><PERSON>, 2017) , understanding neural networks (<PERSON> et al., 2020) , and representation distillation (<PERSON> et al., 2021) . Given a discrete random variable X with alphabet A = {x 1 , x 2 , . . . , x n } and corresponding probabilities p i for i = 1, 2, . . . , n, its Rényi entropy is defined as", "section": "R ÉNYI ENTROPY", "sec_num": "2.2"}, {"text": "Hα(X) = 1 1 -α log n i=1 pi α (4)", "section": "R ÉNYI ENTROPY", "sec_num": "2.2"}, {"text": "where α is called the order of Rényi entropy. The limit of Rényi entropy when α → 1 is the well-known Shannon entropy.", "section": "R ÉNYI ENTROPY", "sec_num": "2.2"}, {"text": "In the literature, different perspectives have been developed to understand KD. One of them is the label smoothing (LS) perspective advocated by <PERSON> et al. (2020) and <PERSON> & <PERSON> (2020) .", "section": "LABEL SMOOTHING PERSPECTIVE TOWARDS KD", "sec_num": "2.3"}, {"text": "LS (<PERSON><PERSON><PERSON><PERSON> et al., 2016) is a technique to encourage a model to make less confident predictions by minimizing the following objective function in the learning process", "section": "LABEL SMOOTHING PERSPECTIVE TOWARDS KD", "sec_num": "2.3"}, {"text": "LLS = (1 -ϵ)H(y, q) + ϵH(u, q) (5)", "section": "LABEL SMOOTHING PERSPECTIVE TOWARDS KD", "sec_num": "2.3"}, {"text": "where u is a uniform distribution over all K possible classes, and ϵ controls the strength of the smoothing effect. The model trained with LS tends to have significantly less confident predictions and output probability distributions with larger Shannon entropy compared to its counterpart in the case of ERM (visualized in A.1).", "section": "LABEL SMOOTHING PERSPECTIVE TOWARDS KD", "sec_num": "2.3"}, {"text": "If we replace u with the teacher output p t in (5), then we have L LS = (1 -ϵ)H(y, q) + ϵH(p t , q), which is equivalent to L KD with T = 1, since the entropy H(p t ) does not depends on the student. Therefore, when T = 1, KD can indeed be regarded as sample-adaptive LS. However, when T > 1, such a perspective no longer holds since temperature scaling is also applied to the student model. This is confirmed by the empirical analysis shown in A.1. Although KD with T = 1 is able to increase the Shannon entropy of output probability distribution q compared to ERM, KD with T = 4 actually leads to decreased Shannon entropy compared to ERM, showing an opposite effect of LS.", "section": "LABEL SMOOTHING PERSPECTIVE TOWARDS KD", "sec_num": "2.3"}, {"text": "The sample-adaptive LS perspective was also advocated in self-distillation Zhang & Sabuncu (2020) , where the temperature T was dropped for convenience on the student side. However, no systematic treatment was provided to justify the drop-out of the temperature T for the student side. In fact, in terms of prediction accuracy, mixed results were demonstrated: dropping out the temperature T for the student can either decrease or increase the accuracy.", "section": "LABEL SMOOTHING PERSPECTIVE TOWARDS KD", "sec_num": "2.3"}, {"text": "Another perspective to understand KD is the statistical perspective advocated by <PERSON><PERSON> et al. (2021) .", "section": "STATISTICAL PERSPECTIVE AND CROSS ENTROPY UPPER BOUND", "sec_num": "2.4"}, {"text": "A key observation therein is that the Bayes-distilled risk has a smaller variance than the standard empirical risk, which is actually the direct consequence of the law of total probability for variance (Ross, 2019) . Since the Bayes class-probability distribution over the labels, i.e., the conditional probability distribution p * x = [P (i|x)] K i=1 of label y given an input sample x, is unknown in practice, the role of the teacher in KD was believed to use its output probability distribution p t or temperature scaled output probability distribution p t T to estimate p * x for the student. This, in turn, offers some explanation of why improving teacher accuracy can sometimes harm distillation performance, since improving teacher accuracy and providing better estimates for p *", "section": "STATISTICAL PERSPECTIVE AND CROSS ENTROPY UPPER BOUND", "sec_num": "2.4"}, {"text": "x are two different tasks. In this perspective, the temperature T is also dropped for the student. Again, no justification was provided for dropping T on the student side. In addition, the question of why minimizing the Bayes-distilled risk or teacher-distilled risk could improve the student's accuracy performance was not answered either.", "section": "STATISTICAL PERSPECTIVE AND CROSS ENTROPY UPPER BOUND", "sec_num": "2.4"}, {"text": "Recently, it was shown in <PERSON> et al. (2023a) that for any classification neural network, its error rate is upper bounded by E x [H(p *", "section": "STATISTICAL PERSPECTIVE AND CROSS ENTROPY UPPER BOUND", "sec_num": "2.4"}, {"text": "x , q)]. Thus, to reduce its error rate, the neural network can be trained by minimizing E x [H(p *", "section": "STATISTICAL PERSPECTIVE AND CROSS ENTROPY UPPER BOUND", "sec_num": "2.4"}, {"text": "x , q)]. Since the true conditional distribution p * x is generally unavailable in practice, KD with the temperature T dropped for the student can be essentially regarded as one way to solve approximately the problem of minimizing E x [H(p *", "section": "STATISTICAL PERSPECTIVE AND CROSS ENTROPY UPPER BOUND", "sec_num": "2.4"}, {"text": "x , q)], where p * x is first approximated by a linear combination of the one-hot probability distribution corresponding to label y and the temperature scaled output probability distribution p t T of the teacher. This perspective, when applied to KD, does provide justifications for dropping the temperature T entirely on the student side and also for minimizing the Bayes-distilled risk or teacher-distilled risk. Of course, KD with the temperature T dropped for the student may not be necessarily an effective way to minimize E x [H(p *", "section": "STATISTICAL PERSPECTIVE AND CROSS ENTROPY UPPER BOUND", "sec_num": "2.4"}, {"text": "x , q)]. Other recent related works are reviewed in Appendix A.7.", "section": "STATISTICAL PERSPECTIVE AND CROSS ENTROPY UPPER BOUND", "sec_num": "2.4"}, {"text": "In contrast, in this paper, we show more directly that it is better off to drop entirely the temperature T on the student side in KD by comparing TTM with KD both theoretically and experimentally.", "section": "STATISTICAL PERSPECTIVE AND CROSS ENTROPY UPPER BOUND", "sec_num": "2.4"}, {"text": "In this section, we compare TTM with KD theoretically by showing that TTM is equivalent to KD plus Rényi entropy regularization. To this end, we first come up with a general concept of power transform of output distributions. Then, we show the equivalence between temperature scaling and power transform. Based on this, a simple derivation is provided to decompose TTM into KD plus a R<PERSON>yi entropy regularizer. In view of CP, it's clear that TTM can lead to better generalization than KD because of the penalty over confident output distributions.", "section": "TRANSFORMED TEACHER MATCHING", "sec_num": "3"}, {"text": "In KD, model output distributions are transformed by temperature scaling to improve their smoothness. However, such a transform is not unique. There are many other transforms which can smooth out peaked probability distributions as well. Below we will introduce a generalized transform.", "section": "POWER TRANSFORM OF PROBABILITY DISTRIBUTIONS", "sec_num": "3.1"}, {"text": "f : [0, 1] → [0, 1]. For any probability distribution p = [p 1 , . . . , p K ],", "section": "Consider a point-wise mapping", "sec_num": null}, {"text": "we can apply f to each component of p to define a generalized transform p → p, where p = [ p1 , . . . , pK ], and", "section": "Consider a point-wise mapping", "sec_num": null}, {"text": "EQUATION", "section": "Consider a point-wise mapping", "sec_num": null}, {"text": "In this above, K j=1 f (p j ) is used to normalize the vector [f (p i )] K i=1 back to a probability simplex. With this generalized framework, any specific transform can be described by its associated mapping f . Among all possible mappings f , the most interesting one to us is the power function with exponent γ. If f is selected to be the power function with exponent γ, the resulting probability distribution transform p → p is referred to as the power transform of probability distribution. Accordingly, the power transformed distribution is given by", "section": "Consider a point-wise mapping", "sec_num": null}, {"text": "EQUATION", "section": "Consider a point-wise mapping", "sec_num": null}, {"text": "Next, we will show that power transform is equivalent to temperature scaling. Indeed, suppose that p is the softmax of logits", "section": "Consider a point-wise mapping", "sec_num": null}, {"text": "EQUATION", "section": "Consider a point-wise mapping", "sec_num": null}, {"text": "EQUATION", "section": "Consider a point-wise mapping", "sec_num": null}, {"text": ")", "section": "Consider a point-wise mapping", "sec_num": null}, {"text": "Thus p is the softmax of the scaled logits [γl 1 , γl 2 , • • • , γl K ] with temperature T = 1/γ.", "section": "Consider a point-wise mapping", "sec_num": null}, {"text": "Based on the equivalence between power transform and temperature scaling, we can now reveal the connection between KD and TTM.", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "Let γ = 1/T . Go back to (1) and ( 2). In view of (9), we have p t T = pt and qT = q.", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "(10)", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "Then we can decompose D(p t T ||q T ) as follows:", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "EQUATION", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "where ( 11) follows the power transform ( 7), H γ (q) in ( 12) is the <PERSON><PERSON>yi entropy of q of order γ, and ( 14) is due to (10). Rearranging ( 14), we get", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "EQUATION", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "Plugging ( 15) into (2) yields", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "EQUATION", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": ")", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "whenever β is selected to be", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "EQUATION", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "where ( 16) is due to the fact that the Shannon entropy H(p t T ) does not depend on the student model, ( 17) follows (19), and ( 18) is attributable to (1).", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "Thus we have shown that TTM can indeed be decomposed into KD plus a Rényi entropy regularizer. Since Rényi entropy is a generalized version of Shannon entropy, it plays a role in TTM similar to that of Shannon entropy in CP. With this, we have reasons to believe that it can lead to better generalization, which is indeed confirmed later by extensive experiments in Section 5.", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "It is also instructive to compare TTM and KD from the perspective of their respective gradients. The gradients of the distillation component in L T T M with respect to the logits are:", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "∂D(p t T ||q) ∂zi = ∂H(p t T , q) ∂zi = qi -pt i = qi - p t i 1/T K j=1 p t j 1/T (20)", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "where z i and q i are the ith logit and ith class probability of the student model, respectively. In comparison, the corresponding gradients for KD are", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "EQUATION", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": ")", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "From Eq. ( 20), we see that the gradient descent learning process would push q i to move towards the power transformed teacher probability distribution, thus encouraging the student to behave like the power transformed teacher, from which the name TTM (transformed teacher matching) is coined. Since the power transformed teacher distribution p t T with T > 1 is smoother, the student trained by TTM will output a distribution q with similar smoothness, leading to low confidence and high entropy. On the other hand, in Eq. ( 21), it is the transformed student distribution q T that is pushed towards the transformed teacher distribution p t T . Even when q T has similar smoothness as p t T , the original student distribution q can still be quite peaked, thus having high confidence and low entropy.", "section": "FROM KD TO TTM", "sec_num": "3.2"}, {"text": "We can further improve TTM by introducing a sample-adaptive weighting coefficient into TTM. This is explored in this section.", "section": "SAMPLE-<PERSON><PERSON><PERSON><PERSON> MATCHING TO THE TRANSFORMED TEACHER", "sec_num": "4"}, {"text": "In TTM, the soft target we use is a linear combination of the one-hot probability distribution corresponding to y and the power transformed teacher distribution p t T , where the same coefficient β is applied to all samples. As discussed in Subsection 2.4, the role of the teacher in KD is to provide p t T and use it as an estimate for p * x . Assume this estimate is good. It is reasonable to believe that it would be better off to favor a soft target over an one-hot target even more for those samples for which p t T have more intrinsic confusion and is away from the one-hot probability distribution. After all, when p t T is close to the corresponding one-hot probability distribution, minimizing H(p t T , q) has little difference from minimizing H(y, q), and as a result, it's no longer meaningful to do distillation on these types of samples. This motivates us to discriminate among soft targets in TTM based on their smoothness. Concretely, a large β should be assigned to a smooth p t T , while a small β should be assigned to a peaked p t T . To implement the above idea, we need a quantity to quantify the smoothness of a soft target p t T . In view of ( 7) and the definition of Rényi entropy (4), the following power sum defined for any distribution p and any 0 < γ < 1", "section": "SAMPLE-<PERSON><PERSON><PERSON><PERSON> MATCHING TO THE TRANSFORMED TEACHER", "sec_num": "4"}, {"text": "Uγ(p) = k j=1", "section": "SAMPLE-<PERSON><PERSON><PERSON><PERSON> MATCHING TO THE TRANSFORMED TEACHER", "sec_num": "4"}, {"text": "p γ j comes handy. Given 0 < γ < 1, we can use the power sum U γ (p) to quantify the smoothness of p, since it is related to both the power transform and R<PERSON>yi entropy. It is clear that the power sum U γ (p) attains its minimum 1 when p is one-hot and maximum K 1-γ when p is uniform. Using U γ (p t ) to discriminate among different samples, we modify TTM to minimize the following objective function", "section": "SAMPLE-<PERSON><PERSON><PERSON><PERSON> MATCHING TO THE TRANSFORMED TEACHER", "sec_num": "4"}, {"text": "EQUATION", "section": "SAMPLE-<PERSON><PERSON><PERSON><PERSON> MATCHING TO THE TRANSFORMED TEACHER", "sec_num": "4"}, {"text": "The resulting variant of KD is referred to as weighted TTM (WTTM). Note that other sampleadaptive weights such as H(p t T ) may also be effective. Nonetheless, systematic study regarding how to select sample-adaptive weights and which one is optimal, is left for future work.", "section": "SAMPLE-<PERSON><PERSON><PERSON><PERSON> MATCHING TO THE TRANSFORMED TEACHER", "sec_num": "4"}, {"text": "Compared to TTM where the student is trained to match all soft targets uniformly, WTTM trains the student to match more closely to smooth soft targets and less closely to peaked soft targets. Thus, students resulting from WTTM would output smoother q than those distilled from TTM, which is further confirmed in the next section by experiments.", "section": "SAMPLE-<PERSON><PERSON><PERSON><PERSON> MATCHING TO THE TRANSFORMED TEACHER", "sec_num": "4"}, {"text": "We benchmark TTM and WTTM on two prevailing image classification datasets, namely CIFAR-100 and ImageNet (<PERSON><PERSON> et al., 2009) .", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "CIFAR-100 contains 60k 32×32 color images of 100 classes, with 600 images per class, and it's further split into 50k training images and 10k test images. For fair comparison, we adopt the same training strategy and teacher models as CRD (<PERSON><PERSON> et al., 2019) . Also, following CRD, we generate comprehensive experiment results for 13 teacher-student pairs including both same-architecture distillation and different-architecture distillation, and the tested model architectures are VGG (<PERSON> & Zisserman, 2014), ResNet (<PERSON> et al., 2016) , WideResNet (Zagoruyko & Komodakis, 2016b) , MobileNetV2 (<PERSON><PERSON> et al., 2018) , and ShuffleNet (<PERSON> et al., 2018; <PERSON> et al., 2018) .", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "ImageNet is a large-scale image dataset consisting of over 1.2 million training images and 50k validation images from 1000 classes. For experiments on ImageNet, we employ torchdistill (<PERSON><PERSON>bara, 2021) library and follow all the standard settings. The tested model architectures are ResNet and MobileNet (<PERSON> et al., 2017) .", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "Note that we list T and β values of all experiments in A.4 for reproducibility.", "section": "EXPERIMENTAL SETTINGS", "sec_num": "5.1"}, {"text": "Results on CIFAR-100. The pure performances of TTM and WTTM are shown in Table 1 and Table  3 . We compare them with feature-based methods FitNet (<PERSON> et al., 2014) , AT (Zagoruyko & Komodakis, 2016a) , VID (<PERSON><PERSON> et al., 2019) , RKD (<PERSON> et al., 2019) , PKT (Passalis & Tefas, 2018), CRD (<PERSON><PERSON> et al., 2019) , and logits-based methods such as KD, DIST (<PERSON> et al., 2022) and DKD (<PERSON> et al., 2022) . In general, TTM and WTTM provide outstanding performance among all the compared methods, and WTTM is better than TTM in most cases. Note that TTM always outperforms KD, confirming our theoretic analysis in Section 3.", "section": "MAIN RESULTS", "sec_num": "5.2"}, {"text": "To further improve the performance, we combine WTTM loss with 2 existing distillation losses respectively, namely CRD and ITRD (<PERSON> et al., 2021) , and the resulting performance is shown in Table 2 and Table 4 . For the combined methods, we directly adopt the optimal hyperparameters specified in the original papers without tuning (see A.5 for details). From the tables, we can see that the performance of the combined loss is always better than the pure performances of both ingredient losses, meaning that our proposed WTTM loss is orthogonal to other losses like CRD and ITRD. More importantly, the performance of WTTM aided by CRD and ITRD is consistently better than all other methods over all teacher-student pairs, achieving the state-of-the-art accuracy.", "section": "MAIN RESULTS", "sec_num": "5.2"}, {"text": "Table 1 : Top-1 accuracy (%) on CIFAR-100 of student models trained with various distillation methods, including both feature-based methods and logits-based methods. Each teacher-student pair has the same architecture. We highlight the best results in bold, and the second best results with underscores. Note that some results of DIST (for the models excluded in their paper) are produced by our reimplementation. Average over 5 runs. Results on ImageNet. In Table 5 , we demonstrate the performance of WTTM compared to many competitive distillation methods such as KD, CRD, SRRL (<PERSON> et al., 2020) , ReviewKD (<PERSON> et al., 2021) , ITRD (<PERSON> et al., 2021) , DKD (<PERSON> et al., 2022) , DIST (<PERSON> et al., 2022) , KD++ (<PERSON> et al., 2023) , NKD (<PERSON> et al., 2023b) , CTKD (<PERSON> et al., 2023c) , and KD-Zero (<PERSON> et al., 2023a ). It's shown that WTTM achieves outstanding performance on both teacher-student pairs.", "section": "MAIN RESULTS", "sec_num": "5.2"}, {"text": "To provide more comprehensive understanding and deeper insight about TTM and WTTM, we include 4 points of extension in this subsection, demonstrating some promising properties of WTTM and supporting our methodology with some analysis. Distill without L CE . In Table 6 , we compare the performance of WTTM without L CE to the performance of KD with L CE . We find that even in this unfair setting, WTTM can still outperform KD in most cases. This is of great value in the scenario where the ground-truth labels of the transfer set are not available. Distill from better teachers. Results in Table 7 show that the student can benefit more from a better teacher when distilling with WTTM. We observe that as the teacher model grows better, other distillation methods like KD and DIST cannot guarantee consistent improvement on the student side. In contrast, when we apply WTTM, the performance of the student is strictly increasing and consistently better than other distillation methods as the teacher becomes better and better. Regularization effect of TTM and WTTM. Following our methodology, TTM and WTTM are able to embed strong regularization into the distillation process, so it's expected that student's output probability distributions q resulting from TTM and WTTM should be much smoother than those resulting from KD. To validate this, we track the behavior of the average Shannon entropy of q for KD, TTM and WTTM respectively during training over 3 teacher-student pairs used in CIFAR-100 experiments, shown in Fig. 1 . Comparatively, students trained with TTM always have significantly larger entropy than those trained with KD. This is attributable to the Rényi entropy regularizer introduced in TTM when we remove the temperature scaling on the student side from KD. Moreover, students trained with WTTM always have slightly larger entropy than trained with TTM, owing to the sample-adaptive weighting coefficient U 1 T (p t ). Figure 1 : Average H(q) of 3 teacher-student pairs during training. For fair comparison, we use the same temperature T = 4 for KD, TTM and WTTM. The λ for KD is 0.9, so the β for TTM is 36, computed by Eq. ( 19), in order to maintain the same ratio between H(y, q) and H(p t T , q T ) as KD. As for WTTM, β = 36/ Ū , where Ū is the average of U 1 T (p t ) over all samples.", "section": "EXTENSIONS", "sec_num": "5.3"}, {"text": "WTTM facilitates more accurate teacher matching. A closer look at TTM and WTTM is favorable to shed light on why WTTM generally performs better than TTM. To this end, we track the behavior of the average D(p t T ||q) for TTM and WTTM during training over the same 3 teacherstudent pairs as above, shown in Fig. 2 . In order to reflect the behavior of pure distillation, we remove L CE from both WTTM and TTM. It's clear from the plots that WTTM always leads to smaller gap between p t T and q than TTM, demonstrating more accurate transformed teacher matching, which is the reason behind performance improvement. For each pair, the same T is adopted in TTM and WTTM.", "section": "EXTENSIONS", "sec_num": "5.3"}, {"text": "The paper systematically studies a variant of KD without temperature scaling on the student side, dubbed TTM. This slight modification gives rise to a Rényi entropy regularizer which improves the performance of the standard KD. Furthermore, we propose a sample-adaptive version of TTM, dubbed WTTM, to achieve more significant improvement. Extensive experimental results are presented to show the superiority of TTM and WTTM over other distillation methods on two image classification datasets. With almost the same training cost as KD, WTTM demonstrates state-ofthe-art performance, better than most feature-based distillation methods with high computational complexity.", "section": "CONCLUSION", "sec_num": "6"}, {"text": "In support of our claims in Subsection 2.3, we carry out a simple empirical analysis in this section. Specifically, we train four resnet20 models on CIFAR-100 dataset with different objectives and demonstrate their Shannon entropy histograms of the output probability distributions q in Figure 3 .", "section": "A APPENDIX A.1 EMPIRICAL ANALYSIS ON THE LS PERSPECTIVE OF KD", "sec_num": null}, {"text": "From Figures 3(b ) and 3(a), it is clear that the Shannon entropy of q in the case of LS is significantly larger than its counterpart in the case of ERM, which shows the regularization effect of LS.", "section": "A APPENDIX A.1 EMPIRICAL ANALYSIS ON THE LS PERSPECTIVE OF KD", "sec_num": null}, {"text": "In comparison of Figure 3 (c) with Figure 3 (a), it is also clear that the Shannon entropy of q in the case of KD with T = 1 is also significantly larger than its counterpart in the case of ERM, which confirms that KD can indeed be regarded as sample-adaptive LS when T = 1.", "section": "A APPENDIX A.1 EMPIRICAL ANALYSIS ON THE LS PERSPECTIVE OF KD", "sec_num": null}, {"text": "However, when T > 1, such a perspective doesn't hold anymore. To demonstrate this, we also trained resnet20 on CIFAR-100 dataset with KD setting T = 4, corresponding to Figure 3(d) .", "section": "A APPENDIX A.1 EMPIRICAL ANALYSIS ON THE LS PERSPECTIVE OF KD", "sec_num": null}, {"text": "Comparing Figure 3 (d) with Figure 3 (a), we see that the average Shannon entropy in the case of KD with T = 4 is even reduced over the ERM case significantly, showing an exactly opposite effect of LS. This confirms that when T > 1, KD can no longer be regarded as sample-adaptive LS. The reason why we only consider the power function in the main text is that the resulting power transform is equivalent to temperature scaling, which helps us to reveal the R<PERSON>yi entropy regularizer in the subsequent derivation. However, it's worth mentioning that the generalized transform is much more than a tool used in our derivations.", "section": "A APPENDIX A.1 EMPIRICAL ANALYSIS ON THE LS PERSPECTIVE OF KD", "sec_num": null}, {"text": "Currently, we use the power transform (temperature scaling) to smooth teacher's output distributions p in TTM and WTTM, following the convention in standard KD. However, it's possible that some other transforms could lead to better distillation compared to the power transform. Intuitively, mappings f associated to such transforms should satisfy 3 properties:", "section": "A APPENDIX A.1 EMPIRICAL ANALYSIS ON THE LS PERSPECTIVE OF KD", "sec_num": null}, {"text": "• f (0) = 0 and f (1) = 1. A deterministic prediction shouldn't be modified by the transform.", "section": "A APPENDIX A.1 EMPIRICAL ANALYSIS ON THE LS PERSPECTIVE OF KD", "sec_num": null}, {"text": "• Non-decreasing. A non-decreasing mapping avoids ruining the order information in p.", "section": "A APPENDIX A.1 EMPIRICAL ANALYSIS ON THE LS PERSPECTIVE OF KD", "sec_num": null}, {"text": "• f (p i ) > p i . To improve the smoothness of p, we need a mapping above the identity, since it expands the dynamic range of low probability values and compress the dynamic range of high probability values. As a result, after the normalization in Eq. ( 6), small probability values will be increased while large probability values will be decreased, achieving the goal of smoothing a distribution.", "section": "A APPENDIX A.1 EMPIRICAL ANALYSIS ON THE LS PERSPECTIVE OF KD", "sec_num": null}, {"text": "Following these suggested properties, some potential transforms can be developed in place of the power transform, while we leave this topic for future work.", "section": "A APPENDIX A.1 EMPIRICAL ANALYSIS ON THE LS PERSPECTIVE OF KD", "sec_num": null}, {"text": "In this section, we provide the pseudo-code for TTM and WTTM in a Pytorch-like style, shown in Algorithm 1. It's clear that both TTM and WTTM are quite easy to implement.", "section": "A.3 IMPLEMENTATION OF TTM AND WTTM", "sec_num": null}, {"text": "Algorithm 1 PyTorch-style pseudo-code for TTM and WTTM. ", "section": "A.3 IMPLEMENTATION OF TTM AND WTTM", "sec_num": null}, {"text": "γ = 0.1, β = 101 γ = 0.1, β = 76 γ = 0.3, β = 7 γ = 0.2, β = 8 γ = 0.1, β = 33 γ = 0.1, β = 100 γ = 0.1, β = 45 WTTM γ = 0.1, β = 4 γ = 0.1, β = 3 γ = 0.3, β = 1.5 γ = 0.2, β = 2 γ = 0.1, β = 1.5 γ = 0.1, β = 3 γ = 0.1, β = 2.25 WTTM+CRD γ = 0.1, β = 4 γ = 0.1, β = 2 γ = 0.3, β = 0.6 γ = 0.2, β = 1.4 γ = 0.2, β = 1 γ = 0.2, β = 4 γ = 0.2, β = 4 WTTM+ITRD γ = 0.3, β = 6 γ = 0", "section": "A.3 IMPLEMENTATION OF TTM AND WTTM", "sec_num": null}, {"text": "EQUATION", "section": "A.3 IMPLEMENTATION OF TTM AND WTTM", "sec_num": null}, {"text": "where µ is a balancing weight, and L dist is the additional distillation component, which can be CRD or ITRD in our experiments.", "section": "A.3 IMPLEMENTATION OF TTM AND WTTM", "sec_num": null}, {"text": "In the case where we combine WTTM with CRD, µ is always set to be 0.8, which is the optimal value used in the original paper.", "section": "A.3 IMPLEMENTATION OF TTM AND WTTM", "sec_num": null}, {"text": "In the case where we combine WTTM with ITRD, µ is always set to be 1. However, ITRD distillation loss itself is a combination of two components shown as follow", "section": "A.3 IMPLEMENTATION OF TTM AND WTTM", "sec_num": null}, {"text": "EQUATION", "section": "A.3 IMPLEMENTATION OF TTM AND WTTM", "sec_num": null}, {"text": ")", "section": "A.3 IMPLEMENTATION OF TTM AND WTTM", "sec_num": null}, {"text": "where β corr and β mi are two balancing weights within ITRD distillation loss. In our experiments, we always select the optimal β corr and β mi values specified in the original paper. Specifically, β corr = 2 and β mi = 0 for 3 teacher-student pairs, namely ResNet50 → MobileNetV2, ResNet50 → vgg8 and WRN-40-2 → ShuffleNetV1, while β corr = 2 and β mi = 1 for all the other 10 teacherstudent pairs. Note that there is another inherent hyperparameter α it within ITRD, which is selected as 1.01 for same-architecture distillation and 1.5 for different-architecture distillation, following the suggestion in the original paper.", "section": "A.3 IMPLEMENTATION OF TTM AND WTTM", "sec_num": null}, {"text": "This work provides multiple directions for our future research:", "section": "A.6 FUTURE WORK", "sec_num": null}, {"text": "• From Eq. ( 15), we know that the ratio between the distillation term D(p t T ||q T ) and the regularizer H 1 T (q) in TTM is determined by T . Also, the order of Rényi entropy is bound to be 1/T . However, these constraints are not necessary. In future work, we can directly combine the standard KD with a R<PERSON>yi entropy regularizer while setting the balancing weight and the order of Rényi entropy as tunable hyperparameters.", "section": "A.6 FUTURE WORK", "sec_num": null}, {"text": "• Given the generalized transform framework and related discussion in A.2, other transforms can be proposed in place of the power transform (temperature scaling) used in TTM and WTTM.", "section": "A.6 FUTURE WORK", "sec_num": null}, {"text": "• Systematically analyze the selection of the sample-adaptive weight in WTTM, in order to find the optimal one. ", "section": "A.6 FUTURE WORK", "sec_num": null}], "back_matter": [{"text": "This work was supported in part by the Natural Sciences and Engineering Research Council of Canada under Grant RGPIN203035-22, and by the Canada Research Chairs Program.", "section": "ACKNOWLEDGMENTS", "sec_num": null}, {"text": "In recent years, a variety of works have been proposed to advance the methodology of KD and its application to related fields. <PERSON> et al. (2022) proposed a correlation-based loss capturing the inter-class and intra-class relations from the teacher explicitly. <PERSON> et al. (2023b) unified KD and self distillation by decomposing and reorganizing the vanilla KD loss into a normalized KD (NKD) loss and proposed a novel self distillation method based on it. <PERSON> et al. (2023c) proposed a novel distillation method based on a dynamic and learnable distillation temperature. <PERSON><PERSON> et al. (2023) claimed that the power of vanilla KD was underestimated due to small data pitfall, and observed that the performance gap between vanilla KD and other meticulously designed KD variants could be greatly reduced by employing stronger training strategy. <PERSON> (2022) proposed a novel featurebased self distillation approach, reusing channel-wise and layer-wise features within the student to provide regularization. <PERSON> et al. (2023) presented a two-stage KD method dubbed NORM based on a feature transform module. <PERSON> <PERSON> Jin (2022) proposed a Shadow Knowledge Distillation framework to bridge offline and online distillation in an efficient way. <PERSON> et al. (2023) presented a trainingfree framework to search for the optimal student architectures given a teacher architecture. Also, following the trend of Automated Machine Learning (AutoML), several recent works (<PERSON> et al., 2023a; b) focused on automating distiller design using techniques like evolutionary algorithm and Monte Carlo tree search.A.8 STANDARD DEVIATION FOR RESULTS ON CIFAR-100 Below, we report the standard deviation for results on CIFAR-100 dataset in Table 11 and 12. Table 11: Top-1 accuracy (%) on CIFAR-100. Each teacher-student pair has the same architecture. Standard deviation is provided (the standard deviation is missing for DKD since it's not available in the literature). A.9 RESULTS ON TRANSFORMER-BASED MODELSTo verify the effectiveness of our proposed distillation method WTTM on transformer-based models, we apply it to a vision transformer model DeiT-Tiny (Touvron et al., 2021) , results shown in Table 13 . We conduct experiments following the settings in Yang et al. (2023b) and Yang et al. (2022) , and compare our results with the vanilla KD and two distillation methods proposed in the above two papers, namely NKD and ViTKD. It's shown that the performance of WTTM is better than all the three benchmark methods. Moreover, combined with ViTKD, WTTM can improve the Top-1 accuracy of DeiT-Tiny to 78.04%, which is also higher than the performance of NKD combined with ViTKD.", "section": "A.7 RELATED WORK", "sec_num": null}], "ref_entries": {"FIGREF1": {"text": "Figure 2: Average D(p t T ||q) of 3 teacher-student pairs during training. For each pair, the same T is adopted in TTM and WTTM.", "type_str": "figure", "fig_num": "2", "num": null, "uris": null}, "FIGREF2": {"text": "Figure 3: Entropy histograms for resnet20 trained with L CE , L LS with ϵ = 0.5, L KD with T = 1, and L KD with T = 4. For fair comparison, the same λ = 0.9 is adopted in both KD experiments with different temperatures.", "type_str": "figure", "fig_num": "3", "num": null, "uris": null}, "FIGREF3": {"text": "Figure 4: (a) Various point-wise mappings. (b) Power functions with different exponents γ.", "type_str": "figure", "fig_num": "4", "num": null, "uris": null}, "FIGREF4": {"text": "y_s: student output logits # y_t: teacher output logits # r: the exponent for power transform p_s = F.log_softmax(y_s, dim=1) p_t = torch.pow(F.softmax(y_t, dim=1), r) U = torch.sum(p_t, dim=1) # power sum p_t = p_t / U.unsqueeze(1) # power transformed teacher KL = torch.sum(F.kl_div(p_s, p_t, reduction='none'), dim=1) # TTM ttm_loss = torch.mean(KL) # WTTM wttm_loss = torch.mean(U * KL)A.4 HYPERPARAMETERSWe list fine-tuned γ and β in Tables8, 9and 10 covering all experiments, where γ = 1/T . Because we implement the temperature scaling with the equivalent power transform, the tuning is carried out over the exponent γ instead of the temperature T . Table 8: Hyperparameters for same-architecture distillation on CIFAR-100.", "type_str": "figure", "fig_num": null, "num": null, "uris": null}, "FIGREF5": {"text": ".4, β = 0.08 γ = 0.5, β = 5 γ = 0.3, β = 1.5 γ = 0.3, β = 0.015 γ = 0.1, β = 1.5 γ = 0.1, β = 0.5 WTTM w/o CE γ = 0.2 γ = 0.5 γ = 0.6 γ = 0.4 γ = 0.4 γ = 0.5 γ = 0.2 A.5 COMBINATION OF DISTILLATION LOSSES In this section, we clarify how we combine L W T T M with other distillation losses in our experiments. Actually, we simply add another distillation component to L W T T M with a multiplier. The total Table 9: Hyperparameters for different-architecture distillation on CIFAR-100. β = 16 γ = 0.2, β = 20 γ = 0.1, β = 70 γ = 0.2, β = 12 γ = 0.4, β = 40 γ = 0.3, β = 8 WTTM γ = 0.2, β = 3 γ = 0.2, β = 5 γ = 0.1, β = 2 γ = 0.2, β = 1.4 γ = 0.4, β = 16 γ = 0.3, β = 3 WTTM+CRD γ = 0.3, β = 4.2 γ = 0.3, β = 3 γ = 0.1, β = 3 γ = 0.2, β = 0.4 γ = 0.4, β = 12 γ = 0.2, β = 0.16 WTTM+ITRD γ = 0.3, β = 0.03 γ = 0.2, β = 0.02 γ = 0.1, β = 1 γ = 0.3, β = 0.6 γ = 0.4, β = 0.8 γ = 0.1, β = 0.2 Table 10: Hyparameters for ImageNet experiments. MobileNet γ = 0.7, β = 3.5 objective is", "type_str": "figure", "fig_num": null, "num": null, "uris": null}, "TABREF0": {"text": "", "type_str": "table", "content": "<table><tr><td>Teacher</td><td colspan=\"2\">WRN-40-2</td><td colspan=\"2\">WRN-40-2</td><td colspan=\"2\">resnet56</td><td colspan=\"2\">resnet110</td><td>resnet110</td><td>resnet32x4</td><td>vgg13</td></tr><tr><td>Student</td><td colspan=\"2\">WRN-16-2</td><td colspan=\"2\">WRN-40-1</td><td colspan=\"2\">resnet20</td><td colspan=\"2\">resnet20</td><td>resnet32</td><td>resnet8x4</td><td>vgg8</td></tr><tr><td>Teacher</td><td colspan=\"2\">75.61</td><td/><td>75.61</td><td colspan=\"2\">72.34</td><td colspan=\"2\">74.31</td><td>74.31</td><td>79.42</td><td>74.64</td></tr><tr><td>Student</td><td colspan=\"2\">73.26</td><td/><td>71.98</td><td colspan=\"2\">69.06</td><td colspan=\"2\">69.06</td><td>71.14</td><td>72.50</td><td>70.36</td></tr><tr><td/><td/><td/><td/><td/><td colspan=\"4\">Feature-based</td></tr><tr><td>FitNet</td><td colspan=\"2\">73.58</td><td/><td>72.24</td><td colspan=\"2\">69.21</td><td colspan=\"2\">68.99</td><td>71.06</td><td>73.50</td><td>71.02</td></tr><tr><td>AT</td><td colspan=\"2\">74.08</td><td/><td>72.77</td><td colspan=\"2\">70.55</td><td colspan=\"2\">70.22</td><td>72.31</td><td>73.44</td><td>71.43</td></tr><tr><td>VID</td><td colspan=\"2\">74.11</td><td/><td>73.30</td><td colspan=\"2\">70.38</td><td colspan=\"2\">70.16</td><td>72.61</td><td>73.09</td><td>71.23</td></tr><tr><td>RKD</td><td colspan=\"2\">73.35</td><td/><td>72.22</td><td colspan=\"2\">69.61</td><td colspan=\"2\">69.25</td><td>71.82</td><td>71.90</td><td>71.48</td></tr><tr><td>PKT</td><td colspan=\"2\">74.54</td><td/><td>73.45</td><td colspan=\"2\">70.34</td><td colspan=\"2\">70.25</td><td>72.61</td><td>73.64</td><td>72.88</td></tr><tr><td>CRD</td><td colspan=\"2\">75.48</td><td/><td>74.14</td><td colspan=\"2\">71.16</td><td colspan=\"2\">71.46</td><td>73.48</td><td>75.51</td><td>73.94</td></tr><tr><td/><td/><td/><td/><td/><td colspan=\"3\">Logits-based</td></tr><tr><td>KD</td><td colspan=\"2\">74.92</td><td/><td>73.54</td><td colspan=\"2\">70.66</td><td colspan=\"2\">70.67</td><td>73.08</td><td>73.33</td><td>72.98</td></tr><tr><td>DIST</td><td colspan=\"2\">75.51</td><td/><td>74.73</td><td colspan=\"2\">71.75</td><td colspan=\"2\">71.65</td><td>73.69</td><td>76.31</td><td>73.89</td></tr><tr><td>DKD</td><td colspan=\"2\">76.24</td><td/><td>74.81</td><td colspan=\"2\">71.97</td><td colspan=\"2\">n/a</td><td>74.11</td><td>76.32</td><td>74.68</td></tr><tr><td>TTM</td><td colspan=\"2\">76.23</td><td/><td>74.32</td><td colspan=\"2\">71.83</td><td colspan=\"2\">71.46</td><td>73.97</td><td>76.17</td><td>74.33</td></tr><tr><td>WTTM</td><td colspan=\"2\">76.37</td><td/><td>74.58</td><td colspan=\"2\">71.92</td><td colspan=\"2\">71.67</td><td>74.13</td><td>76.06</td><td>74.44</td></tr><tr><td>Teacher</td><td/><td colspan=\"2\">WRN-40-2</td><td colspan=\"2\">WRN-40-2</td><td colspan=\"2\">resnet56</td><td>resnet110</td><td>resnet110</td><td>resnet32x4</td><td>vgg13</td></tr><tr><td>Student</td><td/><td colspan=\"2\">WRN-16-2</td><td colspan=\"2\">WRN-40-1</td><td colspan=\"2\">resnet20</td><td>resnet20</td><td>resnet32</td><td>resnet8x4</td><td>vgg8</td></tr><tr><td>CRD</td><td/><td>75.48</td><td/><td>74.14</td><td/><td>71.16</td><td/><td>71.46</td><td>73.48</td><td>75.51</td><td>73.94</td></tr><tr><td>ITRD</td><td/><td>76.12</td><td/><td>75.18</td><td/><td>71.47</td><td/><td>71.99</td><td>74.26</td><td>76.19</td><td>74.93</td></tr><tr><td>WTTM</td><td/><td>76.37</td><td/><td>74.58</td><td/><td>71.92</td><td/><td>71.67</td><td>74.13</td><td>76.06</td><td>74.44</td></tr><tr><td colspan=\"2\">WTTM+CRD</td><td>76.61</td><td/><td>74.94</td><td/><td>72.20</td><td/><td>72.13</td><td>74.52</td><td>76.65</td><td>74.71</td></tr><tr><td colspan=\"2\">WTTM+ITRD</td><td>76.65</td><td/><td>75.34</td><td/><td>72.16</td><td/><td>72.20</td><td>74.36</td><td>77.36</td><td>75.13</td></tr></table>", "num": null, "html": null}, "TABREF1": {"text": "Top-1 accuracy (%) on CIFAR-100. Each teacher-student pair has different architectures. Note that some results of DIST (for the models excluded in their paper) are produced by our reimplementation. Average over 3 runs.", "type_str": "table", "content": "<table><tr><td>Teacher</td><td>vgg13</td><td>ResNet50</td><td>ResNet50</td><td>resnet32x4</td><td>resnet32x4</td><td>WRN-40-2</td></tr><tr><td>Student</td><td>MobileNetV2</td><td>MobileNetV2</td><td>vgg8</td><td>ShuffleNetV1</td><td>ShuffleNetV2</td><td>ShuffleNetV1</td></tr><tr><td>Teacher</td><td>74.64</td><td>79.34</td><td>79.34</td><td>79.42</td><td>79.42</td><td>75.61</td></tr><tr><td>Student</td><td>64.6</td><td>64.6</td><td>70.36</td><td>70.5</td><td>71.82</td><td>70.5</td></tr><tr><td/><td/><td/><td colspan=\"2\">Feature-based</td><td/><td/></tr><tr><td>FitNet</td><td>64.14</td><td>63.16</td><td>70.69</td><td>73.59</td><td>73.54</td><td>73.73</td></tr><tr><td>AT</td><td>59.40</td><td>58.58</td><td>71.84</td><td>71.73</td><td>72.73</td><td>73.32</td></tr><tr><td>VID</td><td>65.56</td><td>67.57</td><td>70.30</td><td>73.38</td><td>73.40</td><td>73.61</td></tr><tr><td>RKD</td><td>64.52</td><td>64.43</td><td>71.50</td><td>72.28</td><td>73.21</td><td>72.21</td></tr><tr><td>PKT</td><td>67.13</td><td>66.52</td><td>73.01</td><td>74.10</td><td>74.69</td><td>73.89</td></tr><tr><td>CRD</td><td>69.73</td><td>69.11</td><td>74.30</td><td>75.11</td><td>75.65</td><td>76.05</td></tr><tr><td/><td/><td/><td colspan=\"2\">Logits-based</td><td/><td/></tr><tr><td>KD</td><td>67.37</td><td>67.35</td><td>73.81</td><td>74.07</td><td>74.45</td><td>74.83</td></tr><tr><td>DIST</td><td>68.50</td><td>68.66</td><td>74.11</td><td>76.34</td><td>77.35</td><td>76.40</td></tr><tr><td>DKD</td><td>69.71</td><td>70.35</td><td>n/a</td><td>76.45</td><td>77.07</td><td>76.70</td></tr><tr><td>TTM</td><td>68.98</td><td>69.24</td><td>74.87</td><td>74.18</td><td>76.57</td><td>75.39</td></tr><tr><td>WTTM</td><td>69.16</td><td>69.59</td><td>74.82</td><td>74.37</td><td>76.55</td><td>75.42</td></tr></table>", "num": null, "html": null}, "TABREF2": {"text": "Top-1 accuracy (%) on CIFAR-100. Each teacher-student pair has different architectures. Average over 3 runs.", "type_str": "table", "content": "<table><tr><td>Teacher</td><td>vgg13</td><td>ResNet50</td><td>ResNet50</td><td>resnet32x4</td><td>resnet32x4</td><td>WRN-40-2</td></tr><tr><td>Student</td><td>MobileNetV2</td><td>MobileNetV2</td><td>vgg8</td><td>ShuffleNetV1</td><td>ShuffleNetV2</td><td>ShuffleNetV1</td></tr><tr><td>CRD</td><td>69.73</td><td>69.11</td><td>74.30</td><td>75.11</td><td>75.65</td><td>76.05</td></tr><tr><td>ITRD</td><td>70.39</td><td>71.41</td><td>75.71</td><td>76.91</td><td>77.40</td><td>77.35</td></tr><tr><td>WTTM</td><td>69.16</td><td>69.59</td><td>74.82</td><td>74.37</td><td>76.55</td><td>75.42</td></tr><tr><td>WTTM+CRD</td><td>70.30</td><td>70.84</td><td>75.30</td><td>75.82</td><td>77.04</td><td>76.86</td></tr><tr><td>WTTM+ITRD</td><td>70.70</td><td>71.56</td><td>76.00</td><td>77.03</td><td>77.68</td><td>77.44</td></tr></table>", "num": null, "html": null}, "TABREF3": {"text": "Top-1 accuracy (%) on ImageNet. The adopted teacher models are released by PyTorch(<PERSON><PERSON><PERSON> et al., 2019).", "type_str": "table", "content": "<table><tr><td>Teacher</td><td>Student</td><td>KD</td><td colspan=\"5\">CRD SRRL ReviewKD ITRD DKD DIST KD++ NKD CTKD KD-Zero WTTM</td></tr><tr><td colspan=\"4\">ResNet-34 (73.31) ResNet-18 (69.76) 70.66 71.17 71.73</td><td>71.61</td><td colspan=\"3\">71.68 71.70 72.07 71.98 71.96 71.51</td><td>72.17</td><td>72.19</td></tr><tr><td colspan=\"4\">ResNet-50 (76.16) MobileNet (68.87) 70.50 71.37 72.49</td><td>72.56</td><td>n/a</td><td>72.05 73.24 72.77 72.58</td><td>n/a</td><td>73.02</td><td>73.09</td></tr></table>", "num": null, "html": null}, "TABREF4": {"text": "Comparison between WTTM without L CE and KD with L CE on CIFAR-100. Accuracy is averaged over 5 runs.", "type_str": "table", "content": "<table/>", "num": null, "html": null}, "TABREF6": {"text": "Performance of ResNet-18 on ImageNet distilled from different teachers.", "type_str": "table", "content": "<table><tr><td>Teacher</td><td>Student</td><td colspan=\"2\">Teacher Student</td><td>KD</td><td colspan=\"2\">DIST WTTM</td></tr><tr><td>ResNet-34</td><td/><td>73.31</td><td/><td colspan=\"2\">71.21 72.07</td><td>72.19</td></tr><tr><td>ResNet-50 ResNet-101</td><td>ResNet-18</td><td>76.13 77.37</td><td>69.76</td><td colspan=\"2\">71.35 72.12 71.09 72.08</td><td>72.26 72.34</td></tr><tr><td>ResNet-152</td><td/><td>78.31</td><td/><td colspan=\"2\">71.12 72.24</td><td>72.39</td></tr></table>", "num": null, "html": null}, "TABREF7": {"text": "Top-1 accuracy (%) on CIFAR-100. Each teacher-student pair has different architectures. Standard deviation is provided (the standard deviation is missing for DKD since it's not available in the literature). Feature-based FitNet 64.14 ± 0.50 63.16 ± 0.47 70.69 ± 0.22 73.59 ± 0.15 73.54 ± 0.22 73.73 ± 0.32 AT 59.40 ± 0.20 58.58 ± 0.54 71.84 ± 0.28 71.73 ± 0.31 72.73 ± 0.09 73.32 ± 0.35 VID 65.56 ± 0.42 67.57 ± 0.28 70.30 ± 0.31 73.38 ± 0.09 73.40 ± 0.17 73.61 ± 0.12 RKD 64.52 ± 0.45 64.43 ± 0.42 71.50 ± 0.07 72.28 ± 0.39 73.21 ± 0.28 72.21 ± 0.16 PKT 67.13 ± 0.30 66.52 ± 0.33 73.01 ± 0.14 74.10 ± 0.25 74.69 ± 0.34 73.89 ± 0.16 CRD 69.73 ± 0.42 69.11 ± 0.28 74.30 ± 0.14 75.11 ± 0.32 75.65 ± 0.10 76.05 ± 0.14 Logits-based KD 67.37 ± 0.32 67.35 ± 0.32 73.81 ± 0.13 74.07 ± 0.19 74.45 ± 0.27 74.83 ± 0.17 DIST 68.50 ± 0.26 68.66 ± 0.23 74.11 ± 0.07 76.34 ± 0.18 77.35 ± 0.25 76.40 ± 0.± 0.85 69.24 ± 0.28 74.87 ± 0.31 74.18 ± 0.26 76.57 ± 0.26 75.39 ± 0.33 WTTM 69.16 ± 0.20 69.59 ± 0.58 74.82 ± 0.28 74.37 ± 0.39 76.55 ± 0.08 75.42 ± 0.34 WTTM+CRD 70.30 ± 0.68 70.84 ± 0.56 75.30 ± 0.42 75.82 ± 0.16 77.04 ± 0.19 76.86 ± 0.37 WTTM+ITRD 70.70 ± 0.45 71.56 ± 0.15 76.00 ± 0.17 77.03 ± 0.26 77.68 ± 0.26 77.44 ± 0.27", "type_str": "table", "content": "<table><tr><td>Teacher</td><td>vgg13</td><td>ResNet50</td><td>ResNet50</td><td>resnet32x4</td><td>resnet32x4</td><td>WRN-40-2</td></tr><tr><td>Student</td><td>MobileNetV2</td><td>MobileNetV2</td><td>vgg8</td><td>ShuffleNetV1</td><td>ShuffleNetV2</td><td>ShuffleNetV1</td></tr><tr><td>Teacher</td><td>74.64</td><td>79.34</td><td>79.34</td><td>79.42</td><td>79.42</td><td>75.61</td></tr><tr><td>Student</td><td>64.6</td><td>64.6</td><td>70.36</td><td>70.5</td><td>71.82</td><td>70.5</td></tr><tr><td/><td/><td/><td/><td/><td/><td>03</td></tr><tr><td>DKD</td><td>69.71</td><td>70.35</td><td>n/a</td><td>76.45</td><td>77.07</td><td>76.70</td></tr><tr><td>TTM</td><td>68.98</td><td/><td/><td/><td/><td/></tr></table>", "num": null, "html": null}, "TABREF8": {"text": "Top-1 accuracy (%) on ImageNet.", "type_str": "table", "content": "<table><tr><td>Teacher</td><td>Student</td><td>KD</td><td colspan=\"4\">ViTKD NKD WTTM NKD+ViTKD WTTM+ViTKD</td></tr><tr><td colspan=\"3\">DeiT III-Small (82.76) DeiT-Tiny (74.42) 76.01</td><td>76.06</td><td>76.68</td><td>77.03</td><td>77.78</td><td>78.04</td></tr></table>", "num": null, "html": null}}}}