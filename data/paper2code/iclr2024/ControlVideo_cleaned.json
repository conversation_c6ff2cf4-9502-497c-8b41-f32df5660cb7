{"paper_id": "ControlVideo", "title": "CONTROLVIDEO: TRAINING-FREE CONTROLLABLE TEXT-TO-VIDEO GENERATION", "abstract": "Text-driven diffusion models have unlocked unprecedented abilities in image generation, whereas their video counterpart lags behind due to the excessive training cost. To avert the training burden, we propose a training-free ControlVideo to produce high-quality videos based on the provided text prompts and motion sequences. Specifically, ControlVideo adapts a pre-trained text-to-image model (i.e., Con-trolNet) for controllable text-to-video generation. To generate continuous videos without flicker effects, we propose an interleaved-frame smoother to smooth the intermediate frames. In particular, interleaved-frame smoother splits the whole video with successive three-frame clips, and stabilizes each clip by updating the middle frame with the interpolation among other two frames in latent space. Furthermore, a fully cross-frame interaction mechanism is exploited to further enhance the frame consistency, while a hierarchical sampler is employed to produce long videos efficiently. Extensive experiments demonstrate that our ControlVideo outperforms the state-of-the-arts both quantitatively and qualitatively. It is worth noting that, thanks to the efficient designs, ControlVideo could generate both short and long videos within several minutes using one NVIDIA 2080Ti. Code and videos are available at this link.", "pdf_parse": {"paper_id": "ControlVideo", "abstract": [{"text": "Text-driven diffusion models have unlocked unprecedented abilities in image generation, whereas their video counterpart lags behind due to the excessive training cost. To avert the training burden, we propose a training-free ControlVideo to produce high-quality videos based on the provided text prompts and motion sequences. Specifically, ControlVideo adapts a pre-trained text-to-image model (i.e., Con-trolNet) for controllable text-to-video generation. To generate continuous videos without flicker effects, we propose an interleaved-frame smoother to smooth the intermediate frames. In particular, interleaved-frame smoother splits the whole video with successive three-frame clips, and stabilizes each clip by updating the middle frame with the interpolation among other two frames in latent space. Furthermore, a fully cross-frame interaction mechanism is exploited to further enhance the frame consistency, while a hierarchical sampler is employed to produce long videos efficiently. Extensive experiments demonstrate that our ControlVideo outperforms the state-of-the-arts both quantitatively and qualitatively. It is worth noting that, thanks to the efficient designs, ControlVideo could generate both short and long videos within several minutes using one NVIDIA 2080Ti. Code and videos are available at this link.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Large-scale diffusion models have made a tremendous breakthrough on text-to-image synthesis (<PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022) and their creative applications (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2022) . Several studies (<PERSON> et al., 2022b; a; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2022) attempt to replicate this success in the video counterpart, i.e., modeling higher-dimensional complex video distributions in the wild world. However, training such a text-to-video model requires massive amounts of high-quality videos and computational resources, which limits further research and applications by relevant communities.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In this work, we study a new and efficient form to avert the excessive training requirements: controllable text-to-video generation with text-to-image models. As shown in Fig. 1 , our method, termed ControlVideo, takes textual description and motion sequence (e.g., depth or edge maps) as conditions to generate videos. Instead of learning the video distribution from scratch, ControlVideo adapts the pre-trained text-to-image models (e.g., ControlNet (Zhang & Agrawala, 2023) ) for high-quality video generation. With the structural information from motion sequence and the superior generation capability of image models, it is feasible to produce a vivid video without additional training.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "However, as shown in Fig. 1 , due to the lack of temporal interaction, individually producing each frame with ControlNet (Zhang & Agrawala, 2023 ) fails to ensure both (i) frame consistency and (ii) video continuity. Frame consistency requires all frames to be generated with a coherent appearance, while video continuity ensures smooth transitions between frames. Tune-A-Video (<PERSON> et al., 2022b) and Text2Video-Zero (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) facilitate appearance consistency by extending self-attention to sparser cross-frame attention. Nonetheless, such a cross-frame interaction is not sufficient to guarantee video continuity, and visible flickers appear in their synthesized videos (as shown in Fig. 1 and corresponding videos). Intuitively, a continuous video could be considered as multiple continuous three-frame clips, so the problem of ensuring the video continuity is converted to ensuring all three-frame clips continuous.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Driven by this analysis, we propose an interleaved-frame smoother to enable continuous video generation. Specifically, interleaved-frame smoother divides all three-frame clips into even and odd clips based on indices of middle frames, and separately smooths out their corresponding latents at different denoising steps. To stabilize the latent of each clip, we first convert it to predicted RGB frames with DDIM, followed by replacing the middle frame with the interpolated frame. Note that, the smoother is only applied at a few timesteps, and the quality and individuality of interpolated frames can be well retained by the following denoising steps.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We further investigate the cross-frame mechanisms in terms of effectiveness and efficiency. Firstly, we explore fully cross-frame interaction that concatenates all frames to become a \"larger image\", and first empirically demonstrate its superior consistency and quality than sparser counterparts (see Sec. 4.4) . Secondly, applying existing cross-frame mechanisms for long-video generation suffers from either heavy computational burden or long-term inconsistency. Therefore, a hierarchical sampler is presented to produce a long video in a top-down way. In specific, it pre-generates the key frames with fully cross-frame attention for long-range coherence, followed by efficiently generating the short clips conditioned on pairs of key frames.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We conduct the experiments on extensively collected motion-prompt pairs, and show that Con-trolVideo outperforms alternative competitors qualitatively and quantitatively. Thanks to the efficient designs, ControlVideo produces short and long videos in several minutes using one NVIDIA 2080Ti.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In summary, our contributions are presented as follows:", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• We propose training-free ControlVideo with interleaved-frame smoother for consistent and continuous controllable text-to-video generation. • Interleaved-frame smoother alternately smooths out the latents of three-frame clips, effectively stabilizing the entire video during sampling. • We empirically demonstrate the superior consistency and quality of fully cross-frame interaction, while presenting a hierarchical sampler for long-video generation in commodity GPUs.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Latent diffusion model (LDM) (<PERSON><PERSON><PERSON> et al., 2022) is an efficient variant of diffusion models (<PERSON> et al., 2020) by applying the diffusion process in the latent space. LDM uses an encoder to compress an image x into latent code z = (x). It learns the distribution of image latent codes z 0 ∼ p data (z 0 ) in a DDPM formulation (<PERSON> et al., 2020) , including a forward and a backward process. The forward diffusion process gradually adds gaussian noise at each timestep t to obtain z t :", "section": "BACKGROUND", "sec_num": "2"}, {"text": "EQUATION", "section": "BACKGROUND", "sec_num": "2"}, {"text": "or ControlNet", "section": "BACKGROUND", "sec_num": "2"}, {"text": "Interleaved-Frame S<PERSON>other × T steps where {β t } T t=1 are the scale of noises, and T denotes the number of diffusion timesteps. The backward denoising process reverses the above diffusion process to predict less noisy z t-1 :", "section": "A swan moving in a lake", "sec_num": null}, {"text": "p θ (z t-1 |z t ) = N (z t-1 ; µ θ (z t , t), Σ θ (z t , t)).", "section": "A swan moving in a lake", "sec_num": null}, {"text": "(2)", "section": "A swan moving in a lake", "sec_num": null}, {"text": "The µ θ and Σ θ are implemented with a denoising model ϵ θ with learnable parameters θ. When generating new samples, we start from z T ∼ N (0, 1) and employ DDIM sampling to predict z t-1 of previous timestep:", "section": "A swan moving in a lake", "sec_num": null}, {"text": "EQUATION", "section": "A swan moving in a lake", "sec_num": null}, {"text": "where α t = t i=1 (1 -β i ). We use z t→0 to represent \"predicted z 0 \" at timestep t for simplicity. Note that we use Stable Diffusion (SD) ϵ θ (z t , t, τ ) as our base model, which is an instantiation of text-guided LDMs pre-trained on billions of image-text pairs. τ denotes the text prompt.", "section": "A swan moving in a lake", "sec_num": null}, {"text": "ControlNet (Zhang & Agrawala, 2023) enables SD to support more controllable input conditions during text-to-image synthesis, e.g., depth maps, poses, edges, etc. The ControlNet uses the same U-Net (<PERSON> et al., 2015) architecture as SD and finetunes its weights to support taskspecific conditions, converting ϵ θ (z t , t, τ ) to ϵ θ (z t , t, c, τ ), where c denotes additional conditions. To distinguish the U-Net architectures of SD and ControlNet, we denote the former as the main U-Net while the latter as the auxiliary U-Net.", "section": "A swan moving in a lake", "sec_num": null}, {"text": "Controllable text-to-video generation aims to produce a video of length N conditioned on motion sequences c = {c i } N -1 i=0 and a text prompt τ . As illustrated in Fig. 2 , we propose ControlVideo with interleaved-frame smoother towards consistent and continuous video generation. ControlVideo, adapted from ControlNet, adds cross-frame interaction to self-attention modules for frame consistency (in Sec. 3.1). To ensure video continuity, interleaved-frame smoother divides all three-frame clips into even and odd clips, and separately smooths out their corresponding latents at different denoising steps (in Sec. 3.2). Finally, we further investigate the cross-frame mechanisms in terms of effectiveness and efficiency, including fully cross-frame interaction and hierarchical sampler (in Sec. 3.3).", "section": "CONTROLVIDEO", "sec_num": "3"}, {"text": "The main challenge of adapting text-to-image models to the video counterpart is to ensure temporal consistency. Leveraging the controllability of ControlNet, motion sequences could provide coarselevel consistency in structure. Nonetheless, due to the lack of temporal interaction, individually producing each frame with ControlNet leads to drastic inconsistency in appearance (see row 2 in Algorithm 1 Interleaved-frame smoother", "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "Require: z t = {z i t } N -1 i=0 , c = {c i } N -1 i=0 , τ , timestep t. 1: z t→0 ← zt- √ 1-αtϵ θ (zt,t,c,τ ) √ αt . ▷ predict clean latents 2: x t→0 ← D(z t→0 ); xt→0 ← x t→0 ▷ convert latents to RGB space 3: if (t mod 2) = 0 then ▷ smooth all even three-frame clips ( x2k-1 t→0 , x2k t→0 , x2k+1 t→0 ) 4:", "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "for k from 0 to N/2 do 5:", "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "x2k t→0 ← Interpolate(x 2k-1 t→0 , x 2k+1 t→0 ) 6: else if (t mod 2) = 1 then ▷ smooth all odd three-frame clips ( x2k t→0 , x2k+1 t→0 , x2k+2 t→0 ) 7:", "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "for k from 0 to N/2 do 8:", "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "x2k+1 t→0 ← Interpolate(x 2k t→0 , x 2k+2 t→0 ) 9: zt→0 ← E( xt→0 ) ▷ convert frames to latent space 10: z t-1 ← √ α t-1 zt→0 + √ 1 -α t-1 • ϵ θ (z t , t, c, τ ).", "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "▷ predict less noisy latent 11: return z t-1 Fig. 5 ). Similar to previous works (<PERSON> et al., 2022b; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) , we also extend original self-attention of SD U-Net to cross-frame attention, so that the video content could be temporally shared via inter-frame interaction.", "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "In specific, ControlVideo inflates the main U-Net from Stable Diffusion along the temporal axis, while keeping the auxiliary U-Net from ControlNet. Analogous to (<PERSON> et al., 2022b; <PERSON> et al., 2022b; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) , it directly converts 2D convolution layers to 3D counterpart by replacing 3 × 3 kernels with 1 × 3 × 3 kernels. Self-attention is converted to cross-frame attention by querying from other frames as:", "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "EQUATION", "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "where W Q , W K , and W V project z t into query, key, and value, respectively. z i t and z t denote ith latent frame and the latents of reference frames at timestep t. We will discuss the choices of cross-frame mechanisms (i.e., reference frames) in Sec. 3.3", "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "Albeit cross-frame interaction promisingly keeps frame consistency in appearance, they are still visibly flickering in structure. Discrete motion sequences only ensure coarse-level structural consistency, not sufficient to keep the continuous inter-frame transition. Intuitively, a continuous video could be considered as multiple continuous three-frame clips, so we simplify the problem of ensuring the video continuity to ensuring all three-frame clips continuous.", "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "Inspired by this, we propose an interleaved-frame smoother to enable continuous video generation. In Alg. 1, interleaved-frame smoother divides all three-frame clips into even and odd clips based on indices of middle frames, and individually smooths their corresponding latents at different timesteps. To stabilize the latent of each clip, we first convert it to predicted RGB frames with DDIM, following by replacing middle frame with the interpolated frame. Specifically, at timestep t, we first predict the clean video latent z t→0 according to z t :", "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "EQUATION", "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "After projecting z t→0 into a RGB video x t→0 = D(z t→0 ), we convert it to a more smoothed video xt→0 by replacing each middle frame with the interpolated one. Based on smoothed video latent zt→0 = E( xt→0 ), we compute the less noisy latent z t-1 following DDIM denoising in Eq. 3:", "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "EQUATION", "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "We note that the above process is only performed at a few intermediate timesteps, the individuality and quality of interpolated frames are also well retained by the following denoising steps. Additionally, the newly computational burden can be negligible (See Table 3 ). ", "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "Fully cross-frame interaction. Previous works (<PERSON> et al., 2022b; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) usually replace self-attention with sparser cross-frame mechanisms, e.g., taking the reference frames as first or previous frames. Such mechanisms will increase the discrepancy between the query and key in self-attention modules, resulting in the degradation of video quality and consistency. In contrast, fully cross-frame interaction considers all frames as reference (i.e., becoming a \"large image\"), so has a less generation gap with text-to-image models. We conduct comparison experiments on above mechanisms in Fig. 5 and Table 3 . Despite slightly more computational burden, fully cross-frame interaction empirically shows better consistency and quality than the sparser counterparts.", "section": "CROSS-<PERSON><PERSON><PERSON> MECHANISMS FOR EFFECTIVENESS AND EFFICIENCY", "sec_num": "3.3"}, {"text": "Hierarchical sampler. (<PERSON><PERSON><PERSON><PERSON> et al., 2020) to estimate the edges and depth maps of source videos, and form 125 motion-prompt pairs as our evaluation dataset. More details are provided in Appendix A.", "section": "CROSS-<PERSON><PERSON><PERSON> MECHANISMS FOR EFFECTIVENESS AND EFFICIENCY", "sec_num": "3.3"}, {"text": "Metrics. We evaluate the video quality from three perspectives. (i) Frame consistency (FC): the average cosine similarity between all pairs of consecutive frames, and (ii) Prompt consistency (PC): the average cosine similarity between input prompt and all video frames. (iii) Warping error (WE) (<PERSON> et al., 2018) : the average error between all frames and their warped frames using optical flow.", "section": "CROSS-<PERSON><PERSON><PERSON> MECHANISMS FOR EFFECTIVENESS AND EFFICIENCY", "sec_num": "3.3"}, {"text": "Baselines. We compare our ControlVideo with three publicly available methods: (i) Tune-A-Video (<PERSON> et al., 2022b) Qualitative results. Fig. 3 first illustrates the visual comparisons of synthesized videos conditioned on both (a) depth maps and (b) canny edges. As shown in Fig. 3 (a), our ControlVideo demonstrates better consistency in both appearance and structure than alternative competitors. Tune-A-Video fails to keep the temporal consistency of both appearance and fine-grained structure, e.g., the color of coat and the structure of road. With the motion information from depth maps, Text2Video-Zero achieves promising consistency in structure, but still struggles with incoherent appearance in videos e.g., the color of coat. Besides, ControlVideo also performs more robustly when dealing with large motion inputs. As illustrated in Fig. 3 (b), Tune-A-Video ignores the structure information from source videos. Text2Video-Zero adopts the first-only cross-frame mechanism to trade off frame quality and appearance consistency, and generates later frames with visible artifacts. In contrast, with the proposed fully cross-frame mechanism and interleaved-frame smoother, our ControlVideo can handle large motion to generate high-quality and consistent videos. Quantitative results. We have also compared our ControlVideo with existing methods quantitatively on 125 video-prompt pairs. From Table 1 , our ControlVideo conditioned on depth outperforms the state-of-the-art methods in terms of all metrics, which is consistent with the qualitative results. In contrast, despite finetuning on a source video, Tune-A-Video still struggles to produce temporally coherent videos. Although conditioned on the same structure information, Text2Video-Zero obtains worse frame consistency and warping error than ControlVideo. For each method, the depth-conditioned models generate videos with higher frame and prompt consistency than the canny-condition counterpart, since depth maps provide smoother motion information.", "section": "CROSS-<PERSON><PERSON><PERSON> MECHANISMS FOR EFFECTIVENESS AND EFFICIENCY", "sec_num": "3.3"}, {"text": "We then perform the user study to compare our ControlVideo conditioned on depth maps with other competing methods. In specific, we provide each rater a structure sequence, a text prompt, and synthesized videos from two different methods (in random order). Then we ask them to select the better synthesized videos for each of three measurements: (i) video quality, (ii) temporal consistency throughout all frames, and (iii) text alignment between prompts and synthesized videos. The evaluation set consists of 125 representative structure-prompt pairs. Each pair is evaluated by 5 raters, and we take a majority vote for the final result. From Table 2 , the raters strongly favor our synthesized videos from all three perspectives, especially in temporal consistency. On the other hand, Tune-A-Video fails to generate consistent and high-quality videos with only DDIM inversion for structural guidance, and Text2Video-Zero also produces videos with lower quality and coherency. ", "section": "USER STUDY", "sec_num": "4.3"}, {"text": "Effect of fully cross-frame interaction. To demonstrate the effectiveness of the fully cross-frame interaction, we conduct a comparison with the following variants: i) individual: no interaction between all frames, ii) first-only: all frames attend to the first one, iii) sparse-causal: each frame attends to the first and former frames, iv) fully: our fully cross-frame, refer to Sec. 3. Note that, all the above models are extended from ControlNet without any finetuning. The qualitative and quantitative results are shown in Fig. 5 and Table 3 , respectively. From Fig. 5 , the individual cross-frame mechanism suffers from severe temporal inconsistency, e.g., colorful and black-and-white frames. The first-only and sparse-causal mechanisms reduce some appearance inconsistency by adding crossframe interaction. However, they still produce videos with structural inconsistency and visible artifacts, e.g., the orientation of the elephant and duplicate nose (row 3 in Fig. 5 ). In contrast, due to less generation gap with ControlNet, our fully cross-frame interaction performs better appearance coherency and video quality. Though the introduced interaction brings an extra 1 ∼ 2× time cost, it is acceptable for a high-quality video generation.", "section": "ABLATION STUDY", "sec_num": "4.4"}, {"text": "Effect of interleaved-frame smoother. We further analyze the effect of the proposed interleavedframe smoother. From Table 3 and last two rows of Fig. 5 , our interleaved-frame smoother greatly improves the video smoothness, e.g., mitigating structural flickers in red boxes. We provide more ablation studies on the timestep choices of the smoother in Appendix C and ablation studies.", "section": "ABLATION STUDY", "sec_num": "4.4"}, {"text": "Producing a long video usually requires an advanced GPU with high memory. With the proposed hierarchical sampler, our ControlVideo achieves long video generation (more than 100 frames) in a memory-efficient manner. As shown in Fig. 6 , our ControlVideo can produce a long video with consistently high quality. Notably, benefiting from our efficient sampling, it only takes approximately ten minutes to generate 100 frames with resolution 512 × 512 in one NVIDIA RTX 2080Ti. More visualizations of long videos can be found in Appendix D.", "section": "EXTENSION TO LONG-VIDEO GENERATION", "sec_num": "4.5"}, {"text": "In this paper, we present a training-free framework, namely ControlVideo, towards consistent and continuous controllable text-to-video generation. ControlVideo, inflated from ControlNet, introduces an interleaved-frame smoother to ensure video continuity. Particularly, interleaved-frame smoother alternately smooths out the latents of three-frame clips, and stabilizes each clip by updating the middle frame with the interpolation among other two frames in latent space. Moreover, we empirically demonstrate the superior performance of fully cross-frame interaction, while presenting hierarchical sampler for long-video generation in commodity GPUs. Quantitative and qualitative experiments on extensive motion-prompt pairs demonstrate that ControlVideo achieves state-of-the-arts in terms of frame consistency and video continuity.", "section": "DISCUSSION", "sec_num": "6"}, {"text": "Broader impact. Large-scale diffusion models have made tremendous progress in text-to-video synthesis, yet these models are costly and unavailable to the public. ControlVideo focuses on trainingfree controllable text-to-video generation, and takes an essential step in efficient video creation. Concretely, ControlVideo could synthesize high-quality videos with commodity hardware, hence, being accessible to most researchers and users. For example, artists may leverage our approach to create fascinating videos with less time. Moreover, ControlVideo provides insights into the tasks involved in videoss, e.g., video rendering, video editing, and video-to-video translation. On the flip side, albeit we do not intend to use our model for harmful purposes, it might be misused and bring some potential negative impacts, such as producing deceptive, harmful, or explicit videos. Despite the above concerns, we believe that they could be well minimized with some steps. For example, an NSFW filter can be employed to filter out unhealthy and violent content. Also, we hope that the government could establish and improve relevant regulations to restrict the abuse of video creation.", "section": "DISCUSSION", "sec_num": "6"}, {"text": "In Table 4 , we select 25 representative videos from DAVIS dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2017) and manually annotate their source captions. After that, we ask ChatGPT to generate five edited prompts for each source caption, following the instruction like: Please generate five new sentences that similar to \"A man dances on the road\", while being more diverse and highly detailed. Finally, we obtain 125 video-prompt pairs in total, and use them to evaluate both canny and depth conditioned generation.", "section": "A. DATASET DETAILS", "sec_num": null}, {"text": "We conduct a user study to compare ControlVideo against two other methods on 125 samples, and ask five raters to answer questions in each sample. In Fig. 7 , there are three questions involving in (i) video quality, (ii) temporal consistency, and (iii) text alignment. The raters are given unlimited time to make the selection. After collecting their answers, we take a majority vote as the final result for each sample, and present statistics in Table 2 .", "section": "B. USER STUDY DETAILS", "sec_num": null}, {"text": "During inference, we adopt DDIM sampling with T = 50 timesteps, which iteratively denoises a Gaussian noise from T to 0.", "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "Which timesteps does interleaved-frame smoother perform at? In Fig. 8 , we explore three timestep choices at different noise levels, including {48, 49} at large noise level, {30, 31} at middle noise level, and {0, 1} at little noise level. When using the smoother at timesteps {48, 49}, the processed video is still unstable, since structure sequences bring additional flickers at the following timesteps. At timesteps {0, 1} nearby image distribution, applying the interleaved-frame smoother leads to visible distortion in some frames. In contrast, performing smoothing operation at middle timesteps {30, 31} promisingly deflickers the video, while preserving the quality and individuality of interpolated frames.", "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "How many timesteps are used in interleaved-frame smoother? Fig. 9 shows the smoothed videos using interleaved-frame smoother at different numbers of timesteps. Applying the smoother at two consecutive timesteps (i.e., 2 steps) could smooth the entire video with little video quality degradation. As the number of smoothing steps increases, the processed video is much smoother, but some frames become slightly blurred. Thus, for higher quality and efficiency, we set the number of smoothing timesteps as 2 by default.", "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "Non-deterministic DDPM-style sampler. ControlVideo can also employ a non-deterministic DDPM-style sampler during inference. Following Eq.12 in DDIM (<PERSON> et al., 2020b) , one can predict z t-1 from z t via (i.e., line 10 of Alg. 1 in paper):", "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "EQUATION", "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "where ϵ t and σ t controls the level of random noise. DDPM results presents the generated videos of ControlVideo at different noise levels. Notably, as the noise level increases, ControlVideo generates more photo-realistic videos with dynamic details, e.g., ripples in the water. Fig. 10, Fig. 11, and Fig. 12 show more video visualizations conditioned on canny edges, depth maps, and human poses. Fig. 14, Fig. 15, and Fig. 16 present qualitative comparisons conditioned on canny edges, depth maps, and human poses. Fig. 13 provides an additional long video. More comparisons with video editing methods (<PERSON> et al., 2023; <PERSON> et al., 2023) are shown in this link.", "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "Firstly, Vid2Vid-Zero and FateZero are designed for video editing by a hybrid of fully and sparsecasual cross-frame attention, and does not investigate different attention mechanisms in depth. In contrast, our ControlVideo focuses on continuous controllable text-to-video generation, and first empirically investigate the superiority of fully cross-frame attention. Secondly, Fig. 18 shows their qualitative comparisons on video editing. As one can see, the edited videos of ControlVideo not only have more consistent structure with source videos, but also aligns better with text prompts. Which video has better temporal consistency across all frames? 3.", "section": "D. MORE VISUALIZATIONS AND COMPARISONS", "sec_num": null}, {"text": "A determined man is trudging up a snowy and icy mountain slope, braving the biting cold and fierce winds.", "section": "Which video aligns better with text prompt?", "sec_num": null}, {"text": "Figure 7 : The instruction of user study. A user study sample consists of a text prompt, structure sequence, and synthesized videos from two different methods (in random order). The raters are asked to answer the above three questions for each sample.", "section": "Text Prompt", "sec_num": null}, {"text": "While our ControlVideo enables consistent and high-quality video generation, it still struggles with producing videos beyond input motion sequences. For example, in Fig. 17 , given sequential poses of <PERSON>'s moonwalk, it is difficult to generate a vivid video according to text prompts like Iron man runs on the street. In this link, when input text prompts (e.g., rabbit) seriously conflict with input motion (e.g., ), the synthesized videos usually tend to align with input motion, ignoring the implicit structure in text prompts. To increase the ratio of text prompts over structure, we decrease the scale of ControlNet λ to 0.3 (λ = 1 by default). Therefore, it can be seen λ = 0.3 that achieves a better trade-off between two input conditions than λ = 1. In the future, we will explore how to adaptively modify input motions according to text prompts, so that users can create more vivid videos. As we increase the number of smoothing steps, the processed video becomes smoother, but some frames are slightly blurred. Therefore, we set the number of smoothing steps as two by default.", "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "Results best seen at 500% zoom.", "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "A white swan moving on the lake, cartoon style.", "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "A majestic camel gracefully strides across the scorching desert sands.", "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "A shiny red jeep smoothly turns on a narrow, winding road in the mountains.", "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "A dusty old jeep was making its way down the winding forest road, creaking and groaning with each bump.", "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "A fit man is leisurely hiking through a lush and verdant forest. ", "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "https://huggingface.co/lllyasviel/ControlNet", "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by National Key RD Program of China under Grant No. 2021ZD0112100, and the National Natural Science Foundation of China (NSFC) under Grant No. U19A2073.", "section": "ACKNOWLEDGEMENT", "sec_num": null}], "ref_entries": {"FIGREF0": {"text": "Figure 1: Training-free controllable text-to-video generation. Left: We visualize the frames and x-t slice (pixels in red line of original frame) of Text2Video-Zero, and observe visible discontinuity in x-t slice. Right: ControlVideo, adapted from ControlNet, achieves more continuous x-t slice across time, along with improved appearance consistency than Text2Video-Zero. See videos for better view.", "type_str": "figure", "fig_num": "1", "num": null, "uris": null}, "FIGREF1": {"text": "Figure 2: Overview of ControlVideo. For consistency in appearance, ControlVideo adapts ControlNet to the video counterpart by adding cross-frame interaction into self-attention modules. To further improve video continuity, interleaved-frame smoother is introduced to stabilize video latents during denosing (see Alg. 1 for details).", "type_str": "figure", "fig_num": "2", "num": null, "uris": null}, "FIGREF2": {"text": "Figure 3: Qualitative comparisons conditioned on depth maps and canny edges. Our Con-trolVideo produces videos with better (a) appearance consistency and (b) video quality than others. In contrast, Tune-A-Video fails to inherit structures from source videos, while Text2Video-Zero brings visible artifacts in large motion videos. See videos at qualitative comparisons.", "type_str": "figure", "fig_num": "3", "num": null, "uris": null}, "FIGREF3": {"text": "Fig.4further shows the comparison conditioned on human poses. From Fig.4, Tune-A-Video only maintains the coarse structures of the source video, i.e., human position. Text2Video-Zero and Follow-Your-Pose produce video frames with inconsistent appearance, e.g., changing faces of iron man (in row 4) or disappearing objects in the background (in row 5). In comparison, our ControlVideo performs more consistent video generation, demonstrating its superiority. More qualitative comparisons are provided in Appendix D.", "type_str": "figure", "fig_num": "4", "num": null, "uris": null}, "FIGREF4": {"text": "Figure 4: Qualitative comparisons on poses. Tune-A-Video only preserves original human positions, while Text2Video-Zero and Follow-Your-Pose produce frames with appearance incoherence. Our ControlVideo achieves better consistency in both structure and appearance. See videos at qualitative comparisons.", "type_str": "figure", "fig_num": "4", "num": null, "uris": null}, "FIGREF5": {"text": "Figure 5: Qualitative ablation studies on cross-frame mechanisms and interleaved-frame smoother. Fully cross-frame interaction produces video frames with higher quality and consistency than other mechanisms, and adding the smoother further enhances the video smoothness. See corresponding videos for better comparison.", "type_str": "figure", "fig_num": "5", "num": null, "uris": null}, "FIGREF6": {"text": "Figure 6: A long video produced with our hierarchical sampling. Motion sequences are shown on the top left. Using the efficient sampler, our ControlVideo generates a high-quality long video with the holistic consistency. See videos at long video generation.", "type_str": "figure", "fig_num": "6", "num": null, "uris": null}, "FIGREF7": {"text": "Figure8: Ablation on timestep choices in interleaved-frame smoother. We apply interleavedframe smoother at different timesteps, including {48, 49} at large noise level, {30, 31} at middle noise level, and {0, 1} at little noise level. Among them, using the smoother at timesteps {30, 31} promisingly mitigates the flicker effect while ensuring high quality. Results best seen at 500% zoom.", "type_str": "figure", "fig_num": "8", "num": null, "uris": null}, "FIGREF8": {"text": "Figure 9: Ablation on the number of timesteps used in interleaved-frame smoother. Applying the smoother at two consecutive timesteps (i.e., 2 steps) effectively reduces the flickers in structure.As we increase the number of smoothing steps, the processed video becomes smoother, but some frames are slightly blurred. Therefore, we set the number of smoothing steps as two by default.Results best seen at 500% zoom.", "type_str": "figure", "fig_num": "9", "num": null, "uris": null}, "FIGREF9": {"text": "Figure 10: More video visualizations conditioned on canny edges. Results best seen at 500% zoom.", "type_str": "figure", "fig_num": "10", "num": null, "uris": null}, "FIGREF10": {"text": "Figure 11: More video visualizations conditioned on depth maps. Results best seen at 500% zoom.", "type_str": "figure", "fig_num": "11", "num": null, "uris": null}, "TABREF2": {"text": "Quantitative comparisons of ControlVideo with other methods. We evaluate them on 125 motion-prompt pairs in terms of consistency, and the best results are bolded.the middle frame of each three-frame clip. The synthesized short videos are of length 15, while the long videos usually contain about 100 frames. Unless otherwise noted, their resolution is both 512 × 512. During sampling, we adopt DDIM sampling(<PERSON> et al., 2020a)  with 50 timesteps, and interleaved-frame smoother is performed on predicted RGB frames at timesteps {30, 31} by default. With the efficient implementation of xFormers(<PERSON><PERSON><PERSON><PERSON> et al., 2022), ControVideo could produce both short and long videos with one NVIDIA RTX 2080Ti in about 2 and 10 minutes, respectively. Datasets. To evaluate our ControlVideo, we collect 25 object-centric videos from DAVIS dataset(<PERSON><PERSON><PERSON><PERSON> et al., 2017) and manually annotate their source descriptions. Then, for each source description, ChatGPT (OpenAI, 2022) is utilized to generate five editing prompts automatically, resulting in 125 video-prompt pairs in total. Finally, we employ Canny and MiDaS DPT-Hybrid model", "type_str": "table", "content": "<table><tr><td colspan=\"4\">At each timestep, a long video z t = {z i t } N -1 i=0 is separated into multiple short video clips with the selected key frames z key t N = {z kNc t } Nc k=0 , where each clip is of length N c -1 and the kth clip is</td></tr><tr><td colspan=\"4\">denoted as z k t = {z j t } (k+1)Nc-1 j=kNc+1 . Then, we pre-generate the key frames with fully cross-frame</td></tr><tr><td colspan=\"3\">attention for long-range coherence, where reference frames are z key t</td><td>= {z kNc t</td><td>N Nc k=0 . Conditioned on }</td></tr><tr><td>each pair of key frames, i.e., reference frames as {z kNc t corresponding clip z k t holding the holistic consistency.</td><td>, z t (k+1)Nc</td><td colspan=\"2\">}, we sequentially synthesize their</td></tr><tr><td>4 EXPERIMENTS</td><td/><td/></tr></table>", "num": null, "html": null}, "TABREF4": {"text": "User preference study. The numbers denote the percentage of raters who favor the videos synthesized by our ControlVideo over other methods.", "type_str": "table", "content": "<table><tr><td>Method Comparison</td><td colspan=\"3\">Video Quality Temporal Consistency Text Alignment</td></tr><tr><td>Ours vs. Tune-A-<PERSON> et al. (2022b)</td><td>73.6%</td><td>83.2%</td><td>68.0%</td></tr><tr><td>Ours vs. Text2Video-Zero <PERSON> et al. (2023)</td><td>76.0%</td><td>81.6%</td><td>65.6%</td></tr><tr><td>Source</td><td/><td/><td/></tr><tr><td>Videos</td><td/><td/><td/></tr><tr><td>Structure</td><td/><td/><td/></tr><tr><td>Conditions</td><td/><td/><td/></tr><tr><td>Tune-A-Video</td><td/><td/><td/></tr><tr><td>Text2Video-Zero</td><td/><td/><td/></tr><tr><td>Follow-Your-Pose</td><td/><td/><td/></tr><tr><td>ControlVideo</td><td/><td/><td/></tr><tr><td>(Ours)</td><td/><td/><td/></tr></table>", "num": null, "html": null}, "TABREF5": {"text": "Quantitative ablation studies on cross-frame mechanisms and interleaved-frame smoother. The results indicate that our fully cross-frame mechanism achieves better frame consistency than other mechanisms, and the interleaved-frame smoother significantly improves the frame consistency.Cross-Frame Mechanism FC (×10 -2 ) PC (×10 -2 ) WE (×10 -2 ) Time Cost (min)", "type_str": "table", "content": "<table><tr><td>Individual</td><td/><td>89.94</td><td>30.79</td><td>20.13</td><td>1.2</td></tr><tr><td>First-only</td><td/><td>94.92</td><td>30.54</td><td>8.91</td><td>1.2</td></tr><tr><td colspan=\"2\">Sparse-Causal</td><td>95.06</td><td>30.59</td><td>7.05</td><td>1.5</td></tr><tr><td>Fully</td><td/><td>95.36</td><td>30.76</td><td>5.93</td><td>3.0</td></tr><tr><td colspan=\"2\">Fully + Smoother</td><td>96.83</td><td>30.79</td><td>2.75</td><td>3.5</td></tr><tr><td/><td>a</td><td/><td/><td/><td/></tr><tr><td>Frame 0</td><td>Frame 11</td><td>Frame 22</td><td>Frame 33</td><td>Frame 44</td><td>Frame 55</td></tr><tr><td>Frame 66</td><td>Frame 77</td><td>Frame 88</td><td>Frame 99</td><td>Frame 110</td><td>Frame 121</td></tr></table>", "num": null, "html": null}, "TABREF6": {"text": "", "type_str": "table", "content": "<table/>", "num": null, "html": null}, "TABREF7": {"text": "Names and captions of selected videos from DAVIS dataset.", "type_str": "table", "content": "<table><tr><td/><td>Video Name</td><td/><td>Source Caption</td></tr><tr><td/><td>blackswan</td><td colspan=\"2\">a black swan moving on the lake</td></tr><tr><td/><td>boat</td><td/><td>a boat moves in the river</td></tr><tr><td/><td>breakdance-flare</td><td/><td>a man dances on the road</td></tr><tr><td/><td>bus</td><td/><td>a bus moves on the street</td></tr><tr><td/><td>camel</td><td/><td>a camel walks on the desert</td></tr><tr><td/><td>car-roundabout</td><td/><td>a jeep turns on a road</td></tr><tr><td/><td>car-shadow</td><td/><td>a car moves to a building</td></tr><tr><td/><td>car-turn</td><td/><td>a jeep on a forest road</td></tr><tr><td/><td>cows</td><td/><td>a cow walks on the grass</td></tr><tr><td/><td>dog</td><td/><td>a dog walks on the ground</td></tr><tr><td/><td>elephant</td><td colspan=\"2\">an elephant walks on the ground</td></tr><tr><td/><td>flamingo</td><td colspan=\"2\">a flamingo wanders in the water</td></tr><tr><td/><td>gold-fish</td><td colspan=\"2\">golden fishers swim in the water</td></tr><tr><td/><td>hike</td><td/><td>a man hikes on a mountain</td></tr><tr><td/><td>hockey</td><td colspan=\"2\">a player is playing hockey on the ground</td></tr><tr><td/><td>kite-surf</td><td/><td>a man is surfing on the sea</td></tr><tr><td/><td>lab-coat</td><td colspan=\"2\">three women stands on the lawn</td></tr><tr><td/><td>longboard</td><td colspan=\"2\">a man is playing skateboard on the alley</td></tr><tr><td/><td>mallard-water</td><td/><td>a mallard swims on the water</td></tr><tr><td/><td>mbike-trick</td><td/><td>a man riding motorbike</td></tr><tr><td/><td>rhino</td><td/><td>a rhino walks on the rocks</td></tr><tr><td/><td>surf</td><td/><td>a sailing boat moves on the sea</td></tr><tr><td/><td>swing</td><td/><td>a girl is playing on the swings</td></tr><tr><td/><td>tennis</td><td/><td>a man is playing tennis</td></tr><tr><td/><td>walking</td><td/><td>a selfie of walking man</td></tr><tr><td/><td colspan=\"2\">Structure</td><td>Video 1</td><td>Video 2</td></tr><tr><td/><td colspan=\"2\">Sequence</td></tr><tr><td colspan=\"2\">Between Method 1 &amp; 2 :</td><td/></tr><tr><td>1.</td><td colspan=\"2\">Which video has higher quality ?</td></tr><tr><td>2.</td><td/><td/></tr></table>", "num": null, "html": null}}}}