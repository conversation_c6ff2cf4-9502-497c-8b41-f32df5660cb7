{"paper_id": "WassersteinSSL", "title": "RETHINKING THE UNIFORMITY METRIC IN SELF-SUPERVISED LEARNING", "abstract": "Uniformity plays an important role in evaluating learned representations, providing insights into self-supervised learning. In our quest for effective uniformity metrics, we pinpoint four principled properties that such metrics should possess. Namely, an effective uniformity metric should remain invariant to instance permutations and sample replications while accurately capturing feature redundancy and dimensional collapse. Surprisingly, we find that the uniformity metric proposed by Wang & Isola (2020) fails to satisfy the majority of these properties. Specifically, their metric is sensitive to sample replications, and can not account for feature redundancy and dimensional collapse correctly. To overcome these limitations, we introduce a new uniformity metric based on the Wasserstein distance, which satisfies all the aforementioned properties. Integrating this new metric in existing self-supervised learning methods effectively mitigates dimensional collapse and consistently improves their performance on downstream tasks involving CIFAR-10 and CIFAR-100 datasets. Code is available at https://github.com/statsle/WassersteinSSL.", "pdf_parse": {"paper_id": "WassersteinSSL", "abstract": [{"text": "Uniformity plays an important role in evaluating learned representations, providing insights into self-supervised learning. In our quest for effective uniformity metrics, we pinpoint four principled properties that such metrics should possess. Namely, an effective uniformity metric should remain invariant to instance permutations and sample replications while accurately capturing feature redundancy and dimensional collapse. Surprisingly, we find that the uniformity metric proposed by Wang & Isola (2020) fails to satisfy the majority of these properties. Specifically, their metric is sensitive to sample replications, and can not account for feature redundancy and dimensional collapse correctly. To overcome these limitations, we introduce a new uniformity metric based on the Wasserstein distance, which satisfies all the aforementioned properties. Integrating this new metric in existing self-supervised learning methods effectively mitigates dimensional collapse and consistently improves their performance on downstream tasks involving CIFAR-10 and CIFAR-100 datasets. Code is available at https://github.com/statsle/WassersteinSSL.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Self-supervised learning excels in acquiring invariant representations to various augmentations (<PERSON> et al., 2020; <PERSON> et al., 2020; <PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2021) . It has been outstandingly successful across a wide range of domains, such as multimodality learning, object detection, and segmentation (<PERSON><PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON> et al., 2021) . To gain a deeper understanding of self-supervised learning, thoroughly evaluating the learned representations is necessary (<PERSON> & <PERSON>, 2020; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2021; <PERSON> et al., 2022) .", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Dimensional Collapse Alignment, a metric quantifying the similarities between positive pairs, holds significant importance in the evaluation of learned representations (<PERSON> & <PERSON>ola, 2020) . It ensures that positive pairs are mapped to similar features, making them invariant to unnecessary details (<PERSON><PERSON><PERSON> et al., 2006; <PERSON> et al., 2020) . However, relying solely on alignment proves inadequate for effectively assessing the representations. This limitation becomes evident in the presence of extremely small alignment values in collapsing solutions, as observed in Siamese networks (<PERSON><PERSON><PERSON> et al., 2006) , where all outputs collapse to a single point (<PERSON> & <PERSON>, 2021) , as illustrated in Figure 1 . In such cases, the learned representations exhibit optimal alignment but fail to provide meaningful information for any downstream tasks. This underscores the necessity of incorporating additional metrics when evaluating learned representations.", "section": "Constant Collapse", "sec_num": null}, {"text": "To further evaluate the learned representations, <PERSON> & <PERSON> (2020) formally introduced a uniformity metric based on the logarithm of the average pairwise Gaussian potential (<PERSON><PERSON> & <PERSON>, 2007) . Uniformity assesses how feature embeddings are distributed uniformly across the unit hypersphere, and higher uniformity indicates more information from the data is preserved. Since its introduction, uniformity has played a pivotal role in understanding self-supervised learning and mitigating constant collapse (<PERSON><PERSON><PERSON> et al., 2019; Wang & Isola, 2020; <PERSON> et al., 2021) . Nevertheless, the effectiveness of this particular uniformity metric warrants further examination.", "section": "Constant Collapse", "sec_num": null}, {"text": "To delve deeper into the existing uniformity metric proposed by <PERSON> (2020) , we introduce four principled properties that an effective uniformity metric should possess. Guided by these properties, we conduct a theoretical analysis, unveiling key limitations of this metric, particularly its inability to capture feature redundancy and dimensional collapse (<PERSON><PERSON> et al., 2021) . Dimensional collapse refers to the scenario where representations occupy a lower-dimensional subspace rather than the entire embedding space (<PERSON> et al., 2022) ; see Figure 1 . We reinforce our theoretical findings with empirical evidence, demonstrating, for instance, the existing metric's inability to differentiate between different degrees of dimensional collapse. Subsequently, we propose a novel uniformity metric based on the quadratic Wasserstein distance that satisfies all four properties, thereby surpassing the existing one. Finally, integrating the proposed uniformity metric as an auxiliary loss within existing self-supervised learning methods consistently enhances their performance in downstream tasks.", "section": "Constant Collapse", "sec_num": null}, {"text": "Our main contributions are summarized as follows. (i) We identify four principled properties that an effective uniformity metric should possess, providing new guidelines on designing such metrics.", "section": "Constant Collapse", "sec_num": null}, {"text": "(ii) Surprisingly, we find that the existing uniformity metric (Wang & Isola, 2020) fails to meet the majority of these properties. For example, it can not correctly capture dimensional collapse. (iii) We propose a new uniformity metric based on the <PERSON><PERSON><PERSON> distance that satisfies all four properties, addressing key limitations of the existing metric. (iv) Our proposed uniformity metric can seamlessly integrate as an auxiliary loss in various self-supervised learning methods, resulting in improved performance in downstream tasks.", "section": "Constant Collapse", "sec_num": null}, {"text": "2.1 SELF-SUPERVISED REPRESENTATION LEARNING Self-supervised learning leverages the idea that similar samples should have similar representations that are invariant to unnecessary details (Wang & <PERSON>, 2020) . For instance, the Siamese network (<PERSON><PERSON><PERSON> et al., 2006) takes as input positive pairs (x a , x b ), often obtained by taking two augmented views of the same sample x. These positive pairs are then processed by an encoder network f consisting of a backbone (e.g., ResNet (<PERSON> et al., 2016) ) and a projection MLP head (<PERSON> et al., 2020) , yielding representations", "section": "BACKGROUND", "sec_num": "2"}, {"text": "(z a = f (x a ), z b = f (x b ) 1 .", "section": "BACKGROUND", "sec_num": "2"}, {"text": "To enforce invariance, a natural approach is to minimize the following alignment loss, defined as the expected distance between positive pairs:", "section": "BACKGROUND", "sec_num": "2"}, {"text": "EQUATION", "section": "BACKGROUND", "sec_num": "2"}, {"text": "where p pos (•, •) is the distribution of positive pairs. However, optimizing the above alignment loss alone may lead to an undesired collapsing solution, where all representations collapse into a single point, as shown in Figure 1 .", "section": "BACKGROUND", "sec_num": "2"}, {"text": "To prevent constant collapse, existing solutions include contrastive learning, asymmetric model architecture, and redundancy reduction.", "section": "EXISTING SOLUTIONS TO CONSTANT COLLAPSE", "sec_num": "2.2"}, {"text": "Contrastive Learning Contrastive learning offers a potent solution to mitigate constant collapse.", "section": "EXISTING SOLUTIONS TO CONSTANT COLLAPSE", "sec_num": "2.2"}, {"text": "The key idea is to leverage negative pairs. For example, SimCLR (<PERSON> et al., 2020) introduced an in-batch negative sampling strategy that utilizes samples within a batch as negative samples. However, its effectiveness is contingent on the use of a large batch size. To address this limitation, MoCo (<PERSON> et al., 2020 ) used a memory bank, which stores additional representations as negative samples. Recent research endeavors have also explored clustering-based contrastive learning, which combines a clustering objective with contrastive learning techniques (<PERSON> et al., 2021; <PERSON><PERSON> et al., 2020) .", "section": "EXISTING SOLUTIONS TO CONSTANT COLLAPSE", "sec_num": "2.2"}, {"text": "Asymmetric Model Architecture The use of asymmetric model architecture represents another strategy to combat constant collapse. One plausible explanation for its effectiveness is that such an asymmetric design encourages encoding more information (<PERSON><PERSON> et al., 2020) . To maintain this asymmetry, BYOL (<PERSON><PERSON> et al., 2020) introduces the concept of using an additional predictor in one branch of the Siamese network while employing momentum updates and stop-gradient operators in the other branch. DINO (<PERSON><PERSON> et al., 2021) , takes this asymmetry a step further by applying it to two encoders, distilling knowledge from the momentum encoder into the other one (<PERSON><PERSON> et al., 2015) . <PERSON><PERSON><PERSON><PERSON> (Chen & He, 2021) removes the momentum update from BYOL, and shows that the momentum update may not be essential in preventing constant collapse. However, Mirror<PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON> et al., 2022a) swaps the stop-gradient operator to the other branch. Its failure challenges the assertion made in <PERSON><PERSON><PERSON><PERSON> (Chen & He, 2021 ) that the stop-gradient operator is the key component for preventing constant collapse. <PERSON><PERSON> et al. (2021) provides a theoretical examination to elucidate why an asymmetric model architecture can effectively avoid constant collapse.", "section": "EXISTING SOLUTIONS TO CONSTANT COLLAPSE", "sec_num": "2.2"}, {"text": "The fundamental principle behind redundancy reduction to mitigate constant collapse is to maximize the information preserved by the representations. The key idea is to decorrelate the learned representations. <PERSON> (<PERSON><PERSON><PERSON> et al., 2021) aims to achieve decorrelation by focusing on the cross-correlation matrix, while VICReg (<PERSON><PERSON> et al., 2022) focuses on the covariance matrix. Zero-CL (<PERSON> et al., 2022b ) takes a hybrid approach, combining instance-wise and feature-wise whitening techniques.", "section": "Redundancy Reduction", "sec_num": null}, {"text": "While the aforementioned solutions effectively prevent constant collapse, they are not as effective in preventing dimensional collapse, wherein representations occupy a lower-dimensional subspace instead of the entire space. This phenomenon has been observed in contrastive learning by visualizing the singular value spectra of representations (<PERSON> et al., 2022; <PERSON><PERSON> et al., 2021) .", "section": "THE EXISTING UNIFORMITY METRIC", "sec_num": "2.3"}, {"text": "To quantitatively measure the degree of collapse, <PERSON> & <PERSON> (2020) introduced a uniformity loss based on the logarithm of the average pairwise Gaussian potential. Given (normalized) feature representations {z 1 , z 2 , ..., z n }, their proposed empirical uniformity loss is:", "section": "THE EXISTING UNIFORMITY METRIC", "sec_num": "2.3"}, {"text": "EQUATION", "section": "THE EXISTING UNIFORMITY METRIC", "sec_num": "2.3"}, {"text": "where t > 0 is a fixed parameter, often set to 2. Then -L U serves as the corresponding uniformity metric, with a higher value indicating greater uniformity.", "section": "THE EXISTING UNIFORMITY METRIC", "sec_num": "2.3"}, {"text": "We demonstrate in this work that this metric is insensitive to dimensional collapse, both theoretically in Section 3.2 and empirically in Section 5.2.", "section": "THE EXISTING UNIFORMITY METRIC", "sec_num": "2.3"}, {"text": "In this section, we begin by presenting four fundamental properties that an effective uniformity metric should possess. Leveraging these properties as a lens, we then scrutinize the existing uniformity metric -L U , shedding light on its limitations.", "section": "WHAT MAKES AN EFFECTIVE UNIFORMITY METRIC?", "sec_num": "3"}, {"text": "A uniformity metric U : R mn → R is a function that maps a set of learned representations to a scalar indicator of uniformity. In the following section, we introduce four principled properties that an effective uniformity metric should possess. Let D = z 1 , . . . , z n ∈ R mn represent the learned representations. To avoid the trivial case, we assume that z 1 , . . . , z n are not all equal, meaning that not all points collapse to a single constant point.", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "First, an effective uniformity metric should be invariant to the permutation of instances, as the distribution of representations should not be affected by permutations.", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "Property 1 (Instance Permutation Constraint (IPC)). An effective uniformity metric U should satisfy", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "EQUATION", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "where π is a permutation over the instances.", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "Second, an effective uniformity metric should be invariant to instance clones, as instance cloning does not vary the distribution of representations.", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "Property 2 (Instance Cloning Constraint (ICC)). An effective uniformity metric U should satisfy", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "EQUATION", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "where", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "D ⊎ D := {z 1 , z 2 , ..., z n , z 1 , z 2 , ..., z n }.", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "Third, an effective uniformity metric should strictly decrease as feature-level cloning for each instance occurs, as this duplication introduces redundancy, which corresponds to dimensional collapse (<PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON> et al., 2022) .", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "Property 3 (Feature Cloning Constraint (FCC)). An effective uniformity metric U should satisfy", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "EQUATION", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "where", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "D⊕D := {z 1 ⊕z 1 , z 2 ⊕z 2 , ..., z n ⊕z n } and z i ⊕z i := (z i1 , • • • , z im , z i1 , • • • , z im ) T ∈ R 2m .", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "Fourth, an effective uniformity metric should strictly decrease with the addition of constant features for each instance, as this introduces uninformative and thus redundant features, which again corresponds to dimensional collapse.", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "Property 4 (Feature Baby Constraint (FBC)). An effective uniformity metric U should satisfy", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "EQUATION", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": ")", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "where ⊕ is defined in Property 3, that is,", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "D ⊕ 0 k = {z 1 ⊕ 0 k , z 2 ⊕ 0 k , ..., z n ⊕ 0 k } and z i ⊕ 0 k = (z i1 , z i2 , ..., z im , 0, 0, ..., 0) T ∈ R m+k .", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "Intuitively, Properties 1 and 2 ensure that the uniformity metric should remain insensitive to instance permutations and sample replications, respectively. Meanwhile, Properties 3 and 4 ensure that feature redundancy and dimensional collapse reduce the uniformity metric, as they make the distribution of the representations less uniform. These four properties constitute intuitive yet principled characteristics of an effective uniformity metric.", "section": "FOUR PROPERTIES FOR UNIFORMITY", "sec_num": "3.1"}, {"text": "We employ the four properties introduced earlier to analyze the uniformity metric -L U defined in Eqn.", "section": "EXAMINING THE UNIFORMITY METRIC -L U", "sec_num": "3.2"}, {"text": "(2). The following theorem summarizes our findings.", "section": "EXAMINING THE UNIFORMITY METRIC -L U", "sec_num": "3.2"}, {"text": "Theorem 1. The uniformity metric -L U satisfies Property 1, but violates Properties 2, 3, and 4.", "section": "EXAMINING THE UNIFORMITY METRIC -L U", "sec_num": "3.2"}, {"text": "The proof of the above theorem is provided in Appendix C. The violation of Property 2 indicates that the uniformity metric -L U is sensitive to sample replications, while the violations of Properties 3 and 4 suggest that feature redundancy and dimensional collapse do not reduce the uniformity metric -L U , making this uniformity metric unable to correctly reflect feature redundancy and dimensional collapse. Therefore, there is a pressing need to develop a new uniformity metric. ", "section": "EXAMINING THE UNIFORMITY METRIC -L U", "sec_num": "3.2"}, {"text": "As pointed out by (<PERSON> & <PERSON>, 2020) , feature vectors should be roughly uniformly distributed on the unit hypersphere S m-1 , preserving as much information of the data as possible. Therefore, we adopt the uniform spherical distribution as our target distribution.", "section": "THE UNIFORM SPHERICAL DISTRIBUTION AND AN APPROXIMATION", "sec_num": "4.1"}, {"text": "Our approach utilizes the quadratic <PERSON><PERSON>stein distance, a form of statistical distance, between the feature distribution and the target distribution as the new uniformity loss. However, computing any statistical distances involving the uniform spherical distribution can be challenging. To address this, we first establish an asymptotic equivalence between the uniform spherical distribution and the isotropic Gaussian distribution. By adopting a Gaussian distribution for the representations, we then exploit the fact that the quadratic <PERSON>serstein distance between two Gaussian distributions has a closed form involving only the means and covariance matrices, leading to a new and simple uniformity loss. We need the following fact.", "section": "THE UNIFORM SPHERICAL DISTRIBUTION AND AN APPROXIMATION", "sec_num": "4.1"}, {"text": "Fact 1. If Z ∼ N (0, σ 2 I m ), then Y := Z/∥Z∥ 2 is uniformly distributed on the unit hypersphere S m-1 . Because the average length of ∥Z∥ 2 is roughly σ √ m (<PERSON><PERSON><PERSON><PERSON> et al., 2012), that is, m √ m + 1 ≤ ∥Z∥ 2 /σ ≤ √ m,", "section": "THE UNIFORM SPHERICAL DISTRIBUTION AND AN APPROXIMATION", "sec_num": "4.1"}, {"text": "we expect that Z/(σ √ m) ∼ N (0, I m /m) provides a reasonable approximation to Z/∥Z∥ 2 , and thus to the uniform spherical distribution. This is partially justified by the following theorem.", "section": "THE UNIFORM SPHERICAL DISTRIBUTION AND AN APPROXIMATION", "sec_num": "4.1"}, {"text": "Theorem 2. Let Y i be the i-th coordinate of Y = Z/∥Z∥ 2 ∈ R m , where Z ∼ N (0, σ 2 I m ). Then the quadratic <PERSON><PERSON><PERSON> distance between Y i and Y i ∼ N (0, 1/m) converges to zero as m → ∞, that is, lim m→∞ W 2 (Y i , Y i ) = 0.", "section": "THE UNIFORM SPHERICAL DISTRIBUTION AND AN APPROXIMATION", "sec_num": "4.1"}, {"text": "Theorem 2 suggests that N (0, I m /m) approximates the distribution of each coordinate of the uniform spherical distribution as m → ∞. It can be proven by first employing the Talagrand T 2 inequality (<PERSON>, 2016) to upper bound the quadratic <PERSON><PERSON><PERSON> distance using the Kullback-Leibler (KL) divergence, and then establishing that the Kullback-Leibler (KL) divergence converges to 0. The proof is provided in Appendix B.", "section": "THE UNIFORM SPHERICAL DISTRIBUTION AND AN APPROXIMATION", "sec_num": "4.1"}, {"text": "We empirically compare the distributions of Y i and Y i across various dimensions m ∈ 2, 4, 8, 16, 32, 64, 128, 256. For each m, we sample 200,000 data points from both Y i and Y i , bin them into 51 groups, and calculate the empirical KL divergence and <PERSON><PERSON><PERSON> distance. Figure 2 plots both distances versus increasing dimensions. We observe that both distances converge to 0 as m increases. Specifically, these results indicate that the distribution of Y i provides a reasonable approximation to that of Y i when m ≥ 2 4 = 16. Further comparisons between Y and Y can be found in Appendix D.", "section": "THE UNIFORM SPHERICAL DISTRIBUTION AND AN APPROXIMATION", "sec_num": "4.1"}, {"text": "In this section, we discuss how to use the quadratic <PERSON><PERSON><PERSON> distance between the distribution of learned representations and N (0, I m /m), in place of the uniform spherical distribution Unif(S m-1 ), as our new uniformity loss.", "section": "A NEW METRIC FOR UNIFORMITY", "sec_num": "4.2"}, {"text": "To facilitate computation, we adopt a Gaussian hypothesis for the learned representations and assume they follow N (µ, Σ). With this assumption, we employ the quadratic <PERSON><PERSON>stein distance2 to measure the distance between two distributions. We need the following well-known lemma (<PERSON><PERSON><PERSON> & <PERSON>, 1982) . Lemma 1. Then the quadratic <PERSON>serstein distance between N (µ, Σ) and N (0, I/m) is", "section": "A NEW METRIC FOR UNIFORMITY", "sec_num": "4.2"}, {"text": "EQUATION", "section": "A NEW METRIC FOR UNIFORMITY", "sec_num": "4.2"}, {"text": "The lemma above indicates that the quadratic <PERSON><PERSON><PERSON> distance can be easily computed using the population mean and covariance of the representations. In practice, we estimate the population mean and covariance by using the sample mean µ and covariance matrix Σ, respectively. Specifically, the empirical quadratic Wasserstein distance serves as the new empirical uniformity loss:", "section": "A NEW METRIC FOR UNIFORMITY", "sec_num": "4.2"}, {"text": "EQUATION", "section": "A NEW METRIC FOR UNIFORMITY", "sec_num": "4.2"}, {"text": "Thus, -W 2 can be utilized as the new uniformity metric, with larger values indicating greater uniformity. Moreover, our new uniformity loss can be seamlessly integrated into various existing self-supervised learning methods to enhance their performance.", "section": "A NEW METRIC FOR UNIFORMITY", "sec_num": "4.2"}, {"text": "We examine the proposed metric -W 2 in terms of the four properties introduced earlier. The following theorem summarizes our findings. Theorem 3. The uniformity metric -W 2 satisfies all four properties, that is, Properties 1-4.", "section": "THEORETICAL COMPARISON", "sec_num": "5.1"}, {"text": "The proof of the above theorem is similar to that of Theorem 1, and is provided in Appendix C.2. Taking dimensional collapse as an example, we consider D ⊕ 0 k versus D. Here, a larger k indicates a more severe dimensional collapse. However, -L U fails to identify this issue, as", "section": "THEORETICAL COMPARISON", "sec_num": "5.1"}, {"text": "-L U (D ⊕ 0 k ) = -L U (D).", "section": "THEORETICAL COMPARISON", "sec_num": "5.1"}, {"text": "In stark contrast, our proposed metric can accurately detect this dimensional collapse, as", "section": "THEORETICAL COMPARISON", "sec_num": "5.1"}, {"text": "-W 2 (D ⊕ 0 k ) < -W 2 (D).", "section": "THEORETICAL COMPARISON", "sec_num": "5.1"}, {"text": "We perform synthetic experiments to investigate the two uniformity metrics. An empirical examination of the correlation between these metrics shows that data points following an isotropic Gaussian distribution exhibit better uniformity compared to those from other distributions; see Appendix E for detailed results. Additionally, we generate data vectors from this distribution to enable a thorough comparison between the two metrics.", "section": "EMPIRICAL COMPARISONS VIA SYNTHETIC STUDIES", "sec_num": "5.2"}, {"text": "On Dimensional Collapse Degrees To generate data reflecting varying degrees of dimensional collapse, we sample data vectors from an isotropic Gaussian distribution, normalize them to have ℓ 2 norms3 , and then zero out a proportion of the coordinates. As the proportion of zero-value coordinates, denoted by η, increases, dimensional collapse becomes more pronounced, while the proportion of On Sensitiveness of Dimensions Figure 4 demonstrates that -L U can not distinguish between different degrees of dimensional collapse (η = 25%, 50%, and 75%) as the dimension m increases (e.g., m ≥ 2 8 = 256). In contrast, -W 2 only depends on the degree of dimensional collapse and is independent of the dimensions m.", "section": "EMPIRICAL COMPARISONS VIA SYNTHETIC STUDIES", "sec_num": "5.2"}, {"text": "To complement the theoretical comparisons between the two metrics discussed in Section 5.1, we also conduct empirical comparisons in terms of FCC and FBC. ICC comparisons are collected in Appendix E.", "section": "EMPIRICAL COMPARISONS VIA SYNTHETIC STUDIES", "sec_num": "5.2"}, {"text": "On Feature Cloning Constraint We investigate the impact of feature cloning by creating multiple feature clones of the dataset, such as D ⊕ D and D ⊕ D ⊕ D, corresponding to one and two times cloning, respectively. Figure 5 (a) demonstrates that the value of -L U increases as the number of clones increases, which violates the strict decline in Eqn. (5). In contrast, in Figure 5 (b), our proposed metric -W 2 decreases, satisfying the property.", "section": "EMPIRICAL COMPARISONS VIA SYNTHETIC STUDIES", "sec_num": "5.2"}, {"text": "On Feature Baby Constraint We proceed to analyze the effect of feature baby, where we insert k dimensional zero vectors into each instance of D. This modified dataset is denoted as D ⊕ 0 k , and we examine the impact of k on both metrics. Figure 6 (a) shows that the value of -L U remains constant as k increases, violating the strict inequality constraint in Eqn. (6). In contrast, Figure 6 (b) shows that our proposed metric -W 2 decreases, satisfying the constraint.", "section": "EMPIRICAL COMPARISONS VIA SYNTHETIC STUDIES", "sec_num": "5.2"}, {"text": "In summary, our empirical results corroborate our theoretical analysis, confirming that our proposed metric -W 2 outperforms the existing metric -L U in capturing feature redundancy and dimensional collapse. ", "section": "Summary of Synthetic Studies", "sec_num": null}, {"text": "In this section, we integrate the proposed uniformity loss as an auxiliary term into various existing self-supervised methods. We then conduct experiments on CIFAR-10 and CIFAR-100 datasets to demonstrate its effectiveness.", "section": "EXPERIMENTS", "sec_num": "6"}, {"text": "We conduct experiments on a series of self-supervised representation learning models: (i) AlignUniform (Wang & Isola, 2020) , which incorporates both alignment and uniformity losses in its objective function; (ii) three contrastive learning methods, namely SimCLR (<PERSON> et al., 2020) , MoCo (<PERSON> et al., 2020) , and NNCLR (<PERSON><PERSON><PERSON><PERSON> et al., 2021) ; (iii) two asymmetric models, BYOL (<PERSON><PERSON> et al., 2020) and SimSiam (Chen & He, 2021); (iv) two methods based on redundancy reduction, BarlowTwins (Zbon<PERSON> et al., 2021) and Zero-CL (<PERSON> et al., 2022b) . To investigate the behavior of the proposed Wasserstein uniformity loss in self-supervised learning, we integrate it as an auxiliary loss into the following models: MoCo v2, BYOL, BarlowTwins, and Zero-CL. Additionally, we propose using linear decay to weight the Wasserstein uniformity loss during training. This is achieved by setting α t = α max -t, (α max -α min )/T , where t, T , α max , α min , and α t represent the current epoch, maximum epochs, maximum weight, minimum weight, and current weight, respectively. Further details on the experimental settings can be found in Appendix F.1.", "section": "Models", "sec_num": null}, {"text": "We assess the aforementioned methods using two distinct criteria: accuracy and representation quality/capacity. Accuracy is gauged through linear evaluation accuracy, quantified by Top-1 accuracy (Acc@1) and Top-5 accuracy (Acc@5). On the other hand, representation quality/capacity is evaluated using the uniformity losses L U and W 2 , along with the alignment loss L A . .", "section": "Accuracy and representation capacity", "sec_num": null}, {"text": "As depicted in Table 2 , incorporating W 2 as an additional loss consistently yields superior performance compared to models without this loss or those with L U as the additional term. Intriguingly, although it marginally compromises alignment, it enhances uniformity and accuracy in downstream tasks. This underscores the effectiveness of W 2 as a uniformity loss. Notably, integrating the Wasserstein uniformity loss does not impede training or inference efficiency.", "section": "Main Results", "sec_num": null}, {"text": "We evaluate the Top-1 accuracy of these models on CIFAR-10 and CIFAR-100 using the linear evaluation protocol, as described in Appendix F.2, across different training Dimensional Collapse Analysis We visualize the singular value spectra of the learned representations (<PERSON> et al., 2022) for various models. These spectra contain the singular values of the covariance matrix of representations from CIFAR-100 dataset, sorted in logarithmic scale order. As shown in Figure 7 (a), most singular values collapse to zeros in most models, indicating a large number of collapsed coordinates in most models. To further understand how the additional loss W 2 helps prevent dimensional collapse, we add W 2 as an additional loss for Moco v2 and BYOL, the numbers of collapsed coordinates decrease to zeros in both cases; see Figure 7 (b) and Figure 7(c ). This verifies that our proposed uniformity loss W 2 can effectively address the dimensional collapse issue for Moco v2 and BYOL. In contrast, L U can not effectively prevent dimensional collapse.", "section": "Convergence Analysis", "sec_num": null}, {"text": "In this paper, we have identified four principled properties that an effective uniformity metric should possess. Namely, an effective uniformity metric should remain invariant to instance permutations and sample replications while accurately capturing feature redundancy and dimensional collapse. Surprisingly, the popular uniformity metric proposed by Wang & Isola (2020) fails to meet the majority of these properties, unveiling its limitations. Empirical investigations corroborate our theoretical findings. To overcome these limitations, we introduce a new uniformity metric that satisfies all four properties. Particularly, this new metric demonstrates remarkable abilities to capture feature redundancy and dimensional collapse. Integrating it as an auxiliary loss in various selfsupervised learning methods effectively mitigates dimensional collapse and consistently improves their performance on downstream tasks. Nonetheless, it is worth noting that the four identified properties may not encompass a comprehensive characterization of an ideal uniformity metric, warranting further exploration. ", "section": "CONCLUSION", "sec_num": "7"}, {"text": "We first introduce the Wasserstein distance or the earth mover distance. Definition 1. The Wasserstein distance or earth-mover distance with p norm is defined as below:", "section": "A STATISTICAL DISTANCES OVER GAUSSIAN DISTRIBUTIONS", "sec_num": null}, {"text": "EQUATION", "section": "A STATISTICAL DISTANCES OVER GAUSSIAN DISTRIBUTIONS", "sec_num": null}, {"text": ")", "section": "A STATISTICAL DISTANCES OVER GAUSSIAN DISTRIBUTIONS", "sec_num": null}, {"text": "where Π(P r , P g ) denotes the set of all joint distributions γ(x, y) whose marginals are respectively P r and P g . Intuitively, when viewing each distribution as a unit amount of earth/soil, the Wasserstein distance or earth-mover distance takes the minimum cost of transporting \"mass\" from x to y to transform the distribution P r into the distribution P g . This distance is also called the quadratic <PERSON>serstein distance when p = 2.", "section": "A STATISTICAL DISTANCES OVER GAUSSIAN DISTRIBUTIONS", "sec_num": null}, {"text": "In this paper, we mainly exploit the quadratic <PERSON><PERSON> distance over Gaussian distributions.", "section": "A STATISTICAL DISTANCES OVER GAUSSIAN DISTRIBUTIONS", "sec_num": null}, {"text": "Besides this distance, we also discuss other distribution distances as uniformity metrics and make comparisons with the <PERSON><PERSON><PERSON> distance. Specifically, the <PERSON><PERSON>back-Le<PERSON>r divergence and the Bhat<PERSON><PERSON><PERSON><PERSON> distance over Gaussian distributions are provided in Lemma 2 and Lemma 3 respectively. Both distances require full-rank covariance matrices, making them impropriate to conduct dimensional collapse analysis. In contrast, our quadratic Wasserstein distance-based uniformity metric is free of such a requirement. Lemma 2 (Kullback-Leibler divergence (<PERSON><PERSON> & <PERSON>, 1959) ). Suppose two random variables Z 1 ∼ N (µ 1 , Σ 1 ) and Z 2 ∼ N (µ 2 , Σ 2 ) obey multivariate normal distributions, then <PERSON><PERSON>back-Leibler divergence between Z1 and Z 2 is:", "section": "A STATISTICAL DISTANCES OVER GAUSSIAN DISTRIBUTIONS", "sec_num": null}, {"text": "D KL (Z 1 , Z 2 ) = 1 2 ((µ 1 -µ 2 ) T Σ -1 2 (µ 1 -µ 2 ) + tr(Σ -1 2 Σ 1 -I) + ln det Σ 2 det Σ 1 ).", "section": "A STATISTICAL DISTANCES OVER GAUSSIAN DISTRIBUTIONS", "sec_num": null}, {"text": "Lemma 3 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Distance (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 1943) ). Suppose two random variables Z 1 ∼ N (µ 1 , Σ 1 ) and Z 2 ∼ N (µ 2 , Σ 2 ) obey multivariate normal distributions, Σ = 1 2 (Σ 1 + Σ 2 ), then b<PERSON><PERSON><PERSON><PERSON><PERSON> distance between Z1 and Z 2 is:", "section": "A STATISTICAL DISTANCES OVER GAUSSIAN DISTRIBUTIONS", "sec_num": null}, {"text": "D B (Z 1 , Z 2 ) = 1 8 (µ 1 -µ 2 ) T Σ -1 (µ 1 -µ 2 ) + 1 2 ln det Σ √ det Σ 1 det Σ 2 .", "section": "A STATISTICAL DISTANCES OVER GAUSSIAN DISTRIBUTIONS", "sec_num": null}, {"text": "We first need the following lemma, whose proof is collected in the end of this section.", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "Lemma 4. Let Z ∼ N (0, σ 2 I m ) and Y = Z/∥Z∥ 2 . Then the probability density function of Y i , the i-th coordinate of Y is:", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "f Yi (y i ) = Γ(m/2) √ πΓ((m -1)/2) (1 -y 2 i ) (m-3)/2 , ∀ y i ∈ [-1, 1].", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "We are ready to prove Theorem 2.", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "Proof of Theorem 2. According to the Lemma 4, the pdf of Y i and Y i are:", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "f Yi (y) = Γ(m/2) √ πΓ((m -1)/2) (1 -y 2 ) (m-3)/2 , f Yi (y) = m 2π exp{- my 2 2 }.", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "Then the <PERSON><PERSON>back-Le<PERSON> divergence between Y i and Y i is", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "D KL (Y i ∥ Y i ) = 1 -1 f Yi (y)[log f Yi (y) -log f Yi (y)]dy = 1 -1 f Yi (y)[log Γ(m/2) √ πΓ((m -1)/2) + m -3 2 log(1 -y 2 ) -log m 2π + my 2 2 ]dy = log 2 m Γ(m/2) Γ((m -1)/2) + 1 -1 f Yi (y)[ m -3 2 log(1 -y 2 ) + my 2 2 ]dy.", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "Letting µ = y 2 , we have y = √ µ and dy = 1 2 µ -1 2 du. Thus,", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "A := 1 -1 f Yi (y)[ m -3 2 log(1 -y 2 ) + my 2 2 ]dy = 2 1 0 Γ(m/2) √ πΓ((m -1)/2) (1 -y 2 ) m-3 2 [ m -3 2 log(1 -y 2 ) + my 2 2 ]dy = Γ(m/2) √ πΓ((m -1)/2) 1 0 (1 -µ) m-3 2 [ m -3 2 log(1 -µ) + m 2 µ]µ -1 2 dµ = Γ(m/2) √ πΓ((m -1)/2) m -3 2 1 0 (1 -µ) m-3 2 µ -1 2 log(1 -µ)dµ + Γ(m/2) √ πΓ((m -1)/2) m 2 1 0 (1 -µ) m-3 2 µ 1 2 dµ.", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "By using the property of Beta distribution, and the inequality that -µ 1-µ ≤ log(1 -µ) ≤ -µ, we have", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "A 1 := Γ(m/2) √ πΓ((m -1)/2) m -3 2 1 0 (1 -µ) m-3 2 µ -1 2 log(1 -µ)dµ ≤ - Γ(m/2) √ πΓ((m -1)/2) m -3 2 1 0 (1 -µ) m-3 2 µ 1 2 dµ = - Γ(m/2) √ πΓ((m -1)/2) m -3 2 B ( 3 2 , m -1 2 ) and A 2 , := Γ(m/2) √ πΓ((m -1)/2) m 2 1 0 (1 -µ) m-3 2 µ 1 2 dµ = Γ(m/2) √ πΓ((m -1)/2) m 2 B ( 3 2 , m -1 2 ).", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "Then, for A, we have", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "A = A 1 + A 2 ≤ - Γ(m/2) √ πΓ((m -1)/2) m -3 2 B ( 3 2 , m -1 2 ) + Γ(m/2) √ πΓ((m -1)/2) m 2 B ( 3 2 , m -1 2 ) = 3 2 Γ(m/2) √ πΓ((m -1)/2) B ( 3 2 , m -1 2 ) = 3 2 Γ(m/2) √ πΓ((m -1)/2) Γ(3/2)Γ((m -1)/2) Γ((m + 2)/2) = 3 2 Γ(3/2)Γ(m/2) √ πΓ((m + 2)/2) = 3 2 ( √ π/2)Γ(m/2) √ πΓ((m + 2)/2) = 3 4 Γ(m/2) Γ((m + 2)/2)", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": ".", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "Using the Stirling formula, we have Γ(x + α) → Γ(x)x α as x → ∞ and thus", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "lim m→∞ D KL (Y i ∥ Y i ) = lim m→∞ log 2 m Γ(m/2) Γ((m -1)/2) + lim m→∞ A ≤ lim m→∞ log 2 m Γ((m -1)/2)( m-1 2 ) 1/2 Γ((m -1)/2) + lim m→∞ 3 4 Γ(m/2) Γ((m + 2)/2) = lim m→∞ log 2 m m -1 2 + 3 4 Γ(m/2) Γ(m/2)m = lim m→∞ log m -1 m + 3 4m = 0.", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "We further use T 2 inequality (<PERSON>, 2016, Theorem 4.31) to derive the quadratic <PERSON><PERSON><PERSON> metric (<PERSON>, 2016, Definition 4.29) as: follows the Student's t-distribution with m -1 degrees of freedom, and its probability density function (pdf) is:", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "EQUATION", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "f T (t) = Γ(m/2) (m -1)πΓ((m -1)/2) (1 + t 2 m -1 ) -m/2 .", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "For random variable Y i , we have", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "Y i = Z i m i=1 Z 2 i = Z i Z 2 i + m j̸ =i Z 2 j = Z i /σ (Z i /σ) 2 + m j̸ =i (Z j /σ) 2 = U √ U 2 + V , and then T = U √ V /(m-1) = √ m-1Yi √ 1-Y 2 i , Y i = T √", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "T 2 +m-1 . Therefore, the cumulative distribution function (cdf) of T is:", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "F Yi (y i ) = P ({Y i ≤ y i }) = P ({Y i ≤ y i }) y i ≤ 0 P ({Y i ≤ 0}) + P ({0 < Y i ≤ y i }) y i > 0 = P ({ T √ T 2 +m-1 ≤ y i }) y i ≤ 0 P ({ T √ T 2 +m-1 ≤ 0}) + P ({0 < T √ T 2 +m-1 ≤ y i }) y i > 0 = P ({ T 2 T 2 +m-1 ≥ y 2 i , T ≤ 0}) y i ≤ 0 P ({T ≤ 0} + P ({ T 2 T 2 +m-1 ≤ y 2 i , T > 0}) y i > 0 =    P ({T ≤ √ m-1yi √ 1-y 2 i }) y i ≤ 0 P ({T ≤ 0} + P ({0 < T ≤ √ m-1yi √ 1-y 2 i }) y i > 0 = P ({T ≤ √ m -1y i 1 -y 2 i }) = F T ( √ m -1y i 1 -y 2 i ).", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "The probability density function of Y i can then be derived as:", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "f Yi (y i ) = d dy i F Yi (y i ) = d dy i F T ( √ m -1y i 1 -y 2 i ) = f T ( √ m -1y i 1 -y 2 i ) d dy i ( √ m -1y i 1 -y 2 i ) = [ Γ(m/2) (m -1)πΓ((m -1)/2) (1 -y 2 i ) m/2 ][ √ m -1(1 -y 2 i ) -3/2 ] = Γ(m/2) √ πΓ((m -1)/2) (1 -y 2 i ) (m-3)/2 .", "section": "B PROOF OF THEOREM 2", "sec_num": null}, {"text": "C.1 PROOF OF THEOREM 1: EXAMINING THE FOUR PROPERTIES FOR -L U Property 1 can be easily verified for -L U and thus we skip the verification. We only examine the other three properties for the uniformity metric -L U .", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "First, we prove that -L U does not satisfy Property 2. Due to the definition of L U in Eqn.", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "(2), we have", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "EQUATION", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": ") Letting G = n i=2 i-1 j=1 e -t∥zi-zj ∥ 2 2 , we have G = n i=2 i-1 j=1 e -t∥zi-zj ∥ 2 2 ≤ n i=2 i-1 j=1 e -t∥zi-zi∥ 2 2 = n(n -1)/2,", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "We easily have", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "Σ 1/2 = Σ 1/2 / √ 2 Σ 1/2 / √ 2 Σ 1/2 / √ 2 Σ 1/2 / √ 2", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": ", tr( Σ) = 2 tr( Σ), and tr( Σ 1/2 ) = √ 2 tr( Σ 1/2 ).", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "Thus", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "W 2 (D ⊕ D) := ∥ µ∥ 2 2 + 1 + tr( Σ) - 2 √ 2m tr( Σ 1/2 ) = 2∥ µ∥ 2 2 + 1 + 2 tr( Σ) - 2 √ 2 √ 2m tr( Σ 1/2 ) > ∥ µ∥ 2 2 + 1 + tr( Σ) - 2 √ m tr( Σ 1/2 ) = W 2 (D).", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "Therefore, -W 2 (D ⊕ D) < -W 2 (D), indicating that our proposed metric -W 2 could satisfy the Property 3.", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "Third, we prove that our proposed metric -W 2 satisfies Property 4. Let z i = z i ⊕ 0 k ∈ R m+k with an overload of notation. For D ⊕ 0 k , the sample mean and covariance estimators are", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "µ = µ 0 k , Σ = Σ 0 m×k 0 k×m 0 k×k ,", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "where µ and Σ are defined previously. Therefore, we have tr( Σ) = tr( Σ), tr( Σ 1/2 ) = tr( Σ 1/2 ), and thus", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "W 2 (D ⊕ 0 k ) := ∥ µ∥ 2 2 + 1 + tr( Σ) - 2 √ m + k tr( Σ 1/2 ) = ∥ µ∥ 2 2 + 1 + tr( Σ) - 2 √ m + k tr( Σ 1/2 ) > ∥ µ∥ 2 2 + 1 + tr( Σ) - 2 √ m tr( Σ 1/2 ) = W 2 (D).", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "Therefore, -W 2 (D ⊕ 0 k ) < -W 2 (D), indicating that our proposed metric -W 2 satisfies the Property 4.", "section": "C EXAMINING THE FOUR PROPERTIES FOR TWO UNIFORMITY METRICS", "sec_num": null}, {"text": "This section further compares the distributions of Y and Y.", "section": "D FURTHER COMPARISONS BETWEEN Y AND Y", "sec_num": null}, {"text": "We visually compare the distributions of Y i and Y i . To estimate the distributions of Y i and Y i , we bin 200,000 sampled data points into 51 groups. Figure 8 compares the binning densities of Y i and Y i when m ∈ {2, 4, 8, 16, 32, 64, 128, 256}. We can observe that two distributions are highly overlapped when m is moderately large, e.g., m ≥ 8 or m ≥ 16.", "section": "D FURTHER COMPARISONS BETWEEN Y AND Y", "sec_num": null}, {"text": "By binning 2,000,000 data points into 51 × 51 groups in two-axis, we also analyze the joint binning densities and present 2D joint binning densities of (Y i , Y j ) (i ̸ = j) in Figure 9 We employ synthetic experiments to study the uniformity metrics across different distributions. Specifically, we sample 50,000 data vectors (m = 256) from different distributions, such as the isotropic Gaussian distribution N (0, I), the uniform distribution on the hyperrectangle [0, 1], and the mixture of Gaussians, etc. Then we normalize these data vectors, and estimate the uniformity of different distributions by two metrics. As shown in Fig. 10 , isotropic Gaussian distribution achieves the maximum values for both -W 2 and -L U , which indicates that isotropic Gaussian distribution achieves larger uniformity than other distributions. This empirical result is consistent with Fact 1 that the isotropic Gaussian distribution (approximately) achieves the maximum uniformity. In this section, we compare the two metrics in terms of Property 2 (ICC). Specifically, we randomly sample 1,000 data vectors from the isotropic Gaussian distribution (m = 32) and then mask 50% of their coordinates with zeros, forming a new dataset D with an overload of notation. To investigate the impact of instance cloning, we create multiple clones of the dataset, such as D ⊎ D and D ⊎ D ⊎ D, which correspond to one and two times cloning, respectively. We evaluate the two metrics on these datasets. Figure 11 shows that the value of -L U slightly decreases as the number of clones increases, in- In this section, we explore our uniformity loss W 2 . This loss embodies two primary constraints. Firstly, it promotes the covariance matrix to be isotropic (specifically I m /m). Secondly, it enforces the mean to be zero. The latter constraint on the mean is crucial. To illustrate, we present a case study demonstrating that deviating the mean from zero compromises uniformity, even if the covariance matrix is precisely I m /m and thus isotropic. Means deviating from zero may result in dimensional collapse and even constant collapse.", "section": "D FURTHER COMPARISONS BETWEEN Y AND Y", "sec_num": null}, {"text": "For simplicity, we also refer to (z a , z b ) as positive pairs.", "section": "", "sec_num": null}, {"text": "A NEW UNIFORMITY METRICIn this section, we introduce a new uniformity metric to address the limitations of -L U .", "section": "", "sec_num": null}, {"text": "We discuss using other statistical distances as uniformity losses, such as the <PERSON><PERSON><PERSON>-<PERSON> divergence and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> distance, in Appendix A.", "section": "", "sec_num": null}, {"text": "In this paper, we always first normalize the representations to have unit ℓ2 norms.", "section": "", "sec_num": null}, {"text": "Intuitively, maximal uniformity should stay constant regardless of dimensions; otherwise the corresponding uniformity metric exhibit a preference for larger or smaller dimensions.", "section": "", "sec_num": null}], "back_matter": [{"text": "ACKNOWLEDGEMENT Ben<PERSON><PERSON> was partially supported by the Shenzhen Science and Technology Program (JCYJ20220818103001002), Shenzhen Doctoral Startup Funding (RCBS20221008093330065), and Tianyuan Fund for Mathematics of National Natural Science Foundation of China (NSFC) (12326608). Qiang Sun was partially supported in part by the Natural Sciences and Engineering Research Council of Canada under Grant RGPIN-2018-06484 and a Data Sciences Institute Catalyst Grant.", "section": "acknowledgement", "sec_num": null}, {"text": "and G = n(n -1)/2 if and only if z 1 = z 2 = . . . = z n . ThusThe above equality holds if and only if G = n(n -1)/2, which requires z 1 = z 2 = ... = z n , a trivial case when all representations collapse to one constant point. We have excluded this trivial case, and thus -L U (D ⊎ D) < -L U (D). Therefore, the uniformity metric -L U does not satisfy Property 2.Second, we prove that -L U does not satisfy Property 3. Letting z i = z i ⊕ z i and z j = z j ⊕ z j , we haveBy the definitions of z i and z j , we have, indicating that the uniformity metric -L U does not satisfy the Property 3.Third, we prove that the existing metric -L U does not satisfy the Property 4. Letting z i = z i ⊕ 0 k and z j = z j ⊕ 0 k , we haveBy the definitions of z i and z j , we have D) , indicating that the uniformity metric -L U does not satisfy Property 4.", "section": "Appendix Table of Contents", "sec_num": null}, {"text": "Property 1 can be easily verified for -W 2 , and thus the proof is skipped. We only examine the rest three properties for the proposed uniformity metric -W 2 .First, we prove that our proposed metric -W 2 satisfies Property 2. Let µ and Σ be defined as above, for D ⊎ D = {z 1 , z 2 , ..., z n , z 1 , z 2 , ..., z n }, the mean and covariance estimators arewhich agree with those for D. Then we haveTherefore, our proposed metric -W 2 satisfies Property 2.Second, we prove that -W 2 satisfies Property 3. LetFor D ⊕ D, the mean and covariance estimators are: , where 1 ∈ R k represents a vector of all ones. We vary k from 0 to 32 and visualize the ℓ 2 -normalized Y's in Figure 14 (by generating multiple independent copies). It is clear that an excessively large means will cause representations to collapse to a single point, even if the covariance matrix is isotropic.", "section": "C.2 PROOF OF THEOREM 3: EXAMINING THE FOUR PROPERTIES FOR -W 2", "sec_num": null}, {"text": "To ensure fair comparisons, all experiments in Section 6 are conducted on a single 1080 GPU. Additionally, we maintain consistency in network architecture across all models, utilizing ResNet-18 (<PERSON> et al., 2016) as the backbone and a three-layer MLP as the projector. The LARS optimizer (<PERSON> et al., 2017) is employed with a base learning rate of 0.2, accompanied by a cosine decay learning rate schedule (<PERSON><PERSON><PERSON> & Hutter, 2017 ) for all models. Evaluation follows a linear evaluation protocol, where models are pre-trained for 500 epochs. Evaluation involves adding a linear classifier and training the classifier for 100 epochs while preserving the learned representations. The same augmentation strategy is deployed across all models, encompassing various operations such as color distortion, rotation, and cutout. Following <PERSON> et al. ( 2022), we set the temperature t = 0.2 for all contrastive learning methods. For MoCo (<PERSON> et al., 2020) and NNCLR (<PERSON><PERSON><PERSON><PERSON> et al., 2021) , which require an additional queue to store negative samples, we set the queue size to 2 12 . Regarding the linear decay for weighting the quadratic <PERSON><PERSON><PERSON> distance, refer to Table 3 for the parameter settings.", "section": "F EXPERIMENT SETTINGS AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ANALYSIS F.1 EXPERIMENT SETTINGS", "sec_num": null}, {"text": "Here we illustrate the convergence of Top-1 accuracy across all training epochs in Fig 15 . Throughout the training, we capture the model checkpoint at the end of each epoch to train a linear classifier. We subsequently evaluate the Top-1 accuracy on unseen images from the test set (either CIFAR-10 or CIFAR-100).For both CIFAR-10 and CIFAR-100, we observe that integrating the proposed uniformity metric as an auxiliary loss significantly enhances the Top-1 accuracy, particularly in the initial stages of training.", "section": "F.2 CONVERGE<PERSON><PERSON> ANALYSIS FOR TOP-1 ACCURACY", "sec_num": null}, {"text": "This section presents the convergence of the uniformity metric and alignment loss across all training epochs in Figure 16 and Figure 17 , respectively. Throughout the training, we record the model checkpoint at the end of each epoch to evaluate the uniformity using the proposed metric W 2 and alignment (Wang & Isola, 2020) on unseen images from the test set (either CIFAR-10 or CIFAR-100).For both CIFAR-10 and CIFAR-100, we observe that integrating the proposed uniformity metric as an auxiliary loss significantly improves uniformity. However, it also slightly compromises alignment (where a smaller alignment loss indicates better alignment). It should be noted that improved uniformity often leads to worse alignment. ", "section": "F.3 CONVER<PERSON><PERSON><PERSON> ANALYSIS FOR UNIFORMITY AND ALIGNMENT", "sec_num": null}], "ref_entries": {"FIGREF0": {"uris": null, "fig_num": "1", "type_str": "figure", "text": "Figure 1: The left figure presents constant collapse, and the right figure visualizes dimensional collapse.", "num": null}, "FIGREF1": {"uris": null, "fig_num": "2", "type_str": "figure", "text": "Figure 2: The KL divergence and Wasserstein distance between Y i and Y i w.r.t. various dimensions.", "num": null}, "FIGREF2": {"uris": null, "fig_num": "34", "type_str": "figure", "text": "Figure 3: Sensitivity to dimensional collapse degrees: -W 2 is more sensitive than -L U .", "num": null}, "FIGREF3": {"uris": null, "fig_num": null, "type_str": "figure", "text": "Figure 5: FCC analysis.", "num": null}, "FIGREF4": {"uris": null, "fig_num": "7", "type_str": "figure", "text": "Figure 7: Dimensional collapse analysis on CIFAR-100 dataset.", "num": null}, "FIGREF5": {"uris": null, "fig_num": null, "type_str": "figure", "text": "1), then U and V are independent with each other. The random variable T =", "num": null}, "FIGREF6": {"uris": null, "fig_num": "8", "type_str": "figure", "text": "Figure 8: Comparing the binning densities of Y i and Y i with various dimensions.", "num": null}, "FIGREF7": {"uris": null, "fig_num": null, "type_str": "figure", "text": "Density for <PERSON> and <PERSON>j", "num": null}, "FIGREF8": {"uris": null, "fig_num": "910", "type_str": "figure", "text": "Figure 9: Visualization of two arbitrary dimensions for Y and Y when m = 32.", "num": null}, "FIGREF9": {"uris": null, "fig_num": "2", "type_str": "figure", "text": "Figure 11: ICC analysis.", "num": null}, "FIGREF10": {"uris": null, "fig_num": "13", "type_str": "figure", "text": "Figure 12: A case study for Property 4 and blue points are data vectors.", "num": null}, "TABREF0": {"content": "<table><tr><td>Properties IPC ICC FCC FBC</td></tr><tr><td>-L U</td></tr><tr><td>-W 2</td></tr></table>", "type_str": "table", "html": null, "text": "Comparing the two uniformity metrics.", "num": null}, "TABREF1": {"content": "<table><tr><td>Methods</td><td colspan=\"2\">Proj. Pred.</td><td>Acc@1↑</td><td>Acc@5↑</td><td>CIFAR-10 W2 ↓</td><td>LU ↓</td><td>LA ↓</td><td>Acc@1↑</td><td>Acc@5↑</td><td>CIFAR-100 W2 ↓</td><td>LU ↓</td><td>LA ↓</td></tr><tr><td>SimCLR</td><td>256</td><td/><td>89.85</td><td>99.78</td><td>1.04</td><td colspan=\"2\">-3.75 0.47</td><td>63.43</td><td>88.97</td><td>1.05</td><td colspan=\"2\">-3.75 0.50</td></tr><tr><td>NNCLR</td><td>256</td><td colspan=\"2\">256 87.46</td><td>99.63</td><td>1.23</td><td colspan=\"2\">-3.12 0.38</td><td>54.90</td><td>83.81</td><td>1.23</td><td colspan=\"2\">-3.18 0.43</td></tr><tr><td>SimSiam</td><td>256</td><td colspan=\"2\">256 86.71</td><td>99.67</td><td>1.19</td><td colspan=\"2\">-3.33 0.39</td><td>56.10</td><td>84.34</td><td>1.21</td><td colspan=\"2\">-3.29 0.42</td></tr><tr><td>AlignUniform</td><td>256</td><td/><td>90.37</td><td>99.76</td><td>0.94</td><td colspan=\"2\">-3.82 0.51</td><td>65.08</td><td>90.15</td><td>0.95</td><td colspan=\"2\">-3.82 0.53</td></tr><tr><td>MoCo v2</td><td>256</td><td/><td>90.65</td><td>99.81</td><td>1.06</td><td colspan=\"2\">-3.75 0.51</td><td>60.27</td><td>86.29</td><td>1.07</td><td colspan=\"2\">-3.60 0.46</td></tr><tr><td>MoCo v2 + LU</td><td>256</td><td/><td>90.98 ↑0.33</td><td>99.67</td><td colspan=\"4\">0.98 ↑0.08 -3.82 0.53 ↓0.02 61.21 ↑0.94</td><td>87.32</td><td colspan=\"3\">0.98 ↑0.09 -3.81 0.52 ↓0.06</td></tr><tr><td>MoCo v2 + W2</td><td>256</td><td/><td>91.41 ↑0.76</td><td>99.68</td><td colspan=\"4\">0.33 ↑0.73 -3.84 0.63 ↓0.12 63.68 ↑3.41</td><td>88.48</td><td colspan=\"3\">0.28 ↑0.79 -3.86 0.66 ↓0.20</td></tr><tr><td>BYOL</td><td>256</td><td colspan=\"2\">256 89.53</td><td>99.71</td><td>1.21</td><td colspan=\"2\">-2.99 0.31</td><td>63.66</td><td>88.81</td><td>1.20</td><td colspan=\"2\">-2.87 0.33</td></tr><tr><td>BYOL + LU</td><td>256</td><td/><td>90.09 ↑0.56</td><td>99.75</td><td colspan=\"4\">1.09 ↑0.12 -3.66 0.40 ↓0.09 62.68 ↓0.98</td><td>88.44</td><td colspan=\"3\">1.08 ↑0.12 -3.70 0.51 ↓0.18</td></tr><tr><td>BYOL + W2</td><td>256</td><td colspan=\"2\">256 90.31 ↑0.78</td><td>99.77</td><td colspan=\"4\">0.38 ↑0.83 -3.90 0.65 ↓0.34 65.16 ↑1.50</td><td>89.25</td><td colspan=\"3\">0.36 ↑0.84 -3.91 0.69 ↓0.36</td></tr><tr><td>BarlowTwins</td><td>256</td><td/><td>91.16</td><td>99.80</td><td>0.22</td><td colspan=\"2\">-3.91 0.75</td><td>68.19</td><td>90.64</td><td>0.23</td><td colspan=\"2\">-3.91 0.75</td></tr><tr><td>BarlowTwins + LU</td><td>256</td><td/><td>91.38 ↑0.22</td><td>99.77</td><td colspan=\"4\">0.21 ↑0.01 -3.92 0.76 ↓0.01 68.41 ↑0.22</td><td>90.99</td><td colspan=\"3\">0.22 ↑0.01 -3.91 0.76 ↓0.01</td></tr><tr><td colspan=\"2\">BarlowTwins + W2 256</td><td/><td>91.43 ↑0.27</td><td>99.78</td><td colspan=\"4\">0.19 ↑0.03 -3.92 0.76 ↓0.01 68.47 ↑0.28</td><td>90.64</td><td colspan=\"3\">0.19 ↑0.04 -3.91 0.79 ↓0.04</td></tr><tr><td>Zero-CL</td><td>256</td><td/><td>91.35</td><td>99.74</td><td>0.15</td><td colspan=\"2\">-3.94 0.70</td><td>68.50</td><td>90.97</td><td>0.15</td><td colspan=\"2\">-3.93 0.75</td></tr><tr><td>Zero-CL + LU</td><td>256</td><td/><td>91.28 ↓0.07</td><td>99.74</td><td>0.15</td><td colspan=\"3\">-3.94 0.72 ↓0.02 68.44 ↓0.06</td><td>90.91</td><td>0.15</td><td colspan=\"2\">-3.93 0.74 ↑0.01</td></tr><tr><td>Zero-CL + W2</td><td>256</td><td/><td>91.42 ↑0.07</td><td>99.82</td><td colspan=\"4\">0.14 ↑0.01 -3.94 0.71 ↓0.01 68.55 ↑0.05</td><td>91.02</td><td colspan=\"3\">0.14 ↑0.01 -3.94 0.76 ↓0.01</td></tr><tr><td colspan=\"4\">epochs. 6RUWHGHLJHQYDOXHLQGH[ /RJRI6LQJXODU9DOXHV %&lt;2/ 0R&amp;RY 6LP&amp;/5 6LP6LDP 11&amp;/5 $OLJQ8QLIRUP</td><td>/RJRI6LQJXODU9DOXHV</td><td colspan=\"3\">6RUWHGHLJHQYDOXHLQGH[ 0R&amp;RY 0R&amp;RY 0R&amp;RY</td><td>/RJRI6LQJXODU9DOXHV</td><td/><td colspan=\"3\">6RUWHGHLJHQYDOXHLQGH[ %&lt;2/ %&lt;2/ %&lt;2/</td></tr><tr><td colspan=\"4\">(a) Singular Value Spectra</td><td/><td colspan=\"2\">(b) MoCo v2</td><td/><td/><td/><td colspan=\"2\">(c) BYOL</td></tr></table>", "type_str": "table", "html": null, "text": "Main results on CIFAR-10 and CIFAR-100. Proj. and Pred. are the hidden dimensions in projector and predictor. ↑ and ↓ indicates gains and losses, respectively. Figure15illustrates the results. By incorporating W 2 as an additional loss for these models, we observe faster convergence compared to the raw models, particularly for MoCo v2 and BYOL, which exhibit significant collapse issues. Our experiments demonstrate that imposing the proposed Wasserstein uniformity metric as an auxiliary penalty loss greatly enhances uniformity but may compromise alignment. We further analyze uniformity and alignment throughout all training epochs in Appendix F.3.", "num": null}, "TABREF2": {"content": "<table><tr><td>E Additional synthetic studies</td><td>17</td></tr><tr><td>E.1</td><td/></tr></table>", "type_str": "table", "html": null, "text": "Correlation between -LU and -W2 . . . . . . . . . . . . . . . . . . . . . . . 17 E.2 On Instance Cloning Constraint . . . . . . . . . . . . . . . . . . . . . . . . . . 18 E.3 Understanding Property 4: Why does it relate to dimensional collapse? . . . . . 19 E.4 Understanding W2: Large means may lead to collapse . . . . . . . . . . . . . . 19 F Experiment settings and convergence analysis 20 F.1 Experiment settings . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 20 F.2 Convergence analysis for Top-1 accuracy . . . . . . . . . . . . . . . . . . . . . 20 F.3 Convergence analysis for uniformity and alignment . . . . . . . . . . . . . . . . 21", "num": null}}}}