{"paper_id": "VCAS", "title": "EFFICIENT BACKPROPAGATION WITH VARIANCE-CONTROLLED ADAPTIVE SAMPLING", "abstract": "Sampling-based algorithms, which eliminate \"unimportant\" computations during forward and/or back propagation (BP), offer potential solutions to accelerate neural network training. However, since sampling introduces approximations to training, such algorithms may not consistently maintain accuracy across various tasks. In this work, we introduce a variance-controlled adaptive sampling (VCAS) method designed to accelerate BP. VCAS computes an unbiased stochastic gradient with fine-grained layerwise importance sampling in data dimension for activation gradient calculation and leverage score sampling in token dimension for weight gradient calculation. To preserve accuracy, we control the additional variance by learning the sample ratio jointly with model parameters during training. We assessed VCAS on multiple fine-tuning and pre-training tasks in both vision and natural language domains. On all the tasks, VCAS can preserve the original training loss trajectory and validation accuracy with an up to 73.87% FLOPs reduction of BP and 49.58% FLOPs reduction of the whole training process. The implementation is available at https://github.com/thu-ml/VCAS.", "pdf_parse": {"paper_id": "VCAS", "abstract": [{"text": "Sampling-based algorithms, which eliminate \"unimportant\" computations during forward and/or back propagation (BP), offer potential solutions to accelerate neural network training. However, since sampling introduces approximations to training, such algorithms may not consistently maintain accuracy across various tasks. In this work, we introduce a variance-controlled adaptive sampling (VCAS) method designed to accelerate BP. VCAS computes an unbiased stochastic gradient with fine-grained layerwise importance sampling in data dimension for activation gradient calculation and leverage score sampling in token dimension for weight gradient calculation. To preserve accuracy, we control the additional variance by learning the sample ratio jointly with model parameters during training. We assessed VCAS on multiple fine-tuning and pre-training tasks in both vision and natural language domains. On all the tasks, VCAS can preserve the original training loss trajectory and validation accuracy with an up to 73.87% FLOPs reduction of BP and 49.58% FLOPs reduction of the whole training process. The implementation is available at https://github.com/thu-ml/VCAS.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "and UB (Kathar<PERSON><PERSON> & F<PERSON>t, 2018) fail with a similar FLOPs reduction.", "section": "", "sec_num": null}, {"text": "Training neural networks can be computationally intensive. Contemporary networks typically employ stochastic gradient methods (<PERSON><PERSON><PERSON> et al., 2018) for training, which iteratively process batches of data to compute stochastic gradients through forward propagation (FP) and back propagation (BP) techniques (<PERSON><PERSON><PERSON><PERSON> et al., 1986) . FP+BP are costly, as they need to process every datum in the batch and every connection in the network, resulting in a multiplicative time complexity of batch size and model size. Such a time complexity becomes increasingly problematic in the era of big data and big models.", "section": "", "sec_num": null}, {"text": "Data samples are not equally important. Some might be easy for the network to learn, while others might be extremely hard. Training can be accelerated by utilizing this disparity, focusing the available computational resources on more pivotal samples. At a high level, this can be achieved by further sampling the batch with higher keep probability of more important samples. The computational overhead is consequently diminished, in proportion to the quantity of retained samples. Various methods are proposed to assess the importance of samples, including meta-learning methods (<PERSON> et al., 2017; <PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2022) , loss-based methods (<PERSON> & <PERSON>, 2015; <PERSON> et al., 2017; <PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2022) , and gradient norm based methods (<PERSON><PERSON> et al., 2014; <PERSON> & <PERSON>, 2015; <PERSON> et al., 2015; <PERSON> & <PERSON>, 2018; Kathar<PERSON><PERSON> & F<PERSON>t, 2018) .", "section": "", "sec_num": null}, {"text": "While such methods seem promising, one core concern of sampling-based methods is their robustness. Misjudging the importance can hamper convergence, potentially leading to degraded accuracy and even longer training time than uniform sampling. Moreover, the optimal sample ratio is influenced by data distribution, which differs between tasks and is challenging to determine in advance.", "section": "", "sec_num": null}, {"text": "In general, there is a \"no-free-lunch\" phenomenon (<PERSON><PERSON><PERSON> et al., 2023) , where aggressive sampling often comes at the cost of reduced robustness.", "section": "", "sec_num": null}, {"text": "In this work, we propose a robust variance-controlled adaptive sampling (VCAS) algorithm for deep learning under the stochastic optimization framework. VCAS computes a cost-effective approximated stochastic gradient (ASG) by partially conducting backpropagation for specific data and tokens. This ASG is unbiased, and we have developed an adaptive sampling method to meticulously control the variance of the ASG, aligning it with the original stochastic gradient's variance. Consequently, convergence remains largely unaffected, with our method mirroring the progression of exact algorithms, as delineated in Fig. 1 .", "section": "", "sec_num": null}, {"text": "Unlike previous methods, VCAS construct the ASG in a fine-grained manner. Rather than dropping samples one-time in a whole, VCAS gradually drops more samples when backpropagating from topmost to bottommost network layers, as the gradient getting sparser. Furthermore, VCAS also more aggressively drops data in finer granularity of tokens rather than samples when computing the weight gradients. VCAS can achieve smaller variance under a given computational budget compared to coarse grained sampling on the data dimension.", "section": "", "sec_num": null}, {"text": "We evaluate VCAS on multiple finetuning and pre-training tasks of language models and vision transformers. VCAS can preserve the original training loss trajectory and the validation accuracy on all tasks, while adaptively determining the computational saving depending on the difficulty of the task. VCAS can reduce the computational cost of backpropagation by up to 73.87%, and reduce the overall training computation by up to 49.58%.", "section": "", "sec_num": null}, {"text": "Methods focusing on the difference of data, known as online batch selection (<PERSON><PERSON><PERSON><PERSON> & Hutter, 2015) , can be mainly categorized into three classes: meta learning methods, loss based methods and gradient norm based methods. In this section we will discuss these three ways separately and briefly introduce other orthogonal efficient training methods.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Meta Learning Methods. Some works formulate data sampling into an optimization problem and train a separate meta predictor to solve it. <PERSON> et al. (2017) use deep reinforcement learning to train an agent for data selection. <PERSON> et al. (2019) and <PERSON><PERSON><PERSON> et al. (2022) train a separate cheaper model with similar architecture for guidance. However, training a meta predictor will introduce further overhead and it's a non-trivial learning task with more uncertainty introduced for weak theoretical guarantee.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Loss Based Methods. Loss is a natural indicator of the importance of different data. <PERSON><PERSON><PERSON>lov & Hutter (2015) maintains a history of losses and develops a sophisticated distribution based on the value or rank of loss. <PERSON> et al. (2019) and <PERSON><PERSON><PERSON> et al. (2022) simplify it with sampling distribution proportion to the percentile of loss in the history. <PERSON> et al. (2017) broadens the history to every datum and proposes to sample by the variance of prediction probability directly linked with previous losses. <PERSON> et al. (2021) provides another method of minimizing the L 2 norm between the sampled loss and the exact counterpart. <PERSON> et al. (2020) samples the smallest loss for robustness to outliers. <PERSON> et al. (2023) ensembles several loss methods with a preset sample ratio and varies the weights assigned to these methods adaptively. Simple and effective as they may be, the loss based methods are heuristic and always need a hyperparameter of sample ratio to tune for different tasks, violating the goal of efficient training.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Gradient Norm Based Methods. Previous works have proved that the optimal data sampling distribution for SGD is proportional to the gradient norm (<PERSON><PERSON> et al., 2014; <PERSON> & <PERSON>, 2015) . But calculating the gradient norm is prohibitive since it needs a full process of backpropagation. Orthogonal Efficient Training Methods. Data pruning (<PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2022) focuses on filtering less informative data before the whole training. Architecture pruning like layer dropping (<PERSON> et al., 2016; <PERSON> & <PERSON>, 2020) and token dropping (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2022) modifies the architecture to make models faster to train with modest affect to performance. Mixed precision training and quantization (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018; <PERSON> et al., 2021; <PERSON> et al., 2022) change the training procedure to use low-precision in calculation for acceleration. Sparsity (<PERSON><PERSON><PERSON> et al., 2021) focuses on pruning near-zero values in weights, activations, or gradients to achieve a low FLOPs(<PERSON><PERSON> & <PERSON>, 2020) and low memory footprint (<PERSON><PERSON> et al., 2023) , yet is usually hard to bring a wall-clock time reduction like us due to the lack of hardware support (NVIDIA, 2021) . All these works are orthogonal to our work since we focus on the computation approximation of a certain model architecture on a certain dataset with a certain training procedure to bring real training acceleration.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "In this section, we present a high-level overview of our sampling algorithm as stochastic optimization. Consider the learning problem of a model f (X; θ) parameterized by θ on a dataset D = {(X i , y i )} |D| i=1 with a loss function ℓ(•, •). Define the learning objective as", "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "EQUATION", "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "where the expectation is taken over all possible batches B = (X, y) from D. The model parameters can be learned by stochastic optimization algorithms (<PERSON><PERSON><PERSON> et al., 2018) with a stochastic gradient (SG) g(θ; B) := ∇ θ ℓ(f (X; θ), y), which is an unbiased approximation of ∇ θ L(θ).", "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "However, computing the stochastic gradient can be still too expensive, since it requires the full forward and back propagation, which iterate over all model parameters and all data in the batch. We build a cheap stochastic approximation g(θ; B, ϵ) of the SG, which we refer as approximated stochastic gradient (ASG). ASG only computes the backpropagation partially, and is therefore cheaper than the SG. The randomness in the computing procedure of ASG is captured by ϵ. We ensure that ASG is unbiased: E ϵ [g(θ; B, ϵ)] = g(θ; B). With an unbiased SG, stochastic optimization algorithms are guaranteed to converge to a stationary point of Eq. ( 1), while the converge speed depends on the variance (cf. <PERSON> et al. ( 2018)). Therefore, if the variance of the ASG can be controlled to the similar variance level of SG, substituting the SG with ASG should have little impact to the convergence behavior. In fact, by the law of total variance (<PERSON>, 2001) , the variance of ASG can be decoupled as", "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "Var [g(θ; B, ϵ)] = Var [g(θ; B)] + E B [Var ϵ [g(θ; B, ϵ)]] ,", "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "where the first term is the intrinsic variance of SG caused by subsampling batches from the dataset, and the second term is the additional variance incurred by ASG. In the subsequent sections, we will discuss our constructions of the ASG, which incurs negligible additional variance compared to SG.", "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "Here we present variance-controlled adaptive sampling (VCAS), a specific construction of the ASG. We compute ASG by approximating the backpropagation in a fine-grained manner, and speed up matrix multiplications with importance sampling on the data dimension.", "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "Assume a batch X of shape N × T × K, where N is the batch size, T is the number of tokens of each datum, and K is the dimensionality. For an L-layer network , the model f (X; θ) can be described by the following forward propagation procedure: L) , where Z (l) and θ (l) are the activation and parameters of the l-th layer, and θ = (θ (l) ) L l=1 . The SG can be computed by back-propagation in the following form: l) , where ∇ Z (l) and ∇ θ (l) denote the activation / weight gradient, h (l) and g (l) denote the function that calculates input / weight gradient of layer l with the output gradient, layer input and weight. The SG g(θ; B) = (∇ θ (l) ) L l=1 . As illustrated by Fig. 3 , the activation gradients ∇ Z (l) are sparse: the gradient (∇ Z (l) ) i is close to zero for most sample i, except for a few important samples. Such sparsity becomes more prominent as backpropagating to lower layers and as the training progresses. To speed up computation, we add samplers in the backpropagation graph:", "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "Z (0) = X, Z (l) = f (l) Z (l-1) ; θ (l) , f (X; θ) = Z (", "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "∇ Z (l-1) = h (l) ∇ Z (l) ; Z (l-1) , θ (l) , ∇ θ (l) = g (l) ∇ Z (l) ; Z (l-1) , θ (", "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "∇Z (l) = SampleA ϵ,ρ l (∇ Z (l) ) , ∇ Z (l-1) = h (l)", "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "∇Z (l) ; Z (l-1) , θ (l) , l) .", "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "EQUATION", "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "(2)", "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "The sampler SampleA ϵ,ρ l (•) randomly filter out unimportant data from the activation gradient, the keep ratio is ρ l , with the randomness captured by ϵ. The sampler is applied for each layer, so the activation gradient becomes increasingly sparse when backpropagating from the L-th layer to the first layer. The sampler SampleW ξ l ,ν l (•) filters (data, token) pairs specifically for weight gradient calculation, with a keep ratio ν l and the randomness ξ l . With these samplers, we only need to compute backpropagation for the retained data / token, so the computational cost is reduced. The sampling procedure is illustrated in Fig. 2 , which constructs an unbiased ASG g(θ; B, ϵ, ξ, ρ, ν) = (∇ θ (l) ) L l=1 , with ∇ θ (l) defined as Eq. ( 2), and", "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "ξ = (ξ l ) L l=1 , ρ = (ρ) L l=1 , ν = (ν l ) L l=1 .", "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "We apply unbiased low-variance approximation to the activation gradient to speed up subsequent computation. For an activation gradient tensor G of shape N × T × K, we sample", "section": "ACTIVATION GRADIENT", "sec_num": "4.1"}, {"text": "Ĝ = SampleA ϵ,ρ (G) = G • (m(ϵ, ρ) ⊗ 1 ⊗ 1),", "section": "ACTIVATION GRADIENT", "sec_num": "4.1"}, {"text": "where • is element-wise product, and ⊗ is tensor outer product. The mask m ∈ R N is a random <PERSON><PERSON><PERSON> vector: m(ϵ, ρ) i = Bern(p i ; ϵ)/p i , where N i=1 p i = N ρ, and <PERSON>(p; ϵ) denotes a <PERSON><PERSON><PERSON> random number generator with probability p and randomness ϵ. Since E[m(ϵ, ρ) i ] = 1, ∀i, the approximation is unbiased: E[ Ĝ] = G. The sampler zeros out the gradient for all the data whose m(ϵ, ρ) i = 0. The amount of retained data is N ρ in expectation. With the sampler, we only need to compute backpropagation for retained data, so the cost is ρ times lower.", "section": "ACTIVATION GRADIENT", "sec_num": "4.1"}, {"text": "Var Ĝ = N i=1 1-pi pi ∥G i ∥ 2 F", "section": "The variance of the approximation is", "sec_num": null}, {"text": ", where we define the variance of a random tensor element-wise as Var Ĝ = ijk Var Ĝijk , and G i denotes the i-th matrix of G in the N dimension. We compute the keep probability (p i ) to minimize the variance, deriving a distribution proportional to the gradient norm of each datum: p i ∝ ∥G i ∥ F . Minimizing the variance of the activation gradient not necessarily minimize the variance of ASG, which is the gradient of parameters. Nevertheless, this is a useful heuristic which empirically achieves low variance as is revealed by <PERSON><PERSON><PERSON><PERSON> & Fleuret (2018) , and the ASG variance will be carefully controlled by our adaptive algorithm, as we shall see soon in Sec. 5.", "section": "The variance of the approximation is", "sec_num": null}, {"text": "We can accelerate the computation of weight gradient for linear layers by sampling in both data and token dimensions. Consider the approximate back propagation of a linear layer Z (l) = Z (l-1) θ (l) ⊤ :", "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "∇Z (l) = SampleA ϵ,ρ l (∇ Z (l) ) , ∇Z (l) = SampleW ξ l ,ν l ∇Z (l) , Z (l-1) , ∇ θ (l) = ∇⊤ Z (l) Z (l-1)", "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "in matrix form, where we reshape the activation/gradients to N T ×K, and ∇Z (l) is already a sampled matrix with only N T ρ l non-zero rows in expectation. However, ∇Z (l) is only sampled in the data dimension. In fact, even ( ∇Z (l) ) i is retained for some datum i, it might still have some rows (i.e., tokens) which are close to zero. We can further sample", "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "∇Z (l) = SampleW ξ l ,ν l ∇Z (l) , Z (l-1) = ∇Z (l) • (m(ξ, ν) ⊤ 1),", "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "where the mask m ∈ R N L is a random <PERSON><PERSON><PERSON> vector, and 1 is an all-one vector: m(ξ, ν) i = Bern(q i ; ϵ)/q i , where", "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "EQUATION", "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": ")", "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "The minimal variance solution is", "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "q i ∝ ∇Z (l) i 2 Z (l-1) i 2", "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": ". This sampling method is also known as leverage score sampling in randomized numerical linear algebra (Drineas & Mahoney, 2018) .", "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "The question remained is how to set the sample ratios (ρ l ) L l=1 and (ν l ) L l=1 . There is a tradeoff: lowering the sample ratio reduces the computational cost, but increases the variance. As discussed in Sec. 3, this ratio should be set to ensure that the additional variance of ASG is marginal compared to the original variance of SG. Adapting the sample ratio is nontrivial since the gradient sparsity pattern vary across layers and vary over time during training. In this section, we present an adaptation algorithm to control the variance during the entire training trajectory.", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "First, we introduce a single hyperparameter s to control the sample ratios (ρ l ) L l=1 for all layers. Intuitively, when the gradient norm (∥G i ∥ F ) N i=1 becomes sparser, we can more aggressively utilize smaller keep ratio ρ l to maximize speedup. Therefore, we compute ρ l based on the sparsity p l of the gradient norm sequence:", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "EQUATION", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "where s ∈ [0, 1] is a hyperparameter on how much gradient norm is preserved. It's shown in Fig. 3 that gradient norm grows sparser with layer, yielding a descending trend of p l for l from L to 1. Thus it's reasonable to construct a monotone increasing sequence of {ρ l } L l=1 based on {p l } L l=1 . By law of total variance, we can decompose the variance of ASG as", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "Var [g(θ; B, ϵ, ξ, ρ, ν)] = Var [g(θ; B)] + E B [Var ϵ [g(θ; B, ϵ, ρ(s))]] + E B,ϵ [Var ξ [g(θ; B, ϵ, ξ, ρ, ν]],", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "where we write g(θ; B, ϵ, ρ) := E ξ [g(θ; B, ϵ, ξ, ρ, ν)] to be the ASG without the sampler for weight gradient computation. The three variance terms are the SG variance, the variance introduced by approximately computing activation gradient, and the variance introduced by approximately computing weight gradient, respectively. Our algorithm adaptively tunes s and ν during train to control the last two variance terms to be fractional comparing to the first variance term.", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "Controlling E B [Var ϵ [g(θ; B, ϵ, ρ(s))]]:", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "We adopt a zeroth order method to adapt the hyperparameter s to keep E B [Var ϵ [g(θ; B, ϵ, ρ(s))]] = τ act Var [g(θ; B)], where τ act ≪ 1 is a small constant. That is, the additional variance raised by approximately computing activation gradient is only τ act times the SG variance itself. Since larger s increases the keep ratio and decreases the variance, we adopt the update:", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "EQUATION", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "where sign(x) = +1 when x ≥ 0 and sign(x) = -1 when x < 0, and α is a step size. We approximate the expectation and variance with empirical ones with M Monte Carlo repetitions. Therefore, each update requires O(M 2 ) FP+BPs, and we run the update every F SGD iterations, where F ≫ M 2 .", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "Controlling E B,ϵ [Var ξ [g(θ; B, ϵ, ξ, ρ, ν]]: As the variance sums up for each parameter θ (l) , we can further decompose the variance as", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "EQUATION", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "where g (l) is the gradient of the l-th layer (i.e., ∇ θ (l) ). We control the variance of each layer separately to keep E B,ϵ Var ξ g (l) (θ; B, ϵ, ξ l , ρ, ν l ) = τ w Var g (l) (θ; B) . Again, this is achieved by a zeroth-order algorithm:", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "EQUATION", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "where Var ξ g (l) can be computed analytically by Eq. 3, and β is a multiplier. Now we are fully prepared to present the whole picture of VCAS in Alg. 1. Please refer to Appendix. D for more details about the algorithm.", "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "We assessed VCAS on multiple fine-tuning and pre-training tasks in both vision and natural language domains. We compare our algorithm with the exact training and two previous works in BP sampling: a loss based method SB(selective backprop) in Johnson & Guestrin (2018) and a gradient norm based method UB(upper bound) in Katharopoulos & Fleuret (2018) . We choose these two methods since they are entirely online and need little modification to the original training pipeline like us. The results are shown in Tab. 1. All results are the average of 3 different seeds except for BERT-base pretraining and ViT finetuning on ImageNet-1k which we use 1.", "section": "TRAINING FLOPS REDUCTION", "sec_num": "6.1"}, {"text": "Require: update frequency F , <PERSON><PERSON><PERSON> repetition number M , variance tolerant ratio for activation τact, for weight τw, s step size α, weight ratio multiplier β s ← 1, activation sample ratio schedule {ρ l } L l=1 ← 1, weight sample ratios {ν l } L l=1 ← 1 t ← 0 while not converge do if t mod F = 0 then for i in 1, . . . , M do (Xi, yi) ← batch selected randomly SGD gradient Gs,i ← exact backward using (Xi, yi) for j in 1, . . . , M do activation gradient Gact,i,j ← backward using (Xi, yi) with SampleA only calculate weight variance Vw,i,j analytically with Eq. 3 and Eq. 6 end for end for", "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "SGD variance Vs ← 1 M -1 M i=1 Gs,i -1 M M i=1 Gs,i 2 F activation variance Vact ← 1 M M i=1 1 M M j=1 ∥Gact,i,j -Gs,i∥ 2 F weight variance Vw ← 1 M M i=1 1 M M j=1", "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "Vw,i,j update s with Vact and Vs according to Eq. 5 update {ρ l } L l=1 with new s according to Eq. 4 update {ν l } L l=1 with Vw and Vs according to Eq. 7 end if backward with SampleA and SampleW t ← t + 1 end while Note that to avoid falling into the pitfall of unfair comparison with baseline which is not tuned under efficient settings as is pointed out by <PERSON><PERSON><PERSON><PERSON> et al. (2021) and <PERSON><PERSON><PERSON> et al. (2023) , for all these experiments we use the same conservative setting of τ act = τ w = 0.025, α = 0.01, β = 0.95, M = 2. We preset all these values heuristically without any tuning or prior knowledge. The only hyperpamater we modified among different tasks is the variance calculation frequency F , which can be defined easily according to the total training steps.", "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "In fact, all the hyperparameters introduced by VCAS have explicit meanings and are insensitive. We show experimentally that though extra tuning may achieve a slightly better result, overall VCAS is robust to these hyperparameters with reasonable values. Please refer to Appendix. A for details about ablation studies on these insensitive hyperparameters.", "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "For SB and UB, we both adopt a sample ratio of 1/3, since it's the recommended setting in the original papers and it can achieve a FLOPs reduction of 1 -(1 + 2 * 1/3)/3 = 44.44% which is close to the results we get in most tasks. An exception is BERT-base pretraining task where we find the FLOPs reduction achievable is low so we manually set the sample ratio of SB and UB to get the same FLOPs reduction as VCAS, so that they can still give a decent result. Nevertheless we are indeed favoring these methods by helping them to define a reasonable sample ratio, which can not be done themselves.", "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "From the table we can see that overall VCAS is better than SB and UB with the least impact on final train loss and final evaluation accuracy. With FLOPs reduction of up to 49.58%, VCAS can still achieve nearly the same results with the exact counterpart.", "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "We record the wall-clock time of BERT-large finetuning on MNLI and ViT-large finetuning on ImageNet-1k with NVIDIA 3090Ti, the results are depicted in Tab. 2 and Tab. 3.", "section": "WALL-<PERSON>L<PERSON><PERSON> TIME REDUCTION", "sec_num": "6.2"}, {"text": "From these tables, we can find that VCAS can translate FLOPs reduction into wall-clock time reduction as effectively as simpler online batch sampling methods like UB and SB that drop part of The success of VCAS comes in two ways. One is the fine-grained sampling strategy that samples activation and weight jointly, which enables us to achieve much lower FLOPs given the variance budget. The other is the variance controlled framework combined with the self-adaptation algorithm, with which we are able to learn the proper sample ratios of different training phases. In the following two subsections, we will experimentally show the effectiveness of these two folds.", "section": "WALL-<PERSON>L<PERSON><PERSON> TIME REDUCTION", "sec_num": "6.2"}, {"text": "We compare VCAS that samples activation and weight jointly with strategies that solely sampling activation or weight. Specifically, we keep an equal extra variance for BERT-base finetuning on MNLI. We set τ act = τ w = 0.025 for VCAS, τ act = 0.05 for activation sampling only and τ w = 0.05 for weight sampling only. We find that under the preliminary that τ act , τ w ≪ 1, the results of these sampling strategies show no significant difference due to controlled variance. While as is shown in Fig. 4 , VCAS can achieve a much greater FLOPs reduction with the same total variance introduced. It's reasonable since we can utilize more sparsity in both data and token dimensions with a fine-grained sampling strategy of VCAS.", "section": "EFFECTIVENESS OF FINE-GRAINED SAMPLING", "sec_num": "6.3"}, {"text": "In Fig. 5 we plot the variance of different methods during training process of BERT-base finetuning on MNLI. We can find that VCAS is able to control the extra sampling variance introduced to our preset threshold, while for other variance-unaware algorithms like UB and SB, the extra variance is out of control with a similar FLOPs reduction.", "section": "EFFECTIVENESS OF VARIANCE CONTROL AND SELF-ADAPTATION", "sec_num": "6.4"}, {"text": "With carefully controlled variance, a similar convergence with exact training is guaranteed as we mentioned in the introduction. As is depicted in Fig. 1 and Fig. 6 for BERT-base finetuning on MNLI, VCAS shares nearly the same convergence trajectory with the exact training with reduced FLOPs, while UB converges slightly slower due to uncontrolled variance, and SB converges in an entirely different trajectory with variance introduced far larger than exact. ", "section": "EFFECTIVENESS OF VARIANCE CONTROL AND SELF-ADAPTATION", "sec_num": "6.4"}, {"text": "We propose VCAS, a robust sampling method for back propagation with controlled variance and self-adaptive sample ratios. VCAS computes an approximate stochastic gradient by applying finegrained sampling to gradually remove samples and tokens during backpropagation. VCAS enjoys similar variance, convergence trajectory, and final accuracy with exact back propagation, while reduces the training cost by up to 49.58%. ", "section": "CONCLUSION", "sec_num": "7"}, {"text": "There are a few hyperparameters in our self-adaptation algorithm, but all of them have explicit meaning. In this section we show that though extra tuning of these hyperparameters may achieve a slightly better result, overall VCAS is robust to these hyperparameters with reasonable values. We conduct ablation experiments on two tasks: BERT-base finetuning on SST-2 and MNLI. All the results are averaged over 3 different seeds.", "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "A.1 ACTIVATION AND WEIGHT VARIANCE THRESHOLDS τ act , τ w", "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "The main hyperparameters in VCAS is the variance thresholds of activation τ act and weight τ w . For these two thresholds, how to split total variance among them is a big problem with optimal solution differing across models and tasks. So without prior knowledge introduced, we compromise by keeping τ act = τ w = τ ≪ 1.", "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "We further conduct an ablation on τ from 0.01 to 0.5 as is shown in Tab. 4 for SST-2 and Tab. 5 for MNLI. From the results we can find that a satisfactory outcome is assured regardless of the specific value of τ provided that τ ≪ 1, which proves the robustness of VCAS. with a final accuracy drop of no more than 0.3% for both tasks. Thus, VCAS is robust to different α and β. From all the ablation results above, we can see that VCAS is robust to all these hyperparameters with reasonable values, proving the insensitiveness.", "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "B INSIGHTS ON UPDATE OF s, {ρ l } AND {ν l }", "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "In this section, we will show how the gradient norm preserving ratio s as well as all the sample ratios {ρ l } and {ν l } update across the training.", "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "We record the update process of BERT-base finetuning on MNLI with different variance tolerance thresholds τ as in Appendix. A.1. All results are averaged on three different seeds.", "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "Fig. 11a depicts the update of s. For non-decreasing {ρ l }, we plot the update of the first and the last values ρ 1 , ρ L in Fig. 11b , with other values lying between. For {ν l }, we show the update of the first three ones ν 1 , ν 2 , ν 3 in Fig. 11c and observe similar behavior of other weights.", "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "It is seen in Fig. 11 that during training of BERT-base on MNLI, the gradient norm preserving ratio s first decreases and then shows a slight downward trend. The activation sample ratios {ρ l } gradually decrease with an abrupt change between epochs due to the rapid decline of train loss caused by the lowered learning rate in the linear learning rate scheduler. The weight sample ratios {ν l } first decrease and then fluctuate to match the change of activation sample ratios.", "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "In Sec. 6, we mainly experiment with Transformer-based models and Adam optimizers. But the variance controlled adaptation depicted in Sec. 5 holds universally for any DNNs with SGD-based optimizers, since it just provides an approximated stochastic gradient with controlled variance to estimate the full gradient. In this section, we employ VCAS on other architectures and other optimizers to prove its versatility.", "section": "C PERFORMANCE ON CNN", "sec_num": null}, {"text": "Let's first consider a L-layer MLP. (Note: for simplicity we mildly abuse the term \"layer\" here, representing a single operation like matrix multiplication and ReLU)", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "For the last layer L, the output gradient ∇ Z (L) is calculated from the loss directly, the same as the Exact BP. Since activation sampler ∇Z (L) = SampleA ϵ,ρ L (∇ Z (L) ) is unbiased, we have:", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "E ∇Z (L) = ∇ Z (L)", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "When back propagation proceeds, we may encounter two types of layers: linear and non-linear. For the linear layer, we have:", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "∇ Z (L-1) = ∇Z (L) θ (L)", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "Thus unbiasedness is preserved with the output gradient of the (L -1)-th layer:", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "E [∇ Z (L-1) ] = E ∇Z (L) θ (L) = ∇ Z (L) θ (L) = Exact BP result", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "While for the non-linear layer like ReLU, we have:", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "∇ Z (L-1) = ∇Z (L) ⊙ J Z (L)", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "where ⊙ is the Had<PERSON>rd product and J Z (L) is the Jacobbi matrix determined by Z (L) which is saved in forward pass and is exact. Thus again we derive the the output gradient of the (L -1)-th layer being unbiased:", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "E [∇ Z (L-1) ] = E ∇Z (L) ⊙ J Z (L) = ∇ Z (L) ⊙ J Z (L) = Exact BP result", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "Thus by induction, VCAS assures all activation gradients ∇Z (l) , l = 1 . . . L being unbiased.", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "Then for weight gradients, since weight sampler ∇Z (l) = SampleW ξ l ,ν l ∇Z (l) , Z (l-1) is unbiased, we have:", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "E ∇Z (l) = E ∇Z (l) = ∇ Z (l)", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "Finally, we derive all weight gradients being unbiased:", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "E [∇ θ (l) ] = E ∇Z (l) ⊤ Z (l-1) = ∇ ⊤ Z (l) Z (l-1) = Exact BP result", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "For more complicated neural networks like CNN and Transformer, since operations like convolutions and layernorm are all linear transforms, by similar reasoning the unbiasedness still holds.", "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "F.1 BERT-BASE PRETRAINING For BERT-base pretraining we use a crammed BERT in Geiping & Goldstein (2022) with the recipe same as the original settings of 1 day training on a single NVIDIA 2080Ti. The full results are as follows in Tab. 9", "section": "F EXPERIMENT DETAILS", "sec_num": null}, {"text": "From the table we can find that although VCAS achieves a relatively high train loss, the downstream task performance is still competent with exact training. While SB and UB both perform worse on CoLA, which is a vulnerable task, reflecting that they have changed the original convergence trajectory of SGD. For BERT finetuning, we use AdamW optimizer with lr = 2e -5 and wd = 0.01. The learning rate scheduler is a linear one with warmup ratio = 0.1. We set epoch numbers N = 3 and a batch size of batch size = 32.", "section": "F EXPERIMENT DETAILS", "sec_num": null}, {"text": "For ViT finetuning, we use Adam optimizer with lr = 2e -5 . A linear lr scheduler with no warmup employed. We run N = 5 epochs with batch size batch size = 32", "section": "F EXPERIMENT DETAILS", "sec_num": null}, {"text": "VCAS is designed for adaptively learning the proper sample ratios of large model training on large datasets. It is not suitable for small models with low gradient variances resulting in increased numerical errors, or small datasets with few training steps that is insufficient for the update process in VCAS.", "section": "G LIMITATIONS", "sec_num": null}, {"text": "The weight sampler SampleW in VCAS is specially designed for linear layers and is not usable for other operations like convolution. But the activation sampler SampleA can be applied to all mainstream architectures with deep layers. So for CNN or RNN, we need to employ a degraded version of VCAS with activation sampling only, as shown in Appendix. C.", "section": "G LIMITATIONS", "sec_num": null}, {"text": "VCAS focuses on mirroring the exact training with theoretical guarantee and is lack of exploration of other possible convergence trajectories that may bring a better result. Thus it is not recommended when the original training recipe is under-optimized.", "section": "G LIMITATIONS", "sec_num": null}], "back_matter": [{"text": "The authors would like to thank <PERSON><PERSON><PERSON> and <PERSON><PERSON> for their valuable discussions and help on algorithm design and implementation details. This work was supported by the National Key Research and Development Program of China (No. 2021ZD0110502), NSFC Projects (Nos. 62376131, 62061136001, 62106123, 62076147, U19A2081, 61972224), Tsinghua Institute for Guo Qiang, and the High Performance Computing Center, Tsinghua University. J.Z is also supported by the XPlorer Prize.", "section": "ACKNOWLEDGEMENTS", "sec_num": null}, {"text": "Obviously bigger M will bring more precise empirical variance, yet the cost is prohibitive.We experiment on different M from 2 to 10 and find no significant difference in the empirical variance as is shown in Fig. 7 for SST-2 and Fig. 8 for MNLI. Therefore, we adopted the setting of M = 2, with which we only need to perform 6 extra iterations that is negligible if the variance calculation frequency is large enough like 100 in SST-2 and 500 in MNLI.", "section": "annex", "sec_num": null}, {"text": "Similar to M , the variance calculation frequency F is also a trade-off between better empirical approximation and less overhead introduced. We experimented on F = 50, 100, 200, 500, 1000 in Tab. 6 for SST-2 and Tab. 7 for MNLI. We can see that although as F grows larger the overhead of VCAS is gradually relieved, with a too large F , like F = 1000 in SST-2 that leads to only 6 times of self-adaptation update, the sample ratio schedule is not fully explored and the final FLOPs reduction is even smaller. Therefore, for all these tasks we set F to be at least 1/50 of total training steps and no more than 500 due to slight marginal gains. A simple grid search is conducted for α ∈ {0.005, 0.01, 0.02} and β ∈ {0.95, 0.9, 0.8} in Fig. 9 for SST-2 and Fig. 10 for MNLI. From the figures, we can find that we are able to trade convergence for efficiency with a more aggressive setting of larger α and smaller β, yet all results here are decent For CNN, it is noted that the weight sampler SampleW in Sec. 4 designed for linear layers is not usable for convolution layers. Thus we employ VCAS with a degraded version of activation sampling only.We experiment with WideResNet-18 with widen factor w = 4 pretraining on ImageNet. We use eight NVIDIA 3090Ti to parallel the training with Distributed Data Parallel(DDP). We employ SGDM optimizer with momentum m = 0.9. The results are in Tab. 8. D DETAILS ABOUT ALGORITHM. 1It should be noted that some parts of Alg. 1 are simplified for clarity and we list the implementation details below:In the algorithm table, we put the calculation of empirical variances out of the two Monte-Carlo loops for simplicity. Yet practically we can calculate V act and V w inside the loops and average the variance scalars outside. Therefore, we only need to store three tensors additionally regardless of M : SGD gradient G s,i to calculate V act , and its running mean and running square mean to calculate V s . By sampling only part of parameters to keep gradients, like 1% in our experiments, the memory overhead can be neglected.Besides, since weight sample ratios {ν l } are updated parameter-wise according to Eq. 7, the empirical weight variances and SGD variances are also stored parameter-wise when implemented.Update of activation sample ratios {ρ l } requires finding out gradient sparsity {p l } with the new s according to Eq. 4. In implementation, this is achieved by calculating possible new {ρ l } with both s + α and s -α inside the Monte-Carlo loops and averaging them outside. Then just choose the proper one with new s.", "section": "A.3 VARIANCE CALCULATION FREQUENCY F", "sec_num": null}], "ref_entries": {"FIGREF0": {"num": null, "text": "Figure 3: Gradient distribution over different layer and iterations of BERT-base finetuning on SST2 (6315 iterations in total). The normalized gradient norm of each datum is shown in the heatmaps. Black solid lines are the 95% percentile. Data above the lines are likely to be dicarded by VCAS.", "type_str": "figure", "uris": null, "fig_num": "3"}, "FIGREF1": {"num": null, "text": "Figure 4: FLOPs reduction ratio of VCAS vs. sampling activation or weight solely with equal variance.", "type_str": "figure", "uris": null, "fig_num": "45"}, "FIGREF2": {"num": null, "text": "Figure 6: Convergence comparison of different sampling methods. FLOPs is normalized by exact training.", "type_str": "figure", "uris": null, "fig_num": "6"}, "FIGREF3": {"num": null, "text": "Figure 9: Grid search of s update step α and weight ratio multiplier β of BERT-base finetuning on SST-2. The darker color the better.", "type_str": "figure", "uris": null, "fig_num": "910"}, "TABREF1": {"num": null, "content": "<table><tr><td/><td/><td>Layer 𝑙</td><td/></tr><tr><td/><td>𝑁</td><td>∇</td><td>∇</td><td>∇</td></tr><tr><td/><td/><td>𝑆𝑎𝑚𝑝𝑙𝑒𝐴 ,</td><td>ℎ</td></tr><tr><td>Layer 𝑙 1</td><td>𝑇</td><td/><td/><td>Layer 𝑙 1</td></tr><tr><td/><td>𝐾</td><td/><td/></tr><tr><td/><td/><td/><td>∇</td></tr><tr><td/><td/><td/><td>∇</td></tr><tr><td/><td/><td>𝑆𝑎𝑚𝑝𝑙𝑒𝑊 ,</td><td>𝑔</td></tr><tr><td/><td>𝑁𝑇</td><td/><td/></tr><tr><td colspan=\"5\">Figure 2: this importance score in parallel. Johnson &amp; Guestrin (2018) uses a second-order approximation of</td></tr><tr><td colspan=\"5\">gradient norm with history maintained. Closely related to our work, Katharopoulos &amp; Fleuret (2018)</td></tr><tr><td colspan=\"5\">develops a pure online algorithm by constructing an upper bound of gradient norm to sample with</td></tr><tr><td colspan=\"5\">much cheaper computation. These methods are usually more expensive but have relatively strong</td></tr><tr><td colspan=\"5\">theoretical guarantees. So we follow this way in our activation sampling.</td></tr></table>", "type_str": "table", "text": "The computing diagram of backpropagation with VCAS in every layer. We use light blue squares to represent small gradient entries and orange for large ones. White squares are discarded by sampling. The upper line calculates activation gradient and the lower for weight gradient. Please refer to Sec. 4 for notations.", "html": null}, "TABREF2": {"num": null, "content": "<table><tr><td>Task</td><td>Dataset</td><td>exact</td><td>SB</td><td>UB</td><td>VCAS</td></tr><tr><td>BERT-base</td><td>C4</td><td>2.099 / 78.37</td><td>2.133 / 77.53</td><td>2.106 / 77.96</td><td>2.134 / 78.36 / 21.58</td></tr><tr><td>pretraining</td><td/><td/><td/><td/><td/></tr><tr><td/><td>MNLI-m</td><td colspan=\"4\">0.2372 / 84.33 0.3833 / 83.71 0.2957 / 83.82 0.2428 / 84.23 / 41.56</td></tr><tr><td>BERT-base</td><td>QQP</td><td colspan=\"4\">0.1143 / 91.00 0.1441 / 90.76 0.1964 / 89.53 0.1189 / 90.92 / 47.10</td></tr><tr><td>finetuning</td><td>QNLI</td><td colspan=\"4\">0.1014 / 91.67 0.2017 / 90.58 0.1441 / 91.23 0.1056 / 91.29 / 44.45</td></tr><tr><td/><td>SST-2</td><td colspan=\"4\">0.0559 / 92.59 0.0727 / 92.63 0.0743 / 92.82 0.0600 / 93.04 / 48.28</td></tr><tr><td/><td>MNLI-m</td><td colspan=\"4\">0.1439 / 86.58 0.2492 / 85.18 0.2266 / 86.09 0.1619 / 86.63 / 44.17</td></tr><tr><td>BERT-large</td><td>QQP</td><td colspan=\"4\">0.0885 / 91.64 0.1308 / 91.20 0.1751 / 90.51 0.0962 / 91.57 / 49.50</td></tr><tr><td>finetuning</td><td>QNLI</td><td colspan=\"4\">0.0877 / 92.02 0.1436 / 91.50 0.1325 / 91.98 0.0640 / 92.15 / 46.19</td></tr><tr><td/><td>SST-2</td><td colspan=\"4\">0.0537 / 93.60 0.1136 / 91.81 0.0838 / 93.40 0.0593 / 93.67 / 49.24</td></tr><tr><td>ViT-base finetuning</td><td colspan=\"5\">CIFAR10 CIFAR100 ImageNet-1k 0.6032 / 82.27 0.6533 / 82.09 0.6109 / 82.28 0.6089 / 82.27 / 45.29 0.1868 / 98.92 0.2367 / 98.82 0.1923 / 98.94 0.1873 / 98.90 / 45.90 0.8760 / 91.19 2.248 / 89.60 1.175 / 89.68 0.8811 / 91.08 / 29.32</td></tr><tr><td>ViT-large finetuning</td><td colspan=\"5\">CIFAR10 CIFAR100 ImageNet-1k 0.4135 / 82.04 0.4637 / 82.21 0.4242 / 82.21 0.4228 / 82.27 / 49.58 0.1359 / 99.24 0.1439 / 99.21 0.1378 / 99.17 0.1393 / 99.28 / 48.37 0.4590 / 93.56 0.5983 / 93.07 0.5170 / 93.36 0.4649 / 93.64 / 38.67</td></tr></table>", "type_str": "table", "text": "Comparison of VCAS with other methods. Data format is Final Train Loss / Final Eval Acc.(%) for exact, SB and UB, and Final Train Loss / Final Eval Acc.(%) / FLOPs reduction ratio(%) for VCAS. The FLOPs reduction of SB and UB is 21.58% for BERT pretraining and 44.44% for other tasks. VCAS's FLOPs take account of the adaptation overhead. For BERT pretraining, accuracy=average performance on GLUE. Bold indicates the best result of each metric except for exact. Underline means Eval Acc less than 0.1% off the exact training.", "html": null}, "TABREF3": {"num": null, "content": "<table><tr><td colspan=\"6\">Method Train Loss Eval Acc.(%) Wall-clock Time(h) FLOPs↓(%) Time↓(%)</td></tr><tr><td>exact</td><td>0.1439</td><td>86.58</td><td>5.478</td><td>-</td><td>-</td></tr><tr><td>SB</td><td>0.2492</td><td>85.18</td><td>4.320</td><td>44.44</td><td>21.14</td></tr><tr><td>UB</td><td>0.2266</td><td>86.09</td><td>4.266</td><td>44.44</td><td>22.12</td></tr><tr><td>VCAS</td><td>0.1619</td><td>86.63</td><td>4.437</td><td>44.17</td><td>19.00</td></tr></table>", "type_str": "table", "text": "Wall-clock time of BERT-large finetuning on MNLI.", "html": null}, "TABREF4": {"num": null, "content": "<table><tr><td colspan=\"6\">Method Train Loss Eval Acc.(%) Wall-clock Time(h) FLOPs↓(%) Time↓(%)</td></tr><tr><td>exact</td><td>0.4135</td><td>82.04</td><td>52.29</td><td>-</td><td>-</td></tr><tr><td>SB</td><td>0.4637</td><td>82.21</td><td>42.56</td><td>44.44</td><td>18.61</td></tr><tr><td>UB</td><td>0.4242</td><td>82.21</td><td>41.92</td><td>44.44</td><td>19.83</td></tr><tr><td>VCAS</td><td>0.4228</td><td>82.27</td><td>41.28</td><td>49.58</td><td>21.06</td></tr><tr><td colspan=\"6\">data one-time in a whole, while enjoying mirrored performance with the exact training under theo-</td></tr><tr><td>retical guarantee.</td><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "text": "Wall-clock time of ViT-large finetuning on ImageNet-1k.", "html": null}, "TABREF5": {"num": null, "content": "<table><tr><td><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON></td></tr><tr><td><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Adaselection: Accelerating deep learning training through</td></tr><tr><td>data subsampling. arXiv preprint arXiv:2306.10728, 2023.</td></tr><tr><td><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Accelerating training of transformer-based language models with</td></tr><tr><td>progressive layer dropping. Advances in Neural Information Processing Systems, 33:14011-</td></tr><tr><td>14023, 2020.</td></tr><tr><td><PERSON><PERSON><PERSON> and <PERSON>. Stochastic optimization with importance sampling for regularized loss</td></tr><tr><td>minimization. In international conference on machine learning, pp. 1-9. PMLR, 2015.</td></tr></table>", "type_str": "table", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Choosing the sample with lowest loss makes sgd robust. In International Conference on Artificial Intelligence and Statistics, pp. 2120-2130. PMLR, 2020. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Random-ltd: Random and layerwise token dropping brings efficient training for large-scale transformers. arXiv preprint arXiv:2211.11586, 2022.", "html": null}, "TABREF6": {"num": null, "content": "<table><tr><td>τ</td><td>0(exact)</td><td>0.01</td><td>0.025</td><td>0.05</td><td>0.1</td><td>0.25</td><td>0.5</td></tr><tr><td>Final Train Loss</td><td colspan=\"7\">0.0559 0.0586 0.0600 0.0625 0.0642 0.0705 0.0761</td></tr><tr><td>Final Eval Acc(%)</td><td>92.59</td><td>93.07</td><td>93.04</td><td>93.25</td><td>92.81</td><td>92.79</td><td>92.18</td></tr><tr><td>FLOPs reduction(%)</td><td>-</td><td>45.92</td><td>48.28</td><td>49.82</td><td>50.05</td><td>51.57</td><td>52.71</td></tr><tr><td colspan=\"8\">Table 5: Ablation on different variance thresholds τ of BERT-base finetuning on MNLI</td></tr><tr><td>τ</td><td>0(exact)</td><td>0.01</td><td>0.025</td><td>0.05</td><td>0.1</td><td>0.25</td><td>0.5</td></tr><tr><td>Final Train Loss</td><td colspan=\"7\">0.2372 0.2388 0.2428 0.2459 0.2552 0.2684 0.2805</td></tr><tr><td>Final Eval Acc(%)</td><td>84.33</td><td>84.31</td><td>84.23</td><td>84.33</td><td>84.07</td><td>84.13</td><td>84.08</td></tr><tr><td>FLOPs reduction(%)</td><td>-</td><td>38.59</td><td>41.56</td><td>43.49</td><td>45.37</td><td>47.53</td><td>48.92</td></tr><tr><td colspan=\"3\">A.2 MONTE-CARLO REPETITIONS M</td><td/><td/><td/><td/><td/></tr><tr><td colspan=\"8\">To calculate variances, VCAS introduces an overhead of extra iterations quadratic with Monte-Carlo</td></tr><tr><td>repetitions M .</td><td/><td/><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "text": "Ablation on different variance thresholds τ of BERT-base finetuning on SST-2", "html": null}, "TABREF7": {"num": null, "content": "<table><tr><td>exact</td><td>2.099</td><td>82.28</td><td>82.68</td><td>87.08 88.85 91.28 48.07 83.26</td><td>86.98 54.87 78.37</td></tr><tr><td>SB</td><td>2.133</td><td>82.34</td><td>82.86</td><td>87.27 88.63 91.28 41.82 82.86</td><td>85.53 55.23 77.53</td></tr><tr><td>UB</td><td>2.106</td><td>82.95</td><td>83.46</td><td>87.27 88.66 91.05 42.80 83.68</td><td>85.90 55.95 77.96</td></tr><tr><td>VCAS</td><td>2.134</td><td>82.03</td><td>82.82</td><td>86.92 89.23 91.62 48.36 83.02</td><td>86.03 55.23 78.36</td></tr><tr><td colspan=\"4\">F.2 RECIPE OF OTHER TASKS</td><td/><td/></tr></table>", "type_str": "table", "text": "Full results on BERT-base pretraining Methods Loss MNLI-m MNLI-mm QQP QNLI SST2 CoLA STSB MRPC RTE Avg.", "html": null}}}}