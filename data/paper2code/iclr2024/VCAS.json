{"paper_id": "VCAS", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T21:52:30.955531Z"}, "title": "EFFICIENT BACKPROPAGATION WITH VARIANCE-CONTROLLED ADAPTIVE SAMPLING", "authors": [{"first": "Ziteng", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": "jian<PERSON><PERSON>@tsinghua.edu.cn"}, {"first": "Jun", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Sampling-based algorithms, which eliminate \"unimportant\" computations during forward and/or back propagation (BP), offer potential solutions to accelerate neural network training. However, since sampling introduces approximations to training, such algorithms may not consistently maintain accuracy across various tasks. In this work, we introduce a variance-controlled adaptive sampling (VCAS) method designed to accelerate BP. VCAS computes an unbiased stochastic gradient with fine-grained layerwise importance sampling in data dimension for activation gradient calculation and leverage score sampling in token dimension for weight gradient calculation. To preserve accuracy, we control the additional variance by learning the sample ratio jointly with model parameters during training. We assessed VCAS on multiple fine-tuning and pre-training tasks in both vision and natural language domains. On all the tasks, VCAS can preserve the original training loss trajectory and validation accuracy with an up to 73.87% FLOPs reduction of BP and 49.58% FLOPs reduction of the whole training process. The implementation is available at https://github.com/thu-ml/VCAS.", "pdf_parse": {"paper_id": "VCAS", "_pdf_hash": "", "abstract": [{"text": "Sampling-based algorithms, which eliminate \"unimportant\" computations during forward and/or back propagation (BP), offer potential solutions to accelerate neural network training. However, since sampling introduces approximations to training, such algorithms may not consistently maintain accuracy across various tasks. In this work, we introduce a variance-controlled adaptive sampling (VCAS) method designed to accelerate BP. VCAS computes an unbiased stochastic gradient with fine-grained layerwise importance sampling in data dimension for activation gradient calculation and leverage score sampling in token dimension for weight gradient calculation. To preserve accuracy, we control the additional variance by learning the sample ratio jointly with model parameters during training. We assessed VCAS on multiple fine-tuning and pre-training tasks in both vision and natural language domains. On all the tasks, VCAS can preserve the original training loss trajectory and validation accuracy with an up to 73.87% FLOPs reduction of BP and 49.58% FLOPs reduction of the whole training process. The implementation is available at https://github.com/thu-ml/VCAS.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "and UB (Kathar<PERSON><PERSON> & F<PERSON>t, 2018) fail with a similar FLOPs reduction.", "cite_spans": [{"start": 7, "end": 38, "text": "(Kathar<PERSON>ou<PERSON> & Fleuret, 2018)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Training neural networks can be computationally intensive. Contemporary networks typically employ stochastic gradient methods (<PERSON><PERSON><PERSON> et al., 2018) for training, which iteratively process batches of data to compute stochastic gradients through forward propagation (FP) and back propagation (BP) techniques (<PERSON><PERSON><PERSON><PERSON> et al., 1986) . FP+BP are costly, as they need to process every datum in the batch and every connection in the network, resulting in a multiplicative time complexity of batch size and model size. Such a time complexity becomes increasingly problematic in the era of big data and big models.", "cite_spans": [{"start": 126, "end": 147, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF1"}, {"start": 306, "end": 330, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 1986)", "ref_id": "BIBREF30"}], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Data samples are not equally important. Some might be easy for the network to learn, while others might be extremely hard. Training can be accelerated by utilizing this disparity, focusing the available computational resources on more pivotal samples. At a high level, this can be achieved by further sampling the batch with higher keep probability of more important samples. The computational overhead is consequently diminished, in proportion to the quantity of retained samples. Various methods are proposed to assess the importance of samples, including meta-learning methods (<PERSON> et al., 2017; <PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2022) , loss-based methods (<PERSON> & <PERSON>, 2015; <PERSON> et al., 2017; <PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2022) , and gradient norm based methods (<PERSON><PERSON> et al., 2014; <PERSON> & <PERSON>, 2015; <PERSON> et al., 2015; <PERSON> & <PERSON>, 2018; Kathar<PERSON><PERSON> & F<PERSON>t, 2018) .", "cite_spans": [{"start": 580, "end": 598, "text": "(<PERSON> et al., 2017;", "ref_id": "BIBREF9"}, {"start": 599, "end": 620, "text": "<PERSON> et al., 2019;", "ref_id": "BIBREF5"}, {"start": 621, "end": 645, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF23"}, {"start": 667, "end": 694, "text": "(<PERSON>h<PERSON><PERSON> & Hutter, 2015;", "ref_id": "BIBREF21"}, {"start": 695, "end": 714, "text": "<PERSON> et al., 2017;", "ref_id": "BIBREF2"}, {"start": 715, "end": 734, "text": "<PERSON> et al., 2019;", "ref_id": "BIBREF15"}, {"start": 735, "end": 755, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF27"}, {"start": 790, "end": 812, "text": "(<PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF24"}, {"start": 813, "end": 832, "text": "<PERSON> & <PERSON>, 2015;", "ref_id": null}, {"start": 833, "end": 852, "text": "<PERSON> et al., 2015;", "ref_id": "BIBREF0"}, {"start": 853, "end": 878, "text": "<PERSON> & Guestrin, 2018;", "ref_id": "BIBREF16"}, {"start": 879, "end": 909, "text": "Kat<PERSON><PERSON><PERSON><PERSON> & Fleuret, 2018)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "While such methods seem promising, one core concern of sampling-based methods is their robustness. Misjudging the importance can hamper convergence, potentially leading to degraded accuracy and even longer training time than uniform sampling. Moreover, the optimal sample ratio is influenced by data distribution, which differs between tasks and is challenging to determine in advance.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "In general, there is a \"no-free-lunch\" phenomenon (<PERSON><PERSON><PERSON> et al., 2023) , where aggressive sampling often comes at the cost of reduced robustness.", "cite_spans": [{"start": 50, "end": 72, "text": "(<PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "In this work, we propose a robust variance-controlled adaptive sampling (VCAS) algorithm for deep learning under the stochastic optimization framework. VCAS computes a cost-effective approximated stochastic gradient (ASG) by partially conducting backpropagation for specific data and tokens. This ASG is unbiased, and we have developed an adaptive sampling method to meticulously control the variance of the ASG, aligning it with the original stochastic gradient's variance. Consequently, convergence remains largely unaffected, with our method mirroring the progression of exact algorithms, as delineated in Fig. 1 .", "cite_spans": [], "ref_spans": [{"start": 614, "end": 615, "text": "1", "ref_id": null}], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Unlike previous methods, VCAS construct the ASG in a fine-grained manner. Rather than dropping samples one-time in a whole, VCAS gradually drops more samples when backpropagating from topmost to bottommost network layers, as the gradient getting sparser. Furthermore, VCAS also more aggressively drops data in finer granularity of tokens rather than samples when computing the weight gradients. VCAS can achieve smaller variance under a given computational budget compared to coarse grained sampling on the data dimension.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "We evaluate VCAS on multiple finetuning and pre-training tasks of language models and vision transformers. VCAS can preserve the original training loss trajectory and the validation accuracy on all tasks, while adaptively determining the computational saving depending on the difficulty of the task. VCAS can reduce the computational cost of backpropagation by up to 73.87%, and reduce the overall training computation by up to 49.58%.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Methods focusing on the difference of data, known as online batch selection (<PERSON><PERSON><PERSON><PERSON> & Hutter, 2015) , can be mainly categorized into three classes: meta learning methods, loss based methods and gradient norm based methods. In this section we will discuss these three ways separately and briefly introduce other orthogonal efficient training methods.", "cite_spans": [{"start": 76, "end": 103, "text": "(<PERSON><PERSON><PERSON><PERSON> & Hutter, 2015)", "ref_id": "BIBREF21"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "Meta Learning Methods. Some works formulate data sampling into an optimization problem and train a separate meta predictor to solve it. <PERSON> et al. (2017) use deep reinforcement learning to train an agent for data selection. <PERSON> et al. (2019) and <PERSON><PERSON><PERSON> et al. (2022) train a separate cheaper model with similar architecture for guidance. However, training a meta predictor will introduce further overhead and it's a non-trivial learning task with more uncertainty introduced for weak theoretical guarantee.", "cite_spans": [{"start": 136, "end": 153, "text": "<PERSON> et al. (2017)", "ref_id": "BIBREF9"}, {"start": 224, "end": 249, "text": "<PERSON> et al. (2019) and", "ref_id": "BIBREF5"}, {"start": 250, "end": 274, "text": "<PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF23"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "Loss Based Methods. Loss is a natural indicator of the importance of different data. <PERSON><PERSON><PERSON>lov & Hutter (2015) maintains a history of losses and develops a sophisticated distribution based on the value or rank of loss. <PERSON> et al. (2019) and <PERSON><PERSON><PERSON> et al. (2022) simplify it with sampling distribution proportion to the percentile of loss in the history. <PERSON> et al. (2017) broadens the history to every datum and proposes to sample by the variance of prediction probability directly linked with previous losses. <PERSON> et al. (2021) provides another method of minimizing the L 2 norm between the sampled loss and the exact counterpart. <PERSON> et al. (2020) samples the smallest loss for robustness to outliers. <PERSON> et al. (2023) ensembles several loss methods with a preset sample ratio and varies the weights assigned to these methods adaptively. Simple and effective as they may be, the loss based methods are heuristic and always need a hyperparameter of sample ratio to tune for different tasks, violating the goal of efficient training.", "cite_spans": [{"start": 85, "end": 111, "text": "<PERSON><PERSON><PERSON><PERSON> & Hutter (2015)", "ref_id": "BIBREF21"}, {"start": 220, "end": 239, "text": "<PERSON> et al. (2019)", "ref_id": "BIBREF15"}, {"start": 244, "end": 264, "text": "<PERSON><PERSON><PERSON> et al. (2022)", "ref_id": "BIBREF27"}, {"start": 357, "end": 376, "text": "<PERSON> et al. (2017)", "ref_id": "BIBREF2"}, {"start": 516, "end": 534, "text": "<PERSON> et al. (2021)", "ref_id": "BIBREF7"}, {"start": 638, "end": 656, "text": "<PERSON> et al. (2020)", "ref_id": null}, {"start": 711, "end": 730, "text": "<PERSON> et al. (2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "Gradient Norm Based Methods. Previous works have proved that the optimal data sampling distribution for SGD is proportional to the gradient norm (<PERSON><PERSON> et al., 2014; <PERSON> & <PERSON>, 2015) . But calculating the gradient norm is prohibitive since it needs a full process of backpropagation. Orthogonal Efficient Training Methods. Data pruning (<PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2022) focuses on filtering less informative data before the whole training. Architecture pruning like layer dropping (<PERSON> et al., 2016; <PERSON> & <PERSON>, 2020) and token dropping (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2022) modifies the architecture to make models faster to train with modest affect to performance. Mixed precision training and quantization (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018; <PERSON> et al., 2021; <PERSON> et al., 2022) change the training procedure to use low-precision in calculation for acceleration. Sparsity (<PERSON><PERSON><PERSON> et al., 2021) focuses on pruning near-zero values in weights, activations, or gradients to achieve a low FLOPs(<PERSON><PERSON> & <PERSON>, 2020) and low memory footprint (<PERSON><PERSON> et al., 2023) , yet is usually hard to bring a wall-clock time reduction like us due to the lack of hardware support (NVIDIA, 2021) . All these works are orthogonal to our work since we focus on the computation approximation of a certain model architecture on a certain dataset with a certain training procedure to bring real training acceleration.", "cite_spans": [{"start": 145, "end": 167, "text": "(<PERSON><PERSON> et al., 2014;", "ref_id": "BIBREF24"}, {"start": 168, "end": 187, "text": "<PERSON> & <PERSON>, 2015)", "ref_id": null}, {"start": 341, "end": 360, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF28"}, {"start": 361, "end": 381, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF10"}, {"start": 493, "end": 513, "text": "(<PERSON> et al., 2016;", "ref_id": "BIBREF14"}, {"start": 514, "end": 531, "text": "<PERSON> & He, 2020)", "ref_id": null}, {"start": 551, "end": 569, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF13"}, {"start": 570, "end": 587, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF19"}, {"start": 588, "end": 604, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF19"}, {"start": 739, "end": 766, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2018;", "ref_id": null}, {"start": 767, "end": 785, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF3"}, {"start": 786, "end": 803, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF20"}, {"start": 897, "end": 919, "text": "(<PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF12"}, {"start": 1065, "end": 1086, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF25"}, {"start": 1190, "end": 1204, "text": "(NVIDIA, 2021)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "In this section, we present a high-level overview of our sampling algorithm as stochastic optimization. Consider the learning problem of a model f (X; θ) parameterized by θ on a dataset D = {(X i , y i )} |D| i=1 with a loss function ℓ(•, •). Define the learning objective as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "L(θ) = E B [ℓ(f (X; θ), y)] ,", "eq_num": "(1)"}], "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "where the expectation is taken over all possible batches B = (X, y) from D. The model parameters can be learned by stochastic optimization algorithms (<PERSON><PERSON><PERSON> et al., 2018) with a stochastic gradient (SG) g(θ; B) := ∇ θ ℓ(f (X; θ), y), which is an unbiased approximation of ∇ θ L(θ).", "cite_spans": [{"start": 150, "end": 171, "text": "(<PERSON><PERSON><PERSON> et al., 2018)", "ref_id": "BIBREF1"}], "ref_spans": [], "eq_spans": [], "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "However, computing the stochastic gradient can be still too expensive, since it requires the full forward and back propagation, which iterate over all model parameters and all data in the batch. We build a cheap stochastic approximation g(θ; B, ϵ) of the SG, which we refer as approximated stochastic gradient (ASG). ASG only computes the backpropagation partially, and is therefore cheaper than the SG. The randomness in the computing procedure of ASG is captured by ϵ. We ensure that ASG is unbiased: E ϵ [g(θ; B, ϵ)] = g(θ; B). With an unbiased SG, stochastic optimization algorithms are guaranteed to converge to a stationary point of Eq. ( 1), while the converge speed depends on the variance (cf. <PERSON> et al. ( 2018)). Therefore, if the variance of the ASG can be controlled to the similar variance level of SG, substituting the SG with ASG should have little impact to the convergence behavior. In fact, by the law of total variance (<PERSON>, 2001) , the variance of ASG can be decoupled as", "cite_spans": [{"start": 942, "end": 955, "text": "(<PERSON>, 2001)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "Var [g(θ; B, ϵ)] = Var [g(θ; B)] + E B [Var ϵ [g(θ; B, ϵ)]] ,", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "where the first term is the intrinsic variance of SG caused by subsampling batches from the dataset, and the second term is the additional variance incurred by ASG. In the subsequent sections, we will discuss our constructions of the ASG, which incurs negligible additional variance compared to SG.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "VARIANCE-CONTROLLED SAMPLING AS STOCHASTIC OPTIMIZATION", "sec_num": "3"}, {"text": "Here we present variance-controlled adaptive sampling (VCAS), a specific construction of the ASG. We compute ASG by approximating the backpropagation in a fine-grained manner, and speed up matrix multiplications with importance sampling on the data dimension.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "Assume a batch X of shape N × T × K, where N is the batch size, T is the number of tokens of each datum, and K is the dimensionality. For an L-layer network , the model f (X; θ) can be described by the following forward propagation procedure: L) , where Z (l) and θ (l) are the activation and parameters of the l-th layer, and θ = (θ (l) ) L l=1 . The SG can be computed by back-propagation in the following form: l) , where ∇ Z (l) and ∇ θ (l) denote the activation / weight gradient, h (l) and g (l) denote the function that calculates input / weight gradient of layer l with the output gradient, layer input and weight. The SG g(θ; B) = (∇ θ (l) ) L l=1 . As illustrated by Fig. 3 , the activation gradients ∇ Z (l) are sparse: the gradient (∇ Z (l) ) i is close to zero for most sample i, except for a few important samples. Such sparsity becomes more prominent as backpropagating to lower layers and as the training progresses. To speed up computation, we add samplers in the backpropagation graph:", "cite_spans": [], "ref_spans": [{"start": 682, "end": 683, "text": "3", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "Z (0) = X, Z (l) = f (l) Z (l-1) ; θ (l) , f (X; θ) = Z (", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "∇ Z (l-1) = h (l) ∇ Z (l) ; Z (l-1) , θ (l) , ∇ θ (l) = g (l) ∇ Z (l) ; Z (l-1) , θ (", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "∇Z (l) = SampleA ϵ,ρ l (∇ Z (l) ) , ∇ Z (l-1) = h (l)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "∇Z (l) ; Z (l-1) , θ (l) , l) .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "∇Z (l) = SampleW ξ l ,ν l ∇Z (l) , Z (l-1) , ∇ θ (l) = g (l) ∇Z (l) ; Z (l-1) , θ", "eq_num": "("}], "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "(2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "The sampler SampleA ϵ,ρ l (•) randomly filter out unimportant data from the activation gradient, the keep ratio is ρ l , with the randomness captured by ϵ. The sampler is applied for each layer, so the activation gradient becomes increasingly sparse when backpropagating from the L-th layer to the first layer. The sampler SampleW ξ l ,ν l (•) filters (data, token) pairs specifically for weight gradient calculation, with a keep ratio ν l and the randomness ξ l . With these samplers, we only need to compute backpropagation for the retained data / token, so the computational cost is reduced. The sampling procedure is illustrated in Fig. 2 , which constructs an unbiased ASG g(θ; B, ϵ, ξ, ρ, ν) = (∇ θ (l) ) L l=1 , with ∇ θ (l) defined as Eq. ( 2), and", "cite_spans": [], "ref_spans": [{"start": 641, "end": 642, "text": "2", "ref_id": null}], "eq_spans": [], "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "ξ = (ξ l ) L l=1 , ρ = (ρ) L l=1 , ν = (ν l ) L l=1 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FINE-GRAINED SAMPLING", "sec_num": "4"}, {"text": "We apply unbiased low-variance approximation to the activation gradient to speed up subsequent computation. For an activation gradient tensor G of shape N × T × K, we sample", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACTIVATION GRADIENT", "sec_num": "4.1"}, {"text": "Ĝ = SampleA ϵ,ρ (G) = G • (m(ϵ, ρ) ⊗ 1 ⊗ 1),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACTIVATION GRADIENT", "sec_num": "4.1"}, {"text": "where • is element-wise product, and ⊗ is tensor outer product. The mask m ∈ R N is a random <PERSON><PERSON><PERSON> vector: m(ϵ, ρ) i = Bern(p i ; ϵ)/p i , where N i=1 p i = N ρ, and <PERSON>(p; ϵ) denotes a <PERSON><PERSON><PERSON> random number generator with probability p and randomness ϵ. Since E[m(ϵ, ρ) i ] = 1, ∀i, the approximation is unbiased: E[ Ĝ] = G. The sampler zeros out the gradient for all the data whose m(ϵ, ρ) i = 0. The amount of retained data is N ρ in expectation. With the sampler, we only need to compute backpropagation for retained data, so the cost is ρ times lower.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACTIVATION GRADIENT", "sec_num": "4.1"}, {"text": "Var Ĝ = N i=1 1-pi pi ∥G i ∥ 2 F", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "The variance of the approximation is", "sec_num": null}, {"text": ", where we define the variance of a random tensor element-wise as Var Ĝ = ijk Var Ĝijk , and G i denotes the i-th matrix of G in the N dimension. We compute the keep probability (p i ) to minimize the variance, deriving a distribution proportional to the gradient norm of each datum: p i ∝ ∥G i ∥ F . Minimizing the variance of the activation gradient not necessarily minimize the variance of ASG, which is the gradient of parameters. Nevertheless, this is a useful heuristic which empirically achieves low variance as is revealed by <PERSON><PERSON><PERSON><PERSON> & Fleuret (2018) , and the ASG variance will be carefully controlled by our adaptive algorithm, as we shall see soon in Sec. 5.", "cite_spans": [{"start": 534, "end": 564, "text": "Kat<PERSON><PERSON><PERSON><PERSON> & Fleuret (2018)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "The variance of the approximation is", "sec_num": null}, {"text": "We can accelerate the computation of weight gradient for linear layers by sampling in both data and token dimensions. Consider the approximate back propagation of a linear layer Z (l) = Z (l-1) θ (l) ⊤ :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "∇Z (l) = SampleA ϵ,ρ l (∇ Z (l) ) , ∇Z (l) = SampleW ξ l ,ν l ∇Z (l) , Z (l-1) , ∇ θ (l) = ∇⊤ Z (l) Z (l-1)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "in matrix form, where we reshape the activation/gradients to N T ×K, and ∇Z (l) is already a sampled matrix with only N T ρ l non-zero rows in expectation. However, ∇Z (l) is only sampled in the data dimension. In fact, even ( ∇Z (l) ) i is retained for some datum i, it might still have some rows (i.e., tokens) which are close to zero. We can further sample", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "∇Z (l) = SampleW ξ l ,ν l ∇Z (l) , Z (l-1) = ∇Z (l) • (m(ξ, ν) ⊤ 1),", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "where the mask m ∈ R N L is a random <PERSON><PERSON><PERSON> vector, and 1 is an all-one vector: m(ξ, ν) i = Bern(q i ; ϵ)/q i , where", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "N T i=1 q i = N T ρ l ν l . The variance is Var ∇θ (l) = N T i=1 1 -q i q i ∇Z (l) i 2 2 Z (l-1) i 2 2 . (", "eq_num": "3"}], "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": ")", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "The minimal variance solution is", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "q i ∝ ∇Z (l) i 2 Z (l-1) i 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": ". This sampling method is also known as leverage score sampling in randomized numerical linear algebra (Drineas & Mahoney, 2018) .", "cite_spans": [{"start": 103, "end": 128, "text": "(Drineas & Mahoney, 2018)", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "WEIGHT GRADIENT", "sec_num": "4.2"}, {"text": "The question remained is how to set the sample ratios (ρ l ) L l=1 and (ν l ) L l=1 . There is a tradeoff: lowering the sample ratio reduces the computational cost, but increases the variance. As discussed in Sec. 3, this ratio should be set to ensure that the additional variance of ASG is marginal compared to the original variance of SG. Adapting the sample ratio is nontrivial since the gradient sparsity pattern vary across layers and vary over time during training. In this section, we present an adaptation algorithm to control the variance during the entire training trajectory.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "First, we introduce a single hyperparameter s to control the sample ratios (ρ l ) L l=1 for all layers. Intuitively, when the gradient norm (∥G i ∥ F ) N i=1 becomes sparser, we can more aggressively utilize smaller keep ratio ρ l to maximize speedup. Therefore, we compute ρ l based on the sparsity p l of the gradient norm sequence:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "p l (s) = min{n/N | n i=1 ∥G i ∥ F ≥ s N i=1 ∥G i ∥ F }, ρ l (s) = max j≤l p j (s)", "eq_num": "(4)"}], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "where s ∈ [0, 1] is a hyperparameter on how much gradient norm is preserved. It's shown in Fig. 3 that gradient norm grows sparser with layer, yielding a descending trend of p l for l from L to 1. Thus it's reasonable to construct a monotone increasing sequence of {ρ l } L l=1 based on {p l } L l=1 . By law of total variance, we can decompose the variance of ASG as", "cite_spans": [], "ref_spans": [{"start": 96, "end": 97, "text": "3", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "Var [g(θ; B, ϵ, ξ, ρ, ν)] = Var [g(θ; B)] + E B [Var ϵ [g(θ; B, ϵ, ρ(s))]] + E B,ϵ [Var ξ [g(θ; B, ϵ, ξ, ρ, ν]],", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "where we write g(θ; B, ϵ, ρ) := E ξ [g(θ; B, ϵ, ξ, ρ, ν)] to be the ASG without the sampler for weight gradient computation. The three variance terms are the SG variance, the variance introduced by approximately computing activation gradient, and the variance introduced by approximately computing weight gradient, respectively. Our algorithm adaptively tunes s and ν during train to control the last two variance terms to be fractional comparing to the first variance term.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "Controlling E B [Var ϵ [g(θ; B, ϵ, ρ(s))]]:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "We adopt a zeroth order method to adapt the hyperparameter s to keep E B [Var ϵ [g(θ; B, ϵ, ρ(s))]] = τ act Var [g(θ; B)], where τ act ≪ 1 is a small constant. That is, the additional variance raised by approximately computing activation gradient is only τ act times the SG variance itself. Since larger s increases the keep ratio and decreases the variance, we adopt the update:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "s ← s + α sign (E B [Var ϵ [g(θ; B, ϵ, ρ(s))]] -τ act Var [g(θ; B)]) ,", "eq_num": "(5)"}], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "where sign(x) = +1 when x ≥ 0 and sign(x) = -1 when x < 0, and α is a step size. We approximate the expectation and variance with empirical ones with M Monte Carlo repetitions. Therefore, each update requires O(M 2 ) FP+BPs, and we run the update every F SGD iterations, where F ≫ M 2 .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "Controlling E B,ϵ [Var ξ [g(θ; B, ϵ, ξ, ρ, ν]]: As the variance sums up for each parameter θ (l) , we can further decompose the variance as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "E B,ϵ [Var ξ [g(θ; B, ϵ, ξ, ρ, ν]] = L l=1 E B,ϵ Var ξ g (l) (θ; B, ϵ, ξ l , ρ, ν l ,", "eq_num": "(6)"}], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "where g (l) is the gradient of the l-th layer (i.e., ∇ θ (l) ). We control the variance of each layer separately to keep E B,ϵ Var ξ g (l) (θ; B, ϵ, ξ l , ρ, ν l ) = τ w Var g (l) (θ; B) . Again, this is achieved by a zeroth-order algorithm:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "ν l ← ν l β sign(E B,ϵ [Varξ[g (l) (θ;B,ϵ,ξ l ,ρ,ν l )]]-τwVar[g (l) (θ;B)]) ,", "eq_num": "(7)"}], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "where Var ξ g (l) can be computed analytically by Eq. 3, and β is a multiplier. Now we are fully prepared to present the whole picture of VCAS in Alg. 1. Please refer to Appendix. D for more details about the algorithm.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ADAPTING SAMPLE RATIOS", "sec_num": "5"}, {"text": "We assessed VCAS on multiple fine-tuning and pre-training tasks in both vision and natural language domains. We compare our algorithm with the exact training and two previous works in BP sampling: a loss based method SB(selective backprop) in Johnson & Guestrin (2018) and a gradient norm based method UB(upper bound) in Katharopoulos & Fleuret (2018) . We choose these two methods since they are entirely online and need little modification to the original training pipeline like us. The results are shown in Tab. 1. All results are the average of 3 different seeds except for BERT-base pretraining and ViT finetuning on ImageNet-1k which we use 1.", "cite_spans": [{"start": 321, "end": 351, "text": "Kat<PERSON><PERSON><PERSON><PERSON> & Fleuret (2018)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "TRAINING FLOPS REDUCTION", "sec_num": "6.1"}, {"text": "Require: update frequency F , <PERSON><PERSON><PERSON> repetition number M , variance tolerant ratio for activation τact, for weight τw, s step size α, weight ratio multiplier β s ← 1, activation sample ratio schedule {ρ l } L l=1 ← 1, weight sample ratios {ν l } L l=1 ← 1 t ← 0 while not converge do if t mod F = 0 then for i in 1, . . . , M do (Xi, yi) ← batch selected randomly SGD gradient Gs,i ← exact backward using (Xi, yi) for j in 1, . . . , M do activation gradient Gact,i,j ← backward using (Xi, yi) with SampleA only calculate weight variance Vw,i,j analytically with Eq. 3 and Eq. 6 end for end for", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "SGD variance Vs ← 1 M -1 M i=1 Gs,i -1 M M i=1 Gs,i 2 F activation variance Vact ← 1 M M i=1 1 M M j=1 ∥Gact,i,j -Gs,i∥ 2 F weight variance Vw ← 1 M M i=1 1 M M j=1", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "Vw,i,j update s with Vact and Vs according to Eq. 5 update {ρ l } L l=1 with new s according to Eq. 4 update {ν l } L l=1 with Vw and Vs according to Eq. 7 end if backward with SampleA and SampleW t ← t + 1 end while Note that to avoid falling into the pitfall of unfair comparison with baseline which is not tuned under efficient settings as is pointed out by <PERSON><PERSON><PERSON><PERSON> et al. (2021) and <PERSON><PERSON><PERSON> et al. (2023) , for all these experiments we use the same conservative setting of τ act = τ w = 0.025, α = 0.01, β = 0.95, M = 2. We preset all these values heuristically without any tuning or prior knowledge. The only hyperpamater we modified among different tasks is the variance calculation frequency F , which can be defined easily according to the total training steps.", "cite_spans": [{"start": 361, "end": 383, "text": "<PERSON><PERSON><PERSON><PERSON> et al. (2021)", "ref_id": "BIBREF6"}, {"start": 388, "end": 409, "text": "<PERSON><PERSON><PERSON> et al. (2023)", "ref_id": "BIBREF17"}], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "In fact, all the hyperparameters introduced by VCAS have explicit meanings and are insensitive. We show experimentally that though extra tuning may achieve a slightly better result, overall VCAS is robust to these hyperparameters with reasonable values. Please refer to Appendix. A for details about ablation studies on these insensitive hyperparameters.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "For SB and UB, we both adopt a sample ratio of 1/3, since it's the recommended setting in the original papers and it can achieve a FLOPs reduction of 1 -(1 + 2 * 1/3)/3 = 44.44% which is close to the results we get in most tasks. An exception is BERT-base pretraining task where we find the FLOPs reduction achievable is low so we manually set the sample ratio of SB and UB to get the same FLOPs reduction as VCAS, so that they can still give a decent result. Nevertheless we are indeed favoring these methods by helping them to define a reasonable sample ratio, which can not be done themselves.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "From the table we can see that overall VCAS is better than SB and UB with the least impact on final train loss and final evaluation accuracy. With FLOPs reduction of up to 49.58%, VCAS can still achieve nearly the same results with the exact counterpart.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Algorithm 1 Variance controlled adaptive sampling(VCAS) for backpropagation", "sec_num": null}, {"text": "We record the wall-clock time of BERT-large finetuning on MNLI and ViT-large finetuning on ImageNet-1k with NVIDIA 3090Ti, the results are depicted in Tab. 2 and Tab. 3.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "WALL-<PERSON>L<PERSON><PERSON> TIME REDUCTION", "sec_num": "6.2"}, {"text": "From these tables, we can find that VCAS can translate FLOPs reduction into wall-clock time reduction as effectively as simpler online batch sampling methods like UB and SB that drop part of The success of VCAS comes in two ways. One is the fine-grained sampling strategy that samples activation and weight jointly, which enables us to achieve much lower FLOPs given the variance budget. The other is the variance controlled framework combined with the self-adaptation algorithm, with which we are able to learn the proper sample ratios of different training phases. In the following two subsections, we will experimentally show the effectiveness of these two folds.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "WALL-<PERSON>L<PERSON><PERSON> TIME REDUCTION", "sec_num": "6.2"}, {"text": "We compare VCAS that samples activation and weight jointly with strategies that solely sampling activation or weight. Specifically, we keep an equal extra variance for BERT-base finetuning on MNLI. We set τ act = τ w = 0.025 for VCAS, τ act = 0.05 for activation sampling only and τ w = 0.05 for weight sampling only. We find that under the preliminary that τ act , τ w ≪ 1, the results of these sampling strategies show no significant difference due to controlled variance. While as is shown in Fig. 4 , VCAS can achieve a much greater FLOPs reduction with the same total variance introduced. It's reasonable since we can utilize more sparsity in both data and token dimensions with a fine-grained sampling strategy of VCAS.", "cite_spans": [], "ref_spans": [{"start": 501, "end": 502, "text": "4", "ref_id": null}], "eq_spans": [], "section": "EFFECTIVENESS OF FINE-GRAINED SAMPLING", "sec_num": "6.3"}, {"text": "In Fig. 5 we plot the variance of different methods during training process of BERT-base finetuning on MNLI. We can find that VCAS is able to control the extra sampling variance introduced to our preset threshold, while for other variance-unaware algorithms like UB and SB, the extra variance is out of control with a similar FLOPs reduction.", "cite_spans": [], "ref_spans": [{"start": 8, "end": 9, "text": "5", "ref_id": null}], "eq_spans": [], "section": "EFFECTIVENESS OF VARIANCE CONTROL AND SELF-ADAPTATION", "sec_num": "6.4"}, {"text": "With carefully controlled variance, a similar convergence with exact training is guaranteed as we mentioned in the introduction. As is depicted in Fig. 1 and Fig. 6 for BERT-base finetuning on MNLI, VCAS shares nearly the same convergence trajectory with the exact training with reduced FLOPs, while UB converges slightly slower due to uncontrolled variance, and SB converges in an entirely different trajectory with variance introduced far larger than exact. ", "cite_spans": [], "ref_spans": [{"start": 152, "end": 153, "text": "1", "ref_id": null}, {"start": 163, "end": 164, "text": "6", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "EFFECTIVENESS OF VARIANCE CONTROL AND SELF-ADAPTATION", "sec_num": "6.4"}, {"text": "We propose VCAS, a robust sampling method for back propagation with controlled variance and self-adaptive sample ratios. VCAS computes an approximate stochastic gradient by applying finegrained sampling to gradually remove samples and tokens during backpropagation. VCAS enjoys similar variance, convergence trajectory, and final accuracy with exact back propagation, while reduces the training cost by up to 49.58%. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION", "sec_num": "7"}, {"text": "There are a few hyperparameters in our self-adaptation algorithm, but all of them have explicit meaning. In this section we show that though extra tuning of these hyperparameters may achieve a slightly better result, overall VCAS is robust to these hyperparameters with reasonable values. We conduct ablation experiments on two tasks: BERT-base finetuning on SST-2 and MNLI. All the results are averaged over 3 different seeds.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "A.1 ACTIVATION AND WEIGHT VARIANCE THRESHOLDS τ act , τ w", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "The main hyperparameters in VCAS is the variance thresholds of activation τ act and weight τ w . For these two thresholds, how to split total variance among them is a big problem with optimal solution differing across models and tasks. So without prior knowledge introduced, we compromise by keeping τ act = τ w = τ ≪ 1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "We further conduct an ablation on τ from 0.01 to 0.5 as is shown in Tab. 4 for SST-2 and Tab. 5 for MNLI. From the results we can find that a satisfactory outcome is assured regardless of the specific value of τ provided that τ ≪ 1, which proves the robustness of VCAS. with a final accuracy drop of no more than 0.3% for both tasks. Thus, VCAS is robust to different α and β. From all the ablation results above, we can see that VCAS is robust to all these hyperparameters with reasonable values, proving the insensitiveness.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "B INSIGHTS ON UPDATE OF s, {ρ l } AND {ν l }", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "In this section, we will show how the gradient norm preserving ratio s as well as all the sample ratios {ρ l } and {ν l } update across the training.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "We record the update process of BERT-base finetuning on MNLI with different variance tolerance thresholds τ as in Appendix. A.1. All results are averaged on three different seeds.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "Fig. 11a depicts the update of s. For non-decreasing {ρ l }, we plot the update of the first and the last values ρ 1 , ρ L in Fig. 11b , with other values lying between. For {ν l }, we show the update of the first three ones ν 1 , ν 2 , ν 3 in Fig. 11c and observe similar behavior of other weights.", "cite_spans": [], "ref_spans": [{"start": 5, "end": 8, "text": "11a", "ref_id": null}, {"start": 131, "end": 134, "text": "11b", "ref_id": null}, {"start": 249, "end": 252, "text": "11c", "ref_id": null}], "eq_spans": [], "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "It is seen in Fig. 11 that during training of BERT-base on MNLI, the gradient norm preserving ratio s first decreases and then shows a slight downward trend. The activation sample ratios {ρ l } gradually decrease with an abrupt change between epochs due to the rapid decline of train loss caused by the lowered learning rate in the linear learning rate scheduler. The weight sample ratios {ν l } first decrease and then fluctuate to match the change of activation sample ratios.", "cite_spans": [], "ref_spans": [{"start": 19, "end": 21, "text": "11", "ref_id": null}], "eq_spans": [], "section": "A ABLATION ON <PERSON><PERSON><PERSON><PERSON>RAMETERS", "sec_num": null}, {"text": "In Sec. 6, we mainly experiment with Transformer-based models and Adam optimizers. But the variance controlled adaptation depicted in Sec. 5 holds universally for any DNNs with SGD-based optimizers, since it just provides an approximated stochastic gradient with controlled variance to estimate the full gradient. In this section, we employ VCAS on other architectures and other optimizers to prove its versatility.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C PERFORMANCE ON CNN", "sec_num": null}, {"text": "Let's first consider a L-layer MLP. (Note: for simplicity we mildly abuse the term \"layer\" here, representing a single operation like matrix multiplication and ReLU)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "For the last layer L, the output gradient ∇ Z (L) is calculated from the loss directly, the same as the Exact BP. Since activation sampler ∇Z (L) = SampleA ϵ,ρ L (∇ Z (L) ) is unbiased, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "E ∇Z (L) = ∇ Z (L)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "When back propagation proceeds, we may encounter two types of layers: linear and non-linear. For the linear layer, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "∇ Z (L-1) = ∇Z (L) θ (L)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "Thus unbiasedness is preserved with the output gradient of the (L -1)-th layer:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "E [∇ Z (L-1) ] = E ∇Z (L) θ (L) = ∇ Z (L) θ (L) = Exact BP result", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "While for the non-linear layer like ReLU, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "∇ Z (L-1) = ∇Z (L) ⊙ J Z (L)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "where ⊙ is the Had<PERSON>rd product and J Z (L) is the Jacobbi matrix determined by Z (L) which is saved in forward pass and is exact. Thus again we derive the the output gradient of the (L -1)-th layer being unbiased:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "E [∇ Z (L-1) ] = E ∇Z (L) ⊙ J Z (L) = ∇ Z (L) ⊙ J Z (L) = Exact BP result", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "Thus by induction, VCAS assures all activation gradients ∇Z (l) , l = 1 . . . L being unbiased.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "Then for weight gradients, since weight sampler ∇Z (l) = SampleW ξ l ,ν l ∇Z (l) , Z (l-1) is unbiased, we have:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "E ∇Z (l) = E ∇Z (l) = ∇ Z (l)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "Finally, we derive all weight gradients being unbiased:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "E [∇ θ (l) ] = E ∇Z (l) ⊤ Z (l-1) = ∇ ⊤ Z (l) Z (l-1) = Exact BP result", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "For more complicated neural networks like CNN and Transformer, since operations like convolutions and layernorm are all linear transforms, by similar reasoning the unbiasedness still holds.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E PROOF E.1 PROOF TO UNBIASEDNESS OF VCAS", "sec_num": null}, {"text": "F.1 BERT-BASE PRETRAINING For BERT-base pretraining we use a crammed BERT in Geiping & Goldstein (2022) with the recipe same as the original settings of 1 day training on a single NVIDIA 2080Ti. The full results are as follows in Tab. 9", "cite_spans": [{"start": 77, "end": 103, "text": "Geiping & Goldstein (2022)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "F EXPERIMENT DETAILS", "sec_num": null}, {"text": "From the table we can find that although VCAS achieves a relatively high train loss, the downstream task performance is still competent with exact training. While SB and UB both perform worse on CoLA, which is a vulnerable task, reflecting that they have changed the original convergence trajectory of SGD. For BERT finetuning, we use AdamW optimizer with lr = 2e -5 and wd = 0.01. The learning rate scheduler is a linear one with warmup ratio = 0.1. We set epoch numbers N = 3 and a batch size of batch size = 32.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F EXPERIMENT DETAILS", "sec_num": null}, {"text": "For ViT finetuning, we use Adam optimizer with lr = 2e -5 . A linear lr scheduler with no warmup employed. We run N = 5 epochs with batch size batch size = 32", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "F EXPERIMENT DETAILS", "sec_num": null}, {"text": "VCAS is designed for adaptively learning the proper sample ratios of large model training on large datasets. It is not suitable for small models with low gradient variances resulting in increased numerical errors, or small datasets with few training steps that is insufficient for the update process in VCAS.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G LIMITATIONS", "sec_num": null}, {"text": "The weight sampler SampleW in VCAS is specially designed for linear layers and is not usable for other operations like convolution. But the activation sampler SampleA can be applied to all mainstream architectures with deep layers. So for CNN or RNN, we need to employ a degraded version of VCAS with activation sampling only, as shown in Appendix. C.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G LIMITATIONS", "sec_num": null}, {"text": "VCAS focuses on mirroring the exact training with theoretical guarantee and is lack of exploration of other possible convergence trajectories that may bring a better result. Thus it is not recommended when the original training recipe is under-optimized.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "G LIMITATIONS", "sec_num": null}], "back_matter": [{"text": "The authors would like to thank <PERSON><PERSON><PERSON> and <PERSON><PERSON> for their valuable discussions and help on algorithm design and implementation details. This work was supported by the National Key Research and Development Program of China (No. 2021ZD0110502), NSFC Projects (Nos. 62376131, 62061136001, 62106123, 62076147, U19A2081, 61972224), Tsinghua Institute for Guo Qiang, and the High Performance Computing Center, Tsinghua University. J.Z is also supported by the XPlorer Prize.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACKNOWLEDGEMENTS", "sec_num": null}, {"text": "Obviously bigger M will bring more precise empirical variance, yet the cost is prohibitive.We experiment on different M from 2 to 10 and find no significant difference in the empirical variance as is shown in Fig. 7 for SST-2 and Fig. 8 for MNLI. Therefore, we adopted the setting of M = 2, with which we only need to perform 6 extra iterations that is negligible if the variance calculation frequency is large enough like 100 in SST-2 and 500 in MNLI.", "cite_spans": [], "ref_spans": [{"start": 214, "end": 215, "text": "7", "ref_id": null}, {"start": 235, "end": 236, "text": "8", "ref_id": null}], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "Similar to M , the variance calculation frequency F is also a trade-off between better empirical approximation and less overhead introduced. We experimented on F = 50, 100, 200, 500, 1000 in Tab. 6 for SST-2 and Tab. 7 for MNLI. We can see that although as F grows larger the overhead of VCAS is gradually relieved, with a too large F , like F = 1000 in SST-2 that leads to only 6 times of self-adaptation update, the sample ratio schedule is not fully explored and the final FLOPs reduction is even smaller. Therefore, for all these tasks we set F to be at least 1/50 of total training steps and no more than 500 due to slight marginal gains. A simple grid search is conducted for α ∈ {0.005, 0.01, 0.02} and β ∈ {0.95, 0.9, 0.8} in Fig. 9 for SST-2 and Fig. 10 for MNLI. From the figures, we can find that we are able to trade convergence for efficiency with a more aggressive setting of larger α and smaller β, yet all results here are decent For CNN, it is noted that the weight sampler SampleW in Sec. 4 designed for linear layers is not usable for convolution layers. Thus we employ VCAS with a degraded version of activation sampling only.We experiment with WideResNet-18 with widen factor w = 4 pretraining on ImageNet. We use eight NVIDIA 3090Ti to parallel the training with Distributed Data Parallel(DDP). We employ SGDM optimizer with momentum m = 0.9. The results are in Tab. 8. D DETAILS ABOUT ALGORITHM. 1It should be noted that some parts of Alg. 1 are simplified for clarity and we list the implementation details below:In the algorithm table, we put the calculation of empirical variances out of the two Monte-Carlo loops for simplicity. Yet practically we can calculate V act and V w inside the loops and average the variance scalars outside. Therefore, we only need to store three tensors additionally regardless of M : SGD gradient G s,i to calculate V act , and its running mean and running square mean to calculate V s . By sampling only part of parameters to keep gradients, like 1% in our experiments, the memory overhead can be neglected.Besides, since weight sample ratios {ν l } are updated parameter-wise according to Eq. 7, the empirical weight variances and SGD variances are also stored parameter-wise when implemented.Update of activation sample ratios {ρ l } requires finding out gradient sparsity {p l } with the new s according to Eq. 4. In implementation, this is achieved by calculating possible new {ρ l } with both s + α and s -α inside the Monte-Carlo loops and averaging them outside. Then just choose the proper one with new s.", "cite_spans": [], "ref_spans": [{"start": 739, "end": 740, "text": "9", "ref_id": null}, {"start": 760, "end": 762, "text": "10", "ref_id": null}], "eq_spans": [], "section": "A.3 VARIANCE CALCULATION FREQUENCY F", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Variance reduction in sgd by distributed importance sampling", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chinnadhurai", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Courville", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1511.06481"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Variance reduction in sgd by distributed importance sampling. arXiv preprint arXiv:1511.06481, 2015.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Optimization methods for large-scale machine learning", "authors": [{"first": "Léon", "middle": [], "last": "Bo<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Nocedal", "suffix": ""}], "year": 2018, "venue": "SIAM Review", "volume": "60", "issue": "2", "pages": "223--311", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Optimization methods for large-scale machine learning. SIAM Review, 60(2):223-311, 2018.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Active bias: Training more accurate neural networks by emphasizing high variance samples", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Learned-<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "Advances in Neural Information Processing Systems", "volume": "30", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Active bias: Training more accurate neural networks by emphasizing high variance samples. Advances in Neural Information Processing Systems, 30, 2017.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Actnn: Reducing training memory footprint via 2-bit activation compressed training", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zhewei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Stoica", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "1803--1813", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Actnn: Reducing training memory footprint via 2-bit activation compressed training. In International Conference on Machine Learning, pp. 1803-1813. PMLR, 2021.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "A course in probability theory", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2001, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. A course in probability theory. Academic press, 2001.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Selection via proxy: Efficient data selection for deep learning", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Yeh", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>hara<PERSON>", "middle": [], "last": "Mirzasoleiman", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Zaharia", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1906.11829"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Selection via proxy: Efficient data selection for deep learning. arXiv preprint arXiv:1906.11829, 2019.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "The efficiency misnomer", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2110.12894"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. The efficiency mis- nomer. arXiv preprint arXiv:2110.12894, 2021.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "One backward from ten forward, subsampling for large-scale deep learning", "authors": [{"first": "Chaosheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Wei<PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hongyi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2104.13114"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. One backward from ten forward, subsampling for large-scale deep learning. arXiv preprint arXiv:2104.13114, 2021.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Lectures on randomized numerical linear algebra", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Drineas", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "The Mathematics of Data", "volume": "25", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Lectures on randomized numerical linear algebra. The Mathematics of Data, 25(1), 2018.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Learning what data to learn", "authors": [{"first": "<PERSON>", "middle": [], "last": "Fan", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Tian", "suffix": ""}, {"first": "Tao", "middle": [], "last": "Qin", "suffix": ""}, {"first": "Jiang", "middle": [], "last": "Bian", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1702.08635"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Learning what data to learn. arXiv preprint arXiv:1702.08635, 2017.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Bert on a data diet: Finding important examples by gradient-based pruning", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Fayyaz", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>azade<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["<PERSON><PERSON><PERSON><PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.05610"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Bert on a data diet: Finding important examples by gradient-based pruning. arXiv preprint arXiv:2211.05610, 2022.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Cramming: Training a language model on a single gpu in one day", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2212.14034"]}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Cramming: Training a language model on a single gpu in one day. arXiv preprint arXiv:2212.14034, 2022.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Sparsity in deep learning: Pruning and growth for efficient inference and training in neural networks", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>l", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Nik<PERSON>", "middle": [], "last": "Dryden", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Peste", "suffix": ""}], "year": 2021, "venue": "The Journal of Machine Learning Research", "volume": "22", "issue": "1", "pages": "10882--11005", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Sparsity in deep learning: Pruning and growth for efficient inference and training in neural networks. The Journal of Machine Learning Research, 22(1):10882-11005, 2021.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Token dropping for efficient bert pretraining", "authors": [{"first": "Le", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["Yuanzhe"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2203.13240"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Token dropping for efficient bert pretraining. arXiv preprint arXiv:2203.13240, 2022.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Deep networks with stochastic depth", "authors": [{"first": "Gao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": ["Q"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "Computer Vision-ECCV 2016: 14th European Conference", "volume": "14", "issue": "", "pages": "646--661", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Deep networks with stochastic depth. In Computer Vision-ECCV 2016: 14th European Conference, Amsterdam, The Netherlands, October 11-14, 2016, Proceedings, Part IV 14, pp. 646-661. Springer, 2016.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Accelerating deep learning by focusing on the biggest losers", "authors": [{"first": "<PERSON>", "middle": ["H"], "last": "Jiang", "suffix": ""}, {"first": "L-K", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["G"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["R"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>er", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Kaminksy", "suffix": ""}, {"first": "<PERSON>", "middle": ["C"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Lipton", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1910.00762"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Accelerating deep learning by focusing on the biggest losers. arXiv preprint arXiv:1910.00762, 2019.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Training deep models faster with robust, approximate importance sampling", "authors": [{"first": "B", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "Advances in Neural Information Processing Systems", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON>. Training deep models faster with robust, approximate impor- tance sampling. Advances in Neural Information Processing Systems, 31, 2018.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "No train no gain: Revisiting efficient training algorithms for transformer-based language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Key", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2307.06440"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. No train no gain: Revisiting efficient training algorithms for transformer-based language models. arXiv preprint arXiv:2307.06440, 2023.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Not all samples are created equal: Deep learning with importance sampling", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Franc", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "International conference on machine learning", "volume": "", "issue": "", "pages": "2525--2534", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON> <PERSON>. Not all samples are created equal: Deep learning with importance sampling. In International conference on machine learning, pp. 2525-2534. PMLR, 2018.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Deepspeed data efficiency: Improving deep learning model quality and training efficiency via efficient data sampling and routing", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Zhewei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xiao<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "He", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2212.03597"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Deepspeed data efficiency: Improving deep learning model quality and training efficiency via efficient data sampling and routing. arXiv preprint arXiv:2212.03597, 2022.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Gact: Activation compressed training for generic network architectures", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zhiyuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "14139--14152", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. Gact: Activation compressed training for generic network architectures. In International Conference on Machine Learning, pp. 14139-14152. PMLR, 2022.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Online batch selection for faster training of neural networks", "authors": [{"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2015, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1511.06343"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON>. Online batch selection for faster training of neural networks. arXiv preprint arXiv:1511.06343, 2015.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Prioritized training on points that are learnable, worth learning, and not yet learnt", "authors": [{"first": "Jan", "middle": ["M"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "Höltgen", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "International Conference on Machine Learning", "volume": "", "issue": "", "pages": "15630--15649", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, et al. Prioritized training on points that are learnable, worth learning, and not yet learnt. In International Confer- ence on Machine Learning, pp. 15630-15649. PMLR, 2022.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Stochastic gradient descent, weighted sampling, and the randomized kacz<PERSON><PERSON> algorithm", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ward", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Srebro", "suffix": ""}], "year": 2014, "venue": "Advances in neural information processing systems", "volume": "27", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Stochastic gradient descent, weighted sampling, and the randomized kaczmarz algorithm. Advances in neural information processing systems, 27, 2014.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Sparseprop: Efficient sparse backpropagation for faster training of neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Eldar", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.04852"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Sparse- prop: Efficient sparse backpropagation for faster training of neural networks. arXiv preprint arXiv:2302.04852, 2023.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Accelerating inference with sparsity using the nvidia ampere architecture and nvidia tensorrt", "authors": [{"first": "", "middle": [], "last": "Nvidia", "suffix": ""}], "year": null, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "NVIDIA. Accelerating inference with sparsity using the nvidia ampere archi- tecture and nvidia tensorrt. https://developer.nvidia.com/blog/ accelerating-inference-with-sparsity-using-ampere-and-tensorrt/,", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Efficient model finetuning for text classification via data filtering", "authors": [{"first": "<PERSON>", "middle": [], "last": "O<PERSON><PERSON>", "suffix": ""}, {"first": "Shah<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2207.14386"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Efficient model finetuning for text classification via data filtering. arXiv preprint arXiv:2207.14386, 2022.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Deep learning on a data diet: Finding important examples early in training", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Surya", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Gintare", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Dziugaite", "middle": [], "last": "", "suffix": ""}], "year": 2021, "venue": "Advances in Neural Information Processing Systems", "volume": "34", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Deep learning on a data diet: Find- ing important examples early in training. Advances in Neural Information Processing Systems, 34:20596-20607, 2021.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Sparse weight activation training", "authors": [{"first": "Md", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}, {"first": "Tor", "middle": [], "last": "Aamodt", "suffix": ""}], "year": 2020, "venue": "Advances in Neural Information Processing Systems", "volume": "33", "issue": "", "pages": "15625--15638", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> <PERSON><PERSON><PERSON> and <PERSON>. Sparse weight activation training. Advances in Neural Infor- mation Processing Systems, 33:15625-15638, 2020.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Learning representations by backpropagating errors", "authors": [{"first": "<PERSON>", "middle": ["E"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1986, "venue": "nature", "volume": "323", "issue": "6088", "pages": "533--536", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Learning representations by back- propagating errors. nature, 323(6088):533-536, 1986.", "links": null}}, "ref_entries": {"FIGREF0": {"num": null, "text": "Figure 3: Gradient distribution over different layer and iterations of BERT-base finetuning on SST2 (6315 iterations in total). The normalized gradient norm of each datum is shown in the heatmaps. Black solid lines are the 95% percentile. Data above the lines are likely to be dicarded by VCAS.", "type_str": "figure", "uris": null, "fig_num": "3"}, "FIGREF1": {"num": null, "text": "Figure 4: FLOPs reduction ratio of VCAS vs. sampling activation or weight solely with equal variance.", "type_str": "figure", "uris": null, "fig_num": "45"}, "FIGREF2": {"num": null, "text": "Figure 6: Convergence comparison of different sampling methods. FLOPs is normalized by exact training.", "type_str": "figure", "uris": null, "fig_num": "6"}, "FIGREF3": {"num": null, "text": "Figure 9: Grid search of s update step α and weight ratio multiplier β of BERT-base finetuning on SST-2. The darker color the better.", "type_str": "figure", "uris": null, "fig_num": "910"}, "TABREF1": {"num": null, "content": "<table><tr><td/><td/><td>Layer 𝑙</td><td/></tr><tr><td/><td>𝑁</td><td>∇</td><td>∇</td><td>∇</td></tr><tr><td/><td/><td>𝑆𝑎𝑚𝑝𝑙𝑒𝐴 ,</td><td>ℎ</td></tr><tr><td>Layer 𝑙 1</td><td>𝑇</td><td/><td/><td>Layer 𝑙 1</td></tr><tr><td/><td>𝐾</td><td/><td/></tr><tr><td/><td/><td/><td>∇</td></tr><tr><td/><td/><td/><td>∇</td></tr><tr><td/><td/><td>𝑆𝑎𝑚𝑝𝑙𝑒𝑊 ,</td><td>𝑔</td></tr><tr><td/><td>𝑁𝑇</td><td/><td/></tr><tr><td colspan=\"5\">Figure 2: this importance score in parallel. Johnson &amp; Guestrin (2018) uses a second-order approximation of</td></tr><tr><td colspan=\"5\">gradient norm with history maintained. Closely related to our work, Katharopoulos &amp; Fleuret (2018)</td></tr><tr><td colspan=\"5\">develops a pure online algorithm by constructing an upper bound of gradient norm to sample with</td></tr><tr><td colspan=\"5\">much cheaper computation. These methods are usually more expensive but have relatively strong</td></tr><tr><td colspan=\"5\">theoretical guarantees. So we follow this way in our activation sampling.</td></tr></table>", "type_str": "table", "text": "The computing diagram of backpropagation with VCAS in every layer. We use light blue squares to represent small gradient entries and orange for large ones. White squares are discarded by sampling. The upper line calculates activation gradient and the lower for weight gradient. Please refer to Sec. 4 for notations.", "html": null}, "TABREF2": {"num": null, "content": "<table><tr><td>Task</td><td>Dataset</td><td>exact</td><td>SB</td><td>UB</td><td>VCAS</td></tr><tr><td>BERT-base</td><td>C4</td><td>2.099 / 78.37</td><td>2.133 / 77.53</td><td>2.106 / 77.96</td><td>2.134 / 78.36 / 21.58</td></tr><tr><td>pretraining</td><td/><td/><td/><td/><td/></tr><tr><td/><td>MNLI-m</td><td colspan=\"4\">0.2372 / 84.33 0.3833 / 83.71 0.2957 / 83.82 0.2428 / 84.23 / 41.56</td></tr><tr><td>BERT-base</td><td>QQP</td><td colspan=\"4\">0.1143 / 91.00 0.1441 / 90.76 0.1964 / 89.53 0.1189 / 90.92 / 47.10</td></tr><tr><td>finetuning</td><td>QNLI</td><td colspan=\"4\">0.1014 / 91.67 0.2017 / 90.58 0.1441 / 91.23 0.1056 / 91.29 / 44.45</td></tr><tr><td/><td>SST-2</td><td colspan=\"4\">0.0559 / 92.59 0.0727 / 92.63 0.0743 / 92.82 0.0600 / 93.04 / 48.28</td></tr><tr><td/><td>MNLI-m</td><td colspan=\"4\">0.1439 / 86.58 0.2492 / 85.18 0.2266 / 86.09 0.1619 / 86.63 / 44.17</td></tr><tr><td>BERT-large</td><td>QQP</td><td colspan=\"4\">0.0885 / 91.64 0.1308 / 91.20 0.1751 / 90.51 0.0962 / 91.57 / 49.50</td></tr><tr><td>finetuning</td><td>QNLI</td><td colspan=\"4\">0.0877 / 92.02 0.1436 / 91.50 0.1325 / 91.98 0.0640 / 92.15 / 46.19</td></tr><tr><td/><td>SST-2</td><td colspan=\"4\">0.0537 / 93.60 0.1136 / 91.81 0.0838 / 93.40 0.0593 / 93.67 / 49.24</td></tr><tr><td>ViT-base finetuning</td><td colspan=\"5\">CIFAR10 CIFAR100 ImageNet-1k 0.6032 / 82.27 0.6533 / 82.09 0.6109 / 82.28 0.6089 / 82.27 / 45.29 0.1868 / 98.92 0.2367 / 98.82 0.1923 / 98.94 0.1873 / 98.90 / 45.90 0.8760 / 91.19 2.248 / 89.60 1.175 / 89.68 0.8811 / 91.08 / 29.32</td></tr><tr><td>ViT-large finetuning</td><td colspan=\"5\">CIFAR10 CIFAR100 ImageNet-1k 0.4135 / 82.04 0.4637 / 82.21 0.4242 / 82.21 0.4228 / 82.27 / 49.58 0.1359 / 99.24 0.1439 / 99.21 0.1378 / 99.17 0.1393 / 99.28 / 48.37 0.4590 / 93.56 0.5983 / 93.07 0.5170 / 93.36 0.4649 / 93.64 / 38.67</td></tr></table>", "type_str": "table", "text": "Comparison of VCAS with other methods. Data format is Final Train Loss / Final Eval Acc.(%) for exact, SB and UB, and Final Train Loss / Final Eval Acc.(%) / FLOPs reduction ratio(%) for VCAS. The FLOPs reduction of SB and UB is 21.58% for BERT pretraining and 44.44% for other tasks. VCAS's FLOPs take account of the adaptation overhead. For BERT pretraining, accuracy=average performance on GLUE. Bold indicates the best result of each metric except for exact. Underline means Eval Acc less than 0.1% off the exact training.", "html": null}, "TABREF3": {"num": null, "content": "<table><tr><td colspan=\"6\">Method Train Loss Eval Acc.(%) Wall-clock Time(h) FLOPs↓(%) Time↓(%)</td></tr><tr><td>exact</td><td>0.1439</td><td>86.58</td><td>5.478</td><td>-</td><td>-</td></tr><tr><td>SB</td><td>0.2492</td><td>85.18</td><td>4.320</td><td>44.44</td><td>21.14</td></tr><tr><td>UB</td><td>0.2266</td><td>86.09</td><td>4.266</td><td>44.44</td><td>22.12</td></tr><tr><td>VCAS</td><td>0.1619</td><td>86.63</td><td>4.437</td><td>44.17</td><td>19.00</td></tr></table>", "type_str": "table", "text": "Wall-clock time of BERT-large finetuning on MNLI.", "html": null}, "TABREF4": {"num": null, "content": "<table><tr><td colspan=\"6\">Method Train Loss Eval Acc.(%) Wall-clock Time(h) FLOPs↓(%) Time↓(%)</td></tr><tr><td>exact</td><td>0.4135</td><td>82.04</td><td>52.29</td><td>-</td><td>-</td></tr><tr><td>SB</td><td>0.4637</td><td>82.21</td><td>42.56</td><td>44.44</td><td>18.61</td></tr><tr><td>UB</td><td>0.4242</td><td>82.21</td><td>41.92</td><td>44.44</td><td>19.83</td></tr><tr><td>VCAS</td><td>0.4228</td><td>82.27</td><td>41.28</td><td>49.58</td><td>21.06</td></tr><tr><td colspan=\"6\">data one-time in a whole, while enjoying mirrored performance with the exact training under theo-</td></tr><tr><td>retical guarantee.</td><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "text": "Wall-clock time of ViT-large finetuning on ImageNet-1k.", "html": null}, "TABREF5": {"num": null, "content": "<table><tr><td><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON></td></tr><tr><td><PERSON><PERSON>, <PERSON>, <PERSON>, et al. Adaselection: Accelerating deep learning training through</td></tr><tr><td>data subsampling. arXiv preprint arXiv:2306.10728, 2023.</td></tr><tr><td><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Accelerating training of transformer-based language models with</td></tr><tr><td>progressive layer dropping. Advances in Neural Information Processing Systems, 33:14011-</td></tr><tr><td>14023, 2020.</td></tr><tr><td><PERSON><PERSON><PERSON> and <PERSON>. Stochastic optimization with importance sampling for regularized loss</td></tr><tr><td>minimization. In international conference on machine learning, pp. 1-9. PMLR, 2015.</td></tr></table>", "type_str": "table", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Choosing the sample with lowest loss makes sgd robust. In International Conference on Artificial Intelligence and Statistics, pp. 2120-2130. PMLR, 2020. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Random-ltd: Random and layerwise token dropping brings efficient training for large-scale transformers. arXiv preprint arXiv:2211.11586, 2022.", "html": null}, "TABREF6": {"num": null, "content": "<table><tr><td>τ</td><td>0(exact)</td><td>0.01</td><td>0.025</td><td>0.05</td><td>0.1</td><td>0.25</td><td>0.5</td></tr><tr><td>Final Train Loss</td><td colspan=\"7\">0.0559 0.0586 0.0600 0.0625 0.0642 0.0705 0.0761</td></tr><tr><td>Final Eval Acc(%)</td><td>92.59</td><td>93.07</td><td>93.04</td><td>93.25</td><td>92.81</td><td>92.79</td><td>92.18</td></tr><tr><td>FLOPs reduction(%)</td><td>-</td><td>45.92</td><td>48.28</td><td>49.82</td><td>50.05</td><td>51.57</td><td>52.71</td></tr><tr><td colspan=\"8\">Table 5: Ablation on different variance thresholds τ of BERT-base finetuning on MNLI</td></tr><tr><td>τ</td><td>0(exact)</td><td>0.01</td><td>0.025</td><td>0.05</td><td>0.1</td><td>0.25</td><td>0.5</td></tr><tr><td>Final Train Loss</td><td colspan=\"7\">0.2372 0.2388 0.2428 0.2459 0.2552 0.2684 0.2805</td></tr><tr><td>Final Eval Acc(%)</td><td>84.33</td><td>84.31</td><td>84.23</td><td>84.33</td><td>84.07</td><td>84.13</td><td>84.08</td></tr><tr><td>FLOPs reduction(%)</td><td>-</td><td>38.59</td><td>41.56</td><td>43.49</td><td>45.37</td><td>47.53</td><td>48.92</td></tr><tr><td colspan=\"3\">A.2 MONTE-CARLO REPETITIONS M</td><td/><td/><td/><td/><td/></tr><tr><td colspan=\"8\">To calculate variances, VCAS introduces an overhead of extra iterations quadratic with Monte-Carlo</td></tr><tr><td>repetitions M .</td><td/><td/><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "text": "Ablation on different variance thresholds τ of BERT-base finetuning on SST-2", "html": null}, "TABREF7": {"num": null, "content": "<table><tr><td>exact</td><td>2.099</td><td>82.28</td><td>82.68</td><td>87.08 88.85 91.28 48.07 83.26</td><td>86.98 54.87 78.37</td></tr><tr><td>SB</td><td>2.133</td><td>82.34</td><td>82.86</td><td>87.27 88.63 91.28 41.82 82.86</td><td>85.53 55.23 77.53</td></tr><tr><td>UB</td><td>2.106</td><td>82.95</td><td>83.46</td><td>87.27 88.66 91.05 42.80 83.68</td><td>85.90 55.95 77.96</td></tr><tr><td>VCAS</td><td>2.134</td><td>82.03</td><td>82.82</td><td>86.92 89.23 91.62 48.36 83.02</td><td>86.03 55.23 78.36</td></tr><tr><td colspan=\"4\">F.2 RECIPE OF OTHER TASKS</td><td/><td/></tr></table>", "type_str": "table", "text": "Full results on BERT-base pretraining Methods Loss MNLI-m MNLI-mm QQP QNLI SST2 CoLA STSB MRPC RTE Avg.", "html": null}}}}