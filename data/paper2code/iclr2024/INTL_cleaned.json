{"paper_id": "INTL", "title": "MODULATE YOUR SPECTRUM IN SELF-SUPERVISED LEARNING", "abstract": "Whitening loss offers a theoretical guarantee against feature collapse in selfsupervised learning (SSL) with joint embedding architectures. Typically, it involves a hard whitening approach, transforming the embedding and applying loss to the whitened output. In this work, we introduce Spectral Transformation (ST), a framework to modulate the spectrum of embedding and to seek for functions beyond whitening that can avoid dimensional collapse. We show that whitening is a special instance of ST by definition, and our empirical investigations unveil other ST instances capable of preventing collapse. Additionally, we propose a novel ST instance named IterNorm with trace loss (INTL). Theoretical analysis confirms INTL's efficacy in preventing collapse and modulating the spectrum of embedding toward equal-eigenvalues during optimization. Our experiments on ImageNet classification and COCO object detection demonstrate INTL's potential in learning superior representations. The code is available at https://github.com/winci-ai/INTL.", "pdf_parse": {"paper_id": "INTL", "abstract": [{"text": "Whitening loss offers a theoretical guarantee against feature collapse in selfsupervised learning (SSL) with joint embedding architectures. Typically, it involves a hard whitening approach, transforming the embedding and applying loss to the whitened output. In this work, we introduce Spectral Transformation (ST), a framework to modulate the spectrum of embedding and to seek for functions beyond whitening that can avoid dimensional collapse. We show that whitening is a special instance of ST by definition, and our empirical investigations unveil other ST instances capable of preventing collapse. Additionally, we propose a novel ST instance named IterNorm with trace loss (INTL). Theoretical analysis confirms INTL's efficacy in preventing collapse and modulating the spectrum of embedding toward equal-eigenvalues during optimization. Our experiments on ImageNet classification and COCO object detection demonstrate INTL's potential in learning superior representations. The code is available at https://github.com/winci-ai/INTL.", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Self-supervised learning (SSL) via joint embedding architectures to learn visual representations has made significant progress over the last several years (<PERSON><PERSON> et al., 2019; <PERSON> et al., 2020; <PERSON> et al., 2020a; <PERSON> & <PERSON>, 2021; <PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2023) , almost outperforming their supervised counterpart on many downstream tasks (<PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2022) . This paradigm addresses to train a dual pair of networks to produce similar embeddings for different views of the same image (<PERSON> & <PERSON>, 2021) . One main challenge with the joint embedding architectures is how to prevent a collapse of the representation, in which the two branches ignore the inputs and produce identical and constant outputs (<PERSON> & <PERSON>, 2021) . A variety of methods have been proposed to successfully avoid collapse, including contrastive learning methods (<PERSON> et al., 2018; <PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2019) that attract different views from the same image (positive pairs) while pull apart different images (negative pairs), and non-contrastive methods (<PERSON><PERSON> et al., 2020; <PERSON> & <PERSON>, 2021) that directly match the positive targets without introducing negative pairs. The collapse problem is further generalized into dimensional collapse (<PERSON><PERSON> et al., 2021; <PERSON> et al., 2022) (or informational collapse (<PERSON><PERSON> et al., 2022) ), where the embedding vectors only span a lower-dimensional subspace and would be highly correlated. In this case, the covariance matrix of embedding has certain zero eigenvalues, which degenerates the representation in SSL. To prevent dimensional collapse, a theoretically motivated paradigm, called whitening loss, is proposed by minimizing the distance between embeddings of positive pairs under the condition that embeddings from different views are whitened (Ermolov et al., 2021; Hua et al., 2021) . One typical implementation of whitening loss is hard whitening (Ermolov et al., 2021; Weng et al., 2022) that designs whitening transformation over mini-batch data and imposes the loss on the whitened output (Ermolov et al., 2021; Hua et al., 2021; Weng et al., 2022) . We note that the whitening transformation is a function over embedding during forward pass, and modulates the spectrum of embedding implicitly during backward pass when minimizing the objective. This raises questions whether there exist other functions over embedding can avoid collapse? If yes, how the function affects the spectrum of embedding?", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "This paper proposes spectral transformation (ST), a framework to modulate the spectrum of embedding in joint embedding architecture. ST maps the spectrum of embedding to a desired distribution during forward pass, and modulates the spectrum of embedding by implicit gradient update during backward pass (Figure 1 ). This framework provides a way to seek for functions beyond whitening transformation that can avoid dimensional collapse. We show that whitening transformation is a special instance of ST using a power function by definition, and there exist other power functions that can avoid dimensional collapse by our empirical investigation (see Section 3.2 for details). We demonstrate that IterNorm (<PERSON> et al., 2019) , an approximating whitening method by using <PERSON>'s iterations (<PERSON><PERSON> et al., 2005; <PERSON> et al., 2020) , is also an instance of ST, and show that IterNorm with different iteration number corresponds to different ST (see Section 3.2.2 for details). We further theoretically characterize how the spectrum evolves as the increasing of iteration number of IterNorm.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We empirically observe that IterNorm suffers from severe dimensional collapse and mostly fails to train the model in SSL unexpectedly, unlike its benefits in approximating whitening for supervised learning (<PERSON> et al., 2019) . We thus propose IterNorm with trace loss (INTL), a simple solution to address the failure of IterNorm, by adding an extra penalty on the transformed output. Moreover, we theoretically demonstrate that INTL can avoid dimensional collapse, and reveal its mechanism in modulating the spectrum of embedding to be equal-eigenvalues. We conduct comprehensive experiments and show that INTL is a promising SSL method in practice. Our main contributions are summarized as follows:", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• We propose spectral transformation, a framework to modulate the spectrum of embedding and to seek for functions beyond whitening that can avoid dimensional collapse. We show there exist other functions that can avoid dimensional collapse by empirical observation and intuitive explanation.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• We propose a new instance of ST, called IterNorm with trace loss (INTL) . We theoretically prove that INTL can avoid collapse and modulate the spectrum of embedding towards an equal-eigenvalue distribution during the course of optimization.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• INTL's experimental performance on standard benchmarks showcases its high promise as a practical SSL method, consistently achieving or surpassing state-of-the-art methods, even when utilizing a relatively small batch size.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Our work is related to the SSL methods that address the feature collapse problem when using joint embedding architectures.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Contrastive learning prevents collapse by attracting positive samples closer, and spreading negative samples apart (<PERSON> et al., 2018; <PERSON> et al., 2019) . In these methods, negative samples play an important role and need to be well designed (<PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON>, 2020) . Mo<PERSON>os (<PERSON> et al., 2020; <PERSON> et al., 2020b ) build a memory bank with a momentum encoder to provide consistent negative samples, while SimCLR (<PERSON> et al., 2020a) addresses that more negative samples in a batch with strong data augmentations perform better. Our proposed INTL can avoid collapse and work well without negative samples. Additionally, recent work (<PERSON> et al., 2023) explores the use of data augmentation for contrastive learning through spectrum analysis to enhance performance, while our paper focuses on developing a novel non-contrastive method to prevent collapse under standard augmentation.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Non-contrastive learning can be categorized into two groups: asymmetric methods and whitening loss. Asymmetric methods employ asymmetric network architectures to prevent feature collapse without the need for explicit negative pairs (<PERSON><PERSON> et al., 2018; 2020; <PERSON> et al., 2021; <PERSON><PERSON> et al., 2020; <PERSON> & <PERSON>, 2021) . For instance, BYOL (<PERSON><PERSON> et al., 2020) enhances network stability by appending a predictor after the online network and introducing momentum into the target network. <PERSON><PERSON><PERSON><PERSON> (Chen & He, 2021) extends BYOL and emphasizes the importance of stop-gradient to prevent trivial solutions. Other advancements in this realm include cluster assignment prediction using the Sin<PERSON>orn-<PERSON>nopp algorithm (<PERSON><PERSON> et al., 2020) and the development of asymmetric pipelines with self-distillation losses for Vision Transformers (<PERSON><PERSON> et al., 2021) . However, it remains unclear how these asymmetric networks effectively prevent collapse without the inclusion of negative pairs. This has sparked debates surrounding topics such as batch normalization (BN) (Fetter<PERSON> & Albrecht, 2020; <PERSON><PERSON> et al., 2020b; <PERSON><PERSON><PERSON> et al., 2020) and stop-gradient (<PERSON> & He, 2021; <PERSON> et al., 2022a) . Despite preliminary efforts to analyze training dynamics (<PERSON><PERSON> et al., 2021) and establish connections between non-contrastive and contrastive methods (<PERSON> et al., 2022; <PERSON><PERSON><PERSON><PERSON> et al., 2023) , the exact mechanisms behind these methods remain an ongoing area of research. In our work, we address the more intricate challenge of dimensional collapse and theoretically demonstrate that our INTL method effectively prevents this issue, offering valuable insights into mitigating feature collapse in various scenarios.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Whitening loss is a theoretically motivated paradigm to prevent dimensional collapse (<PERSON><PERSON><PERSON><PERSON> et al., 2021) . One typical implementation of whitening loss is hard whitening that designs whitening transformation over mini-batch data and imposes the loss on the whitened output. The designed whitening transformation includes batch whitening in W-MSE (<PERSON>rmolo<PERSON> et al., 2021) and Shuffled-DBN (<PERSON><PERSON> et al., 2021) , channel whitening in CW-RGP (<PERSON><PERSON> et al., 2022) , and the combination of both in Zero-CL (<PERSON> et al., 2022b) . Our proposed ST generalizes whitening transformation and provides a frame to modulate the spectrum of embedding. Our INTL can improve these work in training stability and performance, by replacing whitening transformation with IterNorm (<PERSON> et al., 2019) and imposing an additional trace loss on the transformed output. Furthermore, we theoretically show that our proposed INTL modulates the spectrum of embedding to be equal-eigenvalues.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Another way to implement whitening loss is soft whitening that imposes a whitening penalty as regularization on the embedding, including <PERSON> Twins (<PERSON><PERSON><PERSON> et al., 2021) , VICReg (<PERSON><PERSON> et al., 2022) and CCA-SSG (<PERSON> et al., 2021) . Different from these works, our proposed INTL imposes the trace loss on the approximated whitened output, providing equal-eigenvalues modulation on the embedding.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "There are also theoretical works analyzing how dimensional collapse occurs (<PERSON><PERSON> et al., 2021; <PERSON> et al., 2022) and how it can be avoided by using whitening loss (<PERSON><PERSON> et al., 2021; <PERSON><PERSON> et al., 2022) .", "section": "RELATED WORK", "sec_num": "2"}, {"text": "The recent works (<PERSON> <PERSON>, 2022; <PERSON><PERSON><PERSON> et al., 2022) further discuss how to characterize the magnitude of dimensional collapse, and connect the spectrum of a representation to a power law. They show the coefficient of the power law is a strong indicator for the effects of the representation. Different from these works, our theoretical analysis presents a new thought in demonstrating how to avoid dimensional collapse, which provides theoretical basis for our proposed INTL.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Feature collapse. While minimizing Eqn. 1, a trivial solution known as complete collapse could occur such that F θ (x) ≡ c, ∀x ∈ D. Moreover, a weaker collapse condition called dimensional collapse can be easily arrived, for which the projected features collapse into a low-dimensional manifold. To express dimensional collapse more mathematically, we refer to dimensional collapse as the phenomenon that one or certain eigenvalues of the covariance matrix of feature vectors degenerate to 0. Therefore, we can determine the occurrence of dimensional collapse by observing the spectrum of the covariance matrix.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Whitening loss. To address the collapse problem, whitening loss (<PERSON><PERSON><PERSON><PERSON> et al., 2021) is proposed to minimize Eqn. 1, under the condition that embeddings from different views are whitened.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "Whitening loss provides theoretical guarantee in avoiding (dimensional) collapse, since the embedding is whitened with all axes decorrelated (<PERSON><PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON> et al., 2021) . <PERSON><PERSON><PERSON><PERSON> et al. (<PERSON><PERSON><PERSON><PERSON> et al., 2021) propose to whiten the mini-batch embedding Z ∈ R d×m using batch whitening (BW) (<PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2019) and impose the loss on the whitened output Z ∈ R d×m , given the mini-batch inputs X with size of m, as follows:", "section": "RELATED WORK", "sec_num": "2"}, {"text": "EQUATION", "section": "RELATED WORK", "sec_num": "2"}, {"text": "where Σ =1 m ZZ T is the covariance matrix of embedding 1 . Σ -1 2 is called the whitening matrix, and is calculated either by <PERSON><PERSON><PERSON> decomposition in (<PERSON><PERSON><PERSON><PERSON> et al., 2021) or by eigen-decomposition in (<PERSON><PERSON> et al., 2021) . E.g., zero-phase component analysis (ZCA) whitening (<PERSON> et al., 2018) calculates Σ -1 2 = UΛ -1 2 U T , where Λ = diag(λ 1 , . . . , λ d ) and U = [u 1 , ..., u d ] are the eigenvalues and associated eigenvectors of Σ, i.e., UΛU T = Σ. One intriguing result shown in (<PERSON><PERSON> et al., 2022) is that hard whitening can avoid collapse by only constraining the embedding Z to be full-rank, but not whitened.", "section": "RELATED WORK", "sec_num": "2"}, {"text": "We note that the whitening transformation is a function over embedding Z during forward pass, and modulates the spectrum of embedding Z implicitly during backward pass when minimizing MSE loss imposed on the whitened output. This raises a question of whether there are other functions over embedding Z that can avoid collapse? If yes, how the function affects the spectrum of embedding Z?", "section": "RELATED WORK", "sec_num": "2"}, {"text": "In this section, we extend the whitening transformation to spectral transformation (ST), a more general view to characterize the modulation on the spectrum of embedding, and empirically investigate the interaction between the spectrum of the covariance matrix of Z and collapse of the SSL model. Definition 1. (Spectral Transformation) Given any unary function g(•) in the definition domain λ(Z) = {λ 1 , λ 2 , . . . , λ d }. Drawing an analogy with whitening, g(•) on the covariance matrix Σ of embedding Z is defined as g(Σ) = Ug(Λ)U T , where g(Λ) = diag(g(λ(Z))). We denote the transformation matrix of ST as Φ ST = g(Σ), so that the output of ST is calculated by", "section": "SPECTRAL TRANSFORMATION", "sec_num": "3.2"}, {"text": "Z = Φ ST Z = Ug(Λ)U T Z and the covariance matrix of Z is Σ Z = 1 m Z Z T = UΛg 2 (Λ)U T .", "section": "SPECTRAL TRANSFORMATION", "sec_num": "3.2"}, {"text": "Based on Definition 1, ST is an abstract framework until g(•) is determined, and its essence is mapping the spectrum λ(Z) to λ( Z) = λ 1 g 2 (λ 1 ), λ 2 g 2 (λ 2 ), . . . , λ d g 2 (λ d ) . When applied in the context of self-supervised learning, the loss function for ST remains the same as Eqn 2, with the only difference being that Z is determined by g(•). Meanwhile, the optimization direction for the embedding spectrum can also be determined when employing gradient-based methods. That is, what spectrum of embedding will be modulated to be during the course of training.", "section": "SPECTRAL TRANSFORMATION", "sec_num": "3.2"}, {"text": "Can we unveil the potential of ST? Our ST framework exhibits uncertainty and diversity, allowing g(•) to adopt the guise of any single-variable function within the defined domain, including power functions, exponential functions, iterative functions, and more. Whitening, on the other hand, is a special and successful instance within ST, where g(•) takes the form of a power function g(λ) = λ -1 2 . This naturally prompts two questions: 1. Could there be other functions, akin to whitening, capable of preventing collapse within the ST framework? 2. If yes, how the function works and affects the spectrum of embedding Z?", "section": "SPECTRAL TRANSFORMATION", "sec_num": "3.2"}, {"text": "With these questions in mind, we embark on a deeper exploration of the mechanics extending beyond whitening, considering a more comprehensive transformation g(λ) = λ -p , p ∈ (-∞, +∞) for ST. Based on Definition. 1, this comprehensive power transformation is mapping the spectrum λ(Z) to", "section": "SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": "3.2.1"}, {"text": "λ( Z) = λ 1 1-2p , λ 2 1-2p , . . . , λ d 1-2p .", "section": "SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": "3.2.1"}, {"text": "Empirical observation. Initially, we conduct experiments on a 2D dataset, varying the parameter p, and visualize the outputs of the toy models as depicted in Figure 2 (a). Our observations indicate that the toy model tends to perform well in avoiding collapse when p falls within the neighborhood of 0.5, specifically in the range of 0.45 to 0.55. However, as p gradually deviates from 0.5, collapse becomes more pronounced. Subsequently, we extend our experiments to real-world datasets to validate these findings. The results presented in Figure 2 (b) align with the previously observed phenomena. When p is set to either 0.45 or 0.55, the model maintains high evaluation performance, similar to that of whitening (p = 0.5). This discovery suggests that within the ST framework, there exist other functions capable of successfully preventing collapse, which answers the first question.", "section": "SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": "3.2.1"}, {"text": "For the second question, as illustrated in Figure 2 (c), it becomes evident that when p lies in the vicinity of 0.5, the embedding showcases a more well-conditioned spectrum, characterized by a smaller condition number (larger IoC). However, when p significantly deviates from 0.5, the spectrum of embedding loses its well-conditioned attributes, closely aligning with the occurrence of embedding collapse. This statement asserts that if g(•) is effective in preventing collapse within the ST framework, it will result in the modulation of the embedding spectrum towards a well-conditioned state.", "section": "SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": "3.2.1"}, {"text": "Intuitive explanation. We note that (<PERSON><PERSON> et al., 2022) implied that whitening loss in Eqn 2 can be decomposed into two asymmetric losses", "section": "SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": "3.2.1"}, {"text": "L = 1 m ∥ϕ(Z (1) )Z (1) -( Z (2) ) st ∥ 2 F + 1 m ∥ϕ(Z (2) )Z (2) - ( Z (1) ) st ∥ 2", "section": "SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": "3.2.1"}, {"text": "F , where ϕ(Z) refers to the whitening matrix of Z, st represents the stop gradient operation, and Z denotes the whitening output. Each asymmetric loss can be viewed as an online network to match a whitened target Z. As a more generalized form of whitening, our ST can also extend this decomposition to the loss function. As depicted in Figure 2 (c), when p falls within the range of 0.45 to 0.55, Z exhibits a well-conditioned spectrum, with each eigenvalue approaching 1. In such instances, Z serves as an ideal target for ϕ(Z)Z to match, enabling the embedding Z to learn a favorable spectrum to prevent collapse. Conversely, when p deviates significantly from 0.5, the spectrum of the transformed output loses its well-conditioned characteristics, with Z becoming a detrimental target, ultimately leading to the collapse of the embedding.", "section": "SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": "3.2.1"}, {"text": "However, utilizing the power function g(λ) = λ -p (where p is approximately 0.5) within our ST framework is not without its drawbacks. One issue is the potential for numerical instability when computing eigenvalues λ and eigenvectors U via eigen-decomposition, particularly when the covariance matrix is ill-conditioned (<PERSON><PERSON><PERSON> et al., 2019) . We provide comprehensive experiments and analysis in Appendix D.3 to validate the presence of this problem in SSL.", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "Naturally, if we could implement a spectral transformation that can modulate the spectrum without the need for explicit calculation of λ or U, this issue could be mitigated. In fact, we take note of an approximate whitening method called iterative normalization (IterNorm) (<PERSON> et al., 2019) , which uses <PERSON>'s iteration to address the numerical challenges associated with batch whitening in supervised learning. Specifically, given the centered embedding Z, the iteration count T , and the trace-normalized covariance matrix Σ N = Σ/tr(Σ), IterNorm performs <PERSON>'s iteration as follows.", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "EQUATION", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "The whitening matrix Σ -1 2 is approximated by Φ T = P T / tr(Σ) and we have the whitened output", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "Z = Φ T Z. When T → +∞, Φ T → Σ -1", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "2 and the covariance matrix of Z will be an identity matrix. Here, we theoretically show that IterNorm is also an instance of spectral transformation as follows.", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "Theorem 1. Define one-variable iterative function f T (x), satisfying", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "f k+1 (x) = 3 2 f k (x) -1 2 xf k 3 (x), k ≥ 0; f 0 (x) = 1. The mapping function of IterNorm is g(λ) = f T ( λ tr(Σ) )/ tr(Σ). Without calculating λ or U, IterNorm implicitly maps ∀λ i ∈ λ(Z) to λ i = λi tr(Σ) f T 2 ( λi tr(Σ)", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "). The proof is provided in Appendix B.1. For simplicity, we define the T-whitening function of IterNorm h T (x) = xf T2 (x), which obtains the spectrum of transformed output. Based on the fact that the covariance matrix of transformed output will be identity when T of IterNorm increases to infinity (<PERSON><PERSON> et al., 2005) , we thus have", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "∀λ i > 0, lim T →∞ h T ( λ i tr(Σ) ) = 1. (4) Different iteration numbers T of IterNorm imply different T-whitening functions h T (•). It is interest- ing to analyze the characteristics of h T (•). Proposition 1. Given x ∈ (0, 1), ∀T ∈ N we have h T (x) ∈ (0, 1) and h ′ T (x) > 0.", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "The proof is shown in Appendix A.1. Proposition 1 states h T (x) is a monotone increasing function for x ∈ (0, 1) and its range is also in (0, 1). Since λi tr(Σ) ∈ (0, 1), ∀λ i > 0, we have", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "EQUATION", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "Formula 5 indicates that IterNorm maps all non-zero eigenvalues to (0, 1) and preserves monotonicity.", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "Proposition 2. Given x ∈ (0, 1), ∀T ∈ N, we have h T +1 (x) > h T (x).", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "The proof is shown in Appendix A.2. Proposition 2 indicates that IterNorm gradually stretches the eigenvalues towards one as the iteration number T increases. This property of IterNorm theoretically shows that the spectrum of Z will have better condition if we use a larger iteration number T of IterNorm.", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "In summary, our analyses theoretically show that IterNorm gradually stretches the eigenvalues towards one as the iteration number T increases, and the smaller the eigenvalue is, the larger T is required to approach one.", "section": "IMPLICIT SPECTRAL TRANSFORMATION USING NEWTON'S ITERATION", "sec_num": "3.2.2"}, {"text": "It is expected that IterNorm, as a kind of spectral transformation, can avoid collapse and obtain good performance in SSL, due to its benefits in approximating whitening for supervised learning (<PERSON> et al., 2019) . However, we empirically observe that IterNorm suffers severe dimensional collapse and mostly fails to train the model in SSL (we postpone the details in Section 4.2.). Based on the analyses in Section 3.2 and 3.2.2, we propose a simple solution by adding an extra penalty named trace loss on the transformed output Z by IterNorm to ensure a well-conditioned spectrum. It is clear that the sum of eigenvalues of Σ Z is less than or equal to d, we thus propose a trace loss that encourages the trace of Σ Z to be its maximum d, when d ≤ m. In particular, we design a new method called IterNorm with trace loss (INTL) for optimizing the SSL model as 2 :", "section": "ITERATIVE NORMALIZATION WITH TRACE LOSS", "sec_num": "4"}, {"text": "EQUATION", "section": "ITERATIVE NORMALIZATION WITH TRACE LOSS", "sec_num": "4"}, {"text": "where Z = F θ (•) and Z = IterN orm(Z). Eqn. 6 can be viewed as an optimization problem over θ to encourage the trace of Z to be d.", "section": "ITERATIVE NORMALIZATION WITH TRACE LOSS", "sec_num": "4"}, {"text": "In this section, we theoretically prove that INTL can avoid collapse, and INTL modulates the spectrum of embedding towards an equal-eigenvalue distribution during the course of optimization.", "section": "THEORETICAL ANALYSIS", "sec_num": "4.1"}, {"text": "Note that Σ Z can be expressed using the T-whitening function h T (•) as", "section": "THEORETICAL ANALYSIS", "sec_num": "4.1"}, {"text": "Σ Z = d i=1 h T (x i )u i u T i ,", "section": "THEORETICAL ANALYSIS", "sec_num": "4.1"}, {"text": "where (d) indicates that IterNorm (without trace loss) suffers from numeric divergence when using a large iteration number, e.g. T = 9. It is noteworthy that when T ≥ 11, the loss values are all NAN, making the model unable to be trained. Similar phenomena can be observed when using other datasets (e.g., ImageNet) and other networks (e.g., ResNet-50).", "section": "THEORETICAL ANALYSIS", "sec_num": "4.1"}, {"text": "x i = λ i /tr(Σ) ≥ 0 and", "section": "THEORETICAL ANALYSIS", "sec_num": "4.1"}, {"text": "problem over θ (Eqn. 6) can be transformed as the following optimization problem over x (Eqn. 7) without changing the optimal value (please see Appendix B.2 for the details of derivation):", "section": "THEORETICAL ANALYSIS", "sec_num": "4.1"}, {"text": "EQUATION", "section": "THEORETICAL ANALYSIS", "sec_num": "4.1"}, {"text": "where u ji is the j-th elements of vector u i . In this formulation, we can prove that our proposed INTL can theoretically avoid collapse, as long as the iteration number T of IterNorm is larger than zero.", "section": "THEORETICAL ANALYSIS", "sec_num": "4.1"}, {"text": "Theorem 2. Let x ∈ [0, 1] d , ∀T ∈ N + , IN T L(x) shown in Eqn. 7 is a strictly convex function. x * = [ 1 d , • • • , 1 d ] T", "section": "THEORETICAL ANALYSIS", "sec_num": "4.1"}, {"text": "is the unique minimum point as well as the optimal solution to IN T L(x). The proof is provided in Appendix B.2. Based on Theorem 2, INTL modulates the spectrum of embedding to be equal-eigenvalues during the backward pass, which provides a theoretical guarantee to avoid dimensional collapse. Connection to hard whitening. Hard whitening methods, like W-MSE (<PERSON><PERSON><PERSON><PERSON> et al., 2021) and shuffle-DBN (<PERSON><PERSON> et al., 2021) , design a whitening transformation over each view and minimize the distances between the whitened outputs from different views. This mechanism modulates the covariance matrix of embedding to be full-rank (<PERSON><PERSON> et al., 2022) . Our INTL designs an approximated whitening transformation using IterNorm and imposes an additional trace loss penalty on the (approximately) whitened output, which modulates the covariance matrix of embedding having equal eigenvalues. Connection to soft whitening. Soft whitening methods, like Barlow-Twins (Zbon<PERSON> et al., 2021) and VICReg (<PERSON><PERSON> et al., 2022) directly impose a whitening penalty as a regularization on the embedding. This modulates the covariance matrix of the embedding to be identity (with a fixed scalar γ, e.g., γI). Our INTL imposes the penalty on the transformed output, but can be viewed as implicitly modulating the covariance matrix of the embedding to be identity with a free scalar (i.e., having equal eigenvalues).", "section": "THEORETICAL ANALYSIS", "sec_num": "4.1"}, {"text": "Intuitively, INTL modulates the spectrum of embedding to be equal-eigenvalues during the backward pass, which is a stronger constraint than hard whitening (the full-rank modulation), but a weaker constraint than soft whitening (the whitening modulation). This preliminary but new comparison provides a new way to understand the whitening loss in SSL.", "section": "THEORETICAL ANALYSIS", "sec_num": "4.1"}, {"text": "In this section, we empirically show that IterNorm-only and trace-loss-only fail to avoid collapse, but IterNorm with trace loss can well avoid collapse. IterNorm fails to avoid collapse. In theory, IterNorm can map all non-zero eigenvalues to approach one, with a large enough T . In practice, it usually uses a fixed T , and it is very likely to encounter small eigenvalues during training. In this case, IterNorm cannot ensure the transformed output has a well-conditioned spectrum (Figure 3(b) ), which potentially results in dimensional collapse. One may use a large T , however, IterNorm will encounter numeric divergence upon further increasing the iteration number T , even though it has converged. E.g., IterNorm suffers from numeric divergence in Figure 3 (d) when using T = 9, since the maximum eigenvalue of whitened output is around 10 7 , significantly large than 1 (we attribute to the numeric divergence, since this result goes against Proposition 1 and 2, and we further validate it by monitoring the transformed output). It is noteworthy that when T ≥ 11, the loss values are all NAN, making the model unable to be trained. These problems make IterNorm difficult to avoid dimensional collapse in practice.", "section": "EMPIRICAL ANALYSIS", "sec_num": "4.2"}, {"text": "The Synergy between IterNorm and trace loss. IterNorm in combination with trace loss demonstrates significant differences compared to IterNorm-only. Our experimental results, as shown in Figure 3 ", "section": "EMPIRICAL ANALYSIS", "sec_num": "4.2"}, {"text": "In this section, we conduct experiments on standard SSL benchmarks to validate the effectiveness of our proposed INTL. We first evaluate the performance of INTL for classification on CIFAR-10/100 (<PERSON><PERSON><PERSON><PERSON>, 2009) , ImageNet-100 (<PERSON><PERSON> et al., 2020a) , and ImageNet (<PERSON><PERSON> et al., 2009) .", "section": "EXPERIMENTS ON STANDARD SSL BENCHMARK", "sec_num": "5"}, {"text": "Then we evaluate the effectiveness in transfer learning, for a pre-trained model using INTL. We provide the full PyTorch-style algorithm in Appendix C as well as details of implementation and computational overhead in Appendix E. We observe that our INTL performs even better when utilized in conjunction with the Exponential Moving Average (EMA) technique, as employed in BYOL and MoCo. This combination yielded a top-1 accuracy of 74.3% after 800 epochs of training.", "section": "EXPERIMENTS ON STANDARD SSL BENCHMARK", "sec_num": "5"}, {"text": "We examine the representation quality by transferring our pre-trained model to other tasks, including COCO (<PERSON> et al., 2014) object detection and instance segmentation. We use the baseline of the detection codebase from <PERSON><PERSON><PERSON> (<PERSON> et al., 2020) for INTL. The results of baselines shown in Table 3 are mostly inherited from (<PERSON> & <PERSON>, 2021) . We observe that INTL performs much better than other state-of-the-art approaches on COCO object detection and instance segmentation, which shows the great potential of INTL in transferring to downstream tasks.", "section": "TRANSFER TO DOWNSTREAM TASKS", "sec_num": "5.2"}, {"text": "We conducte a comprehensive set of ablation experiments to assess the robustness and versatility of our INTL in Appendix F. These experiments cover various aspects, including batch sizes, embedding dimensions, the use of multi-crop augmentation, semi-supervised training, the choice of Vision Transformer (ViT) backbones and adding trace loss to other methods. Through these experiments, we gain valuable insights into how INTL performs under different conditions and configurations, shedding light on its adaptability and effectiveness in diverse scenarios. The results collectively reinforce the notion that INTL is a robust and flexible self-supervised learning method capable of delivering strong performance across a wide range of settings and data representations. Notably, our INTL achieved a remarkable top-1 accuracy of 76.6% on ImageNet linear evaluation with ResNet-50 when employing multi-crop augmentation, surpassing even the common supervised baseline of 76.5%.", "section": "ABLATION STUDY", "sec_num": "5.3"}, {"text": "In this paper, we proposed spectral transformation (ST) framework to modulate the spectrum of embedding and to seek for functions beyond whitening that can avoid dimensional collapse. Our proposed IterNorm with trace loss (INTL) is well-motivated, theoretically demonstrated, and empirically validated in avoiding dimension collapse. Comprehensive experiments have shown the merits of INTL for achieving state-of-the-art performance for SSL in practice. We showed that INTL modulates the spectrum of embedding to be equal-eigenvalues during the backward pass, which is a stronger constraint than hard whitening (the full-rank modulation), but a weaker constraint than soft whitening (the whitening modulation). This preliminary but new results provides a potential way to understand and compare SSL methods.", "section": "CONCLUSION", "sec_num": "6"}, {"text": "A.1 PROOF OF PROPOSITION.1", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "Proposition 1. Given x ∈ (0, 1), ∀T ∈ N we have h T (x) ∈ (0, 1) and h ′ T (x) > 0.", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "Proof. We know the iterative function f T (x) satisfies", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "f k+1 (x) = 3 2 f k (x) - 1 2 xf k 3 (x), k ≥ 0; f 0 (x) = 1 (8)", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "We define h T (x) = xf T 2 (x). When x = 1, it is easy to verify ∀T ∈ N, h T (1) = f T (1) = 1. We first prove f T (x) > 0 and h ′ T (x) > 0 by mathematical induction. (1) When T = 0, we have f 0 (x) = 1 > 0, and h 0 (x) = x, h ′ 0 (x) = 1 > 0.", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "(2) Assuming it holds when T = k, we have", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "f k (x) > 0 and h ′ k (x) > 0. Based on h ′ k (x) = f k (x)[f k (x) + 2xf ′ k (x)],", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "we have:", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "f k (x) + 2xf ′ k (x) > 0 (9) Since h k (1) = 1, h ′ k (x) > 0 and h k (x)", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "is continuous, we have ∀x ∈ (0, 1), h k (x) < 1. We thus can obtain:", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "f k+1 (x) = 1 2 f k (x)[3 -xf 2 k (x)] = 1 2 f k (x)[3 -h k (x)] > 0 (10) Furthermore, h ′ k+1 (x) = f k+1 (x)[f k+1 (x) + 2xf ′ k+1 (x)],", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "where", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "f k+1 (x) + 2xf ′ k+1 (x) = 3 2 [f k (x) + 2xf ′ k (x)] - 3 2 xf 3 k (x) -3x 2 f 2 k (x)f ′ k (x) = 3 2 [f k (x) + 2xf ′ k (x)] - 3 2 xf 2 k (x)[f k (x) + 2xf ′ k (x)] = 3 2 [1 -xf 2 k (x)][f k (x) + 2xf ′ k (x)] = 3 2 [1 -h k (x)][f k (x) + 2xf ′ k (x)] So we have h ′ k+1 (x) = 3 2 f k+1 (x)[1 -h k (x)][f k (x) + 2xf ′ k (x)] > 0.", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "Combining the result in Eqn. 10, we thus have it holds when T = k + 1.", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "As a result, we have ∀T ∈ N, f T (x) > 0 and h ′ T (x) > 0, when x ∈ (0, 1). Since h T (1) = 1 and h T (x) is continuous, we have h T (x) < 1. Besides, we have h T (x) = xf T 2 (x) > 0, then h T (x) ∈ (0, 1).", "section": "A PROOFS OF PROPOSITION", "sec_num": null}, {"text": "Proposition 2. Given x ∈ (0, 1), ∀T ∈ N, we have h T +1 (x) > h T (x).", "section": "A.2 PROOF OF PROPOSITION.2", "sec_num": null}, {"text": "Proof. According to proof of Proposition.1, we have that when x ∈ (0, 1) and ∀T ∈ N, f T (x) > 0 and h T (x) = xf T 2 (x) ∈ (0, 1).", "section": "A.2 PROOF OF PROPOSITION.2", "sec_num": null}, {"text": "T +1 (x) > h T (x) ⇐⇒ f T +1 (x) > f T (x). It is obvious that f k+1 (x) -f k (x) = 3 2 f k (x) - 1 2 xf 3 k (x) -f k (x) = 1 2 f k (x) - 1 2 xf 3 k (x) = 1 2 f k (x)[1 -xf 2 k (x)] = 1 2 f k (x)[1 -h k (x)] > 0 So given x ∈ (0, 1), ∀T ∈ N, we have h T +1 (x) > h T (x).", "section": "Therefore, we have h", "sec_num": null}, {"text": "B.1 PROOF OF THEOREM 1.", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Theorem 1. Define one-variable iterative function f T (x), satisfying", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "f k+1 (x) = 3 2 f k (x) -1 2 xf k 3 (x), k ≥ 0; f 0 (x) = 1.", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "The mapping function of IterNorm is", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "g(λ) = f T ( λ tr(Σ) )/ tr(Σ), so that ∀λ i ∈ λ(Z), IterNorm maps it to λ i = λi tr(Σ) f T 2 ( λi tr(Σ) ). Proof. Given Σ = UΛU T , Λ = diag(λ 1 , . . . , λ d ), U = [u 1 , . . . , u d ].", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Following the calculation steps of IterNorm, we have", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Σ N = Σ/tr(Σ) = d i=1 λ i tr(Σ) u i u i T (11) Define Φ ′ T = d i=1 1 tr(Σ) f T ( λ i tr(Σ) )u i u i T (12) Based on Φ T = d i=1 g(λ i )u i u i T , if we can prove Φ ′ T = Φ T , we will have g(λ) = 1 √ tr(Σ) f T ( λ tr(Σ) ) Define P ′ T = tr(Σ)Φ ′ T , then we have Φ ′ T = Φ T ⇐⇒ P ′ T = P T .", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "We can prove P ′ T = P T by mathematical induction.", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "(1) When T = 0,", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "f 0 ( λi tr(Σ) ) = 1, P ′ 0 = P 0 = I (2) When T ≥ 1, assume that P ′ T -1 = P T -1 , thus P T = 3 2 P T -1 - 1 2 P 3 T -1 Σ N = 3 2 P ′ T -1 - 1 2 (P ′ T -1 ) 3 Σ N", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "According to the definition of P ′ T ,", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "P ′ T -1 = d i=1 f T -1 ( λ i tr(Σ) )u i u i T", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Because ∀i, u i T u i = 1 and ∀i ̸ = j, u i T u j = 0,", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "P 3 T -1 Σ N = (P ′ T -1 ) 3 Σ N = d i=1 f T -1 ( λ i tr(Σ) )u i u i T 3 d i=1 λ i tr(Σ) u i u i T = d i=1 f 3 T -1 ( λ i tr(Σ) ) λ i tr(Σ) u i u i T", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Therefore, we have", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "P T = 3 2 P ′ T -1 - 1 2 (P ′ T -1 ) 3 Σ N = 3 2 d i=1 f T -1 ( λ i tr(Σ) )u i u i T - 1 2 d i=1 f 3 T -1 ( λ i tr(Σ) ) λ i tr(Σ) u i u i T = d i=1 3 2 f T -1 ( λ i tr(Σ) ) - 1 2 f 3 T -1 ( λ i tr(Σ) ) λ i tr(Σ) u i u i T Note f T ( λ i tr(Σ) ) = 3 2 f T -1 ( λ i tr(Σ) ) - 1 2 f 3 T -1 ( λ i tr(Σ) ) λ i tr(Σ)", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "So that", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "P T = d i=1 f T ( λ i tr(Σ) )u i u i T = P ′ T", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "We obtain that", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Φ T = Φ ′ T = d i=1 1 tr(Σ) f T ( λ i tr(Σ) )u i u i T = U 1 tr(Σ) f T ( Λ tr(Σ) )U T", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Thus, the mapping function of IterNorm is g(λ) = f T ( λ tr(Σ) )/ tr(Σ). The whitened output is", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Z = Φ T Z c = U 1 √ tr(Σ) f T ( Λ tr(Σ) )U T Z c . The covariance matrix of Z is Σ Z = 1 m Z Z T = U Λ tr(Σ) f T 2 ( Λ tr(Σ) )U T = d i=1 λ i tr(Σ) f T 2 ( λ i tr(Σ) )u i u i T So that ∀λ i ∈ λ(Z), IterNorm maps it to λ i = λi tr(Σ) f T 2 ( λi tr(Σ) ) which is a special instance of Spectral Transformation. B.2 PROOF OF THEOREM 2. Theorem 2. Let x ∈ [0, 1] d , ∀T ∈ N + , IN T L(x) shown in Eqn. 7 is a strictly convex function. x * = [ 1 d , • • • , 1 d ]", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "T is the unique minimum point as well as the optimal solution to IN T L(x).", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Proof. The INTL can be viewed as the following optimization problem:", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "EQUATION", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "where Z = F θ (•) and Z = IterN orm(Z). Eqn. 6 can be viewed as a optimization problem over θ to encourage the trace of Z to be d.", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Let (x 1 , • • • , x d ) = φ(Z)", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": ", where x i = λ i /tr(Σ) as defined in the submitted paper. If Z ∈ R d×m , φ(•) will be surjective from R d×m to D", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "x = {x ∈ [0, 1] d : x 1 + • • • + x d = 1}. When the range of F θ (•) is wide enough, for example, F θ (•) is surjective from θ ∈ Θ to Z ∈ R d×m .", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Here we can view F θ (•) as a function over θ, since the input is given and fixed. Then φ(F θ (•)) is surjective from θ ∈ Θ to x ∈ D x , meaning that if we find the optimal solution x * , we are able to get the corresponding θ * ∈ Θ, subject to x * = φ(F θ * (•)). On the contrary, for any θ ∈ Θ, we can get", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "x = φ(F (•)) ∈ D x .", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Therefore, the optimization expression for minimizing INTL can be written as follows which have the same range and optimal value as Eqn. 6:", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "EQUATION", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "We denote the <PERSON><PERSON><PERSON> function of", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "P IN T L is that L(x; α, µ) = IN T L(x) + d i=1 α i (-x i ) + µ d i=1 x i -1 B.2.1 CONVEXITY AND CONCAVITY OF h T (x)", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Before calculating extreme points of P IN T L , we first consider the convexity and concavity of h T (x) which is critical to proof.", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "When T = 0, we have h 0 (x) = x, so h ′′ 0 (x) = 0. (1) When T = 1, we have", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "h 1 (x) = f 1 2 (x) = 9 4 x -3 2 x 2 + 1 4 x 3 , so h ′′ 1 (x) = 3 2 (x -2) < 0.", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "(2) Assume that when T = k, h ′′ k (x) < 0 holds. We can easily get following propositions by derivation:", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "EQUATION", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "For convenience in our calculation, let", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "a = f k (x), b = f ′ k (x), c = f ′′ k (x)", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": ", and h = h k (x) = xa 2 . We split Eqn. 17 into three parts and take Eqn. 15 and 16 into calculation:", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "4f k+1 (x)f ′ k+1 (x) = 4( 3 2 a - 1 2 ah)( 3 2 b - 1 2 a 3 - 3 2 bh) = a(3 -h)(3b -a 3 -3bh) 2x[f ′ k+1 (x)] 2 = 2x( 3 2 b - 1 2 a 3 - 3 2 bh) 2 = 1 2 x(3b -a 3 -3bh) 2 2xf k+1 (x)f ′′ k+1 (x) = 2( 3 2 a - 1 2 ah)[ 3 2 c(1 -h) -3a 2 b -3xab 2 ] = 1 2 ax(3 -h)[3c(1 -h) -6a 2 b -6xab 2 ]", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "We denote that all eigenvalues of (∇g j )(∇g j ) T are (∇g j ) T (∇g j ), 0, • • • , 0. All eigenvalues are non-negtive, denoting that 2(∇g j )(∇g j ) T is semi-positive. Now we denote that the Hessen Matrix of IN T L(x) is", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "∇ 2 IN T L(x) = d j=1 ∇ 2 (g 2 j ) = 2 d j=1 (∇g j )(∇g j ) T + 2 d j=1 g j ∇ 2 g j where 2 d j=1 g j ∇ 2 g j = 2         - d j=1 u 2 j1 h ′′ T (x 1 )g j . . . - d j=1 u 2 jd h ′′ T (x d )g j        ", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "We denote that h ′′ T (x i ) < 0, g j > 0, and u ji are not all zeros for a certain i (since", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "d j=1 u 2 ji = 1). Therefore, - d j=1 u 2 ji h ′′ T (x i )g j > 0 and 2 d j=1", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "g j ∇ 2 g j must be a positive matrix.", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Since 2 d j=1 (∇g j )(∇g j ) T is semi-positive, then we can denote that ∇ 2 IN T L is positive.", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "Therefore, IN T L(x) is strictly convex about x on (0, 1) d .", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "And for IN T L(x) is continuous, the minimum point on [0, 1] d is the same as that on (0, 1) d .", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "While the constraints of (P IN T L ) form a convex set, (P IN T L ) must be a convex programming, which means that the KKT point of (P IN T L ) is its unique extreme point, and the global minimum point in the same time.", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "We denote that the KKT conditions of", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "(P IN T L ) is that            ∂L ∂xi = 0, i = 1, • • • , d α i (-x i ) = 0, i = 1, • • • , d α i ≥ 0, i = 1, • • • , d d i=1 x i -1 = 0", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "We can identify one of the solutions to the KKT conditions is that", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "   x = [ 1 d , • • • , 1 d ] T α = 0 µ = -2h ′ T ( 1 d )[h T ( 1 d ) -1]", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "It is easy to identify the last three equations in KKT conditions. As for the first equation, for all t = 1, • • • , d, we have", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "∂L ∂x t = 2h ′ T (x t ) d i=1 d j=1 [h T (x i ) -1]u 2 ji u 2 jt -α i + µ = 2h ′ T ( 1 d ) d i=1 d j=1 [h T ( 1 d ) -1]u 2 ji u 2 jt + µ = 2h ′ T ( 1 d ) d j=1 [h T ( 1 d ) -1] d i=1 u 2 ji u 2 jt + µ = 2h ′ T ( 1 d ) d j=1 [h T ( 1 d ) -1]u 2 jt + µ = 2h ′ T ( 1 d )[h T ( 1 d ) -1]   d j=1 u 2 jt   + µ = 2h ′ T ( 1 d )[h T ( 1 d ) -1] + µ = 0 Therefore, x * = [ 1 d , • • • , 1 d ]", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "T is the optimal solution to (P IN T L ). INTL promotes the equality of all eigenvalues in the optimization process, which provides a theoretical guarantee to avoid dimensional collapse.", "section": "B PROOFS OF THEOREM", "sec_num": null}, {"text": "The description of our paper is based on batch whitening (BW) (<PERSON><PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON> et al., 2021) , and it can extend similarly for channel whitening (CW) (<PERSON><PERSON> et al., 2022) , where the covariance matrix of Z is calculated as Σ = 1 d Z T Z. We implement INTL based on CW, considering CW is more effective when the batch size m is relatively small. Given the centralized embedding of two positive pairs Z (v) , Z (v) ∈ R d×m and v ∈ {1, 2}, we use IterNorm to obtain the approximately whitened output", "section": "C ALGORITHM OF INTL", "sec_num": null}, {"text": "Z (v) = [ẑ (v) 1 , . . . , ẑ(v) m ].", "section": "C ALGORITHM OF INTL", "sec_num": null}, {"text": "The loss functions used in our method are", "section": "C ALGORITHM OF INTL", "sec_num": null}, {"text": "EQUATION", "section": "C ALGORITHM OF INTL", "sec_num": null}, {"text": "EQUATION", "section": "C ALGORITHM OF INTL", "sec_num": null}, {"text": "where L M SE indicates MSE of L 2 -normalized vectors which minimizes the distance between Z (1) and Z (2) . Here we simplify the expression of L trace in Eqn. 6, because off-diagonal elements of Σ Z does not need to be calculated. β is the trade-off between L M SE and INTL.", "section": "C ALGORITHM OF INTL", "sec_num": null}, {"text": "In our experiments, we observe that when the iteration number T of IterNorm is fixed, the coefficient β that obtains good performance has only relevant to the batch size. So we fix the iteration number T to 4 and empirically regress β with various batch sizes and obtain that β = 0.01 * (log 2 bs -3) where bs means the batch size and bs > 8. We keep the iteration number T of IterNorm and the coefficient β fixed in this form (i.e., β is determined given the batch size) across all the datasets and architectures, so our INTL can be directly applied to other datasets and models without tuning the coefficient.", "section": "C ALGORITHM OF INTL", "sec_num": null}, {"text": "For clarity, we also describe the algorithm of INTL in PyTorch-style pseudocode, shown in Figure 4 (a). In section 3.2 of the submitted paper, we conduct experiments on the 2D dataset and report the results on with varying p. Here, we provide the details of the experimental setup, and further show the results of IterNorm (<PERSON> et al., 2019) for SSL in this 2D dataset.", "section": "C ALGORITHM OF INTL", "sec_num": null}, {"text": "We synthesize a two-dimensional dataset with isotropic Gaussian blobs containing 512 sample points as shown in Figure 5 (a). We construct a toy Siamese network (a simple three-layer neural network, including three fully connected (FC) layers, with BN and ReLU appended to the first two) as the encoder for this dataset. The dimensions of the network are (2 -16) -(16 -16) -(16 -2) that each bracket represents the input and output dimensions of each FC layer respectively. We then use MSE as the loss function and do not normalize the features before calculating the loss function.", "section": "D.1.1 DETAILS OF EXPERIMENTAL SETUPS", "sec_num": null}, {"text": "We train the model by randomly shuffling the data into mini-batches, and set the batch size to 32. We use the stochastic gradient descent (SGD) algorithm with a learning rate of 0.1. In terms of the data transformation, we only apply Gaussian noise as data augmentation and generate 2 views from each Figure 6 : Investigate the spectrum of transformed output Z (solid lines) and the corresponding embedding Z (dashed lines) using IterNorm for SSL with different iteration numbers T . We show the evolution of eigenvalues during training on the toy 2D dataset (Note that there are only two eigenvalues and we ignore the larger one because it always remains a high value during training). In particular, (a) shows the results with a well-conditioned initial spectrum while (b) with a illconditioned one.", "section": "D.1.1 DETAILS OF EXPERIMENTAL SETUPS", "sec_num": null}, {"text": "To figure out the failure of IterNorm (<PERSON> et al., 2019) for SSL, we further conduct experiments to investigate the spectrum of the whitened output Z using IterNorm on this synthetic 2D dataset for intuitive analyses. The output dimension of the toy model is 2, so there are only two eigenvalues of the covariance matrix of the output. We then track alterations of the two eigenvalues during training. IterNorm can obtain an idealized whitened output with a small iteration number (e.g.,T=5, as recommend in (<PERSON> et al., 2019) ) and avoid collapse, if the embedding Z has a well-conditioned spectrum3 (Figure 6(a) ). However, if the embedding Z has a ill-conditioned spectrum as shown in Figure 6 (b), IterNorm fails to pull the small eigenvalue to approach 1 which results in dimensional collapse.", "section": "D.1.2 RESULTS OF ITERNORM FOR SSL", "sec_num": null}, {"text": "In section 3 and 4 of the submitted paper, we conduct several experiments on CIFAR-10 to illustrate our analysis. We provide a brief description of the setup in the caption of Figure 1 Training Settings. We use the ResNet-18 as the encoder (the dimension of encoding is 512.), a two layer MLP with ReLU and BN appended as the projector (the dimension of the hidden layer and embedding are 1024 and 128 respectively). The model is trained on CIFAR-10 with a batch size of 256, using Adam optimizer (Kingma & Ba, 2014) with a learning rate of 3 × 10 -3 , and learning rate warm-up for the first 500 iterations and a 0.2 learning rate drop at the last 50 and 25 epochs. The weight decay is set as 10 -6 . All transformations are performed with 2 positives extracted per image with standard data argumentation (see Section E.3 for details). We use the same evaluation protocol as in W-MSE (<PERSON><PERSON><PERSON><PERSON> et al., 2021) .", "section": "D.2 EXPERIMENTS ON CIFAR-10", "sec_num": null}, {"text": "Method Settings. We use MSE of L 2 -normalized vectors to be the loss function in all experiments. Specifically, in Figure 3 ", "section": "D.2 EXPERIMENTS ON CIFAR-10", "sec_num": null}, {"text": "One issue with employing the spectral transformation g(λ) = λ -p (where p is approximately 0.5) is the risk of numerical instability during the calculation of eigenvalues λ and eigenvectors U via eigen-decomposition. This instability can arise when dealing with an ill-conditioned covariance matrix, as noted in (<PERSON><PERSON><PERSON> et al., 2019) . In this study, we empirically validate the existence of this phenomenon in the context of self-supervised pre-training. It's important to mention that we primarily focus on the special case of p = 0.5, referred to as hard whitening, as similar phenomena are observed when p is set near 0.5.", "section": "D.3 NUMERICAL INSTABILITY OF SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": null}, {"text": "To assess the generality of this phenomenon, we conduct experiments on both ImageNet with ResNet-50 and CIFAR-10 with ResNet-18. We maintain a fixed batch size of 512 and manipulate the shape of the covariance matrix by adjusting the embedding dimension d (where the covariance matrix has a shape of d × d). The models undergo 6000 iterations, and we monitor the inverse of the condition number (c -1 = λ d λ1 ) to ascertain the ill-conditioned nature of the covariance matrix. The experimental results, depicted in Figure 7 , lead to the following key observations:", "section": "D.3 NUMERICAL INSTABILITY OF SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": null}, {"text": "(a) Training crashes when the embedding dimension exceeds the batch size (e.g., d = 1024 or 2048). In such cases, the covariance matrix becomes theoretically singular, and computing the inverse of the eigenvalues introduces numerical errors. However, in practice, the minimum eigenvalue of the covariance matrix is likely a very small non-zero value due to precision rounding or the use of a small constant. Consequently, the covariance matrix may already be ill-conditioned from the start of training. Both Figure 7 In such cases, it's challenging to definitively establish whether the covariance matrix is singular. However, our observations from Figure 7 suggest that the covariance matrix tends towards illconditioning when d = 512. The inverse of the condition number progressively decreases during training, eventually leading to training instability.", "section": "D.3 NUMERICAL INSTABILITY OF SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": null}, {"text": "(c) There is a possibility of training instability when the embedding dimension is less than the batch size. In these situations, we initially observe that the covariance matrix remains well-conditioned. However, this favorable condition is not consistently maintained throughout training. We notice that well-conditioning suddenly breaks after a few iterations, leading to model collapse for d = 64 or d = 128. Interestingly, training does not crash when d = 256. This phenomenon was briefly discussed in (<PERSON><PERSON><PERSON><PERSON> et al., 2021) , suggesting that stability can be improved by setting m = 2d.", "section": "D.3 NUMERICAL INSTABILITY OF SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": null}, {"text": "We confirm the presence of numerical instability when employing hard whitening (<PERSON><PERSON><PERSON><PERSON> et al., 2021) , as indicated by the above analysis. While one can mitigate this instability empirically by setting m = 2d, our experiments reveal that training crashes due to numerical instability can still occur at various points during training. In our extensive experimentation (with 10 random seeds and longer training iterations), we observed instances of numerical issues-approximately 3-4 times-occurring at different stages, including early, mid, or even towards the end of training. Even though it is possible to resume training using saved checkpoints in the event of a crash, this significantly limits the practical applicability of long-term pre-training.", "section": "D.3 NUMERICAL INSTABILITY OF SPECTRAL TRANSFORMATION USING POWER FUNCTIONS", "sec_num": null}, {"text": "In this section, we provide the details of implementation and training protocol for the experiments on large-scale ImageNet (<PERSON><PERSON> et al., 2009) , medium-scale ImageNet-100 (<PERSON><PERSON> et al., 2020a) and smallscale CIFAR-10/100 (<PERSON><PERSON><PERSON><PERSON>, 2009) classification as well as transfer learning to COCO (<PERSON> et al., 2014) object detection and instance segmentation. We also provide computational overhead of INTL pre-training on ImageNet.", "section": "E DETAILS OF EX<PERSON><PERSON>IMENTS ON STANDARD SSL BENCHMARK", "sec_num": null}, {"text": "• CIFAR-10 and CIFAR-100 (<PERSON><PERSON><PERSON><PERSON>, 2009) , two small-scale datasets composed of 32 × 32 images with 10 and 100 classes, respectively.", "section": "E.1 DATASETS", "sec_num": null}, {"text": "• ImageNet-100 (<PERSON><PERSON> et al., 2020a) , a random 100-class subset of ImageNet (<PERSON><PERSON> et al., 2009) .", "section": "E.1 DATASETS", "sec_num": null}, {"text": "• ImageNet (<PERSON><PERSON> et al., 2009) , the well-known largescale dataset with about 1.3M training images and 50K test images, spanning over 1000 classes.", "section": "E.1 DATASETS", "sec_num": null}, {"text": "• COCO2017 (<PERSON> et al., 2014) , a large-scale object detection, segmentation, and captioning dataset with 330K images containing 1.5 million object instances.", "section": "E.1 DATASETS", "sec_num": null}, {"text": "In section 5.1 of the paper, we compare our INTL to the state-of-the-art SSL methods on large-scale ImageNet classification. Here, we describe the training details of these experiments. 7 . The details of image transformation are shown in Table 8 . For evaluation, we use the same setup of protocol as in W-MSE (<PERSON><PERSON><PERSON><PERSON> et al., 2021) : training the linear classifier for 500 epochs using the Adam optimizer and the labeled training set of each specific dataset, without data augmentation; the learning rate is exponentially decayed from 10 -2 to 10 -6 and the weight decay is 5 × 10 -6 .", "section": "E.2 EXPERIMENT ON IMAGENET", "sec_num": null}, {"text": "In addition, we also evaluate the accuracy of a k-nearest neighbors classifier (k-NN, k = 5) in these experiments. For other methods, we evaluate the models provided by (<PERSON> et al., 2022) to obtain k-NN accuracy which does not require additional parameters and training.", "section": "E.2 EXPERIMENT ON IMAGENET", "sec_num": null}, {"text": "In this part, we describe the training details of experiments for transfer learning. Our implementation is based on the released codebase of MoCo (<PERSON> et al., 2020) 4 for transfer learning to object detection and instance segmentation tasks. We use the default hyper-parameter configurations from the training scripts provided by the codebase for INTL, using our 200-epoch and 800-epoch pre-trained model on ImageNet.", "section": "E.4 EXPERIMENTS FOR TRANSFER LEARNING", "sec_num": null}, {"text": "For the experiments of COCO detection and COCO instance segmentation, we use Mask R-CNN (1× schedule) fine-tuned in COCO 2017 train, evaluated in COCO 2017 val. The Mask R-CNN model is with the C4-backbone. Our INTL is performed with 3 random seeds, with mean and standard deviation reported.", "section": "E.4 EXPERIMENTS FOR TRANSFER LEARNING", "sec_num": null}, {"text": "In Table 9 , we report compute and GPU memory requirements based on our implementation for different settings on ImageNet with ResNet-50. The batch size is 256, and we train each model with 2 A100-PCIE-40GB GPUs, using mixed precision and py-torch optimized version of synchronized batch-normalization layers. ", "section": "E.5 COMPUTATIONAL OVERHEAD", "sec_num": null}, {"text": "ImageNet (<PERSON><PERSON> et al., 2009) is subject to the ImageNet terms of access: (contributors, 2020)", "section": "G LICENSES OF DATASETS", "sec_num": null}, {"text": "COCO (<PERSON> et al., 2014) . The annotations are under the Creative Commons Attribution 4.0 License. The images are subject to the Flickr terms of use (Flickr, 2020) .", "section": "G LICENSES OF DATASETS", "sec_num": null}, {"text": "The embedding is usually centralized by performing Z := Z(I -1 m 1 • 1 T ) for whitening, and we assume Z is centralized in this paper for simplifying discussion.", "section": "", "sec_num": null}, {"text": "The complete loss function of INTL is LINT L = LMSE + β • Ltrace, where the coefficient β is fixed across all datasets and architectures, and its determination is elaborated in 'Algorithm of INTL' of Appendix C. To simplify the discussion, we omit the LMSE term here, without compromising the validity.", "section": "", "sec_num": null}, {"text": "A well-conditioned spectrum means that the condition number c = λ 1 λ d is small. Note λ1 is the maximum eigenvalue and λ d is the minimum one.", "section": "", "sec_num": null}, {"text": "https://github.com/facebookresearch/moco/tree/main/detection under the CC-BY-NC 4.0 license.", "section": "", "sec_num": null}], "back_matter": [{"text": "This work was partially supported by the National Science and Technology Major Project under Grant 2022ZD0116310, National Natural Science Foundation of China (Grant No. 62106012), the Fundamental Research Funds for the Central Universities.", "section": "ACKNOWLEDGMENTS", "sec_num": null}, {"text": "To ensure the reproducibility and comprehensiveness of our paper, we have included an appendix comprising six main sections. These sections serve various purposes:• Appendix A contains detailed proofs for the propositions presented in our work.• Appendix B provides in-depth proofs for the theorems introduced in our research.• Appendix C offers a comprehensive view of the INTL algorithm, including detailed formulas and PyTorch-style code for implementation. • Appendix D elaborates on the settings used in our analytical experiments, with reference to Figure 2 and Figure 3 . • Appendix E furnishes insights into the implementation details and computational intricacies of experiments conducted on standard SSL benchmarks, as discussed in Section 5. • Finally, Appendix F encompasses a comprehensive set of ablation experiments, assessing the robustness and versatility of our INTL method across various scenarios.Considering to construct the form of h ′′ k (x) = 2(2ab + xac + xb 2 ), we first calculate thatThen we calculate the left partFor convenience, letThen we haveHere we obtainand the Hessen Matrix of gPublished as a conference paper at ICLR 2024 Backbone and Projection. We use the ResNet-50 (<PERSON> et al., 2016) as the backbone and the output dimension is 2048. We use a 3-layers MLP as the projection: two hidden layers with BN and ReLU applied to it and a linear layer as output. We set dimensions of the hidden layer and embedding to 8192 as our initial experiments followed the settings of VICReg and Barlow Twins, both of which use a dimension of 8192 for the projection. Compared to a projection dimension of 2048, using a projection dimension of 8192 can bring about a 0.14% improvement in top-1 accuracy for INTL. Therefore, we followed this setting in subsequent experiments on ImageNet. We report that using a projection dimension of 8192 requires approximately 18% additional GPU memory and 2% time per epoch compared to using the one of 2048.Image Transformation Details. In image transformation, We use the same augmentation parameters as BYOL (Grill et al., 2020) . Each input image is transformed twice to produce the two distorted views. The image augmentation pipeline consists of the following transformations: random cropping, resizing to 224 × 224, horizontal flipping, color jittering, converting to grayscale, Gaussian blurring, and solarization. The details of parameters are shown in Table 4 .Optimizer and Learning Rate Schedule. We apply the SGD optimizer, using a learning rate of base-lr × BatchSize / 256 and cosine decay schedule. The base-lr for 100-epoch pre-training is 0.5, for 200(400)-epoch is 0.4 and for 800-epoch is 0.3. The weight decay is 10 -5 and the SGD momentum is 0.9. In addition, we use learning rate warm-up for the first 2 epochs of the optimizer.Evaluation Protocol. For linear classification, we train the linear classifier for 100 epochs with SGD optimizer (using a learning rate of base-lr × BatchSize / 256 with a base-lr of 0.2) and using MultiStepLR scheduler with γ = 0.1 dropping at the last 40 and 20 epochs. Note that when combining INTL with multi-crop in the ablation experiments, the base-lr is set to 0.4. The batch size and weight decay for both are 256 and 0 respectively.Exponential Moving Average. In the main text, we observe that our INTL can performs even better when utilized in conjunction with the Exponential Moving Average (EMA) technique. We set the base coefficient for momentum updating to 0.996 for all-epoch training. The momentum coefficient follows a cosine increasing schedule with final value of 1.0 as BYOL (Grill et al., 2020) . Batch size. Most SSL methods, including certain whitening-based methods, are known to be sensitive to batch sizes, e.g. SimCLR (Chen et al., 2020a) , SwAV (Caron et al., 2020) and W-MSE (Ermolov et al., 2021) Embedding dimension. Embedding dimension, the output dimension of the projection, is also a key element for most self-supervised learning methods, which may have a significant impact on training results. As illustrated in (Zbontar et al., 2021) , Barlow Twins is very sensitive to embedding dimension and it requires a large dimension (e.g. 8192 or 16384) to work well. We also test the robustness of INTL to embedding dimensions. Following the setup of (Chen et al., 2020a) and (Zbontar et al., 2021) , we train INTL on ImageNet for 300 epochs with the dimension ranging from 64 to 16384. As shown in Figure . 8, even when the embedding dimension is low as 64 or 128, INTL still achieves good results. These results show that INTL also has strong robustness to embedding dimensions.Multi-Crop. In the main text experiments, we employ the standard augmentation, which generates two augmented views for each sample. It's worth noting that multi-crop strategies, such as the one used by SwAV (Caron et al., 2020) , are widely recognized for enhancing the performance of SSL methods. For instance, SwAV achieves a remarkable Top-1 accuracy of 75.3% with multi-crop. Therefore, we also conduct experiments with INTL using multi-crop. We apply an efficient multi-crop approach that generates 6 views for each image, with sizes of 2 × 224 + 192 + 160 + 128 + 96, which is similar to the approach used by CLSA (Wang & Qi, 2022) . (Detailed parameter settings are provided in Table 5 ). The results are shown in Table 11 . When INTL is paired with multi-crop augmentation, it consistently achieve notable improvements in top-1 accuracy. For instance, after 800 epochs of pre-training, INTL attains an impressive top-1 accuracy of 76.6%, even surpassing the common supervised baseline of 76.5%. The incorporation of multi-crop augmentation enhances the performance of INTL, making it a promising method for self-supervised representation learning across a range of experimental setups.Semi-supervised training. For semi-supervised classification, we fine-tune our pre-trained INTL backbone and train the linear classifier on ImageNet for 20 epochs. We employ subsets of size 1% and 10%, following the same split as SimCLR. The optimization is performed using the SGD optimizer with a base learning rate of 0.006 for the backbone and 0.2 for the classifier, along with a cosine decay schedule. The semi-supervised results on the ImageNet validation dataset are presented in Table 12 , demonstrating that INTL performs well in semi-supervised training scenarios.Vison Transformer backbones. We conduct additional experiments using vision transformer (ViT) backbones for INTL. For comparison, we reproduce five other method under the same settings. The results are shown in Figure 9 , illustrating that INTL maintains strong performance when ViTs are used as backbones. This suggests that INTL exhibits robust generalization capabilities across different network architectures.Barlow Twins/VICReg with Trace loss. We conducte experiments on CIFAR-10/100 and ImageNet-100 to assess the impact of adding trace loss to Barlow Twins and VICReg, following the experimental setup outlined in Table 2 of our paper. We trained the models on CIFAR-10/100 for 200 epochs and on ImageNet-100 for 100 epochs. The coefficient of trace loss was set to 0.01, an empirically suitable value for both methods. The results are presented in the Table 13 . We observed that adding trace loss to Barlow Twins had a minor positive effect on performance, while introducing it to VICReg significantly reduced performance, particularly on ImageNet-100. We hypothesize that this discrepancy may arise from the influence of trace loss on the regularization strength of these", "section": "REPRODUCIBILITY STATEMENT", "sec_num": null}], "ref_entries": {"FIGREF0": {"type_str": "figure", "fig_num": "1", "num": null, "uris": null, "text": "Figure 1: The framework using spectral transformation (ST) to modulate the spectrum of embedding in joint embedding architecture for SSL."}, "FIGREF1": {"type_str": "figure", "fig_num": "2", "num": null, "uris": null, "text": "Figure 2: Investigate <PERSON> using power functions. We choose several p from 0 to 1.5. We show (a) the visualization of the toy model output; (b) top-1 and 5-nearest neighbors (5-nn) accuracy on CIFAR-10; (c) condition indicator of embedding Z and transformed output Z on CIFAR-10. We use the inverse of the condition number (IoC) in logarithmic scale with base 10 ( lgIoC = lgc -1 = lg λ d λ1 ) as the condition indicator. The results on CIFAR-10 are obtained through training with ResNet-18 for 200 epochs and averaged over five runs, with standard deviation shown as error bars. We show the details of experimental setup in Appendix D. Similar phenomena can be observed when using other datasets (e.g., ImageNet) and other networks (e.g., ResNet-50)."}, "FIGREF2": {"type_str": "figure", "fig_num": "3", "num": null, "uris": null, "text": "Figure3: Investigate the effectiveness of IterNorm with and without trace loss. We train the models on CIFAR-10 with ResNet-18 for 100 epochs. We apply IterNorm with various iteration numbers T , and show the results with (solid lines) and without (dashed lines) trace loss respectively. (a) The spectrum of the embedding Z; (b) The spectrum of the transformed output Z; (c) The top-1 accuracy. (d) indicates that IterNorm (without trace loss) suffers from numeric divergence when using a large iteration number, e.g. T = 9. It is noteworthy that when T ≥ 11, the loss values are all NAN, making the model unable to be trained. Similar phenomena can be observed when using other datasets (e.g., ImageNet) and other networks (e.g., ResNet-50)."}, "FIGREF3": {"type_str": "figure", "fig_num": null, "num": null, "uris": null, "text": "(a), empirically confirm that INTL effectively prevents dimensional collapse, aligning with the findings of Theorem 2. INTL encourages the uniformity of eigenvalues within the covariance matrix of the embedding Z, resulting in well-conditioned spectra for the transformed output (Figure3(b)) and impressive evaluation performance (Figure3(c)), even when the iteration count T is as low as 1. To further evaluate the performance of trace-loss-only, we conducte experiments under the same setup. Without IterNorm, trace-loss-only achieves a top-1 accuracy of only 16.15%, indicating significant collapse. Therefore, the efficacy of INTL, as well as the attainment of an optimal solution characterized by equal eigenvalues, is a result of the synergy between IterNorm and trace loss."}, "FIGREF4": {"type_str": "figure", "fig_num": "4", "num": null, "uris": null, "text": "Figure 4: Algorithm of INTL, PyTorch-style Pseudocode."}, "FIGREF5": {"type_str": "figure", "fig_num": "5", "num": null, "uris": null, "text": "Figure 5: Visualization of our synthetic 2D dataset. We show (a) the distribution of our 2D dataset; (b) the initial output of the toy Siamese network. sample point in mini-batches. We visualize the output of the initialized network without training in Figure 5(b). All runs are performed under the same random seed."}, "FIGREF6": {"type_str": "figure", "fig_num": "7", "num": null, "uris": null, "text": "Figure 7: Investigate numerical instability of spectral transformation using power functions for SSL. The numbers in the legend represent embedding dimensions and the batch size is fixed to 512. (a) trains models on ImageNet with ResNet-50; (b) trains models on CIFAR-10 with ResNet-18; The models are trained for 6000 iterations, and we track the inverse of condition number (c -1 = λ d λ1 ) in logarithmic scale with base 10 to judge whether the the covariance matrix is ill-conditioned. The models that were interrupted before the end of the training indicate training crash caused by numerical instability."}, "FIGREF7": {"type_str": "figure", "fig_num": null, "num": null, "uris": null, "text": "of the paper for the experiments of training the models with INTL, we simply set the trade-off parameter β between MSE and INTL as follows: β = 0.05 for T = 5, β = 0.5 for T = 3 and β = 5 for T = 1 without fine-tuning. The details of INTL algorithm please refer to Section C."}, "FIGREF8": {"type_str": "figure", "fig_num": null, "num": null, "uris": null, "text": "(a)  and (b) illustrate that when d = 1024 or 2048, the inverse of the condition number is approximately 10 -12 ∼ 10 -10 , indicating severe ill-conditioning from the beginning, resulting in rapid training breakdown."}, "FIGREF9": {"type_str": "figure", "fig_num": "9", "num": null, "uris": null, "text": "Figure9: Abalation experiments using vision transformer (ViT) backbones. We train our INTL well as other 5 methods (including SimCLR, W-MSE, DINO, Barlow Twins, and VICReg) for comparison when using ViTs as backbones. Our training setup involved ViT-tiny for 200 epochs on CIFAR-10/100 and ViT-small for 100 epochs on ImageNet-100. The settings were kept consistent with DINO, with the exception of the embedding dimension for W-MSE, which was set to 64, while other methods used 2048. We evaluated their classification performance using both a linear classifier and a 5-nearest neighbors classifier. The results for CIFAR-10, CIFAR-100, and ImageNet-100 are presented in panels (a), (b), and (c) respectively."}, "TABREF0": {"type_str": "table", "num": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">CIFAR-10 top-1 5-nn</td><td colspan=\"2\">CIFAR-100 top-1 5-nn</td><td colspan=\"2\">ImageNet-100 top-1 5-nn</td></tr><tr><td><PERSON>m<PERSON>R (Chen et al., 2020a)</td><td>90.74</td><td>85.13</td><td>65.78</td><td>53.19</td><td>77.64</td><td>65.78</td></tr><tr><td>MoCo V2 (Chen et al., 2020b)</td><td>92.94</td><td>88.95</td><td>69.89</td><td>58.09</td><td>79.28</td><td>70.46</td></tr><tr><td><PERSON><PERSON><PERSON> (<PERSON><PERSON> et al., 2020)</td><td>92.58</td><td>87.40</td><td>70.46</td><td>56.46</td><td>80.32</td><td>68.94</td></tr><tr><td>SwAV (Caron et al., 2020)</td><td>89.17</td><td>84.18</td><td>64.88</td><td>53.32</td><td>74.28</td><td>63.84</td></tr><tr><td>SimSiam (Chen &amp; He, 2021)</td><td>90.51</td><td>86.82</td><td>66.04</td><td>55.79</td><td>78.72</td><td>67.92</td></tr><tr><td>W-MSE (Ermolov et al., 2021)</td><td>88.67</td><td>84.95</td><td>61.33</td><td>49.65</td><td>69.06</td><td>58.44</td></tr><tr><td>Shuffled-DBN (Hua et al., 2021)</td><td>91.17</td><td>88.95</td><td>66.81</td><td>57.27</td><td>75.27</td><td>67.21</td></tr><tr><td>DINO (Caron et al., 2021)</td><td>89.52</td><td>86.13</td><td>66.76</td><td>56.24</td><td>74.92</td><td>64.30</td></tr><tr><td>Barlow Twins (Zbontar et al., 2021)</td><td>92.10</td><td>88.09</td><td>70.90</td><td>59.40</td><td>80.16</td><td>72.14</td></tr><tr><td>VICReg (Bardes et al., 2022)</td><td>92.07</td><td>87.38</td><td>68.54</td><td>56.32</td><td>79.40</td><td>71.94</td></tr><tr><td>Zero-CL (Zhang et al., 2022b)</td><td>90.81</td><td>87.51</td><td>70.33</td><td>59.21</td><td>79.26</td><td>71.18</td></tr><tr><td>CW-RGP (Weng et al., 2022)</td><td>92.03</td><td>89.67</td><td>67.78</td><td>58.24</td><td>76.96</td><td>68.46</td></tr><tr><td>INTL (ours)</td><td>92.60</td><td>90.03</td><td>70.88</td><td>61.90</td><td>81.68</td><td>73.46</td></tr></table>", "text": "Classification top-1 accuracy of a linear classifier and a 5-nearest neighbors classifier for different loss functions and datasets. The table is mostly inherited from solo-learn(<PERSON> et al., 2022). All methods are based on ResNet-18 with two augmented views generated from per sample and are trained for 1000-epoch on CIFAR-10/100 with a batch size of 256 and 400-epoch on ImageNet-100 with a batch size of 128.", "html": null}, "TABREF1": {"type_str": "table", "num": null, "content": "<table><tr><td>Method</td><td colspan=\"6\">Batch size EMA 100 eps 200 eps 400 eps 800 eps</td></tr><tr><td>SimCLR</td><td>4096</td><td>No</td><td>66.5</td><td>68.3</td><td>69.8</td><td>70.4</td></tr><tr><td>SwAV</td><td>4096 512</td><td>No No</td><td>66.5 65.8</td><td>69.1 67.9</td><td>70.7 -</td><td>71.8 -</td></tr><tr><td>SimSiam</td><td>256</td><td>No</td><td>68.1</td><td>70.0</td><td>70.8</td><td>71.3</td></tr><tr><td>W-MSE</td><td>512</td><td>No</td><td>65.1</td><td>66.4</td><td>-</td><td>-</td></tr><tr><td>Shuffled-DBN</td><td>512</td><td>No</td><td>65.2</td><td>-</td><td>-</td><td>-</td></tr><tr><td>Barlow Twins</td><td>2048</td><td>No</td><td>67.7</td><td>-</td><td>72.5</td><td>73.2</td></tr><tr><td>Zero-CL</td><td>1024</td><td>No</td><td>68.9</td><td>-</td><td>72.6</td><td>-</td></tr><tr><td>CW-RGP</td><td>512</td><td>No</td><td>67.1</td><td>69.6</td><td>-</td><td>-</td></tr><tr><td>INTL (ours)</td><td>512</td><td>No</td><td>69.5</td><td>71.1</td><td>72.4</td><td>73.1</td></tr><tr><td>MoCo v2</td><td>256</td><td>Yes</td><td>67.4</td><td>69.9</td><td>71.0</td><td>72.2</td></tr><tr><td>BYOL</td><td>4096</td><td>Yes</td><td>66.5</td><td>70.6</td><td>73.2</td><td>74.3</td></tr><tr><td>INTL (ours)</td><td>256</td><td>Yes</td><td>69.2</td><td>71.5</td><td>73.7</td><td>74.3</td></tr></table>", "text": "Comparisons on ImageNet linear classification with various training epochs. All methods are based on ResNet-50 backbone with two augmented views generated from per sample. EMA represents Exponential Moving Average. Given that one of the objectives of SSL methods is to achieve high performance with small batch sizes, it's worth noting that our INTL performs effectively when trained with small batch sizes, such as 256 and 512.", "html": null}, "TABREF2": {"type_str": "table", "num": null, "content": "<table><tr><td>Method</td><td>AP50</td><td>COCO detection AP</td><td>AP75</td><td>AP50</td><td>COCO instance seg. AP</td><td>AP75</td></tr><tr><td>Scratch</td><td>44.0</td><td>26.4</td><td>27.8</td><td>46.9</td><td>29.3</td><td>30.8</td></tr><tr><td>Supervised</td><td>58.2</td><td>38.2</td><td>41.2</td><td>54.7</td><td>33.3</td><td>35.2</td></tr><tr><td>SimCLR</td><td>57.7</td><td>37.9</td><td>40.9</td><td>54.6</td><td>33.3</td><td>35.3</td></tr><tr><td>MoCo v2</td><td>58.8</td><td>39.2</td><td>42.5</td><td>55.5</td><td>34.3</td><td>36.6</td></tr><tr><td>BYOL</td><td>57.8</td><td>37.9</td><td>40.9</td><td>54.3</td><td>33.2</td><td>35.0</td></tr><tr><td>SwAV</td><td>57.6</td><td>37.6</td><td>40.3</td><td>54.2</td><td>33.1</td><td>35.1</td></tr><tr><td>SimSiam</td><td>57.5</td><td>37.9</td><td>40.9</td><td>54.2</td><td>33.2</td><td>35.2</td></tr><tr><td>W-MSE (repro.)</td><td>60.1</td><td>39.2</td><td>42.8</td><td>56.8</td><td>34.8</td><td>36.7</td></tr><tr><td>Barlow Twins</td><td>59.0</td><td>39.2</td><td>42.5</td><td>56.0</td><td>34.3</td><td>36.5</td></tr><tr><td>INTL (ours)</td><td colspan=\"6\">60.9±0.08 40.7±0.09 43.7±0.17 57.3±0.08 35.4±0.05 37.6±0.14</td></tr><tr><td colspan=\"3\">5.1 EVALUATION FOR CLASSIFICATION</td><td/><td/><td/><td/></tr></table>", "text": "Transfer Learning. All competitive unsupervised methods are based on 200-epoch pretraining on ImageNet (IN). The table are mostly inherited from(<PERSON>, 2021). Our INTL is performed with 3 random seeds, with mean and standard deviation reported. Evaluation on small and medium size datasets. We initially train and perform linear evaluation of INTL using ResNet-18 as the backbone on CIFAR-10/100(<PERSON><PERSON><PERSON><PERSON>, 2009) and ImageNet-100(<PERSON><PERSON> et al., 2020a). We strictly adhere to the experimental settings outlined in solo-learn(<PERSON> et al., 2022) for these datasets. As depicted in Table1, INTL achieves remarkable results, with a top-1 accuracy of 92.60% on CIFAR-10, 70.88% on CIFAR-100, and 81.68% on ImageNet-100. These results are on par with or even surpass the state-of-the-art methods as reproduced by solo-learn. Furthermore, when employing a 5-nearest neighbors classifier, INTL outperforms other baselines by a significant margin, underscoring its capacity to learn superior representations. Evaluation on ImageNet. To further assess the versatility of INTL, we train it using a ResNet-50 backbone and evaluate its performance using the standard linear evaluation protocol on ImageNet.", "html": null}, "TABREF3": {"type_str": "table", "num": null, "content": "<table><tr><td>Parameter</td><td>T1</td><td>T2</td></tr><tr><td>crop size</td><td colspan=\"2\">224 × 224 224 × 224</td></tr><tr><td>maximum scale of crops</td><td>1.0</td><td>1.0</td></tr><tr><td>minimum scale of crops</td><td>0.08</td><td>0.08</td></tr><tr><td>brightness</td><td>0.4</td><td>0.4</td></tr><tr><td>contrast</td><td>0.4</td><td>0.4</td></tr><tr><td>saturation</td><td>0.2</td><td>0.2</td></tr><tr><td>hue</td><td>0.1</td><td>0.1</td></tr><tr><td>color jitter prob</td><td>0.8</td><td>0.8</td></tr><tr><td>horizontal flip prob</td><td>0.5</td><td>0.5</td></tr><tr><td>gaussian prob</td><td>1.0</td><td>0.1</td></tr><tr><td>solarization prob</td><td>0.0</td><td>0.2</td></tr><tr><td>(b)</td><td/><td/></tr></table>", "text": "Parameters used for image augmentations on ImageNet and ImageNet-100. Training is prone to crashing when the embedding dimension equals the batch size (d = 512).", "html": null}, "TABREF4": {"type_str": "table", "num": null, "content": "<table><tr><td>Parameter</td><td>Value</td></tr><tr><td>max epoch</td><td>1000</td></tr><tr><td>backbone</td><td>ResNet-18</td></tr><tr><td>projection layers</td><td>3</td></tr><tr><td>projection hidden dimension</td><td>2048</td></tr><tr><td>projection output dimension</td><td>2048</td></tr><tr><td>optimizer</td><td>SGD</td></tr><tr><td>SGD momentum</td><td>0.9</td></tr><tr><td>learning rate</td><td>0.3</td></tr><tr><td>learning rate warm-up</td><td>2 epochs</td></tr><tr><td>learning rate schedule</td><td>cosine decay</td></tr><tr><td>weight decay</td><td>1e-4</td></tr><tr><td>batch size</td><td>256</td></tr><tr><td>E.3</td><td/></tr></table>", "text": "Parameters used for INTL pre-training on CIFAR-10/100. <PERSON><PERSON><PERSON><PERSON><PERSON>ENTS FOR SMALL AND MEDIUM SIZE DATASETS In section 5.1 of the paper, we provide the classification results of INTL pre-training on small and medium size datasets such as CIFAR-10, CIFAR-100 and ImageNet-100. Here, We describe the details of implementation and training protocol for the experiments on these datasets as follows. For fairness, most of hyper-parameters we used such as batch size, projection settings, data augmentation and so on are consistent with solo-learn (<PERSON> et al., 2022). Experimental setup on ImageNet-100. Details of implementation and training protocol for INTL pre-training on ImageNet-100 are shown in Table 6. The image transformation and evaluation protocol are the same as ones on ImageNet. Experimental setup on CIFAR-10/100. Then Details of implementation and training protocol for INTL pre-training on CIFAR-10/100 are shown in Table", "html": null}, "TABREF6": {"type_str": "table", "num": null, "content": "<table><tr><td>Method</td><td colspan=\"2\">CIFAR-10 top-1 5-nn</td><td colspan=\"2\">CIFAR-100 top-1 5-nn</td><td colspan=\"2\">ImageNet-100 top-1 5-nn</td></tr><tr><td><PERSON></td><td>80.43</td><td>76.68</td><td>51.60</td><td>42.71</td><td>58.34</td><td>50.21</td></tr><tr><td><PERSON> + trace loss</td><td>80.45</td><td>76.32</td><td>51.66</td><td>43.94</td><td>59.78</td><td>50.45</td></tr><tr><td>VICReg</td><td>83.14</td><td>79.62</td><td>55.96</td><td>46.71</td><td>66.01</td><td>57.76</td></tr><tr><td>VICReg + trace loss</td><td>81.67</td><td>78.74</td><td>54.75</td><td>46.24</td><td>63.54</td><td>55.18</td></tr><tr><td>methods.</td><td/><td/><td/><td/><td/><td/></tr></table>", "text": "Evaluate the performance of adding trace loss to BarlowTwins/VICReg. It can either disrupt the existing balance, leading to reduced performance, or achieve a more favorable balance, resulting in improved performance.", "html": null}}}}