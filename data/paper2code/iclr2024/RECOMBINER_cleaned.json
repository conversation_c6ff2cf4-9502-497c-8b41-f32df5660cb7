{"paper_id": "RECOMBINER", "title": "RECOMBINER: ROBUST AND <PERSON><PERSON><PERSON><PERSON>ED COMPRESSION WITH BAYESIAN IMPLICIT NEURAL REPRESENTATIONS", "abstract": "COMpression with Bayesian Implicit NEural Representations (COMBINER) is a recent data compression method that addresses a key inefficiency of previous Implicit Neural Representation (INR)-based approaches: it avoids quantization and enables direct optimization of the rate-distortion performance. However, COMBINER still has significant limitations: 1) it uses factorized priors and posterior approximations that lack flexibility; 2) it cannot effectively adapt to local deviations from global patterns in the data; and 3) its performance can be susceptible to modeling choices and the variational parameters' initializations. Our proposed method, Robust and Enhanced COMBINER (RECOMBINER), addresses these issues by 1) enriching the variational approximation while retaining a low computational cost via a linear reparameterization of the INR weights, 2) augmenting our INRs with learnable positional encodings that enable them to adapt to local details and 3) splitting high-resolution data into patches to increase robustness and utilizing expressive hierarchical priors to capture dependency across patches. We conduct extensive experiments across several data modalities, showcasing that RECOMBINER achieves competitive results with the best INR-based methods and even outperforms autoencoder-based codecs on lowresolution images at low bitrates. Our PyTorch implementation is available at https://github.com/cambridge-mlg/RECOMBINER/.\n˚equal contribution.\nThis section reviews the essential parts of <PERSON> et al. ( 2023)'s compression with Bayesian implicit neural representations (COMBINER), as it provides the basis for our method.\nVariational Bayesian Implicit Neural Representations: We assume the data we wish to compress can be represented as a continuous function f : R I Ñ R O from I-dimensional coordinates to Odimensional signal values. Then, our goal is to approximate f with a small neural network gp¨| wq", "pdf_parse": {"paper_id": "RECOMBINER", "abstract": [{"text": "COMpression with Bayesian Implicit NEural Representations (COMBINER) is a recent data compression method that addresses a key inefficiency of previous Implicit Neural Representation (INR)-based approaches: it avoids quantization and enables direct optimization of the rate-distortion performance. However, COMBINER still has significant limitations: 1) it uses factorized priors and posterior approximations that lack flexibility; 2) it cannot effectively adapt to local deviations from global patterns in the data; and 3) its performance can be susceptible to modeling choices and the variational parameters' initializations. Our proposed method, Robust and Enhanced COMBINER (RECOMBINER), addresses these issues by 1) enriching the variational approximation while retaining a low computational cost via a linear reparameterization of the INR weights, 2) augmenting our INRs with learnable positional encodings that enable them to adapt to local details and 3) splitting high-resolution data into patches to increase robustness and utilizing expressive hierarchical priors to capture dependency across patches. We conduct extensive experiments across several data modalities, showcasing that RECOMBINER achieves competitive results with the best INR-based methods and even outperforms autoencoder-based codecs on lowresolution images at low bitrates. Our PyTorch implementation is available at https://github.com/cambridge-mlg/RECOMBINER/.", "section": "Abstract", "sec_num": null}, {"text": "˚equal contribution.", "section": "Abstract", "sec_num": null}, {"text": "This section reviews the essential parts of <PERSON> et al. ( 2023)'s compression with Bayesian implicit neural representations (COMBINER), as it provides the basis for our method.", "section": "Abstract", "sec_num": null}, {"text": "Variational Bayesian Implicit Neural Representations: We assume the data we wish to compress can be represented as a continuous function f : R I Ñ R O from I-dimensional coordinates to Odimensional signal values. Then, our goal is to approximate f with a small neural network gp¨| wq", "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Advances in deep learning recently enabled a new data compression technique impossible with classical approaches: we train a neural network to memorize the data (<PERSON>, 2007) and then encode the network's weights instead. These networks are called the implicit neural representation (INR) of the data, and differ from neural networks used elsewhere in three significant ways. First, they treat data as a signal that maps from coordinates to values, such as mapping pX, Y q pixel coordinates to pR, G, Bq color triplets in the case of an image. Second, their architecture consists of many fewer layers and units than usual and tends to utilize SIREN activations (<PERSON><PERSON><PERSON> et al., 2020) . Third, we aim to overfit them to the data as much as possible.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Unfortunately, most INR-based data compression methods cannot directly and jointly optimize ratedistortion, which results in a wasteful allocation of bits leading to suboptimal coding performance. COMpression with Bayesian Implicit NEural Representations (COMBINER; <PERSON> et al., 2023) addresses this issue by picking a variational Gaussian mean-field Bayesian neural network (<PERSON><PERSON><PERSON> et al., 2015) as the INR of the data. This choice enables joint rate-distortion optimization via maximizing the INR's β-evidence lower bound (β-ELBO), where β controls the rate-distortion trade-off. Finally, the authors encode a weight sample from the INR's variational weight posterior to represent the data using relative entropy coding (REC; <PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2020) .", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Although COMBINER performs strongly among INR-based approaches, it falls short of the stateof-the-art codecs on well-established data modalities both in terms of performance and robustness. In this paper, we identify several issues that lead to this discrepancy: 1) COMBINER employs a fully-factorized Gaussian variational posterior over the INR weights, which tends to underfit the data (<PERSON><PERSON><PERSON> et al., 2020) , going directly against our goal of overfitting; 2) Overfitting small INRs used by COMBINER is challenging, especially at low bitrates: a small change to any weight can significantly affect the reconstruction at every coordinate, hence optimization by stochastic gradient descent becomes unstable and yields suboptimal results. 3) Overfitting becomes more problematic on high-resolution signals. As highlighted by <PERSON> et al. (2023) , the method is sensitive to model choices and the variational parameters' initialization and requires considerable effort to tune.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We tackle these problems by proposing several non-trivial extensions to COMBINER, which significantly improve the rate-distortion performance and robustness to modeling choices. Hence, we dub our method robust and enhanced COMBINER (RECOMBINER). Concretely, our contributions are:", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• We propose a simple yet effective learned reparameterization for neural network weights specifically tailored for INR-based compression, yielding more expressive variational posteriors while matching the computational cost of standard mean-field variational inference. • We augment our INR with learnable positional encodings whose parameters only have a local influence on the reconstructed signal, thus allowing deviations from the global patterns captured by the network weights, facilitating overfitting the INR with gradient descent. • We split high-resolution data into patches to improve robustness to modeling choices and the variational parameters' initialization. Moreover, we propose an expressive hierarchical Bayesian model to capture the dependencies across patches to enhance performance. • We conduct extensive experiments to verify the effectiveness of our proposed extensions across several data modalities, including image, audio, video and protein structure data.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In particular, we show that RECOMBINER achieves better rate-distortion performance than VAE-based approaches on low-resolution images at low bitrates.", "section": "INTRODUCTION", "sec_num": "1"}, {"text": "with weights w. Given L hidden layers in the network, we write w \" rw r1s , . . . , w rLs s, which represents the concatenation of the L weight matrices w r1s , . . . w rLs , each flattened into a rowvector. <PERSON> et al. (2023) propose using variational Bayesian neural networks (BNN; <PERSON><PERSON> et al., 2015) that place a prior p w and a variational posterior q w on the weights. Furthermore, they use Fourier embeddings γpxq for the input data (<PERSON><PERSON><PERSON> et al., 2020) and sine activations at the hidden layers (<PERSON><PERSON><PERSON> et al., 2020) . To infer the implicit neural representation (INR) for some data D, we treat D as a dataset of coordinate-value pairs tpx i , y i qu D i\"1 , e.g. for an image, x i can be an pX, Y q pixel coordinate and y i the corresponding pR, G, Bq triplet. Next, we pick a distortion metric ∆ (e.g., mean squared error) and a trade-off parameter β to define the β-rate-distortion objective:", "section": "BACKGROUND", "sec_num": "2"}, {"text": "EQUATION", "section": "BACKGROUND", "sec_num": "2"}, {"text": "where D KL rq w }p w s denotes the Kullback-Le<PERSON> divergence of q w from p w , and as we explain below, it represents the compression rate of a single weight sample w \" q w . Note that Equation (1) corresponds to a negative β-evidence lower bound under mild assumptions on ∆.", "section": "BACKGROUND", "sec_num": "2"}, {"text": "We infer the optimal posterior by computing q ẘ \" arg min qwPQ LpD, q w , p w , βq over an appropriate variational family Q. <PERSON> et al. (2023) set Q to be the family of factorized Gaussian distributions.", "section": "BACKGROUND", "sec_num": "2"}, {"text": "Training COMBINER: Once we selected a network architecture g for our INRs, a crucial element of COMBINER is to select a good prior on the weights p w . Given a training set tD 1 , . . . , D M u and an initial guess for p w , <PERSON> et al. (2023) propose the following iterative scheme to select the optimal prior: 1) Fix p w and infer the variational INR posteriors q ẘ,m for each datum D m by minimizng Equation (1); 2) Fix the q ẘ,m s and update the prior parameters p w based on the parameters of the posteriors. When the q w are Gaussian, <PERSON> et al. (2023) derive analytic formulae for updating the prior parameters. To avoid overloading the notion of training, we refer to learning p w and the other model parameters as training, and to learning q w as inferring the INR.", "section": "BACKGROUND", "sec_num": "2"}, {"text": "Compressing data with COMBINER: Once we picked the INR architecture g and found the optimal prior p w , we can use COMBINER to compress new data D in two steps: 1) We first infer the variational INR posterior q w for D by optimizing Equation (1), after which 2) we encode an approximate sample from q w using relative entropy coding (REC), whose expected coding cost is approximately D KL rq w }p w s (<PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2020) . Following <PERSON> et al. (2023) , we used depth-limited global-bound A ˚coding (<PERSON><PERSON><PERSON> et al., 2022) , to which we will refer as just A ˚coding. Unfortunately, applying A ˚coding to encode a sample from q w is infeasible in practice, as the time complexity of the algorithm grows as ΩpexppD KL rq w }p w sqq. Hence, <PERSON> et al. (2023) suggest breaking up the problem into smaller ones. First, they draw a uniformly random permutation α on dimpwq elements, and use it to permute the dimensions of w as αpwq \" rw αp1q , . . . , w αpdimpwqq s. Then, they partition αpwq into smaller blocks, and compress the blocks sequentially. Permuting the weight vector ensures that the KL divergences are spread approximately evenly across the blocks.", "section": "BACKGROUND", "sec_num": "2"}, {"text": "As an additional technical note, between compressing each block, we run a few steps of finetuning the posterior of the weights that are yet to be compressed, see <PERSON> et al. (2023) for more details.", "section": "BACKGROUND", "sec_num": "2"}, {"text": "In this section, we propose several extensions to <PERSON> et al. (2023) 's framework that significantly improve its robustness and performance: 1) we introduce a linear reparemeterization for the INR's weights which yields a richer variational posterior family; 2) we augment the INR's input with learned positional encodings to capture local features in the data and to assist overfitting; 3) we scale our method to high-resolution image compression by dividing the images into patches and introducing an expressive hierarchical Bayesian model over the patch-INRs, and 4) we introduce minor modifications to the training procedure and adaptively select β to achieve the desired coding budget. Contributions 1) and 2) are depicted in Figure 1 , while 3) is shown in Figure 2 .", "section": "METHODS", "sec_num": "3"}, {"text": "A significant limitation of the factorized Gaussian variational posterior used by COMBINER is that it posits dimension-wise independent weights. This assumption is known to be unrealistic (<PERSON><PERSON><PERSON><PERSON> et al., 2021) and to underfit the data (<PERSON><PERSON><PERSON> et al., 2020) , which goes directly against our goal of overfitting the data. On the other hand, using a full-covariance Gaussian posterior approximation would increase the INR's training and coding time significantly, even for small network architectures.", "section": "LINEAR REPARAM<PERSON><PERSON>IZATION FOR THE NETWORK PARAMETERS", "sec_num": "3.1"}, {"text": "Hence, we propose a solution that lies in-between: at a high level, we learn a linearly-transformed factorized Gaussian approximation that closely matches the full-covariance Gaussian posterior on average over the training data. Formally, for each layer l \" 1, . . . , L, we model the weights as w rls \" h rls w A rls , where the A rls are square matrices, and we place a factorized Gaussian prior and variational posterior on h rls w instead. We learn each A rls during the training stage, after which we fix them and only infer factorized posteriors q h rls w when compressing new data. To simplify notation, we collect the A rls in a block-diagonal matrix A \" diagpA r1s , . . . , A rLs q and the h rls w in a single row-vector h w \" rh r1s w , . . . , h rLs w s, so that now the weights are given by w \" h w A. We found this layer-wise weight reparameterization as efficient as using a joint one for the entire weight vector w. Hence, we use the layer-wise approach, as it is more parameter and compute-efficient.", "section": "LINEAR REPARAM<PERSON><PERSON>IZATION FOR THE NETWORK PARAMETERS", "sec_num": "3.1"}, {"text": "This simple yet expressive variational approximation has a couple of advantages. First, it provides an expressive full-covariance prior and posterior while requiring much less training and coding time. Specifically, the KL divergence required by Equation ( 1) is still between factorized Gaussians and we do not need to optimize the full covariance matrices of the posteriors during coding. Second, this parameterization has scale redundancy: for any c P R we have h w A \" p 1 {c ¨hw qpc ¨Aq. Hence, if we initialize h w suboptimally during training, A can still learn to compensate for it, making our method more robust. Finally, note that this reparameterization is specifically tailored for INR-based compression and would usually not be feasible in other BNN use-cases, since we learn A while inferring multiple variational posteriors simultaneously.", "section": "LINEAR REPARAM<PERSON><PERSON>IZATION FOR THE NETWORK PARAMETERS", "sec_num": "3.1"}, {"text": "A challenge for overfitting INRs, especially at low bitrates is their global representation of the data, in the sense that each of their weights influences the reconstruction at every coordinate. To mitigate this issue, we extend our INRs to take a learned positional input z i at each coordinate x i : gpx i , z i | wq.", "section": "LEARNED POSITIONAL ENCODINGS", "sec_num": "3.2"}, {"text": "However, it is usually wasteful to introduce a vector for each coordinate in practice. Instead, we use a lower-dimensional row-vector representation h z , that we reshape and upsample with a learnable function ϕ. In the case of a W ˆH image with F -dimensional positional encodings, we could pick h z such that dimph z q ! F ¨W ¨H, then reshape and upsample it to be F ˆW ˆH by picking ϕ to be some small convolutional network. Then, we set z i \" ϕph z q xi to be the positional encoding at location x i . We placed a factorized Gaussian prior and variational posterior on h z . Hereafter, we refer to h z as the latent positional encodings, ϕph z q and z i as the upsampled positional encodings.", "section": "LEARNED POSITIONAL ENCODINGS", "sec_num": "3.2"}, {"text": "With considerable effort, <PERSON> et al. (2023) successfully scaled COMBINER to high-resolution images by significantly increasing the number of INR parameters. However, they note that the training procedure was very sensitive to hyperparameters, including the initialization of variational parameters and model size selection. Unfortunately, improving the robustness of large INRs using the weight reparameterization we describe in Section 3.1 is also impractical, because the size of the transformation matrix A grows quadratically in the number of weights. Therefore, we split high-resolution data into patches and infer a separate small INR for each patch, in line with other INR-based works as well (<PERSON><PERSON> et al., 2022; <PERSON><PERSON> & <PERSON>, 2022; <PERSON><PERSON><PERSON> et al., 2023) . However, the patches' INRs are independent by default, hence we re-introduce information sharing between the patch-INRs' weights via a hierarchical model for h w . Finally, we take advantage of the patch structure to parallelize data compression and reduce the encoding time in RECOMBINER, as discussed at the end of this section.", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "RECOMBINER's hierarchical Bayesian model: We posit a global representation for the weights h w , from which each patch-INR can deviate. Thus, assuming that the data D is split into P patches, for each patch π P 1, . . . , P , we need to define the conditional distributions of patch representations h pπq w | h w . However, since we wish to model deviations from the global representation, it is natural to decompose the patch representation as h representation and the deviations, given by the following product of P `1 Gaussian measures:", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "p hw,∆h p1:P q w \" N pµ w , diagpσ w qq ˆP ź π\"1 N pµ pπq ∆ , diagpσ pπq ∆ qq (2)", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "q hw,∆h p1:P q w \" N pν w , diagpρ w qq ˆP ź", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "π\"1", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "EQUATION", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "where 1 : P is the slice notation, i.e. ∆h p1:P q w \" ∆h p1q w , . . . , ∆h pP q w . Importantly, while the posterior approximation in Equation (3) assumes that the global representation and the differences are independent, h w and h pπq w remain correlated. Note that optimizing Equation (1) requires us to compute D KL rq h p1:P q w }p h p1:P q w s. Unfortunately, due to the complex dependence between the h pπq w s, this calculation is infeasible. Instead, we can minimize an upper bound to it by observing that D KL rq h p1:P q w }p h p1:P q w s ď D KL rq h p1:P q w }p h p1:P q w s `DKL rq hw|h p1:P q w }p hw|h p1:P q w s \" D KL rq hw,h p1:P q w }p hw,h p1:P q w s \" D KL rq hw,∆h p1:P q w }p hw,∆h p1:P q w s.", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "(4)", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "Hence, when training the patch-INRs, we replace the KL term in Equation ( 1) with the divergence in Equation ( 4), which is between factorized Gaussian distributions and cheap to compute. Finally, we remark that we can view h w as side information also prevalent in other neural compression codecs (<PERSON> et al., 2018) , or auxiliary latent variables enabling factorization (<PERSON><PERSON> & Friedman, 2009) .", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "While Equations ( 2) and ( 3) describe a two-level hierarchical model, we can easily extend the hierarchical structure by breaking up patches further into sub-patches and adding extra levels to the probabilistic model. For our experiments on high-resolution audio, images, and video, we found that a three-level hierarchical model worked best, with global weight representation hw, second/grouplevel representations h p1:Gq w and third/patch-level representations h p1:P q w , illustrated in Figure 2a . Empirically, a hierarchical model for h z did not yield significant gains, thus we only use it for h w .", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "Compressing high-resolution data with RECOMBINER: An advantage of patching is that we can compress and fine-tune INRs and latent positional encodings of all patches in parallel. Unfortunately, compressing P patches in parallel using COMBINER's procedure is suboptimal, since the information content between patches might vary significantly. However, by carefully permuting the weights across the patches' representations we can 1) adaptively allocate bits to each patch to compensate for the differences in their information content and 2) enforce the same coding budget across each parallel thread to ensure consistent coding times. Concretely, we stack representations of each patch in a matrix at each level of the hierarchical model. For example, in our three-level model we set", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "H p0q π,: \" rh pπq w , h pπq z s, H p1q g,: \" h pgq w , H p2q \" h w ,", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "(5) where we use slice notation to denote the ith row as H i,: and the jth column as H :,j . Furthermore, let S n denote the set of permutations on n elements. Now, at each level ℓ, assume H pℓq has C ℓ columns and R ℓ rows. We sample a single within-row permutation κ uniformly from S C ℓ and for each column of H pℓq we sample an across-rows permutation α j uniformly from S R ℓ elements. Then, we permute H pℓq as Ă H pℓq i,j \" H pℓq αj piq,κpjq . Finally, we split the H pℓq s into blocks row-wise, and encode and fine-tune each row in parallel. We illustrate the above procedure in Figure 2b .", "section": "SCALING TO HIGH-RESOLUTION DATA WITH PATCHES", "sec_num": "3.3"}, {"text": "In this section, we describe the ways in which RECOMBINER's training procedure deviates from COMBINER's. To begin, we collect the RECOMBINER's representations into one vector. For nonpatching cases we set h \" rh w , h z s, and for the patch case using the three-level hierarchical model we set h \" vecprH p0q , H p1q , H p2q sq. For simplicity, we denote the factorized Gaussian prior and variational posterior over h as p h \" N pµ, diagpσqq and q h \" N pν, diagpρqq, where µ and ν are the means and σ and ρ are the diagonals of covariances of the prior and the posterior, respectively. In this paper, we adopt an explicit approach and tune β dynamically based on our desired coding budget of C bits. More precisely, after every iteration, we calculate the average KL divergence of the training examples, i.e., δ \"1 ", "section": "EXTENDED TRAINING PROCEDURE", "sec_num": "3.4"}, {"text": "M ř M m\"1 D KL rq h,m ||p h s. If δ ą C, we update β by β Ð β ˆp1 `τC q; if δ ă C ´ϵC , we update β by β Ð β{p1 `τC q.", "section": "EXTENDED TRAINING PROCEDURE", "sec_num": "3.4"}, {"text": "Here ϵ C is a threshold parameter to stabilize the training process and prevent overly frequent updates to β, and τ C is the adjustment step size. Unless otherwise stated, we set τ C \" 0.5 in our experiments. Empirically, we find the value of β stabilizes after 30 to 50 iterations. We present the pseudocode of this prior learning algorithm in Algorithm 1. Then, our training step is a three-step coordinate descent process analogous to <PERSON> et al. (2023) 's:", "section": "EXTENDED TRAINING PROCEDURE", "sec_num": "3.4"}, {"text": "1. Optimize variational parameters, linear transformation and upsampling network: Fix the prior p h , and optimize Equation ( 1) or its modified version from Section 3.3 via gradient descent. Note, that L is a function of the linear transform A and upsampling network parameters ϕ too:", "section": "EXTENDED TRAINING PROCEDURE", "sec_num": "3.4"}, {"text": "EQUATION", "section": "EXTENDED TRAINING PROCEDURE", "sec_num": "3.4"}, {"text": "2. Update prior: Update the prior parameters by the closed-form solution:", "section": "EXTENDED TRAINING PROCEDURE", "sec_num": "3.4"}, {"text": "EQUATION", "section": "EXTENDED TRAINING PROCEDURE", "sec_num": "3.4"}, {"text": "3. Update β: Set β Ð β ˆp1 `τC q or β Ð β{p1 `τC q based on the procedure described above.", "section": "EXTENDED TRAINING PROCEDURE", "sec_num": "3.4"}, {"text": "Note that unlike other INR-based methods (<PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> & <PERSON>, 2022; <PERSON><PERSON><PERSON> et al., 2023) our training procedure is remarkably stable, as we illustrate in Appendix D.4.", "section": "EXTENDED TRAINING PROCEDURE", "sec_num": "3.4"}, {"text": "Nonlinear transform coding: Currently, the dominant paradigm in neural compression is nonlinear transform coding (NTC; <PERSON><PERSON> et al., 2020) usually implemented using variational autoencoders (VAE). NTC has achieved impressive performance in terms of both objective metrics (<PERSON> et al., 2020; <PERSON> et al., 2022) and perceptual quality (<PERSON><PERSON><PERSON> et al., 2020) , mainly due to their expressive learned non-linear transforms (<PERSON><PERSON> et al., 2020; <PERSON> et al., 2021; <PERSON> et al., 2023) and elaborate entropy models (<PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2018; <PERSON> et al., 2021) .", "section": "RELATED WORKS", "sec_num": "4"}, {"text": "Compressing INRs can also be viewed as a form of NTC: we use gradent descent to transform data into an INR. The idea to quantize INR weights and entropy code them was first proposed by <PERSON><PERSON> et al. (2021) , whose method has since been extended significantly (<PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> & <PERSON>, 2022; <PERSON><PERSON><PERSON> et al., 2023) . The current state-of-the-art INR-based method, VC-INR (<PERSON><PERSON><PERSON> et al., 2023) , achieves impressive results across several data modalities, albeit at the cost of significantly higher complexity and still falling short of autoencoder-based NTC methods on images. Our method, following COMBINER (<PERSON> et al., 2023) , differs from all of the above methods, as it uses REC to encode our variational INRs, instead of quantization and entropy coding.", "section": "RELATED WORKS", "sec_num": "4"}, {"text": "Linear weight reparameterization: Similar to our proposal in Section 3.1, <PERSON><PERSON><PERSON> et al. (2019) learn an affine reparameterization of the weights of large neural networks. They demonstrate that scalar quantization in the transformed space leads to significant gains in compression performance. However, since they are performing one-shot model compression, their linear transformations have (a) RD curve on CIFAR-10 (left) and <PERSON><PERSON> (right) . We also provide full-resolution plots in Appendix F. very few parameters as they need to transmit them alongside the quantized weights, limiting their expressivity. On the other hand, RECOMBINER learns the linear transform during training after which it is fixed and shared between communicating parties, thus it does not cause any communication overhead. Therefore, our linear transformation can be significantly more expressive.", "section": "RELATED WORKS", "sec_num": "4"}, {"text": "Positional encodings: Some recent works have demonstrated that learning positional features is beneficial for fitting INRs (<PERSON> et al., 2020; <PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2023) . Sharing a similar motivation, our method essentially incorporates implicit representations with explicit ones, forming a hybrid INR framework (<PERSON> et al., 2023).", "section": "RELATED WORKS", "sec_num": "4"}, {"text": "In this section, we evaluate RECOMBINER on image, audio, video, and 3D protein structure data and demonstrate that it achieves strong performance across all modalities. We also perform extensive ablation studies on the CIFAR-10 and Kodak datasets which demonstrate RECOMBINER's robustness and the effectiveness of each of our proposed solutions. For all experiments, we use a 4-layer, 32hidden unit SIREN network (<PERSON><PERSON><PERSON> et al., 2020) as the INR architecture unless otherwise stated, and a small 3-layer convolution network as the upsampling network ϕ, as shown in Figure 6 in the appendix. See Appendix C for the detailed description of our experimental setup. Audio: Following the experimental set-up of <PERSON> et al. (2023) , we evaluate our method on the LibriSpeech (Panayotov et al., 2015) dataset. In Figure 3b Video: We evaluate RECOMBINER on UCF-101 action recognition dataset (<PERSON><PERSON><PERSON> et al., 2012) , following <PERSON><PERSON><PERSON> et al. (2023) 's experimental setup. However, as they do not report their traintest split and due to the time-consuming encoding process of our approach, we only benchmark our method against H.264 and H.265 on 16 randomly selected video clips. Figure 3c shows RECOM-BINER achieves comparable performance to the classic domain-specific codecs H.264 and H.265, especially at lower bitrates. However, there is still a gap between our approach and H.264 and H.265 when they are configured to prioritize quality. Figure 3e shows a non-cherry-picked video compressed with RECOMBINER at two different bitrates and its reconstruction errors.", "section": "EXPERIMENTAL RESULTS", "sec_num": "5"}, {"text": "3D Protein Structure: To further illustrate the applicability of our approach, we use it to compress the 3D coordinates of Cα atoms in protein fragments. We take domain-specific lossy codecs as baselines, including Foldcomp (<PERSON> et al., 2023) , PDC (Zhang & Pyle, 2023) and PIC (Staniscia & Yu, 2023) . Surprisingly, as shown in Figure 3d , RECOMBINER's performance is competitive with highly domain-specific codecs. Furthermore, it allows us to tune its rate-distortion performance, whereas the baselines only support a certain compression rate. Since the experimental resolution of 3D structures is typically between 1-3 Å (RCSB Protein Data Bank, 2000), RECOMBINER could help with reducing the increasing storage demand for protein structures without losing key information.", "section": "EXPERIMENTAL RESULTS", "sec_num": "5"}, {"text": "Figure 3f shows non-cherry-picked examples compressed with our method.", "section": "EXPERIMENTAL RESULTS", "sec_num": "5"}, {"text": "This section showcases RECOMBINER's robustness to model size and the effectiveness of each component. Appendix D.1 provides additional visualizations for a deeper understanding of our methods.", "section": "EFFECTIVENESS OF OUR SOLUTIONS, AB<PERSON><PERSON><PERSON> STUDIES AND RUNTIME ANALYSIS", "sec_num": "5.2"}, {"text": "Positional RECOMBINER is more robust to model size: Using the same INR architecture, Figure 5a shows COMBINER and RECOMBINER's RD curves as we vary the number of hidden units. RECOMBINER displays minimal performance variation and also consistently outperforms COMBINER. Based on Figure 7 in Appendix D, this phenomenon is likely due to RECOMBINER's linear weight reparameterization allowing it to more flexibly prune its weight representations.", "section": "EFFECTIVENESS OF OUR SOLUTIONS, AB<PERSON><PERSON><PERSON> STUDIES AND RUNTIME ANALYSIS", "sec_num": "5.2"}, {"text": "Ablation study: In Figures 5b and 5c , we ablate our linear reparameterization, positional encodings, hierarchical model, and permutation strategy on CIFAR-10 and Kodak, with five key takeaways:", "section": "EFFECTIVENESS OF OUR SOLUTIONS, AB<PERSON><PERSON><PERSON> STUDIES AND RUNTIME ANALYSIS", "sec_num": "5.2"}, {"text": "1. Linear weight reparameterization consistently improves performance on both datasets, yielding up to 4dB gain on CIFAR-10 at high bitrates and over 0.5 dB gain on Kodak in PSNR.", "section": "EFFECTIVENESS OF OUR SOLUTIONS, AB<PERSON><PERSON><PERSON> STUDIES AND RUNTIME ANALYSIS", "sec_num": "5.2"}, {"text": "2. Learnable positional encodings provide more substantial advantages at lower bitrates. On CIFAR-10, the encodings contribute up to 0.5 dB gain when the bitrate falls below 2 bpp. On Kodak, the encodings provide noteworthy gains of 2 dB at low bitrates and 1 dB at high bitrates.", "section": "EFFECTIVENESS OF OUR SOLUTIONS, AB<PERSON><PERSON><PERSON> STUDIES AND RUNTIME ANALYSIS", "sec_num": "5.2"}, {"text": "3. Surprisingly, the hierarchical model without positional encodings can degrade performance. We hypothesize that this is because directly applying the hierarchical model poses challenges in optimizing Equation (1). A potential solution is to warm up the rate penalty β level by level akin to what is done in hierarchical VAEs (<PERSON><PERSON><PERSON><PERSON> et al., 2016), which we leave for further work.", "section": "EFFECTIVENESS OF OUR SOLUTIONS, AB<PERSON><PERSON><PERSON> STUDIES AND RUNTIME ANALYSIS", "sec_num": "5.2"}, {"text": "4. However, positional encodings appear to consistently alleviate this optimization difficulty, yielding 0.5 dB gain when used with hierarchical models.", "section": "EFFECTIVENESS OF OUR SOLUTIONS, AB<PERSON><PERSON><PERSON> STUDIES AND RUNTIME ANALYSIS", "sec_num": "5.2"}, {"text": "5. Our proposed permutation strategy provides significant gains of 0.5 dB at low bitrates and more than 1.5 dB at higher bitrates.", "section": "EFFECTIVENESS OF OUR SOLUTIONS, AB<PERSON><PERSON><PERSON> STUDIES AND RUNTIME ANALYSIS", "sec_num": "5.2"}, {"text": "Runtime Analysis: We list RECOMBINER's encoding and decoding times in Appendix D.5. Unfortunately, our approach exhibits a long encoding time, similar to COMBINER. However, our decoding process is still remarkably fast, matching the speed of COIN and COMBINER, even on CPUs.", "section": "EFFECTIVENESS OF OUR SOLUTIONS, AB<PERSON><PERSON><PERSON> STUDIES AND RUNTIME ANALYSIS", "sec_num": "5.2"}, {"text": "In this paper, we propose RECOMBINER, a new codec based on several non-trivial extensions to COMBINER, encompassing the linear reparameterization for the network weights, learnable positional encodings, and expressive hierarchical Bayesian models for high-resolution signals. Experiments demonstrate that our proposed method sets a new state-of-the-art on low-resolution images at low bitrates, and consistently delivers strong results across other data modalities.", "section": "CONCLUSIONS AND LIMITATIONS", "sec_num": "6"}, {"text": "A major limitation of our work is the encoding time complexity and tackling it should be of primary concern in future work. A possible avenue for solving this issue is to reduce the number of parameters to optimize over and switch from inference over weights to modulations using, e.g. FiLM layers (<PERSON> et al., 2018) , as is done in other INR-based works. A second limitation is that while compressing with patches enables parallelization and higher robustness, it is suboptimal as it leads to block artifacts, as can be seen in Figure 4 . Third, as <PERSON> et al. (2023) demonstrate, the approximate samples given by A ˚coding significantly impact the methods performance, e.g. by requiring more fine-tuning. An interesting question is whether an exact REC algorithm could be adapted to solve this issue, such as the recently developed greedy Poisson rejection sampler (<PERSON><PERSON>ich, 2023) .", "section": "CONCLUSIONS AND LIMITATIONS", "sec_num": "6"}, {"text": "We summarize the notations used in this paper in ", "section": "A NOTATIONS", "sec_num": null}, {"text": "tν m , ρ m u M m\"1 , A, ϕ Ð arg min tνm,ρmu M m\"1 ,A,ϕ ! 1 M ř M m\"1 LpD m , q h,m , p h , A, ϕ, βq", "section": "A NOTATIONS", "sec_num": null}, {"text": ") .", "section": "A NOTATIONS", "sec_num": null}, {"text": "Ź Optimize by Equation ( 6)", "section": "A NOTATIONS", "sec_num": null}, {"text": "# Step 2: Update prior µ Ð 1 M ř M m\"1 ν m , σ Ð 1 M ř M m\"1 \" pν m ´µq 2 `ρm ı .", "section": "A NOTATIONS", "sec_num": null}, {"text": "Ź Update by Equation ( 7)", "section": "A NOTATIONS", "sec_num": null}, {"text": "# Step 3: Update β δ \" 1 M ř M m\"1 D KL rq h,m ||p h s. Ź Calculate the average training KL if δ ą C then β Ð β ˆp1 `τC q Ź Increase β if budget is exceeded end if if δ ă C ´ϵC then β Ð β{p1 `τC q Ź Decrease β if budget is not fully occupied end if end repeat Return: p h \" N pµ, diag pσqq, A, ϕ.", "section": "A NOTATIONS", "sec_num": null}, {"text": "In this section, we describe the dataset and our experimental settings. We depict the upsampling network we used in Figure 6 and summarize the hyperparameters for each modality in Table 2 . Besides, we present details for the baselines in Appendix C.2. Note, that as the proposed linear reparameterization yields a full-covariance Gaussian posterior over the weights in the INR, the local reparameterization trick (<PERSON> et al., 2015) is not applicable in RECOMBINER. Therefore, in the above experiments, when inferring the posteriors of a test signal, we employ a Monte Carlo estimator with 5 samples to estimate the expectation in β-ELBO in Equation (1). While during the training stage, we still use 1 sample. In Appendix D.3, we provide an analysis of the sample size's influence. It is worth noting that using just 1 sample during inferring does not significantly deteriorate performance, and therefore we have the flexibility to reduce the sample size when prioritizing encoding time, with marginal performance impact.", "section": "C SUPPLEMENTARY EXPERIMENTAL DETAILS C.1 DATASETS AND MORE DETAILS ON EXPERIMENTS", "sec_num": null}, {"text": "CIFAR-10: CIFAR-10 is a set of low-resolution images with a size of 32 ˆ32. It has a training set of 50,000 images and a test set of 10,000 images. We randomly select 15,000 images from the training set for the training stage and evaluate RD performance on all test images. we use SIREN network (<PERSON><PERSON><PERSON> et al., 2020) with 4 layers and 32 hidden units as the INR architecture.", "section": "C SUPPLEMENTARY EXPERIMENTAL DETAILS C.1 DATASETS AND MORE DETAILS ON EXPERIMENTS", "sec_num": null}, {"text": "Kodak: Kodak dataset is a commonly used image compression benchmark, containing 24 images with resolutions of either 768 ˆ512 or 512 ˆ768. In our experiments, we split each images into 96 patches with size 64 ˆ64. Lacking a standard training set, we randomly select and crop 83 images with the same size (splitting into 7,968 patches) from the DIV2K dataset (<PERSON><PERSON><PERSON><PERSON> & Timofte, 2017) as the training set. We compress each Kodak image in 64 ˆ64 patches. For each patch, we use the same INR setup as that for CIFAR-10, i.e., SIREN network (<PERSON>zman<PERSON> et al., 2020) with 4 layers and 32 hidden units. Besides, we apply a three-level hierarchical Bayesian model to Kodak patches. The lowest level has 96 patches. Every 16 (4 ˆ4) patches are grouped together in the second level, and in total there are 6 groups. The highest level consists of a global representation for the entire image.", "section": "C SUPPLEMENTARY EXPERIMENTAL DETAILS C.1 DATASETS AND MORE DETAILS ON EXPERIMENTS", "sec_num": null}, {"text": "Audio: LibriSpeech (<PERSON><PERSON><PERSON><PERSON> et al., 2015) is a speech dataset recorded at a 16kHz sampling rate. We follow the experiment settings by <PERSON> et al. (2023) , taking the first 3 seconds of every recording, corresponding to 48,000 audio samples. We compress each audio clip with 60 patches, each of which has 800 audio samples. For each patch, we use the same INR architecture as CIFAR-10 except the output of the network has only one dimension. We train RECOMBINER on 197 training instances (corresponding to 11,820 patches) and evaluate it on the test set split by <PERSON> et al. (2023) , consisting of 24 instances. We also apply a three-level hierarchical model. The lowest level consists of 60 patches. Every 4 patches are grouped together in the second level, and in total there are 60{4 \" 16 groups. The highest level consists of a global representation for the entire signal.", "section": "C SUPPLEMENTARY EXPERIMENTAL DETAILS C.1 DATASETS AND MORE DETAILS ON EXPERIMENTS", "sec_num": null}, {"text": "Video: UCF-101 (<PERSON><PERSON><PERSON> et al., 2012) is a dataset of human actions. It consists of 101 action classes, over 13k clips, and 27 hours of video data. We follow <PERSON><PERSON><PERSON> et al. (2023) center-cropping each video clip to 240 ˆ240 ˆ24 and then resizing them to 128 ˆ128 ˆ24. Then we compress each clip with 16ˆ16ˆ24 patches. We train RECOMBINER on 75 video clips (4,800 patches) and evaluate it on 16 randomly selected clips. For each patch, we still use the INR with 4 layers and 32 hidden units. We also apply the three-level hierarchical model. The lowest level consists of 64 patches. Every 16 4 ˆ4 patches are grouped together in the second level, and in total, there are 4 groups. The highest level consists of a global representation for the entire clip. 3D Protein structure: We evaluate RECOMBINER on the Saccharomyces cerevisiae proteome from the AlphaFold DB v43 . To standardize the dataset, for each protein, we take the Cα atom of the first 96 residues (i.e., amino acids) as the target data to be compressed. The input coordinates are the indices of the Cα atoms (varying between 1-96, and normalized between 0-1) and the outputs of INRs are their corresponding 3D coordinates. We randomly select 1,000 structures as the test set and others as the training set. We still use the same INR architecture as CIFAR-10, i.e., SIREN network with 4 layers and 32 hidden units in each layer. We use the standard MSE as the distortion measure. Note that our method can also be extended to take the fact that the 3D structure is rotation and translation invariant into account by using different losses. Figure 6 : Architecture of the up-sampling network ϕ for learnable positional encodings. The numbers in the convolution layer represent the number of input channels, the number of output channels, and kernel size respectively. same padding mode is used in all convolution layers. The kernel dimension depends on the modality, for instances, we use kernels with sizes of 5, 3, 3 for audio and proteins, kernels with sizes of 5 ˆ5, 3 ˆ3, 3 ˆ3 for images, and kernels with sizes of 5 ˆ5 ˆ5, 3 ˆ3 ˆ3, 3 ˆ3 ˆ3 for video.", "section": "C SUPPLEMENTARY EXPERIMENTAL DETAILS C.1 DATASETS AND MORE DETAILS ON EXPERIMENTS", "sec_num": null}, {"text": "The baseline performances, including JPEG2000, BPG, COIN, COIN++, <PERSON><PERSON> et al. (2018) vided by the authors in the paper. We also include a comparison of RECOMBINER and COMBINER on 24 test audio clips since the authors of COMBINER did not test on the full test set. For this comparison, the performances of COMBINER and MP3 on 24 test audio clips are provided by the authors of COMBINER.", "section": "C.2 BASELINE SETTINGS", "sec_num": null}, {"text": "Below, we describe details about the baseline of the video and protein structure compression.", "section": "C.2 BASELINE SETTINGS", "sec_num": null}, {"text": "Video compression baselines are implemented by ffmpeg (<PERSON><PERSON>, 2006) , with the following commands.", "section": "C.2.1 VIDEO BASELINES", "sec_num": null}, {"text": "H PIC first employs a lossy mapping, converting the 3D coordinates of atoms to an image, and then lossless compresses the image in PNG format. We use the PNG image size to calculate the bitrate.", "section": "C.2.1 VIDEO BASELINES", "sec_num": null}, {"text": "As for PDC and Foldcomp, since they directly operate on PDB files containing other information like the headers, sequences, B factor, etc., we cannot use the file size directly. Therefore, we use their theoretical bitrates as our baseline. Below we present how we calculate their theoretical bitrates.", "section": "C.2.1 VIDEO BASELINES", "sec_num": null}, {"text": "PDC uses three 4-byte integers to save the coordinates of the first Cα atom, and three 1-byte integers for coordinate differences of all remaining Cα atoms. Therefore, in theory, for a 96-residue length protein, each Cα atom is assigned with p8 ˆ3 ˆ95 `4 ˆ8 ˆ3 ˆ1q{96 bits.", "section": "C.2.1 VIDEO BASELINES", "sec_num": null}, {"text": "Foldcomp compresses the quantized dihedral/bond angles for each residue. Every residue needs 59 bits. Besides, Foldcomp saves uncompressed coordinates for every 25 residues as anchors, which requires 36 bytes. Therefore, the theoretical number of bits assigned to each Cα is given by p36 8 `59 ˆ25q{25. However, since Foldcomp is designed to encode all backbone atoms (C, N, Cα) instead of merely Cα, it is unfair to compare in this way. We thus also report its performance on all backbone atoms for reference.", "section": "C.2.1 VIDEO BASELINES", "sec_num": null}, {"text": "In this section, we describe the details settings for ablation studies in Figures 5b and 5c .", "section": "C.3 ABLATION STUDY SETTINGS", "sec_num": null}, {"text": "Experiments without Linear Reparameterization: We simply set w \" h w without the linear matrix A. Besides, since in this case, w follows mean-field Gaussian, we use the local reparameterization trick with 1 sample to reduce the variance during both training and inferring.", "section": "C.3 ABLATION STUDY SETTINGS", "sec_num": null}, {"text": "Experiments without Positional Encodings: Recall that the inputs of INRs in RECOMBINER is the concatenation of Fourier transformed coordinates γpx i q and the upsampled positional encodings at the corresponding position z i \" ϕph z q xi . In the experiments without positional encodings, we only input the Fourier transformed coordinates to the INR. To keep the INR size consistent, we also increase the dimension of the Fourier transformation, so that dimpγ 1 px i qq Ð dimpγpx i qq `dimpz i q. Also, we no longer need to train the upsampling network ϕ.", "section": "C.3 ABLATION STUDY SETTINGS", "sec_num": null}, {"text": "We assume all patch-INRs are independent and simply assign independent mean-field Gaussian priors and posteriors over h pπq w for each patch. Experiments without Random Permutation across patches: Recall in RECOMBINER, for each level in the hierarchical model, we stack the representations together into a matrix, where each row is one representation. We then (a) apply the same permutation over all rows. This is the same as COMBINER and is to ensure KL is distributed uniformly across the entire representation for each patch. Then (b) for each column, we apply its own permutation to encourage KL to be distributed uniformly across patches. In the ablation study, we do not only apply the permutation in (b) but still perform the permutation in (a). encodings already present a pattern of the image. This is an indication of how the learnable positional encodings help with the fitting. When the target signal is intricate, and there is a strict bitrate constraint, the INR capacity is insufficient for learning the complex mapping from coordinates to signal values directly. On the other hand, when combined with positional encodings, INR simply needs to extract, combine, and enhance this information, instead of \"creating\" information from scratch. This aligns with the findings of the ablation study, which indicate that learnable positional encodings have a more significant impact on CIFAR-10 at low bitrates and the <PERSON>dak dataset, but a small effect on CIFAR-10 at high bitrates.", "section": "Experiments without Hierarchical Model:", "sec_num": null}, {"text": "Information contained in h w : To visualize the information contained in h w , we also take kodim03 at 0.488 bpp as an example. We reconstruct the image using h w for this image but mask out h Z by the prior mean. The image reconstructed in this way is shown in Fig 7b . From the figure, we can clearly see h w mostly captures the color specific to each patch, in comparison to the positional encodings containing information more about edges and shapes. Moreover, interestingly, we can see patches close to each other share similar patterns, indicating the redundancy between patches. This explains why employing the hierarchical model provides substantial gains, especially when applying it together with positional encodings.", "section": "Experiments without Hierarchical Model:", "sec_num": null}, {"text": "Linear Transform A: To interpret how the linear reparameterization works, we take the Kodak dataset as an example, and visualize A for the second layer (i.e., A r2s ) at 0.074 and 0.972 bpp in Fig 7c and 7d . Note that this layer has 32 hidden units and thus A r2s has a shape of 1056 ˆ1056. We only take a subset of 150 ˆ150 in order to have a clearer visualization. Recall w \" h w A, and thus rows correspond to dimensions in h w and columns correspond to dimensions in w.", "section": "Experiments without Hierarchical Model:", "sec_num": null}, {"text": "It can be seen that when the bitrate is high, many rows in A are active, enabling a flexible model. Conversely, at lower bitrates, many rows become 0, effectively pruning out corresponding dimensions. This explains clearly how A contributes to improve the performance: first, A greatly promotes parameter sharing. For instance, at low bitrates, merely 10 percent of the parameters get involved in constructing the entire network. Second, the pruning in h w is more efficient than that in w directly. The predecessor of RECOMBINER, i.e., COMBINER, utilizes standard Bayesian neural networks. It controls its bitrates by pruning or activating the hidden units. When a unit is pruned, the entire column in the weight matrix will be pruned out (<PERSON><PERSON> & Turner, 2017) . In other words, in COMBINER, the pruning in w is always conducted in chunks, which highly limits the flexibility of the network. On the contrary, in our approach, the linear reparameterization enables a direct pruning or activating of each dimension in h w individually, ensuring the flexibility of INR while effectively managing the rate.", "section": "Experiments without Hierarchical Model:", "sec_num": null}, {"text": "Another interesting observation is the matrix A essentially learns a low-rank pattern without manual tuning. This is in comparison with VC-INR (<PERSON><PERSON><PERSON> et al., 2023) where the low-rank pattern is explicitly enforced by manually setting the LoRA-style (<PERSON> et al., 2021) modulation.", "section": "Experiments without Hierarchical Model:", "sec_num": null}, {"text": "In this section, we provide an example illustrating the effectiveness of random permutation across patches. Specifically, we encode kodim23 at 0.074 bpp, both with and without random permutation, and visualize their residual images in Figure 8 . We can see that, without permutation, the residuals for complex patches are significantly larger than simpler patches. This is due to the fact that, in RECOMBINER, the bits allocated to each patch are merely determined by the number of blocks, which is shared across all the patches. On the other hand, after the permutation, we can see a more balanced distribution of residuals across patches: complex patches achieve better reconstructions, whereas simple patches' performances only degrade marginally. This is because, after the permutation across patches, each block can have different patches' representations, enabling an adaptive allocation of bits across patches. Overall, random permutation yields a 1.00 dB gain on this image.", "section": "D.2 EFFECTIVENESS OF RANDOM PERMUTATION", "sec_num": null}, {"text": "As discussed in Appendix C.1, in our experiments, we use 5 samples to estimate the expectation in the β-ELBO in Equation (1), when inferring the posterior of a test datum. Here, we provide the RD As shown in Figure 9 , the sample size mainly impacts the performance at high bitrates. Besides, further increasing the sample size to 10 only brings a minor improvement. Therefore, we choose 5 samples in our experiments to balance between encoding time and performance. It is also worth noting that using just 1 sample does not significantly reduce the performance. Therefore, we have the flexibility of choosing smaller sample sizes when prioritizing encoding time, with minor performance impacts. (<PERSON> et al., 2017) including COIN++ (<PERSON> et al., 2022) , MSCN (Schwarz & Teh, 2022) and VC-INR (<PERSON><PERSON><PERSON> et al., 2023) , our proposed RECOMBINER does not require nested gradient descent and thus features higher stability during training period.", "section": "D.3 INFLUENCE OF SAMPLE SIZE", "sec_num": null}, {"text": "To demonstrate this advantage, we present a visualization of the average β-ELBO during training on CIFAR-10 across three bitrates in Figure 10 . We can see that the training curves exhibit an initial dip followed by a consistent increase. The dip at the beginning is a result of our adjustment of β during training (Step 3 in Algorithm 1). Importantly, this adjustment does not impact training robustness; and we can see that β is quickly adjusted, and the training proceeds smoothly. In this section, we provide details regarding the encoding and decoding time of RECOMBINER. The encoding speed is measured on a single NVIDIA A100-SXM-80GB GPU. On CIFAR-10 and protein structures, we compress signals in batch, with a batch size of 500 images and 1,000 structures, respectively. On Kodak, audio, and video datasets, we compress each signal separately. We should note that the batch size does not influence the results. Posteriors of signals within one batch are optimized in parallel, and their gradients are not crossed. The decoding speed is measured per signal on CPU.", "section": "D.3 INFLUENCE OF SAMPLE SIZE", "sec_num": null}, {"text": "Similar to COMBINER, our approach features a high encoding time complexity. However, the decoding process is remarkably fast, even on CPU, matching the speed of COIN and COMBINER. Note that the decoding time listed here encompasses the retrieval of samples for each block. In practical applications, this process can be implemented and parallelized using lower-level languages such as C++ or C, which can lead to further acceleration of execution. E THINGS WE TRIED THAT DID NOT WORK", "section": "D.3 INFLUENCE OF SAMPLE SIZE", "sec_num": null}, {"text": "• in RECOMBINER, we apply linear reparameterization on INR weights, which transfers the weights linearly into a transformed space. Perhaps a natural extension is to apply more complex transformations, e.g., neural networks, or flows. We experimented with this idea, but it did not provide gains over the linear transformation.", "section": "Bitrate", "sec_num": null}, {"text": "• in RECOMBINER, we propose a hierarchical Bayesian model, equivalent to assigning hierarchical hyper-priors and inferring the hierarchical posteriors over the means of the INR weights. A natural extension can be assigning hyper-priors/posteriors to both means and variances. But we did not find any gain by this.", "section": "Bitrate", "sec_num": null}, {"text": "• in RECOMBINER, the hierarchical Bayesian model is only applied to the latent INR weights h w . It is natural to apply the same hierarchical structure to the latent positional encodings h z . However, we found it does not provide visible gain.", "section": "Bitrate", "sec_num": null}, {"text": "Here, we show the full-resolution RD curves for image compression in Figures 11 and 12 . Besides, we also provide a further comparison between RECOMBINER with COMBINER on 24 test audio clips from LibriSpeech in Figure 13 . ", "section": "F MORE RD CURVES", "sec_num": null}, {"text": "As a slight abuse of notation, we use ϕ to denote both the upsampling function and its parameters.", "section": "", "sec_num": null}, {"text": "https://vcgit.hhi.fraunhofer.de/jvet/VVCSoftware_VTM/-/tree/VTM-12.0?ref_type=tags", "section": "", "sec_num": null}, {"text": "https://ftp.ebi.ac.uk/pub/databases/alphafold/v4/UP000002311_559292_ YEAST_v4.tar", "section": "", "sec_num": null}, {"text": "https://github.com/EmilienDupont/coinpp", "section": "", "sec_num": null}], "back_matter": [{"text": "The authors would like to thank <PERSON><PERSON> for helping us ensure that our baseline for our experiments on video compression is correctly set up. GF acknowledges funding from DeepMind. ZG acknowledges funding from the Outstanding PhD Student Program at the University of Science and Technology of China.", "section": "ACKNOWLEDGEMENTS", "sec_num": "7"}, {"text": "In this section, we bring insights into our methods by visualizations. Recall that each signal is represented by h Z and h w together in RECOMBINER. We visualize the information contained in each of them. Besides, we visualize the linear transform A to understand how it improves performances.", "section": "annex", "sec_num": null}, {"text": "We take kodim03 at 0.488 bpp as an example, and visualize 4 channels of its upsampled positional encodings ϕph z q in [23.592, 27.222, 28.505, 30.911, 32.168, 35.732, 38.139] Kodak: rate = [0.074, 0.130, 0.178, 0.316, 0.488, 0.972, 1.567, 3.320] PSNR = [26.158, 27.653, 28.594, 30.439, 31.953, 34.540, 36.547, 40.426] Audio:On full test set: rate = [5. 685, 10.661, 22.112, 43.637] PSNR = [42.612, 47.101, 52.196, 58.195] On 24 test examples (to compare with COMBINER): rate = [5. 168, 10.805, 22.112, 43.637] PSNR = [42.789, 47.106, 52.206, 58.327] Video: rate = [0.115, 0.244, 0.605, 1.183] PSNR = [28.722, 31.494, 35.717, 39.171] Protein: rate = [11.17, 35.17, 60.67, 83.83, 106.17 ", "section": "Positional encodings:", "sec_num": null}], "ref_entries": {"FIGREF0": {"uris": null, "num": null, "fig_num": "1", "type_str": "figure", "text": "Figure 1: Schematic of (a) COMBINER and (b) RECOMBINER, our proposed method. See Sections 2 and 3 for notation. As the INR's input, RECOMBINER uses h z upsampled to pixel-wise positional encodings concatenated with Fourier embeddings. (c) A closer look at how RECOMBINER maps h z to the INR input, taking images as an example. FE: Fourier embeddings; FC: fully connected layer."}, "FIGREF1": {"uris": null, "num": null, "fig_num": "2", "type_str": "figure", "text": "Figure 2: Illustration of (a) the three-level hierarchical model and (b) our permutation strategy."}, "FIGREF2": {"uris": null, "num": null, "fig_num": null, "type_str": "figure", "text": "Training RECOMBINER: Our objective for the training stage is to obtain the model parameters A, ϕ, µ, σ given a training dataset tD 1 , . . . , D M u and a coding budget C. 1 In their work, <PERSON> et al. (2023) control the coding budget implicitly by manually setting different values for β in Equation (1)."}, "FIGREF3": {"uris": null, "num": null, "fig_num": null, "type_str": "figure", "text": "RD curve on video. RECOMBINER (ours) H.265 (best quality) H.264 (best quality) H.265 (best speed) H.264 (best speed) RD curve on protein data.RECOMBINER (ours) Foldcomp : (backbone) Foldcomp : (Cα) PDC : (Cα) PIC (Cα) : \" theoretical rate 1.18 bpp 40.24 dB 0.12 bpp 28.68 dB (e) Decoded videos and residuals. Bitrate 11.17 bpa RMSD 0.99 Å Bitrate 35.17 bpa RMSD 0.12 Å ■ Ground Truth ■ Decoded Structure (f) Decoded protein structure examples."}, "FIGREF4": {"uris": null, "num": null, "fig_num": "3", "type_str": "figure", "text": "Figure 3: Quantitive evaluation and qualitative examples of RECOMBINER on image, audio, video, and 3D protein structure. Kbps stands for kilobits per second, RMSD stands for Root Mean Square Deviation, and bpa stands for bits per atom. For all plots, we use solid lines to denote INR-based codecs, dotted lines to denote VAE-based codecs, and dashed lines to denote classical codecs."}, "FIGREF5": {"uris": null, "num": null, "fig_num": null, "type_str": "figure", "text": "(a) w/o positional encodings; bitrate 0.287 bpp; PSNR 25.62 dB. (b) with positional encodings; bitrate 0.316 bpp; PSNR 26.85 dB. (c) with positional encodings; bitrate 0.178 bpp; PSNR 25.05 dB."}, "FIGREF6": {"uris": null, "num": null, "fig_num": "4", "type_str": "figure", "text": "Figure 4: Comparison between kodim24 details compressed with and without learnable positional encodings. (a)(b) have similar bitrates and (a)(c) have similar PSNRs."}, "FIGREF7": {"uris": null, "num": null, "fig_num": "5", "type_str": "figure", "text": "Figure 5: (a) RD performances of COMBINER and RECOMBINER with different numbers of hidden units. (b)(c) Ablation studies on CIFAR-10 and Kodak. LR: linear reparameterization; PE: positional encodings; HM: hierarchical model; RP: random permutation across patches. We describe the details of experimental settings for ablation studies in Appendix C.3."}, "FIGREF9": {"uris": null, "num": null, "fig_num": null, "type_str": "figure", "text": "Figure 8: Comparison of residuals of kodim23 at 0.074 bpp, with and without random permutation across patches."}, "FIGREF11": {"uris": null, "num": null, "fig_num": "9", "type_str": "figure", "text": "Figure 9: Influence of Sample size. (a) RD curve evaluated on 500 randomly selected CIFAR-10 images. (b) RD curve evaluated on kodim03."}, "FIGREF13": {"uris": null, "num": null, "fig_num": "10", "type_str": "figure", "text": "Figure 10: Average training β-ELBO on Cifar-10 at three different bitrates. The initial dip is because we also adjust β during training to ensure the coding budget (Step 3 in Algorithm 1). We can see the initial β quickly adjusts in the first several steps, and then the training proceeds smoothly."}, "FIGREF14": {"uris": null, "num": null, "fig_num": "16", "type_str": "figure", "text": "Figure 11: RD curve on CIFAR-10."}, "FIGREF15": {"uris": null, "num": null, "fig_num": "17", "type_str": "figure", "text": "Figure 17: Examples of decoded protein structures and their ground truths."}, "TABREF1": {"num": null, "type_str": "table", "html": null, "content": "<table/>", "text": ", we depict RECOMBINER's RD curve on the full test set, alongside the curves of VC-INR, COIN++, and MP3. We can see RECOMBINER outperforms both COIN++ and MP3 and matches with VC-INR. Since<PERSON><PERSON> et al. (2023) only tested COMBINER on 24 test clips, we do not include COMBINER in this plot but put an extra comparison in Figure13in Appendix F, where we can also see that RECOMBINER clearly outperforms COMBINER."}, "TABREF3": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td colspan=\"2\">Notation Name</td></tr><tr><td>β</td><td>rate penalty hyperparameter in Equation (1)</td></tr><tr><td>C</td><td>coding budget</td></tr><tr><td>τ C</td><td>step size for adjusting β</td></tr><tr><td>ϵ C</td><td>threshold parameter to stabilize training when adjusting β</td></tr><tr><td>w</td><td>weights in INR</td></tr><tr><td>x i</td><td>ith coordinate</td></tr><tr><td>y i</td><td>ith signal value</td></tr><tr><td>z i</td><td>RECOMBINER's upsampled positional encodings at coordinate x i</td></tr><tr><td>h w</td><td>RECOMBINER's latent INR weights</td></tr><tr><td>h z</td><td>RECOMBINER's latent positional encodings</td></tr><tr><td>h pπq w</td><td>latent INR weights for πth patch (lowest level of the hierarchical model)</td></tr><tr><td>h pπq z</td><td>latent positional encodings for πth patch (lowest level of the hierarchical model)</td></tr><tr><td>h pgq w</td><td>gth representation in the second level of the hierarchical model</td></tr><tr><td>h w</td><td>third level representations of the hierarchical model</td></tr><tr><td>ν</td><td>mean of the Gaussian posterior</td></tr><tr><td>µ</td><td>mean of the Gaussian prior</td></tr><tr><td>ρ</td><td>diagonal of the covariance matrix of the Gaussian posterior</td></tr><tr><td>σ</td><td>diagonal of the covariance matrix of the Gaussian prior</td></tr><tr><td>A</td><td>RECOMBINER's linear transform on INR weights</td></tr><tr><td>H pℓq</td><td>matrix stacking representations in the ℓth level defined in Equation (5)</td></tr><tr><td>Ă H pℓq</td><td>matrix for representations in the ℓth level after permutation</td></tr><tr><td>D</td><td>a signal data point (as a dataset with coordinate-value pairs)</td></tr><tr><td>S n</td><td>set of all permutations on n elements</td></tr><tr><td>γp¨q</td><td>Fourier embedding to coordinates</td></tr><tr><td colspan=\"2\">αp¨q, κp¨q a permutation</td></tr><tr><td>ϕp¨q</td><td>upsampling network for positional encodings</td></tr><tr><td colspan=\"2\">gp¨| wq INR with weights w</td></tr></table>", "text": ""}, "TABREF4": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Algorithm 1 Initialize: A, ϕ.</td></tr><tr><td>repeat until convergence</td></tr><tr><td># Step 1: Optimize posteriors, linear reparameterization matrix, and upsampling network</td></tr></table>", "text": "Notations. B RECOMBINER'S TRAINING ALGORITHMS We describe the algorithm to train RECOMBINER in Algorithm 1. Training RECOMBINER: the prior, the linear transform A and upsampling network ϕ Require: Training data tD 1 , ..., D M u; desired bitrate C.Initialize: q h,m \" N pν m , diag pρ m qq for every training instance D m . Initialize: p h \" N pµ, diag pσqq."}, "TABREF5": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td/><td/><td>Image</td><td colspan=\"2\">Audio</td><td/><td>Video</td><td>Protein</td></tr><tr><td/><td>Cifar-10</td><td>Kodak</td><td/><td/><td/></tr><tr><td/><td/><td>Patching</td><td/><td/><td/></tr><tr><td>patch or not</td><td>✗</td><td>✓</td><td>✓</td><td/><td/><td>✓</td><td>✗</td></tr><tr><td>patch size</td><td>z</td><td>64 ˆ64</td><td>800</td><td/><td colspan=\"2\">16 ˆ16 ˆ24</td><td>z</td></tr><tr><td>hierarchical model levels</td><td>z</td><td>3</td><td>3</td><td/><td/><td>3</td><td>z</td></tr><tr><td>number of patches (lowest level)</td><td>z</td><td>96</td><td>60</td><td/><td/><td>64</td><td>z</td></tr><tr><td>number of groups of patches (middle level)</td><td>z</td><td>6</td><td>16</td><td/><td/><td>4</td><td>z</td></tr><tr><td>number of groups of groups (highest level)</td><td>z</td><td>1</td><td>1</td><td/><td/><td>1</td><td>z</td></tr><tr><td/><td/><td>Positional Encodings</td><td/><td/><td/></tr><tr><td>latent positional encoding shape</td><td>128 ¨2 ¨2</td><td>128 ¨4 ¨4</td><td colspan=\"2\">128 ¨50</td><td colspan=\"2\">128 ¨1 ¨1 ¨1</td><td>128 ¨6</td></tr><tr><td>latent positional encoding param number</td><td>512</td><td>2560</td><td>6400</td><td/><td/><td>128</td><td>768</td></tr><tr><td>upsampled positional encoding shape</td><td>16 ˆ32 ˆ32</td><td>16 ˆ64 ˆ64</td><td colspan=\"2\">16 ˆ800</td><td colspan=\"2\">16 ˆ16 ˆ16 ˆ24</td><td>16 ˆ96</td></tr><tr><td/><td/><td>INR Architecture</td><td/><td/><td/></tr><tr><td>layers</td><td/><td/><td>4</td><td/><td/></tr><tr><td>hidden units</td><td/><td/><td>32</td><td/><td/></tr><tr><td>Fourier embeddings dimension output dimension</td><td>16 3</td><td>16 3</td><td>16 1</td><td/><td colspan=\"2\">18 ( 16 3 is not integer) 3</td><td>16 1</td></tr><tr><td>number of parameters</td><td>3267</td><td>3267</td><td>3201</td><td/><td/><td>3331</td><td>3201</td></tr><tr><td/><td/><td>Training Stage</td><td/><td/><td/></tr><tr><td>training size</td><td>15000</td><td colspan=\"3\">83 (7968 patches) 197 (11820 patches)</td><td colspan=\"2\">75 (4800 patches)</td><td>4691</td></tr><tr><td>epochs</td><td/><td/><td>550</td><td/><td/></tr><tr><td>optimizer</td><td/><td/><td colspan=\"2\">Adam (lr=0.0002)</td><td/></tr><tr><td>sample size to estimate β-ELBO</td><td/><td/><td>1</td><td/><td/></tr><tr><td>gradient iteration between updating prior</td><td/><td/><td>100</td><td/><td/></tr><tr><td>the first gradient iteration</td><td/><td/><td>200</td><td/><td/></tr><tr><td>initial posterior variance</td><td/><td/><td>9 ˆ10</td><td>´6</td><td/></tr><tr><td>initial posterior mean</td><td/><td/><td colspan=\"2\">SIREN initialization</td><td/></tr><tr><td colspan=\"3\">initial A rls values A \" ϵ C 0.3 bpp 0.05 bpp</td><td colspan=\"2\">0.5 kbps</td><td/><td>0.3 bpp</td><td>0.3 bpa</td></tr><tr><td>β</td><td/><td colspan=\"4\">Adaptively adjusted. Initial value 1 ˆ10</td><td>´8</td></tr><tr><td/><td colspan=\"3\">Posterior Inferring and Compression Stage</td><td/><td/></tr><tr><td>gradient descent iteration</td><td/><td/><td colspan=\"2\">30000</td><td/></tr><tr><td>optimizer</td><td/><td/><td colspan=\"2\">Adam (lr=0.0002)</td><td/></tr><tr><td>sample size to estimate β-ELBO</td><td/><td/><td>5</td><td/><td/></tr><tr><td>blocks per signal (total number of blocks)</td><td>{19,46,60,98, 123,214,281}</td><td>{1819, 3187, 4373,7770, 12004, 23898}</td><td colspan=\"2\">{1066, 1999, 4146, 8182}</td><td colspan=\"2\">{2827, 5992, 14858, 29073}</td><td>{67, 211, 364 503, 637}</td></tr><tr><td>bits per block</td><td/><td/><td colspan=\"2\">16 bits</td><td/></tr><tr><td>blocks in the lowest level (patch)</td><td>z</td><td>{17, 30, 41, 73, 114, 233}</td><td colspan=\"2\">{15, 31, 64, 122}</td><td colspan=\"2\">{34, 71, 198, 409}</td><td>z</td></tr><tr><td>blocks in the middle level</td><td>z</td><td>{17, 34, 52, 102, 145, 211}</td><td colspan=\"2\">{5, 5, 14, 50}</td><td colspan=\"2\">{109, 284, 427, 561}</td><td>z</td></tr><tr><td>blocks in the highest level</td><td>z</td><td>{85,103, 125, 150, 190, 264}</td><td colspan=\"2\">{31, 64, 96, 112}</td><td colspan=\"2\">{215, 312, 478, 653}</td><td>z</td></tr></table>", "text": "and <PERSON> et al. (2020) on and MP3 and COIN++ on  the full test set of Lib-riSpeech, are taken from the COIN++'s GitHub repo 4 . Statistics for VC-INR and MSCN are pro-Up´1{a, 1{aq, a \" d in d out where d in and d out are the input and output dimension for layer l. Hyperparameters for images, audio, video, and protein structure compression."}, "TABREF7": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td/><td/><td>Encoding Time</td><td>Decoding Time</td></tr><tr><td/><td/><td>(GPU, 500 instances)</td><td>(CPU, per instance)</td></tr><tr><td colspan=\"2\">0.297 bpp</td><td>\"63 min</td><td>0.00386 s</td></tr><tr><td colspan=\"2\">0.719 bpp</td><td>\"65 min</td><td>0.00429 s</td></tr><tr><td colspan=\"2\">0.938 bpp</td><td>\"68 min</td><td>0.00461 s</td></tr><tr><td colspan=\"2\">1.531 bpp</td><td>\"72 min</td><td>0.00514 s</td></tr><tr><td colspan=\"2\">1.922 bpp</td><td>\"75 min</td><td>0.00581 s</td></tr><tr><td colspan=\"2\">3.344 bpp</td><td>\"87 min</td><td>0.00776 s</td></tr><tr><td colspan=\"2\">4.391 bpp</td><td>\"93 min</td><td>0.01050 s</td></tr><tr><td>Bitrate</td><td colspan=\"3\">Encoding Time (GPU, per instance, 96 patches)</td><td>Decoding Time (CPU, per instance)</td></tr><tr><td>0.074 bpp</td><td/><td>\"59 min</td><td>0.25848 s</td></tr><tr><td>0.130 bpp</td><td/><td>\"64 min</td><td>0.29117 s</td></tr><tr><td>0.178 bpp</td><td/><td>\"67 min</td><td>0.30875 s</td></tr><tr><td>0.316 bpp</td><td/><td>\"72 min</td><td>0.29690 s</td></tr><tr><td>0.488 bpp</td><td/><td>\"80 min</td><td>0.34237 s</td></tr><tr><td>0.972 bpp</td><td/><td>\"92 min</td><td>0.41861 s</td></tr></table>", "text": "Coding time for CIFAR-10."}, "TABREF8": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Bitrate</td><td>Encoding Time (GPU, per instance, 50 patches)</td><td>Decoding Time (CPU, per instance)</td></tr><tr><td>5.69 kbps</td><td>\"18 min</td><td>0.05564 s</td></tr><tr><td>10.66 kbps</td><td>\"21 min</td><td>0.06003 s</td></tr><tr><td>22.11 kbps</td><td>\"22 min</td><td>0.06166 s</td></tr><tr><td>43.64 kbps</td><td>\"22 min</td><td>0.07350 s</td></tr></table>", "text": "Coding time forKodak."}, "TABREF9": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Bitrate</td><td>Encoding Time (GPU, per instance, 64 patches)</td><td>Decoding Time (CPU, per instance)</td></tr><tr><td>0.115 bpp</td><td>\"49 min</td><td>0.31936 s</td></tr><tr><td>0.244 bpp</td><td>\"62 min</td><td>0.33416 s</td></tr><tr><td>0.605 bpp</td><td>\"78 min</td><td>0.33448 s</td></tr><tr><td>1.183 bpp</td><td>\"102 min</td><td>0.35665 s</td></tr></table>", "text": "Coding time for Audio."}, "TABREF10": {"num": null, "type_str": "table", "html": null, "content": "<table><tr><td>Bitrate</td><td>Encoding Time (GPU, 1000 instance)</td><td>Decoding Time (CPU, per instance)</td></tr><tr><td>11.17 bpa</td><td>\"72 min</td><td>0.00704 s</td></tr><tr><td>35.17 bpa</td><td>\"123 min</td><td>0.00948 s</td></tr><tr><td>60.67 bpa</td><td>\"175 min</td><td>0.01429 s</td></tr><tr><td>83.83 bpa</td><td>\"226 min</td><td>0.01778 s</td></tr><tr><td>106.17 bpa</td><td>\"274 min</td><td>0.02014 s</td></tr></table>", "text": "Coding time for Video."}, "TABREF11": {"num": null, "type_str": "table", "html": null, "content": "<table/>", "text": "Coding time for <PERSON><PERSON>."}}}}