{"paper_id": "iTransformer", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-24T23:38:14.214022Z"}, "title": "ITRANSFORMER: INVERTED TRA<PERSON><PERSON>ORMERS ARE EFFECTIVE FOR TIME SERIES FORECASTING", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": "<EMAIL>"}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "Haixu", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "Ant Group", "institution": "", "location": {"settlement": "Hangzhou", "country": "China"}}, "email": ""}, {"first": "Lintao", "middle": [], "last": "Ma", "suffix": "", "affiliation": {"laboratory": "Ant Group", "institution": "", "location": {"settlement": "Hangzhou", "country": "China"}}, "email": "<EMAIL>"}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {}, "email": "<EMAIL>"}], "year": "", "venue": null, "identifiers": {}, "abstract": "The recent boom of linear forecasting models questions the ongoing passion for architectural modifications of Transformer-based forecasters. These forecasters leverage Transformers to model the global dependencies over temporal tokens of time series, with each token formed by multiple variates of the same timestamp. However, Transformers are challenged in forecasting series with larger lookback windows due to performance degradation and computation explosion. Besides, the embedding for each temporal token fuses multiple variates that represent potential delayed events and distinct physical measurements, which may fail in learning variate-centric representations and result in meaningless attention maps. In this work, we reflect on the competent duties of Transformer components and repurpose the Transformer architecture without any modification to the basic components. We propose iTransformer that simply applies the attention and feed-forward network on the inverted dimensions. Specifically, the time points of individual series are embedded into variate tokens which are utilized by the attention mechanism to capture multivariate correlations; meanwhile, the feed-forward network is applied for each variate token to learn nonlinear representations. The iTransformer model achieves state-of-the-art on challenging real-world datasets, which further empowers the Transformer family with promoted performance, generalization ability across different variates, and better utilization of arbitrary lookback windows, making it a nice alternative as the fundamental backbone of time series forecasting. Code is available at this repository: https://github.com/thuml/iTransformer.", "pdf_parse": {"paper_id": "iTransformer", "_pdf_hash": "", "abstract": [{"text": "The recent boom of linear forecasting models questions the ongoing passion for architectural modifications of Transformer-based forecasters. These forecasters leverage Transformers to model the global dependencies over temporal tokens of time series, with each token formed by multiple variates of the same timestamp. However, Transformers are challenged in forecasting series with larger lookback windows due to performance degradation and computation explosion. Besides, the embedding for each temporal token fuses multiple variates that represent potential delayed events and distinct physical measurements, which may fail in learning variate-centric representations and result in meaningless attention maps. In this work, we reflect on the competent duties of Transformer components and repurpose the Transformer architecture without any modification to the basic components. We propose iTransformer that simply applies the attention and feed-forward network on the inverted dimensions. Specifically, the time points of individual series are embedded into variate tokens which are utilized by the attention mechanism to capture multivariate correlations; meanwhile, the feed-forward network is applied for each variate token to learn nonlinear representations. The iTransformer model achieves state-of-the-art on challenging real-world datasets, which further empowers the Transformer family with promoted performance, generalization ability across different variates, and better utilization of arbitrary lookback windows, making it a nice alternative as the fundamental backbone of time series forecasting. Code is available at this repository: https://github.com/thuml/iTransformer.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Transformer (<PERSON><PERSON><PERSON><PERSON> et al., 2017) has achieved tremendous success in natural language processing (<PERSON> et al., 2020) and computer vision (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021) , growing into the foundation model that follows the scaling law (<PERSON> et al., 2020) .", "cite_spans": [{"start": 12, "end": 34, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF28"}, {"start": 98, "end": 118, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF3"}, {"start": 139, "end": 165, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021)", "ref_id": "BIBREF7"}, {"start": 231, "end": 252, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF11"}], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Inspired by the immense success in extensive fields, Transformer with strong capabilities of depicting pairwise dependencies and extracting multi-level representations in sequences is emerging in time series forecasting (<PERSON> et al., 2021; <PERSON><PERSON> et al., 2023) .", "cite_spans": [{"start": 220, "end": 237, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF29"}, {"start": 238, "end": 255, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "However, researchers have recently begun to question the validity of Transformer-based forecasters, which typically embed multiple variates of the same timestamp into indistinguishable channels and apply attention on these temporal tokens to capture temporal dependencies. Considering the numerical but less semantic relationship among time points, researchers find that simple linear layers, which can be traced back to statistical forecasters (<PERSON> & Jenkins, 1968) , have exceeded complicated Transformers on both performance and efficiency (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) . Meanwhile, ensuring the independence of variate and utilizing mutual Transformer embeds the temporal token, which contains the multivariate representation of each time step. iTransformer embeds each series independently to the variate token, such that the attention module depicts the multivariate correlations and the feed-forward network encodes series representations.", "cite_spans": [{"start": 445, "end": 466, "text": "(Box & Jenkins, 1968)", "ref_id": "BIBREF2"}, {"start": 543, "end": 562, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF32"}, {"start": 563, "end": 580, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "information is ever more highlighted by recent research that explicitly models multivariate correlations to achieve accurate forecasting (<PERSON> & <PERSON>, 2023; <PERSON><PERSON><PERSON> et al., 2023) , but this goal can be hardly achieved without subverting the vanilla Transformer architecture.", "cite_spans": [{"start": 137, "end": 156, "text": "(Zhang & Yan, 2023;", "ref_id": "BIBREF33"}, {"start": 157, "end": 180, "text": "<PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF8"}], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Considering the disputes of Transformer-based forecasters, we reflect on why Transformers perform even worse than linear models in time series forecasting while acting predominantly in many other fields. We notice that the existing structure of Transformer-based forecasters may be not suitable for multivariate time series forecasting. As shown on the top of Figure 2 , it is notable that the points of the same time step that basically represent completely different physical meanings recorded by inconsistent measurements are embedded into one token with wiped-out multivariate correlations. And the token formed by a single time step can struggle to reveal beneficial information due to excessively local receptive field and time-unaligned events represented by simultaneous time points. Besides, while series variations can be greatly influenced by the sequence order, permutationinvariant attention mechanisms are improperly adopted on the temporal dimension (<PERSON><PERSON> et al., 2023) . Consequently, Transformer is weakened to capture essential series representations and portray multivariate correlations, limiting its capacity and generalization ability on diverse time series data.", "cite_spans": [{"start": 965, "end": 984, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}], "ref_spans": [{"start": 367, "end": 368, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Concerning the potential risks of embedding multivariate points of a timestamp as a (temporal) token, we take an inverted view on time series and embed the whole time series of each variate independently into a (variate) token, the extreme case of Patching (<PERSON><PERSON> et al., 2023) that enlarges local receptive field. By inverting, the embedded token aggregates the global representations of series that can be more variate-centric and better leveraged by booming attention mechanisms for multivariate correlating. Meanwhile, the feed-forward network can be proficient enough to learn generalizable representations for distinct variates encoded from arbitrary lookback series and decoded to predict future series.", "cite_spans": [{"start": 257, "end": 275, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "Based on the above motivations, we believe it is not that Transformer is ineffective for time series forecasting, but rather it is improperly used. In this paper, we revisit the structure of Transformer and advocate iTransformer as a fundamental backbone for time series forecasting. Technically, we embed each time series as variate tokens, adopt the attention for multivariate correlations, and employ the feed-forward network for series representations. Experimentally, the proposed iTransformer achieves state-of-the-art performance on real-world forecasting benchmarks shown in Figure 1 and surprisingly tackles the pain points of Transformer-based forecasters. Our contributions lie in three aspects:", "cite_spans": [], "ref_spans": [{"start": 590, "end": 591, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "", "sec_num": null}, {"text": "• We reflect on the architecture of Transformer and refine that the competent capability of native Transformer components on multivariate time series is underexplored. • We propose iTransformer that regards independent time series as tokens to capture multivariate correlations by self-attention and utilize layer normalization and feed-forward network modules to learn better series-global representations for time series forecasting. • Experimentally, iTransformer achieves comprehensive state-of-the-art on real-world benchmarks. We extensively analyze the inverted modules and architecture choices, indicating a promising direction for the future improvement of Transformer-based forecasters.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}, {"text": "With the progressive breakthrough made in natural language processing and computer vision areas, elaboratively designed Transformer variants are proposed to tackle ubiquitous time series forecasting applications. Going beyond contemporaneous TCNs (<PERSON> et al., 2018; <PERSON> et al., 2022a) and RNNbased forecasters (<PERSON> et al., 2017; <PERSON>nga<PERSON> et al., 2018; <PERSON><PERSON> et al., 2020) , Transformer has exhibited powerful sequence modeling capability and promising model scalability, leading to the trend of passionate modifications adapted for time series forecasting.", "cite_spans": [{"start": 247, "end": 265, "text": "(<PERSON> et al., 2018;", "ref_id": "BIBREF1"}, {"start": 266, "end": 284, "text": "<PERSON> et al., 2022a)", "ref_id": null}, {"start": 310, "end": 329, "text": "(<PERSON> et al., 2017;", "ref_id": "BIBREF34"}, {"start": 330, "end": 354, "text": "<PERSON><PERSON><PERSON> et al., 2018;", "ref_id": "BIBREF25"}, {"start": 355, "end": 376, "text": "<PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF26"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "Through a systematical review of Transformer-based forecasters, we conclude that existing modifications can be divided into four categories by whether to modify the component and architecture.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "As shown in Figure 3 , the first category (<PERSON> et al., 2021; <PERSON> et al., 2021; <PERSON> et al., 2022) , which is the most common practice, mainly concerns the component adaptation, especially the attention module for the temporal dependency modeling and the complexity optimization on long sequences. Nevertheless, with the rapid emergence of linear forecasters (<PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON> et al., 2023) , the impressive performance and efficiency continuously challenge this direction. Soon afterward, the second category attempts to fully utilize Transformer.", "cite_spans": [{"start": 42, "end": 59, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF29"}, {"start": 60, "end": 76, "text": "<PERSON> et al., 2021;", "ref_id": "BIBREF17"}, {"start": 77, "end": 95, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF35"}, {"start": 356, "end": 379, "text": "(<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF23"}, {"start": 380, "end": 398, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF32"}, {"start": 399, "end": 416, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF5"}, {"start": 417, "end": 434, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF21"}], "ref_spans": [{"start": 19, "end": 20, "text": "3", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "It pays more attention to the inherent processing of time series, such as Stationarization (<PERSON> et al., 2022b) , Channel Independence, and Patching (<PERSON><PERSON> et al., 2023) , which bring about consistently improved performance. Moreover, faced with the increasing significance of the independence and mutual interactions of multiple variates, the third category refurbishes Transformer in both aspects of component and architecture. Representative (Zhang & Yan, 2023 ) explicitly captures the cross-time and cross-variate dependencies by the renovated attention mechanism and architecture.", "cite_spans": [{"start": 91, "end": 110, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 148, "end": 166, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 442, "end": 460, "text": "(Zhang & Yan, 2023", "ref_id": "BIBREF33"}], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "Unlike previous works, iTransformer modifies none of the native components of Transformer. Instead, we adopt the components on the inverted dimensions with the altered architecture, as the only one that belongs to the fourth category to our best knowledge. We believe the capabilities of the components have stood the test extensively, the truth is that the architecture of Transformer is improperly adopted. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "RELATED WORK", "sec_num": "2"}, {"text": "In multivariate time series forecasting, given historical observations X = {x 1 , . . . , x T } ∈ R T ×N with T time steps and N variates, we predict the future S time steps Y = {x T +1 , . . . , x T +S } ∈ R S×N . For convenience, we denote X t,: as the simultaneously recorded time points at the step t, and X :,n as the whole time series of each variate indexed by n. It is notable that X t,: may not contain time points that essentially reflect the same event in real-world scenarios because of the systematical time lags among variates in the dataset. Besides, the elements of X t,: can be distinct from each other in physical measurements and statistical distributions, for which a variate X :,n generally shares.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ITRANSFORMER", "sec_num": "3"}, {"text": "Our proposed iTransformer illustrated in Figure 4 adopts the encoder-only architecture of Transformer (<PERSON><PERSON><PERSON><PERSON> et al., 2017) , including the embedding, projection, and Transformer blocks.", "cite_spans": [{"start": 102, "end": 124, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF28"}], "ref_spans": [{"start": 48, "end": 49, "text": "4", "ref_id": "FIGREF3"}], "eq_spans": [], "section": "STRUCTURE OVERVIEW", "sec_num": "3.1"}, {"text": "Embedding the whole series as the token Most Transformer-based forecasters typically regard multiple variates of the same time as the (temporal) token and follow the generative formulation of forecasting tasks. However, we find the approach on the numerical modality can be less instructive for learning attention maps, which is supported by increasing applications of Patching (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON> et al., 2023) that broadens the respective field. Meanwhile, the triumph of linear forecasters also challenges the necessity of adopting a heavy encoder-decoder Transformer for generating tokens. Instead, our proposed encoder-only iTransformer focuses on representation learning and adaptive correlating of multivariate series. Each time series driven by the underlying complicated process is firstly tokenized to describe the properties of the variate, applied by self-attention for mutual interactions, and individually processed by feed-forward networks for series representations. Notably, the task to generate the predicted series is essentially delivered to linear layers, which has been proven competent by previous work (<PERSON> et al., 2023) and we provide a detailed analysis in the next section.", "cite_spans": [{"start": 378, "end": 404, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF7"}, {"start": 405, "end": 422, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 1137, "end": 1155, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "STRUCTURE OVERVIEW", "sec_num": "3.1"}, {"text": "Based on the above considerations, in iTransformer, the process of predicting future series of each specific variate Ŷ:,n based on the lookback series X :,n is simply formulated as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "STRUCTURE OVERVIEW", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "h 0 n = Embedding(X :,n ), H l+1 = TrmBlock(H l ), l = 0, . . . , L -1, Ŷ:,n = Projection(h L n ),", "eq_num": "(1)"}], "section": "STRUCTURE OVERVIEW", "sec_num": "3.1"}, {"text": "where H = {h 1 , . . . , h N } ∈ R N ×D contains N embedded tokens of dimension D and the superscript denotes the layer index. Embedding : R T → R D and Projection : R D → R S are both implemented by multi-layer perceptron (MLP). The obtained variate tokens interact with each other by self-attention and are independently processed by the shared feed-forward network in each TrmBlock. Specifically, as the order of sequence is implicitly stored in the neuron permutation of the feed-forward network, the position embedding in the vanilla Transformer is no longer needed here.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "STRUCTURE OVERVIEW", "sec_num": "3.1"}, {"text": "iTransformers The architecture essentially presupposes no more specific requirements on Transformer variants, other than the attention is applicable for multivariate correlation. Thus, a bundle of efficient attention mechanisms (<PERSON> et al., 2021; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2022 ) can be the plugins, reducing the complexity when the variate number grows large. Besides, with the input flexibility of attention, the token number can vary from training to inference, and the model is allowed to be trained on arbitrary numbers of variates. The inverted Transformers, named iTransformers, are extensively evaluated in experiments of Section 4.2 and demonstrate advantages on time series forecasting.", "cite_spans": [{"start": 228, "end": 245, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF17"}, {"start": 246, "end": 262, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF30"}, {"start": 263, "end": 279, "text": "<PERSON><PERSON> et al., 2022", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "STRUCTURE OVERVIEW", "sec_num": "3.1"}, {"text": "We organize a stack of L blocks composed of the layer normalization, feed-forward network, and self-attention modules. But their duties on the inverted dimension are carefully reconsidered.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INVERTED TRANSFORMER COMPONENTS", "sec_num": "3.2"}, {"text": "Layer normalization Layer normalization (<PERSON> et al., 2016) is originally proposed to increase the convergence and training stability of deep networks. In typical Transformer-based forecasters, the module normalizes the multivariate representation of the same timestamp, gradually fusing the variates with each other. Once the collected time points do not represent the same event, the operation will also introduce interaction noises between noncausal or delayed processes. In our inverted version, the normalization is applied to the series representation of individual variate as Equation 2, which has been studied and proved effective in tackling non-stationary problems (<PERSON> et al., 2021; <PERSON> et al., 2022b) . Besides, since all series as (variate) tokens are normalized to a Gaussian distribution, the discrepancies caused by inconsistent measurements can be diminished. By contrast, in previous architecture, different tokens of time steps will be normalized, leading to oversmooth time series.", "cite_spans": [{"start": 40, "end": 57, "text": "(<PERSON> et al., 2016)", "ref_id": "BIBREF0"}, {"start": 673, "end": 691, "text": "(<PERSON> et al., 2021;", "ref_id": "BIBREF12"}, {"start": 692, "end": 710, "text": "<PERSON> et al., 2022b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "INVERTED TRANSFORMER COMPONENTS", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "LayerNorm(H) = h n -Mean(h n ) Var(h n ) n = 1, . . . , N", "eq_num": "(2)"}], "section": "INVERTED TRANSFORMER COMPONENTS", "sec_num": "3.2"}, {"text": "Feed-forward network Transformer adopts the feed-forward network (FFN) as the basic building block for encoding token representation and it is identically applied to each token. As aforementioned, in the vanilla Transformer, multiple variates of the same timestamp that form the token can be malpositioned and too localized to reveal enough information for predictions. In the inverted version, FFN is leveraged on the series representation of each variate token. By the universal approximation theorem (<PERSON><PERSON>, 1991) , they can extract complicated representations to describe a time series. With the stacking of inverted blocks, they are devoted to encoding the observed time series and decoding the representations for future series using dense non-linear connections, which work effectively as the recent works completely built on MLPs (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2023) .", "cite_spans": [{"start": 503, "end": 517, "text": "(<PERSON><PERSON>, 1991)", "ref_id": "BIBREF10"}, {"start": 839, "end": 864, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF27"}, {"start": 865, "end": 882, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF5"}], "ref_spans": [], "eq_spans": [], "section": "INVERTED TRANSFORMER COMPONENTS", "sec_num": "3.2"}, {"text": "More interestingly, the identical linear operation on independent time series, which serves as the combination of the recent linear forecasters (<PERSON><PERSON> et al., 2023) and Channel Independence (<PERSON><PERSON> et al., 2023) , can be instructive for us to understand the series representations. Recent revisiting on linear forecasters (<PERSON> et al., 2023) highlights that temporal features extracted by MLPs are supposed to be shared within distinct time series. We propose a rational explanation that the neurons of MLP are taught to portray the intrinsic properties of any time series, such as the amplitude, periodicity, and even frequency spectrums (neuron as a filter), serving as a more advantageous predictive representation learner than the self-attention applied on time points. Experimentally, we validate that the division of labor helps enjoy the benefits of linear layers in Section 4.3, such as the promoted performance if providing enlarged lookback series, and the generalization ability on unseen variates.", "cite_spans": [{"start": 144, "end": 163, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}, {"start": 189, "end": 207, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 318, "end": 335, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "INVERTED TRANSFORMER COMPONENTS", "sec_num": "3.2"}, {"text": "Self-attention While the attention mechanism is generally adopted for facilitating the temporal dependencies modeling in previous forecasters, the inverted model regards the whole series of one variate as an independent process. Concretely, with comprehensively extracted representations of each time series H = {h 0 , . . . , h N } ∈ R N ×D , the self-attention module adopts linear projections to get queries, keys, and values Q, K, V ∈ R N ×d k , where d k is the projected dimension.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INVERTED TRANSFORMER COMPONENTS", "sec_num": "3.2"}, {"text": "With denotation of q i , k j ∈ R d k as the specific query and key of one (variate) token, we notice that each entry of the pre-Softmax scores is formulated as", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INVERTED TRANSFORMER COMPONENTS", "sec_num": "3.2"}, {"text": "A i,j = (QK ⊤ / √ d k ) i,j ∝ q ⊤ i k j .", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INVERTED TRANSFORMER COMPONENTS", "sec_num": "3.2"}, {"text": "Since each token is previously normalized on its feature dimension, the entries can somewhat reveal the variate-wise correlation, and the whole score map A ∈ R N ×N exhibits the multivariate correlations between paired variate tokens. Consequently, highly correlated variate will be more weighted for the next representation interaction with values V. Based on this intuition, the proposed mechanism is believed to be more natural and interpretable for multivariate series forecasting. We further provide the visualization analysis of the score map in Section 4.3 and Appendix E.1.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INVERTED TRANSFORMER COMPONENTS", "sec_num": "3.2"}, {"text": "We thoroughly evaluate the proposed iTransformer on various time series forecasting applications, validate the generality of the proposed framework and further dive into the effectiveness of applying the Transformer components on the inverted dimensions of time series.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "EXPERIMENTS", "sec_num": "4"}, {"text": "We extensively include 7 real-world datasets in our experiments, including ECL, ETT (4 subsets), Exchange, Traffic, Weather used by Autoformer (<PERSON> et al., 2021) , Solar-Energy datasets proposed in LSTNet (<PERSON> et al., 2018) , and PEMS (4 subsets) evaluated in SCINet (<PERSON> et al., 2022a) . We also provide the experiments on Market (6 subsets) in Appendix F.4. It records the minutesampled server load of Alipay online transaction application with hundreds of variates, where we consistently outperform other baselines. Detailed dataset descriptions are provided in Appendix A.1.", "cite_spans": [{"start": 143, "end": 160, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF29"}, {"start": 204, "end": 222, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF16"}, {"start": 266, "end": 285, "text": "(<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "Datasets", "sec_num": null}, {"text": "In this section, we conduct extensive experiments to evaluate the forecasting performance of our proposed model together with advanced deep forecasters.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "FORECASTING RESULTS", "sec_num": "4.1"}, {"text": "Baselines We carefully choose 10 well-acknowledged forecasting models as our benchmark, including (1) Transformer-based methods: Autoformer (<PERSON> et al., 2021) , FEDformer (<PERSON> et al., 2022) , Stationary (<PERSON> et al., 2022b) , Crossformer (Zhang & Yan, 2023) , PatchTST (<PERSON><PERSON> et al., 2023) ;", "cite_spans": [{"start": 140, "end": 157, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF29"}, {"start": 170, "end": 189, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF35"}, {"start": 203, "end": 222, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 237, "end": 256, "text": "(<PERSON> & Yan, 2023)", "ref_id": "BIBREF33"}, {"start": 268, "end": 286, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "FORECASTING RESULTS", "sec_num": "4.1"}, {"text": "(2) Linear-based methods: DLinear (<PERSON><PERSON> et al., 2023) , TiDE (<PERSON> et al., 2023) , RLinear (<PERSON> et al., 2023) ; and (3) TCN-based methods: SCINet (<PERSON> et al., 2022a) , TimesNet (<PERSON> et al., 2023) .", "cite_spans": [{"start": 34, "end": 53, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}, {"start": 61, "end": 79, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF5"}, {"start": 90, "end": 107, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF18"}, {"start": 144, "end": 163, "text": "(<PERSON> et al., 2022a)", "ref_id": null}, {"start": 175, "end": 192, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF31"}], "ref_spans": [], "eq_spans": [], "section": "FORECASTING RESULTS", "sec_num": "4.1"}, {"text": "Comprehensive forecasting results are listed in Table 1 with the best in red and the second underlined. The lower MSE/MAE indicates the more accurate prediction result. Compared with other forecasters, iTransformer is particularly good at forecasting high-dimensional time series. Besides, PatchTST as the previous state-of-the-art, fails in many cases of PEMS, which can stem from the extremely fluctuating series of the dataset, and the patching mechanism of PatchTST may lose focus on specific locality to handle rapid fluctuation. By contrast, the proposed model aggregating the whole series variations for series representations can better cope with this situation. Notably, as the representative that explicitly captures multivariate correlations, the performance of Crossformer is still subpar to iTransformer, indicating the interaction of time-unaligned patches from different multivariate will bring about unnecessary noise for forecasting. Therefore, the native Transformer components are competent for temporal modeling and multivariate correlating, and the proposed inverted architecture can effectively tackle real-world time series forecasting scenarios. ", "cite_spans": [], "ref_spans": [{"start": 54, "end": 55, "text": "1", "ref_id": "TABREF0"}], "eq_spans": [], "section": "Main results", "sec_num": null}, {"text": "In this section, we evaluate iTransformers by applying our framework to Transformer and its variants, which generally address the quadratic complexity of the self-attention mechanism, including Reformer (<PERSON><PERSON><PERSON> et al., 2020) , Informer (<PERSON> et al., 2021) , Flowformer (<PERSON> et al., 2022) and FlashAttention (<PERSON><PERSON> et al., 2022) . Surprising and promising discoveries are exhibited, indicating the simple inverted perspective can enhance Transformer-based forecasters with promoted performance with efficiency, generalization on unseen variates, and better utilization of historical observations.", "cite_spans": [{"start": 203, "end": 224, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF14"}, {"start": 236, "end": 253, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF17"}, {"start": 267, "end": 284, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF30"}, {"start": 304, "end": 322, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF4"}], "ref_spans": [], "eq_spans": [], "section": "ITRANSFORMERS GENERALITY", "sec_num": "4.2"}, {"text": "Performance promotion We evaluate Transformers and the corresponding iTransformers with the reported performance promotions in the attention mechanism is adopted on the variate dimension in our inverted structure, the introduction of efficient attentions with linear complexity essentially addresses the computational problem due to numerous variates, which is prevalent in real-world applications but can be resource-consuming for Channel Independence (<PERSON><PERSON> et al., 2023) . Therefore, the idea of iTransformer can be widely practiced on Transformer-based forecasters to take advantage of booming efficient attention mechanisms. Variate generalization By inverting vanilla Transformers, it is notable that the models are empowered with the generalization capability on unseen variates. Firstly, benefiting from the flexibility of the number of input tokens, the amount of variate channels is no longer restricted and thus feasible to vary from training and inference. Besides, feed-forward networks are identically applied on independent variate tokens in iTransformer. As aforementioned, the neurons as filters learn the intrinsic patterns of any time series, which are inclined to be shared and transferable among distinct variates.", "cite_spans": [{"start": 453, "end": 471, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "ITRANSFORMERS GENERALITY", "sec_num": "4.2"}, {"text": "To verify the hypothesis, we compare inverting with another generalizing strategy: Channel Independence, training a shared backbone to forecast all variates. We partition the variates of each dataset into five folders, train models with only 20% of variates of one folder, and directly forecast all variates without fine-tuning. We compare the performance in Figure 5 and each bar presents the averaged results of all folders to avoid the randomness of partition Figure 5 : Performance of generalization on unseen variates. We partition the variates of each dataset into five folders, train models with 20% variates, and use the partially trained model to forecast all varieties. iTransformers can be trained efficiently and forecast with good generalizability.", "cite_spans": [], "ref_spans": [{"start": 366, "end": 367, "text": "5", "ref_id": null}, {"start": 470, "end": 471, "text": "5", "ref_id": null}], "eq_spans": [], "section": "ITRANSFORMERS GENERALITY", "sec_num": "4.2"}, {"text": "Increasing lookback length Previous works have witnessed the phenomenon that the forecasting performance does not necessarily improve with the increase of lookback length on Transformers (<PERSON><PERSON> et al., 2023; <PERSON><PERSON> et al., 2023) , which can be attributed to the distracted attention on the growing input. However, the desired performance improvement is generally held on linear forecasts, theoretically supported by statistical methods (Box & Jenkins, 1968 ) with enlarged historical information to be utilized. As the working dimensions of attention and feed-forward network are inverted, we evaluate the performance of Transformers and iTransformer in Figure 6 with increased lookback length. The results surprisingly verify the rationality of leveraging MLPs on the temporal dimension such that Transformers can benefit from the extended lookback window for more precise predictions. ", "cite_spans": [{"start": 187, "end": 205, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF22"}, {"start": 206, "end": 224, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}, {"start": 432, "end": 452, "text": "(Box & Jenkins, 1968", "ref_id": "BIBREF2"}], "ref_spans": [{"start": 657, "end": 658, "text": "6", "ref_id": null}], "eq_spans": [], "section": "ITRANSFORMERS GENERALITY", "sec_num": "4.2"}, {"text": "Ablation study To verify the rational business of Transformer components, we provide detailed ablations covering both replacing components (Replace) and removing components (w/o) experiments.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "MODEL ANALYSIS", "sec_num": "4.3"}, {"text": "The results are listed in Table 3 . iTransformer that utilizes attention on the variate dimension and feed-forward on the temporal dimension generally achieves the best performance. Notably, the performance of vanilla Transformer (the third row) performs the worst among these designs, revealing the potential risks of the conventional architecture, which we describe in detail in Appendix E.3. Analysis of series representations To further validate the claim that feed-forward networks are more favored to extract the series representations. We conduct representation analysis based on the centered kernel alignment (CKA) similarity (<PERSON><PERSON><PERSON> et al., 2019) . A higher CKA indicates more similar representations. For Transformer variants and iTransformers, we calculate the CKA between the output features of the first and the last block. Notably, previous works have demonstrated that time series forecasting, as a low-level generative task, prefers the higher CKA similarity (<PERSON> et al., 2023; <PERSON> et al., 2023) for the better performance. As shown in Figure 7 , a clear division line is exhibited, implying that iTransformers have learned more appropriate series representations by inverting the dimension and thus achieve more accurate predictions. The results also advocate inverting Transformer deserves a fundamental renovation of the forecasting backbone.", "cite_spans": [{"start": 634, "end": 658, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF15"}, {"start": 978, "end": 995, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF31"}, {"start": 996, "end": 1014, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF6"}], "ref_spans": [{"start": 32, "end": 33, "text": "3", "ref_id": "TABREF5"}, {"start": 1062, "end": 1063, "text": "7", "ref_id": null}], "eq_spans": [], "section": "MODEL ANALYSIS", "sec_num": "4.3"}, {"text": "Analysis of multivariate correlations By assigning the duty of multivariate correlation to the attention mechanism, the learned map enjoys enhanced interpretability. We present the case visualization on series from Solar-Energy in Figure 7 , which has distinct correlations in the lookback and future windows. It can be observed that in the shallow attention layer, the learned map shares lots of similarities to the correlations of raw input series. As it dives into deeper layers, the learned map become gradually alike to the correlations of future series, which validates the inverted operation empowers interpretable attention for correlating, and the processes of encoding the past and decoding for the future are essentially conducted in series representations during feed-forwarding. ", "cite_spans": [], "ref_spans": [{"start": 238, "end": 239, "text": "7", "ref_id": null}], "eq_spans": [], "section": "MODEL ANALYSIS", "sec_num": "4.3"}, {"text": "Considering the characteristics of multivariate time series, we propose iTransformer that inverts the structure of Transformer without modifying any native modules. iTransformer regards independent series as variate tokens to capture multivariate correlations by attention and utilize layer normalization and feed-forward networks to learn series representations. Experimentally, iTransformer achieves state-of-the-art performance and exhibits remarkable framework generality supported by promising analysis. In the future, we will explore large-scale pre-training and more time series analysis tasks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "CONCLUSION AND FUTURE WORK", "sec_num": "5"}, {"text": "Our work only focuses on the time series forecasting problem, so there is no potential ethical risk.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ETHICS STATEMENT", "sec_num": "6"}, {"text": "In the main text, we have strictly formalized the model architecture with equations. All the implementation details are included in the Appendix, including dataset descriptions, metrics, model, and experiment configurations. The code will be made public once the paper is accepted.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "REPRODUCIBILITY STATEMENT", "sec_num": "7"}, {"text": "A.1 DATASET DESCRIPTIONS", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "We conduct experiments on 7 real-world datasets to evaluate the performance of the proposed iTransformer including (1) ETT (<PERSON> et al., 2021) 7) PEMS contains the public traffic network data in California collected by 5-minute windows. We use the same four public subsets (PEMS03, PEMS04, PEMS07, PEMS08) adopted in SCINet (<PERSON> et al., 2022a) .", "cite_spans": [{"start": 123, "end": 140, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF17"}, {"start": 322, "end": 341, "text": "(<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "A IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "Apart from the public datasets widely used as forecasting benchmarks, we also collect a set of Market datasets of a real-world application, which records the minute-sampled server load of Alipay online transactions between January 30th, 2023, and April 9th, 2023 with the number of variates varied from 285 to 759. It includes 6 sub-datasets, which are divided according to diverse transaction domains.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "We follow the same data processing and train-validation-test set split protocol used in TimesNet (<PERSON> et al., 2023) , where the train, validation, and test datasets are strictly divided according to chronological order to make sure there are no data leakage issues. As for the forecasting settings, we fix the length of the lookback series as 96 in ETT, Weather, ECL, Solar-Energy, PEMS, and Traffic, and the prediction length varies in {96, 192, 336, 720}. For the PEMS dataset, the prediction length varies in {12, 24, 36, 48}, which is the same as SCINet, the previous state-of-the-art on this dataset. For the Market dataset, the lookback contains the past one day observations with 144 time points and the forecasting length varies in {12, 24, 72, 144}. The details of datasets are provided in Table 4 .", "cite_spans": [{"start": 97, "end": 114, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF31"}], "ref_spans": [{"start": 804, "end": 805, "text": "4", "ref_id": "TABREF7"}], "eq_spans": [], "section": "A IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "Algorithm 1 iTransformer -Overall Architecture.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "Require: Input lookback time series X ∈ R T ×N ; input Length T ; predicted length S; variates number N ; token dimension D; iTransformer block number L.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "1: X = X.transpose ▷ X ∈ R N ×T", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "2: ▷ Multi-layer Perceptron works on the last dimension to embed series into variate tokens.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "3: H 0 = MLP(X) ▷ H 0 ∈ R N ×D", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "4: for l in {1, . . . , L}: ▷ Run through iTransformer blocks.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "5: for ▷ Self-attention layer is applied on variate tokens.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "6: for H l-1 = LayerNorm H l-1 + Self-Attn(H l-1 ) ▷ H l-1 ∈ R N ×D 7: for ▷ Feed-forward network is utilized for series representations, broadcasting to each token.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "8: for", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "H l = LayerNorm H l-1 + Feed-Forward(H l-1 ) ▷ H l ∈ R N ×D 9:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "for ▷ LayerNorm is adopted on series representations to reduce variates discrepancies. All the experiments are implemented in PyTorch (<PERSON><PERSON><PERSON> et al., 2019) and conducted on a single NVIDIA P100 16GB GPU. We utilize ADAM (Kingma & Ba, 2015) with an initial learning rate in {10 -3 , 5 × 10 -4 , 10 -4 } and L2 loss for the model optimization. The batch size is uniformly set to 32 and the number of training epochs is fixed to 10. We set the number of inverted Transformer blocks in our proposed model L ∈ {2, 3, 4}. The dimension of series representations D is set from {256, 512}.", "cite_spans": [{"start": 134, "end": 155, "text": "(<PERSON><PERSON><PERSON> et al., 2019)", "ref_id": "BIBREF24"}, {"start": 220, "end": 239, "text": "(Kingma & Ba, 2015)", "ref_id": "BIBREF13"}], "ref_spans": [], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "All the compared baseline models that we reproduced are implemented based on the benchmark of TimesNet (Wu et al., 2023) Repository, which is fairly built on the configurations provided by each model's original paper or official code. We provide the pseudo-code of iTransformer in Algorithm 1. We also report the standard deviation of iTransformer performance under five runs with different random seeds in Table 5 , which exhibits that the performance of iTransformer is stable. ", "cite_spans": [{"start": 103, "end": 120, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF31"}], "ref_spans": [{"start": 413, "end": 414, "text": "5", "ref_id": "TABREF8"}], "eq_spans": [], "section": "A.2 IMPLEMENTATION DETAILS", "sec_num": null}, {"text": "To elaborate on the rational business of Transformer components, we conduct detailed ablations covering replacing components (Replace) and removing components (w/o). Since the average results are listed in Table 3 due to the paper limit, we provide detailed results and analysis here.", "cite_spans": [], "ref_spans": [{"start": 212, "end": 213, "text": "3", "ref_id": "TABREF5"}], "eq_spans": [], "section": "B ABLATION STUDIES", "sec_num": null}, {"text": "As shown in Table 6 , among various architectural designs, iTransformer generally exhibits superior performance, which learns multivariate correlations by self-attention and encodes series representations by FFN. Nevertheless, the arrangement of the vanilla Transformer can lead to degenerated performance, indicating the misuse of Transformer components on the time series modality. Based on the relatively poor results of the second (both attentions) and the third (the vanilla Transformer) designs, one of the reasons for that may lie in the attention module over the temporal tokens of the lagged time series, which we elaborate more with the datasets support in Section E.3.", "cite_spans": [], "ref_spans": [{"start": 18, "end": 19, "text": "6", "ref_id": "TABREF9"}], "eq_spans": [], "section": "B ABLATION STUDIES", "sec_num": null}, {"text": "It is also notable that applying FFN on both dimensions can also lead to fair performance on datasets with small variate numbers (such as Weather with 21 variates). Still, with the increasing of variate numbers in challenging multivariate forecasting tasks, the importance of capturing multivariate correlations is ever more highlighted. We note that the heterogeneity of variates can be hardly considered by the vanilla Transformer. During embedding, the variates are projected into indistinguishable channels, which ignores the inconsistent physical measurements and thus fails to maintain the independence of variates, let alone capture and utilize the multivariate correlation. Consequently, by incorporating the advanced attention module for the variate correlating, the first (iTransformer) and the fifth (attention on variates) designs perform more effectively in challenging multivariate datasets.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B ABLATION STUDIES", "sec_num": null}, {"text": "In a nutshell, both temporal dependencies and multivariate correlations are of importance for multivariate time series forecasting. The proposed iTransformer employing the self-attention module to disentangle the correlations between variate tokens proves to be more powerful and interpretable than feed-forward networks, thereby further boosting the performance on challenging multivariate datasets and enhancing the model capacity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "B ABLATION STUDIES", "sec_num": null}, {"text": "We evaluate the hyperparameter sensitivity of iTransformer with respect to the following factors: the learning rate lr, the number of Transformer blocks L, and the hidden dimension D of variate tokens.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C HYPERPARAMETER SENSITIVITY", "sec_num": null}, {"text": "The results are shown in Figure 9 . We find that the learning rate, as the most common influencing factor, should be carefully selected when the number of variates is large (ECL, Traffic) . The block number and hidden dimension are not essentially favored to be as large as possible in iTransformer. ", "cite_spans": [{"start": 173, "end": 187, "text": "(ECL, Traffic)", "ref_id": null}], "ref_spans": [{"start": 32, "end": 33, "text": "9", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "C HYPERPARAMETER SENSITIVITY", "sec_num": null}, {"text": "We comprehensively compare the forecasting performance, training speed, and memory footprint of the following models: iTransformer, iTransformer with our efficient training strategy and iTransformer with the efficient flow attention module (<PERSON> et al., 2022) ; linear models: DLinear (<PERSON><PERSON> et al., 2023) and TiDE (<PERSON> et al., 2023) ; Transformers: Transformer (<PERSON><PERSON><PERSON><PERSON> et al., 2017) , PatchTST (<PERSON><PERSON> et al., 2023) , and Crossformer (Zhang & Yan, 2023) . The results are recorded with the official model configuration and the same batch size. In Figure 10 , we compare the efficiency under two representative datasets (21 variates in Weather and 862 in Traffic) with 96 time steps for lookback.", "cite_spans": [{"start": 240, "end": 257, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF30"}, {"start": 283, "end": 302, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}, {"start": 312, "end": 330, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF5"}, {"start": 359, "end": 381, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF28"}, {"start": 393, "end": 411, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 430, "end": 449, "text": "(<PERSON> & Yan, 2023)", "ref_id": "BIBREF33"}], "ref_spans": [{"start": 550, "end": 552, "text": "10", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "D MODEL EFFICIENCY", "sec_num": null}, {"text": "In a nutshell, the efficiency of iTransformer exceeds other Transformers in datasets with a relatively small number of variates (Weather). In datasets with numerous variates (Traffic), the memory footprints are basically the same as Transformers variates, but iTransformer can be trained faster. Based on the complexity of O(N 2 ) of the attention module, where N is the number of tokens, Transformer surpasses iTransformer on efficiency in this case because of N = 96 for the temporal token and N = 862 for the variate token. Meanwhile, iTransformer achieves better performance on numerous variates, since the multivariate correlations can be explicitly utilized. By adopting a linear-complexity attention (<PERSON> et al., 2022) or the proposed efficient training strategy as mentioned in Figure 8 (trained on 20% variates and forecast all variates), iTransformer can enjoy a comparable speed and memory footprint with linear models. Also, the two strategies can be adopted together.", "cite_spans": [{"start": 707, "end": 724, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF30"}], "ref_spans": [{"start": 792, "end": 793, "text": "8", "ref_id": null}], "eq_spans": [], "section": "D MODEL EFFICIENCY", "sec_num": null}, {"text": "By using the attention mechanism on variate tokens, the resulting learned map becomes more interpretable. To present an intuitive understanding of the multivariate correlations, we provide three randomly chosen case visualizations of the time series from Solar-Energy in Figure 11 . We provide the Pearson Correlation coefficients of each variate of the raw series by the following equation:", "cite_spans": [], "ref_spans": [{"start": 278, "end": 280, "text": "11", "ref_id": "FIGREF7"}], "eq_spans": [], "section": "E SHOWCASES E.1 VISUALIZATION OF MULTIVARIATE CORRELATIONS", "sec_num": null}, {"text": "ρ xy = i (x i -x)(y i -ȳ) i (x i -x) 2 i (y i -ȳ) 2", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E SHOWCASES E.1 VISUALIZATION OF MULTIVARIATE CORRELATIONS", "sec_num": null}, {"text": ", where x i , y i ∈ R run through all time points of the paired variates to be correlated. All the cases have distinct multivariate correlations in the lookback and forecast window because the dataset exhibits obvious seasonal changes in the daytime and night. On the second row of each case, we provide the learned pre-Softmax maps of the self-attention module in both the first and the last layers. As we observe in the shallow attention layer (left), we find that the learned map is similar to the correlations of the raw lookback series. As we go deeper into the layers (right), the learned map gradually becomes more similar to the correlations of the future series to be predicted. This demonstrates that the inverted operation allows for interpretable attention in correlating, and that encoding of the past and decoding for the future are conducted series representations during layer stacking. We present another interesting observation in Figure 12 to show that the attention module of iTransformer has enhanced interpretability. We provide randomly chosen multivariate time series from Market. In this dataset, each variate represents the monitored values of a service interface of a kind, and the service can be further grouped into refined application categories. We divide these variates into corresponding applications (as listed on the top bar App), such that adjacent variates belong to the same application and we reveal the application index by the top bar.", "cite_spans": [], "ref_spans": [{"start": 956, "end": 958, "text": "12", "ref_id": "FIGREF8"}], "eq_spans": [], "section": "E SHOWCASES E.1 VISUALIZATION OF MULTIVARIATE CORRELATIONS", "sec_num": null}, {"text": "We visualize the time series of the variates and plot the learned multivariate correlations with the marks of specific correlations between variates. On the one hand, we observe clear partitioning in the multivariate correlations map, indicating the grouping of variates. On the one hand, the marked correlation values can reflect the correlation of the raw series, where the similarity of variates from the same application becomes closer than the pairs from the different groups. Therefore, highly correlated variate will be leveraged for the next interaction and thus benefit for multivariate forecasting. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E SHOWCASES E.1 VISUALIZATION OF MULTIVARIATE CORRELATIONS", "sec_num": null}, {"text": "To provide a clear comparison among different models, we list supplementary prediction showcases of four representative datasets in Figures 13 14 15 16 , which are given by the following models: iTransfomrer, PatchTST (<PERSON><PERSON> et al., 2023) , DLinear (<PERSON><PERSON> et al., 2023) , Crossformer (Zhang & Yan, 2023) , Autoformer (<PERSON> et al., 2021) , Transformer (<PERSON><PERSON><PERSON><PERSON> et al., 2017) . Among the various models, iTransformer predicts the most precise future series variations and exhibits superior performance. ", "cite_spans": [{"start": 218, "end": 236, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 247, "end": 266, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF32"}, {"start": 281, "end": 300, "text": "(<PERSON> & Yan, 2023)", "ref_id": "BIBREF33"}, {"start": 314, "end": 331, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF29"}, {"start": 346, "end": 368, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF28"}], "ref_spans": [{"start": 140, "end": 142, "text": "13", "ref_id": "FIGREF9"}, {"start": 143, "end": 145, "text": "14", "ref_id": "FIGREF3"}, {"start": 146, "end": 148, "text": "15", "ref_id": "FIGREF0"}, {"start": 149, "end": 151, "text": "16", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "E.2 VISUALIZATION OF PREDICTION RESULTS", "sec_num": null}, {"text": "As aforementioned, the embedding approach of the previous Transformer fuses multiple variates representing potentially delayed events and distinct physical measurements, which may fail to learn variate-centric representations and result in meaningless attention maps. We provide the visualization case of Traffic (<PERSON> et al., 2022a) , which is collected from sensors on Los Angeles city roads in different areas. As shown in Figure 17 , we can observe a strong correlation between the multivariate time series of the dataset, while they also exhibit obvious phase offset, which is due to the systematical time lags in the road occupancy that each series describes. Since the sensors are installed in different areas of the highway, an event (such as a traffic jam) can affect road occupancy with different delays. Besides, we observe the significantly declined performance on the second and third designs of Traffic in Table 6 , which apply attention to temporal tokens. In our opinion, capturing temporal dependencies by attention is not a big problem. But it is based on the fact that the time points of each timestamp essentially reflect the same event to enclose a semantic representation. Since there are inherent delays between the time points, the performance can degrade a lot because of the meaningless attention map, unless the model has an enlarged respective field to learn about the decay or causal process.", "cite_spans": [{"start": 313, "end": 332, "text": "(<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [{"start": 432, "end": 434, "text": "17", "ref_id": "FIGREF11"}, {"start": 925, "end": 926, "text": "6", "ref_id": "TABREF9"}], "eq_spans": [], "section": "E.3 RISKS OF EMBEDDING M<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> POINTS OF A TIMESTAMP", "sec_num": null}, {"text": "Other risks can be aroused from the distinct variate measurements, such as organizing together different meteorological indicators (the temperature and rainfall) in the Weather dataset (<PERSON> et al., 2021) , and the quantity and proportion of the same observation in ILI (<PERSON> et al., 2023) . Given these potential risks, iTransformer proposes a new paradigm that embeds the whole series as the variate token, which can be more robust to extensive real-world scenarios, such as delayed events, inconsistent measurements, irregular (unevenly spaced) time series, systematical delay of monitors, and the time interval of generating and recording different time series.", "cite_spans": [{"start": 185, "end": 202, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF29"}, {"start": 268, "end": 285, "text": "(<PERSON> et al., 2023)", "ref_id": "BIBREF31"}], "ref_spans": [], "eq_spans": [], "section": "E.3 RISKS OF EMBEDDING M<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> POINTS OF A TIMESTAMP", "sec_num": null}, {"text": "F FULL RESULTS", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E.3 RISKS OF EMBEDDING M<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> POINTS OF A TIMESTAMP", "sec_num": null}, {"text": "We compare the performance of Transformer and iTransformer on all datasets in Table 7 . Consistent and great promotions can be achieved, indicating that the attention and feed-forward network on the inverted dimensions greatly empower Transformers in multivariate time series forecasting, leaving an instructive direction to build up the foundation model of extensive time series data. ", "cite_spans": [], "ref_spans": [{"start": 84, "end": 85, "text": "7", "ref_id": "TABREF10"}], "eq_spans": [], "section": "F.1 FULL PROMOTION RESULTS", "sec_num": null}, {"text": "We apply the proposed inverting framework to Transformer and its variants: Transformer (<PERSON><PERSON><PERSON><PERSON> et al., 2017) , Reformer (<PERSON><PERSON><PERSON> et al., 2020) , Informer (<PERSON> et al., 2021) , Flowformer (<PERSON> et al., 2022) , Flashformer (<PERSON><PERSON> et al., 2022) . The averaged results are shown in Table 2 due to the limited pages. We provide the supplementary forecasting results in Table 8 . The results demonstrate that our iTransformers framework can consistently promote these Transformer variants, and take advantage of the booming efficient attention mechanisms. ", "cite_spans": [{"start": 87, "end": 109, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF28"}, {"start": 121, "end": 142, "text": "(<PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF14"}, {"start": 154, "end": 171, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF17"}, {"start": 185, "end": 202, "text": "(<PERSON> et al., 2022)", "ref_id": "BIBREF30"}, {"start": 217, "end": 235, "text": "(<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF4"}], "ref_spans": [{"start": 278, "end": 279, "text": "2", "ref_id": "TABREF1"}, {"start": 364, "end": 365, "text": "8", "ref_id": "TABREF11"}], "eq_spans": [], "section": "F.2 FULL FRA<PERSON><PERSON>ORK GENERALITY RESULTS", "sec_num": null}, {"text": "We divide the variates of each dataset into five folders, train models with only 20% of variates of one folder, and directly forecast all variates without fine-tuning. We adopt two strategies for Transformers to generalize on unseen variates: (1) CI-Transformers (<PERSON><PERSON> et al., 2023) : Channel Independence regards each variate of time series as independent channels, and trains with a shared backbone. During inference, the model predicts variates one by one, but the procedure can be time-consuming. (2) iTransformers: with the flexibility of the attention mechanism that the number of input tokens can be dynamically changeable, the amount of variates as tokens is no longer restricted and thus feasible to vary from training and inference, and can even allow the model to be trained on arbitrary variates.", "cite_spans": [{"start": 263, "end": 281, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}], "ref_spans": [], "eq_spans": [], "section": "F.3 FULL RESULTS OF VARIATE GENERALIZATION", "sec_num": null}, {"text": "As shown in Table 18 , iTransformers can be naturally trained with 20% variates and accomplish forecast on all variates with the ability to learn transferable representations.", "cite_spans": [], "ref_spans": [{"start": 18, "end": 20, "text": "18", "ref_id": "TABREF11"}], "eq_spans": [], "section": "F.3 FULL RESULTS OF VARIATE GENERALIZATION", "sec_num": null}, {"text": "Figure 18 : Full performance of generalization on unseen variates, comparing the iTransformers with CI-Transfomers. We divide the variates of each dataset into five folders, train with 20% variates, and use the trained model to forecast all varieties. We plot the averaged results of all five folders.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 9, "text": "18", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "F.3 FULL RESULTS OF VARIATE GENERALIZATION", "sec_num": null}, {"text": "The full multivariate forecasting results are provided in the following section due to the space limitation of the main text. We extensively evaluate competitive counterparts on challenging forecasting tasks. Table 9 contains the forecasting results on the four public subsets from PEMS (<PERSON> et al., 2022a) . ", "cite_spans": [{"start": 287, "end": 306, "text": "(<PERSON> et al., 2022a)", "ref_id": null}], "ref_spans": [{"start": 215, "end": 216, "text": "9", "ref_id": "TABREF12"}], "eq_spans": [], "section": "F.4 FULL FORECASTING RESULTS", "sec_num": null}], "back_matter": [{"text": "This work was supported by the National Key Research and Development Plan (2021YFB1715200), the National Natural Science Foundation of China (U2342217 and 62022050), the BNRist Innovation Fund (BNR2024RC01010), Ant Group through CCF-Ant Research Fund, and the National Engineering Research Center for Big Data Software.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACKNOWLEDGMENTS", "sec_num": null}, {"text": "Table 10 : Full results of the long-term forecasting task. We compare extensive competitive models under different prediction lengths following the setting of TimesNet (2023) . The input sequence length is set to 96 for all baselines. Avg means the average results from all four prediction lengths. Channel Independence (CI) (<PERSON><PERSON> et al., 2023) , regarding variates of time series independently and adopting the shared backbone, have gained increasing popularity in forecasting with performance promotions as an architecture-free method. Recent works (<PERSON> et al., 2023; <PERSON> et al., 2023) found that while Channel Dependence (CD) benefits from a higher capacity ideally, CI can greatly boost the performance because of sample scarcity, since most of the current forecasting benchmarks are not large enough. We think it is essential to make variates independent, especially when there are potential risks of embedding as mentioned in Appendix E.3, inducing the ideal model capacity of CD limited by the excessively localized receptive field. However, the essence of CI, regarding multivariate time series univariately, can lead to time-consuming training and inference and become an obstacle to scalability. Still, multivariate correlations can not be explicitly utilized. Perpendicular to these works, iTransformer repurposes an architecture with the native Transformer modules to tackle the issues.RevIN (<PERSON> et al., 2021) and Stationarization (<PERSON> et al., 2022b) have been widely applied for the distribution shift (non-stationarity) as architecture-free techniques. These works strive to reveal the temporal dependency better. This is accomplished by layer normalization in iTransformer and still leaves further improvement for us to tackle the distribution shift.", "cite_spans": [{"start": 159, "end": 174, "text": "TimesNet (2023)", "ref_id": null}, {"start": 325, "end": 343, "text": "(<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}, {"start": 550, "end": 568, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF9"}, {"start": 569, "end": 585, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF18"}, {"start": 1402, "end": 1420, "text": "(<PERSON> et al., 2021)", "ref_id": "BIBREF12"}, {"start": 1442, "end": 1461, "text": "(<PERSON> et al., 2022b)", "ref_id": null}], "ref_spans": [{"start": 6, "end": 8, "text": "10", "ref_id": null}], "eq_spans": [], "section": "annex", "sec_num": null}, {"text": "Linear forecasters have natural advantages in modeling temporal dependencies. The dense weighting (<PERSON><PERSON> et al., 2023; <PERSON> et al., 2023) can reveal measurement-free relationships among the time points of the same variate. More advanced linear forecasters focus on structural point-wise modeling (<PERSON><PERSON><PERSON> et al., 2019; <PERSON> et al., 2022a; 2023) . By contrast, iTransformer is particularly good at forecasting high-dimensional time series (numerous variates with complicated correlations, which can be common and realistic for practitioners in real forecasting applications). For variate correlating, the embedding keeps the variate independent and the attention module can be applied to dig it out. Under univariate scenarios, iTransformer actually becomes a stackable linear forecaster (attention degradation), which leaves further enhancement to exploit the temporal dependency better.", "cite_spans": [{"start": 98, "end": 117, "text": "(<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF32"}, {"start": 118, "end": 134, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF18"}, {"start": 293, "end": 316, "text": "(<PERSON><PERSON><PERSON> et al., 2019;", "ref_id": "BIBREF23"}, {"start": 317, "end": 335, "text": "<PERSON> et al., 2022a;", "ref_id": null}, {"start": 336, "end": 341, "text": "2023)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "G.2 DISCUSSIONS ON LINEAR FORECASTERS", "sec_num": null}, {"text": "We emphasize that iTransformer actually proposes a new perspective to think about the multivariate time series modality, specifically, how to consider the variates and the tokenization. We list several representatives in Figure 19 . Transformer treats time series as the natural language but the timealigned embedding may bring about risks in multi-dimensional series. The problem can be alleviated by expanding the receptive field. Although it is believed that Patching (Zhang & Yan, 2023; <PERSON> et al., 2023) can be more fine-grained, it also brings higher computational complexity and the potential interaction noise between time-unaligned patches. If the current embedding (implemented by MLP) is enhanced with more inductive bias (such as TCN), it may handle more robust cases with the variate token paradigm and enjoy the flexibility of Transformer with changeable numbers of tokens.We believe the capability and scalability of Transformer have stood the test by extensive fields, but there is still improvement room to elaborately design components based on the inverted architecture, such as efficient attention for multivariate correlation, structural temporal dependency modeling under distribution shift, fine-grained variate tokenization and well-designed embedding mechanisms. ", "cite_spans": [{"start": 471, "end": 490, "text": "(Zhang & Yan, 2023;", "ref_id": "BIBREF33"}, {"start": 491, "end": 508, "text": "<PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF22"}], "ref_spans": [{"start": 228, "end": 230, "text": "19", "ref_id": null}], "eq_spans": [], "section": "G.3 DISCUSSIONS ON TRANSFORMERS", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "Layer normalization", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2016, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. Layer normalization. https://arxiv.org/pdf/1607.06450.pdf, 2016.", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "An empirical evaluation of generic convolutional and recurrent networks for sequence modeling", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zico", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Koltun", "suffix": ""}], "year": 2018, "venue": "", "volume": "2", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1803.01271"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. An empirical evaluation of generic convolutional and recurrent networks for sequence modeling. arXiv preprint arXiv:1803.01271, 2, 2018.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Some recent advances in forecasting and control", "authors": [{"first": "E", "middle": ["P"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": ["M"], "last": "Box", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 1968, "venue": "Journal of the Royal Statistical Society. Series C (Applied Statistics)", "volume": "17", "issue": "2", "pages": "91--109", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON> and <PERSON><PERSON><PERSON><PERSON>. Some recent advances in forecasting and control. Journal of the Royal Statistical Society. Series C (Applied Statistics), 17(2):91-109, 1968.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Language models are few-shot learners", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Subbiah", "suffix": ""}, {"first": "<PERSON>", "middle": ["D"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Sastry", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Language models are few-shot learners. NeurIPS, 2020.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Flashattention: Fast and memoryefficient exact attention with io-awareness", "authors": [{"first": "Tri", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Flashattention: Fast and memory- efficient exact attention with io-awareness. NeurIPS, 2022.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "Long-term forecasting with tide: Time-series dense encoder", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Wei<PERSON>", "middle": [], "last": "Kong", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Leach", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.08424"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Long-term forecasting with tide: Time-series dense encoder. arXiv preprint arXiv:2304.08424, 2023.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "Simmtm: A simple pre-training framework for masked time-series modeling", "authors": [{"first": "Jiaxiang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Haixu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Li", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.00861"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Simmtm: A simple pre-training framework for masked time-series modeling. arXiv preprint arXiv:2302.00861, 2023.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "An image is worth 16x16 words: Transformers for image recognition at scale", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Dosovitskiy", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Weissenborn", "suffix": ""}, {"first": "Xiaohua", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, et al. An image is worth 16x16 words: Transformers for image recognition at scale. ICLR, 2021.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Tsmixer: Lightweight mlp-mixer model for multivariate time series forecasting", "authors": [{"first": "<PERSON>", "middle": [], "last": "Eka<PERSON>ram", "suffix": ""}, {"first": "Arinda<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Nam", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "KDD", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Tsmixer: Lightweight mlp-mixer model for multivariate time series forecasting. KDD, 2023.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "The capacity and robustness trade-off: Revisiting the channel independent strategy for multivariate time series forecasting", "authors": [{"first": "<PERSON>", "middle": [], "last": "Han", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ye", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.05206"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. The capacity and robustness trade-off: Revisiting the chan- nel independent strategy for multivariate time series forecasting. arXiv preprint arXiv:2304.05206, 2023.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Approximation capabilities of multilayer feedforward networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 1991, "venue": "Neural networks", "volume": "4", "issue": "2", "pages": "251--257", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>. Approximation capabilities of multilayer feedforward networks. Neural networks, 4(2): 251-257, 1991.", "links": null}, "BIBREF11": {"ref_id": "b11", "title": "Scaling laws for neural language models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Sam", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["B"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Chess", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Child", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Dar<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2001.08361"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Scaling laws for neural language models. arXiv preprint arXiv:2001.08361, 2020.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Reversible instance normalization for accurate time-series forecasting against distribution shift", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Cheonbok", "middle": [], "last": "Park", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Reversible instance normalization for accurate time-series forecasting against distribution shift. ICLR, 2021.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Adam: A method for stochastic optimization", "authors": [{"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Ba", "suffix": ""}], "year": 2015, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON>: A method for stochastic optimization. ICLR, 2015.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Reformer: The efficient transformer", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Lev<PERSON>", "suffix": ""}], "year": 2020, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Reformer: The efficient transformer. ICLR, 2020.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Similarity of neural network representations revisited. ICML", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["E"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Similarity of neural network representations revisited. ICML, 2019.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Modeling long-and short-term temporal patterns with deep neural networks", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "SIGIR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Modeling long-and short-term temporal patterns with deep neural networks. SIGIR, 2018.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Informer: Beyond efficient transformer for long sequence time-series forecasting", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Wancai", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2012.07436"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Informer: Beyond efficient transformer for long sequence time-series forecasting. arXiv: 2012.07436, 2021.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Revisiting long-term time series forecasting: An investigation on linear mapping", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Qi", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>n", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.10721"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Revisiting long-term time series forecasting: An investigation on linear mapping. arXiv preprint arXiv:2305.10721, 2023.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "Scinet: time series modeling and forecasting with sample convolution and interaction", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>ling", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Muxi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Scinet: time series modeling and forecasting with sample convolution and interaction. NeurIPS, 2022a.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Non-stationary transformers: Rethinking the stationarity in time series forecasting", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Haixu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Non-stationary transformers: Rethinking the stationarity in time series forecasting. NeurIPS, 2022b.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Learning non-stationary time series dynamics with koopman predictors", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2305.18803"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Koopa: Learning non-stationary time series dynamics with koopman predictors. arXiv preprint arXiv:2305.18803, 2023.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "A time series is worth 64 words: Long-term forecasting with transformers", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Nam", "middle": ["H"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. A time series is worth 64 words: Long-term forecasting with transformers. ICLR, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "N-BEATS: Neural basis expansion analysis for interpretable time series forecasting", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Carpov", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. N-BEATS: Neural basis expansion analysis for interpretable time series forecasting. ICLR, 2019.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Pytorch: An imperative style, high-performance deep learning library", "authors": [{"first": "S", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Francisco", "middle": [], "last": "Gross", "suffix": ""}, {"first": "A", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Bradbury", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Z", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "N", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "L", "middle": [], "last": "Gimelshein", "suffix": ""}, {"first": "Alban", "middle": [], "last": "Antiga", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>son", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Soumith", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2019, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Pytorch: An imperative style, high-performance deep learning library. NeurIPS, 2019.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Deep state space models for time series forecasting", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Sundar <PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["W"], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2018, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Deep state space models for time series forecasting. NeurIPS, 2018.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "Deepar: Probabilistic forecasting with autoregressive recurrent networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "Salinas", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Jan", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "International Journal of Forecasting", "volume": "36", "issue": "3", "pages": "1181--1191", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Deepar: Probabilistic forecasting with autoregressive recurrent networks. International Journal of Forecasting, 36(3): 1181-1191, 2020.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Mlp-mixer: An all-mlp architecture for vision", "authors": [{"first": "<PERSON>", "middle": [], "last": "Ilya O Tolstikhin", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Xiaohua", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Z<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Uszkoreit", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Mlp-mixer: An all-mlp architecture for vision. NeurIPS, 2021.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Attention is all you need", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>am", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Uszkoreit", "suffix": ""}, {"first": "Llion", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["N"], "last": "<PERSON>", "suffix": ""}, {"first": "Lukasz", "middle": [], "last": "Kaiser", "suffix": ""}, {"first": "Illia", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. NeurIPS, 2017.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Autoformer: Decomposition transformers with Auto-Correlation for long-term series forecasting", "authors": [{"first": "Haixu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Autoformer: Decomposition transformers with Auto-Correlation for long-term series forecasting. NeurIPS, 2021.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Flowformer: Linearizing transformers with conservation flows", "authors": [{"first": "Haixu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jialong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Flowformer: Linearizing transformers with conservation flows. ICML, 2022.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Timesnet: Temporal 2d-variation modeling for general time series analysis", "authors": [{"first": "Haixu", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Mingsheng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Timesnet: Temporal 2d-variation modeling for general time series analysis. ICLR, 2023.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "Are transformers effective for time series forecasting?", "authors": [{"first": "<PERSON>ling", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Muxi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Are transformers effective for time series forecasting? AAAI, 2023.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Crossformer: Transformer utilizing cross-dimension dependency for multivariate time series forecasting", "authors": [{"first": "Yunhao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Yan", "suffix": ""}], "year": 2023, "venue": "ICLR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON> and <PERSON><PERSON>. Crossformer: Transformer utilizing cross-dimension dependency for multivariate time series forecasting. ICLR, 2023.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Lstm network: a deep learning approach for short-term traffic forecast", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weihai", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xingming", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "C", "middle": ["Y"], "last": "<PERSON>", "suffix": ""}, {"first": "Jingmeng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "IET Intelligent Transport Systems", "volume": "11", "issue": "2", "pages": "68--75", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Lstm network: a deep learning approach for short-term traffic forecast. IET Intelligent Transport Systems, 11(2):68-75, 2017.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "FEDformer: Frequency enhanced decomposed transformer for long-term series forecasting", "authors": [{"first": "Tian", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Qingsong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. FEDformer: Frequency enhanced decomposed transformer for long-term series forecasting. ICML, 2022.", "links": null}}, "ref_entries": {"FIGREF0": {"type_str": "figure", "fig_num": "1", "num": null, "uris": null, "text": "Figure 1: Performance of iTransformer. Average results (MSE) are reported following TimesNet (2023)."}, "FIGREF1": {"type_str": "figure", "fig_num": "2", "num": null, "uris": null, "text": "Figure 2: Comparison between the vanilla Transformer (top) and the proposed iTransformer (bottom).Transformer embeds the temporal token, which contains the multivariate representation of each time step. iTransformer embeds each series independently to the variate token, such that the attention module depicts the multivariate correlations and the feed-forward network encodes series representations."}, "FIGREF2": {"type_str": "figure", "fig_num": "3", "num": null, "uris": null, "text": "Figure 3: Transformer-based forecasters categorized by component and architecture modifications."}, "FIGREF3": {"type_str": "figure", "fig_num": "4", "num": null, "uris": null, "text": "Figure 4: Overall structure of iTransformer, which shares the same modular arrangement with the encoder of Transformer. (a) Raw series of different variates are independently embedded as tokens. (b) Self-attention is applied to embedded variate tokens with enhanced interpretability revealing multivariate correlations. (c) Series representations of each token are extracted by the shared feedforward network. (d) Layer normalization is adopted to reduce the discrepancies among variates."}, "FIGREF4": {"type_str": "figure", "fig_num": "78", "num": null, "uris": null, "text": "Figure 7: Analysis of series representations and multivariate correlations. Left: MSE and CKA similarity of representations comparison between Transformers and iTransformers. A higher CKA similarity indicates more favored representations for accurate predictions. Right: A case visualization of multivariate correlations of raw time series and the learned score maps by inverted self-attention. Efficient training strategy Due to the quadratic complexity of self-attention, it can be overwhelming for training on numerous variates, which is very common in real-world scenarios. In addition to efficient attention mechanisms, we propose a novel training strategy for high-dimensional multivariate series by taking advantage of previously demonstrated variate generation capability. Concretely, we randomly choose part of the variates in each batch and only train the model with selected variates.Since the number of variate channels is flexible because of our inverting, the model can predict all the variates for predictions. As shown in Figure8, the performance of our proposed strategy is still comparable with full-variate training, while the memory footprint can be reduced significantly."}, "FIGREF5": {"type_str": "figure", "fig_num": "9", "num": null, "uris": null, "text": "Figure 9: Hyperparameter sensitivity with respect to the learning rate, the number of Transformer blocks, and the hidden dimension of variate tokens. The results are recorded with the lookback window length T = 96 and the forecast window length S = 96."}, "FIGREF6": {"type_str": "figure", "fig_num": "10", "num": null, "uris": null, "text": "Figure 10: Model efficiency comparison under input-96-predict-96 of Weather and Traffic."}, "FIGREF7": {"type_str": "figure", "fig_num": "11", "num": null, "uris": null, "text": "Figure 11: Multivariate correlations of the lookback series and future series and the learned score maps by inverted self-attention of different layers. Cases all come from the Solar-Energy dataset."}, "FIGREF8": {"type_str": "figure", "fig_num": "12", "num": null, "uris": null, "text": "Figure 12: Visualization of the variates from the Market dataset and the learned multivariate correlations. Each variate represents the monitored interface values of an application, and the applications can be further grouped into refined categories. The color bar is shared with Figure 11."}, "FIGREF9": {"type_str": "figure", "fig_num": "13", "num": null, "uris": null, "text": "Figure 13: Visualization of input-96-predict-96 results on the Traffic dataset."}, "FIGREF10": {"type_str": "figure", "fig_num": "141516", "num": null, "uris": null, "text": "Figure 14: Visualization of input-96-predict-96 results on the ECL dataset."}, "FIGREF11": {"type_str": "figure", "fig_num": "17", "num": null, "uris": null, "text": "Figure 17: Visualization of partial variates of Traffic. We can observe that several series exhibit strong synchronization (such as Sensor 2 and Sensor 4), and there also exist obvious delays and advances between series (such as Sensor 1 and Sensor 2, Sensor 859 and Sensor 861)."}, "TABREF0": {"html": null, "content": "<table><tr><td>Models</td><td>iTransformer RLinear (Ours) (2023)</td><td>PatchTST Crossformer (2023) (2023)</td><td>TiDE (2023)</td><td>TimesNet (2023)</td><td>DLinear (2023)</td><td>SCINet (2022a)</td><td>FEDformer Stationary Autoformer (2022) (2022b) (2021)</td></tr><tr><td>Traffic</td><td colspan=\"7\">0.428 0.282 0.626 0.378 0.481 0.304 0.550 0.304 0.760 0.473 0.620 0.336 0.625 0.383 0.804 0.509 0.610 0.376 0.624 0.340 0.628 0.379</td></tr><tr><td colspan=\"8\">Weather 0.258 0.278 0.272 0.291 0.259 0.281 0.259 0.315 0.271 0.320 0.259 0.287 0.265 0.317 0.292 0.363 0.309 0.360 0.288 0.314 0.338 0.382</td></tr><tr><td colspan=\"8\">Solar-Energy 0.233 0.262 0.369 0.356 0.270 0.307 0.641 0.639 0.347 0.417 0.301 0.319 0.330 0.401 0.282 0.375 0.291 0.381 0.261 0.381 0.885 0.711</td></tr><tr><td colspan=\"8\">PEMS (Avg) 0.119 0.218 0.514 0.482 0.217 0.305 0.220 0.304 0.375 0.440 0.148 0.246 0.320 0.394 0.121 0.222 0.224 0.327 0.151 0.249 0.614 0.575</td></tr></table>", "type_str": "table", "num": null, "text": "Multivariate forecasting results with prediction lengths S ∈ {12, 24, 36, 48} for PEMS and S ∈ {96, 192, 336, 720} for others and fixed lookback length T = 96. Results are averaged from all prediction lengths. Avg means further averaged by subsets. Full results are listed in Appendix F.4."}, "TABREF1": {"html": null, "content": "<table/>", "type_str": "table", "num": null, "text": ""}, "TABREF2": {"html": null, "content": "<table><tr><td colspan=\"2\">Models</td><td>Transformer (2017)</td><td>Reformer (2020)</td><td>Informer (2021)</td><td>Flowformer (2022)</td><td>Flashformer (2022)</td></tr><tr><td colspan=\"2\">Metric</td><td colspan=\"5\">MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE</td></tr><tr><td/><td colspan=\"6\">Original 0.277 0.372 0.338 0.422 0.311 0.397 0.267 0.359 0.285 0.377</td></tr><tr><td>ECL</td><td colspan=\"6\">+Inverted 0.178 0.270 0.208 0.301 0.216 0.311 0.210 0.293 0.206 0.291</td></tr><tr><td/><td colspan=\"6\">Promotion 35.6% 27.4% 38.4% 28.7% 30.5% 21.6% 21.3% 18.6% 27.8% 22.9%</td></tr><tr><td/><td colspan=\"6\">Original 0.665 0.363 0.741 0.422 0.764 0.416 0.750 0.421 0.658 0.356</td></tr><tr><td>Traffic</td><td colspan=\"6\">+Inverted 0.428 0.282 0.647 0.370 0.662 0.380 0.524 0.355 0.492 0.333</td></tr><tr><td/><td colspan=\"6\">Promotion 35.6% 22.3% 12.7% 12.3% 13.3% 8.6% 30.1% 15.6% 25.2% 6.4%</td></tr><tr><td/><td colspan=\"6\">Original 0.657 0.572 0.803 0.656 0.634 0.548 0.286 0.308 0.659 0.574</td></tr><tr><td>Weather</td><td colspan=\"6\">+Inverted 0.258 0.279 0.248 0.292 0.271 0.330 0.266 0.285 0.262 0.282</td></tr><tr><td/><td colspan=\"6\">Promotion 60.2% 50.8% 69.2% 55.5% 57.3% 39.8% 7.2% 7.7% 60.2% 50.8%</td></tr></table>", "type_str": "table", "num": null, "text": "Performance promotion obtained by our inverted framework. Flashformer means Transformer equipped with hardware-accelerated FlashAttention(<PERSON><PERSON> et al., 2022). We report the average performance and the relative MSE reduction (Promotion). Full results can be found in Appendix F.2."}, "TABREF4": {"html": null, "content": "<table><tr><td>(&amp;/</td><td/><td/><td/><td>7UDIILF</td><td/></tr><tr><td>06(</td><td/><td>06(</td><td/><td/><td/></tr><tr><td>L7UDQVIRUPHU</td><td>L)ORZIRUPHU</td><td>L,QIRUPHU</td><td>7UDQVIRUPHU</td><td>)ORZIRUPHU</td><td>,QIRUPHU</td></tr><tr><td>Figure 6:</td><td/><td/><td/><td/><td/></tr></table>", "type_str": "table", "num": null, "text": "Forecasting performance with the lookback length T ∈ {48, 96, 192, 336, 720} and fixed prediction length S = 96. While the performance of Transformer-based forecasters does not necessarily benefit from the increased lookback length, the inverted framework empowers the vanilla Transformer and its variants with improved performance on the enlarged lookback window."}, "TABREF5": {"html": null, "content": "<table><tr><td>Design</td><td>Variate</td><td>Temporal</td><td>ECL</td><td>Traffic</td><td>Weather</td><td>Solar-Energy</td></tr><tr><td/><td/><td/><td colspan=\"4\">MSE MAE MSE MAE MSE MAE MSE MAE</td></tr><tr><td colspan=\"2\">iTransformer Attention</td><td>FFN</td><td colspan=\"4\">0.178 0.270 0.428 0.282 0.258 0.278 0.233 0.262</td></tr><tr><td/><td colspan=\"6\">Attention Attention 0.193 0.293 0.913 0.500 0.255 0.280 0.261 0.291</td></tr><tr><td>Replace</td><td>FFN</td><td colspan=\"5\">Attention 0.202 0.300 0.863 0.499 0.258 0.283 0.285 0.317</td></tr><tr><td/><td>FFN</td><td>FFN</td><td colspan=\"4\">0.182 0.287 0.599 0.348 0.248 0.274 0.269 0.287</td></tr><tr><td>w/o</td><td>Attention w/o</td><td>w/o FFN</td><td colspan=\"4\">0.189 0.278 0.456 0.306 0.261 0.281 0.258 0.289 0.193 0.276 0.461 0.294 0.265 0.283 0.261 0.283</td></tr></table>", "type_str": "table", "num": null, "text": "Ablations on iTransformer. We replace different components on the respective dimension to learn multivariate correlations (Variate) and series representations (Temporal), in addition to component removal. The average results of all predicted lengths are listed here."}, "TABREF7": {"html": null, "content": "<table><tr><td>Dataset</td><td colspan=\"2\">Dim Prediction Length</td><td>Dataset Size</td><td>Frequency</td><td>Information</td></tr><tr><td>ETTh1, ETTh2</td><td>7</td><td>{96, 192, 336, 720}</td><td>(8545, 2881, 2881)</td><td>Hourly</td><td>Electricity</td></tr><tr><td>ETTm1, ETTm2</td><td>7</td><td>{96, 192, 336, 720}</td><td>(34465, 11521, 11521)</td><td>15min</td><td>Electricity</td></tr><tr><td>Exchange</td><td>8</td><td>{96, 192, 336, 720}</td><td>(5120, 665, 1422)</td><td>Daily</td><td>Economy</td></tr><tr><td>Weather</td><td>21</td><td>{96, 192, 336, 720}</td><td>(36792, 5271, 10540)</td><td>10min</td><td>Weather</td></tr><tr><td>ECL</td><td>321</td><td>{96, 192, 336, 720}</td><td>(18317, 2633, 5261)</td><td>Hourly</td><td>Electricity</td></tr><tr><td>Traffic</td><td>862</td><td>{96, 192, 336, 720}</td><td>(12185, 1757, 3509)</td><td>Hourly</td><td>Transportation</td></tr><tr><td>Solar-Energy</td><td>137</td><td>{96, 192, 336, 720}</td><td>(36601, 5161, 10417)</td><td>10min</td><td>Energy</td></tr><tr><td>PEMS03</td><td>358</td><td>{12, 24, 48, 96}</td><td>(15617, 5135, 5135)</td><td>5min</td><td>Transportation</td></tr><tr><td>PEMS04</td><td>307</td><td>{12, 24, 48, 96}</td><td>(10172, 3375, 3375)</td><td>5min</td><td>Transportation</td></tr><tr><td>PEMS07</td><td>883</td><td>{12, 24, 48, 96}</td><td>(16911, 5622, 5622)</td><td>5min</td><td>Transportation</td></tr><tr><td>PEMS08</td><td>170</td><td>{12, 24, 48, 96}</td><td>(10690, 3548, 3548)</td><td>5min</td><td>Transportation</td></tr><tr><td>Market-Merchant</td><td>285</td><td>{12, 24, 72, 144}</td><td>(7045, 1429, 1429)</td><td>10min</td><td>Transaction</td></tr><tr><td>Market-Wealth</td><td>485</td><td>{12, 24, 72, 144}</td><td>(7045, 1429, 1429)</td><td>10min</td><td>Transaction</td></tr><tr><td>Market-Finance</td><td>405</td><td>{12, 24, 72, 144}</td><td>(7045, 1429, 1429)</td><td>10min</td><td>Transaction</td></tr><tr><td>Market-Terminal</td><td>307</td><td>{12, 24, 72, 144}</td><td>(7045, 1429, 1429)</td><td>10min</td><td>Transaction</td></tr><tr><td>Market-Payment</td><td>759</td><td>{12, 24, 72, 144}</td><td>(7045, 1429, 1429)</td><td>10min</td><td>Transaction</td></tr><tr><td>Market-Customer</td><td>395</td><td>{12, 24, 72, 144}</td><td>(7045, 1429, 1429)</td><td>10min</td><td>Transaction</td></tr></table>", "type_str": "table", "num": null, "text": "Detailed dataset descriptions. Dim denotes the variate number of each dataset. Dataset Size denotes the total number of time points in (Train, Validation, Test) split respectively. Prediction Length denotes the future time points to be predicted and four prediction settings are included in each dataset. Frequency denotes the sampling interval of time points."}, "TABREF8": {"html": null, "content": "<table><tr><td>Dataset</td><td colspan=\"2\">ECL</td><td colspan=\"2\">ETTh2</td><td colspan=\"2\">Exchange</td></tr><tr><td>Horizon</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td></tr><tr><td>96</td><td>0.148±0.000</td><td>0.240±0.000</td><td>0.297±0.002</td><td>0.349±0.001</td><td>0.088±0.001</td><td>0.209±0.001</td></tr><tr><td>192</td><td>0.162±0.002</td><td>0.253±0.002</td><td>0.380±0.001</td><td>0.400±0.001</td><td>0.181±0.001</td><td>0.304±0.001</td></tr><tr><td>336</td><td>0.178±0.000</td><td>0.269±0.001</td><td>0.428±0.002</td><td>0.432±0.001</td><td>0.334±0.001</td><td>0.419±0.001</td></tr><tr><td>720</td><td>0.225±0.006</td><td>0.317±0.007</td><td>0.427±0.004</td><td>0.445±0.002</td><td>0.829±0.012</td><td>0.691±0.005</td></tr><tr><td>Dataset</td><td colspan=\"2\">Solar-Energy</td><td colspan=\"2\">Traffic</td><td colspan=\"2\">Weather</td></tr><tr><td>Horizon</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td><td>MSE</td><td>MAE</td></tr><tr><td>96</td><td>0.203±0.002</td><td>0.237±0.002</td><td>0.395±0.001</td><td>0.268±0.001</td><td>0.174±0.000</td><td>0.214±0.000</td></tr><tr><td>192</td><td>0.233±0.002</td><td>0.261±0.001</td><td>0.417±0.002</td><td>0.276±0.001</td><td>0.221±0.002</td><td>0.254±0.001</td></tr><tr><td>336</td><td>0.248±0.000</td><td>0.273±0.000</td><td>0.433±0.004</td><td>0.283±0.000</td><td>0.278±0.002</td><td>0.296±0.001</td></tr><tr><td>720</td><td>0.249±0.001</td><td>0.275±0.000</td><td>0.467±0.003</td><td>0.302±0.000</td><td>0.358±0.000</td><td>0.349±0.000</td></tr></table>", "type_str": "table", "num": null, "text": "Robustness of iTransformer performance. The results are obtained from five random seeds."}, "TABREF9": {"html": null, "content": "<table><tr><td/><td colspan=\"2\">Design</td><td colspan=\"3\">Variate Temporal</td><td colspan=\"2\">Prediction</td><td colspan=\"2\">ECL</td><td>Traffic</td><td colspan=\"2\">Weather</td><td>Solar-Energy</td></tr><tr><td/><td/><td/><td/><td/><td/><td colspan=\"8\">Lengths MSE MAE MSE MAE MSE MAE MSE MAE</td></tr><tr><td/><td/><td/><td/><td/><td/><td>96</td><td/><td colspan=\"6\">0.148 0.240 0.395 0.268 0.174 0.214 0.203 0.237</td></tr><tr><td/><td/><td/><td/><td/><td/><td>192</td><td/><td colspan=\"6\">0.162 0.253 0.417 0.276 0.221 0.254 0.233 0.261</td></tr><tr><td colspan=\"5\">iTransformer Attention</td><td>FFN</td><td>336</td><td/><td colspan=\"6\">0.178 0.269 0.433 0.283 0.278 0.296 0.248 0.273</td></tr><tr><td/><td/><td/><td/><td/><td/><td>720</td><td/><td colspan=\"6\">0.225 0.317 0.467 0.302 0.358 0.349 0.249 0.275</td></tr><tr><td/><td/><td/><td/><td/><td/><td>Avg</td><td/><td colspan=\"6\">0.178 0.270 0.428 0.282 0.258 0.279 0.233 0.262</td></tr><tr><td/><td/><td/><td/><td/><td/><td>96</td><td/><td colspan=\"6\">0.161 0.263 1.021 0.581 0.168 0.213 0.227 0.270</td></tr><tr><td/><td/><td/><td/><td/><td/><td>192</td><td/><td colspan=\"6\">0.180 0.280 0.834 0.447 0.217 0.256 0.255 0.292</td></tr><tr><td/><td/><td/><td colspan=\"3\">Attention Attention</td><td>336</td><td/><td colspan=\"6\">0.194 0.296 0.906 0.493 0.277 0.299 0.279 0.301</td></tr><tr><td/><td/><td/><td/><td/><td/><td>720</td><td/><td colspan=\"6\">0.238 0.331 0.892 0.477 0.356 0.351 0.283 0.300</td></tr><tr><td/><td/><td/><td/><td/><td/><td>Avg</td><td/><td colspan=\"6\">0.193 0.293 0.913 0.500 0.255 0.280 0.261 0.291</td></tr><tr><td/><td/><td/><td/><td/><td/><td>96</td><td/><td colspan=\"6\">0.169 0.270 0.907 0.540 0.176 0.221 0.247 0.299</td></tr><tr><td/><td colspan=\"2\">Replace</td><td/><td/><td/><td>192</td><td/><td colspan=\"6\">0.189 0.292 0.839 0.489 0.224 0.261 0.275 0.305</td></tr><tr><td/><td/><td/><td colspan=\"2\">FFN</td><td>Attention</td><td>336</td><td/><td colspan=\"6\">0.204 0.304 0.248 0.364 0.279 0.301 0.317 0.337</td></tr><tr><td/><td/><td/><td/><td/><td/><td>720</td><td/><td colspan=\"6\">0.245 0.335 1.059 0.606 0.354 0.347 0.301 0.329</td></tr><tr><td/><td/><td/><td/><td/><td/><td>Avg</td><td/><td colspan=\"6\">0.202 0.300 0.863 0.499 0.258 0.283 0.285 0.317</td></tr><tr><td/><td/><td/><td/><td/><td/><td>96</td><td/><td colspan=\"6\">0.159 0.261 0.606 0.342 0.162 0.207 0.237 0.277</td></tr><tr><td/><td/><td/><td/><td/><td/><td>192</td><td/><td colspan=\"6\">0.171 0.271 0.559 0.342 0.211 0.252 0.273 0.293</td></tr><tr><td/><td/><td/><td colspan=\"2\">FFN</td><td>FFN</td><td>336</td><td/><td colspan=\"6\">0.187 0.287 0.569 0.348 0.270 0.293 0.284 0.287</td></tr><tr><td/><td/><td/><td/><td/><td/><td>720</td><td/><td colspan=\"6\">0.211 0.307 0.664 0.359 0.349 0.345 0.284 0.289</td></tr><tr><td/><td/><td/><td/><td/><td/><td>Avg</td><td/><td colspan=\"6\">0.182 0.287 0.599 0.348 0.248 0.274 0.269 0.287</td></tr><tr><td/><td/><td/><td/><td/><td/><td>96</td><td/><td colspan=\"6\">0.163 0.254 0.427 0.296 0.177 0.219 0.226 0.266</td></tr><tr><td/><td/><td/><td/><td/><td/><td>192</td><td/><td colspan=\"6\">0.174 0.263 0.446 0.300 0.226 0.259 0.255 0.288</td></tr><tr><td/><td/><td/><td colspan=\"2\">Attention</td><td>w/o</td><td>336</td><td/><td colspan=\"6\">0.191 0.280 0.459 0.306 0.281 0.298 0.275 0.301</td></tr><tr><td/><td/><td/><td/><td/><td/><td>720</td><td/><td colspan=\"6\">0.228 0.315 0.492 0.324 0.359 0.249 0.275 0.301</td></tr><tr><td/><td/><td>w/o</td><td/><td/><td/><td>Avg</td><td/><td colspan=\"6\">0.189 0.278 0.456 0.306 0.261 0.281 0.258 0.289</td></tr><tr><td/><td/><td/><td/><td/><td/><td>96</td><td/><td colspan=\"6\">0.169 0.253 0.437 0.283 0.183 0.220 0.228 0.263</td></tr><tr><td/><td/><td/><td/><td/><td/><td>192</td><td/><td colspan=\"6\">0.177 0.261 0.449 0.287 0.231 0.262 0.261 0.283</td></tr><tr><td/><td/><td/><td colspan=\"2\">w/o</td><td>FFN</td><td>336</td><td/><td colspan=\"6\">0.194 0.278 0.464 0.294 0.285 0.300 0.279 0.294</td></tr><tr><td/><td/><td/><td/><td/><td/><td>720</td><td/><td colspan=\"6\">0.233 0.311 0.496 0.313 0.362 0.350 0.276 0.291</td></tr><tr><td/><td/><td/><td/><td/><td/><td>Avg</td><td/><td colspan=\"6\">0.193 0.276 0.461 0.294 0.265 0.283 0.261 0.283</td></tr><tr><td>0.9</td><td/><td/><td colspan=\"3\">Traffic (862 Variates)</td><td/><td/><td/><td/><td/><td colspan=\"2\">Weather (21 Variates)</td></tr><tr><td>0.8</td><td/><td>TiDE</td><td colspan=\"3\">Memory Footprint</td><td/><td/><td/><td>0.36</td><td>0.85GB 0.95GB</td><td>1.10GB</td><td/><td>Transformer</td></tr><tr><td>0.7 MSE 0.6</td><td/><td>2.72GB, 130ms DLinear 0.91GB, 60ms</td><td>1.5GB Transformer 1.16GB, 145ms</td><td>3.0GB</td><td>6.0GB</td><td>PatchTST 8.58GB, 635ms</td><td/><td>MSE</td><td>0.28</td><td colspan=\"2\">Memory Footprint TiDE 0.90GB, 28ms 0.83GB, 28ms DLinear</td><td/><td>1.09GB, 85ms</td></tr><tr><td/><td/><td colspan=\"2\">iFlowformer</td><td/><td/><td/><td/><td/><td/><td/><td/><td/></tr><tr><td>0.5</td><td/><td colspan=\"2\">1.66GB, 91ms</td><td/><td/><td/><td/><td/><td/><td colspan=\"3\">iTransformer (Efficient) 0.87GB, 29ms</td></tr><tr><td/><td/><td/><td/><td/><td/><td/><td/><td/><td>0.20</td><td colspan=\"2\">iFlowformer 0.89GB, 30ms</td><td/></tr><tr><td>0.4</td><td/><td/><td/><td colspan=\"2\">iTransformer</td><td colspan=\"2\">Crossformer</td><td/><td/><td colspan=\"2\">iTransformer 0.88GB, 30ms</td><td colspan=\"2\">Crossformer</td></tr><tr><td/><td colspan=\"3\">iTransformer (Efficient)</td><td colspan=\"2\">7.50GB, 265ms</td><td colspan=\"2\">9.74GB, 702ms</td><td/><td/><td>PatchTST</td><td/><td colspan=\"2\">1.18GB, 110ms</td></tr><tr><td>0.3</td><td>0</td><td>150 1.28GB, 91ms</td><td colspan=\"2\">300</td><td>450</td><td>600</td><td>750</td><td/><td>20 0.12</td><td>40 1.09GB, 31ms</td><td>60</td><td>80</td><td>100</td><td>120</td></tr><tr><td/><td/><td/><td colspan=\"3\">Training Time (ms/iter)</td><td/><td/><td/><td/><td/><td colspan=\"2\">Training Time (ms/iter)</td></tr></table>", "type_str": "table", "num": null, "text": "Full results of the ablation on iTransformer. We apply different components on the respective dimension to learn multivariate correlations (Variate) and series representations (Temporal), in addition to removing the specific component of Transformer."}, "TABREF10": {"html": null, "content": "<table><tr><td>Datasets</td><td>ETT</td><td>ECL</td><td>PEMS</td><td>Solar-Energy</td><td>Traffic</td><td>Weather</td></tr><tr><td>Metric</td><td colspan=\"6\">MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE</td></tr><tr><td colspan=\"7\">Transformer 2.750 1.375 0.277 0.372 0.157 0.263 0.256 0.276 0.665 0.363 0.657 0.572</td></tr><tr><td colspan=\"7\">iTransformer 0.383 0.407 0.178 0.270 0.113 0.221 0.233 0.262 0.428 0.282 0.258 0.279</td></tr><tr><td colspan=\"7\">Promotion 86.1% 70.4% 35.6% 27.4% 28.0% 16.0% 9.0% 5.1% 35.6% 22.3% 60.2% 50.8%</td></tr></table>", "type_str": "table", "num": null, "text": "Full performance comparison between the vanilla Transformer and the proposed iTransformer. The results are averaged from all four prediction lengths."}, "TABREF11": {"html": null, "content": "<table><tr><td>Models</td><td>Transformer (2017)</td><td>Reformer (2020)</td><td>Informer (2021)</td><td>Flowformer (2022)</td><td>Flashformer (2022)</td></tr><tr><td>Metric</td><td colspan=\"5\">MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE</td></tr><tr><td/><td colspan=\"5\">96 0.260 0.358 0.312 0.402 0.274 0.368 0.215 0.320 0.259 0.357</td></tr><tr><td/><td colspan=\"5\">192 0.266 0.367 0.348 0.433 0.296 0.386 0.259 0.355 0.274 0.374</td></tr><tr><td>Original</td><td colspan=\"5\">336 0.280 0.375 0.350 0.433 0.300 0.394 0.296 0.383 0.310 0.396</td></tr><tr><td/><td colspan=\"5\">720 0.302 0.386 0.340 0.420 0.373 0.439 0.296 0.380 0.298 0.383</td></tr><tr><td>ECL</td><td colspan=\"5\">Avg 0.277 0.372 0.338 0.422 0.311 0.397 0.267 0.359 0.285 0.377</td></tr><tr><td/><td colspan=\"5\">96 0.148 0.240 0.182 0.275 0.190 0.286 0.183 0.267 0.178 0.265</td></tr><tr><td/><td colspan=\"5\">192 0.162 0.253 0.192 0.286 0.201 0.297 0.192 0.277 0.189 0.276</td></tr><tr><td>+Inverted</td><td colspan=\"5\">336 0.178 0.269 0.210 0.304 0.218 0.315 0.210 0.295 0.207 0.294</td></tr><tr><td/><td colspan=\"5\">720 0.225 0.317 0.249 0.339 0.255 0.347 0.255 0.332 0.251 0.329</td></tr><tr><td/><td colspan=\"5\">Avg 0.178 0.270 0.208 0.301 0.216 0.311 0.210 0.293 0.206 0.291</td></tr><tr><td/><td colspan=\"5\">96 0.647 0.357 0.732 0.423 0.719 0.391 0.691 0.393 0.641 0.348</td></tr><tr><td/><td colspan=\"5\">192 0.649 0.356 0.733 0.420 0.696 0.379 0.729 0.419 0.648 0.358</td></tr><tr><td>Original</td><td colspan=\"5\">336 0.667 0.364 0.742 0.420 0.777 0.420 0.756 0.423 0.670 0.364</td></tr><tr><td/><td colspan=\"5\">720 0.697 0.376 0.755 0.432 0.864 0.472 0.825 0.449 0.673 0.354</td></tr><tr><td>Traffic</td><td colspan=\"5\">Avg 0.665 0.363 0.741 0.422 0.764 0.416 0.750 0.421 0.658 0.356</td></tr><tr><td/><td colspan=\"5\">96 0.395 0.268 0.617 0.356 0.632 0.367 0.493 0.339 0.464 0.320</td></tr><tr><td/><td colspan=\"5\">192 0.417 0.276 0.629 0.361 0.641 0.370 0.506 0.345 0.479 0.326</td></tr><tr><td>+Inverted</td><td colspan=\"5\">336 0.433 0.283 0.648 0.370 0.663 0.379 0.526 0.355 0.501 0.337</td></tr><tr><td/><td colspan=\"5\">720 0.467 0.302 0.694 0.394 0.713 0.405 0.572 0.381 0.524 0.350</td></tr><tr><td/><td colspan=\"5\">Avg 0.428 0.282 0.647 0.370 0.662 0.380 0.524 0.355 0.492 0.333</td></tr><tr><td/><td colspan=\"5\">96 0.395 0.427 0.689 0.596 0.300 0.384 0.182 0.233 0.388 0.425</td></tr><tr><td/><td colspan=\"5\">192 0.619 0.560 0.752 0.638 0.598 0.544 0.250 0.288 0.619 0.560</td></tr><tr><td>Original</td><td colspan=\"5\">336 0.689 0.594 0.639 0.596 0.578 0.523 0.309 0.329 0.698 0.600</td></tr><tr><td/><td colspan=\"5\">720 0.926 0.710 1.130 0.792 1.059 0.741 0.404 0.385 0.930 0.711</td></tr><tr><td>Weather</td><td colspan=\"5\">Avg 0.657 0.572 0.803 0.656 0.634 0.548 0.286 0.308 0.659 0.574</td></tr><tr><td/><td colspan=\"5\">96 0.174 0.214 0.169 0.225 0.180 0.251 0.183 0.223 0.177 0.218</td></tr><tr><td/><td colspan=\"5\">192 0.221 0.254 0.213 0.265 0.244 0.318 0.231 0.262 0.229 0.261</td></tr><tr><td>+Inverted</td><td colspan=\"5\">336 0.278 0.296 0.268 0.317 0.282 0.343 0.286 0.301 0.283 0.300</td></tr><tr><td/><td colspan=\"5\">720 0.358 0.349 0.340 0.361 0.377 0.409 0.363 0.352 0.359 0.251</td></tr><tr><td/><td colspan=\"5\">Avg 0.258 0.279 0.248 0.292 0.271 0.330 0.266 0.285 0.262 0.282</td></tr></table>", "type_str": "table", "num": null, "text": "Full results of Transformers with our inverted framework. Flashformer means Transformer equipped with the hardware-accelerated FlashAttention(<PERSON><PERSON> et al., 2022)."}, "TABREF12": {"html": null, "content": "<table><tr><td colspan=\"2\">Models</td><td colspan=\"8\">iTransformer RLinear PatchTST Crossformer (Ours) (2023) (2023) (2023)</td><td colspan=\"2\">TiDE (2023)</td><td colspan=\"4\">TimesNet DLinear (2023) (2023)</td><td colspan=\"7\">SCINet FEDformer Stationary Autoformer (2022a) (2022) (2022b) (2021)</td></tr><tr><td/><td colspan=\"22\">Avg 0.111 0.221 0.526 0.491 0.195 0.307 0.209 0.314 0.353 0.437 0.129 0.241 0.295 0.388 0.092 0.202 0.231 0.337 0.127 0.240 0.610 0.590</td></tr><tr><td>PEMS07</td><td colspan=\"22\">12 0.067 0.165 0.118 0.235 0.095 0.207 0.094 0.200 0.173 0.304 0.082 0.181 0.115 0.242 0.068 0.171 0.109 0.225 0.083 0.185 0.199 0.336 24 0.088 0.190 0.242 0.341 0.150 0.262 0.139 0.247 0.271 0.383 0.101 0.204 0.210 0.329 0.119 0.225 0.125 0.244 0.102 0.207 0.323 0.420 48 0.110 0.215 0.562 0.541 0.253 0.340 0.311 0.369 0.446 0.495 0.134 0.238 0.398 0.458 0.149 0.237 0.165 0.288 0.136 0.240 0.390 0.470 96 0.139 0.245 1.096 0.795 0.346 0.404 0.396 0.442 0.628 0.577 0.181 0.279 0.594 0.553 0.141 0.234 0.262 0.376 0.187 0.287 0.554 0.578</td></tr><tr><td/><td colspan=\"22\">Avg 0.101 0.204 0.504 0.478 0.211 0.303 0.235 0.315 0.380 0.440 0.124 0.225 0.329 0.395 0.119 0.234 0.165 0.283 0.127 0.230 0.367 0.451</td></tr><tr><td colspan=\"3\">PEMS08 12 0.079 01 st Count 13</td><td>13</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>7</td><td>7</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr></table>", "type_str": "table", "num": null, "text": "Table 10 contains the detailed results of all prediction lengths of the nine well-acknowledged forecasting benchmarks. And Table 11 records the Market results for Alipay server load forecasting. The proposed model achieves comprehensive state-of-the-art in real-world forecasting applications. Full results of the PEMS forecasting task. We compare extensive competitive models under different prediction lengths following the setting of SCINet (2022a). The input length is set to 96 for all baselines. Avg means the average results from all four prediction lengths. Metric MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE MSE MAE PEMS03 12 0.071 0.174 0.126 0.236 0.099 0.216 0.090 0.203 0.178 0.305 0.085 0.192 0.122 0.243 0.066 0.172 0.126 0.251 0.081 0.188 0.272 0.385 24 0.093 0.201 0.246 0.334 0.142 0.259 0.121 0.240 0.257 0.371 0.118 0.223 0.201 0.317 0.085 0.198 0.149 0.275 0.105 0.214 0.334 0.440 48 0.125 0.236 0.551 0.529 0.211 0.319 0.202 0.317 0.379 0.463 0.155 0.260 0.333 0.425 0.127 0.238 0.227 0.348 0.154 0.257 1.032 0.782 96 0.164 0.275 1.057 0.787 0.269 0.370 0.262 0.367 0.490 0.539 0.228 0.317 0.457 0.515 0.178 0.287 0.348 0.434 0.247 0.336 1.031 0.796 Avg 0.113 0.221 0.495 0.472 0.180 0.291 0.169 0.281 0.326 0.419 0.147 0.248 0.278 0.375 0.114 0.224 0.213 0.327 0.147 0.249 0.667 0.601 PEMS04 12 0.078 0.183 0.138 0.252 0.105 0.224 0.098 0.218 0.219 0.340 0.087 0.195 0.148 0.272 0.073 0.177 0.138 0.262 0.088 0.196 0.424 0.491 24 0.095 0.205 0.258 0.348 0.153 0.275 0.131 0.256 0.292 0.398 0.103 0.215 0.224 0.340 0.084 0.193 0.177 0.293 0.104 0.216 0.459 0.509 48 0.120 0.233 0.572 0.544 0.229 0.339 0.205 0.326 0.409 0.478 0.136 0.250 0.355 0.437 0.099 0.211 0.270 0.368 0.137 0.251 0.646 0.610 96 0.150 0.262 1.137 0.820 0.291 0.389 0.402 0.457 0.492 0.532 0.190 0.303 0.452 0.504 0.114 0.227 0.341 0.427 0.186 0.297 0.912 0.748 .182 0.133 0.247 0.168 0.232 0.165 0.214 0.227 0.343 0.112 0.212 0.154 0.276 0.087 0.184 0.173 0.273 0.109 0.207 0.436 0.485 24 0.115 0.219 0.249 0.343 0.224 0.281 0.215 0.260 0.318 0.409 0.141 0.238 0.248 0.353 0.122 0.221 0.210 0.301 0.140 0.236 0.467 0.502 48 0.186 0.235 0.569 0.544 0.321 0.354 0.315 0.355 0.497 0.510 0.198 0.283 0.440 0.470 0.189 0.270 0.320 0.394 0.211 0.294 0.966 0.733 96 0.221 0.267 1.166 0.814 0.408 0.417 0.377 0.397 0.721 0.592 0.320 0.351 0.674 0.565 0.236 0.300 0.442 0.465 0.345 0.367 1.385 0.915 Avg 0.150 0.226 0.529 0.487 0.280 0.321 0.268 0.307 0.441 0.464 0.193 0.271 0.379 0.416 0.158 0.244 0.286 0.358 0.201 0.276 0.814 0.659"}}}}