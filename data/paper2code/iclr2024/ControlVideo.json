{"paper_id": "ControlVideo", "header": {"generated_with": "S2ORC 1.0.0", "date_generated": "2025-02-25T22:01:49.063237Z"}, "title": "CONTROLVIDEO: TRAINING-FREE CONTROLLABLE TEXT-TO-VIDEO GENERATION", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Harbin Institute of Technology", "location": {}}, "email": ""}, {"first": "Yuxiang", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Harbin Institute of Technology", "location": {}}, "email": ""}, {"first": "Dongsheng", "middle": [], "last": "Jiang", "suffix": "", "affiliation": {"laboratory": "", "institution": "Harbin Institute of Technology", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Harbin Institute of Technology", "location": {}}, "email": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": "", "affiliation": {"laboratory": "", "institution": "Harbin Institute of Technology", "location": {}}, "email": ""}, {"first": "Qi", "middle": [], "last": "Tian", "suffix": "", "affiliation": {"laboratory": "", "institution": "Harbin Institute of Technology", "location": {}}, "email": ""}], "year": "", "venue": null, "identifiers": {}, "abstract": "Text-driven diffusion models have unlocked unprecedented abilities in image generation, whereas their video counterpart lags behind due to the excessive training cost. To avert the training burden, we propose a training-free ControlVideo to produce high-quality videos based on the provided text prompts and motion sequences. Specifically, ControlVideo adapts a pre-trained text-to-image model (i.e., Con-trolNet) for controllable text-to-video generation. To generate continuous videos without flicker effects, we propose an interleaved-frame smoother to smooth the intermediate frames. In particular, interleaved-frame smoother splits the whole video with successive three-frame clips, and stabilizes each clip by updating the middle frame with the interpolation among other two frames in latent space. Furthermore, a fully cross-frame interaction mechanism is exploited to further enhance the frame consistency, while a hierarchical sampler is employed to produce long videos efficiently. Extensive experiments demonstrate that our ControlVideo outperforms the state-of-the-arts both quantitatively and qualitatively. It is worth noting that, thanks to the efficient designs, ControlVideo could generate both short and long videos within several minutes using one NVIDIA 2080Ti. Code and videos are available at this link.", "pdf_parse": {"paper_id": "ControlVideo", "_pdf_hash": "", "abstract": [{"text": "Text-driven diffusion models have unlocked unprecedented abilities in image generation, whereas their video counterpart lags behind due to the excessive training cost. To avert the training burden, we propose a training-free ControlVideo to produce high-quality videos based on the provided text prompts and motion sequences. Specifically, ControlVideo adapts a pre-trained text-to-image model (i.e., Con-trolNet) for controllable text-to-video generation. To generate continuous videos without flicker effects, we propose an interleaved-frame smoother to smooth the intermediate frames. In particular, interleaved-frame smoother splits the whole video with successive three-frame clips, and stabilizes each clip by updating the middle frame with the interpolation among other two frames in latent space. Furthermore, a fully cross-frame interaction mechanism is exploited to further enhance the frame consistency, while a hierarchical sampler is employed to produce long videos efficiently. Extensive experiments demonstrate that our ControlVideo outperforms the state-of-the-arts both quantitatively and qualitatively. It is worth noting that, thanks to the efficient designs, ControlVideo could generate both short and long videos within several minutes using one NVIDIA 2080Ti. Code and videos are available at this link.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Abstract", "sec_num": null}], "body_text": [{"text": "Large-scale diffusion models have made a tremendous breakthrough on text-to-image synthesis (<PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022) and their creative applications (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2022) . Several studies (<PERSON> et al., 2022b; a; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2022) attempt to replicate this success in the video counterpart, i.e., modeling higher-dimensional complex video distributions in the wild world. However, training such a text-to-video model requires massive amounts of high-quality videos and computational resources, which limits further research and applications by relevant communities.", "cite_spans": [{"start": 92, "end": 113, "text": "(<PERSON><PERSON><PERSON> et al., 2021;", "ref_id": "BIBREF24"}, {"start": 114, "end": 135, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF31"}, {"start": 136, "end": 156, "text": "<PERSON><PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF1"}, {"start": 157, "end": 177, "text": "<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF29"}, {"start": 178, "end": 199, "text": "<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF34"}, {"start": 232, "end": 250, "text": "(<PERSON><PERSON> et al., 2022;", "ref_id": "BIBREF6"}, {"start": 251, "end": 268, "text": "<PERSON> et al., 2023;", "ref_id": "BIBREF41"}, {"start": 269, "end": 285, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF23"}, {"start": 286, "end": 305, "text": "<PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF8"}, {"start": 324, "end": 342, "text": "(<PERSON> et al., 2022b;", "ref_id": null}, {"start": 343, "end": 345, "text": "a;", "ref_id": null}, {"start": 346, "end": 366, "text": "<PERSON> et al., 2022;", "ref_id": "BIBREF36"}, {"start": 367, "end": 386, "text": "<PERSON><PERSON> et al., 2023;", "ref_id": "BIBREF5"}, {"start": 387, "end": 405, "text": "<PERSON> et al., 2022)", "ref_id": "BIBREF12"}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In this work, we study a new and efficient form to avert the excessive training requirements: controllable text-to-video generation with text-to-image models. As shown in Fig. 1 , our method, termed ControlVideo, takes textual description and motion sequence (e.g., depth or edge maps) as conditions to generate videos. Instead of learning the video distribution from scratch, ControlVideo adapts the pre-trained text-to-image models (e.g., ControlNet (Zhang & Agrawala, 2023) ) for high-quality video generation. With the structural information from motion sequence and the superior generation capability of image models, it is feasible to produce a vivid video without additional training.", "cite_spans": [{"start": 452, "end": 476, "text": "(Zhang & <PERSON>wala, 2023)", "ref_id": "BIBREF47"}], "ref_spans": [{"start": 176, "end": 177, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "However, as shown in Fig. 1 , due to the lack of temporal interaction, individually producing each frame with ControlNet (Zhang & Agrawala, 2023 ) fails to ensure both (i) frame consistency and (ii) video continuity. Frame consistency requires all frames to be generated with a coherent appearance, while video continuity ensures smooth transitions between frames. Tune-A-Video (<PERSON> et al., 2022b) and Text2Video-Zero (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) facilitate appearance consistency by extending self-attention to sparser cross-frame attention. Nonetheless, such a cross-frame interaction is not sufficient to guarantee video continuity, and visible flickers appear in their synthesized videos (as shown in Fig. 1 and corresponding videos). Intuitively, a continuous video could be considered as multiple continuous three-frame clips, so the problem of ensuring the video continuity is converted to ensuring all three-frame clips continuous.", "cite_spans": [{"start": 121, "end": 144, "text": "(Zhang & Agrawala, 2023", "ref_id": "BIBREF47"}, {"start": 378, "end": 396, "text": "(<PERSON> et al., 2022b)", "ref_id": null}, {"start": 417, "end": 443, "text": "(<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF16"}], "ref_spans": [{"start": 26, "end": 27, "text": "1", "ref_id": "FIGREF0"}, {"start": 707, "end": 708, "text": "1", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Driven by this analysis, we propose an interleaved-frame smoother to enable continuous video generation. Specifically, interleaved-frame smoother divides all three-frame clips into even and odd clips based on indices of middle frames, and separately smooths out their corresponding latents at different denoising steps. To stabilize the latent of each clip, we first convert it to predicted RGB frames with DDIM, followed by replacing the middle frame with the interpolated frame. Note that, the smoother is only applied at a few timesteps, and the quality and individuality of interpolated frames can be well retained by the following denoising steps.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We further investigate the cross-frame mechanisms in terms of effectiveness and efficiency. Firstly, we explore fully cross-frame interaction that concatenates all frames to become a \"larger image\", and first empirically demonstrate its superior consistency and quality than sparser counterparts (see Sec. 4.4) . Secondly, applying existing cross-frame mechanisms for long-video generation suffers from either heavy computational burden or long-term inconsistency. Therefore, a hierarchical sampler is presented to produce a long video in a top-down way. In specific, it pre-generates the key frames with fully cross-frame attention for long-range coherence, followed by efficiently generating the short clips conditioned on pairs of key frames.", "cite_spans": [{"start": 301, "end": 310, "text": "Sec. 4.4)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "We conduct the experiments on extensively collected motion-prompt pairs, and show that Con-trolVideo outperforms alternative competitors qualitatively and quantitatively. Thanks to the efficient designs, ControlVideo produces short and long videos in several minutes using one NVIDIA 2080Ti.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "In summary, our contributions are presented as follows:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "• We propose training-free ControlVideo with interleaved-frame smoother for consistent and continuous controllable text-to-video generation. • Interleaved-frame smoother alternately smooths out the latents of three-frame clips, effectively stabilizing the entire video during sampling. • We empirically demonstrate the superior consistency and quality of fully cross-frame interaction, while presenting a hierarchical sampler for long-video generation in commodity GPUs.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTRODUCTION", "sec_num": "1"}, {"text": "Latent diffusion model (LDM) (<PERSON><PERSON><PERSON> et al., 2022) is an efficient variant of diffusion models (<PERSON> et al., 2020) by applying the diffusion process in the latent space. LDM uses an encoder to compress an image x into latent code z = (x). It learns the distribution of image latent codes z 0 ∼ p data (z 0 ) in a DDPM formulation (<PERSON> et al., 2020) , including a forward and a backward process. The forward diffusion process gradually adds gaussian noise at each timestep t to obtain z t :", "cite_spans": [{"start": 29, "end": 51, "text": "(<PERSON><PERSON><PERSON> et al., 2022)", "ref_id": "BIBREF31"}, {"start": 96, "end": 113, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF9"}, {"start": 329, "end": 346, "text": "(<PERSON> et al., 2020)", "ref_id": "BIBREF9"}], "ref_spans": [], "eq_spans": [], "section": "BACKGROUND", "sec_num": "2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "q(z t |z t-1 ) = N (z t ; 1 -β t z t-1 , β t I),", "eq_num": "(1)"}], "section": "BACKGROUND", "sec_num": "2"}, {"text": "or ControlNet", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "BACKGROUND", "sec_num": "2"}, {"text": "Interleaved-Frame S<PERSON>other × T steps where {β t } T t=1 are the scale of noises, and T denotes the number of diffusion timesteps. The backward denoising process reverses the above diffusion process to predict less noisy z t-1 :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A swan moving in a lake", "sec_num": null}, {"text": "p θ (z t-1 |z t ) = N (z t-1 ; µ θ (z t , t), Σ θ (z t , t)).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A swan moving in a lake", "sec_num": null}, {"text": "(2)", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A swan moving in a lake", "sec_num": null}, {"text": "The µ θ and Σ θ are implemented with a denoising model ϵ θ with learnable parameters θ. When generating new samples, we start from z T ∼ N (0, 1) and employ DDIM sampling to predict z t-1 of previous timestep:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A swan moving in a lake", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z t-1 = √ α t-1 z t - √ 1 -α t ϵ θ (z t , t) √ α t \" predicted z0\" + 1 -α t-1 • ϵ θ (z t , t) \"direction pointing to zt\" ,", "eq_num": "(3)"}], "section": "A swan moving in a lake", "sec_num": null}, {"text": "where α t = t i=1 (1 -β i ). We use z t→0 to represent \"predicted z 0 \" at timestep t for simplicity. Note that we use Stable Diffusion (SD) ϵ θ (z t , t, τ ) as our base model, which is an instantiation of text-guided LDMs pre-trained on billions of image-text pairs. τ denotes the text prompt.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "A swan moving in a lake", "sec_num": null}, {"text": "ControlNet (Zhang & Agrawala, 2023) enables SD to support more controllable input conditions during text-to-image synthesis, e.g., depth maps, poses, edges, etc. The ControlNet uses the same U-Net (<PERSON> et al., 2015) architecture as SD and finetunes its weights to support taskspecific conditions, converting ϵ θ (z t , t, τ ) to ϵ θ (z t , t, c, τ ), where c denotes additional conditions. To distinguish the U-Net architectures of SD and ControlNet, we denote the former as the main U-Net while the latter as the auxiliary U-Net.", "cite_spans": [{"start": 11, "end": 35, "text": "(Zhang & <PERSON>wala, 2023)", "ref_id": "BIBREF47"}, {"start": 197, "end": 223, "text": "(<PERSON><PERSON> et al., 2015)", "ref_id": "BIBREF32"}], "ref_spans": [], "eq_spans": [], "section": "A swan moving in a lake", "sec_num": null}, {"text": "Controllable text-to-video generation aims to produce a video of length N conditioned on motion sequences c = {c i } N -1 i=0 and a text prompt τ . As illustrated in Fig. 2 , we propose ControlVideo with interleaved-frame smoother towards consistent and continuous video generation. ControlVideo, adapted from ControlNet, adds cross-frame interaction to self-attention modules for frame consistency (in Sec. 3.1). To ensure video continuity, interleaved-frame smoother divides all three-frame clips into even and odd clips, and separately smooths out their corresponding latents at different denoising steps (in Sec. 3.2). Finally, we further investigate the cross-frame mechanisms in terms of effectiveness and efficiency, including fully cross-frame interaction and hierarchical sampler (in Sec. 3.3).", "cite_spans": [], "ref_spans": [{"start": 171, "end": 172, "text": "2", "ref_id": "FIGREF1"}], "eq_spans": [], "section": "CONTROLVIDEO", "sec_num": "3"}, {"text": "The main challenge of adapting text-to-image models to the video counterpart is to ensure temporal consistency. Leveraging the controllability of ControlNet, motion sequences could provide coarselevel consistency in structure. Nonetheless, due to the lack of temporal interaction, individually producing each frame with ControlNet leads to drastic inconsistency in appearance (see row 2 in Algorithm 1 Interleaved-frame smoother", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "Require: z t = {z i t } N -1 i=0 , c = {c i } N -1 i=0 , τ , timestep t. 1: z t→0 ← zt- √ 1-αtϵ θ (zt,t,c,τ ) √ αt . ▷ predict clean latents 2: x t→0 ← D(z t→0 ); xt→0 ← x t→0 ▷ convert latents to RGB space 3: if (t mod 2) = 0 then ▷ smooth all even three-frame clips ( x2k-1 t→0 , x2k t→0 , x2k+1 t→0 ) 4:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "for k from 0 to N/2 do 5:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "x2k t→0 ← Interpolate(x 2k-1 t→0 , x 2k+1 t→0 ) 6: else if (t mod 2) = 1 then ▷ smooth all odd three-frame clips ( x2k t→0 , x2k+1 t→0 , x2k+2 t→0 ) 7:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "for k from 0 to N/2 do 8:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "x2k+1 t→0 ← Interpolate(x 2k t→0 , x 2k+2 t→0 ) 9: zt→0 ← E( xt→0 ) ▷ convert frames to latent space 10: z t-1 ← √ α t-1 zt→0 + √ 1 -α t-1 • ϵ θ (z t , t, c, τ ).", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "▷ predict less noisy latent 11: return z t-1 Fig. 5 ). Similar to previous works (<PERSON> et al., 2022b; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) , we also extend original self-attention of SD U-Net to cross-frame attention, so that the video content could be temporally shared via inter-frame interaction.", "cite_spans": [{"start": 81, "end": 99, "text": "(<PERSON> et al., 2022b;", "ref_id": null}, {"start": 100, "end": 125, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF16"}], "ref_spans": [{"start": 50, "end": 51, "text": "5", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "In specific, ControlVideo inflates the main U-Net from Stable Diffusion along the temporal axis, while keeping the auxiliary U-Net from ControlNet. Analogous to (<PERSON> et al., 2022b; <PERSON> et al., 2022b; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) , it directly converts 2D convolution layers to 3D counterpart by replacing 3 × 3 kernels with 1 × 3 × 3 kernels. Self-attention is converted to cross-frame attention by querying from other frames as:", "cite_spans": [{"start": 161, "end": 179, "text": "(<PERSON> et al., 2022b;", "ref_id": null}, {"start": 180, "end": 197, "text": "<PERSON> et al., 2022b;", "ref_id": null}, {"start": 198, "end": 223, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF16"}], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "Attention(Q, K, V ) = Softmax( QK T √ d ) • V , where Q = W Q z i t , K = W K zt, V = W V zt,", "eq_num": "(4)"}], "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "where W Q , W K , and W V project z t into query, key, and value, respectively. z i t and z t denote ith latent frame and the latents of reference frames at timestep t. We will discuss the choices of cross-frame mechanisms (i.e., reference frames) in Sec. 3.3", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "PRELIMINARY", "sec_num": "3.1"}, {"text": "Albeit cross-frame interaction promisingly keeps frame consistency in appearance, they are still visibly flickering in structure. Discrete motion sequences only ensure coarse-level structural consistency, not sufficient to keep the continuous inter-frame transition. Intuitively, a continuous video could be considered as multiple continuous three-frame clips, so we simplify the problem of ensuring the video continuity to ensuring all three-frame clips continuous.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "Inspired by this, we propose an interleaved-frame smoother to enable continuous video generation. In Alg. 1, interleaved-frame smoother divides all three-frame clips into even and odd clips based on indices of middle frames, and individually smooths their corresponding latents at different timesteps. To stabilize the latent of each clip, we first convert it to predicted RGB frames with DDIM, following by replacing middle frame with the interpolated frame. Specifically, at timestep t, we first predict the clean video latent z t→0 according to z t :", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z t→0 = z t - √ 1 -α t ϵ θ (z t , t, c, τ ) √ α t .", "eq_num": "(5)"}], "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "After projecting z t→0 into a RGB video x t→0 = D(z t→0 ), we convert it to a more smoothed video xt→0 by replacing each middle frame with the interpolated one. Based on smoothed video latent zt→0 = E( xt→0 ), we compute the less noisy latent z t-1 following DDIM denoising in Eq. 3:", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z t-1 = √ α t-1 zt→0 + 1 -α t-1 • ϵ θ (z t , t, c, τ ).", "eq_num": "(6)"}], "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "We note that the above process is only performed at a few intermediate timesteps, the individuality and quality of interpolated frames are also well retained by the following denoising steps. Additionally, the newly computational burden can be negligible (See Table 3 ). ", "cite_spans": [], "ref_spans": [{"start": 266, "end": 267, "text": "3", "ref_id": "TABREF5"}], "eq_spans": [], "section": "INTERLEAVED-<PERSON><PERSON><PERSON> SMOOTHER", "sec_num": "3.2"}, {"text": "Fully cross-frame interaction. Previous works (<PERSON> et al., 2022b; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023) usually replace self-attention with sparser cross-frame mechanisms, e.g., taking the reference frames as first or previous frames. Such mechanisms will increase the discrepancy between the query and key in self-attention modules, resulting in the degradation of video quality and consistency. In contrast, fully cross-frame interaction considers all frames as reference (i.e., becoming a \"large image\"), so has a less generation gap with text-to-image models. We conduct comparison experiments on above mechanisms in Fig. 5 and Table 3 . Despite slightly more computational burden, fully cross-frame interaction empirically shows better consistency and quality than the sparser counterparts.", "cite_spans": [{"start": 46, "end": 64, "text": "(<PERSON> et al., 2022b;", "ref_id": null}, {"start": 65, "end": 90, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2023)", "ref_id": "BIBREF16"}], "ref_spans": [{"start": 613, "end": 614, "text": "5", "ref_id": "FIGREF5"}, {"start": 625, "end": 626, "text": "3", "ref_id": "TABREF5"}], "eq_spans": [], "section": "CROSS-<PERSON><PERSON><PERSON> MECHANISMS FOR EFFECTIVENESS AND EFFICIENCY", "sec_num": "3.3"}, {"text": "Hierarchical sampler. (<PERSON><PERSON><PERSON><PERSON> et al., 2020) to estimate the edges and depth maps of source videos, and form 125 motion-prompt pairs as our evaluation dataset. More details are provided in Appendix A.", "cite_spans": [{"start": 22, "end": 43, "text": "(<PERSON><PERSON><PERSON><PERSON> et al., 2020)", "ref_id": "BIBREF30"}], "ref_spans": [], "eq_spans": [], "section": "CROSS-<PERSON><PERSON><PERSON> MECHANISMS FOR EFFECTIVENESS AND EFFICIENCY", "sec_num": "3.3"}, {"text": "Metrics. We evaluate the video quality from three perspectives. (i) Frame consistency (FC): the average cosine similarity between all pairs of consecutive frames, and (ii) Prompt consistency (PC): the average cosine similarity between input prompt and all video frames. (iii) Warping error (WE) (<PERSON> et al., 2018) : the average error between all frames and their warped frames using optical flow.", "cite_spans": [{"start": 295, "end": 313, "text": "(<PERSON> et al., 2018)", "ref_id": "BIBREF18"}], "ref_spans": [], "eq_spans": [], "section": "CROSS-<PERSON><PERSON><PERSON> MECHANISMS FOR EFFECTIVENESS AND EFFICIENCY", "sec_num": "3.3"}, {"text": "Baselines. We compare our ControlVideo with three publicly available methods: (i) Tune-A-Video (<PERSON> et al., 2022b) Qualitative results. Fig. 3 first illustrates the visual comparisons of synthesized videos conditioned on both (a) depth maps and (b) canny edges. As shown in Fig. 3 (a), our ControlVideo demonstrates better consistency in both appearance and structure than alternative competitors. Tune-A-Video fails to keep the temporal consistency of both appearance and fine-grained structure, e.g., the color of coat and the structure of road. With the motion information from depth maps, Text2Video-Zero achieves promising consistency in structure, but still struggles with incoherent appearance in videos e.g., the color of coat. Besides, ControlVideo also performs more robustly when dealing with large motion inputs. As illustrated in Fig. 3 (b), Tune-A-Video ignores the structure information from source videos. Text2Video-Zero adopts the first-only cross-frame mechanism to trade off frame quality and appearance consistency, and generates later frames with visible artifacts. In contrast, with the proposed fully cross-frame mechanism and interleaved-frame smoother, our ControlVideo can handle large motion to generate high-quality and consistent videos. Quantitative results. We have also compared our ControlVideo with existing methods quantitatively on 125 video-prompt pairs. From Table 1 , our ControlVideo conditioned on depth outperforms the state-of-the-art methods in terms of all metrics, which is consistent with the qualitative results. In contrast, despite finetuning on a source video, Tune-A-Video still struggles to produce temporally coherent videos. Although conditioned on the same structure information, Text2Video-Zero obtains worse frame consistency and warping error than ControlVideo. For each method, the depth-conditioned models generate videos with higher frame and prompt consistency than the canny-condition counterpart, since depth maps provide smoother motion information.", "cite_spans": [{"start": 95, "end": 113, "text": "(<PERSON> et al., 2022b)", "ref_id": null}], "ref_spans": [{"start": 140, "end": 141, "text": "3", "ref_id": "FIGREF2"}, {"start": 278, "end": 279, "text": "3", "ref_id": "FIGREF2"}, {"start": 847, "end": 848, "text": "3", "ref_id": "FIGREF2"}, {"start": 1403, "end": 1404, "text": "1", "ref_id": "TABREF2"}], "eq_spans": [], "section": "CROSS-<PERSON><PERSON><PERSON> MECHANISMS FOR EFFECTIVENESS AND EFFICIENCY", "sec_num": "3.3"}, {"text": "We then perform the user study to compare our ControlVideo conditioned on depth maps with other competing methods. In specific, we provide each rater a structure sequence, a text prompt, and synthesized videos from two different methods (in random order). Then we ask them to select the better synthesized videos for each of three measurements: (i) video quality, (ii) temporal consistency throughout all frames, and (iii) text alignment between prompts and synthesized videos. The evaluation set consists of 125 representative structure-prompt pairs. Each pair is evaluated by 5 raters, and we take a majority vote for the final result. From Table 2 , the raters strongly favor our synthesized videos from all three perspectives, especially in temporal consistency. On the other hand, Tune-A-Video fails to generate consistent and high-quality videos with only DDIM inversion for structural guidance, and Text2Video-Zero also produces videos with lower quality and coherency. ", "cite_spans": [], "ref_spans": [{"start": 649, "end": 650, "text": "2", "ref_id": "TABREF4"}], "eq_spans": [], "section": "USER STUDY", "sec_num": "4.3"}, {"text": "Effect of fully cross-frame interaction. To demonstrate the effectiveness of the fully cross-frame interaction, we conduct a comparison with the following variants: i) individual: no interaction between all frames, ii) first-only: all frames attend to the first one, iii) sparse-causal: each frame attends to the first and former frames, iv) fully: our fully cross-frame, refer to Sec. 3. Note that, all the above models are extended from ControlNet without any finetuning. The qualitative and quantitative results are shown in Fig. 5 and Table 3 , respectively. From Fig. 5 , the individual cross-frame mechanism suffers from severe temporal inconsistency, e.g., colorful and black-and-white frames. The first-only and sparse-causal mechanisms reduce some appearance inconsistency by adding crossframe interaction. However, they still produce videos with structural inconsistency and visible artifacts, e.g., the orientation of the elephant and duplicate nose (row 3 in Fig. 5 ). In contrast, due to less generation gap with ControlNet, our fully cross-frame interaction performs better appearance coherency and video quality. Though the introduced interaction brings an extra 1 ∼ 2× time cost, it is acceptable for a high-quality video generation.", "cite_spans": [], "ref_spans": [{"start": 533, "end": 534, "text": "5", "ref_id": "FIGREF5"}, {"start": 545, "end": 546, "text": "3", "ref_id": "TABREF5"}, {"start": 573, "end": 574, "text": "5", "ref_id": "FIGREF5"}, {"start": 976, "end": 977, "text": "5", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "ABLATION STUDY", "sec_num": "4.4"}, {"text": "Effect of interleaved-frame smoother. We further analyze the effect of the proposed interleavedframe smoother. From Table 3 and last two rows of Fig. 5 , our interleaved-frame smoother greatly improves the video smoothness, e.g., mitigating structural flickers in red boxes. We provide more ablation studies on the timestep choices of the smoother in Appendix C and ablation studies.", "cite_spans": [], "ref_spans": [{"start": 122, "end": 123, "text": "3", "ref_id": "TABREF5"}, {"start": 150, "end": 151, "text": "5", "ref_id": "FIGREF5"}], "eq_spans": [], "section": "ABLATION STUDY", "sec_num": "4.4"}, {"text": "Producing a long video usually requires an advanced GPU with high memory. With the proposed hierarchical sampler, our ControlVideo achieves long video generation (more than 100 frames) in a memory-efficient manner. As shown in Fig. 6 , our ControlVideo can produce a long video with consistently high quality. Notably, benefiting from our efficient sampling, it only takes approximately ten minutes to generate 100 frames with resolution 512 × 512 in one NVIDIA RTX 2080Ti. More visualizations of long videos can be found in Appendix D.", "cite_spans": [], "ref_spans": [{"start": 232, "end": 233, "text": "6", "ref_id": "FIGREF6"}], "eq_spans": [], "section": "EXTENSION TO LONG-VIDEO GENERATION", "sec_num": "4.5"}, {"text": "In this paper, we present a training-free framework, namely ControlVideo, towards consistent and continuous controllable text-to-video generation. ControlVideo, inflated from ControlNet, introduces an interleaved-frame smoother to ensure video continuity. Particularly, interleaved-frame smoother alternately smooths out the latents of three-frame clips, and stabilizes each clip by updating the middle frame with the interpolation among other two frames in latent space. Moreover, we empirically demonstrate the superior performance of fully cross-frame interaction, while presenting hierarchical sampler for long-video generation in commodity GPUs. Quantitative and qualitative experiments on extensive motion-prompt pairs demonstrate that ControlVideo achieves state-of-the-arts in terms of frame consistency and video continuity.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISCUSSION", "sec_num": "6"}, {"text": "Broader impact. Large-scale diffusion models have made tremendous progress in text-to-video synthesis, yet these models are costly and unavailable to the public. ControlVideo focuses on trainingfree controllable text-to-video generation, and takes an essential step in efficient video creation. Concretely, ControlVideo could synthesize high-quality videos with commodity hardware, hence, being accessible to most researchers and users. For example, artists may leverage our approach to create fascinating videos with less time. Moreover, ControlVideo provides insights into the tasks involved in videoss, e.g., video rendering, video editing, and video-to-video translation. On the flip side, albeit we do not intend to use our model for harmful purposes, it might be misused and bring some potential negative impacts, such as producing deceptive, harmful, or explicit videos. Despite the above concerns, we believe that they could be well minimized with some steps. For example, an NSFW filter can be employed to filter out unhealthy and violent content. Also, we hope that the government could establish and improve relevant regulations to restrict the abuse of video creation.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "DISCUSSION", "sec_num": "6"}, {"text": "In Table 4 , we select 25 representative videos from DAVIS dataset (<PERSON><PERSON><PERSON><PERSON> et al., 2017) and manually annotate their source captions. After that, we ask ChatGPT to generate five edited prompts for each source caption, following the instruction like: Please generate five new sentences that similar to \"A man dances on the road\", while being more diverse and highly detailed. Finally, we obtain 125 video-prompt pairs in total, and use them to evaluate both canny and depth conditioned generation.", "cite_spans": [{"start": 67, "end": 92, "text": "(<PERSON><PERSON> et al., 2017)", "ref_id": "BIBREF26"}], "ref_spans": [{"start": 9, "end": 10, "text": "4", "ref_id": "TABREF7"}], "eq_spans": [], "section": "A. DATASET DETAILS", "sec_num": null}, {"text": "We conduct a user study to compare ControlVideo against two other methods on 125 samples, and ask five raters to answer questions in each sample. In Fig. 7 , there are three questions involving in (i) video quality, (ii) temporal consistency, and (iii) text alignment. The raters are given unlimited time to make the selection. After collecting their answers, we take a majority vote as the final result for each sample, and present statistics in Table 2 .", "cite_spans": [], "ref_spans": [{"start": 154, "end": 155, "text": "7", "ref_id": null}, {"start": 453, "end": 454, "text": "2", "ref_id": "TABREF4"}], "eq_spans": [], "section": "B. USER STUDY DETAILS", "sec_num": null}, {"text": "During inference, we adopt DDIM sampling with T = 50 timesteps, which iteratively denoises a Gaussian noise from T to 0.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "Which timesteps does interleaved-frame smoother perform at? In Fig. 8 , we explore three timestep choices at different noise levels, including {48, 49} at large noise level, {30, 31} at middle noise level, and {0, 1} at little noise level. When using the smoother at timesteps {48, 49}, the processed video is still unstable, since structure sequences bring additional flickers at the following timesteps. At timesteps {0, 1} nearby image distribution, applying the interleaved-frame smoother leads to visible distortion in some frames. In contrast, performing smoothing operation at middle timesteps {30, 31} promisingly deflickers the video, while preserving the quality and individuality of interpolated frames.", "cite_spans": [], "ref_spans": [{"start": 68, "end": 69, "text": "8", "ref_id": "FIGREF7"}], "eq_spans": [], "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "How many timesteps are used in interleaved-frame smoother? Fig. 9 shows the smoothed videos using interleaved-frame smoother at different numbers of timesteps. Applying the smoother at two consecutive timesteps (i.e., 2 steps) could smooth the entire video with little video quality degradation. As the number of smoothing steps increases, the processed video is much smoother, but some frames become slightly blurred. Thus, for higher quality and efficiency, we set the number of smoothing timesteps as 2 by default.", "cite_spans": [], "ref_spans": [{"start": 64, "end": 65, "text": "9", "ref_id": "FIGREF8"}], "eq_spans": [], "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "Non-deterministic DDPM-style sampler. ControlVideo can also employ a non-deterministic DDPM-style sampler during inference. Following Eq.12 in DDIM (<PERSON> et al., 2020b) , one can predict z t-1 from z t via (i.e., line 10 of Alg. 1 in paper):", "cite_spans": [{"start": 148, "end": 168, "text": "(<PERSON> et al., 2020b)", "ref_id": null}], "ref_spans": [], "eq_spans": [], "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "EQUATION", "cite_spans": [], "ref_spans": [], "eq_spans": [{"start": 0, "end": 8, "text": "EQUATION", "ref_id": "EQREF", "raw_str": "z t-1 ← √ α t-1 zt→0 + 1 -α t-1 • ϵ θ (z t , t, c, τ ) + σ t ϵ t ,", "eq_num": "(7)"}], "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "where ϵ t and σ t controls the level of random noise. DDPM results presents the generated videos of ControlVideo at different noise levels. Notably, as the noise level increases, ControlVideo generates more photo-realistic videos with dynamic details, e.g., ripples in the water. Fig. 10, Fig. 11, and Fig. 12 show more video visualizations conditioned on canny edges, depth maps, and human poses. Fig. 14, Fig. 15, and Fig. 16 present qualitative comparisons conditioned on canny edges, depth maps, and human poses. Fig. 13 provides an additional long video. More comparisons with video editing methods (<PERSON> et al., 2023; <PERSON> et al., 2023) are shown in this link.", "cite_spans": [{"start": 280, "end": 288, "text": "Fig. 10,", "ref_id": null}, {"start": 289, "end": 297, "text": "Fig. 11,", "ref_id": null}, {"start": 298, "end": 309, "text": "and Fig. 12", "ref_id": null}, {"start": 398, "end": 406, "text": "Fig. 14,", "ref_id": null}, {"start": 407, "end": 415, "text": "Fig. 15,", "ref_id": null}, {"start": 416, "end": 427, "text": "and Fig. 16", "ref_id": null}, {"start": 604, "end": 621, "text": "(<PERSON> et al., 2023;", "ref_id": "BIBREF27"}, {"start": 622, "end": 640, "text": "<PERSON> et al., 2023)", "ref_id": "BIBREF40"}], "ref_spans": [{"start": 522, "end": 524, "text": "13", "ref_id": "FIGREF2"}], "eq_spans": [], "section": "C. MORE ABLATION STUDIES", "sec_num": null}, {"text": "Firstly, Vid2Vid-Zero and FateZero are designed for video editing by a hybrid of fully and sparsecasual cross-frame attention, and does not investigate different attention mechanisms in depth. In contrast, our ControlVideo focuses on continuous controllable text-to-video generation, and first empirically investigate the superiority of fully cross-frame attention. Secondly, Fig. 18 shows their qualitative comparisons on video editing. As one can see, the edited videos of ControlVideo not only have more consistent structure with source videos, but also aligns better with text prompts. Which video has better temporal consistency across all frames? 3.", "cite_spans": [], "ref_spans": [{"start": 381, "end": 383, "text": "18", "ref_id": "FIGREF7"}], "eq_spans": [], "section": "D. MORE VISUALIZATIONS AND COMPARISONS", "sec_num": null}, {"text": "A determined man is trudging up a snowy and icy mountain slope, braving the biting cold and fierce winds.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "Which video aligns better with text prompt?", "sec_num": null}, {"text": "Figure 7 : The instruction of user study. A user study sample consists of a text prompt, structure sequence, and synthesized videos from two different methods (in random order). The raters are asked to answer the above three questions for each sample.", "cite_spans": [], "ref_spans": [{"start": 7, "end": 8, "text": "7", "ref_id": null}], "eq_spans": [], "section": "Text Prompt", "sec_num": null}, {"text": "While our ControlVideo enables consistent and high-quality video generation, it still struggles with producing videos beyond input motion sequences. For example, in Fig. 17 , given sequential poses of <PERSON>'s moonwalk, it is difficult to generate a vivid video according to text prompts like Iron man runs on the street. In this link, when input text prompts (e.g., rabbit) seriously conflict with input motion (e.g., ), the synthesized videos usually tend to align with input motion, ignoring the implicit structure in text prompts. To increase the ratio of text prompts over structure, we decrease the scale of ControlNet λ to 0.3 (λ = 1 by default). Therefore, it can be seen λ = 0.3 that achieves a better trade-off between two input conditions than λ = 1. In the future, we will explore how to adaptively modify input motions according to text prompts, so that users can create more vivid videos. As we increase the number of smoothing steps, the processed video becomes smoother, but some frames are slightly blurred. Therefore, we set the number of smoothing steps as two by default.", "cite_spans": [], "ref_spans": [{"start": 170, "end": 172, "text": "17", "ref_id": "FIGREF0"}], "eq_spans": [], "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "Results best seen at 500% zoom.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "A white swan moving on the lake, cartoon style.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "A majestic camel gracefully strides across the scorching desert sands.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "A shiny red jeep smoothly turns on a narrow, winding road in the mountains.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "A dusty old jeep was making its way down the winding forest road, creaking and groaning with each bump.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "A fit man is leisurely hiking through a lush and verdant forest. ", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "E. LIMITATIONS.", "sec_num": null}, {"text": "https://huggingface.co/lllyasviel/ControlNet", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "", "sec_num": null}], "back_matter": [{"text": "This work was supported by National Key RD Program of China under Grant No. 2021ZD0112100, and the National Natural Science Foundation of China (NSFC) under Grant No. U19A2073.", "cite_spans": [], "ref_spans": [], "eq_spans": [], "section": "ACKNOWLEDGEMENT", "sec_num": null}], "bib_entries": {"BIBREF0": {"ref_id": "b0", "title": "<PERSON><PERSON> et al., 2022) adopt an autoregressive framework to synthesize videos according to given descriptions. Capitalizing on the success of diffusion models in image generation, recent works (<PERSON> et al., 2022a;b; <PERSON> et al., 2022) propose to leverage their potential to produce high-quality videos. Nevertheless, training such large-scale video generative models requires extensive video-text pairs and computational resources. To reduce the training burden", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "", "suffix": ""}], "year": 2020, "venue": "Zhang & Agrawala", "volume": "", "issue": "1", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "RELATED WORK Text-to-image synthesis. Through pre-training on billions of image-text pairs, large-scale gen- erative models (<PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2021; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2021; 2022; <PERSON> et al., 2022; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023; <PERSON> et al., 2023) have made remarkable progress in creative and photo-realistic visual generation. Various frameworks have been explored to enhance image quality, including GANs (<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2023; <PERSON> et al., 2023), autoregressive models (<PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2021; 2022; <PERSON> et al., 2022), and diffusion models (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2022; <PERSON><PERSON><PERSON> et al., 2022). Among these generative models, diffusion-based models are well open-sourced and popularly applied to several downstream tasks, such as image editing (<PERSON><PERSON> et al., 2022; <PERSON><PERSON> et al., 2021) and customized generation (<PERSON><PERSON> et al., 2022; <PERSON> et al., 2023; <PERSON><PERSON> et al., 2022; <PERSON> et al., 2022). Besides text prompts, several works (<PERSON> & Agra<PERSON>, 2023; Mou et al., 2023) also introduce additional structure conditions to pre-trained text-to-image diffusion models for controllable text-to-image generation. Our ControlVideo is implemented based on the controllable text-to-image models to inherit their ability of high-quality and consistent generation. Text-to-video synthesis. Large text-to-video generative models usually extend text-to-image mod- els by adding temporal consistency. Earlier works (Wu et al., 2022a; Hong et al., 2022; Wu et al., 2021; Villegas et al., 2022) adopt an autoregressive framework to synthesize videos according to given descriptions. Capitalizing on the success of diffusion models in image generation, recent works (Ho et al., 2022a;b; Singer et al., 2022) propose to leverage their potential to produce high-quality videos. Nevertheless, training such large-scale video generative models requires extensive video-text pairs and computational resources. To reduce the training burden, Gen-1 (Esser et al., 2023) and Follow- Your-Pose (Ma et al., 2023) provide coarse temporal information (e.g., motion sequences) for video generation, yet are still costly for most researchers and users. By replacing self-attention with the sparser cross-frame mechanisms, Tune-A-Video (Wu et al., 2022b) and Text2Video-Zero (Khacha- tryan et al., 2023) keep considerable consistency in appearance with little finetuning. ControlVideo also adapts text-to-image diffusion models without any training, but generates videos with better temporal consistency and continuity. REFERENCES", "links": null}, "BIBREF1": {"ref_id": "b1", "title": "Text-to-image diffusion models with an ensemble of expert denoisers", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Nah", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Vah<PERSON>t", "suffix": ""}, {"first": "Jiaming", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Kreis", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Cat<PERSON>ro", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2211.01324"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, et al. ediffi: Text-to-image diffusion models with an ensemble of expert denoisers. arXiv preprint arXiv:2211.01324, 2022.", "links": null}, "BIBREF2": {"ref_id": "b2", "title": "Text-to-image generation via masked generative transformers", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Han", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2301.00704"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Muse: Text-to-image generation via masked generative transformers. arXiv preprint arXiv:2301.00704, 2023.", "links": null}, "BIBREF3": {"ref_id": "b3", "title": "Cogview: Mastering text-to-image generation via transformers", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Da", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Junyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Hongxia", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, et al. Cogview: Mastering text-to-image generation via transformers. NeurIPS, 2021.", "links": null}, "BIBREF4": {"ref_id": "b4", "title": "Cogview2: Faster and better text-to-image generation via hierarchical transformers", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2204.14217"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Cogview2: Faster and better text-to-image generation via hierarchical transformers. arXiv preprint arXiv:2204.14217, 2022.", "links": null}, "BIBREF5": {"ref_id": "b5", "title": "<PERSON>, and <PERSON><PERSON><PERSON>. Structure and content-guided video synthesis with diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Parm<PERSON>", "middle": [], "last": "Atighehchian", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.03011"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Structure and content-guided video synthesis with diffusion models. arXiv preprint arXiv:2302.03011, 2023.", "links": null}, "BIBREF6": {"ref_id": "b6", "title": "An image is worth one word: Personalizing text-to-image generation using textual inversion", "authors": [{"first": "Rinon", "middle": [], "last": "Gal", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Atzmon", "suffix": ""}, {"first": "Or", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "H", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Gal", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Chechik", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>-<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2208.01618"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. An image is worth one word: Personalizing text-to-image generation using textual inversion. arXiv preprint arXiv:2208.01618, 2022.", "links": null}, "BIBREF7": {"ref_id": "b7", "title": "Generative adversarial networks", "authors": [{"first": "<PERSON>", "middle": [], "last": "Goodfellow", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Pouget-Abadie", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Warde-Farley", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Courville", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "Communications of the ACM", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Generative adversarial networks. Communications of the ACM, 2020.", "links": null}, "BIBREF8": {"ref_id": "b8", "title": "Promptto-prompt image editing with cross attention control", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>-<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2208.01626"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Prompt- to-prompt image editing with cross attention control. arXiv preprint arXiv:2208.01626, 2022.", "links": null}, "BIBREF9": {"ref_id": "b9", "title": "Denoising diffusion probabilistic models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "NeurIPS", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, and <PERSON>. Denoising diffusion probabilistic models. NeurIPS, 2020.", "links": null}, "BIBREF10": {"ref_id": "b10", "title": "Imagen video: High definition video generation with diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>t<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Gao", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "P", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Poole", "suffix": ""}, {"first": "<PERSON>", "middle": ["J"], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Fleet", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.02303"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Imagen video: High definition video generation with diffusion models. arXiv preprint arXiv:2210.02303, 2022a.", "links": null}, "BIBREF12": {"ref_id": "b12", "title": "Cogvideo: Large-scale pretraining for text-to-video generation via transformers", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Hong", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xinghan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2205.15868"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Cogvideo: Large-scale pretraining for text-to-video generation via transformers. arXiv preprint arXiv:2205.15868, 2022.", "links": null}, "BIBREF13": {"ref_id": "b13", "title": "Textfield3d: Towards enhancing open-vocabulary 3d generation with noisy text fields", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Hang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Songcen", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": ["Wh"], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2309.17175"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Textfield3d: Towards enhancing open-vocabulary 3d generation with noisy text fields. arXiv preprint arXiv:2309.17175, 2023.", "links": null}, "BIBREF14": {"ref_id": "b14", "title": "Real-time intermediate flow estimation for video frame interpolation", "authors": [{"first": "Zhewei", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Tianyuan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Heng", "suffix": ""}, {"first": "Boxin", "middle": [], "last": "Shi", "suffix": ""}, {"first": "Shuchang", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Real-time intermediate flow estimation for video frame interpolation. In ECCV, 2022.", "links": null}, "BIBREF15": {"ref_id": "b15", "title": "Scaling up gans for text-to-image synthesis", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Park", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Paris", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Park", "suffix": ""}], "year": 2023, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and Taesung Park. Scaling up gans for text-to-image synthesis. In CVPR, 2023.", "links": null}, "BIBREF16": {"ref_id": "b16", "title": "Text2video-zero: Text-to-image diffusion models are zero-shot video generators", "authors": [{"first": "Levon", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Andranik", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Zhangyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Shi", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.13439"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Text2video-zero: Text-to-image diffusion models are zero-shot video generators. arXiv preprint arXiv:2303.13439, 2023.", "links": null}, "BIBREF17": {"ref_id": "b17", "title": "Multi-concept customization of text-to-image diffusion", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2212.04488"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Multi-concept customization of text-to-image diffusion. arXiv preprint arXiv:2212.04488, 2022.", "links": null}, "BIBREF18": {"ref_id": "b18", "title": "Learning blind video temporal consistency", "authors": [{"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ersin", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2018, "venue": "Proceedings of the European conference on computer vision (ECCV)", "volume": "", "issue": "", "pages": "170--185", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Learning blind video temporal consistency. In Proceedings of the European conference on computer vision (ECCV), pp. 170-185, 2018.", "links": null}, "BIBREF19": {"ref_id": "b19", "title": "xformers: A modular and hackable transformer modelling library", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Francisco", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Caggia<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Min", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Tintore", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Labatut", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Haziza", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. xformers: A modular and hackable transformer modelling library. https://github.com/ facebookresearch/xformers, 2022.", "links": null}, "BIBREF20": {"ref_id": "b20", "title": "Follow your pose: Pose-guided text-to-video generation using pose-free videos", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Ma", "suffix": ""}, {"first": "Yingqing", "middle": [], "last": "He", "suffix": ""}, {"first": "Xiaodong", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xintao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "Qifeng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2304.01186"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Fol- low your pose: Pose-guided text-to-video generation using pose-free videos. arXiv preprint arXiv:2304.01186, 2023.", "links": null}, "BIBREF21": {"ref_id": "b21", "title": "Sdedit: Image synthesis and editing with stochastic differential equations", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Song", "suffix": ""}, {"first": "Jiaming", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2108.01073"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Sdedit: Image synthesis and editing with stochastic differential equations. arXiv preprint arXiv:2108.01073, 2021.", "links": null}, "BIBREF22": {"ref_id": "b22", "title": "T2i-adapter: Learning adapters to dig out more controllable ability for text-to-image diffusion models", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xintao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Qi", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.08453"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. T2i-adapter: Learning adapters to dig out more controllable ability for text-to-image diffusion models. arXiv preprint arXiv:2302.08453, 2023.", "links": null}, "BIBREF23": {"ref_id": "b23", "title": "Imaginarynet: Learning object detectors without real images and annotations", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zitong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.06886"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Imaginarynet: Learning object detectors without real images and annotations. arXiv preprint arXiv:2210.06886, 2022.", "links": null}, "BIBREF24": {"ref_id": "b24", "title": "Glide: Towards photorealistic image generation and editing with text-guided diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2112.10741"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Glide: Towards photorealistic image generation and editing with text-guided diffusion models. arXiv preprint arXiv:2112.10741, 2021.", "links": null}, "BIBREF25": {"ref_id": "b25", "title": "Chatgpt: Optimizing language models for dialogue", "authors": [{"first": "", "middle": [], "last": "Tb Openai", "suffix": ""}], "year": 2022, "venue": "OpenAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "TB OpenAI. Chatgpt: Optimizing language models for dialogue. OpenAI, 2022.", "links": null}, "BIBREF26": {"ref_id": "b26", "title": "The 2017 davis challenge on video object segmentation", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Caelles", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Sorkine-Hornung", "suffix": ""}, {"first": "Luc", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2017, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:1704.00675"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. The 2017 davis challenge on video object segmentation. arXiv preprint arXiv:1704.00675, 2017.", "links": null}, "BIBREF27": {"ref_id": "b27", "title": "Fatezero: Fusing attentions for zero-shot text-based video editing", "authors": [{"first": "Chenyang", "middle": [], "last": "Qi", "suffix": ""}, {"first": "Xiaodong", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chenyang", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Xintao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qifeng", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.09535"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Fatezero: Fusing attentions for zero-shot text-based video editing. arXiv preprint arXiv:2303.09535, 2023.", "links": null}, "BIBREF28": {"ref_id": "b28", "title": "Zero-shot text-to-image generation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chelsea", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Ilya", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2021, "venue": "ICML", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Zero-shot text-to-image generation. In ICML, 2021.", "links": null}, "BIBREF29": {"ref_id": "b29", "title": "Hierarchical textconditional image generation with clip latents", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2204.06125"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Hierarchical text- conditional image generation with clip latents. arXiv preprint arXiv:2204.06125, 2022.", "links": null}, "BIBREF30": {"ref_id": "b30", "title": "Towards robust monocular depth estimation: Mixing datasets for zero-shot cross-dataset transfer", "authors": [{"first": "<PERSON>", "middle": [], "last": "Ranftl", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Koltun", "suffix": ""}], "year": 2020, "venue": "TPAMI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Towards robust monocular depth estimation: Mixing datasets for zero-shot cross-dataset transfer. TPAMI, 2020.", "links": null}, "BIBREF31": {"ref_id": "b31", "title": "Highresolution image synthesis with latent diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "Ommer", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. High- resolution image synthesis with latent diffusion models. In CVPR, 2022.", "links": null}, "BIBREF32": {"ref_id": "b32", "title": "U-net: Convolutional networks for biomedical image segmentation", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Brox", "suffix": ""}], "year": 2015, "venue": "MICCAI", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON>, and <PERSON>. U-net: Convolutional networks for biomedical image segmentation. In MICCAI, 2015.", "links": null}, "BIBREF33": {"ref_id": "b33", "title": "Dreambooth: Fine tuning text-to-image diffusion models for subject-driven generation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuanzhen", "middle": [], "last": "Li", "suffix": ""}, {"first": "Varun", "middle": [], "last": "Jampani", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2208.12242"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Dreambooth: Fine tuning text-to-image diffusion models for subject-driven generation. arXiv preprint arXiv:2208.12242, 2022.", "links": null}, "BIBREF34": {"ref_id": "b34", "title": "Photorealistic text-to-image diffusion models with deep language understanding", "authors": [{"first": "<PERSON>t<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2205.11487"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> <PERSON>, <PERSON><PERSON>, et al. Photorealistic text-to-image diffusion models with deep language understanding. arXiv preprint arXiv:2205.11487, 2022.", "links": null}, "BIBREF35": {"ref_id": "b35", "title": "Stylegan-t: Unlocking the power of gans for fast large-scale text-to-image synthesis", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2301.09515"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Stylegan-t: Unlocking the power of gans for fast large-scale text-to-image synthesis. arXiv preprint arXiv:2301.09515, 2023.", "links": null}, "BIBREF36": {"ref_id": "b36", "title": "Make-a-video: Text-to-video generation without text-video data", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Polyak", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Xi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "An", "suffix": ""}, {"first": "Songyang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qiyuan", "middle": [], "last": "Hu", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Oron", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2209.14792"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, et al. Make-a-video: Text-to-video generation without text-video data. arXiv preprint arXiv:2209.14792, 2022.", "links": null}, "BIBREF37": {"ref_id": "b37", "title": "Denoising diffusion implicit models", "authors": [{"first": "Jiaming", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.02502"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Denoising diffusion implicit models. arXiv preprint arXiv:2010.02502, 2020a.", "links": null}, "BIBREF38": {"ref_id": "b38", "title": "Denoising diffusion implicit models", "authors": [{"first": "Jiaming", "middle": [], "last": "Song", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>g", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2020, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2010.02502"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Denoising diffusion implicit models. arXiv preprint arXiv:2010.02502, 2020b.", "links": null}, "BIBREF39": {"ref_id": "b39", "title": "Phenaki: Variable length video generation from open domain textual description", "authors": [{"first": "<PERSON><PERSON>", "middle": [], "last": "Villegas", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "Han", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Santiago", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2210.02399"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Phenaki: Variable length video generation from open domain textual description. arXiv preprint arXiv:2210.02399, 2022.", "links": null}, "BIBREF40": {"ref_id": "b40", "title": "Zeroshot video editing using off-the-shelf image diffusion models", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Zide", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>o", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Chunhua", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2303.17599"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Zero- shot video editing using off-the-shelf image diffusion models. arXiv preprint arXiv:2303.17599, 2023.", "links": null}, "BIBREF41": {"ref_id": "b41", "title": "Elite: Encoding visual concepts into textual embeddings for customized text-to-image generation", "authors": [{"first": "Yuxiang", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Jinfeng", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.13848"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Elite: Encoding visual concepts into textual embeddings for customized text-to-image generation. arXiv preprint arXiv:2302.13848, 2023.", "links": null}, "BIBREF42": {"ref_id": "b42", "title": "Generating open-domain videos from natural descriptions", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>n", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Qianxi", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Binyang", "middle": [], "last": "Li", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Fan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "Godiva", "suffix": ""}], "year": 2021, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2104.14806"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>. Godiva: Generating open-domain videos from natural descriptions. arXiv preprint arXiv:2104.14806, 2021.", "links": null}, "BIBREF43": {"ref_id": "b43", "title": "Nüwa: Visual synthesis pre-training for neural visual world creation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Fan", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Jiang", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "ECCV", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Nüwa: Visual synthesis pre-training for neural visual world creation. In ECCV, 2022a.", "links": null}, "BIBREF44": {"ref_id": "b44", "title": "Tune-a-video: One-shot tuning of image diffusion models for text-to-video generation", "authors": [{"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Ge", "suffix": ""}, {"first": "Xintao", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Weixia<PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>u", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": ["<PERSON>"], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2212.11565"]}, "num": null, "urls": [], "raw_text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Tune-a-video: One-shot tuning of image diffusion models for text-to-video generation. arXiv preprint arXiv:2212.11565, 2022b.", "links": null}, "BIBREF45": {"ref_id": "b45", "title": "Advancing high-resolution video-language representation with large-scale video transcriptions", "authors": [{"first": "Tiankai", "middle": [], "last": "Hong<PERSON> Xue", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "Hang", "suffix": ""}, {"first": "Yuchong", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "Bei", "middle": [], "last": "Sun", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Baining", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "", "middle": [], "last": "<PERSON>", "suffix": ""}], "year": 2022, "venue": "CVPR", "volume": "", "issue": "", "pages": "", "other_ids": {}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Advancing high-resolution video-language representation with large-scale video transcriptions. In CVPR, 2022.", "links": null}, "BIBREF46": {"ref_id": "b46", "title": "Scaling autoregressive models for contentrich text-to-image generation", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "Yuanzhong", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "<PERSON><PERSON><PERSON><PERSON>", "suffix": ""}, {"first": "<PERSON>", "middle": [], "last": "Ku", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON><PERSON>", "suffix": ""}], "year": 2022, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2206.10789"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Scaling autoregressive models for content- rich text-to-image generation. arXiv preprint arXiv:2206.10789, 2022.", "links": null}, "BIBREF47": {"ref_id": "b47", "title": "Adding conditional control to text-to-image diffusion models", "authors": [{"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "<PERSON>", "suffix": ""}, {"first": "<PERSON><PERSON><PERSON>", "middle": [], "last": "Agrawala", "suffix": ""}], "year": 2023, "venue": "", "volume": "", "issue": "", "pages": "", "other_ids": {"arXiv": ["arXiv:2302.05543"]}, "num": null, "urls": [], "raw_text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. Adding conditional control to text-to-image diffusion models. arXiv preprint arXiv:2302.05543, 2023.", "links": null}}, "ref_entries": {"FIGREF0": {"text": "Figure 1: Training-free controllable text-to-video generation. Left: We visualize the frames and x-t slice (pixels in red line of original frame) of Text2Video-Zero, and observe visible discontinuity in x-t slice. Right: ControlVideo, adapted from ControlNet, achieves more continuous x-t slice across time, along with improved appearance consistency than Text2Video-Zero. See videos for better view.", "type_str": "figure", "fig_num": "1", "num": null, "uris": null}, "FIGREF1": {"text": "Figure 2: Overview of ControlVideo. For consistency in appearance, ControlVideo adapts ControlNet to the video counterpart by adding cross-frame interaction into self-attention modules. To further improve video continuity, interleaved-frame smoother is introduced to stabilize video latents during denosing (see Alg. 1 for details).", "type_str": "figure", "fig_num": "2", "num": null, "uris": null}, "FIGREF2": {"text": "Figure 3: Qualitative comparisons conditioned on depth maps and canny edges. Our Con-trolVideo produces videos with better (a) appearance consistency and (b) video quality than others. In contrast, Tune-A-Video fails to inherit structures from source videos, while Text2Video-Zero brings visible artifacts in large motion videos. See videos at qualitative comparisons.", "type_str": "figure", "fig_num": "3", "num": null, "uris": null}, "FIGREF3": {"text": "Fig.4further shows the comparison conditioned on human poses. From Fig.4, Tune-A-Video only maintains the coarse structures of the source video, i.e., human position. Text2Video-Zero and Follow-Your-Pose produce video frames with inconsistent appearance, e.g., changing faces of iron man (in row 4) or disappearing objects in the background (in row 5). In comparison, our ControlVideo performs more consistent video generation, demonstrating its superiority. More qualitative comparisons are provided in Appendix D.", "type_str": "figure", "fig_num": "4", "num": null, "uris": null}, "FIGREF4": {"text": "Figure 4: Qualitative comparisons on poses. Tune-A-Video only preserves original human positions, while Text2Video-Zero and Follow-Your-Pose produce frames with appearance incoherence. Our ControlVideo achieves better consistency in both structure and appearance. See videos at qualitative comparisons.", "type_str": "figure", "fig_num": "4", "num": null, "uris": null}, "FIGREF5": {"text": "Figure 5: Qualitative ablation studies on cross-frame mechanisms and interleaved-frame smoother. Fully cross-frame interaction produces video frames with higher quality and consistency than other mechanisms, and adding the smoother further enhances the video smoothness. See corresponding videos for better comparison.", "type_str": "figure", "fig_num": "5", "num": null, "uris": null}, "FIGREF6": {"text": "Figure 6: A long video produced with our hierarchical sampling. Motion sequences are shown on the top left. Using the efficient sampler, our ControlVideo generates a high-quality long video with the holistic consistency. See videos at long video generation.", "type_str": "figure", "fig_num": "6", "num": null, "uris": null}, "FIGREF7": {"text": "Figure8: Ablation on timestep choices in interleaved-frame smoother. We apply interleavedframe smoother at different timesteps, including {48, 49} at large noise level, {30, 31} at middle noise level, and {0, 1} at little noise level. Among them, using the smoother at timesteps {30, 31} promisingly mitigates the flicker effect while ensuring high quality. Results best seen at 500% zoom.", "type_str": "figure", "fig_num": "8", "num": null, "uris": null}, "FIGREF8": {"text": "Figure 9: Ablation on the number of timesteps used in interleaved-frame smoother. Applying the smoother at two consecutive timesteps (i.e., 2 steps) effectively reduces the flickers in structure.As we increase the number of smoothing steps, the processed video becomes smoother, but some frames are slightly blurred. Therefore, we set the number of smoothing steps as two by default.Results best seen at 500% zoom.", "type_str": "figure", "fig_num": "9", "num": null, "uris": null}, "FIGREF9": {"text": "Figure 10: More video visualizations conditioned on canny edges. Results best seen at 500% zoom.", "type_str": "figure", "fig_num": "10", "num": null, "uris": null}, "FIGREF10": {"text": "Figure 11: More video visualizations conditioned on depth maps. Results best seen at 500% zoom.", "type_str": "figure", "fig_num": "11", "num": null, "uris": null}, "TABREF2": {"text": "Quantitative comparisons of ControlVideo with other methods. We evaluate them on 125 motion-prompt pairs in terms of consistency, and the best results are bolded.the middle frame of each three-frame clip. The synthesized short videos are of length 15, while the long videos usually contain about 100 frames. Unless otherwise noted, their resolution is both 512 × 512. During sampling, we adopt DDIM sampling(<PERSON> et al., 2020a)  with 50 timesteps, and interleaved-frame smoother is performed on predicted RGB frames at timesteps {30, 31} by default. With the efficient implementation of xFormers(<PERSON><PERSON><PERSON><PERSON> et al., 2022), ControVideo could produce both short and long videos with one NVIDIA RTX 2080Ti in about 2 and 10 minutes, respectively. Datasets. To evaluate our ControlVideo, we collect 25 object-centric videos from DAVIS dataset(<PERSON><PERSON><PERSON><PERSON> et al., 2017) and manually annotate their source descriptions. Then, for each source description, ChatGPT (OpenAI, 2022) is utilized to generate five editing prompts automatically, resulting in 125 video-prompt pairs in total. Finally, we employ Canny and MiDaS DPT-Hybrid model", "type_str": "table", "content": "<table><tr><td colspan=\"4\">At each timestep, a long video z t = {z i t } N -1 i=0 is separated into multiple short video clips with the selected key frames z key t N = {z kNc t } Nc k=0 , where each clip is of length N c -1 and the kth clip is</td></tr><tr><td colspan=\"4\">denoted as z k t = {z j t } (k+1)Nc-1 j=kNc+1 . Then, we pre-generate the key frames with fully cross-frame</td></tr><tr><td colspan=\"3\">attention for long-range coherence, where reference frames are z key t</td><td>= {z kNc t</td><td>N Nc k=0 . Conditioned on }</td></tr><tr><td>each pair of key frames, i.e., reference frames as {z kNc t corresponding clip z k t holding the holistic consistency.</td><td>, z t (k+1)Nc</td><td colspan=\"2\">}, we sequentially synthesize their</td></tr><tr><td>4 EXPERIMENTS</td><td/><td/></tr></table>", "num": null, "html": null}, "TABREF4": {"text": "User preference study. The numbers denote the percentage of raters who favor the videos synthesized by our ControlVideo over other methods.", "type_str": "table", "content": "<table><tr><td>Method Comparison</td><td colspan=\"3\">Video Quality Temporal Consistency Text Alignment</td></tr><tr><td>Ours vs. Tune-A-<PERSON> et al. (2022b)</td><td>73.6%</td><td>83.2%</td><td>68.0%</td></tr><tr><td>Ours vs. Text2Video-Zero <PERSON> et al. (2023)</td><td>76.0%</td><td>81.6%</td><td>65.6%</td></tr><tr><td>Source</td><td/><td/><td/></tr><tr><td>Videos</td><td/><td/><td/></tr><tr><td>Structure</td><td/><td/><td/></tr><tr><td>Conditions</td><td/><td/><td/></tr><tr><td>Tune-A-Video</td><td/><td/><td/></tr><tr><td>Text2Video-Zero</td><td/><td/><td/></tr><tr><td>Follow-Your-Pose</td><td/><td/><td/></tr><tr><td>ControlVideo</td><td/><td/><td/></tr><tr><td>(Ours)</td><td/><td/><td/></tr></table>", "num": null, "html": null}, "TABREF5": {"text": "Quantitative ablation studies on cross-frame mechanisms and interleaved-frame smoother. The results indicate that our fully cross-frame mechanism achieves better frame consistency than other mechanisms, and the interleaved-frame smoother significantly improves the frame consistency.Cross-Frame Mechanism FC (×10 -2 ) PC (×10 -2 ) WE (×10 -2 ) Time Cost (min)", "type_str": "table", "content": "<table><tr><td>Individual</td><td/><td>89.94</td><td>30.79</td><td>20.13</td><td>1.2</td></tr><tr><td>First-only</td><td/><td>94.92</td><td>30.54</td><td>8.91</td><td>1.2</td></tr><tr><td colspan=\"2\">Sparse-Causal</td><td>95.06</td><td>30.59</td><td>7.05</td><td>1.5</td></tr><tr><td>Fully</td><td/><td>95.36</td><td>30.76</td><td>5.93</td><td>3.0</td></tr><tr><td colspan=\"2\">Fully + Smoother</td><td>96.83</td><td>30.79</td><td>2.75</td><td>3.5</td></tr><tr><td/><td>a</td><td/><td/><td/><td/></tr><tr><td>Frame 0</td><td>Frame 11</td><td>Frame 22</td><td>Frame 33</td><td>Frame 44</td><td>Frame 55</td></tr><tr><td>Frame 66</td><td>Frame 77</td><td>Frame 88</td><td>Frame 99</td><td>Frame 110</td><td>Frame 121</td></tr></table>", "num": null, "html": null}, "TABREF6": {"text": "", "type_str": "table", "content": "<table/>", "num": null, "html": null}, "TABREF7": {"text": "Names and captions of selected videos from DAVIS dataset.", "type_str": "table", "content": "<table><tr><td/><td>Video Name</td><td/><td>Source Caption</td></tr><tr><td/><td>blackswan</td><td colspan=\"2\">a black swan moving on the lake</td></tr><tr><td/><td>boat</td><td/><td>a boat moves in the river</td></tr><tr><td/><td>breakdance-flare</td><td/><td>a man dances on the road</td></tr><tr><td/><td>bus</td><td/><td>a bus moves on the street</td></tr><tr><td/><td>camel</td><td/><td>a camel walks on the desert</td></tr><tr><td/><td>car-roundabout</td><td/><td>a jeep turns on a road</td></tr><tr><td/><td>car-shadow</td><td/><td>a car moves to a building</td></tr><tr><td/><td>car-turn</td><td/><td>a jeep on a forest road</td></tr><tr><td/><td>cows</td><td/><td>a cow walks on the grass</td></tr><tr><td/><td>dog</td><td/><td>a dog walks on the ground</td></tr><tr><td/><td>elephant</td><td colspan=\"2\">an elephant walks on the ground</td></tr><tr><td/><td>flamingo</td><td colspan=\"2\">a flamingo wanders in the water</td></tr><tr><td/><td>gold-fish</td><td colspan=\"2\">golden fishers swim in the water</td></tr><tr><td/><td>hike</td><td/><td>a man hikes on a mountain</td></tr><tr><td/><td>hockey</td><td colspan=\"2\">a player is playing hockey on the ground</td></tr><tr><td/><td>kite-surf</td><td/><td>a man is surfing on the sea</td></tr><tr><td/><td>lab-coat</td><td colspan=\"2\">three women stands on the lawn</td></tr><tr><td/><td>longboard</td><td colspan=\"2\">a man is playing skateboard on the alley</td></tr><tr><td/><td>mallard-water</td><td/><td>a mallard swims on the water</td></tr><tr><td/><td>mbike-trick</td><td/><td>a man riding motorbike</td></tr><tr><td/><td>rhino</td><td/><td>a rhino walks on the rocks</td></tr><tr><td/><td>surf</td><td/><td>a sailing boat moves on the sea</td></tr><tr><td/><td>swing</td><td/><td>a girl is playing on the swings</td></tr><tr><td/><td>tennis</td><td/><td>a man is playing tennis</td></tr><tr><td/><td>walking</td><td/><td>a selfie of walking man</td></tr><tr><td/><td colspan=\"2\">Structure</td><td>Video 1</td><td>Video 2</td></tr><tr><td/><td colspan=\"2\">Sequence</td></tr><tr><td colspan=\"2\">Between Method 1 &amp; 2 :</td><td/></tr><tr><td>1.</td><td colspan=\"2\">Which video has higher quality ?</td></tr><tr><td>2.</td><td/><td/></tr></table>", "num": null, "html": null}}}}